## ASP.NET to Django Conversion Script: Auto WIS Time Schedule

This document outlines the modernization plan for migrating the `AutoWIS_Time_Set.aspx` ASP.NET application to a modern Django solution. The focus is on leveraging Django 5.0+, HTMX, Alpine.js, and DataTables for a highly efficient, maintainable, and interactive web application, minimizing manual coding through AI-assisted automation principles.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code utilizes `SqlDataSource` and direct SQL commands for CRUD operations on `tblinv_AutoWIS_TimeSchedule`.

**Extracted Schema:**
- **Table Name:** `tblinv_AutoWIS_TimeSchedule`
- **Columns:**
    - `Id` (Primary Key, inferred from `DataKeyNames="Id"` and `DELETE WHERE Id = @Id`)
    - `TimeAuto` (`string` in ASP.NET, stored as `HH:MM:SS AM/PM` string)
    - `CompId` (`Int32` from Session)
    - `FinYearId` (`Int32` from Session)
    - `TimeToOrder` (`string`, set to the same value as `TimeAuto` during insertion)

### Step 2: Identify Backend Functionality

**Analysis:**
The C# code-behind handles standard CRUD operations via `GridView` events and `SqlDataSource`.

**Functionality Identified:**
-   **Create:** Handled by `GridView1_RowCommand` (commands "Add" and "Add1").
    -   `Add` (from GridView footer): Inserts new time if it's at least 2 hours after the latest existing `TimeAuto`.
    -   `Add1` (from EmptyDataTemplate): Inserts new time without the 2-hour validation check.
-   **Read:** `FillGrid()` method populates `GridView1` using a `SqlDataAdapter` with `GetAutoWIS_Time` stored procedure (`SELECT Id,TimeAuto FROM tblinv_AutoWIS_TimeSchedule ORDER BY Id DESC`).
-   **Update:** `GridView1_RowUpdating` event. Updates `TimeAuto` based on `Id`.
-   **Delete:** `GridView1_RowDeleting` event. Deletes record based on `Id`.
-   **Validation:**
    -   New `TimeAuto` must not be empty.
    -   For non-empty grid additions, new `TimeAuto` must be at least 2 hours later than the `TimeAuto` of the latest existing record.
-   **Session Data:** `CompId` and `FinYearId` are retrieved from the session for `INSERT` operations.

### Step 3: Infer UI Components

**Analysis:**
The `.aspx` file primarily uses an `asp:GridView` for data display and interaction, along with `MKB:TimeSelector` for time input.

**UI Components Identified:**
-   **Data Display:** `GridView1` (will be replaced by DataTables for client-side pagination, sorting, and filtering).
-   **Input Fields:** `MKB:TimeSelector` controls for `TimeAuto` in footer (add), edit template, and empty data template. These will be standard HTML `<input type="text">` fields that can be enhanced with an Alpine.js/HTMX-compatible time picker if specific UI is required, or simpler `HH:MM:SS AM/PM` string input.
-   **Action Buttons:** LinkButtons for Edit/Delete and a Button for Insert. These will be HTMX-triggered buttons for modal forms.
-   **Messages:** `lblMessage` for status updates (will be replaced by Django messages framework).

### Step 4: Generate Django Code

We will create a Django application named `inventory_masters` to house this module.

#### 4.1 Models (`inventory_masters/models.py`)

This model directly maps to `tblinv_AutoWIS_TimeSchedule`, using `CharField` for time fields to preserve the `HH:MM:SS AM/PM` string format as per the ASP.NET `SqlDataSource` definition. Helper properties and methods are added for robust time comparison and business logic within the 'fat model' approach.

```python
import datetime
from django.db import models
from django.core.exceptions import ValidationError

class AutoWISTimeSchedule(models.Model):
    # Django will implicitly add 'id' as AutoField, which maps to the PK 'Id' in tblinv_AutoWIS_TimeSchedule
    time_auto = models.CharField(db_column='TimeAuto', max_length=50) # Stores 'HH:MM:SS AM/PM' string
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    time_to_order = models.CharField(db_column='TimeToOrder', max_length=50, blank=True, null=True) # Stored same as TimeAuto on insert

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblinv_AutoWIS_TimeSchedule'
        verbose_name = 'Auto WIS Time Schedule'
        verbose_name_plural = 'Auto WIS Time Schedules'
        ordering = ['-id'] # Default order for the list view, matching ASP.NET's SELECT ... ORDER BY Id DESC

    def __str__(self):
        return f"Schedule {self.id}: {self.time_auto}"

    @property
    def parsed_time_auto(self):
        """Converts the 'TimeAuto' string to a datetime.time object for internal use."""
        if self.time_auto:
            try:
                # Use %I for 12-hour clock, %p for AM/PM
                return datetime.datetime.strptime(self.time_auto, '%I:%M:%S %p').time()
            except ValueError:
                return None
        return None

    @classmethod
    def get_latest_schedule_time(cls):
        """
        Retrieves the parsed time of the latest entry in the database.
        This replicates the ASP.NET code's behavior for comparison (taking the latest time
        from a `SELECT ... ORDER BY Id DESC` result).
        """
        latest_entry = cls.objects.order_by('-id').first()
        if latest_entry and latest_entry.parsed_time_auto:
            return latest_entry.parsed_time_auto
        return None

    def set_time_to_order_if_empty(self):
        """Sets TimeToOrder to TimeAuto if it's not already set, mirroring ASP.NET behavior."""
        if not self.time_to_order:
            self.time_to_order = self.time_auto

```

#### 4.2 Forms (`inventory_masters/forms.py`)

A Django ModelForm for `AutoWISTimeSchedule` with custom validation to incorporate the 2-hour difference business logic. The `time_auto` field will be a `CharField` for flexible input, and validation will ensure correct format and business rules.

```python
import datetime
from django import forms
from django.core.exceptions import ValidationError
from .models import AutoWISTimeSchedule

class AutoWISTimeScheduleForm(forms.ModelForm):
    # Use CharField for time_auto to allow flexible HH:MM:SS AM/PM input
    time_auto = forms.CharField(
        label="Time",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'HH:MM:SS AM/PM',
            'pattern': '^(0[1-9]|1[0-2]):([0-5][0-9]):([0-5][0-9]) (AM|PM)$' # Basic pattern for frontend help
        })
    )

    class Meta:
        model = AutoWISTimeSchedule
        fields = ['time_auto']
        # comp_id and fin_year_id will be set in the view's form_valid
        # time_to_order is set in the model's save method for new instances

    def __init__(self, *args, **kwargs):
        self.request = kwargs.pop('request', None) # Pass request to form for session data if needed
        super().__init__(*args, **kwargs)

    def clean_time_auto(self):
        new_time_str = self.cleaned_data['time_auto']

        if not new_time_str:
            raise ValidationError("Time cannot be empty.")

        try:
            # Attempt to parse to validate format
            parsed_time_obj = datetime.datetime.strptime(new_time_str, '%I:%M:%S %p').time()
        except ValueError:
            raise ValidationError("Invalid time format. Please use HH:MM:SS AM/PM (e.g., 01:00:00 PM).")

        # Business logic: new time must be 2 hours after the latest existing time (for new records only)
        if not self.instance.pk: # This applies only to create operations (not update)
            latest_db_time_obj = AutoWISTimeSchedule.get_latest_schedule_time()

            if latest_db_time_obj:
                # Combine times with a dummy date for timedelta comparison
                dummy_date = datetime.date(2000, 1, 1)
                new_datetime = datetime.datetime.combine(dummy_date, parsed_time_obj)
                latest_db_datetime = datetime.datetime.combine(dummy_date, latest_db_time_obj)

                time_difference = new_datetime - latest_db_datetime

                # If new time is earlier than or less than 2 hours after latest DB time
                if time_difference < datetime.timedelta(hours=2):
                    raise ValidationError("New time must be at least 2 hours after the latest existing time.")
        
        return new_time_str # Return the original string for storage

```

#### 4.3 Views (`inventory_masters/views.py`)

Django Class-Based Views (CBVs) for all CRUD operations, keeping views thin as business logic resides in models and forms. An additional `TablePartialView` is created to serve the DataTables content dynamically via HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from .models import AutoWISTimeSchedule
from .forms import AutoWISTimeScheduleForm

# Ensure session context is available (example for CompId/FinYearId)
# This would typically be handled by a middleware or login system
# For demonstration, we'll use dummy values or assume they're set via a global context
# In a real app, you'd get these from request.user or session.
def get_session_company_and_year(request):
    # Replace with actual session/user logic
    comp_id = request.session.get('compid', 1) # Default to 1 for demonstration
    fin_year_id = request.session.get('finyear', 1) # Default to 1 for demonstration
    return comp_id, fin_year_id

class AutoWISTimeScheduleListView(ListView):
    model = AutoWISTimeSchedule
    template_name = 'inventory_masters/autowistimeschedule/list.html'
    context_object_name = 'autowistimeschedules' # Name for the list of objects in template

class AutoWISTimeScheduleTablePartialView(ListView):
    model = AutoWISTimeSchedule
    template_name = 'inventory_masters/autowistimeschedule/_autowistimeschedule_table.html'
    context_object_name = 'autowistimeschedules'

    # This view will be specifically hit by HTMX to refresh the table.
    # No need for special HTTPResponse status 204 as it's returning HTML fragment.
    def get_queryset(self):
        # Override to ensure the queryset is ordered as expected by the ASP.NET original
        return super().get_queryset().order_by('-id')

class AutoWISTimeScheduleCreateView(CreateView):
    model = AutoWISTimeSchedule
    form_class = AutoWISTimeScheduleForm
    template_name = 'inventory_masters/autowistimeschedule/_autowistimeschedule_form.html' # Partial template
    success_url = reverse_lazy('autowistimeschedule_list') # Not strictly used with HTMX, but good practice

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request # Pass request to form for session context
        return kwargs

    def form_valid(self, form):
        # Set session-dependent fields before saving
        comp_id, fin_year_id = get_session_company_and_year(self.request)
        form.instance.comp_id = comp_id
        form.instance.fin_year_id = fin_year_id
        
        # Original ASP.NET sets TimeToOrder equal to TimeAuto on insert,
        # which is handled in the model's save method.
        
        response = super().form_valid(form)
        messages.success(self.request, 'Auto WIS Time Schedule added successfully.')
        
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response
            # and trigger a client-side event to refresh the table
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAutoWISTimeScheduleList'
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, re-render the form fragment with errors
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=422 # Unprocessable Entity, suitable for HTMX validation errors
            )
        return super().form_invalid(form)


class AutoWISTimeScheduleUpdateView(UpdateView):
    model = AutoWISTimeSchedule
    form_class = AutoWISTimeScheduleForm
    template_name = 'inventory_masters/autowistimeschedule/_autowistimeschedule_form.html' # Partial template
    success_url = reverse_lazy('autowistimeschedule_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['request'] = self.request
        return kwargs

    def form_valid(self, form):
        # Note: Original ASP.NET did not apply the 2-hour validation on update.
        # So we don't need to manually trigger model.validate_new_time() here.
        response = super().form_valid(form)
        messages.success(self.request, 'Auto WIS Time Schedule updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAutoWISTimeScheduleList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, request=self.request),
                status=422
            )
        return super().form_invalid(form)


class AutoWISTimeScheduleDeleteView(DeleteView):
    model = AutoWISTimeSchedule
    template_name = 'inventory_masters/autowistimeschedule/_autowistimeschedule_confirm_delete.html' # Partial template
    success_url = reverse_lazy('autowistimeschedule_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Auto WIS Time Schedule deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAutoWISTimeScheduleList'
                }
            )
        return response

    def get(self, request, *args, **kwargs):
        # Render the delete confirmation partial on GET request (for HTMX modal)
        self.object = self.get_object()
        context = self.get_context_data(object=self.object)
        return self.render_to_response(context)
```

#### 4.4 Templates (`inventory_masters/templates/inventory_masters/autowistimeschedule/`)

**`list.html`** (Main page for the Auto WIS Time Schedule list)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Auto WIS Time Schedules</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'autowistimeschedule_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Time
        </button>
    </div>
    
    <div id="autowistimescheduleTable-container"
         hx-trigger="load, refreshAutoWISTimeScheduleList from:body"
         hx-get="{% url 'autowistimeschedule_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Auto WIS Time Schedule data...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
            <!-- Content will be loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initiated globally or per component.
    // No specific Alpine.js component logic needed here for simple modal toggle via _ syntax.
</script>
{% endblock %}

```

**`_autowistimeschedule_table.html`** (Partial for DataTables, loaded via HTMX)

```html
<table id="autowistimescheduleTable" class="min-w-full bg-white border border-gray-300 shadow-sm rounded-lg">
    <thead>
        <tr class="bg-gray-100 border-b border-gray-300">
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in autowistimeschedules %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} border-b border-gray-200">
            <td class="py-3 px-4 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm text-gray-700">{{ obj.time_auto }}</td>
            <td class="py-3 px-4 text-sm text-gray-700 whitespace-nowrap">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs mr-2"
                    hx-get="{% url 'autowistimeschedule_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs"
                    hx-get="{% url 'autowistimeschedule_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-gray-500">
                No Auto WIS Time Schedules found.
                <button
                    class="ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs"
                    hx-get="{% url 'autowistimeschedule_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Time
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#autowistimescheduleTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "paging": true,
            "info": true,
            "ordering": true
        });
    });
</script>

```

**`_autowistimeschedule_form.html`** (Partial for Create/Update forms, loaded via HTMX)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Auto WIS Time Schedule</h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.successful) htmx.find('#modal').classList.remove('is-active')">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="text-xs text-gray-500 mt-1">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mb-4">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Save Schedule
            </button>
        </div>
    </form>
</div>

```

**`_autowistimeschedule_confirm_delete.html`** (Partial for delete confirmation, loaded via HTMX)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the schedule for: <strong class="font-medium">{{ object.time_auto }}</strong> (ID: {{ object.id }})?</p>
    
    <form hx-delete="{% url 'autowistimeschedule_delete' object.pk %}" 
          hx-swap="none" 
          hx-on::after-request="if(event.detail.successful) htmx.find('#modal').classList.remove('is-active')">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Delete Schedule
            </button>
        </div>
    </form>
</div>

```

#### 4.5 URLs (`inventory_masters/urls.py`)

Defines URL patterns for each view, ensuring clean and descriptive paths.

```python
from django.urls import path
from .views import (
    AutoWISTimeScheduleListView,
    AutoWISTimeScheduleTablePartialView,
    AutoWISTimeScheduleCreateView,
    AutoWISTimeScheduleUpdateView,
    AutoWISTimeScheduleDeleteView
)

urlpatterns = [
    # Main list view
    path('auto-wis-time/', AutoWISTimeScheduleListView.as_view(), name='autowistimeschedule_list'),
    
    # HTMX partial for the table content
    path('auto-wis-time/table/', AutoWISTimeScheduleTablePartialView.as_view(), name='autowistimeschedule_table'),

    # HTMX partials for CRUD operations (rendered in modal)
    path('auto-wis-time/add/', AutoWISTimeScheduleCreateView.as_view(), name='autowistimeschedule_add'),
    path('auto-wis-time/edit/<int:pk>/', AutoWISTimeScheduleUpdateView.as_view(), name='autowistimeschedule_edit'),
    path('auto-wis-time/delete/<int:pk>/', AutoWISTimeScheduleDeleteView.as_view(), name='autowistimeschedule_delete'),
]

```

#### 4.6 Tests (`inventory_masters/tests.py`)

Comprehensive unit tests for the model's business logic and integration tests for views, ensuring proper HTTP responses and HTMX interactions.

```python
import datetime
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import AutoWISTimeSchedule

class AutoWISTimeScheduleModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        # Create an initial entry for validation logic
        cls.initial_schedule = AutoWISTimeSchedule.objects.create(
            time_auto='08:00:00 AM',
            comp_id=1,
            fin_year_id=2023,
            time_to_order='08:00:00 AM'
        )
  
    def test_autowistimeschedule_creation_valid(self):
        # New time 2 hours after existing
        new_time_str = '10:00:00 AM' # 2 hours after 08:00:00 AM
        schedule = AutoWISTimeSchedule(
            time_auto=new_time_str,
            comp_id=1,
            fin_year_id=2023
        )
        try:
            schedule.full_clean() # Triggers form/model clean methods
            schedule.save()
            self.assertEqual(AutoWISTimeSchedule.objects.count(), 2)
            self.assertEqual(AutoWISTimeSchedule.objects.get(pk=schedule.pk).time_auto, new_time_str)
            self.assertEqual(AutoWISTimeSchedule.objects.get(pk=schedule.pk).time_to_order, new_time_str)
        except Exception as e:
            self.fail(f"Valid creation failed with exception: {e}")

    def test_autowistimeschedule_creation_invalid_time_format(self):
        # Invalid time format
        schedule = AutoWISTimeSchedule(
            time_auto='invalid-time',
            comp_id=1,
            fin_year_id=2023
        )
        with self.assertRaises(ValidationError) as cm:
            schedule.full_clean()
        self.assertIn("Invalid time format.", cm.exception.messages[0])
    
    def test_autowistimeschedule_creation_too_close(self):
        # New time less than 2 hours after latest existing (08:00:00 AM)
        schedule = AutoWISTimeSchedule(
            time_auto='09:30:00 AM', # 1.5 hours after
            comp_id=1,
            fin_year_id=2023
        )
        with self.assertRaises(ValidationError) as cm:
            schedule.full_clean()
        self.assertIn("New time must be at least 2 hours after the latest existing time.", cm.exception.messages[0])

    def test_autowistimeschedule_creation_earlier(self):
        # New time earlier than latest existing (08:00:00 AM)
        schedule = AutoWISTimeSchedule(
            time_auto='07:00:00 AM', 
            comp_id=1,
            fin_year_id=2023
        )
        with self.assertRaises(ValidationError) as cm:
            schedule.full_clean()
        self.assertIn("New time must be at least 2 hours after the latest existing time.", cm.exception.messages[0])

    def test_autowistimeschedule_creation_empty_time(self):
        # Empty time
        schedule = AutoWISTimeSchedule(
            time_auto='',
            comp_id=1,
            fin_year_id=2023
        )
        with self.assertRaises(ValidationError) as cm:
            schedule.full_clean()
        self.assertIn("Time cannot be empty.", cm.exception.messages[0])

    def test_parsed_time_auto_property(self):
        # Test property to parse time string
        schedule = AutoWISTimeSchedule(time_auto='03:45:00 PM')
        self.assertEqual(schedule.parsed_time_auto, datetime.time(15, 45, 0))

    def test_get_latest_schedule_time(self):
        # Test class method to get latest time
        # The latest created by setUpTestData is '08:00:00 AM'
        latest_time = AutoWISTimeSchedule.get_latest_schedule_time()
        self.assertEqual(latest_time, datetime.time(8, 0, 0))

class AutoWISTimeScheduleViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.schedule1 = AutoWISTimeSchedule.objects.create(
            time_auto='08:00:00 AM',
            comp_id=1,
            fin_year_id=2023,
            time_to_order='08:00:00 AM'
        )
        cls.schedule2 = AutoWISTimeSchedule.objects.create(
            time_auto='11:00:00 AM',
            comp_id=1,
            fin_year_id=2023,
            time_to_order='11:00:00 AM'
        )
    
    def setUp(self):
        self.client = Client()
        # Set dummy session data for CompId and FinYearId
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('autowistimeschedule_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_masters/autowistimeschedule/list.html')
        self.assertContains(response, 'Auto WIS Time Schedules')

    def test_table_partial_view(self):
        response = self.client.get(reverse('autowistimeschedule_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_masters/autowistimeschedule/_autowistimeschedule_table.html')
        self.assertContains(response, self.schedule1.time_auto)
        self.assertContains(response, self.schedule2.time_auto)
        self.assertContains(response, 'id="autowistimescheduleTable"') # Check for DataTables ID

    def test_create_view_get(self):
        response = self.client.get(reverse('autowistimeschedule_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_masters/autowistimeschedule/_autowistimeschedule_form.html')
        self.assertContains(response, 'Add Auto WIS Time Schedule')
        self.assertContains(response, 'name="time_auto"')

    def test_create_view_post_valid(self):
        data = {
            'time_auto': '01:00:00 PM', # 2 hours after latest (11:00:00 AM)
        }
        response = self.client.post(reverse('autowistimeschedule_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(AutoWISTimeSchedule.objects.filter(time_auto='01:00:00 PM').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Auto WIS Time Schedule added successfully.')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAutoWISTimeScheduleList')

    def test_create_view_post_invalid(self):
        data = {
            'time_auto': '11:30:00 AM', # Too close to latest (11:00:00 AM)
        }
        response = self.client.post(reverse('autowistimeschedule_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 422) # HTMX validation error
        self.assertTemplateUsed(response, 'inventory_masters/autowistimeschedule/_autowistimeschedule_form.html')
        self.assertContains(response, "New time must be at least 2 hours after the latest existing time.")
        self.assertFalse(AutoWISTimeSchedule.objects.filter(time_auto='11:30:00 AM').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('autowistimeschedule_edit', args=[self.schedule1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_masters/autowistimeschedule/_autowistimeschedule_form.html')
        self.assertContains(response, 'Edit Auto WIS Time Schedule')
        self.assertContains(response, self.schedule1.time_auto)

    def test_update_view_post_valid(self):
        data = {
            'time_auto': '09:00:00 AM', # Update doesn't have the 2-hour restriction
        }
        response = self.client.post(reverse('autowistimeschedule_edit', args=[self.schedule1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.schedule1.refresh_from_db()
        self.assertEqual(self.schedule1.time_auto, '09:00:00 AM')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Auto WIS Time Schedule updated successfully.')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAutoWISTimeScheduleList')

    def test_delete_view_get(self):
        response = self.client.get(reverse('autowistimeschedule_delete', args=[self.schedule1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_masters/autowistimeschedule/_autowistimeschedule_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.schedule1.time_auto)

    def test_delete_view_post(self):
        response = self.client.delete(reverse('autowistimeschedule_delete', args=[self.schedule1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(AutoWISTimeSchedule.objects.filter(pk=self.schedule1.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Auto WIS Time Schedule deleted successfully.')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshAutoWISTimeScheduleList')

```

### Step 5: HTMX and Alpine.js Integration

The provided templates and views are designed for seamless HTMX and Alpine.js integration:

-   **HTMX for Dynamic Updates:**
    -   The main `list.html` uses `hx-get` to load the table content from `{% url 'autowistimeschedule_table' %}` on page load and `refreshAutoWISTimeScheduleList` event.
    -   Add/Edit/Delete buttons use `hx-get` to load the respective partial forms (`_autowistimeschedule_form.html`, `_autowistimeschedule_confirm_delete.html`) into a modal via `hx-target="#modalContent"`.
    -   Form submissions (`hx-post`, `hx-delete`) are handled by HTMX, which expects a `204 No Content` response on success and triggers `refreshAutoWISTimeScheduleList` to update the main list.
    -   Validation errors on forms result in a `422 Unprocessable Entity` response, and HTMX swaps the form back with errors.

-   **Alpine.js for UI State Management:**
    -   The modal (`#modal`) visibility is controlled using Alpine.js's `x-data` or `_` (hyperscript) syntax (`on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me`). This keeps the UI logic minimal and declarative.

-   **DataTables for List Views:**
    -   The `_autowistimeschedule_table.html` partial includes the JavaScript to initialize DataTables, providing client-side searching, sorting, and pagination.

-   **DRY Template Inheritance:**
    -   All templates extend `core/base.html` to inherit common structure, CDN links (including HTMX, Alpine.js, jQuery for DataTables, and DataTables CSS/JS).

### Final Notes

This comprehensive plan provides a robust, modern Django solution for the `AutoWIS_Time_Set` ASP.NET application. By adhering to the 'fat model, thin view' principle, leveraging HTMX for dynamic interactions, DataTables for enhanced UI, and ensuring thorough testing, the migrated application will be highly maintainable, scalable, and provide a superior user experience with minimal manual coding. The detailed structure and automated generation approach facilitate a smooth transition, understandable by both technical and non-technical stakeholders.