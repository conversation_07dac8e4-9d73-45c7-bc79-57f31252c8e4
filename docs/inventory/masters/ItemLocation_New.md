## ASP.NET to Django Conversion Script: Item Location Master

This document outlines the strategic transition of your legacy ASP.NET 'Item Location' module to a modern, robust Django 5.0+ application. Our approach prioritizes AI-assisted automation, ensuring a smooth, efficient, and low-risk migration with significant business benefits.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

This plan translates your existing 'Item Location' functionality into a modern Django solution. Instead of manual, error-prone code rewriting, we leverage AI to generate the foundational Django components. This ensures consistency, adherence to best practices, and significantly reduces development time and costs. The outcome is a highly maintainable, scalable, and performant application.

The new system will provide:
- **Faster User Experience:** Dynamic updates with HTMX mean no full page reloads, making the application feel snappier.
- **Simplified Frontend:** Relying solely on HTMX and Alpine.js eliminates complex JavaScript frameworks, making the frontend lighter and easier to maintain.
- **Improved Data Management:** DataTables offers powerful search, sort, and pagination capabilities out-of-the-box for better data visibility and control.
- **Enhanced Maintainability:** Django's structured approach, coupled with our 'Fat Model, Thin View' philosophy, ensures business logic is centralized and easy to manage.
- **Future-Proof Architecture:** Built on modern Django, the application is ready for future enhancements and integrations.

---

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

From the `SqlDataSource` definitions in `ItemLocation_New.aspx`, we identify the following:

-   **Table Name:** `tblDG_Location_Master`
-   **Columns:**
    -   `Id` (Primary Key, implicitly integer)
    -   `LocationLabel` (String, based on DropDownList items A-Z)
    -   `LocationNo` (String)
    -   `Description` (String)
    -   `SysDate` (String, derived from `fun.getCurrDate()`)
    -   `SysTime` (String, derived from `fun.getCurrTime()`)
    -   `CompId` (Integer, from session)
    -   `FinYearId` (Integer, from session)
    -   `SessionId` (String, from session `username`)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Based on the ASP.NET code:

-   **Create (Add):** The `GridView2_RowCommand` event handler explicitly manages "Add" and "Add1" commands, triggered by the "Insert" buttons in the `GridView` footer and `EmptyDataTemplate` respectively. This involves collecting `LocationLabel`, `LocationNo`, `Description`, and setting `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId` before executing an `INSERT` command via `SqlDataSource1`.
-   **Read:** The `SqlDataSource1` uses a `SELECT` command to fetch all records from `tblDG_Location_Master` based on `CompId`, populating `GridView1`.
-   **Update/Delete:** These operations are not explicitly defined in the provided ASP.NET snippet. However, for a complete Django CRUD solution, we will include placeholder `Update` and `Delete` views/templates, assuming they would be part of a full-featured application.
-   **Validation:** `RequiredFieldValidator` is used for `LocationLabel`, `LocationNo`, and `Description`. This will be translated to `required=True` in Django forms.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET page primarily uses:

-   **`GridView` (`GridView1`):** Serves as the primary data display and input mechanism. It shows existing records and provides a footer/empty template for new record creation.
    -   **Django Equivalent:** A standard HTML `<table>` combined with DataTables. HTMX will be used to dynamically load and refresh this table.
-   **`DropDownList`:** Used for `LocationLabel` (A-Z selection) in the insert row.
    -   **Django Equivalent:** `forms.ChoiceField` with a `Select` widget.
-   **`TextBox`:** Used for `LocationNo` and `Description` input in the insert row.
    -   **Django Equivalent:** `forms.TextInput` widget.
-   **`Button`:** "Insert" buttons trigger the add functionality.
    -   **Django Equivalent:** HTML `<button>` elements with `hx-post` attributes for HTMX-driven form submissions.
-   **`Label` (`lblMessage`):** Displays status messages like "Record Inserted".
    -   **Django Equivalent:** Django's messages framework displayed in the base template, or HTMX-specific UI updates.

The `OnClientClick=" return confirmationAdd()"` JavaScript would be replaced by Alpine.js for basic client-side confirmation or HTMX's confirmation features.

---

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema, mapping directly to your existing database table.

## Instructions:

This model represents the `tblDG_Location_Master` table. It's configured to not manage the table schema (`managed = False`), allowing Django to work directly with your existing database. Business logic related to location masters, such as formatting or deriving values, will be encapsulated here.

```python
# location_master/models.py
from django.db import models
from django.utils import timezone # For SysDate/SysTime, though we'll follow string as per ASP.NET for now

class LocationMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=1, verbose_name="Location Label")
    location_no = models.CharField(db_column='LocationNo', max_length=255, verbose_name="Number")
    description = models.CharField(db_column='Description', max_length=255, verbose_name="Description")
    sys_date = models.CharField(db_column='SysDate', max_length=10) # As it was a string in ASP.NET
    sys_time = models.CharField(db_column='SysTime', max_length=8) # As it was a string in ASP.NET
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # From username

    class Meta:
        managed = False  # Important: Django won't create/alter this table
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Item Location'
        verbose_name_plural = 'Item Locations'
        ordering = ['-id'] # Matches ORDER BY [Id] DESC from ASP.NET

    def __str__(self):
        return f"{self.location_label}-{self.location_no} ({self.description})"

    def save(self, *args, **kwargs):
        """
        Populate SysDate, SysTime, CompId, FinYearId, SessionId
        This logic simulates the ASP.NET code-behind's parameter setting.
        In a real application, CompId, FinYearId, SessionId would come from
        the request context or a user profile. For this automation, we assume
        these can be set by the calling view or a default mechanism.
        """
        if not self.pk: # Only on creation
            current_datetime = timezone.now()
            self.sys_date = current_datetime.strftime('%Y-%m-%d') # Example format
            self.sys_time = current_datetime.strftime('%H:%M:%S') # Example format
            # These values would typically come from request.session or user object
            # For demonstration, we'll use placeholder values that can be overridden
            # or dynamically set in the view if needed.
            if not self.company_id:
                 self.company_id = 1 # Placeholder, replace with actual logic
            if not self.financial_year_id:
                self.financial_year_id = 1 # Placeholder, replace with actual logic
            if not self.session_id:
                self.session_id = 'SYSTEM' # Placeholder, replace with actual logic (e.g., request.user.username)
        super().save(*args, **kwargs)

```

### 4.2 Forms

Task: Define a Django form for user input, mirroring the ASP.NET input fields and validation.

## Instructions:

This form will handle data input for `LocationMaster` records, including the dropdown for `LocationLabel` and text inputs for `Number` and `Description`. Django's built-in validation will replace the `RequiredFieldValidator`s.

```python
# location_master/forms.py
from django import forms
from .models import LocationMaster

class LocationMasterForm(forms.ModelForm):
    # Mimic ASP.NET DropDownList for LocationLabel (A-Z)
    LOCATION_LABEL_CHOICES = [(chr(i), chr(i)) for i in range(ord('A'), ord('Z') + 1)]
    # Add 'Select' option as first element, mimicking InitialValue="Select"
    location_label = forms.ChoiceField(
        choices=[('Select', 'Select')] + LOCATION_LABEL_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = LocationMaster
        fields = ['location_label', 'location_no', 'description']
        widgets = {
            'location_no': forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Number'}),
            'description': forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Description'}),
        }
        labels = { # Aligning with ASP.NET HeaderText
            'location_label': 'Location Label',
            'location_no': 'Number',
            'description': 'Description',
        }

    def clean_location_label(self):
        location_label = self.cleaned_data.get('location_label')
        if location_label == 'Select':
            raise forms.ValidationError("Location Label is required.")
        return location_label
```

### 4.3 Views

Task: Implement CRUD operations using Django's Class-Based Views (CBVs), ensuring thin views and HTMX support.

## Instructions:

These views are designed to be concise, adhering to the 'thin view' principle. All business logic, such as data persistence and validation, is handled by the model and form. HTMX headers are checked to provide appropriate responses for dynamic updates.

```python
# location_master/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import LocationMaster
from .forms import LocationMasterForm

# Helper to check if the request is HTMX
def is_htmx(request):
    return request.headers.get('HX-Request') == 'true'

# Main list view (renders the full page with the HTMX container for the table)
class LocationMasterListView(ListView):
    model = LocationMaster
    template_name = 'location_master/locationmaster/list.html'
    context_object_name = 'location_masters' # Plural name for template context

# Partial view for the DataTables table, loaded via HTMX
class LocationMasterTablePartialView(ListView):
    model = LocationMaster
    template_name = 'location_master/locationmaster/_locationmaster_table.html'
    context_object_name = 'location_masters' # Plural name for template context

# Create View using a modal form with HTMX
class LocationMasterCreateView(CreateView):
    model = LocationMaster
    form_class = LocationMasterForm
    template_name = 'location_master/locationmaster/_locationmaster_form.html' # Partial template for modal

    def form_valid(self, form):
        # Assign session/system-level parameters before saving, mimicking ASP.NET
        # These values would typically come from the request.session or request.user
        # For this example, we'll set placeholder values or derive from request.user
        if self.request.user.is_authenticated:
            form.instance.session_id = self.request.user.username
            # Assuming company_id and financial_year_id are available in session
            # or a user profile. Using placeholders for demonstration.
            # Replace with actual logic to retrieve from self.request.session
            form.instance.company_id = self.request.session.get('compid', 1) # Default 1
            form.instance.financial_year_id = self.request.session.get('finyear', 1) # Default 1
        else:
            # Handle anonymous user or default settings
            form.instance.session_id = 'ANONYMOUS'
            form.instance.company_id = 1
            form.instance.financial_year_id = 1

        response = super().form_valid(form)
        messages.success(self.request, 'Item Location added successfully.')
        if is_htmx(self.request):
            return HttpResponse(
                status=204, # No content, tells HTMX not to swap anything
                headers={
                    'HX-Trigger': 'refreshLocationMasterList' # Custom event to refresh the table
                }
            )
        return response # Fallback for non-HTMX request

    def form_invalid(self, form):
        # Render the form again with errors for HTMX modal
        return render(self.request, self.template_name, {'form': form})

# Update View using a modal form with HTMX
class LocationMasterUpdateView(UpdateView):
    model = LocationMaster
    form_class = LocationMasterForm
    template_name = 'location_master/locationmaster/_locationmaster_form.html' # Partial template for modal

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item Location updated successfully.')
        if is_htmx(self.request):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLocationMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        return render(self.request, self.template_name, {'form': form})

# Delete View with confirmation via HTMX modal
class LocationMasterDeleteView(DeleteView):
    model = LocationMaster
    template_name = 'location_master/locationmaster/_locationmaster_confirm_delete.html' # Partial template for modal

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Location deleted successfully.')
        if is_htmx(request):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLocationMasterList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the object to the template for display in confirmation
        context['location_master'] = self.get_object()
        return context

```

### 4.4 Templates

Task: Create templates for each view, leveraging HTMX, Alpine.js, and DataTables for a dynamic and interactive user interface.

## Instructions:

These templates ensure a modern user experience with dynamic content loading and form submissions without full page reloads. The main list template sets up the HTMX environment, while partial templates handle modal content for CRUD operations.

```html
<!-- location_master/templates/location_master/locationmaster/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Item Locations</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out"
            hx-get="{% url 'locationmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Location
        </button>
    </div>
    
    <div id="locationMasterTable-container"
         hx-trigger="load, refreshLocationMasterList from:body"
         hx-get="{% url 'locationmaster_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex items-center justify-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Item Locations...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation, managed by Alpine.js and HTMX -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:add-location-master-modal.window="showModal = true"
         _="on click if event.target.id == 'modal' remove .is-active from me then set #modalContent.innerHTML to ''"
         >
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 relative transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add DataTables JS here, assuming core/base.html already includes jQuery and necessary DataTables CDNs -->
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be initialized here if needed
        // For modal, simply adding/removing 'hidden' or 'is-active' class is often sufficient
        // or a global x-data 'showModal' and events
    });

    // Custom event listener for refreshing the table
    document.body.addEventListener('refreshLocationMasterList', function() {
        console.log('Refreshing Location Master list...');
        // HTMX automatically re-requests if hx-trigger specifies custom event on target
    });
</script>
{% endblock %}

```

```html
<!-- location_master/templates/location_master/locationmaster/_locationmaster_table.html -->
<!-- This partial template is loaded dynamically by HTMX -->
<table id="locationMasterTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location Label</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Number</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in location_masters %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.location_label }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.location_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ obj.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md text-xs shadow-sm transition duration-200 ease-in-out mr-2"
                    hx-get="{% url 'locationmaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-xs shadow-sm transition duration-200 ease-in-out"
                    hx-get="{% url 'locationmaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 text-center text-gray-500">No item locations found. Click "Add New Location" to start.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#locationMasterTable')) {
            $('#locationMasterTable').DataTable().destroy();
        }
        $('#locationMasterTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize="20"
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            // Additional DataTables configurations can go here
        });
    });
</script>

```

```html
<!-- location_master/templates/location_master/locationmaster/_locationmaster_form.html -->
<!-- This partial template is loaded into the modal for Add/Edit operations -->
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Item Location</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-4">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.errors %}
            <p class="text-red-600 text-xs mt-1">{{ field.errors|join:", " }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4 pt-4 border-t">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out"
                _="on click remove .is-active from #modal then set #modalContent.innerHTML to ''">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out">
                <i class="fas fa-save mr-2"></i>Save
            </button>
        </div>
    </form>
</div>

```

```html
<!-- location_master/templates/location_master/locationmaster/_locationmaster_confirm_delete.html -->
<!-- This partial template is loaded into the modal for Delete confirmation -->
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Item Location:</p>
    <p class="text-lg font-medium text-red-600 mb-6">
        "{{ location_master.location_label }}-{{ location_master.location_no }}" ({{ location_master.description }})?
    </p>
    
    <form hx-post="{% url 'locationmaster_delete' location_master.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-center space-x-4 mt-6">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-200 ease-in-out"
                _="on click remove .is-active from #modal then set #modalContent.innerHTML to ''">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i>Delete
            </button>
        </div>
    </form>
</div>

```

### 4.5 URLs

Task: Define URL patterns for the views, ensuring clean and semantic routing.

## Instructions:

These URL patterns provide clear endpoints for the list view, and dedicated routes for creating, updating, and deleting `LocationMaster` records, including a specific route for the HTMX-loaded table partial.

```python
# location_master/urls.py
from django.urls import path
from .views import (
    LocationMasterListView, 
    LocationMasterCreateView, 
    LocationMasterUpdateView, 
    LocationMasterDeleteView,
    LocationMasterTablePartialView # For HTMX partial table
)

urlpatterns = [
    # Main page for displaying the list (includes HTMX container)
    path('item-locations/', LocationMasterListView.as_view(), name='locationmaster_list'),
    
    # HTMX endpoint for fetching the table content only
    path('item-locations/table/', LocationMasterTablePartialView.as_view(), name='locationmaster_table'),
    
    # HTMX endpoint for the 'Add' form (modal content)
    path('item-locations/add/', LocationMasterCreateView.as_view(), name='locationmaster_add'),
    
    # HTMX endpoint for the 'Edit' form (modal content)
    path('item-locations/edit/<int:pk>/', LocationMasterUpdateView.as_view(), name='locationmaster_edit'),
    
    # HTMX endpoint for the 'Delete' confirmation (modal content)
    path('item-locations/delete/<int:pk>/', LocationMasterDeleteView.as_view(), name='locationmaster_delete'),
]

```

### 4.6 Tests

Task: Write comprehensive unit tests for the model and integration tests for views to ensure functionality and maintainability.

## Instructions:

These tests provide robust coverage for your `LocationMaster` module. Model tests verify data integrity and business logic, while view tests ensure correct rendering, form submission, and HTMX interactions. Achieving high test coverage is crucial for confidently refactoring and extending the application.

```python
# location_master/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import OperationalError
from .models import LocationMaster
from .forms import LocationMasterForm
from django.contrib.messages import get_messages
import datetime # For SysDate/SysTime mock or comparison

# Mocking timezone for consistent SysDate/SysTime in tests
from unittest.mock import patch
from django.utils import timezone

class LocationMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        # We need to manually set PK because `id` is not auto-incrementing in managed=False
        try:
            LocationMaster.objects.create(
                id=1,
                location_label='A',
                location_no='001',
                description='Test Location 1',
                sys_date='2023-01-01',
                sys_time='10:00:00',
                company_id=100,
                financial_year_id=2023,
                session_id='testuser'
            )
            LocationMaster.objects.create(
                id=2,
                location_label='B',
                location_no='002',
                description='Test Location 2',
                sys_date='2023-01-02',
                sys_time='11:00:00',
                company_id=100,
                financial_year_id=2023,
                session_id='testuser2'
            )
        except OperationalError:
            # Handle case where the table might not exist (e.g., if you run makemigrations)
            # In a real scenario with managed=False, the table is assumed to exist.
            print("Warning: tblDG_Location_Master might not exist. Ensure your database is set up correctly.")
            pass # Continue tests, some might fail if data cannot be inserted

    def test_location_master_creation(self):
        obj = LocationMaster.objects.get(id=1)
        self.assertEqual(obj.location_label, 'A')
        self.assertEqual(obj.location_no, '001')
        self.assertEqual(obj.description, 'Test Location 1')
        self.assertEqual(obj.company_id, 100)
        self.assertEqual(obj.session_id, 'testuser')

    def test_location_label_verbose_name(self):
        obj = LocationMaster.objects.get(id=1)
        field_label = obj._meta.get_field('location_label').verbose_name
        self.assertEqual(field_label, 'Location Label')

    def test_str_method(self):
        obj = LocationMaster.objects.get(id=1)
        self.assertEqual(str(obj), "A-001 (Test Location 1)")
    
    @patch('django.utils.timezone.now')
    def test_save_method_new_instance(self, mock_now):
        mock_now.return_value = datetime.datetime(2024, 7, 15, 12, 30, 0, tzinfo=datetime.timezone.utc)
        
        # Test creation of a new instance (id=3)
        new_location = LocationMaster(
            id=3, # Must provide id for managed=False, primary_key=True
            location_label='C',
            location_no='003',
            description='New Location Description',
            company_id=200, # Override placeholder
            financial_year_id=2024, # Override placeholder
            session_id='newuser' # Override placeholder
        )
        new_location.save()
        
        retrieved_location = LocationMaster.objects.get(id=3)
        self.assertEqual(retrieved_location.sys_date, '2024-07-15')
        self.assertEqual(retrieved_location.sys_time, '12:30:00')
        self.assertEqual(retrieved_location.company_id, 200)
        self.assertEqual(retrieved_location.financial_year_id, 2024)
        self.assertEqual(retrieved_location.session_id, 'newuser')

    def test_save_method_existing_instance(self):
        obj = LocationMaster.objects.get(id=1)
        old_sys_date = obj.sys_date
        old_sys_time = obj.sys_time
        obj.description = 'Updated Description'
        obj.save()
        
        updated_obj = LocationMaster.objects.get(id=1)
        self.assertEqual(updated_obj.description, 'Updated Description')
        # SysDate/SysTime should NOT change on update if logic is 'if not self.pk'
        self.assertEqual(updated_obj.sys_date, old_sys_date)
        self.assertEqual(updated_obj.sys_time, old_sys_time)

class LocationMasterFormTest(TestCase):
    def test_form_valid_data(self):
        form = LocationMasterForm(data={
            'location_label': 'X',
            'location_no': '999',
            'description': 'Valid description'
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_data_missing_fields(self):
        form = LocationMasterForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('location_label', form.errors)
        self.assertIn('location_no', form.errors)
        self.assertIn('description', form.errors)

    def test_form_invalid_location_label_select(self):
        form = LocationMasterForm(data={
            'location_label': 'Select',
            'location_no': '123',
            'description': 'Description'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('location_label', form.errors)
        self.assertEqual(form.errors['location_label'], ['Location Label is required.'])

class LocationMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        LocationMaster.objects.create(
            id=1,
            location_label='A',
            location_no='001',
            description='Test Location 1',
            sys_date='2023-01-01',
            sys_time='10:00:00',
            company_id=1,
            financial_year_id=1,
            session_id='testuser'
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('locationmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'location_master/locationmaster/list.html')
        self.assertIn('location_masters', response.context) # Check if context contains location_masters

    def test_table_partial_view_get(self):
        # Simulate HTMX request for the table partial
        response = self.client.get(reverse('locationmaster_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'location_master/locationmaster/_locationmaster_table.html')
        self.assertIn('location_masters', response.context)
        self.assertContains(response, 'Test Location 1')

    def test_create_view_get(self):
        response = self.client.get(reverse('locationmaster_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'location_master/locationmaster/_locationmaster_form.html')
        self.assertIn('form', response.context)

    @patch('django.utils.timezone.now')
    def test_create_view_post_success(self, mock_now):
        mock_now.return_value = datetime.datetime(2024, 7, 15, 14, 0, 0, tzinfo=datetime.timezone.utc)
        
        initial_count = LocationMaster.objects.count()
        data = {
            'location_label': 'Z',
            'location_no': '999',
            'description': 'New Location via HTMX',
        }
        # Simulate HTMX request
        response = self.client.post(reverse('locationmaster_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(LocationMaster.objects.count(), initial_count + 1)
        new_obj = LocationMaster.objects.latest('id') # Assuming id increments
        self.assertEqual(new_obj.location_label, 'Z')
        self.assertEqual(new_obj.location_no, '999')
        self.assertEqual(new_obj.description, 'New Location via HTMX')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshLocationMasterList')
        
        # Check messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Location added successfully.')

    def test_create_view_post_invalid(self):
        initial_count = LocationMaster.objects.count()
        data = {
            'location_label': 'Select', # Invalid choice
            'location_no': '',
            'description': '',
        }
        response = self.client.post(reverse('locationmaster_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'location_master/locationmaster/_locationmaster_form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('location_label', response.context['form'].errors)
        self.assertIn('location_no', response.context['form'].errors)
        self.assertEqual(LocationMaster.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        obj = LocationMaster.objects.get(id=1)
        response = self.client.get(reverse('locationmaster_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'location_master/locationmaster/_locationmaster_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)

    def test_update_view_post_success(self):
        obj = LocationMaster.objects.get(id=1)
        data = {
            'location_label': 'A',
            'location_no': '001',
            'description': 'Updated Description',
        }
        response = self.client.post(reverse('locationmaster_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.description, 'Updated Description')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshLocationMasterList')
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Location updated successfully.')

    def test_update_view_post_invalid(self):
        obj = LocationMaster.objects.get(id=1)
        old_description = obj.description
        data = {
            'location_label': 'Select', # Invalid choice
            'location_no': '001',
            'description': '', # Empty description
        }
        response = self.client.post(reverse('locationmaster_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'location_master/locationmaster/_locationmaster_form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('location_label', response.context['form'].errors)
        self.assertIn('description', response.context['form'].errors)
        obj.refresh_from_db()
        self.assertEqual(obj.description, old_description) # Should not be updated

    def test_delete_view_get(self):
        obj = LocationMaster.objects.get(id=1)
        response = self.client.get(reverse('locationmaster_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'location_master/locationmaster/_locationmaster_confirm_delete.html')
        self.assertIn('location_master', response.context)
        self.assertEqual(response.context['location_master'], obj)

    def test_delete_view_post_success(self):
        obj = LocationMaster.objects.get(id=1)
        response = self.client.post(reverse('locationmaster_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertFalse(LocationMaster.objects.filter(id=obj.id).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshLocationMasterList')
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Location deleted successfully.')

    def test_delete_view_post_non_existent_object(self):
        response = self.client.post(reverse('locationmaster_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for Dynamic Content:**
    -   The `locationMasterTable-container` in `list.html` uses `hx-get="{% url 'locationmaster_table' %}"` and `hx-trigger="load, refreshLocationMasterList from:body"` to load the table content dynamically upon page load and whenever the `refreshLocationMasterList` custom event is triggered (after a successful CRUD operation).
    -   Add/Edit/Delete buttons use `hx-get` to fetch the form/confirmation partials into `#modalContent`, combined with `hx-target` and `hx-trigger`.
    -   Form submissions within the modal use `hx-post` to the same URL, with `hx-swap="none"` and `HX-Trigger` header in the Django view to signal a refresh of the list, ensuring the modal closes and the list updates.
-   **Alpine.js for UI State:**
    -   The `#modal` in `list.html` uses `x-data="{ showModal: false }"` and `x-show="showModal"` to manage its visibility.
    -   The `on click` `Alpine.js` directives in the buttons and modal background effectively show/hide the modal by toggling the `is-active` class. The `set #modalContent.innerHTML to ''` cleans the modal after closing.
-   **DataTables for List Views:**
    -   The `_locationmaster_table.html` partial initializes `DataTables` on the `locationMasterTable` when it's loaded into the DOM by HTMX. This provides client-side sorting, searching, and pagination as observed in the ASP.NET GridView.
-   **No Full Page Reloads:** All user interactions (add, edit, delete, table refresh) are designed to occur without full page reloads, providing a smooth, app-like experience.

## Final Notes

-   **Placeholder Replacement:** Ensure `core/base.html` exists and contains necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and Font Awesome for icons. Replace placeholder values like `company_id` and `financial_year_id` in the `LocationMasterCreateView` with actual logic to retrieve these from your Django `request.session` or authenticated user object.
-   **DRY Principles:** Templates use inheritance (`{% extends 'core/base.html' %}`) and partials (`_locationmaster_table.html`, `_locationmaster_form.html`, `_locationmaster_confirm_delete.html`) to avoid code duplication.
-   **Fat Model, Thin View:** Business logic (like `SysDate`, `SysTime`, `CompId` assignment) is handled within the `LocationMaster` model's `save` method, keeping views concise and focused on request/response handling.
-   **Comprehensive Testing:** The provided `tests.py` includes unit tests for the `LocationMaster` model and integration tests for all CRUD views, covering various scenarios including valid/invalid form submissions and HTMX interactions. This ensures high code quality and confidence in future modifications.
-   **Tailwind CSS:** The provided HTML includes Tailwind CSS classes (`bg-blue-600`, `rounded-md`, `shadow-md`, etc.) for modern, responsive styling. Ensure Tailwind CSS is properly set up in your Django project.