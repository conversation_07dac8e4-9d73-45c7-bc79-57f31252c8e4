## ASP.NET to Django Conversion Script: Customer <PERSON>llan Edit & Clear Details

This modernization plan outlines the strategic transition of your existing ASP.NET Customer Challan management module to a modern Django application. Our focus is on leveraging AI-assisted automation to streamline the conversion process, ensuring a robust, scalable, and maintainable solution.

By migrating to Django, your organization will benefit from:

1.  **Enhanced Maintainability:** Django's clear structure and best practices reduce code complexity, making it easier to understand, update, and debug.
2.  **Improved Performance:** Optimized data handling and efficient request processing lead to faster response times for your users.
3.  **Modern User Experience:** Integration with HTMX and Alpine.js provides dynamic, responsive interfaces without the overhead of heavy JavaScript frameworks, leading to a smoother user experience.
4.  **Increased Developer Productivity:** Django's "batteries included" philosophy, combined with automation-driven migration, significantly speeds up development and future feature implementation.
5.  **Cost Efficiency:** Reduced manual coding effort through automation, coupled with a more efficient development lifecycle, translates to lower operational costs.

This plan details the necessary Django application files, emphasizing our "Fat Model, Thin View" architecture, and utilizing HTMX, Alpine.js, and DataTables for a superior user experience.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we've identified interactions with several SQL Server tables through direct SQL queries and stored procedures (`GetCustomerChallan_Details`, `GetCust_Challan_Clear_Edit`).

**Inferred Tables and Columns:**

*   **`tblInv_Customer_Challan_Master` (Django Model: `CustomerChallanMaster`)**
    *   `Id` (Primary Key, integer)
    *   `CCNo` (Challan Number, string)
    *   `CCDate` (Challan Date, date)
    *   `WONo` (Work Order Number, string)
    *   `CustomerId` (Integer, likely a foreign key to a Customer table)
    *   `SysDate` (System Date, date)
    *   `SysTime` (System Time, time)
    *   `SessionId` (User session ID, string)
    *   `CompId` (Company ID, integer)
    *   `FinYearId` (Financial Year ID, integer)

*   **`tblInv_Customer_Challan_Details` (Django Model: `CustomerChallanDetail`)**
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblInv_Customer_Challan_Master.Id`, integer)
    *   `PRDId` (Foreign Key to `tblMM_PR_Details.Id`, integer)
    *   `ItemCode` (Item Code, string)
    *   `ManfDesc` (Description, string)
    *   `Symbol` (Unit of Measure, string)
    *   `ChallanQty` (Challan Quantity, decimal/float, editable)

*   **`tblInv_Customer_Challan_Clear` (Django Model: `ChallanClearance`)**
    *   `Id` (Primary Key, integer)
    *   `DId` (Foreign Key to `tblInv_Customer_Challan_Details.Id`, integer)
    *   `ClearQty` (Cleared Quantity, decimal/float, editable)
    *   `SysDate` (System Date, date)
    *   `SysTime` (System Time, time)
    *   `CompId` (Company ID, integer)
    *   `FinYearId` (Financial Year ID, integer)
    *   `SessionId` (User session ID, string)

*   **`tblMM_PR_Details` (Django Model: `PRDetail`)**
    *   `Id` (Primary Key, integer)
    *   `Qty` (Purchase Request Quantity, decimal/float)
    *   `CustomerId` (Integer, inferred from `disableCheck` method's SQL)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic within the ASP.NET code.

**Instructions:**
The ASP.NET page primarily handles **Read** and **Update** operations, specifically for modifying existing Customer Challan details and recording clearances.

*   **Read Operations (Data Display):**
    *   `fillGrid()`: Populates the first grid (`GridView2`) with existing Customer Challan `Details` records. This corresponds to viewing/editing `ChallanQty`.
    *   `LoadGrid()`: Populates the second grid (`GridView1`) with existing Customer Challan `Clearance` records. This corresponds to viewing/editing `ClearQty`.

*   **Update Operations (Data Modification):**
    *   `BtnAdd_Click` (for `GridView2`): Handles updates to `ChallanQty` for selected `CustomerChallanDetail` records. It also updates system metadata on the `CustomerChallanMaster`.
        *   **Validation:** Ensures checked items have valid, non-zero numeric quantities.
    *   `BtnAdd1_Click` (for `GridView1`): Handles updates to `ClearQty` for selected `ChallanClearance` records.
        *   **Validation:** Ensures checked items have valid, non-zero numeric quantities. Includes a critical business rule: `(ChallanQty - (CleardQty + TotChalnQty - ClearQty)) >= 0`, meaning the new cleared quantity, combined with existing clearances, must not exceed the original challan quantity.

*   **Business Logic & Helper Functions:**
    *   `disableCheck()`: Calculates remaining PR quantity for each Challan Detail line. This logic will be moved to the `CustomerChallanDetail` model.
    *   `GetValidate()`, `GetValidates()`: Controls the enabling/disabling of input fields based on checkbox selection and performs client-side validation. This will be handled by Alpine.js for UI state and Django forms for server-side validation.
    *   `CheckBox1_CheckedChanged`, `CheckBox2_CheckedChanged`: Trigger the validation updates. In Django, this will be handled dynamically via HTMX and Alpine.js.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in the user interface.

**Instructions:**
The user interface consists of two main tabs, each containing a data grid and associated action buttons.

*   **TabContainer (`TabContainer1`):** Represents a tabbed interface. This will be recreated using standard HTML with HTMX attributes to load content dynamically.
*   **GridView (`GridView2` and `GridView1`):** These are the core data display and input components.
    *   They are primarily tables with rows of data, checkboxes, labels, and editable text boxes (`txtqty`).
    *   They feature pagination (`PagerSettings`).
    *   They display calculated values (e.g., "Cleared Qty", "Remaining Qty" - though "Remaining Qty" was mostly backend in ASP.NET).
*   **TextBox (`txtqty`):** Used for inputting `ChallanQty` and `ClearQty`.
*   **CheckBox (`CheckBox1`, `CheckBox2`):** Used to select rows for update and to enable/disable associated `txtqty` fields.
*   **Labels (`lblId`, `lblCCNo`, `lblWONo`, etc.):** Display read-only data.
*   **Buttons (`BtnAdd`, `Btncancel`, `BtnAdd1`, `BtnCancel1`):** Trigger update and cancel actions.
    *   `OnClientClick="return confirmationUpdate;"` indicates client-side confirmation for updates.
    *   `ClientScript.RegisterStartupScript` is used for showing alerts. These will be replaced by Django's `messages` framework, potentially displayed via HTMX.
*   **Validation Controls (`RegularExpressionValidator`, `RequiredFieldValidator`):** Server-side and client-side validation for quantity inputs. This will be handled by Django Forms and JavaScript validation.

### Step 4: Generate Django Code

We will create a new Django application named `inventory` to house this module.

#### 4.1 Models (in `inventory/models.py`)

This section defines the Django models that map directly to your existing database tables. We'll include business logic as methods within these models, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal, InvalidOperation

class CustomerChallanMaster(models.Model):
    """
    Corresponds to tblInv_Customer_Challan_Master.
    Represents the main customer challan record.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    cc_no = models.CharField(db_column='CCNo', max_length=50, blank=True, null=True)
    cc_date = models.DateField(db_column='CCDate', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    customer_id = models.IntegerField(db_column='CustomerId', blank=True, null=True) # Assuming FK to customer
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Master'
        verbose_name = 'Customer Challan (Master)'
        verbose_name_plural = 'Customer Challans (Master)'

    def __str__(self):
        return f"CC No: {self.cc_no} ({self.cc_date})"

class PRDetail(models.Model):
    """
    Corresponds to tblMM_PR_Details.
    Used for Purchase Request Quantity validation.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, default=Decimal('0.000'))
    customer_id = models.IntegerField(db_column='CustomerId', blank=True, null=True) # Inferred

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR Detail {self.id} - Qty: {self.qty}"

class CustomerChallanDetail(models.Model):
    """
    Corresponds to tblInv_Customer_Challan_Details.
    Represents individual items within a customer challan.
    Handles ChallanQty updates and related business logic.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(CustomerChallanMaster, models.DO_NOTHING, db_column='MId')
    pr_detail = models.ForeignKey(PRDetail, models.DO_NOTHING, db_column='PRDId', blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=200, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)
    challan_qty = models.DecimalField(db_column='ChallanQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Details'
        verbose_name = 'Customer Challan Detail'
        verbose_name_plural = 'Customer Challan Details'

    def __str__(self):
        return f"Challan Detail {self.id} - Item: {self.item_code}"

    def get_total_challaned_qty_from_pr(self):
        """
        Calculates the total challan quantity against the associated PR detail.
        Corresponds to parts of disableCheck.
        """
        if not self.pr_detail:
            return Decimal('0.000')
        
        # Exclude the current challan detail's quantity to prevent double counting
        # if this method is used for remaining_pr_qty calculation in a context where the current item's
        # challan_qty is being considered. The original code implicitly excluded it by summing all
        # ChallanQty for a PRDId.
        
        # The original ASP.NET Sum(tblInv_Customer_Challan_Details.ChallanQty) included the current ChallanQty.
        # This will be handled in the update logic to correctly assess the remaining.
        
        total_challaned_qty = CustomerChallanDetail.objects.filter(
            pr_detail=self.pr_detail,
            master__comp_id=self.master.comp_id,
            master__customer_id=self.master.customer_id
        ).exclude(id=self.id).aggregate(total=models.Sum('challan_qty'))['total'] or Decimal('0.000')
        
        return total_challaned_qty

    def get_remaining_pr_qty(self):
        """
        Calculates the remaining quantity from the Purchase Request for this detail.
        Corresponds to the logic in disableCheck for lblrmnqty.
        """
        if not self.pr_detail:
            return Decimal('0.000')
        
        pr_qty = self.pr_detail.qty
        total_challaned_from_pr = self.get_total_challaned_qty_from_pr() # Sum of other challans for this PRDId
        
        # Remaining is PR_Qty - (Sum of ALL ChallanQty for this PRDId)
        # The ASP.NET logic: PrQty - TotChalnQty. TotChalnQty included the current ChallanQty from DS3.
        # So we need to consider the current challan_qty in the total.
        
        total_existing_challan_qty_for_pr = CustomerChallanDetail.objects.filter(
            pr_detail=self.pr_detail,
            master__comp_id=self.master.comp_id,
            master__customer_id=self.master.customer_id
        ).aggregate(total=models.Sum('challan_qty'))['total'] or Decimal('0.000')
        
        # When calculating remaining, subtract all challaned quantities from the PR quantity.
        # If this is used for validation *before* updating, we might need to adjust for the current item's original quantity.
        # Let's make this method return the total quantity *already allocated* by challans.
        # The validation method will then use this + the new proposed quantity.
        
        return pr_qty - total_existing_challan_qty_for_pr

    def update_challan_quantity(self, new_qty):
        """
        Updates the challan_qty for this detail.
        Includes validation logic from BtnAdd_Click.
        Returns (True, "Success message") or (False, "Error message").
        """
        try:
            new_qty = Decimal(new_qty)
        except InvalidOperation:
            return False, "Invalid quantity format."

        if new_qty <= 0:
            return False, "Quantity must be greater than zero."
            
        # The original ASP.NET code had a commented-out PR validation here.
        # If self.pr_detail and self.pr_detail.qty is available, we should re-enable this.
        # This check is crucial for inventory integrity.
        
        # current_total_challaned_for_pr = self.get_total_challaned_qty_from_pr()
        # total_challaned_including_this_update = current_total_challaned_for_pr + new_qty
        
        # if self.pr_detail and total_challaned_including_this_update > self.pr_detail.qty:
        #     return False, f"Proposed quantity {new_qty} exceeds remaining PR quantity."

        self.challan_qty = new_qty
        self.save()
        
        # Update master sys_date/time as per original code
        self.master.sys_date = timezone.localdate()
        self.master.sys_time = timezone.localtime().time()
        self.master.save()
        
        return True, "Customer Challan Detail updated successfully."

class ChallanClearance(models.Model):
    """
    Corresponds to tblInv_Customer_Challan_Clear.
    Handles ClearQty updates and complex validation.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    detail = models.ForeignKey(CustomerChallanDetail, models.DO_NOTHING, db_column='DId')
    clear_qty = models.DecimalField(db_column='ClearQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Customer_Challan_Clear'
        verbose_name = 'Challan Clearance'
        verbose_name_plural = 'Challan Clearances'

    def __str__(self):
        return f"Clearance {self.id} for Detail {self.detail.id}"

    def get_total_cleared_qty_for_detail(self):
        """
        Calculates the sum of all cleared quantities for the associated Challan Detail.
        Corresponds to the logic in GetValidates for TotClearedQty.
        """
        total_cleared_qty = ChallanClearance.objects.filter(
            detail=self.detail,
            comp_id=self.comp_id # Assuming CompId is relevant for clearance
        ).exclude(id=self.id).aggregate(total=models.Sum('clear_qty'))['total'] or Decimal('0.000')
        return total_cleared_qty

    def update_clear_quantity(self, new_qty, original_clear_qty_for_validation):
        """
        Updates the clear_qty for this clearance record.
        Includes complex validation logic from BtnAdd1_Click.
        Returns (True, "Success message") or (False, "Error message").
        """
        try:
            new_qty = Decimal(new_qty)
            original_clear_qty_for_validation = Decimal(original_clear_qty_for_validation) # The original clear qty from the form row
        except InvalidOperation:
            return False, "Invalid quantity format."

        if new_qty <= 0:
            return False, "Cleared quantity must be greater than zero."
            
        # Replicate ASP.NET complex validation: ChallanQty - (CleardQty + TotChalnQty - ClearQty) >= 0
        # Where:
        # ChallanQty = self.detail.challan_qty
        # ClearedQty = new_qty (proposed)
        # TotChalnQty = sum of OTHER clearances for this detail (from get_total_cleared_qty_for_detail)
        # ClearQty = original_clear_qty_for_validation (the value of *this specific* clearance BEFORE this update)

        total_cleared_excluding_this = self.get_total_cleared_qty_for_detail()
        
        # ASP.NET formula breakdown:
        # TotChalnQty (from DS3) was sum of all ClearQty for that DId.
        # The validation was (ChallanQty - (CleardQty + TotChalnQty - ClearQty)) >= 0
        # Let's redefine:
        # Sum of all previous clearances (including the current item's *original* value if it exists):
        # old_total_cleared_qty_for_detail = total_cleared_excluding_this + original_clear_qty_for_validation
        #
        # new_total_cleared_qty_for_detail = total_cleared_excluding_this + new_qty
        #
        # validation_remaining = self.detail.challan_qty - new_total_cleared_qty_for_detail
        
        # Simplified: The sum of all existing clearances for this detail, plus the NEW proposed quantity for THIS clearance,
        # minus the original quantity of THIS clearance being replaced. This ensures we correctly compare against the detail's total.
        
        # Sum of all *other* clearances for this detail:
        other_clearances_total = ChallanClearance.objects.filter(
            detail=self.detail,
            comp_id=self.comp_id
        ).exclude(id=self.id).aggregate(total=models.Sum('clear_qty'))['total'] or Decimal('0.000')

        # Total cleared quantity if this update goes through:
        proposed_total_cleared = other_clearances_total + new_qty
        
        if proposed_total_cleared > self.detail.challan_qty:
            return False, f"Proposed cleared quantity ({new_qty}) combined with others ({other_clearances_total}) exceeds Challan Quantity ({self.detail.challan_qty}). Remaining: {self.detail.challan_qty - proposed_total_cleared:.3f}"
            
        self.clear_qty = new_qty
        self.sys_date = timezone.localdate()
        self.sys_time = timezone.localtime().time()
        self.save()
        
        return True, "Challan Clearance updated successfully."

```

#### 4.2 Forms (in `inventory/forms.py`)

Django forms will handle data validation and input rendering, replacing ASP.NET's validator controls. We'll use a `ModelForm` for single instances and base `Form` for formsets if needed. Given the checkbox-driven enable/disable logic, we'll implement a custom `BaseFormSet` to manage the fields.

```python
from django import forms
from django.forms import formset_factory
from .models import CustomerChallanDetail, ChallanClearance
from decimal import Decimal, InvalidOperation

class CustomerChallanDetailForm(forms.Form):
    """
    Form for updating a single CustomerChallanDetail item in the first grid.
    Note: We use forms.Form here because we are processing a subset of fields
    from the model and handling the object creation/update manually based on checkbox.
    """
    id = forms.IntegerField(widget=forms.HiddenInput())
    # Label for original challan quantity for validation display if needed
    # original_challan_qty = forms.DecimalField(max_digits=18, decimal_places=3, required=False, widget=forms.HiddenInput()) 
    
    challan_qty = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        required=False, # Required only if checkbox is checked
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3',
            'hx-post': 'this.dataset.hxPostUrl', # Placeholder for dynamic URL
            'hx-trigger': 'change, keyup changed delay:500ms',
            'hx-swap': 'none',
            'hx-indicator': '.htmx-indicator',
            'disabled': 'true', # Initially disabled, Alpine.js will enable
            'x-bind:disabled': '!isChecked', # Alpine.js
            'x-model.number': 'qty', # Alpine.js
        })
    )
    is_checked = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'x-model': 'isChecked', # Alpine.js
            'hx-post': 'this.dataset.hxPostUrl', # This checkbox might trigger a validation check or just local UI state
            'hx-trigger': 'change',
            'hx-swap': 'none',
            'hx-indicator': '.htmx-indicator',
            'data-initial-checked': 'false' # For Alpine.js initialization
        })
    )

    def clean_challan_qty(self):
        challan_qty = self.cleaned_data.get('challan_qty')
        is_checked = self.cleaned_data.get('is_checked')

        if is_checked and (challan_qty is None or challan_qty <= 0):
            raise forms.ValidationError("Challan quantity is required and must be greater than zero for checked items.")
        return challan_qty

class ChallanClearanceForm(forms.Form):
    """
    Form for updating a single ChallanClearance item in the second grid.
    """
    id = forms.IntegerField(widget=forms.HiddenInput())
    # original_clear_qty and challan_qty are needed for validation, will be passed from view context
    
    clear_qty = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3',
            'hx-post': 'this.dataset.hxPostUrl',
            'hx-trigger': 'change, keyup changed delay:500ms',
            'hx-swap': 'none',
            'hx-indicator': '.htmx-indicator',
            'disabled': 'true',
            'x-bind:disabled': '!isChecked',
            'x-model.number': 'qty',
        })
    )
    is_checked = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'x-model': 'isChecked',
            'hx-post': 'this.dataset.hxPostUrl',
            'hx-trigger': 'change',
            'hx-swap': 'none',
            'hx-indicator': '.htmx-indicator',
            'data-initial-checked': 'false'
        })
    )

    def clean_clear_qty(self):
        clear_qty = self.cleaned_data.get('clear_qty')
        is_checked = self.cleaned_data.get('is_checked')

        if is_checked and (clear_qty is None or clear_qty <= 0):
            raise forms.ValidationError("Cleared quantity is required and must be greater than zero for checked items.")
        return clear_qty


# We'll use a single form for batch update, using formsets
# The ASP.NET GridView approach essentially acts as a formset.

# A FormSet factory that can be used if we go with an approach to submit all items
# when the main update button is clicked, rather than per-row updates via HTMX.
# For simplicity and HTMX alignment, we'll handle per-row updates, but the main update button
# will submit only the *selected* rows in a single HTMX request.

# Base formset to capture only selected rows
class BaseUpdateFormSet(forms.BaseFormSet):
    def get_queryset(self):
        # We don't want a queryset here, as we're processing arbitrary POST data
        # for selected items, not necessarily a pre-filtered queryset.
        return None 
    
    def __init__(self, *args, **kwargs):
        self.instance_data = kwargs.pop('instance_data', {})
        super().__init__(*args, **kwargs)

    def is_valid(self):
        # Override is_valid to handle dynamic fields and ensure only checked forms are validated fully
        valid = True
        for i in range(self.total_form_count()):
            form = self.forms[i]
            # Populate form with original values needed for model validation
            instance_id = form.cleaned_data.get('id') if form.is_bound else form.initial.get('id')
            if instance_id and instance_id in self.instance_data:
                # Add original values from DB instance to form for model's validation methods
                form.cleaned_data['original_instance'] = self.instance_data[instance_id]
                if not form.is_valid():
                    valid = False
            elif form.is_bound and not form.is_valid(): # If it's a bound form and not valid after basic clean
                 valid = False
        return valid

# Factory for the formset for CustomerChallanDetail
CustomerChallanDetailFormSet = formset_factory(CustomerChallanDetailForm, extra=0)

# Factory for the formset for ChallanClearance
ChallanClearanceFormSet = formset_factory(ChallanClearanceForm, extra=0)

```

#### 4.3 Views (in `inventory/views.py`)

Views will be thin, primarily handling HTTP requests, orchestrating data flow, and returning responses. All complex business logic will reside in the models.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import render, get_object_or_404, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from django.forms import formset_factory

from .models import CustomerChallanMaster, CustomerChallanDetail, ChallanClearance, PRDetail
from .forms import CustomerChallanDetailForm, ChallanClearanceForm, CustomerChallanDetailFormSet, ChallanClearanceFormSet
from decimal import Decimal, InvalidOperation

class CustomerChallanEditDashboardView(TemplateView):
    """
    Renders the main dashboard for editing Customer Challan details and clearances.
    This view will contain the tab structure and serve as the entry point for HTMX-loaded content.
    """
    template_name = 'inventory/customerchallan/edit_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        master_pk = self.kwargs.get('pk')
        context['master_challan'] = get_object_or_404(CustomerChallanMaster, pk=master_pk)
        return context

    # No POST handling here, as all form submissions are handled by partial views via HTMX.

class CustomerChallanDetailTablePartialView(View):
    """
    Handles displaying and updating the Customer Challan Details grid (first tab).
    This acts as both the list view and the update handler for the details.
    """
    template_name = 'inventory/customerchallan/_customerchallandetail_table.html'

    def get(self, request, pk):
        master_challan = get_object_or_404(CustomerChallanMaster, pk=pk)
        details = CustomerChallanDetail.objects.filter(master=master_challan).order_by('id')
        
        # Prepare forms for each detail. Use initial data for current values.
        # This will be used by Alpine.js to set initial state for checkboxes and quantities.
        detail_forms = []
        for detail in details:
            # Calculate remaining PR quantity for display
            remaining_pr_qty = detail.get_remaining_pr_qty()
            
            detail_forms.append({
                'form': CustomerChallanDetailForm(initial={
                    'id': detail.id,
                    'challan_qty': detail.challan_qty,
                    'is_checked': False, # Default to unchecked
                }),
                'detail_obj': detail, # Pass object for direct access to fields
                'remaining_pr_qty': remaining_pr_qty,
                'original_challan_qty': detail.challan_qty # Store original for context
            })
            
        context = {
            'master_pk': pk,
            'detail_forms': detail_forms,
        }
        return render(request, self.template_name, context)

    def post(self, request, pk):
        master_challan = get_object_or_404(CustomerChallanMaster, pk=pk)
        
        # Extract selected items from POST data based on their 'name' attributes
        # e.g., 'items[0][id]', 'items[0][challan_qty]', 'items[0][is_checked]'
        # This mirrors how the original ASP.NET GridView would submit data.
        
        # We need to loop through the incoming form data to process only checked items.
        # The formset approach is more robust for multiple row updates.
        
        data_to_process = []
        # Assuming form fields come in as 'item_id', 'item_challan_qty', 'item_is_checked'
        # Or alternatively, an HTMX request per row as seen in the templates.
        
        # Let's assume individual row updates are triggered by HTMX on quantity change or checkbox change.
        # This view's POST should handle multiple items submitted by the main "Update" button,
        # or potentially a single item update if an HTMX event on `txtqty` triggers it.
        
        # If the button 'Update' is clicked, it will submit a form with selected items.
        # The actual ASP.NET `BtnAdd_Click` iterates `GridView2.Rows` and checks `CheckBox1`.
        # We simulate this by getting `id`, `challan_qty`, `is_checked` for all items.

        # The POST request will contain data for all rows in the form,
        # but we only process rows that have `is_checked` as 'on'.
        
        # Reconstruct formset data from request.POST
        formset_data = []
        
        # Get all detail IDs related to this master challan for verification
        existing_detail_ids = set(CustomerChallanDetail.objects.filter(master=master_challan).values_list('id', flat=True))

        # This reconstruction assumes the POST data comes in a structured way,
        # e.g., from a JavaScript loop packaging `id` and `challan_qty` for selected rows.
        # For simplicity, we will assume HTMX form submission sends selected items only.
        
        # Iterate over submitted data. Assuming data comes as:
        # checked_ids = ['id1', 'id2', ...]
        # challan_qty_id1 = 'value'
        # challan_qty_id2 = 'value'
        
        # Simulating ASP.NET GridView's loop over rows.
        # HTMX approach for this: The main Update button submits a form where each *selected* row's
        # inputs are included with distinct names (e.g. `item-<id>-challan_qty`).
        
        # A more robust HTMX approach for multi-row updates:
        # Use hx-vals to pass data from selected rows when the main update button is clicked.
        # Or, each input field on change sends its update to the backend.
        # For this example, let's process the main update button click, which sends all relevant
        # `id` and `challan_qty` pairs for checked items.
        
        updated_count = 0
        errors = []
        
        # The POST request will contain `id_<row_id>` and `challan_qty_<row_id>` for checked items.
        # E.g., `id_123=123&challan_qty_123=50.00&id_456=456&challan_qty_456=25.00`
        
        # Collect all submitted detail IDs.
        submitted_detail_ids = []
        for key in request.POST:
            if key.startswith('challan_detail_id_'):
                try:
                    detail_id = int(request.POST.get(key))
                    submitted_detail_ids.append(detail_id)
                except (ValueError, TypeError):
                    continue

        if not submitted_detail_ids:
            messages.warning(request, "No items selected for update or invalid data submitted.")
            return HttpResponse(status=204) # No Content, but trigger a re-render if needed
            
        with transaction.atomic():
            for detail_id in submitted_detail_ids:
                if detail_id not in existing_detail_ids:
                    errors.append(f"Invalid detail ID: {detail_id}")
                    continue

                new_qty_str = request.POST.get(f'challan_qty_{detail_id}')
                if not new_qty_str:
                    errors.append(f"Quantity missing for item ID: {detail_id}")
                    continue
                
                try:
                    new_qty = Decimal(new_qty_str)
                except InvalidOperation:
                    errors.append(f"Invalid quantity format for item ID: {detail_id}")
                    continue

                detail_obj = get_object_or_404(CustomerChallanDetail, pk=detail_id, master=master_challan)
                
                # Call model method for validation and update
                success, msg = detail_obj.update_challan_quantity(new_qty)
                if not success:
                    errors.append(f"Item ID {detail_id}: {msg}")
                else:
                    updated_count += 1
        
        if errors:
            for error_msg in errors:
                messages.error(request, error_msg)
            # Re-render the table with errors
            return self.get(request, pk) # Re-render to show errors
        else:
            if updated_count > 0:
                messages.success(request, f"{updated_count} Customer Challan Details updated successfully.")
            else:
                messages.info(request, "No changes were made to Customer Challan Details.")
            
            # HTMX response for success, trigger refresh
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshCustomerChallanDetailList'}
            )

class ChallanClearanceTablePartialView(View):
    """
    Handles displaying and updating the Challan Clearance grid (second tab).
    This acts as both the list view and the update handler for the clearances.
    """
    template_name = 'inventory/customerchallan/_challanclearance_table.html'

    def get(self, request, pk):
        master_challan = get_object_or_404(CustomerChallanMaster, pk=pk)
        
        # Get all challan details associated with this master for filtering clearances
        detail_ids = CustomerChallanDetail.objects.filter(master=master_challan).values_list('id', flat=True)
        clearances = ChallanClearance.objects.filter(detail__id__in=detail_ids).order_by('id')
        
        clearance_forms = []
        for clearance in clearances:
            # Need ChallanQty from detail for validation, and existing cleared qty
            total_cleared_for_detail = clearance.get_total_cleared_qty_for_detail() + clearance.clear_qty # Total including current
            
            clearance_forms.append({
                'form': ChallanClearanceForm(initial={
                    'id': clearance.id,
                    'clear_qty': clearance.clear_qty,
                    'is_checked': False,
                }),
                'clearance_obj': clearance,
                'current_total_cleared': total_cleared_for_detail, # For display
                'original_clear_qty': clearance.clear_qty, # Pass original for model validation
            })
            
        context = {
            'master_pk': pk,
            'clearance_forms': clearance_forms,
        }
        return render(request, self.template_name, context)

    def post(self, request, pk):
        master_challan = get_object_or_404(CustomerChallanMaster, pk=pk)

        # Collect all submitted clearance IDs.
        submitted_clearance_ids = []
        for key in request.POST:
            if key.startswith('clearance_id_'):
                try:
                    clearance_id = int(request.POST.get(key))
                    submitted_clearance_ids.append(clearance_id)
                except (ValueError, TypeError):
                    continue
        
        if not submitted_clearance_ids:
            messages.warning(request, "No items selected for update or invalid data submitted.")
            return HttpResponse(status=204) # No Content, but trigger a re-render if needed

        updated_count = 0
        errors = []
        
        # Get all challan detail IDs for this master to validate clearance IDs
        valid_detail_ids = set(CustomerChallanDetail.objects.filter(master=master_challan).values_list('id', flat=True))

        with transaction.atomic():
            for clearance_id in submitted_clearance_ids:
                new_qty_str = request.POST.get(f'clear_qty_{clearance_id}')
                original_qty_str = request.POST.get(f'original_clear_qty_{clearance_id}') # Need this from form for validation
                
                if not new_qty_str or not original_qty_str:
                    errors.append(f"Quantity missing for clearance ID: {clearance_id}")
                    continue
                
                try:
                    new_qty = Decimal(new_qty_str)
                    original_qty = Decimal(original_qty_str)
                except InvalidOperation:
                    errors.append(f"Invalid quantity format for clearance ID: {clearance_id}")
                    continue

                clearance_obj = get_object_or_404(ChallanClearance, pk=clearance_id)
                
                # Ensure the clearance belongs to a detail under the current master challan
                if clearance_obj.detail.id not in valid_detail_ids:
                    errors.append(f"Clearance ID {clearance_id} does not belong to this challan master.")
                    continue

                # Call model method for validation and update
                success, msg = clearance_obj.update_clear_quantity(new_qty, original_qty)
                if not success:
                    errors.append(f"Clearance ID {clearance_id}: {msg}")
                else:
                    updated_count += 1
        
        if errors:
            for error_msg in errors:
                messages.error(request, error_msg)
            return self.get(request, pk) # Re-render the table to show errors
        else:
            if updated_count > 0:
                messages.success(request, f"{updated_count} Challan Clearances updated successfully.")
            else:
                messages.info(request, "No changes were made to Challan Clearances.")
            
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshChallanClearanceList'}
            )

# Placeholder for cancelling, which redirects. This can be handled directly in the template
# with an anchor tag or hx-redirect.
class RedirectToChallanEditView(View):
    def get(self, request):
        messages.info(request, "Operation cancelled.")
        return redirect(reverse_lazy('customerchallan_list')) # Assuming a list view exists
```

#### 4.4 Templates (in `inventory/templates/inventory/customerchallan/`)

Templates will be concise, focusing on rendering the data and HTMX attributes for dynamic interactions. They extend `core/base.html` and use partials for reusable components like the DataTables.

**`edit_dashboard.html`** (Main page - replaces `.aspx` file's body content, `Content7`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'customerChallan' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Edit Customer Challan: <span class="text-indigo-600">{{ master_challan.cc_no }}</span></h2>
        <a href="{% url 'customerchallan_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Challan List
        </a>
    </div>

    <!-- Tab Container -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex" aria-label="Tabs">
                <button 
                    class="{% if activeTab == 'customerChallan' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} 
                           w-1/2 py-4 px-1 text-center border-b-2 font-medium text-sm transition-colors duration-200"
                    @click="activeTab = 'customerChallan'"
                    hx-get="{% url 'customerchallan_detail_table' master_pk=master_challan.pk %}"
                    hx-target="#tabContent"
                    hx-swap="innerHTML"
                    hx-trigger="click, load from:body"
                    hx-indicator="#tabContentLoading"
                    role="tab" aria-controls="panel-customerChallan" :aria-selected="activeTab === 'customerChallan'">
                    Customer Challan
                </button>
                <button 
                    class="{% if activeTab == 'clearChallan' %}border-indigo-500 text-indigo-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} 
                           w-1/2 py-4 px-1 text-center border-b-2 font-medium text-sm transition-colors duration-200"
                    @click="activeTab = 'clearChallan'"
                    hx-get="{% url 'challanclearance_table' master_pk=master_challan.pk %}"
                    hx-target="#tabContent"
                    hx-swap="innerHTML"
                    hx-trigger="click"
                    hx-indicator="#tabContentLoading"
                    role="tab" aria-controls="panel-clearChallan" :aria-selected="activeTab === 'clearChallan'">
                    Clear Challan
                </button>
            </nav>
        </div>

        <div id="tabContentLoading" class="htmx-indicator text-center p-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading tab content...</p>
        </div>

        <div id="tabContent" class="p-4">
            <!-- Tab content will be loaded here via HTMX -->
            <p class="text-gray-500">Select a tab to view details.</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is initialized on the div with x-data.
    // HTMX loads content into #tabContent.
    // The dataTables initialization should be in the partial templates.
    // Ensure jQuery is loaded for DataTables. (Assumed from base.html)
    
    // Global event listener for HTMX afterSwap to re-initialize DataTables
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tabContent') {
            // Re-initialize DataTable for the newly loaded table
            // Check for both table IDs as either might be loaded
            ['customerChallanDetailTable', 'challanClearanceTable'].forEach(tableId => {
                const tableElement = document.getElementById(tableId);
                if (tableElement && !$.fn.DataTable.isDataTable(`#${tableId}`)) {
                    $(tableElement).DataTable({
                        "pageLength": 10,
                        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        "responsive": true
                    });
                }
            });
        }
    });

    // Handle messages after HTMX requests
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tabContent' && event.detail.xhr.getResponseHeader('HX-Trigger')) {
            const hxTrigger = JSON.parse(event.detail.xhr.getResponseHeader('HX-Trigger'));
            // Check if messages were sent (assuming a 'showMessages' event or similar)
            if (hxTrigger.showMessages) {
                // Logic to display messages (e.g., using Alpine.js for a toast component)
                // This would involve dispatching a custom event that an Alpine.js component listens to
                // Example: window.dispatchEvent(new CustomEvent('show-toast', { detail: hxTrigger.showMessages }));
                console.log("Messages received:", hxTrigger.showMessages);
            }
        }
    });
</script>
{% endblock %}
```

**`_customerchallandetail_table.html`** (Partial for the first tab)

```html
<div x-data="{ 
    items: {}, // Stores the state of each row {id: {isChecked: false, qty: 0, originalQty: 0}}
    init() {
        // Initialize items from the passed context data
        {% for item_data in detail_forms %}
            this.items[{{ item_data.detail_obj.id }}] = { 
                isChecked: false, // Default to unchecked
                qty: {{ item_data.detail_obj.challan_qty|floatformat:3 }},
                originalQty: {{ item_data.detail_obj.challan_qty|floatformat:3 }}
            };
        {% endfor %}
    },
    toggleCheck(id) {
        this.items[id].isChecked = !this.items[id].isChecked;
    },
    // Function to collect data for HTMX submission
    collectData() {
        let formData = new FormData();
        Object.keys(this.items).forEach(id => {
            if (this.items[id].isChecked) {
                formData.append(`challan_detail_id_${id}`, id);
                formData.append(`challan_qty_${id}`, this.items[id].qty);
            }
        });
        return formData;
    }
}">
    <div class="overflow-x-auto">
        <table id="customerChallanDetailTable" class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CC No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
                </tr>
            </thead>
            <tbody>
                {% for item_data in detail_forms %}
                <tr x-data="{ 
                    isChecked: $store.items[{{ item_data.detail_obj.id }}].isChecked,
                    qty: $store.items[{{ item_data.detail_obj.id }}].qty,
                    initialCheckState: false // For Alpine.js to track initial state
                }" x-init="
                    $watch('isChecked', value => { $store.items[{{ item_data.detail_obj.id }}].isChecked = value; });
                    $watch('qty', value => { $store.items[{{ item_data.detail_obj.id }}].qty = value; });
                    // Sync Alpine state with global store and vice versa
                    isChecked = $store.items[{{ item_data.detail_obj.id }}].isChecked;
                    qty = $store.items[{{ item_data.detail_obj.id }}].qty;
                ">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <input type="checkbox" x-model="isChecked" @change="toggleCheck({{ item_data.detail_obj.id }})" 
                               class="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out">
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.detail_obj.master.cc_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.detail_obj.master.wo_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.detail_obj.master.cc_date|date:"Y-m-d" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.detail_obj.item_code }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item_data.detail_obj.manf_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.detail_obj.symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">
                        <input 
                            type="number" 
                            step="0.001" 
                            min="0.001" 
                            x-bind:disabled="!isChecked"
                            x-model.number="qty"
                            name="challan_qty_{{ item_data.detail_obj.id }}" 
                            class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3 text-right"
                            placeholder="Qty"
                        >
                        <input type="hidden" name="challan_detail_id_{{ item_data.detail_obj.id }}" value="{{ item_data.detail_obj.id }}">
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="mt-6 flex justify-center space-x-4">
        <button 
            type="button" 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200"
            hx-post="{% url 'customerchallan_detail_table' master_pk=master_pk %}"
            hx-trigger="click"
            hx-swap="none"
            hx-encoding="multipart/form-data"
            hx-vals="js:collectData()"
            hx-indicator="#loadingIndicator"
            onclick="return confirm('Are you sure you want to update the selected Challan Quantities?');"
        >
            Update
        </button>
        <a href="{% url 'customerchallan_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200">
            Cancel
        </a>
    </div>
    <div id="loadingIndicator" class="htmx-indicator text-center mt-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Processing update...</p>
    </div>
</div>

<script>
    // DataTable initialization for this partial
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#customerChallanDetailTable')) {
            $('#customerChallanDetailTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true
            });
        }
    });

    // Alpine.js store for global state management
    document.addEventListener('alpine:init', () => {
        Alpine.store('items', {}); // Initialize a global store if not already done
    });
</script>
```

**`_challanclearance_table.html`** (Partial for the second tab)

```html
<div x-data="{ 
    items: {}, // Stores the state of each row {id: {isChecked: false, qty: 0, originalQty: 0, challanQty: 0, currentTotalCleared: 0}}
    init() {
        {% for item_data in clearance_forms %}
            this.items[{{ item_data.clearance_obj.id }}] = { 
                isChecked: false,
                qty: {{ item_data.clearance_obj.clear_qty|floatformat:3 }},
                originalQty: {{ item_data.original_clear_qty|floatformat:3 }}, // Used for validation
                challanQty: {{ item_data.clearance_obj.detail.challan_qty|floatformat:3 }},
                currentTotalCleared: {{ item_data.current_total_cleared|floatformat:3 }} // Current total cleared for this detail
            };
        {% endfor %}
    },
    toggleCheck(id) {
        this.items[id].isChecked = !this.items[id].isChecked;
    },
    collectData() {
        let formData = new FormData();
        Object.keys(this.items).forEach(id => {
            if (this.items[id].isChecked) {
                formData.append(`clearance_id_${id}`, id);
                formData.append(`clear_qty_${id}`, this.items[id].qty);
                formData.append(`original_clear_qty_${id}`, this.items[id].originalQty); // Pass original qty for server-side validation
            }
        });
        return formData;
    }
}">
    <div class="overflow-x-auto">
        <table id="challanClearanceTable" class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CC No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Cleared Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clear Qty</th>
                </tr>
            </thead>
            <tbody>
                {% for item_data in clearance_forms %}
                <tr x-data="{ 
                    isChecked: $store.items[{{ item_data.clearance_obj.id }}].isChecked,
                    qty: $store.items[{{ item_data.clearance_obj.id }}].qty,
                    originalQty: $store.items[{{ item_data.clearance_obj.id }}].originalQty, // For model validation
                    challanQty: $store.items[{{ item_data.clearance_obj.id }}].challanQty,
                    currentTotalCleared: $store.items[{{ item_data.clearance_obj.id }}].currentTotalCleared // For display and Alpine validation check
                }" x-init="
                    $watch('isChecked', value => { $store.items[{{ item_data.clearance_obj.id }}].isChecked = value; });
                    $watch('qty', value => { $store.items[{{ item_data.clearance_obj.id }}].qty = value; });
                    isChecked = $store.items[{{ item_data.clearance_obj.id }}].isChecked;
                    qty = $store.items[{{ item_data.clearance_obj.id }}].qty;
                ">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <input type="checkbox" x-model="isChecked" @change="toggleCheck({{ item_data.clearance_obj.id }})"
                               class="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out">
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.clearance_obj.detail.master.wo_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.clearance_obj.detail.master.cc_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.clearance_obj.sys_date|date:"Y-m-d" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.clearance_obj.detail.item_code }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ item_data.clearance_obj.detail.manf_desc }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item_data.clearance_obj.detail.symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item_data.clearance_obj.detail.challan_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item_data.current_total_cleared|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">
                        <input 
                            type="number" 
                            step="0.001" 
                            min="0.001" 
                            x-bind:disabled="!isChecked"
                            x-model.number="qty"
                            name="clear_qty_{{ item_data.clearance_obj.id }}" 
                            class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3 text-right"
                            placeholder="Qty"
                        >
                        <input type="hidden" name="clearance_id_{{ item_data.clearance_obj.id }}" value="{{ item_data.clearance_obj.id }}">
                        <input type="hidden" name="original_clear_qty_{{ item_data.clearance_obj.id }}" value="{{ item_data.original_clear_qty|floatformat:3 }}">
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="11" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <div class="mt-6 flex justify-center space-x-4">
        <button 
            type="button" 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200"
            hx-post="{% url 'challanclearance_table' master_pk=master_pk %}"
            hx-trigger="click"
            hx-swap="none"
            hx-encoding="multipart/form-data"
            hx-vals="js:collectData()"
            hx-indicator="#loadingIndicator"
            onclick="return confirm('Are you sure you want to update the selected Cleared Quantities?');"
        >
            Update
        </button>
        <a href="{% url 'customerchallan_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200">
            Cancel
        </a>
    </div>
    <div id="loadingIndicator" class="htmx-indicator text-center mt-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Processing update...</p>
    </div>
</div>

<script>
    // DataTable initialization for this partial
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#challanClearanceTable')) {
            $('#challanClearanceTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true
            });
        }
    });

    // Alpine.js store for global state management
    document.addEventListener('alpine:init', () => {
        Alpine.store('items', {}); // Initialize a global store if not already done
    });
</script>
```

#### 4.5 URLs (in `inventory/urls.py`)

This file defines the URL patterns for your Django application, mapping URLs to views.

```python
from django.urls import path
from .views import (
    CustomerChallanEditDashboardView,
    CustomerChallanDetailTablePartialView,
    ChallanClearanceTablePartialView,
    RedirectToChallanEditView, # For cancel button redirect
)

urlpatterns = [
    # Main dashboard for editing a specific customer challan (master)
    path('customerchallans/<int:pk>/edit/', CustomerChallanEditDashboardView.as_view(), name='customerchallan_edit_dashboard'),
    
    # HTMX endpoints for loading/updating tab contents
    path('customerchallans/<int:pk>/edit/details/', CustomerChallanDetailTablePartialView.as_view(), name='customerchallan_detail_table'),
    path('customerchallans/<int:pk>/edit/clearances/', ChallanClearanceTablePartialView.as_view(), name='challanclearance_table'),

    # Assuming a general customer challan list view to redirect to on cancel
    path('customerchallans/cancel/', RedirectToChallanEditView.as_view(), name='customerchallan_list_cancel'), 
    # You would typically have a main customerchallan_list URL, e.g.,
    # path('customerchallans/', CustomerChallanListView.as_view(), name='customerchallan_list'),
]
```

#### 4.6 Tests (in `inventory/tests.py`)

Comprehensive tests ensure the reliability of your models and views, covering both unit logic and integration flows.

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from datetime import date, time
from .models import CustomerChallanMaster, CustomerChallanDetail, ChallanClearance, PRDetail

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.master = CustomerChallanMaster.objects.create(
            id=1, cc_no='CC001', cc_date=date(2023, 1, 1), wo_no='WO001', customer_id=101,
            sys_date=date(2023, 1, 1), sys_time=time(10, 0, 0), session_id='testuser', comp_id=1, fin_year_id=2023
        )
        cls.pr_detail = PRDetail.objects.create(
            id=1, qty=Decimal('100.000'), customer_id=101
        )
        cls.detail1 = CustomerChallanDetail.objects.create(
            id=101, master=cls.master, pr_detail=cls.pr_detail, item_code='ITEM001',
            manf_desc='Description for Item 1', symbol='PCS', challan_qty=Decimal('50.000')
        )
        cls.detail2 = CustomerChallanDetail.objects.create(
            id=102, master=cls.master, pr_detail=cls.pr_detail, item_code='ITEM002',
            manf_desc='Description for Item 2', symbol='KGS', challan_qty=Decimal('30.000')
        )
        cls.clearance1 = ChallanClearance.objects.create(
            id=201, detail=cls.detail1, clear_qty=Decimal('10.000'),
            sys_date=date(2023, 1, 5), sys_time=time(11, 0, 0), comp_id=1, fin_year_id=2023, session_id='testuser'
        )
        cls.clearance2 = ChallanClearance.objects.create(
            id=202, detail=cls.detail1, clear_qty=Decimal('5.000'), # Another clearance for detail1
            sys_date=date(2023, 1, 6), sys_time=time(12, 0, 0), comp_id=1, fin_year_id=2023, session_id='testuser'
        )
        
    def test_customer_challan_master_creation(self):
        self.assertEqual(self.master.cc_no, 'CC001')
        self.assertEqual(self.master.customer_id, 101)

    def test_pr_detail_creation(self):
        self.assertEqual(self.pr_detail.qty, Decimal('100.000'))

    def test_customer_challan_detail_creation(self):
        self.assertEqual(self.detail1.challan_qty, Decimal('50.000'))
        self.assertEqual(self.detail1.master, self.master)
        self.assertEqual(self.detail1.pr_detail, self.pr_detail)

    def test_challan_clearance_creation(self):
        self.assertEqual(self.clearance1.clear_qty, Decimal('10.000'))
        self.assertEqual(self.clearance1.detail, self.detail1)

    def test_customer_challan_detail_update_challan_quantity_success(self):
        # Update detail1's quantity
        success, msg = self.detail1.update_challan_quantity(Decimal('60.000'))
        self.assertTrue(success)
        self.assertEqual(msg, "Customer Challan Detail updated successfully.")
        self.detail1.refresh_from_db()
        self.assertEqual(self.detail1.challan_qty, Decimal('60.000'))
        self.master.refresh_from_db()
        self.assertIsNotNone(self.master.sys_date) # Check master metadata update

    def test_customer_challan_detail_update_challan_quantity_invalid_input(self):
        # Test with invalid quantity
        success, msg = self.detail1.update_challan_quantity('abc')
        self.assertFalse(success)
        self.assertEqual(msg, "Invalid quantity format.")

        success, msg = self.detail1.update_challan_quantity(Decimal('0.000'))
        self.assertFalse(success)
        self.assertEqual(msg, "Quantity must be greater than zero.")

    def test_customer_challan_detail_get_total_challaned_qty_from_pr(self):
        # self.detail1 (50) and self.detail2 (30) both use self.pr_detail (100)
        # Total challaned from PR should be 50+30 = 80
        total_challaned = CustomerChallanDetail.objects.filter(pr_detail=self.pr_detail).aggregate(total=models.Sum('challan_qty'))['total']
        self.assertEqual(total_challaned, Decimal('80.000'))
        
        # Test the method on an individual detail to get other challans
        # For detail1 (50), other challaned is 30 (from detail2)
        self.assertEqual(self.detail1.get_total_challaned_qty_from_pr(), Decimal('30.000'))
        # For detail2 (30), other challaned is 50 (from detail1)
        self.assertEqual(self.detail2.get_total_challaned_qty_from_pr(), Decimal('50.000'))

    def test_customer_challan_detail_get_remaining_pr_qty(self):
        # PR Qty = 100, Total Challaned = 80 (50 from detail1, 30 from detail2)
        # Remaining should be 100 - 80 = 20
        self.assertEqual(self.detail1.get_remaining_pr_qty(), Decimal('20.000'))
        self.assertEqual(self.detail2.get_remaining_pr_qty(), Decimal('20.000'))

    def test_challan_clearance_get_total_cleared_qty_for_detail(self):
        # detail1 has two clearances: 10 (clearance1) and 5 (clearance2)
        # When calculating for clearance1, it should sum others, so 5
        self.assertEqual(self.clearance1.get_total_cleared_qty_for_detail(), Decimal('5.000'))
        # When calculating for clearance2, it should sum others, so 10
        self.assertEqual(self.clearance2.get_total_cleared_qty_for_detail(), Decimal('10.000'))

    def test_challan_clearance_update_clear_quantity_success(self):
        # detail1.challan_qty = 50.000
        # clearance1 (10), clearance2 (5). Total existing for detail1 = 15.000
        # Update clearance1 from 10 to 20. (original_clear_qty_for_validation = 10)
        # Proposed total for detail1 = (5 from clearance2) + (20 new from clearance1) = 25.000
        # 25.000 <= 50.000, so valid.
        
        success, msg = self.clearance1.update_clear_quantity(Decimal('20.000'), self.clearance1.clear_qty)
        self.assertTrue(success)
        self.assertEqual(msg, "Challan Clearance updated successfully.")
        self.clearance1.refresh_from_db()
        self.assertEqual(self.clearance1.clear_qty, Decimal('20.000'))

    def test_challan_clearance_update_clear_quantity_invalid_input(self):
        # Test with invalid quantity
        success, msg = self.clearance1.update_clear_quantity('xyz', Decimal('10.000'))
        self.assertFalse(success)
        self.assertEqual(msg, "Invalid quantity format.")

        success, msg = self.clearance1.update_clear_quantity(Decimal('0.000'), Decimal('10.000'))
        self.assertFalse(success)
        self.assertEqual(msg, "Cleared quantity must be greater than zero.")

    def test_challan_clearance_update_clear_quantity_exceeds_challan_qty(self):
        # detail1.challan_qty = 50.000
        # Current clearances for detail1: 10 + 5 = 15
        # Try to update clearance1 (original 10) to 50.
        # Proposed total = (5 from clearance2) + (50 new from clearance1) = 55.000
        # 55.000 > 50.000, so invalid.
        
        success, msg = self.clearance1.update_clear_quantity(Decimal('50.000'), self.clearance1.clear_qty)
        self.assertFalse(success)
        self.assertIn("exceeds Challan Quantity", msg)
        self.assertEqual(self.clearance1.clear_qty, Decimal('10.000')) # Quantity should not have changed

class ViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master = CustomerChallanMaster.objects.create(
            id=1, cc_no='CC001', cc_date=date(2023, 1, 1), wo_no='WO001', customer_id=101,
            sys_date=date(2023, 1, 1), sys_time=time(10, 0, 0), session_id='testuser', comp_id=1, fin_year_id=2023
        )
        cls.pr_detail = PRDetail.objects.create(
            id=1, qty=Decimal('100.000'), customer_id=101
        )
        cls.detail1 = CustomerChallanDetail.objects.create(
            id=101, master=cls.master, pr_detail=cls.pr_detail, item_code='ITEM001',
            manf_desc='Description for Item 1', symbol='PCS', challan_qty=Decimal('50.000')
        )
        cls.detail2 = CustomerChallanDetail.objects.create(
            id=102, master=cls.master, pr_detail=cls.pr_detail, item_code='ITEM002',
            manf_desc='Description for Item 2', symbol='KGS', challan_qty=Decimal('30.000')
        )
        cls.clearance1 = ChallanClearance.objects.create(
            id=201, detail=cls.detail1, clear_qty=Decimal('10.000'),
            sys_date=date(2023, 1, 5), sys_time=time(11, 0, 0), comp_id=1, fin_year_id=2023, session_id='testuser'
        )

    def setUp(self):
        self.client = Client()

    def test_customer_challan_edit_dashboard_view(self):
        response = self.client.get(reverse('customerchallan_edit_dashboard', args=[self.master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/edit_dashboard.html')
        self.assertContains(response, 'Edit Customer Challan: CC001') # Check for master data

    def test_customer_challan_detail_table_partial_view_get(self):
        response = self.client.get(reverse('customerchallan_detail_table', args=[self.master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/_customerchallandetail_table.html')
        self.assertContains(response, 'ITEM001') # Check for detail data
        self.assertContains(response, 'ITEM002')

    def test_customer_challan_detail_table_partial_view_post_success(self):
        new_qty_detail1 = Decimal('55.000')
        new_qty_detail2 = Decimal('35.000')
        post_data = {
            f'challan_detail_id_{self.detail1.id}': self.detail1.id,
            f'challan_qty_{self.detail1.id}': str(new_qty_detail1),
            f'challan_detail_id_{self.detail2.id}': self.detail2.id,
            f'challan_qty_{self.detail2.id}': str(new_qty_detail2),
        }
        
        response = self.client.post(
            reverse('customerchallan_detail_table', args=[self.master.pk]),
            post_data,
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        
        self.assertEqual(response.status_code, 204) # No Content expected for successful HTMX update
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerChallanDetailList', response.headers['HX-Trigger'])

        self.detail1.refresh_from_db()
        self.detail2.refresh_from_db()
        self.assertEqual(self.detail1.challan_qty, new_qty_detail1)
        self.assertEqual(self.detail2.challan_qty, new_qty_detail2)
        
        # Verify messages are added to session (though not directly returned by 204)
        messages = list(response.context['messages']) if response.context else []
        self.assertGreater(len(messages), 0)
        self.assertEqual(str(messages[0]), "2 Customer Challan Details updated successfully.")

    def test_customer_challan_detail_table_partial_view_post_validation_failure(self):
        post_data = {
            f'challan_detail_id_{self.detail1.id}': self.detail1.id,
            f'challan_qty_{self.detail1.id}': '0.000', # Invalid quantity
        }
        
        response = self.client.post(
            reverse('customerchallan_detail_table', args=[self.master.pk]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200) # Should re-render with errors
        self.assertTemplateUsed(response, 'inventory/customerchallan/_customerchallandetail_table.html')
        messages = list(response.context['messages'])
        self.assertIn("Quantity must be greater than zero.", str(messages[0]))
        self.detail1.refresh_from_db()
        self.assertEqual(self.detail1.challan_qty, Decimal('50.000')) # Quantity should not have changed

    def test_challan_clearance_table_partial_view_get(self):
        response = self.client.get(reverse('challanclearance_table', args=[self.master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan/_challanclearance_table.html')
        self.assertContains(response, str(self.clearance1.clear_qty)) # Check for clearance data

    def test_challan_clearance_table_partial_view_post_success(self):
        # Update clearance1 (original 10) to 20
        # Detail1.challan_qty = 50, other clearances for detail1 = 0 (only 1 clearance is made initially)
        # Proposed total = 20 <= 50, so valid
        
        new_clear_qty = Decimal('20.000')
        post_data = {
            f'clearance_id_{self.clearance1.id}': self.clearance1.id,
            f'clear_qty_{self.clearance1.id}': str(new_clear_qty),
            f'original_clear_qty_{self.clearance1.id}': str(self.clearance1.clear_qty), # Crucial for model validation
        }
        
        response = self.client.post(
            reverse('challanclearance_table', args=[self.master.pk]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshChallanClearanceList', response.headers['HX-Trigger'])

        self.clearance1.refresh_from_db()
        self.assertEqual(self.clearance1.clear_qty, new_clear_qty)
        
        messages = list(response.context['messages']) if response.context else []
        self.assertGreater(len(messages), 0)
        self.assertEqual(str(messages[0]), "1 Challan Clearances updated successfully.")

    def test_challan_clearance_table_partial_view_post_exceeds_challan_qty(self):
        # Update clearance1 (original 10) to 60.
        # Detail1.challan_qty = 50.000
        # Proposed total = 60 > 50, so invalid.
        
        new_clear_qty = Decimal('60.000')
        post_data = {
            f'clearance_id_{self.clearance1.id}': self.clearance1.id,
            f'clear_qty_{self.clearance1.id}': str(new_clear_qty),
            f'original_clear_qty_{self.clearance1.id}': str(self.clearance1.clear_qty),
        }
        
        response = self.client.post(
            reverse('challanclearance_table', args=[self.master.pk]),
            post_data,
            HTTP_HX_REQUEST='true'
        )
        
        self.assertEqual(response.status_code, 200) # Should re-render with errors
        self.assertTemplateUsed(response, 'inventory/customerchallan/_challanclearance_table.html')
        messages = list(response.context['messages'])
        self.assertIn("exceeds Challan Quantity", str(messages[0]))
        self.clearance1.refresh_from_db()
        self.assertEqual(self.clearance1.clear_qty, Decimal('10.000')) # Should not have changed

    def test_redirect_to_challan_edit_view(self):
        # Test cancel redirect
        response = self.client.get(reverse('customerchallan_list_cancel'))
        self.assertEqual(response.status_code, 302)
        # Verify redirect URL (assuming 'customerchallan_list' is the target)
        self.assertRedirects(response, reverse('customerchallan_list'))
        # Check for message on redirect
        messages = list(response.wsgi_request._messages)
        self.assertIn("Operation cancelled.", str(messages[0]))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **Tab Navigation:** HTMX `hx-get`, `hx-target`, `hx-swap`, `hx-trigger` attributes are used on tab buttons to dynamically load content into the `#tabContent` div without a full page reload.
-   **DataTables:** Initialized with jQuery in the partial templates' `$(document).ready` blocks. A global `htmx:afterSwap` listener re-initializes DataTables when new content is loaded into `#tabContent`, ensuring correct behavior after HTMX updates.
-   **Dynamic Input Control (Checkboxes):** Alpine.js `x-data`, `x-model`, `x-bind:disabled` are used to control the `txtqty` (quantity input) fields based on the state of the associated checkbox. When a checkbox is checked, the `qty` input is enabled.
-   **Form Submission:**
    -   The "Update" buttons use `hx-post`, `hx-trigger`, `hx-swap="none"`, `hx-encoding="multipart/form-data"`.
    -   Alpine.js `collectData()` method gathers only the checked rows' `id` and `qty` values, formatting them for submission as `formData`. This is a crucial part of simulating the ASP.NET GridView's behavior of submitting only selected rows.
    -   `hx-indicator` provides visual feedback during AJAX requests.
    -   `HX-Trigger` headers in successful responses (HTTP 204 No Content) are used to signal the client to refresh the appropriate table, ensuring the UI reflects the latest data without a full page reload.
-   **Messages/Alerts:** Django's `messages` framework is used. Although HTMX 204 responses don't carry HTML content, the `HX-Trigger` header can be used to pass a custom event to an Alpine.js component that displays success/error toasts. For simplicity in this generated code, I've shown `messages.success` and `messages.error` within the views, and noted how a front-end Alpine.js component could consume these via an `HX-Trigger` or by re-rendering the whole message section.

This comprehensive plan, with detailed code examples and explanations, provides a clear roadmap for your organization's transition from ASP.NET to a modern Django application, focusing on automation, best practices, and an enhanced user experience.