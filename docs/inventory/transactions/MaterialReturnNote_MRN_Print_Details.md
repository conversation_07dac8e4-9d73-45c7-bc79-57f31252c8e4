## ASP.NET to Django Conversion Script: Material Return Note Print Details

This modernization plan outlines the transition of your existing ASP.NET Material Return Note (MRN) Print Details functionality to a robust, modern Django application. Our approach focuses on leveraging Django's "fat model, thin view" paradigm, combined with HTMX and Alpine.js for dynamic front-end interactions and DataTables for enhanced data presentation. We prioritize automated conversion strategies to minimize manual effort and reduce potential errors.

The original ASP.NET page primarily displays a report generated from multiple database tables. In Django, this will be reimagined as a dedicated report view that dynamically fetches and presents consolidated data, replacing the proprietary Crystal Reports viewer with standard, print-friendly HTML. Additionally, to fully demonstrate the capabilities for similar modules, we will outline standard CRUD (Create, Read, Update, Delete) operations for the core `MaterialReturnNote` entity, which aligns with common enterprise application patterns.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`inventory` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several tables to compile the Material Return Note report. The data model for Django will reflect these existing tables.

**Identified Tables and Key Columns:**

*   **`tblInv_MaterialReturn_Master`**:
    *   `Id` (Primary Key, `int`)
    *   `MRNNo` (`string`)
    *   `CompId` (`int`)
    *   `SysDate` (`datetime`)
    *   `SessionId` (`int`)
*   **`tblInv_MaterialReturn_Details`**:
    *   `Id` (Primary Key, `int`)
    *   `MRNNo` (`string`)
    *   `MId` (`int` - foreign key to `tblInv_MaterialReturn_Master.Id`)
    *   `ItemId` (`int` - foreign key to `tblDG_Item_Master.Id`)
    *   `DeptId` (`int` - foreign key to `BusinessGroup.Id` or a special value)
    *   `WONo` (`string`)
    *   `RetQty` (`double`)
    *   `Remarks` (`string`)
    *   `CompId` (`int`)
*   **`tblDG_Item_Master`**:
    *   `Id` (Primary Key, `int`)
    *   `ItemCode` (`string`)
    *   `ManfDesc` (`string`)
    *   `UOMBasic` (`int` - foreign key to `Unit_Master.Id`)
    *   `CompId` (`int`)
*   **`Unit_Master`**:
    *   `Id` (Primary Key, `int`)
    *   `Symbol` (`string`)
*   **`BusinessGroup`**:
    *   `Id` (Primary Key, `int`)
    *   `Symbol` (`string`)
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (Primary Key, `int`)
    *   `Title` (`string`)
    *   `EmployeeName` (`string`)
    *   `CompId` (`int`)

### Step 2: Identify Backend Functionality

The ASP.NET page is a **read-only report display**. It involves complex data retrieval and aggregation from multiple tables.

*   **Read:** The core functionality is to fetch master MRN details, associated line items, item descriptions, units of measure, department/work order information, and employee details (for "Generated By"). This aggregation of data is crucial for the report.
*   **No Create, Update, Delete:** This specific ASP.NET page does not perform any CRUD operations directly on the MRN data itself, only displays a report. However, as per instructions, we will provide standard CRUD views for the `MaterialReturnNoteMaster` entity for a complete example.
*   **Redirection:** The `btnCancel_Click` simply redirects the user to a previous page.

### Step 3: Infer UI Components

The primary UI component is `CrystalReportViewer`, which renders a pre-designed report (`MRN_Print.rpt`).
In Django, this will be replaced by:

*   A **Django Template** (`materialreturnnote/print_detail.html`) that dynamically renders the aggregated data into a structured HTML document. This HTML will be designed to be print-friendly.
*   A simple **HTML Button** or link for the "Cancel" action, leveraging HTMX for navigation or a standard Django redirect.

For the generic CRUD views (as per template instructions), we will use:

*   **DataTables** for listing all Material Return Notes, providing search, sort, and pagination.
*   **HTMX** for dynamic loading of forms (add/edit) into modals and for handling delete confirmations, ensuring a smooth user experience without full page reloads.
*   **Alpine.js** to manage the modal's open/close state.
*   **Tailwind CSS** for modern styling.

---

### Step 4: Generate Django Code

We will create a Django app named `inventory` to house this functionality.

#### 4.1 Models (`inventory/models.py`)

We map the identified ASP.NET database tables to Django models, ensuring `managed = False` to use existing database schemas. We also add a custom manager for `MaterialReturnNoteMaster` to encapsulate the complex report data retrieval logic, adhering to the "fat model" principle.

```python
from django.db import models
from datetime import datetime

# Helper functions that mimic ASP.NET's `fun` class
# In a real system, these would interact with a `Company` model and `Employee` model etc.
class SystemUtility:
    @staticmethod
    def get_company_address(company_id):
        # Placeholder: In a real system, query a Company model/table
        # For this example, returning a dummy address
        if company_id == 1:
            return "123 Main St, Anytown, State 12345"
        return "Unknown Company Address"

    @staticmethod
    def format_date_dmy(date_obj):
        # Mimics fun.FromDateDMY - formats date as DD-MM-YYYY
        if isinstance(date_obj, datetime):
            return date_obj.strftime("%d-%m-%Y")
        return ""
    
    @staticmethod
    def get_item_code_part_no(company_id, item_id):
        # Placeholder: In a real system, query ItemMaster and derive code
        # For this example, we'll try to get it directly from ItemMaster object in report data
        return ItemMaster.objects.filter(pk=item_id, compid=company_id).values_list('itemcode', flat=True).first() or "N/A"

    @staticmethod
    def get_employee_name(company_id, employee_id):
        # Placeholder: In a real system, query OfficeStaff
        employee = OfficeStaff.objects.filter(pk=employee_id, compid=company_id).first()
        if employee:
            return f"{employee.title}. {employee.employeename}"
        return "N/A"

class MaterialReturnNoteManager(models.Manager):
    def get_mrn_report_data(self, mrn_master_id, company_id):
        """
        Retrieves all necessary data for the MRN print report.
        This method encapsulates the complex joins and lookups from the ASP.NET code-behind.
        """
        try:
            mrn_master = self.select_related('gen_by').filter(pk=mrn_master_id, compid=company_id).first()
            if not mrn_master:
                return None

            details = MaterialReturnNoteDetail.objects.filter(mid=mrn_master.id, compid=company_id) \
                                                    .select_related('itemid', 'deptid').order_by('id')

            report_details = []
            for detail in details:
                item_code = SystemUtility.get_item_code_part_no(detail.compid, detail.itemid_id)
                item_desc = detail.itemid.manfdesc if detail.itemid else "N/A"
                uom = detail.itemid.uom_basic.symbol if detail.itemid and detail.itemid.uom_basic else "N/A"

                dept_symbol = "NA"
                won_no = detail.wono

                if detail.deptid_id == 1: # Assuming '1' means 'NA' for department and WO is applicable
                    dept_symbol = "NA"
                    won_no = detail.wono
                elif detail.deptid:
                    dept_symbol = detail.deptid.symbol
                    won_no = "NA" # Assuming WO is 'NA' if department is present

                report_details.append({
                    'id': detail.id,
                    'mrn_no': detail.mrnno,
                    'item_code': item_code,
                    'purch_desc': item_desc,
                    'uom': uom,
                    'dept_symbol': dept_symbol,
                    'wo_no': won_no,
                    'ret_qty': f"{detail.retqty:.3f}", # Format to N3
                    'remarks': detail.remarks,
                })

            mrn_date = SystemUtility.format_date_dmy(mrn_master.sysdate)
            generated_by = SystemUtility.get_employee_name(mrn_master.compid, mrn_master.sessionid)
            company_address = SystemUtility.get_company_address(mrn_master.compid)

            report_data = {
                'mrn_master': {
                    'mrn_no': mrn_master.mrnno,
                    'mrn_date': mrn_date,
                    'generated_by': generated_by,
                    'company_address': company_address,
                    'id': mrn_master.id,
                    'comp_id': mrn_master.compid,
                },
                'mrn_details': report_details,
            }
            return report_data
        except Exception as e:
            # Log the exception for debugging
            print(f"Error fetching MRN report data: {e}")
            return None

class MaterialReturnNote(models.Model): # Corresponds to tblInv_MaterialReturn_Master
    # Field names converted to Pythonic snake_case, db_column specifies original name
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrnno = models.CharField(db_column='MRNNo', max_length=50) # Assuming max length
    compid = models.IntegerField(db_column='CompId')
    sysdate = models.DateTimeField(db_column='SysDate')
    sessionid = models.IntegerField(db_column='SessionId') # This is EmpId from tblHR_OfficeStaff

    objects = MaterialReturnNoteManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Note'
        verbose_name_plural = 'Material Return Notes'

    def __str__(self):
        return f"MRN No: {self.mrnno} (ID: {self.id})"

    # Example of a business logic method
    def get_total_returned_qty(self):
        return self.mrn_details.aggregate(total_qty=models.Sum('retqty'))['total_qty'] or 0.0

class MaterialReturnNoteDetail(models.Model): # Corresponds to tblInv_MaterialReturn_Details
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrnno = models.CharField(db_column='MRNNo', max_length=50)
    # MId is a FK to MaterialReturnNote (tblInv_MaterialReturn_Master)
    mid = models.ForeignKey(MaterialReturnNote, on_delete=models.DO_NOTHING, db_column='MId', related_name='mrn_details')
    # ItemId is a FK to ItemMaster (tblDG_Item_Master)
    itemid = models.ForeignKey('ItemMaster', on_delete=models.DO_NOTHING, db_column='ItemId')
    # DeptId is a FK to BusinessGroup
    deptid = models.ForeignKey('BusinessGroup', on_delete=models.DO_NOTHING, db_column='DeptId', null=True, blank=True)
    wono = models.CharField(db_column='WONo', max_length=100, null=True, blank=True) # Assuming max length
    retqty = models.FloatField(db_column='RetQty')
    remarks = models.CharField(db_column='Remarks', max_length=250, null=True, blank=True) # Assuming max length
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Details'
        verbose_name = 'Material Return Note Detail'
        verbose_name_plural = 'Material Return Note Details'

    def __str__(self):
        return f"MRN Detail ID: {self.id} for MRN: {self.mrnno}"

class ItemMaster(models.Model): # Corresponds to tblDG_Item_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=50)
    manfdesc = models.CharField(db_column='ManfDesc', max_length=250)
    # UOMBasic is a FK to UnitMaster
    uom_basic = models.ForeignKey('UnitMaster', on_delete=models.DO_NOTHING, db_column='UOMBasic')
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.itemcode

class UnitMaster(models.Model): # Corresponds to Unit_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=20)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class BusinessGroup(models.Model): # Corresponds to BusinessGroup (for DeptId)
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup' # Adjust if the actual table name is different
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class OfficeStaff(models.Model): # Corresponds to tblHR_OfficeStaff
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=20)
    employeename = models.CharField(db_column='EmployeeName', max_length=100)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}. {self.employeename}"

```

#### 4.2 Forms (`inventory/forms.py`)

A `ModelForm` for the `MaterialReturnNote` master record, allowing creation and updates.

```python
from django import forms
from .models import MaterialReturnNote

class MaterialReturnNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialReturnNote
        fields = ['mrnno', 'sysdate', 'sessionid', 'compid']
        widgets = {
            'mrnno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sysdate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sessionid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_mrnno(self):
        mrnno = self.cleaned_data['mrnno']
        # Example validation: ensure MRNNo is unique for new records
        if MaterialReturnNote.objects.filter(mrnno=mrnno).exists() and not self.instance.pk:
            raise forms.ValidationError("This Material Return Note number already exists.")
        return mrnno
```

#### 4.3 Views (`inventory/views.py`)

Implementation of CRUD operations for `MaterialReturnNote` (master records) using Class-Based Views. Additionally, a dedicated `MaterialReturnNotePrintDetailView` to replicate the original ASP.NET page's reporting functionality.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import MaterialReturnNote, MaterialReturnNoteManager
from .forms import MaterialReturnNoteForm

# Standard CRUD Views for MaterialReturnNote (Master records)
class MaterialReturnNoteListView(ListView):
    model = MaterialReturnNote
    template_name = 'inventory/materialreturnnote/list.html'
    context_object_name = 'materialreturnnotes'

    def get_queryset(self):
        # Example: Filter by company ID from session, similar to ASP.NET
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        return MaterialReturnNote.objects.filter(compid=comp_id).order_by('-sysdate', '-id')

class MaterialReturnNoteTablePartialView(MaterialReturnNoteListView):
    template_name = 'inventory/materialreturnnote/_materialreturnnote_table.html'

class MaterialReturnNoteCreateView(CreateView):
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'inventory/materialreturnnote/_materialreturnnote_form.html'
    success_url = reverse_lazy('materialreturnnote_list')

    def form_valid(self, form):
        # Set default values if not provided by form (e.g., CompId, SessionId from session)
        form.instance.compid = self.request.session.get('compid', 1) # Default to 1
        form.instance.sessionid = self.request.session.get('empid', 1) # Default to 1 (Logged-in User ID)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnNoteList'
                }
            )
        return response

class MaterialReturnNoteUpdateView(UpdateView):
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'inventory/materialreturnnote/_materialreturnnote_form.html'
    success_url = reverse_lazy('materialreturnnote_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnNoteList'
                }
            )
        return response

class MaterialReturnNoteDeleteView(DeleteView):
    model = MaterialReturnNote
    template_name = 'inventory/materialreturnnote/confirm_delete.html'
    success_url = reverse_lazy('materialreturnnote_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Return Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnNoteList'
                }
            )
        return response

# Dedicated View for MRN Print Details (replicates original ASP.NET page)
class MaterialReturnNotePrintDetailView(TemplateView):
    template_name = 'inventory/materialreturnnote/print_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mrn_master_id = self.kwargs.get('pk')
        # Simulate CompId from session, as in ASP.NET
        company_id = self.request.session.get('compid', 1) 

        report_data = MaterialReturnNote.objects.get_mrn_report_data(mrn_master_id, company_id)

        if not report_data:
            raise Http404("Material Return Note not found or data error.")
        
        context['report_data'] = report_data
        return context

```

#### 4.4 Templates

**`inventory/materialreturnnote/list.html`** (Main list page for Material Return Notes)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Return Notes</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'materialreturnnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Material Return Note
        </button>
    </div>
    
    <div id="materialreturnnoteTable-container"
         hx-trigger="load, refreshMaterialReturnNoteList from:body"
         hx-get="{% url 'materialreturnnote_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**`inventory/materialreturnnote/_materialreturnnote_table.html`** (Partial for DataTables)

```html
<table id="materialreturnnoteTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRN No.</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in materialreturnnotes %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.mrnno }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.sysdate|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.compid }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a href="{% url 'materialreturnnote_print_detail' obj.pk %}" 
                   class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                   target="_blank">
                    Print
                </a>
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'materialreturnnote_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'materialreturnnote_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500">No Material Return Notes found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#materialreturnnoteTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**`inventory/materialreturnnote/_materialreturnnote_form.html`** (Partial for Add/Edit Form)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Return Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`inventory/materialreturnnote/confirm_delete.html`** (Partial for Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete Material Return Note "<strong>{{ object.mrnno }}</strong>"? This action cannot be undone.</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'materialreturnnote_delete' object.pk %}" 
            hx-swap="none" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

**`inventory/materialreturnnote/print_detail.html`** (Report Print View - replicates original ASP.NET page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 bg-white shadow-lg rounded-lg print:shadow-none print:rounded-none">
    <div class="flex justify-between items-center mb-6 print:hidden">
        <h2 class="text-2xl font-bold">Material Return Note [MRN] - Print Preview</h2>
        <div class="flex space-x-2">
            <button onclick="window.print()" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                Print
            </button>
            <a href="{% url 'materialreturnnote_list' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Cancel
            </a>
        </div>
    </div>

    <div class="report-content p-6 border border-gray-200">
        <h1 class="text-center text-xl font-bold mb-4">MATERIAL RETURN NOTE</h1>
        <p class="text-center text-sm mb-6">{{ report_data.mrn_master.company_address }}</p>

        <div class="grid grid-cols-2 gap-4 text-sm mb-6">
            <div>
                <strong>MRN No:</strong> {{ report_data.mrn_master.mrn_no }}<br>
                <strong>Date:</strong> {{ report_data.mrn_master.mrn_date }}
            </div>
            <div class="text-right">
                <strong>Generated By:</strong> {{ report_data.mrn_master.generated_by }}
            </div>
        </div>

        <h3 class="text-lg font-semibold mb-4">Returned Items:</h3>
        <table class="min-w-full border-collapse border border-gray-300 text-sm">
            <thead>
                <tr class="bg-gray-100">
                    <th class="border border-gray-300 px-3 py-2 text-left">SN</th>
                    <th class="border border-gray-300 px-3 py-2 text-left">Item Code</th>
                    <th class="border border-gray-300 px-3 py-2 text-left">Description</th>
                    <th class="border border-gray-300 px-3 py-2 text-left">UOM</th>
                    <th class="border border-gray-300 px-3 py-2 text-left">Dept. / Work Order</th>
                    <th class="border border-gray-300 px-3 py-2 text-right">Returned Qty</th>
                    <th class="border border-gray-300 px-3 py-2 text-left">Remarks</th>
                </tr>
            </thead>
            <tbody>
                {% for detail in report_data.mrn_details %}
                <tr>
                    <td class="border border-gray-300 px-3 py-2">{{ forloop.counter }}</td>
                    <td class="border border-gray-300 px-3 py-2">{{ detail.item_code }}</td>
                    <td class="border border-gray-300 px-3 py-2">{{ detail.purch_desc }}</td>
                    <td class="border border-gray-300 px-3 py-2">{{ detail.uom }}</td>
                    <td class="border border-gray-300 px-3 py-2">
                        {% if detail.dept_symbol != "NA" %}{{ detail.dept_symbol }}{% endif %}
                        {% if detail.wo_no != "NA" %}{{ detail.wo_no }}{% endif %}
                    </td>
                    <td class="border border-gray-300 px-3 py-2 text-right">{{ detail.ret_qty }}</td>
                    <td class="border border-gray-300 px-3 py-2">{{ detail.remarks }}</td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="border border-gray-300 px-3 py-2 text-center text-gray-500">No items found for this MRN.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <div class="mt-8 flex justify-between text-sm">
            <div>
                <p>____________________</p>
                <p>Prepared By</p>
            </div>
            <div>
                <p>____________________</p>
                <p>Approved By</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<style>
    @media print {
        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        .container {
            max-width: none !important;
            padding: 0 !important;
            margin: 0 !important;
            box-shadow: none !important;
            border-radius: 0 !important;
        }
        .print\:hidden {
            display: none !important;
        }
        table {
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ccc !important;
        }
    }
</style>
{% endblock %}
```

#### 4.5 URLs (`inventory/urls.py`)

URL patterns for the Material Return Note module.

```python
from django.urls import path
from .views import (
    MaterialReturnNoteListView,
    MaterialReturnNoteTablePartialView,
    MaterialReturnNoteCreateView,
    MaterialReturnNoteUpdateView,
    MaterialReturnNoteDeleteView,
    MaterialReturnNotePrintDetailView,
)

urlpatterns = [
    # URLs for Material Return Note (Master) CRUD operations
    path('materialreturnnote/', MaterialReturnNoteListView.as_view(), name='materialreturnnote_list'),
    path('materialreturnnote/table/', MaterialReturnNoteTablePartialView.as_view(), name='materialreturnnote_table'),
    path('materialreturnnote/add/', MaterialReturnNoteCreateView.as_view(), name='materialreturnnote_add'),
    path('materialreturnnote/edit/<int:pk>/', MaterialReturnNoteUpdateView.as_view(), name='materialreturnnote_edit'),
    path('materialreturnnote/delete/<int:pk>/', MaterialReturnNoteDeleteView.as_view(), name='materialreturnnote_delete'),
    
    # URL for the specific MRN Print Details page (replicating the original ASP.NET page)
    path('materialreturnnote/print/<int:pk>/', MaterialReturnNotePrintDetailView.as_view(), name='materialreturnnote_print_detail'),
]

```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for models, managers, and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from .models import (
    MaterialReturnNote,
    MaterialReturnNoteDetail,
    ItemMaster,
    UnitMaster,
    BusinessGroup,
    OfficeStaff,
    SystemUtility
)

class MaterialReturnNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data for relationships
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_master_1 = ItemMaster.objects.create(id=101, itemcode='ITEM001', manfdesc='Test Item 1', uom_basic=cls.unit_ea, compid=1)
        cls.dept_na = BusinessGroup.objects.create(id=1, symbol='NA') # Assuming ID 1 for 'NA' department logic
        cls.dept_prod = BusinessGroup.objects.create(id=2, symbol='Production')
        cls.staff_gen = OfficeStaff.objects.create(empid=1, title='Mr', employeename='John Doe', compid=1)
        
        # Create MaterialReturnNote instance
        cls.mrn1 = MaterialReturnNote.objects.create(
            id=1,
            mrnno='MRN/2023/001',
            compid=1,
            sysdate=datetime(2023, 1, 15),
            sessionid=cls.staff_gen.empid
        )
        MaterialReturnNoteDetail.objects.create(
            id=1, mrnno='MRN/2023/001', mid=cls.mrn1, itemid=cls.item_master_1,
            deptid=cls.dept_na, wono='WO-001', retqty=10.5, remarks='Damaged', compid=1
        )
        MaterialReturnNoteDetail.objects.create(
            id=2, mrnno='MRN/2023/001', mid=cls.mrn1, itemid=cls.item_master_1,
            deptid=cls.dept_prod, wono=None, retqty=5.0, remarks='Excess', compid=1
        )
        
        cls.mrn2 = MaterialReturnNote.objects.create(
            id=2,
            mrnno='MRN/2023/002',
            compid=1,
            sysdate=datetime(2023, 1, 16),
            sessionid=cls.staff_gen.empid
        )

    def test_material_return_note_creation(self):
        mrn = MaterialReturnNote.objects.get(id=1)
        self.assertEqual(mrn.mrnno, 'MRN/2023/001')
        self.assertEqual(mrn.compid, 1)
        self.assertEqual(mrn.sysdate, datetime(2023, 1, 15))
        
    def test_material_return_note_detail_creation(self):
        detail = MaterialReturnNoteDetail.objects.get(id=1)
        self.assertEqual(detail.mid, self.mrn1)
        self.assertEqual(detail.itemid, self.item_master_1)
        self.assertEqual(detail.retqty, 10.5)

    def test_mrn_total_returned_qty_method(self):
        mrn = MaterialReturnNote.objects.get(id=1)
        self.assertEqual(mrn.get_total_returned_qty(), 15.5)

    def test_material_return_note_manager_report_data(self):
        # Set session for view logic
        self.client.session['compid'] = 1 
        self.client.session['empid'] = 1

        report_data = MaterialReturnNote.objects.get_mrn_report_data(self.mrn1.id, 1)
        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['mrn_master']['mrn_no'], 'MRN/2023/001')
        self.assertEqual(report_data['mrn_master']['mrn_date'], '15-01-2023')
        self.assertEqual(report_data['mrn_master']['generated_by'], 'Mr. John Doe')
        self.assertIn("123 Main St", report_data['mrn_master']['company_address'])
        self.assertEqual(len(report_data['mrn_details']), 2)

        detail1 = report_data['mrn_details'][0]
        self.assertEqual(detail1['item_code'], 'ITEM001')
        self.assertEqual(detail1['uom'], 'EA')
        self.assertEqual(detail1['dept_symbol'], 'NA')
        self.assertEqual(detail1['wo_no'], 'WO-001')
        self.assertEqual(detail1['ret_qty'], '10.500') # Check formatting

        detail2 = report_data['mrn_details'][1]
        self.assertEqual(detail2['dept_symbol'], 'Production')
        self.assertEqual(detail2['wo_no'], 'NA')


class MaterialReturnNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary dependent data
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_master_1 = ItemMaster.objects.create(id=101, itemcode='ITEM001', manfdesc='Test Item 1', uom_basic=cls.unit_ea, compid=1)
        cls.dept_na = BusinessGroup.objects.create(id=1, symbol='NA')
        cls.dept_prod = BusinessGroup.objects.create(id=2, symbol='Production')
        cls.staff_gen = OfficeStaff.objects.create(empid=1, title='Mr', employeename='John Doe', compid=1)

        # Create test data for all tests
        cls.mrn1 = MaterialReturnNote.objects.create(
            id=1, mrnno='MRN/TEST/001', compid=1, sysdate=datetime(2023, 1, 1), sessionid=cls.staff_gen.empid
        )
        MaterialReturnNoteDetail.objects.create(
            id=1, mrnno='MRN/TEST/001', mid=cls.mrn1, itemid=cls.item_master_1,
            deptid=cls.dept_na, wono='WO-ABC', retqty=5.0, remarks='Initial item', compid=1
        )
        
        cls.mrn2 = MaterialReturnNote.objects.create(
            id=2, mrnno='MRN/TEST/002', compid=1, sysdate=datetime(2023, 2, 1), sessionid=cls.staff_gen.empid
        )
        
        cls.mrn3 = MaterialReturnNote.objects.create(
            id=3, mrnno='MRN/TEST/003', compid=2, sysdate=datetime(2023, 3, 1), sessionid=cls.staff_gen.empid
        )

    def setUp(self):
        self.client = Client()
        # Set session data to simulate logged-in user and company context
        session = self.client.session
        session['compid'] = 1
        session['empid'] = 1
        session.save()
    
    def test_list_view(self):
        response = self.client.get(reverse('materialreturnnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/list.html')
        self.assertIn('materialreturnnotes', response.context)
        self.assertEqual(response.context['materialreturnnotes'].count(), 2) # Only for compid 1

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialreturnnote_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_materialreturnnote_table.html')
        self.assertIn('materialreturnnotes', response.context)
        self.assertContains(response, 'MRN/TEST/001')
        self.assertNotContains(response, 'MRN/TEST/003') # Should not be in default compid

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialreturnnote_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_materialreturnnote_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_success(self):
        data = {
            'mrnno': 'MRN/NEW/004',
            'sysdate': '2023-04-01',
            'sessionid': self.staff_gen.empid, # This will be overridden by view logic if not provided
            'compid': 1 # This will be overridden by view logic if not provided
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialreturnnote_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertTrue(MaterialReturnNote.objects.filter(mrnno='MRN/NEW/004', compid=1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialReturnNoteList')
        
    def test_create_view_post_invalid(self):
        data = {
            'mrnno': 'MRN/TEST/001', # Duplicate MRNNo
            'sysdate': '2023-01-01',
            'sessionid': self.staff_gen.empid,
            'compid': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialreturnnote_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_materialreturnnote_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('This Material Return Note number already exists.', str(response.content))


    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialreturnnote_edit', args=[self.mrn1.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_materialreturnnote_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.mrn1)
        
    def test_update_view_post(self):
        data = {
            'mrnno': 'MRN/TEST/001-UPDATED',
            'sysdate': '2023-01-01',
            'sessionid': self.staff_gen.empid,
            'compid': 1
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialreturnnote_edit', args=[self.mrn1.id]), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.mrn1.refresh_from_db()
        self.assertEqual(self.mrn1.mrnno, 'MRN/TEST/001-UPDATED')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialReturnNoteList')

    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialreturnnote_delete', args=[self.mrn2.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.mrn2)
        
    def test_delete_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialreturnnote_delete', args=[self.mrn2.id]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertFalse(MaterialReturnNote.objects.filter(id=self.mrn2.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialReturnNoteList')

    def test_print_detail_view(self):
        response = self.client.get(reverse('materialreturnnote_print_detail', args=[self.mrn1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/print_detail.html')
        self.assertIn('report_data', response.context)
        self.assertEqual(response.context['report_data']['mrn_master']['mrn_no'], 'MRN/TEST/001')
        self.assertContains(response, 'MRN/TEST/001')
        self.assertContains(response, 'Test Item 1')
        self.assertContains(response, 'WO-ABC')
        self.assertContains(response, '10.500')

    def test_print_detail_view_not_found(self):
        response = self.client.get(reverse('materialreturnnote_print_detail', args=[99999]))
        self.assertEqual(response.status_code, 404)

```

---

### Step 5: HTMX and Alpine.js Integration

*   **List View (`materialreturnnote/list.html`):**
    *   The entire DataTables content is loaded via `hx-get` to `{% url 'materialreturnnote_table' %}` using `hx-trigger="load, refreshMaterialReturnNoteList from:body"`. This means the table is loaded on page load and refreshed whenever a custom `refreshMaterialReturnNoteList` event is triggered (e.g., after successful form submission).
    *   "Add New" button uses `hx-get` to fetch the form into `#modalContent` and triggers Alpine.js (`_ = "on click add .is-active to #modal"`) to open the modal.
    *   "Edit" and "Delete" buttons within the table use similar `hx-get` attributes to fetch forms/confirmation dialogs into the modal.

*   **Form Partials (`_materialreturnnote_form.html`, `confirm_delete.html`):**
    *   These templates are designed to be loaded dynamically into a modal. They do not extend `base.html`.
    *   Forms use `hx-post="{{ request.path }}" hx-swap="none"` for submission. `hx-swap="none"` is used because the view will return a `204 No Content` response with an `HX-Trigger` header to signal the client to refresh the list, rather than swapping content directly.
    *   "Cancel" buttons in modals use `_ = "on click remove .is-active from #modal"` to close the modal directly via Alpine.js.
    *   Delete confirmation has `hx-post` for the deletion itself.

*   **DataTables:** The `_materialreturnnote_table.html` partial includes a `$(document).ready` script to initialize DataTables on the loaded table. This ensures DataTables features like searching, sorting, and pagination work client-side.

*   **Print Detail View (`materialreturnnote/print_detail.html`):**
    *   This page is designed as a standalone print preview. It includes standard HTML/CSS for a report layout.
    *   A simple `window.print()` JavaScript call is attached to the "Print" button.
    *   CSS with `@media print` rules ensures the page looks correct when printed, hiding UI elements not relevant to the printout.

### Final Notes

This comprehensive plan transforms the legacy ASP.NET Crystal Report viewer into a modern Django solution. By encapsulating data retrieval logic in models/managers, utilizing Django's powerful ORM, and implementing dynamic UI with HTMX and Alpine.js, the application becomes more maintainable, scalable, and provides a superior user experience. The inclusion of comprehensive tests ensures high code quality and reliability. Remember to replace placeholder values like `CompId` and `EmpId` in session/view logic with your actual authentication and session management mechanisms.