This document outlines a strategic plan to modernize your legacy ASP.NET "Release WO for WIS" module into a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation, focusing on transforming your existing business logic and UI into modern Django patterns, ensuring a smooth transition with minimal manual effort.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`inventory` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

This conversion will result in a new Django application, let's call it `inventory`, containing the modernized functionality.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
Based on the provided ASP.NET code, the following database tables and their likely columns are identified:

*   **`SD_Cust_WorkOrder_Master`** (Main Work Order data)
    *   `Id` (Primary Key, Integer)
    *   `SysDate` (Date/DateTime)
    *   `WONo` (String, Work Order Number)
    *   `TaskProjectTitle` (String, Project Title)
    *   `ReleaseWIS` (Integer, stores 0 or 1, indicating release status)
    *   `CompId` (Integer, Company Identifier)
    *   `CloseOpen` (Integer, stores 0 or 1, indicating open/closed status, `0` for open)
    *   `CId` (Integer, Category Identifier, implicitly linked to `tblSD_WO_Category`)

*   **`tblInv_WORelease_WIS`** (Work Order Release details - history of releases for WIS)
    *   `Id` (Primary Key, Integer)
    *   `CompId` (Integer, Company Identifier)
    *   `FinYearId` (Integer, Financial Year Identifier)
    *   `WONo` (String, Work Order Number, implicitly linked to `SD_Cust_WorkOrder_Master.WONo`)
    *   `ReleaseSysDate` (Date/DateTime, Date of release)
    *   `ReleaseSysTime` (String, Time of release)
    *   `ReleaseBy` (String, Employee ID, implicitly linked to `tblHR_OfficeStaff.EmpId`)

*   **`tblSD_WO_Category`** (Work Order Category lookup)
    *   `CId` (Primary Key, Integer, Category Identifier)
    *   `Symbol` (String, Category Symbol)
    *   `CName` (String, Category Name)
    *   `CompId` (Integer, Company Identifier)

*   **`tblHR_OfficeStaff`** (Office Staff/Employee lookup)
    *   `EmpId` (String, Employee Identifier, assumed primary key or unique identifier)
    *   `Title` (String, e.g., 'Mr.', 'Ms.')
    *   `EmployeeName` (String, Employee's full name)

### Step 2: Identify Backend Functionality

**Task:** Determine the core business operations performed by the ASP.NET code.

**Instructions:**
The module primarily handles the listing, searching, and managing the release status of Work Orders for the "WIS" (Work In Progress/System) system.

*   **Read (List and Filter):** The page displays a list of open Work Orders (`CloseOpen='0'`) from `SD_Cust_WorkOrder_Master`. This list can be filtered by `WO Category` (`DrpWOType`) and `WO No` (`TxtWONo`). For each Work Order, it retrieves its latest release details (date, time, who released it) and the total count of times it has been released for WIS, by joining or sub-querying `tblInv_WORelease_WIS` and `tblHR_OfficeStaff`.
*   **Update (Release/Stop Work Order):**
    *   **Release:** When the "Release" button is clicked for a Work Order, its `ReleaseWIS` status in `SD_Cust_WorkOrder_Master` is updated to `1` (released), and a new record is inserted into `tblInv_WORelease_WIS` to log the release event with current date, time, and the user who initiated it.
    *   **Stop:** When the "Stop" button is clicked, its `ReleaseWIS` status in `SD_Cust_WorkOrder_Master` is updated back to `0` (stopped/unreleased).
*   **Navigation (View Release Details):** Clicking on the "Rel Count" link redirects the user to a separate "ReleaseWIS\_Details.aspx" page, passing the Work Order Number and Company ID.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactive roles.

**Instructions:**
The user interface is composed of the following key elements:

*   **Filtering and Search Bar:**
    *   A dropdown list for `WO Category` (`DrpWOType`), which automatically triggers a search when its value changes.
    *   A text input field for `WO No` (`TxtWONo`).
    *   A "Search" button (`Button1`) to initiate filtering based on the `WO No` and currently selected `WO Category`.
*   **Work Order List (Data Table):**
    *   A `GridView` (`GridView2`) displays work order information in a paginated table.
    *   It includes columns for sequential number (SN), WO No, Date, Project Title, Release Count, Release Date, Release Time, and Release By.
    *   Each row contains action buttons: "Release" (visible if not released) and "Stop" (visible if released).
    *   The "Rel Count" is a clickable link for viewing details.

### Step 4: Generate Django Code

This section details the transformation of the identified components and logic into a modern Django application.

#### 4.1 Models (`inventory/models.py`)

We will define Django models for each identified database table. Crucially, we use `managed = False` and `db_table` to connect directly to your existing database, and `db_column` to map Django field names to your actual column names where they differ. Business logic related to fetching release information and toggling status will be added as methods to the `WorkOrderMaster` model.

```python
# inventory/models.py

from django.db import models
from django.db.models import Max, Count
from django.utils import timezone

class WoCategory(models.Model):
    """
    Represents the Work Order Categories from tblSD_WO_Category.
    """
    id = models.IntegerField(db_column='CId', primary_key=True)  # Mapped to CId
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    c_name = models.CharField(db_column='CName', max_length=100, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.c_name}" if self.symbol else self.c_name


class OfficeStaff(models.Model):
    """
    Represents office staff from tblHR_OfficeStaff.
    ReleaseBy in tblInv_WORelease_WIS refers to EmpId here.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be alphanumeric
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=200, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name or ''}".strip()


class WorkOrderMaster(models.Model):
    """
    Represents work orders from SD_Cust_WorkOrder_Master.
    Includes methods for business logic related to release status.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True, unique=True) # Assuming WONo is unique
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=500, blank=True, null=True)
    release_wis = models.BooleanField(db_column='ReleaseWIS', default=False) # 0 or 1 in DB
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    close_open = models.BooleanField(db_column='CloseOpen', default=True) # 0 for open, 1 for close

    # Foreign key to WoCategory based on CId
    category = models.ForeignKey(WoCategory, models.DO_NOTHING, db_column='CId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return f"{self.wo_no} - {self.task_project_title}"

    # Business Logic for Fat Models

    def get_latest_release_info(self):
        """
        Retrieves the latest release date, time, and releasing employee for this Work Order.
        Corresponds to the nested query in loadgrid.
        """
        latest_release = self.woreleasewis_set.order_by('-id').first()
        if latest_release:
            return {
                'date': latest_release.release_sys_date,
                'time': latest_release.release_sys_time,
                'by': latest_release.release_by_employee
            }
        return {'date': None, 'time': None, 'by': None}

    def get_release_count(self):
        """
        Calculates the total number of times this Work Order has been released.
        Corresponds to the 'counts' column in loadgrid.
        """
        return self.woreleasewis_set.count()

    def toggle_release_status(self, user_id, company_id, fin_year_id):
        """
        Toggles the ReleaseWIS status and logs the action in tblInv_WORelease_WIS.
        Corresponds to GridView2_RowCommand 'add' and 'stp' logic.
        """
        try:
            self.release_wis = not self.release_wis
            self.save(update_fields=['release_wis'])

            if self.release_wis:  # If just released (was false, now true)
                WoReleaseWis.objects.create(
                    comp_id=company_id,
                    fin_year_id=fin_year_id,
                    work_order_master=self, # Link to WorkOrderMaster object
                    wo_no_text=self.wo_no, # Store text WONo for historical data consistency with ASP.NET
                    release_sys_date=timezone.now().date(),
                    release_sys_time=timezone.now().strftime('%H:%M:%S'),
                    release_by_employee_id=user_id # Assuming user_id maps to EmpId
                )
            # If it was stopped, no new record is inserted in original ASP.NET code
            return True
        except Exception as e:
            # Log the error, maybe raise a custom exception
            print(f"Error toggling release status: {e}")
            return False


class WoReleaseWis(models.Model):
    """
    Represents Work Order Release details from tblInv_WORelease_WIS.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    
    # Use a ForeignKey to WorkOrderMaster for integrity
    work_order_master = models.ForeignKey(WorkOrderMaster, models.DO_NOTHING, db_column='WONo', 
                                          to_field='wo_no', blank=True, null=True) # Link via wo_no for existing data
    wo_no_text = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Keep original column for safety

    release_sys_date = models.DateField(db_column='ReleaseSysDate', blank=True, null=True)
    release_sys_time = models.CharField(db_column='ReleaseSysTime', max_length=50, blank=True, null=True)

    # Use a ForeignKey to OfficeStaff for integrity
    release_by_employee = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='ReleaseBy', 
                                            to_field='emp_id', blank=True, null=True) # Link via emp_id for existing data

    class Meta:
        managed = False
        db_table = 'tblInv_WORelease_WIS'
        verbose_name = 'WO Release WIS'
        verbose_name_plural = 'WO Release WIS'
        # Ordering by id desc to easily get the latest release if needed
        ordering = ['-id']
    
    def __str__(self):
        return f"Release for WO: {self.wo_no_text} on {self.release_sys_date}"

```

#### 4.2 Forms (`inventory/forms.py`)

We don't have a direct form for `WorkOrderMaster` creation/editing on this page. Instead, we need a simple form for the search filters.

```python
# inventory/forms.py

from django import forms
from .models import WoCategory

class WorkOrderFilterForm(forms.Form):
    """
    Form for filtering Work Orders by type and number.
    Corresponds to DrpWOType and TxtWONo.
    """
    # Choices for WO Type dropdown, including a default "WO Category" option
    wo_type = forms.ModelChoiceField(
        queryset=WoCategory.objects.all().order_by('c_name'),
        empty_label="WO Category", # Corresponds to "WO Category" item in ASP.NET dropdown
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': "{% url 'inventory:workordermaster_table' %}", # HTMX: submit on change
            'hx-target': '#workOrderMasterTable-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '.htmx-indicator',
        })
    )
    wo_no = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Work Order Number',
        })
    )

```

#### 4.3 Views (`inventory/views.py`)

Views will be thin, delegating complex data retrieval and business logic to the models. We will use Django's Class-Based Views (CBVs) and integrate HTMX for dynamic content updates.

```python
# inventory/views.py

from django.views.generic import ListView, View
from django.shortcuts import render, redirect
from django.urls import reverse
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.db.models import Q # For complex queries
from .models import WorkOrderMaster, WoCategory, WoReleaseWis, OfficeStaff
from .forms import WorkOrderFilterForm

# Dummy values for session variables - In a real app, these would come from
# the logged-in user's profile or session data.
# For demonstration purposes, replace with actual logic.
COMPANY_ID = 1 # Example company ID
FIN_YEAR_ID = 1 # Example financial year ID

class WorkOrderMasterListView(ListView):
    """
    Main view to display the Work Order list page.
    It renders the initial page with the filter form and a container for the HTMX-loaded table.
    Corresponds to the main ReleaseWIS.aspx page.
    """
    model = WorkOrderMaster
    template_name = 'inventory/workordermaster/list.html'
    context_object_name = 'work_order_masters'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize filter form
        context['filter_form'] = WorkOrderFilterForm(self.request.GET)
        return context

    def get_queryset(self):
        # This method is only called for the initial page load, not for HTMX table updates
        # The actual table data is loaded via WorkOrderMasterTablePartialView
        return WorkOrderMaster.objects.none() # Return empty queryset for initial load

class WorkOrderMasterTablePartialView(ListView):
    """
    HTMX-specific view to render only the DataTables portion of the page.
    Handles filtering, pagination, and sorting for DataTables.
    Corresponds to the loadgrid() function in ASP.NET.
    """
    model = WorkOrderMaster
    template_name = 'inventory/workordermaster/_workordermaster_table.html'
    context_object_name = 'work_order_masters'
    paginate_by = 20 # Matches ASP.NET PageSize

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by company and open status, as in ASP.NET
        queryset = queryset.filter(comp_id=COMPANY_ID, close_open=False) # close_open=0 in ASP.NET

        # Apply filters from request.GET (search box and dropdown)
        wo_no = self.request.GET.get('wo_no')
        wo_type_id = self.request.GET.get('wo_type')

        if wo_no:
            queryset = queryset.filter(wo_no__iexact=wo_no) # Case-insensitive exact match
        
        if wo_type_id and wo_type_id != 'WO Category': # 'WO Category' is the default option
            try:
                wo_type_id = int(wo_type_id)
                queryset = queryset.filter(category__id=wo_type_id)
            except ValueError:
                pass # Invalid WO Type ID, ignore filter

        # Order by WONo ASC as in ASP.NET
        queryset = queryset.order_by('wo_no')
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # DataTables expects all data upfront for client-side operations.
        # So we pass the full filtered queryset, not just the paginated slice.
        # Pagination will be handled by DataTables itself.
        context['work_order_masters_data'] = list(self.get_queryset())
        return context


class WorkOrderMasterReleaseToggleView(View):
    """
    Handles the 'Release' and 'Stop' actions via HTMX POST requests.
    Corresponds to GridView2_RowCommand 'add' and 'stp' logic.
    """
    def post(self, request, pk, *args, **kwargs):
        try:
            work_order = WorkOrderMaster.objects.get(id=pk)
        except WorkOrderMaster.DoesNotExist:
            raise Http404("Work Order not found.")

        # In a real application, request.user.id would be mapped to EmpId in OfficeStaff
        # For simplicity, using a placeholder.
        user_id = request.user.username if request.user.is_authenticated else 'UNKNOWN_USER' 
        
        success = work_order.toggle_release_status(user_id, COMPANY_ID, FIN_YEAR_ID)

        if success:
            action_status = "released" if work_order.release_wis else "stopped"
            messages.success(request, f"Work Order {work_order.wo_no} successfully {action_status}.")
            
            # HTMX response to trigger a refresh of the table
            return HttpResponse(
                status=204, # No content
                headers={'HX-Trigger': 'refreshWorkOrderList'}
            )
        else:
            messages.error(request, f"Failed to toggle release status for Work Order {work_order.wo_no}.")
            return HttpResponse(status=400) # Bad request

class WorkOrderReleaseDetailsRedirectView(View):
    """
    Handles the redirection to the 'ReleaseWIS_Details' page.
    Corresponds to GridView2_RowCommand 'Sel' logic.
    """
    def get(self, request, wo_no, *args, **kwargs):
        # In a real application, this would redirect to a Django view
        # that handles release details, possibly with a WorkOrderMaster object.
        # For now, we simulate the original ASP.NET redirect.
        # You'd replace this with:
        # return redirect(reverse('inventory:release_details', kwargs={'wo_no': wo_no, 'company_id': COMPANY_ID}))
        messages.info(request, f"Redirecting to details for WO: {wo_no} (This is a placeholder redirect).")
        return redirect(f"/some/path/to/release_details/?wn={wo_no}&cid={COMPANY_ID}")

```

#### 4.4 Templates (`inventory/templates/inventory/`)

These templates will be compact and adhere to DRY principles. The main list page will extend `core/base.html`, and the table itself will be an HTMX partial.

**`inventory/templates/inventory/workordermaster/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-3xl font-extrabold text-gray-800">Release WO for WIS</h2>
        <div class="flex flex-col md:flex-row items-stretch md:items-center space-y-3 md:space-y-0 md:space-x-4 w-full md:w-auto">
            <div class="w-full md:w-auto">
                {{ filter_form.wo_type }}
            </div>
            <div class="relative w-full md:w-auto">
                {{ filter_form.wo_no }}
                <span class="htmx-indicator absolute right-3 top-1/2 -translate-y-1/2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </span>
            </div>
            <button 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-6 rounded-lg shadow-md transition duration-200 ease-in-out"
                hx-get="{% url 'inventory:workordermaster_table' %}"
                hx-target="#workOrderMasterTable-container"
                hx-swap="innerHTML"
                hx-indicator=".htmx-indicator">
                Search
            </button>
        </div>
    </div>
    
    <div id="workOrderMasterTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'inventory:workordermaster_table' %}"
         hx-swap="innerHTML">
        <!-- Loading spinner for initial load and HTMX updates -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form (not used on this specific page based on ASP.NET analysis, but good practice to include for future CRUD) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me transition opacity ease-out duration-300 then remove @inert">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad add .is-active to #modal then add @inert to #modal wait 200ms remove @inert from #modalContent"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderList', () => ({
            // Add any Alpine.js state or methods specific to this list view if needed
        }));
    });

    // Event listener for messages, to remove them after a delay
    document.addEventListener('DOMContentLoaded', function() {
        const messages = document.querySelectorAll('.message-container');
        messages.forEach(function(message) {
            setTimeout(() => {
                message.remove();
            }, 5000); // Messages disappear after 5 seconds
        });
    });
</script>
{% endblock %}

```

**`inventory/templates/inventory/workordermaster/_workordermaster_table.html`**

```html
{# inventory/templates/inventory/workordermaster/_workordermaster_table.html #}
{# This partial template is loaded via HTMX #}

<div class="bg-white shadow-md rounded-lg overflow-hidden">
    {% if work_order_masters_data %}
    <table id="workOrderMasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rel Count</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rel Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rel Time</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Release By</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in work_order_masters_data %}
            {% with latest_release_info=obj.get_latest_release_info release_count=obj.get_release_count %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.wo_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.sys_date|date:"d/m/Y" }}</td>
                <td class="py-3 px-4 text-sm text-gray-900 min-w-48">{{ obj.task_project_title }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-center">
                    <button class="text-blue-600 hover:text-blue-900 font-medium"
                        hx-get="{% url 'inventory:workordermaster_release_details' wo_no=obj.wo_no %}"
                        hx-trigger="click">
                        {{ release_count }}
                    </button>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ latest_release_info.date|date:"d/m/Y"|default:"-" }}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ latest_release_info.time|default:"-" }}
                </td>
                <td class="py-3 px-4 text-sm text-gray-900 min-w-32">
                    {{ latest_release_info.by|default:"-" }}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                    {% if not obj.release_wis %}
                    <button 
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1.5 px-3 rounded text-xs transition duration-200 ease-in-out"
                        hx-post="{% url 'inventory:workordermaster_toggle_release' pk=obj.pk %}"
                        hx-confirm="Are you sure you want to RELEASE WO {{ obj.wo_no }}?"
                        hx-indicator=".htmx-indicator">
                        Release
                    </button>
                    {% else %}
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded text-xs transition duration-200 ease-in-out"
                        hx-post="{% url 'inventory:workordermaster_toggle_release' pk=obj.pk %}"
                        hx-confirm="Are you sure you want to STOP WO {{ obj.wo_no }}?"
                        hx-indicator=".htmx-indicator">
                        Stop
                    </button>
                    {% endif %}
                </td>
            </tr>
            {% endwith %}
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="py-10 text-center text-red-500 font-bold text-lg">
        No data found to display
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization issues
        if ($.fn.DataTable.isDataTable('#workOrderMasterTable')) {
            $('#workOrderMasterTable').DataTable().destroy();
        }
        $('#workOrderMasterTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>

```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for our views within the `inventory` app.

```python
# inventory/urls.py

from django.urls import path
from .views import WorkOrderMasterListView, WorkOrderMasterTablePartialView, \
                   WorkOrderMasterReleaseToggleView, WorkOrderReleaseDetailsRedirectView

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    # Main Work Order list page
    path('release-wis/', WorkOrderMasterListView.as_view(), name='workordermaster_list'),

    # HTMX endpoint for the table partial (includes filtering/pagination for DataTables)
    path('release-wis/table/', WorkOrderMasterTablePartialView.as_view(), name='workordermaster_table'),

    # HTMX endpoint for toggling Release/Stop status (POST request)
    path('release-wis/toggle/<int:pk>/', WorkOrderMasterReleaseToggleView.as_view(), name='workordermaster_toggle_release'),

    # Redirect for "Rel Count" link
    path('release-wis/details/<str:wo_no>/', WorkOrderReleaseDetailsRedirectView.as_view(), name='workordermaster_release_details'),
]

```
**Important:** You will need to include these URLs in your project's main `urls.py`:
```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('inventory/', include('inventory.urls')), # Include your new app's URLs
    # ... other project URLs
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests are essential to ensure the migrated functionality is correct and robust. This includes unit tests for models and integration tests for views, covering all interactions.

```python
# inventory/tests.py

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import time
from unittest.mock import patch # For mocking timezone.now() if needed

from .models import WoCategory, OfficeStaff, WorkOrderMaster, WoReleaseWis
from .views import COMPANY_ID, FIN_YEAR_ID # Access constants from views

class WoCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        WoCategory.objects.create(id=1, symbol='WO1', c_name='Category A', comp_id=COMPANY_ID)
        WoCategory.objects.create(id=2, symbol='WO2', c_name='Category B', comp_id=COMPANY_ID)

    def test_wo_category_creation(self):
        cat = WoCategory.objects.get(id=1)
        self.assertEqual(cat.symbol, 'WO1')
        self.assertEqual(cat.c_name, 'Category A')
        self.assertEqual(str(cat), 'WO1 - Category A')

    def test_wo_category_str_no_symbol(self):
        cat = WoCategory.objects.create(id=3, c_name='Category C', comp_id=COMPANY_ID)
        self.assertEqual(str(cat), 'Category C')


class OfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        OfficeStaff.objects.create(emp_id='EMP001', title='Mr.', employee_name='John Doe')
        OfficeStaff.objects.create(emp_id='EMP002', title='Ms.', employee_name='Jane Smith')

    def test_office_staff_creation(self):
        staff = OfficeStaff.objects.get(emp_id='EMP001')
        self.assertEqual(staff.employee_name, 'John Doe')
        self.assertEqual(str(staff), 'Mr. John Doe')

    def test_office_staff_str_no_title(self):
        staff = OfficeStaff.objects.create(emp_id='EMP003', employee_name='Alice')
        self.assertEqual(str(staff), 'Alice')


class WorkOrderMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category_a = WoCategory.objects.create(id=101, symbol='CAT_A', c_name='Category Alpha', comp_id=COMPANY_ID)
        cls.staff_a = OfficeStaff.objects.create(emp_id='USER1', title='Mr.', employee_name='User One')
        cls.staff_b = OfficeStaff.objects.create(emp_id='USER2', title='Ms.', employee_name='User Two')

        # Create Work Order Masters
        cls.wo1 = WorkOrderMaster.objects.create(
            id=1, sys_date=timezone.now().date(), wo_no='WO-001', task_project_title='Project Alpha', 
            release_wis=False, comp_id=COMPANY_ID, close_open=False, category=cls.category_a
        )
        cls.wo2 = WorkOrderMaster.objects.create(
            id=2, sys_date=timezone.now().date(), wo_no='WO-002', task_project_title='Project Beta', 
            release_wis=True, comp_id=COMPANY_ID, close_open=False, category=cls.category_a
        )
        cls.wo_closed = WorkOrderMaster.objects.create(
            id=3, sys_date=timezone.now().date(), wo_no='WO-003', task_project_title='Project Closed', 
            release_wis=False, comp_id=COMPANY_ID, close_open=True, category=cls.category_a
        )

        # Create WoReleaseWis records for WO-001
        WoReleaseWis.objects.create(
            id=1001, comp_id=COMPANY_ID, fin_year_id=FIN_YEAR_ID, work_order_master=cls.wo1, wo_no_text='WO-001',
            release_sys_date=timezone.now().date() - timezone.timedelta(days=5),
            release_sys_time='09:00:00', release_by_employee=cls.staff_a
        )
        WoReleaseWis.objects.create(
            id=1002, comp_id=COMPANY_ID, fin_year_id=FIN_YEAR_ID, work_order_master=cls.wo1, wo_no_text='WO-001',
            release_sys_date=timezone.now().date() - timezone.timedelta(days=2),
            release_sys_time='10:30:00', release_by_employee=cls.staff_b
        )
        # Create WoReleaseWis records for WO-002 (already released)
        WoReleaseWis.objects.create(
            id=1003, comp_id=COMPANY_ID, fin_year_id=FIN_YEAR_ID, work_order_master=cls.wo2, wo_no_text='WO-002',
            release_sys_date=timezone.now().date() - timezone.timedelta(days=1),
            release_sys_time='11:00:00', release_by_employee=cls.staff_a
        )

    def test_work_order_creation(self):
        self.assertEqual(self.wo1.wo_no, 'WO-001')
        self.assertFalse(self.wo1.release_wis)
        self.assertFalse(self.wo1.close_open)
        self.assertEqual(self.wo1.category, self.category_a)

    def test_get_latest_release_info(self):
        info_wo1 = self.wo1.get_latest_release_info()
        self.assertEqual(info_wo1['date'], timezone.now().date() - timezone.timedelta(days=2))
        self.assertEqual(info_wo1['time'], '10:30:00')
        self.assertEqual(info_wo1['by'], self.staff_b)

        info_wo2 = self.wo2.get_latest_release_info()
        self.assertEqual(info_wo2['date'], timezone.now().date() - timezone.timedelta(days=1))
        self.assertEqual(info_wo2['time'], '11:00:00')
        self.assertEqual(info_wo2['by'], self.staff_a)

        # Test for WO with no releases
        wo_no_releases = WorkOrderMaster.objects.create(
            id=4, sys_date=timezone.now().date(), wo_no='WO-004', task_project_title='Project Gamma', 
            release_wis=False, comp_id=COMPANY_ID, close_open=False, category=self.category_a
        )
        info_no_releases = wo_no_releases.get_latest_release_info()
        self.assertIsNone(info_no_releases['date'])
        self.assertIsNone(info_no_releases['time'])
        self.assertIsNone(info_no_releases['by'])

    def test_get_release_count(self):
        self.assertEqual(self.wo1.get_release_count(), 2)
        self.assertEqual(self.wo2.get_release_count(), 1)
        
        wo_no_releases = WorkOrderMaster.objects.get(id=4) # The one created in previous test
        self.assertEqual(wo_no_releases.get_release_count(), 0)

    @patch('django.utils.timezone.now')
    def test_toggle_release_status_release(self, mock_now):
        mock_now.return_value = timezone.datetime(2023, 10, 26, 14, 0, 0, tzinfo=timezone.utc)
        
        # Initially not released
        self.assertFalse(self.wo1.release_wis)
        initial_release_count = WoReleaseWis.objects.filter(work_order_master=self.wo1).count()

        # Toggle to release
        success = self.wo1.toggle_release_status('USER_TEST', COMPANY_ID, FIN_YEAR_ID)
        self.assertTrue(success)
        self.wo1.refresh_from_db()
        self.assertTrue(self.wo1.release_wis)
        self.assertEqual(WoReleaseWis.objects.filter(work_order_master=self.wo1).count(), initial_release_count + 1)
        
        latest_log = WoReleaseWis.objects.filter(work_order_master=self.wo1).order_by('-id').first()
        self.assertEqual(latest_log.release_by_employee_id, 'USER_TEST')
        self.assertEqual(latest_log.release_sys_date, timezone.datetime(2023, 10, 26).date())
        self.assertEqual(latest_log.release_sys_time, '14:00:00')

    @patch('django.utils.timezone.now')
    def test_toggle_release_status_stop(self, mock_now):
        mock_now.return_value = timezone.datetime(2023, 10, 27, 15, 0, 0, tzinfo=timezone.utc)

        # Initially released
        self.assertTrue(self.wo2.release_wis)
        initial_release_count = WoReleaseWis.objects.filter(work_order_master=self.wo2).count()

        # Toggle to stop
        success = self.wo2.toggle_release_status('USER_TEST_STOP', COMPANY_ID, FIN_YEAR_ID)
        self.assertTrue(success)
        self.wo2.refresh_from_db()
        self.assertFalse(self.wo2.release_wis)
        # Stopping should not add a new log entry, count remains same
        self.assertEqual(WoReleaseWis.objects.filter(work_order_master=self.wo2).count(), initial_release_count)


class WorkOrderMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.category_a = WoCategory.objects.create(id=101, symbol='CAT_A', c_name='Category Alpha', comp_id=COMPANY_ID)
        cls.category_b = WoCategory.objects.create(id=102, symbol='CAT_B', c_name='Category Beta', comp_id=COMPANY_ID)
        cls.staff_a = OfficeStaff.objects.create(emp_id='U001', title='Mr.', employee_name='Test User')

        cls.wo_open_unreleased = WorkOrderMaster.objects.create(
            id=10, sys_date=timezone.now().date(), wo_no='WO-UNREL-001', task_project_title='Project X',
            release_wis=False, comp_id=COMPANY_ID, close_open=False, category=cls.category_a
        )
        cls.wo_open_released = WorkOrderMaster.objects.create(
            id=11, sys_date=timezone.now().date(), wo_no='WO-REL-002', task_project_title='Project Y',
            release_wis=True, comp_id=COMPANY_ID, close_open=False, category=cls.category_b
        )
        WoReleaseWis.objects.create(
            id=2001, comp_id=COMPANY_ID, fin_year_id=FIN_YEAR_ID, work_order_master=cls.wo_open_released, wo_no_text='WO-REL-002',
            release_sys_date=timezone.now().date(), release_sys_time='10:00:00', release_by_employee=cls.staff_a
        )
        WoReleaseWis.objects.create(
            id=2002, comp_id=COMPANY_ID, fin_year_id=FIN_YEAR_ID, work_order_master=cls.wo_open_released, wo_no_text='WO-REL-002',
            release_sys_date=timezone.now().date(), release_sys_time='11:00:00', release_by_employee=cls.staff_a
        )
        cls.wo_closed = WorkOrderMaster.objects.create(
            id=12, sys_date=timezone.now().date(), wo_no='WO-CLOSED-003', task_project_title='Project Z',
            release_wis=False, comp_id=COMPANY_ID, close_open=True, category=cls.category_a
        )

    def setUp(self):
        self.client = Client()
        # Mock user authentication for views that use request.user
        self.client.login(username='testuser', password='password') # Assume a user 'testuser' exists and is logged in

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:workordermaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workordermaster/list.html')
        self.assertContains(response, 'Release WO for WIS')
        self.assertContains(response, '<div id="workOrderMasterTable-container"') # Check for HTMX container

    def test_table_partial_view_get_initial(self):
        response = self.client.get(reverse('inventory:workordermaster_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workordermaster/_workordermaster_table.html')
        self.assertContains(response, self.wo_open_unreleased.wo_no)
        self.assertContains(response, self.wo_open_released.wo_no)
        self.assertNotContains(response, self.wo_closed.wo_no) # Should not include closed WO
        self.assertContains(response, 'Release') # For WO-UNREL-001
        self.assertContains(response, 'Stop') # For WO-REL-002
        self.assertContains(response, 'id="workOrderMasterTable"') # Check for DataTables table

    def test_table_partial_view_get_filter_wo_no(self):
        response = self.client.get(reverse('inventory:workordermaster_table'), {'wo_no': 'WO-UNREL-001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workordermaster/_workordermaster_table.html')
        self.assertContains(response, self.wo_open_unreleased.wo_no)
        self.assertNotContains(response, self.wo_open_released.wo_no)
        self.assertContains(response, 'SN') # Check if table structure is present

    def test_table_partial_view_get_filter_wo_type(self):
        response = self.client.get(reverse('inventory:workordermaster_table'), {'wo_type': self.category_a.id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workordermaster/_workordermaster_table.html')
        self.assertContains(response, self.wo_open_unreleased.wo_no)
        self.assertNotContains(response, self.wo_open_released.wo_no) # WO-REL-002 is CAT_B

    def test_work_order_release_toggle_release(self):
        # Ensure it's unreleased first
        self.wo_open_unreleased.release_wis = False
        self.wo_open_unreleased.save()
        initial_release_count = WoReleaseWis.objects.filter(work_order_master=self.wo_open_unreleased).count()

        response = self.client.post(reverse('inventory:workordermaster_toggle_release', args=[self.wo_open_unreleased.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code for 'no content'
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        
        self.wo_open_unreleased.refresh_from_db()
        self.assertTrue(self.wo_open_unreleased.release_wis)
        self.assertEqual(WoReleaseWis.objects.filter(work_order_master=self.wo_open_unreleased).count(), initial_release_count + 1)
        # Check messages framework (HX-Trigger should be enough, but good to check)
        messages = list(response.context['messages']) if hasattr(response, 'context') else []
        self.assertTrue(any("released" in str(m) for m in messages))

    def test_work_order_release_toggle_stop(self):
        # Ensure it's released first
        self.wo_open_released.release_wis = True
        self.wo_open_released.save()
        initial_release_count = WoReleaseWis.objects.filter(work_order_master=self.wo_open_released).count()

        response = self.client.post(reverse('inventory:workordermaster_toggle_release', args=[self.wo_open_released.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        
        self.wo_open_released.refresh_from_db()
        self.assertFalse(self.wo_open_released.release_wis)
        self.assertEqual(WoReleaseWis.objects.filter(work_order_master=self.wo_open_released).count(), initial_release_count)
        messages = list(response.context['messages']) if hasattr(response, 'context') else []
        self.assertTrue(any("stopped" in str(m) for m in messages))

    def test_work_order_release_toggle_not_found(self):
        response = self.client.post(reverse('inventory:workordermaster_toggle_release', args=[9999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404)

    def test_work_order_release_details_redirect(self):
        response = self.client.get(reverse('inventory:workordermaster_release_details', args=['WO-REL-002']))
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertIn('/some/path/to/release_details/?wn=WO-REL-002', response.url)
        # Verify messages (if any were added before redirect)
        messages = list(self.client.session.get('_messages', []))
        self.assertTrue(any("Redirecting to details for WO: WO-REL-002" in str(m) for m in messages))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates and views are designed for seamless HTMX and Alpine.js integration:

*   **HTMX for Dynamic Updates:**
    *   The entire data table area (`#workOrderMasterTable-container`) is designed to be loaded and refreshed by HTMX.
    *   `hx-get` and `hx-target` are used on the "Search" button and `wo_type` dropdown to fetch the updated table partial.
    *   `hx-trigger="load, refreshWorkOrderList from:body"` on the container ensures the table loads on page visit and refreshes automatically when `refreshWorkOrderList` is triggered from any part of the body.
    *   `hx-post` attributes on the "Release" and "Stop" buttons send data to `WorkOrderMasterReleaseToggleView`.
    *   `HX-Trigger: refreshWorkOrderList` header is sent back by the toggle view on successful completion, instructing HTMX to re-fetch the table partial, thus updating the display.
    *   `hx-confirm` is added to action buttons for user confirmation, similar to JavaScript `confirm()` in ASP.NET.
    *   `hx-indicator` provides visual feedback during AJAX requests.
*   **Alpine.js for UI State:**
    *   Alpine.js is included in `base.html` (assumed). While not heavily used for complex UI state management in this simple list, it's available for client-side interactions like managing modal visibility or form states if needed (example modal structure included in `list.html`). The `_` (hyperscript) syntax can be used for simpler interactions.
*   **DataTables for List Views:**
    *   The `_workordermaster_table.html` partial explicitly includes the DataTables initialization script `$('#workOrderMasterTable').DataTable({...});`.
    *   This ensures that every time the table is loaded via HTMX, DataTables is re-initialized correctly, providing client-side searching, sorting, and pagination.
    *   CDN links for jQuery and DataTables (JS and CSS) should be present in `core/base.html` for this to work.
*   **No Full Page Reloads:** All filtering, searching, and action toggles happen asynchronously using HTMX, providing a snappy user experience without traditional ASP.NET postbacks.

### Final Notes

This comprehensive plan provides a blueprint for migrating your ASP.NET "Release WO for WIS" module to a modern Django application. By following these structured steps and leveraging AI-assisted automation, your organization can achieve:

*   **Improved Maintainability:** Clean, modular Django code with strict separation of concerns (Fat Models, Thin Views).
*   **Enhanced User Experience:** Dynamic, responsive interface with HTMX and DataTables, eliminating full page reloads.
*   **Increased Scalability:** Django's robust architecture supports growing application needs.
*   **Testability:** Comprehensive unit and integration tests ensure code quality and reduce future bugs.
*   **Cost Efficiency:** Automation-driven migration significantly reduces manual development effort and time.

This approach provides a clear path for business stakeholders to understand the transformation and ensures that the technical implementation aligns with modern web development best practices.