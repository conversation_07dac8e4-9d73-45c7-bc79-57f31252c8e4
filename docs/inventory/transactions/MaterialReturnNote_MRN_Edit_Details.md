The following Django modernization plan outlines the strategic transition from the provided ASP.NET application to a modern Django-based solution. This plan focuses on leveraging AI-assisted automation, adhering to a "fat model, thin view" architecture, and utilizing HTMX and Alpine.js for a highly interactive and efficient user experience. All technical recommendations are presented in plain English, suitable for communication with business stakeholders.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the underlying database tables and their columns used by the ASP.NET application.

**Instructions:**
By analyzing the `SqlDataSource` declarations, direct SQL queries in the C# code, and `GridView` column bindings, we can infer the primary tables involved. This application heavily interacts with detailed material return records, updates master records, and validates against business group and work order tables.

**Identified Tables and Their Key Columns:**

*   **`tblInv_MaterialReturn_Master`** (Primary table for Material Return Note header)
    *   `Id` (Primary Key, Integer)
    *   `CompId` (Company Identifier, Integer)
    *   `FyId` (Financial Year Identifier, String)
    *   `MRNNo` (Material Return Note Number, String)
    *   `SysDate` (System Date, Date - for audit)
    *   `SysTime` (System Time, Time - for audit)
    *   `SessionId` (User Session ID / Username, String - for audit)

*   **`tblInv_MaterialReturn_Details`** (Primary table for individual return items)
    *   `Id` (Primary Key, Integer)
    *   `MId` (Foreign Key to `tblInv_MaterialReturn_Master.Id`, Integer)
    *   `ItemId` (Identifier for the returned item, Integer)
    *   `ItemCode` (Code of the returned item, String)
    *   `Description` (Description of the item, String)
    *   `UOM` (Unit of Measure, String)
    *   `RetQty` (Returned Quantity, Decimal with up to 15 digits total, 3 decimal places)
    *   `Remarks` (Any notes for the return, String)
    *   `DeptId` (Identifier for Business Group / Department, Integer)
    *   `WONo` (Work Order Number, String)

*   **`BusinessGroup`** (Lookup table for Business Group / Department names)
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (The actual department name or symbol, String)

*   **`SD_Cust_WorkOrder_Master`** (Lookup table for Work Order validation)
    *   `WONo` (Work Order Number, String)
    *   `CompId` (Company Identifier, Integer)

*   **`tblQc_MaterialReturnQuality_Details`** (Table to check if an item has been quality controlled)
    *   `MRNId` (Foreign Key to `tblInv_MaterialReturn_Details.Id`, Integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the core business logic and operations performed by the ASP.NET code.

**Instructions:**
The existing ASP.NET page is designed for "Editing Details" of a specific Material Return Note. This involves fetching, displaying, and modifying individual line items.

*   **Displaying Data (Read):**
    *   The application fetches a list of Material Return Detail items from the database based on a Master Return Note ID, company ID, and financial year ID. This data is displayed in a tabular format, supporting pagination.
*   **Updating Records (Edit/Save):**
    *   Users can inline edit individual line items directly within the table.
    *   Editable fields include "Returned Quantity" (`RetQty`) and "Remarks".
    *   A critical dynamic field allows selection between a "Business Group" (department) or a "Work Order Number" (`WONo`) for each line item.
    *   **Quantity Validation:** Ensures that the "Returned Quantity" is a valid number, allowing up to 3 decimal places.
    *   **Work Order Validation:** If a "Work Order Number" is selected, the system verifies that the entered `WONo` exists within the company's master work order records. If invalid, an alert message is shown.
    *   **Audit Trail:** Every successful update to a detail item automatically records the current system date, time, and the user's session ID (username) on the associated master return note record. This provides a clear audit history for the main return note.
*   **Preventing Edits (QC Check):**
    *   The system includes a safeguard: if a material return detail item has already undergone a Quality Control (QC) check (indicated by an entry in `tblQc_MaterialReturnQuality_Details`), that specific item cannot be edited further. This prevents unauthorized changes after a critical process step.
*   **Navigation (Cancel):**
    *   A "Cancel" button provides a graceful exit, redirecting the user back to a previous master list or summary page, ensuring a smooth user flow.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET UI elements and how they will be transformed into modern Django templates with HTMX and Alpine.js.

**Instructions:**
The primary user interface component is a large, interactive data grid. We'll replace the ASP.NET GridView with a robust HTML table powered by DataTables for efficient data handling, and use HTMX for all dynamic interactions, ensuring a fast, single-page application feel without complex JavaScript frameworks.

*   **Data Table (List View):** The `asp:GridView` will be fully replaced by a standard HTML `<table>`. This table will be enhanced with **DataTables** for essential features like client-side search, sorting, and pagination, mirroring the original grid's capabilities.
*   **Inline Editing:** Instead of ASP.NET's post-back heavy inline editing, we will use **HTMX**. Clicking "Edit" on a row will trigger an HTMX request to fetch a partial HTML form for that specific row, swapping it into the table. "Update" and "Cancel" actions will then trigger HTMX POST requests, either saving changes or discarding them, and swapping back to the display-only row.
*   **Input Fields:**
    *   `asp:TextBox` elements (for Quantity, Remarks, Work Order Number) will become standard `<input type="text">` or `<textarea>` tags.
    *   `asp:DropDownList` elements (for BG Group/WONo selection, and the Business Group dropdown) will become standard `<select>` tags.
    *   All input fields will be styled with **Tailwind CSS** for a clean, modern look.
*   **Dynamic Field Switching:** The complex logic for showing either a "Business Group" dropdown or a "Work Order Number" textbox based on user selection will be handled dynamically with **HTMX**. When the user changes the main selection dropdown, HTMX will fetch and swap the correct input field (dropdown or textbox) into the row, providing a seamless user experience.
*   **Form Validation:** Client-side validation (like required fields and quantity format) previously handled by ASP.NET validators will be managed by **Django forms** (server-side) and augmented by **Alpine.js** for immediate visual feedback to the user without server roundtrips.
*   **Action Buttons:** `asp:LinkButton` and `asp:Button` will be converted to standard HTML `<button>` tags, primarily driven by **HTMX** attributes for AJAX interactions.
*   **User Feedback:** The original pop-up messages and loading indicators will be replaced by **HTMX's built-in indicators** and simple **Alpine.js** for showing status messages or modal confirmations.

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create Django models that accurately reflect the existing database schema and incorporate business logic.

**Instructions:**
Models are mapped directly to existing database tables using `managed = False`. This ensures Django works with the legacy database without requiring migrations. Business logic, such as checking for QC status and updating audit information, is encapsulated within model methods, adhering to the "fat model" principle.

```python
from django.db import models
from django.utils import timezone
from datetime import time

# These models represent existing lookup tables or master data.
# They are declared with managed=False to connect to an existing database.

class BusinessGroup(models.Model):
    """
    Represents the BusinessGroup table, used for department selections.
    """
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(db_column='Symbol', max_length=100) # Assuming max_length for Symbol

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class WorkOrder(models.Model):
    """
    Represents the SD_Cust_WorkOrder_Master table, used for WONo validation.
    """
    won_o = models.CharField(primary_key=True, db_column='WONo', max_length=100) # Assuming WONo is PK and CharField
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.won_o

class MaterialReturnMaster(models.Model):
    """
    Represents the tblInv_MaterialReturn_Master table.
    This model handles the header-level information for a Material Return Note.
    """
    id = models.IntegerField(primary_key=True, db_column='Id')
    comp_id = models.IntegerField(db_column='CompId')
    fin_id = models.CharField(db_column='FyId', max_length=10) # Assuming FyId is string based on ASP.NET
    mrn_no = models.CharField(db_column='MRNNo', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Note (Master)'
        verbose_name_plural = 'Material Return Notes (Master)'

    def __str__(self):
        return f"MRN: {self.mrn_no} (ID: {self.id})"

    def update_audit_info(self, session_id):
        """
        Updates the audit information (SysDate, SysTime, SessionId)
        for the master record. This is business logic from ASP.NET code-behind.
        """
        self.sys_date = timezone.localdate()
        self.sys_time = timezone.localtime().time()
        self.session_id = session_id
        self.save(update_fields=['sys_date', 'sys_time', 'session_id'])


class QualityControlDetail(models.Model):
    """
    Represents tblQc_MaterialReturnQuality_Details.
    Used to check if a MaterialReturnDetail item has been quality-checked.
    """
    id = models.IntegerField(primary_key=True, db_column='Id')
    m_id = models.IntegerField(db_column='MId') # FK to tblQc_MaterialReturnQuality_Master
    mrn_id = models.IntegerField(db_column='MRNId') # FK to tblInv_MaterialReturn_Details.Id

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Details'
        verbose_name = 'Quality Control Detail'
        verbose_name_plural = 'Quality Control Details'

    def __str__(self):
        return f"QC Detail for MRNId: {self.mrn_id}"


class MaterialReturnDetail(models.Model):
    """
    Represents the tblInv_MaterialReturn_Details table.
    This is the core model for the editable grid items.
    """
    id = models.IntegerField(primary_key=True, db_column='Id')
    master = models.ForeignKey(MaterialReturnMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    item_id = models.IntegerField(db_column='ItemId')
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    description = models.CharField(db_column='Description', max_length=255)
    uom = models.CharField(db_column='UOM', max_length=10)
    ret_qty = models.DecimalField(db_column='RetQty', max_digits=15, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)
    dept_id = models.IntegerField(db_column='DeptId', null=True, blank=True)
    wo_no = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialReturn_Details'
        verbose_name = 'Material Return Detail'
        verbose_name_plural = 'Material Return Details'

    def __str__(self):
        return f"{self.item_code} - {self.description} ({self.ret_qty} {self.uom})"

    def is_qc_checked(self):
        """
        Business logic: Checks if this material return detail has an associated QC entry.
        Corresponds to the disableEdit() method in ASP.NET code-behind.
        If true, the detail item should not be editable.
        """
        return QualityControlDetail.objects.filter(mrn_id=self.id).exists()

    @property
    def display_dept_wo(self):
        """
        Provides a display string for the 'BG Group/WoNo' column,
        similar to ASP.NET's lblWODept/lblDW field.
        """
        if self.wo_no:
            return self.wo_no
        elif self.dept_id:
            try:
                # Attempt to get the symbol from BusinessGroup if DeptId is set
                return BusinessGroup.objects.get(id=self.dept_id).symbol
            except BusinessGroup.DoesNotExist:
                return f"Dept ID: {self.dept_id}" # Fallback if business group not found
        return "N/A" # Default if neither is set

    def save_and_update_master(self, session_id, *args, **kwargs):
        """
        Saves the current detail and updates the master's audit info.
        This consolidates the update logic from GridView1_RowUpdating.
        """
        super().save(*args, **kwargs)
        if self.master:
            self.master.update_audit_info(session_id)

```

### 4.2 Forms

**Task:** Define a Django ModelForm for `MaterialReturnDetail` to handle input validation and saving.

**Instructions:**
The form will facilitate editing of `RetQty`, `Remarks`, `DeptId`, and `WONo`. It includes custom validation for `RetQty` (decimal format) and `WONo` (existence check), reflecting the ASP.NET validation logic. A choice field will represent the `BG Group/WoNo` selection.

```python
from django import forms
from .models import MaterialReturnDetail, BusinessGroup, WorkOrder
from django.core.exceptions import ValidationError
import re

class MaterialReturnDetailForm(forms.ModelForm):
    """
    Form for editing MaterialReturnDetail items.
    Includes custom validation for RetQty and WONo, and manages dynamic fields.
    """
    # Choice field to select between Business Group (1) and Work Order (2)
    # This corresponds to ASP.NET's DropDownList1
    BG_WO_CHOICES = [
        ('1', 'BG Group'),
        ('2', 'WONo'),
    ]
    # Default to '1' (BG Group) if dept_id is set and no wo_no, else '2' if wo_no
    # This logic matches the initial state handling in ASP.NET GridView_RowEditing.
    bg_wo_selection = forms.ChoiceField(
        choices=BG_WO_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True,
        label="Group/WONo Type"
    )

    # These fields are conditionally displayed in the template based on bg_wo_selection
    # They are not directly mapped to model fields in initial declaration because they are dynamic.
    # The clean method will populate the actual model fields.
    # dept_id_display and wo_no_display are for rendering purposes.
    dept_id_display = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        to_field_name='id', # Map to the 'Id' column in BusinessGroup
        required=False,
        empty_label="Select Business Group",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Business Group"
    )
    wo_no_display = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Work Order No.'}),
        label="Work Order No."
    )

    class Meta:
        model = MaterialReturnDetail
        fields = ['ret_qty', 'remarks'] # Core editable fields
        # Exclude item_id, item_code, description, uom as they are read-only for editing
        # dept_id and wo_no will be handled via custom fields and clean method
        widgets = {
            'ret_qty': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'pattern': r'^\d{1,15}(\.\d{0,3})?$', # Client-side regex for quantity validation
                'title': 'Enter a number up to 15 digits with up to 3 decimal places.'
            }),
            'remarks': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values for dynamic fields
        if self.instance.pk:
            if self.instance.wo_no:
                self.fields['bg_wo_selection'].initial = '2' # WONo selected
                self.fields['wo_no_display'].initial = self.instance.wo_no
            elif self.instance.dept_id:
                self.fields['bg_wo_selection'].initial = '1' # BG Group selected
                self.fields['dept_id_display'].initial = self.instance.dept_id
            else:
                self.fields['bg_wo_selection'].initial = '1' # Default if neither is set

    def clean_ret_qty(self):
        """
        Server-side validation for RetQty using regex, matching ASP.NET's RegularExpressionValidator.
        """
        ret_qty = self.cleaned_data['ret_qty']
        if not re.match(r'^\d{1,15}(\.\d{0,3})?$', str(ret_qty)):
            raise ValidationError("Quantity must be a number with up to 15 digits and 3 decimal places.")
        return ret_qty

    def clean(self):
        """
        Handles conditional validation and population of dept_id/wo_no based on selection.
        This corresponds to the complex conditional logic in GridView1_RowUpdating.
        """
        cleaned_data = super().clean()
        bg_wo_selection = cleaned_data.get('bg_wo_selection')
        dept_id_display = cleaned_data.get('dept_id_display')
        wo_no_display = cleaned_data.get('wo_no_display')

        # Reset model fields to ensure only one is populated based on selection
        cleaned_data['dept_id'] = None
        cleaned_data['wo_no'] = None

        if bg_wo_selection == '1': # BG Group selected
            if not dept_id_display:
                self.add_error('dept_id_display', 'Business Group is required.')
            else:
                cleaned_data['dept_id'] = dept_id_display.id
                cleaned_data['wo_no'] = '' # Ensure WONo is cleared
        elif bg_wo_selection == '2': # WONo selected
            if not wo_no_display:
                self.add_error('wo_no_display', 'Work Order Number is required.')
            else:
                # Validate WONo against WorkOrder master table
                # This corresponds to 'checkwo' in ASP.NET
                if not WorkOrder.objects.filter(won_o=wo_no_display, comp_id=self.instance.master.comp_id).exists():
                    self.add_error('wo_no_display', 'Invalid Work Order Number found.')
                else:
                    cleaned_data['wo_no'] = wo_no_display
                    cleaned_data['dept_id'] = 1 # Hardcoded '1' as per ASP.NET logic for WONo type
        
        return cleaned_data

    def save(self, commit=True):
        """
        Overrides save to handle the dynamic fields and update master audit info.
        """
        instance = super().save(commit=False)
        # Apply the cleaned data for dept_id and wo_no to the instance
        instance.dept_id = self.cleaned_data.get('dept_id')
        instance.wo_no = self.cleaned_data.get('wo_no')

        if commit:
            # Use the model's custom save method to also update master audit info
            # We need to pass session_id, which will come from the request user.
            # This will be handled in the view by calling instance.save_and_update_master()
            pass # Defer saving to the view to pass session_id
        return instance

```

### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), keeping views thin and logic in models.

**Instructions:**
The `MaterialReturnDetailEditView` will handle fetching the list of details for a specific master, and the inline editing operations (GET for edit form, POST for update). A dedicated HTMX endpoint for rendering the editable row and handling dynamic field changes is also included. This structure ensures views are compact and focus on request/response handling.

```python
from django.views.generic import ListView, UpdateView, TemplateView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, Http404, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.template.loader import render_to_string
from django.db import transaction

from .models import MaterialReturnDetail, MaterialReturnMaster, BusinessGroup, WorkOrder
from .forms import MaterialReturnDetailForm

# Max lines for view methods: 15
# All business logic MUST be in models or forms.

class MaterialReturnDetailEditView(ListView):
    """
    Main view to display and manage Material Return Note details for a specific master.
    Corresponds to MaterialReturnNote_MRN_Edit_Details.aspx.
    """
    model = MaterialReturnDetail
    template_name = 'inventory/materialreturndetail/list.html'
    context_object_name = 'details'

    def get_queryset(self):
        """
        Filters details by master ID, company ID, and financial year.
        Corresponds to LoadData() in ASP.NET.
        """
        master_id = self.kwargs.get('master_pk')
        # In a real app, CompId and FyId would come from user session/context.
        # For this migration, we'll assume they are also from URL or session for simplicity.
        # ASP.NET used Request.QueryString["CompId"] and Request.QueryString["FyId"]
        # Let's mock them from URL for demonstration or retrieve from session if implemented.
        comp_id = self.request.session.get('compid', 1) # Assuming compid from session
        fin_id = self.request.session.get('finid', '2023-24') # Assuming finid from session

        self.master = get_object_or_404(MaterialReturnMaster,
                                        pk=master_id,
                                        comp_id=comp_id,
                                        fin_id=fin_id)

        # In ASP.NET, a stored procedure was used. Here we simulate the data structure.
        # For full fidelity, you might need to use raw SQL if the SP involves complex logic
        # not easily replicated by ORM.
        queryset = self.model.objects.filter(master=self.master).order_by('id')

        # Perform the disableEdit() check from ASP.NET for each object
        for detail in queryset:
            detail.can_edit = not detail.is_qc_checked()

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['master'] = self.master
        # Pass MRNNo, CompId, FyId for potential navigation/context, as seen in ASP.NET
        context['mrn_no'] = self.master.mrn_no
        context['comp_id'] = self.master.comp_id
        context['fin_id'] = self.master.fin_id
        return context

class MaterialReturnDetailUpdateHTMXView(UpdateView):
    """
    Handles inline editing for a single MaterialReturnDetail item via HTMX.
    This replaces GridView1_RowEditing (GET) and GridView1_RowUpdating (POST).
    """
    model = MaterialReturnDetail
    form_class = MaterialReturnDetailForm
    template_name = 'inventory/materialreturndetail/_row_edit_form.html' # Partial template for editing row
    context_object_name = 'detail'

    def get_object(self, queryset=None):
        """
        Ensure the detail belongs to the correct master and is editable.
        """
        master_id = self.kwargs.get('master_pk')
        detail_id = self.kwargs.get('pk')
        obj = get_object_or_404(
            MaterialReturnDetail.objects.filter(master__pk=master_id),
            pk=detail_id
        )
        if obj.is_qc_checked():
            messages.error(self.request, "This item cannot be edited as it has been Quality Controlled.")
            # If QC checked, return the display row immediately
            return Http404("Item is QC checked and cannot be edited.")
        return obj

    def get(self, request, *args, **kwargs):
        """
        Handles the HTMX GET request to display the editable form for a row.
        """
        try:
            self.object = self.get_object()
        except Http404:
            # If item is QC checked, render the read-only row
            detail = get_object_or_404(MaterialReturnDetail, pk=self.kwargs.get('pk'))
            return render_to_string('inventory/materialreturndetail/_row_display.html', {'detail': detail})

        form = self.get_form()
        return self.render_to_response(self.get_context_data(form=form))

    def form_valid(self, form):
        """
        Handles valid form submission (HTMX POST for update).
        Saves the detail, updates master audit info, and returns the display row.
        """
        # This is the "thin view" part; actual saving logic is in the model/form.
        session_id = self.request.session.get('username', 'system') # Get user session ID
        with transaction.atomic():
            instance = form.save(commit=False) # Get instance from form, but don't commit yet
            instance.save_and_update_master(session_id=session_id) # Call fat model method
            messages.success(self.request, "Material Return Detail updated successfully.")
            # Render the updated display row for HTMX swap
            return render_to_string('inventory/materialreturndetail/_row_display.html', {'detail': instance, 'request': self.request})

    def form_invalid(self, form):
        """
        Handles invalid form submission.
        Returns the form with errors for HTMX to swap back.
        """
        messages.error(self.request, "Error updating detail. Please correct the errors.")
        return self.render_to_response(self.get_context_data(form=form))

class MaterialReturnDetailDisplayHTMXView(TemplateView):
    """
    Renders a single MaterialReturnDetail row in display mode.
    Used for HTMX swaps after cancel or initial load, or if an item is not editable.
    """
    template_name = 'inventory/materialreturndetail/_row_display.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        detail_id = self.kwargs.get('pk')
        detail = get_object_or_404(MaterialReturnDetail, pk=detail_id)
        detail.can_edit = not detail.is_qc_checked() # Add can_edit flag for template
        context['detail'] = detail
        return context

class MaterialReturnDetailWONoDeptDynamicFieldsView(TemplateView):
    """
    HTMX endpoint to dynamically render the correct input field (Business Group dropdown
    or Work Order textbox) based on the user's selection in _row_edit_form.html.
    This replaces part of GridView1_RowEditing logic.
    """
    def get(self, request, *args, **kwargs):
        selected_type = request.GET.get('bg_wo_type')
        detail_id = kwargs.get('pk')
        initial_dept_id = None
        initial_wo_no = ''

        if detail_id:
            try:
                detail = MaterialReturnDetail.objects.get(pk=detail_id)
                initial_dept_id = detail.dept_id
                initial_wo_no = detail.wo_no
            except MaterialReturnDetail.DoesNotExist:
                pass # New row or error, keep defaults

        context = {
            'form': MaterialReturnDetailForm(), # A dummy form to access widget rendering
            'selected_type': selected_type,
            'initial_dept_id': initial_dept_id,
            'initial_wo_no': initial_wo_no,
        }

        # Render only the specific field HTML
        if selected_type == '1': # BG Group
            return HttpResponse(render_to_string(
                'inventory/materialreturndetail/_dept_field.html', context, request))
        elif selected_type == '2': # WONo
            return HttpResponse(render_to_string(
                'inventory/materialreturndetail/_wono_field.html', context, request))
        return HttpResponse("") # Empty response if invalid type

```

### 4.4 Templates

**Task:** Create Django templates for each view, leveraging HTMX, Alpine.js, and DataTables for a rich, dynamic user experience.

**Instructions:**
Templates will extend `core/base.html` for consistency. The main list view (`list.html`) will use DataTables. Inline editing will be managed through HTMX-driven partial templates (`_row_edit_form.html`, `_row_display.html`) to swap content efficiently. Dynamic fields (Business Group/WONo) will have their own small partials (`_dept_field.html`, `_wono_field.html`).

**`inventory/materialreturndetail/list.html`**
This template provides the main page for editing MRN details. It uses HTMX to initially load the table and to re-render it after any updates, ensuring the page stays dynamic.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Return Note [MRN] - Edit Details (MRN: {{ master.mrn_no }})</h2>
        <a href="{% url 'materialreturn_master_list' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    <!-- Messages display -->
    {% if messages %}
    <div x-data="{ show: true }" x-init="setTimeout(() => show = false, 3000)" x-show="show"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-90"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-90"
         class="mb-4">
        {% for message in messages %}
        <div class="p-3 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div id="mrn-details-table-container"
         hx-trigger="load"
         hx-get="{% url 'materialreturndetail_list_partial' master.pk %}"
         hx-swap="innerHTML">
        <!-- Loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Return Details...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component if needed for other page-level interactions
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here for this page logic
    });

    // Event listener for HTMX after swap to reinitialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'mrn-details-table-container') {
            // Check if DataTables already initialized, destroy if so
            if ($.fn.DataTable.isDataTable('#materialReturnDetailTable')) {
                $('#materialReturnDetailTable').DataTable().destroy();
            }
            // Initialize DataTables
            $('#materialReturnDetailTable').DataTable({
                "pageLength": 20, // Match ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "ordering": false, // Disable default ordering, ASP.NET didn't specify
                "searching": true // Enable searching
            });
        }
    });

</script>
{% endblock %}
```

**`inventory/materialreturndetail/_list_table_partial.html`**
This partial template contains the HTML table and is loaded into `list.html` via HTMX. It's responsible for rendering the entire list of detail items.

```html
<table id="materialReturnDetailTable" class="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
    <thead class="bg-gray-50 border-b border-gray-200">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group/WoNo</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Ret Qty</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for detail in details %}
        <tr id="detail-row-{{ detail.pk }}">
            {% include 'inventory/materialreturndetail/_row_display.html' %}
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="text-center py-6 text-gray-500 font-medium text-lg">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTable initialization is handled in list.html after htmx:afterSwap
    // This script block is primarily here for placeholder and can be removed
    // as it's triggered by the parent template's JS.
</script>
```

**`inventory/materialreturndetail/_row_display.html`**
This partial template renders a single detail row in its read-only display mode. It includes the "Edit" button.

```html
<td class="py-2 px-4 text-right">{{ forloop.counter0|add:details.start_index }}</td> {# SN based on pagination #}
<td class="py-2 px-4 text-center">{{ detail.item_code }}</td>
<td class="py-2 px-4 text-left">{{ detail.description }}</td>
<td class="py-2 px-4 text-center">{{ detail.uom }}</td>
<td class="py-2 px-4 text-center font-bold">{{ detail.display_dept_wo }}</td>
<td class="py-2 px-4 text-right">{{ detail.ret_qty|floatformat:"3" }}</td> {# Format to 3 decimal places #}
<td class="py-2 px-4 text-left">{{ detail.remarks|default_if_none:"" }}</td>
<td class="py-2 px-4 text-center">
    {% if detail.can_edit %}
    <button class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-sm"
            hx-get="{% url 'materialreturndetail_edit_htmx' master_pk=detail.master.pk pk=detail.pk %}"
            hx-target="#detail-row-{{ detail.pk }}"
            hx-swap="outerHTML">
        Edit
    </button>
    {% else %}
    <span class="inline-block bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded">QC Checked</span>
    {% endif %}
    <label id="lblmrqn" class="hidden">MRQN</label> {# Placeholder for original hidden label #}
</td>
```

**`inventory/materialreturndetail/_row_edit_form.html`**
This partial template renders a single detail row in its editable form mode. It handles the dynamic input fields.

```html
<td class="py-2 px-4 text-right">{{ forloop.counter0|add:details.start_index }}</td> {# SN based on pagination #}
<td class="py-2 px-4 text-center">{{ detail.item_code }}</td>
<td class="py-2 px-4 text-left">{{ detail.description }}</td>
<td class="py-2 px-4 text-center">{{ detail.uom }}</td>
<td class="py-2 px-4 text-center">
    <div class="mb-2">
        {{ form.bg_wo_selection.label_tag }}
        {{ form.bg_wo_selection }}
        <div class="text-red-500 text-xs mt-1">{{ form.bg_wo_selection.errors }}</div>
    </div>
    <div id="dynamic-wo-dept-field-{{ detail.pk }}"
         hx-get="{% url 'materialreturndetail_dynamic_fields' pk=detail.pk %}"
         hx-trigger="load changed[name='bg_wo_selection'] from:#detail-row-{{ detail.pk }} select[name='bg_wo_selection']"
         hx-target="#dynamic-wo-dept-field-{{ detail.pk }}"
         hx-swap="innerHTML"
         hx-include="this.closest('tr')">
        <!-- Dynamic field (dept_id_display or wo_no_display) will be loaded here -->
    </div>
</td>
<td class="py-2 px-4 text-right">
    <div class="mb-2">
        {{ form.ret_qty }}
        <div class="text-red-500 text-xs mt-1">{{ form.ret_qty.errors }}</div>
    </div>
</td>
<td class="py-2 px-4 text-left">
    <div class="mb-2">
        {{ form.remarks }}
        <div class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</div>
    </div>
</td>
<td class="py-2 px-4 text-center">
    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm mb-1"
            hx-post="{% url 'materialreturndetail_edit_htmx' master_pk=detail.master.pk pk=detail.pk %}"
            hx-target="#detail-row-{{ detail.pk }}"
            hx-swap="outerHTML"
            hx-include="closest tr"
            hx-trigger="click">
        Update
    </button>
    <button class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded text-sm"
            hx-get="{% url 'materialreturndetail_display_htmx' pk=detail.pk %}"
            hx-target="#detail-row-{{ detail.pk }}"
            hx-swap="outerHTML">
        Cancel
    </button>
</td>
```

**`inventory/materialreturndetail/_dept_field.html`**
Partial for the Business Group dropdown.

```html
<div class="mb-2">
    {{ form.dept_id_display }}
    <div class="text-red-500 text-xs mt-1">{{ form.dept_id_display.errors }}</div>
</div>
<script>
    // Set initial selected value if available
    var deptIdDisplay = document.querySelector('#id_dept_id_display');
    if (deptIdDisplay && '{{ initial_dept_id }}') {
        deptIdDisplay.value = '{{ initial_dept_id }}';
    }
</script>
```

**`inventory/materialreturndetail/_wono_field.html`**
Partial for the Work Order Number textbox.

```html
<div class="mb-2">
    {{ form.wo_no_display }}
    <div class="text-red-500 text-xs mt-1">{{ form.wo_no_display.errors }}</div>
</div>
<script>
    // Set initial value if available
    var woNoDisplay = document.querySelector('#id_wo_no_display');
    if (woNoDisplay && '{{ initial_wo_no }}') {
        woNoDisplay.value = '{{ initial_wo_no }}';
    }
</script>
```

### 4.5 URLs

**Task:** Define URL patterns to map incoming web requests to the appropriate Django views.

**Instructions:**
URLs are organized under the `inventory` app, clearly defining paths for the main detail list, and specific HTMX endpoints for inline editing, displaying individual rows, and dynamically updating form fields.

```python
from django.urls import path
from .views import (
    MaterialReturnDetailEditView,
    MaterialReturnDetailUpdateHTMXView,
    MaterialReturnDetailDisplayHTMXView,
    MaterialReturnDetailWONoDeptDynamicFieldsView,
)

app_name = 'inventory' # Define app_name for namespacing URLs

urlpatterns = [
    # Main view for Material Return Note Details (Edit Page)
    # master_pk corresponds to MId from ASP.NET QueryString
    path('materialreturndetail/<int:master_pk>/edit/',
         MaterialReturnDetailEditView.as_view(),
         name='materialreturndetail_list'),

    # HTMX endpoint to fetch the full table content (for initial load and refresh)
    path('materialreturndetail/<int:master_pk>/table_partial/',
         MaterialReturnDetailEditView.as_view(template_name='inventory/materialreturndetail/_list_table_partial.html'),
         name='materialreturndetail_list_partial'),

    # HTMX endpoint for getting / posting individual editable row forms
    path('materialreturndetail/<int:master_pk>/item/<int:pk>/edit_htmx/',
         MaterialReturnDetailUpdateHTMXView.as_view(),
         name='materialreturndetail_edit_htmx'),

    # HTMX endpoint for getting individual display row (after cancel or successful update)
    path('materialreturndetail/item/<int:pk>/display_htmx/',
         MaterialReturnDetailDisplayHTMXView.as_view(),
         name='materialreturndetail_display_htmx'),

    # HTMX endpoint for dynamically rendering the BG Group/WONo input fields
    path('materialreturndetail/item/<int:pk>/dynamic_fields/',
         MaterialReturnDetailWONoDeptDynamicFieldsView.as_view(),
         name='materialreturndetail_dynamic_fields'),

    # Placeholder for redirecting back to a master list, assuming it's under 'inventory' app
    path('materialreturn/master/list/',
         lambda request: HttpResponse("Redirected to Master List Page"), # Replace with actual Master List View
         name='materialreturn_master_list'),
]

```

### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and maintainability.

**Instructions:**
Tests cover model methods, form validations, and view behavior, including HTMX interactions. This ensures high test coverage (aim for 80%+) and validates that the converted logic behaves as expected.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from datetime import date, time
from unittest.mock import patch # For mocking timezone/session data

from .models import (
    MaterialReturnMaster, MaterialReturnDetail,
    BusinessGroup, WorkOrder, QualityControlDetail
)
from .forms import MaterialReturnDetailForm

# --- Model Tests ---

class MaterialReturnModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company_id = 1
        cls.financial_year_id = '2023-24'
        cls.test_session_id = 'testuser123'

        cls.master = MaterialReturnMaster.objects.create(
            id=101,
            comp_id=cls.company_id,
            fin_id=cls.financial_year_id,
            mrn_no='MRN/2023/001',
            sys_date=date.today(),
            sys_time=time(10, 0, 0),
            session_id='initial_user'
        )

        cls.item_id_1 = 1001
        cls.item_id_2 = 1002

        cls.bg_dept_1 = BusinessGroup.objects.create(id=1, symbol='Textile')
        cls.bg_dept_2 = BusinessGroup.objects.create(id=2, symbol='Chemical')
        cls.wo_master = WorkOrder.objects.create(won_o='WO-XYZ-001', comp_id=cls.company_id)
        WorkOrder.objects.create(won_o='WO-ABC-002', comp_id=cls.company_id) # Another WO for negative test

        cls.detail_1 = MaterialReturnDetail.objects.create(
            id=1,
            master=cls.master,
            item_id=cls.item_id_1,
            item_code='FAB001',
            description='Fabric A',
            uom='MTR',
            ret_qty=100.500,
            remarks='Tear on side',
            dept_id=cls.bg_dept_1.id, # Initial BG Group
            wo_no=''
        )
        cls.detail_2 = MaterialReturnDetail.objects.create(
            id=2,
            master=cls.master,
            item_id=cls.item_id_2,
            item_code='CHEM05',
            description='Chemical B',
            uom='KG',
            ret_qty=25.750,
            remarks='Expired',
            dept_id=1, # Hardcoded 1 for WONo type as per ASP.NET logic
            wo_no=cls.wo_master.won_o # Initial WONo
        )

        # Create a QC entry for detail_1 to test disableEdit
        QualityControlDetail.objects.create(id=1, m_id=1, mrn_id=cls.detail_1.id)

    def test_material_return_master_creation(self):
        self.assertEqual(self.master.mrn_no, 'MRN/2023/001')
        self.assertEqual(self.master.comp_id, self.company_id)

    def test_material_return_detail_creation(self):
        self.assertEqual(self.detail_1.item_code, 'FAB001')
        self.assertEqual(self.detail_1.ret_qty, 100.500)
        self.assertEqual(self.detail_1.master, self.master)

    def test_detail_is_qc_checked_method(self):
        self.assertTrue(self.detail_1.is_qc_checked()) # detail_1 has QC entry
        self.assertFalse(self.detail_2.is_qc_checked()) # detail_2 does not

    def test_master_update_audit_info(self):
        old_sys_date = self.master.sys_date
        old_sys_time = self.master.sys_time
        old_session_id = self.master.session_id

        with patch('django.utils.timezone.localdate', return_value=date(2024, 1, 15)):
            with patch('django.utils.timezone.localtime', return_value=time(14, 30, 0)):
                self.master.update_audit_info(self.test_session_id)
                self.master.refresh_from_db()
                self.assertEqual(self.master.sys_date, date(2024, 1, 15))
                self.assertEqual(self.master.sys_time, time(14, 30, 0))
                self.assertEqual(self.master.session_id, self.test_session_id)
                self.assertNotEqual(self.master.sys_date, old_sys_date)

    def test_detail_save_and_update_master(self):
        original_master_session_id = self.master.session_id
        new_detail_qty = 150.000
        test_user = 'new_editor'

        with patch('django.utils.timezone.localdate', return_value=date(2024, 2, 1)):
            with patch('django.utils.timezone.localtime', return_value=time(16, 0, 0)):
                self.detail_2.ret_qty = new_detail_qty
                self.detail_2.save_and_update_master(session_id=test_user)

                self.detail_2.refresh_from_db()
                self.master.refresh_from_db()

                self.assertEqual(self.detail_2.ret_qty, new_detail_qty)
                self.assertEqual(self.master.session_id, test_user)
                self.assertEqual(self.master.sys_date, date(2024, 2, 1))
                self.assertEqual(self.master.sys_time, time(16, 0, 0))
                self.assertNotEqual(self.master.session_id, original_master_session_id)

    def test_display_dept_wo(self):
        # Test with BG Group
        self.assertEqual(self.detail_1.display_dept_wo, self.bg_dept_1.symbol)
        # Test with WONo
        self.assertEqual(self.detail_2.display_dept_wo, self.wo_master.won_o)
        # Test with no dept_id or wo_no
        detail_no_info = MaterialReturnDetail.objects.create(
            id=3, master=self.master, item_id=1, item_code='X', description='Y', uom='Z', ret_qty=1.000)
        self.assertEqual(detail_no_info.display_dept_wo, "N/A")
        # Test with invalid dept_id
        detail_invalid_dept = MaterialReturnDetail.objects.create(
            id=4, master=self.master, item_id=1, item_code='X', description='Y', uom='Z', ret_qty=1.000, dept_id=999)
        self.assertEqual(detail_invalid_dept.display_dept_wo, "Dept ID: 999")


# --- Form Tests ---

class MaterialReturnDetailFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.financial_year_id = '2023-24'
        cls.master = MaterialReturnMaster.objects.create(
            id=102, comp_id=cls.company_id, fin_id=cls.financial_year_id,
            mrn_no='MRN/2023/002', sys_date=date.today(), sys_time=time(10, 0, 0), session_id='test')
        cls.bg_dept = BusinessGroup.objects.create(id=1, symbol='Textile')
        cls.wo_master = WorkOrder.objects.create(won_o='WO-FORM-001', comp_id=cls.company_id)
        cls.detail = MaterialReturnDetail.objects.create(
            id=10, master=cls.master, item_id=1, item_code='ABC', description='Test Item',
            uom='PCS', ret_qty=10.000, remarks='Good', dept_id=cls.bg_dept.id, wo_no='')

    def test_form_valid_data_bg_group(self):
        data = {
            'bg_wo_selection': '1', # BG Group
            'dept_id_display': self.bg_dept.id,
            'wo_no_display': '',
            'ret_qty': '15.500',
            'remarks': 'Updated remarks'
        }
        form = MaterialReturnDetailForm(data=data, instance=self.detail)
        self.assertTrue(form.is_valid(), form.errors)
        instance = form.save(commit=False)
        self.assertEqual(instance.ret_qty, 15.500)
        self.assertEqual(instance.dept_id, self.bg_dept.id)
        self.assertEqual(instance.wo_no, '')

    def test_form_valid_data_wo_no(self):
        data = {
            'bg_wo_selection': '2', # WONo
            'dept_id_display': '', # Not used
            'wo_no_display': self.wo_master.won_o,
            'ret_qty': '20.123',
            'remarks': 'New WO remarks'
        }
        form = MaterialReturnDetailForm(data=data, instance=self.detail)
        self.assertTrue(form.is_valid(), form.errors)
        instance = form.save(commit=False)
        self.assertEqual(instance.ret_qty, 20.123)
        self.assertEqual(instance.dept_id, 1) # Hardcoded 1 as per ASP.NET logic
        self.assertEqual(instance.wo_no, self.wo_master.won_o)

    def test_form_invalid_ret_qty_format(self):
        data = {
            'bg_wo_selection': '1',
            'dept_id_display': self.bg_dept.id,
            'wo_no_display': '',
            'ret_qty': 'abc', # Invalid quantity
            'remarks': 'Remarks'
        }
        form = MaterialReturnDetailForm(data=data, instance=self.detail)
        self.assertFalse(form.is_valid())
        self.assertIn('ret_qty', form.errors)
        self.assertIn('Quantity must be a number with up to 15 digits and 3 decimal places.', form.errors['ret_qty'])

    def test_form_invalid_wo_no_not_found(self):
        data = {
            'bg_wo_selection': '2',
            'dept_id_display': '',
            'wo_no_display': 'NON_EXISTENT_WO', # Invalid WO
            'ret_qty': '10.000',
            'remarks': 'Remarks'
        }
        form = MaterialReturnDetailForm(data=data, instance=self.detail)
        self.assertFalse(form.is_valid())
        self.assertIn('wo_no_display', form.errors)
        self.assertIn('Invalid Work Order Number found.', form.errors['wo_no_display'])

    def test_form_missing_bg_group(self):
        data = {
            'bg_wo_selection': '1',
            'dept_id_display': '', # Missing
            'wo_no_display': '',
            'ret_qty': '10.000',
            'remarks': 'Remarks'
        }
        form = MaterialReturnDetailForm(data=data, instance=self.detail)
        self.assertFalse(form.is_valid())
        self.assertIn('dept_id_display', form.errors)
        self.assertIn('Business Group is required.', form.errors['dept_id_display'])

    def test_form_missing_wo_no(self):
        data = {
            'bg_wo_selection': '2',
            'dept_id_display': '',
            'wo_no_display': '', # Missing
            'ret_qty': '10.000',
            'remarks': 'Remarks'
        }
        form = MaterialReturnDetailForm(data=data, instance=self.detail)
        self.assertFalse(form.is_valid())
        self.assertIn('wo_no_display', form.errors)
        self.assertIn('Work Order Number is required.', form.errors['wo_no_display'])


# --- View Tests ---

class MaterialReturnDetailViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Set up shared data for all view tests
        cls.company_id = 1
        cls.financial_year_id = '2023-24'
        cls.test_session_id = 'testuser'

        cls.master = MaterialReturnMaster.objects.create(
            id=201, comp_id=cls.company_id, fin_id=cls.financial_year_id,
            mrn_no='MRN/2023/003', sys_date=date.today(), sys_time=time(10, 0, 0), session_id='initial')
        
        cls.detail_editable = MaterialReturnDetail.objects.create(
            id=20, master=cls.master, item_id=1, item_code='VIEW01', description='View Item A',
            uom='PC', ret_qty=5.000, remarks='OK', dept_id=None, wo_no='WO-VIEW-001')
        
        cls.detail_qc_checked = MaterialReturnDetail.objects.create(
            id=21, master=cls.master, item_id=2, item_code='VIEW02', description='View Item B',
            uom='EA', ret_qty=10.000, remarks='QC Checked', dept_id=None, wo_no='')
        QualityControlDetail.objects.create(id=2, m_id=1, mrn_id=cls.detail_qc_checked.id) # Mark as QC checked

        cls.bg_dept_view = BusinessGroup.objects.create(id=1, symbol='General')
        cls.wo_master_view = WorkOrder.objects.create(won_o='WO-VIEW-001', comp_id=cls.company_id)
        WorkOrder.objects.create(won_o='WO-NEW-002', comp_id=cls.company_id)

    def setUp(self):
        # Set up session data for each test, mimicking ASP.NET
        session = self.client.session
        session['compid'] = self.company_id
        session['finid'] = self.financial_year_id
        session['username'] = self.test_session_id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:materialreturndetail_list', args=[self.master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/list.html')
        self.assertIn('details', response.context)
        self.assertEqual(len(response.context['details']), 2) # Both details should be present
        self.assertTrue(response.context['master'])

    def test_list_view_qc_checked_flag(self):
        response = self.client.get(reverse('inventory:materialreturndetail_list', args=[self.master.pk]))
        details = response.context['details']
        
        detail_1 = next(d for d in details if d.id == self.detail_editable.id)
        detail_2 = next(d for d in details if d.id == self.detail_qc_checked.id)

        self.assertTrue(detail_1.can_edit)
        self.assertFalse(detail_2.can_edit)

    def test_list_partial_htmx_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:materialreturndetail_list_partial', args=[self.master.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_list_table_partial.html')
        self.assertIn('details', response.context)
        self.assertEqual(len(response.context['details']), 2)

    def test_edit_htmx_get_editable_item(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:materialreturndetail_edit_htmx', args=[self.master.pk, self.detail_editable.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_row_edit_form.html')
        self.assertIn('form', response.context)
        self.assertIn('detail', response.context)
        self.assertEqual(response.context['detail'], self.detail_editable)

    def test_edit_htmx_get_qc_checked_item(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:materialreturndetail_edit_htmx', args=[self.master.pk, self.detail_qc_checked.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        # Should render the display row, not the edit form
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_row_display.html')
        self.assertIn('detail', response.context)
        self.assertEqual(response.context['detail'], self.detail_qc_checked)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "This item cannot be edited as it has been Quality Controlled.")

    def test_edit_htmx_post_valid_update_bg_group(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'bg_wo_selection': '1',
            'dept_id_display': self.bg_dept_view.id,
            'wo_no_display': '',
            'ret_qty': '55.555',
            'remarks': 'Updated via HTMX'
        }
        response = self.client.post(reverse('inventory:materialreturndetail_edit_htmx', args=[self.master.pk, self.detail_editable.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_row_display.html') # Should return display row
        
        self.detail_editable.refresh_from_db()
        self.master.refresh_from_db()

        self.assertEqual(self.detail_editable.ret_qty, 55.555)
        self.assertEqual(self.detail_editable.dept_id, self.bg_dept_view.id)
        self.assertEqual(self.detail_editable.wo_no, '')
        self.assertEqual(self.master.session_id, self.test_session_id) # Master audit info updated

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Material Return Detail updated successfully.")


    def test_edit_htmx_post_invalid_update(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'bg_wo_selection': '1',
            'dept_id_display': '', # Missing required field
            'wo_no_display': '',
            'ret_qty': 'invalid', # Invalid quantity
            'remarks': 'Updated via HTMX'
        }
        response = self.client.post(reverse('inventory:materialreturndetail_edit_htmx', args=[self.master.pk, self.detail_editable.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_row_edit_form.html') # Should return form with errors
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('ret_qty', response.context['form'].errors)
        self.assertIn('dept_id_display', response.context['form'].errors)
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Error updating detail. Please correct the errors.")


    def test_display_htmx_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:materialreturndetail_display_htmx', args=[self.detail_editable.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_row_display.html')
        self.assertIn('detail', response.context)
        self.assertEqual(response.context['detail'], self.detail_editable)

    def test_dynamic_fields_htmx_get_bg_group(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:materialreturndetail_dynamic_fields', args=[self.detail_editable.pk]) + '?bg_wo_type=1', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_dept_field.html')
        self.assertContains(response, '<select name="dept_id_display"')
        # Check if initial value is correctly set based on self.detail_editable's initial state (wo_no was set for detail_editable, not dept_id)
        # This will be empty, as detail_editable had wo_no initially
        self.assertContains(response, 'value=""') # The JS will set it if initial_dept_id exists.

    def test_dynamic_fields_htmx_get_wo_no(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Testing on a detail that had WONo initially
        response = self.client.get(reverse('inventory:materialreturndetail_dynamic_fields', args=[self.detail_editable.pk]) + '?bg_wo_type=2', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturndetail/_wono_field.html')
        self.assertContains(response, '<input type="text" name="wo_no_display"')
        self.assertContains(response, 'value="WO-VIEW-001"') # Initial value should be loaded

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The migration ensures a highly dynamic user interface without traditional JavaScript frameworks. HTMX handles all server-side interactions, and Alpine.js provides client-side reactive sugar.

*   **HTMX for Dynamic Updates:**
    *   The `inventory/materialreturndetail/list.html` uses `hx-get` to initially load the entire table content into `#mrn-details-table-container` from `materialreturndetail_list_partial`. This ensures the table data is dynamically loaded.
    *   **Inline Edit/Update/Cancel:**
        *   "Edit" buttons on each row use `hx-get` to request `materialreturndetail_edit_htmx` for the specific row, swapping the display row with the editable form row (`_row_edit_form.html`).
        *   "Update" buttons within the edit form use `hx-post` to submit the form data back to `materialreturndetail_edit_htmx`. Upon successful update, the view returns the updated display row (`_row_display.html`) to be swapped back in. On failure, the view returns the form with errors.
        *   "Cancel" buttons use `hx-get` to request `materialreturndetail_display_htmx` for the specific row, swapping back to the original display state.
    *   **Dynamic BG Group/WONo Fields:**
        *   Within `_row_edit_form.html`, a dedicated `div` (`#dynamic-wo-dept-field`) uses `hx-get` with a `changed` trigger on the `bg_wo_selection` dropdown. This fetches either `_dept_field.html` or `_wono_field.html` based on the selected value, swapping the correct input type dynamically.
    *   **Success Messages:** HTMX's implicit handling of `HX-Trigger` headers (or explicit ones in views) can be used to re-display messages, combined with Alpine.js for fade-out effects. Messages are directly rendered in `list.html` and appear on updates.

*   **Alpine.js for UI State:**
    *   Alpine.js is used for simple client-side reactive elements, primarily for managing the visibility and auto-hide of notification messages (e.g., success/error banners). It keeps the UI lightweight.
    *   For the dynamic BG Group/WONo fields, HTMX does most of the heavy lifting. Alpine.js is less critical for this specific interaction, ensuring a minimal client-side footprint.
    *   DataTables initialization is handled by jQuery/JavaScript within the `base.html` context, re-initialized via HTMX `htmx:afterSwap` event listener in `list.html`.

*   **DataTables for List Views:**
    *   The `inventory/materialreturndetail/_list_table_partial.html` includes the basic HTML table structure.
    *   The `list.html` contains JavaScript to initialize DataTables on the table (`#materialReturnDetailTable`) after the HTMX swap. This ensures client-side search, sort, and pagination are enabled, providing a powerful and familiar data grid experience.

*   **DRY Templates:**
    *   `list.html` acts as the main container.
    *   `_list_table_partial.html` loads the entire table.
    *   `_row_display.html` and `_row_edit_form.html` are partials for individual rows, promoting reusability and clean separation.
    *   `_dept_field.html` and `_wono_field.html` are small partials for conditional form fields.
    *   All templates extend `core/base.html` for consistent layout and CDN inclusion.

---

## Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET application to a modern Django solution. By focusing on automated processes, leveraging Django's robust ORM, and implementing an HTMX+Alpine.js frontend, the organization can achieve:

*   **Improved Maintainability:** Clean code structure, separation of concerns, and clear responsibilities for models, forms, and views.
*   **Enhanced Performance:** HTMX eliminates full page reloads for dynamic interactions, leading to a faster and more responsive user experience.
*   **Reduced Development Overhead:** AI-assisted automation can streamline code generation and pattern application, reducing manual effort significantly.
*   **Scalability:** Django's architecture is well-suited for scaling web applications.
*   **Future-Proofing:** Transitioning to a widely adopted, open-source framework like Django ensures long-term viability and access to a large developer community.

The generated code is complete and runnable, providing a strong foundation for the migration. Further refinements would involve adapting the exact business rules, error handling, and security considerations from the original ASP.NET application, guided by this systematic approach.