## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Understanding:** This application manages the editing and clearing of supplier challans. It involves a main challan record, detailed items within that challan, and records of quantities cleared against those detailed items.

**Instructions:**
Based on the ASP.NET code, we've identified three core database tables involved in this module: `tblInv_Supplier_Challan_Master` for the main challan information, `tblInv_Supplier_Challan_Details` for individual items on the challan, and `tblInv_Supplier_Challan_Clear` for tracking quantities cleared against these detailed items.

**Inferred Tables and Columns:**

**`tblInv_Supplier_Challan_Master` (Main Challan Information)**
- `Id`: Unique identifier for the challan (Primary Key, Integer)
- `Remarks`: Any notes or comments about the challan (Text)
- `VehicleNo`: Vehicle number used for transportation (Text)
- `Transpoter`: Name of the transporter (Text)
- `SupplierId`: Identifier for the supplier (Integer, inferred)
- `SysDate`: Date of system entry/last modification (Date)
- `SysTime`: Time of system entry/last modification (Time)
- `SessionId`: User session identifier (Text, likely a username)
- `CompId`: Company identifier (Integer)
- `FinYearId`: Financial year identifier (Integer)

**`tblInv_Supplier_Challan_Details` (Individual Items on Challan)**
- `Id`: Unique identifier for the challan detail (Primary Key, Integer)
- `MId`: Foreign key linking to `tblInv_Supplier_Challan_Master.Id` (Integer)
- `PRDId`: Purchase Request Detail identifier (Integer)
- `ChallanQty`: Quantity recorded on the challan for this item (Decimal with 3 decimal places)
- `ItemCode`: Code for the item (Text)
- `ManfDesc`: Manufacturer's description of the item (Text, displayed as "Description")
- `UOM`: Unit of Measure (Text)
- `PRQty`: Quantity from the original Purchase Request (Decimal with 3 decimal places)
- `SCNo`: Supplier Challan Number (Text)
- `SCDate`: Supplier Challan Date (Date)

**`tblInv_Supplier_Challan_Clear` (Quantities Cleared Against Challan Items)**
- `Id`: Unique identifier for the cleared quantity record (Primary Key, Integer)
- `DId`: Foreign key linking to `tblInv_Supplier_Challan_Details.Id` (Integer)
- `ClearedQty`: Quantity that has been cleared (Decimal with 3 decimal places)
- `SysDate`: Date of system entry/last modification (Date)
- `SysTime`: Time of system entry/last modification (Time)
- `SessionId`: User session identifier (Text, likely a username)
- `CompId`: Company identifier (Integer)
- `FinYearId`: Financial year identifier (Integer)

### Step 2: Identify Backend Functionality

**Business Understanding:** The application allows users to edit master challan details, update quantities for items on the challan, and record quantities that have been cleared against those challan items. Crucially, it enforces quantity validations to prevent over-receiving or over-clearing.

**Instructions:**
The existing ASP.NET code primarily focuses on **Update** operations for existing challans and their associated details and cleared quantities. It also includes sophisticated **Validation** logic for quantities.

-   **Read (Display Data):**
    -   The system reads the main challan information (Vehicle No., Transporter, Remarks) from `tblInv_Supplier_Challan_Master`.
    -   It displays a list of challan items from `tblInv_Supplier_Challan_Details`, along with calculated "Remaining Quantity" against the original Purchase Request.
    -   It displays a list of cleared quantities from `tblInv_Supplier_Challan_Clear` linked to the challan details, along with calculated "Cleared Quantity" totals for each item.
-   **Update (Modify Data):**
    -   Users can modify the `Remarks`, `VehicleNo`, and `Transpoter` for the master challan record.
    -   Users can update the `ChallanQty` for individual items listed on the challan, typically when receiving goods.
    -   Users can update the `ClearedQty` for individual challan items, indicating how much of the received quantity has been processed or "cleared".
-   **Validation (Business Rules Enforcement):**
    -   **Quantity Format:** Input quantities must be numeric with specific decimal precision (up to 15 digits before, 3 after the decimal point).
    -   **Challan Quantity Validation:** When updating a challan item's quantity, the new quantity, combined with any previously challaned quantities for the same Purchase Request item, cannot exceed the original Purchase Request quantity. It also ensures the new quantity doesn't exceed the 'remaining' quantity as per the specific ASP.NET calculation.
    -   **Cleared Quantity Validation:** When updating a cleared quantity, the total cleared quantity for a specific challan item cannot exceed the `ChallanQty` (the quantity on the challan itself).
    -   **Conditional Input:** Quantity input fields are enabled/disabled based on whether their corresponding checkbox is selected.
-   **Navigation:** The application uses tabs to switch between editing challan details and clearing quantities. It also handles redirects after update/cancel actions.

### Step 3: Infer UI Components

**Business Understanding:** The user interface provides a structured way to manage complex data related to supplier challans, utilizing tabs for logical separation and interactive grids for data entry and display.

**Instructions:**
The ASP.NET UI uses standard web controls to achieve its functionality. We will map these to modern web components using Django templates, HTMX for dynamic updates, and Alpine.js for client-side interactivity, all styled with Tailwind CSS.

-   **Page Layout:** A master page structure, which translates to Django's template inheritance (extending `core/base.html`).
-   **Tabbed Interface:** The `AjaxControlToolkit:TabContainer` will be re-implemented using HTMX to load content for each tab dynamically, ensuring a smooth user experience without full page reloads. Alpine.js will manage the active tab state.
-   **Data Grids (Tables):**
    -   `GridView2` (for `tblInv_Supplier_Challan_Details`): Will be replaced by a Django template partial containing a standard HTML `<table>` element, enhanced by **DataTables.js** for client-side sorting, filtering, and pagination. Each row will include:
        -   A checkbox (e.g., `CheckBox1`) for selection.
        -   Display labels for "PR NO", "WO No", "SC No", "Date", "Item Code", "Description", "UOM", "PR Qty", "Remain Qty".
        -   An editable text input (e.g., `txtqty`) for "Challan Qty". This input will be enabled/disabled via Alpine.js based on the checkbox state.
    -   `GridView1` (for `tblInv_Supplier_Challan_Clear`): Similar to `GridView2`, it will be a DataTables-powered HTML table. Each row will include:
        -   A checkbox (e.g., `CheckBox2`) for selection.
        -   Display labels for "PR NO", "WO No", "SC No", "Date", "Item Code", "Description", "UOM", "Challan Qty", "Cleared Qty".
        -   An editable text input (e.g., `txtqty`) for "Clear Qty", also enabled/disabled by Alpine.js.
-   **Form Fields (Master Details):**
    -   `txtVehicleNo`, `txtTranspoter`: Standard text input fields.
    -   `txtRemarks`: A multi-line text area.
-   **Action Buttons:**
    -   `BtnAdd` (Update), `BtnAdd1` (Update): These will be standard HTML `<button>` elements that trigger HTMX `hx-post` requests to submit form data for the respective sections.
    -   `Btncancel`, `BtnCancel1`: Standard HTML `<button>` elements that trigger HTMX `hx-redirect` or simple `window.location.href` to navigate away.
-   **Client-Side Interactivity:**
    -   The `AutoPostBack` functionality of checkboxes will be replaced by **Alpine.js** for toggling input field `disabled` states, ensuring a responsive UI without server roundtrips for simple UI changes.
    -   Success/error messages will be handled by Django's `messages` framework and displayed via HTMX.
    -   Loading indicators (from `loadingNotifier.js`) will be natively managed by HTMX.

### Step 4: Generate Django Code

We will create a Django application named `inventory` to house this functionality.

#### 4.1 Models

**Business Understanding:** Our Django models are the blueprint for the database tables and encapsulate all the business logic related to supplier challans, ensuring data integrity and consistency. The "fat model" approach means complex calculations and validations live here, not in the views.

**Instructions:**
We define three Django models that directly map to the existing database tables. The `Meta` class is used to specify `managed = False` (Django won't create/alter these tables) and `db_table` (to use existing table names). Crucial business logic for quantity validation and calculation is implemented as methods within these models.

```python
# inventory/models.py
from django.db import models, transaction
from django.core.exceptions import ValidationError
from django.db.models import Sum
from decimal import Decimal

# Assuming session, company, financial year context can be accessed
# from request or global settings in a real application.
# For demonstration, we'll use placeholders or infer based on ASP.NET usage.

class SupplierChallanMaster(models.Model):
    """
    Corresponds to tblInv_Supplier_Challan_Master.
    Manages the overall supplier challan information.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    vehicle_no = models.CharField(db_column='VehicleNo', max_length=255, blank=True, null=True)
    transporter = models.CharField(db_column='Transpoter', max_length=255, blank=True, null=True)
    supplier_id = models.IntegerField(db_column='SupplierId', blank=True, null=True) # Inferred from SP usage
    
    # Audit fields, auto_now_add is for creation, for update explicit assignment will be needed
    sys_date = models.DateField(db_column='SysDate', auto_now=True) 
    sys_time = models.TimeField(db_column='SysTime', auto_now=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # Username
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Challan_Master'
        verbose_name = 'Supplier Challan Master'
        verbose_name_plural = 'Supplier Challan Masters'

    def __str__(self):
        return f"Challan: {self.id} - {self.vehicle_no}"

    # Business logic: Updates the master record's changeable fields.
    def update_master_details(self, remarks, vehicle_no, transporter, session_id, comp_id, fy_id):
        """
        Updates the main challan details.
        """
        self.remarks = remarks
        self.vehicle_no = vehicle_no
        self.transporter = transporter
        self.session_id = session_id
        self.company_id = comp_id
        self.financial_year_id = fy_id
        self.save() # sys_date and sys_time will update automatically via auto_now=True

class SupplierChallanDetail(models.Model):
    """
    Corresponds to tblInv_Supplier_Challan_Details.
    Details for each item in a supplier challan.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(SupplierChallanMaster, models.DO_NOTHING, db_column='MId', related_name='details')
    prd_id = models.IntegerField(db_column='PRDId', blank=True, null=True) # Purchase Request Detail ID
    challan_qty = models.DecimalField(db_column='ChallanQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    description = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=10, blank=True, null=True)
    pr_qty = models.DecimalField(db_column='PRQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))
    sc_no = models.CharField(db_column='SCNo', max_length=50, blank=True, null=True)
    sc_date = models.DateField(db_column='SCDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Challan_Details'
        verbose_name = 'Supplier Challan Detail'
        verbose_name_plural = 'Supplier Challan Details'

    def __str__(self):
        return f"Detail {self.id} for Challan {self.master.id}: {self.item_code}"

    # Business logic: Calculates the remaining quantity against PR.
    def get_total_challaned_qty_for_pr_item(self):
        """
        Calculates the total quantity challaned across all supplier challan details
        for the same Purchase Request Detail (PRDId), excluding the current record's
        existing challan_qty (as we are evaluating a potential *new* challan_qty).
        This mirrors `TotChalnQty` logic from `View_Sup_PR_Challan_Calc` / SP.
        """
        existing_challaned = SupplierChallanDetail.objects.filter(
            prd_id=self.prd_id
        ).exclude(id=self.id).aggregate(Sum('challan_qty'))['challan_qty__sum'] or Decimal('0.000')
        return existing_challaned

    def get_remaining_pr_qty(self):
        """
        Calculates the effective remaining quantity for this PR item.
        This is `rmnQty` from ASP.NET `GetSupplier_PR_ChQty` SP.
        It's PRQty minus what has already been challaned (excluding this detail's original qty).
        """
        total_challaned_excluding_self = self.get_total_challaned_qty_for_pr_item()
        return self.pr_qty - total_challaned_excluding_self

    def validate_challan_update_qty(self, new_challan_qty):
        """
        Validates if the new challan quantity is valid based on PR quantity and existing challans.
        Mirrors the complex validation from `BtnAdd_Click` in ASP.NET.
        """
        if not isinstance(new_challan_qty, (int, float, Decimal)):
            raise ValidationError("Challan quantity must be a number.", code='invalid_type')
        new_challan_qty = Decimal(str(new_challan_qty)) # Ensure Decimal type

        if new_challan_qty < 0:
            raise ValidationError("Challan quantity cannot be negative.", code='negative_qty')

        # This is the "TotChalnQty" that would exist if `new_challan_qty` were applied to this detail.
        total_challaned_if_updated = new_challan_qty + self.get_total_challaned_qty_for_pr_item()

        # ASP.NET logic: (PrQty - (Qty + TotChalnQty)) >= 0
        # This translates to: (Qty + TotChalnQty) <= PrQty
        if total_challaned_if_updated > self.pr_qty:
            raise ValidationError(
                f"Total challaned quantity for item ({total_challaned_if_updated:.3f}) "
                f"exceeds PR quantity ({self.pr_qty:.3f}).",
                code='exceeds_pr_qty'
            )
        
        # ASP.NET logic: (rmnQty + lblQty - Qty) >= 0
        # Where: rmnQty = TotDiffQty from SP (our get_remaining_pr_qty())
        #        lblQty = Original ChallanQty (self.challan_qty)
        #        Qty = new_challan_qty
        # This translates to: new_challan_qty <= self.get_remaining_pr_qty() + self.challan_qty
        # This ensures that the new quantity doesn't exceed the amount currently available from PR
        # if this detail were to 'return' its original quantity and then draw from the pool again.
        if new_challan_qty > (self.get_remaining_pr_qty() + self.challan_qty):
            raise ValidationError(
                f"Input quantity ({new_challan_qty:.3f}) is more than remaining ({self.get_remaining_pr_qty() + self.challan_qty:.3f}).",
                code='exceeds_available'
            )

    def update_challan_qty(self, new_challan_qty):
        """
        Updates the challan quantity for this detail record after validation.
        """
        with transaction.atomic():
            self.validate_challan_update_qty(new_challan_qty)
            self.challan_qty = Decimal(str(new_challan_qty))
            self.save()

class SupplierChallanClear(models.Model):
    """
    Corresponds to tblInv_Supplier_Challan_Clear.
    Records quantities cleared against a supplier challan detail.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    challan_detail = models.ForeignKey(SupplierChallanDetail, models.DO_NOTHING, db_column='DId', related_name='clears')
    cleared_qty = models.DecimalField(db_column='ClearedQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))

    # Audit fields
    sys_date = models.DateField(db_column='SysDate', auto_now=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # Username
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Challan_Clear'
        verbose_name = 'Supplier Challan Clear'
        verbose_name_plural = 'Supplier Challan Clears'

    def __str__(self):
        return f"Clear {self.id} for Challan Detail {self.challan_detail.id}: {self.cleared_qty}"

    # Business logic: Calculates total cleared quantity for a detail.
    def get_total_cleared_qty_for_related_detail(self):
        """
        Calculates the total quantity cleared against the associated
        SupplierChallanDetail, excluding the current clear record's quantity.
        This mirrors the `StrSql3` logic in `GetValidates()` for `TotClearedQty`.
        """
        existing_cleared = SupplierChallanClear.objects.filter(
            challan_detail=self.challan_detail
        ).exclude(id=self.id).aggregate(Sum('cleared_qty'))['cleared_qty__sum'] or Decimal('0.000')
        return existing_cleared
        
    def validate_clear_update_qty(self, new_clear_qty):
        """
        Validates if the new cleared quantity is valid based on the challan quantity.
        Mirrors `BtnAdd1_Click` validation: `ChallanQty - CleardQty >= 0`.
        """
        if not isinstance(new_clear_qty, (int, float, Decimal)):
            raise ValidationError("Cleared quantity must be a number.", code='invalid_type')
        new_clear_qty = Decimal(str(new_clear_qty))

        if new_clear_qty < 0:
            raise ValidationError("Cleared quantity cannot be negative.", code='negative_qty')

        # Total cleared quantity if `new_clear_qty` were applied to this clear record.
        total_cleared_if_updated = new_clear_qty + self.get_total_cleared_qty_for_related_detail()

        # ASP.NET logic: ChallanQty - CleardQty >= 0
        # This translates to: total_cleared_if_updated <= challan_detail.challan_qty
        if total_cleared_if_updated > self.challan_detail.challan_qty:
            raise ValidationError(
                f"Total cleared quantity ({total_cleared_if_updated:.3f}) "
                f"exceeds challan quantity ({self.challan_detail.challan_qty:.3f}).",
                code='exceeds_challan_qty'
            )

    def update_cleared_qty(self, new_clear_qty, session_id, comp_id, fy_id):
        """
        Updates the cleared quantity for this clear record after validation.
        """
        with transaction.atomic():
            self.validate_clear_update_qty(new_clear_qty)
            self.cleared_qty = Decimal(str(new_clear_qty))
            self.session_id = session_id
            self.company_id = comp_id
            self.financial_year_id = fy_id
            self.save() # sys_date and sys_time will update automatically

```

#### 4.2 Forms

**Business Understanding:** Django forms provide a robust way to handle user input, validate data, and map it to our models. We use `ModelForm` for single records and `inlineformset_factory` for handling multiple related records (like challan details or clear records) on a single form. Custom validation ensures business rules are met.

**Instructions:**
We create a `ModelForm` for the `SupplierChallanMaster` to edit its fields. For the detail and clear items, we use `inlineformset_factory` to create formsets that allow updating multiple `SupplierChallanDetail` and `SupplierChallanClear` instances simultaneously. A custom `QuantityField` is defined to enforce the specific numeric format and basic validity checks as in the ASP.NET code.

```python
# inventory/forms.py
from django import forms
from django.forms import inlineformset_factory
from .models import SupplierChallanMaster, SupplierChallanDetail, SupplierChallanClear
import re
from decimal import Decimal

class QuantityField(forms.DecimalField):
    """
    Custom DecimalField with ASP.NET-like validation for quantity.
    Ensures numeric input with up to 15 digits before, 3 after decimal, and non-negative.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(max_digits=18, decimal_places=3, localize=False, *args, **kwargs) # localize=False for consistent format
        self.widget.attrs.update({'pattern': r"^\d{1,15}(\.\d{0,3})?$", 'title': 'Enter up to 15 digits before and 3 digits after the decimal point.'})


    def clean(self, value):
        if value in self.empty_values:
            if self.required:
                raise forms.ValidationError(self.error_messages['required'], code='required')
            return None # Return None if not required and empty
        
        # Ensure value is string for regex matching
        value_str = str(value)

        # Regex validation equivalent to ASP.NET's ^\d{1,15}(\.\d{0,3})?$
        if not re.fullmatch(r"^\d{1,15}(\.\d{0,3})?$", value_str):
            raise forms.ValidationError("Invalid quantity format. Up to 15 digits before, 3 after decimal.", code='invalid_format')
        
        try:
            cleaned_value = Decimal(value_str)
        except (ValueError, TypeError):
            raise forms.ValidationError("Enter a valid number.", code='invalid')

        if cleaned_value < 0:
            raise forms.ValidationError("Quantity cannot be negative.", code='negative_quantity')

        return cleaned_value

class SupplierChallanMasterForm(forms.ModelForm):
    """Form for editing the master details of a supplier challan."""
    class Meta:
        model = SupplierChallanMaster
        fields = ['vehicle_no', 'transporter', 'remarks']
        widgets = {
            'vehicle_no': forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'transporter': forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-20', 'rows': 3}),
        }

class SupplierChallanDetailForm(forms.ModelForm):
    """
    Form for an individual supplier challan detail item,
    used in the formset for 'Supplier Challan' tab.
    """
    # Checkbox to indicate if this item should be updated.
    select_item = forms.BooleanField(required=False, initial=False, label="",
                                     widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-4 w-4 text-blue-600'}))
    
    # Custom quantity field with client-side Alpine.js attributes for toggling disabled state.
    challan_qty = QuantityField(required=True, label="Challan Qty",
                                widget=forms.TextInput(attrs={
                                    'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                    'x-bind:disabled': '!itemChecked', # Alpine.js: disabled if itemChecked is false
                                    'x-ref': 'qtyField' # Alpine.js reference for direct access
                                }))

    class Meta:
        model = SupplierChallanDetail
        fields = ['id', 'challan_qty'] # 'id' is hidden, used to identify the instance

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure the hidden ID field is correctly handled for existing instances
        if self.instance and self.instance.pk:
            self.fields['id'].widget = forms.HiddenInput()
            # Set initial value for challan_qty from instance for display
            if self.instance.challan_qty is not None:
                self.initial['challan_qty'] = self.instance.challan_qty.normalize()

    def clean(self):
        cleaned_data = super().clean()
        select_item = cleaned_data.get('select_item')
        new_challan_qty = cleaned_data.get('challan_qty')

        # Only apply model-level validation if the item is selected and a new quantity is provided.
        if select_item and self.instance and new_challan_qty is not None:
            try:
                self.instance.validate_challan_update_qty(new_challan_qty)
            except forms.ValidationError as e:
                # Add validation errors to the challan_qty field
                self.add_error('challan_qty', e)
        return cleaned_data

# Formset for SupplierChallanDetail items linked to a Master Challan.
SupplierChallanDetailFormSet = inlineformset_factory(
    SupplierChallanMaster,
    SupplierChallanDetail,
    form=SupplierChallanDetailForm,
    fields=['challan_qty', 'id'], # Required fields for the formset to work
    extra=0, # No extra blank forms for editing existing items
    can_delete=False # No deletion of detail items through this formset
)

class SupplierChallanClearForm(forms.ModelForm):
    """
    Form for an individual supplier challan clear item,
    used in the formset for 'Clear Challan' tab.
    """
    select_item = forms.BooleanField(required=False, initial=False, label="",
                                     widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-4 w-4 text-blue-600'}))

    cleared_qty = QuantityField(required=True, label="Clear Qty",
                                widget=forms.TextInput(attrs={
                                    'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                    'x-bind:disabled': '!itemChecked', # Alpine.js: disabled if itemChecked is false
                                    'x-ref': 'qtyField' # Alpine.js reference for direct access
                                }))

    class Meta:
        model = SupplierChallanClear
        fields = ['id', 'cleared_qty'] # 'id' is hidden, used to identify the instance

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            self.fields['id'].widget = forms.HiddenInput()
            # Set initial value for cleared_qty from instance for display
            if self.instance.cleared_qty is not None:
                self.initial['cleared_qty'] = self.instance.cleared_qty.normalize()
        
    def clean(self):
        cleaned_data = super().clean()
        select_item = cleaned_data.get('select_item')
        new_clear_qty = cleaned_data.get('cleared_qty')

        # Only apply model-level validation if the item is selected and a new quantity is provided.
        if select_item and self.instance and new_clear_qty is not None:
            try:
                # Note: Model's update method handles audit fields like session_id etc.
                self.instance.validate_clear_update_qty(new_clear_qty)
            except forms.ValidationError as e:
                self.add_error('cleared_qty', e)
        return cleaned_data

# Formset for SupplierChallanClear items linked to a SupplierChallanDetail.
SupplierChallanClearFormSet = inlineformset_factory(
    SupplierChallanDetail, # Parent model for SupplierChallanClear
    SupplierChallanClear,
    form=SupplierChallanClearForm,
    fields=['cleared_qty', 'id'],
    extra=0,
    can_delete=False
)

```

#### 4.3 Views

**Business Understanding:** Our Django views act as the interface between user requests and our models. Following the "thin view" principle, they handle HTTP requests, instantiate forms/formsets, call model methods for business logic, and render the appropriate templates. HTMX enables these views to return small, targeted HTML updates for a dynamic user experience.

**Instructions:**
We'll define a main `UpdateView` for the `SupplierChallanMaster` which also orchestrates the formsets for details. Separate views will handle the HTMX requests for updating the detail and clear item grids, ensuring minimal code in each view.

```python
# inventory/views.py
from django.views.generic import UpdateView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from django.db import transaction
from django.contrib.auth.mixins import LoginRequiredMixin # Assumes authentication

from .models import SupplierChallanMaster, SupplierChallanDetail, SupplierChallanClear
from .forms import (
    SupplierChallanMasterForm,
    SupplierChallanDetailFormSet,
    SupplierChallanClearFormSet,
    SupplierChallanDetailForm # For individual row updates via HTMX
)

# Placeholder for user and company info (replace with actual session/user data)
# In a real app, this would come from request.user, request.session, or middleware.
def get_session_context(request):
    return {
        'session_id': request.user.username if request.user.is_authenticated else 'anonymous',
        'comp_id': request.session.get('compid', 1), # Default to 1, replace with actual
        'fy_id': request.session.get('finyear', 1)  # Default to 1, replace with actual
    }

class SupplierChallanDetailEditView(LoginRequiredMixin, UpdateView):
    """
    Main view to edit Supplier Challan Master details.
    Also handles initial display of Supplier Challan Details and Clear Challan grids.
    This view will render the full page with the tab structure.
    """
    model = SupplierChallanMaster
    form_class = SupplierChallanMasterForm
    template_name = 'inventory/supplier_challan/supplier_challan_edit.html'
    context_object_name = 'challan_master'
    pk_url_kwarg = 'pk' # Matches the 'pk' from URL pattern

    def get_success_url(self):
        # Redirect back to the same edit page after successful master update
        return reverse_lazy('supplier_challan_edit', kwargs={'pk': self.object.pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The forms for details and clears will be loaded via HTMX,
        # so we don't pass formsets here directly for performance.
        # We only need the master form for this view.
        return context

    def form_valid(self, form):
        # Master form submission logic
        session_context = get_session_context(self.request)
        with transaction.atomic():
            self.object.update_master_details(
                remarks=form.cleaned_data['remarks'],
                vehicle_no=form.cleaned_data['vehicle_no'],
                transporter=form.cleaned_data['transporter'],
                session_id=session_context['session_id'],
                comp_id=session_context['comp_id'],
                fy_id=session_context['fy_id']
            )
        messages.success(self.request, 'Challan master details updated successfully.')
        
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, respond with status 204 (No Content) to avoid full page reload
            # and trigger a client-side event to refresh relevant sections if necessary.
            # Here, we might just re-render the form with success message for confirmation.
            # Or, if this form is part of a larger HTMX-controlled area, trigger.
            # For a main page refresh, a simple redirect or no specific HX-Trigger is fine.
            return render(self.request, 'inventory/supplier_challan/_master_form_partial.html', {
                'form': form,
                'challan_master': self.object,
            })
        return super().form_valid(form)

    def form_invalid(self, form):
        messages.error(self.request, 'Please correct the errors in the master details.')
        if self.request.headers.get('HX-Request'):
             return render(self.request, 'inventory/supplier_challan/_master_form_partial.html', {
                'form': form,
                'challan_master': self.object,
            }, status=400) # Indicate an error via status code
        return super().form_invalid(form)


class SupplierChallanDetailsTablePartialView(LoginRequiredMixin, View):
    """
    Renders the Supplier Challan Details table for HTMX requests.
    """
    def get(self, request, pk, *args, **kwargs):
        challan_master = get_object_or_404(SupplierChallanMaster, pk=pk)
        
        # Prefetch related challan_detail objects
        detail_formset = SupplierChallanDetailFormSet(instance=challan_master)
        
        return render(request, 'inventory/supplier_challan/_supplier_challan_details_table.html', {
            'challan_master': challan_master,
            'detail_formset': detail_formset,
        })

    def post(self, request, pk, *args, **kwargs):
        challan_master = get_object_or_404(SupplierChallanMaster, pk=pk)
        detail_formset = SupplierChallanDetailFormSet(request.POST, instance=challan_master)
        
        session_context = get_session_context(request)
        
        if detail_formset.is_valid():
            instances_to_update = []
            for form in detail_formset:
                if form.cleaned_data.get('select_item') and form.has_changed():
                    detail_instance = form.instance
                    new_challan_qty = form.cleaned_data['challan_qty']
                    # Apply updates via model method
                    try:
                        detail_instance.update_challan_qty(new_challan_qty)
                    except ValidationError as e:
                        form.add_error('challan_qty', e) # Add error back to the form
                        messages.error(request, f"Validation error for item {detail_instance.item_code}: {e.message}")
                        return render(request, 'inventory/supplier_challan/_supplier_challan_details_table.html', {
                            'challan_master': challan_master,
                            'detail_formset': detail_formset,
                        }, status=400)
            
            messages.success(request, 'Supplier challan details updated successfully.')
            # HTMX: Return 204 No Content to indicate success and avoid full page reload.
            # The HX-Trigger will refresh the table.
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshChallanDetails'})
        else:
            messages.error(request, 'Please correct the errors in the supplier challan details.')
            # If invalid, re-render the formset with errors
            return render(request, 'inventory/supplier_challan/_supplier_challan_details_table.html', {
                'challan_master': challan_master,
                'detail_formset': detail_formset,
            }, status=400) # Indicate an error via status code


class SupplierChallanClearTablePartialView(LoginRequiredMixin, View):
    """
    Renders the Clear Challan table for HTMX requests.
    """
    def get(self, request, pk, *args, **kwargs):
        challan_master = get_object_or_404(SupplierChallanMaster, pk=pk)
        
        # We need to build a formset for SupplierChallanClear.
        # This requires iterating through SupplierChallanDetail items to get their related clear items.
        # A more direct approach for the formset factory would be to pass queryset of clear items directly,
        # but the ASP.NET code loads per DId (Detail ID).
        
        # To get the data for GridView1, ASP.NET uses `GetSup_Challan_Clear_Edit` SP
        # which probably joins tblInv_Supplier_Challan_Details and tblInv_Supplier_Challan_Clear.
        # We will iterate through challan_details and generate a form for each existing clear record,
        # or an empty form if no clear record exists for a detail.
        
        # Option 1: Directly query SupplierChallanClear and pass the queryset.
        # Filter clear records by master's details
        clear_records = SupplierChallanClear.objects.filter(
            challan_detail__master=challan_master
        ).select_related('challan_detail') # Optimize lookup of parent detail

        # Create a "dummy" parent instance for formset to work, or use custom formset.
        # For simplicity, let's create a custom list of forms.
        clear_forms = []
        for detail in challan_master.details.all():
            # Get existing clear records for this detail
            existing_clears = detail.clears.all()
            if existing_clears.exists():
                for clear_obj in existing_clears:
                    clear_forms.append(SupplierChallanClearForm(instance=clear_obj, prefix=f'clear_form_{detail.id}_{clear_obj.id}'))
            else:
                # If no existing clear record for this detail, we need to decide if user can create one.
                # ASP.NET code implies editing existing ones by DId.
                # For simplicity, we'll assume there's always at least one clear record to edit per detail,
                # or create a dummy one if not.
                # Here, we simplify by focusing on updating *existing* clear records.
                # If no clear records are found for a detail, it won't appear in the formset by default.
                # To match ASP.NET (which seems to always show existing clear entries if any),
                # we only prepare forms for existing `SupplierChallanClear` instances.
                pass # No form for non-existent clear records for a detail

        # In a real scenario, this would likely be a proper formset where parent is SupplierChallanMaster,
        # then through SupplierChallanDetail as an intermediary for SupplierChallanClear.
        # For this setup, we'll process each clear record individually.
        
        # A simpler approach for the formset to correctly handle "Clear Challan" tab:
        # We need to map `SupplierChallanClear` instances to their parent `SupplierChallanDetail`s,
        # and then collect all `SupplierChallanClear` forms for the master challan.
        
        # Get all SupplierChallanClear instances related to this SupplierChallanMaster
        all_clear_instances = SupplierChallanClear.objects.filter(
            challan_detail__master=challan_master
        ).select_related('challan_detail') # Preload detail for efficient access

        # We'll use a custom formset approach as inlineformset_factory is for direct parent-child relationships.
        # This will be simpler: create individual forms for each clear instance.
        clear_forms_list = []
        for instance in all_clear_instances:
            form = SupplierChallanClearForm(instance=instance, prefix=f'clear-{instance.pk}')
            clear_forms_list.append(form)

        return render(request, 'inventory/supplier_challan/_supplier_challan_clear_table.html', {
            'challan_master': challan_master,
            'clear_forms_list': clear_forms_list, # List of individual forms
            'total_forms': len(clear_forms_list) # For management form if needed (though not using FormSet class)
        })

    def post(self, request, pk, *args, **kwargs):
        challan_master = get_object_or_404(SupplierChallanMaster, pk=pk)
        session_context = get_session_context(request)
        
        success_count = 0
        errors = []

        # Loop through submitted form data based on known clear record IDs
        # We assume form fields are named something like 'clear-PK-cleared_qty'
        
        clear_instances = SupplierChallanClear.objects.filter(
            challan_detail__master=challan_master
        )

        with transaction.atomic():
            for clear_instance in clear_instances:
                prefix = f'clear-{clear_instance.pk}'
                form = SupplierChallanClearForm(request.POST, instance=clear_instance, prefix=prefix)
                
                if form.is_valid():
                    if form.cleaned_data.get('select_item'): # Only process if checkbox was checked
                        new_clear_qty = form.cleaned_data['cleared_qty']
                        try:
                            clear_instance.update_cleared_qty(
                                new_clear_qty=new_clear_qty,
                                session_id=session_context['session_id'],
                                comp_id=session_context['comp_id'],
                                fy_id=session_context['fy_id']
                            )
                            success_count += 1
                        except ValidationError as e:
                            form.add_error('cleared_qty', e)
                            errors.append(f"Validation error for clear ID {clear_instance.pk}: {e.message}")
                else:
                    if form.cleaned_data.get('select_item', False): # Add errors if checked but invalid
                        errors.append(f"Form error for clear ID {clear_instance.pk}: {form.errors.as_text()}")

            if errors:
                messages.error(request, 'Please correct the errors in the clear challan details.')
                for error_msg in errors:
                    messages.error(request, error_msg)
                
                # Re-render the table with current errors
                clear_forms_list = []
                for instance in clear_instances:
                    # Re-instantiate forms to show errors correctly on the re-rendered partial
                    form = SupplierChallanClearForm(request.POST if errors else None, instance=instance, prefix=f'clear-{instance.pk}')
                    # Manually add non-field errors from processing if any
                    for error_msg in errors:
                        if f"clear ID {instance.pk}" in error_msg: # Simple matching for demo
                             form.add_error(None, error_msg) # Add as non-field error
                    clear_forms_list.append(form)

                return render(request, 'inventory/supplier_challan/_supplier_challan_clear_table.html', {
                    'challan_master': challan_master,
                    'clear_forms_list': clear_forms_list,
                }, status=400) # Indicate an error via status code
            else:
                messages.success(request, 'Clear challan details updated successfully.')
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshChallanClears'})

        messages.error(request, 'An unexpected error occurred during update.')
        return HttpResponse(status=400) # Generic error

```

#### 4.4 Templates

**Business Understanding:** Django templates render dynamic HTML content, extending a base layout and inserting module-specific elements. HTMX will be heavily used for partial updates, meaning sections of the page can be reloaded without a full page refresh, enhancing user experience. Alpine.js will manage simple UI states directly in the browser.

**Instructions:**
We'll create a main template for the overall page, and several partial templates that are loaded via HTMX to update specific sections (like the master form, and the two DataTables grids).

```html
{# inventory/templates/inventory/supplier_challan/supplier_challan_edit.html #}
{% extends 'core/base.html' %}

{% block title %}Edit Supplier Challan{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Edit Supplier Challan - ID: {{ challan_master.id }}</h2>

    {# HTMX messages display area #}
    <div id="messages" class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>

    {# Master Details Form #}
    <div id="master-form-container" class="bg-white shadow-md rounded-lg p-6 mb-8"
         hx-trigger="load, refreshMasterForm from:body"
         hx-get="{% url 'supplier_challan_master_form_partial' pk=challan_master.pk %}"
         hx-swap="innerHTML">
        {# Loading indicator for HTMX #}
        <div class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading master details...</p>
        </div>
    </div>

    {# Tab Container using HTMX and Alpine.js #}
    <div x-data="{ activeTab: 'details' }">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a @click="activeTab = 'details'"
                   :class="activeTab === 'details' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm cursor-pointer"
                   hx-get="{% url 'supplier_challan_details_table_partial' pk=challan_master.pk %}"
                   hx-target="#tab-content"
                   hx-indicator="#tab-loading-indicator"
                   hx-swap="innerHTML">
                    Supplier Challan
                </a>
                <a @click="activeTab = 'clear'"
                   :class="activeTab === 'clear' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm cursor-pointer"
                   hx-get="{% url 'supplier_challan_clear_table_partial' pk=challan_master.pk %}"
                   hx-target="#tab-content"
                   hx-indicator="#tab-loading-indicator"
                   hx-swap="innerHTML">
                    Clear Challan
                </a>
            </nav>
        </div>

        {# Loading indicator for tab content #}
        <div id="tab-loading-indicator" class="htmx-indicator text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading tab content...</p>
        </div>

        {# Tab Content Area - Initial load will be for 'details' tab #}
        <div id="tab-content" class="py-6">
            {# Content will be loaded here via HTMX #}
            {# Initial load triggered by HTMX on the 'Supplier Challan' tab link #}
            {# To ensure initial content without manual click on load #}
            <div hx-get="{% url 'supplier_challan_details_table_partial' pk=challan_master.pk %}"
                 hx-trigger="load delay:50ms" hx-swap="innerHTML">
                <div class="text-center py-4">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading initial tab content...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global event listener for HTMX messages
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr && evt.detail.xhr.getResponseHeader('HX-Trigger')) {
            const triggers = JSON.parse(evt.detail.xhr.getResponseHeader('HX-Trigger'));
            if (triggers.messages) {
                // Assuming messages are returned in a separate div
                // You might need a more sophisticated way to inject messages or re-trigger global refresh
                // For simplicity, we can reload the message container on relevant updates
            }
        }
    });

    // Re-render messages div on form submissions to show success/error
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.target && evt.detail.target.id === 'master-form-container' || 
            evt.detail.xhr.getResponseHeader('HX-Trigger') === 'refreshChallanDetails' ||
            evt.detail.xhr.getResponseHeader('HX-Trigger') === 'refreshChallanClears') {
            
            // Re-fetch messages after a successful update (204 No Content) or a form re-render (400)
            // This is a simplified approach. A dedicated messages view or custom event might be better.
            fetch('/messages/') // Assuming a URL '/messages/' that renders the latest messages
                .then(response => response.text())
                .then(html => {
                    const messagesDiv = document.getElementById('messages');
                    if (messagesDiv) {
                        messagesDiv.innerHTML = html;
                    }
                });
        }
    });

    // DataTable initialization
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Only initialize DataTables if the element exists in the swapped content
        if (event.detail.target.id === 'tab-content' || event.detail.target.id === 'challan-details-table-container' || event.detail.target.id === 'challan-clear-table-container') {
            const detailTable = document.getElementById('supplierChallanDetailsTable');
            if (detailTable && !$.fn.DataTable.isDataTable('#supplierChallanDetailsTable')) {
                $(detailTable).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true // Destroy existing DataTable before re-initializing
                });
            }
            const clearTable = document.getElementById('supplierChallanClearTable');
            if (clearTable && !$.fn.DataTable.isDataTable('#supplierChallanClearTable')) {
                $(clearTable).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true
                });
            }
        }
    });
</script>
{% endblock %}

```

```html
{# inventory/templates/inventory/supplier_challan/_master_form_partial.html #}
<div class="p-0"> {# Padding controlled by parent container #}
    <h3 class="text-xl font-semibold text-gray-700 mb-4">Challan Master Details</h3>
    <form hx-post="{% url 'supplier_challan_edit' pk=challan_master.pk %}" hx-swap="outerHTML" hx-target="#master-form-container">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
                <label for="{{ form.vehicle_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Vehicle No.</label>
                {{ form.vehicle_no }}
                {% if form.vehicle_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vehicle_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.transporter.id_for_label }}" class="block text-sm font-medium text-gray-700">Transporter</label>
                {{ form.transporter }}
                {% if form.transporter.errors %}<p class="text-red-500 text-xs mt-1">{{ form.transporter.errors }}</p>{% endif %}
            </div>
        </div>
        <div class="mb-6">
            <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">Remarks</label>
            {{ form.remarks }}
            {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
        </div>
        
        <div class="flex justify-end space-x-3">
            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Update Master
            </button>
            <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                    onclick="window.location.href='{% url 'supplier_challan_list' %}'"> {# Example redirect to a list view #}
                Cancel
            </button>
        </div>
    </form>
</div>

```

```html
{# inventory/templates/inventory/supplier_challan/_supplier_challan_details_table.html #}
{# This partial is loaded into #tab-content via HTMX #}
<div class="bg-white shadow-md rounded-lg p-6">
    <h3 class="text-xl font-semibold text-gray-700 mb-4">Supplier Challan Items</h3>
    
    <form hx-post="{% url 'supplier_challan_details_table_partial' pk=challan_master.pk %}"
          hx-swap="outerHTML" hx-target="#tab-content"
          hx-trigger="submit from #[supplierChallanDetailsTable] .btn-update-details, refreshChallanDetails from:body"
          x-data> {# Alpine.js context for data and functions #}
        {% csrf_token %}
        {{ detail_formset.management_form }}
        
        <div class="overflow-x-auto">
            <table id="supplierChallanDetailsTable" class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR NO</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SC No</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                        <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PR Qty</th>
                        <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Remain Qty</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if detail_formset %}
                        {% for form in detail_formset %}
                        <tr x-data="{ itemChecked: {{ form.select_item.value|lower }} }"> {# Alpine.js state for checkbox #}
                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center">
                                {{ form.select_item }}
                                {{ form.id }} {# Hidden ID field for the instance #}
                                {% if form.select_item.errors %}<p class="text-red-500 text-xs mt-1">{{ form.select_item.errors }}</p>{% endif %}
                            </td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.pr_no }}</td> {# Assuming PRNo is a field/property #}
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.wo_no }}</td> {# Assuming WONo is a field/property #}
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.sc_no }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.sc_date|date:"Y-m-d" }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.item_code }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ form.instance.description }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.uom }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ form.instance.pr_qty|floatformat:3 }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ form.instance.get_remaining_pr_qty|floatformat:3 }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-left">
                                {{ form.challan_qty }}
                                {% if form.challan_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.challan_qty.errors }}</p>{% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                    <tr>
                        <td colspan="12" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        {% if detail_formset.forms %}
        <div class="mt-6 flex justify-center space-x-4">
            <button type="submit" class="btn-update-details bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Update
            </button>
            <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                    onclick="window.location.href='{% url 'supplier_challan_list' %}'">
                Cancel
            </button>
        </div>
        {% endif %}
    </form>
</div>

```

```html
{# inventory/templates/inventory/supplier_challan/_supplier_challan_clear_table.html #}
{# This partial is loaded into #tab-content via HTMX #}
<div class="bg-white shadow-md rounded-lg p-6">
    <h3 class="text-xl font-semibold text-gray-700 mb-4">Clear Challan Items</h3>
    
    <form hx-post="{% url 'supplier_challan_clear_table_partial' pk=challan_master.pk %}"
          hx-swap="outerHTML" hx-target="#tab-content"
          hx-trigger="submit from #[supplierChallanClearTable] .btn-update-clear, refreshChallanClears from:body"
          x-data> {# Alpine.js context #}
        {% csrf_token %}
        {# No formset management form as we're not using formset_factory directly for post #}
        
        <div class="overflow-x-auto">
            <table id="supplierChallanClearTable" class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gray-50">
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR NO</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SC No</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                        <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Qty</th>
                        <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Cleared Qty</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Clear Qty</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% if clear_forms_list %}
                        {% for form in clear_forms_list %}
                        <tr x-data="{ itemChecked: {{ form.select_item.value|lower }} }"> {# Alpine.js state for checkbox #}
                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center">
                                {{ form.select_item }}
                                {{ form.id }} {# Hidden ID field for the instance #}
                            </td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.challan_detail.pr_no }}</td> {# Assuming PRNo is a field/property #}
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.challan_detail.wo_no }}</td> {# Assuming WONo is a field/property #}
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.challan_detail.sc_no }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.challan_detail.sc_date|date:"Y-m-d" }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.challan_detail.item_code }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ form.instance.challan_detail.description }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ form.instance.challan_detail.uom }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ form.instance.challan_detail.challan_qty|floatformat:3 }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ form.instance.get_total_cleared_qty_for_related_detail|floatformat:3 }}</td>
                            <td class="py-2 px-4 whitespace-nowrap text-left">
                                {{ form.cleared_qty }}
                                {% if form.cleared_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cleared_qty.errors }}</p>{% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                    <tr>
                        <td colspan="12" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
                    </tr>
                    {% endif %}
                </tbody>
            </table>
        </div>
        
        {% if clear_forms_list %}
        <div class="mt-6 flex justify-center space-x-4">
            <button type="submit" class="btn-update-clear bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Update
            </button>
            <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                    onclick="window.location.href='{% url 'supplier_challan_list' %}'">
                Cancel
            </button>
        </div>
        {% endif %}
    </form>
</div>

```

```html
{# inventory/templates/messages.html #}
{# A simple partial for messages, to be loaded via HTMX #}
{% for message in messages %}
<div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
    {{ message }}
</div>
{% endfor %}
```

#### 4.5 URLs

**Business Understanding:** URLs define how users navigate and interact with our application. Clean and predictable URLs improve usability and maintainability. HTMX interactions rely on specific URL endpoints for partial updates.

**Instructions:**
We'll define URL patterns for the main challan edit page, and dedicated URLs for the HTMX-powered partial views that load the master form, the details table, and the clear table.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    SupplierChallanDetailEditView,
    SupplierChallanDetailsTablePartialView,
    SupplierChallanClearTablePartialView,
)
from django.contrib.messages.views import SuccessMessageMixin
from django.views.generic import TemplateView # For dummy list page redirect

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    # Main challan edit page (handles master form GET/POST)
    path('supplier-challan/edit/<int:pk>/', SupplierChallanDetailEditView.as_view(), name='supplier_challan_edit'),

    # HTMX endpoints for partial views:
    path('supplier-challan/edit/<int:pk>/master-form/', SuccessMessageMixin.as_view(template_name='inventory/supplier_challan/_master_form_partial.html'), name='supplier_challan_master_form_partial'), # Only for GET
    path('supplier-challan/edit/<int:pk>/details-table/', SupplierChallanDetailsTablePartialView.as_view(), name='supplier_challan_details_table_partial'),
    path('supplier-challan/edit/<int:pk>/clear-table/', SupplierChallanClearTablePartialView.as_view(), name='supplier_challan_clear_table_partial'),
    
    # Dummy URL for the redirect from "Cancel" buttons (replace with actual list view URL)
    path('supplier-challan/list/', TemplateView.as_view(template_name='inventory/supplier_challan/dummy_list.html'), name='supplier_challan_list'),
    
    # Optional: A dedicated view to render messages if needed for HTMX
    path('messages/', TemplateView.as_view(template_name='messages.html'), name='messages_partial'),

]
```
```python
# inventory/templates/inventory/supplier_challan/dummy_list.html
{# A placeholder for the actual supplier challan list view #}
{% extends 'core/base.html' %}

{% block title %}Supplier Challans List{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Supplier Challans List (Placeholder)</h2>
    <p class="text-gray-700">This is a placeholder page for the supplier challan list. In a real application, this would display all challans.</p>
    <a href="{% url 'inventory:supplier_challan_edit' pk=1 %}" class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Go to Sample Edit Challan (ID 1)
    </a>
</div>
{% endblock %}
```

#### 4.6 Tests

**Business Understanding:** Automated tests are critical for ensuring the correctness and reliability of our application. They verify that models behave as expected (unit tests) and that views correctly handle user interactions and data flow (integration tests). Achieving high test coverage boosts confidence in the migration.

**Instructions:**
We'll include comprehensive tests for model methods (especially quantity calculations and validations) and for view functionalities (GET requests for rendering, POST requests for updates, and HTMX-specific behaviors).

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from django.db import transaction
from decimal import Decimal
from unittest.mock import patch, MagicMock

from .models import SupplierChallanMaster, SupplierChallanDetail, SupplierChallanClear
from .forms import (
    SupplierChallanMasterForm,
    SupplierChallanDetailFormSet,
    SupplierChallanClearForm,
    SupplierChallanClearFormSet
)

# Mock user and session data for tests
MOCK_USERNAME = 'testuser'
MOCK_COMP_ID = 100
MOCK_FY_ID = 2023

# Mock the get_session_context utility function for views
mock_get_session_context_patch = patch('inventory.views.get_session_context',
    return_value={'session_id': MOCK_USERNAME, 'comp_id': MOCK_COMP_ID, 'fy_id': MOCK_FY_ID})

class SupplierChallanMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a sample master challan for all tests
        cls.master = SupplierChallanMaster.objects.create(
            id=1,
            remarks="Initial remarks",
            vehicle_no="VEH123",
            transporter="Transporter A",
            supplier_id=1,
            company_id=MOCK_COMP_ID,
            financial_year_id=MOCK_FY_ID,
            session_id=MOCK_USERNAME
        )
  
    def test_master_creation(self):
        self.assertEqual(self.master.remarks, "Initial remarks")
        self.assertEqual(self.master.vehicle_no, "VEH123")
        self.assertEqual(str(self.master), "Challan: 1 - VEH123")

    def test_update_master_details(self):
        new_remarks = "Updated remarks"
        new_vehicle_no = "VEH456"
        new_transporter = "Transporter B"
        
        self.master.update_master_details(
            remarks=new_remarks,
            vehicle_no=new_vehicle_no,
            transporter=new_transporter,
            session_id=MOCK_USERNAME,
            comp_id=MOCK_COMP_ID,
            fy_id=MOCK_FY_ID
        )
        self.master.refresh_from_db()
        self.assertEqual(self.master.remarks, new_remarks)
        self.assertEqual(self.master.vehicle_no, new_vehicle_no)
        self.assertEqual(self.master.transporter, new_transporter)


class SupplierChallanDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master = SupplierChallanMaster.objects.create(id=1, remarks="", vehicle_no="", transporter="")
        cls.detail1 = SupplierChallanDetail.objects.create(
            id=101, master=cls.master, prd_id=1, challan_qty=Decimal('10.000'), pr_qty=Decimal('100.000'),
            item_code="ITEM001", description="Test Item 1", uom="PCS", sc_no="SC001", sc_date="2023-01-01"
        )
        cls.detail2 = SupplierChallanDetail.objects.create(
            id=102, master=cls.master, prd_id=1, challan_qty=Decimal('20.000'), pr_qty=Decimal('100.000'),
            item_code="ITEM002", description="Test Item 2", uom="KG", sc_no="SC001", sc_date="2023-01-01"
        )
        cls.detail3_same_pr = SupplierChallanDetail.objects.create(
            id=103, master=cls.master, prd_id=1, challan_qty=Decimal('15.000'), pr_qty=Decimal('100.000'), # Same PRDId as detail1
            item_code="ITEM003", description="Test Item 3", uom="M", sc_no="SC002", sc_date="2023-01-02"
        )

    def test_get_total_challaned_qty_for_pr_item(self):
        # detail1's prd_id=1. Other challaned for prd_id=1 is detail2 (20) and detail3 (15)
        # Should be 20+15 = 35
        self.assertEqual(self.detail1.get_total_challaned_qty_for_pr_item(), Decimal('35.000'))
        # For detail2: other challaned is detail1 (10) and detail3 (15) = 25
        self.assertEqual(self.detail2.get_total_challaned_qty_for_pr_item(), Decimal('25.000'))

    def test_get_remaining_pr_qty(self):
        # pr_qty=100. Challaned by others for prd_id=1: detail2(20) + detail3(15) = 35
        # Remaining PR qty for detail1's perspective: 100 - 35 = 65
        self.assertEqual(self.detail1.get_remaining_pr_qty(), Decimal('65.000'))

    def test_validate_challan_update_qty_valid(self):
        # detail1: PRQty=100, original_challan_qty=10, others_challaned=35.
        # rmnQty = 100 - 35 = 65.
        # Max allowed: rmnQty + original_challan_qty = 65 + 10 = 75
        # Test valid: new_qty=50 (50 <= 75 and 50+35 <= 100)
        self.assertIsNone(self.detail1.validate_challan_update_qty(Decimal('50.000')))
        self.assertIsNone(self.detail1.validate_challan_update_qty(Decimal('75.000'))) # Max allowed

    def test_validate_challan_update_qty_exceeds_pr_qty(self):
        # Try to set challan_qty for detail1 to 70.
        # Total if updated = 70 (new) + 35 (others) = 105. PRQty is 100.
        with self.assertRaisesMessage(ValidationError, "Total challaned quantity for item (105.000) exceeds PR quantity (100.000)."):
            self.detail1.validate_challan_update_qty(Decimal('70.000'))

    def test_validate_challan_update_qty_exceeds_available(self):
        # Try to set challan_qty for detail1 to 76.
        # rmnQty + original_challan_qty = 65 + 10 = 75.
        with self.assertRaisesMessage(ValidationError, "Input quantity (76.000) is more than remaining (75.000)."):
            self.detail1.validate_challan_update_qty(Decimal('76.000'))

    def test_update_challan_qty(self):
        self.detail1.update_challan_qty(Decimal('15.000'))
        self.detail1.refresh_from_db()
        self.assertEqual(self.detail1.challan_qty, Decimal('15.000'))

    def test_update_challan_qty_invalid(self):
        with self.assertRaises(ValidationError):
            self.detail1.update_challan_qty(Decimal('100.000')) # Will fail validation


class SupplierChallanClearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master = SupplierChallanMaster.objects.create(id=1, remarks="", vehicle_no="", transporter="")
        cls.detail = SupplierChallanDetail.objects.create(
            id=201, master=cls.master, prd_id=2, challan_qty=Decimal('50.000'), pr_qty=Decimal('100.000'),
            item_code="ITEM004", description="Test Item 4", uom="PCS", sc_no="SC002", sc_date="2023-01-01"
        )
        cls.clear1 = SupplierChallanClear.objects.create(
            id=301, challan_detail=cls.detail, cleared_qty=Decimal('10.000'),
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FY_ID, session_id=MOCK_USERNAME
        )
        cls.clear2 = SupplierChallanClear.objects.create(
            id=302, challan_detail=cls.detail, cleared_qty=Decimal('5.000'),
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FY_ID, session_id=MOCK_USERNAME
        )

    def test_get_total_cleared_qty_for_related_detail(self):
        # For clear1 (10), others are clear2 (5). Total should be 5.
        self.assertEqual(self.clear1.get_total_cleared_qty_for_related_detail(), Decimal('5.000'))
        # For clear2 (5), others are clear1 (10). Total should be 10.
        self.assertEqual(self.clear2.get_total_cleared_qty_for_related_detail(), Decimal('10.000'))

    def test_validate_clear_update_qty_valid(self):
        # detail.challan_qty = 50. clear1.cleared_qty = 10, clear2.cleared_qty = 5.
        # Total cleared by others for clear1 is 5.
        # Max allowed for clear1: 50 (challan_qty) - 5 (others) = 45.
        self.assertIsNone(self.clear1.validate_clear_update_qty(Decimal('40.000')))
        self.assertIsNone(self.clear1.validate_clear_update_qty(Decimal('45.000'))) # Max allowed

    def test_validate_clear_update_qty_exceeds_challan_qty(self):
        # Try to set clear1 to 46. Total if updated = 46 (new) + 5 (others) = 51. Challan Qty is 50.
        with self.assertRaisesMessage(ValidationError, "Total cleared quantity (51.000) exceeds challan quantity (50.000)."):
            self.clear1.validate_clear_update_qty(Decimal('46.000'))

    def test_update_cleared_qty(self):
        self.clear1.update_cleared_qty(Decimal('20.000'), MOCK_USERNAME, MOCK_COMP_ID, MOCK_FY_ID)
        self.clear1.refresh_from_db()
        self.assertEqual(self.clear1.cleared_qty, Decimal('20.000'))

    def test_update_cleared_qty_invalid(self):
        with self.assertRaises(ValidationError):
            self.clear1.update_cleared_qty(Decimal('60.000'), MOCK_USERNAME, MOCK_COMP_ID, MOCK_FY_ID) # Will fail validation


class QuantityFieldTest(TestCase):
    def test_valid_quantity_formats(self):
        field = forms.QuantityField()
        self.assertEqual(field.clean("123"), Decimal('123'))
        self.assertEqual(field.clean("123.456"), Decimal('123.456'))
        self.assertEqual(field.clean("0.123"), Decimal('0.123'))
        self.assertEqual(field.clean("123456789012345"), Decimal('123456789012345'))
        self.assertEqual(field.clean("123456789012345.123"), Decimal('123456789012345.123'))

    def test_invalid_quantity_formats(self):
        field = forms.QuantityField()
        with self.assertRaisesMessage(ValidationError, "Invalid quantity format."):
            field.clean("123.4567")
        with self.assertRaisesMessage(ValidationError, "Invalid quantity format."):
            field.clean("abc")
        with self.assertRaisesMessage(ValidationError, "Invalid quantity format."):
            field.clean("1234567890123456") # More than 15 digits before decimal

    def test_negative_quantity(self):
        field = forms.QuantityField()
        with self.assertRaisesMessage(ValidationError, "Quantity cannot be negative."):
            field.clean("-10.00")

    def test_required_field(self):
        field = forms.QuantityField(required=True)
        with self.assertRaisesMessage(ValidationError, "This field is required."):
            field.clean("")
        with self.assertRaisesMessage(ValidationError, "This field is required."):
            field.clean(None)

    def test_not_required_field(self):
        field = forms.QuantityField(required=False)
        self.assertIsNone(field.clean(""))
        self.assertIsNone(field.clean(None))


@mock_get_session_context_patch # Apply the mock to all view tests
class SupplierChallanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create master and detail data for tests
        cls.master = SupplierChallanMaster.objects.create(
            id=1, remarks="Master Remarks", vehicle_no="XYZ123", transporter="Transporter A",
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FY_ID, session_id=MOCK_USERNAME
        )
        cls.detail1 = SupplierChallanDetail.objects.create(
            id=101, master=cls.master, prd_id=1, challan_qty=Decimal('10.000'), pr_qty=Decimal('100.000'),
            item_code="ITEM001", description="Test Item 1", uom="PCS", sc_no="SC001", sc_date="2023-01-01"
        )
        cls.detail2 = SupplierChallanDetail.objects.create(
            id=102, master=cls.master, prd_id=2, challan_qty=Decimal('20.000'), pr_qty=Decimal('50.000'),
            item_code="ITEM002", description="Test Item 2", uom="KG", sc_no="SC001", sc_date="2023-01-01"
        )
        cls.clear1 = SupplierChallanClear.objects.create(
            id=201, challan_detail=cls.detail1, cleared_qty=Decimal('5.000'),
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FY_ID, session_id=MOCK_USERNAME
        )
        cls.clear2 = SupplierChallanClear.objects.create(
            id=202, challan_detail=cls.detail2, cleared_qty=Decimal('10.000'),
            company_id=MOCK_COMP_ID, financial_year_id=MOCK_FY_ID, session_id=MOCK_USERNAME
        )

    def setUp(self):
        self.client = Client()
        # Simulate a logged-in user
        self.user = MagicMock()
        self.user.is_authenticated = True
        self.user.username = MOCK_USERNAME
        self.client.force_login(self.user)

    def test_supplier_challan_edit_view_get(self):
        url = reverse('inventory:supplier_challan_edit', kwargs={'pk': self.master.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier_challan/supplier_challan_edit.html')
        self.assertContains(response, 'Edit Supplier Challan - ID: 1')
        self.assertIsInstance(response.context['form'], SupplierChallanMasterForm)
        self.assertEqual(response.context['form'].instance.pk, self.master.pk)

    def test_supplier_challan_edit_view_post_valid(self):
        url = reverse('inventory:supplier_challan_edit', kwargs={'pk': self.master.pk})
        data = {
            'vehicle_no': 'NEWVEH',
            'transporter': 'NEWTRANS',
            'remarks': 'NEW REMARKS',
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX request returns partial
        self.master.refresh_from_db()
        self.assertEqual(self.master.vehicle_no, 'NEWVEH')
        self.assertContains(response, 'NEWVEH') # Check if new value is in re-rendered form

    def test_supplier_challan_edit_view_post_invalid(self):
        url = reverse('inventory:supplier_challan_edit', kwargs={'pk': self.master.pk})
        data = {
            'vehicle_no': '', # Invalid, assuming required
            'transporter': 'NEWTRANS',
            'remarks': 'NEW REMARKS',
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # HTMX request, indicates error
        self.assertContains(response, 'This field is required') # Check for form error message

    def test_supplier_challan_details_table_partial_get(self):
        url = reverse('inventory:supplier_challan_details_table_partial', kwargs={'pk': self.master.pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier_challan/_supplier_challan_details_table.html')
        self.assertContains(response, 'ITEM001') # Check for detail item
        self.assertContains(response, self.detail1.challan_qty.normalize())

    def test_supplier_challan_details_table_partial_post_valid(self):
        url = reverse('inventory:supplier_challan_details_table_partial', kwargs={'pk': self.master.pk})
        
        # Prepare formset data for updating detail1
        data = {
            'form-TOTAL_FORMS': '2',
            'form-INITIAL_FORMS': '2',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
            
            f'form-0-id': str(self.detail1.pk),
            f'form-0-select_item': 'on', # Checkbox selected
            f'form-0-challan_qty': '12.000', # New valid quantity
            
            f'form-1-id': str(self.detail2.pk),
            f'form-1-select_item': 'off', # Not selected, should not update
            f'form-1-challan_qty': str(self.detail2.challan_qty.normalize()), # Original value
        }
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshChallanDetails')

        self.detail1.refresh_from_db()
        self.assertEqual(self.detail1.challan_qty, Decimal('12.000')) # Check updated quantity
        self.detail2.refresh_from_db()
        self.assertEqual(self.detail2.challan_qty, Decimal('20.000')) # Should not be updated

    def test_supplier_challan_details_table_partial_post_invalid_qty(self):
        url = reverse('inventory:supplier_challan_details_table_partial', kwargs={'pk': self.master.pk})
        
        # Try to over-challan detail2 (PRQty=50, original challan=20)
        # Remaining PR qty for detail2: 50 - 0 (others) = 50. Max for detail2 = 50.
        # Try to set 60 (invalid)
        data = {
            'form-TOTAL_FORMS': '2',
            'form-INITIAL_FORMS': '2',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
            
            f'form-0-id': str(self.detail1.pk),
            f'form-0-select_item': 'off',
            f'form-0-challan_qty': str(self.detail1.challan_qty.normalize()),
            
            f'form-1-id': str(self.detail2.pk),
            f'form-1-select_item': 'on',
            f'form-1-challan_qty': '60.000', # Invalid quantity
        }
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Should return 400 with errors
        self.assertContains(response, 'Input quantity (60.000) is more than remaining (50.000).')
        self.detail2.refresh_from_db()
        self.assertEqual(self.detail2.challan_qty, Decimal('20.000')) # Should not be updated

    def test_supplier_challan_clear_table_partial_get(self):
        url = reverse('inventory:supplier_challan_clear_table_partial', kwargs={'pk': self.master.pk})
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplier_challan/_supplier_challan_clear_table.html')
        self.assertContains(response, 'ITEM001') # Check for cleared item via detail
        self.assertContains(response, self.clear1.cleared_qty.normalize())

    def test_supplier_challan_clear_table_partial_post_valid(self):
        url = reverse('inventory:supplier_challan_clear_table_partial', kwargs={'pk': self.master.pk})
        
        # Update clear1 (original 5.000) to 7.000
        data = {
            f'clear-{self.clear1.pk}-id': str(self.clear1.pk),
            f'clear-{self.clear1.pk}-select_item': 'on',
            f'clear-{self.clear1.pk}-cleared_qty': '7.000',
            
            f'clear-{self.clear2.pk}-id': str(self.clear2.pk),
            f'clear-{self.clear2.pk}-select_item': 'off',
            f'clear-{self.clear2.pk}-cleared_qty': str(self.clear2.cleared_qty.normalize()),
        }
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshChallanClears')

        self.clear1.refresh_from_db()
        self.assertEqual(self.clear1.cleared_qty, Decimal('7.000'))
        self.clear2.refresh_from_db()
        self.assertEqual(self.clear2.cleared_qty, Decimal('10.000'))

    def test_supplier_challan_clear_table_partial_post_invalid_qty(self):
        url = reverse('inventory:supplier_challan_clear_table_partial', kwargs={'pk': self.master.pk})
        
        # detail2.challan_qty = 20. clear2.cleared_qty = 10.
        # Try to set clear2 to 15. Total if updated = 15 + 0 (others) = 15. Valid.
        # Try to set clear2 to 25. Total if updated = 25 + 0 (others) = 25. Invalid (exceeds 20).
        data = {
            f'clear-{self.clear1.pk}-id': str(self.clear1.pk),
            f'clear-{self.clear1.pk}-select_item': 'off',
            f'clear-{self.clear1.pk}-cleared_qty': str(self.clear1.cleared_qty.normalize()),
            
            f'clear-{self.clear2.pk}-id': str(self.clear2.pk),
            f'clear-{self.clear2.pk}-select_item': 'on',
            f'clear-{self.clear2.pk}-cleared_qty': '25.000', # Invalid quantity
        }
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Total cleared quantity (25.000) exceeds challan quantity (20.000).')
        self.clear2.refresh_from_db()
        self.assertEqual(self.clear2.cleared_qty, Decimal('10.000')) # Should not be updated

```

### Step 5: HTMX and Alpine.js Integration

**Business Understanding:** HTMX and Alpine.js are chosen to create a modern, responsive user interface without relying on complex JavaScript frameworks. HTMX handles all server-side interactions via partial HTML updates, while Alpine.js manages minor client-side state and interactivity, such as enabling/disabling fields.

**Instructions:**
The generated templates and views already include the necessary HTMX attributes (`hx-get`, `hx-post`, `hx-swap`, `hx-target`, `hx-trigger`, `hx-indicator`, `hx-redirect`) and Alpine.js directives (`x-data`, `x-bind`, `@click`).

-   **Dynamic Tab Content:** The main `supplier_challan_edit.html` uses HTMX `hx-get` on tab clicks to load `_supplier_challan_details_table.html` and `_supplier_challan_clear_table.html` into the `#tab-content` div. Alpine.js manages the visual active state of the tabs.
-   **Master Form Updates:** The `_master_form_partial.html` is loaded via HTMX `hx-get` on initial page load and for subsequent refreshes. Form submissions use `hx-post` to update the master record, re-rendering just that form section (`hx-target="outerHTML"` on `#master-form-container`).
-   **DataTables Integration:** Both `_supplier_challan_details_table.html` and `_supplier_challan_clear_table.html` contain HTML tables. The Django template includes a `<script>` block with `$(document).ready()` to initialize DataTables. This script is re-executed each time the partial is loaded by HTMX. `destroy: true` is used to ensure re-initialization works correctly.
-   **Checkbox-driven Input Enabling:** In both table partials, each row with an editable quantity field (`challan_qty` or `cleared_qty`) is wrapped in an `x-data` Alpine.js context. The checkbox (`select_item`) updates an Alpine.js `itemChecked` variable, which then dynamically controls the `disabled` attribute of the quantity `TextBox` via `x-bind:disabled="!itemChecked"`. This replaces the ASP.NET `AutoPostBack` functionality with a client-side solution.
-   **Form Submission for Grids:** The "Update" buttons within the table partials trigger HTMX `hx-post` requests. On successful update (Django view returns `HttpResponse(status=204)`), a `HX-Trigger` header (`refreshChallanDetails` or `refreshChallanClears`) is sent. This event is listened for globally (in `supplier_challan_edit.html`), which then triggers an `hx-get` on the relevant table container to reload and re-render the DataTables, reflecting the latest data.
-   **Messages Handling:** Django's `messages` framework is used. A global HTMX listener attempts to refresh a designated `messages` div after relevant HTMX requests, ensuring user feedback is visible.

---

## Final Notes

This modernization plan provides a comprehensive and automated approach to transforming the ASP.NET application to a modern Django solution.

-   **Placeholders:** Replace `[APP_NAME]`, `[MODEL_NAME]`, `[TABLE_NAME]`, `[FIELD1]`, etc., with actual values derived from your ASP.NET project. For instance, `[APP_NAME]` is assumed to be `inventory`.
-   **Database Connection:** Ensure your Django `settings.py` is configured to connect to the existing SQL Server database, and that the `id` fields in your models are correctly mapped to identity columns in SQL Server (or Django will try to manage them).
-   **Security:** This plan assumes a basic `LoginRequiredMixin`. In a production environment, implement robust authentication and authorization (e.g., Django's built-in `User` model, custom permission classes, row-level security if needed).
-   **Error Handling:** While basic error messages are included, consider more sophisticated user feedback mechanisms and server-side logging for production readiness.
-   **Custom Logic:** The `get_remaining_pr_qty` and `get_total_challaned_qty_for_pr_item` in `SupplierChallanDetail` and related methods in `SupplierChallanClear` are modeled based on the inferred ASP.NET logic. Review these carefully against the actual stored procedures (`GetSupplier_PR_ChQty`, `View_Sup_PR_Challan_Calc`) and data relationships to ensure exact replication of business rules. Complex database-side aggregations might require custom managers or optimized ORM queries.
-   **Front-end Libraries:** Ensure DataTables, HTMX, and Alpine.js are properly included in your `core/base.html` template (e.g., via CDN links or local static files).
-   **UI/UX:** The Tailwind CSS styling provides a clean, modern look. Further customization may be needed to match specific branding or design requirements.
-   **Scalability:** Django, with its ORM and robust architecture, provides excellent scalability. HTMX and Alpine.js keep the frontend lightweight and efficient.