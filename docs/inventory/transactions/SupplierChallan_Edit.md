## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, especially the `SearchGridView1` columns (`FinYear`, `SCNo`, `SupplierName`, `SupplierId`, `Id`) and the `sql` web method (accessing `tblMM_Supplier_master` for `SupplierId`, `SupplierName`), we can infer the following database tables and their columns:

**Primary Table for Listing (Inferred):** `tblInv_Supplier_Challan_Master`
*   `Id` (Primary Key, integer)
*   `FinYear` (Varchar/string)
*   `SCNo` (Varchar/string)
*   `SupplierId` (Varchar/string, Foreign Key)
*   `CompId` (integer)
*   `FinId` (Varchar/string)

**Related Table (Explicitly mentioned for autocomplete):** `tblMM_Supplier_master`
*   `SupplierId` (Primary Key, Varchar/string)
*   `SupplierName` (Varchar/string)
*   `CompId` (integer)

### Step 2: Identify Backend Functionality

The ASP.NET page `SupplierChallan_Edit.aspx` primarily serves as a list and search interface.

*   **Read (List):** The `BindData` method, utilizing the `GetSupChallan` stored procedure, fetches a list of supplier challans. This includes `FinYear`, `SCNo`, `SupplierName`, and `SupplierId`. The list is paginated and sortable by the `GridView`.
*   **Search/Filter:** The `Search_Click` event triggers `BindData` with a `SupplierId` obtained from `TxtSearchValue` via `fun.getCode()`. This allows filtering the list by supplier.
*   **Autocomplete:** The `sql` web method provides auto-completion suggestions for `SupplierName` based on `tblMM_Supplier_master`, returning `SupplierName [SupplierId]`.
*   **Navigation:** The `SCNo` column is a hyperlink (`HyperLinkField`) to `SupplierChallan_Edit_Details.aspx?Id={0}`, indicating a separate page for detailed view or editing of a specific challan.

*Note: This specific ASP.NET page does not contain direct Create, Update, or Delete functionality for `SupplierChallan` records. It's a search/list page that links to an edit details page. However, per the requirements, the Django plan will include full CRUD views and templates to demonstrate a complete modernization.*

### Step 3: Infer UI Components

The ASP.NET controls translate to Django concepts as follows:

*   **`TxtSearchValue` (TextBox with `AutoCompleteExtender`):** A standard HTML `input type="text"` element in Django, enhanced with HTMX for `hx-get` to an autocomplete endpoint and Alpine.js for managing suggestions.
*   **`Search` (Button):** A standard HTML `button` in Django, which will trigger an HTMX request (`hx-get`) to refresh the main data table.
*   **`SearchGridView1` (GridView):** This will be replaced by an HTML `<table>` in Django, dynamically populated and managed by DataTables. HTMX will be used to load this table as a partial view.
*   **`HyperLinkField` for `SCNo`:** A standard HTML `<a>` tag in Django, linking to the specific challan's edit page using Django's URL routing.

---

### Step 4: Generate Django Code

We will create a new Django application named `inventory` to house this module.

#### 4.1 Models (`inventory/models.py`)

We'll define two models, `SupplierMaster` and `SupplierChallan`, reflecting the database schema. The business logic for querying and search will be placed as methods within these models (Fat Model approach).

```python
from django.db import models
from django.db.models import F

class SupplierMaster(models.Model):
    """
    Represents the tblMM_Supplier_master table.
    Used for supplier information and autocomplete functionality.
    """
    supplierid = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    suppliername = models.CharField(db_column='SupplierName', max_length=255)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return f"{self.suppliername} [{self.supplierid}]"

    @classmethod
    def autocomplete_search(cls, prefix_text: str, comp_id: int):
        """
        Mimics the 'sql' web method for supplier autocomplete.
        Filters suppliers by name starting with prefix_text for a given company.
        """
        return cls.objects.filter(
            compid=comp_id,
            suppliername__istartswith=prefix_text
        ).order_by('suppliername')[:10] # Limit to 10 results as in ASP.NET

    @classmethod
    def get_supplier_id_from_formatted_string(cls, formatted_string: str):
        """
        Parses a string like 'SupplierName [SupplierId]' and returns SupplierId.
        If no ID in brackets, attempts to return the string itself as a potential SupplierId or Name.
        """
        import re
        match = re.search(r'\[(.*?)\]$', formatted_string)
        if match:
            return match.group(1)
        return formatted_string # Return as-is if no bracketed ID

class SupplierChallan(models.Model):
    """
    Represents the tblInv_Supplier_Challan_Master table.
    This is the main table for the list view.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=10)
    scno = models.CharField(db_column='SCNo', max_length=50)
    # ForeignKey to SupplierMaster, linking via SupplierId
    # on_delete=models.DO_NOTHING is used because managed=False tables often have
    # foreign keys not managed by Django's ORM.
    supplier = models.ForeignKey(
        SupplierMaster,
        db_column='SupplierId',
        on_delete=models.DO_NOTHING,
        to_field='supplierid', # Specifies which field in SupplierMaster to link to
        related_name='challans'
    )
    compid = models.IntegerField(db_column='CompId')
    finid = models.CharField(db_column='FinId', max_length=10)

    class Meta:
        managed = False
        db_table = 'tblInv_Supplier_Challan_Master'
        verbose_name = 'Supplier Challan'
        verbose_name_plural = 'Supplier Challans'
        # Default ordering for consistency
        ordering = ['-id']

    def __str__(self):
        return f"{self.scno} ({self.finyear})"

    @classmethod
    def get_filtered_challans(cls, comp_id: int, fin_id: str, search_value: str = None):
        """
        Encapsulates the 'BindData' logic and 'GetSupChallan' stored procedure.
        Filters challans by Company ID, Financial Year ID, and optionally Supplier ID/Name.
        """
        queryset = cls.objects.select_related('supplier').filter(
            compid=comp_id,
            finid=fin_id
        )

        if search_value:
            # Try to extract SupplierId first, then try to match by name
            supplier_id_candidate = SupplierMaster.get_supplier_id_from_formatted_string(search_value)
            
            # Check if it's an existing SupplierId
            if SupplierMaster.objects.filter(supplierid=supplier_id_candidate, compid=comp_id).exists():
                queryset = queryset.filter(supplier__supplierid=supplier_id_candidate)
            else:
                # If not an exact ID, try to match by SupplierName (case-insensitive)
                supplier_obj_by_name = SupplierMaster.objects.filter(
                    suppliername__iexact=supplier_id_candidate,
                    compid=comp_id
                ).first()
                if supplier_obj_by_name:
                    queryset = queryset.filter(supplier__supplierid=supplier_obj_by_name.supplierid)
                else:
                    # If no matching supplier found by ID or exact name, return empty set
                    queryset = cls.objects.none()
        
        return queryset
```

#### 4.2 Forms (`inventory/forms.py`)

A form for the `SupplierChallan` model, which will be used for Create/Update operations.

```python
from django import forms
from .models import SupplierChallan, SupplierMaster

class SupplierChallanForm(forms.ModelForm):
    """
    Form for creating and updating SupplierChallan objects.
    """
    class Meta:
        model = SupplierChallan
        # Fields for a typical CRUD form. Adjust based on what fields are editable.
        fields = ['finyear', 'scno', 'supplier', 'compid', 'finid']
        widgets = {
            'finyear': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'scno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finid': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'finyear': 'Financial Year',
            'scno': 'Challan Number',
            'supplier': 'Supplier',
            'compid': 'Company ID',
            'finid': 'Financial Year ID',
        }

    # Custom validation example (if needed, based on ASP.NET validation)
    def clean_scno(self):
        scno = self.cleaned_data['scno']
        # Example: Ensure SCNo is unique for the given company and financial year
        # This would require more context, but demonstrates the concept
        return scno

```

#### 4.3 Views (`inventory/views.py`)

We'll define multiple CBVs for listing, creating, updating, deleting, and an HTMX-specific view for the data table and autocomplete. Views will remain thin, delegating business logic to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from .models import SupplierChallan, SupplierMaster
from .forms import SupplierChallanForm

# Helper to get session data, assuming these are set after login
def get_session_context(request):
    """Retrieves compid and finyear from session with defaults."""
    # In a real application, these might come from user profiles or specific contexts
    comp_id = request.session.get('compid', 1)  # Default for demonstration
    fin_id = request.session.get('finyear', '2023-2024') # Default for demonstration
    return comp_id, fin_id

class SupplierChallanListView(ListView):
    """
    Main view for displaying the Supplier Challan list page.
    Renders the initial page layout with search form and container for the HTMX-loaded table.
    """
    model = SupplierChallan
    template_name = 'inventory/supplierchallan/list.html'
    context_object_name = 'supplierchallans' # This context is not used directly, table loaded by HTMX

    # This get_queryset is mostly for initial page load if table content is part of initial render
    # However, since the table is loaded by HTMX, it's less critical here.
    def get_queryset(self):
        comp_id, fin_id = get_session_context(self.request)
        # Initially, load all challans (or the first page's worth)
        return SupplierChallan.get_filtered_challans(comp_id, fin_id)


class SupplierChallanTablePartialView(ListView):
    """
    HTMX-specific view to load and refresh the Supplier Challan data table.
    Handles search filtering from the request parameters.
    """
    model = SupplierChallan
    template_name = 'inventory/supplierchallan/_supplierchallan_table.html'
    context_object_name = 'supplierchallans' # Context name for the partial template

    def get_queryset(self):
        comp_id, fin_id = get_session_context(self.request)
        search_value = self.request.GET.get('search_value', '').strip()
        
        # Delegate filtering logic to the model
        return SupplierChallan.get_filtered_challans(comp_id, fin_id, search_value)

class SupplierChallanCreateView(CreateView):
    """
    Handles creation of new Supplier Challan records via a modal form.
    """
    model = SupplierChallan
    form_class = SupplierChallanForm
    template_name = 'inventory/supplierchallan/_supplierchallan_form.html' # Partial template for modal
    success_url = reverse_lazy('inventory:supplierchallan_list') # Not directly used for HTMX 204

    def form_valid(self, form):
        # Set default values for compid and finid if not in form
        comp_id, fin_id = get_session_context(self.request)
        if not form.instance.compid:
            form.instance.compid = comp_id
        if not form.instance.finid:
            form.instance.finid = fin_id

        # Save the instance
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier Challan added successfully.')

        # HTMX response for success, triggering a refresh of the list
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshSupplierChallanList' # Custom event to refresh table
                }
            )
        return response # Fallback for non-HTMX requests

class SupplierChallanUpdateView(UpdateView):
    """
    Handles updating existing Supplier Challan records via a modal form.
    """
    model = SupplierChallan
    form_class = SupplierChallanForm
    template_name = 'inventory/supplierchallan/_supplierchallan_form.html' # Partial template for modal
    success_url = reverse_lazy('inventory:supplierchallan_list') # Not directly used for HTMX 204

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier Challan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierChallanList'
                }
            )
        return response

class SupplierChallanDeleteView(DeleteView):
    """
    Handles deletion of Supplier Challan records via a confirmation modal.
    """
    model = SupplierChallan
    template_name = 'inventory/supplierchallan/_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('inventory:supplierchallan_list') # Not directly used for HTMX 204

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier Challan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierChallanList'
                }
            )
        return response

class SupplierAutocompleteView(View):
    """
    Provides JSON responses for supplier name autocomplete functionality.
    Mimics the ASP.NET 'sql' web method.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '').strip()
        comp_id, _ = get_session_context(request) # Only comp_id is relevant here

        suppliers = SupplierMaster.autocomplete_search(query, comp_id)

        results = []
        for supplier in suppliers:
            results.append({
                'id': supplier.supplierid,
                'text': str(supplier) # Formatted as "SupplierName [SupplierId]"
            })
        return JsonResponse({'results': results})

```

#### 4.4 Templates (`inventory/templates/inventory/supplierchallan/`)

These templates will fully utilize HTMX and Alpine.js for dynamic interactions and DataTables for list presentation. They extend a conceptual `core/base.html` that contains all necessary CDN links (jQuery, DataTables, HTMX, Alpine.js, Tailwind CSS).

**`list.html`** (Main list page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Supplier Challans - Edit List</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300"
            hx-get="{% url 'inventory:supplierchallan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Challan
        </button>
    </div>

    <!-- Search Form Area -->
    <div class="mb-6 bg-white p-6 rounded-lg shadow-md">
        <form hx-get="{% url 'inventory:supplierchallan_table' %}"
              hx-target="#supplierchallanTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit">
            <div class="flex items-end space-x-4">
                <div class="relative flex-grow">
                    <label for="id_search_value" class="block text-sm font-medium text-gray-700 mb-1">Supplier Name / ID</label>
                    <input type="text" id="id_search_value" name="search_value"
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                           placeholder="Enter Supplier Name or ID"
                           hx-get="{% url 'inventory:supplier_autocomplete' %}"
                           hx-target="#autocomplete-suggestions"
                           hx-vals="js:{q: event.target.value}"
                           hx-trigger="keyup changed delay:300ms, search"
                           autocomplete="off"
                           x-data="{ value: '' }"
                           @input="value = $event.target.value">

                    <div id="autocomplete-suggestions"
                         class="absolute bg-white border border-gray-300 w-full max-h-60 overflow-y-auto z-10 rounded-md shadow-lg mt-1 hidden"
                         x-data="{ selectedIndex: -1 }"
                         @click.away="selectedIndex = -1; $el.classList.add('hidden')">
                        <!-- Autocomplete suggestions will be loaded here via HTMX -->
                    </div>
                </div>
                <button type="submit"
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300">
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Container for the DataTables content, loaded via HTMX -->
    <div id="supplierchallanTable-container"
         hx-trigger="load, refreshSupplierChallanList from:body"
         hx-get="{% url 'inventory:supplierchallan_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md p-6">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Challan Data...</p>
        </div>
    </div>

    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js and HTMX integration for autocomplete suggestions
    document.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.target.id === 'autocomplete-suggestions') {
            const suggestionsDiv = evt.detail.target;
            const inputField = document.getElementById('id_search_value');
            if (suggestionsDiv.children.length > 0 && inputField.value.trim() !== '') {
                suggestionsDiv.classList.remove('hidden');
            } else {
                suggestionsDiv.classList.add('hidden');
            }
        }
    });

    // Function to select a suggestion and populate the input field
    function selectSuggestion(element) {
        const text = element.dataset.text;
        document.getElementById('id_search_value').value = text;
        document.getElementById('autocomplete-suggestions').classList.add('hidden');
    }
</script>
{% endblock %}

```

**`_supplierchallan_table.html`** (Partial for DataTables list)

```html
{% load django_tables2 crispy_forms_tags %} {# crispy_forms_tags might be used for future advanced forms #}

<table id="supplierchallanTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fin Year</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SC No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Supplier Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Supplier Code</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for challan in supplierchallans %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-center">{{ challan.finyear }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-blue-600 hover:underline cursor-pointer text-center">
                {# Link to the edit details page, similar to original ASP.NET HyperLinkField #}
                <a href="{% url 'inventory:supplierchallan_edit' challan.pk %}"
                   hx-get="{% url 'inventory:supplierchallan_edit' challan.pk %}"
                   hx-target="#modalContent"
                   hx-trigger="click"
                   _="on click add .is-active to #modal">
                    {{ challan.scno }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ challan.supplier.suppliername }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-center">{{ challan.supplier.supplierid }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-center">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-xs transition duration-300"
                    hx-get="{% url 'inventory:supplierchallan_edit' challan.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-300"
                    hx-get="{% url 'inventory:supplierchallan_delete' challan.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500 text-base">
                No data to display!
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after HTMX swap
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#supplierchallanTable')) {
            $('#supplierchallanTable').DataTable().destroy();
        }
        $('#supplierchallanTable').DataTable({
            "pageLength": 20, // Page size as in ASP.NET GridView
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            "pagingType": "simple_numbers", // or "full_numbers"
        });
    });
</script>
```

**`_autocomplete_suggestions.html`** (Partial for autocomplete dropdown)

```html
{% for supplier in results %}
    <div class="py-2 px-4 hover:bg-gray-100 cursor-pointer text-sm text-gray-800"
         data-id="{{ supplier.id }}"
         data-text="{{ supplier.text }}"
         onclick="selectSuggestion(this)">
        {{ supplier.text }}
    </div>
{% empty %}
    <div class="py-2 px-4 text-gray-500 text-sm">No matches found.</div>
{% endfor %}
```

**`_supplierchallan_form.html`** (Partial for Create/Update modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Supplier Challan
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loading-indicator">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300">
                Save
                <span id="loading-indicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

**`_confirm_delete.html`** (Partial for Delete confirmation modal)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4 border-b pb-2">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete Supplier Challan <strong class="font-medium">{{ object.scno }}</strong>?
        This action cannot be undone.
    </p>

    <div class="flex justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-post="{% url 'inventory:supplierchallan_delete' object.pk %}"
            hx-swap="none"
            hx-trigger="click"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

Define URL patterns for all views.

```python
from django.urls import path
from .views import (
    SupplierChallanListView,
    SupplierChallanTablePartialView,
    SupplierChallanCreateView,
    SupplierChallanUpdateView,
    SupplierChallanDeleteView,
    SupplierAutocompleteView
)

app_name = 'inventory' # Define app namespace for URL lookups

urlpatterns = [
    # Main list page
    path('supplierchallan/', SupplierChallanListView.as_view(), name='supplierchallan_list'),

    # HTMX-specific endpoint for the table content
    path('supplierchallan/table/', SupplierChallanTablePartialView.as_view(), name='supplierchallan_table'),

    # CRUD operations for Supplier Challan (via HTMX modals)
    path('supplierchallan/add/', SupplierChallanCreateView.as_view(), name='supplierchallan_add'),
    path('supplierchallan/edit/<int:pk>/', SupplierChallanUpdateView.as_view(), name='supplierchallan_edit'),
    path('supplierchallan/delete/<int:pk>/', SupplierChallanDeleteView.as_view(), name='supplierchallan_delete'),

    # HTMX-specific endpoint for supplier autocomplete
    path('supplier/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
]

```

#### 4.6 Tests (`inventory/tests/test_models.py`, `inventory/tests/test_views.py`)

Comprehensive tests for model methods and view functionality, including HTMX interactions.

**`inventory/tests/test_models.py`**

```python
from django.test import TestCase
from inventory.models import SupplierMaster, SupplierChallan
from django.db.utils import IntegrityError

class SupplierMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.comp_id = 101
        SupplierMaster.objects.create(
            supplierid='SUP001',
            suppliername='Test Supplier A',
            compid=cls.comp_id
        )
        SupplierMaster.objects.create(
            supplierid='SUP002',
            suppliername='Another Supplier B',
            compid=cls.comp_id
        )
        SupplierMaster.objects.create(
            supplierid='SUP003',
            suppliername='Supplier C',
            compid=cls.comp_id + 1 # Different company
        )

    def test_supplier_master_creation(self):
        supplier = SupplierMaster.objects.get(supplierid='SUP001')
        self.assertEqual(supplier.suppliername, 'Test Supplier A')
        self.assertEqual(supplier.compid, self.comp_id)

    def test_str_representation(self):
        supplier = SupplierMaster.objects.get(supplierid='SUP001')
        self.assertEqual(str(supplier), 'Test Supplier A [SUP001]')

    def test_autocomplete_search_by_prefix(self):
        # Test case-insensitivity
        results = SupplierMaster.autocomplete_search('test', self.comp_id)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].supplierid, 'SUP001')

        # Test multiple matches
        SupplierMaster.objects.create(supplierid='SUP004', suppliername='Test Supplier B', compid=self.comp_id)
        results = SupplierMaster.autocomplete_search('test', self.comp_id)
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0].supplierid, 'SUP001') # Ordered by name

    def test_autocomplete_search_no_match(self):
        results = SupplierMaster.autocomplete_search('xyz', self.comp_id)
        self.assertEqual(len(results), 0)

    def test_autocomplete_search_different_company(self):
        results = SupplierMaster.autocomplete_search('Supplier', self.comp_id + 1)
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].supplierid, 'SUP003')

    def test_get_supplier_id_from_formatted_string(self):
        self.assertEqual(SupplierMaster.get_supplier_id_from_formatted_string('Supplier Name [ABC]'), 'ABC')
        self.assertEqual(SupplierMaster.get_supplier_id_from_formatted_string('Just Name'), 'Just Name')
        self.assertEqual(SupplierMaster.get_supplier_id_from_formatted_string('ID_ONLY'), 'ID_ONLY')
        self.assertEqual(SupplierMaster.get_supplier_id_from_formatted_string(''), '')


class SupplierChallanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data
        cls.comp_id = 101
        cls.fin_id = '2023-2024'
        cls.supplier1 = SupplierMaster.objects.create(
            supplierid='SUP001', suppliername='Test Supplier A', compid=cls.comp_id
        )
        cls.supplier2 = SupplierMaster.objects.create(
            supplierid='SUP002', suppliername='Another Supplier B', compid=cls.comp_id
        )
        cls.supplier_diff_comp = SupplierMaster.objects.create(
            supplierid='SUP003', suppliername='Supplier C', compid=cls.comp_id + 1
        )

        SupplierChallan.objects.create(
            id=1, finyear=cls.fin_id, scno='SC-001', supplier=cls.supplier1, compid=cls.comp_id, finid=cls.fin_id
        )
        SupplierChallan.objects.create(
            id=2, finyear=cls.fin_id, scno='SC-002', supplier=cls.supplier2, compid=cls.comp_id, finid=cls.fin_id
        )
        SupplierChallan.objects.create(
            id=3, finyear='2022-2023', scno='SC-003', supplier=cls.supplier1, compid=cls.comp_id, finid='2022-2023'
        )
        SupplierChallan.objects.create(
            id=4, finyear=cls.fin_id, scno='SC-004', supplier=cls.supplier_diff_comp, compid=cls.comp_id + 1, finid=cls.fin_id
        )

    def test_supplier_challan_creation(self):
        challan = SupplierChallan.objects.get(id=1)
        self.assertEqual(challan.scno, 'SC-001')
        self.assertEqual(challan.supplier.suppliername, 'Test Supplier A')
        self.assertEqual(challan.compid, self.comp_id)

    def test_str_representation(self):
        challan = SupplierChallan.objects.get(id=1)
        self.assertEqual(str(challan), 'SC-001 (2023-2024)')

    def test_get_filtered_challans_no_search(self):
        challans = SupplierChallan.get_filtered_challans(self.comp_id, self.fin_id)
        self.assertEqual(challans.count(), 2) # SC-001, SC-002

    def test_get_filtered_challans_by_supplier_id(self):
        challans = SupplierChallan.get_filtered_challans(self.comp_id, self.fin_id, search_value='SUP001')
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().scno, 'SC-001')

    def test_get_filtered_challans_by_supplier_name_exact(self):
        challans = SupplierChallan.get_filtered_challans(self.comp_id, self.fin_id, search_value='Test Supplier A')
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().scno, 'SC-001')
        
    def test_get_filtered_challans_by_formatted_string(self):
        challans = SupplierChallan.get_filtered_challans(self.comp_id, self.fin_id, search_value='Another Supplier B [SUP002]')
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().scno, 'SC-002')

    def test_get_filtered_challans_no_match(self):
        challans = SupplierChallan.get_filtered_challans(self.comp_id, self.fin_id, search_value='NonExistentSupplier')
        self.assertEqual(challans.count(), 0)

    def test_get_filtered_challans_different_financial_year(self):
        challans = SupplierChallan.get_filtered_challans(self.comp_id, '2022-2023')
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().scno, 'SC-003')

    def test_get_filtered_challans_different_company(self):
        challans = SupplierChallan.get_filtered_challans(self.comp_id + 1, self.fin_id)
        self.assertEqual(challans.count(), 1)
        self.assertEqual(challans.first().scno, 'SC-004')
```

**`inventory/tests/test_views.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from inventory.models import SupplierChallan, SupplierMaster
from inventory.forms import SupplierChallanForm
from unittest.mock import patch

class SupplierChallanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.comp_id = 101
        cls.fin_id = '2023-2024'
        cls.supplier1 = SupplierMaster.objects.create(
            supplierid='SUP001', suppliername='Test Supplier A', compid=cls.comp_id
        )
        cls.supplier2 = SupplierMaster.objects.create(
            supplierid='SUP002', suppliername='Another Supplier B', compid=cls.comp_id
        )
        cls.challan1 = SupplierChallan.objects.create(
            id=1, finyear=cls.fin_id, scno='SC-001', supplier=cls.supplier1, compid=cls.comp_id, finid=cls.fin_id
        )
        cls.challan2 = SupplierChallan.objects.create(
            id=2, finyear=cls.fin_id, scno='SC-002', supplier=cls.supplier2, compid=cls.comp_id, finid=cls.fin_id
        )
    
    def setUp(self):
        self.client = Client()
        # Mock session context for all requests
        patcher_compid = patch('inventory.views.get_session_context', return_value=(self.comp_id, self.fin_id))
        self.mock_get_session_context = patcher_compid.start()
        self.addCleanup(patcher_compid.stop)

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:supplierchallan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/list.html')
        # Check that context is minimal as table is loaded by HTMX
        self.assertIn('supplierchallans', response.context) # Should have initial queryset from ListView

    def test_table_partial_view_get_no_search(self):
        response = self.client.get(reverse('inventory:supplierchallan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplierchallan_table.html')
        self.assertIn('supplierchallans', response.context)
        self.assertEqual(response.context['supplierchallans'].count(), 2)

    def test_table_partial_view_get_with_supplier_id_search(self):
        response = self.client.get(reverse('inventory:supplierchallan_table'), {'search_value': 'SUP001'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['supplierchallans'].count(), 1)
        self.assertEqual(response.context['supplierchallans'].first().scno, 'SC-001')

    def test_table_partial_view_get_with_supplier_name_search(self):
        response = self.client.get(reverse('inventory:supplierchallan_table'), {'search_value': 'Test Supplier A'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['supplierchallans'].count(), 1)
        self.assertEqual(response.context['supplierchallans'].first().scno, 'SC-001')

    def test_create_view_get(self):
        response = self.client.get(reverse('inventory:supplierchallan_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplierchallan_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], SupplierChallanForm)

    def test_create_view_post_success(self):
        new_challan_data = {
            'finyear': '2024-2025',
            'scno': 'SC-003',
            'supplier': self.supplier1.supplierid,
            'compid': self.comp_id,
            'finid': '2024-2025',
        }
        response = self.client.post(reverse('inventory:supplierchallan_add'), new_challan_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierChallanList')
        self.assertTrue(SupplierChallan.objects.filter(scno='SC-003').exists())

    def test_create_view_post_invalid(self):
        invalid_challan_data = {
            'finyear': '', # Missing required field
            'scno': 'SC-004',
            'supplier': self.supplier1.supplierid,
            'compid': self.comp_id,
            'finid': self.fin_id,
        }
        response = self.client.post(reverse('inventory:supplierchallan_add'), invalid_challan_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplierchallan_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertFalse(SupplierChallan.objects.filter(scno='SC-004').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('inventory:supplierchallan_edit', args=[self.challan1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplierchallan_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.challan1)

    def test_update_view_post_success(self):
        updated_data = {
            'finyear': '2023-2024',
            'scno': 'SC-001-UPDATED',
            'supplier': self.supplier2.supplierid, # Change supplier
            'compid': self.comp_id,
            'finid': self.fin_id,
        }
        response = self.client.post(reverse('inventory:supplierchallan_edit', args=[self.challan1.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.challan1.refresh_from_db()
        self.assertEqual(self.challan1.scno, 'SC-001-UPDATED')
        self.assertEqual(self.challan1.supplier, self.supplier2)

    def test_delete_view_get(self):
        response = self.client.get(reverse('inventory:supplierchallan_delete', args=[self.challan1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.challan1)

    def test_delete_view_post_success(self):
        response = self.client.post(reverse('inventory:supplierchallan_delete', args=[self.challan1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(SupplierChallan.objects.filter(pk=self.challan1.pk).exists())

    def test_autocomplete_view(self):
        response = self.client.get(reverse('inventory:supplier_autocomplete'), {'q': 'test'})
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIn('results', json_response)
        self.assertEqual(len(json_response['results']), 1)
        self.assertEqual(json_response['results'][0]['text'], 'Test Supplier A [SUP001]')

    def test_autocomplete_view_no_query(self):
        response = self.client.get(reverse('inventory:supplier_autocomplete'))
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIn('results', json_response)
        self.assertEqual(len(json_response['results']), 2) # Both SUP001, SUP002 are in comp_id 101

    def test_autocomplete_view_no_results(self):
        response = self.client.get(reverse('inventory:supplier_autocomplete'), {'q': 'nonexistent'})
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIn('results', json_response)
        self.assertEqual(len(json_response['results']), 0)
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic updates:**
    *   The main `list.html` uses `hx-get` on `load` and a custom `refreshSupplierChallanList` event to populate the `supplierchallanTable-container` with content from `{% url 'inventory:supplierchallan_table' %}`.
    *   The search form submits via `hx-get` to the same `supplierchallan_table` URL, causing a refresh of the table with filtered data.
    *   Add/Edit/Delete buttons within `_supplierchallan_table.html` use `hx-get` to fetch their respective forms (`_supplierchallan_form.html` or `_confirm_delete.html`) into the `#modalContent` div.
    *   Form submissions (`hx-post`) in `_supplierchallan_form.html` and `_confirm_delete.html` return a `204 No Content` status with an `HX-Trigger: refreshSupplierChallanList` header, prompting the main list to refresh without a full page reload.
    *   The autocomplete input uses `hx-get` to `{% url 'inventory:supplier_autocomplete' %}` to fetch suggestions into `autocomplete-suggestions`.

*   **Alpine.js for UI state management:**
    *   Alpine.js's `x-data` is used on the modal container to manage its visibility (`.is-active` class) based on button clicks or `click.away` events to dismiss the modal.
    *   A JavaScript `selectSuggestion` function (wrapped for Alpine compatibility if needed) handles populating the search input from autocomplete results and hiding the suggestions.

*   **DataTables for list views:**
    *   The `_supplierchallan_table.html` partial directly initializes DataTables on the `supplierchallanTable` after it's loaded by HTMX. This ensures client-side sorting, searching, and pagination are handled efficiently.
    *   The `pageLength` and `lengthMenu` settings are set to match the original `GridView` configuration.

*   **DRY Template Inheritance:**
    *   All templates extend `core/base.html` (not included here) to ensure consistent layout, styling, and inclusion of necessary CDN links (Tailwind CSS, HTMX, Alpine.js, jQuery, DataTables).

### Final Notes

This comprehensive plan provides a systematic approach to migrating the ASP.NET `SupplierChallan_Edit` module to a modern Django application. By leveraging AI-assisted automation principles, the focus remains on clearly defined responsibilities for models and views, dynamic interactions with HTMX, intuitive UI/UX with Alpine.js and DataTables, and robust testing to ensure functionality and maintainability. This structure facilitates efficient development and reduces manual effort, aligning with the goal of automated modernization.