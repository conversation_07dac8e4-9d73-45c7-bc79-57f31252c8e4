## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Modernization Plan for Received Receipt Dashboard

The provided ASP.NET code for "RecievedReciept_Dashboard.aspx" and its code-behind file is currently a placeholder with no active functionality, database interactions, or user interface elements defined. This makes it an ideal candidate for a clean-slate modernization to Django, focusing on establishing the core data model and foundational CRUD (Create, Read, Update, Delete) operations that a dashboard of this type would typically require.

**Business Value of Modernization:**
Transitioning this empty ASP.NET page to a Django application brings immediate business value:
*   **Future-Proofing:** Moving away from legacy ASP.NET Web Forms to a modern, open-source framework like Django ensures your application stack remains current, maintainable, and secure.
*   **Scalability:** Django's robust architecture and ecosystem provide a solid foundation for future growth and increased user loads.
*   **Developer Productivity:** Django's "batteries included" approach, clear structure, and extensive documentation enable faster development and easier onboarding of new developers.
*   **Enhanced User Experience:** By adopting HTMX and Alpine.js, we create a highly responsive and interactive user interface without the complexity of traditional JavaScript frameworks, leading to a smoother and more efficient user experience.
*   **Simplified Maintenance:** A clean, well-structured Django application with strong separation of concerns (business logic in models, minimal view code, no HTML in Python) significantly reduces maintenance overhead and the risk of errors.

Our approach will treat this as a new module within your Django ERP system, likely under an `inventory` application, focusing on managing "Received Receipts." We will assume a basic structure for `ReceivedReceipt` data, as no schema was provided.

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the provided ASP.NET code is empty and contains no database-related elements, we must infer a plausible schema for a "Received Receipt" dashboard. For demonstration purposes, we will define a `ReceivedReceipt` table with the following assumed columns:

*   `id` (Primary Key, implicitly handled by Django)
*   `receipt_number` (Unique identifier for the receipt)
*   `receipt_date` (Date the receipt was received)
*   `amount` (Total amount of the receipt)
*   `supplier` (Name of the supplier)
*   `description` (Optional description of the receipt)

**Inferred `[TABLE_NAME]`:** `tbl_received_receipts` (or a similar naming convention in your existing database).

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Since the ASP.NET code has no implemented functionality, we will assume the core requirements for a dashboard managing "Received Receipts" are standard CRUD operations:

*   **Create:** Ability to add new received receipt records.
*   **Read:** Display a list of all received receipts, with capabilities for searching, sorting, and pagination (using DataTables).
*   **Update:** Ability to edit existing received receipt records.
*   **Delete:** Ability to remove received receipt records.

No validation logic was present, so we will implement basic form validation at the Django form level.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Based on the dashboard context and inferred functionality, we will design the Django UI to include:

*   A **List View** for all `ReceivedReceipt` records, presented as a table leveraging **DataTables** for interactive features (search, sort, paginate).
*   **Buttons** for "Add New Received Receipt," "Edit," and "Delete" actions.
*   **Modals** for adding, editing, and confirming deletion, loaded dynamically via **HTMX**.
*   **Forms** within these modals for user input, styled with **Tailwind CSS**.
*   **Alpine.js** will manage the modal's open/close state.

---

## Step 4: Generate Django Code

We will create a new Django application named `inventory` (or integrate into an existing `erp` or `modules` app). The following files will be created within this application.

### 4.1 Models (`inventory/models.py`)

**Task:** Create a Django model based on the database schema.

**Instructions:**
We'll define the `ReceivedReceipt` model, mapping to the assumed `tbl_received_receipts` table. This model will contain the fields identified in Step 1.

```python
from django.db import models

class ReceivedReceipt(models.Model):
    # Using 'pk' for primary key as it's automatically handled by Django
    receipt_number = models.CharField(
        max_length=100, 
        unique=True, 
        verbose_name="Receipt Number",
        db_column='receipt_no' # Assuming a different column name for clarity
    )
    receipt_date = models.DateField(
        verbose_name="Receipt Date",
        db_column='reciept_dt'
    )
    amount = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        verbose_name="Amount",
        db_column='amount_val'
    )
    supplier = models.CharField(
        max_length=200, 
        verbose_name="Supplier",
        db_column='supplier_nm'
    )
    description = models.TextField(
        blank=True, 
        null=True, 
        verbose_name="Description",
        db_column='descr'
    )

    class Meta:
        managed = False  # Set to False if managing existing database table
        db_table = 'tbl_received_receipts' # Assumed table name
        verbose_name = 'Received Receipt'
        verbose_name_plural = 'Received Receipts'
        ordering = ['-receipt_date', 'receipt_number'] # Default ordering

    def __str__(self):
        return f"Receipt {self.receipt_number} from {self.supplier}"

    # Business logic methods (example):
    def is_overdue(self):
        """
        Example business logic: Check if a receipt is considered 'overdue' based on its date.
        (Requires a definition of 'overdue' - e.g., if payment is not made within 30 days)
        This is a placeholder to show where business logic would reside.
        """
        # For demonstration, let's say a receipt is "overdue" if it's older than 60 days
        # and has not been marked as processed (requires a 'processed' field in model)
        # For now, a simple date comparison.
        from datetime import date, timedelta
        return (date.today() - self.receipt_date) > timedelta(days=60)

    def get_summary_text(self):
        """
        Returns a short summary for display purposes.
        """
        return f"{self.receipt_number} ({self.supplier}) - {self.amount:.2f}"
```

### 4.2 Forms (`inventory/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**
We'll create a `ModelForm` for `ReceivedReceipt` to handle creation and updates.

```python
from django import forms
from .models import ReceivedReceipt

class ReceivedReceiptForm(forms.ModelForm):
    class Meta:
        model = ReceivedReceipt
        fields = ['receipt_number', 'receipt_date', 'amount', 'supplier', 'description']
        widgets = {
            'receipt_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'receipt_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'supplier': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
    
    # Custom validation example (optional, but good practice):
    def clean_receipt_number(self):
        receipt_number = self.cleaned_data['receipt_number']
        # Example: Ensure receipt number format
        if not receipt_number.startswith('RC-'):
            # This is a placeholder for actual business rules
            pass #raise forms.ValidationError("Receipt number must start with 'RC-'.")
        return receipt_number
```

### 4.3 Views (`inventory/views.py`)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
We'll define `ListView`, `CreateView`, `UpdateView`, `DeleteView`, and an additional `ListView` for the HTMX table partial.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ReceivedReceipt
from .forms import ReceivedReceiptForm

# Base ListView for the full page
class ReceivedReceiptListView(ListView):
    model = ReceivedReceipt
    template_name = 'inventory/receivedreceipt/list.html'
    context_object_name = 'received_receipts' # This name is used in the template loop

# HTMX partial view for the DataTables table
class ReceivedReceiptTablePartialView(ListView):
    model = ReceivedReceipt
    template_name = 'inventory/receivedreceipt/_receivedreceipt_table.html'
    context_object_name = 'received_receipts' # This name is used in the template loop

class ReceivedReceiptCreateView(CreateView):
    model = ReceivedReceipt
    form_class = ReceivedReceiptForm
    template_name = 'inventory/receivedreceipt/_receivedreceipt_form.html' # Use partial for modal
    success_url = reverse_lazy('received_receipt_list') # Fallback URL if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Received Receipt added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, respond with 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReceivedReceiptList'
                }
            )
        return response # For non-HTMX requests, perform standard redirect

class ReceivedReceiptUpdateView(UpdateView):
    model = ReceivedReceipt
    form_class = ReceivedReceiptForm
    template_name = 'inventory/receivedreceipt/_receivedreceipt_form.html' # Use partial for modal
    success_url = reverse_lazy('received_receipt_list') # Fallback URL if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Received Receipt updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, respond with 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReceivedReceiptList'
                }
            )
        return response # For non-HTMX requests, perform standard redirect

class ReceivedReceiptDeleteView(DeleteView):
    model = ReceivedReceipt
    template_name = 'inventory/receivedreceipt/_receivedreceipt_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('received_receipt_list') # Fallback URL if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Received Receipt deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, respond with 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReceivedReceiptList'
                }
            )
        return response # For non-HTMX requests, perform standard redirect
```

### 4.4 Templates (`inventory/templates/inventory/receivedreceipt/`)

**Task:** Create templates for each view.

**Instructions:**
These templates will demonstrate HTMX and Alpine.js integration, along with DataTables for the list view.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Received Receipts</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'received_receipt_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Receipt
        </button>
    </div>
    
    <div id="receivedReceiptTable-container"
         hx-trigger="load, refreshReceivedReceiptList from:body"
         hx-get="{% url 'received_receipt_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTables table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Received Receipts...</p>
        </div>
    </div>
    
    <!-- Global Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal"
         x-init="$watch('showModal', value => { if(value) document.body.classList.add('overflow-hidden'); else document.body.classList.remove('overflow-hidden'); })"
         @refreshReceivedReceiptList.window="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-3xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
             @click.away="showModal = false">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
    // HTMX listens for custom events
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            // After modal content is loaded, reveal the modal via Alpine.js
            const modal = document.getElementById('modal');
            if (modal && modal.__alpine && !modal.__alpine.data.showModal) {
                modal.__alpine.data.showModal = true;
            }
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) {
            // After a 204 response (successful HTMX form submission/delete), close modal
            const modal = document.getElementById('modal');
            if (modal && modal.__alpine) {
                modal.__alpine.data.showModal = false;
            }
        }
    });
</script>
{% endblock %}
```

#### `_receivedreceipt_table.html` (Partial for DataTables)

```html
<table id="receivedReceiptTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt Number</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Receipt Date</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in received_receipts %}
        <tr>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ obj.receipt_number }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ obj.receipt_date|date:"Y-m-d" }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ obj.amount|floatformat:2 }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ obj.supplier }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                    class="text-indigo-600 hover:text-indigo-900 mr-4"
                    hx-get="{% url 'received_receipt_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="text-red-600 hover:text-red-900"
                    hx-get="{% url 'received_receipt_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="px-6 py-4 text-center text-gray-500">No received receipts found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        $('#receivedReceiptTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true
        });
    });
</script>
```

#### `_receivedreceipt_form.html` (Partial for Create/Update)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Received Receipt</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            <div class="mt-1">
                {{ field }}
            </div>
            {% if field.help_text %}
            <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <ul class="mt-2 text-sm text-red-600 list-disc pl-5">
                {% for error in field.errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="pt-6 flex justify-end space-x-4 border-t border-gray-200">
            <button
                type="button"
                class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `_receivedreceipt_confirm_delete.html` (Partial for Delete)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-lg text-gray-700 mb-6">Are you sure you want to delete the received receipt <strong>"{{ object }}"</strong>?</p>
    <p class="text-red-600 text-sm mb-6">This action cannot be undone.</p>
    
    <div class="flex justify-end space-x-4">
        <button
            type="button"
            class="px-4 py-2 bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold rounded-md shadow-sm transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            type="button"
            hx-delete="{% url 'received_receipt_delete' object.pk %}"
            hx-target="body" # Target body to trigger full page refresh if needed, but 204 + trigger handles it.
            hx-swap="none"
            class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-md shadow-sm transition duration-300 ease-in-out">
            Delete
        </button>
    </div>
</div>
```

### 4.5 URLs (`inventory/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
We'll map the CBVs to appropriate URLs, including the HTMX partial for the table.

```python
from django.urls import path
from .views import (
    ReceivedReceiptListView,
    ReceivedReceiptCreateView,
    ReceivedReceiptUpdateView,
    ReceivedReceiptDeleteView,
    ReceivedReceiptTablePartialView, # New view for HTMX
)

urlpatterns = [
    path('received-receipts/', ReceivedReceiptListView.as_view(), name='received_receipt_list'),
    path('received-receipts/add/', ReceivedReceiptCreateView.as_view(), name='received_receipt_add'),
    path('received-receipts/edit/<int:pk>/', ReceivedReceiptUpdateView.as_view(), name='received_receipt_edit'),
    path('received-receipts/delete/<int:pk>/', ReceivedReceiptDeleteView.as_view(), name='received_receipt_delete'),
    # HTMX specific endpoint for the table
    path('received-receipts/table/', ReceivedReceiptTablePartialView.as_view(), name='received_receipt_table'),
]
```

### 4.6 Tests (`inventory/tests.py`)

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for the model and integration tests for all view operations (list, create, update, delete) will be included.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import ReceivedReceipt
from .forms import ReceivedReceiptForm

class ReceivedReceiptModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test instance for all model tests
        cls.receipt_1 = ReceivedReceipt.objects.create(
            receipt_number='REC-2023-001',
            receipt_date=timezone.now().date(),
            amount=150.75,
            supplier='Supplier A',
            description='Test receipt for goods.'
        )
        cls.receipt_2 = ReceivedReceipt.objects.create(
            receipt_number='REC-2023-002',
            receipt_date=timezone.now().date() - timedelta(days=70), # For overdue test
            amount=200.00,
            supplier='Supplier B',
            description='Another test receipt.'
        )
  
    def test_received_receipt_creation(self):
        self.assertEqual(self.receipt_1.receipt_number, 'REC-2023-001')
        self.assertEqual(self.receipt_1.supplier, 'Supplier A')
        self.assertAlmostEqual(self.receipt_1.amount, 150.75)
        self.assertEqual(str(self.receipt_1), 'Receipt REC-2023-001 from Supplier A')

    def test_field_verbose_names(self):
        # Test verbose names for model fields
        field_receipt_number = self.receipt_1._meta.get_field('receipt_number').verbose_name
        self.assertEqual(field_receipt_number, 'Receipt Number')
        field_supplier = self.receipt_1._meta.get_field('supplier').verbose_name
        self.assertEqual(field_supplier, 'Supplier')
        
    def test_amount_decimal_places(self):
        self.assertEqual(self.receipt_1.amount.as_tuple().exponent, -2) # Should have 2 decimal places

    def test_is_overdue_method(self):
        # Test the example business logic method
        self.assertFalse(self.receipt_1.is_overdue()) # Current receipt should not be overdue
        self.assertTrue(self.receipt_2.is_overdue()) # Older receipt should be overdue
        
    def test_get_summary_text_method(self):
        self.assertEqual(self.receipt_1.get_summary_text(), 'REC-2023-001 (Supplier A) - 150.75')


class ReceivedReceiptViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test instance for all view tests
        cls.receipt = ReceivedReceipt.objects.create(
            receipt_number='VIEW-TEST-001',
            receipt_date=timezone.now().date(),
            amount=500.00,
            supplier='ViewTest Inc.',
            description='For view testing.'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('received_receipt_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/receivedreceipt/list.html')
        self.assertContains(response, 'Received Receipts')
        self.assertContains(response, self.receipt.receipt_number)

    def test_table_partial_view(self):
        # This view is loaded via HTMX, so we test it directly
        response = self.client.get(reverse('received_receipt_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/receivedreceipt/_receivedreceipt_table.html')
        self.assertContains(response, self.receipt.receipt_number) # Check if data is present
        self.assertContains(response, 'id="receivedReceiptTable"') # Check if DataTables structure is there

    def test_create_view_get(self):
        response = self.client.get(reverse('received_receipt_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/receivedreceipt/_receivedreceipt_form.html')
        self.assertIsInstance(response.context['form'], ReceivedReceiptForm)
        self.assertContains(response, 'Add Received Receipt')

    def test_create_view_post_success(self):
        data = {
            'receipt_number': 'NEW-REC-003',
            'receipt_date': '2024-01-15',
            'amount': 300.50,
            'supplier': 'New Supplier',
            'description': 'A new receipt entry.'
        }
        # Simulate HTMX request with HX-Request header
        response = self.client.post(reverse('received_receipt_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX successful post should return 204 No Content
        self.assertEqual(response.status_code, 204)
        # Check for HTMX-Trigger header for list refresh
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshReceivedReceiptList')
        
        # Verify object was created in the database
        self.assertTrue(ReceivedReceipt.objects.filter(receipt_number='NEW-REC-003').exists())
        self.assertEqual(ReceivedReceipt.objects.count(), 2) # Initial + new

    def test_create_view_post_invalid(self):
        data = {
            'receipt_number': '', # Invalid, should be required
            'receipt_date': 'invalid-date',
            'amount': 'not-a-number',
            'supplier': '',
        }
        response = self.client.post(reverse('received_receipt_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'inventory/receivedreceipt/_receivedreceipt_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(ReceivedReceipt.objects.filter(receipt_number='').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('received_receipt_edit', args=[self.receipt.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/receivedreceipt/_receivedreceipt_form.html')
        self.assertIsInstance(response.context['form'], ReceivedReceiptForm)
        self.assertEqual(response.context['form'].instance, self.receipt)
        self.assertContains(response, 'Edit Received Receipt')

    def test_update_view_post_success(self):
        updated_data = {
            'receipt_number': 'UPDATED-REC-001',
            'receipt_date': '2024-02-01',
            'amount': 600.00,
            'supplier': 'Updated Supplier',
            'description': 'Updated description.'
        }
        response = self.client.post(reverse('received_receipt_edit', args=[self.receipt.pk]), updated_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshReceivedReceiptList')
        
        self.receipt.refresh_from_db() # Reload instance from DB
        self.assertEqual(self.receipt.receipt_number, 'UPDATED-REC-001')
        self.assertEqual(self.receipt.supplier, 'Updated Supplier')

    def test_delete_view_get(self):
        response = self.client.get(reverse('received_receipt_delete', args=[self.receipt.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/receivedreceipt/_receivedreceipt_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.receipt.receipt_number)

    def test_delete_view_post_success(self):
        initial_count = ReceivedReceipt.objects.count()
        response = self.client.delete(reverse('received_receipt_delete', args=[self.receipt.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshReceivedReceiptList')
        
        self.assertEqual(ReceivedReceipt.objects.count(), initial_count - 1)
        self.assertFalse(ReceivedReceipt.objects.filter(pk=self.receipt.pk).exists())
```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided templates and views already integrate HTMX and Alpine.js as per the requirements:

*   **HTMX for Dynamic Updates:**
    *   The main `list.html` uses `hx-get` to load the `_receivedreceipt_table.html` partial, which contains the DataTables setup. This ensures the table data is refreshed without a full page reload after any CRUD operation.
    *   The "Add New," "Edit," and "Delete" buttons use `hx-get` to fetch the form/confirmation partials into the modal, allowing for a seamless user experience.
    *   Form submissions (POST) are handled by HTMX with `hx-post`, and the `form_valid` and `delete` methods in views return `HttpResponse(status=204)` with `HX-Trigger` headers (`refreshReceivedReceiptList`). This instructs HTMX to trigger a refresh of the `receivedReceiptTable-container` (which reloads the table via `hx-get`), ensuring the UI is updated immediately.
*   **Alpine.js for UI State Management:**
    *   A global modal (`#modal`) is defined with `x-data="{ showModal: false }"`, managing its visibility.
    *   HTMX interactions that open the modal (`on click add .is-active to #modal`) are complemented by `alpine:init` or direct Alpine property updates (e.g., `modal.__alpine.data.showModal = true`) to ensure Alpine's state is in sync.
    *   The `refreshReceivedReceiptList` custom event (triggered by `HX-Trigger`) is listened for by Alpine (`@refreshReceivedReceiptList.window="showModal = false"`) to automatically close the modal after a successful form submission or deletion.
*   **DataTables for List Views:**
    *   The `_receivedreceipt_table.html` partial contains the `<table>` element with the ID `receivedReceiptTable`.
    *   A `<script>` block within this partial initializes DataTables on `$(document).ready()`. This ensures that every time the partial is loaded (e.g., after a refresh), DataTables is re-applied correctly to the new table content.
    *   All list views (specifically, the `ReceivedReceiptTablePartialView`) will render through this partial, providing client-side searching, sorting, and pagination.

**Ensuring HTMX-only interactions:**
The design strictly avoids writing custom JavaScript functions for AJAX calls or DOM manipulation. All dynamic content loading, form submissions, and UI updates are orchestrated purely through HTMX attributes and basic Alpine.js for modal state. The use of jQuery is limited to DataTables initialization, which is a common and acceptable pattern.

---

## Final Notes

*   **Placeholders Replaced:** All `[PLACEHOLDER]` values have been replaced with concrete names like `ReceivedReceipt`, `received_receipt`, `inventory`, etc., to provide a runnable example.
*   **DRY Templates:** The use of partial templates (`_receivedreceipt_table.html`, `_receivedreceipt_form.html`, `_receivedreceipt_confirm_delete.html`) promotes reusability and keeps the main `list.html` clean. All templates extend `core/base.html` for consistent layout.
*   **Fat Model, Thin Views:** Business logic, such as the `is_overdue` and `get_summary_text` methods, is placed within the `ReceivedReceipt` model. Views are kept concise (mostly 5-15 lines per method, primarily handling form validation and HTMX responses).
*   **Comprehensive Tests:** The `tests.py` file includes unit tests for the model's attributes and methods, and integration tests for all CRUD operations, including specific checks for HTMX headers and responses. This ensures high test coverage and application reliability.
*   **Automation Focus:** This entire structure is designed to be easily generated and adapted by an AI-assisted automation tool. The standardized patterns, strict separation of concerns, and reliance on framework conventions (Django CBVs, HTMX triggers) minimize the need for manual intervention and reduce human error during migration.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes directly in the `widgets` of the Django forms and within the template structures for consistent, modern styling.

This modernized Django application provides a robust, maintainable, and user-friendly solution for managing "Received Receipts," laying a strong foundation for future development and integration within your ERP system.