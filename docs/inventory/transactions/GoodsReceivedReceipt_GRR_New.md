## ASP.NET to Django Conversion Script: Goods Received Receipt (GRR) List

This document outlines the modernization plan for the ASP.NET Goods Received Receipt (GRR) list module, transitioning it to a robust and modern Django 5.0+ application. The approach prioritizes automation, clean architecture, and enhanced user experience through HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`Module_Inventory_Transactions_GoodsReceivedReceipt_GRR_New`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code-behind `loadData()` method directly queries a stored procedure `Sp_GRR_New` and then constructs a `DataTable` with specific columns. The `sql` web method queries `tblMM_Supplier_master`. The commented-out `loadData(string spid)` also reveals `tblInv_Inward_Master`, `tblMM_PO_Master`, `tblMM_PO_Details`, `tblMM_Supplier_master`, `tblFinancial_master`, and others.

For this migration, we will model the **output schema** of `Sp_GRR_New` as our primary `GoodsReceivedReceiptEntry` entity, as this represents the data displayed. We will also create a `Supplier` model for the autocomplete functionality.

**Inferred Tables and Columns:**

1.  **`tblMM_Supplier_master`** (for Supplier data):
    *   `SupplierId` (Primary Key, unique identifier)
    *   `SupplierName` (Name of the supplier)
    *   `CompId` (Company ID, for multi-tenancy)

2.  **Logical View/Query Result for GRR Entries** (mapping to the `DataTable` columns from `Sp_GRR_New`):
    *   `Id` (int, Primary Key, from `tblInv_Inward_Master` or similar, referred to as `Id` in GridView)
    *   `FinYearId` (int, Financial Year ID, from `tblInv_Inward_Master` or `Sp_GRR_New`)
    *   `FinYear` (string, Display Year, derived from `FinYearId` and `tblFinancial_master`)
    *   `PONo` (string, Purchase Order Number)
    *   `GINNo` (string, Goods Inward Note Number)
    *   `GINDate` (datetime, original date, displayed as `SysDate` in DMY format)
    *   `SupplierId` (string, ID of the supplier, referred to as `SupId`)
    *   `SupplierName` (string, Name of the supplier, displayed as `Supplier`)
    *   `ChallanNo` (string, Challan Number)
    *   `ChallanDate` (datetime, original date, displayed as `ChDT` in DMY format)
    *   `GINQty` (double, Inwarded Quantity, used in business logic, not displayed directly)
    *   `GRRQty` (double, Goods Received Receipt Quantity, used in business logic, not displayed directly)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page primarily acts as a **Read (List) view** with **Filtering** and **Navigation**.

*   **Read:** The `loadData()` method fetches a list of GRR entries based on `CompId`, `FinYearId`, and an optional `SupplierId` filter. It then applies a specific business rule: `calbalrecqty = (getInvQty - getRecQty); if (calbalrecqty > 0) { ... }`, which means only entries where the inwarded quantity is greater than the received quantity (i.e., pending receipt) are displayed. This is critical business logic.
*   **Filtering:** A `TextBox` (`txtSupplier`) combined with an `AutoCompleteExtender` and a `Search` button (`btnSearch`) allows filtering the list by supplier.
*   **Pagination:** The `GridView2` inherently supports pagination.
*   **Navigation:** The `Select` `LinkButton` in each row redirects the user to a detailed GRR page (`GoodsReceivedReceipt_GRR_New_Details.aspx`) with various IDs passed as query parameters. This is not a CRUD operation on the current entity but a navigational action.

**Validation Logic:** No explicit input validation is shown for the search form.

### Step 3: Infer UI Components

**Analysis:**

*   **Supplier Search Input:** `asp:TextBox` with `cc1:AutoCompleteExtender`. This will be replaced by a standard HTML `<input>` field, with HTMX for triggering autocomplete suggestions and Alpine.js for handling the display of suggestions.
*   **Search Button:** `asp:Button`. This will be replaced by an HTML `<button>` with HTMX attributes to trigger a re-fetch of the table data.
*   **Data Grid:** `asp:GridView`. This will be replaced by DataTables.js, populated by a Django template rendered via HTMX.
*   **Row Actions:** `asp:LinkButton` for "Select". This will be an HTML `<button>` or `<a>` tag, likely using standard Django URL reversing for redirection.

### Step 4: Generate Django Code

We will create a Django application named `inventory` to house this module.

#### 4.1 Models (`inventory/models.py`)

**Explanation:**
We define two models. `Supplier` maps directly to `tblMM_Supplier_master`. `GoodsReceivedReceiptEntry` is designed to represent the structured output of the original `Sp_GRR_New` procedure, including the `GINQty` and `GRRQty` necessary for the filtering logic, even if they aren't directly displayed columns. We use `managed = False` because these models will interact with existing database tables or views. The `has_pending_receipt_quantity` method encapsulates the core business logic from the ASP.NET `loadData` method.

```python
# inventory/models.py

from django.db import models
from datetime import datetime

class Supplier(models.Model):
    """
    Maps to tblMM_Supplier_master for supplier details.
    Used for autocomplete functionality.
    """
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False  # Set to False if this table exists in the external database
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class GoodsReceivedReceiptEntry(models.Model):
    """
    Represents an entry in the Goods Received Receipt list.
    This model mirrors the output structure of the original Sp_GRR_New stored procedure,
    including fields for business logic even if not directly displayed.
    We assume these fields come from a logical view or joined tables.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)  # Matches DataKeyNames="Id"
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    gin_date = models.DateTimeField(db_column='GINDate', blank=True, null=True) # Original date field
    # SupId was a separate column in ASP.NET GridView, Supplier was SupplierName+' ['+SupplierId+']'
    supplier_id = models.CharField(db_column='SupId', max_length=50, blank=True, null=True) # Actual Supplier ID
    supplier_name = models.CharField(db_column='Supplier', max_length=255, blank=True, null=True) # Display name from SP
    challan_no = models.CharField(db_column='ChNO', max_length=50, blank=True, null=True)
    challan_date = models.DateTimeField(db_column='ChDT', blank=True, null=True) # Original date field
    
    # These fields are crucial for the business logic (calbalrecqty) but not displayed directly.
    # In a real scenario, they might come from related tables or be computed in a view.
    gin_qty = models.DecimalField(db_column='GINQty', max_digits=18, decimal_places=3, blank=True, null=True)
    grr_qty = models.DecimalField(db_column='GRRQty', max_digits=18, decimal_places=3, blank=True, null=True)
    
    # We will assume FinYear and SysDate as properties derived from other fields or related lookups
    # If FinYear is directly available from SP output, it can be a CharField.
    # For now, let's assume it's derived or a lookup.
    # fin_year_display = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)

    class Meta:
        managed = False  # Set to False if this table/view exists in the external database
        # This db_table name is an assumption. In a real scenario, it would be the actual
        # table name or the name of a materialized view that matches Sp_GRR_New's output.
        db_table = 'vw_Inv_GoodsReceivedReceiptEntry' # Example: a database view created from Sp_GRR_New logic
        verbose_name = 'Goods Received Receipt Entry'
        verbose_name_plural = 'Goods Received Receipt Entries'
        # Ordering as per GridView display, or a natural order
        ordering = ['-id']

    def __str__(self):
        return f"GRR {self.gin_no} for {self.supplier_name}"

    @property
    def display_gin_date(self):
        """Formats GINDate to DD/MM/YYYY as per ASP.NET 'FromDateDMY'."""
        if self.gin_date:
            return self.gin_date.strftime('%d/%m/%Y')
        return ''

    @property
    def display_challan_date(self):
        """Formats ChallanDate to DD/MM/YYYY as per ASP.NET 'FromDateDMY'."""
        if self.challan_date:
            return self.challan_date.strftime('%d/%m/%Y')
        return ''

    @property
    def has_pending_receipt_quantity(self):
        """
        Implements the core business logic from ASP.NET loadData method:
        calbalrecqty = (getInvQty - getRecQty); if (calbalrecqty > 0)
        Only entries where GINQty > GRRQty are considered.
        """
        gin_qty = self.gin_qty if self.gin_qty is not None else 0
        grr_qty = self.grr_qty if self.grr_qty is not None else 0
        
        # k > 0 means GINQty > 0
        # z > 0 means calbalrecqty > 0
        return gin_qty > 0 and (gin_qty - grr_qty) > 0
    
    @classmethod
    def get_filtered_grr_entries(cls, comp_id, fin_year_id, supplier_id=None):
        """
        Simulates the logic of Sp_GRR_New and the subsequent filtering.
        In a real scenario, this would ideally be handled by a custom manager or
        directly calling a database view/stored procedure if ORM is insufficient.
        """
        queryset = cls.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)
        
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)
        
        # Apply the business logic filter (k>0 && z>0)
        # This assumes gin_qty and grr_qty are part of the model/view.
        # If not, a complex join or raw SQL might be necessary.
        filtered_queryset = [
            entry for entry in queryset if entry.has_pending_receipt_quantity
        ]
        
        return filtered_queryset

```

#### 4.2 Forms (`inventory/forms.py`)

**Explanation:**
A simple `Form` for the supplier search input. No `ModelForm` is needed as this is not for CRUD on a `GoodsReceivedReceiptEntry` itself.

```python
# inventory/forms.py

from django import forms

class GRRSearchForm(forms.Form):
    """
    Form for filtering Goods Received Receipt entries by supplier.
    """
    supplier_search = forms.CharField(
        max_length=255,
        required=False,
        label='Supplier',
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/inventory/goodsreceivedreceiptentry/autocomplete/supplier/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'x-model': 'supplierSearch',
            'x-on:click.outside': 'showSuggestions = false',
            'x-on:focus': 'if (supplierSuggestions.length > 0) showSuggestions = true',
            'x-on:keydown.tab': 'showSuggestions = false' # Hide suggestions on tab out
        })
    )
    # Hidden field to store selected supplier_id
    selected_supplier_id = forms.CharField(
        widget=forms.HiddenInput(attrs={'x-model': 'selectedSupplierId'}),
        required=False
    )

```

#### 4.3 Views (`inventory/views.py`)

**Explanation:**
- `GRRListView`: Serves the main page with the search form and an HTMX container for the table. It sets up Alpine.js data for managing the search input and suggestions.
- `GRRTablePartialView`: This view is crucial. It simulates the `loadData()` logic. It retrieves `comp_id` and `fin_year_id` (assuming they come from session/user context for `managed=False` models). It then filters the `GoodsReceivedReceiptEntry` queryset based on the supplier search and applies the `has_pending_receipt_quantity` business logic. This is fetched via HTMX.
- `SupplierAutocompleteView`: Provides JSON suggestions for the supplier autocomplete, mimicking the ASP.NET `sql` web method.
- `GRRRedirectView`: Handles the "Select" action, redirecting to the detailed GRR page.

```python
# inventory/views.py

from django.views.generic import ListView, TemplateView, View, RedirectView
from django.urls import reverse_lazy, reverse
from django.http import JsonResponse, HttpResponseRedirect
from django.db.models import Q # For ORM queries
import json # For parsing supplier_search if needed
import re # For parsing supplier name [id]

from .models import GoodsReceivedReceiptEntry, Supplier
from .forms import GRRSearchForm

class GRRListView(ListView):
    """
    Main view to display the Goods Received Receipt list page.
    Handles the search form and sets up HTMX/Alpine.js containers.
    """
    template_name = 'inventory/goodsreceivedreceiptentry/list.html'
    model = GoodsReceivedReceiptEntry # Placeholder, actual data loaded via HTMX
    context_object_name = 'grr_entries'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = GRRSearchForm()
        # Initial data for Alpine.js x-data
        context['alpine_data'] = {
            'supplierSearch': '',
            'selectedSupplierId': '',
            'showSuggestions': False,
            'supplierSuggestions': [],
            'selectSuggestion': """
                (suggestion) => {
                    supplierSearch = suggestion.display;
                    selectedSupplierId = suggestion.id;
                    showSuggestions = false;
                    // Trigger hx-post on the form to refresh the table
                    $dispatch('refreshGRRTable'); 
                }
            """
        }
        return context

class GRRTablePartialView(TemplateView):
    """
    Renders the partial HTML for the GRR table, designed to be loaded via HTMX.
    Applies filtering and business logic from the original ASP.NET loadData method.
    """
    template_name = 'inventory/goodsreceivedreceiptentry/_grr_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Simulate session variables (replace with actual session/user context logic)
        # In a real application, these would come from request.session or user profile.
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not found
        fin_year_id = self.request.session.get('finyear', datetime.now().year) # Default to current year
        
        supplier_search_text = self.request.GET.get('supplier_search', '')
        selected_supplier_id = self.request.GET.get('selected_supplier_id', '').strip()

        # Extract supplier ID from search text if it's in "Name [ID]" format
        # This mimics the ASP.NET 'fun.getCode(txtSupplier.Text)'
        if supplier_search_text:
            match = re.search(r'\[(.*?)\]$', supplier_search_text)
            if match:
                selected_supplier_id = match.group(1)
            elif not selected_supplier_id: # If not found by regex, and no hidden ID, try direct name match
                try:
                    # Attempt to find supplier by exact name if no ID is provided
                    supplier_obj = Supplier.objects.using('external_db').get(supplier_name=supplier_search_text, comp_id=comp_id)
                    selected_supplier_id = supplier_obj.supplier_id
                except Supplier.DoesNotExist:
                    selected_supplier_id = '' # No exact match, so no supplier filter applied

        # Apply the complex filtering logic from the model manager/classmethod
        grr_entries = GoodsReceivedReceiptEntry.get_filtered_grr_entries(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            supplier_id=selected_supplier_id if selected_supplier_id else None
        )
        
        context['grr_entries'] = grr_entries
        return context

class SupplierAutocompleteView(View):
    """
    Provides autocomplete suggestions for suppliers via HTMX/JSON.
    Mimics the ASP.NET 'sql' web method.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('supplier_search', '')
        # Simulate session comp_id
        comp_id = request.session.get('compid', 1) 
        
        suggestions = []
        if query:
            # Query the Supplier model (assuming 'external_db' for existing DB)
            # Use __istartswith for case-insensitive startswith match
            suppliers = Supplier.objects.using('external_db').filter(
                Q(supplier_name__istartswith=query) | Q(supplier_id__istartswith=query),
                comp_id=comp_id
            ).order_by('supplier_name')[:10] # Limit results
            
            for supplier in suppliers:
                suggestions.append({
                    'id': supplier.supplier_id,
                    'display': f"{supplier.supplier_name} [{supplier.supplier_id}]"
                })
        
        # HTMX requires a simple HTML fragment or JSON response.
        # For Alpine.js, we'll return JSON.
        return JsonResponse(suggestions, safe=False)

class GRRRedirectView(RedirectView):
    """
    Handles the 'Select' action from the GRR list, redirecting to a details page.
    Mimics the ASP.NET Response.Redirect logic.
    """
    pattern_name = 'grr_details' # This should point to your actual GRR details page URL

    def get_redirect_url(self, *args, **kwargs):
        # Extract data from kwargs (pk corresponds to Id in the model)
        grr_id = kwargs.get('pk')
        
        # Retrieve the specific GRR entry to get other details for the redirect
        try:
            grr_entry = GoodsReceivedReceiptEntry.objects.using('external_db').get(id=grr_id)
        except GoodsReceivedReceiptEntry.DoesNotExist:
            # Handle case where entry not found, e.g., redirect to an error page
            return reverse_lazy('grr_list') # Fallback to list view
        
        # Construct the query string parameters, mimicking the original ASP.NET redirect
        # Note: 'ModId' and 'SubModId' are arbitrary in ASP.NET, should be mapped to Django concepts if truly needed.
        query_params = {
            'Id': grr_entry.id,
            'SupId': grr_entry.supplier_id,
            'GINNo': grr_entry.gin_no,
            'PONo': grr_entry.po_no,
            'FyId': grr_entry.fin_year_id,
            'ModId': 9,  # Placeholder, should be mapped to real module IDs in Django
            'SubModId': 38 # Placeholder
        }
        
        # Build the URL for the details page
        # Assuming 'grr_details' URL pattern can accept some of these, or query params directly.
        # If the 'grr_details' URL pattern expects kwargs, you'd structure it differently.
        # For simplicity, passing as query parameters.
        base_url = reverse(self.pattern_name)
        from urllib.parse import urlencode
        return f"{base_url}?{urlencode(query_params)}"

```

#### 4.4 Templates

**Explanation:**
- `list.html` sets up the main page, including the search form and an HTMX `div` that will load the `_grr_table.html` content. Alpine.js is used for managing the autocomplete suggestions.
- `_grr_table.html` is a partial template that contains the DataTables setup. It will be loaded via HTMX when the page loads or when the search criteria change.
- No form or delete templates are needed for this particular page, as its primary function is listing and navigation.

```html
{# inventory/templates/inventory/goodsreceivedreceiptentry/list.html #}

{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Received Receipt [GRR] - New</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{ 
        supplierSearch: '{{ form.supplier_search.value|default:"" }}',
        selectedSupplierId: '{{ form.selected_supplier_id.value|default:"" }}',
        showSuggestions: false,
        supplierSuggestions: [],
        init() {
            // Watch for changes to supplierSearch and fetch suggestions
            this.$watch('supplierSearch', (value) => {
                if (value.length > 0 && !value.includes('[')) { // Only fetch if not already a full selection
                    fetch(`/inventory/goodsreceivedreceiptentry/autocomplete/supplier/?supplier_search=${value}`)
                        .then(response => response.json())
                        .then(data => {
                            this.supplierSuggestions = data;
                            this.showSuggestions = data.length > 0;
                        });
                } else {
                    this.supplierSuggestions = [];
                    this.showSuggestions = false;
                }
            });
            // Listen for custom event to trigger table refresh
            this.$root.addEventListener('refreshGRRTable', () => {
                document.getElementById('searchForm').requestSubmit();
            });
        },
        selectSuggestion(suggestion) {
            this.supplierSearch = suggestion.display;
            this.selectedSupplierId = suggestion.id;
            this.showSuggestions = false;
            // Dispatch a custom event to trigger the form submission (and thus HTMX refresh)
            this.$dispatch('refreshGRRTable');
        }
    }">
        <form id="searchForm" 
              hx-get="{% url 'grr_table' %}" 
              hx-target="#grr-table-container" 
              hx-swap="innerHTML"
              hx-trigger="submit, refreshGRRTable from:body"
              class="flex flex-col md:flex-row items-center gap-4">
            {% csrf_token %}
            <div class="flex-grow w-full md:w-auto relative">
                <label for="{{ form.supplier_search.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.supplier_search.label }}:
                </label>
                {{ form.supplier_search }}
                <input type="hidden" name="selected_supplier_id" x-model="selectedSupplierId">
                <div x-show="showSuggestions && supplierSuggestions.length > 0"
                     class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto"
                     x-cloak>
                    <template x-for="suggestion in supplierSuggestions" :key="suggestion.id">
                        <div x-text="suggestion.display"
                             @click="selectSuggestion(suggestion)"
                             class="px-3 py-2 cursor-pointer hover:bg-gray-100 text-sm"></div>
                    </template>
                </div>
            </div>
            <div class="mt-2 md:mt-0">
                <button type="submit" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                    Search
                </button>
            </div>
        </form>
    </div>

    <div id="grr-table-container" 
         hx-trigger="load delay:100ms, refreshGRRTable from:body" 
         hx-get="{% url 'grr_table' %}" 
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial loading indicator -->
        <div class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Goods Received Receipts...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any Alpine.js scripts if more complex state management is needed globally -->
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize DataTables when the table content is swapped by HTMX
        if (evt.target.id === 'grr-table-container') {
            $('#grrTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "ordering": true,
                "searching": true,
                "paging": true,
                "info": true
            });
        }
    });
</script>
{% endblock %}

```

```html
{# inventory/templates/inventory/goodsreceivedreceiptentry/_grr_table.html #}

<table id="grrTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for entry in grr_entries %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.fin_year_id }}</td> {# Assuming fin_year_id is used for display year #}
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.gin_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.display_gin_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.po_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-justify">{{ entry.supplier_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.challan_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.display_challan_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <a href="{% url 'grr_select' pk=entry.id %}" 
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs">
                    Select
                </a>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-lg text-red-700">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization is handled by htmx:afterSwap event listener in list.html
    // This script block should not run directly on page load if this is a partial.
    // The main list.html will re-initialize it.
    // If you need to debug, ensure jQuery and DataTables are loaded in base.html.
</script>

```

#### 4.5 URLs (`inventory/urls.py`)

**Explanation:**
Defines URL patterns for the main list page, the HTMX-loaded table partial, the supplier autocomplete endpoint, and the "select" redirect.

```python
# inventory/urls.py

from django.urls import path
from .views import GRRListView, GRRTablePartialView, SupplierAutocompleteView, GRRRedirectView

urlpatterns = [
    # Main GRR list page
    path('goodsreceivedreceiptentry/', GRRListView.as_view(), name='grr_list'),
    
    # HTMX endpoint for the table content (for search/refresh)
    path('goodsreceivedreceiptentry/table/', GRRTablePartialView.as_view(), name='grr_table'),
    
    # HTMX/AJAX endpoint for supplier autocomplete suggestions
    path('goodsreceivedreceiptentry/autocomplete/supplier/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
    
    # URL for the "Select" action to redirect to GRR details page
    # Note: 'grr_details' should be defined in your main project's urls.py or another app's urls.py
    path('goodsreceivedreceiptentry/select/<int:pk>/', GRRRedirectView.as_view(), name='grr_select'),
    
    # Placeholder for the actual GRR details page. You need to define this in your project.
    # Example: path('goodsreceivedreceiptentry/details/', YourGRRDetailsView.as_view(), name='grr_details'),
    # For now, we'll assume a dummy path for the redirect target.
    path('goodsreceivedreceiptentry/details/', GRRListView.as_view(), name='grr_details'), # Dummy target for redirection testing
]

```

#### 4.6 Tests (`inventory/tests.py`)

**Explanation:**
Comprehensive tests covering the `Supplier` and `GoodsReceivedReceiptEntry` models, including the crucial business logic. Integration tests for the views ensure correct rendering, data filtering, and HTMX interactions.

```python
# inventory/tests.py

from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection # For raw SQL if needed, though models should handle it
from datetime import datetime
from unittest.mock import patch, MagicMock

from .models import GoodsReceivedReceiptEntry, Supplier
from .forms import GRRSearchForm
from .views import GRRListView, GRRTablePartialView, SupplierAutocompleteView, GRRRedirectView

# Mocking the external database connection for tests
# In a real scenario, you'd configure a test database in settings.py
# For this migration, we'll ensure model methods don't hit a real external DB during tests.
class MockQuerySet:
    def __init__(self, data):
        self._data = data

    def filter(self, *args, **kwargs):
        # Simple mock filter logic, extend as needed
        filtered_data = [item for item in self._data if all(getattr(item, k, None) == v for k, v in kwargs.items())]
        return MockQuerySet(filtered_data)

    def order_by(self, *args, **kwargs):
        return self

    def values_list(self, *args, **kwargs):
        return [tuple(getattr(item, f) for f in args) for item in self._data]

    def get(self, *args, **kwargs):
        for item in self._data:
            if all(getattr(item, k, None) == v for k, v in kwargs.items()):
                return item
        raise GoodsReceivedReceiptEntry.DoesNotExist # Or Supplier.DoesNotExist

    def __iter__(self):
        return iter(self._data)

    def __len__(self):
        return len(self._data)

    def __getitem__(self, index):
        return self._data[index]

    def exists(self):
        return len(self._data) > 0
    
    def count(self):
        return len(self._data)

class MockSupplier:
    def __init__(self, supplier_id, supplier_name, comp_id):
        self.supplier_id = supplier_id
        self.supplier_name = supplier_name
        self.comp_id = comp_id

class MockGRREntry:
    def __init__(self, id, fin_year_id, po_no, gin_no, gin_date, supplier_id, supplier_name,
                 challan_no, challan_date, gin_qty, grr_qty, comp_id):
        self.id = id
        self.fin_year_id = fin_year_id
        self.po_no = po_no
        self.gin_no = gin_no
        self.gin_date = gin_date
        self.supplier_id = supplier_id
        self.supplier_name = supplier_name
        self.challan_no = challan_no
        self.challan_date = challan_date
        self.gin_qty = gin_qty
        self.grr_qty = grr_qty
        self.comp_id = comp_id

    @property
    def display_gin_date(self):
        if self.gin_date:
            return self.gin_date.strftime('%d/%m/%Y')
        return ''

    @property
    def display_challan_date(self):
        if self.challan_date:
            return self.challan_date.strftime('%d/%m/%Y')
        return ''

    @property
    def has_pending_receipt_quantity(self):
        gin_qty = self.gin_qty if self.gin_qty is not None else 0
        grr_qty = self.grr_qty if self.grr_qty is not None else 0
        return gin_qty > 0 and (gin_qty - grr_qty) > 0

class InventoryModelTest(TestCase):

    def test_supplier_creation(self):
        # Test basic properties (managed=False means no DB write here)
        supplier = Supplier(supplier_id='SUP001', supplier_name='Test Supplier', comp_id=1)
        self.assertEqual(supplier.supplier_id, 'SUP001')
        self.assertEqual(supplier.supplier_name, 'Test Supplier')
        self.assertEqual(str(supplier), 'Test Supplier [SUP001]')

    def test_grr_entry_properties(self):
        entry = GoodsReceivedReceiptEntry(
            id=1,
            fin_year_id=2023,
            po_no='PO001',
            gin_no='GIN001',
            gin_date=datetime(2023, 1, 15),
            supplier_id='SUP001',
            supplier_name='Test Supplier',
            challan_no='CH001',
            challan_date=datetime(2023, 1, 10),
            gin_qty=100.0,
            grr_qty=50.0,
            comp_id=1
        )
        self.assertEqual(entry.id, 1)
        self.assertEqual(entry.display_gin_date, '15/01/2023')
        self.assertEqual(entry.display_challan_date, '10/01/2023')
        self.assertEqual(str(entry), 'GRR GIN001 for Test Supplier')

    def test_has_pending_receipt_quantity_logic(self):
        # Case 1: Pending quantity exists (GINQty > GRRQty)
        entry_pending = GoodsReceivedReceiptEntry(gin_qty=100.0, grr_qty=50.0, id=1, fin_year_id=2023, comp_id=1)
        self.assertTrue(entry_pending.has_pending_receipt_quantity)

        # Case 2: No pending quantity (GINQty == GRRQty)
        entry_completed = GoodsReceivedReceiptEntry(gin_qty=100.0, grr_qty=100.0, id=2, fin_year_id=2023, comp_id=1)
        self.assertFalse(entry_completed.has_pending_receipt_quantity)

        # Case 3: No pending quantity (GINQty < GRRQty - should not happen normally, but for completeness)
        entry_over_received = GoodsReceivedReceiptEntry(gin_qty=100.0, grr_qty=110.0, id=3, fin_year_id=2023, comp_id=1)
        self.assertFalse(entry_over_received.has_pending_receipt_quantity)

        # Case 4: Zero GINQty
        entry_zero_gin = GoodsReceivedReceiptEntry(gin_qty=0.0, grr_qty=0.0, id=4, fin_year_id=2023, comp_id=1)
        self.assertFalse(entry_zero_gin.has_pending_receipt_quantity)

        # Case 5: Null values
        entry_null_qty = GoodsReceivedReceiptEntry(gin_qty=None, grr_qty=None, id=5, fin_year_id=2023, comp_id=1)
        self.assertFalse(entry_null_qty.has_pending_receipt_quantity)

        # Case 6: GINQty > 0 but GRRQty is None
        entry_gin_only = GoodsReceivedReceiptEntry(gin_qty=50.0, grr_qty=None, id=6, fin_year_id=2023, comp_id=1)
        self.assertTrue(entry_gin_only.has_pending_receipt_quantity)

    @patch('inventory.models.GoodsReceivedReceiptEntry.objects.using')
    def test_get_filtered_grr_entries(self, mock_using):
        mock_data = [
            MockGRREntry(1, 2023, 'PO001', 'GIN001', datetime(2023,1,15), 'SUP001', 'Supplier A', 'CH001', datetime(2023,1,10), 100.0, 50.0, 1), # Pending, Matches
            MockGRREntry(2, 2023, 'PO002', 'GIN002', datetime(2023,1,16), 'SUP002', 'Supplier B', 'CH002', datetime(2023,1,11), 100.0, 100.0, 1),# Completed, No Match
            MockGRREntry(3, 2022, 'PO003', 'GIN003', datetime(2022,1,17), 'SUP001', 'Supplier A', 'CH003', datetime(2022,1,12), 80.0, 30.0, 1), # Pending, Wrong FinYear
            MockGRREntry(4, 2023, 'PO004', 'GIN004', datetime(2023,1,18), 'SUP003', 'Supplier C', 'CH004', datetime(2023,1,13), 70.0, 10.0, 1), # Pending, Different Supplier
            MockGRREntry(5, 2023, 'PO005', 'GIN005', datetime(2023,1,19), 'SUP001', 'Supplier A', 'CH005', datetime(2023,1,14), 0.0, 0.0, 1), # Zero qty, No Match
            MockGRREntry(6, 2023, 'PO006', 'GIN006', datetime(2023,1,20), 'SUP001', 'Supplier A', 'CH006', datetime(2023,1,15), 20.0, 10.0, 2), # Different Company, No Match
        ]
        mock_using.return_value.all.return_value = MockQuerySet(mock_data)

        # Test with no supplier filter
        filtered_entries = GoodsReceivedReceiptEntry.get_filtered_grr_entries(
            comp_id=1, fin_year_id=2023
        )
        self.assertEqual(len(filtered_entries), 2)
        self.assertIn(mock_data[0], filtered_entries) # GIN001 for SUP001
        self.assertIn(mock_data[3], filtered_entries) # GIN004 for SUP003

        # Test with supplier filter
        filtered_entries_sup1 = GoodsReceivedReceiptEntry.get_filtered_grr_entries(
            comp_id=1, fin_year_id=2023, supplier_id='SUP001'
        )
        self.assertEqual(len(filtered_entries_sup1), 1)
        self.assertIn(mock_data[0], filtered_entries_sup1)

        # Test with no matching entries
        filtered_entries_no_match = GoodsReceivedReceiptEntry.get_filtered_grr_entries(
            comp_id=1, fin_year_id=2023, supplier_id='NONEXISTENT'
        )
        self.assertEqual(len(filtered_entries_no_match), 0)

        # Test with different comp_id
        filtered_entries_comp2 = GoodsReceivedReceiptEntry.get_filtered_grr_entries(
            comp_id=2, fin_year_id=2023
        )
        self.assertEqual(len(filtered_entries_comp2), 1)
        self.assertIn(mock_data[5], filtered_entries_comp2)


class InventoryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.session = self.client.session
        self.session['compid'] = 1
        self.session['finyear'] = 2023
        self.session.save()

        # Mock initial data for views
        self.mock_suppliers = [
            MockSupplier('SUP001', 'Acme Corp', 1),
            MockSupplier('SUP002', 'Beta Ltd', 1),
            MockSupplier('SUP003', 'Gamma Inc', 2), # Different company
        ]
        self.mock_grr_entries = [
            MockGRREntry(1, 2023, 'PO001', 'GIN001', datetime(2023, 1, 15), 'SUP001', 'Acme Corp', 'CH001', datetime(2023, 1, 10), 100.0, 50.0, 1),
            MockGRREntry(2, 2023, 'PO002', 'GIN002', datetime(2023, 1, 16), 'SUP002', 'Beta Ltd', 'CH002', datetime(2023, 1, 11), 100.0, 100.0, 1), # No pending qty
            MockGRREntry(3, 2022, 'PO003', 'GIN003', datetime(2022, 1, 17), 'SUP001', 'Acme Corp', 'CH003', datetime(2022, 1, 12), 80.0, 30.0, 1),
        ]

    @patch('inventory.models.GoodsReceivedReceiptEntry.objects.using')
    def test_grr_list_view(self, mock_grr_using):
        mock_grr_using.return_value.all.return_value = MockQuerySet(self.mock_grr_entries)
        response = self.client.get(reverse('grr_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceiptentry/list.html')
        self.assertIsInstance(response.context['form'], GRRSearchForm)
        self.assertIn('alpine_data', response.context)

    @patch('inventory.models.GoodsReceivedReceiptEntry.objects.using')
    def test_grr_table_partial_view_no_filter(self, mock_grr_using):
        mock_grr_using.return_value.all.return_value = MockQuerySet(self.mock_grr_entries)
        response = self.client.get(reverse('grr_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsreceivedreceiptentry/_grr_table.html')
        self.assertIn('grr_entries', response.context)
        # Only pending entries for comp_id=1, fin_year_id<=2023
        # Entry 1 (pending, 2023, comp 1) -> should be included
        # Entry 2 (not pending, 2023, comp 1) -> should be excluded
        # Entry 3 (pending, 2022, comp 1) -> should be included
        self.assertEqual(len(response.context['grr_entries']), 2)
        self.assertIn(self.mock_grr_entries[0], response.context['grr_entries'])
        self.assertIn(self.mock_grr_entries[2], response.context['grr_entries'])
        self.assertContains(response, 'GIN001')
        self.assertContains(response, 'GIN003')
        self.assertNotContains(response, 'GIN002')


    @patch('inventory.models.GoodsReceivedReceiptEntry.objects.using')
    def test_grr_table_partial_view_with_supplier_filter(self, mock_grr_using):
        mock_grr_using.return_value.all.return_value = MockQuerySet(self.mock_grr_entries)
        # Test with supplier ID filter
        response = self.client.get(reverse('grr_table'), {'selected_supplier_id': 'SUP001'})
        self.assertEqual(response.status_code, 200)
        self.assertIn('grr_entries', response.context)
        self.assertEqual(len(response.context['grr_entries']), 2) # Both GIN001 and GIN003 are for SUP001 and pending

        # Test with supplier name [id] format
        response = self.client.get(reverse('grr_table'), {'supplier_search': 'Acme Corp [SUP001]'})
        self.assertEqual(response.status_code, 200)
        self.assertIn('grr_entries', response.context)
        self.assertEqual(len(response.context['grr_entries']), 2)

        # Test with only supplier name, and expect internal lookup
        with patch('inventory.models.Supplier.objects.using') as mock_supplier_using:
            mock_supplier_using.return_value.get.return_value = self.mock_suppliers[0] # Acme Corp [SUP001]
            response = self.client.get(reverse('grr_table'), {'supplier_search': 'Acme Corp'})
            self.assertEqual(response.status_code, 200)
            self.assertEqual(len(response.context['grr_entries']), 2)
            mock_supplier_using.return_value.get.assert_called_once_with(supplier_name='Acme Corp', comp_id=1)

    @patch('inventory.models.Supplier.objects.using')
    def test_supplier_autocomplete_view(self, mock_supplier_using):
        mock_supplier_using.return_value.filter.return_value.order_by.return_value[:10] = [
            self.mock_suppliers[0], # Acme Corp [SUP001]
            self.mock_suppliers[1], # Beta Ltd [SUP002]
        ]

        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_search': 'a'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        suggestions = json.loads(response.content)
        self.assertEqual(len(suggestions), 2)
        self.assertEqual(suggestions[0]['display'], 'Acme Corp [SUP001]')
        self.assertEqual(suggestions[1]['display'], 'Beta Ltd [SUP002]')

        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_search': ''})
        self.assertEqual(response.status_code, 200)
        suggestions = json.loads(response.content)
        self.assertEqual(len(suggestions), 0)

    @patch('inventory.models.GoodsReceivedReceiptEntry.objects.using')
    def test_grr_redirect_view(self, mock_grr_using):
        mock_grr_using.return_value.get.return_value = self.mock_grr_entries[0] # GIN001
        
        response = self.client.get(reverse('grr_select', args=[1])) # Select GRR with ID 1
        self.assertEqual(response.status_code, 302) # Should redirect
        
        expected_url = reverse('grr_details') + '?Id=1&SupId=SUP001&GINNo=GIN001&PONo=PO001&FyId=2023&ModId=9&SubModId=38'
        self.assertEqual(response.url, expected_url)

        # Test with a non-existent ID (should redirect to list or error page)
        mock_grr_using.return_value.get.side_effect = GoodsReceivedReceiptEntry.DoesNotExist
        response_not_found = self.client.get(reverse('grr_select', args=[999]))
        self.assertEqual(response_not_found.status_code, 302)
        self.assertEqual(response_not_found.url, reverse('grr_list'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions Compliance:**

1.  **HTMX for dynamic updates and form submissions:**
    *   The `searchForm` in `list.html` uses `hx-get="{% url 'grr_table' %}"`, `hx-target="#grr-table-container"`, and `hx-trigger="submit, refreshGRRTable from:body"` to dynamically update the table without a full page reload when the search button is clicked or a suggestion is selected.
    *   The `supplier_search` input uses `hx-get="/inventory/goodsreceivedreceiptentry/autocomplete/supplier/"`, `hx-trigger="keyup changed delay:500ms"`, `hx-target="#supplier-suggestions"`, and `hx-swap="innerHTML"` for live autocomplete suggestions.
    *   A custom `HX-Trigger` is not explicitly used for the table refresh, but an Alpine.js `x-dispatch` (`refreshGRRTable` event) is used to trigger the form submission (and thus the HTMX `hx-get` on the table) after selecting a suggestion. This is a common and effective pattern.

2.  **Alpine.js for client-side reactivity and modals:**
    *   `list.html` includes an `x-data` block to manage the `supplierSearch`, `selectedSupplierId`, `showSuggestions`, and `supplierSuggestions` states.
    *   `x-show` controls the visibility of the suggestion list.
    *   `x-for` iterates through `supplierSuggestions` to render the autocomplete list.
    *   `@click` and `x-on:click.outside` are used for user interaction with suggestions.
    *   The modal component is not needed here as there are no CRUD forms on this page that would appear in a modal; the "Select" action is a full page redirect.

3.  **DataTables for all list views with sorting and filtering:**
    *   The `_grr_table.html` partial contains the `<table>` with `id="grrTable"`.
    *   A `<script>` block in `list.html` (within `{% block extra_js %}`) ensures that DataTables is re-initialized whenever the `grr-table-container` is swapped by HTMX, maintaining client-side sorting, searching, and pagination capabilities.
    *   The table headers and data cells are structured to be compatible with DataTables.

4.  **All interactions work without full page reloads:**
    *   The search and table refresh mechanisms are entirely driven by HTMX, providing a smooth single-page application feel without complex JavaScript frameworks.

5.  **Ensure proper HX-Trigger responses for list refreshes after CRUD operations:**
    *   While this page doesn't perform CRUD operations, the `refreshGRRTable` event dispatched by Alpine.js and listened by HTMX demonstrates how a custom trigger mechanism for list refreshes would work if CRUD operations were performed on the current page and required a table update.

## Final Notes

*   **Placeholders:** `[APP_NAME]` is `inventory`. `[MODEL_NAME]` is `GoodsReceivedReceiptEntry`.
*   **DRY Templates:** The use of `_grr_table.html` as a partial template for the table content is a good example of DRY, allowing the table to be reloaded independently.
*   **Fat Model, Thin View:** The complex filtering and business logic (e.g., `has_pending_receipt_quantity` and `get_filtered_grr_entries`) have been encapsulated within the `GoodsReceivedReceiptEntry` model, keeping `GRRTablePartialView` concise and focused on rendering.
*   **Comprehensive Tests:** Unit tests cover model properties and methods (especially `has_pending_receipt_quantity` and `get_filtered_grr_entries` method), and integration tests ensure views behave as expected, including HTMX-driven interactions. Mocking is used for `managed=False` models to avoid hitting a real external database during testing.
*   **Session Variables:** The `comp_id` and `fin_year_id` are accessed from `request.session`. Ensure your Django authentication and session middleware are correctly set up to provide these values based on the logged-in user or application context.
*   **External Database:** The `using('external_db')` calls in models and views assume you have configured an alias named `external_db` in your Django `settings.py` to point to your existing ASP.NET SQL Server database.
*   **`grr_details` URL:** The `GRRRedirectView` redirects to a URL named `grr_details`. You *must* define this URL pattern in your overall Django project's `urls.py` (or another application's `urls.py`) to point to the actual GRR detail page. For the purpose of this example, a dummy `grr_details` path was added to `inventory/urls.py` which simply points back to the `GRRListView`.