## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**

From the ASP.NET code, we infer the following:

-   **Main Data Source:** The `GridView1` is populated by the stored procedure `Sp_MRS_FillGrid`. The columns displayed in the `GridView` are `Id`, `MRSNo`, `SysDate`, `GenBy`, `FinYear`, `FinYearId`. The `loadData()` method passes `CompId` and `FinId` (FinYearId) as parameters to the stored procedure. `SessionId` (from `fun.getCode`) is also passed for employee-based filtering.
    -   **Inferred Table Name:** `tblMaterialRequisitionSlip` (or similar, based on `MaterialRequisitionSlip` context)
    -   **Inferred Columns:**
        -   `Id` (Primary Key, Integer)
        -   `MRSNo` (String/Varchar)
        -   `SysDate` (Date/DateTime)
        -   `GenBy` (String/Varchar, likely stores employee name)
        -   `FinYear` (String/Varchar)
        -   `FinYearId` (Integer)
        -   `CompId` (Integer, inferred from SP parameter)
        -   `SessionId` (Integer, inferred from SP parameter, likely corresponding to `EmpId`)

-   **Employee Data Source:** The `GetCompletionList` web method directly queries `tblHR_OfficeStaff` for employee names.
    -   **Table Name:** `tblHR_OfficeStaff`
    -   **Columns:**
        -   `EmpId` (Primary Key, Integer)
        -   `EmployeeName` (String/Varchar)
        -   `CompId` (Integer, inferred from `Session["compid"]`)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed in the ASP.NET code.

**Instructions:**

-   **Read (List & Filter):** The primary function is to display a list of "Material Requisition Slips" (`MRS`) in `GridView1`.
    -   Data is loaded via `loadData()` which calls `Sp_MRS_FillGrid`.
    -   Filtering is applied based on user selection in `drpfield` (Employee Name or MRS No) and corresponding text inputs (`txtEmpName`, `txtMrsNo`).
    -   Pagination (`AllowPaging="True"`) is handled by `GridView1_PageIndexChanging`.
-   **Search Input Toggling:** `drpfield_SelectedIndexChanged` dynamically shows/hides `txtEmpName` or `txtMrsNo`.
-   **Autocomplete:** `txtEmpName` uses `AutoCompleteExtender` which calls the `GetCompletionList` web method to suggest employee names from `tblHR_OfficeStaff`.
-   **Select/Navigate:** `GridView1_RowCommand` with `CommandName="Sel"` extracts details (`Id`, `MRSNo`, `FyId`) from the selected row and redirects the user to a separate "details/edit" page (`MaterialRequisitionSlip_MRS_Edit_Details.aspx`). There are no direct Create/Update/Delete operations for MRS on *this* specific page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

-   **Data Grid:** `asp:GridView` is used for displaying tabular data. This will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side functionality (pagination, searching, sorting) and loaded via **HTMX**.
-   **Search Dropdown:** `asp:DropDownList` (`drpfield`) for selecting search criteria. This will be an HTML `<select>` element. Its `AutoPostBack` behavior will be replicated using **HTMX's `hx-trigger="change"`** to dynamically update the search input fields.
-   **Text Inputs:** `asp:TextBox` (`txtEmpName`, `txtMrsNo`) for search queries. These will be HTML `<input type="text">` elements.
-   **Autocomplete:** `cc1:AutoCompleteExtender` for `txtEmpName`. This will be implemented using **HTMX's `hx-get`** to call a Django view that provides suggestions, with **Alpine.js** potentially managing the display of the suggestions list.
-   **Search Button:** `asp:Button` (`Button1`) to trigger the search. This will be an HTML `<button>` with **HTMX `hx-get`** or `hx-post` to re-fetch and render the table.
-   **Select Link:** `asp:LinkButton` within the `GridView` for "Select". This will be an HTML `<a>` tag or a `<button>` with **HTMX `hx-on:click`** to navigate to the MRS detail page.
-   **Styling:** Existing `CssClass` attributes (e.g., `box3`, `redbox`, `yui-datatable-theme`) will be mapped to **Tailwind CSS** classes.

### Step 4: Generate Django Code

We will create a Django application named `myapp` (or `inventory` as per the module name, but `myapp` is used for generalization).

#### 4.1 Models (`myapp/models.py`)

```python
from django.db import models
from django.urls import reverse

# Helper to provide default values for company and financial year
# In a real system, this would likely come from the authenticated user's profile
# or a session context.
def get_default_company_id():
    # Placeholder: In a real app, integrate with user session or company settings.
    return 1 

def get_default_fin_year_id():
    # Placeholder: In a real app, determine active financial year dynamically.
    return 2023 

class MaterialRequisitionSlip(models.Model):
    # Mapping 'Id' from ASP.NET GridView to primary_key
    id = models.IntegerField(db_column='Id', primary_key=True) 
    mrs_no = models.CharField(db_column='MRSNo', max_length=50) # Assuming MRSNo is a string
    sys_date = models.DateField(db_column='SysDate') # Assuming SysDate is a date without time
    generated_by = models.CharField(db_column='GenBy', max_length=100) # Assuming this stores the Employee Name as a string
    fin_year = models.CharField(db_column='FinYear', max_length=10) # Example: '2023-24'
    fin_year_id = models.IntegerField(db_column='FinYearId', default=get_default_fin_year_id) # Inferred from SP parameter
    company_id = models.IntegerField(db_column='CompId', default=get_default_company_id) # Inferred from SP parameter
    # SessionId is used for filtering by employee; assuming it corresponds to EmpId from tblHR_OfficeStaff
    session_id = models.IntegerField(db_column='SessionId', default=0) 

    class Meta:
        managed = False # Django won't manage this table's creation/deletion
        db_table = 'tblMaterialRequisitionSlip' # Inferred table name
        verbose_name = 'Material Requisition Slip'
        verbose_name_plural = 'Material Requisition Slips'

    def __str__(self):
        return f"{self.mrs_no} ({self.fin_year})"

    # Business logic method: Generates the URL for the MRS details page, mirroring ASP.NET's redirect
    def get_details_url(self):
        # This mimics the `Response.Redirect` behavior, providing query parameters.
        # This assumes a 'materialrequisitionslip_detail' URL pattern exists.
        return reverse('materialrequisitionslip_detail', kwargs={'pk': self.id}) + \
               f"?MRSNo={self.mrs_no}&FyId={self.fin_year_id}&ModId=9&SubModId=40"

class Employee(models.Model):
    # Mapping 'EmpId' from tblHR_OfficeStaff to primary_key
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company_id = models.IntegerField(db_column='CompId', default=get_default_company_id)

    class Meta:
        managed = False # Django won't manage this table's creation/deletion
        db_table = 'tblHR_OfficeStaff' # Explicitly mentioned in GetCompletionList
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

    # Business logic method: Replicates `fun.getCode()` behavior if it returns EmpId
    def get_employee_code(self):
        return self.id
```

#### 4.2 Forms (`myapp/forms.py`)

```python
from django import forms
from .models import MaterialRequisitionSlip, Employee

# This form is for standard CRUD operations for MaterialRequisitionSlip.
# It's included as per the template, but not directly used by the current list page's search.
class MaterialRequisitionSlipForm(forms.ModelForm):
    class Meta:
        model = MaterialRequisitionSlip
        fields = ['mrs_no', 'sys_date', 'generated_by', 'fin_year', 'fin_year_id']
        widgets = {
            'mrs_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'generated_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = { # Custom labels for better UI
            'mrs_no': 'MRS Number',
            'sys_date': 'Date',
            'generated_by': 'Generated By',
            'fin_year': 'Financial Year',
            'fin_year_id': 'Financial Year ID',
        }

# This form is specifically for the search/filter functionality on the list page.
# It uses choice fields and text inputs to replicate the ASP.NET search controls.
class MaterialRequisitionSlipSearchForm(forms.Form):
    SEARCH_FIELD_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'MRS No'),
    ]
    # Replicates drpfield
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm p-2',
            'name': 'search_field',
            'hx-post': 'search-form-partial/', # HTMX post to update input visibility
            'hx-target': '#search-inputs-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change' # Replicates AutoPostBack="True"
        })
    )
    # Replicates txtEmpName
    employee_name_text = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-96 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Employee Name',
            'name': 'employee_name_text', 
            'hx-get': '/myapp/employee/autocomplete/', # Autocomplete URL for HTMX
            'hx-trigger': 'keyup changed delay:500ms, search', # Triggers on keyup after delay, or search event
            'hx-target': '#autocomplete-results', # Target for autocomplete suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser's native autocomplete
            'x-on:click.outside': '$el.innerHTML = \'\';', # Clear results if clicked outside using Alpine.js
            'x-on:keyup': 'document.getElementById("autocomplete-results").classList.remove("hidden")', # Show suggestions on keyup
            'x-on:focus': 'document.getElementById("autocomplete-results").classList.remove("hidden")', # Show suggestions on focus
        })
    )
    # Replicates txtMrsNo
    mrs_no_text = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-96 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter MRS No',
            'name': 'mrs_no_text' 
        })
    )

    # Custom validation/cleaning to extract employee ID from autocomplete text
    def clean_employee_name_text(self):
        employee_name = self.cleaned_data.get('employee_name_text')
        if employee_name:
            # Check if the format is "Name [ID]" from autocomplete
            if ' [' in employee_name and employee_name.endswith(']'):
                try:
                    emp_id_str = employee_name.split(' [')[-1][:-1]
                    # Return just the ID for filtering
                    return int(emp_id_str)
                except ValueError:
                    pass # Malformed ID, proceed with name search or no filter
            # If not in "Name [ID]" format, assume direct name search or no specific ID is chosen yet
            # In a real scenario, this would ideally use the ID passed by autocomplete or ensure exact match.
            # For robustness, we allow searching by name if ID extraction fails.
        return employee_name # Return name if no ID extracted or if it's just a partial type

```

#### 4.3 Views (`myapp/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import Q 
from .models import MaterialRequisitionSlip, Employee
from .forms import MaterialRequisitionSlipForm, MaterialRequisitionSlipSearchForm

# Helper function to get company_id and fin_year_id from session
# In a real application, this would be robustly tied to user authentication and profile.
def get_user_context_data(request):
    company_id = request.session.get('compid', 1)  # Default to 1 if not in session
    fin_year_id = request.session.get('finyear', 2023) # Default to 2023 if not in session
    return company_id, fin_year_id

# Main list view for Material Requisition Slips
class MaterialRequisitionSlipListView(ListView):
    model = MaterialRequisitionSlip
    template_name = 'myapp/materialrequisitionslip/list.html'
    context_object_name = 'material_requisition_slips'
    paginate_by = 20 # Replicates ASP.NET GridView's PageSize

    # This method contains the business logic for filtering the queryset.
    # It's extracted from get_queryset to keep views thin.
    def _get_filtered_queryset(self):
        company_id, fin_year_id = get_user_context_data(self.request)
        
        # Start with base queryset filtered by company and financial year (similar to SP parameters)
        queryset = MaterialRequisitionSlip.objects.filter(company_id=company_id, fin_year_id=fin_year_id)

        # Retrieve search parameters from GET request (submitted by HTMX form)
        search_field = self.request.GET.get('search_field', '0') # Default to Employee Name
        employee_search_value = self.request.GET.get('employee_name_text', '')
        mrs_no_text = self.request.GET.get('mrs_no_text', '')

        if search_field == '1' and mrs_no_text: # Search by MRS No
            # ASP.NET used exact match: MRSNo='" + txtMrsNo.Text + "'"
            queryset = queryset.filter(mrs_no__iexact=mrs_no_text) 
        elif search_field == '0' and employee_search_value: # Search by Employee Name
            # The employee_search_value can be an EmpId (if from autocomplete selection) or a name string
            try:
                # If employee_search_value is an integer, it's an EmpId (fun.getCode behavior)
                employee_session_id = int(employee_search_value)
                queryset = queryset.filter(session_id=employee_session_id)
            except ValueError:
                # Otherwise, it's a name, search by contains on 'generated_by' field
                queryset = queryset.filter(generated_by__icontains=employee_search_value)

        return queryset.order_by('-sys_date', 'mrs_no') # Order results

    def get_queryset(self):
        # Calls the private helper method to apply filtering
        return self._get_filtered_queryset()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form with current GET parameters for sticky values
        search_field = self.request.GET.get('search_field', '0')
        employee_name_text = self.request.GET.get('employee_name_text', '')
        mrs_no_text = self.request.GET.get('mrs_no_text', '')

        context['search_form'] = MaterialRequisitionSlipSearchForm(initial={
            'search_field': search_field,
            'employee_name_text': employee_name_text, # Will be handled by Alpine.js for display
            'mrs_no_text': mrs_no_text
        })
        context['current_search_field'] = search_field # Used for initial visibility control in template
        return context

# HTMX partial view for rendering only the MRS table content
class MaterialRequisitionSlipTablePartialView(MaterialRequisitionSlipListView):
    template_name = 'myapp/materialrequisitionslip/_mrs_table.html'

    def get(self, request, *args, **kwargs):
        # This method is called by HTMX requests to refresh the table.
        # It leverages the filtering logic from the parent ListView.
        # It's intentionally concise, adhering to thin view principles.
        return super().get(request, *args, **kwargs)

# HTMX partial view for dynamically rendering search input fields based on dropdown selection
class MaterialRequisitionSlipSearchFormPartialView(View):
    def post(self, request, *args, **kwargs):
        # Create a form instance from POST data to access `search_field`
        form = MaterialRequisitionSlipSearchForm(request.POST)
        selected_field = form.data.get('search_field', '0') # Get the newly selected value

        # Retrieve current text input values to persist them
        employee_name_text_value = form.data.get('employee_name_text', '')
        mrs_no_text_value = form.data.get('mrs_no_text', '')
        
        # Render the partial HTML for the search inputs
        context = {
            'search_form': form, # Pass the form instance for rendering fields
            'current_search_field': selected_field,
            'employee_name_text_value': employee_name_text_value,
            'mrs_no_text_value': mrs_no_text_value,
        }
        # Render a specific partial template for just the inputs section
        return HttpResponse(render_to_string('myapp/materialrequisitionslip/_search_inputs.html', context, request))

# HTMX endpoint for Employee Autocomplete
class EmployeeAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('employee_name_text', '') # Matches the name attribute in the form field
        company_id, _ = get_user_context_data(request)
        
        results = []
        if query:
            # Filter employees by company_id and name starting with the query (case-insensitive)
            # Mimicking ASP.NET's `StartsWith` and `CompletionSetCount` (limit 10)
            employees = Employee.objects.filter(
                Q(company_id=company_id) & Q(employee_name__istartswith=query)
            ).order_by('employee_name')[:10] 

            # Format results as "EmployeeName [EmpId]" to replicate ASP.NET's output
            results = [f"{emp.employee_name} [{emp.id}]" for emp in employees]
        
        # Return HTML fragments for HTMX to inject directly into a list/div
        # This approach simplifies frontend Alpine.js logic for autocomplete.
        html_results = "\n".join([
            f'<div class="p-2 cursor-pointer hover:bg-gray-100" '
            f'hx-on:click="document.getElementById(\'id_employee_name_text\').value=\'{res}\'; '
            f'document.getElementById(\'autocomplete-results\').innerHTML=\'\'; '
            f'document.getElementById(\'autocomplete-results\').classList.add(\'hidden\');" '
            f'>{res}</div>' 
            for res in results
        ])
        return HttpResponse(html_results)

# Placeholder CRUD views as requested by the template structure,
# even though the original ASP.NET page primarily focused on listing and searching.
class MaterialRequisitionSlipCreateView(CreateView):
    model = MaterialRequisitionSlip
    form_class = MaterialRequisitionSlipForm
    template_name = 'myapp/materialrequisitionslip/form.html'
    success_url = reverse_lazy('materialrequisitionslip_list')

    def form_valid(self, form):
        # Automatically set company_id and fin_year_id before saving
        company_id, fin_year_id = get_user_context_data(self.request)
        form.instance.company_id = company_id
        form.instance.fin_year_id = fin_year_id
        
        response = super().form_valid(form)
        messages.success(self.request, 'Material Requisition Slip added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success for HTMX
                headers={
                    'HX-Trigger': 'refreshMaterialRequisitionSlipList' # Custom HTMX trigger to refresh list
                }
            )
        return response

class MaterialRequisitionSlipUpdateView(UpdateView):
    model = MaterialRequisitionSlip
    form_class = MaterialRequisitionSlipForm
    template_name = 'myapp/materialrequisitionslip/form.html'
    success_url = reverse_lazy('materialrequisitionslip_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Requisition Slip updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialRequisitionSlipList'
                }
            )
        return response

class MaterialRequisitionSlipDeleteView(DeleteView):
    model = MaterialRequisitionSlip
    template_name = 'myapp/materialrequisitionslip/confirm_delete.html'
    success_url = reverse_lazy('materialrequisitionslip_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Requisition Slip deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialRequisitionSlipList'
                }
            )
        return response

# This view represents the target of the ASP.NET `Response.Redirect` from the "Select" button.
# It's a placeholder for MaterialRequisitionSlip_MRS_Edit_Details.aspx.
class MaterialRequisitionSlipDetailView(View):
    def get(self, request, pk, *args, **kwargs):
        # In a full application, this would fetch the MRS details and render a dedicated detail page.
        # For this migration context, it demonstrates how to handle the redirect.
        # You would fetch: mrs = MaterialRequisitionSlip.objects.get(pk=pk)
        mrs_no = request.GET.get('MRSNo', 'N/A')
        fin_year_id = request.GET.get('FyId', 'N/A')
        messages.info(request, f"Viewing details for MRS ID: {pk}, MRS No: {mrs_no}, Fin Year ID: {fin_year_id}.")
        return HttpResponse(f"<h1>Material Requisition Slip Details for ID: {pk}</h1><p>This is a placeholder for the details page ('MaterialRequisitionSlip_MRS_Edit_Details.aspx').</p><p>MRS No: {mrs_no}, Financial Year ID: {fin_year_id}</p><a href='{reverse_lazy('materialrequisitionslip_list')}' class='text-blue-500 hover:underline'>Back to List</a>", status=200)

```

#### 4.4 Templates (`myapp/templates/myapp/materialrequisitionslip/`)

**`list.html`** (Main list page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Requisition Slip [MRS] - Edit</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'materialrequisitionslip_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New MRS
        </button>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <div x-data="{ currentSearchField: '{{ current_search_field }}' }" >
            <form hx-get="{% url 'materialrequisitionslip_table' %}" hx-target="#materialrequisitionslipTable-container" hx-swap="innerHTML" hx-trigger="submit">
                {% csrf_token %}
                <div class="flex items-center space-x-4 mb-4">
                    <label for="{{ search_form.search_field.id_for_label }}" class="font-medium text-gray-700">Search By:</label>
                    {{ search_form.search_field }}
                    
                    <div id="search-inputs-container" class="flex-grow">
                        <!-- Search inputs will be dynamically loaded here by HTMX post to search-form-partial -->
                        {% include 'myapp/materialrequisitionslip/_search_inputs.html' with search_form=search_form current_search_field=current_search_field employee_name_text_value=search_form.employee_name_text.value mrs_no_text_value=search_form.mrs_no_text.value %}
                    </div>

                    <button id="search-button" type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Search
                    </button>
                </div>
                <div id="autocomplete-results" class="relative bg-white border border-gray-300 rounded-md shadow-lg z-10 hidden w-96">
                    <!-- Autocomplete suggestions will be loaded here via HTMX -->
                </div>
            </form>
        </div>
    </div>

    <div id="materialrequisitionslipTable-container"
         hx-trigger="load, refreshMaterialRequisitionSlipList from:body"
         hx-get="{% url 'materialrequisitionslip_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Requisition Slips...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css"/>
<script type="text/javascript" src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
{% endblock %}
```

**`_mrs_table.html`** (Partial template for DataTables)

```html
<div class="overflow-x-auto rounded-lg shadow-md">
    {% if material_requisition_slips %}
    <table id="materialrequisitionslipTable" class="min-w-full bg-white">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for mrs in material_requisition_slips %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ mrs.mrs_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ mrs.sys_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ mrs.generated_by }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ mrs.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <a 
                        href="{{ mrs.get_details_url }}" 
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-on:click="window.location.href='{{ mrs.get_details_url }}'; return false;">{# Replicates Response.Redirect #}
                        Select
                    </a>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'materialrequisitionslip_edit' mrs.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'materialrequisitionslip_delete' mrs.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-8">
        <p class="text-lg font-medium text-maroon">No data to display !</p>
    </div>
    {% endif %}
</div>

<script>
// DataTables initialization
$(document).ready(function() {
    $('#materialrequisitionslipTable').DataTable({
        "pageLength": 20, // Initial page size matching ASP.NET
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]], // Options for page size
        "searching": true, // Enable built-in search box
        "paging": true, // Enable pagination
        "info": true, // Show info about entries
        "ordering": true // Enable column ordering
    });
});
</script>
```

**`_search_inputs.html`** (Partial template for dynamic search input visibility)

```html
<div x-show="currentSearchField === '0'" x-cloak>
    {{ search_form.employee_name_text }}
</div>
<div x-show="currentSearchField === '1'" x-cloak>
    {{ search_form.mrs_no_text }}
</div>

<script>
    // This script runs when _search_inputs.html is loaded by HTMX
    // It updates the Alpine.js state in the parent list.html
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'search-inputs-container') {
            let selectElement = document.getElementById('id_search_field');
            if (selectElement) {
                // Ensure Alpine.js knows about the current selection
                document.getElementById('search-inputs-container')._x_dataStack[0].currentSearchField = selectElement.value;
            }
        }
    });
</script>
```

**`form.html`** (Partial template for Add/Edit forms - for modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Requisition Slip</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial template for delete confirmation - for modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete MRS "{{ object }}"?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`myapp/urls.py`)

```python
from django.urls import path
from .views import (
    MaterialRequisitionSlipListView, 
    MaterialRequisitionSlipTablePartialView,
    MaterialRequisitionSlipSearchFormPartialView,
    EmployeeAutocompleteView,
    MaterialRequisitionSlipCreateView, 
    MaterialRequisitionSlipUpdateView, 
    MaterialRequisitionSlipDeleteView,
    MaterialRequisitionSlipDetailView, # For the 'Select' action's target
)

urlpatterns = [
    # Main MRS list page
    path('materialrequisitionslip/', MaterialRequisitionSlipListView.as_view(), name='materialrequisitionslip_list'),
    
    # HTMX endpoints for dynamic updates
    path('materialrequisitionslip/table/', MaterialRequisitionSlipTablePartialView.as_view(), name='materialrequisitionslip_table'),
    path('materialrequisitionslip/search-form-partial/', MaterialRequisitionSlipSearchFormPartialView.as_view(), name='materialrequisitionslip_search_form_partial'),
    path('employee/autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Placeholder CRUD operations (for modal forms)
    path('materialrequisitionslip/add/', MaterialRequisitionSlipCreateView.as_view(), name='materialrequisitionslip_add'),
    path('materialrequisitionslip/edit/<int:pk>/', MaterialRequisitionSlipUpdateView.as_view(), name='materialrequisitionslip_edit'),
    path('materialrequisitionslip/delete/<int:pk>/', MaterialRequisitionSlipDeleteView.as_view(), name='materialrequisitionslip_delete'),

    # Target for the 'Select' action (MaterialRequisitionSlip_MRS_Edit_Details.aspx equivalent)
    path('materialrequisitionslip/details/<int:pk>/', MaterialRequisitionSlipDetailView.as_view(), name='materialrequisitionslip_detail'),
]
```

#### 4.6 Tests (`myapp/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialRequisitionSlip, Employee
from django.contrib.messages import get_messages
from django.conf import settings

# Mock session data for testing
# This needs to be set up in settings.py for tests, or as a fixture
# For simplicity in this test, we'll manually set session data if needed.
# Usually, you'd use @override_settings or a custom test client.
# For unit tests, we can mock the session. For integration tests, modify the client.

class MaterialRequisitionSlipModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test company and financial year
        cls.company_id = 1
        cls.fin_year_id = 2023
        
        # Create a test employee
        Employee.objects.create(id=101, employee_name='John Doe', company_id=cls.company_id)
        Employee.objects.create(id=102, employee_name='Jane Smith', company_id=cls.company_id)
        Employee.objects.create(id=103, employee_name='Alice Brown', company_id=cls.company_id)

        # Create test MRS data
        MaterialRequisitionSlip.objects.create(
            id=1, mrs_no='MRS001', sys_date='2023-01-15', generated_by='John Doe', 
            fin_year='2023-24', fin_year_id=cls.fin_year_id, company_id=cls.company_id, session_id=101
        )
        MaterialRequisitionSlip.objects.create(
            id=2, mrs_no='MRS002', sys_date='2023-02-20', generated_by='Jane Smith', 
            fin_year='2023-24', fin_year_id=cls.fin_year_id, company_id=cls.company_id, session_id=102
        )
        MaterialRequisitionSlip.objects.create(
            id=3, mrs_no='MRS003', sys_date='2022-11-01', generated_by='John Doe', 
            fin_year='2022-23', fin_year_id=2022, company_id=cls.company_id, session_id=101
        )
        MaterialRequisitionSlip.objects.create(
            id=4, mrs_no='MRS004', sys_date='2023-03-10', generated_by='Alice Brown', 
            fin_year='2023-24', fin_year_id=cls.fin_year_id, company_id=cls.company_id, session_id=103
        )
  
    def test_mrs_creation(self):
        mrs = MaterialRequisitionSlip.objects.get(id=1)
        self.assertEqual(mrs.mrs_no, 'MRS001')
        self.assertEqual(mrs.generated_by, 'John Doe')
        self.assertEqual(str(mrs.sys_date), '2023-01-15')
        self.assertEqual(mrs.fin_year_id, self.fin_year_id)
        self.assertEqual(mrs.company_id, self.company_id)
        self.assertEqual(mrs.session_id, 101)
        
    def test_employee_creation(self):
        emp = Employee.objects.get(id=101)
        self.assertEqual(emp.employee_name, 'John Doe')
        self.assertEqual(emp.company_id, self.company_id)
        self.assertEqual(emp.get_employee_code(), 101)

    def test_mrs_details_url(self):
        mrs = MaterialRequisitionSlip.objects.get(id=1)
        expected_url = f'/myapp/materialrequisitionslip/details/1/?MRSNo=MRS001&FyId={self.fin_year_id}&ModId=9&SubModId=40'
        self.assertEqual(mrs.get_details_url(), expected_url)

class MaterialRequisitionSlipViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all view tests
        cls.company_id = 1
        cls.fin_year_id = 2023
        # Mock session for tests
        # This approach requires setting up the session engine in settings for testing
        # For a quick mock, we'll manually set session on the client.
        
        Employee.objects.create(id=101, employee_name='John Doe', company_id=cls.company_id)
        Employee.objects.create(id=102, employee_name='Jane Smith', company_id=cls.company_id)
        Employee.objects.create(id=103, employee_name='Alice Brown', company_id=cls.company_id)

        MaterialRequisitionSlip.objects.create(
            id=1, mrs_no='MRS001', sys_date='2023-01-15', generated_by='John Doe', 
            fin_year='2023-24', fin_year_id=cls.fin_year_id, company_id=cls.company_id, session_id=101
        )
        MaterialRequisitionSlip.objects.create(
            id=2, mrs_no='MRS002', sys_date='2023-02-20', generated_by='Jane Smith', 
            fin_year='2023-24', fin_year_id=cls.fin_year_id, company_id=cls.company_id, session_id=102
        )
        MaterialRequisitionSlip.objects.create(
            id=3, mrs_no='MRS003', sys_date='2022-11-01', generated_by='John Doe', 
            fin_year='2022-23', fin_year_id=2022, company_id=cls.company_id, session_id=101
        )
        MaterialRequisitionSlip.objects.create(
            id=4, mrs_no='MRS004', sys_date='2023-03-10', generated_by='Alice Brown', 
            fin_year='2023-24', fin_year_id=cls.fin_year_id, company_id=cls.company_id, session_id=103
        )
    
    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.fin_year_id
        session.save()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('materialrequisitionslip_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'myapp/materialrequisitionslip/list.html')
        self.assertTrue('material_requisition_slips' in response.context)
        # Should only get MRS for current fin year and company
        self.assertEqual(response.context['material_requisition_slips'].count(), 3)
        self.assertContains(response, 'MRS001')
        self.assertNotContains(response, 'MRS003') # From a different financial year

    def test_list_view_search_by_mrs_no(self):
        response = self.client.get(reverse('materialrequisitionslip_list'), {'search_field': '1', 'mrs_no_text': 'MRS002'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['material_requisition_slips'].count(), 1)
        self.assertContains(response, 'MRS002')
        self.assertNotContains(response, 'MRS001')

    def test_list_view_search_by_employee_name_with_id(self):
        # Mimic autocomplete selection: "John Doe [101]"
        response = self.client.get(reverse('materialrequisitionslip_list'), {'search_field': '0', 'employee_name_text': 'John Doe [101]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['material_requisition_slips'].count(), 1) # MRS001
        self.assertContains(response, 'MRS001')
        self.assertNotContains(response, 'MRS002')

    def test_list_view_search_by_employee_name_partial(self):
        # Mimic direct typing without selecting ID
        response = self.client.get(reverse('materialrequisitionslip_list'), {'search_field': '0', 'employee_name_text': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['material_requisition_slips'].count(), 1) # MRS001
        self.assertContains(response, 'MRS001')
        self.assertNotContains(response, 'MRS002')

    def test_mrs_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialrequisitionslip_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'myapp/materialrequisitionslip/_mrs_table.html')
        self.assertContains(response, '<table id="materialrequisitionslipTable"')
        self.assertContains(response, 'MRS001')

    def test_search_form_partial_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialrequisitionslip_search_form_partial'), {'search_field': '1'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'myapp/materialrequisitionslip/_search_inputs.html')
        self.assertContains(response, 'id="id_mrs_no_text"')
        self.assertNotContains(response, 'id="id_employee_name_text"')

    def test_employee_autocomplete_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('employee_autocomplete'), {'employee_name_text': 'john'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe [101]')
        self.assertNotContains(response, 'Jane Smith')
        self.assertGreater(len(response.content), 0)

    def test_employee_autocomplete_view_empty_query(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('employee_autocomplete'), {'employee_name_text': ''}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), '') # Should return empty string

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialrequisitionslip_add'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'myapp/materialrequisitionslip/form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post(self):
        new_mrs_id = 5
        data = {
            'id': new_mrs_id, # Manually setting ID for managed=False, or auto-increment handled by DB
            'mrs_no': 'MRS005', 
            'sys_date': '2023-04-01', 
            'generated_by': 'Alice Brown',
            'fin_year': '2023-24', 
            'fin_year_id': self.fin_year_id,
            # company_id and session_id are set in form_valid
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialrequisitionslip_add'), data, headers=headers)
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for successful ops
        self.assertTrue(MaterialRequisitionSlip.objects.filter(mrs_no='MRS005').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Material Requisition Slip added successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionSlipList')

    def test_update_view_get(self):
        mrs = MaterialRequisitionSlip.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialrequisitionslip_edit', args=[mrs.id]), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'myapp/materialrequisitionslip/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, mrs)
        
    def test_update_view_post(self):
        mrs = MaterialRequisitionSlip.objects.get(id=1)
        data = {
            'id': mrs.id, # Must include PK for managed=False
            'mrs_no': 'MRS001_Updated', 
            'sys_date': mrs.sys_date, 
            'generated_by': mrs.generated_by,
            'fin_year': mrs.fin_year, 
            'fin_year_id': mrs.fin_year_id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialrequisitionslip_edit', args=[mrs.id]), data, headers=headers)
        self.assertEqual(response.status_code, 204)
        mrs.refresh_from_db()
        self.assertEqual(mrs.mrs_no, 'MRS001_Updated')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Material Requisition Slip updated successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionSlipList')

    def test_delete_view_get(self):
        mrs = MaterialRequisitionSlip.objects.get(id=2)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialrequisitionslip_delete', args=[mrs.id]), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'myapp/materialrequisitionslip/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], mrs)

    def test_delete_view_post(self):
        mrs_to_delete_id = 2
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialrequisitionslip_delete', args=[mrs_to_delete_id]), headers=headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialRequisitionSlip.objects.filter(id=mrs_to_delete_id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Material Requisition Slip deleted successfully.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionSlipList')

    def test_detail_view_get(self):
        mrs = MaterialRequisitionSlip.objects.get(id=1)
        response = self.client.get(mrs.get_details_url())
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<h1>Material Requisition Slip Details for ID: 1</h1>')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), f"Viewing details for MRS ID: {mrs.id}, MRS No: {mrs.mrs_no}, Fin Year ID: {mrs.fin_year_id}.")
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Search/Filter:**
    -   The `drpfield` `select` element in `list.html` uses `hx-post="{% url 'materialrequisitionslip_search_form_partial' %}"`, `hx-target="#search-inputs-container"`, `hx-swap="innerHTML"`, and `hx-trigger="change"` to dynamically update the search input fields when the dropdown changes.
    -   The search form itself (`<form hx-get="{% url 'materialrequisitionslip_table' %}" ... hx-trigger="submit">`) triggers a GET request to `materialrequisitionslip_table` when submitted, dynamically refreshing only the DataTables section.
-   **HTMX for DataTables Update:**
    -   The `div` with `id="materialrequisitionslipTable-container"` in `list.html` uses `hx-trigger="load, refreshMaterialRequisitionSlipList from:body"` and `hx-get="{% url 'materialrequisitionslip_table' %}"` to load the table content initially and refresh it after CRUD operations. The `refreshMaterialRequisitionSlipList` is a custom event triggered by the Django views (e.g., in `form_valid` of `CreateView`, `UpdateView`, `DeleteView`) using `HX-Trigger` header.
-   **HTMX for Modals (CRUD):**
    -   Buttons like "Add New MRS", "Edit", and "Delete" in `list.html` and `_mrs_table.html` use `hx-get` to fetch the respective form/confirmation templates (`form.html`, `confirm_delete.html`) into the `#modalContent` div.
    -   Form submissions within the modal (`form.html`, `confirm_delete.html`) use `hx-post="{{ request.path }}" hx-swap="none"` to submit data without full page refresh. The Django views respond with `status=204` and `HX-Trigger` headers to close the modal and refresh the list.
-   **HTMX for Autocomplete:**
    -   The `employee_name_text` input in `_search_inputs.html` uses `hx-get="{% url 'employee_autocomplete' %}"`, `hx-trigger="keyup changed delay:500ms, search"`, `hx-target="#autocomplete-results"`, and `hx-swap="innerHTML"`. The `EmployeeAutocompleteView` returns HTML `div` elements, which HTMX injects.
-   **Alpine.js for UI State Management:**
    -   The `x-data="{ currentSearchField: '{{ current_search_field }}' }"` attribute in `list.html` manages the state of which search input field should be visible.
    -   `x-show` directives on the input `div`s in `_search_inputs.html` (`x-show="currentSearchField === '0'"` etc.) control their visibility based on the Alpine.js `currentSearchField` variable.
    -   Alpine.js `x-on:click.outside` on `#autocomplete-results` hides the autocomplete suggestions when clicking outside the input area. `x-on:keyup` and `x-on:focus` manage visibility.
    -   Modal visibility is managed with `hidden is-active` classes and `_=` attribute for `on click` outside, which is part of htmx/alpine integration.
-   **DataTables.js:**
    -   Initialized in a `script` tag within `_mrs_table.html` using `$(document).ready(function() { $('#materialrequisitionslipTable').DataTable(...) });`. This script runs every time the partial `_mrs_table.html` is loaded by HTMX, ensuring the table is correctly re-initialized for client-side functionality.

**Final Notes:**

-   **Placeholders:** Remember to replace placeholder `[APP_NAME]` with your actual Django app name (e.g., `myapp` as used here, or `inventory`).
-   **CSS:** Tailwind CSS classes (`block`, `w-full`, `px-3`, `py-2`, `border`, `rounded-md`, `shadow-sm`, `focus:outline-none`, `focus:ring-indigo-500`, `focus:border-indigo-500`, `sm:text-sm`, `bg-blue-500`, `hover:bg-blue-700`, `text-white`, `font-bold`, `py-2`, `px-4`) are extensively used for modern styling, replacing the legacy `CssClass` attributes.
-   **Business Logic in Models:** The `_get_filtered_queryset` method in `MaterialRequisitionSlipListView` demonstrates moving filtering logic (which was in ASP.NET's `loadData()`) into the view's `get_queryset` but it's a private helper for the view. More complex calculations or state changes would be pushed directly into model methods.
-   **Database:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database (or a compatible database if migrating data). The `managed = False` in models ensures Django does not attempt to create or alter these tables.
-   **Session Management:** The `get_user_context_data` helper function is a simplified placeholder. In a real application, Django's authentication system and session middleware would be properly configured to manage `CompId` and `FinYearId` for the authenticated user.
-   **Error Handling:** The original ASP.NET code had broad `try-catch` blocks. In Django, error handling typically involves middleware, custom exception handling, or specific form/view validation. This migration focuses on the functional aspect, assuming robust error handling is implemented at a broader application level.