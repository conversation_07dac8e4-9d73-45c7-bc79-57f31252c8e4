Your organization's transition from legacy ASP.NET to modern Django is a strategic move that will significantly enhance agility, maintainability, and scalability. By adopting Django 5.0+ with HTMX, Alpine.js, and DataTables, we are building a highly responsive, robust, and cost-effective system that aligns with current web development best practices. This approach prioritizes automation and clean architecture, reducing manual effort and future technical debt.

This plan details the migration of the `WIS_ActualRun_Material.aspx` and its C# code-behind to a Django-based solution, focusing on business value and automated translation wherever possible.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code's SQL commands and data structures, the following database tables and their inferred columns are identified:

*   **`tblDG_BOM_Master`** (Bill of Material Master)
    *   `CId` (int): Child Component ID (often acts as a primary key or part of composite key)
    *   `PId` (int): Parent Component ID
    *   `ItemId` (int): Item ID (Foreign Key to `tblDG_Item_Master`)
    *   `WONo` (string): Work Order Number (Foreign Key to `SD_Cust_WorkOrder_Master`)
    *   `Qty` (double): Quantity of the child component required for the parent

*   **`tblDG_Item_Master`** (Item Master)
    *   `Id` (int): Item ID (Primary Key)
    *   `ManfDesc` (string): Manufacturing Description
    *   `StockQty` (double): Current Stock Quantity
    *   `UOMBasic` (int): Unit of Measure Basic ID (Foreign Key to `Unit_Master`)

*   **`Unit_Master`** (Unit Master)
    *   `Id` (int): Unit ID (Primary Key)
    *   `Symbol` (string): Unit Symbol (e.g., "KG", "PCS")

*   **`tblInv_WIS_Master`** (Work In Progress Issue Slip Master)
    *   `Id` (int): Primary Key for WIS Master
    *   `WISNo` (string): Unique WIS Number
    *   `SysDate` (string): System Date of transaction
    *   `SysTime` (string): System Time of transaction
    *   `CompId` (int): Company ID
    *   `SessionId` (string): User Session ID (who performed the transaction)
    *   `FinYearId` (int): Financial Year ID
    *   `WONo` (string): Work Order Number (Foreign Key to `SD_Cust_WorkOrder_Master`)

*   **`tblInv_WIS_Details`** (Work In Progress Issue Slip Details)
    *   `WISNo` (string): WIS Number (Foreign Key to `tblInv_WIS_Master`)
    *   `PId` (int): Parent Component ID
    *   `CId` (int): Child Component ID
    *   `ItemId` (int): Item ID (Foreign Key to `tblDG_Item_Master`)
    *   `IssuedQty` (double): Quantity of material issued
    *   `MId` (int): Master ID (Foreign Key to `tblInv_WIS_Master.Id`)

*   **`SD_Cust_WorkOrder_Master`** (Work Order Master)
    *   `WONo` (string): Work Order Number (Primary Key or part of composite primary key)
    *   `CompId` (int): Company ID (Part of Composite Primary Key if applicable)
    *   `DryActualRun` (int): Flag (0 or 1) indicating if the dry/actual run process has been completed for this work order.

### Step 2: Identify Backend Functionality

The ASP.NET code primarily performs two major functions:
1.  **Displaying Material Requirements (Read Operation):** It fetches Bill of Material (BOM) data, enriches it with item details, and calculates various quantities like `BOM Qty`, `Total WIS Issued Qty`, `Balance BOM Qty`, `Dry Run Qty` (how much can be issued now), and `After Stock Qty`. This involves complex recursive calculations across the BOM hierarchy and querying existing inventory records.
2.  **Processing Actual Material Run (Transactional Create/Update Operation):** Triggered by the "Actual Run of Material" button, this function records the actual issuance of materials. It generates a new Work In Progress Issue Slip (WIS) number, inserts master and detail records into `tblInv_WIS_Master` and `tblInv_WIS_Details`, and updates the stock quantity in `tblDG_Item_Master` for each issued item. Finally, it flags the Work Order as processed.

### Step 3: Infer UI Components

The original ASP.NET page used a Telerik RadTreeList for a hierarchical display of materials. This will be transformed into a standard HTML table enhanced with DataTables, providing robust client-side features like searching, sorting, and pagination.

*   **Page Title:** Displays the Work Order Number.
*   **Action Bar:** Includes an "Expand Tree" checkbox (behavior will be adapted for DataTables), an "Actual Run of Material" button, and a "Cancel" button.
*   **Message Area:** A dedicated space to display success or error messages.
*   **Data Grid:** A DataTables-enabled table to display BOM items with columns for `Item Code`, `Description`, `UOM`, `Unit Qty`, `BOM Qty`, `Tot. WIS Qty`, `Balance BOM Qty`, `Stock Qty`, `Dry Run Qty`, and `After Stock Qty`. Hidden columns like `PId`, `CId`, `ItemId` will be available in the underlying data for internal use or future advanced features.
*   **Per-Row Action:** A "Select" button/icon on each row, likely for viewing more details or triggering a report/print for that specific item.

### Step 4: Generate Django Code

**Django Application Name:** `inventory_transactions`
**Module Name:** `wis_material_run` (within the `inventory_transactions` app)

#### 4.1 Models (`inventory_transactions/models.py`)

Models are designed to map directly to existing database tables (`managed = False`). Critical business logic and complex calculations are encapsulated within model methods or custom managers, adhering to the "Fat Model" principle. Precision for quantities (`DecimalField`) is used to match the `double`/`decimal` handling in C#.

```python
import decimal
from django.db import models, transaction
from django.db.models import Sum, F
from django.utils import timezone
from django.core.exceptions import ObjectDoesNotExist

# --- Placeholder Models (replace with actual imports/definitions from your project) ---
# These models are assumed to exist in your broader ERP Django project.
# For this exercise, simplified versions are provided.

class Company(models.Model):
    comp_id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255)
    class Meta:
        managed = False
        db_table = 'Company_Master'
    def __str__(self): return self.name

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_name = models.CharField(max_length=50)
    class Meta:
        managed = False
        db_table = 'Financial_Year_Master'
    def __str__(self): return self.year_name

class CustomUser(models.Model): # Renamed to CustomUser to avoid conflict with auth.User
    username = models.CharField(max_length=150, unique=True, db_column='username')
    class Meta:
        managed = False
        db_table = 'auth_user' # Or your custom user table
    def __str__(self): return self.username

class WorkOrderMaster(models.Model):
    won_o = models.CharField(max_length=50, db_column='WONo', primary_key=True)
    company = models.ForeignKey(Company, on_delete=models.PROTECT, db_column='CompId', related_name='work_orders')
    dry_actual_run = models.BooleanField(db_column='DryActualRun', default=False)
    # Add other fields from SD_Cust_WorkOrder_Master as needed
    
    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        unique_together = (('won_o', 'company'),) # Example of composite PK

    def __str__(self):
        return self.won_o

    @classmethod
    def mark_dry_actual_run_completed(cls, won_o_value, company_id_value):
        """
        Marks a Work Order as having completed its dry/actual run process.
        Corresponds to the update on SD_Cust_WorkOrder_Master in Button2_Click.
        """
        try:
            with transaction.atomic():
                obj = cls.objects.get(won_o=won_o_value, company_id=company_id_value)
                obj.dry_actual_run = True
                obj.save()
                return True
        except cls.DoesNotExist:
            return False
        except Exception as e:
            print(f"Error marking work order as completed: {e}") # Log error
            return False

# --- End Placeholder Models ---

class UnitMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol


class ItemMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # Corresponds to ItemId in BOM context
    manf_desc = models.CharField(max_length=255, db_column='ManfDesc')
    stock_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='StockQty')
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.PROTECT, db_column='UOMBasic', related_name='items')
    # Assuming 'Item Code' is either a property or another field.
    # For now, it's a derived property as per the C# `fun.GetItemCode_PartNo`

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    @property
    def item_code(self):
        """
        Mimics fun.GetItemCode_PartNo. In a real system, this might be a direct DB field
        or a lookup. For this example, a simplified derived value.
        """
        return f"ITEM-{self.id}" # Placeholder

    def update_stock_quantity(self, change_amount):
        """
        Updates the stock quantity for this item, typically decrementing it.
        Args:
            change_amount (Decimal): The quantity to add (positive) or subtract (negative).
        """
        with transaction.atomic():
            self.stock_qty = F('stock_qty') + change_amount
            self.save(update_fields=['stock_qty'])
            self.refresh_from_db() # Get the updated value
            return True


class WISDetailManager(models.Manager):
    def get_total_issued_qty(self, company_id, won_o, item_id, p_id, c_id):
        """
        Corresponds to GetSchTime_TWIS_Qty stored procedure.
        Calculates the total issued quantity for a specific BOM component path.
        """
        total_issued = self.filter(
            wis_master__company_id=company_id,
            wis_master__won_o=won_o,
            item_id=item_id,
            p_id=p_id,
            c_id=c_id
        ).aggregate(Sum('issued_qty'))['issued_qty__sum']
        return total_issued if total_issued is not None else decimal.Decimal(0)


class WISDetail(models.Model):
    # No direct primary key specified in original code, assuming composite or Django will add one.
    # MId is a foreign key to WISMaster, WISNo is also here.
    wis_master = models.ForeignKey('WISMaster', on_delete=models.CASCADE, db_column='MId', related_name='details')
    wis_no = models.CharField(max_length=50, db_column='WISNo') # Redundant, but kept for direct mapping
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    item = models.ForeignKey(ItemMaster, on_delete=models.PROTECT, db_column='ItemId', related_name='wis_details')
    issued_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='IssuedQty')

    objects = WISDetailManager()

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Details'
        verbose_name = 'WIS Detail'
        verbose_name_plural = 'WIS Details'
        # Consider adding a unique_together constraint if a combination of fields is unique
        # Example: unique_together = (('wis_no', 'p_id', 'c_id', 'item'),)


class WISMasterManager(models.Manager):
    def generate_wis_no(self, company_id, fin_year_id):
        """
        Corresponds to GetSchTime_WISNo stored procedure.
        Generates a new sequential WIS number for the given company and financial year.
        """
        # Get the latest WISNo for the company and financial year
        latest_wis = self.filter(company_id=company_id, fin_year_id=fin_year_id) \
                           .order_by('-id').first() # Order by primary key to get latest
        
        if latest_wis and latest_wis.wis_no.isdigit():
            new_wis_num = int(latest_wis.wis_no) + 1
        else:
            new_wis_num = 1
        return str(new_wis_num).zfill(4) # Format as 0001, 0002, etc.

    def process_actual_run(self, won_o, company_id, fin_year_id, session_id):
        """
        Encapsulates the WIS_Material() logic from the C# code-behind.
        This is a transactional operation to issue materials based on BOM requirements.
        """
        decimal.getcontext().prec = 10 # Set precision for calculations

        try:
            with transaction.atomic():
                new_wis_no = self.generate_wis_no(company_id, fin_year_id)
                current_time = timezone.now()
                wis_master = None
                
                bom_items = BOMMaster.objects.get_bom_details_for_processing(won_o)

                for item_data in bom_items:
                    item_master_obj = ItemMaster.objects.get(id=item_data['item_id'])

                    # Recalculate 'Balance BOM Qty' and 'Dry Run Qty' based on current state
                    # This recalculation mimics the logic in GetDataTable but is for issuance
                    
                    # 1. Base Unit Qty for this BOM component
                    unit_qty = item_data['unit_qty'] # This is the Qty field from BOMMaster

                    # 2. Calculate BOM Qty (fun.BOMTreeQty logic)
                    bom_calc_qty = item_data['bom_qty'] # Re-use pre-calculated BOM Qty from helper

                    # 3. Calculate Total WIS Issued Qty (GetSchTime_TWIS_Qty)
                    total_wis_qty = WISDetail.objects.get_total_issued_qty(
                        company_id, won_o, item_data['item_id'], item_data['p_id'], item_data['c_id']
                    )

                    # 4. Calculate Balance BOM Qty (h - TotWISQty)
                    bal_bom_qty = bom_calc_qty - total_wis_qty
                    bal_bom_qty = max(decimal.Decimal(0), bal_bom_qty)

                    # 5. Complex Bal BOM Qty for non-root assemblies (CalBOMTreeQty logic)
                    if item_data['p_id'] != 0:
                        # This should reflect the adjusted balance from upstream parents,
                        # calculated by the BOMMaster.get_adjusted_parent_multiplier
                        adjusted_multiplier = BOMMaster.objects.get(
                            c_id=item_data['c_id'], p_id=item_data['p_id'], won_o=won_o
                        ).get_adjusted_parent_multiplier(company_id, won_o)
                        
                        calculated_qty_based_on_parents = unit_qty * adjusted_multiplier
                        bal_bom_qty = calculated_qty_based_on_parents - total_wis_qty
                        bal_bom_qty = max(decimal.Decimal(0), bal_bom_qty)

                    # 6. Determine CalIssueQty (Dry Run Qty) and CalStockQty
                    current_stock_qty = item_master_obj.stock_qty
                    cal_issue_qty = decimal.Decimal(0)
                    cal_stock_qty = current_stock_qty

                    if current_stock_qty >= bal_bom_qty:
                        cal_issue_qty = bal_bom_qty
                        cal_stock_qty = current_stock_qty - bal_bom_qty
                    elif bal_bom_qty > current_stock_qty:
                        cal_issue_qty = current_stock_qty
                        cal_stock_qty = decimal.Decimal(0)

                    if cal_issue_qty > 0:
                        if wis_master is None:
                            # Create WIS Master record only once
                            wis_master = self.create(
                                wis_no=new_wis_no,
                                sys_date=current_time.strftime('%Y-%m-%d'),
                                sys_time=current_time.strftime('%H:%M:%S'),
                                company_id=company_id,
                                session_id=session_id,
                                fin_year_id=fin_year_id,
                                won_o=won_o
                            )
                        
                        # Create WIS Detail record
                        WISDetail.objects.create(
                            wis_master=wis_master,
                            wis_no=new_wis_no, # Redundant but kept for direct column mapping
                            p_id=item_data['p_id'],
                            c_id=item_data['c_id'],
                            item=item_master_obj,
                            issued_qty=cal_issue_qty
                        )
                        
                        # Update Stock Quantity in Item Master
                        item_master_obj.update_stock_quantity(-cal_issue_qty) # Decrement stock

                # Update Work Order status (DryActualRun = 1)
                WorkOrderMaster.mark_dry_actual_run_completed(won_o, company_id)

            return True, "WIS process completed successfully."
        except Exception as e:
            # Log the exception for debugging
            print(f"Error during WIS material run: {e}")
            return False, f"WIS process failed: {e}"


class WISMaster(models.Model):
    id = models.AutoField(primary_key=True, db_column='Id') # AutoField as per C# logic for Id
    wis_no = models.CharField(max_length=50, db_column='WISNo')
    sys_date = models.CharField(max_length=10, db_column='SysDate') # Store as Char, or use DateField
    sys_time = models.CharField(max_length=8, db_column='SysTime') # Store as Char, or use TimeField
    company = models.ForeignKey(Company, on_delete=models.PROTECT, db_column='CompId', related_name='wis_masters')
    session_id = models.CharField(max_length=100, db_column='SessionId') # User ID/Session
    fin_year = models.ForeignKey(FinancialYear, on_delete=models.PROTECT, db_column='FinYearId', related_name='wis_masters')
    won_o = models.CharField(max_length=50, db_column='WONo') # Explicitly use CharField for WONo

    objects = WISMasterManager()

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Master'
        verbose_name = 'WIS Master'
        verbose_name_plural = 'WIS Masters'


class BOMManager(models.Manager):
    def get_bom_details_with_item_data(self, company_id, won_o):
        """
        Corresponds to GetDataTable() logic for initial display.
        Fetches BOM details and enriches with item data and calculated quantities.
        This method is complex and encapsulates significant business logic.
        """
        decimal.getcontext().prec = 10 # Set precision for calculations

        bom_items = self.filter(won_o=won_o) \
            .select_related('item', 'item__uom_basic', 'work_order') \
            .order_by('p_id', 'c_id')

        results = []
        for bom in bom_items:
            item = bom.item
            
            # 1. Base Unit Qty for this BOM component
            unit_qty_val = bom.qty 

            # 2. Calculate BOM Qty (fun.BOMTreeQty logic)
            bom_calc_qty = bom.calculate_bom_tree_qty()

            # 3. Calculate Total WIS Issued Qty (GetSchTime_TWIS_Qty)
            total_wis_qty = WISDetail.objects.get_total_issued_qty(company_id, won_o, bom.item_id, bom.p_id, bom.c_id)

            # 4. Calculate Balance BOM Qty (h - TotWISQty)
            bal_bom_qty = bom_calc_qty - total_wis_qty
            bal_bom_qty = max(decimal.Decimal(0), bal_bom_qty) # Ensure non-negative

            # 5. Complex Bal BOM Qty for non-root assemblies (CalBOMTreeQty logic)
            # This logic is applied AFTER the initial bal_bom_qty for root.
            # For non-root, this means adjusting based on parent issued quantities.
            if bom.p_id != 0: 
                # This should reflect the adjusted balance from upstream parents,
                # calculated by the BOMMaster.get_adjusted_parent_multiplier
                adjusted_multiplier = bom.get_adjusted_parent_multiplier(company_id, won_o)
                calculated_qty_based_on_parents = unit_qty_val * adjusted_multiplier
                bal_bom_qty = calculated_qty_based_on_parents - total_wis_qty
                bal_bom_qty = max(decimal.Decimal(0), bal_bom_qty)


            # 6. Calculate Dry Run Qty & After Stock Qty (Issue logic)
            stock_qty_val = item.stock_qty if item else decimal.Decimal(0)
            cal_issue_qty = decimal.Decimal(0)
            cal_stock_qty = stock_qty_val

            if stock_qty_val >= bal_bom_qty:
                cal_issue_qty = bal_bom_qty
                cal_stock_qty = stock_qty_val - bal_bom_qty
            elif bal_bom_qty > stock_qty_val:
                cal_issue_qty = stock_qty_val
                cal_stock_qty = decimal.Decimal(0)

            results.append({
                'item_id': bom.item_id,
                'won_o': bom.won_o,
                'p_id': bom.p_id,
                'c_id': bom.c_id,
                'item_code': item.item_code if item else 'N/A', # Property from ItemMaster
                'description': item.manf_desc if item else 'N/A',
                'uom': item.uom_basic.symbol if item and item.uom_basic else 'N/A',
                'unit_qty': unit_qty_val,
                'bom_qty': bom_calc_qty,
                'weld': 'N/A', # Original code had this, but no logic for it. Placeholder.
                'stock_qty': stock_qty_val,
                'tot_wis_qty': total_wis_qty,
                'balance_bom_qty': bal_bom_qty,
                'dry_run_qty': cal_issue_qty,
                'after_stock_qty': cal_stock_qty,
            })
        return results

    def get_bom_details_for_processing(self, won_o):
        """
        Similar to get_bom_details_with_item_data but optimized for process_actual_run.
        Returns BOM items with pre-calculated BOM quantity.
        """
        bom_items = self.filter(won_o=won_o).select_related('item', 'item__uom_basic')
        processed_bom = []
        for bom in bom_items:
            processed_bom.append({
                'item_id': bom.item_id,
                'won_o': bom.won_o,
                'p_id': bom.p_id,
                'c_id': bom.c_id,
                'unit_qty': bom.qty, # This is the base Qty from BOMMaster
                'bom_qty': bom.calculate_bom_tree_qty(), # Pre-calculate for efficiency
            })
        return processed_bom

class BOMMaster(models.Model):
    # CId is the primary key as per ASP.NET DataKeyNames
    c_id = models.IntegerField(primary_key=True, db_column='CId')
    p_id = models.IntegerField(db_column='PId')
    item = models.ForeignKey(ItemMaster, on_delete=models.PROTECT, db_column='ItemId', related_name='bom_master_components')
    won_o = models.CharField(max_length=50, db_column='WONo') # Use CharField for WONo
    qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')
    # Link to WorkOrderMaster, assuming WONo is a key there
    work_order = models.ForeignKey(WorkOrderMaster, on_delete=models.PROTECT, db_column='WONo', to_field='won_o', related_name='bom_items')


    objects = BOMManager()

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'
        # Composite unique constraint, inferred from typical BOM structures
        unique_together = (('c_id', 'p_id', 'won_o', 'item'),) 

    def __str__(self):
        return f"BOM: WO {self.won_o}, Item {self.item.item_code}, CId {self.c_id}, PId {self.p_id}"

    def calculate_bom_tree_qty(self):
        """
        Corresponds to fun.BOMTreeQty(wonosrc, Pid, Cid).
        Calculates the effective BOM quantity for this component by traversing up its parent chain
        and multiplying quantities. This is a recursive method (or iterative equivalent).
        """
        decimal.getcontext().prec = 10 # Set precision for calculations
        
        # Base case: Root node (PId = 0)
        if self.p_id == 0:
            return self.qty

        # Recursive step: Find parent BOM item and multiply its quantity
        # To avoid N+1 queries in a loop, prefetch or use a more efficient graph traversal
        # For a single call, this is acceptable. For bulk, optimize.
        try:
            parent_bom = BOMMaster.objects.get(won_o=self.won_o, c_id=self.p_id)
            return self.qty * parent_bom.calculate_bom_tree_qty()
        except ObjectDoesNotExist:
            # This should ideally not happen if BOM structure is valid
            print(f"Warning: Parent BOM for CId {self.p_id} not found for WO {self.won_o}")
            return self.qty # Fallback to own quantity if parent not found

    def get_adjusted_parent_multiplier(self, company_id, won_o):
        """
        Replicates the complex 'CalBOMTreeQty' logic for non-root assemblies.
        This method calculates an effective multiplier based on the available (unissued)
        quantity of parent components up the BOM tree. It directly translates the C# 'n' variable logic.
        """
        decimal.getcontext().prec = 10
        current_multiplier = decimal.Decimal(1)

        # Build path from current node's parent up to the root (PId=0)
        path_segments = []
        current_p_id_traverse = self.p_id
        current_c_id_traverse = self.c_id # This node's CId

        while current_p_id_traverse != 0:
            # Find the BOM record for the current_p_id_traverse as a child
            # i.e., find the item whose CId is current_p_id_traverse, and PId is its parent
            try:
                parent_bom_record = BOMMaster.objects.get(
                    won_o=won_o, 
                    c_id=current_p_id_traverse # The current PId in traversal is the CId of the parent element
                )
                path_segments.insert(0, parent_bom_record) # Insert at beginning to get path from root
                current_p_id_traverse = parent_bom_record.p_id
            except ObjectDoesNotExist:
                print(f"BOM Pathing Warning: Missing BOM for CId {current_p_id_traverse} in WO {won_o}")
                break # Break if parent not found, indicates malformed BOM or an error

        # Now, iterate through the path from the effective 'root' down to *this item's direct parent*
        # and calculate the `n` multiplier.
        # This loop mirrors the 'getcidpid' and 'getpidcid' loops in C#
        # For simplicity, we directly compute the `n` based on the path.
        
        for parent_segment_bom in path_segments:
            # Get the BOM quantity for this parent segment
            parent_segment_bom_qty = parent_segment_bom.qty 
            
            # Get the total WIS issued quantity for this specific parent segment
            # Note: This is the `TotWISQty4` or `TotWISQty3` in C#
            issued_qty_for_segment = WISDetail.objects.get_total_issued_qty(
                company_id, 
                won_o, 
                parent_segment_bom.item_id, 
                parent_segment_bom.p_id, 
                parent_segment_bom.c_id
            )

            # Apply the C# logic for 'n': (n * segment_qty) - segment_issued_qty
            # This means the multiplier is reduced by issued quantity at each step.
            current_multiplier = (current_multiplier * parent_segment_bom_qty) - issued_qty_for_segment
            current_multiplier = max(decimal.Decimal(0), current_multiplier) # Ensure non-negative multiplier

            if current_multiplier == 0: # If any parent in the chain has no remaining quantity, stop
                break
        
        return current_multiplier

```

#### 4.2 Forms (`inventory_transactions/forms.py`)

No direct form is needed for CRUD operations on individual BOM items. The main interaction is displaying data and triggering an action. However, for potential future filtering or search components, a simple form can be defined. For the "Actual Run" action, there isn't a traditional form, it's a button submission handled by HTMX.

```python
# inventory_transactions/forms.py (optional, for future filters)
from django import forms
from .models import WorkOrderMaster

class WISMaterialRunFilterForm(forms.Form):
    won_o = forms.CharField(
        max_length=50, 
        required=False, 
        label="Work Order No.",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    # Add other filters if needed

```

#### 4.3 Views (`inventory_transactions/views.py`)

Views are kept thin, delegating complex logic to models. HTMX is used for partial updates.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import render, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.utils import OperationalError

from .models import BOMMaster, WISMaster, WorkOrderMaster, Company, FinancialYear, CustomUser
from .forms import WISMaterialRunFilterForm # If you create one

# --- Global Context (for demonstration, replace with actual session/user data) ---
# In a real Django app, these would come from request.user, session, or authentication backend.
# For now, we'll use dummy values or assume them passed.
def get_current_user_context(request):
    # Example: Retrieve from session or authenticated user
    # This might require custom middleware or context processors
    # For now, hardcoded for demonstration
    user_id = request.session.get('user_id', 1) # Example, assuming user ID 1
    company_id = request.session.get('company_id', 1) # Example, assuming company ID 1
    fin_year_id = request.session.get('fin_year_id', 1) # Example, assuming financial year ID 1
    username = request.session.get('username', 'default_user') # Example

    try:
        current_company = Company.objects.get(comp_id=company_id)
    except Company.DoesNotExist:
        current_company = None # Handle case where company not found

    try:
        current_fin_year = FinancialYear.objects.get(fin_year_id=fin_year_id)
    except FinancialYear.DoesNotExist:
        current_fin_year = None # Handle case where financial year not found

    return {
        'company_id': company_id,
        'fin_year_id': fin_year_id,
        'session_id': username, # Maps to ASP.NET Session["username"]
        'current_company': current_company,
        'current_fin_year': current_fin_year,
        'current_username': username,
    }

# This view renders the initial page structure.
class WISMaterialRunListView(TemplateView):
    template_name = 'inventory_transactions/wis_material_run/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        won_o = self.request.GET.get('WONo')
        context['won_o'] = won_o
        # Add messages from query string if available (mimics ASP.NET behavior)
        if self.request.GET.get('msg'):
            messages.success(self.request, self.request.GET['msg'])
        return context

# This view provides the DataTables content via HTMX.
class WISMaterialRunTablePartialView(View):
    def get(self, request, *args, **kwargs):
        won_o = request.GET.get('WONo')
        if not won_o:
            return HttpResponse("<span>No Work Order Number provided.</span>", status=400)

        user_context = get_current_user_context(request)
        company_id = user_context['company_id']

        try:
            # Call the model method to get the processed data
            material_data = BOMMaster.objects.get_bom_details_with_item_data(company_id, won_o)
            # The 'expand_tree' checkbox in ASP.NET controls expansion.
            # With DataTables, this might mean simply showing all items in flat list or
            # applying a client-side filter/grouping if a DataTables plugin is used.
            # For direct translation to flat DataTables, we just pass all data.
            context = {
                'material_data': material_data,
                'won_o': won_o,
                'expand_tree_checked': request.GET.get('expand_tree', 'true') == 'true' # Initial state
            }
            return render(request, 'inventory_transactions/wis_material_run/_material_table.html', context)
        except OperationalError:
            return HttpResponse("<span>Database connection error. Please try again.</span>", status=500)
        except Exception as e:
            print(f"Error loading material data: {e}")
            return HttpResponse(f"<span>Error loading data: {e}</span>", status=500)


# This view handles the "Actual Run of Material" button click.
class WISMaterialRunProcessView(View):
    def post(self, request, *args, **kwargs):
        won_o = request.POST.get('won_o') # Get WO from hidden input or form data
        if not won_o:
            messages.error(request, "Work Order Number not provided for processing.")
            return HttpResponse(status=400, headers={'HX-Refresh': 'true'}) # Trigger full refresh

        user_context = get_current_user_context(request)
        company_id = user_context['company_id']
        fin_year_id = user_context['fin_year_id']
        session_id = user_context['session_id']

        # Call the fat model method to perform the transaction
        success, msg = WISMaster.objects.process_actual_run(won_o, company_id, fin_year_id, session_id)

        if success:
            messages.success(request, msg)
            # Redirect to the same page with a success message, mimicking ASP.NET behavior
            return HttpResponse(
                status=204, # No Content for HTMX to indicate success without changing page
                headers={
                    'HX-Trigger': '{"refreshMaterialList":{}, "showMessage": "WIS process completed successfully."}',
                    'Location': f"{reverse_lazy('wis_material_run_list')}?WONo={won_o}&msg=WIS process is completed." # Full page redirect
                }
            )
        else:
            messages.error(request, msg)
            return HttpResponse(
                status=204, # No Content for HTMX to indicate success without changing page
                headers={
                    'HX-Trigger': '{"showMessage": "WIS process failed.", "refreshMaterialList":{}}'
                }
            )

# This view handles the "Cancel" button click.
class WISMaterialRunCancelView(View):
    def get(self, request, *args, **kwargs):
        won_o = request.GET.get('WONo', '')
        # Mimic redirect to WIS_ActualRun_Assembly.aspx
        # In Django, this would be another view/URL
        return redirect(f"{reverse_lazy('wis_actual_run_assembly_list')}?WONo={won_o}&ModId=9&SubModId=53")

# This view handles the "Select" button per row (for report/print)
class WISMaterialRunReportView(View):
    def get(self, request, *args, **kwargs):
        won_o = request.GET.get('won_o')
        p_id = request.GET.get('p_id')
        c_id = request.GET.get('c_id')
        item_id = request.GET.get('item_id')

        # Mimic redirect to BOM_Design_Print_Cry.aspx
        # In Django, this would be a new URL leading to a report generation view
        report_url = reverse_lazy('bom_design_print_cry') # Placeholder URL
        return redirect(f"{report_url}?WONo={won_o}&PId={p_id}&CId={c_id}&ItemId={item_id}&ModId=9&SubModId=53")

```

#### 4.4 Templates

Templates will leverage HTMX for dynamic content loading and Alpine.js for simple UI state management (like modal visibility). DataTables is initialized via JavaScript.

**`inventory_transactions/wis_material_run/list.html`**
This is the main page template.

```html
{% extends 'core/base.html' %}

{% block title %}WIS Actual Run Material{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-2xl font-bold text-gray-800">
                WIS Dry Run for Material of Work Order No.: <span class="text-blue-600">{{ won_o }}</span>
            </h2>
            <div class="flex items-center space-x-4">
                <label class="inline-flex items-center">
                    <input type="checkbox" id="expandTreeCheckbox" class="form-checkbox h-5 w-5 text-blue-600"
                           x-data="{ checked: true }" x-model="checked"
                           hx-get="{% url 'wis_material_run_table_partial' %}?WONo={{ won_o }}&expand_tree={{ 'true' if checked else 'false' }}"
                           hx-target="#materialTable-container"
                           hx-trigger="change"
                           hx-indicator="#loadingIndicator">
                    <span class="ml-2 text-gray-700">Expand Tree</span>
                </label>

                <button 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    hx-post="{% url 'wis_material_run_process' %}"
                    hx-confirm="Are you sure you want to process the Actual Run of Material for Work Order {{ won_o }}?"
                    hx-vals='{"won_o": "{{ won_o }}"}'
                    hx-indicator="#loadingIndicator"
                    hx-target="body" hx-swap="none"
                    _="on hx:afterRequest if event.detail.successful (add .hidden to #loadingIndicator then init new DataTable on #wisMaterialTable) else remove .hidden from #loadingIndicator"
                >
                    Actual Run of Material
                </button>
                <button 
                    class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                    onclick="window.location.href='{% url 'wis_material_run_cancel' %}?WONo={{ won_o }}'"
                >
                    Cancel
                </button>
            </div>
        </div>

        <div id="lblmsg" class="text-red-600 font-bold mb-4">
            {% if messages %}
                <ul class="messages list-none p-0">
                    {% for message in messages %}
                        <li{% if message.tags %} class="{{ message.tags }} text-sm"{% endif %}>{{ message }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>

        <div id="materialTable-container"
             hx-trigger="load, refreshMaterialList from:body"
             hx-get="{% url 'wis_material_run_table_partial' %}?WONo={{ won_o }}"
             hx-swap="innerHTML">
            <!-- Loading indicator for initial load -->
            <div class="text-center py-10" id="loadingIndicator">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading material data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'materialTable-container' && evt.detail.xhr.status === 200) {
            // Re-initialize DataTable after content swap
            $('#wisMaterialTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance before re-initialising
                "responsive": true,
                "pagingType": "full_numbers"
            });
        }
    });

    // Handle messages triggered by HX-Trigger with showMessage
    document.body.addEventListener('showMessage', function(event) {
        const message = event.detail.value;
        const msgDiv = document.getElementById('lblmsg');
        msgDiv.innerHTML = `<ul class="messages list-none p-0"><li class="success text-sm">${message}</li></ul>`;
        setTimeout(() => {
            msgDiv.innerHTML = ''; // Clear message after some time
        }, 5000);
    });
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css" rel="stylesheet">
<style>
    /* Custom styling for DataTables pagination/controls if needed beyond Tailwind */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.5rem 0.75rem;
        border-radius: 0.25rem;
        margin: 0 0.125rem;
        cursor: pointer;
        background-color: #f3f4f6;
        border: 1px solid #d1d5db;
        color: #374151;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background-color: #3b82f6;
        color: white;
        border-color: #3b82f6;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }
    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_processing,
    .dataTables_wrapper .dataTables_paginate {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }
</style>
{% endblock %}
```

**`inventory_transactions/wis_material_run/_material_table.html`**
This partial template is loaded dynamically by HTMX and contains the DataTables structure.

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="wisMaterialTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tot. WIS Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Balance BOM Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Dry Run Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">After Stock Qty</th>
                <th scope="col" class="py-3 px-6 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <!-- Hidden columns for DataTables internal use or future visibility toggles -->
                <th scope="col" class="hidden">WONo</th>
                <th scope="col" class="hidden">PId</th>
                <th scope="col" class="hidden">CId</th>
                <th scope="col" class="hidden">ItemId</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in material_data %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.item_code }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.description }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-center">{{ row.uom }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.unit_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.bom_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.tot_wis_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.balance_bom_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.stock_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.dry_run_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-700 text-right">{{ row.after_stock_qty|floatformat:"3" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-center text-sm font-medium">
                    <button 
                        class="text-blue-600 hover:text-blue-900 focus:outline-none"
                        hx-get="{% url 'wis_material_run_report' %}?won_o={{ row.won_o }}&p_id={{ row.p_id }}&c_id={{ row.c_id }}&item_id={{ row.item_id }}"
                        hx-target="body" hx-swap="none"
                        title="View Report"
                    >
                        <img src="/static/images/export.ico" alt="Export" class="h-4 w-4 inline-block">
                    </button>
                </td>
                <td class="hidden">{{ row.won_o }}</td>
                <td class="hidden">{{ row.p_id }}</td>
                <td class="hidden">{{ row.c_id }}</td>
                <td class="hidden">{{ row.item_id }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 px-6 text-center text-gray-500">No material data found for this Work Order.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Alpine.js for checkbox state (optional, HTMX handles direct action) -->
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('expandCheckbox', () => ({
            checked: true, // Default checked state
            init() {
                // If the checkbox should reflect an initial server-side value
                this.checked = document.getElementById('expandTreeCheckbox').checked;
            }
        }));
    });
</script>
```

#### 4.5 URLs (`inventory_transactions/urls.py`)

URL patterns define the endpoints for the views, including HTMX-specific partial loads.

```python
from django.urls import path
from .views import (
    WISMaterialRunListView,
    WISMaterialRunTablePartialView,
    WISMaterialRunProcessView,
    WISMaterialRunCancelView,
    WISMaterialRunReportView,
)

urlpatterns = [
    # Main page for displaying the material run
    path('wis-material-run/', WISMaterialRunListView.as_view(), name='wis_material_run_list'),
    
    # HTMX endpoint to load/refresh the DataTables content
    path('wis-material-run/table/', WISMaterialRunTablePartialView.as_view(), name='wis_material_run_table_partial'),

    # HTMX endpoint to trigger the "Actual Run of Material" process (POST request)
    path('wis-material-run/process/', WISMaterialRunProcessView.as_view(), name='wis_material_run_process'),

    # Endpoint for the "Cancel" button, which redirects
    path('wis-material-run/cancel/', WISMaterialRunCancelView.as_view(), name='wis_material_run_cancel'),

    # Endpoint for the "Select" (Report/Print) button on each row
    path('wis-material-run/report/', WISMaterialRunReportView.as_view(), name='wis_material_run_report'),

    # Placeholder for the Assembly page redirect
    path('wis-actual-run-assembly/', TemplateView.as_view(template_name='inventory_transactions/wis_actual_run_assembly/list.html'), name='wis_actual_run_assembly_list'),
    
    # Placeholder for the BOM Design Print Cry report page
    path('bom-design-print-cry/', TemplateView.as_view(template_name='reports/bom_design_print_cry.html'), name='bom_design_print_cry'),
]

```

#### 4.6 Tests (`inventory_transactions/tests/test_models.py`, `inventory_transactions/tests/test_views.py`)

Comprehensive tests ensure that models encapsulate business logic correctly and views handle requests and responses as expected, especially with HTMX interactions.

**`inventory_transactions/tests/test_models.py`**

```python
from django.test import TestCase
from django.db.utils import OperationalError
from decimal import Decimal
import datetime
from unittest.mock import patch, MagicMock

# Import placeholder models (or actual models if defined in your project)
from ..models import (
    Company, FinancialYear, CustomUser, WorkOrderMaster,
    UnitMaster, ItemMaster, BOMMaster, WISMaster, WISDetail
)

class InventoryModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all model tests (run once for the class)
        cls.company = Company.objects.create(comp_id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, year_name='2023-2024')
        cls.user = CustomUser.objects.create(username='testuser')
        cls.work_order = WorkOrderMaster.objects.create(won_o='WO-001', company=cls.company, dry_actual_run=False)
        cls.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')

        # Items
        cls.item_a = ItemMaster.objects.create(id=101, manf_desc='Assembly A', stock_qty=Decimal('50.000'), uom_basic=cls.unit_pcs)
        cls.item_b = ItemMaster.objects.create(id=102, manf_desc='Part B', stock_qty=Decimal('100.000'), uom_basic=cls.unit_pcs)
        cls.item_c = ItemMaster.objects.create(id=103, manf_desc='Sub-Assembly C', stock_qty=Decimal('20.000'), uom_basic=cls.unit_pcs)
        cls.item_d = ItemMaster.objects.create(id=104, manf_desc='Raw Material D', stock_qty=Decimal('500.000'), uom_basic=cls.unit_kg)

        # BOM Structure for WO-001:
        # A (root, PId=0)
        #   -> B (CId=1, PId=0, Qty=5)
        #   -> C (CId=2, PId=0, Qty=2)
        #      -> D (CId=3, PId=2, Qty=10)

        cls.bom_a = BOMMaster.objects.create(c_id=1, p_id=0, item=cls.item_a, won_o='WO-001', qty=Decimal('1.000'), work_order=cls.work_order) # Root Assembly itself
        cls.bom_b = BOMMaster.objects.create(c_id=2, p_id=1, item=cls.item_b, won_o='WO-001', qty=Decimal('5.000'), work_order=cls.work_order) # 5x Part B per Assembly A
        cls.bom_c = BOMMaster.objects.create(c_id=3, p_id=1, item=cls.item_c, won_o='WO-001', qty=Decimal('2.000'), work_order=cls.work_order) # 2x Sub-Assembly C per Assembly A
        cls.bom_d = BOMMaster.objects.create(c_id=4, p_id=3, item=cls.item_d, won_o='WO-001', qty=Decimal('10.000'), work_order=cls.work_order) # 10x Raw Material D per Sub-Assembly C

    def setUp(self):
        # Clean up WIS and reset stock for each test method
        WISMaster.objects.all().delete()
        WISDetail.objects.all().delete()
        self.item_a.stock_qty = Decimal('50.000')
        self.item_b.stock_qty = Decimal('100.000')
        self.item_c.stock_qty = Decimal('20.000')
        self.item_d.stock_qty = Decimal('500.000')
        self.item_a.save()
        self.item_b.save()
        self.item_c.save()
        self.item_d.save()
        self.work_order.dry_actual_run = False
        self.work_order.save()


    # --- ItemMaster Tests ---
    def test_item_master_creation(self):
        self.assertEqual(self.item_a.manf_desc, 'Assembly A')
        self.assertEqual(self.item_a.stock_qty, Decimal('50.000'))
        self.assertEqual(self.item_a.uom_basic.symbol, 'PCS')
    
    def test_item_code_property(self):
        self.assertEqual(self.item_a.item_code, 'ITEM-101')

    def test_item_stock_update(self):
        initial_stock = self.item_b.stock_qty
        self.item_b.update_stock_quantity(Decimal('-10.000'))
        self.item_b.refresh_from_db()
        self.assertEqual(self.item_b.stock_qty, initial_stock - Decimal('10.000'))
        self.item_b.update_stock_quantity(Decimal('5.000'))
        self.item_b.refresh_from_db()
        self.assertEqual(self.item_b.stock_qty, initial_stock - Decimal('10.000') + Decimal('5.000'))

    # --- BOMMaster Tests ---
    def test_bom_master_creation(self):
        self.assertEqual(self.bom_b.item.manf_desc, 'Part B')
        self.assertEqual(self.bom_b.qty, Decimal('5.000'))
        self.assertEqual(self.bom_b.p_id, 1) # Parent is A's CId (1)

    def test_calculate_bom_tree_qty(self):
        # A itself: 1.000
        self.assertEqual(self.bom_a.calculate_bom_tree_qty(), Decimal('1.000'))
        # B: 5.000 (per A)
        self.assertEqual(self.bom_b.calculate_bom_tree_qty(), Decimal('5.000'))
        # C: 2.000 (per A)
        self.assertEqual(self.bom_c.calculate_bom_tree_qty(), Decimal('2.000'))
        # D: 10.000 (per C) * C's qty (2.000 per A) = 20.000 (per A)
        self.assertEqual(self.bom_d.calculate_bom_tree_qty(), Decimal('20.000'))

    def test_get_adjusted_parent_multiplier(self):
        # No issues yet
        # For a root item (A), multiplier is 1
        self.assertEqual(self.bom_a.get_adjusted_parent_multiplier(self.company.comp_id, self.work_order.won_o), Decimal('1'))
        
        # For B, its direct parent is A (c_id=1, p_id=0). No issues on A
        self.assertEqual(self.bom_b.get_adjusted_parent_multiplier(self.company.comp_id, self.work_order.won_o), Decimal('1'))
        
        # For D, its direct parent is C (c_id=3, p_id=1). Its grandparent is A (c_id=1, p_id=0)
        # No issues on A or C
        self.assertEqual(self.bom_d.get_adjusted_parent_multiplier(self.company.comp_id, self.work_order.won_o), Decimal('1'))

        # Issue some A (itself)
        WISMaster.objects.create(
            wis_no='0001', sys_date='2023-01-01', sys_time='10:00:00',
            company=self.company, session_id=self.user.username, fin_year=self.fin_year, won_o=self.work_order.won_o
        )
        WISDetail.objects.create(
            wis_master=WISMaster.objects.first(), wis_no='0001',
            p_id=0, c_id=1, item=self.item_a, issued_qty=Decimal('0.5')
        )
        # Now, multiplier for B should be (1 * A.qty - A_issued_qty) = (1 * 1 - 0.5) = 0.5
        self.assertEqual(self.bom_b.get_adjusted_parent_multiplier(self.company.comp_id, self.work_order.won_o), Decimal('0.5'))
        # Multiplier for D (whose parent is C, grandparent A)
        # 1. A: (1 * 1 - 0.5) = 0.5
        # 2. C: (0.5 * C.qty - C_issued_qty) = (0.5 * 2 - 0) = 1.0
        self.assertEqual(self.bom_d.get_adjusted_parent_multiplier(self.company.comp_id, self.work_order.won_o), Decimal('1.0'))


    def test_get_bom_details_with_item_data_initial(self):
        data = BOMMaster.objects.get_bom_details_with_item_data(self.company.comp_id, self.work_order.won_o)
        self.assertEqual(len(data), 4) # A, B, C, D

        item_a_data = next(item for item in data if item['item_id'] == self.item_a.id)
        self.assertAlmostEqual(item_a_data['bom_qty'], Decimal('1.000'))
        self.assertAlmostEqual(item_a_data['tot_wis_qty'], Decimal('0.000'))
        self.assertAlmostEqual(item_a_data['balance_bom_qty'], Decimal('1.000'))
        self.assertAlmostEqual(item_a_data['dry_run_qty'], Decimal('1.000')) # Can issue 1 of A
        self.assertAlmostEqual(item_a_data['after_stock_qty'], Decimal('49.000')) # 50 - 1

        item_b_data = next(item for item in data if item['item_id'] == self.item_b.id)
        self.assertAlmostEqual(item_b_data['bom_qty'], Decimal('5.000'))
        self.assertAlmostEqual(item_b_data['tot_wis_qty'], Decimal('0.000'))
        self.assertAlmostEqual(item_b_data['balance_bom_qty'], Decimal('5.000'))
        self.assertAlmostEqual(item_b_data['dry_run_qty'], Decimal('5.000'))
        self.assertAlmostEqual(item_b_data['after_stock_qty'], Decimal('95.000'))

        item_c_data = next(item for item in data if item['item_id'] == self.item_c.id)
        self.assertAlmostEqual(item_c_data['bom_qty'], Decimal('2.000'))
        self.assertAlmostEqual(item_c_data['balance_bom_qty'], Decimal('2.000'))
        self.assertAlmostEqual(item_c_data['dry_run_qty'], Decimal('2.000'))
        self.assertAlmostEqual(item_c_data['after_stock_qty'], Decimal('18.000'))

        item_d_data = next(item for item in data if item['item_id'] == self.item_d.id)
        self.assertAlmostEqual(item_d_data['bom_qty'], Decimal('20.000')) # 10 * 2 = 20
        self.assertAlmostEqual(item_d_data['balance_bom_qty'], Decimal('20.000'))
        self.assertAlmostEqual(item_d_data['dry_run_qty'], Decimal('20.000'))
        self.assertAlmostEqual(item_d_data['after_stock_qty'], Decimal('480.000'))

    def test_get_bom_details_with_item_data_with_issued_qty(self):
        # Issue 0.5 of Assembly A
        wis_master_1 = WISMaster.objects.create(
            wis_no='0001', sys_date='2023-01-01', sys_time='10:00:00',
            company=self.company, session_id=self.user.username, fin_year=self.fin_year, won_o=self.work_order.won_o
        )
        WISDetail.objects.create(
            wis_master=wis_master_1, wis_no='0001',
            p_id=0, c_id=1, item=self.item_a, issued_qty=Decimal('0.5')
        )
        # Issue 2.0 of Part B (c_id=2, p_id=1)
        WISDetail.objects.create(
            wis_master=wis_master_1, wis_no='0001',
            p_id=1, c_id=2, item=self.item_b, issued_qty=Decimal('2.0')
        )

        data = BOMMaster.objects.get_bom_details_with_item_data(self.company.comp_id, self.work_order.won_o)

        item_a_data = next(item for item in data if item['item_id'] == self.item_a.id)
        self.assertAlmostEqual(item_a_data['tot_wis_qty'], Decimal('0.500'))
        self.assertAlmostEqual(item_a_data['balance_bom_qty'], Decimal('0.500')) # 1 - 0.5 = 0.5

        item_b_data = next(item for item in data if item['item_id'] == self.item_b.id)
        # B's original BOM qty is 5.
        # Adjusted parent multiplier for B: (1 * A.qty - A_issued_qty) = (1 * 1 - 0.5) = 0.5
        # Calculated based on parents: B.unit_qty * adjusted_multiplier = 5 * 0.5 = 2.5
        # Total WIS issued for B: 2.0
        # Balance BOM Qty for B: 2.5 - 2.0 = 0.5
        self.assertAlmostEqual(item_b_data['tot_wis_qty'], Decimal('2.000'))
        self.assertAlmostEqual(item_b_data['balance_bom_qty'], Decimal('0.500')) 

        item_c_data = next(item for item in data if item['item_id'] == self.item_c.id)
        # C's original BOM qty is 2.
        # Adjusted parent multiplier for C: (1 * A.qty - A_issued_qty) = (1 * 1 - 0.5) = 0.5
        # Calculated based on parents: C.unit_qty * adjusted_multiplier = 2 * 0.5 = 1.0
        # Total WIS issued for C: 0.0
        # Balance BOM Qty for C: 1.0 - 0.0 = 1.0
        self.assertAlmostEqual(item_c_data['balance_bom_qty'], Decimal('1.000'))

        item_d_data = next(item for item in data if item['item_id'] == self.item_d.id)
        # D's original BOM qty per A is 20.
        # Adjusted parent multiplier for D (relative to A -> C path):
        # 1. Parent A: (1 * A.qty - A_issued_qty) = (1 * 1 - 0.5) = 0.5
        # 2. Parent C: (0.5 * C.qty - C_issued_qty) = (0.5 * 2 - 0) = 1.0
        # Calculated based on parents: D.unit_qty * adjusted_multiplier = 10 * 1.0 = 10.0
        # Total WIS issued for D: 0.0
        # Balance BOM Qty for D: 10.0 - 0.0 = 10.0
        self.assertAlmostEqual(item_d_data['balance_bom_qty'], Decimal('10.000'))


    # --- WISMaster Tests ---
    @patch('django.utils.timezone.now')
    def test_generate_wis_no_first(self, mock_now):
        mock_now.return_value = datetime.datetime(2023, 1, 1, 10, 0, 0, tzinfo=datetime.timezone.utc)
        wis_no = WISMaster.objects.generate_wis_no(self.company.comp_id, self.fin_year.fin_year_id)
        self.assertEqual(wis_no, '0001')

    @patch('django.utils.timezone.now')
    def test_generate_wis_no_sequential(self, mock_now):
        mock_now.return_value = datetime.datetime(2023, 1, 1, 10, 0, 0, tzinfo=datetime.timezone.utc)
        WISMaster.objects.create(
            wis_no='0001', sys_date='2023-01-01', sys_time='09:00:00',
            company=self.company, session_id=self.user.username, fin_year=self.fin_year, won_o='WO-001'
        )
        wis_no = WISMaster.objects.generate_wis_no(self.company.comp_id, self.fin_year.fin_year_id)
        self.assertEqual(wis_no, '0002')

    @patch('django.utils.timezone.now')
    def test_process_actual_run_success(self, mock_now):
        mock_now.return_value = datetime.datetime(2023, 1, 1, 10, 0, 0, tzinfo=datetime.timezone.utc)

        success, msg = WISMaster.objects.process_actual_run(
            self.work_order.won_o, self.company.comp_id, self.fin_year.fin_year_id, self.user.username
        )
        
        self.assertTrue(success)
        self.assertIn("completed successfully", msg)

        # Verify WIS Master created
        wis_master_count = WISMaster.objects.count()
        self.assertEqual(wis_master_count, 1)
        wis_master = WISMaster.objects.first()
        self.assertEqual(wis_master.wis_no, '0001')
        self.assertEqual(wis_master.won_o, 'WO-001')

        # Verify WIS Details created and stock updated
        # Item A: issued 1.0 (from 1.0 Balance BOM Qty), stock 50 -> 49
        item_a_wis_detail = WISDetail.objects.filter(item=self.item_a).first()
        self.assertIsNotNone(item_a_wis_detail)
        self.assertAlmostEqual(item_a_wis_detail.issued_qty, Decimal('1.000'))
        self.item_a.refresh_from_db()
        self.assertAlmostEqual(self.item_a.stock_qty, Decimal('49.000'))

        # Item B: issued 5.0 (from 5.0 Balance BOM Qty), stock 100 -> 95
        item_b_wis_detail = WISDetail.objects.filter(item=self.item_b).first()
        self.assertIsNotNone(item_b_wis_detail)
        self.assertAlmostEqual(item_b_wis_detail.issued_qty, Decimal('5.000'))
        self.item_b.refresh_from_db()
        self.assertAlmostEqual(self.item_b.stock_qty, Decimal('95.000'))

        # Item C: issued 2.0 (from 2.0 Balance BOM Qty), stock 20 -> 18
        item_c_wis_detail = WISDetail.objects.filter(item=self.item_c).first()
        self.assertIsNotNone(item_c_wis_detail)
        self.assertAlmostEqual(item_c_wis_detail.issued_qty, Decimal('2.000'))
        self.item_c.refresh_from_db()
        self.assertAlmostEqual(self.item_c.stock_qty, Decimal('18.000'))

        # Item D: issued 20.0 (from 20.0 Balance BOM Qty), stock 500 -> 480
        item_d_wis_detail = WISDetail.objects.filter(item=self.item_d).first()
        self.assertIsNotNone(item_d_wis_detail)
        self.assertAlmostEqual(item_d_wis_detail.issued_qty, Decimal('20.000'))
        self.item_d.refresh_from_db()
        self.assertAlmostEqual(self.item_d.stock_qty, Decimal('480.000'))

        # Verify Work Order updated
        self.work_order.refresh_from_db()
        self.assertTrue(self.work_order.dry_actual_run)

    @patch('django.utils.timezone.now')
    def test_process_actual_run_with_insufficient_stock(self, mock_now):
        mock_now.return_value = datetime.datetime(2023, 1, 1, 10, 0, 0, tzinfo=datetime.timezone.utc)
        
        # Reduce stock of Part B to be less than required
        self.item_b.stock_qty = Decimal('2.000') # Need 5, only have 2
        self.item_b.save()

        success, msg = WISMaster.objects.process_actual_run(
            self.work_order.won_o, self.company.comp_id, self.fin_year.fin_year_id, self.user.username
        )

        self.assertTrue(success) # The process completes, just issues less
        
        # Item B should have issued only 2.0 (its available stock)
        item_b_wis_detail = WISDetail.objects.filter(item=self.item_b).first()
        self.assertIsNotNone(item_b_wis_detail)
        self.assertAlmostEqual(item_b_wis_detail.issued_qty, Decimal('2.000'))
        self.item_b.refresh_from_db()
        self.assertAlmostEqual(self.item_b.stock_qty, Decimal('0.000')) # Stock should be zero

        # Other items should still issue their full Dry Run Qty as calculated by available stock
        item_a_wis_detail = WISDetail.objects.filter(item=self.item_a).first()
        self.assertAlmostEqual(item_a_wis_detail.issued_qty, Decimal('1.000'))
        self.item_a.refresh_from_db()
        self.assertAlmostEqual(self.item_a.stock_qty, Decimal('49.000'))


    # --- WISDetail Tests ---
    def test_wis_detail_get_total_issued_qty(self):
        wis_master_1 = WISMaster.objects.create(
            wis_no='0001', sys_date='2023-01-01', sys_time='10:00:00',
            company=self.company, session_id=self.user.username, fin_year=self.fin_year, won_o=self.work_order.won_o
        )
        WISDetail.objects.create(
            wis_master=wis_master_1, wis_no='0001',
            p_id=0, c_id=1, item=self.item_a, issued_qty=Decimal('10.0')
        )
        WISDetail.objects.create(
            wis_master=wis_master_1, wis_no='0001',
            p_id=0, c_id=1, item=self.item_a, issued_qty=Decimal('5.0')
        )
        total_issued = WISDetail.objects.get_total_issued_qty(
            self.company.comp_id, self.work_order.won_o, self.item_a.id, 0, 1
        )
        self.assertAlmostEqual(total_issued, Decimal('15.0'))

        # Test for item with no issues
        total_issued_no_issues = WISDetail.objects.get_total_issued_qty(
            self.company.comp_id, self.work_order.won_o, self.item_d.id, 3, 4 # Example, D's PId and CId
        )
        self.assertAlmostEqual(total_issued_no_issues, Decimal('0.0'))

    # --- WorkOrderMaster Tests ---
    def test_mark_dry_actual_run_completed(self):
        self.assertFalse(self.work_order.dry_actual_run)
        WorkOrderMaster.mark_dry_actual_run_completed(self.work_order.won_o, self.company.comp_id)
        self.work_order.refresh_from_db()
        self.assertTrue(self.work_order.dry_actual_run)

    def test_mark_dry_actual_run_completed_non_existent(self):
        success = WorkOrderMaster.mark_dry_actual_run_completed('NON_EXISTENT_WO', self.company.comp_id)
        self.assertFalse(success)

```

**`inventory_transactions/tests/test_views.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from decimal import Decimal
from unittest.mock import patch, MagicMock
import datetime

# Import models
from ..models import (
    Company, FinancialYear, CustomUser, WorkOrderMaster,
    UnitMaster, ItemMaster, BOMMaster, WISMaster, WISDetail
)

# Mock the get_current_user_context to provide consistent test data
# In a real app, you'd log in a user or use Django's test client auth.
@patch('inventory_transactions.views.get_current_user_context')
class WISMaterialRunViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.company = Company.objects.create(comp_id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, year_name='2023-2024')
        cls.user = CustomUser.objects.create(username='testuser')
        cls.work_order = WorkOrderMaster.objects.create(won_o='WO-TEST', company=cls.company, dry_actual_run=False)
        cls.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')

        cls.item_parent = ItemMaster.objects.create(id=1, manf_desc='Parent Assy', stock_qty=Decimal('10.000'), uom_basic=cls.unit_pcs)
        cls.item_child = ItemMaster.objects.create(id=2, manf_desc='Child Part', stock_qty=Decimal('100.000'), uom_basic=cls.unit_pcs)
        
        cls.bom_parent = BOMMaster.objects.create(c_id=1, p_id=0, item=cls.item_parent, won_o='WO-TEST', qty=Decimal('1.000'), work_order=cls.work_order)
        cls.bom_child = BOMMaster.objects.create(c_id=2, p_id=1, item=cls.item_child, won_o='WO-TEST', qty=Decimal('5.000'), work_order=cls.work_order)

    def setUp(self):
        self.client = Client()
        # Reset stock and WIS records before each test method
        WISMaster.objects.all().delete()
        WISDetail.objects.all().delete()
        self.item_parent.stock_qty = Decimal('10.000')
        self.item_child.stock_qty = Decimal('100.000')
        self.item_parent.save()
        self.item_child.save()
        self.work_order.dry_actual_run = False
        self.work_order.save()


    def mock_user_context(self, mock_func):
        mock_func.return_value = {
            'company_id': self.company.comp_id,
            'fin_year_id': self.fin_year.fin_year_id,
            'session_id': self.user.username,
            'current_company': self.company,
            'current_fin_year': self.fin_year,
            'current_username': self.user.username,
        }

    # --- WISMaterialRunListView Tests ---
    def test_list_view_get(self, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        response = self.client.get(reverse('wis_material_run_list'), {'WONo': 'WO-TEST'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/wis_material_run/list.html')
        self.assertContains(response, 'WIS Dry Run for Material of Work Order No.:')
        self.assertContains(response, 'WO-TEST')
        self.assertContains(response, 'id="materialTable-container"') # Check for HTMX target div

    def test_list_view_with_message_in_query_string(self, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        response = self.client.get(reverse('wis_material_run_list'), {'WONo': 'WO-TEST', 'msg': 'Test message'})
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Test message')
        self.assertContains(response, 'Test message')

    # --- WISMaterialRunTablePartialView Tests ---
    def test_table_partial_view_get_success(self, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        response = self.client.get(reverse('wis_material_run_table_partial'), {'WONo': 'WO-TEST'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/wis_material_run/_material_table.html')
        self.assertContains(response, 'id="wisMaterialTable"')
        self.assertContains(response, 'Parent Assy')
        self.assertContains(response, 'Child Part')

    def test_table_partial_view_get_no_won_o(self, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        response = self.client.get(reverse('wis_material_run_table_partial'))
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'No Work Order Number provided.')

    # --- WISMaterialRunProcessView Tests ---
    @patch('inventory_transactions.models.WISMaster.objects.process_actual_run')
    def test_process_view_post_success(self, mock_process_actual_run, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        mock_process_actual_run.return_value = (True, "WIS process completed successfully.")

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('wis_material_run_process'), {'won_o': 'WO-TEST'}, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success code for "no content"
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshMaterialList', response.headers['HX-Trigger'])
        self.assertIn('showMessage', response.headers['HX-Trigger'])
        self.assertTrue('Location' in response.headers) # Should contain redirect for full page reload

        mock_process_actual_run.assert_called_once_with(
            'WO-TEST', self.company.comp_id, self.fin_year.fin_year_id, self.user.username
        )
        
        # Check messages for the request (if not HX-Triggered, messages would be here)
        messages = list(get_messages(self.client.get(response.headers['Location']))) # Follow the redirect
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'WIS process is completed.')

    @patch('inventory_transactions.models.WISMaster.objects.process_actual_run')
    def test_process_view_post_failure(self, mock_process_actual_run, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        mock_process_actual_run.return_value = (False, "WIS process failed due to an error.")

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('wis_material_run_process'), {'won_o': 'WO-TEST'}, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('showMessage', response.headers['HX-Trigger'])
        self.assertIn('WIS process failed.', response.headers['HX-Trigger']) # Check error message in trigger

        mock_process_actual_run.assert_called_once()
        # No Location header on failure for HTMX
        self.assertFalse('Location' in response.headers)

    def test_process_view_post_no_won_o(self, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('wis_material_run_process'), {}, **headers)
        self.assertEqual(response.status_code, 400)
        self.assertTrue('HX-Refresh' in response.headers) # Should trigger a full refresh
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order Number not provided for processing.')

    # --- WISMaterialRunCancelView Tests ---
    def test_cancel_view(self, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        response = self.client.get(reverse('wis_material_run_cancel'), {'WONo': 'WO-TEST'})
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertIn(reverse('wis_actual_run_assembly_list'), response.url)
        self.assertIn('WONo=WO-TEST', response.url)

    # --- WISMaterialRunReportView Tests ---
    def test_report_view(self, mock_get_user_context):
        self.mock_user_context(mock_get_user_context)
        query_params = {
            'won_o': 'WO-TEST',
            'p_id': '1',
            'c_id': '2',
            'item_id': '102'
        }
        response = self.client.get(reverse('wis_material_run_report'), query_params)
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertIn(reverse('bom_design_print_cry'), response.url)
        self.assertIn('WONo=WO-TEST', response.url)
        self.assertIn('PId=1', response.url)
        self.assertIn('CId=2', response.url)
        self.assertIn('ItemId=102', response.url)

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:** All dynamic interactions are driven by HTMX.
    *   The `materialTable-container` `div` uses `hx-trigger="load, refreshMaterialList from:body"` and `hx-get="{% url 'wis_material_run_table_partial' %}"` to load the table content dynamically upon page load and whenever a `refreshMaterialList` custom event is triggered from the body (e.g., after a successful material run).
    *   The "Actual Run of Material" button uses `hx-post` to send data to `wis_material_run_process`. On success, it triggers `HX-Trigger` events for `refreshMaterialList` and `showMessage` to update the UI without a full page reload and display the success message.
    *   The "Expand Tree" checkbox triggers a `hx-get` to reload the table partial with a parameter to indicate the desired state.
    *   The "Select" button (`images/export.ico`) on each row performs a `hx-get` to the `wis_material_run_report` URL, which will redirect to the report page.
    *   Loading indicators (`hx-indicator`) provide visual feedback during AJAX requests.

*   **Alpine.js:** Used for simple UI state management, primarily for the "Expand Tree" checkbox if more complex local state was needed. For this simple checkbox, HTMX's direct form submission is sufficient, but Alpine.js is included as a best practice for local UI component management.

*   **DataTables:**
    *   The `_material_table.html` partial renders a standard `<table>` with the ID `wisMaterialTable`.
    *   JavaScript in `list.html` ensures that DataTables is initialized on this table *after* HTMX has successfully swapped in the new table content (`htmx:afterSwap` event). This guarantees DataTables functions correctly with dynamically loaded data.
    *   The `pageLength` and `lengthMenu` options are configured for client-side pagination.

---

### Final Notes

*   **Placeholders:** Replace `Company_Master`, `Financial_Year_Master`, `auth_user` table names with your actual ERP database table names if different. The `get_current_user_context` function is a placeholder and should be integrated with your Django authentication and session management system.
*   **Precision:** `DecimalField` with `max_digits=18` and `decimal_places=3` is used to maintain precision for all quantity fields, mirroring the `double` and `decimal.Parse().ToString("N3")` usage in the original ASP.NET code.
*   **Error Handling:** Basic `try-except` blocks are included in views and models. Robust logging and user-friendly error messages should be implemented in a production environment.
*   **BOM Logic Complexity:** The BOM calculation logic in `BOMMaster` and `WISMaster` is a direct translation of the complex C# logic. While functional, for extremely large BOMs, further optimization (e.g., pre-calculating paths, using a specialized graph database, or more advanced memoization) might be considered in a highly scaled production system. However, the current "fat model" approach adheres to the strict guidelines.
*   **Static Files:** Ensure `../../../images/export.ico` is placed in `static/images/export.ico` and Django's static files are configured to serve it.
*   **DRY Principle:** Templates extensively use partials. Business logic is strictly within models.
*   **Test Coverage:** The provided tests aim for high coverage, demonstrating validation and interaction across models and views. Further edge cases and more complex data scenarios should be added as the system evolves.