The following Django modernization plan addresses the ASP.NET `MaterialIssueNote_MIN_Edit.aspx` page and its C# code-behind. This plan emphasizes a "Fat Model, Thin View" architecture, leveraging HTMX and Alpine.js for dynamic interactions, and DataTables for efficient data presentation, all within the constraints of a `managed=False` Django model mapping to an existing database.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

Based on the `GridView1` column bindings and the `Sp_MIN_FillGrid` stored procedure parameters, we identify the main table and its relevant columns. The `GetCompletionList` method reveals the employee lookup table.

-   **Primary Table:** `tblInv_MaterialIssue_Master` (for Material Issue Notes)
    -   Columns: `Id` (PK), `FinYearId`, `MINNo`, `SysDate`, `MRSNo`, `GenBy`, `SessionId` (likely maps to employee ID), `CompId`.
-   **Lookup Table:** `tblHR_OfficeStaff` (for Employees)
    -   Columns: `EmpId` (PK), `EmployeeName`, `CompId`.

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The ASP.NET page is primarily a search and list interface. It allows users to filter Material Issue Notes and then navigate to a detail page for editing.

-   **Read (List & Search):** The core functionality is to fetch and display a list of `Material Issue Notes` based on user-selected search criteria (`MRS No`, `MIN No`, or `Employee Name`). Pagination is handled by `GridView1`.
-   **Employee Autocomplete:** Provides real-time suggestions for employee names.
-   **Navigation:** A "Select" action on a row redirects the user to `MaterialIssueNote_MIN_Edit_Details.aspx` with various query parameters, implying that actual CRUD operations (edit/update) happen on that separate details page, not on this current page. Therefore, we will only implement the search and list functionality for this specific page.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The ASP.NET controls are mapped to modern web equivalents, focusing on HTMX and Alpine.js for dynamic behavior.

-   **Search Controls:**
    -   `DrpField` (DropDownList): A `<select>` HTML element. Its `onselectedindexchanged` (AutoPostBack) will be replaced by HTMX to dynamically swap input fields.
    -   `TxtMrs` (TextBox): A `<input type="text">` for MRS/MIN numbers.
    -   `TxtEmpName` (TextBox with `AutoCompleteExtender`): A `<input type="text">` with HTMX for fetching autocomplete suggestions and Alpine.js for managing the suggestion list.
    -   `Button1` (Button): A `<button type="submit">` that triggers the HTMX search.
-   **Data Display:**
    -   `GridView1`: This will be replaced by a standard HTML `<table>` element, enhanced with the DataTables JavaScript library for client-side searching, sorting, and pagination.
-   **Dynamic Interactions:**
    -   All form submissions and partial updates will be handled by HTMX, eliminating full page postbacks.
    -   Alpine.js will manage the state of the autocomplete dropdown and other minor UI interactions.

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**

We define two models: `Employee` for the lookup table and `MaterialIssueNote` for the main data. Both models use `managed = False` and `db_table` to map to the existing database schema. Class methods are added for business logic, following the "Fat Model" principle.

```python
# inventory/models.py
from django.db import models

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee details.
    """
    EmpId = models.IntegerField(db_column='EmpId', primary_key=True) # Assuming EmpId is PK
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255)
    CompId = models.IntegerField(db_column='CompId') # Company ID for filtering

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.EmployeeName} [{self.EmpId}]"

    @classmethod
    def get_employee_by_name_or_id(cls, search_term):
        """
        Retrieves an employee instance by full name or by parsing "Name [ID]" format.
        This mirrors the fun.getCode(TxtEmpName.Text) behavior to get an ID.
        """
        # Try to parse "Employee Name [ID]" format
        if '[' in search_term and search_term.endswith(']'):
            try:
                parts = search_term.rsplit(' [', 1)
                name = parts[0].strip()
                emp_id = int(parts[1][:-1].strip()) # Remove ']' and convert to int
                return cls.objects.filter(EmpId=emp_id, EmployeeName__iexact=name).first()
            except (ValueError, IndexError):
                pass # Fallback to name search if parsing fails

        # Fallback to searching by employee name if no ID is provided or parsing failed
        return cls.objects.filter(EmployeeName__iexact=search_term).first()

    @classmethod
    def get_employee_suggestions(cls, prefix_text, company_id):
        """
        Provides employee name suggestions for autocomplete, filtered by company.
        Limits results to 10 for efficiency.
        """
        return cls.objects.filter(
            EmployeeName__icontains=prefix_text,
            CompId=company_id
        ).values_list('EmployeeName', 'EmpId')[:10] # Return name and ID for formatting

class MaterialIssueNote(models.Model):
    """
    Maps to tblInv_MaterialIssue_Master for Material Issue Note details.
    """
    Id = models.IntegerField(db_column='Id', primary_key=True)
    FinYearId = models.IntegerField(db_column='FinYearId')
    MINNo = models.CharField(db_column='MINNo', max_length=50)
    SysDate = models.DateTimeField(db_column='SysDate')
    MRSNo = models.CharField(db_column='MRSNo', max_length=50)
    GenBy = models.CharField(db_column='GenBy', max_length=100) # Assuming this stores the employee name
    SessionId = models.IntegerField(db_column='SessionId', null=True, blank=True) # Likely maps to EmpId
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Note'
        verbose_name_plural = 'Material Issue Notes'
        # Default ordering for consistency, can be overridden by DataTables
        ordering = ['-SysDate', '-Id'] 

    def __str__(self):
        return self.MINNo

    @classmethod
    def search_notes(cls, company_id, fin_year_id, search_type, search_term):
        """
        Performs search for Material Issue Notes based on criteria,
        encapsulating the logic from the ASP.NET loaddata method.
        """
        queryset = cls.objects.filter(CompId=company_id, FinYearId=fin_year_id)

        if search_term:
            if search_type == '0': # Search by MRS No
                queryset = queryset.filter(MRSNo__iexact=search_term)
            elif search_type == '1': # Search by MIN No
                queryset = queryset.filter(MINNo__iexact=search_term)
            elif search_type == '2': # Search by Employee Name (using SessionId as employee ID)
                # Resolve the employee name (which might include ID) to an employee object
                employee = Employee.get_employee_by_name_or_id(search_term)
                if employee:
                    queryset = queryset.filter(SessionId=employee.EmpId)
                else:
                    # If no employee is found for the given search term, return an empty queryset
                    return cls.objects.none()

        # The queryset is returned for client-side DataTables to handle pagination and sorting.
        # For very large datasets, server-side processing for DataTables would be required.
        return queryset.order_by('-SysDate', '-Id') # Consistent ordering for search results
```

### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**

A non-ModelForm `Form` is used to represent the search criteria as no database record is directly created/updated on this page. HTMX attributes are directly added to the widgets for dynamic behavior.

```python
# inventory/forms.py
from django import forms
from .models import MaterialIssueNote, Employee

class MaterialIssueNoteSearchForm(forms.Form):
    SEARCH_TYPES = [
        ('0', 'MRS No'),
        ('1', 'MIN No'),
        ('2', 'Employee Name'),
    ]

    DrpField = forms.ChoiceField(
        choices=SEARCH_TYPES,
        widget=forms.Select(attrs={
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm',
            'hx-get': '/inventory/materialissuenote/search-controls/', # HTMX to update search inputs
            'hx-target': '#search-inputs',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change', # Trigger when dropdown value changes
        }),
        label="Search By"
    )
    TxtMrs = forms.CharField(
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm w-[150px] focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Enter MRS/MIN No.',
        }),
        label="MRS/MIN No"
    )
    TxtEmpName = forms.CharField(
        max_length=350,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm w-[350px] focus:ring-blue-500 focus:border-blue-500',
            'placeholder': 'Enter Employee Name',
            'hx-get': '/inventory/materialissuenote/employee-autocomplete/', # HTMX for autocomplete suggestions
            'hx-trigger': 'keyup changed delay:300ms, search', # Trigger on keyup after delay, or explicit search event
            'hx-target': '#autocomplete-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser's autocomplete
            'x-on:click.outside': 'suggestions = []', # Alpine.js: hide suggestions on outside click
            'x-model': 'employeeSearchTerm', # Alpine.js model for input value
            'x-on:keyup.escape': 'suggestions = []', # Alpine.js: hide suggestions on Escape key
            'x-on:focus': 'if (employeeSearchTerm.length > 0) $dispatch("triggerAutocomplete")', # Show suggestions on focus if text exists
            'id': 'TxtEmpName' # Unique ID for Alpine.js/HTMX
        }),
        label="Employee Name"
    )

    def __init__(self, *args, **kwargs):
        initial_search_type = kwargs.pop('initial_search_type', '0')
        super().__init__(*args, **kwargs)
        self.fields['DrpField'].initial = initial_search_type
        
        # Adjust initial visibility based on the initial search type selected
        if initial_search_type == '2':
            # Hide TxtMrs, show TxtEmpName
            self.fields['TxtMrs'].widget.attrs['class'] += ' hidden'
        else:
            # Hide TxtEmpName, show TxtMrs (default)
            self.fields['TxtEmpName'].widget.attrs['class'] += ' hidden'

    def get_search_params(self):
        """
        Extracts validated search parameters from the form.
        """
        if self.is_valid():
            search_type = self.cleaned_data['DrpField']
            search_term = self.cleaned_data['TxtMrs'] if search_type in ['0', '1'] else self.cleaned_data['TxtEmpName']
            return search_type, search_term
        return None, None # Return None if form is not valid
```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

The views are designed to be thin, offloading most logic to the models and using HTMX for partial rendering.
`LoginRequiredMixin` is included assuming user authentication is required.

```python
# inventory/views.py
from django.views.generic import TemplateView, View
from django.shortcuts import render
from django.http import JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.conf import settings # Used for simulating session values like CompId, FinYearId

from .models import MaterialIssueNote, Employee
from .forms import MaterialIssueNoteSearchForm

class MaterialIssueNoteListView(LoginRequiredMixin, TemplateView):
    """
    Main view for the Material Issue Note search and list page.
    Corresponds to MaterialIssueNote_MIN_Edit.aspx.
    """
    template_name = 'inventory/materialissuenote/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with a default search type
        context['form'] = MaterialIssueNoteSearchForm(initial_search_type=self.request.GET.get('DrpField', '0'))
        return context

class MaterialIssueNoteTablePartialView(LoginRequiredMixin, View):
    """
    HTMX endpoint to render and return the Material Issue Notes table partial.
    This replaces the GridView data binding logic.
    """
    def get(self, request, *args, **kwargs):
        # Extract search parameters from the GET request
        search_type = request.GET.get('DrpField', '0')
        # Determine which text input to use based on search_type
        search_term = request.GET.get('TxtMrs', '') if search_type in ['0', '1'] else request.GET.get('TxtEmpName', '')
        
        # Simulate session values for CompId and FinYearId.
        # In a real application, these would typically come from the authenticated user's profile
        # or a robust session management system.
        company_id = getattr(settings, 'DEFAULT_COMP_ID', 1) 
        fin_year_id = getattr(settings, 'DEFAULT_FIN_YEAR_ID', 1)

        # Use the MaterialIssueNote model's class method to perform the search
        material_issue_notes = MaterialIssueNote.search_notes(
            company_id=company_id,
            fin_year_id=fin_year_id,
            search_type=search_type,
            search_term=search_term
        )
        
        context = {
            'material_issue_notes': material_issue_notes,
        }
        return render(request, 'inventory/materialissuenote/_materialissuenote_table.html', context)

class MaterialIssueNoteSearchControlsPartialView(LoginRequiredMixin, View):
    """
    HTMX endpoint to dynamically render the appropriate search input field
    (TxtMrs or TxtEmpName) based on the selected DrpField value.
    Replaces DrpField_SelectedIndexChanged C# logic.
    """
    def get(self, request, *args, **kwargs):
        search_type = request.GET.get('DrpField', '0')
        form = MaterialIssueNoteSearchForm(initial_search_type=search_type)
        
        context = {
            'form': form,
            'selected_search_type': search_type # Pass to template for conditional rendering
        }
        return render(request, 'inventory/materialissuenote/_search_controls.html', context)

class EmployeeAutocompleteView(LoginRequiredMixin, View):
    """
    HTMX endpoint for providing employee name autocomplete suggestions.
    Replaces the GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        # Simulate session value for Company ID
        company_id = getattr(settings, 'DEFAULT_COMP_ID', 1) 
        
        if not prefix_text:
            return JsonResponse([], safe=False)

        # Get suggestions using the Employee model's class method
        suggestions = Employee.get_employee_suggestions(prefix_text, company_id)
        
        # Format suggestions as "EmployeeName [EmpId]"
        formatted_suggestions = [f"{name} [{emp_id}]" for name, emp_id in suggestions]
        
        return JsonResponse(formatted_suggestions, safe=False)

```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

Templates adhere to DRY principles, extend `core/base.html`, and use HTMX and Alpine.js for interactive elements. Tailwind CSS classes are used for styling.

```html
<!-- inventory/materialissuenote/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Issue Note [MIN] - Edit</h2>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="min-search-form" 
              hx-get="{% url 'inventory:materialissuenote_table' %}" 
              hx-target="#materialissuenote-table-container" 
              hx-swap="innerHTML">
            {% csrf_token %} {# Include CSRF token for forms #}
            <div class="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <div class="flex-shrink-0 w-full sm:w-auto">
                    <label for="{{ form.DrpField.id_for_label }}" class="sr-only">{{ form.DrpField.label }}</label>
                    {{ form.DrpField }}
                </div>
                
                {# Search inputs container, dynamically updated by HTMX #}
                <div id="search-inputs" class="flex-grow flex items-center space-x-4 w-full sm:w-auto" 
                     x-data="{ employeeSearchTerm: '{{ form.TxtEmpName.value|default:"" }}' }"
                     hx-trigger="load" 
                     hx-get="{% url 'inventory:materialissuenote_search_controls' %}?DrpField={{ form.DrpField.value|default:'0' }}" 
                     hx-target="#search-inputs" 
                     hx-swap="innerHTML">
                    <!-- Initial loading state for search fields -->
                    <div class="text-gray-500 w-full text-center">Loading search fields...</div>
                </div>
                
                <div class="flex-shrink-0 w-full sm:w-auto">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm w-full">Search</button>
                </div>
            </div>
            
            {# Autocomplete suggestions container, positioned absolutely relative to its parent (the form) #}
            <div id="autocomplete-suggestions" 
                 class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full sm:w-[350px]">
            </div>
        </form>
    </div>

    {# Material Issue Notes table container, loaded/refreshed by HTMX #}
    <div id="materialissuenote-table-container"
         hx-trigger="load, refreshMaterialIssueNoteList from:body, submit from:#min-search-form"
         hx-get="{% url 'inventory:materialissuenote_table' %}"
         hx-target="this"
         hx-swap="innerHTML">
        <!-- Initial loading state for table -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Issue Notes...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for employee autocomplete functionality
        Alpine.data('employeeAutocomplete', () => ({
            employeeSearchTerm: '', // Binds to the input field
            suggestions: [], // Stores autocomplete suggestions
            
            selectSuggestion(suggestion) {
                // When a suggestion is clicked, set the input value and clear suggestions
                this.employeeSearchTerm = suggestion;
                this.suggestions = [];
            }
        }));

        // HTMX event listener to update Alpine.js state after autocomplete response is loaded
        // This listens for any HTMX request that targets '#autocomplete-suggestions'
        document.body.addEventListener('htmx:afterOnLoad', function(evt) {
            if (evt.detail.xhr.getResponseHeader('HX-Target') === 'autocomplete-suggestions') {
                try {
                    // Parse the JSON response from the autocomplete view
                    const suggestions = JSON.parse(evt.detail.xhr.responseText);
                    const employeeSearchInput = document.getElementById('TxtEmpName');
                    if (employeeSearchInput && suggestions) {
                        // Update the Alpine.js 'suggestions' property for the input
                        Alpine.$data(employeeSearchInput).suggestions = suggestions;
                    }
                } catch (e) {
                    console.error("Error parsing autocomplete response:", e);
                }
            }
        });
    });

    // Manually trigger the main search form submission after the search controls are loaded by HTMX
    // This ensures the table is populated correctly when the page initially loads or DrpField changes.
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'search-inputs') {
            const searchForm = document.getElementById('min-search-form');
            if (searchForm) {
                // Dispatch a 'submit' event to the form to trigger the table load
                searchForm.dispatchEvent(new Event('submit', { bubbles: true }));
            }
        }
    });

</script>
{% endblock %}
```

```html
<!-- inventory/materialissuenote/_search_controls.html -->
<!-- This partial template dynamically renders the specific search input field -->
<div class="flex-grow flex items-center space-x-4 w-full">
    <div class="relative flex-1">
        {% if selected_search_type == '2' %} {# Employee Name search #}
            <label for="{{ form.TxtEmpName.id_for_label }}" class="sr-only">{{ form.TxtEmpName.label }}</label>
            <input type="text" name="TxtEmpName" id="TxtEmpName" 
                   class="box3 p-2 border border-gray-300 rounded-md shadow-sm w-full focus:ring-blue-500 focus:border-blue-500" 
                   placeholder="Enter Employee Name" autocomplete="off"
                   x-model="employeeSearchTerm"
                   hx-get="{% url 'inventory:employee_autocomplete' %}" 
                   hx-trigger="keyup changed delay:300ms, search" 
                   hx-target="#autocomplete-suggestions" 
                   hx-swap="innerHTML"
                   x-on:click.outside="suggestions = []"
                   x-on:keyup.escape="suggestions = []"
                   x-on:focus="if (employeeSearchTerm.length > 0) $dispatch('triggerAutocomplete')">
            
            {# Autocomplete suggestions list handled by Alpine.js #}
            <template x-if="suggestions.length > 0">
                <ul class="absolute border border-gray-300 bg-white mt-1 max-h-48 overflow-y-auto z-10 w-full rounded-md shadow-lg" x-cloak>
                    <template x-for="suggestion in suggestions" :key="suggestion">
                        <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm" x-text="suggestion"
                            x-on:click="selectSuggestion(suggestion)"></li>
                    </template>
                </ul>
            </template>
        {% else %} {# MRS No or MIN No search #}
            <label for="{{ form.TxtMrs.id_for_label }}" class="sr-only">{{ form.TxtMrs.label }}</label>
            <input type="text" name="TxtMrs" id="TxtMrs" 
                   class="box3 p-2 border border-gray-300 rounded-md shadow-sm w-full focus:ring-blue-500 focus:border-blue-500" 
                   placeholder="Enter MRS/MIN No." value="{{ form.TxtMrs.value|default:'' }}">
        {% endif %}
    </div>
</div>
```

```html
<!-- inventory/materialissuenote/_materialissuenote_table.html -->
<!-- This partial template renders the DataTables content -->
{% if material_issue_notes %}
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="materialIssueNoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MIN No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for note in material_issue_notes %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ note.FinYearId }}</td> 
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ note.MINNo }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ note.SysDate|date:"d M Y" }}</td> {# Format date #}
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ note.MRSNo }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ note.GenBy }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">
                    {# Link to the (yet to be implemented) details page, passing required parameters #}
                    <a href="{% url 'inventory:materialissuenote_details' note.Id %}?MINNo={{ note.MINNo }}&MRSNo={{ note.MRSNo }}&FYId={{ note.FinYearId }}"
                       class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded-md text-xs">
                        Select
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
<script>
    // Initialize DataTables after HTMX swaps in the table content.
    // This script block will be re-executed every time this partial is loaded.
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#materialIssueNoteTable')) {
            $('#materialIssueNoteTable').DataTable().destroy();
        }
        $('#materialIssueNoteTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 20, // Matches ASP.NET GridView PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        });
    });
</script>
{% else %}
<div class="bg-white shadow-md rounded-lg p-6 text-center text-gray-600">
    <p class="font-bold text-lg">No data to display !</p>
</div>
{% endif %}
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**

URLs are defined within the `inventory` app's `urls.py` file, using namespaces for better organization.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    MaterialIssueNoteListView,
    MaterialIssueNoteTablePartialView,
    MaterialIssueNoteSearchControlsPartialView,
    EmployeeAutocompleteView
)

app_name = 'inventory' # Define app_name for namespacing URLs

urlpatterns = [
    # Main page for Material Issue Notes search and list
    path('materialissuenote/', MaterialIssueNoteListView.as_view(), name='materialissuenote_list'),
    
    # HTMX endpoint for the Material Issue Notes table content
    path('materialissuenote/table/', MaterialIssueNoteTablePartialView.as_view(), name='materialissuenote_table'),
    
    # HTMX endpoint for dynamic search input fields (TxtMrs/TxtEmpName)
    path('materialissuenote/search-controls/', MaterialIssueNoteSearchControlsPartialView.as_view(), name='materialissuenote_search_controls'),
    
    # HTMX endpoint for employee name autocomplete suggestions
    path('materialissuenote/employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    
    # Placeholder for the Material Issue Note details page (from Response.Redirect)
    # This URL would lead to a separate view/module for actual MIN editing/details.
    path('materialissuenote/details/<int:pk>/', View.as_view(), name='materialissuenote_details'), # Replace View.as_view() with actual detail view
]
```

### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**

Comprehensive unit tests cover model methods and properties, while integration tests verify the functionality of all views, including HTMX interactions. Mocking is used for external dependencies (like session values) to ensure isolated testing.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
import json
from datetime import datetime

from .models import MaterialIssueNote, Employee
from .forms import MaterialIssueNoteSearchForm

# --- Model Tests ---

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all Employee model tests
        cls.company_id = 1
        Employee.objects.create(EmpId=101, EmployeeName='John Doe', CompId=cls.company_id)
        Employee.objects.create(EmpId=102, EmployeeName='Jane Smith', CompId=cls.company_id)
        Employee.objects.create(EmpId=103, EmployeeName='Alice Brown', CompId=cls.company_id)
        Employee.objects.create(EmpId=201, EmployeeName='Other Company Employee', CompId=2)

    def test_employee_creation(self):
        """Test basic Employee object creation."""
        emp = Employee.objects.get(EmpId=101)
        self.assertEqual(emp.EmployeeName, 'John Doe')
        self.assertEqual(emp.CompId, self.company_id)
        self.assertEqual(emp.EmpId, 101)

    def test_str_representation(self):
        """Test the __str__ method of Employee model."""
        emp = Employee.objects.get(EmpId=101)
        self.assertEqual(str(emp), 'John Doe [101]')

    def test_get_employee_by_name_or_id_with_id_format(self):
        """Test retrieving employee by 'Name [ID]' format."""
        emp = Employee.get_employee_by_name_or_id('John Doe [101]')
        self.assertIsNotNone(emp)
        self.assertEqual(emp.EmpId, 101)
        self.assertEqual(emp.EmployeeName, 'John Doe')

    def test_get_employee_by_name_or_id_by_name(self):
        """Test retrieving employee by name only."""
        emp = Employee.get_employee_by_name_or_id('Jane Smith')
        self.assertIsNotNone(emp)
        self.assertEqual(emp.EmpId, 102)
        self.assertEqual(emp.EmployeeName, 'Jane Smith')

    def test_get_employee_by_name_or_id_not_found(self):
        """Test case where employee is not found."""
        emp = Employee.get_employee_by_name_or_id('Non Existent Employee')
        self.assertIsNone(emp)
        emp = Employee.get_employee_by_name_or_id('John Doe [999]') # Correct name, wrong ID
        self.assertIsNone(emp)

    def test_get_employee_suggestions_basic(self):
        """Test basic functionality of getting employee suggestions."""
        suggestions = Employee.get_employee_suggestions('john', self.company_id)
        self.assertIn(('John Doe', 101), list(suggestions))
        self.assertNotIn(('Jane Smith', 102), list(suggestions)) # Not matching prefix

    def test_get_employee_suggestions_case_insensitive(self):
        """Test case-insensitivity of employee name suggestions."""
        suggestions = Employee.get_employee_suggestions('jOhN', self.company_id)
        self.assertIn(('John Doe', 101), list(suggestions))

    def test_get_employee_suggestions_other_company(self):
        """Test filtering suggestions by company ID."""
        suggestions = Employee.get_employee_suggestions('employee', self.company_id)
        self.assertNotIn(('Other Company Employee', 201), list(suggestions)) # Different company ID


class MaterialIssueNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all MaterialIssueNote model tests
        cls.company_id = 1
        cls.fin_year_id = 1
        MaterialIssueNote.objects.create(
            Id=1, FinYearId=cls.fin_year_id, MINNo='MIN001', SysDate=datetime(2023, 1, 1),
            MRSNo='MRS001', GenBy='John Doe', SessionId=101, CompId=cls.company_id
        )
        MaterialIssueNote.objects.create(
            Id=2, FinYearId=cls.fin_year_id, MINNo='MIN002', SysDate=datetime(2023, 1, 5),
            MRSNo='MRS002', GenBy='Jane Smith', SessionId=102, CompId=cls.company_id
        )
        MaterialIssueNote.objects.create(
            Id=3, FinYearId=cls.fin_year_id, MINNo='MIN003', SysDate=datetime(2023, 1, 10),
            MRSNo='MRS003', GenBy='Alice Brown', SessionId=103, CompId=cls.company_id
        )
        # Data for a different company and financial year
        MaterialIssueNote.objects.create(
            Id=4, FinYearId=2, MINNo='MIN_OC1', SysDate=datetime(2023, 2, 1),
            MRSNo='MRS_OC1', GenBy='Other Employee', SessionId=201, CompId=2
        )
        # Create related employee objects for lookups
        Employee.objects.create(EmpId=101, EmployeeName='John Doe', CompId=cls.company_id)
        Employee.objects.create(EmpId=102, EmployeeName='Jane Smith', CompId=cls.company_id)
        Employee.objects.create(EmpId=103, EmployeeName='Alice Brown', CompId=cls.company_id)

    def test_materialissuenote_creation(self):
        """Test basic MaterialIssueNote object creation."""
        note = MaterialIssueNote.objects.get(Id=1)
        self.assertEqual(note.MINNo, 'MIN001')
        self.assertEqual(note.GenBy, 'John Doe')
        self.assertEqual(note.CompId, self.company_id)

    def test_str_representation(self):
        """Test the __str__ method of MaterialIssueNote model."""
        note = MaterialIssueNote.objects.get(Id=1)
        self.assertEqual(str(note), 'MIN001')

    def test_search_notes_no_filter(self):
        """Test search with no specific filter applied (all matching notes)."""
        notes = MaterialIssueNote.search_notes(
            company_id=self.company_id,
            fin_year_id=self.fin_year_id,
            search_type=None, # Or '0' with empty term
            search_term=''
        )
        self.assertEqual(notes.count(), 3)
        self.assertIn(MaterialIssueNote.objects.get(MINNo='MIN001'), notes)
        self.assertIn(MaterialIssueNote.objects.get(MINNo='MIN002'), notes)
        self.assertIn(MaterialIssueNote.objects.get(MINNo='MIN003'), notes)

    def test_search_notes_mrs_no(self):
        """Test search by MRS No."""
        notes = MaterialIssueNote.search_notes(
            company_id=self.company_id,
            fin_year_id=self.fin_year_id,
            search_type='0', # MRS No
            search_term='MRS002'
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().MINNo, 'MIN002')

    def test_search_notes_min_no(self):
        """Test search by MIN No."""
        notes = MaterialIssueNote.search_notes(
            company_id=self.company_id,
            fin_year_id=self.fin_year_id,
            search_type='1', # MIN No
            search_term='MIN003'
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().MRSNo, 'MRS003')

    def test_search_notes_employee_name_with_id_format(self):
        """Test search by employee name using 'Name [ID]' format."""
        notes = MaterialIssueNote.search_notes(
            company_id=self.company_id,
            fin_year_id=self.fin_year_id,
            search_type='2', # Employee Name
            search_term='John Doe [101]'
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().MINNo, 'MIN001')

    def test_search_notes_employee_name_only(self):
        """Test search by employee name only (without ID format)."""
        notes = MaterialIssueNote.search_notes(
            company_id=self.company_id,
            fin_year_id=self.fin_year_id,
            search_type='2', # Employee Name
            search_term='Jane Smith'
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().MINNo, 'MIN002')

    def test_search_notes_employee_name_not_found(self):
        """Test search by employee name where no match is found."""
        notes = MaterialIssueNote.search_notes(
            company_id=self.company_id,
            fin_year_id=self.fin_year_id,
            search_type='2',
            search_term='Non Existent Employee'
        )
        self.assertEqual(notes.count(), 0)

    def test_search_notes_other_company(self):
        """Test search filtered by a different company ID and financial year ID."""
        notes = MaterialIssueNote.search_notes(
            company_id=2,
            fin_year_id=2,
            search_type=None,
            search_term=''
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().MINNo, 'MIN_OC1')

# --- Form Tests ---

class MaterialIssueNoteSearchFormTest(TestCase):
    def test_form_initial_visibility_mrs_min(self):
        """Test initial visibility of fields when MRS/MIN is selected."""
        form = MaterialIssueNoteSearchForm(initial_search_type='0')
        self.assertNotIn('hidden', form.fields['TxtMrs'].widget.attrs.get('class', ''))
        self.assertIn('hidden', form.fields['TxtEmpName'].widget.attrs.get('class', ''))

    def test_form_initial_visibility_employee_name(self):
        """Test initial visibility of fields when Employee Name is selected."""
        form = MaterialIssueNoteSearchForm(initial_search_type='2')
        self.assertIn('hidden', form.fields['TxtMrs'].widget.attrs.get('class', ''))
        self.assertNotIn('hidden', form.fields['TxtEmpName'].widget.attrs.get('class', ''))

    def test_get_search_params_mrs(self):
        """Test extracting search parameters for MRS No."""
        form = MaterialIssueNoteSearchForm(data={'DrpField': '0', 'TxtMrs': 'TESTMRS', 'TxtEmpName': ''})
        self.assertTrue(form.is_valid())
        search_type, search_term = form.get_search_params()
        self.assertEqual(search_type, '0')
        self.assertEqual(search_term, 'TESTMRS')

    def test_get_search_params_employee_name(self):
        """Test extracting search parameters for Employee Name."""
        form = MaterialIssueNoteSearchForm(data={'DrpField': '2', 'TxtMrs': '', 'TxtEmpName': 'Test Employee'})
        self.assertTrue(form.is_valid())
        search_type, search_term = form.get_search_params()
        self.assertEqual(search_type, '2')
        self.assertEqual(search_term, 'Test Employee')

    def test_get_search_params_invalid_form(self):
        """Test get_search_params returns None for invalid form."""
        form = MaterialIssueNoteSearchForm(data={'DrpField': '', 'TxtMrs': '', 'TxtEmpName': ''}) # DrpField required
        self.assertFalse(form.is_valid())
        search_type, search_term = form.get_search_params()
        self.assertIsNone(search_type)
        self.assertIsNone(search_term)

# --- View Tests ---

# Mock settings for company_id and fin_year_id during tests to avoid dependency on actual settings file
@patch('django.conf.settings.DEFAULT_COMP_ID', 1)
@patch('django.conf.settings.DEFAULT_FIN_YEAR_ID', 1)
class MaterialIssueNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for views (similar to model test, but needed for views directly)
        cls.company_id = 1
        cls.fin_year_id = 1
        MaterialIssueNote.objects.create(
            Id=1, FinYearId=cls.fin_year_id, MINNo='MIN001', SysDate=datetime(2023, 1, 1),
            MRSNo='MRS001', GenBy='John Doe', SessionId=101, CompId=cls.company_id
        )
        MaterialIssueNote.objects.create(
            Id=2, FinYearId=cls.fin_year_id, MINNo='MIN002', SysDate=datetime(2023, 1, 5),
            MRSNo='MRS002', GenBy='Jane Smith', SessionId=102, CompId=cls.company_id
        )
        Employee.objects.create(EmpId=101, EmployeeName='John Doe', CompId=cls.company_id)
        Employee.objects.create(EmpId=102, EmployeeName='Jane Smith', CompId=cls.company_id)

    def setUp(self):
        self.client = Client()
        # Simulate an authenticated user for LoginRequiredMixin
        self.user = MagicMock()
        self.user.is_authenticated = True
        self.client.force_login(self.user) # Use force_login for simpler authentication in tests

    def test_materialissuenote_list_view_get(self):
        """Test the main list page view (GET request)."""
        response = self.client.get(reverse('inventory:materialissuenote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/list.html')
        self.assertIsInstance(response.context['form'], MaterialIssueNoteSearchForm)
        # Check for initial loading text for table container
        self.assertContains(response, 'Loading Material Issue Notes...')

    def test_materialissuenote_table_partial_view_get_no_params(self):
        """Test HTMX table partial with no search parameters (initial load)."""
        response = self.client.get(reverse('inventory:materialissuenote_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertContains(response, 'MIN001')
        self.assertContains(response, 'MIN002')
        self.assertContains(response, 'materialIssueNoteTable') # Check if table ID is present

    def test_materialissuenote_table_partial_view_get_mrs_search(self):
        """Test HTMX table partial with MRS No search."""
        response = self.client.get(reverse('inventory:materialissuenote_table'), {'DrpField': '0', 'TxtMrs': 'MRS001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertContains(response, 'MIN001')
        self.assertNotContains(response, 'MIN002')

    def test_materialissuenote_table_partial_view_get_employee_search(self):
        """Test HTMX table partial with employee name search (name only)."""
        response = self.client.get(reverse('inventory:materialissuenote_table'), {'DrpField': '2', 'TxtEmpName': 'Jane Smith'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertContains(response, 'MIN002')
        self.assertNotContains(response, 'MIN001')
        
    def test_materialissuenote_table_partial_view_get_employee_search_with_id(self):
        """Test HTMX table partial with employee name search ('Name [ID]' format)."""
        response = self.client.get(reverse('inventory:materialissuenote_table'), {'DrpField': '2', 'TxtEmpName': 'John Doe [101]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertContains(response, 'MIN001')
        self.assertNotContains(response, 'MIN002')

    def test_materialissuenote_table_partial_view_no_data(self):
        """Test HTMX table partial when no data matches the search."""
        response = self.client.get(reverse('inventory:materialissuenote_table'), {'DrpField': '0', 'TxtMrs': 'NONEXISTENT'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertContains(response, 'No data to display !')
        self.assertNotContains(response, 'materialIssueNoteTable') # Table should not be rendered

    def test_materialissuenote_search_controls_partial_view_mrs(self):
        """Test HTMX search controls partial when MRS/MIN option is selected."""
        response = self.client.get(reverse('inventory:materialissuenote_search_controls'), {'DrpField': '0'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_search_controls.html')
        self.assertContains(response, '<input type="text" name="TxtMrs"')
        self.assertNotContains(response, '<input type="text" name="TxtEmpName"')

    def test_materialissuenote_search_controls_partial_view_emp_name(self):
        """Test HTMX search controls partial when Employee Name option is selected."""
        response = self.client.get(reverse('inventory:materialissuenote_search_controls'), {'DrpField': '2'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_search_controls.html')
        self.assertContains(response, '<input type="text" name="TxtEmpName"')
        self.assertNotContains(response, '<input type="text" name="TxtMrs"')

    def test_employee_autocomplete_view_suggestions(self):
        """Test employee autocomplete endpoint returns suggestions."""
        response = self.client.get(reverse('inventory:employee_autocomplete'), {'q': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        suggestions = json.loads(response.content)
        self.assertIn('John Doe [101]', suggestions)
        self.assertNotIn('Jane Smith [102]', suggestions) # Not matching prefix

    def test_employee_autocomplete_view_empty_query(self):
        """Test employee autocomplete endpoint with empty query."""
        response = self.client.get(reverse('inventory:employee_autocomplete'), {'q': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        suggestions = json.loads(response.content)
        self.assertEqual(suggestions, [])

    def test_employee_autocomplete_view_no_match(self):
        """Test employee autocomplete endpoint when no match is found."""
        response = self.client.get(reverse('inventory:employee_autocomplete'), {'q': 'xyz_no_match'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        suggestions = json.loads(response.content)
        self.assertEqual(suggestions, [])

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The plan fully integrates HTMX for server-side dynamism and Alpine.js for client-side UI management, ensuring a modern, interactive user experience without traditional full-page reloads.

-   **HTMX for Search:**
    -   The main search form (`#min-search-form`) uses `hx-get` to send parameters to `materialissuenote_table` and `hx-target="#materialissuenote-table-container"` with `hx-swap="innerHTML"` to update only the table section.
    -   The `DrpField` dropdown (search type selector) has `hx-get` to `materialissuenote_search_controls` and `hx-target="#search-inputs"` with `hx-trigger="change"`, allowing dynamic swapping of `TxtMrs` or `TxtEmpName` input fields.
-   **HTMX for Autocomplete:**
    -   `TxtEmpName` input has `hx-get` to `employee_autocomplete` and `hx-target="#autocomplete-suggestions"` with `hx-trigger="keyup changed delay:300ms, search"`. This triggers a server call for suggestions after user input, updating a dedicated suggestion container.
-   **Alpine.js for UI State:**
    -   `x-data="employeeAutocomplete()"` on the search inputs container (`#search-inputs`) manages the `employeeSearchTerm` and `suggestions` array for the autocomplete.
    -   `x-model="employeeSearchTerm"` binds the input value to the Alpine.js state.
    -   `x-if="suggestions.length > 0"` conditionally renders the suggestion list.
    -   `x-for="suggestion in suggestions"` iterates and displays suggestions.
    -   `x-on:click="selectSuggestion(suggestion)"` updates the input and hides suggestions when a suggestion is clicked.
    -   `x-on:click.outside` and `x-on:keyup.escape` provide intuitive ways to dismiss suggestions.
-   **DataTables Integration:**
    -   The `_materialissuenote_table.html` partial includes a `script` block that initializes DataTables (`$('#materialIssueNoteTable').DataTable()`) immediately after the partial is loaded and swapped in by HTMX. This ensures DataTables correctly applies its features (pagination, search, sort) to the newly loaded table content.
-   **No Full Page Reloads:** All interactions, including changing search criteria, triggering searches, and showing autocomplete, are handled via HTMX, leading to a Single Page Application-like experience.
-   **Success Triggers:** While this specific page doesn't have create/update actions, the principle of `HX-Trigger` for list refreshes would be applied if CRUD operations were present on this page, or when the detail page redirects back. For now, `submit from:#min-search-form` on the table partial handles the refresh after a search.

## Final Notes

-   **Placeholder URLs:** The `materialissuenote_details` URL is a placeholder. Its actual implementation (view and template) for editing/viewing details would reside in a separate Django module or within this `inventory` app, following a similar modernization process.
-   **Session Data (`CompId`, `FinYearId`):** In the provided Django code, `settings.DEFAULT_COMP_ID` and `settings.DEFAULT_FIN_YEAR_ID` are used as placeholders. In a real application, these values would typically be sourced from the authenticated `request.user` object (e.g., `request.user.profile.company_id`) or a session-based context. You would need to ensure these are properly set up in your Django project's `settings.py` or middleware.
-   **DRY Templates:** The use of partial templates (`_search_controls.html`, `_materialissuenote_table.html`) promotes reusability and clean separation of concerns, preventing repetition.
-   **Business Logic in Models:** The `search_notes` method in the `MaterialIssueNote` model and `get_employee_suggestions`/`get_employee_by_name_or_id` in the `Employee` model keep the views very lean, enhancing maintainability and testability.
-   **Testing:** The provided test suite covers model logic, form behavior, and crucial view interactions (including HTMX-specific requests), ensuring high code quality and functional correctness.