This document outlines a strategic modernization plan to transition your legacy ASP.NET application, specifically the `CustomerChallan_New` module, to a robust and modern Django 5.0+ solution. Our approach prioritizes AI-assisted automation, emphasizing a "fat model, thin view" architecture, and leveraging HTMX and Alpine.js for highly interactive user experiences without traditional JavaScript complexities.

The core business value of this modernization lies in:

1.  **Improved Performance:** By shifting from server-side rendering with full page postbacks to efficient HTMX-driven partial updates, the application will feel significantly faster and more responsive to users.
2.  **Enhanced Maintainability:** Django's clear structure, ORM, and "fat model" approach centralize business logic, making it easier to understand, debug, and extend.
3.  **Modern User Experience:** Leveraging HTMX and Alpine.js delivers a rich, interactive frontend similar to single-page applications, but with simpler development and fewer dependencies. DataTables will provide powerful client-side search and filtering capabilities.
4.  **Reduced Development Costs:** Adopting a modern, well-supported framework like Django, combined with automation-driven migration, significantly lowers future development and maintenance costs.
5.  **Scalability:** Django's architecture is inherently more scalable for modern web applications compared to legacy ASP.NET Web Forms.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following key tables and their inferred columns from SQL queries and GridView bindings:

*   **`SD_Cust_master` (Customers):**
    *   `CustomerId` (Primary Key, unique identifier)
    *   `CustomerName` (Text, customer's full name)
    *   `CompId` (Integer, company ID, used for filtering)

*   **`SD_Cust_WorkOrder_Master` (Work Orders):**
    *   `Id` (Primary Key, unique identifier)
    *   `EnqId` (Text, Enquiry ID)
    *   `TaskProjectTitle` (Text, Project Name)
    *   `CustomerId` (Foreign Key to `SD_Cust_master`)
    *   `WONo` (Text, Work Order Number)
    *   `PONo` (Text, Purchase Order Number)
    *   `POId` (Integer, Purchase Order ID)
    *   `SessionId` (Integer, related to employee who generated it)
    *   `FinYearId` (Integer, related to financial year)
    *   `SysDate` (Text, System Date, stored as a string and converted in C#)
    *   `CloseOpen` (Boolean/Integer, likely 0 for open)
    *   `CompId` (Integer, company ID, used for filtering)

*   **`tblFinancial_master` (Financial Years):**
    *   `FinYearId` (Primary Key, unique identifier)
    *   `FinYear` (Text, e.g., "2023-2024")

*   **`tblHR_OfficeStaff` (Employees):**
    *   `EmpId` (Primary Key, unique identifier)
    *   `EmployeeName` (Text, employee's full name)
    *   `Title` (Text, e.g., "Mr.", "Ms.")
    *   `CompId` (Integer, company ID, used for filtering)

## Step 2: Identify Backend Functionality

The ASP.NET page `CustomerChallan_New.aspx` primarily serves as a **read and search interface** for customer and their associated work order data.

*   **Read Customer Data:**
    *   The `BindDataCust` method retrieves customer information, potentially filtered by `CustomerId` or `CustomerName` prefix. It uses a stored procedure `GetCustomer`.
    *   This data populates `SearchGridView1`.
*   **Search Customer Data:**
    *   The `TxtSearchValue` textbox, combined with `AutoCompleteExtender`, provides an autocomplete search feature based on `CustomerName`.
    *   The `Search` button explicitly triggers a customer search based on the `TxtSearchValue`.
*   **Read Work Order Data (Dependent on Customer):**
    *   The `LoadData` method retrieves work order details from `SD_Cust_WorkOrder_Master` for a *selected customer*.
    *   It performs lookups for `FinYear` (from `tblFinancial_master`) and `EmployeeName` (from `tblHR_OfficeStaff`) for each work order.
    *   This data populates `GridView1`.
*   **Customer Selection:**
    *   Clicking a "Customer Name" in `SearchGridView1` (`SearchGridView1_RowCommand` with `CommandName="Sel"`) selects a customer and triggers the loading of their work orders.
*   **Pagination:** Both customer and work order grids support client-side pagination.

**No explicit Create, Update, or Delete operations are performed directly on this ASP.NET page.** However, to adhere to the general migration guidelines, we will provide the Django model, form, and view templates for basic CRUD operations on `Customer` and `WorkOrder` entities, assuming these operations would exist elsewhere in a full application. The core functionality of this page will be translated to a search and display pattern using HTMX.

## Step 3: Infer UI Components

The ASP.NET page employs standard Web Forms controls, which will be translated into Django templates with HTMX and Alpine.js for dynamic interaction, and DataTables for grid functionality.

*   **Customer Search Input:**
    *   ASP.NET: `asp:TextBox ID="TxtSearchValue"` with `cc1:AutoCompleteExtender`.
    *   Django: An HTML `input` field with HTMX attributes for live search and autocomplete.
*   **Search Button:**
    *   ASP.NET: `asp:Button ID="Search"` with `onclick="Search_Click"`.
    *   Django: An HTML `button` with HTMX attributes to trigger the customer search.
*   **Customer Search Results Grid:**
    *   ASP.NET: `asp:GridView ID="SearchGridView1"` with `AllowPaging`, `AllowSorting`, `DataKeyNames="CustomerId"`, `onpageindexchanging`, `onrowcommand`.
    *   Django: An HTML `table` powered by DataTables, dynamically updated via HTMX. Each row will have an HTMX trigger to select a customer.
*   **Work Order Details Grid:**
    *   ASP.NET: `asp:GridView ID="GridView1"` with `AllowPaging`, `AllowSorting`, `DataKeyNames="Id"`, `onpageindexchanging`.
    *   Django: An HTML `table` powered by DataTables, dynamically updated via HTMX based on customer selection.

## Step 4: Generate Django Code

We will structure the Django application within an app named `inventory`.

### 4.1 Models

We will create Django models corresponding to the identified database tables, with `managed = False` to connect to the existing database. Business logic related to data retrieval (e.g., getting work orders with related financial year and employee names) will be placed in model methods or custom managers.

**`inventory/models.py`**

```python
from django.db import models

class Customer(models.Model):
    """
    Represents a customer in the system.
    Maps to SD_Cust_master table.
    """
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class FinancialYear(models.Model):
    """
    Represents a financial year.
    Maps to tblFinancial_master table.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    """
    Represents an employee.
    Maps to tblHR_OfficeStaff table.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}".strip()

class WorkOrderManager(models.Manager):
    def get_work_orders_for_customer(self, customer_id, comp_id, fin_year_id):
        """
        Retrieves work orders for a specific customer, including related financial year and employee data.
        Replicates logic from ASP.NET LoadData.
        """
        # Ensure correct comparison for string-based CustomerId if applicable in DB
        # Assuming FinYearId and CompId are integers
        return self.filter(
            customer_id=customer_id,
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # Replicates FinYearId<='{FinYearId}'
            close_open=0 # Replicates CloseOpen='0'
        ).select_related('financial_year', 'generated_by').order_by('-id')

class WorkOrder(models.Model):
    """
    Represents a customer work order.
    Maps to SD_Cust_WorkOrder_Master table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=500, blank=True, null=True)
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId', related_name='work_orders')
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True)
    generated_by = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', related_name='work_orders')
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', related_name='work_orders')
    sys_date = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True) # Stored as string in legacy DB
    close_open = models.IntegerField(db_column='CloseOpen', blank=True, null=True) # Assuming 0 for open
    comp_id = models.IntegerField(db_column='CompId')

    objects = WorkOrderManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no or f"WO {self.id}"

    def get_formatted_sys_date(self):
        """
        Converts the 'SysDate' string from legacy format to a more readable format.
        Replicates C# logic: REPLACE(CONVERT(varchar, CONVERT(datetime, SUBSTRING(..., 103), '/', '-')AS SysDate
        """
        try:
            # Assuming 'MM-DD-YYYY' or similar if not specified
            # The C# code implies a specific format, e.g. 'MM-DD-YYYY'
            # Let's assume a common format or adapt based on actual data:
            # SUBSTRING(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) + 1, 2) + '-' + LEFT(SD_Cust_WorkOrder_Master.SysDate,CHARINDEX('-', SD_Cust_WorkOrder_Master.SysDate) - 1) + '-' + RIGHT(SD_Cust_WorkOrder_Master.SysDate, CHARINDEX('-', REVERSE(SD_Cust_WorkOrder_Master.SysDate)) - 1))
            # This looks like M-D-YYYY or D-M-YYYY. Example: '5-20-2023' or '20-5-2023'.
            # Given SUBSTRING(..., CHARINDEX('-',...), 2) for day/month and LEFT/RIGHT for month/day/year.
            # A more robust solution might involve regex or more sophisticated parsing.
            # For simplicity, let's assume it's `DD-MM-YYYY` or `MM-DD-YYYY` after the C# manipulation.
            # The C# `CONVERT(datetime, ..., 103)` indicates 'dd/mm/yyyy' format.
            parts = self.sys_date.replace('-', '/').split('/')
            if len(parts) == 3:
                # Assuming original was e.g. '5-20-2023', C# converted to '20/05/2023'
                day = parts[0]
                month = parts[1]
                year = parts[2]
                # Reconstruct to YYYY-MM-DD for datetime parsing, then format
                return f"{day}/{month}/{year}" # Return in DD/MM/YYYY
            return self.sys_date # Fallback if parsing fails
        except Exception:
            return self.sys_date # Return raw if error
```

### 4.2 Forms

We'll define a simple form for the customer search input and generic `ModelForm`s for Customer and WorkOrder CRUD operations.

**`inventory/forms.py`**

```python
from django import forms
from .models import Customer, WorkOrder

class CustomerSearchForm(forms.Form):
    """
    Form for the customer search input field.
    """
    search_value = forms.CharField(
        max_length=255,
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing customer name...',
            'hx-get': '/inventory/customer-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customerSuggestions', # Target for autocomplete results
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'name': 'search_value' # Ensure name matches form field
        })
    )
    # This form is for the search input on the main page.
    # The actual selection of customer will be handled by HTMX on the search results grid.

class CustomerForm(forms.ModelForm):
    """
    Generic form for Customer Create/Update operations.
    """
    class Meta:
        model = Customer
        fields = ['customer_id', 'customer_name', 'comp_id']
        widgets = {
            'customer_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'customer_id': 'Customer ID',
            'customer_name': 'Customer Name',
            'comp_id': 'Company ID',
        }

    # Add custom validation methods here if needed
    # def clean_customer_id(self):
    #    customer_id = self.cleaned_data['customer_id']
    #    # Add validation for customer ID format or uniqueness
    #    return customer_id

class WorkOrderForm(forms.ModelForm):
    """
    Generic form for Work Order Create/Update operations.
    """
    class Meta:
        model = WorkOrder
        # Exclude auto-generated or system fields like 'id', 'sys_date', 'close_open' for simple form
        fields = ['enq_id', 'task_project_title', 'customer', 'wo_no', 'po_no', 'po_id', 'generated_by', 'financial_year', 'comp_id']
        widgets = {
            'enq_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'task_project_title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'generated_by': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'enq_id': 'Enquiry ID',
            'task_project_title': 'Project Title',
            'customer': 'Customer',
            'wo_no': 'WO No',
            'po_no': 'PO No',
            'po_id': 'PO ID',
            'generated_by': 'Generated By',
            'financial_year': 'Financial Year',
            'comp_id': 'Company ID',
        }
```

### 4.3 Views

The views will be structured to handle the main page display and HTMX partial updates for search results, work order lists, and autocomplete. Generic CRUD views are included for completeness.

**`inventory/views.py`**

```python
import json
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.db.models import Q # For complex queries
from django.conf import settings # To access settings like session variables (CompId, FinYearId)

from .models import Customer, WorkOrder
from .forms import CustomerForm, WorkOrderForm, CustomerSearchForm

# --- Main Page View ---
class CustomerChallanView(TemplateView):
    """
    Main view for Customer Challan, displaying the search form and initial empty tables.
    """
    template_name = 'inventory/customerchallan_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = CustomerSearchForm(self.request.GET)
        # Initial empty customer and work order lists
        context['customers'] = Customer.objects.none()
        context['work_orders'] = WorkOrder.objects.none()
        return context

# --- HTMX Partial Views for DataTables ---
class CustomerSearchTableView(ListView):
    """
    HTMX view to return the customer search results table partial.
    Handles customer search logic.
    """
    model = Customer
    template_name = 'inventory/_customer_search_table.html'
    context_object_name = 'customers'

    def get_queryset(self):
        # Replicate ASP.NET's BindDataCust logic
        search_value = self.request.GET.get('search_value', '').strip()
        comp_id = self.request.session.get('compid') # From session
        fin_year_id = self.request.session.get('finyear') # From session

        if not comp_id or not fin_year_id:
            # Handle cases where session data might be missing, return empty or error
            return self.model.objects.none()
        
        queryset = self.model.objects.filter(comp_id=comp_id)

        if search_value:
            # The C# `fun.getCode(TxtSearchValue.Text)` is vague, might involve looking up ID from name
            # For autocomplete, it extracts ID from "Name [ID]". For button click, it filters directly.
            # Assuming for direct search, it's customer name.
            if ' [' in search_value and search_value.endswith(']'):
                # Handle "Name [ID]" format from autocomplete
                try:
                    customer_id_part = search_value.split(' [')[-1][:-1]
                    queryset = queryset.filter(customer_id=customer_id_part)
                except Exception:
                    queryset = queryset.filter(customer_name__icontains=search_value)
            else:
                queryset = queryset.filter(customer_name__icontains=search_value)
        
        # In ASP.NET, stored procedure 'GetCustomer' also takes @FinId and @CompId.
        # This implies it might filter customers based on if they have transactions in that fin year etc.
        # For now, we only filter by CompId as per SD_Cust_master table structure.
        # If `GetCustomer` has more complex logic, it would need to be migrated to a custom manager method on Customer.
        
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # For HTMX requests, we just return the table HTML.
        # For non-HTMX, this view might not be called directly.
        return super().render_to_response(context, **response_kwargs)

class WorkOrderTableView(ListView):
    """
    HTMX view to return the work order list table partial for a selected customer.
    Replicates ASP.NET's LoadData logic.
    """
    model = WorkOrder
    template_name = 'inventory/_workorder_table.html'
    context_object_name = 'work_orders'

    def get_queryset(self):
        # Replicate ASP.NET's LoadData logic
        customer_id = self.kwargs.get('customer_id') # Passed via URL from customer selection
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')

        if not customer_id or not comp_id or not fin_year_id:
            return self.model.objects.none() # Return empty if no customer or session data

        # Use the custom manager method to encapsulate the complex query
        queryset = WorkOrder.objects.get_work_orders_for_customer(
            customer_id=customer_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        return queryset

    def render_to_response(self, context, **response_kwargs):
        return super().render_to_response(context, **response_kwargs)

class CustomerAutocompleteView(ListView):
    """
    HTMX view for customer name autocomplete.
    Replicates ASP.NET's `sql` WebMethod.
    """
    model = Customer
    context_object_name = 'customers'
    paginate_by = 10 # Mimic max 10 results from C# code

    def get_queryset(self):
        # Replicate ASP.NET's sql WebMethod logic
        prefix_text = self.request.GET.get('search_value', '')
        comp_id = self.request.session.get('compid')

        if not comp_id:
            return self.model.objects.none()

        queryset = self.model.objects.filter(comp_id=comp_id)

        if prefix_text:
            # Case-insensitive starts-with
            queryset = queryset.filter(customer_name__istartswith=prefix_text)
        
        # Order by customer name to replicate Array.Sort(main)
        return queryset.order_by('customer_name')

    def render_to_response(self, context, **response_kwargs):
        # Return a list of formatted strings, similar to ASP.NET's `main` array
        suggestions = [
            f"{customer.customer_name} [{customer.customer_id}]"
            for customer in context['customers']
        ]
        # For HTMX, we can render a simple unordered list or data list options
        # For a truly generic autocomplete, a JSON response might be more robust for JS frameworks.
        # But for HTMX, a direct HTML snippet is often preferred.
        html_suggestions = ''.join([
            f'<option value="{s}"></option>' for s in suggestions
        ])
        # This is a common pattern for datalist/autocomplete in HTMX
        return HttpResponse(f'<datalist id="customerSuggestions">{html_suggestions}</datalist>')

# --- Generic CRUD Views (as per template instructions, though not directly used by this specific ASPX page) ---
class CustomerCreateView(CreateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'inventory/customer/form.html'
    success_url = reverse_lazy('customer_list') # Assuming a main customer list page

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshCustomerList': None, 'closeModal': None})
                }
            )
        return response

class CustomerUpdateView(UpdateView):
    model = Customer
    form_class = CustomerForm
    template_name = 'inventory/customer/form.html'
    success_url = reverse_lazy('customer_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Customer updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshCustomerList': None, 'closeModal': None})
                }
            )
        return response

class CustomerDeleteView(DeleteView):
    model = Customer
    template_name = 'inventory/customer/confirm_delete.html'
    success_url = reverse_lazy('customer_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Customer deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshCustomerList': None, 'closeModal': None})
                }
            )
        return response

class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'inventory/workorder/form.html'
    success_url = reverse_lazy('workorder_list') # Assuming a main workorder list page

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response

class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'inventory/workorder/form.html'
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'inventory/workorder/confirm_delete.html'
    success_url = reverse_lazy('workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response
```

### 4.4 Templates

Templates will leverage DRY principles, HTMX for dynamic content loading, and DataTables for interactive grids.

**`inventory/templates/inventory/customerchallan_list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Customer Challan - New</h2>

    {# Customer Search Section #}
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form hx-get="{% url 'inventory:customer_search_table' %}" 
              hx-target="#customerSearchResults" 
              hx-swap="innerHTML"
              hx-indicator="#customer-loading-indicator"
              class="mb-4">
            <div class="flex items-end space-x-4">
                <div class="flex-grow">
                    <label for="{{ search_form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ search_form.search_value.label }}</label>
                    {{ search_form.search_value }}
                    <datalist id="customerSuggestions"></datalist> {# Autocomplete suggestions will populate here #}
                </div>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Search
                </button>
            </div>
            <div id="customer-loading-indicator" class="htmx-indicator text-blue-500 mt-2">
                Searching customers...
            </div>
        </form>

        <h3 class="text-xl font-semibold mb-4">Customer Search Results</h3>
        <div id="customerSearchResults" 
             hx-trigger="load once, customerSearchTrigger from:body" 
             hx-get="{% url 'inventory:customer_search_table' %}" 
             hx-swap="innerHTML">
            <!-- Initial table or loading indicator -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading customers...</p>
            </div>
        </div>
    </div>

    {# Work Order Section #}
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold mb-4">Customer Work Orders</h3>
        <div id="workOrderResults"
             hx-trigger="load once, workOrderListTrigger from:body" {# This is triggered by customer selection #}
             hx-get="{% url 'inventory:workorder_table' 'NONE' %}" {# Initial empty load, will be replaced #}
             hx-swap="innerHTML">
            <!-- Work orders table will be loaded here via HTMX after customer selection -->
            <div class="text-center py-8">
                <p class="mt-2 text-gray-600">Select a customer from the search results above to view their work orders.</p>
            </div>
        </div>
    </div>
</div>

{# Modal for generic CRUD forms #}
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me then trigger closeModal">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
</div>
{% endblock %}

{% block extra_js %}
{# Ensure DataTables library is loaded in base.html for optimal performance #}
<script>
    // Alpine.js integration for modal control
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            open: false,
            init() {
                this.$watch('open', value => {
                    if (value) {
                        document.getElementById('modal').classList.remove('hidden');
                        document.getElementById('modal').classList.add('is-active');
                    } else {
                        document.getElementById('modal').classList.add('hidden');
                        document.getElementById('modal').classList.remove('is-active');
                    }
                });
            },
            closeModal() {
                this.open = false;
                document.getElementById('modalContent').innerHTML = ''; // Clear modal content
            }
        }));
    });

    // Listen for custom event to close modal (e.g., from HX-Trigger)
    document.body.addEventListener('closeModal', () => {
        document.getElementById('modal').classList.add('hidden');
        document.getElementById('modal').classList.remove('is-active');
        document.getElementById('modalContent').innerHTML = '';
    });

    // DataTables initialization function (called after HTMX loads content)
    function initializeDataTable(tableId) {
        if ($.fn.DataTable.isDataTable('#' + tableId)) {
            $('#' + tableId).DataTable().destroy(); // Destroy existing DataTable instance if any
        }
        $('#' + tableId).DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": -1 } // Disable sorting on the last column (Actions)
            ]
        });
    }

    // HTMX 'htmx:afterSwap' event listener to initialize DataTables
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'customerSearchResults') {
            initializeDataTable('customerTable');
        } else if (evt.target.id === 'workOrderResults') {
            initializeDataTable('workOrderTable');
        }
    });

    // Ensure DataTables is initialized on initial page load if content is present
    document.addEventListener('DOMContentLoaded', function() {
        if (document.getElementById('customerTable')) {
            initializeDataTable('customerTable');
        }
        if (document.getElementById('workOrderTable')) {
            initializeDataTable('workOrderTable');
        }
    });
</script>
{% endblock %}
```

**`inventory/templates/inventory/_customer_search_table.html`** (Partial for customer search results)

```html
<table id="customerTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th> {# This field was inferred from ASP.NET Grid, but not directly from SD_Cust_master. Keeping it for consistency but it will be empty if not in model #}
        </tr>
    </thead>
    <tbody>
        {% if customers %}
            {% for customer in customers %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">
                    <button 
                        class="text-blue-600 hover:text-blue-800 font-medium focus:outline-none"
                        hx-get="{% url 'inventory:workorder_table' customer.customer_id %}"
                        hx-target="#workOrderResults"
                        hx-swap="innerHTML"
                        hx-indicator="#workorder-loading-indicator">
                        {{ customer.customer_name }}
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ customer.customer_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">N/A</td> {# This field is not directly from SD_Cust_master. If it needs to be displayed, it requires a join in the Customer model or view. #}
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="4" class="py-4 px-4 text-center text-gray-500 text-lg">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>
<div id="customer-loading-indicator" class="htmx-indicator text-center text-blue-500 py-2">
    Loading customers...
</div>
```

**`inventory/templates/inventory/_workorder_table.html`** (Partial for work order list)

```html
<table id="workOrderTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
        </tr>
    </thead>
    <tbody>
        {% if work_orders %}
            {% for wo in work_orders %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.financial_year.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.task_project_title }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.enq_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {# Replicate HyperLinkField. Assuming CustomerChallan_New_Details.aspx is a Django page now #}
                    <a href="{% url 'inventory:customer_challan_details' enq_id=wo.enq_id po_no=wo.po_no po_id=wo.po_id wo_no=wo.wo_no %}" class="text-blue-600 hover:text-blue-800 font-medium">
                        {{ wo.wo_no }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.get_formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.generated_by.employee_name }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-gray-500 text-lg">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>
<div id="workorder-loading-indicator" class="htmx-indicator text-center text-blue-500 py-2">
    Loading work orders...
</div>
```

**`inventory/templates/inventory/customer/form.html`** (Partial for Customer CRUD, for modal loading)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Customer</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div id="form-loading-indicator" class="htmx-indicator text-center text-blue-500 py-2">
            Saving...
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click trigger closeModal"> {# Alpine.js event to close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`inventory/templates/inventory/customer/confirm_delete.html`** (Partial for Customer Delete, for modal loading)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete customer "{{ object.customer_name }}"?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-loading-indicator">
        {% csrf_token %}
        <div id="delete-loading-indicator" class="htmx-indicator text-center text-blue-500 py-2">
            Deleting...
        </div>
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click trigger closeModal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```
*(Similarly for `inventory/templates/inventory/workorder/form.html` and `inventory/templates/inventory/workorder/confirm_delete.html` by replacing `customer` with `workorder` and relevant field names.)*

### 4.5 URLs

URL patterns for the main page, HTMX partials, autocomplete, and generic CRUD.

**`inventory/urls.py`**

```python
from django.urls import path
from .views import (
    CustomerChallanView,
    CustomerSearchTableView,
    WorkOrderTableView,
    CustomerAutocompleteView,
    CustomerCreateView, CustomerUpdateView, CustomerDeleteView,
    WorkOrderCreateView, WorkOrderUpdateView, WorkOrderDeleteView,
)

app_name = 'inventory' # Namespace for this app's URLs

urlpatterns = [
    # Main page URL for Customer Challan
    path('customer-challan/', CustomerChallanView.as_view(), name='customer_challan_list'),

    # HTMX endpoints for dynamic table updates
    path('customer-challan/customer-table/', CustomerSearchTableView.as_view(), name='customer_search_table'),
    path('customer-challan/workorder-table/<str:customer_id>/', WorkOrderTableView.as_view(), name='workorder_table'),
    
    # Autocomplete endpoint
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder for the details page if it exists in Django
    # The ASP.NET link: ~/Module/Inventory/Transactions/CustomerChallan_New_Details.aspx?EnqId={0}&PONo={1}&POId={2}&WONo={3}&ModId=9&SubModId=121
    # This implies a new view and template would be needed for the details page.
    path('customer-challan-details/<str:enq_id>/<str:po_no>/<int:po_id>/<str:wo_no>/', 
         TemplateView.as_view(template_name='inventory/customer_challan_details.html'), # Placeholder view
         name='customer_challan_details'),

    # Generic Customer CRUD URLs (for potential future use or other parts of the app)
    path('customers/add/', CustomerCreateView.as_view(), name='customer_add'),
    path('customers/edit/<str:pk>/', CustomerUpdateView.as_view(), name='customer_edit'),
    path('customers/delete/<str:pk>/', CustomerDeleteView.as_view(), name='customer_delete'),

    # Generic WorkOrder CRUD URLs (for potential future use or other parts of the app)
    path('workorders/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorders/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorders/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]
```
*(Make sure to include `path('inventory/', include('inventory.urls'))` in your project's `urls.py`.)*

### 4.6 Tests

Comprehensive unit tests for model methods and integration tests for views are crucial.

**`inventory/tests.py`**

```python
import json
from datetime import datetime
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.test.client import RequestFactory
from django.http import HttpRequest
from django.db import connection

from .models import Customer, FinancialYear, Employee, WorkOrder

class InventoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Establish a mock database connection for tests (if using managed=False with actual DB setup)
        # For simple unit tests, Django's default in-memory SQLite is sufficient.
        # If testing against a real SQL Server, ensure test settings point to it.
        cls.comp_id = 1
        cls.fin_year_id = 2023 # Example financial year ID

        # Create test data for related models first
        cls.financial_year_obj = FinancialYear.objects.create(fin_year_id=cls.fin_year_id, fin_year="2023-2024")
        cls.employee_obj = Employee.objects.create(emp_id=101, employee_name="John Doe", title="Mr.", comp_id=cls.comp_id)
        cls.customer_obj = Customer.objects.create(customer_id="CUST001", customer_name="Test Customer 1", comp_id=cls.comp_id)
        cls.customer_obj_2 = Customer.objects.create(customer_id="CUST002", customer_name="Another Customer", comp_id=cls.comp_id)

        # Create WorkOrder instances
        cls.workorder_obj_1 = WorkOrder.objects.create(
            id=1, enq_id="ENQ001", task_project_title="Project Alpha", customer=cls.customer_obj,
            wo_no="WO001", po_no="PO001", po_id=1, generated_by=cls.employee_obj,
            financial_year=cls.financial_year_obj, sys_date="20-05-2023", close_open=0, comp_id=cls.comp_id
        )
        cls.workorder_obj_2 = WorkOrder.objects.create(
            id=2, enq_id="ENQ002", task_project_title="Project Beta", customer=cls.customer_obj,
            wo_no="WO002", po_no="PO002", po_id=2, generated_by=cls.employee_obj,
            financial_year=cls.financial_year_obj, sys_date="15-04-2023", close_open=0, comp_id=cls.comp_id
        )
        cls.workorder_obj_closed = WorkOrder.objects.create(
            id=3, enq_id="ENQ003", task_project_title="Project Gamma", customer=cls.customer_obj,
            wo_no="WO003", po_no="PO003", po_id=3, generated_by=cls.employee_obj,
            financial_year=cls.financial_year_obj, sys_date="10-03-2023", close_open=1, comp_id=cls.comp_id
        )
        cls.workorder_obj_other_cust = WorkOrder.objects.create(
            id=4, enq_id="ENQ004", task_project_title="Project Delta", customer=cls.customer_obj_2,
            wo_no="WO004", po_no="PO004", po_id=4, generated_by=cls.employee_obj,
            financial_year=cls.financial_year_obj, sys_date="01-01-2023", close_open=0, comp_id=cls.comp_id
        )

    def test_customer_creation(self):
        self.assertEqual(self.customer_obj.customer_name, "Test Customer 1")
        self.assertEqual(str(self.customer_obj), "Test Customer 1")

    def test_workorder_creation(self):
        self.assertEqual(self.workorder_obj_1.wo_no, "WO001")
        self.assertEqual(self.workorder_obj_1.customer.customer_name, "Test Customer 1")

    def test_get_formatted_sys_date_method(self):
        # Test valid date format
        self.assertEqual(self.workorder_obj_1.get_formatted_sys_date(), "20/05/2023")
        # Test invalid date format (fallback)
        self.workorder_obj_1.sys_date = "Invalid Date"
        self.assertEqual(self.workorder_obj_1.get_formatted_sys_date(), "Invalid Date")

    def test_workorder_manager_get_work_orders_for_customer(self):
        # Test fetching open work orders for a specific customer
        work_orders = WorkOrder.objects.get_work_orders_for_customer(
            customer_id=self.customer_obj.customer_id,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id
        )
        self.assertEqual(work_orders.count(), 2) # Should get WO001 and WO002
        self.assertIn(self.workorder_obj_1, work_orders)
        self.assertIn(self.workorder_obj_2, work_orders)
        self.assertNotIn(self.workorder_obj_closed, work_orders) # Excludes closed
        self.assertNotIn(self.workorder_obj_other_cust, work_orders) # Excludes other customer

        # Test with no matching customer
        work_orders_none = WorkOrder.objects.get_work_orders_for_customer(
            customer_id="NONEXIST",
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id
        )
        self.assertEqual(work_orders_none.count(), 0)

class InventoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 2023 # Example financial year ID

        # Create test data for related models first
        cls.financial_year_obj = FinancialYear.objects.create(fin_year_id=cls.fin_year_id, fin_year="2023-2024")
        cls.employee_obj = Employee.objects.create(emp_id=101, employee_name="John Doe", title="Mr.", comp_id=cls.comp_id)
        cls.customer_obj = Customer.objects.create(customer_id="CUST001", customer_name="Test Customer 1", comp_id=cls.comp_id)
        cls.customer_obj_2 = Customer.objects.create(customer_id="CUST002", customer_name="Another Customer", comp_id=cls.comp_id)

        cls.workorder_obj_1 = WorkOrder.objects.create(
            id=1, enq_id="ENQ001", task_project_title="Project Alpha", customer=cls.customer_obj,
            wo_no="WO001", po_no="PO001", po_id=1, generated_by=cls.employee_obj,
            financial_year=cls.financial_year_obj, sys_date="20-05-2023", close_open=0, comp_id=cls.comp_id
        )
        cls.workorder_obj_2 = WorkOrder.objects.create(
            id=2, enq_id="ENQ002", task_project_title="Project Beta", customer=cls.customer_obj,
            wo_no="WO002", po_no="PO002", po_id=2, generated_by=cls.employee_obj,
            financial_year=cls.financial_year_obj, sys_date="15-04-2023", close_open=0, comp_id=cls.comp_id
        )

    def setUp(self):
        self.client = Client()
        # Simulate session data for CompId and FinYearId
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_customer_challan_view(self):
        response = self.client.get(reverse('inventory:customer_challan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customerchallan_list.html')
        self.assertContains(response, "Customer Challan - New")
        self.assertIsInstance(response.context['search_form'], CustomerSearchForm)

    def test_customer_search_table_htmx(self):
        # Test initial load without search value
        response = self.client.get(reverse('inventory:customer_search_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/_customer_search_table.html')
        self.assertContains(response, "Test Customer 1")
        self.assertContains(response, "Another Customer")
        
        # Test with search value
        response = self.client.get(reverse('inventory:customer_search_table'), {'search_value': 'Test Customer'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Test Customer 1")
        self.assertNotContains(response, "Another Customer")

        # Test with "Name [ID]" format
        response = self.client.get(reverse('inventory:customer_search_table'), {'search_value': 'Another Customer [CUST002]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Another Customer")
        self.assertNotContains(response, "Test Customer 1")


    def test_workorder_table_htmx(self):
        # Test loading work orders for a specific customer
        response = self.client.get(reverse('inventory:workorder_table', args=[self.customer_obj.customer_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/_workorder_table.html')
        self.assertContains(response, "Project Alpha")
        self.assertContains(response, "WO001")
        self.assertNotContains(response, "Project Delta") # Ensure only selected customer's WOs

        # Test loading work orders for a non-existent customer
        response = self.client.get(reverse('inventory:workorder_table', args=['NONEXIST']), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "No data to display !")

    def test_customer_autocomplete_htmx(self):
        response = self.client.get(reverse('inventory:customer_autocomplete'), {'search_value': 'test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'value="Test Customer 1 [CUST001]"')
        self.assertNotContains(response, 'value="Another Customer [CUST002]"')
        self.assertContains(response, '<datalist id="customerSuggestions">')

    # --- Generic CRUD View Tests ---
    def test_customer_create_view_get(self):
        response = self.client.get(reverse('inventory:customer_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/customer/form.html')
        self.assertTrue('form' in response.context)

    def test_customer_create_view_post(self):
        data = {
            'customer_id': 'NEWCUST',
            'customer_name': 'New Customer',
            'comp_id': self.comp_id,
        }
        response = self.client.post(reverse('inventory:customer_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content swap
        self.assertTrue(Customer.objects.filter(customer_id='NEWCUST').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_update_view_post(self):
        customer = Customer.objects.get(customer_id="CUST001")
        updated_name = "Updated Customer Name"
        data = {
            'customer_id': customer.customer_id,
            'customer_name': updated_name,
            'comp_id': customer.comp_id,
        }
        response = self.client.post(reverse('inventory:customer_edit', args=[customer.customer_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        customer.refresh_from_db()
        self.assertEqual(customer.customer_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    def test_customer_delete_view_post(self):
        customer_to_delete = Customer.objects.create(customer_id="TEMP_DEL", customer_name="Temporary Customer", comp_id=self.comp_id)
        response = self.client.post(reverse('inventory:customer_delete', args=[customer_to_delete.customer_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Customer.objects.filter(customer_id="TEMP_DEL").exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCustomerList', response.headers['HX-Trigger'])

    # Add similar tests for WorkOrder CRUD views
    def test_workorder_create_view_get(self):
        response = self.client.get(reverse('inventory:workorder_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/workorder/form.html')
        self.assertTrue('form' in response.context)

    # Note: For WorkOrder POST tests, ensure all required fields are provided
    # and Foreign Keys are correctly set up, similar to WorkOrder.objects.create in setUpTestData.
```

## Step 5: HTMX and Alpine.js Integration

The provided Django templates and views demonstrate robust HTMX and Alpine.js integration:

*   **HTMX for Dynamic Updates:**
    *   The `CustomerChallanView` initially loads an empty page, with `hx-get` attributes on `div` elements to fetch `_customer_search_table.html` and `_workorder_table.html` partials on `load once`.
    *   The "Search" button on the main page triggers an `hx-get` to `customer_search_table` endpoint, updating `#customerSearchResults` with new data.
    *   Clicking a customer name in the search results (`_customer_search_table.html`) triggers an `hx-get` to `workorder_table` endpoint, passing the `customer_id`, and updates `#workOrderResults`.
    *   The customer search input field (`TxtSearchValue`) uses `hx-get` with `hx-trigger="keyup changed delay:500ms, search"` to continuously update the `datalist` for autocomplete suggestions.
    *   CRUD operations (Add, Edit, Delete) for Customer and WorkOrder forms are submitted via `hx-post` with `hx-swap="none"`, triggering custom `HX-Trigger` events (`refreshCustomerList`, `refreshWorkOrderList`, `closeModal`) for client-side orchestration.
    *   `htmx-indicator` classes are used to provide loading feedback during HTMX requests.

*   **Alpine.js for UI State Management:**
    *   A global Alpine.js component `modalController` is used to manage the visibility of the modal (`#modal`).
    *   HTMX `hx-trigger` with `json.dumps({'closeModal': None})` will dispatch a custom `closeModal` event, which the Alpine.js controller listens for to hide the modal and clear its content.
    *   `_=` (hyperscript) attributes on buttons like `on click add .is-active to #modal` are used to open the modal directly from HTMX button clicks.

*   **DataTables for List Views:**
    *   Both `customerTable` and `workOrderTable` are initialized using jQuery DataTables.
    *   The `htmx:afterSwap` event listener ensures that DataTables is re-initialized whenever new content is swapped into the table containers, preventing issues with dynamically loaded content.
    *   Pagination, searching, and sorting are handled client-side by DataTables.

*   **DRY Templates:**
    *   Separate partials (`_customer_search_table.html`, `_workorder_table.html`, `_customer_form.html`, `_workorder_form.html`, `_confirm_delete.html`) are used for reusable UI components.
    *   The main `customerchallan_list.html` orchestrates these partials using HTMX.

This comprehensive plan provides a clear, actionable roadmap for modernizing your ASP.NET `CustomerChallan_New` module to a performant, maintainable, and user-friendly Django application.