This document outlines a comprehensive plan for modernizing the provided ASP.NET Material Requisition Slip (MRS) editing functionality into a modern Django-based solution. Our approach emphasizes AI-assisted automation, adheres to Django 5.0+ best practices, utilizes HTMX and Alpine.js for dynamic frontend interactions, and follows the "fat model, thin view" architecture.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code primarily interacts with `tblInv_MaterialRequisition_Details` for the grid data. It joins with `tblInv_MaterialRequisition_Master` (via `MId` and `MRSNo`), and retrieves related data from `tblDG_Item_Master`, `Unit_Master`, `BusinessGroup`, and `SD_Cust_WorkOrder_Master`. It also checks `tblInv_MaterialIssue_Master` and `tblInv_MaterialIssue_Details` for editability.

**Identified Tables and Key Columns:**

*   **`tblInv_MaterialRequisition_Details` (Main Table for this page)**
    *   `Id` (PK)
    *   `MId` (FK to `tblInv_MaterialRequisition_Master.Id`)
    *   `MRSNo` (Denormalized, also from `tblInv_MaterialRequisition_Master`)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `DeptId` (Can be `BusinessGroup.Id` or a special value `1` indicating `WONo`)
    *   `WONo`
    *   `ReqQty`
    *   `Remarks`
*   **`tblInv_MaterialRequisition_Master`**
    *   `Id` (PK)
    *   `MRSNo`
    *   `CompId`
    *   `FyId`
    *   `SysDate`
    *   `SysTime`
    *   `SessionId`
*   **`tblDG_Item_Master`**
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc` (Description)
    *   `UOMBasic` (FK to `Unit_Master.Id`)
    *   `StockQty`
    *   `CompId`
*   **`Unit_Master`**
    *   `Id` (PK)
    *   `Symbol` (UOM)
*   **`BusinessGroup`**
    *   `Id` (PK)
    *   `Symbol` (Dept Name)
*   **`SD_Cust_WorkOrder_Master`**
    *   `Id` (PK)
    *   `WONo`
    *   `CompId`
    *   `FyId`
*   **`tblInv_MaterialIssue_Master`**
    *   `Id` (PK)
    *   `MRSId` (FK to `tblInv_MaterialRequisition_Master.Id`)
    *   `MRSNo` (Denormalized)
*   **`tblInv_MaterialIssue_Details`**
    *   `Id` (PK)
    *   `MId` (FK to `tblInv_MaterialIssue_Master.Id`)
    *   `MRSId` (FK to `tblInv_MaterialRequisition_Details.Id`)

### Step 2: Identify Backend Functionality

**Read Operations:**
*   The `LoadData()` method retrieves a list of `MaterialRequisitionDetail` items based on a `MaterialRequisitionMaster.Id` (passed as `MId` in the query string).
*   It dynamically constructs a `DataTable` by fetching related `Item` details (`ItemCode`, `Description`, `UOM`, `StockQty`) and `BusinessGroup` symbols, and then calculates `DW` (Department/Work Order value) and `WODept` (label).
*   The `GridView1_PageIndexChanging` event triggers a data reload for pagination.

**Update Operations:**
*   The `GridView1_RowUpdating` event handles updating `ReqQty`, `Remarks`, `DeptId`, and `WONo` for a specific detail line.
*   **Validation:**
    *   `ReqQty` must be less than or equal to `StockQty`.
    *   Quantity must be a valid number (`fun.NumberValidationQty`).
    *   If `DeptId` implies a `BusinessGroup` (`deptwo == 1`), `drpdept` must be selected.
    *   If `DeptId` implies a `WONo` (`deptwo == 2`), `WONo` must be valid (`fun.CheckValidWONo`) and exist in `SD_Cust_WorkOrder_Master`.
*   Updates the `SysDate`, `SysTime`, `SessionId` on the parent `MaterialRequisitionMaster` record.

**Control Flow / Dynamic Behavior:**
*   `disableEdit()`: Prevents editing of a `MaterialRequisitionDetail` if it has been associated with a `MaterialIssueDetail`. This logic needs to be transferred to the model or a view helper.
*   `DropDownList1_SelectedIndexChanged` and `getval()`: Client-side logic to show/hide `txtwono` or `drpdept` based on the selection ("BG Group" or "WONo"). This will be handled by Alpine.js in the Django template.
*   `BtnCancel_Click`: Redirects to another page.

**No Explicit Create or Delete functionality for detail lines on this page.** The page is strictly for *editing* existing details.

### Step 3: Infer UI Components

*   **`GridView1`**: Will be replaced by a Django template displaying data rendered as an HTML table, enhanced with `DataTables.js` for client-side functionality.
*   **`LinkButton` (Edit, Update, Cancel)**: Will be HTMX-enabled buttons that trigger modal forms for edit and handle form submission.
*   **`Label`**: Displayed values from model properties.
*   **`TextBox` (`txtqty`, `txtremarks`, `txtwono`)**: Rendered as `forms.TextInput` fields in Django forms.
*   **`DropDownList` (`DropDownList1`, `drpdept`)**: Rendered as `forms.Select` fields. The dynamic show/hide behavior will be managed by Alpine.js in the template.
*   **`RequiredFieldValidator`**: Handled by Django form validation (`required=True` or custom `clean()` methods).
*   **`SqlDataSource1`**: Data for `BusinessGroup` dropdown. This will be handled by a Django `ModelChoiceField` or by passing the queryset directly to the template for a standard `<select>` element.
*   **Modal Dialogs**: HTMX will be used to load form partials into a modal `div`, controlled by Alpine.js.

### Step 4: Generate Django Code

We will create a new Django app named `inventory`.

#### 4.1 Models (`inventory/models.py`)

We'll define models for the primary tables as `managed=False` to map to the existing database schema.

```python
from django.db import models
from datetime import datetime

# Managed=False models for existing database tables

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic')
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class MaterialRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=255)
    sys_date = models.CharField(db_column='SysDate', max_length=255) # Consider converting to DateField/DateTimeField
    sys_time = models.CharField(db_column='SysTime', max_length=255) # Consider converting to DateField/DateTimeField
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fy_id = models.IntegerField(db_column='FyId')

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Master'
        verbose_name_plural = 'Material Requisition Masters'

    def __str__(self):
        return self.mrs_no

class CustomerWorkOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fy_id = models.IntegerField(db_column='FyId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Customer Work Order Master'
        verbose_name_plural = 'Customer Work Order Masters'

    def __str__(self):
        return self.wo_no

class MaterialIssueMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrs_id = models.ForeignKey(MaterialRequisitionMaster, models.DO_NOTHING, db_column='MRSId')
    mrs_no = models.CharField(db_column='MRSNo', max_length=255) # Denormalized MRSNo

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Master'
        verbose_name_plural = 'Material Issue Masters'

class MaterialIssueDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(MaterialIssueMaster, models.DO_NOTHING, db_column='MId')
    mrs_id = models.ForeignKey('MaterialRequisitionDetail', models.DO_NOTHING, db_column='MRSId') # Link to MaterialRequisitionDetail's Id

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Details'
        verbose_name = 'Material Issue Detail'
        verbose_name_plural = 'Material Issue Details'


class MaterialRequisitionDetail(models.Model):
    # This 'Id' is the primary key for the detail line
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Foreign key to MaterialRequisitionMaster.Id (MId in ASP.NET)
    master_requisition = models.ForeignKey(MaterialRequisitionMaster, models.DO_NOTHING, db_column='MId')
    mrs_no = models.CharField(db_column='MRSNo', max_length=255) # Denormalized MRSNo, kept as per DB schema
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    dept_id = models.IntegerField(db_column='DeptId') # Can be BusinessGroup.Id or special value 1 for WONo
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

    def __str__(self):
        return f"MRS Detail {self.id} (MRS: {self.master_requisition.mrs_no})"

    # Business logic methods (Fat Model)
    @property
    def item_code(self):
        return self.item.item_code if self.item else "N/A"

    @property
    def description(self):
        return self.item.manf_desc if self.item else "N/A"

    @property
    def uom(self):
        return self.item.uom_basic.symbol if self.item and self.item.uom_basic else "N/A"

    @property
    def stock_qty(self):
        return self.item.stock_qty if self.item else 0.000

    @property
    def business_group_symbol(self):
        try:
            # Assuming DeptId 1 is special for WONo, otherwise it's a BusinessGroup Id
            if self.dept_id and self.dept_id != 1:
                return BusinessGroup.objects.get(id=self.dept_id).symbol
            return "" # Empty string as per original logic if not a BG Group
        except BusinessGroup.DoesNotExist:
            return ""

    @property
    def wo_dept_display(self):
        """Returns 'WONo' or 'BG Group' based on DeptId."""
        if self.dept_id == 1:
            return "WONo"
        elif self.dept_id and self.dept_id != 0: # Assuming 0 is initial 'Select'
            return "BG Group"
        return "NA" # Fallback if DeptId is 0 or unhandled

    @property
    def dw_value(self):
        """Returns the actual WO or Business Group value for display."""
        if self.wo_dept_display == "WONo":
            return self.wo_no if self.wo_no else ""
        elif self.wo_dept_display == "BG Group":
            return self.business_group_symbol
        return ""

    def is_editable(self):
        """
        Checks if the MRS detail line has been issued, disabling editing.
        Equivalent to the disableEdit() function in ASP.NET.
        """
        return not MaterialIssueDetail.objects.filter(mrs_id=self).exists()

```

#### 4.2 Forms (`inventory/forms.py`)

We'll create a `ModelForm` for `MaterialRequisitionDetail` and include a custom field for the `Dept/WO` choice.

```python
from django import forms
from .models import MaterialRequisitionDetail, BusinessGroup, CustomerWorkOrderMaster

class MaterialRequisitionDetailForm(forms.ModelForm):
    # Custom field to select between BG Group and WONo (mimics DropDownList1)
    DEPT_WO_CHOICES = [
        ('0', 'Select'), # Mimics "Select" option
        ('1', 'BG Group'),
        ('2', 'WONo'),
    ]
    
    dept_wo_choice = forms.ChoiceField(
        choices=DEPT_WO_CHOICES,
        label="BG Group/WoNo",
        initial='0', # Default value
        widget=forms.Select(attrs={'class': 'box3', 'x-model': 'deptWoChoice', 'hx-trigger': 'change'})
    )

    # ModelChoiceField for BusinessGroup (drpdept)
    dept_id = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        required=False, # Required based on dept_wo_choice
        empty_label="Select Business Group",
        label="Business Group",
        widget=forms.Select(attrs={'class': 'box3'}),
    )

    # CharField for WONo (txtwono)
    wo_no = forms.CharField(
        max_length=255,
        required=False, # Required based on dept_wo_choice
        label="Work Order No",
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter WO No'}),
    )

    class Meta:
        model = MaterialRequisitionDetail
        fields = ['req_qty', 'remarks'] # Base fields from model
        widgets = {
            'req_qty': forms.NumberInput(attrs={'class': 'box3', 'step': '0.001'}), # N3 format
            'remarks': forms.TextInput(attrs={'class': 'box3'}),
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values for custom fields if instance exists
        if self.instance.pk:
            if self.instance.dept_id == 1:
                self.fields['dept_wo_choice'].initial = '2' # WONo
                self.fields['wo_no'].initial = self.instance.wo_no
            elif self.instance.dept_id and self.instance.dept_id != 0:
                self.fields['dept_wo_choice'].initial = '1' # BG Group
                self.fields['dept_id'].initial = self.instance.dept_id
            else:
                self.fields['dept_wo_choice'].initial = '0' # Select

    def clean(self):
        cleaned_data = super().clean()
        req_qty = cleaned_data.get('req_qty')
        dept_wo_choice = cleaned_data.get('dept_wo_choice')
        new_dept_id = cleaned_data.get('dept_id')
        new_wo_no = cleaned_data.get('wo_no')

        # ReqQty vs StockQty validation
        if req_qty is not None and self.instance and self.instance.stock_qty is not None:
            if req_qty > self.instance.stock_qty:
                self.add_error('req_qty', "Requested Quantity should be less than or equal to Stock Quantity.")

        # Dept/WO selection validation
        if dept_wo_choice == '1': # BG Group selected
            if not new_dept_id:
                self.add_error('dept_id', "Business Group must be selected.")
            cleaned_data['wo_no'] = '' # Clear WONo if BG Group is selected
            cleaned_data['dept_id'] = new_dept_id.id if new_dept_id else 0 # Store ID
        elif dept_wo_choice == '2': # WONo selected
            if not new_wo_no:
                self.add_error('wo_no', "Work Order Number cannot be empty.")
            # CheckValidWONo logic (requires comp_id, fy_id from URL/Session)
            # In a real scenario, this context would be passed to the form/view.
            # For now, we'll assume a dummy comp_id/fy_id or fetch from master_requisition.
            master_req = self.instance.master_requisition if self.instance else None
            comp_id = master_req.comp_id if master_req else None # Or pass as kwarg
            fy_id = master_req.fy_id if master_req else None # Or pass as kwarg
            
            if new_wo_no and comp_id is not None and fy_id is not None:
                if not CustomerWorkOrderMaster.objects.filter(wo_no=new_wo_no, comp_id=comp_id, fy_id=fy_id).exists():
                    self.add_error('wo_no', "Work Order Number not found!")
            
            cleaned_data['dept_id'] = 1 # Special value for WONo
        else: # "Select" chosen or invalid
            if self.instance.pk: # Only require if instance exists and is being edited
                 self.add_error('dept_wo_choice', "Please select a BG Group or WONo.")
            cleaned_data['dept_id'] = 0
            cleaned_data['wo_no'] = ''
            
        return cleaned_data

```

#### 4.3 Views (`inventory/views.py`)

Views will be thin, delegating complex logic to models and forms. The master requisition ID (`master_pk`) will be passed in the URL.

```python
from django.views.generic import ListView, UpdateView, TemplateView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from django.db import transaction # For atomic updates

from .models import MaterialRequisitionDetail, MaterialRequisitionMaster, BusinessGroup
from .forms import MaterialRequisitionDetailForm

class MaterialRequisitionDetailListView(ListView):
    model = MaterialRequisitionDetail
    template_name = 'inventory/materialrequisitiondetail/list.html'
    context_object_name = 'material_requisition_details'
    paginate_by = 20 # Matches PageSize in ASP.NET GridView

    def get_queryset(self):
        # Filter by master_requisition ID (MId from ASP.NET QueryString)
        master_pk = self.kwargs.get('master_pk')
        if not master_pk:
            return MaterialRequisitionDetail.objects.none() # Or raise Http404
        
        # Prefetch related data for efficiency, similar to ASP.NET's LoadData logic
        queryset = MaterialRequisitionDetail.objects.filter(
            master_requisition__id=master_pk
        ).select_related('item', 'item__uom_basic', 'master_requisition')
        
        # The ASP.NET LoadData does complex joins and filtering within the loop.
        # For 'Fat Model', properties on the model handle the derived fields.
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        master_pk = self.kwargs.get('master_pk')
        if master_pk:
            context['master_requisition'] = get_object_or_404(MaterialRequisitionMaster, pk=master_pk)
        return context

class MaterialRequisitionDetailTablePartialView(MaterialRequisitionDetailListView):
    """
    Renders only the table portion, used by HTMX to refresh the list.
    """
    template_name = 'inventory/materialrequisitiondetail/_table.html'

class MaterialRequisitionDetailUpdateView(UpdateView):
    model = MaterialRequisitionDetail
    form_class = MaterialRequisitionDetailForm
    template_name = 'inventory/materialrequisitiondetail/_form.html'
    context_object_name = 'material_requisition_detail'

    def get_success_url(self):
        # Redirect back to the list view for the specific master requisition
        master_pk = self.kwargs.get('master_pk')
        return reverse('inventory:materialrequisitiondetail_list', kwargs={'master_pk': master_pk})

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass BusinessGroup data for the dropdown in the form
        context['business_groups'] = BusinessGroup.objects.all()
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        
        # Update logic based on form data after validation
        instance = self.object
        master_req = instance.master_requisition
        
        try:
            with transaction.atomic():
                # Apply updates from form clean method
                instance.dept_id = form.cleaned_data['dept_id']
                instance.wo_no = form.cleaned_data['wo_no']
                instance.req_qty = form.cleaned_data['req_qty']
                instance.remarks = form.cleaned_data['remarks']
                instance.save()

                # Update MaterialRequisitionMaster (SysDate, SysTime, SessionId)
                master_req.sys_date = datetime.now().strftime("%Y-%m-%d")
                master_req.sys_time = datetime.now().strftime("%H:%M:%S")
                # Assuming request.user.username is available from Django's auth system
                master_req.session_id = self.request.user.username if self.request.user.is_authenticated else 'Anonymous'
                master_req.save()

            messages.success(self.request, 'Material Requisition Detail updated successfully.')

            if self.request.headers.get('HX-Request'):
                # For HTMX, return a 204 No Content response with a trigger header
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshMaterialRequisitionDetailList'
                    }
                )
            return response
        except Exception as e:
            messages.error(self.request, f"Error updating detail: {e}")
            # If not HX-Request, re-render form with errors
            if self.request.headers.get('HX-Request'):
                # For HTMX, re-render the form with errors for a partial update
                return self.form_invalid(form) # Rerenders form with validation errors
            return self.form_invalid(form) # Fallback for non-HTMX (shouldn't happen with our setup)

    def form_invalid(self, form):
        # Handle form invalidation for HTMX requests by rendering the form again
        # This allows HTMX to swap the content and show errors.
        if self.request.headers.get('HX-Request'):
            messages.error(self.request, "Please correct the errors below.")
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class MaterialRequisitionCancelView(TemplateView):
    """
    Mimics the BtnCancel_Click functionality by redirecting to a specific URL.
    This could be a simple function view or a RedirectView if no template rendering is needed.
    """
    def get(self, request, *args, **kwargs):
        # Redirect to the main MRS edit page, based on the ASP.NET redirect.
        # This URL is hardcoded as per ASP.NET, in Django it's better to reverse_lazy.
        # Assuming 'MaterialRequisitionSlip_MRS_Edit.aspx' maps to a Django URL like 'inventory:mrs_edit_master'
        messages.info(request, "Operation cancelled.")
        return HttpResponseRedirect(reverse_lazy('inventory:mrs_edit_master')) # Placeholder URL

```

#### 4.4 Templates

**`inventory/materialrequisitiondetail/list.html`**
This is the main page for listing and triggering edits.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Requisition Slip [MRS] - Edit Details for {{ master_requisition.mrs_no }}</h2>
        <a href="{% url 'inventory:mrs_edit_master' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
    
    <div id="materialrequisitiondetailTable-container"
         hx-trigger="load, refreshMaterialRequisitionDetailList from:body"
         hx-get="{% url 'inventory:materialrequisitiondetail_table' master_pk=master_requisition.id %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Requisition Details...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here for the list view,
        // but 'x-show' on the modal div is controlled by HTMX swap into #modalContent
    });
</script>
{% endblock %}
```

**`inventory/materialrequisitiondetail/_table.html`**
This partial template is loaded by HTMX and contains the DataTables structure.

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="materialrequisitiondetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRS No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group/WoNo</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Req Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in material_requisition_details %}
            <tr hx-target="this" hx-swap="outerHTML" {% if not obj.is_editable %}class="bg-gray-100 text-gray-500"{% endif %}>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.mrs_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.uom }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.wo_dept_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.dw_value }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.stock_qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.req_qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.remarks }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if obj.is_editable %}
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'inventory:materialrequisitiondetail_edit' master_pk=obj.master_requisition.id pk=obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    {% else %}
                    <span class="text-xs text-red-600 font-semibold py-1 px-2">MIN</span> {# "MIN" label from ASP.NET #}
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="11" class="py-4 px-4 text-center text-lg font-medium text-maroon">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#materialrequisitiondetailTable')) {
            $('#materialrequisitiondetailTable').DataTable().destroy();
        }
        $('#materialrequisitiondetailTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 10] }, // SN and Actions columns not sortable
                { "searchable": false, "targets": [0, 10] } // SN and Actions columns not searchable
            ]
        });
    });
</script>
```

**`inventory/materialrequisitiondetail/_form.html`**
This partial template contains the edit form, loaded dynamically into the modal. Alpine.js manages the conditional visibility of `BusinessGroup` and `WONo` fields.

```html
<div class="p-6" x-data="{ deptWoChoice: '{{ form.dept_wo_choice.value }}' }">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Requisition Detail</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.successful) htmx.find('#modal').classList.remove('is-active')">
        {% csrf_token %}
        
        <div class="space-y-4">
            {# Hidden Item details for context #}
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">Item Code</label>
                    <p class="text-gray-900">{{ material_requisition_detail.item_code }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Description</label>
                    <p class="text-gray-900">{{ material_requisition_detail.description }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">UOM</label>
                    <p class="text-gray-900">{{ material_requisition_detail.uom }}</p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">Stock Quantity</label>
                    <p class="text-gray-900">{{ material_requisition_detail.stock_qty|floatformat:3 }}</p>
                </div>
            </div>
            <hr class="my-4">

            {# Req Qty Field #}
            <div class="mb-4">
                <label for="{{ form.req_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.req_qty.label }}
                    {% if form.req_qty.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.req_qty }}
                {% if form.req_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.req_qty.errors }}</p>
                {% endif %}
            </div>

            {# BG Group/WoNo selection (DropDownList1) #}
            <div class="mb-4">
                <label for="{{ form.dept_wo_choice.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.dept_wo_choice.label }}
                    {% if form.dept_wo_choice.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ form.dept_wo_choice }}
                {% if form.dept_wo_choice.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.dept_wo_choice.errors }}</p>
                {% endif %}
            </div>

            {# Conditional Fields based on dept_wo_choice #}
            <div x-show="deptWoChoice === '1'" class="mb-4">
                <label for="{{ form.dept_id.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.dept_id.label }}
                    <span x-show="deptWoChoice === '1'" class="text-red-500">*</span> {# Required when BG Group is selected #}
                </label>
                {{ form.dept_id }}
                {% if form.dept_id.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.dept_id.errors }}</p>
                {% endif %}
            </div>

            <div x-show="deptWoChoice === '2'" class="mb-4">
                <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.wo_no.label }}
                    <span x-show="deptWoChoice === '2'" class="text-red-500">*</span> {# Required when WONo is selected #}
                </label>
                {{ form.wo_no }}
                {% if form.wo_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>
                {% endif %}
            </div>
            
            {# Remarks Field #}
            <div class="mb-4">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.remarks.label }}
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>
                {% endif %}
            </div>

        </div>
        
        {# Display non-field errors #}
        {% if form.non_field_errors %}
        <div class="text-red-500 text-sm mt-4">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-confirm="Are you sure you want to update this item?"> {# Equivalent to confirmationUpdate() #}
                Update
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`inventory/urls.py`)

This file defines the URL patterns for the `inventory` app.

```python
from django.urls import path
from .views import (
    MaterialRequisitionDetailListView, 
    MaterialRequisitionDetailUpdateView,
    MaterialRequisitionDetailTablePartialView,
    MaterialRequisitionCancelView, # Added for cancel button redirect
)

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    # List view for MRS details, filtered by master_pk
    path('mrs/edit/details/<int:master_pk>/', MaterialRequisitionDetailListView.as_view(), name='materialrequisitiondetail_list'),
    
    # HTMX partial endpoint for refreshing the table
    path('mrs/edit/details/<int:master_pk>/table/', MaterialRequisitionDetailTablePartialView.as_view(), name='materialrequisitiondetail_table'),
    
    # Update view for a specific MRS detail item
    path('mrs/edit/details/<int:master_pk>/edit/<int:pk>/', MaterialRequisitionDetailUpdateView.as_view(), name='materialrequisitiondetail_edit'),
    
    # URL for the cancel button, redirects to the main MRS edit page
    path('mrs/edit/cancel/', MaterialRequisitionCancelView.as_view(), name='mrs_edit_master'), # Placeholder for redirection
]
```
**Note:** You would need to include these URLs in your project's main `urls.py`:
`path('inventory/', include('inventory.urls')),`

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests cover model logic and view interactions, including HTMX.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from decimal import Decimal
from datetime import datetime

from .models import (
    BusinessGroup, UnitMaster, ItemMaster, 
    MaterialRequisitionMaster, CustomerWorkOrderMaster, 
    MaterialIssueMaster, MaterialIssueDetail, MaterialRequisitionDetail
)

class MaterialRequisitionDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for related models (managed=False)
        cls.user = User.objects.create_user(username='testuser', password='password')

        cls.business_group_bg = BusinessGroup.objects.create(id=10, symbol='BG_TEST')
        cls.business_group_wono = BusinessGroup.objects.create(id=1, symbol='WONo_Marker') # Special ID for WONo
        
        cls.unit_master = UnitMaster.objects.create(id=1, symbol='PCS')
        
        cls.item_master_1 = ItemMaster.objects.create(
            id=100, item_code='ITEM001', manf_desc='Test Item Description 1', 
            uom_basic=cls.unit_master, stock_qty=Decimal('100.500'), comp_id=1
        )
        cls.item_master_2 = ItemMaster.objects.create(
            id=101, item_code='ITEM002', manf_desc='Test Item Description 2', 
            uom_basic=cls.unit_master, stock_qty=Decimal('50.000'), comp_id=1
        )
        
        cls.mrs_master = MaterialRequisitionMaster.objects.create(
            id=1, mrs_no='MRS-001', comp_id=1, fy_id=2024,
            sys_date=datetime.now().strftime("%Y-%m-%d"),
            sys_time=datetime.now().strftime("%H:%M:%S"),
            session_id='initial'
        )
        
        cls.customer_wo = CustomerWorkOrderMaster.objects.create(
            id=1, wo_no='WO-ALPHA', comp_id=1, fy_id=2024
        )

        # Create MaterialRequisitionDetail instances
        cls.mrs_detail_bg = MaterialRequisitionDetail.objects.create(
            id=1, master_requisition=cls.mrs_master, mrs_no='MRS-001',
            item=cls.item_master_1, dept_id=cls.business_group_bg.id,
            wo_no='', req_qty=Decimal('10.000'), remarks='Initial BG remarks'
        )
        cls.mrs_detail_wo = MaterialRequisitionDetail.objects.create(
            id=2, master_requisition=cls.mrs_master, mrs_no='MRS-001',
            item=cls.item_master_2, dept_id=cls.business_group_wono.id, # Using the special WONo marker ID
            wo_no='WO-ALPHA', req_qty=Decimal('5.000'), remarks='Initial WO remarks'
        )
        
        # Detail that will be marked as issued
        cls.mrs_detail_issued = MaterialRequisitionDetail.objects.create(
            id=3, master_requisition=cls.mrs_master, mrs_no='MRS-001',
            item=cls.item_master_1, dept_id=cls.business_group_bg.id,
            wo_no='', req_qty=Decimal('20.000'), remarks='Issued item'
        )
        cls.material_issue_master = MaterialIssueMaster.objects.create(id=1, mrs_id=cls.mrs_master, mrs_no='MRS-001')
        MaterialIssueDetail.objects.create(id=1, m_id=cls.material_issue_master, mrs_id=cls.mrs_detail_issued)

    def test_material_requisition_detail_creation(self):
        obj = MaterialRequisitionDetail.objects.get(id=1)
        self.assertEqual(obj.master_requisition.mrs_no, 'MRS-001')
        self.assertEqual(obj.item.item_code, 'ITEM001')
        self.assertEqual(obj.req_qty, Decimal('10.000'))

    def test_item_code_property(self):
        obj = MaterialRequisitionDetail.objects.get(id=1)
        self.assertEqual(obj.item_code, 'ITEM001')

    def test_description_property(self):
        obj = MaterialRequisitionDetail.objects.get(id=1)
        self.assertEqual(obj.description, 'Test Item Description 1')

    def test_uom_property(self):
        obj = MaterialRequisitionDetail.objects.get(id=1)
        self.assertEqual(obj.uom, 'PCS')

    def test_stock_qty_property(self):
        obj = MaterialRequisitionDetail.objects.get(id=1)
        self.assertEqual(obj.stock_qty, Decimal('100.500'))
    
    def test_business_group_symbol_property(self):
        obj = MaterialRequisitionDetail.objects.get(id=1) # BG Group item
        self.assertEqual(obj.business_group_symbol, 'BG_TEST')
        obj_wo = MaterialRequisitionDetail.objects.get(id=2) # WO item
        self.assertEqual(obj_wo.business_group_symbol, '') # Should be empty for WO

    def test_wo_dept_display_property(self):
        obj_bg = MaterialRequisitionDetail.objects.get(id=1)
        self.assertEqual(obj_bg.wo_dept_display, 'BG Group')
        obj_wo = MaterialRequisitionDetail.objects.get(id=2)
        self.assertEqual(obj_wo.wo_dept_display, 'WONo')

    def test_dw_value_property(self):
        obj_bg = MaterialRequisitionDetail.objects.get(id=1)
        self.assertEqual(obj_bg.dw_value, 'BG_TEST')
        obj_wo = MaterialRequisitionDetail.objects.get(id=2)
        self.assertEqual(obj_wo.dw_value, 'WO-ALPHA')

    def test_is_editable_method(self):
        obj_editable = MaterialRequisitionDetail.objects.get(id=1)
        self.assertTrue(obj_editable.is_editable())
        obj_issued = MaterialRequisitionDetail.objects.get(id=3)
        self.assertFalse(obj_issued.is_editable())

class MaterialRequisitionDetailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='testuser', password='password')

        cls.business_group_bg = BusinessGroup.objects.create(id=10, symbol='BG_TEST')
        cls.business_group_wono = BusinessGroup.objects.create(id=1, symbol='WONo_Marker')
        
        cls.unit_master = UnitMaster.objects.create(id=1, symbol='PCS')
        
        cls.item_master_1 = ItemMaster.objects.create(
            id=100, item_code='ITEM001', manf_desc='Test Item Description 1', 
            uom_basic=cls.unit_master, stock_qty=Decimal('100.500'), comp_id=1
        )
        cls.item_master_2 = ItemMaster.objects.create(
            id=101, item_code='ITEM002', manf_desc='Test Item Description 2', 
            uom_basic=cls.unit_master, stock_qty=Decimal('50.000'), comp_id=1
        )
        
        cls.mrs_master = MaterialRequisitionMaster.objects.create(
            id=1, mrs_no='MRS-001', comp_id=1, fy_id=2024,
            sys_date=datetime.now().strftime("%Y-%m-%d"),
            sys_time=datetime.now().strftime("%H:%M:%S"),
            session_id='initial'
        )
        cls.customer_wo = CustomerWorkOrderMaster.objects.create(
            id=1, wo_no='WO-ALPHA', comp_id=1, fy_id=2024
        )

        cls.mrs_detail_bg = MaterialRequisitionDetail.objects.create(
            id=1, master_requisition=cls.mrs_master, mrs_no='MRS-001',
            item=cls.item_master_1, dept_id=cls.business_group_bg.id,
            wo_no='', req_qty=Decimal('10.000'), remarks='Initial BG remarks'
        )
        cls.mrs_detail_wo = MaterialRequisitionDetail.objects.create(
            id=2, master_requisition=cls.mrs_master, mrs_no='MRS-001',
            item=cls.item_master_2, dept_id=cls.business_group_wono.id,
            wo_no='WO-ALPHA', req_qty=Decimal('5.000'), remarks='Initial WO remarks'
        )

    def setUp(self):
        self.client = Client()
        self.client.force_login(self.user)

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:materialrequisitiondetail_list', kwargs={'master_pk': self.mrs_master.id}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiondetail/list.html')
        self.assertIn('material_requisition_details', response.context)
        self.assertContains(response, 'MRS-001')
        self.assertContains(response, 'ITEM001')

    def test_table_partial_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:materialrequisitiondetail_table', kwargs={'master_pk': self.mrs_master.id}), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiondetail/_table.html')
        self.assertContains(response, 'materialrequisitiondetailTable') # Check for table ID
        self.assertContains(response, 'Edit') # Check for edit button

    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:materialrequisitiondetail_edit', kwargs={
            'master_pk': self.mrs_master.id, 'pk': self.mrs_detail_bg.id
        }), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiondetail/_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Material Requisition Detail')
        self.assertContains(response, 'value="10.000"') # Check initial req_qty

    def test_update_view_post_bg_group_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_remarks = 'Updated remarks BG'
        new_req_qty = Decimal('5.000') # Within stock
        
        data = {
            'req_qty': str(new_req_qty),
            'remarks': new_remarks,
            'dept_wo_choice': '1', # BG Group
            'dept_id': str(self.business_group_bg.id),
            'wo_no': '', # Should be ignored/cleared
        }
        
        response = self.client.post(reverse('inventory:materialrequisitiondetail_edit', kwargs={
            'master_pk': self.mrs_master.id, 'pk': self.mrs_detail_bg.id
        }), data, **headers)

        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialRequisitionDetailList')

        # Verify update in database
        updated_detail = MaterialRequisitionDetail.objects.get(id=self.mrs_detail_bg.id)
        self.assertEqual(updated_detail.req_qty, new_req_qty)
        self.assertEqual(updated_detail.remarks, new_remarks)
        self.assertEqual(updated_detail.dept_id, self.business_group_bg.id)
        self.assertEqual(updated_detail.wo_no, '') # WO_No should be cleared

        # Verify master record was updated
        updated_master = MaterialRequisitionMaster.objects.get(id=self.mrs_master.id)
        self.assertEqual(updated_master.session_id, 'testuser')


    def test_update_view_post_wo_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_remarks = 'Updated remarks WO'
        new_req_qty = Decimal('2.000') # Within stock
        
        data = {
            'req_qty': str(new_req_qty),
            'remarks': new_remarks,
            'dept_wo_choice': '2', # WONo
            'dept_id': '', # Should be ignored/set to 1
            'wo_no': 'WO-ALPHA',
        }
        
        response = self.client.post(reverse('inventory:materialrequisitiondetail_edit', kwargs={
            'master_pk': self.mrs_master.id, 'pk': self.mrs_detail_wo.id
        }), data, **headers)

        self.assertEqual(response.status_code, 204) # HTMX success code

        updated_detail = MaterialRequisitionDetail.objects.get(id=self.mrs_detail_wo.id)
        self.assertEqual(updated_detail.req_qty, new_req_qty)
        self.assertEqual(updated_detail.remarks, new_remarks)
        self.assertEqual(updated_detail.dept_id, 1) # Should be set to 1 for WONo
        self.assertEqual(updated_detail.wo_no, 'WO-ALPHA')

    def test_update_view_post_req_qty_exceeds_stock_failure(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'req_qty': str(self.mrs_detail_bg.item.stock_qty + Decimal('1.000')), # Exceeds stock
            'remarks': 'Should fail',
            'dept_wo_choice': '1',
            'dept_id': str(self.business_group_bg.id),
            'wo_no': '',
        }
        
        response = self.client.post(reverse('inventory:materialrequisitiondetail_edit', kwargs={
            'master_pk': self.mrs_master.id, 'pk': self.mrs_detail_bg.id
        }), data, **headers)

        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'inventory/materialrequisitiondetail/_form.html')
        self.assertContains(response, "Requested Quantity should be less than or equal to Stock Quantity.")
        
        # Verify no update in database
        original_detail = MaterialRequisitionDetail.objects.get(id=self.mrs_detail_bg.id)
        self.assertEqual(original_detail.req_qty, Decimal('10.000')) # Should be unchanged

    def test_update_view_post_invalid_wo_failure(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'req_qty': '1.000',
            'remarks': 'Invalid WO',
            'dept_wo_choice': '2',
            'dept_id': '',
            'wo_no': 'NON_EXISTENT_WO', # Invalid WO
        }
        
        response = self.client.post(reverse('inventory:materialrequisitiondetail_edit', kwargs={
            'master_pk': self.mrs_master.id, 'pk': self.mrs_detail_wo.id
        }), data, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiondetail/_form.html')
        self.assertContains(response, "Work Order Number not found!")

    def test_cancel_view(self):
        response = self.client.get(reverse('inventory:mrs_edit_master'))
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('inventory:mrs_edit_master')) # Redirects to itself for now.
        # In a real app, this would redirect to the MRS master edit page.
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for CRUD:**
    *   **List Loading:** The `list.html` uses `hx-get` on `materialrequisitiondetailTable-container` with `hx-trigger="load, refreshMaterialRequisitionDetailList from:body"` to initially load and then refresh the table.
    *   **Edit Form Loading:** The "Edit" buttons in `_table.html` use `hx-get` to fetch `_form.html` into `#modalContent` and `on click add .is-active to #modal` to show the modal.
    *   **Form Submission:** The form in `_form.html` uses `hx-post="{{ request.path }}" hx-swap="none"` to submit data. On success, the `hx-on::after-request` JavaScript closes the modal.
    *   **Table Refresh:** The `MaterialRequisitionDetailUpdateView` returns `HX-Trigger: refreshMaterialRequisitionDetailList` on successful update, which tells HTMX to re-run the `hx-get` on the `materialrequisitiondetailTable-container`, thus refreshing the table.
    *   **Confirmation:** `hx-confirm="Are you sure you want to update this item?"` is used on the submit button.

*   **Alpine.js for UI State:**
    *   `x-data="{ deptWoChoice: '{{ form.dept_wo_choice.value }}' }"` on the form wrapper in `_form.html` initializes an Alpine.js variable.
    *   `x-model="deptWoChoice"` is used on the `dept_wo_choice` dropdown to keep the `deptWoChoice` variable updated.
    *   `x-show="deptWoChoice === '1'"` and `x-show="deptWoChoice === '2'"` are used on the `dept_id` and `wo_no` form fields, respectively, to conditionally show/hide them based on the dropdown selection.
    *   The modal's visibility is managed by `on click add .is-active to #modal` for showing and `on click if event.target.id == 'modal' remove .is-active from me` for closing.

*   **DataTables for List Views:**
    *   The `_table.html` partial includes a `<table>` with `id="materialrequisitiondetailTable"`.
    *   A `<script>` block at the bottom of `_table.html` contains the `$(document).ready(function() { $('#materialrequisitiondetailTable').DataTable({...}); });` code, which initializes DataTables every time the partial is loaded by HTMX. This ensures proper re-initialization.

*   **DRY Template Inheritance & CDN:**
    *   All templates `{% extends 'core/base.html' %}`. It's assumed `core/base.html` includes all necessary CDN links for DataTables CSS/JS, HTMX, Alpine.js, and jQuery (for DataTables).

This comprehensive plan provides a clear, actionable roadmap for transitioning the ASP.NET Material Requisition Slip editing module to a robust, modern Django application using automation-friendly techniques.