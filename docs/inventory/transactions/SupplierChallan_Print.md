## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with the database primarily through a stored procedure named `GetSupChallan` and direct SQL queries for supplier lookup (`tblMM_Supplier_master`).

*   **Main Data Table (for Challans):** Inferred to be `tblInv_Supplier_Challan_Master` based on common naming conventions in ASP.NET ERPs and the context of "Supplier Challan". The `GetSupChallan` stored procedure likely queries this table.
    *   **Columns:**
        *   `Id` (Primary Key, integer) - corresponds to `DataKeyNames="SupplierId"` but also `lblId` in row command, let's assume `Id` is the primary key for challans.
        *   `SCNo` (Challan Number, string)
        *   `FinYear` (Financial Year, string)
        *   `SupplierId` (Foreign Key to Supplier, integer)
        *   `SupplierName` (Likely denormalized or joined by the SP, string) - For Django model, we'll rely on the `supplier` foreign key.

*   **Supplier Master Table:** `tblMM_Supplier_master` (Explicitly used in `sql` web method).
    *   **Columns:**
        *   `SupplierId` (Primary Key, integer)
        *   `SupplierName` (string)
        *   `CompId` (Company ID, integer)

### Step 2: Identify Backend Functionality

**Analysis:**
The page `SupplierChallan_Print.aspx` is primarily a **read/list** functionality with **filtering** and an **action** to redirect to a print details page.

*   **Read/List:** The `SearchGridView1` displays a list of supplier challans. The `BindData` method is responsible for fetching this data using `GetSupChallan` stored procedure.
*   **Filtering/Search:**
    *   A `TextBox` (`TxtSearchValue`) allows searching by `SupplierName`.
    *   An `AutoCompleteExtender` provides type-ahead suggestions for supplier names from `tblMM_Supplier_master`.
    *   A `Button` (`Search`) triggers the filter.
*   **Pagination:** The `GridView` supports pagination (`AllowPaging="True"`, `onpageindexchanging`).
*   **Action/Redirection:**
    *   A `LinkButton` (`Btn1` with `CommandName="sel"`) in each row allows selecting a challan.
    *   A `DropDownList` (`DrpPrintType`) in each row allows selecting a print type (ORIGINAL, DUPLICATE, TRIPLICATE, ACKNOWLEDGEMENT).
    *   The `SearchGridView1_RowCommand` event handler redirects to `SupplierChallan_Print_Details.aspx` with various query parameters, including the selected Challan `Id` and `PrintType`.

### Step 3: Infer UI Components

**Analysis:**
The page layout consists of a search bar area and a data grid.

*   **Search Bar:**
    *   `TxtSearchValue` (ASP:TextBox): Will be a Django `forms.CharField` with HTMX for dynamic search suggestions.
    *   `Search` (ASP:Button): Will trigger an HTMX request to refresh the data table.
*   **Data Grid:**
    *   `SearchGridView1` (ASP:GridView): Will be replaced by an HTML `<table>` element rendered by Django, managed by DataTables.js for client-side functionality.
    *   **Columns:**
        *   `SN`: Simple row counter.
        *   `Select` (`LinkButton`): Replaced by a button/link that triggers a Django view redirection.
        *   `Print Type` (`DropDownList`): Replaced by a `<select>` element within each table row.
        *   `Fin Yrs` (`BoundField`): Displays `FinYear`.
        *   `SCNo` (`TemplateField` with `Label`): Displays `SCNo`.
        *   `Supplier Name` (`BoundField`): Displays `SupplierName` (from related `Supplier` model).
        *   `Code` (`BoundField`): Displays `SupplierId` (from related `Supplier` model).
        *   `Id` (`TemplateField` with `Label`, `Visible="false"`): Hidden ID for challan.

---

### Step 4: Generate Django Code

We'll define an application named `inventory` for these components.

#### 4.0 Utilities (`inventory/utils.py`)

This utility helps parse the supplier name from the autocomplete output, similar to `fun.getCode()`.

```python
# inventory/utils.py
import re

def extract_supplier_id_from_name(name_with_id_string):
    """
    Extracts the integer ID from a string formatted as "Supplier Name [ID]".
    Returns None if no ID is found or if parsing fails.
    """
    if not name_with_id_string:
        return None
    match = re.search(r'\[(\d+)\]$', name_with_id_string.strip())
    if match:
        try:
            return int(match.group(1))
        except ValueError:
            pass
    return None

# Placeholder for session/context variables
# In a real application, these would be retrieved from request.session or a multi-tenancy context.
def get_current_company_id():
    """Placeholder for retrieving the current company ID from session/context."""
    # This value should be dynamically set based on the logged-in user's context.
    # For demonstration/testing, we use a fixed value.
    return 1 # Example: Replace with actual session logic: request.session.get('compid')

def get_current_financial_year():
    """Placeholder for retrieving the current financial year from session/context."""
    # This value should be dynamically set based on the current financial period.
    # For demonstration/testing, we use a fixed value.
    return '2023-2024' # Example: Replace with actual session logic: request.session.get('finyear')

```

#### 4.1 Models (`inventory/models.py`)

```python
from django.db import models
from inventory.utils import get_current_company_id, get_current_financial_year

class Supplier(models.Model):
    # SupplierId in DB maps to 'id' in Django ORM
    id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    # Assuming CompId is an integer column on tblMM_Supplier_master
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_Supplier_master' # Explicitly map to the existing table name
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.id}]"

    @classmethod
    def search_suppliers(cls, prefix_text, company_id):
        """
        Simulates the 'sql' web method for autocomplete by filtering
        suppliers based on company_id and a prefix match on supplier_name.
        Returns a queryset of dictionaries with 'id' and 'supplier_name'.
        """
        return cls.objects.filter(
            company_id=company_id,
            supplier_name__icontains=prefix_text # Use icontains for broader match like ASP.NET example
        ).order_by('supplier_name').values('id', 'supplier_name')


class SupplierChallan(models.Model):
    # Id in DB maps to 'id' in Django ORM
    id = models.IntegerField(db_column='Id', primary_key=True)
    challan_no = models.CharField(db_column='SCNo', max_length=50)
    # Assuming FinYear is an integer/varchar column on tblInv_Supplier_Challan_Master
    financial_year = models.CharField(db_column='FinYear', max_length=10)
    # SupplierId in DB maps to 'supplier_id' FK to Supplier.id
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='challans')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblInv_Supplier_Challan_Master' # Inferred from ASP.NET code usage
        verbose_name = 'Supplier Challan'
        verbose_name_plural = 'Supplier Challans'

    def __str__(self):
        return f"Challan {self.challan_no} ({self.financial_year}) by {self.supplier.supplier_name}"

    @property
    def supplier_name_display(self):
        """
        Property to access the related supplier's name for display in templates.
        This mirrors the 'SupplierName' BoundField in the ASP.NET GridView.
        """
        return self.supplier.supplier_name if self.supplier else 'N/A'

    @classmethod
    def get_challans_for_display(cls, supplier_id=None):
        """
        Simulates the 'GetSupChallan' stored procedure logic.
        This is a fat model method that encapsulates the data retrieval.
        Filters by company_id and financial_year from global context, and optional supplier_id.
        """
        # Retrieve company_id and financial_year from the application's context/session
        # (This should be a proper context variable in a real app, not a direct call)
        current_company_id = get_current_company_id()
        current_financial_year = get_current_financial_year()

        queryset = cls.objects.select_related('supplier') # Optimize by prefetching supplier data

        # Apply filters based on the parameters that would be passed to the SP
        if supplier_id:
            queryset = queryset.filter(supplier__id=supplier_id)
        
        # Apply global filters based on company and financial year from context
        if current_company_id:
            queryset = queryset.filter(supplier__company_id=current_company_id) # Filter via related supplier's company_id
        if current_financial_year:
            queryset = queryset.filter(financial_year=current_financial_year)

        return queryset.order_by('challan_no')

```

#### 4.2 Forms (`inventory/forms.py`)

This page doesn't have a traditional create/update form. It has a search input.

```python
from django import forms
from .models import Supplier

class SupplierChallanSearchForm(forms.Form):
    """
    Form for searching Supplier Challans by supplier name.
    """
    supplier_search_value = forms.CharField(
        max_length=255,
        required=False,
        label="Supplier Name",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3',
            'placeholder': 'Start typing a supplier name...',
            'id': 'txtSearchValue', # Mimic ASP.NET ID
            'hx-get': '/inventory/supplier-autocomplete/', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms', # Trigger on keyup after delay
            'hx-target': '#supplier-suggestions', # Target for suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
            'x-model': 'supplierSearchInput', # Alpine.js model
            '@input': 'filterSuggestions', # Alpine.js event
            '@focus': 'showSuggestions = true',
            '@blur.away': 'hideSuggestionsDelayed()', # Delay hide to allow click
        })
    )

    # Note: No ModelForm here as this is purely for searching, not CRUD on SupplierChallan.
    # The actual search logic (extracting ID) will be in the view or a form clean method.
```

#### 4.3 Views (`inventory/views.py`)

```python
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.template.loader import render_to_string
from django.utils.html import escape
from django.contrib import messages # For future use if messages are needed

from inventory.models import SupplierChallan, Supplier
from inventory.forms import SupplierChallanSearchForm
from inventory.utils import extract_supplier_id_from_name, get_current_company_id

class SupplierChallanListView(TemplateView):
    """
    Main view for displaying the Supplier Challan list page.
    Handles the initial load and renders the search form.
    """
    template_name = 'inventory/supplierchallan/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['search_form'] = SupplierChallanSearchForm(self.request.GET)
        return context

class SupplierChallanTablePartialView(ListView):
    """
    HTMX-driven partial view to render the DataTables content.
    Filters the challans based on search query and renders the table rows.
    """
    model = SupplierChallan
    template_name = 'inventory/supplierchallan/_supplierchallan_table.html'
    context_object_name = 'supplier_challans'
    paginate_by = 17 # Matches ASP.NET PageSize

    def get_queryset(self):
        form = SupplierChallanSearchForm(self.request.GET)
        supplier_id = None
        if form.is_valid():
            supplier_search_value = form.cleaned_data.get('supplier_search_value')
            if supplier_search_value:
                # Extract supplier ID from the autocomplete string format
                supplier_id = extract_supplier_id_from_name(supplier_search_value)

        # Use the fat model method to get the filtered queryset
        return SupplierChallan.get_challans_for_display(supplier_id=supplier_id)

    # This method is crucial for HTMX to handle pagination via DataTables
    # DataTables will make its own GET requests with page parameters.
    # The ListView's default pagination handling is sufficient.


class SupplierAutocompleteView(View):
    """
    HTMX endpoint for supplier name autocomplete suggestions.
    Mimics the ASP.NET AutoCompleteExtender's 'sql' web method.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        if not prefix_text:
            return HttpResponse("") # Return empty if no prefix text

        company_id = get_current_company_id() # Get current company ID from context/session

        suggestions = Supplier.search_suppliers(prefix_text, company_id)

        # Format suggestions as an unordered list for HTMX to inject
        # Each list item will be clickable to populate the input field
        html_suggestions = '<ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">'
        if suggestions:
            for s in suggestions:
                # Use data attributes for Alpine.js to pick up ID and Name
                html_suggestions += f'<li class="p-2 cursor-pointer hover:bg-gray-200" data-id="{s["id"]}" data-name="{s["supplier_name"]}" ' \
                                    f'x-on:click="selectSuggestion(\'{escape(s["supplier_name"])} [{s["id"]}]\')">' \
                                    f'{escape(s["supplier_name"])} <span class="text-gray-500">[{s["id"]}]</span></li>'
        else:
            html_suggestions += '<li class="p-2 text-gray-500">No suggestions found.</li>'
        html_suggestions += '</ul>'

        return HttpResponse(html_suggestions)

class SupplierChallanPrintDetailView(View):
    """
    Handles the redirection to the challan print details page.
    This simulates the Response.Redirect logic from the ASP.NET code-behind.
    """
    def get(self, request, pk, *args, **kwargs):
        print_type = request.GET.get('print_type', '0') # Default to '0' (ORIGINAL)
        # Note: ModId and SubModId from ASP.NET are likely module/sub-module IDs
        # in a larger ERP system, which would be handled differently in Django,
        # e.g., by permissions or context variables. We will omit them for now.

        # Construct the URL for the print details.
        # In a real Django app, 'supplierchallan_print_details' would be a named URL.
        # For simplicity, we'll just redirect to a dummy path that includes the parameters.
        # You would replace this with the actual URL to your print rendering view.
        redirect_url = reverse_lazy('inventory:supplierchallan_print_actual_details')
        return redirect(f"{redirect_url}?id={pk}&print_type={print_type}")

```

#### 4.4 Templates (`inventory/supplierchallan/`)

##### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Supplier Challan - Print</h2>
        <!-- No "Add New" button here as per ASP.NET page's functionality -->
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <div class="flex items-center space-x-4" x-data="{
            showSuggestions: false,
            supplierSearchInput: '{{ search_form.supplier_search_value.value|default:"" }}',
            selectedSupplierId: '',
            selectSuggestion(value) {
                this.supplierSearchInput = value;
                this.showSuggestions = false;
                // Trigger form submission or direct table refresh
                document.getElementById('searchForm').requestSubmit();
            },
            hideSuggestionsDelayed() {
                // Delay hiding to allow click event to register
                setTimeout(() => { this.showSuggestions = false; }, 100);
            }
        }">
            <form id="searchForm" hx-get="{% url 'inventory:supplierchallan_table' %}" hx-target="#supplierChallanTable-container" hx-swap="innerHTML" class="flex-grow flex items-center space-x-4">
                {% csrf_token %}
                <label for="txtSearchValue" class="block text-sm font-medium text-gray-700 whitespace-nowrap">
                    Supplier Name:
                </label>
                <div class="relative flex-grow">
                    {{ search_form.supplier_search_value }}
                    <div id="supplier-suggestions" x-show="showSuggestions" class="absolute top-full left-0 w-full" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-95">
                        <!-- Autocomplete suggestions will be loaded here by HTMX -->
                    </div>
                </div>
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                    Search
                </button>
            </form>
        </div>
    </div>
    
    <div id="supplierChallanTable-container"
         hx-trigger="load, refreshSupplierChallanList from:body"
         hx-get="{% url 'inventory:supplierchallan_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Challans...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DataTables initialization (will be applied when _supplierchallan_table is loaded)
    document.addEventListener('DOMContentLoaded', function() {
        // This is a global function if needed, or put inside an Alpine component
    });

    // Ensure DataTables re-initializes on HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'supplierChallanTable-container') {
            const table = document.getElementById('supplierChallanTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 17, // Matches ASP.NET PageSize
                    "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 1, 2] }, // Disable sorting for SN, Select, Print Type
                        { "searchable": false, "targets": [0, 1, 2] } // Disable searching for SN, Select, Print Type
                    ]
                });
            }
        }
    });

    // Optional: Alpine.js for general page state if more complex UI is needed.
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component for the whole page required beyond the search field.
    });
</script>
{% endblock %}
```

##### `_supplierchallan_table.html` (Partial for HTMX)

```html
<div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
    {% if supplier_challans %}
    <table id="supplierChallanTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-3/100">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-6/100">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-20/100">Print Type</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-12/100">Fin Yrs</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10/100">SCNo</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider w-10/100">Code</th>
                <!-- 'Id' column is hidden -->
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in supplier_challans %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button
                        class="text-blue-600 hover:text-blue-900 font-medium"
                        onclick="window.location.href='{% url 'inventory:supplierchallan_print_details' pk=obj.pk %}?print_type=' + document.getElementById('drpPrintType_{{ obj.pk }}').value">
                        Select
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <select id="drpPrintType_{{ obj.pk }}" class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3">
                        <option value="0">ORIGINAL</option>
                        <option value="1">DUPLICATE</option>
                        <option value="2">TRIPLICATE</option>
                        <option value="3">ACKNOWLEDGEMENT</option>
                    </select>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.financial_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.challan_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier_name_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.supplier.id }}</td>
                <!-- Hidden Id can be accessed via obj.pk if needed, not displayed directly -->
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-10 font-medium text-lg text-maroon-600">
        <p>No data to display !</p>
    </div>
    {% endif %}
</div>

<!-- DataTables initialization script. This runs when the HTMX-swapped content is in the DOM. -->
<script>
    // Only initialize DataTables if it hasn't been initialized on this table yet
    // This is important because HTMX might swap content multiple times.
    $(document).ready(function() {
        const table = $('#supplierChallanTable');
        if (table.length && !$.fn.DataTable.isDataTable(table)) {
            table.DataTable({
                "pageLength": 17, // Matches ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, 1, 2] }, // Disable sorting for SN, Select, Print Type
                    { "searchable": false, "targets": [0, 1, 2] } // Disable searching for SN, Select, Print Type
                ],
                "language": { // Optional: Customize DataTables messages
                    "emptyTable": "No data to display !",
                    "zeroRecords": "No matching records found"
                }
            });
        }
    });
</script>
```

#### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    SupplierChallanListView,
    SupplierChallanTablePartialView,
    SupplierAutocompleteView,
    SupplierChallanPrintDetailView,
)

app_name = 'inventory' # Define app_name for namespacing URLs

urlpatterns = [
    # Main list view for Supplier Challans
    path('supplierchallan/', SupplierChallanListView.as_view(), name='supplierchallan_list'),

    # HTMX endpoint to load/refresh the DataTables partial
    path('supplierchallan/table/', SupplierChallanTablePartialView.as_view(), name='supplierchallan_table'),

    # HTMX endpoint for supplier autocomplete suggestions
    path('supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # Endpoint to handle redirection to the print details page
    # This URL will receive 'pk' (challan ID) from the selected row
    path('supplierchallan/print/<int:pk>/', SupplierChallanPrintDetailView.as_view(), name='supplierchallan_print_details'),

    # Placeholder for the actual challan print detail view.
    # In a real system, this would point to a view that renders the print layout.
    path('supplierchallan/print-actual-details/', lambda request: HttpResponse("<h1>Supplier Challan Print Details Page</h1><p>ID: {{ request.GET.id }}, Print Type: {{ request.GET.print_type }}</p>"), name='supplierchallan_print_actual_details'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch

from inventory.models import Supplier, SupplierChallan
from inventory.utils import extract_supplier_id_from_name, get_current_company_id, get_current_financial_year

# Mock the utility functions that get session data
# This ensures tests are isolated from actual session management
@patch('inventory.utils.get_current_company_id', return_value=1)
@patch('inventory.utils.get_current_financial_year', return_value='2023-2024')
class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_get_finyear, mock_get_compid):
        # Create test data for all tests
        cls.supplier1 = Supplier.objects.create(id=101, supplier_name='ABC Corp', company_id=1)
        cls.supplier2 = Supplier.objects.create(id=102, supplier_name='XYZ Ltd', company_id=1)
        cls.supplier_other_company = Supplier.objects.create(id=103, supplier_name='Other Co', company_id=2)

    def test_supplier_creation(self, mock_get_finyear, mock_get_compid):
        self.assertEqual(self.supplier1.supplier_name, 'ABC Corp')
        self.assertEqual(self.supplier1.company_id, 1)

    def test_supplier_str_representation(self, mock_get_finyear, mock_get_compid):
        self.assertEqual(str(self.supplier1), 'ABC Corp [101]')

    def test_search_suppliers_by_prefix(self, mock_get_finyear, mock_get_compid):
        suggestions = Supplier.search_suppliers(prefix_text='abc', company_id=1)
        self.assertEqual(len(suggestions), 1)
        self.assertEqual(suggestions[0]['supplier_name'], 'ABC Corp')
        self.assertEqual(suggestions[0]['id'], 101)

    def test_search_suppliers_no_match(self, mock_get_finyear, mock_get_compid):
        suggestions = Supplier.search_suppliers(prefix_text='nonexistent', company_id=1)
        self.assertEqual(len(suggestions), 0)

    def test_search_suppliers_company_filter(self, mock_get_finyear, mock_get_compid):
        suggestions = Supplier.search_suppliers(prefix_text='other', company_id=1)
        self.assertEqual(len(suggestions), 0) # Should not find 'Other Co' for company_id 1


@patch('inventory.utils.get_current_company_id', return_value=1)
@patch('inventory.utils.get_current_financial_year', return_value='2023-2024')
class SupplierChallanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_get_finyear, mock_get_compid):
        cls.supplier1 = Supplier.objects.create(id=101, supplier_name='ABC Corp', company_id=1)
        cls.supplier2 = Supplier.objects.create(id=102, supplier_name='XYZ Ltd', company_id=1)
        cls.supplier_other_company = Supplier.objects.create(id=103, supplier_name='Other Co', company_id=2)

        cls.challan1 = SupplierChallan.objects.create(
            id=1, challan_no='SC001', financial_year='2023-2024', supplier=cls.supplier1
        )
        cls.challan2 = SupplierChallan.objects.create(
            id=2, challan_no='SC002', financial_year='2023-2024', supplier=cls.supplier2
        )
        cls.challan3 = SupplierChallan.objects.create(
            id=3, challan_no='SC003', financial_year='2022-2023', supplier=cls.supplier1
        )
        cls.challan_other_company = SupplierChallan.objects.create(
            id=4, challan_no='SC004', financial_year='2023-2024', supplier=cls.supplier_other_company
        )

    def test_challan_creation(self, mock_get_finyear, mock_get_compid):
        self.assertEqual(self.challan1.challan_no, 'SC001')
        self.assertEqual(self.challan1.financial_year, '2023-2024')
        self.assertEqual(self.challan1.supplier.supplier_name, 'ABC Corp')

    def test_challan_str_representation(self, mock_get_finyear, mock_get_compid):
        self.assertEqual(str(self.challan1), 'Challan SC001 (2023-2024) by ABC Corp')

    def test_supplier_name_display_property(self, mock_get_finyear, mock_get_compid):
        self.assertEqual(self.challan1.supplier_name_display, 'ABC Corp')
        self.challan1.supplier = None # Test case for no supplier (though FK is usually non-null)
        self.assertEqual(self.challan1.supplier_name_display, 'N/A')

    def test_get_challans_for_display_no_filter(self, mock_get_finyear, mock_get_compid):
        challans = SupplierChallan.get_challans_for_display()
        self.assertEqual(challans.count(), 2) # Should include SC001, SC002 (company 1, fin year 2023-2024)
        self.assertIn(self.challan1, challans)
        self.assertIn(self.challan2, challans)
        self.assertNotIn(self.challan3, challans) # Wrong financial year
        self.assertNotIn(self.challan_other_company, challans) # Wrong company

    def test_get_challans_for_display_with_supplier_filter(self, mock_get_finyear, mock_get_compid):
        challans = SupplierChallan.get_challans_for_display(supplier_id=self.supplier1.id)
        self.assertEqual(challans.count(), 1) # Should only include SC001
        self.assertIn(self.challan1, challans)
        self.assertNotIn(self.challan2, challans)


@patch('inventory.utils.get_current_company_id', return_value=1)
@patch('inventory.utils.get_current_financial_year', return_value='2023-2024')
class SupplierChallanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls, mock_get_finyear, mock_get_compid):
        cls.supplier1 = Supplier.objects.create(id=101, supplier_name='ABC Corp', company_id=1)
        cls.supplier2 = Supplier.objects.create(id=102, supplier_name='XYZ Ltd', company_id=1)
        cls.challan1 = SupplierChallan.objects.create(
            id=1, challan_no='SC001', financial_year='2023-2024', supplier=cls.supplier1
        )
        cls.challan2 = SupplierChallan.objects.create(
            id=2, challan_no='SC002', financial_year='2023-2024', supplier=cls.supplier2
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self, mock_get_finyear, mock_get_compid):
        response = self.client.get(reverse('inventory:supplierchallan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/list.html')
        self.assertIsInstance(response.context['search_form'], SupplierChallanSearchForm)

    def test_table_partial_view_get(self, mock_get_finyear, mock_get_compid):
        response = self.client.get(reverse('inventory:supplierchallan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplierchallan_table.html')
        self.assertTrue('supplier_challans' in response.context)
        self.assertEqual(response.context['supplier_challans'].count(), 2) # Both challans

    def test_table_partial_view_get_with_search(self, mock_get_finyear, mock_get_compid):
        # Simulate a search by supplier name that resolves to supplier ID
        search_query = f"ABC Corp [{self.supplier1.id}]"
        response = self.client.get(reverse('inventory:supplierchallan_table'), {'supplier_search_value': search_query})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplierchallan_table.html')
        self.assertEqual(response.context['supplier_challans'].count(), 1)
        self.assertEqual(response.context['supplier_challans'][0], self.challan1)

    def test_supplier_autocomplete_view(self, mock_get_finyear, mock_get_compid):
        response = self.client.get(reverse('inventory:supplier_autocomplete'), {'prefixText': 'abc'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ABC Corp [101]')
        self.assertNotContains(response, 'XYZ Ltd') # Should only contain ABC Corp

    def test_supplier_autocomplete_view_no_prefix(self, mock_get_finyear, mock_get_compid):
        response = self.client.get(reverse('inventory:supplier_autocomplete'), {'prefixText': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), "")

    def test_supplier_challan_print_detail_view(self, mock_get_finyear, mock_get_compid):
        response = self.client.get(reverse('inventory:supplierchallan_print_details', args=[self.challan1.pk]), {'print_type': '1'})
        self.assertEqual(response.status_code, 302) # Expects a redirect
        self.assertRedirects(response, reverse('inventory:supplierchallan_print_actual_details') + '?id=1&print_type=1')

    def test_htmx_triggers_and_swaps(self, mock_get_finyear, mock_get_compid):
        # Test that the table partial is loaded via HTMX on initial page load
        # This is implicitly tested by checking the presence of the table in the main list view.
        # For explicit HTMX request:
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:supplierchallan_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/supplierchallan/_supplierchallan_table.html')

        # Test HTMX search form submission
        search_query = f"ABC Corp [{self.supplier1.id}]"
        response = self.client.get(reverse('inventory:supplierchallan_table'), {'supplier_search_value': search_query}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'SC001', response.content)
        self.assertNotIn(b'SC002', response.content)

```