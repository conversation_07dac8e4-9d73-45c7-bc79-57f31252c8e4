## ASP.NET to Django Conversion Script: Material Return Note [MRN] - Edit

This document outlines a strategic plan to modernize your existing ASP.NET application, specifically the "Material Return Note [MRN] - Edit" module, by migrating it to a robust and scalable Django-based solution. This approach prioritizes automated conversion, clean architecture, and modern web technologies to deliver a superior user experience and simplified maintenance.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

This conversion process will systematically transform your ASP.NET code into modern Django components, ensuring business logic is properly encapsulated and user interfaces are dynamic and responsive.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the ASP.NET code, we observe the `GridView1` binds to data from a stored procedure `Sp_MRN_FillGrid`, which filters data based on `MRNNo` and an employee `SessionId`. The `AutoCompleteExtender` for `TxtEmpName` queries `tblHR_OfficeStaff`.

**Inferred Tables and Columns:**

*   **Primary Table:** `tblInv_MaterialReturn_Master` (for Material Return Notes)
    *   `Id` (Primary Key, integer)
    *   `FinYearId` (integer)
    *   `FinYear` (string)
    *   `SysDate` (datetime/date)
    *   `MRNNo` (string)
    *   `SessionId` (string/integer, links to `tblHR_OfficeStaff.EmpId`)
    *   `CompId` (integer, Company ID, used for filtering)

*   **Lookup Table:** `tblHR_OfficeStaff` (for Employees)
    *   `EmpId` (Primary Key, string/integer)
    *   `EmployeeName` (string)
    *   `CompId` (integer, Company ID)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET code.

**Instructions:**

The current ASP.NET page, "Material Return Note [MRN] - Edit," primarily functions as a search and list interface. It allows users to:

*   **Read (List):** Display a paginated list of Material Return Notes. This is handled by `GridView1` and the `fillgrid` method executing `Sp_MRN_FillGrid`.
*   **Search/Filter:** Filter the list by either "MRN No" or "Employee Name." This involves the `DrpField` (dropdown), `TxtMrn` (MRN text box), `TxtEmpName` (Employee Name text box), and `Button1` (Search button), with logic in `DrpField_SelectedIndexChanged` and `Button1_Click`.
*   **Autocomplete:** Provide suggestions for "Employee Name" using `TxtEmpName_AutoCompleteExtender` and the `GetCompletionList` web method.
*   **Navigation to Detail:** When a "Select" link is clicked in the `GridView1`, it redirects to `MaterialReturnNote_MRN_Edit_Details.aspx`, indicating a transition to a separate page for editing or viewing the detailed record. This page itself does not perform direct create, update, or delete operations on Material Return Notes.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their interactive roles.

**Instructions:**

The page's user interface consists of a search/filter section and a data display area:

*   **Search/Filter Controls:**
    *   `DrpField` (DropdownList): Allows selecting the search criterion (Employee Name or MRN No). Its `AutoPostBack` behavior suggests dynamic UI updates on selection.
    *   `TxtMrn` (TextBox): Hidden initially, visible when "MRN No" is selected for search.
    *   `TxtEmpName` (TextBox): Visible initially, hidden when "MRN No" is selected. Features an `AutoCompleteExtender` for employee name suggestions.
    *   `Button1` (Button): Triggers the search operation.
*   **Data Display Control:**
    *   `GridView1` (GridView): Displays the list of Material Return Notes. It supports pagination and has columns for "SN," "MRN No," "Date," "FinYear," "Gen. By," and a "Select" action link. The `yui-datatable-theme` CSS class indicates it's styled as a data table.

### Step 4: Generate Django Code

This step translates the identified components and functionalities into structured Django code, following the specified architectural patterns.

#### 4.1 Models

**Task:** Create Django models that map to the identified database tables, incorporating business logic.

**Instructions:**
We will define two models: `Employee` for the staff lookup and `MaterialReturnNote` for the core data. Business logic for data filtering (replacing the stored procedure) will be encapsulated as a class method within the `MaterialReturnNote` model.

```python
# inventory/models.py
from django.db import models
from django.urls import reverse_lazy
import re

class Employee(models.Model):
    """
    Django model for the tblHR_OfficeStaff database table.
    Used for employee lookup and autocomplete functionality.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50, verbose_name="Employee ID")
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, verbose_name="Employee Name")
    company_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.employee_name} [{self.emp_id}]"

    @classmethod
    def get_employee_by_name_or_id(cls, search_term):
        """
        Business logic to resolve an employee from a search term,
        which might be 'Name [ID]' or just 'Name'.
        This replaces the fun.getCode functionality from ASP.NET.
        """
        search_term = search_term.strip()
        
        # Attempt to extract EmpId from 'Name [ID]' format
        match = re.search(r'\[(.*?)\]$', search_term)
        if match:
            emp_id = match.group(1)
            try:
                return cls.objects.get(emp_id=emp_id)
            except cls.DoesNotExist:
                pass # Continue to search by name if ID doesn't yield a result

        # Fallback to searching by employee name (case-insensitive)
        return cls.objects.filter(employee_name__iexact=search_term).first()


class MaterialReturnNote(models.Model):
    """
    Django model for the tblInv_MaterialReturn_Master database table.
    Represents a Material Return Note.
    """
    id = models.BigIntegerField(db_column='Id', primary_key=True, verbose_name="ID")
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True, verbose_name="Financial Year ID")
    fin_year = models.CharField(db_column='FinYear', max_length=50, null=True, blank=True, verbose_name="Financial Year")
    sys_date = models.DateTimeField(db_column='SysDate', null=True, blank=True, verbose_name="System Date")
    mrn_no = models.CharField(db_column='MRNNo', max_length=100, null=True, blank=True, verbose_name="MRN No")
    # session_emp is a ForeignKey linking to the Employee who generated the MRN.
    # It assumes a 'SessionId' column in 'tblInv_MaterialReturn_Master' that stores 'EmpId'.
    session_emp = models.ForeignKey(Employee, on_delete=models.SET_NULL, db_column='SessionId', 
                                    to_field='emp_id', null=True, blank=True, verbose_name="Generated By Employee")
    company_id = models.IntegerField(db_column='CompId', null=True, blank=True, verbose_name="Company ID")

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Note'
        verbose_name_plural = 'Material Return Notes'
        # Order by date descending, then MRN number ascending by default
        ordering = ['-sys_date', 'mrn_no']

    def __str__(self):
        return f"MRN {self.mrn_no} ({self.sys_date.strftime('%Y-%m-%d') if self.sys_date else 'N/A'})"
        
    def get_generated_by_name(self):
        """Returns the name of the employee who generated the MRN."""
        return self.session_emp.employee_name if self.session_emp else "N/A"

    def get_absolute_url(self):
        """
        Returns the URL to the detail/edit view of this Material Return Note.
        This corresponds to the redirect in ASP.NET's GridView1_RowCommand.
        """
        # This URL needs to be defined in a separate module/view for MRN details/editing.
        return reverse_lazy('inventory:materialreturnnote_detail', kwargs={'pk': self.pk})

    @classmethod
    def filter_notes(cls, comp_id, fin_year_id, mrn_no=None, emp_id_str=None):
        """
        Business logic to filter Material Return Notes based on provided criteria.
        This method replaces the logic of 'Sp_MRN_FillGrid' stored procedure
        and dynamic WHERE clause construction in ASP.NET.
        """
        # Start with base filters for company and financial year
        queryset = cls.objects.filter(company_id=comp_id, fin_year_id=fin_year_id)
        
        # Apply MRN number filter if provided
        if mrn_no:
            queryset = queryset.filter(mrn_no__iexact=mrn_no) # Case-insensitive exact match
        
        # Apply employee ID filter if provided
        if emp_id_str:
            queryset = queryset.filter(session_emp__emp_id__iexact=emp_id_str) # Case-insensitive exact match for employee ID

        return queryset
```

#### 4.2 Forms

**Task:** Define a Django form for handling the search input, as this page's primary function is filtering.

**Instructions:**
A non-ModelForm will be created to manage the search dropdown and text inputs, including basic validation to ensure a search term is provided when a filter type is selected.

```python
# inventory/forms.py
from django import forms

class MaterialReturnNoteSearchForm(forms.Form):
    """
    Form for searching Material Return Notes.
    Manages the search field selection (Employee Name or MRN No) and corresponding input.
    """
    SEARCH_FIELD_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'MRN No'),
    ]
    
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm box3',
                                   'x-ref': 'searchField'}), # Alpine.js ref
        initial='0', # Default to Employee Name as in ASP.NET
        label="Search By"
    )
    
    mrn_no = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm box3',
                                       'placeholder': 'Enter MRN No',
                                       'x-ref': 'mrnInput'}), # Alpine.js ref
        label="MRN No"
    )
    
    employee_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm box3',
                                       'placeholder': 'Enter Employee Name', 
                                       'autocomplete': 'off', # Disable browser autocomplete
                                       'x-ref': 'empNameInput'}), # Alpine.js ref
        label="Employee Name"
    )

    def clean(self):
        """
        Custom validation to ensure a search term is provided for the selected field.
        """
        cleaned_data = super().clean()
        search_field = cleaned_data.get('search_field')
        mrn_no = cleaned_data.get('mrn_no')
        employee_name = cleaned_data.get('employee_name')

        if search_field == '1' and not mrn_no:
            self.add_error('mrn_no', "Please enter MRN No when searching by MRN No.")
        elif search_field == '0' and not employee_name:
            self.add_error('employee_name', "Please enter Employee Name when searching by Employee Name.")
        
        return cleaned_data
```

#### 4.3 Views

**Task:** Implement Class-Based Views (CBVs) for listing Material Return Notes and handling autocomplete.

**Instructions:**
We'll use a `ListView` for the main page and a specific partial view for the HTMX table swap. A separate `View` will handle the employee autocomplete requests. These views are designed to be concise, delegating business logic to the models.

```python
# inventory/views.py
from django.views.generic import ListView, View
from django.http import JsonResponse
from django.db.models import Q # For OR queries if needed
from .models import MaterialReturnNote, Employee
from .forms import MaterialReturnNoteSearchForm
import json # Not strictly needed for JsonResponse, but good practice for more complex JSON.

class MaterialReturnNoteListView(ListView):
    """
    View for displaying a paginated list of Material Return Notes.
    Handles initial page load and search form submission.
    """
    model = MaterialReturnNote
    template_name = 'inventory/materialreturnnote/list.html'
    context_object_name = 'material_return_notes'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        """
        Constructs the queryset based on search form parameters and session context.
        Delegates filtering logic to the MaterialReturnNote model.
        """
        # Placeholder for dynamic CompId and FinYearId from user session/profile
        # In a real application, these would come from request.user or middleware.
        current_comp_id = 1 
        current_fin_year_id = 1 

        form = MaterialReturnNoteSearchForm(self.request.GET or None)
        
        mrn_no_filter = None
        emp_id_filter = None

        if form.is_valid():
            search_field = form.cleaned_data.get('search_field')
            
            if search_field == '1': # Search by MRN No
                mrn_no_filter = form.cleaned_data.get('mrn_no')
            elif search_field == '0': # Search by Employee Name
                employee_name_search = form.cleaned_data.get('employee_name')
                if employee_name_search:
                    employee_obj = Employee.get_employee_by_name_or_id(employee_name_search)
                    if employee_obj:
                        emp_id_filter = employee_obj.emp_id
                    # If employee not found, emp_id_filter remains None, leading to no results for that specific filter.
                    # No explicit error message needed here as it's handled by form validation for empty field.
        
        # Always filter by current company and financial year, then apply search filters
        queryset = MaterialReturnNote.filter_notes(
            comp_id=current_comp_id,
            fin_year_id=current_fin_year_id,
            mrn_no=mrn_no_filter,
            emp_id_str=emp_id_filter
        )
        return queryset

    def get_context_data(self, **kwargs):
        """
        Adds the search form to the template context.
        """
        context = super().get_context_data(**kwargs)
        # Re-initialize form with current GET data to preserve selections/inputs
        context['search_form'] = MaterialReturnNoteSearchForm(self.request.GET or None)
        return context

class MaterialReturnNoteTablePartialView(MaterialReturnNoteListView):
    """
    View for rendering only the Material Return Note table, used for HTMX swaps.
    Inherits filtering logic from MaterialReturnNoteListView.
    """
    template_name = 'inventory/materialreturnnote/_materialreturnnote_table.html'

    # No need to override get() or get_queryset() here, as ListView handles it.
    # We just change the template for the HTMX request.

class EmployeeAutocompleteView(View):
    """
    API endpoint for employee name autocomplete functionality.
    Returns a JSON list of employee names and IDs.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('term', '')
        # Placeholder for dynamic CompId from user session/profile
        current_comp_id = 1 
        
        # Filter employees by search term (case-insensitive contains for name or ID)
        employees = Employee.objects.filter(
            Q(employee_name__icontains=query) | Q(emp_id__icontains=query),
            company_id=current_comp_id # Filter by current company
        ).order_by('employee_name')[:10] # Limit results to 10 for performance, similar to CompletionSetCount

        results = [str(emp) for emp in employees] # Use __str__ method which formats as "Name [ID]"
        
        return JsonResponse(results, safe=False) # safe=False allows serialization of a list directly
```

#### 4.4 Templates

**Task:** Create HTML templates for the main page and the HTMX-swapped table partial, styled with Tailwind CSS.

**Instructions:**
The main list template will include the search form and a container for the dynamic table. The table itself will be in a separate partial template, loaded and refreshed via HTMX. DataTables and jQuery UI (for autocomplete) will be integrated for client-side enhancements.

**`inventory/materialreturnnote/list.html`**
This is the main page template that loads the search form and the HTMX-powered table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Return Note [MRN] - Edit</h2>
        {# No "Add New" button on this page per original ASP.NET analysis, only "Select" to edit existing. #}
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{
        searchField: '{{ search_form.search_field.value|default:'0' }}',
        init() {
            // Re-initialize autocomplete when the Alpine component is initialized, 
            // ensuring it's available after potential HTMX swaps or page reloads.
            this.$nextTick(() => {
                const empNameInput = this.$refs.empNameInput;
                if (empNameInput) {
                    $(empNameInput).autocomplete({
                        source: "{% url 'inventory:employee_autocomplete' %}",
                        minLength: 1, 
                        delay: 100, // CompletionInterval from ASP.NET
                        appendTo: empNameInput.parentElement, // Append to parent for better positioning
                        select: function(event, ui) {
                            $(this).val(ui.item.value);
                            return false; // Prevent default behavior
                        },
                        // Custom CSS classes for autocomplete list items as in ASP.NET
                        open: function(event, ui) {
                            $(this).autocomplete("widget").addClass("almt").removeClass("ui-corner-all");
                            $(this).autocomplete("widget").find(".ui-menu-item").addClass("bg").removeClass("ui-corner-all");
                            $(this).autocomplete("widget").find(".ui-state-active").addClass("bgtext").removeClass("ui-corner-all ui-state-focus");
                        },
                        focus: function(event, ui) {
                            // When an item is focused, update the input field visually
                            $(this).val(ui.item.value);
                            return false;
                        }
                    });
                }
            });
        }
    }">
        <form hx-get="{% url 'inventory:materialreturnnote_table' %}" 
              hx-target="#materialreturnnoteTable-container" 
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_search_field" {# Trigger on form submit OR when the dropdown changes #}
              class="space-y-4">
            
            <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-4">
                <div class="flex-shrink-0">
                    <label for="{{ search_form.search_field.id_for_label }}" class="sr-only">Search Field</label>
                    <select id="{{ search_form.search_field.id_for_label }}" name="{{ search_form.search_field.name }}" 
                            class="{{ search_form.search_field.field.widget.attrs.class }}"
                            x-model="searchField"> {# Bind Alpine.js x-model to manage visibility #}
                        {% for value, label in search_form.search_field.field.choices %}
                            <option value="{{ value }}" {% if search_form.search_field.value == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="flex-grow">
                    <label for="{{ search_form.mrn_no.id_for_label }}" class="sr-only">MRN No</label>
                    <input type="text" id="{{ search_form.mrn_no.id_for_label }}" name="{{ search_form.mrn_no.name }}" 
                           value="{{ search_form.mrn_no.value|default:'' }}"
                           class="{{ search_form.mrn_no.field.widget.attrs.class }} w-full"
                           placeholder="{{ search_form.mrn_no.field.widget.attrs.placeholder }}"
                           x-ref="mrnInput" 
                           x-show="searchField === '1'"> {# Show/hide based on dropdown selection #}
                    {% if search_form.mrn_no.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.mrn_no.errors }}</p>
                    {% endif %}
                    
                    <label for="{{ search_form.employee_name.id_for_label }}" class="sr-only">Employee Name</label>
                    <input type="text" id="{{ search_form.employee_name.id_for_label }}" name="{{ search_form.employee_name.name }}" 
                           value="{{ search_form.employee_name.value|default:'' }}"
                           class="{{ search_form.employee_name.field.widget.attrs.class }} w-full"
                           placeholder="{{ search_form.employee_name.field.widget.attrs.placeholder }}"
                           x-ref="empNameInput" 
                           x-show="searchField === '0'"> {# Show/hide based on dropdown selection #}
                    {% if search_form.employee_name.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.employee_name.errors }}</p>
                    {% endif %}
                </div>
                
                <div class="flex-shrink-0">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            {% if search_form.non_field_errors %}
                <div class="text-red-500 text-sm mt-2">
                    {% for error in search_form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="materialreturnnoteTable-container"
         hx-trigger="load, searchMaterialReturnNote from:body" {# 'load' for initial render, custom 'searchMaterialReturnNote' for programmatic refresh #}
         hx-get="{% url 'inventory:materialreturnnote_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Return Notes...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# jQuery and jQuery UI for Autocomplete #}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js" integrity="sha256-lSjKY0/SrUM9FDKsGMxJXdCoUTXZHPNQu8Rxgce0F8E=" crossorigin="anonymous"></script>
<link href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css" rel="stylesheet">

{# DataTables #}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet">

<style>
    /* Custom CSS for jQuery UI Autocomplete to match ASP.NET styling */
    .ui-autocomplete {
        z-index: 1000; /* Ensure it appears above other elements */
        border: 1px solid #ccc;
        background-color: #fff;
        max-height: 300px;
        overflow-y: auto;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        list-style: none;
        padding: 0;
        margin: 0;
    }
    .ui-menu-item {
        padding: 8px 12px;
        cursor: pointer;
    }
    .ui-menu-item:hover, .ui-state-active {
        background-color: #f0f0f0; /* bg */
        color: #333; /* bgtext */
    }
    .almt { /* CompletionListCssClass */
        border-radius: 4px;
    }
    /* box3 class from ASP.NET */
    .box3 {
        border-width: 1px;
        border-style: solid;
        border-color: #cccccc #e0e0e0 #e0e0e0 #cccccc;
        font-family: Arial, Helvetica, sans-serif;
        font-size: 11px;
        color: #333333;
        background-color: #fcfcfc;
        padding: 4px 6px;
    }
</style>

<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for managing form visibility and autocomplete initialization.
        // The x-data attribute is now on the form's container div.
    });

    // Re-initialize DataTable after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'materialreturnnoteTable-container') {
            // Check if the swapped content contains the table
            const tableElement = $('#materialReturnNoteTable');
            if (tableElement.length && !$.fn.DataTable.isDataTable(tableElement)) {
                tableElement.DataTable({
                    "pageLength": 20, // PageSize from ASP.NET GridView
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "ordering": true,    // Enable sorting
                    "searching": true,   // Enable client-side search box
                    "paging": true,      // Enable pagination
                    "info": true,        // Show "Showing X to Y of Z entries"
                    "responsive": true   // Make table responsive
                });
            }
        }
    });

    // Handle form submission via HTMX without full page reload.
    // The hx-trigger="submit" on the form will handle this automatically.
    // The change from:#id_search_field trigger on the form will re-fetch the table when dropdown changes.
</script>
{% endblock %}
```

**`inventory/materialreturnnote/_materialreturnnote_table.html`**
This partial template contains only the table structure, which will be dynamically loaded into `list.html` via HTMX.

```html
{# inventory/materialreturnnote/_materialreturnnote_table.html #}
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    {% if material_return_notes %}
    <table id="materialReturnNoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRN No</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for note in material_return_notes %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ note.mrn_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ note.sys_date|date:"Y-m-d" }}</td> {# Format date as needed #}
                <td class="py-2 px-4 whitespace-nowrap">{{ note.fin_year }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ note.get_generated_by_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    {# This "Select" action mirrors the ASP.NET Response.Redirect behavior #}
                    <a href="{{ note.get_absolute_url }}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs">
                        Select
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="text-center text-gray-600 py-8 text-lg">No data to display !</p>
    {% endif %}
</div>

{# No <script> tags for DataTables initialization here. It's handled by htmx:afterSwap in list.html. #}
{# This keeps the partial template clean and focused on content. #}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views within the `inventory` app.

**Instructions:**
URL patterns will be created for the main list page, the HTMX-driven table partial, and the employee autocomplete endpoint.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    MaterialReturnNoteListView, 
    MaterialReturnNoteTablePartialView,
    EmployeeAutocompleteView,
)

app_name = 'inventory' # Namespace for this application's URLs

urlpatterns = [
    # Main list page for Material Return Notes
    path('materialreturnnote/', MaterialReturnNoteListView.as_view(), name='materialreturnnote_list'),
    
    # HTMX endpoint for dynamically loading/refreshing the table content
    path('materialreturnnote/table/', MaterialReturnNoteTablePartialView.as_view(), name='materialreturnnote_table'),

    # API endpoint for employee name autocomplete suggestions
    path('employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    
    # Placeholder URL for the Material Return Note detail/edit page.
    # This corresponds to the ASP.NET MaterialReturnNote_MRN_Edit_Details.aspx redirection.
    # A separate Django DetailView/UpdateView would handle this.
    path('materialreturnnote/<int:pk>/detail/', MaterialReturnNoteListView.as_view(), name='materialreturnnote_detail'), 
    # NOTE: MaterialReturnNoteListView is a placeholder here.
    # In a full application, this would be a specific DetailView or UpdateView for MRN.
]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the models and integration tests for the views to ensure functionality and correctness.

**Instructions:**
Tests will cover model data integrity, business logic (filtering), and view interactions, including HTMX requests.

```python       
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialReturnNote, Employee
from datetime import datetime

class EmployeeModelTest(TestCase):
    """
    Unit tests for the Employee model and its class methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company_id = 1
        Employee.objects.create(emp_id='EMP001', employee_name='John Doe', company_id=cls.company_id)
        Employee.objects.create(emp_id='EMP002', employee_name='Jane Smith', company_id=cls.company_id)
        Employee.objects.create(emp_id='EMP003', employee_name='Alice Brown', company_id=cls.company_id)
  
    def test_employee_creation(self):
        """Test that an Employee object can be created successfully."""
        emp = Employee.objects.get(emp_id='EMP001')
        self.assertEqual(emp.employee_name, 'John Doe')
        self.assertEqual(emp.company_id, self.company_id)
        
    def test_get_employee_by_name_or_id_with_id_in_name(self):
        """Test resolving an employee using the 'Name [ID]' format."""
        emp = Employee.get_employee_by_name_or_id('John Doe [EMP001]')
        self.assertIsNotNone(emp)
        self.assertEqual(emp.emp_id, 'EMP001')

    def test_get_employee_by_name_or_id_with_name_only(self):
        """Test resolving an employee using only their name."""
        emp = Employee.get_employee_by_name_or_id('Jane Smith')
        self.assertIsNotNone(emp)
        self.assertEqual(emp.emp_id, 'EMP002')

    def test_get_employee_by_name_or_id_not_found(self):
        """Test that the method returns None for a non-existent employee."""
        emp = Employee.get_employee_by_name_or_id('Non Existent')
        self.assertIsNone(emp)

    def test_employee_str_representation(self):
        """Test the __str__ method of the Employee model."""
        emp = Employee.objects.get(emp_id='EMP001')
        self.assertEqual(str(emp), 'John Doe [EMP001]')

class MaterialReturnNoteModelTest(TestCase):
    """
    Unit tests for the MaterialReturnNote model and its class methods.
    """
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.fin_year_id = 2024
        cls.employee = Employee.objects.create(emp_id='EMP001', employee_name='Test Employee', company_id=cls.company_id)
        
        MaterialReturnNote.objects.create(
            id=1, 
            fin_year_id=cls.fin_year_id, 
            fin_year='2024-25', 
            sys_date=datetime(2024, 5, 10, 10, 0, 0), 
            mrn_no='MRN/001', 
            session_emp=cls.employee, 
            company_id=cls.company_id
        )
        MaterialReturnNote.objects.create(
            id=2, 
            fin_year_id=cls.fin_year_id, 
            fin_year='2024-25', 
            sys_date=datetime(2024, 5, 11, 11, 0, 0), 
            mrn_no='MRN/002', 
            session_emp=None, # Test with no employee associated
            company_id=cls.company_id
        )
        MaterialReturnNote.objects.create(
            id=3, 
            fin_year_id=cls.fin_year_id + 1, # Different financial year
            fin_year='2025-26', 
            sys_date=datetime(2025, 1, 1, 9, 0, 0), 
            mrn_no='MRN/003', 
            session_emp=cls.employee, 
            company_id=cls.company_id
        )


    def test_materialreturnnote_creation(self):
        """Test that a MaterialReturnNote object can be created successfully."""
        note = MaterialReturnNote.objects.get(id=1)
        self.assertEqual(note.mrn_no, 'MRN/001')
        self.assertEqual(note.session_emp.employee_name, 'Test Employee')
        self.assertEqual(note.company_id, self.company_id)
        
    def test_get_generated_by_name(self):
        """Test the get_generated_by_name method."""
        note1 = MaterialReturnNote.objects.get(id=1)
        self.assertEqual(note1.get_generated_by_name(), 'Test Employee')
        note2 = MaterialReturnNote.objects.get(id=2)
        self.assertEqual(note2.get_generated_by_name(), 'N/A')

    def test_filter_notes_by_mrn_no(self):
        """Test filtering notes by MRN number."""
        notes = MaterialReturnNote.filter_notes(
            comp_id=self.company_id, 
            fin_year_id=self.fin_year_id, 
            mrn_no='MRN/001'
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().mrn_no, 'MRN/001')

    def test_filter_notes_by_employee_id(self):
        """Test filtering notes by employee ID."""
        notes = MaterialReturnNote.filter_notes(
            comp_id=self.company_id, 
            fin_year_id=self.fin_year_id, 
            emp_id_str='EMP001'
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().mrn_no, 'MRN/001')

    def test_filter_notes_no_criteria(self):
        """Test filtering notes with no specific search criteria."""
        notes = MaterialReturnNote.filter_notes(
            comp_id=self.company_id, 
            fin_year_id=self.fin_year_id
        )
        self.assertEqual(notes.count(), 2) # Both notes for current fin_year_id should be returned

    def test_filter_notes_different_financial_year(self):
        """Test that notes from a different financial year are excluded."""
        notes = MaterialReturnNote.filter_notes(
            comp_id=self.company_id, 
            fin_year_id=self.fin_year_id + 1, 
            mrn_no='MRN/003'
        )
        self.assertEqual(notes.count(), 1)
        self.assertEqual(notes.first().mrn_no, 'MRN/003')

    def test_materialreturnnote_str_representation(self):
        """Test the __str__ method of the MaterialReturnNote model."""
        note = MaterialReturnNote.objects.get(id=1)
        self.assertIn('MRN/001', str(note))
        self.assertIn('2024-05-10', str(note))

class MaterialReturnNoteViewsTest(TestCase):
    """
    Integration tests for the MaterialReturnNote views.
    """
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.fin_year_id = 2024
        cls.employee = Employee.objects.create(emp_id='EMP001', employee_name='Test Employee', company_id=cls.company_id)
        
        MaterialReturnNote.objects.create(
            id=1, 
            fin_year_id=cls.fin_year_id, 
            fin_year='2024-25', 
            sys_date=datetime(2024, 5, 10, 10, 0, 0), 
            mrn_no='MRN/001', 
            session_emp=cls.employee, 
            company_id=cls.company_id
        )
        MaterialReturnNote.objects.create(
            id=2, 
            fin_year_id=cls.fin_year_id, 
            fin_year='2024-25', 
            sys_date=datetime(2024, 5, 11, 11, 0, 0), 
            mrn_no='MRN/002', 
            session_emp=None,
            company_id=cls.company_id
        )
    
    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test that the main list view renders correctly with default notes."""
        response = self.client.get(reverse('inventory:materialreturnnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/list.html')
        self.assertTrue('material_return_notes' in response.context)
        self.assertEqual(response.context['material_return_notes'].count(), 2) # All notes for current fin_year_id

    def test_list_view_search_by_mrn_no(self):
        """Test searching the list by MRN number."""
        response = self.client.get(reverse('inventory:materialreturnnote_list'), {
            'search_field': '1', 
            'mrn_no': 'MRN/001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/list.html')
        self.assertEqual(response.context['material_return_notes'].count(), 1)
        self.assertEqual(response.context['material_return_notes'].first().mrn_no, 'MRN/001')

    def test_list_view_search_by_employee_name(self):
        """Test searching the list by employee name (simulating autocomplete input)."""
        response = self.client.get(reverse('inventory:materialreturnnote_list'), {
            'search_field': '0', 
            'employee_name': 'Test Employee [EMP001]' 
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/list.html')
        self.assertEqual(response.context['material_return_notes'].count(), 1)
        self.assertEqual(response.context['material_return_notes'].first().mrn_no, 'MRN/001')

    def test_list_view_search_by_non_existent_employee(self):
        """Test searching by a non-existent employee name results in no notes."""
        response = self.client.get(reverse('inventory:materialreturnnote_list'), {
            'search_field': '0', 
            'employee_name': 'Non Existent Employee'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/list.html')
        self.assertEqual(response.context['material_return_notes'].count(), 0) # No notes found

    def test_list_view_invalid_form_submission(self):
        """Test that an invalid form submission (e.g., missing required search term)
        still renders the page with form errors."""
        response = self.client.get(reverse('inventory:materialreturnnote_list'), {
            'search_field': '1', 
            'mrn_no': '' # Missing MRN No for MRN search
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/list.html')
        self.assertTrue('search_form' in response.context)
        self.assertTrue(response.context['search_form'].errors)
        self.assertIn('mrn_no', response.context['search_form'].errors)
        # Even with form errors, the initial queryset should be shown, or an empty one if filtered.
        # In this case, `filter_notes` won't apply an MRN filter if it's empty, so it might return all.
        # It's important to check the `material_return_notes` context after error.
        self.assertEqual(response.context['material_return_notes'].count(), 2)


    def test_htmx_table_partial_view(self):
        """Test that the HTMX endpoint for the table partial renders correctly."""
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate an HTMX request
        response = self.client.get(reverse('inventory:materialreturnnote_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_materialreturnnote_table.html')
        self.assertTrue('material_return_notes' in response.context)
        self.assertEqual(response.context['material_return_notes'].count(), 2) # All notes for current fin_year_id

    def test_employee_autocomplete_view(self):
        """Test the employee autocomplete API endpoint."""
        response = self.client.get(reverse('inventory:employee_autocomplete'), {'term': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('John Doe [EMP001]', data)
        self.assertNotIn('Jane Smith [EMP002]', data) # Should not be in results

    def test_employee_autocomplete_view_empty_term(self):
        """Test autocomplete with an empty search term returns limited results."""
        response = self.client.get(reverse('inventory:employee_autocomplete'), {'term': ''})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertGreater(len(data), 0) # Should return some employees (up to 10)
        self.assertLessEqual(len(data), 10) # Max 10 results

    def test_employee_autocomplete_view_no_match(self):
        """Test autocomplete with a term that has no matches."""
        response = self.client.get(reverse('inventory:employee_autocomplete'), {'term': 'xyzabc'})
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 0)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided Django templates (`list.html` and `_materialreturnnote_table.html`) and the associated `extra_js` block demonstrate the following:

*   **HTMX for Dynamic Updates:**
    *   The search `form` uses `hx-get="{% url 'inventory:materialreturnnote_table' %}"` and `hx-target="#materialreturnnoteTable-container"` with `hx-swap="innerHTML"` to load the table content dynamically when the form is submitted or the search field dropdown changes. This replaces ASP.NET's `AutoPostBack` and `Button1_Click` full page refreshes with partial updates.
    *   `hx-trigger="load, searchMaterialReturnNote from:body"` ensures the table loads on page entry and can be refreshed programmatically if needed (e.g., after an action on another related page).
*   **Alpine.js for UI State Management:**
    *   The `x-data` attribute on the search form's container (`div`) is used to manage the `searchField` variable.
    *   `x-model="searchField"` on the dropdown (`select`) keeps its value synchronized with the Alpine.js state.
    *   `x-show="searchField === '1'"` and `x-show="searchField === '0'"` on the input fields (`mrn_no` and `employee_name`) dynamically show/hide them based on the selected search criterion, replicating the ASP.NET `Visible` property toggling.
    *   `x-ref` attributes are used to reference the input elements for jQuery UI Autocomplete initialization within Alpine's `init` block, ensuring elements are available in the DOM.
*   **DataTables for List Views:**
    *   The `_materialreturnnote_table.html` partial contains the `<table>` element with the ID `materialReturnNoteTable`.
    *   The `extra_js` block in `list.html` listens for the `htmx:afterSwap` event on the `materialreturnnoteTable-container`. Once the new table content is loaded by HTMX, it re-initializes `$(#materialReturnNoteTable).DataTable()`, ensuring full client-side search, sort, and pagination capabilities.
*   **HTMX-only Interactions:** All listed interactions (search, filtering, table refresh) are driven by HTMX and Alpine.js, minimizing custom JavaScript beyond necessary library initialization and configuration.
*   **Base Template Inheritance:** `list.html` extends `core/base.html`, which is assumed to contain all common CDN links (jQuery, DataTables, Alpine.js, Tailwind CSS) to adhere to DRY principles. The `extra_js` block in `list.html` is used for component-specific JS.

### Final Notes

*   **Placeholders:** Ensure `CompId` and `FinYearId` are dynamically retrieved from your Django application's session or user context in a real deployment, replacing the hardcoded dummy values.
*   **`materialreturnnote_detail` URL:** Remember that `materialreturnnote_detail` is a placeholder. You will need to implement a separate Django view (likely a `DetailView` or `UpdateView`) and its corresponding template for handling the details and editing of an individual Material Return Note.
*   **Scalability:** The fat model/thin view approach, combined with HTMX for dynamic updates and DataTables for client-side processing, provides a highly scalable and performant solution, reducing server load and improving user experience.
*   **Test Coverage:** The provided test suite aims for comprehensive coverage of models and views. As your application grows, continuously expand tests to maintain high code quality and prevent regressions.