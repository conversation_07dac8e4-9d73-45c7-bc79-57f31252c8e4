This ASP.NET code represents a foundational, blank page within a larger application structure, likely serving as a placeholder or a dashboard entry point. It doesn't expose specific database interactions, business logic, or UI components directly in the provided snippets.

Given the name `Inward_DashBoard` and its location `Module_Inventory_Transactions`, we will infer a common scenario: an "Inward Transaction" module. This will allow us to demonstrate a complete Django modernization, adhering strictly to the "Fat Model, Thin View" principle, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for list presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

This ASP.NET page is a blank canvas. To provide a meaningful Django modernization plan, we will assume it's meant to display and manage a list of "Inward Transactions" as implied by its name and module path.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code (both .aspx and .aspx.cs) contains no explicit database interactions, `SqlDataSource` declarations, or direct SQL commands, we must infer a plausible database schema based on the page's name: `Inward_DashBoard` within `Module_Inventory_Transactions`.

**Inferred Details:**

-   **[TABLE_NAME]:** `tbl_inward_transactions` (A common ASP.NET convention prefixing tables with `tbl_`)
-   **[MODEL_NAME]:** `InwardTransaction`
-   **[APP_NAME]:** `inventory` (Derived from `Module_Inventory`)

**Inferred Columns:**
We'll assume a basic `InwardTransaction` table would include fields necessary for tracking incoming inventory.

-   `inward_id` (Primary Key, Auto-incrementing)
-   `transaction_date` (Date of the inward transaction)
-   `item_code` (Code of the item received)
-   `quantity_received` (Number of units received)
-   `supplier_name` (Name of the supplier)
-   `document_reference` (Reference number, e.g., GRN, Invoice)
-   `remarks` (Any additional notes)
-   `is_approved` (Boolean indicating approval status)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code is empty and doesn't explicitly define any CRUD operations. However, a dashboard for "Inward Transactions" would typically support:

-   **Read (R):** Displaying a list of all inward transactions, possibly with filtering and pagination. This is the primary function of a "Dashboard."
-   **Create (C):** Adding new inward transactions.
-   **Update (U):** Modifying existing inward transaction details.
-   **Delete (D):** Removing inward transactions.

We will implement these standard CRUD operations in Django. No specific validation logic was present in the ASP.NET code, so we will implement basic Django form validation and demonstrate custom validation patterns.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET `.aspx` file is bare, containing only `asp:Content` placeholders. Based on the inferred backend functionality (Step 2), a typical UI for an "Inward Transaction Dashboard" would involve:

-   **A Data Grid/Table:** To display a list of `InwardTransaction` records. This corresponds to a `GridView` in ASP.NET, which we will replace with a DataTables-enhanced HTML table.
-   **Add/Edit/Delete Buttons:** For triggering CRUD operations. These would typically be `asp:Button` or `asp:LinkButton` controls in ASP.NET. In Django, we will use standard HTML buttons enhanced with HTMX for dynamic modal interactions.
-   **Input Forms:** For creating or updating `InwardTransaction` details. This would involve `asp:TextBox`, `asp:DropDownList`, `asp:CheckBox` controls. In Django, we will use a `ModelForm` rendered within HTMX-loaded modals.
-   **Client-side interactivity:** The presence of `loadingNotifier.js` suggests some client-side JavaScript for UX feedback. We will replace this with HTMX and Alpine.js patterns.

---

## Step 4: Generate Django Code

Now, we will generate the Django components based on our analysis and inferred details, strictly following the specified guidelines.

**[APP_NAME]** will be `inventory`.
**[MODEL_NAME]** will be `InwardTransaction`.
**[MODEL_NAME_LOWER]** will be `inwardtransaction`.
**[MODEL_NAME_PLURAL]** will be `InwardTransactions`.
**[MODEL_NAME_PLURAL_LOWER]** will be `inwardtransactions`.

### 4.1 Models (`inventory/models.py`)

This model will represent the `tbl_inward_transactions` table in your database.

```python
from django.db import models
from django.utils import timezone

class InwardTransaction(models.Model):
    """
    Represents an inward transaction record for inventory.
    """
    inward_id = models.AutoField(db_column='inward_id', primary_key=True)
    transaction_date = models.DateField(db_column='transaction_date', default=timezone.now,
                                        help_text="Date of the inward transaction.")
    item_code = models.CharField(db_column='item_code', max_length=50,
                                 help_text="Unique code for the item received.")
    quantity_received = models.IntegerField(db_column='quantity_received',
                                           help_text="Number of units received.")
    supplier_name = models.CharField(db_column='supplier_name', max_length=100,
                                     help_text="Name of the supplier.")
    document_reference = models.CharField(db_column='document_reference', max_length=100, blank=True, null=True,
                                          help_text="Reference number (e.g., GRN, Invoice).")
    remarks = models.TextField(db_column='remarks', blank=True, null=True,
                               help_text="Any additional notes about the transaction.")
    is_approved = models.BooleanField(db_column='is_approved', default=False,
                                      help_text="Approval status of the transaction.")

    class Meta:
        managed = False  # Set to False if the table already exists and is not managed by Django migrations
        db_table = 'tbl_inward_transactions'
        verbose_name = 'Inward Transaction'
        verbose_name_plural = 'Inward Transactions'
        ordering = ['-transaction_date', 'item_code'] # Default ordering

    def __str__(self):
        return f"INW-{self.inward_id} - {self.item_code} ({self.quantity_received})"

    def get_display_status(self):
        """
        Business logic: Returns a user-friendly status string for the approval status.
        """
        return "Approved" if self.is_approved else "Pending Approval"

    def mark_as_approved(self):
        """
        Business logic: Marks the transaction as approved.
        """
        if not self.is_approved:
            self.is_approved = True
            self.save()
            return True
        return False

    def can_be_deleted(self):
        """
        Business logic: Determines if a transaction can be deleted.
        Example: only if not approved.
        """
        return not self.is_approved

```

### 4.2 Forms (`inventory/forms.py`)

This form will handle data input and validation for `InwardTransaction` objects.

```python
from django import forms
from .models import InwardTransaction
import re

class InwardTransactionForm(forms.ModelForm):
    """
    Form for creating and updating InwardTransaction records.
    """
    class Meta:
        model = InwardTransaction
        fields = [
            'transaction_date',
            'item_code',
            'quantity_received',
            'supplier_name',
            'document_reference',
            'remarks',
            'is_approved'
        ]
        widgets = {
            'transaction_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity_received': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'document_reference': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'is_approved': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'inward_id': 'Transaction ID',
            'transaction_date': 'Transaction Date',
            'item_code': 'Item Code',
            'quantity_received': 'Quantity Received',
            'supplier_name': 'Supplier Name',
            'document_reference': 'Document Reference',
            'remarks': 'Remarks',
            'is_approved': 'Approved',
        }

    def clean_item_code(self):
        """
        Custom validation for item_code: Ensure it's alphanumeric.
        """
        item_code = self.cleaned_data.get('item_code')
        if not re.match(r'^[a-zA-Z0-9_-]+$', item_code):
            raise forms.ValidationError("Item Code must be alphanumeric, hyphens, or underscores.")
        return item_code

    def clean_quantity_received(self):
        """
        Custom validation for quantity_received: Must be a positive value.
        """
        quantity = self.cleaned_data.get('quantity_received')
        if quantity is not None and quantity <= 0:
            raise forms.ValidationError("Quantity received must be a positive number.")
        return quantity

```

### 4.3 Views (`inventory/views.py`)

These views implement the CRUD operations using Django's Class-Based Views, keeping them thin as per the fat model/thin view principle. A `TablePartialView` is added to support HTMX loading of the DataTables content.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import InwardTransaction
from .forms import InwardTransactionForm

class InwardTransactionListView(ListView):
    """
    Displays the main dashboard for Inward Transactions.
    The actual table content is loaded via HTMX from InwardTransactionTablePartialView.
    """
    model = InwardTransaction
    template_name = 'inventory/inwardtransaction/list.html'
    context_object_name = 'inwardtransactions' # This context is mainly for initial setup, table loads dynamically

class InwardTransactionTablePartialView(TemplateView):
    """
    Renders only the DataTables table content for Inward Transactions.
    Used by HTMX to load and refresh the table without full page reload.
    """
    template_name = 'inventory/inwardtransaction/_inwardtransaction_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['inwardtransactions'] = InwardTransaction.objects.all()
        return context

class InwardTransactionCreateView(CreateView):
    """
    Handles creation of new Inward Transaction records via a modal form.
    Returns HTMX response for successful creation to trigger table refresh.
    """
    model = InwardTransaction
    form_class = InwardTransactionForm
    template_name = 'inventory/inwardtransaction/_inwardtransaction_form.html'
    success_url = reverse_lazy('inwardtransaction_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Inward Transaction added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content with a trigger header for HTMX
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshInwardTransactionList'}
            )
        return response

class InwardTransactionUpdateView(UpdateView):
    """
    Handles updating existing Inward Transaction records via a modal form.
    Returns HTMX response for successful update to trigger table refresh.
    """
    model = InwardTransaction
    form_class = InwardTransactionForm
    template_name = 'inventory/inwardtransaction/_inwardtransaction_form.html'
    success_url = reverse_lazy('inwardtransaction_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Inward Transaction updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content with a trigger header for HTMX
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshInwardTransactionList'}
            )
        return response

class InwardTransactionDeleteView(DeleteView):
    """
    Handles deletion of Inward Transaction records via a confirmation modal.
    Returns HTMX response for successful deletion to trigger table refresh.
    """
    model = InwardTransaction
    template_name = 'inventory/inwardtransaction/_inwardtransaction_confirm_delete.html'
    context_object_name = 'inwardtransaction'
    success_url = reverse_lazy('inwardtransaction_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Example business logic check before deletion (from model)
        if not self.object.can_be_deleted():
            messages.error(request, 'Inward Transaction cannot be deleted as it is already approved.')
            # Return a response to close modal and perhaps show error
            return HttpResponse(status=403, headers={'HX-Trigger': 'closeModal'})
        
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Inward Transaction deleted successfully.')
        if request.headers.get('HX-Request'):
            # Return 204 No Content with a trigger header for HTMX
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshInwardTransactionList'}
            )
        return response

```

### 4.4 Templates (`inventory/templates/inventory/inwardtransaction/`)

These templates implement the UI with HTMX, Alpine.js, and DataTables.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Inward Transactions Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200"
            hx-get="{% url 'inwardtransaction_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Transaction
        </button>
    </div>
    
    <div id="inwardtransactionTable-container"
         class="bg-white shadow-md rounded-lg p-4"
         hx-trigger="load, refreshInwardTransactionList from:body"
         hx-get="{% url 'inwardtransaction_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Inward Transactions...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active:flex transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on closeModal from body remove .is-active from me"> {# Listen for custom event to close modal #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 transform transition-transform duration-300 scale-95 is-active:scale-100"
             _="on hx:afterOnLoad from #modalContent if (event.detail.xhr.status >= 400) add .is-active to #modal else add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed for this basic setup,
        // but this block is reserved for future Alpine.js functionality.
        // The modal visibility is handled by Hyperscript/HTMX directly.
    });
</script>
{% endblock %}
```

#### `_inwardtransaction_table.html` (Partial)

```html
<div class="overflow-x-auto">
    <table id="inwardtransactionTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Supplier</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Reference</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Approved</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in inwardtransactions %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.transaction_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.quantity_received }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.supplier_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.document_reference|default:"-" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if obj.is_approved %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ obj.get_display_status }}
                    </span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700 flex space-x-2">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs"
                        hx-get="{% url 'inwardtransaction_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs"
                        hx-get="{% url 'inwardtransaction_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 text-center text-gray-500">No inward transactions found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors on HTMX swap
    if ($.fn.DataTable.isDataTable('#inwardtransactionTable')) {
        $('#inwardtransactionTable').DataTable().destroy();
    }
    $('#inwardtransactionTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] }, // Disable sorting on SN and Actions columns
            { "searchable": false, "targets": [0, 7] } // Disable searching on SN and Actions columns
        ]
    });
});
</script>
```

#### `_inwardtransaction_form.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Inward Transaction</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-5 gap-x-6">
            {% for field in form %}
            <div class="{% if field.name == 'remarks' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200">
                Save Transaction
                <span id="form-spinner" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

#### `_inwardtransaction_confirm_delete.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Inward Transaction for item "<strong>{{ inwardtransaction.item_code }}</strong>" from <strong>{{ inwardtransaction.transaction_date|date:"Y-m-d" }}</strong>?</p>
    
    <form hx-post="{% url 'inwardtransaction_delete' inwardtransaction.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-200">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for the `inventory` application.

```python
from django.urls import path
from .views import (
    InwardTransactionListView,
    InwardTransactionTablePartialView,
    InwardTransactionCreateView,
    InwardTransactionUpdateView,
    InwardTransactionDeleteView
)

urlpatterns = [
    # Main dashboard view
    path('inward-transactions/', InwardTransactionListView.as_view(), name='inwardtransaction_list'),

    # HTMX partial for the DataTables table
    path('inward-transactions/table/', InwardTransactionTablePartialView.as_view(), name='inwardtransaction_table'),

    # CRUD operations via modals
    path('inward-transactions/add/', InwardTransactionCreateView.as_view(), name='inwardtransaction_add'),
    path('inward-transactions/edit/<int:pk>/', InwardTransactionUpdateView.as_view(), name='inwardtransaction_edit'),
    path('inward-transactions/delete/<int:pk>/', InwardTransactionDeleteView.as_view(), name='inwardtransaction_delete'),
]

```

**Remember to include these URLs in your project's main `urls.py`:**

```python
# yourproject/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('inventory/', include('inventory.urls')), # Include your new app's URLs
    # ... other paths
]
```

### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for both the model's business logic and the views' functionality, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import InwardTransaction
from .forms import InwardTransactionForm

class InwardTransactionModelTest(TestCase):
    """
    Unit tests for the InwardTransaction model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a sample transaction for all tests
        cls.transaction1 = InwardTransaction.objects.create(
            transaction_date=timezone.now().date(),
            item_code='ITEM-001',
            quantity_received=100,
            supplier_name='Supplier A',
            document_reference='GRN-001',
            remarks='Initial stock.',
            is_approved=False
        )
        cls.transaction2 = InwardTransaction.objects.create(
            transaction_date=timezone.now().date() - timezone.timedelta(days=5),
            item_code='ITEM-002',
            quantity_received=50,
            supplier_name='Supplier B',
            document_reference='GRN-002',
            remarks='Consignment received.',
            is_approved=True
        )

    def test_inward_transaction_creation(self):
        """Verify object creation and field values."""
        self.assertEqual(self.transaction1.item_code, 'ITEM-001')
        self.assertEqual(self.transaction1.quantity_received, 100)
        self.assertFalse(self.transaction1.is_approved)

    def test_str_representation(self):
        """Test the __str__ method."""
        expected_str = f"INW-{self.transaction1.inward_id} - ITEM-001 (100)"
        self.assertEqual(str(self.transaction1), expected_str)

    def test_get_display_status_method(self):
        """Test the get_display_status model method."""
        self.assertEqual(self.transaction1.get_display_status(), "Pending Approval")
        self.assertEqual(self.transaction2.get_display_status(), "Approved")

    def test_mark_as_approved_method(self):
        """Test the mark_as_approved model method."""
        self.assertFalse(self.transaction1.is_approved)
        result = self.transaction1.mark_as_approved()
        self.assertTrue(result) # Should return True as it was updated
        self.assertTrue(self.transaction1.is_approved)
        # Try to approve again, should return False
        result_again = self.transaction1.mark_as_approved()
        self.assertFalse(result_again)

    def test_can_be_deleted_method(self):
        """Test the can_be_deleted model method."""
        self.assertTrue(self.transaction1.can_be_deleted()) # Not approved
        self.assertFalse(self.transaction2.can_be_deleted()) # Approved

    def test_item_code_max_length(self):
        """Test item_code max_length."""
        max_length = self.transaction1._meta.get_field('item_code').max_length
        self.assertEqual(max_length, 50)

    def test_db_table_name(self):
        """Verify the db_table name."""
        self.assertEqual(InwardTransaction._meta.db_table, 'tbl_inward_transactions')

    def test_managed_status(self):
        """Verify managed is set to False."""
        self.assertFalse(InwardTransaction._meta.managed)


class InwardTransactionFormTest(TestCase):
    """
    Unit tests for the InwardTransactionForm.
    """
    def test_form_valid_data(self):
        form = InwardTransactionForm(data={
            'transaction_date': timezone.now().date(),
            'item_code': 'NEW_ITEM-ABC',
            'quantity_received': 75,
            'supplier_name': 'New Supplier',
            'document_reference': 'INV-005',
            'remarks': 'New entry',
            'is_approved': False
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_item_code(self):
        form = InwardTransactionForm(data={
            'transaction_date': timezone.now().date(),
            'item_code': 'ITEM 123!', # Invalid character
            'quantity_received': 10,
            'supplier_name': 'Test',
        })
        self.assertFalse(form.is_valid())
        self.assertIn('item_code', form.errors)
        self.assertEqual(form.errors['item_code'], ["Item Code must be alphanumeric, hyphens, or underscores."])

    def test_form_invalid_quantity(self):
        form = InwardTransactionForm(data={
            'transaction_date': timezone.now().date(),
            'item_code': 'ITEM-XYZ',
            'quantity_received': 0, # Invalid quantity
            'supplier_name': 'Test',
        })
        self.assertFalse(form.is_valid())
        self.assertIn('quantity_received', form.errors)
        self.assertEqual(form.errors['quantity_received'], ["Quantity received must be a positive number."])

    def test_form_missing_required_fields(self):
        form = InwardTransactionForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('transaction_date', form.errors)
        self.assertIn('item_code', form.errors)
        self.assertIn('quantity_received', form.errors)
        self.assertIn('supplier_name', form.errors)


class InwardTransactionViewsTest(TestCase):
    """
    Integration tests for InwardTransaction views (ListView, CreateView, UpdateView, DeleteView).
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.transaction = InwardTransaction.objects.create(
            transaction_date=timezone.now().date(),
            item_code='VIEW-ITEM-001',
            quantity_received=75,
            supplier_name='View Supplier',
            document_reference='REF-001',
            is_approved=False
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test the InwardTransactionListView."""
        response = self.client.get(reverse('inwardtransaction_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/inwardtransaction/list.html')
        # The actual table content is loaded via HTMX, so no objects directly in initial context

    def test_table_partial_view_get(self):
        """Test the InwardTransactionTablePartialView (HTMX target)."""
        response = self.client.get(reverse('inwardtransaction_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/inwardtransaction/_inwardtransaction_table.html')
        self.assertTrue('inwardtransactions' in response.context)
        self.assertContains(response, 'VIEW-ITEM-001') # Check if our test data is rendered

    def test_create_view_get(self):
        """Test GET request for InwardTransactionCreateView."""
        response = self.client.get(reverse('inwardtransaction_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/inwardtransaction/_inwardtransaction_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success_htmx(self):
        """Test POST request for InwardTransactionCreateView with HTMX."""
        data = {
            'transaction_date': (timezone.now() - timezone.timedelta(days=1)).strftime('%Y-%m-%d'),
            'item_code': 'NEW-ITEM-ABC',
            'quantity_received': 200,
            'supplier_name': 'New Supplier Co.',
            'document_reference': 'NEW-REF-001',
            'remarks': 'Test HTMX create.',
            'is_approved': 'on'
        }
        response = self.client.post(reverse('inwardtransaction_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content with HX-Trigger for HTMX success
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInwardTransactionList')
        
        # Verify object was created
        self.assertTrue(InwardTransaction.objects.filter(item_code='NEW-ITEM-ABC').exists())
        self.assertEqual(InwardTransaction.objects.filter(item_code='NEW-ITEM-ABC').first().quantity_received, 200)

    def test_create_view_post_invalid_htmx(self):
        """Test POST request for InwardTransactionCreateView with invalid data (HTMX)."""
        data = {
            'transaction_date': '', # Invalid data
            'item_code': 'INV ID!',
            'quantity_received': 0,
            'supplier_name': '',
        }
        response = self.client.post(reverse('inwardtransaction_add'), data, HTTP_HX_REQUEST='true')
        
        # Expect 200 OK with form errors for HTMX validation
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/inwardtransaction/_inwardtransaction_form.html')
        self.assertContains(response, 'This field is required')
        self.assertContains(response, 'Item Code must be alphanumeric')
        self.assertContains(response, 'Quantity received must be a positive number.')
        self.assertFalse(InwardTransaction.objects.filter(item_code='INV ID!').exists()) # Ensure no object created

    def test_update_view_get(self):
        """Test GET request for InwardTransactionUpdateView."""
        response = self.client.get(reverse('inwardtransaction_edit', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/inwardtransaction/_inwardtransaction_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'VIEW-ITEM-001') # Check if existing data is pre-filled

    def test_update_view_post_success_htmx(self):
        """Test POST request for InwardTransactionUpdateView with HTMX."""
        updated_data = {
            'transaction_date': self.transaction.transaction_date.strftime('%Y-%m-%d'),
            'item_code': 'VIEW-ITEM-UPDATED',
            'quantity_received': 150,
            'supplier_name': 'Updated Supplier',
            'document_reference': self.transaction.document_reference,
            'remarks': 'Updated transaction.',
            'is_approved': 'on'
        }
        response = self.client.post(reverse('inwardtransaction_edit', args=[self.transaction.pk]), updated_data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content with HX-Trigger for HTMX success
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInwardTransactionList')
        
        # Verify object was updated
        self.transaction.refresh_from_db()
        self.assertEqual(self.transaction.item_code, 'VIEW-ITEM-UPDATED')
        self.assertEqual(self.transaction.quantity_received, 150)
        self.assertTrue(self.transaction.is_approved)

    def test_delete_view_get(self):
        """Test GET request for InwardTransactionDeleteView."""
        response = self.client.get(reverse('inwardtransaction_delete', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/inwardtransaction/_inwardtransaction_confirm_delete.html')
        self.assertTrue('inwardtransaction' in response.context)
        self.assertContains(response, 'VIEW-ITEM-001')

    def test_delete_view_post_success_htmx(self):
        """Test POST request for InwardTransactionDeleteView with HTMX."""
        # Create a deletable transaction
        deletable_transaction = InwardTransaction.objects.create(
            transaction_date=timezone.now().date(),
            item_code='TO-BE-DELETED',
            quantity_received=1,
            supplier_name='Temp',
            is_approved=False
        )
        response = self.client.post(reverse('inwardtransaction_delete', args=[deletable_transaction.pk]), HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content with HX-Trigger for HTMX success
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshInwardTransactionList')
        
        # Verify object was deleted
        self.assertFalse(InwardTransaction.objects.filter(pk=deletable_transaction.pk).exists())

    def test_delete_view_post_failure_htmx(self):
        """Test POST request for InwardTransactionDeleteView when deletion is not allowed (HTMX)."""
        # We'll use self.transaction, which is initially not approved, but we'll approve it first.
        self.transaction.is_approved = True
        self.transaction.save()

        response = self.client.post(reverse('inwardtransaction_delete', args=[self.transaction.pk]), HTTP_HX_REQUEST='true')
        
        # Expect 403 Forbidden with HX-Trigger to close modal
        self.assertEqual(response.status_code, 403)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'closeModal')
        
        # Verify object was NOT deleted
        self.assertTrue(InwardTransaction.objects.filter(pk=self.transaction.pk).exists())
        self.assertContains(response, 'Inward Transaction cannot be deleted as it is already approved.')

```

---

## Step 5: HTMX and Alpine.js Integration

**Summary of Implementation:**

*   **HTMX for Dynamic Content:**
    *   The main `list.html` uses `hx-get="{% url 'inwardtransaction_table' %}"` on the `#inwardtransactionTable-container` div with `hx-trigger="load, refreshInwardTransactionList from:body"`. This ensures the table content is loaded initially and automatically refreshes whenever a `refreshInwardTransactionList` custom event is triggered (e.g., after a successful create, update, or delete).
    *   Add/Edit/Delete buttons use `hx-get` to fetch partial templates (`_inwardtransaction_form.html` or `_inwardtransaction_confirm_delete.html`) into the `#modalContent` div.
    *   Form submissions within the modal use `hx-post` with `hx-swap="none"`. Upon successful submission, Django views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshInwardTransactionList'})`, which tells HTMX to do nothing (`none` swap) but trigger the specified event, causing the main table to reload.
    *   Form validation errors are handled by HTMX gracefully: if `form.is_valid()` fails, the view re-renders the form partial with errors, and HTMX swaps it back into the modal.
    *   A loading spinner (`htmx-indicator`) is included for visual feedback during form submissions.

*   **Alpine.js for UI State Management:**
    *   While explicit `x-data` components were not strictly necessary for modal visibility, the `_` (Hyperscript) syntax for `on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me` is used to toggle modal visibility based on clicks. This is a compact, declarative way to manage UI state similar to Alpine.js.
    *   A custom `closeModal` event is triggered if deletion fails, to ensure the modal closes gracefully.

*   **DataTables for List Views:**
    *   The `_inwardtransaction_table.html` partial contains the HTML `<table>` structure.
    *   A `$(document).ready(function() { $('#inwardtransactionTable').DataTable({...}); });` script within this partial initializes DataTables. This script is executed every time the partial is loaded via HTMX, ensuring the table is always a functional DataTables instance. We include a `destroy()` call to prevent re-initialization errors.

*   **No Full Page Reloads:** All CRUD interactions (add, edit, delete) are performed without a full page refresh, providing a smooth, modern user experience.

---

## Final Notes

This comprehensive plan transforms the bare ASP.NET dashboard page into a fully functional, modern Django application.

-   **Business Value:** This approach significantly improves user experience with dynamic interfaces, reduces server load by avoiding full page reloads, and modernizes the technology stack for better maintainability, scalability, and security. The "Fat Model, Thin View" architecture ensures business logic is centralized and testable, leading to a more robust application.
-   **Automation Potential:** The structure provided (models, forms, views, templates, URLs, tests) is highly standardized. Tools, potentially powered by AI, can be developed to parse legacy database schemas and UI definitions (even if inferred) to automate the generation of these Django components, drastically reducing manual migration effort and ensuring consistency across different modules. For instance, a script could read database table definitions and automatically scaffold `models.py` and initial `forms.py` and `urls.py` entries. Similarly, a tool could analyze ASP.NET GridView definitions to generate the corresponding DataTables HTML and associated view logic.
-   **DRY Principles:** Emphasized through template inheritance, partials, and logic centralization in models.
-   **Test Coverage:** Extensive tests are included to ensure reliability and maintainability, crucial for a robust migration.

This plan provides a clear, actionable roadmap for migrating `Inward_DashBoard` to a modern Django application, focusing on efficiency, maintainability, and user experience.