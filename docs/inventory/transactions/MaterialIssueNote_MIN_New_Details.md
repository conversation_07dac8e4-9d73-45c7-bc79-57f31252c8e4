This comprehensive plan outlines the migration of your ASP.NET Material Issue Note (MIN) management module to a modern Django application, emphasizing automation, clear communication, and robust architecture. Our approach focuses on delivering business value by streamlining operations, improving system reliability, and setting a foundation for future development with cutting-edge technologies.

This plan details each step of the conversion, breaking down the complex ASP.NET and C# code into modular, maintainable Django components. We will leverage Django's "Fat Model, Thin View" paradigm, HTMX for dynamic interactions, Alpine.js for UI state management, and DataTables for superior data presentation, ensuring a highly performant and user-friendly experience.

---

## ASP.NET to Django Conversion Script: Material Issue Note [MIN] - New Details

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:** The ASP.NET code interacts with several tables, primarily to display material requisition details and process material issue notes.

**Identified Tables and Columns:**

*   **`tblInv_MaterialRequisition_Master` (maps to `MaterialRequisitionMaster` model):**
    *   `Id` (Primary Key, Integer)
    *   `MRSNo` (String, Material Requisition Number)
    *   `CompId` (Integer, Company ID)
    *   `FinYearId` (Integer, Financial Year ID)

*   **`tblInv_MaterialRequisition_Details` (maps to `MaterialRequisitionDetail` model):**
    *   `Id` (Primary Key, Integer)
    *   `MId` (Foreign Key to `MaterialRequisitionMaster.Id`, Integer)
    *   `ItemId` (Foreign Key to `ItemMaster.Id`, Integer)
    *   `DeptId` (Foreign Key to `BusinessGroup.Id`, Integer)
    *   `WONo` (String, Work Order Number)
    *   `ReqQty` (Float/Double, Required Quantity)
    *   `Remarks` (String)

*   **`tblDG_Item_Master` (maps to `ItemMaster` model):**
    *   `Id` (Primary Key, Integer)
    *   `CompId` (Integer, Company ID)
    *   `ItemCode` (String)
    *   `ManfDesc` (String, Manufacturer Description / Item Description)
    *   `UOMBasic` (Foreign Key to `UnitMaster.Id`, Integer, Unit of Measure)
    *   `StockQty` (Float/Double, Current Stock Quantity)

*   **`Unit_Master` (maps to `UnitMaster` model):**
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (String, Unit Symbol like 'KG', 'PCS')

*   **`BusinessGroup` (maps to `BusinessGroup` model):**
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (String, Department/Business Group Symbol)

*   **`tblInv_MaterialIssue_Master` (maps to `MaterialIssueMaster` model):**
    *   `Id` (Primary Key, Integer)
    *   `MINNo` (String, Material Issue Note Number)
    *   `MRSNo` (String, Material Requisition Number, copied from master)
    *   `MRSId` (Foreign Key to `MaterialRequisitionMaster.Id`, Integer)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `SessionId` (String, User Session ID)
    *   `SysDate` (Date)
    *   `SysTime` (Time)

*   **`tblInv_MaterialIssue_Details` (maps to `MaterialIssueDetail` model):**
    *   `Id` (Primary Key, Integer)
    *   `MId` (Foreign Key to `MaterialIssueMaster.Id`, Integer)
    *   `MRSId` (Foreign Key to `MaterialRequisitionDetail.Id`, Integer)
    *   `IssueQty` (Float/Double, Issued Quantity)

*   **`tblCompany_master` (maps to `CompanyMaster` model):**
    *   `CompId` (Primary Key, Integer)
    *   `MailServerIp` (String)
    *   `ErpSysmail` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**

*   **Read (Display List):** The `loadgrid()` method is responsible for retrieving `MaterialRequisitionDetail` records based on `CompId` and `MId` (Material Requisition Master ID, passed via query string). It enriches this data by fetching related `ItemMaster` details (ItemCode, Description, UOM, Stock Quantity), `BusinessGroup` (Department), and `UnitMaster` (UOM Symbol). Crucially, it also calculates the `IssuedQty` for each requisition detail by summing up existing `MaterialIssueDetail` records.
*   **Create/Update (Generate MIN Transaction):** The `btnProceed_Click` method orchestrates the "Generate MIN" transaction.
    1.  **MIN Number Generation:** It generates a new unique `MINNo` for the `MaterialIssueMaster` record.
    2.  **Item Selection & Quantity Calculation:** It iterates through selected (checked) items from the displayed grid. For each selected item, it calculates the `IssueQty` (the lesser of remaining `ReqQty` and available `StockQty`) and the `BalStkQty` (remaining stock after issue).
    3.  **Record Creation:** It creates a new `MaterialIssueMaster` record (once per transaction) and multiple `MaterialIssueDetail` records (one for each issued item).
    4.  **Stock Update:** It updates the `StockQty` in `ItemMaster` for each issued item.
    5.  **Discrepancy Notification:** It checks for stock discrepancies (e.g., if the `StockQty` wasn't updated as expected) and sends an email notification if any are found.
*   **Navigation:** The `btnCancel_Click` redirects the user back to a previous list page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Data Display:** `GridView3` serves as the primary data display component. It shows a list of Material Requisition Details, including Item Code, Description, UOM, Stock Qty, Required Qty, Issued Qty, and Remarks. It includes a checkbox for each row, allowing users to select items for processing.
*   **Actions:**
    *   `btnProceed` ("Generate MIN"): Triggers the material issue transaction for selected items. It includes a client-side confirmation (`confirmationAdd()`).
    *   `btnCancel` ("Cancel"): Navigates back to the main Material Issue Note list.
*   **Dynamic UI:** `UpdateProgress` and `ModalPopupExtender` from `AjaxControlToolkit` are used to display a loading spinner during asynchronous postbacks. This will be replaced by HTMX's `hx-indicator` or Alpine.js for a more modern experience.
*   **Client-Side Scripting:** Minimal JavaScript is used for the loading indicator and a `confirmationAdd()` function (which will be integrated into HTMX flow or Alpine.js).

---

### Step 4: Generate Django Code

We will create a Django application named `inventory` for this module.

#### 4.1 Models (`inventory/models.py`)

We'll define models for all identified tables, ensuring they map correctly to the existing database schema using `managed = False` and `db_table`. The core business logic for generating MINs and updating stock will reside within the `MaterialIssueMaster` and `ItemMaster` models, following the fat model principle.

```python
from django.db import models, transaction
from django.db.models import Sum, F
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or f"Business Group {self.id}"

class ItemMasterManager(models.Manager):
    def update_stock(self, item_id, quantity_issued):
        """
        Atomically updates the stock quantity for a given item.
        Returns True if successful, False otherwise (e.g., insufficient stock).
        """
        try:
            with transaction.atomic():
                item = self.select_for_update().get(id=item_id)
                if item.stock_qty >= quantity_issued:
                    item.stock_qty = F('StockQty') - quantity_issued
                    item.save(update_fields=['stock_qty'])
                    item.refresh_from_db() # Get updated stock_qty after F() expression
                    return True, item.stock_qty
                return False, item.stock_qty # Not enough stock
        except ItemMaster.DoesNotExist:
            logger.error(f"Item with ID {item_id} not found for stock update.")
            return False, 0
        except Exception as e:
            logger.error(f"Error updating stock for item {item_id}: {e}")
            return False, 0

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=200, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', blank=True, null=True)

    objects = ItemMasterManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.manf_desc or f"Item {self.id}"

class MaterialRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'
        verbose_name = 'Material Requisition Master'
        verbose_name_plural = 'Material Requisition Masters'

    def __str__(self):
        return self.mrs_no or f"MRS Master {self.id}"

class MaterialRequisitionDetailManager(models.Manager):
    def get_details_with_related_data(self, mrs_master_id, company_id):
        """
        Retrieves Material Requisition Details along with related item, UOM,
        department, and calculated issued quantities, similar to ASP.NET loadgrid().
        """
        queryset = self.filter(
            mid=mrs_master_id,
            materialrequisitionmaster__comp_id=company_id
        ).select_related('itemid', 'itemid__uom_basic', 'deptid') # Eager load relationships

        results = []
        for detail in queryset:
            issued_qty_sum = MaterialIssueDetail.objects.filter(
                mrs_id=detail.id
            ).aggregate(Sum('issue_qty'))['issue_qty__sum'] or 0

            item = detail.itemid
            department = detail.deptid

            results.append({
                'id': detail.id,
                'item_id': item.id if item else None,
                'item_code': item.item_code if item else 'N/A',
                'manf_desc': item.manf_desc if item else 'N/A',
                'uom_basic': item.uom_basic.symbol if item and item.uom_basic else 'N/A',
                'stock_qty': item.stock_qty if item else 0.0,
                'dept': department.symbol if department else 'N/A',
                'wo_no': detail.wo_no if detail.wo_no else 'NA',
                'req_qty': detail.req_qty or 0.0,
                'remarks': detail.remarks or '',
                'issued_qty': issued_qty_sum,
                'remaining_req_qty': detail.req_qty - issued_qty_sum,
            })
        return results

class MaterialRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(MaterialRequisitionMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    itemid = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    deptid = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    req_qty = models.FloatField(db_column='ReqQty', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    objects = MaterialRequisitionDetailManager()

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'
        verbose_name = 'Material Requisition Detail'
        verbose_name_plural = 'Material Requisition Details'

    def __str__(self):
        return f"MRS Detail {self.id} for {self.mid.mrs_no if self.mid else 'N/A'}"

class MaterialIssueMasterManager(models.Manager):
    def generate_min_no(self, comp_id, fin_year_id):
        """Generates the next MINNo based on the last one in the database."""
        last_min = self.filter(comp_id=comp_id, fin_year_id=fin_year_id).order_by('-min_no').first()
        if last_min and last_min.min_no.isdigit():
            next_min_int = int(last_min.min_no) + 1
            return f"{next_min_int:04d}"
        return "0001"

    @transaction.atomic
    def process_material_issue(self, mrs_master_id, mrs_no, company_id, fin_year_id, session_id, selected_items_data):
        """
        Processes the material issue transaction, creating MIN records and updating stock.
        selected_items_data: A list of dictionaries, each with 'mrs_detail_id', 'item_id', 'req_qty', 'issued_qty_prev', 'stock_qty'.
        """
        current_date = timezone.localdate()
        current_time = timezone.localtime().time()

        min_no = self.generate_min_no(company_id, fin_year_id)

        material_issue_master = self.create(
            sys_date=current_date,
            sys_time=current_time,
            comp_id=company_id,
            fin_year_id=fin_year_id,
            session_id=session_id,
            min_no=min_no,
            mrs_no=mrs_no,
            mrs_id=MaterialRequisitionMaster.objects.get(id=mrs_master_id)
        )

        discrepancy_items = []
        for item_data in selected_items_data:
            mrs_detail_id = item_data['mrs_detail_id']
            item_id = item_data['item_id']
            req_qty_remaining = item_data['req_qty'] - item_data['issued_qty_prev'] # This is the "Req. Qty" shown in UI
            current_stock_qty_from_ui = item_data['stock_qty'] # This is "Stk Qty" from UI

            # Re-fetch item to get the most current stock_qty to avoid race conditions
            # The atomic transaction with select_for_update in ItemMasterManager.update_stock handles this.
            item_obj = ItemMaster.objects.get(id=item_id)
            actual_stock_before_issue = item_obj.stock_qty

            issue_qty = 0.0
            if actual_stock_before_issue > 0 and req_qty_remaining > 0:
                if actual_stock_before_issue >= req_qty_remaining:
                    issue_qty = req_qty_remaining
                else:
                    issue_qty = actual_stock_before_issue

                # Update stock
                success, new_stock_qty = ItemMaster.objects.update_stock(item_id, issue_qty)

                if success:
                    # Create MaterialIssueDetail record
                    MaterialIssueDetail.objects.create(
                        mid=material_issue_master,
                        min_no=min_no, # Redundant, MId links to MINNo, but matching old schema
                        mrs_id=MaterialRequisitionDetail.objects.get(id=mrs_detail_id),
                        issue_qty=issue_qty
                    )
                    # Check for discrepancies based on stock before and after issue
                    # The original code's discrepancy check was very specific and confusing (StkQty2 - BalStkQty != 0)
                    # A more robust check would be to verify the actual stock update.
                    # Here, we can compare current_stock_qty_from_ui vs actual_stock_before_issue if needed for log.
                    # Or verify if the final stock matches expected
                    if (actual_stock_before_issue - issue_qty) != new_stock_qty:
                        discrepancy_items.append({
                            'item_code': item_obj.item_code,
                            'initial_stock': actual_stock_before_issue,
                            'issued_qty': issue_qty,
                            'final_stock_after_update': new_stock_qty,
                            'expected_final_stock': actual_stock_before_issue - issue_qty
                        })
                else:
                    logger.warning(f"Failed to issue {issue_qty} for item {item_id} due to insufficient stock or DB error.")

        if discrepancy_items:
            self._send_discrepancy_email(company_id, discrepancy_items)

        return True, material_issue_master.min_no

    def _send_discrepancy_email(self, company_id, discrepancy_items):
        """Sends an email notification about stock discrepancies."""
        try:
            company = CompanyMaster.objects.get(comp_id=company_id)
            smtp_server = company.mail_server_ip
            erp_mail = company.erp_sysmail
            recipient_email = "<EMAIL>" # Hardcoded as in original ASP.NET code

            subject = "Stock Tracing If wrong operation in MIN"
            body_parts = ["Dear Sir, This is Auto generated mail by ERP system, please do not reply.<br><br> Thank you.<br><br>"]
            for item in discrepancy_items:
                body_parts.append(
                    f"ItemCode: {item['item_code']}; "
                    f"Initial Stock: {item['initial_stock']}; "
                    f"Issued Qty: {item['issued_qty']}; "
                    f"Final Stock After Update: {item['final_stock_after_update']}; "
                    f"Expected Final Stock: {item['expected_final_stock']}<br>"
                )
            body = "".join(body_parts)

            send_mail(
                subject,
                body,
                erp_mail, # From email
                [recipient_email],
                html_message=body,
                fail_silently=False,
                connection=None, # Use default connection, or configure specific SMTP settings if needed
            )
            logger.info(f"Discrepancy email sent for company {company_id}.")
        except CompanyMaster.DoesNotExist:
            logger.error(f"Company {company_id} not found for email configuration.")
        except Exception as e:
            logger.error(f"Error sending discrepancy email: {e}")

class MaterialIssueMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    min_no = models.CharField(db_column='MINNo', max_length=50, blank=True, null=True)
    mrs_no = models.CharField(db_column='MRSNo', max_length=50, blank=True, null=True)
    mrs_id = models.ForeignKey(MaterialRequisitionMaster, models.DO_NOTHING, db_column='MRSId', blank=True, null=True)

    objects = MaterialIssueMasterManager()

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'
        verbose_name = 'Material Issue Master'
        verbose_name_plural = 'Material Issue Masters'

    def __str__(self):
        return self.min_no or f"MIN Master {self.id}"

class MaterialIssueDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(MaterialIssueMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    min_no = models.CharField(db_column='MINNo', max_length=50, blank=True, null=True) # Redundant but kept for schema match
    mrs_id = models.ForeignKey(MaterialRequisitionDetail, models.DO_NOTHING, db_column='MRSId', blank=True, null=True)
    issue_qty = models.FloatField(db_column='IssueQty', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Details'
        verbose_name = 'Material Issue Detail'
        verbose_name_plural = 'Material Issue Details'

    def __str__(self):
        return f"MIN Detail {self.id} (Issued: {self.issue_qty})"

class CompanyMaster(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    mail_server_ip = models.CharField(db_column='MailServerIp', max_length=100, blank=True, null=True)
    erp_sysmail = models.CharField(db_column='ErpSysmail', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

    def __str__(self):
        return f"Company {self.comp_id}"

```

#### 4.2 Forms (`inventory/forms.py`)

For the "Generate MIN" action, we don't need a typical `ModelForm` as we're processing a selection from a table. Instead, we can define a simple form to handle the submission of selected items, or process the data directly in the view. Given the complexity of the data (multiple items with quantities), it's better to use `request.POST` directly in the view and validate/process in the model method.

For a formal representation, we can define a dummy form to handle the POST request if required for `FormView`, but for simple processing, a basic `forms.Form` or direct data extraction in view is sufficient. Let's make a `Form` for the "Generate MIN" action.

```python
from django import forms
from django.core.exceptions import ValidationError

class GenerateMinForm(forms.Form):
    # This form doesn't represent a single model, but rather
    # the data submitted from the grid for material issue.
    # The actual selected items (IDs and quantities) will be
    # submitted via HTMX and processed in the view.
    # This form primarily acts as a placeholder for a POST request.

    # A hidden field could be used to pass a token or context,
    # but actual item data will be in dynamic fields.
    mrs_master_id = forms.IntegerField(widget=forms.HiddenInput())
    mrs_no = forms.CharField(widget=forms.HiddenInput())
    
    # We will expect selected_items to be a list of dictionaries in POST.
    # Form processing might involve custom clean methods or direct view logic.

    def clean(self):
        cleaned_data = super().clean()
        # Custom validation logic for selected items can go here.
        # For simplicity, we'll assume item data is validated in the model manager.
        return cleaned_data
```

#### 4.3 Views (`inventory/views.py`)

We'll define two main views: one for displaying the list of material requisition details and a `ProcessView` for handling the "Generate MIN" action. We'll also need a partial view to render just the DataTables content for HTMX swaps.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.db import transaction
import json
import logging

from .models import MaterialRequisitionMaster, MaterialRequisitionDetail, MaterialIssueMaster, ItemMaster
from .forms import GenerateMinForm

logger = logging.getLogger(__name__)

# Helper to get current user/company/fin_year (replace with actual session management)
# In a real application, these would come from authentication and user profiles.
def get_session_data(request):
    # Mocking session data as in original ASP.NET code
    # In a real Django app, use request.user and profile for CompId, FinYearId, SessionId
    # Or, if these are truly per-session, store them after login.
    comp_id = request.session.get('compid', 1)  # Default to 1 for demonstration
    session_id = request.session.get('username', 'system_user')
    fin_year_id = request.session.get('finyear', 2023) # Default to 2023 for demonstration
    return comp_id, session_id, fin_year_id

class MaterialRequisitionDetailListView(TemplateView):
    """
    Displays the list of Material Requisition Details.
    Corresponds to the initial load of MaterialIssueNote_MIN_New_Details.aspx.
    """
    template_name = 'inventory/materialrequisitiondetail/min_new_details_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Extract MRS Master ID and MRS No from query parameters
        mrs_master_id = self.request.GET.get('Id')
        mrs_no = self.request.GET.get('mrsno')
        fyid = self.request.GET.get('fyid') # Not directly used in template, but good to pass context

        # Add these to context for use in the template (e.g., for hidden fields)
        context['mrs_master_id'] = mrs_master_id
        context['mrs_no'] = mrs_no
        context['fyid'] = fyid
        context['generate_min_form'] = GenerateMinForm(initial={'mrs_master_id': mrs_master_id, 'mrs_no': mrs_no})
        return context

class MaterialRequisitionDetailTablePartialView(View):
    """
    Renders only the table rows for Material Requisition Details.
    Designed for HTMX to swap in new content without full page reload.
    """
    def get(self, request, *args, **kwargs):
        mrs_master_id = request.GET.get('mrs_master_id')
        comp_id, _, _ = get_session_data(request)

        if not mrs_master_id:
            return HttpResponse("<p class='text-red-500'>Material Requisition ID is missing.</p>", status=400)

        try:
            mrs_master = MaterialRequisitionMaster.objects.get(id=mrs_master_id, comp_id=comp_id)
            # Use the custom manager method to get aggregated data
            requisition_details = MaterialRequisitionDetail.objects.get_details_with_related_data(
                mrs_master_id=mrs_master.id,
                company_id=comp_id
            )

            # Filter items to hide checkboxes if remaining_req_qty is 0 or stock_qty is 0
            # This logic is applied *after* data retrieval as it affects UI display.
            for detail in requisition_details:
                detail['show_checkbox'] = detail['remaining_req_qty'] > 0 and detail['stock_qty'] > 0

            context = {
                'requisition_details': requisition_details,
                'mrs_master_id': mrs_master_id, # Pass for HTMX Post
            }
            return render(request, 'inventory/materialrequisitiondetail/_min_new_details_table.html', context)
        except MaterialRequisitionMaster.DoesNotExist:
            return HttpResponse(f"<p class='text-red-500'>Material Requisition with ID {mrs_master_id} not found.</p>", status=404)
        except Exception as e:
            logger.error(f"Error loading requisition details table: {e}")
            return HttpResponse("<p class='text-red-500'>An error occurred while loading data.</p>", status=500)


class MaterialIssueNoteGenerateView(View):
    """
    Handles the 'Generate MIN' action.
    Corresponds to btnProceed_Click in ASP.NET.
    Receives selected items via HTMX POST.
    """
    def post(self, request, *args, **kwargs):
        # The form is just for CSRF token and base context.
        # Actual data comes from JSON payload or form data fields named like array.
        mrs_master_id = request.POST.get('mrs_master_id')
        mrs_no = request.POST.get('mrs_no')
        selected_items_json = request.POST.get('selected_items_data') # Expecting JSON string from JS

        if not mrs_master_id or not selected_items_json:
            messages.error(request, "Invalid request: Missing Material Requisition data or selected items.")
            return HttpResponse(status=400, headers={'HX-Refresh': 'true'}) # Trigger full refresh to show error

        try:
            selected_items_data = json.loads(selected_items_json)
        except json.JSONDecodeError:
            messages.error(request, "Invalid data format for selected items.")
            return HttpResponse(status=400, headers={'HX-Refresh': 'true'})

        comp_id, session_id, fin_year_id = get_session_data(request)

        # Ensure selected_items_data is not empty
        if not selected_items_data:
            messages.info(request, "No items selected for Material Issue Note generation.")
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshMINList'}) # No content, but trigger refresh

        try:
            success, min_number = MaterialIssueMaster.objects.process_material_issue(
                mrs_master_id=mrs_master_id,
                mrs_no=mrs_no,
                company_id=comp_id,
                fin_year_id=fin_year_id,
                session_id=session_id,
                selected_items_data=selected_items_data
            )

            if success:
                messages.success(request, f"Material Issue Note {min_number} generated successfully.")
                # Redirect to the main MIN list page as in ASP.NET
                # Or, if staying on details page, refresh content
                # Original redirected to MaterialIssueNote_MIN_New.aspx
                # Assuming `min_list` is the target URL.
                return HttpResponse(status=204, headers={'HX-Redirect': reverse_lazy('inventory:min_list')})
            else:
                messages.error(request, "Failed to generate Material Issue Note.")
                return HttpResponse(status=500, headers={'HX-Refresh': 'true'})

        except MaterialRequisitionMaster.DoesNotExist:
            messages.error(request, f"Material Requisition Master with ID {mrs_master_id} not found.")
            return HttpResponse(status=404, headers={'HX-Refresh': 'true'})
        except Exception as e:
            logger.exception("Error during Material Issue Note generation.")
            messages.error(request, f"An unexpected error occurred: {e}. Please contact support.")
            return HttpResponse(status=500, headers={'HX-Refresh': 'true'})

class MaterialIssueNoteCancelView(View):
    """
    Handles the 'Cancel' action, redirecting to the main MIN list page.
    Corresponds to btnCancel_Click in ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        # Redirect to the main MIN list page
        # Original redirected to MaterialIssueNote_MIN_New.aspx
        return redirect(reverse_lazy('inventory:min_list'))

```

#### 4.4 Templates

We'll create two templates: one for the main page (`min_new_details_list.html`) and one for the partial table content (`_min_new_details_table.html`).

**`inventory/templates/inventory/materialrequisitiondetail/min_new_details_list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Material Issue Note [MIN] - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col items-center justify-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Issue Note [MIN] - New</h2>
        <p class="text-sm text-gray-600">Requisition No: {{ mrs_no }} (ID: {{ mrs_master_id }})</p>
    </div>

    <!-- Loading Indicator -->
    <div id="loading-indicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        <p class="ml-4 text-white text-lg">Processing...</p>
    </div>

    <div id="min-details-table-container"
         hx-trigger="load, refreshMINDetailsTable from:body"
         hx-get="{% url 'inventory:min_details_table' %}?mrs_master_id={{ mrs_master_id }}"
         hx-swap="innerHTML"
         hx-indicator="#loading-indicator">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="ml-2 text-gray-600">Loading Material Requisition Details...</p>
        </div>
    </div>
    
    <div class="mt-6 flex justify-center items-center space-x-4">
        <button id="btnProceed"
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md"
            hx-post="{% url 'inventory:min_generate' %}"
            hx-trigger="click"
            hx-confirm="Are you sure you want to generate the Material Issue Note for selected items?"
            hx-include="#min-details-table-container" {# Include form data from the table checkboxes #}
            hx-indicator="#loading-indicator"
            hx-swap="none"
            _="on click call submitGenerateMinForm()"
            >
            Generate MIN
        </button>
        <button
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-md"
            hx-get="{% url 'inventory:min_cancel' %}"
            hx-swap="outerHTML"
            hx-target="body"> {# Redirects, so swap body #}
            Cancel
        </button>
    </div>

    <!-- Hidden form for submitting data via HTMX Post -->
    <form id="generateMinForm" method="post" class="hidden">
        {% csrf_token %}
        {{ generate_min_form.mrs_master_id }}
        {{ generate_min_form.mrs_no }}
        <input type="hidden" name="selected_items_data" id="selectedItemsData">
    </form>

</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">

<script>
    // Alpine.js is assumed to be included in base.html
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed here,
        // as HTMX handles interactions and DataTables handles table functionality.
    });

    function submitGenerateMinForm() {
        const checkedRows = document.querySelectorAll('#minDetailsTable tbody input[type="checkbox"]:checked');
        const selectedItems = [];
        checkedRows.forEach(checkbox => {
            const row = checkbox.closest('tr');
            selectedItems.push({
                mrs_detail_id: row.dataset.mrsDetailId,
                item_id: row.dataset.itemId,
                req_qty: parseFloat(row.dataset.reqQty),
                issued_qty_prev: parseFloat(row.dataset.issuedQty),
                stock_qty: parseFloat(row.dataset.stockQty)
            });
        });

        const selectedItemsDataInput = document.getElementById('selectedItemsData');
        selectedItemsDataInput.value = JSON.stringify(selectedItems);

        // Manually trigger the hx-post on the button.
        // HTMX handles form inclusion with hx-include.
        // The important part is that `selectedItemsDataInput` has the correct JSON.
    }

    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'min-details-table-container') {
            // Reinitialize DataTable after HTMX swaps in new table content
            if ($.fn.DataTable.isDataTable('#minDetailsTable')) {
                $('#minDetailsTable').DataTable().destroy(); // Destroy previous instance if any
            }
            $('#minDetailsTable').DataTable({
                "pageLength": 15, // As in original ASP.NET GridView
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "language": {
                    "emptyTable": "No data to display !"
                }
            });
        }
    });

</script>
{% endblock %}
```

**`inventory/templates/inventory/materialrequisitiondetail/_min_new_details_table.html`**

```html
<table id="minDetailsTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg">
    <thead>
        <tr class="bg-gray-100 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">
            <th class="py-3 px-4 border-b border-gray-200 rounded-tl-lg">SN</th>
            <th class="py-3 px-4 border-b border-gray-200">Select</th>
            <th class="py-3 px-4 border-b border-gray-200">Item Code</th>
            <th class="py-3 px-4 border-b border-gray-200">Description</th>
            <th class="py-3 px-4 border-b border-gray-200">UOM</th>
            <th class="py-3 px-4 border-b border-gray-200">BG Group</th>
            <th class="py-3 px-4 border-b border-gray-200">WO No</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right">Stk Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right">Req. Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right">Issued Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 rounded-tr-lg">Remarks</th>
        </tr>
    </thead>
    <tbody>
        {% if requisition_details %}
            {% for detail in requisition_details %}
            <tr class="hover:bg-gray-50"
                data-mrs-detail-id="{{ detail.id }}"
                data-item-id="{{ detail.item_id }}"
                data-req-qty="{{ detail.req_qty }}"
                data-issued-qty="{{ detail.issued_qty }}"
                data-stock-qty="{{ detail.stock_qty }}">
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if detail.show_checkbox %}
                        <input type="checkbox" name="selected_items" value="{{ detail.id }}" 
                            class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.manf_desc }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.uom_basic }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.dept }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ detail.wo_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.stock_qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.req_qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ detail.issued_qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ detail.remarks }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="11" class="py-4 px-4 text-center text-lg text-maroon-700 font-semibold">No data to display !</td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // This script block will be executed after HTMX swaps the content.
    // The DataTables initialization is handled in min_new_details_list.html
    // with a htmx:afterSwap event listener, ensuring it runs only once
    // per content swap and properly re-initializes the table.
</script>
```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for accessing these views.

```python
from django.urls import path
from .views import MaterialRequisitionDetailListView, MaterialRequisitionDetailTablePartialView, MaterialIssueNoteGenerateView, MaterialIssueNoteCancelView

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    # Main page for Material Issue Note details based on Material Requisition
    path('materialissuenote/new-details/', MaterialRequisitionDetailListView.as_view(), name='min_new_details'),
    
    # HTMX endpoint for the table content (partial update)
    path('materialissuenote/new-details/table/', MaterialRequisitionDetailTablePartialView.as_view(), name='min_details_table'),

    # Endpoint for generating the Material Issue Note (POST request)
    path('materialissuenote/generate/', MaterialIssueNoteGenerateView.as_view(), name='min_generate'),

    # Endpoint for the Cancel action
    path('materialissuenote/cancel/', MaterialIssueNoteCancelView.as_view(), name='min_cancel'),

    # Placeholder for the actual Material Issue Note List page (redirect target)
    # This URL should exist in your project's main urls.py or another app's urls.py
    # For now, we'll assume a dummy path, replace with your actual list view URL.
    path('materialissuenote/', MaterialIssueNoteCancelView.as_view(), name='min_list'), # Placeholder for the list view
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection
from unittest.mock import patch, MagicMock
from datetime import date, time
import json

from .models import (
    UnitMaster, BusinessGroup, ItemMaster, MaterialRequisitionMaster,
    MaterialRequisitionDetail, MaterialIssueMaster, MaterialIssueDetail,
    CompanyMaster
)

class MaterialIssueNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Ensure tables are empty before creating test data, as they are unmanaged
        with connection.cursor() as cursor:
            cursor.execute("TRUNCATE TABLE Unit_Master")
            cursor.execute("TRUNCATE TABLE BusinessGroup")
            cursor.execute("TRUNCATE TABLE tblDG_Item_Master")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialRequisition_Master")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialRequisition_Details")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialIssue_Master")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialIssue_Details")
            cursor.execute("TRUNCATE TABLE tblCompany_master")

        # Create dummy data for related models
        cls.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.dept_prod = BusinessGroup.objects.create(id=10, symbol='PROD')
        cls.item1 = ItemMaster.objects.create(id=101, comp_id=1, item_code='ITEM001', manf_desc='Widget A', uom_basic=cls.unit_pcs, stock_qty=100.0)
        cls.item2 = ItemMaster.objects.create(id=102, comp_id=1, item_code='ITEM002', manf_desc='Gadget B', uom_basic=cls.unit_pcs, stock_qty=50.0)
        cls.mrs_master = MaterialRequisitionMaster.objects.create(id=1, mrs_no='MRS001', comp_id=1, fin_year_id=2023)
        cls.mrs_master_2 = MaterialRequisitionMaster.objects.create(id=2, mrs_no='MRS002', comp_id=1, fin_year_id=2023)
        cls.company = CompanyMaster.objects.create(comp_id=1, mail_server_ip='smtp.example.com', erp_sysmail='<EMAIL>')

        # Create initial Material Requisition Details
        cls.mrs_detail_1 = MaterialRequisitionDetail.objects.create(id=1001, mid=cls.mrs_master, itemid=cls.item1, deptid=cls.dept_prod, wo_no='WO-ABC', req_qty=20.0, remarks='For production')
        cls.mrs_detail_2 = MaterialRequisitionDetail.objects.create(id=1002, mid=cls.mrs_master, itemid=cls.item2, deptid=cls.dept_prod, wo_no='WO-DEF', req_qty=60.0, remarks='For testing')
        cls.mrs_detail_no_stock = MaterialRequisitionDetail.objects.create(id=1003, mid=cls.mrs_master, itemid=cls.item1, deptid=cls.dept_prod, wo_no='WO-XYZ', req_qty=10.0, remarks='Item with no stock')
        # Simulate an item with 0 stock
        ItemMaster.objects.filter(id=cls.item1.id).update(stock_qty=0.0) # Update item1 for mrs_detail_no_stock
        cls.item1.refresh_from_db()

        # Simulate existing issue for mrs_detail_1
        cls.existing_min_master = MaterialIssueMaster.objects.create(id=1, sys_date=date.today(), sys_time=time(10,0), comp_id=1, fin_year_id=2023, session_id='testuser', min_no='0001', mrs_no='MRS001', mrs_id=cls.mrs_master)
        MaterialIssueDetail.objects.create(id=1, mid=cls.existing_min_master, mrs_id=cls.mrs_detail_1, issue_qty=5.0)

    def test_unit_master_creation(self):
        unit = UnitMaster.objects.get(id=1)
        self.assertEqual(unit.symbol, 'PCS')

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=101)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.stock_qty, 0.0) # After update for no_stock test

    def test_item_master_update_stock_success(self):
        item = ItemMaster.objects.get(id=102)
        initial_stock = item.stock_qty # 50.0
        success, new_stock = ItemMaster.objects.update_stock(item.id, 10.0)
        self.assertTrue(success)
        self.assertEqual(new_stock, initial_stock - 10.0)
        item.refresh_from_db()
        self.assertEqual(item.stock_qty, 40.0)

    def test_item_master_update_stock_insufficient(self):
        item = ItemMaster.objects.get(id=102)
        initial_stock = item.stock_qty # 40.0 now
        success, new_stock = ItemMaster.objects.update_stock(item.id, 100.0)
        self.assertFalse(success)
        self.assertEqual(new_stock, initial_stock) # Stock should not change

    def test_material_requisition_detail_get_details_with_related_data(self):
        details = MaterialRequisitionDetail.objects.get_details_with_related_data(
            self.mrs_master.id, self.mrs_master.comp_id
        )
        self.assertEqual(len(details), 3)

        # Check detail 1 (ITEM001) - req_qty 20.0, issued_qty_prev 5.0
        detail1_data = next(d for d in details if d['id'] == self.mrs_detail_1.id)
        self.assertEqual(detail1_data['item_code'], 'ITEM001')
        self.assertEqual(detail1_data['req_qty'], 20.0)
        self.assertEqual(detail1_data['issued_qty'], 5.0)
        self.assertEqual(detail1_data['remaining_req_qty'], 15.0) # 20 - 5
        self.assertEqual(detail1_data['stock_qty'], 0.0) # Item1 stock was set to 0.0
        self.assertFalse(detail1_data['show_checkbox']) # Because stock is 0

        # Check detail 2 (ITEM002) - req_qty 60.0, issued_qty_prev 0.0 (initial)
        detail2_data = next(d for d in details if d['id'] == self.mrs_detail_2.id)
        self.assertEqual(detail2_data['item_code'], 'ITEM002')
        self.assertEqual(detail2_data['req_qty'], 60.0)
        self.assertEqual(detail2_data['issued_qty'], 0.0)
        self.assertEqual(detail2_data['remaining_req_qty'], 60.0)
        # Note: Item2 stock was 50.0, then reduced by 10.0 in previous test to 40.0.
        self.assertEqual(detail2_data['stock_qty'], 40.0)
        self.assertTrue(detail2_data['show_checkbox']) # Stock > 0 and req_qty > 0

    def test_material_issue_master_generate_min_no(self):
        min_no = MaterialIssueMaster.objects.generate_min_no(self.mrs_master.comp_id, self.mrs_master.fin_year_id)
        self.assertEqual(min_no, '0002') # Starts from 0001, 1 already created in setup

        MaterialIssueMaster.objects.create(
            id=2, sys_date=date.today(), sys_time=time(10,0), comp_id=1, fin_year_id=2023,
            session_id='testuser', min_no='0002', mrs_no='MRS001', mrs_id=self.mrs_master
        )
        min_no_next = MaterialIssueMaster.objects.generate_min_no(self.mrs_master.comp_id, self.mrs_master.fin_year_id)
        self.assertEqual(min_no_next, '0003')

    @patch('inventory.models.send_mail')
    def test_material_issue_master_process_material_issue_success(self, mock_send_mail):
        initial_stock_item2 = ItemMaster.objects.get(id=self.item2.id).stock_qty # Current: 40.0

        selected_items = [
            {
                'mrs_detail_id': self.mrs_detail_1.id, # Item1, currently 0 stock
                'item_id': self.item1.id,
                'req_qty': 20.0,
                'issued_qty_prev': 5.0, # Remaining 15
                'stock_qty': 0.0 # From UI
            },
            {
                'mrs_detail_id': self.mrs_detail_2.id, # Item2, currently 40 stock
                'item_id': self.item2.id,
                'req_qty': 60.0,
                'issued_qty_prev': 0.0, # Remaining 60
                'stock_qty': initial_stock_item2 # From UI
            }
        ]

        success, min_no = MaterialIssueMaster.objects.process_material_issue(
            mrs_master_id=self.mrs_master.id,
            mrs_no=self.mrs_master.mrs_no,
            company_id=self.mrs_master.comp_id,
            fin_year_id=self.mrs_master.fin_year_id,
            session_id='test_user',
            selected_items_data=selected_items
        )

        self.assertTrue(success)
        self.assertEqual(min_no, '0003') # Current: 0001, 0002 created in test, so this is 0003

        # Verify MaterialIssueMaster created
        new_min_master = MaterialIssueMaster.objects.get(min_no='0003')
        self.assertIsNotNone(new_min_master)
        self.assertEqual(new_min_master.mrs_id, self.mrs_master)

        # Verify MaterialIssueDetails created
        # For item1 (mrs_detail_1), should issue 0 as stock is 0
        min_details_item1 = MaterialIssueDetail.objects.filter(mid=new_min_master, mrs_id=self.mrs_detail_1).first()
        self.assertIsNone(min_details_item1) # No issue record if issue_qty is 0

        # For item2 (mrs_detail_2), should issue 40 (stock_qty)
        min_details_item2 = MaterialIssueDetail.objects.get(mid=new_min_master, mrs_id=self.mrs_detail_2)
        self.assertIsNotNone(min_details_item2)
        self.assertEqual(min_details_item2.issue_qty, 40.0) # Issued current stock_qty (40.0)

        # Verify ItemMaster stock updated
        self.item1.refresh_from_db()
        self.assertEqual(self.item1.stock_qty, 0.0) # No change, was 0.0

        self.item2.refresh_from_db()
        self.assertEqual(self.item2.stock_qty, 0.0) # Was 40.0, 40.0 issued

        # No discrepancy email should be sent if stock update is successful
        mock_send_mail.assert_not_called()

    @patch('inventory.models.send_mail')
    def test_material_issue_master_process_material_issue_discrepancy_email(self, mock_send_mail):
        # Temporarily manipulate stock to create a discrepancy scenario for email testing
        ItemMaster.objects.filter(id=self.item1.id).update(stock_qty=10.0)
        self.item1.refresh_from_db() # Stock is now 10.0

        selected_items = [
            {
                'mrs_detail_id': self.mrs_detail_no_stock.id, # Item1, currently 10 stock
                'item_id': self.item1.id,
                'req_qty': 10.0,
                'issued_qty_prev': 0.0, # Remaining 10
                'stock_qty': 10.0 # From UI
            }
        ]

        # Simulate a failed stock update (e.g., another process consumed stock)
        # We'll mock update_stock to report a different final stock than expected
        with patch.object(ItemMaster.objects, 'update_stock', return_value=(True, 5.0)) as mock_item_update_stock:
             # The first value (True) indicates success, the second (5.0) is the reported new stock
             # This will cause a discrepancy as (initial_stock - issued_qty) != new_stock_qty
            success, min_no = MaterialIssueMaster.objects.process_material_issue(
                mrs_master_id=self.mrs_master.id,
                mrs_no=self.mrs_master.mrs_no,
                company_id=self.mrs_master.comp_id,
                fin_year_id=self.mrs_master.fin_year_id,
                session_id='test_user',
                selected_items_data=selected_items
            )

        self.assertTrue(success)
        mock_send_mail.assert_called_once()
        self.assertIn("ItemCode:ITEM001", mock_send_mail.call_args[1]['html_message'])
        self.assertIn("Initial Stock:10.0", mock_send_mail.call_args[1]['html_message'])
        self.assertIn("Issued Qty:10.0", mock_send_mail.call_args[1]['html_message'])
        self.assertIn("Final Stock After Update:5.0", mock_send_mail.call_args[1]['html_message'])
        self.assertIn("Expected Final Stock:0.0", mock_send_mail.call_args[1]['html_message']) # 10-10=0

class MaterialIssueNoteViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session data
        session = self.client.session
        session['compid'] = 1
        session['username'] = 'testuser'
        session['finyear'] = 2023
        session.save()

        # Clean tables and create base data for each test method
        with connection.cursor() as cursor:
            cursor.execute("TRUNCATE TABLE Unit_Master CASCADE")
            cursor.execute("TRUNCATE TABLE BusinessGroup CASCADE")
            cursor.execute("TRUNCATE TABLE tblDG_Item_Master CASCADE")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialRequisition_Master CASCADE")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialRequisition_Details CASCADE")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialIssue_Master CASCADE")
            cursor.execute("TRUNCATE TABLE tblInv_MaterialIssue_Details CASCADE")
            cursor.execute("TRUNCATE TABLE tblCompany_master CASCADE")

        self.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        self.dept_prod = BusinessGroup.objects.create(id=10, symbol='PROD')
        self.item1 = ItemMaster.objects.create(id=101, comp_id=1, item_code='ITEM001', manf_desc='Widget A', uom_basic=self.unit_pcs, stock_qty=100.0)
        self.item2 = ItemMaster.objects.create(id=102, comp_id=1, item_code='ITEM002', manf_desc='Gadget B', uom_basic=self.unit_pcs, stock_qty=50.0)
        self.mrs_master = MaterialRequisitionMaster.objects.create(id=1, mrs_no='MRS001', comp_id=1, fin_year_id=2023)
        self.company = CompanyMaster.objects.create(comp_id=1, mail_server_ip='smtp.example.com', erp_sysmail='<EMAIL>')
        self.mrs_detail_1 = MaterialRequisitionDetail.objects.create(id=1001, mid=self.mrs_master, itemid=self.item1, deptid=self.dept_prod, wo_no='WO-ABC', req_qty=20.0, remarks='For production')
        self.mrs_detail_2 = MaterialRequisitionDetail.objects.create(id=1002, mid=self.mrs_master, itemid=self.item2, deptid=self.dept_prod, wo_no='WO-DEF', req_qty=60.0, remarks='For testing')
        self.mrs_detail_zero_stock = MaterialRequisitionDetail.objects.create(id=1003, mid=self.mrs_master, itemid=self.item1, deptid=self.dept_prod, wo_no='WO-XYZ', req_qty=10.0, remarks='For zero stock item')
        ItemMaster.objects.filter(id=self.item1.id).update(stock_qty=0.0) # Set stock to 0 for this test

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:min_new_details'), {'Id': self.mrs_master.id, 'mrsno': 'MRS001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiondetail/min_new_details_list.html')
        self.assertContains(response, 'Material Issue Note [MIN] - New')
        self.assertContains(response, 'Requisition No: MRS001')
        self.assertContains(response, f'mrs_master_id={self.mrs_master.id}') # Check hidden input for HTMX

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('inventory:min_details_table'), {'mrs_master_id': self.mrs_master.id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialrequisitiondetail/_min_new_details_table.html')
        self.assertContains(response, 'minDetailsTable')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'ITEM002')
        
        # Check if checkbox is hidden for zero stock item
        self.assertContains(response, '<tr class="hover:bg-gray-50" data-mrs-detail-id="1003"')
        self.assertNotContains(response, '<tr class="hover:bg-gray-50" data-mrs-detail-id="1003"><td class="py-2 px-4 border-b border-gray-200 text-center"><input type="checkbox"', html=True)

    def test_table_partial_view_get_no_mrs_id(self):
        response = self.client.get(reverse('inventory:min_details_table'))
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Material Requisition ID is missing.')

    def test_table_partial_view_get_invalid_mrs_id(self):
        response = self.client.get(reverse('inventory:min_details_table'), {'mrs_master_id': 9999})
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, 'Material Requisition with ID 9999 not found.')

    @patch('inventory.models.MaterialIssueMasterManager.process_material_issue', return_value=(True, '0001'))
    def test_generate_min_view_post_success(self, mock_process_material_issue):
        selected_items_data = [
            {'mrs_detail_id': self.mrs_detail_2.id, 'item_id': self.item2.id, 'req_qty': 60.0, 'issued_qty_prev': 0.0, 'stock_qty': 50.0}
        ]
        
        response = self.client.post(
            reverse('inventory:min_generate'),
            {
                'mrs_master_id': self.mrs_master.id,
                'mrs_no': self.mrs_master.mrs_no,
                'selected_items_data': json.dumps(selected_items_data),
                'csrfmiddlewaretoken': self.client.cookies['csrftoken'].value # Include CSRF token
            },
            HTTP_HX_REQUEST='true' # Simulate HTMX request
        )
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse_lazy('inventory:min_list'))
        mock_process_material_issue.assert_called_once()
        messages_list = list(response.wsgi_request._messages)
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), 'Material Issue Note 0001 generated successfully.')

    @patch('inventory.models.MaterialIssueMasterManager.process_material_issue', return_value=(False, None))
    def test_generate_min_view_post_failure(self, mock_process_material_issue):
        selected_items_data = [
            {'mrs_detail_id': self.mrs_detail_2.id, 'item_id': self.item2.id, 'req_qty': 60.0, 'issued_qty_prev': 0.0, 'stock_qty': 50.0}
        ]
        
        response = self.client.post(
            reverse('inventory:min_generate'),
            {
                'mrs_master_id': self.mrs_master.id,
                'mrs_no': self.mrs_master.mrs_no,
                'selected_items_data': json.dumps(selected_items_data),
                'csrfmiddlewaretoken': self.client.cookies['csrftoken'].value
            },
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 500)
        self.assertIn('HX-Refresh', response.headers)
        messages_list = list(response.wsgi_request._messages)
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), 'Failed to generate Material Issue Note.')

    def test_generate_min_view_post_no_items_selected(self):
        response = self.client.post(
            reverse('inventory:min_generate'),
            {
                'mrs_master_id': self.mrs_master.id,
                'mrs_no': self.mrs_master.mrs_no,
                'selected_items_data': json.dumps([]), # Empty list of items
                'csrfmiddlewaretoken': self.client.cookies['csrftoken'].value
            },
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMINList')
        messages_list = list(response.wsgi_request._messages)
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), 'No items selected for Material Issue Note generation.')


    def test_cancel_view_get(self):
        response = self.client.get(reverse('inventory:min_cancel'))
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('inventory:min_list'))

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Integration:**

*   **Initial Load & Table Refresh:**
    *   The `min_new_details_list.html` uses `hx-get` on the `min-details-table-container` div to load the `_min_new_details_table.html` content dynamically on page load and when `refreshMINDetailsTable` is triggered (e.g., after an action or external event).
    *   `hx-indicator` is used to show a spinning loader during HTMX requests.
*   **DataTables:**
    *   DataTables is initialized on the `minDetailsTable` after the HTMX swap completes. The `htmx:afterSwap` event listener ensures that DataTables is correctly re-initialized for the newly loaded table content. This provides client-side sorting, searching, and pagination.
*   **"Generate MIN" Button:**
    *   The "Generate MIN" button uses `hx-post` to submit data to `{% url 'inventory:min_generate' %}`.
    *   `hx-confirm` provides a native browser confirmation dialog before submission.
    *   `hx-include="#generateMinForm"` ensures the CSRF token and any hidden form fields (`mrs_master_id`, `mrs_no`) are included in the POST request.
    *   A JavaScript function `submitGenerateMinForm()` collects the data from the selected checkboxes and populates a hidden input (`#selectedItemsData`) with a JSON string of selected item details (`mrs_detail_id`, `item_id`, `req_qty`, `issued_qty_prev`, `stock_qty`). This structured data is then sent to the backend.
    *   On successful generation, the view sends `HX-Redirect` header to navigate to the MIN list page, as per the original ASP.NET behavior. On failure, it sends `HX-Refresh` to reload the page to show messages.
*   **"Cancel" Button:**
    *   The "Cancel" button uses a simple `hx-get` to `{% url 'inventory:min_cancel' %}` which performs a redirect to the main Material Issue Note list.
*   **Alpine.js:**
    *   While not extensively used for complex UI state in this direct migration, Alpine.js remains available via `core/base.html` for any future client-side reactivity needs, modal management, or complex form interactions that HTMX alone cannot elegantly handle. For simple modals as in the general template example, Alpine.js is excellent. In this specific case, the loading indicator is purely HTMX/CSS.

---

### Final Notes

*   **Placeholders:** Remember to replace `[APP_NAME]` and other placeholders (like `min_list` URL) with your actual project values.
*   **Session Data:** The `get_session_data` helper in `views.py` mocks the ASP.NET session access. In a production Django application, you would replace this with actual user authentication and profile data (e.g., `request.user.profile.company_id`).
*   **Error Handling:** The Django views include basic error handling and messaging. In a production environment, you might want more sophisticated logging and user feedback mechanisms.
*   **Database Interaction:** Since `managed = False` is used, ensure your Django settings are configured to connect to your existing SQL Server database (e.g., using `django-pyodbc-azure` or `django-mssql-backend`).
*   **Security:** Always ensure proper input validation, CSRF protection (Django handles this automatically with `{% csrf_token %}`), and parameterized queries (Django ORM handles this) to prevent SQL injection and other vulnerabilities.
*   **Frontend Libraries:** Ensure DataTables CSS and JS (and jQuery, which DataTables depends on) are correctly linked in your `core/base.html`. Tailwind CSS classes are applied directly in the HTML.
*   **Refinement:** This plan provides a robust foundation. Further refinements might include:
    *   More granular permissions checks.
    *   Advanced search/filter options beyond DataTables' default.
    *   Asynchronous tasks for email sending (e.g., using Celery) to avoid blocking the request.