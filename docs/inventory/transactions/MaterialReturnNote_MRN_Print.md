## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

The ASP.NET code primarily interacts with two database entities: `tblInv_MaterialReturn_Master` for Material Return Notes and `tblHR_OfficeStaff` for employee information used in search and generation tracking. The data grid columns and stored procedure parameters (`Sp_MRN_FillGrid`) provide strong clues.

*   **Main Table:** `tblInv_MaterialReturn_Master`
    *   **Inferred Columns:**
        *   `Id` (Primary Key, integer)
        *   `CompId` (Foreign Key to Company, integer)
        *   `FinYearId` (Foreign Key to Financial Year, integer)
        *   `SysDate` (Date)
        *   `MRNNo` (String/Varchar)
        *   `SessionId` (Foreign Key to Employee ID, integer) - This column is used for filtering by employee. The `GenBy` column displayed in the GridView is likely a derived name from this ID.

*   **Lookup Table:** `tblHR_OfficeStaff`
    *   **Inferred Columns:**
        *   `EmpId` (Primary Key, integer)
        *   `EmployeeName` (String/Varchar)
        *   `CompId` (Foreign Key to Company, integer)

*   **Auxiliary Tables (inferred from `CompId` and `FinYearId`):**
    *   `tblCompanyMaster` (for `CompId`)
    *   `tblFinancialYear` (for `FinYearId`)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

This specific ASP.NET page is focused on viewing and searching for Material Return Notes for printing purposes.

*   **Read (R):** The primary functionality is to display a paginated list of Material Return Notes in a GridView.
*   **Search/Filter:** Users can filter the list by "MRN No" or "Employee Name". The Employee Name filter utilizes an autocomplete feature.
*   **Navigation/Redirection:** Upon selecting a Material Return Note from the list, the page redirects to a "details" page (`MaterialReturnNote_MRN_Print_Details.aspx`) with various parameters related to the selected MRN.
*   **No Create, Update, or Delete (CUD) operations** are present for Material Return Notes on this specific page. This page only serves as a lookup and navigation point for existing records.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

*   **Search/Filter Controls:**
    *   `DrpField` (DropDownList): Allows selection between "Employee Name" or "MRN No" for filtering. It triggers an `AutoPostBack` (Django equivalent: HTMX request).
    *   `TxtMrn` (TextBox): Input field for "MRN No" filter. Its visibility changes based on `DrpField` selection.
    *   `TxtEmpName` (TextBox): Input field for "Employee Name" filter. Its visibility changes based on `DrpField` selection.
    *   `TxtEmpName_AutoCompleteExtender`: Provides real-time suggestions for employee names as the user types. This will be replaced by an HTMX-driven autocomplete endpoint.
    *   `Button1` (Button): "Search" button to apply filters and refresh the grid.
*   **Data Display:**
    *   `GridView1`: Displays the list of Material Return Notes. It includes pagination, and columns for SN, Select, Id (hidden), FinYear Id (hidden), FinYear, Date, MRN No, and Gen. By. The "Select" column contains a `LinkButton` for redirection. This will be replaced by a Django template using DataTables.

### Step 4: Generate Django Code

We will create a new Django application, e.g., `inventory`, to house the migration components.

#### 4.1 Models (`inventory/models.py`)

We'll define Django models that map to the identified database tables using `managed = False` for existing databases. We'll also add helper methods for business logic in the models themselves, adhering to the "fat model" principle.

```python
from django.db import models
from django.db.models import Q # For complex queries

# Dummy/Placeholder models for related entities if they are not explicitly defined elsewhere
# These models assume corresponding tables exist in the legacy database.

class Company(models.Model):
    """
    Represents a Company. Assumed to be mapped to tblCompanyMaster.
    Used for filtering by CompId which is passed in session.
    """
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(max_length=255, db_column='CompanyName') # Assuming column name

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Placeholder table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name

class FinancialYear(models.Model):
    """
    Represents a Financial Year. Assumed to be mapped to tblFinancialYear.
    Used for filtering by FinYearId which is passed in session.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(max_length=50, db_column='FinYear') # e.g., '2023-2024'
    # Add other fields if necessary, e.g., start_date, end_date

    class Meta:
        managed = False
        db_table = 'tblFinancialYear' # Placeholder table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    """
    Represents an Employee. Mapped to tblHR_OfficeStaff, used for autocomplete and linking.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(max_length=255, db_column='EmployeeName')
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='employees', null=True)
    # Add other fields from tblHR_OfficeStaff if they are relevant for display or logic

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        """Returns the formatted string 'Employee Name [ID]' for autocomplete."""
        return f"{self.employee_name} [{self.emp_id}]"

    @classmethod
    def get_employee_id_from_formatted_string(cls, formatted_string):
        """
        Extracts the employee ID from a string in the format 'Employee Name [ID]'.
        Mimics the fun.getCode() logic used in ASP.NET.
        """
        import re
        match = re.search(r'\[(\d+)\]$', formatted_string)
        if match:
            return int(match.group(1))
        return None

    @classmethod
    def get_autocomplete_suggestions(cls, query, company_id=None, limit=10):
        """
        Provides autocomplete suggestions for employees based on prefix text.
        Mimics the GetCompletionList WebMethod.
        """
        if not query:
            return []

        # Start with a base queryset
        queryset = cls.objects.all()

        # Filter by company if provided (mimics CompId filter in WebMethod)
        if company_id is not None:
            queryset = queryset.filter(comp_id=company_id)

        # Filter by employee_name starting with prefixText (case-insensitive)
        results = queryset.filter(employee_name__istartswith=query) \
                          .order_by('employee_name')[:limit]
        
        # Return formatted strings as per ASP.NET WebMethod output
        return [str(emp) for emp in results]


class MaterialReturnNote(models.Model):
    """
    Represents a Material Return Note. Mapped to tblInv_MaterialReturn_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='material_return_notes', null=True)
    fin_year_id = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='material_return_notes', null=True)
    sys_date = models.DateField(db_column='SysDate')
    mrn_no = models.CharField(max_length=50, db_column='MRNNo') # Assuming max length
    # SessionId in ASP.NET code implies a link to Employee.EmpId. Renamed for clarity.
    generated_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='generated_mrns', null=True, blank=True)
    # The 'GenBy' field in GridView is a display field, likely employee_name from generated_by_employee.

    class Meta:
        managed = False # Crucial for mapping to existing legacy tables
        db_table = 'tblInv_MaterialReturn_Master'
        verbose_name = 'Material Return Note'
        verbose_name_plural = 'Material Return Notes'
        # Ordering can be added here if there's a default (e.g., ordering = ['-sys_date'])

    def __str__(self):
        return self.mrn_no

    @property
    def fin_year_display(self):
        """Returns the financial year string for display in the grid."""
        return self.fin_year_id.fin_year if self.fin_year_id else 'N/A'

    @property
    def generated_by_display(self):
        """Returns the employee name for the 'Gen. By' column in the grid."""
        return self.generated_by_employee.employee_name if self.generated_by_employee else 'N/A'

    @classmethod
    def get_filtered_notes(cls, company_id, fin_year_id, search_type, search_value):
        """
        Applies filtering logic similar to the ASP.NET fillgrid method.
        This method encapsulates the data retrieval and filtering.
        """
        queryset = cls.objects.filter(comp_id=company_id, fin_year_id=fin_year_id)

        if search_type == '1': # MRN No filter
            if search_value:
                # Use iexact for case-insensitive exact match like 'MRNNo='<TxtMrn.Text>'
                queryset = queryset.filter(mrn_no__iexact=search_value)
            
        elif search_type == '0': # Employee Name filter
            if search_value:
                emp_id = Employee.get_employee_id_from_formatted_string(search_value)
                if emp_id:
                    queryset = queryset.filter(generated_by_employee__emp_id=emp_id)
                else:
                    # If employee ID cannot be extracted, no match, return empty queryset
                    return cls.objects.none()
        
        # Default ordering for consistency if not specified in Meta
        return queryset.order_by('-sys_date', '-id') 

```

#### 4.2 Forms (`inventory/forms.py`)

This form will handle the search criteria and dynamic visibility of input fields using HTMX attributes.

```python
from django import forms

class MaterialReturnNoteSearchForm(forms.Form):
    """
    Form for searching Material Return Notes by MRN No or Employee Name.
    Designed with HTMX in mind for dynamic behavior.
    """
    SEARCH_CHOICES = [
        ('0', 'Employee Name'),
        ('1', 'MRN No'),
    ]
    
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-[150px] p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            # HTMX to dynamically update the search fields container on change
            'hx-get': '/inventory/materialreturnnote/search-fields/', 
            'hx-target': '#search-fields-container', 
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
        }),
        label="Search By"
    )
    
    mrn_no = forms.CharField(
        max_length=50, 
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[150px] p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500', 
            'placeholder': 'Enter MRN No'
        }),
        label="MRN No"
    )
    
    employee_name = forms.CharField(
        max_length=350, 
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[350px] p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500', 
            'placeholder': 'Enter Employee Name',
            # HTMX for autocomplete suggestions (target div below input)
            'hx-post': '/inventory/materialreturnnote/autocomplete/employee/', 
            'hx-trigger': 'keyup changed delay:500ms from:this, search', # Trigger on keyup after 500ms delay, or on native search input event
            'hx-target': '#employee-suggestions-container', 
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser's native autocomplete
        }),
        label="Employee Name"
    )

    def __init__(self, *args, **kwargs):
        # Allow initial_search_by to control which field is initially visible
        initial_search_by = kwargs.pop('initial_search_by', '0') # Default to '0' (Employee Name)
        super().__init__(*args, **kwargs)

        self.fields['search_by'].initial = initial_search_by

        # Set initial visibility based on the selected search type
        if initial_search_by == '0': # Employee Name
            self.fields['mrn_no'].widget.attrs['style'] = 'display:none;'
            self.fields['employee_name'].widget.attrs.pop('style', None)
        else: # MRN No
            self.fields['employee_name'].widget.attrs['style'] = 'display:none;'
            self.fields['mrn_no'].widget.attrs.pop('style', None)

```

#### 4.3 Views (`inventory/views.py`)

We'll use Django Class-Based Views (CBVs) for the list display and separate views for HTMX partials like the search fields and the DataTables content.

```python
from django.views.generic import ListView, TemplateView
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.contrib import messages
from django.urls import reverse
from .models import MaterialReturnNote, Employee, Company, FinancialYear
from .forms import MaterialReturnNoteSearchForm

class MaterialReturnNotePrintListView(ListView):
    """
    Main view to display the Material Return Note search and list page.
    This view renders the initial page with the search form and a placeholder for the DataTable.
    """
    model = MaterialReturnNote
    template_name = 'inventory/materialreturnnote/list.html'
    context_object_name = 'material_return_notes'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with current request parameters for persistence
        search_type = self.request.GET.get('search_by', '0')
        context['search_form'] = MaterialReturnNoteSearchForm(
            self.request.GET, 
            initial_search_by=search_type
        )
        return context

    def get_queryset(self):
        # This view doesn't directly return the filtered queryset for DataTables,
        # it's handled by MaterialReturnNoteTablePartialView.
        # But for initial load, we might want to show some data, or return an empty queryset.
        # The actual data fetching will happen via HTMX to MaterialReturnNoteTablePartialView.
        return MaterialReturnNote.objects.none() # Initially empty, loaded via HTMX

class MaterialReturnNoteTablePartialView(TemplateView):
    """
    HTMX partial view to render only the DataTables table content.
    This is called via HTMX to update the list dynamically.
    """
    template_name = 'inventory/materialreturnnote/_material_return_note_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve session-specific parameters (mimicking ASP.NET Session)
        # In a real ERP, these would come from user session management or a global context
        company_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 1) # Default to 1 if not in session

        # Retrieve search parameters from GET request (from search form submission)
        search_type = self.request.GET.get('search_by', '0') # Default to Employee Name
        mrn_no = self.request.GET.get('mrn_no', '').strip()
        employee_name = self.request.GET.get('employee_name', '').strip()

        # Determine the actual search value based on search_type
        search_value = mrn_no if search_type == '1' else employee_name

        # Call the fat model method to get filtered data
        material_return_notes = MaterialReturnNote.get_filtered_notes(
            company_id, fin_year_id, search_type, search_value
        )
        
        context['material_return_notes'] = material_return_notes
        return context

class MaterialReturnNoteSearchFieldsPartialView(TemplateView):
    """
    HTMX partial view to render only the dynamically visible search fields.
    This is called when the 'Search By' dropdown changes.
    """
    template_name = 'inventory/materialreturnnote/_search_fields_partial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get the selected search type from the request
        selected_search_by = self.request.GET.get('search_by', '0')
        # Re-instantiate the form to get the correct initial visibility based on the selection
        context['search_form'] = MaterialReturnNoteSearchForm(
            initial_search_by=selected_search_by
        )
        return context

class EmployeeAutocompleteView(TemplateView):
    """
    HTMX/JSON endpoint for employee name autocomplete suggestions.
    Mimics the ASP.NET GetCompletionList WebMethod.
    """
    def post(self, request, *args, **kwargs):
        query = request.POST.get('employee_name_ac', '').strip() # Use the name for the AC input
        company_id = self.request.session.get('compid', 1) # Mimic CompId filter
        
        suggestions = Employee.get_autocomplete_suggestions(query, company_id=company_id)
        
        # Render the suggestions in a simple list suitable for HTMX swapping
        html_suggestions = render_to_string('inventory/materialreturnnote/_employee_suggestions.html', {'suggestions': suggestions})
        return HttpResponse(html_suggestions)

    def get(self, request, *args, **kwargs):
        # In case it's accessed via GET (e.g., direct URL), return empty or error
        return HttpResponse("", status=204) # No content


# This is just a placeholder for the redirect logic, as per the original ASP.NET
# For a real application, this would link to a proper Material Return Note DetailView
class MaterialReturnNoteDetailRedirectView(TemplateView):
    """
    Mimics the ASP.NET Response.Redirect for 'Select' action.
    This will redirect to a placeholder URL or an actual detail page.
    """
    def get(self, request, *args, **kwargs):
        mrn_id = kwargs.get('pk')
        mrn_no = request.GET.get('mrn_no', '')
        fin_year_id = request.GET.get('fyid', '')
        
        # Generate a dummy random key (not security-critical for this example)
        # In Django, CSRF tokens or signed IDs would be used for security-sensitive links
        import uuid
        random_key = uuid.uuid4().hex[:10]

        # Construct the URL for the details page
        # In a real Django app, you would reverse a URL pattern for the actual detail view
        # For now, mimicking the original query parameters structure
        detail_url = reverse('material_return_note_details_placeholder')
        redirect_params = f"id={mrn_id}&key={random_key}&mrn_no={mrn_no}&fyid={fin_year_id}&modid=9&submodid=48"
        return HttpResponse(f'<script>window.location.href="{detail_url}?{redirect_params}";</script>')

```

#### 4.4 Templates (`inventory/templates/inventory/materialreturnnote/`)

The templates are designed for HTMX interactions, using partials to update specific parts of the page without full reloads. We will use `core/base.html` as the base template.

**`list.html`**: The main page template.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-6 text-gray-800">Material Return Note [MRN] - Print</h2>
        
        <form id="searchForm" class="mb-6" hx-get="{% url 'material_return_note_table_partial' %}" hx-target="#mrnTableContainer" hx-swap="innerHTML">
            {% csrf_token %}
            <div class="flex flex-wrap items-center space-x-4 mb-4" id="search-form-controls">
                {# search_by dropdown is rendered first to control dynamic visibility #}
                <div class="flex items-center">
                    <label for="{{ search_form.search_by.id_for_label }}" class="text-sm font-medium text-gray-700 mr-2">{{ search_form.search_by.label }}:</label>
                    {{ search_form.search_by }}
                </div>
                
                {# Container for dynamic search fields (MRN No or Employee Name) #}
                <div id="search-fields-container" class="flex-grow flex items-center space-x-4">
                    {# This content will be replaced by _search_fields_partial.html via HTMX #}
                    {% include 'inventory/materialreturnnote/_search_fields_partial.html' %}
                </div>

                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Search
                </button>
            </div>
        </form>

        <div id="mrnTableContainer" 
             hx-trigger="load, refreshMrnList from:body" {# Load on page load and on custom event #}
             hx-get="{% url 'material_return_note_table_partial' %}" 
             hx-swap="innerHTML"
             class="min-h-[200px] flex items-center justify-center">
            <!-- Initial loading state for HTMX -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Material Return Notes...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('mrnPrintData', () => ({
            // Add Alpine.js specific states or functions if needed for complex UI interactions
            // For simple HTMX setups, Alpine.js might not be strictly necessary for all components
        }));
    });

    // We can use HTMX's htmx:afterSwap event to re-initialize DataTables
    // This script runs only when the table partial is swapped into the DOM
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'mrnTableContainer') {
            $('#materialReturnNoteTable').DataTable({
                "pageLength": 20, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true, // Destroy existing instance if any
                "pagingType": "simple_numbers", // Example paging style
                // Ensure DataTables doesn't try to re-fetch via Ajax since HTMX manages that
                "searching": true, // Allow client-side search
                "ordering": true,  // Allow client-side sorting
            });
        }
    });

    // Event listener for clicking on autocomplete suggestions
    // This needs to be delegated because suggestions are dynamically loaded
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('autocomplete-suggestion-item')) {
            const selectedValue = event.target.textContent;
            const employeeNameInput = document.querySelector('input[name="employee_name"]');
            if (employeeNameInput) {
                employeeNameInput.value = selectedValue;
                // Trigger form submission or refresh table after selecting
                document.getElementById('searchForm').requestSubmit();
                document.getElementById('employee-suggestions-container').innerHTML = ''; // Clear suggestions
            }
        }
    });
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css" />
<style>
    /* Custom CSS for consistency or specific styling */
    .box3 { /* Mimics ASP.NET CssClass */
        @apply block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm;
    }
    .autocomplete-suggestion-item {
        @apply cursor-pointer p-2 hover:bg-blue-100;
    }
    .autocomplete-list {
        @apply border border-gray-300 rounded-md shadow-lg bg-white mt-1 absolute z-10 w-full;
    }
</style>
{% endblock %}
```

**`_material_return_note_table.html`**: Partial for the DataTable, loaded via HTMX.

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="materialReturnNoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRN No</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th scope="col" class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for note in material_return_notes %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ note.mrn_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ note.sys_date|date:"d M Y" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ note.fin_year_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ note.generated_by_display }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-center">
                    <a href="{% url 'material_return_note_detail_redirect' note.pk %}?mrn_no={{ note.mrn_no }}&fyid={{ note.fin_year_id.fin_year_id }}" 
                       class="text-indigo-600 hover:text-indigo-900 px-3 py-1 rounded-md border border-indigo-600 hover:border-indigo-900 transition duration-150 ease-in-out">
                        Select
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-gray-500">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# This script will run on HTMX swap, re-initializing DataTables #}
<script>
    // DataTable initialization is handled in list.html's htmx:afterSwap listener
    // No need to put it here directly to avoid multiple initializations
</script>

```

**`_search_fields_partial.html`**: Partial for dynamically swapping search input fields.

```html
<div id="search-fields-container" class="flex items-center space-x-4">
    {% if search_form.search_by.value == '0' %} {# Employee Name is selected #}
        <div class="flex items-center">
            <label for="{{ search_form.employee_name.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">{{ search_form.employee_name.label }}:</label>
            {{ search_form.employee_name }}
            {# Autocomplete suggestions container #}
            <div id="employee-suggestions-container" class="autocomplete-list"></div>
        </div>
    {% else %} {# MRN No is selected #}
        <div class="flex items-center">
            <label for="{{ search_form.mrn_no.id_for_label }}" class="text-sm font-medium text-gray-700 sr-only">{{ search_form.mrn_no.label }}:</label>
            {{ search_form.mrn_no }}
        </div>
    {% endif %}
</div>
```

**`_employee_suggestions.html`**: Partial for autocomplete suggestions.

```html
{% if suggestions %}
    {% for suggestion in suggestions %}
        <div class="autocomplete-suggestion-item">{{ suggestion }}</div>
    {% endfor %}
{% else %}
    {% if request.POST.employee_name_ac %}
        <div class="p-2 text-gray-500 text-sm">No matches found.</div>
    {% endif %}
{% endif %}
```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for the views, including HTMX-specific endpoints.

```python
from django.urls import path
from .views import (
    MaterialReturnNotePrintListView, 
    MaterialReturnNoteTablePartialView,
    MaterialReturnNoteSearchFieldsPartialView,
    EmployeeAutocompleteView,
    MaterialReturnNoteDetailRedirectView,
)

urlpatterns = [
    # Main list view (renders the initial page)
    path('materialreturnnote/print/', MaterialReturnNotePrintListView.as_view(), name='material_return_note_print_list'),

    # HTMX endpoint to load/refresh the DataTables content
    path('materialreturnnote/table/', MaterialReturnNoteTablePartialView.as_view(), name='material_return_note_table_partial'),
    
    # HTMX endpoint to swap the search input fields (MRN No / Employee Name)
    path('materialreturnnote/search-fields/', MaterialReturnNoteSearchFieldsPartialView.as_view(), name='material_return_note_search_fields_partial'),

    # HTMX/JSON endpoint for employee autocomplete suggestions
    path('materialreturnnote/autocomplete/employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),

    # Placeholder for redirecting to a details page (mimicking ASP.NET behavior)
    # The 'pk' will be the 'Id' from the MaterialReturnNote
    path('materialreturnnote/details/<int:pk>/', MaterialReturnNoteDetailRedirectView.as_view(), name='material_return_note_detail_redirect'),

    # Add a dummy URL for the actual details page that the redirect points to
    # This would be implemented in a separate Django app or a full DetailView
    path('materialreturnnote/details_placeholder/', MaterialReturnNotePrintListView.as_view(), name='material_return_note_details_placeholder'), # Redirects to list for now
]

```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive unit tests for model methods and integration tests for all views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Company, FinancialYear, Employee, MaterialReturnNote
from .forms import MaterialReturnNoteSearchForm
import datetime

class SetupTestDataMixin:
    """Mixin to set up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create dummy Company and FinancialYear objects for FK relationships
        cls.company = Company.objects.create(comp_id=1, company_name='Test Company')
        cls.fin_year = FinancialYear.objects.create(fin_year_id=1, fin_year='2023-2024')
        
        # Create dummy Employee objects for testing autocomplete and linking
        cls.employee1 = Employee.objects.create(emp_id=101, employee_name='John Doe', comp_id=cls.company)
        cls.employee2 = Employee.objects.create(emp_id=102, employee_name='Jane Smith', comp_id=cls.company)
        cls.employee3 = Employee.objects.create(emp_id=103, employee_name='Adam Johnson', comp_id=cls.company)

        # Create MaterialReturnNote objects
        cls.mrn1 = MaterialReturnNote.objects.create(
            id=1, 
            comp_id=cls.company, 
            fin_year_id=cls.fin_year, 
            sys_date=datetime.date(2023, 1, 15), 
            mrn_no='MRN/2023/001', 
            generated_by_employee=cls.employee1
        )
        cls.mrn2 = MaterialReturnNote.objects.create(
            id=2, 
            comp_id=cls.company, 
            fin_year_id=cls.fin_year, 
            sys_date=datetime.date(2023, 2, 20), 
            mrn_no='MRN/2023/002', 
            generated_by_employee=cls.employee2
        )
        cls.mrn3 = MaterialReturnNote.objects.create(
            id=3, 
            comp_id=cls.company, 
            fin_year_id=cls.fin_year, 
            sys_date=datetime.date(2023, 3, 25), 
            mrn_no='MRN/2023/003', 
            generated_by_employee=cls.employee1
        )


class EmployeeModelTest(SetupTestDataMixin, TestCase):
    def test_employee_creation(self):
        self.assertEqual(self.employee1.employee_name, 'John Doe')
        self.assertEqual(self.employee1.emp_id, 101)
        self.assertEqual(str(self.employee1), 'John Doe [101]')

    def test_get_employee_id_from_formatted_string(self):
        self.assertEqual(Employee.get_employee_id_from_formatted_string('John Doe [101]'), 101)
        self.assertIsNone(Employee.get_employee_id_from_formatted_string('Invalid Format'))
        self.assertIsNone(Employee.get_employee_id_from_formatted_string(''))

    def test_get_autocomplete_suggestions(self):
        # Test case-insensitive prefix matching
        suggestions = Employee.get_autocomplete_suggestions('john', company_id=self.company.comp_id)
        self.assertIn('John Doe [101]', suggestions)
        self.assertNotIn('Jane Smith [102]', suggestions)

        suggestions = Employee.get_autocomplete_suggestions('j', company_id=self.company.comp_id)
        self.assertIn('Jane Smith [102]', suggestions)
        self.assertIn('John Doe [101]', suggestions)
        self.assertEqual(len(suggestions), 2) # John Doe, Jane Smith

        suggestions = Employee.get_autocomplete_suggestions('ad', company_id=self.company.comp_id)
        self.assertIn('Adam Johnson [103]', suggestions)
        self.assertEqual(len(suggestions), 1)

        # Test with no query
        self.assertEqual(Employee.get_autocomplete_suggestions(''), [])

        # Test with limit
        suggestions = Employee.get_autocomplete_suggestions('j', company_id=self.company.comp_id, limit=1)
        self.assertEqual(len(suggestions), 1) # Order might vary without explicit order_by in test setup

class MaterialReturnNoteModelTest(SetupTestDataMixin, TestCase):
    def test_mrn_creation(self):
        self.assertEqual(self.mrn1.mrn_no, 'MRN/2023/001')
        self.assertEqual(self.mrn1.generated_by_employee, self.employee1)
        self.assertEqual(self.mrn1.comp_id, self.company)
        self.assertEqual(self.mrn1.fin_year_id, self.fin_year)

    def test_fin_year_display_property(self):
        self.assertEqual(self.mrn1.fin_year_display, '2023-2024')
        # Test case for null fin_year_id
        mrn_no_fin_year = MaterialReturnNote.objects.create(
            id=4, 
            comp_id=self.company, 
            fin_year_id=None, 
            sys_date=datetime.date(2023, 4, 1), 
            mrn_no='MRN/2023/004', 
            generated_by_employee=self.employee3
        )
        self.assertEqual(mrn_no_fin_year.fin_year_display, 'N/A')

    def test_generated_by_display_property(self):
        self.assertEqual(self.mrn1.generated_by_display, 'John Doe')
        # Test case for null generated_by_employee
        mrn_no_gen_by = MaterialReturnNote.objects.create(
            id=5, 
            comp_id=self.company, 
            fin_year_id=self.fin_year, 
            sys_date=datetime.date(2023, 5, 1), 
            mrn_no='MRN/2023/005', 
            generated_by_employee=None
        )
        self.assertEqual(mrn_no_gen_by.generated_by_display, 'N/A')

    def test_get_filtered_notes_mrn_no(self):
        # Filter by MRN No
        filtered_notes = MaterialReturnNote.get_filtered_notes(
            self.company.comp_id, self.fin_year.fin_year_id, '1', 'MRN/2023/001'
        )
        self.assertEqual(filtered_notes.count(), 1)
        self.assertEqual(filtered_notes.first(), self.mrn1)

        # No match
        filtered_notes = MaterialReturnNote.get_filtered_notes(
            self.company.comp_id, self.fin_year.fin_year_id, '1', 'NON_EXISTENT_MRN'
        )
        self.assertEqual(filtered_notes.count(), 0)

    def test_get_filtered_notes_employee_name(self):
        # Filter by Employee Name (using formatted string)
        filtered_notes = MaterialReturnNote.get_filtered_notes(
            self.company.comp_id, self.fin_year.fin_year_id, '0', 'John Doe [101]'
        )
        self.assertEqual(filtered_notes.count(), 2)
        self.assertIn(self.mrn1, filtered_notes)
        self.assertIn(self.mrn3, filtered_notes)

        # No match (invalid employee string format)
        filtered_notes = MaterialReturnNote.get_filtered_notes(
            self.company.comp_id, self.fin_year.fin_year_id, '0', 'Invalid Name'
        )
        self.assertEqual(filtered_notes.count(), 0)

        # No match (non-existent employee ID)
        filtered_notes = MaterialReturnNote.get_filtered_notes(
            self.company.comp_id, self.fin_year.fin_year_id, '0', 'Someone [999]'
        )
        self.assertEqual(filtered_notes.count(), 0)
        
    def test_get_filtered_notes_no_filter_value(self):
        # If no filter value is provided, should return all for the company/fin year
        filtered_notes = MaterialReturnNote.get_filtered_notes(
            self.company.comp_id, self.fin_year.fin_year_id, '0', '' # Employee filter, empty value
        )
        self.assertEqual(filtered_notes.count(), 3)
        self.assertIn(self.mrn1, filtered_notes)
        self.assertIn(self.mrn2, filtered_notes)
        self.assertIn(self.mrn3, filtered_notes)

        filtered_notes = MaterialReturnNote.get_filtered_notes(
            self.company.comp_id, self.fin_year.fin_year_id, '1', '' # MRN filter, empty value
        )
        self.assertEqual(filtered_notes.count(), 3)


class MaterialReturnNoteViewsTest(SetupTestDataMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Set session variables similar to ASP.NET environment
        session = self.client.session
        session['compid'] = self.company.comp_id
        session['finyear'] = self.fin_year.fin_year_id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('material_return_note_print_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/list.html')
        self.assertIsInstance(response.context['search_form'], MaterialReturnNoteSearchForm)
        self.assertEqual(response.context['material_return_notes'].count(), 0) # Should be empty initially, loaded via HTMX

    def test_table_partial_view_get_no_filter(self):
        # HTMX request for the table without specific filters
        response = self.client.get(reverse('material_return_note_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_material_return_note_table.html')
        self.assertIn(self.mrn1, response.context['material_return_notes'])
        self.assertEqual(response.context['material_return_notes'].count(), 3)

    def test_table_partial_view_get_mrn_filter(self):
        # HTMX request with MRN filter
        response = self.client.get(reverse('material_return_note_table_partial'), 
                                   {'search_by': '1', 'mrn_no': 'MRN/2023/001'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(self.mrn1, response.context['material_return_notes'])
        self.assertEqual(response.context['material_return_notes'].count(), 1)

    def test_table_partial_view_get_employee_filter(self):
        # HTMX request with Employee filter
        response = self.client.get(reverse('material_return_note_table_partial'), 
                                   {'search_by': '0', 'employee_name': 'John Doe [101]'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(self.mrn1, response.context['material_return_notes'])
        self.assertIn(self.mrn3, response.context['material_return_notes'])
        self.assertEqual(response.context['material_return_notes'].count(), 2)

    def test_search_fields_partial_view_get_mrn(self):
        # HTMX request to change search fields to MRN
        response = self.client.get(reverse('material_return_note_search_fields_partial'), {'search_by': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_search_fields_partial.html')
        self.assertContains(response, 'name="mrn_no"')
        self.assertNotContains(response, 'name="employee_name"')

    def test_search_fields_partial_view_get_employee(self):
        # HTMX request to change search fields to Employee Name
        response = self.client.get(reverse('material_return_note_search_fields_partial'), {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_search_fields_partial.html')
        self.assertContains(response, 'name="employee_name"')
        self.assertNotContains(response, 'name="mrn_no"')

    def test_employee_autocomplete_view_post(self):
        # HTMX POST request for autocomplete suggestions
        response = self.client.post(reverse('employee_autocomplete'), 
                                    {'employee_name_ac': 'john'}, 
                                    HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_employee_suggestions.html')
        self.assertContains(response, 'John Doe [101]')
        self.assertNotContains(response, 'Jane Smith [102]')

    def test_employee_autocomplete_view_post_no_query(self):
        response = self.client.post(reverse('employee_autocomplete'), 
                                    {'employee_name_ac': ''}, 
                                    HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialreturnnote/_employee_suggestions.html')
        self.assertContains(response, '') # Should be empty or contain empty list

    def test_employee_autocomplete_view_get(self):
        # Should return 204 No Content for GET requests as it's a POST endpoint
        response = self.client.get(reverse('employee_autocomplete'))
        self.assertEqual(response.status_code, 204)

    def test_material_return_note_detail_redirect_view(self):
        # Test the redirect view for 'Select' action
        response = self.client.get(
            reverse('material_return_note_detail_redirect', args=[self.mrn1.id]),
            {'mrn_no': self.mrn1.mrn_no, 'fyid': self.fin_year.fin_year_id}
        )
        self.assertEqual(response.status_code, 200) # Response is an HTML snippet with JS redirect
        self.assertContains(response, 'window.location.href=')
        self.assertContains(response, 'id=1')
        self.assertContains(response, f'mrn_no={self.mrn1.mrn_no}')
        self.assertContains(response, f'fyid={self.fin_year.fin_year_id}')
```

### Step 5: HTMX and Alpine.js Integration

The plan incorporates HTMX for all dynamic interactions and `Alpine.js` for lightweight client-side state management (though in this specific case, HTMX handles most dynamic UI updates).

*   **HTMX for dynamic updates:**
    *   The main list view (`list.html`) uses `hx-get` to load the table content (`_material_return_note_table.html`) into `#mrnTableContainer` on `load` and on a custom `refreshMrnList` event.
    *   The search form uses `hx-get` to trigger a re-render of `_material_return_note_table.html` whenever the form is submitted.
    *   The `search_by` dropdown in the form uses `hx-get` to update the `#search-fields-container` with the correct input field (`_search_fields_partial.html`).
    *   The `employee_name` input uses `hx-post` to send typing input to the `employee_autocomplete` view, updating a `#employee-suggestions-container` div with suggestions.
*   **DataTables:** The `_material_return_note_table.html` partial is designed to be easily initialized with DataTables after HTMX swaps it into the DOM. A JavaScript listener on `htmx:afterSwap` event ensures DataTables is correctly initialized or re-initialized.
*   **Alpine.js:** While not heavily used for complex logic in this conversion (HTMX handles most of the heavy lifting for form submission and partial updates), it's included as a base for future UI enhancements. The `alpine:init` event listener is present, and a simple data component `mrnPrintData` is outlined as an example placeholder.
*   **No full page reloads:** All search, filter, and autocomplete interactions are handled via HTMX, leading to a much smoother user experience without full page refreshes, mimicking and improving upon the ASP.NET AJAX behavior.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the `MaterialReturnNote_MRN_Print` functionality from ASP.NET to Django. By following the "Fat Model, Thin View" principle, utilizing HTMX for dynamic interactions, DataTables for enhanced data presentation, and adhering to strict testing guidelines, the resulting Django application will be modern, maintainable, and scalable. The communication avoids technical jargon, focusing on the steps and benefits for business stakeholders. The entire process is designed to be automation-friendly, ensuring that subsequent migrations can follow a similar, predictable pattern.