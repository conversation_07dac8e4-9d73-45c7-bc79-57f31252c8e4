## ASP.NET to Django Conversion Script: Material Credit Note Module

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

Based on the provided minimal ASP.NET code, which primarily defines a page structure and an empty `Page_Load` method, we will infer a common "Material Credit Note" entity and its associated CRUD (Create, Read, Update, Delete) operations. This approach demonstrates how to translate a typical ASP.NET data entry/editing page into a modern Django application, even with limited initial code.

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the ASP.NET `Module_Inventory_Transactions_MaterialCreditNote_MCN_Edit.aspx` page, we infer the primary entity is `Material Credit Note`. For demonstration purposes, we will assume a corresponding database table named `MaterialCreditNotes` with the following columns:

*   `MCN_Id` (Primary Key, integer)
*   `NoteNumber` (String, unique identifier for the credit note)
*   `IssueDate` (Date, when the note was issued)
*   `CustomerName` (String, who the credit note is for)
*   `Amount` (Decimal, the credit amount)
*   `Status` (String, e.g., 'Draft', 'Approved', 'Cancelled')
*   `CreatedBy` (String)
*   `CreatedAt` (DateTime)
*   `UpdatedBy` (String)
*   `UpdatedAt` (DateTime)

**Inferred Database Details:**
*   **[TABLE_NAME]:** `MaterialCreditNotes`
*   **Columns:** `MCN_Id`, `NoteNumber`, `IssueDate`, `CustomerName`, `Amount`, `Status`, `CreatedBy`, `CreatedAt`, `UpdatedBy`, `UpdatedAt`

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Although the provided ASP.NET code-behind is empty, the page name `MCN_Edit.aspx` suggests an "Edit" functionality. In a complete application, this would typically be part of a full CRUD workflow. We will plan for the following standard operations:

*   **Create:** Adding a new Material Credit Note.
*   **Read (List):** Displaying a list of all Material Credit Notes.
*   **Read (Detail/Edit):** Retrieving and displaying details of a specific Material Credit Note for editing.
*   **Update:** Modifying an existing Material Credit Note.
*   **Delete:** Removing a Material Credit Note.

**Inferred Functionality:**
*   **Create:** Handled via a form submission for new entries.
*   **Read:** Data will be presented in a list view using DataTables. Individual records will be loaded into an edit form.
*   **Update:** Handled via a form submission for existing entries.
*   **Delete:** Handled via a confirmation prompt.
*   **Validation Logic:** We will implement basic form validation (e.g., required fields, data type checks) within the Django Form.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
A typical ASP.NET "Edit" page often uses `TextBox` controls for text input, `Calendar` or `DatePicker` for dates, and `DropDownList` for selections. A list view would commonly use a `GridView`.

**Inferred UI Components for Django:**
*   **List View:** A table element (`<table>`) rendered in Django, enhanced with DataTables for client-side interactivity (searching, sorting, pagination).
*   **Form (Create/Edit):** Standard HTML input elements (`<input type="text">`, `<input type="date">`, `<input type="number">`, `<select>`) rendered by Django Forms, styled with Tailwind CSS.
*   **Buttons:** Standard HTML `<button>` elements for actions (Add, Edit, Delete, Save, Cancel), integrated with HTMX for dynamic interactions (e.g., loading forms into modals).
*   **Modals:** Used for create, edit, and delete forms, managed by Alpine.js for visibility and HTMX for content loading.

---

### Step 4: Generate Django Code

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
We will define the `MaterialCreditNote` model, mapping directly to the `MaterialCreditNotes` table.

**`inventory/models.py`**
```python
from django.db import models
from django.utils import timezone

class MaterialCreditNote(models.Model):
    mcn_id = models.AutoField(db_column='MCN_Id', primary_key=True)
    note_number = models.CharField(db_column='NoteNumber', max_length=50, unique=True, verbose_name="Note Number")
    issue_date = models.DateField(db_column='IssueDate', verbose_name="Issue Date")
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name="Customer Name")
    amount = models.DecimalField(db_column='Amount', max_digits=10, decimal_places=2, verbose_name="Amount")
    status = models.CharField(db_column='Status', max_length=50, default='Draft', verbose_name="Status")
    created_by = models.CharField(db_column='CreatedBy', max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(db_column='CreatedAt', auto_now_add=True)
    updated_by = models.CharField(db_column='UpdatedBy', max_length=100, blank=True, null=True)
    updated_at = models.DateTimeField(db_column='UpdatedAt', auto_now=True)

    class Meta:
        managed = False  # Important: Django won't manage this table's schema
        db_table = 'MaterialCreditNotes'
        verbose_name = 'Material Credit Note'
        verbose_name_plural = 'Material Credit Notes'
        ordering = ['-issue_date', 'note_number']

    def __str__(self):
        return f"{self.note_number} - {self.customer_name}"

    # Business logic methods (Fat Model approach)
    def update_status(self, new_status, updated_by_user):
        """
        Updates the status of the material credit note.
        Example of business logic within the model.
        """
        if new_status not in ['Draft', 'Approved', 'Cancelled', 'Issued']:
            raise ValueError("Invalid status provided.")
        self.status = new_status
        self.updated_by = updated_by_user
        self.updated_at = timezone.now()
        self.save()
        return True

    def calculate_total_amount(self):
        """
        Example method to calculate total amount, if there were line items.
        For simplicity, it returns the current amount.
        """
        # In a real scenario, this might sum line item amounts
        return self.amount

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for `MaterialCreditNote`, including common fields and styled with Tailwind CSS classes.

**`inventory/forms.py`**
```python
from django import forms
from .models import MaterialCreditNote

class MaterialCreditNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialCreditNote
        fields = ['note_number', 'issue_date', 'customer_name', 'amount', 'status']
        widgets = {
            'note_number': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Note Number'
            }),
            'issue_date': forms.DateInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'date'
            }),
            'customer_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Customer Name'
            }),
            'amount': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'step': '0.01'
            }),
            'status': forms.Select(choices=[
                ('Draft', 'Draft'),
                ('Approved', 'Approved'),
                ('Cancelled', 'Cancelled'),
                ('Issued', 'Issued')
            ], attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }
        labels = {
            'note_number': 'Credit Note Number',
            'issue_date': 'Date Issued',
            'customer_name': 'Customer',
            'amount': 'Credit Amount',
            'status': 'Current Status',
        }

    # Custom validation example
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive value.")
        return amount

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
We will create `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the `MaterialCreditNote`. Additionally, a specific `ListView` for the HTMX-loaded table partial is needed. Views will be kept thin, delegating business logic to the model.

**`inventory/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialCreditNote
from .forms import MaterialCreditNoteForm

# Base view for Material Credit Note list
class MaterialCreditNoteListView(ListView):
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/list.html'
    context_object_name = 'materialcreditnotes' # This will be the main context variable for the list view

# Partial view for the DataTables table, loaded via HTMX
class MaterialCreditNoteTablePartialView(ListView):
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/_materialcreditnote_table.html'
    context_object_name = 'materialcreditnotes' # Context for the partial template

    def get_queryset(self):
        # Example of applying ordering or filtering if needed, though DataTables handles much of this client-side
        return MaterialCreditNote.objects.all()

class MaterialCreditNoteCreateView(CreateView):
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html' # Use partial template for modal
    success_url = reverse_lazy('materialcreditnote_list') # Redirect to list view on success

    def form_valid(self, form):
        # Example of adding auto-filled fields like created_by
        # In a real app, request.user.username would be used
        form.instance.created_by = 'System' # Placeholder for current user
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,  # No Content, tells HTMX not to swap anything
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList' # Trigger custom event to refresh list
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If HX-Request, render the form again with errors
            return response
        return response

class MaterialCreditNoteUpdateView(UpdateView):
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html' # Use partial template for modal
    success_url = reverse_lazy('materialcreditnote_list')

    def form_valid(self, form):
        # Example of updating auto-filled fields
        form.instance.updated_by = 'System' # Placeholder for current user
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class MaterialCreditNoteDeleteView(DeleteView):
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/confirm_delete.html' # Use partial template for modal
    success_url = reverse_lazy('materialcreditnote_list')

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        # Optional: Add business logic in model before deletion, e.g., obj.can_delete()
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Material Credit Note "{obj.note_number}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
These templates are designed for HTMX interaction, allowing for dynamic modal loading and partial updates without full page reloads.

**`inventory/templates/inventory/materialcreditnote/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Credit Notes</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialcreditnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then remove .hidden from #modal">
            Add New Credit Note
        </button>
    </div>
    
    <div id="materialcreditnoteTable-container"
         hx-trigger="load, refreshMaterialCreditNoteList from:body"
         hx-get="{% url 'materialcreditnote_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Material Credit Notes...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .flex from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 sm:mx-0 transition-transform transform scale-95 duration-200">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
    });

    // Handle messages (toasts) triggered by HX-Trigger-After-Swap
    document.body.addEventListener('htmx:afterSettle', function(event) {
        if (event.detail.target && event.detail.target.id === 'modalContent') {
            // If the modal content is swapped, ensure the modal is visible
            document.getElementById('modal').classList.remove('hidden');
            document.getElementById('modal').classList.add('flex');
        }
        // General message handling (e.g., from HX-Trigger 'refreshMaterialCreditNoteList')
        // We assume Django messages are handled by the base.html,
        // or a specific HTMX component that listens to a 'showMessage' trigger.
    });

    // Close modal on 'refreshMaterialCreditNoteList' trigger
    document.body.addEventListener('refreshMaterialCreditNoteList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('flex');
            modal.classList.add('hidden');
        }
    });

</script>
{% endblock %}

```

**`inventory/templates/inventory/materialcreditnote/_materialcreditnote_table.html`**
```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="materialcreditnoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Note Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in materialcreditnotes %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.note_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.issue_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.customer_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">${{ obj.amount|floatformat:2 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.status == 'Approved' %}bg-green-100 text-green-800
                        {% elif obj.status == 'Cancelled' %}bg-red-100 text-red-800
                        {% elif obj.status == 'Issued' %}bg-blue-100 text-blue-800
                        {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ obj.status }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'materialcreditnote_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg transition duration-300 ease-in-out"
                        hx-get="{% url 'materialcreditnote_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-4 text-center text-gray-500">No Material Credit Notes found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Ensure DataTables is initialized only once and correctly on HTMX re-load
// This script runs every time the partial is loaded, so we need to destroy existing DataTable instance if any.
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#materialcreditnoteTable')) {
        $('#materialcreditnoteTable').DataTable().destroy();
    }
    $('#materialcreditnoteTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 6] }, // Disable sorting for SN and Actions columns
            { "searchable": false, "targets": [0, 6] } // Disable searching for SN and Actions columns
        ]
    });
});
</script>
```

**`inventory/templates/inventory/materialcreditnote/_materialcreditnote_form.html`**
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Material Credit Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Material Credit Note
            </button>
        </div>

        <div id="form-spinner" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
    </form>
</div>
```

**`inventory/templates/inventory/materialcreditnote/confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Material Credit Note: <strong>{{ object.note_number }}</strong>?</p>
    
    <form hx-post="{% url 'materialcreditnote_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <input type="hidden" name="_method" value="DELETE"> {# For HTMX delete method #}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Confirm Delete
            </button>
        </div>
        <div id="delete-spinner" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs will be defined to map to the respective Django views.

**`inventory/urls.py`**
```python
from django.urls import path
from .views import (
    MaterialCreditNoteListView, 
    MaterialCreditNoteCreateView, 
    MaterialCreditNoteUpdateView, 
    MaterialCreditNoteDeleteView,
    MaterialCreditNoteTablePartialView, # Added for HTMX table loading
)

urlpatterns = [
    path('materialcreditnotes/', MaterialCreditNoteListView.as_view(), name='materialcreditnote_list'),
    path('materialcreditnotes/add/', MaterialCreditNoteCreateView.as_view(), name='materialcreditnote_add'),
    path('materialcreditnotes/edit/<int:pk>/', MaterialCreditNoteUpdateView.as_view(), name='materialcreditnote_edit'),
    path('materialcreditnotes/delete/<int:pk>/', MaterialCreditNoteDeleteView.as_view(), name='materialcreditnote_delete'),
    # HTMX-specific endpoint for refreshing the table
    path('materialcreditnotes/table/', MaterialCreditNoteTablePartialView.as_view(), name='materialcreditnote_table'),
]

```
**Project's `urls.py` (e.g., `AutoERP/urls.py`)**
```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('inventory/', include('inventory.urls')), # Include your app's URLs
    # Add other project-level URL patterns here
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for the model and integration tests for all view actions, including HTMX interactions, will be included.

**`inventory/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialCreditNote
from datetime import date
from django.utils import timezone

class MaterialCreditNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.mcn1 = MaterialCreditNote.objects.create(
            note_number='MCN001',
            issue_date=date(2023, 1, 15),
            customer_name='ABC Corp',
            amount=1500.75,
            status='Approved',
            created_by='TestUser',
            updated_by='TestUser'
        )
        cls.mcn2 = MaterialCreditNote.objects.create(
            note_number='MCN002',
            issue_date=date(2023, 2, 20),
            customer_name='XYZ Ltd',
            amount=500.00,
            status='Draft',
            created_by='TestUser',
            updated_by='TestUser'
        )
  
    def test_materialcreditnote_creation(self):
        self.assertEqual(self.mcn1.note_number, 'MCN001')
        self.assertEqual(self.mcn1.customer_name, 'ABC Corp')
        self.assertEqual(self.mcn1.amount, 1500.75)
        self.assertEqual(self.mcn1.status, 'Approved')
        self.assertIsNotNone(self.mcn1.created_at)
        self.assertIsNotNone(self.mcn1.updated_at)
        
    def test_note_number_label(self):
        field_label = self.mcn1._meta.get_field('note_number').verbose_name
        self.assertEqual(field_label, 'Note Number')
        
    def test_str_method(self):
        self.assertEqual(str(self.mcn1), 'MCN001 - ABC Corp')

    def test_update_status_method(self):
        initial_status = self.mcn2.status
        self.mcn2.update_status('Approved', 'Admin')
        self.mcn2.refresh_from_db() # Refresh instance from DB to get latest state
        self.assertEqual(self.mcn2.status, 'Approved')
        self.assertEqual(self.mcn2.updated_by, 'Admin')
        self.assertGreater(self.mcn2.updated_at, initial_status) # Ensure updated_at changed

        with self.assertRaises(ValueError):
            self.mcn1.update_status('InvalidStatus', 'Admin')

    def test_calculate_total_amount_method(self):
        self.assertEqual(self.mcn1.calculate_total_amount(), 1500.75)


class MaterialCreditNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.mcn = MaterialCreditNote.objects.create(
            note_number='MCN_TEST_001',
            issue_date=date(2023, 3, 10),
            customer_name='Test Customer',
            amount=999.99,
            status='Draft',
            created_by='TestUser',
            updated_by='TestUser'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('materialcreditnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/list.html')
        self.assertIn('materialcreditnotes', response.context) # Check context for the list object
        self.assertContains(response, 'Material Credit Notes') # Check for title
        
    def test_table_partial_view_get(self):
        response = self.client.get(reverse('materialcreditnote_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_table.html')
        self.assertIn('materialcreditnotes', response.context)
        self.assertContains(response, self.mcn.note_number) # Check if object is in the table

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialcreditnote_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Material Credit Note') # Check for form title

    def test_create_view_post_htmx_success(self):
        initial_count = MaterialCreditNote.objects.count()
        data = {
            'note_number': 'MCN_NEW_003',
            'issue_date': '2023-04-01',
            'customer_name': 'New Customer Inc',
            'amount': '1234.56',
            'status': 'Draft',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcreditnote_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialCreditNoteList')
        self.assertEqual(MaterialCreditNote.objects.count(), initial_count + 1)
        self.assertTrue(MaterialCreditNote.objects.filter(note_number='MCN_NEW_003').exists())
        
    def test_create_view_post_htmx_invalid_form(self):
        initial_count = MaterialCreditNote.objects.count()
        data = {
            'note_number': '', # Invalid: required field
            'issue_date': '2023-04-01',
            'customer_name': 'New Customer Inc',
            'amount': '-100.00', # Invalid: custom validation
            'status': 'Draft',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcreditnote_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Amount must be a positive value.')
        self.assertEqual(MaterialCreditNote.objects.count(), initial_count) # No new object created

    def test_update_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialcreditnote_edit', args=[self.mcn.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.mcn)
        self.assertContains(response, 'Edit Material Credit Note') # Check for form title
        
    def test_update_view_post_htmx_success(self):
        data = {
            'note_number': 'MCN_UPDATED_001',
            'issue_date': '2023-03-10', # Same date
            'customer_name': 'Updated Customer',
            'amount': '1111.11',
            'status': 'Approved',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcreditnote_edit', args=[self.mcn.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialCreditNoteList')
        self.mcn.refresh_from_db()
        self.assertEqual(self.mcn.note_number, 'MCN_UPDATED_001')
        self.assertEqual(self.mcn.customer_name, 'Updated Customer')
        
    def test_update_view_post_htmx_invalid_form(self):
        data = {
            'note_number': '', # Invalid
            'issue_date': '2023-03-10',
            'customer_name': 'Updated Customer',
            'amount': '100.00',
            'status': 'Approved',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcreditnote_edit', args=[self.mcn.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertContains(response, 'This field is required.')
        self.mcn.refresh_from_db() # Ensure object was not updated
        self.assertNotEqual(self.mcn.note_number, '')

    def test_delete_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialcreditnote_delete', args=[self.mcn.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertContains(response, f'Are you sure you want to delete the Material Credit Note: {self.mcn.note_number}?')

    def test_delete_view_post_htmx_success(self):
        mcn_to_delete = MaterialCreditNote.objects.create(
            note_number='MCN_TO_DELETE',
            issue_date=date(2023, 5, 1),
            customer_name='Delete Me Inc',
            amount=100.00,
            status='Draft',
            created_by='TestUser',
            updated_by='TestUser'
        )
        initial_count = MaterialCreditNote.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialcreditnote_delete', args=[mcn_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialCreditNoteList')
        self.assertEqual(MaterialCreditNote.objects.count(), initial_count - 1)
        self.assertFalse(MaterialCreditNote.objects.filter(pk=mcn_to_delete.pk).exists())
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates and views are structured to leverage HTMX for dynamic content loading and form submissions. Alpine.js is implicitly used for modal management via the `_=` attributes in HTML.

*   **Modal Handling:** All `Create`, `Update`, and `Delete` forms are loaded into a single modal (`#modal`) using `hx-get` on buttons. The modal's visibility is controlled by Alpine.js-like `_=` attributes (`on click add .flex to #modal then remove .hidden from #modal`).
*   **Form Submission:** Forms use `hx-post` to submit data back to the same URL that loaded the form (`request.path`). Upon successful submission, the view returns a `204 No Content` status with an `HX-Trigger: refreshMaterialCreditNoteList` header.
*   **List Refresh:** The `materialcreditnoteTable-container` in `list.html` listens for `refreshMaterialCreditNoteList` event (`hx-trigger="load, refreshMaterialCreditNoteList from:body"`). When triggered, it re-fetches the table content from `{% url 'materialcreditnote_table' %}` via `hx-get` and swaps the inner HTML, effectively refreshing the DataTables instance.
*   **DataTables Initialization:** The `_materialcreditnote_table.html` partial includes a `<script>` block that re-initializes DataTables every time the partial is loaded. This handles the dynamic loading correctly by destroying any prior instance before creating a new one.
*   **Loading Indicators:** `hx-indicator` attributes (`#form-spinner`, `#delete-spinner`) are used on forms to display a visual loading spinner during HTMX requests, providing immediate feedback to the user.
*   **User Feedback (Messages):** Django's messages framework is used (`messages.success`). While the example assumes `base.html` handles general message display, the `HX-Trigger` on success allows for custom client-side toast notifications if needed.

---

### Final Notes

*   This comprehensive plan demonstrates the automated conversion process, moving from a minimal ASP.NET page to a full-fledged Django module with modern frontend techniques (HTMX, Alpine.js, DataTables).
*   All placeholders have been replaced with concrete examples based on the inferred `MaterialCreditNote` entity.
*   Emphasis is placed on the "Fat Model, Thin View" principle, strict separation of concerns, and DRY code.
*   The use of `managed = False` in the model is critical for integrating with existing databases without Django attempting to manage the schema.
*   Extensive tests are provided for both model business logic and view interactions, ensuring high test coverage.
*   This structure is highly scalable and maintainable, offering a robust foundation for further modernization efforts.