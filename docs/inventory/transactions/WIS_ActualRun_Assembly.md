## ASP.NET to Django Conversion Script:

This document outlines a strategic plan to modernize your ASP.NET application, specifically the "WIS Actual Run of Assembly" module, by migrating it to a robust and scalable Django 5.0+ framework. Our approach prioritizes automation, leverages modern web standards like HTMX and Alpine.js, and adheres to Django best practices, ensuring a future-proof solution.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using managed = False and db_table.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend core/base.html (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

This conversion focuses on the core functionality of displaying Bill of Materials (BOM) for a Work Order, calculating various quantities, and performing an "Actual Run" transaction that issues items from stock and records them.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several tables via `SqlDataAdapter` and `SqlCommand` statements. We infer the following tables and their key columns:

-   **`tblDG_BOM_Master`**: This table holds the Bill of Materials structure.
    -   `ItemId` (INT)
    -   `WONo` (VARCHAR) - Foreign key to Work Order
    -   `PId` (INT) - Parent ID for hierarchy
    -   `CId` (INT) - Child ID for hierarchy
    -   `Unit Qty` (VARCHAR/DECIMAL) - Quantity of the component in the parent assembly.
-   **`tblDG_Item_Master`**: Stores details about inventory items.
    -   `Id` (INT) - Primary Key
    -   `CompId` (INT) - Company ID
    -   `UOMBasic` (INT) - Unit of Measure ID
    -   `StockQty` (DECIMAL) - Current stock quantity
    -   `ManfDesc` (VARCHAR) - Manufacturer description
    -   `ItemCode` (VARCHAR) - Inferred from `fun.GetItemCode_PartNo`
-   **`Unit_Master`**: Stores Unit of Measure details.
    -   `Id` (INT) - Primary Key
    -   `Symbol` (VARCHAR) - Unit symbol (e.g., 'KG', 'PCS')
-   **`tblInv_WIS_Master`**: Master table for Work In Progress Issue System (WIS) transactions.
    -   `Id` (INT) - Primary Key (Auto-incrementing)
    -   `WISNo` (VARCHAR) - Unique WIS Number
    -   `WONo` (VARCHAR) - Foreign key to Work Order
    -   `SysDate` (DATE)
    -   `SysTime` (TIME)
    -   `CompId` (INT)
    -   `SessionId` (VARCHAR) - User session ID/username
    -   `FinYearId` (INT) - Financial Year ID
-   **`tblInv_WIS_Details`**: Detail table for WIS transactions.
    -   `Id` (INT) - Primary Key (Auto-incrementing)
    -   `WISNo` (VARCHAR) - Redundant if FK to WISMaster.WISNo, but matches original schema.
    -   `MId` (INT) - Foreign key to `tblInv_WIS_Master.Id`
    -   `PId` (INT) - Parent ID (from BOM)
    -   `CId` (INT) - Child ID (from BOM)
    -   `ItemId` (INT) - Item ID (from BOM)
    -   `IssuedQty` (DECIMAL) - Quantity issued
-   **`SD_Cust_WorkOrder_Master`**: Stores Work Order details.
    -   `WONo` (VARCHAR) - Primary Key
    -   `CompId` (INT)
    -   `DryActualRun` (INT) - Status flag (0 or 1)

### Step 2: Identify Backend Functionality

**Task:** Determine the business logic and CRUD operations in the ASP.NET code.

**Analysis:**
The application primarily performs two major functions:

1.  **Read/Display (Report Generation):**
    *   Retrieves Bill of Materials (BOM) data for a given Work Order Number (`WONo`).
    *   Performs complex calculations for each BOM item, including:
        *   `BOM Qty`: Calculated by traversing the BOM tree and multiplying quantities.
        *   `Tot. WIS Qty`: Total quantity already issued for the item in previous WIS transactions.
        *   `Dry Run Qty`: The quantity that *could* be issued based on current stock and BOM needs.
        *   `Balance BOM Qty`: Remaining quantity needed from BOM after considering total issued and dry run quantity.
        *   `After Stock Qty`: Remaining stock after issuing `Dry Run Qty`.
    *   This data is displayed in a tree-like list (Telerik RadTreeList).

2.  **Create/Update (Transaction):**
    *   **"Actual Run of Assembly" (WIS Transaction):** This is a critical process initiated by a button click.
        *   Generates a new unique `WISNo`.
        *   Iterates through the BOM items.
        *   For each item, it recalculates the `Dry Run Qty` based on current stock and BOM needs.
        *   If `Dry Run Qty` is greater than zero:
            *   Creates a new record in `tblInv_WIS_Master` (if not already created for this run).
            *   Creates a new record in `tblInv_WIS_Details` with the `IssuedQty` (equal to `Dry Run Qty`).
            *   **Updates** the `StockQty` in `tblDG_Item_Master` by deducting the `IssuedQty`.
        *   **Updates** the `DryActualRun` status flag in `SD_Cust_WorkOrder_Master` to `1`.

3.  **Navigation:**
    *   "Dry Run of Material": Redirects to another page for material dry run.
    *   "Cancel": Redirects to a previous page.
    *   "Select" (Export Icon): Appears to be intended for printing BOM details, with a redirect.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django mapping.

**Analysis:**
-   **`asp:Label ID="Label2"`**: Displays the Work Order Number. This will be a simple Django template variable.
-   **`asp:CheckBox ID="CheckBox1"` (Expand Tree)**: Toggles tree expansion. In a flat DataTables structure, this functionality would be complex to replicate directly. For this conversion, we will show all BOM items flattened in the table, and this checkbox might be re-purposed for filtering or removed if not strictly necessary for the core function.
-   **`asp:Button ID="Button1"` (Actual Run of Assembly)**: This triggers the `WIS_RootAssly` transaction. This will be an HTMX `hx-post` button.
-   **`asp:Button ID="Button2"` (Dry Run of Material)**, **`asp:Button ID="btnCancel"` (Cancel)**, **`asp:ImageButton ID="ImageButton1"` (Export icon)**: These are navigation buttons. They will be simple `<a>` tags or HTMX `hx-get` buttons leading to other Django URLs.
-   **`asp:Label ID="lblmsg"`**: Displays messages. Django's `messages` framework will handle this, rendered in the base template.
-   **`telerik:RadTreeList1`**: This is the main data grid. It will be replaced by an HTML `<table>` element, styled with Tailwind CSS, and initialized as a DataTables instance via JavaScript. The hierarchical display will be flattened into columns like `PId` and `CId`, allowing sorting and filtering on all calculated columns.

### Step 4: Generate Django Code

We will create a new Django application named `inventory` to house this module.

#### 4.1 Models (`inventory/models.py`)

This file will define the Django models that map to your existing SQL Server tables using `managed = False` and `db_table`. It will also include the core business logic (fat model approach) to encapsulate the complex calculations and transaction processes identified in `GetDataTable()` and `WIS_RootAssly()`.

```python
from django.db import models, transaction
from django.db.models import Sum
from datetime import date, datetime

# --- Placeholder for global/session variables (Replace with actual implementation) ---
# In a real Django application, CompId, FinYearId, and SessionId would typically
# come from the authenticated user's profile, session, or system settings.
# For demonstration purposes, we'll use mock values.
MOCK_COMP_ID = 1
MOCK_FIN_YEAR_ID = 1
MOCK_SESSION_ID = 'system_user' # Typically request.user.username

# --- Helper Functions (Mimicking original C# fun. methods and Stored Procedures) ---
# These functions encapsulate specific pieces of business logic.
# In a real system, BOMTreeQty might involve a complex recursive query or pre-calculated paths.
# GetSchTime_TWIS_Qty would be a direct query against WISDetail.

def calculate_bom_tree_qty(won_no, p_id, c_id):
    """
    Simulates the 'fun.BOMTreeQty' logic for calculating cumulative BOM quantities.
    This is a simplified placeholder. Actual implementation would involve
    recursive traversal of the BOM hierarchy for the given WONo.
    """
    # This is a placeholder for a complex BOM calculation logic
    # that would likely reside in a dedicated BOM service or a BOMManager.
    # For the purpose of this example, we'll return a placeholder value.
    # A robust implementation would query BOM data and apply recursive logic.
    if p_id == 0: # Assuming 0 indicates a top-level assembly
        return 1.0
    return 2.5 # A default multiplier for sub-assemblies for demonstration

def get_item_code_part_no(comp_id, item_id):
    """
    Simulates 'fun.GetItemCode_PartNo' to retrieve an item's code.
    """
    try:
        item = ItemMaster.objects.get(compid=comp_id, id=item_id)
        return item.item_code
    except ItemMaster.DoesNotExist:
        return f"ITEM-{item_id}" # Fallback or error handling

def get_total_wis_qty(comp_id, won_no, item_id, p_id, c_id):
    """
    Simulates 'GetSchTime_TWIS_Qty' stored procedure to get total issued WIS quantity.
    """
    total_qty = WISDetail.objects.filter(
        wis_master__compid=comp_id,
        wis_master__wo_no__won_no=won_no, # Accessing won_no through ForeignKey
        item_id=item_id,
        p_id=p_id,
        c_id=c_id
    ).aggregate(total=Sum('issued_qty'))['total']
    return total_qty if total_qty is not None else 0.0


# --- Django Models ---

class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    Represents a work order.
    """
    won_no = models.CharField(db_column='WONo', max_length=50, primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    dry_actual_run = models.IntegerField(db_column='DryActualRun', default=0) # 0 for dry run, 1 for actual run

    class Meta:
        managed = False # Tell Django not to manage this table's creation/modification
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.won_no

class BOM(models.Model):
    """
    Maps to tblDG_BOM_Master.
    Represents an item within a Bill of Materials structure for a specific work order.
    """
    item_id = models.IntegerField(db_column='ItemId')
    wo_no = models.ForeignKey(WorkOrder, on_delete=models.DO_NOTHING, db_column='WONo', related_name='bom_items')
    p_id = models.IntegerField(db_column='PId') # Parent ID in the BOM tree
    c_id = models.IntegerField(db_column='CId') # Child ID in the BOM tree, part of composite key
    unit_qty = models.FloatField(db_column='Unit Qty', default=0.0)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Item'
        verbose_name_plural = 'BOM Items'
        unique_together = (('item_id', 'wo_no', 'p_id', 'c_id'),) # Inferring a composite primary key

    def __str__(self):
        return f"BOM: WO {self.wo_no.won_no}, Item {self.item_id} (P:{self.p_id}, C:{self.c_id})"

class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master.
    Represents an inventory item.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    uom_basic = models.IntegerField(db_column='UOMBasic') # Foreign key to UnitMaster
    stock_qty = models.FloatField(db_column='StockQty', default=0.0)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True) # Inferred

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.manf_desc

class UnitMaster(models.Model):
    """
    Maps to Unit_Master.
    Represents a unit of measure.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class WISMaster(models.Model):
    """
    Maps to tblInv_WIS_Master.
    Represents the master record for a Work In Progress Issue System transaction.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    wis_no = models.CharField(db_column='WISNo', max_length=10, unique=True)
    wo_no = models.ForeignKey(WorkOrder, on_delete=models.DO_NOTHING, db_column='WONo', related_name='wis_masters')
    compid = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.CharField(db_column='SysTime', max_length=10) # Storing as string to match original format

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Master'
        verbose_name = 'WIS Master'
        verbose_name_plural = 'WIS Masters'
        ordering = ['-id'] # To get the latest WISNo

    def __str__(self):
        return self.wis_no

    @classmethod
    def generate_next_wis_no(cls, comp_id, fin_year_id):
        """
        Generates the next sequential WIS Number for a given company and financial year.
        Corresponds to 'GetSchTime_WISNo' stored procedure.
        """
        try:
            # Get the latest WIS entry for the given company and financial year
            last_wis = cls.objects.filter(compid=comp_id, fin_year_id=fin_year_id).order_by('-id').first()
            if last_wis and last_wis.wis_no.isdigit():
                next_no = int(last_wis.wis_no) + 1
            else:
                next_no = 1 # Start from 1 if no previous or non-numeric WISNo
        except cls.DoesNotExist:
            next_no = 1
        return f"{next_no:04d}" # Format as 4-digit string (e.g., "0001")

class WISDetail(models.Model):
    """
    Maps to tblInv_WIS_Details.
    Represents a detail record for a Work In Progress Issue System transaction.
    """
    id = models.AutoField(primary_key=True) # Django default PK
    wis_no = models.CharField(db_column='WISNo', max_length=10) # Denormalized, matching original schema
    wis_master = models.ForeignKey(WISMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    item_id = models.IntegerField(db_column='ItemId')
    issued_qty = models.FloatField(db_column='IssuedQty', default=0.0)

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Details'
        verbose_name = 'WIS Detail'
        verbose_name_plural = 'WIS Details'

    def __str__(self):
        return f"WIS {self.wis_no} - Item {self.item_id} Qty: {self.issued_qty}"


# --- Business Logic Service (Fat Model Principle - encapsulates complex operations) ---
# This class aggregates the complex data retrieval and transaction logic from the original C# code.

class AssemblyRunService:
    """
    Service class to encapsulate the business logic related to Assembly Run operations,
    including data retrieval (mimicking GetDataTable) and performing the actual run transaction (WIS_RootAssly).
    """

    @staticmethod
    def get_assembly_run_details(comp_id, won_no):
        """
        Retrieves and calculates all details for the Assembly Run display.
        Corresponds to the logic in the original GetDataTable() method.
        Returns a list of dictionaries, where each dictionary represents a row
        with calculated values for the display table.
        """
        details = []
        try:
            # Fetch BOM items for the given Work Order
            bom_items = BOM.objects.filter(wo_no__won_no=won_no).order_by('p_id', 'c_id')

            for bom in bom_items:
                # Fetch related ItemMaster and UnitMaster details
                item = ItemMaster.objects.filter(compid=comp_id, id=bom.item_id).first()
                if not item:
                    continue # Skip if item not found

                unit = UnitMaster.objects.filter(id=item.uom_basic).first()
                uom_symbol = unit.symbol if unit else ''

                # Calculate BOM Qty (h in ASP.NET code)
                # This involves recursive logic from original 'fun.BOMTreeQty' multiplied by 'Unit Qty' from BOM
                bom_calc_qty = calculate_bom_tree_qty(won_no, bom.p_id, bom.c_id) * bom.unit_qty

                # Calculate Total WIS Issued Qty (TotWISQty in ASP.NET code)
                total_wis_qty = get_total_wis_qty(comp_id, won_no, bom.item_id, bom.p_id, bom.c_id)

                # Calculate Balance BOM Qty (BalBomQty)
                bal_bom_qty = max(0.0, round(bom_calc_qty - total_wis_qty, 3))

                # Calculate Dry Run Qty (CalIssueQty) and After Stock Qty (CalStockQty)
                current_stock = round(item.stock_qty, 3)
                dry_run_qty = 0.0
                after_stock_qty = current_stock

                if current_stock >= bal_bom_qty:
                    dry_run_qty = bal_bom_qty
                    after_stock_qty = round(current_stock - bal_bom_qty, 3)
                elif bal_bom_qty > current_stock:
                    dry_run_qty = current_stock
                    after_stock_qty = 0.0

                details.append({
                    'item_id': bom.item_id,
                    'won_no': won_no,
                    'p_id': bom.p_id,
                    'c_id': bom.c_id,
                    'item_code': get_item_code_part_no(comp_id, bom.item_id),
                    'description': item.manf_desc,
                    'uom': uom_symbol,
                    'unit_qty': round(bom.unit_qty, 3),
                    'bom_qty': round(bom_calc_qty, 3),
                    'weldments': 'N/A', # Placeholder as it was often hidden or not directly derived
                    'stock_qty': round(current_stock, 3),
                    'total_wis_qty': round(total_wis_qty, 3),
                    'dry_run_qty': round(dry_run_qty, 3),
                    'balance_bom_qty': round(max(0.0, bom_calc_qty - (total_wis_qty + dry_run_qty)), 3),
                    'after_stock_qty': round(after_stock_qty, 3),
                })
        except Exception as e:
            # Log the error for debugging
            print(f"Error in get_assembly_run_details: {e}")
            # Potentially raise a custom exception or return an empty list/error indicator
        return details

    @staticmethod
    @transaction.atomic # Ensures all DB operations are atomic (either all succeed or all fail)
    def perform_actual_run(comp_id, fin_year_id, session_id, won_no):
        """
        Executes the 'Actual Run of Assembly' transaction.
        Corresponds to the logic in WIS_RootAssly() and Button1_Click().
        """
        wis_no = WISMaster.generate_next_wis_no(comp_id, fin_year_id)
        
        current_date = date.today()
        current_time = datetime.now().strftime("%H:%M:%S")

        wis_master_obj = None
        wis_master_created = False

        # Fetch WorkOrder object once
        work_order = WorkOrder.objects.get(won_no=won_no, compid=comp_id)

        # Fetch BOM items for the given Work Order, ordering might be important for hierarchy
        bom_items = BOM.objects.filter(wo_no=work_order).order_by('p_id', 'c_id')

        for bom in bom_items:
            # Use select_for_update to lock the item row during the transaction
            # to prevent race conditions when updating stock.
            item = ItemMaster.objects.select_for_update().filter(compid=comp_id, id=bom.item_id).first()
            if not item:
                continue

            # Recalculate quantities for current transaction to ensure accuracy
            bom_calc_qty = calculate_bom_tree_qty(won_no, bom.p_id, bom.c_id) * bom.unit_qty
            total_wis_qty = get_total_wis_qty(comp_id, won_no, bom.item_id, bom.p_id, bom.c_id)
            bal_bom_qty = max(0.0, round(bom_calc_qty - total_wis_qty, 3))

            cal_issue_qty = 0.0
            cal_stock_qty = item.stock_qty

            if item.stock_qty >= bal_bom_qty:
                cal_issue_qty = bal_bom_qty
                cal_stock_qty = round(item.stock_qty - bal_bom_qty, 3)
            elif bal_bom_qty > item.stock_qty:
                cal_issue_qty = item.stock_qty
                cal_stock_qty = 0.0
            
            if cal_issue_qty > 0:
                # Create WIS Master record only once per transaction
                if not wis_master_created:
                    wis_master_obj = WISMaster.objects.create(
                        wis_no=wis_no,
                        wo_no=work_order,
                        compid=comp_id,
                        fin_year_id=fin_year_id,
                        session_id=session_id,
                        sys_date=current_date,
                        sys_time=current_time
                    )
                    wis_master_created = True

                # Create WIS Detail record
                WISDetail.objects.create(
                    wis_no=wis_no, # Denormalized field
                    wis_master=wis_master_obj,
                    p_id=bom.p_id,
                    c_id=bom.c_id,
                    item_id=bom.item_id,
                    issued_qty=round(cal_issue_qty, 3)
                )

                # Update item stock quantity
                item.stock_qty = cal_stock_qty
                item.save(update_fields=['stock_qty']) # Save only the updated field for efficiency
        
        # Update WorkOrder status after successful WIS process
        work_order.dry_actual_run = 1
        work_order.save(update_fields=['dry_actual_run'])

        return True # Indicate success
```

#### 4.2 Forms (`inventory/forms.py`)

The original ASP.NET page primarily performs a transaction (the "Actual Run") via a button click rather than a traditional data entry form. Therefore, a Django `forms.ModelForm` is not explicitly needed for the primary functionality. The "Actual Run" will be triggered by an HTMX `POST` request.

```python
# No specific forms.py needed for this particular page's primary functionality.
# Form validation for the "Actual Run" logic is handled within the AssemblyRunService.
```

#### 4.3 Views (`inventory/views.py`)

Views are kept thin, delegating complex logic to the `AssemblyRunService` class. We use Django's `TemplateView` for displaying the initial page and `View` for handling HTMX requests and redirects.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import render, redirect
from django.urls import reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from .models import AssemblyRunService, WorkOrder # Import the service and relevant models

# --- Mock global/session variables for demonstration ---
# In a real application, these values would be dynamically obtained
# from the user's session or authentication system (e.g., request.user.profile.comp_id).
GLOBAL_COMP_ID = 1
GLOBAL_FIN_YEAR_ID = 1
GLOBAL_SESSION_ID = 'authenticated_user' # Represents Session["username"]

class AssemblyRunListView(TemplateView):
    """
    Main view for the 'WIS Actual Run Assembly' page.
    It primarily renders the base HTML structure and the Work Order number.
    The main data table content is loaded dynamically via HTMX.
    """
    template_name = 'inventory/assemblyrun/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Retrieve Work Order Number from query parameters
        won_no = self.request.GET.get('WONo', '')
        context['work_order_no'] = won_no
        # Pass any messages from redirects
        context['page_message'] = self.request.GET.get('msg', '')
        return context

class AssemblyRunTablePartialView(View):
    """
    HTMX-specific view to render only the DataTables content.
    This allows dynamic refreshing of the table without a full page reload.
    """
    def get(self, request, *args, **kwargs):
        won_no = request.GET.get('WONo')
        if not won_no:
            # HTMX expects a partial HTML response or specific headers for errors
            return HttpResponse("<p class='text-red-500'>Error: Work Order Number is missing.</p>", status=400)

        # Delegate data retrieval and calculation to the service layer (Fat Model principle)
        assembly_details = AssemblyRunService.get_assembly_run_details(GLOBAL_COMP_ID, won_no)
        
        context = {
            'assembly_details': assembly_details,
            'work_order_no': won_no,
        }
        return render(request, 'inventory/assemblyrun/_assembly_run_table.html', context)

class PerformActualRunView(View):
    """
    Handles the POST request to perform the 'Actual Run of Assembly' transaction.
    This view is triggered by an HTMX POST button.
    """
    def post(self, request, *args, **kwargs):
        won_no = request.POST.get('won_no')
        if not won_no:
            messages.error(request, "Work Order Number is required to perform Actual Run.")
            # HTMX needs to trigger a message display. HTTP 204 No Content is good for success with no UI change.
            # But for error, we might want to return HTML or a trigger to display the message.
            return HttpResponse(status=400, headers={'HX-Trigger': 'showMessage'}) # Custom HTMX trigger to show messages

        try:
            # Delegate transaction logic to the service layer
            AssemblyRunService.perform_actual_run(GLOBAL_COMP_ID, GLOBAL_FIN_YEAR_ID, GLOBAL_SESSION_ID, won_no)
            messages.success(request, 'WIS process is completed successfully.')
            # After a successful transaction, redirect (HX-Location) back to the list view
            # with the original WONo and a success message.
            return HttpResponse(
                status=204, # No Content to return, but successful operation
                headers={
                    'HX-Location': reverse('inventory:assemblyrun_list') + f'?WONo={won_no}&msg=WIS process is completed.'
                }
            )
        except WorkOrder.DoesNotExist:
            messages.error(request, f"Work Order '{won_no}' not found.")
            return HttpResponse(status=404, headers={'HX-Trigger': 'showMessage'})
        except Exception as e:
            messages.error(request, f'An unexpected error occurred during WIS process: {e}')
            return HttpResponse(status=500, headers={'HX-Trigger': 'showMessage'}) # Server error, show message

class RedirectActionView(View):
    """
    Handles redirects for "Cancel" and "Dry Run of Material" buttons,
    and also the "Export" image button.
    """
    def get(self, request, *args, **kwargs):
        action_type = kwargs.get('action_type')
        won_no = request.GET.get('WONo', '') # Pass WONo if needed for target page

        if action_type == 'cancel':
            # Original: Redirect to WIS_Dry_Actual_Run.aspx
            return redirect(reverse('inventory:wis_dry_actual_run')) # Placeholder URL

        elif action_type == 'dry_run_material':
            # Original: Redirect to WIS_ActualRun_Material.aspx
            return redirect(reverse('inventory:wis_actual_run_material') + f'?WONo={won_no}') # Placeholder URL

        elif action_type == 'export_bom_print':
            # Original: Redirect to BOM_Design_Print_Cry.aspx
            # This action doesn't require a full page redirect in a typical HTMX flow
            # if it's just triggering a download or showing a message.
            messages.info(request, "BOM Export/Print functionality is not implemented yet. (Simulating redirect)")
            # For HTMX, we might simply return 204 No Content and let messages be displayed.
            return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'}) 
        
        # Fallback for unknown action_type
        messages.error(request, f"Unknown action: {action_type}")
        return HttpResponse(status=400, headers={'HX-Trigger': 'showMessage'})
```

#### 4.4 Templates (`inventory/templates/inventory/assemblyrun/`)

We'll create a main list template and a partial template for the DataTables content, ensuring proper HTMX integration and DRY principles.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">WIS Dry Run of Assembly for Work Order No.: <span class="text-blue-600">{{ work_order_no }}</span></h2>
        
        <div class="flex space-x-2 items-center">
            <!-- Checkbox for "Expand Tree" - Re-purposed for UI toggle if needed, or simplified -->
            <label class="flex items-center space-x-2 cursor-pointer">
                <input type="checkbox" id="expandTreeCheckbox" class="form-checkbox h-4 w-4 text-blue-600 rounded" 
                       x-data="{ expanded: true }" :checked="expanded" @change="expanded = !expanded" 
                       hx-trigger="change" 
                       hx-get="{% url 'inventory:assemblyrun_table' %}?WONo={{ work_order_no }}" 
                       hx-target="#assemblyRunTable-container" 
                       hx-swap="innerHTML"
                       hx-indicator="#loadingIndicator">
                <span class="text-gray-700 font-semibold">Expand Tree</span>
            </label>

            <!-- Buttons for actions -->
            <button 
                id="actualRunBtn"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-post="{% url 'inventory:perform_actual_run' %}"
                hx-vals='{"won_no": "{{ work_order_no }}"}'
                hx-confirm="Are you sure you want to perform the Actual Run of Assembly for Work Order: {{ work_order_no }}?"
                hx-indicator="#loadingIndicator">
                Actual Run of Assembly
            </button>
            <button 
                class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'inventory:assemblyrun_redirect' action_type='dry_run_material' %}?WONo={{ work_order_no }}"
                hx-target="body" hx-swap="outerHTML">
                Dry Run of Material
            </button>
            <button 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'inventory:assemblyrun_redirect' action_type='cancel' %}"
                hx-target="body" hx-swap="outerHTML">
                Cancel
            </button>
            <button 
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'inventory:assemblyrun_redirect' action_type='export_bom_print' %}?WONo={{ work_order_no }}"
                hx-indicator="#loadingIndicator">
                Export BOM Print
            </button>
        </div>
    </div>

    <!-- Message display area for alerts -->
    <div id="message-container" x-data="{ show: false }" x-init="document.body.addEventListener('showMessage', () => { show = true; setTimeout(() => show = false, 5000); });" x-show="show" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100" x-transition:leave="transition ease-in duration-300" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90" class="fixed top-4 right-4 z-50">
        {% for message in messages %}
        <div class="alert {% if message.tags %}alert-{{ message.tags }}{% endif %} p-3 rounded-md shadow-md mb-2 {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
        {% if page_message %}
        <div class="alert alert-success p-3 rounded-md shadow-md bg-green-100 text-green-800">
            {{ page_message }}
        </div>
        {% endif %}
    </div>

    <!-- Loading Indicator for HTMX operations -->
    <div id="loadingIndicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
        <p class="mt-4 ml-4 text-white text-lg">Processing...</p>
    </div>

    <div id="assemblyRunTable-container"
         hx-trigger="load, refreshAssemblyRunList from:body"
         hx-get="{% url 'inventory:assemblyrun_table' %}?WONo={{ work_order_no }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Assembly Run Details...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is automatically available if included in base.html
    // For DataTables, ensure jQuery and DataTables JS/CSS are loaded in base.html
    document.addEventListener('DOMContentLoaded', function() {
        // HTMX will handle reloading the table container, which includes the DataTable script.
        // We only need to re-initialize DataTable when the content is swapped.
        document.body.addEventListener('htmx:afterSwap', function(evt) {
            if (evt.target.id === 'assemblyRunTable-container') {
                initDataTable();
            }
        });

        // Initialize DataTable if already present on initial load
        if (document.getElementById('assemblyRunTable')) {
            initDataTable();
        }

        // Global function to initialize DataTable
        window.initDataTable = function() {
            if ($.fn.DataTable.isDataTable('#assemblyRunTable')) {
                $('#assemblyRunTable').DataTable().destroy(); // Destroy existing instance if any
            }
            $('#assemblyRunTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 15, // Matches original PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]]
            });
        };
    });
</script>
{% endblock %}
```

**`_assembly_run_table.html` (Partial for HTMX)**

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="assemblyRunTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tot. WIS Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dry Run Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance BOM Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">After Stock Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for detail in assembly_details %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ detail.item_code }}</td>
                <td class="py-3 px-4 text-sm text-gray-500">{{ detail.description }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ detail.unit_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-center">{{ detail.uom }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ detail.bom_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ detail.stock_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ detail.total_wis_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ detail.dry_run_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ detail.balance_bom_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ detail.after_stock_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'inventory:assemblyrun_redirect' action_type='export_bom_print' %}?WONo={{ work_order_no }}&PId={{ detail.p_id }}&CId={{ detail.c_id }}"
                        hx-indicator="#loadingIndicator">
                        <img src="/static/images/export.ico" alt="Export" class="h-4 w-4 inline-block">
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 text-center text-gray-500">No assembly run details found for this Work Order.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Re-initialize DataTables after HTMX swaps the content -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ensure initDataTable is called after content is loaded
        if (window.initDataTable) {
            window.initDataTable();
        }
    });
</script>
```

#### 4.5 URLs (`inventory/urls.py`)

This file defines the URL patterns that map to our Django views, using namespaces for clarity.

```python
from django.urls import path
from django.views.generic import TemplateView # For placeholder views
from .views import (
    AssemblyRunListView,
    AssemblyRunTablePartialView,
    PerformActualRunView,
    RedirectActionView
)

app_name = 'inventory' # Namespace for this app's URLs

urlpatterns = [
    # Main page to display assembly run details
    path('wis_actualrun_assembly/', AssemblyRunListView.as_view(), name='assemblyrun_list'),
    
    # HTMX endpoint to load/refresh the DataTables content
    path('wis_actualrun_assembly/table/', AssemblyRunTablePartialView.as_view(), name='assemblyrun_table'),
    
    # HTMX endpoint to perform the "Actual Run" transaction
    path('wis_actualrun_assembly/perform_run/', PerformActualRunView.as_view(), name='perform_actual_run'),
    
    # Generic redirect view for "Cancel", "Dry Run Material", "Export Print" buttons
    path('wis_actualrun_assembly/action/<str:action_type>/', RedirectActionView.as_view(), name='assemblyrun_redirect'),

    # --- Placeholder URLs for other modules/pages mentioned in the original ASP.NET code ---
    # These would be actual views in a complete Django project.
    path('wis_dry_actual_run/', TemplateView.as_view(template_name='inventory/placeholder.html'), name='wis_dry_actual_run'),
    path('wis_actual_run_material/', TemplateView.as_view(template_name='inventory/placeholder.html'), name='wis_actual_run_material'),
    path('bom_design_print_cry/', TemplateView.as_view(template_name='inventory/placeholder.html'), name='bom_design_print_cry'),
]
```

**`inventory/templates/inventory/placeholder.html`** (for redirects)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 text-center">
    <h2 class="text-3xl font-bold text-gray-800 mb-4">Placeholder Page</h2>
    <p class="text-gray-600">This page serves as a placeholder for a redirected module.</p>
    <p class="mt-4 text-gray-600">Functionality for this module will be implemented in a future migration phase.</p>
    <a href="{% url 'inventory:assemblyrun_list' %}" class="mt-8 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Go back to Assembly Run
    </a>
</div>
{% endblock %}
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for models (unit tests for business logic) and views (integration tests for HTTP responses and HTMX interactions) are crucial.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from datetime import date

from .models import (
    WorkOrder, BOM, ItemMaster, UnitMaster, WISMaster, WISDetail,
    AssemblyRunService,
    calculate_bom_tree_qty, get_item_code_part_no, get_total_wis_qty,
    MOCK_COMP_ID, MOCK_FIN_YEAR_ID, MOCK_SESSION_ID
)

# --- Model Unit Tests ---

class ModelCreationTest(TestCase):
    """
    Tests for basic model creation and string representation.
    """
    def test_work_order_creation(self):
        wo = WorkOrder.objects.create(won_no='WO001', compid=MOCK_COMP_ID, dry_actual_run=0)
        self.assertEqual(str(wo), 'WO001')
        self.assertEqual(wo.dry_actual_run, 0)

    def test_item_master_creation(self):
        item = ItemMaster.objects.create(id=1, compid=MOCK_COMP_ID, uom_basic=1, stock_qty=100.0, manf_desc='Test Item', item_code='ITEM001')
        self.assertEqual(str(item), 'ITEM001')
        self.assertEqual(item.stock_qty, 100.0)

    def test_unit_master_creation(self):
        unit = UnitMaster.objects.create(id=1, symbol='PCS')
        self.assertEqual(str(unit), 'PCS')

    def test_wis_master_creation(self):
        wo = WorkOrder.objects.create(won_no='WO002', compid=MOCK_COMP_ID)
        wis_master = WISMaster.objects.create(
            wis_no='0001', wo_no=wo, compid=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID, session_id='test_user',
            sys_date=date.today(), sys_time='10:00:00'
        )
        self.assertEqual(str(wis_master), '0001')
        self.assertEqual(wis_master.wo_no, wo)

    def test_wis_detail_creation(self):
        wo = WorkOrder.objects.create(won_no='WO003', compid=MOCK_COMP_ID)
        wis_master = WISMaster.objects.create(
            wis_no='0002', wo_no=wo, compid=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID, session_id='test_user',
            sys_date=date.today(), sys_time='10:00:00'
        )
        wis_detail = WISDetail.objects.create(
            wis_no='0002', wis_master=wis_master, p_id=0, c_id=1, item_id=1, issued_qty=10.5
        )
        self.assertEqual(str(wis_detail), 'WIS 0002 - Item 1 Qty: 10.5')
        self.assertEqual(wis_detail.issued_qty, 10.5)

class WISMasterMethodTest(TestCase):
    """
    Tests specific methods on WISMaster model.
    """
    def test_generate_next_wis_no(self):
        # Initial call should be 0001
        self.assertEqual(WISMaster.generate_next_wis_no(MOCK_COMP_ID, MOCK_FIN_YEAR_ID), '0001')

        # Create one WISMaster, next should be 0002
        wo = WorkOrder.objects.create(won_no='WO_GEN_1', compid=MOCK_COMP_ID)
        WISMaster.objects.create(
            wis_no='0001', wo_no=wo, compid=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID, session_id='test',
            sys_date=date.today(), sys_time='10:00:00'
        )
        self.assertEqual(WISMaster.generate_next_wis_no(MOCK_COMP_ID, MOCK_FIN_YEAR_ID), '0002')

        # Test for different company/fin year
        self.assertEqual(WISMaster.generate_next_wis_no(MOCK_COMP_ID + 1, MOCK_FIN_YEAR_ID), '0001')
        self.assertEqual(WISMaster.generate_next_wis_no(MOCK_COMP_ID, MOCK_FIN_YEAR_ID + 1), '0001')

class HelperFunctionTest(TestCase):
    """
    Tests the helper functions (mocking original C# logic).
    """
    @classmethod
    def setUpTestData(cls):
        cls.item1 = ItemMaster.objects.create(id=1, compid=MOCK_COMP_ID, uom_basic=1, stock_qty=100.0, manf_desc='Item One', item_code='ITM001')
        cls.item2 = ItemMaster.objects.create(id=2, compid=MOCK_COMP_ID, uom_basic=1, stock_qty=50.0, manf_desc='Item Two', item_code='ITM002')
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')

    def test_calculate_bom_tree_qty(self):
        # These values depend on the placeholder logic in models.py
        self.assertEqual(calculate_bom_tree_qty('WO_TEST', 0, 1), 1.0)
        self.assertEqual(calculate_bom_tree_qty('WO_TEST', 1, 2), 2.5)

    def test_get_item_code_part_no(self):
        self.assertEqual(get_item_code_part_no(MOCK_COMP_ID, self.item1.id), 'ITM001')
        self.assertEqual(get_item_code_part_no(MOCK_COMP_ID, 999), 'ITEM-999') # Non-existent item

    def test_get_total_wis_qty(self):
        wo = WorkOrder.objects.create(won_no='WO_TWIS_TEST', compid=MOCK_COMP_ID)
        wis_master = WISMaster.objects.create(
            wis_no='0001', wo_no=wo, compid=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID, session_id='test',
            sys_date=date.today(), sys_time='10:00:00'
        )
        WISDetail.objects.create(wis_no='0001', wis_master=wis_master, p_id=0, c_id=1, item_id=self.item1.id, issued_qty=5.0)
        WISDetail.objects.create(wis_no='0001', wis_master=wis_master, p_id=0, c_id=1, item_id=self.item1.id, issued_qty=3.0)

        self.assertEqual(get_total_wis_qty(MOCK_COMP_ID, 'WO_TWIS_TEST', self.item1.id, 0, 1), 8.0)
        self.assertEqual(get_total_wis_qty(MOCK_COMP_ID, 'WO_TWIS_TEST', self.item2.id, 0, 1), 0.0) # No issues for item2

class AssemblyRunServiceTest(TestCase):
    """
    Integration tests for the AssemblyRunService business logic.
    """
    @classmethod
    def setUpTestData(cls):
        cls.won_no = 'WO_AR_TEST'
        cls.work_order = WorkOrder.objects.create(won_no=cls.won_no, compid=MOCK_COMP_ID, dry_actual_run=0)
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')

        cls.item_assy = ItemMaster.objects.create(id=100, compid=MOCK_COMP_ID, uom_basic=cls.unit.id, stock_qty=1000.0, manf_desc='Assembly A', item_code='ASSY001')
        cls.item_comp_a = ItemMaster.objects.create(id=101, compid=MOCK_COMP_ID, uom_basic=cls.unit.id, stock_qty=50.0, manf_desc='Component A', item_code='COMP001')
        cls.item_comp_b = ItemMaster.objects.create(id=102, compid=MOCK_COMP_ID, uom_basic=cls.unit.id, stock_qty=10.0, manf_desc='Component B', item_code='COMP002')

        # BOM Structure: Assy A (P:0, C:100) requires 1 unit of Comp A (P:100, C:101)
        BOM.objects.create(item_id=cls.item_assy.id, wo_no=cls.work_order, p_id=0, c_id=100, unit_qty=1.0)
        BOM.objects.create(item_id=cls.item_comp_a.id, wo_no=cls.work_order, p_id=100, c_id=101, unit_qty=2.0) # Assy A needs 2 of Comp A
        BOM.objects.create(item_id=cls.item_comp_b.id, wo_no=cls.work_order, p_id=100, c_id=102, unit_qty=1.0) # Assy A needs 1 of Comp B

        # Pre-existing WIS issues for Item Comp A
        wis_master_old = WISMaster.objects.create(
            wis_no='0000', wo_no=cls.work_order, compid=MOCK_COMP_ID,
            fin_year_id=MOCK_FIN_YEAR_ID, session_id='old_user',
            sys_date=date(2023, 1, 1), sys_time='09:00:00'
        )
        WISDetail.objects.create(wis_no='0000', wis_master=wis_master_old, p_id=100, c_id=101, item_id=cls.item_comp_a.id, issued_qty=10.0)

    @patch('inventory.models.calculate_bom_tree_qty', side_effect=lambda wo,p,c: 1.0 if p==0 else 2.0) # Mock BOM Tree Qty for consistent testing
    @patch('inventory.models.get_item_code_part_no', side_effect=lambda c,i: f'ITEM-{i}')
    def test_get_assembly_run_details(self, mock_get_item_code, mock_calc_bom_tree_qty):
        details = AssemblyRunService.get_assembly_run_details(MOCK_COMP_ID, self.won_no)
        self.assertIsInstance(details, list)
        self.assertGreater(len(details), 0)

        # Find Component A's details
        comp_a_detail = next((d for d in details if d['item_id'] == self.item_comp_a.id), None)
        self.assertIsNotNone(comp_a_detail)

        # Expected calculations for Comp A:
        # BOM Unit Qty for Comp A (from BOM table): 2.0
        # Mocked calculate_bom_tree_qty (for P=100, C=101) = 2.0
        # Expected BOM Qty = 2.0 (unit_qty) * 2.0 (tree_qty) = 4.0
        self.assertEqual(comp_a_detail['bom_qty'], 4.0)

        # Pre-existing WIS qty: 10.0
        self.assertEqual(comp_a_detail['total_wis_qty'], 10.0)

        # Balance BOM Qty = Max(0, BOM_Qty - Tot_WIS_Qty) = Max(0, 4.0 - 10.0) = 0.0
        self.assertEqual(comp_a_detail['balance_bom_qty'], 0.0)

        # Current Stock Qty: 50.0
        self.assertEqual(comp_a_detail['stock_qty'], 50.0)

        # Dry Run Qty (CalIssueQty): Since Bal BOM Qty is 0, Dry Run Qty should be 0.
        # This means no new issues are needed for Comp A based on these numbers.
        self.assertEqual(comp_a_detail['dry_run_qty'], 0.0)

        # After Stock Qty: Should remain 50.0 as no issue happened.
        self.assertEqual(comp_a_detail['after_stock_qty'], 50.0)

        # Test for an item where issue is needed (e.g., Comp B)
        comp_b_detail = next((d for d in details if d['item_id'] == self.item_comp_b.id), None)
        self.assertIsNotNone(comp_b_detail)
        # BOM Unit Qty for Comp B: 1.0
        # Mocked calculate_bom_tree_qty: 2.0
        # Expected BOM Qty = 1.0 * 2.0 = 2.0
        self.assertEqual(comp_b_detail['bom_qty'], 2.0)
        self.assertEqual(comp_b_detail['total_wis_qty'], 0.0) # No prior issues for Comp B
        self.assertEqual(comp_b_detail['balance_bom_qty'], 0.0) # Original: (h - (TotWISQty + CalIssueQty))
        # After stock qty for BOM Qty of 2.0 and Stock 10.0.
        # Current stock = 10.0, Bal BOM Qty = 2.0
        # Dry Run Qty = 2.0 (issue what's needed)
        # After Stock Qty = 10.0 - 2.0 = 8.0
        self.assertEqual(comp_b_detail['stock_qty'], 10.0)
        self.assertEqual(comp_b_detail['dry_run_qty'], 2.0)
        self.assertEqual(comp_b_detail['after_stock_qty'], 8.0)
        self.assertEqual(comp_b_detail['balance_bom_qty'], 0.0)


    @patch('inventory.models.calculate_bom_tree_qty', side_effect=lambda wo,p,c: 1.0 if p==0 else 2.0)
    @patch('inventory.models.get_item_code_part_no', side_effect=lambda c,i: f'ITEM-{i}')
    def test_perform_actual_run_success(self, mock_get_item_code, mock_calc_bom_tree_qty):
        initial_stock_comp_a = self.item_comp_a.stock_qty # 50.0
        initial_stock_comp_b = self.item_comp_b.stock_qty # 10.0

        # Run the transaction
        success = AssemblyRunService.perform_actual_run(MOCK_COMP_ID, MOCK_FIN_YEAR_ID, MOCK_SESSION_ID, self.won_no)
        self.assertTrue(success)

        # Verify WorkOrder status updated
        self.work_order.refresh_from_db()
        self.assertEqual(self.work_order.dry_actual_run, 1)

        # Verify WISMaster and WISDetail records created
        wis_masters = WISMaster.objects.filter(wo_no=self.work_order, session_id=MOCK_SESSION_ID)
        self.assertEqual(wis_masters.count(), 1)
        wis_master = wis_masters.first()
        self.assertEqual(wis_master.wis_no, '0001') # First WIS after 0000

        wis_details = WISDetail.objects.filter(wis_master=wis_master)
        # Should create a detail for Comp B (since its dry_run_qty was 2.0)
        # No detail for Comp A because its dry_run_qty was 0.0
        self.assertEqual(wis_details.count(), 1)

        comp_b_detail = wis_details.get(item_id=self.item_comp_b.id)
        self.assertEqual(comp_b_detail.issued_qty, 2.0) # Issued 2.0 of Comp B

        # Verify ItemMaster stock updated
        self.item_comp_a.refresh_from_db()
        self.item_comp_b.refresh_from_db()

        self.assertEqual(self.item_comp_a.stock_qty, initial_stock_comp_a) # Should not change
        self.assertEqual(self.item_comp_b.stock_qty, initial_stock_comp_b - 2.0) # Should decrease by 2.0

    @patch('inventory.models.calculate_bom_tree_qty', side_effect=Exception('Mock DB Error'))
    def test_perform_actual_run_failure_rollback(self, mock_calc_bom_tree_qty):
        initial_stock_comp_a = self.item_comp_a.stock_qty
        initial_stock_comp_b = self.item_comp_b.stock_qty
        initial_wis_master_count = WISMaster.objects.count()
        initial_wis_detail_count = WISDetail.objects.count()
        initial_wo_dry_actual_run = self.work_order.dry_actual_run

        with self.assertRaises(Exception): # Expect the mocked exception to be raised
            AssemblyRunService.perform_actual_run(MOCK_COMP_ID, MOCK_FIN_YEAR_ID, MOCK_SESSION_ID, self.won_no)

        # Verify no changes due to transaction rollback
        self.item_comp_a.refresh_from_db()
        self.item_comp_b.refresh_from_db()
        self.work_order.refresh_from_db()

        self.assertEqual(self.item_comp_a.stock_qty, initial_stock_comp_a)
        self.assertEqual(self.item_comp_b.stock_qty, initial_stock_comp_b)
        self.assertEqual(WISMaster.objects.count(), initial_wis_master_count)
        self.assertEqual(WISDetail.objects.count(), initial_wis_detail_count)
        self.assertEqual(self.work_order.dry_actual_run, initial_wo_dry_actual_run)


# --- View Integration Tests ---

class AssemblyRunViewsTest(TestCase):
    """
    Tests for Django views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        cls.won_no = 'WO_VIEW_TEST'
        cls.work_order = WorkOrder.objects.create(won_no=cls.won_no, compid=MOCK_COMP_ID, dry_actual_run=0)
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item = ItemMaster.objects.create(id=1, compid=MOCK_COMP_ID, uom_basic=cls.unit.id, stock_qty=100.0, manf_desc='Test Component', item_code='TESTCOMP')
        BOM.objects.create(item_id=cls.item.id, wo_no=cls.work_order, p_id=0, c_id=1, unit_qty=1.0)


    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory:assemblyrun_list') + f'?WONo={self.won_no}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/assemblyrun/list.html')
        self.assertContains(response, f'WIS Dry Run of Assembly for Work Order No.: <span class="text-blue-600">{self.won_no}</span>')
        self.assertContains(response, '<div id="assemblyRunTable-container"') # Check for HTMX target div

    def test_list_view_get_with_message(self):
        test_msg = "Test message from redirect."
        response = self.client.get(reverse('inventory:assemblyrun_list') + f'?WONo={self.won_no}&msg={test_msg}')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, test_msg)

    @patch('inventory.views.AssemblyRunService.get_assembly_run_details')
    def test_table_partial_view_get_htmx(self, mock_get_details):
        mock_get_details.return_value = [{'item_code': 'TESTCOMP', 'description': 'Test Component', 'dry_run_qty': 10.0}]
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:assemblyrun_table') + f'?WONo={self.won_no}', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/assemblyrun/_assembly_run_table.html')
        self.assertContains(response, 'TESTCOMP')
        self.assertContains(response, 'Dry Run Qty') # Check a header

    def test_table_partial_view_get_missing_wono(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:assemblyrun_table'), **headers)
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Error: Work Order Number is missing.')

    @patch('inventory.views.AssemblyRunService.perform_actual_run')
    def test_perform_actual_run_view_post_success(self, mock_perform_run):
        mock_perform_run.return_value = True # Simulate successful transaction
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('inventory:perform_actual_run'), {'won_no': self.won_no}, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success status
        self.assertEqual(response.headers['HX-Location'], reverse('inventory:assemblyrun_list') + f'?WONo={self.won_no}&msg=WIS process is completed.')
        self.assertTrue(mock_perform_run.called)
        
        # Verify success message is added (though not returned in 204, it's set for next request)
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'WIS process is completed successfully.')

    @patch('inventory.views.AssemblyRunService.perform_actual_run', side_effect=Exception('DB Error'))
    def test_perform_actual_run_view_post_failure(self, mock_perform_run):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('inventory:perform_actual_run'), {'won_no': self.won_no}, **headers)
        self.assertEqual(response.status_code, 500) # Server error due to mocked exception
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'showMessage')
        
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertIn('Error during WIS process', str(messages[0]))

    def test_redirect_action_view_cancel(self):
        response = self.client.get(reverse('inventory:assemblyrun_redirect', args=['cancel']))
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertRedirects(response, reverse('inventory:wis_dry_actual_run'))

    def test_redirect_action_view_dry_run_material(self):
        response = self.client.get(reverse('inventory:assemblyrun_redirect', args=['dry_run_material']) + f'?WONo={self.won_no}')
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('inventory:wis_actual_run_material') + f'?WONo={self.won_no}')

    def test_redirect_action_view_export_bom_print_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:assemblyrun_redirect', args=['export_bom_print']) + f'?WONo={self.won_no}', **headers)
        self.assertEqual(response.status_code, 204) # HTMX success status with no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'showMessage')
        
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertIn('BOM Export/Print functionality is not implemented yet', str(messages[0]))

    def test_redirect_action_view_unknown_action(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:assemblyrun_redirect', args=['unknown_action']), **headers)
        self.assertEqual(response.status_code, 400)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'showMessage')
        
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertIn('Unknown action: unknown_action', str(messages[0]))
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Dynamic Updates:**
    -   The main list page (`list.html`) uses `hx-trigger="load, refreshAssemblyRunList from:body"` and `hx-get="{% url 'inventory:assemblyrun_table' %}?WONo={{ work_order_no }}"` to initially load the DataTable content into `#assemblyRunTable-container`.
    -   After a successful "Actual Run" `POST` operation, the `PerformActualRunView` returns an `HX-Location` header, instructing HTMX to redirect the browser to the updated list view, effectively reloading the page with messages and the updated data.
    -   The "Expand Tree" checkbox also uses `hx-trigger="change"` to reload the table content (though its effect on a flat DataTable would be a simplified re-render).
    -   All buttons (`Actual Run`, `Dry Run Material`, `Cancel`, `Export BOM Print`) are equipped with `hx-post` or `hx-get` to interact with Django views without full page reloads, making the user experience snappier.
    -   A global HTMX loading indicator (`#loadingIndicator`) is configured to show "Processing..." during any HTMX request.
    -   Custom `HX-Trigger` (`showMessage`) is used to inform the frontend (Alpine.js) when to display Django messages.

-   **Alpine.js for UI State Management:**
    -   The message display area in `list.html` uses `x-data` and `x-show` with a custom `showMessage` event listener. This allows Django `messages` to be displayed dynamically in a non-blocking toast-like manner.
    -   The "Expand Tree" checkbox has `x-data` and `@change` to manage its internal checked state, though its primary interaction is still HTMX.

-   **DataTables for List Views:**
    -   The `_assembly_run_table.html` partial contains the `<table>` element which is initialized as a DataTables instance upon insertion into the DOM.
    -   A `window.initDataTable` function is created in `list.html`'s `extra_js` block to be called after HTMX `htmx:afterSwap` event, ensuring DataTables is correctly re-initialized every time the table content is updated.
    -   DataTables provides built-in client-side searching, sorting, and pagination, replacing the manual paging and sorting logic found in the original `RadTreeList`.

-   **HTMX-only Interactions:**
    -   All form submissions and button clicks utilize HTMX attributes (`hx-post`, `hx-get`, `hx-target`, `hx-swap`, `hx-trigger`, `hx-confirm`, `hx-indicator`) to handle interactions.
    -   No custom JavaScript beyond the DataTables initialization (which is managed by HTMX events) and Alpine.js is required for the core dynamic behavior.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the specified ASP.NET functionality to Django. By leveraging AI-assisted automation, the focus remains on systematic conversion and maximizing efficiency. The resulting Django application will be modern, maintainable, and aligned with best practices, offering a significantly improved user experience and a robust foundation for future development. Remember to replace placeholder `MOCK_COMP_ID`, `MOCK_FIN_YEAR_ID`, `MOCK_SESSION_ID` with your actual application's way of managing these context variables, typically through authenticated user profiles or system settings.