This document outlines a strategic plan to modernize your legacy ASP.NET application, specifically the "Goods Service Note [GSN] - Print" module, by migrating it to a robust and scalable Django 5.0+ solution. Our approach prioritizes automation, leverages modern web technologies like HTMX and Alpine.js, and adheres to Django's best practices for maintainability and performance.

Our goal is to deliver a solution that is easier to maintain, more efficient, and provides a superior user experience through dynamic, single-page application (SPA)-like interactions without the complexity of traditional JavaScript frameworks.

## ASP.NET to Django Conversion Script: Goods Service Note (GSN) Print Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists by extending `core/base.html`.
- Focus ONLY on component-specific code for the current module (`inventory` app).
- Always include complete unit tests for models and integration tests for views, aiming for at least 80% coverage.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy (DRY principle).
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code to define Django models.

**Analysis from ASP.NET Code:**
The ASP.NET code interacts with several tables. The primary table for this module is `tblinv_MaterialServiceNote_Master`. Data is then enriched by joining or querying related tables: `tblFinancial_master`, `tblInv_Inward_Master`, `tblInv_Inward_Details`, `tblMM_PO_Master`, and `tblMM_Supplier_master`.

**Inferred Tables and Key Columns:**

*   **`tblinv_MaterialServiceNote_Master` (Main GSN Data):**
    *   `Id` (Primary Key, Integer)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`, Integer)
    *   `GSNNo` (String)
    *   `GINNo` (String)
    *   `GINId` (Foreign Key to `tblInv_Inward_Master`, Integer)
    *   `SysDate` (Date)
    *   `CompId` (Foreign Key to `tblCompany_master` - assumed, Integer)

*   **`tblFinancial_master` (Financial Year Data):**
    *   `FinYearId` (Primary Key, Integer)
    *   `FinYear` (String)

*   **`tblInv_Inward_Master` (Goods Inward Note - GIN Data):**
    *   `Id` (Primary Key, Integer)
    *   `PONo` (String - actual PO number, not a direct FK in original schema but used for lookup)
    *   `ChallanNo` (String)
    *   `ChallanDate` (Date)

*   **`tblInv_Inward_Details` (GIN Details - used for `POId` linkage):**
    *   `Id` (Primary Key, Integer - this is the `POId` from the C# code)
    *   `GINId` (Foreign Key to `tblInv_Inward_Master`, Integer)
    *   `MId` (Foreign Key to `tblMM_PO_Master` - derived from C# `MId` usage, Integer)

*   **`tblMM_PO_Master` (Purchase Order Data):**
    *   `Id` (Primary Key, Integer)
    *   `PONo` (String - actual PO number)
    *   `SupplierId` (Foreign Key to `tblMM_Supplier_master`, String)

*   **`tblMM_Supplier_master` (Supplier Data):**
    *   `SupplierId` (Primary Key, String)
    *   `SupplierName` (String)

*   **`tblCompany_master` (Company Data - inferred from `CompId`):**
    *   `CompId` (Primary Key, Integer)
    *   `CompName` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data flow within the ASP.NET code.

**Functionality Analysis:**

*   **Read (Primary):** The main purpose is to display a list of "Goods Service Note" entries.
    *   Initial page load retrieves GSN records, then performs multiple nested lookups to enrich each record with Financial Year, GIN details (PONo, Challan No/Date), and Supplier information.
    *   Data is filtered by `CompId` and `FinYearId` (from session).
    *   Supports pagination (`AllowPaging`).
*   **Search/Filter:** Users can filter the GSN list by `Supplier` name using an autocomplete-enabled textbox and a "Search" button. The search converts the supplier name (e.g., "Supplier Name [SUP001]") to `SupplierId` before filtering.
*   **Select/Redirect:** Each row in the GridView has a "Select" `LinkButton`. Clicking this button extracts multiple data points from the row (`Id`, `SupId`, `GSNNo`, `GINNo`, `GINId`, `PONo`, `FyId`) and redirects the user to a detail page (`GoodsServiceNote_SN_Print_Details.aspx`) with these parameters in the URL.
*   **Autocomplete (Supplier):** A web method (`sql`) provides supplier suggestions based on typed prefix, fetching `SupplierId` and `SupplierName` from `tblMM_Supplier_master`.

**Validation Logic:** No explicit server-side validation is visible beyond implicit database constraints. Dates are formatted.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to modern Django templates with HTMX/Alpine.js.

**UI Component Mapping:**

*   **Page Structure:** The `.aspx` uses a `MasterPageFile` and `asp:Content` controls. In Django, this maps to `{% extends 'core/base.html' %}` and `{% block content %}`.
*   **Header:** `<b>&nbsp;Goods Service Note [GSN] - Print</b>` will be an `<h1>` or `<h2>` tag.
*   **Supplier Search:**
    *   `asp:TextBox ID="txtSupplier"` + `cc1:AutoCompleteExtender`: This will be a standard HTML `<input type="text">` field. Autocomplete functionality will be provided via HTMX to a Django endpoint, with Alpine.js handling the dynamic display of suggestions.
    *   `asp:Button ID="btnSearch"`: This will be an HTMX-powered `<button>` or part of the `hx-get` on the input, triggering a partial table refresh.
*   **Data Grid:**
    *   `asp:GridView ID="GridView2"`: This will be a standard HTML `<table>` element.
    *   `CssClass="yui-datatable-theme"`: This suggests client-side data manipulation, which will be implemented using DataTables.js on the frontend.
    *   `AutoGenerateColumns="False"`: Columns are explicitly defined in the ASP.NET, similar to how they will be in a Django template.
    *   `LinkButton` for "Select": This will be an `<a>` tag or a button with `hx-redirect` to the detail page.
    *   `EmptyDataTemplate`: Replaced by Django's `{% if not objects %}` template logic.
*   **Styling:** `StyleSheet.css`, `yui-datatable.css`. These will be replaced by Tailwind CSS (as per guidelines) and DataTables.js's default styling, or custom CSS integrated with Tailwind.

### Step 4: Generate Django Code

We will structure the Django application within an `inventory` app.

#### 4.1 Models (`inventory/models.py`)

This section defines the Django ORM models, mapping to your existing database tables. We'll include a custom manager for efficient data retrieval to mimic the original `loadData` logic using Django's ORM capabilities.

```python
from django.db import models
from django.db.models import F, OuterRef, Subquery
import datetime # For date operations

# --- Assumed Core Models (can be in a separate 'core' app) ---
# Assuming these exist or will be migrated
class Company(models.Model):
    """Maps to tblCompany_master."""
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

# --- Inventory Module Specific Models ---

class FinancialYear(models.Model):
    """Maps to tblFinancial_master."""
    id = models.IntegerField(db_column='FinYearId', primary_key=True) # Using id to match FinYearId
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Supplier(models.Model):
    """Maps to tblMM_Supplier_master."""
    id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50) # Assuming SupplierId is VARCHAR
    name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.name} [{self.id}]"

class PurchaseOrder(models.Model):
    """Maps to tblMM_PO_Master.
    Note: PONo field is duplicated in Inward model, assuming a string matching
    for simplicity rather than full FK based on original SQL's loose joins.
    However, for ORM efficiency, we make PONo a PK here if it's unique
    or add a specific ID for it, then link with supplier.
    Based on C# `tblMM_PO_Master.Id=tblMM_PO_Details.MId`, `Id` is the PK.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50) # This is a string field
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='purchase_orders')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

class Inward(models.Model):
    """Maps to tblInv_Inward_Master."""
    id = models.IntegerField(db_column='Id', primary_key=True) # GINId maps to this Id
    po_no_text = models.CharField(db_column='PONo', max_length=50, blank=True, null=True) # PONo as a string from Inward Master
    challan_no = models.CharField(db_column='ChallanNo', max_length=50, blank=True, null=True)
    challan_date = models.DateField(db_column='ChallanDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Note'
        verbose_name_plural = 'Inward Notes'

    def __str__(self):
        return f"GIN {self.id}"

class InwardDetail(models.Model):
    """Maps to tblInv_Inward_Details.
    This table was used to link Inward to PurchaseOrder based on C# code (`POId`).
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # This 'Id' corresponds to POId in C#
    gin_master = models.ForeignKey(Inward, on_delete=models.DO_NOTHING, db_column='GINId', related_name='details')
    po_master = models.ForeignKey(PurchaseOrder, on_delete=models.DO_NOTHING, db_column='MId', related_name='inward_details') # MId corresponds to tblMM_PO_Master.Id

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Inward Detail'
        verbose_name_plural = 'Inward Details'

    def __str__(self):
        return f"Inward Detail {self.id} for GIN {self.gin_master_id}"

class GoodsServiceNoteManager(models.Manager):
    def get_queryset(self):
        # Optimize query with select_related for direct FKs and prefetch_related for reverse/indirect lookups.
        # This mimics the data gathering in the original `loadData` method.
        return super().get_queryset().select_related(
            'financial_year', # Direct FK
            'gin',            # Direct FK to Inward
        ).prefetch_related(
            # For supplier and PO details, we need to traverse through InwardDetail
            # This is complex due to the original SQL's denormalized structure
            # We assume InwardDetail links an Inward record to a PurchaseOrder
            # To get distinct POs/Suppliers per GSN, we might need a custom annotation or method.
            # Simpler approach: annotate directly for common fields for display.
            # This is a simplification of the complex multi-step C# lookups.
            # We'll use subqueries to pull these related fields efficiently.
            Subquery(InwardDetail.objects.filter(gin_master=OuterRef('gin')).values('po_master__po_no')[:1], output_field=models.CharField()),
            Subquery(InwardDetail.objects.filter(gin_master=OuterRef('gin')).values('po_master__supplier__name')[:1], output_field=models.CharField()),
            Subquery(InwardDetail.objects.filter(gin_master=OuterRef('gin')).values('po_master__supplier__id')[:1], output_field=models.CharField())
        ).annotate(
            po_no_display=Subquery(InwardDetail.objects.filter(gin_master=OuterRef('gin')).values('po_master__po_no')[:1], output_field=models.CharField()),
            supplier_name_display=Subquery(InwardDetail.objects.filter(gin_master=OuterRef('gin')).values('po_master__supplier__name')[:1], output_field=models.CharField()),
            supplier_id_display=Subquery(InwardDetail.objects.filter(gin_master=OuterRef('gin')).values('po_master__supplier__id')[:1], output_field=models.CharField()),
        )


    def filter_by_params(self, company_id, financial_year_id, supplier_id=None):
        """
        Mimics the `loadData` method's filtering logic.
        Assumes `CompId` field is on `GoodsServiceNote` for `company_id`.
        """
        queryset = self.get_queryset().filter(
            #company_id=company_id, # Uncomment if CompId is a FK on GoodsServiceNote
            financial_year_id__lte=financial_year_id # FinYearId <= current FinYearId
        ).order_by('-id')

        if supplier_id:
            # Filter by supplier ID which is linked via InwardDetail and PurchaseOrder
            queryset = queryset.filter(
                inwarddetail__po_master__supplier_id=supplier_id
            ).distinct() # Use distinct to avoid duplicate GSNs if one GSN links to multiple POs with same supplier

        return queryset

class GoodsServiceNote(models.Model):
    """Maps to tblinv_MaterialServiceNote_Master."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='goods_service_notes')
    gsn_no = models.CharField(db_column='GSNNo', max_length=50)
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    gin = models.ForeignKey(Inward, on_delete=models.DO_NOTHING, db_column='GINId', related_name='goods_service_notes')
    system_date = models.DateField(db_column='SysDate')
    # company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='goods_service_notes') # If Company is a FK here

    objects = GoodsServiceNoteManager() # Use our custom manager

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'
        verbose_name = 'Goods Service Note'
        verbose_name_plural = 'Goods Service Notes'

    def __str__(self):
        return self.gsn_no

    # Properties to expose related data for display, leveraging annotations from manager.
    # These properties provide the values as they were prepared in the original ASP.NET DataTable.
    @property
    def fin_year_display(self):
        return self.financial_year.fin_year if hasattr(self, 'financial_year') else ''

    @property
    def challan_no_display(self):
        return self.gin.challan_no if hasattr(self, 'gin') else ''

    @property
    def challan_date_display(self):
        # Mimic fun.FromDateDMY (DD/MM/YYYY)
        return self.gin.challan_date.strftime('%d/%m/%Y') if self.gin and self.gin.challan_date else ''

    @property
    def po_no_display_from_annotated(self):
        # This comes from the annotation in the manager
        return self.po_no_display if hasattr(self, 'po_no_display') else ''

    @property
    def supplier_display(self):
        # This comes from the annotation in the manager
        return self.supplier_name_display if hasattr(self, 'supplier_name_display') else ''

    @property
    def sup_id_display(self):
        # This comes from the annotation in the manager
        return self.supplier_id_display if hasattr(self, 'supplier_id_display') else ''

    @property
    def system_date_display(self):
        return self.system_date.strftime('%d/%m/%Y') if self.system_date else ''


```

#### 4.2 Forms (`inventory/forms.py`)

For the search functionality, we don't need a ModelForm. A simple `Form` will suffice, handling the supplier input.

```python
from django import forms
from .models import Supplier

class GSNPrintSearchForm(forms.Form):
    """
    Form for searching Goods Service Notes by Supplier.
    The supplier input will be handled via HTMX + Alpine.js autocomplete.
    """
    supplier_input = forms.CharField(
        max_length=255,
        required=False,
        label='Supplier',
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': "{% url 'inventory:supplier_autocomplete' %}", # HTMX for autocomplete
            'hx-trigger': "keyup changed delay:500ms, search",
            'hx-target': "#supplier-suggestions",
            'hx-swap': "innerHTML",
            'autocomplete': 'off',
            'x-model': 'supplierSearch', # Alpine.js model for input
            '@input': 'clearSupplierId()', # Alpine.js action to clear hidden ID
            '@keydown.tab.prevent': 'selectFirstSuggestion($event)', # Alpine.js tab behavior
        })
    )
    # Hidden field to store the actual supplier ID once selected from autocomplete
    supplier_id = forms.CharField(
        widget=forms.HiddenInput(attrs={'x-model': 'selectedSupplierId'}),
        required=False
    )

    def clean_supplier_input(self):
        # The original fun.getCode parsed "Name [ID]". We assume the client-side
        # autocomplete will set supplier_id directly.
        # This method is primarily for showing how validation would work if needed.
        # For HTMX, much of this might be skipped on form submission unless full form validation is needed.
        return self.cleaned_data['supplier_input']
```

#### 4.3 Views (`inventory/views.py`)

We'll define a main ListView for the page and a partial view for the DataTables content that HTMX can swap. We'll also add a view for supplier autocomplete.

```python
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from .models import GoodsServiceNote, Supplier
from .forms import GSNPrintSearchForm
import re # For parsing supplier string

# Placeholder for company_id and financial_year_id, as they came from session
# In a real application, these would come from request.user.profile or a multi-tenancy context.
DEFAULT_COMPANY_ID = 1
DEFAULT_FINANCIAL_YEAR_ID = 2023 # Example, adjust as per your data

class GoodsServiceNotePrintListView(TemplateView):
    """
    Main view for the Goods Service Note Print page.
    This view renders the initial page structure, including the search form
    and an empty container for the GSN list table, which will be loaded via HTMX.
    """
    template_name = 'inventory/goodsservicenote/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = GSNPrintSearchForm(self.request.GET)
        # Pass initial GSN list if not using HTMX for initial load, but for
        # full HTMX integration, the table content will be loaded by a separate HTMX GET.
        # We ensure relevant data like company_id and financial_year_id are available
        # to the templates/HTMX calls.
        context['company_id'] = self.request.session.get('compid', DEFAULT_COMPANY_ID) # Mimic original session usage
        context['financial_year_id'] = self.request.session.get('finyear', DEFAULT_FINANCIAL_YEAR_ID) # Mimic original session usage
        return context

class GoodsServiceNoteTablePartialView(ListView):
    """
    Returns the partial HTML for the Goods Service Note table.
    This view is designed to be called via HTMX to update the table content
    after search or pagination.
    """
    model = GoodsServiceNote
    template_name = 'inventory/goodsservicenote/_goodsservicenotetable.html'
    context_object_name = 'goodsservicenotes'
    # DataTables will handle client-side pagination, so Django's built-in pagination
    # is not strictly necessary for this specific setup IF DataTables is used.
    # However, if server-side pagination is preferred (for large datasets),
    # Django's Paginator should be used here. For this case, DataTables is client-side.

    def get_queryset(self):
        company_id = self.request.session.get('compid', DEFAULT_COMPANY_ID)
        financial_year_id = self.request.session.get('finyear', DEFAULT_FINANCIAL_YEAR_ID)
        supplier_id = self.request.GET.get('supplier_id') # Get supplier_id from HTMX parameter

        # The manager handles the complex data retrieval and filtering.
        queryset = GoodsServiceNote.objects.filter_by_params(
            company_id=company_id,
            financial_year_id=financial_year_id,
            supplier_id=supplier_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Any additional data needed for the table partial.
        # For DataTables, the entire queryset is typically passed, and DT handles display.
        return context


class SupplierAutocompleteView(View):
    """
    Provides supplier suggestions for the autocomplete field via HTMX.
    Returns JSON or an HTML fragment with suggestions.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        company_id = request.session.get('compid', DEFAULT_COMPANY_ID) # Using compid from session

        if not query:
            return HttpResponse("") # No results if query is empty

        # Filter suppliers by company_id and name starting with query
        # Mimics ASP.NET's `StartsWith` logic
        suppliers = Supplier.objects.filter(
            # company_id=company_id, # Uncomment if Supplier model has company_id
            name__icontains=query # Using icontains for more robust search
        ).values('id', 'name')[:10] # Limit to 10 suggestions, similar to original

        # Return as HTML fragment for HTMX to swap
        # The original returned "Name [ID]", we'll return a simple list for Alpine to manage
        context = {'suppliers': suppliers}
        return render(request, 'inventory/goodsservicenote/_supplier_autocomplete_suggestions.html', context)


class GoodsServiceNoteDetailView(View):
    """
    Handles the 'Select' action, mimicking the ASP.NET redirect.
    It constructs the URL for the detail page based on the selected GSN.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            gsn_note = GoodsServiceNote.objects.get(pk=pk)
            # Reconstruct the parameters as they were in the original ASP.NET redirect
            # Note: getRandomKey and ModId/SubModId are specific to the original system.
            # In Django, these would be handled via proper URL routing and permissions.
            # For demonstration, we'll mimic the redirect URL format.
            redirect_params = {
                'Id': gsn_note.id,
                'SupId': gsn_note.sup_id_display,
                'GSNNo': gsn_note.gsn_no,
                'GINNo': gsn_note.gin_no,
                'GINId': gsn_note.gin.id,
                'PONo': gsn_note.po_no_display_from_annotated,
                'FyId': gsn_note.financial_year.id,
                # 'Key': 'RANDOM_KEY', # Placeholder for random key
                # 'ModId': '9', 'SubModId': '39' # Placeholder for module IDs
            }
            # Construct a URL for the detail page.
            # In a real Django app, this would route to a proper GoodsServiceNoteDetailView.
            # For direct mimicry of the original URL, we build it manually.
            # Example: reverse_lazy('inventory:goodsservicenote_detail', kwargs={'pk': gsn_note.id})
            # Or if it's a legacy system with specific URL format:
            # Note: This hardcoded URL implies a non-Django target or a very specific legacy integration.
            # Ideally, it would be a named Django URL.
            query_string = '&'.join([f"{k}={v}" for k, v in redirect_params.items()])
            redirect_url = f"/inventory/goodsservicenote/details/{gsn_note.id}/?{query_string}"
            # For a proper Django app, this would be:
            # return redirect(reverse_lazy('inventory:goodsservicenote_detail', kwargs={'pk': gsn_note.id}))
            # For direct mimicry, we'll send a 302 redirect.
            response = HttpResponse(status=302)
            response['Location'] = redirect_url
            return response
        except GoodsServiceNote.DoesNotExist:
            return HttpResponse("GSN not found.", status=404)
        except Exception as e:
            messages.error(request, f"Error selecting GSN: {e}")
            return HttpResponse(f"Error: {e}", status=500)
```

#### 4.4 Templates (`inventory/templates/inventory/goodsservicenote/`)

These templates handle the rendering of your application's UI, integrating HTMX and Alpine.js for dynamic interactions and DataTables for powerful list presentation.

**`list.html`** (Main page template)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ supplierSearch: '', selectedSupplierId: '' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Service Note [GSN] - Print</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <h3 class="text-lg font-semibold text-gray-700 mb-4">Search GSN</h3>
        <form hx-get="{% url 'inventory:goodsservicenote_table' %}"
              hx-target="#goodsservicenote-table-container"
              hx-swap="innerHTML"
              hx-indicator="#loading-indicator"
              class="space-y-4">
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.supplier_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.supplier_input.label }}
                    </label>
                    <div class="relative">
                        {{ form.supplier_input }}
                        <div id="supplier-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1"
                             x-show="supplierSearch.length > 0"
                             x-cloak
                             @click.away="supplierSearch = ''">
                            <!-- Autocomplete suggestions will be loaded here by HTMX -->
                        </div>
                    </div>
                    {{ form.supplier_id }} {# Hidden field to hold the selected supplier's ID #}
                    {% if form.supplier_input.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.supplier_input.errors }}</p>
                    {% endif %}
                </div>
            </div>
            
            <div class="flex justify-end mt-6">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm redbox">
                    Search
                </button>
            </div>
        </form>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md">
        <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-700">GSN List</h3>
        </div>
        <div id="goodsservicenote-table-container"
             hx-trigger="load, refreshGSNList from:body" {# Initial load and refresh trigger #}
             hx-get="{% url 'inventory:goodsservicenote_table' %}?company_id={{ company_id }}&financial_year_id={{ financial_year_id }}"
             hx-swap="innerHTML">
            <!-- Loading indicator for initial load -->
            <div id="loading-indicator" class="text-center p-4">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading GSN data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.tailwindcss.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('gsnSearch', () => ({
            supplierSearch: '',
            selectedSupplierId: '',

            init() {
                // If the form was pre-filled (e.g., after an error), set Alpine state
                const supplierInput = document.getElementById('id_supplier_input');
                const supplierIdInput = document.getElementById('id_supplier_id');
                if (supplierInput && supplierInput.value) {
                    this.supplierSearch = supplierInput.value;
                }
                if (supplierIdInput && supplierIdInput.value) {
                    this.selectedSupplierId = supplierIdInput.value;
                }
            },

            selectSuggestion(name, id) {
                this.supplierSearch = name;
                this.selectedSupplierId = id;
                document.getElementById('id_supplier_id').value = id; // Update the hidden input
                document.getElementById('supplier-suggestions').innerHTML = ''; // Clear suggestions
            },

            clearSupplierId() {
                // Clear the hidden ID when the user types
                this.selectedSupplierId = '';
                document.getElementById('id_supplier_id').value = '';
            },

            selectFirstSuggestion(event) {
                const firstSuggestion = document.querySelector('#supplier-suggestions li button');
                if (firstSuggestion) {
                    firstSuggestion.click();
                    event.preventDefault(); // Prevent tab from moving out of the form
                }
            }
        }));
    });

    // Helper to initialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'goodsservicenote-table-container') {
            const table = document.getElementById('goodsservicenoteTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $('#goodsservicenoteTable').DataTable({
                    "pageLength": 20, // Match ASP.NET PageSize
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "responsive": true
                });
            }
        }
    });
</script>
{% endblock %}

{% block extra_css %}
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.tailwindcss.min.css">
    {# If you have custom CSS from StyleSheet.css, integrate it here with Tailwind #}
    <style>
        .box3 { /* Mimicking original CSS class */
            /* Add Tailwind equivalents or custom styles */
        }
        .redbox { /* Mimicking original CSS class */
            background-color: #ef4444; /* red-500 */
        }
        .redbox:hover {
            background-color: #dc2626; /* red-600 */
        }
    </style>
{% endblock %}
```

**`_goodsservicenotetable.html`** (Partial for the DataTables content)

```html
<table id="goodsservicenoteTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GSN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PONo</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if goodsservicenotes %}
            {% for obj in goodsservicenotes %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.fin_year_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.gsn_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.gin_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.system_date_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.po_no_display_from_annotated }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-left">{{ obj.supplier_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.challan_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.challan_date_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">
                    <a hx-get="{% url 'inventory:goodsservicenote_select' pk=obj.pk %}"
                       hx-trigger="click"
                       hx-swap="none" {# Let the browser handle the redirect #}
                       class="text-blue-600 hover:text-blue-900 font-medium">
                        Select
                    </a>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="10" class="py-4 px-4 text-center text-gray-500">
                    No data to display !
                </td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Initialize DataTables only if it hasn't been initialized
        const table = $('#goodsservicenoteTable');
        if (table.length && !$.fn.DataTable.isDataTable(table)) {
            table.DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true
            });
        }
    });
</script>
```

**`_supplier_autocomplete_suggestions.html`** (Partial for supplier autocomplete)

```html
{% comment %}
    This partial is loaded via HTMX into the #supplier-suggestions div.
    Alpine.js will then process these `<li>` elements.
{% endcomment %}
<ul class="max-h-60 overflow-y-auto">
    {% for supplier in suppliers %}
        <li>
            <button type="button" 
                    class="block w-full text-left px-4 py-2 hover:bg-gray-100 focus:outline-none focus:bg-gray-100"
                    @click="selectSuggestion('{{ supplier.name }} [{{ supplier.id }}]', '{{ supplier.id }}')">
                {{ supplier.name }} [{{ supplier.id }}]
            </button>
        </li>
    {% empty %}
        <li>
            <span class="block px-4 py-2 text-gray-500">No suggestions</span>
        </li>
    {% endfor %}
</ul>
```

#### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for your views, making them accessible via web requests.

```python
from django.urls import path
from .views import (
    GoodsServiceNotePrintListView,
    GoodsServiceNoteTablePartialView,
    SupplierAutocompleteView,
    GoodsServiceNoteDetailView, # For the 'Select' action mimicry
)

app_name = 'inventory'

urlpatterns = [
    # Main page for GSN print
    path('goodsservicenote/print/', GoodsServiceNotePrintListView.as_view(), name='goodsservicenote_print_list'),

    # HTMX endpoint to load/refresh the GSN table content
    path('goodsservicenote/print/table/', GoodsServiceNoteTablePartialView.as_view(), name='goodsservicenote_table'),

    # HTMX endpoint for supplier autocomplete suggestions
    path('goodsservicenote/supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # Endpoint to handle the 'Select' action and redirect
    path('goodsservicenote/print/select/<int:pk>/', GoodsServiceNoteDetailView.as_view(), name='goodsservicenote_select'),

    # In a full migration, you would also have a detail view like:
    # path('goodsservicenote/details/<int:pk>/', GoodsServiceNoteDetailView.as_view(), name='goodsservicenote_detail'),
]
```

#### 4.6 Tests (`inventory/tests.py`)

Comprehensive unit tests for models and integration tests for views are crucial for ensuring correctness and maintaining code quality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date
from .models import GoodsServiceNote, FinancialYear, Supplier, PurchaseOrder, Inward, InwardDetail, Company

class InventoryModelsTest(TestCase):
    """
    Unit tests for the Inventory app models.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=2023, fin_year='2023-2024')
        cls.supplier = Supplier.objects.create(id='SUP001', name='Test Supplier')
        cls.po = PurchaseOrder.objects.create(id=101, po_no='PO-2023-001', supplier=cls.supplier)
        cls.inward = Inward.objects.create(id=201, po_no_text='PO-2023-001', challan_no='CH-001', challan_date=date(2023, 1, 15))
        cls.inward_detail = InwardDetail.objects.create(id=301, gin_master=cls.inward, po_master=cls.po)

        cls.gsn_note_1 = GoodsServiceNote.objects.create(
            id=1,
            financial_year=cls.fin_year,
            gsn_no='GSN-001',
            gin_no='GIN-001',
            gin=cls.inward,
            system_date=date(2023, 1, 20),
            # company=cls.company # Uncomment if company FK is added to GSN
        )

        cls.fin_year_older = FinancialYear.objects.create(id=2022, fin_year='2022-2023')
        cls.supplier_2 = Supplier.objects.create(id='SUP002', name='Another Supplier')
        cls.po_2 = PurchaseOrder.objects.create(id=102, po_no='PO-2022-002', supplier=cls.supplier_2)
        cls.inward_2 = Inward.objects.create(id=202, po_no_text='PO-2022-002', challan_no='CH-002', challan_date=date(2022, 12, 1))
        cls.inward_detail_2 = InwardDetail.objects.create(id=302, gin_master=cls.inward_2, po_master=cls.po_2)

        cls.gsn_note_2 = GoodsServiceNote.objects.create(
            id=2,
            financial_year=cls.fin_year_older,
            gsn_no='GSN-002',
            gin_no='GIN-002',
            gin=cls.inward_2,
            system_date=date(2022, 12, 5),
            # company=cls.company
        )

    def test_goods_service_note_creation(self):
        self.assertEqual(self.gsn_note_1.gsn_no, 'GSN-001')
        self.assertEqual(self.gsn_note_1.gin_no, 'GIN-001')
        self.assertEqual(self.gsn_note_1.system_date, date(2023, 1, 20))
        self.assertEqual(self.gsn_note_1.financial_year, self.fin_year)
        self.assertEqual(self.gsn_note_1.gin, self.inward)

    def test_gsn_properties_display(self):
        # Test properties that derive values from related objects or annotations
        gsn = GoodsServiceNote.objects.get(pk=self.gsn_note_1.pk)
        self.assertEqual(gsn.fin_year_display, '2023-2024')
        self.assertEqual(gsn.challan_no_display, 'CH-001')
        self.assertEqual(gsn.challan_date_display, '15/01/2023')
        self.assertEqual(gsn.system_date_display, '20/01/2023')

        # Test annotated properties from custom manager
        # Need to fetch via manager method that adds annotations
        gsn_annotated = GoodsServiceNote.objects.filter_by_params(
            company_id=self.company.id, financial_year_id=self.fin_year.id
        ).get(pk=self.gsn_note_1.pk)

        self.assertEqual(gsn_annotated.po_no_display_from_annotated, 'PO-2023-001')
        self.assertEqual(gsn_annotated.supplier_display, 'Test Supplier')
        self.assertEqual(gsn_annotated.sup_id_display, 'SUP001')

    def test_goodsservicenote_manager_filter_by_params_no_supplier(self):
        # Test default filter by company and financial year
        queryset = GoodsServiceNote.objects.filter_by_params(
            company_id=self.company.id, financial_year_id=self.fin_year.id
        )
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.gsn_note_1, queryset)
        self.assertNotIn(self.gsn_note_2, queryset) # GSN-002 has older fin year

    def test_goodsservicenote_manager_filter_by_params_with_supplier(self):
        # Test filter by supplier ID
        queryset = GoodsServiceNote.objects.filter_by_params(
            company_id=self.company.id, financial_year_id=self.fin_year.id, supplier_id='SUP001'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.gsn_note_1, queryset)
        self.assertNotIn(self.gsn_note_2, queryset)

        queryset = GoodsServiceNote.objects.filter_by_params(
            company_id=self.company.id, financial_year_id=self.fin_year.id, supplier_id='SUP002'
        )
        self.assertEqual(queryset.count(), 0) # SUP002 is linked to an older GSN

        queryset = GoodsServiceNote.objects.filter_by_params(
            company_id=self.company.id, financial_year_id=self.fin_year_older.id, supplier_id='SUP002'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.gsn_note_2, queryset)


class InventoryViewsTest(TestCase):
    """
    Integration tests for the Inventory app views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Reuse data from model tests if possible, or define here
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=2023, fin_year='2023-2024')
        cls.supplier = Supplier.objects.create(id='SUP001', name='Test Supplier')
        cls.po = PurchaseOrder.objects.create(id=101, po_no='PO-2023-001', supplier=cls.supplier)
        cls.inward = Inward.objects.create(id=201, po_no_text='PO-2023-001', challan_no='CH-001', challan_date=date(2023, 1, 15))
        cls.inward_detail = InwardDetail.objects.create(id=301, gin_master=cls.inward, po_master=cls.po)
        cls.gsn_note_1 = GoodsServiceNote.objects.create(
            id=1, financial_year=cls.fin_year, gsn_no='GSN-001', gin_no='GIN-001', gin=cls.inward, system_date=date(2023, 1, 20),
            # company=cls.company
        )

        cls.supplier_2 = Supplier.objects.create(id='SUP002', name='Another Supplier')
        cls.po_2 = PurchaseOrder.objects.create(id=102, po_no='PO-2022-002', supplier=cls.supplier_2)
        cls.inward_2 = Inward.objects.create(id=202, po_no_text='PO-2022-002', challan_no='CH-002', challan_date=date(2022, 12, 1))
        cls.inward_detail_2 = InwardDetail.objects.create(id=302, gin_master=cls.inward_2, po_master=cls.po_2)
        cls.gsn_note_2 = GoodsServiceNote.objects.create(
            id=2, financial_year=FinancialYear.objects.create(id=2022, fin_year='2022-2023'), gsn_no='GSN-002', gin_no='GIN-002', gin=cls.inward_2, system_date=date(2022, 12, 5),
            # company=cls.company
        )

    def setUp(self):
        self.client = Client()
        # Set session data to mimic ASP.NET context
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year.id
        session.save()

    def test_gsn_print_list_view(self):
        response = self.client.get(reverse('inventory:goodsservicenote_print_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/list.html')
        self.assertContains(response, 'Goods Service Note [GSN] - Print')
        self.assertContains(response, '<div id="goodsservicenote-table-container"') # Check for HTMX target div
        self.assertContains(response, '<input type="text" name="supplier_input"') # Check for supplier input

    def test_gsn_table_partial_view_initial_load(self):
        response = self.client.get(reverse('inventory:goodsservicenote_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/_goodsservicenotetable.html')
        self.assertContains(response, 'GSN-001') # Check if GSN-001 is present
        self.assertNotContains(response, 'GSN-002') # Should not contain older GSN

    def test_gsn_table_partial_view_search_by_supplier(self):
        # Simulate HTMX request with supplier_id
        response = self.client.get(
            reverse('inventory:goodsservicenote_table'),
            {'supplier_id': self.supplier.id},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/_goodsservicenotetable.html')
        self.assertContains(response, 'GSN-001')
        self.assertContains(response, 'Test Supplier')
        self.assertNotContains(response, 'GSN-002') # Filtered out by supplier

        response_no_match = self.client.get(
            reverse('inventory:goodsservicenote_table'),
            {'supplier_id': 'NONEXISTENT'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_no_match.status_code, 200)
        self.assertContains(response_no_match, 'No data to display !')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(
            reverse('inventory:supplier_autocomplete'),
            {'q': 'Test'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsservicenote/_supplier_autocomplete_suggestions.html')
        self.assertContains(response, 'Test Supplier [SUP001]')
        self.assertNotContains(response, 'Another Supplier')

        response_empty = self.client.get(
            reverse('inventory:supplier_autocomplete'),
            {'q': ''},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_empty.status_code, 200)
        self.assertContains(response_empty, '') # Should return empty string or empty suggestions

        response_no_results = self.client.get(
            reverse('inventory:supplier_autocomplete'),
            {'q': 'XYZXYZ'},
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_no_results.status_code, 200)
        self.assertContains(response_no_results, 'No suggestions')

    def test_goodsservicenote_select_view(self):
        response = self.client.get(reverse('inventory:goodsservicenote_select', args=[self.gsn_note_1.pk]))
        self.assertEqual(response.status_code, 302) # Expect a redirect
        # Check that the redirect URL contains the correct parameters
        expected_redirect_url_start = f'/inventory/goodsservicenote/details/{self.gsn_note_1.pk}/?'
        self.assertTrue(response.url.startswith(expected_redirect_url_start))
        self.assertIn(f'Id={self.gsn_note_1.pk}', response.url)
        self.assertIn(f'SupId={self.supplier.id}', response.url)
        self.assertIn(f'GSNNo={self.gsn_note_1.gsn_no}', response.url)
        self.assertIn(f'GINNo={self.gsn_note_1.gin_no}', response.url)
        self.assertIn(f'GINId={self.gsn_note_1.gin.id}', response.url)
        self.assertIn(f'PONo={self.po.po_no}', response.url) # Direct PO_NO from the model
        self.assertIn(f'FyId={self.fin_year.id}', response.url)

    def test_goodsservicenote_select_view_not_found(self):
        response = self.client.get(reverse('inventory:goodsservicenote_select', args=[99999])) # Non-existent PK
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, "GSN not found.")
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:**
    *   The main `list.html` page uses `hx-get` on a `div` (`#goodsservicenote-table-container`) to load the table content (`_goodsservicenotetable.html`) initially and whenever a `refreshGSNList` event is triggered (e.g., after a new GSN is added/edited/deleted, though not directly applicable for this print view).
    *   The search `form` uses `hx-get` to trigger a re-load of the table partial with new filter parameters.
    *   The `supplier_input` field uses `hx-get` with `hx-trigger="keyup changed delay:500ms, search"` to fetch autocomplete suggestions from `{% url 'inventory:supplier_autocomplete' %}`.
    *   The "Select" action `<a>` tag uses `hx-get` and `hx-swap="none"` to trigger the backend redirect, preventing HTMX from attempting to swap content.
*   **Alpine.js for UI State Management:**
    *   `x-data="{ supplierSearch: '', selectedSupplierId: '' }"` initializes reactive data for the search input and hidden ID.
    *   `x-model` binds `supplierSearch` to the `supplier_input` field.
    *   `x-show` controls the visibility of the autocomplete suggestions div.
    *   `@click` and `@input` handlers manage selecting suggestions and clearing the hidden ID when the user types.
*   **DataTables for List Views:**
    *   The `_goodsservicenotetable.html` partial contains a standard `<table>` with `id="goodsservicenoteTable"`.
    *   A JavaScript block within `_goodsservicenotetable.html` (or attached after HTMX swap) initializes this table using `$('#goodsservicenoteTable').DataTable()`. This enables client-side pagination, searching, and sorting automatically.
    *   The `htmx:afterSwap` event listener in `list.html` ensures DataTables is re-initialized correctly after HTMX swaps the table content.
*   **No Full Page Reloads:** All interactions (search, autocomplete, table refresh) are handled asynchronously using HTMX, providing a smooth user experience without full page reloads.
*   **Strict Separation of Concerns:** Python views handle data fetching and logic; HTML templates render the UI; DataTables, HTMX, and Alpine.js manage client-side interactivity.

---

## Final Notes

*   This plan provides a comprehensive migration pathway for the "Goods Service Note [GSN] - Print" module.
*   Placeholders like `DEFAULT_COMPANY_ID` and `DEFAULT_FINANCIAL_YEAR_ID` should be replaced with actual dynamic values from your Django authentication system or multi-tenancy setup.
*   The `GoodsServiceNoteDetailView` currently mimics the original ASP.NET redirect. In a full Django modernization, this would likely be replaced with a proper Django detail view (e.g., a `DetailView` CBV) that renders the details within your Django application, possibly using HTMX to load them into a modal if desired for a more seamless UI.
*   Thorough testing with your actual data and environment is critical, especially for the complex data retrieval logic mimicked by the `GoodsServiceNoteManager`.
*   Ensure all necessary static files (CSS, JS, images) are correctly configured and served by Django.
*   Consider using Django Admin for rapid CRUD operations on your `managed=False` models for administrative purposes.