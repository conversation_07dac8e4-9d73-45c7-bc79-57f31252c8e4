## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code for `MaterialIssueNote_Dashboard.aspx` and its empty C# code-behind file `MaterialIssueNote_Dashboard.aspx.cs` offers minimal explicit information regarding database schema or business logic. Based on the page name "Material Issue Note Dashboard", we infer that this page likely serves as a central view for managing "Material Issue Notes," implying a list of notes and associated CRUD (Create, Read, Update, Delete) operations.

For this modernization plan, we will assume a common database table structure for "Material Issue Notes" and implement standard CRUD functionality using Django, HTMX, and Alpine.js.

## Step 1: Extract Database Schema

**Analysis:**
Since no explicit database schema or SQL commands are present in the provided ASP.NET code, we will infer a typical schema for a "Material Issue Note" based on common enterprise resource planning (ERP) requirements.

**Inferred Schema:**

*   **[TABLE_NAME]:** `tblMaterialIssueNote` (a common legacy naming convention)
*   **Columns:**
    *   `Id` (Primary Key, integer)
    *   `IssueNoteNo` (String, unique identifier for the note)
    *   `IssueDate` (Date, when the material was issued)
    *   `IssuedBy` (String, who issued the material)
    *   `Department` (String, to which department it was issued)
    *   `Status` (String, e.g., 'Draft', 'Issued', 'Cancelled')
    *   `Remarks` (Text, any additional notes)

## Step 2: Identify Backend Functionality

**Analysis:**
Given "Material Issue Note Dashboard" typically implies a list view of material issue notes with the ability to manage them.

**Inferred Functionality:**

*   **Create:** Ability to add new Material Issue Notes.
*   **Read:** Display a list of all Material Issue Notes, with filtering and sorting capabilities.
*   **Update:** Edit existing Material Issue Notes.
*   **Delete:** Remove Material Issue Notes.
*   **Validation:** Basic field validations (e.g., required fields, date formats) will be implemented in Django Forms.

## Step 3: Infer UI Components

**Analysis:**
The `.aspx` file primarily defines content placeholders, and the code-behind is empty. A "Dashboard" for material issue notes would typically contain a data grid for listing items and buttons/links for actions.

**Inferred UI Components:**

*   **List View:** A table (like an ASP.NET `GridView`) to display multiple Material Issue Notes, supporting client-side search, sort, and pagination.
*   **Form (Add/Edit):** Input fields (like `TextBox`es, `DropDownList`s) for `IssueNoteNo`, `IssueDate`, `IssuedBy`, `Department`, `Status`, and `Remarks`.
*   **Action Buttons:** Buttons or links (like `Button`, `LinkButton`) for "Add New", "Edit", and "Delete".
*   **Dynamic Interactions:** The `loadingNotifier.js` suggests some client-side interaction. We will modernize this with HTMX for dynamic content loading and Alpine.js for simple UI state management (e.g., modal visibility).

## Step 4: Generate Django Code

### 4.1 Models (`inventory/models.py`)

```python
from django.db import models
from django.utils import timezone

class MaterialIssueNote(models.Model):
    # Primary key is implicitly added by Django, but we'll use a specific db_column for clarity
    id = models.AutoField(primary_key=True, db_column='Id')
    issue_note_no = models.CharField(max_length=50, unique=True, db_column='IssueNoteNo', verbose_name='Issue Note No.')
    issue_date = models.DateField(db_column='IssueDate', default=timezone.now, verbose_name='Issue Date')
    issued_by = models.CharField(max_length=100, db_column='IssuedBy', verbose_name='Issued By')
    department = models.CharField(max_length=100, db_column='Department', verbose_name='Department')
    status = models.CharField(max_length=20, db_column='Status', default='Draft', verbose_name='Status')
    remarks = models.TextField(blank=True, null=True, db_column='Remarks', verbose_name='Remarks')

    class Meta:
        managed = False  # Set to False if managing schema externally or if table pre-exists
        db_table = 'tblMaterialIssueNote'
        verbose_name = 'Material Issue Note'
        verbose_name_plural = 'Material Issue Notes'
        ordering = ['-issue_date', 'issue_note_no'] # Default ordering

    def __str__(self):
        return f"{self.issue_note_no} ({self.issue_date.strftime('%Y-%m-%d')})"
        
    def is_editable(self):
        """
        Business logic: Determine if the note can be edited.
        Example: Only 'Draft' notes can be edited.
        """
        return self.status == 'Draft'

    def update_status(self, new_status):
        """
        Business logic: Update the status of the material issue note.
        """
        valid_statuses = ['Draft', 'Issued', 'Cancelled']
        if new_status not in valid_statuses:
            raise ValueError("Invalid status provided.")
        self.status = new_status
        self.save()
        return True
```

### 4.2 Forms (`inventory/forms.py`)

```python
from django import forms
from .models import MaterialIssueNote

class MaterialIssueNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialIssueNote
        fields = ['issue_note_no', 'issue_date', 'issued_by', 'department', 'status', 'remarks']
        widgets = {
            'issue_note_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MIN-YYMMDD-XXX'}),
            'issue_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'issued_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[('Draft', 'Draft'), ('Issued', 'Issued'), ('Cancelled', 'Cancelled')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        
    def clean_issue_note_no(self):
        issue_note_no = self.cleaned_data['issue_note_no']
        # Example validation: ensure issue_note_no follows a pattern
        if not issue_note_no.startswith('MIN-'):
            raise forms.ValidationError("Issue Note Number must start with 'MIN-'.")
        return issue_note_no

    def clean(self):
        cleaned_data = super().clean()
        # Example cross-field validation
        if 'issue_date' in cleaned_data and cleaned_data['issue_date'] > forms.DateField().to_python('9999-12-31'):
            self.add_error('issue_date', "Issue date cannot be in the far future.")
        return cleaned_data
```

### 4.3 Views (`inventory/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialIssueNote
from .forms import MaterialIssueNoteForm

class MaterialIssueNoteListView(ListView):
    model = MaterialIssueNote
    template_name = 'inventory/materialissuenote/list.html'
    context_object_name = 'material_issue_notes'

class MaterialIssueNoteTablePartialView(ListView):
    model = MaterialIssueNote
    template_name = 'inventory/materialissuenote/_materialissuenote_table.html'
    context_object_name = 'material_issue_notes'

class MaterialIssueNoteCreateView(CreateView):
    model = MaterialIssueNote
    form_class = MaterialIssueNoteForm
    template_name = 'inventory/materialissuenote/_materialissuenote_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('materialissuenote_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Issue Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response

class MaterialIssueNoteUpdateView(UpdateView):
    model = MaterialIssueNote
    form_class = MaterialIssueNoteForm
    template_name = 'inventory/materialissuenote/_materialissuenote_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('materialissuenote_list')

    def form_valid(self, form):
        # Example of moving business logic to model (can be extended)
        if not form.instance.is_editable():
            messages.error(self.request, "This Material Issue Note cannot be edited in its current status.")
            return self.form_invalid(form) # Keep the form open with error

        response = super().form_valid(form)
        messages.success(self.request, 'Material Issue Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response

class MaterialIssueNoteDeleteView(DeleteView):
    model = MaterialIssueNote
    template_name = 'inventory/materialissuenote/_materialissuenote_confirm_delete.html' # Use partial for HTMX modal
    success_url = reverse_lazy('materialissuenote_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Issue Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content
                headers={
                    'HX-Trigger': 'refreshMaterialIssueNoteList'
                }
            )
        return response
```

### 4.4 Templates

#### List Template (`inventory/templates/inventory/materialissuenote/list.html`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Issue Notes Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialissuenote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Material Issue Note
        </button>
    </div>
    
    <div id="materialissuenoteTable-container"
         hx-trigger="load, refreshMaterialIssueNoteList from:body"
         hx-get="{% url 'materialissuenote_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Issue Notes...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden is-active:flex transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto transform scale-95 opacity-0 is-active:scale-100 is-active:opacity-100 transition-all duration-300"
             _="on load add .is-active to #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For simple modal show/hide, htmx and _ (hyperscript) is sufficient.
    });
    // Global listener to hide modal on HTMX 204 response (successful form submission/deletion)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
            // Re-enable body scroll if it was disabled by the modal
            document.body.style.overflow = ''; 
        }
    });

    // Handle modal show/hide with body scroll management
    document.body.addEventListener('click', function(event) {
        if (event.target.closest('[hx-target="#modalContent"]')) {
            document.body.style.overflow = 'hidden'; // Disable body scroll when modal opens
        } else if (event.target.id === 'modal' || event.target.closest('[hx-post][hx-swap="none"]')) {
             // If clicking outside the modal or successful form submission (hx-swap="none")
             // Scroll will be re-enabled by htmx:afterRequest for 204 status.
        } else if (event.target.closest('button[_="on click remove .is-active from #modal"]')) {
            document.body.style.overflow = ''; // Re-enable body scroll if Cancel button is clicked
        }
    });
</script>
{% endblock %}
```

#### Table Partial Template (`inventory/templates/inventory/materialissuenote/_materialissuenote_table.html`)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="materialissuenoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Note No.</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issued By</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for note in material_issue_notes %}
            <tr class="hover:bg-gray-100">
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-blue-600">{{ note.issue_note_no }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ note.issue_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ note.issued_by }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ note.department }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if note.status == 'Issued' %}bg-green-100 text-green-800
                    {% elif note.status == 'Draft' %}bg-yellow-100 text-yellow-800
                    {% else %}bg-red-100 text-red-800{% endif %}">
                        {{ note.status }}
                    </span>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm mr-2 transition duration-300"
                        hx-get="{% url 'materialissuenote_edit' note.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm transition duration-300"
                        hx-get="{% url 'materialissuenote_delete' note.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No Material Issue Notes found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#materialissuenoteTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers",
        "responsive": true,
        "language": {
            "search": "Search:",
            "lengthMenu": "Show _MENU_ entries",
            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
            "infoEmpty": "Showing 0 to 0 of 0 entries",
            "infoFiltered": "(filtered from _MAX_ total entries)",
            "paginate": {
                "first": "First",
                "last": "Last",
                "next": "Next",
                "previous": "Previous"
            }
        }
    });
});
</script>
```

#### Form Partial Template (`inventory/templates/inventory/materialissuenote/_materialissuenote_form.html`)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Material Issue Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#modalLoadingIndicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4 {% if field.name == 'remarks' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300">
                Save Material Issue Note
            </button>
        </div>
    </form>
    <div id="modalLoadingIndicator" class="htmx-indicator fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-50">
        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        <p class="ml-3 text-white text-lg">Processing...</p>
    </div>
</div>
```

#### Delete Confirmation Partial Template (`inventory/templates/inventory/materialissuenote/_materialissuenote_confirm_delete.html`)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Material Issue Note: <span class="font-bold">{{ object.issue_note_no }}</span>?</p>
    
    <form hx-post="{% url 'materialissuenote_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-300"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`inventory/urls.py`)

```python
from django.urls import path
from .views import (
    MaterialIssueNoteListView, 
    MaterialIssueNoteCreateView, 
    MaterialIssueNoteUpdateView, 
    MaterialIssueNoteDeleteView,
    MaterialIssueNoteTablePartialView
)

urlpatterns = [
    path('materialissuenotes/', MaterialIssueNoteListView.as_view(), name='materialissuenote_list'),
    path('materialissuenotes/add/', MaterialIssueNoteCreateView.as_view(), name='materialissuenote_add'),
    path('materialissuenotes/edit/<int:pk>/', MaterialIssueNoteUpdateView.as_view(), name='materialissuenote_edit'),
    path('materialissuenotes/delete/<int:pk>/', MaterialIssueNoteDeleteView.as_view(), name='materialissuenote_delete'),
    # HTMX-specific endpoint for refreshing the table
    path('materialissuenotes/table/', MaterialIssueNoteTablePartialView.as_view(), name='materialissuenote_table_partial'),
]
```

### 4.6 Tests (`inventory/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import MaterialIssueNote
from .forms import MaterialIssueNoteForm

class MaterialIssueNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.note1 = MaterialIssueNote.objects.create(
            issue_note_no='MIN-20231026-001',
            issue_date='2023-10-26',
            issued_by='John Doe',
            department='Production',
            status='Draft',
            remarks='Initial draft for production run.'
        )
        cls.note2 = MaterialIssueNote.objects.create(
            issue_note_no='MIN-20231027-002',
            issue_date='2023-10-27',
            issued_by='Jane Smith',
            department='Assembly',
            status='Issued',
            remarks='Materials issued for assembly.'
        )
  
    def test_materialissuenote_creation(self):
        obj = MaterialIssueNote.objects.get(issue_note_no='MIN-20231026-001')
        self.assertEqual(obj.issue_date, timezone.localdate(timezone.datetime(2023, 10, 26)))
        self.assertEqual(obj.issued_by, 'John Doe')
        self.assertEqual(obj.department, 'Production')
        self.assertEqual(obj.status, 'Draft')
        self.assertEqual(obj.remarks, 'Initial draft for production run.')
        self.assertEqual(str(obj), 'MIN-20231026-001 (2023-10-26)')
        
    def test_issue_note_no_label(self):
        obj = MaterialIssueNote.objects.get(id=self.note1.id)
        field_label = obj._meta.get_field('issue_note_no').verbose_name
        self.assertEqual(field_label, 'Issue Note No.')
        
    def test_is_editable_method(self):
        self.assertTrue(self.note1.is_editable()) # Draft status
        self.assertFalse(self.note2.is_editable()) # Issued status
        
    def test_update_status_method(self):
        self.note1.update_status('Issued')
        self.assertEqual(self.note1.status, 'Issued')
        
        with self.assertRaises(ValueError):
            self.note1.update_status('InvalidStatus')

class MaterialIssueNoteFormTest(TestCase):
    def test_form_valid_data(self):
        form = MaterialIssueNoteForm(data={
            'issue_note_no': 'MIN-20240101-001',
            'issue_date': '2024-01-01',
            'issued_by': 'Test User',
            'department': 'Testing',
            'status': 'Draft',
            'remarks': 'Form test.'
        })
        self.assertTrue(form.is_valid())
        
    def test_form_invalid_issue_note_no(self):
        form = MaterialIssueNoteForm(data={
            'issue_note_no': 'ABC-20240101-001', # Invalid format
            'issue_date': '2024-01-01',
            'issued_by': 'Test User',
            'department': 'Testing',
            'status': 'Draft',
            'remarks': 'Form test.'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('issue_note_no', form.errors)
        self.assertIn("Issue Note Number must start with 'MIN-'.", form.errors['issue_note_no'])

class MaterialIssueNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.note = MaterialIssueNote.objects.create(
            issue_note_no='MIN-VIEWS-001',
            issue_date='2023-11-01',
            issued_by='View Test',
            department='QA',
            status='Draft',
            remarks='For view testing.'
        )
    
    def setUp(self):
        # Set up client for each test method
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('materialissuenote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/list.html')
        self.assertIn('material_issue_notes', response.context)
        self.assertContains(response, self.note.issue_note_no)
        
    def test_table_partial_view(self):
        response = self.client.get(reverse('materialissuenote_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_table.html')
        self.assertIn('material_issue_notes', response.context)
        self.assertContains(response, self.note.issue_note_no)

    def test_create_view_get(self):
        response = self.client.get(reverse('materialissuenote_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_success(self):
        data = {
            'issue_note_no': 'MIN-NEW-001',
            'issue_date': '2023-11-02',
            'issued_by': 'New User',
            'department': 'New Dept',
            'status': 'Draft',
            'remarks': 'New note added.'
        }
        response = self.client.post(reverse('materialissuenote_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful creation
        self.assertTrue(MaterialIssueNote.objects.filter(issue_note_no='MIN-NEW-001').exists())
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Material Issue Note added successfully.')

    def test_create_view_post_htmx_success(self):
        data = {
            'issue_note_no': 'MIN-HTMX-001',
            'issue_date': '2023-11-03',
            'issued_by': 'HTMX User',
            'department': 'HTMX Dept',
            'status': 'Draft',
            'remarks': 'HTMX note added.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialissuenote_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(MaterialIssueNote.objects.filter(issue_note_no='MIN-HTMX-001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialIssueNoteList')
        
    def test_create_view_post_invalid(self):
        data = {
            'issue_note_no': 'INVALID-001', # Invalid format
            'issue_date': '2023-11-02',
            'issued_by': '', # Missing required field
            'department': 'New Dept',
            'status': 'Draft',
            'remarks': 'Invalid form test.'
        }
        response = self.client.post(reverse('materialissuenote_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('issued_by', response.context['form'].errors)

    def test_update_view_get(self):
        response = self.client.get(reverse('materialissuenote_edit', args=[self.note.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.note)
        
    def test_update_view_post_success(self):
        new_note_no = 'MIN-UPDATED-001'
        data = {
            'issue_note_no': new_note_no,
            'issue_date': '2023-11-01', # Keep same date
            'issued_by': 'View Test Updated',
            'department': 'QA Updated',
            'status': 'Issued', # Change status
            'remarks': 'Updated note.'
        }
        response = self.client.post(reverse('materialissuenote_edit', args=[self.note.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.note.refresh_from_db()
        self.assertEqual(self.note.issued_by, 'View Test Updated')
        self.assertEqual(self.note.status, 'Issued')
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Material Issue Note updated successfully.')

    def test_update_view_post_htmx_success(self):
        data = {
            'issue_note_no': self.note.issue_note_no, # keep existing
            'issue_date': '2023-11-01',
            'issued_by': 'HTMX Updated',
            'department': 'HTMX QA',
            'status': 'Draft',
            'remarks': 'HTMX update.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialissuenote_edit', args=[self.note.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.note.refresh_from_db()
        self.assertEqual(self.note.issued_by, 'HTMX Updated')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialIssueNoteList')
        
    def test_delete_view_get(self):
        response = self.client.get(reverse('materialissuenote_delete', args=[self.note.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialissuenote/_materialissuenote_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.note)

    def test_delete_view_post_success(self):
        # Create a new note specifically for deletion test
        note_to_delete = MaterialIssueNote.objects.create(
            issue_note_no='MIN-DELETE-001',
            issue_date='2023-11-04',
            issued_by='Delete User',
            department='Delete Dept',
            status='Draft',
            remarks='Note to be deleted.'
        )
        response = self.client.post(reverse('materialissuenote_delete', args=[note_to_delete.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(MaterialIssueNote.objects.filter(pk=note_to_delete.pk).exists())
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Material Issue Note deleted successfully.')
        
    def test_delete_view_post_htmx_success(self):
        # Create a new note specifically for HTMX deletion test
        note_to_delete_htmx = MaterialIssueNote.objects.create(
            issue_note_no='MIN-HTMX-DELETE-001',
            issue_date='2023-11-05',
            issued_by='HTMX Delete User',
            department='HTMX Delete Dept',
            status='Draft',
            remarks='Note to be deleted via HTMX.'
        )
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('materialissuenote_delete', args=[note_to_delete_htmx.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialIssueNote.objects.filter(pk=note_to_delete_htmx.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialIssueNoteList')
```

## Step 5: HTMX and Alpine.js Integration

All dynamic interactions are handled using HTMX and Alpine.js as per the instructions:

*   **HTMX for CRUD Modals:**
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch the form/confirmation partials into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) are configured with `hx-swap="none"` and trigger a `204 No Content` response from Django views, which then sends an `HX-Trigger: refreshMaterialIssueNoteList` header. This avoids full page reloads and cleanly closes the modal.
    *   The `list.html` uses `hx-trigger="load, refreshMaterialIssueNoteList from:body"` on the `materialissuenoteTable-container` div to automatically load the table on page load and refresh it whenever a CRUD operation signals a list update.
*   **Alpine.js/Hyperscript for Modal State:**
    *   The `_` (Hyperscript) attribute is used directly on buttons and the modal overlay (`#modal`) to control the `.is-active` class, effectively showing and hiding the modal. This keeps JavaScript minimal and declarative.
    *   A global `htmx:afterRequest` listener is added in `list.html` to handle the `204` status and ensure the modal is hidden and body scroll is re-enabled after successful HTMX form submissions/deletions.
*   **DataTables for List Views:**
    *   The `_materialissuenote_table.html` partial contains the `<table>` element and the JavaScript initialization for DataTables. This ensures that when the table partial is loaded via HTMX, DataTables is initialized correctly for the newly loaded content, providing client-side searching, sorting, and pagination.
*   **DRY Templates:**
    *   The `_materialissuenote_table.html`, `_materialissuenote_form.html`, and `_materialissuenote_confirm_delete.html` are used as partials to avoid code duplication.
    *   All templates extend `core/base.html` for consistent layout and CDN links, as per the rules.

## Final Notes

This comprehensive plan provides a complete Django application structure for the Material Issue Note Dashboard, including inferred database schema, models, forms, views, URLs, and tests. It strictly adheres to the "fat model, thin view" principle, utilizes HTMX and Alpine.js for modern frontend interactions, and integrates DataTables for efficient data presentation. The entire process is designed for automated conversion and aims for high test coverage, delivering a robust and maintainable Django solution.