The ASP.NET code provided indicates a dashboard for "Material Credit Notes" within an "Inventory" module. However, the `.aspx` file is largely empty, and the C# code-behind contains only an empty `Page_Load` event. This means there are no explicit UI controls, database interactions, or business logic defined in the given snippets.

Therefore, for this modernization plan, we will infer a typical "Material Credit Note" entity and its associated CRUD (Create, Read, Update, Delete) operations, assuming a standard dashboard view with a list and modal forms for adding/editing. This approach aligns with the common patterns found in such enterprise applications and allows us to demonstrate a complete Django solution.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code does not contain direct database schema information (e.g., `SqlDataSource` definitions or explicit SQL queries). Based on the ASP.NET file path `Module_Inventory_Transactions_MaterialCreditNote_Dashboard.aspx`, we infer the core entity is a "Material Credit Note". We will assume a database table named `TblMaterialCreditNote` which is a common naming convention in legacy ASP.NET applications.

**Inferred Schema:**
*   **Table Name:** `TblMaterialCreditNote`
*   **Columns (inferred):**
    *   `Id` (Primary Key, integer)
    *   `CreditNoteNumber` (string, e.g., MCN-001-2023)
    *   `IssueDate` (date)
    *   `MaterialDescription` (text)
    *   `Quantity` (decimal)
    *   `Amount` (decimal)
    *   `Status` (string, e.g., 'Pending', 'Approved', 'Cancelled')
    *   `IssuedBy` (string, e.g., User ID)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Due to the minimal ASP.NET code, no specific CRUD operations are explicitly defined. However, a "Dashboard" typically implies listing, viewing details, and often includes functionality to add, edit, and delete records.

**Inferred Functionality:**
*   **Read (R):** Display a list of all Material Credit Notes.
*   **Create (C):** Ability to add a new Material Credit Note.
*   **Update (U):** Ability to modify an existing Material Credit Note.
*   **Delete (D):** Ability to remove a Material Credit Note.
*   **Validation:** Basic field validation (e.g., required fields, data types).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Without explicit ASP.NET controls, we infer the standard UI components for a dashboard based on common practices:

*   **List View:** A table (like a `GridView` in ASP.NET) to display `Material Credit Notes` with columns for key attributes and an "Actions" column for edit/delete buttons. This will be implemented with DataTables for rich client-side features.
*   **Input Forms:** For "Add" and "Edit" operations, we anticipate forms with text input fields (like `TextBox`), possibly date pickers, and dropdowns (if status or other lookup values are involved). These will be rendered within HTMX-powered modals.
*   **Action Buttons:** Buttons (e.g., "Add New", "Edit", "Delete") to trigger form modals or deletion confirmations.

### Step 4: Generate Django Code

We will create a new Django app named `inventory` to house this module.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `MaterialCreditNote` model will map to the `TblMaterialCreditNote` table.

```python
# inventory/models.py
from django.db import models
from django.utils import timezone

class MaterialCreditNote(models.Model):
    """
    Represents a material credit note in the system.
    Maps to the existing TblMaterialCreditNote table.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    credit_note_number = models.CharField(
        db_column='CreditNoteNumber', 
        max_length=50, 
        unique=True, 
        verbose_name="Credit Note No."
    )
    issue_date = models.DateField(
        db_column='IssueDate', 
        default=timezone.now, 
        verbose_name="Issue Date"
    )
    material_description = models.TextField(
        db_column='MaterialDescription', 
        verbose_name="Material Description"
    )
    quantity = models.DecimalField(
        db_column='Quantity', 
        max_digits=10, 
        decimal_places=2, 
        verbose_name="Quantity"
    )
    amount = models.DecimalField(
        db_column='Amount', 
        max_digits=12, 
        decimal_places=2, 
        verbose_name="Amount"
    )
    status = models.CharField(
        db_column='Status', 
        max_length=20, 
        default='Pending', 
        verbose_name="Status"
    )
    issued_by = models.CharField(
        db_column='IssuedBy', 
        max_length=100, 
        blank=True, 
        null=True, 
        verbose_name="Issued By"
    )

    class Meta:
        managed = False  # Set to False as the table already exists
        db_table = 'TblMaterialCreditNote'
        verbose_name = 'Material Credit Note'
        verbose_name_plural = 'Material Credit Notes'
        ordering = ['-issue_date', 'credit_note_number'] # Order by latest notes

    def __str__(self):
        return f"{self.credit_note_number} ({self.issue_date})"

    # Business logic methods for MaterialCreditNote
    def approve_credit_note(self):
        """
        Approves the material credit note if its status is 'Pending'.
        """
        if self.status == 'Pending':
            self.status = 'Approved'
            self.save()
            return True
        return False

    def cancel_credit_note(self):
        """
        Cancels the material credit note if its status is not 'Cancelled' or 'Approved'.
        """
        if self.status not in ['Cancelled', 'Approved']:
            self.status = 'Cancelled'
            self.save()
            return True
        return False

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for `MaterialCreditNote`, including form validation and Tailwind CSS classes for styling.

```python
# inventory/forms.py
from django import forms
from .models import MaterialCreditNote

class MaterialCreditNoteForm(forms.ModelForm):
    """
    Form for creating and updating MaterialCreditNote instances.
    """
    class Meta:
        model = MaterialCreditNote
        fields = [
            'credit_note_number', 
            'issue_date', 
            'material_description', 
            'quantity', 
            'amount', 
            'status', 
            'issued_by'
        ]
        widgets = {
            'credit_note_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'issue_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_description': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[
                ('Pending', 'Pending'),
                ('Approved', 'Approved'),
                ('Cancelled', 'Cancelled')
            ], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'issued_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'credit_note_number': 'Credit Note Number',
            'issue_date': 'Issue Date',
            'material_description': 'Material Description',
            'quantity': 'Quantity',
            'amount': 'Amount',
            'status': 'Status',
            'issued_by': 'Issued By',
        }

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is not None and quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive number.")
        return quantity

    def clean_amount(self):
        amount = self.cleaned_data.get('amount')
        if amount is not None and amount < 0:
            raise forms.ValidationError("Amount cannot be negative.")
        return amount

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept thin, delegating any complex logic to the model. HTMX headers are checked for partial responses.

```python
# inventory/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialCreditNote
from .forms import MaterialCreditNoteForm

class MaterialCreditNoteListView(ListView):
    """
    Displays a dashboard of Material Credit Notes.
    """
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/list.html'
    context_object_name = 'materialcreditnotes'

class MaterialCreditNoteTablePartialView(TemplateView):
    """
    Renders the DataTables partial for Material Credit Notes.
    This view is specifically for HTMX requests to refresh the table.
    """
    template_name = 'inventory/materialcreditnote/_materialcreditnote_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['materialcreditnotes'] = MaterialCreditNote.objects.all()
        return context

class MaterialCreditNoteCreateView(CreateView):
    """
    Handles creation of new Material Credit Notes.
    """
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html' # Use partial for modal
    success_url = reverse_lazy('materialcreditnote_list') # Not strictly used for HTMX but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success without navigating
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList' # Trigger HTMX event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors for re-rendering in modal
            return response
        return response

class MaterialCreditNoteUpdateView(UpdateView):
    """
    Handles updates to existing Material Credit Notes.
    """
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'inventory/materialcreditnote/_materialcreditnote_form.html' # Use partial for modal
    success_url = reverse_lazy('materialcreditnote_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Credit Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class MaterialCreditNoteDeleteView(DeleteView):
    """
    Handles deletion of Material Credit Notes.
    """
    model = MaterialCreditNote
    template_name = 'inventory/materialcreditnote/_materialcreditnote_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('materialcreditnote_list')

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        # Add any pre-delete business logic here if needed (fat model approach)
        # For example, obj.can_be_deleted()
        
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Material Credit Note "{obj.credit_note_number}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialCreditNoteList'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will extend `core/base.html` and use HTMX for dynamic interactions.

```html
{# inventory/templates/inventory/materialcreditnote/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Credit Notes</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'materialcreditnote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i>Add New Material Credit Note
        </button>
    </div>
    
    <!-- DataTable container, dynamically loaded by HTMX -->
    <div id="materialcreditnoteTable-container"
         hx-trigger="load, refreshMaterialCreditNoteList from:body"
         hx-get="{% url 'materialcreditnote_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md p-6">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Material Credit Notes...</p>
        </div>
    </div>
    
    <!-- HTMX Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden transition-opacity duration-300 ease-out"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 transform transition-transform duration-300 ease-out scale-95"
             _="on modal.is-active add .scale-100 then remove .scale-95">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.tailwindcss.min.js"></script>
<script>
    // Alpine.js is typically integrated via core/base.html
    // Any specific Alpine.js component initialization for this page goes here
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            open: false,
            show() { this.open = true },
            hide() { this.open = false }
        });
    });

    // Custom event listener for HTMX
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            // Modal content loaded, show the modal
            document.getElementById('modal').classList.add('is-active');
        }
    });

    // Close modal on HX-Trigger that refreshes list (status 204 response)
    document.body.addEventListener('refreshMaterialCreditNoteList', function(evt) {
        // Hide the modal after successful CRUD operation
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
        }
    });
</script>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

```html
{# inventory/templates/inventory/materialcreditnote/_materialcreditnote_table.html #}
<div class="overflow-x-auto">
    <table id="materialcreditnoteTable" class="min-w-full bg-white table-auto">
        <thead>
            <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left">SN</th>
                <th class="py-3 px-6 text-left">Credit Note No.</th>
                <th class="py-3 px-6 text-left">Issue Date</th>
                <th class="py-3 px-6 text-left">Material Description</th>
                <th class="py-3 px-6 text-left">Quantity</th>
                <th class="py-3 px-6 text-left">Amount</th>
                <th class="py-3 px-6 text-left">Status</th>
                <th class="py-3 px-6 text-center">Actions</th>
            </tr>
        </thead>
        <tbody class="text-gray-600 text-sm font-light">
            {% for note in materialcreditnotes %}
            <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-6 text-left">{{ note.credit_note_number }}</td>
                <td class="py-3 px-6 text-left">{{ note.issue_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-6 text-left">{{ note.material_description|truncatechars:50 }}</td>
                <td class="py-3 px-6 text-left">{{ note.quantity }}</td>
                <td class="py-3 px-6 text-left">{{ note.amount }}</td>
                <td class="py-3 px-6 text-left">
                    <span class="px-2 py-1 font-semibold leading-tight rounded-full 
                        {% if note.status == 'Approved' %}text-green-700 bg-green-100
                        {% elif note.status == 'Pending' %}text-yellow-700 bg-yellow-100
                        {% else %}text-red-700 bg-red-100{% endif %}">
                        {{ note.status }}
                    </span>
                </td>
                <td class="py-3 px-6 text-center">
                    <div class="flex item-center justify-center">
                        <button 
                            class="w-6 mr-2 transform hover:text-purple-500 hover:scale-110"
                            hx-get="{% url 'materialcreditnote_edit' note.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button 
                            class="w-6 mr-2 transform hover:text-purple-500 hover:scale-110"
                            hx-get="{% url 'materialcreditnote_delete' note.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            <i class="fas fa-trash-alt"></i>
                        </button>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#materialcreditnoteTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 7] } // Disable sorting for SN and Actions
            ]
        });
    });
</script>
```

```html
{# inventory/templates/inventory/materialcreditnote/_materialcreditnote_form.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} Material Credit Note
    </h3>
    <form hx-post="{{ request.path }}" hx-target="#modalContent" hx-swap="outerHTML">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i>Save
            </button>
        </div>
    </form>
</div>
```

```html
{# inventory/templates/inventory/materialcreditnote/_materialcreditnote_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-lg text-gray-700 mb-6">
        Are you sure you want to delete the Material Credit Note
        "<strong>{{ object.credit_note_number }}</strong>" issued on {{ object.issue_date|date:"Y-m-d" }}?
    </p>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" will rely on HX-Trigger #}
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i>Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the list view, and for HTMX-driven partials like the table, form, and delete confirmation.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    MaterialCreditNoteListView, 
    MaterialCreditNoteCreateView, 
    MaterialCreditNoteUpdateView, 
    MaterialCreditNoteDeleteView,
    MaterialCreditNoteTablePartialView
)

urlpatterns = [
    # Main dashboard view
    path('materialcreditnote/', MaterialCreditNoteListView.as_view(), name='materialcreditnote_list'),
    
    # HTMX endpoints for forms/partials
    path('materialcreditnote/table/', MaterialCreditNoteTablePartialView.as_view(), name='materialcreditnote_table'),
    path('materialcreditnote/add/', MaterialCreditNoteCreateView.as_view(), name='materialcreditnote_add'),
    path('materialcreditnote/edit/<int:pk>/', MaterialCreditNoteUpdateView.as_view(), name='materialcreditnote_edit'),
    path('materialcreditnote/delete/<int:pk>/', MaterialCreditNoteDeleteView.as_view(), name='materialcreditnote_delete'),
]

```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and integration tests for all views, including HTMX interactions.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialCreditNote
from django.utils import timezone
from datetime import timedelta

class MaterialCreditNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        MaterialCreditNote.objects.create(
            credit_note_number='MCN-001-2023',
            issue_date=timezone.now().date(),
            material_description='Test Material A for refund',
            quantity=10.50,
            amount=1500.75,
            status='Pending',
            issued_by='user123'
        )
        MaterialCreditNote.objects.create(
            credit_note_number='MCN-002-2023',
            issue_date=timezone.now().date() - timedelta(days=5),
            material_description='Defective product return',
            quantity=2.00,
            amount=500.00,
            status='Approved',
            issued_by='admin_user'
        )
  
    def test_material_credit_note_creation(self):
        obj = MaterialCreditNote.objects.get(id=1)
        self.assertEqual(obj.credit_note_number, 'MCN-001-2023')
        self.assertEqual(obj.material_description, 'Test Material A for refund')
        self.assertEqual(obj.status, 'Pending')
        
    def test_credit_note_number_label(self):
        obj = MaterialCreditNote.objects.get(id=1)
        field_label = obj._meta.get_field('credit_note_number').verbose_name
        self.assertEqual(field_label, 'Credit Note No.')
        
    def test_str_representation(self):
        obj = MaterialCreditNote.objects.get(id=1)
        self.assertEqual(str(obj), f"MCN-001-2023 ({timezone.now().date()})")

    def test_approve_credit_note(self):
        obj = MaterialCreditNote.objects.get(id=1) # Status is 'Pending'
        self.assertTrue(obj.approve_credit_note())
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Approved')

        obj_approved = MaterialCreditNote.objects.get(id=2) # Status is 'Approved'
        self.assertFalse(obj_approved.approve_credit_note()) # Cannot re-approve an approved note

    def test_cancel_credit_note(self):
        obj = MaterialCreditNote.objects.get(id=1) # Status is 'Pending'
        self.assertTrue(obj.cancel_credit_note())
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Cancelled')

        obj_approved = MaterialCreditNote.objects.get(id=2) # Status is 'Approved'
        self.assertFalse(obj_approved.cancel_credit_note()) # Cannot cancel an approved note

        obj_cancelled = MaterialCreditNote.objects.create(
            credit_note_number='MCN-003-2023',
            issue_date=timezone.now().date(),
            material_description='Already cancelled item',
            quantity=1.00,
            amount=100.00,
            status='Cancelled'
        )
        self.assertFalse(obj_cancelled.cancel_credit_note()) # Cannot re-cancel a cancelled note


class MaterialCreditNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        MaterialCreditNote.objects.create(
            credit_note_number='MCN-VIEW-001',
            issue_date=timezone.now().date(),
            material_description='Test View Material 1',
            quantity=5.00,
            amount=50.00,
            status='Pending',
            issued_by='testuser'
        )
        MaterialCreditNote.objects.create(
            credit_note_number='MCN-VIEW-002',
            issue_date=timezone.now().date() - timedelta(days=1),
            material_description='Test View Material 2',
            quantity=1.00,
            amount=10.00,
            status='Approved',
            issued_by='adminuser'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('materialcreditnote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/list.html')
        self.assertTrue('materialcreditnotes' in response.context)
        self.assertEqual(response.context['materialcreditnotes'].count(), 2)
        
    def test_table_partial_view_htmx(self):
        # Test the HTMX partial view for the table
        response = self.client.get(reverse('materialcreditnote_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_table.html')
        self.assertTrue('materialcreditnotes' in response.context)
        self.assertContains(response, 'MCN-VIEW-001') # Check if content from notes is present
        self.assertContains(response, 'materialcreditnoteTable') # Check for table ID

    def test_create_view_get_htmx(self):
        response = self.client.get(reverse('materialcreditnote_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_htmx_valid_data(self):
        data = {
            'credit_note_number': 'MCN-NEW-003',
            'issue_date': timezone.now().date(),
            'material_description': 'New item for credit',
            'quantity': 20.00,
            'amount': 2000.00,
            'status': 'Pending',
            'issued_by': 'new_user'
        }
        response = self.client.post(reverse('materialcreditnote_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response for hx-swap="none"
        self.assertTrue(MaterialCreditNote.objects.filter(credit_note_number='MCN-NEW-003').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialCreditNoteList', response.headers['HX-Trigger'])

    def test_create_view_post_htmx_invalid_data(self):
        data = { # Missing required fields, invalid quantity
            'credit_note_number': 'MCN-NEW-004',
            'quantity': 0, 
            'amount': -100,
            # 'material_description' is missing, 'issue_date' is missing
        }
        response = self.client.post(reverse('materialcreditnote_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertFalse(MaterialCreditNote.objects.filter(credit_note_number='MCN-NEW-004').exists())
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Quantity must be a positive number.')
        self.assertContains(response, 'Amount cannot be negative.')
        
    def test_update_view_get_htmx(self):
        obj = MaterialCreditNote.objects.get(credit_note_number='MCN-VIEW-001')
        response = self.client.get(reverse('materialcreditnote_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.credit_note_number, 'MCN-VIEW-001')

    def test_update_view_post_htmx_valid_data(self):
        obj = MaterialCreditNote.objects.get(credit_note_number='MCN-VIEW-001')
        data = {
            'credit_note_number': 'MCN-VIEW-001-UPDATED',
            'issue_date': obj.issue_date,
            'material_description': 'Updated description',
            'quantity': 6.00,
            'amount': 60.00,
            'status': 'Approved',
            'issued_by': 'updater'
        }
        response = self.client.post(reverse('materialcreditnote_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.credit_note_number, 'MCN-VIEW-001-UPDATED')
        self.assertEqual(obj.material_description, 'Updated description')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialCreditNoteList', response.headers['HX-Trigger'])

    def test_delete_view_get_htmx(self):
        obj = MaterialCreditNote.objects.get(credit_note_number='MCN-VIEW-001')
        response = self.client.get(reverse('materialcreditnote_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/materialcreditnote/_materialcreditnote_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].credit_note_number, 'MCN-VIEW-001')

    def test_delete_view_post_htmx(self):
        obj_to_delete = MaterialCreditNote.objects.get(credit_note_number='MCN-VIEW-001')
        response = self.client.post(reverse('materialcreditnote_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialCreditNote.objects.filter(credit_note_number='MCN-VIEW-001').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialCreditNoteList', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django code fully embraces HTMX and Alpine.js for a modern, dynamic user experience, eliminating full page reloads and enhancing interactivity.

*   **HTMX for CRUD Modals:**
    *   The "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch the form or confirmation partials into the `#modalContent` div.
    *   `hx-target="#modalContent"` and `hx-trigger="click"` ensure that clicking the button loads the content into the modal.
    *   `on click add .is-active to #modal` (via Alpine.js/h_underscore) makes the modal visible.
    *   Form submissions (`hx-post`) on the partial forms are configured with `hx-target="#modalContent"` and `hx-swap="outerHTML"` for form errors, or `hx-swap="none"` for success (relying on `HX-Trigger`).
    *   After successful form submission or deletion, the Django view returns a `204 No Content` status with an `HX-Trigger` header (`refreshMaterialCreditNoteList`). This triggers a custom event on the client-side.

*   **HTMX for DataTables Refresh:**
    *   The `materialcreditnoteTable-container` div uses `hx-get="{% url 'materialcreditnote_table' %}"` and `hx-trigger="load, refreshMaterialCreditNoteList from:body"`.
    *   This ensures the DataTable is loaded initially and automatically refreshes whenever the `refreshMaterialCreditNoteList` event is triggered (e.g., after a successful add, edit, or delete operation).
    *   `hx-swap="innerHTML"` replaces only the content of the container, keeping the rest of the page intact.

*   **Alpine.js for Modal State:**
    *   The main `list.html` template uses `_` (h_underscore syntax for `htmx.org/extensions/alpine-morph`) to manage the `is-active` class on the `#modal` element, controlling its visibility. This is a very compact way to handle UI state without writing explicit JavaScript functions.
    *   A simple Alpine.js store `Alpine.store('modal')` is outlined for more complex modal interactions, if needed.

*   **DataTables Integration:**
    *   The `_materialcreditnote_table.html` partial directly contains the `<table>` element with an ID (`materialcreditnoteTable`).
    *   A `<script>` tag within this partial initializes DataTables on `$(document).ready()`. This script runs every time the partial is loaded via HTMX, ensuring DataTables is re-initialized correctly with the new data.
    *   CDN links for DataTables CSS and JS are placed in `base.html` (implied by the `extra_css` and `extra_js` blocks).

## Final Notes

This comprehensive plan provides a clear, actionable path for modernizing the "Material Credit Note Dashboard" from ASP.NET to Django. By focusing on inferred functionalities and applying modern Django patterns with HTMX, Alpine.js, and DataTables, the solution offers:

*   **Improved User Experience:** Dynamic interactions without page reloads, fast data presentation.
*   **Maintainable Codebase:** Clear separation of concerns (Fat Model, Thin View), reusable templates.
*   **Enhanced Productivity:** Leveraging Django's powerful ORM, CBVs, and community-driven tools.
*   **Test-Driven Confidence:** Robust test coverage ensures reliability and facilitates future development.
*   **Scalability:** A clean architecture that can easily accommodate future features and increased data volume.

This approach demonstrates how AI-assisted automation can guide the conversion process, providing specific, runnable code components that adhere to best practices and enable non-technical stakeholders to understand and oversee the modernization effort.