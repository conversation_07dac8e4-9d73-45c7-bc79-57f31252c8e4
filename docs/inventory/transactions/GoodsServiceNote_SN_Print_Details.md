```markdown
## ASP.NET to Django Conversion Script: Goods Service Note Print

This plan outlines the conversion of the ASP.NET GoodsServiceNote_SN_Print_Details.aspx page to a modern Django 5.0+ application. The original ASP.NET page is primarily a **read-only report display**, not a typical CRUD (Create, Read, Update, Delete) interface for a single entity. Therefore, this modernization plan focuses on transforming the complex data aggregation logic into a robust Django model method or service, and displaying the resulting report data using Django templates, HTMX, Alpine.js, and DataTables, adhering to the "fat model, thin view" principle.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Analysis:** The ASP.NET code performs intricate queries across many database tables to compile the report data. For Django, we will define models that directly map to these existing tables using `managed = False`. This ensures Django uses the existing schema without attempting to modify it.

**Identified Tables (and assumed minimal fields relevant to this report):**

*   `tblinv_MaterialServiceNote_Master` (for GSN master data)
*   `tblinv_MaterialServiceNote_Details` (for GSN item details)
*   `tblInv_Inward_Master` (for Goods Inward Note (GIN) master data)
*   `tblInv_Inward_Details` (for GIN item details)
*   `tblMM_PO_Master` (for Purchase Order (PO) master data)
*   `tblMM_PO_Details` (for PO item details)
*   `tblMM_PR_Master` (for Purchase Requisition (PR) master data)
*   `tblMM_PR_Details` (for PR item details)
*   `tblMM_SPR_Master` (for Store Purchase Requisition (SPR) master data)
*   `tblMM_SPR_Details` (for SPR item details)
*   `tblDG_Item_Master` (for general item master data)
*   `Unit_Master` (for Unit of Measurement data)
*   `tblMM_Supplier_master` (for supplier information)

## Step 2: Identify Backend Functionality

**Analysis:** The core functionality of the ASP.NET page is to **generate and display a comprehensive report** based on multiple related inventory and procurement transactions. It acts as a data aggregator and presenter, not a direct data entry or modification interface.

**Key Functionality:**
*   **Data Aggregation:** Consolidating data from numerous tables (`MaterialServiceNote`, `Inward`, `Purchase Order`, `Purchase Requisition`, `Store Purchase Requisition`, `Item Master`, `Unit Master`, `Supplier Master`) into a single, flattened report structure.
*   **Parameter-Driven:** The report is generated dynamically based on URL query parameters (`GSNNo`, `GINNo`, `Id`, `GINId`) and session data (`CompId`, `FinYearId`).
*   **Business Logic:** Includes conditional logic (`PRSPRFlag`) to determine how item details are fetched, and calculations (e.g., `TotRecedQty`).
*   **Lookup:** Retrieving associated details like `SupplierName`, `ItemCode`, `Description`, `UOM`, and `Company Address`.

## Step 3: Infer UI Components

**Analysis:** The ASP.NET page utilizes a `CrystalReportViewer` to render a pre-defined report. It also has a "Cancel" button for navigation. There are no interactive input fields for creating or updating data.

**Django Equivalent UI Strategy:**
*   **Report Display:** The report data, once retrieved and structured by the Django backend, will be presented in a dynamic HTML table powered by **DataTables**. This allows for client-side sorting, searching, and pagination, mimicking a robust report viewer without complex third-party tools.
*   **Navigation:** The "Cancel" button will simply link back to a previous list view or dashboard.
*   **Dynamic Loading:** HTMX will be used to load the report table asynchronously if the page is refreshed or parameters change, providing a smooth user experience. Alpine.js can manage UI state, such as loading indicators.

## Step 4: Generate Django Code

**Django Application Name:** `inventory`

Given the page's focus is solely on *displaying a report*, we will not create standard `CreateView`, `UpdateView`, `DeleteView` forms and templates. Instead, we'll focus on a single `TemplateView` (or `ListView` if the data is simple) that renders the complex report. The "fat model" approach will be crucial here, with the intricate data retrieval logic encapsulated in a dedicated method within one of the primary models or a service class.

### 4.1 Models (`inventory/models.py`)

We will define minimal models for the involved database tables using `managed = False`. Crucially, a `ReportService` class will be introduced to encapsulate the complex data retrieval and aggregation logic, returning a list of dictionaries that represent the report's rows. This service class will reside in `models.py` or a dedicated `inventory/services.py` if preferred.

```python
from django.db import models
from django.db.models import Sum, F
from django.utils import timezone
from datetime import datetime

# Dummy placeholder for clsFunctions utilities
class clsFunctions:
    """
    Simulates utility functions from the original ASP.NET clsFunctions.
    In a real application, these would be proper Django ORM queries or utility functions.
    """
    def Connection(self):
        # Django manages connections; this is just a placeholder
        return "Django managed connection string"

    def FromDateDMY(self, date_str):
        # Assuming date_str is 'YYYY-MM-DD HH:MM:SS' like format
        if not date_str:
            return ""
        try:
            dt_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S.%f') # Adjust format based on actual DB
            return dt_obj.strftime('%d/%m/%Y')
        except ValueError:
            try: # Try simpler format if milliseconds not present
                dt_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
                return dt_obj.strftime('%d/%m/%Y')
            except ValueError:
                return date_str # Fallback
                
    def GetItemCode_PartNo(self, comp_id, item_id):
        try:
            item = ItemMaster.objects.using('legacy_db').get(id=item_id, compid=comp_id)
            return item.itemcode
        except ItemMaster.DoesNotExist:
            return "N/A"

    def CompAdd(self, comp_id):
        # This would typically fetch address from a CompanyMaster table
        return f"Company Address for Comp ID {comp_id}, Line 1, City, State, ZIP"

# --- Django Models for existing DB tables (managed=False) ---
# NOTE: These are minimal models with fields inferred from the C# queries.
# In a real scenario, all table columns would be mapped.

class MaterialServiceNoteMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gsnno = models.CharField(db_column='GSNNo', max_length=50)
    ginid = models.IntegerField(db_column='GINId')
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Master'
        verbose_name = 'Material Service Note Master'
        verbose_name_plural = 'Material Service Note Masters'

class MaterialServiceNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId') # FK to MaterialServiceNoteMaster
    gsnno = models.CharField(db_column='GSNNo', max_length=50) # Redundant with MId but kept for legacy
    poid = models.IntegerField(db_column='POId')
    receivedqty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'
        verbose_name = 'Material Service Note Detail'
        verbose_name_plural = 'Material Service Note Details'

class InwardMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ginno = models.CharField(db_column='GINNo', max_length=50)
    challanno = models.CharField(db_column='ChallanNo', max_length=50, null=True, blank=True)
    challandate = models.DateTimeField(db_column='ChallanDate', null=True, blank=True)
    finyearid = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Master'
        verbose_name_plural = 'Inward Masters'

class InwardDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ginid = models.IntegerField(db_column='GINId') # FK to InwardMaster
    ginno = models.CharField(db_column='GINNo', max_length=50) # Redundant with GINId but kept for legacy
    poid = models.IntegerField(db_column='POId')
    receivedqty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Inward Detail'
        verbose_name_plural = 'Inward Details'

class PoMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50)
    prsprflag = models.CharField(db_column='PRSPRFlag', max_length=1, null=True, blank=True)
    supplierid = models.CharField(db_column='SupplierId', max_length=50, null=True, blank=True)
    finyearid = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

class PoDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId') # FK to PoMaster
    pono = models.CharField(db_column='PONo', max_length=50) # Redundant with MId
    prno = models.CharField(db_column='PRNo', max_length=50, null=True, blank=True)
    prid = models.IntegerField(db_column='PRId', null=True, blank=True)
    sprno = models.CharField(db_column='SPRNo', max_length=50, null=True, blank=True)
    sprid = models.IntegerField(db_column='SPRId', null=True, blank=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

class PrMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    prno = models.CharField(db_column='PRNo', max_length=50)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

class PrDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId') # FK to PrMaster
    prno = models.CharField(db_column='PRNo', max_length=50) # Redundant with MId
    itemid = models.IntegerField(db_column='ItemId', null=True, blank=True)
    ahid = models.IntegerField(db_column='AHId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

class SprMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sprno = models.CharField(db_column='SPRNo', max_length=50)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

class SprDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId') # FK to SprMaster
    sprno = models.CharField(db_column='SPRNo', max_length=50) # Redundant with MId
    itemid = models.IntegerField(db_column='ItemId', null=True, blank=True)
    wono = models.CharField(db_column='WONo', max_length=50, null=True, blank=True)
    deptid = models.IntegerField(db_column='DeptId', null=True, blank=True)
    ahid = models.IntegerField(db_column='AHId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=50)
    manfdesc = models.CharField(db_column='ManfDesc', max_length=255, null=True, blank=True)
    uombasic = models.IntegerField(db_column='UOMBasic', null=True, blank=True) # FK to UnitMaster
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

class SupplierMaster(models.Model):
    supplierid = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    suppliername = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

class GoodsServiceNoteReportService:
    """
    A service class to encapsulate the complex data retrieval and aggregation
    logic for the Goods Service Note Print report, replacing the C# code-behind's
    data fetching and Crystal Report preparation.
    This demonstrates the "fat model" principle by moving business logic out of views.
    """
    def __init__(self):
        self.fun = clsFunctions() # Initialize our dummy utility class

    def get_report_data(self, gsn_no, gin_no, master_id, gin_master_id, comp_id, fin_year_id):
        report_data = []
        supplier_name = ""
        challan_no = ""
        challan_date = ""
        
        # 1. Fetch base MaterialServiceNote_Details
        # StrSql in original: select Id,POId,ReceivedQty from tblinv_MaterialServiceNote_Master,tblinv_MaterialServiceNote_Details
        # where Master.CompId='CompId' AND Master.GSNNo='GSNNo' AND Master.GSNNo=Details.GSNNo AND Master.Id=Details.MId AND Master.GINId='GINId'
        msn_details = MaterialServiceNoteDetail.objects.using('legacy_db').filter(
            gsnno=gsn_no,
            mid__compid=comp_id, # Access through related master via __ notation (assuming relation exists)
            mid__ginid=gin_master_id
        ).select_related('materialservicenotemaster_set', 'pomaster_set') # Assuming inverse relation on Master

        # If direct Master.GSNNo=Details.GSNNo is needed for a composite key
        # msn_details = MaterialServiceNoteDetail.objects.using('legacy_db').filter(
        #     materialservicenotemaster__compid=comp_id,
        #     materialservicenotemaster__gsnno=gsn_no,
        #     gsnno=gsn_no, # Redundant but matches SQL exactly if required
        #     materialservicenotemaster__id=F('mid'), # Link master.id to details.MId
        #     materialservicenotemaster__ginid=gin_master_id
        # ).values('id', 'poid', 'receivedqty')
        
        # Simulating the first SQL query, which iterates over MSN details linked to a specific GSN/GIN
        initial_msn_details_query = MaterialServiceNoteDetail.objects.using('legacy_db').filter(
            gsnno=gsn_no,
            mid__gsnno=gsn_no, # Assuming MSN Master also has GSNNo
            mid__compid=comp_id,
            mid__ginid=gin_master_id,
            mid=master_id # Also filtering by the Master Id passed in (original: Id = MId)
        ).values('id', 'poid', 'receivedqty', 'mid')

        for msn_detail in initial_msn_details_query:
            row_data = {
                "Id": msn_detail['id'],
                "ItemCode": "",
                "Description": "",
                "UOM": "",
                "POQty": 0.0,
                "InvQty": 0.0,
                "RecedQty": float(msn_detail['receivedqty'] or 0.0), # Use float for calculations
                "CompId": comp_id,
                "POId": msn_detail['poid'],
                "TotRecedQty": "0.000"
            }

            # 2. Fetch Inward Details (similar to StrSql2)
            # tblInv_Inward_Master.GINNo=tblInv_Inward_Details.GINNo AND Master.GINNo='GINNo' AND Master.CompId='CompId' AND Master.Id=Details.GINId AND Master.Id='GINId' AND Details.POId='POId'
            inward_details = InwardDetail.objects.using('legacy_db').filter(
                ginno=gin_no,
                ginid__ginno=gin_no, # Assuming Inward Master also has GINNo
                ginid__compid=comp_id,
                ginid=gin_master_id,
                poid=msn_detail['poid']
            ).values('ginid__challanno', 'ginid__challandate', 'receivedqty', 'poid').first()

            if inward_details:
                challan_no = inward_details['ginid__challanno'] or ""
                challan_date = self.fun.FromDateDMY(str(inward_details['ginid__challandate']))
                row_data["InvQty"] = float(inward_details['receivedqty'] or 0.0)

                # 3. Fetch PO Details (similar to StrSql3)
                # tblMM_PO_Master.PONo=tblMM_PO_Details.PONo AND Master.Id=Details.MId AND Master.CompId='CompId' AND Details.Id='POId'
                po_detail = PoDetail.objects.using('legacy_db').filter(
                    pono=inward_details['poid__pono'] if 'poid__pono' in inward_details else '', # assuming PO number can be retrieved from POID
                    mid__compid=comp_id,
                    id=msn_detail['poid'] # This is the POId from MSN details
                ).values('qty', 'prsprflag', 'supplierid', 'prno', 'prid', 'sprno', 'sprid').first()

                if po_detail:
                    supplier_id = po_detail['supplierid'] or ""
                    row_data["POQty"] = float(po_detail['qty'] or 0.0)

                    item_id = None
                    # Determine ItemId based on PRSPRFlag
                    if po_detail['prsprflag'] == "0": # PR Flag
                        pr_detail = PrDetail.objects.using('legacy_db').filter(
                            prno=po_detail['prno'],
                            id=po_detail['prid'],
                            mid__prno=po_detail['prno'], # Assuming PrMaster also has PRNo
                            mid__compid=comp_id
                        ).values('itemid').first()
                        if pr_detail:
                            item_id = pr_detail['itemid']
                    elif po_detail['prsprflag'] == "1": # SPR Flag
                        spr_detail = SprDetail.objects.using('legacy_db').filter(
                            sprno=po_detail['sprno'],
                            id=po_detail['sprid'],
                            mid__sprno=po_detail['sprno'], # Assuming SprMaster also has SPRNo
                            mid__compid=comp_id
                        ).values('itemid').first()
                        if spr_detail:
                            item_id = spr_detail['itemid']

                    if item_id:
                        # 4. Fetch Item Details (similar to StrIcode/StrIcode1)
                        item_master = ItemMaster.objects.using('legacy_db').filter(
                            id=item_id, compid=comp_id
                        ).values('itemcode', 'manfdesc', 'uombasic').first()

                        if item_master:
                            row_data["ItemCode"] = self.fun.GetItemCode_PartNo(comp_id, item_id)
                            row_data["Description"] = item_master['manfdesc'] or ""

                            # 5. Fetch UOM Symbol (similar to sqlPurch/sqlPurch1)
                            unit_master = UnitMaster.objects.using('legacy_db').filter(
                                id=item_master['uombasic']
                            ).values('symbol').first()
                            if unit_master:
                                row_data["UOM"] = unit_master['symbol'] or ""

            # 6. Calculate Total Received Quantity (similar to sqlget)
            total_reced_qty = MaterialServiceNoteDetail.objects.using('legacy_db').filter(
                mid__compid=comp_id,
                gsnno=gsn_no,
                poid=msn_detail['poid'],
                mid=F('id'), # Assumes MId in details points to master's Id
                mid__ginid=gin_master_id
            ).aggregate(sum_received_qty=Sum('receivedqty'))['sum_received_qty']

            row_data["TotRecedQty"] = f"{float(total_reced_qty or 0.0):.3f}"

            report_data.append(row_data)

        # Retrieve report header info
        if supplier_id:
            supplier_master = SupplierMaster.objects.using('legacy_db').filter(
                supplierid=supplier_id
            ).values('suppliername').first()
            if supplier_master:
                supplier_name = supplier_master['suppliername']

        company_address = self.fun.CompAdd(comp_id)

        report_header = {
            "SupplierName": supplier_name,
            "ChallanNo": challan_no,
            "ChallanDate": challan_date,
            "GSNNO": gsn_no,
            "GINNO": gin_no,
            "Address": company_address
        }

        return {"header": report_header, "details": report_data}

```

### 4.2 Forms (`inventory/forms.py`)

**Analysis:** This page is a report display and does not have any user input forms for data creation or modification. Therefore, no Django Forms are needed for this specific page.

```python
# No forms.py needed for this report display page.
# If there were search filters or input fields, a forms.Form could be defined here.
```

### 4.3 Views (`inventory/views.py`)

The view will be a simple `TemplateView` that fetches the required parameters from the request, calls the `GoodsServiceNoteReportService` to generate the report data, and passes it to the template.

```python
from django.views.generic import TemplateView, RedirectView
from django.urls import reverse_lazy
from django.http import HttpResponseBadRequest, HttpResponse
from django.shortcuts import render
from django.contrib import messages
from .models import GoodsServiceNoteReportService # Assuming this class is in models.py

class GoodsServiceNoteReportView(TemplateView):
    template_name = 'inventory/goods_service_note_print/detail.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters from query string and session
        gsn_no = self.request.GET.get('GSNNo')
        gin_no = self.request.GET.get('GINNo')
        master_id = self.request.GET.get('Id') # Refers to tblinv_MaterialServiceNote_Master.Id
        gin_master_id = self.request.GET.get('GINId') # Refers to tblInv_Inward_Master.Id
        po_no = self.request.GET.get('PONo') # Not directly used in the final report data, but part of context
        fy_id = self.request.GET.get('FyId') # Not directly used in the report, but part of context
        
        # Assuming compid and finyear are available in session as per ASP.NET code
        comp_id = self.request.session.get('compid') # e.g., 1
        fin_year_id = self.request.session.get('finyear') # e.g., 2023
        
        # Basic validation for required parameters
        if not all([gsn_no, gin_no, master_id, gin_master_id, comp_id, fin_year_id]):
            messages.error(self.request, "Missing required report parameters. Please provide GSNNo, GINNo, Id, GINId, Company ID, and Financial Year.")
            context['report_header'] = {}
            context['report_details'] = []
            return context

        try:
            report_service = GoodsServiceNoteReportService()
            report_data = report_service.get_report_data(
                gsn_no=gsn_no,
                gin_no=gin_no,
                master_id=int(master_id),
                gin_master_id=int(gin_master_id),
                comp_id=int(comp_id),
                fin_year_id=int(fin_year_id) # Though not used in get_report_data for now
            )
            context['report_header'] = report_data['header']
            context['report_details'] = report_data['details']
        except Exception as e:
            messages.error(self.request, f"Error generating report: {e}")
            context['report_header'] = {}
            context['report_details'] = []

        return context

    def get(self, request, *args, **kwargs):
        # Handle HTMX request for partial table refresh if needed
        if request.headers.get('HX-Request') and 'table_only' in request.GET:
            context = self.get_context_data(**kwargs)
            return render(request, 'inventory/goods_service_note_print/_report_table.html', context)
        return super().get(request, *args, **kwargs)

class GoodsServiceNoteCancelRedirectView(RedirectView):
    """
    Simulates the btnCancel_Click functionality by redirecting to a
    conceptual GoodsServiceNote list/print page.
    """
    # Assuming 'goods_service_note_list' is the target URL for the main GSN print/list page
    url = reverse_lazy('inventory:goods_service_note_print_list') 
    permanent = False # Not a permanent redirect

```

### 4.4 Templates

We will create two templates: one for the main page (`detail.html`) that extends the base template, and a partial template (`_report_table.html`) that contains only the DataTables structure for HTMX updates.

**Main Template (`inventory/templates/inventory/goods_service_note_print/detail.html`):**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Service Note [GSN] - Print</h2>
        <a href="{% url 'inventory:goods_service_note_cancel' %}" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-300">
            Cancel
        </a>
    </div>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-3 {% if message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-green-100 text-green-700{% endif %} rounded-md" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Report Summary</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
            <div><strong>GSN No:</strong> {{ report_header.GSNNO|default:"N/A" }}</div>
            <div><strong>GIN No:</strong> {{ report_header.GINNO|default:"N/A" }}</div>
            <div><strong>Challan No:</strong> {{ report_header.ChallanNo|default:"N/A" }}</div>
            <div><strong>Challan Date:</strong> {{ report_header.ChallanDate|default:"N/A" }}</div>
            <div class="md:col-span-2 lg:col-span-1"><strong>Supplier:</strong> {{ report_header.SupplierName|default:"N/A" }}</div>
            <div class="md:col-span-2 lg:col-span-3"><strong>Company Address:</strong> {{ report_header.Address|default:"N/A" }}</div>
        </div>
    </div>

    <div id="goods-service-note-report-container"
         hx-trigger="load, refreshReport from:body"
         hx-get="{% url 'inventory:goods_service_note_print_detail' %}?GSNNo={{ request.GET.GSNNo }}&GINNo={{ request.GET.GINNo }}&Id={{ request.GET.Id }}&GINId={{ request.GET.GINId }}&PONo={{ request.GET.PONo }}&FyId={{ request.GET.FyId }}&table_only=true"
         hx-swap="innerHTML">
        <!-- Report table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Report Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be added here for complex UI interactions
    });
</script>
{% endblock %}

```

**Partial Template (`inventory/templates/inventory/goods_service_note_print/_report_table.html`):**

```html
<div class="overflow-x-auto bg-white shadow-lg rounded-lg">
    <table id="goodsServiceNotePrintTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inv Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reced Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Reced Qty</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if report_details %}
                {% for row in report_details %}
                <tr>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.ItemCode }}</td>
                    <td class="py-3 px-4 text-sm text-gray-900">{{ row.Description }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ row.UOM }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.POQty|floatformat:3 }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.InvQty|floatformat:3 }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.RecedQty|floatformat:3 }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ row.TotRecedQty }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="8" class="py-4 px-4 text-center text-sm text-gray-500">No report data available.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#goodsServiceNotePrintTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>
```

### 4.5 URLs (`inventory/urls.py`)

Define the URL patterns for the report view and the cancel redirect.

```python
from django.urls import path
from .views import GoodsServiceNoteReportView, GoodsServiceNoteCancelRedirectView

app_name = 'inventory' # Define app namespace for URL reversing

urlpatterns = [
    # Main report detail view
    path('goods-service-note/print-details/', GoodsServiceNoteReportView.as_view(), name='goods_service_note_print_detail'),
    
    # Redirect for the cancel button
    path('goods-service-note/cancel/', GoodsServiceNoteCancelRedirectView.as_view(), name='goods_service_note_cancel'),

    # Note: If a separate URL for a list/dashboard of GSNs exists, it would be added here:
    # path('goods-service-note/', GoodsServiceNoteListView.as_view(), name='goods_service_note_print_list'),
    # For this example, we assume 'goods_service_note_print_list' is a placeholder for a different page.
]
```

### 4.6 Tests (`inventory/tests.py`)

Comprehensive tests for both the `GoodsServiceNoteReportService` (unit tests for data aggregation logic) and the `GoodsServiceNoteReportView` (integration tests for URL access, context data, and HTMX responses).

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from decimal import Decimal

# Import all models to enable mocking their managers
from .models import (
    MaterialServiceNoteMaster, MaterialServiceNoteDetail,
    InwardMaster, InwardDetail,
    PoMaster, PoDetail,
    PrMaster, PrDetail,
    SprMaster, SprDetail,
    ItemMaster, UnitMaster, SupplierMaster,
    GoodsServiceNoteReportService, clsFunctions
)

class GoodsServiceNoteReportServiceTest(TestCase):
    def setUp(self):
        self.report_service = GoodsServiceNoteReportService()
        self.mock_fun = MagicMock(spec=clsFunctions)
        self.report_service.fun = self.mock_fun

        # Mocking clsFunctions methods
        self.mock_fun.FromDateDMY.side_effect = lambda x: f"{x.split(' ')[0].replace('-', '/')}" if x else ""
        self.mock_fun.GetItemCode_PartNo.return_value = "ITEM001"
        self.mock_fun.CompAdd.return_value = "Test Company Address"

    @patch('inventory.models.MaterialServiceNoteDetail.objects')
    @patch('inventory.models.InwardDetail.objects')
    @patch('inventory.models.PoDetail.objects')
    @patch('inventory.models.PrDetail.objects')
    @patch('inventory.models.SprDetail.objects')
    @patch('inventory.models.ItemMaster.objects')
    @patch('inventory.models.UnitMaster.objects')
    @patch('inventory.models.SupplierMaster.objects')
    def test_get_report_data_success(self, mock_supplier_master_objects, mock_unit_master_objects,
                                     mock_item_master_objects, mock_spr_detail_objects,
                                     mock_pr_detail_objects, mock_po_detail_objects,
                                     mock_inward_detail_objects, mock_msn_detail_objects):
        # Setup mock data for nested queries
        mock_msn_detail_objects.using.return_value.filter.return_value.values.return_value.first.return_value = {
            'id': 1, 'poid': 101, 'receivedqty': Decimal('10.500'), 'mid': 1001
        }
        mock_msn_detail_objects.using.return_value.filter.return_value.values.return_value = [
            {'id': 1, 'poid': 101, 'receivedqty': Decimal('10.500'), 'mid': 1001}
        ] # For initial_msn_details_query
        mock_msn_detail_objects.using.return_value.filter.return_value.aggregate.return_value = {
            'sum_received_qty': Decimal('20.000')
        }

        mock_inward_detail_objects.using.return_value.filter.return_value.values.return_value.first.return_value = {
            'ginid__challanno': 'CHALLAN001', 'ginid__challandate': '2023-01-15 10:00:00.000', 'receivedqty': Decimal('12.000')
        }

        # Mock PO Detail for PR_FLAG = 0
        mock_po_detail_objects.using.return_value.filter.return_value.values.return_value.first.return_value = {
            'qty': Decimal('100.000'), 'prsprflag': '0', 'supplierid': 'SUP001', 'prno': 'PR001', 'prid': 201
        }
        mock_pr_detail_objects.using.return_value.filter.return_value.values.return_value.first.return_value = {
            'itemid': 301
        }
        mock_item_master_objects.using.return_value.filter.return_value.values.return_value.first.return_value = {
            'itemcode': 'TESTITEM', 'manfdesc': 'Test Item Description', 'uombasic': 401
        }
        mock_unit_master_objects.using.return_value.filter.return_value.values.return_value.first.return_value = {
            'symbol': 'KG'
        }
        mock_supplier_master_objects.using.return_value.filter.return_value.values.return_value.first.return_value = {
            'suppliername': 'Test Supplier Inc.'
        }

        # Call the method
        report_data = self.report_service.get_report_data(
            gsn_no="GSN001", gin_no="GIN001", master_id=1001, gin_master_id=2001, comp_id=1, fin_year_id=2023
        )

        # Assertions for header
        self.assertEqual(report_data['header']['SupplierName'], "Test Supplier Inc.")
        self.assertEqual(report_data['header']['ChallanNo'], "CHALLAN001")
        self.assertEqual(report_data['header']['ChallanDate'], "2023/01/15")
        self.assertEqual(report_data['header']['GSNNO'], "GSN001")
        self.assertEqual(report_data['header']['GINNO'], "GIN001")
        self.assertEqual(report_data['header']['Address'], "Test Company Address")

        # Assertions for details
        self.assertEqual(len(report_data['details']), 1)
        detail_row = report_data['details'][0]
        self.assertEqual(detail_row['Id'], 1)
        self.assertEqual(detail_row['ItemCode'], "ITEM001")
        self.assertEqual(detail_row['Description'], "Test Item Description")
        self.assertEqual(detail_row['UOM'], "KG")
        self.assertEqual(detail_row['POQty'], 100.0)
        self.assertEqual(detail_row['InvQty'], 12.0)
        self.assertEqual(detail_row['RecedQty'], 10.5)
        self.assertEqual(detail_row['CompId'], 1)
        self.assertEqual(detail_row['POId'], 101)
        self.assertEqual(detail_row['TotRecedQty'], "20.000")

        # Verify that ORM calls were made as expected
        mock_msn_detail_objects.using.assert_called_with('legacy_db')
        mock_inward_detail_objects.using.assert_called_with('legacy_db')
        mock_po_detail_objects.using.assert_called_with('legacy_db')
        mock_pr_detail_objects.using.assert_called_with('legacy_db')
        mock_item_master_objects.using.assert_called_with('legacy_db')
        mock_unit_master_objects.using.assert_called_with('legacy_db')
        mock_supplier_master_objects.using.assert_called_with('legacy_db')

    @patch('inventory.models.MaterialServiceNoteDetail.objects')
    def test_get_report_data_no_details(self, mock_msn_detail_objects):
        mock_msn_detail_objects.using.return_value.filter.return_value.values.return_value = []
        mock_msn_detail_objects.using.return_value.filter.return_value.aggregate.return_value = {
            'sum_received_qty': None
        }

        report_data = self.report_service.get_report_data(
            gsn_no="GSN002", gin_no="GIN002", master_id=1002, gin_master_id=2002, comp_id=1, fin_year_id=2023
        )

        self.assertEqual(len(report_data['details']), 0)
        self.assertEqual(report_data['header']['GSNNO'], "GSN002")


class GoodsServiceNoteReportViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.report_url = reverse('inventory:goods_service_note_print_detail')
        self.cancel_url = reverse('inventory:goods_service_note_cancel')
        self.default_params = {
            'GSNNo': 'TESTGSN',
            'GINNo': 'TESTGIN',
            'Id': '1',
            'GINId': '101',
            'PONo': 'TESTPO',
            'FyId': '2023'
        }
        # Simulate session data
        self.client.session['compid'] = 123
        self.client.session['finyear'] = 2023

    @patch('inventory.views.GoodsServiceNoteReportService')
    def test_report_view_success(self, MockReportService):
        mock_instance = MockReportService.return_value
        mock_instance.get_report_data.return_value = {
            'header': {
                "SupplierName": "Mock Supplier", "ChallanNo": "MOCKCHALLAN",
                "ChallanDate": "10/01/2023", "GSNNO": "TESTGSN", "GINNO": "TESTGIN",
                "Address": "Mock Company Address"
            },
            'details': [
                {'Id': 1, 'ItemCode': 'ABC', 'Description': 'Item A', 'UOM': 'PCS',
                 'POQty': 100.0, 'InvQty': 50.0, 'RecedQty': 25.0, 'CompId': 123,
                 'POId': 10, 'TotRecedQty': '75.000'}
            ]
        }

        response = self.client.get(self.report_url, self.default_params)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_service_note_print/detail.html')
        self.assertIn('report_header', response.context)
        self.assertIn('report_details', response.context)
        self.assertEqual(response.context['report_header']['GSNNO'], 'TESTGSN')
        self.assertEqual(len(response.context['report_details']), 1)
        MockReportService.assert_called_once()
        mock_instance.get_report_data.assert_called_once_with(
            gsn_no='TESTGSN', gin_no='TESTGIN', master_id=1, gin_master_id=101, comp_id=123, fin_year_id=2023
        )

    def test_report_view_missing_params(self):
        # Test with missing GSNNo
        missing_params = self.default_params.copy()
        del missing_params['GSNNo']
        response = self.client.get(self.report_url, missing_params)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Missing required report parameters.")
        self.assertIn('report_header', response.context)
        self.assertIn('report_details', response.context)
        self.assertFalse(response.context['report_header']) # Should be empty
        self.assertFalse(response.context['report_details']) # Should be empty

    @patch('inventory.views.GoodsServiceNoteReportService')
    def test_report_view_service_exception(self, MockReportService):
        mock_instance = MockReportService.return_value
        mock_instance.get_report_data.side_effect = Exception("Database connection error")

        response = self.client.get(self.report_url, self.default_params)

        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Error generating report: Database connection error")
        self.assertIn('report_header', response.context)
        self.assertIn('report_details', response.context)
        self.assertFalse(response.context['report_header'])
        self.assertFalse(response.context['report_details'])

    @patch('inventory.views.GoodsServiceNoteReportService')
    def test_report_view_htmx_partial_load(self, MockReportService):
        mock_instance = MockReportService.return_value
        mock_instance.get_report_data.return_value = {
            'header': {},
            'details': [{'Id': 1, 'ItemCode': 'ABC', 'Description': 'Item A', 'UOM': 'PCS',
                         'POQty': 100.0, 'InvQty': 50.0, 'RecedQty': 25.0, 'CompId': 123,
                         'POId': 10, 'TotRecedQty': '75.000'}]
        }

        params_with_htmx = self.default_params.copy()
        params_with_htmx['table_only'] = 'true'
        response = self.client.get(self.report_url, params_with_htmx, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goods_service_note_print/_report_table.html')
        self.assertContains(response, 'id="goodsServiceNotePrintTable"')
        self.assertNotContains(response, '<h2 class="text-2xl font-bold">') # Should not contain main page elements
        self.assertIn('report_details', response.context)
        self.assertEqual(len(response.context['report_details']), 1)

    def test_cancel_redirect_view(self):
        response = self.client.get(self.cancel_url)
        self.assertEqual(response.status_code, 302)
        # Assuming goods_service_note_print_list is the target URL. Update this if a different path is intended.
        self.assertRedirects(response, reverse('inventory:goods_service_note_print_list'))

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX for dynamic table loading:** The main `detail.html` template uses `hx-get` on the `goods-service-note-report-container` div to load the `_report_table.html` partial. This is triggered on `load` and `refreshReport` (a custom event that could be triggered from other parts of the application if the report needs to be updated).
*   **DataTables for interactive display:** The `_report_table.html` partial includes a `<script>` tag that initializes DataTables on the loaded table. This provides out-of-the-box searching, sorting, and pagination.
*   **Alpine.js (optional for this specific page):** While Alpine.js is not strictly necessary for this report view, its integration is shown in `base.html` (implied) and `detail.html`. If more complex UI state (e.g., dynamic filtering forms, toggles) were added to the report page, Alpine.js would be used for client-side interactivity without writing much JavaScript.
*   **No full page reloads:** All report data updates (e.g., if filter changes were implemented) would be handled via HTMX, swapping only the necessary table content.
*   **No custom JavaScript requirements:** The approach relies on HTMX attributes and Alpine.js directives, minimizing manual JavaScript coding.

## Final Notes

*   **Placeholders:** Replace database connection details, exact column names, and specific URLs (`goods_service_note_print_list`) with actual values from your project's configuration.
*   **Database Configuration:** Ensure your Django `settings.py` is configured to connect to the legacy ASP.NET database using the `legacy_db` alias (or whatever name you choose) and that `managed=False` is respected.
*   **Fat Model, Thin View:** The complex data fetching and business logic previously in the ASP.NET code-behind is now encapsulated within the `GoodsServiceNoteReportService` class, adhering to the "fat model" principle. The view remains minimal, primarily handling request parameters and rendering the appropriate template.
*   **Test Coverage:** The provided tests demonstrate a strong commitment to 80% test coverage, including mocking external dependencies to ensure unit tests are isolated and efficient.
*   **Modularity:** This plan focuses specifically on the "Goods Service Note Print" functionality. Other modules and their respective conversions would follow a similar pattern.
```