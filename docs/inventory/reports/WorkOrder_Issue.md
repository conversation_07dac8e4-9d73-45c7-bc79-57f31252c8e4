## ASP.NET to Django Conversion Script: Work Order Issue & Shortage Report

This document outlines the strategic transition of your ASP.NET Work Order Issue and Shortage Report page to a modern Django-based solution. Our approach prioritizes automated conversion, reusability, and maintainability, ensuring a seamless upgrade to a robust and scalable architecture.

### Business Value of Django Modernization:

*   **Enhanced Performance:** Django's optimized ORM and efficient request handling lead to faster page loads and improved user experience, even with complex data queries.
*   **Reduced Development Costs:** Leveraging Django's built-in features, coupled with HTMX and Alpine.js, significantly reduces the need for extensive front-end JavaScript, simplifying development and maintenance.
*   **Improved Scalability:** Django's architecture is designed for scalability, allowing your application to handle increased user loads and data volumes as your business grows.
*   **Future-Proof Technology:** Moving to a widely adopted, open-source framework like Django ensures access to a vibrant community, continuous updates, and a wealth of readily available talent.
*   **Simplified Maintenance:** The "Fat Model, Thin View" paradigm centralizes business logic, making code easier to understand, debug, and update, reducing long-term maintenance overhead.
*   **Modern User Experience:** Integration with HTMX and Alpine.js provides dynamic, interactive interfaces without the complexity of traditional SPA frameworks, leading to a snappier feel for users.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include base.html template code in your output - assume it already exists
*   Focus ONLY on component-specific code for the current module
*   Always include complete unit tests for models and integration tests for views
*   Use modern Django 5.0+ patterns and follow best practices
*   Keep your code clean, efficient, and avoid redundancy
*   Always generate complete, runnable Django code

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
*   Map models to existing database using `managed = False` and `db_table`
*   Implement DataTables for client-side searching, sorting, and pagination
*   Use HTMX for dynamic interactions and Alpine.js for UI state management
*   All templates should extend `core/base.html` (but DO NOT include base.html code)
*   Achieve at least 80% test coverage with unit and integration tests
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase
*   Use Tailwind CSS for styling components

---

## Conversion Steps:

For this modernization plan, we will create a new Django application named `inventory_reports`.

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code primarily retrieves data for a work order report using a stored procedure `Sp_WONO_NotInBom`. This procedure seems to filter and join data from several tables. Based on the `GridView` columns and the C# logic, we can infer the primary data source tables and their relevant columns.

*   **Main Data Source (Inferred):** `SD_Cust_WorkOrder_Master` (represents the core work order data).
    *   **Columns:** `WONo` (primary key for Work Order), `FinYear`, `CustomerName`, `CustomerId`, `EnqId`, `PONo`, `SysDate`, `EmployeeName`.
*   **Related Table 1:** `tblSD_WO_Category` (for Work Order categories).
    *   **Columns:** `CId` (category ID), `Symbol`, `CName` (category name), `CompId`.
*   **Related Table 2:** `SD_Cust_master` (for customer details, especially for the auto-complete feature).
    *   **Columns:** `CustomerId`, `CustomerName`, `CompId`.
*   **Related Table 3:** `tblDG_BOM_Master` (used for filtering which Work Orders to display).
    *   **Columns:** `WONo` (likely a foreign key to `SD_Cust_WorkOrder_Master`).
*   **Session Variables (for filtering context):** `username`, `finyear` (FinYearId), `compid` (CompId). These will be passed to model methods or views in Django.

## Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Analysis:**
The ASP.NET page is a "Work Order Issue & Shortage" report, primarily focusing on displaying filtered data and redirecting to detail pages.

*   **Read (Filter & List):** This is the core functionality.
    *   Dynamically filters a list of work orders based on user selections:
        *   Search criteria (`DropDownList1`): Customer Name, Enquiry No, PO No, WO No.
        *   Search value (`txtEnqId`, `TxtSearchValue`).
        *   Work Order Category (`DDLTaskWOType`).
    *   The filtering logic heavily relies on the `BindDataCust` method which uses the `Sp_WONO_NotInBom` stored procedure. This procedure's logic (filtering by company, financial year, and `WONo` presence in `tblDG_BOM_Master`) must be replicated in Django.
    *   Pagination and sorting are handled by the `GridView`, which will be replaced by DataTables in Django.
*   **Auto-complete:** The `TxtSearchValue_AutoCompleteExtender` provides suggestions for customer names from `SD_Cust_master`.
*   **Redirection:** The "Select" `LinkButton` in the `GridView` redirects the user to either `WorkOrder_Issue_Details.aspx` or `WorkOrder_Shortage_Details.aspx`, passing the `WONo` and a generated `Key`.

**No direct Create, Update, or Delete (CRUD) operations are performed on *this* page.** The page serves as a search and navigation hub.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The page's UI consists of a search/filter section and a data grid.

*   **Search/Filter Section:**
    *   **Search By Dropdown (`DropDownList1`):** A dropdown to select the type of search (e.g., Customer Name, Enquiry No, PO No, WO No).
    *   **Search Value Textboxes (`txtEnqId`, `TxtSearchValue`):** Text inputs that appear/disappear based on the "Search By" selection. `TxtSearchValue` includes an auto-complete feature for customer names.
    *   **Work Order Category Dropdown (`DDLTaskWOType`):** A dropdown to filter by work order category.
    *   **Search Button (`btnSearch`):** Triggers the filtering.
*   **Data Grid (`SearchGridView1`):**
    *   Displays work order details in a tabular format.
    *   Includes columns for `SN` (serial number), `FinYear`, `Customer Name`, `Code` (CustomerId), `Enquiry No`, `WO No`, `PO No`, `Date` (SysDate), `Gen. By` (EmployeeName).
    *   **Action Column:** Contains a "Select" `LinkButton` and a "Work Order Type" `DropDownList` (Issue/Shortage) which together determine the redirect target. This "Work Order Type" dropdown is *per row* and its value is only read when "Select" is clicked, it's not a filter for the entire grid.

## Step 4: Generate Django Code

We will create a Django application named `inventory_reports`.

### 4.1 Models (`inventory_reports/models.py`)

We'll define models for the primary tables identified, ensuring `managed = False` as per the guidelines. The complex filtering logic from `BindDataCust` and `Sp_WONO_NotInBom` will be encapsulated in a custom manager on the `WorkOrder` model.

```python
from django.db import models
from django.db.models import Q

class WorkOrderCategory(models.Model):
    """
    Represents the tblSD_WO_Category table.
    Used for filtering Work Orders by category.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    cname = models.CharField(db_column='CName', max_length=255)
    # Assuming CompId is a column in tblSD_WO_Category based on C# code
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'Work Order Category'
        verbose_name_plural = 'Work Order Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class Customer(models.Model):
    """
    Represents the SD_Cust_master table.
    Used for customer lookup and auto-completion.
    """
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    # Assuming CompId is a column in SD_Cust_master based on C# code
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class BOMMaster(models.Model):
    """
    Represents the tblDG_BOM_Master table.
    Used to filter Work Orders that have a BOM entry.
    """
    # Assuming a simple primary key and a foreign key to WorkOrder's WONo
    id = models.AutoField(db_column='ID', primary_key=True) # Example PK, adjust as per actual DB
    # Assuming WONo is the column linking to WorkOrder.won_no
    won_no_ref = models.CharField(db_column='WONo', max_length=50) # Use CharField if WONo in BOM is not a direct FK object

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master Entry'
        verbose_name_plural = 'BOM Master Entries'

    def __str__(self):
        return f"BOM for WO: {self.won_no_ref}"


class WorkOrderManager(models.Manager):
    """
    Custom manager for WorkOrder model to encapsulate complex report filtering logic.
    This mimics the functionality of the ASP.NET BindDataCust method and Sp_WONO_NotInBom stored procedure.
    """
    def get_work_orders_report(self, comp_id, fin_year_id, search_type, search_value, wo_category_id):
        # Start with base queryset, filtered by company and financial year if applicable
        qs = self.get_queryset().filter(comp_id=comp_id, fin_year_id=fin_year_id)

        # Apply Work Order Category filter (mimics ASP.NET's 'Z' variable)
        if wo_category_id and wo_category_id != 'WO Category':
            qs = qs.filter(category__cid=wo_category_id)

        # Apply specific search filters (mimics ASP.NET's 'x' and 'y' variables)
        if search_value:
            if search_type == '1':  # Enquiry No
                qs = qs.filter(enquiry_no__iexact=search_value)
            elif search_type == '2':  # PO No
                qs = qs.filter(po_no__iexact=search_value)
            elif search_type == '3':  # WO No
                qs = qs.filter(wo_no__iexact=search_value)
            elif search_type == '0':  # Customer Name
                # In ASP.NET, fun.getCode() is used to get CustomerId from CustomerName.
                # Here, we assume the search_value for customer name might be "Name [ID]".
                # If only name is passed, we attempt a lookup.
                customer_id = None
                if '[' in search_value and search_value.endswith(']'):
                    try:
                        customer_id = int(search_value.split('[')[-1][:-1])
                    except ValueError:
                        pass
                
                if customer_id is not None:
                    qs = qs.filter(customer_id=customer_id)
                else:
                    # Fallback for direct name search if ID is not provided
                    customer_obj = Customer.objects.filter(customer_name__iexact=search_value, comp_id=comp_id).first()
                    if customer_obj:
                        qs = qs.filter(customer_id=customer_obj.customer_id)
                    else: # If no customer found, return empty queryset
                        return self.get_queryset().none()

        # Apply the 'WONo in (select WONo from tblDG_BOM_Master)' filter (mimics ASP.NET's 'L' variable)
        # This implies a WorkOrder must have a corresponding entry in tblDG_BOM_Master.
        # We achieve this using an `exists` subquery or by joining on the related `won_no_ref`.
        # For simplicity, we assume `won_no_ref` in BOMMaster is the WONo from WorkOrder.
        # If the relationship is a ForeignKey, we could use `filter(bommaster__isnull=False)`.
        # Since `WONo` is likely a CharField in BOMMaster, we'll do a join or subquery.
        # Example using subquery (assuming WONo is charfield in BOMMaster):
        from django.db.models import Exists, OuterRef
        qs = qs.annotate(
            has_bom=Exists(
                BOMMaster.objects.filter(won_no_ref=OuterRef('wo_no'))
            )
        ).filter(has_bom=True)

        return qs.order_by('wo_no') # Default sorting

class WorkOrder(models.Model):
    """
    Represents the SD_Cust_WorkOrder_Master table and its report-relevant fields.
    """
    # Fields mapped directly from GridView columns and implied database columns.
    # Adjust db_column names if they differ in your actual database schema.
    wo_no = models.CharField(db_column='WONo', max_length=50, primary_key=True) # Primary Key
    fin_year = models.IntegerField(db_column='FinYear', null=True, blank=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, null=True, blank=True)
    customer_id = models.IntegerField(db_column='CustomerId', null=True, blank=True)
    enquiry_no = models.CharField(db_column='EnqId', max_length=50, null=True, blank=True)
    po_no = models.CharField(db_column='PONo', max_length=50, null=True, blank=True)
    sys_date = models.DateTimeField(db_column='SysDate', null=True, blank=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, null=True, blank=True)
    
    # Foreign key to WorkOrderCategory
    category = models.ForeignKey(WorkOrderCategory, models.DO_NOTHING, db_column='CId', null=True, blank=True)

    # Placeholder for company/financial year IDs, as they are used in filtering.
    # Assume these are columns in SD_Cust_WorkOrder_Master
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)

    objects = WorkOrderManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master' # The actual table name
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.wo_no} ({self.customer_name})"

    # Additional methods for business logic can be added here
    # Example: A method to determine if it's an 'issue' or 'shortage' type,
    # though this was a UI dropdown in ASP.NET, not stored in the WO itself.
```

### 4.2 Forms (`inventory_reports/forms.py`)

We'll define a `FilterForm` for the search criteria section. Since no direct CRUD is involved on this page, a standard `ModelForm` is not needed for the `WorkOrder` itself.

```python
from django import forms
from .models import WorkOrderCategory, Customer

class WorkOrderFilterForm(forms.Form):
    """
    Form for filtering Work Order report data.
    Corresponds to DropDownList1, txtEnqId, TxtSearchValue, DDLTaskWOType in ASP.NET.
    """
    SEARCH_CHOICES = [
        ('Select', 'Select'), # Default
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="", # Label managed by template for flexibility
        widget=forms.Select(attrs={
            'class': 'box3 min-w-[180px] bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': "{% url 'inventory_reports:work_order_issue_table' %}",
            'hx-target': '#work-order-table-container',
            'hx-indicator': '#table-loading-indicator',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change, keyup changed delay:500ms from:#id_search_value', # Trigger reload on change or search value change
            'name': 'search_by' # Explicitly set name to match query param
        })
    )

    search_value = forms.CharField(
        required=False,
        label="", # Label managed by template
        widget=forms.TextInput(attrs={
            'class': 'box3 min-w-[350px] bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter search value',
            'id': 'id_search_value', # For Alpine.js/HTMX visibility toggle
            'hx-get': "{% url 'inventory_reports:work_order_issue_table' %}",
            'hx-target': '#work-order-table-container',
            'hx-indicator': '#table-loading-indicator',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'keyup changed delay:500ms', # Live search after delay
            'name': 'search_value' # Explicitly set name
        })
    )
    
    # AutoComplete Extender equivalent for Customer Name
    customer_autocomplete_value = forms.CharField(
        required=False,
        label="",
        widget=forms.TextInput(attrs={
            'class': 'box3 min-w-[350px] bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Search Customer Name',
            'id': 'id_customer_autocomplete_value', # For Alpine.js/HTMX visibility toggle
            # HTMX attributes for autocomplete
            'hx-get': "{% url 'inventory_reports:customer_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#customer-suggestions',
            'hx-swap': 'innerHTML',
            'name': 'customer_autocomplete_value', # Explicitly set name
            '@input': 'clearTimeout(typingTimer); typingTimer = setTimeout(() => { $dispatch(\'filter-changed\'); }, 500);' # Alpine.js event
        })
    )

    wo_category = forms.ModelChoiceField(
        queryset=WorkOrderCategory.objects.none(), # Will be set in view
        required=False,
        empty_label="WO Category",
        label="",
        widget=forms.Select(attrs={
            'class': 'box3 min-w-[180px] bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'hx-get': "{% url 'inventory_reports:work_order_issue_table' %}",
            'hx-target': '#work-order-table-container',
            'hx-indicator': '#table-loading-indicator',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
            'name': 'wo_category' # Explicitly set name
        })
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        if comp_id:
            # Filter WO categories by comp_id for the dropdown
            self.fields['wo_category'].queryset = WorkOrderCategory.objects.filter(comp_id=comp_id).order_by('cname')
        
        # Initial visibility settings for search fields, mimicking ASP.NET
        self.fields['search_value'].widget.attrs['x-show'] = 'searchBy == "1" || searchBy == "2" || searchBy == "3" || searchBy == "Select"'
        self.fields['customer_autocomplete_value'].widget.attrs['x-show'] = 'searchBy == "0"'
        
        # Pass current search_value to customer_autocomplete_value
        if 'search_value' in self.initial and self.initial['search_by'] == '0':
            self.fields['customer_autocomplete_value'].initial = self.initial['search_value']
            self.fields['search_value'].initial = '' # Clear the actual search_value field if customer autocomplete is active
        elif 'search_value' in self.initial:
            self.fields['search_value'].initial = self.initial['search_value']
            self.fields['customer_autocomplete_value'].initial = ''

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        search_value = cleaned_data.get('search_value')
        customer_autocomplete_value = cleaned_data.get('customer_autocomplete_value')

        # Adjust the search_value based on the search_by field
        if search_by == '0': # Customer Name
            cleaned_data['search_value'] = customer_autocomplete_value
        else:
            cleaned_data['search_value'] = search_value
            cleaned_data['customer_autocomplete_value'] = '' # Clear if not customer search

        return cleaned_data
```

### 4.3 Views (`inventory_reports/views.py`)

We'll implement a `ListView` for the main page and a partial view for the DataTables content, driven by HTMX. We'll also need a view for the customer auto-completion.

```python
from django.views.generic import ListView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.shortcuts import render
from django.conf import settings
import uuid # For generating random keys

from .models import WorkOrder, Customer, WorkOrderCategory
from .forms import WorkOrderFilterForm

class WorkOrderIssueListView(ListView):
    """
    Main view for the Work Order Issue & Shortage report.
    Handles the search form and displays the initial table structure.
    Corresponds to the WorkOrder_Issue.aspx page.
    """
    model = WorkOrder
    template_name = 'inventory_reports/work_order_issue/list.html'
    context_object_name = 'work_orders'
    paginate_by = 15 # ASP.NET GridView had PageSize="15"

    def get_queryset(self):
        # Initial load or if no filters are applied, return empty or a default set.
        # Data will be loaded via HTMX into _work_order_issue_table.html
        return WorkOrder.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Session variables from ASP.NET
        # In Django, this would come from the authenticated user or another session management.
        # For this example, we'll use placeholder or assume they are in self.request.session
        # Replace with actual session/user attribute retrieval in a real app.
        comp_id = self.request.session.get('compid', settings.DEFAULT_COMPANY_ID) # Use default from settings
        fin_year_id = self.request.session.get('finyear', settings.DEFAULT_FIN_YEAR_ID) # Use default from settings

        # Populate the filter form with current request GET parameters
        # And ensure it's initialized with relevant queryset for categories
        form = WorkOrderFilterForm(self.request.GET, comp_id=comp_id)
        
        # Manually set initial 'search_by' for Alpine.js if form not valid or not submitted
        if not form.is_valid() and 'search_by' not in self.request.GET:
             form.fields['search_by'].initial = 'Select' # Default 'Select' for DropDownList1

        context['filter_form'] = form
        
        # Provide WO Type choices for the dropdown in the table rows
        context['wo_type_choices'] = [
            {'value': '0', 'text': 'Issue'},
            {'value': '1', 'text': 'Shortage'}
        ]

        return context

class WorkOrderIssueTablePartialView(ListView):
    """
    Renders only the work order table content, designed for HTMX requests.
    This replaces the ASP.NET GridView data binding logic.
    """
    model = WorkOrder
    template_name = 'inventory_reports/work_order_issue/_work_order_issue_table.html'
    context_object_name = 'work_orders'
    paginate_by = 15 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Get session variables (mimicking ASP.NET session)
        comp_id = self.request.session.get('compid', settings.DEFAULT_COMPANY_ID)
        fin_year_id = self.request.session.get('finyear', settings.DEFAULT_FIN_YEAR_ID)

        # Get filter parameters from GET request
        search_by = self.request.GET.get('search_by', 'Select')
        search_value = self.request.GET.get('search_value', '')
        customer_autocomplete_value = self.request.GET.get('customer_autocomplete_value', '')
        wo_category_id = self.request.GET.get('wo_category', 'WO Category')

        # Determine the actual search value based on search_by
        if search_by == '0': # Customer Name
            actual_search_value = customer_autocomplete_value
        else:
            actual_search_value = search_value
        
        # Use the custom manager method to fetch filtered data
        queryset = WorkOrder.objects.get_work_orders_report(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_type=search_by,
            search_value=actual_search_value,
            wo_category_id=wo_category_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure WO Type choices are available in the partial template too
        context['wo_type_choices'] = [
            {'value': '0', 'text': 'Issue'},
            {'value': '1', 'text': 'Shortage'}
        ]
        return context

class CustomerAutocompleteView(View):
    """
    Provides customer name suggestions for the auto-complete feature via HTMX.
    Mimics the ASP.NET WebMethod 'sql'.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('customer_autocomplete_value', '')
        comp_id = request.session.get('compid', settings.DEFAULT_COMPANY_ID)

        if len(prefix_text) < 1: # Minimum prefix length from ASP.NET AutoCompleteExtender
            return HttpResponse("")

        # Filter customers by prefix_text and company ID
        customers = Customer.objects.filter(
            customer_name__icontains=prefix_text, # Using icontains for broader match
            comp_id=comp_id
        ).order_by('customer_name')[:10] # Limit suggestions to top 10

        suggestions = []
        for customer in customers:
            suggestions.append(f"{customer.customer_name} [{customer.customer_id}]")
        
        # Render suggestions as a simple list for HTMX.
        # This can be a separate partial template for more complex display.
        html_suggestions = '<div id="customer-suggestions" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto mt-1">'
        for suggestion in suggestions:
            # Clicking a suggestion populates the input and triggers a search.
            html_suggestions += f"""
                <div class="px-3 py-2 cursor-pointer hover:bg-gray-100"
                     hx-on:click="document.getElementById('id_customer_autocomplete_value').value='{suggestion.replace("'", "&#39;") | safe}'; document.getElementById('work-order-filter-form').dispatchEvent(new Event('submit'));"
                >
                    {suggestion}
                </div>
            """
        html_suggestions += '</div>' if suggestions else '<div id="customer-suggestions" class="hidden"></div>'

        return HttpResponse(html_suggestions)

# Helper function for generating random keys (like ASP.NET fun.GetRandomAlphaNumeric())
def generate_random_key():
    return uuid.uuid4().hex[:10] # Generates a 10-char hex string


# The actual redirect views (WorkOrder_Issue_Details.aspx / WorkOrder_Shortage_Details.aspx)
# would be separate Django views, potentially in another app (e.g., 'work_order_details').
# For this example, we'll just demonstrate the URL construction.
class WorkOrderIssueDetailRedirectView(View):
    """
    Handles the redirection logic from the 'Select' button in the GridView.
    Mimics the ASP.NET SearchGridView1_RowCommand.
    """
    def get(self, request, *args, **kwargs):
        wo_no = kwargs.get('wo_no')
        wo_type = request.GET.get('wo_type', '0') # Default to 'Issue'

        random_key = generate_random_key()

        if wo_type == '0': # Issue
            # This would be a URL for a Work Order Issue Details page
            return HttpResponse(status=204, headers={'HX-Redirect': reverse_lazy('work_order_details:issue_detail', kwargs={'wo_no': wo_no}) + f'?Key={random_key}'})
        else: # Shortage
            # This would be a URL for a Work Order Shortage Details page
            return HttpResponse(status=204, headers={'HX-Redirect': reverse_lazy('work_order_details:shortage_detail', kwargs={'wo_no': wo_no}) + f'?Key={random_key}'})

# Placeholder for actual detail views (they would exist in 'work_order_details' app)
# You would define these in a real application:
# from django.views.generic import DetailView
# class WorkOrderIssueDetailView(DetailView):
#     model = WorkOrder
#     template_name = 'work_order_details/issue_detail.html'
#     slug_field = 'wo_no'
#     slug_url_kwarg = 'wo_no'
#
# class WorkOrderShortageDetailView(DetailView):
#     model = WorkOrder
#     template_name = 'work_order_details/shortage_detail.html'
#     slug_field = 'wo_no'
#     slug_url_kwarg = 'wo_no'

```

### 4.4 Templates

We'll create the main list template and partial templates for the filter form and the table content.

**`inventory_reports/templates/inventory_reports/work_order_issue/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Work Order Issue & Shortage{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 border-b pb-2">Work Order Issue & Shortage Report</h2>
        
        <div x-data="{ searchBy: '{{ filter_form.search_by.value }}' }" id="work-order-filter-container">
            <form id="work-order-filter-form" hx-get="{% url 'inventory_reports:work_order_issue_table' %}" 
                  hx-target="#work-order-table-container" hx-swap="innerHTML" hx-indicator="#table-loading-indicator">
                {% csrf_token %}
                <div class="flex flex-wrap items-center gap-4 mb-4">
                    <div class="flex-shrink-0">
                        <label for="{{ filter_form.search_by.id_for_label }}" class="sr-only">Search By</label>
                        <select 
                            id="{{ filter_form.search_by.id_for_label }}" 
                            name="{{ filter_form.search_by.name }}" 
                            class="{{ filter_form.search_by.field.widget.attrs.class }}"
                            x-model="searchBy"
                            hx-trigger="change"
                            hx-get="{% url 'inventory_reports:work_order_issue_table' %}"
                            hx-target="#work-order-table-container"
                            hx-swap="innerHTML"
                            hx-indicator="#table-loading-indicator"
                        >
                            {% for value, label in filter_form.search_by.field.choices %}
                                <option value="{{ value }}" {% if value == filter_form.search_by.value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="relative flex-grow min-w-[150px] max-w-sm">
                        {# Conditional rendering based on searchBy selection #}
                        {# txtEnqId equivalent (for Enquiry No, PO No, WO No) #}
                        <input 
                            type="text" 
                            id="{{ filter_form.search_value.id_for_label }}" 
                            name="{{ filter_form.search_value.name }}" 
                            placeholder="{{ filter_form.search_value.field.widget.attrs.placeholder }}" 
                            class="{{ filter_form.search_value.field.widget.attrs.class }}"
                            value="{{ filter_form.search_value.value }}"
                            x-show="searchBy == '1' || searchBy == '2' || searchBy == '3' || searchBy == 'Select'"
                            hx-get="{% url 'inventory_reports:work_order_issue_table' %}"
                            hx-target="#work-order-table-container"
                            hx-swap="innerHTML"
                            hx-indicator="#table-loading-indicator"
                            hx-trigger="keyup changed delay:500ms from:#{{ filter_form.search_value.id_for_label }}"
                        >
                        {# TxtSearchValue equivalent (for Customer Name with autocomplete) #}
                        <input 
                            type="text" 
                            id="{{ filter_form.customer_autocomplete_value.id_for_label }}" 
                            name="{{ filter_form.customer_autocomplete_value.name }}" 
                            placeholder="{{ filter_form.customer_autocomplete_value.field.widget.attrs.placeholder }}" 
                            class="{{ filter_form.customer_autocomplete_value.field.widget.attrs.class }}"
                            value="{{ filter_form.customer_autocomplete_value.value }}"
                            x-show="searchBy == '0'"
                            hx-get="{% url 'inventory_reports:customer_autocomplete' %}"
                            hx-target="#customer-suggestions-container"
                            hx-swap="outerHTML" {# Replace the container itself #}
                            hx-trigger="keyup changed delay:300ms from:#{{ filter_form.customer_autocomplete_value.id_for_label }}"
                        >
                        {# Autocomplete suggestions container #}
                        <div id="customer-suggestions-container">
                            {# Suggestions will be loaded here by hx-target #}
                        </div>
                    </div>

                    <div class="flex-shrink-0">
                        <label for="{{ filter_form.wo_category.id_for_label }}" class="sr-only">WO Category</label>
                        <select 
                            id="{{ filter_form.wo_category.id_for_label }}" 
                            name="{{ filter_form.wo_category.name }}" 
                            class="{{ filter_form.wo_category.field.widget.attrs.class }}"
                            hx-get="{% url 'inventory_reports:work_order_issue_table' %}"
                            hx-target="#work-order-table-container"
                            hx-swap="innerHTML"
                            hx-indicator="#table-loading-indicator"
                            hx-trigger="change"
                        >
                            {{ filter_form.wo_category.as_widget }}
                        </select>
                    </div>

                    <button 
                        type="submit" 
                        class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        hx-indicator="#table-loading-indicator"
                    >
                        Search
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <div id="work-order-table-container" class="bg-white shadow-md rounded-lg p-6 overflow-x-auto min-h-[300px]">
        <!-- Initial loading indicator -->
        <div id="table-loading-indicator" class="htmx-indicator text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
        <!-- DataTables content will be loaded here via HTMX -->
        <!-- hx-get will be triggered on initial load via hx-trigger="load" on an outer div -->
        {# Initial load via HTMX from the main view (or load from another div) #}
        {# For simplicity, the first load will happen on the hx-get of the form #}
        {# or you can add a div with hx-get and hx-trigger="load" for initial load. #}
        {# For now, assume the form submission or filter change triggers the load #}
        {# Or, render the table directly on first page load #}
        {% include 'inventory_reports/work_order_issue/_work_order_issue_table.html' %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.0/js/dataTables.min.js"></script>
<script>
    // Alpine.js data for dynamic input visibility
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderFilter', () => ({
            searchBy: '{{ filter_form.search_by.value|default:"Select" }}', // Initial value from Django form
            init() {
                // Ensure initial visibility is set correctly based on the form's initial value
                this.$watch('searchBy', value => {
                    // When searchBy changes, clear the other search value field
                    if (value !== '0') {
                        document.getElementById('id_customer_autocomplete_value').value = '';
                    } else {
                        document.getElementById('id_search_value').value = '';
                    }
                });
            }
        }));
    });

    // DataTables initialization
    document.addEventListener('DOMContentLoaded', function() {
        // HTMX will replace the table content, so we need to re-initialize DataTables
        // after each HTMX swap. This can be done using htmx.on or via Alpine.js event.
        // A common pattern is to listen for htmx:afterSwap on the target element.
        htmx.on('#work-order-table-container', 'htmx:afterSwap', function(evt) {
            // Check if the swapped content actually contains the table
            if (evt.detail.target.querySelector('#workOrderIssueTable')) {
                $('#workOrderIssueTable').DataTable({
                    "pageLength": 15, // Matching ASP.NET PageSize
                    "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
                    "destroy": true, // Destroy existing instance before re-initialization
                    "pagingType": "full_numbers"
                });
            }
        });

        // Initialize DataTable on first load if it's rendered directly
        if (document.getElementById('workOrderIssueTable')) {
            $('#workOrderIssueTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
                "pagingType": "full_numbers"
            });
        }
    });

    // To trigger the form submission when a customer autocomplete suggestion is clicked
    // The hx-on:click on the suggestion div will handle setting the value and dispatching submit event.
</script>
{% endblock %}
```

**`inventory_reports/templates/inventory_reports/work_order_issue/_work_order_issue_table.html`**

```html
<table id="workOrderIssueTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WO Type</th>
            <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th scope="col" class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if work_orders %}
            {% for wo in work_orders %}
            <tr x-data="{ woType: '0' }"> {# Alpine.js to manage per-row WO Type selection #}
                <td class="py-2 px-4 text-right whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap text-sm text-gray-900">
                    {# LinkButton "Select" equivalent #}
                    <button 
                        class="text-blue-600 hover:text-blue-900 font-medium"
                        hx-get="{% url 'inventory_reports:work_order_select_redirect' wo_no=wo.wo_no %}?wo_type={{ woType }}" {# Pass selected WO Type #}
                        hx-swap="none"
                        hx-trigger="click"
                    >
                        Select
                    </button>
                </td>
                <td class="py-2 px-4 text-center whitespace-nowrap text-sm text-gray-900">
                    {# DrpWorkOrderType dropdown equivalent #}
                    <select x-model="woType" class="box3 bg-white border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                        {% for choice in wo_type_choices %}
                            <option value="{{ choice.value }}">{{ choice.text }}</option>
                        {% endfor %}
                    </select>
                </td>
                <td class="py-2 px-4 text-center whitespace-nowrap text-sm text-gray-900">{{ wo.fin_year|default_if_none:"" }}</td>
                <td class="py-2 px-4 text-left whitespace-nowrap text-sm text-gray-900">{{ wo.customer_name|default_if_none:"" }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap text-sm text-gray-900">{{ wo.customer_id|default_if_none:"" }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap text-sm text-gray-900">{{ wo.enquiry_no|default_if_none:"" }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap text-sm text-gray-900">{{ wo.wo_no|default_if_none:"" }}</td>
                <td class="py-2 px-4 text-left whitespace-nowrap text-sm text-gray-900">{{ wo.po_no|default_if_none:"" }}</td>
                <td class="py-2 px-4 text-center whitespace-nowrap text-sm text-gray-900">{{ wo.sys_date|date:"Y-m-d"|default_if_none:"" }}</td> {# Format date #}
                <td class="py-2 px-4 text-left whitespace-nowrap text-sm text-gray-900">{{ wo.employee_name|default_if_none:"" }}</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="11" class="py-4 px-4 text-center text-lg text-maroon font-semibold">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>

{# This script will be re-executed by HTMX after swap #}
<script>
    // DataTables initialization (will be handled by htmx:afterSwap in parent list.html)
    // No need to initialize here as it's a partial and will be destroyed/re-initialized by parent
</script>
```

### 4.5 URLs (`inventory_reports/urls.py`)

Define the URL patterns for our views within the `inventory_reports` app.

```python
from django.urls import path
from .views import WorkOrderIssueListView, WorkOrderIssueTablePartialView, CustomerAutocompleteView, WorkOrderIssueDetailRedirectView

app_name = 'inventory_reports'

urlpatterns = [
    # Main Work Order Issue & Shortage Report page
    path('work-order-issue/', WorkOrderIssueListView.as_view(), name='work_order_issue_list'),
    
    # HTMX endpoint to load/reload the Work Order table content (for search/filter)
    path('work-order-issue/table/', WorkOrderIssueTablePartialView.as_view(), name='work_order_issue_table'),
    
    # HTMX endpoint for customer name auto-completion
    path('work-order-issue/autocomplete-customer/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Endpoint for redirecting to detail pages (Issue or Shortage)
    path('work-order-issue/<str:wo_no>/select/', WorkOrderIssueDetailRedirectView.as_view(), name='work_order_select_redirect'),

    # Placeholder URLs for the actual detail pages (these would be in a separate 'work_order_details' app)
    # path('work-order-details/issue/<str:wo_no>/', WorkOrderIssueDetailView.as_view(), name='issue_detail'),
    # path('work-order-details/shortage/<str:wo_no>/', WorkOrderShortageDetailView.as_view(), name='shortage_detail'),
]
```

**Project-level `urls.py` (e.g., `myproject/urls.py`):**

```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('inventory/', include('inventory_reports.urls')), # Include your new app's URLs
    # Assuming 'work_order_details' app exists for redirection targets
    path('work-order-details/', include('work_order_details.urls', namespace='work_order_details')),
]
```

### 4.6 Tests (`inventory_reports/tests.py`)

Comprehensive tests for models and views ensure functionality and prevent regressions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch, MagicMock
from .models import WorkOrder, WorkOrderCategory, Customer, BOMMaster

# Define default settings for tests if not already in your settings.py
# In a real project, these would be in settings.py or test_settings.py
if not hasattr(settings, 'DEFAULT_COMPANY_ID'):
    settings.DEFAULT_COMPANY_ID = 1
if not hasattr(settings, 'DEFAULT_FIN_YEAR_ID'):
    settings.DEFAULT_FIN_YEAR_ID = 2023

class WorkOrderModelTest(TestCase):
    """
    Unit tests for the WorkOrder, WorkOrderCategory, Customer, and BOMMaster models.
    Focus on model field definitions, string representations, and custom manager logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = settings.DEFAULT_COMPANY_ID
        cls.fin_year_id = settings.DEFAULT_FIN_YEAR_ID

        cls.category_issue = WorkOrderCategory.objects.create(
            cid=101, symbol='ISS', cname='Issue Category', comp_id=cls.company_id
        )
        cls.category_shortage = WorkOrderCategory.objects.create(
            cid=102, symbol='SHT', cname='Shortage Category', comp_id=cls.company_id
        )

        cls.customer1 = Customer.objects.create(
            customer_id=1, customer_name='Test Customer A', comp_id=cls.company_id
        )
        cls.customer2 = Customer.objects.create(
            customer_id=2, customer_name='Another Customer B', comp_id=cls.company_id
        )

        cls.wo1 = WorkOrder.objects.create(
            wo_no='WO001', fin_year=2023, customer_name='Test Customer A', customer_id=cls.customer1.customer_id,
            enquiry_no='ENQ001', po_no='PO001', sys_date='2023-01-15', employee_name='Emp One',
            category=cls.category_issue, comp_id=cls.company_id, fin_year_id=cls.fin_year_id
        )
        cls.wo2 = WorkOrder.objects.create(
            wo_no='WO002', fin_year=2023, customer_name='Another Customer B', customer_id=cls.customer2.customer_id,
            enquiry_no='ENQ002', po_no='PO002', sys_date='2023-01-20', employee_name='Emp Two',
            category=cls.category_shortage, comp_id=cls.company_id, fin_year_id=cls.fin_year_id
        )
        cls.wo3_no_bom = WorkOrder.objects.create(
            wo_no='WO003', fin_year=2023, customer_name='Third Customer C', customer_id=3,
            enquiry_no='ENQ003', po_no='PO003', sys_date='2023-01-25', employee_name='Emp Three',
            category=cls.category_issue, comp_id=cls.company_id, fin_year_id=cls.fin_year_id
        )

        BOMMaster.objects.create(won_no_ref='WO001')
        BOMMaster.objects.create(won_no_ref='WO002')


    def test_work_order_creation(self):
        self.assertEqual(WorkOrder.objects.count(), 3)
        self.assertEqual(self.wo1.customer_name, 'Test Customer A')
        self.assertEqual(self.wo2.category.cname, 'Shortage Category')

    def test_work_order_category_creation(self):
        self.assertEqual(WorkOrderCategory.objects.count(), 2)
        self.assertEqual(self.category_issue.symbol, 'ISS')

    def test_customer_creation(self):
        self.assertEqual(Customer.objects.count(), 2)
        self.assertEqual(self.customer1.customer_name, 'Test Customer A')

    def test_bom_master_creation(self):
        self.assertEqual(BOMMaster.objects.count(), 2)
        self.assertEqual(BOMMaster.objects.get(won_no_ref='WO001').won_no_ref, 'WO001')

    def test_model_string_representation(self):
        self.assertEqual(str(self.wo1), 'WO001 (Test Customer A)')
        self.assertEqual(str(self.category_issue), 'ISS - Issue Category')
        self.assertEqual(str(self.customer1), 'Test Customer A [1]')

    def test_work_order_manager_get_work_orders_report_no_filter(self):
        # When no filters are applied, should return all WOs that have BOM entries
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type=None, search_value=None, wo_category_id='WO Category'
        )
        self.assertIn(self.wo1, qs)
        self.assertIn(self.wo2, qs)
        self.assertNotIn(self.wo3_no_bom, qs) # WO003 has no BOM entry
        self.assertEqual(qs.count(), 2) # Only WO001 and WO002 have BOM entries

    def test_work_order_manager_filter_by_category(self):
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type=None, search_value=None, wo_category_id=str(self.category_issue.cid)
        )
        self.assertIn(self.wo1, qs)
        self.assertNotIn(self.wo2, qs)
        self.assertNotIn(self.wo3_no_bom, qs) # Also excluded by BOM filter
        self.assertEqual(qs.count(), 1)

    def test_work_order_manager_filter_by_enquiry_no(self):
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type='1', search_value='ENQ002', wo_category_id='WO Category'
        )
        self.assertIn(self.wo2, qs)
        self.assertNotIn(self.wo1, qs)
        self.assertNotIn(self.wo3_no_bom, qs)
        self.assertEqual(qs.count(), 1)

    def test_work_order_manager_filter_by_wo_no(self):
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type='3', search_value='WO001', wo_category_id='WO Category'
        )
        self.assertIn(self.wo1, qs)
        self.assertEqual(qs.count(), 1)

    def test_work_order_manager_filter_by_customer_name_with_id(self):
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type='0', search_value='Test Customer A [1]', wo_category_id='WO Category'
        )
        self.assertIn(self.wo1, qs)
        self.assertEqual(qs.count(), 1)

    def test_work_order_manager_filter_by_customer_name_only(self):
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type='0', search_value='Another Customer B', wo_category_id='WO Category'
        )
        self.assertIn(self.wo2, qs)
        self.assertEqual(qs.count(), 1)

    def test_work_order_manager_filter_no_match(self):
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type='3', search_value='NONEXISTENTWO', wo_category_id='WO Category'
        )
        self.assertEqual(qs.count(), 0)

    def test_work_order_manager_filter_invalid_customer_name_format(self):
        qs = WorkOrder.objects.get_work_orders_report(
            comp_id=self.company_id, fin_year_id=self.fin_year_id,
            search_type='0', search_value='Test Customer A [INVALID]', wo_category_id='WO Category'
        )
        self.assertEqual(qs.count(), 0) # Should return empty if customer ID is invalid

class WorkOrderIssueViewsTest(TestCase):
    """
    Integration tests for WorkOrderIssueListView, WorkOrderIssueTablePartialView,
    and CustomerAutocompleteView.
    """
    @classmethod
    def setUpTestData(cls):
        cls.company_id = settings.DEFAULT_COMPANY_ID
        cls.fin_year_id = settings.DEFAULT_FIN_YEAR_ID

        cls.category = WorkOrderCategory.objects.create(cid=101, symbol='REP', cname='Repair Category', comp_id=cls.company_id)
        cls.customer = Customer.objects.create(customer_id=1, customer_name='Django Customer', comp_id=cls.company_id)
        cls.wo_with_bom = WorkOrder.objects.create(
            wo_no='WO-BOM-123', fin_year=2024, customer_name='Django Customer', customer_id=cls.customer.customer_id,
            enquiry_no='EQ001', po_no='P001', sys_date='2024-03-01', employee_name='John Doe',
            category=cls.category, comp_id=cls.company_id, fin_year_id=cls.fin_year_id
        )
        cls.wo_without_bom = WorkOrder.objects.create(
            wo_no='WO-NOBOM-456', fin_year=2024, customer_name='Another Customer', customer_id=2,
            enquiry_no='EQ002', po_no='P002', sys_date='2024-03-02', employee_name='Jane Doe',
            category=cls.category, comp_id=cls.company_id, fin_year_id=cls.fin_year_id
        )
        BOMMaster.objects.create(won_no_ref=cls.wo_with_bom.wo_no)

    def setUp(self):
        self.client = Client()
        # Set session variables for the test client
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.fin_year_id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('inventory_reports:work_order_issue_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/work_order_issue/list.html')
        self.assertIsInstance(response.context['filter_form'], WorkOrderFilterForm)
        # Initial table should be empty or load via HTMX trigger
        self.assertQuerysetEqual(response.context['work_orders'], [])

    def test_table_partial_view_get_no_filters(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory_reports:work_order_issue_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/work_order_issue/_work_order_issue_table.html')
        # Only WO with BOM should be present
        self.assertContains(response, 'WO-BOM-123')
        self.assertNotContains(response, 'WO-NOBOM-456') # Should not contain WO without BOM

    def test_table_partial_view_filter_by_wo_no(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('inventory_reports:work_order_issue_table'),
            {'search_by': '3', 'search_value': 'WO-BOM-123'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-BOM-123')
        self.assertNotContains(response, 'WO-NOBOM-456')

    def test_table_partial_view_filter_by_customer_name_autocomplete(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('inventory_reports:work_order_issue_table'),
            {'search_by': '0', 'customer_autocomplete_value': f'{self.customer.customer_name} [{self.customer.customer_id}]'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-BOM-123')
        self.assertNotContains(response, 'WO-NOBOM-456')

    def test_customer_autocomplete_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('inventory_reports:customer_autocomplete'),
            {'customer_autocomplete_value': 'django'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'{self.customer.customer_name} [{self.customer.customer_id}]')
        self.assertContains(response, 'id="customer-suggestions"')

    def test_customer_autocomplete_view_no_prefix(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('inventory_reports:customer_autocomplete'),
            {'customer_autocomplete_value': ''},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'id="customer-suggestions"')
        self.assertEqual(response.content.decode(), '') # Expect empty div if no suggestions

    @patch('inventory_reports.views.generate_random_key', return_value='RANDOMKEY123')
    def test_work_order_select_redirect_issue(self, mock_generate_random_key):
        # Mocking `reverse_lazy` for detail views that might not exist in test setup
        with patch('django.urls.reverse_lazy') as mock_reverse_lazy:
            mock_reverse_lazy.side_effect = lambda name, kwargs: f"/{name}/{kwargs['wo_no']}/"

            response = self.client.get(
                reverse('inventory_reports:work_order_select_redirect', kwargs={'wo_no': self.wo_with_bom.wo_no}),
                {'wo_type': '0'}, # Issue
                HTTP_HX_REQUEST='true'
            )
            self.assertEqual(response.status_code, 204) # HTMX No Content response
            self.assertIn('HX-Redirect', response.headers)
            self.assertIn(f'/work_order_details:issue_detail/{self.wo_with_bom.wo_no}/?Key=RANDOMKEY123', response.headers['HX-Redirect'])
            mock_generate_random_key.assert_called_once()

    @patch('inventory_reports.views.generate_random_key', return_value='RANDOMKEY456')
    def test_work_order_select_redirect_shortage(self, mock_generate_random_key):
        with patch('django.urls.reverse_lazy') as mock_reverse_lazy:
            mock_reverse_lazy.side_effect = lambda name, kwargs: f"/{name}/{kwargs['wo_no']}/"

            response = self.client.get(
                reverse('inventory_reports:work_order_select_redirect', kwargs={'wo_no': self.wo_with_bom.wo_no}),
                {'wo_type': '1'}, # Shortage
                HTTP_HX_REQUEST='true'
            )
            self.assertEqual(response.status_code, 204)
            self.assertIn('HX-Redirect', response.headers)
            self.assertIn(f'/work_order_details:shortage_detail/{self.wo_with_bom.wo_no}/?Key=RANDOMKEY456', response.headers['HX-Redirect'])
            mock_generate_random_key.assert_called_once()

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic updates:**
    *   The main list page (`list.html`) uses `hx-get` on the `work-order-table-container` to fetch the table content.
    *   Filter changes (dropdowns, text inputs) trigger `hx-get` to the `work_order_issue_table` URL, targeting the `work-order-table-container` and swapping its `innerHTML`. This simulates the ASP.NET `AutoPostBack` and `btnSearch_Click`.
    *   The search button explicitly triggers a form submission (which HTMX intercepts).
    *   Customer auto-completion uses `hx-get` to `customer_autocomplete` URL, targeting a suggestion div.
    *   The "Select" button in the table triggers an `hx-get` to the `work_order_select_redirect` URL. This view then sends an `HX-Redirect` header to perform a client-side full page navigation.
    *   A loading indicator (`#table-loading-indicator`) is used with `hx-indicator` to provide visual feedback during AJAX requests.
*   **Alpine.js for UI state management:**
    *   `x-data="{ searchBy: '...' }"` on a parent `div` in `list.html` manages the visibility of the search input fields (`search_value` vs `customer_autocomplete_value`) based on the `search_by` dropdown selection. This replaces the ASP.NET `txtEnqId.Visible` and `TxtSearchValue.Visible` toggles.
    *   Alpine.js is also used on each table row (`<tr x-data="{ woType: '0' }">`) to bind the `wo_type` dropdown value to an Alpine.js variable. This variable is then used in the `hx-get` attribute of the "Select" button to pass the selected WO type to the redirection view.
*   **DataTables for list views:**
    *   The `_work_order_issue_table.html` partial includes the `<table>` element with `id="workOrderIssueTable"`.
    *   The `list.html` template contains JavaScript to initialize DataTables. Crucially, it listens for the `htmx:afterSwap` event on the `work-order-table-container` element. This ensures that every time HTMX replaces the table content, the DataTables plugin is re-initialized on the new DOM elements, maintaining sorting, searching, and pagination functionality without full page reloads. The `destroy: true` option ensures proper re-initialization.

## Final Notes

*   **Placeholders:** `[APP_NAME]`, `[MODEL_NAME]`, etc., have been replaced with `inventory_reports`, `WorkOrder`, `WorkOrderCategory`, `Customer`, and `BOMMaster` as appropriate. Database column names (`db_column`) are inferred and should be verified against your actual database schema.
*   **DRY Templates:** We've used `{% include %}` for the table partial, keeping the `list.html` clean and reusable.
*   **Fat Model, Thin View:** The complex data retrieval and filtering logic from `BindDataCust` and `Sp_WONO_NotInBom` is encapsulated within the `WorkOrderManager.get_work_orders_report` method, ensuring views remain concise and focused on rendering.
*   **Comprehensive Tests:** Unit tests for models and integration tests for views have been provided to ensure the correctness and robustness of the migrated functionality.
*   **Session Management:** The ASP.NET code relies on `Session["compid"]` and `Session["finyear"]`. In Django, these would typically come from the authenticated user's profile, a custom session manager, or context variables. The provided Django code assumes these are available in `request.session` or provides default values.
*   **Error Handling:** The original ASP.NET code used empty `catch (Exception ex) { }` blocks. The Django solution implicitly handles many errors gracefully through the framework, but robust error logging and user-friendly error messages should be implemented in a production environment.
*   **Security:** Ensure proper authentication and authorization are implemented in your Django project, as the ASP.NET code relies on `Session["username"]` for user context.
*   **Static Files:** Assume `core/base.html` correctly links to Tailwind CSS, HTMX, Alpine.js, and DataTables CDN links.