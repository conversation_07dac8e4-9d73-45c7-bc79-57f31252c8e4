This comprehensive modernization plan outlines the transition of your legacy ASP.NET Stock Ledger application to a modern, efficient Django 5.0+ solution. By leveraging AI-assisted automation, we aim to minimize manual effort, reduce errors, and accelerate your digital transformation.

Our approach prioritizes a "fat model, thin view" architecture, using HTMX and Alpine.js for dynamic frontend interactions, ensuring a responsive user experience without complex JavaScript frameworks. DataTables will provide robust data presentation and filtering capabilities.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts primarily with `tblDG_Item_Master`, `tblDG_Category_Master`, and `tblFinancial_master`. There's also an inferred `tblLocation` table based on the `fun.drpLocat` call. The `GetAllItem` stored procedure seems to be a central data retrieval mechanism, which we will simulate with Django ORM for filtering or use direct SQL calls if its complexity demands.

**Inferred Tables and Columns:**

*   **`tblDG_Item_Master`**:
    *   `Id` (Primary Key, integer)
    *   `ItemCode` (string)
    *   `ManfDesc` (string, Description)
    *   `UOMBasic` (string, Unit of Measure)
    *   `StockQty` (decimal/numeric)
    *   `CId` (Foreign Key to `tblDG_Category_Master`, integer)
    *   `Location` (Foreign Key to `tblLocation`, integer - inferred)
    *   `FileName` (string, for image file name)
    *   `FileData` (binary, for image data)
    *   `ContentType` (string, for image content type)
    *   `AttName` (string, for attachment file name)
    *   `AttData` (binary, for attachment data)
    *   `AttContentType` (string, for attachment content type)
    *   `CompId` (integer, Company ID)
    *   `FinYearId` (integer, Financial Year ID)

*   **`tblDG_Category_Master`**:
    *   `CId` (Primary Key, integer)
    *   `CompId` (integer, Company ID)
    *   `Symbol` (string)
    *   `CName` (string, Category Name)

*   **`tblFinancial_master`**:
    *   `FinYearId` (Primary Key, integer)
    *   `CompId` (integer, Company ID)
    *   `FinYearFrom` (date)
    *   `FinYearTo` (date)

*   **`tblLocation` (Inferred)**:
    *   `LId` (Primary Key, integer)
    *   `LName` (string, Location Name)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic.

**Analysis:**

*   **Read (R):**
    *   The primary function is to display a list of "Stock Ledger" items based on various filters (Financial Year, From/To Dates, Item Type - Category/WO Items, Search Code - Item Code/Description/Location, Search Term).
    *   Data is displayed in a `GridView` (`GridView1`) with pagination and columns like SN, Item Code, Description, UOM, Stock Quantity.
    *   Dynamic population of `DrpCategory1` and `DropDownList3` based on `DrpType` and `DrpSearchCode` selections.
    *   Display of `lblFromDate`, `lblToDate` based on `Session` values and `tblFinancial_master`.
*   **Update (U):** No direct update operation for the stock ledger items is observed on this page.
*   **Create (C):** No direct create operation observed.
*   **Delete (D):** No direct delete operation observed.
*   **Other Actions:**
    *   **"Select" Command (`CommandName="Sel"`):** Redirects to `StockLedger_Details.aspx` with `Id`, `FD` (From Date), `TD` (To Date) as query parameters. This implies a detailed view for a selected item.
    *   **"downloadImg" Command (`CommandName="downloadImg"`):** Redirects to `DownloadFile.aspx` to download an associated image (`FileName`, `FileData`, `ContentType`).
    *   **"downloadSpec" Command (`CommandName="downloadSpec"`):** Redirects to `DownloadFile.aspx` to download an associated specification sheet (`AttName`, `AttData`, `AttContentType`).
*   **Validation:**
    *   Date validation (`RequiredFieldValidator`, `RegularExpressionValidator`) for `TxtFromDate` and `TxtToDate`.
    *   Date range validation: `To Date >= From Date`.
    *   Financial Year validation: `From Date >= Financial Year Opening Date`.
    *   Client-side alerts for missing selections ("Please Select Category or WO Items.").
*   **Dynamic UI:** Visibility of search controls (`DrpCategory1`, `DrpSearchCode`, `DropDownList3`, `txtSearchItemCode`) changes based on `DrpType` and `DrpSearchCode` selections.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **`asp:Label` (`lblFromDate`, `lblToDate`, `lblMessage`):** Display static text or messages.
*   **`asp:TextBox` (`TxtFromDate`, `TxtToDate`, `txtSearchItemCode`):** User input for dates and search terms. `CalendarExtender` for date pickers.
*   **`asp:DropDownList` (`DrpType`, `DrpCategory1`, `DrpSearchCode`, `DropDownList3`):** Selection inputs. `AutoPostBack="True"` indicates immediate server interaction on selection change.
*   **`asp:Button` (`Btnsearch`):** Triggers data retrieval.
*   **`asp:GridView` (`GridView1`):** Displays tabular data. Supports pagination, row commands (`Sel`, `downloadImg`, `downloadSpec`).
*   **`asp:LinkButton`:** Used within `GridView` for "Select" and download actions.

**Django Mapping:**

*   **HTML Structure:** Recreate with Tailwind CSS.
*   **Input Fields:** Django `forms` (e.g., `forms.DateField`, `forms.CharField`, `forms.ChoiceField`, `forms.ModelChoiceField`).
*   **Dynamic UI:** HTMX for partial updates and `hx-get` for loading data. Alpine.js for client-side state management (e.g., toggling visibility of dropdowns/textboxes based on selection).
*   **Date Pickers:** HTML5 `input type="date"` combined with Alpine.js if a more feature-rich date picker is desired.
*   **Grid Data Display:** DataTables JavaScript library, integrated into the template and populated via a Django `ListView` delivering JSON or an HTMX-rendered partial.
*   **File Downloads:** Django views returning `FileResponse` or `StreamingHttpResponse`.
*   **Client-side Alerts:** Django `messages` framework for server-side messages, and potentially `hx-on::after.swap` to display client-side alerts if needed.

---

### Step 4: Generate Django Code

We will create a new Django application named `stockledger`.

#### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**
Models are defined with `managed = False` to connect to existing database tables. Business logic for filtering will be added as a class method to `ItemMaster`.

**File:** `stockledger/models.py`

```python
from django.db import models
from django.urls import reverse
from django.db.models import Q # For complex queries

class FinancialYear(models.Model):
    id = models.AutoField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    from_date = models.DateField(db_column='FinYearFrom')
    to_date = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"{self.from_date.year}-{self.to_date.year}"

class Category(models.Model):
    id = models.AutoField(db_column='CId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'
        
    def __str__(self):
        return f"[{self.symbol}] - {self.name}"

class Location(models.Model):
    # Inferred from fun.drpLocat() call
    id = models.AutoField(db_column='LId', primary_key=True)
    name = models.CharField(db_column='LName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblLocation'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return self.name

class ItemMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4)
    
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='items', null=True, blank=True)
    location = models.ForeignKey(Location, on_delete=models.DO_NOTHING, db_column='Location', related_name='items', null=True, blank=True) # Location might be int PK or a string, assuming FK for now.
    
    file_name = models.CharField(db_column='FileName', max_length=255, null=True, blank=True)
    file_data = models.BinaryField(db_column='FileData', null=True, blank=True) # Assuming binary data
    content_type = models.CharField(db_column='ContentType', max_length=100, null=True, blank=True)

    att_name = models.CharField(db_column='AttName', max_length=255, null=True, blank=True)
    att_data = models.BinaryField(db_column='AttData', null=True, blank=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, null=True, blank=True)

    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    @classmethod
    def get_filtered_stock_ledger_items(cls, comp_id, fin_year_id, type_filter, category_id, search_code, search_term, location_id):
        """
        Mimics the Fillgrid logic to filter ItemMaster records using Django ORM.
        This method centralizes the complex filtering logic.
        """
        # Start with base query, filtering by company and financial year as observed in ASP.NET
        items = cls.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)

        if type_filter == 'Category':
            if category_id: # If a category is selected
                items = items.filter(category__id=category_id)

            if search_code == 'tblDG_Item_Master.ItemCode':
                items = items.filter(item_code__istartswith=search_term)
            elif search_code == 'tblDG_Item_Master.ManfDesc':
                items = items.filter(manf_desc__istartswith=search_term)
            elif search_code == 'tblDG_Item_Master.Location':
                if location_id:
                    items = items.filter(location__id=location_id)
            elif not search_code and search_term: # Case for sd == "Select" && B == "Select" && s != string.Empty
                items = items.filter(manf_desc__icontains=search_term)
        elif type_filter == 'WOItems':
            if search_code == 'tblDG_Item_Master.ItemCode':
                items = items.filter(item_code__icontains=search_term)
            elif search_code == 'tblDG_Item_Master.ManfDesc':
                items = items.filter(manf_desc__icontains=search_term)
            elif not search_code and search_term: # Case for B == "Select" && s != string.Empty
                items = items.filter(manf_desc__icontains=search_term)
        
        # Default order by item_code for consistent results
        return items.order_by('item_code')

    def get_stock_ledger_details_url(self, from_date, to_date):
        """
        Mimics the "Sel" command's redirection to StockLedger_Details.aspx.
        """
        # Assuming a Django view for details page.
        # Dates are passed as query parameters, similar to ASP.NET.
        from urllib.parse import urlencode
        params = {'FD': from_date.strftime('%d-%m-%Y'), 'TD': to_date.strftime('%d-%m-%Y')}
        return f"{reverse('stockledger_details', kwargs={'pk': self.pk})}?{urlencode(params)}"

    def get_download_url(self, file_type='image'):
        """
        Provides URL for file downloads, mimicking DownloadFile.aspx.
        'file_type' can be 'image' or 'spec'.
        """
        return reverse('stockledger_download_file', kwargs={'pk': self.pk, 'file_type': file_type})

```

#### 4.2 Forms

**Task:** Define Django forms for user input and filtering.

**Instructions:**
We'll create a single `StockLedgerFilterForm` to manage all the search and filter inputs.

**File:** `stockledger/forms.py`

```python
from django import forms
from .models import Category, Location, FinancialYear
from datetime import date
from django.core.exceptions import ValidationError

class StockLedgerFilterForm(forms.Form):
    TYPE_CHOICES = [
        ('', 'Select'), # Blank value for "Select"
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]

    SEARCH_CODE_CHOICES = [
        ('', 'Select'), # Blank value for "Select"
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    from_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        initial=date.today(), # Set initial to current date as in ASP.NET
        required=True
    )
    to_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        initial=date.today(), # Set initial to current date
        required=True
    )
    type_filter = forms.ChoiceField(
        choices=TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False,
        initial='', # "Select"
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.none(), # Will be populated dynamically via HTMX or Alpine.js
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False,
        initial='', # "Select"
    )
    search_term = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.none(), # Will be populated dynamically
        required=False,
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
    )

    def __init__(self, *args, **kwargs):
        self.comp_id = kwargs.pop('comp_id', None)
        self.fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)
        
        # Initial population of categories and locations for the first load
        if self.comp_id:
            self.fields['category'].queryset = Category.objects.filter(comp_id=self.comp_id)
        self.fields['location'].queryset = Location.objects.all() # Assuming locations are not comp_id filtered for now

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        type_filter = cleaned_data.get('type_filter')

        if from_date and to_date:
            if from_date > to_date:
                self.add_error('from_date', 'From date should be less than or equal to To date!')
        
            # Mimic financial year date validation
            if self.comp_id and self.fin_year_id:
                try:
                    fin_year = FinancialYear.objects.get(comp_id=self.comp_id, id=self.fin_year_id)
                    if from_date < fin_year.from_date:
                        self.add_error('from_date', f'From date should not be less than opening date ({fin_year.from_date.strftime("%d-%m-%Y")})!')
                except FinancialYear.DoesNotExist:
                    self.add_error(None, 'Financial year data not found for current company.')

        if not type_filter:
            raise ValidationError('Please select Category or WO Items.')

        return cleaned_data

```

#### 4.3 Views

**Task:** Implement Read operations using CBVs and handle file downloads.

**Instructions:**
Views will be thin, relying on models for data retrieval and forms for validation. HTMX will be used extensively for dynamic content updates.

**File:** `stockledger/views.py`

```python
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse, Http404
from django.db.models import Q
from .models import ItemMaster, Category, Location, FinancialYear
from .forms import StockLedgerFilterForm
import mimetypes
from datetime import date

# Helper to get current company and financial year (replace with actual session/user logic)
def get_session_context(request):
    # In a real application, CompId and FinYearId would come from user session,
    # user profile, or other authentication context.
    # For this example, we'll use dummy values.
    # Replace with your actual session/user data retrieval.
    comp_id = getattr(request.user, 'company_id', 1) # Example: user.company_id
    fin_year_id = getattr(request.user, 'financial_year_id', 1) # Example: user.financial_year_id
    
    # Retrieve financial year dates
    fin_year_from = date.today()
    fin_year_to = date.today()
    try:
        financial_master = FinancialYear.objects.get(comp_id=comp_id, id=fin_year_id)
        fin_year_from = financial_master.from_date
        fin_year_to = financial_master.to_date
    except FinancialYear.DoesNotExist:
        messages.warning(request, "Financial year data not found. Using current date as fallback.")

    return {
        'comp_id': comp_id,
        'fin_year_id': fin_year_id,
        'fin_year_from_date': fin_year_from,
        'fin_year_to_date': fin_year_to,
    }


class StockLedgerListView(ListView):
    model = ItemMaster
    template_name = 'stockledger/list.html'
    context_object_name = 'items'
    paginate_by = 20 # ASP.NET GridView PageSize=20

    def get_queryset(self):
        # Initial empty queryset or load from session/defaults
        return ItemMaster.objects.none()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        session_context = get_session_context(self.request)
        
        # Initialize form with data from GET or session/defaults
        form = StockLedgerFilterForm(
            self.request.GET or None,
            comp_id=session_context['comp_id'],
            fin_year_id=session_context['fin_year_id']
        )
        context['form'] = form
        context.update(session_context) # Add financial year info to context
        
        return context

class StockLedgerTablePartialView(ListView):
    model = ItemMaster
    template_name = 'stockledger/_stockledger_table.html'
    context_object_name = 'items'
    paginate_by = 20 # For DataTables pagination

    def get_queryset(self):
        session_context = get_session_context(self.request)
        comp_id = session_context['comp_id']
        fin_year_id = session_context['fin_year_id']

        # Get form data from GET parameters
        form = StockLedgerFilterForm(
            self.request.GET,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )

        if form.is_valid():
            # Use cleaned data to call the model's filtering method
            type_filter = form.cleaned_data.get('type_filter')
            category_id = form.cleaned_data.get('category').id if form.cleaned_data.get('category') else None
            search_code = form.cleaned_data.get('search_code')
            search_term = form.cleaned_data.get('search_term')
            location_id = form.cleaned_data.get('location').id if form.cleaned_data.get('location') else None

            # IMPORTANT: The ASP.NET Fillgrid logic has a check `if (DrpType.SelectedValue != "Select")`
            # We enforce this with form validation (`clean` method)
            if type_filter:
                queryset = ItemMaster.get_filtered_stock_ledger_items(
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    type_filter=type_filter,
                    category_id=category_id,
                    search_code=search_code,
                    search_term=search_term,
                    location_id=location_id
                )
                return queryset
            else:
                # If type_filter is 'Select', return empty queryset (as per ASP.NET logic displaying alert)
                return ItemMaster.objects.none()
        else:
            # If form is not valid (e.g., date validation fails or type not selected),
            # send messages back via HTMX and return empty queryset.
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(self.request, f"Error in {field}: {error}" if field else error)
            return ItemMaster.objects.none()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure 'items' is always in context, even if empty
        if not hasattr(context, 'items'):
            context['items'] = ItemMaster.objects.none()
        return context

class StockLedgerDynamicDropdownView(View):
    """
    Handles dynamic population of dropdowns based on selections.
    Used for Category and Location dropdowns.
    """
    def get(self, request, *args, **kwargs):
        field_name = request.GET.get('field_name')
        comp_id = get_session_context(request)['comp_id']
        
        options = []
        if field_name == 'category':
            options = [{'id': obj.id, 'name': str(obj)} for obj in Category.objects.filter(comp_id=comp_id).order_by('name')]
        elif field_name == 'location':
            options = [{'id': obj.id, 'name': str(obj)} for obj in Location.objects.all().order_by('name')]
        
        # Return options as HTML options for HTMX to swap
        html_options = '<option value="">Select</option>'
        for opt in options:
            html_options += f'<option value="{opt["id"]}">{opt["name"]}</option>'
        return HttpResponse(html_options)

class StockLedgerDetailsView(TemplateView):
    """
    Placeholder for the details page, mimicking StockLedger_Details.aspx.
    This view would display details for a specific ItemMaster.
    """
    template_name = 'stockledger/details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_id = kwargs.get('pk')
        from_date_str = self.request.GET.get('FD')
        to_date_str = self.request.GET.get('TD')

        try:
            context['item'] = ItemMaster.objects.get(pk=item_id)
            context['from_date_param'] = from_date_str
            context['to_date_param'] = to_date_str
            # Additional logic to retrieve related ledger data based on dates if available
        except ItemMaster.DoesNotExist:
            raise Http404("Item not found.")
        return context

class StockLedgerFileDownloadView(View):
    """
    Handles file downloads (Image, Spec Sheet), mimicking DownloadFile.aspx.
    """
    def get(self, request, pk, file_type, *args, **kwargs):
        try:
            item = ItemMaster.objects.get(pk=pk)
        except ItemMaster.DoesNotExist:
            raise Http404("File not found.")

        if file_type == 'image':
            file_data = item.file_data
            file_name = item.file_name
            content_type = item.content_type
        elif file_type == 'spec':
            file_data = item.att_data
            file_name = item.att_name
            content_type = item.att_content_type
        else:
            raise Http404("Invalid file type.")

        if not file_data:
            raise Http404("File data is missing.")

        # Try to infer content type if not explicitly set
        if not content_type:
            content_type, _ = mimetypes.guess_type(file_name)
            if not content_type:
                content_type = 'application/octet-stream' # Default if type cannot be guessed

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will use HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for grid functionality.

**File:** `stockledger/templates/stockledger/list.html` (Main page)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ 
    typeFilter: '{{ form.type_filter.value }}',
    searchCode: '{{ form.search_code.value }}',
    showCategory: false,
    showLocation: false,
    showSearchTerm: false,
    init() {
        this.$watch('typeFilter', value => this.updateVisibility(value, this.searchCode));
        this.$watch('searchCode', value => this.updateVisibility(this.typeFilter, value));
        this.updateVisibility(this.typeFilter, this.searchCode);
    },
    updateVisibility(type, search) {
        this.showCategory = (type === 'Category');
        this.showSearchTerm = true; // Default for most cases
        this.showLocation = false; // Default to false
        
        if (search === 'tblDG_Item_Master.Location') {
            this.showLocation = true;
            this.showSearchTerm = false;
        } else if (search === 'Select' || search === '') {
            // If search_code is 'Select' or empty, and type is Category or WOItems
            // The ASP.NET code shows txtSearchItemCode if type is selected
            if (type !== 'Select' && type !== '') {
                this.showSearchTerm = true;
            } else {
                this.showSearchTerm = false; // If type is "Select"
            }
            this.showLocation = false;
        }
    }
}">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Stock Ledger</h2>

    <!-- Financial Year Display -->
    <div class="mb-6 bg-gray-50 p-4 rounded-lg shadow-sm text-sm text-gray-700">
        <p class="font-semibold">Financial Year: 
            <span class="text-blue-600">{{ fin_year_from_date|date:"d-m-Y" }}</span> 
            to 
            <span class="text-blue-600">{{ fin_year_to_date|date:"d-m-Y" }}</span>
        </p>
        {% if messages %}
            <div class="mt-4">
                {% for message in messages %}
                    <div class="p-3 mb-2 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-700{% else %}bg-green-100 text-green-700{% endif %}" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    </div>

    <!-- Filter Form -->
    <form id="stockLedgerFilterForm" 
          hx-get="{% url 'stockledger_table' %}" 
          hx-target="#stockLedgerTable-container" 
          hx-swap="innerHTML"
          hx-trigger="submit, DrpTypeChange from:#id_type_filter, DrpCategory1Change from:#id_category, DrpSearchCodeChange from:#id_search_code, DropDownList3Change from:#id_location"
          class="bg-white p-6 rounded-lg shadow-md mb-8 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% csrf_token %}

        <div>
            <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
            {{ form.from_date }}
            {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
            {{ form.to_date }}
            {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
        </div>
        
        <div class="col-span-full flex flex-wrap gap-4">
            <div>
                <label for="{{ form.type_filter.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
                <select id="{{ form.type_filter.id_for_label }}" name="{{ form.type_filter.html_name }}"
                        class="{{ form.type_filter.field.widget.attrs.class }}"
                        x-model="typeFilter"
                        hx-trigger="change"
                        hx-target="#stockLedgerTable-container"
                        hx-swap="innerHTML"
                        hx-get="{% url 'stockledger_table' %}"
                        hx-include="#stockLedgerFilterForm"
                        hx-indicator="#loadingIndicator">
                    {% for value, label in form.type_filter.field.choices %}
                        <option value="{{ value }}" {% if value == form.type_filter.value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
                {% if form.type_filter.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_filter.errors }}</p>{% endif %}
            </div>

            <div x-show="showCategory" style="display: none;">
                <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
                <select id="{{ form.category.id_for_label }}" name="{{ form.category.html_name }}"
                        class="{{ form.category.field.widget.attrs.class }}"
                        hx-trigger="change"
                        hx-target="#stockLedgerTable-container"
                        hx-swap="innerHTML"
                        hx-get="{% url 'stockledger_table' %}"
                        hx-include="#stockLedgerFilterForm"
                        hx-indicator="#loadingIndicator">
                    <option value="">{{ form.category.empty_label }}</option>
                    {% for pk, name in form.category.field.choices %}
                        {% if pk %} {# Exclude the empty label #}
                            <option value="{{ pk }}" {% if pk|stringformat:"s" == form.category.value|stringformat:"s" %}selected{% endif %}>{{ name }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
                {% if form.category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>{% endif %}
            </div>

            <div>
                <label for="{{ form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                <select id="{{ form.search_code.id_for_label }}" name="{{ form.search_code.html_name }}"
                        class="{{ form.search_code.field.widget.attrs.class }}"
                        x-model="searchCode"
                        hx-trigger="change"
                        hx-target="#stockLedgerTable-container"
                        hx-swap="innerHTML"
                        hx-get="{% url 'stockledger_table' %}"
                        hx-include="#stockLedgerFilterForm"
                        hx-indicator="#loadingIndicator">
                    {% for value, label in form.search_code.field.choices %}
                        <option value="{{ value }}" {% if value == form.search_code.value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
                {% if form.search_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.search_code.errors }}</p>{% endif %}
            </div>

            <div x-show="showSearchTerm" style="display: none;">
                <label for="{{ form.search_term.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Term</label>
                {{ form.search_term }}
                {% if form.search_term.errors %}<p class="text-red-500 text-xs mt-1">{{ form.search_term.errors }}</p>{% endif %}
            </div>

            <div x-show="showLocation" style="display: none;">
                <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">Location</label>
                <select id="{{ form.location.id_for_label }}" name="{{ form.location.html_name }}"
                        class="{{ form.location.field.widget.attrs.class }}"
                        hx-trigger="change"
                        hx-target="#stockLedgerTable-container"
                        hx-swap="innerHTML"
                        hx-get="{% url 'stockledger_table' %}"
                        hx-include="#stockLedgerFilterForm"
                        hx-indicator="#loadingIndicator">
                    <option value="">{{ form.location.empty_label }}</option>
                    {% for pk, name in form.location.field.choices %}
                        {% if pk %}
                            <option value="{{ pk }}" {% if pk|stringformat:"s" == form.location.value|stringformat:"s" %}selected{% endif %}>{{ name }}</option>
                        {% endif %}
                    {% endfor %}
                </select>
                {% if form.location.errors %}<p class="text-red-500 text-xs mt-1">{{ form.location.errors }}</p>{% endif %}
            </div>
            
            <div class="flex items-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow">
                    Search
                </button>
            </div>
        </div>
    </form>

    <!-- Data Table Container -->
    <div id="stockLedgerTable-container"
         hx-trigger="load delay:10ms" {# Load table on page load #}
         hx-get="{% url 'stockledger_table' %}"
         hx-include="#stockLedgerFilterForm" {# Include form data on initial load #}
         hx-swap="innerHTML">
        <!-- Loading spinner while content loads -->
        <div id="loadingIndicator" class="htmx-indicator text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Stock Ledger...</p>
        </div>
    </div>

    <!-- Modals for details if needed (not explicitly in ASP.NET for this page but a common pattern) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        // Re-initialize DataTable if a new table is swapped in
        if (evt.detail.target.id === 'stockLedgerTable-container') {
            $('#stockLedgerTable').DataTable({
                "pageLength": 20, // Matches ASP.NET GridView PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "responsive": true
            });
        }
    });

    // Handle messages after HTMX requests (if not already handled by htmx.js)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.getResponseHeader('HX-Trigger-After-Swap')) {
            const triggers = JSON.parse(evt.detail.xhr.getResponseHeader('HX-Trigger-After-Swap'));
            if (triggers.showMessage) {
                // This would be where you show a custom notification toast, etc.
                // For simplicity, Django's default messages framework can handle it if rendered on full page reload.
                // For HTMX, you might need a dedicated message display element that also updates.
                // Here, we just let Django messages render on the next full page or specific message div.
            }
        }
    });

    // Reset form to initial state on certain conditions (e.g., if type filter goes back to "Select")
    document.addEventListener('alpine:init', () => {
        Alpine.data('stockLedgerPage', () => ({
            typeFilter: '{{ form.type_filter.value|default:"" }}',
            searchCode: '{{ form.search_code.value|default:"" }}',
            showCategory: false,
            showLocation: false,
            showSearchTerm: false,
            init() {
                this.$watch('typeFilter', value => this.updateVisibility(value, this.searchCode));
                this.$watch('searchCode', value => this.updateVisibility(this.typeFilter, value));
                this.updateVisibility(this.typeFilter, this.searchCode);
            },
            updateVisibility(type, search) {
                this.showCategory = (type === 'Category');
                this.showLocation = (type === 'Category' && search === 'tblDG_Item_Master.Location');
                this.showSearchTerm = (type !== '' && type !== 'Select' && (search !== 'tblDG_Item_Master.Location' || search === '' || search === 'Select'));
            }
        }));
    });
</script>
{% endblock %}
```

**File:** `stockledger/templates/stockledger/_stockledger_table.html` (Partial for DataTable)

```html
<table id="stockLedgerTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-100">
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Image</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Spec. Sheet</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Stock Qty</th>
        </tr>
    </thead>
    <tbody>
        {% if items %}
            {% for item in items %}
            <tr class="hover:bg-gray-50 {% cycle 'bg-white' 'bg-gray-50' %}">
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-center">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-center">
                    {% comment %}
                        The ASP.NET "Sel" button redirects to StockLedger_Details.aspx with query params.
                        Here, we'll use a standard link or HTMX to load content into a modal/new page.
                        For simplicity, using a standard link for now to match redirect.
                    {% endcomment %}
                    <a href="{{ item.get_stock_ledger_details_url(form.cleaned_data.from_date, form.cleaned_data.to_date) }}" 
                       class="text-blue-600 hover:underline">Select</a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-center">
                    {% if item.file_name %}
                        <a href="{{ item.get_download_url('image') }}" class="text-blue-600 hover:underline">{{ item.file_name }}</a>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-center">
                    {% if item.att_name %}
                        <a href="{{ item.get_download_url('spec') }}" class="text-blue-600 hover:underline">{{ item.att_name }}</a>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ item.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ item.manf_desc }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-center">{{ item.uom_basic }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800 text-right">{{ item.stock_qty }}</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="8" class="py-8 text-center text-gray-600 text-lg">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // This script block will be executed every time the partial is loaded by HTMX.
    // It's crucial to destroy any existing DataTable instance before re-initializing.
    if ($.fn.DataTable.isDataTable('#stockLedgerTable')) {
        $('#stockLedgerTable').DataTable().destroy();
    }
    $('#stockLedgerTable').DataTable({
        "pageLength": 20,
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "responsive": true,
        "paging": true,
        "searching": true,
        "ordering": true,
        "info": true
    });
</script>
```

**File:** `stockledger/templates/stockledger/details.html` (Placeholder for details page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Stock Ledger Details for: {{ item.item_code }}</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <p class="mb-2"><span class="font-semibold">Description:</span> {{ item.manf_desc }}</p>
        <p class="mb-2"><span class="font-semibold">UOM:</span> {{ item.uom_basic }}</p>
        <p class="mb-2"><span class="font-semibold">Stock Quantity:</span> {{ item.stock_qty }}</p>
        <p class="mb-2"><span class="font-semibold">From Date:</span> {{ from_date_param }}</p>
        <p class="mb-4"><span class="font-semibold">To Date:</span> {{ to_date_param }}</p>
        
        <a href="{% url 'stockledger_list' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
            Back to Stock Ledger
        </a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs for the main list view, the HTMX-loaded table partial, dynamic dropdown population, item details, and file downloads.

**File:** `stockledger/urls.py`

```python
from django.urls import path
from .views import StockLedgerListView, StockLedgerTablePartialView, StockLedgerDynamicDropdownView, StockLedgerDetailsView, StockLedgerFileDownloadView

urlpatterns = [
    path('stock-ledger/', StockLedgerListView.as_view(), name='stockledger_list'),
    path('stock-ledger/table/', StockLedgerTablePartialView.as_view(), name='stockledger_table'),
    path('stock-ledger/dynamic-options/', StockLedgerDynamicDropdownView.as_view(), name='stockledger_dynamic_options'),
    path('stock-ledger/details/<int:pk>/', StockLedgerDetailsView.as_view(), name='stockledger_details'),
    path('stock-ledger/download/<int:pk>/<str:file_type>/', StockLedgerFileDownloadView.as_view(), name='stockledger_download_file'),
]
```
You would also need to include these URLs in your project's main `urls.py`:
`path('inventory/', include('stockledger.urls')),`

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and integration tests for all views and HTMX interactions.

**File:** `stockledger/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import ItemMaster, Category, Location, FinancialYear
from datetime import date, timedelta
from io import BytesIO
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch

class StockLedgerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy company and financial year data
        cls.company_id = 1
        cls.fin_year_id = 1
        cls.fin_year_from = date(2023, 4, 1)
        cls.fin_year_to = date(2024, 3, 31)
        FinancialYear.objects.create(
            id=cls.fin_year_id,
            comp_id=cls.company_id,
            from_date=cls.fin_year_from,
            to_date=cls.fin_year_to
        )

        # Create dummy category and location data
        cls.category1 = Category.objects.create(id=101, comp_id=cls.company_id, symbol='CAT1', name='Category One')
        cls.category2 = Category.objects.create(id=102, comp_id=cls.company_id, symbol='CAT2', name='Category Two')
        cls.location1 = Location.objects.create(id=201, name='Warehouse A')
        cls.location2 = Location.objects.create(id=202, name='Factory B')

        # Create test ItemMaster data
        ItemMaster.objects.create(
            id=1,
            item_code='ITEM001',
            manf_desc='Widget Alpha',
            uom_basic='PCS',
            stock_qty=100.50,
            category=cls.category1,
            location=cls.location1,
            comp_id=cls.company_id,
            fin_year_id=cls.fin_year_id,
            file_name='widget_alpha.png',
            file_data=b'imagedata1',
            content_type='image/png',
            att_name='widget_alpha_spec.pdf',
            att_data=b'specdata1',
            att_content_type='application/pdf'
        )
        ItemMaster.objects.create(
            id=2,
            item_code='ITEM002',
            manf_desc='Gadget Beta',
            uom_basic='KG',
            stock_qty=50.25,
            category=cls.category2,
            location=cls.location2,
            comp_id=cls.company_id,
            fin_year_id=cls.fin_year_id
        )
        ItemMaster.objects.create(
            id=3,
            item_code='ITEM003',
            manf_desc='Component Gamma',
            uom_basic='MTR',
            stock_qty=200.00,
            category=cls.category1,
            location=cls.location1,
            comp_id=cls.company_id,
            fin_year_id=cls.fin_year_id
        )
        ItemMaster.objects.create(
            id=4,
            item_code='ANOTHER_ITEM',
            manf_desc='Another Gadget for WO',
            uom_basic='LBS',
            stock_qty=10.00,
            category=None, # WOItems might not have category
            location=None,
            comp_id=cls.company_id,
            fin_year_id=cls.fin_year_id
        )

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.manf_desc, 'Widget Alpha')
        self.assertEqual(item.stock_qty, 100.50)
        self.assertEqual(item.category, self.category1)
        self.assertEqual(item.location, self.location1)

    def test_item_master_str(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(str(item), 'ITEM001 - Widget Alpha')

    def test_get_filtered_stock_ledger_items_no_filter(self):
        # Simulating initial load with no specific filters
        items = ItemMaster.get_filtered_stock_ledger_items(
            comp_id=self.company_id,
            fin_year_id=self.fin_year_id,
            type_filter='Select', # Or empty string as per form logic
            category_id=None,
            search_code=None,
            search_term='',
            location_id=None
        )
        self.assertEqual(items.count(), 0) # Expect 0 if type_filter is 'Select'

    def test_get_filtered_stock_ledger_items_category_no_search(self):
        items = ItemMaster.get_filtered_stock_ledger_items(
            comp_id=self.company_id,
            fin_year_id=self.fin_year_id,
            type_filter='Category',
            category_id=self.category1.id,
            search_code=None,
            search_term='',
            location_id=None
        )
        self.assertIn(ItemMaster.objects.get(item_code='ITEM001'), items)
        self.assertIn(ItemMaster.objects.get(item_code='ITEM003'), items)
        self.assertEqual(items.count(), 2)

    def test_get_filtered_stock_ledger_items_category_item_code_search(self):
        items = ItemMaster.get_filtered_stock_ledger_items(
            comp_id=self.company_id,
            fin_year_id=self.fin_year_id,
            type_filter='Category',
            category_id=self.category1.id,
            search_code='tblDG_Item_Master.ItemCode',
            search_term='ITEM001',
            location_id=None
        )
        self.assertIn(ItemMaster.objects.get(item_code='ITEM001'), items)
        self.assertEqual(items.count(), 1)

    def test_get_filtered_stock_ledger_items_category_desc_search(self):
        items = ItemMaster.get_filtered_stock_ledger_items(
            comp_id=self.company_id,
            fin_year_id=self.fin_year_id,
            type_filter='Category',
            category_id=self.category1.id,
            search_code='tblDG_Item_Master.ManfDesc',
            search_term='Widget',
            location_id=None
        )
        self.assertIn(ItemMaster.objects.get(manf_desc='Widget Alpha'), items)
        self.assertEqual(items.count(), 1)

    def test_get_filtered_stock_ledger_items_category_location_search(self):
        items = ItemMaster.get_filtered_stock_ledger_items(
            comp_id=self.company_id,
            fin_year_id=self.fin_year_id,
            type_filter='Category',
            category_id=self.category1.id,
            search_code='tblDG_Item_Master.Location',
            search_term='', # search_term is ignored for location, location_id is used
            location_id=self.location1.id
        )
        self.assertIn(ItemMaster.objects.get(item_code='ITEM001'), items)
        self.assertIn(ItemMaster.objects.get(item_code='ITEM003'), items)
        self.assertEqual(items.count(), 2)

    def test_get_filtered_stock_ledger_items_category_manf_desc_only(self):
        # This tests the 'sd == "Select" && B == "Select" && s != string.Empty' case
        # in the ASP.NET code, which is handled by filtering `manf_desc` if no category/search_code is selected.
        items = ItemMaster.get_filtered_stock_ledger_items(
            comp_id=self.company_id,
            fin_year_id=self.fin_year_id,
            type_filter='Category',
            category_id=None,
            search_code=None,
            search_term='Gadget',
            location_id=None
        )
        self.assertIn(ItemMaster.objects.get(manf_desc='Gadget Beta'), items)
        self.assertEqual(items.count(), 1)


    def test_get_filtered_stock_ledger_items_wo_items_desc_search(self):
        items = ItemMaster.get_filtered_stock_ledger_items(
            comp_id=self.company_id,
            fin_year_id=self.fin_year_id,
            type_filter='WOItems',
            category_id=None,
            search_code='tblDG_Item_Master.ManfDesc',
            search_term='Another Gadget',
            location_id=None
        )
        self.assertIn(ItemMaster.objects.get(item_code='ANOTHER_ITEM'), items)
        self.assertEqual(items.count(), 1)
    
    def test_get_stock_ledger_details_url(self):
        item = ItemMaster.objects.get(id=1)
        from_dt = date(2024, 1, 1)
        to_dt = date(2024, 1, 31)
        expected_url = f'/inventory/stock-ledger/details/1/?FD=01-01-2024&TD=31-01-2024'
        self.assertEqual(item.get_stock_ledger_details_url(from_dt, to_dt), expected_url)

    def test_get_download_image_url(self):
        item = ItemMaster.objects.get(id=1)
        expected_url = reverse('stockledger_download_file', kwargs={'pk': item.pk, 'file_type': 'image'})
        self.assertEqual(item.get_download_url('image'), expected_url)

    def test_get_download_spec_url(self):
        item = ItemMaster.objects.get(id=1)
        expected_url = reverse('stockledger_download_file', kwargs={'pk': item.pk, 'file_type': 'spec'})
        self.assertEqual(item.get_download_url('spec'), expected_url)

# Mock the get_session_context to provide consistent session data for tests
class MockSessionContext:
    def __init__(self, comp_id, fin_year_id, fin_year_from_date, fin_year_to_date):
        self.comp_id = comp_id
        self.fin_year_id = fin_year_id
        self.fin_year_from_date = fin_year_from_date
        self.fin_year_to_date = fin_year_to_date

    def __call__(self, request):
        return {
            'comp_id': self.comp_id,
            'fin_year_id': self.fin_year_id,
            'fin_year_from_date': self.fin_year_from_date,
            'fin_year_to_date': self.fin_year_to_date,
        }

@patch('stockledger.views.get_session_context', MockSessionContext(
    comp_id=1,
    fin_year_id=1,
    fin_year_from_date=date(2023, 4, 1),
    fin_year_to_date=date(2024, 3, 31)
))
class StockLedgerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy company and financial year data
        cls.company_id = 1
        cls.fin_year_id = 1
        cls.fin_year_from = date(2023, 4, 1)
        cls.fin_year_to = date(2024, 3, 31)
        FinancialYear.objects.create(
            id=cls.fin_year_id,
            comp_id=cls.company_id,
            from_date=cls.fin_year_from,
            to_date=cls.fin_year_to
        )

        # Create dummy category and location data
        cls.category1 = Category.objects.create(id=101, comp_id=cls.company_id, symbol='CAT1', name='Category One')
        cls.category2 = Category.objects.create(id=102, comp_id=cls.company_id, symbol='CAT2', name='Category Two')
        cls.location1 = Location.objects.create(id=201, name='Warehouse A')
        cls.location2 = Location.objects.create(id=202, name='Factory B')

        # Create test ItemMaster data
        ItemMaster.objects.create(
            id=1,
            item_code='ITEM001',
            manf_desc='Widget Alpha',
            uom_basic='PCS',
            stock_qty=100.50,
            category=cls.category1,
            location=cls.location1,
            comp_id=cls.company_id,
            fin_year_id=cls.fin_year_id,
            file_name='test_image.png',
            file_data=b'dummy_image_data',
            content_type='image/png',
            att_name='test_spec.pdf',
            att_data=b'dummy_spec_data',
            att_content_type='application/pdf'
        )
        ItemMaster.objects.create(
            id=2,
            item_code='ITEM002',
            manf_desc='Gadget Beta',
            uom_basic='KG',
            stock_qty=50.25,
            category=cls.category2,
            location=cls.location2,
            comp_id=cls.company_id,
            fin_year_id=cls.fin_year_id
        )

    def setUp(self):
        self.client = Client()

    def test_stock_ledger_list_view(self):
        response = self.client.get(reverse('stockledger_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'stockledger/list.html')
        self.assertIsInstance(response.context['form'], (object)) # Check if form object is present

    def test_stock_ledger_table_partial_view_no_filters(self):
        # Initial load with no specific filters selected
        response = self.client.get(reverse('stockledger_table'), {
            'from_date': date.today().strftime('%Y-%m-%d'),
            'to_date': date.today().strftime('%Y-%m-%d'),
            'type_filter': '', # 'Select'
            'category': '',
            'search_code': '',
            'search_term': '',
            'location': ''
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'stockledger/_stockledger_table.html')
        self.assertContains(response, 'No data to display !') # Should be empty if type_filter is 'Select' or empty

    def test_stock_ledger_table_partial_view_valid_filter(self):
        response = self.client.get(reverse('stockledger_table'), {
            'from_date': date.today().strftime('%Y-%m-%d'),
            'to_date': date.today().strftime('%Y-%m-%d'),
            'type_filter': 'Category',
            'category': str(self.category1.id),
            'search_code': '',
            'search_term': '',
            'location': ''
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'stockledger/_stockledger_table.html')
        self.assertContains(response, 'ITEM001') # Should contain items from category1
        self.assertNotContains(response, 'ITEM002')

    def test_stock_ledger_table_partial_view_invalid_dates(self):
        response = self.client.get(reverse('stockledger_table'), {
            'from_date': (date.today() + timedelta(days=1)).strftime('%Y-%m-%d'), # From date after To date
            'to_date': date.today().strftime('%Y-%m-%d'),
            'type_filter': 'Category',
            'category': str(self.category1.id),
            'search_code': '',
            'search_term': '',
            'location': ''
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'stockledger/_stockledger_table.html')
        self.assertContains(response, 'No data to display !') # Should be empty due to form errors
        # Check for message, though HTMX might not render it directly on swap.
        # This usually relies on custom JS to read HX-Trigger-After-Swap headers for messages.

    def test_stock_ledger_details_view(self):
        item = ItemMaster.objects.get(id=1)
        from_date_param = '01-01-2024'
        to_date_param = '31-01-2024'
        response = self.client.get(reverse('stockledger_details', kwargs={'pk': item.pk}), {
            'FD': from_date_param,
            'TD': to_date_param
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'stockledger/details.html')
        self.assertContains(response, item.item_code)
        self.assertContains(response, from_date_param)
        self.assertContains(response, to_date_param)

    def test_stock_ledger_details_view_not_found(self):
        response = self.client.get(reverse('stockledger_details', kwargs={'pk': 999}))
        self.assertEqual(response.status_code, 404)

    def test_stock_ledger_file_download_image(self):
        item = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('stockledger_download_file', kwargs={'pk': item.pk, 'file_type': 'image'}))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/png')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_image.png"')
        self.assertEqual(response.content, b'dummy_image_data')

    def test_stock_ledger_file_download_spec(self):
        item = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('stockledger_download_file', kwargs={'pk': item.pk, 'file_type': 'spec'}))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_spec.pdf"')
        self.assertEqual(response.content, b'dummy_spec_data')

    def test_stock_ledger_file_download_invalid_type(self):
        item = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('stockledger_download_file', kwargs={'pk': item.pk, 'file_type': 'invalid'}))
        self.assertEqual(response.status_code, 404)

    def test_stock_ledger_file_download_item_not_found(self):
        response = self.client.get(reverse('stockledger_download_file', kwargs={'pk': 999, 'file_type': 'image'}))
        self.assertEqual(response.status_code, 404)

    def test_dynamic_dropdown_category(self):
        response = self.client.get(reverse('stockledger_dynamic_options'), {'field_name': 'category'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="">Select</option>')
        self.assertContains(response, f'<option value="{self.category1.id}">{self.category1}</option>')
        self.assertContains(response, f'<option value="{self.category2.id}">{self.category2}</option>')

    def test_dynamic_dropdown_location(self):
        response = self.client.get(reverse('stockledger_dynamic_options'), {'field_name': 'location'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="">Select</option>')
        self.assertContains(response, f'<option value="{self.location1.id}">{self.location1}</option>')
        self.assertContains(response, f'<option value="{self.location2.id}">{self.location2}</option>')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The provided templates already integrate HTMX for swapping table content based on form submissions and dropdown changes. Alpine.js is used to manage the visibility of search fields dynamically (`showCategory`, `showLocation`, `showSearchTerm`) mirroring the ASP.NET `Visible = true/false` logic.

**Key HTMX/Alpine.js points:**

*   **Main Form Submission:** The entire filter form (`stockLedgerFilterForm`) uses `hx-get="{% url 'stockledger_table' %}"` and `hx-target="#stockLedgerTable-container"` to fetch and replace the data table dynamically when the "Search" button is clicked.
*   **Dropdown Auto-PostBack:** `hx-trigger="change"` is added to the `type_filter`, `category`, `search_code`, and `location` dropdowns. This makes them behave like ASP.NET's `AutoPostBack="True"`, triggering a refresh of the table.
*   **Loading Indicator:** `hx-indicator="#loadingIndicator"` on the form will show a loading spinner while the HTMX request is in progress.
*   **DataTables Reinitialization:** The `_stockledger_table.html` partial includes a JavaScript snippet that safely destroys and reinitializes the DataTables instance every time the partial is loaded, ensuring proper functionality after HTMX swaps the content.
*   **Alpine.js for UI Logic:** The `x-data` attribute on the main container with `typeFilter` and `searchCode` variables manages the `x-show` directives on the category, location, and search term fields, making them appear/disappear based on dropdown selections. This replaces the complex `DrpType_SelectedIndexChanged` and `DrpSearchCode_SelectedIndexChanged` C# logic.
*   **Messages:** Django's `messages` framework is used for server-side validation messages. These will be rendered in the main `list.html` and persist across HTMX requests unless specifically cleared or handled client-side with a custom message display system using HTMX `HX-Trigger-After-Swap` headers.

---

## Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Stock Ledger to Django. By automating the conversion of database interactions, form handling, and dynamic UI elements, you can achieve a modern, maintainable, and highly performant application. Remember to replace placeholder values with your specific database details and financial year/company ID logic where necessary. Regular testing throughout the migration process will ensure accuracy and functionality.