This ASP.NET application provides a very minimal setup for a `Report` page, indicating a placeholder or an entry point within an existing ERP system. The provided code does not contain explicit database interactions, UI controls beyond content placeholders, or business logic. This scenario is common in legacy systems where the core functionality might be wrapped in external assemblies or older components.

For modernization, we will infer a standard `Report` entity that would typically exist in an ERP's inventory module. Our Django modernization plan will focus on creating a robust, maintainable, and highly interactive (using HTMX and Alpine.js) solution for managing these reports.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Given the absence of explicit database schema information in the provided ASP.NET code, we will infer a common table structure for a reporting module within an ERP system. We assume a table named `tblReport` which stores information about various reports.

-   **Table Name:** `tblReport`
-   **Columns Inferred:**
    *   `ReportID` (Primary Key, Integer)
    *   `ReportName` (String, e.g., "Daily Sales Report")
    *   `ReportType` (String, e.g., "Financial", "Inventory", "Production")
    *   `Description` (Text, optional details about the report)
    *   `IsActive` (Boolean, to enable/disable reports)
    *   `CreatedAt` (DateTime, timestamp of creation)
    *   `UpdatedAt` (DateTime, timestamp of last update)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Since the provided ASP.NET code-behind is empty, no explicit CRUD operations are defined. We will assume the standard set of CRUD (Create, Read, Update, Delete) operations are required for managing report definitions in the ERP system.

-   **Create:** Ability to add new report definitions.
-   **Read:** Ability to view a list of all reports and individual report details. This will be the primary functionality expected from a "Report" page.
-   **Update:** Ability to modify existing report definitions.
-   **Delete:** Ability to remove report definitions.
-   **Validation Logic:** Basic field validations (e.g., `ReportName` is required, `ReportType` is from a predefined list) will be implemented in Django Forms.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET `.aspx` file only contains content placeholders and a master page reference. No specific UI controls like `GridView`, `TextBox`, or `Button` are present. Based on the inferred backend functionality and modern web best practices, we will design the following Django UI components:

-   **List View:** A table displaying all `Report` records. This will leverage **DataTables** for client-side searching, sorting, and pagination.
-   **Add/Edit Form:** A form for creating new reports or editing existing ones. This will be loaded dynamically into a modal using **HTMX**.
-   **Delete Confirmation:** A simple confirmation dialog for deleting reports, also loaded into a modal via **HTMX**.
-   **Dynamic Interactions:** All form submissions (create, update, delete) will be handled via **HTMX** to prevent full page reloads, providing a smooth user experience.
-   **UI State Management:** **Alpine.js** will be used to manage the visibility of the modal and any other minor client-side UI states.
-   **Styling:** **Tailwind CSS** will be used for all component styling, ensuring a clean and modern look.

## Step 4: Generate Django Code

We will create a new Django application named `inventory` (inferring from `Module_Inventory_Reports_Report`). Within this app, we will define the models, forms, views, templates, and URLs related to the `Report` entity.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `Report` model will represent the `tblReport` table. We will map the inferred columns to appropriate Django field types. Business logic (e.g., default values, validation rules beyond simple field types, or methods related to report generation/execution if they were present in the legacy system) would be placed here.

```python
# inventory/models.py
from django.db import models
from django.utils import timezone

class Report(models.Model):
    # Assuming 'ReportID' is the primary key and Django's default 'id' maps to it.
    # If 'ReportID' is a separate unique identifier, it would be defined as:
    # report_id = models.IntegerField(db_column='ReportID', primary_key=True)
    # For now, we'll use Django's default 'id' and assume ReportID is handled implicitly or via another field like report_code.

    report_name = models.CharField(db_column='ReportName', max_length=255, verbose_name='Report Name')
    report_type = models.CharField(db_column='ReportType', max_length=100, verbose_name='Report Type')
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name='Description')
    is_active = models.BooleanField(db_column='IsActive', default=True, verbose_name='Is Active')
    created_at = models.DateTimeField(db_column='CreatedAt', auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(db_column='UpdatedAt', auto_now=True, verbose_name='Updated At')

    class Meta:
        managed = False  # Set to True if Django should manage the table, False if it's an existing table
        db_table = 'tblReport'
        verbose_name = 'Report'
        verbose_name_plural = 'Reports'
        ordering = ['report_name'] # Default ordering

    def __str__(self):
        return self.report_name

    # Business logic methods related to Report functionality (e.g., generating report)
    def generate_report_data(self, filters=None):
        """
        Placeholder for method that would fetch or generate report data.
        In a real ERP, this might involve complex queries or integrations.
        """
        print(f"Generating data for report: {self.report_name} (Type: {self.report_type}) with filters: {filters}")
        # Example: Return dummy data for illustration
        return [
            {'item': 'Product A', 'quantity': 100, 'sales': 1000.00},
            {'item': 'Product B', 'quantity': 50, 'sales': 750.00},
        ]

    def get_status_display(self):
        """
        Returns a user-friendly string for the active status.
        """
        return "Active" if self.is_active else "Inactive"

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` for the `Report` model will be created. It will include all necessary fields for user input and apply Tailwind CSS classes via widgets for proper styling.

```python
# inventory/forms.py
from django import forms
from .models import Report

class ReportForm(forms.ModelForm):
    class Meta:
        model = Report
        fields = ['report_name', 'report_type', 'description', 'is_active']
        widgets = {
            'report_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'report_type': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        labels = {
            'report_name': 'Report Name',
            'report_type': 'Report Type',
            'description': 'Description',
            'is_active': 'Is Active',
        }

    def clean_report_name(self):
        report_name = self.cleaned_data.get('report_name')
        if not report_name:
            raise forms.ValidationError("Report Name is required.")
        # Example of custom validation: Check for uniqueness (case-insensitive)
        existing_reports = Report.objects.filter(report_name__iexact=report_name)
        if self.instance.pk: # If it's an update, exclude the current instance
            existing_reports = existing_reports.exclude(pk=self.instance.pk)
        if existing_reports.exists():
            raise forms.ValidationError(f"A report with the name '{report_name}' already exists.")
        return report_name

    def clean_report_type(self):
        report_type = self.cleaned_data.get('report_type')
        if not report_type:
            raise forms.ValidationError("Report Type is required.")
        # Example of validation against a predefined list
        allowed_types = ['Financial', 'Inventory', 'Sales', 'Production', 'Logistics', 'Custom']
        if report_type not in allowed_types:
            raise forms.ValidationError(f"Invalid Report Type. Must be one of: {', '.join(allowed_types)}")
        return report_type

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We will define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the `Report` model. Additionally, a `ReportTablePartialView` will be added to serve the DataTables content via HTMX, keeping the main `ListView` clean. Views will adhere to the "thin view" principle, with business logic residing in the `Report` model.

```python
# inventory/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Report
from .forms import ReportForm

class ReportListView(ListView):
    model = Report
    template_name = 'inventory/report/list.html'
    context_object_name = 'reports' # Plural for the context variable

class ReportTablePartialView(ListView):
    model = Report
    template_name = 'inventory/report/_report_table.html' # This is a partial template
    context_object_name = 'reports'
    # No need for specific context beyond the objects themselves,
    # as this is just rendering the table rows.

class ReportCreateView(CreateView):
    model = Report
    form_class = ReportForm
    template_name = 'inventory/report/_report_form.html' # This is a partial template for HTMX
    success_url = reverse_lazy('report_list') # Not strictly used with HTMX hx-swap="none" + HX-Trigger

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Report added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, send a 204 No Content and a trigger to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReportList' # Custom HTMX trigger
                }
            )
        return response # Fallback for non-HTMX requests

class ReportUpdateView(UpdateView):
    model = Report
    form_class = ReportForm
    template_name = 'inventory/report/_report_form.html' # Partial template
    context_object_name = 'report' # Context for the specific object being edited
    success_url = reverse_lazy('report_list') # Not strictly used with HTMX hx-swap="none" + HX-Trigger

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Report updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReportList'
                }
            )
        return response

class ReportDeleteView(DeleteView):
    model = Report
    template_name = 'inventory/report/_report_confirm_delete.html' # Partial template
    context_object_name = 'report' # Context for the specific object being deleted
    success_url = reverse_lazy('report_list') # Not strictly used with HTMX hx-swap="none" + HX-Trigger

    def delete(self, request, *args, **kwargs):
        report_name = self.get_object().report_name # Get name before deletion for message
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Report "{report_name}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReportList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will follow DRY principles by using partials for reusable components like forms and the table body. All main templates will extend `core/base.html` to inherit the common layout and CDN links. HTMX attributes will drive dynamic interactions, and Alpine.js will manage modal visibility.

**`inventory/report/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Reports Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'report_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Report
        </button>
    </div>
    
    <div id="reportTable-container"
         hx-trigger="load, refreshReportList from:body"
         hx-get="{% url 'report_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md p-4">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading reports...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .is-active from me
            then remove children from #modalContent"
         x-data="{ showModal: false }"
         x-show="showModal"
         @refreshreportlist.window="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 transform transition-transform duration-300 scale-95"
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             @htmx:afteronLoad="document.querySelector('#modal')._x_dataStack[0].showModal = true">
            <!-- HTMX content will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables and JQuery should be included in base.html if not already there -->
<!-- Alpine.js is linked in base.html too -->
<script>
    // Alpine.js is already initialized globally via CDN in base.html.
    // Any specific Alpine.js components would be defined within x-data attributes.

    // Intercept htmx:afterSwap to reinitialize DataTables when the table content changes
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'reportTable-container') {
            $('#reportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTables instance before creating a new one
                "responsive": true, // Make table responsive
                "autoWidth": false, // Disable auto-width to allow full Tailwind control
                "dom": '<"flex flex-col md:flex-row justify-between items-center mb-4"lf><"block w-full overflow-x-auto"t><"flex flex-col md:flex-row justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
        }
    });

    // Handle messages using Alpine.js or simple JS
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        const messages = document.querySelectorAll('[data-alert-type]');
        messages.forEach(msg => {
            setTimeout(() => {
                msg.remove();
            }, 3000); // Remove message after 3 seconds
        });
    });
</script>
{% endblock %}
```

**`inventory/report/_report_table.html` (Partial for HTMX)**

```html
<div class="overflow-x-auto">
    <table id="reportTable" class="min-w-full bg-white border border-gray-200 rounded-lg shadow-sm">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Report Name</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Report Type</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created At</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for report in reports %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ report.report_name }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ report.report_type }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if report.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ report.get_status_display }}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ report.created_at|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'report_edit' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'report_delete' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500">No reports found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- This script block will be executed each time HTMX swaps this content -->
<script>
    // DataTables reinitialization handled by the parent list.html listener
</script>
```

**`inventory/report/_report_form.html` (Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Report
    </h3>
    <form hx-post="{{ request.path }}" 
          hx-swap="none" 
          hx-indicator="#form-spinner"
          hx-on::after.response="if(event.detail.xhr.status === 204) {
            document.querySelector('#modal')._x_dataStack[0].showModal = false; 
            // Re-hide modal after successful HTMX form submission
          } else { 
            // If validation errors, swap the new form content back into modalContent
            htmx.swap('innerHTML', event.detail.xhr.responseText, document.querySelector('#modalContent'));
          }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="{% if field.field.widget.input_type == 'checkbox' %}flex items-center{% else %}mb-4{% endif %}">
                {% if field.field.widget.input_type == 'checkbox' %}
                    {{ field }}
                    <label for="{{ field.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
                        {{ field.label }}
                    </label>
                {% else %}
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ field.label }}
                    </label>
                    {{ field }}
                {% endif %}
                
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-1 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click document.querySelector('#modal')._x_dataStack[0].showModal = false
                   then remove children from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
                <i id="form-spinner" class="fas fa-spinner fa-spin ml-2 htmx-indicator"></i>
            </button>
        </div>
    </form>
</div>
```

**`inventory/report/_report_confirm_delete.html` (Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">
        Are you sure you want to delete the report "<strong class="font-medium text-red-600">{{ report.report_name }}</strong>"? This action cannot be undone.
    </p>
    <form hx-post="{% url 'report_delete' report.pk %}" 
          hx-swap="none" 
          hx-indicator="#delete-spinner"
          hx-on::after.response="if(event.detail.xhr.status === 204) {
            document.querySelector('#modal')._x_dataStack[0].showModal = false; 
            // Re-hide modal after successful HTMX delete
          }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click document.querySelector('#modal')._x_dataStack[0].showModal = false
                   then remove children from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
                <i id="delete-spinner" class="fas fa-spinner fa-spin ml-2 htmx-indicator"></i>
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns will be defined within the `inventory` app's `urls.py` file, routing requests to the appropriate CBVs and enabling dynamic HTMX interactions.

```python
# inventory/urls.py
from django.urls import path
from .views import (
    ReportListView, 
    ReportTablePartialView, 
    ReportCreateView, 
    ReportUpdateView, 
    ReportDeleteView
)

urlpatterns = [
    # Main list view (initial page load)
    path('reports/', ReportListView.as_view(), name='report_list'),
    
    # HTMX endpoint to fetch the table content
    path('reports/table/', ReportTablePartialView.as_view(), name='report_table'),

    # HTMX endpoint to get the add form (renders partial)
    path('reports/add/', ReportCreateView.as_view(), name='report_add'),
    
    # HTMX endpoint to get the edit form (renders partial) and handle submission
    path('reports/edit/<int:pk>/', ReportUpdateView.as_view(), name='report_edit'),
    
    # HTMX endpoint to get the delete confirmation (renders partial) and handle submission
    path('reports/delete/<int:pk>/', ReportDeleteView.as_view(), name='report_delete'),
]

```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `Report` model and integration tests for all view functions will ensure the application's correctness and reliability. This also helps in verifying the HTMX interaction logic.

```python
# inventory/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import Report
import json

class ReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.report1 = Report.objects.create(
            report_name='Daily Sales Report',
            report_type='Sales',
            description='Summary of daily sales activities.',
            is_active=True,
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        cls.report2 = Report.objects.create(
            report_name='Monthly Inventory Review',
            report_type='Inventory',
            description='Detailed review of stock levels.',
            is_active=False,
            created_at=timezone.now(),
            updated_at=timezone.now()
        )

    def test_report_creation(self):
        self.assertEqual(Report.objects.count(), 2)
        self.assertEqual(self.report1.report_name, 'Daily Sales Report')
        self.assertEqual(self.report1.report_type, 'Sales')
        self.assertTrue(self.report1.is_active)
        self.assertEqual(self.report2.report_name, 'Monthly Inventory Review')
        self.assertFalse(self.report2.is_active)

    def test_report_str_method(self):
        self.assertEqual(str(self.report1), 'Daily Sales Report')

    def test_field_labels(self):
        report = Report.objects.get(pk=self.report1.pk)
        field_report_name = report._meta.get_field('report_name').verbose_name
        field_report_type = report._meta.get_field('report_type').verbose_name
        field_is_active = report._meta.get_field('is_active').verbose_name
        self.assertEqual(field_report_name, 'Report Name')
        self.assertEqual(field_report_type, 'Report Type')
        self.assertEqual(field_is_active, 'Is Active')

    def test_get_status_display_method(self):
        self.assertEqual(self.report1.get_status_display(), 'Active')
        self.assertEqual(self.report2.get_status_display(), 'Inactive')
        
    def test_generate_report_data_method(self):
        data = self.report1.generate_report_data()
        self.assertIsInstance(data, list)
        self.assertGreater(len(data), 0)
        self.assertIn('item', data[0])
        self.assertIn('quantity', data[0])
        self.assertIn('sales', data[0])

class ReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.report1 = Report.objects.create(
            report_name='Annual Financial Report',
            report_type='Financial',
            description='Yearly financial summary.',
            is_active=True,
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
        cls.report2 = Report.objects.create(
            report_name='Weekly Production Report',
            report_type='Production',
            description='Summary of weekly production.',
            is_active=False,
            created_at=timezone.now(),
            updated_at=timezone.now()
        )
    
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('report_list')
        self.add_url = reverse('report_add')
        self.edit_url = reverse('report_edit', args=[self.report1.pk])
        self.delete_url = reverse('report_delete', args=[self.report1.pk])
        self.table_url = reverse('report_table')

    def test_list_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/report/list.html')
        self.assertIn('reports', response.context)
        self.assertQuerySetEqual(response.context['reports'], [self.report1, self.report2], ordered=False)
        self.assertContains(response, 'Annual Financial Report')
        self.assertContains(response, 'Weekly Production Report')
        self.assertContains(response, 'Add New Report') # Check for the add button

    def test_report_table_partial_view_get(self):
        response = self.client.get(self.table_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/report/_report_table.html')
        self.assertIn('reports', response.context)
        self.assertContains(response, 'Annual Financial Report')
        self.assertContains(response, 'Weekly Production Report')
        self.assertContains(response, 'id="reportTable"') # Check for the table element

    def test_create_view_get_htmx(self):
        response = self.client.get(self.add_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/report/_report_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Report')

    def test_create_view_post_success_htmx(self):
        data = {
            'report_name': 'New Product Report',
            'report_type': 'Inventory',
            'description': 'Report on new product launches.',
            'is_active': 'on', # Checkbox value for 'on'
        }
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshReportList')
        self.assertTrue(Report.objects.filter(report_name='New Product Report').exists())
        self.assertEqual(Report.objects.count(), 3) # Two existing + one new

    def test_create_view_post_invalid_htmx(self):
        data = { # Missing report_type
            'report_name': 'Another New Report',
            'description': 'Test description.',
            'is_active': 'on',
        }
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should return form with errors
        self.assertTemplateUsed(response, 'inventory/report/_report_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Report Type is required.')
        self.assertEqual(Report.objects.count(), 2) # No new report created

    def test_update_view_get_htmx(self):
        response = self.client.get(self.edit_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/report/_report_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Report')
        self.assertContains(response, self.report1.report_name)

    def test_update_view_post_success_htmx(self):
        data = {
            'report_name': 'Updated Financial Report',
            'report_type': 'Financial',
            'description': 'Updated description.',
            'is_active': 'off',
        }
        response = self.client.post(self.edit_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshReportList')
        self.report1.refresh_from_db()
        self.assertEqual(self.report1.report_name, 'Updated Financial Report')
        self.assertFalse(self.report1.is_active)

    def test_update_view_post_invalid_htmx(self):
        # Attempt to change name to an existing one (report2's name)
        data = {
            'report_name': self.report2.report_name,
            'report_type': 'Financial',
            'description': 'Conflict test.',
            'is_active': 'on',
        }
        response = self.client.post(self.edit_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/report/_report_form.html')
        self.assertContains(response, 'A report with the name &#x27;Weekly Production Report&#x27; already exists.')
        self.report1.refresh_from_db()
        self.assertNotEqual(self.report1.report_name, self.report2.report_name) # Ensure it wasn't updated

    def test_delete_view_get_htmx(self):
        response = self.client.get(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/report/_report_confirm_delete.html')
        self.assertIn('report', response.context)
        self.assertContains(response, f'Are you sure you want to delete the report "{self.report1.report_name}"?')

    def test_delete_view_post_success_htmx(self):
        report_id_to_delete = self.report1.pk
        response = self.client.post(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshReportList')
        self.assertFalse(Report.objects.filter(pk=report_id_to_delete).exists())
        self.assertEqual(Report.objects.count(), 1) # Only report2 should remain

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

As demonstrated in the templates and views, HTMX and Alpine.js are deeply integrated:

-   **HTMX for Dynamic Content:**
    *   The `list.html` uses `hx-get="{% url 'report_table' %}"` and `hx-trigger="load, refreshReportList from:body"` on the `#reportTable-container` to load the table content dynamically upon page load or when the `refreshReportList` custom event is triggered (after a successful CRUD operation).
    *   "Add New Report", "Edit", and "Delete" buttons use `hx-get` to fetch their respective forms (`_report_form.html` or `_report_confirm_delete.html`) and load them into `#modalContent`.
    *   Form submissions (POST requests from `_report_form.html` and `_report_confirm_delete.html`) use `hx-post` and `hx-swap="none"`. Upon successful completion (status 204), a `HX-Trigger: refreshReportList` header is sent, which signals the main list view to reload its table.
    *   HTMX indicators (`htmx-indicator` class and `#form-spinner`, `#delete-spinner` IDs) provide visual feedback during AJAX requests.

-   **Alpine.js for UI State:**
    *   The `#modal` element uses `x-data="{ showModal: false }"` and `x-show="showModal"` to control its visibility.
    *   The "on click" events of action buttons in `list.html` and "Cancel" buttons in form partials use Alpine.js (via `_`) to toggle `showModal`.
    *   The `htmx:afteronLoad` event on `#modalContent` ensures that `showModal` is set to true *after* HTMX has successfully loaded content into the modal, ensuring smooth transition.
    *   A listener for `refreshreportlist.window` (the custom HTMX trigger) on the modal automatically sets `showModal` to `false` when a list refresh is triggered, effectively closing the modal after a successful submission.

-   **DataTables for List Views:**
    *   The `_report_table.html` contains the structure for the `DataTables` table (`id="reportTable"`).
    *   A JavaScript listener on `document.body` for `htmx:afterSwap` event specifically checks if the `reportTable-container` was updated. If so, it re-initializes `DataTables` on the `#reportTable` element, ensuring sorting, searching, and pagination work correctly for newly loaded data. `destroy: true` is crucial for re-initialization.

-   **No Full Page Reloads:** All CRUD operations and data list updates are handled dynamically via HTMX, providing a single-page application (SPA)-like experience without the complexity of a full JavaScript framework.

## Final Notes

This comprehensive plan provides a clear, step-by-step approach to modernizing the legacy ASP.NET Report page into a robust Django application. By focusing on the `Report` entity and following the prescribed architecture (Fat Model, Thin View, HTMX, Alpine.js, DataTables), the resulting solution will be highly maintainable, scalable, and user-friendly, directly addressing the business need for efficient report management within the ERP system. The use of automation-friendly instructions makes this plan suitable for execution through conversational AI guidance, minimizing manual development effort.