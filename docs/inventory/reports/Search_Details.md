## ASP.NET to Django Conversion Script: Inventory Search Details

This document outlines a comprehensive plan to modernize the provided ASP.NET Search_Details.aspx and its C# code-behind to a robust Django 5.0+ application. Our approach leverages AI-assisted automation, focusing on Django's "Fat Model, Thin View" architecture, HTMX for dynamic front-end interactions, Alpine.js for UI state, and DataTables for powerful data presentation, all styled with Tailwind CSS.

### Business Value Proposition

Migrating this ASP.NET reporting tool to Django will bring significant business advantages:

*   **Enhanced Performance:** Django's optimized ORM and Python's efficiency, combined with HTMX's partial page updates, will lead to a faster and more responsive user experience, especially for complex reports.
*   **Reduced Maintenance Costs:** Moving from legacy ASP.NET Web Forms to a modern, open-source framework like Django significantly lowers the cost of maintaining, updating, and extending the application.
*   **Improved Scalability:** Django is inherently scalable, making it easier to handle increased data volumes and user traffic as your business grows without major re-architecture.
*   **Simplified Development:** Django's "batteries-included" philosophy and strong conventions will streamline future feature development, allowing your team to deliver new capabilities faster.
*   **Modern User Experience:** The combination of HTMX and Alpine.js provides a dynamic, single-page application (SPA)-like feel without the complexity of traditional JavaScript frameworks, improving user satisfaction.
*   **Future-Proofing:** Transitioning to a widely adopted, actively maintained framework like Django protects your investment, ensures access to a large talent pool, and integrates seamlessly with modern cloud infrastructure.
*   **Automation-Ready:** This plan focuses on systematic conversion, laying the groundwork for AI-powered migration tools to automate significant portions of the code translation, reducing manual effort and potential errors.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code dynamically queries two primary SQL Server views: `View_PO_PR_SPR_GIN` and `View_PO_PR_SPR_GSN`. It also references `View_PO_PR_SPR_Item` and `AccHead`. The `sp_columns` stored procedure is used to fetch column names for dynamic display. Date fields are stored as `varchar` and undergo complex string manipulation for formatting in SQL.

**Identified Database Views/Tables and Representative Columns:**

*   **Primary Data Sources:**
    *   `View_PO_PR_SPR_GIN`
    *   `View_PO_PR_SPR_GSN`
    *   `View_PO_PR_SPR_Item` (used as a fallback in certain `GetData` scenarios)
*   **Lookup Table:**
    *   `AccHead` (for account head symbol lookup)

*   **Common Columns (from `chkFields` list):**
    *   `SrNo` (Calculated, not a physical column)
    *   `ItemCode` (string)
    *   `Description` (string)
    *   `UOM` (string)
    *   `StockQty` (decimal/numeric)
    *   `PRNo` (string), `PRDate` (varchar), `PRQty` (decimal/numeric)
    *   `SPRNo` (string), `SPRDate` (varchar), `SPRQty` (decimal/numeric)
    *   `PONo` (string), `Date` (PO Date, varchar), `WONo` (string), `Qty` (PO Qty, decimal/numeric)
    *   `Rate` (decimal), `Discount` (decimal)
    *   `SupplierName` (string)
    *   `DelDate` (varchar)
    *   `Authorized` (string), `AuthorizedBy` (string), `AuthorizeDate` (varchar), `AuthorizeTime` (varchar)
    *   `GINNo` (string), `GINDate` (varchar), `ChallanNo` (string), `ChallanDate` (varchar), `GateEntryNo` (string), `ModeofTransport` (string), `VehicleNo` (string), `ChallanQty` (decimal), `GINQty` (decimal)
    *   `GRRNo` (string), `GRRDate` (varchar), `GRRQty` (decimal)
    *   `GQNNo` (string), `GQNDate` (varchar), `AcceptedQty` (decimal), `RejectedQty` (decimal)
    *   `ACHead` (string)
    *   `GSNNo` (string), `GSNDate` (varchar), `GSNQty` (decimal) - *Specific to GSN view*
    *   `CompId` (int) - filter parameter
    *   `Code` (ItemCode, filter parameter)
    *   `AHId` (Account Head ID, filter parameter)

**Conclusion:** Due to the dynamic nature of column selection and the presence of `varchar` dates requiring complex SQL parsing, we will define two distinct Django models for the primary views, mapping fields as `CharField` for dates. The dynamic column selection and filtering will be handled by a dedicated `ReportService` class to keep views thin.

### Step 2: Identify Backend Functionality

**Analysis:**
The page's core functionality is a sophisticated "Read" (reporting) operation. Users select columns, apply filters via query parameters, and view the results in a grid. There's also an "Export to Excel" feature. No "Create," "Update," or "Delete" operations are performed on the data itself from this page.

*   **Read (Reporting):**
    *   Dynamic column selection via checkboxes (`chkFields`).
    *   Filtering based on `RAd` (flag), `type`, `No`, `FDate`, `TDate`, `SupId`, `Code` (ItemCode), `WONo`, `accval` (Account Head ID).
    *   Displays data in a tabular format (`GridView`).
*   **Export:**
    *   Exports the currently displayed report data to an Excel file.
*   **Navigation:**
    *   `btnCancel` redirects to a `Search.aspx` page (implies this page is a detailed report from a prior search).

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET UI primarily consists of a column selection panel with checkboxes, buttons for "Show" and "Export," and a `GridView` for displaying results. Custom JavaScript was used for `GridView` scrolling, which will be replaced by DataTables.

*   **Column Selection:**
    *   `checkAll` CheckBox: To toggle all column selections.
    *   `chkFields` CheckBoxList: To list and select individual report columns.
    *   `lblGIN`: A label, likely for displaying some report context (e.g., "GIN Report").
*   **Action Buttons:**
    *   `btnSub` (Show): Triggers data retrieval and display.
    *   `btnExport` (Export To Excel): Triggers data export.
    *   `btnCancel` (Cancel): Navigates back.
*   **Data Display:**
    *   `GridView1`: Main data display area. This will be replaced by a modern HTML `<table>` managed by DataTables.
*   **Layout:** ASP.NET Panels (`Panel1`, `Panel2`) for grouping and scrolling. These will translate to simple `div` elements with Tailwind CSS for layout.
*   **Client-side Scripting:** The inline JavaScript for `GridView` scrolling will be replaced by DataTables built-in features. All button actions will use HTMX.

---

### Step 4: Generate Django Code

We will create a new Django app named `inventory_reports`.

#### 4.1 Models (in `inventory_reports/models.py`)

We'll define models for the two main views (`GIN` and `GSN`) and the `AccHead` table. Since date fields are `varchar` in the source, they'll be `CharField` in Django models.

```python
from django.db import models
from django.conf import settings

# Helper for dynamic field choices, if needed, but not mapping to DB table
class ReportFieldChoices:
    """
    A class to hold available report fields and their display names.
    This replaces the dynamic sp_columns call and manual text assignments.
    """
    GIN_FIELDS = {
        'SrNo': 'Sr No', 'ItemCode': 'Item Code', 'Description': 'Description', 'UOM': 'UOM',
        'StockQty': 'Stock Qty', 'PRNo': 'PR No', 'PRDate': 'PR Date', 'PRQty': 'PR Qty',
        'SPRNo': 'SPR No', 'SPRDate': 'SPR Date', 'SPRQty': 'SPR Qty', 'PONo': 'PO NO',
        'Date': 'PO Date', 'WONo': 'WO No', 'Qty': 'PO Qty', 'Rate': 'Rate',
        'Discount': 'Discount', 'SupplierName': 'Supplier Name', 'DelDate': 'Del. Date',
        'Authorized': 'Authorized', 'AuthorizedBy': 'Authorized By', 'AuthorizeDate': 'Authorized Date',
        'AuthorizeTime': 'Authorized Time', 'GINNo': 'GIN No', 'GINDate': 'GIN. Date',
        'ChallanNo': 'Challan No', 'ChallanDate': 'Challan Date', 'GateEntryNo': 'Gate Entry No',
        'ModeofTransport': 'Mode of Transport', 'VehicleNo': 'Vehicle No', 'ChallanQty': 'Challan Qty',
        'GINQty': 'GIN Qty', 'GRRNo': 'GRR No', 'GRRDate': 'GRR Date', 'GRRQty': 'GRR Qty',
        'GQNNo': 'GQN No', 'GQNDate': 'GQN Date', 'AcceptedQty': 'Accepted Qty',
        'RejectedQty': 'Rejected Qty', 'ACHead': 'Ac Head',
        # Filter-only fields, not usually displayed but might be present in view
        'CompId': 'Company ID', 'AHId': 'Account Head ID', 'Code': 'Item Code (Internal)'
    }
    GSN_FIELDS = {
        'SrNo': 'Sr No', 'ItemCode': 'Item Code', 'Description': 'Description', 'UOM': 'UOM',
        'StockQty': 'Stock Qty', 'PRNo': 'PR No', 'PRDate': 'PR Date', 'PRQty': 'PR Qty',
        'SPRNo': 'SPR No', 'SPRDate': 'SPR Date', 'SPRQty': 'SPR Qty', 'PONo': 'PO NO',
        'Date': 'PO Date', 'WONo': 'WO No', 'Qty': 'PO Qty', 'Rate': 'Rate',
        'Discount': 'Discount', 'SupplierName': 'Supplier Name', 'DelDate': 'Del. Date',
        'Authorized': 'Authorized', 'AuthorizedBy': 'Authorized By', 'AuthorizeDate': 'Authorized Date',
        'AuthorizeTime': 'Authorized Time', 'GINNo': 'GIN No', 'GINDate': 'GIN. Date',
        'ChallanNo': 'Challan No', 'ChallanDate': 'Challan Date', 'GateEntryNo': 'Gate Entry No',
        'ModeofTransport': 'Mode of Transport', 'VehicleNo': 'Vehicle No', 'ChallanQty': 'Challan Qty',
        'GINQty': 'GIN Qty', 'ACHead': 'Ac Head', # GSN specific fields
        'GSNNo': 'GSN No', 'GSNDate': 'GSN Date', 'GSNQty': 'GSN Qty',
        # Filter-only fields, not usually displayed but might be present in view
        'CompId': 'Company ID', 'AHId': 'Account Head ID', 'Code': 'Item Code (Internal)'
    }

    # Default selected fields on initial load
    DEFAULT_SELECTED_FIELDS = ['SrNo', 'ItemCode', 'Description', 'UOM', 'StockQty']
    
    @staticmethod
    def get_choices_for_flag(flag: int):
        if flag == 4:
            return ReportFieldChoices.GSN_FIELDS
        else:
            return ReportFieldChoices.GIN_FIELDS

    @staticmethod
    def get_default_selected(flag: int):
        # Specific default fields for GIN vs GSN if different rules apply
        if flag == 4: # GSN report
            return ReportFieldChoices.DEFAULT_SELECTED_FIELDS
        else: # GIN report (or others)
            return ReportFieldChoices.DEFAULT_SELECTED_FIELDS


# Model for View_PO_PR_SPR_GIN
class InventoryGINReport(models.Model):
    # These fields are representative, based on the ASP.NET code.
    # Adjust types and add more fields as per actual database view schema.
    # Using CharField for dates as they are stored as varchar in source.
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=20, blank=True, null=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_date_str = models.CharField(db_column='Date', max_length=20, blank=True, null=True) # Stored as varchar
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    po_qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4, blank=True, null=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    del_date_str = models.CharField(db_column='DelDate', max_length=20, blank=True, null=True) # Stored as varchar
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    gin_date_str = models.CharField(db_column='GINDate', max_length=20, blank=True, null=True) # Stored as varchar
    challan_no = models.CharField(db_column='ChallanNo', max_length=50, blank=True, null=True)
    challan_date_str = models.CharField(db_column='ChallanDate', max_length=20, blank=True, null=True) # Stored as varchar
    grr_no = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    grr_date_str = models.CharField(db_column='GRRDate', max_length=20, blank=True, null=True) # Stored as varchar
    gqn_no = models.CharField(db_column='GQNNo', max_length=50, blank=True, null=True)
    gqn_date_str = models.CharField(db_column='GQNDate', max_length=20, blank=True, null=True) # Stored as varchar
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=4, blank=True, null=True)
    rejected_qty = models.DecimalField(db_column='RejectedQty', max_digits=18, decimal_places=4, blank=True, null=True)
    ac_head = models.CharField(db_column='ACHead', max_length=50, blank=True, null=True)
    # Add other fields as identified from the ASP.NET code (PRNo, SPRNo etc.)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Filter field
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True) # Filter field for AccHead
    item_code_filter = models.CharField(db_column='Code', max_length=50, blank=True, null=True) # ItemCode for filtering

    class Meta:
        managed = False
        db_table = 'View_PO_PR_SPR_GIN'
        verbose_name = 'GIN Report Entry'
        verbose_name_plural = 'GIN Report Entries'

    def __str__(self):
        return f"GIN Report - Item: {self.item_code} - GIN: {self.gin_no}"

    # Business logic methods can be added here if needed, e.g., for date parsing
    def get_parsed_po_date(self):
        try:
            return models.DateField().to_python(self.po_date_str)
        except (ValueError, TypeError):
            return None

# Model for View_PO_PR_SPR_GSN
class InventoryGSNReport(models.Model):
    # Most fields are common, but specific GSN ones are included.
    # Adjust types and add more fields as per actual database view schema.
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=20, blank=True, null=True)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_date_str = models.CharField(db_column='Date', max_length=20, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    po_qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4, blank=True, null=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    del_date_str = models.CharField(db_column='DelDate', max_length=20, blank=True, null=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    gin_date_str = models.CharField(db_column='GINDate', max_length=20, blank=True, null=True)
    challan_no = models.CharField(db_column='ChallanNo', max_length=50, blank=True, null=True)
    challan_date_str = models.CharField(db_column='ChallanDate', max_length=20, blank=True, null=True)
    ac_head = models.CharField(db_column='ACHead', max_length=50, blank=True, null=True)
    gsn_no = models.CharField(db_column='GSNNo', max_length=50, blank=True, null=True)
    gsn_date_str = models.CharField(db_column='GSNDate', max_length=20, blank=True, null=True)
    gsn_qty = models.DecimalField(db_column='GSNQty', max_digits=18, decimal_places=4, blank=True, null=True)
    # Add other common fields here that are present in GSN view and GIN view
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Filter field
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True) # Filter field for AccHead
    item_code_filter = models.CharField(db_column='Code', max_length=50, blank=True, null=True) # ItemCode for filtering

    class Meta:
        managed = False
        db_table = 'View_PO_PR_SPR_GSN'
        verbose_name = 'GSN Report Entry'
        verbose_name_plural = 'GSN Report Entries'

    def __str__(self):
        return f"GSN Report - Item: {self.item_code} - GSN: {self.gsn_no}"

# Model for AccHead (lookup for ACHead symbol)
class AccHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    # Add other fields as per actual AccHead table schema

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol or f"AccHead {self.id}"

```

#### 4.2 Forms (in `inventory_reports/forms.py`)

We need two forms: one for general report filters and one for dynamic column selection.

```python
from django import forms
from .models import ReportFieldChoices, AccHead
from datetime import date

class ReportFilterForm(forms.Form):
    """
    Form to capture report filtering parameters.
    These parameters correspond to Request.QueryString values in ASP.NET.
    """
    RAd_choices = [
        ('0', 'Default Report'), # GIN (flag = 0)
        ('1', 'Report Type 1 (GINDate)'), # flag = 1
        ('2', 'Report Type 2 (GRRDate)'), # flag = 2
        ('3', 'Report Type 3 (GQNDate)'), # flag = 3
        ('4', 'GSN Report (flag = 4)') # GSN view
    ]
    type_choices = [
        ('0', 'Any'), ('1', 'GINNo'), ('2', 'GRRNo'), ('3', 'GQNNo'),
        ('4', 'GSNNo'), ('5', 'PONo')
    ]

    rad = forms.ChoiceField(
        choices=RAd_choices,
        required=False,
        label="Report Type (RAd)",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500',
            'hx-get': '/reports/inventory/search/columns/', # HTMX to update column checkboxes
            'hx-target': '#column-selection-panel',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change'
        })
    )
    report_type = forms.ChoiceField(
        choices=type_choices,
        required=False,
        label="Document Type",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )
    report_no = forms.CharField(
        max_length=50,
        required=False,
        label="Document No.",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )
    from_date = forms.DateField(
        required=False,
        label="From Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
        })
    )
    to_date = forms.DateField(
        required=False,
        label="To Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
        })
    )
    supplier_id = forms.CharField( # Assuming supplier ID can be text or number
        max_length=50,
        required=False,
        label="Supplier ID",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )
    item_code = forms.CharField(
        max_length=50,
        required=False,
        label="Item Code",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )
    wo_no = forms.CharField(
        max_length=50,
        required=False,
        label="WO No",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )
    acc_val = forms.ModelChoiceField(
        queryset=AccHead.objects.all(), # Assuming AccHead table is populated
        to_field_name='id', # Map to 'Id' column in AccHead
        required=False,
        empty_label="-- Select Account Head --",
        label="Account Head",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'})
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', "To Date cannot be before From Date.")
        return cleaned_data

class ReportColumnSelectionForm(forms.Form):
    """
    Form to select columns for the report grid dynamically.
    The choices are populated based on the `rad` (flag) parameter.
    """
    selected_columns = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-checkbox h-4 w-4 text-blue-600'
        }),
        required=False,
        label="" # Label handled by template logic
    )
    
    def __init__(self, *args, **kwargs):
        report_flag = kwargs.pop('report_flag', 0)
        super().__init__(*args, **kwargs)
        
        column_choices = [(k, v) for k, v in ReportFieldChoices.get_choices_for_flag(int(report_flag)).items()]
        self.fields['selected_columns'].choices = column_choices
        
        # Set initial selected choices for existing forms or if no data
        if not self.data:
            self.fields['selected_columns'].initial = ReportFieldChoices.get_default_selected(int(report_flag))

    def clean_selected_columns(self):
        selected = self.cleaned_data['selected_columns']
        if not selected:
            raise forms.ValidationError("Please select at least one column to display.")
        return selected

```

#### 4.3 Views (in `inventory_reports/views.py`)

We'll use Class-Based Views (CBVs) for the main report page, and separate `View` classes for HTMX partials. The business logic for filtering and data retrieval will be encapsulated in a `ReportService` class.

```python
from django.views.generic import TemplateView, View
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.shortcuts import render
from django.db.models import F, Value
from django.db.models.functions import RowNumber
from django.db.models.expressions import Window
from django.contrib import messages
import pandas as pd
from datetime import datetime, date

from .forms import ReportFilterForm, ReportColumnSelectionForm
from .models import InventoryGINReport, InventoryGSNReport, AccHead, ReportFieldChoices

# --- Report Service (Fat Model/Business Logic) ---
class ReportService:
    @staticmethod
    def get_report_data(filters, selected_columns_raw, comp_id):
        flag = int(filters.get('rad', 0))
        report_type_filter = filters.get('report_type')
        report_no_filter = filters.get('report_no')
        from_date = filters.get('from_date')
        to_date = filters.get('to_date')
        supplier_id_filter = filters.get('supplier_id')
        item_code_filter = filters.get('item_code')
        wo_no_filter = filters.get('wo_no')
        acc_val_filter = filters.get('acc_val') # This is AccHead ID

        # Determine which model/view to use
        if flag == 4:
            model = InventoryGSNReport
            field_map = ReportFieldChoices.GSN_FIELDS
        else:
            model = InventoryGINReport
            field_map = ReportFieldChoices.GIN_FIELDS

        queryset = model.objects.filter(comp_id=comp_id)

        # Apply filters based on query string parameters
        if report_no_filter:
            if report_type_filter == '1': # GINNo
                queryset = queryset.filter(gin_no__icontains=report_no_filter)
            elif report_type_filter == '2': # GRRNo
                queryset = queryset.filter(grr_no__icontains=report_no_filter)
            elif report_type_filter == '3': # GQNNo
                queryset = queryset.filter(gqn_no__icontains=report_no_filter)
            elif report_type_filter == '4': # GSNNo (GSN specific)
                queryset = queryset.filter(gsn_no__icontains=report_no_filter)
            elif report_type_filter == '5': # PONo
                queryset = queryset.filter(po_no__icontains=report_no_filter)

        if from_date and to_date:
            # Date filtering on varchar fields is tricky.
            # For simplicity, if DB stores YYYY-MM-DD or similar sortable string:
            # Convert python dates to string format matching DB for comparison
            # Example: '2023-01-01' <= '2023-01-31' for date_str
            # If DB date format is truly complex like MM-DD-YYYY or DD-MM-YYYY,
            # direct string comparison might fail. A custom Django DB function
            # or converting to proper date fields in DB is highly recommended.
            # For this example, we assume `YYY-MM-DD` for comparison, or client-side parsing.
            
            # ASP.NET code applies date filter to different fields based on flag:
            # flag 0: And Date (PO Date) between
            # flag 1: And GINDate between
            # flag 2: And GRRDate between
            # flag 3: And GQNDate between
            # flag 4: And GSNDate between

            from_date_str = from_date.strftime('%Y-%m-%d')
            to_date_str = to_date.strftime('%Y-%m-%d')

            if flag == 0:
                queryset = queryset.filter(po_date_str__gte=from_date_str, po_date_str__lte=to_date_str)
            elif flag == 1:
                queryset = queryset.filter(gin_date_str__gte=from_date_str, gin_date_str__lte=to_date_str)
            elif flag == 2:
                queryset = queryset.filter(grr_date_str__gte=from_date_str, grr_date_str__lte=to_date_str)
            elif flag == 3:
                queryset = queryset.filter(gqn_date_str__gte=from_date_str, gqn_date_str__lte=to_date_str)
            elif flag == 4:
                queryset = queryset.filter(gsn_date_str__gte=from_date_str, gsn_date_str__lte=to_date_str)


        if supplier_id_filter:
            queryset = queryset.filter(supplier_name__icontains=supplier_id_filter) # Assuming SupplierId means Name contains
        if item_code_filter:
            queryset = queryset.filter(item_code_filter__icontains=item_code_filter) # 'Code' in ASP.NET code
        if wo_no_filter:
            queryset = queryset.filter(wo_no__icontains=wo_no_filter)
        if acc_val_filter: # Account Head ID to Symbol lookup
            try:
                acc_head_symbol = AccHead.objects.get(id=acc_val_filter).symbol
                queryset = queryset.filter(ac_head__icontains=acc_head_symbol) # Assuming ACHead is symbol
            except AccHead.DoesNotExist:
                pass # No matching acc head, filter won't apply

        # Dynamically select columns and add SrNo
        # Map selected ASP.NET column names to Django model field names
        field_mapping = {
            'SrNo': 'sr_no', # This will be added as annotation
            'ItemCode': 'item_code', 'Description': 'description', 'UOM': 'uom', 'StockQty': 'stock_qty',
            'PRNo': 'pr_no', 'PRDate': 'pr_date_str', 'PRQty': 'pr_qty',
            'SPRNo': 'spr_no', 'SPRDate': 'spr_date_str', 'SPRQty': 'spr_qty',
            'PONo': 'po_no', 'Date': 'po_date_str', 'WONo': 'wo_no', 'Qty': 'po_qty',
            'Rate': 'rate', 'Discount': 'discount', 'SupplierName': 'supplier_name',
            'DelDate': 'del_date_str', 'Authorized': 'authorized', 'AuthorizedBy': 'authorized_by',
            'AuthorizeDate': 'authorize_date_str', 'AuthorizeTime': 'authorize_time_str',
            'GINNo': 'gin_no', 'GINDate': 'gin_date_str', 'ChallanNo': 'challan_no',
            'ChallanDate': 'challan_date_str', 'GateEntryNo': 'gate_entry_no',
            'ModeofTransport': 'mode_of_transport', 'VehicleNo': 'vehicle_no',
            'ChallanQty': 'challan_qty', 'GINQty': 'gin_qty',
            'GRRNo': 'grr_no', 'GRRDate': 'grr_date_str', 'GRRQty': 'grr_qty',
            'GQNNo': 'gqn_no', 'GQNDate': 'gqn_date_str', 'AcceptedQty': 'accepted_qty',
            'RejectedQty': 'rejected_qty', 'AcHead': 'ac_head',
            'GSNNo': 'gsn_no', 'GSNDate': 'gsn_date_str', 'GSNQty': 'gsn_qty',
        }

        # Ensure that only fields available in the current model are selected
        valid_model_fields = [f.name for f in model._meta.get_fields()]
        
        selected_db_fields = []
        # Get actual model field names for selected columns
        for col in selected_columns_raw:
            django_field_name = field_mapping.get(col)
            if django_field_name and django_field_name in valid_model_fields:
                selected_db_fields.append(django_field_name)
        
        # Add SrNo. This calculation will be done in Python after fetching data
        # or as an annotation if ordering is consistent for all selected data.
        # For DataTables, often just an index in the template is enough.
        # If SrNo needs to be part of the dataset for export, we can add it here.
        # For large datasets, RowNumber in SQL is preferred but complex with dynamic queries.
        
        # If no columns selected, return empty queryset
        if not selected_db_fields:
            return []

        # Fetch data as dictionaries
        data = list(queryset.values(*selected_db_fields))

        # Add SrNo and format display for output
        # Use field_map (display names) for headers, not Django field names
        output_data = []
        for i, row in enumerate(data):
            formatted_row = {'SrNo': i + 1} # SrNo is 1-based index
            for selected_col_name in selected_columns_raw:
                if selected_col_name == 'SrNo':
                    continue # Skip as we added it above
                
                django_field_name = field_mapping.get(selected_col_name)
                if django_field_name:
                    value = row.get(django_field_name)
                    # Basic date formatting (example for YYYY-MM-DD to DD-MM-YYYY)
                    if '_date_str' in django_field_name and value:
                        try:
                            # Attempt to parse as YYYY-MM-DD, then format
                            parsed_date = datetime.strptime(value, '%Y-%m-%d').strftime('%d-%m-%Y')
                            formatted_row[selected_col_name] = parsed_date
                        except ValueError:
                            # If format is different, use as-is or try other formats
                            formatted_row[selected_col_name] = value
                    else:
                        formatted_row[selected_col_name] = value
            output_data.append(formatted_row)
        
        return output_data, field_map # Return raw data and mapping for display names

# --- Django Views ---

class ReportSearchView(TemplateView):
    """
    Main view for the inventory search and report page.
    Renders the filter form and column selection, and an empty container for the report table.
    """
    template_name = 'inventory_reports/search/report_search.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Initialize forms from query parameters or defaults
        query_params = self.request.GET
        context['filter_form'] = ReportFilterForm(query_params)
        
        # Determine initial report flag (for column selection)
        initial_flag = int(query_params.get('rad', 0))
        context['column_selection_form'] = ReportColumnSelectionForm(
            query_params, report_flag=initial_flag
        )
        
        return context

class ReportColumnsHTMXView(View):
    """
    HTMX view to dynamically update column selection checkboxes based on report type (rad).
    """
    def get(self, request, *args, **kwargs):
        report_flag = int(request.GET.get('rad', 0))
        form = ReportColumnSelectionForm(report_flag=report_flag)
        return render(request, 'inventory_reports/search/_column_checkboxes.html', {'form': form})

class ReportTableHTMXView(View):
    """
    HTMX view to load and refresh the report data table.
    Fetches data using ReportService and renders the partial table.
    """
    def get(self, request, *args, **kwargs):
        query_params = request.GET
        filter_form = ReportFilterForm(query_params)
        column_selection_form = ReportColumnSelectionForm(
            query_params, report_flag=int(query_params.get('rad', 0))
        )
        
        report_data = []
        column_headers = {}
        
        if filter_form.is_valid() and column_selection_form.is_valid():
            try:
                # Get comp_id from session (as in ASP.NET Session["compid"])
                comp_id = request.session.get('compid', settings.DEFAULT_COMPANY_ID) # Use a default or raise error if missing
                
                selected_columns = column_selection_form.cleaned_data['selected_columns']
                report_data, column_headers_map = ReportService.get_report_data(
                    filter_form.cleaned_data,
                    selected_columns,
                    comp_id
                )
                
                # Prepare headers for display (SrNo + selected columns in order)
                headers_for_display = []
                if 'SrNo' in selected_columns:
                    headers_for_display.append(('SrNo', 'Sr No'))

                for col_name in selected_columns:
                    if col_name != 'SrNo':
                        headers_for_display.append((col_name, column_headers_map.get(col_name, col_name)))

                column_headers = headers_for_display

                if not report_data:
                    messages.warning(request, "No records to display based on your criteria.")
            except Exception as e:
                messages.error(request, f"An error occurred while fetching report data: {e}")
                # Log the exception for debugging
        else:
            messages.error(request, "Invalid filter or column selection. Please correct the errors.")
        
        context = {
            'report_data': report_data,
            'column_headers': column_headers,
            'filter_form': filter_form, # Pass back to retain state in case of errors
            'column_selection_form': column_selection_form
        }
        return render(request, 'inventory_reports/search/_report_table.html', context)


class ReportExportExcelView(View):
    """
    Handles the export of current report data to an Excel file.
    """
    def get(self, request, *args, **kwargs):
        query_params = request.GET
        filter_form = ReportFilterForm(query_params)
        column_selection_form = ReportColumnSelectionForm(
            query_params, report_flag=int(query_params.get('rad', 0))
        )

        if filter_form.is_valid() and column_selection_form.is_valid():
            comp_id = request.session.get('compid', settings.DEFAULT_COMPANY_ID)
            selected_columns = column_selection_form.cleaned_data['selected_columns']
            
            try:
                report_data, column_headers_map = ReportService.get_report_data(
                    filter_form.cleaned_data,
                    selected_columns,
                    comp_id
                )

                if not report_data:
                    messages.warning(request, "No records to export.")
                    # Redirect back or render an error message on the main page
                    return HttpResponse(status=204, headers={'HX-Trigger': 'refreshReportList'}) # Or a client-side alert
                
                # Create DataFrame
                df = pd.DataFrame(report_data)

                # Rename columns for export based on the map
                # The data already uses the display names for keys if SrNo is present
                # Otherwise, ensure we use the display names
                export_columns = [col_name for col_name, _ in column_headers_map.items() if col_name in selected_columns or col_name == 'SrNo']
                
                # Reorder columns to match selected_columns order + SrNo
                final_columns_order = []
                if 'SrNo' in df.columns:
                    final_columns_order.append('SrNo') # Make SrNo first if present

                for col in selected_columns:
                    if col != 'SrNo' and col in df.columns:
                        final_columns_order.append(col)

                df = df[final_columns_order]

                response = HttpResponse(
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    headers={'Content-Disposition': f'attachment; filename="inventory_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.xlsx"'},
                )
                df.to_excel(response, index=False, engine='openpyxl')
                return response

            except Exception as e:
                messages.error(request, f"Error exporting report: {e}")
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshReportList'}) # Indicate failure on UI via HTMX
        else:
            messages.error(request, "Invalid report parameters for export.")
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshReportList'})
```

#### 4.4 Templates

**Directory Structure:** `inventory_reports/templates/inventory_reports/search/`

**`report_search.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-6">Inventory Detailed Report</h2>

    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Filter Report Data</h3>
        <form id="report-filter-form" hx-get="{% url 'inventory_reports:report_table' %}" hx-target="#report-table-container" hx-swap="innerHTML" hx-indicator="#loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                {% for field in filter_form %}
                <div>
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ field.label }}
                    </label>
                    {{ field }}
                    {% if field.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>

            <div id="column-selection-panel" class="border border-gray-200 rounded-md p-4 mb-6">
                <p class="text-lg font-medium text-gray-800 mb-3">Select columns to show in the GridView:</p>
                <!-- Column checkboxes will be loaded here via HTMX -->
                {% include 'inventory_reports/search/_column_checkboxes.html' with form=column_selection_form %}
            </div>

            <div class="flex flex-col sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    Show Report
                </button>
                <button 
                    type="button" 
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
                    hx-get="{% url 'inventory_reports:report_export' %}"
                    hx-params="get"
                    hx-target="body" hx-swap="none">
                    Export To Excel
                </button>
                <a href="{% url 'inventory_reports:report_search' %}" class="inline-flex items-center justify-center bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <div id="loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading Report...</p>
    </div>

    <div id="report-table-container" class="bg-white shadow-lg rounded-lg overflow-x-auto p-6">
        <!-- Report table will be loaded here via HTMX -->
        <p class="text-center text-gray-500">Select filters and click "Show Report" to display data.</p>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportApp', () => ({
            init() {
                // Initial load of the table might be desired,
                // or wait for user to click "Show Report"
                // hx-trigger="load" on #report-table-container if immediate load is desired.
            }
        }));
    });

    // Re-initialize DataTables when HTMX swaps content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'report-table-container') {
            const tableElement = document.getElementById('reportTable');
            if (tableElement && !$.fn.DataTable.isDataTable(tableElement)) {
                $(tableElement).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "scrollX": true, // Enable horizontal scrolling for wide tables
                    "scrollY": "400px", // Fixed height with vertical scrolling
                    "scrollCollapse": true, // Allow vertical scrollbar to collapse
                });
            }
        }
    });

    // Handle messages
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.getResponseHeader('HX-Trigger')) {
            const trigger = JSON.parse(event.detail.xhr.getResponseHeader('HX-Trigger'));
            if (trigger.messages) {
                trigger.messages.forEach(msg => {
                    // Assuming messages are structured as { level: 'success', message: '...' }
                    // You would need a frontend JS to display these
                    console.log(`Message (${msg.level}): ${msg.message}`);
                    // Example: display a toast notification
                    // showToast(msg.message, msg.level);
                });
            }
        }
    });

    // Optionally, if you want "Check All" behavior using Alpine/HTMX:
    document.addEventListener('DOMContentLoaded', () => {
        const checkAllCheckbox = document.getElementById('id_selected_columns_check_all');
        if (checkAllCheckbox) {
            checkAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('input[name="selected_columns"]');
                checkboxes.forEach(checkbox => {
                    if (checkbox.id !== 'id_selected_columns_check_all') {
                        checkbox.checked = this.checked;
                    }
                });
            });
        }
    });
</script>
{% endblock %}
```

**`_column_checkboxes.html` (Partial for Column Selection)**

```html
<div>
    <div class="mb-3">
        <label class="inline-flex items-center">
            <input type="checkbox" id="id_selected_columns_check_all" class="form-checkbox h-4 w-4 text-blue-600 mr-2"
                   _="on change set checked of <input type='checkbox' name='selected_columns'/> to my checked">
            <span class="text-gray-900 font-bold">Check All:</span>
        </label>
    </div>
    
    <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-2">
        {% for field_id, field_name in form.fields.selected_columns.choices %}
        <label class="inline-flex items-center text-sm">
            <input type="checkbox" name="{{ form.selected_columns.name }}" value="{{ field_id }}" 
                   {% if field_id in form.selected_columns.initial %}checked{% endif %}
                   class="{{ form.selected_columns.widget.attrs.class }} mr-2">
            <span class="text-gray-700">{{ field_name }}</span>
        </label>
        {% endfor %}
    </div>
    {% if form.selected_columns.errors %}
        <p class="text-red-500 text-xs mt-1">{{ form.selected_columns.errors|join:", " }}</p>
    {% endif %}
</div>
```

**`_report_table.html` (Partial for Data Table)**

```html
{% if report_data %}
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="reportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                {% for col_id, col_name in column_headers %}
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ col_name }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in report_data %}
            <tr>
                {% for col_id, _ in column_headers %}
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">
                    {{ row|get_item:col_id|default_if_none:"-" }}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<p class="text-center text-gray-500">No data found matching your selection.</p>
{% endif %}

<!-- Custom filter to access dictionary items by key -->
{% load custom_filters %}
```
**`inventory_reports/templatetags/custom_filters.py`**
```python
from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    return dictionary.get(key)
```
*(Remember to create `inventory_reports/templatetags/` directory and an empty `__init__.py` file inside it.)*

#### 4.5 URLs (in `inventory_reports/urls.py`)

```python
from django.urls import path
from .views import ReportSearchView, ReportTableHTMXView, ReportColumnsHTMXView, ReportExportExcelView

app_name = 'inventory_reports'

urlpatterns = [
    path('search/', ReportSearchView.as_view(), name='report_search'),
    path('search/table/', ReportTableHTMXView.as_view(), name='report_table'),
    path('search/columns/', ReportColumnsHTMXView.as_view(), name='report_columns_htmx'),
    path('search/export/', ReportExportExcelView.as_view(), name='report_export'),
]
```
*(Don't forget to include `inventory_reports.urls` in your project's main `urls.py`.)*

#### 4.6 Tests (in `inventory_reports/tests.py`)

Due to the complexity of dynamic reporting and data-dependent filtering from views, comprehensive testing requires careful setup of mock data or a test database.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch, MagicMock
from datetime import date
from io import BytesIO

# Mock settings for DEFAULT_COMPANY_ID if not in actual settings.py
if not hasattr(settings, 'DEFAULT_COMPANY_ID'):
    settings.DEFAULT_COMPANY_ID = 1

# Mock models for testing purposes if you don't have a live DB connection
# For true integration tests, set up a test database with schema and test data.
# For this example, we'll use mocks for simplicity and focus on view logic.

class MockAccHead:
    objects = MagicMock()
    def __init__(self, id, symbol):
        self.id = id
        self.symbol = symbol

class MockReportEntry:
    # A generic mock for report data rows
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __getitem__(self, key): # To allow dictionary-like access for .values()
        return getattr(self, key, None)

    def get(self, key, default=None):
        return getattr(self, key, default)

class MockQuerySet(list):
    def __init__(self, *args, **kwargs):
        super().__init__(*args)
        self._mock_filters = {}

    def filter(self, **kwargs):
        new_qs = MockQuerySet(self)
        for k, v in kwargs.items():
            new_qs._mock_filters[k] = v
        
        # Simulate filtering by checking if items match all current filters
        filtered_items = [
            item for item in self if all(
                (getattr(item, k.replace('__icontains', '').replace('__gte', '').replace('__lte', ''), '') is not None and
                 (str(getattr(item, k.replace('__icontains', '').replace('__gte', '').replace('__lte', ''), '')).lower() == str(v).lower() if '__icontains' not in k else str(getattr(item, k.replace('__icontains', ''), '')).lower().__contains__(str(v).lower()))
                 )
                for k, v in new_qs._mock_filters.items()
            )
        ]
        return MockQuerySet(filtered_items)

    def values(self, *fields):
        result = []
        for item in self:
            row = {}
            for field in fields:
                row[field] = getattr(item, field, None)
            result.append(row)
        return result

    def first(self):
        return self[0] if self else None


# Mocking the ORM classes for tests
class MockInventoryGINReport:
    objects = MockQuerySet()
    _meta = MagicMock()
    _meta.get_fields.return_value = [
        MagicMock(name='item_code'), MagicMock(name='description'), MagicMock(name='comp_id'),
        MagicMock(name='po_date_str'), MagicMock(name='gin_date_str'),
        MagicMock(name='grr_date_str'), MagicMock(name='gqn_date_str'),
        MagicMock(name='supplier_name'), MagicMock(name='item_code_filter'),
        MagicMock(name='wo_no'), MagicMock(name='ac_head'), MagicMock(name='po_no'),
        MagicMock(name='gin_no'), MagicMock(name='grr_no'), MagicMock(name='gqn_no'),
    ]

class MockInventoryGSNReport:
    objects = MockQuerySet()
    _meta = MagicMock()
    _meta.get_fields.return_value = [
        MagicMock(name='item_code'), MagicMock(name='description'), MagicMock(name='comp_id'),
        MagicMock(name='po_date_str'), MagicMock(name='gin_date_str'),
        MagicMock(name='gsn_date_str'),
        MagicMock(name='supplier_name'), MagicMock(name='item_code_filter'),
        MagicMock(name='wo_no'), MagicMock(name='ac_head'), MagicMock(name='po_no'),
        MagicMock(name='gin_no'), MagicMock(name='gsn_no'),
    ]

class ReportServiceTest(TestCase):
    def setUp(self):
        # Sample data for mocking
        self.mock_gin_data = [
            MockReportEntry(
                item_code='ITEM001', description='Product A', comp_id=1,
                po_date_str='2023-01-15', gin_date_str='2023-01-20', grr_date_str='2023-01-25', gqn_date_str='2023-01-22',
                supplier_name='SupplierX', item_code_filter='ITEM001', wo_no='WO001', ac_head='ACC1',
                po_no='PO001', gin_no='GIN001', grr_no='GRR001', gqn_no='GQN001',
            ),
            MockReportEntry(
                item_code='ITEM002', description='Product B', comp_id=1,
                po_date_str='2023-02-10', gin_date_str='2023-02-15', grr_date_str='2023-02-20', gqn_date_str='2023-02-18',
                supplier_name='SupplierY', item_code_filter='ITEM002', wo_no='WO002', ac_head='ACC2',
                po_no='PO002', gin_no='GIN002', grr_no='GRR002', gqn_no='GQN002',
            ),
            MockReportEntry(
                item_code='ITEM003', description='Product C', comp_id=2,
                po_date_str='2023-03-01', gin_date_str='2023-03-05', grr_date_str='2023-03-10', gqn_date_str='2023-03-08',
                supplier_name='SupplierZ', item_code_filter='ITEM003', wo_no='WO003', ac_head='ACC3',
                po_no='PO003', gin_no='GIN003', grr_no='GRR003', gqn_no='GQN003',
            ),
        ]
        self.mock_gsn_data = [
            MockReportEntry(
                item_code='ITEM004', description='Product D', comp_id=1,
                po_date_str='2023-04-01', gin_date_str='2023-04-05', gsn_date_str='2023-04-10',
                supplier_name='SupplierA', item_code_filter='ITEM004', wo_no='WO004', ac_head='ACC4',
                po_no='PO004', gin_no='GIN004', gsn_no='GSN004',
            ),
        ]
        self.mock_acc_head = [
            MockAccHead(id=1, symbol='ACC1'),
            MockAccHead(id=2, symbol='ACC2'),
            MockAccHead(id=3, symbol='ACC3'),
            MockAccHead(id=4, symbol='ACC4'),
        ]

    @patch('inventory_reports.models.InventoryGINReport.objects')
    @patch('inventory_reports.models.InventoryGSNReport.objects')
    @patch('inventory_reports.models.AccHead.objects')
    def test_get_report_data_gin_basic(self, mock_acc_head_objects, mock_gsn_objects, mock_gin_objects):
        mock_gin_objects.filter.return_value = MockQuerySet(self.mock_gin_data)
        
        filters = {'rad': '0'}
        selected_columns = ['SrNo', 'ItemCode', 'Description']
        comp_id = 1
        
        data, headers_map = ReportService.get_report_data(filters, selected_columns, comp_id)
        
        self.assertEqual(len(data), 3) # All 3 entries for comp_id=1
        self.assertIn('SrNo', data[0])
        self.assertIn('ItemCode', data[0])
        self.assertIn('Description', data[0])
        self.assertEqual(data[0]['ItemCode'], 'ITEM001')
        mock_gin_objects.filter.assert_called_with(comp_id=comp_id)

    @patch('inventory_reports.models.InventoryGSNReport.objects')
    @patch('inventory_reports.models.InventoryGINReport.objects')
    @patch('inventory_reports.models.AccHead.objects')
    def test_get_report_data_gsn_basic(self, mock_acc_head_objects, mock_gin_objects, mock_gsn_objects):
        mock_gsn_objects.filter.return_value = MockQuerySet(self.mock_gsn_data)

        filters = {'rad': '4'}
        selected_columns = ['SrNo', 'ItemCode', 'GSNNo']
        comp_id = 1
        
        data, headers_map = ReportService.get_report_data(filters, selected_columns, comp_id)
        
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['ItemCode'], 'ITEM004')
        self.assertEqual(data[0]['GSNNo'], 'GSN004')
        mock_gsn_objects.filter.assert_called_with(comp_id=comp_id)

    @patch('inventory_reports.models.InventoryGINReport.objects')
    @patch('inventory_reports.models.AccHead.objects')
    def test_get_report_data_with_filters(self, mock_acc_head_objects, mock_gin_objects):
        mock_gin_objects.filter.return_value = MockQuerySet(self.mock_gin_data)
        mock_acc_head_objects.get.side_effect = lambda id: next(ah for ah in self.mock_acc_head if ah.id == id)

        filters = {
            'rad': '0',
            'report_type': '1', # GINNo
            'report_no': 'GIN001',
            'from_date': date(2023, 1, 1),
            'to_date': date(2023, 1, 31),
            'supplier_id': 'SupplierX',
            'item_code': 'ITEM001',
            'wo_no': 'WO001',
            'acc_val': 1, # ID for ACC1
        }
        selected_columns = ['SrNo', 'ItemCode', 'GINNo', 'SupplierName']
        comp_id = 1
        
        data, headers_map = ReportService.get_report_data(filters, selected_columns, comp_id)
        
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['ItemCode'], 'ITEM001')
        self.assertEqual(data[0]['GINNo'], 'GIN001')
        self.assertEqual(data[0]['SupplierName'], 'SupplierX')

class InventoryReportViewsTest(TestCase):
    client = Client()

    def setUp(self):
        # Mock session for compid
        self.client.session['compid'] = 1

    @patch('inventory_reports.forms.ReportFilterForm.is_valid', return_value=True)
    @patch('inventory_reports.forms.ReportColumnSelectionForm.is_valid', return_value=True)
    @patch('inventory_reports.services.ReportService.get_report_data', return_value=([], {})) # No data initially
    def test_report_search_view(self, mock_get_report_data, mock_col_form_valid, mock_filter_form_valid):
        response = self.client.get(reverse('inventory_reports:report_search'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/search/report_search.html')
        self.assertIn('filter_form', response.context)
        self.assertIn('column_selection_form', response.context)
    
    @patch('inventory_reports.forms.ReportFilterForm')
    @patch('inventory_reports.forms.ReportColumnSelectionForm')
    @patch('inventory_reports.services.ReportService.get_report_data')
    def test_report_table_htmx_view_success(self, mock_get_report_data, MockColumnSelectionForm, MockReportFilterForm):
        # Setup mocks for forms and service
        mock_filter_form_instance = MockReportFilterForm.return_value
        mock_filter_form_instance.is_valid.return_value = True
        mock_filter_form_instance.cleaned_data = {'rad': '0', 'from_date': None, 'to_date': None}

        mock_col_form_instance = MockColumnSelectionForm.return_value
        mock_col_form_instance.is_valid.return_value = True
        mock_col_form_instance.cleaned_data = {'selected_columns': ['ItemCode', 'Description']}

        mock_get_report_data.return_value = (
            [{'ItemCode': 'TEST1', 'Description': 'Desc1'}, {'ItemCode': 'TEST2', 'Description': 'Desc2'}],
            {'ItemCode': 'Item Code', 'Description': 'Description'}
        )

        response = self.client.get(reverse('inventory_reports:report_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/search/_report_table.html')
        self.assertIn(b'Item Code', response.content)
        self.assertIn(b'Desc1', response.content)
        mock_get_report_data.assert_called_once()

    @patch('inventory_reports.forms.ReportFilterForm')
    @patch('inventory_reports.forms.ReportColumnSelectionForm')
    @patch('inventory_reports.services.ReportService.get_report_data')
    def test_report_table_htmx_view_invalid_form(self, mock_get_report_data, MockColumnSelectionForm, MockReportFilterForm):
        mock_filter_form_instance = MockReportFilterForm.return_value
        mock_filter_form_instance.is_valid.return_value = False # Invalid form
        mock_filter_form_instance.errors.as_data.return_value = {'rad': [MagicMock(message='Invalid choice')]}

        mock_col_form_instance = MockColumnSelectionForm.return_value
        mock_col_form_instance.is_valid.return_value = True
        mock_col_form_instance.cleaned_data = {'selected_columns': ['ItemCode']}

        response = self.client.get(reverse('inventory_reports:report_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/search/_report_table.html')
        self.assertIn(b'No data found', response.content) # Check for no data message
        mock_get_report_data.assert_not_called()

    @patch('inventory_reports.forms.ReportFilterForm.is_valid', return_value=True)
    @patch('inventory_reports.forms.ReportColumnSelectionForm.is_valid', return_value=True)
    def test_report_columns_htmx_view(self, mock_col_form_valid, mock_filter_form_valid):
        response = self.client.get(reverse('inventory_reports:report_columns_htmx'), {'rad': '4'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/search/_column_checkboxes.html')
        self.assertIn(b'GSN No', response.content) # Verify GSN specific field is present

    @patch('inventory_reports.forms.ReportFilterForm')
    @patch('inventory_reports.forms.ReportColumnSelectionForm')
    @patch('inventory_reports.services.ReportService.get_report_data')
    def test_report_export_excel_view_success(self, mock_get_report_data, MockColumnSelectionForm, MockReportFilterForm):
        mock_filter_form_instance = MockReportFilterForm.return_value
        mock_filter_form_instance.is_valid.return_value = True
        mock_filter_form_instance.cleaned_data = {'rad': '0', 'from_date': None, 'to_date': None}

        mock_col_form_instance = MockColumnSelectionForm.return_value
        mock_col_form_instance.is_valid.return_value = True
        mock_col_form_instance.cleaned_data = {'selected_columns': ['SrNo', 'ItemCode']}

        mock_get_report_data.return_value = (
            [{'SrNo': 1, 'ItemCode': 'TEST1'}, {'SrNo': 2, 'ItemCode': 'TEST2'}],
            {'SrNo': 'Sr No', 'ItemCode': 'Item Code'}
        )
        
        response = self.client.get(reverse('inventory_reports:report_export'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertTrue('attachment; filename="inventory_report_' in response['Content-Disposition'])

        # Verify Excel content (basic check)
        from openpyxl import load_workbook
        workbook = load_workbook(filename=BytesIO(response.content))
        sheet = workbook.active
        self.assertEqual(sheet['A1'].value, 'SrNo') # Dataframe will rename SrNo to SrNo if it exists
        self.assertEqual(sheet['B1'].value, 'ItemCode')
        self.assertEqual(sheet['A2'].value, 1)
        self.assertEqual(sheet['B2'].value, 'TEST1')

    @patch('inventory_reports.forms.ReportFilterForm')
    @patch('inventory_reports.forms.ReportColumnSelectionForm')
    @patch('inventory_reports.services.ReportService.get_report_data')
    def test_report_export_excel_view_no_data(self, mock_get_report_data, MockColumnSelectionForm, MockReportFilterForm):
        mock_filter_form_instance = MockReportFilterForm.return_value
        mock_filter_form_instance.is_valid.return_value = True
        mock_filter_form_instance.cleaned_data = {'rad': '0', 'from_date': None, 'to_date': None}

        mock_col_form_instance = MockColumnSelectionForm.return_value
        mock_col_form_instance.is_valid.return_value = True
        mock_col_form_instance.cleaned_data = {'selected_columns': ['ItemCode']}

        mock_get_report_data.return_value = ([], {}) # No data
        
        response = self.client.get(reverse('inventory_reports:report_export'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No content status for HTMX trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshReportList', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Content:**
    *   The main report page (`report_search.html`) loads the initial filter and column selection forms.
    *   When the "Show Report" button is clicked, an `hx-get` request is sent to `{% url 'inventory_reports:report_table' %}`. The response, which is the `_report_table.html` partial, replaces the content of `#report-table-container`.
    *   Changing the "Report Type (RAd)" dropdown triggers an `hx-get` to `{% url 'inventory_reports:report_columns_htmx' %}` which updates the `_column_checkboxes.html` partial inside `#column-selection-panel`.
    *   The "Export To Excel" button uses `hx-get` to `{% url 'inventory_reports:report_export' %}` with `hx-params="get"` to send all current form parameters. `hx-swap="none"` prevents content replacement, as the response is a file download.
    *   `HX-Trigger` headers are used in `ReportExportExcelView` to signal the frontend (e.g., to refresh a list or display a message) after an export attempt.
    *   `hx-indicator` (`#loading-indicator`) provides visual feedback during AJAX requests.

*   **Alpine.js for UI State:**
    *   Alpine.js is initialized globally, but for this page, it's primarily used for minor UI enhancements, such as the "Check All" checkbox behavior (though this can also be done with plain JS, or even HTMX with `_` attribute).
    *   The modal example in the template is an illustration of how Alpine would manage `hidden` classes for showing/hiding dynamic content (not directly used for this specific page, but for general CRUD flows).

*   **DataTables for List Views:**
    *   The `_report_table.html` partial contains the `<table>` element.
    *   After HTMX injects this partial, a JavaScript event listener `htmx:afterSwap` re-initializes DataTables on the `#reportTable` element. This ensures DataTables features (searching, sorting, pagination, scrolling) are applied to the newly loaded content.
    *   The `scrollX` and `scrollY` options are used to replicate the ASP.NET `GridView`'s scrolling behavior.

*   **DRY Template Inheritance:**
    *   All templates (`report_search.html`) extend `core/base.html` (as per instruction). This ensures a consistent header, footer, and shared CDN links (including jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS) are inherited without being explicitly defined in each child template.

*   **No Additional JavaScript:**
    *   The solution strictly avoids writing complex custom JavaScript. All dynamic interactions are handled by HTMX, with Alpine.js managing simple UI toggles or states. DataTables is a pre-built library, not custom JS logic.

---

### Final Notes

*   **Placeholder Replacement:** Remember to replace `settings.DEFAULT_COMPANY_ID` with the actual default company ID or ensure `request.session['compid']` is properly set (likely during user login/authentication).
*   **Database `varchar` Dates:** The models use `CharField` for dates stored as `varchar`. For robustness and proper date handling in Django, it is *highly recommended* to convert these database columns to proper `DateField` or `DateTimeField` types during the modernization process, which would simplify filtering and display in Python. The current solution works but may be less performant for date range queries on `varchar` columns without proper indexing or conversion at the DB level.
*   **Error Handling and User Feedback:** Django's `messages` framework is used for success/error messages. The frontend should have a mechanism (e.g., toast notifications) to display these messages, triggered by HTMX `HX-Trigger` headers.
*   **Security:** Ensure proper authentication and authorization are in place for accessing these reports, as `Session["compid"]` indicates a multi-tenant or company-specific context. Django's built-in authentication system and decorators can be used.
*   **Custom Filter:** A simple custom template filter (`get_item`) was added to facilitate accessing dictionary values by dynamic keys in the template, which is useful when columns are selected dynamically.