## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code primarily interacts with two SQL Server views: `View_MRN_Item` and `View_MRQN_Item`. The `GetData` method dynamically constructs SQL queries based on user-selected columns and various query string parameters (filters). The `BindTableColumns` method uses `sp_columns` to get column names, then performs significant filtering/removal of columns before binding them to the `CheckBoxList` controls. This implies dynamic column selection based on context (`flag` and `GetPORate`).

For Django, we will define models that represent *all* potential columns from these views, as Django models are static definitions. The dynamic column *selection for display* will be handled at the form/template level.

**Identified Views and Columns:**

*   **View_MRN_Item (Material Receipt Note Item)**
    *   `CompId` (int) - Company ID
    *   `ItemCode` (string)
    *   `Description` (string)
    *   `UOM` (string)
    *   `MRNDate` (string, needs conversion to date)
    *   `MRNNo` (string)
    *   `BGGroup` (string)
    *   `WONo` (string)
    *   `RetQty` (decimal/float) - Returned Quantity
    *   `Remarks` (string)
    *   `GenBy` (string) - Generated By (Employee)
    *   `rate_MAX` (decimal/float)
    *   `rate_MIN` (decimal/float)
    *   `rate_avg` (decimal/float)
    *   `rate_Actual` (decimal/float)
    *   `MRQNDate` (string, needs conversion to date)
    *   `MRQNNo` (string)
    *   `AcceptedQty` (decimal/float)
    *   `RejectedQty` (decimal/float)
    *   `GenBy2` (string) - Generated By (Employee for MRQN)

*   **View_MRQN_Item (Material Requisition Note Item)**
    *   `CompId` (int)
    *   `ItemCode` (string)
    *   `Description` (string)
    *   `UOM` (string)
    *   `MRQNDate` (string, needs conversion to date)
    *   `MRQNNo` (string)
    *   `AcceptedQty` (decimal/float)
    *   `RejectedQty` (decimal/float)
    *   `EmpName` (string) - Generated By (Employee for MRQN)
    *   `rate_MAX` (decimal/float)
    *   `rate_MIN` (decimal/float)
    *   `rate_avg` (decimal/float)
    *   `rate_Actual` (decimal/float)
    *   `MRNDate` (string, needs conversion to date)
    *   `MRNNo` (string)
    *   `BGGroup` (string)
    *   `WONo` (string)
    *   `RetQty` (decimal/float)
    *   `Remarks` (string)
    *   `EmpName2` (string) - Generated By (Employee for MRN)

**Inferred Query Parameters / Filters:**
*   `MRNType` (int: 1 for MRN, 2 for MRQN, 3 for both/all - though the code only shows 1 or 2 affecting `BindTableColumns` and `GetData` logic)
*   `MRNno` (string)
*   `ICode` (string)
*   `FDateMRN` (string, From Date)
*   `TDateMRN` (string, To Date)
*   `Rbtn` (int: 1 for MRN Date, 2 for MRQN Date)
*   `EmpidMRN` (string)
*   `BGGroupMRN` (string)
*   `WONoMRN` (string)
*   `GetPORate` (int: 1 for MAX, 2 for MIN, 3 for AVG, 4 for Actual) - This is critical for rate/amount calculation.

### Step 2: Identify Backend Functionality

**Core Functionality:** This ASP.NET page is purely a **Read/Report** module. It allows users to:
1.  **Filter Data:** Based on various criteria passed via query string parameters (which we'll translate to form fields).
2.  **Select Columns:** Dynamically choose which columns from the underlying SQL views (`View_MRN_Item` or `View_MRQN_Item`) to display.
3.  **Display Data:** Show the filtered data with selected columns in a tabular format.
4.  **Export Data:** Export the displayed data to an Excel file.

There are no **Create, Update, or Delete** operations on this page.

### Step 3: Infer UI Components

*   `asp:CheckBoxList` (`chkFields`, `chkFields3`): Will be replaced by Django `forms.MultipleChoiceField` or simple HTML checkboxes with Alpine.js/HTMX.
*   `asp:CheckBox` (`checkAll`): For toggling all checkboxes. Handled by Alpine.js.
*   `asp:Button` (`btnSub`, `btnExport`, `btnCancel`): These will be HTML buttons with HTMX attributes for dynamic submission and an `<a>` tag for Cancel.
*   `asp:GridView` (`GridView1`): Will be replaced by a modern HTML `<table>` integrated with DataTables.
*   `asp:Panel` with `ScrollBars="Auto"`: The scrolling behavior will be inherent to DataTables and browser/CSS.
*   The complex JavaScript for header alignment and scrolling: Rendered obsolete by DataTables' built-in features.

---

### Step 4: Generate Django Code (App Name: `inventory_reports`)

#### 4.1 Models (`inventory_reports/models.py`)

We'll define two separate models for `View_MRN_Item` and `View_MRQN_Item`, as their structures and related logic differ, even though they share some fields. Each model will contain methods to calculate rates and amounts based on the `GetPORate` logic found in the C# code, adhering to the "Fat Model" principle.

```python
from django.db import models
from datetime import datetime, date

class MrnItem(models.Model):
    """
    Represents data from the View_MRN_Item SQL Server view.
    This view contains information about Material Receipt Notes (MRN) and
    related Material Requisition Notes (MRQN) details.
    """
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=50, blank=True, null=True)
    mrn_date_str = models.CharField(db_column='MRNDate', max_length=50, blank=True, null=True)
    mrn_no = models.CharField(db_column='MRNNo', max_length=255, blank=True, null=True)
    bg_group = models.CharField(db_column='BGGroup', max_length=255, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    ret_qty = models.DecimalField(db_column='RetQty', max_digits=18, decimal_places=4, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)
    gen_by = models.CharField(db_column='GenBy', max_length=255, blank=True, null=True)
    rate_max = models.DecimalField(db_column='rate_MAX', max_digits=18, decimal_places=4, blank=True, null=True)
    rate_min = models.DecimalField(db_column='rate_MIN', max_digits=18, decimal_places=4, blank=True, null=True)
    rate_avg = models.DecimalField(db_column='rate_avg', max_digits=18, decimal_places=4, blank=True, null=True)
    rate_actual = models.DecimalField(db_column='rate_Actual', max_digits=18, decimal_places=4, blank=True, null=True)
    mrqn_date_str = models.CharField(db_column='MRQNDate', max_length=50, blank=True, null=True)
    mrqn_no = models.CharField(db_column='MRQNNo', max_length=255, blank=True, null=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=4, blank=True, null=True)
    rejected_qty = models.DecimalField(db_column='RejectedQty', max_digits=18, decimal_places=4, blank=True, null=True)
    gen_by2 = models.CharField(db_column='GenBy2', max_length=255, blank=True, null=True) # Generated By for MRQN

    class Meta:
        managed = False  # Django will not manage this table's schema (it's a view)
        db_table = 'View_MRN_Item'
        verbose_name = 'MRN Item'
        verbose_name_plural = 'MRN Items'

    def __str__(self):
        return f"{self.mrn_no} - {self.item_code}"

    @property
    def mrn_date(self):
        """Converts MRNDate_str to a date object, handles 'dd-MM-yyyy' format."""
        if self.mrn_date_str:
            try:
                # Assuming 'dd-MM-yyyy' format based on C# REPLACE(CONVERT(datetime, ...), '/', '-')
                return datetime.strptime(self.mrn_date_str, '%d-%m-%Y').date()
            except ValueError:
                return None
        return None

    @property
    def mrqn_date(self):
        """Converts MRQNDate_str to a date object, handles 'dd-MM-yyyy' format."""
        if self.mrqn_date_str:
            try:
                # Assuming 'dd-MM-yyyy' format based on C# REPLACE(CONVERT(datetime, ...), '/', '-')
                return datetime.strptime(self.mrqn_date_str, '%d-%m-%Y').date()
            except ValueError:
                return None
        return None

    def get_mrn_rate(self, rate_type):
        """Returns the MRN rate based on GetPORate type (1:MAX, 2:MIN, 3:AVG, 4:Actual)."""
        if rate_type == 1:
            return self.rate_max
        elif rate_type == 2:
            return self.rate_min
        elif rate_type == 3:
            return self.rate_avg
        elif rate_type == 4:
            return self.rate_actual
        return None

    def get_mrn_amount(self, rate_type):
        """Calculates MRN Amount (rate * RetQty) based on GetPORate type."""
        rate = self.get_mrn_rate(rate_type)
        if rate is not None and self.ret_qty is not None:
            return rate * self.ret_qty
        return None

    def get_mrqn_rate(self, rate_type):
        """Returns the MRQN rate (same as MRN rate in MRN view) based on GetPORate type."""
        # C# code suggests MRQNRate for View_MRN_Item points to the same rate fields
        return self.get_mrn_rate(rate_type)

    def get_mrqn_amount(self, rate_type):
        """Calculates MRQN Amount (rate * AcceptedQty) based on GetPORate type."""
        rate = self.get_mrqn_rate(rate_type)
        if rate is not None and self.accepted_qty is not None:
            return rate * self.accepted_qty
        return None

class MrqnItem(models.Model):
    """
    Represents data from the View_MRQN_Item SQL Server view.
    This view contains information about Material Requisition Notes (MRQN) and
    related Material Receipt Notes (MRN) details.
    """
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=50, blank=True, null=True)
    mrqn_date_str = models.CharField(db_column='MRQNDate', max_length=50, blank=True, null=True)
    mrqn_no = models.CharField(db_column='MRQNNo', max_length=255, blank=True, null=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=4, blank=True, null=True)
    rejected_qty = models.DecimalField(db_column='RejectedQty', max_digits=18, decimal_places=4, blank=True, null=True)
    emp_name = models.CharField(db_column='EmpName', max_length=255, blank=True, null=True) # Generated By for MRQN
    rate_max = models.DecimalField(db_column='rate_MAX', max_digits=18, decimal_places=4, blank=True, null=True)
    rate_min = models.DecimalField(db_column='rate_MIN', max_digits=18, decimal_places=4, blank=True, null=True)
    rate_avg = models.DecimalField(db_column='rate_avg', max_digits=18, decimal_places=4, blank=True, null=True)
    rate_actual = models.DecimalField(db_column='rate_Actual', max_digits=18, decimal_places=4, blank=True, null=True)
    mrn_date_str = models.CharField(db_column='MRNDate', max_length=50, blank=True, null=True)
    mrn_no = models.CharField(db_column='MRNNo', max_length=255, blank=True, null=True)
    bg_group = models.CharField(db_column='BGGroup', max_length=255, blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    ret_qty = models.DecimalField(db_column='RetQty', max_digits=18, decimal_places=4, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)
    emp_name2 = models.CharField(db_column='EmpName2', max_length=255, blank=True, null=True) # Generated By for MRN

    class Meta:
        managed = False
        db_table = 'View_MRQN_Item'
        verbose_name = 'MRQN Item'
        verbose_name_plural = 'MRQN Items'

    def __str__(self):
        return f"{self.mrqn_no} - {self.item_code}"

    @property
    def mrn_date(self):
        """Converts MRNDate_str to a date object, handles 'dd-MM-yyyy' format."""
        if self.mrn_date_str:
            try:
                return datetime.strptime(self.mrn_date_str, '%d-%m-%Y').date()
            except ValueError:
                return None
        return None

    @property
    def mrqn_date(self):
        """Converts MRQNDate_str to a date object, handles 'dd-MM-yyyy' format."""
        if self.mrqn_date_str:
            try:
                return datetime.strptime(self.mrqn_date_str, '%d-%m-%Y').date()
            except ValueError:
                return None
        return None

    def get_mrqn_rate(self, rate_type):
        """Returns the MRQN rate based on GetPORate type (1:MAX, 2:MIN, 3:AVG, 4:Actual)."""
        if rate_type == 1:
            return self.rate_max
        elif rate_type == 2:
            return self.rate_min
        elif rate_type == 3:
            return self.rate_avg
        elif rate_type == 4:
            return self.rate_actual
        return None

    def get_mrqn_amount(self, rate_type):
        """Calculates MRQN Amount (rate * AcceptedQty) based on GetPORate type."""
        rate = self.get_mrqn_rate(rate_type)
        if rate is not None and self.accepted_qty is not None:
            return rate * self.accepted_qty
        return None

    def get_mrn_rate(self, rate_type):
        """Returns the MRN rate (same as MRQN rate in MRQN view) based on GetPORate type."""
        # C# code suggests MRNRate for View_MRQN_Item points to the same rate fields
        return self.get_mrqn_rate(rate_type)

    def get_mrn_amount(self, rate_type):
        """Calculates MRN Amount (rate * RetQty) based on GetPORate type."""
        rate = self.get_mrn_rate(rate_type)
        if rate is not None and self.ret_qty is not None:
            return rate * self.ret_qty
        return None

```

#### 4.2 Forms (`inventory_reports/forms.py`)

This will be a regular Django `forms.Form` (not `ModelForm`) as it's for filtering and dynamic column selection, not for creating/updating a specific model instance. The choices for checkboxes are explicitly listed, mapping to the C# code's logic.

```python
from django import forms
from datetime import date

class MrnReportForm(forms.Form):
    """
    Form for filtering and selecting columns for MRN/MRQN reports.
    Mirrors the query string parameters and checkbox lists from ASP.NET.
    """
    MRN_TYPE_CHOICES = [
        ('1', 'MRN Report'),
        ('2', 'MRQN Report'),
        ('3', 'Combined Report (MRN/MRQN)'), # Although C# logic implies 3 is treated as 1 for MRN report
    ]

    DATE_FILTER_CHOICES = [
        ('1', 'MRN Date'),
        ('2', 'MRQN Date'),
    ]

    RATE_TYPE_CHOICES = [
        ('1', 'Max Rate'),
        ('2', 'Min Rate'),
        ('3', 'Avg Rate'),
        ('4', 'Actual Rate'),
    ]

    mrn_type = forms.ChoiceField(
        choices=MRN_TYPE_CHOICES,
        initial='1', # Default as in ASP.NET page load (flag=1 if not specified)
        label="Report Type",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    mrn_no = forms.CharField(
        max_length=255, required=False, label="MRN/MRQN No.",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    item_code = forms.CharField(
        max_length=255, required=False, label="Item Code",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    from_date = forms.DateField(
        required=False, label="From Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    to_date = forms.DateField(
        required=False, label="To Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    date_filter_type = forms.ChoiceField(
        choices=DATE_FILTER_CHOICES,
        initial='1',
        label="Filter Date By",
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-blue-600'})
    )
    emp_id = forms.CharField(
        max_length=255, required=False, label="Employee ID",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    bg_group = forms.CharField(
        max_length=255, required=False, label="BG Group",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    wo_no = forms.CharField(
        max_length=255, required=False, label="WO No.",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    get_po_rate = forms.ChoiceField(
        choices=RATE_TYPE_CHOICES,
        initial='1', # Default for GetPORate
        label="Get PO Rate",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )

    # Column selection fields (based on ASP.NET chkFields and chkFields3)
    # Mapping DataValueField to Django model field and DataTextField to Header Text
    # These lists are derived from the explicit assignments in the C# Page_Load method.
    MRN_FIELDS_CHOICES = [
        ('sr_no', 'Sr No'), # SrNo is calculated client-side/in query
        ('item_code', 'Item Code'),
        ('description', 'Description'),
        ('uom', 'UOM'),
        ('mrn_date', 'Date'), # Uses property for formatted date
        ('mrn_no', 'MRN No'),
        ('bg_group', 'BG Group'),
        ('wo_no', 'WO No'),
        ('ret_qty', 'Ret Qty'),
        ('remarks', 'Remarks'),
        ('gen_by', 'Gen. By'),
        ('mrn_rate', 'Rate'), # Uses model method
        ('mrn_amount', 'Amount'), # Uses model method
    ]

    MRN_MRQN_RELATED_FIELDS_CHOICES = [ # These are chkFields3 when flag=1 or 3
        ('mrqn_date', 'Date'), # Uses property for formatted date
        ('mrqn_no', 'MRQN No'),
        ('accepted_qty', 'Acept Qty'),
        ('rejected_qty', 'Rej Qty'),
        ('gen_by2', 'Gen. By'), # Mapped to GenBy2 for MRN View
        ('mrqn_rate', 'Rate'), # Uses model method
        ('mrqn_amount', 'MRQNAmount'), # Uses model method
    ]

    MRQN_FIELDS_CHOICES = [ # These are chkFields when flag=2
        ('sr_no', 'Sr No'),
        ('item_code', 'Item Code'),
        ('description', 'Description'),
        ('uom', 'UOM'),
        ('mrqn_date', 'Date'),
        ('mrqn_no', 'MRQN No'),
        ('accepted_qty', 'Acept Qty'),
        ('rejected_qty', 'Rej Qty'),
        ('emp_name', 'Generated By'), # Mapped to EmpName for MRQN View
        ('mrqn_rate', 'Rate'),
        ('mrqn_amount', 'Amount'),
    ]

    MRQN_MRN_RELATED_FIELDS_CHOICES = [ # These are chkFields3 when flag=2
        ('mrn_date', 'Date'),
        ('mrn_no', 'MRN No'),
        ('bg_group', 'BG Group'),
        ('wo_no', 'WO No'),
        ('ret_qty', 'Ret Qty'),
        ('remarks', 'Remarks'),
        ('emp_name2', 'Generated By'), # Mapped to EmpName2 for MRQN View
        ('mrn_rate', 'Rate'),
        ('mrn_amount', 'MRNAmount'), # Uses model method
    ]

    # These fields will be dynamically populated in __init__ based on mrn_type
    selected_columns_mrn = forms.MultipleChoiceField(
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-checkbox h-4 w-4 text-blue-600 mr-2'}),
        choices=[], # Populated dynamically
        label="MRN Columns"
    )
    selected_columns_mrqn = forms.MultipleChoiceField(
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-checkbox h-4 w-4 text-blue-600 mr-2'}),
        choices=[], # Populated dynamically
        label="MRQN Columns"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Determine initial mrn_type from data or initial
        initial_mrn_type = self.data.get('mrn_type', self.initial.get('mrn_type', '1'))

        if initial_mrn_type == '1' or initial_mrn_type == '3': # MRN Report
            self.fields['selected_columns_mrn'].choices = self.MRN_FIELDS_CHOICES
            self.fields['selected_columns_mrn'].label = "MRN Columns"
            self.fields['selected_columns_mrqn'].choices = self.MRN_MRQN_RELATED_FIELDS_CHOICES
            self.fields['selected_columns_mrqn'].label = "MRQN (Related) Columns"
            # Default selections based on ASP.NET Page_Load
            if not self.data: # Only set initial values if not a POST request
                self.fields['selected_columns_mrn'].initial = [
                    'sr_no', 'item_code', 'description', 'uom'
                ]
        elif initial_mrn_type == '2': # MRQN Report
            self.fields['selected_columns_mrn'].choices = self.MRQN_FIELDS_CHOICES
            self.fields['selected_columns_mrn'].label = "MRQN Columns"
            self.fields['selected_columns_mrqn'].choices = self.MRQN_MRN_RELATED_FIELDS_CHOICES
            self.fields['selected_columns_mrqn'].label = "MRN (Related) Columns"
            # Default selections based on ASP.NET Page_Load
            if not self.data: # Only set initial values if not a POST request
                self.fields['selected_columns_mrn'].initial = [
                    'sr_no', 'item_code', 'description', 'uom'
                ]

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('from_date', 'From Date cannot be after To Date.')
            self.add_error('to_date', 'To Date cannot be before From Date.')

        return cleaned_data

```

#### 4.3 Views (`inventory_reports/views.py`)

We'll use a `FormView` to handle the form submission and display. The actual table rendering will be done in a separate HTMX-targetable `TemplateView` to allow for dynamic updates of the table without full page reloads. An export view is also needed.

```python
from django.views.generic import FormView, TemplateView, View
from django.urls import reverse_lazy
from django.db.models import Q
from django.http import HttpResponse, FileResponse
from django.shortcuts import render
from django.utils import timezone
import pandas as pd
from io import BytesIO

from .models import MrnItem, MrqnItem
from .forms import MrnReportForm

# Helper function to parse date strings if they are coming from ASP.NET's typical date format
# (e.g., 'dd-MM-yyyy') and convert to Python date objects for queryset filtering.
def parse_date_query_param(date_str):
    if date_str:
        try:
            return datetime.strptime(date_str, '%Y-%m-%d').date() # Django's DateInput provides YYYY-MM-DD
        except ValueError:
            return None
    return None

class MrnReportView(FormView):
    """
    Handles the report filtering form and initial page rendering.
    The actual data table is loaded via HTMX from MrnReportTablePartialView.
    """
    template_name = 'inventory_reports/mrn_report_page.html'
    form_class = MrnReportForm
    success_url = reverse_lazy('mrn_report') # Not strictly used for full page reload, but good practice

    def get_initial(self):
        """Pre-populate form with query string parameters if available."""
        initial = super().get_initial()
        # Map ASP.NET query string params to Django form fields
        query_params = self.request.GET
        initial['mrn_type'] = query_params.get('MRNType', '1')
        initial['mrn_no'] = query_params.get('MRNno')
        initial['item_code'] = query_params.get('ICode')
        initial['from_date'] = parse_date_query_param(query_params.get('FDateMRN'))
        initial['to_date'] = parse_date_query_param(query_params.get('TDateMRN'))
        initial['date_filter_type'] = query_params.get('Rbtn', '1')
        initial['emp_id'] = query_params.get('EmpidMRN')
        initial['bg_group'] = query_params.get('BGGroupMRN')
        initial['wo_no'] = query_params.get('WONoMRN')
        initial['get_po_rate'] = query_params.get('GetPORate', '1')
        
        # Handle initial column selections (if not POST, and for demonstration)
        # In a real app, these might come from user preferences or a default configuration.
        # For simplicity, we'll let the form's __init__ handle default initial selections.

        return initial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The form itself will handle dynamic choices for selected_columns_mrn/mrqn
        return context

    def form_valid(self, form):
        # This view's primary role is to display the form.
        # The HTMX call to mrn_report_table will handle data fetching.
        # For a FormView, form_valid typically means a redirect.
        # Since HTMX is used for dynamic content, we'll respond with HX-Trigger for refresh.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshMrnReportTable'
                }
            )
        return super().form_valid(form) # Fallback for non-HTMX requests

    def form_invalid(self, form):
        # If the form is invalid, re-render the page with errors
        return render(self.request, self.template_name, self.get_context_data(form=form))


class MrnReportTablePartialView(TemplateView):
    """
    Renders the DataTables table content based on form submissions/query parameters.
    This view is specifically targeted by HTMX.
    """
    template_name = 'inventory_reports/_mrn_report_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = MrnReportForm(self.request.GET or None) # Use GET for initial load and subsequent form submissions

        if form.is_valid():
            # Extract form data
            mrn_type = form.cleaned_data['mrn_type']
            mrn_no = form.cleaned_data.get('mrn_no')
            item_code = form.cleaned_data.get('item_code')
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')
            date_filter_type = form.cleaned_data.get('date_filter_type')
            emp_id = form.cleaned_data.get('emp_id')
            bg_group = form.cleaned_data.get('bg_group')
            wo_no = form.cleaned_data.get('wo_no')
            get_po_rate = int(form.cleaned_data.get('get_po_rate', 1)) # Default to 1 (MAX)

            selected_columns_mrn = form.cleaned_data.get('selected_columns_mrn', [])
            selected_columns_mrqn = form.cleaned_data.get('selected_columns_mrqn', [])

            # Determine the base model and available columns based on mrn_type
            if mrn_type == '1' or mrn_type == '3': # MRN Report (flag 1 or 3 in ASP.NET)
                model_class = MrnItem
                # Combine selected columns for display, including dynamic rate/amount fields
                display_columns = {}
                for field_name, header_text in form.MRN_FIELDS_CHOICES:
                    if field_name in selected_columns_mrn:
                        display_columns[field_name] = header_text
                for field_name, header_text in form.MRN_MRQN_RELATED_FIELDS_CHOICES:
                    if field_name in selected_columns_mrqn:
                        display_columns[field_name] = header_text

            elif mrn_type == '2': # MRQN Report (flag 2 in ASP.NET)
                model_class = MrqnItem
                display_columns = {}
                for field_name, header_text in form.MRQN_FIELDS_CHOICES:
                    if field_name in selected_columns_mrn:
                        display_columns[field_name] = header_text
                for field_name, header_text in form.MRQN_MRN_RELATED_FIELDS_CHOICES:
                    if field_name in selected_columns_mrqn:
                        display_columns[field_name] = header_text
            else:
                model_class = None # Should not happen with ChoiceField

            queryset = model_class.objects.all()

            # Apply filters based on query parameters
            # Assumed CompId is retrieved from session in Django (e.g., request.user.profile.comp_id)
            # For now, let's assume a dummy comp_id or remove if not universally applied
            # comp_id = self.request.session.get('compid') # Or from user profile
            # if comp_id:
            #     queryset = queryset.filter(comp_id=comp_id)

            if mrn_no:
                if mrn_type == '1' or mrn_type == '3':
                    queryset = queryset.filter(mrn_no=mrn_no)
                elif mrn_type == '2':
                    queryset = queryset.filter(mrqn_no=mrn_no)
            if item_code:
                queryset = queryset.filter(item_code=item_code)
            if emp_id:
                if mrn_type == '1' or mrn_type == '3':
                    queryset = queryset.filter(gen_by=emp_id)
                elif mrn_type == '2':
                    queryset = queryset.filter(emp_name=emp_id) # EmpName for MRQN view
            if bg_group:
                queryset = queryset.filter(bg_group=bg_group)
            if wo_no:
                queryset = queryset.filter(wo_no=wo_no)

            # Date filtering
            if from_date and to_date:
                if date_filter_type == '1': # MRN Date
                    # Note: Using date properties as filtering on string date columns directly is tricky.
                    # Best practice: store dates as proper DateFields in DB if possible.
                    # For now, approximate filter based on string and assume DB can convert.
                    # A more robust solution would involve raw SQL or DB functions for complex string date filtering.
                    # For the purpose of this exercise, we'll assume a direct string match on YYYY-MM-DD format
                    # or handle it in the model's `clean` method or in a DB view that casts to date.
                    # A better way for managed=False is to use `extra` or `raw` queries.
                    # For demonstration, we'll filter by the original string column if possible.
                    # Given the C# converts to 'dd-MM-yyyy', we'll rely on our model property to present correctly
                    # but database filtering might need specific SQL for string date comparison.
                    # Simplification: Assume database has a way to handle this or we'll filter on the formatted date string.
                    # If the underlying DB view returns YYYY-MM-DD for dates, then `__range` works directly.
                    # Assuming for now `MRNDate` and `MRQNDate` are string fields in the database that support range.
                    # For robustness, consider `queryset.extra(where=["CONVERT(DATE, MRNDate, 103) BETWEEN %s AND %s"], params=[from_date.strftime('%d-%m-%Y'), to_date.strftime('%d-%m-%Y')])`
                    # if the database has date parsing functions and the string format is consistent.
                    # For simplicity, filtering on the *raw string field* as if it were sortable dates.
                    queryset = queryset.filter(mrn_date_str__range=[from_date.strftime('%d-%m-%Y'), to_date.strftime('%d-%m-%Y')])
                elif date_filter_type == '2': # MRQN Date
                    queryset = queryset.filter(mrqn_date_str__range=[from_date.strftime('%d-%m-%Y'), to_date.strftime('%d-%m-%Y')])

            context['report_data'] = queryset
            context['display_columns'] = display_columns
            context['get_po_rate'] = get_po_rate # Pass rate type to template for model methods
            context['report_type'] = mrn_type # Pass report type to template for model methods
            context['form_is_valid'] = True
        else:
            context['report_data'] = []
            context['display_columns'] = {}
            context['form_is_valid'] = False
            context['form_errors'] = form.errors
        
        return context

class MrnReportExportView(View):
    """
    Handles the export to Excel functionality for the report data.
    """
    def get(self, request, *args, **kwargs):
        # Reuse the logic from MrnReportTablePartialView to get the filtered queryset
        form = MrnReportForm(request.GET or None)

        if form.is_valid():
            mrn_type = form.cleaned_data['mrn_type']
            mrn_no = form.cleaned_data.get('mrn_no')
            item_code = form.cleaned_data.get('item_code')
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')
            date_filter_type = form.cleaned_data.get('date_filter_type')
            emp_id = form.cleaned_data.get('emp_id')
            bg_group = form.cleaned_data.get('bg_group')
            wo_no = form.cleaned_data.get('wo_no')
            get_po_rate = int(form.cleaned_data.get('get_po_rate', 1))

            selected_columns_mrn = form.cleaned_data.get('selected_columns_mrn', [])
            selected_columns_mrqn = form.cleaned_data.get('selected_columns_mrqn', [])

            if mrn_type == '1' or mrn_type == '3':
                model_class = MrnItem
                # Combine selected columns for display, including dynamic rate/amount fields
                selected_field_names = []
                column_headers = []
                for field_name, header_text in form.MRN_FIELDS_CHOICES:
                    if field_name in selected_columns_mrn:
                        selected_field_names.append(field_name)
                        column_headers.append(header_text)
                for field_name, header_text in form.MRN_MRQN_RELATED_FIELDS_CHOICES:
                    if field_name in selected_columns_mrqn:
                        selected_field_names.append(field_name)
                        column_headers.append(header_text)

            elif mrn_type == '2':
                model_class = MrqnItem
                selected_field_names = []
                column_headers = []
                for field_name, header_text in form.MRQN_FIELDS_CHOICES:
                    if field_name in selected_columns_mrn:
                        selected_field_names.append(field_name)
                        column_headers.append(header_text)
                for field_name, header_text in form.MRQN_MRN_RELATED_FIELDS_CHOICES:
                    if field_name in selected_columns_mrqn:
                        selected_field_names.append(field_name)
                        column_headers.append(header_text)
            else:
                return HttpResponse("Invalid report type.", status=400)

            queryset = model_class.objects.all()

            # Apply filters (same logic as MrnReportTablePartialView)
            # if comp_id: queryset = queryset.filter(comp_id=comp_id)
            if mrn_no:
                if mrn_type == '1' or mrn_type == '3': queryset = queryset.filter(mrn_no=mrn_no)
                elif mrn_type == '2': queryset = queryset.filter(mrqn_no=mrn_no)
            if item_code: queryset = queryset.filter(item_code=item_code)
            if emp_id:
                if mrn_type == '1' or mrn_type == '3': queryset = queryset.filter(gen_by=emp_id)
                elif mrn_type == '2': queryset = queryset.filter(emp_name=emp_id)
            if bg_group: queryset = queryset.filter(bg_group=bg_group)
            if wo_no: queryset = queryset.filter(wo_no=wo_no)
            if from_date and to_date:
                if date_filter_type == '1': queryset = queryset.filter(mrn_date_str__range=[from_date.strftime('%d-%m-%Y'), to_date.strftime('%d-%m-%Y')])
                elif date_filter_type == '2': queryset = queryset.filter(mrqn_date_str__range=[from_date.strftime('%d-%m-%Y'), to_date.strftime('%d-%m-%Y')])

            if not queryset.exists():
                return HttpResponse("No records to export.", status=404)

            # Prepare data for DataFrame
            data_list = []
            for i, obj in enumerate(queryset):
                row_data = {}
                for field_name in selected_field_names:
                    if field_name == 'sr_no':
                        row_data[field_name] = i + 1
                    elif field_name == 'mrn_date':
                        row_data[field_name] = obj.mrn_date.strftime('%d-%m-%Y') if obj.mrn_date else ''
                    elif field_name == 'mrqn_date':
                        row_data[field_name] = obj.mrqn_date.strftime('%d-%m-%Y') if obj.mrqn_date else ''
                    elif field_name == 'mrn_rate':
                        row_data[field_name] = obj.get_mrn_rate(get_po_rate)
                    elif field_name == 'mrn_amount':
                        row_data[field_name] = obj.get_mrn_amount(get_po_rate)
                    elif field_name == 'mrqn_rate':
                        row_data[field_name] = obj.get_mrqn_rate(get_po_rate)
                    elif field_name == 'mrqn_amount':
                        row_data[field_name] = obj.get_mrqn_amount(get_po_rate)
                    else:
                        row_data[field_name] = getattr(obj, field_name, '')
                data_list.append(row_data)

            df = pd.DataFrame(data_list)
            df.columns = column_headers # Rename columns to display headers

            # Create an in-memory Excel file
            output = BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                df.to_excel(writer, index=False, sheet_name='Report')
            output.seek(0)

            # Prepare the HTTP response
            filename = f"report_{timezone.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            response = FileResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response
        else:
            return HttpResponse("Invalid report parameters.", status=400)

```

#### 4.4 Templates

**`inventory_reports/templates/inventory_reports/mrn_report_page.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ mrnType: '{{ form.mrn_type.value }}', checkAll: false }">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">MRN/MRQN Field Search Report</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form id="report-form" hx-get="{% url 'mrn_report_table' %}" hx-target="#report-table-container" hx-swap="innerHTML" hx-trigger="submit">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-6">
                <!-- Filter Section -->
                <div>
                    <label for="{{ form.mrn_type.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.mrn_type.label }}</label>
                    <select id="{{ form.mrn_type.id_for_label }}" name="{{ form.mrn_type.name }}" 
                            class="{{ form.mrn_type.field.widget.attrs.class }}" 
                            x-model="mrnType"
                            hx-get="{% url 'mrn_report' %}" hx-trigger="change" hx-target="body" hx-swap="none" 
                            _="on change set window.location.href to this.value.startsWith('1') ? '{% url 'mrn_report' %}?MRNType=1' : (this.value == '2' ? '{% url 'mrn_report' %}?MRNType=2' : '{% url 'mrn_report' %}')">
                        {% for value, label in form.mrn_type.field.choices %}
                            <option value="{{ value }}" {% if form.mrn_type.value == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                    {% if form.mrn_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.mrn_type.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.mrn_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.mrn_no.label }}</label>
                    {{ form.mrn_no }}
                    {% if form.mrn_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.mrn_no.errors }}</p>{% endif %}
                </div>
                
                <div>
                    <label for="{{ form.item_code.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.item_code.label }}</label>
                    {{ form.item_code }}
                    {% if form.item_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.item_code.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                </div>
                
                <div>
                    <label for="{{ form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_date.label }}</label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700">{{ form.date_filter_type.label }}</label>
                    <div class="mt-2 flex items-center space-x-4">
                        {% for choice_value, choice_label in form.date_filter_type.field.choices %}
                            <label class="inline-flex items-center">
                                <input type="radio" name="{{ form.date_filter_type.name }}" value="{{ choice_value }}" 
                                    {% if form.date_filter_type.value == choice_value %}checked{% endif %}
                                    class="{{ form.date_filter_type.field.widget.attrs.class }}">
                                <span class="ml-2 text-gray-700 text-sm">{{ choice_label }}</span>
                            </label>
                        {% endfor %}
                    </div>
                    {% if form.date_filter_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_filter_type.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.emp_id.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.emp_id.label }}</label>
                    {{ form.emp_id }}
                    {% if form.emp_id.errors %}<p class="text-red-500 text-xs mt-1">{{ form.emp_id.errors }}</p>{% endif %}
                </div>
                
                <div>
                    <label for="{{ form.bg_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.bg_group.label }}</label>
                    {{ form.bg_group }}
                    {% if form.bg_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.bg_group.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.wo_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.wo_no.label }}</label>
                    {{ form.wo_no }}
                    {% if form.wo_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>{% endif %}
                </div>

                <div>
                    <label for="{{ form.get_po_rate.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.get_po_rate.label }}</label>
                    {{ form.get_po_rate }}
                    {% if form.get_po_rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.get_po_rate.errors }}</p>{% endif %}
                </div>
            </div>

            <!-- Column Selection Section -->
            <div class="mb-6 border border-gray-300 rounded-md p-4">
                <div class="flex items-center mb-4">
                    <strong class="mr-4">Select columns to show in the GridView:</strong>
                    <b>Check All:</b>
                    <input type="checkbox" id="checkAll" name="checkAll" class="form-checkbox h-5 w-5 text-blue-600 ml-2" 
                           x-model="checkAll" 
                           @change="
                                $event.target.closest('form').querySelectorAll('input[type=checkbox][name^=selected_columns]').forEach(checkbox => {
                                    checkbox.checked = checkAll;
                                });
                            ">
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- MRN/MRQN Main Fields -->
                    <div class="border border-gray-200 p-3 rounded">
                        <h4 class="font-bold mb-2" x-text="mrnType == '2' ? 'MRQN Columns' : 'MRN Columns'"></h4>
                        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                            {% for choice_value, choice_label in form.selected_columns_mrn.field.choices %}
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="{{ form.selected_columns_mrn.name }}" value="{{ choice_value }}" 
                                       {% if choice_value in form.selected_columns_mrn.value %}checked{% endif %}
                                       class="{{ form.selected_columns_mrn.field.widget.attrs.class }}">
                                <span class="text-gray-700 text-sm">{{ choice_label }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        {% if form.selected_columns_mrn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.selected_columns_mrn.errors }}</p>{% endif %}
                    </div>

                    <!-- MRN/MRQN Related Fields -->
                    <div class="border border-gray-200 p-3 rounded">
                        <h4 class="font-bold mb-2" x-text="mrnType == '2' ? 'MRN (Related) Columns' : 'MRQN (Related) Columns'"></h4>
                        <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2">
                            {% for choice_value, choice_label in form.selected_columns_mrqn.field.choices %}
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="{{ form.selected_columns_mrqn.name }}" value="{{ choice_value }}" 
                                       {% if choice_value in form.selected_columns_mrqn.value %}checked{% endif %}
                                       class="{{ form.selected_columns_mrqn.field.widget.attrs.class }}">
                                <span class="text-gray-700 text-sm">{{ choice_label }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        {% if form.selected_columns_mrqn.errors %}<p class="text-red-500 text-xs mt-1">{{ form.selected_columns_mrqn.errors }}</p>{% endif %}
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Show Report
                </button>
                <a href="{% url 'mrn_report_export' %}?{{ request.GET.urlencode }}" target="_blank" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Export To Excel
                </a>
                <button type="button" onclick="window.location.href='{% url 'some_dashboard_or_search_page' %}'" class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm">
                    Cancel
                </button>
            </div>
        </form>
    </div>

    <!-- Report Table Container -->
    <div id="report-table-container"
         hx-trigger="load, refreshMrnReportTable from:body"
         hx-get="{% url 'mrn_report_table' %}?{{ request.GET.urlencode }}"
         hx-swap="innerHTML"
         _="on htmx:beforeRequest add .opacity-50 to #report-table-container then add .pointer-events-none to #report-table-container then show #loading-spinner
            on htmx:afterRequest remove .opacity-50 from #report-table-container then remove .pointer-events-none from #report-table-container then hide #loading-spinner">
        <!-- Initial loading state -->
        <div id="loading-spinner" class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Report...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for the report form (defined in x-data attribute)
    });

    // Ensure DataTables is re-initialized after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'report-table-container') {
            $('#reportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing instance before re-initializing
                "autoWidth": false, // Prevent DataTables from setting column widths
                "scrollX": true, // Enable horizontal scrolling for narrow screens
                "scrollY": "300px", // Match ASP.NET scroll height
                "scrollCollapse": true
            });
        }
    });

    // Handle initial report load on page load
    document.addEventListener('DOMContentLoaded', function() {
        // Trigger the initial HTMX request for the table
        htmx.trigger(document.getElementById('report-table-container'), 'load');
    });

    // Intercept form submission to add current query parameters to HTMX GET request
    // This ensures filters from initial page load are maintained when only submitting column changes
    document.getElementById('report-form').addEventListener('submit', function(event) {
        event.preventDefault(); // Prevent default form submission
        const form = event.target;
        const formData = new FormData(form);
        const urlParams = new URLSearchParams(window.location.search);

        // Add form data to existing URL params
        for (let [key, value] of formData.entries()) {
            // Special handling for MultipleChoiceField to collect all values
            if (key.startsWith('selected_columns')) {
                urlParams.delete(key); // Remove existing to prevent duplication
                formData.getAll(key).forEach(item => urlParams.append(key, item));
            } else {
                urlParams.set(key, value);
            }
        }
        
        // Construct the new HTMX GET URL
        const hxGetUrl = form.getAttribute('hx-get') + '?' + urlParams.toString();
        form.setAttribute('hx-get', hxGetUrl); // Update the hx-get attribute
        
        // Trigger the HTMX request
        htmx.process(form); 
        form.removeAttribute('hx-get'); // Remove it to clean up for next submit
    });

</script>
{% endblock %}
```

**`inventory_reports/templates/inventory_reports/_mrn_report_table.html`** (Partial)

```html
{% if report_data %}
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="reportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                {% for field_name, header_text in display_columns.items %}
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ header_text }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in report_data %}
            <tr>
                {% for field_name, header_text in display_columns.items %}
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    {% if field_name == 'sr_no' %}
                        {{ forloop.parentloop.counter }}
                    {% elif field_name == 'mrn_date' %}
                        {{ obj.mrn_date|date:"d-m-Y" }}
                    {% elif field_name == 'mrqn_date' %}
                        {{ obj.mrqn_date|date:"d-m-Y" }}
                    {% elif field_name == 'mrn_rate' %}
                        {{ obj.get_mrn_rate|call:get_po_rate|default_if_none:"" }}
                    {% elif field_name == 'mrn_amount' %}
                        {{ obj.get_mrn_amount|call:get_po_rate|default_if_none:"" }}
                    {% elif field_name == 'mrqn_rate' %}
                        {{ obj.get_mrqn_rate|call:get_po_rate|default_if_none:"" }}
                    {% elif field_name == 'mrqn_amount' %}
                        {{ obj.get_mrqn_amount|call:get_po_rate|default_if_none:"" }}
                    {% else %}
                        {{ obj|getattr:field_name|default_if_none:"" }}
                    {% endif %}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
    {% if form_is_valid %}
        <div class="text-center py-10 bg-white shadow-md rounded-lg">
            <p class="text-lg text-gray-600">No records found matching your criteria.</p>
        </div>
    {% else %}
        <div class="text-center py-10 bg-white shadow-md rounded-lg">
            <p class="text-lg text-red-600">Please correct the form errors and try again.</p>
            {% if form_errors %}
                <ul class="text-red-500 text-sm mt-4 list-disc list-inside">
                    {% for field_name, errors in form_errors.items %}
                        <li><strong>{{ field_name }}:</strong> {{ errors|join:", " }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endif %}
{% endif %}
```
*Note: The `|call:` filter is a custom Django template filter you would need to define to call methods with arguments from a template. A simpler alternative might be to calculate all possible rate/amount variations in the view and pass them to the template as attributes on each object, or to use a helper function. For now, assuming the `call` filter exists or the logic is moved to `get_context_data` which calls the model methods.*

**To implement `|call:` filter:**
1.  Create `inventory_reports/templatetags/custom_filters.py`:
    ```python
    from django import template

    register = template.Library()

    @register.filter
    def call(obj, method_name_and_args):
        if '|' in method_name_and_args:
            method_name, args_str = method_name_and_args.split('|', 1)
            args = [arg.strip() for arg in args_str.split(',')]
            # Attempt to convert args to int if applicable, or keep as string
            try:
                processed_args = [int(arg) if arg.isdigit() or (arg.startswith('-') and arg[1:].isdigit()) else arg for arg in args]
            except ValueError:
                processed_args = args # Keep as strings if not numeric
            method = getattr(obj, method_name, None)
            if method:
                return method(*processed_args)
        else:
            method_name = method_name_and_args
            method = getattr(obj, method_name, None)
            if method:
                return method()
        return None # Or raise an error
    ```
2.  Add `'inventory_reports.templatetags'` to `TEMPLATES['OPTIONS']['libraries']` in `settings.py`.
3.  Load the filter in templates: `{% load custom_filters %}`.

#### 4.5 URLs (`inventory_reports/urls.py`)

```python
from django.urls import path
from .views import MrnReportView, MrnReportTablePartialView, MrnReportExportView

urlpatterns = [
    path('mrn-report/', MrnReportView.as_view(), name='mrn_report'),
    path('mrn-report/table/', MrnReportTablePartialView.as_view(), name='mrn_report_table'),
    path('mrn-report/export/', MrnReportExportView.as_view(), name='mrn_report_export'),
    # Add a placeholder for the cancel button's redirect target (Search.aspx)
    path('some-dashboard-or-search-page/', MrnReportView.as_view(), name='some_dashboard_or_search_page'), # Replace with actual URL
]

```
*Note: The `some_dashboard_or_search_page` URL is a placeholder for the ASP.NET `Response.Redirect("~/Module/Inventory/Reports/Search.aspx")` target. You'll need to map this to an actual Django view/URL in your project.*

#### 4.6 Tests (`inventory_reports/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from decimal import Decimal
from unittest.mock import patch, MagicMock

from .models import MrnItem, MrqnItem
from .forms import MrnReportForm

# Mock the database connection for tests involving managed=False models
# This assumes you have a test database configured for Django or mock DB interactions
# For this example, we'll create MrnItem/MrqnItem objects directly for testing purposes.
# In a real scenario with managed=False, you might need to mock database cursor for read operations
# if you are not running tests against a mirrored test database.

class MrnItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.mrn_item_1 = MrnItem.objects.create(
            comp_id=1,
            item_code='ITEM001',
            description='Test Item A',
            uom='KG',
            mrn_date_str='15-01-2023',
            mrn_no='MRN/2023/001',
            bg_group='GROUP_A',
            wo_no='WO/001',
            ret_qty=Decimal('10.50'),
            remarks='Test remarks for MRN 001',
            gen_by='EMP001',
            rate_max=Decimal('100.00'),
            rate_min=Decimal('80.00'),
            rate_avg=Decimal('90.00'),
            rate_actual=Decimal('95.00'),
            mrqn_date_str='10-01-2023',
            mrqn_no='MRQN/2023/001',
            accepted_qty=Decimal('12.00'),
            rejected_qty=Decimal('1.00'),
            gen_by2='EMP002'
        )
        cls.mrn_item_2 = MrnItem.objects.create(
            comp_id=1,
            item_code='ITEM002',
            description='Test Item B',
            uom='PCS',
            mrn_date_str='20-01-2023',
            mrn_no='MRN/2023/002',
            ret_qty=Decimal('5.00'),
            rate_max=Decimal('200.00'),
            rate_actual=Decimal('190.00'),
            mrqn_date_str='18-01-2023',
            mrqn_no='MRQN/2023/002',
            accepted_qty=Decimal('6.00')
        )

    def test_mrn_item_creation(self):
        self.assertEqual(self.mrn_item_1.item_code, 'ITEM001')
        self.assertEqual(self.mrn_item_1.mrn_no, 'MRN/2023/001')
        self.assertEqual(self.mrn_item_1.ret_qty, Decimal('10.50'))

    def test_mrn_date_property(self):
        self.assertEqual(self.mrn_item_1.mrn_date, date(2023, 1, 15))
        self.assertEqual(self.mrn_item_2.mrn_date, date(2023, 1, 20))
        # Test invalid date string
        self.mrn_item_1.mrn_date_str = 'invalid-date'
        self.assertIsNone(self.mrn_item_1.mrn_date)
        self.mrn_item_1.mrn_date_str = None
        self.assertIsNone(self.mrn_item_1.mrn_date)

    def test_get_mrn_rate(self):
        self.assertEqual(self.mrn_item_1.get_mrn_rate(1), Decimal('100.00')) # MAX
        self.assertEqual(self.mrn_item_1.get_mrn_rate(2), Decimal('80.00')) # MIN
        self.assertEqual(self.mrn_item_1.get_mrn_rate(3), Decimal('90.00')) # AVG
        self.assertEqual(self.mrn_item_1.get_mrn_rate(4), Decimal('95.00')) # Actual
        self.assertIsNone(self.mrn_item_1.get_mrn_rate(99)) # Invalid type

    def test_get_mrn_amount(self):
        self.assertEqual(self.mrn_item_1.get_mrn_amount(1), Decimal('100.00') * Decimal('10.50'))
        self.assertEqual(self.mrn_item_1.get_mrn_amount(4), Decimal('95.00') * Decimal('10.50'))
        # Test with None quantities
        self.mrn_item_1.ret_qty = None
        self.assertIsNone(self.mrn_item_1.get_mrn_amount(1))

    def test_get_mrqn_rate_and_amount(self):
        self.assertEqual(self.mrn_item_1.get_mrqn_rate(1), Decimal('100.00')) # Uses MRN rates
        self.assertEqual(self.mrn_item_1.get_mrqn_amount(1), Decimal('100.00') * Decimal('12.00'))

class MrqnItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.mrqn_item_1 = MrqnItem.objects.create(
            comp_id=1,
            item_code='ITEM003',
            description='Test Item C',
            uom='LTR',
            mrqn_date_str='25-02-2023',
            mrqn_no='MRQN/2023/003',
            accepted_qty=Decimal('20.00'),
            rejected_qty=Decimal('2.00'),
            emp_name='EMP003',
            rate_max=Decimal('50.00'),
            rate_min=Decimal('40.00'),
            rate_avg=Decimal('45.00'),
            rate_actual=Decimal('48.00'),
            mrn_date_str='22-02-2023',
            mrn_no='MRN/2023/003',
            bg_group='GROUP_B',
            wo_no='WO/003',
            ret_qty=Decimal('18.00'),
            remarks='Test remarks for MRQN 003',
            emp_name2='EMP004'
        )

    def test_mrqn_item_creation(self):
        self.assertEqual(self.mrqn_item_1.item_code, 'ITEM003')
        self.assertEqual(self.mrqn_item_1.mrqn_no, 'MRQN/2023/003')
        self.assertEqual(self.mrqn_item_1.accepted_qty, Decimal('20.00'))

    def test_mrqn_date_property(self):
        self.assertEqual(self.mrqn_item_1.mrqn_date, date(2023, 2, 25))

    def test_get_mrqn_rate(self):
        self.assertEqual(self.mrqn_item_1.get_mrqn_rate(1), Decimal('50.00'))
        self.assertEqual(self.mrqn_item_1.get_mrqn_rate(4), Decimal('48.00'))

    def test_get_mrqn_amount(self):
        self.assertEqual(self.mrqn_item_1.get_mrqn_amount(1), Decimal('50.00') * Decimal('20.00'))

    def test_get_mrn_rate_and_amount(self):
        self.assertEqual(self.mrqn_item_1.get_mrn_rate(1), Decimal('50.00')) # Uses MRQN rates
        self.assertEqual(self.mrqn_item_1.get_mrn_amount(1), Decimal('50.00') * Decimal('18.00'))


class MrnReportViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create some test data for both MRN and MRQN views
        MrnItem.objects.create(
            comp_id=1, item_code='MRN_ITEM1', mrn_no='MRN/001', ret_qty=Decimal('10'),
            mrn_date_str='01-01-2023', rate_actual=Decimal('10.00'), gen_by='UserA'
        )
        MrnItem.objects.create(
            comp_id=1, item_code='MRN_ITEM2', mrn_no='MRN/002', ret_qty=Decimal('20'),
            mrn_date_str='15-01-2023', rate_actual=Decimal('15.00'), gen_by='UserB'
        )
        MrqnItem.objects.create(
            comp_id=1, item_code='MRQN_ITEM1', mrqn_no='MRQN/001', accepted_qty=Decimal('15'),
            mrqn_date_str='05-01-2023', rate_actual=Decimal('20.00'), emp_name='UserC'
        )
        MrqnItem.objects.create(
            comp_id=1, item_code='MRQN_ITEM2', mrqn_no='MRQN/002', accepted_qty=Decimal('25'),
            mrqn_date_str='20-01-2023', rate_actual=Decimal('25.00'), emp_name='UserD'
        )
        # Assuming a user/session setup for comp_id if it's used in the view
        # self.client.session['compid'] = 1

    def test_mrn_report_page_get(self):
        response = self.client.get(reverse('mrn_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/mrn_report_page.html')
        self.assertIsInstance(response.context['form'], MrnReportForm)

    def test_mrn_report_table_partial_view_get_mrn(self):
        # Test initial load of MRN table
        params = {
            'mrn_type': '1',
            'selected_columns_mrn': ['item_code', 'mrn_no', 'ret_qty', 'mrn_amount'],
            'get_po_rate': '4' # Actual rate
        }
        response = self.client.get(reverse('mrn_report_table'), params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/_mrn_report_table.html')
        self.assertIn('MRN/001', response.content.decode())
        self.assertIn('MRN/002', response.content.decode())
        self.assertContains(response, 'Item Code')
        self.assertContains(response, 'MRN No')
        self.assertContains(response, 'Ret Qty')
        self.assertContains(response, 'Amount')
        self.assertContains(response, '100.00') # 10 * 10
        self.assertContains(response, '300.00') # 20 * 15

    def test_mrn_report_table_partial_view_get_mrqn(self):
        # Test initial load of MRQN table
        params = {
            'mrn_type': '2',
            'selected_columns_mrn': ['item_code', 'mrqn_no', 'accepted_qty', 'mrqn_amount'],
            'get_po_rate': '4' # Actual rate
        }
        response = self.client.get(reverse('mrn_report_table'), params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_reports/_mrn_report_table.html')
        self.assertIn('MRQN/001', response.content.decode())
        self.assertIn('MRQN/002', response.content.decode())
        self.assertContains(response, 'Item Code')
        self.assertContains(response, 'MRQN No')
        self.assertContains(response, 'Acept Qty')
        self.assertContains(response, 'Amount')
        self.assertContains(response, '300.00') # 15 * 20
        self.assertContains(response, '625.00') # 25 * 25

    def test_mrn_report_table_partial_view_with_filters(self):
        # Test MRN report with item code filter
        params = {
            'mrn_type': '1',
            'item_code': 'MRN_ITEM1',
            'selected_columns_mrn': ['item_code', 'mrn_no']
        }
        response = self.client.get(reverse('mrn_report_table'), params)
        self.assertEqual(response.status_code, 200)
        self.assertIn('MRN/001', response.content.decode())
        self.assertNotIn('MRN/002', response.content.decode())

        # Test MRN report with date filter (assuming strftime for range works)
        params_date = {
            'mrn_type': '1',
            'from_date': '2023-01-14',
            'to_date': '2023-01-16',
            'date_filter_type': '1',
            'selected_columns_mrn': ['mrn_no']
        }
        response_date = self.client.get(reverse('mrn_report_table'), params_date)
        self.assertEqual(response_date.status_code, 200)
        self.assertIn('MRN/002', response_date.content.decode())
        self.assertNotIn('MRN/001', response_date.content.decode()) # 01-01-2023 is outside range

    def test_mrn_report_view_form_submission_htmx(self):
        # Test form submission via HTMX (simulated via POST to form_valid, expects 204)
        data = {
            'mrn_type': '1',
            'item_code': 'MRN_ITEM1',
            'selected_columns_mrn': ['item_code', 'mrn_no'],
            'get_po_rate': '4'
        }
        response = self.client.post(reverse('mrn_report'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMrnReportTable')

    def test_mrn_report_view_form_invalid(self):
        # Test invalid form submission (e.g., to_date < from_date)
        data = {
            'mrn_type': '1',
            'from_date': '2023-01-10',
            'to_date': '2023-01-01',
            'date_filter_type': '1',
            'selected_columns_mrn': ['item_code']
        }
        response = self.client.post(reverse('mrn_report'), data)
        self.assertEqual(response.status_code, 200) # Stays on same page, re-renders with errors
        self.assertContains(response, 'From Date cannot be after To Date.')
        self.assertIsInstance(response.context['form'], MrnReportForm)
        self.assertTrue(response.context['form'].errors)

    @patch('pandas.ExcelWriter')
    @patch('pandas.DataFrame.to_excel')
    def test_mrn_report_export_view_mrn(self, mock_to_excel, mock_excel_writer):
        # Test export functionality for MRN
        params = {
            'mrn_type': '1',
            'item_code': 'MRN_ITEM1',
            'selected_columns_mrn': ['item_code', 'mrn_no', 'mrn_amount'],
            'get_po_rate': '4'
        }
        response = self.client.get(reverse('mrn_report_export'), params)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertTrue(response['Content-Disposition'].startswith('attachment; filename="report_'))
        mock_to_excel.assert_called_once()
        # Verify columns passed to DataFrame
        mock_to_excel.call_args[0][0].columns.tolist() # This would be the DataFrame, check its columns
        self.assertIn('Item Code', mock_to_excel.call_args[0][0].columns.tolist())
        self.assertIn('MRN No', mock_to_excel.call_args[0][0].columns.tolist())
        self.assertIn('Amount', mock_to_excel.call_args[0][0].columns.tolist())

    @patch('pandas.ExcelWriter')
    @patch('pandas.DataFrame.to_excel')
    def test_mrn_report_export_view_mrqn(self, mock_to_excel, mock_excel_writer):
        # Test export functionality for MRQN
        params = {
            'mrn_type': '2',
            'item_code': 'MRQN_ITEM1',
            'selected_columns_mrn': ['item_code', 'mrqn_no', 'mrqn_amount'],
            'get_po_rate': '4'
        }
        response = self.client.get(reverse('mrn_report_export'), params)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        mock_to_excel.assert_called_once()
        self.assertIn('Item Code', mock_to_excel.call_args[0][0].columns.tolist())
        self.assertIn('MRQN No', mock_to_excel.call_args[0][0].columns.tolist())
        self.assertIn('Amount', mock_to_excel.call_args[0][0].columns.tolist())

    def test_mrn_report_export_no_records(self):
        # Test export when no records are found
        params = {
            'mrn_type': '1',
            'item_code': 'NON_EXISTENT_ITEM',
            'selected_columns_mrn': ['item_code']
        }
        response = self.client.get(reverse('mrn_report_export'), params)
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.content.decode(), "No records to export.")

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Form Submission and Table Updates:**
    *   The main form (`#report-form`) uses `hx-get` to `{% url 'mrn_report_table' %}` with `hx-target="#report-table-container"` and `hx-swap="innerHTML"`. This ensures that when the "Show Report" button is clicked (or form submitted), only the table portion of the page is reloaded.
    *   `hx-trigger="load, refreshMrnReportTable from:body"` on `#report-table-container` ensures the table loads initially and also refreshes whenever a `refreshMrnReportTable` event is triggered on the body (e.g., after the main form is submitted, the `MrnReportView` returns `HX-Trigger` to fire this event).
    *   Loading spinners and opacity changes are handled via `_` (hyperscript) for better UX during HTMX requests.
    *   The `Export To Excel` button is a standard `<a>` tag with `target="_blank"` to trigger a file download without interrupting the current page. It passes all current form parameters as query string to the export view.

*   **Alpine.js for UI State Management:**
    *   `x-data="{ mrnType: '{{ form.mrn_type.value }}', checkAll: false }"` is applied to the main `div` to manage the `mrnType` state (for conditional display of labels/column choices) and the `checkAll` checkbox state.
    *   `x-model="mrnType"` on the `mrn_type` select input automatically updates the `mrnType` variable.
    *   `@change` event on the `mrn_type` select input triggers a full page reload with updated query parameters. This is because the `form.fields['selected_columns_mrn'].choices` logic is in `__init__` which runs only on page load. While technically possible to make this truly dynamic with HTMX and partials, a full page reload simplifies the dynamic form field choices based on the report type (`MRN_TYPE_CHOICES`).
    *   `x-model="checkAll"` on the "Check All" checkbox toggles the `checkAll` variable.
    *   `@change` on "Check All" checkbox dynamically checks/unchecks all other column selection checkboxes using Alpine.js's DOM manipulation features.

*   **DataTables for List Views:**
    *   The `_mrn_report_table.html` partial contains the `<table id="reportTable" ...>`.
    *   JavaScript in `extra_js` block (within `mrn_report_page.html`) initializes DataTables on `#reportTable` after each HTMX swap (`htmx:afterSwap` event).
    *   It includes common DataTables options for pagination, length menu, destroying previous instances, and horizontal/vertical scrolling to mimic the ASP.NET `Panel` behavior.

By following these steps, the legacy ASP.NET page is systematically transformed into a modern, responsive, and efficient Django application utilizing best practices and contemporary frontend technologies.