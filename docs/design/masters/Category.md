The ASP.NET application provides a master-detail view for managing "Item Categories," including functionality for viewing, adding, editing, and deleting records. This migration plan outlines a comprehensive transition to a modern Django application, leveraging Django's Model-View-Template (MVT) pattern, HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET `SqlDataSource1` explicitly defines the database table and its interaction commands.

-   **Table Name:** `tblDG_Category_Master`
-   **Columns:**
    -   `CId`: This acts as the primary key (`DataKeyNames="CId"`) and is implicitly an integer, likely auto-incrementing in the database.
    -   `CName`: This maps to the "Description" field in the UI and is a string (`Type="String"` in `InsertParameters` and `UpdateParameters`). It is a required field.
    -   `Symbol`: This maps to the "Symbol" field in the UI and is also a string. It is a required field.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET code demonstrates a complete set of Create, Read, Update, and Delete (CRUD) operations for item categories.

-   **Create (Insert):** New category records are added via the "Insert" button found in the `GridView`'s footer or empty data template. The `GridView1_RowCommand` method in the C# code-behind handles retrieving "Description" (`CName`) and "Symbol" values from textboxes and then uses `SqlDataSource1.Insert()` to persist the data.
-   **Read (Select):** The `GridView1` populates its data by executing `SqlDataSource1.SelectCommand="SELECT * FROM [tblDG_Category_Master] Order by [CId] desc"`. This displays all existing category records, ordered by `CId` in descending order, with built-in pagination.
-   **Update (Edit):** Existing records can be modified using the "Edit" command button in each `GridView` row. The editable fields, "Description" (`CName`) and "Symbol`, are updated through `SqlDataSource1.UpdateCommand`. The `GridView1_RowUpdated` method confirms the update.
-   **Delete (Delete):** Records can be removed using the "Delete" command button. The `SqlDataSource1.DeleteCommand` handles the database deletion. The `GridView1_RowDeleted` method confirms the deletion.

**Validation:** Both "Description" (`CName`) and "Symbol" fields are validated as required fields using `asp:RequiredFieldValidator` controls during both insert and update operations.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The user interface is centered around the `asp:GridView` control, which acts as a dynamic table for displaying and managing category data.

-   **`asp:GridView` (`GridView1`):** This is the primary control, presenting categories in a tabular format. It provides:
    -   **Pagination:** Displays data in pages (20 items per page).
    -   **Inline Editing:** Allows users to modify `Description` and `Symbol` directly within the table rows.
    -   **Inline Deletion:** Provides a button to remove individual records.
    -   **Footer for Insertion:** A dedicated section at the bottom of the table to input new category details and trigger an insert operation.
    -   **Empty Data Template:** Offers an alternative input method when the table is empty.
-   **`asp:TextBox` controls:** Used for inputting (`txtDescription`, `txtSymbol`) and editing (`lblDescription0`, `lblSymbol0`) the category's name and symbol.
-   **`asp:Label` controls:** Used for displaying static text and data (`lblDescription`, `lblSymbol`, `lblID`) within the grid and for showing status messages (`lblMessage`).
-   **`asp:Button` and `asp:LinkButton` controls:** Trigger actions such as "Insert," "Edit," and "Delete."
-   **Client-Side Scripting:** The ASP.NET code uses `OnClientClick` attributes and `GridView1_RowDataBound` to attach JavaScript functions (e.g., `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) for client-side confirmation dialogs. This functionality will be replaced by HTMX and Alpine.js for a more modern and integrated frontend experience.

## Step 4: Generate Django Code

We will create a new Django application named `design_master` to host the converted Category management module.

### 4.1 Models

This `Category` model directly maps to the existing `tblDG_Category_Master` table. The `managed = False` setting is critical to ensure Django doesn't try to alter the existing database schema.

```python
# design_master/models.py
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class Category(models.Model):
    """
    Represents an Item Category from the existing tblDG_Category_Master table.
    Maps CName to 'name' and Symbol to 'symbol'.
    """
    # CId is the primary key in the existing database. Django's default 'id' field
    # (which is an auto-incrementing PK) will implicitly map to CId for managed=False tables.
    # If explicit mapping or a different PK type were needed, it would be defined here,
    # e.g., cid = models.IntegerField(db_column='CId', primary_key=True).

    name = models.CharField(db_column='CName', max_length=255, verbose_name=_("Description"))
    symbol = models.CharField(db_column='Symbol', max_length=50, verbose_name=_("Symbol"))

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (crucial for existing DBs)
        db_table = 'tblDG_Category_Master' # Exact name of the table in the database
        verbose_name = _('Item Category')
        verbose_name_plural = _('Item Categories')
        ordering = ['-id'] # Default order by CId (Django's 'id') descending, matching ASP.NET.

    def __str__(self):
        """Returns the name of the category for easy identification."""
        return self.name

    def clean(self):
        """
        Custom validation for Category fields, equivalent to ASP.NET's RequiredFieldValidator.
        Ensures 'name' and 'symbol' are not empty.
        """
        if not self.name or not self.name.strip():
            raise ValidationError({'name': _("Description cannot be empty.")})
        if not self.symbol or not self.symbol.strip():
            raise ValidationError({'symbol': _("Symbol cannot be empty.")})

    def save(self, *args, **kwargs):
        """
        Overrides the default save method to run full validation before saving.
        """
        self.full_clean() # Ensures model's clean() method and field validations are run
        super().save(*args, **kwargs)

    def get_display_info(self):
        """
        Example of a 'fat model' method: Provides a formatted string for display.
        """
        return f"{self.name} ({self.symbol})"

```

### 4.2 Forms

A Django `ModelForm` is created to handle input and validation for the `Category` model. It includes Tailwind CSS classes for proper styling.

```python
# design_master/forms.py
from django import forms
from .models import Category

class CategoryForm(forms.ModelForm):
    """
    Form for creating and updating Category objects.
    Applies styling with Tailwind CSS and sets fields as required.
    """
    class Meta:
        model = Category
        fields = ['name', 'symbol'] # Fields corresponding to CName and Symbol
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter category description',
                'autocomplete': 'off'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'Enter category symbol',
                'autocomplete': 'off'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Mark fields as required, though validation is also handled by model's clean() method
        self.fields['name'].required = True
        self.fields['symbol'].required = True

```

### 4.3 Views

These Django Class-Based Views (CBVs) handle the CRUD operations. They are designed to be thin, with business logic residing in the model, and to integrate seamlessly with HTMX for dynamic content updates.

```python
# design_master/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404

from .models import Category
from .forms import CategoryForm

class CategoryListView(ListView):
    """
    Renders the main page for displaying item categories.
    The actual table content is loaded dynamically via HTMX.
    """
    model = Category
    template_name = 'design_master/category/list.html'
    context_object_name = 'categories' # The name for the list of objects in the context

    # This view is minimal, primarily serving the base HTML structure.
    # Data is fetched by CategoryTablePartialView.

class CategoryTablePartialView(ListView):
    """
    Renders only the table portion of the category list, intended for HTMX requests.
    This view provides the data for DataTables.
    """
    model = Category
    template_name = 'design_master/category/_category_table.html'
    context_object_name = 'categories' # The name for the list of objects in the context

    def get_queryset(self):
        """Returns the queryset for the category table, ordered as in ASP.NET."""
        return Category.objects.all().order_by('-id')

class CategoryCreateView(CreateView):
    """
    Handles the creation of new Category objects.
    Responds with HTMX triggers for modal management and list refresh.
    """
    model = Category
    form_class = CategoryForm
    template_name = 'design_master/category/_category_form.html' # Uses a partial template for modal forms
    success_url = reverse_lazy('category_list') # Fallback URL for non-HTMX requests

    def form_valid(self, form):
        """Handles successful form submission, saves the object, and sends HTMX triggers."""
        response = super().form_valid(form)
        messages.success(self.request, 'Item Category added successfully.')
        if self.request.headers.get('HX-Request'):
            # Send 204 No Content with HTMX triggers to close modal and refresh list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCategoryList, closeModal'
                }
            )
        return response

    def form_invalid(self, form):
        """Handles invalid form submission by re-rendering the form with errors."""
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the rendered form HTML with errors, HTMX swaps it back
            return response
        return response

class CategoryUpdateView(UpdateView):
    """
    Handles the updating of existing Category objects.
    Responds with HTMX triggers for modal management and list refresh.
    """
    model = Category
    form_class = CategoryForm
    template_name = 'design_master/category/_category_form.html' # Uses a partial template for modal forms
    context_object_name = 'category'
    success_url = reverse_lazy('category_list') # Fallback URL for non-HTMX requests

    def form_valid(self, form):
        """Handles successful form submission, saves the object, and sends HTMX triggers."""
        response = super().form_valid(form)
        messages.success(self.request, 'Item Category updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCategoryList, closeModal'
                }
            )
        return response
    
    def form_invalid(self, form):
        """Handles invalid form submission by re-rendering the form with errors."""
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response


class CategoryDeleteView(DeleteView):
    """
    Handles the deletion of Category objects.
    Responds with HTMX triggers for modal management and list refresh.
    """
    model = Category
    template_name = 'design_master/category/_category_confirm_delete.html' # Uses a partial template for modal confirmation
    context_object_name = 'category'
    success_url = reverse_lazy('category_list') # Fallback URL for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        """Handles deletion and sends HTMX triggers for success."""
        try:
            response = super().delete(request, *args, **kwargs)
            messages.success(self.request, 'Item Category deleted successfully.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshCategoryList, closeModal'
                    }
                )
            return response
        except Http404:
            # Handle cases where the object to delete isn't found
            messages.error(self.request, 'Item Category not found for deletion.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=404, # Not Found
                    headers={
                        'HX-Trigger': 'closeModal' # Only close the modal on error
                    }
                )
            raise # Re-raise for non-HTMX requests to return proper HTTP response

    def get_context_data(self, **kwargs):
        """Adds category name to context for confirmation message."""
        context = super().get_context_data(**kwargs)
        # Ensure object_name is available for the confirmation message
        context['object_name'] = self.get_object().name
        return context

```

### 4.4 Templates

The templates are structured to maximize reusability and optimize for HTMX interactions. The `list.html` acts as the main page, while `_category_table.html`, `_category_form.html`, and `_category_confirm_delete.html` are partials loaded dynamically.

```html
{# design_master/templates/design_master/category/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-3xl font-extrabold text-gray-900">Item Categories Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105"
            hx-get="{% url 'category_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
            Add New Item Category
        </button>
    </div>
    
    <div id="categoryTable-container"
         hx-trigger="load, refreshCategoryList from:body"
         hx-get="{% url 'category_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-xl rounded-lg p-6 animate-fade-in">
        {# Initial loading state for HTMX #}
        <div class="flex flex-col items-center justify-center h-48 text-gray-600">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500 mb-3"></div>
            <p class="text-lg">Loading item categories...</p>
        </div>
    </div>
    
    {# Global modal for all CRUD forms/confirmations #}
    <div id="modal"
         class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 transition-all duration-300 ease-in-out hidden opacity-0"
         _="on closeModal from body remove .flex from me then remove .opacity-100 from me then remove .scale-100 from #modalContent and add .scale-95 to #modalContent"
         x-data="{ show: false }" {# Alpine.js for fallback modal visibility control #}
         x-show="show"
         x-init="$watch('show', value => { 
             if (!value) { setTimeout(() => $el.classList.add('hidden'), 300); } 
             else { $el.classList.remove('hidden'); } 
         })"
         @click.self="show = false"> {# Click outside to close #}
        <div id="modalContent"
             class="bg-white p-8 rounded-xl shadow-2xl max-w-2xl w-full transform scale-95 transition-all duration-300 ease-in-out"
             @click.outside="show = false"> {# Click outside to close #}
            {# HTMX-loaded form or confirmation content will go here #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# DataTables CSS and JS (assuming Tailwind CSS is globally configured) #}
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.0/css/dataTables.dataTables.min.css">
<script src="https://cdn.datatables.net/2.0.0/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.0/js/dataTables.tailwindcss.min.js"></script>

<script>
    // Alpine.js integration to control modal visibility based on HTMX events
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            // When modal content is loaded, ensure the modal is visible
            let modal = document.getElementById('modal');
            let modalContent = document.getElementById('modalContent');
            modal.classList.add('flex');
            modal.classList.remove('hidden');
            // Force reflow for transition to apply
            void modal.offsetWidth; 
            modal.classList.add('opacity-100');
            modalContent.classList.remove('scale-95');
            modalContent.classList.add('scale-100');

            // Set Alpine's show state to true if available
            if (modal.__alpine && modal.__alpine.$data) {
                modal.__alpine.$data.show = true;
            }
        }
    });

    // Custom event listener for 'closeModal' triggered by HTMX
    document.body.addEventListener('closeModal', function() {
        let modal = document.getElementById('modal');
        let modalContent = document.getElementById('modalContent');
        
        modal.classList.remove('opacity-100');
        modalContent.classList.remove('scale-100');
        modalContent.classList.add('scale-95');

        // If Alpine is managing, set its state to false
        if (modal.__alpine && modal.__alpine.$data) {
            modal.__alpine.$data.show = false;
        } else {
            // Fallback for direct DOM manipulation if Alpine not in control
            setTimeout(() => {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
                modalContent.innerHTML = ''; // Clear modal content after closing
            }, 300); // Match CSS transition duration
        }
    });

    // DataTables initialization function
    function initializeDataTable() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#categoryTable')) {
            $('#categoryTable').DataTable().destroy();
        }
        $('#categoryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "search": "Search:",
                "paginate": {
                    "next": "Next",
                    "previous": "Previous"
                }
            },
            "responsive": true // Make table responsive
        });
    }

    // Initialize DataTables when HTMX swaps in the table content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'categoryTable-container') {
            initializeDataTable();
        }
    });

    // Triggered by HTMX after successful CRUD operation to refresh table
    document.body.addEventListener('refreshCategoryList', function() {
        // HTMX's hx-trigger="refreshCategoryList from:body" on categoryTable-container will re-fetch
        // and re-initialize the table. This listener is primarily for confirming cleanup.
        console.log('Category list refreshed.');
    });
</script>
{% endblock %}
```

```html
{# design_master/templates/design_master/category/_category_table.html #}
{# This template is loaded via HTMX into the 'categoryTable-container' div. #}
<div class="overflow-x-auto">
    <table id="categoryTable" class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for category in categories %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-800">{{ category.name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-800">{{ category.symbol }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm flex space-x-2">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out transform hover:scale-105"
                        hx-get="{% url 'category_edit' category.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Edit
                    </button>
                    <button
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out transform hover:scale-105"
                        hx-get="{% url 'category_delete' category.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="4" class="py-6 px-6 text-center text-gray-600">
                    No item categories found.
                    {# Provide an option to add from here if the table is empty, matching ASP.NET's EmptyDataTemplate #}
                    <button
                        class="mt-4 bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105"
                        hx-get="{% url 'category_add' %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                        Add First Item Category
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables is initialized by JavaScript in list.html after this content is swapped in. #}
```

```html
{# design_master/templates/design_master/category/_category_form.html #}
{# This template is loaded via HTMX into the modal's #modalContent div. #}
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-6 text-center">
        {{ form.instance.pk|yesno:'Edit,Add' }} Item Category
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg transition duration-150 ease-in-out"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                Save Item Category
            </button>
        </div>
        <div id="form-spinner" class="htmx-indicator mt-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Saving...</p>
        </div>
    </form>
</div>
```

```html
{# design_master/templates/design_master/category/_category_confirm_delete.html #}
{# This template is loaded via HTMX into the modal's #modalContent div. #}
<div class="p-6 text-center">
    <h3 class="text-2xl font-bold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">
        Are you sure you want to permanently delete the Item Category:
        <span class="font-semibold text-blue-700">"{{ object_name }}"</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'category_delete' category.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-lg transition duration-150 ease-in-out"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                Confirm Delete
            </button>
        </div>
        <div id="delete-spinner" class="htmx-indicator mt-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
            <p class="mt-2 text-gray-600">Deleting...</p>
        </div>
    </form>
</div>
```

### 4.5 URLs

This file defines the URL patterns for the `design_master` application, mapping URLs to the respective views.

```python
# design_master/urls.py
from django.urls import path
from .views import (
    CategoryListView,
    CategoryCreateView,
    CategoryUpdateView,
    CategoryDeleteView,
    CategoryTablePartialView
)

urlpatterns = [
    # Main page for Item Categories
    path('categories/', CategoryListView.as_view(), name='category_list'),
    
    # HTMX endpoint to fetch the DataTables content (table rows)
    path('categories/table/', CategoryTablePartialView.as_view(), name='category_table'),

    # HTMX endpoint to load the Add/Create form into a modal
    path('categories/add/', CategoryCreateView.as_view(), name='category_add'),
    
    # HTMX endpoint to load the Edit/Update form for a specific category into a modal
    path('categories/edit/<int:pk>/', CategoryUpdateView.as_view(), name='category_edit'),
    
    # HTMX endpoint to load the Delete confirmation for a specific category into a modal
    path('categories/delete/<int:pk>/', CategoryDeleteView.as_view(), name='category_delete'),
]

```

### 4.6 Tests

Comprehensive unit tests for the `Category` model validate its properties and custom methods, while integration tests verify the functionality of all `Category` views, including their interaction with HTMX.

```python
# design_master/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.http import HttpResponse

from .models import Category
from .forms import CategoryForm

class CategoryModelTest(TestCase):
    """
    Unit tests for the Category model, focusing on data integrity and custom methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test Category instance for use across all model tests
        cls.category_1 = Category.objects.create(name='Electronics', symbol='ELEC')
        cls.category_2 = Category.objects.create(name='Clothing', symbol='CLTH')

    def test_category_creation(self):
        """Verify Category object creation and field assignments."""
        self.assertEqual(self.category_1.name, 'Electronics')
        self.assertEqual(self.category_1.symbol, 'ELEC')
        self.assertTrue(isinstance(self.category_1, Category))
        self.assertEqual(Category.objects.count(), 2)

    def test_field_verbose_names(self):
        """Verify verbose names match expected labels."""
        # Get verbose name for 'name' field (mapped to 'Description')
        name_field = self.category_1._meta.get_field('name')
        self.assertEqual(name_field.verbose_name, 'Description')
        # Get verbose name for 'symbol' field
        symbol_field = self.category_1._meta.get_field('symbol')
        self.assertEqual(symbol_field.verbose_name, 'Symbol')

    def test_object_str_representation(self):
        """Verify __str__ method returns the category name."""
        self.assertEqual(str(self.category_1), 'Electronics')

    def test_default_ordering(self):
        """Verify categories are ordered by ID in descending order."""
        categories = list(Category.objects.all()) # Convert to list to check order
        self.assertEqual(categories[0], self.category_2) # category_2 (higher ID) should be first
        self.assertEqual(categories[1], self.category_1) # category_1 (lower ID) should be second

    def test_model_clean_method_required_fields(self):
        """Test custom model validation for required 'name' and 'symbol' fields."""
        # Test case: 'name' is empty
        category_no_name = Category(name='', symbol='SYM')
        with self.assertRaisesMessage(ValidationError, "Description cannot be empty."):
             category_no_name.full_clean()

        # Test case: 'name' is only whitespace
        category_whitespace_name = Category(name='   ', symbol='SYM')
        with self.assertRaisesMessage(ValidationError, "Description cannot be empty."):
             category_whitespace_name.full_clean()

        # Test case: 'symbol' is empty
        category_no_symbol = Category(name='Valid Name', symbol='')
        with self.assertRaisesMessage(ValidationError, "Symbol cannot be empty."):
            category_no_symbol.full_clean()
        
        # Test case: 'symbol' is only whitespace
        category_whitespace_symbol = Category(name='Valid Name', symbol='   ')
        with self.assertRaisesMessage(ValidationError, "Symbol cannot be empty."):
            category_whitespace_symbol.full_clean()

        # Test case: Both 'name' and 'symbol' are empty
        category_both_empty = Category(name='', symbol='')
        with self.assertRaises(ValidationError) as cm:
            category_both_empty.full_clean()
        self.assertIn('name', cm.exception.message_dict)
        self.assertIn('symbol', cm.exception.message_dict)

    def test_get_display_info_method(self):
        """Test the custom 'get_display_info' method."""
        expected_info = "Electronics (ELEC)"
        self.assertEqual(self.category_1.get_display_info(), expected_info)


class CategoryViewsTest(TestCase):
    """
    Integration tests for Category views, covering all CRUD operations and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        cls.category_alpha = Category.objects.create(name='Alpha Category', symbol='ALPH')
        cls.category_beta = Category.objects.create(name='Beta Category', symbol='BETA')

    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()

    def test_category_list_view_get(self):
        """Test the main category list page loads successfully."""
        response = self.client.get(reverse('category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_master/category/list.html')
        # Ensure DataTables related CSS/JS links are present in the base template (not directly tested here)
        self.assertContains(response, '<div id="categoryTable-container"')

    def test_category_table_partial_view_get(self):
        """Test the HTMX partial view for the DataTables content loads correctly."""
        response = self.client.get(reverse('category_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_master/category/_category_table.html')
        self.assertTrue('categories' in response.context)
        self.assertContains(response, self.category_alpha.name)
        self.assertContains(response, self.category_beta.name)
        # Verify default ordering
        self.assertEqual(list(response.context['categories']), [self.category_beta, self.category_alpha])

    def test_category_create_view_get(self):
        """Test GET request to load the 'add category' form into the modal via HTMX."""
        response = self.client.get(reverse('category_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_master/category/_category_form.html')
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], CategoryForm)
        self.assertContains(response, 'Add Item Category') # Check for form title

    def test_category_create_view_post_success(self):
        """Test successful POST request to create a new category via HTMX."""
        initial_count = Category.objects.count()
        data = {'name': 'New Product Type', 'symbol': 'NPT'}
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshCategoryList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        self.assertEqual(Category.objects.count(), initial_count + 1)
        self.assertTrue(Category.objects.filter(name='New Product Type', symbol='NPT').exists())
        
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), 'Item Category added successfully.')

    def test_category_create_view_post_invalid(self):
        """Test POST request with invalid data for creating a new category via HTMX."""
        initial_count = Category.objects.count()
        data = {'name': '', 'symbol': 'INV'} # Invalid data (empty name)
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'design_master/category/_category_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertContains(response, 'Description cannot be empty.')
        self.assertEqual(Category.objects.count(), initial_count) # No new object created

    def test_category_update_view_get(self):
        """Test GET request to load the 'edit category' form into the modal via HTMX."""
        response = self.client.get(reverse('category_edit', args=[self.category_alpha.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_master/category/_category_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.category_alpha)
        self.assertContains(response, 'Edit Item Category') # Check for form title
        self.assertContains(response, self.category_alpha.name)

    def test_category_update_view_post_success(self):
        """Test successful POST request to update an existing category via HTMX."""
        new_name = 'Updated Alpha'
        data = {'name': new_name, 'symbol': self.category_alpha.symbol}
        response = self.client.post(reverse('category_edit', args=[self.category_alpha.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshCategoryList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        self.category_alpha.refresh_from_db() # Reload the object to get updated values
        self.assertEqual(self.category_alpha.name, new_name)
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(str(messages_list[0]), 'Item Category updated successfully.')

    def test_category_update_view_post_invalid(self):
        """Test POST request with invalid data for updating a category via HTMX."""
        old_name = self.category_alpha.name
        data = {'name': '', 'symbol': self.category_alpha.symbol} # Invalid data (empty name)
        response = self.client.post(reverse('category_edit', args=[self.category_alpha.pk]), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_master/category/_category_form.html')
        self.assertTrue('form' in response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertContains(response, 'Description cannot be empty.')
        self.category_alpha.refresh_from_db() # Ensure name was not updated
        self.assertEqual(self.category_alpha.name, old_name)

    def test_category_delete_view_get(self):
        """Test GET request to load the delete confirmation into the modal via HTMX."""
        response = self.client.get(reverse('category_delete', args=[self.category_alpha.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_master/category/_category_confirm_delete.html')
        self.assertTrue('category' in response.context)
        self.assertEqual(response.context['category'], self.category_alpha)
        self.assertContains(response, f'Are you sure you want to permanently delete the Item Category: <span class="font-semibold text-blue-700">"{self.category_alpha.name}"</span>?')

    def test_category_delete_view_post_success(self):
        """Test successful POST request to delete a category via HTMX."""
        category_to_delete_pk = self.category_alpha.pk
        response = self.client.post(reverse('category_delete', args=[category_to_delete_pk]), HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshCategoryList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        self.assertFalse(Category.objects.filter(pk=category_to_delete_pk).exists())
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(str(messages_list[0]), 'Item Category deleted successfully.')

    def test_category_delete_view_post_not_found(self):
        """Test POST request to delete a non-existent category via HTMX."""
        non_existent_pk = 999
        response = self.client.post(reverse('category_delete', args=[non_existent_pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 404) # Django's DeleteView returns 404 for non-existent objects
        self.assertTrue('HX-Trigger' in response.headers) # HX-Trigger should still close the modal
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        messages_list = list(messages.get_messages(response.wsgi_request))
        self.assertEqual(str(messages_list[0]), 'Item Category not found for deletion.')

```

## Step 5: HTMX and Alpine.js Integration

The core of this modernization lies in creating a highly dynamic user experience using HTMX for server-side updates and Alpine.js for lightweight client-side state management, specifically for the modal. DataTables handles the client-side data presentation and interaction.

1.  **HTMX for Dynamic Content Loading:**
    *   **Main List View (`list.html`):** The initial page load renders a skeleton, and the actual category table content (`_category_table.html`) is fetched via an HTMX `hx-get` request to `{% url 'category_table' %}`. This request is triggered `on load` and whenever a `refreshCategoryList` event is fired. This ensures the table data is always fresh.
    *   **CRUD Modals:** "Add," "Edit," and "Delete" buttons on `list.html` and `_category_table.html` use `hx-get` to fetch their respective forms (`_category_form.html`) or confirmation messages (`_category_confirm_delete.html`) into a central `#modalContent` div.
    *   **Form Submissions:** All forms (`_category_form.html`, `_category_confirm_delete.html`) use `hx-post` to submit data. Upon successful submission, Django views return a `204 No Content` response, which is crucial for HTMX's `hx-swap="none"` directive. This indicates that HTMX should not swap any content but instead rely on HTTP headers for instructions.
    *   **`HX-Trigger` Headers:** After a successful CRUD operation, the Django views respond with `HX-Trigger: 'refreshCategoryList, closeModal'`.
        *   `refreshCategoryList`: This custom event is listened for by the `#categoryTable-container` (via `hx-trigger="load, refreshCategoryList from:body"`), causing it to re-fetch and update the category list table.
        *   `closeModal`: This custom event is listened for by the main modal container, triggering its closing animation and hiding.

2.  **Alpine.js for Modal Management:**
    *   The `#modal` container in `list.html` uses Alpine.js (`x-data`, `x-show`, `@click.self`, `@click.outside`) to manage its visibility and provide smooth transitions and click-outside-to-close functionality.
    *   JavaScript listeners within `list.html` bridge HTMX events (`htmx:afterSwap` for showing the modal after content loads, and the custom `closeModal` event) to Alpine.js's state (`x-show='true'/'false'`) or directly manipulate CSS classes for animation.

3.  **DataTables for List Presentation:**
    *   The `_category_table.html` partial contains the HTML structure for the table.
    *   The `list.html` template, in its `{% block extra_js %}` section, includes the DataTables JavaScript library.
    *   A JavaScript function `initializeDataTable()` is defined to apply DataTables to the loaded table. This function is called within an `htmx:afterSwap` event listener, ensuring DataTables is initialized only after the `_category_table.html` content has been dynamically loaded into the DOM by HTMX. This guarantees correct initialization on dynamically loaded content.
    *   The `pageLength` and `lengthMenu` options for DataTables match the ASP.NET GridView's pagination.

**Overall Workflow:**

The user navigates to the `/categories/` URL. `CategoryListView` renders the `list.html` template. Simultaneously, HTMX initiates a request to `/categories/table/`, which `CategoryTablePartialView` fulfills by rendering the `_category_table.html` containing the actual category data. Once loaded, DataTables is initialized on this table.

When the user clicks "Add," "Edit," or "Delete," an HTMX request is sent to the appropriate view, which renders the corresponding partial form/confirmation template into the modal. The user interacts with the form. Upon submission, HTMX POSTs the data. The Django view processes the request, saves/deletes the data, issues a success message, and returns a `204 No Content` response with `HX-Trigger` headers (`refreshCategoryList, closeModal`). HTMX then interprets these headers, closing the modal and triggering a refresh of the category list table, all without a full page reload, providing a seamless and responsive user experience.

## Final Notes

*   **Consistency:** The naming conventions and structure adhere to modern Django best practices (e.g., singular model names, lowercase plural for list URLs, `_partial.html` naming).
*   **DRY Principle:** Achieved by using a single modal for all CRUD operations and by separating the table rendering logic into a partial template.
*   **Scalability:** This architecture is highly scalable. New features or modules can be added with similar HTMX/Alpine.js patterns, minimizing frontend complexity and avoiding a full JavaScript framework.
*   **Maintainability:** Clear separation of concerns (models for business logic, thin views for orchestration, dedicated templates for UI, HTMX for interaction) makes the codebase easier to understand and maintain.
*   **Security:** Django's built-in CSRF protection is automatically included in forms.
*   **Accessibility:** Tailwind CSS provides a strong foundation for accessible UI components, and the use of semantic HTML further aids accessibility.