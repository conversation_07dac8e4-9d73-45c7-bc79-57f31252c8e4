## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code utilizes `tblDG_Item_Master` and refers to fields like `CId`, `ItemCode`, `ManfDesc`, `Location`, `CompId`, and `FinYearId`. It also mentions parameters like `Company` and `Address` which likely come from a `Company` related table. The core data comes from a stored procedure named `GetAllItem` which is filtered dynamically based on various input parameters.

**Inferred Schema:**

-   **Table Name:** `tblDG_Item_Master`
-   **Columns:**
    -   `ItemMasterId` (Primary Key, inferred)
    -   `CId` (Category ID, integer)
    -   `ItemCode` (string)
    -   `ManfDesc` (Manufacturer Description, string)
    -   `Location` (string)
    -   `CompId` (Company ID, integer)
    -   `FinYearId` (Financial Year ID, integer)
    -   *(Other columns from `tblDG_Item_Master` would be added upon full schema analysis)*

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page `ItemMaster_Print_Details.aspx` is exclusively a **Read** (report viewing) operation. It dynamically filters data based on query string parameters and session variables (`CompId`, `FinYearId`) and then renders this filtered data via Crystal Reports. There are no explicit Create, Update, or Delete operations on *this specific page*.

-   **Read (Primary):** Data is fetched using the `GetAllItem` stored procedure with various conditional filters based on `category`, `SearchCode`, `SearchItemCode`, `DrpTypeVal`, `DrpLocVal`, `CompId`, and `FinYearId`.
-   **Validation Logic:** Implicit validation is handled by the C# `try-catch` blocks and the conditional logic for building SQL parameters. Django forms and model managers will handle this.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET page uses `CrystalReportViewer` to display a pre-defined report (`ItemMaster.rpt`). The UI is minimal, focused on rendering the report. `loadingNotifier.js` suggests a simple loading indicator.

**Django Equivalent UI Strategy:**
-   **Main Display:** Replace the Crystal Report Viewer with an HTML table rendered in a Django template.
-   **Interactivity:** Use DataTables for client-side features like searching, sorting, and pagination.
-   **Dynamic Loading:** HTMX will load the table content dynamically based on user filters (if filtering controls were added to a parent page) or on initial load.
-   **Loading Indicator:** HTMX's built-in loading indicators will replace `loadingNotifier.js`.
-   **Styling:** Tailwind CSS.

*Note: While the original page has no direct input fields, a modernized Django page would typically include filter controls (dropdowns, text inputs) to allow users to replicate the dynamic filtering capabilities of the original C# code. For this plan, I'll assume these filters will be present on the main list view page.*

### Step 4: Generate Django Code

We will create a Django app named `design` for this module.

#### 4.1 Models (`design/models.py`)

The `ItemMasterManager` will encapsulate the complex data filtering logic found in the original C# `Fillgrid` method. We'll also define a placeholder `Company` model to handle company name and address lookups, mimicking `fun.getCompany` and `fun.CompAdd`.

```python
from django.db import models
from django.db.models import Q # For complex ORM queries

class ItemMasterManager(models.Manager):
    """
    Custom manager for ItemMaster to encapsulate the complex filtering logic
    from the original ASP.NET Fillgrid method and GetAllItem stored procedure.
    """
    def get_filtered_items(self, category_id, search_code_type, search_item_code_value, drp_type_val, drp_loc_val, comp_id, fy_id):
        # Initial queryset
        queryset = self.get_queryset()

        # Apply company and financial year filters always
        # C# had FinYearId <= FYId. Assuming financial year context.
        queryset = queryset.filter(company_id=comp_id, financial_year_id__lte=fy_id)

        # Helper for cleaning parameters
        def clean_param(param):
            # Treat "Select" string or empty string as None for filtering
            return None if param in ["Select", "", None] else param

        category_id = clean_param(category_id)
        search_code_type = clean_param(search_code_type)
        search_item_code_value = clean_param(search_item_code_value)
        drp_type_val = clean_param(drp_type_val)
        drp_loc_val = clean_param(drp_loc_val)

        # Build dynamic queries based on drp_type_val and search_code_type
        # This part mirrors the nested if-else logic from the original Fillgrid method.
        
        # Start with a base filter, if any common conditions are needed
        # current_filters = Q() # Not strictly needed if filters are always additive to base queryset

        if drp_type_val == "Category":
            if category_id:
                queryset = queryset.filter(category_id=category_id)

                if search_code_type:
                    if search_code_type == "tblDG_Item_Master.ItemCode":
                        if search_item_code_value:
                            queryset = queryset.filter(item_code__istartswith=search_item_code_value) # Case-insensitive startswith
                    elif search_code_type == "tblDG_Item_Master.ManfDesc":
                        if search_item_code_value:
                            queryset = queryset.filter(manf_desc__icontains=search_item_code_value) # Case-insensitive contains
                    elif search_code_type == "tblDG_Item_Master.Location":
                        if drp_loc_val:
                            queryset = queryset.filter(location=drp_loc_val)
            elif category_id is None and search_code_type is None and search_item_code_value:
                # This corresponds to the 'y' parameter in C# (ManfDesc search if other filters are "Select")
                queryset = queryset.filter(manf_desc__icontains=search_item_code_value)

        elif drp_type_val == "WOItems": # "Without Items" or similar specific filter type
            if search_code_type:
                if search_code_type == "tblDG_Item_Master.ItemCode":
                    if search_item_code_value:
                        queryset = queryset.filter(item_code__icontains=search_item_code_value) # Case-insensitive contains
                elif search_code_type == "tblDG_Item_Master.ManfDesc":
                    if search_item_code_value:
                        queryset = queryset.filter(manf_desc__icontains=search_item_code_value) # Case-insensitive contains
            elif search_code_type is None and search_item_code_value:
                # Similar fallback for WOItems type
                queryset = queryset.filter(manf_desc__icontains=search_item_code_value)
        
        # Add ordering for consistent results in DataTables
        return queryset.order_by('item_code') # Or another relevant field

class ItemMaster(models.Model):
    """
    Model representing the tblDG_Item_Master table.
    Fields inferred from ASP.NET code usage.
    """
    item_master_id = models.IntegerField(db_column='ItemMasterId', primary_key=True) # Assuming this is the PK
    category_id = models.IntegerField(db_column='CId', null=True, blank=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, null=True, blank=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, null=True, blank=True)
    location = models.CharField(db_column='Location', max_length=255, null=True, blank=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')

    objects = ItemMasterManager() # Attach the custom manager

    class Meta:
        managed = False # Do not manage schema via Django migrations
        db_table = 'tblDG_Item_Master' # Map to the existing database table
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"
    
    # Example business logic method (Fat Model)
    def get_full_description(self):
        """Returns a combined description of the item."""
        return f"Item Code: {self.item_code}, Description: {self.manf_desc}, Location: {self.location}"

# Placeholder for Company Master data, mimicking fun.getCompany and fun.CompAdd
class Company(models.Model):
    company_id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255)
    company_address = models.CharField(db_column='CompanyAddress', max_length=500)

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assumed table name for company details
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name

    def get_address(self):
        return self.company_address
```

#### 4.2 Forms (`design/forms.py`)

A form for `ItemMaster` to handle creation/updates, adhering to the general CRUD structure.

```python
from django import forms
from .models import ItemMaster

class ItemMasterForm(forms.ModelForm):
    class Meta:
        model = ItemMaster
        # Include all fields that would typically be editable
        fields = [
            'item_code', 
            'manf_desc', 
            'category_id', 
            'location', 
            'company_id', 
            'financial_year_id'
        ]
        widgets = {
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manf_desc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'category_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'item_code': 'Item Code',
            'manf_desc': 'Manufacturer Description',
            'category_id': 'Category ID',
            'location': 'Location',
            'company_id': 'Company ID',
            'financial_year_id': 'Financial Year ID',
        }
        
    def clean_category_id(self):
        category_id = self.cleaned_data.get('category_id')
        if category_id is not None and category_id < 0:
            raise forms.ValidationError("Category ID cannot be negative.")
        return category_id
```

#### 4.3 Views (`design/views.py`)

The `ItemMasterListView` will be the primary view for migrating the report display. A `TablePartialView` will handle HTMX requests for just the table. The other CRUD views are included for completeness based on the prompt's requirements.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import ItemMaster, Company # Import Company model
from .forms import ItemMasterForm

# Helper to get Company and Address details, mimicking fun.getCompany and fun.CompAdd
# In a real application, this would be a service layer or part of a Company model's methods.
def get_company_details(comp_id):
    try:
        company = Company.objects.get(company_id=comp_id)
        return {"name": company.company_name, "address": company.company_address}
    except Company.DoesNotExist:
        return {"name": "Unknown Company", "address": "N/A"}

class ItemMasterListView(ListView):
    model = ItemMaster
    template_name = 'design/itemmaster/list.html'
    context_object_name = 'item_masters' # Renamed for clarity in templates

    def get_queryset(self):
        # This view primarily serves the initial page load and the filter form
        # The actual data for the table comes from ItemMasterTablePartialView via HTMX
        return ItemMaster.objects.none() # Return an empty queryset for the initial load

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Assuming compid and finyear are available in session or from user profile
        # For demonstration, hardcoding or getting from request.GET if not from session.
        # In a real app, integrate with Django authentication/session system.
        comp_id = self.request.session.get('compid', 1) # Default to 1
        fy_id = self.request.session.get('finyear', 2024) # Default to 2024

        company_details = get_company_details(comp_id)
        context['company_name'] = company_details['name']
        context['company_address'] = company_details['address']
        return context

class ItemMasterTablePartialView(TemplateView):
    """
    Renders the DataTables table content for HTMX requests, including filtering.
    This replaces the Crystal Report data source functionality.
    """
    template_name = 'design/itemmaster/_itemmaster_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Retrieve parameters from request.GET, mirroring ASP.NET QueryString
        category = self.request.GET.get('category')
        search_code = self.request.GET.get('SearchCode')
        search_item_code = self.request.GET.get('SearchItemCode')
        drp_type_val = self.request.GET.get('DrpTypeVal')
        drp_loc_val = self.request.GET.get('DrpLocationVal') # Typo corrected from DrplocationVal

        # Retrieve session parameters (compid, finyear)
        # In a real application, these would come from the authenticated user's session
        comp_id = self.request.session.get('compid', 1) # Default to 1 for testing
        fy_id = self.request.session.get('finyear', 2024) # Default to 2024 for testing

        # Use the custom manager to get filtered data
        item_masters = ItemMaster.objects.get_filtered_items(
            category_id=category,
            search_code_type=search_code,
            search_item_code_value=search_item_code,
            drp_type_val=drp_type_val,
            drp_loc_val=drp_loc_val,
            comp_id=comp_id,
            fy_id=fy_id
        )
        
        context['item_masters'] = item_masters
        
        # Add company/address details for the report header if needed in the partial
        company_details = get_company_details(comp_id)
        context['company_name'] = company_details['name']
        context['company_address'] = company_details['address']

        return context

class ItemMasterCreateView(CreateView):
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'design/itemmaster/form.html'
    success_url = reverse_lazy('itemmaster_list') # Redirect to list view

    def get_initial(self):
        # Pre-fill company_id and financial_year_id from session/context
        initial = super().get_initial()
        initial['company_id'] = self.request.session.get('compid', 1)
        initial['financial_year_id'] = self.request.session.get('finyear', 2024)
        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item Master added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return 204 No Content with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

class ItemMasterUpdateView(UpdateView):
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'design/itemmaster/form.html'
    success_url = reverse_lazy('itemmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

class ItemMasterDeleteView(DeleteView):
    model = ItemMaster
    template_name = 'design/itemmaster/confirm_delete.html'
    success_url = reverse_lazy('itemmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

```

#### 4.4 Templates (`design/templates/design/itemmaster/`)

**`list.html`**: The main page that will house filter controls and the DataTables container.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Item Masters Report</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'itemmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Item Master
        </button>
    </div>

    <!-- Report Header mimicking Crystal Reports -->
    <div class="mb-6 p-4 border rounded-md bg-gray-50 shadow-sm">
        <p class="text-lg font-semibold text-gray-800">{{ company_name }}</p>
        <p class="text-sm text-gray-600">{{ company_address }}</p>
        <p class="text-md mt-2 text-gray-700">Item Master Details Report</p>
    </div>

    <!-- Filter/Search Controls (Add these if needed based on ASP.NET functionality) -->
    <div class="mb-6 p-4 border rounded-md bg-white shadow-sm" x-data="{
        category: '', searchCode: '', searchItemCode: '', drpType: 'Category', drpLocation: ''
    }">
        <h3 class="text-xl font-semibold mb-4">Filter Items</h3>
        <form hx-get="{% url 'itemmaster_table' %}" hx-target="#itemmasterTable-container" hx-swap="innerHTML" hx-indicator="#table-loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                <div>
                    <label for="id_drpType" class="block text-sm font-medium text-gray-700">Filter Type</label>
                    <select id="id_drpType" name="DrpTypeVal" x-model="drpType" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="Category">Category</option>
                        <option value="WOItems">WO Items</option>
                        <option value="Select">Select (No Type)</option>
                    </select>
                </div>
                <div x-show="drpType === 'Category'">
                    <label for="id_category" class="block text-sm font-medium text-gray-700">Category</label>
                    <input type="text" id="id_category" name="category" x-model="category" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter Category ID or Select">
                </div>
                <div>
                    <label for="id_searchCode" class="block text-sm font-medium text-gray-700">Search By</label>
                    <select id="id_searchCode" name="SearchCode" x-model="searchCode" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                        <option value="Select">Select Field</option>
                        <option value="tblDG_Item_Master.ItemCode">Item Code</option>
                        <option value="tblDG_Item_Master.ManfDesc">Manufacturer Description</option>
                        <option value="tblDG_Item_Master.Location">Location</option>
                    </select>
                </div>
                <div x-show="searchCode === 'tblDG_Item_Master.Location'">
                    <label for="id_drpLocation" class="block text-sm font-medium text-gray-700">Location</label>
                    <input type="text" id="id_drpLocation" name="DrpLocationVal" x-model="drpLocation" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter Location">
                </div>
                <div x-show="searchCode !== 'tblDG_Item_Master.Location'">
                    <label for="id_searchItemCode" class="block text-sm font-medium text-gray-700">Search Value</label>
                    <input type="text" id="id_searchItemCode" name="SearchItemCode" x-model="searchItemCode" class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" placeholder="Enter Search Value">
                </div>
            </div>
            <div class="mt-4 flex justify-end">
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>
    
    <div id="itemmasterTable-container"
         hx-trigger="load, refreshItemMasterList from:body"
         hx-get="{% url 'itemmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div id="table-loading-indicator" class="htmx-indicator text-center p-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Item Master Data...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is automatically initialized for x-data attributes.
        // No additional specific Alpine.js component initialization needed here for this simple form.
    });

    // Handle HTMX messages for toasts
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'messages-container') {
            setTimeout(() => {
                document.getElementById('messages-container').innerHTML = ''; // Clear messages after some time
            }, 5000);
        }
    });
</script>
{% endblock %}
```

**`_itemmaster_table.html`**: A partial template that renders the DataTables table. This is fetched via HTMX.

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="itemmasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manufacturer Description</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category ID</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Year ID</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in item_masters %}
            <tr class="hover:bg-gray-100">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.manf_desc }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.category_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.location }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.company_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.financial_year_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2"
                        hx-get="{% url 'itemmaster_edit' obj.item_master_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md"
                        hx-get="{% url 'itemmaster_delete' obj.item_master_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-gray-500">No Item Masters found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization must happen after the table is in the DOM
// and re-initialized if the table content is swapped by HTMX.
// HTMX fires htmx:afterSwap on its target.
document.addEventListener('htmx:afterSwap', function(event) {
    if (event.detail.target.id === 'itemmasterTable-container') {
        // Ensure DataTables is only initialized once or properly destroyed/reinitialized
        if ($.fn.DataTable.isDataTable('#itemmasterTable')) {
            $('#itemmasterTable').DataTable().destroy(); // Destroy previous instance
        }
        $('#itemmasterTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    }
});

// Initial load for DataTables, in case htmx:afterSwap isn't enough on first page load
$(document).ready(function() {
    if ($('#itemmasterTable').length && !$.fn.DataTable.isDataTable('#itemmasterTable')) {
        $('#itemmasterTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    }
});
</script>
```

**`form.html`**: Partial for Create/Update forms loaded in a modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Item Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit" onsubmit="return validateForm(this)">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                Save
            </button>
        </div>
    </form>
</div>

<script>
    // Basic client-side validation example with Alpine.js if needed, otherwise rely on Django forms
    function validateForm(form) {
        // Implement any specific client-side validation if necessary
        // For now, rely on Django's server-side validation and HTMX error handling
        return true; 
    }
</script>
```

**`confirm_delete.html`**: Partial for Delete confirmation loaded in a modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Item Master: <strong>{{ object.item_code }} - {{ object.manf_desc }}</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design/urls.py`)

```python
from django.urls import path
from .views import ItemMasterListView, ItemMasterCreateView, ItemMasterUpdateView, ItemMasterDeleteView, ItemMasterTablePartialView

urlpatterns = [
    path('itemmaster/', ItemMasterListView.as_view(), name='itemmaster_list'),
    path('itemmaster/table/', ItemMasterTablePartialView.as_view(), name='itemmaster_table'), # HTMX partial
    path('itemmaster/add/', ItemMasterCreateView.as_view(), name='itemmaster_add'),
    path('itemmaster/edit/<int:pk>/', ItemMasterUpdateView.as_view(), name='itemmaster_edit'),
    path('itemmaster/delete/<int:pk>/', ItemMasterDeleteView.as_view(), name='itemmaster_delete'),
]
```

#### 4.6 Tests (`design/tests/test_models.py`, `design/tests/test_views.py`)

```python
# design/tests/test_models.py
from django.test import TestCase
from .models import ItemMaster, Company

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company_1 = Company.objects.create(company_id=1, company_name="Test Company A", company_address="123 Test St")
        cls.company_2 = Company.objects.create(company_id=2, company_name="Test Company B", company_address="456 Test Ave")

        ItemMaster.objects.create(
            item_master_id=1, category_id=101, item_code='ITEM001', manf_desc='Widget Alpha', location='Warehouse1', company_id=1, financial_year_id=2023
        )
        ItemMaster.objects.create(
            item_master_id=2, category_id=101, item_code='ITEM002', manf_desc='Gadget Beta', location='Warehouse2', company_id=1, financial_year_id=2023
        )
        ItemMaster.objects.create(
            item_master_id=3, category_id=102, item_code='ITEM003', manf_desc='Tool Gamma', location='Warehouse1', company_id=1, financial_year_id=2023
        )
        ItemMaster.objects.create(
            item_master_id=4, category_id=103, item_code='ITEM004', manf_desc='Component Delta', location='Warehouse3', company_id=2, financial_year_id=2022
        )
        ItemMaster.objects.create(
            item_master_id=5, category_id=101, item_code='ITEM005', manf_desc='Widget Epsilon', location='Warehouse1', company_id=1, financial_year_id=2024
        )

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(item_master_id=1)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.manf_desc, 'Widget Alpha')
        self.assertEqual(item.company_id, 1)

    def test_get_full_description_method(self):
        item = ItemMaster.objects.get(item_master_id=1)
        expected_desc = "Item Code: ITEM001, Description: Widget Alpha, Location: Warehouse1"
        self.assertEqual(item.get_full_description(), expected_desc)

    # --- ItemMasterManager Filtering Tests ---

    def test_filter_by_company_and_financial_year(self):
        # Only items from comp_id=1 and FYId <= 2023
        filtered = ItemMaster.objects.get_filtered_items(
            category_id=None, search_code_type=None, search_item_code_value=None,
            drp_type_val=None, drp_loc_val=None, comp_id=1, fy_id=2023
        )
        self.assertEqual(filtered.count(), 3) # ITEM001, ITEM002, ITEM003

    def test_filter_by_category_type_and_category_id(self):
        # Category 101, type="Category", comp_id=1, FYId=2024
        filtered = ItemMaster.objects.get_filtered_items(
            category_id="101", search_code_type=None, search_item_code_value=None,
            drp_type_val="Category", drp_loc_val=None, comp_id=1, fy_id=2024
        )
        self.assertEqual(filtered.count(), 3) # ITEM001, ITEM002, ITEM005

    def test_filter_by_category_type_and_item_code_search(self):
        # Category 101, search by ItemCode starting with 'ITEM001', type="Category"
        filtered = ItemMaster.objects.get_filtered_items(
            category_id="101", search_code_type="tblDG_Item_Master.ItemCode", search_item_code_value="ITEM001",
            drp_type_val="Category", drp_loc_val=None, comp_id=1, fy_id=2024
        )
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first().item_code, 'ITEM001')

    def test_filter_by_category_type_and_manf_desc_search(self):
        # Category 101, search by ManfDesc containing 'Widget', type="Category"
        filtered = ItemMaster.objects.get_filtered_items(
            category_id="101", search_code_type="tblDG_Item_Master.ManfDesc", search_item_code_value="Widget",
            drp_type_val="Category", drp_loc_val=None, comp_id=1, fy_id=2024
        )
        self.assertEqual(filtered.count(), 2) # Widget Alpha, Widget Epsilon

    def test_filter_by_category_type_and_location_search(self):
        # Category 101, search by Location 'Warehouse1', type="Category"
        filtered = ItemMaster.objects.get_filtered_items(
            category_id="101", search_code_type="tblDG_Item_Master.Location", search_item_code_value=None,
            drp_type_val="Category", drp_loc_val="Warehouse1", comp_id=1, fy_id=2024
        )
        self.assertEqual(filtered.count(), 2) # ITEM001, ITEM005

    def test_fallback_manf_desc_search_when_category_select_and_search_code_select(self):
        # sd="Select", B="Select", s="Tool"
        filtered = ItemMaster.objects.get_filtered_items(
            category_id="Select", search_code_type="Select", search_item_code_value="Tool",
            drp_type_val="Category", drp_loc_val=None, comp_id=1, fy_id=2024
        )
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first().item_code, 'ITEM003')

    def test_filter_by_woitems_type_and_item_code_search(self):
        # drp_type_val="WOItems", search by ItemCode containing '004'
        filtered = ItemMaster.objects.get_filtered_items(
            category_id=None, search_code_type="tblDG_Item_Master.ItemCode", search_item_code_value="004",
            drp_type_val="WOItems", drp_loc_val=None, comp_id=2, fy_id=2022
        )
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first().item_code, 'ITEM004')

    def test_filter_by_woitems_type_and_manf_desc_search(self):
        # drp_type_val="WOItems", search by ManfDesc containing 'Gadget'
        filtered = ItemMaster.objects.get_filtered_items(
            category_id=None, search_code_type="tblDG_Item_Master.ManfDesc", search_item_code_value="Gadget",
            drp_type_val="WOItems", drp_loc_val=None, comp_id=1, fy_id=2024
        )
        self.assertEqual(filtered.count(), 1)
        self.assertEqual(filtered.first().item_code, 'ITEM002')

    def test_no_filters_applied_returns_base_queryset(self):
        # Only company and FY filters apply if no other search params
        filtered = ItemMaster.objects.get_filtered_items(
            category_id=None, search_code_type=None, search_item_code_value=None,
            drp_type_val=None, drp_loc_val=None, comp_id=1, fy_id=2024
        )
        self.assertEqual(filtered.count(), 4) # ITEM001, ITEM002, ITEM003, ITEM005 (all from comp_id 1)

    def test_company_model_details(self):
        company = Company.objects.get(company_id=1)
        self.assertEqual(company.company_name, "Test Company A")
        self.assertEqual(company.get_address(), "123 Test St")


# design/tests/test_views.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import ItemMaster, Company

class ItemMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        Company.objects.create(company_id=1, company_name="Test Company A", company_address="123 Test St")
        Company.objects.create(company_id=2, company_name="Test Company B", company_address="456 Test Ave")

        ItemMaster.objects.create(
            item_master_id=1, category_id=101, item_code='ITEM001', manf_desc='Widget Alpha', location='Warehouse1', company_id=1, financial_year_id=2023
        )
        ItemMaster.objects.create(
            item_master_id=2, category_id=102, item_code='ITEM002', manf_desc='Gadget Beta', location='Warehouse2', company_id=1, financial_year_id=2023
        )
        ItemMaster.objects.create(
            item_master_id=3, category_id=103, item_code='ITEM003', manf_desc='Tool Gamma', location='Warehouse1', company_id=2, financial_year_id=2022
        )
        
    def setUp(self):
        self.client = Client()
        # Set session data if needed for views that rely on it
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('itemmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemmaster/list.html')
        self.assertContains(response, 'Item Masters Report')
        self.assertContains(response, 'Test Company A') # Check for company details in context

    def test_table_partial_view_get_no_filters(self):
        response = self.client.get(reverse('itemmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemmaster/_itemmaster_table.html')
        self.assertTrue('item_masters' in response.context)
        self.assertEqual(response.context['item_masters'].count(), 2) # ITEMS 1, 2 from compid=1, FY <= 2024 (excluding 3 from compid 2)
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, 'ITEM002')
        self.assertNotContains(response, 'ITEM003')

    def test_table_partial_view_get_with_filters(self):
        # Mimic a complex ASP.NET filter query
        query_params = {
            'category': '101',
            'SearchCode': 'tblDG_Item_Master.ManfDesc',
            'SearchItemCode': 'Widget',
            'DrpTypeVal': 'Category',
            'DrpLocationVal': '', # No specific location filter
        }
        response = self.client.get(reverse('itemmaster_table'), query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemmaster/_itemmaster_table.html')
        self.assertTrue('item_masters' in response.context)
        self.assertEqual(response.context['item_masters'].count(), 1)
        self.assertContains(response, 'ITEM001')
        self.assertNotContains(response, 'ITEM002')
        
    def test_create_view_get(self):
        response = self.client.get(reverse('itemmaster_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemmaster/form.html')
        self.assertTrue('form' in response.context)
        # Check initial values from session
        self.assertEqual(response.context['form'].initial['company_id'], 1)
        self.assertEqual(response.context['form'].initial['financial_year_id'], 2024)

    def test_create_view_post_success(self):
        data = {
            'item_code': 'ITEM006',
            'manf_desc': 'New Item',
            'category_id': 105,
            'location': 'New Loc',
            'company_id': 1,
            'financial_year_id': 2024,
        }
        response = self.client.post(reverse('itemmaster_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on non-HTMX success
        self.assertTrue(ItemMaster.objects.filter(item_code='ITEM006').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Master added successfully.')

    def test_create_view_post_htmx_success(self):
        data = {
            'item_code': 'ITEM007',
            'manf_desc': 'HTMX Item',
            'category_id': 106,
            'location': 'HTMX Loc',
            'company_id': 1,
            'financial_year_id': 2024,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('itemmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(ItemMaster.objects.filter(item_code='ITEM007').exists())
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')

    def test_update_view_get(self):
        item = ItemMaster.objects.get(item_master_id=1)
        response = self.client.get(reverse('itemmaster_edit', args=[item.item_master_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemmaster/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.item_code, 'ITEM001')

    def test_update_view_post_success(self):
        item = ItemMaster.objects.get(item_master_id=1)
        data = {
            'item_code': 'ITEM001_UPDATED',
            'manf_desc': 'Updated Widget Alpha',
            'category_id': 101,
            'location': 'Warehouse1',
            'company_id': 1,
            'financial_year_id': 2023,
        }
        response = self.client.post(reverse('itemmaster_edit', args=[item.item_master_id]), data)
        self.assertEqual(response.status_code, 302)
        item.refresh_from_db()
        self.assertEqual(item.item_code, 'ITEM001_UPDATED')

    def test_delete_view_get(self):
        item = ItemMaster.objects.get(item_master_id=1)
        response = self.client.get(reverse('itemmaster_delete', args=[item.item_master_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/itemmaster/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].item_code, 'ITEM001')

    def test_delete_view_post_success(self):
        item = ItemMaster.objects.get(item_master_id=1)
        response = self.client.post(reverse('itemmaster_delete', args=[item.item_master_id]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(ItemMaster.objects.filter(item_master_id=1).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Master deleted successfully.')

    def test_delete_view_post_htmx_success(self):
        item = ItemMaster.objects.get(item_master_id=2)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('itemmaster_delete', args=[item.item_master_id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ItemMaster.objects.filter(item_master_id=2).exists())
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')

```

### Step 5: HTMX and Alpine.js Integration

The generated templates (`list.html`, `_itemmaster_table.html`, `form.html`, `confirm_delete.html`) demonstrate the integration:

-   **HTMX:**
    -   `hx-get`, `hx-post` for dynamic content loading and form submissions.
    -   `hx-target`, `hx-swap` to specify where and how HTML content is updated.
    -   `hx-trigger` to initiate requests (e.g., `load`, `click`, `refreshItemMasterList`).
    -   `HX-Trigger` headers (`status=204`) are used in views for cross-component communication (e.g., refreshing the list after a CRUD operation).
    -   `htmx-indicator` for visual feedback during AJAX requests.
-   **Alpine.js:**
    -   `x-data` is used in `list.html` for simple state management of filter form inputs.
    -   `x-show` conditionally displays elements based on Alpine.js state.
    -   `_=` (hyperscript) for minor DOM manipulations like showing/hiding modals.
-   **DataTables:**
    -   Initialized in `_itemmaster_table.html`'s `<script>` block.
    -   The script includes logic to destroy and re-initialize DataTables when HTMX swaps the table content, ensuring it always works correctly.
    -   `pageLength` and `lengthMenu` provide pagination and entry control.
    -   `responsive: true` is added for better mobile experience.
-   **No Full Page Reloads:** All CRUD and filtering interactions are designed to happen asynchronously via HTMX, updating only parts of the page.

### Final Notes

-   **Placeholders:** Replace `[TABLE_NAME]`, `[FRIENDLY_NAME]`, etc., with actual values derived from the ASP.NET database schema. For this example, I've used `tblDG_Item_Master` and `ItemMaster` as derived names.
-   **DRY Templates:** The use of `_itemmaster_table.html`, `form.html`, and `confirm_delete.html` as partials promotes DRY principles. `base.html` (assumed to exist) handles common layout elements.
-   **Fat Models, Thin Views:** The complex filtering logic from the original `Fillgrid` method is encapsulated within `ItemMasterManager` in `models.py`, keeping the views concise (typically 5-15 lines for core logic).
-   **Comprehensive Tests:** Unit tests cover model methods and the custom manager's filtering logic. Integration tests ensure views behave correctly, including HTMX interactions and message handling.
-   **Business Value:** This modernization shifts from a legacy, proprietary reporting system (Crystal Reports) to a modern, open-source web application using Django, HTMX, and Alpine.js. This reduces licensing costs, improves maintainability, enhances user experience with interactive web reports (search, sort, paginate), and provides a flexible foundation for future development, all while adhering to strong architectural principles and test coverage.