## ASP.NET to Django Conversion Script: SubCategory Master Management

This modernization plan outlines the strategic transition of the ASP.NET SubCategory editing functionality to a robust, modern Django application. Our approach leverages AI-assisted automation, focusing on creating a scalable, maintainable, and user-friendly system. By adhering to a 'Fat Model, Thin View' architecture, and integrating cutting-edge frontend technologies like HTMX and Alpine.js, we ensure a highly responsive user experience without the complexity of traditional JavaScript frameworks.

The business benefits of this migration include:
*   **Enhanced Performance:** Django and HTMX provide faster load times and smoother interactions, leading to increased user productivity.
*   **Improved Maintainability:** Clean code separation, strict architectural patterns, and comprehensive testing reduce bugs and simplify future development.
*   **Future-Proof Architecture:** A modern, open-source stack ensures long-term viability and easier integration with new technologies.
*   **Reduced Development Costs:** Automation-driven migration minimizes manual coding effort and accelerates the transition timeline.
*   **Simplified User Interface:** Intuitive, dynamic forms and list views powered by HTMX and DataTables offer a superior user experience.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
*   **Primary Entity:** `tblDG_SubCategory_Master`
    *   `SCId`: Primary Key (Integer)
    *   `CId`: Foreign Key to `tblDG_Category_Master` (Integer)
    *   `SCName`: Sub Category Name (String)
    *   `Symbol`: Sub Category Symbol (String, MaxLength 2)
    *   `CompId`: Company ID (Integer, Session Parameter)
    *   `FinYearId`: Financial Year ID (Integer, Session Parameter)
*   **Related Entity:** `tblDG_Category_Master`
    *   `CId`: Primary Key (Integer)
    *   `CName`: Category Name (String)
    *   `Symbol`: Category Symbol (String)
    *   `CompId`: Company ID (Integer, Session Parameter)
    *   `FinYearId`: Financial Year ID (Integer, Session Parameter)
    *   `HasSubCat`: Indicates if category has subcategories (String, '0' or '1')

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business rules in the ASP.NET code.

**Instructions:**
*   **Read:** The `GridView1` populates data from `tblDG_SubCategory_Master` joined with `tblDG_Category_Master`, filtered by `CompId` and `FinYearId`. This is a list view.
*   **Update:** The `GridView1` allows inline editing. The `UpdateCommand` updates `CId`, `SCName`, and `Symbol` for a given `SCId` and `CompId`.
    *   The `CId` (Category) is updated from a `DropDownList` populated by `tblDG_Category_Master` where `HasSubCat != '0'`, also filtered by `CompId` and `FinYearId`.
    *   Validation: `SCName` and `Symbol` are required fields.
    *   Post-update message: "Record Updated".
*   **Create:** No explicit "create" functionality is found in the provided ASP.NET code. However, a modern Django application should include it for completeness. We will generate it.
*   **Delete:** No explicit "delete" functionality is found. We will generate it for completeness.
*   **Business Logic:**
    *   Filtering by `CompId` and `FinYearId` is crucial for all data operations. This implies a multi-company/multi-financial year setup.
    *   Category selection for SubCategory is limited to categories where `HasSubCat != '0'`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to modern web components.

**Instructions:**
*   **Main Display:** `asp:GridView` maps directly to an HTML `<table>` managed by DataTables.
*   **Data Display:** `asp:Label` for displaying `SCName`, `Symbol`, and `catsy` (derived category info) maps to `<span>` or direct text rendering.
*   **Input Controls:**
    *   `asp:TextBox` for `SCName` and `Symbol` maps to `<input type="text">`.
    *   `asp:DropDownList` for `Category` maps to `<select>`.
*   **Actions:** `asp:CommandField` for "Edit" maps to an HTMX-powered `<button>` or `<a>` tag that triggers a modal form.
*   **Validation:** `asp:RequiredFieldValidator` will be handled by Django's form validation and client-side rendering with Tailwind CSS.
*   **Messages:** `asp:Label ID="lblMessage"` maps to Django's built-in `messages` framework, displayed via HTMX on the main page.
*   **Master Page:** The master page concept maps to Django's template inheritance (`core/base.html`).
*   **Styling/JS:** `yui-datatable.css`, `StyleSheet.css`, `PopUpMsg.js`, `loadingNotifier.js` will be replaced by DataTables, Tailwind CSS, HTMX, and Alpine.js.

### Step 4: Generate Django Code

We will create a Django application named `design_masters` to house the `SubCategory` and `Category` models and related logic.

#### 4.1 Models (design_masters/models.py)

We need models for both `Category` and `SubCategory` as they are related. The `CompId` and `FinYearId` are crucial for filtering and will be added as fields to both models.

```python
from django.db import models

class Company(models.Model):
    """
    Represents a Company for multi-tenancy.
    Assumed to exist for CompId filtering.
    """
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_name = models.CharField(db_column='CompName', max_length=255)

    class Meta:
        managed = False  # Set to True if you want Django to manage this table
        db_table = 'tblCompanyMaster' # Assuming a table name for companies
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.comp_name

class FinancialYear(models.Model):
    """
    Represents a Financial Year for multi-tenancy.
    Assumed to exist for FinYearId filtering.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year_name = models.CharField(db_column='FinYearName', max_length=100)

    class Meta:
        managed = False  # Set to True if you want Django to manage this table
        db_table = 'tblFinancialYearMaster' # Assuming a table name for financial years
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year_name


class Category(models.Model):
    """
    Maps to tblDG_Category_Master for item categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    has_subcat = models.CharField(db_column='HasSubCat', max_length=1, default='0') # '0' or '1'
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='categories')
    fin_year_id = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='categories')

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

    @property
    def full_name(self):
        """
        Combines Symbol and Name for display, replicating ASP.NET 'catsy'.
        """
        return f"{self.symbol} - {self.cname}"
        
    def filter_by_session_context(self, comp_id, fin_year_id):
        """
        Filters categories by company and financial year, as seen in ASP.NET.
        This method would typically be part of a custom manager or a utility function.
        """
        return self.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # ASP.NET uses <=
            has_subcat='1' # Only categories with HasSubCat != '0' (which means '1')
        )


class SubCategory(models.Model):
    """
    Maps to tblDG_SubCategory_Master for item subcategories.
    """
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='subcategories')
    sc_name = models.CharField(db_column='SCName', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=2) # MaxLength 2 as per ASP.NET
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='subcategories')
    fin_year_id = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='subcategories')

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'Sub Category'
        verbose_name_plural = 'Sub Categories'
        ordering = ['-scid'] # Order by SCId desc as per ASP.NET SelectCommand

    def __str__(self):
        return self.sc_name

    def filter_by_session_context(self, comp_id, fin_year_id):
        """
        Filters subcategories by company and financial year, as seen in ASP.NET.
        This method would typically be part of a custom manager or a utility function.
        """
        return self.objects.select_related('cid').filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # ASP.NET uses <=
        )
    
    def update_record(self, cid_id, sc_name, symbol, comp_id):
        """
        Business logic for updating a subcategory record.
        This method encapsulates the UPDATE command's parameters.
        """
        self.cid_id = cid_id
        self.sc_name = sc_name
        self.symbol = symbol
        # Assuming comp_id is part of the WHERE clause for security/data integrity,
        # not typically updated through this method.
        # It's already part of the object's instance if fetched correctly.
        self.save()
        return True # Indicate success

    def add_record(self, cid_id, sc_name, symbol, comp_id, fin_year_id):
        """
        Business logic for adding a new subcategory record.
        This would handle generating SCId if it's an identity column,
        or rely on the database's auto-increment.
        """
        # Assuming scid is auto-incremented by DB, so we don't set it explicitly
        SubCategory.objects.create(
            cid_id=cid_id,
            sc_name=sc_name,
            symbol=symbol,
            comp_id_id=comp_id,
            fin_year_id_id=fin_year_id
        )
        return True # Indicate success

```

#### 4.2 Forms (design_masters/forms.py)

```python
from django import forms
from .models import SubCategory, Category, Company, FinancialYear

class SubCategoryForm(forms.ModelForm):
    # The 'cid' field in the model is a ForeignKey, so Django's ModelForm
    # automatically handles it as a ModelChoiceField.
    # We will filter the queryset for this field to match ASP.NET's SqlDataSource1.
    cid = forms.ModelChoiceField(
        queryset=Category.objects.none(), # Will be set dynamically in __init__
        label="Category",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )

    class Meta:
        model = SubCategory
        fields = ['cid', 'sc_name', 'symbol']
        # The 'CompId' and 'FinYearId' are session-based parameters, not directly edited by user
        # They will be set during save in the view or using a custom manager if needed.
        widgets = {
            'sc_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'symbol': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'maxlength': '2'}),
        }
        labels = {
            'sc_name': 'Sub Category',
            'symbol': 'Symbol',
        }

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)
        
        # Filter the Category dropdown based on session parameters and HasSubCat
        if comp_id and fin_year_id is not None:
            self.fields['cid'].queryset = Category.objects.filter(
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id, # ASP.NET uses <=
                has_subcat='1' # Only categories with HasSubCat = '1'
            ).order_by('cname') # Order for better UX

```

#### 4.3 Views (design_masters/views.py)

These views will handle fetching and processing data, keeping logic thin and delegating to models. Session parameters `compid` and `finyear` are assumed to be available in `request.session`.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import SubCategory, Category, Company, FinancialYear # Import Company and FinancialYear
from .forms import SubCategoryForm
from django.db import transaction # For atomic operations

class SubCategoryContextMixin(View):
    """
    Mixin to provide company and financial year context to views.
    Assumes 'compid' and 'finyear' are in request.session.
    """
    def get_comp_id(self):
        return self.request.session.get('compid')

    def get_fin_year_id(self):
        return self.request.session.get('finyear')
    
    def get_filtered_queryset(self):
        comp_id = self.get_comp_id()
        fin_year_id = self.get_fin_year_id()
        if comp_id and fin_year_id is not None:
            # Use the model's filtering method, or custom manager
            # For simplicity, implementing directly here, but a custom manager is ideal.
            return self.model.objects.select_related('cid').filter(
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id
            ).order_by('-scid') # Default ordering from model Meta
        return self.model.objects.none() # Return empty queryset if context missing


class SubCategoryListView(SubCategoryContextMixin, ListView):
    model = SubCategory
    template_name = 'design_masters/subcategory/list.html'
    context_object_name = 'subcategories'

    def get_queryset(self):
        return self.get_filtered_queryset()

class SubCategoryTablePartialView(SubCategoryContextMixin, ListView):
    """
    Renders only the table rows for HTMX.
    """
    model = SubCategory
    template_name = 'design_masters/subcategory/_subcategory_table.html'
    context_object_name = 'subcategories'

    def get_queryset(self):
        return self.get_filtered_queryset()

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the CompId and FinYearId from session for any potential client-side needs, or just ensure filtering.
        return context


class SubCategoryCreateView(SubCategoryContextMixin, CreateView):
    model = SubCategory
    form_class = SubCategoryForm
    template_name = 'design_masters/subcategory/_subcategory_form.html'
    success_url = reverse_lazy('subcategory_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['comp_id'] = self.get_comp_id()
        kwargs['fin_year_id'] = self.get_fin_year_id()
        return kwargs

    def form_valid(self, form):
        comp_id_obj = get_object_or_404(Company, comp_id=self.get_comp_id())
        fin_year_id_obj = get_object_or_404(FinancialYear, fin_year_id=self.get_fin_year_id())
        
        form.instance.comp_id = comp_id_obj
        form.instance.fin_year_id = fin_year_id_obj
        
        # Call the fat model method to add the record
        try:
            with transaction.atomic():
                # Assuming SCId is auto-incremented by the database when managed=False
                # If SCId needs to be explicitly generated/assigned, do it here before save()
                # Or call a custom model method like form.instance.add_record(...)
                # For now, let Django's ModelForm save handle it.
                self.object = form.save() 
                messages.success(self.request, 'Sub Category added successfully.')
        except Exception as e:
            messages.error(self.request, f"Error adding Sub Category: {e}")
            return self.form_invalid(form)

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content, signals HTMX that action is complete
                headers={'HX-Trigger': 'refreshSubCategoryList'}
            )
        return super().form_valid(form) # Fallback for non-HTMX
        
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add New Sub Category'
        return context


class SubCategoryUpdateView(SubCategoryContextMixin, UpdateView):
    model = SubCategory
    form_class = SubCategoryForm
    template_name = 'design_masters/subcategory/_subcategory_form.html'
    success_url = reverse_lazy('subcategory_list')
    pk_url_kwarg = 'scid' # Use 'scid' from URL as primary key

    def get_queryset(self):
        # Ensure that only records belonging to the current session's company/fin_year are editable
        return self.get_filtered_queryset()

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['comp_id'] = self.get_comp_id()
        kwargs['fin_year_id'] = self.get_fin_year_id()
        return kwargs

    def form_valid(self, form):
        # The update_record method in the model could be called here
        # E.g., self.object.update_record(form.cleaned_data['cid'].cid, form.cleaned_data['sc_name'], form.cleaned_data['symbol'], self.get_comp_id())
        # For ModelForm, `form.save()` handles updating the instance directly.
        try:
            with transaction.atomic():
                self.object = form.save()
                messages.success(self.request, 'Sub Category updated successfully.')
        except Exception as e:
            messages.error(self.request, f"Error updating Sub Category: {e}")
            return self.form_invalid(form)
            
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshSubCategoryList'}
            )
        return super().form_valid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Sub Category'
        return context

class SubCategoryDeleteView(SubCategoryContextMixin, DeleteView):
    model = SubCategory
    template_name = 'design_masters/subcategory/_subcategory_confirm_delete.html'
    success_url = reverse_lazy('subcategory_list')
    pk_url_kwarg = 'scid' # Use 'scid' from URL as primary key

    def get_queryset(self):
        # Ensure that only records belonging to the current session's company/fin_year are deletable
        return self.get_filtered_queryset()

    def delete(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                self.object = self.get_object()
                self.object.delete() # Call model's delete method
                messages.success(self.request, 'Sub Category deleted successfully.')
        except Exception as e:
            messages.error(self.request, f"Error deleting Sub Category: {e}")
            # If deletion fails, return an appropriate response, perhaps re-render the delete form
            return HttpResponse(status=400, content=f"Error deleting: {e}") # Or render an error message partial

        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshSubCategoryList'}
            )
        return HttpResponseRedirect(self.get_success_url())

```

#### 4.4 Templates (design_masters/templates/design_masters/subcategory/)

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Item SubCategories</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'subcategory_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
            Add New Sub Category
        </button>
    </div>
    
    <div id="subcategoryTable-container"
         hx-trigger="load, refreshSubCategoryList from:body"
         hx-get="{% url 'subcategory_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading SubCategories...</p>
        </div>
    </div>
    
    <!-- Global Modal Structure (hidden by default) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 hidden items-center justify-center z-50 transition-opacity duration-300 opacity-0"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then remove .scale-100 from #modalContent then remove .flex from me for 300ms">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 transform scale-95 transition-transform duration-300"
             _="on htmx:afterOnLoad if !htmx.triggeringEl.closest('form') then add .scale-100 to #modalContent">
            <!-- Modal content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for modal if complex state is needed, otherwise HTMX + Hyperscript is sufficient.
    // The Hyperscript in template handles simple modal show/hide.
    // If you need more complex state management with Alpine.js:
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });

        // Example: Listen for HTMX events to toggle modal
        document.body.addEventListener('htmx:afterOnLoad', (evt) => {
            if (evt.detail.xhr.getResponseHeader('HX-Target') === 'modalContent') {
                Alpine.store('modal').open();
            }
        });
        document.body.addEventListener('htmx:afterRequest', (evt) => {
            if (evt.detail.xhr.getResponseHeader('HX-Trigger') === 'refreshSubCategoryList') {
                Alpine.store('modal').close();
            }
        });
    });
</script>
{% endblock %}

```

**`_subcategory_table.html`**

```html
<table id="subcategoryTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub Category</th>
            <th class="py-3 px-4 border-b text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 border-b text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in subcategories %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b text-sm text-gray-900">{{ obj.cid.full_name }}</td> {# Using the full_name property from Category model #}
            <td class="py-3 px-4 border-b text-sm text-gray-900">{{ obj.sc_name }}</td>
            <td class="py-3 px-4 border-b text-sm text-gray-900">{{ obj.symbol }}</td>
            <td class="py-3 px-4 border-b text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'subcategory_edit' obj.scid %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'subcategory_delete' obj.scid %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then add .scale-100 to #modalContent">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-lg text-red-700 font-semibold">
                No data to display!
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after HTMX loads the table content
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#subcategoryTable')) {
            $('#subcategoryTable').DataTable().destroy();
        }
        $('#subcategoryTable').DataTable({
            "pageLength": 20, // Matching ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>
```

**`_subcategory_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on--after-request="if(event.detail.successful) Alpine.store('modal').close()">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="relative">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modal then remove .scale-100 from #modalContent then remove .flex from #modal for 300ms">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_subcategory_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Sub Category: <span class="font-bold">{{ object.sc_name }}</span>?
        This action cannot be undone.
    </p>
    <form hx-delete="{% url 'subcategory_delete' object.scid %}" hx-swap="none"
          hx-on--after-request="if(event.detail.successful) Alpine.store('modal').close()">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .opacity-100 from #modal then remove .scale-100 from #modalContent then remove .flex from #modal for 300ms">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (design_masters/urls.py)

```python
from django.urls import path
from .views import (
    SubCategoryListView, 
    SubCategoryCreateView, 
    SubCategoryUpdateView, 
    SubCategoryDeleteView,
    SubCategoryTablePartialView
)

urlpatterns = [
    path('subcategory/', SubCategoryListView.as_view(), name='subcategory_list'),
    path('subcategory/table/', SubCategoryTablePartialView.as_view(), name='subcategory_table'),
    path('subcategory/add/', SubCategoryCreateView.as_view(), name='subcategory_add'),
    path('subcategory/edit/<int:scid>/', SubCategoryUpdateView.as_view(), name='subcategory_edit'),
    path('subcategory/delete/<int:scid>/', SubCategoryDeleteView.as_view(), name='subcategory_delete'),
]

```

#### 4.6 Tests (design_masters/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import SubCategory, Category, Company, FinancialYear
from unittest.mock import patch # For mocking session

class SubCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create mock Company and FinancialYear objects as foreign keys
        cls.company1 = Company.objects.create(comp_id=1, comp_name='Test Company 1')
        cls.fin_year1 = FinancialYear.objects.create(fin_year_id=1, fin_year_name='2023-2024')
        cls.fin_year2 = FinancialYear.objects.create(fin_year_id=2, fin_year_name='2024-2025')

        # Create test Category instances
        cls.category_has_sub = Category.objects.create(
            cid=101, cname='Electronics', symbol='ELE', has_subcat='1', 
            comp_id=cls.company1, fin_year_id=cls.fin_year1
        )
        cls.category_no_sub = Category.objects.create(
            cid=102, cname='Tools', symbol='TOL', has_subcat='0', 
            comp_id=cls.company1, fin_year_id=cls.fin_year1
        )
        cls.category_diff_comp = Category.objects.create(
            cid=103, cname='Apparel', symbol='APP', has_subcat='1',
            comp_id=Company.objects.create(comp_id=2, comp_name='Test Company 2'), fin_year_id=cls.fin_year1
        )
        cls.category_old_fin_year = Category.objects.create(
            cid=104, cname='Furniture', symbol='FUR', has_subcat='1',
            comp_id=cls.company1, fin_year_id=FinancialYear.objects.create(fin_year_id=0, fin_year_name='2022-2023')
        )

        # Create test SubCategory instances
        cls.subcategory1 = SubCategory.objects.create(
            scid=1, cid=cls.category_has_sub, sc_name='Smartphones', symbol='SM', 
            comp_id=cls.company1, fin_year_id=cls.fin_year1
        )
        cls.subcategory2 = SubCategory.objects.create(
            scid=2, cid=cls.category_has_sub, sc_name='Laptops', symbol='LA',
            comp_id=cls.company1, fin_year_id=cls.fin_year2
        )
        cls.subcategory_diff_comp = SubCategory.objects.create(
            scid=3, cid=cls.category_diff_comp, sc_name='Shirts', symbol='SH',
            comp_id=Company.objects.get(comp_id=2), fin_year_id=cls.fin_year1
        )

    def test_category_creation(self):
        self.assertEqual(self.category_has_sub.cname, 'Electronics')
        self.assertEqual(self.category_has_sub.symbol, 'ELE')
        self.assertEqual(self.category_has_sub.full_name, 'ELE - Electronics')
        self.assertEqual(self.category_has_sub.comp_id.comp_id, 1)

    def test_subcategory_creation(self):
        self.assertEqual(self.subcategory1.sc_name, 'Smartphones')
        self.assertEqual(self.subcategory1.symbol, 'SM')
        self.assertEqual(self.subcategory1.cid.cname, 'Electronics')
        self.assertEqual(self.subcategory1.comp_id.comp_id, 1)
        self.assertEqual(self.subcategory1.fin_year_id.fin_year_id, 1)

    def test_category_full_name_property(self):
        self.assertEqual(self.category_has_sub.full_name, 'ELE - Electronics')

    def test_subcategory_filter_by_session_context(self):
        # Test filtering for a specific company and fin_year
        qs = SubCategory.objects.filter_by_session_context(self.company1.comp_id, self.fin_year1.fin_year_id)
        self.assertIn(self.subcategory1, qs)
        self.assertNotIn(self.subcategory2, qs) # sc2 is for fin_year2 > fin_year1
        self.assertNotIn(self.subcategory_diff_comp, qs)

        # Test filtering for fin_year2, which should include both
        qs = SubCategory.objects.filter_by_session_context(self.company1.comp_id, self.fin_year2.fin_year_id)
        self.assertIn(self.subcategory1, qs) # fin_year1 <= fin_year2
        self.assertIn(self.subcategory2, qs) # fin_year2 <= fin_year2

    def test_category_filter_by_session_context(self):
        # Test filtering for a specific company and fin_year with HasSubCat='1'
        qs = Category.objects.filter_by_session_context(self.company1.comp_id, self.fin_year1.fin_year_id)
        self.assertIn(self.category_has_sub, qs)
        self.assertNotIn(self.category_no_sub, qs) # HasSubCat='0'
        self.assertNotIn(self.category_diff_comp, qs) # Different company
        self.assertNotIn(self.category_old_fin_year, qs) # Old financial year

    def test_subcategory_update_record(self):
        initial_name = self.subcategory1.sc_name
        self.subcategory1.update_record(
            self.category_has_sub.cid, 'Updated Smartphones', 'US', self.company1.comp_id
        )
        self.subcategory1.refresh_from_db()
        self.assertEqual(self.subcategory1.sc_name, 'Updated Smartphones')
        self.assertEqual(self.subcategory1.symbol, 'US')
        # CID should not change unless explicitly set with a new category object.
        self.assertEqual(self.subcategory1.cid, self.category_has_sub)


class SubCategoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company1 = Company.objects.create(comp_id=1, comp_name='Test Company 1')
        cls.fin_year1 = FinancialYear.objects.create(fin_year_id=1, fin_year_name='2023-2024')
        cls.fin_year_old = FinancialYear.objects.create(fin_year_id=0, fin_year_name='2022-2023')

        cls.category_elec = Category.objects.create(
            cid=101, cname='Electronics', symbol='ELE', has_subcat='1', 
            comp_id=cls.company1, fin_year_id=cls.fin_year1
        )
        cls.category_tools = Category.objects.create(
            cid=102, cname='Tools', symbol='TOL', has_subcat='0',
            comp_id=cls.company1, fin_year_id=cls.fin_year1
        )
        cls.subcategory1 = SubCategory.objects.create(
            scid=1, cid=cls.category_elec, sc_name='Smartphones', symbol='SM', 
            comp_id=cls.company1, fin_year_id=cls.fin_year1
        )
        cls.subcategory2 = SubCategory.objects.create(
            scid=2, cid=cls.category_elec, sc_name='Laptops', symbol='LA',
            comp_id=cls.company1, fin_year_id=cls.fin_year_old
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables
        session = self.client.session
        session['compid'] = self.company1.comp_id
        session['finyear'] = self.fin_year1.fin_year_id
        session.save()
        
    def test_list_view(self):
        response = self.client.get(reverse('subcategory_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/subcategory/list.html')
        
    def test_list_view_context(self):
        response = self.client.get(reverse('subcategory_list'))
        self.assertEqual(response.status_code, 200)
        self.assertIn('subcategories', response.context)
        # Only subcategory1 should be in the context due to fin_year filtering
        self.assertIn(self.subcategory1, response.context['subcategories'])
        self.assertNotIn(self.subcategory2, response.context['subcategories'])

    def test_table_partial_view(self):
        response = self.client.get(reverse('subcategory_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/subcategory/_subcategory_table.html')
        self.assertIn('subcategories', response.context)
        self.assertContains(response, 'Smartphones')
        self.assertNotContains(response, 'Laptops') # filtered out by fin_year

    def test_create_view_get(self):
        response = self.client.get(reverse('subcategory_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/subcategory/_subcategory_form.html')
        self.assertTrue('form' in response.context)
        # Check if Category dropdown is filtered correctly
        form_categories = response.context['form'].fields['cid'].queryset
        self.assertIn(self.category_elec, form_categories)
        self.assertNotIn(self.category_tools, form_categories) # has_subcat='0'
        
    def test_create_view_post_success(self):
        initial_count = SubCategory.objects.count()
        data = {
            'cid': self.category_elec.cid,
            'sc_name': 'New Gadget',
            'symbol': 'NG',
        }
        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertEqual(SubCategory.objects.count(), initial_count + 1)
        new_subcategory = SubCategory.objects.get(sc_name='New Gadget')
        self.assertEqual(new_subcategory.comp_id.comp_id, self.company1.comp_id)
        self.assertEqual(new_subcategory.fin_year_id.fin_year_id, self.fin_year1.fin_year_id)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sub Category added successfully.')
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')

    def test_create_view_post_invalid(self):
        initial_count = SubCategory.objects.count()
        data = {
            'cid': self.category_elec.cid,
            'sc_name': '', # Invalid: required field
            'symbol': 'NG',
        }
        response = self.client.post(reverse('subcategory_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should re-render form with errors
        self.assertEqual(SubCategory.objects.count(), initial_count)
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        response = self.client.get(reverse('subcategory_edit', args=[self.subcategory1.scid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/subcategory/_subcategory_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.subcategory1)
        # Check if Category dropdown is filtered correctly
        form_categories = response.context['form'].fields['cid'].queryset
        self.assertIn(self.category_elec, form_categories)
        self.assertNotIn(self.category_tools, form_categories)

    def test_update_view_post_success(self):
        updated_name = 'Updated Phone'
        data = {
            'cid': self.category_elec.cid, # Keep same category or change to another valid one
            'sc_name': updated_name,
            'symbol': 'UP',
        }
        response = self.client.post(reverse('subcategory_edit', args=[self.subcategory1.scid]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.subcategory1.refresh_from_db()
        self.assertEqual(self.subcategory1.sc_name, updated_name)
        self.assertEqual(self.subcategory1.symbol, 'UP')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sub Category updated successfully.')
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')

    def test_update_view_post_invalid(self):
        data = {
            'cid': self.category_elec.cid,
            'sc_name': '', # Invalid: required field
            'symbol': 'X',
        }
        response = self.client.post(reverse('subcategory_edit', args=[self.subcategory1.scid]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'This field is required.')
        self.subcategory1.refresh_from_db()
        self.assertNotEqual(self.subcategory1.sc_name, '') # Ensure it's not updated

    def test_delete_view_get(self):
        response = self.client.get(reverse('subcategory_delete', args=[self.subcategory1.scid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/subcategory/_subcategory_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.subcategory1)

    def test_delete_view_post_success(self):
        initial_count = SubCategory.objects.count()
        response = self.client.delete(reverse('subcategory_delete', args=[self.subcategory1.scid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(SubCategory.objects.count(), initial_count - 1)
        self.assertFalse(SubCategory.objects.filter(scid=self.subcategory1.scid).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Sub Category deleted successfully.')
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSubCategoryList')

    def test_delete_view_not_found(self):
        # Attempt to delete a non-existent subcategory
        response = self.client.delete(reverse('subcategory_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found
        
    def test_security_filtering_update(self):
        # Attempt to update a subcategory from a different company/fin_year (not in filtered queryset)
        self.client.session['compid'] = Company.objects.get(comp_id=2).comp_id # Switch company
        self.client.session.save()
        
        # Subcategory from company1 should not be accessible
        response = self.client.get(reverse('subcategory_edit', args=[self.subcategory1.scid]))
        self.assertEqual(response.status_code, 404) # Object not found within allowed queryset
        
    def test_security_filtering_delete(self):
        # Attempt to delete a subcategory from a different company/fin_year (not in filtered queryset)
        self.client.session['compid'] = Company.objects.get(comp_id=2).comp_id # Switch company
        self.client.session.save()
        
        # Subcategory from company1 should not be deletable
        response = self.client.delete(reverse('subcategory_delete', args=[self.subcategory1.scid]))
        self.assertEqual(response.status_code, 404) # Object not found within allowed queryset

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX:** All dynamic content loading (table refresh, modal forms) is handled by HTMX.
    *   `hx-get` is used to load forms (`_form.html`, `_confirm_delete.html`) into the modal.
    *   `hx-post` (for create/update) and `hx-delete` (for delete) are used to submit forms.
    *   `hx-swap="none"` is used on form submissions to allow the server to control UI updates via `HX-Trigger`.
    *   `HX-Trigger: refreshSubCategoryList` is sent from successful form submissions (Create, Update, Delete views) to tell the list view to re-fetch its content.
    *   The `hx-trigger="load, refreshSubCategoryList from:body"` on the main table container ensures initial load and subsequent refreshes.
*   **Alpine.js:** Used for simple UI state management, primarily the modal's `is-active` class. The Hyperscript (`_`) attributes provide a concise way to manage modal visibility directly in the HTML based on HTMX events and button clicks. An Alpine.js store is also demonstrated for more centralized control if needed.
*   **DataTables:** The `_subcategory_table.html` partial includes the DataTables initialization script `$(document).ready(function() { $('#subcategoryTable').DataTable(...) });`. This ensures DataTables is re-initialized every time the table content is loaded via HTMX, providing client-side search, sort, and pagination.
*   **DRY Templates:** The `_subcategory_form.html` and `_subcategory_confirm_delete.html` are partials, allowing them to be loaded dynamically into the modal without repeating the modal's outer structure. All templates extend `core/base.html` for consistent layout and CDN includes.

### Final Notes

*   **Placeholders:** Replace `tblCompanyMaster` and `tblFinancialYearMaster` with your actual table names if different. The `Company` and `FinancialYear` models are assumed for foreign key relationships; if these are not direct database tables and `CompId`/`FinYearId` are just `int` fields, you might adjust the models accordingly (e.g., `models.IntegerField` and custom `save()` logic in `SubCategory` to handle the session IDs). For the current setup, it strictly follows the FK relation that would be required if `CompId`/`FinYearId` were actual entities.
*   **Session Management:** The `SubCategoryContextMixin` ensures `CompId` and `FinYearId` from `request.session` are used for filtering and saving. Ensure your Django project's `settings.py` includes session middleware and your authentication system properly sets these session variables upon user login or selection.
*   **Error Handling:** Basic error handling with `messages.error` is included. For production, more robust error logging and user feedback mechanisms (e.g., specific error partials) would be beneficial.
*   **ID Generation:** For `managed = False` models, Django typically doesn't manage primary key auto-increment. If `SCId` is an identity column in SQL Server, new records will automatically get their `SCId` upon insertion. If `SCId` needs to be manually assigned or generated by a sequence, that logic would need to be added to the model's `save()` method or the view's `form_valid`. For this example, we assume it's auto-incrementing on the database side.
*   **Testing:** The provided tests cover basic CRUD operations, template usage, and HTMX interactions. Always expand tests to cover edge cases, validation rules, and specific business logic. Achieving high test coverage is critical for successful long-term maintenance.