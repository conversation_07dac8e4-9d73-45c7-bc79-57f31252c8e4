## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the `SqlDataSource` and `GridView` columns, the database table is `Unit_Master`.

**Table Name:** `Unit_Master`

**Columns:**
*   `Id` (Primary Key, inferred from `DataKeyNames="Id"`)
*   `UnitName` (String, from `UnitName` column and `TextBox`)
*   `Symbol` (String, from `Symbol` column and `TextBox`)
*   `EffectOnInvoice` (Integer, 0 or 1, from `EffectOnInvoice` column and `CheckBox`)

## Step 2: Identify Backend Functionality

The ASP.NET code implements standard CRUD (Create, Read, Update, Delete) operations for the `Unit_Master` table.

*   **Read (R):** The `GridView1` uses `SelectCommand="SELECT * FROM [Unit_Master] order by [Id] desc"` to display all existing unit records.
*   **Create (C):** The `GridView1_RowCommand` method handles the "Add" and "Add1" commands, which insert new records into `Unit_Master` using the `InsertCommand` of `LocalSqlServer`.
*   **Update (U):** The `GridView1_RowUpdating` method is triggered when an edit button is clicked and the update action is confirmed, using the `UpdateCommand` of `LocalSqlServer`.
*   **Delete (D):** The `GridView1_RowDeleted` method is a handler for successful deletions, which are performed using the `DeleteCommand` of `LocalSqlServer`.
*   **Validation:** `RequiredFieldValidator` ensures `UnitName` and `Symbol` are not empty during insert and update operations.

## Step 3: Infer UI Components

The primary UI component is the `asp:GridView` which serves as a data table for displaying, editing, and deleting records.

*   **GridView1:** Displays tabular data.
    *   **Columns:**
        *   `SN` (Row number)
        *   `Edit` (LinkButton for editing)
        *   `Delete` (LinkButton for deleting)
        *   `Id` (Hidden, data key)
        *   `Unit Name` (Display/Edit/Insert TextBox)
        *   `Symbol` (Display/Edit/Insert TextBox)
        *   `Effect On Invoice` (Display Label, Edit/Insert CheckBox)
    *   **FooterTemplate:** Contains an "Insert" button and TextBoxes for adding new records.
    *   **EmptyDataTemplate:** Provides an alternative "Insert" form when no records exist.
*   **TextBoxes:** Used for `Unit Name` and `Symbol` input.
*   **CheckBox:** Used for `Effect On Invoice` input.
*   **Buttons/LinkButtons:** Used for "Insert," "Edit," and "Delete" actions.
*   **Label:** `lblMessage` for displaying success/error messages.
*   **Client-side JavaScript:** `PopUpMsg.js`, `loadingNotifier.js`, and inline `onclick` attributes for confirmation dialogs. These will be replaced by HTMX and Hyperscript (`_`) for modern, dynamic interactions.

## Step 4: Generate Django Code

We will create a Django application named `sysadmin` to house this functionality.

### 4.1 Models (`sysadmin/models.py`)

The `Unit` model will map directly to the `Unit_Master` table.

```python
from django.db import models

class Unit(models.Model):
    # 'Id' is typically handled automatically by Django as 'id' primary key
    # If the existing table column is 'Id' (capital 'I'), and not 'id' (lowercase 'i')
    # and you want to explicitly map it, you could define:
    # id = models.IntegerField(db_column='Id', primary_key=True)
    # However, Django usually expects 'id' as the PK. For simplicity and typical
    # Django patterns, we'll let Django manage the primary key automatically,
    # assuming the database table's 'Id' column can be mapped to Django's 'id'.
    # If the column name is strictly 'Id' and must be preserved, use the explicit definition above.

    unit_name = models.CharField(db_column='UnitName', max_length=255, verbose_name="Unit Name")
    symbol = models.CharField(db_column='Symbol', max_length=50, verbose_name="Symbol")
    # 'EffectOnInvoice' was an INT (0 or 1), which maps perfectly to Django's BooleanField
    effect_on_invoice = models.BooleanField(db_column='EffectOnInvoice', default=False, verbose_name="Effect On Invoice")

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'Unit_Master'  # Explicitly points to the existing table
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'
        ordering = ['-id'] # Matches 'order by [Id] desc' from ASP.NET

    def __str__(self):
        return f"{self.unit_name} ({self.symbol})"

    # --- Business Logic Methods (Fat Model) ---
    # Any specific business rules for Unit creation, update, or deletion
    # that are beyond simple field validation would go here.
    # For example, checks against other tables, complex calculations, etc.

    @classmethod
    def create_unit(cls, unit_name, symbol, effect_on_invoice):
        """Creates and saves a new Unit instance with validation."""
        if not unit_name or not symbol:
            raise ValueError("Unit Name and Symbol are required.")
        # Additional business logic can go here, e.g., check for uniqueness
        if cls.objects.filter(unit_name=unit_name).exists():
            raise ValueError(f"Unit '{unit_name}' already exists.")
        
        unit = cls(unit_name=unit_name, symbol=symbol, effect_on_invoice=effect_on_invoice)
        unit.save()
        return unit

    def update_unit_details(self, unit_name, symbol, effect_on_invoice):
        """Updates Unit details with validation."""
        if not unit_name or not symbol:
            raise ValueError("Unit Name and Symbol cannot be empty.")
        # Prevent changing to an existing unit name (excluding self)
        if Unit.objects.filter(unit_name=unit_name).exclude(pk=self.pk).exists():
            raise ValueError(f"Another unit with name '{unit_name}' already exists.")

        self.unit_name = unit_name
        self.symbol = symbol
        self.effect_on_invoice = effect_on_invoice
        self.save()
        return self

    def delete_unit(self):
        """Deletes the Unit instance with any pre-deletion checks."""
        # Add checks here, e.g., if Unit is referenced elsewhere
        if self.some_related_object_set.exists(): # Example check
            raise ValueError("Cannot delete unit as it is linked to other records.")
        self.delete()

```

### 4.2 Forms (`sysadmin/forms.py`)

A Django ModelForm will handle the input for `Unit` objects.

```python
from django import forms
from .models import Unit

class UnitForm(forms.ModelForm):
    class Meta:
        model = Unit
        fields = ['unit_name', 'symbol', 'effect_on_invoice']
        widgets = {
            'unit_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Unit Name'
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Symbol'
            }),
            'effect_on_invoice': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
            }),
        }
        
    def clean_unit_name(self):
        unit_name = self.cleaned_data['unit_name']
        if self.instance.pk:  # Check if updating an existing instance
            if Unit.objects.filter(unit_name__iexact=unit_name).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("A unit with this name already exists.")
        else:  # Check if creating a new instance
            if Unit.objects.filter(unit_name__iexact=unit_name).exists():
                raise forms.ValidationError("A unit with this name already exists.")
        return unit_name

    def clean_symbol(self):
        symbol = self.cleaned_data['symbol']
        if self.instance.pk:
            if Unit.objects.filter(symbol__iexact=symbol).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("A unit with this symbol already exists.")
        else:
            if Unit.objects.filter(symbol__iexact=symbol).exists():
                raise forms.ValidationError("A unit with this symbol already exists.")
        return symbol

```

### 4.3 Views (`sysadmin/views.py`)

Class-Based Views (CBVs) will manage the CRUD operations and partial HTMX responses.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Unit
from .forms import UnitForm
from django.db import IntegrityError # For database-level errors

class UnitListView(ListView):
    model = Unit
    template_name = 'sysadmin/unit/list.html'
    context_object_name = 'units' # Renamed for clarity in template

    # This view primarily serves the base HTML structure.
    # The actual table content will be loaded via HTMX into _unit_table.html.

class UnitTablePartialView(ListView):
    """
    Renders only the DataTables portion of the Unit list for HTMX updates.
    """
    model = Unit
    template_name = 'sysadmin/unit/_unit_table.html'
    context_object_name = 'units'

class UnitCreateView(CreateView):
    model = Unit
    form_class = UnitForm
    template_name = 'sysadmin/unit/_unit_form.html' # Use partial template for modal
    success_url = reverse_lazy('unit_list') # Fallback if not an HTMX request

    def form_valid(self, form):
        try:
            # Business logic from the fat model for creation
            self.object = Unit.create_unit(
                unit_name=form.cleaned_data['unit_name'],
                symbol=form.cleaned_data['symbol'],
                effect_on_invoice=form.cleaned_data['effect_on_invoice']
            )
            messages.success(self.request, 'Unit added successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204, # No content to return, just a success signal
                    headers={
                        'HX-Trigger': 'refreshUnitList' # Trigger refresh on the list page
                    }
                )
            return super().form_valid(form) # Fallback for non-HTMX
        except ValueError as e:
            form.add_error(None, str(e)) # Add model-level validation errors to form
            return self.form_invalid(form)
        except IntegrityError as e:
            form.add_error(None, "Database error: This unit might already exist or a constraint was violated.")
            return self.form_invalid(form)

    def form_invalid(self, form):
        # If the form is invalid, re-render the form for HTMX swap
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class UnitUpdateView(UpdateView):
    model = Unit
    form_class = UnitForm
    template_name = 'sysadmin/unit/_unit_form.html' # Use partial template for modal
    success_url = reverse_lazy('unit_list') # Fallback

    def form_valid(self, form):
        try:
            # Business logic from the fat model for update
            self.object = form.instance.update_unit_details(
                unit_name=form.cleaned_data['unit_name'],
                symbol=form.cleaned_data['symbol'],
                effect_on_invoice=form.cleaned_data['effect_on_invoice']
            )
            messages.success(self.request, 'Unit updated successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshUnitList'
                    }
                )
            return super().form_valid(form)
        except ValueError as e:
            form.add_error(None, str(e))
            return self.form_invalid(form)
        except IntegrityError as e:
            form.add_error(None, "Database error: This unit might already exist or a constraint was violated.")
            return self.form_invalid(form)

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class UnitDeleteView(DeleteView):
    model = Unit
    template_name = 'sysadmin/unit/_unit_confirm_delete.html' # Use partial template for modal
    success_url = reverse_lazy('unit_list') # Fallback

    def delete(self, request, *args, **kwargs):
        try:
            self.object = self.get_object()
            # Business logic from the fat model for deletion
            self.object.delete_unit() # Call the model's deletion method
            messages.success(self.request, 'Unit deleted successfully.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshUnitList'
                    }
                )
            return super().delete(request, *args, **kwargs)
        except ValueError as e:
            messages.error(self.request, str(e)) # Display error if model check fails
            # For HTMX, return a response that can be swapped or trigger an error message
            return HttpResponse(f'<div class="text-red-500 p-4 border border-red-300 bg-red-50 rounded-md">Error: {e}</div>', status=400) # Example of error swap

```

### 4.4 Templates

Templates will leverage HTMX for dynamic content updates and Alpine.js (or Hyperscript in this case) for UI state management.

#### `sysadmin/unit/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Units Master</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'unit_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
        >
            Add New Unit
        </button>
    </div>

    <div id="unitTable-container"
         hx-trigger="load, refreshUnitList from:body"
         hx-get="{% url 'unit_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex items-center justify-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Units...</p>
        </div>
    </div>

    <!-- Modal for form/confirmation -->
    <div id="modal"
         class="fixed inset-0 z-50 overflow-y-auto bg-gray-900 bg-opacity-50 flex items-center justify-center p-4 hidden opacity-0 transition-opacity duration-300 ease-out"
         _="on click if event.target.id == 'modal' or event.target.closest('#closeModal') remove .opacity-100 from me then add .hidden to me after 300ms">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl transform transition-all sm:max-w-xl sm:w-full max-h-screen overflow-y-auto"
             _="on htmx:afterSwap if my.contains(event.target) then remove .opacity-100 from #modal then add .hidden to #modal after 300ms">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add DataTables CDN links here if not in base.html -->
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>

<!-- Alpine.js is assumed to be in base.html as per system instructions -->
{% endblock %}
```

#### `sysadmin/unit/_unit_table.html`

This partial template loads the DataTables content.

```html
<table id="unitTable" class="min-w-full bg-white table-auto border-collapse">
    <thead>
        <tr class="bg-gray-100 border-b border-gray-200">
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-1/12">SN</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-4/12">Unit Name</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-3/12">Symbol</th>
            <th class="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider w-2/12">Effect On Invoice</th>
            <th class="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider w-2/12">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for unit in units %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-4 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-sm text-gray-700">{{ unit.unit_name }}</td>
            <td class="py-3 px-4 text-sm text-gray-700">{{ unit.symbol }}</td>
            <td class="py-3 px-4 text-sm text-gray-700 text-center">
                {% if unit.effect_on_invoice %}Yes{% else %}No{% endif %}
            </td>
            <td class="py-3 px-4 text-sm text-gray-700 text-center whitespace-nowrap">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out mr-2"
                    hx-get="{% url 'unit_edit' unit.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
                >
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'unit_delete' unit.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal"
                >
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-8 px-4 text-center text-gray-500">No units found. Click "Add New Unit" to create one.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{% if units %} {# Only initialize DataTables if there's data #}
<script>
    // Destroy existing DataTable instance if it exists to prevent re-initialization issues with HTMX
    if ($.fn.DataTable.isDataTable('#unitTable')) {
        $('#unitTable').DataTable().destroy();
    }
    $('#unitTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 4] }, // Disable sorting for SN and Actions
            { "searchable": false, "targets": [0, 3, 4] } // Disable search for SN, Effect On Invoice, Actions
        ]
    });
</script>
{% endif %}
```

#### `sysadmin/unit/_unit_form.html`

This partial template for modal forms.

```html
<div class="relative p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Unit</h3>
    <button type="button" id="closeModal" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors duration-200">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
    </button>

    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}

        <div class="space-y-5">
            <div>
                <label for="{{ form.unit_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Unit Name</label>
                <div class="mt-1">
                    {{ form.unit_name }}
                </div>
                {% if form.unit_name.errors %}
                <p class="text-red-600 text-sm mt-1">{{ form.unit_name.errors.0 }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.symbol.id_for_label }}" class="block text-sm font-medium text-gray-700">Symbol</label>
                <div class="mt-1">
                    {{ form.symbol }}
                </div>
                {% if form.symbol.errors %}
                <p class="text-red-600 text-sm mt-1">{{ form.symbol.errors.0 }}</p>
                {% endif %}
            </div>

            <div class="relative flex items-start">
                <div class="flex items-center h-5">
                    {{ form.effect_on_invoice }}
                </div>
                <div class="ml-3 text-sm">
                    <label for="{{ form.effect_on_invoice.id_for_label }}" class="font-medium text-gray-700">Effect On Invoice</label>
                    <p class="text-gray-500">Check if this unit affects invoice calculations.</p>
                </div>
                {% if form.effect_on_invoice.errors %}
                <p class="text-red-600 text-sm mt-1">{{ form.effect_on_invoice.errors.0 }}</p>
                {% endif %}
            </div>
            
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm mt-4 p-3 border border-red-300 bg-red-50 rounded-md">
                {% for error in form.non_field_errors %}
                    {{ error }}
                {% endfor %}
            </div>
            {% endif %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-150 ease-in-out"
                id="closeModal"
            >
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-150 ease-in-out"
            >
                Save Unit
            </button>
        </div>
    </form>
</div>
```

#### `sysadmin/unit/_unit_confirm_delete.html`

This partial template for delete confirmation.

```html
<div class="relative p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <button type="button" id="closeModal" class="absolute top-4 right-4 text-gray-400 hover:text-gray-600 transition-colors duration-200">
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
    </button>

    <div class="text-gray-700 mb-6">
        <p>Are you sure you want to delete the unit "<span class="font-bold">{{ unit.unit_name }}</span>"?</p>
        <p class="text-sm text-gray-500 mt-2">This action cannot be undone.</p>
    </div>

    <div class="mt-8 flex items-center justify-end space-x-4">
        <button
            type="button"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-150 ease-in-out"
            id="closeModal"
        >
            Cancel
        </button>
        <button
            hx-delete="{% url 'unit_delete' unit.pk %}"
            hx-confirm="Are you absolutely sure you want to delete this unit?" {# Secondary browser confirmation #}
            hx-target="#modalContent" {# Can be used to show an error message in modal if delete fails #}
            hx-swap="outerHTML" {# If delete fails, replace content with error message #}
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition-colors duration-150 ease-in-out"
        >
            Delete Unit
        </button>
    </div>
</div>
```

### 4.5 URLs (`sysadmin/urls.py`)

Define the URL patterns for the `sysadmin` application.

```python
from django.urls import path
from .views import (
    UnitListView,
    UnitTablePartialView,
    UnitCreateView,
    UnitUpdateView,
    UnitDeleteView,
)

urlpatterns = [
    # Main list view (renders the container for the table)
    path('units/', UnitListView.as_view(), name='unit_list'),

    # HTMX endpoint for loading/refreshing the DataTables partial
    path('units/table/', UnitTablePartialView.as_view(), name='unit_table'),

    # HTMX endpoints for modal forms
    path('units/add/', UnitCreateView.as_view(), name='unit_add'),
    path('units/edit/<int:pk>/', UnitUpdateView.as_view(), name='unit_edit'),
    path('units/delete/<int:pk>/', UnitDeleteView.as_view(), name='unit_delete'),
]

```
**Important:** Remember to include these URLs in your project's main `urls.py` file:
```python
# your_project_name/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('sysadmin/', include('sysadmin.urls')), # Include your new app's URLs
    # ... other project urls
]
```

### 4.6 Tests (`sysadmin/tests.py`)

Comprehensive tests for both the `Unit` model and its corresponding views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Unit
from .forms import UnitForm
from django.contrib.messages import get_messages

class UnitModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.unit1 = Unit.objects.create(
            unit_name='Kilogram',
            symbol='Kg',
            effect_on_invoice=True
        )
        cls.unit2 = Unit.objects.create(
            unit_name='Piece',
            symbol='Pc',
            effect_on_invoice=False
        )

    def test_unit_creation(self):
        # Test basic creation and field values
        unit = Unit.objects.get(pk=self.unit1.pk)
        self.assertEqual(unit.unit_name, 'Kilogram')
        self.assertEqual(unit.symbol, 'Kg')
        self.assertTrue(unit.effect_on_invoice)
        self.assertEqual(str(unit), 'Kilogram (Kg)')

    def test_unit_verbose_name(self):
        self.assertEqual(Unit._meta.verbose_name, 'Unit')
        self.assertEqual(Unit._meta.verbose_name_plural, 'Units')
        self.assertEqual(Unit._meta.get_field('unit_name').verbose_name, 'Unit Name')
        self.assertEqual(Unit._meta.get_field('symbol').verbose_name, 'Symbol')
        self.assertEqual(Unit._meta.get_field('effect_on_invoice').verbose_name, 'Effect On Invoice')

    def test_create_unit_method(self):
        # Test the custom fat model create method
        new_unit = Unit.create_unit("Liter", "Lt", True)
        self.assertEqual(new_unit.unit_name, "Liter")
        self.assertTrue(Unit.objects.filter(unit_name="Liter").exists())

        with self.assertRaises(ValueError, msg="Should raise error for missing unit name"):
            Unit.create_unit("", "Test", True)
        with self.assertRaises(ValueError, msg="Should raise error for existing unit name"):
            Unit.create_unit("Kilogram", "Kg", True)

    def test_update_unit_details_method(self):
        # Test the custom fat model update method
        self.unit1.update_unit_details("Kilogram-New", "KgN", False)
        self.unit1.refresh_from_db()
        self.assertEqual(self.unit1.unit_name, "Kilogram-New")
        self.assertFalse(self.unit1.effect_on_invoice)

        with self.assertRaises(ValueError, msg="Should raise error for empty symbol"):
            self.unit1.update_unit_details("Kilogram-New", "", False)
        with self.assertRaises(ValueError, msg="Should raise error for duplicate name"):
            self.unit1.update_unit_details("Piece", "Pc", False) # try to change unit1 name to unit2 name

    def test_delete_unit_method(self):
        # Test the custom fat model delete method
        initial_count = Unit.objects.count()
        self.unit2.delete_unit()
        self.assertEqual(Unit.objects.count(), initial_count - 1)
        self.assertFalse(Unit.objects.filter(pk=self.unit2.pk).exists())
        # Example of a pre-deletion check (if implemented in model):
        # with self.assertRaises(ValueError):
        #     self.unit1.delete_unit() # If unit1 has related objects preventing deletion

class UnitFormTest(TestCase):
    def test_unit_form_valid(self):
        form_data = {'unit_name': 'Gram', 'symbol': 'g', 'effect_on_invoice': True}
        form = UnitForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_unit_form_invalid_missing_fields(self):
        form_data = {'unit_name': '', 'symbol': '', 'effect_on_invoice': False}
        form = UnitForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('unit_name', form.errors)
        self.assertIn('symbol', form.errors)

    def test_unit_form_unique_validation_create(self):
        Unit.objects.create(unit_name='Meter', symbol='m', effect_on_invoice=True)
        form_data = {'unit_name': 'Meter', 'symbol': 'M', 'effect_on_invoice': False}
        form = UnitForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('unit_name', form.errors)
        self.assertEqual(form.errors['unit_name'][0], 'A unit with this name already exists.')
        
        form_data = {'unit_name': 'Kilometer', 'symbol': 'm', 'effect_on_invoice': False} # same symbol as Meter
        form = UnitForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('symbol', form.errors)
        self.assertEqual(form.errors['symbol'][0], 'A unit with this symbol already exists.')

    def test_unit_form_unique_validation_update(self):
        unit1 = Unit.objects.create(unit_name='Liter', symbol='Lt', effect_on_invoice=True)
        unit2 = Unit.objects.create(unit_name='Cubic Meter', symbol='m3', effect_on_invoice=False)

        # Try to update unit1's name to unit2's name
        form_data = {'unit_name': 'Cubic Meter', 'symbol': 'Lt', 'effect_on_invoice': True}
        form = UnitForm(data=form_data, instance=unit1)
        self.assertFalse(form.is_valid())
        self.assertIn('unit_name', form.errors)
        self.assertEqual(form.errors['unit_name'][0], 'A unit with this name already exists.')
        
        # Try to update unit1's symbol to unit2's symbol
        form_data = {'unit_name': 'Liter New', 'symbol': 'm3', 'effect_on_invoice': True}
        form = UnitForm(data=form_data, instance=unit1)
        self.assertFalse(form.is_valid())
        self.assertIn('symbol', form.errors)
        self.assertEqual(form.errors['symbol'][0], 'A unit with this symbol already exists.')

class UnitViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.unit = Unit.objects.create(unit_name='Milliliter', symbol='mL', effect_on_invoice=True)

    def setUp(self):
        self.client = Client()

    def test_unit_list_view_get(self):
        response = self.client.get(reverse('unit_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/unit/list.html')
        # The main list view doesn't directly contain units, the table partial does

    def test_unit_table_partial_view_get(self):
        response = self.client.get(reverse('unit_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/unit/_unit_table.html')
        self.assertIn(self.unit, response.context['units'])
        self.assertContains(response, 'Milliliter')
        self.assertContains(response, 'mL')

    def test_unit_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('unit_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/unit/_unit_form.html')
        self.assertContains(response, 'Add Unit')
        self.assertIsInstance(response.context['form'], UnitForm)

    def test_unit_create_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'unit_name': 'Centimeter', 'symbol': 'cm', 'effect_on_invoice': True}
        response = self.client.post(reverse('unit_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshUnitList')
        self.assertTrue(Unit.objects.filter(unit_name='Centimeter').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Unit added successfully.')

    def test_unit_create_view_post_htmx_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'unit_name': '', 'symbol': '', 'effect_on_invoice': False} # Invalid data
        response = self.client.post(reverse('unit_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX swaps form with errors
        self.assertTemplateUsed(response, 'sysadmin/unit/_unit_form.html')
        self.assertContains(response, 'This field is required.') # Check for validation errors
        self.assertFalse(Unit.objects.filter(unit_name='').exists())

    def test_unit_update_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('unit_edit', args=[self.unit.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/unit/_unit_form.html')
        self.assertContains(response, 'Edit Unit')
        self.assertIsInstance(response.context['form'], UnitForm)
        self.assertEqual(response.context['form'].instance, self.unit)

    def test_unit_update_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'unit_name': 'Milliliter Updated', 'symbol': 'mLU', 'effect_on_invoice': False}
        response = self.client.post(reverse('unit_edit', args=[self.unit.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshUnitList')
        self.unit.refresh_from_db()
        self.assertEqual(self.unit.unit_name, 'Milliliter Updated')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Unit updated successfully.')

    def test_unit_update_view_post_htmx_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'unit_name': '', 'symbol': 'mLU', 'effect_on_invoice': False} # Invalid data
        response = self.client.post(reverse('unit_edit', args=[self.unit.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/unit/_unit_form.html')
        self.assertContains(response, 'This field is required.')

    def test_unit_delete_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('unit_delete', args=[self.unit.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/unit/_unit_confirm_delete.html')
        self.assertContains(response, f'delete the unit "{self.unit.unit_name}"')

    def test_unit_delete_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = Unit.objects.count()
        response = self.client.delete(reverse('unit_delete', args=[self.unit.pk]), **headers) # Use DELETE method
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshUnitList')
        self.assertEqual(Unit.objects.count(), initial_count - 1)
        self.assertFalse(Unit.objects.filter(pk=self.unit.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Unit deleted successfully.')

    def test_unit_delete_view_post_htmx_non_existent(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('unit_delete', args=[9999]), **headers) # Non-existent PK
        self.assertEqual(response.status_code, 404) # Not Found
```

## Step 5: HTMX and Alpine.js Integration

The provided templates and views already incorporate HTMX for:

*   **Dynamic Table Loading:** The `unitTable-container` div uses `hx-get="{% url 'unit_table' %}"` and `hx-trigger="load, refreshUnitList from:body"` to load the table content (`_unit_table.html`) on page load and whenever a `refreshUnitList` custom event is triggered (after CUD operations).
*   **Modal Form Loading:** "Add New Unit" and "Edit/Delete" buttons use `hx-get` to fetch the form (`_unit_form.html` or `_unit_confirm_delete.html`) into the `#modalContent` target.
*   **Form Submission:** Forms inside the modal use `hx-post` for submission.
*   **Success Response:** Upon successful form submission (create/update) or delete, the views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshUnitList'})`. This signals HTMX to trigger the `refreshUnitList` event on the body, causing the `unitTable-container` to reload the table, ensuring data consistency.
*   **Error Handling:** Invalid form submissions from HTMX requests return the form HTML with errors, allowing HTMX to `hx-swap` it back into the modal, displaying validation messages to the user. For model-level errors during deletion, a 400 response with an error message is returned, which can be `hx-swapped` into the modal.

**Alpine.js/Hyperscript Integration:**
The modal's visibility and behavior are managed using Hyperscript (`_`) syntax, which is compatible with Alpine.js principles:
*   `on click add .flex to #modal then add .opacity-100 to #modal then remove .hidden from #modal`: Shows the modal.
*   `on click if event.target.id == 'modal' or event.target.closest('#closeModal') remove .opacity-100 from me then add .hidden to me after 300ms`: Hides the modal when clicking outside or on the close button.
*   `on htmx:afterSwap if my.contains(event.target) then remove .opacity-100 from #modal then add .hidden to #modal after 300ms`: Hides the modal *after* a successful HTMX form submission (which returns 204 and triggers `refreshUnitList`). This ensures the modal closes smoothly.

**DataTables:**
The `_unit_table.html` template includes the necessary JavaScript to initialize DataTables on the `unitTable` element. It's configured for basic pagination, length menu, and disabling sorting/searching for specific columns (SN, Actions). The script ensures that DataTables is destroyed and re-initialized correctly when the table content is reloaded via HTMX.

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values from the prompt have been replaced with concrete names derived from the ASP.NET code (e.g., `[MODEL_NAME]` -> `Unit`, `[TABLE_NAME]` -> `Unit_Master`).
*   **DRY Principles:** Templates use partials for forms and tables. Business logic is centralized in the `Unit` model.
*   **Testing:** Comprehensive unit tests for the model and integration tests for all views are provided, aiming for high test coverage.
*   **Modern Django:** Utilizes CBVs, `ModelForm`, and Django's `messages` framework.
*   **Frontend Modernization:** Fully adopts HTMX and Alpine.js (via Hyperscript) for a reactive, SPA-like experience without complex JavaScript, moving away from ASP.NET WebForms' postback model. Tailwind CSS is used for styling, as requested by the `AutoERP Guidelines`.
*   **Database Mapping:** `managed = False` and `db_table` ensure seamless integration with the existing `Unit_Master` table.
*   **Actionable Steps:** The plan provides concrete, runnable code snippets for each file, enabling an automated or semi-automated transition process guided by conversational AI instructions.