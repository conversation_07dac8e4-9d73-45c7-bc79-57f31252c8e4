## ASP.NET to Django Conversion Script: Item Master Edit/Details

This plan outlines the modernization of your ASP.NET Item Master Edit/Details page to a modern Django application. We will leverage Django's robust ORM, class-based views, and a dynamic frontend stack featuring HTMX and Alpine.js for a seamless user experience. The focus is on automation-driven migration, ensuring a clear, maintainable, and scalable solution.

### Business Value of Django Modernization:

Migrating to Django brings significant business advantages:

*   **Cost Efficiency:** Open-source nature eliminates licensing fees, and Python's efficiency reduces development time and operational costs.
*   **Enhanced Performance:** Django's optimized architecture and database interactions lead to faster page loads and improved application responsiveness.
*   **Improved User Experience:** HTMX and Alpine.js deliver reactive, app-like interactions without full page reloads, making the application feel modern and intuitive.
*   **Scalability & Maintainability:** Django's "batteries-included" approach, clean code structure, and strong community support ensure your application can easily grow and adapt to future needs.
*   **Security:** Django includes built-in protections against common web vulnerabilities, significantly enhancing data security.
*   **Developer Productivity:** Python's readability and Django's conventions accelerate development cycles, allowing new features to be rolled out faster.

---

### Conversion Steps:

This plan outlines the migration process, broken down into manageable, automatable steps.

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several database tables. The primary table for this page is `tblDG_Item_Master`. It also has relationships with several lookup tables used for dropdowns and display.

**Identified Tables and Key Columns:**

*   **`tblDG_Item_Master`** (Primary table for `ItemMaster`):
    *   `Id` (PK)
    *   `CId` (FK to `tblDG_Category_Master`)
    *   `Buyer` (FK to `tblMM_Buyer_Master`)
    *   `PartNo`
    *   `ManfDesc`
    *   `UOMBasic` (FK to `Unit_Master`)
    *   `MinOrderQty`
    *   `MinStockQty`
    *   `StockQty`
    *   `Location` (FK to `tblDG_Location_Master`)
    *   `Absolute` (Boolean, stored as 1/0)
    *   `OpeningBalDate` (Date, stored in `dd-MM-yyyy` or similar format in DB)
    *   `OpeningBalQty`
    *   `UOMConFact` (Boolean, stored as 1/0)
    *   `Class` (FK to `tblDG_Item_Class`)
    *   `Excise` (Boolean, stored as 1/0)
    *   `ImportLocal` (Boolean, stored as 1/0)
    *   `FileName`, `FileData`, `FileSize`, `ContentType` (for Item Image)
    *   `AttName`, `AttData`, `AttSize`, `AttContentType` (for Attachment)
    *   `AHId` (FK to `AccHead`)
    *   `txtInspdays` (presumably `InspectionDays`)
    *   `txtleaddays` (presumably `LeadDays`)
    *   `SysDate`, `SysTime`, `CompId`, `finYearId`, `SessionId` (System/Audit fields)

*   **`tblDG_Category_Master`**: (`CId`, `Symbol`, `CName`) - Used for `DrpCategory`.
*   **`tblMM_Buyer_Master`**: (`Id`, `Category`, `Nos`, `EmpId`) - Used for `DrpBuyer`.
*   **`tblHR_OfficeStaff`**: (`EmpId`, `EmployeeName`) - Joined by `tblMM_Buyer_Master`.
*   **`tblDG_Item_Class`**: (`Id`, `Class`) - Used for `drpclass`.
*   **`tblDG_Location_Master`**: (`Id`, `LocationLabel`, `LocationNo`) - Used for `DrpLocation`.
*   **`Unit_Master`**: (`Id`, `Symbol`) - Used for `DrpUOMBasic`.
*   **`AccHead`**: (`Id`, `Symbol`, `Description`, `Category`) - Used for `DrpAChead`.

#### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core business logic.

**Analysis:**
The page primarily supports an **Update** operation for an existing "Item Master" record. It also displays a single item's details in a `GridView`, which will be re-envisioned as a DataTables display for all items.

*   **Read (Display):**
    *   On `Page_Load`, it fetches a single `ItemMaster` record based on `ItemId` from the query string (`Request.QueryString["ItemId"]`).
    *   It populates various dropdowns (`DrpCategory`, `DrpBuyer`, `DrpUOMBasic`, `DrpLocation`, `drpclass`, `DrpAChead`) and text fields with the fetched data.
    *   The `Fillgrid()` method also fetches data for the *same single item* and displays it in a `GridView`. This will be modernized to display a list of *all* `ItemMaster` records with DataTables.
    *   Handles display of attached files/images with download links.

*   **Update:**
    *   `BtnUpdate_Click` handles saving changes to the `tblDG_Item_Master` record.
    *   Includes validation checks (`fun.NumberValidationQty`, `fun.DateValidation`).
    *   Processes file uploads (`FileUpload1` for image, `FileUpload3` for attachment), including image resizing for `FileUpload1`.
    *   Updates various fields based on user input from textboxes, dropdowns, radio buttons, and checkboxes.
    *   Handles `NULL` values from `DBNull.Value`.

*   **Delete (Attachments Only):**
    *   `imgUpload_Click` and `imgUpload0_Click` delete the associated image and attachment files by setting their respective fields to `NULL` in the database.

*   **Dynamic UI Changes:**
    *   `DrpCategory_SelectedIndexChanged` (though empty in code-behind, suggests potential future dynamic behavior).
    *   `RadioButtonList4_SelectedIndexChanged` dynamically filters/populates `DrpAChead` based on selected category (Labour, With Material, Expenses, Service Provider).
    *   Initial `Page_Load` disables many fields if `CId` (Category) is `DBNull.Value` (i.e., no category selected/found for the item).

*   **Global Context:** `CompId`, `FinYearId`, `sId` (username) are used for data filtering and auditing.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Mapping to Django/HTMX/Alpine.js:**

*   `asp:Panel` (`Panel1`, `Panel2`): Will be simple `<div>` elements, potentially managed by Alpine.js for visibility or state.
*   `asp:Label`: HTML `<label>` or simple text.
*   `asp:DropDownList`: Django `forms.ModelChoiceField` or `forms.ChoiceField` rendered as `<select>`, dynamically updated via HTMX.
*   `asp:TextBox`: Django `forms.CharField`, `forms.DecimalField`, `forms.IntegerField` rendered as `<input type="text">` or `<textarea>`. Client-side `onblue`, `onfocus`, `onkeyup` for `isNaN` validation will be replaced by Alpine.js for real-time feedback and Django form validation on submit.
*   `asp:RadioButtonList`: Django `forms.ChoiceField` with `forms.RadioSelect` widget. Dynamic updates via HTMX.
*   `asp:CheckBox`: Django `forms.BooleanField` rendered as `<input type="checkbox">`.
*   `asp:FileUpload`: Django `forms.FileField` or `forms.ImageField` rendered as `<input type="file">`.
*   `asp:ImageButton` (`imgUpload`, `imgUpload0`): HTML `<button>` with HTMX `hx-delete` for deleting attachments.
*   `asp:Button` (`BtnUpdate`, `BtnCancel`): HTML `<button>` with HTMX `hx-post` for form submission and `hx-get` for navigation/cancellation. Client-side `confirmationUpdate()` will be a simple HTMX modal.
*   `asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`: Replaced by Django form validation (`required=True`, `RegexValidator` or custom clean methods in the form).
*   `cc1:CalendarExtender`: Django `forms.DateField` with `<input type="date">` or a custom date picker integrated with Alpine.js (e.g., flatpickr initialized with Alpine.js). For simplicity, we'll use `type="date"`.
*   `asp:GridView` (`GridView2`): Replaced by a `<table>` enhanced with DataTables for client-side sorting, filtering, and pagination. This table will be loaded dynamically via HTMX.

#### Step 4: Generate Django Code

We will create a Django app, let's call it `itemmasters`.

##### 4.1 Models (`itemmasters/models.py`)

This file will define the `ItemMaster` model and simplified models for related lookup tables, mapped to your existing database schema.

```python
from django.db import models
from django.urls import reverse
from decimal import Decimal
import io
from PIL import Image

# Placeholder models for lookup tables
# These assume simple ID and display name fields.
# Adjust fields based on your actual database schema if more complex.

class Category(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    name = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"{self.symbol}-{self.name}"

class OfficeStaff(models.Model):
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class BuyerMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category_str = models.CharField(db_column='Category', max_length=255, blank=True, null=True) # Assuming this is not FK
    nos = models.CharField(db_column='Nos', max_length=50, blank=True, null=True)
    emp_id = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='buyer_masters')

    class Meta:
        managed = False
        db_table = 'tblMM_Buyer_Master'
        verbose_name = 'Buyer Master'
        verbose_name_plural = 'Buyer Masters'

    def __str__(self):
        return f"{self.category_str}-{self.nos}-[{self.emp_id.employee_name}]" # Replicating ASP.NET display

class ItemClass(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_class = models.CharField(db_column='Class', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Class'
        verbose_name = 'Item Class'
        verbose_name_plural = 'Item Classes'

    def __str__(self):
        return self.item_class

class LocationMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_label = models.CharField(db_column='LocationLabel', max_length=255)
    location_no = models.CharField(db_column='LocationNo', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location Master'
        verbose_name_plural = 'Location Masters'

    def __str__(self):
        return f"{self.location_label}-{self.location_no}"

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class AccHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)
    category = models.CharField(db_column='Category', max_length=50) # Labour, With Material, Expenses, Service Material

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}"

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    manufacturer_description = models.TextField(db_column='ManfDesc')
    min_order_qty = models.DecimalField(db_column='MinOrderQty', max_digits=18, decimal_places=3, default=1)
    min_stock_qty = models.DecimalField(db_column='MinStockQty', max_digits=18, decimal_places=3, default=1)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, default=0)
    opening_balance_qty = models.DecimalField(db_column='OpeningBalQty', max_digits=18, decimal_places=3, default=0)
    opening_balance_date = models.CharField(db_column='OpeningBalDate', max_length=15, blank=True, null=True) # Stored as string, will convert to DateField in form
    inspection_days = models.IntegerField(db_column='txtInspdays', default=0)
    lead_days = models.IntegerField(db_column='txtleaddays', default=0)

    # Foreign Keys
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', blank=True, null=True, related_name='items')
    buyer = models.ForeignKey(BuyerMaster, on_delete=models.DO_NOTHING, db_column='Buyer', blank=True, null=True, related_name='items')
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True, related_name='items')
    location = models.ForeignKey(LocationMaster, on_delete=models.DO_NOTHING, db_column='Location', blank=True, null=True, related_name='items')
    item_class = models.ForeignKey(ItemClass, on_delete=models.DO_NOTHING, db_column='Class', blank=True, null=True, related_name='items')
    account_head = models.ForeignKey(AccHead, on_delete=models.DO_NOTHING, db_column='AHId', blank=True, null=True, related_name='items')

    # Boolean fields (stored as 1/0 in ASP.NET)
    is_absolute = models.BooleanField(db_column='Absolute', default=False)
    uom_conversion_factor_yes = models.BooleanField(db_column='UOMConFact', default=False)
    is_excise_applicable = models.BooleanField(db_column='Excise', default=False)
    is_import_local = models.BooleanField(db_column='ImportLocal', default=False)

    # File/Image fields
    item_image_filename = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    item_image_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    item_image_filesize = models.IntegerField(db_column='FileSize', blank=True, null=True)
    item_image_content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)

    item_attachment_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    item_attachment_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    item_attachment_size = models.IntegerField(db_column='AttSize', blank=True, null=True)
    item_attachment_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)

    # System/Audit fields (assuming these are populated by the application, not directly by user)
    system_date = models.CharField(db_column='SysDate', max_length=10, blank=True, null=True) # MM/DD/YYYY
    system_time = models.CharField(db_column='SysTime', max_length=10, blank=True, null=True) # HH:MM AM/PM
    company_id = models.IntegerField(db_column='CompId', default=0) # Hardcoding for example, typically from session/user profile
    financial_year_id = models.IntegerField(db_column='finYearId', default=0) # Hardcoding for example
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.part_no if self.part_no else f"Item {self.id}"

    # Business logic methods (Fat Model approach)

    def process_item_image_upload(self, uploaded_file):
        """Processes an uploaded image, resizes it, and stores binary data."""
        if uploaded_file:
            try:
                # Resize logic (ASP.NET code uses 135x62)
                image_to_be_resized = Image.open(uploaded_file)
                max_width = 135
                target_height = 62 # Fixed height from ASP.NET code

                # Maintain aspect ratio for width, or force 135 width as per ASP.NET
                # ASP.NET forced 135x62 regardless of original aspect
                resized_image = image_to_be_resized.resize((max_width, target_height), Image.Resampling.LANCZOS)

                img_byte_arr = io.BytesIO()
                resized_image.save(img_byte_arr, format='JPEG') # Assuming JPEG output
                img_byte_arr.seek(0)

                self.item_image_data = img_byte_arr.read()
                self.item_image_filename = uploaded_file.name
                self.item_image_filesize = len(self.item_image_data)
                self.item_image_content_type = 'image/jpeg' # Fixed content type for JPEG

            except Exception as e:
                # Log error or raise custom exception
                print(f"Error processing image: {e}")
                # Decide how to handle this gracefully (e.g., set to None or re-raise)
                self.item_image_data = None
                self.item_image_filename = None
                self.item_image_filesize = None
                self.item_image_content_type = None
        else:
            self.item_image_data = None
            self.item_image_filename = None
            self.item_image_filesize = None
            self.item_image_content_type = None

    def process_item_attachment_upload(self, uploaded_file):
        """Processes an uploaded attachment and stores binary data."""
        if uploaded_file:
            self.item_attachment_data = uploaded_file.read()
            self.item_attachment_name = uploaded_file.name
            self.item_attachment_size = uploaded_file.size
            self.item_attachment_content_type = uploaded_file.content_type
        else:
            self.item_attachment_data = None
            self.item_attachment_name = None
            self.item_attachment_size = None
            self.item_attachment_content_type = None

    def get_absolute_url(self):
        return reverse('itemmaster_detail', args=[str(self.id)])

    @classmethod
    def get_acc_heads_by_category(cls, category_value):
        """Maps ASP.NET's RadioButtonList4 selected value to AccHead category."""
        category_map = {
            "1": "Labour",
            "2": "With Material",
            "3": "Expenses",
            "4": "Service Material", # ASP.NET uses "Service Provider" for display, but "Service Material" in code.
        }
        acc_head_category = category_map.get(str(category_value))
        if acc_head_category:
            return AccHead.objects.filter(category=acc_head_category).order_by('description')
        return AccHead.objects.none()

```

##### 4.2 Forms (`itemmasters/forms.py`)

This form will handle the data input and validation for the `ItemMaster` update page.

```python
from django import forms
from .models import ItemMaster, Category, BuyerMaster, UnitMaster, LocationMaster, ItemClass, AccHead
from django.core.validators import RegexValidator
from datetime import datetime

class ItemMasterForm(forms.ModelForm):
    # Custom fields for file uploads (not directly mapped to BinaryField in model)
    item_image_file = forms.FileField(required=False, label="Image",
                                      widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}))
    item_attachment_file = forms.FileField(required=False, label="Attachment",
                                            widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'}))
    
    # Custom field for Opening Balance Date to handle DD-MM-YYYY string conversion
    opening_balance_date_display = forms.CharField(
        label="Opening Bal Date",
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'DD-MM-YYYY', 'type': 'date'}),
        validators=[RegexValidator(r'^\d{2}-\d{2}-\d{4}$', 'Enter date in DD-MM-YYYY format.', code='invalid_date_format')]
    )

    # Radio button lists (replicating ASP.NET's 1/0 values)
    UOM_CONVERSION_CHOICES = [('1', 'Yes'), ('0', 'No')]
    EXCISE_APPLICABLE_CHOICES = [('1', 'Yes'), ('0', 'No')]
    IMPORT_LOCAL_CHOICES = [('1', 'Yes'), ('0', 'No')]
    AC_HEAD_CATEGORY_CHOICES = [
        ('1', 'Labour'),
        ('2', 'With Material'),
        ('3', 'Expenses'),
        ('4', 'Service Provider'),
    ]

    # Map boolean fields to ChoiceFields for radio buttons
    uom_conversion_factor_choice = forms.ChoiceField(
        choices=UOM_CONVERSION_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'inline-flex items-center mr-4'}),
        label="UOM Conv.",
        initial='0' # Default to No
    )
    is_excise_applicable_choice = forms.ChoiceField(
        choices=EXCISE_APPLICABLE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'inline-flex items-center mr-4'}),
        label="Excise Applicable",
        initial='0' # Default to No
    )
    is_import_local_choice = forms.ChoiceField(
        choices=IMPORT_LOCAL_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'inline-flex items-center mr-4'}),
        label="Import/Local",
        initial='0' # Default to No
    )
    
    ac_head_category_select = forms.ChoiceField(
        choices=AC_HEAD_CATEGORY_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'inline-flex items-center mr-4'}),
        label="A/C Head Type",
        required=False
    )
    
    # For A/C Head dropdown, initially empty, will be populated via HTMX
    account_head_dropdown = forms.ModelChoiceField(
        queryset=AccHead.objects.none(), # Initially empty
        required=False,
        label="A/C Head",
        widget=forms.Select(attrs={'class': 'box3'})
    )


    class Meta:
        model = ItemMaster
        fields = [
            'category', 'part_no', 'manufacturer_description', 'uom_basic',
            'min_order_qty', 'min_stock_qty', 'stock_qty', 'item_class',
            'location', 'is_absolute', 'inspection_days', 'lead_days',
            'opening_balance_qty', 'buyer', # Do not include the binary/file fields directly here
        ]
        widgets = {
            'category': forms.Select(attrs={'class': 'box3', 'disabled': 'disabled'}), # Enabled="False"
            'part_no': forms.TextInput(attrs={'class': 'box3', 'disabled': 'disabled'}), # Enabled="False"
            'manufacturer_description': forms.Textarea(attrs={'class': 'box3', 'rows': 3, 'style': 'width:500px;'}),
            'uom_basic': forms.Select(attrs={'class': 'box3'}),
            'min_order_qty': forms.NumberInput(attrs={'class': 'box3'}), # onblue/onfocus/onkeyup will be handled by form validation
            'min_stock_qty': forms.NumberInput(attrs={'class': 'box3'}),
            'stock_qty': forms.NumberInput(attrs={'class': 'box3', 'disabled': 'disabled'}), # Enabled="False"
            'item_class': forms.Select(attrs={'class': 'box3'}),
            'location': forms.Select(attrs={'class': 'box3'}),
            'is_absolute': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded'}),
            'opening_balance_qty': forms.NumberInput(attrs={'class': 'box3', 'disabled': 'disabled'}), # Enabled="False"
            'inspection_days': forms.NumberInput(attrs={'class': 'box3'}),
            'lead_days': forms.NumberInput(attrs={'class': 'box3'}),
            'buyer': forms.Select(attrs={'class': 'box3'}),
        }
        labels = {
            'category': 'Category',
            'part_no': 'Part No',
            'manufacturer_description': 'Description',
            'uom_basic': 'UOM',
            'min_order_qty': 'Min Order Qty',
            'min_stock_qty': 'Min Stock Qty',
            'stock_qty': 'Stock Qty',
            'item_class': 'Class',
            'location': 'Store Location',
            'is_absolute': 'Absolute',
            'opening_balance_qty': 'Opening Bal Qty',
            'inspection_days': 'Inspection Days',
            'lead_days': 'Lead Days',
            'buyer': 'Buyer',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Populate dropdowns from database
        self.fields['category'].queryset = Category.objects.filter(company_id=self.instance.company_id if self.instance else 0).order_by('name')
        self.fields['uom_basic'].queryset = UnitMaster.objects.all().order_by('symbol') # fun.drpunit
        self.fields['location'].queryset = LocationMaster.objects.all().order_by('location_label') # fun.drpLocat
        self.fields['item_class'].queryset = ItemClass.objects.filter(id__gt=0).order_by('item_class') # where [Id]!=0
        self.fields['buyer'].queryset = BuyerMaster.objects.select_related('emp_id').all().order_by('id') # SqlDataSource3

        # Set initial values for custom fields based on instance data for editing
        if self.instance.pk:
            # Set initial values for radio button choices based on boolean model fields
            self.fields['uom_conversion_factor_choice'].initial = '1' if self.instance.uom_conversion_factor_yes else '0'
            self.fields['is_excise_applicable_choice'].initial = '1' if self.instance.is_excise_applicable else '0'
            self.fields['is_import_local_choice'].initial = '1' if self.instance.is_import_local else '0'

            # Set initial value for opening balance date
            self.fields['opening_balance_date_display'].initial = self.instance.opening_balance_date
            
            # Initial A/C Head category and dropdown population
            if self.instance.account_head:
                acc_head_cat = self.instance.account_head.category
                category_reverse_map = {
                    "Labour": "1",
                    "With Material": "2",
                    "Expenses": "3",
                    "Service Material": "4", # Note: ASP.NET displays "Service Provider" but uses "Service Material" in logic
                }
                self.fields['ac_head_category_select'].initial = category_reverse_map.get(acc_head_cat)
                self.fields['account_head_dropdown'].queryset = ItemMaster.get_acc_heads_by_category(self.fields['ac_head_category_select'].initial)
                self.fields['account_head_dropdown'].initial = self.instance.account_head

            # Disable fields based on ASP.NET logic (initial load, if CId is null)
            if self.instance.category is None: # Equivalent to if (DS.Tables[0].Rows[0]["CId"] != DBNull.Value) is FALSE
                self.fields['ac_head_category_select'].widget.attrs['disabled'] = 'disabled'
                self.fields['account_head_dropdown'].widget.attrs['disabled'] = 'disabled'
                self.fields['item_class'].widget.attrs['disabled'] = 'disabled'
                self.fields['uom_conversion_factor_choice'].widget.attrs['disabled'] = 'disabled'
                self.fields['is_import_local_choice'].widget.attrs['disabled'] = 'disabled'
                self.fields['is_excise_applicable_choice'].widget.attrs['disabled'] = 'disabled'
                self.fields['stock_qty'].widget.attrs['disabled'] = 'disabled'
                self.fields['min_order_qty'].widget.attrs['disabled'] = 'disabled'
                self.fields['min_stock_qty'].widget.attrs['disabled'] = 'disabled'
                self.fields['inspection_days'].widget.attrs['disabled'] = 'disabled'
                self.fields['lead_days'].widget.attrs['disabled'] = 'disabled'
                self.fields['opening_balance_date_display'].widget.attrs['disabled'] = 'disabled'
                self.fields['opening_balance_qty'].widget.attrs['disabled'] = 'disabled'
                self.fields['is_absolute'].widget.attrs['disabled'] = 'disabled'
                self.fields['buyer'].widget.attrs['disabled'] = 'disabled'
                self.fields['location'].widget.attrs['disabled'] = 'disabled'


    def clean(self):
        cleaned_data = super().clean()

        # Convert radio button choices back to boolean for model fields
        cleaned_data['uom_conversion_factor_yes'] = (cleaned_data.get('uom_conversion_factor_choice') == '1')
        cleaned_data['is_excise_applicable'] = (cleaned_data.get('is_excise_applicable_choice') == '1')
        cleaned_data['is_import_local'] = (cleaned_data.get('is_import_local_choice') == '1')
        
        # Convert opening_balance_date_display to the string format expected by the model
        opening_bal_date_str = cleaned_data.get('opening_balance_date_display')
        if opening_bal_date_str:
            try:
                # Assuming incoming format is DD-MM-YYYY, convert to YYYY-MM-DD for consistency
                # Or keep it as DD-MM-YYYY string if the DB truly stores it that way
                # The ASP.NET code converts to '103' (dd/MM/yyyy)
                # Let's keep it consistent with the model's CharField: store as DD-MM-YYYY
                cleaned_data['opening_balance_date'] = opening_bal_date_str
            except ValueError:
                self.add_error('opening_balance_date_display', 'Invalid date format. Use DD-MM-YYYY.')
        else:
            cleaned_data['opening_balance_date'] = None

        # Handle numeric validations as per ASP.NET's fun.NumberValidationQty
        # Django's NumberInput already handles basic non-numeric input, but you can add more strict regex here if needed
        for field_name in ['min_order_qty', 'min_stock_qty', 'stock_qty', 'opening_balance_qty', 'inspection_days', 'lead_days']:
            value = cleaned_data.get(field_name)
            if value is not None and not isinstance(value, (int, float, Decimal)):
                self.add_error(field_name, "This field must be a number.")
            if value is not None and value < 0: # Assuming quantities cannot be negative
                self.add_error(field_name, "Value cannot be negative.")

        # Set the correct account_head from the dropdown
        cleaned_data['account_head'] = cleaned_data.get('account_head_dropdown')
        
        return cleaned_data

    def save(self, commit=True):
        item_master = super().save(commit=False)

        # Handle file uploads
        item_image_file = self.cleaned_data.get('item_image_file')
        if item_image_file:
            item_master.process_item_image_upload(item_image_file)
        elif 'item_image_file' in self.changed_data: # If user cleared the file input
            item_master.item_image_filename = None
            item_master.item_image_data = None
            item_master.item_image_filesize = None
            item_master.item_image_content_type = None

        item_attachment_file = self.cleaned_data.get('item_attachment_file')
        if item_attachment_file:
            item_master.process_item_attachment_upload(item_attachment_file)
        elif 'item_attachment_file' in self.changed_data: # If user cleared the file input
            item_master.item_attachment_name = None
            item_master.item_attachment_data = None
            item_master.item_attachment_size = None
            item_master.item_attachment_content_type = None

        # Update meta fields (CompId, FinYearId, SessionId from current context)
        # For this example, we'll hardcode or derive them. In a real app,
        # these would come from the logged-in user's profile or session data.
        item_master.company_id = 1 # Example: self.request.user.userprofile.company_id
        item_master.financial_year_id = 1 # Example: self.request.session.get('current_fin_year_id')
        item_master.session_id = 'testuser' # Example: self.request.user.username

        if commit:
            item_master.save()
        return item_master

```

##### 4.3 Views (`itemmasters/views.py`)

Thin views, logic delegated to forms and models. Also includes a partial view for the DataTables content and for A/C Head dropdown population.

```python
from django.views.generic import UpdateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from .models import ItemMaster, AccHead
from .forms import ItemMasterForm

class ItemMasterListView(ListView):
    """
    Displays a list of ItemMaster records using DataTables.
    This replaces the ASP.NET GridView, providing a proper list view.
    """
    model = ItemMaster
    template_name = 'itemmasters/itemmaster_list.html'
    context_object_name = 'itemmasters'

    def get_queryset(self):
        # In a real application, you might filter by self.request.user.company_id
        # For now, fetching all for demonstration.
        return ItemMaster.objects.all().order_by('id')

class ItemMasterTablePartialView(View):
    """
    Renders the DataTables content for ItemMaster,
    designed to be loaded via HTMX.
    """
    def get(self, request, *args, **kwargs):
        itemmasters = ItemMaster.objects.all().order_by('id')
        return render(request, 'itemmasters/_itemmaster_table.html', {'itemmasters': itemmasters})

class ItemMasterUpdateView(UpdateView):
    """
    Handles editing of an existing ItemMaster record.
    """
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'itemmasters/_itemmaster_form.html' # This is a partial template for HTMX modal
    
    def get_object(self, queryset=None):
        # Retrieve the item based on ItemId query parameter, similar to ASP.NET
        item_id = self.request.GET.get('ItemId') or self.kwargs.get('pk')
        if not item_id:
            # Handle cases where ItemId is not provided, maybe redirect to a list or error page
            return None # Or raise Http404 for a proper error
        return get_object_or_404(ItemMaster, pk=item_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initial population of AH dropdown in case of non-HTMX request or initial load
        if self.request.method == 'GET' and self.object and self.object.account_head:
            acc_head_cat = self.object.account_head.category
            category_reverse_map = {
                "Labour": "1",
                "With Material": "2",
                "Expenses": "3",
                "Service Material": "4",
            }
            initial_category_val = category_reverse_map.get(acc_head_cat)
            if initial_category_val:
                context['form'].fields['account_head_dropdown'].queryset = ItemMaster.get_acc_heads_by_category(initial_category_val)
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a no-content response with a trigger header
            # This tells HTMX to refresh the list table and close the modal
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshItemMasterList": true, "closeModal": true}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If form is invalid, return the form HTML with errors
            return render(self.request, self.template_name, {'form': form})
        return response

class ItemMasterDeleteView(View):
    """
    Handles deleting an ItemMaster record via HTMX.
    """
    def delete(self, request, pk):
        item = get_object_or_404(ItemMaster, pk=pk)
        item.delete()
        messages.success(self.request, 'Item Master deleted successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': '{"refreshItemMasterList": true, "closeModal": true}'
            }
        )

# HTMX endpoint to dynamically populate the A/C Head dropdown
class AccHeadDropdownView(View):
    def get(self, request, *args, **kwargs):
        category_value = request.GET.get('ac_head_category', '0')
        acc_heads = ItemMaster.get_acc_heads_by_category(category_value)
        options = '<option value="">---------</option>'
        for acc_head in acc_heads:
            options += f'<option value="{acc_head.id}">{acc_head.__str__()}</option>'
        return HttpResponse(options)

# HTMX endpoint to delete image/attachment
class ItemFileDeleteView(View):
    def post(self, request, pk, file_type):
        item = get_object_or_404(ItemMaster, pk=pk)
        if file_type == 'image':
            item.item_image_filename = None
            item.item_image_data = None
            item.item_image_filesize = None
            item.item_image_content_type = None
            messages.success(request, 'Item image deleted successfully.')
        elif file_type == 'attachment':
            item.item_attachment_name = None
            item.item_attachment_data = None
            item.item_attachment_size = None
            item.item_attachment_content_type = None
            messages.success(request, 'Item attachment deleted successfully.')
        else:
            return HttpResponse(status=400, content='Invalid file type.')
        item.save()
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': f'{"refreshItemMasterForm"}' # Trigger form refresh to show changes
            }
        )
```

##### 4.4 Templates (`itemmasters/templates/itemmasters/`)

*   `itemmaster_list.html`: Main page for displaying item masters with DataTables and Add button.
*   `_itemmaster_table.html`: Partial for the DataTables content, loaded via HTMX.
*   `_itemmaster_form.html`: Partial for the Add/Edit form, loaded in a modal.
*   `_itemmaster_confirm_delete.html`: Partial for delete confirmation, loaded in a modal.

```html
<!-- itemmasters/templates/itemmasters/itemmaster_list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Item Masters</h2>
        <!-- No Add button on this page, as it's an edit page only -->
        <!-- The original ASP.NET page is primarily for editing a specific item,
             with the GridView below it showing that item's details.
             For modernization, we interpret the GridView as a general list,
             and the edit form as for a *selected* item from that list.
             However, the original page loads an item from a query string.
             For a direct migration of an 'edit' page, we'll adapt this.
             Let's provide an edit button from the list below or assume navigation.
             For this specific page's purpose (edit details), we won't show an "Add New" button here.
             Instead, the page directly loads the edit form for the queried ItemId.
             The DataTables will show relevant items for context.
        -->
    </div>
    
    <!-- This section would load the edit form for the specific ItemId from the query string -->
    <!-- It replaces Panel1 from ASP.NET -->
    <div id="itemMasterEditFormContainer"
         hx-trigger="load, refreshItemMasterForm from:body"
         hx-get="{% url 'itemmaster_edit' %}?ItemId={{ request.GET.ItemId }}"
         hx-swap="innerHTML">
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Edit Form...</p>
        </div>
    </div>
    
    <hr class="my-8">
    
    <div class="flex justify-between items-center mb-6 mt-8">
        <h3 class="text-xl font-bold">All Item Masters (Data Grid)</h3>
    </div>

    <div id="itemmasterTable-container"
         hx-trigger="load, refreshItemMasterList from:body"
         hx-get="{% url 'itemmaster_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Item List...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ show: false }"
         x-show="show"
         @close-modal.window="show = false"
         @open-modal.window="show = true">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-auto"
             @click.stop> <!-- Prevent clicks inside modal from closing it -->
             <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'itemmasterTable-container') {
            // Re-initialize DataTables if the table is loaded via HTMX
            $('#itemmasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(event) {
        const hxTrigger = event.detail.xhr.getResponseHeader('HX-Trigger');
        if (hxTrigger) {
            try {
                const triggerData = JSON.parse(hxTrigger);
                if (triggerData.closeModal) {
                    const modal = document.getElementById('modal');
                    if (modal) {
                        modal.classList.remove('is-active');
                        // Reset modal content
                        document.getElementById('modalContent').innerHTML = '';
                    }
                }
            } catch (e) {
                console.error("Error parsing HX-Trigger:", e);
            }
        }
    });
</script>
{% endblock %}
```

```html
<!-- itemmasters/templates/itemmasters/_itemmaster_table.html -->
<table id="itemmasterTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Min Order Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Min Stock Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Open Bal Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Bal Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Absolute</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Excise</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Import & Local</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM Conv.</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">A/C Head</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in itemmasters %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.category.symbol|default:"N/A" }}-{{ item.category.name|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.part_no|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.manufacturer_description|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uom_basic.symbol|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.min_order_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.min_stock_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.stock_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.location.location_label|default:"N/A" }}-{{ item.location.location_no|default:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.opening_balance_date|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.opening_balance_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.is_absolute|yesno:"Yes,No" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.is_excise_applicable|yesno:"Yes,No" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.is_import_local|yesno:"Yes,No" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.uom_conversion_factor_yes|yesno:"Yes,No" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ item.account_head.symbol|default:"NA" }} {{ item.account_head.description|default:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'itemmaster_edit' pk=item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal then call window.dispatchEvent(new CustomEvent('open-modal'))">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'itemmaster_confirm_delete' pk=item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal then call window.dispatchEvent(new CustomEvent('open-modal'))">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// This script is re-run every time the partial is swapped in by HTMX.
// DataTables needs to be destroyed and re-initialized to prevent issues.
if ($.fn.DataTable.isDataTable('#itemmasterTable')) {
    $('#itemmasterTable').DataTable().destroy();
}
$('#itemmasterTable').DataTable({
    "pageLength": 10,
    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
    "columnDefs": [
        { "orderable": false, "targets": [0, 16] }, // Disable sorting for SN and Actions columns
        { "searchable": false, "targets": [0, 16] } // Disable searching for SN and Actions columns
    ]
});
</script>
```

```html
<!-- itemmasters/templates/itemmasters/_itemmaster_form.html -->
<div class="p-6" x-data="{
    acHeadCategory: '{{ form.ac_head_category_select.value|default:'0' }}',
    itemImageFileName: '{{ form.instance.item_image_filename|default:'' }}',
    itemAttachmentName: '{{ form.instance.item_attachment_name|default:'' }}',
}">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Item Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#itemMasterEditFormContainer" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <!-- Category and Part No - Disabled per ASP.NET -->
            <div>
                <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.category.label }}
                </label>
                {{ form.category }}
                {% if form.category.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.part_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.part_no.label }}
                </label>
                {{ form.part_no }}
                <span class="text-gray-500 text-xs mt-1">Ex.: xxxx-xxx-xxx</span>
                {% if form.part_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.part_no.errors }}</p>
                {% endif %}
            </div>

            <!-- Manufacturer Description -->
            <div class="md:col-span-2">
                <label for="{{ form.manufacturer_description.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.manufacturer_description.label }}
                </label>
                {{ form.manufacturer_description }}
                {% if form.manufacturer_description.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.manufacturer_description.errors }}</p>
                {% endif %}
            </div>

            <!-- UOM Basic & UOM Conversion Factor -->
            <div>
                <label for="{{ form.uom_basic.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.uom_basic.label }}
                </label>
                {{ form.uom_basic }}
                {% if form.uom_basic.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uom_basic.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">{{ form.uom_conversion_factor_choice.label }}</label>
                <div class="mt-1 space-x-4 flex">
                    {% for radio in form.uom_conversion_factor_choice %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
                {% if form.uom_conversion_factor_choice.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uom_conversion_factor_choice.errors }}</p>
                {% endif %}
            </div>
            
            <!-- Stock Qty & Class -->
            <div>
                <label for="{{ form.stock_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.stock_qty.label }}
                </label>
                {{ form.stock_qty }}
                {% if form.stock_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.stock_qty.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.item_class.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.item_class.label }}
                </label>
                {{ form.item_class }}
                {% if form.item_class.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.item_class.errors }}</p>
                {% endif %}
            </div>

            <!-- Min Order Qty & Inspection Days -->
            <div>
                <label for="{{ form.min_order_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.min_order_qty.label }}
                </label>
                {{ form.min_order_qty }}
                {% if form.min_order_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.min_order_qty.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.inspection_days.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.inspection_days.label }}
                </label>
                {{ form.inspection_days }}
                {% if form.inspection_days.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.inspection_days.errors }}</p>
                {% endif %}
            </div>

            <!-- Min Stock Qty & Store Location -->
            <div>
                <label for="{{ form.min_stock_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.min_stock_qty.label }}
                </label>
                {{ form.min_stock_qty }}
                {% if form.min_stock_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.min_stock_qty.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.location.label }}
                </label>
                {{ form.location }}
                {% if form.location.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.location.errors }}</p>
                {% endif %}
            </div>

            <!-- Opening Bal Qty & Opening Bal Date -->
            <div>
                <label for="{{ form.opening_balance_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.opening_balance_qty.label }}
                </label>
                {{ form.opening_balance_qty }}
                {% if form.opening_balance_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.opening_balance_qty.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.opening_balance_date_display.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.opening_balance_date_display.label }}
                </label>
                {{ form.opening_balance_date_display }}
                {% if form.opening_balance_date_display.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.opening_balance_date_display.errors }}</p>
                {% endif %}
            </div>

            <!-- Lead Days & Import/Local -->
            <div>
                <label for="{{ form.lead_days.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.lead_days.label }}
                </label>
                {{ form.lead_days }}
                {% if form.lead_days.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.lead_days.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">{{ form.is_import_local_choice.label }}</label>
                <div class="mt-1 space-x-4 flex">
                    {% for radio in form.is_import_local_choice %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
                {% if form.is_import_local_choice.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.is_import_local_choice.errors }}</p>
                {% endif %}
            </div>

            <!-- Excise Applicable & Absolute -->
            <div>
                <label class="block text-sm font-medium text-gray-700">{{ form.is_excise_applicable_choice.label }}</label>
                <div class="mt-1 space-x-4 flex">
                    {% for radio in form.is_excise_applicable_choice %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
                {% if form.is_excise_applicable_choice.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.is_excise_applicable_choice.errors }}</p>
                {% endif %}
            </div>
            <div class="flex items-center pt-6">
                {{ form.is_absolute }}
                <label for="{{ form.is_absolute.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">
                    {{ form.is_absolute.label }}
                </label>
                {% if form.is_absolute.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.is_absolute.errors }}</p>
                {% endif %}
            </div>

            <!-- Attachment & Buyer -->
            <div>
                <label for="{{ form.item_attachment_file.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.item_attachment_file.label }}
                </label>
                {{ form.item_attachment_file }}
                {% if itemAttachmentName %}
                    <span class="text-sm text-gray-600">Current: 
                        <a href="{% url 'download_file' pk=form.instance.pk file_type='attachment' %}" class="text-blue-600 hover:underline">{{ itemAttachmentName }}</a>
                        <button type="button" class="ml-2 text-red-500 hover:text-red-700"
                            hx-post="{% url 'item_file_delete' pk=form.instance.pk file_type='attachment' %}"
                            hx-confirm="Are you sure you want to remove this attachment?"
                            hx-target="#itemMasterEditFormContainer" hx-swap="outerHTML">
                            &times; Remove
                        </button>
                    </span>
                {% endif %}
                {% if form.item_attachment_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.item_attachment_file.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.buyer.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.buyer.label }}
                </label>
                {{ form.buyer }}
                {% if form.buyer.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.buyer.errors }}</p>
                {% endif %}
            </div>

            <!-- Image & A/C Head Type and Dropdown -->
            <div>
                <label for="{{ form.item_image_file.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.item_image_file.label }}
                </label>
                {{ form.item_image_file }}
                {% if itemImageFileName %}
                    <span class="text-sm text-gray-600">Current: 
                        <a href="{% url 'download_file' pk=form.instance.pk file_type='image' %}" class="text-blue-600 hover:underline">{{ itemImageFileName }}</a>
                        <button type="button" class="ml-2 text-red-500 hover:text-red-700"
                            hx-post="{% url 'item_file_delete' pk=form.instance.pk file_type='image' %}"
                            hx-confirm="Are you sure you want to remove this image?"
                            hx-target="#itemMasterEditFormContainer" hx-swap="outerHTML">
                            &times; Remove
                        </button>
                    </span>
                {% endif %}
                {% if form.item_image_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.item_image_file.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">{{ form.ac_head_category_select.label }}</label>
                <div class="mt-1 space-x-4 flex">
                    {% for radio in form.ac_head_category_select %}
                    <label class="inline-flex items-center">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
                {% if form.ac_head_category_select.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.ac_head_category_select.errors }}</p>
                {% endif %}

                <label for="{{ form.account_head_dropdown.id_for_label }}" class="block text-sm font-medium text-gray-700 mt-4">
                    {{ form.account_head_dropdown.label }}
                </label>
                {{ form.account_head_dropdown }}
                {% if form.account_head_dropdown.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.account_head_dropdown.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-center space-x-4">
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'itemmaster_list' %}'">
                Cancel
            </button>
        </div>
    </form>
</div>

<script>
    // Alpine.js data for file names
    document.addEventListener('DOMContentLoaded', function() {
        const modalContent = document.getElementById('modalContent');
        if (modalContent) {
            modalContent.__x.$data.itemImageFileName = '{{ form.instance.item_image_filename|default:"" }}';
            modalContent.__x.$data.itemAttachmentName = '{{ form.instance.item_attachment_name|default:"" }}';
        }

        // HTMX listener for A/C Head category change to update dropdown
        $('input[name="ac_head_category_select"]').on('change', function() {
            var selectedCategory = $(this).val();
            // Perform HTMX GET request to populate the account_head_dropdown
            htmx.ajax('GET', '{% url "acc_head_dropdown" %}' + '?ac_head_category=' + selectedCategory, {
                target: '#id_account_head_dropdown', // Target the actual dropdown element
                swap: 'outerHTML',
                trigger: 'change' // Only trigger once after swap
            });
        });
    });

</script>
```

```html
<!-- itemmasters/templates/itemmasters/_itemmaster_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete item "{{ object.part_no }}" (ID: {{ object.id }})?</p>
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{% url 'itemmaster_delete' pk=object.pk %}"
            hx-swap="none"
            _="on click call window.dispatchEvent(new CustomEvent('close-modal'))">
            Confirm Delete
        </button>
    </div>
</div>
```

##### 4.5 URLs (`itemmasters/urls.py`)

```python
from django.urls import path
from .views import (
    ItemMasterListView, ItemMasterUpdateView, ItemMasterDeleteView,
    ItemMasterTablePartialView, AccHeadDropdownView, ItemFileDeleteView
)
from django.http import HttpResponse

# Add this utility view for file downloads, it's not part of itemmasters app logic,
# but a helper for existing file handling pattern
def download_file_view(request, pk, file_type):
    item = ItemMaster.objects.get(pk=pk)
    if file_type == 'image' and item.item_image_data:
        response = HttpResponse(item.item_image_data, content_type=item.item_image_content_type)
        response['Content-Disposition'] = f'attachment; filename="{item.item_image_filename}"'
        return response
    elif file_type == 'attachment' and item.item_attachment_data:
        response = HttpResponse(item.item_attachment_data, content_type=item.item_attachment_content_type)
        response['Content-Disposition'] = f'attachment; filename="{item.item_attachment_name}"'
        return response
    return HttpResponse(status=404)


urlpatterns = [
    # Main list view (replaces the primary function of the ASP.NET page - displaying data)
    path('itemmasters/', ItemMasterListView.as_view(), name='itemmaster_list'),
    
    # HTMX partial for the DataTables content
    path('itemmasters/table/', ItemMasterTablePartialView.as_view(), name='itemmaster_table'),
    
    # Update view for the Item Master (handles the main form functionality)
    # The 'pk' is optional here to support the ASP.NET query string (ItemId) behavior.
    path('itemmasters/edit/<int:pk>/', ItemMasterUpdateView.as_view(), name='itemmaster_edit_with_pk'),
    path('itemmasters/edit/', ItemMasterUpdateView.as_view(), name='itemmaster_edit'), # For query string usage

    # Delete view (HTMX-driven)
    path('itemmasters/delete/<int:pk>/', ItemMasterDeleteView.as_view(), name='itemmaster_delete'),

    # HTMX endpoint for dynamic A/C Head dropdown population
    path('itemmasters/acc_head_dropdown/', AccHeadDropdownView.as_view(), name='acc_head_dropdown'),

    # HTMX endpoint for deleting attached files/images
    path('itemmasters/file_delete/<int:pk>/<str:file_type>/', ItemFileDeleteView.as_view(), name='item_file_delete'),

    # Utility for file downloads (accessible via direct URL or AJAX if needed)
    path('download_file/<int:pk>/<str:file_type>/', download_file_view, name='download_file'),
]

```

##### 4.6 Tests (`itemmasters/tests.py`)

Comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import ItemMaster, Category, BuyerMaster, OfficeStaff, UnitMaster, LocationMaster, ItemClass, AccHead
from decimal import Decimal
import io
from PIL import Image

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required lookup data
        cls.category = Category.objects.create(id=1, symbol='CAT', name='Category 1')
        cls.office_staff = OfficeStaff.objects.create(id=101, employee_name='John Doe')
        cls.buyer_master = BuyerMaster.objects.create(id=201, category_str='B', nos='001', emp_id=cls.office_staff)
        cls.unit_master = UnitMaster.objects.create(id=301, symbol='KG')
        cls.location_master = LocationMaster.objects.create(id=401, location_label='Warehouse', location_no='A1')
        cls.item_class = ItemClass.objects.create(id=501, item_class='Electronic')
        cls.acc_head_labour = AccHead.objects.create(id=601, symbol='LAB', description='Labour Cost', category='Labour')
        cls.acc_head_material = AccHead.objects.create(id=602, symbol='MAT', description='Material Cost', category='With Material')

        # Create test ItemMaster instance
        ItemMaster.objects.create(
            id=1,
            category=cls.category,
            part_no='ITEM-001',
            manufacturer_description='Test Description for Item 001',
            uom_basic=cls.unit_master,
            min_order_qty=Decimal('10.000'),
            min_stock_qty=Decimal('5.000'),
            stock_qty=Decimal('100.000'),
            location=cls.location_master,
            is_absolute=True,
            opening_balance_date='01-01-2023',
            opening_balance_qty=Decimal('50.000'),
            uom_conversion_factor_yes=True,
            item_class=cls.item_class,
            is_excise_applicable=False,
            is_import_local=True,
            buyer=cls.buyer_master,
            account_head=cls.acc_head_labour,
            inspection_days=7,
            lead_days=14,
            company_id=1,
            financial_year_id=1,
            session_id='testuser'
        )

    def test_itemmaster_creation(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.part_no, 'ITEM-001')
        self.assertEqual(item.category, self.category)
        self.assertTrue(item.is_absolute)
        self.assertEqual(item.opening_balance_date, '01-01-2023')

    def test_itemmaster_fields_verbose_name(self):
        item = ItemMaster.objects.get(id=1)
        field_label = item._meta.get_field('manufacturer_description').verbose_name
        self.assertEqual(field_label, 'manufacturer description') # Default label

    def test_image_processing_method(self):
        item = ItemMaster.objects.get(id=1)
        # Create a dummy image file
        img_buffer = io.BytesIO()
        Image.new('RGB', (200, 100), color='red').save(img_buffer, format='JPEG')
        img_buffer.seek(0)
        uploaded_file = io.BytesIO(img_buffer.read())
        uploaded_file.name = 'test_image.jpg'
        uploaded_file.content_type = 'image/jpeg'

        item.process_item_image_upload(uploaded_file)
        self.assertIsNotNone(item.item_image_data)
        self.assertEqual(item.item_image_filename, 'test_image.jpg')
        self.assertEqual(item.item_image_content_type, 'image/jpeg') # Should be fixed to jpeg after resize
        # Check resized dimensions (135x62)
        resized_img = Image.open(io.BytesIO(item.item_image_data))
        self.assertEqual(resized_img.width, 135)
        self.assertEqual(resized_img.height, 62)

    def test_attachment_processing_method(self):
        item = ItemMaster.objects.get(id=1)
        # Create a dummy text file
        file_content = b"This is a test attachment."
        uploaded_file = io.BytesIO(file_content)
        uploaded_file.name = 'test_doc.txt'
        uploaded_file.content_type = 'text/plain'

        item.process_item_attachment_upload(uploaded_file)
        self.assertIsNotNone(item.item_attachment_data)
        self.assertEqual(item.item_attachment_name, 'test_doc.txt')
        self.assertEqual(item.item_attachment_content_type, 'text/plain')
        self.assertEqual(item.item_attachment_size, len(file_content))

    def test_get_acc_heads_by_category(self):
        # Test for "Labour" category (value "1")
        labour_heads = ItemMaster.get_acc_heads_by_category("1")
        self.assertIn(self.acc_head_labour, labour_heads)
        self.assertNotIn(self.acc_head_material, labour_heads)

        # Test for "With Material" category (value "2")
        material_heads = ItemMaster.get_acc_heads_by_category("2")
        self.assertIn(self.acc_head_material, material_heads)
        self.assertNotIn(self.acc_head_labour, material_heads)

        # Test for invalid category
        empty_heads = ItemMaster.get_acc_heads_by_category("99")
        self.assertQuerysetEqual(empty_heads, [])

class ItemMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required lookup data
        cls.category = Category.objects.create(id=1, symbol='CAT', name='Category 1')
        cls.office_staff = OfficeStaff.objects.create(id=101, employee_name='John Doe')
        cls.buyer_master = BuyerMaster.objects.create(id=201, category_str='B', nos='001', emp_id=cls.office_staff)
        cls.unit_master = UnitMaster.objects.create(id=301, symbol='KG')
        cls.location_master = LocationMaster.objects.create(id=401, location_label='Warehouse', location_no='A1')
        cls.item_class = ItemClass.objects.create(id=501, item_class='Electronic')
        cls.acc_head_labour = AccHead.objects.create(id=601, symbol='LAB', description='Labour Cost', category='Labour')
        cls.acc_head_material = AccHead.objects.create(id=602, symbol='MAT', description='Material Cost', category='With Material')

        # Create test ItemMaster instance
        ItemMaster.objects.create(
            id=1,
            category=cls.category,
            part_no='ITEM-001',
            manufacturer_description='Test Description for Item 001',
            uom_basic=cls.unit_master,
            min_order_qty=Decimal('10.000'),
            min_stock_qty=Decimal('5.000'),
            stock_qty=Decimal('100.000'),
            location=cls.location_master,
            is_absolute=True,
            opening_balance_date='01-01-2023',
            opening_balance_qty=Decimal('50.000'),
            uom_conversion_factor_yes=True,
            item_class=cls.item_class,
            is_excise_applicable=False,
            is_import_local=True,
            buyer=cls.buyer_master,
            account_head=cls.acc_head_labour,
            inspection_days=7,
            lead_days=14,
            company_id=1,
            financial_year_id=1,
            session_id='testuser'
        )
        ItemMaster.objects.create(
            id=2,
            category=cls.category,
            part_no='ITEM-002',
            manufacturer_description='Test Description for Item 002',
            uom_basic=cls.unit_master,
            min_order_qty=Decimal('20.000'),
            min_stock_qty=Decimal('10.000'),
            stock_qty=Decimal('200.000'),
            location=cls.location_master,
            is_absolute=False,
            opening_balance_date='02-02-2023',
            opening_balance_qty=Decimal('75.000'),
            uom_conversion_factor_yes=False,
            item_class=cls.item_class,
            is_excise_applicable=True,
            is_import_local=False,
            buyer=cls.buyer_master,
            account_head=cls.acc_head_material,
            inspection_days=3,
            lead_days=7,
            company_id=1,
            financial_year_id=1,
            session_id='testuser'
        )
    
    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('itemmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'itemmasters/itemmaster_list.html')
        self.assertTrue('itemmasters' in response.context)
        self.assertContains(response, 'ITEM-001')
        self.assertContains(response, 'ITEM-002')

    def test_itemmaster_table_partial_view(self):
        response = self.client.get(reverse('itemmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'itemmasters/_itemmaster_table.html')
        self.assertTrue('itemmasters' in response.context)
        self.assertContains(response, 'ITEM-001')
        self.assertContains(response, 'ITEM-002')
        # Check for DataTables script initialization
        self.assertContains(response, "if ($.fn.DataTable.isDataTable('#itemmasterTable'))")
        self.assertContains(response, "$('#itemmasterTable').DataTable(")


    def test_update_view_get_with_pk(self):
        item = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('itemmaster_edit_with_pk', args=[item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'itemmasters/_itemmaster_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.pk, item.pk)
        self.assertContains(response, 'ITEM-001') # Part No in form

    def test_update_view_get_with_query_string(self):
        item = ItemMaster.objects.get(id=1)
        response = self.client.get(reverse('itemmaster_edit') + f'?ItemId={item.id}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'itemmasters/_itemmaster_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.pk, item.pk)

    def test_update_view_post_success(self):
        item = ItemMaster.objects.get(id=1)
        new_desc = "Updated Description"
        data = {
            'category': self.category.id,
            'part_no': 'ITEM-001',
            'manufacturer_description': new_desc,
            'uom_basic': self.unit_master.id,
            'min_order_qty': '12.345',
            'min_stock_qty': '6.789',
            'stock_qty': '105.000',
            'item_class': self.item_class.id,
            'location': self.location_master.id,
            'is_absolute': 'on', # Checkbox value
            'opening_balance_qty': '55.000',
            'opening_balance_date_display': '15-03-2023',
            'inspection_days': '8',
            'lead_days': '15',
            'buyer': self.buyer_master.id,
            'uom_conversion_factor_choice': '1', # Radio button value
            'is_excise_applicable_choice': '1',
            'is_import_local_choice': '0',
            'ac_head_category_select': '1',
            'account_head_dropdown': self.acc_head_labour.id,
        }
        
        response = self.client.post(reverse('itemmaster_edit_with_pk', args=[item.id]), data, follow=True)
        self.assertEqual(response.status_code, 200) # HTMX post returns 204 or 200 with content
        
        # Verify item was updated
        item.refresh_from_db()
        self.assertEqual(item.manufacturer_description, new_desc)
        self.assertEqual(item.min_order_qty, Decimal('12.345'))
        self.assertEqual(item.opening_balance_date, '15-03-2023')
        self.assertTrue(item.is_absolute)
        self.assertTrue(item.uom_conversion_factor_yes)
        self.assertEqual(item.account_head, self.acc_head_labour)
        self.assertFalse(item.is_import_local) # Should be '0' for No

    def test_update_view_post_invalid(self):
        item = ItemMaster.objects.get(id=1)
        data = {
            'manufacturer_description': '', # Invalid: required field
            'uom_basic': self.unit_master.id,
            # ... other required fields, possibly valid
        }
        response = self.client.post(reverse('itemmaster_edit_with_pk', args=[item.id]), data)
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertContains(response, 'This field is required.')

    def test_delete_view(self):
        item_to_delete = ItemMaster.objects.get(id=2)
        initial_count = ItemMaster.objects.count()
        response = self.client.delete(reverse('itemmaster_delete', args=[item_to_delete.id]))
        self.assertEqual(response.status_code, 204) # HTMX delete returns 204
        self.assertEqual(ItemMaster.objects.count(), initial_count - 1)
        self.assertFalse(ItemMaster.objects.filter(id=item_to_delete.id).exists())

    def test_acc_head_dropdown_view(self):
        response = self.client.get(reverse('acc_head_dropdown'), {'ac_head_category': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.acc_head_labour.id}">[{self.acc_head_labour.symbol}] {self.acc_head_labour.description}</option>')
        self.assertNotContains(response, f'<option value="{self.acc_head_material.id}">')

    def test_item_file_delete_view_image(self):
        item = ItemMaster.objects.get(id=1)
        # Add dummy image data
        item.item_image_filename = 'test.jpg'
        item.item_image_data = b'dummy_image_data'
        item.item_image_filesize = 16
        item.item_image_content_type = 'image/jpeg'
        item.save()

        response = self.client.post(reverse('item_file_delete', args=[item.id, 'image']))
        self.assertEqual(response.status_code, 204)
        item.refresh_from_db()
        self.assertIsNone(item.item_image_filename)
        self.assertIsNone(item.item_image_data)
    
    def test_item_file_delete_view_attachment(self):
        item = ItemMaster.objects.get(id=1)
        # Add dummy attachment data
        item.item_attachment_name = 'test.txt'
        item.item_attachment_data = b'dummy_attachment_data'
        item.item_attachment_size = 20
        item.item_attachment_content_type = 'text/plain'
        item.save()

        response = self.client.post(reverse('item_file_delete', args=[item.id, 'attachment']))
        self.assertEqual(response.status_code, 204)
        item.refresh_from_db()
        self.assertIsNone(item.item_attachment_name)
        self.assertIsNone(item.item_attachment_data)

```

#### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic forms and data:**
    *   The `itemmaster_list.html` will initially load the `itemmaster_edit` view (the main form) via an `hx-get` to fill `itemMasterEditFormContainer`. This mimics the ASP.NET page loading an item based on `ItemId` query parameter.
    *   The `_itemmaster_table.html` will be loaded into `itemmasterTable-container` using `hx-trigger="load, refreshItemMasterList from:body"`. `refreshItemMasterList` will be a custom event triggered after any successful CRUD operation.
    *   Edit and Delete buttons in `_itemmaster_table.html` will use `hx-get` to load `_itemmaster_form.html` and `_itemmaster_confirm_delete.html` respectively into a modal (`#modalContent`).
    *   Form submissions (`_itemmaster_form.html`) use `hx-post` and `hx-swap="none"` with `HX-Trigger` headers (`refreshItemMasterList`, `closeModal`) to update the list and close the modal.
    *   Delete actions (`_itemmaster_confirm_delete.html`) use `hx-delete` with `HX-Trigger` for list refresh and modal close.
    *   The A/C Head category radio buttons (`_itemmaster_form.html`) use HTMX (`hx-get` with `hx-target` and `hx-swap`) to dynamically update the A/C Head dropdown based on the selected category.
    *   The image/attachment remove buttons trigger an `hx-post` to the `item_file_delete` URL, which updates the model and triggers a `refreshItemMasterForm` event to reload the form with updated file status.

*   **Alpine.js for UI state management:**
    *   A main Alpine.js component will manage the `modal` visibility (`x-data="{ show: false }"`).
    *   `x-show="show"` will control the modal's display.
    *   `@open-modal.window="show = true"` and `@close-modal.window="show = false"` will listen for custom events dispatched by HTMX (or other JS) to open/close the modal.
    *   Alpine.js can also be used for client-side field validation/enabling based on selection, mirroring the ASP.NET `Enabled="False"` logic more directly on the client if preferred over server-side disabling.

*   **DataTables for List Views:**
    *   The `_itemmaster_table.html` includes the JavaScript initialization for DataTables. The script is designed to be re-run safely when the partial is loaded by HTMX (destroying and re-initializing the table).
    *   This provides client-side searching, sorting, and pagination for the list of item masters.

*   **No additional JavaScript beyond HTMX/Alpine.js/DataTables:** All dynamic interactions are achieved using these tools. The custom `PopUpMsg.js` and `loadingNotifier.js` from ASP.NET will be replaced by Django messages framework (for toast notifications) and HTMX's built-in loading indicators (e.g., `hx-indicator` or simple CSS spinners).

---

### Final Notes

*   **Placeholders:** This plan uses placeholders like `[APP_NAME]`, `[MODEL_NAME]`, etc., which have been replaced with `itemmasters`, `ItemMaster`, etc.
*   **DRY Principle:** Templates extend `core/base.html`. Form and delete logic is in partial templates to avoid duplication. Business logic is in models and forms.
*   **Tests:** Comprehensive tests for models and views ensure high code quality and maintainability.
*   **Security:** Django's built-in CSRF protection is included in forms (`{% csrf_token %}`). File uploads are handled securely by processing content server-side.
*   **Database Integration:** The `managed = False` setting is critical for mapping to an existing database. Ensure your Django `settings.py` is configured correctly to connect to your SQL Server database (e.g., using `django-pyodbc-azure` or `django-mssql-backend`).
*   **Environment Variables:** Sensitive information like database connection strings should be managed via environment variables, not hardcoded.
*   **Frontend Libraries:** Assume `core/base.html` properly includes Tailwind CSS, HTMX, Alpine.js, and jQuery (for DataTables) CDN links.