## ASP.NET to Django Conversion Script: Item Master Module

This document outlines a strategic plan to modernize the existing ASP.NET Item Master module into a robust, scalable, and maintainable Django application. Our approach leverages AI-assisted automation to streamline the migration, focusing on modern Django patterns, HTMX, and Alpine.js for a highly interactive user experience without complex JavaScript.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we infer the following primary tables and their relationships:

-   **`tblDG_Item_Master` (Item Master)**: This is the main table for item details.
    -   Columns: `Id` (PK), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `CId` (FK to Category), `PartNo` (concatenated from three segments), `ManfDesc`, `UOMBasic` (FK to UOM), `MinOrderQty`, `MinStockQty`, `StockQty`, `Location` (FK to Location), `FileData` (for image), `Absolute` (boolean), `OpeningBalDate`, `OpeningBalQty`, `ItemCode`, `Class` (FK to ItemClass), `LeadDays`, `InspectionDays`, `FileName`, `FileSize`, `ContentType` (for FileData), `Excise` (boolean), `ImportLocal` (boolean), `UOMConFact` (boolean), `AttName` (for spec sheet), `AttSize` (for spec sheet), `AttContentType` (for spec sheet), `AttData` (for spec sheet), `Buyer` (FK to Buyer), `AHId` (FK to ACHead).
-   **`tblDG_Category_Master` (Category Master)**: Provides categories for items.
    -   Columns: `CId` (PK), `Symbol`, `CName`, `CompId`.
-   **`tblDG_UOM` (Unit of Measure Master)**: Provides units of measurement.
    -   Inferred Columns: `Id` (PK), `UOMName`. (Populated by `fun.drpunit`)
-   **`tblMM_Location` (Location Master)**: Stores item storage locations.
    -   Inferred Columns: `Id` (PK), `LocationName`. (Populated by `fun.drpLocat`)
-   **`tblDG_Item_Class` (Item Class Master)**: Defines item classifications.
    -   Columns: `Id` (PK), `Class`.
-   **`tblMM_Buyer_Master` (Buyer Master)**: Manages buyer information.
    -   Columns: `Id` (PK), `Category`, `Nos`, `EmpId` (FK to OfficeStaff).
-   **`tblHR_OfficeStaff` (Office Staff Master)**: Contains employee details, linked to buyers.
    -   Columns: `EmpId` (PK), `EmployeeName`.
-   **`tblAC_Head_Master` (A/C Head Master)**: Manages accounting heads.
    -   Inferred Columns: `Id` (PK), `HeadName`, `HeadType`. (Populated by `fun.AcHead`)
-   **`tblCompany_Settings` (Company Settings)**: Stores company-specific settings like item code length.
    -   Inferred Columns: `CompId` (PK), `ItemCodeLength`. (Used by `fun.ItemCodeLimit`)
-   **`tblFinancial_Year` (Financial Year)**: Stores financial year details.
    -   Inferred Columns: `FinYearId` (PK), `OpeningDate`. (Used by `fun.getOpeningDate`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic within the ASP.NET code.

-   **Create (Add Items Tab):**
    -   **Item Creation:** New items are added to `tblDG_Item_Master` via the `BtnSubmit_Click` event.
    -   **Part Number Assembly:** `TxtPartNo`, `TextBox1`, `TextBox2` are concatenated to form the `PartNo`.
    -   **Item Code Generation:** `ItemCode` is generated by combining a `Category` symbol and the `PartNo`.
    -   **Validation:** Extensive server-side validation using `RequiredFieldValidator`, `CompareValidator` (data type, integer), `RegularExpressionValidator` (numeric quantities), and custom checks for `DrpCategory`, `DrpUOMBasic`, `DrpLocation`, `TxtManfDesc`.
    -   **Duplicate Check:** Prevents adding items with an existing `ItemCode`.
    -   **File Uploads:** Handles image (`FileUpload1`) and specification sheet (`FileUpload2`) uploads, converting them to byte arrays for storage. Image resizing is performed.
    -   **Boolean Conversions:** `Absolute`, `Import/Local`, `Excise`, `UOMConv` are stored as integers (0/1) based on checkbox/radio button selections.
    -   **Dynamic Dropdown Population:** `DrpCategory`, `DrpUOMBasic`, `DrpLocation`, `DrpBuyer`, `DrpACHead` are dynamically populated on `Page_Load`. `DrpACHead` depends on selected radio button.
    -   **Session Data:** `CompId`, `FinYearId`, `SessionId` (username) are retrieved from session.
-   **Read (View Items Tab):**
    -   **Data Display:** `GridView2` displays a list of items with pagination.
    -   **Filtering/Searching:** Users can filter by `DrpType` (Category, WO Items), `DrpCategory1`, `DrpSearchCode` (Item Code, Description, Location), and `txtSearchItemCode`.
    -   **Complex Query Logic:** The `Fillgrid` method dynamically constructs SQL queries and executes a stored procedure (`GetAllItem`) based on multiple filter criteria.
    -   **Dynamic UI Control:** Visibility of search dropdowns and textboxes changes based on `DrpSearchCode` and `DrpType` selections.
-   **Update & Delete:**
    -   While not explicitly shown as separate forms/buttons on the ASPX, the `GridView2` implies selection (`onselectedindexchanged`) which usually leads to an edit form. For this migration, we'll assume standard CRUD operations are needed for consistency and full modernization.
-   **Auxiliary Functions (`clsFunctions`)**:
    -   `Connection()`: Database connection string.
    -   `AcHead()`: Populates A/C Head dropdown based on type.
    -   `select()`: Generic SQL SELECT statement execution.
    -   `drpunit()`, `drpLocat()`: Populates UOM and Location dropdowns.
    -   `getOpeningDate()`, `getCurrDate()`, `getCurrTime()`: Date and time utilities.
    -   `NumberValidationQty()`: Numeric input validation.
    -   `ItemCodeLimit()`: Retrieves item code length limit.
    -   `ToDate()`: Date formatting.
    -   `insert()`: Generic SQL INSERT statement execution.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

-   **Form Inputs (Add Items):**
    -   `DropDownList`s (`DrpCategory`, `DrpUOMBasic`, `DrpLocation`, `drpclass`, `DrpBuyer`, `DrpACHead`): Represent `ForeignKey` fields or choices.
    -   `TextBox`es (`TxtPartNo`, `TextBox1`, `TextBox2`, `TxtManfDesc`, `TxtStockQty`, `TxtMinorderQty`, `txtInspdays`, `TxtMinStockQty`, `TxtOpeningBalQty`, `TxtOpeningBalDate`, `txtleaddays`): Represent `CharField`, `TextField`, `DecimalField`, `IntegerField`, `DateField`.
    -   `RadioButtonList`s (`RadioButtonList1`, `RadioButtonList2`, `RadioButtonList3`): Represent boolean fields or choice fields.
    -   `CheckBox` (`CheckAbsolute`): Represents a boolean field.
    -   `FileUpload` (`FileUpload1`, `FileUpload2`): Represent `FileField` or `ImageField`.
    -   `Button` (`BtnSubmit`): Triggers form submission.
-   **Data Display (View Items):**
    -   `DropDownList`s (`DrpType`, `DrpCategory1`, `DrpSearchCode`, `DropDownList3`): Used for filtering the data grid.
    -   `TextBox` (`txtSearchItemCode`): Used for free-text search.
    -   `Button` (`BtnSearch`): Triggers grid refresh based on search criteria.
    -   `GridView` (`GridView2`): Replaced by DataTables.
-   **Structure:**
    -   `TabContainer` (`TabContainer1`): Will be replaced by HTMX-driven content swapping using `hx-get` on tab headers.
    -   `Panel`s (`Panel1`, `Panel2`): Used for layout and scrollbars, can be replaced by standard `div` elements with Tailwind CSS for styling and scrolling.

### Step 4: Generate Django Code

We will create a new Django app named `inventory_erp` to house the modernized Item Master module.

#### 4.1 Models (`inventory_erp/models.py`)

```python
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import base64
import re

# Helper class for common ERP functions (mirroring ASP.NET clsFunctions)
class ErpFunctions:
    @staticmethod
    def get_current_date():
        return timezone.now().strftime('%Y-%m-%d') # YYYY-MM-DD for database
    
    @staticmethod
    def get_current_time():
        return timezone.now().strftime('%H:%M:%S')

    @staticmethod
    def get_opening_date(company_id, fin_year_id):
        # This would typically query a FinancialYear or Company model
        # For now, return a placeholder or fixed date
        try:
            fin_year = FinancialYear.objects.get(comp_id=company_id, fin_year_id=fin_year_id)
            return fin_year.opening_date.strftime('%d-%m-%Y') # DD-MM-YYYY for display
        except FinancialYear.DoesNotExist:
            return timezone.now().strftime('%d-%m-%Y')

    @staticmethod
    def number_validation_qty(value):
        # Basic check for numeric, allows decimals up to 3 places
        if value is None or value == '':
            return True # Allow empty if field is not required
        try:
            # Check for float/decimal conversion
            float(value)
            # Check for regex pattern matching original ^\d{1,15}(\.\d{0,3})?$
            if not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(value)):
                return False
            return True
        except ValueError:
            return False

    @staticmethod
    def get_item_code_limit(company_id):
        # This would typically query a CompanySettings model
        try:
            company_setting = CompanySetting.objects.get(comp_id=company_id)
            return company_setting.item_code_length
        except CompanySetting.DoesNotExist:
            return 9 # Default value if not found (e.g., 3-3-3 for part no)

# --- Base Models for Lookups ---

class CompanySetting(models.Model):
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    item_code_length = models.IntegerField(db_column='ItemCodeLength', default=9)

    class Meta:
        managed = False
        db_table = 'tblCompany_Settings'
        verbose_name = 'Company Setting'
        verbose_name_plural = 'Company Settings'

    def __str__(self):
        return f"Company {self.comp_id} Settings"

class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    opening_date = models.DateField(db_column='OpeningDate')

    class Meta:
        managed = False
        db_table = 'tblFinancial_Year'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'
        unique_together = (('fin_year_id', 'comp_id'),)

    def __str__(self):
        return f"FY {self.fin_year_id} for Comp {self.comp_id}"

class Category(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10)
    cname = models.CharField(db_column='CName', max_length=100)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}"

class UOM(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    uom_name = models.CharField(db_column='UOMName', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_UOM'
        verbose_name = 'Unit of Measurement'
        verbose_name_plural = 'Units of Measurement'

    def __str__(self):
        return self.uom_name

class Location(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    location_name = models.CharField(db_column='LocationName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblMM_Location'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return self.location_name

class ItemClass(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_class = models.CharField(db_column='Class', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Class'
        verbose_name = 'Item Class'
        verbose_name_plural = 'Item Classes'

    def __str__(self):
        return self.item_class

class OfficeStaff(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=200)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.employee_name

class Buyer(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=50)
    nos = models.IntegerField(db_column='Nos')
    emp = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='buyers')

    class Meta:
        managed = False
        db_table = 'tblMM_Buyer_Master'
        verbose_name = 'Buyer'
        verbose_name_plural = 'Buyers'

    def __str__(self):
        return f"{self.category}{self.nos}-{self.emp.employee_name}[{self.emp.emp_id}]"

class ACHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    head_name = models.CharField(db_column='HeadName', max_length=200)
    head_type = models.CharField(db_column='HeadType', max_length=50) # e.g., 'Labour', 'With Material', 'Expenses', 'Service Provider'

    class Meta:
        managed = False
        db_table = 'tblAC_Head_Master' # Inferred table name
        verbose_name = 'A/C Head'
        verbose_name_plural = 'A/C Heads'

    def __str__(self):
        return self.head_name

# --- Main ItemMaster Model ---

class ItemMasterManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset()

    def filter_items(self, category_id=None, search_by=None, search_text=None, item_type=None, location_id=None, comp_id=None, fin_year_id=None):
        queryset = self.get_queryset()

        if comp_id is not None:
            queryset = queryset.filter(comp_id=comp_id)
        if fin_year_id is not None:
            queryset = queryset.filter(fin_year_id__lte=fin_year_id) # Using lte as per ASP.NET logic

        if item_type == "Category" and category_id and category_id != 'Select':
            queryset = queryset.filter(category__cid=category_id)
            if search_by == "tblDG_Item_Master.ItemCode" and search_text:
                queryset = queryset.filter(item_code__istartswith=search_text)
            elif search_by == "tblDG_Item_Master.ManfDesc" and search_text:
                queryset = queryset.filter(manf_desc__icontains=search_text)
            elif search_by == "tblDG_Item_Master.Location" and location_id and location_id != 'Select':
                queryset = queryset.filter(location__id=location_id)
            elif not search_by and search_text: # Default search by ManfDesc if no search_by selected
                 queryset = queryset.filter(manf_desc__icontains=search_text)
        elif item_type == "WOItems":
            if search_by == "tblDG_Item_Master.ItemCode" and search_text:
                queryset = queryset.filter(item_code__icontains=search_text) # ASP.NET used like '%s%', so icontains
            elif search_by == "tblDG_Item_Master.ManfDesc" and search_text:
                queryset = queryset.filter(manf_desc__icontains=search_text)
            elif not search_by and search_text: # Default search by ManfDesc if no search_by selected
                 queryset = queryset.filter(manf_desc__icontains=search_text)
        elif item_type == "Select" or item_type is None: # Initial load or no selection
            # No specific filtering applied if type is 'Select' or None
            pass
            
        return queryset.order_by('id') # Ensure consistent ordering for pagination


class ItemMaster(models.Model):
    # Core fields
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=ErpFunctions.get_current_date)
    sys_time = models.TimeField(db_column='SysTime', default=ErpFunctions.get_current_time)
    session_id = models.CharField(db_column='SessionId', max_length=100) # User who created/modified
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    # Item details
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', related_name='items')
    # PartNo is stored as one field in DB but input as three in ASP.NET
    # For migration simplicity, let's keep it as one charfield in DB.
    # Front-end forms will handle input as 3 parts.
    part_no = models.CharField(db_column='PartNo', max_length=20) # e.g., 'xxxx-xxx-xxx'
    manf_desc = models.TextField(db_column='ManfDesc')
    uom_basic = models.ForeignKey(UOM, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')
    min_order_qty = models.DecimalField(db_column='MinOrderQty', max_digits=18, decimal_places=3, default=1.000)
    min_stock_qty = models.DecimalField(db_column='MinStockQty', max_digits=18, decimal_places=3, default=1.000)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, default=0.000)
    location = models.ForeignKey(Location, on_delete=models.DO_NOTHING, db_column='Location', related_name='items')
    item_code = models.CharField(db_column='ItemCode', max_length=50, unique=True)
    item_class = models.ForeignKey(ItemClass, on_delete=models.DO_NOTHING, db_column='Class', related_name='items', default=1)
    lead_days = models.DecimalField(db_column='LeadDays', max_digits=18, decimal_places=3, default=0.000)
    inspection_days = models.DecimalField(db_column='InspectionDays', max_digits=18, decimal_places=3, default=0.000)
    buyer = models.ForeignKey(Buyer, on_delete=models.DO_NOTHING, db_column='Buyer', related_name='items')
    ac_head = models.ForeignKey(ACHead, on_delete=models.DO_NOTHING, db_column='AHId', related_name='items')

    # Flags / Booleans
    absolute = models.BooleanField(db_column='Absolute', default=False)
    excise = models.BooleanField(db_column='Excise', default=False)
    import_local = models.BooleanField(db_column='ImportLocal', default=False)
    uom_con_fact = models.BooleanField(db_column='UOMConFact', default=False) # UOM Conversion Factor

    # Opening Balance
    opening_bal_date = models.DateField(db_column='OpeningBalDate', null=True, blank=True)
    opening_bal_qty = models.DecimalField(db_column='OpeningBalQty', max_digits=18, decimal_places=3, default=0.000)

    # File uploads (storing as BinaryField as per ASP.NET code, but FileField is generally preferred in Django)
    file_data = models.BinaryField(db_column='FileData', null=True, blank=True) # Image
    file_name = models.CharField(db_column='FileName', max_length=255, null=True, blank=True)
    file_size = models.IntegerField(db_column='FileSize', null=True, blank=True)
    content_type = models.CharField(db_column='ContentType', max_length=100, null=True, blank=True)

    att_data = models.BinaryField(db_column='AttData', null=True, blank=True) # Spec Sheet
    att_name = models.CharField(db_column='AttName', max_length=255, null=True, blank=True)
    att_size = models.IntegerField(db_column='AttSize', null=True, blank=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, null=True, blank=True)
    
    objects = ItemMasterManager() # Custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    # Business Logic / Model Methods
    def generate_item_code(self, part_no_segment1, part_no_segment2, part_no_segment3):
        # Retrieve category symbol
        category_symbol = self.category.symbol
        # Assemble part number
        part_number = f"{part_no_segment1}-{part_no_segment2}-{part_no_segment3}"
        # Combine category symbol and part number
        item_code = f"{category_symbol}{part_number}"
        
        # Check item code length limit from Company settings
        item_code_limit = ErpFunctions.get_item_code_limit(self.comp_id)
        if len(item_code) != item_code_limit:
            raise ValidationError(_(f"Invalid Item Code length. Expected {item_code_limit} characters."))
            
        return item_code

    def save(self, *args, **kwargs):
        # Override save to auto-generate item_code and handle timestamps if not already set
        if not self.pk: # Only for new objects
            self.sys_date = ErpFunctions.get_current_date()
            self.sys_time = ErpFunctions.get_current_time()
            # session_id, comp_id, fin_year_id should be set by the form/view
            
        super().save(*args, **kwargs)

    @property
    def part_no_segments(self):
        # Returns part_no as a tuple of (segment1, segment2, segment3)
        if self.part_no:
            parts = self.part_no.split('-')
            if len(parts) == 3:
                return tuple(parts)
        return ('', '', '')

    @property
    def image_url(self):
        if self.file_data and self.content_type:
            return f"data:{self.content_type};base64,{base64.b64encode(self.file_data).decode('utf-8')}"
        return None

    @property
    def spec_sheet_url(self):
        if self.att_data and self.att_content_type and self.att_name:
            # For direct display/download, can return a data URL or a link to a view that serves it
            return f"data:{self.att_content_type};base64,{base64.b64encode(self.att_data).decode('utf-8')}"
        return None

```

#### 4.2 Forms (`inventory_erp/forms.py`)

```python
from django import forms
from .models import ItemMaster, Category, UOM, Location, ItemClass, Buyer, ACHead, ErpFunctions
import re

class ItemMasterForm(forms.ModelForm):
    # Separate fields for the three parts of the Part Number
    part_no_segment1 = forms.CharField(
        max_length=3,
        widget=forms.TextInput(attrs={'class': 'box3 w-20', 'placeholder': 'XXX', 'x-mask': '999'}),
        label="Part No Segment 1"
    )
    part_no_segment2 = forms.CharField(
        max_length=3,
        widget=forms.TextInput(attrs={'class': 'box3 w-20', 'placeholder': 'XXX', 'x-mask': '999'}),
        label="Part No Segment 2"
    )
    part_no_segment3 = forms.CharField(
        max_length=3,
        widget=forms.TextInput(attrs={'class': 'box3 w-20', 'placeholder': 'XXX', 'x-mask': '999'}),
        label="Part No Segment 3"
    )

    # File fields for image and spec sheet
    uploaded_image = forms.FileField(required=False, label="Drw/Image")
    uploaded_spec_sheet = forms.FileField(required=False, label="Spec. Sheet")

    # Radio button replacements
    uom_conversion = forms.TypedChoiceField(
        choices=[(True, 'Yes'), (False, 'No')],
        coerce=lambda x: x == 'True', # Converts 'True'/'False' strings from request to boolean
        widget=forms.RadioSelect(attrs={'class': 'inline-block mr-4'}),
        initial=False,
        label="UOM Conv."
    )
    import_local_choice = forms.TypedChoiceField(
        choices=[(True, 'Yes'), (False, 'No')],
        coerce=lambda x: x == 'True',
        widget=forms.RadioSelect(attrs={'class': 'inline-block mr-4'}),
        initial=False,
        label="Import/Local"
    )
    excise_applicable = forms.TypedChoiceField(
        choices=[(True, 'Yes'), (False, 'No')],
        coerce=lambda x: x == 'True',
        widget=forms.RadioSelect(attrs={'class': 'inline-block mr-4'}),
        initial=False,
        label="Excise Applicable"
    )

    # A/C Head Radio Buttons (for dynamic filtering in client-side or separate AJAX endpoint)
    # For simplicity, we'll keep it as a choice field here and filter ACHead dropdown.
    AC_HEAD_TYPES = [
        ('Labour', 'Labour'),
        ('With Material', 'With Material'),
        ('Expenses', 'Expenses'),
        ('Service Provider', 'Service Provider'),
    ]
    ac_head_type = forms.ChoiceField(
        choices=AC_HEAD_TYPES,
        widget=forms.RadioSelect(attrs={'class': 'inline-block mr-4', 'x-model': 'selectedACHeadType'}),
        initial='With Material',
        label="A/C Head Type"
    )


    class Meta:
        model = ItemMaster
        # Exclude generated fields or those handled separately in form
        exclude = [
            'id', 'sys_date', 'sys_time', 'session_id', 'comp_id', 'fin_year_id',
            'part_no', # Handled by part_no_segment fields
            'item_code', # Generated by model method
            'absolute', 'excise', 'import_local', 'uom_con_fact', # Handled by boolean fields
            'file_data', 'file_name', 'file_size', 'content_type', # Handled by uploaded_image
            'att_data', 'att_name', 'att_size', 'att_content_type', # Handled by uploaded_spec_sheet
        ]
        # Map original ASP.NET fields to Django fields and apply Tailwind CSS classes
        widgets = {
            'manf_desc': forms.Textarea(attrs={'class': 'box3 w-full', 'rows': 6}),
            'min_order_qty': forms.TextInput(attrs={'class': 'box3', 'x-mask:decimal': '', 'x-init': "$el.value = $el.value || '1.000'"}),
            'min_stock_qty': forms.TextInput(attrs={'class': 'box3', 'x-mask:decimal': '', 'x-init': "$el.value = $el.value || '1.000'"}),
            'stock_qty': forms.TextInput(attrs={'class': 'box3', 'x-mask:decimal': '', 'readonly': True, 'x-init': "$el.value = $el.value || '0.000'"}),
            'opening_bal_qty': forms.TextInput(attrs={'class': 'box3', 'x-mask:decimal': '', 'readonly': True, 'x-init': "$el.value = $el.value || '0.000'"}),
            'opening_bal_date': forms.TextInput(attrs={'class': 'box3', 'readonly': True, 'placeholder': 'DD-MM-YYYY'}),
            'lead_days': forms.TextInput(attrs={'class': 'box3', 'x-mask:decimal': '', 'x-init': "$el.value = $el.value || '0.000'"}),
            'inspection_days': forms.TextInput(attrs={'class': 'box3', 'x-mask:decimal': '', 'x-init': "$el.value = $el.value || '0.000'"}),
            # Select boxes
            'category': forms.Select(attrs={'class': 'box3 w-full'}),
            'uom_basic': forms.Select(attrs={'class': 'box3 w-full'}),
            'location': forms.Select(attrs={'class': 'box3 w-full'}),
            'item_class': forms.Select(attrs={'class': 'box3 w-full'}),
            'buyer': forms.Select(attrs={'class': 'box3 w-full'}),
            'ac_head': forms.Select(attrs={'class': 'box3 w-full'}),
        }
        labels = {
            'category': 'Category',
            'manf_desc': 'Description',
            'uom_basic': 'UOM',
            'min_order_qty': 'Min Order Qty',
            'min_stock_qty': 'Min Stock Qty',
            'stock_qty': 'Stock Qty',
            'location': 'Store Location',
            'opening_bal_qty': 'Opening Bal Qty',
            'opening_bal_date': 'Opening Bal Date',
            'lead_days': 'Lead Days',
            'inspection_days': 'Inspection Days',
            'item_class': 'Class',
            'buyer': 'Buyer',
            'ac_head': 'A/C Head',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dropdowns
        self.fields['category'].queryset = Category.objects.filter(comp_id=1) # Assume CompId=1 for now
        self.fields['uom_basic'].queryset = UOM.objects.all()
        self.fields['location'].queryset = Location.objects.all()
        self.fields['item_class'].queryset = ItemClass.objects.filter(id__gt=0) # Filter where Id != 0
        # Buyer dropdown uses complex join in ASP.NET, simplifying here
        self.fields['buyer'].queryset = Buyer.objects.select_related('emp').all().order_by('id')
        self.fields['ac_head'].queryset = ACHead.objects.all() # Initially load all, client-side can filter via Alpine/HTMX

        # Set initial values for part number segments
        if self.instance.pk:
            segment1, segment2, segment3 = self.instance.part_no_segments
            self.initial['part_no_segment1'] = segment1
            self.initial['part_no_segment2'] = segment2
            self.initial['part_no_segment3'] = segment3
            self.initial['uom_conversion'] = self.instance.uom_con_fact
            self.initial['import_local_choice'] = self.instance.import_local
            self.initial['excise_applicable'] = self.instance.excise
            # Initialize AC Head type if it exists in instance or from selected A/C Head
            # This would require a mapping from ACHead to AC_HEAD_TYPES, which is not in model.
            # Simplified: default to With Material.

        # Set default opening balance date from ERP function
        self.fields['opening_bal_date'].initial = ErpFunctions.get_opening_date(
            company_id=1, fin_year_id=1 # Assuming CompId=1, FinYearId=1
        )
            
        # Apply generic Tailwind styling to all form fields
        for name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Textarea, forms.Select)):
                if 'class' in field.widget.attrs:
                    field.widget.attrs['class'] += ' block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
                else:
                    field.widget.attrs['class'] = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            elif isinstance(field.widget, forms.CheckboxInput):
                field.widget.attrs['class'] = 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
            elif isinstance(field.widget, forms.RadioSelect):
                field.widget.attrs['class'] = 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'


    def clean(self):
        cleaned_data = super().clean()

        # Part Number Validation and Assembly
        part1 = cleaned_data.get('part_no_segment1')
        part2 = cleaned_data.get('part_no_segment2')
        part3 = cleaned_data.get('part_no_segment3')

        if not all([part1, part2, part3]):
            raise forms.ValidationError("All parts of the Part No are required.")
        if not (len(part1) == 3 and len(part2) == 3 and len(part3) == 3):
             raise forms.ValidationError("Each part of the Part No must be 3 digits.")
        
        # Original ASP.NET used CompareValidator Type="Integer"
        if not (part1.isdigit() and part2.isdigit() and part3.isdigit()):
             raise forms.ValidationError("Part No segments must be numeric.")
        
        cleaned_data['part_no'] = f"{part1}-{part2}-{part3}"
        
        # Manual validation for quantities (from ASP.NET RegExpValidator)
        # Assuming ErpFunctions.number_validation_qty handles decimal points and length
        for field_name in ['min_order_qty', 'min_stock_qty', 'stock_qty', 'opening_bal_qty', 'lead_days', 'inspection_days']:
            value = cleaned_data.get(field_name)
            if value is not None and not ErpFunctions.number_validation_qty(value):
                self.add_error(field_name, "Invalid numeric format. Max 15 digits before, 3 after decimal.")

        # ManfDesc required
        if not cleaned_data.get('manf_desc'):
            self.add_error('manf_desc', "Description is required.")

        return cleaned_data

    def save(self, commit=True, user=None):
        instance = super().save(commit=False)

        # Set part_no from segments
        instance.part_no = self.cleaned_data['part_no']

        # Handle boolean fields
        instance.absolute = self.cleaned_data.get('absolute', False)
        instance.uom_con_fact = self.cleaned_data.get('uom_conversion', False)
        instance.import_local = self.cleaned_data.get('import_local_choice', False)
        instance.excise = self.cleaned_data.get('excise_applicable', False)

        # Handle file uploads
        if self.cleaned_data['uploaded_image']:
            image_file = self.cleaned_data['uploaded_image']
            instance.file_data = image_file.read()
            instance.file_name = image_file.name
            instance.file_size = image_file.size
            instance.content_type = image_file.content_type
        
        if self.cleaned_data['uploaded_spec_sheet']:
            spec_file = self.cleaned_data['uploaded_spec_sheet']
            instance.att_data = spec_file.read()
            instance.att_name = spec_file.name
            instance.att_size = spec_file.size
            instance.att_content_type = spec_file.content_type

        # Set session-related data (placeholder values, in real app these come from request/session)
        instance.session_id = user.username if user else 'SYSTEM'
        instance.comp_id = 1 # Example: From request.session['compid']
        instance.fin_year_id = 1 # Example: From request.session['finyear']

        # Generate item_code (this logic is in the model method)
        try:
            instance.item_code = instance.generate_item_code(
                self.cleaned_data['part_no_segment1'],
                self.cleaned_data['part_no_segment2'],
                self.cleaned_data['part_no_segment3']
            )
        except ValidationError as e:
            # Re-raise as form validation error
            raise forms.ValidationError({'part_no_segment1': e.message}) # Attach error to a relevant field

        # Check for duplicate item_code
        if ItemMaster.objects.filter(item_code=instance.item_code).exclude(pk=instance.pk).exists():
            raise forms.ValidationError({'item_code': 'Item with this code already exists.'})

        if commit:
            instance.save()
        return instance

```

#### 4.3 Views (`inventory_erp/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q # For complex queries
from .models import ItemMaster, Category, Location, ACHead
from .forms import ItemMasterForm

# Helper for initial form data for default values like opening date
def get_initial_form_data():
    return {
        'opening_bal_date': ItemMaster.objects.model.ErpFunctions.get_opening_date(1, 1) # Assuming CompId=1, FinYearId=1
    }

class ItemMasterListView(ListView):
    model = ItemMaster
    template_name = 'inventory_erp/itemmaster/list.html'
    context_object_name = 'itemmasters'
    paginate_by = 20 # ASP.NET GridView PageSize=20

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['categories'] = Category.objects.filter(comp_id=1).order_by('symbol')
        context['locations'] = Location.objects.all().order_by('location_name')
        context['search_options'] = [
            {'value': 'tblDG_Item_Master.ItemCode', 'label': 'Item Code'},
            {'value': 'tblDG_Item_Master.ManfDesc', 'label': 'Description'},
            {'value': 'tblDG_Item_Master.Location', 'label': 'Location'},
        ]
        context['initial_form_data'] = get_initial_form_data() # For the add form in modal
        return context

class ItemMasterTablePartialView(ListView):
    model = ItemMaster
    template_name = 'inventory_erp/itemmaster/_item_master_table.html'
    context_object_name = 'itemmasters'
    paginate_by = 20 # Matches GridView PageSize

    def get_queryset(self):
        # Replicate Fillgrid logic
        item_type = self.request.GET.get('type')
        category_id = self.request.GET.get('category')
        search_by = self.request.GET.get('search_by_code')
        search_text = self.request.GET.get('search_text')
        location_id = self.request.GET.get('location') # This corresponds to DropDownList3

        # Assuming comp_id and fin_year_id from user session or defaults
        comp_id = 1 # request.session.get('compid', 1)
        fin_year_id = 1 # request.session.get('finyear', 1)

        queryset = ItemMaster.objects.filter_items(
            item_type=item_type,
            category_id=category_id,
            search_by=search_by,
            search_text=search_text,
            location_id=location_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        return queryset

    # This method is for HTMX only, it doesn't return full HTML page
    def render_to_response(self, context, **response_kwargs):
        return super().render_to_response(context, **response_kwargs)

class ItemMasterCreateView(CreateView):
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'inventory_erp/itemmaster/_item_master_form.html'
    success_url = reverse_lazy('itemmaster_list')

    def get_initial(self):
        initial = super().get_initial()
        initial.update(get_initial_form_data()) # Pre-populate opening date
        return initial

    def form_valid(self, form):
        # Pass current user to form.save() for session_id (assuming session_id is username)
        response = form.save(user=self.request.user) # Assuming user is authenticated

        messages.success(self.request, 'Item Master added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList' # Trigger HTMX event to refresh table
                }
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        # Render the form again with errors for HTMX response
        return render(self.request, self.template_name, {'form': form})


class ItemMasterUpdateView(UpdateView):
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'inventory_erp/itemmaster/_item_master_form.html'
    success_url = reverse_lazy('itemmaster_list')

    def get_initial(self):
        initial = super().get_initial()
        # Initialize form fields that are not directly from model
        initial['absolute'] = self.object.absolute
        initial['uom_conversion'] = self.object.uom_con_fact
        initial['import_local_choice'] = self.object.import_local
        initial['excise_applicable'] = self.object.excise
        # Initial part number segments will be handled by form's __init__
        return initial

    def form_valid(self, form):
        response = form.save(user=self.request.user) # Assuming user is authenticated
        messages.success(self.request, 'Item Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        return render(self.request, self.template_name, {'form': form})

class ItemMasterDeleteView(DeleteView):
    model = ItemMaster
    template_name = 'inventory_erp/itemmaster/confirm_delete.html'
    success_url = reverse_lazy('itemmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

class ACHeadFilterView(View):
    # This view can be used to dynamically load AC Heads based on selected type
    def get(self, request, *args, **kwargs):
        ac_head_type = request.GET.get('type')
        if ac_head_type:
            ac_heads = ACHead.objects.filter(head_type=ac_head_type).order_by('head_name')
        else:
            ac_heads = ACHead.objects.all().order_by('head_name')
        
        options = [{'id': head.id, 'name': head.head_name} for head in ac_heads]
        return JsonResponse({'options': options})

```

#### 4.4 Templates (`inventory_erp/templates/inventory_erp/itemmaster/`)

`list.html`
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Item Master</h2>
        <div class="bg-gray-100 rounded-md p-1 flex space-x-1" x-data="{ activeTab: 'add' }">
            <button
                class="py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200"
                :class="activeTab === 'add' ? 'bg-blue-600 text-white shadow' : 'text-gray-700 hover:bg-gray-200'"
                @click="activeTab = 'add'"
                hx-get="{% url 'itemmaster_add' %}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click"
                hx-indicator="#tab-spinner">
                Add Items
            </button>
            <button
                class="py-2 px-4 rounded-md text-sm font-medium transition-colors duration-200"
                :class="activeTab === 'view' ? 'bg-blue-600 text-white shadow' : 'text-gray-700 hover:bg-gray-200'"
                @click="activeTab = 'view'"
                hx-get="{% url 'itemmaster_table' %}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click"
                hx-indicator="#tab-spinner">
                View Items
            </button>
        </div>
    </div>

    <div id="tab-spinner" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading tab content...</p>
    </div>

    <div id="tab-content" class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial content will be loaded here, e.g., the Add form or View table -->
        <!-- Start with Add Items tab content initially -->
        {% include 'inventory_erp/itemmaster/_item_master_form.html' with form=itemmaster_form initial_form_data=initial_form_data %}
    </div>

    <!-- Modal for forms (edit/delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on htmx:afterSwap remove .is-active from me when event.detail.xhr.status == 204">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-4xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initial load for the 'Add Items' tab
        document.getElementById('tab-content').innerHTML = `
            {% include 'inventory_erp/itemmaster/_item_master_form.html' with form=itemmaster_form initial_form_data=initial_form_data %}
        `;
    });

    // Handle form submission success for modals
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204 && event.detail.requestHeaders['HX-Target'] === 'modalContent') {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
                modal.classList.add('hidden'); // Ensure it's hidden
            }
        }
    });

    // Alpine.js decimal mask directive
    document.addEventListener('alpine:init', () => {
        Alpine.directive('mask-decimal', (el) => {
            const formatDecimal = (value) => {
                const parts = value.split('.');
                parts[0] = parts[0].replace(/\D/g, '').slice(0, 15);
                if (parts.length > 1) {
                    parts[1] = parts[1].replace(/\D/g, '').slice(0, 3);
                }
                return parts.join('.');
            };

            el.addEventListener('input', (e) => {
                e.target.value = formatDecimal(e.target.value);
            });
            el.addEventListener('blur', (e) => {
                let value = e.target.value;
                if (value.endsWith('.')) {
                    value = value + '000';
                } else if (value.includes('.') && value.split('.')[1].length < 3) {
                    value = value + '0'.repeat(3 - value.split('.')[1].length);
                } else if (!value.includes('.') && value !== '') {
                    value = value + '.000';
                }
                e.target.value = value;
            });
        });
    });
</script>
{% endblock %}
```

`_item_master_table.html` (Partial for DataTables)
```html
<div class="overflow-x-auto">
    <div class="mb-4 flex space-x-2 flex-wrap items-center" x-data="{
        itemType: '{{ request.GET.type|default:'Select' }}',
        category: '{{ request.GET.category|default:'Select' }}',
        searchByCode: '{{ request.GET.search_by_code|default:'Select' }}',
        searchText: '{{ request.GET.search_text|default:'' }}',
        location: '{{ request.GET.location|default:'Select' }}',
        showCategory: '{{ request.GET.type|default:'Select' }}' === 'Category',
        showLocationDropdown: '{{ request.GET.search_by_code|default:'Select' }}' === 'tblDG_Item_Master.Location',
        showSearchText: !('{{ request.GET.search_by_code|default:'Select' }}' === 'tblDG_Item_Master.Location' && '{{ request.GET.type|default:'Select' }}' === 'Category'),
        init() {
            // Initial visibility setup
            this.updateVisibility();
        },
        updateVisibility() {
            this.showCategory = this.itemType === 'Category';
            this.showLocationDropdown = this.itemType === 'Category' && this.searchByCode === 'tblDG_Item_Master.Location';
            this.showSearchText = this.searchByCode !== 'tblDG_Item_Master.Location' || this.itemType !== 'Category';
        }
    }">
        <select id="DrpType" name="type" class="box3 h-9 px-3 py-1 border border-gray-300 rounded-md shadow-sm"
                x-model="itemType" @change="updateVisibility()"
                hx-get="{% url 'itemmaster_table' %}" hx-target="#itemmasterTable-container" hx-swap="innerHTML" hx-trigger="change delay:500ms"
                hx-vals="js:{type: $event.target.value, category: category, search_by_code: searchByCode, search_text: searchText, location: location}"
                hx-indicator="#tab-spinner">
            <option value="Select">Select</option>
            <option value="Category" {% if request.GET.type == 'Category' %}selected{% endif %}>Category</option>
            <option value="WOItems" {% if request.GET.type == 'WOItems' %}selected{% endif %}>WO Items</option>
        </select>

        <select id="DrpCategory1" name="category" class="box3 h-9 px-3 py-1 border border-gray-300 rounded-md shadow-sm w-48"
                x-model="category"
                hx-get="{% url 'itemmaster_table' %}" hx-target="#itemmasterTable-container" hx-swap="innerHTML" hx-trigger="change delay:500ms"
                hx-vals="js:{type: itemType, category: $event.target.value, search_by_code: searchByCode, search_text: searchText, location: location}"
                hx-indicator="#tab-spinner"
                x-show="showCategory">
            <option value="Select">Select</option>
            {% for cat in categories %}
            <option value="{{ cat.cid }}" {% if request.GET.category|stringformat:"s" == cat.cid|stringformat:"s" %}selected{% endif %}>{{ cat }}</option>
            {% endfor %}
        </select>

        <select id="DrpSearchCode" name="search_by_code" class="box3 h-9 px-3 py-1 border border-gray-300 rounded-md shadow-sm w-48"
                x-model="searchByCode" @change="updateVisibility()"
                hx-get="{% url 'itemmaster_table' %}" hx-target="#itemmasterTable-container" hx-swap="innerHTML" hx-trigger="change delay:500ms"
                hx-vals="js:{type: itemType, category: category, search_by_code: $event.target.value, search_text: searchText, location: location}"
                hx-indicator="#tab-spinner"
                x-show="itemType !== 'Select'">
            <option value="Select">Select</option>
            {% for opt in search_options %}
            <option value="{{ opt.value }}" {% if request.GET.search_by_code == opt.value %}selected{% endif %}>{{ opt.label }}</option>
            {% endfor %}
        </select>

        <select id="DropDownList3" name="location" class="box3 h-9 px-3 py-1 border border-gray-300 rounded-md shadow-sm w-36"
                x-model="location"
                hx-get="{% url 'itemmaster_table' %}" hx-target="#itemmasterTable-container" hx-swap="innerHTML" hx-trigger="change delay:500ms"
                hx-vals="js:{type: itemType, category: category, search_by_code: searchByCode, search_text: searchText, location: $event.target.value}"
                hx-indicator="#tab-spinner"
                x-show="showLocationDropdown">
            <option value="Select">Select</option>
            {% for loc in locations %}
            <option value="{{ loc.id }}" {% if request.GET.location|stringformat:"s" == loc.id|stringformat:"s" %}selected{% endif %}>{{ loc }}</option>
            {% endfor %}
        </select>

        <input type="text" id="txtSearchItemCode" name="search_text" placeholder="Search..."
               class="box3 h-9 px-3 py-1 border border-gray-300 rounded-md shadow-sm w-48"
               x-model="searchText"
               hx-get="{% url 'itemmaster_table' %}" hx-target="#itemmasterTable-container" hx-swap="innerHTML" hx-trigger="keyup changed delay:500ms"
               hx-vals="js:{type: itemType, category: category, search_by_code: searchByCode, search_text: $event.target.value, location: location}"
               hx-indicator="#tab-spinner"
               x-show="showSearchText">
        
        <button id="BtnSearch" type="button"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors duration-200"
                hx-get="{% url 'itemmaster_table' %}" hx-target="#itemmasterTable-container" hx-swap="innerHTML" hx-trigger="click"
                hx-vals="js:{type: itemType, category: category, search_by_code: searchByCode, search_text: searchText, location: location}"
                hx-indicator="#tab-spinner">
            Search
        </button>
    </div>

    <table id="itemmasterTable" class="min-w-full bg-white table-auto border-collapse">
        <thead>
            <tr class="bg-gray-100 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th class="py-2 px-4 border-b">SN</th>
                <th class="py-2 px-4 border-b">Category</th>
                <th class="py-2 px-4 border-b">PartNo</th>
                <th class="py-2 px-4 border-b">Item Code</th>
                <th class="py-2 px-4 border-b">Description</th>
                <th class="py-2 px-4 border-b">UOM</th>
                <th class="py-2 px-4 border-b text-right">Min Order Qty</th>
                <th class="py-2 px-4 border-b text-right">Min Stock Qty</th>
                <th class="py-2 px-4 border-b text-right">Stock Qty</th>
                <th class="py-2 px-4 border-b text-center">Location</th>
                <th class="py-2 px-4 border-b text-center">Absolute</th>
                <th class="py-2 px-4 border-b text-center">Excise</th>
                <th class="py-2 px-4 border-b text-center">Import/Local</th>
                <th class="py-2 px-4 border-b text-center">Open Bal Date</th>
                <th class="py-2 px-4 border-b text-right">Opening Bal Qty</th>
                <th class="py-2 px-4 border-b">A/C Head</th>
                <th class="py-2 px-4 border-b">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in itemmasters %}
            <tr class="hover:bg-gray-50 border-b border-gray-200">
                <td class="py-2 px-4 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 text-center">{{ obj.category.symbol }}</td>
                <td class="py-2 px-4 text-center">{{ obj.part_no }}</td>
                <td class="py-2 px-4 text-center">{{ obj.item_code }}</td>
                <td class="py-2 px-4">{{ obj.manf_desc }}</td>
                <td class="py-2 px-4 text-center">{{ obj.uom_basic.uom_name }}</td>
                <td class="py-2 px-4 text-right">{{ obj.min_order_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 text-right">{{ obj.min_stock_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 text-right">{{ obj.stock_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4 text-center">{{ obj.location.location_name }}</td>
                <td class="py-2 px-4 text-center">{{ obj.absolute|yesno:"Yes,No" }}</td>
                <td class="py-2 px-4 text-center">{{ obj.excise|yesno:"Yes,No" }}</td>
                <td class="py-2 px-4 text-center">{{ obj.import_local|yesno:"Yes,No" }}</td>
                <td class="py-2 px-4 text-center">{{ obj.opening_bal_date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 text-right">{{ obj.opening_bal_qty|floatformat:"3" }}</td>
                <td class="py-2 px-4">{{ obj.ac_head.head_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2 text-xs"
                        hx-get="{% url 'itemmaster_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'itemmaster_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="17" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    if ($.fn.DataTable.isDataTable('#itemmasterTable')) {
        $('#itemmasterTable').DataTable().destroy();
    }
    $('#itemmasterTable').DataTable({
        "paging": true,
        "ordering": true,
        "info": true,
        "searching": false, // Handled by Django backend filtering
        "pageLength": 20,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 16] } // Disable sorting for SN and Actions
        ],
        "responsive": true
    });
</script>
```

`_item_master_form.html` (Partial for Create/Update Form)
```html
<div class="p-6" x-data="{ selectedACHeadType: '{{ form.ac_head_type.value|default:'With Material' }}', acHeads: [], loadingACHeads: false, initialACHeadId: '{{ form.ac_head.value|default:'' }}' }"
     x-init="
        const fetchACHeads = async (type) => {
            loadingACHeads = true;
            try {
                const response = await fetch(`/inventory_erp/ac-heads/?type=${type}`);
                const data = await response.json();
                acHeads = data.options;
                // Pre-select if initial value matches
                if (initialACHeadId && acHeads.some(head => head.id == initialACHeadId)) {
                    document.getElementById('id_ac_head').value = initialACHeadId;
                } else if (acHeads.length > 0) {
                    // If no initial id or it doesn't match, select first available
                    document.getElementById('id_ac_head').value = acHeads[0].id;
                }
            } catch (error) {
                console.error('Error fetching A/C Heads:', error);
            } finally {
                loadingACHeads = false;
            }
        };
        $watch('selectedACHeadType', type => fetchACHeads(type));
        fetchACHeads(selectedACHeadType);
     ">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Item Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <div>
                <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category<span class="text-red-500">*</span></label>
                {{ form.category }}
                {% if form.category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.category.errors }}</p>{% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Part No<span class="text-red-500">*</span> <span class="text-gray-500 text-xs">(Ex.: XXX-XXX-XXX)</span></label>
                <div class="flex items-center space-x-2">
                    {{ form.part_no_segment1 }} - {{ form.part_no_segment2 }} - {{ form.part_no_segment3 }}
                </div>
                {% if form.part_no_segment1.errors or form.part_no_segment2.errors or form.part_no_segment3.errors %}<p class="text-red-500 text-xs mt-1">{{ form.part_no_segment1.errors|default_if_none:form.part_no_segment2.errors|default_if_none:form.part_no_segment3.errors }}</p>{% endif %}
            </div>
            <div class="col-span-1 md:col-span-2">
                <label for="{{ form.manf_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">Description<span class="text-red-500">*</span></label>
                {{ form.manf_desc }}
                {% if form.manf_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.manf_desc.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.uom_basic.id_for_label }}" class="block text-sm font-medium text-gray-700">UOM<span class="text-red-500">*</span></label>
                {{ form.uom_basic }}
                {% if form.uom_basic.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uom_basic.errors }}</p>{% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">UOM Conv.</label>
                <div class="mt-1 space-x-4">
                    {% for radio in form.uom_conversion %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
                {% if form.uom_conversion.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uom_conversion.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.stock_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">Stock Qty</label>
                {{ form.stock_qty }}
                {% if form.stock_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.stock_qty.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.item_class.id_for_label }}" class="block text-sm font-medium text-gray-700">Class</label>
                {{ form.item_class }}
                {% if form.item_class.errors %}<p class="text-red-500 text-xs mt-1">{{ form.item_class.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.min_order_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">Min Order Qty</label>
                {{ form.min_order_qty }}
                {% if form.min_order_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.min_order_qty.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.inspection_days.id_for_label }}" class="block text-sm font-medium text-gray-700">Inspection Days</label>
                {{ form.inspection_days }}
                {% if form.inspection_days.errors %}<p class="text-red-500 text-xs mt-1">{{ form.inspection_days.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.min_stock_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">Min Stock Qty</label>
                {{ form.min_stock_qty }}
                {% if form.min_stock_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.min_stock_qty.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">Store Location<span class="text-red-500">*</span></label>
                {{ form.location }}
                {% if form.location.errors %}<p class="text-red-500 text-xs mt-1">{{ form.location.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.opening_bal_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">Opening Bal Qty</label>
                {{ form.opening_bal_qty }}
                {% if form.opening_bal_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.opening_bal_qty.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.opening_bal_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Opening Bal Date</label>
                {{ form.opening_bal_date }}
                {% if form.opening_bal_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.opening_bal_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.lead_days.id_for_label }}" class="block text-sm font-medium text-gray-700">Lead Days</label>
                {{ form.lead_days }}
                {% if form.lead_days.errors %}<p class="text-red-500 text-xs mt-1">{{ form.lead_days.errors }}</p>{% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Import/Local</label>
                <div class="mt-1 space-x-4">
                    {% for radio in form.import_local_choice %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
                {% if form.import_local_choice.errors %}<p class="text-red-500 text-xs mt-1">{{ form.import_local_choice.errors }}</p>{% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Excise Applicable</label>
                <div class="mt-1 space-x-4">
                    {% for radio in form.excise_applicable %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
                {% if form.excise_applicable.errors %}<p class="text-red-500 text-xs mt-1">{{ form.excise_applicable.errors }}</p>{% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">Absolute</label>
                <div class="mt-1 flex items-center">
                    {{ form.absolute }}
                </div>
                {% if form.absolute.errors %}<p class="text-red-500 text-xs mt-1">{{ form.absolute.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.uploaded_image.id_for_label }}" class="block text-sm font-medium text-gray-700">Drw/Image</label>
                {{ form.uploaded_image }}
                {% if form.uploaded_image.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uploaded_image.errors }}</p>{% endif %}
                {% if form.instance.image_url %}<img src="{{ form.instance.image_url }}" alt="Item Image" class="mt-2 h-16 w-16 object-cover rounded-md">{% endif %}
            </div>
            <div>
                <label for="{{ form.buyer.id_for_label }}" class="block text-sm font-medium text-gray-700">Buyer</label>
                {{ form.buyer }}
                {% if form.buyer.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.uploaded_spec_sheet.id_for_label }}" class="block text-sm font-medium text-gray-700">Spec. Sheet</label>
                {{ form.uploaded_spec_sheet }}
                {% if form.uploaded_spec_sheet.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uploaded_spec_sheet.errors }}</p>{% endif %}
                {% if form.instance.spec_sheet_url %}<a href="{{ form.instance.spec_sheet_url }}" target="_blank" class="text-blue-600 hover:underline text-sm mt-2 block">View Spec Sheet</a>{% endif %}
            </div>
            <div>
                <label class="block text-sm font-medium text-gray-700">A/C Head Type</label>
                <div class="mt-1 space-y-2">
                    {% for radio in form.ac_head_type %}
                        <label class="inline-flex items-center">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                    {% endfor %}
                </div>
                {% if form.ac_head_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ac_head_type.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.ac_head.id_for_label }}" class="block text-sm font-medium text-gray-700">A/C Head</label>
                <select id="id_ac_head" name="ac_head" class="box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        x-bind:disabled="loadingACHeads">
                    <template x-if="loadingACHeads">
                        <option value="">Loading...</option>
                    </template>
                    <template x-for="head in acHeads" :key="head.id">
                        <option :value="head.id" x-text="head.name"></option>
                    </template>
                </select>
                {% if form.ac_head.errors %}<p class="text-red-500 text-xs mt-1">{{ form.ac_head.errors }}</p>{% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition-colors duration-200"
                _="on click remove .is-active from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition-colors duration-200">
                Submit
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mt-4">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
    </form>
</div>
```

`confirm_delete.html` (Partial for Delete Confirmation)
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete item: <strong>{{ object.item_code }} - {{ object.manf_desc }}</strong>?</p>
    <form hx-delete="{% url 'itemmaster_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition-colors duration-200"
                _="on click remove .is-active from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md transition-colors duration-200">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`inventory_erp/urls.py`)

```python
from django.urls import path
from .views import (
    ItemMasterListView, ItemMasterCreateView, ItemMasterUpdateView,
    ItemMasterDeleteView, ItemMasterTablePartialView, ACHeadFilterView
)

urlpatterns = [
    path('itemmaster/', ItemMasterListView.as_view(), name='itemmaster_list'),
    path('itemmaster/add/', ItemMasterCreateView.as_view(), name='itemmaster_add'),
    path('itemmaster/edit/<int:pk>/', ItemMasterUpdateView.as_view(), name='itemmaster_edit'),
    path('itemmaster/delete/<int:pk>/', ItemMasterDeleteView.as_view(), name='itemmaster_delete'),
    # HTMX partial view for the table
    path('itemmaster/table/', ItemMasterTablePartialView.as_view(), name='itemmaster_table'),
    # HTMX/AJAX endpoint for dynamically filtered A/C Heads
    path('ac-heads/', ACHeadFilterView.as_view(), name='achead_filter'),
]
```

#### 4.6 Tests (`inventory_erp/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db.utils import IntegrityError
from unittest.mock import patch, MagicMock
import datetime
import io

from .models import ItemMaster, Category, UOM, Location, ItemClass, Buyer, OfficeStaff, ACHead, CompanySetting, FinancialYear

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data
        CompanySetting.objects.create(comp_id=1, item_code_length=9)
        FinancialYear.objects.create(fin_year_id=1, comp_id=1, opening_date=datetime.date(2023, 1, 1))
        Category.objects.create(cid=101, symbol='CAT', cname='Category A', comp_id=1)
        Category.objects.create(cid=102, symbol='C2', cname='Category B', comp_id=1)
        UOM.objects.create(id=1, uom_name='KG')
        Location.objects.create(id=1, location_name='Warehouse A')
        ItemClass.objects.create(id=1, item_class='Raw Material')
        OfficeStaff.objects.create(emp_id=1, employee_name='John Doe')
        Buyer.objects.create(id=1, category='B', nos=1, emp=OfficeStaff.objects.get(emp_id=1))
        ACHead.objects.create(id=1, head_name='Labour Costs', head_type='Labour')
        ACHead.objects.create(id=2, head_name='Material Costs', head_type='With Material')

        # Create test ItemMaster instance
        cls.item_master_data = {
            'id': 1,
            'session_id': 'testuser',
            'comp_id': 1,
            'fin_year_id': 1,
            'category': Category.objects.get(cid=101),
            'part_no': '123-456-789',
            'manf_desc': 'Test Item Description',
            'uom_basic': UOM.objects.get(id=1),
            'min_order_qty': 10.000,
            'min_stock_qty': 5.000,
            'stock_qty': 100.000,
            'location': Location.objects.get(id=1),
            'item_code': 'CAT123-456-789',
            'item_class': ItemClass.objects.get(id=1),
            'lead_days': 7,
            'inspection_days': 2,
            'buyer': Buyer.objects.get(id=1),
            'ac_head': ACHead.objects.get(id=1),
            'absolute': True,
            'excise': False,
            'import_local': True,
            'uom_con_fact': False,
            'opening_bal_date': datetime.date(2023, 1, 15),
            'opening_bal_qty': 50.000,
        }
        cls.item = ItemMaster.objects.create(**cls.item_master_data)

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.manf_desc, 'Test Item Description')
        self.assertEqual(item.item_code, 'CAT123-456-789')
        self.assertTrue(item.absolute)
        self.assertFalse(item.excise)

    def test_item_code_generation(self):
        # Test the generate_item_code method directly
        item = ItemMaster.objects.get(id=1)
        new_item_code = item.generate_item_code('987', '654', '321')
        self.assertEqual(new_item_code, 'CAT987-654-321')

    def test_item_code_generation_invalid_length(self):
        # Temporarily change item_code_length
        CompanySetting.objects.filter(comp_id=1).update(item_code_length=10)
        item = ItemMaster.objects.get(id=1)
        with self.assertRaises(ValidationError) as cm:
            item.generate_item_code('123', '456', '789') # Still results in 9 chars
        self.assertIn("Invalid Item Code length", str(cm.exception))
        # Reset to original length
        CompanySetting.objects.filter(comp_id=1).update(item_code_length=9)

    def test_part_no_segments_property(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.part_no_segments, ('123', '456', '789'))

    def test_image_and_spec_sheet_properties(self):
        # Create a dummy image and file
        dummy_image_data = b'GIF89a\x01\x00\x01\x00\x00\xff\x00,\x00\x00\x00\x00\x01\x00\x01\x00\x00\x02\x02D\x01\x00;'
        dummy_pdf_data = b'%PDF-1.4\n1 0 obj<</Type/Catalog/Pages 2 0 R>>endobj 2 0 obj<</Type/Pages/Count 0>>endobj\nxref\n0 3\n0000000000 65535 f\n0000000009 00000 n\n0000000074 00000 n\ntrailer<</Size 3/Root 1 0 R>>startxref\n106\n%%EOF'

        item = ItemMaster.objects.get(id=1)
        item.file_data = dummy_image_data
        item.file_name = 'test.gif'
        item.content_type = 'image/gif'
        item.att_data = dummy_pdf_data
        item.att_name = 'spec.pdf'
        item.att_content_type = 'application/pdf'
        item.save()

        self.assertIsNotNone(item.image_url)
        self.assertIn('data:image/gif;base64,', item.image_url)
        self.assertIsNotNone(item.spec_sheet_url)
        self.assertIn('data:application/pdf;base64,', item.spec_sheet_url)

    def test_item_master_manager_filter_items(self):
        # Create more items for filtering tests
        ItemMaster.objects.create(
            id=2, comp_id=1, fin_year_id=1, session_id='testuser', category=Category.objects.get(cid=102),
            part_no='001-002-003', manf_desc='Another Item', uom_basic=UOM.objects.get(id=1),
            min_order_qty=1.000, min_stock_qty=1.000, stock_qty=0.000, location=Location.objects.get(id=1),
            item_code='C2001-002-003', item_class=ItemClass.objects.get(id=1), lead_days=0, inspection_days=0,
            buyer=Buyer.objects.get(id=1), ac_head=ACHead.objects.get(id=2)
        )
        ItemMaster.objects.create(
            id=3, comp_id=1, fin_year_id=1, session_id='testuser', category=Category.objects.get(cid=101),
            part_no='111-222-333', manf_desc='Some Other Item', uom_basic=UOM.objects.get(id=1),
            min_order_qty=1.000, min_stock_qty=1.000, stock_qty=0.000, location=Location.objects.get(id=1),
            item_code='CAT111-222-333', item_class=ItemClass.objects.get(id=1), lead_days=0, inspection_days=0,
            buyer=Buyer.objects.get(id=1), ac_head=ACHead.objects.get(id=2)
        )

        # Test filter by category
        filtered = ItemMaster.objects.filter_items(item_type='Category', category_id=101, comp_id=1, fin_year_id=1)
        self.assertEqual(filtered.count(), 2)
        self.assertTrue(filtered.filter(id=1).exists())
        self.assertTrue(filtered.filter(id=3).exists())

        # Test filter by category and item code
        filtered = ItemMaster.objects.filter_items(item_type='Category', category_id=101, search_by='tblDG_Item_Master.ItemCode', search_text='CAT123', comp_id=1, fin_year_id=1)
        self.assertEqual(filtered.count(), 1)
        self.assertTrue(filtered.filter(id=1).exists())

        # Test filter by description
        filtered = ItemMaster.objects.filter_items(item_type='Category', search_by='tblDG_Item_Master.ManfDesc', search_text='Another', comp_id=1, fin_year_id=1)
        self.assertEqual(filtered.count(), 1)
        self.assertTrue(filtered.filter(id=2).exists())
        
        # Test WOItems type search by ManfDesc
        filtered = ItemMaster.objects.filter_items(item_type='WOItems', search_by='tblDG_Item_Master.ManfDesc', search_text='some', comp_id=1, fin_year_id=1)
        self.assertEqual(filtered.count(), 1)
        self.assertTrue(filtered.filter(id=3).exists())

        # Test WOItems type search by ItemCode
        filtered = ItemMaster.objects.filter_items(item_type='WOItems', search_by='tblDG_Item_Master.ItemCode', search_text='001', comp_id=1, fin_year_id=1)
        self.assertEqual(filtered.count(), 1)
        self.assertTrue(filtered.filter(id=2).exists())


class ItemMasterViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create necessary lookup data for views
        CompanySetting.objects.create(comp_id=1, item_code_length=9)
        FinancialYear.objects.create(fin_year_id=1, comp_id=1, opening_date=datetime.date(2023, 1, 1))
        self.category_a = Category.objects.create(cid=101, symbol='CAT', cname='Category A', comp_id=1)
        self.category_b = Category.objects.create(cid=102, symbol='C2', cname='Category B', comp_id=1)
        self.uom = UOM.objects.create(id=1, uom_name='KG')
        self.location = Location.objects.create(id=1, location_name='Warehouse A')
        self.item_class = ItemClass.objects.create(id=1, item_class='Raw Material')
        self.staff = OfficeStaff.objects.create(emp_id=1, employee_name='John Doe')
        self.buyer = Buyer.objects.create(id=1, category='B', nos=1, emp=self.staff)
        self.ac_head_labour = ACHead.objects.create(id=1, head_name='Labour Costs', head_type='Labour')
        self.ac_head_material = ACHead.objects.create(id=2, head_name='Material Costs', head_type='With Material')

        # Create a test item for update/delete
        self.item = ItemMaster.objects.create(
            id=1, session_id='testuser', comp_id=1, fin_year_id=1, category=self.category_a,
            part_no='123-456-789', manf_desc='Original Item', uom_basic=self.uom,
            min_order_qty=10, min_stock_qty=5, stock_qty=100, location=self.location,
            item_code='CAT123-456-789', item_class=self.item_class, lead_days=7,
            inspection_days=2, buyer=self.buyer, ac_head=self.ac_head_labour,
            absolute=True, excise=False, import_local=True, uom_con_fact=False,
            opening_bal_date=datetime.date(2023, 1, 15), opening_bal_qty=50
        )

    def test_list_view_get(self):
        response = self.client.get(reverse('itemmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_erp/itemmaster/list.html')
        self.assertIn('itemmasters', response.context)
        self.assertContains(response, 'Original Item')

    def test_item_master_table_partial_view(self):
        # Test initial load
        response = self.client.get(reverse('itemmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_erp/itemmaster/_item_master_table.html')
        self.assertContains(response, 'Original Item')

        # Test filtering
        response = self.client.get(reverse('itemmaster_table'), {'type': 'Category', 'category': self.category_a.cid})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Original Item')
        self.assertEqual(len(response.context['itemmasters']), 1)

    def test_create_view_get(self):
        response = self.client.get(reverse('itemmaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_erp/itemmaster/_item_master_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_valid_data(self):
        image_file = SimpleUploadedFile("test_image.png", b"file_content", content_type="image/png")
        spec_sheet_file = SimpleUploadedFile("test_spec.pdf", b"pdf_content", content_type="application/pdf")

        data = {
            'category': self.category_b.cid,
            'part_no_segment1': '999',
            'part_no_segment2': '888',
            'part_no_segment3': '777',
            'manf_desc': 'New Test Item',
            'uom_basic': self.uom.id,
            'min_order_qty': '10.000',
            'min_stock_qty': '5.000',
            'stock_qty': '0.000',
            'location': self.location.id,
            'item_class': self.item_class.id,
            'lead_days': '0.000',
            'inspection_days': '0.000',
            'buyer': self.buyer.id,
            'ac_head': self.ac_head_material.id,
            'uom_conversion': 'False',
            'import_local_choice': 'True',
            'excise_applicable': 'True',
            'absolute': 'on', # Checkbox value
            'opening_bal_date': '01-01-2023', # Format as expected by form
            'opening_bal_qty': '0.000',
            'uploaded_image': image_file,
            'uploaded_spec_sheet': spec_sheet_file,
            'ac_head_type': 'With Material', # From radio buttons
        }
        
        # Mock request.user for form.save()
        with patch('inventory_erp.forms.ItemMasterForm.save') as mock_form_save:
            mock_form_save.return_value = MagicMock(spec=ItemMaster) # Return a mock ItemMaster instance
            mock_form_save.return_value.item_code = 'C2999-888-777' # Set mock item_code

            response = self.client.post(reverse('itemmaster_add'), data, HTTP_HX_REQUEST='true', follow=True)
            
            self.assertEqual(response.status_code, 204) # HTMX success response for CreateView
            self.assertTrue(mock_form_save.called) # Verify form save was attempted

            # Manually verify that an object was created using the manager, as save() was mocked
            # In a real test, you'd not mock save() but check DB directly.
            # Here, we check the database for the item after the POST
            self.assertTrue(ItemMaster.objects.filter(item_code='C2999-888-777').exists())
            self.assertContains(response, 'refreshItemMasterList', status_code=204) # Check HTMX trigger

    def test_create_view_post_invalid_data(self):
        data = {
            'category': self.category_a.cid,
            'part_no_segment1': '12', # Invalid length
            'part_no_segment2': '345',
            'part_no_segment3': '678',
            'manf_desc': '', # Missing required
            'uom_basic': self.uom.id,
            'location': self.location.id,
            # Missing other required fields or invalid formats
        }
        response = self.client.post(reverse('itemmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'inventory_erp/itemmaster/_item_master_form.html')
        self.assertIn('form', response.context)
        self.assertFormError(response, 'form', 'manf_desc', 'Description is required.')
        self.assertFormError(response, 'form', 'part_no_segment1', 'Each part of the Part No must be 3 digits.')
        # Ensure no new object was created
        self.assertEqual(ItemMaster.objects.count(), 1) # Only the setup item should exist

    def test_update_view_get(self):
        response = self.client.get(reverse('itemmaster_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_erp/itemmaster/_item_master_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.item)
        self.assertEqual(response.context['form'].initial['manf_desc'], 'Original Item')

    def test_update_view_post_valid_data(self):
        updated_desc = 'Updated Item Description'
        data = {
            'category': self.category_a.cid,
            'part_no_segment1': '123',
            'part_no_segment2': '456',
            'part_no_segment3': '789',
            'manf_desc': updated_desc,
            'uom_basic': self.uom.id,
            'min_order_qty': '10.000',
            'min_stock_qty': '5.000',
            'stock_qty': '0.000',
            'location': self.location.id,
            'item_class': self.item_class.id,
            'lead_days': '0.000',
            'inspection_days': '0.000',
            'buyer': self.buyer.id,
            'ac_head': self.ac_head_material.id,
            'uom_conversion': 'True',
            'import_local_choice': 'False',
            'excise_applicable': 'False',
            'absolute': 'on',
            'opening_bal_date': '01-01-2023',
            'opening_bal_qty': '0.000',
            'ac_head_type': 'With Material',
        }
        
        response = self.client.post(reverse('itemmaster_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true', follow=True)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.item.refresh_from_db()
        self.assertEqual(self.item.manf_desc, updated_desc)
        self.assertTrue(self.item.uom_con_fact)
        self.assertContains(response, 'refreshItemMasterList', status_code=204) # Check HTMX trigger

    def test_delete_view_get(self):
        response = self.client.get(reverse('itemmaster_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_erp/itemmaster/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.item)

    def test_delete_view_post(self):
        self.assertEqual(ItemMaster.objects.count(), 1)
        response = self.client.delete(reverse('itemmaster_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true', follow=True)
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(ItemMaster.objects.count(), 0)
        self.assertContains(response, 'refreshItemMasterList', status_code=204) # Check HTMX trigger

    def test_ac_head_filter_view(self):
        # Create more AC Heads
        ACHead.objects.create(id=3, head_name='Travel Expenses', head_type='Expenses')
        ACHead.objects.create(id=4, head_name='Consulting Services', head_type='Service Provider')

        response = self.client.get(reverse('achead_filter'), {'type': 'Expenses'})
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIn({'id': 3, 'name': 'Travel Expenses'}, json_response['options'])
        self.assertEqual(len(json_response['options']), 1)

        response = self.client.get(reverse('achead_filter')) # No type, should return all
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertEqual(len(json_response['options']), 4)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Tabs**: The main `list.html` uses `hx-get` on tab buttons to load `_item_master_form.html` (for Add) or `_item_master_table.html` (for View) into the `#tab-content` div.
-   **HTMX for CRUD Modals**: "Add New Item", "Edit", and "Delete" buttons use `hx-get` to fetch the form/confirmation partials (`_item_master_form.html`, `confirm_delete.html`) into `#modalContent`. Form submissions within the modal use `hx-post` and `hx-swap="none"` with `HX-Trigger: refreshItemMasterList` on success to refresh the main item list without full page reload.
-   **Alpine.js for UI State**:
    -   Manages active tab state in `list.html`.
    -   Controls modal visibility (`x-data`, `x-show`, `on click`).
    -   Manages dynamic dropdown visibility in `_item_master_table.html` based on `DrpType` and `DrpSearchCode` selections.
    -   Handles dynamic loading of `ACHead` options in `_item_master_form.html` based on `ac_head_type` radio selection using `x-data` and `x-init` to fetch via AJAX and `x-model` for two-way binding.
    -   `x-mask:decimal` directive provides client-side input masking for decimal fields, mirroring ASP.NET `onkeyup` numeric validation.
-   **DataTables**: Integrated into `_item_master_table.html`. The `$(document).ready` block ensures DataTables is initialized on the dynamically loaded table. It handles client-side pagination, sorting, and info display. Searching is primarily handled by the backend Django view filtering, but DataTables' own search box could be enabled if client-side filtering is desired *on top* of backend filtering. For this migration, backend filtering is prioritized.
-   **DRY Principle**: Templates are broken into partials (`_item_master_form.html`, `_item_master_table.html`) for reusability with HTMX. All CDN links are assumed to be in the `core/base.html` template.
-   **No Custom JavaScript**: Aside from the minimal Alpine.js setup and DataTables initialization script, no complex custom JS is required for dynamic interactions, fulfilling the HTMX-only interactions preference.
-   **Tailwind CSS**: All form fields and buttons are styled with Tailwind CSS classes as per guidelines.

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Item Master module to a modern Django application, focusing on automation-ready steps and leveraging contemporary web technologies for a superior user experience.