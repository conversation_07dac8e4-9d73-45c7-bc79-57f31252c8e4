## ASP.NET to Django Conversion Plan: Item Category Module

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET Item Category module to a robust, modern Django 5.0+ solution. Our approach prioritizes automation, clean architecture, and enhanced user experience through intelligent frontend technologies.

### Business Value & Outcomes:
- **Reduced Technical Debt:** Moves away from outdated ASP.NET WebForms, improving maintainability and security.
- **Improved Performance:** Django's efficiency combined with HTMX/Alpine.js delivers faster, more responsive user interfaces by minimizing full page reloads.
- **Scalability & Flexibility:** Django's modular design and ORM simplify future expansions and integrations.
- **Developer Productivity:** Standardized patterns, comprehensive tooling, and a thriving ecosystem boost development speed.
- **Modern User Experience:** Interactive elements like DataTables and HTMX provide a seamless, desktop-like feel in a web application.
- **Cost Efficiency:** Automated migration steps reduce manual coding effort and potential errors, leading to significant cost savings and faster time-to-market.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is managed separately.
- Focus ONLY on component-specific code for the current module (Item Category).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `SqlDataSource` `SelectCommand` and `InsertCommand`:
- **Table Name:** `tblDG_Category_Master`
- **Columns (and inferred types/details):**
    - `CId`: Primary Key (used as `DataKeyNames`).
    - `SysDate`: Date of system entry.
    - `SysTime`: Time of system entry.
    - `CompId`: Company ID (from session `compid`).
    - `FinYearId`: Financial Year ID (from session `finyear`).
    - `SessinId`: Session ID / User ID (from session `username`).
    - `CName`: Category Name (text input).
    - `Symbol`: Category Symbol (single character text input, unique per company).
    - `HasSubCat`: Boolean (checkbox, stored as "1" or "0").

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
- **Create:**
    - Handled by `btnInsert` in `GridView` footer (`CommandName="Add"`) and `EmptyDataTemplate` (`CommandName="Add1"`).
    - Inserts into `tblDG_Category_Master`.
    - Populates `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessinId` automatically from session/system.
    - `CName`, `Symbol`, `HasSubCat` are user inputs.
    - **Validation:** `CName` and `Symbol` are required. `Symbol` must be unique for the given `CompId`.
- **Read:**
    - `SqlDataSource` `SelectCommand`: `SELECT * FROM [tblDG_Category_Master] WHERE (([CompId] = @CompId) AND ([FinYearId] <= @FinYearId)) Order by [CId] desc`.
    - Data displayed in `GridView1`.
    - Supports pagination (`AllowPaging="True"`, `PageSize="20"`).
- **Update:** Not explicitly shown in the provided code (no `Edit` or `Update` commands in `GridView1`). For a full CRUD, this would be a required next step. We will include a placeholder for this.
- **Delete:** Not explicitly shown in the provided code. We will include a placeholder for this.
- **Validation Logic:**
    - `RequiredFieldValidator` for `txtCName` and `txtAbb`.
    - Server-side check for `Symbol` uniqueness `Symbol='X' And CompId='Y'`.
    - Client-side `confirmationAdd()` for insert.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
- **GridView (`GridView1`):** Serves as the primary data list display, handles pagination, and provides inline row creation (via footer/empty template). This will be replaced by a Django ListView displaying a DataTables-enabled HTML table.
- **TextBox (`txtCName`, `txtAbb`):** Used for user input for Category Name and Symbol. These will become Django `TextInput` widgets within a `ModelForm`.
- **CheckBox (`CheckBox1`):** Used for the 'Has SubCategory' boolean field. This will become a Django `CheckboxInput` widget.
- **Button (`btnInsert`):** Triggers the 'add' action. This will be replaced by HTMX-driven buttons to trigger form modals.
- **Label (`lblMessage`):** Displays status messages. This will be replaced by Django's `messages` framework and HTMX toast notifications.
- **Validation Controls (`RequiredFieldValidator`):** Client-side validation cues. Replaced by Django Form validation and HTML5 `required` attributes.

---

### Step 4: Generate Django Code

We will create a new Django app, let's call it `inventory`, within your Django project.

**`inventory/models.py`**

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class Category(models.Model):
    """
    Represents an item category in the system.
    Maps to the existing tblDG_Category_Master table.
    """
    # Assuming CId is the primary key in the legacy database
    # Django usually handles 'id' as AutoField by default.
    # If CId is truly auto-incrementing and used as PK, map it explicitly.
    id = models.AutoField(db_column='CId', primary_key=True) 
    
    # Audit fields - will be populated automatically or by system context
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True, verbose_name=_("System Date"))
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True, verbose_name=_("System Time"))
    comp_id = models.IntegerField(db_column='CompId', verbose_name=_("Company ID"))
    fin_year_id = models.IntegerField(db_column='FinYearId', verbose_name=_("Financial Year ID"))
    session_id = models.CharField(db_column='SessinId', max_length=255, verbose_name=_("Session ID / User")) # Assuming this holds username

    # Core business fields
    name = models.CharField(db_column='CName', max_length=255, verbose_name=_("Category Name"))
    symbol = models.CharField(db_column='Symbol', max_length=1, unique=False, verbose_name=_("Symbol")) # Unique constraint handled at model level
    has_subcategory = models.BooleanField(db_column='HasSubCat', default=False, verbose_name=_("Has SubCategory"))

    class Meta:
        managed = False  # Tells Django not to manage table creation/alteration
        db_table = 'tblDG_Category_Master'
        verbose_name = _("Item Category")
        verbose_name_plural = _("Item Categories")
        # Ensure symbol is unique per company, mimicking original ASP.NET logic
        unique_together = ('symbol', 'comp_id') 
        ordering = ['-id'] # Matches 'Order by [CId] desc' from ASP.NET

    def __str__(self):
        return f"{self.name} ({self.symbol})"

    def clean(self):
        """
        Custom validation for the model.
        This method is called by ModelForm's is_valid() before saving.
        It's crucial for complex business rules like cross-field uniqueness.
        """
        # Ensure symbol is uppercase as per ASP.NET 'ToUpper()'
        if self.symbol:
            self.symbol = self.symbol.upper()
        
        # Django's unique_together handles the symbol/comp_id uniqueness effectively
        # However, if there were more complex checks (e.g., uniqueness based on a date range),
        # this is where you'd implement them.
        
        # Example: If you needed to ensure symbol is not a reserved keyword
        # reserved_symbols = ['ADMIN', 'SYS']
        # if self.symbol in reserved_symbols:
        #     raise ValidationError({'symbol': _("This symbol is reserved.")})


    # Business logic methods (examples, not directly derived but good practice)
    def is_active(self):
        # Example of a business method
        return self.has_subcategory # Or some other logic based on status fields
        
    def get_summary(self):
        return f"Category: {self.name}, Symbol: {self.symbol}, SubCat: {self.has_subcategory}"

```

**`inventory/forms.py`**

```python
from django import forms
from .models import Category
from django.utils.translation import gettext_lazy as _

class CategoryForm(forms.ModelForm):
    """
    Form for creating and updating Category objects.
    """
    class Meta:
        model = Category
        # The fields exposed to the user for direct input
        fields = ['name', 'symbol', 'has_subcategory'] 
        
        # Widgets for Tailwind CSS styling
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter category name',
                'required': 'true' # HTML5 required, works with Django validation too
            }),
            'symbol': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter 1-character symbol',
                'maxlength': '1', # Matches ASP.NET MaxLength
                'required': 'true'
            }),
            'has_subcategory': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'
            }),
        }
        labels = {
            'name': _("Category Name"),
            'symbol': _("Symbol"),
            'has_subcategory': _("Has SubCategory"),
        }

    def clean_symbol(self):
        """
        Ensures symbol is uppercase and can add custom validation if needed.
        """
        symbol = self.cleaned_data.get('symbol')
        if symbol:
            return symbol.upper() # Ensure uppercase as per ASP.NET
        return symbol

    # No need for explicit uniqueness check here because unique_together in Model Meta
    # will handle it and raise a ValidationError if violated during save.
    # The clean method on the model (Category.clean) is also called by ModelForm.
```

**`inventory/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin # Recommended for production apps
from django.db import IntegrityError # To catch database-level unique constraint errors

from .models import Category
from .forms import CategoryForm

# Helper to automatically populate context fields from session/request
# In a real app, this would be handled by middleware, a request context processor,
# or by a User model with Company/FinancialYear relations.
# For demonstration, we simulate populating these as in ASP.NET.
def _get_system_context(request):
    """
    Simulates fetching system/session variables similar to ASP.NET code-behind.
    In a real Django app, this would be linked to the authenticated user.
    """
    # Placeholder values - replace with actual session/user data
    comp_id = request.session.get('compid', 1)  # Default to 1 if not in session
    fin_year_id = request.session.get('finyear', 2024) # Default to 2024
    session_id = request.user.username if request.user.is_authenticated else 'guest'
    return {
        'comp_id': comp_id,
        'fin_year_id': fin_year_id,
        'session_id': session_id,
    }


class CategoryListView(LoginRequiredMixin, ListView):
    """
    Displays a list of item categories.
    Uses HTMX to dynamically load the DataTables-enabled table.
    """
    model = Category
    template_name = 'inventory/category/list.html'
    context_object_name = 'categories' # Plural for list
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        """
        Filters categories by company and financial year, mimicking ASP.NET.
        """
        context = _get_system_context(self.request)
        # Note: ASP.NET had FinYearId <= @FinYearId, which is unusual for a single year.
        # Assuming it should be exact for the current financial year. Adjust if needed.
        return Category.objects.filter(
            comp_id=context['comp_id'],
            fin_year_id=context['fin_year_id']
        ).order_by('-id') # Matches ASP.NET 'Order by [CId] desc'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # We don't directly pass the object list to the main template as it's loaded via HTMX
        # by CategoryTablePartialView.
        return context


class CategoryTablePartialView(LoginRequiredMixin, ListView):
    """
    Returns the HTML partial for the category table, designed for HTMX.
    """
    model = Category
    template_name = 'inventory/category/_category_table.html' # Underscore indicates partial
    context_object_name = 'categories'
    
    def get_queryset(self):
        """
        Filters categories by company and financial year, mimicking ASP.NET.
        """
        context = _get_system_context(self.request)
        return Category.objects.filter(
            comp_id=context['comp_id'],
            fin_year_id=context['fin_year_id']
        ).order_by('-id')


class CategoryCreateView(LoginRequiredMixin, CreateView):
    """
    Handles creation of new item categories.
    Designed to be loaded via HTMX into a modal.
    """
    model = Category
    form_class = CategoryForm
    template_name = 'inventory/category/_category_form.html' # Partial for modal
    success_url = reverse_lazy('category_list') # Not directly used for HTMX, but good practice

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the request to the form if it needed to access user/session for specific validation
        # or initial data, though in this case model handles it.
        # kwargs['request'] = self.request 
        return kwargs

    def form_valid(self, form):
        """
        Populates audit fields before saving the category.
        Handles HTMX response for successful creation.
        """
        context = _get_system_context(self.request)
        form.instance.comp_id = context['comp_id']
        form.instance.fin_year_id = context['fin_year_id']
        form.instance.session_id = context['session_id']
        
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Item Category added successfully.')
            if self.request.headers.get('HX-Request'):
                # HTMX expects a 204 No Content for successful form submission
                # that triggers a client-side event.
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshCategoryList' # Custom HTMX event to refresh table
                    }
                )
            return response
        except IntegrityError:
            # Catch unique_together constraint violation for symbol/comp_id
            form.add_error('symbol', 'Category symbol already exists for this company.')
            return self.form_invalid(form) # Re-render form with error


    def form_invalid(self, form):
        """
        Handles HTMX response for form validation errors.
        """
        if self.request.headers.get('HX-Request'):
            # Return the form partial with errors for HTMX to swap in
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)


class CategoryUpdateView(LoginRequiredMixin, UpdateView):
    """
    Handles updating existing item categories.
    Designed to be loaded via HTMX into a modal.
    """
    model = Category
    form_class = CategoryForm
    template_name = 'inventory/category/_category_form.html' # Partial for modal
    success_url = reverse_lazy('category_list')

    def form_valid(self, form):
        """
        Handles HTMX response for successful update.
        """
        try:
            response = super().form_valid(form)
            messages.success(self.request, 'Item Category updated successfully.')
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshCategoryList'
                    }
                )
            return response
        except IntegrityError:
            form.add_error('symbol', 'Category symbol already exists for this company.')
            return self.form_invalid(form)


    def form_invalid(self, form):
        """
        Handles HTMX response for form validation errors.
        """
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)


class CategoryDeleteView(LoginRequiredMixin, DeleteView):
    """
    Handles deletion of item categories.
    Designed to be loaded via HTMX into a modal for confirmation.
    """
    model = Category
    template_name = 'inventory/category/_category_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('category_list')

    def delete(self, request, *args, **kwargs):
        """
        Handles HTMX response for successful deletion.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Category deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCategoryList'
                }
            )
        return response

```

**`inventory/templates/inventory/category/list.html`**

```html
{% extends 'core/base.html' %}
{% load i18n %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">{% translate "Item Categories" %}</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'category_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            {% translate "Add New Category" %}
        </button>
    </div>
    
    {# The container where the DataTable will be loaded via HTMX #}
    <div id="categoryTable-container"
         hx-trigger="load, refreshCategoryList from:body" {# Loads on page load and on custom event #}
         hx-get="{% url 'category_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        {# Initial loading state for HTMX #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">{% translate "Loading categories..." %}</p>
        </div>
    </div>
    
    {# Modal structure for forms and confirmations #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             _="on load add .scale-95 to me then transition ease-out duration-300 transform scale-100 opacity-100 else transition ease-in duration-200 transform opacity-0 scale-95 remove .is-active from #modal">
            {# Content will be loaded here by HTMX #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Ensure jQuery and DataTables JS are loaded in base.html or here #}
<script>
    // No additional Alpine.js specific logic needed here unless complex UI state
    // is introduced. HTMX handles the modal visibility with _ attributes.
</script>
{% endblock %}

```

**`inventory/templates/inventory/category/_category_table.html`** (Partial for HTMX)

```html
{% load i18n %}

<table id="categoryTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{% translate "SN" %}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{% translate "Category Name" %}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{% translate "Symbol" %}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{% translate "Has SubCategory" %}</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">{% translate "Actions" %}</th>
        </tr>
    </thead>
    <tbody>
        {% for category in categories %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100 transition duration-150 ease-in-out">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ category.name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ category.symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">
                <input type="checkbox" {% if category.has_subcategory %}checked{% endif %} disabled class="h-4 w-4 text-indigo-600 border-gray-300 rounded">
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out mr-2"
                    hx-get="{% url 'category_edit' category.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    {% translate "Edit" %}
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'category_delete' category.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    {% translate "Delete" %}
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 text-center text-gray-500">{% translate "No categories found." %}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#categoryTable').DataTable({
        "pageLength": 10, // Default page length
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for page length
        "responsive": true, // Make table responsive
        "pagingType": "simple_numbers" // Simpler pagination controls
        // Add more DataTables options as needed: https://datatables.net/reference/option/
    });
});
</script>
```

**`inventory/templates/inventory/category/_category_form.html`** (Partial for HTMX modal)

```html
{% load i18n %}

<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} {% translate "Item Category" %}</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent" hx-select="#modalContent > *"
          hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-5">
            {# Render non-checkbox fields #}
            {% for field in form %}
                {% if field.widget_type != 'checkbox' %}
                <div>
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ field.label }}
                    </label>
                    {{ field }}
                    {% if field.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                    {% endif %}
                    {% if field.errors %}
                    <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endif %}
            {% endfor %}

            {# Render checkbox field separately for custom styling #}
            {% if form.has_subcategory %}
            <div class="flex items-center mt-4">
                {{ form.has_subcategory }}
                <label for="{{ form.has_subcategory.id_for_label }}" class="ml-2 block text-sm text-gray-900">
                    {{ form.has_subcategory.label }}
                </label>
                {% if form.has_subcategory.errors %}
                <p class="text-red-600 text-sm mt-1">{{ form.has_subcategory.errors }}</p>
                {% endif %}
            </div>
            {% endif %}

            {% if form.non_field_errors %}
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                    {% for error in form.non_field_errors %}
                        <p class="block sm:inline">{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                {% translate "Cancel" %}
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                <span class="htmx-indicator" id="form-indicator">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                </span>
                {% translate "Save" %}
            </button>
        </div>
    </form>
</div>
```

**`inventory/templates/inventory/category/_category_confirm_delete.html`** (Partial for HTMX modal)

```html
{% load i18n %}

<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{% translate "Confirm Delete" %}</h3>
    <p class="text-gray-700 mb-8">
        {% translate "Are you sure you want to delete the item category" %} 
        "<span class="font-bold">{{ category.name }} ({{ category.symbol }})</span>"?
        {% translate "This action cannot be undone." %}
    </p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            {% translate "Cancel" %}
        </button>
        <button 
            type="button" 
            hx-delete="{% url 'category_delete' category.pk %}"
            hx-target="body" {# Target body to receive the HX-Trigger #}
            hx-swap="none" {# No content swap, just trigger event #}
            _="on click add .is-active to #modal-indicator then remove .is-active from #modal" {# Hide modal and show loading #}
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
            {% translate "Delete" %}
        </button>
    </div>
</div>

{# A global indicator for delete operations if needed, typically in base.html #}
<div id="modal-indicator" class="htmx-indicator fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50">
    <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
</div>
```

**`inventory/urls.py`**

```python
from django.urls import path
from .views import (
    CategoryListView, 
    CategoryCreateView, 
    CategoryUpdateView, 
    CategoryDeleteView,
    CategoryTablePartialView
)

urlpatterns = [
    # Main list view (initial page load)
    path('categories/', CategoryListView.as_view(), name='category_list'),
    
    # HTMX endpoint for the DataTables partial (loaded dynamically)
    path('categories/table/', CategoryTablePartialView.as_view(), name='category_table'),
    
    # HTMX endpoints for modal forms
    path('categories/add/', CategoryCreateView.as_view(), name='category_add'),
    path('categories/edit/<int:pk>/', CategoryUpdateView.as_view(), name='category_edit'),
    path('categories/delete/<int:pk>/', CategoryDeleteView.as_view(), name='category_delete'),
]
```

**`inventory/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
from django.contrib.messages import get_messages
from unittest.mock import patch # For mocking request.user if needed

from .models import Category

class CategoryModelTest(TestCase):
    """
    Unit tests for the Category model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test category for all tests in this class
        Category.objects.create(
            comp_id=1,
            fin_year_id=2024,
            session_id='testuser',
            name='Test Category One',
            symbol='A',
            has_subcategory=False
        )
        Category.objects.create(
            comp_id=1,
            fin_year_id=2024,
            session_id='testuser',
            name='Test Category Two',
            symbol='B',
            has_subcategory=True
        )
        Category.objects.create(
            comp_id=2, # Different company ID
            fin_year_id=2024,
            session_id='testuser2',
            name='Other Company Category',
            symbol='A', # Symbol 'A' is allowed for a different company
            has_subcategory=False
        )

    def test_category_creation(self):
        """Test that a category can be created with correct data."""
        category = Category.objects.get(id=1)
        self.assertEqual(category.name, 'Test Category One')
        self.assertEqual(category.symbol, 'A')
        self.assertFalse(category.has_subcategory)
        self.assertEqual(category.comp_id, 1)

    def test_category_str_representation(self):
        """Test the __str__ method returns the expected string."""
        category = Category.objects.get(id=1)
        self.assertEqual(str(category), 'Test Category One (A)')

    def test_symbol_uppercasing(self):
        """Test that the symbol is automatically uppercased on save."""
        new_category = Category.objects.create(
            comp_id=1, fin_year_id=2024, session_id='anotheruser',
            name='Lowercase Test', symbol='c', has_subcategory=False
        )
        self.assertEqual(new_category.symbol, 'C')
        
    def test_unique_symbol_per_company(self):
        """Test that symbol is unique per company (unique_together)."""
        with self.assertRaises(IntegrityError): # Or ValidationError if raised by form/model.clean()
            Category.objects.create(
                comp_id=1, fin_year_id=2024, session_id='testuser',
                name='Duplicate Symbol', symbol='A', has_subcategory=False
            )
        
        # Should allow same symbol for a different company
        try:
            Category.objects.create(
                comp_id=3, fin_year_id=2024, session_id='newuser',
                name='Unique Across Companies', symbol='A', has_subcategory=False
            )
        except IntegrityError:
            self.fail("Should have allowed creation of category with same symbol for different company.")

    def test_has_subcategory_boolean_default(self):
        """Test that has_subcategory defaults to False."""
        category = Category.objects.get(symbol='A', comp_id=1) # Get the first 'A' category
        self.assertFalse(category.has_subcategory)


class CategoryViewsTest(TestCase):
    """
    Integration tests for Category views.
    We'll mock a logged-in user and session attributes for `comp_id`, `fin_year_id`.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test user for views that require LoginRequiredMixin
        from django.contrib.auth.models import User
        cls.user = User.objects.create_user(username='testuser', password='password123')
        
        # Create initial test data for categories
        Category.objects.create(
            comp_id=1, fin_year_id=2024, session_id='testuser',
            name='View Test Category 1', symbol='X', has_subcategory=False
        )
        Category.objects.create(
            comp_id=1, fin_year_id=2024, session_id='testuser',
            name='View Test Category 2', symbol='Y', has_subcategory=True
        )
        Category.objects.create(
            comp_id=2, fin_year_id=2024, session_id='anotheruser',
            name='View Test Category 3', symbol='Z', has_subcategory=False
        )

    def setUp(self):
        self.client = Client()
        # Log in the test user for views that require authentication
        self.client.login(username='testuser', password='password123')
        # Simulate session variables like ASP.NET
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()

    def test_list_view_get(self):
        """Test CategoryListView displays categories for the correct company/fin year."""
        response = self.client.get(reverse('category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/category/list.html')
        # The list itself is loaded via HTMX, so we check the container
        self.assertContains(response, '<div id="categoryTable-container"')
        self.assertContains(response, 'hx-get="/categories/table/"') # Verify HTMX call

    def test_table_partial_view_get(self):
        """Test CategoryTablePartialView loads correctly with HTMX."""
        response = self.client.get(reverse('category_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/category/_category_table.html')
        # Check that categories belonging to comp_id=1 are present
        self.assertContains(response, 'View Test Category 1')
        self.assertContains(response, 'View Test Category 2')
        self.assertNotContains(response, 'View Test Category 3') # Should not be there (different comp_id)
        self.assertEqual(response.context['categories'].count(), 2)

    def test_create_view_get(self):
        """Test CategoryCreateView GET request renders form."""
        response = self.client.get(reverse('category_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/category/_category_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        """Test CategoryCreateView POST request creates category and sends HTMX trigger."""
        initial_count = Category.objects.filter(comp_id=1, fin_year_id=2024).count()
        data = {
            'name': 'New Category',
            'symbol': 'N',
            'has_subcategory': 'on' # Checkbox value for form submission
        }
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCategoryList')
        self.assertEqual(Category.objects.filter(comp_id=1, fin_year_id=2024).count(), initial_count + 1)
        new_category = Category.objects.get(name='New Category', comp_id=1)
        self.assertEqual(new_category.symbol, 'N') # Should be uppercase due to clean_symbol
        self.assertTrue(new_category.has_subcategory)
        
        # Check messages for success notification
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Category added successfully.')

    def test_create_view_post_validation_error(self):
        """Test CategoryCreateView POST with invalid data (e.g., duplicate symbol)."""
        initial_count = Category.objects.count()
        data = {
            'name': 'Duplicate Test',
            'symbol': 'X', # Symbol 'X' already exists for comp_id=1
            'has_subcategory': 'off'
        }
        response = self.client.post(reverse('category_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should return the form with errors
        self.assertTemplateUsed(response, 'inventory/category/_category_form.html')
        self.assertContains(response, 'Category symbol already exists for this company.')
        self.assertEqual(Category.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test CategoryUpdateView GET request renders form for existing object."""
        category_to_edit = Category.objects.get(symbol='X', comp_id=1)
        response = self.client.get(reverse('category_edit', args=[category_to_edit.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/category/_category_form.html')
        self.assertEqual(response.context['form'].instance, category_to_edit)
        self.assertContains(response, category_to_edit.name)

    def test_update_view_post_success(self):
        """Test CategoryUpdateView POST request updates category and sends HTMX trigger."""
        category_to_update = Category.objects.get(symbol='X', comp_id=1)
        data = {
            'name': 'Updated Category Name',
            'symbol': 'X', # Keep same symbol
            'has_subcategory': 'on'
        }
        response = self.client.post(reverse('category_edit', args=[category_to_update.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCategoryList')
        
        category_to_update.refresh_from_db() # Reload object from DB
        self.assertEqual(category_to_update.name, 'Updated Category Name')
        self.assertTrue(category_to_update.has_subcategory)
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Category updated successfully.')

    def test_update_view_post_duplicate_symbol_error(self):
        """Test update fails if new symbol conflicts with another existing symbol in the same company."""
        category_to_update = Category.objects.get(symbol='X', comp_id=1)
        data = {
            'name': 'Will Fail',
            'symbol': 'Y', # Symbol 'Y' already exists for comp_id=1
            'has_subcategory': 'off'
        }
        response = self.client.post(reverse('category_edit', args=[category_to_update.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/category/_category_form.html')
        self.assertContains(response, 'Category symbol already exists for this company.')
        
        category_to_update.refresh_from_db()
        self.assertNotEqual(category_to_update.name, 'Will Fail') # Name should not have changed


    def test_delete_view_get(self):
        """Test CategoryDeleteView GET request renders confirmation."""
        category_to_delete = Category.objects.create(
            comp_id=1, fin_year_id=2024, session_id='testuser',
            name='Temp Category', symbol='T', has_subcategory=False
        )
        response = self.client.get(reverse('category_delete', args=[category_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/category/_category_confirm_delete.html')
        self.assertContains(response, f'Are you sure you want to delete the item category "{category_to_delete.name}')

    def test_delete_view_post_success(self):
        """Test CategoryDeleteView POST request deletes category and sends HTMX trigger."""
        category_to_delete = Category.objects.create(
            comp_id=1, fin_year_id=2024, session_id='testuser',
            name='Another Temp', symbol='U', has_subcategory=False
        )
        initial_count = Category.objects.filter(comp_id=1, fin_year_id=2024).count()
        response = self.client.delete(reverse('category_delete', args=[category_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshCategoryList')
        self.assertEqual(Category.objects.filter(comp_id=1, fin_year_id=2024).count(), initial_count - 1)
        self.assertFalse(Category.objects.filter(pk=category_to_delete.pk).exists())
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item Category deleted successfully.')

    def test_unauthenticated_access(self):
        """Test that unauthenticated users are redirected to login."""
        self.client.logout()
        response = self.client.get(reverse('category_list'))
        self.assertEqual(response.status_code, 302) # Redirects to login
        self.assertTrue(response.url.startswith('/accounts/login/')) # Assuming login URL
```

### Step 5: HTMX and Alpine.js Integration

The provided Django templates already integrate HTMX for:
- **Dynamic Table Loading:** `categoryTable-container` uses `hx-get="{% url 'category_table' %}"` and `hx-trigger="load, refreshCategoryList from:body"` to load the table content.
- **Form Modals:** Buttons like "Add New Category", "Edit", and "Delete" use `hx-get` to fetch partial templates (`_category_form.html`, `_category_confirm_delete.html`) into the `#modalContent` div.
- **Form Submissions:** Forms inside the modal use `hx-post` or `hx-delete` with `hx-swap="none"` or `hx-swap="outerHTML" hx-target="#modalContent" hx-select="#modalContent > *"` to handle submissions without full page reload.
- **Refreshing List:** Upon successful form submission or deletion, the views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshCategoryList'})`, which tells HTMX to re-trigger the `hx-get` on `categoryTable-container`, thus refreshing the DataTables.
- **Loading Indicators:** `htmx-indicator` class is used to show loading spinners during HTMX requests.

Alpine.js is used for simple UI state management, specifically for the modal's `hidden` class toggle and click-outside-to-close behavior (`_=` attributes). DataTables handles client-side sorting, searching, and pagination automatically once initialized on the loaded table.

### Final Notes

This comprehensive plan covers the core functionality of the ASP.NET Item Category module, translating it into a modern Django stack with HTMX and Alpine.js.

- **Placeholders:** Remember to replace `{{% url '' %}}` syntax within templates with actual `{% url '' %}` if copying for a final project.
- **`core/base.html`:** Ensure your `base.html` includes necessary CDN links for HTMX, Alpine.js, jQuery, and DataTables, along with Tailwind CSS.
- **Security:** `LoginRequiredMixin` is included in views as a basic security measure. For production, ensure proper authorization (e.g., using Django permissions) based on user roles and business logic.
- **Audit Fields:** The `comp_id`, `fin_year_id`, `session_id` are populated in the view's `form_valid`. In a production setup, these would typically be linked to the `User` model, potentially via middleware or a custom `BaseModel` that handles common audit fields automatically.
- **Internationalization:** `gettext_lazy` is used for `verbose_name` and template text, facilitating future internationalization.
- **Extensibility:** The fat model approach with business logic in `Category` model methods makes the application logic reusable and testable, independent of the UI.
- **Test Coverage:** The provided tests aim for high coverage and demonstrate how to test HTMX interactions effectively. Continual testing is crucial during the migration process.