## ASP.NET to Django Conversion Script: Item Master - Edit

This modernization plan outlines the conversion of the provided ASP.NET `ItemMaster_Edit.aspx` and its C# code-behind to a modern Django-based solution. Our approach prioritizes automation-driven techniques, moving away from manual coding and embracing AI-assisted processes for efficiency and accuracy. We'll leverage Django's "fat model, thin view" pattern, HTMX for dynamic interactions, Alpine.js for UI state management, and DataTables for superior data presentation.

This document uses non-technical language to ensure clarity for business stakeholders, focusing on the *what* and *why* rather than the low-level *how*.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The ASP.NET code primarily interacts with `tblDG_Item_Master` and `tblDG_Category_Master`. The `Fillgrid` method uses a stored procedure `GetAllItem` and constructs dynamic SQL based on various dropdown selections and text input. The `GridView2` control explicitly binds to several columns.

**Identified Tables and Columns:**

*   **`tblDG_Item_Master` (Main Item Data):**
    *   `Id` (Primary Key, integer)
    *   `CId` (Foreign Key to `tblDG_Category_Master.CId`, integer)
    *   `PartNo` (String/Text)
    *   `ItemCode` (String/Text)
    *   `ManfDesc` (Description, String/Text)
    *   `UOMBasic` (Unit of Measurement, String/Text)
    *   `MinOrderQty` (Decimal/Float)
    *   `MinStockQty` (Decimal/Float)
    *   `StockQty` (Decimal/Float)
    *   `Location` (String/Text)
    *   `Absolute` (Boolean/SmallInt)
    *   `Excise` (String/Text)
    *   `ImportLocal` (String/Text)
    *   `OpeningBalDate` (Date)
    *   `OpeningBalQty` (Decimal/Float)
    *   `UOMConFact` (Decimal/Float)
    *   `CompId` (Company ID, integer, likely a FK)
    *   `FinYearId` (Financial Year ID, integer, likely a FK)

*   **`tblDG_Category_Master` (Category Data):**
    *   `CId` (Primary Key, integer)
    *   `Symbol` (Category Symbol, String/Text)
    *   `CName` (Category Name, String/Text)
    *   `CompId` (Company ID, integer, likely a FK)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic from the ASP.NET code.

**Analysis:**
The primary functionality of this ASP.NET page is **Read/Search** operations.

*   **Read/Search:** The page displays a list of "Item Master" records in a `GridView`. It allows filtering and searching based on:
    *   **Type:** "Category" or "WOItems". This dynamically changes the visibility and content of other filter controls.
    *   **Category:** A dropdown (`DrpCategory1`) populated from `tblDG_Category_Master` when "Type" is "Category".
    *   **Search Code:** A dropdown (`DrpSearchCode`) allowing selection of "Item Code", "Description", or "Location" as the search target.
    *   **Search Term:** A textbox (`txtSearchItemCode`) for free-text input or a dropdown (`DropDownList3`) for location.
    *   **Location:** A dropdown (`DropDownList3`) populated by `fun.drpLocat` (presumably a utility function to fetch locations).
    *   **Pagination:** The `GridView` supports pagination.
*   **Dynamic UI Updates:** The `DrpType_SelectedIndexChanged`, `DrpCategory1_SelectedIndexChanged`, and `DrpSearchCode_SelectedIndexChanged` events trigger partial updates of the UI and re-population of the grid.
*   **Database Interaction:** The `Fillgrid` function constructs complex SQL queries using `GetAllItem` stored procedure with numerous parameters, demonstrating a highly conditional filtering logic. It also queries `tblDG_Category_Master` directly.
*   **No Direct Create/Update/Delete:** This specific page does not directly implement CRUD operations for items on the grid. However, the `HyperLinkField` points to `ItemMaster_Edit_Details.aspx` for editing. For a complete Django solution, we will implement standard CRUD views as per the guidelines.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting client-side behaviors.

**Analysis:**
The page is composed of several ASP.NET Web Forms controls, which need to be mapped to HTML form elements and enhanced with HTMX/Alpine.js for dynamic behavior.

*   **Filters/Search Area:**
    *   `DrpType` (DropDownList): Select Type (Category/WOItems). `AutoPostBack` implies immediate server interaction.
    *   `DrpCategory1` (DropDownList): Select Category. `AutoPostBack`. Dynamically populated.
    *   `DrpSearchCode` (DropDownList): Select Search Field (Item Code/Description/Location). `AutoPostBack`.
    *   `DropDownList3` (DropDownList): Select Location. `AutoPostBack`. Dynamically populated, visible only if `DrpSearchCode` is "Location".
    *   `txtSearchItemCode` (TextBox): Text input for Item Code/Description search. Visible/hidden based on `DrpSearchCode`.
    *   `btnSearch` (Button): Triggers search.
*   **Data Display:**
    *   `GridView2` (GridView): Displays item data.
        *   Columns: SN, Select (HyperLinkField to details), Category, ItemCode, Description, UOM, StockQty, Location, Excise, Import/Local, Open Bal Date, Opening Bal Qty.
        *   Hidden Columns: Id, PartNo, MinOrderQty, MinStockQty, Absolute, UOMConFact.
        *   Pagination (`AllowPaging=True`, `PageSize=20`).
        *   CSS class `yui-datatable-theme` suggests it was styled as a data table.

**Client-Side Behavior Conversion:**
The `AutoPostBack` property on dropdowns and the `onclick` on the search button indicate full page postbacks in ASP.NET. In Django, these will be replaced with HTMX requests (e.g., `hx-get`, `hx-post`) targeting specific parts of the page for partial updates. Alpine.js will be used for any local UI state management, such as showing/hiding elements. DataTables will handle the client-side sorting, filtering, and pagination of the main grid.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `masters`, to house this functionality.

#### 4.1 Models (`masters/models.py`)

We'll define `ItemMaster` and `CategoryMaster` models, ensuring they map to the existing database tables. A custom manager for `ItemMaster` will encapsulate the complex search logic from the ASP.NET `Fillgrid` method.

```python
from django.db import models
from django.db.models import Q
from django.utils.timezone import now # Assuming date is used for OpeningBalDate

class CategoryMaster(models.Model):
    """
    Maps to tblDG_Category_Master. Represents item categories.
    """
    cid = models.AutoField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category Master'
        verbose_name_plural = 'Category Masters'

    def __str__(self):
        return f"[{self.symbol}] - {self.cname}" if self.symbol else self.cname

class ItemMasterManager(models.Manager):
    """
    Custom manager for ItemMaster to encapsulate complex filtering logic
    from the original Fillgrid method.
    """
    def filter_items(self, comp_id, fin_year_id, item_type=None, category_id=None,
                     search_code=None, search_term=None, location_id=None):
        queryset = self.get_queryset()

        # Base filters from ASP.NET Fillgrid
        queryset = queryset.filter(
            Q(compid=comp_id) & Q(finyearid__lte=fin_year_id)
        )

        if item_type == 'Category':
            if category_id and category_id != 'Select':
                queryset = queryset.filter(cid=category_id)

            if search_code and search_code != 'Select':
                if search_code == 'tblDG_Item_Master.ItemCode' and search_term:
                    queryset = queryset.filter(itemcode__istartswith=search_term)
                elif search_code == 'tblDG_Item_Master.ManfDesc' and search_term:
                    queryset = queryset.filter(manfdesc__icontains=search_term)
                elif search_code == 'tblDG_Item_Master.Location' and location_id and location_id != 'Select':
                    queryset = queryset.filter(location=location_id)
            elif search_term: # Case for sd == "Select" && B == "Select" && s != string.Empty
                queryset = queryset.filter(manfdesc__icontains=search_term)

        elif item_type == 'WOItems':
            if search_code and search_code != 'Select':
                if search_code == 'tblDG_Item_Master.ItemCode' and search_term:
                    queryset = queryset.filter(itemcode__icontains=search_term)
                elif search_code == 'tblDG_Item_Master.ManfDesc' and search_term:
                    queryset = queryset.filter(manfdesc__icontains=search_term)
            elif search_term: # Case for B == "Select" && s != string.Empty
                queryset = queryset.filter(manfdesc__icontains=search_term)
        
        # Note: The original ASP.NET code for "WOItems" and "Select" Type
        # seems to lack explicit CompId/FinYearId filtering within its conditional logic.
        # However, the initial filter applies globally, so we'll maintain that.
        
        return queryset

class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master. Represents individual items.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    cid = models.ForeignKey(CategoryMaster, on_delete=models.SET_NULL, db_column='CId', blank=True, null=True,
                            related_name='items') # Changed from IntegerField to ForeignKey
    partno = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manfdesc = models.TextField(db_column='ManfDesc', blank=True, null=True)
    uombasic = models.CharField(db_column='UOMBasic', max_length=50, blank=True, null=True)
    minorderqty = models.DecimalField(db_column='MinOrderQty', max_digits=18, decimal_places=3, blank=True, null=True)
    minstockqty = models.DecimalField(db_column='MinStockQty', max_digits=18, decimal_places=3, blank=True, null=True)
    stockqty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)
    absolute = models.BooleanField(db_column='Absolute', blank=True, null=True) # Assuming 0/1 maps to bool
    excise = models.CharField(db_column='Excise', max_length=50, blank=True, null=True)
    importlocal = models.CharField(db_column='ImportLocal', max_length=50, blank=True, null=True)
    openingbaldate = models.DateField(db_column='OpeningBalDate', blank=True, null=True)
    openingbalqty = models.DecimalField(db_column='OpeningBalQty', max_digits=18, decimal_places=3, blank=True, null=True)
    uomconfact = models.DecimalField(db_column='UOMConFact', max_digits=18, decimal_places=3, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = ItemMasterManager() # Assign the custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.itemcode or self.manfdesc or f"Item {self.id}"

    # Example of a business logic method (Fat Model)
    def calculate_reorder_level(self):
        """Calculates a potential reorder level based on stock and min order/stock quantities."""
        if self.minorderqty is not None and self.minstockqty is not None and self.stockqty is not None:
            # Simple example: if current stock is below min stock, suggest reorder based on min order qty
            if self.stockqty < self.minstockqty:
                return self.minorderqty
        return 0 # No reorder needed or cannot calculate
```

#### 4.2 Forms (`masters/forms.py`)

We'll create an `ItemMasterForm` for the standard CRUD operations and a separate `ItemMasterSearchForm` for the dynamic search filters, making it easier to manage the complex filtering UI.

```python
from django import forms
from .models import ItemMaster, CategoryMaster

class ItemMasterForm(forms.ModelForm):
    """
    Form for creating and updating ItemMaster records.
    """
    class Meta:
        model = ItemMaster
        fields = [
            'cid', 'partno', 'itemcode', 'manfdesc', 'uombasic',
            'minorderqty', 'minstockqty', 'stockqty', 'location',
            'absolute', 'excise', 'importlocal', 'openingbaldate',
            'openingbalqty', 'uomconfact'
        ]
        widgets = {
            'cid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'partno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'itemcode': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manfdesc': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'uombasic': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'minorderqty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'minstockqty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'stockqty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'excise': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'importlocal': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'openingbaldate': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'openingbalqty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'uomconfact': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'absolute': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }

    # You could add custom validation or clean methods here if needed
    # def clean_some_field(self):
    #     data = self.cleaned_data['some_field']
    #     # Add validation logic
    #     return data

class ItemMasterSearchForm(forms.Form):
    """
    Form for the item master search filters, designed for HTMX dynamic updates.
    """
    TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('Select', 'Select'),
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-post': 'itemmaster/search_form_elements/', # Endpoint to re-render partial form fields
            'hx-target': '#search-form-dynamic-fields',
            'hx-swap': 'outerHTML',
            'hx-include': '#id_category1, #id_search_code, #id_search_term, #id_location' # Include other form fields to retain state
        }),
        label="Type"
    )
    category1 = forms.ChoiceField(
        choices=[('Select', 'Select')], # Will be dynamically populated
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-post': 'itemmaster/search_form_elements/', # Re-render for updates/grid refresh
            'hx-target': '#search-form-dynamic-fields',
            'hx-swap': 'outerHTML',
            'hx-include': '#id_type, #id_search_code, #id_search_term, #id_location',
        }),
        label="Category"
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 w-full',
            'hx-post': 'itemmaster/search_form_elements/', # Re-render for updates/grid refresh
            'hx-target': '#search-form-dynamic-fields',
            'hx-swap': 'outerHTML',
            'hx-include': '#id_type, #id_category1, #id_search_term, #id_location'
        }),
        label="Search By"
    )
    search_term = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full',
            'placeholder': 'Enter search term'
        }),
        label="Search Term"
    )
    location = forms.ChoiceField(
        choices=[('Select', 'Select')], # Will be dynamically populated
        required=False,
        widget=forms.Select(attrs={
            'class': 'box3 w-full'
        }),
        label="Location"
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)

        # Initial population of category and location dropdowns if needed on first load
        # For dynamic updates, these will be handled by separate HTMX endpoints
        if comp_id:
            # Populate category dropdown
            category_choices = [('Select', 'Select')] + [
                (str(c.cid), f"[{c.symbol}] - {c.cname}")
                for c in CategoryMaster.objects.filter(compid=comp_id).order_by('cname')
            ]
            self.fields['category1'].choices = category_choices

            # Simulate fun.drpLocat - assuming 'Location' field in ItemMaster itself contains distinct values
            # Or this would be a separate 'LocationMaster' model
            location_choices = [('Select', 'Select')] + [
                (loc, loc) for loc in ItemMaster.objects.values_list('location', flat=True).distinct().exclude(location__isnull=True).exclude(location='')
            ]
            self.fields['location'].choices = location_choices

        # Set initial visibility based on selected values (mimicking ASP.NET Page_Load)
        # This will be handled more robustly by Alpine.js and HTMX in templates
        self.fields['category1'].widget.attrs['x-show'] = "type === 'Category'"
        self.fields['location'].widget.attrs['x-show'] = "type === 'Category' && searchCode === 'tblDG_Item_Master.Location'"
        self.fields['search_term'].widget.attrs['x-show'] = "!(type === 'Category' && searchCode === 'tblDG_Item_Master.Location')"
        self.fields['search_code'].widget.attrs['x-show'] = "type !== 'Select'"

    # Custom validation for search form if needed
    def clean(self):
        cleaned_data = super().clean()
        item_type = cleaned_data.get('type')
        search_code = cleaned_data.get('search_code')
        search_term = cleaned_data.get('search_term')
        location_id = cleaned_data.get('location')

        # Add any cross-field validation if necessary for search parameters
        return cleaned_data
```

#### 4.3 Views (`masters/views.py`)

Views will be thin, delegating complex logic to the `ItemMaster` model's custom manager. HTMX endpoints will handle partial updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from .models import ItemMaster, CategoryMaster
from .forms import ItemMasterForm, ItemMasterSearchForm

# Assume CompId and FinYearId are accessible, e.g., from user session or config.
# For demonstration, using dummy values. In a real app, integrate with auth/session.
# COMP_ID = 1 # Example
# FIN_YEAR_ID = 2023 # Example

class ItemMasterListView(TemplateView):
    """
    Main view for displaying the Item Master list and search form.
    It orchestrates the initial load of the search form and the HTMX-powered table.
    """
    template_name = 'masters/itemmaster/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass dummy comp_id/fin_year_id for form initialization.
        # In a real app, get these from request.user or session.
        comp_id = 1 # self.request.session.get('compid') or self.request.user.profile.compid
        context['search_form'] = ItemMasterSearchForm(comp_id=comp_id)
        return context

class ItemMasterTablePartialView(ListView):
    """
    HTMX endpoint to render just the item master table based on search filters.
    This replaces the Fillgrid functionality from ASP.NET.
    """
    model = ItemMaster
    template_name = 'masters/itemmaster/_itemmaster_table.html'
    context_object_name = 'item_masters'
    paginate_by = 20 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Extract search parameters from GET or POST
        # For simplicity, we assume GET parameters for initial form submission for table refresh
        # In a real app, combine GET/POST based on how form submits.
        form = ItemMasterSearchForm(self.request.GET) # For initial load or button click
        
        comp_id = 1 # Get from self.request.session.get('compid') or similar
        fin_year_id = 2023 # Get from self.request.session.get('finyearid') or similar

        if form.is_valid():
            data = form.cleaned_data
            return ItemMaster.objects.filter_items(
                comp_id=comp_id,
                fin_year_id=fin_year_id,
                item_type=data.get('type'),
                category_id=data.get('category1'),
                search_code=data.get('search_code'),
                search_term=data.get('search_term'),
                location_id=data.get('location')
            )
        else:
            # If form is not valid or no search parameters, return an initial unfiltered queryset
            # Or raise an error/return empty based on desired behavior
            return ItemMaster.objects.filter(compid=comp_id, finyearid__lte=fin_year_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add pagination details if needed for DataTables, though DataTables handles it client-side
        return context

class ItemMasterSearchFormElementsView(TemplateView):
    """
    HTMX endpoint to re-render the search form elements (dropdowns, textboxes)
    based on changes in 'Type' or 'Search By' dropdowns.
    This replaces the dynamic visibility logic in ASP.NET code-behind.
    """
    template_name = 'masters/itemmaster/_itemmaster_search_form.html'

    def post(self, request, *args, **kwargs):
        # Re-initialize form with POST data to get selected values
        # comp_id for populating dropdowns
        comp_id = 1 # Get from request.user or session
        form = ItemMasterSearchForm(request.POST, comp_id=comp_id)
        
        # Render the partial form with updated choices/visibility attributes
        context = {'search_form': form}
        html = render_to_string(self.template_name, context, request=request)
        return HttpResponse(html)

class ItemMasterCreateView(CreateView):
    """
    View for adding a new Item Master.
    """
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'masters/itemmaster/_itemmaster_form.html' # Partial for modal
    success_url = reverse_lazy('itemmaster_list') # Redirect not used directly with HTMX, but good practice

    def form_valid(self, form):
        # Set default values if needed, e.g., compid, finyearid
        form.instance.compid = 1 # Example: self.request.user.profile.compid
        form.instance.finyearid = 2023 # Example: self.request.user.profile.finyearid
        response = super().form_valid(form)
        messages.success(self.request, 'Item Master added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX nothing to swap
                headers={
                    'HX-Trigger': 'refreshItemMasterList' # Custom event to trigger table refresh
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If form is invalid, re-render the form itself with errors
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response


class ItemMasterUpdateView(UpdateView):
    """
    View for editing an existing Item Master.
    """
    model = ItemMaster
    form_class = ItemMasterForm
    template_name = 'masters/itemmaster/_itemmaster_form.html' # Partial for modal
    success_url = reverse_lazy('itemmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response

class ItemMasterDeleteView(DeleteView):
    """
    View for deleting an Item Master.
    """
    model = ItemMaster
    template_name = 'masters/itemmaster/_itemmaster_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('itemmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemMasterList'
                }
            )
        return response

```

#### 4.4 Templates (`masters/templates/masters/itemmaster/`)

We'll define the main list template and partials for the search form, table, and CRUD operations, adhering to HTMX and Alpine.js principles.

**`list.html`**
This is the main page that hosts the search form and the DataTables container.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Item Master - Edit</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'itemmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .block to #modal then add .opacity-100 to #modal then remove .hidden from #modal">
            Add New Item
        </button>
    </div>

    <!-- Search/Filter Area -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-8" x-data="{ 
            type: '{{ search_form.type.value|default:'Select' }}',
            searchCode: '{{ search_form.search_code.value|default:'Select' }}',
            init() {
                this.$watch('type', value => {
                    // Reset category and search code when type changes (if needed, or let HTMX handle)
                    // For now, assume HTMX re-renders the dynamic part
                });
                this.$watch('searchCode', value => {
                    // No explicit reset needed here, Alpine.js handles show/hide
                });
            }
        }">
        <form hx-get="{% url 'itemmaster_table' %}" 
              hx-target="#itemmaster-table-container" 
              hx-swap="innerHTML"
              hx-indicator="#table-loading-indicator"
              id="itemmaster-search-form">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" id="search-form-dynamic-fields">
                {# This section will be replaced by HTMX when dropdowns change #}
                {% include 'masters/itemmaster/_itemmaster_search_form.html' %}
            </div>
            <div class="mt-6 text-right">
                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Loading Indicator for DataTables -->
    <div id="table-loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading Items...</p>
    </div>

    <!-- DataTables Container -->
    <div id="itemmaster-table-container"
         hx-trigger="load, refreshItemMasterList from:body"
         hx-get="{% url 'itemmaster_table' %}?{{ request.GET.urlencode }}" {# Pass initial query params #}
         hx-swap="innerHTML">
        <!-- Table will be loaded here via HTMX -->
    </div>
    
    <!-- Modal for forms (Create, Edit, Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden z-50 flex items-center justify-center transition-opacity duration-300 ease-out opacity-0"
         _="on click if event.target.id == 'modal' remove .opacity-100 from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 md:mx-auto relative transform transition-transform duration-300 ease-out scale-95"
             _="on modal-open from #modal add .scale-100 then remove .scale-95">
            <!-- Form/Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('itemMasterSearch', () => ({
            type: '',
            searchCode: '',
            init() {
                // Initialize based on current form values if available
                this.type = document.getElementById('id_type').value || 'Select';
                this.searchCode = document.getElementById('id_search_code').value || 'Select';
                
                this.$watch('type', value => {
                    // If type changes, re-submit the form to update dependent fields
                    // HTMX will handle updating the partial form and then triggering a table refresh
                    if (this.$el.id === 'id_type') { // Check if this is the element triggering change
                        htmx.trigger(document.getElementById('itemmaster-search-form'), 'submit');
                    }
                });
                this.$watch('searchCode', value => {
                     if (this.$el.id === 'id_search_code') {
                         htmx.trigger(document.getElementById('itemmaster-search-form'), 'submit');
                     }
                });

                // Listen for custom HX-Trigger events to refresh the table without direct form submission
                document.body.addEventListener('refreshItemMasterList', (evt) => {
                    console.log('refreshItemMasterList triggered');
                    htmx.trigger(document.getElementById('itemmaster-table-container'), 'load');
                });
            }
        }));
    });

    // Function to re-initialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'itemmaster-table-container' || $(event.target).has('#itemmasterTable').length) {
            // Destroy any existing DataTable instance first to prevent re-initialization issues
            if ($.fn.DataTable.isDataTable('#itemmasterTable')) {
                $('#itemmasterTable').DataTable().destroy();
            }
            // Initialize DataTable
            $('#itemmasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Filter records:",
                    "lengthMenu": "Show _MENU_ entries"
                },
                "responsive": true
            });
        }
        // Handle modal opening after HTMX swap
        if (event.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            modal.classList.remove('hidden', 'opacity-0');
            modal.classList.add('block', 'opacity-100');
            // Trigger an Alpine.js event for the modal content if needed
            modal.dispatchEvent(new CustomEvent('modal-open'));
        }
    });

    // Close modal when form is successfully submitted via HTMX (status 204)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('opacity-100');
                modal.classList.add('opacity-0', 'hidden');
                document.getElementById('modalContent').innerHTML = ''; // Clear modal content
            }
        }
    });

    // Close modal on 'Cancel' button click (for forms)
    document.body.addEventListener('click', function(event) {
        if (event.target.matches('[data-modal-close]')) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('opacity-100');
                modal.classList.add('opacity-0', 'hidden');
                document.getElementById('modalContent').innerHTML = ''; // Clear modal content
            }
        }
    });

</script>
{% endblock %}
```

**`_itemmaster_search_form.html`**
This partial renders the dynamic parts of the search form. It's designed to be swapped by HTMX.

```html
{# This partial is designed to be dynamically updated via HTMX #}
{# It should be included in list.html and also rendered by ItemMasterSearchFormElementsView #}

{% load tailwind_filters %}

<div class="col-span-1" x-data="itemMasterSearch" x-init="init()">
    <label for="{{ search_form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type</label>
    {{ search_form.type }}
</div>

<div class="col-span-1" x-data="itemMasterSearch" x-init="init()">
    <label for="{{ search_form.category1.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
    <div x-show="type === 'Category'" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100">
        {{ search_form.category1 }}
    </div>
    <div x-show="type !== 'Category'" class="mt-2 text-sm text-gray-500">
        Not applicable for selected type.
    </div>
</div>

<div class="col-span-1" x-data="itemMasterSearch" x-init="init()">
    <label for="{{ search_form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
    <div x-show="type !== 'Select'" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100">
        {{ search_form.search_code }}
    </div>
    <div x-show="type === 'Select'" class="mt-2 text-sm text-gray-500">
        Please select a type first.
    </div>
</div>

<div class="col-span-1" x-data="itemMasterSearch" x-init="init()">
    <label for="{{ search_form.search_term.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Value</label>
    <div x-show="type !== 'Select' && searchCode !== 'Select' && !(type === 'Category' && searchCode === 'tblDG_Item_Master.Location')" 
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100">
        {{ search_form.search_term }}
    </div>
    <div x-show="type !== 'Select' && searchCode !== 'Select' && (type === 'Category' && searchCode === 'tblDG_Item_Master.Location')"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100">
        {{ search_form.location }} {# Use the location dropdown here #}
    </div>
    <div x-show="type === 'Select' || searchCode === 'Select'" class="mt-2 text-sm text-gray-500">
        Select search parameters.
    </div>
</div>
```

**`_itemmaster_table.html`**
This partial contains the DataTables structure and is loaded dynamically.

```html
<table id="itemmasterTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Category</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Stock Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Location</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Excise</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Import/Local</th>
            <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Open Bal Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Opening Bal Qty</th>
            <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% if item_masters %}
            {% for obj in item_masters %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.cid.cname|default:"-" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.itemcode|default:"-" }}</td>
                <td class="py-2 px-4 text-sm text-gray-800">{{ obj.manfdesc|default:"-" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.uombasic|default:"-" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ obj.stockqty|default:"0.00" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.location|default:"-" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.excise|default:"-" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.importlocal|default:"-" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ obj.openingbaldate|date:"Y-m-d"|default:"-" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ obj.openingbalqty|default:"0.00" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-800">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'itemmaster_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                        hx-get="{% url 'itemmaster_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .block to #modal then add .opacity-100 to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="12" class="py-4 px-4 text-center text-lg text-red-700 font-semibold">
                No data to display!
            </td>
        </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // DataTables initialization handled by the `htmx:afterSwap` event listener in list.html
    // This script block should effectively be empty or contain only code that
    // specifically needs to run *after* this partial is loaded, if not covered by the global listener.
    // For now, it's empty as the main script in list.html handles the DataTables re-init.
</script>
```

**`_itemmaster_form.html`**
Partial for create/update forms, loaded into the modal.

```html
{% load tailwind_filters %}

<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Item Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-xs mt-1 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out"
                data-modal-close> {# Custom attribute for Alpine.js/JS to close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_itemmaster_confirm_delete.html`**
Partial for delete confirmation, loaded into the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete Item Master: <strong>{{ object.itemcode }} - {{ object.manfdesc }}</strong>?</p>
    
    <form hx-delete="{% url 'itemmaster_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out"
                data-modal-close>
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`masters/urls.py`)

Define URL patterns for all views, including HTMX partials.

```python
from django.urls import path
from .views import (
    ItemMasterListView, ItemMasterTablePartialView, ItemMasterSearchFormElementsView,
    ItemMasterCreateView, ItemMasterUpdateView, ItemMasterDeleteView
)

urlpatterns = [
    # Main list page
    path('itemmaster/', ItemMasterListView.as_view(), name='itemmaster_list'),
    
    # HTMX endpoints for dynamic content
    path('itemmaster/table/', ItemMasterTablePartialView.as_view(), name='itemmaster_table'),
    path('itemmaster/search_form_elements/', ItemMasterSearchFormElementsView.as_view(), name='itemmaster_search_form_elements'),

    # CRUD operations (loaded into modal via HTMX)
    path('itemmaster/add/', ItemMasterCreateView.as_view(), name='itemmaster_add'),
    path('itemmaster/edit/<int:pk>/', ItemMasterUpdateView.as_view(), name='itemmaster_edit'),
    path('itemmaster/delete/<int:pk>/', ItemMasterDeleteView.as_view(), name='itemmaster_delete'),
]
```

#### 4.6 Tests (`masters/tests.py`)

Comprehensive unit tests for models and integration tests for views are crucial for ensuring the migrated application functions correctly and maintains quality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import ItemMaster, CategoryMaster
from .forms import ItemMasterForm, ItemMasterSearchForm
from django.db.utils import IntegrityError
from django.db import connection

class CategoryMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.category1 = CategoryMaster.objects.create(
            cid=1, # Manually set PK since it's AutoField but managed=False
            symbol='CAT-A',
            cname='Category A',
            compid=100
        )
        cls.category2 = CategoryMaster.objects.create(
            cid=2,
            symbol='CAT-B',
            cname='Category B',
            compid=100
        )

    def test_category_creation(self):
        self.assertEqual(self.category1.symbol, 'CAT-A')
        self.assertEqual(self.category1.cname, 'Category A')
        self.assertEqual(self.category1.compid, 100)

    def test_str_method(self):
        self.assertEqual(str(self.category1), '[CAT-A] - Category A')
        
    def test_db_table(self):
        self.assertEqual(CategoryMaster._meta.db_table, 'tblDG_Category_Master')
        self.assertFalse(CategoryMaster._meta.managed)

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = CategoryMaster.objects.create(
            cid=1, symbol='TST', cname='Test Category', compid=1
        )
        cls.item1 = ItemMaster.objects.create(
            id=1, # Manually set PK
            cid=cls.category,
            itemcode='ITEM001',
            manfdesc='Test Item Description 1',
            uombasic='PCS',
            stockqty=100.50,
            location='Warehouse A',
            compid=1,
            finyearid=2023,
            minorderqty=10,
            minstockqty=20
        )
        cls.item2 = ItemMaster.objects.create(
            id=2, # Manually set PK
            cid=cls.category,
            itemcode='ITEM002',
            manfdesc='Another Item',
            uombasic='KG',
            stockqty=50.00,
            location='Warehouse B',
            compid=1,
            finyearid=2023,
            minorderqty=5,
            minstockqty=10
        )
        cls.item3 = ItemMaster.objects.create(
            id=3, # Manually set PK
            itemcode='ITEM003',
            manfdesc='WO Item',
            uombasic='MTR',
            stockqty=200.00,
            location='Shop Floor',
            compid=1,
            finyearid=2023,
            cid=None # WOItems might not have category
        )
        
    def test_item_creation(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.itemcode, 'ITEM001')
        self.assertEqual(item.cid, self.category)
        self.assertEqual(item.stockqty, 100.50)

    def test_str_method(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(str(item), 'ITEM001')
        item_no_code = ItemMaster.objects.create(id=4, manfdesc='No Code Item', compid=1, finyearid=2023)
        self.assertEqual(str(item_no_code), 'No Code Item')

    def test_db_table(self):
        self.assertEqual(ItemMaster._meta.db_table, 'tblDG_Item_Master')
        self.assertFalse(ItemMaster._meta.managed)

    def test_calculate_reorder_level_below_min_stock(self):
        item = ItemMaster.objects.create(
            id=5, itemcode='REORDER1', manfdesc='Reorder Test', uombasic='PCS',
            stockqty=15, minstockqty=20, minorderqty=50, compid=1, finyearid=2023
        )
        self.assertEqual(item.calculate_reorder_level(), 50)

    def test_calculate_reorder_level_above_min_stock(self):
        item = ItemMaster.objects.create(
            id=6, itemcode='REORDER2', manfdesc='Reorder Test 2', uombasic='PCS',
            stockqty=25, minstockqty=20, minorderqty=50, compid=1, finyearid=2023
        )
        self.assertEqual(item.calculate_reorder_level(), 0)

    def test_filter_items_by_category_and_itemcode(self):
        items = ItemMaster.objects.filter_items(
            comp_id=1, fin_year_id=2023, item_type='Category', category_id=self.category.cid,
            search_code='tblDG_Item_Master.ItemCode', search_term='ITEM001'
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().itemcode, 'ITEM001')

    def test_filter_items_by_category_and_description(self):
        items = ItemMaster.objects.filter_items(
            comp_id=1, fin_year_id=2023, item_type='Category', category_id=self.category.cid,
            search_code='tblDG_Item_Master.ManfDesc', search_term='another'
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().itemcode, 'ITEM002')

    def test_filter_items_by_category_and_location(self):
        items = ItemMaster.objects.filter_items(
            comp_id=1, fin_year_id=2023, item_type='Category', category_id=self.category.cid,
            search_code='tblDG_Item_Master.Location', location_id='Warehouse A'
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().itemcode, 'ITEM001')

    def test_filter_items_by_woitems_and_description(self):
        items = ItemMaster.objects.filter_items(
            comp_id=1, fin_year_id=2023, item_type='WOItems',
            search_code='tblDG_Item_Master.ManfDesc', search_term='WO'
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().itemcode, 'ITEM003')

    def test_filter_items_no_filters(self):
        # Initial filter applied by manager: compid and finyearid
        items = ItemMaster.objects.filter_items(comp_id=1, fin_year_id=2023)
        self.assertEqual(items.count(), 3) # All 3 items match comp_id and fin_year_id

    def test_filter_items_type_select_with_search_term(self):
        # This corresponds to: sd == "Select" && B == "Select" && s != string.Empty
        # in ASP.NET, which applied ManfDesc like filter.
        items = ItemMaster.objects.filter_items(
            comp_id=1, fin_year_id=2023, item_type='Category',
            search_code='Select', search_term='Item' # 'Category' type, but no Category selected
        )
        # Should match ITEM001 and Another Item (ITEM002) if 'Item' is in their description
        self.assertEqual(items.count(), 2) # ITEM001, Another Item. ITEM003 has "WO Item"

    def test_filter_items_woitems_with_empty_search_code_and_term(self):
        # This corresponds to: B == "Select" && s != string.Empty
        items = ItemMaster.objects.filter_items(
            comp_id=1, fin_year_id=2023, item_type='WOItems',
            search_code='Select', search_term='Another'
        )
        self.assertEqual(items.count(), 1) # Should match ITEM002, assuming it was marked as WOItem type if applicable

class ItemMasterFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = CategoryMaster.objects.create(cid=1, symbol='TST', cname='Test Cat', compid=1)

    def test_form_valid_data(self):
        data = {
            'cid': self.category.cid,
            'itemcode': 'NEWITEM',
            'manfdesc': 'New test item',
            'uombasic': 'BOX',
            'stockqty': 50.00,
            'location': 'New Loc',
            'excise': 'EXC1',
            'importlocal': 'Local',
            'openingbaldate': '2023-01-01',
            'openingbalqty': 10.00,
            'absolute': True,
            'minorderqty': 5,
            'minstockqty': 10,
            'uomconfact': 1.0,
            'partno': 'PN123'
        }
        form = ItemMasterForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_form_invalid_data(self):
        data = {
            'itemcode': '', # Missing required field if itemcode was mandatory
            'manfdesc': 'Invalid item',
        }
        form = ItemMasterForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('itemcode', form.errors) # If itemcode was set to not null in model

class ItemMasterSearchFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = CategoryMaster.objects.create(cid=1, symbol='TST', cname='Test Cat', compid=1)
        # Create dummy locations from ItemMaster if not using a separate LocationMaster
        ItemMaster.objects.create(
            id=1, itemcode='ITEM1', manfdesc='Desc1', location='LocA', compid=1, finyearid=2023
        )
        ItemMaster.objects.create(
            id=2, itemcode='ITEM2', manfdesc='Desc2', location='LocB', compid=1, finyearid=2023
        )

    def test_form_initialization(self):
        form = ItemMasterSearchForm(comp_id=1)
        self.assertIn(('Select', 'Select'), form.fields['type'].choices)
        self.assertIn((str(self.category.cid), '[TST] - Test Cat'), form.fields['category1'].choices)
        self.assertIn(('LocA', 'LocA'), form.fields['location'].choices)

    def test_form_valid_search_data(self):
        data = {
            'type': 'Category',
            'category1': str(self.category.cid),
            'search_code': 'tblDG_Item_Master.ItemCode',
            'search_term': 'ITEM',
            'location': 'Select'
        }
        form = ItemMasterSearchForm(data=data, comp_id=1)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['type'], 'Category')

    def test_form_valid_location_search_data(self):
        data = {
            'type': 'Category',
            'category1': str(self.category.cid),
            'search_code': 'tblDG_Item_Master.Location',
            'location': 'LocA',
            'search_term': ''
        }
        form = ItemMasterSearchForm(data=data, comp_id=1)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['location'], 'LocA')

class ItemMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = CategoryMaster.objects.create(cid=1, symbol='TESTCAT', cname='Test Category', compid=1)
        cls.item = ItemMaster.objects.create(
            id=1,
            cid=cls.category,
            itemcode='TEST001',
            manfdesc='Test Item Description',
            uombasic='PCS',
            stockqty=100.00,
            location='Test Loc',
            compid=1,
            finyearid=2023
        )
        # Ensure distinct locations for testing location dropdown
        ItemMaster.objects.create(
            id=2, itemcode='TEST002', manfdesc='Another item', location='Another Loc', compid=1, finyearid=2023
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('itemmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/itemmaster/list.html')
        self.assertIsInstance(response.context['search_form'], ItemMasterSearchForm)

    def test_itemmaster_table_partial_view_get(self):
        response = self.client.get(reverse('itemmaster_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/itemmaster/_itemmaster_table.html')
        self.assertContains(response, 'TEST001')
        self.assertTrue('item_masters' in response.context)
        self.assertEqual(response.context['item_masters'].count(), 2) # Both items created

    def test_itemmaster_table_partial_view_search(self):
        # Test searching by item code
        response = self.client.get(reverse('itemmaster_table'), {
            'type': 'Category',
            'category1': str(self.category.cid),
            'search_code': 'tblDG_Item_Master.ItemCode',
            'search_term': 'TEST001',
            'location': 'Select' # Ensure this is 'Select' for non-location search
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TEST001')
        self.assertNotContains(response, 'TEST002')
        self.assertEqual(response.context['item_masters'].count(), 1)

        # Test searching by location
        response = self.client.get(reverse('itemmaster_table'), {
            'type': 'Category',
            'category1': str(self.category.cid),
            'search_code': 'tblDG_Item_Master.Location',
            'search_term': '', # Search term should be empty when location is selected
            'location': 'Test Loc'
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'TEST001')
        self.assertNotContains(response, 'TEST002')
        self.assertEqual(response.context['item_masters'].count(), 1)


    def test_itemmaster_search_form_elements_view_post(self):
        # Simulate selecting 'Category' type
        response = self.client.post(reverse('itemmaster_search_form_elements'), {'type': 'Category'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/itemmaster/_itemmaster_search_form.html')
        self.assertContains(response, 'id="id_category1"')
        self.assertContains(response, 'x-show="type === &#x27;Category&#x27;"') # Check Alpine.js attribute

        # Simulate selecting 'Location' search code when 'Category' type is active
        response = self.client.post(reverse('itemmaster_search_form_elements'), {
            'type': 'Category',
            'category1': str(self.category.cid),
            'search_code': 'tblDG_Item_Master.Location'
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'id="id_location"')
        self.assertContains(response, 'x-show="type === &#x27;Category&#x27; &amp;&amp; searchCode === &#x27;tblDG_Item_Master.Location&#x27;"')
        self.assertContains(response, 'x-show="!(type === &#x27;Category&#x27; &amp;&amp; searchCode === &#x27;tblDG_Item_Master.Location&#x27;)"')


    def test_create_view_get_htmx(self):
        response = self.client.get(reverse('itemmaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/itemmaster/_itemmaster_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Item Master')

    def test_create_view_post_htmx_valid(self):
        new_category = CategoryMaster.objects.create(cid=99, symbol='NEWC', cname='New Category', compid=1)
        data = {
            'cid': new_category.cid,
            'itemcode': 'NEWITEM',
            'manfdesc': 'Brand new item',
            'uombasic': 'EA',
            'stockqty': 50.00,
            'location': 'New Storage',
            'absolute': False,
            'excise': 'N/A',
            'importlocal': 'Local',
            'openingbaldate': '2023-01-15',
            'openingbalqty': 0.00,
            'uomconfact': 1.0,
        }
        response = self.client.post(reverse('itemmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')
        self.assertTrue(ItemMaster.objects.filter(itemcode='NEWITEM').exists())

    def test_create_view_post_htmx_invalid(self):
        data = {
            'itemcode': '', # Invalid data
            'manfdesc': 'Invalid item',
        }
        response = self.client.post(reverse('itemmaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Add Item Master')
        self.assertContains(response, 'This field is required') # Check for form errors

    def test_update_view_get_htmx(self):
        response = self.client.get(reverse('itemmaster_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/itemmaster/_itemmaster_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Item Master')
        self.assertContains(response, 'TEST001')

    def test_update_view_post_htmx_valid(self):
        updated_description = 'Updated Description'
        data = {
            'cid': self.item.cid.cid, # Pass PK of existing category
            'itemcode': self.item.itemcode,
            'manfdesc': updated_description,
            'uombasic': self.item.uombasic,
            'stockqty': self.item.stockqty,
            'location': self.item.location,
            'absolute': self.item.absolute,
            'excise': self.item.excise,
            'importlocal': self.item.importlocal,
            'openingbaldate': self.item.openingbaldate.strftime('%Y-%m-%d') if self.item.openingbaldate else '',
            'openingbalqty': self.item.openingbalqty,
            'uomconfact': self.item.uomconfact,
        }
        response = self.client.post(reverse('itemmaster_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')
        self.item.refresh_from_db()
        self.assertEqual(self.item.manfdesc, updated_description)

    def test_delete_view_get_htmx(self):
        response = self.client.get(reverse('itemmaster_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'masters/itemmaster/_itemmaster_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'TEST001')

    def test_delete_view_post_htmx(self):
        # Create a new item to delete so it doesn't affect other tests
        item_to_delete = ItemMaster.objects.create(
            id=99, itemcode='DELITEM', manfdesc='Item to be deleted', compid=1, finyearid=2023
        )
        response = self.client.delete(reverse('itemmaster_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshItemMasterList')
        self.assertFalse(ItemMaster.objects.filter(pk=item_to_delete.pk).exists())

```

### Step 5: HTMX and Alpine.js Integration

The HTMX and Alpine.js integration is embedded directly within the templates (`list.html`, `_itemmaster_search_form.html`) and the Django views (`ItemMasterTablePartialView`, `ItemMasterSearchFormElementsView`, and CRUD views).

*   **Dynamic Search Form:**
    *   The `type` and `search_code` dropdowns in `_itemmaster_search_form.html` have `hx-post` attributes targeting `itemmaster/search_form_elements/`. When a selection changes, HTMX sends the form data to the server.
    *   `ItemMasterSearchFormElementsView` receives this POST request, re-initializes the `ItemMasterSearchForm` with the new data, and renders `_itemmaster_search_form.html` again. This partial replaces the old one on the page, dynamically updating the available choices and visible fields based on the new selections (e.g., showing/hiding Category, Location, or Search Term fields using Alpine.js `x-show` attributes).
    *   The `hx-include` attribute ensures that all other form fields are sent with the request, maintaining their state across partial updates.
*   **Table Refresh:**
    *   The main search button (`btnSearch`) uses `hx-get` to trigger `itemmaster/table/`. This loads *only* the table content into the `#itemmaster-table-container` div, preventing a full page refresh.
    *   The `#itemmaster-table-container` itself has `hx-trigger="load, refreshItemMasterList from:body"`. This means it will load its content on initial page load and whenever a custom HTMX event `refreshItemMasterList` is triggered from anywhere on the page (e.g., after a successful create, update, or delete).
    *   The `htmx:afterSwap` event listener in `list.html` ensures that DataTables is re-initialized *after* new table content is loaded via HTMX, correctly applying its functionality to the new DOM.
*   **Modals for CRUD:**
    *   `Add New Item`, `Edit`, and `Delete` buttons use `hx-get` to fetch the respective `_itemmaster_form.html` or `_itemmaster_confirm_delete.html` partials.
    *   `hx-target="#modalContent"` directs the loaded partial into the modal's content area.
    *   Alpine.js `x-data` and `x-show` (with transitions) on the `#modal` div manage its visibility and animations.
    *   `hx-post` for form submissions within the modal (e.g., `_itemmaster_form.html`) sends data. On successful submission (handled by Django views returning `HttpResponse(status=204)`), an `HX-Trigger: refreshItemMasterList` header is sent. This event then refreshes the main item list without user interaction.
    *   Error handling for forms (e.g., `form_invalid` in `CreateView`/`UpdateView`) re-renders the form *with errors* back into the modal, allowing immediate correction without closing the modal.
    *   A generic `data-modal-close` attribute combined with a JavaScript event listener helps close the modal from within its content.

This systematic approach ensures that the application provides a highly responsive user experience, while keeping the backend logic clear, testable, and maintainable following modern Django best practices.