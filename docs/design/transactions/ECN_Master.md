## ASP.NET to Django Conversion Script: ECN Master Page Modernization

This modernization plan outlines the strategic transition of your legacy ASP.NET ECN Master page to a robust, scalable, and maintainable Django-based solution. Our approach prioritizes automation and leverages modern web technologies to deliver a superior user experience with minimal manual coding.

The core business value of this modernization lies in:
1.  **Enhanced Maintainability:** Django's structured framework and ORM reduce development complexity and make future updates easier.
2.  **Improved Performance:** Efficient data handling and partial updates via HTMX provide a snappier user experience.
3.  **Modern User Interface:** Integration with DataTables, HTMX, and Alpine.js delivers dynamic, interactive interfaces without full page reloads.
4.  **Reduced Technical Debt:** Moving away from legacy ASP.NET WebForms and server-side controls simplifies the architecture.
5.  **Scalability:** Django's design patterns and Python's ecosystem provide a solid foundation for growth.

This plan is structured to be actionable via conversational AI guidance, breaking down the migration into clear, manageable steps focusing on Django application file generation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with multiple tables to manage ECN (Engineering Change Notice) processes:
- `tblDG_ECN_Reason`: Used for listing predefined ECN reasons.
- `tblDG_BOMItem_Temp`: A temporary table for BOM items, acting as a header for `ECN_Master_Temp`.
- `tblDG_ECN_Master_Temp`: A temporary header table for ECNs, linking to BOM items.
- `tblDG_ECN_Details_Temp`: A temporary detail table holding specific ECN reasons and remarks for an ECN.

**Identified Schema:**

*   **Table Name:** `tblDG_ECN_Reason`
    *   **Purpose:** Stores predefined reasons for ECNs.
    *   **Columns:**
        *   `Id` (Primary Key, Integer)
        *   `Types` (String, e.g., "Description")
        *   `CompId` (Integer, Company ID)

*   **Table Name:** `tblDG_BOMItem_Temp`
    *   **Purpose:** Temporary Bill of Material item.
    *   **Columns:**
        *   `Id` (Primary Key, Integer)
        *   `CompId` (Integer)
        *   `SessionId` (String)
        *   `WONo` (String, Work Order Number)
        *   `ItemId` (Integer)
        *   `Qty` (Float/Double)
        *   `ChildId` (Integer)
        *   `ECNFlag` (String, typically '1')

*   **Table Name:** `tblDG_ECN_Master_Temp`
    *   **Purpose:** Temporary ECN header, linking to a `BOMItem_Temp`.
    *   **Columns:**
        *   `Id` (Primary Key, Integer)
        *   `MId` (Integer, Foreign Key to `tblDG_BOMItem_Temp.Id`)
        *   `SysDate` (String, System Date in a specific format)
        *   `SysTime` (String, System Time in a specific format)
        *   `CompId` (Integer)
        *   `FinYearId` (Integer, Financial Year ID)
        *   `SessionId` (String)
        *   `ItemId` (Integer)
        *   `WONo` (String)
        *   `PId` (Integer, Parent ID)
        *   `CId` (Integer, Child ID)

*   **Table Name:** `tblDG_ECN_Details_Temp`
    *   **Purpose:** Temporary ECN details, linking to an `ECN_Master_Temp` and a specific `ECN_Reason`.
    *   **Columns:**
        *   `MId` (Integer, Foreign Key to `tblDG_ECN_Master_Temp.Id`)
        *   `ECNReason` (Integer, Foreign Key to `tblDG_ECN_Reason.Id`)
        *   `Remarks` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core business logic.

**Analysis:**
The ASP.NET page performs the following:

*   **Read (Display):**
    *   Populates a `GridView` by selecting all records from `tblDG_ECN_Reason` where `CompId` matches the session `CompId`. This is a read operation for displaying master data.

*   **Create (Transactional Insert):**
    *   Triggered by the "Insert" button within the `GridView` footer.
    *   This is a multi-step transaction involving three temporary tables:
        1.  **Insert into `tblDG_BOMItem_Temp`**: A new record is created with `CompId`, `SessionId`, `WONo`, `ItemId`, `Qty`, `ChildId`, and `ECNFlag='1'`.
        2.  **Insert into `tblDG_ECN_Master_Temp`**: A new ECN header record is created, linked to the newly created `tblDG_BOMItem_Temp` (via `MId`). It also includes `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `ItemId`, `WONo`, `PId`, `CId`.
        3.  **Insert into `tblDG_ECN_Details_Temp` (Multiple)**: For each `ECN_Reason` row that was checked in the `GridView`, a new detail record is inserted. Each detail record links to the `tblDG_ECN_Master_Temp` (via `MId`), the selected `ECN_Reason.Id`, and the corresponding `Remarks` entered in the `TextBox` for that row.
    *   Session and Query String parameters (`WONo`, `ItemId`, `AssblyNo`, `childId`, `PId`, `Qty`, `CompId`, `FinYearId`, `SessionId`, `username`) are crucial inputs for these inserts.

*   **Update/Delete:**
    *   No direct update or delete operations are explicitly shown for `tblDG_ECN_Reason` on this specific page. The page's primary role is to facilitate the *creation* of ECN detail entries. However, in a full modernization, `ECN_Reason` master data would likely have its own CRUD interface. This plan will provide boilerplate CRUD for `ECNReason` as the primary `MODEL_NAME` as per instructions, and then focus on the actual transaction logic.

*   **Redirection:**
    *   Upon successful transaction completion, the page redirects to `BOM_WoItems.aspx`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to modern Django/HTMX/Alpine.js equivalents.

**Analysis:**

*   **`GridView1` (List Display & Input Form):**
    *   Displays data from `tblDG_ECN_Reason`.
    *   `AutoGenerateColumns="False"` indicates explicit column definition.
    *   `PagerSettings` implies pagination.
    *   `CssClass="yui-datatable-theme"` suggests a tabular data presentation style.
    *   **Columns:**
        *   `SN` (`<%# Container.DataItemIndex+1 %>`): Row counter.
        *   `CheckBox1` (`<asp:CheckBox ID="CheckBox1" ... AutoPostBack="true" OnCheckedChanged="CheckBox1_CheckedChanged" />`): For selecting individual ECN reasons. `AutoPostBack` suggests immediate server interaction on change, which will be handled client-side with HTMX/Alpine.js or submitted as part of the batch.
        *   `Id` (hidden `lblId`): Stores the primary key of the ECN Reason.
        *   `Description` (`lblDesc` bound to `Types`): Displays the ECN reason description.
        *   `Remarks` (`TxtRemarks` Textbox): Allows users to enter free-form remarks for each selected reason.
        *   `BtnInsert` (`<asp:Button ID="BtnInsert" ... CommandName="Ins" />` in `FooterTemplate`): Triggers the batch insertion process for all selected items. `OnClientClick="return confirmationAdd()"` implies a JavaScript confirmation.
        *   `EmptyDataTemplate`: Handles cases where no data is found.

*   **`BtnCancel` (`<asp:Button ID="BtnCancel" ... onclick="BtnCancel_Click" />`):** A standard button for navigating away without saving changes.

**Modernization Mapping:**

*   `GridView1` -> `<table id="ecnReasonTable">` managed by **DataTables.js**.
*   `AutoPostBack` CheckBox behavior -> Managed by **HTMX** (e.g., `hx-post` for dynamic updates, or simply as part of the main form submission). For this specific page, since it's a batch insert, the checkboxes and textboxes will be part of a single form submitted by the "Insert" button.
*   `yui-datatable-theme` CSS -> **Tailwind CSS** for styling, **DataTables.js** for functionality.
*   `asp:Label`, `asp:TextBox` -> Standard HTML `<label>`, `<input type="text">`, `<textarea>`.
*   `asp:Button` -> Standard HTML `<button>`.
*   `OnClientClick="return confirmationAdd()"` -> Can be done with **Alpine.js** for simple confirmations or handled by the server response/HTMX trigger.

### Step 4: Generate Django Code

We will create an `ecn_management` Django application.

#### 4.1 Models (`ecn_management/models.py`)

**Task:** Create Django models based on the identified database schema. Include a manager method for the complex ECN transaction logic.

**Instructions:**
- Define `EcnReason`, `BomItemTemp`, `EcnMasterTemp`, and `EcnDetailTemp`.
- Use `managed = False` and `db_table` for existing database tables.
- Define appropriate field types (e.g., `models.IntegerField`, `models.CharField`, `models.FloatField`).
- Add foreign key relationships where identified.
- Implement a `EcnMasterTempManager` to encapsulate the complex `GridView1_RowCommand` (Ins) logic.

```python
from django.db import models, transaction
from datetime import datetime

class EcnReason(models.Model):
    """
    Maps to tblDG_ECN_Reason: Stores predefined reasons for ECNs.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    types = models.CharField(db_column='Types', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Reason'
        verbose_name = 'ECN Reason'
        verbose_name_plural = 'ECN Reasons'

    def __str__(self):
        return self.types

class BomItemTemp(models.Model):
    """
    Maps to tblDG_BOMItem_Temp: Temporary Bill of Material item.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    wo_no = models.CharField(db_column='WONo', max_length=255)
    item_id = models.IntegerField(db_column='ItemId')
    qty = models.FloatField(db_column='Qty')
    child_id = models.IntegerField(db_column='ChildId')
    ecn_flag = models.CharField(db_column='ECNFlag', max_length=10) # Assuming '1' is char

    class Meta:
        managed = False
        db_table = 'tblDG_BOMItem_Temp'
        verbose_name = 'BOM Item Temp'
        verbose_name_plural = 'BOM Items Temp'

    def __str__(self):
        return f"BOM Temp {self.id} for WO: {self.wo_no}"

class EcnMasterTempManager(models.Manager):
    @transaction.atomic
    def create_ecn_transaction(self, selected_reasons_data, session_params):
        """
        Handles the complex insertion logic mimicking GridView1_RowCommand (Ins).
        
        Args:
            selected_reasons_data (list): List of dicts, each with 'reason_id' and 'remarks'.
            session_params (dict): Contains 'comp_id', 'fin_year_id', 'session_id', 
                                   'item_id', 'wo_no', 'p_id', 'c_id', 'qty'.
        
        Returns:
            bool: True if transaction was successful and details were created, False otherwise.
        """
        if not selected_reasons_data:
            return False # No reasons selected

        comp_id = session_params.get('comp_id')
        session_id = session_params.get('session_id')
        wo_no = session_params.get('wo_no')
        item_id = session_params.get('item_id')
        qty = session_params.get('qty')
        child_id = session_params.get('c_id')
        fin_year_id = session_params.get('fin_year_id')
        p_id = session_params.get('p_id')
        current_date = datetime.now().strftime('%Y-%m-%d') # Example format
        current_time = datetime.now().strftime('%H:%M:%S') # Example format

        # 1. Insert into tblDG_BOMItem_Temp
        bom_item_temp = BomItemTemp.objects.create(
            comp_id=comp_id,
            session_id=session_id,
            wo_no=wo_no,
            item_id=item_id,
            qty=qty,
            child_id=child_id,
            ecn_flag='1'
        )

        # 2. Insert into tblDG_ECN_Master_Temp
        ecn_master_temp = self.create(
            mid=bom_item_temp.id,
            sys_date=current_date,
            sys_time=current_time,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            session_id=session_id,
            item_id=item_id,
            wo_no=wo_no,
            p_id=p_id,
            cid=child_id
        )

        # 3. Insert into tblDG_ECN_Details_Temp for each selected reason
        details_created = 0
        for data in selected_reasons_data:
            EcnDetailTemp.objects.create(
                mid=ecn_master_temp.id,
                ecn_reason=data['reason_id'],
                remarks=data['remarks']
            )
            details_created += 1
        
        return details_created > 0


class EcnMasterTemp(models.Model):
    """
    Maps to tblDG_ECN_Master_Temp: Temporary ECN header, linking to a BOMItem_Temp.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='MId') # Could be ForeignKey(BomItemTemp, db_column='MId') if Id is reliable.
                                            # Keeping as IntegerField as per original code for Id retrieval.
    sys_date = models.CharField(db_column='SysDate', max_length=50) # CDate / Date object
    sys_time = models.CharField(db_column='SysTime', max_length=50) # CTime / Time object
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    item_id = models.IntegerField(db_column='ItemId')
    wo_no = models.CharField(db_column='WONo', max_length=255)
    p_id = models.IntegerField(db_column='PId')
    cid = models.IntegerField(db_column='CId') # Using cid to avoid conflict with Python's id()

    objects = EcnMasterTempManager()

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Master_Temp'
        verbose_name = 'ECN Master Temp'
        verbose_name_plural = 'ECN Masters Temp'

    def __str__(self):
        return f"ECN Master Temp {self.id} for WO: {self.wo_no}"

class EcnDetailTemp(models.Model):
    """
    Maps to tblDG_ECN_Details_Temp: Temporary ECN details.
    """
    # No explicit primary key in original table, assuming MId, ECNReason are composite or auto-PK is added by Django
    # We'll use a default auto-incrementing ID for simplicity, or make MId, ECNReason a unique_together.
    # For managed=False, Django won't create a default PK if one doesn't exist, so careful consideration.
    # Assuming Id column exists or is handled by the database for composite PK for insert.
    # If not present, Django will add a default 'id' field, which is fine as long as DB is configured for it.
    mid = models.IntegerField(db_column='MId') # Could be ForeignKey(EcnMasterTemp, db_column='MId')
    ecn_reason = models.IntegerField(db_column='ECNReason') # Could be ForeignKey(EcnReason, db_column='ECNReason')
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Details_Temp'
        verbose_name = 'ECN Detail Temp'
        verbose_name_plural = 'ECN Details Temp'
        # Consider unique_together = (('mid', 'ecn_reason'),) if a reason can only be added once per master.

    def __str__(self):
        return f"ECN Detail Temp for Master {self.mid}, Reason {self.ecn_reason}"

```

#### 4.2 Forms (`ecn_management/forms.py`)

**Task:** Define a Django form for standard CRUD of `EcnReason` and a custom form/logic to handle the batch submission for ECN details.

**Instructions:**
- Create `EcnReasonForm` for basic CRUD.
- For the ECN details submission, we'll use a `Form` that validates `reason_id` and `remarks` for multiple entries. This will be processed directly in the view from the POST data.

```python
from django import forms
from .models import EcnReason

class EcnReasonForm(forms.ModelForm):
    """
    Standard form for CRUD operations on EcnReason master data.
    """
    class Meta:
        model = EcnReason
        fields = ['types', 'comp_id'] # Id is auto-managed
        widgets = {
            'types': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
        labels = {
            'types': 'Description',
            'comp_id': 'Company ID',
        }

    # No specific custom validation needed beyond default for EcnReason
```

#### 4.3 Views (`ecn_management/views.py`)

**Task:** Implement standard CRUD operations for `EcnReason` and a custom view for the batch ECN details transaction.

**Instructions:**
- Define `EcnReasonListView`, `EcnReasonCreateView`, `EcnReasonUpdateView`, `EcnReasonDeleteView` adhering to the template.
- Create `EcnReasonTablePartialView` for HTMX-driven table updates.
- Implement `ProcessEcnDetailsView` to handle the `hx-post` for batch inserts. This view will call the `EcnMasterTempManager` method.
- Use `request.session` and `request.GET` for context parameters.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from .models import EcnReason, EcnMasterTemp
from .forms import EcnReasonForm
import json

# --- Standard CRUD Views for EcnReason (Master Data) ---

class EcnReasonListView(ListView):
    """
    Displays a list of ECN Reasons.
    This view also provides the context for the ECN details entry form.
    """
    model = EcnReason
    template_name = 'ecn_management/ecn_reason/list.html'
    context_object_name = 'ecn_reasons'

    def get_queryset(self):
        # Filter by CompId from session, mimicking ASP.NET behavior
        comp_id = self.request.session.get('compid', 0) # Default to 0 or handle error
        return EcnReason.objects.filter(comp_id=comp_id).order_by('id')

class EcnReasonTablePartialView(ListView):
    """
    Provides the DataTables content for HTMX partial updates.
    """
    model = EcnReason
    template_name = 'ecn_management/ecn_reason/_ecn_reason_table.html'
    context_object_name = 'ecn_reasons'

    def get_queryset(self):
        comp_id = self.request.session.get('compid', 0)
        return EcnReason.objects.filter(comp_id=comp_id).order_by('id')

class EcnReasonCreateView(CreateView):
    model = EcnReason
    form_class = EcnReasonForm
    template_name = 'ecn_management/ecn_reason/form.html'
    success_url = reverse_lazy('ecn_reason_list')

    def form_valid(self, form):
        # Set comp_id from session if not already set by user input
        if not form.instance.comp_id:
            form.instance.comp_id = self.request.session.get('compid', 0)

        response = super().form_valid(form)
        messages.success(self.request, 'ECN Reason added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, indicates success to HTMX
                headers={
                    'HX-Trigger': 'refreshEcnReasonList'
                }
            )
        return response

class EcnReasonUpdateView(UpdateView):
    model = EcnReason
    form_class = EcnReasonForm
    template_name = 'ecn_management/ecn_reason/form.html'
    success_url = reverse_lazy('ecn_reason_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'ECN Reason updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEcnReasonList'
                }
            )
        return response

class EcnReasonDeleteView(DeleteView):
    model = EcnReason
    template_name = 'ecn_management/ecn_reason/confirm_delete.html'
    success_url = reverse_lazy('ecn_reason_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'ECN Reason deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshEcnReasonList'
                }
            )
        return response

# --- Custom View for ECN Details Transaction ---

class ProcessEcnDetailsView(View):
    """
    Handles the POST request for inserting ECN details (batch operation).
    Corresponds to GridView1_RowCommand (Ins) logic.
    """
    def post(self, request, *args, **kwargs):
        # Extract session/query string parameters
        session_params = {
            'comp_id': request.session.get('compid', 0),
            'fin_year_id': request.session.get('finyear', 0),
            'session_id': request.session.get('username', ''),
            'wo_no': request.GET.get('WONo', ''),
            'item_id': int(request.GET.get('ItemId', 0)),
            'c_id': int(request.GET.get('CId', 0)), # childId from ASP.NET
            'p_id': int(request.GET.get('ParentId', 0)), # PId from ASP.NET
            'qty': float(request.GET.get('Qty', 0.0)),
            # AssblyNo is ItemId in redirect, used for BOM_WoItems.aspx
            'assbly_no': int(request.GET.get('asslyNo', 0)) # Using asslyNo for consistency
        }

        # Collect selected ECN reasons and their remarks
        selected_reasons_data = []
        # Expecting data like: {'reason_id_1': 'on', 'remarks_1': 'some text', 'reason_id_2': 'on', ...}
        # Iterate over POST data to find checkboxes and remarks
        for key, value in request.POST.items():
            if key.startswith('reason_id_') and value == 'on':
                reason_id = int(key.replace('reason_id_', ''))
                remarks = request.POST.get(f'remarks_{reason_id}', '').strip()
                selected_reasons_data.append({
                    'reason_id': reason_id,
                    'remarks': remarks
                })

        # Call the fat model logic to perform the transaction
        if selected_reasons_data:
            success = EcnMasterTemp.objects.create_ecn_transaction(selected_reasons_data, session_params)
            if success:
                messages.success(request, 'ECN details processed successfully.')
                # Mimic ASP.NET redirect
                redirect_url = reverse_lazy('bom_wo_items_page') + \
                               f"?WONo={session_params['wo_no']}" \
                               f"&PId={session_params['p_id']}" \
                               f"&CId={session_params['c_id']}" \
                               f"&ItemId={session_params['assbly_no']}" \
                               "&ModId=3&SubModId=26"
                
                # For HTMX, trigger a redirect or a success message
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        status=200, # OK
                        headers={
                            'HX-Redirect': redirect_url # HTMX will handle client-side redirect
                        }
                    )
                return redirect(redirect_url)
            else:
                messages.error(request, 'Failed to process ECN details.')
        else:
            messages.warning(request, 'No ECN reasons selected for processing.')
            
        # If not successful or no selections and not an HTMX request, reload the current page.
        # This mimics the loaddata() call in ASP.NET if y == 0.
        if request.headers.get('HX-Request'):
             # If HTMX, return a 204 or re-render part of the page with errors/messages
             return HttpResponse(status=204) # No content, indicates to HTMX nothing new rendered, but can show messages
        
        # Fallback for non-HTMX or if no details were selected
        return redirect(reverse_lazy('ecn_reason_list') + f"?{request.GET.urlencode()}")

class BomWoItemsPageView(View):
    """
    Placeholder view for BOM_WoItems.aspx redirect target.
    In a real app, this would be the actual BOM_WoItems page.
    """
    def get(self, request, *args, **kwargs):
        # This is a placeholder. In a real app, render the BOM_WoItems page.
        return HttpResponse("Successfully redirected to BOM_WoItems page (placeholder).")

```

#### 4.4 Templates (`ecn_management/templates/ecn_management/ecn_reason/`)

**Task:** Create templates for list, form, and delete confirmation, leveraging HTMX, Alpine.js, and DataTables.

**Instructions:**
- `list.html` will contain the main page structure and wrap the `_ecn_reason_table.html` for HTMX. It will also hold the overall form for submission.
- `_ecn_reason_table.html` will be a partial that defines the DataTables structure for the ECN Reasons, including checkboxes and remarks textboxes for the batch submission.
- `form.html` will be a partial for standard `EcnReason` CRUD modals.
- `confirm_delete.html` will be a partial for standard `EcnReason` deletion modals.

**`ecn_management/templates/ecn_management/ecn_reason/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">ECN Reasons Management</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'ecn_reason_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New ECN Reason
        </button>
    </div>
    
    <!-- This form wraps the table and will send all data for batch processing -->
    <form hx-post="{% url 'process_ecn_details' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" hx-swap="none">
        {% csrf_token %}
        <div id="ecnReasonTable-container"
             hx-trigger="load, refreshEcnReasonList from:body"
             hx-get="{% url 'ecn_reason_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading ECN Reasons...</p>
            </div>
        </div>
        
        <div class="mt-6 flex justify-end">
            <button type="submit" 
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded mr-2"
                    onclick="return confirm('Are you sure you want to process the selected ECN details?');">
                Process Selected ECN Details
            </button>
            <button type="button" 
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                    hx-get="{% url 'bom_wo_items_page' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                    hx-target="body" hx-swap="outerHTML">
                Cancel
            </button>
        </div>
    </form>
    
    <!-- Modal for form (Add/Edit/Delete ECN Reason) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states.
        // Example: x-data="{ showModal: false }" for the modal.
    });
</script>
{% endblock %}
```

**`ecn_management/templates/ecn_management/ecn_reason/_ecn_reason_table.html`**

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="ecnReasonTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for reason in ecn_reasons %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-center text-sm">
                    <input type="checkbox" id="checkbox_{{ reason.id }}" name="reason_id_{{ reason.id }}" class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500">
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-left text-sm text-gray-900">
                    <label for="checkbox_{{ reason.id }}">{{ reason.types }}</label>
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">
                    <input type="text" name="remarks_{{ reason.id }}" class="box3 block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                </td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'ecn_reason_edit' reason.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'ecn_reason_delete' reason.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-6 text-center text-red-500 font-bold">No data found to display</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#ecnReasonTable').DataTable({
        "pageLength": 15,
        "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [1, 3, 4] } // Disable sorting on Select, Remarks, Actions columns
        ]
    });
});
</script>
```

**`ecn_management/templates/ecn_management/ecn_reason/form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} ECN Reason</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`ecn_management/templates/ecn_management/ecn_reason/confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete ECN Reason: <span class="font-bold">{{ object.types }}</span>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`ecn_management/urls.py`)

**Task:** Define URL patterns for all views.

**Instructions:**
- Create paths for standard ECN Reason CRUD.
- Add a path for the HTMX partial table update.
- Add a path for the `ProcessEcnDetailsView`.
- Add a placeholder for the `BOM_WoItems` redirect target.

```python
from django.urls import path
from .views import (
    EcnReasonListView, 
    EcnReasonCreateView, 
    EcnReasonUpdateView, 
    EcnReasonDeleteView,
    EcnReasonTablePartialView,
    ProcessEcnDetailsView,
    BomWoItemsPageView
)

urlpatterns = [
    # ECN Reason Master Data CRUD
    path('ecn-reasons/', EcnReasonListView.as_view(), name='ecn_reason_list'),
    path('ecn-reasons/table/', EcnReasonTablePartialView.as_view(), name='ecn_reason_table'),
    path('ecn-reasons/add/', EcnReasonCreateView.as_view(), name='ecn_reason_add'),
    path('ecn-reasons/edit/<int:pk>/', EcnReasonUpdateView.as_view(), name='ecn_reason_edit'),
    path('ecn-reasons/delete/<int:pk>/', EcnReasonDeleteView.as_view(), name='ecn_reason_delete'),

    # ECN Details Transactional Process
    path('process-ecn-details/', ProcessEcnDetailsView.as_view(), name='process_ecn_details'),

    # Placeholder for Redirect Target (BOM_WoItems.aspx)
    path('bom-wo-items/', BomWoItemsPageView.as_view(), name='bom_wo_items_page'),
]
```

#### 4.6 Tests (`ecn_management/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
- Include unit tests for `EcnReason`, `BomItemTemp`, `EcnMasterTemp`, `EcnDetailTemp` models.
- Specifically test the `EcnMasterTempManager.create_ecn_transaction` method to ensure correct multi-table insertion.
- Add integration tests for all views: `EcnReasonListView`, `EcnReasonCreateView`, `EcnReasonUpdateView`, `EcnReasonDeleteView`, `EcnReasonTablePartialView`, and `ProcessEcnDetailsView`.
- Test HTMX interactions (e.g., `HX-Request` headers, `HX-Trigger` responses).

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import datetime
from .models import EcnReason, BomItemTemp, EcnMasterTemp, EcnDetailTemp

# Mocking auto-increment behavior for managed=False tables
class MockDbId:
    _id_counter = 0

    @classmethod
    def get_next_id(cls):
        cls._id_counter += 1
        return cls._id_counter

# Unit Tests for Models
class EcnModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup test data for EcnReason (master data)
        EcnReason.objects.create(id=1, types='Design Change', comp_id=101)
        EcnReason.objects.create(id=2, types='Material Change', comp_id=101)
        EcnReason.objects.create(id=3, types='Process Change', comp_id=102) # Different comp_id

    def test_ecn_reason_creation(self):
        reason = EcnReason.objects.get(id=1)
        self.assertEqual(reason.types, 'Design Change')
        self.assertEqual(reason.comp_id, 101)
        self.assertEqual(str(reason), 'Design Change')

    # Test the EcnMasterTempManager.create_ecn_transaction method
    @patch('ecn_management.models.BomItemTemp.objects.create')
    @patch('ecn_management.models.EcnMasterTemp.objects.create')
    @patch('ecn_management.models.EcnDetailTemp.objects.create')
    @patch('ecn_management.models.datetime')
    def test_create_ecn_transaction(self, mock_datetime, mock_ecn_detail_create, mock_ecn_master_create, mock_bom_item_create):
        # Mock current date and time
        mock_datetime.now.return_value = datetime(2023, 10, 26, 10, 30, 0)
        mock_datetime.now().strftime.side_effect = lambda format: {
            '%Y-%m-%d': '2023-10-26',
            '%H:%M:%S': '10:30:00'
        }[format]

        # Mock objects created in the transaction
        mock_bom_item = MagicMock(spec=BomItemTemp)
        mock_bom_item.id = MockDbId.get_next_id() # Simulate auto-incrementing ID
        mock_bom_item_create.return_value = mock_bom_item

        mock_ecn_master = MagicMock(spec=EcnMasterTemp)
        mock_ecn_master.id = MockDbId.get_next_id() # Simulate auto-incrementing ID
        mock_ecn_master_create.return_value = mock_ecn_master

        selected_reasons_data = [
            {'reason_id': 1, 'remarks': 'Revised specs for component A'},
            {'reason_id': 2, 'remarks': 'New material for housing'}
        ]
        session_params = {
            'comp_id': 101, 'fin_year_id': 2023, 'session_id': 'user123',
            'wo_no': 'WO-001', 'item_id': 123, 'qty': 10.5, 'c_id': 456, 'p_id': 789,
            'assbly_no': 999
        }

        manager = EcnMasterTemp.objects
        result = manager.create_ecn_transaction(selected_reasons_data, session_params)

        self.assertTrue(result)
        
        # Verify BomItemTemp creation
        mock_bom_item_create.assert_called_once_with(
            comp_id=101, session_id='user123', wo_no='WO-001', item_id=123,
            qty=10.5, child_id=456, ecn_flag='1'
        )

        # Verify EcnMasterTemp creation
        mock_ecn_master_create.assert_called_once_with(
            mid=mock_bom_item.id, sys_date='2023-10-26', sys_time='10:30:00',
            comp_id=101, fin_year_id=2023, session_id='user123', item_id=123,
            wo_no='WO-001', p_id=789, cid=456
        )

        # Verify EcnDetailTemp creations
        self.assertEqual(mock_ecn_detail_create.call_count, 2)
        mock_ecn_detail_create.assert_any_call(
            mid=mock_ecn_master.id, ecn_reason=1, remarks='Revised specs for component A'
        )
        mock_ecn_detail_create.assert_any_call(
            mid=mock_ecn_master.id, ecn_reason=2, remarks='New material for housing'
        )
    
    def test_create_ecn_transaction_no_reasons(self):
        result = EcnMasterTemp.objects.create_ecn_transaction([], {})
        self.assertFalse(result)


# Integration Tests for Views
class EcnViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Setup session data
        session = self.client.session
        session['compid'] = 101
        session['finyear'] = 2023
        session['username'] = 'testuser'
        session.save()

        # Create test data for EcnReason (master data)
        EcnReason.objects.create(id=10, types='Reason A', comp_id=101)
        EcnReason.objects.create(id=11, types='Reason B', comp_id=101)
        EcnReason.objects.create(id=12, types='Reason C', comp_id=102) # Different comp_id

        # Base URL parameters mimicking QueryString
        self.base_url_params = '?WONo=WO123&ItemId=1&asslyNo=100&CId=2&ParentId=3&Qty=5.0'


    def test_ecn_reason_list_view(self):
        response = self.client.get(reverse('ecn_reason_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecn_management/ecn_reason/list.html')
        self.assertIn('ecn_reasons', response.context)
        # Should only show reasons for comp_id 101
        self.assertEqual(response.context['ecn_reasons'].count(), 2)
        self.assertContains(response, 'Reason A')
        self.assertNotContains(response, 'Reason C')

    def test_ecn_reason_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('ecn_reason_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecn_management/ecn_reason/_ecn_reason_table.html')
        self.assertContains(response, 'Reason A')
        self.assertContains(response, 'id="ecnReasonTable"')

    def test_ecn_reason_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('ecn_reason_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecn_management/ecn_reason/form.html')
        self.assertIn('form', response.context)

    @patch('ecn_management.models.MockDbId.get_next_id', side_effect=[13]) # For the new EcnReason ID
    def test_ecn_reason_create_view_post_htmx(self, mock_get_next_id):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'types': 'New Reason D', 'comp_id': 101}
        response = self.client.post(reverse('ecn_reason_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success no content
        self.assertTrue(EcnReason.objects.filter(types='New Reason D', comp_id=101).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshEcnReasonList')

    def test_ecn_reason_update_view_get(self):
        reason = EcnReason.objects.get(id=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('ecn_reason_edit', args=[reason.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecn_management/ecn_reason/form.html')
        self.assertContains(response, 'Reason A')

    def test_ecn_reason_update_view_post_htmx(self):
        reason = EcnReason.objects.get(id=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'types': 'Updated Reason A', 'comp_id': 101}
        response = self.client.post(reverse('ecn_reason_edit', args=[reason.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        reason.refresh_from_db()
        self.assertEqual(reason.types, 'Updated Reason A')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshEcnReasonList')

    def test_ecn_reason_delete_view_get(self):
        reason = EcnReason.objects.get(id=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('ecn_reason_delete', args=[reason.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecn_management/ecn_reason/confirm_delete.html')
        self.assertContains(response, 'Reason A')

    def test_ecn_reason_delete_view_post_htmx(self):
        reason_to_delete = EcnReason.objects.create(id=99, types='To Be Deleted', comp_id=101)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('ecn_reason_delete', args=[reason_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(EcnReason.objects.filter(id=99).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshEcnReasonList')

    @patch('ecn_management.models.EcnMasterTemp.objects.create_ecn_transaction')
    def test_process_ecn_details_view_post_htmx_success(self, mock_create_transaction):
        # Configure mock to return True (success)
        mock_create_transaction.return_value = True

        headers = {'HTTP_HX_REQUEST': 'true'}
        post_data = {
            'reason_id_10': 'on',
            'remarks_10': 'Test remarks for Reason A',
            'reason_id_11': 'on',
            'remarks_11': 'Test remarks for Reason B'
        }
        
        response = self.client.post(reverse('process_ecn_details') + self.base_url_params, post_data, **headers)
        
        self.assertEqual(response.status_code, 200) # HTMX redirect will be status 200 with HX-Redirect header
        self.assertIn('HX-Redirect', response.headers)
        self.assertTrue(response.headers['HX-Redirect'].startswith('/bom-wo-items/'))

        # Verify that the model manager method was called with correct data
        mock_create_transaction.assert_called_once()
        args, kwargs = mock_create_transaction.call_args
        
        passed_selected_reasons = args[0]
        self.assertEqual(len(passed_selected_reasons), 2)
        self.assertIn({'reason_id': 10, 'remarks': 'Test remarks for Reason A'}, passed_selected_reasons)
        self.assertIn({'reason_id': 11, 'remarks': 'Test remarks for Reason B'}, passed_selected_reasons)

        passed_session_params = args[1]
        self.assertEqual(passed_session_params['comp_id'], 101)
        self.assertEqual(passed_session_params['wo_no'], 'WO123')
        self.assertEqual(passed_session_params['item_id'], 1)
        self.assertEqual(passed_session_params['assbly_no'], 100) # ItemId in redirect
        self.assertEqual(passed_session_params['c_id'], 2)
        self.assertEqual(passed_session_params['p_id'], 3)
        self.assertEqual(passed_session_params['qty'], 5.0)

    @patch('ecn_management.models.EcnMasterTemp.objects.create_ecn_transaction')
    def test_process_ecn_details_view_post_htmx_failure(self, mock_create_transaction):
        # Configure mock to return False (failure)
        mock_create_transaction.return_value = False

        headers = {'HTTP_HX_REQUEST': 'true'}
        post_data = {
            'reason_id_10': 'on',
            'remarks_10': 'Test remarks for Reason A'
        }
        
        response = self.client.post(reverse('process_ecn_details') + self.base_url_params, post_data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content for non-redirect failure
        self.assertNotIn('HX-Redirect', response.headers)
        
        mock_create_transaction.assert_called_once()

    def test_process_ecn_details_view_post_no_selection_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        post_data = {} # No reasons selected
        
        response = self.client.post(reverse('process_ecn_details') + self.base_url_params, post_data, **headers)
        
        self.assertEqual(response.status_code, 204) # No content, as no action taken.
        self.assertNotIn('HX-Redirect', response.headers)
        # Verify that create_ecn_transaction was NOT called
        self.assertFalse(EcnMasterTemp.objects.create_ecn_transaction.called)

    def test_bom_wo_items_page_view(self):
        response = self.client.get(reverse('bom_wo_items_page'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Successfully redirected to BOM_WoItems page (placeholder).')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for dynamic updates:**
    - `ecnReasonTable-container` uses `hx-trigger="load, refreshEcnReasonList from:body"` and `hx-get="{% url 'ecn_reason_table' %}"` to load the DataTables content initially and refresh it after CRUD operations.
    - CRUD operation buttons (Edit, Delete, Add) within the table use `hx-get` to load forms into the modal (`#modalContent`) and use `_ = "on click add .is-active to #modal"` to show the modal.
    - Form submissions for CRUD are `hx-post` with `hx-swap="none"` and rely on `HX-Trigger` from the backend to refresh the table.
    - The main "Process Selected ECN Details" button `hx-post` to `process_ecn_details` view, and the view sends `HX-Redirect` on success.
- **Alpine.js for UI state management:**
    - The modal (`#modal`) uses `x-data` implicitly for its `hidden` state and `on click` for closing the modal (e.g., `_ = "on click if event.target.id == 'modal' remove .is-active from me"`). This is a simple example of using Alpine.js's `_` syntax for direct DOM manipulation.
- **DataTables for list views:**
    - `_ecn_reason_table.html` includes a `<script>` block to initialize DataTables on `#ecnReasonTable`.
    - `pageLength` and `lengthMenu` are configured for pagination.
    - `columnDefs` are used to disable sorting on non-data columns.
- **DRY templates:** `base.html` (not included here) would contain all CDN links for HTMX, Alpine.js, jQuery, and DataTables.

---

## Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET ECN Master page to a modern Django application. By leveraging Django's robust ORM, HTMX for dynamic interactions, Alpine.js for lightweight frontend reactivity, and DataTables for data presentation, the new system will be more performant, maintainable, and aligned with modern web development best practices. The emphasis on fat models ensures business logic is centralized and testable, while thin views maintain clean separation of concerns. This automated, structured approach significantly reduces manual effort and potential for human error during the migration process.