## ASP.NET to Django Conversion Script: BOM Design Work Order Tree View Edit

This document outlines the modernization plan for the ASP.NET `BOM_Design_WO_TreeView_Edit.aspx` application to a Django 5.0+ solution. The focus is on leveraging AI-assisted automation by providing clear, component-specific instructions and adhering strictly to the "Fat Model, Thin View" philosophy, HTMX for dynamic interactions, and DataTables for rich tabular data presentation.

This plan is designed to be actionable through conversational AI, providing structured guidance for automated code generation and integration.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (`core/base.html`).
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the C# `GetDataTable` method and SQL queries, the following tables and their conceptual columns are identified:

*   **`tblDG_BOM_Master` (Target Django Model: `BOMMaster`)**
    *   `Id` (int, Primary Key)
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master.Id`)
    *   `WONo` (string)
    *   `PId` (int, Parent ID, referencing `CId` of another BOM entry)
    *   `CId` (int, Child ID, uniquely identifies this BOM entry as a potential parent)
    *   `Qty` (decimal)
    *   `AmdNo` (string)
    *   `Revision` (string)
    *   `CompId` (int, Company ID)
    *   `FinYearId` (int, Financial Year ID)

*   **`tblDG_Item_Master` (Target Django Model: `ItemMaster`)**
    *   `Id` (int, Primary Key)
    *   `ItemCode` (string)
    *   `UOMBasic` (int, Foreign Key to `Unit_Master.Id`)
    *   `FileName` (string, drawing file name)
    *   `AttName` (string, attachment file name)
    *   `PartNo` (string)
    *   `ManfDesc` (string, Manufacturer Description/General Description)
    *   `CId` (int, Category ID, distinguishes 'BoughtOut' vs. 'Manufacturing' items)
    *   `FileData` (binary, actual drawing data)
    *   `ContentType` (string, drawing file MIME type)
    *   `AttData` (binary, actual attachment data)
    *   `AttContentType` (string, attachment file MIME type)

*   **`Unit_Master` (Target Django Model: `UnitMaster`)**
    *   `Id` (int, Primary Key)
    *   `Symbol` (string)

**Note:** `tblDG_BOMItem_Temp` is a temporary table managed directly in the C# code. In Django, this logic will be absorbed into in-memory data processing within the model's manager to maintain a stateless and scalable architecture.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data flows within the ASP.NET code.

**Instructions:**
The ASP.NET page primarily functions as a **Read** (display) operation with filtering and navigation to other related "edit" pages.

*   **Read:**
    *   The `GetDataTable` method is the central data retrieval logic. It queries `tblDG_BOM_Master`, `tblDG_Item_Master`, and `Unit_Master` to build a hierarchical Bill of Material (BOM) structure.
    *   It filters data based on `DropDownList1` (`All`, `BoughtOut`, `Manufacturing`) and dynamically calculates "BOM Qty" based on the hierarchy.
    *   The `RadTreeList1` control displays this data, supporting pagination and hierarchical expansion/collapse.
*   **Filtering/User Interaction:**
    *   `DropDownList1_SelectedIndexChanged`: Triggers data reload based on item type filter.
    *   `CheckBox1_CheckedChanged`: Toggles full tree expansion/collapse.
*   **Navigation/Actions:**
    *   `Button1_Click` (Cancel): Redirects to `BOM_Design_WO_Grid_Update.aspx`.
    *   `RadTreeList1_ItemCommand`: Handles row-specific actions:
        *   `Sel` (Select/Edit): Redirects to `BOM_Design_Assembly_Edit.aspx` (for root assemblies) or `BOM_Design_Item_Edit.aspx` (for child items), passing various IDs and the Work Order number.
        *   `downloadImg`, `downloadSpec`: Redirects to `DownloadFile.aspx` for file download.
        *   `uploadImg`, `uploadSpec`: Redirects to `BOM_UploadDrw.aspx` for file upload.
        *   `Amd` (Amend): Redirects to `BOM_Amd.aspx`.

No direct `Create`, `Update`, or `Delete` operations are performed *on the BOM data itself* within this specific ASP.NET page; it serves as a gateway to other editing pages.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to inform Django template design.

**Instructions:**
*   **`Label2` (WO No):** Displays the Work Order Number, passed via query string.
*   **`DropDownList1` (Filter):** A standard dropdown with options "All", "BoughtOut", "Manufacturing". Controls data filtering.
*   **`CheckBox1` (Expand Tree):** Toggles tree expansion.
*   **`Button1` (Cancel):** A simple button for navigation.
*   **`RadTreeList1` (Telerik TreeList):** The primary data display component. This will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side sorting, filtering, and pagination. For the tree structure, visual indentation will be applied based on hierarchy depth, and a custom DataTables plugin could further enhance tree functionality if required, but for standard DataTables, it will be a flat list with visual cues.
    *   **Columns identified:** `ItemId` (hidden), `WONo` (hidden), `PId` (hidden), `CId` (hidden), `Id` (hidden), `AmdNo` (visible as link), `Revision` (hidden), `Item Code`, `Description`, `UOM`, `Unit Qty`, `BOM Qty`, `Drw/Image` (link), `Spec. Sheet` (link).
    *   **Action Buttons:** `SelectButton` (`Sel` command), `btnlnkImg`, `lnkUploadImg`, `btnlnkSpec`, `lnkUploadSpec`, `BtnAmdNo`. These will be represented as standard HTML `<a>` or `<button>` tags triggering redirects.

### Step 4: Generate Django Code

The Django application will be named `design` (corresponding to `Module_Design`).

#### 4.1 Models (`design/models.py`)

**Task:** Create Django models based on the identified database schema, incorporating "Fat Model" principles for business logic.

**Instructions:**
Models are mapped directly to existing database tables using `managed = False` and `db_table`. A custom manager (`BOMMasterManager`) is crucial for encapsulating the complex `GetDataTable` logic, including hierarchical data retrieval and BOM quantity calculations.

```python
from django.db import models
from django.db.models import Q # For complex queries

class UnitMaster(models.Model):
    """
    Corresponds to 'Unit_Master' table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or 'N/A'

class ItemMaster(models.Model):
    """
    Corresponds to 'tblDG_Item_Master' table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # ItemId in ASP.NET code is often ItemMaster.Id, avoiding 'item_id_legacy'
    item_code = models.CharField(db_column='ItemCode', max_length=200, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=200, blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=200, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=200, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True) # Category ID (BoughtOut/Manufacturing)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # Assuming binary data for files
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.part_no or 'N/A'

    def get_display_item_code(self):
        """
        Returns ItemCode if CId is not null, otherwise returns PartNo.
        Corresponds to ASP.NET logic for 'Item Code' column.
        """
        return self.item_code if self.c_id is not None else self.part_no

    def get_drawing_display(self):
        """Returns 'View' if FileName exists, 'Upload' otherwise."""
        return 'View' if self.file_name else 'Upload'

    def get_spec_sheet_display(self):
        """Returns 'View' if AttName exists, 'Upload' otherwise."""
        return 'View' if self.att_name else 'Upload'

class BOMMasterManager(models.Manager):
    """
    Custom manager for BOMMaster to encapsulate complex data retrieval logic
    from the original ASP.NET GetDataTable method.
    """
    def get_bom_tree_data(self, wo_no, comp_id, fin_year_id, drp_value):
        """
        Generates a flattened list of BOM items with hierarchical information (depth).
        
        Args:
            wo_no (str): Work Order Number.
            comp_id (int): Company ID.
            fin_year_id (int): Financial Year ID (data up to this year).
            drp_value (int): Filter type (0: All, 1: BoughtOut, 2: Manufacturing).
        
        Returns:
            list: A list of dictionaries, each representing a BOM item with
                  all required display and action data, including 'depth' for tree visualization.
        """
        
        # Build initial query filters
        q_filters = Q(wo_no=wo_no, comp_id=comp_id, fin_year_id__lte=fin_year_id)
        
        if drp_value == 1: # BoughtOut
            q_filters &= Q(item__c_id__isnull=False)
        elif drp_value == 2: # Manufacturing
            q_filters &= Q(item__c_id__isnull=True)

        # Fetch all relevant BOM entries and related ItemMaster and UnitMaster data
        # Ordering by PId and CId helps in consistent tree construction (optional, depends on actual tree structure)
        all_bom_entries = list(self.filter(q_filters).select_related('item', 'item__uom_basic').order_by('p_id', 'c_id'))

        # Create a map for quick lookup of children by their parent_id
        # (where parent_id here refers to BOMMaster.CId of the parent entry)
        parent_child_map = {}
        for entry in all_bom_entries:
            parent_child_map.setdefault(entry.p_id, []).append(entry)

        # Recursively build the flattened tree data for display
        def _build_flat_tree(parent_id, current_depth):
            tree_data = []
            children = parent_child_map.get(parent_id, [])
            
            # Sort children for consistent display, e.g., by item code or description
            children.sort(key=lambda x: x.item.get_display_item_code() or '')

            for bom_entry in children:
                item = bom_entry.item
                
                # Calculate BOM Qty for this specific item considering its ancestral path
                # This replicates the `fun.BOMTreeQty` logic by multiplying quantities up the hierarchy.
                bom_qty_calc = float(bom_entry.qty) # Start with current item's quantity
                current_p_id_for_qty = bom_entry.p_id
                
                # Find the direct parent BOM entry by matching current_p_id to a BOM entry's CId
                # This traversal goes *up* the tree to accumulate quantities
                while current_p_id_for_qty != 0:
                    parent_entry_for_qty = next((e for e in all_bom_entries if e.c_id == current_p_id_for_qty), None)
                    if parent_entry_for_qty:
                        bom_qty_calc *= float(parent_entry_for_qty.qty)
                        current_p_id_for_qty = parent_entry_for_qty.p_id
                    else:
                        break # No parent found (should not happen in a well-formed BOM, or root reached)

                row_data = {
                    'item_id': item.id, # Corresponds to lblItemId (ItemMaster.Id)
                    'wo_no': bom_entry.wo_no, # Corresponds to lblWONo
                    'p_id': bom_entry.p_id, # Corresponds to lblPId
                    'c_id': bom_entry.c_id, # Corresponds to lblCId
                    'item_code': item.get_display_item_code(), # 'Item Code' column
                    'description': item.manf_desc, # 'Description' column
                    'uom': item.uom_basic.symbol if item.uom_basic else '', # 'UOM' column
                    'unit_qty': f"{bom_entry.qty:.3f}", # 'Unit Qty' column
                    'bom_qty': f"{bom_qty_calc:.3f}", # 'BOM Qty' column
                    'file_name_display': item.get_drawing_display(), # 'Drw/Image' link text
                    'att_name_display': item.get_spec_sheet_display(), # 'Spec. Sheet' link text
                    'id': bom_entry.id, # Corresponds to lblId (BOMMaster.Id)
                    'amd_no': bom_entry.amd_no, # 'Amd No' column
                    'revision': bom_entry.revision, # 'Revision' column (hidden in ASP.NET UI)
                    'depth': current_depth # For visual indentation in template
                }
                tree_data.append(row_data)
                
                # Recursively add children
                tree_data.extend(_build_flat_tree(bom_entry.c_id, current_depth + 1))
            return tree_data

        # Start building the tree from root items (PId = 0)
        return _build_flat_tree(0, 0)

class BOMMaster(models.Model):
    """
    Corresponds to 'tblDG_BOM_Master' table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # PK for BOMMaster
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId') # FK to ItemMaster
    wo_no = models.CharField(db_column='WONo', max_length=200)
    p_id = models.IntegerField(db_column='PId') # Parent's CId (Child ID of the parent BOM entry)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True) # This BOM item's unique child identifier
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    amd_no = models.CharField(db_column='AmdNo', max_length=50, blank=True, null=True)
    revision = models.CharField(db_column='Revision', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = BOMMasterManager() # Assign custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master Entry'
        verbose_name_plural = 'BOM Master Entries'

    def __str__(self):
        return f"BOM {self.wo_no}: {self.item.get_display_item_code()} (Qty: {self.qty})"

```

#### 4.2 Forms (`design/forms.py`)

**Task:** Define Django forms.

**Instructions:**
For this specific page, which is a list view with filtering and navigation actions, no direct `ModelForm` for `BOMMaster` is required for Add/Edit operations. The "Sel" action (edit) redirects to separate pages. Therefore, no `BOMMasterForm` is generated in this scope.

```python
# No forms.py needed for this specific list/tree view.
# Forms would be defined for the target pages (e.g., BOMAssemblyEdit, BOMItemEdit).

```

#### 4.3 Views (`design/views.py`)

**Task:** Implement Read operations using CBVs.

**Instructions:**
The `BOMMasterListView` serves the main page, while `BOMMasterTablePartialView` uses HTMX to dynamically load the DataTables content based on filters. Placeholder views are created for all redirect targets (`Sel`, `downloadImg`, `uploadImg`, `Amd`, `Cancel`). Views are kept thin (5-15 lines) by delegating complex data logic to the models.

```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse
from django.shortcuts import redirect, get_object_or_404
from .models import BOMMaster, ItemMaster

# --- Mocking Session/Query String Parameters ---
# In a real application, CompId, FinYearId would typically come from
# request.user.profile or request.session after user authentication.
# SId would be request.user.username. WONo comes from URL query parameters.
# For demonstration purposes, we use mock defaults.
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR_ID = 1

class BOMMasterListView(TemplateView):
    """
    Main view for the BOM Design Work Order Tree View Edit page.
    It renders the base HTML structure with filters and a container for the HTMX-loaded table.
    """
    template_name = 'design/bom_master/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Retrieve Work Order Number from URL query parameter.
        # This is equivalent to Request.QueryString["WONo"] in ASP.NET.
        context['wo_no'] = self.request.GET.get('WONo', '')
        if not context['wo_no']:
            messages.error(self.request, "Work Order Number (WONo) is required.")
        return context

class BOMMasterTablePartialView(ListView):
    """
    HTMX-powered partial view that renders the BOM data in a DataTables format.
    It receives filtering parameters from the main page via HTMX requests.
    """
    model = BOMMaster
    template_name = 'design/bom_master/_bom_master_table.html'
    context_object_name = 'bom_masters' # This will be a list of dictionaries, not model instances.

    def get_queryset(self):
        # Parameters derived from ASP.NET code-behind
        wo_no = self.request.GET.get('wo_no')
        drp_value = int(self.request.GET.get('filter_type', 0)) # Default to 'All'
        # The 'expand_tree' checkbox implies visual state; the model manager always returns a flat list.
        # DataTables itself or a JS tree plugin would handle the visual collapse/expand.

        # Retrieve CompId and FinYearId from session (simulating ASP.NET Session usage)
        comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)
        
        if not wo_no:
            return [] # Return empty if WO No is missing

        # Call the fat model's manager method to get the processed data
        # This encapsulates the complex BOM data retrieval and calculation logic.
        bom_data = BOMMaster.objects.get_bom_tree_data(wo_no, comp_id, fin_year_id, drp_value)
        
        return bom_data

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The ListView expects a queryset, but our manager returns a list of dictionaries.
        # We explicitly set the context variable to this list.
        context[self.context_object_name] = self.get_queryset()
        return context

# --- Placeholder Views for Redirection Actions ---

class BOMAssemblyEditView(TemplateView):
    """Placeholder for BOM_Design_Assembly_Edit.aspx."""
    template_name = 'design/placeholder_page.html'
    def get(self, request, bom_id, *args, **kwargs):
        messages.info(request, f"Redirected to BOM Assembly Edit for BOM ID: {bom_id}. (Placeholder)")
        return super().get(request, *args, **kwargs)

class BOMItemEditView(TemplateView):
    """Placeholder for BOM_Design_Item_Edit.aspx."""
    template_name = 'design/placeholder_page.html'
    def get(self, request, bom_id, *args, **kwargs):
        messages.info(request, f"Redirected to BOM Item Edit for BOM ID: {bom_id}. (Placeholder)")
        return super().get(request, *args, **kwargs)

class DownloadFileView(TemplateView):
    """
    Handles file downloads (drawing/spec sheet).
    Corresponds to Controls/DownloadFile.aspx.
    """
    def get(self, request, pk, *args, **kwargs):
        # pk here corresponds to ItemMaster.id (from lblItemId)
        table_name = request.GET.get('tbl')
        field_data = request.GET.get('qfd') # e.g., 'FileData', 'AttData'
        field_name = request.GET.get('qfn') # e.g., 'FileName', 'AttName'
        content_type_field = request.GET.get('qct') # e.g., 'ContentType', 'AttContentType'
        
        if table_name == 'tblDG_Item_Master':
            try:
                item = get_object_or_404(ItemMaster, id=pk)
                file_data = getattr(item, field_data, None)
                download_file_name = getattr(item, field_name, f'item_{pk}_file')
                mime_type = getattr(item, content_type_field, 'application/octet-stream')

                if file_data:
                    response = HttpResponse(file_data, content_type=mime_type)
                    response['Content-Disposition'] = f'attachment; filename="{download_file_name}"'
                    return response
                else:
                    messages.warning(request, "File data not found.")
            except ItemMaster.DoesNotExist:
                messages.error(request, "Item not found for download.")
            except AttributeError:
                messages.error(request, "Invalid file field specified.")
        else:
            messages.error(request, "Invalid table for download specified.")
        
        # Redirect back to the BOM list, preserving the WO number
        wo_no = request.GET.get('WONo', '')
        return redirect(reverse_lazy('bom_master_list') + f"?WONo={wo_no}")


class BOMUploadDrawingView(TemplateView):
    """Placeholder for Module/Design/Transactions/BOM_UploadDrw.aspx."""
    template_name = 'design/placeholder_page.html'
    def get(self, request, pk, *args, **kwargs): # pk here corresponds to ItemMaster.id
        wo_no = request.GET.get('WONo', 'N/A')
        img_type = request.GET.get('img', 'unknown')
        messages.info(request, f"Redirected to BOM Upload Drawing for Item ID: {pk}, WO: {wo_no}, Type: {img_type}. (Placeholder)")
        return super().get(request, *args, **kwargs)

class BOMAmdView(TemplateView):
    """Placeholder for Module/Design/Transactions/BOM_Amd.aspx."""
    template_name = 'design/placeholder_page.html'
    def get(self, request, bom_id, *args, **kwargs): # bom_id here corresponds to BOMMaster.id
        wo_no = request.GET.get('WONo', 'N/A')
        item_id = request.GET.get('ItemId', 'N/A')
        messages.info(request, f"Redirected to BOM Amend for BOM ID: {bom_id}, Item ID: {item_id}, WO: {wo_no}. (Placeholder)")
        return super().get(request, *args, **kwargs)

class BOMDesignWOGridUpdateView(TemplateView):
    """Placeholder for BOM_Design_WO_Grid_Update.aspx (Cancel button target)."""
    template_name = 'design/placeholder_page.html'
    def get(self, request, *args, **kwargs):
        messages.info(request, "Redirected to BOM Design WO Grid Update. (Placeholder)")
        return super().get(request, *args, **kwargs)

```

#### 4.4 Templates (`design/templates/design/bom_master/`)

**Task:** Create templates for the list view and the HTMX-loaded table partial.

**Instructions:**
`list.html` will contain the main page structure, including the filter controls and a container for the DataTables partial. `_bom_master_table.html` will render the actual table, designed to be reloaded via HTMX. Visual indentation is added for hierarchical representation.

*   **`design/templates/design/bom_master/list.html`**

    ```html
    {% extends 'core/base.html' %}

    {% block content %}
    <div class="container mx-auto px-4 py-8">
        <div class="flex flex-wrap items-center justify-between mb-6 gap-4">
            <h2 class="text-2xl font-bold text-gray-800">Edit BOM: WO No: <span class="text-indigo-600">{{ wo_no }}</span></h2>
            
            <div class="flex items-center space-x-4">
                <!-- Dropdown Filter -->
                <div x-data="{ filterType: '0' }" class="flex items-center space-x-2">
                    <label for="filterDropdown" class="text-gray-700 font-medium">Filter:</label>
                    <select id="filterDropdown"
                            x-model="filterType"
                            name="filter_type"
                            class="block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            hx-get="{% url 'bom_master_table' %}"
                            hx-target="#bomMasterTable-container"
                            hx-trigger="change"
                            hx-swap="innerHTML"
                            hx-indicator="#table-loading-indicator"
                            hx-vals='js:{wo_no: "{{ wo_no }}", expand_tree: document.getElementById("expandTreeCheckbox").checked}'
                            >
                        <option value="0">All</option>
                        <option value="1">BoughtOut</option>
                        <option value="2">Manufacturing</option>
                    </select>
                </div>

                <!-- Expand Tree Checkbox -->
                <div x-data="{ expandTree: true }" class="flex items-center space-x-2">
                    <input type="checkbox" id="expandTreeCheckbox"
                           x-model="expandTree"
                           class="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                           hx-get="{% url 'bom_master_table' %}"
                           hx-target="#bomMasterTable-container"
                           hx-trigger="change"
                           hx-swap="innerHTML"
                           hx-indicator="#table-loading-indicator"
                           hx-vals='js:{wo_no: "{{ wo_no }}", filter_type: document.getElementById("filterDropdown").value}'
                           >
                    <label for="expandTreeCheckbox" class="text-gray-700 font-medium">Expand Tree</label>
                </div>

                <!-- Cancel Button -->
                <a href="{% url 'bom_design_wo_grid_update' %}" 
                   class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Cancel
                </a>
            </div>
        </div>
        
        <!-- Loading Indicator for HTMX -->
        <div id="table-loading-indicator" class="htmx-indicator text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
            <p class="mt-2 text-indigo-600">Loading BOM data...</p>
        </div>

        <!-- HTMX Container for the DataTables -->
        <div id="bomMasterTable-container"
             hx-trigger="load once" {# Load once on page load #}
             hx-get="{% url 'bom_master_table' %}?wo_no={{ wo_no }}&filter_type=0&expand_tree=true" {# Initial load with defaults #}
             hx-swap="innerHTML">
            <!-- DataTables content will be loaded here -->
        </div>

        <!-- Placeholder for messages -->
        {% if messages %}
            <div class="mt-4">
                {% for message in messages %}
                    <div class="p-3 mb-2 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

    </div>
    {% endblock %}

    {% block extra_js %}
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script> {# Include Tailwind CSS CDN for convenience, though ideally compiled #}
    {% endblock %}
    ```

*   **`design/templates/design/bom_master/_bom_master_table.html`**

    ```html
    <div class="overflow-x-auto bg-white shadow-md rounded-lg p-4">
        <table id="bomMasterTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S.No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Drw/Image</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Amd No</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for obj in bom_masters %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                        <span style="padding-left: {{ obj.depth|add:'0'|mul:20 }}px;">{{ obj.item_code }}</span>
                    </td>
                    <td class="py-2 px-4 text-sm text-gray-900">{{ obj.description }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">{{ obj.uom }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ obj.unit_qty }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ obj.bom_qty }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                        {% if obj.file_name_display == 'View' %}
                            <a href="{% url 'download_file' obj.item_id %}?tbl=tblDG_Item_Master&qfd=FileData&qfn=FileName&qct=ContentType&WONo={{ obj.wo_no }}" 
                               class="text-blue-600 hover:text-blue-900">{{ obj.file_name_display }}</a>
                        {% else %}
                            <a href="{% url 'bom_upload_drw' obj.item_id %}?WONo={{ obj.wo_no }}&img=0"
                               class="text-green-600 hover:text-green-900">{{ obj.file_name_display }}</a>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                        {% if obj.att_name_display == 'View' %}
                            <a href="{% url 'download_file' obj.item_id %}?tbl=tblDG_Item_Master&qfd=AttData&qfn=AttName&qct=AttContentType&WONo={{ obj.wo_no }}"
                               class="text-blue-600 hover:text-blue-900">{{ obj.att_name_display }}</a>
                        {% else %}
                            <a href="{% url 'bom_upload_drw' obj.item_id %}?WONo={{ obj.wo_no }}&img=1"
                               class="text-green-600 hover:text-green-900">{{ obj.att_name_display }}</a>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                        {% if obj.amd_no %}
                        <a href="{% url 'bom_amd' obj.id %}?WONo={{ obj.wo_no }}&ItemId={{ obj.item_id }}" 
                           class="text-purple-600 hover:text-purple-900">{{ obj.amd_no }}</a>
                        {% else %}
                            N/A
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                        <!-- Select/Edit Button -->
                        {% if obj.p_id == 0 %} {# Root item (Assembly) #}
                            <a href="{% url 'bom_assembly_edit' obj.id %}?CId={{ obj.c_id }}&WONo={{ obj.wo_no }}&ItemId={{ obj.item_id }}&PgUrl=bom_master_list&ModId=3&SubModId=26"
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded-md text-xs">
                                Sel
                            </a>
                        {% else %} {# Child item #}
                            <a href="{% url 'bom_item_edit' obj.id %}?CId={{ obj.c_id }}&WONo={{ obj.wo_no }}&ItemId={{ obj.item_id }}&PgUrl=bom_master_list&ModId=3&SubModId=26"
                               class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-1 px-2 rounded-md text-xs">
                                Sel
                            </a>
                        {% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="10" class="py-4 text-center text-gray-500">No BOM entries found for the selected Work Order.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Only initialize DataTables if it hasn't been initialized on this table
        if (!$.fn.DataTable.isDataTable('#bomMasterTable')) {
            $('#bomMasterTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6, 7, 9] } // Disable ordering for S.No, Drw/Image, Spec. Sheet, Actions
                ]
            });
        }
    });

    // Re-initialize DataTables when HTMX swaps in new content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'bomMasterTable-container') {
            // Destroy existing DataTable instance if it exists before re-initializing
            var table = $('#bomMasterTable').DataTable();
            if (table) {
                table.destroy();
            }
            $('#bomMasterTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, 6, 7, 9] } 
                ]
            });
        }
    });
    </script>
    ```

*   **`design/templates/design/placeholder_page.html`**
    (This simple template serves as a generic page for the redirects, indicating the successful navigation to the next module/page.)

    ```html
    {% extends 'core/base.html' %}

    {% block content %}
    <div class="container mx-auto px-4 py-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Placeholder Page</h2>
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <p class="text-lg text-gray-700">This page serves as a placeholder for a redirected action.</p>
            <p class="text-md text-gray-600 mt-2">In a full migration, this would be a dedicated page with specific functionality.</p>
            <p class="mt-4">
                <a href="{% url 'bom_master_list' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                    Go Back to BOM List
                </a>
            </p>
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="p-3 mb-2 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
    </div>
    {% endblock %}
    ```

#### 4.5 URLs (`design/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are clearly named and structured. `pk` is used for `ItemMaster.id` based actions (download/upload), while `bom_id` is used for `BOMMaster.id` based actions (edit/amend).

```python
from django.urls import path
from .views import (
    BOMMasterListView,
    BOMMasterTablePartialView,
    BOMAssemblyEditView,
    BOMItemEditView,
    DownloadFileView,
    BOMUploadDrawingView,
    BOMAmdView,
    BOMDesignWOGridUpdateView
)

urlpatterns = [
    # Main BOM Tree Edit Page
    # Accepts 'WONo' as a query parameter in the URL
    path('bom-design-wo-tree-edit/', BOMMasterListView.as_view(), name='bom_master_list'),
    
    # HTMX partial endpoint for the DataTables content
    # This endpoint receives filtering parameters via HTMX calls
    path('bom-design-wo-tree-edit/table/', BOMMasterTablePartialView.as_view(), name='bom_master_table'),

    # Redirect targets for the 'Sel' (Select/Edit) command
    # bom_id refers to BOMMaster.id
    path('bom-design-assembly-edit/<int:bom_id>/', BOMAssemblyEditView.as_view(), name='bom_assembly_edit'),
    path('bom-design-item-edit/<int:bom_id>/', BOMItemEditView.as_view(), name='bom_item_edit'),
    
    # Redirect targets for file Download/Upload actions
    # pk here refers to ItemMaster.id (which is ItemId in the original BOM data)
    path('download-file/<int:pk>/', DownloadFileView.as_view(), name='download_file'),
    path('bom-upload-drw/<int:pk>/', BOMUploadDrawingView.as_view(), name='bom_upload_drw'),
    
    # Redirect target for 'Amd' (Amend) command
    # bom_id refers to BOMMaster.id
    path('bom-amd/<int:bom_id>/', BOMAmdView.as_view(), name='bom_amd'),

    # Redirect target for the 'Cancel' button
    path('bom-design-wo-grid-update/', BOMDesignWOGridUpdateView.as_view(), name='bom_design_wo_grid_update'),
]
```

#### 4.6 Tests (`design/tests.py`)

**Task:** Write tests for the models and views to ensure functionality and data integrity.

**Instructions:**
Tests cover model methods, manager logic (especially `get_bom_tree_data`), and view responses, including HTMX interactions. Data is carefully mocked to simulate the database.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection # For checking if tables are managed=False
from django.contrib.messages import get_messages
import io # For mocking file data

# Import models
from .models import UnitMaster, ItemMaster, BOMMaster, BOMMasterManager

class ModelExistenceAndManagementTest(TestCase):
    """
    Tests to ensure models are correctly mapped to existing tables
    and not managed by Django migrations.
    """
    def test_model_managed_false(self):
        self.assertFalse(UnitMaster._meta.managed)
        self.assertFalse(ItemMaster._meta.managed)
        self.assertFalse(BOMMaster._meta.managed)

    def test_db_table_names(self):
        self.assertEqual(UnitMaster._meta.db_table, 'Unit_Master')
        self.assertEqual(ItemMaster._meta.db_table, 'tblDG_Item_Master')
        self.assertEqual(BOMMaster._meta.db_table, 'tblDG_BOM_Master')

class UnitMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        UnitMaster.objects.create(id=1, symbol='MTS')
        UnitMaster.objects.create(id=2, symbol='PCS')

    def test_unit_creation(self):
        unit = UnitMaster.objects.get(id=1)
        self.assertEqual(unit.symbol, 'MTS')
        self.assertEqual(str(unit), 'MTS')

    def test_verbose_name_plural(self):
        self.assertEqual(str(UnitMaster._meta.verbose_name_plural), 'Units')

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a UnitMaster first as it's a FK
        UnitMaster.objects.create(id=101, symbol='KGS')
        
        # Create ItemMaster instances
        # Item with CId (BoughtOut)
        ItemMaster.objects.create(
            id=1, item_code='BO-001', uom_basic_id=101, file_name='drawing.pdf',
            att_name='spec.doc', part_no='P001', manf_desc='Bought Out Item 1', c_id=1,
            file_data=b'pdf_content', content_type='application/pdf',
            att_data=b'doc_content', att_content_type='application/msword'
        )
        # Item without CId (Manufacturing)
        ItemMaster.objects.create(
            id=2, item_code='MF-001', uom_basic_id=101, file_name=None,
            att_name=None, part_no='P002', manf_desc='Manufacturing Item 1', c_id=None
        )

    def test_item_creation(self):
        item_bo = ItemMaster.objects.get(id=1)
        self.assertEqual(item_bo.item_code, 'BO-001')
        self.assertEqual(item_bo.uom_basic.symbol, 'KGS')
        self.assertEqual(item_bo.c_id, 1)

        item_mf = ItemMaster.objects.get(id=2)
        self.assertEqual(item_mf.item_code, 'MF-001')
        self.assertIsNone(item_mf.c_id)

    def test_get_display_item_code(self):
        item_bo = ItemMaster.objects.get(id=1)
        self.assertEqual(item_bo.get_display_item_code(), 'BO-001') # CId is not null

        item_mf = ItemMaster.objects.get(id=2)
        self.assertEqual(item_mf.get_display_item_code(), 'P002') # CId is null, returns PartNo

    def test_get_drawing_display(self):
        item_bo = ItemMaster.objects.get(id=1)
        self.assertEqual(item_bo.get_drawing_display(), 'View')

        item_mf = ItemMaster.objects.get(id=2)
        self.assertEqual(item_mf.get_drawing_display(), 'Upload')

    def test_get_spec_sheet_display(self):
        item_bo = ItemMaster.objects.get(id=1)
        self.assertEqual(item_bo.get_spec_sheet_display(), 'View')

        item_mf = ItemMaster.objects.get(id=2)
        self.assertEqual(item_mf.get_spec_sheet_display(), 'Upload')

class BOMMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup prerequisite data for BOMMaster
        UnitMaster.objects.create(id=201, symbol='NOS')
        ItemMaster.objects.create(id=101, item_code='ASSY-001', uom_basic_id=201, part_no='PA-001', manf_desc='Main Assembly', c_id=1) # Root Assembly
        ItemMaster.objects.create(id=102, item_code='SUB-001', uom_basic_id=201, part_no='PB-001', manf_desc='Sub Assembly', c_id=1) # Sub-assembly
        ItemMaster.objects.create(id=103, item_code='PART-001', uom_basic_id=201, part_no='PC-001', manf_desc='Component Part', c_id=None) # Part (Manufacturing)
        ItemMaster.objects.create(id=104, item_code='PART-002', uom_basic_id=201, part_no='PD-001', manf_desc='Another Component', c_id=2) # Part (BoughtOut)

        # Create BOM structure for WO_TEST
        # Level 0 (Root)
        BOMMaster.objects.create(id=1, item_id=101, wo_no='WO_TEST', p_id=0, c_id=1001, qty=1.0, comp_id=1, fin_year_id=2023)
        # Level 1 children of 1001 (ASSY-001)
        BOMMaster.objects.create(id=2, item_id=102, wo_no='WO_TEST', p_id=1001, c_id=1002, qty=2.0, comp_id=1, fin_year_id=2023) # Sub-assembly
        BOMMaster.objects.create(id=3, item_id=103, wo_no='WO_TEST', p_id=1001, c_id=1003, qty=3.0, comp_id=1, fin_year_id=2023) # Component Part
        # Level 2 children of 1002 (SUB-001)
        BOMMaster.objects.create(id=4, item_id=104, wo_no='WO_TEST', p_id=1002, c_id=1004, qty=0.5, comp_id=1, fin_year_id=2023) # Another Component

        # Another WO for testing filters
        BOMMaster.objects.create(id=5, item_id=104, wo_no='WO_OTHER', p_id=0, c_id=2001, qty=1.0, comp_id=1, fin_year_id=2023)


    def test_bom_master_creation(self):
        bom_entry = BOMMaster.objects.get(id=1)
        self.assertEqual(bom_entry.wo_no, 'WO_TEST')
        self.assertEqual(bom_entry.item.item_code, 'ASSY-001')
        self.assertEqual(float(bom_entry.qty), 1.0)
        self.assertEqual(bom_entry.p_id, 0)
        self.assertEqual(bom_entry.c_id, 1001)

    def test_get_bom_tree_data_all(self):
        bom_data = BOMMaster.objects.get_bom_tree_data(wo_no='WO_TEST', comp_id=1, fin_year_id=2023, drp_value=0)
        
        # Expected structure (flattened, with depth and calculated BOM Qty)
        # Order should be depth-first traversal, then by item code/part no at each level.
        self.assertEqual(len(bom_data), 4)

        # Check root item
        root_item = bom_data[0]
        self.assertEqual(root_item['item_code'], 'ASSY-001')
        self.assertEqual(root_item['depth'], 0)
        self.assertEqual(root_item['unit_qty'], '1.000')
        self.assertEqual(root_item['bom_qty'], '1.000') # 1.0 * 1 = 1.0 (qty itself)

        # Check sub-assembly (child of root)
        sub_assembly = bom_data[1]
        self.assertEqual(sub_assembly['item_code'], 'SUB-001')
        self.assertEqual(sub_assembly['depth'], 1)
        self.assertEqual(sub_assembly['unit_qty'], '2.000')
        self.assertEqual(sub_assembly['bom_qty'], '2.000') # 2.0 * 1.0 (parent qty) = 2.0

        # Check component part (child of root)
        component_part = bom_data[2]
        self.assertEqual(component_part['item_code'], 'PART-001')
        self.assertEqual(component_part['depth'], 1)
        self.assertEqual(component_part['unit_qty'], '3.000')
        self.assertEqual(component_part['bom_qty'], '3.000') # 3.0 * 1.0 (parent qty) = 3.0

        # Check another component (child of sub-assembly)
        another_component = bom_data[3]
        self.assertEqual(another_component['item_code'], 'PART-002')
        self.assertEqual(another_component['depth'], 2)
        self.assertEqual(another_component['unit_qty'], '0.500')
        self.assertEqual(another_component['bom_qty'], '1.000') # 0.5 * 2.0 (sub-assembly qty) * 1.0 (root qty) = 1.0

    def test_get_bom_tree_data_boughtout_filter(self):
        # BoughtOut items have c_id IS NOT NULL
        bom_data = BOMMaster.objects.get_bom_tree_data(wo_no='WO_TEST', comp_id=1, fin_year_id=2023, drp_value=1)
        # Expected: ASSY-001 (c_id=1), SUB-001 (c_id=1), PART-002 (c_id=2)
        # PART-001 (c_id=None) should be filtered out.
        self.assertEqual(len(bom_data), 3)
        item_codes = [item['item_code'] for item in bom_data]
        self.assertIn('ASSY-001', item_codes)
        self.assertIn('SUB-001', item_codes)
        self.assertIn('PART-002', item_codes)
        self.assertNotIn('PART-001', item_codes)

    def test_get_bom_tree_data_manufacturing_filter(self):
        # Manufacturing items have c_id IS NULL
        bom_data = BOMMaster.objects.get_bom_tree_data(wo_no='WO_TEST', comp_id=1, fin_year_id=2023, drp_value=2)
        # Expected: Only PART-001 (c_id=None)
        self.assertEqual(len(bom_data), 1)
        self.assertEqual(bom_data[0]['item_code'], 'PART-001')

    def test_get_bom_tree_data_no_wo(self):
        bom_data = BOMMaster.objects.get_bom_tree_data(wo_no='NON_EXISTENT_WO', comp_id=1, fin_year_id=2023, drp_value=0)
        self.assertEqual(len(bom_data), 0)

class BOMMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up a minimal dataset for view tests
        UnitMaster.objects.create(id=301, symbol='EA')
        ItemMaster.objects.create(id=1, item_code='ITEM-A', uom_basic_id=301, part_no='P-A', manf_desc='Test Item A', c_id=1, 
                                  file_name='test_img.png', file_data=b'image_data', content_type='image/png',
                                  att_name='test_spec.pdf', att_data=b'spec_data', att_content_type='application/pdf')
        ItemMaster.objects.create(id=2, item_code='ITEM-B', uom_basic_id=301, part_no='P-B', manf_desc='Test Item B', c_id=None)
        
        # Root BOM item
        BOMMaster.objects.create(id=10, item_id=1, wo_no='WO-001', p_id=0, c_id=100, qty=1.0, comp_id=1, fin_year_id=2023, amd_no='Amd-001')
        # Child BOM item
        BOMMaster.objects.create(id=11, item_id=2, wo_no='WO-001', p_id=100, c_id=101, qty=2.0, comp_id=1, fin_year_id=2023)

    def setUp(self):
        self.client = Client()
        self.wo_no = 'WO-001'
        # Set session data if views rely on it (mocking ASP.NET session)
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_list_view_get_success(self):
        response = self.client.get(reverse('bom_master_list') + f'?WONo={self.wo_no}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bom_master/list.html')
        self.assertContains(response, self.wo_no)
        self.assertContains(response, '<div id="bomMasterTable-container"') # Check for HTMX container

    def test_list_view_get_no_wo_no(self):
        response = self.client.get(reverse('bom_master_list'))
        self.assertEqual(response.status_code, 200)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Work Order Number (WONo) is required.")

    def test_table_partial_view_get_success(self):
        response = self.client.get(reverse('bom_master_table') + f'?wo_no={self.wo_no}&filter_type=0&expand_tree=true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bom_master/_bom_master_table.html')
        self.assertContains(response, 'ITEM-A') # Check if item code is in table
        self.assertContains(response, 'ITEM-B')
        self.assertContains(response, 'id="bomMasterTable"') # Check for table ID

    def test_table_partial_view_get_boughtout_filter(self):
        # ITEM-A (c_id=1) is bought out. ITEM-B (c_id=None) is manufacturing.
        response = self.client.get(reverse('bom_master_table') + f'?wo_no={self.wo_no}&filter_type=1')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ITEM-A')
        self.assertNotContains(response, 'ITEM-B')

    def test_table_partial_view_get_manufacturing_filter(self):
        response = self.client.get(reverse('bom_master_table') + f'?wo_no={self.wo_no}&filter_type=2')
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'ITEM-A')
        self.assertContains(response, 'ITEM-B')
    
    def test_bom_assembly_edit_redirect(self):
        # Test 'Sel' for root (PId=0)
        response = self.client.get(reverse('bom_assembly_edit', args=[10]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/placeholder_page.html')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Redirected to BOM Assembly Edit for BOM ID: 10. (Placeholder)")

    def test_bom_item_edit_redirect(self):
        # Test 'Sel' for child item (PId!=0)
        response = self.client.get(reverse('bom_item_edit', args=[11]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/placeholder_page.html')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Redirected to BOM Item Edit for BOM ID: 11. (Placeholder)")

    def test_download_file_view_success(self):
        # Test download of drawing
        item_id = 1
        response = self.client.get(reverse('download_file', args=[item_id]) + '?tbl=tblDG_Item_Master&qfd=FileData&qfn=FileName&qct=ContentType')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/png')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_img.png"')
        self.assertEqual(response.content, b'image_data')

        # Test download of spec sheet
        response = self.client.get(reverse('download_file', args=[item_id]) + '?tbl=tblDG_Item_Master&qfd=AttData&qfn=AttName&qct=AttContentType')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_spec.pdf"')
        self.assertEqual(response.content, b'spec_data')

    def test_download_file_view_no_data(self):
        # Item 2 has no files
        response = self.client.get(reverse('download_file', args=[2]) + '?tbl=tblDG_Item_Master&qfd=FileData&qfn=FileName&qct=ContentType')
        self.assertEqual(response.status_code, 302) # Redirects back to list on error
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "File data not found.")

    def test_download_file_view_item_not_found(self):
        response = self.client.get(reverse('download_file', args=[999]) + '?tbl=tblDG_Item_Master&qfd=FileData&qfn=FileName&qct=ContentType')
        self.assertEqual(response.status_code, 302)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Item not found for download.")

    def test_upload_drawing_redirect(self):
        item_id = 1
        response = self.client.get(reverse('bom_upload_drw', args=[item_id]) + f'?WONo={self.wo_no}&img=0')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/placeholder_page.html')
        messages = list(get_messages(response.wsgi_request))
        self.assertIn("Redirected to BOM Upload Drawing", str(messages[0]))

    def test_bom_amd_redirect(self):
        bom_id = 10
        item_id = 1
        response = self.client.get(reverse('bom_amd', args=[bom_id]) + f'?WONo={self.wo_no}&ItemId={item_id}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/placeholder_page.html')
        messages = list(get_messages(response.wsgi_request))
        self.assertIn("Redirected to BOM Amend", str(messages[0]))

    def test_cancel_button_redirect(self):
        response = self.client.get(reverse('bom_design_wo_grid_update'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/placeholder_page.html')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Redirected to BOM Design WO Grid Update. (Placeholder)")

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
HTMX is used to dynamically update the DataTables partial without full page reloads. Alpine.js manages simple UI state (like checkbox and dropdown values).

*   **HTMX:**
    *   The `filterDropdown` and `expandTreeCheckbox` both have `hx-get` attributes targeting `{% url 'bom_master_table' %}`.
    *   They use `hx-trigger="change"` to initiate the request on selection/check.
    *   `hx-target="#bomMasterTable-container"` ensures the table content is swapped.
    *   `hx-swap="innerHTML"` replaces the entire content of the container.
    *   `hx-indicator` is used for a loading spinner.
    *   `hx-vals` dynamically passes the current values of other form elements (`wo_no`, `filter_type`, `expand_tree`) to the HTMX request.
    *   The `bomMasterTable-container` uses `hx-trigger="load once"` to load the table initially on page load.
    *   JavaScript is used to ensure DataTables is re-initialized correctly after HTMX swaps content.

*   **Alpine.js:**
    *   `x-data` is used on the filter `select` and `checkbox` to manage their `x-model` state, providing simple two-way data binding for the UI elements.
    *   No complex Alpine.js components are needed for this specific page beyond simple state management.

*   **DataTables:**
    *   The `_bom_master_table.html` partial includes the JavaScript to initialize DataTables on the `bomMasterTable` ID.
    *   It handles client-side pagination, searching, and sorting.
    *   Crucially, `document.body.addEventListener('htmx:afterSwap', ...)` is added to ensure DataTables is correctly re-initialized when HTMX injects new table content, preventing issues with existing instances.

*   **Front-end Design:**
    *   Tailwind CSS classes are used throughout the templates for modern, responsive styling, adhering to the AutoERP guidelines.
    *   The tree structure is visually conveyed through `padding-left` based on the `obj.depth` value, providing an immediate visual hierarchy.

---

### Final Notes

This comprehensive plan provides a robust foundation for migrating the ASP.NET `BOM_Design_WO_TreeView_Edit.aspx` functionality to Django. Key considerations:

*   **Data Fidelity:** The `BOMMasterManager.get_bom_tree_data` method is critical and replicates complex data processing. Thorough testing (as provided) is essential to ensure its accuracy.
*   **Scalability:** Moving complex data processing to the fat model and leveraging HTMX reduces server load on subsequent requests, improving responsiveness and scalability.
*   **User Experience:** HTMX and DataTables provide a modern, dynamic user experience without full page reloads, similar to a single-page application but with simpler implementation.
*   **Extensibility:** The modular Django design makes it easy to extend functionality, for example, by replacing placeholder views with full-fledged Django CRUD views for editing BOM items or handling file uploads.
*   **Automation Focus:** The clear breakdown into distinct files and explicit instructions makes this plan highly suitable for automated code generation and integration within an AI-assisted migration pipeline.