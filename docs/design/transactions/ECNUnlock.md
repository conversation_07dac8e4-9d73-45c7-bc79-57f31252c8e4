This Django modernization plan outlines the transition of your ASP.NET ECN Unlock module to a modern Django 5.0+ application. It prioritizes AI-assisted automation by providing clear, structured code blocks and instructions that can be adapted and executed through automated processes. The focus is on business value, efficiency, and maintaining a robust, scalable architecture.

## ASP.NET to Django Conversion Script: ECN Unlock Module

This document details the conversion of the `ECNUnlock.aspx` module to a Django application, emphasizing automated, structured approaches.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   **NEVER** include `base.html` template code in your output - assume it already exists.
*   Focus **ONLY** on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

## Conversion Steps:

For the purpose of the structured template, we will consider `ECNEntry` as our primary `[MODEL_NAME]` because it represents the core entity (`tblDG_ECN_Master`) being updated by the "Unlock ECN" action. The displayed list is a derived set of data related to `ECNEntry` and associated items.

### Step 1: Extract Database Schema

Based on the provided ASP.NET code, the following database tables and their relevant columns are identified. These will be mapped to Django models.

**Primary Tables Involved:**

*   **`tblDG_ECN_Master`**:
    *   `Id` (PK, int)
    *   `ItemId` (FK, int)
    *   `WONo` (string)
    *   `Flag` (int, `0` for unlockable, `1` for unlocked)
    *   `CompId` (int)
*   **`tblDG_Item_Master`**:
    *   `Id` (PK, int, maps to `tblDG_ECN_Master.ItemId`)
    *   `ItemCode` (string)
    *   `ManfDesc` (string)
    *   `UOMBasic` (FK, int)
*   **`Unit_Master`**:
    *   `Id` (PK, int, maps to `tblDG_Item_Master.UOMBasic`)
    *   `Symbol` (string)
*   **`tblDG_ECN_Details`**:
    *   `MId` (FK, int, maps to `tblDG_ECN_Master.Id`)
    *   `ECNReason` (FK, int)
    *   `Remarks` (string)
*   **`tblDG_ECN_Reason`**:
    *   `Id` (PK, int, maps to `tblDG_ECN_Details.ECNReason`)
    *   `Types` (string)
*   **`tblDG_BOM_Master`**:
    *   `ItemId` (FK, int)
    *   `WONo` (string)
    *   `CompId` (int)
    *   `ECNFlag` (int, `0` for unlocked, `1` for locked)
*   **`tblCompany_master`**:
    *   `CompId` (PK, int)
    *   `MailServerIp` (string)
    *   `ErpSysmail` (string)

### Step 2: Identify Backend Functionality

The ASP.NET module primarily performs a "read-and-bulk-update" operation with an email notification.

*   **Read (Display List):**
    *   Fetches distinct `ItemId`s from `tblDG_ECN_Master` where `WONo` matches the query string parameter and `Flag` is `0`.
    *   Joins with `tblDG_Item_Master` for `ItemCode` and `ManfDesc`.
    *   Joins with `Unit_Master` for `UOM` (`Symbol`).
    *   Calculates `BOMQty` using an external function (`fun.AllComponentBOMQty`). This implies a specific business rule for Bill of Materials quantity.
    *   Aggregates `Reason` (`Types` from `tblDG_ECN_Reason`) and `Remarks` from multiple `tblDG_ECN_Details` entries associated with each `ItemId` and `WONo`.
    *   The result is presented in a `GridView`.
*   **Update (Unlock ECN):**
    *   Triggered by the "Unlock ECN" button (`Button1_Click`).
    *   For each selected item in the `GridView` (identified by `ItemId`):
        *   Updates `ECNFlag` to `0` in `tblDG_BOM_Master` for the specific `ItemId`, `WONo`, and `CompId`.
        *   Updates `Flag` to `1` in `tblDG_ECN_Master` for the specific `ItemId`, `WONo`, and `CompId`.
    *   **Email Notification:**
        *   After successful updates, gathers details of unlocked items into an HTML table.
        *   Fetches mail server configuration (`MailServerIp`, `ErpSysmail`) from `tblCompany_master`.
        *   Sends an email to predefined recipients (BCC `<EMAIL>,<EMAIL>,<EMAIL>`) with the summary of unlocked items.
*   **Navigation:**
    *   "Cancel" button redirects to `ECN_WO.aspx`.
    *   "Unlock ECN" refreshes the current page after action.
*   **UI Interactions:** Checkbox for select all/deselect all items.

### Step 3: Infer UI Components

The ASP.NET `GridView` will be replaced by a modern DataTables implementation. The various `asp:Label` controls will be dynamic data points in the table. Buttons will become standard HTML buttons with HTMX attributes. Checkbox logic for select-all will be handled by Alpine.js.

### Step 4: Generate Django Code

We will create a new Django application, `ecnunlock`, to house this functionality.

#### 4.1 Models (`ecnunlock/models.py`)

Here we define the Django models that map to your existing database tables. The core business logic for fetching and updating ECN items will be embedded as methods within the `ECNEntry` model (or a custom manager) to adhere to the "Fat Model" principle.

```python
from django.db import models, transaction
from django.db.models import F, Value, CharField
from django.db.models.functions import Concat
from django.core.mail import EmailMessage
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

# Helper classes/functions from original ASP.NET
# In a real scenario, this would be refactored into a services layer
# or a custom manager to encapsulate specific business logic.

class ECNEntryManager(models.Manager):
    """
    Custom manager for ECNEntry to encapsulate complex data retrieval and update logic.
    """
    def get_unlockable_items_data(self, won_o: str, comp_id: int):
        """
        Replicates the loaddata() logic from ASP.NET code-behind to fetch
        ECN items eligible for unlocking for a given Work Order.
        This method is designed to be a "fat model" component.
        """
        # Step 1: Get distinct ItemIds and basic item info from ECNMaster, ItemMaster, UnitMaster
        # Equivalent to the initial StrSql query
        initial_queryset = self.filter(
            wono=won_o,
            flag=0, # Flag=0 means it's unlockable
            compid=comp_id
        ).select_related('itemid_master', 'itemid_master__uombasic_unit').values(
            'itemid',
            item_code=F('itemid_master__itemcode'),
            manf_desc=F('itemid_master__manfdesc'),
            uom_symbol=F('itemid_master__uombasic_unit__symbol')
        ).distinct()

        results_data = []

        for row in initial_queryset:
            item_id = row['itemid']
            # Step 2: Calculate BOMQty (simulated, needs actual implementation logic)
            # In C#, this was fun.AllComponentBOMQty(CompId, WONo, ItemId, FinYearId)
            # This would likely involve querying tblDG_BOM_Master or similar logic.
            # For demonstration, we'll use a placeholder or assume a simple lookup.
            # Replace with actual complex logic based on your BOM structure.
            try:
                bom_entry = BOMMaster.objects.get(itemid=item_id, wono=won_o, compid=comp_id)
                bom_qty = bom_entry.bomqty # Assuming BOMMaster has BOMQty, needs to be verified.
                                           # The original code's BOMQty is complex, "fun.AllComponentBOMQty".
                                           # For now, it's a placeholder. A dedicated service/function
                                           # in a services.py or within a custom manager would be ideal.
            except BOMMaster.DoesNotExist:
                bom_qty = 0 # Default if no BOM entry exists or logic not found

            # Step 3: Aggregate Reason and Remarks from ECN Details
            # This replicates the nested loops in loaddata()
            ecn_ids_for_item = ECNEntry.objects.filter(
                wono=won_o,
                itemid=item_id,
                compid=comp_id
            ).values_list('id', flat=True)

            reasons = []
            remarks = []

            for ecn_id in ecn_ids_for_item:
                details = ECNDetail.objects.filter(
                    mid=ecn_id
                ).select_related('ecnreason_reason')

                for detail in details:
                    if detail.ecnreason_reason:
                        reasons.append(detail.ecnreason_reason.types)
                    if detail.remarks:
                        remarks.append(detail.remarks)

            # Combine and clean aggregated strings
            final_reason = ", ".join(filter(None, reasons))
            final_remarks = ", ".join(filter(None, remarks))

            results_data.append({
                'itemid': item_id,
                'item_code': row['item_code'],
                'manf_desc': row['manf_desc'],
                'uom': row['uom_symbol'],
                'bom_qty': bom_qty,
                'reason': final_reason,
                'remarks': final_remarks,
            })
        return results_data

    @transaction.atomic
    def unlock_items_and_notify(self, won_o: str, selected_item_ids: list, comp_id: int):
        """
        Replicates the Button1_Click() logic, performing updates and sending email.
        This method ensures atomicity for database updates and separates email logic.
        """
        unlocked_items_summary = []
        for item_id in selected_item_ids:
            # Update tblDG_BOM_Master: ECNFlag=0
            BOMMaster.objects.filter(
                itemid=item_id,
                wono=won_o,
                compid=comp_id
            ).update(ecnflag=0)

            # Update tblDG_ECN_Master: Flag=1
            ECNEntry.objects.filter(
                itemid=item_id,
                wono=won_o,
                compid=comp_id
            ).update(flag=1)

            # Collect data for email summary
            # Fetch the most up-to-date data for the item after unlocking
            item_data = self.get_unlockable_items_data(won_o, comp_id)
            unlocked_item = next((item for item in item_data if item['itemid'] == item_id), None)
            if unlocked_item:
                unlocked_items_summary.append(unlocked_item)

        if unlocked_items_summary:
            self._send_unlock_notification_email(won_o, unlocked_items_summary, comp_id)

        return True

    def _send_unlock_notification_email(self, won_o: str, unlocked_items: list, comp_id: int):
        """
        Helper method to send email notification.
        Abstracted to allow for different email backends/templates.
        """
        try:
            company_info = CompanyMaster.objects.get(compid=comp_id)
            smtp_server = company_info.mailserverip
            erp_mail_from = company_info.erpsysmail
        except CompanyMaster.DoesNotExist:
            logger.error(f"CompanyMaster with CompId {comp_id} not found for email config.")
            smtp_server = settings.EMAIL_HOST # Fallback to Django settings
            erp_mail_from = settings.DEFAULT_FROM_EMAIL # Fallback to Django settings

        # Construct HTML table for email body
        html_body = "<table width='100%' border='1' style='font-size:10pt'>"
        
        # Add header row
        headers = ["SRNo", "ItemCode", "Description", "UOM", "BOMQty", "Reason", "Remarks"]
        html_body += "<tr>"
        for header in headers:
            html_body += f"<td align='center'>{header}</td>"
        html_body += "</tr>"

        # Add data rows
        for i, item in enumerate(unlocked_items):
            html_body += "<tr>"
            html_body += f"<td>{i+1}</td>" # SRNo
            html_body += f"<td>{item.get('item_code', '')}</td>"
            html_body += f"<td>{item.get('manf_desc', '')}</td>"
            html_body += f"<td>{item.get('uom', '')}</td>"
            html_body += f"<td>{item.get('bom_qty', '')}</td>"
            html_body += f"<td>{item.get('reason', '')}</td>"
            html_body += f"<td>{item.get('remarks', '')}</td>"
            html_body += "</tr>"
        html_body += "</table>"

        subject = "ECN Unlock"
        body = (
            f"Work Order No : {won_o}<br><br>"
            f"{html_body}<br><br><br>"
            "This is Auto generated mail by ERP system, please do not reply.<br><br> Thank you."
        )

        # Recipients (hardcoded as per original ASP.NET)
        # In a real system, these would be configurable, perhaps from DB or settings.
        # For testing, you might want to direct these to a test email.
        bcc_recipients = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
        
        try:
            email = EmailMessage(
                subject,
                body,
                from_email=erp_mail_from,
                to=[], # No direct 'To' recipient as per original, only BCC
                bcc=bcc_recipients,
            )
            email.content_subtype = "html"
            email.send()
            logger.info(f"ECN Unlock email sent for WONo: {won_o}")
        except Exception as e:
            logger.error(f"Failed to send ECN Unlock email for WONo {won_o}: {e}", exc_info=True)


class ECNEntry(models.Model):
    # Id is implicitly added by Django as an AutoField primary key
    itemid = models.IntegerField(db_column='ItemId')
    wono = models.CharField(db_column='WONo', max_length=255) # Assuming max_length
    flag = models.IntegerField(db_column='Flag')
    compid = models.IntegerField(db_column='CompId')

    # Manager for custom methods
    objects = ECNEntryManager()

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Master'
        verbose_name = 'ECN Entry'
        verbose_name_plural = 'ECN Entries'

    def __str__(self):
        return f"ECN for WONo: {self.wono}, ItemId: {self.itemid}"

    # Define explicit relationships if they are not just IDs but actual FKs in DB
    # These would typically be defined if Django migrations are managing the schema
    # For managed=False, they are just for ORM convenience and querying
    # Assumes 'Id' in related tables is the primary key.
    itemid_master = models.ForeignKey('ItemMaster', on_delete=models.DO_NOTHING, db_column='ItemId', related_name='ecn_entries')
    compid_company = models.ForeignKey('CompanyMaster', on_delete=models.DO_NOTHING, db_column='CompId', related_name='ecn_entries')


class ItemMaster(models.Model):
    itemcode = models.CharField(db_column='ItemCode', max_length=255)
    manfdesc = models.CharField(db_column='ManfDesc', max_length=500)
    uombasic = models.IntegerField(db_column='UOMBasic')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.itemcode
    
    uombasic_unit = models.ForeignKey('UnitMaster', on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')


class UnitMaster(models.Model):
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol


class ECNDetail(models.Model):
    mid = models.IntegerField(db_column='MId') # Foreign key to ECNEntry.Id
    ecnreason = models.IntegerField(db_column='ECNReason') # Foreign key to ECNReason.Id
    remarks = models.CharField(db_column='Remarks', max_length=1000, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Details'
        verbose_name = 'ECN Detail'
        verbose_name_plural = 'ECN Details'

    def __str__(self):
        return f"Detail for ECN {self.mid}"
    
    # Explicit FKs for ORM querying
    mid_ecn_entry = models.ForeignKey(ECNEntry, on_delete=models.DO_NOTHING, db_column='MId', related_name='ecn_details')
    ecnreason_reason = models.ForeignKey('ECNReason', on_delete=models.DO_NOTHING, db_column='ECNReason', related_name='ecn_details')


class ECNReason(models.Model):
    types = models.CharField(db_column='Types', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblDG_ECN_Reason'
        verbose_name = 'ECN Reason'
        verbose_name_plural = 'ECN Reasons'

    def __str__(self):
        return self.types


class BOMMaster(models.Model):
    itemid = models.IntegerField(db_column='ItemId')
    wono = models.CharField(db_column='WONo', max_length=255)
    compid = models.IntegerField(db_column='CompId')
    ecnflag = models.IntegerField(db_column='ECNFlag')
    bomqty = models.FloatField(db_column='BOMQty', default=0.0) # Inferred from BOMQty usage

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for WONo: {self.wono}, ItemId: {self.itemid}"


class CompanyMaster(models.Model):
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    mailserverip = models.CharField(db_column='MailServerIp', max_length=255, blank=True, null=True)
    erpsysmail = models.CharField(db_column='ErpSysmail', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

    def __str__(self):
        return f"Company {self.compid}"

```

#### 4.2 Forms (`ecnunlock/forms.py`)

For this specific "unlock" functionality, we don't need a complex `ModelForm` for CRUD. The selection is handled by checkboxes directly in the template, and the selected `item_ids` are sent via HTMX POST. However, if we were to define a form for validation of selected items, it would be a simple `forms.Form`.

```python
from django import forms

class ECNUnlockForm(forms.Form):
    """
    A simple form to handle the selected Item IDs for unlocking.
    In HTMX, we might just pass these as form data directly, but a form
    can provide validation.
    """
    selected_item_ids = forms.MultipleChoiceField(
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'hidden'}),
        required=False,
        label="Items to Unlock"
    )

    def __init__(self, *args, **kwargs):
        # We might dynamically set choices if necessary, though HTMX POST
        # usually sends values directly.
        super().__init__(*args, **kwargs)
        # Example of setting dynamic choices if needed, but for selected IDs,
        # it's usually just receiving the list from the frontend.
        # self.fields['selected_item_ids'].choices = [(item.id, item.item_code) for item in ECNEntry.objects.all()]

```

#### 4.3 Views (`ecnunlock/views.py`)

The ASP.NET page performs both display and an action. In Django, this can be managed by a single `View` class with `get` and `post` methods. We will use a `TemplateView` with custom `get_context_data` and override `post` for the unlock action.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect
from django.conf import settings # For accessing session variables like compid, username, finyear
import json

from .models import ECNEntry # The main model for our fat model methods

class ECNUnlockView(TemplateView):
    template_name = 'ecnunlock/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono = self.request.GET.get('WONo') # Get WONo from query string
        if not wono:
            messages.error(self.request, "Work Order Number (WONo) is missing in the URL.")
            # Optionally redirect to a list of WOs or error page
            # return redirect(reverse_lazy('ecn_wo_list')) # Assuming you have this URL
            return context # Or raise Http404

        # Dummy session data (replace with actual session retrieval)
        # In a real system, compid, username, finyear would be securely retrieved from Django session
        # after user authentication. Example: self.request.session.get('compid')
        comp_id = self.request.session.get('compid', 1) # Default to 1 for testing
        # fin_year_id = self.request.session.get('finyear', 2023) # Not used in this specific function but for fun.AllComponentBOMQty

        context['wono'] = wono
        # Fetch data using the fat model method
        # ECNEntryManager.get_unlockable_items_data() is expected to return the processed list
        context['ecn_items'] = ECNEntry.objects.get_unlockable_items_data(wono, comp_id)
        
        # This view is for the main page, _table.html is for the HTMX loaded table
        return context

    def post(self, request, *args, **kwargs):
        # This handles the "Unlock ECN" action
        wono = request.GET.get('WONo') # Get WONo from query string again
        if not wono:
            messages.error(self.request, "Work Order Number (WONo) is missing for unlock action.")
            return HttpResponseRedirect(request.path_info) # Redirect back to current page

        # Dummy session data (replace with actual session retrieval)
        comp_id = self.request.session.get('compid', 1) # Default to 1 for testing

        # Get selected ItemIds from the POST data
        # Assuming checkboxes send their value as 'selected_item_ids'
        # e.g., <input type="checkbox" name="selected_item_ids" value="{{ item.itemid }}">
        selected_item_ids_str = request.POST.getlist('selected_item_ids')
        selected_item_ids = [int(item_id) for item_id in selected_item_ids_str if item_id.isdigit()]

        if not selected_item_ids:
            messages.warning(request, "No ECN items selected for unlock.")
        else:
            try:
                # Call the fat model method to perform unlock and notification
                ECNEntry.objects.unlock_items_and_notify(wono, selected_item_ids, comp_id)
                messages.success(request, f"Successfully unlocked {len(selected_item_ids)} ECN items.")
            except Exception as e:
                messages.error(request, f"An error occurred during ECN unlock: {e}")
        
        # For HTMX requests, return a 204 No Content and trigger a client-side event
        # to refresh the table.
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshECNUnlockTable': True,
                        'showMessage': messages.get_messages(request) # Pass messages for Alpine.js
                    })
                }
            )
        return HttpResponseRedirect(request.path_info) # Full page redirect for non-HTMX


class ECNUnlockTablePartialView(View):
    """
    View to render only the DataTables partial for HTMX requests.
    """
    def get(self, request, *args, **kwargs):
        wono = request.GET.get('WONo')
        if not wono:
            return HttpResponse("Work Order Number (WONo) is missing for table load.", status=400)

        comp_id = self.request.session.get('compid', 1) # Default to 1 for testing
        ecn_items = ECNEntry.objects.get_unlockable_items_data(wono, comp_id)

        return render(request, 'ecnunlock/_table.html', {'ecn_items': ecn_items, 'wono': wono})

from django.shortcuts import render
from django.views import View # Make sure View is imported

class ECNUnlockCancelView(View):
    """
    Handles the cancel action, redirecting to a different page.
    Equivalent to BtnCancel_Click.
    """
    def get(self, request, *args, **kwargs):
        # Redirects to ECN_WO.aspx, mapped to a Django URL 'ecn_wo_list'
        return redirect(reverse_lazy('ecn_wo_list')) # Assuming 'ecn_wo_list' is the URL name for ECN_WO.aspx

```

#### 4.4 Templates (`ecnunlock/templates/ecnunlock/`)

We'll create `list.html` for the main page and `_table.html` for the HTMX-driven DataTables content.

**`list.html`**: The main page layout.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="ecnUnlock()">
    <h2 class="text-2xl font-bold mb-6">ECN Unlock for WONo: <span class="text-blue-600">{{ wono }}</span></h2>

    {% comment %} Display messages from Django messages framework {% endcomment %}
    <div x-show="showMessage" @showMessage.window="showTemporaryMessage($event.detail)"
         class="mb-4 p-3 rounded-md text-sm transition-all duration-300 ease-in-out"
         :class="{'bg-green-100 text-green-800': messageType === 'success',
                  'bg-red-100 text-red-800': messageType === 'error',
                  'bg-yellow-100 text-yellow-800': messageType === 'warning'}"
         style="display: none;">
        <p x-text="messageText"></p>
    </div>

    <form hx-post="{% url 'ecnunlock_list' %}?WONo={{ wono }}" hx-target="#ecn-table-container"
          hx-swap="innerHTML" hx-trigger="submit">
        {% csrf_token %}
        <input type="hidden" name="wono" value="{{ wono }}">

        <div id="ecn-table-container"
             hx-trigger="load, refreshECNUnlockTable from:body"
             hx-get="{% url 'ecnunlock_table_partial' %}?WONo={{ wono }}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-lg text-gray-700">Loading ECN Data...</p>
            </div>
        </div>

        <div class="mt-6 flex items-center justify-center space-x-4">
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-lg
                       focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 transition duration-150 ease-in-out"
                hx-indicator="#loading-indicator">
                <span class="htmx-indicator mr-2" id="loading-indicator">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
                Unlock ECN
            </button>

            <a href="{% url 'ecn_wo_list' %}"
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-lg
                      focus:outline-none focus:ring-2 focus:ring-gray-400 focus:ring-opacity-50 transition duration-150 ease-in-out">
                Cancel
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for checkbox logic and messages
    document.addEventListener('alpine:init', () => {
        Alpine.data('ecnUnlock', () => ({
            selectAll: false,
            selectedItems: new Set(),
            messageText: '',
            messageType: '',
            showMessage: false,
            init() {
                this.$watch('selectAll', (value) => {
                    document.querySelectorAll('input[name="selected_item_ids"]').forEach(checkbox => {
                        checkbox.checked = value;
                        this.toggleSelection(checkbox.value, value);
                    });
                });
                
                // Listen for custom event to show messages from HTMX response
                this.$el.addEventListener('showMessage', (event) => {
                    if (event.detail && event.detail.messages) {
                        const messages = event.detail.messages;
                        if (messages.length > 0) {
                            const firstMessage = messages[0]; // Take the first message
                            this.messageText = firstMessage.message;
                            this.messageType = firstMessage.tags; // e.g., 'success', 'error'
                            this.showMessage = true;
                            setTimeout(() => this.showMessage = false, 5000); // Hide after 5 seconds
                        }
                    }
                });
            },
            toggleSelection(itemId, isChecked) {
                if (isChecked) {
                    this.selectedItems.add(itemId);
                } else {
                    this.selectedItems.delete(itemId);
                }
            },
            // Method to handle messages from HTMX trigger
            showTemporaryMessage(messages) {
                if (messages && messages.length > 0) {
                    const firstMessage = messages[0];
                    this.messageText = firstMessage.message;
                    this.messageType = firstMessage.tags;
                    this.showMessage = true;
                    setTimeout(() => this.showMessage = false, 5000);
                }
            }
        }));
    });

    // DataTables initialization (deferred until table content is loaded)
    document.addEventListener('DOMContentLoaded', function() {
        // HTMX triggers 'htmx:afterSwap' on target elements after content is swapped
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.target.id === 'ecn-table-container') {
                $('#ecnUnlockTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [0, 1] } // Disable sorting for SN and Checkbox columns
                    ]
                });
                // After table loads, reset selectAll checkbox
                const selectAllCheckbox = document.getElementById('selectAllCheckbox');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = false;
                }
            }
        });
    });
</script>
{% endblock %}
```

**`_table.html`**: Partial for the DataTables content, loaded dynamically via HTMX.

```html
<table id="ecnUnlockTable" class="min-w-full bg-white shadow-md rounded-lg overflow-hidden border border-gray-200">
    <thead class="bg-gray-100 border-b border-gray-200">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-3%">SN</th>
            <th class="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider w-2%">
                <input type="checkbox" id="selectAllCheckbox" x-model="selectAll" class="form-checkbox h-4 w-4 text-blue-600 rounded">
            </th>
            <th class="hidden">ItemId</th>{# Hidden as per original, but still needed for data #}
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-7%">ItemCode</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-35%">Description</th>
            <th class="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider w-5%">UOM</th>
            <th class="py-3 px-4 text-center text-xs font-semibold text-gray-600 uppercase tracking-wider w-5%">BOM Qty</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-20%">Reason</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider w-25%">Remarks</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-100">
        {% if ecn_items %}
            {% for item in ecn_items %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 text-right text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-2 px-4 text-center">
                    <input type="checkbox" name="selected_item_ids" value="{{ item.itemid }}"
                           x-bind:checked="selectAll || selectedItems.has('{{ item.itemid }}')"
                           @change="toggleSelection('{{ item.itemid }}', $event.target.checked)"
                           class="form-checkbox h-4 w-4 text-blue-600 rounded">
                </td>
                <td class="hidden">{{ item.itemid }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-800">{{ item.item_code }}</td>
                <td class="py-2 px-4 text-left text-sm text-gray-800">{{ item.manf_desc }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-800">{{ item.uom }}</td>
                <td class="py-2 px-4 text-center text-sm text-gray-800">{{ item.bom_qty|floatformat:"2" }}</td>
                <td class="py-2 px-4 text-left text-sm text-gray-800">{{ item.reason }}</td>
                <td class="py-2 px-4 text-left text-sm text-gray-800">{{ item.remarks }}</td>
            </tr>
            {% endfor %}
        {% else %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-red-500 font-bold">No data found to display</td>
        </tr>
        {% endif %}
    </tbody>
</table>

```

#### 4.5 URLs (`ecnunlock/urls.py`)

This file defines the URL patterns for your ECN Unlock module.

```python
from django.urls import path
from .views import ECNUnlockView, ECNUnlockTablePartialView, ECNUnlockCancelView

urlpatterns = [
    # Main ECN Unlock page (GET for display, POST for unlock action)
    # The WONo is passed as a query parameter as in original ASP.NET
    path('ecn-unlock/', ECNUnlockView.as_view(), name='ecnunlock_list'),
    
    # HTMX endpoint for refreshing the table content
    path('ecn-unlock/table/', ECNUnlockTablePartialView.as_view(), name='ecnunlock_table_partial'),

    # URL for the "Cancel" button, redirecting to ECN_WO.aspx equivalent
    path('ecn-unlock/cancel/', ECNUnlockCancelView.as_view(), name='ecn_unlock_cancel'),
]
```

#### 4.6 Tests (`ecnunlock/tests.py`)

Comprehensive unit tests for the models (especially the fat model methods) and integration tests for the views are crucial for ensuring correctness and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.core import mail
from unittest.mock import patch, MagicMock

# Import all models to ensure they are tested or can be created as fixtures
from .models import ECNEntry, ItemMaster, UnitMaster, ECNDetail, ECNReason, BOMMaster, CompanyMaster

class ECNEntryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for models
        cls.company = CompanyMaster.objects.create(compid=1, mailserverip='test.smtp.com', erpsysmail='<EMAIL>')
        cls.unit = UnitMaster.objects.create(id=1, symbol='MTS')
        cls.item1 = ItemMaster.objects.create(id=101, itemcode='ITM001', manfdesc='Item One Description', uombasic=cls.unit.id)
        cls.item2 = ItemMaster.objects.create(id=102, itemcode='ITM002', manfdesc='Item Two Description', uombasic=cls.unit.id)
        cls.ecn_reason1 = ECNReason.objects.create(id=1, types='Design Change')
        cls.ecn_reason2 = ECNReason.objects.create(id=2, types='Material Change')

        # Create ECN Entries
        cls.ecn1 = ECNEntry.objects.create(id=1, itemid=cls.item1.id, wono='WO-001', flag=0, compid=cls.company.compid)
        cls.ecn2 = ECNEntry.objects.create(id=2, itemid=cls.item2.id, wono='WO-001', flag=0, compid=cls.company.compid)
        cls.ecn3 = ECNEntry.objects.create(id=3, itemid=cls.item1.id, wono='WO-001', flag=0, compid=cls.company.compid) # Another entry for same item
        cls.ecn_locked = ECNEntry.objects.create(id=4, itemid=cls.item2.id, wono='WO-002', flag=1, compid=cls.company.compid) # Locked ECN

        # Create ECN Details
        ECNDetail.objects.create(id=1, mid=cls.ecn1.id, ecnreason=cls.ecn_reason1.id, remarks='Initial design issue.')
        ECNDetail.objects.create(id=2, mid=cls.ecn3.id, ecnreason=cls.ecn_reason2.id, remarks='Changed material supplier.')
        ECNDetail.objects.create(id=3, mid=cls.ecn2.id, ecnreason=cls.ecn_reason1.id, remarks='Minor fix.')

        # Create BOM Entries
        BOMMaster.objects.create(id=1, itemid=cls.item1.id, wono='WO-001', compid=cls.company.compid, ecnflag=1, bomqty=10.5)
        BOMMaster.objects.create(id=2, itemid=cls.item2.id, wono='WO-001', compid=cls.company.compid, ecnflag=1, bomqty=20.0)
        BOMMaster.objects.create(id=3, itemid=cls.item2.id, wono='WO-002', compid=cls.company.compid, ecnflag=1, bomqty=5.0)


    def setUp(self):
        self.client = Client()
        # Mock session for compid etc.
        session = self.client.session
        session['compid'] = self.company.compid
        session.save()

    def test_get_unlockable_items_data(self):
        """
        Test the ECNEntryManager.get_unlockable_items_data method.
        This tests the 'loaddata' equivalent logic.
        """
        won_o = 'WO-001'
        comp_id = self.company.compid
        items_data = ECNEntry.objects.get_unlockable_items_data(won_o, comp_id)

        self.assertEqual(len(items_data), 2) # Should get distinct items for WO-001 with flag=0

        # Check data for Item1 (ITM001)
        item1_data = next(item for item in items_data if item['itemid'] == self.item1.id)
        self.assertIsNotNone(item1_data)
        self.assertEqual(item1_data['item_code'], self.item1.itemcode)
        self.assertEqual(item1_data['manf_desc'], self.item1.manfdesc)
        self.assertEqual(item1_data['uom'], self.unit.symbol)
        self.assertEqual(item1_data['bom_qty'], 10.5)
        # Reasons/Remarks should be aggregated correctly
        self.assertIn('Design Change', item1_data['reason'])
        self.assertIn('Material Change', item1_data['reason'])
        self.assertIn('Initial design issue', item1_data['remarks'])
        self.assertIn('Changed material supplier', item1_data['remarks'])

        # Check data for Item2 (ITM002)
        item2_data = next(item for item in items_data if item['itemid'] == self.item2.id)
        self.assertIsNotNone(item2_data)
        self.assertEqual(item2_data['item_code'], self.item2.itemcode)
        self.assertEqual(item2_data['manf_desc'], self.item2.manfdesc)
        self.assertEqual(item2_data['uom'], self.unit.symbol)
        self.assertEqual(item2_data['bom_qty'], 20.0)
        self.assertIn('Design Change', item2_data['reason'])
        self.assertIn('Minor fix', item2_data['remarks'])

        # Verify items with flag=1 or different WONo are not included
        self.assertNotIn(self.ecn_locked.itemid, [item['itemid'] for item in items_data])

    @patch('ecnunlock.models.EmailMessage') # Mock EmailMessage to prevent actual email sending
    def test_unlock_items_and_notify(self, mock_email_message):
        """
        Test the ECNEntryManager.unlock_items_and_notify method.
        This tests the 'Button1_Click' equivalent logic including DB updates and email.
        """
        won_o = 'WO-001'
        selected_item_ids = [self.item1.id, self.item2.id]
        comp_id = self.company.compid

        # Initial state checks
        self.assertEqual(ECNEntry.objects.filter(itemid=self.item1.id, wono=won_o, flag=0).count(), 2)
        self.assertEqual(BOMMaster.objects.get(itemid=self.item1.id, wono=won_o).ecnflag, 1)
        self.assertEqual(ECNEntry.objects.filter(itemid=self.item2.id, wono=won_o, flag=0).count(), 1)
        self.assertEqual(BOMMaster.objects.get(itemid=self.item2.id, wono=won_o).ecnflag, 1)

        # Perform the unlock action
        success = ECNEntry.objects.unlock_items_and_notify(won_o, selected_item_ids, comp_id)
        self.assertTrue(success)

        # Verify database updates
        # ECNEntry flag should be 1 (unlocked)
        self.assertEqual(ECNEntry.objects.filter(itemid=self.item1.id, wono=won_o, flag=1).count(), 2)
        self.assertEqual(ECNEntry.objects.filter(itemid=self.item2.id, wono=won_o, flag=1).count(), 1)

        # BOMMaster ECNFlag should be 0 (unlocked)
        self.assertEqual(BOMMaster.objects.get(itemid=self.item1.id, wono=won_o).ecnflag, 0)
        self.assertEqual(BOMMaster.objects.get(itemid=self.item2.id, wono=won_o).ecnflag, 0)

        # Verify email sending
        self.assertEqual(mock_email_message.call_count, 1)
        args, kwargs = mock_email_message.call_args
        email_instance = args[0] # Get the EmailMessage instance that was created
        self.assertEqual(email_instance.subject, "ECN Unlock")
        self.assertIn(f"Work Order No : {won_o}", email_instance.body)
        self.assertIn(self.item1.itemcode, email_instance.body)
        self.assertIn(self.item2.itemcode, email_instance.body)
        self.assertEqual(email_instance.from_email, self.company.erpsysmail)
        self.assertListEqual(email_instance.bcc, ["<EMAIL>", "<EMAIL>", "<EMAIL>"])
        self.assertEqual(email_instance.content_subtype, 'html')

class ECNUnlockViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Re-create setup data for views testing
        cls.company = CompanyMaster.objects.create(compid=1, mailserverip='test.smtp.com', erpsysmail='<EMAIL>')
        cls.unit = UnitMaster.objects.create(id=1, symbol='MTS')
        cls.item1 = ItemMaster.objects.create(id=101, itemcode='ITM001', manfdesc='Item One Description', uombasic=cls.unit.id)
        cls.item2 = ItemMaster.objects.create(id=102, itemcode='ITM002', manfdesc='Item Two Description', uombasic=cls.unit.id)
        cls.ecn_reason1 = ECNReason.objects.create(id=1, types='Design Change')
        cls.ecn_reason2 = ECNReason.objects.create(id=2, types='Material Change')

        cls.ecn1 = ECNEntry.objects.create(id=1, itemid=cls.item1.id, wono='WO-VIEW-001', flag=0, compid=cls.company.compid)
        cls.ecn2 = ECNEntry.objects.create(id=2, itemid=cls.item2.id, wono='WO-VIEW-001', flag=0, compid=cls.company.compid)
        ECNDetail.objects.create(id=1, mid=cls.ecn1.id, ecnreason=cls.ecn_reason1.id, remarks='Initial design issue.')
        ECNDetail.objects.create(id=2, mid=cls.ecn2.id, ecnreason=cls.ecn_reason1.id, remarks='Minor fix.')
        BOMMaster.objects.create(id=1, itemid=cls.item1.id, wono='WO-VIEW-001', compid=cls.company.compid, ecnflag=1, bomqty=10.5)
        BOMMaster.objects.create(id=2, itemid=cls.item2.id, wono='WO-VIEW-001', compid=cls.company.compid, ecnflag=1, bomqty=20.0)


    def setUp(self):
        self.client = Client()
        session = self.client.session
        session['compid'] = self.company.compid # Set compid in session for view logic
        session.save()

    def test_ecn_unlock_view_get(self):
        """Test GET request to ECNUnlockView."""
        response = self.client.get(reverse('ecnunlock_list'), {'WONo': 'WO-VIEW-001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecnunlock/list.html')
        self.assertContains(response, 'WO-VIEW-001') # Check if WONo is displayed
        self.assertIn('ecn_items', response.context)
        self.assertEqual(len(response.context['ecn_items']), 2) # Both items should be listed

    def test_ecn_unlock_view_get_no_wono(self):
        """Test GET request without WONo parameter."""
        response = self.client.get(reverse('ecnunlock_list'))
        self.assertEqual(response.status_code, 200) # Still 200, but message should be present
        messages = list(get_messages(response))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Work Order Number (WONo) is missing in the URL.")

    @patch('ecnunlock.models.ECNEntryManager.unlock_items_and_notify', return_value=True)
    def test_ecn_unlock_view_post_htmx(self, mock_unlock_method):
        """Test POST request with HTMX header for unlock action."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'selected_item_ids': [self.item1.id, self.item2.id]} # Pass selected item IDs
        response = self.client.post(reverse('ecnunlock_list') + '?WONo=WO-VIEW-001', data, **headers)

        # Expect 204 No Content for successful HTMX POST
        self.assertEqual(response.status_code, 204)
        self.assertTrue(mock_unlock_method.called) # Verify the model method was called
        
        # Verify HX-Trigger header
        self.assertIn('HX-Trigger', response.headers)
        trigger_header = json.loads(response.headers['HX-Trigger'])
        self.assertIn('refreshECNUnlockTable', trigger_header)
        self.assertIn('showMessage', trigger_header)

    @patch('ecnunlock.models.ECNEntryManager.unlock_items_and_notify', return_value=True)
    def test_ecn_unlock_view_post_non_htmx(self, mock_unlock_method):
        """Test POST request without HTMX header (full page redirect)."""
        data = {'selected_item_ids': [self.item1.id]}
        response = self.client.post(reverse('ecnunlock_list') + '?WONo=WO-VIEW-001', data)

        # Expect 302 redirect for non-HTMX POST
        self.assertEqual(response.status_code, 302)
        self.assertTrue(mock_unlock_method.called)

        # Verify messages on redirect (follow redirect to check)
        response_follow = self.client.get(response.url)
        messages = list(get_messages(response_follow))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Successfully unlocked 1 ECN items.")

    def test_ecn_unlock_view_post_no_selection(self):
        """Test POST request with no items selected."""
        data = {} # Empty data means no checkboxes selected
        response = self.client.post(reverse('ecnunlock_list') + '?WONo=WO-VIEW-001', data)
        messages = list(get_messages(response))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "No ECN items selected for unlock.")

    def test_ecn_unlock_table_partial_view(self):
        """Test the HTMX partial view for the table."""
        response = self.client.get(reverse('ecnunlock_table_partial'), {'WONo': 'WO-VIEW-001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'ecnunlock/_table.html')
        self.assertContains(response, self.item1.itemcode)
        self.assertContains(response, self.item2.itemcode)
        self.assertContains(response, 'id="ecnUnlockTable"') # Ensure DataTables ID is present

    def test_ecn_unlock_cancel_view(self):
        """Test the cancel view redirection."""
        response = self.client.get(reverse('ecn_unlock_cancel'))
        self.assertEqual(response.status_code, 302)
        # This assumes 'ecn_wo_list' exists and is the target.
        # In a real setup, ensure this URL points to the correct Django view.
        self.assertRedirects(response, reverse('ecn_wo_list'))

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:** All dynamic content loading (the DataTables table) and form submissions (the "Unlock ECN" action) are handled via HTMX.
    *   `list.html` uses `hx-get` on `ecn-table-container` to load `_table.html` initially and on `refreshECNUnlockTable` trigger.
    *   The `form` uses `hx-post` for submission, targeting `ecn-table-container` for table refresh.
    *   `hx-indicator` is used to show a loading spinner during HTMX requests.
    *   `HX-Trigger` header from the `post` method in `ECNUnlockView` ensures the table refreshes after a successful unlock without a full page reload.
*   **Alpine.js:**
    *   `x-data="ecnUnlock()"` initializes an Alpine.js component.
    *   `selectAll` property manages the "select all" checkbox logic, dynamically checking/unchecking individual item checkboxes.
    *   `selectedItems` `Set` tracks individual item selections, allowing for persistent state if needed (though for a POST form, just `name="selected_item_ids"` is sufficient).
    *   Message display: Alpine.js listens for a custom `showMessage` event triggered by HTMX responses to display Django messages dynamically and fade them out.
*   **DataTables:**
    *   The `_table.html` partial contains the HTML structure for the table.
    *   The JavaScript for DataTables initialization is placed within the `list.html`'s `extra_js` block. Critically, it's inside an `htmx:afterSwap` event listener, ensuring DataTables is initialized *after* the table content is loaded into the DOM by HTMX.
    *   `pageLength` and `lengthMenu` are configured. `columnDefs` are used to disable sorting for SN and checkbox columns as per the original GridView behavior.
*   **No custom JavaScript:** All dynamic behaviors are achieved solely through HTMX and Alpine.js, fulfilling the requirement for no additional JavaScript.

### Final Notes

*   **Django Settings:** Remember to configure your `settings.py` for database connection (pointing to your existing SQL Server database if `managed=False` is used), email backend (e.g., `django.core.mail.backends.smtp.EmailBackend` and relevant `EMAIL_HOST`, `EMAIL_PORT`, etc.), and static/media files.
*   **Session Management:** The `compid` and `finyear` from `Session` in ASP.NET need to be correctly mapped to Django's session management, typically after user authentication.
*   **Security:** Ensure proper validation and sanitization of `WONo` and other inputs. Implement Django's CSRF protection, which is included in the form.
*   **Error Handling:** The `try-catch` blocks in ASP.NET are replaced by more Pythonic error handling and Django's `messages` framework for user feedback. Logging is also recommended.
*   **`fun.AllComponentBOMQty`**: This function's exact logic needs to be translated into Python/Django ORM queries. A placeholder was used, but it's a critical piece of business logic that belongs in the `ECNEntryManager` or a dedicated service.
*   **`clsFunctions` `update` and `select`:** These generic functions are naturally handled by Django's ORM (e.g., `Model.objects.filter().update()`, `Model.objects.get()`, `Model.objects.filter().values()`).
*   **Tailwind CSS:** The templates include classes for Tailwind CSS, assuming it's configured in your `core/base.html` and `settings.py`.