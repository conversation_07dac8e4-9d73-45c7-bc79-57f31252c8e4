## ASP.NET to Django Conversion Script: BOM Root Assembly Copy From Work Order

This document outlines a strategic plan for migrating the `BOM_Design_Root_Assembly_Copy_WO.aspx` functionality from ASP.NET to a modern Django application. The focus is on leveraging Django's robust features, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for efficient data presentation, all while adhering to the 'Fat Model, Thin View' architectural pattern.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database entities:
1.  **Main Work Order Data:** The `GridView` is populated by a stored procedure `Sp_WONO_NotInBom`. The displayed columns are `FinYear`, `CustomerName`, `CustomerId`, `EnqId`, `PONo`, `WONo`, `SysDate`, `EmployeeName`. `WONo` is identified as a `DataKeyNames`. This suggests a view or table that consolidates this work order summary information. For this migration, we'll model the *output* structure of this stored procedure/view.
2.  **Work Order Categories:** `tblSD_WO_Category` is used to populate `DDLTaskWOType` with `CId` and `Symbol+' - '+CName`.
3.  **Customer Master:** `SD_Cust_master` is used for the customer name autocomplete functionality, querying `CustomerId` and `CustomerName`.

**Inferred Database Elements:**
-   **Main Work Order View/Table:** `vw_WorkOrderSummaryForBOM` (representing the result of `Sp_WONO_NotInBom`)
    -   Columns: `WONo` (VARCHAR/NVARCHAR, PK), `FinYear` (VARCHAR/NVARCHAR), `CustomerName` (VARCHAR/NVARCHAR), `CustomerId` (VARCHAR/NVARCHAR), `EnqId` (VARCHAR/NVARCHAR), `PONo` (VARCHAR/NVARCHAR), `SysDate` (DATETIME/DATE), `EmployeeName` (VARCHAR/NVARCHAR).
-   **Work Order Categories Table:** `tblSD_WO_Category`
    -   Columns: `CId` (INT, PK), `Symbol` (VARCHAR/NVARCHAR), `CName` (VARCHAR/NVARCHAR), `CompId` (INT).
-   **Customer Master Table:** `SD_Cust_master`
    -   Columns: `CustomerId` (VARCHAR/NVARCHAR, PK), `CustomerName` (VARCHAR/NVARCHAR), `CompId` (INT).

### Step 2: Identify Backend Functionality

**Analysis:**
This ASP.NET page primarily serves as a search and selection interface for Work Orders that can be copied (likely to create a new Bill of Material (BOM) from an existing one).

-   **Read/Retrieve:** The core functionality involves fetching a list of Work Orders based on various search criteria. This is handled by `BindDataCust` which calls the `Sp_WONO_NotInBom` stored procedure.
-   **Filtering/Search:** Dynamic filtering based on:
    -   Search Type (Customer Name, Enquiry No, PO No, WO No) selected via `DropDownList1`.
    -   Search Value entered in `txtSearchCustomer` or `TxtSearchValue` (with autocomplete for Customer Name).
    -   Work Order Category selected via `DDLTaskWOType`.
-   **Autocomplete:** The `sql` WebMethod provides customer name suggestions for `TxtSearchValue`.
-   **Navigation:**
    -   Hyperlinks within the `WONo` column of the `GridView` navigate to `BOM_Design_Root_Assembly_Copy_Grid.aspx` (the actual copy operation page), passing `WONoSrc` and `WONoDest`.
    -   "Cancel" button (`btnCancel`) redirects to `BOM_Design_WO_TreeView.aspx`.

**No direct Create, Update, or Delete (CRUD) operations for the `WorkOrder` entity are performed on this page.** This page acts as a "source selector."

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page is built using standard Web Forms controls.

-   **Page Structure:** Utilizes a MasterPage, providing a consistent layout. In Django, this translates to extending a base template (e.g., `core/base.html`).
-   **Search/Filter Area:**
    -   `DropDownList ID="DropDownList1"`: For selecting search criteria (Customer Name, Enquiry No, PO No, WO No). This will be a Django `forms.ChoiceField` with HTMX `hx-trigger` to dynamically swap input fields.
    -   `TextBox ID="txtSearchCustomer"` / `TextBox ID="TxtSearchValue"`: Text input fields, one shown at a time based on `DropDownList1` selection. `TxtSearchValue` has `AutoCompleteExtender`. This will be managed by a Django `forms.CharField` and an Alpine.js component for the autocomplete functionality, integrated with HTMX.
    -   `DropDownList ID="DDLTaskWOType"`: For selecting Work Order Categories. This will be a Django `forms.ModelChoiceField` with HTMX `hx-trigger` to refresh the results.
    -   `asp:Button ID="btnSearch"`: Triggers the data refresh.
    -   `asp:Button ID="btnCancel"`: Redirects to another page.
-   **Data Display Area:**
    -   `asp:Panel ID="Panel1"`: Acts as a container for the `GridView`, providing scrollbars.
    -   `asp:GridView ID="SearchGridView1"`: Displays the list of Work Orders.
        -   Features: Paging (`AllowPaging="true"`, `PageSize="25"`), Sorting (`AllowSorting="True"` - though no server-side sorting logic implemented in C# for it).
        -   Columns are explicitly defined (`AutoGenerateColumns="False"`).
        -   `WONo` column contains dynamic hyperlinks.
-   **Hidden Fields:** `hfSort`, `hfSearchText` are used for retaining state across postbacks. In Django, these are often handled by form fields, query parameters, or HTMX requests.

---

### Step 4: Generate Django Code

We will create a Django application named `design_bom`.

#### 4.1 Models (`design_bom/models.py`)

For `managed=False` models, Django doesn't create/manage their tables. We assume these tables/views pre-exist in the database. The `WorkOrderQueryManager` will be a custom manager to encapsulate the complex logic of calling the `Sp_WONO_NotInBom` stored procedure, which Django's ORM cannot directly represent.

```python
from django.db import models
from django.db.models.manager import BaseManager
from django.db import connection # For raw SQL/stored procedure calls
from django.conf import settings # For default session values in models/tests

# Custom manager to simulate the behavior of the Sp_WONO_NotInBom stored procedure
class WorkOrderQueryManager(BaseManager):
    def get_queryset(self):
        # This method is typically used by the ORM.
        # Since we're using raw SQL for WorkOrder, this might return an empty queryset
        # or be conceptually overridden by filter_work_orders.
        # For simplicity, if WorkOrder is directly mapped to a view,
        # it would return super().get_queryset(). For an SP, we need a custom method.
        # We will use 'filter_work_orders' as the main entry point for data retrieval.
        return super().get_queryset().none() # No default queryset if not a real table

    def filter_work_orders(self, comp_id, fin_id, search_type, search_value, wo_category_id):
        """
        Executes a raw SQL query or calls the stored procedure Sp_WONO_NotInBom.
        This method will replicate the logic found in the ASP.NET BindDataCust.
        """
        # Build dynamic WHERE clauses for the stored procedure parameters
        x_param = ""
        if search_type == "1" and search_value: # Enquiry No
            x_param = f" AND EnqId='{search_value}'"
        elif search_type == "2" and search_value: # PO No
            x_param = f" AND PONo='{search_value}'"
        elif search_type == "3" and search_value: # WO No
            x_param = f" AND WONo='{search_value}'"

        y_param = ""
        if search_type == "0" and search_value: # Customer Name (CustomerId after extraction)
            y_param = f" AND CustomerId='{search_value}'"
        
        z_param = ""
        if wo_category_id and wo_category_id != 'WO Category': # DDLTaskWOType filter
            z_param = f" AND CId='{wo_category_id}'"

        # L is empty in the ASP.NET code, so it remains empty here.
        l_param = ""

        # Dummy data for demonstration. In a real migration, this would execute
        # the actual stored procedure and map results.
        # Example of how a raw query might look:
        # with connection.cursor() as cursor:
        #     cursor.execute("EXEC Sp_WONO_NotInBom @CompId=%s, @FinId=%s, @x=%s, @y=%s, @z=%s, @l=%s",
        #                    [comp_id, fin_id, x_param, y_param, z_param, l_param])
        #     columns = [col[0] for col in cursor.description]
        #     results = [dict(zip(columns, row)) for row in cursor.fetchall()]
        
        # Simulating results of Sp_WONO_NotInBom based on columns in GridView
        all_dummy_data = [
            {'WONo': 'WO001', 'FinYear': '2023-24', 'CustomerName': 'Alpha Corp', 'CustomerId': 'CUST001', 'EnqId': 'ENQ001', 'PONo': 'PO001', 'SysDate': '2023-01-15', 'EmployeeName': 'John Doe'},
            {'WONo': 'WO002', 'FinYear': '2023-24', 'CustomerName': 'Beta Industries', 'CustomerId': 'CUST002', 'EnqId': 'ENQ002', 'PONo': 'PO002', 'SysDate': '2023-02-20', 'EmployeeName': 'Jane Smith'},
            {'WONo': 'WO003', 'FinYear': '2023-24', 'CustomerName': 'Alpha Corp', 'CustomerId': 'CUST001', 'EnqId': 'ENQ003', 'PONo': 'PO003', 'SysDate': '2023-03-01', 'EmployeeName': 'John Doe'},
            {'WONo': 'WO004', 'FinYear': '2023-24', 'CustomerName': 'Gamma Solutions', 'CustomerId': 'CUST003', 'EnqId': 'ENQ004', 'PONo': 'PO004', 'SysDate': '2023-03-10', 'EmployeeName': 'Alice Brown'},
            {'WONo': 'WO005', 'FinYear': '2023-24', 'CustomerName': 'Beta Industries', 'CustomerId': 'CUST002', 'EnqId': 'ENQ005', 'PONo': 'PO005', 'SysDate': '2023-03-15', 'EmployeeName': 'Jane Smith'},
        ]

        filtered_data = []
        for wo in all_dummy_data:
            match = True
            # Apply filters dynamically (mimicking the SP's logic)
            if x_param:
                if 'EnqId' in x_param and wo['EnqId'] != search_value: match = False
                if 'PONo' in x_param and wo['PONo'] != search_value: match = False
                if 'WONo' in x_param and wo['WONo'] != search_value: match = False
            
            if y_param:
                if 'CustomerId' in y_param and wo['CustomerId'] != search_value: match = False
            
            # This would typically involve a CId lookup on WO data, which is missing in dummy WO data
            # For this example, we assume WO categories are not directly on the WO table
            # so Z_param would be handled by the SP's join.
            # Here, we skip category filtering on dummy data.

            if match:
                filtered_data.append(wo)

        # Convert dictionaries to WorkOrder objects
        return [WorkOrder(**data) for data in filtered_data]


class WorkOrder(models.Model):
    # This model represents the structure of the data returned by Sp_WONO_NotInBom.
    # It's intended to map to a database VIEW (e.g., vw_WorkOrderSummaryForBOM)
    # or be instantiated from the results of a raw SQL query/stored procedure call.
    WONo = models.CharField(db_column='WONo', max_length=50, primary_key=True, verbose_name='WO No')
    FinYear = models.CharField(db_column='FinYear', max_length=20, blank=True, null=True, verbose_name='Fin Yrs')
    CustomerName = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True, verbose_name='Customer Name')
    CustomerId = models.CharField(db_column='CustomerId', max_length=50, blank=True, null=True, verbose_name='Code')
    EnqId = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True, verbose_name='Enquiry No')
    PONo = models.CharField(db_column='PONo', max_length=50, blank=True, null=True, verbose_name='PO No')
    SysDate = models.DateField(db_column='SysDate', blank=True, null=True, verbose_name='Gen. Date')
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True, verbose_name='Gen. By')

    objects = WorkOrderQueryManager() # Custom manager for data retrieval

    class Meta:
        managed = False # Django will not create/manage this table/view
        db_table = 'vw_WorkOrderSummaryForBOM' # Hypothetical view encapsulating SP results
        verbose_name = 'Work Order for BOM Copy'
        verbose_name_plural = 'Work Orders for BOM Copy'

    def __str__(self):
        return self.WONo

class WorkOrderCategory(models.Model):
    # Maps to tblSD_WO_Category
    CId = models.IntegerField(db_column='CId', primary_key=True, verbose_name='Category ID')
    Symbol = models.CharField(db_column='Symbol', max_length=50, verbose_name='Symbol')
    CName = models.CharField(db_column='CName', max_length=255, verbose_name='Category Name')
    CompId = models.IntegerField(db_column='CompId', verbose_name='Company ID')

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'Work Order Category'
        verbose_name_plural = 'Work Order Categories'

    def __str__(self):
        return f"{self.Symbol} - {self.CName}"

class Customer(models.Model):
    # Maps to SD_Cust_master
    CustomerId = models.CharField(db_column='CustomerId', max_length=50, primary_key=True, verbose_name='Customer ID')
    CustomerName = models.CharField(db_column='CustomerName', max_length=255, verbose_name='Customer Name')
    CompId = models.IntegerField(db_column='CompId', verbose_name='Company ID')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.CustomerName

    @staticmethod
    def get_customer_id_from_name_and_code(full_name_with_code):
        """
        Extracts CustomerId from a string like "Customer Name [CustomerId]".
        This mimics the fun.getCode() logic.
        """
        if '[' in full_name_with_code and ']' in full_name_with_code:
            try:
                # Find the last occurrence of '[' and ']'
                start_index = full_name_with_code.rfind('[')
                end_index = full_name_with_code.rfind(']')
                if start_index != -1 and end_index != -1 and start_index < end_index:
                    return full_name_with_code[start_index + 1 : end_index]
            except Exception:
                pass # Fallback to None if parsing fails
        return None

```

#### 4.2 Forms (`design_bom/forms.py`)

This form handles the search criteria and dynamic input fields based on the selected search type.

```python
from django import forms
from .models import WorkOrderCategory, Customer
from django.conf import settings

class WorkOrderSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('Select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        initial='Select', # Set initial selection
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/design_bom/bom_copy_wo/hx-sync-search-inputs/', # HTMX endpoint to swap inputs
            'hx-target': '#search_input_container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change' # Trigger on change
        })
    )

    # This field is used for Enquiry No, PO No, WO No (standard text input)
    search_text = forms.CharField(
        max_length=350,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search value'
        })
    )

    # This field is used for Customer Name (autocomplete input)
    search_value_autocomplete = forms.CharField(
        max_length=350,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing customer name...'
        })
    )

    wo_category = forms.ModelChoiceField(
        queryset=WorkOrderCategory.objects.none(), # Will be populated dynamically
        required=False,
        empty_label="WO Category",
        label="WO Category",
        widget=forms.Select(attrs={
            'class': 'box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        super().__init__(*args, **kwargs)
        
        # Populate wo_category queryset based on comp_id
        if comp_id:
            self.fields['wo_category'].queryset = WorkOrderCategory.objects.filter(CompId=comp_id)

        # Set initial visibility based on default search type (or pre-filled if from GET)
        current_search_type = self.data.get('search_type', self.initial.get('search_type', 'Select'))
        if current_search_type == '0': # Customer Name
            self.fields['search_text'].widget.attrs['style'] = 'display: none;'
            self.fields['search_value_autocomplete'].widget.attrs.pop('style', None)
        else: # Other types or 'Select'
            self.fields['search_text'].widget.attrs.pop('style', None)
            self.fields['search_value_autocomplete'].widget.attrs['style'] = 'display: none;'
```

#### 4.3 Views (`design_bom/views.py`)

This module defines the views for displaying the work order list, handling search, and supporting HTMX interactions.

```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.conf import settings
from .models import WorkOrder, WorkOrderCategory, Customer, WorkOrderQueryManager
from .forms import WorkOrderSearchForm

# Ensure settings for default IDs are present for development/testing
# In a real application, these would come from user sessions or configuration.
# Example in settings.py: DEFAULT_COMP_ID = 1, DEFAULT_FINYEAR_ID = 2023

class WorkOrderListAndSearch(TemplateView):
    """
    Main view for the BOM Root Assembly Copy Work Order page.
    It renders the search form and acts as a container for the HTMX-loaded table.
    """
    template_name = 'design_bom/workorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve session variables, mirroring ASP.NET Session state
        comp_id = self.request.session.get('compid', getattr(settings, 'DEFAULT_COMP_ID', 1))
        fin_id = self.request.session.get('finyear', getattr(settings, 'DEFAULT_FINYEAR_ID', 2023))
        
        # Retrieve query string parameter
        wo_no_dest = self.request.GET.get('WONoDest', '')

        # Initialize the search form with request data and company ID
        # The initial state of the form (e.g., selected dropdowns, text values)
        # comes from self.request.GET when a search is performed.
        form = WorkOrderSearchForm(self.request.GET or None, comp_id=comp_id)
        
        context['form'] = form
        context['wo_no_dest'] = wo_no_dest # Pass destination WO No to template for links
        return context

class WorkOrderTablePartialView(TemplateView):
    """
    HTMX-driven partial view to load and refresh the Work Order list table.
    This view encapsulates the logic for filtering and retrieving data,
    mimicking the ASP.NET BindDataCust method.
    """
    template_name = 'design_bom/workorder/_workorder_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Retrieve session and query parameters
        comp_id = self.request.session.get('compid', getattr(settings, 'DEFAULT_COMP_ID', 1))
        fin_id = self.request.session.get('finyear', getattr(settings, 'DEFAULT_FINYEAR_ID', 2023))
        wo_no_dest = self.request.GET.get('WONoDest', '')

        # Extract search parameters from GET request
        search_type = self.request.GET.get('search_type', 'Select')
        search_text = self.request.GET.get('search_text', '') # For non-autocomplete
        search_value_autocomplete = self.request.GET.get('search_value_autocomplete', '') # For autocomplete
        wo_category_id = self.request.GET.get('wo_category')

        # Determine the actual search value based on search type
        actual_search_value = ''
        if search_type == '0': # Customer Name: requires extracting ID from "Name [ID]" format
            actual_search_value = Customer.get_customer_id_from_name_and_code(search_value_autocomplete)
        else: # Other types use the plain search_text
            actual_search_value = search_text

        # Call the custom manager method to fetch filtered Work Orders
        # This is where the ASP.NET's Sp_WONO_NotInBom logic is applied.
        work_orders = WorkOrder.objects.filter_work_orders(
            comp_id=comp_id,
            fin_id=fin_id,
            search_type=search_type,
            search_value=actual_search_value,
            wo_category_id=wo_category_id if wo_category_id and wo_category_id != 'WO Category' else None
        )
        
        context['work_orders'] = work_orders
        context['wo_no_dest'] = wo_no_dest
        return context

class SyncSearchInputsView(TemplateView):
    """
    HTMX endpoint to dynamically swap the search input fields
    (standard text vs. autocomplete) based on the selected search type.
    This replaces the ASP.NET DropDownList1_SelectedIndexChanged2 logic.
    """
    template_name = 'design_bom/workorder/_search_inputs_partial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        search_type = self.request.GET.get('search_type', 'Select')
        context['search_type'] = search_type
        # Preserve existing values if present during a swap
        context['search_text_value'] = self.request.GET.get('search_text', '')
        context['search_value_autocomplete_value'] = self.request.GET.get('search_value_autocomplete', '')
        return context

class CustomerAutocompleteView(TemplateView):
    """
    JSON endpoint for customer name autocomplete, mimicking the ASP.NET WebMethod `sql`.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        comp_id = request.session.get('compid', getattr(settings, 'DEFAULT_COMP_ID', 1))

        customers = []
        if prefix_text:
            # Query for customer names or IDs containing the prefix, filtered by company
            # Only fetch top 10 results, similar to the original behavior
            customer_query = Customer.objects.filter(
                CompId=comp_id
            ).filter(
                models.Q(CustomerName__icontains=prefix_text) | models.Q(CustomerId__icontains=prefix_text)
            )[:10]

            for customer in customer_query:
                # Format: "Customer Name [CustomerId]"
                customers.append(f"{customer.CustomerName} [{customer.CustomerId}]")

        return JsonResponse(customers, safe=False)

class RedirectToBOMTreeView(TemplateView):
    """
    View to handle the "Cancel" button click, redirecting to the BOM Tree View.
    This replaces the ASP.NET Button2_Click and btnCancel_Click logic.
    """
    def get(self, request, *args, **kwargs):
        wo_no_dest = request.GET.get('WONoDest', '')
        # Construct the redirect URL for the equivalent Django view
        # Assume 'bom_design_tree_view' is the name of the Django URL for the tree view.
        redirect_url = reverse_lazy('design_bom:bom_design_tree_view') + f"?WONo={wo_no_dest}&ModId=3&SubModId=26"
        
        # For HTMX requests, issue an HX-Redirect header
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})
        
        # For non-HTMX requests, perform a standard redirect
        return HttpResponseRedirect(redirect_url)

```

#### 4.4 Templates (`design_bom/templates/design_bom/workorder/`)

This section provides the main list template and its partials.

**`design_bom/templates/design_bom/workorder/list.html` (Main Page)**

```html
{% extends 'core/base.html' %}
{% load static %} {# If you need to load static files like images #}
{% load humanize %} {# For date formatting, if you choose to use it in tables #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <table width="100%" cellpadding="0" cellspacing="0" class="mb-4">
        {# Header Row #}
        <tr>
            <td class="bg-blue-600 fontcsswhite h-21 text-white px-4 py-2 rounded-t-lg">
                &nbsp;<b>BOM Root Assembly Copy From</b>
            </td>
        </tr>
        {# Search/Filter Form Row #}
        <tr>
            <td class="fontcss p-4 bg-white shadow-md rounded-b-lg">
                <form id="searchForm"
                      hx-get="{% url 'design_bom:workorder_table_partial' %}"
                      hx-target="#workOrderTable-container"
                      hx-swap="innerHTML"
                      hx-trigger="submit, change from:#id_wo_category" {# Re-fetch table on submit or WO category change #}
                      hx-vals='js:{ WONoDest: "{{ wo_no_dest }}" }' {# Pass WONoDest as a static value for HTMX #}
                      class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                    
                    {# Search Type Dropdown #}
                    <div>
                        <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">{{ form.search_type.label }}</label>
                        {{ form.search_type }}
                    </div>

                    {# Dynamic Search Input (Text or Autocomplete) #}
                    <div id="search_input_container" class="col-span-1 md:col-span-1">
                        {# Initial load of the correct search input based on form's initial value #}
                        {% include 'design_bom/workorder/_search_inputs_partial.html' with search_type=form.search_type.value search_text_value=form.search_text.value search_value_autocomplete_value=form.search_value_autocomplete.value %}
                    </div>

                    {# WO Category Dropdown #}
                    <div>
                        <label for="{{ form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">{{ form.wo_category.label }}</label>
                        {{ form.wo_category }}
                    </div>

                    {# Search and Cancel Buttons #}
                    <div class="flex items-center space-x-2 mt-2 md:mt-0">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                            Search
                        </button>
                        <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded redbox"
                                hx-get="{% url 'design_bom:redirect_to_tree_view' %}?WONoDest={{ wo_no_dest }}" hx-swap="none">
                            Cancel
                        </button>
                    </div>
                </form>
            </td>
        </tr>
        {# Work Order Table Container #}
        <tr>
            <td class="fontcss pt-4">
                <div id="workOrderTable-container"
                     hx-trigger="load delay:50ms, refreshTable from:body" {# Load on page load, and on custom 'refreshTable' event #}
                     hx-get="{% url 'design_bom:workorder_table_partial' %}"
                     {# Pass all current form field values as parameters for the initial load and any manual refresh #}
                     hx-vals='js:{
                        search_type: document.getElementById("id_search_type").value,
                        search_text: document.getElementById("id_search_text") ? document.getElementById("id_search_text").value : "",
                        search_value_autocomplete: document.getElementById("id_search_value_autocomplete") ? document.getElementById("id_search_value_autocomplete").value : "",
                        wo_category: document.getElementById("id_wo_category").value,
                        WONoDest: "{{ wo_no_dest }}"
                     }'
                     hx-indicator="#loading-spinner"
                     hx-swap="innerHTML">
                    <!-- Loading indicator -->
                    <div id="loading-spinner" class="htmx-indicator text-center p-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Work Orders...</p>
                    </div>
                </div>
            </td>
        </tr>
    </table>
</div>
{% endblock %}

{% block extra_js %}
{# Include jQuery for DataTables, if not already in base.html #}
{# <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script> #}
{# Include DataTables JS, if not already in base.html #}
{# <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script> #}
{# Include DataTables CSS, if not already in base.html #}
{# <link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css"> #}


<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('customerAutocomplete', () => ({
            searchText: '',
            autocompleteResults: [],
            showResults: false,
            async searchCustomers() {
                if (this.searchText.length < 1) {
                    this.autocompleteResults = [];
                    this.showResults = false;
                    return;
                }
                // Fetch autocomplete suggestions from Django endpoint
                const response = await fetch(`{% url 'design_bom:customer_autocomplete' %}?q=${this.searchText}`);
                this.autocompleteResults = await response.json();
                this.showResults = true;
            },
            selectCustomer(customerNameWithCode) {
                this.searchText = customerNameWithCode;
                this.showResults = false;
                // Important: Trigger a change event on the input so HTMX can pick it up for form submission
                const event = new Event('change');
                this.$el.dispatchEvent(event);
            }
        }));
    });

    // Re-initialize DataTable when the table partial is loaded/swapped
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'workOrderTable-container') {
            const tableElement = $('#workOrderTable');
            if (tableElement.length && !$.fn.DataTable.isDataTable(tableElement)) {
                // Initialize DataTable
                tableElement.DataTable({
                    "pageLength": 25, // Matches ASP.NET GridView PageSize
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Ensure any previous instance is destroyed
                    "order": [] // Disable initial sorting
                });
            } else if ($.fn.DataTable.isDataTable(tableElement)) {
                // If DataTable already exists, just redraw (useful for re-applying search/sort if any)
                tableElement.DataTable().clear().rows.add(tableElement.find('tbody tr')).draw();
            }
        }
    });

    // When the form is submitted or category changes, trigger the table refresh
    // This is already handled by hx-trigger on the form itself.
    // document.getElementById('searchForm').addEventListener('submit', function(event) {
    //     htmx.trigger(document.body, 'refreshTable');
    // });
</script>
{% endblock %}
```

**`design_bom/templates/design_bom/workorder/_workorder_table.html` (Partial for DataTable)**

```html
{% load humanize %} {# To format SysDate as a date, if it's a date object #}

{% if work_orders %}
<table id="workOrderTable" class="min-w-full bg-white yui-datatable-theme border border-gray-200">
    <thead>
        <tr class="bg-gray-50 text-gray-500 uppercase tracking-wider">
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium">Gen. By</th>
        </tr>
    </thead>
    <tbody>
        {% for wo in work_orders %}
        <tr class="hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.FinYear|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.CustomerName|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.CustomerId|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.EnqId|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.PONo|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">
                {# Link to the next step of the BOM copy process #}
                <a href="{% url 'design_bom:bom_copy_grid' %}?WONoSrc={{ wo.WONo }}&WONoDest={{ wo_no_dest }}&ModId=3&SubModId=26"
                   class="text-blue-600 hover:underline">
                    {{ wo.WONo }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.SysDate|date:"Y-m-d"|default_if_none:"" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.EmployeeName|default_if_none:"" }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<div class="text-center p-8 bg-white shadow-md rounded-lg">
    <p class="text-lg text-red-700 font-semibold">No data to display !</p>
</div>
{% endif %}

{# Re-initialize DataTable on partial load. This script runs after HTMX injects the content. #}
<script>
    $(document).ready(function() {
        const tableElement = $('#workOrderTable');
        if (tableElement.length && !$.fn.DataTable.isDataTable(tableElement)) {
            tableElement.DataTable({
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy any existing instance before creating a new one
                "order": [] // Disable initial sorting
            });
        }
    });
</script>
```

**`design_bom/templates/design_bom/workorder/_search_inputs_partial.html` (Partial for Dynamic Search Inputs)**

```html
{# This partial dynamically swaps the input fields based on search_type selection #}
{# Variables available: #}
{# - search_type: Current selected search type ('0', '1', '2', '3', 'Select') #}
{# - search_text_value: Value for the text input (for non-autocomplete) #}
{# - search_value_autocomplete_value: Value for the autocomplete input #}

{% comment %}
    The hx-trigger="change" on the select element will cause hx-get to this partial.
    This partial will then decide which input to show.
    Alpine.js is used for the autocomplete behavior.
{% endcomment %}

{% if search_type == "0" %} {# Customer Name (autocomplete) #}
<div x-data="customerAutocomplete" class="relative">
    <label for="id_search_value_autocomplete" class="sr-only">Search Value</label>
    <input type="text"
           id="id_search_value_autocomplete"
           name="search_value_autocomplete"
           x-model="searchText"
           @input.debounce.300ms="searchCustomers"
           @focus="showResults = true"
           @click.away="showResults = false"
           placeholder="Start typing customer name..."
           class="box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
           value="{{ search_value_autocomplete_value }}">
    <template x-if="showResults && autocompleteResults.length > 0">
        <ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto mt-1">
            <template x-for="result in autocompleteResults" :key="result">
                <li x-text="result" @click="selectCustomer(result);" class="px-4 py-2 hover:bg-gray-100 cursor-pointer"></li>
            </template>
        </ul>
    </template>
    {# Always include both fields as hidden if not active to ensure form submission includes them #}
    <input type="hidden" id="id_search_text" name="search_text" value="">
</div>
{% else %} {# Enquiry No, PO No, WO No, Select (standard text input) #}
<div class="relative">
    <label for="id_search_text" class="sr-only">Search Value</label>
    <input type="text"
           id="id_search_text"
           name="search_text"
           value="{{ search_text_value }}"
           placeholder="Enter search value"
           class="box3 w-full block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    {# Always include both fields as hidden if not active to ensure form submission includes them #}
    <input type="hidden" id="id_search_value_autocomplete" name="search_value_autocomplete" value="">
</div>
{% endif %}

```

#### 4.5 URLs (`design_bom/urls.py`)

Define URL patterns for the views within the `design_bom` app.

```python
from django.urls import path
from django.views.generic import TemplateView # For placeholder views
from .views import (
    WorkOrderListAndSearch,
    WorkOrderTablePartialView,
    SyncSearchInputsView,
    CustomerAutocompleteView,
    RedirectToBOMTreeView,
)

app_name = 'design_bom' # Namespace for this Django application

urlpatterns = [
    # Main page for BOM Root Assembly Copy From Work Order
    path('bom_copy_wo/', WorkOrderListAndSearch.as_view(), name='bom_copy_wo_list'),

    # HTMX endpoint for dynamically loading/refreshing the work order table
    path('bom_copy_wo/table_partial/', WorkOrderTablePartialView.as_view(), name='workorder_table_partial'),

    # HTMX endpoint for dynamically swapping search input fields
    path('bom_copy_wo/hx-sync-search-inputs/', SyncSearchInputsView.as_view(), name='hx_sync_search_inputs'),

    # JSON endpoint for customer name autocomplete suggestions
    path('bom_copy_wo/customer_autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Endpoint for the "Cancel" button redirection
    path('bom_copy_wo/redirect_to_tree_view/', RedirectToBOMTreeView.as_view(), name='redirect_to_tree_view'),

    # Placeholder URL for the destination BOM Copy Grid page (BOM_Design_Root_Assembly_Copy_Grid.aspx)
    # This would be a separate Django view in a full migration.
    path('bom_copy_grid/', TemplateView.as_view(template_name='design_bom/placeholders/bom_copy_grid_placeholder.html'), name='bom_copy_grid'),

    # Placeholder URL for the BOM Design WO Tree View page (BOM_Design_WO_TreeView.aspx)
    # This would also be a separate Django view.
    path('bom_design_tree_view/', TemplateView.as_view(template_name='design_bom/placeholders/bom_tree_view_placeholder.html'), name='bom_design_tree_view'),
]
```
*(You would need to create the placeholder template files in `design_bom/templates/design_bom/placeholders/` for the placeholder URLs to work.)*

#### 4.6 Tests (`design_bom/tests.py`)

Comprehensive tests for models, forms, and views to ensure functionality and integration. Mocking `managed=False` models and raw SQL calls is crucial here.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch, MagicMock
from .models import WorkOrder, WorkOrderCategory, Customer, WorkOrderQueryManager
from .forms import WorkOrderSearchForm

# Define default settings for tests (should be in your actual settings.py)
settings.DEFAULT_COMP_ID = 1
settings.DEFAULT_FINYEAR_ID = 2023

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # We need to mock the underlying database interactions for managed=False models
        # since Django's test database setup won't include these pre-existing tables/views.

        # Mock the WorkOrderQueryManager's filter_work_orders method
        cls.mock_work_orders_data = [
            WorkOrder(WONo='WO001', FinYear='2023-24', CustomerName='Test Cust A', CustomerId='C001', EnqId='ENQ001', PONo='PO001', SysDate='2023-01-01', EmployeeName='Emp One'),
            WorkOrder(WONo='WO002', FinYear='2023-24', CustomerName='Test Cust B', CustomerId='C002', EnqId='ENQ002', PONo='PO002', SysDate='2023-02-01', EmployeeName='Emp Two'),
            WorkOrder(WONo='WO003', FinYear='2023-24', CustomerName='Test Cust A', CustomerId='C001', EnqId='ENQ003', PONo='PO003', SysDate='2023-03-01', EmployeeName='Emp Three'),
        ]
        
        # Mock WorkOrderCategory.objects.filter for the form's queryset
        cls.mock_wo_categories = [
            WorkOrderCategory(CId=1, Symbol='CAT1', CName='Category Alpha', CompId=settings.DEFAULT_COMP_ID),
            WorkOrderCategory(CId=2, Symbol='CAT2', CName='Category Beta', CompId=settings.DEFAULT_COMP_ID),
        ]
        
        # Mock Customer.objects.filter for autocomplete and customer ID lookup
        cls.mock_customers = [
            Customer(CustomerId='C001', CustomerName='Test Cust A', CompId=settings.DEFAULT_COMP_ID),
            Customer(CustomerId='C002', CustomerName='Test Cust B', CompId=settings.DEFAULT_COMP_ID),
        ]

    # --- Unit Tests for Model Methods ---
    def test_work_order_str_representation(self):
        wo = WorkOrder(WONo='TESTWO')
        self.assertEqual(str(wo), 'TESTWO')

    def test_work_order_category_str_representation(self):
        cat = WorkOrderCategory(CId=1, Symbol='SYM', CName='Category Name', CompId=1)
        self.assertEqual(str(cat), 'SYM - Category Name')

    def test_customer_str_representation(self):
        cust = Customer(CustomerId='CUST001', CustomerName='Test Customer')
        self.assertEqual(str(cust), 'Test Customer')

    def test_get_customer_id_from_name_and_code_static_method(self):
        self.assertEqual(Customer.get_customer_id_from_name_and_code("Example Customer [CUST123]"), "CUST123")
        self.assertIsNone(Customer.get_customer_id_from_name_and_code("No brackets"))
        self.assertIsNone(Customer.get_customer_id_from_name_and_code("Missing closing bracket [ID"))
        self.assertIsNone(Customer.get_customer_id_from_name_and_code("Missing opening bracket ID]"))
        self.assertEqual(Customer.get_customer_id_from_name_and_code("Multiple [brackets] [LAST]"), "LAST")

    @patch.object(WorkOrderQueryManager, 'filter_work_orders')
    def test_work_order_query_manager_calls_sp_logic(self, mock_filter_work_orders):
        mock_filter_work_orders.return_value = self.mock_work_orders_data # Mock the return value
        
        comp_id = settings.DEFAULT_COMP_ID
        fin_id = settings.DEFAULT_FINYEAR_ID
        search_type = '3' # WO No
        search_value = 'WO001'
        wo_category_id = None
        
        # Call the method via the manager
        result = WorkOrder.objects.filter_work_orders(comp_id, fin_id, search_type, search_value, wo_category_id)
        
        # Assert that the method was called with correct parameters
        mock_filter_work_orders.assert_called_once_with(
            comp_id=comp_id,
            fin_id=fin_id,
            search_type=search_type,
            search_value=search_value,
            wo_category_id=wo_category_id
        )
        self.assertEqual(len(result), len(self.mock_work_orders_data))

class WorkOrderFormsTest(TestCase):
    def test_form_initialization_with_comp_id(self):
        with patch.object(WorkOrderCategory.objects, 'filter', return_value=WorkOrderModelTest.mock_wo_categories):
            form = WorkOrderSearchForm(comp_id=settings.DEFAULT_COMP_ID)
            self.assertIn('wo_category', form.fields)
            self.assertEqual(len(form.fields['wo_category'].queryset), len(WorkOrderModelTest.mock_wo_categories))
            self.assertTrue(form.is_valid())

    def test_form_initial_visibility_customer_name(self):
        form = WorkOrderSearchForm(data={'search_type': '0'})
        self.assertNotIn('style', form.fields['search_value_autocomplete'].widget.attrs)
        self.assertIn('style', form.fields['search_text'].widget.attrs) # Hidden

    def test_form_initial_visibility_enquiry_no(self):
        form = WorkOrderSearchForm(data={'search_type': '1'})
        self.assertNotIn('style', form.fields['search_text'].widget.attrs)
        self.assertIn('style', form.fields['search_value_autocomplete'].widget.attrs) # Hidden

class WorkOrderViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('design_bom:bom_copy_wo_list')
        self.table_partial_url = reverse('design_bom:workorder_table_partial')
        self.sync_inputs_url = reverse('design_bom:hx_sync_search_inputs')
        self.autocomplete_url = reverse('design_bom:customer_autocomplete')
        self.redirect_url = reverse('design_bom:redirect_to_tree_view')
        
        # Set session for tests
        session = self.client.session
        session['compid'] = settings.DEFAULT_COMP_ID
        session['finyear'] = settings.DEFAULT_FINYEAR_ID
        session.save()
        
        # Apply global mocks for DB interactions in views
        self.filter_work_orders_patch = patch.object(WorkOrderQueryManager, 'filter_work_orders', return_value=WorkOrderModelTest.mock_work_orders_data)
        self.mock_filter_work_orders = self.filter_work_orders_patch.start()

        self.wo_cat_filter_patch = patch.object(WorkOrderCategory.objects, 'filter', return_value=WorkOrderModelTest.mock_wo_categories)
        self.mock_wo_cat_filter = self.wo_cat_filter_patch.start()

        self.customer_filter_patch = patch.object(Customer.objects, 'filter', return_value=WorkOrderModelTest.mock_customers)
        self.mock_customer_filter = self.customer_filter_patch.start()

    def tearDown(self):
        self.filter_work_orders_patch.stop()
        self.wo_cat_filter_patch.stop()
        self.customer_filter_patch.stop()

    # --- Integration Tests for Views ---
    def test_work_order_list_and_search_view_get(self):
        response = self.client.get(self.list_url + '?WONoDest=DUMMYWO')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/workorder/list.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['wo_no_dest'], 'DUMMYWO')
        self.mock_wo_cat_filter.assert_called_once_with(CompId=settings.DEFAULT_COMP_ID) # Check if category queryset was filtered

    def test_work_order_table_partial_view_initial_load(self):
        # Simulate HTMX initial load
        response = self.client.get(self.table_partial_url, {
            'WONoDest': 'DUMMYWO',
            'search_type': 'Select',
            'search_text': '',
            'search_value_autocomplete': '',
            'wo_category': 'WO Category'
        }, headers={'HX-Request': 'true'})
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/workorder/_workorder_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), len(WorkOrderModelTest.mock_work_orders_data))
        self.mock_filter_work_orders.assert_called_once_with(
            comp_id=settings.DEFAULT_COMP_ID,
            fin_id=settings.DEFAULT_FINYEAR_ID,
            search_type='Select',
            search_value='',
            wo_category_id=None
        )
        self.assertContains(response, 'WO001') # Check for data in table

    def test_work_order_table_partial_view_search_by_wo_no(self):
        self.mock_filter_work_orders.reset_mock() # Reset mock call count for new test
        response = self.client.get(self.table_partial_url, {
            'search_type': '3', # WO No
            'search_text': 'WO001',
            'search_value_autocomplete': '', # Not used for this search type
            'wo_category': 'WO Category',
            'WONoDest': 'DUMMYWO'
        }, headers={'HX-Request': 'true'})

        self.assertEqual(response.status_code, 200)
        self.mock_filter_work_orders.assert_called_once_with(
            comp_id=settings.DEFAULT_COMP_ID,
            fin_id=settings.DEFAULT_FINYEAR_ID,
            search_type='3',
            search_value='WO001',
            wo_category_id=None
        )
        self.assertContains(response, 'WO001')

    def test_work_order_table_partial_view_search_by_customer_name(self):
        self.mock_filter_work_orders.reset_mock()
        # Mock the static method call within the view for Customer.get_customer_id_from_name_and_code
        with patch('design_bom.models.Customer.get_customer_id_from_name_and_code', return_value='C001') as mock_get_code:
            response = self.client.get(self.table_partial_url, {
                'search_type': '0', # Customer Name
                'search_text': '', # Not used for this search type
                'search_value_autocomplete': 'Test Cust A [C001]', # Formatted customer name
                'wo_category': 'WO Category',
                'WONoDest': 'DUMMYWO'
            }, headers={'HX-Request': 'true'})

            self.assertEqual(response.status_code, 200)
            mock_get_code.assert_called_once_with('Test Cust A [C001]')
            self.mock_filter_work_orders.assert_called_once_with(
                comp_id=settings.DEFAULT_COMP_ID,
                fin_id=settings.DEFAULT_FINYEAR_ID,
                search_type='0',
                search_value='C001', # Expected extracted customer ID
                wo_category_id=None
            )
            self.assertContains(response, 'Test Cust A')


    def test_sync_search_inputs_view(self):
        # Test for Customer Name (autocomplete)
        response = self.client.get(self.sync_inputs_url, {'search_type': '0'}, headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/workorder/_search_inputs_partial.html')
        self.assertContains(response, 'id="id_search_value_autocomplete"')
        self.assertNotContains(response, 'id="id_search_text"') # Should be hidden, or not present visibly

        # Test for Enquiry No (standard text)
        response = self.client.get(self.sync_inputs_url, {'search_type': '1'}, headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/workorder/_search_inputs_partial.html')
        self.assertContains(response, 'id="id_search_text"')
        self.assertNotContains(response, 'id="id_search_value_autocomplete"') # Should be hidden

    def test_customer_autocomplete_view(self):
        self.mock_customer_filter.reset_mock()
        response = self.client.get(self.autocomplete_url, {'q': 'test cust'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 2) # Both Test Cust A and B match 'test cust'
        self.assertIn('Test Cust A [C001]', data)
        self.assertIn('Test Cust B [C002]', data)
        self.mock_customer_filter.assert_called_once() # Verify filter was called on Customer model

    def test_redirect_to_tree_view_htmx(self):
        response = self.client.get(self.redirect_url + '?WONoDest=DUMMYWO', headers={'HX-Request': 'true'})
        self.assertEqual(response.status_code, 204) # No Content for HTMX redirect
        self.assertIn('HX-Redirect', response.headers)
        self.assertIn(reverse('design_bom:bom_design_tree_view'), response.headers['HX-Redirect'])
        self.assertIn('?WONo=DUMMYWO&ModId=3&SubModId=26', response.headers['HX-Redirect'])

    def test_redirect_to_tree_view_non_htmx(self):
        response = self.client.get(self.redirect_url + '?WONoDest=DUMMYWO')
        self.assertEqual(response.status_code, 302) # Standard redirect
        self.assertIn(reverse('design_bom:bom_design_tree_view'), response.url)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for dynamic updates:**
    -   The main form uses `hx-get` to trigger a refresh of the `workOrderTable-container` whenever the form is submitted or `wo_category` dropdown changes.
    -   The `search_type` dropdown uses `hx-get` to `hx-sync-search-inputs` to dynamically swap the `search_text` and `search_value_autocomplete` input fields.
    -   `hx-trigger="load"` on `workOrderTable-container` ensures the table loads immediately when the page is accessed.
    -   The "Cancel" button uses `hx-get` with `hx-swap="none"` and `HX-Redirect` header to perform a client-side redirect for HTMX requests.
    -   `hx-indicator` is used to provide a loading spinner during HTMX requests.
-   **Alpine.js for UI state management:**
    -   The `customerAutocomplete` Alpine.js component manages the `searchText`, `autocompleteResults`, and `showResults` state for the customer name autocomplete. It fetches data via a Django JSON endpoint and updates the UI reactively.
-   **DataTables for list views:**
    -   The `_workorder_table.html` partial is designed to render a standard HTML `<table>` that is then initialized as a DataTables instance using jQuery after HTMX injects it into the DOM.
    -   The `htmx:afterSwap` event listener ensures that DataTables is correctly re-initialized every time the table content is updated via HTMX.
    -   The `destroy: true` option in DataTables initialization ensures that any existing instance is properly cleaned up before a new one is created.
-   **No custom JavaScript requirements beyond HTMX, Alpine.js, and jQuery/DataTables.** All interactions are handled declaratively with HTMX or by Alpine.js for specific UI behaviors.

---

### Final Notes

-   **Placeholder URLs/Templates:** The `bom_copy_grid` and `bom_design_tree_view` URLs and their corresponding templates are placeholders. In a full migration, these would be developed as complete Django views and templates.
-   **Session Management:** Django's session framework (`request.session`) replaces ASP.NET's `Session` object. `CompId` and `FinYearId` are accessed from the session.
-   **Database Interactions:** The most complex part of this migration is `Sp_WONO_NotInBom`. The `WorkOrderQueryManager` and its `filter_work_orders` method serve as a conceptual placeholder. In a real scenario, this method would contain the actual raw SQL call to the stored procedure or would interact with a database view specifically created to encapsulate the SP's logic for easier Django integration. This is a critical point for automated migration tools to analyze and generate.
-   **Styling:** Tailwind CSS classes (`box3`, `w-full`, `block`, `px-3`, `py-2`, `border`, etc.) are applied directly in the HTML templates for modern, responsive styling.
-   **DRY Principle:** Templates are kept DRY by using partials (`_workorder_table.html`, `_search_inputs_partial.html`).
-   **Fat Model, Thin View:** Business logic for data retrieval and filtering is conceptually pushed into the `WorkOrderQueryManager` (part of the model layer), keeping the views (e.g., `WorkOrderListAndSearch`, `WorkOrderTablePartialView`) concise and focused on request/response handling and context preparation.
-   **Test Coverage:** The provided tests aim for high coverage, including mocking external dependencies like database calls for `managed=False` models to ensure robustness.

This plan provides a clear, actionable roadmap for migrating the specified ASP.NET functionality to a modern Django application, emphasizing automation-friendly approaches and a clear separation of concerns.