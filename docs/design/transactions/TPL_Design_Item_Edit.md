## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET code primarily interacts with `tblDG_TPL_Master` for updating item quantities, and retrieves associated details from `tblDG_Item_Master` and `Unit_Master`. It also logs amendments to `tblDG_TPL_Amd`.

-   **`tblDG_TPL_Master`** (Main table for TPL item entries)
    *   `Id` (PK, referenced as `asslyId` in query string)
    *   `ItemId` (FK to `tblDG_Item_Master.Id`)
    *   `WONo`
    *   `CId`
    *   `Qty` (Quantity, the field being updated)
    *   `AmdNo` (Amendment Number, updated on each modification)
    *   `PId`
    *   `CompId`
    *   `FinYearId`
    *   `SysDate`
    *   `SysTime`
    *   `SessionId`

-   **`tblDG_Item_Master`** (For item details)
    *   `Id` (PK)
    *   `ItemCode`
    *   `ManfDesc` (Manufacturer Description, used for item description)
    *   `UOMBasic` (FK to `Unit_Master.Id`)
    *   `CId` (referenced as `ItemCId`)
    *   `PartNo`
    *   `FileName`

-   **`Unit_Master`** (For Unit of Measure details)
    *   `Id` (PK)
    *   `Symbol` (e.g., 'Kg', 'Mtrs')

-   **`tblDG_TPL_Amd`** (Amendment log table)
    *   `SysDate`
    *   `SysTime`
    *   `SessionId`
    *   `CompId`
    *   `FinYearId`
    *   `WONo`
    *   `TPLId` (Corresponds to `tblDG_TPL_Master.Id`)
    *   `PId`
    *   `CId`
    *   `ItemId`
    *   `Description`
    *   `UOM` (Corresponds to `Unit_Master.Id`)
    *   `AmdNo`
    *   `Qty`

## Step 2: Identify Backend Functionality

The ASP.NET page `TPL_Design_Item_Edit.aspx` primarily performs the following operations:

-   **Read (Display):**
    *   On `Page_Load`, it fetches a single `tblDG_TPL_Master` record along with related `tblDG_Item_Master` and `Unit_Master` data based on `Id` (from `asslyId` query string), `WONo`, and `ItemId`.
    *   It displays `WONo`, `ItemCode`, `ManfDesc`, `UOMBasic` (Symbol) and the current `Qty` in `txtQuntity`.

-   **Update:**
    *   On `btnUpdate_Click`, it validates the input quantity (`txtQuntity`).
    *   It retrieves the existing `AmdNo` (Amendment Number) from `tblDG_TPL_Master`, increments it, and uses the old `AmdNo` and `Qty` to log an amendment entry into `tblDG_TPL_Amd`.
    *   Finally, it updates the `Qty` and the new `AmdNo` in the `tblDG_TPL_Master` record.
    *   After successful update, it redirects to `TPL_Design_WO_TreeView_Edit.aspx`.

-   **Cancel:**
    *   `btncancel_Click` redirects back to `TPL_Design_WO_TreeView_Edit.aspx`.

The primary focus for Django will be creating an `UpdateView` for the `TPLItem` model, and integrating the amendment logging logic into the model itself following the "Fat Model" principle. We will also include a `ListView` and associated partials for a comprehensive Django CRUD interface, as per the guidelines, assuming this "edit item" page is part of a larger TPL management module.

## Step 3: Infer UI Components

The ASP.NET page uses the following UI controls:

-   `lblWONo`: Label to display Work Order Number (read-only).
-   `lblItemCode`: Label to display Item Code (read-only).
-   `lblMfDesc`: Label to display Manufacturer Description (read-only).
-   `lblUOMB`: Label to display Unit of Measure (read-only).
-   `txtQuntity`: TextBox for user input of quantity (editable).
    *   Associated `RequiredFieldValidator` and `RegularExpressionValidator` for numeric input and format.
-   `btnUpdate`: Button to trigger the update operation.
-   `btncancel`: Button to cancel and navigate back.

In Django, these will map to:
-   Read-only values: Direct display of model attributes in templates.
-   `txtQuntity`: A `forms.DecimalField` rendered as a `TextInput` widget with appropriate CSS classes.
-   Buttons: Standard HTML buttons with `hx-post` or `hx-get` for HTMX interactions.
-   Validation: Django Forms validation methods.

## Step 4: Generate Django Code

### Application Name: `design_transactions`

### 4.1 Models

We will define three models: `Unit`, `DesignItem`, and `TPLItem`, mapping to `Unit_Master`, `tblDG_Item_Master`, and `tblDG_TPL_Master` respectively. We also need `TPLAmendment` for the amendment log.

```python
# design_transactions/models.py
from django.db import models
from django.utils import timezone

class Unit(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=10, db_column='Symbol')

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class DesignItem(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    item_code = models.CharField(max_length=50, db_column='ItemCode')
    manufacturer_description = models.CharField(max_length=255, db_column='ManfDesc')
    uom_basic = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='UOMBasic')
    c_id = models.IntegerField(db_column='CId') # This is the ItemCId in ASP.NET, not the CId from TPL_Master

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Design Item'
        verbose_name_plural = 'Design Items'

    def __str__(self):
        return f"{self.item_code} - {self.manufacturer_description}"

class TPLItem(models.Model):
    # This is the 'Id' referred to as 'asslyId' in ASP.NET
    id = models.IntegerField(primary_key=True, db_column='Id')
    
    # Foreign key to DesignItem based on tblDG_TPL_Master.ItemId = tblDG_Item_Master.Id
    item = models.ForeignKey(DesignItem, on_delete=models.DO_NOTHING, db_column='ItemId')
    
    # Other identifying fields based on ASP.NET query/update
    won_no = models.CharField(max_length=50, db_column='WONo')
    c_id = models.IntegerField(db_column='CId') # This is the CId from TPL_Master
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    
    # Fields being updated or related to update
    quantity = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')
    amendment_no = models.IntegerField(db_column='AmdNo', null=True, blank=True, default=0)
    p_id = models.IntegerField(db_column='PId') # Used in tblDG_TPL_Amd insert

    # Audit fields
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.CharField(max_length=10, db_column='SysTime', default=lambda: timezone.now().strftime('%H:%M:%S'))
    session_id = models.CharField(max_length=50, db_column='SessionId')

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Item'
        verbose_name_plural = 'TPL Items'

    def __str__(self):
        return f"TPL Item: {self.item.item_code} ({self.won_no})"

    def update_quantity_and_log_amendment(self, new_quantity, session_username):
        """
        Updates the TPL item's quantity and logs an amendment entry.
        Corresponds to btnUpdate_Click logic.
        """
        # Import here to avoid circular dependency if TPLAmendment uses TPLItem, although unlikely
        from .models import TPLAmendment 

        try:
            # Ensure new_quantity is a valid decimal
            parsed_new_quantity = models.DecimalField(max_digits=18, decimal_places=3).to_python(new_quantity)
            if parsed_new_quantity < 0:
                raise ValueError("Quantity cannot be negative.")
        except Exception:
            raise ValueError("Invalid quantity format. Please enter a valid number.")

        old_quantity = self.quantity
        old_amendment_no = self.amendment_no if self.amendment_no is not None else 0
        new_amendment_no = old_amendment_no + 1

        # Log amendment
        TPLAmendment.objects.create(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().strftime('%H:%M:%S'),
            session_id=session_username,
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            won_no=self.won_no,
            tpl_id=self.id, # This TPLItem's ID
            p_id=self.p_id,
            c_id=self.c_id,
            item_id=self.item.id,
            description=self.item.manufacturer_description,
            uom=self.item.uom_basic.id, # UOMBasic is ID from tblDG_Item_Master, which is FK to Unit_Master.Id
            amendment_no=old_amendment_no, # Log the *old* amendment number
            quantity=old_quantity # Log the *old* quantity
        )

        # Update current TPLItem
        self.quantity = parsed_new_quantity
        self.amendment_no = new_amendment_no
        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().strftime('%H:%M:%S')
        self.session_id = session_username
        self.save(update_fields=['quantity', 'amendment_no', 'sys_date', 'sys_time', 'session_id'])
        return True


class TPLAmendment(models.Model):
    # No direct PK from ASP.NET, Django will add an auto-incrementing PK
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.CharField(max_length=10, db_column='SysTime')
    session_id = models.CharField(max_length=50, db_column='SessionId')
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    won_no = models.CharField(max_length=50, db_column='WONo')
    tpl_id = models.IntegerField(db_column='TPLId') # Corresponds to tblDG_TPL_Master.Id
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    item_id = models.IntegerField(db_column='ItemId') # Corresponds to tblDG_Item_Master.Id
    description = models.CharField(max_length=255, db_column='Description', null=True, blank=True)
    uom = models.IntegerField(db_column='UOM', null=True, blank=True) # Corresponds to UOMBasic ID
    amendment_no = models.IntegerField(db_column='AmdNo', null=True, blank=True)
    quantity = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Amd'
        verbose_name = 'TPL Amendment'
        verbose_name_plural = 'TPL Amendments'

    def __str__(self):
        return f"Amd for TPL {self.tpl_id} (Item: {self.item_id}) at {self.sys_date}"

```

### 4.2 Forms

Only `quantity` is editable via the form. Other fields are displayed as read-only.

```python
# design_transactions/forms.py
from django import forms
from .models import TPLItem

class TPLItemForm(forms.ModelForm):
    # Using DecimalField for quantity to match database precision
    quantity = forms.DecimalField(
        max_digits=18, 
        decimal_places=3,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'pattern': r'^\d{1,15}(\.\d{0,3})?$', # Matches ASP.NET RegularExpressionValidator
            'title': 'Enter a number with up to 15 digits before decimal and 3 after.'
        })
    )

    class Meta:
        model = TPLItem
        fields = ['quantity']
        # No need for other fields here as they are read-only in the UI.

    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity < 0:
            raise forms.ValidationError("Quantity cannot be negative.")
        # Additional custom validation if needed (e.g., business rules)
        return quantity

```

### 4.3 Views

We'll provide `ListView`, `CreateView`, `UpdateView`, `DeleteView` as per the general guidelines, and a `TablePartialView` for HTMX. The ASP.NET page specifically maps to the `UpdateView` here.

```python
# design_transactions/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from .models import TPLItem, DesignItem, Unit
from .forms import TPLItemForm

# This view is for the list page, leading to the edit page
class TPLItemListView(ListView):
    model = TPLItem
    template_name = 'design_transactions/tplitem/list.html'
    context_object_name = 'tplitems'
    
    # We will pass won_no and item_id as query parameters for filtering the initial list
    def get_queryset(self):
        # Example: Filter by WONo and ItemId if provided in query params
        # In a real scenario, this would likely be part of a broader WO management page
        queryset = super().get_queryset().select_related('item', 'item__uom_basic')
        won_no = self.request.GET.get('won_no')
        item_id = self.request.GET.get('item_id')
        if won_no:
            queryset = queryset.filter(won_no=won_no)
        if item_id:
            queryset = queryset.filter(item__id=item_id)
        return queryset

# This view is for the HTMX-loaded table partial
class TPLItemTablePartialView(TPLItemListView):
    template_name = 'design_transactions/tplitem/_tplitem_table.html'

# This corresponds to the ASP.NET TPL_Design_Item_Edit.aspx page
class TPLItemUpdateView(UpdateView):
    model = TPLItem
    form_class = TPLItemForm
    template_name = 'design_transactions/tplitem/_tplitem_form.html' # This will be the modal content
    
    # Success URL should ideally point to the WO Tree View or the list of TPL Items
    # We use reverse_lazy for URL resolution
    success_url = reverse_lazy('tplitem_list') 

    def get_object(self, queryset=None):
        """
        Retrieves the TPLItem instance using the 'pk' from the URL.
        The ASP.NET code used 'asslyId' which maps to 'Id' (PK) of tblDG_TPL_Master.
        """
        return get_object_or_404(TPLItem.objects.select_related('item', 'item__uom_basic'), pk=self.kwargs['pk'])

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass additional read-only data to the template
        tpl_item = self.get_object()
        context['won_no'] = tpl_item.won_no
        context['item_code'] = tpl_item.item.item_code
        context['manufacturer_description'] = tpl_item.item.manufacturer_description
        context['uom_symbol'] = tpl_item.item.uom_basic.symbol
        return context

    def form_valid(self, form):
        # Get session details for logging amendment
        session_username = self.request.session.get('username', 'anonymous') # Fallback if not logged in
        
        # Call the fat model method to handle update and amendment logging
        try:
            self.object.update_quantity_and_log_amendment(
                new_quantity=form.cleaned_data['quantity'], 
                session_username=session_username
            )
            messages.success(self.request, 'TPL Item quantity updated successfully.')
            
            # For HTMX requests, return a 204 No Content with HX-Trigger
            if self.request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshTPLItemList' # Trigger client-side refresh of the list
                    }
                )
            return HttpResponseRedirect(self.get_success_url()) # For non-HTMX (full page reload)
        except ValueError as e:
            form.add_error('quantity', str(e)) # Add error to the form
            return self.form_invalid(form) # Re-render form with errors
        except Exception as e:
            messages.error(self.request, f"An unexpected error occurred: {e}")
            return self.form_invalid(form)

    def form_invalid(self, form):
        # For HTMX requests, re-render the form content
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, self.get_context_data(form=form))
        return super().form_invalid(form)


# Generic Create/Delete views for completeness as per template guidelines
class TPLItemCreateView(CreateView):
    model = TPLItem
    form_class = TPLItemForm
    template_name = 'design_transactions/tplitem/_tplitem_form.html'
    success_url = reverse_lazy('tplitem_list')

    def form_valid(self, form):
        # In a real scenario, creation would involve populating many more fields
        # beyond just quantity, likely including item, won_no, c_id etc.
        # This is a placeholder for a 'new TPL Item' if such a feature exists.
        form.instance.session_id = self.request.session.get('username', 'anonymous')
        form.instance.company_id = self.request.session.get('compid', 1) # Default
        form.instance.financial_year_id = self.request.session.get('finyear', 1) # Default
        
        response = super().form_valid(form)
        messages.success(self.request, 'TPL Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTPLItemList'
                }
            )
        return response

class TPLItemDeleteView(DeleteView):
    model = TPLItem
    template_name = 'design_transactions/tplitem/confirm_delete.html'
    success_url = reverse_lazy('tplitem_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'TPL Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTPLItemList'
                }
            )
        return response

```

### 4.4 Templates

The `list.html` and `_tplitem_table.html` for general TPL Item listing.
The `_tplitem_form.html` will be the content of the modal for `UpdateView` (and `CreateView`).

```html
<!-- design_transactions/tplitem/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">TPL Items</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'tplitem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New TPL Item
        </button>
    </div>
    
    <div id="tplitemTable-container"
         hx-trigger="load, refreshTPLItemList from:body"
         hx-get="{% url 'tplitem_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading TPL Items...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
        // For example, managing modal state or other UI interactions
        Alpine.data('modal', () => ({
            open: false,
            show() { this.open = true },
            hide() { this.open = false },
        }));
    });

    // Re-initialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tplitemTable-container') {
            $('#tplitemTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing table if it exists
            });
        }
    });

    // Hide modal on HX-Trigger that refreshes list
    document.body.addEventListener('refreshTPLItemList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
            modal.classList.add('hidden'); // Ensure it's hidden
        }
    });
</script>
{% endblock %}
```

```html
<!-- design_transactions/tplitem/_tplitem_table.html -->
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="tplitemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in tplitems %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.won_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.item.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.item.manufacturer_description }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.item.uom_basic.symbol }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.quantity }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'tplitem_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'tplitem_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No TPL items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTable initialization is handled in list.html via htmx:afterSwap to ensure it runs after content is loaded
// No need to initialize DataTables here directly on document.ready
</script>
```

```html
<!-- design_transactions/tplitem/_tplitem_form.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} TPL Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            <!-- Read-only fields from ASP.NET Page_Load display -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Work Order No:</label>
                <p class="mt-1 text-gray-900">{{ won_no }}</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Item Code:</label>
                <p class="mt-1 text-gray-900">{{ item_code }}</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Description:</label>
                <p class="mt-1 text-gray-900">{{ manufacturer_description }}</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">UOM:</label>
                <p class="mt-1 text-gray-900">{{ uom_symbol }}</p>
            </div>

            <!-- Editable Quantity field -->
            <div class="mb-4">
                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.quantity.label }}
                </label>
                {{ form.quantity }}
                {% if form.quantity.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

```html
<!-- design_transactions/tplitem/confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the TPL Item for WO: <strong>{{ object.won_no }}</strong>, Item Code: <strong>{{ object.item.item_code }}</strong>?</p>
    <form hx-post="{% url 'tplitem_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

### 4.5 URLs

```python
# design_transactions/urls.py
from django.urls import path
from .views import (
    TPLItemListView, 
    TPLItemCreateView, 
    TPLItemUpdateView, 
    TPLItemDeleteView,
    TPLItemTablePartialView
)

urlpatterns = [
    path('tplitems/', TPLItemListView.as_view(), name='tplitem_list'),
    path('tplitems/add/', TPLItemCreateView.as_view(), name='tplitem_add'),
    path('tplitems/edit/<int:pk>/', TPLItemUpdateView.as_view(), name='tplitem_edit'),
    path('tplitems/delete/<int:pk>/', TPLItemDeleteView.as_view(), name='tplitem_delete'),
    # HTMX specific endpoint for table refresh
    path('tplitems/table/', TPLItemTablePartialView.as_view(), name='tplitem_table'),
]

```

### 4.6 Tests

```python
# design_transactions/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.utils import timezone
from decimal import Decimal
from .models import Unit, DesignItem, TPLItem, TPLAmendment

class TPLItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependencies first
        cls.unit = Unit.objects.create(id=1, symbol='NOS')
        cls.design_item = DesignItem.objects.create(
            id=101,
            item_code='ITM001',
            manufacturer_description='Widget Alpha',
            uom_basic=cls.unit,
            c_id=1
        )
        # Create a TPLItem for testing
        cls.tpl_item = TPLItem.objects.create(
            id=1,
            item=cls.design_item,
            won_no='WO2023-001',
            c_id=1,
            company_id=1,
            financial_year_id=2023,
            quantity=Decimal('10.500'),
            amendment_no=0,
            p_id=1,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().strftime('%H:%M:%S'),
            session_id='testuser'
        )
        cls.tpl_item_no_amd = TPLItem.objects.create(
            id=2,
            item=cls.design_item,
            won_no='WO2023-002',
            c_id=2,
            company_id=1,
            financial_year_id=2023,
            quantity=Decimal('5.000'),
            amendment_no=None, # Test None case for amendment_no
            p_id=2,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().strftime('%H:%M:%S'),
            session_id='testuser'
        )

    def test_tplitem_creation(self):
        obj = TPLItem.objects.get(id=1)
        self.assertEqual(obj.won_no, 'WO2023-001')
        self.assertEqual(obj.item.item_code, 'ITM001')
        self.assertEqual(obj.quantity, Decimal('10.500'))
        self.assertEqual(obj.amendment_no, 0)
        self.assertEqual(obj.session_id, 'testuser')

    def test_update_quantity_and_log_amendment_success(self):
        initial_quantity = self.tpl_item.quantity
        initial_amendment_no = self.tpl_item.amendment_no
        new_quantity = Decimal('12.750')
        session_id = 'updateduser'

        self.tpl_item.update_quantity_and_log_amendment(new_quantity, session_id)

        self.tpl_item.refresh_from_db() # Reload the object from DB
        self.assertEqual(self.tpl_item.quantity, new_quantity)
        self.assertEqual(self.tpl_item.amendment_no, initial_amendment_no + 1)
        self.assertEqual(self.tpl_item.session_id, session_id)

        # Verify amendment log entry
        amendment = TPLAmendment.objects.last()
        self.assertIsNotNone(amendment)
        self.assertEqual(amendment.tpl_id, self.tpl_item.id)
        self.assertEqual(amendment.quantity, initial_quantity)
        self.assertEqual(amendment.amendment_no, initial_amendment_no)
        self.assertEqual(amendment.session_id, session_id)
        self.assertEqual(amendment.description, self.design_item.manufacturer_description)
        self.assertEqual(amendment.uom, self.unit.id)

    def test_update_quantity_and_log_amendment_no_initial_amendment_no(self):
        initial_quantity = self.tpl_item_no_amd.quantity
        initial_amendment_no = 0 # Expected when None
        new_quantity = Decimal('6.000')
        session_id = 'user_with_no_amd'

        self.tpl_item_no_amd.update_quantity_and_log_amendment(new_quantity, session_id)
        self.tpl_item_no_amd.refresh_from_db()

        self.assertEqual(self.tpl_item_no_amd.quantity, new_quantity)
        self.assertEqual(self.tpl_item_no_amd.amendment_no, initial_amendment_no + 1)

        amendment = TPLAmendment.objects.last()
        self.assertEqual(amendment.quantity, initial_quantity)
        self.assertEqual(amendment.amendment_no, initial_amendment_no)

    def test_update_quantity_and_log_amendment_invalid_quantity(self):
        with self.assertRaisesMessage(ValueError, "Invalid quantity format. Please enter a valid number."):
            self.tpl_item.update_quantity_and_log_amendment("abc", 'user')
        with self.assertRaisesMessage(ValueError, "Quantity cannot be negative."):
            self.tpl_item.update_quantity_and_log_amendment(Decimal('-5.000'), 'user')

class TPLItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependencies first
        cls.unit = Unit.objects.create(id=1, symbol='NOS')
        cls.design_item = DesignItem.objects.create(
            id=101,
            item_code='ITM001',
            manufacturer_description='Widget Alpha',
            uom_basic=cls.unit,
            c_id=1
        )
        cls.tpl_item = TPLItem.objects.create(
            id=1,
            item=cls.design_item,
            won_no='WO2023-001',
            c_id=1,
            company_id=1,
            financial_year_id=2023,
            quantity=Decimal('10.500'),
            amendment_no=0,
            p_id=1,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().strftime('%H:%M:%S'),
            session_id='testuser'
        )
        cls.tpl_item_2 = TPLItem.objects.create(
            id=2,
            item=cls.design_item,
            won_no='WO2023-002',
            c_id=2,
            company_id=1,
            financial_year_id=2023,
            quantity=Decimal('20.000'),
            amendment_no=1,
            p_id=2,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().strftime('%H:%M:%S'),
            session_id='testuser2'
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables if your app uses them for CompId, FinYearId, etc.
        session = self.client.session
        session['username'] = 'testuser'
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()
    
    def test_list_view(self):
        response = self.client.get(reverse('tplitem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tplitem/list.html')
        # Check that context contains tplitems (rendered by the partial view via HTMX)
        # Actual object check will be in the partial view test

    def test_table_partial_view(self):
        response = self.client.get(reverse('tplitem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tplitem/_tplitem_table.html')
        self.assertContains(response, self.tpl_item.won_no)
        self.assertContains(response, self.tpl_item_2.won_no)
        self.assertContains(response, self.tpl_item.item.item_code)
        self.assertContains(response, str(self.tpl_item.quantity))


    def test_update_view_get(self):
        response = self.client.get(reverse('tplitem_edit', args=[self.tpl_item.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tplitem/_tplitem_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.tpl_item)
        self.assertContains(response, self.tpl_item.won_no) # Check read-only data
        self.assertContains(response, self.tpl_item.item.item_code)

    def test_update_view_post_success_htmx(self):
        new_quantity = Decimal('15.000')
        data = {'quantity': str(new_quantity)} # Form expects string from input
        
        response = self.client.post(reverse('tplitem_edit', args=[self.tpl_item.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX response
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTPLItemList')

        self.tpl_item.refresh_from_db()
        self.assertEqual(self.tpl_item.quantity, new_quantity)
        self.assertEqual(self.tpl_item.amendment_no, 1) # Initial 0 + 1

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'TPL Item quantity updated successfully.')

    def test_update_view_post_invalid_quantity_htmx(self):
        data = {'quantity': 'invalid'}
        response = self.client.post(reverse('tplitem_edit', args=[self.tpl_item.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'design_transactions/tplitem/_tplitem_form.html')
        self.assertContains(response, "Invalid quantity format. Please enter a valid number.")
        self.assertFalse('HX-Trigger' in response.headers)

    def test_update_view_post_negative_quantity_htmx(self):
        data = {'quantity': '-5.000'}
        response = self.client.post(reverse('tplitem_edit', args=[self.tpl_item.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'design_transactions/tplitem/_tplitem_form.html')
        self.assertContains(response, "Quantity cannot be negative.")
        self.assertFalse('HX-Trigger' in response.headers)

    def test_create_view_get(self):
        response = self.client.get(reverse('tplitem_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tplitem/_tplitem_form.html')
        self.assertTrue('form' in response.context)
        # For Add, won_no, etc. might not be in context unless specifically passed,
        # so don't assert their presence for 'add' form.

    def test_create_view_post_success_htmx(self):
        # Requires valid item, won_no, c_id, etc. which aren't in form fields.
        # This test relies on TPLItemCreateView's placeholder logic.
        new_design_item = DesignItem.objects.create(
            id=102, item_code='ITM002', manufacturer_description='Widget Beta', uom_basic=self.unit, c_id=2
        )
        data = {
            'quantity': '25.000',
            # For a real creation, these would likely be in the form,
            # but per current ASP.NET page, they are read-only.
            # Assuming dummy values or specific context for creating.
            # Here we'll patch the form.instance if it were a real create use-case
            # For this simple form, we just ensure it passes validation.
        }
        # To make TPLItemCreateView work minimally for testing form_valid
        with self.settings(DEFAULT_ITEM_ID=new_design_item.id, DEFAULT_WON_NO='WO-NEW', DEFAULT_C_ID=3, DEFAULT_P_ID=3):
            from .views import TPLItemCreateView
            original_form_valid = TPLItemCreateView.form_valid
            def mock_form_valid(self, form):
                form.instance.item_id = self.request.session.get('DEFAULT_ITEM_ID', new_design_item.id) # Set a default for test
                form.instance.won_no = self.request.session.get('DEFAULT_WON_NO', 'WO-NEW')
                form.instance.c_id = self.request.session.get('DEFAULT_C_ID', 3)
                form.instance.p_id = self.request.session.get('DEFAULT_P_ID', 3)
                return original_form_valid(self, form)
            TPLItemCreateView.form_valid = mock_form_valid
            
            response = self.client.post(reverse('tplitem_add'), data, HTTP_HX_REQUEST='true')
            
            self.assertEqual(response.status_code, 204)
            self.assertTrue('HX-Trigger' in response.headers)
            self.assertEqual(response.headers['HX-Trigger'], 'refreshTPLItemList')
            self.assertTrue(TPLItem.objects.filter(quantity=Decimal('25.000')).exists())
            messages = list(get_messages(response.wsgi_request))
            self.assertEqual(str(messages[0]), 'TPL Item added successfully.')

            TPLItemCreateView.form_valid = original_form_valid # Restore original

    def test_delete_view_get(self):
        response = self.client.get(reverse('tplitem_delete', args=[self.tpl_item.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tplitem/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], self.tpl_item)

    def test_delete_view_post_success_htmx(self):
        response = self.client.post(reverse('tplitem_delete', args=[self.tpl_item.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTPLItemList')
        self.assertFalse(TPLItem.objects.filter(pk=self.tpl_item.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'TPL Item deleted successfully.')

```

## Step 5: HTMX and Alpine.js Integration

The provided templates and views already implement HTMX for:
-   Loading the list table dynamically (`hx-get` on `tplitemTable-container` for `_tplitem_table.html`).
-   Triggering list refresh after CRUD operations (`HX-Trigger: refreshTPLItemList`).
-   Loading add/edit/delete forms into a modal (`hx-get` on buttons, `hx-target="#modalContent"`).
-   Submitting forms within the modal (`hx-post` on form, `hx-swap="none"` with `HX-Trigger`).
-   Hiding the modal after successful submission (Alpine.js on `refreshTPLItemList` trigger).
-   Loading indicators (`htmx-indicator`).

Alpine.js is integrated to manage the modal's active state (`is-active` class) and hide it when clicking outside or after a successful HTMX trigger.

DataTables is initialized on the `_tplitem_table.html` content after it's loaded via HTMX, ensuring proper client-side searching, sorting, and pagination. The `htmx:afterSwap` event listener ensures DataTables is re-initialized correctly when the table content changes.

## Final Notes

This modernization plan provides a structured approach to transition from the ASP.NET TPL Item Edit page to a modern Django application. It emphasizes:

-   **Business Value:** By modernizing to Django with HTMX/Alpine.js, the organization gains a highly responsive and interactive user experience without the complexity of traditional JavaScript frameworks, leading to faster development cycles and reduced maintenance overhead. The "Fat Model, Thin View" architecture enhances code maintainability and scalability.
-   **Automation Focus:** The detailed breakdown into models, forms, views, templates, and tests provides clear, actionable steps that can be translated into automated migration scripts or guided through conversational AI. The use of `managed=False` allows seamless integration with existing databases.
-   **Maintainability and Scalability:** Adhering to Django best practices, separation of concerns, and comprehensive testing ensures the new application is robust, easy to maintain, and capable of scaling with future business needs. The amendment logging in the model ensures business logic is encapsulated correctly.
-   **User Experience:** HTMX and Alpine.js ensure that all user interactions (updates, deletes, form submissions) are smooth and instantaneous, providing a desktop-like experience within the browser. DataTables empowers users with powerful data manipulation features directly on the frontend.