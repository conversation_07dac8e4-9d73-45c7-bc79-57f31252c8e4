The organization's journey from ASP.NET to Django with AI-assisted automation is a strategic move towards a more flexible, scalable, and maintainable ERP system. This modernization plan focuses on transforming a legacy ASP.NET BOM (Bill of Material) item editing page into a streamlined Django application. By leveraging modern Django patterns, HTMX, and Alpine.js, we aim to deliver a highly interactive user experience without complex JavaScript, while maintaining a robust, testable, and clean backend.

The current ASP.NET page, `BOM_Design_Assembly_Edit.aspx`, is designed to edit a single BOM item's details for a given Work Order Number (`WONo`) and a specific Item ID (`ItemId`). Despite using a `GridView`, it effectively functions as a single-record edit form, updating both the `BOM_Master` and its related `Item_Master` data, including file attachments and an amendment history.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination (note: for a single item edit page, DataTables is less relevant for the *main* display, but if this were a list, it would be crucial). I will provide the single item edit approach.
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET page interacts with three primary database tables: `tblDG_BOM_Master` (for BOM assembly details), `tblDG_Item_Master` (for individual item attributes like description, UOM, and attachments), and `Unit_Master` (a lookup for Units of Measure). It also logs changes to `tblDG_BOM_Amd`. The page edits a single `tblDG_BOM_Master` record, which is linked to a `tblDG_Item_Master` record via `ItemId`.

**Identified Tables and Key Columns:**

*   **`tblDG_BOM_Master`**:
    *   `Id` (Primary Key, `int`)
    *   `WONo` (`nvarchar`, Work Order Number)
    *   `AmdNo` (`int`, Amendment Number)
    *   `EquipmentNo` (`nvarchar`)
    *   `UnitNo` (`nvarchar`)
    *   `PartNo` (`nvarchar`)
    *   `CId` (`int`)
    *   `ItemId` (`int`, Foreign Key to `tblDG_Item_Master.Id`)
    *   `Qty` (`float`, Quantity)
    *   `Revision` (`nvarchar`)
    *   `PId` (`int`, Parent ID, usually '0' for top-level)
    *   `CompId` (`int`, Company ID)
    *   `FinYearId` (`int`, Financial Year ID)
    *   `SysDate` (`datetime`)
    *   `SysTime` (`time`)
    *   `SessionId` (`nvarchar`)

*   **`tblDG_Item_Master`**:
    *   `Id` (Primary Key, `int`)
    *   `ManfDesc` (`nvarchar`, Manufacturer Description)
    *   `UOMBasic` (`int`, Foreign Key to `Unit_Master.Id`)
    *   `FileName` (`nvarchar`, Drawing/Image filename)
    *   `FileData` (`varbinary(max)`, Drawing/Image binary data)
    *   `ContentType` (`nvarchar`, Drawing/Image content type)
    *   `AttName` (`nvarchar`, Spec Sheet filename)
    *   `AttData` (`varbinary(max)`, Spec Sheet binary data)
    *   `AttContentType` (`nvarchar`, Spec Sheet content type)
    *   `AttSize` (`float`, Spec Sheet file size)
    *   `OpeningBalDate` (`datetime`)
    *   `OpeningBalQty` (`float`)
    *   `CompId` (`int`)
    *   `FinYearId` (`int`)
    *   `SysDate` (`datetime`)
    *   `SysTime` (`time`)
    *   `SessionId` (`nvarchar`)

*   **`Unit_Master`**:
    *   `Id` (Primary Key, `int`)
    *   `Symbol` (`nvarchar`, Unit Symbol, e.g., "NOS")

*   **`tblDG_BOM_Amd`**: (Amendment/Audit Log)
    *   `Id` (Primary Key, `int`)
    *   `SysDate` (`datetime`)
    *   `SysTime` (`time`)
    *   `SessionId` (`nvarchar`)
    *   `CompId` (`int`)
    *   `FinYearId` (`int`)
    *   `WONo` (`nvarchar`)
    *   `BOMId` (`int`, Foreign Key to `tblDG_BOM_Master.Id`)
    *   `PId` (`int`)
    *   `CId` (`int`)
    *   `ItemId` (`int`, Foreign Key to `tblDG_Item_Master.Id`)
    *   `Description` (`nvarchar`)
    *   `UOM` (`int`)
    *   `AmdNo` (`int`)
    *   `Qty` (`float`)

### Step 2: Identify Backend Functionality

**Analysis:**
The page's core functionality is to allow a user to **read** (view) and **update** (edit) a specific BOM item. It also supports **delete** operations for both the BOM item itself and its associated file attachments (drawing and spec sheet). A crucial business rule involves incrementing an `AmendmentNo` and logging historical changes to `tblDG_BOM_Amd` upon any update.

*   **Read**: Displays details of a single BOM item (identified by `WONo` and `ItemId` from query string, mapping to a `tblDG_BOM_Master.Id`).
*   **Update**: Edits `Qty`, `Revision` (from `tblDG_BOM_Master`), `ManfDesc`, `UOMBasic`, and uploads/updates `FileName`, `FileData`, `AttName`, `AttData` (from `tblDG_Item_Master`). This operation triggers an increment of `AmdNo` and an audit log entry in `tblDG_BOM_Amd`.
*   **Delete**: Deletes the `tblDG_BOM_Master` record and its associated `tblDG_Item_Master` record.
*   **File Delete**: Clears specific file data (`FileName`, `FileData`, etc. for drawing; `AttName`, `AttData`, etc. for spec sheet) within the `tblDG_Item_Master` record.
*   **Navigation**: Redirects to a tree view page (`BOM_Design_WO_TreeView_Edit.aspx`) upon cancel or successful save.
*   **Validation**: Input validation for `Qty`, `Revision`, `ManfDesc` and `UOMBasic`. Quantity allows up to 3 decimal places.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET `GridView` functions as a form for a single record.
*   **Display**: `lblWONo` for Work Order No, `Label` controls for `AmdNo`, `EquipmentNo`, `UnitNo`, `PartNo`, `Description`, `UOM`, `Qty`, `Revision`, `HyperLink` for file downloads.
*   **Input (Edit Mode)**: `TextBox` for `ManfDesc`, `txtQuntity`, `txtRevision`. `DropDownList` for `DDLUnitBasic` (UOM). `FileUpload` for `DrwUpload` (Drawing/Image) and `OtherUpload` (Spec. Sheet).
*   **Actions**: `LinkButton` for `Edit`, `Update`, `Cancel`, `Delete`. `ImageButton` for file deletion.
*   **Client-side Validation**: `RequiredFieldValidator` and `RegularExpressionValidator` are present, which will be translated to Django form validation.
*   **Client-side Confirmation**: `OnClientClick="return confirmationDelete()"` and `confirmationUpdate()"` will be replaced by HTMX/Alpine.js modal confirmation.

### Step 4: Generate Django Code

We will create a Django app, let's call it `design_bom`.

#### 4.1 Models (`design_bom/models.py`)

This file will define the Django ORM mappings to your existing database tables. We'll use `managed = False` because the tables already exist.

```python
from django.db import models
from django.utils import timezone
from datetime import time
from decimal import Decimal

class UnitMaster(models.Model):
    """
    Maps to Unit_Master table for Units of Measure.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master. Contains detailed item information including attachments.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    manufacturer_description = models.TextField(db_column='ManfDesc', blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True, related_name='items')
    
    drawing_filename = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    drawing_filedata = models.BinaryField(db_column='FileData', blank=True, null=True)
    drawing_content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    
    spec_sheet_filename = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    spec_sheet_filedata = models.BinaryField(db_column='AttData', blank=True, null=True)
    spec_sheet_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)
    spec_sheet_filesize = models.FloatField(db_column='AttSize', blank=True, null=True)

    company_id = models.IntegerField(db_column='CompId', default=1) # Placeholder, ideally from session
    financial_year_id = models.IntegerField(db_column='FinYearId', default=1) # Placeholder
    system_date = models.DateField(db_column='SysDate', auto_now_add=True)
    system_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    session_id = models.CharField(db_column='SessionId', max_length=100, blank=True, null=True) # Placeholder, ideally from session
    
    opening_balance_date = models.DateField(db_column='OpeningBalDate', blank=True, null=True)
    opening_balance_quantity = models.FloatField(db_column='OpeningBalQty', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.manufacturer_description or f"Item {self.id}"

    def delete_file_data(self, file_type):
        """
        Clears file data for a given file_type (e.g., 'drawing' or 'spec_sheet').
        """
        if file_type == 'drawing':
            self.drawing_filename = None
            self.drawing_filedata = None
            self.drawing_content_type = None
        elif file_type == 'spec_sheet':
            self.spec_sheet_filename = None
            self.spec_sheet_filedata = None
            self.spec_sheet_content_type = None
            self.spec_sheet_filesize = None
        self.save()
        return True

class BOMMaster(models.Model):
    """
    Maps to tblDG_BOM_Master. Contains Bill of Material assembly information.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    work_order_no = models.CharField(db_column='WONo', max_length=50)
    amendment_no = models.IntegerField(db_column='AmdNo', default=0)
    equipment_no = models.CharField(db_column='EquipmentNo', max_length=50, blank=True, null=True)
    unit_no = models.CharField(db_column='UnitNo', max_length=50, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=50, blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', related_name='bom_masters')
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    revision = models.CharField(db_column='Revision', max_length=50, blank=True, null=True)
    parent_id = models.IntegerField(db_column='PId', default=0) # Typically '0' for top-level
    
    company_id = models.IntegerField(db_column='CompId', default=1) # Placeholder
    financial_year_id = models.IntegerField(db_column='FinYearId', default=1) # Placeholder
    system_date = models.DateField(db_column='SysDate', auto_now_add=True)
    system_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    session_id = models.CharField(db_column='SessionId', max_length=100, blank=True, null=True) # Placeholder

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for WO: {self.work_order_no}, Item: {self.item.manufacturer_description}"

    def update_item_details(self, description, uom_id, quantity, revision, drawing_file=None, spec_sheet_file=None, session_id='system'):
        """
        Updates BOMMaster and its associated ItemMaster details, handles file uploads,
        increments amendment number, and logs to BOMAmendment.
        This encapsulates the complex update logic from the ASP.NET code-behind.
        """
        old_description = self.item.manufacturer_description
        old_uom_id = self.item.uom_basic_id
        old_qty = self.quantity
        old_amd_no = self.amendment_no

        # Log old data to BOMAmendment
        BOMAmendment.objects.create(
            system_date=timezone.now().date(),
            system_time=timezone.now().time(),
            session_id=session_id,
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            work_order_no=self.work_order_no,
            bom_master=self,
            parent_id=self.parent_id,
            c_id=self.c_id,
            item=self.item,
            description=old_description,
            uom=UnitMaster.objects.get(id=old_uom_id) if old_uom_id else None, # Store old UOM symbol
            amendment_no=old_amd_no,
            quantity=old_qty
        )

        # Update ItemMaster fields
        self.item.manufacturer_description = description
        self.item.uom_basic_id = uom_id

        # Handle drawing file upload
        if drawing_file:
            self.item.drawing_filename = drawing_file.name
            self.item.drawing_content_type = drawing_file.content_type
            self.item.drawing_filedata = drawing_file.read()
        
        # Handle spec sheet file upload
        if spec_sheet_file:
            self.item.spec_sheet_filename = spec_sheet_file.name
            self.item.spec_sheet_content_type = spec_sheet_file.content_type
            self.item.spec_sheet_filedata = spec_sheet_file.read()
            self.item.spec_sheet_filesize = spec_sheet_file.size # Assuming size is in bytes

        self.item.system_date = timezone.now().date()
        self.item.system_time = timezone.now().time()
        self.item.session_id = session_id
        self.item.save()

        # Update BOMMaster fields
        self.quantity = Decimal(quantity)
        self.revision = revision
        self.amendment_no = old_amd_no + 1 # Increment amendment number
        
        self.system_date = timezone.now().date()
        self.system_time = timezone.now().time()
        self.session_id = session_id
        self.save()

        return True

    def delete_cascade(self):
        """
        Deletes the BOMMaster record and its associated ItemMaster record.
        Mimics the cascading delete in ASP.NET.
        """
        item_to_delete = self.item
        self.delete() # Deletes the BOMMaster record
        item_to_delete.delete() # Deletes the associated ItemMaster record
        return True


class BOMAmendment(models.Model):
    """
    Maps to tblDG_BOM_Amd. Logs historical changes to BOM items.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    system_date = models.DateField(db_column='SysDate')
    system_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=100, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    work_order_no = models.CharField(db_column='WONo', max_length=50)
    bom_master = models.ForeignKey(BOMMaster, models.DO_NOTHING, db_column='BOMId', related_name='amendments')
    parent_id = models.IntegerField(db_column='PId', blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    description = models.TextField(db_column='Description', blank=True, null=True)
    uom = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOM', blank=True, null=True) # UOM here seems to be an ID
    amendment_no = models.IntegerField(db_column='AmdNo', blank=True, null=True)
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Amd'
        verbose_name = 'BOM Amendment'
        verbose_name_plural = 'BOM Amendments'

    def __str__(self):
        return f"Amendment {self.amendment_no} for BOM {self.bom_master.id}"

```

#### 4.2 Forms (`design_bom/forms.py`)

A single form combining fields from `BOMMaster` and `ItemMaster` to simplify the UI interaction. File fields will be handled separately.

```python
from django import forms
from django.core.validators import RegexValidator
from decimal import Decimal, InvalidOperation
from .models import BOMMaster, ItemMaster, UnitMaster

class BOMItemEditForm(forms.Form):
    """
    Form for editing a BOMMaster entry and its associated ItemMaster details.
    Handles fields from both models and file uploads.
    """
    # Fields from ItemMaster
    manufacturer_description = forms.CharField(
        max_length=500,
        required=True,
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'rows': 3
        })
    )
    uom_basic = forms.ModelChoiceField(
        queryset=UnitMaster.objects.all(),
        required=True,
        empty_label="-- Select UOM --",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    drawing_file = forms.FileField(
        required=False,
        label='Drawing/Image File',
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )
    spec_sheet_file = forms.FileField(
        required=False,
        label='Spec. Sheet File',
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    # Fields from BOMMaster
    quantity = forms.CharField( # Use CharField for regex validation then convert
        max_length=15,
        required=True,
        validators=[
            RegexValidator(
                regex=r'^\d{1,15}(\.\d{0,3})?$',
                message="Quantity must be a number with up to 3 decimal places."
            )
        ],
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    revision = forms.CharField(
        max_length=50,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    def __init__(self, *args, **kwargs):
        self.bom_item = kwargs.pop('instance', None)
        super().__init__(*args, **kwargs)
        if self.bom_item:
            self.fields['manufacturer_description'].initial = self.bom_item.item.manufacturer_description
            self.fields['uom_basic'].initial = self.bom_item.item.uom_basic
            self.fields['quantity'].initial = self.bom_item.quantity
            self.fields['revision'].initial = self.bom_item.revision
            # Initial file fields are tricky for BinaryField, they just indicate presence
            self.fields['drawing_file'].label = f"Drawing/Image File (Current: {self.bom_item.item.drawing_filename or 'None'})"
            self.fields['spec_sheet_file'].label = f"Spec. Sheet File (Current: {self.bom_item.item.spec_sheet_filename or 'None'})"

    def clean_quantity(self):
        qty_str = self.cleaned_data['quantity']
        try:
            # Convert to Decimal for precise storage, as per ASP.NET's N3 format
            quantity = Decimal(qty_str)
            return quantity
        except InvalidOperation:
            raise forms.ValidationError("Quantity must be a valid number.")

    def save(self, session_id='system'):
        if not self.bom_item:
            raise ValueError("Instance not provided for saving form.")

        # Extract cleaned data
        description = self.cleaned_data['manufacturer_description']
        uom_id = self.cleaned_data['uom_basic'].id
        quantity = self.cleaned_data['quantity']
        revision = self.cleaned_data['revision']
        drawing_file = self.cleaned_data.get('drawing_file')
        spec_sheet_file = self.cleaned_data.get('spec_sheet_file')

        # Delegate to the model's fat method
        self.bom_item.update_item_details(
            description=description,
            uom_id=uom_id,
            quantity=quantity,
            revision=revision,
            drawing_file=drawing_file,
            spec_sheet_file=spec_sheet_file,
            session_id=session_id
        )
        return self.bom_item

```

#### 4.3 Views (`design_bom/views.py`)

We'll use a single `UpdateView` for the main edit page and custom `DeleteView` for specific file deletions.

```python
from django.views.generic import UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse, Http404
from django.shortcuts import get_object_or_404, redirect
from django.utils import timezone
import os # For mimetype

from .models import BOMMaster, ItemMaster
from .forms import BOMItemEditForm

# Placeholder for session data. In a real ERP, this would come from request.user or middleware.
CURRENT_COMPANY_ID = 1
CURRENT_FINANCIAL_YEAR_ID = 1

class BOMItemUpdateView(UpdateView):
    """
    Handles displaying and processing the form for editing a single BOM item.
    Corresponds to the main functionality of BOM_Design_Assembly_Edit.aspx.
    """
    model = BOMMaster
    form_class = BOMItemEditForm
    template_name = 'design_bom/bom_item_edit.html' # Main page template
    context_object_name = 'bom_item'

    def get_queryset(self):
        """
        Filter the queryset based on WONo and ItemId from URL parameters.
        The 'pk' in the URL corresponds to BOMMaster.Id.
        """
        pk = self.kwargs.get('pk')
        work_order_no = self.request.GET.get('WONo') # Assuming WONo comes from GET for context display
        
        queryset = super().get_queryset().select_related('item', 'item__uom_basic').filter(id=pk)

        if work_order_no:
            queryset = queryset.filter(work_order_no=work_order_no)
        # Ensure it's for top-level items if PId is consistently '0'
        queryset = queryset.filter(parent_id=0, company_id=CURRENT_COMPANY_ID, financial_year_id__lte=CURRENT_FINANCIAL_YEAR_ID)
        
        # If the query string ItemId was used to find the BOM entry, 
        # it would be applied here, e.g., .filter(item__id=self.request.GET.get('ItemId'))
        # However, the ASP.NET code suggests pk is the BOM Id itself, with ItemId as a confirmation.
        
        if not queryset.exists():
            raise Http404("BOM Item not found for the given criteria.")
            
        return queryset

    def get_object(self, queryset=None):
        """
        Retrieves the single BOMMaster object to be edited.
        """
        # We're overriding get_queryset, so get_object will use that filtered queryset.
        # This will raise Http404 if not found as per get_queryset().
        return super().get_object(queryset)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass WONo to the template for display, as per ASP.NET lblWONo
        context['work_order_no'] = self.request.GET.get('WONo', 'N/A')
        return context

    def get_form_kwargs(self):
        """
        Pass the BOMMaster instance to the form for initial data.
        """
        kwargs = super().get_form_kwargs()
        kwargs['instance'] = self.object # The BOMMaster instance
        return kwargs

    def form_valid(self, form):
        """
        Called when form data is valid. Delegates saving to model method.
        """
        try:
            # Get session ID (replace with actual user session ID)
            session_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous'
            
            # Use the fat model's update method
            form.save(session_id=session_id)
            messages.success(self.request, 'BOM Item updated successfully.')

            # HTMX response for success, trigger list refresh or redirect
            if self.request.headers.get('HX-Request'):
                # For HTMX, a 204 No Content response is common to indicate success
                # without page refresh, and use HX-Trigger to update parts of the page.
                # Here, we redirect to the BOM WO Tree View as in ASP.NET.
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Redirect': reverse_lazy('bom_tree_view', kwargs={'wono': self.object.work_order_no}),
                        'HX-Trigger': 'refreshBOMItemList' # Potentially useful if there was a list to refresh
                    }
                )
            return redirect(reverse_lazy('bom_tree_view', kwargs={'wono': self.object.work_order_no}))
        except Exception as e:
            messages.error(self.request, f"Error updating BOM Item: {e}")
            return self.form_invalid(form) # Re-render form with errors if any exception occurs

    def form_invalid(self, form):
        """
        Called when form data is invalid.
        """
        messages.error(self.request, 'Please correct the errors below.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, render the form again with errors
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

# Helper function for file downloads, mirroring ASP.NET's DownloadFile.aspx
def download_file_view(request, item_id, file_type):
    """
    Handles file downloads for drawing and spec sheets.
    Mimics DownloadFile.aspx functionality.
    """
    item = get_object_or_404(ItemMaster, id=item_id)
    
    file_data = None
    file_name = None
    content_type = None

    if file_type == 'drawing':
        file_data = item.drawing_filedata
        file_name = item.drawing_filename
        content_type = item.drawing_content_type
    elif file_type == 'spec_sheet':
        file_data = item.spec_sheet_filedata
        file_name = item.spec_sheet_filename
        content_type = item.spec_sheet_content_type

    if file_data:
        response = HttpResponse(file_data, content_type=content_type or 'application/octet-stream')
        response['Content-Disposition'] = f'attachment; filename="{file_name or "download"}"'
        return response
    else:
        raise Http404("File not found.")

class BOMItemDeleteView(DeleteView):
    """
    Handles deletion of a BOMMaster record and its associated ItemMaster record.
    """
    model = BOMMaster
    template_name = 'design_bom/_bom_item_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('bom_tree_view') # Will be redirected via HX-Redirect

    def get_queryset(self):
        """
        Ensure deletion is for the correct BOM item.
        """
        return super().get_queryset().select_related('item').filter(
            company_id=CURRENT_COMPANY_ID,
            financial_year_id__lte=CURRENT_FINANCIAL_YEAR_ID
        )

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to use the model's custom cascade delete logic.
        """
        self.object = self.get_object()
        self.object.delete_cascade() # Use the custom model method
        messages.success(request, 'BOM Item deleted successfully.')

        if request.headers.get('HX-Request'):
            # For HTMX, send 204 No Content and redirect client-side
            return HttpResponse(
                status=204,
                headers={
                    'HX-Redirect': reverse_lazy('bom_tree_view', kwargs={'wono': self.object.work_order_no})
                }
            )
        return super().delete(request, *args, **kwargs)

class BOMItemFileDeleteView(DeleteView):
    """
    Handles deletion of specific files (drawing or spec sheet) from ItemMaster.
    """
    model = ItemMaster
    file_type = None # 'drawing' or 'spec_sheet'
    template_name = 'design_bom/_item_file_confirm_delete.html' # Reusable partial
    success_url = reverse_lazy('bom_item_edit') # This will be set by get_success_url

    def dispatch(self, request, *args, **kwargs):
        self.file_type = self.kwargs.get('file_type') # Get file_type from URL
        if self.file_type not in ['drawing', 'spec_sheet']:
            raise Http404("Invalid file type for deletion.")
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        """
        Filter to ensure only relevant items are targeted.
        """
        return super().get_queryset().filter(
            company_id=CURRENT_COMPANY_ID,
            financial_year_id__lte=CURRENT_FINANCIAL_YEAR_ID
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['file_type_display'] = self.file_type.replace('_', ' ').title()
        return context

    def get_success_url(self):
        # Redirect back to the BOM item edit page with its original parameters
        # We need the BOMMaster ID and WONo to construct the redirect URL.
        # The ItemMaster ID (self.object.id) is known, but we need BOMMaster.
        # Since this file deletion is tightly coupled to BOMItemUpdateView, 
        # we might pass the BOMMaster.id to this view or retrieve it.
        # Assuming ItemMaster has a reverse relation to BOMMaster (bom_masters)
        bom_item = self.object.bom_masters.first() # Get the related BOMMaster
        if bom_item:
            return reverse_lazy('bom_item_edit', kwargs={'pk': bom_item.id}) + f'?WONo={bom_item.work_order_no}'
        return reverse_lazy('bom_tree_view') # Fallback if no BOMMaster found

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to use the ItemMaster's method for file clearing.
        """
        self.object = self.get_object()
        self.object.delete_file_data(self.file_type) # Use the model method
        messages.success(request, f'{self.file_type.replace("_", " ").title()} deleted successfully.')

        if request.headers.get('HX-Request'):
            # For HTMX, send 204 No Content and redirect client-side
            return HttpResponse(
                status=204,
                headers={
                    'HX-Redirect': self.get_success_url()
                }
            )
        return super().delete(request, *args, **kwargs)

# Mocked BOM_Design_WO_TreeView_Edit.aspx equivalent for redirection purposes
class BOMTreeView(UpdateView):
    """
    A placeholder view to simulate the redirection target
    'BOM_Design_WO_TreeView_Edit.aspx'.
    In a real scenario, this would be a comprehensive tree view of BOMs.
    """
    model = BOMMaster # Use BOMMaster for context, but not for direct editing here
    template_name = 'design_bom/bom_tree_view.html'
    context_object_name = 'work_order_no' # Used to pass WONo to template

    def get_object(self, queryset=None):
        # This view doesn't necessarily edit an object, but requires WONo.
        # It's an entry point to a different view.
        return None 

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The WONo would typically be passed from the previous page's redirect
        # or from a URL parameter if directly navigated to.
        context['work_order_no'] = self.kwargs.get('wono', self.request.GET.get('WONo', 'N/A'))
        return context
```

#### 4.4 Templates (`design_bom/templates/design_bom/`)

*   `bom_item_edit.html`: The main page for editing the BOM item.
*   `_bom_item_form.html`: Partial for the form to be loaded via HTMX.
*   `_bom_item_confirm_delete.html`: Partial for BOM item deletion confirmation.
*   `_item_file_confirm_delete.html`: Partial for file deletion confirmation.
*   `bom_tree_view.html`: A mock template for the redirect target.

**`design_bom/templates/design_bom/bom_item_edit.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6 bg-blue-600 text-white p-3 rounded-md">
        <h2 class="text-xl font-bold">BOM Item - Edit</h2>
        <span class="font-semibold">WO No: {{ work_order_no }}</span>
    </div>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="p-3 mb-3 {% if message.tags %}bg-{{ message.tags }}-100 text-{{ message.tags }}-700{% else %}bg-blue-100 text-blue-700{% endif %} rounded-md" role="alert">
                {{ message }}
            </div>
            {% endfor %}
        </div>
    {% endif %}

    <div id="bomItemEditFormContainer"
         hx-trigger="load, refreshBOMItemEdit from:body"
         hx-get="{% url 'bom_item_edit_partial' bom_item.pk %}?WONo={{ work_order_no }}"
         hx-swap="innerHTML">
        <!-- The form will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading BOM Item details...</p>
        </div>
    </div>

    <!-- Modal for delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed for this page as modal is handled by HTMX and _ syntax
    });
</script>
{% endblock %}
```

**`design_bom/templates/design_bom/_bom_item_form.html`** (Partial for the edit form)

```html
<div class="p-6 bg-white rounded-lg shadow-md">
    <form hx-post="{% url 'bom_item_edit' bom_item.pk %}?WONo={{ work_order_no }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Display fields -->
            <div class="mb-4 col-span-2">
                <label class="block text-sm font-medium text-gray-700">Amendment No:</label>
                <p class="mt-1 text-base text-gray-900">{{ bom_item.amendment_no }}</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Equipment No:</label>
                <p class="mt-1 text-base text-gray-900">{{ bom_item.equipment_no|default:"N/A" }}</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Unit No:</label>
                <p class="mt-1 text-base text-gray-900">{{ bom_item.unit_no|default:"N/A" }}</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Part No/SN:</label>
                <p class="mt-1 text-base text-gray-900">{{ bom_item.part_no|default:"N/A" }}</p>
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700">Item ID:</label>
                <p class="mt-1 text-base text-gray-900">{{ bom_item.item.id }}</p>
            </div>

            <!-- Editable fields -->
            <div class="mb-4 col-span-2">
                <label for="{{ form.manufacturer_description.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.manufacturer_description.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.manufacturer_description }}
                {% if form.manufacturer_description.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.manufacturer_description.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.uom_basic.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.uom_basic.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.uom_basic }}
                {% if form.uom_basic.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uom_basic.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.quantity.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.quantity }}
                {% if form.quantity.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.revision.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.revision.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.revision }}
                {% if form.revision.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.revision.errors }}</p>
                {% endif %}
            </div>
            
            <div class="mb-4">
                <label for="{{ form.drawing_file.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.drawing_file.label }}
                    {% if bom_item.item.drawing_filename %}
                        <a href="{% url 'download_file' bom_item.item.pk 'drawing' %}" class="text-blue-500 hover:underline ml-2" target="_blank">{{ bom_item.item.drawing_filename }}</a>
                        <button type="button" 
                            class="bg-red-200 hover:bg-red-300 text-red-700 font-bold py-0.5 px-1.5 rounded-full text-xs ml-2"
                            hx-get="{% url 'delete_item_file' bom_item.item.pk 'drawing' %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            X
                        </button>
                    {% endif %}
                </label>
                {{ form.drawing_file }}
                {% if form.drawing_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.drawing_file.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.spec_sheet_file.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.spec_sheet_file.label }}
                    {% if bom_item.item.spec_sheet_filename %}
                        <a href="{% url 'download_file' bom_item.item.pk 'spec_sheet' %}" class="text-blue-500 hover:underline ml-2" target="_blank">{{ bom_item.item.spec_sheet_filename }}</a>
                        <button type="button" 
                            class="bg-red-200 hover:bg-red-300 text-red-700 font-bold py-0.5 px-1.5 rounded-full text-xs ml-2"
                            hx-get="{% url 'delete_item_file' bom_item.item.pk 'spec_sheet' %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            X
                        </button>
                    {% endif %}
                </label>
                {{ form.spec_sheet_file }}
                {% if form.spec_sheet_file.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.spec_sheet_file.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-between space-x-4">
            <div>
                <button 
                    type="button" 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mr-2"
                    hx-get="{% url 'bom_item_delete' bom_item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete BOM Item
                </button>
            </div>
            <div>
                <a href="{% url 'bom_tree_view' wono=work_order_no %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded inline-block">
                    Cancel
                </a>
                <button 
                    type="submit" 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded ml-2">
                    Update
                </button>
            </div>
        </div>
    </form>
</div>
```

**`design_bom/templates/design_bom/_bom_item_confirm_delete.html`** (Partial for BOM item delete confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="mb-4">Are you sure you want to delete this BOM Item?</p>
    <p class="mb-4 font-semibold text-red-600">This will also delete the associated Item Master record!</p>
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'bom_item_delete' bom_item.pk %}" 
            hx-swap="none"
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

**`design_bom/templates/design_bom/_item_file_confirm_delete.html`** (Partial for file delete confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm File Deletion</h3>
    <p class="mb-4">Are you sure you want to delete the {{ file_type_display }} for this item?</p>
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'delete_item_file' itemmaster.pk file_type %}" 
            hx-swap="none"
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

**`design_bom/templates/design_bom/bom_tree_view.html`** (Mock redirect target)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">BOM Work Order Tree View</h2>
    <p class="text-lg">Work Order Number: <span class="font-semibold">{{ work_order_no }}</span></p>
    <p class="mt-4 text-gray-700">
        This is a placeholder for the actual BOM Work Order Tree View. 
        In a full migration, this page would display a hierarchical view of BOMs.
    </p>
    {% if messages %}
        <div class="mt-4">
            {% for message in messages %}
            <div class="p-3 mb-3 {% if message.tags %}bg-{{ message.tags }}-100 text-{{ message.tags }}-700{% else %}bg-blue-100 text-blue-700{% endif %} rounded-md" role="alert">
                {{ message }}
            </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="mt-8">
        <a href="#" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Go back to BOM List (if applicable)
        </a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`design_bom/urls.py`)

This file defines the routes for accessing the Django views.

```python
from django.urls import path
from .views import BOMItemUpdateView, BOMItemDeleteView, BOMItemFileDeleteView, download_file_view, BOMTreeView

urlpatterns = [
    # Main edit page for a specific BOM Item
    # pk here is BOMMaster.Id
    path('bom_item/<int:pk>/edit/', BOMItemUpdateView.as_view(), name='bom_item_edit'),
    # HTMX endpoint to load the form partial
    path('bom_item/<int:pk>/edit/partial/', BOMItemUpdateView.as_view(), name='bom_item_edit_partial'),
    
    # URL for downloading associated files
    path('item/<int:item_id>/download/<str:file_type>/', download_file_view, name='download_file'),

    # URL for deleting a BOM item (confirm via modal)
    path('bom_item/<int:pk>/delete/', BOMItemDeleteView.as_view(), name='bom_item_delete'),

    # URL for deleting drawing or spec sheet (confirm via modal)
    path('item/<int:pk>/delete_file/<str:file_type>/', BOMItemFileDeleteView.as_view(), name='delete_item_file'),

    # Mock URL for the BOM Tree View (redirection target)
    path('bom_tree_view/<str:wono>/', BOMTreeView.as_view(), name='bom_tree_view'),
]
```

#### 4.6 Tests (`design_bom/tests.py`)

Comprehensive tests for models (unit) and views (integration) ensuring functionality and adherence to business rules.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from django.db import IntegrityError
from decimal import Decimal
import os
from datetime import date, time

from .models import UnitMaster, ItemMaster, BOMMaster, BOMAmendment, CURRENT_COMPANY_ID, CURRENT_FINANCIAL_YEAR_ID

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.unit_nos = UnitMaster.objects.create(symbol='NOS')
        cls.unit_kg = UnitMaster.objects.create(symbol='KG')

        cls.item1 = ItemMaster.objects.create(
            manufacturer_description='Legacy Item 1',
            uom_basic=cls.unit_nos,
            company_id=CURRENT_COMPANY_ID,
            financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            session_id='testuser',
            opening_balance_date=date.today(),
            opening_balance_quantity=0
        )

        cls.bom1 = BOMMaster.objects.create(
            work_order_no='WO001',
            amendment_no=0,
            equipment_no='EQP001',
            unit_no='UNIT001',
            part_no='PART001',
            c_id=1,
            item=cls.item1,
            quantity=Decimal('10.500'),
            revision='RevA',
            parent_id=0,
            company_id=CURRENT_COMPANY_ID,
            financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            session_id='testuser'
        )

        # Item with existing files
        cls.drawing_content = b"fake drawing content"
        cls.spec_sheet_content = b"fake spec sheet content"
        cls.item_with_files = ItemMaster.objects.create(
            manufacturer_description='Item with files',
            uom_basic=cls.unit_kg,
            drawing_filename='drawing.pdf',
            drawing_filedata=cls.drawing_content,
            drawing_content_type='application/pdf',
            spec_sheet_filename='spec.docx',
            spec_sheet_filedata=cls.spec_sheet_content,
            spec_sheet_content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            spec_sheet_filesize=len(cls.spec_sheet_content),
            company_id=CURRENT_COMPANY_ID,
            financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )
        cls.bom_with_files = BOMMaster.objects.create(
            work_order_no='WO002',
            amendment_no=0,
            item=cls.item_with_files,
            quantity=Decimal('5.000'),
            revision='RevB',
            parent_id=0,
            company_id=CURRENT_COMPANY_ID,
            financial_year_id=CURRENT_FINANCIAL_YEAR_ID
        )

class UnitMasterModelTest(ModelTest):
    def test_unit_master_creation(self):
        self.assertEqual(self.unit_nos.symbol, 'NOS')
        self.assertEqual(UnitMaster.objects.count(), 2) # Including KG

    def test_unit_master_str(self):
        self.assertEqual(str(self.unit_nos), 'NOS')

    def test_meta_options(self):
        self.assertEqual(UnitMaster._meta.db_table, 'Unit_Master')
        self.assertFalse(UnitMaster._meta.managed)
        self.assertEqual(UnitMaster._meta.verbose_name, 'Unit of Measure')
        self.assertEqual(UnitMaster._meta.verbose_name_plural, 'Units of Measure')

class ItemMasterModelTest(ModelTest):
    def test_item_master_creation(self):
        self.assertEqual(self.item1.manufacturer_description, 'Legacy Item 1')
        self.assertEqual(self.item1.uom_basic, self.unit_nos)
        self.assertIsNone(self.item1.drawing_filename)
        self.assertEqual(ItemMaster.objects.count(), 2)

    def test_item_master_str(self):
        self.assertEqual(str(self.item1), 'Legacy Item 1')
        item_no_desc = ItemMaster.objects.create(uom_basic=self.unit_nos, company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID)
        self.assertEqual(str(item_no_desc), f'Item {item_no_desc.id}')

    def test_delete_file_data_drawing(self):
        item = self.item_with_files
        item.delete_file_data('drawing')
        item.refresh_from_db()
        self.assertIsNone(item.drawing_filename)
        self.assertIsNone(item.drawing_filedata)
        self.assertIsNone(item.drawing_content_type)
        self.assertIsNotNone(item.spec_sheet_filename) # Other file untouched

    def test_delete_file_data_spec_sheet(self):
        item = self.item_with_files
        item.delete_file_data('spec_sheet')
        item.refresh_from_db()
        self.assertIsNone(item.spec_sheet_filename)
        self.assertIsNone(item.spec_sheet_filedata)
        self.assertIsNone(item.spec_sheet_content_type)
        self.assertIsNone(item.spec_sheet_filesize)
        self.assertIsNotNone(item.drawing_filename) # Other file untouched

    def test_delete_file_data_invalid_type(self):
        item = self.item_with_files
        initial_filename = item.drawing_filename
        item.delete_file_data('invalid_type')
        item.refresh_from_db()
        self.assertEqual(item.drawing_filename, initial_filename) # Should not change

class BOMMasterModelTest(ModelTest):
    def test_bom_master_creation(self):
        self.assertEqual(self.bom1.work_order_no, 'WO001')
        self.assertEqual(self.bom1.item, self.item1)
        self.assertEqual(self.bom1.quantity, Decimal('10.500'))
        self.assertEqual(self.bom1.amendment_no, 0)
        self.assertEqual(BOMMaster.objects.count(), 2)

    def test_bom_master_str(self):
        self.assertEqual(str(self.bom1), 'BOM for WO: WO001, Item: Legacy Item 1')

    def test_update_item_details(self):
        bom = self.bom1
        initial_amd_no = bom.amendment_no
        initial_bom_amendments = BOMAmendment.objects.count()

        new_description = 'Updated Item Desc'
        new_uom = self.unit_kg
        new_quantity = Decimal('25.755')
        new_revision = 'RevB'
        new_drawing_file = SimpleUploadedFile("new_drawing.jpg", b"new drawing data", content_type="image/jpeg")
        new_spec_sheet_file = SimpleUploadedFile("new_spec.txt", b"new spec data", content_type="text/plain")

        bom.update_item_details(
            description=new_description,
            uom_id=new_uom.id,
            quantity=new_quantity,
            revision=new_revision,
            drawing_file=new_drawing_file,
            spec_sheet_file=new_spec_sheet_file,
            session_id='test_updater'
        )

        bom.refresh_from_db()
        bom.item.refresh_from_db()

        # Verify BOMMaster updates
        self.assertEqual(bom.quantity, new_quantity)
        self.assertEqual(bom.revision, new_revision)
        self.assertEqual(bom.amendment_no, initial_amd_no + 1)
        self.assertEqual(bom.session_id, 'test_updater')
        self.assertEqual(bom.system_date, date.today())
        self.assertIsInstance(bom.system_time, time)

        # Verify ItemMaster updates
        self.assertEqual(bom.item.manufacturer_description, new_description)
        self.assertEqual(bom.item.uom_basic, new_uom)
        self.assertEqual(bom.item.drawing_filename, new_drawing_file.name)
        self.assertEqual(bom.item.drawing_filedata, new_drawing_file.read())
        self.assertEqual(bom.item.drawing_content_type, new_drawing_file.content_type)
        self.assertEqual(bom.item.spec_sheet_filename, new_spec_sheet_file.name)
        self.assertEqual(bom.item.spec_sheet_filedata, new_spec_sheet_file.read())
        self.assertEqual(bom.item.spec_sheet_content_type, new_spec_sheet_file.content_type)
        self.assertEqual(bom.item.spec_sheet_filesize, len(new_spec_sheet_file.read()))
        self.assertEqual(bom.item.session_id, 'test_updater')
        self.assertEqual(bom.item.system_date, date.today())
        self.assertIsInstance(bom.item.system_time, time)

        # Verify BOMAmendment entry
        self.assertEqual(BOMAmendment.objects.count(), initial_bom_amendments + 1)
        amendment = BOMAmendment.objects.latest('id')
        self.assertEqual(amendment.bom_master, bom)
        self.assertEqual(amendment.description, 'Legacy Item 1') # Old description
        self.assertEqual(amendment.uom, self.unit_nos) # Old UOM
        self.assertEqual(amendment.quantity, Decimal('10.500')) # Old quantity
        self.assertEqual(amendment.amendment_no, initial_amd_no) # Old amendment number

    def test_delete_cascade(self):
        bom_count = BOMMaster.objects.count()
        item_count = ItemMaster.objects.count()
        bom = self.bom1 # Uses item1

        bom.delete_cascade()

        self.assertEqual(BOMMaster.objects.count(), bom_count - 1)
        self.assertEqual(ItemMaster.objects.count(), item_count - 1)
        self.assertFalse(BOMMaster.objects.filter(id=bom.id).exists())
        self.assertFalse(ItemMaster.objects.filter(id=self.item1.id).exists()) # item1 should be deleted

        # Verify item_with_files and bom_with_files are still there
        self.assertTrue(ItemMaster.objects.filter(id=self.item_with_files.id).exists())
        self.assertTrue(BOMMaster.objects.filter(id=self.bom_with_files.id).exists())


class BOMAmendmentModelTest(ModelTest):
    def test_bom_amendment_creation(self):
        amendment = BOMAmendment.objects.create(
            system_date=date.today(),
            system_time=time.fromisoformat('10:30:00'),
            session_id='user123',
            company_id=CURRENT_COMPANY_ID,
            financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            work_order_no='WO001',
            bom_master=self.bom1,
            parent_id=0,
            c_id=1,
            item=self.item1,
            description='Initial description',
            uom=self.unit_nos,
            amendment_no=0,
            quantity=Decimal('10.000')
        )
        self.assertEqual(amendment.description, 'Initial description')
        self.assertEqual(amendment.bom_master, self.bom1)
        self.assertEqual(amendment.quantity, Decimal('10.000'))

    def test_bom_amendment_str(self):
        amendment = BOMAmendment.objects.create(
            system_date=date.today(),
            system_time=time.fromisoformat('10:30:00'),
            session_id='user123',
            company_id=CURRENT_COMPANY_ID,
            financial_year_id=CURRENT_FINANCIAL_YEAR_ID,
            work_order_no='WO001',
            bom_master=self.bom1,
            parent_id=0,
            c_id=1,
            item=self.item1,
            description='Initial description',
            uom=self.unit_nos,
            amendment_no=0,
            quantity=Decimal('10.000')
        )
        self.assertEqual(str(amendment), f'Amendment {amendment.amendment_no} for BOM {self.bom1.id}')

class BOMItemViewsTest(ModelTest):
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.bom_item_edit_url = reverse('bom_item_edit', kwargs={'pk': self.bom1.pk}) + f'?WONo={self.bom1.work_order_no}'
        self.bom_item_edit_partial_url = reverse('bom_item_edit_partial', kwargs={'pk': self.bom1.pk}) + f'?WONo={self.bom1.work_order_no}'
        self.bom_item_delete_url = reverse('bom_item_delete', kwargs={'pk': self.bom1.pk})
        
        self.item_drawing_delete_url = reverse('delete_item_file', kwargs={'pk': self.bom_with_files.item.pk, 'file_type': 'drawing'})
        self.item_spec_sheet_delete_url = reverse('delete_item_file', kwargs={'pk': self.bom_with_files.item.pk, 'file_type': 'spec_sheet'})
        
        self.download_drawing_url = reverse('download_file', kwargs={'item_id': self.bom_with_files.item.pk, 'file_type': 'drawing'})
        self.download_spec_sheet_url = reverse('download_file', kwargs={'item_id': self.bom_with_files.item.pk, 'file_type': 'spec_sheet'})

    def test_bom_item_edit_view_get(self):
        response = self.client.get(self.bom_item_edit_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/bom_item_edit.html')
        self.assertContains(response, 'BOM Item - Edit')
        self.assertContains(response, f'WO No: {self.bom1.work_order_no}')
        self.assertContains(response, 'Loading BOM Item details...') # Initial load message

    def test_bom_item_edit_partial_view_get(self):
        # This simulates the HTMX load of the form
        response = self.client.get(self.bom_item_edit_partial_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/_bom_item_form.html')
        self.assertContains(response, 'Update') # Check for form elements
        self.assertContains(response, f'value="{self.bom1.quantity}"')
        self.assertContains(response, f'{self.bom1.item.manufacturer_description}')
        self.assertContains(response, 'name="manufacturer_description"')

    def test_bom_item_update_view_post_success(self):
        new_drawing_file = SimpleUploadedFile("test_drawing.pdf", b"pdf content", content_type="application/pdf")
        new_spec_sheet_file = SimpleUploadedFile("test_spec.txt", b"text content", content_type="text/plain")

        data = {
            'manufacturer_description': 'Updated Desc',
            'uom_basic': self.unit_kg.pk,
            'quantity': '123.456',
            'revision': 'RevC',
            'drawing_file': new_drawing_file,
            'spec_sheet_file': new_spec_sheet_file,
        }
        response = self.client.post(self.bom_item_edit_url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('bom_tree_view', kwargs={'wono': self.bom1.work_order_no}))
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBOMItemList')

        self.bom1.refresh_from_db()
        self.bom1.item.refresh_from_db()

        self.assertEqual(self.bom1.quantity, Decimal('123.456'))
        self.assertEqual(self.bom1.revision, 'RevC')
        self.assertEqual(self.bom1.amendment_no, 1) # Initial was 0, now 1
        self.assertEqual(self.bom1.item.manufacturer_description, 'Updated Desc')
        self.assertEqual(self.bom1.item.uom_basic, self.unit_kg)
        self.assertEqual(self.bom1.item.drawing_filename, 'test_drawing.pdf')
        self.assertEqual(self.bom1.item.spec_sheet_filename, 'test_spec.txt')

        # Check BOMAmendment entry
        amendment = BOMAmendment.objects.latest('id')
        self.assertEqual(amendment.bom_master, self.bom1)
        self.assertEqual(amendment.description, 'Legacy Item 1')
        self.assertEqual(amendment.quantity, Decimal('10.500'))
        self.assertEqual(amendment.amendment_no, 0) # Logged old value

    def test_bom_item_update_view_post_invalid_data(self):
        data = {
            'manufacturer_description': '', # Missing required field
            'uom_basic': self.unit_kg.pk,
            'quantity': 'invalid_qty', # Invalid quantity
            'revision': '', # Missing required field
        }
        response = self.client.post(self.bom_item_edit_url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # Should re-render form with errors
        self.assertContains(response, 'Please correct the errors below.', html=False)
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Quantity must be a number with up to 3 decimal places.')
        self.assertTemplateUsed(response, 'design_bom/_bom_item_form.html')

    def test_bom_item_delete_view_get(self):
        response = self.client.get(self.bom_item_delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/_bom_item_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Are you sure you want to delete this BOM Item?')

    def test_bom_item_delete_view_post_success(self):
        bom_count = BOMMaster.objects.count()
        item_count = ItemMaster.objects.count()

        response = self.client.post(self.bom_item_delete_url, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse('bom_tree_view', kwargs={'wono': self.bom1.work_order_no}))

        self.assertEqual(BOMMaster.objects.count(), bom_count - 1)
        self.assertEqual(ItemMaster.objects.count(), item_count - 1) # ItemMaster should also be deleted

        self.assertFalse(BOMMaster.objects.filter(pk=self.bom1.pk).exists())
        self.assertFalse(ItemMaster.objects.filter(pk=self.item1.pk).exists())

    def test_download_file_view_drawing(self):
        response = self.client.get(self.download_drawing_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], self.item_with_files.drawing_content_type)
        self.assertEqual(response['Content-Disposition'], f'attachment; filename="{self.item_with_files.drawing_filename}"')
        self.assertEqual(response.content, self.item_with_files.drawing_filedata)

    def test_download_file_view_spec_sheet(self):
        response = self.client.get(self.download_spec_sheet_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], self.item_with_files.spec_sheet_content_type)
        self.assertEqual(response['Content-Disposition'], f'attachment; filename="{self.item_with_files.spec_sheet_filename}"')
        self.assertEqual(response.content, self.item_with_files.spec_sheet_filedata)

    def test_download_file_view_not_found(self):
        # Item with no files
        item_no_files = ItemMaster.objects.create(manufacturer_description='No Files', uom_basic=self.unit_nos, company_id=CURRENT_COMPANY_ID, financial_year_id=CURRENT_FINANCIAL_YEAR_ID)
        url = reverse('download_file', kwargs={'item_id': item_no_files.pk, 'file_type': 'drawing'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_item_file_delete_view_get(self):
        response = self.client.get(self.item_drawing_delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/_item_file_confirm_delete.html')
        self.assertContains(response, 'Confirm File Deletion')
        self.assertContains(response, 'drawing file')

    def test_item_file_delete_view_post_drawing_success(self):
        item = self.bom_with_files.item
        self.assertIsNotNone(item.drawing_filename)

        response = self.client.post(self.item_drawing_delete_url, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertIn('HX-Redirect', response.headers)
        # Check if redirect URL is correct, includes wono
        expected_redirect_url = reverse('bom_item_edit', kwargs={'pk': self.bom_with_files.pk}) + f'?WONo={self.bom_with_files.work_order_no}'
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)

        item.refresh_from_db()
        self.assertIsNone(item.drawing_filename)
        self.assertIsNone(item.drawing_filedata)
        self.assertIsNone(item.drawing_content_type)
        self.assertIsNotNone(item.spec_sheet_filename) # Other file should be untouched

    def test_item_file_delete_view_post_spec_sheet_success(self):
        item = self.bom_with_files.item
        self.assertIsNotNone(item.spec_sheet_filename)

        response = self.client.post(self.item_spec_sheet_delete_url, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertIn('HX-Redirect', response.headers)
        expected_redirect_url = reverse('bom_item_edit', kwargs={'pk': self.bom_with_files.pk}) + f'?WONo={self.bom_with_files.work_order_no}'
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)

        item.refresh_from_db()
        self.assertIsNone(item.spec_sheet_filename)
        self.assertIsNone(item.spec_sheet_filedata)
        self.assertIsNone(item.spec_sheet_content_type)
        self.assertIsNone(item.spec_sheet_filesize)
        self.assertIsNotNone(item.drawing_filename) # Other file should be untouched

    def test_bom_tree_view(self):
        response = self.client.get(reverse('bom_tree_view', kwargs={'wono': 'WOXYZ'}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/bom_tree_view.html')
        self.assertContains(response, 'Work Order Number: WOXYZ')
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:** All CRUD operations (Update, Delete, File Delete) are designed to be triggered via HTMX requests. Forms are submitted with `hx-post`, and success results in a `204 No Content` response with `HX-Redirect` and `HX-Trigger` headers to manage client-side state. The main page uses `hx-get` to dynamically load the form partial, ensuring a single-page application feel.
*   **Alpine.js:** For this specific single-record edit page, Alpine.js is not strictly necessary for the modal functionality (HTMX's `_` syntax can handle showing/hiding). However, it's kept as a recommendation for future interactive UI elements within the form itself if needed.
*   **DataTables:** While the original ASP.NET page used a `GridView` for a single record, a typical Django list view would employ DataTables. Since this specific page is a single item edit, DataTables is not directly applied here, but the principle would be applied to a *list* of BOM items if one existed.

## Final Notes

This comprehensive plan provides a direct migration path for the `BOM_Design_Assembly_Edit.aspx` page to Django. The key benefits include:

*   **Improved Maintainability**: By adhering to Django's "Fat Model, Thin View" and separation of concerns, the business logic is encapsulated in models, making it easier to understand, test, and evolve.
*   **Modern User Experience**: HTMX delivers dynamic, responsive interactions without the overhead of heavy JavaScript frameworks, resulting in a snappier user interface akin to a Single Page Application.
*   **Robustness**: Extensive unit and integration tests ensure the migrated functionality is correct and resilient to future changes.
*   **Scalability**: Django's architecture is inherently more scalable than legacy ASP.NET Web Forms, providing a solid foundation for future growth.
*   **Automation-Ready**: The structured approach and clear definition of components make this migration highly amenable to AI-assisted automation, significantly reducing manual effort and accelerating the transition.

By following these steps, organizations can confidently transition their critical ERP functionalities to a modern, efficient, and future-proof Django platform.