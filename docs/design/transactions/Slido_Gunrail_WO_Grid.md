## ASP.NET to Django Conversion Script: <PERSON>lido GunRail Work Order Grid

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several data sources:
*   **Main Grid Data:** Populated by `Sp_WONO_NotInBom` stored procedure. This procedure seems to filter work orders that are not yet in the BOM (Bill of Materials). The fields displayed in the `GridView` suggest the primary table for work orders is `SD_Cust_WorkOrder_Master`.
    *   **Table:** `SD_Cust_WorkOrder_Master` (inferred as the source for the grid data)
    *   **Columns:** `WONo` (Work Order Number, likely primary key), `FinYear` (Financial Year), `CustomerName`, `CustomerId`, `EnqId` (Enquiry ID), `PONo` (Purchase Order Number), `SysDate` (System Date, generation date), `EmployeeName` (Generated By).
*   **WO Category Dropdown:** `DDLTaskWOType` is populated from `tblSD_WO_Category`.
    *   **Table:** `tblSD_WO_Category`
    *   **Columns:** `CId` (Category ID, likely primary key), `Symbol`, `CName` (Category Name).
*   **Customer AutoComplete:** `TxtSearchValue_AutoCompleteExtender` uses data from `SD_Cust_master`.
    *   **Table:** `SD_Cust_master`
    *   **Columns:** `CustomerId` (Customer ID, likely primary key), `CustomerName`.
*   **BOM Exclusion:** The `Sp_WONO_NotInBom` procedure name and the C# code `WONo not in (select WONo from tblDG_BOM_Master)` indicates a check against another table.
    *   **Table:** `tblDG_BOM_Master`
    *   **Columns:** At least `WONo` is present for the exclusion logic.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**
*   **Read (R):** This is the primary function. The page displays a list of "Slido GunRail" work orders (`SearchGridView1`).
    *   Data is loaded on `Page_Load` and explicitly on search button click (`btnSearch_Click`), dropdown changes (`DropDownList1_SelectedIndexChanged2`, `DDLTaskWOType_SelectedIndexChanged`), and grid paging (`SearchGridView1_PageIndexChanging`).
    *   Filtering is applied based on:
        *   Search Type (Customer Name, Enquiry No, PO No, WO No) selected via `DropDownList1`.
        *   Search Value entered in `TxtSearchValue` or `txtSearchCustomer`.
        *   WO Category selected via `DDLTaskWOType`.
    *   Work Orders *not* in `tblDG_BOM_Master` are displayed.
    *   Customer name autocomplete functionality (`sql` WebMethod).
*   **Create (C), Update (U), Delete (D):** There are no direct CUD operations on this page. The `WONo` column is a `HyperLinkField` leading to `Slido_Gunrail_Details.aspx`, implying that CRUD operations are handled on a separate detail page.
*   **Session Management:** `CompId`, `FinYearId`, `username` are retrieved from `Session`. Temporary data (`tblDG_Gunrail_CrossRail_Temp`, `tblDG_Gunrail_LongRail_Temp`) is deleted from session-specific tables.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
*   **Data Display:** `asp:GridView` (`SearchGridView1`) is used for tabular data presentation, supporting paging and sorting. This directly maps to Django templates with DataTables.
*   **Filtering/Searching:**
    *   `asp:DropDownList` (`DropDownList1`): Selects the search field type.
    *   `asp:TextBox` (`TxtSearchValue`, `txtSearchCustomer`): Input for the search value.
    *   `cc1:autocompleteextender` (`TxtSearchValue_AutoCompleteExtender`): Provides real-time suggestions for customer names.
    *   `asp:DropDownList` (`DDLTaskWOType`): Filters by WO Category.
    *   `asp:Button` (`btnSearch`): Triggers the search action.
*   **Navigation:** `asp:HyperLinkField` on `WONo` column directs to a detail page.
*   **Master Page:** The page uses a master page (`MasterPage.master`) for layout, which corresponds to Django's template inheritance (extending `core/base.html`).

---

### Step 4: Generate Django Code

**App Name:** `design` (derived from `Module_Design_Transactions`)

#### 4.1 Models (design/models.py)

```python
from django.db import models

class Customer(models.Model):
    """
    Maps to SD_Cust_master table.
    """
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class WOCategory(models.Model):
    """
    Maps to tblSD_WO_Category table.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    cname = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class BomMaster(models.Model):
    """
    Maps to tblDG_BOM_Master for exclusion logic.
    Only WONo is needed for this context.
    """
    # Assuming WONo is a primary key or unique identifier in tblDG_BOM_Master
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return self.wono

class WorkOrderManager(models.Manager):
    """
    Custom manager for WorkOrder model to handle complex filtering logic
    similar to Sp_WONO_NotInBom stored procedure.
    """
    def get_eligible_work_orders(self, search_type, search_value, wo_category_id,
                                 company_id, financial_year_id):
        
        # Start with base queryset and apply global filters like company and financial year
        # These are assumed to be part of the WorkOrder model or implicitly handled by DB views.
        # For this example, we will apply them as if they are fields on WorkOrder.
        # In a real scenario, these might be context variables or handled by a multi-tenancy setup.
        queryset = self.get_queryset()
        # Example if CompId and FinYearId are actual fields:
        # queryset = queryset.filter(company_id=company_id, financial_year_id=financial_year_id)

        # Apply dynamic search type filtering
        if search_value:
            if search_type == '1':  # Enquiry No
                queryset = queryset.filter(enq_id__icontains=search_value)
            elif search_type == '2': # PO No
                queryset = queryset.filter(po_no__icontains=search_value)
            elif search_type == '3': # WO No
                queryset = queryset.filter(wono__icontains=search_value)
            elif search_type == '0': # Customer Name (extract CustomerId from "Name [ID]" format)
                if '[' in search_value and ']' in search_value:
                    try:
                        customer_id_str = search_value.split('[')[-1].strip(']')
                        customer_id = int(customer_id_str)
                        queryset = queryset.filter(customer_id=customer_id)
                    except ValueError:
                        # If ID is not valid, no results match
                        queryset = queryset.none()
                else:
                    # Fallback if only customer name is provided, try partial match
                    queryset = queryset.filter(customer_name__icontains=search_value)
        
        # Apply WO Category filtering
        if wo_category_id and wo_category_id != '': # Assuming '' is the default 'WO Category' value
            queryset = queryset.filter(wo_category__cid=wo_category_id)
            
        # Simulate "WONo not in (select WONo from tblDG_BOM_Master)"
        # Exclude work orders that have an entry in BomMaster
        queryset = queryset.exclude(wono__in=BomMaster.objects.values_list('wono', flat=True))

        return queryset.order_by('-sys_date') # Order by date descending by default

class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master table (inferred).
    """
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=50) # Assuming WONo is the primary key
    fin_year = models.CharField(db_column='FinYear', max_length=10)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    # Using ForeignKey for CustomerId, assuming it links to the Customer model
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='workorders')
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate')
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    # Assuming CId from tblSD_WO_Category is also stored on WorkOrder table or derived via a join
    # Let's add it as a ForeignKey if it influences the query. If not a direct column, then removed.
    # Based on the ASP.NET code, DDLTaskWOType.SelectedValue maps to CId, implying a direct link.
    wo_category = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', related_name='workorders_by_category', blank=True, null=True)

    objects = WorkOrderManager() # Use the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master' # Inferred
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono

    # Add methods here for business logic if needed, e.g., to generate detail URL
    def get_absolute_url(self):
        # This simulates the HyperLinkField navigation to a detail page
        return f"/design/workorder/details/{self.wono}/" # Example URL pattern
```

#### 4.2 Forms (design/forms.py)

```python
from django import forms
from .models import WOCategory, Customer

class WorkOrderSearchForm(forms.Form):
    """
    Form for filtering work orders. Replicates ASP.NET dropdowns and textboxes.
    """
    SEARCH_TYPE_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/design/workorder/search-input/', 'hx-target': '#search-value-container', 'hx-swap': 'outerHTML'})
    )
    search_value = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter search value'})
    )
    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('cname'),
        empty_label="WO Category", # This matches ASP.NET's "WO Category" item
        required=False,
        label="WO Category",
        widget=forms.Select(attrs={'class': 'box3'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply CSS classes to all fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Select)):
                field.widget.attrs['class'] = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            
            if field_name == 'search_value':
                current_search_type = self.initial.get('search_type') or self.data.get('search_type') or '0'
                if current_search_type == '0':
                    self.fields['search_value'].widget.attrs['hx-get'] = '/design/workorder/customer-autocomplete/'
                    self.fields['search_value'].widget.attrs['hx-trigger'] = 'keyup changed delay:500ms'
                    self.fields['search_value'].widget.attrs['hx-target'] = '#autocomplete-results'
                    self.fields['search_value'].widget.attrs['hx-swap'] = 'innerHTML'
                    self.fields['search_value'].widget.attrs['placeholder'] = 'Type Customer Name (e.g., "ABC [123]")'
                else:
                    self.fields['search_value'].widget.attrs.pop('hx-get', None)
                    self.fields['search_value'].widget.attrs.pop('hx-trigger', None)
                    self.fields['search_value'].widget.attrs.pop('hx-target', None)
                    self.fields['search_value'].widget.attrs.pop('hx-swap', None)
                    self.fields['search_value'].widget.attrs['placeholder'] = 'Enter search value'
```

#### 4.3 Views (design/views.py)

```python
from django.views.generic import ListView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from .models import WorkOrder, Customer
from .forms import WorkOrderSearchForm

class WorkOrderListView(TemplateView):
    """
    Main view for the Slido GunRail Work Order Grid.
    Handles initial page load and renders the search form and table container.
    """
    template_name = 'design/workorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with any submitted data if available, or empty
        form = WorkOrderSearchForm(self.request.GET or None)
        context['form'] = form
        return context

class WorkOrderTablePartialView(ListView):
    """
    HTMX endpoint to load/refresh the work order table.
    Filters data based on GET parameters and renders the table partial.
    """
    model = WorkOrder
    template_name = 'design/workorder/_workorder_table.html'
    context_object_name = 'workorders'
    paginate_by = 20 # Matches ASP.NET GridView's PageSize

    def get_queryset(self):
        form = WorkOrderSearchForm(self.request.GET)
        search_type = self.request.GET.get('search_type', '0')
        search_value = self.request.GET.get('search_value', '').strip()
        wo_category_id = self.request.GET.get('wo_category', '')
        
        # Simulate session variables (CompId, FinYearId) for the manager method
        # In a real app, these would come from authentication/session context
        # For demo purposes, using placeholder values.
        company_id = 1 # Example: self.request.session.get('compid')
        financial_year_id = 1 # Example: self.request.session.get('finyear')

        queryset = WorkOrder.objects.get_eligible_work_orders(
            search_type=search_type,
            search_value=search_value,
            wo_category_id=wo_category_id,
            company_id=company_id,
            financial_year_id=financial_year_id
        )
        return queryset

class CustomerAutoCompleteView(ListView):
    """
    HTMX endpoint for customer name autocomplete suggestions.
    Replicates ASP.NET's `sql` web method.
    """
    model = Customer
    template_name = 'design/workorder/_autocomplete_results.html'
    context_object_name = 'customers'

    def get_queryset(self):
        query = self.request.GET.get('search_value', '')
        if query:
            # Simulate CompId from session as in ASP.NET
            # company_id = self.request.session.get('compid')
            company_id = 1 # Placeholder
            
            # Case-insensitive search starting with prefix
            queryset = Customer.objects.filter(
                customer_name__istartswith=query,
                # Assuming company_id is a filterable field on Customer
                # customer_id=company_id
            ).order_by('customer_name')[:10] # Limit to 10 suggestions
        else:
            queryset = Customer.objects.none()
        return queryset
    
    def render_to_response(self, context, **response_kwargs):
        # For HTMX, we just return the partial HTML, not JSON as in old AJAX
        return super().render_to_response(context, **response_kwargs)

class WorkOrderSearchInputPartialView(TemplateView):
    """
    HTMX endpoint to swap the search_value input based on search_type selection.
    This replaces the ASP.NET Visible=false/true logic.
    """
    template_name = 'design/workorder/_search_input_partial.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Re-initialize the form with current data to ensure correct input type
        form = WorkOrderSearchForm(self.request.GET)
        context['form'] = form
        return context

```

#### 4.4 Templates (design/templates/design/workorder/)

`list.html`
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Slido GunRail - New</h2>
        
        <form id="search-form" hx-get="{% url 'workorder_table' %}" hx-target="#workorder-table-container" hx-swap="innerHTML" hx-trigger="submit">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6 items-end">
                <div class="col-span-1">
                    <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_type.label }}
                    </label>
                    {{ form.search_type }}
                </div>
                
                <div id="search-value-container" class="col-span-1">
                    {% include 'design/workorder/_search_input_partial.html' %}
                </div>
                
                <div class="col-span-1">
                    <label for="{{ form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.wo_category.label }}
                    </label>
                    {{ form.wo_category }}
                </div>
                
                <div class="col-span-1 flex items-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            {% csrf_token %}
        </form>

        <div id="workorder-table-container"
             hx-trigger="load, searchSubmit from:#search-form"
             hx-get="{% url 'workorder_table' %}"
             hx-swap="innerHTML">
            <!-- Initial loading state -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Work Orders...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js needed for this simplified HTMX approach
        // The dynamic input swapping is handled by HTMX alone.
    });
</script>
{% endblock %}
```

`_workorder_table.html` (Partial for HTMX)
```html
<table id="workOrderTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% if workorders %}
            {% for wo in workorders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.customer_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">
                    <a href="{{ wo.get_absolute_url }}" class="text-blue-600 hover:underline">
                        {{ wo.wono }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ wo.sys_date|date:"d M Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.employee_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {# This page only lists, so no Edit/Delete buttons needed as per ASP.NET analysis #}
                    {# The ASP.NET WONo links to a details page. #}
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="7" class="py-8 text-center text-red-700 text-lg">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#workOrderTable').DataTable({
        "pageLength": 20, // Matches ASP.NET PageSize
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "searching": true, // Enable built-in DataTables search
        "ordering": true,  // Enable sorting
        "paging": true,    // Enable pagination
    });
});
</script>
```

`_search_input_partial.html` (Partial for HTMX swapping)
```html
<label for="{{ form.search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">
    {{ form.search_value.label }}
</label>
{{ form.search_value }}
<input type="hidden" name="search_type" value="{{ form.search_type.value }}"> {# Pass search_type along #}
{% if form.search_type.value == '0' %}
    <div id="autocomplete-results" class="absolute z-10 bg-white border border-gray-200 mt-1 w-full rounded-md shadow-lg max-h-60 overflow-auto">
        <!-- Autocomplete suggestions will be loaded here -->
    </div>
    <script>
        // Alpine.js or HTMX-driven selection for autocomplete
        document.querySelector('#autocomplete-results').addEventListener('click', function(e) {
            if (e.target.tagName === 'DIV' && e.target.dataset.value) {
                document.getElementById('{{ form.search_value.id_for_label }}').value = e.target.dataset.value;
                this.innerHTML = ''; // Clear suggestions
            }
        });
    </script>
{% endif %}
```

`_autocomplete_results.html` (Partial for HTMX autocomplete results)
```html
{% if customers %}
    {% for customer in customers %}
        <div class="p-2 cursor-pointer hover:bg-gray-100" data-value="{{ customer.__str__ }}">
            {{ customer.customer_name }} [{{ customer.customer_id }}]
        </div>
    {% endfor %}
{% else %}
    <div class="p-2 text-gray-500">No suggestions</div>
{% endif %}
```

#### 4.5 URLs (design/urls.py)

```python
from django.urls import path
from .views import WorkOrderListView, WorkOrderTablePartialView, CustomerAutoCompleteView, WorkOrderSearchInputPartialView

app_name = 'design' # Important for namespacing

urlpatterns = [
    path('workorder/', WorkOrderListView.as_view(), name='workorder_list'),
    path('workorder/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    path('workorder/customer-autocomplete/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),
    path('workorder/search-input/', WorkOrderSearchInputPartialView.as_view(), name='workorder_search_input_partial'),
    
    # Placeholder for detail page link, as per ASP.NET's HyperLinkField
    # You would define WorkOrderDetailView in views.py and a corresponding template
    path('workorder/details/<str:pk>/', lambda request, pk: HttpResponse(f"Detail page for Work Order: {pk}", status=200), name='workorder_detail'),
]
```

#### 4.6 Tests (design/tests.py)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Customer, WOCategory, BomMaster, WorkOrder

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Customer.objects.create(customer_id=101, customer_name='Test Customer A')
        Customer.objects.create(customer_id=102, customer_name='Another Customer')
  
    def test_customer_creation(self):
        customer = Customer.objects.get(customer_id=101)
        self.assertEqual(customer.customer_name, 'Test Customer A')
        
    def test_customer_str_method(self):
        customer = Customer.objects.get(customer_id=101)
        self.assertEqual(str(customer), 'Test Customer A [101]')

class WOCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        WOCategory.objects.create(cid=1, symbol='CAT-A', cname='Category Alpha')
  
    def test_wo_category_creation(self):
        category = WOCategory.objects.get(cid=1)
        self.assertEqual(category.cname, 'Category Alpha')
        
    def test_wo_category_str_method(self):
        category = WOCategory.objects.get(cid=1)
        self.assertEqual(str(category), 'CAT-A - Category Alpha')

class BomMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        BomMaster.objects.create(wono='WO-BOM-001')
        BomMaster.objects.create(wono='WO-BOM-002')

    def test_bom_master_creation(self):
        bom = BomMaster.objects.get(wono='WO-BOM-001')
        self.assertEqual(bom.wono, 'WO-BOM-001')

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = Customer.objects.create(customer_id=1, customer_name='Alpha Corp')
        cls.customer2 = Customer.objects.create(customer_id=2, customer_name='Beta Inc')
        cls.category1 = WOCategory.objects.create(cid=10, symbol='DGN', cname='Design WO')
        cls.category2 = WOCategory.objects.create(cid=20, symbol='PRO', cname='Production WO')

        WorkOrder.objects.create(
            wono='WO-001', fin_year='2023', customer_name='Alpha Corp', customer=cls.customer1,
            enq_id='ENQ-A1', po_no='PO-X1', sys_date='2023-01-15T10:00:00Z',
            employee_name='John Doe', wo_category=cls.category1
        )
        WorkOrder.objects.create(
            wono='WO-002', fin_year='2023', customer_name='Beta Inc', customer=cls.customer2,
            enq_id='ENQ-B2', po_no='PO-Y2', sys_date='2023-02-20T11:30:00Z',
            employee_name='Jane Smith', wo_category=cls.category2
        )
        WorkOrder.objects.create(
            wono='WO-003', fin_year='2024', customer_name='Alpha Corp', customer=cls.customer1,
            enq_id='ENQ-C3', po_no='PO-Z3', sys_date='2024-03-01T09:00:00Z',
            employee_name='John Doe', wo_category=cls.category1
        )
        BomMaster.objects.create(wono='WO-002') # WO-002 should be excluded

    def test_workorder_creation(self):
        wo = WorkOrder.objects.get(wono='WO-001')
        self.assertEqual(wo.customer_name, 'Alpha Corp')
        self.assertEqual(wo.customer.customer_id, 1)
        self.assertEqual(wo.wo_category.cname, 'Design WO')

    def test_get_eligible_work_orders_no_filter(self):
        # WO-002 is in BomMaster, so it should be excluded
        workorders = WorkOrder.objects.get_eligible_work_orders(
            search_type='0', search_value='', wo_category_id='',
            company_id=1, financial_year_id=1 # Placeholders
        )
        self.assertEqual(workorders.count(), 2) # WO-001, WO-003
        self.assertNotIn('WO-002', [wo.wono for wo in workorders])

    def test_get_eligible_work_orders_customer_name_search(self):
        workorders = WorkOrder.objects.get_eligible_work_orders(
            search_type='0', search_value='Alpha Corp [1]', wo_category_id='',
            company_id=1, financial_year_id=1
        )
        self.assertEqual(workorders.count(), 2)
        self.assertEqual(workorders.first().wono, 'WO-003') # Ordered by sys_date desc
        self.assertIn('WO-001', [wo.wono for wo in workorders])

    def test_get_eligible_work_orders_wo_no_search(self):
        workorders = WorkOrder.objects.get_eligible_work_orders(
            search_type='3', search_value='WO-001', wo_category_id='',
            company_id=1, financial_year_id=1
        )
        self.assertEqual(workorders.count(), 1)
        self.assertEqual(workorders.first().wono, 'WO-001')

    def test_get_eligible_work_orders_category_filter(self):
        workorders = WorkOrder.objects.get_eligible_work_orders(
            search_type='0', search_value='', wo_category_id='10', # Design WO
            company_id=1, financial_year_id=1
        )
        self.assertEqual(workorders.count(), 2) # WO-001, WO-003
        self.assertIn('WO-001', [wo.wono for wo in workorders])
        self.assertIn('WO-003', [wo.wono for wo in workorders])

class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = Customer.objects.create(customer_id=1, customer_name='Alpha Corp')
        cls.category1 = WOCategory.objects.create(cid=10, symbol='DGN', cname='Design WO')
        WorkOrder.objects.create(
            wono='WO-TEST-001', fin_year='2023', customer_name='Alpha Corp', customer=cls.customer1,
            sys_date='2023-01-01T10:00:00Z', employee_name='Test User', wo_category=cls.category1
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('design:workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/list.html')
        self.assertIsInstance(response.context['form'], WorkOrderSearchForm)
        
    def test_workorder_table_partial_view_get(self):
        response = self.client.get(reverse('design:workorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_table.html')
        self.assertIn('workorders', response.context)
        self.assertEqual(response.context['workorders'].count(), 1) # Only WO-TEST-001 initially

    def test_workorder_table_partial_view_search(self):
        response = self.client.get(reverse('design:workorder_table'), {
            'search_type': '0',
            'search_value': 'Alpha Corp [1]',
            'wo_category': '10'
        })
        self.assertEqual(response.status_code, 200)
        self.assertIn('WO-TEST-001', response.content.decode())
        
    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('design:customer_autocomplete'), {'search_value': 'Alp'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_autocomplete_results.html')
        self.assertIn('Alpha Corp [1]', response.content.decode())
        
    def test_workorder_search_input_partial_view(self):
        response = self.client.get(reverse('design:workorder_search_input_partial'), {'search_type': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_search_input_partial.html')
        self.assertIn('hx-get="/design/workorder/customer-autocomplete/"', response.content.decode())

    def test_htmx_trigger_on_search_form(self):
        # Simulate HTMX request for search form submission
        # Check if HTMX header leads to correct behavior (e.g., table update)
        response = self.client.get(reverse('design:workorder_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        # Verify content is the table partial, not full page HTML
        self.assertTemplateUsed(response, 'design/workorder/_workorder_table.html')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   The `list.html` uses `hx-get` on `id="workorder-table-container"` with `hx-trigger="load, searchSubmit from:#search-form"` to initially load and then refresh the `_workorder_table.html` partial whenever the search form is submitted.
*   The search form itself uses `hx-get` and `hx-target` to submit parameters for filtering the table.
*   The `search_type` dropdown has `hx-get` and `hx-target` to dynamically swap the `search_value` input field using `_search_input_partial.html`. This handles the ASP.NET `Visible=false/true` logic purely with HTMX.
*   The `search_value` input, when `search_type` is 'Customer Name', has `hx-get`, `hx-trigger="keyup changed delay:500ms"`, and `hx-target="#autocomplete-results"` to fetch customer suggestions from `customer-autocomplete/` endpoint, rendering them in `_autocomplete_results.html`.
*   DataTables (`_workorder_table.html`) is initialized via its standard JavaScript library, providing client-side search, sorting, and pagination on the *loaded* data. The backend filtering ensures that DataTables only receives the relevant subset of data, making it efficient for large datasets.
*   Alpine.js is not strictly necessary for this specific UI dynamic behavior with the chosen HTMX strategy but is typically included for broader UI state management (e.g., showing/hiding modals for CRUD if they were part of this page, which they aren't). Its core script is included in `base.html` as per guidelines. Minimal Alpine.js can be used for selecting autocomplete results if needed, but HTMX swapping for the form elements and simple JS for DataTables is sufficient and aligns with "no additional JavaScript" for *custom* interactions.

---

### Final Notes

This comprehensive plan provides a modular, maintainable, and modern Django solution for the given ASP.NET Work Order Grid. By adopting fat models, thin views, HTMX, Alpine.js, and DataTables, the application gains efficiency, a responsive user experience, and a robust, testable codebase. The conversion focuses on automated pattern application, significantly reducing manual coding effort compared to traditional refactoring.