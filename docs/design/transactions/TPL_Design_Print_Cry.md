## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET page is primarily a report viewer displaying hierarchical data. The core data tables identified are `tblDG_TPL_Master`, `tblDG_Item_Master`, and `Unit_Master`. Additionally, `Company` and `FinancialYear` tables are inferred from session variables and helper functions.

-   **`tblDG_TPL_Master`**: This table stores the hierarchical Bill of Materials (BOM) or TPL (Third-Party Logistics) structure.
    *   Columns: `Id` (Primary Key, assumed), `CId` (Child ID, identifies the component in this TPL record), `WONo` (Work Order Number), `PId` (Parent ID, links to `CId` of parent component), `FinYearId` (Foreign Key to Financial Year), `CompId` (Foreign Key to Company), `SysDate` (System Date), `Qty` (Quantity of the component in its parent), `ItemId` (Foreign Key to `tblDG_Item_Master`).
-   **`tblDG_Item_Master`**: This table stores details about items/parts.
    *   Columns: `Id` (Primary Key), `ItemCode`, `PartNo`, `ManfDesc` (Manufacturer Description), `UOMBasic` (Foreign Key to `Unit_Master` for Unit of Measure), `CId` (Item Category ID, distinct from `TPL_Master`'s `CId`).
-   **`Unit_Master`**: This table stores definitions for units of measure.
    *   Columns: `Id` (Primary Key), `Symbol` (Unit Symbol).
-   **`Company_Master` (Assumed)**: Stores company information.
    *   Columns: `Id` (Primary Key), `Name`, `Address`.
-   **`Financial_Year_Master` (Assumed)**: Stores financial year information.
    *   Columns: `Id` (Primary Key), `YearName`.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code (`TPL_Design_Print_Cry.aspx`) is a **reporting page** with the primary function of **reading and displaying structured data** (a Bill of Materials or similar TPL print). It involves:
-   **Read/Retrieve:** Data is fetched from `tblDG_TPL_Master`, `tblDG_Item_Master`, and `Unit_Master` based on `WONo`, parent/child IDs (`PId`, `CId`), company, financial year, and a date range.
-   **Complex Data Processing:** The `getPrintnode` function recursively traverses the hierarchical data (using `PId` and `CId`) and the `RecurQty` function calculates a cumulative quantity for each component, simulating a Bill of Materials explosion. This processed data is then bound to a Crystal Report viewer.
-   **No explicit Create, Update, or Delete operations** are present on this specific ASP.NET page. However, to fulfill the comprehensive migration plan requirements for general data management, we will provide Django CRUD capabilities for the underlying `TPLMaster` model, in addition to the report viewer itself.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

-   **`CrystalReportViewer1`**: This control displays the generated report. In Django, this will be replaced by a dynamic HTML table rendered using **DataTables.js** for client-side functionality (searching, sorting, pagination).
-   **`asp:Panel`**: A container for the report viewer, often used for scrolling. In Django, standard HTML `div` elements with Tailwind CSS for layout and scrolling will be used.
-   **`asp:Button` (Cancel)**: This button redirects the user to another page (`TPL_Design_Print_Tree.aspx`). In Django, this will be a standard HTML `<a>` tag or a button triggering an HTMX redirect to a relevant Django URL.
-   **Query String Parameters**: The page relies heavily on `wono`, `SD` (StartDate), `TD` (UpToDate), `PId`, and `CId` from the URL. These will be handled as GET parameters in Django views.
-   **Master Page Integration**: The ASP.NET page uses `MasterPage.master`. In Django, this concept is replicated via **template inheritance**, where content blocks extend a `core/base.html` file.

## Step 4: Generate Django Code

We will create a Django application named `design_reports` for this migration.

### 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

Models are defined with `managed = False` and `db_table` to map to existing database tables. Foreign keys and appropriate field types are used. The complex `fun.RecurQty` and `getPrintnode` logic is abstracted into a class method within the `TPLMaster` model, focusing on the "fat model" principle.

```python
# design_reports/models.py
from django.db import models
from django.utils import timezone
from datetime import datetime, date

# Placeholder models for external dependencies as inferred
class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Company_Master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class FinancialYear(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    year_name = models.CharField(db_column='YearName', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Financial_Year_Master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name or f"Fin Year {self.id}"

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic')
    category_id = models.IntegerField(db_column='CId', blank=True, null=True) # Item Category ID

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.part_no or f"Item {self.id}"

class TPLMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    c_id = models.IntegerField(db_column='CId') # Child ID in hierarchy
    won_o = models.CharField(db_column='WONo', max_length=50) # Work Order No
    p_id = models.IntegerField(db_column='PId', blank=True, null=True) # Parent ID in hierarchy (points to TPLMaster.CId)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    sys_date = models.DateTimeField(db_column='SysDate')
    qty = models.FloatField(db_column='Qty')
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Record'
        verbose_name_plural = 'TPL Records'

    def __str__(self):
        return f"{self.won_o} - {self.item.item_code if self.item else 'N/A'} (CId: {self.c_id})"

    @classmethod
    def get_project_title(cls, wono):
        """
        Simulates fun.getProjectTitle from ASP.NET.
        In a real ERP, this might come from a dedicated ProjectMaster table.
        For demonstration, a placeholder.
        """
        return f"Project for Work Order: {wono}"

    @classmethod
    def _calculate_effective_quantity_from_top(cls, wono_val, component_c_id, comp_id_val, fin_id_val):
        """
        Recursively calculates the effective quantity of a component within a BOM structure.
        This attempts to replicate the `fun.RecurQty` logic by multiplying quantities
        up the hierarchy to get a cumulative quantity for the component.
        """
        effective_qty = 1.0
        current_node_c_id = component_c_id
        
        # Traverse up the hierarchy until a root (PId=0 or None) is found
        while current_node_c_id is not None:
            node = cls.objects.filter(
                won_o=wono_val,
                c_id=current_node_c_id,
                company_id=comp_id_val,
                fin_year_id__lte=fin_id_val
            ).first()

            if not node:
                break # Component not found in the hierarchy path
            
            effective_qty *= node.qty
            current_node_c_id = node.p_id if node.p_id != 0 else None # Move to parent's CId (PId of current node)
        
        return effective_qty

    @classmethod
    def get_report_data(cls, wono, comp_id, fin_year_id, start_date_str=None, up_to_date_str=None, p_id_query=0, c_id_query=0):
        """
        Simulates the logic of Page_Init and getPrintnode from ASP.NET to generate report data.
        Returns a list of dictionaries, each representing a row in the final report table.
        """
        try:
            start_date = datetime.strptime(start_date_str, '%d/%m/%Y').date() if start_date_str else None
            up_to_date = datetime.strptime(up_to_date_str, '%d/%m/%Y').date() if up_to_date_str else None
        except ValueError:
            start_date = None
            up_to_date = None

        report_rows = []
        visited_nodes_for_traversal = set() # To prevent infinite loops in cycles and redundant traversal
        
        def _get_print_node_recursive(current_c_id):
            if current_c_id in visited_nodes_for_traversal:
                return # Already processed this node's children to prevent cycles

            visited_nodes_for_traversal.add(current_c_id)

            # Fetch the current node details
            current_node_query = cls.objects.filter(
                c_id=current_c_id,
                won_o=wono,
                company_id=comp_id,
                fin_year_id__lte=fin_year_id
            ).select_related('item__uom_basic').first()

            if current_node_query and (start_date is None or up_to_date is None or \
                                       (start_date <= current_node_query.sys_date.date() <= up_to_date)):
                item_code_val = current_node_query.item.item_code if current_node_query.item.item_code else current_node_query.item.part_no
                row = {
                    "ItemCode": item_code_val,
                    "ManfDesc": current_node_query.item.manf_desc,
                    "UOM": current_node_query.item.uom_basic.symbol,
                    "Qty": current_node_query.qty,
                    "TPLQty": cls._calculate_effective_quantity_from_top(wono, current_node_query.c_id, comp_id, fin_year_id),
                    "WONo": wono,
                    "CompId": comp_id,
                    "AC": "A", # "A" for component (top-level or first encountered in traversal)
                    "StartDate": current_node_query.sys_date.strftime('%d/%m/%Y')
                }
                report_rows.append(row)

            # Fetch children of the current node
            children_query = cls.objects.filter(
                p_id=current_c_id,
                won_o=wono,
                company_id=comp_id,
                fin_year_id__lte=fin_year_id
            ).select_related('item__uom_basic').order_by('id') # Consistent ordering

            if start_date and up_to_date:
                children_query = children_query.filter(sys_date__date__range=(start_date, up_to_date))

            for child_node in children_query:
                # Add child node to report_rows
                item_code_val = child_node.item.item_code if child_node.item.item_code else child_node.item.part_no
                row = {
                    "ItemCode": item_code_val,
                    "ManfDesc": child_node.item.manf_desc,
                    "UOM": child_node.item.uom_basic.symbol,
                    "Qty": child_node.qty,
                    "TPLQty": cls._calculate_effective_quantity_from_top(wono, child_node.c_id, comp_id, fin_year_id),
                    "WONo": wono,
                    "CompId": comp_id,
                    "AC": "C", # "C" for child component
                    "StartDate": child_node.sys_date.strftime('%d/%m/%Y')
                }
                report_rows.append(row)
                
                # Recursively call for the child's children
                _get_print_node_recursive(child_node.c_id)


        # Determine initial root nodes for traversal
        if c_id_query and p_id_query is not None:
            # If specific PId and CId are provided, start traversal from this specific component
            initial_nodes_filter = {
                'won_o': wono,
                'p_id': p_id_query,
                'c_id': c_id_query,
                'company_id': comp_id,
                'fin_year_id__lte': fin_year_id
            }
        else:
            # Otherwise, start with top-level components (PId = 0 or None)
            initial_nodes_filter = {
                'won_o': wono,
                'p_id': 0, # Assuming 0 indicates top-level
                'company_id': comp_id,
                'fin_year_id__lte': fin_year_id
            }
        
        initial_nodes_query = cls.objects.filter(**initial_nodes_filter)
        
        if start_date and up_to_date:
            initial_nodes_query = initial_nodes_query.filter(sys_date__date__range=(start_date, up_to_date))

        for initial_node in initial_nodes_query:
            _get_print_node_recursive(initial_node.c_id)

        # Deduplicate rows by a combination of key fields if recursive traversal adds duplicates
        # This can happen if a single component appears in multiple paths in the report structure,
        # and the ASP.NET code implicitly handled this or it wasn't an issue.
        unique_report_rows = []
        seen_rows_keys = set() # Using a tuple of key fields for uniqueness

        for row in report_rows:
            # Define a unique key for each row based on essential fields
            row_key = (
                row.get('ItemCode'),
                row.get('WONo'),
                row.get('Qty'), # Qty could be part of distinguishing different line items
                row.get('AC'), # 'A' or 'C' distinguishes how it was added
                row.get('StartDate')
            )
            if row_key not in seen_rows_keys:
                unique_report_rows.append(row)
                seen_rows_keys.add(row_key)

        return unique_report_rows

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` for `TPLMaster` is created, including fields for user input. Widgets are defined with Tailwind CSS classes for consistent styling. Foreign key fields are represented by `ModelChoiceField` for dropdowns.

```python
# design_reports/forms.py
from django import forms
from .models import TPLMaster, ItemMaster, UnitMaster, Company, FinancialYear

class TPLMasterForm(forms.ModelForm):
    # Use ModelChoiceField for foreign key relationships to render as dropdowns
    item = forms.ModelChoiceField(queryset=ItemMaster.objects.all().order_by('item_code'), 
                                  widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}))
    fin_year = forms.ModelChoiceField(queryset=FinancialYear.objects.all().order_by('year_name'), 
                                      widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}))
    company = forms.ModelChoiceField(queryset=Company.objects.all().order_by('name'), 
                                     widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}))

    class Meta:
        model = TPLMaster
        fields = ['c_id', 'won_o', 'p_id', 'fin_year', 'company', 'sys_date', 'qty', 'item']
        widgets = {
            'c_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Child ID'}),
            'won_o': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Work Order No'}),
            'p_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Parent ID (0 for root)'}),
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01', 'placeholder': 'Quantity'}),
        }
```

### 4.3 Views

Task: Implement the report view and general CRUD operations using CBVs.

## Instructions:

A `TemplateView` (`TPLReportView`) is used for the main report page, handling query parameters and passing context. A separate `View` (`TPLReportTableView`) is used for the HTMX-loaded DataTables partial. For the generic CRUD, standard Django `ListView`, `CreateView`, `UpdateView`, and `DeleteView` are used for `TPLMaster`. Views are kept thin by delegating complex logic to models and using HTMX for partial updates and `HttpResponse(status=204)` for successful form submissions. Form errors are handled by returning `status=200` to re-render the form within the modal.

```python
# design_reports/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.template.loader import render_to_string
from .models import TPLMaster, Company, FinancialYear
from .forms import TPLMasterForm
import json
from datetime import datetime

# --- TPL Print Report Views (Direct migration of ASP.NET Crystal Report Viewer) ---

class TPLReportView(TemplateView):
    """
    Main view for the TPL Print Report, mirroring the ASP.NET .aspx page.
    It prepares the context for the report page but delegates the table rendering
    to a separate HTMX-loaded partial view.
    """
    template_name = 'design_reports/tpl_print/report_view.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Simulating Session variables for CompId and FinYearId
        # In a real application, these would come from the authenticated user's session
        current_comp_id = self.request.session.get('compid', 1) 
        current_fin_year_id = self.request.session.get('finyear', 1)
        
        # Extract parameters from query string (from the original ASP.NET page's behavior)
        wono = self.request.GET.get('wono', '')
        start_date_str = self.request.GET.get('SD', '')
        up_to_date_str = self.request.GET.get('TD', '')
        p_id = self.request.GET.get('PId')
        c_id = self.request.GET.get('CId')

        # Convert PId/CId to int, handling empty/invalid cases
        try:
            p_id = int(p_id) if p_id else None
        except ValueError:
            p_id = None
        try:
            c_id = int(c_id) if c_id else None
        except ValueError:
            c_id = None

        context['wono'] = wono
        context['start_date'] = start_date_str
        context['up_to_date'] = up_to_date_str
        
        # Fetch company name and address using assumed Company model
        company_obj = Company.objects.filter(id=current_comp_id).first()
        context['company_name'] = company_obj.name if company_obj else "Default Company"
        context['company_address'] = company_obj.address if company_obj else "Default Address"
        
        # Get project title using TPLMaster model method
        context['project_title'] = TPLMaster.get_project_title(wono) if wono else "TPL Print Report"

        # Pass PId and CId to template for HTMX query string reconstruction
        context['p_id'] = p_id
        context['c_id'] = c_id

        return context

class TPLReportTableView(View):
    """
    Returns the partial HTML for the DataTables report.
    This view is called via HTMX and generates the actual table content.
    """
    def get(self, request, *args, **kwargs):
        current_comp_id = request.session.get('compid', 1)
        current_fin_year_id = request.session.get('finyear', 1)
        
        wono = request.GET.get('wono', '')
        start_date_str = request.GET.get('SD')
        up_to_date_str = request.GET.get('TD')
        p_id_query = request.GET.get('PId')
        c_id_query = request.GET.get('CId')

        try:
            p_id_query = int(p_id_query) if p_id_query else None
        except ValueError:
            p_id_query = None
        try:
            c_id_query = int(c_id_query) if c_id_query else None
        except ValueError:
            c_id_query = None
        
        # Fetch report data using the model method (fat model principle)
        report_data = TPLMaster.get_report_data(
            wono=wono,
            comp_id=current_comp_id,
            fin_year_id=current_fin_year_id,
            start_date_str=start_date_str,
            up_to_date_str=up_to_date_str,
            p_id_query=p_id_query,
            c_id_query=c_id_query
        )

        context = {
            'report_rows': report_data,
        }
        return HttpResponse(render_to_string('design_reports/tpl_print/_report_table.html', context, request))

# --- CRUD Views for TPLMaster (As requested by the prompt's template) ---

class TPLMasterListView(ListView):
    """
    Displays a list of TPLMaster records for general management.
    The actual table content is loaded via HTMX.
    """
    model = TPLMaster
    template_name = 'design_reports/tplmaster/list.html'
    context_object_name = 'tpl_records'

    def get_queryset(self):
        # Initial queryset is empty as HTMX will load the actual table data.
        return TPLMaster.objects.none() 

class TPLMasterTablePartialView(ListView):
    """
    Returns the partial HTML for the TPLMaster DataTables table.
    This view is specifically for HTMX calls to refresh the table.
    """
    model = TPLMaster
    template_name = 'design_reports/tplmaster/_tplmaster_table.html'
    context_object_name = 'tpl_records'
    
    def get_queryset(self):
        # Prefetch related data for efficient display in the table
        return TPLMaster.objects.select_related('item__uom_basic', 'company', 'fin_year').order_by('id')

class TPLMasterCreateView(CreateView):
    model = TPLMaster
    form_class = TPLMasterForm
    template_name = 'design_reports/tplmaster/form.html'
    success_url = reverse_lazy('tplmaster_list') # Redirects to main list view after success

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'TPL Record added successfully.')
        if self.request.headers.get('HX-Request'):
            # Trigger HTMX events to refresh the list and close the modal
            return HttpResponse(
                status=204, # No Content to indicate successful HTMX form submission
                headers={
                    'HX-Trigger': json.dumps({'refreshTPLMasterList': None, 'closeModal': None})
                }
            )
        return response
        
    def form_invalid(self, form):
        # If form is invalid, re-render the form content within the modal via HTMX
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, self.get_context_data(form=form), self.request),
                status=200 # Return 200 so HTMX replaces content
            )
        return super().form_invalid(form) # For non-HTMX requests

class TPLMasterUpdateView(UpdateView):
    model = TPLMaster
    form_class = TPLMasterForm
    template_name = 'design_reports/tplmaster/form.html'
    success_url = reverse_lazy('tplmaster_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'TPL Record updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshTPLMasterList': None, 'closeModal': None})
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, self.get_context_data(form=form), self.request),
                status=200
            )
        return super().form_invalid(form)

class TPLMasterDeleteView(DeleteView):
    model = TPLMaster
    template_name = 'design_reports/tplmaster/confirm_delete.html'
    success_url = reverse_lazy('tplmaster_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'TPL Record deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshTPLMasterList': None, 'closeModal': None})
                }
            )
        return response

# Mock cancel endpoint - original ASP.NET redirects to another ASPX page
# In Django, this would redirect to a relevant listing page or a previous page.
# This assumes TPL_Design_Print_Tree.aspx maps to 'tplmaster_list' or similar.
class TPLPrintCancelView(RedirectView):
    permanent = False
    query_string = True # Pass along query string if needed

    def get_redirect_url(self, *args, **kwargs):
        # Redirecting to the TPLMaster list view as a sensible default
        # In a real app, this would point to the exact target of the ASP.NET redirect
        return reverse_lazy('tplmaster_list')
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates are designed with **DRY principles**, extending `core/base.html` (not included here). HTMX attributes are used for dynamic content loading, form submissions, and modal interactions. DataTables are initialized for list views. Alpine.js can be used for simple UI state management, though explicit Alpine.js directives are kept minimal for this example, relying primarily on HTMX.

#### `design_reports/templates/design_reports/tpl_print/report_view.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">TPL - Print Report</h2>
        <a href="{% url 'tpl_print_cancel' %}?WONo={{ wono }}&SD={{ start_date }}&TD={{ up_to_date }}&PId={{ p_id }}&CId={{ c_id }}&ModId=3&SubModId=23" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">Report Details</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            <div><strong>Work Order No:</strong> {{ wono|default:"N/A" }}</div>
            <div><strong>Company:</strong> {{ company_name|default:"N/A" }}</div>
            <div><strong>Address:</strong> {{ company_address|default:"N/A" }}</div>
            <div><strong>Project Title:</strong> {{ project_title|default:"N/A" }}</div>
            {% if start_date %}
            <div><strong>Start Date:</strong> {{ start_date }}</div>
            {% endif %}
            {% if up_to_date %}
            <div><strong>Up To Date:</strong> {{ up_to_date }}</div>
            {% endif %}
        </div>
    </div>

    <div id="report-table-container"
         hx-trigger="load, refreshReport from:body"
         hx-get="{% url 'tpl_print_report_table' %}?wono={{ wono }}&SD={{ start_date }}&TD={{ up_to_date }}&PId={{ p_id|default:'' }}&CId={{ c_id|default:'' }}"
         hx-swap="innerHTML">
        <!-- Report DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Report Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state
    });
</script>
{% endblock %}
```

#### `design_reports/templates/design_reports/tpl_print/_report_table.html`

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="tplReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TPL Qty</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Comp Id</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AC</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if report_rows %}
                {% for row in report_rows %}
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.ItemCode }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.ManfDesc }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.UOM }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.Qty|floatformat:"2" }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.TPLQty|floatformat:"2" }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.WONo }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.CompId }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.AC }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.StartDate }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="py-3 px-6 text-center text-sm text-gray-500">No report data found for the given criteria.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Check if DataTables is already initialized to prevent re-initialization
    if (!$.fn.DataTable.isDataTable('#tplReportTable') && $('#tplReportTable tbody tr').length > 0) {
        $('#tplReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Enable responsive design
            "searching": true, // Enable search box
            "ordering": true,  // Enable column ordering
            "info": true,      // Enable info display (showing X of Y entries)
            "paging": true     // Enable pagination
        });
    }
});
</script>
```

#### `design_reports/templates/design_reports/tplmaster/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">TPL Records Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md"
            hx-get="{% url 'tplmaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New TPL Record
        </button>
    </div>
    
    <div id="tplmasterTable-container"
         hx-trigger="load, refreshTPLMasterList from:body"
         hx-get="{% url 'tplmaster_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading TPL Records...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on closeModal remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state
        // For example, managing modal open/close states can be enhanced with Alpine.js
        // but HTMX triggers are handling it directly here.
    });
</script>
{% endblock %}
```

#### `design_reports/templates/design_reports/tplmaster/_tplmaster_table.html`

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="tplMasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if tpl_records %}
                {% for obj in tpl_records %}
                <tr class="hover:bg-gray-50">
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.won_o }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.item.item_code|default:"N/A" }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.qty|floatformat:"2" }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.sys_date|date:"d M Y" }}</td>
                    <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                            hx-get="{% url 'tplmaster_edit' obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                            hx-get="{% url 'tplmaster_delete' obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="6" class="py-3 px-6 text-center text-sm text-gray-500">No TPL records found.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    if (!$.fn.DataTable.isDataTable('#tplMasterTable') && $('#tplMasterTable tbody tr').length > 0) {
        $('#tplMasterTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true
        });
    }
});
</script>
```

#### `design_reports/templates/design_reports/tplmaster/form.html`

```html
<div class="p-6">
    <h3 class="text-xl font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} TPL Record</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" crucial for 204 response and HX-Trigger #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm"
                _="on click trigger closeModal from body"> {# Alpine.js or custom JS to close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-sm">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `design_reports/templates/design_reports/tplmaster/confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-xl font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this TPL Record?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns are defined for both the report view and the general TPLMaster CRUD operations. HTMX-specific endpoints (`_table.html` partials) are created to allow for dynamic table loading without full page refreshes.

```python
# design_reports/urls.py
from django.urls import path
from django.views.generic import RedirectView # For the cancel button redirect
from .views import (
    TPLMasterListView, TPLMasterCreateView, TPLMasterUpdateView, TPLMasterDeleteView,
    TPLMasterTablePartialView, # Specific view for HTMX table refresh
    TPLReportView, TPLReportTableView, TPLPrintCancelView
)

urlpatterns = [
    # URLs for the TPL Print Report (direct migration of the ASP.NET page)
    path('tpl_print_report/', TPLReportView.as_view(), name='tpl_print_report'),
    path('tpl_print_report/table/', TPLReportTableView.as_view(), name='tpl_print_report_table'),
    # Original ASP.NET cancel button redirected to TPL_Design_Print_Tree.aspx.
    # This Django URL provides a corresponding redirect target.
    path('tpl_print_report/cancel/', TPLPrintCancelView.as_view(), name='tpl_print_cancel'),

    # URLs for general TPLMaster CRUD (as per prompt's request for CRUD operations)
    path('tplrecords/', TPLMasterListView.as_view(), name='tplmaster_list'),
    path('tplrecords/table/', TPLMasterTablePartialView.as_view(), name='tplmaster_table_partial'), # HTMX partial for the table
    path('tplrecords/add/', TPLMasterCreateView.as_view(), name='tplmaster_add'),
    path('tplrecords/edit/<int:pk>/', TPLMasterUpdateView.as_view(), name='tplmaster_edit'),
    path('tplrecords/delete/<int:pk>/', TPLMasterDeleteView.as_view(), name='tplmaster_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests are provided for the `TPLMaster` model, focusing on the `get_report_data` and `_calculate_effective_quantity_from_top` methods, which encapsulate the complex business logic from the ASP.NET code-behind. Integration tests cover all CRUD views (`ListView`, `CreateView`, `UpdateView`, `DeleteView`) and the report-specific views, including HTMX interactions and expected status codes/triggers.

```python
# design_reports/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import TPLMaster, ItemMaster, UnitMaster, Company, FinancialYear
from datetime import datetime, date
import json

class TPLMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required related data for all tests
        cls.company = Company.objects.create(id=1, name='Test Company Corp', address='123 Test St, Testville')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        
        cls.item1 = ItemMaster.objects.create(id=1, item_code='ITEM001', part_no='P-001', manf_desc='Assembly Product A', uom_basic=cls.unit, category_id=101)
        cls.item2 = ItemMaster.objects.create(id=2, item_code='ITEM002', part_no='P-002', manf_desc='Component B', uom_basic=cls.unit, category_id=102)
        cls.item3 = ItemMaster.objects.create(id=3, item_code='ITEM003', part_no='P-003', manf_desc='Sub-component C', uom_basic=cls.unit, category_id=103)
        cls.item4 = ItemMaster.objects.create(id=4, item_code='ITEM004', part_no='P-004', manf_desc='Another Component D', uom_basic=cls.unit, category_id=104)

        # Create hierarchical TPLMaster data for WO-001
        # WO-001: ITEM001 (qty 1) -> ITEM002 (qty 2) -> ITEM003 (qty 3)
        # WO-001: ITEM001 (qty 1) -> ITEM004 (qty 4)
        cls.tpl_master_root = TPLMaster.objects.create(
            id=1, c_id=100, won_o='WO-001', p_id=0, fin_year=cls.fin_year, company=cls.company,
            sys_date=datetime(2023, 1, 1, 10, 0, 0), qty=1.0, item=cls.item1
        )
        cls.tpl_master_lvl1_b = TPLMaster.objects.create(
            id=2, c_id=200, won_o='WO-001', p_id=100, fin_year=cls.fin_year, company=cls.company,
            sys_date=datetime(2023, 1, 2, 10, 30, 0), qty=2.0, item=cls.item2
        )
        cls.tpl_master_lvl2_c = TPLMaster.objects.create(
            id=3, c_id=300, won_o='WO-001', p_id=200, fin_year=cls.fin_year, company=cls.company,
            sys_date=datetime(2023, 1, 3, 11, 0, 0), qty=3.0, item=cls.item3
        )
        cls.tpl_master_lvl1_d = TPLMaster.objects.create(
            id=4, c_id=400, won_o='WO-001', p_id=100, fin_year=cls.fin_year, company=cls.company,
            sys_date=datetime(2023, 1, 4, 11, 30, 0), qty=4.0, item=cls.item4
        )
        # Another independent TPL record for WO-002
        cls.tpl_master_wo2 = TPLMaster.objects.create(
            id=5, c_id=500, won_o='WO-002', p_id=0, fin_year=cls.fin_year, company=cls.company,
            sys_date=datetime(2023, 2, 1, 9, 0, 0), qty=10.0, item=cls.item1
        )

    def test_tplmaster_creation(self):
        obj = TPLMaster.objects.get(id=1)
        self.assertEqual(obj.won_o, 'WO-001')
        self.assertEqual(obj.item.item_code, 'ITEM001')
        self.assertEqual(obj.qty, 1.0)
        
    def test_item_relation(self):
        obj = TPLMaster.objects.get(id=2)
        self.assertEqual(obj.item.manf_desc, 'Component B')
        self.assertEqual(obj.item.uom_basic.symbol, 'PCS')

    def test_get_project_title(self):
        title = TPLMaster.get_project_title('WO-001')
        self.assertEqual(title, 'Project for Work Order: WO-001')

    def test_calculate_effective_quantity_from_top(self):
        # ITEM001 (Root): Qty 1.0, should be 1.0 * 1.0 = 1.0
        self.assertAlmostEqual(TPLMaster._calculate_effective_quantity_from_top('WO-001', self.tpl_master_root.c_id, self.company.id, self.fin_year.id), 1.0)
        
        # ITEM002 (Child of ITEM001): Qty 2.0, parent is ITEM001 (Qty 1.0) -> 2.0 * 1.0 = 2.0
        self.assertAlmostEqual(TPLMaster._calculate_effective_quantity_from_top('WO-001', self.tpl_master_lvl1_b.c_id, self.company.id, self.fin_year.id), 2.0 * 1.0)
        
        # ITEM003 (Child of ITEM002): Qty 3.0, parent ITEM002 (Qty 2.0), grandparent ITEM001 (Qty 1.0) -> 3.0 * 2.0 * 1.0 = 6.0
        self.assertAlmostEqual(TPLMaster._calculate_effective_quantity_from_top('WO-001', self.tpl_master_lvl2_c.c_id, self.company.id, self.fin_year.id), 3.0 * 2.0 * 1.0)

        # ITEM004 (Another child of ITEM001): Qty 4.0, parent ITEM001 (Qty 1.0) -> 4.0 * 1.0 = 4.0
        self.assertAlmostEqual(TPLMaster._calculate_effective_quantity_from_top('WO-001', self.tpl_master_lvl1_d.c_id, self.company.id, self.fin_year.id), 4.0 * 1.0)

    def test_get_report_data_simple_wono(self):
        report_data = TPLMaster.get_report_data(
            wono='WO-001', comp_id=self.company.id, fin_year_id=self.fin_year.id, p_id_query=0
        )
        self.assertEqual(len(report_data), 4) # ITEM001, ITEM002, ITEM003, ITEM004
        
        item_codes = {row['ItemCode'] for row in report_data}
        self.assertIn('ITEM001', item_codes)
        self.assertIn('ITEM002', item_codes)
        self.assertIn('ITEM003', item_codes)
        self.assertIn('ITEM004', item_codes)

        # Verify TPLQty for ITEM003
        item3_row = next((row for row in report_data if row['ItemCode'] == 'ITEM003'), None)
        self.assertIsNotNone(item3_row)
        self.assertAlmostEqual(item3_row['TPLQty'], 6.0) # 3.0 * 2.0 * 1.0

    def test_get_report_data_with_date_range(self):
        # Only ITEM001 and ITEM002 should be in this range
        report_data = TPLMaster.get_report_data(
            wono='WO-001', comp_id=self.company.id, fin_year_id=self.fin_year.id,
            start_date_str='01/01/2023', up_to_date_str='02/01/2023', p_id_query=0
        )
        self.assertEqual(len(report_data), 2) # ITEM001, ITEM002
        item_codes = {row['ItemCode'] for row in report_data}
        self.assertIn('ITEM001', item_codes)
        self.assertIn('ITEM002', item_codes)
        self.assertNotIn('ITEM003', item_codes)
        self.assertNotIn('ITEM004', item_codes)
        
        # Test with a date range that excludes all items
        report_data_filtered = TPLMaster.get_report_data(
            wono='WO-001', comp_id=self.company.id, fin_year_id=self.fin_year.id,
            start_date_str='05/01/2023', up_to_date_str='05/01/2023', p_id_query=0
        )
        self.assertEqual(len(report_data_filtered), 0)

    def test_get_report_data_specific_component_query(self):
        # Query for ITEM002 (CId=200) as a starting point (PId=100 means its parent is ITEM001)
        report_data = TPLMaster.get_report_data(
            wono='WO-001', comp_id=self.company.id, fin_year_id=self.fin_year.id,
            p_id_query=100, c_id_query=200 # Start traversal from ITEM002 which has PId 100
        )
        # Should include ITEM002 and its child ITEM003
        self.assertEqual(len(report_data), 2) 
        item_codes = {row['ItemCode'] for row in report_data}
        self.assertIn('ITEM002', item_codes)
        self.assertIn('ITEM003', item_codes)
        self.assertAlmostEqual(next(row for row in report_data if row['ItemCode'] == 'ITEM003')['TPLQty'], 6.0)


class TPLViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Setup common data for views tests
        cls.company = Company.objects.create(id=1, name='Test Company Corp', address='123 Test St, Testville')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.unit = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.item1 = ItemMaster.objects.create(id=1, item_code='ITEMX01', part_no='PX-01', manf_desc='Test Product X', uom_basic=cls.unit, category_id=901)
        cls.item2 = ItemMaster.objects.create(id=2, item_code='ITEMY02', part_no='PY-02', manf_desc='Test Component Y', uom_basic=cls.unit, category_id=902)

        # Create TPLMaster instance for CRUD tests
        cls.tpl_record_for_crud = TPLMaster.objects.create(
            id=10, c_id=1000, won_o='WO-CRUD', p_id=0, fin_year=cls.fin_year, company=cls.company,
            sys_date=datetime(2023, 3, 1, 12, 0, 0), qty=20.0, item=cls.item1
        )
        
        # Set session for company and financial year (simulating user login context)
        cls.client.session['compid'] = cls.company.id
        cls.client.session['finyear'] = cls.fin_year.id

    # --- TPL Print Report Views Tests ---
    def test_tpl_report_view_get(self):
        response = self.client.get(reverse('tpl_print_report'), {
            'wono': 'WO-CRUD', 
            'SD': '01/03/2023', 
            'TD': '01/03/2023',
            'PId': '0'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_reports/tpl_print/report_view.html')
        self.assertContains(response, 'WO-CRUD')
        self.assertContains(response, 'Test Company Corp')
        
    def test_tpl_report_table_partial_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('tpl_print_report_table'), {
            'wono': 'WO-CRUD', 
            'SD': '01/03/2023', 
            'TD': '01/03/2023',
            'PId': '0'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_reports/tpl_print/_report_table.html')
        self.assertContains(response, 'WO-CRUD')
        self.assertContains(response, 'ITEMX01')
        self.assertContains(response, '20.00') # Check formatted quantity

    def test_tpl_print_cancel_view_redirect(self):
        response = self.client.get(reverse('tpl_print_cancel'))
        # Should redirect to 'tplmaster_list' as configured
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('tplmaster_list'))

    # --- CRUD Views for TPLMaster Tests ---
    def test_tplmaster_list_view(self):
        response = self.client.get(reverse('tplmaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_reports/tplmaster/list.html')
        # The queryset is empty initially, as the table is loaded via HTMX
        self.assertIsNone(response.context.get('tpl_records')) 
        self.assertContains(response, 'TPL Records Management')

    def test_tplmaster_table_partial_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('tplmaster_table_partial'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_reports/tplmaster/_tplmaster_table.html')
        self.assertIn('tpl_records', response.context)
        self.assertContains(response, self.tpl_record_for_crud.won_o)

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('tplmaster_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_reports/tplmaster/form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add TPL Record')

    def test_create_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'c_id': 1001,
            'won_o': 'WO-NEW',
            'p_id': 0,
            'fin_year': self.fin_year.id,
            'company': self.company.id,
            'sys_date': '2023-04-01',
            'qty': 30.0,
            'item': self.item2.id,
        }
        response = self.client.post(reverse('tplmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertTrue(TPLMaster.objects.filter(won_o='WO-NEW', c_id=1001).exists())
        self.assertEqual(response.headers['HX-Trigger'], json.dumps({'refreshTPLMasterList': None, 'closeModal': None}))

    def test_create_view_post_invalid(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'c_id': '', # Invalid: missing required field
            'won_o': 'WO-INVALID',
            'fin_year': self.fin_year.id,
            'company': self.company.id,
            'sys_date': '2023-04-01',
            'qty': 30.0,
            'item': self.item2.id,
        }
        response = self.client.post(reverse('tplmaster_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX returns 200 to re-render form with errors
        self.assertTemplateUsed(response, 'design_reports/tplmaster/form.html')
        self.assertContains(response, 'This field is required')
        self.assertFalse(TPLMaster.objects.filter(won_o='WO-INVALID').exists())

    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('tplmaster_edit', args=[self.tpl_record_for_crud.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_reports/tplmaster/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.tpl_record_for_crud)
        self.assertContains(response, 'Edit TPL Record')

    def test_update_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_qty = 25.0
        data = {
            'c_id': self.tpl_record_for_crud.c_id,
            'won_o': self.tpl_record_for_crud.won_o,
            'p_id': self.tpl_record_for_crud.p_id,
            'fin_year': self.tpl_record_for_crud.fin_year.id,
            'company': self.tpl_record_for_crud.company.id,
            'sys_date': self.tpl_record_for_crud.sys_date.strftime('%Y-%m-%d'),
            'qty': updated_qty,
            'item': self.tpl_record_for_crud.item.id,
        }
        response = self.client.post(reverse('tplmaster_edit', args=[self.tpl_record_for_crud.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.tpl_record_for_crud.refresh_from_db()
        self.assertEqual(self.tpl_record_for_crud.qty, updated_qty)
        self.assertEqual(response.headers['HX-Trigger'], json.dumps({'refreshTPLMasterList': None, 'closeModal': None}))

    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('tplmaster_delete', args=[self.tpl_record_for_crud.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_reports/tplmaster/confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')

    def test_delete_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_record_for_delete = TPLMaster.objects.create(
            id=11, c_id=1002, won_o='WO-DELETE', p_id=0, fin_year=self.fin_year, company=self.company,
            sys_date=datetime(2023, 5, 1, 9, 0, 0), qty=50.0, item=self.item1
        )
        self.assertTrue(TPLMaster.objects.filter(id=new_record_for_delete.pk).exists())
        
        response = self.client.post(reverse('tplmaster_delete', args=[new_record_for_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(TPLMaster.objects.filter(id=new_record_for_delete.pk).exists())
        self.assertEqual(response.headers['HX-Trigger'], json.dumps({'refreshTPLMasterList': None, 'closeModal': None}))

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for dynamic updates**: All major interactions, including loading the report table, loading the TPL record list table, and submitting CRUD forms (add, edit, delete), are handled via HTMX.
    -   `hx-get` is used to fetch partial HTML content for tables and modal forms.
    -   `hx-post` is used for form submissions.
    -   `hx-target` specifies where the new content should be inserted.
    -   `hx-swap` controls how the content is inserted (e.g., `innerHTML`, `none`).
    -   `hx-trigger` allows custom events (`refreshTPLMasterList`, `closeModal`) to trigger updates, ensuring the main list is refreshed after a successful CRUD operation and the modal is closed.
    -   Views return `status=204` (No Content) with `HX-Trigger` headers for successful form submissions via HTMX, allowing the client to handle the UI update without a full page reload or content replacement. Invalid forms return `status=200` to re-render the form with errors.
-   **Alpine.js for UI state management**: While HTMX handles core dynamic content, Alpine.js (via `_ = "on click add .is-active to #modal"`) manages the modal's visibility. The `closeModal` HTMX trigger also uses Alpine.js to close the modal, making the UI more responsive.
-   **DataTables for list views**: Both the TPL Report table (`tplReportTable`) and the TPL Records management table (`tplMasterTable`) are initialized with DataTables, providing out-of-the-box search, sort, and pagination capabilities. The initialization script for DataTables is placed within the partial HTML templates to ensure it runs *after* the table content is loaded via HTMX.
-   **No custom JavaScript requirements beyond DataTables and HTMX/Alpine**: All dynamic behavior is achieved through these libraries, adhering to the "no additional JavaScript" principle.
-   **DRY template inheritance**: All templates extend `core/base.html`, ensuring all necessary CDN links (HTMX, Alpine.js, jQuery, DataTables, Tailwind CSS) are included once in the base template, avoiding repetition.

## Final Notes

-   **Placeholders**: `[APP_NAME]`, `[MODEL_NAME]`, `[TABLE_NAME]`, etc., have been replaced with `design_reports`, `TPLMaster`, `tblDG_TPL_Master`, etc.
-   **Fat Model, Thin View**: Business logic, especially the complex recursive quantity calculation (`_calculate_effective_quantity_from_top` and `get_report_data`), resides entirely within the `TPLMaster` model, keeping views concise and focused on request/response handling.
-   **Component-Specific Code**: Only code relevant to the `design_reports` module is generated. `base.html` is assumed to exist externally.
-   **Comprehensive Tests**: Unit tests validate model methods and business logic, while integration tests ensure all views and HTMX interactions function correctly, striving for high test coverage.
-   **Automation Focus**: The plan outlines distinct, modular files (models, forms, views, templates, URLs, tests) that can be generated and integrated systematically, making it suitable for AI-assisted migration automation tools.
-   **Business Value**: This modernization shifts from a legacy, proprietary Crystal Reports system to a modern, open-source Django stack, leveraging efficient frontend patterns (HTMX/Alpine.js) and robust data presentation (DataTables). This reduces licensing costs, improves development agility, enhances user experience with dynamic interfaces, and sets the stage for future scalability and maintainability.