## ASP.NET to Django Conversion Script: BOM Design Work Orders

This document outlines a comprehensive modernization plan to transition the provided ASP.NET `BOM_Design_CopyWo.aspx` application to a modern Django solution. Our approach prioritizes AI-assisted automation, leveraging Django's robust features, HTMX for dynamic interactions, Alpine.js for lightweight frontend reactivity, and DataTables for efficient data presentation. The focus is on creating a scalable, maintainable, and highly performant application with a strict "Fat Model, Thin View" architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code utilizes `SD_Cust_WorkOrder_Master` as its primary data source for work orders. It performs implicit lookups/joins with `SD_Cust_Master`, `tblFinancial_master`, and `tblHR_OfficeStaff` to enrich the data display (e.g., retrieving customer names, financial year descriptions, and employee names).

**Extracted Tables and Columns:**

*   **Primary Table**: `SD_Cust_WorkOrder_Master`
    *   `WONo`: Work Order Number (likely the primary key or unique identifier for a work order).
    *   `EnqId`: Enquiry ID associated with the work order.
    *   `CustomerId`: Foreign key linking to the `SD_Cust_Master` table, identifying the customer.
    *   `PONo`: Purchase Order Number related to the work order.
    *   `SessionId`: Foreign key linking to `tblHR_OfficeStaff.EmpId`, identifying the employee who generated the work order.
    *   `FinYearId`: Foreign key linking to `tblFinancial_master`, specifying the financial year.
    *   `SysDate`: System Date (stored as `VARCHAR` in the database, displayed as `dd-mm-yyyy`).
    *   `CompId`: Company ID (used as a global filter across relevant tables).

*   **Lookup Tables**:
    *   `SD_Cust_Master`
        *   `CustomerId`: Customer ID (Primary Key).
        *   `CustomerName`: Name of the customer.
        *   `CompId`: Company ID.
        *   `FinYearId`: Financial Year ID.
    *   `tblFinancial_master`
        *   `FinYearId`: Financial Year ID (Primary Key).
        *   `FinYear`: Descriptive name of the financial year.
    *   `tblHR_OfficeStaff`
        *   `EmpId`: Employee ID (Primary Key).
        *   `Title`: Employee's title (e.g., Mr., Ms.).
        *   `EmployeeName`: Name of the employee.
        *   `CompId`: Company ID.
        *   `FinYearId`: Financial Year ID.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The provided ASP.NET code primarily focuses on **reading** and presenting data, specifically a list of Work Orders. There are no direct Create, Update, or Delete operations implemented on this page. The page name `BOM_Design_CopyWo.aspx` and the dynamic link generation suggest its purpose is to select a Work Order for a subsequent Bill of Material (BOM) copying operation.

*   **Read (Query/Filter/Display)**:
    *   Fetching and displaying a list of Work Orders from `SD_Cust_WorkOrder_Master` and related tables (`SD_Cust_Master`, `tblFinancial_master`, `tblHR_OfficeStaff`).
    *   Supports dynamic filtering based on user-selected criteria: Customer Name, Enquiry No, PO No, or WO No.
    *   Includes an autocomplete feature for searching by Customer Name.
    *   Handles pagination and client-side sorting/filtering via the `GridView` control.
    *   Displays general messages to the user (`lblasslymsg`).

*   **Interaction/Navigation**:
    *   Generates a dynamic hyperlink on the "WONo" column within the displayed grid. This link redirects to `BOM_Design_Copy_Tree.aspx`, passing parameters including the selected Work Order's number (`WONoSrc`) and other context-specific details (`WONoDest`, `DestPId`, `DestCId`, `ModId`, `SubModId`).

**Validation Logic**:
- Basic presence checks for search input fields based on the selected search type. No complex business rule validation is explicitly defined within the provided code snippet.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The ASP.NET page uses standard Web Forms controls. In our Django migration, these will be replaced with semantic HTML, enhanced by Tailwind CSS for styling, and dynamic behaviors managed by HTMX and Alpine.js.

*   **Search and Filtering**:
    *   `asp:DropDownList (ID="DropDownList1")`: This dropdown selects the search criteria (e.g., "Customer Name", "Enquiry No"). Its `AutoPostBack` functionality will be replaced by HTMX `hx-trigger="change"` to refresh the search form partial.
    *   `asp:TextBox (ID="txtSearchCustomer")`: A general text input for search values when criteria are Enquiry No, PO No, or WO No. Its visibility is controlled by the dropdown.
    *   `asp:TextBox (ID="TxtSearchValue")` with `cc1:AutoCompleteExtender`: A specific text input for "Customer Name" search, featuring an autocomplete. Its visibility is also controlled by the dropdown. This will be replaced by an HTMX-powered autocomplete endpoint and an Alpine.js `x-show` directive.
    *   `asp:Button (ID="btnSearch")`: Triggers the search. This will be an HTML `button` with HTMX attributes to trigger a table refresh.

*   **Data Display**:
    *   `asp:GridView (ID="SearchGridView1")`: The primary component for displaying the list of Work Orders. It configured for paging, sorting, and manual column generation. This will be replaced by a `<table>` element integrated with DataTables, populated dynamically via HTMX.

*   **Auxiliary UI**:
    *   `asp:Label (ID="lblasslymsg")`: Displays messages. This will be a simple `<span>` or `<div>` element in Django templates.
    *   `asp:HiddenField (ID="hfSort", "hfSearchText")` and `asp:ScriptManager (ID="ScriptManager1")`: These ASP.NET specific controls for state management and AJAX will be rendered obsolete by HTMX and Alpine.js, which handle dynamic interactions in a stateless manner.

The visibility toggling of search input fields and the autocomplete functionality will be handled by Alpine.js for client-side reactivity and HTMX for server-side data fetching.

### Step 4: Generate Django Code

The Django application will be named `bom_design`.

#### 4.1 Models (`bom_design/models.py`)

This file defines the Django models that map directly to the existing database tables. `managed = False` is crucial here, preventing Django from attempting to create or alter these tables. A custom `WorkOrderManager` is introduced to encapsulate the complex filtering logic, adhering to the "Fat Model" principle.

```python
# bom_design/models.py
from django.db import models
import datetime

# Related models first, as WorkOrder will have ForeignKeys to them.
class Customer(models.Model):
    """
    Maps to the SD_Cust_Master table.
    Contains customer details used for lookups.
    """
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class FinancialYear(models.Model):
    """
    Maps to the tblFinancial_master table.
    Contains financial year information.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    """
    Maps to the tblHR_OfficeStaff table.
    Contains employee details.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId can be string based on SessionId
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        # Replicates the original ASP.NET "Title + '.' + EmployeeName" format
        return f"{self.title or ''}. {self.employee_name}".strip()

class WorkOrderManager(models.Manager):
    """
    Custom manager for WorkOrder to encapsulate complex search and filtering logic.
    This method mirrors the BindDataCust logic from the ASP.NET code-behind,
    ensuring all filtering and joins are handled efficiently at the model layer.
    """
    def get_filtered_work_orders(self, search_type, search_value, company_id, financial_year_id):
        # Base queryset filters by global context (Company ID and Financial Year)
        # ASP.NET code used FinYearId <= current_fin_year_id
        queryset = self.get_queryset().filter(
            comp_id=company_id,
            financial_year__fin_year_id__lte=financial_year_id
        ).select_related('customer', 'financial_year', 'generated_by_employee') # Eager load related data

        # Apply specific search filters based on the selected type
        if search_value:
            if search_type == '1': # Enquiry No
                queryset = queryset.filter(enq_id__icontains=search_value)
            elif search_type == '2': # PO No
                queryset = queryset.filter(po_no__icontains=search_value)
            elif search_type == '3': # WO No
                queryset = queryset.filter(wo_no__icontains=search_value)
            elif search_type == '0': # Customer Name (requires CustomerId lookup)
                # The ASP.NET autocomplete returns "CustomerName [CustomerId]".
                # We need to extract the CustomerId for filtering.
                customer_id_from_search = None
                if '[' in search_value and ']' in search_value:
                    try:
                        customer_id_from_search = search_value.split('[')[-1][:-1].strip()
                    except IndexError:
                        pass # Handle malformed string gracefully

                if customer_id_from_search:
                    queryset = queryset.filter(customer__customer_id=customer_id_from_search)
                else:
                    # Fallback: if no valid CustomerId was extracted, try filtering by customer name (less precise)
                    queryset = queryset.filter(customer__customer_name__icontains=search_value)

        # Order the results as per the original ASP.NET implementation
        queryset = queryset.order_by('wo_no')
        return queryset

class WorkOrder(models.Model):
    """
    Maps to the SD_Cust_WorkOrder_Master table.
    Represents a Work Order, linking to Customer, Financial Year, and Employee details.
    """
    wo_no = models.CharField(db_column='WONo', primary_key=True, max_length=50)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    sys_date_raw = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    # Foreign keys to related lookup tables, matching DB column names
    customer = models.ForeignKey(
        Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='work_orders',
        to_field='customer_id', # Explicitly maps to Customer.CustomerId
        null=True, blank=True # Allow null if some work orders might not have these associations
    )
    generated_by_employee = models.ForeignKey(
        Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='work_orders_generated',
        to_field='emp_id', # Explicitly maps to Employee.EmpId
        null=True, blank=True
    )
    financial_year = models.ForeignKey(
        FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='work_orders',
        to_field='fin_year_id', # Explicitly maps to FinancialYear.FinYearId
        null=True, blank=True
    )

    objects = WorkOrderManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

    @property
    def formatted_sys_date(self):
        """Converts the raw SysDate string (e.g., 'dd-mm-yyyy') to a consistently formatted date string."""
        if not self.sys_date_raw:
            return ''
        try:
            # The ASP.NET code's SQL conversion implies a dd-mm-yyyy format.
            parsed_date = datetime.datetime.strptime(self.sys_date_raw, '%d-%m-%Y').date()
            return parsed_date.strftime('%d-%m-%Y') # Return in desired dd-mm-yyyy format
        except ValueError:
            return self.sys_date_raw # Return raw string if parsing fails (e.g., malformed data)

    # Properties for cleaner access to related model data in templates
    @property
    def customer_name(self):
        return self.customer.customer_name if self.customer else ''

    @property
    def fin_year_name(self):
        return self.financial_year.fin_year if self.financial_year else ''

    @property
    def employee_full_name(self):
        return self.generated_by_employee.__str__() if self.generated_by_employee else ''

    def get_bom_copy_link_params(self, wono_dest, dest_pid, dest_cid):
        """
        Generates query parameters for the BOM_Design_Copy_Tree link,
        mirroring the dynamic URL construction from the original ASP.NET code.
        """
        return {
            'WONoSrc': self.wo_no,
            'WONoDest': wono_dest,
            'DestPId': dest_pid,
            'DestCId': dest_cid,
            'ModId': 3,  # Hardcoded in original ASP.NET
            'SubModId': 26 # Hardcoded in original ASP.NET
        }
```

#### 4.2 Forms (`bom_design/forms.py`)

This file defines a simple Django `forms.Form` to manage the search inputs. It's not a `ModelForm` as it's purely for filtering existing data. HTMX attributes are embedded directly in the widget definitions for seamless integration.

```python
# bom_design/forms.py
from django import forms

class WorkOrderSearchForm(forms.Form):
    """
    Form for defining search criteria for Work Orders.
    This form captures the user's search type and value, mirroring
    the ASP.NET DropDownList and TextBoxes.
    """
    SEARCH_CHOICES = [
        ('Select', 'Select Search Type'), # Default/placeholder option
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'x-model': 'searchType', # Alpine.js binding for visibility control
            'hx-get': "{% url 'bom_design:workorder_search_form_partial' %}", # HTMX to refresh search form partial
            'hx-target': '#searchFormContent', # Target div for HTMX swap
            'hx-swap': 'outerHTML',
            'hx-indicator': '#search-indicator', # Show indicator during swap
            'hx-trigger': 'change', # Trigger HTMX request on dropdown change
        })
    )

    # General search text input, shown/hidden by Alpine.js
    search_text_general = forms.CharField(
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter search value...',
            'autocomplete': 'off', # Prevent browser's built-in autocomplete
            'x-show': "searchType === '1' || searchType === '2' || searchType === '3' || searchType === 'Select'", # Alpine.js visibility
            'x-model': 'searchTextGeneral', # Alpine.js binding
        })
    )

    # Specific input for Customer Name with autocomplete, shown/hidden by Alpine.js
    search_text_customer_autocomplete = forms.CharField(
        required=False,
        label="Customer Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Start typing customer name...',
            'hx-get': "{% url 'bom_design:customer_autocomplete' %}", # HTMX endpoint for autocomplete suggestions
            'hx-trigger': "keyup changed delay:500ms, search", # Trigger after typing or explicit search event
            'hx-target': "#autocomplete-results", # Target for autocomplete suggestions
            'hx-indicator': "#autocomplete-indicator", # Show indicator during autocomplete fetch
            'hx-swap': "outerHTML", # Swap the entire container (including results div)
            'autocomplete': 'off', # Crucial to prevent browser's autocomplete
            'x-show': "searchType === '0'", # Alpine.js visibility
            'x-model': 'searchTextCustomer', # Alpine.js binding
            # Clear hidden customer ID if user starts typing in autocomplete field again
            '@input': 'customerSearchId = "";',
            '@click': 'customerSearchId = "";'
        })
    )

    # Hidden field to store the actual customer ID selected from autocomplete
    customer_id_hidden = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={
            'x-model': 'customerSearchId', # Alpine.js binding for actual ID
        })
    )

    # Hidden fields to pass through query string parameters required by the target link
    wono_dest = forms.CharField(widget=forms.HiddenInput(), required=False)
    dest_pid = forms.CharField(widget=forms.HiddenInput(), required=False)
    dest_cid = forms.CharField(widget=forms.HiddenInput(), required=False)

```

#### 4.3 Views (`bom_design/views.py`)

Views are kept thin, delegating complex query logic to the `WorkOrderManager`. We define a main `TemplateView` for the page and HTMX-specific `ListView` and `View` classes for partial content updates and autocomplete.

```python
# bom_design/views.py
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.db.models import F # For F expressions if needed, though WorkOrderManager handles this

from .models import WorkOrder, Customer
from .forms import WorkOrderSearchForm

# Global context values (Company ID, Financial Year ID)
# In a real ERP, these would typically come from the authenticated user's session,
# a global context processor, or middleware, to ensure security and data scoping.
# For this migration example, we assume hardcoded values mirroring the ASP.NET behavior.
GLOBAL_COMPANY_ID = 1 # Example company ID
GLOBAL_FINANCIAL_YEAR_ID = 2024 # Example financial year ID, matching ASP.NET's <= logic

class WorkOrderListView(TemplateView):
    """
    Main view to display the Work Order search interface.
    This view renders the initial HTML structure including the search form
    and a container that HTMX will populate with the Work Orders table.
    """
    template_name = 'bom_design/workorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with any existing GET parameters for persistence
        search_form = WorkOrderSearchForm(self.request.GET)
        context['search_form'] = search_form

        # Extract and pass through the context query parameters for BOM_Design_Copy_Tree link
        context['wono_dest'] = self.request.GET.get('WONoDest', '')
        context['dest_pid'] = self.request.GET.get('DestPId', '')
        context['dest_cid'] = self.request.GET.get('DestCId', '')
        context['message'] = self.request.GET.get('msg', '') # For the lblasslymsg

        # Pass initial form values to Alpine.js for its x-data component
        context['initial_search_type'] = search_form['search_type'].value
        context['initial_search_value_general'] = search_form['search_text_general'].value
        context['initial_search_value_customer'] = search_form['search_text_customer_autocomplete'].value
        context['initial_customer_id_hidden'] = search_form['customer_id_hidden'].value

        return context

class WorkOrderTablePartialView(ListView):
    """
    HTMX-triggered view responsible for rendering only the Work Order table content.
    This view applies filtering based on GET parameters and uses DataTables
    for client-side pagination and sorting.
    """
    model = WorkOrder
    template_name = 'bom_design/workorder/_workorder_table.html'
    context_object_name = 'work_orders'
    paginate_by = 17 # Matches the original ASP.NET GridView PageSize

    def get_queryset(self):
        # Retrieve search parameters from the GET request
        search_type = self.request.GET.get('search_type', 'Select')
        search_value_general = self.request.GET.get('search_text_general', '').strip()
        search_value_customer_autocomplete = self.request.GET.get('search_text_customer_autocomplete', '').strip()
        customer_id_hidden = self.request.GET.get('customer_id_hidden', '').strip()

        # Determine the effective search value based on the selected search type
        # If '0' (Customer Name) is selected, prioritize the hidden customer_id_hidden (from autocomplete)
        # otherwise, use the general search field.
        actual_search_value = ''
        if search_type == '0':
            actual_search_value = customer_id_hidden if customer_id_hidden else search_value_customer_autocomplete
        else:
            actual_search_value = search_value_general

        # Delegate filtering logic to the WorkOrderManager (Fat Model principle)
        queryset = WorkOrder.objects.get_filtered_work_orders(
            search_type=search_type,
            search_value=actual_search_value,
            company_id=GLOBAL_COMPANY_ID,
            financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass through the original query string parameters for the WONo link generation
        context['wono_dest'] = self.request.GET.get('WONoDest', '')
        context['dest_pid'] = self.request.GET.get('DestPId', '')
        context['dest_cid'] = self.request.GET.get('DestCId', '')
        return context

class WorkOrderSearchFormPartialView(TemplateView):
    """
    HTMX-triggered view to render only the search form content.
    Used when the search type dropdown changes (hx-trigger="change") to refresh
    the input fields and their Alpine.js `x-show` states.
    """
    template_name = 'bom_design/workorder/_search_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Re-initialize the form with current GET parameters to maintain its state
        form = WorkOrderSearchForm(self.request.GET)
        context['search_form'] = form
        # Ensure Alpine.js initial values are correctly passed for the new form render
        context['initial_search_type'] = form['search_type'].value
        context['initial_search_value_general'] = form['search_text_general'].value
        context['initial_search_value_customer'] = form['search_text_customer_autocomplete'].value
        context['initial_customer_id_hidden'] = form['customer_id_hidden'].value
        # Pass through global query params for hidden fields in the form
        context['wono_dest'] = self.request.GET.get('WONoDest', '')
        context['dest_pid'] = self.request.GET.get('DestPId', '')
        context['dest_cid'] = self.request.GET.get('DestCId', '')
        return context

class CustomerAutoCompleteView(View):
    """
    HTMX endpoint for customer name autocomplete functionality.
    This view queries the Customer model and returns HTML snippets (buttons)
    as suggestions for the autocomplete input, mimicking the ASP.NET behavior.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('search_text_customer_autocomplete', '').strip()
        
        # If no query, return an empty container to clear suggestions
        if not query:
            return HttpResponse('<div id="autocomplete-results"></div>')

        # Filter customers based on the query and global context
        customers = Customer.objects.filter(
            customer_name__icontains=query,
            comp_id=GLOBAL_COMPANY_ID,
            fin_year_id__lte=GLOBAL_FINANCIAL_YEAR_ID # Assuming customer table also uses this filter
        ).order_by('customer_name')[:10] # Limit the number of suggestions

        # Render suggestions directly as HTML buttons for HTMX to swap in
        # The ID ensures HTMX replaces this specific container.
        html = '<div id="autocomplete-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-y-auto">'
        if customers:
            for customer in customers:
                # The button's hx-on:click event updates the main input field
                # and the hidden customer_id_hidden field, then clears the suggestions.
                html += f"""
                    <button type="button"
                        class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        hx-on:click="
                            document.getElementById('id_search_text_customer_autocomplete').value = '{customer.customer_name} [{customer.customer_id}]';
                            customerSearchId = '{customer.customer_id}'; // Update Alpine.js variable for hidden ID
                            this.closest('#autocomplete-results').innerHTML = ''; // Clear suggestions
                        ">
                        {customer.customer_name} [<span class="font-mono text-gray-500">{customer.customer_id}</span>]
                    </button>
                """
        else:
            html += '<div class="px-4 py-2 text-sm text-gray-500">No results found</div>'
        html += '</div>'
        return HttpResponse(html)

```

#### 4.4 Templates

Templates leverage HTMX for dynamic content updates and Alpine.js for client-side UI state management, resulting in a single-page application feel without complex JavaScript. DataTables handles list presentation.

Location: `bom_design/templates/bom_design/workorder/`

`bom_design/templates/bom_design/workorder/list.html`:

```html
{% extends 'core/base.html' %}

{% block title %}Work Order Search - BOM Design{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">BOM Design: Copy Work Order</h2>
        <span class="text-red-600 font-bold mt-2 md:mt-0">{{ message }}</span>
    </div>

    <!-- Main container for the search form and table -->
    <!-- x-data initializes Alpine.js state for managing form visibility and autocomplete values -->
    <div class="bg-white shadow-md rounded-lg p-6"
         x-data="{
             searchType: '{{ initial_search_type }}', {# Binds to search_type dropdown #}
             searchTextGeneral: '{{ initial_search_value_general }}', {# Binds to general text input #}
             searchTextCustomer: '{{ initial_search_value_customer }}', {# Binds to customer autocomplete input #}
             customerSearchId: '{{ initial_customer_id_hidden }}' {# Binds to hidden customer ID #}
         }">
        <!-- Search Form Section - HTMX will swap this entire div on dropdown change -->
        <div id="searchFormContent">
            {% include 'bom_design/workorder/_search_form.html' with search_form=search_form wono_dest=wono_dest dest_pid=dest_pid dest_cid=dest_cid %}
        </div>

        <!-- Indicator for search form partial loading -->
        <div id="search-indicator" class="htmx-indicator mt-4 text-center text-blue-600">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p>Updating search options...</p>
        </div>

        <!-- Work Order Table Section -->
        <!-- HTMX loads this content on page load, search submit, and custom refresh event -->
        <div id="workOrderTable-container"
             hx-trigger="load, searchSubmit from:#workOrderSearchForm, refreshWorkOrderList from:body"
             hx-get="{% url 'bom_design:workorder_table' %}?search_type={{ search_form.search_type.value|default:'Select' }}&search_text_general={{ search_form.search_text_general.value|default:'' }}&search_text_customer_autocomplete={{ search_form.search_text_customer_autocomplete.value|default:'' }}&customer_id_hidden={{ search_form.customer_id_hidden.value|default:'' }}&WONoDest={{ wono_dest }}&DestPId={{ dest_pid }}&DestCId={{ dest_cid }}"
             hx-swap="innerHTML"
             class="mt-8 border border-gray-200 rounded-lg overflow-hidden">
            <!-- Initial loading indicator for the table -->
            <div class="text-center p-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Work Orders...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables CDN links (assuming these are in base.html as well for global availability, but explicitly re-including for clarity) -->
<!-- <script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script> -->
<!-- <script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script> -->

<script>
    // Listener for HTMX `htmx:afterSwap` event to re-initialize DataTables
    // This is crucial because HTMX replaces the table's innerHTML, destroying previous DataTables instances.
    document.addEventListener('htmx:afterSwap', function(event) {
        // Only re-initialize DataTables if the swapped content is the Work Order table container
        if (event.detail.target.id === 'workOrderTable-container') {
            $('#workOrderTable').DataTable({
                "pageLength": 17, // Matches original ASP.NET GridView PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "paging": true,
                "searching": true, // Enable DataTables built-in search box
                "ordering": true,
                "info": true,
                "responsive": true // Optional: for responsive tables
            });
        }
    });

    // Event listener to hide autocomplete suggestions when clicking outside the input/results
    document.addEventListener('click', function(event) {
        const autocompleteResults = document.getElementById('autocomplete-results');
        const searchInput = document.getElementById('id_search_text_customer_autocomplete');
        if (autocompleteResults && searchInput && !autocompleteResults.contains(event.target) && !searchInput.contains(event.target)) {
            autocompleteResults.innerHTML = ''; // Clear suggestions by emptying the container
        }
    });
</script>
{% endblock %}
```

`bom_design/templates/bom_design/workorder/_search_form.html`: (Partial template for the search form)

```html
<form hx-get="{% url 'bom_design:workorder_table' %}"
      hx-target="#workOrderTable-container"
      hx-swap="innerHTML"
      hx-trigger="submit" {# Trigger HTMX on form submit #}
      hx-indicator="#workOrderTable-container > .text-center" {# Show loading indicator for table #}
      class="space-y-4"
      id="workOrderSearchForm" {# ID for HTMX to reference for custom events #}
      x-data="{
          searchType: '{{ search_form.search_type.value }}',
          searchTextGeneral: '{{ search_form.search_text_general.value }}',
          searchTextCustomer: '{{ search_form.search_text_customer_autocomplete.value }}',
          customerSearchId: '{{ search_form.customer_id_hidden.value }}'
      }">
    {% csrf_token %} {# Django's CSRF token for security #}

    {# Hidden fields to pass through dynamic query parameters #}
    <input type="hidden" name="WONoDest" value="{{ wono_dest }}">
    <input type="hidden" name="DestPId" value="{{ dest_pid }}">
    <input type="hidden" name="DestCId" value="{{ dest_cid }}">

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
        <div class="col-span-1">
            <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ search_form.search_type.label }}
            </label>
            {{ search_form.search_type }} {# Renders the dropdown field #}
        </div>

        <div class="relative col-span-2">
            {# General search input, conditionally displayed by Alpine.js #}
            <label for="{{ search_form.search_text_general.id_for_label }}" class="block text-sm font-medium text-gray-700"
                   x-show="searchType === '1' || searchType === '2' || searchType === '3' || searchType === 'Select'">
                Search Value
            </label>
            {{ search_form.search_text_general }}

            {# Customer autocomplete input, conditionally displayed by Alpine.js #}
            <label for="{{ search_form.search_text_customer_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700"
                   x-show="searchType === '0'">
                Customer Name
            </label>
            {{ search_form.search_text_customer_autocomplete }}

            {# Autocomplete results container - HTMX swaps content here #}
            <div id="autocomplete-results"></div>
            {# HTMX indicator for autocomplete requests #}
            <div id="autocomplete-indicator" class="htmx-indicator absolute top-0 right-0 p-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
            
            {# Hidden field for the actual customer ID #}
            {{ search_form.customer_id_hidden }}
        </div>
        
        <div class="col-span-1">
            <button type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"
                    {# Trigger a custom event 'searchSubmit' on the form, caught by table container #}
                    hx-on:click="document.getElementById('workOrderSearchForm').dispatchEvent(new Event('searchSubmit'))">
                Search
            </button>
        </div>
    </div>
</form>
```

`bom_design/templates/bom_design/workorder/_workorder_table.html`: (Partial template for the DataTables table)

```html
<table id="workOrderTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if work_orders %}
            {% for wo in work_orders %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-900">{{ wo.fin_year_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ wo.customer_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ wo.customer.customer_id|default:'' }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ wo.enq_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ wo.po_no }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">
                    {# Dynamic link generation mirroring ASP.NET logic #}
                    {% with link_params=wo.get_bom_copy_link_params wono_dest=wono_dest dest_pid=dest_pid dest_cid=dest_cid %}
                    <a href="{% url 'bom_design:bom_design_copy_tree' %}?WONoSrc={{ link_params.WONoSrc }}&WONoDest={{ link_params.WONoDest }}&DestPId={{ link_params.DestPId }}&DestCId={{ link_params.DestCId }}&ModId={{ link_params.ModId }}&SubModId={{ link_params.SubModId }}"
                       class="text-blue-600 hover:underline">
                        {{ wo.wo_no }}
                    </a>
                    {% endwith %}
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ wo.formatted_sys_date }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">{{ wo.employee_full_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-900">
                    <!-- Example action button - can be replaced with more specific actions -->
                    <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded text-xs">
                        Select
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="10" class="py-4 px-4 text-center text-gray-500 text-lg">No data to display !</td>
            </tr>
        {% endif %}
    </tbody>
</table>
```

#### 4.5 URLs (`bom_design/urls.py`)

This file defines the URL patterns for the `bom_design` application, mapping incoming requests to the appropriate Django views. `app_name` ensures URL namespacing.

```python
# bom_design/urls.py
from django.urls import path
from .views import (
    WorkOrderListView,
    WorkOrderTablePartialView,
    WorkOrderSearchFormPartialView,
    CustomerAutoCompleteView,
)

app_name = 'bom_design' # Namespace for this application's URLs

urlpatterns = [
    # Main page URL for Work Order search and copy functionality
    path('bom-design/copy-wo/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX endpoint to fetch and render the Work Order table content dynamically
    path('bom-design/copy-wo/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),

    # HTMX endpoint to fetch and render the search form partial (used for dropdown change)
    path('bom-design/copy-wo/search-form-partial/', WorkOrderSearchFormPartialView.as_view(), name='workorder_search_form_partial'),

    # HTMX endpoint for customer name autocomplete suggestions
    path('autocomplete/customers/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder URL for the target page BOM_Design_Copy_Tree.aspx
    # This URL should be defined in the Django app that will handle the
    # functionality of the original BOM_Design_Copy_Tree.aspx page.
    # For now, it points to the current list view to avoid URL reversal errors.
    path('bom-design/copy-tree/', WorkOrderListView.as_view(), name='bom_design_copy_tree'),
]
```

#### 4.6 Tests (`bom_design/tests.py`)

These tests provide comprehensive coverage for both the model's business logic (via its manager) and the views' functionality, including HTMX interactions.

```python
# bom_design/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch # Used to mock global variables for consistent testing

from .models import Customer, FinancialYear, Employee, WorkOrder
from .forms import WorkOrderSearchForm
from .views import GLOBAL_COMPANY_ID, GLOBAL_FINANCIAL_YEAR_ID # Import actual global constants for testing

# Patch the global constants in views for predictable test results
@patch('bom_design.views.GLOBAL_COMPANY_ID', 101)
@patch('bom_design.views.GLOBAL_FINANCIAL_YEAR_ID', 2024)
class WorkOrderModelTest(TestCase):
    """
    Unit tests for WorkOrder model and its custom manager.
    Ensures that data handling, relationships, properties, and filtering
    logic work as expected.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.customer1 = Customer.objects.create(
            customer_id='CUST001', customer_name='Customer Alpha',
            comp_id=GLOBAL_COMPANY_ID, fin_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        cls.customer2 = Customer.objects.create(
            customer_id='CUST002', customer_name='Customer Beta',
            comp_id=GLOBAL_COMPANY_ID, fin_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        cls.fin_year_2023 = FinancialYear.objects.create(
            fin_year_id=2023, fin_year='2023-24'
        )
        cls.fin_year_2024 = FinancialYear.objects.create(
            fin_year_id=2024, fin_year='2024-25'
        )
        cls.fin_year_2025 = FinancialYear.objects.create( # For testing FinYearId <=
            fin_year_id=2025, fin_year='2025-26'
        )
        cls.employee1 = Employee.objects.create(
            emp_id='EMP001', title='Mr', employee_name='John Doe',
            comp_id=GLOBAL_COMPANY_ID, fin_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        cls.employee2 = Employee.objects.create(
            emp_id='EMP002', title='Ms', employee_name='Jane Smith',
            comp_id=GLOBAL_COMPANY_ID, fin_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )

        # Create various WorkOrder instances for filtering tests
        cls.wo_enq = WorkOrder.objects.create(
            wo_no='WO_ENQ1', enq_id='ENQ100', po_no='PO001', sys_date_raw='01-01-2024',
            comp_id=GLOBAL_COMPANY_ID, customer=cls.customer1,
            generated_by_employee=cls.employee1, financial_year=cls.fin_year_2024
        )
        cls.wo_po = WorkOrder.objects.create(
            wo_no='WO_PO1', enq_id='ENQ101', po_no='PO200', sys_date_raw='02-01-2024',
            comp_id=GLOBAL_COMPANY_ID, customer=cls.customer2,
            generated_by_employee=cls.employee1, financial_year=cls.fin_year_2024
        )
        cls.wo_wo = WorkOrder.objects.create(
            wo_no='WO_WO1', enq_id='ENQ102', po_no='PO003', sys_date_raw='03-01-2024',
            comp_id=GLOBAL_COMPANY_ID, customer=cls.customer1,
            generated_by_employee=cls.employee2, financial_year=cls.fin_year_2024
        )
        cls.wo_cust_a = WorkOrder.objects.create(
            wo_no='WO_CUSTA', enq_id='ENQ103', po_no='PO004', sys_date_raw='04-01-2024',
            comp_id=GLOBAL_COMPANY_ID, customer=cls.customer1,
            generated_by_employee=cls.employee1, financial_year=cls.fin_year_2024
        )
        cls.wo_cust_b = WorkOrder.objects.create(
            wo_no='WO_CUSTB', enq_id='ENQ104', po_no='PO005', sys_date_raw='05-01-2024',
            comp_id=GLOBAL_COMPANY_ID, customer=cls.customer2,
            generated_by_employee=cls.employee2, financial_year=cls.fin_year_2024
        )
        cls.wo_old_finyear = WorkOrder.objects.create(
            wo_no='WO_OLD_FY', enq_id='ENQ105', po_no='PO006', sys_date_raw='06-01-2023',
            comp_id=GLOBAL_COMPANY_ID, customer=cls.customer1,
            generated_by_employee=cls.employee1, financial_year=cls.fin_year_2023
        )
        cls.wo_other_company = WorkOrder.objects.create(
            wo_no='WO_OTHER_C', enq_id='ENQ106', po_no='PO007', sys_date_raw='07-01-2024',
            comp_id=999, customer=cls.customer1, # Different company ID
            generated_by_employee=cls.employee1, financial_year=cls.fin_year_2024
        )
        cls.wo_future_finyear = WorkOrder.objects.create(
            wo_no='WO_FUTURE_FY', enq_id='ENQ107', po_no='PO008', sys_date_raw='08-01-2025',
            comp_id=GLOBAL_COMPANY_ID, customer=cls.customer1,
            generated_by_employee=cls.employee1, financial_year=cls.fin_year_2025
        )

    def test_workorder_creation(self):
        """Verifies basic WorkOrder object creation and field mapping."""
        self.assertEqual(self.wo_enq.wo_no, 'WO_ENQ1')
        self.assertEqual(self.wo_enq.customer.customer_name, 'Customer Alpha')
        self.assertEqual(self.wo_enq.financial_year.fin_year, '2024-25')
        self.assertEqual(self.wo_enq.generated_by_employee.employee_name, 'John Doe')
        self.assertEqual(self.wo_enq.sys_date_raw, '01-01-2024')

    def test_formatted_sys_date_property(self):
        """Tests the formatted_sys_date property for correct date parsing and formatting."""
        self.assertEqual(self.wo_enq.formatted_sys_date, '01-01-2024')
        wo_test = WorkOrder.objects.create(
            wo_no='TEST_DATE', sys_date_raw='31-12-2023', comp_id=GLOBAL_COMPANY_ID,
            customer=self.customer1, generated_by_employee=self.employee1, financial_year=self.fin_year_2024
        )
        self.assertEqual(wo_test.formatted_sys_date, '31-12-2023')
        # Test with invalid date format
        wo_invalid_date = WorkOrder.objects.create(
            wo_no='INVALID_DATE', sys_date_raw='invalid-date', comp_id=GLOBAL_COMPANY_ID,
            customer=self.customer1, generated_by_employee=self.employee1, financial_year=self.fin_year_2024
        )
        self.assertEqual(wo_invalid_date.formatted_sys_date, 'invalid-date') # Should return raw if parsing fails
        # Test with null date
        wo_null_date = WorkOrder.objects.create(
            wo_no='NULL_DATE', sys_date_raw=None, comp_id=GLOBAL_COMPANY_ID,
            customer=self.customer1, generated_by_employee=self.employee1, financial_year=self.fin_year_2024
        )
        self.assertEqual(wo_null_date.formatted_sys_date, '')

    def test_related_properties(self):
        """Tests properties that access related model data."""
        self.assertEqual(self.wo_enq.customer_name, 'Customer Alpha')
        self.assertEqual(self.wo_enq.fin_year_name, '2024-25')
        self.assertEqual(self.wo_enq.employee_full_name, 'Mr. John Doe')

    def test_get_bom_copy_link_params(self):
        """Tests the dynamic link parameter generation for BOM copy functionality."""
        params = self.wo_enq.get_bom_copy_link_params(wono_dest='DEST_WO', dest_pid='123', dest_cid='456')
        self.assertEqual(params['WONoSrc'], 'WO_ENQ1')
        self.assertEqual(params['WONoDest'], 'DEST_WO')
        self.assertEqual(params['DestPId'], '123')
        self.assertEqual(params['DestCId'], '456')
        self.assertEqual(params['ModId'], 3)
        self.assertEqual(params['SubModId'], 26)

    def test_get_filtered_work_orders_no_filter(self):
        """Tests the manager with no search filters applied."""
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='Select', search_value='',
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        # Should return all work orders for GLOBAL_COMPANY_ID and FinYearId <= GLOBAL_FINANCIAL_YEAR_ID
        expected_work_orders = [
            self.wo_enq, self.wo_po, self.wo_wo, self.wo_cust_a, self.wo_cust_b, self.wo_old_finyear
        ]
        self.assertCountEqual(work_orders, expected_work_orders)
        self.assertEqual(work_orders.count(), len(expected_work_orders))

    def test_get_filtered_work_orders_enquiry_no(self):
        """Tests filtering by Enquiry No."""
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='1', search_value='ENQ100',
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertIn(self.wo_enq, work_orders)
        self.assertEqual(work_orders.count(), 1)

    def test_get_filtered_work_orders_po_no(self):
        """Tests filtering by PO No."""
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='2', search_value='PO200',
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertIn(self.wo_po, work_orders)
        self.assertEqual(work_orders.count(), 1)

    def test_get_filtered_work_orders_wo_no(self):
        """Tests filtering by WO No."""
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='3', search_value='WO_WO1',
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertIn(self.wo_wo, work_orders)
        self.assertEqual(work_orders.count(), 1)

    def test_get_filtered_work_orders_customer_id(self):
        """Tests filtering by Customer ID (when autocomplete provides ID)."""
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='0', search_value=self.customer1.customer_id, # Directly providing ID
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertIn(self.wo_enq, work_orders)
        self.assertIn(self.wo_wo, work_orders)
        self.assertIn(self.wo_cust_a, work_orders)
        self.assertIn(self.wo_old_finyear, work_orders)
        self.assertEqual(work_orders.count(), 4)

    def test_get_filtered_work_orders_customer_name_from_autocomplete_string(self):
        """Tests filtering by Customer Name when input string includes ID (e.g., 'Name [ID]')."""
        search_value = f"{self.customer1.customer_name} [{self.customer1.customer_id}]"
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='0', search_value=search_value,
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertIn(self.wo_enq, work_orders)
        self.assertIn(self.wo_wo, work_orders)
        self.assertIn(self.wo_cust_a, work_orders)
        self.assertIn(self.wo_old_finyear, work_orders)
        self.assertEqual(work_orders.count(), 4)

    def test_get_filtered_work_orders_customer_name_fallback(self):
        """Tests filtering by customer name if ID extraction fails from autocomplete string."""
        search_value = self.customer1.customer_name # Only name, no ID part
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='0', search_value=search_value,
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertIn(self.wo_enq, work_orders)
        self.assertIn(self.wo_wo, work_orders)
        self.assertIn(self.wo_cust_a, work_orders)
        self.assertIn(self.wo_old_finyear, work_orders)
        self.assertEqual(work_orders.count(), 4)

    def test_get_filtered_work_orders_company_filter(self):
        """Ensures work orders from different companies are excluded."""
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='Select', search_value='',
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertNotIn(self.wo_other_company, work_orders)
        self.assertEqual(work_orders.count(), 6)

    def test_get_filtered_work_orders_financial_year_lte_filter(self):
        """Ensures filtering by financial year (less than or equal to current)."""
        work_orders = WorkOrder.objects.get_filtered_work_orders(
            search_type='Select', search_value='',
            company_id=GLOBAL_COMPANY_ID, financial_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.assertIn(self.wo_old_finyear, work_orders) # fin_year_id=2023 <= 2024
        self.assertNotIn(self.wo_future_finyear, work_orders) # fin_year_id=2025 > 2024
        self.assertEqual(work_orders.count(), 6)


@patch('bom_design.views.GLOBAL_COMPANY_ID', 101)
@patch('bom_design.views.GLOBAL_FINANCIAL_YEAR_ID', 2024)
class WorkOrderViewsTest(TestCase):
    """
    Integration tests for Django views, covering HTTP responses, template usage,
    context data, and HTMX interactions.
    """
    def setUp(self):
        self.client = Client()
        # Setup minimal data for views to operate
        self.customer = Customer.objects.create(
            customer_id='CUSTVIEW', customer_name='View Test Customer',
            comp_id=GLOBAL_COMPANY_ID, fin_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.fin_year = FinancialYear.objects.create(
            fin_year_id=GLOBAL_FINANCIAL_YEAR_ID, fin_year='2024-25'
        )
        self.employee = Employee.objects.create(
            emp_id='EMPVIEW', title='Mr', employee_name='View User',
            comp_id=GLOBAL_COMPANY_ID, fin_year_id=GLOBAL_FINANCIAL_YEAR_ID
        )
        self.work_order = WorkOrder.objects.create(
            wo_no='WOVIEW1', enq_id='ENQVIEW', po_no='POVIEW', sys_date_raw='01-01-2024',
            comp_id=GLOBAL_COMPANY_ID, customer=self.customer,
            generated_by_employee=self.employee, financial_year=self.fin_year
        )

        self.list_url = reverse('bom_design:workorder_list')
        self.table_url = reverse('bom_design:workorder_table')
        self.autocomplete_url = reverse('bom_design:customer_autocomplete')
        self.search_form_partial_url = reverse('bom_design:workorder_search_form_partial')
        self.bom_copy_tree_url = reverse('bom_design:bom_design_copy_tree')

    def test_workorder_list_view_get(self):
        """Tests initial load of the main Work Order list page."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_design/workorder/list.html')
        self.assertIsInstance(response.context['search_form'], WorkOrderSearchForm)
        self.assertEqual(response.context['wono_dest'], '')
        self.assertEqual(response.context['message'], '')
        # Check Alpine.js initial data attributes
        self.assertContains(response, "x-data=\"{ searchType: 'Select'") # Default value

    def test_workorder_list_view_get_with_query_params(self):
        """Tests that query parameters are correctly passed to context."""
        response = self.client.get(f"{self.list_url}?WONoDest=DWO1&DestPId=P1&DestCId=C1&msg=TestMessage")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['wono_dest'], 'DWO1')
        self.assertEqual(response.context['dest_pid'], 'P1')
        self.assertEqual(response.context['dest_cid'], 'C1')
        self.assertEqual(response.context['message'], 'TestMessage')
        # Check Alpine.js initial data for query params in x-data
        self.assertContains(response, f"&WONoDest=DWO1&DestPId=P1&DestCId=C1")

    def test_workorder_table_partial_view_get_no_search(self):
        """Tests HTMX load of the table with no specific search criteria."""
        response = self.client.get(self.table_url, {
            'WONoDest': 'DWO1', 'DestPId': 'P1', 'DestCId': 'C1' # Pass context params
        }, HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_design/workorder/_workorder_table.html')
        self.assertContains(response, 'WOVIEW1')
        self.assertContains(response, 'View Test Customer')
        self.assertContains(response, 'Mr. View User')
        # Check if passed query params are in context for link generation
        self.assertContains(response, 'WONoDest=DWO1')
        self.assertEqual(len(response.context['work_orders']), 1)

    def test_workorder_table_partial_view_get_with_search_enquiry(self):
        """Tests HTMX load of the table with Enquiry No search."""
        response = self.client.get(self.table_url, {
            'search_type': '1', 'search_text_general': 'ENQVIEW'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WOVIEW1')
        self.assertEqual(len(response.context['work_orders']), 1)

    def test_workorder_table_partial_view_get_with_search_customer_autocomplete(self):
        """Tests HTMX load of the table with Customer Name search (using hidden ID)."""
        response = self.client.get(self.table_url, {
            'search_type': '0',
            'search_text_customer_autocomplete': f'{self.customer.customer_name} [{self.customer.customer_id}]',
            'customer_id_hidden': self.customer.customer_id
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WOVIEW1')
        self.assertEqual(len(response.context['work_orders']), 1)

    def test_workorder_search_form_partial_view_get(self):
        """Tests HTMX load of the search form partial (on dropdown change)."""
        response = self.client.get(self.search_form_partial_url, {
            'search_type': '0', 'search_text_customer_autocomplete': 'TESTCUST', 'customer_id_hidden': 'TESTID'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_design/workorder/_search_form.html')
        # Check if Alpine.js x-data attributes are present and correctly reflect state
        self.assertContains(response, 'x-data="{ searchType: \'0\'')
        self.assertContains(response, 'x-show="searchType === \'0\'"') # Check for customer autocomplete field visibility
        self.assertContains(response, 'value="TESTID"') # Check hidden customer_id_hidden is preserved

    def test_customer_autocomplete_view_get_with_query(self):
        """Tests the customer autocomplete endpoint with a valid query."""
        Customer.objects.create(customer_id='CUSTAC', customer_name='Autocomplete Customer', comp_id=GLOBAL_COMPANY_ID, fin_year_id=GLOBAL_FINANCIAL_YEAR_ID)
        response = self.client.get(self.autocomplete_url, {'search_text_customer_autocomplete': 'Customer'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'View Test Customer [CUSTVIEW]')
        self.assertContains(response, 'Autocomplete Customer [CUSTAC]')
        self.assertContains(response, '<div id="autocomplete-results"') # Check outer container
        self.assertContains(response, 'type="button"') # Check suggestions are rendered as buttons

    def test_customer_autocomplete_view_get_no_query(self):
        """Tests the autocomplete endpoint with an empty query (should clear results)."""
        response = self.client.get(self.autocomplete_url, {'search_text_customer_autocomplete': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode('utf-8'), '<div id="autocomplete-results"></div>')

    def test_customer_autocomplete_view_get_no_results(self):
        """Tests the autocomplete endpoint when no matching results are found."""
        response = self.client.get(self.autocomplete_url, {'search_text_customer_autocomplete': 'NonExistentCustomer'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No results found')

    def test_bom_copy_tree_url(self):
        """Tests the placeholder URL for the BOM copy tree page."""
        # This test ensures the URL exists and resolves, even if it's a placeholder view.
        # In a real scenario, this would point to the actual migrated view.
        response = self.client.get(self.bom_copy_tree_url)
        self.assertEqual(response.status_code, 200) # Should be 200 as it points to WorkOrderListView
        self.assertTemplateUsed(response, 'bom_design/workorder/list.html') # As it's a placeholder
```

### Step 5: HTMX and Alpine.js Integration

The front-end interactions are fully managed by HTMX and Alpine.js, minimizing custom JavaScript and maintaining a clean, declarative approach.

*   **HTMX for Dynamic Content**:
    *   The primary `list.html` uses `hx-get` on a `div` (`#workOrderTable-container`) to load the entire table content from `{% url 'bom_design:workorder_table' %}`. This request is triggered on initial page `load`, on `submit` of the `workOrderSearchForm`, and on a custom `refreshWorkOrderList` event (useful for future needs, though not directly used by this page's CRUD, as it's just a "read" page).
    *   The `search_type` dropdown (`id="id_search_type"`) uses `hx-get` to `{% url 'bom_design:workorder_search_form_partial' %}` with `hx-target="#searchFormContent"` and `hx-swap="outerHTML"`. This ensures that when the search type changes, the *entire search form section* is re-rendered by Django (e.g., to adjust field IDs or attributes), and Alpine.js re-initializes on the new HTML.
    *   The "Search" button, when clicked, dispatches a custom `searchSubmit` event, which the `#workOrderTable-container` is listening for, triggering a table refresh.
    *   The customer autocomplete `input` field uses `hx-get` to `{% url 'bom_design:customer_autocomplete' %}`. `hx-trigger="keyup changed delay:500ms, search"` ensures requests are sent as the user types, with a debounce for performance. The `hx-target="#autocomplete-results"` ensures suggestions are dynamically swapped into the dedicated container.
    *   `hx-indicator` attributes are used on various elements to provide visual feedback (e.g., spinner) when HTMX requests are in progress.

*   **Alpine.js for UI State Management**:
    *   An `x-data` attribute is initialized on the main container `div` in `list.html`. This creates a local Alpine.js scope that holds the `searchType`, `searchTextGeneral`, `searchTextCustomer`, and `customerSearchId` variables.
    *   `x-model` directives are used on the form inputs to bind them to these Alpine.js variables, allowing two-way data binding.
    *   `x-show` directives are used on `search_text_general` and `search_text_customer_autocomplete` to toggle their visibility based on the `searchType` variable, replicating the original ASP.NET's dynamic input field visibility.
    *   `@input` and `@click` events on the autocomplete input reset the `customerSearchId` to ensure a fresh customer ID is captured from a new autocomplete selection.
    *   The `hx-on:click` attribute on the autocomplete suggestion buttons (rendered by HTMX) directly manipulates the DOM elements to update the `search_text_customer_autocomplete` input and the `customerSearchId` Alpine.js variable.

*   **DataTables for List Views**:
    *   The `_workorder_table.html` partial contains the `<table>` element with `id="workOrderTable"`.
    *   Crucially, in `list.html`, a JavaScript block within `{% block extra_js %}` listens for `htmx:afterSwap` events. This event fires after HTMX has successfully swapped in new content. When the `workOrderTable-container` is swapped, the `$(...).DataTable()` function is called to re-initialize DataTables on the newly loaded table, ensuring proper client-side searching, sorting, and pagination.
    *   The `pageLength` is set to `17` to match the original ASP.NET `GridView` configuration.

This structured migration plan ensures a seamless transition to a modern Django application, maintaining core functionality while significantly improving maintainability, performance, and user experience through declarative, lightweight frontend technologies.