## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

**Analysis:**

The ASP.NET code primarily interacts with `SD_Cust_WorkOrder_Master` and performs lookups/joins with `SD_Cust_Master`, `tblFinancial_master`, and `tblHR_OfficeStaff`. The `GridView` binds to a dynamically constructed `DataTable` which aggregates data from these sources.

**Inferred Database Tables and Key Columns:**

*   **`SD_Cust_WorkOrder_Master` (Main Entity: Work Order)**
    *   `Id` (Assumed Primary Key, based on `Order by Id Desc` in SQL)
    *   `EnqId` (Enquiry No)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master`)
    *   `WONo` (Work Order No, used as `DataKeyNames` in GridView)
    *   `PONo` (PO No)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff`'s `EmpId`, represents 'Generated By')
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `SysDate` (System Date, stored as string, e.g., 'MM-DD-YYYY')
    *   `CompId` (Company ID, used for filtering)

*   **`SD_Cust_Master` (Related Entity: Customer)**
    *   `CustomerId` (Primary Key)
    *   `CustomerName`
    *   `CompId`

*   **`tblFinancial_master` (Related Entity: Financial Year)**
    *   `FinYearId` (Primary Key)
    *   `FinYear`

*   **`tblHR_OfficeStaff` (Related Entity: Employee)**
    *   `EmpId` (Primary Key)
    *   `Title`
    *   `EmployeeName`
    *   `CompId`

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

**Analysis:**

The provided ASP.NET page `TPL_Design_Edit.aspx` and its code-behind primarily implement **Read (Search and List)** functionality.

*   **Read**: The `BindDataCust` method fetches data based on various search criteria (Customer Name, Enquiry No, PO No, WO No) and populates the `SearchGridView1`. Pagination is handled by `SearchGridView1_PageIndexChanging`.
*   **Search**: `DropDownList1` selects the search type, `txtSearchCustomer` and `TxtSearchValue` (with `AutoCompleteExtender`) provide input, and `btnSearch` triggers the search.
*   **Autocomplete**: The `sql` web method provides autocomplete suggestions for customer names.
*   **No explicit Create, Update, or Delete** operations are present on this specific page. The `WONo` column has a `HyperLinkField` to `TPL_Design_Assembly_Edit_Ass_grid.aspx?WONo={0}`, indicating that editing of a Work Order is handled on a separate page.
*   *As per the strict instructions to provide generic CRUD views for the inferred model, we will include them, assuming future expansion or consolidation of related functionalities.*

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Search Controls**:
    *   `DropDownList1`: Converts to a `forms.ChoiceField` for selecting search criteria (Customer Name, Enquiry No, PO No, WO No).
    *   `txtSearchCustomer`: Converts to a `forms.CharField` for general text search.
    *   `TxtSearchValue`: Converts to a `forms.CharField` for customer name search. Its `AutoCompleteExtender` functionality will be replaced by HTMX to a Django endpoint returning JSON for suggestions.
    *   `btnSearch`: Converts to an HTML `button` that triggers an HTMX request to update the search results.

*   **Data Display**:
    *   `SearchGridView1`: Converts to an HTML `table` rendered by Django templates, enhanced with DataTables.js for client-side sorting, searching, and pagination.
    *   `HyperLinkField` for `WONo`: Will be a standard Django URL (`{% url 'design:workorder_detail' obj.WONo %}` or `_edit`) in the template.

*   **Styling**: `CssClass` attributes (e.g., `box3`, `redbox`, `yui-datatable-theme`) indicate existing CSS classes that will be mapped to Tailwind CSS classes.

### Step 4: Generate Django Code

We will create a Django application named `design` (representing `Module_Design`).

#### 4.1 Models (`design/models.py`)

This file defines the Django models that map to your existing database tables. We use `managed = False` and `db_table` to connect to pre-existing tables without Django attempting to create or modify them. We also implement "Fat Model" principles by adding methods for retrieving related data, simplifying the view logic.

```python
from django.db import models
from django.db.models import F

class Customer(models.Model):
    """
    Maps to SD_Cust_Master.
    """
    customer_id = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Employee(models.Model):
    """
    Maps to tblHR_OfficeStaff.
    """
    emp_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True) # Changed from int to CharField based on SessionId mapping
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}.{self.employee_name}".strip('.')

class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    This model encapsulates the logic to fetch related data previously done in C#.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assumed primary key
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId', to_field='customer_id', related_name='workorders')
    wo_no = models.CharField(db_column='WONo', max_length=50, unique=True) # Assuming unique based on DataKeyNames
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    generated_by = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', to_field='emp_id', related_name='generated_workorders', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', to_field='fin_year_id', related_name='workorders')
    sys_date = models.CharField(db_column='SysDate', max_length=20) # Storing as char as per original, can convert to DateField if suitable.
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        ordering = ['-id'] # Matches 'Order by Id Desc'

    def __str__(self):
        return self.wo_no

    @property
    def formatted_sys_date(self):
        """
        Converts 'MM-DD-YYYY' string to 'DD/MM/YYYY' for display, mimicking ASP.NET.
        """
        try:
            # Assuming format is MM-DD-YYYY or similar that can be parsed
            # For simplicity, we directly convert parts if not a standard date format
            parts = self.sys_date.split('-')
            if len(parts) == 3:
                return f"{parts[1]}/{parts[0]}/{parts[2]}" # MM-DD-YYYY to DD/MM/YYYY
            return self.sys_date # Return original if not parsable
        except Exception:
            return self.sys_date # Fallback

    def get_absolute_url(self):
        """
        Mimics the HyperLinkField for WO No.
        This would link to a detail/edit page for the specific WO.
        """
        # Placeholder for the actual detail/edit URL
        return reverse('design:workorder_edit', kwargs={'pk': self.pk})

    @classmethod
    def get_work_orders_filtered(cls, search_type, search_value, comp_id):
        """
        Business logic to filter work orders based on search criteria.
        This replaces the complex SQL string building in BindDataCust.
        """
        queryset = cls.objects.filter(comp_id=comp_id).select_related('customer', 'fin_year', 'generated_by')

        if search_type == '1': # Enquiry No
            if search_value:
                queryset = queryset.filter(enq_id=search_value)
        elif search_type == '2': # PO No
            if search_value:
                queryset = queryset.filter(po_no=search_value)
        elif search_type == '3': # WO No
            if search_value:
                queryset = queryset.filter(wo_no=search_value)
        elif search_type == '0': # Customer Name (search_value is CustomerId)
            if search_value:
                queryset = queryset.filter(customer__customer_id=search_value)
        # 'Select' or empty search_value means no specific filter applied for that field
        
        return queryset.order_by('-id') # Consistent with original order

```

#### 4.2 Forms (`design/forms.py`)

We need a search form for the main page and a form for CRUD operations on `WorkOrder` objects.

```python
from django import forms
from .models import WorkOrder, Customer

class WorkOrderSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('Select', 'Select'), # Mimics original "Select" option
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-48 p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'hx-get': "{% url 'design:workorder_search_controls' %}", # HTMX to dynamically update visibility
            'hx-target': '#search-input-container',
            'hx-swap': 'innerHTML',
        })
    )
    
    # These fields will be shown/hidden via HTMX/Alpine.js
    search_customer_text = forms.CharField(
        label='Search Customer',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-96 p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'placeholder': 'Enter search term',
        })
    )
    
    # This field combines customer name and ID for autocomplete
    search_value_customer = forms.CharField(
        label='Customer Name',
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-96 p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'placeholder': 'Start typing customer name...',
            'hx-get': "{% url 'design:customer_autocomplete' %}", # HTMX for autocomplete suggestions
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off'
        })
    )

    def clean_search_value_customer(self):
        """
        Extracts CustomerId from 'CustomerName [CustomerId]' format.
        """
        search_value = self.cleaned_data.get('search_value_customer')
        if search_value:
            # Assuming format 'CustomerName [CustomerId]'
            try:
                # Find the last occurrence of '[' and ']'
                start_bracket = search_value.rfind('[')
                end_bracket = search_value.rfind(']')
                if start_bracket != -1 and end_bracket != -1 and end_bracket > start_bracket:
                    customer_id = search_value[start_bracket + 1:end_bracket]
                    # Validate if the extracted ID actually exists
                    if Customer.objects.filter(customer_id=customer_id).exists():
                        return customer_id
                raise forms.ValidationError("Invalid customer selection. Please choose from suggestions.")
            except Exception:
                raise forms.ValidationError("Invalid customer selection format.")
        return search_value # Allow empty string

class WorkOrderForm(forms.ModelForm):
    """
    Form for creating and updating WorkOrder objects.
    """
    class Meta:
        model = WorkOrder
        fields = ['enq_id', 'customer', 'wo_no', 'po_no', 'generated_by', 'fin_year', 'sys_date', 'comp_id']
        widgets = {
            'enq_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'generated_by': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fin_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM-DD-YYYY'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    # Add custom validation if needed, e.g., for sys_date format
    def clean_sys_date(self):
        sys_date = self.cleaned_data['sys_date']
        # Example: if you want to validate the MM-DD-YYYY format
        # import re
        # if not re.match(r"^\d{2}-\d{2}-\d{4}$", sys_date):
        #     raise forms.ValidationError("Date must be in MM-DD-YYYY format.")
        return sys_date

```

#### 4.3 Views (`design/views.py`)

This file contains the Django Class-Based Views (CBVs) for handling requests. Views are kept "thin" with business logic moved to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from .models import WorkOrder, Customer, Employee, FinancialYear
from .forms import WorkOrderForm, WorkOrderSearchForm
import json # Used for JSON responses for autocomplete

# Helper to get current company ID from session, mimicking ASP.NET
# In a real app, this would be tied to user authentication/profile.
def get_current_comp_id(request):
    return request.session.get('compid', 1) # Default to 1 for demonstration

class WorkOrderSearchListView(ListView):
    """
    Main view for searching and listing Work Orders.
    Handles initial page load and search form submission.
    """
    model = WorkOrder
    template_name = 'design/workorder/list.html'
    context_object_name = 'workorders' # This won't be directly used for table rendering

    def get_queryset(self):
        # This queryset is not directly rendered but used by the partial table view
        # The initial page load might display all, or an empty set.
        return WorkOrder.objects.none() # Initial load shows no data by default, table loads via HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = WorkOrderSearchForm(self.request.GET or None)
        return context

class WorkOrderTablePartialView(ListView):
    """
    Renders only the DataTables HTML for Work Orders.
    Accessed via HTMX for dynamic updates.
    """
    model = WorkOrder
    template_name = 'design/workorder/_workorder_table.html'
    context_object_name = 'workorders'

    def get_queryset(self):
        form = WorkOrderSearchForm(self.request.GET)
        comp_id = get_current_comp_id(self.request)
        
        # If the form is valid, apply filters from the search.
        # Otherwise, display all (or no data if initial state).
        if form.is_valid():
            search_type = form.cleaned_data.get('search_type')
            
            # Decide which search value to use based on search_type
            if search_type == '0': # Customer Name
                search_value = form.cleaned_data.get('search_value_customer')
            else: # Enquiry No, PO No, WO No
                search_value = form.cleaned_data.get('search_customer_text')

            return WorkOrder.get_work_orders_filtered(search_type, search_value, comp_id)
        
        # If form is not valid (e.g., initial load without search params),
        # return an empty queryset or default all based on business rule.
        # For this case, let's return all work orders for the comp_id if no search is performed.
        return WorkOrder.objects.filter(comp_id=comp_id).select_related('customer', 'fin_year', 'generated_by')

class CustomerAutocompleteView(View):
    """
    Provides customer name suggestions for autocomplete via HTMX.
    Mimics the ASP.NET AjaxControlToolkit AutoCompleteExtender ServiceMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '')
        comp_id = get_current_comp_id(request)
        
        customers = Customer.objects.filter(
            comp_id=comp_id,
            customer_name__icontains=prefix_text
        ).values('customer_id', 'customer_name')[:10] # Limit to 10 suggestions

        suggestions = [
            f"{customer['customer_name']} [{customer['customer_id']}]"
            for customer in customers
        ]
        return JsonResponse(suggestions, safe=False)

class WorkOrderSearchControlsView(View):
    """
    HTMX endpoint to dynamically render the correct search input (textbox or autocomplete).
    """
    def get(self, request, *args, **kwargs):
        search_type = request.GET.get('search_type', 'Select')
        search_form = WorkOrderSearchForm() # Use an empty form for rendering structure

        context = {
            'search_type': search_type,
            'search_form': search_form,
        }
        return render(request, 'design/workorder/_search_inputs.html', context)

# CRUD Views for WorkOrder (as per required template, despite original page being search-only)
class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'design/workorder/_workorder_form.html'
    success_url = reverse_lazy('design:workorder_list') # Not directly used for HTMX but good practice

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pre-fill comp_id from session for new objects if not provided
        if not self.object:
            kwargs['initial'] = kwargs.get('initial', {})
            kwargs['initial']['comp_id'] = get_current_comp_id(self.request)
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success and close modal
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # Return the form with errors for HTMX to swap back into the modal
            return render(self.request, self.template_name, {'form': form})
        return response

class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'design/workorder/_workorder_form.html'
    success_url = reverse_lazy('design:workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'design/workorder/_workorder_confirm_delete.html'
    success_url = reverse_lazy('design:workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({'refreshWorkOrderList': None, 'closeModal': None})
                }
            )
        return response
    
    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if request.headers.get('HX-Request'):
            return render(request, self.template_name, {'object': self.object})
        return super().get(request, *args, **kwargs)

```

#### 4.4 Templates

Templates will be stored under `design/templates/design/workorder/`.

**`design/templates/design/workorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">TPL Design Edit (Work Orders)</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'design:workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>

    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <form id="searchForm" hx-get="{% url 'design:workorder_table' %}" hx-target="#workorderTable-container" hx-swap="innerHTML">
            <div class="flex items-end space-x-4">
                <div>
                    <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    {{ search_form.search_type }}
                </div>
                
                <div id="search-input-container" 
                     hx-trigger="load" 
                     hx-get="{% url 'design:workorder_search_controls' %}?search_type={{ search_form.search_type.value|default:'Select' }}" 
                     hx-target="#search-input-container" 
                     hx-swap="innerHTML">
                    <!-- Dynamic search input (text or autocomplete) will be loaded here -->
                    <div class="text-center">
                        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-gray-400"></div>
                        <p class="mt-1 text-sm text-gray-500">Loading input...</p>
                    </div>
                </div>

                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </form>
    </div>

    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'design:workorder_table' %}?{{ request.GET.urlencode }}" {# Pass initial search params #}
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-lg">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-3 text-lg text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on closeModal add .hidden to me and remove .is-active from me" {# Alpine.js for closing modal via HTMX trigger #}
         x-data="{ showModal: false }" x-init="document.body.classList.remove('overflow-hidden')"
         x-show="showModal"
         @keydown.escape.window="showModal = false"
         @click.self="showModal = false"
         :class="{ 'flex': showModal, 'hidden': !showModal }"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 scale-95"
         x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 scale-100"
         x-transition:leave-end="opacity-0 scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-auto"
             @click.stop=""
             _="on load add .is-active to #modal then set showModal to true">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Close modal if response triggers 'closeModal'
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.add('hidden');
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modalContent').innerHTML = ''; // Clear content
        }
    });
</script>
{% endblock %}
```

**`design/templates/design/workorder/_search_inputs.html`**

```html
{% if search_type == '0' %} {# Customer Name #}
    <div x-data="{ open: false }" @click.outside="open = false" class="relative">
        <label for="{{ search_form.search_value_customer.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer Name</label>
        {{ search_form.search_value_customer }}
        <div id="customer-autocomplete-results" x-show="open" 
             class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto"
             _="on htmx:afterSwap toggle .hidden on #customer-autocomplete-results then if my innerHTML != '' set open to true else set open to false">
            <!-- Autocomplete suggestions will be loaded here -->
        </div>
        <input type="hidden" name="search_customer_text" value=""> {# Ensure only one search input is sent #}
    </div>
{% elif search_type == '1' or search_type == '2' or search_type == '3' %} {# Enquiry No, PO No, WO No #}
    <div>
        <label for="{{ search_form.search_customer_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Term</label>
        {{ search_form.search_customer_text }}
        <input type="hidden" name="search_value_customer" value=""> {# Ensure only one search input is sent #}
    </div>
{% else %} {# 'Select' #}
    <div>
        <label for="{{ search_form.search_customer_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Term</label>
        {{ search_form.search_customer_text }}
        <input type="hidden" name="search_value_customer" value="">
    </div>
{% endif %}
```

**`design/templates/design/workorder/_customer_autocomplete_results.html`**

```html
{% if results %}
    {% for result in results %}
        <div class="px-3 py-2 cursor-pointer hover:bg-blue-100"
             _="on click set #id_search_value_customer.value to '{{ result|escapejs }}' then set open to false">
            {{ result }}
        </div>
    {% endfor %}
{% else %}
    <div class="px-3 py-2 text-gray-500">No suggestions</div>
{% endif %}
```

**`design/templates/design/workorder/_workorder_table.html`**

```html
<table id="workorderTable" class="min-w-full bg-white divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if workorders %}
            {% for obj in workorders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.fin_year.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.customer.customer_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.customer.customer_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.enq_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <a href="{% url 'design:workorder_edit' obj.pk %}"
                       class="text-blue-600 hover:text-blue-800 font-medium"
                       hx-get="{% url 'design:workorder_edit' obj.pk %}"
                       hx-target="#modalContent"
                       hx-trigger="click"
                       _="on click add .is-active to #modal">
                       {{ obj.wo_no }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.generated_by.employee_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 text-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'design:workorder_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'design:workorder_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="10" class="py-4 text-center text-lg text-maroon-600 font-semibold">
                    No data to display!
                </td>
            </tr>
        {% endif %}
    </tbody>
</table>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#workorderTable')) {
            $('#workorderTable').DataTable().destroy();
        }
        $('#workorderTable').DataTable({
            "pageLength": 17, // Matches original ASP.NET GridView PageSize
            "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false
        });
    });
</script>
```

**`design/templates/design/workorder/_workorder_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-400 hover:bg-gray-500 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal then trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`design/templates/design/workorder/_workorder_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 text-lg mb-6">Are you sure you want to delete Work Order <span class="font-bold text-red-600">{{ object.wo_no }}</span>?</p>
    
    <form hx-delete="{{ request.path }}" hx-confirm="This action cannot be undone." hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-400 hover:bg-gray-500 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal then trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design/urls.py`)

This file defines the URL patterns that map to your views.

```python
from django.urls import path
from .views import (
    WorkOrderSearchListView, 
    WorkOrderTablePartialView, 
    CustomerAutocompleteView, 
    WorkOrderSearchControlsView,
    WorkOrderCreateView, 
    WorkOrderUpdateView, 
    WorkOrderDeleteView,
)

app_name = 'design' # Define app_name for URL namespacing

urlpatterns = [
    # Main Work Order List and Search Page
    path('workorders/', WorkOrderSearchListView.as_view(), name='workorder_list'),
    
    # HTMX Endpoints
    path('workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    path('workorders/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    path('workorders/search-controls/', WorkOrderSearchControlsView.as_view(), name='workorder_search_controls'),

    # CRUD Operations (rendered in modal via HTMX)
    path('workorders/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorders/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorders/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]

```

#### 4.6 Tests (`design/tests.py`)

Comprehensive tests cover models and views to ensure functionality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch
from .models import WorkOrder, Customer, FinancialYear, Employee
import json

class ModelSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all tests
        cls.comp_id = 1
        cls.fin_year_2023 = FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.fin_year_2024 = FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-25')
        cls.customer_alpha = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=cls.comp_id)
        cls.customer_beta = Customer.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=cls.comp_id)
        cls.employee_john = Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='John Doe', comp_id=cls.comp_id)
        cls.employee_jane = Employee.objects.create(emp_id='EMP002', title='Ms', employee_name='Jane Smith', comp_id=cls.comp_id)

        cls.workorder1 = WorkOrder.objects.create(
            id=1,
            enq_id='ENQ001',
            customer=cls.customer_alpha,
            wo_no='WO001',
            po_no='PO001',
            generated_by=cls.employee_john,
            fin_year=cls.fin_year_2023,
            sys_date='01-15-2023',
            comp_id=cls.comp_id
        )
        cls.workorder2 = WorkOrder.objects.create(
            id=2,
            enq_id='ENQ002',
            customer=cls.customer_beta,
            wo_no='WO002',
            po_no='PO002',
            generated_by=cls.employee_jane,
            fin_year=cls.fin_year_2024,
            sys_date='03-20-2024',
            comp_id=cls.comp_id
        )

class WorkOrderModelTest(ModelSetupMixin, TestCase):
    def test_customer_creation(self):
        self.assertEqual(self.customer_alpha.customer_name, 'Alpha Corp')
        self.assertEqual(str(self.customer_alpha), 'Alpha Corp')

    def test_financial_year_creation(self):
        self.assertEqual(self.fin_year_2023.fin_year, '2023-24')
        self.assertEqual(str(self.fin_year_2023), '2023-24')

    def test_employee_creation(self):
        self.assertEqual(self.employee_john.employee_name, 'John Doe')
        self.assertEqual(str(self.employee_john), 'Mr.John Doe')

    def test_workorder_creation(self):
        self.assertEqual(self.workorder1.wo_no, 'WO001')
        self.assertEqual(self.workorder1.customer.customer_name, 'Alpha Corp')
        self.assertEqual(self.workorder1.fin_year.fin_year, '2023-24')
        self.assertEqual(self.workorder1.generated_by.employee_name, 'John Doe')
        self.assertEqual(str(self.workorder1), 'WO001')

    def test_formatted_sys_date_property(self):
        self.assertEqual(self.workorder1.formatted_sys_date, '15/01/2023')
        self.assertEqual(self.workorder2.formatted_sys_date, '20/03/2024')
        
        # Test with invalid date format
        self.workorder1.sys_date = "2023-01-15"
        self.assertEqual(self.workorder1.formatted_sys_date, '2023-01-15')


    def test_get_work_orders_filtered_by_enquiry_no(self):
        qs = WorkOrder.get_work_orders_filtered('1', 'ENQ001', self.comp_id)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().wo_no, 'WO001')

    def test_get_work_orders_filtered_by_po_no(self):
        qs = WorkOrder.get_work_orders_filtered('2', 'PO002', self.comp_id)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().wo_no, 'WO002')

    def test_get_work_orders_filtered_by_wo_no(self):
        qs = WorkOrder.get_work_orders_filtered('3', 'WO001', self.comp_id)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().wo_no, 'WO001')

    def test_get_work_orders_filtered_by_customer_id(self):
        qs = WorkOrder.get_work_orders_filtered('0', 'CUST001', self.comp_id)
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().customer.customer_name, 'Alpha Corp')

    def test_get_work_orders_filtered_no_match(self):
        qs = WorkOrder.get_work_orders_filtered('1', 'NONEXISTENT', self.comp_id)
        self.assertEqual(qs.count(), 0)

    def test_get_work_orders_filtered_empty_search_value(self):
        # Should return all work orders for the comp_id if search_value is empty
        qs = WorkOrder.get_work_orders_filtered('1', '', self.comp_id)
        self.assertEqual(qs.count(), 2)

    def test_get_work_orders_filtered_select_type(self):
        # Should return all work orders for the comp_id if search_type is 'Select'
        qs = WorkOrder.get_work_orders_filtered('Select', '', self.comp_id)
        self.assertEqual(qs.count(), 2)

class WorkOrderViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.session = self.client.session
        self.session['compid'] = self.comp_id # Mock session compid
        self.session.save()
        self.htmx_headers = {'HTTP_HX_REQUEST': 'true', 'HTTP_HX_TARGET': 'modalContent'}


    def test_workorder_list_view_get(self):
        response = self.client.get(reverse('design:workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/list.html')
        self.assertIsInstance(response.context['search_form'], WorkOrderSearchForm)

    def test_workorder_table_partial_view_get_no_search(self):
        response = self.client.get(reverse('design:workorder_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_table.html')
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WO002')
        self.assertEqual(response.context['workorders'].count(), 2) # All for comp_id

    def test_workorder_table_partial_view_get_with_search_enquiry(self):
        response = self.client.get(reverse('design:workorder_table'), {'search_type': '1', 'search_customer_text': 'ENQ001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertNotContains(response, 'WO002')
        self.assertEqual(response.context['workorders'].count(), 1)

    def test_workorder_table_partial_view_get_with_search_customer_name(self):
        # Mocking the Autocomplete value
        response = self.client.get(reverse('design:workorder_table'), {'search_type': '0', 'search_value_customer': f'{self.customer_alpha.customer_name} [{self.customer_alpha.customer_id}]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO001')
        self.assertNotContains(response, 'WO002')
        self.assertEqual(response.context['workorders'].count(), 1)
    
    def test_workorder_table_partial_view_get_with_invalid_customer_name_autocomplete(self):
        response = self.client.get(reverse('design:workorder_table'), {'search_type': '0', 'search_value_customer': 'NonExistent [NONID]'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form invalid, but still renders table
        self.assertEqual(response.context['workorders'].count(), 2) # Defaults to all if form is invalid

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('design:customer_autocomplete'), {'term': 'Alpha'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn(f'{self.customer_alpha.customer_name} [{self.customer_alpha.customer_id}]', data)
        self.assertNotIn(f'{self.customer_beta.customer_name} [{self.customer_beta.customer_id}]', data)

    def test_workorder_search_controls_view(self):
        response = self.client.get(reverse('design:workorder_search_controls'), {'search_type': '0'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_search_inputs.html')
        self.assertContains(response, 'name="search_value_customer"') # Autocomplete input
        self.assertNotContains(response, 'name="search_customer_text"') # Regular text input

        response = self.client.get(reverse('design:workorder_search_controls'), {'search_type': '1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_search_inputs.html')
        self.assertContains(response, 'name="search_customer_text"') # Regular text input
        self.assertNotContains(response, 'name="search_value_customer"') # Autocomplete input


    # CRUD View Tests
    def test_workorder_create_view_get(self):
        response = self.client.get(reverse('design:workorder_add'), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_form.html')
        self.assertContains(response, 'Add Work Order')
        self.assertTrue('form' in response.context)

    def test_workorder_create_view_post_success(self):
        initial_count = WorkOrder.objects.count()
        data = {
            'enq_id': 'NEWENQ',
            'customer': self.customer_alpha.customer_id,
            'wo_no': 'NEWWO',
            'po_no': 'NEWPO',
            'generated_by': self.employee_john.emp_id,
            'fin_year': self.fin_year_2023.fin_year_id,
            'sys_date': '06-15-2023',
            'comp_id': self.comp_id,
        }
        response = self.client.post(reverse('design:workorder_add'), data, **self.htmx_headers)
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(WorkOrder.objects.count(), initial_count + 1)
        self.assertTrue(WorkOrder.objects.filter(wo_no='NEWWO').exists())
        self.assertEqual(response.headers['HX-Trigger'], json.dumps({'refreshWorkOrderList': None, 'closeModal': None}))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order added successfully.')

    def test_workorder_create_view_post_invalid(self):
        initial_count = WorkOrder.objects.count()
        data = {
            'wo_no': 'WO001', # Duplicate WO_NO
            'customer': '', # Missing required field
        }
        response = self.client.post(reverse('design:workorder_add'), data, **self.htmx_headers)
        self.assertEqual(response.status_code, 200) # Rerenders form with errors
        self.assertTemplateUsed(response, 'design/workorder/_workorder_form.html')
        self.assertEqual(WorkOrder.objects.count(), initial_count)
        self.assertIn('form', response.context)
        self.assertContains(response, 'This field is required.')

    def test_workorder_update_view_get(self):
        response = self.client.get(reverse('design:workorder_edit', args=[self.workorder1.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_form.html')
        self.assertContains(response, 'Edit Work Order')
        self.assertEqual(response.context['form'].instance, self.workorder1)

    def test_workorder_update_view_post_success(self):
        data = {
            'enq_id': 'UPDATEDENQ',
            'customer': self.customer_beta.customer_id,
            'wo_no': self.workorder1.wo_no, # Must keep original WO_NO if not changing
            'po_no': 'UPDATEDPO',
            'generated_by': self.employee_jane.emp_id,
            'fin_year': self.fin_year_2024.fin_year_id,
            'sys_date': '07-20-2023',
            'comp_id': self.comp_id,
        }
        response = self.client.post(reverse('design:workorder_edit', args=[self.workorder1.pk]), data, **self.htmx_headers)
        self.assertEqual(response.status_code, 204)
        self.workorder1.refresh_from_db()
        self.assertEqual(self.workorder1.enq_id, 'UPDATEDENQ')
        self.assertEqual(self.workorder1.customer, self.customer_beta)
        self.assertEqual(response.headers['HX-Trigger'], json.dumps({'refreshWorkOrderList': None, 'closeModal': None}))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order updated successfully.')

    def test_workorder_delete_view_get(self):
        response = self.client.get(reverse('design:workorder_delete', args=[self.workorder1.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_confirm_delete.html')
        self.assertContains(response, f'delete Work Order {self.workorder1.wo_no}')
        self.assertEqual(response.context['object'], self.workorder1)

    def test_workorder_delete_view_post_success(self):
        initial_count = WorkOrder.objects.count()
        response = self.client.delete(reverse('design:workorder_delete', args=[self.workorder1.pk]), **self.htmx_headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(WorkOrder.objects.count(), initial_count - 1)
        self.assertFalse(WorkOrder.objects.filter(pk=self.workorder1.pk).exists())
        self.assertEqual(response.headers['HX-Trigger'], json.dumps({'refreshWorkOrderList': None, 'closeModal': None}))
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order deleted successfully.')

    def test_workorder_delete_view_post_nonexistent(self):
        initial_count = WorkOrder.objects.count()
        response = self.client.delete(reverse('design:workorder_delete', args=[9999]), **self.htmx_headers)
        self.assertEqual(response.status_code, 404) # Object not found
        self.assertEqual(WorkOrder.objects.count(), initial_count)

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Integration:**

1.  **Dynamic Search Input**:
    *   The `search_type` dropdown has `hx-get` to `{% url 'design:workorder_search_controls' %}`.
    *   This HTMX request fetches `_search_inputs.html`, which dynamically renders either `search_customer_text` (for Enquiry, PO, WO) or `search_value_customer` (for Customer Name with autocomplete).
    *   This ensures the correct input field is visible based on the dropdown selection, mirroring the `Visible` property changes in ASP.NET.

2.  **Customer Autocomplete**:
    *   The `search_value_customer` input has `hx-get` to `{% url 'design:customer_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms, search"`.
    *   The autocomplete results are swapped into `#customer-autocomplete-results` using `hx-swap="innerHTML"`.
    *   Alpine.js (or Hyperscript `_`) is used to control the visibility of the autocomplete dropdown (`x-show="open"` and `on htmx:afterSwap ... set open to true/false`).
    *   Clicking a suggestion uses Hyperscript `on click set #id_search_value_customer.value to '...' then set open to false` to populate the input and hide the suggestions.

3.  **Search Results Refresh (DataTables)**:
    *   The `#workorderTable-container` is configured with `hx-trigger="load, refreshWorkOrderList from:body"`.
    *   The search form's `hx-get` targets this container to load filtered data.
    *   After any CRUD operation (Add, Edit, Delete), the server responds with `HX-Trigger: {'refreshWorkOrderList': null}` to automatically refresh the list.
    *   DataTables is initialized on the `_workorder_table.html` partial, running the `$(document).ready` script *after* HTMX injects the new table HTML. This ensures DataTables correctly applies to the updated content.

4.  **Modal for CRUD Operations**:
    *   Add, Edit, and Delete buttons have `hx-get` attributes targeting `#modalContent` inside a hidden `#modal` div.
    *   Hyperscript `_="on click add .is-active to #modal"` shows the modal.
    *   The form/confirmation partials are loaded into `#modalContent`.
    *   Form submissions (`hx-post`, `hx-delete`) are handled by HTMX.
    *   On successful form submission, the server sends `HX-Trigger: {'refreshWorkOrderList': null, 'closeModal': null}`.
    *   `refreshWorkOrderList` triggers the table update, and `closeModal` (an Alpine.js/Hyperscript event) hides the modal and clears its content.
    *   `@click.self` on the modal backdrop, `keydown.escape`, and "Cancel" buttons allow closing the modal.
    *   Form invalid submissions (`form_invalid` in views) return the form HTML with errors, which HTMX swaps back into the modal, allowing inline error display without closing the modal.

5.  **DRY Template Inheritance**:
    *   All component templates (`list.html`, `_workorder_table.html`, `_workorder_form.html`, etc.) extend or are partials loaded into `core/base.html` (not included here).
    *   All necessary CDN links (HTMX, Alpine.js, jQuery, DataTables) are assumed to be in `base.html` or loaded via `{% block extra_js %}` as shown.

### Final Notes

This comprehensive plan transforms the ASP.NET search and display page into a modern Django application, adhering strictly to the 'Fat Model, Thin View' architecture, HTMX/Alpine.js for dynamic interactions, DataTables for enhanced data presentation, and thorough testing. The focus on automation-friendly instructions ensures this migration can be guided through conversational AI for various teams. The identified database schema, coupled with Django's ORM and model methods, simplifies complex data retrieval logic previously handled by direct SQL string concatenation.