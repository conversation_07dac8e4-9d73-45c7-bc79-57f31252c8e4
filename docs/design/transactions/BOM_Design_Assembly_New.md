## ASP.NET to Django Conversion Script: BOM Assembly - New

This modernization plan outlines the strategic transition of your ASP.NET BOM Assembly module to a robust, modern Django application. We will leverage Django's powerful ORM, Class-Based Views, and a dynamic frontend stack featuring HTMX and Alpine.js, ensuring a seamless user experience without full page reloads. Our focus is on automation-driven migration, emphasizing maintainability, scalability, and enhanced performance, all while adhering to the 'fat model, thin view' paradigm and strict code quality standards.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists as `core/base.html`.
- Focus ONLY on component-specific code for the current module (`design` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**

The ASP.NET code primarily interacts with three main tables and one lookup view:
1.  **`tblDG_BOM_Master`**: This is the core Bill of Material (BOM) assembly table.
    *   **Columns:** `Id` (Primary Key), `CId` (likely a sequence/child ID for BOM), `PId` (Parent ID, typically '0' for top-level assemblies as used here), `EquipmentNo`, `UnitNo`, `PartNo`, `WONo`, `ItemId` (Foreign Key to `tblDG_Item_Master`), `Qty` (Decimal/Float), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`.
2.  **`tblDG_Item_Master`**: Stores details about each item/component in the BOM.
    *   **Columns:** `Id` (Primary Key), `ItemCode` (Generated: `EquipNo-UnitNo-PartNo0`), `ManfDesc` (Manufacturer Description), `UOMBasic` (Foreign Key to `Unit_Master`), `FileName`, `FileSize`, `ContentType`, `FileData` (Drawing/Image binary data), `AttName`, `AttSize`, `AttContentType`, `AttData` (Specification Sheet binary data), `PartNo` (Generated: `EquipNo-UnitNo-PartNo`), `Process` (always '0' in this context), `OpeningBalDate`, `OpeningBalQty` (always '0'), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`.
3.  **`Unit_Master`**: A lookup table for Units of Measure (UOM).
    *   **Columns:** `Id` (Primary Key), `Symbol` (UOM symbol like 'KG', 'PCS').
4.  **`SD_Cust_WorkOrder_Master`**: Used to mark a Work Order as updated.
    *   **Columns:** `WONo` (Work Order Number), `CompId` (Company ID), `UpdateWO` (Boolean/Flag).
5.  **`vw_Unit_Master`**: A view used by `SqlDataSource1` to populate the UOM dropdown. Functionally, it maps to `Unit_Master`.

**Inferred Relationships:**
*   `tblDG_BOM_Master.ItemId` -> `tblDG_Item_Master.Id`
*   `tblDG_Item_Master.UOMBasic` -> `Unit_Master.Id`

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic from the ASP.NET code.

**Functionality Breakdown:**

*   **Read (R):**
    *   **Listing BOM Items:** The `GridView2` populates a list of BOM items associated with a specific Work Order (`WONo`). The query filters by `WONo`, `PId='0'`, `CompId`, `FinYearId`, and joins `tblDG_Item_Master` and `Unit_Master`.
    *   **Displaying WO No:** `lblWo` displays the Work Order number passed via query string.
    *   **UOM Dropdown:** `DDLUnitBasic` loads UOM symbols from `vw_Unit_Master`.
    *   **File Download Links:** `HyperLink1` and `HyperLink2` generate links to download `Drw/Image` and `Spec. Sheet` files based on `ItemId`.
*   **Create (C):**
    *   **Adding New BOM Items:** The core functionality resides in `GridView2_RowCommand` (commands "Insert" and "Insert1"). This process is complex:
        1.  **Generate `EquipmentNo`**: Retrieves the latest `EquipmentNo` from `tblDG_BOM_Master` and increments it (e.g., `00001`, `00002`). This requires database query for sequencing.
        2.  **Generate `ItemCode` and `PartNo`**: Based on the generated `EquipmentNo` and user input `UnitNo` and `PartNo`.
        3.  **Validate `ItemCode` Length**: Checks `ItemCode` length against a company-specific configuration (`fun.ItemCodeLimit`).
        4.  **Check `ItemCode` Existence**: Verifies if the generated `ItemCode` already exists in `tblDG_Item_Master` for the given `CompId` and `FinYearId`.
        5.  **Validate Quantity**: Ensures the input quantity is a valid number.
        6.  **Handle File Uploads**: `DrwUpload` (drawing/image) and `OtherUpload` (spec sheet) are processed, their binary data, filenames, sizes, and content types are stored in `tblDG_Item_Master`.
        7.  **Insert into `tblDG_Item_Master`**: Creates a new item record with all details, including system metadata (`SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`), `OpeningBalDate`, `OpeningBalQty`.
        8.  **Get New `ItemId`**: Retrieves the `Id` of the newly created `tblDG_Item_Master` record.
        9.  **Generate `CId`**: Retrieves the next `CId` (Child ID) for the `WONo`, `CompId`, `FinYearId` from `tblDG_BOM_Master`.
        10. **Insert into `tblDG_BOM_Master`**: Creates a new BOM assembly record linking the `WONo`, generated `EquipmentNo`, user input `UnitNo`, `PartNo`, the new `ItemId`, `Qty`, `CId`, and `PId='0'`.
        11. **Update `SD_Cust_WorkOrder_Master`**: Sets the `UpdateWO` flag to '1' for the associated Work Order.
*   **Update (U):** No explicit update functionality for existing rows is present in the provided code (only "Insert" commands are handled).
*   **Delete (D):** No explicit delete functionality is present.
*   **Validation Logic:**
    *   `RequiredFieldValidator` for `txtUnitNo`, `txtPartNo`, `txtManfDescription`, `txtQuntity`.
    *   `RegularExpressionValidator` for `txtQuntity` (`^\d{1,15}(\.\d{0,3})?$`).
    *   Custom C# validation for `ItemCode` length and existence.
    *   Custom C# validation for quantity format (`fun.NumberValidationQty`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django template mapping.

**UI Component Mapping:**

*   **`lblWo` (Label):** Will be displayed directly in the Django template, populated from context.
*   **`GridView2` (Data Grid):** This will be replaced by a DataTables instance in a Django template, populated via HTMX from a partial view.
    *   **Columns:**
        *   `SN`: Django `forloop.counter` in template.
        *   `Id` (Hidden): Primary key, used for edit/delete actions (though no edit/delete is shown).
        *   `Equipment No`, `Unit No`, `Part No/SN`, `Description`, `UOM`, `Qty`: Displayed as table cells.
        *   `Drw/Image`, `Spec. Sheet`: Displayed as Hyperlinks for download; will be rendered as download buttons in Django, calling a file serving view.
    *   **Inline Add Form (FooterTemplate/EmptyDataTemplate):** These input fields (`txtUnitNo`, `txtPartNo`, `txtManfDescription`, `DDLUnitBasic`, `txtQuntity`, `DrwUpload`, `OtherUpload`) will be combined into a single Django form (`BomAssemblyForm`) loaded into an HTMX modal.
        *   `asp:TextBox` -> `forms.TextInput`
        *   `asp:DropDownList` -> `forms.Select`
        *   `asp:FileUpload` -> `forms.FileField`
        *   `asp:Button` -> `button` with HTMX attributes.
*   **`btncancel` (Button):** A simple `a` tag or `button` with HTMX/JavaScript to navigate or close a modal.
*   **`lblMsg1`, `lblMsg` (Labels):** Django's `messages` framework will handle success/error notifications, displayed globally or through HTMX events.
*   **Client-side `alert()` calls:** Replaced by Django's `messages` framework for success/error feedback, potentially enhanced with `HX-Trigger` to show toast notifications via Alpine.js.

### Step 4: Generate Django Code

We will create a new Django app named `design` to house this module.

#### 4.1 Models (`design/models.py`)

This file will define the Django models that map to your existing database tables. We'll include custom managers and methods to encapsulate the business logic identified in Step 2, adhering to the "fat model" principle.

```python
from django.db import models, transaction
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.utils.html import format_html
import math
import os

# --- Helper Models (Assumed to exist in a 'core' app or similar) ---
# For demonstration, simplified versions are included here.
# In a real system, these would be in 'core' or 'accounts' app.

class Company(models.Model):
    id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255, db_column='CompName')
    # Assuming there's a field for item code length limit
    item_code_length_limit = models.IntegerField(db_column='ItemCodeLengthLimit', default=10)

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Placeholder name, adjust as per actual DB
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_name = models.CharField(max_length=50, db_column='FinYearName')
    start_date = models.DateField(db_column='OpeningBalDate') # Assuming this is where opening balance date is stored

    class Meta:
        managed = False
        db_table = 'tblFinancialYear' # Placeholder name, adjust as per actual DB
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class CustomUser(models.Model): # Represents SessionId/username
    id = models.IntegerField(primary_key=True, db_column='SessionId')
    username = models.CharField(max_length=100) # Corresponds to Session["username"]

    class Meta:
        managed = False
        db_table = 'tblUsers' # Placeholder name, adjust as per actual DB
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return self.username

class WorkOrder(models.Model):
    won_o = models.CharField(max_length=50, db_column='WONo', primary_key=True) # Primary key as WONo
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='work_orders')
    update_wo = models.BooleanField(db_column='UpdateWO', default=False)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.won_o

    def mark_as_updated(self):
        """Marks the work order as updated."""
        self.update_wo = True
        self.save(update_fields=['update_wo'])
        return True # Return True for success, if needed for validation/chaining


# --- Main Application Models ---

class UnitManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().order_by('symbol')

class Unit(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    symbol = models.CharField(max_length=50, db_column='Symbol')

    objects = UnitManager()

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class ItemManager(models.Manager):
    def get_next_equipment_no(self, company_id, financial_year_id):
        """Generates the next sequential EquipmentNo (e.g., '00001')."""
        # This logic is based on tblDG_BOM_Master.EquipmentNo, but it's used for ItemCode generation,
        # so it's placed here for simplicity. In a real system, it might belong to a sequence service.
        last_bom = self.model.objects.filter(
            company_id=company_id,
            financial_year_id__lte=financial_year_id # Check logic with >= or <= based on actual business rule
        ).exclude(equipment_no__isnull=True).exclude(equipment_no='99999').order_by('-equipment_no').first()

        if last_bom and last_bom.equipment_no:
            try:
                next_equip_no = int(last_bom.equipment_no) + 1
            except ValueError:
                # Fallback if equipment_no is not a valid number
                next_equip_no = 1
        else:
            next_equip_no = 1
        return f"{next_equip_no:05d}" # Format as D5

    def check_item_code_exists(self, item_code, company_id, financial_year_id):
        """Checks if an item code already exists for the company and financial year."""
        return self.filter(
            item_code=item_code,
            company_id=company_id,
            financial_year_id__lte=financial_year_id # Check logic with >= or <=
        ).exists()

    def get_item_code_limit(self, company_id):
        """Retrieves the item code length limit for a given company."""
        # This assumes Company model has ItemCodeLengthLimit field
        try:
            return Company.objects.get(id=company_id).item_code_length_limit
        except Company.DoesNotExist:
            return 10 # Default or raise error

    def get_opening_balance_date(self, company_id, financial_year_id):
        """Retrieves the opening balance date for a given company and financial year."""
        try:
            # Assuming FinancialYear.start_date serves as OpeningBalDate
            return FinancialYear.objects.get(
                id=financial_year_id
            ).start_date
        except FinancialYear.DoesNotExist:
            return timezone.now().date() # Default to today if not found


class Item(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # tblDG_Item_Master.Id
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session = models.ForeignKey(CustomUser, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='items')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='items')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='items')
    part_no = models.CharField(max_length=50, db_column='PartNo') # Generated: EquipNo-UnitNo-PartNo
    process = models.CharField(max_length=10, db_column='Process', default='0')
    item_code = models.CharField(max_length=50, db_column='ItemCode', unique=True) # Generated: EquipNo-UnitNo-PartNo0
    manf_desc = models.TextField(db_column='ManfDesc')
    uom_basic = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items')

    # Drawing/Image File fields
    file_name = models.CharField(max_length=255, db_column='FileName', blank=True, null=True)
    file_size = models.DecimalField(max_digits=20, decimal_places=0, db_column='FileSize', blank=True, null=True) # Assuming file size can be large
    content_type = models.CharField(max_length=100, db_column='ContentType', blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # Storing binary data directly

    # Specification Sheet File fields
    att_name = models.CharField(max_length=255, db_column='AttName', blank=True, null=True)
    att_size = models.DecimalField(max_digits=20, decimal_places=0, db_column='AttSize', blank=True, null=True)
    att_content_type = models.CharField(max_length=100, db_column='AttContentType', blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True) # Storing binary data directly

    opening_bal_date = models.DateField(db_column='OpeningBalDate', blank=True, null=True)
    opening_bal_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='OpeningBalQty', default=0)

    objects = ItemManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

    @property
    def drawing_file_url(self):
        if self.file_data and self.file_name:
            # This will point to a Django view that serves the file
            return format_html('<a href="{}" target="_blank">{}</a>',
                               f'/design/items/download/{self.pk}/drawing/',
                               self.file_name)
        return 'No drawing' # Original behavior linked to an 'Upload' page if empty

    @property
    def spec_sheet_url(self):
        if self.att_data and self.att_name:
            return format_html('<a href="{}" target="_blank">{}</a>',
                               f'/design/items/download/{self.pk}/specsheet/',
                               self.att_name)
        return 'No spec sheet' # Original behavior linked to an 'Upload' page if empty


class BomAssemblyManager(models.Manager):
    def get_bom_c_id(self, wono, company_id, financial_year_id):
        """Generates the next CId for a given BOM."""
        last_cid = self.filter(
            work_order__won_o=wono,
            company_id=company_id,
            financial_year_id__lte=financial_year_id # Adjust comparison as needed
        ).order_by('-c_id').values_list('c_id', flat=True).first()

        return (last_cid or 0) + 1

    def create_bom_item_and_assembly(self, user_id, company_id, financial_year_id, wono, form_data, files_data):
        """
        Handles the complex logic of creating an Item and then a BomAssembly record.
        This method encapsulates the original C# business logic.
        """
        with transaction.atomic():
            # 1. Generate EquipmentNo
            # The EquipmentNo was derived from tblDG_BOM_Master but used for ItemCode.
            # Assuming a sequence relevant to BOMs, let's derive it from Item for consistency.
            # A more robust solution might use a dedicated sequence table or a database sequence.
            # For this exercise, we'll re-implement the specific C# logic.
            # The C# code finds the max EquipmentNo from tblDG_BOM_Master, increments it,
            # and uses it for ItemCode/PartNo generation.
            # Let's find the max EquipmentNo from BomAssembly (matching original logic)
            last_bom_assembly = BomAssembly.objects.filter(
                company_id=company_id,
                financial_year_id__lte=financial_year_id # Check logic
            ).exclude(equipment_no__isnull=True).exclude(equipment_no='99999').order_by('-equipment_no').first()

            if last_bom_assembly and last_bom_assembly.equipment_no:
                try:
                    next_equip_no = int(last_bom_assembly.equipment_no) + 1
                except ValueError:
                    next_equip_no = 1
            else:
                next_equip_no = 1
            equipment_no = f"{next_equip_no:05d}"

            unit_no = form_data['unit_no']
            part_no_input = form_data['part_no_input'] # Renamed to avoid clash with Item.part_no

            # 2. Generate ItemCode and PartNo for ItemMaster
            generated_item_code = f"{equipment_no}-{unit_no}-{part_no_input}0"
            generated_part_no = f"{equipment_no}-{unit_no}-{part_no_input}"

            # 3. Validate ItemCode Length
            company_item_code_limit = Item.objects.get_item_code_limit(company_id)
            if len(generated_item_code) != company_item_code_limit:
                raise ValidationError(f"Item code '{generated_item_code}' length is invalid. Expected length: {company_item_code_limit}.")

            # 4. Check ItemCode Existence
            if Item.objects.check_item_code_exists(generated_item_code, company_id, financial_year_id):
                raise ValidationError("Equipment/Item with this code already exists.")

            # 5. Handle File Uploads (Drawing/Image)
            drw_upload_file = files_data.get('drw_upload')
            drw_file_name = drw_file_data = drw_file_size = drw_content_type = None
            if drw_upload_file:
                drw_file_name = drw_upload_file.name
                drw_file_data = drw_upload_file.read()
                drw_file_size = drw_upload_file.size
                drw_content_type = drw_upload_file.content_type

            # 6. Handle File Uploads (Spec Sheet)
            other_upload_file = files_data.get('other_upload')
            att_name = att_data = att_size = att_content_type = None
            if other_upload_file:
                att_name = other_upload_file.name
                att_data = other_upload_file.read()
                att_size = other_upload_file.size
                att_content_type = other_upload_file.content_type

            # 7. Get Opening Balance Date
            opening_bal_date = Item.objects.get_opening_balance_date(company_id, financial_year_id)

            # 8. Insert into tblDG_Item_Master
            item_instance = Item.objects.create(
                sys_date=timezone.now().date(),
                sys_time=timezone.now().time(),
                session_id=user_id,
                company_id=company_id,
                financial_year_id=financial_year_id,
                part_no=generated_part_no,
                process='0',
                item_code=generated_item_code,
                manf_desc=form_data['manf_desc'],
                uom_basic_id=form_data['uom_basic'],
                file_name=drw_file_name,
                file_size=drw_file_size,
                content_type=drw_content_type,
                file_data=drw_file_data,
                att_name=att_name,
                att_size=att_size,
                att_content_type=att_content_type,
                att_data=att_data,
                opening_bal_date=opening_bal_date,
                opening_bal_qty=0 # Always '0' as per ASP.NET
            )

            # 9. Get New ItemId (already have it from item_instance.id)

            # 10. Generate CId for tblDG_BOM_Master
            next_c_id = self.get_bom_c_id(wono, company_id, financial_year_id)

            # 11. Insert into tblDG_BOM_Master
            bom_assembly_instance = self.create(
                sys_date=timezone.now().date(),
                sys_time=timezone.now().time(),
                session_id=user_id,
                company_id=company_id,
                financial_year_id=financial_year_id,
                work_order_id=wono, # Use the WONo directly as FK value
                equipment_no=equipment_no,
                unit_no=unit_no,
                part_no=part_no_input, # This is the input PartNo, not the generated Item.part_no
                item=item_instance,
                qty=form_data['qty'],
                c_id=next_c_id,
                p_id='0' # Always '0' as per ASP.NET
            )

            # 12. Update SD_Cust_WorkOrder_Master
            work_order = WorkOrder.objects.get(won_o=wono, company_id=company_id)
            work_order.mark_as_updated()

            return bom_assembly_instance


class BomAssembly(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # tblDG_BOM_Master.Id
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session = models.ForeignKey(CustomUser, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='bom_assemblies')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='bom_assemblies')
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='bom_assemblies')
    work_order = models.ForeignKey(WorkOrder, on_delete=models.DO_NOTHING, db_column='WONo', related_name='bom_assemblies') # Assuming WONo is PK in WorkOrder
    equipment_no = models.CharField(max_length=10, db_column='EquipmentNo', blank=True, null=True) # From ASP.NET's perspective
    unit_no = models.CharField(max_length=2, db_column='UnitNo')
    part_no = models.CharField(max_length=2, db_column='PartNo') # The input part number, not Item.part_no
    item = models.ForeignKey(Item, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='bom_assemblies')
    qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='Qty')
    c_id = models.IntegerField(db_column='CId') # Child ID for BOM structure
    p_id = models.CharField(max_length=10, db_column='PId', default='0') # Parent ID

    objects = BomAssemblyManager()

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Assembly'
        verbose_name_plural = 'BOM Assemblies'
        # Add a unique constraint if (WONo, EquipmentNo, UnitNo, PartNo) are unique for PId='0'
        # unique_together = (('work_order', 'equipment_no', 'unit_no', 'part_no'),)

    def __str__(self):
        return f"BOM for WO: {self.work_order.won_o}, Item: {self.item.item_code}"

    @property
    def formatted_qty(self):
        # Format quantity to 3 decimal places
        return f"{self.qty:.3f}"

```

#### 4.2 Forms (`design/forms.py`)

This form will handle the data input for creating a new BOM item. It combines fields from both `Item` and `BomAssembly` that are collected on the input form, along with file uploads. Custom validation will enforce business rules.

```python
from django import forms
from django.core.validators import RegexValidator
from django.core.exceptions import ValidationError
from .models import Item, BomAssembly, Unit

class BomAssemblyCreateForm(forms.Form):
    # Fields from ItemMaster (ManfDesc, UOMBasic, Drw/Image, Spec. Sheet)
    manf_desc = forms.CharField(
        label="Description",
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        required=True
    )
    uom_basic = forms.ModelChoiceField(
        queryset=Unit.objects.all(),
        label="UOM",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )
    drw_upload = forms.FileField(
        label="Drw/Image",
        required=False,
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )
    other_upload = forms.FileField(
        label="Spec. Sheet",
        required=False,
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    # Fields from BomAssembly (UnitNo, PartNo, Qty)
    unit_no = forms.CharField(
        label="Unit No (Ex: xx)",
        max_length=2,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'pattern': '\\d{1,2}'}), # Client-side validation for 1-2 digits
        required=True
    )
    part_no_input = forms.CharField( # Renamed from 'part_no' to avoid confusion with Item.part_no
        label="Part No/SN (Ex: xx)",
        max_length=2,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'pattern': '\\d{1,2}'}), # Client-side validation for 1-2 digits
        required=True
    )
    qty = forms.DecimalField(
        label="Qty",
        max_digits=18,
        decimal_places=3,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'pattern': '^\\d{1,15}(\\.\\d{0,3})?$'}), # Regex matches C# RegEx validator
        required=True,
        min_value=0 # Assuming quantity can't be negative
    )

    def clean_unit_no(self):
        unit_no = self.cleaned_data['unit_no']
        if not unit_no.isdigit():
            raise ValidationError("Unit No must be numeric.")
        return unit_no

    def clean_part_no_input(self):
        part_no_input = self.cleaned_data['part_no_input']
        if not part_no_input.isdigit():
            raise ValidationError("Part No/SN must be numeric.")
        return part_no_input

    def clean_qty(self):
        qty = self.cleaned_data['qty']
        # The C# `NumberValidationQty` suggests a regex. Django DecimalField handles numeric validation.
        # We also enforce the regex for more specific format checking.
        regex = r"^\d{1,15}(\.\d{0,3})?$"
        if not RegexValidator(regex)(str(qty)): # Convert Decimal to string for regex
            raise ValidationError("Quantity must be a number with up to 15 digits before and 3 digits after the decimal point.")
        return qty

```

#### 4.3 Views (`design/views.py`)

The views will be thin, primarily handling request dispatch, form processing, and delegating complex business logic to the models.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, FileResponse, Http404
from django.shortcuts import render, get_object_or_404
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt # Use carefully, consider CsrfViewMiddleware
from django.db import transaction
from django.core.exceptions import ValidationError

from .models import BomAssembly, Item, WorkOrder, CustomUser, Company, FinancialYear # Import all necessary models
from .forms import BomAssemblyCreateForm

# Helper to get session-related data (replace with actual authentication/session management)
def get_session_context(request):
    """
    Placeholder for retrieving session-specific company, financial year, and user.
    In a real application, this would come from the authenticated user,
    or a multi-tenancy context.
    """
    # Dummy data for demonstration.
    # In a real app, you'd get this from request.user, session, etc.
    user_id = request.session.get('user_id', 1) # Example: Get from session
    comp_id = request.session.get('comp_id', 1) # Example: Get from session
    fin_year_id = request.session.get('fin_year_id', 2023) # Example: Get from session

    # Ensure dummy objects exist for testing
    if not CustomUser.objects.filter(id=user_id).exists():
        CustomUser.objects.create(id=user_id, username=f'testuser{user_id}')
    if not Company.objects.filter(id=comp_id).exists():
        Company.objects.create(id=comp_id, name=f'Test Company {comp_id}', item_code_length_limit=10) # Set a default limit
    if not FinancialYear.objects.filter(id=fin_year_id).exists():
        FinancialYear.objects.create(id=fin_year_id, year_name=f'FY{fin_year_id}', start_date='2023-04-01')

    return {
        'user_id': user_id,
        'comp_id': comp_id,
        'fin_year_id': fin_year_id
    }


class BomAssemblyListView(ListView):
    model = BomAssembly
    template_name = 'design/bomassembly/list.html'
    context_object_name = 'bom_assemblies'

    def get_queryset(self):
        wono = self.request.GET.get('WONo')
        # In a real app, user_id, comp_id, fin_year_id would come from request.user
        session_context = get_session_context(self.request)
        comp_id = session_context['comp_id']
        fin_year_id = session_context['fin_year_id']

        if not wono:
            # Handle case where WONo is not provided
            messages.error(self.request, "Work Order Number (WONo) is required.")
            return BomAssembly.objects.none()

        # Replicate ASP.NET query logic
        # 'tblDG_Item_Master.Id=tblDG_BOM_Master.ItemId AND WONo='" + wono + "' AND PId='0' AND tblDG_BOM_Master.CompId='" + CompId + "'AND tblDG_BOM_Master.FinYearId<='" + FinYearId + "' AND tblDG_Item_Master.UOMBasic = Unit_Master.Id Order by tblDG_BOM_Master.ItemId Desc"
        queryset = self.model.objects.filter(
            work_order__won_o=wono,
            p_id='0',
            company_id=comp_id,
            financial_year_id__lte=fin_year_id # Check actual comparison logic
        ).select_related('item', 'item__uom_basic', 'work_order').order_by('-item__id')

        # Logic from Page_Load to set lblEquipNo1 if no rows:
        # This part will be handled in the template or in get_context_data.
        # It's an initial value for the input form.
        if not queryset.exists():
            # This is specific to the "EmptyDataTemplate" logic
            # where the EquipmentNo is pre-filled.
            # We'll pass this to the form directly.
            pass # The form will handle initial values

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wono = self.request.GET.get('WONo')
        context['wono'] = wono

        # Prepare form for adding new BOM item
        # The form is instantiated here and passed to the template.
        # This allows the modal to be pre-rendered or rendered on demand.
        session_context = get_session_context(self.request)
        context['create_form'] = BomAssemblyCreateForm()

        return context


class BomAssemblyTablePartialView(ListView):
    model = BomAssembly
    template_name = 'design/bomassembly/_table.html'
    context_object_name = 'bom_assemblies'

    def get_queryset(self):
        wono = self.request.GET.get('WONo')
        session_context = get_session_context(self.request)
        comp_id = session_context['comp_id']
        fin_year_id = session_context['fin_year_id']

        if not wono:
            return BomAssembly.objects.none()

        queryset = self.model.objects.filter(
            work_order__won_o=wono,
            p_id='0',
            company_id=comp_id,
            financial_year_id__lte=fin_year_id
        ).select_related('item', 'item__uom_basic', 'work_order').order_by('-item__id')

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Determine initial EquipmentNo for the add form in case of no existing BOM items
        session_context = get_session_context(self.request)
        comp_id = session_context['comp_id']
        fin_year_id = session_context['fin_year_id']

        if not context['bom_assemblies'].exists():
            next_equip_no_initial = BomAssembly.objects.get_next_equipment_no(comp_id, fin_year_id)
            context['initial_equipment_no'] = next_equip_no_initial
        else:
            context['initial_equipment_no'] = None # Form won't use this if items exist

        return context


class BomAssemblyCreateView(View):
    # This view will serve the form for adding a new BOM item via HTMX modal
    template_name = 'design/bomassembly/_form.html'

    def get(self, request, *args, **kwargs):
        form = BomAssemblyCreateForm()
        # Pass the initial EquipmentNo to the form if the table is empty
        # This mimics the lblEquipNo1 population from ASP.NET EmptyDataTemplate
        wono = self.request.GET.get('WONo')
        session_context = get_session_context(self.request)
        comp_id = session_context['comp_id']
        fin_year_id = session_context['fin_year_id']
        
        # Check if there are any BOM assemblies for the current WONo and context
        if not BomAssembly.objects.filter(
            work_order__won_o=wono,
            p_id='0',
            company_id=comp_id,
            financial_year_id__lte=fin_year_id
        ).exists():
            initial_equip_no = BomAssembly.objects.get_next_equipment_no(comp_id, fin_year_id)
            # You might want to pass this as an initial value to the form or display it
            # For simplicity, let's just make it available in the template.
            context = {'form': form, 'wono': wono, 'initial_equipment_no': initial_equip_no}
        else:
            context = {'form': form, 'wono': wono, 'initial_equipment_no': None}

        return render(request, self.template_name, context)

    def post(self, request, *args, **kwargs):
        form = BomAssemblyCreateForm(request.POST, request.FILES)
        wono = request.GET.get('WONo')
        session_context = get_session_context(request)
        user_id = session_context['user_id']
        comp_id = session_context['comp_id']
        fin_year_id = session_context['fin_year_id']

        if not wono:
            # This should ideally be caught before POST if WONo is mandatory for this page
            messages.error(request, "Work Order Number is missing.")
            return render(request, self.template_name, {'form': form})

        if form.is_valid():
            try:
                # Delegate complex creation logic to the BomAssembly manager
                BomAssembly.objects.create_bom_item_and_assembly(
                    user_id=user_id,
                    company_id=comp_id,
                    financial_year_id=fin_year_id,
                    wono=wono,
                    form_data=form.cleaned_data,
                    files_data=request.FILES
                )
                messages.success(request, 'BOM Item added successfully.')
                # HTMX response to trigger refresh of the table and close modal
                return HttpResponse(
                    status=204, # No content
                    headers={
                        'HX-Trigger': 'refreshBomAssemblyList, closeBomAssemblyModal'
                    }
                )
            except ValidationError as e:
                # Catch custom validation errors from model methods
                form.add_error(None, e.message)
            except Exception as e:
                # Catch any other unexpected errors
                messages.error(request, f"An error occurred: {e}")
                form.add_error(None, f"An unexpected error occurred: {e}")

        # If form is not valid or an exception occurred, re-render the form with errors
        return render(request, self.template_name, {'form': form, 'wono': wono})


class ItemFileDownloadView(View):
    """View to handle file downloads for Item drawings and spec sheets."""
    def get(self, request, pk, file_type):
        item = get_object_or_404(Item, pk=pk)

        if file_type == 'drawing':
            if not item.file_data or not item.file_name:
                raise Http404("Drawing file not found.")
            response = HttpResponse(item.file_data, content_type=item.content_type)
            response['Content-Disposition'] = f'attachment; filename="{item.file_name}"'
        elif file_type == 'specsheet':
            if not item.att_data or not item.att_name:
                raise Http404("Specification sheet not found.")
            response = HttpResponse(item.att_data, content_type=item.att_content_type)
            response['Content-Disposition'] = f'attachment; filename="{item.att_name}"'
        else:
            raise Http404("Invalid file type specified.")

        return response

```

#### 4.4 Templates (`design/bomassembly/`)

These templates will constitute the user interface, incorporating HTMX for dynamic interactions and DataTables for list presentation.

**`design/bomassembly/list.html`** (Main page displaying the BOM Assembly list)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">BOM Assembly - New (WO No: {{ wono }})</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'bomassembly_add' %}?WONo={{ wono }}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Item
        </button>
    </div>
    
    <!-- Messages container -->
    <div id="messages-container" class="mb-4">
        {% if messages %}
            {% for message in messages %}
                <div class="p-3 mb-3 rounded-md text-sm {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %}" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <div id="bomAssemblyTable-container"
         hx-trigger="load, refreshBomAssemblyList from:body"
         hx-get="{% url 'bomassembly_table' %}?WONo={{ wono }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading BOM items...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on closeModal add .hidden to #modal end 
            on closeBomAssemblyModal from body call closeModal()">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4"
             _="on htmx:afterSwap remove .hidden from #modal when its #modalContent hasChildNode ">
            <!-- Form will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed for this simple modal logic,
        // as HTMX handles the content swap and _hyperscript handles visibility.
        // But if complex state needed, this is where it'd go.
    });

    // Event listener for messages (optional, for more custom toast notifications)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.successful && event.detail.xhr.getResponseHeader('HX-Trigger')) {
            const trigger = event.detail.xhr.getResponseHeader('HX-Trigger');
            if (trigger.includes('refreshBomAssemblyList')) {
                // You might want to clear existing messages or handle them
                // differently on full list refresh.
            }
        }
    });
</script>
{% endblock %}
```

**`design/bomassembly/_table.html`** (Partial template for the DataTables content)

```html
<div class="bg-white shadow-sm rounded-lg overflow-hidden">
    <table id="bomAssemblyTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit No (Ex: xx)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part No/SN (Ex: xx)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Drw/Image</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if bom_assemblies %}
                {% for obj in bom_assemblies %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap">{{ obj.equipment_no|default:'' }}</td>
                    <td class="py-2 px-4 whitespace-nowrap">{{ obj.unit_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap">{{ obj.part_no }}</td>
                    <td class="py-2 px-4">{{ obj.item.manf_desc }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ obj.item.uom_basic.symbol }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ obj.qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap">{{ obj.item.drawing_file_url }}</td>
                    <td class="py-2 px-4 whitespace-nowrap">{{ obj.item.spec_sheet_url }}</td>
                    <td class="py-2 px-4 whitespace-nowrap">
                        <!-- No Update/Delete functionality in original ASP.NET for rows.
                             If it were there, buttons would go here. -->
                        <span class="text-gray-500 text-sm">N/A (Add only)</span>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="10" class="py-4 px-4 text-center text-gray-500">
                    No BOM Assembly items found for WO No: {{ request.GET.WONo }}. 
                    {% if initial_equipment_no %}
                    Click "Add New Item" to create the first one with Equipment No: <span class="font-bold">{{ initial_equipment_no }}</span>.
                    {% else %}
                    Click "Add New Item" to create one.
                    {% endif %}
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Only initialize DataTable if there are actual rows
    if ($('#bomAssemblyTable tbody tr').length > 0 && !$('#bomAssemblyTable tbody tr:first td').hasClass('text-center')) {
        $('#bomAssemblyTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "simple_numbers", // Smaller pagination controls
            "responsive": true, // Make table responsive
            "columnDefs": [
                { "orderable": false, "targets": [0, 9] } // Disable sorting for SN and Actions columns
            ]
        });
    }
});
</script>
```

**`design/bomassembly/_form.html`** (Partial template for the add/edit modal form)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Add New BOM Item</h3>
    <form hx-post="{% url 'bomassembly_add' %}?WONo={{ wono }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% if initial_equipment_no %}
            <div class="md:col-span-2 mb-4 p-3 bg-blue-50 text-blue-800 rounded-md">
                <p class="text-sm font-medium">Next Equipment No: <span class="font-bold">{{ initial_equipment_no }}</span></p>
            </div>
            {% endif %}

            <div class="col-span-1">
                <label for="{{ form.unit_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.unit_no.label }}
                </label>
                {{ form.unit_no }}
                {% if form.unit_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.unit_no.errors }}</p>
                {% endif %}
            </div>

            <div class="col-span-1">
                <label for="{{ form.part_no_input.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.part_no_input.label }}
                </label>
                {{ form.part_no_input }}
                {% if form.part_no_input.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.part_no_input.errors }}</p>
                {% endif %}
            </div>

            <div class="md:col-span-2">
                <label for="{{ form.manf_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.manf_desc.label }}
                </label>
                {{ form.manf_desc }}
                {% if form.manf_desc.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.manf_desc.errors }}</p>
                {% endif %}
            </div>

            <div class="col-span-1">
                <label for="{{ form.uom_basic.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.uom_basic.label }}
                </label>
                {{ form.uom_basic }}
                {% if form.uom_basic.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.uom_basic.errors }}</p>
                {% endif %}
            </div>

            <div class="col-span-1">
                <label for="{{ form.qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.qty.label }}
                </label>
                {{ form.qty }}
                {% if form.qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>
                {% endif %}
            </div>

            <div class="md:col-span-2">
                <label for="{{ form.drw_upload.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.drw_upload.label }}
                </label>
                {{ form.drw_upload }}
                {% if form.drw_upload.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.drw_upload.errors }}</p>
                {% endif %}
            </div>

            <div class="md:col-span-2">
                <label for="{{ form.other_upload.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.other_upload.label }}
                </label>
                {{ form.other_upload }}
                {% if form.other_upload.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.other_upload.errors }}</p>
                {% endif %}
            </div>
        </div>

        {% if form.non_field_errors %}
            <div class="mt-4 text-red-500 text-sm">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click trigger closeBomAssemblyModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Save
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design/urls.py`)

This file defines the URL patterns that map requests to your Django views.

```python
from django.urls import path
from .views import BomAssemblyListView, BomAssemblyTablePartialView, BomAssemblyCreateView, ItemFileDownloadView

urlpatterns = [
    # Main BOM Assembly list page
    path('bomassembly/', BomAssemblyListView.as_view(), name='bomassembly_list'),
    
    # HTMX endpoint for the table partial (for refreshing the list)
    path('bomassembly/table/', BomAssemblyTablePartialView.as_view(), name='bomassembly_table'),
    
    # HTMX endpoint for the add form (loaded into modal)
    path('bomassembly/add/', BomAssemblyCreateView.as_view(), name='bomassembly_add'),
    
    # File download URLs
    path('items/download/<int:pk>/<str:file_type>/', ItemFileDownloadView.as_view(), name='item_file_download'),
]

```

#### 4.6 Tests (`design/tests.py`)

Comprehensive tests for models (unit tests) and views (integration tests) are crucial for ensuring the migrated functionality is correct and robust.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from io import BytesIO
from unittest.mock import patch

from .models import BomAssembly, Item, Unit, Company, FinancialYear, CustomUser, WorkOrder
from .forms import BomAssemblyCreateForm

# Mock the session context for testing purposes
def mock_get_session_context(request):
    return {
        'user_id': 1,
        'comp_id': 1,
        'fin_year_id': 2023,
    }

# Patch the get_session_context in views during tests
@patch('design.views.get_session_context', mock_get_session_context)
class BomAssemblyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary supporting data
        cls.user = CustomUser.objects.create(id=1, username='testuser')
        cls.company = Company.objects.create(id=1, name='TestCo', item_code_length_limit=10)
        cls.financial_year = FinancialYear.objects.create(id=2023, year_name='2023-24', start_date='2023-04-01')
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')
        cls.work_order = WorkOrder.objects.create(won_o='WO001', company=cls.company)
        
        # Create an initial BomAssembly and Item for sequencing tests
        cls.initial_item = Item.objects.create(
            id=1, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=cls.user, company=cls.company, financial_year=cls.financial_year,
            part_no='00000-00-00', process='0', item_code='00000-00-000',
            manf_desc='Initial Item', uom_basic=cls.unit_pcs,
            opening_bal_date=cls.financial_year.start_date, opening_bal_qty=0
        )
        cls.initial_bom = BomAssembly.objects.create(
            id=1, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=cls.user, company=cls.company, financial_year=cls.financial_year,
            work_order=cls.work_order, equipment_no='00000', unit_no='00', part_no='00',
            item=cls.initial_item, qty=Decimal('10.000'), c_id=1, p_id='0'
        )

    def test_bom_assembly_creation(self):
        new_item = Item.objects.create(
            id=2, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='00001-01-01', process='0', item_code='00001-01-010',
            manf_desc='Test Item', uom_basic=self.unit_pcs,
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        bom_assembly = BomAssembly.objects.create(
            id=2, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            work_order=self.work_order, equipment_no='00001', unit_no='01', part_no='01',
            item=new_item, qty=Decimal('5.500'), c_id=2, p_id='0'
        )
        self.assertEqual(bom_assembly.equipment_no, '00001')
        self.assertEqual(bom_assembly.unit_no, '01')
        self.assertEqual(bom_assembly.part_no, '01')
        self.assertEqual(bom_assembly.item.manf_desc, 'Test Item')
        self.assertEqual(bom_assembly.qty, Decimal('5.500'))
        self.assertEqual(bom_assembly.p_id, '0')

    def test_item_file_properties(self):
        item_with_files = Item.objects.create(
            id=3, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='00002-02-02', process='0', item_code='00002-02-020',
            manf_desc='Item with files', uom_basic=self.unit_kg,
            file_name='drawing.pdf', file_data=b'pdf_data', content_type='application/pdf',
            att_name='spec.doc', att_data=b'doc_data', att_content_type='application/msword',
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        self.assertIn('drawing.pdf', item_with_files.drawing_file_url)
        self.assertIn('spec.doc', item_with_files.spec_sheet_url)

    def test_get_next_equipment_no(self):
        # Ensure previous equipment_no for initial_bom (00000) does not affect next one
        # It's based on the highest existing equipment_no
        next_equip_no = BomAssembly.objects.get_next_equipment_no(self.company.id, self.financial_year.id)
        # Since initial_bom's equipment_no is '00000', the next should be '00001'
        self.assertEqual(next_equip_no, '00001') 
        
        # Create an item with a higher equipment no
        item_high_equip = Item.objects.create(
            id=4, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='00100-01-01', process='0', item_code='00100-01-010',
            manf_desc='High Equip Item', uom_basic=self.unit_pcs,
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        BomAssembly.objects.create(
            id=3, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            work_order=self.work_order, equipment_no='00100', unit_no='01', part_no='01',
            item=item_high_equip, qty=Decimal('1.000'), c_id=3, p_id='0'
        )
        next_equip_no_after_high = BomAssembly.objects.get_next_equipment_no(self.company.id, self.financial_year.id)
        self.assertEqual(next_equip_no_after_high, '00101')

    def test_get_bom_c_id(self):
        c_id_1 = BomAssembly.objects.get_bom_c_id(self.work_order.won_o, self.company.id, self.financial_year.id)
        # Initial bom had c_id=1, so next should be 2
        self.assertEqual(c_id_1, 2)

        # Create another BOM for the same WO
        new_item = Item.objects.create(
            id=5, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='00003-01-01', process='0', item_code='00003-01-010',
            manf_desc='Another Item', uom_basic=self.unit_pcs,
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        BomAssembly.objects.create(
            id=4, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            work_order=self.work_order, equipment_no='00003', unit_no='01', part_no='01',
            item=new_item, qty=Decimal('1.000'), c_id=c_id_1, p_id='0'
        )
        c_id_2 = BomAssembly.objects.get_bom_c_id(self.work_order.won_o, self.company.id, self.financial_year.id)
        self.assertEqual(c_id_2, 3)

    def test_check_item_code_exists(self):
        self.assertTrue(Item.objects.check_item_code_exists(self.initial_item.item_code, self.company.id, self.financial_year.id))
        self.assertFalse(Item.objects.check_item_code_exists('NONEXISTENT-CODE', self.company.id, self.financial_year.id))

    def test_get_item_code_limit(self):
        self.assertEqual(Item.objects.get_item_code_limit(self.company.id), 10)

    def test_get_opening_balance_date(self):
        self.assertEqual(Item.objects.get_opening_balance_date(self.company.id, self.financial_year.id), self.financial_year.start_date)

@patch('design.views.get_session_context', mock_get_session_context)
class BomAssemblyViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = CustomUser.objects.create(id=1, username='testuser')
        cls.company = Company.objects.create(id=1, name='TestCo', item_code_length_limit=10)
        cls.financial_year = FinancialYear.objects.create(id=2023, year_name='2023-24', start_date='2023-04-01')
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.unit_kg = Unit.objects.create(id=2, symbol='KG')
        cls.work_order = WorkOrder.objects.create(won_o='WO001', company=cls.company)
        cls.work_order_2 = WorkOrder.objects.create(won_o='WO002', company=cls.company)

        # Create some initial BOM assemblies for listing
        cls.item1 = Item.objects.create(
            id=101, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=cls.user, company=cls.company, financial_year=cls.financial_year,
            part_no='E0001-U01-P01', process='0', item_code='E0001-U01-P010',
            manf_desc='Test Item 1', uom_basic=cls.unit_pcs,
            opening_bal_date=cls.financial_year.start_date, opening_bal_qty=0
        )
        cls.bom_assembly1 = BomAssembly.objects.create(
            id=201, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=cls.user, company=cls.company, financial_year=cls.financial_year,
            work_order=cls.work_order, equipment_no='E0001', unit_no='01', part_no='01',
            item=cls.item1, qty=Decimal('10.000'), c_id=1, p_id='0'
        )
        cls.item2 = Item.objects.create(
            id=102, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=cls.user, company=cls.company, financial_year=cls.financial_year,
            part_no='E0002-U02-P02', process='0', item_code='E0002-U02-P020',
            manf_desc='Test Item 2', uom_basic=cls.unit_kg,
            opening_bal_date=cls.financial_year.start_date, opening_bal_qty=0
        )
        cls.bom_assembly2 = BomAssembly.objects.create(
            id=202, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=cls.user, company=cls.company, financial_year=cls.financial_year,
            work_order=cls.work_order, equipment_no='E0002', unit_no='02', part_no='02',
            item=cls.item2, qty=Decimal('5.500'), c_id=2, p_id='0'
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('bomassembly_list'), {'WONo': self.work_order.won_o})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bomassembly/list.html')
        self.assertIn('bom_assemblies', response.context)
        self.assertEqual(response.context['bom_assemblies'].count(), 2)
        self.assertEqual(response.context['wono'], self.work_order.won_o)

    def test_list_view_no_wono(self):
        response = self.client.get(reverse('bomassembly_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bomassembly/list.html')
        self.assertIn('bom_assemblies', response.context)
        self.assertEqual(response.context['bom_assemblies'].count(), 0)
        self.assertIn('Work Order Number (WONo) is required.', [str(m) for m in response.context['messages']])

    def test_table_partial_view(self):
        response = self.client.get(reverse('bomassembly_table'), {'WONo': self.work_order.won_o})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bomassembly/_table.html')
        self.assertIn('bom_assemblies', response.context)
        self.assertEqual(response.context['bom_assemblies'].count(), 2)

    def test_table_partial_view_empty(self):
        # Create a new WO with no BOM items
        response = self.client.get(reverse('bomassembly_table'), {'WONo': self.work_order_2.won_o})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bomassembly/_table.html')
        self.assertIn('bom_assemblies', response.context)
        self.assertEqual(response.context['bom_assemblies'].count(), 0)
        self.assertIsNotNone(response.context['initial_equipment_no']) # Should generate next equip no

    def test_create_view_get(self):
        response = self.client.get(reverse('bomassembly_add'), {'WONo': self.work_order.won_o})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bomassembly/_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], BomAssemblyCreateForm)
        self.assertIsNotNone(response.context['wono']) # should be 'WO001'

    def test_create_view_get_empty_table(self):
        # Simulate empty table scenario to check initial_equipment_no
        response = self.client.get(reverse('bomassembly_add'), {'WONo': self.work_order_2.won_o})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bomassembly/_form.html')
        self.assertIsNotNone(response.context['initial_equipment_no'])
        self.assertIn('initial_equipment_no', response.context)

    def test_create_view_post_success(self):
        initial_bom_count = BomAssembly.objects.count()
        initial_item_count = Item.objects.count()
        
        data = {
            'manf_desc': 'New Test Item Description',
            'uom_basic': self.unit_pcs.id,
            'unit_no': '03',
            'part_no_input': '03',
            'qty': '1.234',
        }
        response = self.client.post(reverse('bomassembly_add'), data, headers={'HX-Request': 'true'}, GET={'WONo': self.work_order.won_o})
        
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertEqual(response['HX-Trigger'], 'refreshBomAssemblyList, closeBomAssemblyModal')
        
        self.assertEqual(BomAssembly.objects.count(), initial_bom_count + 1)
        self.assertEqual(Item.objects.count(), initial_item_count + 1)
        
        new_bom = BomAssembly.objects.order_by('-id').first()
        self.assertEqual(new_bom.item.manf_desc, 'New Test Item Description')
        self.assertEqual(new_bom.qty, Decimal('1.234'))
        self.assertTrue(WorkOrder.objects.get(won_o=self.work_order.won_o).update_wo) # Check WO update

    def test_create_view_post_with_files(self):
        initial_bom_count = BomAssembly.objects.count()
        initial_item_count = Item.objects.count()
        
        # Create dummy files
        test_drawing_file = BytesIO(b"This is a dummy drawing content.")
        test_drawing_file.name = 'test_drawing.pdf'
        test_drawing_file.content_type = 'application/pdf'

        test_spec_file = BytesIO(b"This is a dummy spec sheet content.")
        test_spec_file.name = 'test_spec.docx'
        test_spec_file.content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'

        data = {
            'manf_desc': 'Item with Files',
            'uom_basic': self.unit_pcs.id,
            'unit_no': '04',
            'part_no_input': '04',
            'qty': '2.500',
        }
        files = {
            'drw_upload': test_drawing_file,
            'other_upload': test_spec_file,
        }
        
        response = self.client.post(reverse('bomassembly_add'), data, files=files, headers={'HX-Request': 'true'}, GET={'WONo': self.work_order.won_o})
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(BomAssembly.objects.count(), initial_bom_count + 1)
        self.assertEqual(Item.objects.count(), initial_item_count + 1)

        new_item = Item.objects.order_by('-id').first()
        self.assertEqual(new_item.file_name, 'test_drawing.pdf')
        self.assertEqual(new_item.att_name, 'test_spec.docx')
        self.assertEqual(new_item.file_data, b"This is a dummy drawing content.")
        self.assertEqual(new_item.att_data, b"This is a dummy spec sheet content.")

    def test_create_view_post_invalid_form(self):
        initial_bom_count = BomAssembly.objects.count()
        initial_item_count = Item.objects.count()

        data = {
            'manf_desc': '', # Missing required field
            'uom_basic': self.unit_pcs.id,
            'unit_no': '05',
            'part_no_input': '05',
            'qty': 'abc', # Invalid quantity
        }
        response = self.client.post(reverse('bomassembly_add'), data, headers={'HX-Request': 'true'}, GET={'WONo': self.work_order.won_o})
        
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'design/bomassembly/_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('manf_desc', response.context['form'].errors)
        self.assertIn('qty', response.context['form'].errors)
        
        self.assertEqual(BomAssembly.objects.count(), initial_bom_count)
        self.assertEqual(Item.objects.count(), initial_item_count)

    def test_create_view_post_item_code_exists_error(self):
        initial_bom_count = BomAssembly.objects.count()
        initial_item_count = Item.objects.count()

        # Create an item with a known item_code that BomAssemblyCreateForm will try to generate
        # To make this test realistic, we need to manually create an item with the expected
        # item_code for the NEXT available equipment_no, unit_no, part_no_input.
        # Based on current `BomAssembly.objects.get_next_equipment_no` logic, the next equip_no will be '00003'.
        # If we try to create an item with unit_no '06' and part_no '06', the item_code will be '00003-06-060'.
        Item.objects.create(
            id=103, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='00003-06-06', process='0', item_code='00003-06-060',
            manf_desc='Existing Item for Test', uom_basic=self.unit_pcs,
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        
        data = {
            'manf_desc': 'Attempt to create duplicate',
            'uom_basic': self.unit_pcs.id,
            'unit_no': '06',
            'part_no_input': '06',
            'qty': '1.000',
        }
        response = self.client.post(reverse('bomassembly_add'), data, headers={'HX-Request': 'true'}, GET={'WONo': self.work_order.won_o})

        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'design/bomassembly/_form.html')
        self.assertIn('form', response.context)
        self.assertIn('Equipment/Item with this code already exists.', response.context['form'].non_field_errors)
        
        self.assertEqual(BomAssembly.objects.count(), initial_bom_count)
        self.assertEqual(Item.objects.count(), initial_item_count + 1) # Item was created but not BOM
        
    def test_item_file_download_drawing(self):
        # Create an item with drawing data
        item_with_drawing = Item.objects.create(
            id=104, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='DL-DRW-01', process='0', item_code='DL-DRW-010',
            manf_desc='Downloadable Drawing', uom_basic=self.unit_pcs,
            file_name='test_drawing.pdf', file_size=len(b'test_data'), content_type='application/pdf', file_data=b'test_data',
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        url = reverse('item_file_download', args=[item_with_drawing.id, 'drawing'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_drawing.pdf"')
        self.assertEqual(response.content, b'test_data')

    def test_item_file_download_specsheet(self):
        # Create an item with spec sheet data
        item_with_specsheet = Item.objects.create(
            id=105, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='DL-SPEC-01', process='0', item_code='DL-SPEC-010',
            manf_desc='Downloadable Spec Sheet', uom_basic=self.unit_pcs,
            att_name='test_spec.docx', att_size=len(b'spec_data'), att_content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document', att_data=b'spec_data',
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        url = reverse('item_file_download', args=[item_with_specsheet.id, 'specsheet'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="test_spec.docx"')
        self.assertEqual(response.content, b'spec_data')

    def test_item_file_download_not_found(self):
        # Item exists but no file data
        item_no_file = Item.objects.create(
            id=106, sys_date=timezone.now().date(), sys_time=timezone.now().time(),
            session=self.user, company=self.company, financial_year=self.financial_year,
            part_no='NOFILE-01', process='0', item_code='NOFILE-010',
            manf_desc='No file', uom_basic=self.unit_pcs,
            opening_bal_date=self.financial_year.start_date, opening_bal_qty=0
        )
        url = reverse('item_file_download', args=[item_no_file.id, 'drawing'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

        url = reverse('item_file_download', args=[item_no_file.id, 'specsheet'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

        # Invalid file type
        url = reverse('item_file_download', args=[item_no_file.id, 'invalid_type'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

The provided templates (`list.html`, `_table.html`, `_form.html`) already include the necessary HTMX and Alpine.js attributes for dynamic behavior:

*   **HTMX for DataTables Refresh:**
    *   `bomAssemblyTable-container` in `list.html` uses `hx-get="{% url 'bomassembly_table' %}?WONo={{ wono }}"` and `hx-trigger="load, refreshBomAssemblyList from:body"` to load the table content dynamically on page load and whenever a `refreshBomAssemblyList` event is triggered (e.g., after a successful form submission).
    *   The form in `_form.html` uses `hx-post`, `hx-swap="outerHTML"`, and `hx-target="#modalContent"` to submit the form via AJAX and replace the entire modal content with the response (either the re-rendered form with errors or an empty response on success).
    *   On successful form submission (`BomAssemblyCreateView`), the view returns `HttpResponse(status=204, headers={'HX-Trigger': 'refreshBomAssemblyList, closeBomAssemblyModal'})`. This tells HTMX to trigger the `refreshBomAssemblyList` event (to reload the table) and `closeBomAssemblyModal` event (to close the modal).
*   **Alpine.js for Modals:**
    *   The `#modal` in `list.html` uses `_` (Hyperscript) attributes:
        *   `_="on click if event.target.id == 'modal' remove .is-active from me"`: Allows clicking outside the modal content to close it.
        *   `_="on closeModal add .hidden to #modal end on closeBomAssemblyModal from body call closeModal()"`: Defines a `closeModal` function and binds the `closeBomAssemblyModal` HTMX event to call it, hiding the modal.
    *   The "Cancel" button in `_form.html` uses `_="on click trigger closeBomAssemblyModal from body"` to explicitly trigger the modal close event.
    *   `_="on htmx:afterSwap remove .hidden from #modal when its #modalContent hasChildNode"`: Ensures the modal becomes visible after HTMX swaps in content.
*   **DataTables Integration:**
    *   The `_table.html` partial includes a `script` block to initialize DataTables on the `bomAssemblyTable` ID after it has been loaded into the DOM by HTMX. This ensures proper functionality including client-side searching, sorting, and pagination.

### Final Notes

*   **Placeholders:** `CompId`, `FinYearId`, `SessionId` (username) are critical for data filtering and persistence. In the provided Django code, these are handled via a `get_session_context` helper which assumes they are available from the `request.session` or similar global context. In a production environment, this would integrate with your authentication and multi-tenancy system (e.g., pulling these from `request.user.profile` or a dedicated middleware).
*   **Database Mapping:** The `managed = False` in `Meta` classes is essential for working with existing database schemas. Ensure field names (`db_column`) correctly map to your actual database column names.
*   **Error Handling:** The `BomAssemblyCreateView` includes `try-except ValidationError` for custom model/manager validation errors and general `Exception` handling, providing user-friendly messages via Django's `messages` framework.
*   **File Storage:** This plan mirrors the ASP.NET behavior of storing binary files directly in the database. For larger files or better performance/scalability, consider migrating to Django's `FileField` which stores files on the filesystem (local or cloud storage like S3) and stores only the path in the database. This would require changes to the `Item` model (using `FileField` instead of `BinaryField`) and the file upload/download logic.
*   **Refinement:** The `EquipmentNo` generation, while replicated from the ASP.NET logic, might benefit from a more robust sequence management system in a large-scale ERP.