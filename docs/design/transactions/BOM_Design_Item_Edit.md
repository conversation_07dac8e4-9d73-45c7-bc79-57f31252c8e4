## ASP.NET to Django Conversion Script: BOM Item Edit

This document outlines a comprehensive modernization plan to transition the `BOM_Design_Item_Edit.aspx` and its C# code-behind from ASP.NET to a modern Django application. The focus is on leveraging AI-assisted automation, adhering to Django 5.0+ best practices, and employing HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (for list views).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several SQL Server tables for Bill of Material (BOM) management.

*   `tblDG_BOM_Master`: This is the primary table being modified. It stores BOM item details like quantity, revision, and links to work orders and items.
*   `tblDG_Item_Master`: Provides item-specific information such as item code, manufacturer description, and unit of measure.
*   `Unit_Master`: Stores symbols for units of measure.
*   `SD_Cust_WorkOrder_Master`: Used to retrieve the `TaskDesignFinalization_TDate` for business logic validation.
*   `tblDG_BOM_Amd`: Serves as an amendment history table for BOM changes, where new records are inserted upon updates.

**Identified Tables and Key Columns:**

*   **`tblDG_BOM_Master`**
    *   `Id` (Primary Key, passed as `asslyId` in URL)
    *   `WONo` (Work Order Number, passed in URL)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`, passed in URL)
    *   `CId` (Category ID, passed in URL)
    *   `Qty` (Quantity, editable)
    *   `Revision` (Revision, editable)
    *   `AmdNo` (Amendment Number, updated programmatically)
    *   `PId` (Parent ID, seems to be a foreign key to self or another BOM item, though not directly used for update logic here, it's selected)
    *   `CompId` (Company ID, from Session)
    *   `FinYearId` (Financial Year ID, from Session)
    *   `SysDate`, `SysTime`, `SessionId` (Audit fields)

*   **`tblDG_Item_Master`**
    *   `Id` (Primary Key)
    *   `ItemCode`
    *   `ManfDesc` (Manufacturer Description)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`)
    *   `CId` (Item Category ID, named `ItemCId` in C# code)

*   **`Unit_Master`**
    *   `Id` (Primary Key)
    *   `Symbol` (Unit of Measure symbol)

*   **`SD_Cust_WorkOrder_Master`**
    *   `WONo` (Primary Key / Unique Identifier)
    *   `TaskDesignFinalization_TDate` (Date for design finalization)

*   **`tblDG_BOM_Amd`**
    *   `Id` (Primary Key)
    *   `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `WONo` (Audit/Context fields)
    *   `BOMId` (Foreign Key to `tblDG_BOM_Master.Id`)
    *   `PId`, `CId`, `ItemId` (Context from original BOM item)
    *   `Description`, `UOM` (Snapshot of item details)
    *   `AmdNo`, `Qty` (Snapshot of amendment details)

### Step 2: Identify Backend Functionality

**Read Operations:**
*   On `Page_Load`, the system queries `tblDG_Item_Master`, `Unit_Master`, and `tblDG_BOM_Master` to retrieve details for a specific BOM item identified by `WONo`, `ItemId`, `Id` (assembly ID), `CompId`, and `FinYearId`. This data populates the display labels (`lblWONo`, `lblItemCode`, `lblMfDesc`, `lblUOMB`) and editable text boxes (`txtQuntity`, `txtRevision`).

**Update Operations:**
*   The `btnUpdate_Click` event handles the update.
*   **Validation:** It first checks if the `txtQuntity` is not empty and validates its numeric format (up to 3 decimal places). It also seems `txtRevision` is implicitly required by a validator.
*   **Business Rule (Design Date Check):** It fetches `TaskDesignFinalization_TDate` from `SD_Cust_WorkOrder_Master`.
    *   If `CurrentDate <= DesignDate`: The BOM item `Qty` and `Revision` are directly updated in `tblDG_BOM_Master`. An amendment record is *inserted* into `tblDG_BOM_Amd` with the old quantity and amendment number, and `AmdNo` in `tblDG_BOM_Master` is incremented.
    *   If `CurrentDate > DesignDate`: The system redirects to an `ECN_Master_Edit.aspx` page, indicating that an Engineering Change Notice (ECN) process is required due to the design being finalized. The new quantity and revision are passed as query parameters.

**Navigation:**
*   Upon successful update or cancellation, the page redirects to `BOM_Design_WO_TreeView_Edit.aspx`.
*   If the design date check fails, it redirects to `ECN_Master_Edit.aspx`.

### Step 3: Infer UI Components

The ASP.NET page is primarily an edit form for a single BOM item.

*   **Display Fields (Labels):**
    *   `lblWONo`: Work Order Number
    *   `lblItemCode`: Item Code
    *   `lblMfDesc`: Manufacturer Description
    *   `lblUOMB`: Unit of Measure (Basic)

*   **Input Fields (Text Boxes):**
    *   `txtQuntity`: Quantity (Numeric with validation)
    *   `txtRevision`: Revision (Text, required)

*   **Action Buttons:**
    *   `btnUpdate`: Submits the form, triggers update logic and validation. Includes a client-side confirmation.
    *   `btncancel`: Cancels the operation and redirects.

*   **Validation:**
    *   `RequiredFieldValidator` for Quantity and Revision.
    *   `RegularExpressionValidator` for Quantity to allow numbers with up to 3 decimal places.

In Django, this will translate to:
*   Static display of `WONo`, `ItemCode`, `Description`, `UOM` in the template.
*   A Django `ModelForm` with `quantity` and `revision` fields, handled by a `BOMMasterUpdateView`.
*   Form validation will be handled by Django's form system.
*   HTMX will be used for partial page updates (e.g., loading the form into a modal, form submission).

### Step 4: Generate Django Code

We will create a Django application named `bom` (short for Bill of Materials).

#### 4.1 Models (`bom/models.py`)

This section defines Django models that map to the existing database tables. We set `managed = False` to ensure Django does not attempt to create or modify these tables, as they already exist. Business logic previously in the C# code-behind is now encapsulated within the `BOMMaster` model.

```python
from django.db import models
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal

class Unit(models.Model):
    """Maps to Unit_Master table for Unit of Measure symbols."""
    id = models.AutoField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    """Maps to tblDG_Item_Master for item details."""
    id = models.AutoField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    part_no = models.CharField(db_column='PartNo', max_length=100, blank=True, null=True)
    manufacturer_description = models.CharField(db_column='ManfDesc', max_length=250, blank=True, null=True)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    category_id = models.IntegerField(db_column='CId', blank=True, null=True) # ItemCId from ASP.NET

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class WorkOrderMaster(models.Model):
    """Maps to SD_Cust_WorkOrder_Master for work order details."""
    work_order_no = models.CharField(db_column='WONo', primary_key=True, max_length=50)
    task_design_finalization_date = models.DateField(db_column='TaskDesignFinalization_TDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.work_order_no

class BOMMaster(models.Model):
    """Maps to tblDG_BOM_Master for BOM item details."""
    id = models.AutoField(db_column='Id', primary_key=True) # Corresponds to asslyId
    work_order_no = models.CharField(db_column='WONo', max_length=50)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId') # Corresponds to itemId
    category_id = models.IntegerField(db_column='CId') # Corresponds to CId
    parent_id = models.IntegerField(db_column='PId', blank=True, null=True) # Potential FK to self if it's a tree structure
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # Matches regex validation
    revision = models.CharField(db_column='Revision', max_length=50)
    amendment_no = models.IntegerField(db_column='AmdNo', default=0)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # User session ID
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', auto_now=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'
        unique_together = (('work_order_no', 'item', 'category_id', 'company_id', 'financial_year_id'),) # Inferred unique constraint based on original query

    def __str__(self):
        return f"{self.work_order_no} - {self.item.item_code} (Rev: {self.revision})"

    # Business Logic Methods
    def get_next_amendment_no(self):
        """Calculates the next amendment number."""
        return self.amendment_no + 1

    def create_amendment_record(self, old_qty, old_amendment_no, description, uom_symbol):
        """Inserts a record into the tblDG_BOM_Amd table."""
        BOMAmendment.objects.create(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id=self.session_id,
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            work_order_no=self.work_order_no,
            bom_master=self,
            parent_id=self.parent_id,
            category_id=self.category_id,
            item_id=self.item.id,
            description=description,
            uom=uom_symbol, # Store UOM symbol directly as per original code
            amendment_no=old_amendment_no,
            quantity=old_qty
        )

    def is_design_date_valid(self):
        """Checks if the current date is before or on the WO design finalization date."""
        try:
            work_order = WorkOrderMaster.objects.get(work_order_no=self.work_order_no)
            if work_order.task_design_finalization_date:
                return timezone.now().date() <= work_order.task_design_finalization_date
            return False # No design date set, treat as invalid for direct update
        except WorkOrderMaster.DoesNotExist:
            return False # Work order not found, treat as invalid

    def perform_update_logic(self, new_quantity, new_revision):
        """
        Encapsulates the update logic including amendment tracking and design date check.
        Returns 'updated', 'ecn_required', or 'error'
        """
        old_quantity = self.quantity
        old_amendment_no = self.amendment_no

        if not self.is_design_date_valid():
            return 'ecn_required'

        # If design date is valid, proceed with direct update
        try:
            self.quantity = new_quantity
            self.revision = new_revision
            self.amendment_no = self.get_next_amendment_no()
            self.session_id = self.session_id # Ensure session_id is persisted if needed
            self.sys_date = timezone.now().date()
            self.sys_time = timezone.now().time()
            self.save(update_fields=['quantity', 'revision', 'amendment_no', 'sys_date', 'sys_time'])

            # Create amendment record after successful update
            self.create_amendment_record(
                old_qty=old_quantity,
                old_amendment_no=old_amendment_no,
                description=self.item.manufacturer_description if self.item else "",
                uom_symbol=self.item.uom_basic.symbol if self.item and self.item.uom_basic else ""
            )
            return 'updated'
        except Exception as e:
            # Log the error for debugging
            print(f"Error during BOM update: {e}")
            return 'error'

class BOMAmendment(models.Model):
    """Maps to tblDG_BOM_Amd for BOM amendment history."""
    id = models.AutoField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    work_order_no = models.CharField(db_column='WONo', max_length=50)
    bom_master = models.ForeignKey(BOMMaster, models.DO_NOTHING, db_column='BOMId', related_name='amendments')
    parent_id = models.IntegerField(db_column='PId', blank=True, null=True)
    category_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # Stored as int here, original has FK but tblDG_BOM_Amd also has ItemId
    description = models.CharField(db_column='Description', max_length=250, blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=50, blank=True, null=True) # Stored as string, not FK
    amendment_no = models.IntegerField(db_column='AmdNo', blank=True, null=True)
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Amd'
        verbose_name = 'BOM Amendment'
        verbose_name_plural = 'BOM Amendments'
```

#### 4.2 Forms (`bom/forms.py`)

This form is used to capture the `quantity` and `revision` for the `BOMMaster` model. Custom validation ensures data integrity, replicating the ASP.NET `RegularExpressionValidator` and `RequiredFieldValidator`.

```python
from django import forms
from .models import BOMMaster
from decimal import Decimal, InvalidOperation

class BOMMasterForm(forms.ModelForm):
    class Meta:
        model = BOMMaster
        fields = ['quantity', 'revision']
        widgets = {
            'quantity': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'number', 'step': '0.001'}),
            'revision': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'quantity': 'Unit Qty',
            'revision': 'Revision',
        }

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity is None:
            raise forms.ValidationError("Unit Qty is required.")
        try:
            # Ensure it's a Decimal and within allowed precision
            qty_decimal = Decimal(str(quantity))
            if qty_decimal.as_tuple().exponent < -3: # More than 3 decimal places
                raise forms.ValidationError("Quantity can have at most 3 decimal places.")
            if qty_decimal < 0: # Assuming quantity should not be negative
                raise forms.ValidationError("Quantity cannot be negative.")
        except InvalidOperation:
            raise forms.ValidationError("Invalid quantity format. Please enter a valid number.")
        return quantity

    def clean_revision(self):
        revision = self.cleaned_data.get('revision')
        if not revision:
            raise forms.ValidationError("Revision is required.")
        return revision
```

#### 4.3 Views (`bom/views.py`)

This section implements the Django Class-Based Views (CBVs) for the BOMMaster. The `BOMMasterUpdateView` is the core of the ASP.NET page's functionality, with overridden `get_object` to handle multiple URL parameters and `form_valid` to encapsulate the business logic. `ListView` and partial `TablePartialView` are included for DataTables integration as required.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, redirect
from django.db import transaction

from .models import BOMMaster, WorkOrderMaster, ItemMaster # Import other models for context
from .forms import BOMMasterForm

# Helper to get user/company/finyear details. In a real app, this would come from an authentication system.
# For demo purposes, we'll assume dummy values or retrieve from session/user profile.
def get_user_context(request):
    # This is a placeholder. In a real ERP, these would come from the authenticated user.
    # For a migration, you might map them from session or user model.
    # Example:
    # user_session_id = request.user.username if request.user.is_authenticated else 'anonymous'
    # company_id = request.user.profile.company_id if hasattr(request.user, 'profile') else 1
    # financial_year_id = request.user.profile.financial_year_id if hasattr(request.user, 'profile') else 2023
    return {
        'session_id': 'DUMMY_SESSION_ID', # Replace with actual session ID
        'company_id': 1, # Replace with actual company ID
        'fin_year_id': 2023 # Replace with actual financial year ID
    }


class BOMMasterListView(ListView):
    """
    Displays a list of BOMMaster objects, typically for a specific Work Order.
    This view uses HTMX to load the DataTable partial.
    """
    model = BOMMaster
    template_name = 'bom/bommaster/list.html'
    context_object_name = 'bom_masters'

    # Filter by work_order_no if provided in URL (e.g., /bommaster/?won=WO123)
    def get_queryset(self):
        queryset = super().get_queryset()
        work_order_no = self.request.GET.get('won')
        if work_order_no:
            queryset = queryset.filter(work_order_no=work_order_no)
        # Add filtering by company_id, financial_year_id etc. if needed
        # queryset = queryset.filter(company_id=get_user_context(self.request)['company_id'])
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['work_order_no'] = self.request.GET.get('won', 'N/A')
        return context

class BOMMasterTablePartialView(BOMMasterListView):
    """
    Returns the table content for DataTables via HTMX.
    Inherits filtering from BOMMasterListView.
    """
    template_name = 'bom/bommaster/_table.html'


class BOMMasterUpdateView(UpdateView):
    """
    Handles updating an existing BOM Master item.
    Replicates the logic from BOM_Design_Item_Edit.aspx.cs.
    """
    model = BOMMaster
    form_class = BOMMasterForm
    template_name = 'bom/bommaster/form.html'

    def get_object(self, queryset=None):
        """
        Overrides get_object to use multiple URL parameters as primary key.
        The original ASP.NET page uses QueryString["Id"] (asslyId) as the primary ID,
        along with ItemId, WONo, and CId for a robust lookup.
        We'll use 'pk' from the URL as the 'Id' (asslyId) for the BOMMaster.
        """
        bom_id = self.kwargs.get('pk') # This maps to 'Id' (asslyId)
        work_order_no = self.request.GET.get('WONo')
        item_id = self.request.GET.get('ItemId')
        category_id = self.request.GET.get('CId')

        if not bom_id:
            raise ValueError("BOM ID (pk) is required.")

        # In a real scenario, you might also use company_id and fin_year_id from the user context
        user_context = get_user_context(self.request)

        # Build a robust lookup to match original ASP.NET behavior
        lookup_kwargs = {
            'id': bom_id,
            'work_order_no': work_order_no,
            'item_id': item_id,
            'category_id': category_id,
            'company_id': user_context['company_id'], # Important for filtering
            'financial_year_id': user_context['fin_year_id'], # Important for filtering
        }
        return get_object_or_404(BOMMaster, **lookup_kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass non-editable fields from the object to the template
        bom_item = self.object
        context['work_order_no'] = bom_item.work_order_no
        context['item_code'] = bom_item.item.item_code if bom_item.item else 'N/A'
        context['manufacturer_description'] = bom_item.item.manufacturer_description if bom_item.item else 'N/A'
        context['uom_basic_symbol'] = bom_item.item.uom_basic.symbol if bom_item.item and bom_item.item.uom_basic else 'N/A'
        return context

    def form_valid(self, form):
        # Retrieve context from request/session
        user_context = get_user_context(self.request)
        self.object.session_id = user_context['session_id']
        self.object.company_id = user_context['company_id']
        self.object.financial_year_id = user_context['fin_year_id']

        new_quantity = form.cleaned_data['quantity']
        new_revision = form.cleaned_data['revision']

        with transaction.atomic(): # Ensure atomicity for update and amendment creation
            result = self.object.perform_update_logic(new_quantity, new_revision)

        if result == 'updated':
            messages.success(self.request, 'BOM item updated successfully.')
            # Redirect to the main BOM Tree View (or list) after update
            # The original redirects to BOM_Design_WO_TreeView_Edit.aspx
            # We'll use a placeholder URL for now, assuming it's a list view for the WO
            success_url = reverse_lazy('bom_master_list') + f"?won={self.object.work_order_no}"
            if self.request.headers.get('HX-Request'):
                # For HTMX requests, respond with 204 No Content and trigger a refresh event
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': f'refreshBOMMasterList, showMessage:{messages.get_messages(self.request)}' # Trigger message on client side
                    }
                )
            return HttpResponseRedirect(success_url)
        elif result == 'ecn_required':
            messages.warning(self.request, 'Design date passed. ECN process required.')
            # Original: Response.Redirect("ECN_Master_Edit.aspx?ItemId=" + itemId + "&WONo=" + wono + "&CId=" + CId + "&ParentId=" + PID + "&Qty=" + txtQuntity.Text + "&Id=" + asslyId + "&Revision=" + txtRevision.Text + "");
            # This is a redirect to another page with specific params.
            # You would define a URL pattern for 'ecn_master_edit' and pass parameters.
            ecn_params = f"ItemId={self.object.item.id}&WONo={self.object.work_order_no}&CId={self.object.category_id}&ParentId={self.object.parent_id}&Qty={new_quantity}&Id={self.object.id}&Revision={new_revision}"
            return HttpResponseRedirect(reverse_lazy('ecn_master_edit') + f'?{ecn_params}')
        else: # result == 'error'
            form.add_error(None, "An error occurred during update. Please try again.")
            messages.error(self.request, 'An internal error occurred during BOM update.')
            return self.form_invalid(form) # Render form with errors

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # If HTMX request, render only the form content to swap into the modal
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return response

class BOMMasterDeleteView(DeleteView):
    """
    Handles deleting a BOM Master item.
    """
    model = BOMMaster
    template_name = 'bom/bommaster/confirm_delete.html'
    success_url = reverse_lazy('bom_master_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'BOM Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshBOMMasterList, showMessage:{messages.get_messages(request)}'
                }
            )
        return response

# Placeholder for ECN view, not fully implemented here but needed for redirection
class ECNMasterEditView(View):
    """
    A placeholder view for the ECN_Master_Edit.aspx redirection.
    In a real system, this would be a proper Django view for ECN.
    """
    def get(self, request, *args, **kwargs):
        messages.info(request, "Redirected to ECN process due to design date validation.")
        # You might want to pass query parameters to the template for display
        context = {
            'item_id': request.GET.get('ItemId'),
            'work_order_no': request.GET.get('WONo'),
            'category_id': request.GET.get('CId'),
            'parent_id': request.GET.get('ParentId'),
            'quantity': request.GET.get('Qty'),
            'bom_id': request.GET.get('Id'),
            'revision': request.GET.get('Revision'),
        }
        return HttpResponse(f"<div class='p-6'><h2>ECN Process Required</h2><p>Design date passed, requiring an Engineering Change Notice.</p><pre>{context}</pre><button class='bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded' hx-on:click='window.history.back()'>Go Back</button></div>")
```

#### 4.4 Templates (`bom/templates/bom/bommaster/`)

Templates are designed for HTMX interaction and adhere to DRY principles by extending `core/base.html`. The edit form (`_form.html`) is a partial, meant to be loaded into a modal.

**`list.html`** (Main list page for BOM Masters, typically for a specific Work Order)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">BOM Items for WO: {{ work_order_no }}</h2>
        {# Add New button is not on the original page, but included for general CRUD #}
        {# <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'bom_master_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New BOM Item
        </button> #}
    </div>
    
    <div id="bommasterTable-container"
         hx-trigger="load, refreshBOMMasterList from:body"
         hx-get="{% url 'bom_master_table' %}?won={{ work_order_no }}"{# Pass WO No to table partial #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading BOM Items...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('messageHandler', () => ({
            init() {
                this.$el.addEventListener('showMessage', (event) => {
                    const messages = JSON.parse(event.detail.value);
                    messages.forEach(msg => {
                        console.log('Django Message:', msg.message, msg.tags);
                        // Implement a toast/notification system here based on msg.tags
                        // For example:
                        // Toastify({ text: msg.message, className: msg.tags }).showToast();
                        alert(`${msg.tags.toUpperCase()}: ${msg.message}`); // Simple alert for demo
                    });
                });
            }
        }));
    });
</script>
{% endblock %}
```

**`_table.html`** (Partial for the DataTables content, loaded via HTMX)

```html
<table id="bommasterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revision</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in bom_masters %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %}">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.item.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.item.manufacturer_description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.item.uom_basic.symbol }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.quantity|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.revision }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                    hx-get="{% url 'bom_master_edit' pk=obj.pk %}?WONo={{ obj.work_order_no }}&ItemId={{ obj.item.id }}&CId={{ obj.category_id }}"{# Pass context params #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'bom_master_delete' pk=obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No BOM items found for this Work Order.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure jQuery and DataTables scripts are loaded in base.html
    // This script block should be in _table.html itself, as it's loaded dynamically
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#bommasterTable')) {
            $('#bommasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "ordering": true,
                "searching": true,
                "paging": true,
                "info": true
            });
        }
    });
</script>
```

**`_form.html`** (Partial for the update form, loaded into a modal)

```html
<div class="p-6 bg-white rounded-lg shadow-md">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} BOM Item - WO No: <span class="font-normal text-blue-600">{{ work_order_no }}</span></h3>
    
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700">Item Code:</label>
        <p class="mt-1 text-sm text-gray-900 font-bold">{{ item_code }}</p>
    </div>
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700">Description:</label>
        <p class="mt-1 text-sm text-gray-900">{{ manufacturer_description }}</p>
    </div>
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700">UOM:</label>
        <p class="mt-1 text-sm text-gray-900">{{ uom_basic_symbol }}</p>
    </div>

    <form hx-post="{{ request.path }}?WONo={{ work_order_no }}&ItemId={{ form.instance.item.id }}&CId={{ form.instance.category_id }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Update
            </button>
            <span id="loadingIndicator" class="htmx-indicator ml-3 text-blue-600">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                Saving...
            </span>
        </div>
    </form>
</div>
```

**`confirm_delete.html`** (Partial for delete confirmation, loaded into a modal)

```html
<div class="p-6 bg-white rounded-lg shadow-md">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete this BOM Item?</p>
    <p class="font-bold text-red-600">{{ object.item.item_code }} - {{ object.item.manufacturer_description }} (WO: {{ object.work_order_no }})</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-delete="{% url 'bom_master_delete' pk=object.pk %}"
            hx-swap="none"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Confirm Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`bom/urls.py`)

Defines the URL patterns for accessing the BOMMaster views. Note the `pk` parameter for update/delete, corresponding to the `Id` (assemblyId) from the original ASP.NET page.

```python
from django.urls import path
from .views import (
    BOMMasterListView,
    BOMMasterTablePartialView,
    BOMMasterUpdateView,
    BOMMasterDeleteView,
    ECNMasterEditView # Placeholder for redirection
)

urlpatterns = [
    # List view for BOM Masters (e.g., filtered by work order)
    path('bom_masters/', BOMMasterListView.as_view(), name='bom_master_list'),
    path('bom_masters/table/', BOMMasterTablePartialView.as_view(), name='bom_master_table'),
    
    # Update view for a specific BOM Item (maps to BOM_Design_Item_Edit.aspx)
    # The 'pk' corresponds to the 'Id' (asslyId) from the ASP.NET QueryString.
    # Other parameters (WONo, ItemId, CId) will be passed as query parameters for get_object lookup.
    path('bom_masters/edit/<int:pk>/', BOMMasterUpdateView.as_view(), name='bom_master_edit'),
    
    # Delete view (not directly in original ASPX, but part of CRUD)
    path('bom_masters/delete/<int:pk>/', BOMMasterDeleteView.as_view(), name='bom_master_delete'),

    # Placeholder URL for ECN redirection
    path('ecn_master_edit/', ECNMasterEditView.as_view(), name='ecn_master_edit'),
]
```

#### 4.6 Tests (`bom/tests.py`)

Comprehensive tests for the models and views ensure the migration's success and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
import json

from .models import BOMMaster, ItemMaster, Unit, WorkOrderMaster, BOMAmendment

# Helper to mock user context for tests
def mock_user_context():
    return {
        'session_id': 'TEST_SESSION',
        'company_id': 101,
        'fin_year_id': 2024
    }

# Patch get_user_context in views for testing purposes
from .views import get_user_context as original_get_user_context
def mock_get_user_context_for_tests(request):
    return mock_user_context()

# Temporarily patch the function for tests
import unittest.mock
patcher = unittest.mock.patch('bom.views.get_user_context', side_effect=mock_get_user_context_for_tests)
patcher.start()

class BOMModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent data first
        cls.unit = Unit.objects.create(symbol='M')
        cls.item = ItemMaster.objects.create(
            item_code='ITM001',
            manufacturer_description='Test Item Description',
            uom_basic=cls.unit,
            category_id=1
        )
        cls.work_order = WorkOrderMaster.objects.create(
            work_order_no='WO123',
            task_design_finalization_date=timezone.now().date() + timezone.timedelta(days=7)
        )
        cls.work_order_old = WorkOrderMaster.objects.create(
            work_order_no='WOOLD',
            task_design_finalization_date=timezone.now().date() - timezone.timedelta(days=7)
        )

        # Create test BOMMaster data
        cls.bom_master = BOMMaster.objects.create(
            work_order_no=cls.work_order.work_order_no,
            item=cls.item,
            category_id=1,
            parent_id=0,
            quantity=Decimal('10.500'),
            revision='A',
            amendment_no=0,
            session_id='TEST_SESSION',
            company_id=101,
            financial_year_id=2024
        )
        cls.bom_master_old_design = BOMMaster.objects.create(
            work_order_no=cls.work_order_old.work_order_no,
            item=cls.item,
            category_id=1,
            parent_id=0,
            quantity=Decimal('5.000'),
            revision='B',
            amendment_no=0,
            session_id='TEST_SESSION',
            company_id=101,
            financial_year_id=2024
        )

    def test_bom_master_creation(self):
        self.assertEqual(self.bom_master.quantity, Decimal('10.500'))
        self.assertEqual(self.bom_master.revision, 'A')
        self.assertEqual(self.bom_master.item.item_code, 'ITM001')
        self.assertEqual(self.bom_master.work_order_no, 'WO123')
        self.assertEqual(self.bom_master.amendment_no, 0)

    def test_get_next_amendment_no(self):
        self.assertEqual(self.bom_master.get_next_amendment_no(), 1)
        self.bom_master.amendment_no = 5
        self.assertEqual(self.bom_master.get_next_amendment_no(), 6)

    def test_is_design_date_valid_future(self):
        self.assertTrue(self.bom_master.is_design_date_valid())

    def test_is_design_date_valid_past(self):
        self.assertFalse(self.bom_master_old_design.is_design_date_valid())

    def test_create_amendment_record(self):
        initial_amendment_count = BOMAmendment.objects.count()
        self.bom_master.create_amendment_record(
            old_qty=Decimal('10.000'),
            old_amendment_no=0,
            description='Old Description',
            uom_symbol='M'
        )
        self.assertEqual(BOMAmendment.objects.count(), initial_amendment_count + 1)
        amendment = BOMAmendment.objects.last()
        self.assertEqual(amendment.bom_master, self.bom_master)
        self.assertEqual(amendment.quantity, Decimal('10.000'))
        self.assertEqual(amendment.amendment_no, 0)

    def test_perform_update_logic_success(self):
        old_qty = self.bom_master.quantity
        old_rev = self.bom_master.revision
        old_amd_no = self.bom_master.amendment_no
        initial_amendment_count = BOMAmendment.objects.count()

        result = self.bom_master.perform_update_logic(Decimal('12.750'), 'B')
        self.assertEqual(result, 'updated')

        self.bom_master.refresh_from_db()
        self.assertEqual(self.bom_master.quantity, Decimal('12.750'))
        self.assertEqual(self.bom_master.revision, 'B')
        self.assertEqual(self.bom_master.amendment_no, old_amd_no + 1)
        self.assertEqual(BOMAmendment.objects.count(), initial_amendment_count + 1)
        amendment = BOMAmendment.objects.last()
        self.assertEqual(amendment.quantity, old_qty)
        self.assertEqual(amendment.amendment_no, old_amd_no)
        self.assertEqual(amendment.description, self.item.manufacturer_description)

    def test_perform_update_logic_ecn_required(self):
        initial_amendment_count = BOMAmendment.objects.count()
        result = self.bom_master_old_design.perform_update_logic(Decimal('6.000'), 'C')
        self.assertEqual(result, 'ecn_required')
        # Ensure no update or amendment record if ECN is required
        self.assertEqual(BOMAmendment.objects.count(), initial_amendment_count)
        self.bom_master_old_design.refresh_from_db()
        self.assertEqual(self.bom_master_old_design.quantity, Decimal('5.000')) # No change
        self.assertEqual(self.bom_master_old_design.revision, 'B') # No change

class BOMViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create test data for views
        cls.unit = Unit.objects.create(id=1, symbol='M')
        cls.item = ItemMaster.objects.create(id=1, item_code='ITM001', manufacturer_description='Test Item', uom_basic=cls.unit, category_id=1)
        cls.work_order = WorkOrderMaster.objects.create(work_order_no='WOVIEWTEST', task_design_finalization_date=timezone.now().date() + timezone.timedelta(days=7))
        cls.bom_master = BOMMaster.objects.create(
            id=1,
            work_order_no=cls.work_order.work_order_no,
            item=cls.item,
            category_id=1,
            parent_id=0,
            quantity=Decimal('10.500'),
            revision='A',
            amendment_no=0,
            session_id='TEST_SESSION',
            company_id=mock_user_context()['company_id'],
            financial_year_id=mock_user_context()['fin_year_id']
        )
        cls.work_order_old = WorkOrderMaster.objects.create(work_order_no='WOVIEWOLD', task_design_finalization_date=timezone.now().date() - timezone.timedelta(days=7))
        cls.bom_master_old_design = BOMMaster.objects.create(
            id=2,
            work_order_no=cls.work_order_old.work_order_no,
            item=cls.item,
            category_id=1,
            parent_id=0,
            quantity=Decimal('5.000'),
            revision='B',
            amendment_no=0,
            session_id='TEST_SESSION',
            company_id=mock_user_context()['company_id'],
            financial_year_id=mock_user_context()['fin_year_id']
        )

    def test_list_view(self):
        response = self.client.get(reverse('bom_master_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom/bommaster/list.html')
        self.assertTrue('bom_masters' in response.context)
        self.assertContains(response, 'BOM Items for WO: N/A') # Default WO if not provided

        response_filtered = self.client.get(reverse('bom_master_list') + f'?won={self.work_order.work_order_no}')
        self.assertEqual(response_filtered.status_code, 200)
        self.assertContains(response_filtered, f'BOM Items for WO: {self.work_order.work_order_no}')

    def test_table_partial_view(self):
        response = self.client.get(reverse('bom_master_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom/bommaster/_table.html')
        self.assertTrue('bom_masters' in response.context)
        self.assertContains(response, self.bom_master.item.item_code)

    def test_update_view_get(self):
        url = reverse('bom_master_edit', args=[self.bom_master.pk]) + f'?WONo={self.bom_master.work_order_no}&ItemId={self.bom_master.item.id}&CId={self.bom_master.category_id}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom/bommaster/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, self.bom_master.quantity)
        self.assertContains(response, self.bom_master.revision)
        self.assertContains(response, 'Edit BOM Item')
        self.assertContains(response, f'WO No: {self.bom_master.work_order_no}')

    def test_update_view_post_success(self):
        initial_bom_count = BOMMaster.objects.count()
        initial_amendment_count = BOMAmendment.objects.count()
        url = reverse('bom_master_edit', args=[self.bom_master.pk]) + f'?WONo={self.bom_master.work_order_no}&ItemId={self.bom_master.item.id}&CId={self.bom_master.category_id}'
        
        data = {
            'quantity': '15.750',
            'revision': 'B',
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBOMMasterList', response.headers['HX-Trigger'])

        self.bom_master.refresh_from_db()
        self.assertEqual(self.bom_master.quantity, Decimal('15.750'))
        self.assertEqual(self.bom_master.revision, 'B')
        self.assertEqual(self.bom_master.amendment_no, 1) # First amendment
        self.assertEqual(BOMAmendment.objects.count(), initial_amendment_count + 1)
        
        messages = json.loads(response.headers['HX-Trigger'])['showMessage']
        self.assertIn("BOM item updated successfully.", messages[0]['message'])

    def test_update_view_post_ecn_required(self):
        initial_bom_count = BOMMaster.objects.count()
        initial_amendment_count = BOMAmendment.objects.count()
        url = reverse('bom_master_edit', args=[self.bom_master_old_design.pk]) + f'?WONo={self.bom_master_old_design.work_order_no}&ItemId={self.bom_master_old_design.item.id}&CId={self.bom_master_old_design.category_id}'
        
        data = {
            'quantity': '6.000',
            'revision': 'C',
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 302) # Redirect to ECN
        self.assertTrue(reverse('ecn_master_edit') in response.url)
        self.assertIn('messages', self.client.session) # Check for messages

        self.bom_master_old_design.refresh_from_db()
        self.assertEqual(self.bom_master_old_design.quantity, Decimal('5.000')) # Quantity should not change
        self.assertEqual(self.bom_master_old_design.revision, 'B') # Revision should not change
        self.assertEqual(BOMAmendment.objects.count(), initial_amendment_count) # No amendment record created

    def test_update_view_post_invalid_form(self):
        url = reverse('bom_master_edit', args=[self.bom_master.pk]) + f'?WONo={self.bom_master.work_order_no}&ItemId={self.bom_master.item.id}&CId={self.bom_master.category_id}'
        
        data = {
            'quantity': 'invalid_qty', # Invalid data
            'revision': '', # Missing
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'bom/bommaster/form.html')
        self.assertContains(response, 'Invalid quantity format.')
        self.assertContains(response, 'Revision is required.')

    def test_delete_view_get(self):
        url = reverse('bom_master_delete', args=[self.bom_master.pk])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom/bommaster/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Delete')

    def test_delete_view_post_success(self):
        initial_count = BOMMaster.objects.count()
        url = reverse('bom_master_delete', args=[self.bom_master.pk])
        response = self.client.delete(url, HTTP_HX_REQUEST='true') # Using DELETE method for HTMX
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBOMMasterList', response.headers['HX-Trigger'])
        self.assertEqual(BOMMaster.objects.count(), initial_count - 1)
        self.assertFalse(BOMMaster.objects.filter(pk=self.bom_master.pk).exists())
        
        messages = json.loads(response.headers['HX-Trigger'])['showMessage']
        self.assertIn("BOM Master deleted successfully.", messages[0]['message'])

    def test_ecn_master_edit_view(self):
        url = reverse('ecn_master_edit') + '?ItemId=1&WONo=WO123&CId=1&ParentId=0&Qty=12.5&Id=1&Revision=X'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ECN Process Required')
        self.assertContains(response, 'ItemId')

# Stop patching after all tests are run
patcher.stop()

```

### Step 5: HTMX and Alpine.js Integration

This plan fully embraces HTMX and Alpine.js for frontend interactivity, eliminating the need for traditional JavaScript frameworks.

*   **HTMX for Modals and Forms:**
    *   The "Edit" and "Delete" buttons on the list view (`_table.html`) use `hx-get` to fetch the respective form/confirmation partials (`_form.html`, `confirm_delete.html`).
    *   `hx-target="#modalContent"` directs the response to the modal container.
    *   `hx-trigger="click"` makes the fetch happen on button click.
    *   `_="on click add .is-active to #modal"` (an Alpine.js/Hyperscript directive) activates the modal display.
    *   Form submissions within the modal (`_form.html`) use `hx-post` (or `hx-delete` for delete confirmation).
    *   `hx-swap="none"` prevents replacing the whole page, and `hx-trigger="refreshBOMMasterList"` tells the list view to refresh its `_table.html` content after a successful CRUD operation. This creates a seamless single-page application feel without complex JS.
    *   Error responses or invalid form submissions from the view (`form_invalid` method) will re-render just the `_form.html` partial, allowing HTMX to swap it back into the modal, displaying validation errors.
    *   `hx-indicator` is used to show a loading spinner during HTMX requests.

*   **Alpine.js for UI State and Modals:**
    *   Alpine.js is used to manage the visibility of the modal (`x-data` and `x-show` on the modal div, combined with Hyperscript `_` for cleaner syntax).
    *   `on click if event.target.id == 'modal' remove .is-active from me` allows clicking outside the modal content to close it.
    *   `on click remove .is-active from #modal` on cancel/close buttons.
    *   A simple Alpine.js component (`messageHandler`) is used to listen for `showMessage` custom events triggered by HTMX responses, allowing the display of Django messages.

*   **DataTables for List Views:**
    *   The `_table.html` partial contains the `<table>` element.
    *   A small `script` block within `_table.html` initializes DataTables on `$(document).ready()`. This ensures DataTables is applied correctly even when the table is loaded dynamically via HTMX.
    *   DataTables provides client-side sorting, searching, and pagination without any server-side logic required beyond providing the raw data.

*   **No Additional JavaScript:** The entire frontend interaction logic is handled by HTMX and Alpine.js directives, eliminating the need for custom, imperative JavaScript.

### Final Notes

*   **Placeholders:** `DUMMY_SESSION_ID`, `1` (for company_id), `2023` (for financial_year_id) are placeholders in `get_user_context`. In a real application, these would be dynamically retrieved from the authenticated Django user's profile or session management.
*   **Tree View Navigation:** The original `BOM_Design_WO_TreeView_Edit.aspx` is a critical navigation component. In Django, this would likely be a separate view, potentially using a Django-treebeard or MPTT implementation, which would then link to `bom_master_list` (filtered by WO) and then `bom_master_edit` for specific items. The `success_url` in the `UpdateView` is configured to redirect back to the `bom_master_list` for the relevant work order.
*   **ECN Process:** The redirection to `ECN_Master_Edit.aspx` is handled by Django's `HttpResponseRedirect` to a placeholder `ecn_master_edit` URL. A full migration would involve creating a dedicated Django application and views for the ECN process.
*   **Error Handling:** While `try-catch` blocks are present in the original C# code, Django's robust form validation, ORM error handling, and `messages` framework provide a more idiomatic way to manage errors and provide user feedback. Transactions are used to ensure data integrity during updates.
*   **Security:** Ensure proper authentication and authorization are implemented in Django (e.g., using Django's built-in `LoginRequiredMixin` and custom permission mixins for views, and `request.user` for session/company/finyear IDs).