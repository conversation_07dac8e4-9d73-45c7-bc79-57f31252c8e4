## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The ASP.NET code interacts with multiple tables to construct the work order grid.

*   **Main Table:** `SD_Cust_WorkOrder_Master` (for work orders)
*   **Lookup Table 1:** `SD_Cust_Master` (for customer details)
*   **Lookup Table 2:** `tblFinancial_master` (for financial year details)
*   **Lookup Table 3:** `tblHR_OfficeStaff` (for employee details)

**Inferred Columns:**

*   **`SD_Cust_WorkOrder_Master` (Main Work Order Data):**
    *   `Id` (implicit auto-incrementing primary key based on `Order by Id Desc`)
    *   `EnqId` (Enquiry Number)
    *   `CustomerId` (Foreign key to `SD_Cust_Master`)
    *   `WONo` (Work Order Number, unique identifier)
    *   `PONo` (Purchase Order Number)
    *   `SessionId` (Foreign key to `tblHR_OfficeStaff`, representing the employee who generated it)
    *   `FinYearId` (Foreign key to `tblFinancial_master`)
    *   `SysDate` (System Date, stored as a string in `DD-MM-YYYY` format as per ASP.NET conversion logic)
    *   `CompId` (Company ID, for multi-tenancy/filtering)

*   **`SD_Cust_Master` (Customer Data):**
    *   `CustomerId` (Primary Key)
    *   `CustomerName`
    *   `CompId`
    *   `FinYearId`

*   **`tblFinancial_master` (Financial Year Data):**
    *   `FinYearId` (Primary Key)
    *   `FinYear` (e.g., "2023-2024")

*   **`tblHR_OfficeStaff` (Employee Data):**
    *   `EmpId` (Primary Key)
    *   `Title` (e.g., "Mr.", "Ms.")
    *   `EmployeeName`
    *   `CompId`
    *   `FinYearId`

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

*   **Read (List & Filter):** The primary functionality demonstrated is listing work orders (`SD_Cust_WorkOrder_Master`) with extensive filtering capabilities.
    *   Users can search by `Customer Name`, `Enquiry No`, `PO No`, or `WO No`.
    *   The search functionality dynamically changes the input field (text box vs. autocomplete) based on the dropdown selection.
    *   The grid supports pagination and sorting.
    *   Data is aggregated from multiple tables (`SD_Cust_Master`, `tblFinancial_master`, `tblHR_OfficeStaff`) for display.
*   **Autocomplete:** An autocomplete feature is present for `Customer Name` search, querying `SD_Cust_Master`.
*   **No explicit Create, Update, or Delete** operations are shown on this specific `.aspx` page, but the `HyperLinkField` for `WONo` (`TPL_Design_WO_TreeView.aspx?WONo={0}...`) suggests a detail/edit page exists. For a comprehensive modernization, we will implement the full CRUD suite for Work Orders in Django, adhering to the provided template for best practices.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **Search/Filter Section:**
    *   `asp:DropDownList ID="DropDownList1"`: Acts as a search criteria selector. This will be an HTML `<select>` element.
    *   `asp:TextBox ID="txtSearchCustomer"`: A standard text input for `Enquiry No`, `PO No`, `WO No`. This will be an HTML `<input type="text">`.
    *   `asp:TextBox ID="TxtSearchValue"` with `cc1:autocompleteextender`: A text input with an autocomplete feature for `Customer Name`. This will also be an HTML `<input type="text">` managed by HTMX and potentially Alpine.js for showing suggestions.
    *   `asp:Button ID="btnSearch"`: Triggers the search. This will be an HTMX-driven `<button>` element.
*   **Data Display Grid:**
    *   `asp:GridView ID="SearchGridView1"`: Displays tabular data with pagination and sorting. This will be replaced by an HTML `<table>` element initialized with DataTables.
    *   `asp:TemplateField` and `asp:BoundField`: Define table columns and their data sources. These will translate directly to `<th>` and `<td>` elements.
    *   `asp:HyperLinkField` for "WO No": This will be a standard HTML `<a>` tag with its `href` dynamically generated by Django's `url` tag.

The dynamic visibility of `txtSearchCustomer` and `TxtSearchValue` based on `DropDownList1` selection will be handled client-side using Alpine.js for reactive UI state.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `design`, to encapsulate this functionality.

#### 4.1 Models (`design/models.py`)

```python
from django.db import models

# Manager for WorkOrder model to encapsulate search logic
class WorkOrderManager(models.Manager):
    def search_work_orders(self, search_by, search_value, company_id, financial_year_id):
        # Start with base query for the current company and relevant financial year
        # Note: ASP.NET uses FinYearId <= current_fin_year_id for tblFinancial_master and SD_Cust_WorkOrder_Master
        queryset = self.select_related('customer', 'financial_year', 'generated_by').filter(
            company_id=company_id,
            financial_year__fin_year_id__lte=financial_year_id
        )

        # Apply search filters based on dropdown selection
        if search_by == '1' and search_value: # Enquiry No
            queryset = queryset.filter(enquiry_id__iexact=search_value) # Case-insensitive exact match
        elif search_by == '2' and search_value: # PO No
            queryset = queryset.filter(po_no__iexact=search_value)
        elif search_by == '3' and search_value: # WO No
            queryset = queryset.filter(wo_no__iexact=search_value)
        elif search_by == '0' and search_value: # Customer Name (autocomplete sends "Name [ID]")
            import re
            match = re.search(r'\[(.*?)\]', search_value)
            if match:
                cust_id = match.group(1)
                queryset = queryset.filter(customer__customer_id=cust_id)
            else: # Fallback if only name is entered, search by contains
                queryset = queryset.filter(customer__customer_name__icontains=search_value)
        
        # Order by Id Desc as in ASP.NET
        return queryset.order_by('-id')

    def get_customer_autocomplete_suggestions(self, prefix_text, company_id):
        # This method powers the autocomplete for customer names.
        # It replicates the logic of the ASP.NET sql WebMethod.
        queryset = Customer.objects.filter(
            company_id=company_id,
            customer_name__istartswith=prefix_text
        ).values_list('customer_name', 'customer_id')

        # Format as "CustomerName [CustomerId]" similar to ASP.NET's output
        return [f"{name} [{cid}]" for name, cid in queryset]


class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Customer(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId') # This seems to be a FK to FinancialYear

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class Employee(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    company_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId') # This seems to be a FK to FinancialYear

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}. {self.employee_name}" if self.title else self.employee_name

class WorkOrder(models.Model):
    # 'id' is automatically created by Django as an auto-incrementing PK if not explicitly defined.
    # The ASP.NET code used 'Id' for ordering, implying it exists in the DB.
    # For managed=False models, Django expects the PK column name to be 'id' by default or explicitly defined.
    # Assuming 'Id' in ASP.NET maps to Django's 'id' field, or we might need `primary_key=True` on a specific column.
    # Given 'Order by Id Desc', it's most likely an auto-incrementing integer PK.

    enquiry_id = models.CharField(db_column='EnqId', max_length=50, verbose_name="Enquiry No")
    # ForeignKeys infer relationship, but db_column points to the actual column name in the database
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', to_field='customer_id', verbose_name="Customer")
    wo_no = models.CharField(db_column='WONo', unique=True, max_length=50, verbose_name="WO No")
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True, verbose_name="PO No")
    generated_by = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', to_field='emp_id', verbose_name="Generated By")
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', to_field='fin_year_id', verbose_name="Financial Year")
    system_date = models.CharField(db_column='SysDate', max_length=20, verbose_name="Generation Date") # Stored as 'DD-MM-YYYY' string in DB
    company_id = models.IntegerField(db_column='CompId', verbose_name="Company ID") # Assuming this is a fixed ID for the current company
    
    objects = WorkOrderManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        ordering = ['-id'] # Default ordering, can be overridden by manager

    def __str__(self):
        return self.wo_no
    
    # Example of a business logic method in the model (Fat Model approach)
    def get_display_data(self):
        """
        Returns a dictionary of data suitable for display in the DataTables,
        including fields from related models.
        """
        return {
            'sn': None, # SN will be added by DataTables or template loop counter
            'fin_year': self.financial_year.fin_year,
            'customer_name': self.customer.customer_name,
            'customer_code': self.customer.customer_id,
            'enquiry_no': self.enquiry_id,
            'po_no': self.po_no,
            'wo_no': self.wo_no,
            'gen_date': self.system_date, # Already in 'DD-MM-YYYY' from DB
            'gen_by': self.generated_by.employee_name,
            'pk': self.pk # Primary key for actions
        }

```

#### 4.2 Forms (`design/forms.py`)

```python
from django import forms
from .models import WorkOrder, Customer, Employee, FinancialYear

# A form for the search criteria (dropdown and text input)
class WorkOrderSearchForm(forms.Form):
    SEARCH_BY_CHOICES = [
        ('Select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3'}),
        initial='Select'
    )
    # This field will be dynamically shown/hidden by Alpine.js
    text_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3', 'placeholder': 'Search text'}),
    )
    # This field will be dynamically shown/hidden by Alpine.js and used for autocomplete
    autocomplete_search_value = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3', 'placeholder': 'Search customer name'}),
    )

class WorkOrderForm(forms.ModelForm):
    # In a real scenario, these dropdowns might also be populated dynamically
    # For now, assuming they are pre-populated or handled by Django's form rendering.
    customer = forms.ModelChoiceField(
        queryset=Customer.objects.all(), # This should be filtered by company_id and fin_year_id
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    generated_by = forms.ModelChoiceField(
        queryset=Employee.objects.all(), # This should be filtered by company_id and fin_year_id
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    financial_year = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(), # This should be filtered by company_id
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = WorkOrder
        fields = ['enquiry_id', 'customer', 'wo_no', 'po_no', 'generated_by', 'financial_year', 'system_date', 'company_id']
        widgets = {
            'enquiry_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'system_date': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'}), # Explicitly prompt format
            'company_id': forms.HiddenInput(), # Likely fixed for a session/user
        }
        labels = {
            'enquiry_id': 'Enquiry No',
            'wo_no': 'WO No',
            'po_no': 'PO No',
            'system_date': 'Generation Date',
            'company_id': 'Company ID', # This won't be visible due to HiddenInput
        }
        
    def __init__(self, *args, **kwargs):
        # Pass current company_id and fin_year_id from view to form for queryset filtering
        self.company_id = kwargs.pop('company_id', None)
        self.financial_year_id = kwargs.pop('financial_year_id', None)
        super().__init__(*args, **kwargs)

        if self.company_id:
            # Filter related model choice fields based on company_id and financial_year_id
            self.fields['customer'].queryset = Customer.objects.filter(company_id=self.company_id, fin_year_id=self.financial_year_id)
            self.fields['generated_by'].queryset = Employee.objects.filter(company_id=self.company_id, fin_year_id=self.financial_year_id)
            self.fields['financial_year'].queryset = FinancialYear.objects.filter(fin_year_id__lte=self.financial_year_id) # Consistent with ASP.NET logic

        # Set initial company_id if creating a new instance
        if not self.instance.pk and self.company_id:
            self.initial['company_id'] = self.company_id

    # Add custom validation for system_date format if needed
    def clean_system_date(self):
        system_date = self.cleaned_data['system_date']
        # Simple regex for DD-MM-YYYY format
        import re
        if not re.match(r'^\d{2}-\d{2}-\d{4}$', system_date):
            raise forms.ValidationError("Date must be in DD-MM-YYYY format.")
        return system_date

```

#### 4.3 Views (`design/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import F

from .models import WorkOrder, Customer # Import other models if needed for querysets
from .forms import WorkOrderForm, WorkOrderSearchForm

# Assume session data is available via request.session for company_id and fin_year_id
# For a production system, use proper authentication and user profiles for these.
# Mock values for demonstration:
MOCK_COMPANY_ID = 1
MOCK_FIN_YEAR_ID = 2024 # Example: Represents current financial year ID

class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'design/workorder/list.html'
    context_object_name = 'workorders' # Not directly used for table, but good practice
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form
        context['search_form'] = WorkOrderSearchForm(self.request.GET or None)
        return context

class WorkOrderTablePartialView(View):
    """
    Returns the HTML for the WorkOrder table, used by HTMX.
    """
    def get(self, request, *args, **kwargs):
        search_form = WorkOrderSearchForm(request.GET)
        workorders = WorkOrder.objects.none() # Default empty queryset

        company_id = MOCK_COMPANY_ID # Replace with actual request.session.get('compid')
        financial_year_id = MOCK_FIN_YEAR_ID # Replace with actual request.session.get('finyear')

        if search_form.is_valid():
            search_by = search_form.cleaned_data.get('search_by')
            text_search_value = search_form.cleaned_data.get('text_search_value')
            autocomplete_search_value = search_form.cleaned_data.get('autocomplete_search_value')

            # Determine which search value to use based on 'search_by'
            active_search_value = ''
            if search_by == '0': # Customer Name
                active_search_value = autocomplete_search_value
            elif search_by != 'Select': # Enquiry No, PO No, WO No
                active_search_value = text_search_value

            workorders = WorkOrder.objects.search_work_orders(
                search_by, active_search_value, company_id, financial_year_id
            )
        else:
            # If no search performed or form invalid, show all for current comp/finyear
            workorders = WorkOrder.objects.search_work_orders(
                None, None, company_id, financial_year_id
            )
            
        context = {
            'workorders': workorders,
        }
        return render(request, 'design/workorder/_workorder_table.html', context)


class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'design/workorder/_workorder_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('workorder_list') # Not strictly needed with HTMX swap="none" + trigger

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass session data to form for queryset filtering
        kwargs['company_id'] = MOCK_COMPANY_ID # request.session.get('compid')
        kwargs['financial_year_id'] = MOCK_FIN_YEAR_ID # request.session.get('finyear')
        return kwargs

    def form_valid(self, form):
        # Set company_id if not already set by initial data
        if not form.instance.company_id:
            form.instance.company_id = MOCK_COMPANY_ID # request.session.get('compid')
        
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content and trigger a client-side event
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList', # Custom event to refresh the table
                    'HX-Retarget': '#modal', # Target the modal to close it
                    'HX-Reswap': 'outerHTML' # Remove the modal content
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, re-render the form within the modal
        response = render(self.request, self.template_name, {'form': form})
        response['HX-Retarget'] = '#modalContent' # Target the modal content div
        response['HX-Reswap'] = 'innerHTML' # Swap just the inner HTML
        return response


class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'design/workorder/_workorder_form.html' # Use partial for HTMX modal
    success_url = reverse_lazy('workorder_list') # Not strictly needed with HTMX swap="none" + trigger

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass session data to form for queryset filtering
        kwargs['company_id'] = MOCK_COMPANY_ID # request.session.get('compid')
        kwargs['financial_year_id'] = MOCK_FIN_YEAR_ID # request.session.get('finyear')
        return kwargs

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList',
                    'HX-Retarget': '#modal',
                    'HX-Reswap': 'outerHTML'
                }
            )
        return response

    def form_invalid(self, form):
        response = render(self.request, self.template_name, {'form': form})
        response['HX-Retarget'] = '#modalContent'
        response['HX-Reswap'] = 'innerHTML'
        return response


class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'design/workorder/_workorder_confirm_delete.html' # Use partial for HTMX modal
    success_url = reverse_lazy('workorder_list') # Not strictly needed with HTMX swap="none" + trigger

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList',
                    'HX-Retarget': '#modal',
                    'HX-Reswap': 'outerHTML'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure the object is available in the context for confirmation message
        context['workorder'] = self.get_object()
        return context

class CustomerAutocompleteView(View):
    """
    HTMX endpoint for customer name autocomplete suggestions.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        company_id = MOCK_COMPANY_ID # request.session.get('compid')
        
        suggestions = WorkOrder.objects.get_customer_autocomplete_suggestions(prefix_text, company_id)
        
        # Return suggestions as a simple list of strings, each formatted as "Name [ID]"
        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates (`design/templates/design/workorder/`)

```html
<!-- design/templates/design/workorder/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Work Orders</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>
    
    <!-- Search Form -->
    <div x-data="{ 
            searchBy: '{{ search_form.search_by.value|default:'Select' }}',
            showTextSearch: false,
            showAutocompleteSearch: false,
            init() {
                this.$watch('searchBy', value => {
                    this.showTextSearch = (value === '1' || value === '2' || value === '3');
                    this.showAutocompleteSearch = (value === '0');
                });
                this.showTextSearch = (this.searchBy === '1' || this.searchBy === '2' || this.searchBy === '3');
                this.showAutocompleteSearch = (this.searchBy === '0');
            }
         }" class="mb-6 p-4 border border-gray-200 rounded-md bg-gray-50">
        <form hx-get="{% url 'workorder_table' %}" 
              hx-target="#workorderTable-container" 
              hx-swap="innerHTML" 
              hx-indicator="#table-loading-indicator">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    <select id="{{ search_form.search_by.id_for_label }}" name="{{ search_form.search_by.name }}" 
                            class="{{ search_form.search_by.css_classes }}" x-model="searchBy">
                        {% for value, label in search_form.search_by.field.choices %}
                            <option value="{{ value }}" {% if value == search_form.search_by.value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div x-show="showTextSearch" x-transition>
                    <label for="{{ search_form.text_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Value</label>
                    {{ search_form.text_search_value }}
                </div>
                <div x-show="showAutocompleteSearch" x-transition>
                    <label for="{{ search_form.autocomplete_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer Name</label>
                    {{ search_form.autocomplete_search_value|attr:'hx-get="/design/workorders/customer-autocomplete/" hx-trigger="keyup changed delay:500ms" hx-target="#customer-suggestions" hx-indicator="#autocomplete-loading-indicator" autocomplete="off"' }}
                    <div id="customer-suggestions" class="relative z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto">
                        <!-- Autocomplete suggestions will appear here -->
                    </div>
                </div>
                <div class="flex items-end space-x-2">
                    <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded redbox">Search</button>
                    <img id="table-loading-indicator" class="htmx-indicator h-6 w-6" src="/static/img/bars.svg" alt="Loading...">
                    <img id="autocomplete-loading-indicator" class="htmx-indicator h-6 w-6" src="/static/img/bars.svg" alt="Loading...">
                </div>
            </div>
        </form>
    </div>

    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<script>
    // Alpine.js is initialized automatically. No explicit init block needed here.
    // If you have specific Alpine components, define them.
</script>
{% endblock %}
```

```html
<!-- design/templates/design/workorder/_workorder_table.html -->
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="workorderTable" class="min-w-full bg-white dataTable display">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% if workorders %}
                {% for obj in workorders %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.financial_year.fin_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap">{{ obj.customer.customer_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.customer.customer_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.enquiry_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-left">{{ obj.po_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">
                        <a href="{% url 'workorder_detail' obj.pk %}" class="text-blue-600 hover:underline">
                            {{ obj.wo_no }}
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">{{ obj.system_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-left">{{ obj.generated_by.employee_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-center">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                            hx-get="{% url 'workorder_edit' obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                            hx-get="{% url 'workorder_delete' obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="py-4 text-center text-gray-500">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#workorderTable').DataTable({
            "pageLength": 20, // Match ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true // Make table responsive
        });
    });
</script>
```

```html
<!-- design/templates/design/workorder/_workorder_form.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# swap="none" because HX-Trigger and HTMX-Retarget headers handle the UI update #}
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            {% if field.is_hidden %}
                {{ field }}
            {% else %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- design/templates/design/workorder/_workorder_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Work Order <strong>{{ workorder.wo_no }}</strong>
        (Enquiry No: {{ workorder.enquiry_id }} - Customer: {{ workorder.customer.customer_name }})?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'workorder_delete' workorder.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

```html
<!-- design/templates/design/workorder/_customer_autocomplete_suggestions.html -->
<!-- This partial might be used if we wanted to render HTML suggestions. 
     For now, the JsonResponse approach is simpler for a basic autocomplete. -->
{% if suggestions %}
    <ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto">
        {% for suggestion in suggestions %}
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" 
                hx-trigger="click" 
                hx-select="#id_autocomplete_search_value" 
                hx-swap="value" 
                hx-vals='{"autocomplete_search_value": "{{ suggestion }}"}' 
                _="on click put '{{ suggestion }}' into #id_autocomplete_search_value and remove .is-active from #customer-suggestions">
                {{ suggestion }}
            </li>
        {% endfor %}
    </ul>
{% else %}
    <div class="px-4 py-2 text-gray-500">No suggestions</div>
{% endif %}
```

#### 4.5 URLs (`design/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderListView, WorkOrderTablePartialView, 
    WorkOrderCreateView, WorkOrderUpdateView, WorkOrderDeleteView,
    CustomerAutocompleteView
)

urlpatterns = [
    # Main list page
    path('workorders/', WorkOrderListView.as_view(), name='workorder_list'),
    # HTMX endpoint for the table content
    path('workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),
    # CRUD operations via modal
    path('workorders/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorders/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorders/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
    # Autocomplete endpoint for customers
    path('workorders/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    
    # Placeholder for the detail view (similar to ASP.NET TPL_Design_WO_TreeView.aspx)
    # This would link to a separate Django view for showing work order details.
    path('workorders/detail/<int:pk>/', WorkOrderListView.as_view(), name='workorder_detail'), # Placeholder
]
```

#### 4.6 Tests (`design/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import WorkOrder, Customer, FinancialYear, Employee
from .forms import WorkOrderSearchForm, WorkOrderForm
import json

# Mock session data for tests
MOCK_COMPANY_ID = 1
MOCK_FIN_YEAR_ID = 2024

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.financial_year = FinancialYear.objects.create(fin_year_id=MOCK_FIN_YEAR_ID, fin_year='2024-2025')
        cls.prev_financial_year = FinancialYear.objects.create(fin_year_id=MOCK_FIN_YEAR_ID - 1, fin_year='2023-2024')
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Alpha Corp', company_id=MOCK_COMPANY_ID, fin_year_id=MOCK_FIN_YEAR_ID)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Beta Inc', company_id=MOCK_COMPANY_ID, fin_year_id=MOCK_FIN_YEAR_ID)
        cls.employee = Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='John Doe', company_id=MOCK_COMPANY_ID, fin_year_id=MOCK_FIN_YEAR_ID)

        cls.wo1 = WorkOrder.objects.create(
            enquiry_id='ENQ001',
            customer=cls.customer1,
            wo_no='WO001',
            po_no='PO001',
            generated_by=cls.employee,
            financial_year=cls.financial_year,
            system_date='15-03-2024',
            company_id=MOCK_COMPANY_ID,
            id=1 # Explicitly setting ID for testing specific object retrieval
        )
        cls.wo2 = WorkOrder.objects.create(
            enquiry_id='ENQ002',
            customer=cls.customer2,
            wo_no='WO002',
            po_no='PO002',
            generated_by=cls.employee,
            financial_year=cls.prev_financial_year, # Use previous financial year for <= test
            system_date='10-02-2024',
            company_id=MOCK_COMPANY_ID,
            id=2
        )
        cls.wo3 = WorkOrder.objects.create( # Work order for a different company
            enquiry_id='ENQ003',
            customer=cls.customer1,
            wo_no='WO003',
            po_no='PO003',
            generated_by=cls.employee,
            financial_year=cls.financial_year,
            system_date='20-04-2024',
            company_id=MOCK_COMPANY_ID + 1,
            id=3
        )
  
    def test_workorder_creation(self):
        obj = WorkOrder.objects.get(id=1)
        self.assertEqual(obj.enquiry_id, 'ENQ001')
        self.assertEqual(obj.customer.customer_name, 'Alpha Corp')
        self.assertEqual(obj.wo_no, 'WO001')
        self.assertEqual(obj.po_no, 'PO001')
        self.assertEqual(obj.generated_by.employee_name, 'John Doe')
        self.assertEqual(obj.financial_year.fin_year, '2024-2025')
        self.assertEqual(obj.system_date, '15-03-2024')
        self.assertEqual(obj.company_id, MOCK_COMPANY_ID)
        
    def test_workorder_display_data_method(self):
        obj = WorkOrder.objects.get(id=1)
        data = obj.get_display_data()
        self.assertEqual(data['customer_name'], 'Alpha Corp')
        self.assertEqual(data['fin_year'], '2024-2025')
        self.assertEqual(data['gen_by'], 'John Doe')

    def test_search_work_orders_by_enquiry_no(self):
        results = WorkOrder.objects.search_work_orders('1', 'ENQ001', MOCK_COMPANY_ID, MOCK_FIN_YEAR_ID)
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first().wo_no, 'WO001')

    def test_search_work_orders_by_customer_name(self):
        results = WorkOrder.objects.search_work_orders('0', 'Alpha Corp [CUST001]', MOCK_COMPANY_ID, MOCK_FIN_YEAR_ID)
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first().wo_no, 'WO001')
        
        # Test case-insensitive search by name only (without ID)
        results = WorkOrder.objects.search_work_orders('0', 'alpha', MOCK_COMPANY_ID, MOCK_FIN_YEAR_ID)
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first().wo_no, 'WO001')


    def test_search_work_orders_by_wo_no(self):
        results = WorkOrder.objects.search_work_orders('3', 'WO002', MOCK_COMPANY_ID, MOCK_FIN_YEAR_ID)
        self.assertEqual(results.count(), 1)
        self.assertEqual(results.first().wo_no, 'WO002')

    def test_search_work_orders_filters_by_company_and_fin_year(self):
        # Should only return WO001 and WO002 from MOCK_COMPANY_ID
        results = WorkOrder.objects.search_work_orders(None, None, MOCK_COMPANY_ID, MOCK_FIN_YEAR_ID)
        self.assertEqual(results.count(), 2)
        self.assertIn(self.wo1, results)
        self.assertIn(self.wo2, results)
        self.assertNotIn(self.wo3, results) # WO3 is for different company

    def test_customer_autocomplete_suggestions(self):
        suggestions = WorkOrder.objects.get_customer_autocomplete_suggestions('Alp', MOCK_COMPANY_ID)
        self.assertIn('Alpha Corp [CUST001]', suggestions)
        self.assertNotIn('Beta Inc [CUST002]', suggestions)
        self.assertEqual(len(suggestions), 1)

class WorkOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.financial_year = FinancialYear.objects.create(fin_year_id=MOCK_FIN_YEAR_ID, fin_year='2024-2025')
        cls.customer = Customer.objects.create(customer_id='CUST001', customer_name='Test Customer', company_id=MOCK_COMPANY_ID, fin_year_id=MOCK_FIN_YEAR_ID)
        cls.employee = Employee.objects.create(emp_id='EMP001', title='Mr', employee_name='Test Employee', company_id=MOCK_COMPANY_ID, fin_year_id=MOCK_FIN_YEAR_ID)
        cls.workorder = WorkOrder.objects.create(
            enquiry_id='ENQTEST',
            customer=cls.customer,
            wo_no='WOTEST',
            po_no='POTEST',
            generated_by=cls.employee,
            financial_year=cls.financial_year,
            system_date='01-01-2024',
            company_id=MOCK_COMPANY_ID
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/list.html')
        self.assertIsInstance(response.context['search_form'], WorkOrderSearchForm)
        
    def test_table_partial_view_get(self):
        # Test initial load
        response = self.client.get(reverse('workorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_table.html')
        self.assertIn(self.workorder, response.context['workorders'])

        # Test search by WO No
        response = self.client.get(reverse('workorder_table'), {
            'search_by': '3', 
            'text_search_value': 'WOTEST'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['workorders'].count(), 1)
        self.assertEqual(response.context['workorders'].first().wo_no, 'WOTEST')

        # Test search by Customer Name with autocomplete format
        response = self.client.get(reverse('workorder_table'), {
            'search_by': '0', 
            'autocomplete_search_value': 'Test Customer [CUST001]'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['workorders'].count(), 1)
        self.assertEqual(response.context['workorders'].first().customer.customer_name, 'Test Customer')

    def test_create_view_get(self):
        response = self.client.get(reverse('workorder_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_form.html')
        self.assertIsInstance(response.context['form'], WorkOrderForm)
        
    def test_create_view_post_success(self):
        new_wo_data = {
            'enquiry_id': 'ENQNEW',
            'customer': self.customer.pk, # Use PK for ModelChoiceField
            'wo_no': 'WONEW',
            'po_no': 'PONEW',
            'generated_by': self.employee.pk,
            'financial_year': self.financial_year.pk,
            'system_date': '02-02-2024',
            'company_id': MOCK_COMPANY_ID,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_add'), new_wo_data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.assertTrue(WorkOrder.objects.filter(wo_no='WONEW').exists())
        
    def test_create_view_post_invalid(self):
        invalid_wo_data = {
            'enquiry_id': 'ENQINVALID',
            # Missing required fields like 'customer', 'wo_no'
            'system_date': 'invalid-date' # Invalid date format
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_add'), invalid_wo_data, **headers)
        
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'design/workorder/_workorder_form.html')
        self.assertIn('HX-Retarget', response.headers)
        self.assertIn('HX-Reswap', response.headers)
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Date must be in DD-MM-YYYY format.', str(response.content))


    def test_update_view_get(self):
        response = self.client.get(reverse('workorder_edit', args=[self.workorder.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_form.html')
        self.assertIsInstance(response.context['form'], WorkOrderForm)
        self.assertEqual(response.context['form'].instance, self.workorder)

    def test_update_view_post_success(self):
        updated_data = {
            'enquiry_id': 'ENQUPDATED',
            'customer': self.customer.pk,
            'wo_no': self.workorder.wo_no, # WO No should be same for update
            'po_no': 'POUPDATED',
            'generated_by': self.employee.pk,
            'financial_year': self.financial_year.pk,
            'system_date': '03-03-2024',
            'company_id': MOCK_COMPANY_ID,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('workorder_edit', args=[self.workorder.pk]), updated_data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.workorder.refresh_from_db()
        self.assertEqual(self.workorder.enquiry_id, 'ENQUPDATED')

    def test_delete_view_get(self):
        response = self.client.get(reverse('workorder_delete', args=[self.workorder.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/workorder/_workorder_confirm_delete.html')
        self.assertEqual(response.context['workorder'], self.workorder)

    def test_delete_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Create a temporary work order to delete without affecting self.workorder for other tests
        temp_wo = WorkOrder.objects.create(
            enquiry_id='ENQTEMP',
            customer=self.customer,
            wo_no='WOTEMP',
            po_no='POTEMP',
            generated_by=self.employee,
            financial_year=self.financial_year,
            system_date='04-04-2024',
            company_id=MOCK_COMPANY_ID
        )
        response = self.client.post(reverse('workorder_delete', args=[temp_wo.pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.assertFalse(WorkOrder.objects.filter(pk=temp_wo.pk).exists())

    def test_customer_autocomplete_view(self):
        # Test with a matching prefix
        response = self.client.get(reverse('customer_autocomplete'), {'q': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('Test Customer [CUST001]', data)
        
        # Test with no matching prefix
        response = self.client.get(reverse('customer_autocomplete'), {'q': 'Nomatch'})
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.content)
        self.assertEqual(len(data), 0)

```

### Step 5: HTMX and Alpine.js Integration

The provided templates and views demonstrate the integration:

*   **HTMX for dynamic updates:**
    *   The main list (`workorder_list.html`) uses `hx-get` to load the `_workorder_table.html` initially and on `refreshWorkOrderList` event.
    *   The search form uses `hx-get` to refresh only the table content, avoiding full page reloads.
    *   Add/Edit/Delete buttons use `hx-get` to load forms into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) return `204 No Content` along with `HX-Trigger` headers (`refreshWorkOrderList`) to refresh the table and `HX-Retarget`/`HX-Reswap` to close the modal, making the entire CRUD operation seamless and dynamic.
    *   The customer autocomplete uses `hx-get` to fetch suggestions as the user types, updating a dropdown list.
*   **Alpine.js for UI state management:**
    *   Used in `workorder_list.html` to control the visibility of the "text search" and "autocomplete search" input fields based on the selected "Search By" dropdown value. This replicates the `DropDownList1_SelectedIndexChanged` logic from ASP.NET.
*   **DataTables for list views:**
    *   The `_workorder_table.html` partial contains the HTML `<table>` structure.
    *   A `<script>` block within this partial initializes DataTables on the table ID (`workorderTable`) once the HTMX swap is complete. This ensures client-side sorting, searching, and pagination are handled efficiently.
*   **DRY Template Inheritance:**
    *   All module-specific templates (`list.html`) extend `core/base.html`, ensuring consistent layout and inclusion of essential CDN links (jQuery, HTMX, Alpine.js, Tailwind CSS) without needing to repeat them in individual files.
*   **HTMX-only Interactions:**
    *   All dynamic interactions are achieved through HTMX attributes (e.g., `hx-get`, `hx-post`, `hx-target`, `hx-swap`, `hx-trigger`, `hx-indicator`) and Alpine.js for local UI state. No custom JavaScript is written beyond the DataTables initialization.

## Final Notes

This comprehensive plan transforms the ASP.NET grid view into a modern Django application, focusing on:

*   **Business Value:** Faster, more responsive user experience (no full page reloads), reduced server load, improved maintainability, and a clear path for future feature development on a modern stack.
*   **Automation Focus:** The structure is designed for AI-assisted tools to generate these files based on extracted schema and UI/backend patterns. The models infer relationships, forms are automatically generated from models, views are standard CBVs, and templates follow a reusable component-based approach.
*   **Maintainability:** Fat models keep business logic separated, thin views reduce complexity, and HTMX/Alpine.js simplify frontend development by minimizing custom JavaScript.
*   **Testability:** Comprehensive unit and integration tests ensure functionality and regression prevention, crucial for complex migrations.