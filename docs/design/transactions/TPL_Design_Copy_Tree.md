## ASP.NET to Django Conversion Script:

This plan outlines the modernization of your ASP.NET TPL Design Copy Tree module to a robust, scalable Django application. Our approach leverages conversational AI for guided automation, focusing on business value and efficient migration over manual coding.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the C# `GetDataTable()` method, we identify primary data source queries and subsequent lookups.

**Primary Table:** `tblDG_TPL_Master`
*   `ItemId` (int) - Likely a foreign key to `tblDG_Item_Master`.
*   `WONo` (string) - Work Order Number.
*   `PId` (int) - Parent ID, crucial for the tree structure.
*   `CId` (int) - Current ID, acts as a primary key for a node in the tree.
*   `Qty` (decimal) - Quantity.
*   `Weldments` (string)
*   `LH` (string)
*   `RH` (string)
*   `CompId` (int) - Company ID.
*   `FinYearId` (int) - Financial Year ID.

**Related Tables (Inferred from lookups):**
**`tblDG_Item_Master`**
*   `Id` (int) - Primary Key.
*   `ItemCode` (string)
*   `ManfDesc` (string) - Manufacturer Description.
*   `UOMBasic` (int) - Foreign key to `Unit_Master`.
*   `CompId` (int)
*   `FinYearId` (int)

**`Unit_Master`**
*   `Id` (int) - Primary Key.
*   `Symbol` (string) - Unit of Measure symbol.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Read (Data Retrieval):** The `GetDataTable()` function reads data from `tblDG_TPL_Master` filtered by `WONo`, `CompId`, and `FinYearId`. It also performs lookups to `tblDG_Item_Master` for `ItemCode` and `ManfDesc`, and `Unit_Master` for `UOM` (`Symbol`). This data is then bound to the `RadTreeList`.
*   **Business Logic (Quantity Calculation):** The `fun.TreeQty()` method calculates a cumulative quantity based on the hierarchical structure (`PId`, `CId`), which is then formatted as `TPL Qty`. This is a core "fat model" candidate.
*   **Copy (Create Operation):** The `btnCopy_Click` event triggers the `fun.getnode()` function. This function copies a selected assembly node (`SrcCid`) and its entire subtree from a source work order (`wonosrc`) to a destination work order (`wonodest`), placing it under a specified `DestPId` (`pid`) and assigning a new `DestCId` (`cid`) to the copied root. This will be implemented as a model class method in Django for a fully automated copy process.
*   **Navigation/UI Updates:** Paging, expanding/collapsing the tree (`CheckBox1_CheckedChanged`) and item command handlers are present. These primarily involve re-binding the `RadTreeList` with new data, which will be managed by HTMX and DataTables on the client-side, with views providing data endpoints.
*   **Redirection/Messaging:** `Response.Redirect` is used for navigation after operations, and `lblasslymsg` for status messages. Django's `messages` framework and `HttpResponse` with `HX-Trigger` headers will handle this.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **`RadTreeList`:** This complex tree-grid component displays the hierarchical Bill of Materials (BOM) data. In Django, this will be replaced by an HTML `<table>` that leverages the `django-mptt` library for tree structure and `DataTables.js` for client-side search, sort, and pagination. HTMX will be used to dynamically load and update the table content.
*   **`asp:Label` (Label2, lblasslymsg):** Used to display the source Work Order number and status messages. `Label2` will be a simple display element, and `lblasslymsg` will be replaced by Django's `messages` framework displayed via HTMX.
*   **`asp:CheckBox` (CheckBox1 - Expand Tree):** Controls the expansion/collapse state of the tree. This will be handled by HTMX, triggering a view to re-render the table with the appropriate expansion state.
*   **`asp:Button` (btnCopy, BtnCancel):** Trigger server-side actions. `btnCopy` will initiate the assembly copy process, and `BtnCancel` will redirect. Both will use HTMX to post data and handle responses dynamically, with `BtnCancel` potentially triggering a modal close or a simple redirect.

### Step 4: Generate Django Code

We will create a new Django app, let's call it `design_tpl`.

#### 4.1 Models (`design_tpl/models.py`)

**Task:** Create Django models based on the identified database schema. We'll use `django-mptt` for hierarchical data.

**Instructions:**
- Define `TplMaster`, `ItemMaster`, and `UnitMaster` models, mapping to existing tables.
- `TplMaster` will inherit from `MPTTModel` to manage the tree structure.
- Implement `calculate_tpl_qty` method on `TplMaster` to replicate `fun.TreeQty`.
- Implement `copy_assembly_tree` class method on `TplMaster` to replicate `fun.getnode` functionality, handling new unique CIDs for copied nodes.

```python
from django.db import models, transaction
from mptt.models import MPTTModel, TreeForeignKey
from django.db.models import Max
from decimal import Decimal

# Assuming ItemMaster and UnitMaster are existing tables in the database
# and we are mapping to them. In a real scenario, they would likely have their
# own PKs and fields. For this migration, we infer based on usage.

class ItemMaster(models.Model):
    # Id in tblDG_Item_Master is the primary key and corresponds to ItemId in TPL_Master
    id = models.IntegerField(db_column='Id', primary_key=True) 
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.IntegerField(db_column='UOMBasic') # FK to Unit_Master.Id
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

    @property
    def uom_symbol(self):
        """Fetches the UOM symbol from Unit_Master."""
        try:
            return UnitMaster.objects.get(id=self.uom_basic).symbol
        except UnitMaster.DoesNotExist:
            return "N/A"

class UnitMaster(models.Model):
    # Id in Unit_Master is the primary key
    id = models.IntegerField(db_column='Id', primary_key=True) 
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class TplMaster(MPTTModel):
    # CId is treated as the primary key of this table, representing the current node
    # If CId is auto-incrementing in the DB, set primary_key=True.
    # If CId is a unique business key and DB has a separate auto-PK, adjust accordingly.
    # For this migration, we assume CId is the primary key and we need to generate new unique ones.
    cid = models.IntegerField(db_column='CId', primary_key=True)
    
    # PId in ASP.NET maps to 'parent' in MPTT. Using 'id' for the related instance.
    parent = TreeForeignKey(
        'self', 
        on_delete=models.CASCADE, 
        null=True, 
        blank=True, 
        related_name='children', 
        db_column='PId' # This explicitly maps to the PId column
    )
    
    item_id = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='tpl_designs')
    wo_no = models.CharField(db_column='WONo', max_length=255)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    weldments = models.CharField(db_column='Weldments', max_length=255, blank=True, null=True)
    lh = models.CharField(db_column='LH', max_length=255, blank=True, null=True)
    rh = models.CharField(db_column='RH', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class MPTTMeta:
        # PId is the parent, CId is the current node's ID.
        # order_insertion_by defines how children are ordered under a parent.
        order_insertion_by = ['cid']

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'tblDG_TPL_Master' # Map to the existing table name
        verbose_name = 'TPL Design'
        verbose_name_plural = 'TPL Designs'
        # Adding a unique constraint if (cid, wo_no, comp_id, fin_year_id) truly makes a record unique
        unique_together = ('cid', 'wo_no', 'comp_id', 'fin_year_id')


    def __str__(self):
        return f"{self.item_id.item_code} ({self.wo_no})"

    @property
    def item_code(self):
        return self.item_id.item_code if self.item_id else ''
    
    @property
    def manf_desc(self):
        return self.item_id.manf_desc if self.item_id else ''

    @property
    def uom(self):
        return self.item_id.uom_symbol if self.item_id else ''

    def calculate_tpl_qty(self):
        """
        Replicates the fun.TreeQty logic by calculating the cumulative quantity
        of the current item within its BOM structure (walking up the tree).
        Returns a Decimal, formatted to 3 decimal places.
        """
        path_quantities = []
        node = self
        
        # Traverse up the tree to collect quantities of ancestors
        while node:
            path_quantities.append(node.qty)
            node = node.parent
        
        cumulative_qty = Decimal(1.0)
        # Multiply quantities in reverse order (from root down to current node)
        for q in reversed(path_quantities):
            cumulative_qty *= q
        
        return cumulative_qty.quantize(Decimal('0.001'))

    @classmethod
    @transaction.atomic
    def copy_assembly_tree(cls, src_cid, src_wo_no, dest_wo_no, comp_id, user_id, fin_year_id, dest_pid):
        """
        Replicates the fun.getnode logic to copy a TPL assembly subtree.
        This method handles generating new unique CIds and re-establishing
        the tree structure in the destination Work Order.

        Args:
            src_cid (int): The CId of the root node of the assembly to copy.
            src_wo_no (str): The source Work Order number.
            dest_wo_no (str): The destination Work Order number.
            comp_id (int): Company ID for the operation.
            user_id (str): User ID performing the operation. (Not directly used for DB, but for logging/context)
            fin_year_id (int): Financial Year ID.
            dest_pid (int, optional): The CId of the new parent node in dest_wo_no.
                                       If 0 or None, the copied subtree is attached at the root level.
        Returns:
            tuple: (bool, str) indicating success and a message.
        """
        try:
            # Find the source node to be copied
            source_node = cls.objects.get(
                cid=src_cid,
                wo_no=src_wo_no,
                comp_id=comp_id,
                fin_year_id=fin_year_id
            )

            # Get the maximum existing CId to generate new unique CIds
            max_existing_cid = cls.objects.aggregate(Max('cid'))['cid__max'] or 0
            current_new_cid = max_existing_cid

            # Dictionary to map old CIds to new TplMaster instances in the destination WO
            # This is crucial for correctly linking parents during recursive copy
            copied_node_map = {} 

            # Determine the actual parent instance for the *first* copied node (source_node)
            new_root_parent_instance = None
            if dest_pid: # If a destination parent ID is provided
                try:
                    new_root_parent_instance = cls.objects.get(
                        cid=dest_pid,
                        wo_no=dest_wo_no,
                        comp_id=comp_id,
                        fin_year_id=fin_year_id
                    )
                except cls.DoesNotExist:
                    raise ValueError(f"Destination parent with CId {dest_pid} not found in Work Order {dest_wo_no}.")

            # Use a list for BFS/DFS to ensure parents are processed before children
            # Each tuple: (node_to_copy_from_source, parent_cid_in_dest_map_for_this_node)
            # The initial parent_cid_in_dest_map for the source_node is dest_pid
            nodes_to_process = [(source_node, dest_pid)]

            while nodes_to_process:
                node_from_source, parent_cid_for_new_node = nodes_to_process.pop(0) # Process in BFS order

                current_new_cid += 1
                new_cid_for_this_node = current_new_cid

                # Determine the actual parent instance for the newly created node
                parent_instance_for_new_node = None
                if parent_cid_for_new_node:
                    # If the parent is part of the *currently being copied* subtree,
                    # its new instance will be in copied_node_map.
                    if parent_cid_for_new_node in copied_node_map:
                        parent_instance_for_new_node = copied_node_map[parent_cid_for_new_node]
                    else:
                        # If the parent is an *existing* node in the destination WONo (not part of the copied subtree)
                        try:
                            parent_instance_for_new_node = cls.objects.get(
                                cid=parent_cid_for_new_node,
                                wo_no=dest_wo_no,
                                comp_id=comp_id,
                                fin_year_id=fin_year_id
                            )
                        except cls.DoesNotExist:
                            # This case should ideally be caught by the initial new_root_parent_instance check
                            # or indicates an invalid intermediate parent_cid_for_new_node.
                            raise ValueError(f"Internal error: Parent CId {parent_cid_for_new_node} for copied node could not be resolved.")

                # Create the new node instance
                new_node = cls(
                    cid=new_cid_for_this_node,
                    parent=parent_instance_for_new_node,
                    item_id=node_from_source.item_id,
                    wo_no=dest_wo_no, # Assign to the destination work order
                    qty=node_from_source.qty,
                    weldments=node_from_source.weldments,
                    lh=node_from_source.lh,
                    rh=node_from_source.rh,
                    comp_id=comp_id,
                    fin_year_id=fin_year_id
                )
                new_node.save() # Save the node to the database

                # Store the new node instance mapped to its original CId
                copied_node_map[node_from_source.cid] = new_node

                # Add children of the current source node to the queue to be processed
                # Their new parent will be the newly generated CId of the current node
                for child in node_from_source.get_children():
                    nodes_to_process.append((child, new_cid_for_this_node))

            # Rebuild the MPTT tree structure to ensure correct lft/rght/level values
            cls.objects.rebuild()
            return True, f"TPL Assembly copied successfully from WONo: {src_wo_no} to {dest_wo_no}."

        except cls.DoesNotExist:
            return False, f"Source assembly with CId {src_cid} not found in Work Order {src_wo_no}."
        except ValueError as e:
            return False, f"Error during copy: {e}"
        except Exception as e:
            # Catching generic exceptions for robustness during migration
            return False, f"An unexpected error occurred during copy: {e}"

```

#### 4.2 Forms (`design_tpl/forms.py`)

**Task:** Define a Django form for the copy operation.

**Instructions:**
- Since the copy operation is driven by query parameters and selection from a list, a simple form for the destination parameters (`WONoDest`, `DestPId`, `DestCId` from the original code) might be needed if they are to be user-editable.
- The ASP.NET logic shows `WONoSrc`, `WONoDest`, `DestPId`, `DestCId` are passed in the URL. The `SrcCid` is selected from the grid. So, we might not need a full `ModelForm` for CRUD, but rather a simple form for the copy parameters if they are not fixed.
- For `btnCopy_Click`, `WONoSrc`, `WONoDest`, `DestPId`, `DestCId` are taken from query string, and `SrcCid` from selected item.
- We will define a `CopyAssemblyForm` to capture the `dest_wo_no` and `dest_pid` if the user should be able to specify them dynamically. For now, assume these come from the URL as per original.
- The "copy" action itself is not a traditional `ModelForm` submission but a specific command. However, we'll create a small form just to illustrate how to capture the destination work order and parent if they were to be edited. For the provided code, `DestPId` is from query string and `DestCId` is probably just for error checking. Let's simplify.

```python
from django import forms
from .models import TplMaster

# A simple form to allow user to specify destination work order and parent if not via URL
class CopyAssemblyForm(forms.Form):
    # These fields would capture the destination work order and parent ID for the copy operation.
    # In the original code, these are from query strings.
    # We'll make them optional and primarily for demonstration if manual input is ever needed.
    dest_wo_no = forms.CharField(
        max_length=255, 
        required=True, 
        label="Destination Work Order No.",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    dest_pid = forms.IntegerField(
        required=False, 
        label="Destination Parent ID (optional)",
        help_text="CId of the existing parent node in the destination Work Order. Leave blank to copy as a new root.",
        widget=forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def clean(self):
        cleaned_data = super().clean()
        dest_wo_no = cleaned_data.get('dest_wo_no')
        dest_pid = cleaned_data.get('dest_pid')

        # Add any specific validation for the destination work order or parent ID
        # e.g., check if dest_wo_no exists, or if dest_pid exists within dest_wo_no
        if dest_pid is not None:
            # Validate if dest_pid exists in dest_wo_no
            if not TplMaster.objects.filter(wo_no=dest_wo_no, cid=dest_pid).exists():
                self.add_error('dest_pid', 'Destination Parent ID does not exist in the specified Destination Work Order.')
        
        return cleaned_data

```

#### 4.3 Views (`design_tpl/views.py`)

**Task:** Implement the main view for displaying the TPL design tree and handling the copy operation.

**Instructions:**
- A `TplMasterListView` will display the tree. It will use HTMX to fetch the table data.
- A `TplMasterTablePartialView` will render the `_tplmaster_table.html` partial, containing the DataTables.
- A `CopyAssemblyView` will handle the POST request for copying the assembly, triggering the `TplMaster.copy_assembly_tree` method.
- The `CheckBox1_CheckedChanged` behavior (expand/collapse) will be managed by passing a `expand_all` parameter to `TplMasterTablePartialView`.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from django.db.models import F # For efficient lookups if needed

from .models import TplMaster
from .forms import CopyAssemblyForm # Use the form for the copy modal if applicable

class TplMasterListView(TemplateView):
    """
    Main view to display the TPL Design Copy Tree.
    This serves the initial page with the container for the HTMX-loaded table.
    """
    template_name = 'design_tpl/tplmaster_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass initial wonosrc and wonodest from query parameters
        context['wonosrc'] = self.request.GET.get('WONoSrc', 'N/A')
        context['wonodest'] = self.request.GET.get('WONoDest', 'N/A')
        context['dest_pid'] = self.request.GET.get('DestPId', 0) # Default 0 if not provided
        context['dest_cid'] = self.request.GET.get('DestCId', 0) # Default 0 if not provided
        return context

class TplMasterTablePartialView(View):
    """
    Renders the partial HTML for the TPL Design Tree table,
    designed to be loaded via HTMX.
    """
    def get(self, request, *args, **kwargs):
        wonosrc = request.GET.get('WONoSrc')
        comp_id = request.session.get('compid', 1) # Get from session or default
        fin_year_id = request.session.get('finyear', 1) # Get from session or default
        expand_all = request.GET.get('expand_all', 'true').lower() == 'true' # From CheckBox

        if not wonosrc:
            # Handle case where wonosrc is missing (e.g., initial load)
            return HttpResponse("<p class='text-red-500'>Please provide a source Work Order Number (WONoSrc).</p>")

        # Fetch TPL Master data for the source work order
        # Annotate with item_code, manf_desc, uom for display
        # and calculate tpl_qty (moved to model property)
        # Note: MPTT models can have issues with select_related for parents across recursive lookups
        # So we'll get the objects and let the properties handle lookups
        tpl_designs = TplMaster.objects.filter(
            wo_no=wonosrc,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        ).select_related('item_id', 'item_id__uom_basic') # Prefetch related Item and Unit

        # If using django-mptt, prepare the tree structure for template rendering
        # 'mptt_level' will be used for indentation in the template
        # `expand_all` influences how the tree is rendered by DataTables/JS.
        # For a truly expandable tree, the table would need more complex JS.
        # Here we'll just pass all nodes and assume JS handles display.
        # If expand_all is false, we might only pass root nodes and fetch children on demand.
        # For simplicity, pass all and let the client-side DataTables control expansion.
        
        # Original ASP.NET used RadTreeList.ExpandAllItems(). To replicate this,
        # we provide all nodes and let the template/JS handle visual expansion.
        
        context = {
            'tpl_designs': tpl_designs,
            'expand_all': expand_all,
            'wonosrc': wonosrc, # Pass wonosrc to the partial for display
        }
        return render(request, 'design_tpl/_tplmaster_table.html', context)


class CopyAssemblyActionView(View):
    """
    Handles the POST request for copying an assembly.
    This corresponds to the logic in btnCopy_Click.
    """
    def post(self, request, *args, **kwargs):
        # Parameters from original ASP.NET code, assumed to be available
        # from query string or hidden fields/HTMX attributes on the copy button.
        wonosrc = request.GET.get('WONoSrc') # Source WO from main page URL
        wonodest = request.GET.get('WONoDest') # Destination WO from main page URL
        dest_pid_str = request.GET.get('DestPId', '0') # Destination parent ID
        # original `DestCId` (cid) is used for error checking `if (cid == SrcCid)` but not actual copy logic.
        # We will generate a new CId for the copied root.

        try:
            dest_pid = int(dest_pid_str)
        except ValueError:
            messages.error(request, "Invalid Destination Parent ID provided.")
            return HttpResponse(status=200, headers={'HX-Refresh': 'true'}) # Refresh to show message

        # The SrcCid comes from the selected item in the RadTreeList.
        # In HTMX, this would be passed via hx-vals or a hidden input.
        src_cid_str = request.POST.get('src_cid') # The CId of the selected item to copy

        if not src_cid_str:
            messages.error(request, "No assembly selected for copying.")
            return HttpResponse(status=200, headers={'HX-Refresh': 'true'}) # Refresh to show message

        try:
            src_cid = int(src_cid_str)
        except ValueError:
            messages.error(request, "Invalid Source Assembly ID provided.")
            return HttpResponse(status=200, headers={'HX-Refresh': 'true'}) # Refresh to show message

        # Mock session variables (replace with actual session/user data)
        comp_id = request.session.get('compid', 1)  # Default for demonstration
        fin_year_id = request.session.get('finyear', 1) # Default for demonstration
        user_id = request.user.username if request.user.is_authenticated else 'anonymous'

        # Check for the original ASP.NET error condition: if (cid == SrcCid)
        # The `DestCId` from query string seems to be a 'target CId', if it matches `SrcCid`, it's an error.
        # If `dest_cid` from query string is passed and matches `src_cid` from selection, then it's an error.
        # Let's assume `dest_cid` from original query string is not needed for the actual copy,
        # as we are generating new CIDs. If it's for `TPL_Design_CopyWo.aspx?msg=Selection of Assly/Item is incorrect.`
        # we can add a check. `dest_cid` from query is currently not used in `copy_assembly_tree`.

        # Perform the copy operation via the model's class method
        success, msg = TplMaster.copy_assembly_tree(
            src_cid=src_cid,
            src_wo_no=wonosrc,
            dest_wo_no=wonodest,
            comp_id=comp_id,
            user_id=user_id,
            fin_year_id=fin_year_id,
            dest_pid=dest_pid
        )

        if success:
            messages.success(request, msg)
        else:
            messages.error(request, msg)

        # HTMX response: Trigger a refresh of the list
        # And potentially redirect the browser after showing message, similar to ASP.NET
        # The original code redirects to TPL_Design_CopyWo.aspx after copy.
        # If this page is a popup/modal, we might want to just close it and refresh parent.
        # If it's a full page, a redirect is appropriate.

        # For a modal context, we'd typically send 204 No Content with HX-Trigger
        # If `btnCopy` is on the main list page, and not a modal, it would redirect.
        # Given `Response.Redirect` in original, let's redirect to the source page with a success message.
        redirect_url = reverse_lazy('design_tpl:tplmaster_list') + f'?WONoSrc={wonosrc}&WONoDest={wonodest}&DestPId={dest_pid}'
        return HttpResponseRedirect(redirect_url)

class CopyAssemblyModalView(View):
    """
    Renders the modal content for confirming/configuring the copy operation.
    This will be loaded via HTMX into a modal.
    """
    def get(self, request, *args, **kwargs):
        # Parameters for the modal form/display
        wonosrc = request.GET.get('WONoSrc')
        wonodest = request.GET.get('WONoDest')
        dest_pid = request.GET.get('DestPId', 0)
        src_cid = request.GET.get('src_cid') # CId of the item selected from the list

        if not src_cid:
            return HttpResponse("<p class='text-red-500'>No assembly selected for copying.</p>", status=400)
        
        # Populate form if needed, or just display information
        context = {
            'src_cid': src_cid,
            'wonosrc': wonosrc,
            'wonodest': wonodest,
            'dest_pid': dest_pid,
            'form': CopyAssemblyForm(initial={'dest_wo_no': wonodest, 'dest_pid': dest_pid}), # Prefill with current destination
        }
        return render(request, 'design_tpl/_copy_modal.html', context)

    def post(self, request, *args, **kwargs):
        # This POST will be handled by CopyAssemblyActionView via HTMX POST.
        # This view only serves the GET request for the modal content.
        # The actual form submission within the modal would point to `copy_assembly_action`.
        pass


class ToggleExpandView(View):
    """
    Handles the CheckBox1_CheckedChanged logic to toggle tree expansion.
    """
    def get(self, request, *args, **kwargs):
        # Get current state from query param or session
        expand_all = request.GET.get('expand_all', 'false').lower() == 'true'
        
        # Re-render the table with the new expansion state
        return TplMasterTablePartialView.as_view()(request) # Delegate to the partial view
```

#### 4.4 Templates (`design_tpl/templates/design_tpl/`)

**Task:** Create templates for the list view, the table partial, and the copy modal.

**Instructions:**
- `tplmaster_list.html` will be the main page, extending `core/base.html`.
- `_tplmaster_table.html` will contain the DataTables structure, loaded via HTMX.
- `_copy_modal.html` will be a partial for the copy confirmation/form, loaded into a modal.

```html
<!-- design_tpl/templates/design_tpl/tplmaster_list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">TPL Design Copy Tree</h2>
        <span class="font-semibold text-gray-700">WO No: <span id="woSrcLabel">{{ wonosrc }}</span></span>
        
        <label class="inline-flex items-center cursor-pointer">
            <input type="checkbox" id="expandTreeCheckbox" class="sr-only peer"
                   hx-get="{% url 'design_tpl:toggle_expand' %}"
                   hx-target="#tplMasterTable-container"
                   hx-swap="innerHTML"
                   hx-trigger="change"
                   hx-vals="js:{expand_all: document.getElementById('expandTreeCheckbox').checked}"
                   {% if request.GET.expand_all != 'false' %}checked{% endif %}>
            <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border after:border-gray-300 after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            <span class="ml-3 text-sm font-medium text-gray-900">Expand Tree</span>
        </label>
    </div>
    
    <div id="tplMasterTable-container"
         hx-trigger="load, refreshTplList from:body"
         hx-get="{% url 'design_tpl:tplmaster_table_partial' %}?WONoSrc={{ wonosrc }}&WONoDest={{ wonodest }}&DestPId={{ dest_pid }}&expand_all={{ request.GET.expand_all|default:'true' }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading TPL Design data...</p>
        </div>
    </div>
    
    <!-- Modals go here if needed -->
    <!-- Modal for copy confirmation or other actions -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full transform transition-all scale-95 opacity-0 is-active:scale-100 is-active:opacity-100"
             _="on htmx:afterSwap put #modal.is-active
                on htmx:beforeSwap take #modal.is-active">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally.
    // If specific Alpine components are needed for this page, define them here.
    document.addEventListener('alpine:init', () => {
        Alpine.data('tplPage', () => ({
            // Any specific data or functions for this page
        }));
    });

    // HTMX listener for messages (assuming messages are swapped into a div on base.html)
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Initialize DataTables after content swap
        if (event.detail.target.id === 'tplMasterTable-container') {
            $('#tplDesignTable').DataTable({
                "paging": true,
                "searching": true,
                "info": true,
                "pageLength": 8, // Matching original PageSize="8"
                "lengthMenu": [[8, 15, 25, 50, -1], [8, 15, 25, 50, "All"]],
                // For tree structure, DataTables might need a plugin like RowGroup or custom rendering
                // For now, it's a flat table. True tree rendering requires specific JS logic.
            });
        }
    });

    // Client-side confirmation for Copy button, mimicking OnClientClick=" return confirmationCopy()"
    function confirmationCopy() {
        return confirm('Are you sure you want to copy the selected assembly?');
    }

    // HTMX handler for the "Cancel" button to redirect
    document.body.addEventListener('click', function(event) {
        if (event.target.id === 'btnCancelRedirect') {
            event.preventDefault();
            const wonosrc = document.getElementById('woSrcLabel').innerText;
            const wonodest = '{{ wonodest }}'; // Assumed to be available in template context
            const dest_pid = '{{ dest_pid }}'; // Assumed to be available in template context
            const dest_cid = '{{ dest_cid }}'; // Assumed to be available in template context
            window.location.href = `TPL_Design_CopyWo.aspx?WONoSrc=${wonosrc}&WONoDest=${wonodest}&DestPId=${dest_pid}&DestCId=${dest_cid}`;
            // NOTE: Replace the above with Django reverse_lazy URL if 'TPL_Design_CopyWo.aspx' is also migrated
            // Example: window.location.href = "{% url 'design_tpl:other_module_list' %}";
        }
    });
</script>
{% endblock %}
```

```html
<!-- design_tpl/templates/design_tpl/_tplmaster_table.html -->
<!-- This partial template is loaded by HTMX into #tplMasterTable-container -->
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="tplDesignTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assembly</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf Desc</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">TPL Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Weldments</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">LH</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">RH</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for node in tpl_designs.all %}
            <tr class="{% if node.mptt_level > 0 %}bg-gray-50{% endif %}"
                {% if node.mptt_level > 0 %}style="padding-left: {{ node.mptt_level|add:1 }}rem;"{% endif %}>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <span style="padding-left: {{ node.get_level|multiply:1.5 }}rem;">
                        {% if node.get_children %}
                            <span class="toggle-icon cursor-pointer" 
                                  data-node-id="{{ node.cid }}" 
                                  hx-get="{% url 'design_tpl:toggle_node_expansion' node.cid %}"
                                  hx-target="closest tr"
                                  hx-swap="outerHTML"
                                  hx-include="this"
                                  hx-indicator="#loadingSpinner{{ node.cid }}">
                                {% if expand_all %}&#9660;{% else %}&#9658;{% endif %} {# Down/Right arrow #}
                            </span>
                        {% else %}
                            <span class="inline-block w-4"></span> {# Spacer for leaf nodes #}
                        {% endif %}
                        {{ node.item_code }} ({{ node.cid }})
                    </span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ node.item_code }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ node.manf_desc }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ node.qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ node.calculate_tpl_qty|floatformat:3 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ node.uom }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ node.weldments|default_if_none:"" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ node.lh|default_if_none:"" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ node.rh|default_if_none:"" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'design_tpl:copy_modal' %}?WONoSrc={{ wonosrc }}&WONoDest={{ request.GET.WONoDest }}&DestPId={{ request.GET.DestPId }}&src_cid={{ node.cid }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Select to Copy
                    </button>
                    <!-- Loading indicator for this specific node selection -->
                    <span id="loadingSpinner{{ node.cid }}" class="htmx-indicator ml-2">
                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                    </span>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="flex justify-end p-4 space-x-4 bg-gray-50 border-t border-gray-200">
        <button id="btnCopyMain" 
            class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'design_tpl:copy_assembly_action' %}?WONoSrc={{ wonosrc }}&WONoDest={{ request.GET.WONoDest }}&DestPId={{ request.GET.DestPId }}"
            hx-confirm="Are you sure you want to copy the selected assembly?"
            hx-vals="js:{src_cid: prompt('Please enter the CId of the assembly to copy (if not selected directly):')}"
            hx-indicator="#copyLoadingIndicator">
            Copy Selected
        </button>
        <button id="btnCancelRedirect" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
            Cancel
        </button>
        <span id="copyLoadingIndicator" class="htmx-indicator ml-2">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-gray-500"></div>
        </span>
    </div>
</div>

<script>
    // DataTables initialization handled by the parent template's HTMX:afterSwap event.
    // This script block should only contain logic specific to *this partial* if needed.
</script>
```

```html
<!-- design_tpl/templates/design_tpl/_copy_modal.html -->
<!-- This partial template is loaded by HTMX into #modalContent -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Assembly Copy</h3>
    <p class="text-gray-700 mb-4">
        You are about to copy assembly with **CId: {{ src_cid }}** from **Work Order: {{ wonosrc }}**.
        <br>It will be copied to **Work Order: {{ wonodest }}**.
        {% if dest_pid %}
            <br>The copied assembly will be attached under **Parent CId: {{ dest_pid }}** in the destination Work Order.
        {% else %}
            <br>The copied assembly will be attached as a **new root node** in the destination Work Order.
        {% endif %}
    </p>

    <form hx-post="{% url 'design_tpl:copy_assembly_action' %}?WONoSrc={{ wonosrc }}&WONoDest={{ wonodest }}&DestPId={{ dest_pid }}"
          hx-swap="none"
          hx-on::after-request="if(event.detail.successful) {
                                  htmx.trigger(document.body, 'refreshTplList');
                                  removeElement('#modal');
                                } else {
                                  // Handle error, e.g., swap error message
                                  // Re-show modal if form submission fails for validation etc.
                                }">
        {% csrf_token %}
        <input type="hidden" name="src_cid" value="{{ src_cid }}">
        
        <!-- Render copy form fields if needed (e.g., if user can change dest_wo_no/dest_pid in modal) -->
        <!-- For this case, they are derived from URL and fixed, but uncomment if interactive -->
        <!-- 
        <div class="mb-4">
            <label for="id_dest_wo_no" class="block text-sm font-medium text-gray-700">Destination Work Order No.</label>
            {{ form.dest_wo_no }}
            {% if form.dest_wo_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.dest_wo_no.errors }}</p>
            {% endif %}
        </div>
        <div class="mb-4">
            <label for="id_dest_pid" class="block text-sm font-medium text-gray-700">Destination Parent ID</label>
            {{ form.dest_pid }}
            {% if form.dest_pid.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.dest_pid.errors }}</p>
            {% endif %}
        </div>
        -->

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Confirm Copy
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design_tpl/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
- Include paths for the main list view, the HTMX-loaded table partial, the copy action, the copy modal, and the expand/collapse toggle.

```python
from django.urls import path
from .views import (
    TplMasterListView,
    TplMasterTablePartialView,
    CopyAssemblyActionView,
    CopyAssemblyModalView,
    ToggleExpandView,
)

app_name = 'design_tpl' # Namespace for URLs

urlpatterns = [
    # Main list view (full page load)
    path('copy-tree/', TplMasterListView.as_view(), name='tplmaster_list'),
    
    # HTMX endpoint for the table content (partial load)
    path('copy-tree/table/', TplMasterTablePartialView.as_view(), name='tplmaster_table_partial'),
    
    # HTMX endpoint for the copy confirmation modal
    path('copy-tree/copy-modal/', CopyAssemblyModalView.as_view(), name='copy_modal'),
    
    # HTMX endpoint for handling the actual copy POST request
    path('copy-tree/copy-action/', CopyAssemblyActionView.as_view(), name='copy_assembly_action'),

    # HTMX endpoint to toggle expand/collapse state
    path('copy-tree/toggle-expand/', ToggleExpandView.as_view(), name='toggle_expand'),

    # If 'TPL_Design_CopyWo.aspx' is another Django view, define it here:
    # path('copy-wo/', YourCopyWoView.as_view(), name='other_module_list'),
]
```

#### 4.6 Tests (`design_tpl/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
- Test model methods like `calculate_tpl_qty`.
- Test the `copy_assembly_tree` class method thoroughly, including edge cases.
- Test view responses for GET and POST requests, including HTMX specific headers and triggers.
- Ensure proper message handling.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db import connection

from .models import TplMaster, ItemMaster, UnitMaster
from mptt.exceptions import InvalidMove # For MPTT specific tests

# Helper function to reset sequences for manually managed PKs in tests if needed
def reset_sequence(table_name, pk_field_name):
    """Resets the sequence for a given table's primary key."""
    with connection.cursor() as cursor:
        cursor.execute(f"SELECT setval(pg_get_serial_sequence('{table_name}', '{pk_field_name}'), (SELECT MAX({pk_field_name}) FROM {table_name}));")

class TplMasterModelTest(TestCase):
    # Use setUpTestData for data that is not modified by tests
    @classmethod
    def setUpTestData(cls):
        # Create mock data for related tables first
        cls.comp_id = 1
        cls.fin_year_id = 2023

        # Create UnitMaster entry
        UnitMaster.objects.create(id=1, symbol='PCS')
        UnitMaster.objects.create(id=2, symbol='KG')

        # Create ItemMaster entries
        ItemMaster.objects.create(id=101, item_code='ITEM-A', manf_desc='Assembly A', uom_basic=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        ItemMaster.objects.create(id=102, item_code='ITEM-B', manf_desc='Component B', uom_basic=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        ItemMaster.objects.create(id=103, item_code='ITEM-C', manf_desc='Part C', uom_basic=2, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        ItemMaster.objects.create(id=104, item_code='ITEM-D', manf_desc='Sub-Assembly D', uom_basic=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        ItemMaster.objects.create(id=105, item_code='ITEM-E', manf_desc='Part E', uom_basic=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)

        # Create TplMaster entries (representing tblDG_TPL_Master data)
        # cid, wo_no, item_id, qty, parent, comp_id, fin_year_id
        # Define a source work order structure
        cls.src_wo_no = 'WO-SRC-001'
        
        # Root node
        cls.node1 = TplMaster.objects.create(
            cid=1, wo_no=cls.src_wo_no, item_id_id=101, qty=Decimal('1.000'), parent=None,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        # Children of node1
        cls.node2 = TplMaster.objects.create(
            cid=2, wo_no=cls.src_wo_no, item_id_id=102, qty=Decimal('2.000'), parent=cls.node1,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.node3 = TplMaster.objects.create(
            cid=3, wo_no=cls.src_wo_no, item_id_id=103, qty=Decimal('0.500'), parent=cls.node1,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        # Children of node2
        cls.node4 = TplMaster.objects.create(
            cid=4, wo_no=cls.src_wo_no, item_id_id=104, qty=Decimal('3.000'), parent=cls.node2,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.node5 = TplMaster.objects.create(
            cid=5, wo_no=cls.src_wo_no, item_id_id=105, qty=Decimal('1.000'), parent=cls.node2,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        # Another root node
        cls.node6 = TplMaster.objects.create(
            cid=6, wo_no=cls.src_wo_no, item_id_id=101, qty=Decimal('1.000'), parent=None,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        
        # Rebuild MPTT tree after bulk creation
        TplMaster.objects.rebuild()
        # Reset sequence for cid to ensure new CIds are generated correctly
        reset_sequence('tblDG_TPL_Master', 'CId')

    def setUp(self):
        self.client = Client()

    def test_tplmaster_creation_and_properties(self):
        """Test basic model creation and property access."""
        node = TplMaster.objects.get(cid=self.node1.cid)
        self.assertEqual(node.wo_no, self.src_wo_no)
        self.assertEqual(node.item_code, 'ITEM-A')
        self.assertEqual(node.manf_desc, 'Assembly A')
        self.assertEqual(node.uom, 'PCS')
        self.assertEqual(node.get_level(), 0) # Root node level

        node_child = TplMaster.objects.get(cid=self.node2.cid)
        self.assertEqual(node_child.parent, self.node1)
        self.assertEqual(node_child.get_level(), 1) # Child node level

    def test_calculate_tpl_qty(self):
        """Test the calculate_tpl_qty method."""
        # Node1 (Root): Qty 1.000, TPL Qty should be 1.000
        self.assertEqual(self.node1.calculate_tpl_qty(), Decimal('1.000'))

        # Node2 (Child of Node1): Qty 2.000, Parent Qty 1.000, TPL Qty = 1.000 * 2.000 = 2.000
        self.assertEqual(self.node2.calculate_tpl_qty(), Decimal('2.000'))

        # Node3 (Child of Node1): Qty 0.500, Parent Qty 1.000, TPL Qty = 1.000 * 0.500 = 0.500
        self.assertEqual(self.node3.calculate_tpl_qty(), Decimal('0.500'))

        # Node4 (Child of Node2, which is child of Node1): Qty 3.000
        # TPL Qty = Node1.Qty * Node2.Qty * Node4.Qty = 1.000 * 2.000 * 3.000 = 6.000
        self.assertEqual(self.node4.calculate_tpl_qty(), Decimal('6.000'))
        
        # Node5 (Child of Node2): Qty 1.000
        # TPL Qty = Node1.Qty * Node2.Qty * Node5.Qty = 1.000 * 2.000 * 1.000 = 2.000
        self.assertEqual(self.node5.calculate_tpl_qty(), Decimal('2.000'))


    def test_copy_assembly_tree_as_new_root(self):
        """Test copying a subtree as a new root in a destination WO."""
        dest_wo_no = 'WO-DEST-001'
        src_cid_to_copy = self.node2.cid # Copy Node2 (and its children Node4, Node5)
        
        success, msg = TplMaster.copy_assembly_tree(
            src_cid=src_cid_to_copy,
            src_wo_no=self.src_wo_no,
            dest_wo_no=dest_wo_no,
            comp_id=self.comp_id,
            user_id='testuser',
            fin_year_id=self.fin_year_id,
            dest_pid=0 # Copy as new root
        )

        self.assertTrue(success, msg)
        self.assertIn("copied successfully", msg)

        # Verify copied nodes in destination WO
        copied_nodes = TplMaster.objects.filter(wo_no=dest_wo_no, comp_id=self.comp_id, fin_year_id=self.fin_year_id).order_by('cid')
        self.assertEqual(copied_nodes.count(), 3) # Node2, Node4, Node5

        # Check the copied root (Node2's copy)
        copied_node2 = copied_nodes.get(item_id=self.node2.item_id)
        self.assertIsNotNone(copied_node2)
        self.assertIsNone(copied_node2.parent) # Should be a root
        self.assertNotEqual(copied_node2.cid, self.node2.cid) # Should have a new CID

        # Check copied children and their relationships
        copied_node4 = copied_nodes.get(item_id=self.node4.item_id)
        self.assertIsNotNone(copied_node4)
        self.assertEqual(copied_node4.parent, copied_node2)
        self.assertNotEqual(copied_node4.cid, self.node4.cid)

        copied_node5 = copied_nodes.get(item_id=self.node5.item_id)
        self.assertIsNotNone(copied_node5)
        self.assertEqual(copied_node5.parent, copied_node2)
        self.assertNotEqual(copied_node5.cid, self.node5.cid)

        # Check MPTT structure is valid
        self.assertTrue(TplMaster.objects.all().count() > 0) # Ensure no InvalidMove errors on rebuild

    def test_copy_assembly_tree_under_existing_parent(self):
        """Test copying a subtree under an existing parent in a destination WO."""
        dest_wo_no = 'WO-DEST-002'
        dest_parent_item = ItemMaster.objects.create(id=106, item_code='ITEM-PARENT', manf_desc='New Parent', uom_basic=1, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        dest_parent_node = TplMaster.objects.create(
            cid=100, wo_no=dest_wo_no, item_id=dest_parent_item, qty=Decimal('1.000'), parent=None,
            comp_id=self.comp_id, fin_year_id=self.fin_year_id
        )
        TplMaster.objects.rebuild() # Rebuild after adding new parent

        src_cid_to_copy = self.node3.cid # Copy Node3 (a leaf node)
        
        success, msg = TplMaster.copy_assembly_tree(
            src_cid=src_cid_to_copy,
            src_wo_no=self.src_wo_no,
            dest_wo_no=dest_wo_no,
            comp_id=self.comp_id,
            user_id='testuser',
            fin_year_id=self.fin_year_id,
            dest_pid=dest_parent_node.cid
        )

        self.assertTrue(success, msg)
        self.assertIn("copied successfully", msg)

        # Verify copied node
        copied_node3 = TplMaster.objects.filter(wo_no=dest_wo_no, item_id=self.node3.item_id, parent=dest_parent_node).first()
        self.assertIsNotNone(copied_node3)
        self.assertEqual(copied_node3.parent, dest_parent_node)
        self.assertNotEqual(copied_node3.cid, self.node3.cid)

    def test_copy_assembly_tree_source_not_found(self):
        """Test copy fails if source assembly is not found."""
        success, msg = TplMaster.copy_assembly_tree(
            src_cid=999, # Non-existent CID
            src_wo_no=self.src_wo_no,
            dest_wo_no='WO-DEST-003',
            comp_id=self.comp_id,
            user_id='testuser',
            fin_year_id=self.fin_year_id,
            dest_pid=0
        )
        self.assertFalse(success)
        self.assertIn("Source assembly with CId 999 not found", msg)

    def test_copy_assembly_tree_destination_parent_not_found(self):
        """Test copy fails if destination parent ID is invalid."""
        success, msg = TplMaster.copy_assembly_tree(
            src_cid=self.node1.cid,
            src_wo_no=self.src_wo_no,
            dest_wo_no='WO-DEST-004',
            comp_id=self.comp_id,
            user_id='testuser',
            fin_year_id=self.fin_year_id,
            dest_pid=9999 # Non-existent parent CID
        )
        self.assertFalse(success)
        self.assertIn("Destination parent with CId 9999 not found", msg)

class TplMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.src_wo_no = 'WO-TEST-SRC'
        cls.dest_wo_no = 'WO-TEST-DEST'
        cls.dest_pid = 0 # Default for initial tests

        UnitMaster.objects.create(id=1, symbol='PCS')
        ItemMaster.objects.create(id=101, item_code='ITEM-A', manf_desc='Assembly A', uom_basic=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        ItemMaster.objects.create(id=102, item_code='ITEM-B', manf_desc='Component B', uom_basic=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)

        # Source data
        cls.src_node_root = TplMaster.objects.create(
            cid=1, wo_no=cls.src_wo_no, item_id_id=101, qty=Decimal('1.000'), parent=None,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.src_node_child = TplMaster.objects.create(
            cid=2, wo_no=cls.src_wo_no, item_id_id=102, qty=Decimal('2.000'), parent=cls.src_node_root,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

        # Destination parent node (for copy_under_existing_parent test)
        cls.dest_parent_item = ItemMaster.objects.create(id=103, item_code='PARENT-ITEM', manf_desc='Parent for Copy', uom_basic=1, comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.dest_parent_node = TplMaster.objects.create(
            cid=100, wo_no=cls.dest_wo_no, item_id=cls.dest_parent_item, qty=Decimal('1.000'), parent=None,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        TplMaster.objects.rebuild()
        reset_sequence('tblDG_TPL_Master', 'CId') # Reset sequence after creating test data

    def setUp(self):
        self.client = Client()
        # Mock session variables if they are used by views (e.g., CompId, FinYearId)
        self.client.session['compid'] = self.comp_id
        self.client.session['finyear'] = self.fin_year_id

    def test_list_view(self):
        """Test the main list page loads correctly."""
        url = reverse('design_tpl:tplmaster_list') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/tplmaster_list.html')
        self.assertContains(response, 'WO No: WO-TEST-SRC') # Check initial display of WONoSrc

    def test_table_partial_view_get(self):
        """Test the HTMX-loaded table partial."""
        url = reverse('design_tpl:tplmaster_table_partial') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}&expand_all=true'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/_tplmaster_table.html')
        self.assertContains(response, 'ITEM-A')
        self.assertContains(response, 'ITEM-B')
        # Check for HTMX-specific attributes on copy button
        self.assertContains(response, 'hx-get="{% url \'design_tpl:copy_modal\' %}?WONoSrc=WO-TEST-SRC&WONoDest=WO-TEST-DEST&DestPId=0&src_cid=1"')
        self.assertContains(response, 'hx-target="#modalContent"')
        self.assertContains(response, 'hx-swap="innerHTML"')

    def test_copy_modal_view_get(self):
        """Test loading the copy confirmation modal via HTMX."""
        url = reverse('design_tpl:copy_modal') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}&src_cid={self.src_node_root.cid}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/_copy_modal.html')
        self.assertContains(response, f'You are about to copy assembly with **CId: {self.src_node_root.cid}** from **Work Order: {self.src_wo_no}**.')
        self.assertContains(response, f'It will be copied to **Work Order: {self.dest_wo_no}**.')
        self.assertContains(response, 'name="src_cid" value="1"') # Hidden input for src_cid

    def test_copy_modal_view_get_no_src_cid(self):
        """Test loading copy modal without src_cid (should return 400)."""
        url = reverse('design_tpl:copy_modal') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'No assembly selected for copying.')

    def test_copy_assembly_action_view_post_success_new_root(self):
        """Test successful copy action as a new root via POST request."""
        initial_count = TplMaster.objects.count()
        
        # Test copy of src_node_root (cid=1) to dest_wo_no as a new root
        url = reverse('design_tpl:copy_assembly_action') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}'
        data = {'src_cid': self.src_node_root.cid}
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')

        # Check for successful redirect (status 302) after HTMX-driven action.
        # Original ASP.NET redirected after copy, so we replicate this if not a modal.
        # If it were a modal, it would likely return 204 No Content with HX-Trigger.
        # Given the redirect, it implies a full page refresh after successful action.
        self.assertEqual(response.status_code, 302) 
        self.assertTrue(response.url.startswith(reverse('design_tpl:tplmaster_list')))

        # Verify new nodes are created
        self.assertEqual(TplMaster.objects.count(), initial_count + 2) # Root + Child

        # Check if the root node (ITEM-A) and its child (ITEM-B) are copied to dest_wo_no
        copied_root = TplMaster.objects.filter(wo_no=self.dest_wo_no, item_id__item_code='ITEM-A').first()
        self.assertIsNotNone(copied_root)
        self.assertIsNone(copied_root.parent) # Should be a root
        self.assertGreater(copied_root.cid, 100) # Should have a new CID

        copied_child = TplMaster.objects.filter(wo_no=self.dest_wo_no, item_id__item_code='ITEM-B').first()
        self.assertIsNotNone(copied_child)
        self.assertEqual(copied_child.parent, copied_root)
        self.assertGreater(copied_child.cid, copied_root.cid)

        # Check messages
        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), f"TPL Assembly copied successfully from WONo: {self.src_wo_no} to {self.dest_wo_no}.")
        self.assertEqual(messages_list[0].tags, 'success')

    def test_copy_assembly_action_view_post_success_under_parent(self):
        """Test successful copy action under an existing parent via POST request."""
        initial_count = TplMaster.objects.count()
        
        # Test copy of src_node_root (cid=1) to dest_wo_no under dest_parent_node (cid=100)
        url = reverse('design_tpl:copy_assembly_action') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_parent_node.cid}'
        data = {'src_cid': self.src_node_root.cid}
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 302)
        self.assertTrue(response.url.startswith(reverse('design_tpl:tplmaster_list')))

        # Verify new nodes are created and attached to the correct parent
        # There should be 2 new nodes (root and its child)
        copied_root_in_dest = TplMaster.objects.filter(wo_no=self.dest_wo_no, item_id__item_code='ITEM-A', parent=self.dest_parent_node).first()
        self.assertIsNotNone(copied_root_in_dest)
        self.assertEqual(copied_root_in_dest.parent, self.dest_parent_node)

        copied_child_in_dest = TplMaster.objects.filter(wo_no=self.dest_wo_no, item_id__item_code='ITEM-B', parent=copied_root_in_dest).first()
        self.assertIsNotNone(copied_child_in_dest)
        self.assertEqual(copied_child_in_dest.parent, copied_root_in_dest)

        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(messages_list[0].tags, 'success')

    def test_copy_assembly_action_view_post_no_src_cid(self):
        """Test copy action fails if no src_cid is provided."""
        url = reverse('design_tpl:copy_assembly_action') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}'
        data = {} # No src_cid
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # HTMX returns 200 for error messages
        self.assertIn('HX-Refresh', response.headers) # Should trigger refresh to show messages

        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "No assembly selected for copying.")
        self.assertEqual(messages_list[0].tags, 'error')

    def test_copy_assembly_action_view_post_invalid_src_cid(self):
        """Test copy action fails with invalid src_cid."""
        url = reverse('design_tpl:copy_assembly_action') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}'
        data = {'src_cid': 'not_an_int'}
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertIn('HX-Refresh', response.headers)

        messages_list = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages_list), 1)
        self.assertEqual(str(messages_list[0]), "Invalid Source Assembly ID provided.")
        self.assertEqual(messages_list[0].tags, 'error')

    def test_toggle_expand_view(self):
        """Test toggle expand checkbox functionality."""
        url = reverse('design_tpl:toggle_expand') + f'?WONoSrc={self.src_wo_no}&WONoDest={self.dest_wo_no}&DestPId={self.dest_pid}&expand_all=false'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_tpl/_tplmaster_table.html')
        # Here you would assert that the `expand_all` context variable is set correctly
        # and that the HTML content reflects the collapsed state (if implemented visually)
        self.assertContains(response, 'class="toggle-icon cursor-pointer"') # Assuming icons are always present
        # This test primarily ensures the partial is re-rendered with the correct parameter.

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for dynamic updates:**
    - The main `tplmaster_list.html` will use `hx-get` on a `div` (`#tplMasterTable-container`) to load the table content from `{% url 'design_tpl:tplmaster_table_partial' %}`. This ensures the table is loaded dynamically after the page loads and can be refreshed.
    - The "Expand Tree" checkbox uses `hx-get` and `hx-target` to re-fetch the table partial with an `expand_all` query parameter, effectively toggling the display without a full page reload.
    - The "Select to Copy" buttons on each row use `hx-get` to load the `_copy_modal.html` content into the `#modalContent` div, opening the modal.
    - The "Confirm Copy" button inside the modal uses `hx-post` to send the copy request to `{% url 'design_tpl:copy_assembly_action' %}`. It uses `hx-swap="none"` and `hx-on::after-request` to manually close the modal and trigger a `refreshTplList` event on the body to update the main table.
    - `hx-confirm` is used for client-side confirmation on the main "Copy Selected" button, similar to ASP.NET's `OnClientClick`.
    - `HX-Refresh` header is used in views to instruct HTMX to perform a full browser refresh when a complete redirect is desired (e.g., after `btnCopy_Click`).
- **Alpine.js for UI state management:**
    - Alpine.js handles the basic modal show/hide functionality using `x-data` and `x-show` (or custom class toggles like `is-active`). The `on click add .is-active to #modal` syntax in the template demonstrates Alpine.js's (or just hyperscript's, which Alpine complements) ability to manage CSS classes for modal visibility.
    - The `base.html` would contain the global Alpine.js import.
- **DataTables for list views:**
    - The `_tplmaster_table.html` includes a `<table id="tplDesignTable">`.
    - A `script` block within `tplmaster_list.html` (or a dedicated JS file) will contain `$(document).ready(function() { $('#tplDesignTable').DataTable(...); });`. Crucially, this initialization is placed within an `htmx:afterSwap` event listener, ensuring DataTables is re-initialized every time the table partial is loaded or updated by HTMX. This handles pagination, search, and sort.
- **DRY templates:**
    - Use `{% extends 'core/base.html' %}`.
    - Use partial templates (`_tplmaster_table.html`, `_copy_modal.html`) for reusable components loaded via HTMX.

### Final Notes

This comprehensive plan provides a systematic, automated approach to migrating your ASP.NET TPL Design Copy Tree module to Django. By adhering to the principles of fat models, thin views, and utilizing modern frontend techniques like HTMX and Alpine.js with DataTables, the resulting application will be highly maintainable, performant, and deliver a superior user experience. The emphasis on AI-assisted automation and clear, non-technical instructions ensures that this complex migration can be managed efficiently, delivering significant business value through reduced development effort and a modernized solution.