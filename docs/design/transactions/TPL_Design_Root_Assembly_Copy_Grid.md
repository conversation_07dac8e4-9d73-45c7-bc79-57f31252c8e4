## ASP.NET to Django Conversion Script: Root Assembly Copy

This plan outlines the systematic modernization of your ASP.NET Root Assembly Copy module to a robust, maintainable, and scalable Django application. We will leverage AI-assisted automation to transform your existing functionality, focusing on business value and efficiency.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

This conversion focuses on the "TPL Root Assembly Copy" functionality, which allows users to view existing root assemblies from a source Work Order and copy selected ones to a destination Work Order.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The ASP.NET code queries `tblDG_Item_Master`, `tblDG_BOM_Master`, and `Unit_Master`.
*   `tblDG_BOM_Master` appears to be the core table for BOM entries, with a `PId='0'` filter for root assemblies.
*   `tblDG_Item_Master` provides item details like `ItemCode` and `ManfDesc`.
*   `Unit_Master` provides the `Symbol` for Unit of Measurement.

**Identified Tables and Columns:**

*   **`tblDG_BOM_Master`**:
    *   `Id` (int, Primary Key)
    *   `CId` (int) - Likely a unique identifier for the BOM structure within the system.
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master.Id`)
    *   `WONo` (string) - Work Order Number
    *   `Qty` (decimal)
    *   `PId` (int) - Parent ID (0 for root assemblies)
    *   `CompId` (int) - Company ID
    *   `FinYearId` (int) - Financial Year ID

*   **`tblDG_Item_Master`**:
    *   `Id` (int, Primary Key)
    *   `ItemCode` (string) - Assembly Number
    *   `ManfDesc` (string) - Description
    *   `UOMBasic` (int, Foreign Key to `Unit_Master.Id`) - Unit of Measure Basic

*   **`Unit_Master`**:
    *   `Id` (int, Primary Key)
    *   `Symbol` (string) - Unit Symbol

### Step 2: Identify Backend Functionality

**Task:** Determine the business logic and operations performed by the ASP.NET code.

**Operations Identified:**

*   **Read/Display:**
    *   Retrieves a list of "root assemblies" (BOM entries with `PId = 0`) from `tblDG_BOM_Master`.
    *   Filters these entries based on `WONo`, `CompId`, and `FinYearId`.
    *   Performs joins with `tblDG_Item_Master` to get `ItemCode` and `ManfDesc`, and with `Unit_Master` for `UOMBasic` (symbol).
    *   Supports dynamic searching by `ItemCode` or `ManfDesc` using a dropdown and textbox.
    *   Includes pagination.

*   **Complex Create (Copy):**
    *   When the "Add" button is clicked, it iterates through selected (checked) root assemblies in the grid.
    *   For each selected assembly, it extracts the `CId` and `ItemId`.
    *   It then performs a "copy" operation (`fun.getRootNode`) which likely involves creating new `tblDG_BOM_Master` entries (and potentially their children recursively) in the database, associated with a *new* Work Order (`WONoDest`) and a *new* `CId` generated by `fun.getTPLCId`.
    *   Upon successful completion, it redirects to a "Work Order Tree View" page.

*   **Cancel/Navigation:**
    *   A "Cancel" button redirects the user to a "Work Order Tree View" page.

**Key Business Logic to Port:**
*   The `get_root_assemblies` function for filtering and retrieving the data for the grid.
*   The `copy_root_assemblies` function, which encapsulates the `fun.getRootNode` and `fun.getTPLCId` logic, including ID generation for the new BOM entries and potential recursive copying of child BOMs.

### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their intended roles in the user interface.

**UI Components Identified:**

*   **Header Labels:** Displaying "WO No From" and "WO No To" (read from query string parameters).
*   **Search Bar:**
    *   A `DropDownList` for selecting the search criteria (`Assembly No` or `Description`).
    *   A `TextBox` for entering the search text.
    *   A "Search" `Button` to trigger the search.
*   **Action Buttons:**
    *   "Cancel" `Button`: Redirects to the WO Tree View.
*   **Data Display Grid (`GridView`):**
    *   Displays a paginated list of root assemblies.
    *   Columns: Serial Number (SN), Select Checkbox (with a "Select All" header checkbox), Assembly No, Description, UOM, Qty.
    *   Hidden data for each row: `Id`, `CId`, `ItemId` (essential for the copy operation).
    *   Footer with an "Add" `Button` to initiate the bulk copy operation for selected items.
    *   An `EmptyDataTemplate` message if no data is found.
*   **Message Label:** `lblMsg` for displaying system messages (e.g., success messages, errors).

### Step 4: Generate Django Code

We will create a new Django application, let's call it `design_bom`, to house this functionality.

#### 4.1 Models (`design_bom/models.py`)

We will define three models to map to your existing database tables. These models will use `managed = False` to indicate that Django should not manage their schema (assuming the database already exists). The `BomMaster` model will include the business logic for retrieving and copying root assemblies.

```python
from django.db import models, transaction
from django.db.models import Q # For complex queries

class UnitMaster(models.Model):
    """
    Maps to the 'Unit_Master' table.
    Represents units of measure.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ItemMaster(models.Model):
    """
    Maps to the 'tblDG_Item_Master' table.
    Represents master data for items/assemblies.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    # UOMBasic is a ForeignKey to UnitMaster as implied by ASP.NET JOIN
    uom_basic = models.ForeignKey(UnitMaster, on_delete=models.DO_NOTHING, db_column='UOMBasic', related_name='items_basic', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class BomMaster(models.Model):
    """
    Maps to the 'tblDG_BOM_Master' table.
    Represents Bill of Material entries.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is system-managed but not identity by Django.
    c_id = models.IntegerField(db_column='CId') # Component ID, unique for a BOM structure.
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='bom_entries')
    wo_no = models.CharField(db_column='WONo', max_length=50)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4)
    p_id = models.IntegerField(db_column='PId') # Parent ID, 0 for root assemblies.
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Entry'
        verbose_name_plural = 'BOM Entries'
        ordering = ['-id'] # Matches ASP.NET 'Order by tblDG_BOM_Master.Id Desc'

    def __str__(self):
        return f"BOM Entry {self.id} (CId: {self.c_id}) for WO {self.wo_no} - {self.item.item_code}"

    @classmethod
    def get_root_assemblies(cls, wo_no, comp_id, fin_year_id, search_field=None, search_text=''):
        """
        Retrieves root assemblies (PId=0) based on WO number, company,
        financial year, and optional search criteria.
        This method encapsulates the data retrieval logic from ASP.NET's BindDataCustIMaster.
        """
        queryset = cls.objects.select_related('item', 'item__uom_basic').filter(
            wo_no=wo_no,
            p_id=0, # Only root assemblies
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # Matching ASP.NET logic: FinYearId <= current FinYearId
        )

        if search_text:
            if search_field == 'tblDG_Item_Master.ItemCode':
                queryset = queryset.filter(item__item_code__icontains=search_text)
            elif search_field == 'tblDG_Item_Master.ManfDesc':
                queryset = queryset.filter(item__manf_desc__icontains=search_text)
            # If no specific field, or a default search (like ASP.NET's fun.BindDataCustIMaster)
            else:
                queryset = queryset.filter(
                    Q(item__item_code__icontains=search_text) |
                    Q(item__manf_desc__icontains=search_text)
                )
        return queryset

    @classmethod
    def copy_root_assemblies(cls, src_bom_c_ids, src_wo_no, dest_wo_no, comp_id, session_id, fin_year_id):
        """
        Copies selected root assemblies from source WO to destination WO.
        This method encapsulates the complex logic found in ASP.NET's fun.getRootNode.
        It simulates ID generation for CId and Id, which in a real system
        might involve calling database sequences or stored procedures.
        """
        if not src_bom_c_ids:
            return 0

        copied_count = 0
        with transaction.atomic():
            # Get the current maximum IDs to generate new sequential ones
            # This is a simplification; in a real enterprise system, IDs might come
            # from database sequences, GUIDs, or complex business logic.
            # You would integrate your existing `fun.getTPLCId` and ID generation
            # logic here, potentially via raw SQL calls or a dedicated service.
            max_c_id_agg = BomMaster.objects.aggregate(models.Max('c_id'))['c_id__max']
            max_bom_id_agg = BomMaster.objects.aggregate(models.Max('id'))['id__max']

            next_c_id = (max_c_id_agg if max_c_id_agg is not None else 0) + 1
            next_bom_id = (max_bom_id_agg if max_bom_id_agg is not None else 0) + 1

            for src_c_id_str in src_bom_c_ids:
                try:
                    src_c_id = int(src_c_id_str)
                except ValueError:
                    # Skip invalid CId values
                    continue

                source_root_bom = cls.objects.filter(
                    c_id=src_c_id,
                    wo_no=src_wo_no,
                    p_id=0, # Must be a root assembly
                    comp_id=comp_id,
                    fin_year_id__lte=fin_year_id
                ).first()

                if source_root_bom:
                    # Create a new BomMaster entry for the destination WO
                    new_bom_entry = cls(
                        id=next_bom_id,  # Assign new unique ID
                        c_id=next_c_id,  # Assign new unique CId for the copied BOM
                        item=source_root_bom.item,
                        wo_no=dest_wo_no,
                        qty=source_root_bom.qty,
                        p_id=0, # Copied as a new root assembly
                        comp_id=comp_id,
                        fin_year_id=fin_year_id
                        # Other fields (e.g., SessionId from ASP.NET) would be handled here if applicable
                    )
                    new_bom_entry.save()
                    copied_count += 1

                    # Increment IDs for the next copy
                    next_c_id += 1
                    next_bom_id += 1

                    # Important: If fun.getRootNode recursively copies children,
                    # that recursive logic (e.g., a `copy_children` method)
                    # would be called here. For this example, we only show root copying.
                    # Example conceptual call:
                    # cls.copy_children(source_root_bom.id, new_bom_entry.id, src_wo_no, dest_wo_no, comp_id, session_id, fin_year_id)

        return copied_count

```

#### 4.2 Forms (`design_bom/forms.py`)

A simple form for the search functionality. The "Add" operation is a direct batch action from the grid, not a traditional form submission.

```python
from django import forms

class RootAssemblySearchForm(forms.Form):
    """
    Form for handling search criteria on the Root Assembly Copy page.
    """
    search_field = forms.ChoiceField(
        choices=[
            ('0', 'Select'), # Default option, matches ASP.NET
            ('tblDG_Item_Master.ItemCode', 'Assembly No'),
            ('tblDG_Item_Master.ManfDesc', 'Description'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    search_text = forms.CharField(
        max_length=350,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

```

#### 4.3 Views (`design_bom/views.py`)

We will use Django Class-Based Views (CBVs) for efficiency and maintainability.
*   `RootAssemblyCopyListView`: Renders the main page, handles initial data load and search form.
*   `RootAssemblyTablePartialView`: An HTMX-specific endpoint to render just the DataTables content.
*   `RootAssemblyCopyActionView`: Handles the POST request for copying selected assemblies.
*   `WoTreeView`: A placeholder view for the redirect target.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render, redirect

from .models import BomMaster, ItemMaster, UnitMaster
from .forms import RootAssemblySearchForm

class RootAssemblyCopyListView(ListView):
    """
    Main view to display the Root Assembly Copy page.
    Handles initial data loading and search form display.
    """
    model = BomMaster
    template_name = 'design_bom/rootassembly/list.html'
    context_object_name = 'bom_entries'
    # paginate_by = 12 # DataTables handles client-side pagination, so not strictly needed here.

    def get_queryset(self):
        """
        Builds the queryset based on URL parameters and search form.
        Delegates complex filtering to the BomMaster model.
        """
        # Retrieve session-like data from request.session.
        # In a production environment, ensure 'compid', 'finyear', 'username'
        # are properly set in the user's session (e.g., via authentication middleware).
        wo_no_src = self.request.GET.get('WONoSrc', 'N/A')
        comp_id = self.request.session.get('compid', 1) # Default if not in session
        fin_year_id = self.request.session.get('finyear', 2023) # Default if not in session

        search_form = RootAssemblySearchForm(self.request.GET)
        search_field = None
        search_text = ''
        if search_form.is_valid():
            search_field = search_form.cleaned_data.get('search_field')
            search_text = search_form.cleaned_data.get('search_text')

        return BomMaster.get_root_assemblies(
            wo_no=wo_no_src,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_field=search_field,
            search_text=search_text
        )

    def get_context_data(self, **kwargs):
        """
        Adds context data required for the template, including WO numbers and search form.
        """
        context = super().get_context_data(**kwargs)
        # Populate labels for WO numbers from query parameters
        context['wono_from'] = self.request.GET.get('WONoSrc', 'N/A')
        context['wono_to'] = self.request.GET.get('WONoDest', 'N/A')
        context['search_form'] = RootAssemblySearchForm(self.request.GET)
        # Display messages passed via query string (like ASP.NET) or Django messages framework
        context['msg'] = self.request.GET.get('msg', '')
        return context

class RootAssemblyTablePartialView(RootAssemblyCopyListView):
    """
    Renders only the HTML table fragment for HTMX requests.
    This allows dynamic updates of the table content without full page reloads.
    """
    template_name = 'design_bom/rootassembly/_table.html'

    def get_context_data(self, **kwargs):
        """
        Prepares data for the table partial.
        """
        context = super().get_context_data(**kwargs)
        # DataTables will handle pagination and filtering on the client-side
        # so we pass the full filtered queryset for the HTMX load.
        context[self.context_object_name] = self.get_queryset()
        return context

class RootAssemblyCopyActionView(View):
    """
    Handles the POST request to copy selected root assemblies.
    Orchestrates the business logic by calling the model's class method.
    """
    def post(self, request, *args, **kwargs):
        selected_c_ids = request.POST.getlist('selected_c_ids') # Get list of CIds from checked checkboxes

        # Retrieve session-like data and WO numbers from request (assuming GET params are preserved)
        wo_no_src = request.GET.get('WONoSrc', 'N/A')
        wo_no_dest = request.GET.get('WONoDest', 'N/A')
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2023)
        session_id = request.session.get('username', 'anonymous') # Maps to ASP.NET SessionId

        copied_count = BomMaster.copy_root_assemblies(
            src_bom_c_ids=selected_c_ids,
            src_wo_no=wo_no_src,
            dest_wo_no=wo_no_dest,
            comp_id=comp_id,
            session_id=session_id,
            fin_year_id=fin_year_id
        )

        if copied_count > 0:
            messages.success(request, f"{copied_count} root assemblies copied successfully.")
            # ASP.NET redirects to TPL_Design_WO_TreeView.aspx with parameters.
            # With HTMX, we can send an HX-Redirect header for a full page navigation.
            redirect_url_base = reverse_lazy('wo_tree_view') # URL for the WO Tree View page
            redirect_url = f"{redirect_url_base}?WONo={wo_no_dest}&ModId=3&SubModId=23&Msg=Copy of root assemblies are done."
            
            response = HttpResponse(status=204) # 204 No Content for HTMX to process headers
            response['HX-Redirect'] = redirect_url
            return response
        else:
            messages.error(request, "No assemblies were selected or copied.")
            # If no copy, simply trigger a refresh of the table to show updated state/error
            response = HttpResponse(status=204)
            response['HX-Trigger'] = 'refreshRootAssemblyList'
            return response

class WoTreeView(View):
    """
    Placeholder view for the Work Order Tree View page.
    This simulates the redirect target from the ASP.NET application.
    In a full migration, this would be the actual WO Tree View page.
    """
    template_name = 'design_bom/wo_tree_view.html'

    def get(self, request, *args, **kwargs):
        context = {
            'wono': request.GET.get('WONo'),
            'mod_id': request.GET.get('ModId'),
            'sub_mod_id': request.GET.get('SubModId'),
            'msg': request.GET.get('Msg', 'Redirected to Work Order Tree View.'),
        }
        return render(request, self.template_name, context)

```

#### 4.4 Templates

**`design_bom/rootassembly/list.html`**

This template renders the main page layout, including the header, search form, and a container for the dynamic DataTables content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">TPL Root Assembly Copy</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="mb-4 text-gray-700">
            <strong class="font-semibold">WO No From:</strong> <span id="lblWONoFrm" class="text-blue-600">{{ wono_from }}</span>
            <strong class="ml-4 font-semibold">To:</strong> <span id="lblWONo" class="text-blue-600">{{ wono_to }}</span>
        </div>
        
        <form hx-get="{% url 'rootassembly_table' %}" hx-target="#rootAssemblyTable-container" hx-swap="innerHTML" hx-trigger="submit, keyup changed delay:500ms from:#id_search_text">
            {% csrf_token %} {# CSRF token for forms #}
            <div class="flex flex-col sm:flex-row items-end space-y-4 sm:space-y-0 sm:space-x-4 mb-4">
                <div class="w-full sm:w-1/4">
                    <label for="{{ search_form.search_field.id_for_label }}" class="sr-only">Search Field</label>
                    {{ search_form.search_field }}
                </div>
                <div class="w-full sm:flex-grow">
                    <label for="{{ search_form.search_text.id_for_label }}" class="sr-only">Search Text</label>
                    {{ search_form.search_text }}
                </div>
                <button type="submit" class="w-full sm:w-auto bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                    Search
                </button>
                <a href="{% url 'wo_tree_view' %}?WONo={{ wono_to }}&ModId=3&SubModId=23" class="w-full sm:w-auto bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded redbox text-center">
                    Cancel
                </a>
            </div>
            {% if msg %}
            <p id="lblMsg" class="text-red-500 text-sm mt-2">{{ msg }}</p>
            {% endif %}
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                    <p class="text-sm {% if message.tags %}text-{{ message.tags }}-600{% endif %}">{{ message }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="rootAssemblyTable-container"
         hx-trigger="load, refreshRootAssemblyList from:body"
         hx-get="{% url 'rootassembly_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading assemblies...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for managing checkbox selection state (select all/deselect all)
    document.addEventListener('alpine:init', () => {
        Alpine.data('rootAssemblySelection', () => ({
            selectAll: false,
            init() {
                // Initialize selectAll state based on existing checkboxes
                this.updateSelectAll();
                // Watch for changes on individual checkboxes to update selectAll
                this.$el.querySelectorAll('input[type="checkbox"][name="selected_c_ids"]').forEach(checkbox => {
                    checkbox.addEventListener('change', () => this.updateSelectAll());
                });
            },
            toggleSelectAll() {
                // This method is called when the header checkbox is clicked
                const checkboxes = this.$el.querySelectorAll('input[type="checkbox"][name="selected_c_ids"]');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = this.selectAll;
                });
            },
            updateSelectAll() {
                // This method updates the header checkbox based on individual checkboxes
                const checkboxes = this.$el.querySelectorAll('input[type="checkbox"][name="selected_c_ids"]');
                if (checkboxes.length === 0) {
                    this.selectAll = false;
                    return;
                }
                const allChecked = Array.from(checkboxes).every(checkbox => checkbox.checked);
                this.selectAll = allChecked;
            }
        }));
    });

    // Client-side confirmation function for the "Add" button
    // HTMX's hx-confirm can also be used, but this matches the original OnClientClick
    function confirmationAdd() {
        return confirm('Are you sure you want to add the selected assemblies?');
    }
</script>
{% endblock %}
```

**`design_bom/rootassembly/_table.html`**

This is a partial template that will be loaded dynamically into `list.html` using HTMX. It contains the DataTables structure and the "Add" button.

```html
<div x-data="rootAssemblySelection()">
    <table id="rootAssemblyTable" class="min-w-full bg-white table-auto border-collapse border border-gray-300">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200 w-12">SN</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200 w-12">
                    <input type="checkbox" id="chkSelectAll" x-model="selectAll" @change="toggleSelectAll">
                </th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200">Assembly No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200">Description</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200">UOM</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-600 uppercase tracking-wider border-b border-gray-200">Qty</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in bom_entries %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200 text-center">
                    <input type="checkbox" name="selected_c_ids" value="{{ entry.c_id }}" x-bind:checked="selectAll" @change="updateSelectAll">
                    {# Hidden fields for potentially needed IDs, as in original ASP.NET #}
                    <input type="hidden" name="entry_id_{{ entry.c_id }}" value="{{ entry.id }}">
                    <input type="hidden" name="entry_item_id_{{ entry.c_id }}" value="{{ entry.item.id }}">
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200 text-center">{{ entry.item.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200">{{ entry.item.manf_desc }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200 text-center">{{ entry.item.uom_basic.symbol|default:"N/A" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200 text-right">{{ entry.qty }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-lg text-red-600 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot class="bg-gray-100">
            <tr>
                <td colspan="6" class="py-3 px-4 text-right border-t border-gray-300">
                    <button type="button"
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded redbox"
                        hx-post="{% url 'rootassembly_copy_action' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
                        hx-include="[name='selected_c_ids']" {# HTMX will collect all elements with this name #}
                        hx-target="body" {# Target body to allow HX-Redirect to navigate #}
                        hx-swap="none" {# We want a full page redirect or trigger #}
                        onclick="return confirmationAdd()"> {# Client-side confirmation #}
                        Add
                    </button>
                </td>
            </tr>
        </tfoot>
    </table>

    <script>
        // DataTables initialization script. This runs each time the table partial is loaded via HTMX.
        $(document).ready(function() {
            if ($.fn.DataTable.isDataTable('#rootAssemblyTable')) {
                $('#rootAssemblyTable').DataTable().destroy(); // Destroy existing instance if it exists
            }
            $('#rootAssemblyTable').DataTable({
                "pageLength": 12, // Matches ASP.NET PageSize
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers", // For full pagination controls
                "dom": '<"flex flex-col sm:flex-row justify-between mb-4"lf>rt<"flex flex-col sm:flex-row justify-between mt-4"ip>',
                "language": { // Basic custom labels for DataTables
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                },
                "columnDefs": [
                    { "orderable": false, "targets": [1] } // Disable sorting for checkbox column
                ]
            });
        });
    </script>
</div>
```

**`design_bom/wo_tree_view.html`** (Dummy template for redirection target)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-4">Work Order Tree View (Placeholder)</h2>
    
    {% if messages %}
        <div class="mt-4">
            {% for message in messages %}
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                <strong class="font-bold">Success!</strong>
                <span class="block sm:inline">{{ message }}</span>
            </div>
            {% endfor %}
        </div>
    {% elif msg %}
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
            <strong class="font-bold">Info:</strong>
            <span class="block sm:inline">{{ msg }}</span>
        </div>
    {% endif %}

    <p class="mt-4 text-gray-700">This page would display the BOM structure for Work Order: <strong class="text-blue-600">{{ wono }}</strong>.</p>
    <p class="text-sm text-gray-500">Module ID: {{ mod_id }}, Sub Module ID: {{ sub_mod_id }}</p>
    
    <p class="mt-6 text-sm text-gray-500">
        This is a placeholder for the actual Work Order Tree View page that the ASP.NET application
        redirects to after copying root assemblies. In a real system, you'd implement the full
        functionality of the WO Tree View here.
    </p>
</div>
{% endblock %}
```

#### 4.5 URLs (`design_bom/urls.py`)

Defines the URL patterns for accessing the views.

```python
from django.urls import path
from .views import (
    RootAssemblyCopyListView,
    RootAssemblyTablePartialView,
    RootAssemblyCopyActionView,
    WoTreeView # For the redirection target
)

urlpatterns = [
    # Main page to display the root assembly list
    path('root-assembly-copy/', RootAssemblyCopyListView.as_view(), name='rootassembly_list'),
    
    # HTMX endpoint to load the table content dynamically
    path('root-assembly-copy/table/', RootAssemblyTablePartialView.as_view(), name='rootassembly_table'),
    
    # HTMX endpoint to handle the POST request for copying assemblies
    path('root-assembly-copy/action/', RootAssemblyCopyActionView.as_view(), name='rootassembly_copy_action'),
    
    # Placeholder URL for the Work Order Tree View redirection target
    path('wo-tree-view/', WoTreeView.as_view(), name='wo_tree_view'),
]
```

#### 4.6 Tests (`design_bom/tests.py`)

Comprehensive tests for models and views ensure functionality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from django.contrib.messages import get_messages
from django.db import models

from .models import BomMaster, ItemMaster, UnitMaster
from .forms import RootAssemblySearchForm

# --- Mixin for setting up common test data ---
class DesignBomSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for managed=False models in an in-memory database
        # For actual `managed=False` tests against a real DB, you would typically
        # rely on existing data or a more sophisticated test setup.
        # Here, we simulate by creating objects directly, assuming PKs are handled.
        cls.unit_pcs = UnitMaster.objects.create(id=1, symbol='PCS')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')

        cls.item_assy_a = ItemMaster.objects.create(id=101, item_code='ASSY-A', manf_desc='Assembly A Main', uom_basic=cls.unit_pcs)
        cls.item_assy_b = ItemMaster.objects.create(id=102, item_code='ASSY-B', manf_desc='Assembly B Sub-Assembly', uom_basic=cls.unit_kg)
        cls.item_assy_c = ItemMaster.objects.create(id=103, item_code='ASSY-C', manf_desc='Assembly C Spare Part', uom_basic=cls.unit_pcs)
        cls.item_assy_d = ItemMaster.objects.create(id=104, item_code='ITEM-X', manf_desc='Raw Material X', uom_basic=cls.unit_kg)


        cls.wo_src = 'WO-SRC-001'
        cls.wo_dest = 'WO-DEST-001'
        cls.comp_id = 1
        cls.fin_year_id = 2023

        # Root assemblies for source WO (PId=0)
        cls.bom_root_a = BomMaster.objects.create(
            id=1, c_id=1001, item=cls.item_assy_a, wo_no=cls.wo_src, qty=1.0, p_id=0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        cls.bom_root_b = BomMaster.objects.create(
            id=2, c_id=1002, item=cls.item_assy_b, wo_no=cls.wo_src, qty=2.5, p_id=0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        # Non-root BOM entry (PId != 0)
        cls.bom_child_a = BomMaster.objects.create(
            id=3, c_id=1003, item=cls.item_assy_c, wo_no=cls.wo_src, qty=5.0, p_id=cls.bom_root_a.id,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        # Another root assembly for a different WO, should not be retrieved by default
        cls.bom_root_other_wo = BomMaster.objects.create(
            id=4, c_id=1004, item=cls.item_assy_d, wo_no='WO-OTHER', qty=10.0, p_id=0,
            comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        
        # Capture current max IDs for mocking ID generation in copy operations
        cls._max_bom_id = BomMaster.objects.aggregate(models.Max('id'))['id__max']
        cls._max_bom_c_id = BomMaster.objects.aggregate(models.Max('c_id'))['c_id__max']


class BomMasterModelTest(DesignBomSetupMixin, TestCase):
    """
    Unit tests for the BomMaster model and its class methods.
    """
    def test_bom_master_creation(self):
        obj = BomMaster.objects.get(id=self.bom_root_a.id)
        self.assertEqual(obj.item.item_code, 'ASSY-A')
        self.assertEqual(obj.wo_no, self.wo_src)
        self.assertEqual(obj.p_id, 0)
        self.assertEqual(obj.item.uom_basic.symbol, 'PCS')
        self.assertEqual(str(obj), f"BOM Entry {obj.id} (CId: {obj.c_id}) for WO {obj.wo_no} - {obj.item.item_code}")

    def test_get_root_assemblies_no_search(self):
        queryset = BomMaster.get_root_assemblies(self.wo_src, self.comp_id, self.fin_year_id)
        self.assertEqual(queryset.count(), 2)
        self.assertIn(self.bom_root_a, queryset)
        self.assertIn(self.bom_root_b, queryset)
        self.assertNotIn(self.bom_child_a, queryset) # Should not include child
        self.assertNotIn(self.bom_root_other_wo, queryset) # Should not include other WO

    def test_get_root_assemblies_search_item_code(self):
        queryset = BomMaster.get_root_assemblies(self.wo_src, self.comp_id, self.fin_year_id, 'tblDG_Item_Master.ItemCode', 'ASSY-A')
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.bom_root_a, queryset)
        self.assertNotIn(self.bom_root_b, queryset)

    def test_get_root_assemblies_search_description(self):
        queryset = BomMaster.get_root_assemblies(self.wo_src, self.comp_id, self.fin_year_id, 'tblDG_Item_Master.ManfDesc', 'Sub-Assembly')
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.bom_root_b, queryset)
        self.assertNotIn(self.bom_root_a, queryset)

    def test_get_root_assemblies_search_default_field(self):
        # Search both item_code and manf_desc if search_field is '0' or None
        queryset = BomMaster.get_root_assemblies(self.wo_src, self.comp_id, self.fin_year_id, '0', 'ASSY')
        self.assertEqual(queryset.count(), 2) # A and B contain 'ASSY'
        self.assertIn(self.bom_root_a, queryset)
        self.assertIn(self.bom_root_b, queryset)

    @patch('design_bom.models.BomMaster.objects.aggregate')
    def test_copy_root_assemblies_single(self, mock_aggregate):
        # Mocking aggregate for new ID generation, as DB doesn't auto-increment for managed=False directly
        # First call for max_c_id, second for max_id
        mock_aggregate.side_effect = [
            {'c_id__max': self._max_bom_c_id},
            {'id__max': self._max_bom_id},
        ]
        
        selected_c_ids = [str(self.bom_root_a.c_id)]
        copied_count = BomMaster.copy_root_assemblies(
            selected_c_ids, self.wo_src, self.wo_dest, self.comp_id, 'test_user', self.fin_year_id
        )
        self.assertEqual(copied_count, 1)

        # Verify the new entry exists
        new_entry = BomMaster.objects.filter(wo_no=self.wo_dest, item=self.item_assy_a).first()
        self.assertIsNotNone(new_entry)
        self.assertEqual(new_entry.wo_no, self.wo_dest)
        self.assertEqual(new_entry.item.item_code, 'ASSY-A')
        self.assertEqual(new_entry.p_id, 0)
        self.assertEqual(new_entry.c_id, self._max_bom_c_id + 1)
        self.assertEqual(new_entry.id, self._max_bom_id + 1)

    @patch('design_bom.models.BomMaster.objects.aggregate')
    def test_copy_root_assemblies_multiple(self, mock_aggregate):
        # Mocking aggregate for new ID generation for each copied item
        mock_aggregate.side_effect = [
            {'c_id__max': self._max_bom_c_id}, {'id__max': self._max_bom_id}, # For first copy
            {'c_id__max': self._max_bom_c_id + 1}, {'id__max': self._max_bom_id + 1}, # For second copy
        ]
        
        selected_c_ids = [str(self.bom_root_a.c_id), str(self.bom_root_b.c_id)]
        copied_count = BomMaster.copy_root_assemblies(
            selected_c_ids, self.wo_src, self.wo_dest, self.comp_id, 'test_user', self.fin_year_id
        )
        self.assertEqual(copied_count, 2)
        self.assertTrue(BomMaster.objects.filter(wo_no=self.wo_dest, item=self.item_assy_a).exists())
        self.assertTrue(BomMaster.objects.filter(wo_no=self.wo_dest, item=self.item_assy_b).exists())

    def test_copy_root_assemblies_no_selection(self):
        copied_count = BomMaster.copy_root_assemblies(
            [], self.wo_src, self.wo_dest, self.comp_id, 'test_user', self.fin_year_id
        )
        self.assertEqual(copied_count, 0)
        self.assertFalse(BomMaster.objects.filter(wo_no=self.wo_dest).exists())

    def test_copy_root_assemblies_invalid_c_id(self):
        copied_count = BomMaster.copy_root_assemblies(
            ['invalid_id', str(self.bom_root_a.c_id)], self.wo_src, self.wo_dest, self.comp_id, 'test_user', self.fin_year_id
        )
        # Only the valid CId should be copied
        self.assertEqual(copied_count, 1)


class RootAssemblyViewsTest(DesignBomSetupMixin, TestCase):
    """
    Integration tests for the Django views.
    """
    def setUp(self):
        self.client = Client()
        # Mock session data as it's used by views (similar to ASP.NET Session)
        self.session = self.client.session
        self.session['compid'] = self.comp_id
        self.session['finyear'] = self.fin_year_id
        self.session['username'] = 'test_user'
        self.session.save()

        # URLs with query parameters as they are used in ASP.NET navigation
        self.list_url = reverse('rootassembly_list') + f'?WONoSrc={self.wo_src}&WONoDest={self.wo_dest}'
        self.table_url = reverse('rootassembly_table') + f'?WONoSrc={self.wo_src}&WONoDest={self.wo_dest}'
        self.copy_action_url = reverse('rootassembly_copy_action') + f'?WONoSrc={self.wo_src}&WONoDest={self.wo_dest}'
        self.wo_tree_view_url = reverse('wo_tree_view')


    def test_list_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/rootassembly/list.html')
        self.assertIn('bom_entries', response.context)
        self.assertEqual(response.context['bom_entries'].count(), 2) # Should show A and B
        self.assertContains(response, self.item_assy_a.item_code)
        self.assertContains(response, self.item_assy_b.item_code)
        self.assertEqual(response.context['wono_from'], self.wo_src)
        self.assertEqual(response.context['wono_to'], self.wo_dest)
        self.assertIsInstance(response.context['search_form'], RootAssemblySearchForm)
        self.assertContains(response, 'TPL Root Assembly Copy')

    def test_table_partial_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/rootassembly/_table.html')
        self.assertIn('bom_entries', response.context)
        self.assertEqual(response.context['bom_entries'].count(), 2)
        # Check for HTMX/Alpine.js specific content in the partial
        self.assertContains(response, 'x-data="rootAssemblySelection()"')
        self.assertContains(response, 'id="rootAssemblyTable"')
        self.assertContains(response, 'hx-post') # Check HTMX attribute for the Add button

    def test_list_view_search_item_code(self):
        search_url = f"{self.list_url}&search_field=tblDG_Item_Master.ItemCode&search_text=ASSY-A"
        response = self.client.get(search_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/rootassembly/list.html')
        self.assertEqual(response.context['bom_entries'].count(), 1)
        self.assertIn(self.bom_root_a, response.context['bom_entries'])
        self.assertNotIn(self.bom_root_b, response.context['bom_entries'])

    def test_list_view_search_description(self):
        search_url = f"{self.list_url}&search_field=tblDG_Item_Master.ManfDesc&search_text=Sub-Assembly"
        response = self.client.get(search_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/rootassembly/list.html')
        self.assertEqual(response.context['bom_entries'].count(), 1)
        self.assertIn(self.bom_root_b, response.context['bom_entries'])
        self.assertNotIn(self.bom_root_a, response.context['bom_entries'])

    @patch('design_bom.models.BomMaster.copy_root_assemblies')
    def test_copy_action_view_post_success(self, mock_copy_root_assemblies):
        mock_copy_root_assemblies.return_value = 1 # Simulate 1 assembly copied

        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'selected_c_ids': [str(self.bom_root_a.c_id)]}
        response = self.client.post(self.copy_action_url, data, **headers)

        self.assertEqual(response.status_code, 204) # HTMX 204 No Content for HX-Redirect
        self.assertIn('HX-Redirect', response.headers)
        expected_redirect_url = f"{self.wo_tree_view_url}?WONo={self.wo_dest}&ModId=3&SubModId=23&Msg=Copy of root assemblies are done."
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)
        
        # Verify model method was called with correct arguments
        mock_copy_root_assemblies.assert_called_once_with(
            src_bom_c_ids=[str(self.bom_root_a.c_id)],
            src_wo_no=self.wo_src,
            dest_wo_no=self.wo_dest,
            comp_id=self.comp_id,
            session_id='test_user',
            fin_year_id=self.fin_year_id
        )
        
        # Verify success message is added
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "1 root assemblies copied successfully.")
        self.assertEqual(messages[0].level_tag, 'success')


    @patch('design_bom.models.BomMaster.copy_root_assemblies')
    def test_copy_action_view_post_no_selection(self, mock_copy_root_assemblies):
        mock_copy_root_assemblies.return_value = 0 # Simulate no assemblies copied

        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'selected_c_ids': []} # No checkboxes selected
        response = self.client.post(self.copy_action_url, data, **headers)

        self.assertEqual(response.status_code, 204) # HTMX 204 No Content for HX-Trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshRootAssemblyList')
        
        # Verify model method was called with empty list
        mock_copy_root_assemblies.assert_called_once_with(
            src_bom_c_ids=[],
            src_wo_no=self.wo_src,
            dest_wo_no=self.wo_dest,
            comp_id=self.comp_id,
            session_id='test_user',
            fin_year_id=self.fin_year_id
        )
        
        # Verify error message is added
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "No assemblies were selected or copied.")
        self.assertEqual(messages[0].level_tag, 'error')


    def test_cancel_button_redirect_url(self):
        # The cancel button is a simple <a> tag, its redirection is handled by the browser.
        # We test that the link generated in the template is correct.
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        expected_cancel_link = f'{self.wo_tree_view_url}?WONo={self.wo_dest}&ModId=3&SubModId=23'
        self.assertContains(response, f'href="{expected_cancel_link}"')

    def test_wo_tree_view_get(self):
        test_wo = 'WO-ABC'
        test_mod = '5'
        test_sub_mod = '30'
        test_msg = 'Test message for tree view'
        url = f"{self.wo_tree_view_url}?WONo={test_wo}&ModId={test_mod}&SubModId={test_sub_mod}&Msg={test_msg}"
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_bom/wo_tree_view.html')
        self.assertContains(response, test_wo)
        self.assertContains(response, test_msg)
        self.assertContains(response, test_mod)
        self.assertContains(response, test_sub_mod)
```

### Step 5: HTMX and Alpine.js Integration

**HTMX for Dynamic Content and Actions:**
*   The `list.html` template uses `hx-get` on the `rootAssemblyTable-container` to load the `_table.html` partial. This is triggered on initial page `load` and whenever a `refreshRootAssemblyList` event is dispatched from the `body` (e.g., after a successful copy operation).
*   The search form uses `hx-get` to trigger a re-load of the `_table.html` with new search parameters, updating the grid dynamically.
*   The "Add" button in `_table.html` uses `hx-post` to send selected `c_id` values to the `rootassembly_copy_action` URL. `hx-include="[name='selected_c_ids']"` ensures all checked checkboxes are submitted.
*   The view's response to the `hx-post` either sends an `HX-Redirect` header for a full page navigation (if successful) or an `HX-Trigger` to refresh the table (if there was an error or no items copied).

**Alpine.js for UI State Management:**
*   An `Alpine.data` component named `rootAssemblySelection` is defined to manage the state of the "Select All" checkbox and individual row checkboxes.
*   `x-data="rootAssemblySelection()"` initializes this component on the `_table.html` content.
*   `x-model="selectAll"` binds the header checkbox to an Alpine variable.
*   `@change="toggleSelectAll"` and `@change="updateSelectAll"` handle the logic for checking/unchecking all checkboxes and updating the "Select All" state when individual boxes are clicked. This replicates the `chkSelectAll_CheckedChanged` functionality from ASP.NET client-side.

**DataTables for List Views:**
*   The `_table.html` partial includes a JavaScript snippet that initializes DataTables on the `rootAssemblyTable` element.
*   The initialization uses options like `pageLength` and `lengthMenu` to match the ASP.NET GridView's pagination.
*   DataTables provides client-side searching, sorting, and pagination, enhancing user experience without requiring server-side logic for every interaction.
*   The `dom` and `language` options provide basic styling and text customization for DataTables, aligning with modern UI principles.

---

### Final Notes

*   **Session Data:** The `compid`, `finyear`, and `username` are assumed to be available in `request.session`. In a real application, ensure these are properly set during user login or initialization.
*   **ID Generation:** The `BomMaster.copy_root_assemblies` method includes a simplified mechanism for generating new `id` and `c_id` values. In a production environment, this logic would need to precisely replicate your existing `fun.getTPLCId` and other ID management strategies, which might involve integrating with database sequences or specific stored procedures.
*   **Error Handling:** Basic `messages` are used for feedback. A robust application would have more comprehensive error handling and user feedback mechanisms.
*   **Security:** Ensure proper authentication and authorization are implemented in your Django application to protect these endpoints. Django's built-in auth system and permission decorators can be used.