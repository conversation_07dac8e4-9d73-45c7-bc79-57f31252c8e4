This document outlines a comprehensive Django modernization plan for your legacy ASP.NET application, specifically addressing the `BOM_Design_Print_Tree.aspx` page and its associated C# code-behind. This plan adheres strictly to modern Django 5.0+ best practices, emphasizing fat models, thin views, HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience without traditional JavaScript.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (`core/base.html`).
- Focus ONLY on component-specific code for the current module (`design` app).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables. The core data is fetched by the `GetDataTable` method, which queries `tblDG_BOM_Master`, `tblDG_Item_Master`, `tblHR_OfficeStaff`, and `Unit_Master`. It also calls a stored procedure `Get_BOM_DateWise`. The `RadTreeList` uses `CId` as `DataKeyNames` and `PId` as `ParentDataKeyNames`, indicating a hierarchical (Bill of Materials) structure.

**Identified Tables and Key Columns:**

*   **`tblDG_BOM_Master`**:
    *   `Id` (Primary Key - inferred)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`)
    *   `WONo` (Work Order Number)
    *   `PId` (Parent ID for BOM structure)
    *   `CId` (Child ID for BOM structure)
    *   `Qty` (Quantity of the child in the parent BOM)
    *   `SysDate` (System/Entry Date)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff.EmpId`)
    *   `CompId` (Company ID)
    *   `FinYearId` (Financial Year ID)

*   **`tblDG_Item_Master`**:
    *   `Id` (Primary Key)
    *   `ItemCode` (Item Code)
    *   `PartNo` (Part Number)
    *   `ManfDesc` (Manufacturer Description/Description)
    *   `FileName` (File name for drawing/image)
    *   `FileData` (Binary data for drawing/image - inferred from `DownloadFile.aspx`)
    *   `ContentType` (Content type for drawing/image - inferred)
    *   `AttName` (Attachment name for specification sheet)
    *   `AttData` (Binary data for specification sheet - inferred)
    *   `AttContentType` (Content type for specification sheet - inferred)
    *   `CId` (Item Category ID - used for 'BoughtOut' vs 'Manufacturing' distinction)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id` for Unit of Measure)
    *   `Revision` (Revision Number)

*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (Primary Key)
    *   `Title` (Employee Title)
    *   `EmployeeName` (Employee Name)

*   **`Unit_Master`**:
    *   `Id` (Primary Key)
    *   `Symbol` (Unit Symbol/Name)

### Step 2: Identify Backend Functionality

**Analysis:**
The page primarily focuses on **Read** operations, displaying a hierarchical Bill of Materials (BOM) based on various filters and parameters.

*   **Read (Display):**
    *   `GetDataTable` method is the core data retrieval, constructing a `DataTable` from multiple joined tables and a stored procedure.
    *   It filters data by `WONo`, `StartDate`, `UpToDate`, `CompId`, `FinYearId`, and `drpValue` (for 'All', 'BoughtOut', 'Manufacturing' items).
    *   It performs complex calculations for `BOMQty` based on the `PId`/`CId` hierarchy using `fun.BOMTreeQty`.
    *   It determines 'View' status for `Download` and `DownloadSpec` buttons based on file existence.
    *   Data is then bound to a Telerik `RadTreeList` (which will be replaced by DataTables).

*   **Read (File Download):**
    *   `RadTreeList1_ItemCommand` for `Download` and `DownloadSpec` triggers redirects to `DownloadFile.aspx` to serve files based on `ItemId` and file type.

*   **Navigation/Redirection:**
    *   `Page_PreRender`: If an item in `RadTreeList1` is selected, it redirects to `BOM_Design_Print_Cry.aspx` with various parameters, likely for a detailed report or print.
    *   `btnCancel_Click`: Redirects to `BOM_Design_PrintWo.aspx`.
    *   `ImageButton1_Click`: Redirects to `BOM_Design_Print_Cry.aspx` for a full export/report.

*   **Filtering/Interaction:**
    *   `DropDownList1_SelectedIndexChanged`: Changes data displayed based on item type filter (`All`, `BoughtOut`, `Manufacturing`), triggering a full postback.
    *   `CheckBox1_CheckedChanged`: Toggles expansion/collapse of the Telerik `RadTreeList` (a UI feature, does not change data fetched), triggering a full postback. This specific UI feature (tree expansion/collapse) will be abstracted as a flat `DataTables` view, or it would require a dedicated tree component library. Given `DataTables` requirement, we will focus on flat display.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page uses standard Web Forms controls and Telerik components.

*   **Labels:** `Label2` (displays Wo No), `lblasslymsg` (displays messages).
*   **Input Controls:**
    *   `DropDownList1`: For filtering BOM items by type.
    *   `CheckBox1`: For expanding/collapsing the tree (UI only).
*   **Buttons:**
    *   `btnCancel`: For navigation.
    *   `ImageButton1`: For export/report.
    *   `RadButton` (inside `RadTreeList`): For `Select`, `Download`, `DownloadSpec` actions.
*   **Data Display:**
    *   `telerik:RadTreeList`: The main data grid, displaying BOM items hierarchically with columns for `ItemId`, `WONo`, `PId`, `CId`, `Item Code`, `Description`, `UOM`, `UnitQty`, `BOMQty`, `Download`, `DownloadSpec`, `Entry Date`, `Entered by`, `Revision`. This will be replaced by a `DataTables` table.
*   **AJAX:**
    *   `telerik:RadAjaxPanel`: Intended for partial page updates, but `AutoPostBack` on other controls suggests mixed behavior. HTMX will provide superior and simpler partial updates.

### Step 4: Generate Django Code

We will create a new Django application named `design` to encapsulate this module's functionality.

#### 4.1 Models (`design/models.py`)

We will define models that map directly to the existing database tables using `managed = False`. We also introduce a custom manager for `BOMMaster` to encapsulate the complex data retrieval and BOM quantity calculation logic.

```python
from django.db import models
from datetime import datetime # For date parsing

# Helper function placeholder for complex date parsing if needed, similar to fun.FromDate
def parse_date_str(date_str, format='%m/%d/%Y'):
    """Converts date string (e.g., 'MM/DD/YYYY') to a datetime.date object."""
    try:
        return datetime.strptime(date_str, format).date()
    except (ValueError, TypeError):
        return None # Or raise an error, depending on error handling strategy

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class OfficeStaff(models.Model):
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=255, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''}.{self.employeename or ''}".strip('.')

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    partno = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    manfdesc = models.TextField(db_column='ManfDesc', blank=True, null=True)
    filename = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    filedata = models.BinaryField(db_column='FileData', blank=True, null=True) # Assuming BLOB/VARBINARY(MAX)
    contenttype = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    attname = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    attdata = models.BinaryField(db_column='AttData', blank=True, null=True) # Assuming BLOB/VARBINARY(MAX)
    attcontenttype = models.CharField(db_column='AttContentType', max_length=255, blank=True, null=True)
    cid = models.IntegerField(db_column='CId', blank=True, null=True) # Item Category ID, null for Manufacturing
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    revision = models.CharField(db_column='Revision', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.itemcode or self.partno or f"Item {self.id}"

class BOMManager(models.Manager):
    def get_bom_data_for_display(self, wono_src, drp_value, start_date_str, up_to_date_str, comp_id, fin_year_id):
        # Parse dates to Python date objects
        start_date = parse_date_str(start_date_str)
        up_to_date = parse_date_str(up_to_date_str)
        
        if not (start_date and up_to_date):
            # Handle error or return empty if dates are invalid
            return []

        # Base queryset joining relevant tables
        queryset = self.get_queryset().select_related(
            'itemid', 'sessionid', 'itemid__uom_basic'
        ).filter(
            wono=wono_src,
            compid=comp_id,
            finyearid__lte=fin_year_id,
            sysdate__range=(start_date, up_to_date)
        )

        # Apply filtering based on DropDownList1 (drpValue)
        if drp_value == 1: # BoughtOut (tblDG_Item_Master.CId is not null)
            queryset = queryset.filter(itemid__cid__isnull=False)
        elif drp_value == 2: # Manufacturing (tblDG_Item_Master.CId is null)
            queryset = queryset.filter(itemid__cid__isnull=True)
        # For drpValue == 0 ("All"), no additional filter is applied

        # --- COMPLEX BOM TREE LOGIC AND QUANTITY CALCULATION ---
        # The original fun.BOMTree_Search and fun.BOMTreeQty functions are crucial here.
        # Replicating them accurately without their source code or a deep understanding of the
        # BOM structure (PId, CId relationships, root assemblies) is challenging.
        # A robust solution often involves:
        # 1. Using a Django tree library like `django-mptt` (requires migration/setup for `managed=False`).
        # 2. Implementing a SQL Common Table Expression (CTE) for recursive BOM traversal.
        # 3. Building an in-memory graph and traversing it (less efficient for large BOMs).
        
        # For this example, we will provide a conceptual and simplified approach for BOMQty calculation.
        # It assumes `BOMQty` is the `UnitQty` of the item. A full implementation of `fun.BOMTreeQty`
        # would trace back the parent-child relationships and multiply quantities across the path.
        
        results = []
        for bom_entry in queryset:
            # Derived 'Item Code' (from ItemCode or PartNo)
            item_code_or_part_no = bom_entry.itemid.itemcode if bom_entry.itemid.cid is not None else bom_entry.itemid.partno
            
            # Derived 'Download' and 'DownloadSpec'
            download_text = 'View' if bom_entry.itemid.filename else ''
            download_spec_text = 'View' if bom_entry.itemid.attname else ''
            
            # Formatted 'Entry Date' (from SysDate)
            entry_date_formatted = bom_entry.sysdate.strftime('%d/%m/%Y') if bom_entry.sysdate else ''
            
            # Derived 'Entered by' (from OfficeStaff Title and EmployeeName)
            entered_by_name = f"{bom_entry.sessionid.title}.{bom_entry.sessionid.employeename}" if bom_entry.sessionid else ''

            # Simplified BOMQty calculation. In a real scenario, this would be a recursive calculation
            # based on the parent-child (`PId`, `CId`) relationships in `tblDG_BOM_Master`.
            # For this example, we'll make it equal to UnitQty.
            # A more accurate `BOMTreeQty` would multiply `qty` values up the BOM hierarchy.
            calculated_bom_qty = float(bom_entry.qty) # Placeholder: This needs proper recursive calculation
            
            results.append({
                # `id` field here will be the BOMMaster's primary key, used for row identification
                # and potential actions like selection/deletion.
                'id': bom_entry.id, 
                'ItemId': bom_entry.itemid.id,
                'WONo': bom_entry.wono,
                'PId': bom_entry.pid,
                'CId': bom_entry.cid,
                'Item_Code': item_code_or_part_no,
                'Description': bom_entry.itemid.manfdesc,
                'UOM': bom_entry.itemid.uom_basic.symbol if bom_entry.itemid.uom_basic else '',
                'UnitQty': f"{bom_entry.qty:.3f}", # Format to 3 decimal places
                'BOMQty': f"{calculated_bom_qty:.3f}", # Format to 3 decimal places
                'Download': download_text,
                'DownloadSpec': download_spec_text,
                'EntryDate': entry_date_formatted,
                'Entered_by': entered_by_name,
                'Revision': bom_entry.itemid.revision,
            })
        
        # Sort results as per original ASP.NET (Item Code ASC)
        results.sort(key=lambda x: x['Item_Code'] if x['Item_Code'] else '')
        
        return results

class BOMMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the PK for BOMMaster
    itemid = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    pid = models.IntegerField(db_column='PId', blank=True, null=True) # Parent ID in BOM
    cid = models.IntegerField(db_column='CId', blank=True, null=True) # Child ID in BOM
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    sessionid = models.ForeignKey(OfficeStaff, models.DO_NOTHING, db_column='SessionId', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = BOMManager() # Assign custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Entry'
        verbose_name_plural = 'BOM Entries'

    def __str__(self):
        return f"BOM {self.wono} - {self.itemid}"

    # No specific instance methods for this display-focused page.
    # All complex data retrieval is handled by the manager.

```

#### 4.2 Forms (`design/forms.py`)

The original ASP.NET page is primarily a display and filtering page; it doesn't have direct CRUD forms for BOM items. However, the prompt requires a generic `ModelForm` example for the `BOMMaster` model. This form would be used if you later implement create/update functionality.

```python
from django import forms
from .models import BOMMaster

class BOMMasterForm(forms.ModelForm):
    class Meta:
        model = BOMMaster
        # Fields that might be editable if BOM entries were managed via a form
        fields = ['itemid', 'wono', 'pid', 'cid', 'qty', 'sysdate', 'sessionid', 'compid', 'finyearid']
        widgets = {
            'itemid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'cid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sysdate': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'sessionid': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'compid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'finyearid': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    # Custom validation methods can be added here if needed

```

#### 4.3 Views (`design/views.py`)

We'll use a `TemplateView` for the main page and a `ListView` (returned as an HTMX partial) for the dynamic table. Dedicated views for file downloads are also included.

```python
from django.views.generic import TemplateView, ListView, View
from django.http import HttpResponse, Http404
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.conf import settings
import os
from datetime import datetime

from .models import BOMMaster, ItemMaster
# Import form if implementing generic CRUD, though not directly used for this page's primary function
from .forms import BOMMasterForm 
from django.contrib import messages # For success/error messages

class BOMPrintTreeView(TemplateView):
    template_name = 'design/bommaster/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Default values and session/query string parsing (as per original ASP.NET)
        context['wono_src'] = self.request.GET.get('WONo', '')
        context['start_date'] = self.request.GET.get('SD', datetime.now().strftime('%m/%d/%Y')) # Default to current date
        context['up_to_date'] = self.request.GET.get('TD', datetime.now().strftime('%m/%d/%Y')) # Default to current date
        context['message'] = self.request.GET.get('msg', '')
        context['drp_value'] = int(self.request.GET.get('DrpVal', 0)) # Default to 'All'
        
        # Simulating session variables (replace with actual session access in Django)
        # Assuming these are available in request.session or a custom context processor
        context['comp_id'] = self.request.session.get('compid', 1) # Default company ID
        context['fin_year_id'] = self.request.session.get('finyear', 1) # Default financial year ID

        # Dropdown options
        context['dropdown_options'] = [
            {'value': 0, 'label': 'All'},
            {'value': 1, 'label': 'BoughtOut'},
            {'value': 2, 'label': 'Manufacturing'},
        ]
        
        return context

class BOMTablePartialView(ListView):
    # This view will return the HTML snippet for the DataTables table, suitable for HTMX
    template_name = 'design/bommaster/_bom_table.html'
    context_object_name = 'bom_entries'
    model = BOMMaster # Not directly used for query, but required by ListView

    def get_queryset(self):
        # Extract parameters from GET request (can be from main page or HTMX trigger)
        wono_src = self.request.GET.get('WONo', '')
        drp_value = int(self.request.GET.get('DrpVal', 0))
        start_date_str = self.request.GET.get('SD', datetime.now().strftime('%m/%d/%Y'))
        up_to_date_str = self.request.GET.get('TD', datetime.now().strftime('%m/%d/%Y'))
        
        # Simulating session variables
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 1)

        # Call the custom manager method to get the processed BOM data
        bom_data = BOMMaster.objects.get_bom_data_for_display(
            wono_src, drp_value, start_date_str, up_to_date_str, comp_id, fin_year_id
        )
        return bom_data # This returns a list of dictionaries, not a QuerySet

    def render_to_response(self, context, **response_kwargs):
        # Ensure that non-HTMX requests are handled appropriately if this view is accessed directly
        if not self.request.headers.get('HX-Request'):
            return HttpResponse("This endpoint is intended for HTMX requests only.", status=400)
        return super().render_to_response(context, **response_kwargs)


class FileDownloadView(View):
    def get(self, request, pk, file_type):
        item = get_object_or_404(ItemMaster, id=pk)

        file_data = None
        file_name = None
        content_type = None

        if file_type == 'draw_img':
            file_data = item.filedata
            file_name = item.filename
            content_type = item.contenttype
        elif file_type == 'spec_sheet':
            file_data = item.attdata
            file_name = item.attname
            content_type = item.attcontenttype
        else:
            raise Http404("Invalid file type specified.")

        if not file_data:
            messages.error(request, "File not found for this item.")
            # Use HX-Redirect or HX-Refresh for HTMX context
            if request.headers.get('HX-Request'):
                return HttpResponse(status=204, headers={'HX-Refresh': 'true'})
            return HttpResponse("File not found.", status=404)

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response

# --- Generic CRUD Views (as per prompt instructions, though not primary for this specific page) ---

# Note: These are generic CRUD views for BOMMaster.
# The original ASP.NET page only displays a BOM tree, not modifies individual BOM entries.
# If actual BOM entry CRUD is needed, these views would be integrated differently.

class BOMMasterCreateView(View): # Using View to keep it thin, handle form manually
    def get(self, request, *args, **kwargs):
        form = BOMMasterForm()
        return self.render_form(request, form)

    def post(self, request, *args, **kwargs):
        form = BOMMasterForm(request.POST)
        if form.is_valid():
            form.save()
            messages.success(self.request, 'BOM Entry added successfully.')
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBOMMasterList'
                }
            )
        return self.render_form(request, form)

    def render_form(self, request, form):
        return HttpResponse(
            self.render_to_string(
                'design/bommaster/_bommaster_form.html', 
                {'form': form, 'title': 'Add New BOM Entry'}
            ),
            headers={'HX-Reswap': 'outerHTML'}
        )

    # Helper method to render template from string (for HTMX partials)
    def render_to_string(self, template_name, context):
        from django.template.loader import render_to_string
        return render_to_string(template_name, context, request=self.request)

class BOMMasterUpdateView(View): # Using View to keep it thin, handle form manually
    def get(self, request, pk, *args, **kwargs):
        obj = get_object_or_404(BOMMaster, pk=pk)
        form = BOMMasterForm(instance=obj)
        return self.render_form(request, form, obj)

    def post(self, request, pk, *args, **kwargs):
        obj = get_object_or_404(BOMMaster, pk=pk)
        form = BOMMasterForm(request.POST, instance=obj)
        if form.is_valid():
            form.save()
            messages.success(self.request, 'BOM Entry updated successfully.')
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBOMMasterList'
                }
            )
        return self.render_form(request, form, obj)
    
    def render_form(self, request, form, obj=None):
        return HttpResponse(
            self.render_to_string(
                'design/bommaster/_bommaster_form.html', 
                {'form': form, 'title': f'Edit BOM Entry {obj.id}' if obj else 'Edit BOM Entry'}
            ),
            headers={'HX-Reswap': 'outerHTML'}
        )

    def render_to_string(self, template_name, context):
        from django.template.loader import render_to_string
        return render_to_string(template_name, context, request=self.request)

class BOMMasterDeleteView(View): # Using View to keep it thin, handle form manually
    def get(self, request, pk, *args, **kwargs):
        obj = get_object_or_404(BOMMaster, pk=pk)
        return HttpResponse(
            self.render_to_string(
                'design/bommaster/_bommaster_confirm_delete.html', 
                {'object': obj}
            ),
            headers={'HX-Reswap': 'outerHTML'}
        )

    def post(self, request, pk, *args, **kwargs):
        obj = get_object_or_404(BOMMaster, pk=pk)
        obj.delete()
        messages.success(self.request, 'BOM Entry deleted successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshBOMMasterList'
            }
        )
    
    def render_to_string(self, template_name, context):
        from django.template.loader import render_to_string
        return render_to_string(template_name, context, request=self.request)

```

#### 4.4 Templates (`design/templates/design/bommaster/`)

We will create the main list view, a partial for the DataTables table, and generic partials for CRUD forms/confirmations.

**`design/templates/design/bommaster/list.html`**
This is the main page for displaying the BOM tree (now as a flat list with DataTables).

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Print BOM</h2>
        <div class="flex items-center space-x-4">
            <span class="font-semibold text-gray-700">Wo No: <span class="text-blue-600">{{ wono_src }}</span></span>
            
            <div x-data="{ selectedDrp: '{{ drp_value }}' }">
                <select id="id_drp_value" name="DrpVal" x-model="selectedDrp"
                        class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        hx-get="{% url 'design:bommaster_table' %}"
                        hx-target="#bomMasterTable-container"
                        hx-swap="innerHTML"
                        hx-indicator="#bomMasterTable-spinner"
                        hx-trigger="change">
                    {% for option in dropdown_options %}
                        <option value="{{ option.value }}" {% if option.value == drp_value %}selected{% endif %}>
                            {{ option.label }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            {# The original CheckBox1 for 'Expand Tree' is a UI feature specific to Telerik RadTreeList. #}
            {# As we are moving to DataTables, this functionality is usually replaced by DataTables' row details #}
            {# or a dedicated tree grid library, which is outside the scope of a standard DataTables migration. #}
            {# It's omitted here as the data itself is flattened and DataTables handles display. #}
            {# If complex tree display is critical, django-mptt and a custom JS tree component would be needed. #}
            
            <button id="cancelButton"
                class="redbox bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'design:bom_print_wo' %}'"> {# Assuming a URL for BOM_Design_PrintWo.aspx #}
                Cancel
            </button>
            <span id="lblasslymsg" class="text-red-500 font-bold">{{ message }}</span>
        </div>
    </div>
    
    <div id="bomMasterTable-container"
         hx-trigger="load, refreshBOMMasterList from:body"
         hx-get="{% url 'design:bommaster_table' %}"
         hx-target="#bomMasterTable-container"
         hx-swap="innerHTML"
         hx-indicator="#bomMasterTable-spinner"
         hx-vals='{"WONo": "{{ wono_src }}", "SD": "{{ start_date }}", "TD": "{{ up_to_date }}", "DrpVal": document.getElementById("id_drp_value").value}'>
        <!-- Loading Spinner -->
        <div id="bomMasterTable-spinner" class="htmx-indicator text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading BOM data...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD operations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full">
            <!-- Modal content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('bomFilter', (initialDrpVal) => ({
            selectedDrp: initialDrpVal,
            init() {
                // Initial data tables setup
                this.$nextTick(() => {
                    $('#bomMasterTable').DataTable({
                        "pageLength": 10,
                        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                        "columnDefs": [
                            {"orderable": false, "targets": -1} // Disable sorting on action column
                        ]
                    });
                });
            },
        }));
    });

    // Re-initialize DataTable after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'bomMasterTable-container') {
            $('#bomMasterTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "columnDefs": [
                    {"orderable": false, "targets": -1} // Disable sorting on action column
                ]
            });
        }
    });

    // Close modal on successful form submission (hx-trigger=refreshBOMMasterList)
    document.body.addEventListener('refreshBOMMasterList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('hidden'); // Ensure it's hidden after success
            modal.classList.remove('is-active'); // For Alpine.js if using x-show
            modal.classList.add('hidden');
        }
    });
</script>
{% endblock %}
```

**`design/templates/design/bommaster/_bom_table.html`**
This partial template contains the actual DataTables HTML structure, which will be loaded via HTMX into the `list.html`.

```html
<table id="bomMasterTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Revision</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Entry Date</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entered by</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for entry in bom_entries %}
        <tr class="hover:bg-gray-50 cursor-pointer"
            hx-trigger="click once"
            hx-get="{% url 'design:bom_print_report' wono=entry.WONo pid=entry.PId cid=entry.CId sd=request.GET.SD|default:'' td=request.GET.TD|default:'' drp_val=request.GET.DrpVal|default:0 %}"
            hx-push-url="true" {# Navigate fully to the report page #}
            _="on htmx:beforeSend add .opacity-50 to #bomMasterTable-container">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ entry.Item_Code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ entry.Description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.UOM }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ entry.UnitQty }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ entry.BOMQty }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.Revision }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.EntryDate }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ entry.Entered_by }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                {% if entry.Download %}
                <a href="{% url 'design:file_download' pk=entry.ItemId file_type='draw_img' %}" 
                   class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold"
                   onclick="event.stopPropagation();"> {# Prevent row click event #}
                    <img src="{% static 'images/export.ico' %}" alt="Download Drawing" class="w-4 h-4 mr-1">
                    Draw/Img
                </a>
                {% endif %}
                {% if entry.DownloadSpec %}
                <a href="{% url 'design:file_download' pk=entry.ItemId file_type='spec_sheet' %}" 
                   class="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold ml-2"
                   onclick="event.stopPropagation();"> {# Prevent row click event #}
                    <img src="{% static 'images/export.ico' %}" alt="Download Spec Sheet" class="w-4 h-4 mr-1">
                    Spec.sheet
                </a>
                {% endif %}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="10" class="py-4 px-4 text-center text-gray-500">No BOM entries found for the selected criteria.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!--
    DataTables initialization will happen in the parent template's JS block,
    triggered by htmx:afterSwap event.
-->

```

**`design/templates/design/bommaster/_bommaster_form.html`**
Generic partial for Add/Edit forms.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" is for success trigger #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Alpine.js/Hyperscript for modal close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`design/templates/design/bommaster/_bommaster_confirm_delete.html`**
Generic partial for delete confirmation.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-5">Are you sure you want to delete BOM Entry #{{ object.id }} ({{ object.wono }})?</p>
    
    <form hx-post="{% url 'design:bommaster_delete' object.pk %}" hx-swap="none"> {# hx-swap="none" is for success trigger #}
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Alpine.js/Hyperscript for modal close #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design/urls.py`)

```python
from django.urls import path
from .views import (
    BOMPrintTreeView, 
    BOMTablePartialView, 
    FileDownloadView,
    BOMMasterCreateView, 
    BOMMasterUpdateView, 
    BOMMasterDeleteView,
)

app_name = 'design' # Namespace for URLs

urlpatterns = [
    # Main BOM Print Tree view (replaces BOM_Design_Print_Tree.aspx)
    path('bom-print-tree/', BOMPrintTreeView.as_view(), name='bom_print_tree'),
    
    # HTMX endpoint for the DataTables partial
    path('bom-print-tree/table/', BOMTablePartialView.as_view(), name='bommaster_table'),
    
    # File Download view (replaces Controls/DownloadFile.aspx)
    path('download-file/<int:pk>/<str:file_type>/', FileDownloadView.as_view(), name='file_download'),

    # Redirect/report URLs (based on Page_PreRender and ImageButton1_Click)
    # These URLs would exist in your reports/printing module
    # Example: Assuming BOM_Design_Print_Cry.aspx maps to a Django view
    path('bom-print-report/', TemplateView.as_view(template_name='design/reports/bom_report.html'), name='bom_print_report'),
    path('bom-print-report/<str:wono>/<int:pid>/<int:cid>/', TemplateView.as_view(template_name='design/reports/bom_report.html'), name='bom_print_report'),
    path('bom-print-report/<str:wono>/<int:pid>/<int:cid>/<str:sd>/<str:td>/<int:drp_val>/', TemplateView.as_view(template_name='design/reports/bom_report.html'), name='bom_print_report'), # Full set of params
    
    # Example: Assuming BOM_Design_PrintWo.aspx maps to another Django view
    path('bom-print-wo/', TemplateView.as_view(template_name='design/bommaster/bom_print_wo.html'), name='bom_print_wo'),

    # --- Generic CRUD URLs for BOMMaster (as per prompt instructions) ---
    # These are for a hypothetical CRUD interface, not directly from original ASP.NET BOM Print Tree page
    path('bommaster/add/', BOMMasterCreateView.as_view(), name='bommaster_add'),
    path('bommaster/edit/<int:pk>/', BOMMasterUpdateView.as_view(), name='bommaster_edit'),
    path('bommaster/delete/<int:pk>/', BOMMasterDeleteView.as_view(), name='bommaster_delete'),
]

```

#### 4.6 Tests (`design/tests.py`)

Comprehensive tests cover model functionality, custom manager logic, and view interactions including HTMX.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from unittest.mock import patch, MagicMock

from .models import BOMMaster, ItemMaster, OfficeStaff, UnitMaster, parse_date_str

class ModelSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create dummy related objects for FKs
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')
        cls.staff_john = OfficeStaff.objects.create(empid=101, title='Mr', employeename='John Doe')
        cls.staff_jane = OfficeStaff.objects.create(empid=102, title='Ms', employeename='Jane Smith')

        # Create dummy ItemMaster objects
        cls.item_assembly = ItemMaster.objects.create(
            id=1, itemcode='ASM-001', partno='P001', manfdesc='Main Assembly', cid=None, uom_basic=cls.unit_ea,
            revision='A', filename='asm001.pdf', filedata=b'PDFdata', contenttype='application/pdf'
        )
        cls.item_component_bought = ItemMaster.objects.create(
            id=2, itemcode='COMP-001', partno='C001', manfdesc='Bought-out Component', cid=10, uom_basic=cls.unit_ea,
            revision='B', attname='spec001.docx', attdata=b'DOCdata', attcontenttype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        cls.item_component_mfg = ItemMaster.objects.create(
            id=3, itemcode='COMP-002', partno='C002', manfdesc='Manufactured Component', cid=None, uom_basic=cls.unit_kg,
            revision='C'
        )
        cls.item_sub_component = ItemMaster.objects.create(
            id=4, itemcode='SUB-001', partno='S001', manfdesc='Sub-component', cid=20, uom_basic=cls.unit_ea
        )

        # Create BOMMaster objects for a simple tree structure (WONo: 'WO-TEST')
        # BOM: ASM-001 (PId=0, CId=1)
        #   - COMP-001 (PId=1, CId=2) -> Qty: 2
        #   - COMP-002 (PId=1, CId=3) -> Qty: 0.5
        #     - SUB-001 (PId=3, CId=4) -> Qty: 3
        
        cls.bom_entry_assembly = BOMMaster.objects.create(
            id=100, itemid=cls.item_assembly, wono='WO-TEST', pid=0, cid=1, qty=1.0, 
            sysdate=datetime(2023, 1, 1, 10, 0, 0), sessionid=cls.staff_john, compid=1, finyearid=2023
        )
        cls.bom_entry_comp1 = BOMMaster.objects.create(
            id=101, itemid=cls.item_component_bought, wono='WO-TEST', pid=1, cid=2, qty=2.0, 
            sysdate=datetime(2023, 1, 5, 11, 0, 0), sessionid=cls.staff_jane, compid=1, finyearid=2023
        )
        cls.bom_entry_comp2 = BOMMaster.objects.create(
            id=102, itemid=cls.item_component_mfg, wono='WO-TEST', pid=1, cid=3, qty=0.5, 
            sysdate=datetime(2023, 1, 10, 12, 0, 0), sessionid=cls.staff_john, compid=1, finyearid=2023
        )
        cls.bom_entry_sub_comp = BOMMaster.objects.create(
            id=103, itemid=cls.item_sub_component, wono='WO-TEST', pid=3, cid=4, qty=3.0, 
            sysdate=datetime(2023, 1, 15, 13, 0, 0), sessionid=cls.staff_jane, compid=1, finyearid=2023
        )
        
        # Another BOM for different WO
        BOMMaster.objects.create(
            id=104, itemid=cls.item_assembly, wono='WO-OTHER', pid=0, cid=1, qty=1.0, 
            sysdate=datetime(2023, 2, 1, 10, 0, 0), sessionid=cls.staff_john, compid=1, finyearid=2023
        )


class BOMMasterModelTest(ModelSetupMixin, TestCase):
    def test_bommaster_creation(self):
        bom = BOMMaster.objects.get(id=101)
        self.assertEqual(bom.itemid.itemcode, 'COMP-001')
        self.assertEqual(str(bom.wono), 'WO-TEST')
        self.assertEqual(bom.qty, 2.0)
        self.assertEqual(str(bom.sessionid), 'Ms.Jane Smith')
        self.assertEqual(bom.itemid.uom_basic.symbol, 'EA')

    def test_bom_manager_get_bom_data_for_display(self):
        # Test case 1: All items for WO-TEST
        bom_data = BOMMaster.objects.get_bom_data_for_display(
            wono_src='WO-TEST', drp_value=0, start_date_str='01/01/2023', up_to_date_str='01/31/2023',
            comp_id=1, fin_year_id=2023
        )
        self.assertEqual(len(bom_data), 4) # All 4 items in WO-TEST
        self.assertEqual(bom_data[0]['Item_Code'], 'ASM-001') # Sorted by Item Code ASC

        # Test case 2: BoughtOut items for WO-TEST
        bom_data_bought = BOMMaster.objects.get_bom_data_for_display(
            wono_src='WO-TEST', drp_value=1, start_date_str='01/01/2023', up_to_date_str='01/31/2023',
            comp_id=1, fin_year_id=2023
        )
        # COMP-001 (cid=10) and SUB-001 (cid=20) are bought out
        self.assertEqual(len(bom_data_bought), 2)
        self.assertEqual(bom_data_bought[0]['Item_Code'], 'COMP-001')
        self.assertEqual(bom_data_bought[1]['Item_Code'], 'SUB-001')

        # Test case 3: Manufacturing items for WO-TEST
        bom_data_mfg = BOMMaster.objects.get_bom_data_for_display(
            wono_src='WO-TEST', drp_value=2, start_date_str='01/01/2023', up_to_date_str='01/31/2023',
            comp_id=1, fin_year_id=2023
        )
        # ASM-001 (cid=None), COMP-002 (cid=None) are manufacturing
        self.assertEqual(len(bom_data_mfg), 2)
        self.assertEqual(bom_data_mfg[0]['Item_Code'], 'ASM-001')
        self.assertEqual(bom_data_mfg[1]['Item_Code'], 'COMP-002')

        # Test case 4: Non-existent WONo
        bom_data_empty = BOMMaster.objects.get_bom_data_for_display(
            wono_src='NON-EXISTENT', drp_value=0, start_date_str='01/01/2023', up_to_date_str='01/31/2023',
            comp_id=1, fin_year_id=2023
        )
        self.assertEqual(len(bom_data_empty), 0)

        # Test derived fields
        bom_entry = next(item for item in bom_data if item['ItemId'] == self.item_assembly.id)
        self.assertEqual(bom_entry['Download'], 'View') # ASM-001 has filename
        self.assertEqual(bom_entry['DownloadSpec'], '') # ASM-001 has no attname
        self.assertEqual(bom_entry['EntryDate'], '01/01/2023')
        self.assertEqual(bom_entry['Entered_by'], 'Mr.John Doe')
        self.assertEqual(bom_entry['UnitQty'], '1.000')
        self.assertEqual(bom_entry['BOMQty'], '1.000') # Placeholder, as actual BOMQty is simplified

class BOMPrintTreeViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session variables
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023

    def test_bom_print_tree_view_get(self):
        response = self.client.get(reverse('design:bom_print_tree'), {'WONo': 'WO-TEST', 'SD': '01/01/2023', 'TD': '01/31/2023'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bommaster/list.html')
        self.assertContains(response, 'Print BOM')
        self.assertContains(response, 'WO-TEST')
        self.assertContains(response, 'id="bomMasterTable-container"') # Check for HTMX container

    def test_bom_table_partial_view_htmx_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design:bommaster_table'), {
            'WONo': 'WO-TEST', 'SD': '01/01/2023', 'TD': '01/31/2023', 'DrpVal': 0
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bommaster/_bom_table.html')
        self.assertContains(response, 'COMP-001') # Check for data from the table
        self.assertContains(response, 'tbody')
        
        # Verify no full page render
        self.assertNotContains(response, '<!DOCTYPE html>') 
        self.assertContains(response, 'id="bomMasterTable"')

    def test_bom_table_partial_view_non_htmx_get(self):
        # Should return 400 for non-HTMX request
        response = self.client.get(reverse('design:bommaster_table'))
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, "This endpoint is intended for HTMX requests only.")

    def test_file_download_view_draw_img(self):
        response = self.client.get(reverse('design:file_download', args=[self.item_assembly.id, 'draw_img']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="asm001.pdf"')
        self.assertEqual(response.content, b'PDFdata')

    def test_file_download_view_spec_sheet(self):
        response = self.client.get(reverse('design:file_download', args=[self.item_component_bought.id, 'spec_sheet']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.wordprocessingml.document')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="spec001.docx"')
        self.assertEqual(response.content, b'DOCdata')

    def test_file_download_view_not_found(self):
        response = self.client.get(reverse('design:file_download', args=[self.item_component_mfg.id, 'draw_img']))
        self.assertEqual(response.status_code, 404) # ItemMfg has no draw_img
        self.assertContains(response, "File not found.")

    def test_file_download_view_invalid_type(self):
        response = self.client.get(reverse('design:file_download', args=[self.item_assembly.id, 'invalid_type']))
        self.assertEqual(response.status_code, 404)
        self.assertContains(response, "Invalid file type specified.")

    # --- Generic CRUD View Tests (as per prompt instructions) ---
    def test_bommaster_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design:bommaster_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bommaster/_bommaster_form.html')
        self.assertContains(response, 'Add New BOM Entry')
        self.assertContains(response, '<form hx-post=')

    @patch('django.contrib.messages.success')
    def test_bommaster_create_view_post(self, mock_messages_success):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'itemid': self.item_assembly.id,
            'wono': 'NEW-WO',
            'pid': 5,
            'cid': 6,
            'qty': 1.5,
            'sysdate': datetime.now().strftime('%Y-%m-%dT%H:%M'), # DateTimeInput expects this format
            'sessionid': self.staff_john.empid,
            'compid': 1,
            'finyearid': 2023
        }
        response = self.client.post(reverse('design:bommaster_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertTrue(BOMMaster.objects.filter(wono='NEW-WO', pid=5, cid=6).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBOMMasterList')
        mock_messages_success.assert_called_once_with(self.client.request, 'BOM Entry added successfully.')

    def test_bommaster_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design:bommaster_edit', args=[self.bom_entry_comp1.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bommaster/_bommaster_form.html')
        self.assertContains(response, f'Edit BOM Entry {self.bom_entry_comp1.id}')
        self.assertContains(response, 'value="COMP-001"') # Check if form is pre-filled

    @patch('django.contrib.messages.success')
    def test_bommaster_update_view_post(self, mock_messages_success):
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_qty = 3.0
        data = {
            'itemid': self.item_component_bought.id,
            'wono': 'WO-TEST',
            'pid': 1,
            'cid': 2,
            'qty': updated_qty,
            'sysdate': self.bom_entry_comp1.sysdate.strftime('%Y-%m-%dT%H:%M'),
            'sessionid': self.bom_entry_comp1.sessionid.empid,
            'compid': self.bom_entry_comp1.compid,
            'finyearid': self.bom_entry_comp1.finyearid
        }
        response = self.client.post(reverse('design:bommaster_edit', args=[self.bom_entry_comp1.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.bom_entry_comp1.refresh_from_db()
        self.assertEqual(float(self.bom_entry_comp1.qty), updated_qty)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBOMMasterList')
        mock_messages_success.assert_called_once_with(self.client.request, 'BOM Entry updated successfully.')

    def test_bommaster_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design:bommaster_delete', args=[self.bom_entry_comp1.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/bommaster/_bommaster_confirm_delete.html')
        self.assertContains(response, f'delete BOM Entry #{self.bom_entry_comp1.id}')

    @patch('django.contrib.messages.success')
    def test_bommaster_delete_view_post(self, mock_messages_success):
        headers = {'HTTP_HX_REQUEST': 'true'}
        bom_id_to_delete = self.bom_entry_comp1.id
        response = self.client.post(reverse('design:bommaster_delete', args=[bom_id_to_delete]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(BOMMaster.objects.filter(id=bom_id_to_delete).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBOMMasterList')
        mock_messages_success.assert_called_once_with(self.client.request, 'BOM Entry deleted successfully.')

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic updates:**
    *   The dropdown (`id_drp_value`) triggers an `hx-get` to `{% url 'design:bommaster_table' %}` on `change`, reloading only the DataTables table.
    *   The `bomMasterTable-container` div uses `hx-trigger="load, refreshBOMMasterList from:body"` to automatically load the table on page load and refresh it whenever a `refreshBOMMasterList` custom event is triggered (e.g., after a CRUD operation via the modal forms).
    *   The CRUD buttons (Edit, Delete) in the table rows would use `hx-get` to load the respective form/confirmation partials into the `#modalContent` div, opening the modal.
    *   Form submissions within the modal use `hx-post` and `hx-swap="none"` (to prevent the form from replacing the modal content on success, as a `HX-Trigger` handles closing the modal and refreshing the list).
    *   Row clicks on the DataTables table (`<tr>` element) use `hx-get` with `hx-push-url="true"` to navigate to the detailed report page (`bom_print_report`), replicating the `Page_PreRender` redirect.
    *   File download links use standard `<a>` tags with `onclick="event.stopPropagation();"` to prevent the row's HTMX trigger from firing.

*   **Alpine.js for UI state management:**
    *   Used minimally for `x-data` on the dropdown to manage its selected state, though HTMX directly binding `x-model` to `hx-vals` is also an option.
    *   Hyperscript (`_`) is used for the modal open/close logic, demonstrating a modern client-side scripting approach without a large JavaScript framework.

*   **DataTables for list views:**
    *   The `_bom_table.html` partial is designed to be the content for a DataTables instance.
    *   JavaScript in `list.html` includes the DataTables initialization code, ensuring the table is rendered as a fully functional DataTables grid after HTMX swaps the content.
    *   `pageLength` and `lengthMenu` are configured, and the "Actions" column is set to be non-sortable.

*   **DRY Template Inheritance:**
    *   All templates extend `core/base.html`, ensuring consistent layout and including necessary CDN links (HTMX, Alpine.js, jQuery, DataTables, Tailwind CSS) from a single base file.

## Final Notes

*   **BOM Quantity Calculation (Complex Logic):** The `BOMManager.get_bom_data_for_display` method contains a simplified `BOMQty` calculation. In a real migration, the `fun.BOMTreeQty` and `fun.BOMTree_Search` functions would need to be thoroughly reverse-engineered. This often involves either implementing a recursive CTE directly in SQL queries (if using Django's `raw` queries or custom database functions) or performing in-memory graph traversal with libraries like `networkx` after fetching relevant data, or adapting to `django-mptt` if a hierarchical model structure is feasible with `managed=False`.
*   **Session Management:** The ASP.NET application uses `Session["compid"]` and `Session["finyear"]`. In Django, these would typically be handled by Django's session framework (`request.session.get('compid')`) or passed through a custom context processor if globally required.
*   **Security:** Ensure proper authentication and authorization (e.g., using Django's `LoginRequiredMixin` for views) are implemented for all views.
*   **Error Handling:** Enhance error messages and logging beyond what's shown in this example.
*   **File Storage:** Currently, files are assumed to be stored as `BinaryField` in the database. For larger files, consider migrating them to a file system (e.g., `MEDIA_ROOT`) and storing only their paths in the database for better performance and scalability.
*   **Tailwind CSS:** The provided templates include basic Tailwind CSS classes for styling, ensuring a modern and responsive UI.
*   **URL Parameter Consistency:** Ensure `WONo`, `SD`, `TD`, `DrpVal` are consistently passed as query parameters for filtering and navigation across views.
*   **Test Coverage:** The provided tests demonstrate key functionalities. In a real project, expand test cases to cover all edge cases, validations, and complex data scenarios, aiming for at least 80% coverage.