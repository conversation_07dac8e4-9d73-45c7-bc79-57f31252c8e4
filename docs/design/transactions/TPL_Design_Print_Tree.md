## ASP.NET to Django Conversion Script: TPL Print Tree View

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code primarily interacts with `tblDG_TPL_Master` for the hierarchical data, and joins with `tblDG_Item_Master`, `Unit_Master`, and `tblHR_OfficeStaff` to enrich the data. The `RadTreeList` uses `CId` as `DataKeyNames` and `PId` as `ParentDataKeyNames`, indicating a parent-child relationship within `tblDG_TPL_Master`.

**Identified Tables and Columns:**

*   **`tblDG_TPL_Master`** (Main entity for the tree structure)
    *   `ItemId` (int) - Foreign key to `tblDG_Item_Master`
    *   `WONo` (string) - Work Order Number
    *   `PId` (int) - Parent ID in the tree structure
    *   `CId` (int) - Child ID in the tree structure (likely primary key for this table's representation)
    *   `Qty` (decimal) - Quantity
    *   `SessionId` (string) - User Session ID (likely foreign key to `tblHR_OfficeStaff.EmpId`)
    *   `SysDate` (datetime) - System Entry Date

*   **`tblDG_Item_Master`** (Item details)
    *   `Id` (int) - Primary Key
    *   `ItemCode` (string)
    *   `PartNo` (string)
    *   `ManfDesc` (string) - Manufacturing Description (used as "Description")
    *   `UOMBasic` (int) - Unit of Measure Basic (Foreign key to `Unit_Master`)
    *   `FileName` (string) - File name for drawing/image
    *   `AttName` (string) - File name for attachment/spec sheet
    *   `FileData` (binary) - Actual file data for drawings
    *   `ContentType` (string) - Content type for drawing files
    *   `AttData` (binary) - Actual file data for attachments
    *   `AttContentType` (string) - Content type for attachment files

*   **`Unit_Master`** (Unit of Measure details)
    *   `Id` (int) - Primary Key
    *   `Symbol` (string) - Unit Symbol

*   **`tblHR_OfficeStaff`** (Employee details)
    *   `EmpId` (string) - Employee ID (used as `SessionId`)
    *   `Title` (string)
    *   `EmployeeName` (string)

**Derived Columns in `GetDataTable()`:**
The C# code constructs a `DataTable` with the following derived columns that need to be handled by Django models or helper functions:
*   `Item Code` (from `ItemCode` or `PartNo` in `tblDG_Item_Master`)
*   `Description` (from `ManfDesc` in `tblDG_Item_Master`)
*   `UOM` (from `Symbol` in `Unit_Master`)
*   `Unit Qty` (direct from `Qty` in `tblDG_TPL_Master`, formatted to N3)
*   `TPL Qty` (calculated recursively using `fun.TreeQty`)
*   `FileName` (transformed to "View" or "")
*   `AttName` (transformed to "View" or "")
*   `EntryDate` (from `SysDate` in `tblDG_TPL_Master`, formatted to DMY)
*   `Entered by` (from `Title + '.' + EmployeeName` in `tblHR_OfficeStaff`)

### Step 2: Identify Backend Functionality

**Primary Functionality:**
*   **Read (Display Tree View):** The core function is to fetch, process, and display hierarchical data from `tblDG_TPL_Master` along with related item, unit, and employee information. This involves complex joins and a recursive quantity calculation.
*   **Parameter Handling:** Accepts `WONo`, `SD` (StartDate), `TD` (UpToDate) from query strings. Uses `Session` variables for `CompId`, `FinYearId`, `username`.

**Actions/Triggers:**
*   **Expand/Collapse Tree:** `CheckBox1_CheckedChanged` toggles `RadTreeList1.ExpandAllItems()` or `CollapseAllItems()`. In Django, this would be managed client-side using DataTables or Alpine.js for visual hierarchy.
*   **Cancel (`btnCancel_Click`):** Redirects to `TPL_Design_PrintWo.aspx`.
*   **Download Image (`downloadImg` command):** Redirects to a generic `DownloadFile.aspx` handler to serve `FileData` from `tblDG_Item_Master`.
*   **Download Spec Sheet (`downloadSpec` command):** Redirects to a generic `DownloadFile.aspx` handler to serve `AttData` from `tblDG_Item_Master`.
*   **Generate Report (`ImageButton1_Click` / "Select" command):** Redirects to `TPL_Design_Print_Cry.aspx` with various parameters. This is a "print" or "report" action.

**Note:** The original ASP.NET page is primarily a read-only view. There are no direct Create, Update, or Delete operations on the `tblDG_TPL_Master` records themselves on this page. However, to adhere to the prompt's requirement for comprehensive CRUD views/templates, generic CRUD views for the `TPLMaster` model will be generated.

### Step 3: Infer UI Components

**Key ASP.NET Controls and Django Equivalents:**

*   **`RadTreeList1` (Telerik TreeList):** This will be replaced by a standard HTML `<table>` element rendered with DataTables. The hierarchical display will be achieved by calculating and displaying `PId` and `CId`, and potentially using DataTables' child rows feature if interactive expand/collapse beyond a simple flat list is desired. For the immediate migration, a flat list with all relevant calculated columns will be generated.
*   **`asp:Label ID="Label2"` (for `WONo`):** Simple Django template variable display.
*   **`asp:CheckBox ID="CheckBox1"` (Expand Tree):** An HTML checkbox, functionality managed by Alpine.js for client-side DataTables interaction.
*   **`asp:Button ID="btnCancel"`:** An HTML `<button>` or `<a>` tag, handled by standard Django URL redirection.
*   **`asp:Label ID="lblasslymsg"`:** Used for displaying messages. Will be handled by Django's `messages` framework and HTMX `hx-trigger` for UI updates.
*   **`telerik:RadButton` (SelectButton):** An HTML `<button>` or `<a>` with HTMX `hx-get` for redirection to the report view.
*   **`asp:LinkButton ID="btnlnkImg"` / `btnlnkSpec"`:** HTML `<a>` tags with `hx-get` to trigger file download views.
*   **`asp:ImageButton ID="ImageButton1"`:** An HTML `<button>` or `<a>` with `hx-get` to trigger report view.

**Styling:**
*   `Css/yui-datatable.css`, `Css/StyleSheet.css` will be replaced by Tailwind CSS.
*   Inline styles (`style1`) will be converted to Tailwind classes.
*   `loadingNotifier.js` will be replaced by HTMX's built-in indicators and Alpine.js.

### Step 4: Generate Django Code

**App Name:** `design_transactions`

#### 4.1 Models (`design_transactions/models.py`)

```python
from django.db import models
from django.db.models import F, OuterRef, Subquery
from django.utils import timezone
import datetime

# Helper for recursive calculation - will be a method on the TPLMaster model
# This mimics the fun.TreeQty logic.
# In a real-world scenario with deep trees, a more performant solution
# like a recursive CTE (Common Table Expression) via raw SQL,
# or a specialized library like django-treebeard/django-mptt might be considered.
def _calculate_tree_qty(won_no, parent_id, child_id):
    """
    Recursively calculates the product of quantities up the tree.
    This is a simplified in-memory version for demonstration.
    A more robust solution might cache results or use a database CTE.
    """
    current_node = TPLMaster.objects.filter(
        work_order_no=won_no,
        parent_id=parent_id,
        child_id=child_id
    ).first()

    if not current_node:
        return 1.0 # Base case if node not found

    quantities = []
    # Start with the current node's quantity
    quantities.append(float(current_node.quantity))

    # Traverse up the tree until the root is reached
    # PId is the parent, CId is the child
    temp_child_id = current_node.parent_id
    
    # We need to find the parent node for the current child_id
    # This logic assumes parent_id and child_id can be looked up from the current table
    # This loop tries to follow the parent chain.
    while temp_child_id is not None and temp_child_id != 0:
        parent_node = TPLMaster.objects.filter(
            work_order_no=won_no,
            child_id=temp_child_id # The child of the previous iteration is the parent here
        ).first()

        if parent_node:
            quantities.append(float(parent_node.quantity))
            temp_child_id = parent_node.parent_id # Move further up
        else:
            break # No more parents found

    # Multiply all collected quantities
    product = 1.0
    for q in quantities:
        product *= q
    return product

class TPLMasterManager(models.Manager):
    def get_tree_display_data(self, won_no, comp_id, fin_year_id, start_date_str, up_to_date_str):
        # Convert date strings to datetime objects
        # Assuming fun.FromDate converts "DD/MM/YYYY" to "MM/DD/YYYY" or similar for SQL
        # For Django, we convert to YYYY-MM-DD
        
        # In a real scenario, use a proper date parsing utility that handles potential format variations.
        # For now, let's assume 'DD/MM/YYYY' for fun.FromDate.
        def parse_date_dmy(date_str):
            if not date_str:
                return None
            try:
                # Assuming incoming format from C# fun.FromDate is 'MM/DD/YYYY' after conversion
                # Or if it's original 'DD/MM/YYYY' from Request.QueryString
                # Let's assume Request.QueryString gives 'DD/MM/YYYY'
                return datetime.datetime.strptime(date_str, '%d/%m/%Y').date()
            except ValueError:
                # Fallback for other common formats or errors
                try:
                    return datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
                except ValueError:
                    return None # Handle invalid date string gracefully
        
        start_date = parse_date_dmy(start_date_str)
        up_to_date = parse_date_dmy(up_to_date_str)
        
        # Start with the base query for TPLMaster
        queryset = self.get_queryset().filter(
            work_order_no=won_no,
            company_id=comp_id,
            financial_year_id__lte=fin_year_id, # C# had FinYearId <= finyear. This is unusual.
                                                 # Assuming it meant "current or past financial years up to current"
        )
        
        if start_date:
            queryset = queryset.filter(system_date__gte=start_date)
        if up_to_date:
            queryset = queryset.filter(system_date__lte=up_to_date)

        # Optimize with select_related and prefetch_related for efficient joins
        queryset = queryset.select_related('item', 'item__uom_basic', 'entered_by_staff')

        # This part processes each row to add derived columns and the recursive TPL Qty.
        # This will be inefficient for very large datasets and should be optimized
        # with database functions or pre-computation if possible.
        # For now, mimicking the C# loop structure in Python.
        
        results = []
        for tpl_master_obj in queryset.order_by('parent_id'): # Order by PId ASC as in C#
            item_code = ''
            description = ''
            uom_symbol = ''
            file_name_display = ''
            att_name_display = ''
            entered_by_name = ''

            if tpl_master_obj.item:
                if tpl_master_obj.item.category_id is not None: # C# had 'CId' check, assuming category_id
                    item_code = tpl_master_obj.item.item_code
                else:
                    item_code = tpl_master_obj.item.part_no
                
                description = tpl_master_obj.item.manf_description
                
                if tpl_master_obj.item.uom_basic:
                    uom_symbol = tpl_master_obj.item.uom_basic.symbol

                if tpl_master_obj.item.file_name:
                    file_name_display = "View"
                
                if tpl_master_obj.item.attachment_name:
                    att_name_display = "View"
            
            if tpl_master_obj.entered_by_staff:
                entered_by_name = f"{tpl_master_obj.entered_by_staff.title}.{tpl_master_obj.entered_by_staff.employee_name}"

            # Calculate TPL Quantity recursively
            tpl_qty_calculated = _calculate_tree_qty(
                won_no, 
                tpl_master_obj.parent_id, 
                tpl_master_obj.child_id
            )
            
            results.append({
                'item_id': tpl_master_obj.item_id,
                'work_order_no': tpl_master_obj.work_order_no,
                'parent_id': tpl_master_obj.parent_id,
                'child_id': tpl_master_obj.child_id,
                'item_code': item_code,
                'description': description,
                'uom': uom_symbol,
                'unit_quantity': f"{tpl_master_obj.quantity:.3f}", # "N3" format
                'tpl_quantity': f"{tpl_qty_calculated:.3f}", # "N3" format
                'file_name_display': file_name_display,
                'att_name_display': att_name_display,
                'entry_date': tpl_master_obj.system_date.strftime('%d/%m/%Y') if tpl_master_obj.system_date else '',
                'entered_by': entered_by_name,
                'pk': tpl_master_obj.child_id # Use child_id as PK for URL lookups
            })
        return results

class TPLMaster(models.Model):
    item = models.ForeignKey('ItemMaster', on_delete=models.DO_NOTHING, db_column='ItemId', related_name='tpl_masters')
    work_order_no = models.CharField(db_column='WONo', max_length=50) # Assuming max length
    parent_id = models.IntegerField(db_column='PId')
    child_id = models.IntegerField(db_column='CId', primary_key=True) # DataKeyNames="CId" implies this is the primary key for tree operations
    quantity = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # Assuming decimal precision
    session_id = models.CharField(db_column='SessionId', max_length=50) # Assuming string, maps to EmpId
    system_date = models.DateTimeField(db_column='SysDate')
    
    # These fields are inferred from the C# code's WHERE clauses
    company_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)

    # Adding a foreign key for 'Entered by' for better ORM usage
    entered_by_staff = models.ForeignKey('OfficeStaff', on_delete=models.DO_NOTHING, db_column='SessionId',
                                         related_name='tpl_entries', to_field='employee_id', null=True, blank=True)
                                         # Using to_field='employee_id' if SessionId truly maps to EmpId

    objects = TPLMasterManager()

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Design Tree Item'
        verbose_name_plural = 'TPL Design Tree Items'

    def __str__(self):
        return f"WO: {self.work_order_no} - Item: {self.item.item_code if self.item else 'N/A'}"
    
    # Model methods for derived properties or business logic

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=50, blank=True, null=True)
    manf_description = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey('UnitMaster', on_delete=models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    attachment_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)
    category_id = models.IntegerField(db_column='CId', null=True, blank=True) # Inferred from C# DsIt.Tables[0].Rows[0]["CId"]

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.part_no or f"Item {self.id}"

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class OfficeStaff(models.Model):
    employee_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True) # Using EmpId as PK based on session_id mapping
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title}.{self.employee_name}"

# Ensure _calculate_tree_qty can access TPLMaster if defined later
# It's already defined within this file so it's fine.
```

#### 4.2 Forms (`design_transactions/forms.py`)

**Note:** The original ASP.NET page is a display-only view. There are no direct forms for creating or updating `TPLMaster` records on this page. However, adhering to the prompt's requirement for comprehensive CRUD, a generic `TPLMasterForm` is provided. This form would be used if there were an interface to *edit* individual `TPLMaster` entries.

```python
from django import forms
from .models import TPLMaster

class TPLMasterForm(forms.ModelForm):
    class Meta:
        model = TPLMaster
        # Exclude fields derived or session-managed, focus on core editable data if this were a CRUD form.
        # Assuming ItemId, WONo, PId, CId, Qty are directly editable.
        fields = ['item', 'work_order_no', 'parent_id', 'child_id', 'quantity', 'system_date']
        widgets = {
            'item': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'work_order_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'parent_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'child_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'system_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        }
    
    # Custom validation could go here if needed.
```

#### 4.3 Views (`design_transactions/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse, Http404
from django.shortcuts import get_object_or_404, redirect
from django.conf import settings
import os # For file paths, potentially not needed if files are served from DB
import mimetypes # For content type detection

from .models import TPLMaster, ItemMaster
from .forms import TPLMasterForm

class TPLDesignTreeListView(ListView):
    """
    Main view to display the TPL Design Tree, including search/filter capabilities.
    """
    model = TPLMaster
    template_name = 'design_transactions/tpl_master/list.html'
    context_object_name = 'tpl_tree_items' # Renamed to reflect the processed data
    
    # This view will trigger an HTMX request to TPLDesignTreeTablePartialView
    # to load the actual table data dynamically.
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['work_order_no'] = self.request.GET.get('won', '')
        context['start_date'] = self.request.GET.get('sd', '')
        context['up_to_date'] = self.request.GET.get('td', '')
        context['expand_tree_checked'] = self.request.GET.get('expand', 'true') == 'true'
        context['page_title'] = "TPL Print Tree"
        # Messages are handled by base.html
        return context

class TPLDesignTreeTablePartialView(ListView):
    """
    Partial view to render the DataTables content for the TPL Design Tree.
    Loaded via HTMX.
    """
    model = TPLMaster
    template_name = 'design_transactions/tpl_master/_tpl_master_table.html'
    context_object_name = 'tpl_tree_items'

    def get_queryset(self):
        won_no = self.request.GET.get('won')
        start_date = self.request.GET.get('sd')
        up_to_date = self.request.GET.get('td')
        
        # Mock session variables for demonstration.
        # In a real app, these would come from request.user profile or session data.
        comp_id = self.request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = self.request.session.get('finyear', 2024) # Default to 2024
        
        if not won_no:
            return [] # Return empty if no WO number is provided
        
        # Use the custom manager method to get the processed data
        return TPLMaster.objects.get_tree_display_data(won_no, comp_id, fin_year_id, start_date, up_to_date)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['expand_tree_checked'] = self.request.GET.get('expand', 'true') == 'true'
        return context


class TPLMasterCreateView(CreateView):
    """
    Generic Create View for TPLMaster, as per prompt's requirement for CRUD.
    Not directly used by the original ASPX page.
    """
    model = TPLMaster
    form_class = TPLMasterForm
    template_name = 'design_transactions/tpl_master/form.html'
    success_url = reverse_lazy('tpl_master_list')

    def form_valid(self, form):
        # Mock session variables for demonstration, these would be from actual session/user
        form.instance.session_id = self.request.session.get('username', 'system') # Placeholder
        form.instance.company_id = self.request.session.get('compid', 1) # Placeholder
        form.instance.financial_year_id = self.request.session.get('finyear', 2024) # Placeholder
        form.instance.system_date = timezone.now()
        
        response = super().form_valid(form)
        messages.success(self.request, 'TPL Master item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTPLMasterList'
                }
            )
        return response

class TPLMasterUpdateView(UpdateView):
    """
    Generic Update View for TPLMaster, as per prompt's requirement for CRUD.
    Not directly used by the original ASPX page.
    """
    model = TPLMaster
    form_class = TPLMasterForm
    template_name = 'design_transactions/tpl_master/form.html'
    success_url = reverse_lazy('tpl_master_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'TPL Master item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTPLMasterList'
                }
            )
        return response

class TPLMasterDeleteView(DeleteView):
    """
    Generic Delete View for TPLMaster, as per prompt's requirement for CRUD.
    Not directly used by the original ASPX page.
    """
    model = TPLMaster
    template_name = 'design_transactions/tpl_master/confirm_delete.html'
    success_url = reverse_lazy('tpl_master_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'TPL Master item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTPLMasterList'
                }
            )
        return response

class DownloadFileView(View):
    """
    Handles file downloads for images and specification sheets, mimicking DownloadFile.aspx.
    """
    def get(self, request, item_id):
        item = get_object_or_404(ItemMaster, id=item_id)
        
        # Determine which file to download based on query parameter or path
        file_type = request.GET.get('type') # 'img' or 'spec'

        if file_type == 'img' and item.file_data and item.file_name:
            content_type = item.content_type or mimetypes.guess_type(item.file_name)[0] or 'application/octet-stream'
            response = FileResponse(item.file_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{item.file_name}"'
            return response
        elif file_type == 'spec' and item.att_data and item.attachment_name:
            content_type = item.att_content_type or mimetypes.guess_type(item.attachment_name)[0] or 'application/octet-stream'
            response = FileResponse(item.att_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{item.attachment_name}"'
            return response
        else:
            messages.error(request, 'File not found or specified type invalid.')
            # Use HTMX to trigger a message update if this is an HTMX request
            if request.headers.get('HX-Request'):
                return HttpResponse(status=200, headers={'HX-Trigger': 'showMessage'})
            return redirect(reverse_lazy('tpl_master_list')) # Or appropriate fallback

class PrintReportView(View):
    """
    Handles redirection to the report generation page, mimicking TPL_Design_Print_Cry.aspx.
    """
    def get(self, request):
        won_no = request.GET.get('wono')
        p_id = request.GET.get('PId', '') # Allow empty as in ASPX
        c_id = request.GET.get('CId', '') # Allow empty as in ASPX
        start_date = request.GET.get('SD', '')
        up_to_date = request.GET.get('TD', '')
        mod_id = request.GET.get('ModId', '3')
        sub_mod_id = request.GET.get('SubModId', '23')

        # Construct the URL for the external or internal Django report view
        # This assumes TPL_Design_Print_Cry.aspx is also migrated to a Django view
        # For now, it's a placeholder for where the report URL would go.
        # Example: reverse_lazy('design_transactions:tpl_report_view')
        
        # Building a mock report URL. In a real scenario, this would be a Django URL pattern
        # that handles these query parameters.
        report_url = f"/design-transactions/tpl-design-report/?wono={won_no}&PId={p_id}&CId={c_id}&SD={start_date}&TD={up_to_date}&ModId={mod_id}&SubModId={sub_mod_id}"
        
        return redirect(report_url)

class TPLDesignPrintWoView(View):
    """
    Handles redirection to the parent TPL_Design_PrintWo.aspx page.
    """
    def get(self, request):
        mod_id = request.GET.get('ModId', '3')
        sub_mod_id = request.GET.get('SubModId', '23')
        # Assuming this redirects to another Django view or external system.
        # For now, a placeholder URL.
        redirect_url = reverse_lazy('some_other_design_list') # Placeholder for parent page
        if mod_id and sub_mod_id:
            redirect_url = f"{redirect_url}?ModId={mod_id}&SubModId={sub_mod_id}"
        return redirect(redirect_url)
```

#### 4.4 Templates (`design_transactions/templates/design_transactions/tpl_master/`)

`list.html`
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">TPL Print Tree View for Work Order: <span class="text-blue-600">{{ work_order_no }}</span></h2>
        
        <div class="flex items-center space-x-4">
            <label class="flex items-center cursor-pointer text-gray-700 font-medium">
                <input type="checkbox" id="expandTreeCheckbox" class="toggle toggle-primary"
                    {% if expand_tree_checked %}checked{% endif %}
                    hx-get="{% url 'design_transactions:tpl_master_table' %}"
                    hx-target="#tplMasterTable-container"
                    hx-trigger="change"
                    hx-swap="innerHTML"
                    hx-vals='{"won": "{{ work_order_no }}", "sd": "{{ start_date }}", "td": "{{ up_to_date }}", "expand": document.getElementById("expandTreeCheckbox").checked}'
                >
                <span class="ml-2">Expand Tree</span>
            </label>
            <button 
                class="btn bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'design_transactions:tpl_design_print_wo' %}'">
                Cancel
            </button>
            {% if messages %}
            <div id="messages" hx-swap-oob="beforeend:.messages-container">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} shadow-lg">
                    <span>{{ message }}</span>
                </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
    
    <div id="tplMasterTable-container"
         hx-trigger="load, refreshTPLMasterList from:body"
         hx-get="{% url 'design_transactions:tpl_master_table' %}?won={{ work_order_no }}&sd={{ start_date }}&td={{ up_to_date }}&expand={{ expand_tree_checked }}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading TPL Design Tree data...</p>
        </div>
    </div>
    
    <!-- Modals for generic CRUD operations (as per prompt's requirement, though not directly used by this page) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me and remove .is-active from #modalContent">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-auto hidden"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('tplTreeData', () => ({
            // If any specific Alpine.js logic for filtering or tree interaction is needed
            // For now, DataTables handles most of the client-side interaction.
        }));
    });

    // Handle initial DataTables load after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'tplMasterTable-container') {
            // Check if DataTables already initialized (e.g., on subsequent HTMX updates)
            if ($.fn.DataTable.isDataTable('#tplMasterTable')) {
                $('#tplMasterTable').DataTable().destroy();
            }
            const expandChecked = document.getElementById('expandTreeCheckbox').checked;
            $('#tplMasterTable').DataTable({
                "pageLength": 15,
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "paging": true,
                "searching": true,
                "info": true,
                "ordering": true,
                "columnDefs": [
                    { "visible": false, "targets": [0, 2, 3] } // Hide ItemId, PId, CId
                ]
            });

            // Logic to visually expand/collapse (if DataTables tree view plugin is used)
            // For standard DataTables, you might implement custom row grouping/expansion
            // or use a library like DataTables TreeGrid or a custom JS solution.
            // As a fallback, ensure the `expandTreeCheckbox` correctly triggers re-load to reflect flat/grouped data.
            // For now, we assume simple DataTables, and the "Expand Tree" checkbox just re-loads data,
            // which might just change how the data is rendered (e.g., if you pre-indent).
            // A true tree structure would require a DataTables plugin or a custom component.
        }
    });
</script>
{% endblock %}
```

`_tpl_master_table.html`
```html
<table id="tplMasterTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item ID</th> {# Hidden by JS #}
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 120px;">Item Code</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 310px;">Description</th>
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Parent ID</th> {# Hidden by JS #}
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Child ID</th> {# Hidden by JS #}
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 50px;">UOM</th>
            <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 80px;">Unit Qty</th>
            <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 80px;">TPL Qty</th>
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Drw/Image</th>
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider" style="width: 100px;">Entry Date</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entered By</th>
            <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in tpl_tree_items %}
        <tr>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.item_id }}</td> {# Hidden by JS #}
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.item_code }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.description }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.parent_id }}</td> {# Hidden by JS #}
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.child_id }}</td> {# Hidden by JS #}
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.uom }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.unit_quantity }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.tpl_quantity }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                {% if obj.file_name_display == "View" %}
                <button
                    class="btn btn-sm btn-info"
                    hx-get="{% url 'design_transactions:download_file' item_id=obj.item_id %}?type=img"
                    hx-target="body"
                    hx-trigger="click"
                    hx-swap="none"
                    title="Download Drawing/Image">
                    <img src="{{ '/static/images/export.ico' }}" alt="Download" class="w-4 h-4 inline-block">
                </button>
                {% else %}
                -
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                {% if obj.att_name_display == "View" %}
                <button
                    class="btn btn-sm btn-info"
                    hx-get="{% url 'design_transactions:download_file' item_id=obj.item_id %}?type=spec"
                    hx-target="body"
                    hx-trigger="click"
                    hx-swap="none"
                    title="Download Spec. Sheet">
                    <img src="{{ '/static/images/export.ico' }}" alt="Download" class="w-4 h-4 inline-block">
                </button>
                {% else %}
                -
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.entry_date }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.entered_by }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-center">
                <button 
                    class="btn btn-sm bg-yellow-500 hover:bg-yellow-700 text-white"
                    hx-get="{% url 'design_transactions:print_report' %}?wono={{ obj.work_order_no }}&PId={{ obj.parent_id }}&CId={{ obj.child_id }}&SD={{ request.GET.sd }}&TD={{ request.GET.td }}&ModId=3&SubModId=23"
                    hx-target="body"
                    hx-trigger="click"
                    hx-swap="none"
                    title="View Report">
                    Report
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="12" class="py-4 px-4 text-center text-gray-500">No data available for the selected Work Order and date range.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // DataTables initialization is handled in list.html via htmx:afterSwap
    // This script block is within the partial, so it runs when partial is swapped in.
    // Ensure the DataTables re-initialization logic is robust in list.html to avoid issues.
</script>
```

`form.html` (Generic CRUD form, for `TPLMasterCreateView`/`UpdateView`, not directly used by the original ASPX)
```html
<div class="p-6" id="modalContent">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} TPL Master Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="btn bg-gray-300 hover:bg-gray-400 text-gray-800"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="btn bg-blue-500 hover:bg-blue-700 text-white">
                <span class="htmx-indicator loading loading-spinner" id="form-spinner"></span>
                Save
            </button>
        </div>
    </form>
</div>
```

`confirm_delete.html` (Generic Delete confirmation, for `TPLMasterDeleteView`, not directly used by the original ASPX)
```html
<div class="p-6" id="modalContent">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this TPL Master item for Work Order: <strong>{{ object.work_order_no }} (Child ID: {{ object.child_id }})</strong>?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="btn bg-gray-300 hover:bg-gray-400 text-gray-800"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="btn bg-red-500 hover:bg-red-700 text-white">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`design_transactions/urls.py`)

```python
from django.urls import path
from .views import (
    TPLDesignTreeListView, TPLDesignTreeTablePartialView,
    TPLMasterCreateView, TPLMasterUpdateView, TPLMasterDeleteView,
    DownloadFileView, PrintReportView, TPLDesignPrintWoView
)

app_name = 'design_transactions'

urlpatterns = [
    # Main list view for the TPL Design Tree
    path('tpl-design-tree/', TPLDesignTreeListView.as_view(), name='tpl_master_list'),
    # HTMX endpoint for the table content
    path('tpl-design-tree/table/', TPLDesignTreeTablePartialView.as_view(), name='tpl_master_table'),

    # Generic CRUD operations for TPLMaster (as per prompt's requirement, though not directly used by this page)
    path('tpl-master/add/', TPLMasterCreateView.as_view(), name='tpl_master_add'),
    path('tpl-master/edit/<int:pk>/', TPLMasterUpdateView.as_view(), name='tpl_master_edit'),
    path('tpl-master/delete/<int:pk>/', TPLMasterDeleteView.as_view(), name='tpl_master_delete'),

    # File download handlers
    path('download-file/<int:item_id>/', DownloadFileView.as_view(), name='download_file'),
    
    # Report generation handler
    path('print-report/', PrintReportView.as_view(), name='print_report'),

    # Redirection to the parent work order list
    path('tpl-design-print-wo/', TPLDesignPrintWoView.as_view(), name='tpl_design_print_wo'),
]
```

#### 4.6 Tests (`design_transactions/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime
from unittest.mock import patch, MagicMock
from .models import TPLMaster, ItemMaster, UnitMaster, OfficeStaff, _calculate_tree_qty

class TPLMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.unit_kg = UnitMaster.objects.create(id=2, symbol='KG')

        cls.item_assy = ItemMaster.objects.create(
            id=101, item_code='ASSY001', manf_description='Main Assembly',
            uom_basic=cls.unit_ea, file_name='assy_drawing.pdf', attachment_name='assy_spec.doc',
            file_data=b'dummy_file_data', att_data=b'dummy_att_data', category_id=1
        )
        cls.item_part_a = ItemMaster.objects.create(
            id=102, part_no='PARTA001', manf_description='Component A',
            uom_basic=cls.unit_kg, category_id=None # Simulating no CId
        )
        cls.item_part_b = ItemMaster.objects.create(
            id=103, part_no='PARTB001', manf_description='Component B',
            uom_basic=cls.unit_ea, category_id=None
        )
        cls.item_subassy = ItemMaster.objects.create(
            id=104, item_code='SUBASSY001', manf_description='Sub Assembly X',
            uom_basic=cls.unit_ea, category_id=1
        )

        cls.staff_john = OfficeStaff.objects.create(employee_id='john.doe', title='Mr', employee_name='John Doe')

        # Create TPLMaster instances representing a tree
        # WO1: Assy1 (C1, P0, Q1)
        #   - PartA (C2, P1, Q2)
        #   - SubAssy (C3, P1, Q3)
        #     - PartB (C4, P3, Q4)

        cls.tpl_assy = TPLMaster.objects.create(
            item=cls.item_assy, work_order_no='WO123', parent_id=0, child_id=1, quantity=1.000,
            session_id='john.doe', system_date=timezone.now(), company_id=1, financial_year_id=2024,
            entered_by_staff=cls.staff_john
        )
        cls.tpl_part_a = TPLMaster.objects.create(
            item=cls.item_part_a, work_order_no='WO123', parent_id=1, child_id=2, quantity=2.000,
            session_id='john.doe', system_date=timezone.now(), company_id=1, financial_year_id=2024,
            entered_by_staff=cls.staff_john
        )
        cls.tpl_subassy = TPLMaster.objects.create(
            item=cls.item_subassy, work_order_no='WO123', parent_id=1, child_id=3, quantity=3.000,
            session_id='john.doe', system_date=timezone.now(), company_id=1, financial_year_id=2024,
            entered_by_staff=cls.staff_john
        )
        cls.tpl_part_b = TPLMaster.objects.create(
            item=cls.item_part_b, work_order_no='WO123', parent_id=3, child_id=4, quantity=4.000,
            session_id='john.doe', system_date=timezone.now(), company_id=1, financial_year_id=2024,
            entered_by_staff=cls.staff_john
        )

    def test_tpl_master_creation(self):
        tpl_item = TPLMaster.objects.get(child_id=1)
        self.assertEqual(tpl_item.work_order_no, 'WO123')
        self.assertEqual(tpl_item.item.item_code, 'ASSY001')
        self.assertEqual(tpl_item.quantity, 1.000)

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=101)
        self.assertEqual(item.manf_description, 'Main Assembly')
        self.assertEqual(item.uom_basic.symbol, 'EA')

    def test_office_staff_creation(self):
        staff = OfficeStaff.objects.get(employee_id='john.doe')
        self.assertEqual(staff.employee_name, 'John Doe')

    def test_calculate_tree_qty(self):
        # Tree: Assy1 (C1, Q1) -> PartA (C2, Q2)
        # Expected TPL Qty for PartA (child_id=2): Q2 * Q1 = 2 * 1 = 2.0
        calculated_qty_part_a = _calculate_tree_qty('WO123', 1, 2)
        self.assertAlmostEqual(calculated_qty_part_a, 2.0)

        # Tree: Assy1 (C1, Q1) -> SubAssy (C3, Q3) -> PartB (C4, Q4)
        # Expected TPL Qty for PartB (child_id=4): Q4 * Q3 * Q1 = 4 * 3 * 1 = 12.0
        calculated_qty_part_b = _calculate_tree_qty('WO123', 3, 4)
        self.assertAlmostEqual(calculated_qty_part_b, 12.0)

        # Expected TPL Qty for root Assy1 (child_id=1): Q1 = 1.0 (no parent quantity)
        calculated_qty_assy = _calculate_tree_qty('WO123', 0, 1)
        self.assertAlmostEqual(calculated_qty_assy, 1.0)
    
    @patch('design_transactions.models._calculate_tree_qty', side_effect=[2.0, 12.0, 3.0, 4.0]) # Mock recursive calls
    def test_get_tree_display_data(self, mock_calculate_tree_qty):
        start_date_str = (timezone.now() - timezone.timedelta(days=30)).strftime('%d/%m/%Y')
        up_to_date_str = (timezone.now() + timezone.timedelta(days=30)).strftime('%d/%m/%Y')

        data = TPLMaster.objects.get_tree_display_data('WO123', 1, 2024, start_date_str, up_to_date_str)
        self.assertEqual(len(data), 4) # Should return all 4 items
        
        # Verify derived fields for Assy1
        assy_data = next(item for item in data if item['child_id'] == 1)
        self.assertEqual(assy_data['item_code'], 'ASSY001')
        self.assertEqual(assy_data['description'], 'Main Assembly')
        self.assertEqual(assy_data['uom'], 'EA')
        self.assertEqual(assy_data['unit_quantity'], '1.000')
        self.assertEqual(assy_data['tpl_quantity'], '2.000') # Mocked value
        self.assertEqual(assy_data['file_name_display'], 'View')
        self.assertEqual(assy_data['att_name_display'], 'View')
        self.assertIn('Mr.John Doe', assy_data['entered_by'])
        self.assertIn(timezone.now().strftime('%d/%m/%Y'), assy_data['entry_date'])

        # Verify derived fields for PartA
        part_a_data = next(item for item in data if item['child_id'] == 2)
        self.assertEqual(part_a_data['item_code'], 'PARTA001') # PartNo used for item_code
        self.assertEqual(part_a_data['description'], 'Component A')
        self.assertEqual(part_a_data['uom'], 'KG')
        self.assertEqual(part_a_data['unit_quantity'], '2.000')
        self.assertEqual(part_a_data['tpl_quantity'], '12.000') # Mocked value
        self.assertEqual(part_a_data['file_name_display'], '') # No file_name
        self.assertEqual(part_a_data['att_name_display'], '') # No att_name

class TPLDesignTreeViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_test = ItemMaster.objects.create(
            id=201, item_code='TEST001', manf_description='Test Item',
            uom_basic=cls.unit_ea
        )
        cls.staff_test = OfficeStaff.objects.create(employee_id='test.user', title='Ms', employee_name='Test User')
        
        cls.tpl_entry = TPLMaster.objects.create(
            item=cls.item_test, work_order_no='TESTWO', parent_id=0, child_id=1, quantity=1.000,
            session_id='test.user', system_date=timezone.now(), company_id=1, financial_year_id=2024,
            entered_by_staff=cls.staff_test
        )
        
        # Set up a session for mock data if needed for views
        cls.session = cls.client.session
        cls.session['compid'] = 1
        cls.session['finyear'] = 2024
        cls.session['username'] = 'test.user'
        cls.session.save()

    def test_tpl_design_tree_list_view(self):
        response = self.client.get(reverse('design_transactions:tpl_master_list'), {'won': 'TESTWO'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tpl_master/list.html')
        self.assertContains(response, 'TPL Print Tree View for Work Order: TESTWO')

    def test_tpl_design_tree_table_partial_view_get(self):
        # Test loading the table via HTMX
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('design_transactions:tpl_master_table'),
            {'won': 'TESTWO', 'sd': '01/01/2024', 'td': '31/12/2024', 'expand': 'true'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tpl_master/_tpl_master_table.html')
        self.assertContains(response, 'TESTWO') # Check if WO data is in the table
        self.assertContains(response, 'TEST001') # Check if Item Code is present
        self.assertContains(response, 'Test Item') # Check if Description is present

    def test_download_file_view_image(self):
        headers = {'HTTP_HX_REQUEST': 'true'} # Mock HTMX request for message handling
        response = self.client.get(reverse('design_transactions:download_file', args=[self.item_test.id]), {'type': 'img'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn('Content-Disposition', response)
        self.assertIn(b'dummy_file_data', response.content) # Check if file content is returned
        self.assertEqual(response['Content-Type'], 'application/octet-stream') # Default if not specified

    def test_download_file_view_spec(self):
        self.item_test.file_name = None # Clear dummy file name for spec test clarity
        self.item_test.attachment_name = "test_spec.pdf"
        self.item_test.att_data = b'dummy_att_data_spec'
        self.item_test.att_content_type = 'application/pdf'
        self.item_test.save()

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design_transactions:download_file', args=[self.item_test.id]), {'type': 'spec'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertIn('Content-Disposition', response)
        self.assertIn(b'dummy_att_data_spec', response.content)
        self.assertEqual(response['Content-Type'], 'application/pdf')

    def test_download_file_view_not_found(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design_transactions:download_file', args=[9999]), {'type': 'img'}, **headers)
        self.assertEqual(response.status_code, 404) # Item not found -> 404

    def test_print_report_view(self):
        response = self.client.get(reverse('design_transactions:print_report'), {'wono': 'TESTWO'})
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertIn('/design-transactions/tpl-design-report/?wono=TESTWO', response.url)

    def test_tpl_design_print_wo_view(self):
        response = self.client.get(reverse('design_transactions:tpl_design_print_wo'))
        self.assertEqual(response.status_code, 302)
        # Verify redirection to the placeholder URL
        self.assertEqual(response.url, reverse('some_other_design_list')) 
        
    # --- Generic CRUD Views Tests (as per prompt's requirement) ---
    def test_tpl_master_add_view_get(self):
        response = self.client.get(reverse('design_transactions:tpl_master_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tpl_master/form.html')
        self.assertContains(response, 'Add TPL Master Item')

    def test_tpl_master_add_view_post(self):
        new_item = ItemMaster.objects.create(id=301, item_code='NEWITEM', manf_description='New Desc', uom_basic=self.unit_ea)
        data = {
            'item': new_item.id,
            'work_order_no': 'NEWWO',
            'parent_id': 0,
            'child_id': 5, # Ensure this PK is unique
            'quantity': 5.000,
            'system_date': timezone.now().strftime('%Y-%m-%d'),
        }
        response = self.client.post(reverse('design_transactions:tpl_master_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success -> 204 No Content
        self.assertTrue(TPLMaster.objects.filter(work_order_no='NEWWO', child_id=5).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTPLMasterList', response.headers['HX-Trigger'])

    def test_tpl_master_update_view_get(self):
        response = self.client.get(reverse('design_transactions:tpl_master_edit', args=[self.tpl_entry.child_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tpl_master/form.html')
        self.assertContains(response, 'Edit TPL Master Item')

    def test_tpl_master_update_view_post(self):
        data = {
            'item': self.tpl_entry.item.id,
            'work_order_no': 'UPDATEDWO',
            'parent_id': self.tpl_entry.parent_id,
            'child_id': self.tpl_entry.child_id,
            'quantity': 99.000,
            'system_date': self.tpl_entry.system_date.strftime('%Y-%m-%d'),
        }
        response = self.client.post(reverse('design_transactions:tpl_master_edit', args=[self.tpl_entry.child_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.tpl_entry.refresh_from_db()
        self.assertEqual(self.tpl_entry.work_order_no, 'UPDATEDWO')
        self.assertEqual(self.tpl_entry.quantity, 99.000)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTPLMasterList', response.headers['HX-Trigger'])

    def test_tpl_master_delete_view_get(self):
        response = self.client.get(reverse('design_transactions:tpl_master_delete', args=[self.tpl_entry.child_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_transactions/tpl_master/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')

    def test_tpl_master_delete_view_post(self):
        delete_id = self.tpl_entry.child_id
        response = self.client.post(reverse('design_transactions:tpl_master_delete', args=[delete_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(TPLMaster.objects.filter(child_id=delete_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTPLMasterList', response.headers['HX-Trigger'])
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic content:**
    *   The main list page (`list.html`) uses `hx-get` to fetch the table content from `tpl_master_table/` endpoint.
    *   The table content (`_tpl_master_table.html`) is swapped into the `tplMasterTable-container` div.
    *   `hx-trigger="load, refreshTPLMasterList from:body"` ensures the table loads on page load and refreshes whenever a `refreshTPLMasterList` custom event is triggered (e.g., after a CRUD operation).
    *   Download buttons (`btn-info` with image) use `hx-get` to trigger the `download_file` view. The `hx-target="body"` and `hx-swap="none"` ensure the download happens without altering the current page.
    *   Report button (`btn-sm`) uses `hx-get` to trigger `print_report` view for redirection.
    *   The "Expand Tree" checkbox directly triggers an HTMX request to reload the table data, passing its `checked` state via `hx-vals`. This allows the backend to decide how to render the tree (e.g., flattened vs. indented).
    *   Generic CRUD forms are loaded into a modal via `hx-get` and submit via `hx-post` with `hx-swap="none"` and `hx-trigger="refreshTPLMasterList"` on success.

*   **Alpine.js for UI state management:**
    *   Alpine.js is initialized on `list.html` for potential future dynamic UI elements. For the current migration, DataTables and HTMX handle most of the dynamic behavior.
    *   The modal (`#modal`) uses Alpine.js-like `_` (hyperscript) attributes for showing/hiding on button clicks and outside clicks. This keeps JavaScript minimal and declaraitve.

*   **DataTables for list views:**
    *   The `_tpl_master_table.html` partial contains the `<table>` element.
    *   `list.html` contains JavaScript that listens for `htmx:afterSwap` on the table container. When the table HTML is loaded, it initializes DataTables on the `tplMasterTable` ID.
    *   Configured for pagination (`pageLength`), searching (`searching`), and sorting (`ordering`).
    *   Columns like `ItemId`, `PId`, `CId` are initially hidden by DataTables `columnDefs` as they are internal IDs not meant for direct display.

*   **No additional JavaScript:** The solution relies solely on HTMX, Alpine.js, and DataTables, adhering to the "no additional JavaScript" guideline beyond these libraries.

### Final Notes

*   **Placeholders:** `[MODEL_NAME]`, `[APP_NAME]`, `[FIELD]` placeholders have been replaced with `TPLMaster`, `design_transactions`, and specific field names.
*   **DRY Templates:** `list.html` extends `core/base.html`. Partial templates (`_tpl_master_table.html`, `form.html`, `confirm_delete.html`) are used for reusable components and HTMX swaps.
*   **Fat Model, Thin View:** The complex data retrieval and processing logic (e.g., `GetDataTable` and `TreeQty` equivalent) has been moved into the `TPLMasterManager` and a helper function `_calculate_tree_qty` within `models.py`. Views remain concise, primarily orchestrating data retrieval and rendering.
*   **Comprehensive Tests:** Unit tests for models cover data integrity and custom manager methods. Integration tests for views cover GET requests, HTMX interactions, and redirects.
*   **Database Mapping:** All models use `managed = False` and `db_table` to map to existing SQL Server tables without Django's migrations.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for styling, assuming Tailwind is configured in the Django project.
*   **Session Management:** The `CompId`, `FinYearId`, and `username` from ASP.NET `Session` are mocked in the `get_queryset` of views and tests. In a real application, these would be integrated with Django's authentication system (e.g., `request.user.profile.compid`).
*   **File Serving:** The `DownloadFileView` is designed to serve files directly from binary fields in the database. If files are stored on the filesystem, this view would need to be adapted to read from storage using Django's `FileSystemStorage` or similar.
*   **Recursive Quantity Calculation (`_calculate_tree_qty`):** The provided Python implementation of `_calculate_tree_qty` performs recursive lookups. For very deep or wide trees, this could lead to performance issues (N+1 queries). For production, consider implementing this using a single SQL query with a Recursive CTE (Common Table Expression) via Django's `raw()` method or by pre-calculating and storing the TPL Quantity if the tree structure changes infrequently.