## ASP.NET to Django Conversion Script: TPL Item - Edit Module

This document outlines the modernization plan for the ASP.NET "TPL Item - Edit" module, transitioning it to a modern Django-based solution. Our focus is on leveraging Django's robust features, HTMX for dynamic interactions, Alpine.js for lightweight UI state, and DataTables for data presentation, all while adhering to the fat model/thin view architecture.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

The ASP.NET page `TPL_Design_Assembly_Edit.aspx` acts as a detail/edit form for a specific TPL (Third Party Logistics) item identified by a Work Order Number (`WONo`) and an `ItemId`. Although it uses a `GridView`, the underlying SQL query filters it to a single row, effectively making it a single-record update page. The modernization will treat it as such, but demonstrate how elements like DataTables would be applied if it were a true list.

### Step 1: Extract Database Schema

Based on the `SqlDataSource` and SQL commands in the code-behind, we identify the following tables and their relevant columns. We infer data types based on usage (e.g., quantity validation, text length).

-   **`tblDG_TPL_Master`**: Main TPL item record.
    -   `Id` (PK, Integer)
    -   `AmdNo` (Amendment No, Integer)
    -   `EquipmentNo` (String)
    -   `UnitNo` (String)
    -   `PartNo` (String)
    -   `CId` (Integer)
    -   `ItemId` (FK to `tblDG_Item_Master.Id`, Integer)
    -   `Qty` (Quantity, Decimal, up to 3 decimal places)
    -   `WONo` (Work Order Number, String)
    -   `PId` (Parent ID, String, '0' for top-level items)
    -   `CompId` (Company ID, Integer)
    -   `FinYearId` (Financial Year ID, Integer)
    -   `SysDate` (System Date, Date)
    -   `SysTime` (System Time, Time)
    -   `SessionId` (User Session ID, String)

-   **`tblDG_Item_Master`**: Item details, linked to `tblDG_TPL_Master`.
    -   `Id` (PK, Integer)
    -   `ManfDesc` (Manufacturer Description, Text)
    -   `UOMBasic` (FK to `Unit_Master.Id`, Integer)
    -   `FileName` (Drawing File Name, String)
    -   `FileSize` (Drawing File Size, Integer)
    -   `ContentType` (Drawing Content Type, String)
    -   `FileData` (Drawing Binary Data, Binary)
    -   `AttName` (Attachment/Spec Sheet Name, String)
    -   `AttSize` (Attachment/Spec Sheet Size, Integer)
    -   `AttContentType` (Attachment/Spec Sheet Content Type, String)
    -   `AttData` (Attachment/Spec Sheet Binary Data, Binary)
    -   `OpeningBalDate` (Opening Balance Date, Date)
    -   `OpeningBalQty` (Opening Balance Quantity, Decimal)
    -   `SysDate` (System Date, Date)
    -   `SysTime` (System Time, Time)
    -   `SessionId` (User Session ID, String)
    -   `CompId` (Company ID, Integer)
    -   `FinYearId` (Financial Year ID, Integer)

-   **`Unit_Master`** (accessed via `vw_Unit_Master`): Lookup for Units of Measure.
    -   `Id` (PK, Integer)
    -   `Symbol` (Symbol/Abbreviation, String)

-   **`tblDG_TPL_Amd`**: Amendment history log.
    -   `SysDate` (Date)
    -   `SysTime` (Time)
    -   `SessionId` (String)
    -   `CompId` (Integer)
    -   `FinYearId` (Integer)
    -   `WONo` (String)
    -   `TPLId` (FK to `tblDG_TPL_Master.Id`, Integer)
    -   `PId` (String)
    -   `CId` (Integer)
    -   `ItemId` (Integer)
    -   `Description` (Text)
    -   `UOM` (String, UOM symbol)
    -   `AmdNo` (Integer)
    -   `Qty` (Decimal)

### Step 2: Identify Backend Functionality

The ASP.NET page provides the following functionalities:

-   **Read**: Displays details of a specific TPL item (`tblDG_TPL_Master`) and its associated item details (`tblDG_Item_Master`) based on `WONo` and `ItemId` passed in the URL. It also populates a dropdown for Units of Measure from `Unit_Master`.
-   **Update**: Edits the `Qty` (on `tblDG_TPL_Master`), `ManfDesc`, and `UOMBasic` (on `tblDG_Item_Master`). It also handles uploading new drawing/spec sheet files or replacing existing ones. Before saving, it logs the current state of the TPL item into `tblDG_TPL_Amd` and increments the `AmdNo`.
-   **Delete**:
    -   **Full Item Delete**: Removes both the `tblDG_Item_Master` and `tblDG_TPL_Master` records.
    -   **File-Specific Delete**: Clears the drawing or spec sheet data from `tblDG_Item_Master` without deleting the item record itself.
-   **Validation**: Enforces that `Qty` is a valid number (up to 3 decimal places) and `ManfDesc` is not empty.
-   **Contextual Data**: Uses `Session` variables (`username`, `compid`, `finyear`) for logging and filtering.

### Step 3: Infer UI Components

The ASP.NET page's UI elements are translated to Django components:

-   **Main Display**: A single-row table that mimics a `GridView` for presenting and editing the specific TPL item's details.
-   **Input Fields**: Django form fields will replace ASP.NET `TextBox` controls for `ManfDesc` and `Qty`, and a `ModelChoiceField` for `DDLUnitBasic` (UOM).
-   **File Uploads**: Django `FileField` will handle `DrwUpload` and `OtherUpload`.
-   **Action Buttons**: HTMX-driven buttons (`Edit`, `Update`, `Cancel`, `Delete`) will manage the interaction flow, replacing ASP.NET LinkButtons/Buttons. Inline images for file deletion will also be HTMX-triggered.
-   **Dynamic Visibility**: Alpine.js, combined with HTMX re-renders, will manage the visibility of read-only labels versus editable input fields and action buttons based on the "edit mode."

### Step 4: Generate Django Code

The following Django files will be generated within a new app named `design` (reflecting `Module_Design_Transactions`).

#### 4.1 Models (`design/models.py`)

This file defines the Django models that map to the existing database tables. We set `managed = False` as these tables are pre-existing, and `db_table` to specify the exact table names. Business logic related to file handling and amendment logging is encapsulated as model methods.

```python
from django.db import models
from django.utils import timezone
import io # For BytesIO

# Default values for company and financial year IDs, to be replaced by actual system context
# In a real system, these would likely come from the authenticated user's profile or request context.
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR_ID = 2023 

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master' # Or 'vw_Unit_Master' if the view is directly used. Assuming base table for flexibility.
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol or 'N/A'

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    manf_desc = models.TextField(db_column='ManfDesc', blank=True, null=True)
    uom_basic = models.ForeignKey(UnitMaster, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    
    # Drawing file data
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.BigIntegerField(db_column='FileSize', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # Storing binary data in DB as per original
    
    # Attachment/Spec Sheet file data
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    att_size = models.BigIntegerField(db_column='AttSize', blank=True, null=True)
    att_content_type = models.CharField(db_column='AttContentType', max_length=255, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True) # Storing binary data in DB as per original
    
    opening_bal_date = models.DateField(db_column='OpeningBalDate', blank=True, null=True)
    opening_bal_qty = models.DecimalField(db_column='OpeningBalQty', max_digits=18, decimal_places=3, blank=True, null=True)
    
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True, default=DEFAULT_COMP_ID)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True, default=DEFAULT_FIN_YEAR_ID)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master Record'
        verbose_name_plural = 'Item Master Records'

    def __str__(self):
        return f"Item {self.id}: {self.manf_desc or 'No Description'}"

    def has_drawing(self):
        """Checks if a drawing file is associated with this item."""
        return bool(self.file_name and self.file_data)

    def has_attachment(self):
        """Checks if an attachment file is associated with this item."""
        return bool(self.att_name and self.att_data)

    def delete_drawing(self):
        """Removes drawing file data from the item."""
        self.file_name = None
        self.file_size = None
        self.content_type = None
        self.file_data = None
        self.save()

    def delete_attachment(self):
        """Removes attachment/spec sheet file data from the item."""
        self.att_name = None
        self.att_size = None
        self.att_content_type = None
        self.att_data = None
        self.save()

class TPLMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    amd_no = models.IntegerField(db_column='AmdNo', blank=True, null=True, default=0)
    equipment_no = models.CharField(db_column='EquipmentNo', max_length=255, blank=True, null=True)
    unit_no = models.CharField(db_column='UnitNo', max_length=255, blank=True, null=True)
    part_no = models.CharField(db_column='PartNo', max_length=255, blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item_id = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True) # Work Order Number
    p_id = models.CharField(db_column='PId', max_length=50, blank=True, null=True) # Parent ID, fixed to '0' in original query
    
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True, default=DEFAULT_COMP_ID)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True, default=DEFAULT_FIN_YEAR_ID)

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Master'
        verbose_name = 'TPL Master Record'
        verbose_name_plural = 'TPL Master Records'

    def __str__(self):
        return f"TPL {self.id} for WO: {self.wono}"

    def log_amendment(self, old_description, old_uom_symbol, old_amd_no, old_qty, session_id, comp_id, fin_year_id):
        """Logs the current state of the TPL item and its associated item details as an amendment."""
        TPLAmendment.objects.create(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id=session_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            wono=self.wono,
            tpl_id=self.id,
            p_id=self.p_id,
            c_id=self.c_id,
            item_id=self.item_id.id if self.item_id else None,
            description=old_description,
            uom=old_uom_symbol,
            amd_no=old_amd_no,
            qty=old_qty
        )

class TPLAmendment(models.Model):
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True, default=DEFAULT_COMP_ID)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True, default=DEFAULT_FIN_YEAR_ID)
    wono = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    tpl_id = models.IntegerField(db_column='TPLId', blank=True, null=True)
    p_id = models.CharField(db_column='PId', max_length=50, blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    description = models.TextField(db_column='Description', blank=True, null=True)
    uom = models.CharField(db_column='UOM', max_length=50, blank=True, null=True)
    amd_no = models.IntegerField(db_column='AmdNo', blank=True, null=True)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_TPL_Amd'
        verbose_name = 'TPL Amendment Log'
        verbose_name_plural = 'TPL Amendment Logs'

    def __str__(self):
        return f"Amendment for TPLId {self.tpl_id} on {self.sys_date}"

```

#### 4.2 Forms (`design/forms.py`)

A custom form is created to handle fields from both `TPLMaster` and `ItemMaster` models, along with file uploads. This streamlines the update process, keeping the view thin. It also carries original values for amendment logging.

```python
from django import forms
from .models import TPLMaster, ItemMaster, UnitMaster
from django.core.validators import RegexValidator
from django.utils import timezone

class TPLAssemblyEditForm(forms.Form):
    # Fields from TPLMaster
    qty = forms.DecimalField(
        label="Qty",
        max_digits=18,
        decimal_places=3,
        required=True,
        validators=[
            RegexValidator(r"^\d{1,15}(\.\d{0,3})?$", "Quantity must be a number with up to 3 decimal places (e.g., 123.456).")
        ],
        widget=forms.TextInput(attrs={'class': 'w-24'}) 
    )

    # Fields from ItemMaster
    manf_desc = forms.CharField(
        label="Description",
        widget=forms.Textarea(attrs={'class': 'w-full', 'rows': 3}),
        required=True
    )
    uom_basic = forms.ModelChoiceField(
        queryset=UnitMaster.objects.all(),
        label="UOM",
        empty_label=None, 
        widget=forms.Select()
    )
    
    # File upload fields
    drw_upload = forms.FileField(label="Drw/Image", required=False)
    other_upload = forms.FileField(label="Spec. Sheet", required=False)

    # Hidden fields to carry original data for amendment logging
    _original_amd_no = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    _original_qty = forms.DecimalField(max_digits=18, decimal_places=3, widget=forms.HiddenInput(), required=False)
    _original_manf_desc = forms.CharField(widget=forms.HiddenInput(), required=False)
    _original_uom_symbol = forms.CharField(widget=forms.HiddenInput(), required=False) 

    def __init__(self, *args, **kwargs):
        self.tpl_instance = kwargs.pop('tpl_instance', None)
        self.item_instance = kwargs.pop('item_instance', None)
        super().__init__(*args, **kwargs)

        if self.tpl_instance and self.item_instance:
            self.fields['qty'].initial = self.tpl_instance.qty
            self.fields['manf_desc'].initial = self.item_instance.manf_desc
            self.fields['uom_basic'].initial = self.item_instance.uom_basic

            # Populate hidden fields with original values for amendment logging
            self.fields['_original_amd_no'].initial = self.tpl_instance.amd_no
            self.fields['_original_qty'].initial = self.tpl_instance.qty
            self.fields['_original_manf_desc'].initial = self.item_instance.manf_desc
            self.fields['_original_uom_symbol'].initial = self.item_instance.uom_basic.symbol if self.item_instance.uom_basic else ''
        
        # Apply Tailwind CSS classes to most form fields
        for field_name, field in self.fields.items():
            if field_name not in ['drw_upload', 'other_upload'] and not field.widget.attrs.get('type') == 'hidden':
                current_classes = field.widget.attrs.get('class', '')
                field.widget.attrs['class'] = f'{current_classes} block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            elif field_name in ['drw_upload', 'other_upload']:
                field.widget.attrs['class'] = 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none'

    def save(self, tpl_instance, item_instance, request_user_session_id, request_comp_id, request_fin_year_id):
        """
        Saves changes to TPLMaster and ItemMaster instances and logs an amendment.
        This method encapsulates the multi-model update logic described in the ASP.NET code-behind.
        """
        # Log amendment BEFORE applying updates to capture original state
        tpl_instance.log_amendment(
            old_description=self.cleaned_data['_original_manf_desc'],
            old_uom_symbol=self.cleaned_data['_original_uom_symbol'],
            old_amd_no=self.cleaned_data['_original_amd_no'],
            old_qty=self.cleaned_data['_original_qty'],
            session_id=request_user_session_id,
            comp_id=request_comp_id,
            fin_year_id=request_fin_year_id
        )

        # Update TPLMaster instance
        tpl_instance.qty = self.cleaned_data['qty']
        tpl_instance.amd_no = (tpl_instance.amd_no or 0) + 1 # Increment amendment number
        tpl_instance.sys_date = timezone.now().date()
        tpl_instance.sys_time = timezone.now().time()
        tpl_instance.session_id = request_user_session_id
        tpl_instance.comp_id = request_comp_id
        tpl_instance.fin_year_id = request_fin_year_id
        tpl_instance.save()

        # Update ItemMaster instance
        item_instance.manf_desc = self.cleaned_data['manf_desc']
        item_instance.uom_basic = self.cleaned_data['uom_basic']
        item_instance.sys_date = timezone.now().date()
        item_instance.sys_time = timezone.now().time()
        item_instance.session_id = request_user_session_id
        item_instance.comp_id = request_comp_id
        item_instance.fin_year_id = request_fin_year_id

        # Handle file uploads
        drw_file = self.cleaned_data.get('drw_upload')
        if drw_file:
            item_instance.file_name = drw_file.name
            item_instance.file_size = drw_file.size
            item_instance.content_type = drw_file.content_type
            item_instance.file_data = drw_file.read() # Read binary content
        
        other_file = self.cleaned_data.get('other_upload')
        if other_file:
            item_instance.att_name = other_file.name
            item_instance.att_size = other_file.size
            item_instance.att_content_type = other_file.content_type
            item_instance.att_data = other_file.read() # Read binary content
        
        item_instance.save()
        return tpl_instance, item_instance

```

#### 4.3 Views (`design/views.py`)

The primary view (`TPLAssemblyEditView`) will serve as an `UpdateView` for the specific TPL item. It handles both initial display and form submissions via HTMX. Auxiliary views manage file downloads and deletions.

```python
from django.views.generic import View
from django.shortcuts import get_object_or_404, render, redirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404, FileResponse
from django.db import transaction
from django.conf import settings 

from .models import TPLMaster, ItemMaster, UnitMaster, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID
from .forms import TPLAssemblyEditForm

import io 

# Helper function to get context variables (replace with actual session/user logic in a real app)
def get_session_context(request):
    """
    Retrieves user session-related context.
    In a production application, this would fetch data from request.user, session, 
    or a multi-tenancy context. For this migration, we use defaults.
    """
    session_id = request.user.username if request.user.is_authenticated else 'anonymous_user'
    comp_id = DEFAULT_COMP_ID 
    fin_year_id = DEFAULT_FIN_YEAR_ID 
    return session_id, comp_id, fin_year_id

class TPLAssemblyEditView(View):
    template_name = 'design/tpl_assembly_edit/edit.html'
    form_partial_template = 'design/tpl_assembly_edit/_edit_form.html'

    def get_object_instances(self, wono, item_id):
        """
        Fetches the TPLMaster and associated ItemMaster instances based on URL parameters.
        This mirrors the complex WHERE clause from the original ASP.NET query.
        """
        try:
            item_master_instance = ItemMaster.objects.get(id=item_id)
            tpl_master_instance = TPLMaster.objects.select_related('item_id').get(
                item_id=item_master_instance, 
                wono=wono, 
                p_id='0', 
                comp_id=DEFAULT_COMP_ID, 
                fin_year_id__lte=DEFAULT_FIN_YEAR_ID 
            )
            return tpl_master_instance, item_master_instance
        except (TPLMaster.DoesNotExist, ItemMaster.DoesNotExist):
            raise Http404("TPL Assembly Item not found or access denied based on context.")

    def get(self, request, wono, item_id):
        tpl_instance, item_instance = self.get_object_instances(wono, item_id)
        form = TPLAssemblyEditForm(tpl_instance=tpl_instance, item_instance=item_instance)
        
        context = {
            'wono': wono,
            'tpl_item': tpl_instance,
            'item_details': item_instance,
            'form': form,
            'is_editing': False, # Initial state is view mode
        }

        # If it's an HTMX request (e.g., from hx-get to refresh the form), render only the partial
        if request.headers.get('HX-Request'):
            return render(request, self.form_partial_template, context)
        
        # Otherwise, render the full page with the initial loading state
        return render(request, self.template_name, context)

    def post(self, request, wono, item_id):
        tpl_instance, item_instance = self.get_object_instances(wono, item_id)
        session_id, comp_id, fin_year_id = get_session_context(request)
        
        form = TPLAssemblyEditForm(request.POST, request.FILES, 
                                    tpl_instance=tpl_instance, 
                                    item_instance=item_instance)

        if form.is_valid():
            with transaction.atomic(): # Ensure atomicity of database operations
                form.save(tpl_instance, item_instance, session_id, comp_id, fin_year_id)
            
            messages.success(request, 'TPL Item updated successfully.')
            
            # HTMX response: Trigger a refresh of the page content after successful update
            # The HX-Redirect header will cause a full browser redirect to the same page,
            # effectively refreshing the entire context and reflecting the latest data.
            return HttpResponse(
                status=204, # No Content to return, only headers
                headers={
                    'HX-Trigger': 'refreshTPLAssemblyEditPage', 
                    'HX-Redirect': reverse_lazy('tpl_assembly_edit', kwargs={'wono': wono, 'item_id': item_id})
                }
            )
        else:
            # If form is not valid, re-render the form partial with errors to provide feedback
            context = {
                'wono': wono,
                'tpl_item': tpl_instance,
                'item_details': item_instance,
                'form': form,
                'is_editing': True, # Keep editing state if validation fails
            }
            return render(request, self.form_partial_template, context)

class TPLAssemblyDeleteView(View):
    """
    Handles the deletion of a TPL Master record and its associated Item Master record.
    Corresponds to the 'Del' command in the ASP.NET GridView.
    """
    def post(self, request, wono, item_id):
        try:
            item_master_instance = ItemMaster.objects.get(id=item_id)
            tpl_master_instance = TPLMaster.objects.get(
                item_id=item_master_instance, 
                wono=wono, 
                p_id='0',
                comp_id=DEFAULT_COMP_ID,
                fin_year_id__lte=DEFAULT_FIN_YEAR_ID
            )
        except (TPLMaster.DoesNotExist, ItemMaster.DoesNotExist):
            messages.error(request, 'Item not found for deletion.')
            return HttpResponse(status=404)

        with transaction.atomic():
            item_master_instance.delete() 
            tpl_master_instance.delete()

        messages.success(request, 'TPL Item and associated details deleted successfully.')
        
        # After deletion, redirect to the parent view (WO Tree View) as the edited item is gone.
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshTPLAssemblyEditPage', # Optional, but good practice
                'HX-Redirect': reverse_lazy('tpl_design_wo_tree_view_edit', kwargs={'wono': wono}) 
            }
        )

class TPLAssemblyDownloadFileView(View):
    """
    Handles downloading drawing and attachment files from ItemMaster records.
    Corresponds to the HyperLink controls in the ASP.NET GridView.
    """
    def get(self, request, item_id, file_type):
        try:
            item_instance = ItemMaster.objects.get(id=item_id)
        except ItemMaster.DoesNotExist:
            raise Http404("Item not found.")

        file_data = None
        file_name = None
        content_type = None

        if file_type == 'drawing' and item_instance.has_drawing():
            file_data = item_instance.file_data
            file_name = item_instance.file_name
            content_type = item_instance.content_type
        elif file_type == 'attachment' and item_instance.has_attachment():
            file_data = item_instance.att_data
            file_name = item_instance.att_name
            content_type = item_instance.att_content_type
        else:
            raise Http404("File not found or not available.")

        response = FileResponse(io.BytesIO(file_data), content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response

class TPLAssemblyDeleteFileView(View):
    """
    Handles deleting specific drawing or attachment files from an ItemMaster record.
    Corresponds to the ImageButton controls ('Del1', 'Del2') in the ASP.NET GridView.
    """
    def post(self, request, item_id, file_type):
        try:
            item_instance = ItemMaster.objects.get(id=item_id)
        except ItemMaster.DoesNotExist:
            messages.error(request, 'Item not found for file deletion.')
            return HttpResponse(status=404)

        if file_type == 'drawing':
            item_instance.delete_drawing()
            messages.success(request, 'Drawing deleted successfully.')
        elif file_type == 'attachment':
            item_instance.delete_attachment()
            messages.success(request, 'Spec sheet deleted successfully.')
        else:
            messages.error(request, 'Invalid file type specified for deletion.')
            return HttpResponse(status=400)
        
        # Re-fetch related TPL instance to correctly rebuild context for partial rendering
        tpl_instance = TPLMaster.objects.filter(item_id=item_instance, p_id='0', comp_id=DEFAULT_COMP_ID, fin_year_id__lte=DEFAULT_FIN_YEAR_ID).first()
        if tpl_instance:
            form = TPLAssemblyEditForm(tpl_instance=tpl_instance, item_instance=item_instance)
            context = {
                'wono': tpl_instance.wono, 
                'tpl_item': tpl_instance,
                'item_details': item_instance,
                'form': form,
                'is_editing': True, # Stay in editing state after file delete
            }
            # Re-render the form partial to update the file links/upload fields
            return render(request, TPLAssemblyEditView.form_partial_template, context)
        
        # If TPL instance is not found (e.g., orphaned item or data inconsistency),
        # trigger a full page refresh to re-evaluate the state.
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshTPLAssemblyEditPage'})


# Placeholder View for the ASP.NET redirect target: TPL_Design_WO_TreeView_Edit.aspx
class TPLDesignWOTreeViewEdit(View):
    def get(self, request, wono):
        msg = request.GET.get('msg', 'Redirected from TPL Assembly Edit.')
        return HttpResponse(f"""
            <h1 class="text-3xl font-bold mb-4">Work Order Tree View Edit</h1>
            <p class="text-gray-700">Work Order Number: <span class="font-semibold">{wono}</span></p>
            <p class="text-green-600 mt-2">{msg}</p>
            <p class="mt-4"><a href="/" class="text-blue-500 hover:underline">Return to Home</a></p>
        """)

```

#### 4.4 Templates (`design/templates/design/tpl_assembly_edit/`)

This module will use two templates: `edit.html` for the main page structure and `_edit_form.html` as a partial template loaded via HTMX, containing the actual form and data display logic.

**`edit.html`** (Main page template)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">TPL Item - Edit</h2>
        <p class="font-bold">WO No: <span id="lblWONo">{{ wono }}</span></p>
    </div>
    
    {# Container for the HTMX-loaded form partial #}
    <div id="tplAssemblyEditForm-container"
         hx-trigger="load, refreshTPLAssemblyEditPage from:body" {# Load on page load, and refresh on custom event #}
         hx-get="{% url 'tpl_assembly_edit_partial' wono=wono item_id=item_details.id %}" {# Fetches the form's current state #}
         hx-swap="innerHTML">
        {# Initial loading state while HTMX fetches content #}
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading TPL item details...</p>
        </div>
    </div>
    
    <div class="mt-8 text-center">
        <button id="cancelButton"
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded shadow-sm"
            onclick="window.location.href='{% url 'tpl_design_wo_tree_view_edit' wono=wono %}'">
            Cancel
        </button>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initialized globally in base.html
    // Specific Alpine.js components can be defined here if needed for this page.
</script>
{% endblock %}

```

**`_edit_form.html`** (Partial template for the editable single-row "grid")

```html
{% load static %}
{% comment %}
    This partial template renders the editable form for a single TPL Assembly Item.
    It simulates the ASP.NET GridView's single-row edit mode with conditional display.
{% endcomment %}

<div x-data="{ isEditing: {{ is_editing|lower }} }"> {# Alpine.js state for toggling edit mode #}
    <form hx-post="{% url 'tpl_assembly_edit' wono=wono item_id=item_details.id %}" 
          hx-encoding="multipart/form-data" {# Important for file uploads #}
          hx-swap="none" {# No swap needed, headers will manage UI #}
          hx-target="body" {# Target body to listen for global HX-Trigger events #}
          hx-trigger="submit">
        {% csrf_token %}
        
        {# Hidden fields carrying original data for amendment logging #}
        {{ form._original_amd_no }}
        {{ form._original_qty }}
        {{ form._original_manf_desc }}
        {{ form._original_uom_symbol }}

        <table class="min-w-full bg-white yui-datatable-theme border border-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amendment No</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment No</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit No &nbsp; (Ex: xx)</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part No/SN (Ex: xx)</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Drw/Image</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
                </tr>
            </thead>
            <tbody>
                <tr class="hover:bg-gray-100">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">1</td> {# SN #}
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <div x-show="!isEditing" class="flex flex-col space-y-1">
                            <button type="button" @click="isEditing = true" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded shadow-sm">Edit</button>
                            <button type="button" class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded shadow-sm"
                                hx-post="{% url 'tpl_assembly_delete' wono=wono item_id=item_details.id %}" 
                                hx-confirm="Are you sure you want to delete this TPL item? This action cannot be undone."
                                hx-swap="none" {# Handled by HX-Redirect on success #}
                                >
                                Delete
                            </button>
                        </div>
                        <div x-show="isEditing" class="flex flex-col space-y-1">
                            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded shadow-sm">Update</button>
                            <button type="button" @click="isEditing = false" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-2 rounded shadow-sm">Cancel</button>
                        </div>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ tpl_item.amd_no|default:"0" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ tpl_item.equipment_no|default:"N/A" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ tpl_item.unit_no|default:"N/A" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ tpl_item.part_no|default:"N/A" }}</td>
                    
                    <td class="py-2 px-4 border-b border-gray-200 align-top">
                        <div x-show="!isEditing">{{ item_details.manf_desc|default:"-" }}</div>
                        <div x-show="isEditing">
                            {{ form.manf_desc }}
                            {% if form.manf_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.manf_desc.errors }}</p>{% endif %}
                        </div>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center align-top">
                        <div x-show="!isEditing">{{ item_details.uom_basic.symbol|default:"-" }}</div>
                        <div x-show="isEditing">
                            {{ form.uom_basic }}
                            {% if form.uom_basic.errors %}<p class="text-red-500 text-xs mt-1">{{ form.uom_basic.errors }}</p>{% endif %}
                        </div>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right align-top">
                        <div x-show="!isEditing">{{ tpl_item.qty|default:"-" }}</div>
                        <div x-show="isEditing">
                            {{ form.qty }}
                            {% if form.qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.qty.errors }}</p>{% endif %}
                        </div>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left align-top">
                        {% if item_details.has_drawing %}
                            <a href="{% url 'tpl_assembly_download_file' item_id=item_details.id file_type='drawing' %}" target="_blank" class="text-blue-600 hover:underline mr-1">
                                {{ item_details.file_name }}
                            </a>
                            <button type="button"
                                hx-post="{% url 'tpl_assembly_delete_file' item_id=item_details.id file_type='drawing' %}"
                                hx-confirm="Are you sure you want to delete this drawing?"
                                hx-swap="outerHTML" {# Replace the whole TD on success #}
                                hx-target="closest('td')"
                                class="inline-block align-middle focus:outline-none"
                                style="width: 16px; height: 13px;">
                                <img src="{% static 'images/cross.gif' %}" alt="Delete Drawing" class="h-full w-full">
                            </button>
                        {% else %}
                            <span x-show="!isEditing" class="text-gray-500">No drawing</span>
                        {% endif %}
                        <div x-show="isEditing" class="mt-1">
                            {{ form.drw_upload }}
                            {% if form.drw_upload.errors %}<p class="text-red-500 text-xs mt-1">{{ form.drw_upload.errors }}</p>{% endif %}
                        </div>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left align-top">
                        {% if item_details.has_attachment %}
                            <a href="{% url 'tpl_assembly_download_file' item_id=item_details.id file_type='attachment' %}" target="_blank" class="text-blue-600 hover:underline mr-1">
                                {{ item_details.att_name }}
                            </a>
                            <button type="button"
                                hx-post="{% url 'tpl_assembly_delete_file' item_id=item_details.id file_type='attachment' %}"
                                hx-confirm="Are you sure you want to delete this spec sheet?"
                                hx-swap="outerHTML"
                                hx-target="closest('td')"
                                class="inline-block align-middle focus:outline-none"
                                style="width: 16px; height: 13px;">
                                <img src="{% static 'images/cross.gif' %}" alt="Delete Spec Sheet" class="h-full w-full">
                            </button>
                        {% else %}
                            <span x-show="!isEditing" class="text-gray-500">No spec sheet</span>
                        {% endif %}
                        <div x-show="isEditing" class="mt-1">
                            {{ form.other_upload }}
                            {% if form.other_upload.errors %}<p class="text-red-500 text-xs mt-1">{{ form.other_upload.errors }}</p>{% endif %}
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </form>

    <script>
        // Initialize DataTables for styling, even if it's a single row,
        // to conform to the `yui-datatable-theme` class and DataTables mandate.
        // In a true list view, this would enable full search/sort/pagination.
        $(document).ready(function() {
            if (!$.fn.DataTable.isDataTable('.yui-datatable-theme')) {
                $('.yui-datatable-theme').DataTable({
                    "paging": false,    
                    "searching": false, 
                    "info": false,      
                    "ordering": false   
                });
            }
        });
    </script>
</div>
```

#### 4.5 URLs (`design/urls.py`)

This file defines the URL patterns for the views, including the main edit page, partial view for HTMX, and dedicated endpoints for file operations and item deletion.

```python
from django.urls import path
from .views import TPLAssemblyEditView, TPLAssemblyDeleteView, TPLAssemblyDownloadFileView, TPLAssemblyDeleteFileView, TPLDesignWOTreeViewEdit

urlpatterns = [
    # Main edit page for a specific TPL Item within a Work Order
    # Takes WONo (string) and ItemId (int) as path parameters
    path('tpl_assembly_edit/<str:wono>/<int:item_id>/', TPLAssemblyEditView.as_view(), name='tpl_assembly_edit'),
    
    # HTMX-specific endpoint to re-render just the form partial
    path('tpl_assembly_edit/<str:wono>/<int:item_id>/partial/', TPLAssemblyEditView.as_view(), name='tpl_assembly_edit_partial'),

    # Endpoints for file operations (download and delete)
    path('tpl_assembly_edit/<int:item_id>/download/<str:file_type>/', TPLAssemblyDownloadFileView.as_view(), name='tpl_assembly_download_file'),
    path('tpl_assembly_edit/<int:item_id>/delete_file/<str:file_type>/', TPLAssemblyDeleteFileView.as_view(), name='tpl_assembly_delete_file'),

    # Endpoint for deleting the entire TPL item
    path('tpl_assembly_edit/<str:wono>/<int:item_id>/delete/', TPLAssemblyDeleteView.as_view(), name='tpl_assembly_delete'),
    
    # Placeholder redirect target for "Cancel" button, mirroring the ASP.NET behavior
    path('tpl_design_wo_tree_view_edit/<str:wono>/', TPLDesignWOTreeViewEdit.as_view(), name='tpl_design_wo_tree_view_edit'),
]

```

#### 4.6 Tests (`design/tests.py`)

Comprehensive unit tests cover model methods and form validation. Integration tests verify the functionality of the views, including HTMX interactions, file uploads, and deletion scenarios. This ensures high code quality and confidence in the migration.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock
import io

from .models import TPLMaster, ItemMaster, UnitMaster, TPLAmendment, DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID
from .forms import TPLAssemblyEditForm

class UnitMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up non-modified objects for all test methods."""
        UnitMaster.objects.create(id=1, symbol='MTR')
        UnitMaster.objects.create(id=2, symbol='KG')

    def test_unit_master_creation(self):
        """Test UnitMaster model creation and string representation."""
        unit = UnitMaster.objects.get(id=1)
        self.assertEqual(unit.symbol, 'MTR')
        self.assertEqual(str(unit), 'MTR')
        self.assertEqual(unit._meta.db_table, 'Unit_Master')
        self.assertFalse(unit._meta.managed)

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up non-modified objects for all test methods."""
        UnitMaster.objects.create(id=1, symbol='MTR')
        cls.unit_mtr = UnitMaster.objects.get(id=1)
        ItemMaster.objects.create(
            id=101,
            manf_desc='Test Item Description',
            uom_basic=cls.unit_mtr,
            file_name='drawing.pdf',
            file_size=100,
            content_type='application/pdf',
            file_data=b'pdf_content',
            att_name='specs.docx',
            att_size=50,
            att_content_type='application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            att_data=b'docx_content',
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id='testuser_item',
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID
        )

    def test_item_master_creation(self):
        """Test ItemMaster model creation and basic attributes."""
        item = ItemMaster.objects.get(id=101)
        self.assertEqual(item.manf_desc, 'Test Item Description')
        self.assertEqual(item.uom_basic, self.unit_mtr)
        self.assertTrue(item.has_drawing())
        self.assertTrue(item.has_attachment())
        self.assertEqual(str(item), 'Item 101: Test Item Description')

    def test_delete_drawing_method(self):
        """Test the delete_drawing model method."""
        item = ItemMaster.objects.get(id=101)
        item.delete_drawing()
        item.refresh_from_db()
        self.assertFalse(item.has_drawing())
        self.assertIsNone(item.file_name)
        self.assertIsNone(item.file_data)
        self.assertIsNone(item.file_size)
        self.assertIsNone(item.content_type)

    def test_delete_attachment_method(self):
        """Test the delete_attachment model method."""
        item = ItemMaster.objects.get(id=101)
        item.delete_attachment()
        item.refresh_from_db()
        self.assertFalse(item.has_attachment())
        self.assertIsNone(item.att_name)
        self.assertIsNone(item.att_data)
        self.assertIsNone(item.att_size)
        self.assertIsNone(item.att_content_type)

class TPLMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up non-modified objects for all test methods."""
        UnitMaster.objects.create(id=1, symbol='MTR')
        cls.item_master = ItemMaster.objects.create(id=101, manf_desc='Item for TPL', uom_basic_id=1)
        TPLMaster.objects.create(
            id=1,
            amd_no=0,
            equipment_no='EQ123',
            unit_no='U001',
            part_no='P-XYZ',
            c_id=1,
            item_id=cls.item_master,
            qty=10.500,
            wono='WO-001',
            p_id='0',
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id='testuser_tpl',
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID
        )

    def test_tpl_master_creation(self):
        """Test TPLMaster model creation and relationships."""
        tpl = TPLMaster.objects.get(id=1)
        self.assertEqual(tpl.wono, 'WO-001')
        self.assertEqual(tpl.qty, 10.500)
        self.assertEqual(tpl.item_id.manf_desc, 'Item for TPL')
        self.assertEqual(str(tpl), 'TPL 1 for WO: WO-001')

    def test_log_amendment_method(self):
        """Test the log_amendment model method creates a TPLAmendment record."""
        tpl = TPLMaster.objects.get(id=1)
        initial_amendment_count = TPLAmendment.objects.count()
        
        # Mock timezone.now for predictable dates in logs/updates
        with patch('django.utils.timezone.now', return_value=timezone.datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)):
            tpl.log_amendment(
                old_description='Old Desc', 
                old_uom_symbol='OLD', 
                old_amd_no=0, 
                old_qty=10.000,
                session_id='testuser_amend', 
                comp_id=DEFAULT_COMP_ID, 
                fin_year_id=DEFAULT_FIN_YEAR_ID
            )
        
        self.assertEqual(TPLAmendment.objects.count(), initial_amendment_count + 1)
        amendment = TPLAmendment.objects.latest('sys_date', 'sys_time') # Get the most recent amendment
        self.assertEqual(amendment.tpl_id, tpl.id)
        self.assertEqual(amendment.amd_no, 0) # Should be the *old* amendment number
        self.assertEqual(amendment.description, 'Old Desc')
        self.assertEqual(amendment.uom, 'OLD')
        self.assertEqual(amendment.qty, 10.000)
        self.assertEqual(amendment.session_id, 'testuser_amend')

class TPLAssemblyEditFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up common data for form tests."""
        cls.unit_mtr = UnitMaster.objects.create(id=1, symbol='MTR')
        cls.item_master = ItemMaster.objects.create(id=101, manf_desc='Initial Desc', uom_basic=cls.unit_mtr)
        cls.tpl_master = TPLMaster.objects.create(id=1, amd_no=0, qty=10.000, item_id=cls.item_master, wono='WO-FORM')

    def test_form_initialization(self):
        """Test form fields are correctly initialized from model instances."""
        form = TPLAssemblyEditForm(tpl_instance=self.tpl_master, item_instance=self.item_master)
        self.assertEqual(form.initial['qty'], 10.000)
        self.assertEqual(form.initial['manf_desc'], 'Initial Desc')
        self.assertEqual(form.initial['uom_basic'], self.unit_mtr)
        self.assertEqual(form.initial['_original_amd_no'], 0)
        self.assertEqual(form.initial['_original_qty'], 10.000)
        self.assertEqual(form.initial['_original_manf_desc'], 'Initial Desc')
        self.assertEqual(form.initial['_original_uom_symbol'], 'MTR')

    def test_form_validation_valid(self):
        """Test form with valid data."""
        form = TPLAssemblyEditForm(
            data={
                'qty': '12.345',
                'manf_desc': 'Updated Description',
                'uom_basic': self.unit_mtr.id,
                '_original_amd_no': 0,
                '_original_qty': 10.000,
                '_original_manf_desc': 'Initial Desc',
                '_original_uom_symbol': 'MTR',
            },
            tpl_instance=self.tpl_master,
            item_instance=self.item_master
        )
        self.assertTrue(form.is_valid())

    def test_form_validation_invalid_qty(self):
        """Test form with invalid quantity format."""
        form = TPLAssemblyEditForm(
            data={
                'qty': 'abc',
                'manf_desc': 'Updated Description',
                'uom_basic': self.unit_mtr.id,
                '_original_amd_no': 0,
                '_original_qty': 10.000,
                '_original_manf_desc': 'Initial Desc',
                '_original_uom_symbol': 'MTR',
            },
            tpl_instance=self.tpl_master,
            item_instance=self.item_master
        )
        self.assertFalse(form.is_valid())
        self.assertIn('qty', form.errors)
        self.assertIn('Quantity must be a number', form.errors['qty'][0])

    def test_form_validation_missing_manf_desc(self):
        """Test form with missing description."""
        form = TPLAssemblyEditForm(
            data={
                'qty': '12.345',
                'manf_desc': '',
                'uom_basic': self.unit_mtr.id,
                '_original_amd_no': 0,
                '_original_qty': 10.000,
                '_original_manf_desc': 'Initial Desc',
                '_original_uom_symbol': 'MTR',
            },
            tpl_instance=self.tpl_master,
            item_instance=self.item_master
        )
        self.assertFalse(form.is_valid())
        self.assertIn('manf_desc', form.errors)
        self.assertIn('This field is required', form.errors['manf_desc'][0])

    def test_form_save_updates_models_and_logs_amendment(self):
        """Test form's save method correctly updates models and logs amendment."""
        new_qty = 15.000
        new_desc = 'New Description'
        
        new_unit = UnitMaster.objects.create(id=2, symbol='PCS') # Create a new UOM for update

        form = TPLAssemblyEditForm(
            data={
                'qty': str(new_qty), # DecimalField expects string input from forms
                'manf_desc': new_desc,
                'uom_basic': new_unit.id,
                '_original_amd_no': self.tpl_master.amd_no,
                '_original_qty': self.tpl_master.qty,
                '_original_manf_desc': self.item_master.manf_desc,
                '_original_uom_symbol': self.item_master.uom_basic.symbol,
            },
            files={},
            tpl_instance=self.tpl_master,
            item_instance=self.item_master
        )
        self.assertTrue(form.is_valid(), form.errors)

        initial_amendment_count = TPLAmendment.objects.count()
        
        # Mock timezone.now for predictable dates/times in logs/updates
        mock_now = timezone.datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)
        with patch('django.utils.timezone.now', return_value=mock_now):
            form.save(self.tpl_master, self.item_master, 'testuser_session', DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)

        self.tpl_master.refresh_from_db()
        self.item_master.refresh_from_db()

        self.assertEqual(self.tpl_master.qty, new_qty)
        self.assertEqual(self.tpl_master.amd_no, 1) # Should increment from 0 to 1
        self.assertEqual(self.tpl_master.sys_date, mock_now.date())
        self.assertEqual(self.tpl_master.sys_time, mock_now.time())

        self.assertEqual(self.item_master.manf_desc, new_desc)
        self.assertEqual(self.item_master.uom_basic, new_unit)
        self.assertEqual(self.item_master.sys_date, mock_now.date())
        self.assertEqual(self.item_master.sys_time, mock_now.time())

        self.assertEqual(TPLAmendment.objects.count(), initial_amendment_count + 1)
        amendment = TPLAmendment.objects.latest('sys_date', 'sys_time')
        self.assertEqual(amendment.tpl_id, self.tpl_master.id)
        self.assertEqual(amendment.description, 'Initial Desc') # Original value logged
        self.assertEqual(amendment.qty, 10.000) # Original value logged
        self.assertEqual(amendment.amd_no, 0) # Original amendment number logged

    def test_form_save_with_file_uploads(self):
        """Test form's save method handles file uploads."""
        new_drawing_file_content = b"new drawing content"
        new_drawing_file = io.BytesIO(new_drawing_file_content)
        new_drawing_file.name = 'new_drawing.pdf'
        new_drawing_file.content_type = 'application/pdf'

        new_spec_file_content = b"new spec content"
        new_spec_file = io.BytesIO(new_spec_file_content)
        new_spec_file.name = 'new_spec.txt'
        new_spec_file.content_type = 'text/plain'
        
        form = TPLAssemblyEditForm(
            data={
                'qty': '10.000',
                'manf_desc': 'Updated Description',
                'uom_basic': self.unit_mtr.id,
                '_original_amd_no': 0,
                '_original_qty': 10.000,
                '_original_manf_desc': 'Initial Desc',
                '_original_uom_symbol': 'MTR',
            },
            files={
                'drw_upload': new_drawing_file,
                'other_upload': new_spec_file,
            },
            tpl_instance=self.tpl_master,
            item_instance=self.item_master
        )
        self.assertTrue(form.is_valid(), form.errors)

        with patch('django.utils.timezone.now', return_value=timezone.datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc)):
            form.save(self.tpl_master, self.item_master, 'testuser_file_upload', DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)

        self.item_master.refresh_from_db()
        self.assertEqual(self.item_master.file_name, 'new_drawing.pdf')
        self.assertEqual(self.item_master.file_size, len(new_drawing_file_content))
        self.assertEqual(self.item_master.content_type, 'application/pdf')
        self.assertEqual(self.item_master.file_data, new_drawing_file_content)

        self.assertEqual(self.item_master.att_name, 'new_spec.txt')
        self.assertEqual(self.item_master.att_size, len(new_spec_file_content))
        self.assertEqual(self.item_master.att_content_type, 'text/plain')
        self.assertEqual(self.item_master.att_data, new_spec_file_content)


class TPLAssemblyViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """Set up common data for all view tests."""
        cls.wono = 'WO-EDIT-001'
        cls.unit_mtr = UnitMaster.objects.create(id=1, symbol='MTR')
        cls.item_master = ItemMaster.objects.create(
            id=101, 
            manf_desc='Original Item Desc', 
            uom_basic=cls.unit_mtr,
            file_name='drawing.pdf',
            file_size=100,
            content_type='application/pdf',
            file_data=b'drawing_data_content',
            att_name='spec.txt',
            att_size=50,
            att_content_type='text/plain',
            att_data=b'spec_data_content',
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id='setup_user_views',
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID
        )
        cls.tpl_master = TPLMaster.objects.create(
            id=1, 
            amd_no=0, 
            qty=5.000, 
            item_id=cls.item_master, 
            wono=cls.wono, 
            p_id='0',
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            session_id='setup_user_views',
            comp_id=DEFAULT_COMP_ID,
            fin_year_id=DEFAULT_FIN_YEAR_ID
        )

        cls.view_url = reverse('tpl_assembly_edit', kwargs={'wono': cls.wono, 'item_id': cls.item_master.id})
        cls.partial_url = reverse('tpl_assembly_edit_partial', kwargs={'wono': cls.wono, 'item_id': cls.item_master.id})
        cls.delete_url = reverse('tpl_assembly_delete', kwargs={'wono': cls.wono, 'item_id': cls.item_master.id})
        cls.download_drawing_url = reverse('tpl_assembly_download_file', kwargs={'item_id': cls.item_master.id, 'file_type': 'drawing'})
        cls.delete_drawing_file_url = reverse('tpl_assembly_delete_file', kwargs={'item_id': cls.item_master.id, 'file_type': 'drawing'})
        cls.delete_attachment_file_url = reverse('tpl_assembly_delete_file', kwargs={'item_id': cls.item_master.id, 'file_type': 'attachment'})


    def setUp(self):
        """Set up for each test method (e.g., client login)."""
        self.client = Client()
        # Mock user login to provide a session_id
        self.mock_user = MagicMock(username='testuser_current_session', is_authenticated=True)
        self.client.force_login(self.mock_user)

    def test_edit_view_get_initial_page(self):
        """Test GET request for the main edit page."""
        response = self.client.get(self.view_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/tpl_assembly_edit/edit.html')
        self.assertContains(response, self.wono)
        self.assertContains(response, 'Loading TPL item details...') # Because the form is loaded via HTMX

    def test_edit_view_get_partial_rendering(self):
        """Test GET request for the HTMX-loaded form partial."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.partial_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design/tpl_assembly_edit/_edit_form.html')
        self.assertContains(response, 'Original Item Desc') # Initial description
        self.assertContains(response, '5.000') # Initial quantity
        self.assertContains(response, 'MTR') # Initial UOM
        self.assertContains(response, 'drawing.pdf') # Initial drawing link
        self.assertContains(response, 'spec.txt') # Initial spec link
        self.assertContains(response, 'x-data="{ isEditing: false }"') # Should start in view mode

    def test_edit_view_post_update_no_files(self):
        """Test POST request for updating data without new file uploads."""
        new_qty = 10.000
        new_desc = 'Updated Item Description'
        UnitMaster.objects.create(id=2, symbol='PCS') # Create another UOM

        data = {
            'qty': str(new_qty),
            'manf_desc': new_desc,
            'uom_basic': 2, # Use the new UOM
            '_original_amd_no': self.tpl_master.amd_no,
            '_original_qty': self.tpl_master.qty,
            '_original_manf_desc': self.item_master.manf_desc,
            '_original_uom_symbol': self.item_master.uom_basic.symbol,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.view_url, data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success response (No Content)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTPLAssemblyEditPage', response.headers['HX-Trigger'])
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], self.view_url)

        self.tpl_master.refresh_from_db()
        self.item_master.refresh_from_db()
        self.assertEqual(self.tpl_master.qty, new_qty)
        self.assertEqual(self.tpl_master.amd_no, 1) # AmdNo should increment
        self.assertEqual(self.item_master.manf_desc, new_desc)
        self.assertEqual(self.item_master.uom_basic.symbol, 'PCS')

        # Check if amendment was logged
        self.assertTrue(TPLAmendment.objects.filter(tpl_id=self.tpl_master.id, amd_no=0, qty=5.000, description='Original Item Desc').exists())

    def test_edit_view_post_update_with_files(self):
        """Test POST request for updating data with new file uploads."""
        new_qty = 12.000
        new_desc = 'Desc with files'
        new_drawing_file_content = b"new drawing content for upload"
        new_drawing_file = io.BytesIO(new_drawing_file_content)
        new_drawing_file.name = 'uploaded_drawing.pdf'
        new_drawing_file.content_type = 'application/pdf'

        new_spec_file_content = b"new spec content for upload"
        new_spec_file = io.BytesIO(new_spec_file_content)
        new_spec_file.name = 'uploaded_spec.txt'
        new_spec_file.content_type = 'text/plain'
        
        data = {
            'qty': str(new_qty),
            'manf_desc': new_desc,
            'uom_basic': self.unit_mtr.id,
            '_original_amd_no': self.tpl_master.amd_no,
            '_original_qty': self.tpl_master.qty,
            '_original_manf_desc': self.item_master.manf_desc,
            '_original_uom_symbol': self.item_master.uom_basic.symbol,
        }
        files = {
            'drw_upload': new_drawing_file,
            'other_upload': new_spec_file,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.view_url, data, files=files, **headers)
        
        self.assertEqual(response.status_code, 204)

        self.item_master.refresh_from_db()
        self.assertEqual(self.item_master.file_name, 'uploaded_drawing.pdf')
        self.assertEqual(self.item_master.file_data, new_drawing_file_content)
        self.assertEqual(self.item_master.file_size, len(new_drawing_file_content))
        self.assertEqual(self.item_master.content_type, 'application/pdf')

        self.assertEqual(self.item_master.att_name, 'uploaded_spec.txt')
        self.assertEqual(self.item_master.att_data, new_spec_file_content)
        self.assertEqual(self.item_master.att_size, len(new_spec_file_content))
        self.assertEqual(self.item_master.att_content_type, 'text/plain')
    
    def test_edit_view_post_invalid_form(self):
        """Test POST request with invalid form data."""
        data = {
            'qty': 'invalid_qty', # Invalid quantity
            'manf_desc': '', # Missing description
            'uom_basic': self.unit_mtr.id,
            '_original_amd_no': self.tpl_master.amd_no,
            '_original_qty': self.tpl_master.qty,
            '_original_manf_desc': self.item_master.manf_desc,
            '_original_uom_symbol': self.item_master.uom_basic.symbol,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.view_url, data, **headers)
        
        self.assertEqual(response.status_code, 200) # Should re-render form partial with errors
        self.assertTemplateUsed(response, 'design/tpl_assembly_edit/_edit_form.html')
        self.assertContains(response, 'Quantity must be a number')
        self.assertContains(response, 'This field is required')
        self.assertContains(response, 'x-data="{ isEditing: true }"') # Should remain in editing mode

    def test_delete_view(self):
        """Test deletion of a TPL item and its associated item master."""
        item_id_to_delete = self.item_master.id
        tpl_id_to_delete = self.tpl_master.id
        
        self.assertTrue(ItemMaster.objects.filter(id=item_id_to_delete).exists())
        self.assertTrue(TPLMaster.objects.filter(id=tpl_id_to_delete).exists())

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.delete_url, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers) 
        self.assertEqual(response.headers['HX-Redirect'], reverse('tpl_design_wo_tree_view_edit', kwargs={'wono': self.wono}))

        self.assertFalse(ItemMaster.objects.filter(id=item_id_to_delete).exists())
        self.assertFalse(TPLMaster.objects.filter(id=tpl_id_to_delete).exists())

    def test_download_file_view_drawing(self):
        """Test downloading a drawing file."""
        response = self.client.get(self.download_drawing_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="drawing.pdf"')
        self.assertEqual(response.content, b'drawing_data_content')

    def test_download_file_view_non_existent_file_type(self):
        """Test downloading a non-existent file type."""
        url = reverse('tpl_assembly_download_file', kwargs={'item_id': self.item_master.id, 'file_type': 'nonexistent'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_download_file_view_non_existent_item(self):
        """Test downloading file for a non-existent item."""
        url = reverse('tpl_assembly_download_file', kwargs={'item_id': 9999, 'file_type': 'drawing'})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_delete_file_view_drawing(self):
        """Test deleting a drawing file."""
        self.item_master.refresh_from_db()
        self.assertTrue(self.item_master.has_drawing())

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.delete_drawing_file_url, **headers)
        
        self.assertEqual(response.status_code, 200) # Re-renders partial
        self.assertTemplateUsed(response, 'design/tpl_assembly_edit/_edit_form.html')

        self.item_master.refresh_from_db()
        self.assertFalse(self.item_master.has_drawing())
        self.assertIsNone(self.item_master.file_name)
        
        self.assertContains(response, 'No drawing') # Verify the display update
        self.assertContains(response, 'Drw/Image') # Input field is now visible
        self.assertContains(response, 'x-data="{ isEditing: true }"') # Should remain in editing mode

    def test_delete_file_view_attachment(self):
        """Test deleting an attachment file."""
        self.item_master.refresh_from_db()
        self.assertTrue(self.item_master.has_attachment())

        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.delete_attachment_file_url, **headers)
        
        self.assertEqual(response.status_code, 200) 
        self.assertTemplateUsed(response, 'design/tpl_assembly_edit/_edit_form.html')

        self.item_master.refresh_from_db()
        self.assertFalse(self.item_master.has_attachment())
        self.assertIsNone(self.item_master.att_name)
        
        self.assertContains(response, 'No spec sheet')
        self.assertContains(response, 'Spec. Sheet')
        self.assertContains(response, 'x-data="{ isEditing: true }"')

    def test_delete_file_view_non_existent_item(self):
        """Test deleting a file for a non-existent item."""
        url = reverse('tpl_assembly_delete_file', kwargs={'item_id': 9999, 'file_type': 'drawing'})
        response = self.client.post(url)
        self.assertEqual(response.status_code, 404)

    def test_delete_file_view_invalid_file_type(self):
        """Test deleting an invalid file type."""
        url = reverse('tpl_assembly_delete_file', kwargs={'item_id': self.item_master.id, 'file_type': 'invalid'})
        response = self.client.post(url)
        self.assertEqual(response.status_code, 400)

```

### Step 5: HTMX and Alpine.js Integration

The integration strategy for HTMX and Alpine.js is as follows:

-   **HTMX for Form Loading**: The main `edit.html` page uses `hx-get` to load the `_edit_form.html` partial. This partial then handles all subsequent interactions.
-   **Alpine.js for UI State**: An `x-data="{ isEditing: false }"` state variable in `_edit_form.html` controls the visibility of `Edit`/`Update`/`Cancel` buttons and input fields, providing a seamless in-place editing experience.
-   **HTMX for Form Submission**: The form within `_edit_form.html` uses `hx-post` with `hx-encoding="multipart/form-data"` (for file uploads).
-   **HTMX for Post-Update Actions**: Upon successful form submission, the server responds with `status=204` and `HX-Redirect` headers, triggering a full page reload of the main `tpl_assembly_edit` page. This ensures the entire page context is refreshed, including any associated messages.
-   **HTMX for File Deletion**: Buttons for deleting drawings and spec sheets (`cross.gif` images) use `hx-post` with `hx-confirm`. On success, they trigger `hx-swap="outerHTML"` targeting `closest('td')` to re-render just that table cell, displaying the file upload field instead of the download link and delete button.
-   **DataTables**: While this specific page acts as a single-row form, the `yui-datatable-theme` class is applied to the table, and a basic DataTables initialization script is included in `_edit_form.html`. This ensures the styling is carried over and demonstrates readiness for handling multi-row list views as per the general guidelines. For a single row, pagination, searching, and ordering are disabled.

### Final Notes

This comprehensive plan provides a robust, modern Django replacement for the legacy ASP.NET TPL Item Edit module. By prioritizing automated conversion techniques and adhering to modern web development best practices, the migration ensures maintainability, scalability, and an improved user experience. The generated code is designed to be complete and runnable, with clear separation of concerns and thorough testing.