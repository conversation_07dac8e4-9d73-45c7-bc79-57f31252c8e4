## ASP.NET to Django Conversion Script:

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET `ItemHistory_TPL` functionality to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI for guidance, and focuses on business value by enhancing system maintainability, scalability, and user experience.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we infer the following primary tables based on `GridView` bindings, `DropDownList` data sources, and SQL queries:

-   **`tblDG_Item_Master`**: This table holds the main item data displayed in the `GridView`.
    -   `Id` (Primary Key, integer)
    -   `ItemCode` (string)
    -   `ManfDesc` (string)
    -   `PurchDesc` (string)
    -   `UOMBasic` (string)
    -   `Location` (string)
    -   `CompId` (integer, Company ID for filtering)
    -   `FinYearId` (integer, Financial Year ID for filtering)

-   **`tblDG_Category_Master`**: Implied as the source for `DrpCategory`.
    -   `CId` (Primary Key, integer)
    -   `Symbol` (string, for display in dropdown)
    -   `CompId` (integer)
    -   `FinYearId` (integer)

-   **`tblDG_SubCategory_Master`**: Source for `DrpSubCategory`, filtered by `CId`.
    -   `SCId` (Primary Key, integer)
    -   `CId` (Foreign Key to `tblDG_Category_Master`, integer)
    -   `Symbol` (string, for display in dropdown)
    -   `CompId` (integer)
    -   `FinYearId` (integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and data retrieval logic in the ASP.NET code.

**Instructions:**

-   **Read (R):** This is the primary operation.
    -   The `GridView2` is populated with item data.
    -   Data retrieval is orchestrated by `fun.SearchData` which takes parameters like category, subcategory, search code, search text, `CompId`, and `FYId`.
    -   `fun.drpDesignCategory` is responsible for populating category and subcategory dropdowns.
    -   `DrpCategory_SelectedIndexChanged` dynamically updates the `DrpSubCategory` based on the selected category and then refreshes the item list.
    -   `DrpSubCategory_SelectedIndexChanged` and `btnSearch_Click` also trigger a refresh of the item list.
    -   `GridView2_PageIndexChanging` handles pagination, re-fetching data.

-   **Create (C), Update (U), Delete (D):** No direct Create, Update, or Delete operations are identified on the `ItemMaster` table within this specific ASP.NET code. The `HyperLinkField` named "Select" navigates to `ItemHistory_TPL_View.aspx`, indicating a separate view for detail/viewing, not for CRUD directly from this page. If CRUD operations are needed for `ItemMaster`, they would be implemented in separate Django views. For this plan, we will focus on the read/search/filter functionality observed.

-   **Validation:** Basic dropdown selection checks (`DrpCategory.SelectedValue != "Select Category"`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for mapping to Django templates with HTMX/Alpine.js.

**Instructions:**

-   **`DrpCategory` (DropDownList) and `DrpSubCategory` (DropDownList):** These will be rendered as `<select>` elements in Django templates. Their `AutoPostBack` and `SelectedIndexChanged` behavior will be replicated using HTMX `hx-get` and `hx-trigger` attributes, along with `hx-target` to update relevant parts of the page (like the subcategory dropdown itself and the main item table).
-   **`DrpSearchCode` (DropDownList):** A `<select>` element for choosing search criteria.
-   **`txtSearchItemCode` (TextBox):** An `<input type="text">` for the search query.
-   **`btnSearch` (Button):** A `<button>` element with an HTMX `hx-post` or `hx-get` to trigger the item search/filter.
-   **`GridView2` (GridView):** This will be replaced by a `<table>` element initialized with DataTables. HTMX will be used to dynamically load and refresh this table content.
-   **Page Structure:** The master page (`MasterPage.master`) implies a base template structure in Django (`core/base.html`). Content Placeholders (`ContentPlaceHolder3`, `MainContent`) will be mapped to Django template blocks.

### Step 4: Generate Django Code

We will create a Django application named `design_masters` to house this functionality.

#### 4.1 Models (`design_masters/models.py`)

**Task:** Create Django models based on the identified database schema. These models will include methods to encapsulate business logic currently residing in `clsFunctions` (e.g., `SearchData`, `drpDesignCategory`).

**Instructions:**
Models are defined with `managed = False` and `db_table` to map to existing database tables. Foreign keys are explicitly defined. Model methods will encapsulate data retrieval logic.

```python
from django.db import models
from django.db.models import Q # For complex queries

class Category(models.Model):
    """
    Represents an item category from tblDG_Category_Master.
    Managed=False means Django won't create/alter this table.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.symbol or f"Category {self.cid}"

    @classmethod
    def get_all_categories(cls, comp_id, fin_year_id):
        """
        Mimics part of fun.drpDesignCategory for Category dropdown.
        """
        return cls.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id).order_by('symbol')

class SubCategory(models.Model):
    """
    Represents an item subcategory from tblDG_SubCategory_Master.
    Managed=False means Django won't create/alter this table.
    """
    scid = models.IntegerField(db_column='SCId', primary_key=True)
    cid = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_SubCategory_Master'
        verbose_name = 'SubCategory'
        verbose_name_plural = 'SubCategories'

    def __str__(self):
        return self.symbol or f"SubCategory {self.scid}"

    @classmethod
    def get_subcategories_by_category(cls, category_id, comp_id, fin_year_id):
        """
        Mimics logic in DrpCategory_SelectedIndexChanged for SubCategory dropdown.
        """
        if category_id and category_id != 'Select Category': # Handling placeholder
            return cls.objects.filter(
                cid=category_id,
                comp_id=comp_id,
                fin_year_id__lte=fin_year_id
            ).order_by('symbol')
        return cls.objects.none() # Return empty queryset if no valid category

class ItemMaster(models.Model):
    """
    Represents an item from tblDG_Item_Master.
    Managed=False means Django won't create/alter this table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    purch_desc = models.CharField(db_column='PurchDesc', max_length=255, blank=True, null=True)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or f"Item {self.id}"

    @classmethod
    def search_items(cls, comp_id, fin_year_id, category_id=None, subcategory_id=None,
                     search_code=None, search_text=None):
        """
        Mimics fun.SearchData logic.
        This method will return a QuerySet based on the provided filters.
        Note: Joining Category/SubCategory tables would require changes if these are linked in DB directly.
        For now, assuming filters based on IDs are applied at the ItemMaster level or by implied joins.
        """
        queryset = cls.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)

        # Apply category and subcategory filters if provided
        # (This assumes ItemMaster has CId/SCId, which it doesn't from the GridView.
        # This might need a database schema review. For this example, assuming a direct link
        # or that category/subcategory filters are handled by the calling function's logic.
        # If ItemMaster needs these fields, they would be added here with FKs.)
        # As per the ASP.NET code, Category and SubCategory dropdowns just filter the *search*,
        # not necessarily that ItemMaster has direct FKs to Category/SubCategory.
        # The fun.SearchData would likely use these to build a complex query, potentially
        # joining or filtering on a related table not explicitly shown in this ASPX.
        # Given the provided code, I'll *assume* a complex join or indirect filtering if needed,
        # but for simplicity, I'll implement search based on text fields directly on ItemMaster.

        if search_text:
            search_text = search_text.strip()
            if search_code == 'tblDG_Item_Master.ItemCode':
                queryset = queryset.filter(item_code__icontains=search_text)
            elif search_code == 'tblDG_Item_Master.ManfDesc':
                queryset = queryset.filter(manf_desc__icontains=search_text)
            elif search_code == 'tblDG_Item_Master.PurchDesc':
                queryset = queryset.filter(purch_desc__icontains=search_text)
            # If no search_code or 'Select', perhaps search all relevant fields
            elif search_code == 'Select' or not search_code:
                queryset = queryset.filter(
                    Q(item_code__icontains=search_text) |
                    Q(manf_desc__icontains=search_text) |
                    Q(purch_desc__icontains=search_text)
                )

        # For categories/subcategories:
        # If the backend `fun.SearchData` truly filters `ItemMaster` directly by Category/SubCategory
        # IDs, `ItemMaster` would need `category_id` and `subcategory_id` fields.
        # Since these aren't in the GridView columns for ItemMaster, I'll defer complex
        # multi-table filtering to a more explicit requirements phase.
        # The current ASP.NET behavior implies category/subcategory are used to narrow down
        # the *scope* of the search, not necessarily filter *all* items by category.
        # For simplicity in this plan, I'll primarily focus on text search as shown.
        # If the actual DB schema includes category/subcategory foreign keys on ItemMaster,
        # those filters would be added here:
        # if category_id and category_id != 'Select Category':
        #     queryset = queryset.filter(category__cid=category_id)
        # if subcategory_id and subcategory_id != 'Select SubCategory':
        #     queryset = queryset.filter(subcategory__scid=subcategory_id)

        return queryset.order_by('item_code') # Default ordering
```

#### 4.2 Forms (`design_masters/forms.py`)

**Task:** Define Django forms for user input. In this case, we primarily have filter/search inputs, not a typical CRUD form for `ItemMaster`. We'll define simple forms if needed for structure, or handle inputs directly in views/templates if they are very simple. For dropdowns and text input used for filtering, a simple `Form` (not `ModelForm`) is often sufficient, or even just processing `request.GET` parameters.

**Instructions:**
Since the ASP.NET `ItemHistory_TPL` is a list/search page and not a data entry form, we won't define a `ModelForm` for `ItemMaster` here. However, we can create a simple `Form` to encapsulate the search/filter parameters for better structure if desired. For this specific case, direct parameter handling in the view is cleaner given the HTMX pattern.

```python
# design_masters/forms.py
from django import forms
from .models import Category, SubCategory

# No ModelForm for ItemMaster as this page is for listing/searching only.
# If we were to manage the search form as a Django Form:
class ItemSearchForm(forms.Form):
    category = forms.ModelChoiceField(
        queryset=Category.objects.none(), # Will be populated dynamically
        required=False,
        empty_label="Select Category",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'hx-get': '/design-masters/subcategories/', 'hx-target': '#subcategory-dropdown-container', 'hx-trigger': 'change', 'name': 'category'})
    )
    subcategory = forms.ModelChoiceField(
        queryset=SubCategory.objects.none(), # Will be populated dynamically
        required=False,
        empty_label="Select SubCategory",
        widget=forms.Select(attrs={'class': 'box3 w-full', 'name': 'subcategory'})
    )
    search_code = forms.ChoiceField(
        choices=[
            ('', 'Select'), # Blank value for "Select"
            ('tblDG_Item_Master.ItemCode', 'Item Code'),
            ('tblDG_Item_Master.ManfDesc', 'Manuf. Description'),
            ('tblDG_Item_Master.PurchDesc', 'Purchase Description'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'box3 w-full', 'name': 'search_code'})
    )
    search_text = forms.CharField(
        max_length=207,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'name': 'search_text'})
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        selected_category_id = kwargs.pop('selected_category_id', None)
        super().__init__(*args, **kwargs)

        if comp_id and fin_year_id:
            self.fields['category'].queryset = Category.get_all_categories(comp_id, fin_year_id)
            if selected_category_id:
                self.fields['subcategory'].queryset = SubCategory.get_subcategories_by_category(selected_category_id, comp_id, fin_year_id)
            else:
                self.fields['subcategory'].queryset = SubCategory.objects.none() # Initially empty

```

#### 4.3 Views (`design_masters/views.py`)

**Task:** Implement the item history list and search functionality using Django CBVs. We will have a main `ListView` and a partial view for the DataTables content, and another partial view for the dynamic subcategory dropdown.

**Instructions:**
Views will be thin, delegating complex query logic to model methods. HTMX interaction will be central, avoiding full page reloads.

```python
# design_masters/views.py
from django.views.generic import TemplateView, ListView
from django.http import HttpResponse
from django.shortcuts import render
from .models import ItemMaster, Category, SubCategory
from .forms import ItemSearchForm # Although we'll mostly use request.GET directly

# Placeholder for Company ID and Financial Year ID from session/context.
# In a real application, these would come from request.user profile, session, or a global setting.
# For demonstration, we'll hardcode or retrieve from a simple mock.
def get_user_context_vars(request):
    """
    Retrieves CompId and FYId. Replace with actual session/user profile logic.
    """
    # Example: Retrieve from session, fallback to default or configuration
    comp_id = request.session.get('comp_id', 1) # Default to 1
    fin_year_id = request.session.get('fin_year_id', 2023) # Default to 2023
    return {'comp_id': comp_id, 'fin_year_id': fin_year_id}


class ItemHistoryListView(TemplateView):
    """
    Main view for Item History. Renders the initial page with search controls
    and a container for the HTMX-loaded DataTables.
    Corresponds to initial Page_Load in ASP.NET.
    """
    template_name = 'design_masters/itemmaster/item_history_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_context = get_user_context_vars(self.request)
        comp_id = user_context['comp_id']
        fin_year_id = user_context['fin_year_id']

        # Populate initial dropdowns
        categories = Category.get_all_categories(comp_id, fin_year_id)
        context['categories'] = categories
        context['subcategories'] = SubCategory.objects.none() # Initially empty

        # Initialize form for rendering search inputs if using Django forms
        # form = ItemSearchForm(comp_id=comp_id, fin_year_id=fin_year_id)
        # context['form'] = form

        return context


class ItemHistoryTablePartialView(ListView):
    """
    HTMX-targeted view that renders only the DataTables content.
    This replaces the fun.SearchData and GridView binding logic.
    """
    model = ItemMaster
    template_name = 'design_masters/itemmaster/_item_history_table.html'
    context_object_name = 'item_masters'

    def get_queryset(self):
        user_context = get_user_context_vars(self.request)
        comp_id = user_context['comp_id']
        fin_year_id = user_context['fin_year_id']

        # Extract filters from request.GET (e.g., from HTMX parameters)
        category_id = self.request.GET.get('category')
        subcategory_id = self.request.GET.get('subcategory')
        search_code = self.request.GET.get('search_code')
        search_text = self.request.GET.get('search_text')

        return ItemMaster.search_items(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            category_id=category_id,
            subcategory_id=subcategory_id,
            search_code=search_code,
            search_text=search_text
        )

    def render_to_response(self, context, **response_kwargs):
        # Ensure it's an HTMX request for partial render.
        # If not HTMX, it might be an error or direct access.
        if not self.request.headers.get('HX-Request'):
            # Potentially redirect or return an error for non-HTMX requests
            return HttpResponse("This endpoint is designed for HTMX requests only.", status=400)
        return super().render_to_response(context, **response_kwargs)


class SubCategoryDropdownPartialView(TemplateView):
    """
    HTMX-targeted view to render the SubCategory dropdown options dynamically.
    Replaces DrpCategory_SelectedIndexChanged logic for subcategory population.
    """
    template_name = 'design_masters/itemmaster/_sub_category_dropdown.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_context = get_user_context_vars(self.request)
        comp_id = user_context['comp_id']
        fin_year_id = user_context['fin_year_id']

        selected_category_id = self.request.GET.get('category')
        subcategories = SubCategory.get_subcategories_by_category(selected_category_id, comp_id, fin_year_id)
        context['subcategories'] = subcategories
        context['selected_subcategory_id'] = self.request.GET.get('subcategory') # To re-select if coming from a full form

        return context

    def render_to_response(self, context, **response_kwargs):
        if not self.request.headers.get('HX-Request'):
            return HttpResponse("This endpoint is designed for HTMX requests only.", status=400)
        return super().render_to_response(context, **response_kwargs)


# View for Item History Detail (corresponds to ItemHistory_TPL_View.aspx)
# This is a conceptual placeholder as the ASP.NET code for this page wasn't provided.
# It would typically be a DetailView.
class ItemHistoryDetailView(TemplateView):
    template_name = 'design_masters/itemmaster/item_history_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_id = self.kwargs.get('pk')
        try:
            item = ItemMaster.objects.get(pk=item_id)
            context['item'] = item
        except ItemMaster.DoesNotExist:
            context['item'] = None # Handle item not found
            # You might want to raise Http404 or redirect
        return context

```

#### 4.4 Templates (`design_masters/templates/design_masters/itemmaster/`)

**Task:** Create templates for the list view and partials for the table and dynamic dropdowns.

**Instructions:**
Templates will extend `core/base.html`. HTMX attributes (`hx-get`, `hx-target`, `hx-trigger`, `hx-swap`) will drive dynamic updates. DataTables will be initialized on the `_item_history_table.html` partial. Tailwind CSS classes are used for styling.

```html
{# design_masters/templates/design_masters/itemmaster/item_history_list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Item History - TPL</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
            {# Category Dropdown #}
            <div>
                <label for="id_category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select id="id_category" name="category"
                        class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        hx-get="{% url 'design_masters:subcategories_partial' %}"
                        hx-target="#subcategory-dropdown-container"
                        hx-trigger="change"
                        hx-include="[name='category']" {# Pass the category value #}
                        hx-swap="innerHTML">
                    <option value="">Select Category</option>
                    {% for cat in categories %}
                        <option value="{{ cat.cid }}">{{ cat.symbol }}</option>
                    {% endfor %}
                </select>
            </div>

            {# SubCategory Dropdown (Dynamic) #}
            <div id="subcategory-dropdown-container">
                {# Initial state, will be replaced by HTMX #}
                <label for="id_subcategory" class="block text-sm font-medium text-gray-700 mb-1">SubCategory</label>
                <select id="id_subcategory" name="subcategory"
                        class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">Select SubCategory</option>
                    {# Options will be loaded by HTMX #}
                </select>
            </div>

            {# Search Code Dropdown #}
            <div>
                <label for="id_search_code" class="block text-sm font-medium text-gray-700 mb-1">Search By</label>
                <select id="id_search_code" name="search_code"
                        class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">Select</option>
                    <option value="tblDG_Item_Master.ItemCode">Item Code</option>
                    <option value="tblDG_Item_Master.ManfDesc">Manuf. Description</option>
                    <option value="tblDG_Item_Master.PurchDesc">Purchase Description</option>
                </select>
            </div>

            {# Search Item Code Textbox #}
            <div>
                <label for="id_search_text" class="block text-sm font-medium text-gray-700 mb-1">Search Text</label>
                <input type="text" id="id_search_text" name="search_text"
                       class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                       placeholder="Enter search term">
            </div>

            {# Search Button #}
            <div class="md:col-span-4 lg:col-span-1 flex justify-end lg:justify-start">
                <button type="button"
                        class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        hx-get="{% url 'design_masters:item_history_table' %}"
                        hx-target="#item-master-table-container"
                        hx-trigger="click, keyup from:#id_search_text[enter]" {# Trigger on click or Enter in search text #}
                        hx-include="[name='category'], [name='subcategory'], [name='search_code'], [name='search_text']"
                        hx-indicator="#loading-indicator"
                        hx-swap="innerHTML">
                    Search
                </button>
            </div>
        </div>
    </div>

    {# Loading Indicator #}
    <div id="loading-indicator" class="htmx-indicator flex items-center justify-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-2"></div>
        <p class="text-gray-600">Loading data...</p>
    </div>

    {# DataTables Container #}
    <div id="item-master-table-container"
         hx-get="{% url 'design_masters:item_history_table' %}"
         hx-trigger="load delay:100ms, category_changed from:#id_category, subcategory_changed from:#subcategory-dropdown-container select, search_triggered from:body" {# Initial load, and custom events for filter changes #}
         hx-include="[name='category'], [name='subcategory'], [name='search_code'], [name='search_text']"
         hx-swap="innerHTML">
        {# Initial loading state for DataTables #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Item History...</p>
        </div>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
    // Dispatch custom events for HTMX triggers
    document.addEventListener('change', function(event) {
        if (event.target && event.target.id === 'id_category') {
            document.body.dispatchEvent(new Event('category_changed'));
        }
        if (event.target && event.target.name === 'subcategory') { // This targets the dynamic select
            document.body.dispatchEvent(new Event('subcategory_changed'));
        }
    });

    document.addEventListener('click', function(event) {
        if (event.target && event.target.tagName === 'BUTTON' && event.target.textContent.trim() === 'Search') {
            document.body.dispatchEvent(new Event('search_triggered'));
        }
    });
</script>
{% endblock %}
```

```html
{# design_masters/templates/design_masters/itemmaster/_item_history_table.html #}
{# This template is loaded via HTMX, thus no {% extends %} #}
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    {% if item_masters %}
    <table id="itemMasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf Desc</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM Basic</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                {# Purch Desc is hidden in ASP.NET, so it won't be displayed here, but could be added if needed #}
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in item_masters %}
            <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <a href="{% url 'design_masters:item_history_detail' pk=item.id %}"
                       class="text-blue-600 hover:text-blue-900">Select</a>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.item_code }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.manf_desc }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.uom_basic }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.location }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-10">
        <p class="text-lg text-red-600 font-semibold">No data to display !</p>
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTables after the content is loaded by HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#itemMasterTable')) {
            $('#itemMasterTable').DataTable().destroy();
        }
        $('#itemMasterTable').DataTable({
            "pageLength": 15,
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false,
            "order": [], // Disable initial ordering for flexible data
            "columnDefs": [
                { "orderable": false, "targets": [0, 1] } // SN and Actions columns not orderable
            ]
        });
    });
</script>
```

```html
{# design_masters/templates/design_masters/itemmaster/_sub_category_dropdown.html #}
{# This template is loaded via HTMX to update the subcategory dropdown #}
<label for="id_subcategory" class="block text-sm font-medium text-gray-700 mb-1">SubCategory</label>
<select id="id_subcategory" name="subcategory"
        class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
        hx-trigger="change" {# Re-trigger a change event if you need to chain #}
        hx-include="[name='category'], [name='subcategory'], [name='search_code'], [name='search_text']" {# Include all form data #}
        hx-get="{% url 'design_masters:item_history_table' %}" {# Trigger item table refresh #}
        hx-target="#item-master-table-container"
        hx-swap="innerHTML">
    <option value="">Select SubCategory</option>
    {% for subcat in subcategories %}
        <option value="{{ subcat.scid }}" {% if subcat.scid|stringformat:"s" == selected_subcategory_id %}selected{% endif %}>{{ subcat.symbol }}</option>
    {% endfor %}
</select>
<script>
    // Dispatch a custom event after the subcategory dropdown is updated by HTMX
    // This ensures the main item table update can listen for this change if desired
    document.getElementById('subcategory-dropdown-container').dispatchEvent(new Event('subcategory_changed', { bubbles: true }));
</script>
```

```html
{# design_masters/templates/design_masters/itemmaster/item_history_detail.html #}
{# Conceptual placeholder for the detail view for ItemHistory_TPL_View.aspx #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Item Detail: {{ item.item_code }}</h2>

    {% if item %}
    <div class="bg-white shadow-md rounded-lg p-6">
        <dl class="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-8">
            <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">Item ID</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.id }}</dd>
            </div>
            <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">Item Code</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.item_code }}</dd>
            </div>
            <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">Manufacturer Description</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.manf_desc }}</dd>
            </div>
            <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">Purchase Description</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.purch_desc }}</dd>
            </div>
            <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">UOM Basic</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.uom_basic }}</dd>
            </div>
            <div class="sm:col-span-1">
                <dt class="text-sm font-medium text-gray-500">Location</dt>
                <dd class="mt-1 text-sm text-gray-900">{{ item.location }}</dd>
            </div>
        </dl>
    </div>
    <div class="mt-6">
        <a href="{% url 'design_masters:item_history_list' %}"
           class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Back to Item History
        </a>
    </div>
    {% else %}
    <div class="bg-white shadow-md rounded-lg p-6 text-center text-red-600">
        <p class="text-lg">Item not found.</p>
        <a href="{% url 'design_masters:item_history_list' %}" class="mt-4 inline-block text-blue-600 hover:text-blue-800">Back to List</a>
    </div>
    {% endif %}
</div>
{% endblock %}
```

#### 4.5 URLs (`design_masters/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are named for easy referencing in templates and views.

```python
# design_masters/urls.py
from django.urls import path
from .views import ItemHistoryListView, ItemHistoryTablePartialView, SubCategoryDropdownPartialView, ItemHistoryDetailView

app_name = 'design_masters' # Namespace for this app's URLs

urlpatterns = [
    path('item-history/', ItemHistoryListView.as_view(), name='item_history_list'),
    path('item-history/table/', ItemHistoryTablePartialView.as_view(), name='item_history_table'),
    path('subcategories/', SubCategoryDropdownPartialView.as_view(), name='subcategories_partial'),
    path('item-history/<int:pk>/', ItemHistoryDetailView.as_view(), name='item_history_detail'), # For ItemHistory_TPL_View.aspx
]
```

#### 4.6 Tests (`design_masters/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
Tests cover model behavior, data retrieval, and view responses, including HTMX interactions. We aim for high coverage.

```python
# design_masters/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from .models import ItemMaster, Category, SubCategory
from .views import get_user_context_vars # Import for mocking

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.comp_id = 1
        cls.fin_year_id = 2023

        cls.category1 = Category.objects.create(cid=101, symbol='Electronics', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.category2 = Category.objects.create(cid=102, symbol='Clothing', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)

        cls.subcategory1 = SubCategory.objects.create(scid=201, cid=cls.category1, symbol='Laptops', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.subcategory2 = SubCategory.objects.create(scid=202, cid=cls.category1, symbol='Smartphones', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        SubCategory.objects.create(scid=203, cid=cls.category2, symbol='T-shirts', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)

        ItemMaster.objects.create(
            id=1, item_code='ELEC001', manf_desc='Dell XPS 15', purch_desc='Laptop',
            uom_basic='PCS', location='WH1', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        ItemMaster.objects.create(
            id=2, item_code='ELEC002', manf_desc='Samsung S23', purch_desc='Phone',
            uom_basic='PCS', location='WH2', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        ItemMaster.objects.create(
            id=3, item_code='CLO001', manf_desc='Cotton Tee', purch_desc='Basic T-shirt',
            uom_basic='PCS', location='WH3', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        ItemMaster.objects.create(
            id=4, item_code='ELEC003', manf_desc='Apple MacBook Air', purch_desc='Laptop',
            uom_basic='PCS', location='WH1', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )


    def test_category_creation(self):
        cat = Category.objects.get(cid=self.category1.cid)
        self.assertEqual(cat.symbol, 'Electronics')
        self.assertEqual(str(cat), 'Electronics')

    def test_subcategory_creation(self):
        subcat = SubCategory.objects.get(scid=self.subcategory1.scid)
        self.assertEqual(subcat.symbol, 'Laptops')
        self.assertEqual(str(subcat), 'Laptops')
        self.assertEqual(subcat.cid, self.category1)

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.item_code, 'ELEC001')
        self.assertEqual(item.manf_desc, 'Dell XPS 15')
        self.assertEqual(item.uom_basic, 'PCS')
        self.assertEqual(str(item), 'ELEC001')

    def test_get_all_categories(self):
        categories = Category.get_all_categories(self.comp_id, self.fin_year_id)
        self.assertEqual(categories.count(), 2)
        self.assertIn(self.category1, categories)
        self.assertIn(self.category2, categories)

    def test_get_subcategories_by_category(self):
        subcategories = SubCategory.get_subcategories_by_category(self.category1.cid, self.comp_id, self.fin_year_id)
        self.assertEqual(subcategories.count(), 2)
        self.assertIn(self.subcategory1, subcategories)
        self.assertIn(self.subcategory2, subcategories)

        empty_subcategories = SubCategory.get_subcategories_by_category('Select Category', self.comp_id, self.fin_year_id)
        self.assertEqual(empty_subcategories.count(), 0)

        no_category_subcategories = SubCategory.get_subcategories_by_category(999, self.comp_id, self.fin_year_id) # Non-existent category
        self.assertEqual(no_category_subcategories.count(), 0)

    def test_search_items_no_filter(self):
        items = ItemMaster.search_items(self.comp_id, self.fin_year_id)
        self.assertEqual(items.count(), 4)

    def test_search_items_by_item_code(self):
        items = ItemMaster.search_items(self.comp_id, self.fin_year_id,
                                        search_code='tblDG_Item_Master.ItemCode', search_text='ELEC001')
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ELEC001')

    def test_search_items_by_manf_desc(self):
        items = ItemMaster.search_items(self.comp_id, self.fin_year_id,
                                        search_code='tblDG_Item_Master.ManfDesc', search_text='Laptop')
        self.assertEqual(items.count(), 2)
        self.assertIn(ItemMaster.objects.get(id=1), items)
        self.assertIn(ItemMaster.objects.get(id=4), items)

    def test_search_items_by_purch_desc(self):
        items = ItemMaster.search_items(self.comp_id, self.fin_year_id,
                                        search_code='tblDG_Item_Master.PurchDesc', search_text='Phone')
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ELEC002')

    def test_search_items_general_search(self):
        # Search for 'Laptop' without specific search code
        items = ItemMaster.search_items(self.comp_id, self.fin_year_id, search_text='Laptop')
        self.assertEqual(items.count(), 2)
        self.assertIn(ItemMaster.objects.get(id=1), items)
        self.assertIn(ItemMaster.objects.get(id=4), items)

        # Search for 'ELEC' without specific search code (should match item_code)
        items = ItemMaster.search_items(self.comp_id, self.fin_year_id, search_text='ELEC')
        self.assertEqual(items.count(), 3)
        self.assertIn(ItemMaster.objects.get(id=1), items)
        self.assertIn(ItemMaster.objects.get(id=2), items)
        self.assertIn(ItemMaster.objects.get(id=4), items)

class ItemHistoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.comp_id = 1
        cls.fin_year_id = 2023

        cls.category1 = Category.objects.create(cid=101, symbol='Electronics', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.category2 = Category.objects.create(cid=102, symbol='Clothing', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.subcategory1 = SubCategory.objects.create(scid=201, cid=cls.category1, symbol='Laptops', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id)
        cls.item1 = ItemMaster.objects.create(
            id=1, item_code='ELEC001', manf_desc='Dell XPS 15', purch_desc='Laptop',
            uom_basic='PCS', location='WH1', comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

    def setUp(self):
        self.client = Client()
        # Mock get_user_context_vars to control CompId and FYId during tests
        self.patcher = patch('design_masters.views.get_user_context_vars', return_value={'comp_id': self.comp_id, 'fin_year_id': self.fin_year_id})
        self.mock_get_user_context_vars = self.patcher.start()

    def tearDown(self):
        self.patcher.stop()

    def test_item_history_list_view(self):
        response = self.client.get(reverse('design_masters:item_history_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/itemmaster/item_history_list.html')
        self.assertContains(response, 'Item History - TPL')
        self.assertContains(response, 'Select Category')
        self.assertContains(response, 'Select SubCategory')
        self.assertContains(response, 'Search')

    def test_item_history_table_partial_view_initial_load(self):
        # Simulate HTMX request for initial table load
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design_masters:item_history_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/itemmaster/_item_history_table.html')
        self.assertContains(response, self.item1.item_code)
        self.assertContains(response, 'id="itemMasterTable"')
        self.assertNotContains(response, 'No data to display') # Should contain data

    def test_item_history_table_partial_view_with_search(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        search_params = {
            'search_code': 'tblDG_Item_Master.ItemCode',
            'search_text': 'ELEC001'
        }
        response = self.client.get(reverse('design_masters:item_history_table'), search_params, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.item1.item_code)
        self.assertNotContains(response, 'Samsung S23') # Should not contain other items

    def test_item_history_table_partial_view_no_data(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        search_params = {
            'search_code': 'tblDG_Item_Master.ItemCode',
            'search_text': 'NONEXISTENT'
        }
        response = self.client.get(reverse('design_masters:item_history_table'), search_params, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')
        self.assertNotContains(response, 'id="itemMasterTable"') # Table shouldn't be rendered if no data

    def test_subcategories_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design_masters:subcategories_partial'), {'category': self.category1.cid}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/itemmaster/_sub_category_dropdown.html')
        self.assertContains(response, self.subcategory1.symbol)
        self.assertContains(response, 'Laptops')
        self.assertContains(response, 'Smartphones')
        self.assertNotContains(response, 'T-shirts') # Should not contain subcategory from other category

    def test_subcategories_partial_view_no_category_selected(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('design_masters:subcategories_partial'), {'category': ''}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/itemmaster/_sub_category_dropdown.html')
        self.assertContains(response, 'Select SubCategory')
        self.assertNotContains(response, 'Laptops') # Should not contain any subcategories

    def test_item_history_detail_view(self):
        response = self.client.get(reverse('design_masters:item_history_detail', args=[self.item1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'design_masters/itemmaster/item_history_detail.html')
        self.assertContains(response, f'Item Detail: {self.item1.item_code}')
        self.assertContains(response, self.item1.manf_desc)

    def test_item_history_detail_view_not_found(self):
        response = self.client.get(reverse('design_masters:item_history_detail', args=[9999])) # Non-existent ID
        self.assertEqual(response.status_code, 200) # Assumes template handles it gracefully
        self.assertContains(response, 'Item not found.')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Filters and Search:**
    -   The `id_category` dropdown uses `hx-get` to `{% url 'design_masters:subcategories_partial' %}` on `change` to update `subcategory-dropdown-container`. It also includes its own value (`hx-include="[name='category']"`).
    -   Both `id_category` (via custom event `category_changed`) and `subcategory` (via custom event `subcategory_changed`) and the Search button (`search_triggered`) trigger `hx-get` to `{% url 'design_masters:item_history_table' %}` on `change` or `click`.
    -   The `item-master-table-container` div uses `hx-trigger="load delay:100ms, category_changed from:body, subcategory_changed from:body, search_triggered from:body"` to automatically load the initial table and refresh it when any filter or search action occurs.
    -   All filter inputs (`category`, `subcategory`, `search_code`, `search_text`) are included in the `hx-get` request to the `item_history_table` endpoint using `hx-include="[name='category'], [name='subcategory'], [name='search_code'], [name='search_text']"`.
    -   A loading indicator (`#loading-indicator`) is shown during HTMX requests using `hx-indicator`.

-   **Alpine.js for UI State:**
    -   No specific Alpine.js requirements are directly identified from the ASP.NET code beyond basic HTMX-driven dynamic updates. For simple dropdown/text input filtering, HTMX handles the interactions perfectly. Alpine.js would be primarily used for more complex client-side UI states like modals (which are not in this particular ASP.NET page), dynamic visibility, or advanced form interactivity that HTMX doesn't directly support. The current implementation remains "HTMX-only" in terms of core interaction, adhering to the principle of avoiding additional JavaScript where HTMX suffices.

-   **DataTables for List Views:**
    -   The `_item_history_table.html` partial explicitly initializes DataTables on `#itemMasterTable` using `$(document).ready(function() { ... $('#itemMasterTable').DataTable({ ... }); });`. This ensures DataTables is re-applied every time the partial is loaded by HTMX.
    -   `pageLength` and `lengthMenu` are set to match the ASP.NET `PageSize=15` and provide flexibility.

-   **No Full Page Reloads:**
    -   All filtering, searching, and subcategory dropdown updates are handled via HTMX, ensuring a smooth, single-page application feel without full page refreshes, which was the primary drawback of ASP.NET PostBacks.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the `ItemHistory_TPL` module to Django. By leveraging AI-assisted automation, the transition can be significantly faster and more reliable than manual code rewriting. The new Django application will be:

-   **Modern & Maintainable:** Using Django 5.0+, fat models, thin views, and clear separation of concerns.
-   **Performant:** HTMX and DataTables provide a responsive user experience without heavy client-side JavaScript frameworks.
-   **Scalable:** Django's architecture is well-suited for growth and integrating with other services.
-   **Testable:** Comprehensive unit and integration tests ensure code quality and stability.
-   **Business-Oriented:** The focus on clear, conversational AI-driven instructions means that even non-technical stakeholders can understand and oversee the progress, ensuring the business objectives of the modernization are met.