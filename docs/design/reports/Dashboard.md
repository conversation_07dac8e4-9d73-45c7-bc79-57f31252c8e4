## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Analysis of Provided ASP.NET Code:

The provided ASP.NET code for `Dashboard.aspx` and `Dashboard.aspx.cs` is largely a placeholder. The `.aspx` file defines content regions that would typically be populated by controls, but it contains no actual UI elements (like `<PERSON>rid<PERSON>iew`, `<PERSON>Box`, `Button`) or data sources. The C# code-behind file, `Dashboard.aspx.cs`, is also minimal, containing only an empty `Page_Load` method and no custom logic, event handlers, or database interactions.

This indicates that the "Dashboard" page, as provided, serves as a container or entry point, but its actual functionality (what it displays, what data it interacts with) is either managed by master pages, user controls, or other components not included in this snippet.

Given the name "Dashboard" and the lack of explicit functionality, we will proceed by demonstrating a typical dashboard scenario: displaying a list of "Dashboard Entries" (e.g., reports, key metrics, quick links) with basic CRUD capabilities. This allows us to fulfill all requirements for a comprehensive Django migration plan, even with an empty starting point, by inferring common dashboard-like operations.

We will assume the following hypothetical entity and its properties to illustrate the migration:

*   **Entity:** `DashboardEntry`
*   **Fields:** `title` (string), `description` (text), `is_active` (boolean), `created_at` (datetime)
*   **Database Table:** `tbl_dashboard_entries`
*   **App Name:** `reports` (inferred from `Module_Design_Reports_Dashboard`)

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
*   **Observation:** The provided ASP.NET code does not explicitly define any database tables, connection strings, or SQL commands (e.g., `SqlDataSource`, `SELECT`, `INSERT`, `UPDATE`, `DELETE`).
*   **Inference:** Based on the page name "Dashboard" and common application patterns, we will infer a simple table to manage dashboard-specific content or configuration entries.
*   **Resulting Schema (Hypothetical):**
    *   **Table Name:** `tbl_dashboard_entries`
    *   **Columns:**
        *   `dashboard_entry_id` (Primary Key, Auto-increment)
        *   `entry_title` (VARCHAR)
        *   `entry_description` (TEXT)
        *   `is_active_entry` (BOOLEAN)
        *   `created_timestamp` (DATETIME)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
*   **Observation:** The provided C# code-behind file (`Dashboard.aspx.cs`) contains an empty `Page_Load` method and no other event handlers or business logic. There are no explicit CRUD operations defined.
*   **Inference:** For a typical dashboard, common backend functionality includes:
    *   **Read:** Displaying a list of dashboard entries.
    *   **Create:** Adding new dashboard entries.
    *   **Update:** Modifying existing dashboard entries.
    *   **Delete:** Removing dashboard entries.
    *   **Validation:** Basic field validation (e.g., required fields).
*   **Resulting Functionality:** We will implement standard CRUD operations for the `DashboardEntry` entity.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
*   **Observation:** The `.aspx` file uses `asp:Content` controls but does not contain any specific UI controls like `GridView`, `TextBox`, `Button`, or `DropDownList`. The only client-side script mentioned is `loadingNotifier.js`, which suggests some form of dynamic UI feedback.
*   **Inference:** We will design the Django UI based on modern web patterns suitable for a dashboard:
    *   **List View:** A table to display `DashboardEntry` records, enhanced with DataTables for client-side search, sort, and pagination.
    *   **Form Interaction:** Modals triggered by HTMX for creating, editing, and confirming deletion of entries, containing form fields (text input, text area, checkbox).
    *   **Dynamic Updates:** HTMX for loading partial content (like the table and forms) and Alpine.js for modal state management.
*   **Resulting UI Components:**
    *   DataTables for `DashboardEntry` listing.
    *   Modal forms for `DashboardEntry` creation and updates.
    *   Confirmation modal for `DashboardEntry` deletion.
    *   HTMX for all dynamic interactions.
    *   Alpine.js for modal display logic.

## Step 4: Generate Django Code

### 4.1 Models (`reports/models.py`)

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
- Name the model `DashboardEntry`.
- Define fields `title`, `description`, `is_active`, `created_at` with appropriate Django field types.
- Use `db_column` to map to the hypothetical table column names.
- Set `managed = False` and `db_table = 'tbl_dashboard_entries'` in the `Meta` class.
- Include model methods for business logic (fat model approach).

```python
from django.db import models
from django.utils import timezone

class DashboardEntry(models.Model):
    """
    Represents an entry or item displayed on the dashboard.
    This model is inferred from the 'Dashboard.aspx' context.
    """
    id = models.AutoField(db_column='dashboard_entry_id', primary_key=True)
    title = models.CharField(db_column='entry_title', max_length=255, verbose_name="Title")
    description = models.TextField(db_column='entry_description', blank=True, verbose_name="Description")
    is_active = models.BooleanField(db_column='is_active_entry', default=True, verbose_name="Active")
    created_at = models.DateTimeField(db_column='created_timestamp', auto_now_add=True, verbose_name="Created At")

    class Meta:
        managed = False  # Set to True if Django should manage this table, False if it's pre-existing.
        db_table = 'tbl_dashboard_entries'
        verbose_name = 'Dashboard Entry'
        verbose_name_plural = 'Dashboard Entries'
        ordering = ['-created_at']

    def __str__(self):
        return self.title
        
    def get_summary(self, max_length=100):
        """Returns a truncated description for display."""
        if len(self.description) > max_length:
            return self.description[:max_length] + '...'
        return self.description

    def toggle_active_status(self):
        """Toggles the active status of the dashboard entry."""
        self.is_active = not self.is_active
        self.save()

```

### 4.2 Forms (`reports/forms.py`)

**Task:** Define a Django form for user input for `DashboardEntry`.

**Instructions:**
- Create a `ModelForm` for `DashboardEntry`.
- Include editable fields (`title`, `description`, `is_active`).
- Add widgets with Tailwind CSS classes for styling.
- Implement form validation logic.

```python
from django import forms
from .models import DashboardEntry

class DashboardEntryForm(forms.ModelForm):
    """
    Form for creating and updating DashboardEntry objects.
    """
    class Meta:
        model = DashboardEntry
        fields = ['title', 'description', 'is_active']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 4}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        
    def clean_title(self):
        title = self.cleaned_data['title']
        if len(title) < 3:
            raise forms.ValidationError("Title must be at least 3 characters long.")
        return title

```

### 4.3 Views (`reports/views.py`)

**Task:** Implement CRUD operations using CBVs for `DashboardEntry`.

**Instructions:**
- Define `ListView`, `CreateView`, `UpdateView`, `DeleteView`.
- Create a `TablePartialView` for HTMX-driven table refreshes.
- Use `DashboardEntry` as the model, `DashboardEntryForm` for forms, and set appropriate `template_name` and `success_url`.
- Add success messages using `messages.success`.
- Keep views thin (5-15 lines) and move business logic to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardEntry
from .forms import DashboardEntryForm

class DashboardEntryListView(ListView):
    """
    Displays a list of DashboardEntry objects.
    The primary view for the dashboard page.
    """
    model = DashboardEntry
    template_name = 'reports/dashboardentry/list.html'
    context_object_name = 'dashboardentries'

class DashboardEntryTablePartialView(ListView):
    """
    Renders only the table portion of DashboardEntry list.
    Used by HTMX to refresh the table content.
    """
    model = DashboardEntry
    template_name = 'reports/dashboardentry/_dashboardentry_table.html'
    context_object_name = 'dashboardentries'

class DashboardEntryCreateView(CreateView):
    """
    Handles creation of new DashboardEntry objects via a modal form.
    """
    model = DashboardEntry
    form_class = DashboardEntryForm
    template_name = 'reports/dashboardentry/_dashboardentry_form.html' # This is a partial for modal
    success_url = reverse_lazy('dashboardentry_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing with the response body
                headers={
                    'HX-Trigger': 'refreshDashboardEntryList, closeModals' # Custom HTMX trigger
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Add' # For template to show "Add Dashboard Entry"
        return context

class DashboardEntryUpdateView(UpdateView):
    """
    Handles updating existing DashboardEntry objects via a modal form.
    """
    model = DashboardEntry
    form_class = DashboardEntryForm
    template_name = 'reports/dashboardentry/_dashboardentry_form.html' # This is a partial for modal
    success_url = reverse_lazy('dashboardentry_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardEntryList, closeModals'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Edit' # For template to show "Edit Dashboard Entry"
        return context

class DashboardEntryDeleteView(DeleteView):
    """
    Handles deletion of DashboardEntry objects via a confirmation modal.
    """
    model = DashboardEntry
    template_name = 'reports/dashboardentry/_dashboardentry_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('dashboardentry_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardEntryList, closeModals'
                }
            )
        return response

```

### 4.4 Templates (`reports/templates/reports/dashboardentry/`)

**Task:** Create templates for each view.

**Instructions:**
- List Template (`list.html`): Extends `core/base.html`, uses DataTables, implements HTMX for dynamic updates and form loading.
- Table Partial Template (`_dashboardentry_table.html`): The actual DataTable content loaded via HTMX.
- Form Partial Template (`_dashboardentry_form.html`): Renders the form within a modal.
- Delete Template (`_dashboardentry_confirm_delete.html`): Confirmation message for deletion.

#### `reports/templates/reports/dashboardentry/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Entries</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'dashboardentry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Entry
        </button>
    </div>
    
    <div id="dashboardentryTable-container"
         hx-trigger="load, refreshDashboardEntryList from:body"
         hx-get="{% url 'dashboardentry_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md p-4">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Entries...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on hx-trigger[closeModals] remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-auto"
             _="on htmx:afterSwap put #modalContent's scrollHeight into my.style.height
                then put 'auto' into my.style.height">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initiated globally via base.html for simple needs.
    // If complex state specific to this page is needed, define it here.
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed here as HTMX handles modal visibility
        // via class toggling and `on hx-trigger[closeModals]`
    });
</script>
{% endblock %}
```

#### `reports/templates/reports/dashboardentry/_dashboardentry_table.html`

```html
<div class="overflow-x-auto">
    <table id="dashboardentryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Title</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Active</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created At</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in dashboardentries %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ entry.title }}</td>
                <td class="py-3 px-4 text-sm text-gray-700">{{ entry.get_summary }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                 {% if entry.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ entry.is_active|yesno:"Yes,No" }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ entry.created_at|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3 transition duration-150 ease-in-out"
                        hx-get="{% url 'dashboardentry_edit' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 transition duration-150 ease-in-out"
                        hx-get="{% url 'dashboardentry_delete' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500">No dashboard entries found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX swaps the content
    $(document).ready(function() {
        $('#dashboardentryTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Enable responsive extension
            "columnDefs": [
                { "orderable": false, "targets": [5] } // Disable sorting on Actions column
            ]
        });
    });
</script>
```

#### `reports/templates/reports/dashboardentry/_dashboardentry_form.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ action }} Dashboard Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <div class="mt-1 text-sm text-red-600">
                {% for error in field.errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="pt-6 flex items-center justify-end space-x-4 border-t border-gray-200">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

#### `reports/templates/reports/dashboardentry/_dashboardentry_confirm_delete.html`

```html
<div class="p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-6">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
    </div>
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Dashboard Entry "{{ object.title }}"? This action cannot be undone.
    </p>
    <form hx-post="{% url 'dashboardentry_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`reports/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
- Create paths for list, create, update, delete, and the partial table view.
- Use appropriate naming patterns and consistent URL structure.

```python
from django.urls import path
from .views import (
    DashboardEntryListView, 
    DashboardEntryCreateView, 
    DashboardEntryUpdateView, 
    DashboardEntryDeleteView,
    DashboardEntryTablePartialView,
)

urlpatterns = [
    # Main dashboard list view
    path('dashboard-entries/', DashboardEntryListView.as_view(), name='dashboardentry_list'),
    
    # HTMX partial for refreshing the table
    path('dashboard-entries/table/', DashboardEntryTablePartialView.as_view(), name='dashboardentry_table'),

    # CRUD operations, primarily accessed via HTMX modals
    path('dashboard-entries/add/', DashboardEntryCreateView.as_view(), name='dashboardentry_add'),
    path('dashboard-entries/edit/<int:pk>/', DashboardEntryUpdateView.as_view(), name='dashboardentry_edit'),
    path('dashboard-entries/delete/<int:pk>/', DashboardEntryDeleteView.as_view(), name='dashboardentry_delete'),
]
```

### 4.6 Tests (`reports/tests.py`)

**Task:** Write tests for the model and views.

**Instructions:**
- Include comprehensive unit tests for model methods and properties.
- Add integration tests for all views (list, create, update, delete) including HTMX interactions.
- Ensure at least 80% test coverage of code.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardEntry
from django.utils import timezone
from datetime import timedelta

class DashboardEntryModelTest(TestCase):
    """
    Unit tests for the DashboardEntry model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.entry1 = DashboardEntry.objects.create(
            title='First Dashboard Report',
            description='This is a test description for the first report.',
            is_active=True,
            created_at=timezone.now() - timedelta(days=1)
        )
        cls.entry2 = DashboardEntry.objects.create(
            title='Second Important Metric',
            description='A short description for the second metric.',
            is_active=False,
            created_at=timezone.now()
        )
  
    def test_dashboard_entry_creation(self):
        """Test that DashboardEntry objects are created correctly."""
        self.assertEqual(self.entry1.title, 'First Dashboard Report')
        self.assertEqual(self.entry1.description, 'This is a test description for the first report.')
        self.assertTrue(self.entry1.is_active)
        self.assertTrue(isinstance(self.entry1.created_at, timezone.datetime))
        
        self.assertEqual(self.entry2.title, 'Second Important Metric')
        self.assertFalse(self.entry2.is_active)

    def test_title_label(self):
        """Test the verbose name for the title field."""
        field_label = self.entry1._meta.get_field('title').verbose_name
        self.assertEqual(field_label, 'Title')
        
    def test_description_label(self):
        """Test the verbose name for the description field."""
        field_label = self.entry1._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Description')

    def test_str_method(self):
        """Test the __str__ method returns the title."""
        self.assertEqual(str(self.entry1), 'First Dashboard Report')

    def test_get_summary_method(self):
        """Test the get_summary method for description truncation."""
        # Test with description shorter than max_length
        self.assertEqual(self.entry2.get_summary(max_length=50), 'A short description for the second metric.')
        # Test with description longer than max_length
        expected_summary = self.entry1.description[:20] + '...'
        self.assertEqual(self.entry1.get_summary(max_length=20), expected_summary)

    def test_toggle_active_status(self):
        """Test the toggle_active_status method."""
        initial_status = self.entry1.is_active
        self.entry1.toggle_active_status()
        self.assertFalse(self.entry1.is_active)
        self.entry1.toggle_active_status()
        self.assertEqual(self.entry1.is_active, initial_status) # Should revert to original

    def test_meta_options(self):
        """Test Meta options like db_table and verbose_name."""
        self.assertEqual(self.entry1._meta.db_table, 'tbl_dashboard_entries')
        self.assertEqual(self.entry1._meta.verbose_name, 'Dashboard Entry')
        self.assertEqual(self.entry1._meta.verbose_name_plural, 'Dashboard Entries')
        self.assertFalse(self.entry1._meta.managed)

class DashboardEntryViewsTest(TestCase):
    """
    Integration tests for DashboardEntry views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.entry1 = DashboardEntry.objects.create(
            title='Existing Report 1',
            description='Details for existing report 1',
            is_active=True,
        )
        cls.entry2 = DashboardEntry.objects.create(
            title='Existing Report 2',
            description='Details for existing report 2',
            is_active=False,
        )
    
    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()
    
    def test_list_view(self):
        """Test the DashboardEntry list view displays correctly."""
        response = self.client.get(reverse('dashboardentry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/dashboardentry/list.html')
        self.assertIn('dashboardentries', response.context)
        self.assertContains(response, self.entry1.title)
        self.assertContains(response, self.entry2.title)

    def test_table_partial_view_htmx(self):
        """Test the HTMX partial for the table content."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dashboardentry_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/dashboardentry/_dashboardentry_table.html')
        self.assertContains(response, self.entry1.title)
        self.assertContains(response, self.entry2.title)

    def test_create_view_get(self):
        """Test GET request for DashboardEntry creation form (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dashboardentry_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/dashboardentry/_dashboardentry_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Dashboard Entry') # Checks for context value 'action'

    def test_create_view_post_success(self):
        """Test POST request for successful DashboardEntry creation."""
        data = {
            'title': 'New Entry from Test',
            'description': 'A description for the new entry.',
            'is_active': True,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_add'), data, **headers)
        # HTMX successful post should return 204 No Content
        self.assertEqual(response.status_code, 204) 
        self.assertTrue(DashboardEntry.objects.filter(title='New Entry from Test').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardEntryList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """Test POST request for invalid DashboardEntry creation."""
        data = {
            'title': 'No', # Too short
            'description': 'Invalid description',
            'is_active': True,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'reports/dashboardentry/_dashboardentry_form.html')
        self.assertContains(response, 'Title must be at least 3 characters long.')
        self.assertFalse(DashboardEntry.objects.filter(title='No').exists())

    def test_update_view_get(self):
        """Test GET request for DashboardEntry update form (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dashboardentry_edit', args=[self.entry1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/dashboardentry/_dashboardentry_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.entry1)
        self.assertContains(response, 'Edit Dashboard Entry')

    def test_update_view_post_success(self):
        """Test POST request for successful DashboardEntry update."""
        data = {
            'title': 'Updated Title',
            'description': 'Updated description.',
            'is_active': False,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_edit', args=[self.entry1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.entry1.refresh_from_db()
        self.assertEqual(self.entry1.title, 'Updated Title')
        self.assertFalse(self.entry1.is_active)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardEntryList', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        """Test GET request for DashboardEntry delete confirmation (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dashboardentry_delete', args=[self.entry1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/dashboardentry/_dashboardentry_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.entry1)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post_success(self):
        """Test POST request for successful DashboardEntry deletion."""
        entry_to_delete_pk = self.entry1.pk # Use the pk of the existing entry
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_delete', args=[entry_to_delete_pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(DashboardEntry.objects.filter(pk=entry_to_delete_pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardEntryList', response.headers['HX-Trigger'])

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Content:** All CRUD operations (add, edit, delete forms) are loaded into a modal using HTMX `hx-get`. The `hx-target="#modalContent"` and `hx-trigger="click"` attributes manage this.
- **HTMX for Form Submission:** Forms inside modals use `hx-post` with `hx-swap="none"`. Upon successful submission, the server responds with `status=204` and `HX-Trigger` headers (`refreshDashboardEntryList, closeModals`).
- **HTMX for Table Refresh:** The `dashboardentryTable-container` div uses `hx-trigger="load, refreshDashboardEntryList from:body"` and `hx-get="{% url 'dashboardentry_table' %}"` to automatically load the table on page load and refresh it whenever the `refreshDashboardEntryList` custom event is triggered (after successful CRUD operations).
- **Alpine.js for UI State:** The modal's visibility (`hidden` class) is managed by Alpine.js's `_` attribute (`on click add .is-active to #modal`, `on click if event.target.id == 'modal' remove .is-active from me`). It also listens for the custom `closeModals` HTMX event to ensure the modal closes after a successful form submission.
- **DataTables for List Views:** The `_dashboardentry_table.html` partial includes the JavaScript to initialize DataTables on the `dashboardentryTable`. This ensures client-side search, sort, and pagination are enabled. The jQuery DataTables library and its dependencies would be included in the `core/base.html`.

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values from the instructions have been replaced with concrete examples based on the inferred `DashboardEntry` entity.
*   **DRY Templates:** The `_dashboardentry_table.html`, `_dashboardentry_form.html`, and `_dashboardentry_confirm_delete.html` are designed as partials to be loaded dynamically via HTMX, promoting reusability and reducing redundant HTML.
*   **Fat Model, Thin View:** Business logic, such as `get_summary` and `toggle_active_status`, resides in the `DashboardEntry` model. Views (`DashboardEntryCreateView`, `UpdateView`, `DeleteView`) are concise and primarily handle HTTP request/response flow.
*   **Comprehensive Tests:** Unit tests cover model methods and properties, while integration tests validate full view functionality, including crucial HTMX interactions and response headers.
*   **Automation Focus:** This entire plan is structured to be generated and guided by an AI, providing a clear blueprint for systematically converting and modernizing ASP.NET modules to Django. The non-technical language makes it accessible for project managers and business stakeholders to understand the transformation of their legacy application into a modern, dynamic, and maintainable Django solution.