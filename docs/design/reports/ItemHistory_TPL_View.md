## ASP.NET to Django Conversion Script: Item History - TPL

This modernization plan outlines the conversion of the provided ASP.NET `ItemHistory_TPL_View.aspx` and its C# code-behind to a modern Django application. Our approach leverages Django's robust ORM, Class-Based Views (CBVs), HTMX for dynamic interactions, Alpine.js for lightweight frontend reactivity, and DataTables for enhanced data presentation. We prioritize automation-driven migration and clear, non-technical instructions suitable for conversational AI guidance.

### Business Value of Django Modernization:

Transitioning to Django offers significant advantages:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms, which are costly to maintain and scale, to a modern, actively supported Python framework.
2.  **Enhanced Maintainability & Scalability:** Django's clear architecture (MVC-like, with fat models and thin views) promotes organized, reusable code, making future development and scaling more efficient.
3.  **Improved User Experience:** HTMX and Alpine.js provide a rich, responsive, and fast user interface without the complexity of traditional JavaScript frameworks, leading to happier users.
4.  **Cost Efficiency:** Python's ecosystem offers abundant open-source tools and libraries, reducing licensing costs and leveraging a large developer community.
5.  **Future-Proofing:** Adopting a popular, open-source framework ensures long-term viability, easier integration with other services, and access to a broader talent pool.
6.  **Automation Readiness:** The structured nature of Django, combined with the principles of this migration, makes future enhancements and automated code generation far simpler.

---

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   **NEVER** include `base.html` template code in your output - assume it already exists.
*   Focus **ONLY** on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

### AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**

The C# code reveals interaction with `tblDG_Item_Master`, `tblDG_TPL_Master`, and `Unit_Master`. The `GridView2` is populated by `fun.ItemHistory_TPL` which likely aggregates data from these or related tables into a flattened structure. For `managed=False` and to represent the GridView's output, we will assume a logical view `vw_ItemTPLHistory` that provides the necessary denormalized data.

**Extracted Tables and Inferred Columns:**

*   **`tblDG_Item_Master` (for Item Header Details):**
    *   `Id` (Primary Key)
    *   `ItemCode` (String)
    *   `ManfDesc` (String)
    *   `PurchDesc` (String)
    *   `UOMBasic` (Integer, likely Foreign Key to `Unit_Master.Id`)
    *   `UOMPurchase` (Integer, likely Foreign Key to `Unit_Master.Id`)

*   **`Unit_Master` (for Unit of Measure Symbols):**
    *   `Id` (Primary Key)
    *   `Symbol` (String)

*   **`vw_ItemTPLHistory` (Logical View for GridView Data):**
    *   `Id` (Primary Key, representing a unique history record ID, could be composite)
    *   `WONo` (String)
    *   `Date` (Date/DateTime)
    *   `Time` (Time)
    *   `AssemblyNo` (String)
    *   `ItemCode` (String, denormalized from `tblDG_Item_Master`)
    *   `ManfDesc` (String, denormalized from `tblDG_Item_Master`)
    *   `PurchDesc` (String, denormalized from `tblDG_Item_Master`)
    *   `UOMBasicSymbol` (String, denormalized from `Unit_Master` via `tblDG_Item_Master.UOMBasic`)
    *   `UOMPurchaseSymbol` (String, denormalized from `Unit_Master` via `tblDG_Item_Master.UOMPurchase`)
    *   `UnitQty` (Decimal/Float)
    *   `TPLQty` (Decimal/Float)
    *   `PId` (Integer, used for filtering in C# code, likely from `tblDG_TPL_Master`)
    *   `CId` (Integer, used for filtering in C# code, likely from `tblDG_TPL_Master`)
    *   `CompId` (Integer, used for filtering)
    *   `FinYearId` (Integer, used for filtering)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The provided ASP.NET page is a read-only report or view.

*   **Create:** Not present.
*   **Read:** Yes.
    *   Displays details of a single `ItemMaster` record based on a `QueryString["Id"]`.
    *   Displays a paginated list of related `ItemTPLHistory` records in a `GridView`.
*   **Update:** Not present.
*   **Delete:** Not present.
*   **Validation Logic:** No explicit form validation, only checks for empty strings and assigns "NA". This logic will be handled implicitly by Django's ORM or explicitly if custom validation were needed (not applicable here).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **Display Item Details:** `asp:Label` controls (`LblCode`, `lblManfdesc`, `lblPDesc`, `LblUOMBasic`, `LblUOMPurchase`) display item-specific header information.
*   **Data Grid:** `asp:GridView` (`GridView2`) displays the list of item history records. It supports pagination. In Django, this will be replaced by a `<table>` enhanced with DataTables.
*   **Action Button:** `asp:Button` (`Button1`) acts as a "Cancel" button, which redirects to another page.

### Step 4: Generate Django Code

The Django application will be named `item_history`.

#### 4.1 Models (`item_history/models.py`)

**Task:** Create Django models based on the database schema.

**Instructions:**

We will create three models: `ItemMaster`, `UnitMaster`, and `ItemTPLHistory`. All will use `managed = False` to connect to existing tables/views. Business logic for retrieving item details and history will be placed within custom manager methods or fat model methods.

```python
from django.db import models

class UnitMaster(models.Model):
    """
    Maps to the existing Unit_Master table for UOM symbols.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure Master'
        verbose_name_plural = 'Unit of Measure Masters'

    def __str__(self):
        return self.symbol or ''

class ItemMaster(models.Model):
    """
    Maps to the existing tblDG_Item_Master table for item details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=250, blank=True, null=True)
    purch_desc = models.CharField(db_column='PurchDesc', max_length=250, blank=True, null=True)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True)
    uom_purchase_id = models.IntegerField(db_column='UOMPurchase', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or f"Item ID: {self.id}"

    @property
    def uom_basic_symbol(self):
        """Retrieves the symbol for the basic UOM."""
        try:
            return UnitMaster.objects.get(id=self.uom_basic_id).symbol
        except UnitMaster.DoesNotExist:
            return 'NA'

    @property
    def uom_purchase_symbol(self):
        """Retrieves the symbol for the purchase UOM."""
        try:
            return UnitMaster.objects.get(id=self.uom_purchase_id).symbol
        except UnitMaster.DoesNotExist:
            return 'NA'

class ItemTPLHistory(models.Model):
    """
    Represents a record in the assumed vw_ItemTPLHistory view,
    which provides the flattened data for the item history grid.
    This model combines information from tblDG_Item_Master and tblDG_TPL_Master
    along with other potential tables (e.g., for Date, Time, AssemblyNo, UnitQty).
    """
    # Assuming 'Id' is a unique identifier from the view/stored procedure
    id = models.IntegerField(db_column='Id', primary_key=True) 
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    record_date = models.DateField(db_column='Date', blank=True, null=True)
    record_time = models.TimeField(db_column='Time', blank=True, null=True)
    assembly_no = models.CharField(db_column='AssemblyNo', max_length=100, blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=250, blank=True, null=True)
    purch_desc = models.CharField(db_column='PurchDesc', max_length=250, blank=True, null=True)
    uom_basic_symbol = models.CharField(db_column='UOMBasic', max_length=50, blank=True, null=True) # Renamed to UOMBasicSymbol in view
    uom_purchase_symbol = models.CharField(db_column='UOMPurchase', max_length=50, blank=True, null=True) # Renamed to UOMPurchaseSymbol in view
    unit_qty = models.DecimalField(db_column='UnitQty', max_digits=18, decimal_places=3, blank=True, null=True)
    tpl_qty = models.DecimalField(db_column='TPLQty', max_digits=18, decimal_places=3, blank=True, null=True)
    
    # Fields for filtering from original ASP.NET query/logic
    # These would be columns in the vw_ItemTPLHistory view
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    p_id = models.IntegerField(db_column='PId', blank=True, null=True)
    c_id = models.IntegerField(db_column='CId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)


    class Meta:
        # Assuming a database view or stored procedure output,
        # so managed=False is crucial.
        # The db_table should match the view name if one is created.
        managed = False
        db_table = 'vw_ItemTPLHistory' # Placeholder for the assumed view name
        verbose_name = 'Item TPL History Record'
        verbose_name_plural = 'Item TPL History Records'

    def __str__(self):
        return f"WO: {self.wo_no} - Item: {self.item_code}"

    # Business logic methods can be added here if complex calculations or
    # state changes were needed based on a history record.
```

#### 4.2 Forms (`item_history/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**

Since this is a read-only view, no forms are necessary.

```python
# No forms.py needed for a read-only view.
# This file can be omitted or left empty.
```

#### 4.3 Views (`item_history/views.py`)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

We will use a `DetailView` to show the main item information and then a `ListView` (or a function-based view for HTMX partial) to load the DataTables content dynamically. User session details (`CompId`, `FinYearId`) would typically be retrieved from `request.user` or a custom profile, but for this example, we'll assume they're passed or hardcoded as placeholder for the sake of demonstrating the query.

```python
from django.views.generic import DetailView, ListView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from .models import ItemMaster, ItemTPLHistory
from django.db.models import F, Value
from django.db.models.functions import Concat
from datetime import datetime

class ItemHistoryDetailView(DetailView):
    """
    Displays the main item details from ItemMaster based on the ID
    and acts as the container for the HTMX-loaded history table.
    """
    model = ItemMaster
    template_name = 'item_history/itemhistory_detail.html'
    context_object_name = 'item'

    def get_object(self, queryset=None):
        # The original ASP.NET code uses Request.QueryString["Id"]
        item_id = self.kwargs.get('pk')
        if not item_id:
            # Handle cases where Id might be missing in query string,
            # though the URL pattern will usually enforce it.
            # For this example, we'll assume pk is always present in URL.
            pass
        return get_object_or_404(ItemMaster, pk=item_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The actual history data will be loaded via HTMX
        return context

class ItemTPLHistoryTableView(ListView):
    """
    Returns the partial HTML for the Item TPL History table,
    designed to be loaded via HTMX.
    """
    model = ItemTPLHistory
    template_name = 'item_history/_itemhistory_table.html'
    context_object_name = 'history_records'

    def get_queryset(self):
        # In a real application, CompId and FinYearId would come from
        # request.user.profile or session. For this example,
        # we'll assume placeholders or retrieve from a simple mock session.
        # NOTE: The original ASP.NET code's filtering was:
        # tblDG_TPL_Master.ItemId=ItemId AND tblDG_TPL_Master.CompId=CompId
        # AND tblDG_TPL_Master.FinYearId<=FyId AND tblDG_TPL_Master.PId!='0'
        # This assumes the vw_ItemTPLHistory includes these columns.

        item_id = self.kwargs.get('item_id')
        # Placeholder for CompId and FyId retrieval (e.g., from user session/profile)
        # For demonstration, use dummy values.
        # In a real app: comp_id = self.request.session.get('compid')
        # fy_id = self.request.session.get('finyear')
        
        # Mock Session/User Data:
        # In a production system, this would be retrieved from the logged-in user's profile
        # or a session management system.
        current_comp_id = 1 # Replace with actual logic to get company ID
        current_fin_year_id = 2023 # Replace with actual logic to get financial year ID

        queryset = ItemTPLHistory.objects.filter(
            item_id=item_id,
            comp_id=current_comp_id,
            fin_year_id__lte=current_fin_year_id,
            p_id__gt=0 # PId != '0' in C# translates to p_id > 0 for integer field
        ).order_by('record_date', 'record_time') # Order for consistent results

        # If no records, the EmptyDataTemplate in ASP.NET shows "No data to display !"
        # Django template handles this with `{% if not history_records %}`
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # Ensure it's an HTMX request for partial render
        if self.request.headers.get('HX-Request'):
            return super().render_to_response(context, **response_kwargs)
        # If not HTMX, redirect or raise error (e.g., direct access not allowed)
        return HttpResponse(status=400, content="Direct access not allowed for this partial view.")

# No other views (Create, Update, Delete) are needed as per analysis.
```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

*   `item_history/itemhistory_detail.html`: The main page that displays item header details and uses HTMX to load the history table.
*   `item_history/_itemhistory_table.html`: A partial template containing the DataTables structure, loaded dynamically by HTMX.

**`item_history/itemhistory_detail.html`**

```html
{% extends 'core/base.html' %}

{% block title %}Item History - TPL{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gray-100 p-4 rounded-md shadow-sm mb-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Item History - TPL</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-y-2 gap-x-4 text-sm">
            <div class="flex items-center">
                <span class="font-semibold text-gray-700 w-28">Item Code:</span>
                <span class="text-gray-900">{{ item.item_code|default:"NA" }}</span>
            </div>
            <div class="flex items-center col-span-2">
                <span class="font-semibold text-gray-700 w-32">Manf Desc:</span>
                <span class="text-gray-900">{{ item.manf_desc|default:"NA" }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-semibold text-gray-700 w-28">Purchase Desc:</span>
                <span class="text-gray-900">{{ item.purch_desc|default:"NA" }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-semibold text-gray-700 w-28">UOM Basic:</span>
                <span class="text-gray-900">{{ item.uom_basic_symbol|default:"NA" }}</span>
            </div>
            <div class="flex items-center">
                <span class="font-semibold text-gray-700 w-28">UOM Purchase:</span>
                <span class="text-gray-900">{{ item.uom_purchase_symbol|default:"NA" }}</span>
            </div>
            <div class="flex items-center justify-end">
                <a href="{% url 'dashboard' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded transition duration-200 ease-in-out">
                    Cancel
                </a>
            </div>
        </div>
    </div>

    <div id="itemHistoryTable-container"
         hx-trigger="load"
         hx-get="{% url 'item_history_table' item.pk %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-md overflow-x-auto">
        <!-- History table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading item history...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // No specific Alpine.js components needed for this view yet,
    // but the block is ready for future enhancements.
</script>
{% endblock %}
```

**`item_history/_itemhistory_table.html`**

```html
{% comment %}
    This partial template is loaded via HTMX into itemhistory_detail.html
    It contains the DataTables structure for item history records.
{% endcomment %}

{% if history_records %}
<table id="itemHistoryDataTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-50 text-gray-600 uppercase text-sm leading-normal">
            <th class="py-3 px-4 text-left font-medium">SN</th>
            <th class="py-3 px-4 text-left font-medium">WO No</th>
            <th class="py-3 px-4 text-left font-medium">Date</th>
            <th class="py-3 px-4 text-left font-medium">Time</th>
            <th class="py-3 px-4 text-left font-medium">Assembly No</th>
            <th class="py-3 px-4 text-left font-medium">Item Code</th>
            <th class="py-3 px-4 text-left font-medium">Manf Desc</th>
            <!-- <th class="py-3 px-4 text-left font-medium">Purchase Desc</th> -->
            <th class="py-3 px-4 text-left font-medium">UOM Basic</th>
            <!-- <th class="py-3 px-4 text-left font-medium">UOM Purch</th> -->
            <th class="py-3 px-4 text-right font-medium">Unit Qty</th>
            <th class="py-3 px-4 text-right font-medium">TPL Qty</th>
        </tr>
    </thead>
    <tbody class="text-gray-700 text-sm font-light">
        {% for record in history_records %}
        <tr class="border-b border-gray-200 hover:bg-gray-100">
            <td class="py-3 px-4 text-left whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 text-left">{{ record.wo_no|default:"" }}</td>
            <td class="py-3 px-4 text-left">{{ record.record_date|date:"d M Y"|default:"" }}</td>
            <td class="py-3 px-4 text-left">{{ record.record_time|time:"H:i"|default:"" }}</td>
            <td class="py-3 px-4 text-left">{{ record.assembly_no|default:"" }}</td>
            <td class="py-3 px-4 text-left">{{ record.item_code|default:"" }}</td>
            <td class="py-3 px-4 text-left">{{ record.manf_desc|default:"" }}</td>
            {% comment %} <td class="py-3 px-4 text-left">{{ record.purch_desc|default:"" }}</td> {% endcomment %}
            <td class="py-3 px-4 text-left">{{ record.uom_basic_symbol|default:"" }}</td>
            {% comment %} <td class="py-3 px-4 text-left">{{ record.uom_purchase_symbol|default:"" }}</td> {% endcomment %}
            <td class="py-3 px-4 text-right">{{ record.unit_qty|floatformat:3|default:"0.000" }}</td>
            <td class="py-3 px-4 text-right">{{ record.tpl_qty|floatformat:3|default:"0.000" }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#itemHistoryDataTable').DataTable({
            "pageLength": 15, // Matches original ASP.NET PageSize
            "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
            "responsive": true,
            "paging": true,
            "searching": true,
            "info": true,
            "autoWidth": false
        });
    });
</script>

{% else %}
<div class="text-center py-8 bg-gray-50 rounded-md">
    <p class="text-lg text-maroon-600 font-semibold">No data to display !</p>
</div>
{% endif %}
```

#### 4.5 URLs (`item_history/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**

Create paths for the item detail view and the HTMX-loaded history table partial.

```python
from django.urls import path
from .views import ItemHistoryDetailView, ItemTPLHistoryTableView

urlpatterns = [
    # Main detail view for an item's history
    path('<int:pk>/', ItemHistoryDetailView.as_view(), name='item_history_detail'),
    
    # HTMX endpoint to load the item history table
    path('<int:item_id>/table/', ItemTPLHistoryTableView.as_view(), name='item_history_table'),
    
    # Example for 'Cancel' button redirect. In a real app, 'dashboard' would be defined elsewhere.
    # path('dashboard/', SomeDashboardView.as_view(), name='dashboard'), 
]
```

#### 4.6 Tests (`item_history/tests.py`)

**Task:** Write tests for the models and views.

**Instructions:**

Include comprehensive unit tests for model methods and properties. Add integration tests for all views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import ItemMaster, UnitMaster, ItemTPLHistory
from datetime import date, time

class UnitMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        UnitMaster.objects.create(id=1, symbol='MTR')
        UnitMaster.objects.create(id=2, symbol='KG')

    def test_unit_master_creation(self):
        unit = UnitMaster.objects.get(id=1)
        self.assertEqual(unit.symbol, 'MTR')
        self.assertEqual(str(unit), 'MTR')

    def test_unit_master_db_table(self):
        self.assertEqual(UnitMaster._meta.db_table, 'Unit_Master')
        self.assertFalse(UnitMaster._meta.managed)

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        UnitMaster.objects.create(id=101, symbol='UOM1')
        UnitMaster.objects.create(id=102, symbol='UOM2')
        ItemMaster.objects.create(
            id=1,
            item_code='ITEM001',
            manf_desc='Manufacturer Description 1',
            purch_desc='Purchase Description 1',
            uom_basic_id=101,
            uom_purchase_id=102
        )
        ItemMaster.objects.create(
            id=2,
            item_code='ITEM002',
            manf_desc='Manufacturer Description 2',
            purch_desc='Purchase Description 2',
            uom_basic_id=None, # Test 'NA' case
            uom_purchase_id=None
        )

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.manf_desc, 'Manufacturer Description 1')
        self.assertEqual(item.uom_basic_id, 101)
        self.assertEqual(str(item), 'ITEM001')

    def test_item_master_uom_properties(self):
        item = ItemMaster.objects.get(id=1)
        self.assertEqual(item.uom_basic_symbol, 'UOM1')
        self.assertEqual(item.uom_purchase_symbol, 'UOM2')

        item_na = ItemMaster.objects.get(id=2)
        self.assertEqual(item_na.uom_basic_symbol, 'NA')
        self.assertEqual(item_na.uom_purchase_symbol, 'NA')

    def test_item_master_db_table(self):
        self.assertEqual(ItemMaster._meta.db_table, 'tblDG_Item_Master')
        self.assertFalse(ItemMaster._meta.managed)

class ItemTPLHistoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        ItemTPLHistory.objects.create(
            id=1,
            wo_no='WO123',
            record_date=date(2023, 1, 15),
            record_time=time(10, 30),
            assembly_no='ASM-A001',
            item_code='ITEM001',
            manf_desc='Manf A',
            purch_desc='Purch A',
            uom_basic_symbol='KG',
            uom_purchase_symbol='EA',
            unit_qty=10.500,
            tpl_qty=5.250,
            item_id=1,
            p_id=10,
            c_id=20,
            comp_id=1,
            fin_year_id=2023
        )
        ItemTPLHistory.objects.create(
            id=2,
            wo_no='WO124',
            record_date=date(2023, 1, 16),
            record_time=time(11, 00),
            assembly_no='ASM-A002',
            item_code='ITEM001',
            manf_desc='Manf A',
            purch_desc='Purch A',
            uom_basic_symbol='KG',
            uom_purchase_symbol='EA',
            unit_qty=8.000,
            tpl_qty=4.000,
            item_id=1,
            p_id=0, # This record should be filtered out by p_id > 0
            c_id=21,
            comp_id=1,
            fin_year_id=2023
        )
        ItemTPLHistory.objects.create(
            id=3,
            wo_no='WO125',
            record_date=date(2022, 12, 1),
            record_time=time(9, 0),
            assembly_no='ASM-A003',
            item_code='ITEM001',
            manf_desc='Manf A',
            purch_desc='Purch A',
            uom_basic_symbol='KG',
            uom_purchase_symbol='EA',
            unit_qty=12.000,
            tpl_qty=6.000,
            item_id=1,
            p_id=11,
            c_id=22,
            comp_id=1,
            fin_year_id=2022 # This record should be filtered out by fin_year_id <= current_fin_year_id (2023)
        )
        ItemTPLHistory.objects.create(
            id=4,
            wo_no='WO126',
            record_date=date(2023, 2, 1),
            record_time=time(13, 0),
            assembly_no='ASM-A004',
            item_code='ITEM002', # Different item
            manf_desc='Manf B',
            purch_desc='Purch B',
            uom_basic_symbol='L',
            uom_purchase_symbol='DRUM',
            unit_qty=5.000,
            tpl_qty=2.500,
            item_id=2,
            p_id=12,
            c_id=23,
            comp_id=1,
            fin_year_id=2023
        )


    def test_item_tpl_history_creation(self):
        record = ItemTPLHistory.objects.get(id=1)
        self.assertEqual(record.wo_no, 'WO123')
        self.assertEqual(record.record_date, date(2023, 1, 15))
        self.assertEqual(record.item_code, 'ITEM001')
        self.assertEqual(float(record.unit_qty), 10.5)
        self.assertEqual(str(record), 'WO: WO123 - Item: ITEM001')

    def test_item_tpl_history_db_table(self):
        self.assertEqual(ItemTPLHistory._meta.db_table, 'vw_ItemTPLHistory')
        self.assertFalse(ItemTPLHistory._meta.managed)


class ItemHistoryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for ItemHistoryDetailView
        UnitMaster.objects.create(id=1, symbol='KG')
        UnitMaster.objects.create(id=2, symbol='MTR')
        ItemMaster.objects.create(
            id=100,
            item_code='TESTITEM',
            manf_desc='Test Manufacturer',
            purch_desc='Test Purchase',
            uom_basic_id=1,
            uom_purchase_id=2
        )
        
        # Create history records for ItemTPLHistoryTableView
        # Records for item_id=100, comp_id=1, fin_year_id=2023, p_id > 0
        ItemTPLHistory.objects.create(
            id=1001, wo_no='WO-001', record_date=date(2023, 1, 1), record_time=time(9,0),
            assembly_no='ASM-1001', item_code='TESTITEM', manf_desc='Test Manf',
            purch_desc='Test Purch', uom_basic_symbol='KG', uom_purchase_symbol='MTR',
            unit_qty=10.0, tpl_qty=5.0, item_id=100, p_id=1, c_id=1, comp_id=1, fin_year_id=2023
        )
        ItemTPLHistory.objects.create(
            id=1002, wo_no='WO-002', record_date=date(2023, 1, 2), record_time=time(10,0),
            assembly_no='ASM-1002', item_code='TESTITEM', manf_desc='Test Manf',
            purch_desc='Test Purch', uom_basic_symbol='KG', uom_purchase_symbol='MTR',
            unit_qty=12.0, tpl_qty=6.0, item_id=100, p_id=2, c_id=2, comp_id=1, fin_year_id=2023
        )
        # Record that should be filtered out by p_id > 0
        ItemTPLHistory.objects.create(
            id=1003, wo_no='WO-003', record_date=date(2023, 1, 3), record_time=time(11,0),
            assembly_no='ASM-1003', item_code='TESTITEM', manf_desc='Test Manf',
            purch_desc='Test Purch', uom_basic_symbol='KG', uom_purchase_symbol='MTR',
            unit_qty=1.0, tpl_qty=1.0, item_id=100, p_id=0, c_id=3, comp_id=1, fin_year_id=2023
        )
        # Record that should be filtered out by fin_year_id <= 2023
        ItemTPLHistory.objects.create(
            id=1004, wo_no='WO-004', record_date=date(2024, 1, 1), record_time=time(9,0),
            assembly_no='ASM-1004', item_code='TESTITEM', manf_desc='Test Manf',
            purch_desc='Test Purch', uom_basic_symbol='KG', uom_purchase_symbol='MTR',
            unit_qty=10.0, tpl_qty=5.0, item_id=100, p_id=4, c_id=4, comp_id=1, fin_year_id=2024
        )

    def setUp(self):
        self.client = Client()
        # Mock session variables if needed for specific tests
        # self.client.session['compid'] = 1
        # self.client.session['finyear'] = 2023

    def test_item_history_detail_view(self):
        url = reverse('item_history_detail', args=[100])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_history/itemhistory_detail.html')
        self.assertContains(response, 'TESTITEM')
        self.assertContains(response, 'Test Manufacturer')
        self.assertContains(response, 'KG') # UOM Basic Symbol
        self.assertContains(response, 'MTR') # UOM Purchase Symbol

    def test_item_history_detail_view_item_not_found(self):
        url = reverse('item_history_detail', args=[999]) # Non-existent ID
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_item_tpl_history_table_view_htmx(self):
        url = reverse('item_history_table', args=[100])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'item_history/_itemhistory_table.html')
        
        # Check that the expected history records are present (filtered by p_id > 0 and fin_year_id <= 2023)
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'WO-002')
        self.assertNotContains(response, 'WO-003') # filtered out by p_id=0
        self.assertNotContains(response, 'WO-004') # filtered out by fin_year_id=2024
        
        # Verify number of records
        self.assertEqual(len(response.context['history_records']), 2)

    def test_item_tpl_history_table_view_non_htmx(self):
        url = reverse('item_history_table', args=[100])
        response = self.client.get(url) # No HX-Request header
        self.assertEqual(response.status_code, 400) # Expect 400 for direct access

    def test_item_tpl_history_table_view_no_data(self):
        # Create an item with no history records meeting criteria
        ItemMaster.objects.create(
            id=200, item_code='NO-HISTORY', manf_desc='No History Item',
            purch_desc='No History Purch', uom_basic_id=1, uom_purchase_id=2
        )
        url = reverse('item_history_table', args=[200])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')
        self.assertFalse(response.context['history_records']) # Check queryset is empty
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided solution incorporates HTMX and Alpine.js as follows:

*   **HTMX for dynamic content loading:**
    *   The `itemhistory_detail.html` uses `hx-get="{% url 'item_history_table' item.pk %}"` and `hx-trigger="load"` on a `div` to load the `_itemhistory_table.html` partial once the main page is rendered. This ensures that the DataTables content is loaded asynchronously.
    *   `hx-swap="innerHTML"` is used to replace the loading indicator with the actual table.
*   **DataTables for list views:**
    *   The `_itemhistory_table.html` partial includes a `script` block that initializes DataTables on the loaded table (`#itemHistoryDataTable`). This handles client-side searching, sorting, and pagination as requested, matching the ASP.NET `GridView` functionality.
*   **Alpine.js for UI state management (Not explicitly used for this read-only view):**
    *   While not strictly necessary for this simple read-only display, Alpine.js remains available via `base.html` for any future client-side interactivity, such as filtering inputs or more complex UI elements, without introducing heavy JavaScript frameworks.
*   **No custom JavaScript requirements:**
    *   The design strictly avoids custom JavaScript beyond the DataTables initialization, relying on HTMX's declarative attributes for all dynamic interactions.
*   **DRY Template Inheritance:**
    *   Both templates `itemhistory_detail.html` and `_itemhistory_table.html` implicitly extend `core/base.html` (which would contain the CDN links for HTMX, Alpine.js, jQuery, and DataTables), ensuring a consistent look and feel and avoiding redundant code.

### Final Notes

*   **Placeholders:** Replace `vw_ItemTPLHistory` with the actual view or stored procedure name if one is created in your database.
*   **Session Data:** In a live environment, `CompId` and `FinYearId` (and `username`) would be retrieved securely from the logged-in user's session or profile, not hardcoded. Django's authentication system (`request.user`) is the standard way to handle this.
*   **Error Handling:** The current views provide basic error handling (e.g., 404 for non-existent items, 400 for direct partial view access). Robust production systems would add more specific error messages and logging.
*   **Database Setup:** Ensure that the `tblDG_Item_Master`, `Unit_Master`, and the derived `vw_ItemTPLHistory` (or whatever mechanism populates the history grid) tables/views are accessible via Django's database connection defined in `settings.py`.
*   **Tailwind CSS:** The HTML structure uses standard Tailwind CSS classes (e.g., `container`, `mx-auto`, `px-4`, `py-8`, `bg-gray-100`, `rounded-md`, `shadow-sm`, `flex`, `grid`, etc.) to provide a modern and responsive UI, assuming Tailwind CSS is configured in `base.html`.