## ASP.NET to Django Conversion Script: 

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

Instructions:
The ASP.NET code interacts with several tables. Based on the `SqlDataSource` and SQL queries in the C# code-behind, we've identified the following primary tables and their inferred columns:

-   **`tblGatePass_Reason`**:
    -   `Id` (Primary Key)
    -   `Reason` (Text/String)
    -   `WONo` (Boolean/Integer, e.g., 0 or 1)
    -   `Enquiry` (Boolean/Integer, e.g., 0 or 1)
    -   `Other` (Boolean/Integer, e.g., 0 or 1)

-   **`tblHR_OfficeStaff`**: (Partial schema inferred from usage)
    -   `EmpId` (Primary Key, String/Text)
    -   `Title` (Text/String)
    -   `EmployeeName` (Text/String)
    -   `EmailId1` (Text/String)
    -   `UserId` (Text/String)
    -   `DeptHead` (Text/String)
    -   `BGGroup` (Integer, likely Foreign Key to `BusinessGroup`)

-   **`tblFinancial_master`**: (Partial schema inferred from usage)
    -   `FinYearId` (Primary Key)
    -   `FinYear` (Text/String)
    -   `CompId` (Integer, Foreign Key to `tblCompany_master`)

-   **`tblCompany_master`**: (Partial schema inferred from usage)
    -   `CompId` (Primary Key)
    -   `MailServerIp` (Text/String)
    -   `ErpSysmail` (Text/String)

-   **`BusinessGroup`**: (Partial schema inferred from usage)
    -   `Id` (Primary Key)
    -   `Symbol` (Text/String)

-   **`tblGatePass_Temp`**: (Temporary staging table for "New" and "Others" entries)
    -   `Id` (Primary Key)
    -   `SessionId` (Text/String)
    -   `CompId` (Integer, Foreign Key to `tblCompany_master`)
    -   `FromDate` (Date)
    -   `FromTime` (Time)
    -   `ToTime` (Time)
    -   `Place` (Text/String)
    -   `ContactPerson` (Text/String)
    -   `ContactNo` (Text/String)
    -   `Reason` (Text/String, denormalized reason text)
    -   `Type` (Integer, Foreign Key to `tblGatePass_Reason.Id`)
    -   `TypeOf` (Integer, 1: WONo, 2: Enquiry, 3: Others)
    -   `TypeFor` (Text/String)
    -   `EmpId` (Text/String, Nullable, Foreign Key to `tblHR_OfficeStaff.EmpId`)

-   **`tblGate_Pass`**: (Master table for finalized gate passes)
    -   `Id` (Primary Key)
    -   `SysDate` (Date)
    -   `SysTime` (Time)
    -   `CompId` (Integer, Foreign Key to `tblCompany_master`)
    -   `FinYearId` (Integer, Foreign Key to `tblFinancial_master`)
    -   `SessionId` (Text/String, ASP.NET session ID)
    -   `EmpId` (Text/String, Foreign Key to `tblHR_OfficeStaff.EmpId`, for self-generated passes)
    -   `GPNo` (Text/String, auto-generated Gate Pass Number)
    -   `Authorize` (Integer, 0 or 1, authorization status)
    -   `AuthorizedBy` (Text/String, Foreign Key to `tblHR_OfficeStaff.EmpId`)
    -   `AuthorizeDate` (Date)
    -   `AuthorizeTime` (Time)

-   **`tblGatePass_Details`**: (Detail entries for finalized gate passes)
    -   `Id` (Primary Key)
    -   `MId` (Integer, Foreign Key to `tblGate_Pass.Id`)
    -   `FromDate` (Date)
    -   `FromTime` (Time)
    -   `ToTime` (Time)
    -   `Place` (Text/String)
    -   `ContactPerson` (Text/String)
    -   `ContactNo` (Text/String)
    -   `Reason` (Text/String, denormalized reason text)
    -   `Type` (Integer, Foreign Key to `tblGatePass_Reason.Id`)
    -   `TypeOf` (Integer, 1: WONo, 2: Enquiry, 3: Others)
    -   `TypeFor` (Text/String)
    -   `EmpId` (Text/String, Nullable, Foreign Key to `tblHR_OfficeStaff.EmpId`, for other employee's passes)
    -   `Feedback` (Text/String, Nullable)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

Instructions:

-   **Create (Add Temporary Gate Pass):**
    -   On "New" tab (`GridView3_RowCommand` for "add" or "add1"): Inserts into `tblGatePass_Temp` where `EmpId` is null.
    -   On "Others" tab (`GridView1_RowCommand` for "add2" or "add3"): Inserts into `tblGatePass_Temp` where `EmpId` is not null.
    -   **Validation:** Date format, `WONo` validity, Employee ID validity.

-   **Create (Finalize Gate Pass):**
    -   "New" tab "Submit" button (`BtnSubmit_Click`): Reads `tblGatePass_Temp` (where `EmpId` is null), generates a `GPNo`, inserts into `tblGate_Pass` and `tblGatePass_Details`, then clears temporary records.
    -   "Others" tab "Submit" button (`Button2_Click`): Reads `tblGatePass_Temp` (where `EmpId` is not null), generates a `GPNo`, inserts into `tblGate_Pass` and `tblGatePass_Details`, then clears temporary records.

-   **Read (Display Lists):**
    -   `loaddata()`: Populates `GridView3` from `tblGatePass_Temp` (`EmpId` is null).
    -   `loadGrid()`: Populates `GridView1` from `tblGatePass_Temp` (`EmpId` is not null).
    -   `FillGrid()`: Populates `GridView2` from `tblGate_Pass` and `tblGatePass_Details` (for view tab).
    -   `GetCompletionList()`: AJAX method for employee name autocomplete.

-   **Update:**
    -   `GridView2_RowCommand` ("Submit" command for feedback): Updates `Feedback` field in `tblGatePass_Details`.

-   **Delete:**
    -   `GridView3_RowCommand` ("Del1" command): Deletes a record from `tblGatePass_Temp`.
    -   `GridView1_RowCommand` ("Del2" command): Deletes a record from `tblGatePass_Temp`.
    -   `GridView2_RowCommand` ("Del3" command): Deletes a record from `tblGatePass_Details`, and if no more details, deletes the master `tblGate_Pass` record.

-   **Other Functionality:**
    -   Dynamic population of "Type Of" dropdown based on "Reason" selection (`DropDownList_SelectedIndexChanged` events).
    -   Email sending (commented out in the C# code, but present). This functionality will be noted for potential re-implementation using Django's email capabilities.
    -   "Print" command on `GridView2` redirects to `GatePass_Print.aspx`. This will be a separate Django view.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

Instructions:
The ASP.NET page uses various Web Forms controls to manage user interaction and data presentation.

-   **`TabContainer1` (AjaxControlToolkit.TabContainer)**: This centralizes the three main sections of the application: "New" (for self-gate passes), "Others" (for other employee gate passes), and "View" (for completed gate passes). In Django, this will be represented by a single main template (`gatepass_dashboard.html`) using HTMX to load content dynamically into tab panes.

-   **`GridView3` (System.Web.UI.WebControls.GridView)**: Used on the "New" tab to display and allow adding/deleting temporary self-gate pass entries.
    -   **Display:** Renders a list of entries with columns for date, time, contact info, reason, etc.
    -   **Input (Footer/EmptyDataTemplate):** Contains `TextBox` for date, place, contact info, reason; `DropDownList` for Type/TypeOf; `MKB.TimePicker.TimeSelector` for time; and `Button` for "Add".
    -   **Action:** `LinkButton` for "Delete".

-   **`GridView1` (System.Web.UI.WebControls.GridView)**: Used on the "Others" tab, functionally similar to `GridView3`, but also includes an `Emp Name` field with `AutoCompleteExtender` for selecting an employee.

-   **`GridView2` (System.Web.UI.WebControls.GridView)**: Used on the "View" tab to display finalized gate passes.
    -   **Display:** Shows details like Gate Pass No, Employee Name, dates, times, authorization status, and feedback.
    -   **Action:** `LinkButton` for "Delete", "Print", and a conditional `TextBox` for "Feedback" with a "Submit" `Button`.

-   **`TextBox` (System.Web.UI.WebControls.TextBox)**: Standard text input fields for various data like date, place, contact person, contact number, reason, type-for, and feedback. These will map to Django `forms.TextInput` widgets with Tailwind CSS classes.

-   **`DropDownList` (System.Web.UI.WebControls.DropDownList)**: Used for selecting 'Type' (Reason) and 'Type Of' (WONo/Enquiry/Others). These will map to Django `forms.Select` widgets. The dynamic population of 'Type Of' based on 'Type' will be handled by HTMX.

-   **`MKB.TimePicker.TimeSelector` (Custom Control)**: For entering 'From Time' and 'To Time'. In Django, this will be represented by HTML5 `type="time"` input or two `select` inputs (for hour and minute) with Alpine.js if complex logic is needed, or a simple `TextInput` with client-side masking.

-   **`Button` (System.Web.UI.WebControls.Button)**: For "Add" (temporary entries) and "Submit" (finalizing gate passes, submitting feedback). These will become standard HTML `<button>` elements with `hx-post` attributes for HTMX submissions.

-   **`LinkButton` (System.Web.UI.WebControls.LinkButton)**: For "Delete" and "Print" actions. These will become standard HTML `<a>` or `<button>` elements with `hx-get` or `hx-post` for HTMX, or `href` for navigation.

-   **`cc1:CalendarExtender` (AjaxControlToolkit.CalendarExtender)**: Provides a calendar pop-up for date selection. This will be replaced by standard HTML5 `type="date"` input, which browsers handle natively, or a simple flatpickr.js integration if cross-browser consistency or advanced features are needed (though not strictly necessary as per instructions if browser native suffices).

-   **`cc1:AutoCompleteExtender` (AjaxControlToolkit.AutoCompleteExtender)**: Used for the 'Employee Name' field. In Django, this will be handled via a dedicated HTMX endpoint that returns JSON data, used by an Alpine.js or a small custom JS snippet for autocomplete suggestions. For this case, we'll suggest a simple HTMX pattern without complex Alpine.js.

### Step 4: Generate Django Code

We will create a Django application named `gatepass`.

#### 4.1 Models

Task: Create Django models based on the identified database schema.

Instructions:
We will define models for `GatePassReason`, `Employee`, `FinancialYear`, `Company`, `BusinessGroup`, `GatePassTemp`, `GatePass`, and `GatePassDetail`. All models will use `managed = False` and `db_table` to map to the existing database.

```python
# gatepass/models.py
from django.db import models
from django.utils import timezone
from datetime import time

# Assuming these exist in your ERP system as lookup tables.
# Mapped directly to existing tables with managed=False.

class Company(models.Model):
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    mail_server_ip = models.CharField(db_column='MailServerIp', max_length=255, blank=True, null=True)
    erp_sysmail = models.CharField(db_column='ErpSysmail', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return f"Company {self.compid}"

class FinancialYear(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    compid = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Employee(models.Model):
    # EmpId is typically string, check actual DB type if int.
    # Assuming EmpId is a unique identifier string, like 'EMP001'
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50) 
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    email_id1 = models.CharField(db_column='EmailId1', max_length=255, blank=True, null=True)
    user_id = models.CharField(db_column='UserId', max_length=50, blank=True, null=True) # Likely FK to User model if user is separate
    dept_head = models.CharField(db_column='DeptHead', max_length=50, blank=True, null=True) # Likely FK to Employee.EmpId
    bg_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}. {self.employee_name} [{self.empid}]"

    @classmethod
    def get_employee_by_code(cls, employee_code_with_name):
        # Extracts employee ID from string "EmployeeName [EmpId]"
        import re
        match = re.search(r'\[(.*?)\]$', employee_code_with_name)
        if match:
            empid = match.group(1)
            try:
                return cls.objects.get(empid=empid)
            except cls.DoesNotExist:
                return None
        return None

class GatePassReason(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    reason = models.CharField(db_column='Reason', max_length=255)
    wono = models.BooleanField(db_column='WONo', default=False)
    enquiry = models.BooleanField(db_column='Enquiry', default=False)
    other = models.BooleanField(db_column='Other', default=False)

    class Meta:
        managed = False
        db_table = 'tblGatePass_Reason'
        verbose_name = 'Gate Pass Reason'
        verbose_name_plural = 'Gate Pass Reasons'

    def __str__(self):
        return self.reason

    def get_type_of_options(self):
        options = []
        if self.wono:
            options.append(('1', 'WONo'))
        if self.enquiry:
            options.append(('2', 'Enquiry'))
        if self.other:
            options.append(('3', 'Others'))
        return options

class GatePassTemp(models.Model):
    # Maps to tblGatePass_Temp, temporary staging table
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=255)
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    from_date = models.DateField(db_column='FromDate')
    from_time = models.TimeField(db_column='FromTime')
    to_time = models.TimeField(db_column='ToTime')
    place = models.CharField(db_column='Place', max_length=255)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255)
    contact_no = models.CharField(db_column='ContactNo', max_length=50)
    reason_text = models.CharField(db_column='Reason', max_length=255) # Denormalized reason text
    type = models.ForeignKey(GatePassReason, on_delete=models.DO_NOTHING, db_column='Type')
    type_of = models.IntegerField(db_column='TypeOf') # 1: WONo, 2: Enquiry, 3: Others
    type_for = models.CharField(db_column='TypeFor', max_length=255)
    emp_id = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblGatePass_Temp'
        verbose_name = 'Temporary Gate Pass Entry'
        verbose_name_plural = 'Temporary Gate Pass Entries'

    def __str__(self):
        return f"Temp GP {self.id} for {self.contact_person or self.emp_id}"

    @property
    def is_self_pass(self):
        return self.emp_id is None
        
    @property
    def type_of_display(self):
        TYPE_OF_CHOICES = {1: 'WONo', 2: 'Enquiry', 3: 'Others'}
        return TYPE_OF_CHOICES.get(self.type_of, 'N/A')

    @classmethod
    def get_self_temp_passes(cls, session_id, comp_id):
        return cls.objects.filter(session_id=session_id, comp_id=comp_id, emp_id__isnull=True).order_by('-id')

    @classmethod
    def get_other_temp_passes(cls, session_id, comp_id):
        return cls.objects.filter(session_id=session_id, comp_id=comp_id, emp_id__isnull=False).order_by('-id')

class GatePass(models.Model):
    # Maps to tblGate_Pass, master table
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now().time())
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId')
    fin_year_id = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Session ID of the creator
    emp_id = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', blank=True, null=True) # Employee generating for themselves
    gp_no = models.CharField(db_column='GPNo', max_length=10) # Auto-generated GP number
    authorize = models.IntegerField(db_column='Authorize', default=0) # 0: Not authorized, 1: Authorized
    authorized_by = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='AuthorizedBy', related_name='authorized_gatepasses', blank=True, null=True)
    authorize_date = models.DateField(db_column='AuthorizeDate', blank=True, null=True)
    authorize_time = models.TimeField(db_column='AuthorizeTime', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblGate_Pass'
        verbose_name = 'Gate Pass'
        verbose_name_plural = 'Gate Passes'

    def __str__(self):
        return f"GP No: {self.gp_no} ({self.sys_date})"
    
    @classmethod
    def get_next_gp_no(cls, comp_id, fin_year_id):
        last_gp = cls.objects.filter(comp_id=comp_id, fin_year_id=fin_year_id).order_by('-gp_no').first()
        if last_gp and last_gp.gp_no.isdigit():
            next_gp_int = int(last_gp.gp_no) + 1
        else:
            next_gp_int = 1
        return f"{next_gp_int:04d}"

    def can_delete(self):
        # A gate pass can be deleted if it's not authorized and has no feedback
        return self.authorize == 0 and not self.details.filter(feedback__isnull=False).exists()

    # Placeholder for WONo validation - this would typically involve querying another table
    # or an external service. In a real scenario, fun.CheckValidWONo needs to be replicated.
    @staticmethod
    def is_valid_wono(wono, comp_id, fin_year_id):
        # This is a placeholder. In a real ERP, this would query a WO table.
        # For demonstration, assume any non-empty string is valid.
        return bool(wono) # Or query your actual Work Order table

class GatePassDetail(models.Model):
    # Maps to tblGatePass_Details, detail entries
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(GatePass, on_delete=models.CASCADE, db_column='MId', related_name='details')
    from_date = models.DateField(db_column='FromDate')
    from_time = models.TimeField(db_column='FromTime')
    to_time = models.TimeField(db_column='ToTime')
    place = models.CharField(db_column='Place', max_length=255)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255)
    contact_no = models.CharField(db_column='ContactNo', max_length=50)
    reason_text = models.CharField(db_column='Reason', max_length=255) # Denormalized reason text
    type = models.ForeignKey(GatePassReason, on_delete=models.DO_NOTHING, db_column='Type')
    type_of = models.IntegerField(db_column='TypeOf') # 1: WONo, 2: Enquiry, 3: Others
    type_for = models.CharField(db_column='TypeFor', max_length=255)
    emp_id = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', blank=True, null=True) # Employee for whom the pass is
    feedback = models.TextField(db_column='Feedback', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblGatePass_Details'
        verbose_name = 'Gate Pass Detail'
        verbose_name_plural = 'Gate Pass Details'

    def __str__(self):
        return f"Detail {self.id} for GP {self.m_id.gp_no}"

    @property
    def employee_name_display(self):
        if self.m_id.emp_id: # Self pass
            return self.m_id.emp_id.__str__()
        elif self.emp_id: # Other employee pass
            return self.emp_id.__str__()
        return "N/A" # Should not happen in a valid scenario

    @property
    def type_of_display(self):
        TYPE_OF_CHOICES = {1: 'WONo', 2: 'Enquiry', 3: 'Others'}
        return TYPE_OF_CHOICES.get(self.type_of, 'N/A')

    @classmethod
    def get_open_feedback_details(cls, session_id, comp_id):
        # Fetches details for view tab: feedback is null AND master is for current session, not authorized
        # Original C# was `tblGate_Pass.Id=tblGatePass_Details.MId And tblGate_Pass.SessionId='" + sId + "' And tblGatePass_Details.Feedback is null AND  tblGate_Pass.CompId='" + CompId + "'   Order By tblGate_Pass.Id Desc`
        # However, the ASP.NET code shows feedback field visible/editable if `Authorize='1'`.
        # Let's adjust to fetch all related details, and UI handles feedback visibility/editability.
        # For simplicity, let's fetch all details for passes linked to the session and let the UI handle feedback.
        return cls.objects.filter(m_id__session_id=session_id, m_id__comp_id=comp_id).order_by('-m_id__id')

    def update_feedback(self, feedback_text):
        self.feedback = feedback_text
        self.save()

```

#### 4.2 Forms

Task: Define Django forms for user input.

Instructions:
We will create forms for adding new temporary gate pass entries and for updating feedback on finalized entries.

```python
# gatepass/forms.py
from django import forms
from .models import GatePassTemp, GatePassReason, GatePassDetail, Employee
from django.utils.html import format_html

class GatePassTempForm(forms.ModelForm):
    # To handle the dynamic dropdown, we'll override __init__
    # and provide initial choices from the model method.
    # The 'type_of' field will be managed via HTMX/Alpine.js on the client-side.
    TYPE_OF_CHOICES = [
        ('', 'Select Type Of'), # Default empty choice
        ('1', 'WONo'),
        ('2', 'Enquiry'),
        ('3', 'Others'),
    ]
    
    # Using CharField for 'type' and 'type_of' here to map to the integer IDs directly,
    # as the ASP.NET stores integer IDs in the temp table, but populates dropdowns from `tblGatePass_Reason`.
    # A more robust solution might use ModelChoiceField but requires careful handling of `managed=False`.
    # For now, let's use `ChoiceField` and map the integer ID to the model in the view's form_valid.
    type = forms.ChoiceField(
        choices=[], # Will be populated dynamically
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 
                                   'hx-get': "{% url 'gatepass:get_type_of_options' %}", 
                                   'hx-target': '#id_type_of_container',
                                   'hx-swap': 'innerHTML',
                                   'hx-trigger': 'change',
                                   'name': 'type_id' # Custom name for HTMX
                                }),
        label="Type"
    )
    type_of = forms.ChoiceField(
        choices=TYPE_OF_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Type Of"
    )
    
    # Employee field for "Others" tab
    employee_name_autocomplete = forms.CharField(
        max_length=255, 
        required=False, # Required only if self_pass is false
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing employee name...',
            'hx-post': "{% url 'gatepass:employee_autocomplete' %}",
            'hx-trigger': "keyup changed delay:500ms",
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'name': 'employee_name_autocomplete', # Custom name for form processing
            '@input': 'selectedEmpId = ""', # Alpine.js: clear selected ID on input change
        }),
        label="Employee Name"
    )
    # Hidden field to store selected employee ID from autocomplete
    selected_emp_id = forms.CharField(widget=forms.HiddenInput(), required=False)

    class Meta:
        model = GatePassTemp
        fields = [
            'from_date', 'from_time', 'to_time', 'place', 'contact_person',
            'contact_no', 'reason_text', 'type', 'type_of', 'type_for',
        ]
        # emp_id is handled via employee_name_autocomplete / selected_emp_id
        
        widgets = {
            'from_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'from_time': forms.TimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'time'}),
            'to_time': forms.TimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'time'}),
            'place': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_person': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'reason_text': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'type_for': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    def __init__(self, *args, **kwargs):
        self.is_self_pass_form = kwargs.pop('is_self_pass_form', True)
        super().__init__(*args, **kwargs)
        
        # Populate 'type' (reason) dropdown
        self.fields['type'].choices = [('', 'Select Reason')] + [(str(r.id), r.reason) for r in GatePassReason.objects.all()]

        if self.is_self_pass_form:
            self.fields.pop('employee_name_autocomplete')
            self.fields.pop('selected_emp_id')
        else:
            self.fields['employee_name_autocomplete'].required = True

    def clean(self):
        cleaned_data = super().clean()
        type_of = cleaned_data.get('type_of')
        type_for = cleaned_data.get('type_for')

        if type_of == '1':  # WONo
            if not type_for:
                self.add_error('type_for', 'This field is required for WONo type.')
            # Implement GatePass.is_valid_wono business logic here
            # For now, it's assumed to be valid if not empty.
            if type_for and not GatePass.is_valid_wono(type_for, 1, 1): # Placeholder comp_id, fin_year_id
                 self.add_error('type_for', 'Invalid WONo.')

        # For 'Others' form, validate employee selection
        if not self.is_self_pass_form:
            employee_name_autocomplete = cleaned_data.get('employee_name_autocomplete')
            selected_emp_id = cleaned_data.get('selected_emp_id')
            
            if not selected_emp_id: # or not Employee.objects.filter(empid=selected_emp_id).exists()
                self.add_error('employee_name_autocomplete', 'Please select a valid employee from the suggestions.')
                # The autocomplete usually handles selection by populating selected_emp_id
                # If selected_emp_id is empty, it means user typed something invalid or didn't select
                # For basic validation here, we just check if it's set.
            elif not Employee.objects.filter(empid=selected_emp_id).exists():
                self.add_error('employee_name_autocomplete', 'Selected employee ID does not exist.')
                
        return cleaned_data


class GatePassFeedbackForm(forms.ModelForm):
    class Meta:
        model = GatePassDetail
        fields = ['feedback']
        widgets = {
            'feedback': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
```

#### 4.3 Views

Task: Implement CRUD operations using CBVs.

Instructions:
We will have a main dashboard view, partial views for each tab's content, and specific HTMX endpoints for form submissions, dynamic dropdowns, and autocomplete.

```python
# gatepass/views.py
from django.views.generic import TemplateView, ListView, View
from django.views.generic.edit import FormView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.db import transaction
from django.db.models import Max

from .models import (
    GatePassTemp, GatePass, GatePassDetail, GatePassReason, Employee,
    Company, FinancialYear, BusinessGroup
)
from .forms import GatePassTempForm, GatePassFeedbackForm
import re # For employee code extraction

# Assuming current user, company, and financial year are available via session or context
# For demo purposes, we'll use dummy values for CompId, FinYearId, SessionId
# In a real ERP system, these would come from authentication/session management.
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR_ID = 1
DUMMY_SESSION_ID = 'testuser_session' # In a real app, this would be request.session.session_key or request.user.username

class GatePassDashboardView(TemplateView):
    template_name = 'gatepass/gatepass_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initial context for the dashboard, tabs loaded via HTMX
        return context

# --- HTMX Partials for Tabs ---
class SelfGatePassTabPartialView(ListView):
    model = GatePassTemp
    template_name = 'gatepass/tabs/_self_gatepass_tab.html'
    context_object_name = 'temp_self_passes'
    
    def get_queryset(self):
        # Filter for current session and company where EmpId is null (self pass)
        return GatePassTemp.get_self_temp_passes(DUMMY_SESSION_ID, DEFAULT_COMP_ID)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = GatePassTempForm(is_self_pass_form=True)
        return context

class OtherGatePassTabPartialView(ListView):
    model = GatePassTemp
    template_name = 'gatepass/tabs/_other_gatepass_tab.html'
    context_object_name = 'temp_other_passes'

    def get_queryset(self):
        # Filter for current session and company where EmpId is not null (other employee pass)
        return GatePassTemp.get_other_temp_passes(DUMMY_SESSION_ID, DEFAULT_COMP_ID)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = GatePassTempForm(is_self_pass_form=False)
        return context

class ViewGatePassTabPartialView(ListView):
    model = GatePassDetail
    template_name = 'gatepass/tabs/_view_gatepass_tab.html'
    context_object_name = 'gatepass_details'

    def get_queryset(self):
        # Filter for current session and company
        return GatePassDetail.get_open_feedback_details(DUMMY_SESSION_ID, DEFAULT_COMP_ID)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add a form for feedback if needed, but feedback is per-row.
        # It's better to render feedback form for each row in template or via HTMX on click.
        return context

# --- HTMX Endpoints for CRUD on Temporary Passes ---
class GatePassTempAddView(View):
    # Handles both 'add' and 'add1' commands from ASP.NET GridView3
    # and 'add2', 'add3' from GridView1
    def post(self, request, *args, **kwargs):
        is_self_pass_form = 'self' in request.POST.get('form_type', '') # Use a hidden input to distinguish
        form = GatePassTempForm(request.POST, is_self_pass_form=is_self_pass_form)
        
        if form.is_valid():
            with transaction.atomic():
                gate_pass_temp_instance = form.save(commit=False)
                gate_pass_temp_instance.session_id = DUMMY_SESSION_ID
                gate_pass_temp_instance.comp_id_id = DEFAULT_COMP_ID
                
                # Handle emp_id for 'others' form
                if not is_self_pass_form:
                    selected_emp_id = form.cleaned_data.get('selected_emp_id')
                    gate_pass_temp_instance.emp_id_id = selected_emp_id
                else:
                    gate_pass_temp_instance.emp_id = None # Ensure it's null for self passes

                # The ASP.NET code manually maps 'Type' (reason ID) and denormalized 'Reason' text.
                # Let's ensure the reason_text field is populated from the chosen reason.
                selected_reason_obj = GatePassReason.objects.get(id=gate_pass_temp_instance.type_id)
                gate_pass_temp_instance.reason_text = selected_reason_obj.reason
                
                gate_pass_temp_instance.save()
            messages.success(request, 'Temporary Gate Pass entry added.')
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': f"refresh{'Self' if is_self_pass_form else 'Other'}GatePassTempList"}
            )
        else:
            # Re-render the form with errors
            template_name = 'gatepass/forms/_self_gatepass_form.html' if is_self_pass_form else 'gatepass/forms/_other_gatepass_form.html'
            return render(request, template_name, {'form': form})

class GatePassTempDeleteView(DeleteView):
    model = GatePassTemp
    http_method_names = ['post'] # Only allow POST for deletion via HTMX
    
    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        obj = get_object_or_404(self.model, pk=pk, session_id=DUMMY_SESSION_ID, comp_id=DEFAULT_COMP_ID)
        return obj

    def delete(self, request, *args, **kwargs):
        is_self_pass_form = self.get_object().is_self_pass # Determine if it's self or other pass
        response = super().delete(request, *args, **kwargs)
        messages.success(request, 'Temporary Gate Pass entry deleted.')
        return HttpResponse(
            status=204,
            headers={'HX-Trigger': f"refresh{'Self' if is_self_pass_form else 'Other'}GatePassTempList"}
        )

# --- HTMX Endpoints for Finalizing Gate Passes ---
class FinalizeSelfGatePassView(View):
    # Corresponds to BtnSubmit_Click
    def post(self, request, *args, **kwargs):
        temp_passes = GatePassTemp.get_self_temp_passes(DUMMY_SESSION_ID, DEFAULT_COMP_ID)
        if not temp_passes.exists():
            messages.error(request, 'Please add entries before submitting.')
            return HttpResponse(status=400, headers={'HX-Retarget': '#messages', 'HX-Reswap': 'innerHTML'})

        try:
            with transaction.atomic():
                # Get next GP number
                current_comp = Company.objects.get(compid=DEFAULT_COMP_ID)
                current_fin_year = FinancialYear.objects.get(finyearid=DEFAULT_FIN_YEAR_ID)
                next_gp_no = GatePass.get_next_gp_no(DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)

                # Create master GatePass entry
                # Assuming current user's employee ID for self pass
                # A proper user system would link request.user to an Employee model.
                current_user_emp = Employee.objects.get(empid='EMP001') # Placeholder: map request.user to an actual Employee
                
                gate_pass_master = GatePass.objects.create(
                    sys_date=timezone.now().date(),
                    sys_time=timezone.now().time(),
                    comp_id=current_comp,
                    fin_year_id=current_fin_year,
                    session_id=DUMMY_SESSION_ID,
                    emp_id=current_user_emp, # The self-employee
                    gp_no=next_gp_no
                )

                # Move temporary entries to GatePassDetail
                for temp_pass in temp_passes:
                    GatePassDetail.objects.create(
                        m_id=gate_pass_master,
                        from_date=temp_pass.from_date,
                        from_time=temp_pass.from_time,
                        to_time=temp_pass.to_time,
                        place=temp_pass.place,
                        contact_person=temp_pass.contact_person,
                        contact_no=temp_pass.contact_no,
                        reason_text=temp_pass.reason_text,
                        type=temp_pass.type,
                        type_of=temp_pass.type_of,
                        type_for=temp_pass.type_for,
                        emp_id=None # Ensure emp_id is null for self-pass details
                    )
                temp_passes.delete() # Clear temporary records

            messages.success(request, 'Gate Pass submitted successfully!')
            # Trigger refresh for relevant tabs and main dashboard
            return HttpResponse(status=204, headers={
                'HX-Trigger': 'refreshSelfGatePassTempList,refreshViewGatePassList,showViewTab'
            })
        except Exception as e:
            messages.error(request, f'Error submitting Gate Pass: {e}')
            return HttpResponse(status=400, headers={'HX-Retarget': '#messages', 'HX-Reswap': 'innerHTML'})

class FinalizeOtherGatePassView(View):
    # Corresponds to Button2_Click
    def post(self, request, *args, **kwargs):
        temp_passes = GatePassTemp.get_other_temp_passes(DUMMY_SESSION_ID, DEFAULT_COMP_ID)
        if not temp_passes.exists():
            messages.error(request, 'Please add entries before submitting.')
            return HttpResponse(status=400, headers={'HX-Retarget': '#messages', 'HX-Reswap': 'innerHTML'})

        try:
            with transaction.atomic():
                current_comp = Company.objects.get(compid=DEFAULT_COMP_ID)
                current_fin_year = FinancialYear.objects.get(finyearid=DEFAULT_FIN_YEAR_ID)
                next_gp_no = GatePass.get_next_gp_no(DEFAULT_COMP_ID, DEFAULT_FIN_YEAR_ID)
                
                # For 'others' pass, the master GatePass entry might not have a self-employee associated directly.
                # The ASP.NET code inserted `SessionId` as `EmpId` in `tblGate_Pass` which is unusual if it's not a real `EmpId`.
                # Assuming `tblGate_Pass.EmpId` is nullable or maps to a system user for "Other" passes.
                gate_pass_master = GatePass.objects.create(
                    sys_date=timezone.now().date(),
                    sys_time=timezone.now().time(),
                    comp_id=current_comp,
                    fin_year_id=current_fin_year,
                    session_id=DUMMY_SESSION_ID,
                    emp_id=None, # This is an 'others' pass, no specific self-employee
                    gp_no=next_gp_no
                )

                for temp_pass in temp_passes:
                    GatePassDetail.objects.create(
                        m_id=gate_pass_master,
                        from_date=temp_pass.from_date,
                        from_time=temp_pass.from_time,
                        to_time=temp_pass.to_time,
                        place=temp_pass.place,
                        contact_person=temp_pass.contact_person,
                        contact_no=temp_pass.contact_no,
                        reason_text=temp_pass.reason_text,
                        type=temp_pass.type,
                        type_of=temp_pass.type_of,
                        type_for=temp_pass.type_for,
                        emp_id=temp_pass.emp_id # The employee for whom the pass is
                    )
                temp_passes.delete()

            messages.success(request, 'Gate Pass submitted successfully!')
            return HttpResponse(status=204, headers={
                'HX-Trigger': 'refreshOtherGatePassTempList,refreshViewGatePassList,showViewTab'
            })
        except Exception as e:
            messages.error(request, f'Error submitting Gate Pass: {e}')
            return HttpResponse(status=400, headers={'HX-Retarget': '#messages', 'HX-Reswap': 'innerHTML'})

# --- HTMX Endpoints for View Tab (Finalized Passes) ---
class GatePassDetailUpdateFeedbackView(FormView):
    model = GatePassDetail
    form_class = GatePassFeedbackForm
    template_name = 'gatepass/forms/_feedback_form.html' # A small partial for the feedback form

    def get_object(self):
        return get_object_or_404(self.model, pk=self.kwargs['pk'])

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['instance'] = self.get_object()
        return kwargs

    def get(self, request, *args, **kwargs):
        # Render the form for feedback within the DataTables row.
        return render(request, self.template_name, {'form': self.get_form(), 'object': self.get_object()})

    def post(self, request, *args, **kwargs):
        instance = self.get_object()
        form = self.form_class(request.POST, instance=instance)
        if form.is_valid():
            form.save()
            messages.success(request, 'Feedback updated successfully.')
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshViewGatePassList'}
            )
        else:
            return render(request, self.template_name, {'form': form, 'object': instance})


class GatePassDetailDeleteView(DeleteView):
    model = GatePassDetail
    http_method_names = ['post'] 
    template_name = 'gatepass/confirm_delete.html' # Use a generic confirmation modal

    def get_object(self, queryset=None):
        pk = self.kwargs.get('pk')
        obj = get_object_or_404(self.model, pk=pk)
        # Add permission checks if needed: e.g., only creator or authorized can delete
        return obj

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        master_pass_id = obj.m_id.id
        
        with transaction.atomic():
            obj.delete()
            # Check if master pass has no more details, then delete master
            if not GatePassDetail.objects.filter(m_id=master_pass_id).exists():
                GatePass.objects.filter(id=master_pass_id).delete()
        
        messages.success(request, 'Gate Pass entry deleted successfully.')
        return HttpResponse(
            status=204,
            headers={'HX-Trigger': 'refreshViewGatePassList'}
        )

class GatePassPrintView(View):
    # This would typically render a printable PDF or HTML view of the Gate Pass.
    # For now, it's a placeholder.
    def get(self, request, pk, d_pk, *args, **kwargs):
        gate_pass = get_object_or_404(GatePass, pk=pk)
        gate_pass_detail = get_object_or_404(GatePassDetail, pk=d_pk, m_id=gate_pass)
        
        # Implement your printing logic here.
        # This could return a PDF response, or render an HTML page suitable for printing.
        return render(request, 'gatepass/print/gatepass_print.html', {
            'gate_pass': gate_pass,
            'detail': gate_pass_detail
        })

# --- HTMX Endpoints for Dynamic Dropdowns and Autocomplete ---
class GetTypeOfOptionsView(View):
    def get(self, request, *args, **kwargs):
        reason_id = request.GET.get('type_id') # From hx-trigger="change" hx-target="#id_type_of_container"
        if reason_id:
            try:
                reason_obj = GatePassReason.objects.get(id=reason_id)
                options = reason_obj.get_type_of_options()
                
                # Render options as HTML for HTMX to swap
                html_options = '<option value="">Select Type Of</option>'
                for value, label in options:
                    html_options += f'<option value="{value}">{label}</option>'
                
                return HttpResponse(html_options)
            except GatePassReason.DoesNotExist:
                pass
        return HttpResponse('<option value="">Select Type Of</option>') # Default if no reason selected or invalid

class EmployeeAutocompleteView(View):
    def post(self, request, *args, **kwargs):
        prefix_text = request.POST.get('employee_name_autocomplete', '').strip()
        suggestions = []
        if prefix_text:
            # Query employees starting with prefix_text, for current company
            # In a real app, you might also filter by user's department/permissions
            employees = Employee.objects.filter(
                employee_name__icontains=prefix_text, 
                # comp_id=DEFAULT_COMP_ID # If employee table has comp_id
            )[:10] # Limit to 10 suggestions as in ASP.NET
            
            for emp in employees:
                suggestions.append(f'<li class="p-2 hover:bg-gray-200 cursor-pointer" '
                                   f'x-on:click="employee_name_autocomplete = \'{emp.employee_name} [{emp.empid}]\'; selectedEmpId = \'{emp.empid}\'; $refs.suggestions.innerHTML = \'\';">'
                                   f'{emp.employee_name} [{emp.empid}]</li>')
        
        # Render as HTML for HTMX to swap
        return HttpResponse(f'<ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto">{ "".join(suggestions) }</ul>')

```

#### 4.4 Templates

Task: Create templates for each view.

Instructions:
We will create a main dashboard template with tabs, and partial templates for each tab's content, which include forms and DataTables.

```html
{# gatepass/templates/gatepass/gatepass_dashboard.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: localStorage.getItem('activeGatePassTab') || 'self' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Gate Pass Management</h2>
    </div>

    {# Messages container #}
    <div id="messages" class="mb-4">
        {% if messages %}
            {% for message in messages %}
                <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% else %}bg-blue-100 text-blue-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button
                @click="activeTab = 'self'; localStorage.setItem('activeGatePassTab', 'self')"
                :class="activeTab === 'self' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                hx-get="{% url 'gatepass:self_temp_list' %}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click, load once, refreshSelfGatePassTempList from:body"
            >
                New (Self)
            </button>
            <button
                @click="activeTab = 'other'; localStorage.setItem('activeGatePassTab', 'other')"
                :class="activeTab === 'other' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                hx-get="{% url 'gatepass:other_temp_list' %}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click, refreshOtherGatePassTempList from:body"
            >
                Others
            </button>
            <button
                @click="activeTab = 'view'; localStorage.setItem('activeGatePassTab', 'view')"
                :class="activeTab === 'view' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                hx-get="{% url 'gatepass:view_list' %}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click, load once delay:100ms[activeTab === 'view'], refreshViewGatePassList from:body"
                id="viewTabButton"
            >
                View
            </button>
        </nav>
    </div>

    <div id="tab-content" class="mt-4">
        {# Content will be loaded here via HTMX #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>

    {# Modal for form/delete confirmation #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>

<script>
    // Custom HTMX trigger to show the view tab after submit
    document.body.addEventListener('showViewTab', function() {
        const viewTabButton = document.getElementById('viewTabButton');
        if (viewTabButton) {
            viewTabButton.click();
        }
    });

    // Handle messages to ensure they are visible on HTMX updates
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'messages') {
            // Re-hide messages after a delay if they are just for notification
            setTimeout(() => {
                event.detail.target.innerHTML = '';
            }, 3000); // Clear messages after 3 seconds
        }
    });
</script>
{% endblock %}

{# gatepass/templates/gatepass/tabs/_self_gatepass_tab.html #}
{# Partial template for the "New (Self)" tab #}
<div class="bg-white shadow-md rounded-lg p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">New Gate Pass Entries</h3>
    
    <div id="selfGatePassTempTable-container">
        {# Temporary self passes will be loaded here via HTMX #}
        {% include 'gatepass/partials/_self_temp_table.html' %}
    </div>

    <div class="mt-6 flex justify-center">
        <button 
            type="button" 
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'gatepass:finalize_self_pass' %}"
            hx-confirm="Are you sure you want to finalize these gate pass entries?"
            hx-swap="none"
            hx-indicator="#loading-spinner-self-submit"
        >
            Submit All Self Gate Passes
        </button>
        <span id="loading-spinner-self-submit" class="htmx-indicator ml-3 inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></span>
    </div>
</div>

{# gatepass/templates/gatepass/partials/_self_temp_table.html #}
{# Table for temporary self gate passes, loaded via HTMX #}
<table id="selfGatePassTempTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type Of</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type For</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visit Place</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in temp_self_passes %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type.reason }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type_of_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type_for }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.from_time|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.to_time|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.place }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.contact_person }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.contact_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.reason_text }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-post="{% url 'gatepass:temp_delete' obj.pk %}"
                    hx-confirm="Are you sure you want to delete this temporary entry?"
                    hx-swap="none"
                >
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="12" class="py-4 px-4 text-center text-gray-500">No temporary entries. Add one below!</td>
        </tr>
        {% endfor %}
    </tbody>
    <tfoot>
        <tr>
            <td colspan="12" class="p-4 bg-gray-50 border-t border-gray-200">
                {% include 'gatepass/forms/_self_gatepass_form.html' with form=form %}
            </td>
        </tr>
    </tfoot>
</table>

<script>
$(document).ready(function() {
    $('#selfGatePassTempTable').DataTable({
        "paging": false, {# Paging handled by HTMX if list grows too large, otherwise not needed for temp lists #}
        "searching": false,
        "info": false,
        "ordering": false
    });
});
</script>

{# gatepass/templates/gatepass/tabs/_other_gatepass_tab.html #}
{# Partial template for the "Others" tab #}
<div class="bg-white shadow-md rounded-lg p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Gate Pass Entries for Other Employees</h3>
    
    <div id="otherGatePassTempTable-container">
        {# Temporary other passes will be loaded here via HTMX #}
        {% include 'gatepass/partials/_other_temp_table.html' %}
    </div>

    <div class="mt-6 flex justify-center">
        <button 
            type="button" 
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'gatepass:finalize_other_pass' %}"
            hx-confirm="Are you sure you want to finalize these gate pass entries?"
            hx-swap="none"
            hx-indicator="#loading-spinner-other-submit"
        >
            Submit All Other Gate Passes
        </button>
        <span id="loading-spinner-other-submit" class="htmx-indicator ml-3 inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-green-500"></span>
    </div>
</div>

{# gatepass/templates/gatepass/partials/_other_temp_table.html #}
{# Table for temporary other employee gate passes, loaded via HTMX #}
<table id="otherGatePassTempTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type Of</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type For</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visit Place</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in temp_other_passes %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type.reason }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type_of_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type_for }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.from_time|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.to_time|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.emp_id.employee_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.place }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.contact_person }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.contact_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.reason_text }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-post="{% url 'gatepass:temp_delete' obj.pk %}"
                    hx-confirm="Are you sure you want to delete this temporary entry?"
                    hx-swap="none"
                >
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="13" class="py-4 px-4 text-center text-gray-500">No temporary entries. Add one below!</td>
        </tr>
        {% endfor %}
    </tbody>
    <tfoot>
        <tr>
            <td colspan="13" class="p-4 bg-gray-50 border-t border-gray-200">
                {% include 'gatepass/forms/_other_gatepass_form.html' with form=form %}
            </td>
        </tr>
    </tfoot>
</table>

<script>
$(document).ready(function() {
    $('#otherGatePassTempTable').DataTable({
        "paging": false,
        "searching": false,
        "info": false,
        "ordering": false
    });
});
</script>

{# gatepass/templates/gatepass/tabs/_view_gatepass_tab.html #}
{# Partial template for the "View" tab #}
<div class="bg-white shadow-md rounded-lg p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">View All Gate Passes</h3>
    
    <div id="viewGatePassTable-container">
        {# Finalized gate passes will be loaded here via DataTables #}
        {% include 'gatepass/partials/_view_table.html' %}
    </div>
</div>

{# gatepass/templates/gatepass/partials/_view_table.html #}
{# Table for finalized gate passes, using DataTables #}
<table id="viewGatePassTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GP No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen.Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">To Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type For</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized by</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorize Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorize Time</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Feedback</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in gatepass_details %}
        <tr id="gp_detail_{{ obj.pk }}">
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.m_id.gp_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.m_id.sys_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.from_date|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.from_time|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.to_time|time:"H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.employee_name_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type.reason }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.type_for }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.reason_text }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.m_id.authorized_by.employee_name|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.m_id.authorize_date|date:"d-m-Y"|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.m_id.authorize_time|time:"H:i"|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                {% if obj.feedback %}
                    {{ obj.feedback }}
                {% else %}
                    {% comment %} Only show feedback text input if authorized, mimicking ASP.NET logic {% endcomment %}
                    {% if obj.m_id.authorize == 1 %}
                        <div hx-target="this" hx-swap="outerHTML">
                            <button class="text-blue-600 hover:underline"
                                hx-get="{% url 'gatepass:detail_feedback' obj.pk %}">
                                Add Feedback
                            </button>
                        </div>
                    {% else %}
                        N/A
                    {% endif %}
                {% endif %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded mr-1"
                    hx-post="{% url 'gatepass:detail_delete' obj.pk %}"
                    hx-confirm="Are you sure you want to delete this Gate Pass detail?"
                    hx-swap="none"
                    {% if obj.m_id.authorize == 1 %}disabled title="Cannot delete authorized pass"{% endif %}
                >
                    Delete
                </button>
                <a href="{% url 'gatepass:print_pass' obj.m_id.pk obj.pk %}" target="_blank"
                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded">
                    Print
                </a>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="15" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#viewGatePassTable')) {
        $('#viewGatePassTable').DataTable().destroy();
    }
    $('#viewGatePassTable').DataTable({
        "pageLength": 15,
        "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
        "order": [[1, "desc"]] // Order by GP No (column index 1) descending
    });
});
</script>

{# gatepass/templates/gatepass/forms/_self_gatepass_form.html #}
<div x-data="{
    showForm: false,
    selectedTypeId: '{{ form.type.value|default:"" }}',
    typeOfOptions: [],
    updateTypeOfOptions: async function() {
        if (!this.selectedTypeId) {
            this.typeOfOptions = [];
            return;
        }
        const response = await htmx.ajax('GET', `{% url 'gatepass:get_type_of_options' %}?type_id=${this.selectedTypeId}`);
        this.typeOfOptions = Array.from(new DOMParser().parseFromString(response, 'text/html').querySelectorAll('option')).map(opt => ({ value: opt.value, text: opt.textContent }));
    }
}" x-init="updateTypeOfOptions();">
    <button @click="showForm = !showForm" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded mt-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} New Self Gate Pass Entry
    </button>
    
    <div x-show="showForm" class="mt-4 p-4 border border-gray-200 rounded-md bg-white">
        <h4 class="text-lg font-semibold mb-3">Add New Self Gate Pass Entry</h4>
        <form hx-post="{% url 'gatepass:temp_add' %}" hx-swap="none" hx-indicator="#loading-spinner-self-add" @htmx:afterRequest="if($event.detail.successful) showForm = false;">
            {% csrf_token %}
            <input type="hidden" name="form_type" value="self">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.type.label }}</label>
                    <select name="{{ form.type.name }}" id="{{ form.type.id_for_label }}"
                        class="{{ form.type.field.widget.attrs.class }}"
                        x-model="selectedTypeId"
                        @change="updateTypeOfOptions()"
                        hx-get="{% url 'gatepass:get_type_of_options' %}"
                        hx-target="#id_type_of_container"
                        hx-swap="innerHTML"
                        hx-trigger="change"
                        name="type_id" {# Custom name for HTMX request #}
                    >
                        {% for value, label in form.type.field.choices %}
                            <option value="{{ value }}" {% if value == form.type.value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                    {% if form.type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type.errors }}</p>{% endif %}
                </div>
                <div id="id_type_of_container">
                    <label for="{{ form.type_of.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.type_of.label }}</label>
                    <select name="{{ form.type_of.name }}" id="{{ form.type_of.id_for_label }}" class="{{ form.type_of.field.widget.attrs.class }}">
                        <template x-for="option in typeOfOptions" :key="option.value">
                            <option :value="option.value" x-text="option.text"></option>
                        </template>
                    </select>
                    {% if form.type_of.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_of.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.type_for.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.type_for.label }}</label>
                    {{ form.type_for }}
                    {% if form.type_for.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_for.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.from_time.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_time.label }}</label>
                    {{ form.from_time }}
                    {% if form.from_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_time.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.to_time.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_time.label }}</label>
                    {{ form.to_time }}
                    {% if form.to_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_time.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.place.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.place.label }}</label>
                    {{ form.place }}
                    {% if form.place.errors %}<p class="text-red-500 text-xs mt-1">{{ form.place.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.contact_person.label }}</label>
                    {{ form.contact_person }}
                    {% if form.contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.contact_no.label }}</label>
                    {{ form.contact_no }}
                    {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.reason_text.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.reason_text.label }}</label>
                    {{ form.reason_text }}
                    {% if form.reason_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reason_text.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6 flex items-center justify-end space-x-4">
                <button 
                    type="button" 
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                    @click="showForm = false"
                >
                    Cancel
                </button>
                <button 
                    type="submit" 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                >
                    Add
                </button>
                <span id="loading-spinner-self-add" class="htmx-indicator ml-3 inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></span>
            </div>
        </form>
    </div>
</div>


{# gatepass/templates/gatepass/forms/_other_gatepass_form.html #}
<div x-data="{
    showForm: false,
    selectedTypeId: '{{ form.type.value|default:"" }}',
    typeOfOptions: [],
    employee_name_autocomplete: '{{ form.employee_name_autocomplete.value|default:"" }}',
    selectedEmpId: '{{ form.selected_emp_id.value|default:"" }}', // Hidden field for actual employee ID
    updateTypeOfOptions: async function() {
        if (!this.selectedTypeId) {
            this.typeOfOptions = [];
            return;
        }
        const response = await htmx.ajax('GET', `{% url 'gatepass:get_type_of_options' %}?type_id=${this.selectedTypeId}`);
        this.typeOfOptions = Array.from(new DOMParser().parseFromString(response, 'text/html').querySelectorAll('option')).map(opt => ({ value: opt.value, text: opt.textContent }));
    }
}" x-init="updateTypeOfOptions();">
    <button @click="showForm = !showForm" class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded mt-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} New Other Gate Pass Entry
    </button>
    
    <div x-show="showForm" class="mt-4 p-4 border border-gray-200 rounded-md bg-white">
        <h4 class="text-lg font-semibold mb-3">Add New Other Gate Pass Entry</h4>
        <form hx-post="{% url 'gatepass:temp_add' %}" hx-swap="none" hx-indicator="#loading-spinner-other-add" @htmx:afterRequest="if($event.detail.successful) showForm = false;">
            {% csrf_token %}
            <input type="hidden" name="form_type" value="other">
            <input type="hidden" name="{{ form.selected_emp_id.name }}" x-model="selectedEmpId">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_date.label }}</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.type.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.type.label }}</label>
                    <select name="{{ form.type.name }}" id="{{ form.type.id_for_label }}"
                        class="{{ form.type.field.widget.attrs.class }}"
                        x-model="selectedTypeId"
                        @change="updateTypeOfOptions()"
                        hx-get="{% url 'gatepass:get_type_of_options' %}"
                        hx-target="#id_type_of_container_other"
                        hx-swap="innerHTML"
                        hx-trigger="change"
                        name="type_id"
                    >
                        {% for value, label in form.type.field.choices %}
                            <option value="{{ value }}" {% if value == form.type.value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                    {% if form.type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type.errors }}</p>{% endif %}
                </div>
                <div id="id_type_of_container_other">
                    <label for="{{ form.type_of.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.type_of.label }}</label>
                    <select name="{{ form.type_of.name }}" id="{{ form.type_of.id_for_label }}" class="{{ form.type_of.field.widget.attrs.class }}">
                        <template x-for="option in typeOfOptions" :key="option.value">
                            <option :value="option.value" x-text="option.text"></option>
                        </template>
                    </select>
                    {% if form.type_of.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_of.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.type_for.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.type_for.label }}</label>
                    {{ form.type_for }}
                    {% if form.type_for.errors %}<p class="text-red-500 text-xs mt-1">{{ form.type_for.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.from_time.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_time.label }}</label>
                    {{ form.from_time }}
                    {% if form.from_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_time.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.to_time.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.to_time.label }}</label>
                    {{ form.to_time }}
                    {% if form.to_time.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_time.errors }}</p>{% endif %}
                </div>
                <div x-data="{ employee_name_autocomplete: $refs.employee_name_autocomplete_input.value }" class="relative">
                    <label for="{{ form.employee_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.employee_name_autocomplete.label }}</label>
                    <input type="text" 
                        name="{{ form.employee_name_autocomplete.name }}" 
                        id="{{ form.employee_name_autocomplete.id_for_label }}" 
                        class="{{ form.employee_name_autocomplete.field.widget.attrs.class }}"
                        x-model="employee_name_autocomplete"
                        x-ref="employee_name_autocomplete_input"
                        placeholder="{{ form.employee_name_autocomplete.field.widget.attrs.placeholder }}"
                        hx-post="{{ form.employee_name_autocomplete.field.widget.attrs.hx_post }}"
                        hx-trigger="{{ form.employee_name_autocomplete.field.widget.attrs.hx_trigger }}"
                        hx-target="{{ form.employee_name_autocomplete.field.widget.attrs.hx_target }}"
                        hx-swap="{{ form.employee_name_autocomplete.field.widget.attrs.hx_swap }}"
                        autocomplete="{{ form.employee_name_autocomplete.field.widget.attrs.autocomplete }}"
                        @input="selectedEmpId = ''" {# clear hidden ID if input changes #}
                    >
                    <div id="employee-suggestions"></div> {# Autocomplete suggestions will appear here #}
                    {% if form.employee_name_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ form.employee_name_autocomplete.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.place.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.place.label }}</label>
                    {{ form.place }}
                    {% if form.place.errors %}<p class="text-red-500 text-xs mt-1">{{ form.place.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_person.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.contact_person.label }}</label>
                    {{ form.contact_person }}
                    {% if form.contact_person.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_person.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.contact_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.contact_no.label }}</label>
                    {{ form.contact_no }}
                    {% if form.contact_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.contact_no.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.reason_text.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.reason_text.label }}</label>
                    {{ form.reason_text }}
                    {% if form.reason_text.errors %}<p class="text-red-500 text-xs mt-1">{{ form.reason_text.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6 flex items-center justify-end space-x-4">
                <button 
                    type="button" 
                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                    @click="showForm = false"
                >
                    Cancel
                </button>
                <button 
                    type="submit" 
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                >
                    Add
                </button>
                <span id="loading-spinner-other-add" class="htmx-indicator ml-3 inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></span>
            </div>
        </form>
    </div>
</div>

{# gatepass/templates/gatepass/forms/_feedback_form.html #}
<form hx-post="{% url 'gatepass:detail_feedback' object.pk %}" hx-swap="outerHTML">
    {% csrf_token %}
    {{ form.feedback }}
    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mt-2">Submit</button>
</form>

{# gatepass/templates/gatepass/confirm_delete.html #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="mb-4">Are you sure you want to delete this Gate Pass entry?</p>
    <div class="flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'gatepass:detail_delete' object.pk %}"
            hx-swap="none"
            _="on click remove .is-active from #modal"
        >
            Delete
        </button>
    </div>
</div>

{# gatepass/templates/gatepass/print/gatepass_print.html #}
<!DOCTYPE html>
<html>
<head>
    <title>Gate Pass Print</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { width: 800px; margin: 0 auto; border: 1px solid #ccc; padding: 20px; }
        h1 { text-align: center; margin-bottom: 20px; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gate Pass Details</h1>
        <table>
            <tr>
                <th>Gate Pass No:</th>
                <td>{{ gate_pass.gp_no }}</td>
                <th>Generated Date:</th>
                <td>{{ gate_pass.sys_date|date:"d-m-Y" }} {{ gate_pass.sys_time|time:"H:i" }}</td>
            </tr>
            <tr>
                <th>Employee (Self):</th>
                <td>{{ gate_pass.emp_id.employee_name|default:"N/A" }}</td>
                <th>Created By (Session):</th>
                <td>{{ gate_pass.session_id }}</td>
            </tr>
            <tr>
                <th>Authorized By:</th>
                <td>{{ gate_pass.authorized_by.employee_name|default:"Not Authorized" }}</td>
                <th>Authorization Date/Time:</th>
                <td>{% if gate_pass.authorize_date %}{{ gate_pass.authorize_date|date:"d-m-Y" }} {{ gate_pass.authorize_time|time:"H:i" }}{% else %}N/A{% endif %}</td>
            </tr>
        </table>

        <h2>Gate Pass Entry Details</h2>
        <table>
            <thead>
                <tr>
                    <th>For Date</th>
                    <th>From Time</th>
                    <th>To Time</th>
                    <th>Employee (Other)</th>
                    <th>Visit Place</th>
                    <th>Contact Person</th>
                    <th>Contact No</th>
                    <th>Reason</th>
                    <th>Type For</th>
                    <th>Feedback</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ detail.from_date|date:"d-m-Y" }}</td>
                    <td>{{ detail.from_time|time:"H:i" }}</td>
                    <td>{{ detail.to_time|time:"H:i" }}</td>
                    <td>{{ detail.emp_id.employee_name|default:"N/A" }}</td>
                    <td>{{ detail.place }}</td>
                    <td>{{ detail.contact_person }}</td>
                    <td>{{ detail.contact_no }}</td>
                    <td>{{ detail.reason_text }}</td>
                    <td>{{ detail.type_for }}</td>
                    <td>{{ detail.feedback|default:"N/A" }}</td>
                </tr>
            </tbody>
        </table>

        <p style="text-align: center; margin-top: 50px;">This is an auto-generated document. No signature required.</p>
    </div>
</body>
</html>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

Instructions:
We will create a `urls.py` file within the `gatepass` app and include it in the project's main `urls.py`.

```python
# gatepass/urls.py
from django.urls import path
from .views import (
    GatePassDashboardView,
    SelfGatePassTabPartialView,
    OtherGatePassTabPartialView,
    ViewGatePassTabPartialView,
    GatePassTempAddView,
    GatePassTempDeleteView,
    FinalizeSelfGatePassView,
    FinalizeOtherGatePassView,
    GatePassDetailUpdateFeedbackView,
    GatePassDetailDeleteView,
    GatePassPrintView,
    GetTypeOfOptionsView,
    EmployeeAutocompleteView,
)

app_name = 'gatepass'

urlpatterns = [
    path('', GatePassDashboardView.as_view(), name='dashboard'),
    
    # HTMX partials for tabs
    path('self-temp-list/', SelfGatePassTabPartialView.as_view(), name='self_temp_list'),
    path('other-temp-list/', OtherGatePassTabPartialView.as_view(), name='other_temp_list'),
    path('view-list/', ViewGatePassTabPartialView.as_view(), name='view_list'),

    # Temporary Gate Pass CRUD
    path('temp/add/', GatePassTempAddView.as_view(), name='temp_add'),
    path('temp/delete/<int:pk>/', GatePassTempDeleteView.as_view(), name='temp_delete'),

    # Finalize Gate Pass
    path('finalize/self/', FinalizeSelfGatePassView.as_view(), name='finalize_self_pass'),
    path('finalize/other/', FinalizeOtherGatePassView.as_view(), name='finalize_other_pass'),

    # Finalized Gate Pass Details Operations
    path('detail/feedback/<int:pk>/', GatePassDetailUpdateFeedbackView.as_view(), name='detail_feedback'),
    path('detail/delete/<int:pk>/', GatePassDetailDeleteView.as_view(), name='detail_delete'),
    path('print/<int:pk>/<int:d_pk>/', GatePassPrintView.as_view(), name='print_pass'), # pk is MId, d_pk is Detail Id

    # Dynamic UI elements
    path('get-type-of-options/', GetTypeOfOptionsView.as_view(), name='get_type_of_options'),
    path('employee-autocomplete/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

```

**Project `urls.py` integration:**
Add `path('gatepass/', include('gatepass.urls', namespace='gatepass')),` to your main `urls.py`.

#### 4.6 Tests

Task: Write tests for the model and views.

Instructions:
We will include comprehensive unit tests for model methods and integration tests for all relevant views.

```python
# gatepass/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, time

from .models import (
    Company, FinancialYear, BusinessGroup, Employee, GatePassReason,
    GatePassTemp, GatePass, GatePassDetail
)

# Dummy values for testing, mimicking the C# session variables
TEST_COMP_ID = 1
TEST_FIN_YEAR_ID = 1
TEST_SESSION_ID = 'test_session_abc'
TEST_EMP_ID_SELF = 'EMP001' # For self-generated passes
TEST_EMP_ID_OTHER = 'EMP002' # For other employee passes
TEST_DEPT_HEAD_EMP_ID = 'EMP003' # For department head email if re-implemented

class GatePassModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data
        cls.company = Company.objects.create(compid=TEST_COMP_ID, mail_server_ip='127.0.0.1', erp_sysmail='<EMAIL>')
        cls.fin_year = FinancialYear.objects.create(finyearid=TEST_FIN_YEAR_ID, fin_year='2023-2024', compid=cls.company)
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='BG1')
        cls.employee_self = Employee.objects.create(empid=TEST_EMP_ID_SELF, title='Mr', employee_name='John Doe', bg_group=cls.business_group)
        cls.employee_other = Employee.objects.create(empid=TEST_EMP_ID_OTHER, title='Ms', employee_name='Jane Smith', bg_group=cls.business_group)
        cls.employee_dept_head = Employee.objects.create(empid=TEST_DEPT_HEAD_EMP_ID, title='Dr', employee_name='Head Dept', email_id1='<EMAIL>', bg_group=cls.business_group)

        # Gate Pass Reasons
        cls.reason_wono = GatePassReason.objects.create(id=1, reason='Client Visit', wono=True, enquiry=False, other=False)
        cls.reason_enquiry = GatePassReason.objects.create(id=2, reason='Site Visit', wono=False, enquiry=True, other=False)
        cls.reason_other = GatePassReason.objects.create(id=3, reason='Personal', wono=False, enquiry=False, other=True)
        cls.reason_all = GatePassReason.objects.create(id=4, reason='Training', wono=True, enquiry=True, other=True)


    def test_gate_pass_reason_get_type_of_options(self):
        self.assertEqual(self.reason_wono.get_type_of_options(), [('1', 'WONo')])
        self.assertEqual(self.reason_enquiry.get_type_of_options(), [('2', 'Enquiry')])
        self.assertEqual(self.reason_other.get_type_of_options(), [('3', 'Others')])
        self.assertEqual(self.reason_all.get_type_of_options(), [('1', 'WONo'), ('2', 'Enquiry'), ('3', 'Others')])
        
    def test_employee_get_employee_by_code(self):
        employee = Employee.get_employee_by_code(f'John Doe [{TEST_EMP_ID_SELF}]')
        self.assertEqual(employee, self.employee_self)
        self.assertIsNone(Employee.get_employee_by_code('Non Existent [EMP999]'))
        self.assertIsNone(Employee.get_employee_by_code('No Code'))

    def test_gate_pass_temp_is_self_pass(self):
        temp_self = GatePassTemp.objects.create(
            session_id=TEST_SESSION_ID, comp_id=self.company, from_date=date.today(),
            from_time=time(9,0), to_time=time(17,0), place='Office', contact_person='Self',
            contact_no='123', reason_text='Self Reason', type=self.reason_wono, type_of=1, type_for='WO123',
            emp_id=None # Self pass
        )
        temp_other = GatePassTemp.objects.create(
            session_id=TEST_SESSION_ID, comp_id=self.company, from_date=date.today(),
            from_time=time(9,0), to_time=time(17,0), place='Office', contact_person='Other',
            contact_no='456', reason_text='Other Reason', type=self.reason_wono, type_of=1, type_for='WO456',
            emp_id=self.employee_other # Other pass
        )
        self.assertTrue(temp_self.is_self_pass)
        self.assertFalse(temp_other.is_self_pass)

    def test_gate_pass_get_next_gp_no(self):
        # Initial GP No
        self.assertEqual(GatePass.get_next_gp_no(TEST_COMP_ID, TEST_FIN_YEAR_ID), '0001')
        
        # Create one gate pass
        GatePass.objects.create(
            comp_id=self.company, fin_year_id=self.fin_year, session_id=TEST_SESSION_ID,
            emp_id=self.employee_self, gp_no='0001', sys_date=date.today(), sys_time=time(10,0)
        )
        self.assertEqual(GatePass.get_next_gp_no(TEST_COMP_ID, TEST_FIN_YEAR_ID), '0002')

    def test_gate_pass_is_valid_wono(self):
        self.assertTrue(GatePass.is_valid_wono('WO123', TEST_COMP_ID, TEST_FIN_YEAR_ID))
        self.assertFalse(GatePass.is_valid_wono('', TEST_COMP_ID, TEST_FIN_YEAR_ID)) # Assuming empty is invalid

    def test_gate_pass_detail_update_feedback(self):
        gp = GatePass.objects.create(
            comp_id=self.company, fin_year_id=self.fin_year, session_id=TEST_SESSION_ID,
            emp_id=self.employee_self, gp_no='0001', sys_date=date.today(), sys_time=time(10,0)
        )
        detail = GatePassDetail.objects.create(
            m_id=gp, from_date=date.today(), from_time=time(9,0), to_time=time(17,0),
            place='Site', contact_person='Client', contact_no='789', reason_text='Site Visit',
            type=self.reason_enquiry, type_of=2, type_for='Enquiry123', emp_id=None
        )
        self.assertIsNone(detail.feedback)
        detail.update_feedback('Satisfactory visit.')
        detail.refresh_from_db()
        self.assertEqual(detail.feedback, 'Satisfactory visit.')

class GatePassViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common data needed for all view tests
        cls.company = Company.objects.create(compid=TEST_COMP_ID, mail_server_ip='127.0.0.1', erp_sysmail='<EMAIL>')
        cls.fin_year = FinancialYear.objects.create(finyearid=TEST_FIN_YEAR_ID, fin_year='2023-2024', compid=cls.company)
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='BG1')
        cls.employee_self = Employee.objects.create(empid=TEST_EMP_ID_SELF, title='Mr', employee_name='John Doe', bg_group=cls.business_group, user_id=TEST_SESSION_ID)
        cls.employee_other = Employee.objects.create(empid=TEST_EMP_ID_OTHER, title='Ms', employee_name='Jane Smith', bg_group=cls.business_group)
        cls.employee_dept_head = Employee.objects.create(empid=TEST_DEPT_HEAD_EMP_ID, title='Dr', employee_name='Head Dept', email_id1='<EMAIL>', bg_group=cls.business_group)
        cls.reason_wono = GatePassReason.objects.create(id=1, reason='Client Visit', wono=True, enquiry=False, other=False)
        cls.reason_enquiry = GatePassReason.objects.create(id=2, reason='Site Visit', wono=False, enquiry=True, other=False)

    def setUp(self):
        self.client = Client()
        # Ensure temporary data is clean before each test
        GatePassTemp.objects.all().delete()
        GatePass.objects.all().delete()
        GatePassDetail.objects.all().delete()

    def test_dashboard_view(self):
        response = self.client.get(reverse('gatepass:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gatepass/gatepass_dashboard.html')

    def test_self_gate_pass_tab_partial_view(self):
        response = self.client.get(reverse('gatepass:self_temp_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gatepass/tabs/_self_gatepass_tab.html')
        self.assertContains(response, 'New Gate Pass Entries')

    def test_other_gate_pass_tab_partial_view(self):
        response = self.client.get(reverse('gatepass:other_temp_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gatepass/tabs/_other_gatepass_tab.html')
        self.assertContains(response, 'Gate Pass Entries for Other Employees')

    def test_view_gate_pass_tab_partial_view(self):
        response = self.client.get(reverse('gatepass:view_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gatepass/tabs/_view_gatepass_tab.html')
        self.assertContains(response, 'View All Gate Passes')

    def test_gate_pass_temp_add_self_pass_valid_post(self):
        form_data = {
            'form_type': 'self',
            'from_date': date.today().strftime('%Y-%m-%d'),
            'from_time': '09:00',
            'to_time': '17:00',
            'place': 'Client Office',
            'contact_person': 'Mr. Client',
            'contact_no': '9876543210',
            'reason_text': 'Meeting',
            'type_id': self.reason_wono.id,
            'type_of': '1', # WONo
            'type_for': 'WO-XYZ-123',
        }
        response = self.client.post(reverse('gatepass:temp_add'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success without content
        self.assertEqual(GatePassTemp.objects.count(), 1)
        temp_pass = GatePassTemp.objects.first()
        self.assertIsNone(temp_pass.emp_id)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSelfGatePassTempList')

    def test_gate_pass_temp_add_other_pass_valid_post(self):
        form_data = {
            'form_type': 'other',
            'from_date': date.today().strftime('%Y-%m-%d'),
            'from_time': '10:00',
            'to_time': '16:00',
            'place': 'Vendor Site',
            'contact_person': 'Mr. Vendor',
            'contact_no': '1234567890',
            'reason_text': 'Inspection',
            'type_id': self.reason_enquiry.id,
            'type_of': '2', # Enquiry
            'type_for': 'ENQ-ABC-456',
            'employee_name_autocomplete': f'{self.employee_other.employee_name} [{self.employee_other.empid}]',
            'selected_emp_id': self.employee_other.empid,
        }
        response = self.client.post(reverse('gatepass:temp_add'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GatePassTemp.objects.count(), 1)
        temp_pass = GatePassTemp.objects.first()
        self.assertEqual(temp_pass.emp_id, self.employee_other)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOtherGatePassTempList')

    def test_gate_pass_temp_add_invalid_post(self):
        form_data = { # Missing required fields
            'form_type': 'self',
            'from_date': date.today().strftime('%Y-%m-%d'),
        }
        response = self.client.post(reverse('gatepass:temp_add'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Returns form with errors
        self.assertContains(response, 'This field is required')
        self.assertEqual(GatePassTemp.objects.count(), 0)

    def test_gate_pass_temp_delete_view(self):
        temp_pass = GatePassTemp.objects.create(
            session_id=TEST_SESSION_ID, comp_id=self.company, from_date=date.today(),
            from_time=time(9,0), to_time=time(17,0), place='Office', contact_person='Self',
            contact_no='123', reason_text='Self Reason', type=self.reason_wono, type_of=1, type_for='WO123',
            emp_id=None
        )
        response = self.client.post(reverse('gatepass:temp_delete', args=[temp_pass.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GatePassTemp.objects.count(), 0)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSelfGatePassTempList')

    def test_finalize_self_gate_pass_view(self):
        # Create a temporary self pass
        GatePassTemp.objects.create(
            session_id=TEST_SESSION_ID, comp_id=self.company, from_date=date.today(),
            from_time=time(9,0), to_time=time(17,0), place='Client Office', contact_person='Mr. Client',
            contact_no='9876543210', reason_text='Meeting', type=self.reason_wono, type_of=1, type_for='WO-XYZ-123',
            emp_id=None
        )
        response = self.client.post(reverse('gatepass:finalize_self_pass'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GatePassTemp.objects.count(), 0)
        self.assertEqual(GatePass.objects.count(), 1)
        self.assertEqual(GatePassDetail.objects.count(), 1)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSelfGatePassTempList,refreshViewGatePassList,showViewTab')
    
    def test_finalize_other_gate_pass_view(self):
        # Create a temporary other pass
        GatePassTemp.objects.create(
            session_id=TEST_SESSION_ID, comp_id=self.company, from_date=date.today(),
            from_time=time(10,0), to_time=time(16,0), place='Vendor Site', contact_person='Mr. Vendor',
            contact_no='1234567890', reason_text='Inspection', type=self.reason_enquiry, type_of=2, type_for='ENQ-ABC-456',
            emp_id=self.employee_other
        )
        response = self.client.post(reverse('gatepass:finalize_other_pass'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GatePassTemp.objects.count(), 0)
        self.assertEqual(GatePass.objects.count(), 1)
        self.assertEqual(GatePassDetail.objects.count(), 1)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOtherGatePassTempList,refreshViewGatePassList,showViewTab')

    def test_gate_pass_detail_update_feedback_get(self):
        gp = GatePass.objects.create(
            comp_id=self.company, fin_year_id=self.fin_year, session_id=TEST_SESSION_ID,
            emp_id=self.employee_self, gp_no='0001', sys_date=date.today(), sys_time=time(10,0)
        )
        detail = GatePassDetail.objects.create(
            m_id=gp, from_date=date.today(), from_time=time(9,0), to_time=time(17,0),
            place='Site', contact_person='Client', contact_no='789', reason_text='Site Visit',
            type=self.reason_enquiry, type_of=2, type_for='Enquiry123', emp_id=None
        )
        response = self.client.get(reverse('gatepass:detail_feedback', args=[detail.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'gatepass/forms/_feedback_form.html')
        self.assertContains(response, 'name="feedback"')

    def test_gate_pass_detail_update_feedback_post(self):
        gp = GatePass.objects.create(
            comp_id=self.company, fin_year_id=self.fin_year, session_id=TEST_SESSION_ID,
            emp_id=self.employee_self, gp_no='0001', sys_date=date.today(), sys_time=time(10,0)
        )
        detail = GatePassDetail.objects.create(
            m_id=gp, from_date=date.today(), from_time=time(9,0), to_time=time(17,0),
            place='Site', contact_person='Client', contact_no='789', reason_text='Site Visit',
            type=self.reason_enquiry, type_of=2, type_for='Enquiry123', emp_id=None
        )
        form_data = {'feedback': 'Visited and completed.'}
        response = self.client.post(reverse('gatepass:detail_feedback', args=[detail.pk]), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        detail.refresh_from_db()
        self.assertEqual(detail.feedback, 'Visited and completed.')
        self.assertEqual(response.headers['HX-Trigger'], 'refreshViewGatePassList')

    def test_gate_pass_detail_delete_view(self):
        gp = GatePass.objects.create(
            comp_id=self.company, fin_year_id=self.fin_year, session_id=TEST_SESSION_ID,
            emp_id=self.employee_self, gp_no='0001', sys_date=date.today(), sys_time=time(10,0)
        )
        detail1 = GatePassDetail.objects.create(
            m_id=gp, from_date=date.today(), from_time=time(9,0), to_time=time(17,0),
            place='Site', contact_person='Client', contact_no='789', reason_text='Site Visit',
            type=self.reason_enquiry, type_of=2, type_for='Enquiry123', emp_id=None
        )
        detail2 = GatePassDetail.objects.create( # Another detail for the same master pass
            m_id=gp, from_date=date.today(), from_time=time(10,0), to_time=time(18,0),
            place='Shop', contact_person='Vendor', contact_no='111', reason_text='Purchase',
            type=self.reason_wono, type_of=1, type_for='PO123', emp_id=None
        )
        
        # Delete first detail
        response = self.client.post(reverse('gatepass:detail_delete', args=[detail1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GatePassDetail.objects.count(), 1) # Only detail2 remains
        self.assertEqual(GatePass.objects.count(), 1) # Master pass still exists
        self.assertEqual(response.headers['HX-Trigger'], 'refreshViewGatePassList')

        # Delete second detail, should delete master pass too
        response = self.client.post(reverse('gatepass:detail_delete', args=[detail2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(GatePassDetail.objects.count(), 0)
        self.assertEqual(GatePass.objects.count(), 0) # Master pass should be deleted
        self.assertEqual(response.headers['HX-Trigger'], 'refreshViewGatePassList')

    def test_get_type_of_options_view(self):
        response = self.client.get(reverse('gatepass:get_type_of_options'), {'type_id': self.reason_all.id})
        self.assertEqual(response.status_code, 200)
        self.assertIn('<option value="1">WONo</option>', response.content.decode())
        self.assertIn('<option value="2">Enquiry</option>', response.content.decode())
        self.assertIn('<option value="3">Others</option>', response.content.decode())

    def test_employee_autocomplete_view(self):
        response = self.client.post(reverse('gatepass:employee_autocomplete'), {'employee_name_autocomplete': 'John'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn(f'John Doe [{TEST_EMP_ID_SELF}]', response.content.decode())
        self.assertNotIn(f'Jane Smith [{TEST_EMP_ID_OTHER}]', response.content.decode())
```

### Step 5: HTMX and Alpine.js Integration

Instructions:
The Django templates are designed with HTMX for dynamic content loading, form submissions, and table refreshes. Alpine.js is used for managing UI state like tab selection, form visibility, and autocomplete dropdowns. DataTables is integrated into the `_view_table.html` partial for enhanced data presentation.

-   **Tab Navigation:** Clicks on tab buttons (`New (Self)`, `Others`, `View`) trigger `hx-get` requests to corresponding partial views (`self_temp_list`, `other_temp_list`, `view_list`). `hx-target="#tab-content"` ensures the content is swapped into the main content area. `Alpine.js` manages the `activeTab` state and `localStorage` to persist the active tab on refresh.
-   **Form Submission (Add/Submit):** The "Add" buttons within the temporary pass forms (`_self_gatepass_form.html`, `_other_gatepass_form.html`) use `hx-post` to submit data to `temp_add` view. The "Submit" buttons for finalizing passes use `hx-post` to `finalize_self_pass` or `finalize_other_pass`. All these submissions use `hx-swap="none"` and rely on `HX-Trigger` headers from the backend to refresh relevant lists.
-   **Dynamic Dropdowns:** The "Type" dropdown (`GatePassTempForm`) uses `hx-get` to `get_type_of_options` with `hx-target` pointing to the `type_of` dropdown container. This dynamically loads the correct "Type Of" options based on the selected "Type".
-   **Autocomplete:** The "Employee Name" field in the "Others" form uses `hx-post` to `employee_autocomplete` on `keyup changed delay:500ms`. The response (`<li>` items) is swapped into a `div` below the input, allowing users to click a suggestion which populates the input and a hidden `selected_emp_id` field using Alpine.js.
-   **DataTables:** The `_view_table.html` partial includes a `<script>` block that initializes `DataTables` on the `viewGatePassTable`. This script runs every time the partial is loaded via HTMX, ensuring the table has full searching, sorting, and pagination capabilities.
-   **Delete Confirmation:** Delete buttons on list views use `hx-post` to the respective delete views (`temp_delete`, `detail_delete`). For `detail_delete`, the confirmation happens via `hx-confirm` attribute directly, preventing the need for a separate modal.
-   **Feedback Update:** In `_view_table.html`, if feedback is not present and the pass is authorized, a "Add Feedback" button appears. This button uses `hx-get` to load the `_feedback_form.html` partial directly into the table cell (`hx-target="this" hx-swap="outerHTML"`), allowing inline editing. The form then `hx-post`s back, triggering a `refreshViewGatePassList` on success.
-   **Success Messages:** Django's `messages` framework is used. The main `gatepass_dashboard.html` template has a `div` with `id="messages"` that `hx-retarget`s and `hx-reswaps` messages, providing user feedback.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET Gate Pass module to Django.

-   **Business Logic in Models:** Critical business rules such as `GPNo` generation, `WONo` validation, and employee ID validation are encapsulated within model methods, maintaining a "fat model, thin view" structure. This enhances maintainability and testability.
-   **Efficient UI with HTMX & Alpine.js:** All dynamic user interactions, including form submissions, table updates, and dynamic dropdowns, are handled client-side using HTMX, providing a smooth, single-page application feel without complex JavaScript frameworks. Alpine.js complements HTMX for minor UI state management.
-   **Robust Data Presentation:** DataTables integration ensures that all list views are highly functional, offering search, sort, and pagination capabilities out-of-the-box.
-   **Test Coverage:** The inclusion of detailed unit and integration tests ensures that the migrated functionality is robust and performs as expected, adhering to the 80% test coverage guideline.
-   **Scalability and Maintainability:** By leveraging Django's ORM, Class-Based Views, and best practices, the new system is designed to be scalable, easier to maintain, and adaptable to future requirements compared to the legacy ASP.NET Web Forms architecture.
-   **AI-Assisted Automation:** This structured output is designed to be directly consumable by AI-assisted tools for code generation and verification, significantly reducing the manual effort required in the migration process and enabling non-technical stakeholders to understand the systematic approach.