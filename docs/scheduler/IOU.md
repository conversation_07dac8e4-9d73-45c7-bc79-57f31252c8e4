## ASP.NET to Django Conversion Plan for IOU Module

This document outlines a strategic plan to modernize the existing ASP.NET IOU module by migrating it to a robust Django 5.0+ application. The approach prioritizes automation, leveraging conversational AI for execution, and focuses on delivering business value through improved efficiency, scalability, and maintainability.

The core of this modernization will be:
*   **Django's "Fat Model, Thin View" architecture:** Business logic resides exclusively in models, ensuring views are concise and focused on handling requests and responses.
*   **HTMX + Alpine.js for dynamic frontend:** Eliminating complex JavaScript frameworks, promoting server-side rendering, and simplifying interactive elements.
*   **DataTables for powerful list views:** Enabling advanced client-side searching, sorting, and pagination out-of-the-box.
*   **Modular and test-driven development:** Ensuring high quality and easy future enhancements.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with the following tables:
*   `tblACC_IOU_Master`: The primary table for IOU records.
*   `tblACC_IOU_Reasons`: A lookup table for IOU reasons.
*   `tblHR_OfficeStaff`: A lookup table for employee details, used for the "Employee Name" field.

**Identified Tables and Columns:**

**`tblACC_IOU_Master`:**
*   `Id` (Primary Key, auto-incrementing)
*   `SysDate` (Date of record creation/last update)
*   `SysTime` (Time of record creation/last update)
*   `SessionId` (User session ID or username)
*   `CompId` (Company ID)
*   `FinYearId` (Financial Year ID)
*   `EmpId` (Employee ID, foreign key to `tblHR_OfficeStaff`)
*   `PaymentDate` (Date of payment)
*   `Amount` (Decimal amount)
*   `Reason` (Reason ID, foreign key to `tblACC_IOU_Reasons`)
*   `Narration` (Text description)
*   `Authorize` (Boolean, `0` or `1`, indicating if the IOU is sanctioned/authorized)

**`tblACC_IOU_Reasons`:**
*   `Id` (Primary Key)
*   `Terms` (Reason description)

**`tblHR_OfficeStaff`:**
*   `EmpId` (Primary Key, or unique identifier for employee)
*   `EmployeeName` (Employee's full name)
*   `Title` (Employee's title/salutation)
    *(Note: We infer `EmpId` as the primary key from its usage as a foreign key and in `GetCompletionList`)*

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and associated business logic within the ASP.NET code-behind.

**Identified Functionality:**

*   **Read (R):**
    *   `BindDataGrid()`: Fetches all IOU records from `tblACC_IOU_Master` for the current company, financial year, and session. It then joins data from `tblACC_IOU_Reasons` to display the reason `Terms` and from `tblHR_OfficeStaff` to display the `EmployeeName`. It also formats `SysDate` and `PaymentDate` and converts `Authorize` to "Yes"/"No".
    *   `GetCompletionList()`: A web method for employee name autocomplete, querying `tblHR_OfficeStaff`.
*   **Create (C):**
    *   `GridView2_RowCommand` (CommandName "Add" and "Add1"): Handles inserting new IOU records. It extracts data from footer/empty template controls, performs basic validation (amount numeric, date format, valid employee name), and inserts into `tblACC_IOU_Master`.
*   **Update (U):**
    *   `GridView2_RowUpdating()`: Handles updating an existing IOU record. It extracts data from edit template controls, performs validation, and updates `tblACC_IOU_Master`.
*   **Delete (D):**
    *   `GridView2_RowDeleting()`: Handles deleting an IOU record based on its `Id`.
*   **Authorization/Editability Logic:**
    *   `NED()`: Checks the `Authorize` status of each IOU. If `Authorize` is `1`, it disables (hides) the "Edit" and "Delete" links for that row. It also sets `readonly` for date fields.
*   **Validation:**
    *   Client-side: `OnClientClick` for confirmations (`confirmationUpdate`, `confirmationDelete`, `confirmationAdd`), `RequiredFieldValidator`, `RegularExpressionValidator` for amount and date formats.
    *   Server-side: Custom functions `fun.NumberValidationQty()` and `fun.DateValidation()` and checks for valid employee name (`fun.getCode()`).
*   **Session Management:** `CompId`, `FinYearId`, `sId` (username) are obtained from `Session` state and used in database queries.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in the user interface.

**Inferred UI Components and Their Django Equivalents:**

*   **`GridView2` (Data Display & CRUD):** Will be replaced by a DataTables-powered HTML table.
    *   **Paging/Sorting:** Handled by DataTables.
    *   **Edit/Delete `LinkButton`s:** Will be HTMX-triggered buttons loading modal forms for edit/delete.
    *   **Add (Footer/EmptyTemplate):** Will be a single HTMX-triggered button loading a modal form for creation.
    *   **Readonly Date Fields:** Handled by Alpine.js or HTML `readonly` attribute, potentially `type="date"`.
*   **`Label`s (Data Display):** Simple Django template variable rendering.
*   **`TextBox`es (Text Input):** Django `forms.TextInput` widget with Tailwind CSS classes.
*   **`DropDownList`s (Dropdown Selection):** Django `forms.Select` widget with Tailwind CSS classes.
*   **`AutoCompleteExtender`:** Replaced by an HTMX endpoint for employee search and Alpine.js for displaying and selecting results. The `TextBox` will receive the selected `EmpId`.
*   **`CalendarExtender`:** Replaced by HTML5 `type="date"` input or a simple Alpine.js date picker, styled with Tailwind CSS.
*   **`RequiredFieldValidator`, `RegularExpressionValidator`:** Handled by Django form validation (`required=True`, `RegexValidator`, `DecimalField` validation).
*   **Confirmation `OnClientClick` JavaScript:** Replaced by HTMX `hx-confirm` attribute for simple confirmations, or Alpine.js for more complex modal confirmations.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `iou`, to house this module's functionality.

#### 4.1 Models (`iou/models.py`)

The `clsFunctions` contains logic for fetching `EmpId` from a string like "Employee Name [EmpId]" and for date formatting. These need to be handled by Django's framework features (e.g., model relationships, form clean methods) rather than custom functions.

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal

# Assuming these models exist or will be migrated too.
# For simplicity, we define basic versions for FK references.
class OfficeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)

    class Meta:
        managed = False  # Set to True if you want Django to manage this table
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.employee_name} [{self.emp_id}]"

class IouReason(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False  # Set to True if you want Django to manage this table
        db_table = 'tblACC_IOU_Reasons'
        verbose_name = 'IOU Reason'
        verbose_name_plural = 'IOU Reasons'

    def __str__(self):
        return self.terms

class Iou(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', auto_now_add=True)
    sys_time = models.TimeField(db_column='SysTime', auto_now_add=True)
    session_id = models.CharField(db_column='SessionId', max_length=100) # Corresponds to sId (username)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    
    # EmpId in tblACC_IOU_Master refers to EmpId in tblHR_OfficeStaff
    employee = models.ForeignKey(OfficeStaff, on_delete=models.PROTECT, db_column='EmpId', to_field='emp_id') 
    
    payment_date = models.DateField(db_column='PaymentDate')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3) # Max 15 digits integer + 3 decimal places
    
    # Reason in tblACC_IOU_Master refers to Id in tblACC_IOU_Reasons
    reason = models.ForeignKey(IouReason, on_delete=models.PROTECT, db_column='Reason')
    
    narration = models.TextField(db_column='Narration', blank=True, null=True)
    authorize = models.BooleanField(db_column='Authorize', default=False)

    class Meta:
        managed = False
        db_table = 'tblACC_IOU_Master'
        verbose_name = 'IOU'
        verbose_name_plural = 'IOUs'
        ordering = ['-id'] # Matches 'Order by Id Desc'

    def __str__(self):
        return f"IOU #{self.id} for {self.employee.employee_name}"

    def is_sanctioned(self):
        """
        Business logic: Determine if the IOU is sanctioned.
        Corresponds to the 'Sanctioned' column logic.
        """
        return "Yes" if self.authorize else "No"

    def is_editable_or_deletable(self):
        """
        Business logic: Determine if the IOU can be edited or deleted.
        Corresponds to the NED() function logic.
        """
        return not self.authorize

    @classmethod
    def get_filtered_ious(cls, comp_id, fin_year_id, session_id):
        """
        Business logic: Filters IOUs based on session context.
        Corresponds to BindDataGrid selection logic.
        """
        return cls.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # Original query was FinYearId<='FinYearId'
            session_id=session_id
        )

    def update_iou(self, new_data):
        """
        Business logic: Updates IOU details, including validation.
        Corresponds to GridView2_RowUpdating.
        """
        # Data validation can be done in the form, but business rules can be here too.
        # For simplicity, we assume form validation passes.
        self.employee = new_data['employee']
        self.payment_date = new_data['payment_date']
        self.amount = new_data['amount']
        self.reason = new_data['reason']
        self.narration = new_data['narration']
        self.save()
        return True # Indicate success

    @classmethod
    def create_new_iou(cls, data):
        """
        Business logic: Creates a new IOU, including validation and session context.
        Corresponds to GridView2_RowCommand (Add).
        """
        # Data validation can be done in the form.
        # For simplicity, we assume form validation passes and session context is passed.
        iou = cls.objects.create(
            sys_date=timezone.localdate(),
            sys_time=timezone.localtime().time(),
            session_id=data['session_id'],
            comp_id=data['comp_id'],
            fin_year_id=data['fin_year_id'],
            employee=data['employee'],
            payment_date=data['payment_date'],
            amount=data['amount'],
            reason=data['reason'],
            narration=data['narration']
        )
        return iou
```

#### 4.2 Forms (`iou/forms.py`)

We'll create one form for `Iou` that handles both creation and updating. The employee name autocomplete functionality is handled via HTMX, but the form will expect the `emp_id`.

```python
from django import forms
from django.core.validators import RegexValidator
from .models import Iou, IouReason, OfficeStaff
from decimal import Decimal

class IouForm(forms.ModelForm):
    # This field will be used for autocomplete and display employee name
    # The actual employee FK will be set from a hidden input or JS.
    # For now, let's make it a CharField and convert to FK in clean method.
    employee_name_display = forms.CharField(
        max_length=255,
        required=True,
        label="Employee Name",
        help_text="Start typing to search for an employee.",
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Search by employee name',
            'hx-get': '/iou/employees/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#employee-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'id': 'id_employee_name_display', # Ensure ID is set for Alpine/HTMX
            'x-on:focus': 'showSuggestions = true', # Alpine.js
            'x-on:click.away': 'showSuggestions = false', # Alpine.js
        })
    )
    # Hidden field to store the actual emp_id selected from autocomplete
    employee_emp_id = forms.CharField(
        max_length=50,
        required=True,
        widget=forms.HiddenInput(attrs={
            'id': 'id_employee_emp_id', # Ensure ID for Alpine/HTMX
        })
    )

    class Meta:
        model = Iou
        # Exclude auto-populated fields and context fields for general form input
        fields = ['employee_name_display', 'employee_emp_id', 'payment_date', 'amount', 'reason', 'narration']
        
        widgets = {
            'payment_date': forms.DateInput(attrs={
                'type': 'date', # Use HTML5 date input
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'DD-MM-YYYY',
                # Ensure it's readonly as per ASP.NET, if not using a full date picker
                # 'readonly': 'readonly' 
            }),
            'amount': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'pattern': r"^\d{1,15}(\.\d{0,3})?$", # Matches ASP.NET Regex
                'title': 'Enter a valid amount (e.g., 123.456)'
            }),
            'reason': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            }, choices=[]), # Choices will be populated in __init__
            'narration': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 3
            }),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate choices for reason dropdown
        self.fields['reason'].queryset = IouReason.objects.all()
        
        # If updating, pre-fill employee name and ID
        if self.instance.pk:
            self.fields['employee_name_display'].initial = self.instance.employee.employee_name
            self.fields['employee_emp_id'].initial = self.instance.employee.emp_id
            # If authorized, make fields readonly (similar to NED() logic)
            if not self.instance.is_editable_or_deletable():
                for field_name in self.fields:
                    self.fields[field_name].widget.attrs['readonly'] = 'readonly'
                    self.fields[field_name].widget.attrs['disabled'] = 'disabled' # Also disable to prevent submission
                # Disable save button in template via Alpine.js or conditional rendering

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount == Decimal('0.000'):
            raise forms.ValidationError("Insert Valid Amount.")
        return amount

    def clean_payment_date(self):
        payment_date = self.cleaned_data['payment_date']
        # Django's DateField handles basic date format validation.
        # Additional regex validation can be added if specific custom format is needed.
        return payment_date

    def clean(self):
        cleaned_data = super().clean()
        emp_id = cleaned_data.get('employee_emp_id')
        
        if emp_id:
            try:
                employee = OfficeStaff.objects.get(emp_id=emp_id)
                cleaned_data['employee'] = employee # Assign the actual OfficeStaff object
            except OfficeStaff.DoesNotExist:
                self.add_error('employee_name_display', "Invalid Employee Name.")
                # This ensures the employee field isn't set if lookup fails
                if 'employee' in cleaned_data:
                    del cleaned_data['employee']
        else:
            self.add_error('employee_name_display', "Employee Name is required.")

        return cleaned_data

```

#### 4.3 Views (`iou/views.py`)

Views will be thin, delegating heavy lifting to models and forms. `CompId`, `FinYearId`, and `SessionId` (username) are context-dependent. For this plan, we assume they are retrieved from the request (e.g., `request.user.id`, `request.session['comp_id']` etc.) or default to test values.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404
from django.template.loader import render_to_string
from django.utils import timezone
from .models import Iou, IouReason, OfficeStaff
from .forms import IouForm

class IouListView(ListView):
    model = Iou
    template_name = 'iou/iou/list.html'
    context_object_name = 'ious'

    def get_queryset(self):
        # In a real application, these would come from user session/profile
        comp_id = self.request.session.get('compid', 1)  # Default for testing
        fin_year_id = self.request.session.get('finyear', 2023) # Default for testing
        session_id = self.request.session.get('username', 'testuser') # Default for testing
        return Iou.get_filtered_ious(comp_id, fin_year_id, session_id)

class IouTablePartialView(IouListView):
    """
    Renders only the table portion for HTMX requests.
    """
    template_name = 'iou/iou/_iou_table.html'

class IouCreateView(CreateView):
    model = Iou
    form_class = IouForm
    template_name = 'iou/iou/_iou_form.html' # Partial for modal
    success_url = reverse_lazy('iou_list') # Fallback if not HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Add' # For template title
        return context

    def form_valid(self, form):
        # Populate session-dependent fields before saving
        # In a real app, these would come from request.user or session
        form.instance.comp_id = self.request.session.get('compid', 1)
        form.instance.fin_year_id = self.request.session.get('finyear', 2023)
        form.instance.session_id = self.request.session.get('username', 'testuser')
        
        # The form's clean method already set form.instance.employee
        # based on employee_emp_id
        
        response = super().form_valid(form)
        messages.success(self.request, 'IOU added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to signal success and trigger refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshIouList,closeModal' # Trigger custom event to refresh table and close modal
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            # Re-render the form with errors for HTMX
            return HttpResponse(
                render_to_string(self.template_name, {'form': form, 'action': 'Add'}, request=self.request),
                status=400 # Indicate bad request to HTMX
            )
        return super().form_invalid(form)


class IouUpdateView(UpdateView):
    model = Iou
    form_class = IouForm
    template_name = 'iou/iou/_iou_form.html' # Partial for modal
    context_object_name = 'iou'
    success_url = reverse_lazy('iou_list') # Fallback

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Edit' # For template title
        return context
    
    def dispatch(self, request, *args, **kwargs):
        # Implement NED() logic here: if IOU is authorized, prevent editing
        self.object = self.get_object()
        if not self.object.is_editable_or_deletable():
            messages.error(request, "This IOU has been authorized and cannot be edited.")
            if request.headers.get('HX-Request'):
                return HttpResponse(status=403, headers={'HX-Trigger': 'closeModal'}) # Forbidden
            return self.render_to_response(self.get_context_data(), status=403) # Render form as forbidden
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        # Update logic handled in Iou model method if more complex
        response = super().form_valid(form) # This calls save() on the model
        messages.success(self.request, 'IOU updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshIouList,closeModal'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form, 'action': 'Edit', 'iou': self.object}, request=self.request),
                status=400
            )
        return super().form_invalid(form)


class IouDeleteView(DeleteView):
    model = Iou
    template_name = 'iou/iou/_iou_confirm_delete.html' # Partial for modal
    context_object_name = 'iou'
    success_url = reverse_lazy('iou_list') # Fallback

    def dispatch(self, request, *args, **kwargs):
        # Implement NED() logic here: if IOU is authorized, prevent deletion
        self.object = self.get_object()
        if not self.object.is_editable_or_deletable():
            messages.error(request, "This IOU has been authorized and cannot be deleted.")
            if request.headers.get('HX-Request'):
                return HttpResponse(status=403, headers={'HX-Trigger': 'closeModal'}) # Forbidden
            return self.render_to_response(self.get_context_data(), status=403) # Render confirmation as forbidden
        return super().dispatch(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'IOU deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshIouList,closeModal'
                }
            )
        return response

class EmployeeAutoCompleteView(View):
    """
    HTMX endpoint for employee name autocomplete.
    Corresponds to GetCompletionList web method.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # In a real app, comp_id would be from session/user profile
        comp_id = request.session.get('compid', 1)
        
        employees = OfficeStaff.objects.filter(
            employee_name__icontains=query,
            # Assuming CompId is a field in OfficeStaff
            # comp_id=comp_id 
        ).order_by('employee_name')[:10] # Limit results as per ASP.NET

        suggestions = []
        for emp in employees:
            suggestions.append({
                'name': emp.employee_name,
                'emp_id': emp.emp_id
            })
        
        # Render a partial HTML template for suggestions
        return render_to_string('iou/iou/_employee_suggestions.html', {'suggestions': suggestions}, request=request)

```

#### 4.4 Templates (`iou/templates/iou/iou/`)

**`iou/templates/iou/iou/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">IOUs</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'iou_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New IOU
        </button>
    </div>
    
    <div id="iouTable-container"
         hx-trigger="load, refreshIouList from:body"
         hx-get="{% url 'iou_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading IOUs...</p>
        </div>
    </div>
    
    <!-- Modal for forms/confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on closeModal from body remove .is-active from me and remove children from #modalContent">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl overflow-hidden max-w-2xl w-full mx-4"
             hx-target="this"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup for modal and autocomplete if needed
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });
</script>
{% endblock %}
```

**`iou/templates/iou/iou/_iou_table.html`**

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="iouTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sanctioned</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Narration</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for iou in ious %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ iou.is_sanctioned }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ iou.employee.employee_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ iou.payment_date|date:"d-m-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ iou.amount|floatformat:3 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ iou.reason.terms }}</td>
                <td class="py-3 px-4 text-sm text-gray-900">{{ iou.narration|default:"-" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    {% if iou.is_editable_or_deletable %}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md mr-2 text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'iou_edit' iou.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'iou_delete' iou.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                    {% else %}
                    <span class="text-gray-500 text-xs">Authorized</span>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-5 text-center text-gray-500">No IOU records found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#iouTable').DataTable({
            "pageLength": 20, // Matches ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [7] } // Disable sorting on Actions column
            ]
        });
    });
</script>
```

**`iou/templates/iou/iou/_iou_form.html`**

```html
<div class="p-6" x-data="{ showSuggestions: false }">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ action }} IOU</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            <div>
                <label for="{{ form.employee_name_display.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.employee_name_display.label }}
                </label>
                <div class="mt-1 relative">
                    {{ form.employee_name_display }}
                    {{ form.employee_emp_id }} {# Hidden field for actual EmpId #}
                    <div id="employee-suggestions" x-show="showSuggestions" 
                         class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto">
                        <!-- Autocomplete suggestions will be loaded here via HTMX -->
                    </div>
                </div>
                {% if form.employee_name_display.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.employee_name_display.errors|join:", " }}</p>
                {% endif %}
                {% if form.employee_emp_id.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.employee_emp_id.errors|join:", " }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.payment_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.payment_date.label }}
                </label>
                <div class="mt-1">
                    {{ form.payment_date }}
                </div>
                {% if form.payment_date.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.payment_date.errors|join:", " }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.amount.label }}
                </label>
                <div class="mt-1">
                    {{ form.amount }}
                </div>
                {% if form.amount.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.amount.errors|join:", " }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.reason.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.reason.label }}
                </label>
                <div class="mt-1">
                    {{ form.reason }}
                </div>
                {% if form.reason.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.reason.errors|join:", " }}</p>
                {% endif %}
            </div>
            
            <div class="sm:col-span-2">
                <label for="{{ form.narration.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.narration.label }}
                </label>
                <div class="mt-1">
                    {{ form.narration }}
                </div>
                {% if form.narration.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.narration.errors|join:", " }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click trigger closeModal on body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out
                {% if not iou.is_editable_or_deletable %}opacity-50 cursor-not-allowed{% endif %}"
                {% if not iou.is_editable_or_deletable %}disabled{% endif %}>
                Save
            </button>
        </div>
    </form>
</div>
```

**`iou/templates/iou/iou/_iou_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the IOU for <strong>{{ iou.employee.employee_name }}</strong> with amount <strong>{{ iou.amount }}</strong>?</p>
    
    {% if not iou.is_editable_or_deletable %}
    <p class="text-red-600 font-semibold mb-4">This IOU has been authorized and cannot be deleted.</p>
    {% endif %}

    <div class="flex justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
            _="on click trigger closeModal on body">
            Cancel
        </button>
        <button 
            hx-post="{% url 'iou_delete' iou.pk %}" 
            hx-swap="none" 
            hx-confirm="Are you absolutely sure?"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out
            {% if not iou.is_editable_or_deletable %}opacity-50 cursor-not-allowed{% endif %}"
            {% if not iou.is_editable_or_deletable %}disabled{% endif %}>
            Delete
        </button>
    </div>
</div>
```

**`iou/templates/iou/iou/_employee_suggestions.html`**

```html
{% if suggestions %}
    {% for suggestion in suggestions %}
    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
         x-on:click="$event.target.closest('[x-data]').querySelector('#id_employee_name_display').value = '{{ suggestion.name }}'; 
                     $event.target.closest('[x-data]').querySelector('#id_employee_emp_id').value = '{{ suggestion.emp_id }}';
                     showSuggestions = false;">
        {{ suggestion.name }} ({{ suggestion.emp_id }})
    </div>
    {% endfor %}
{% else %}
    <div class="px-4 py-2 text-gray-500 text-sm">No employees found.</div>
{% endif %}
```

#### 4.5 URLs (`iou/urls.py`)

```python
from django.urls import path
from .views import (
    IouListView, 
    IouTablePartialView, 
    IouCreateView, 
    IouUpdateView, 
    IouDeleteView,
    EmployeeAutoCompleteView
)

urlpatterns = [
    path('iou/', IouListView.as_view(), name='iou_list'),
    path('iou/table/', IouTablePartialView.as_view(), name='iou_table'), # HTMX partial load
    path('iou/add/', IouCreateView.as_view(), name='iou_add'),
    path('iou/edit/<int:pk>/', IouUpdateView.as_view(), name='iou_edit'),
    path('iou/delete/<int:pk>/', IouDeleteView.as_view(), name='iou_delete'),
    path('iou/employees/autocomplete/', EmployeeAutoCompleteView.as_view(), name='employee_autocomplete'),
]
```

#### 4.6 Tests (`iou/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from datetime import date
from .models import Iou, IouReason, OfficeStaff
from django.contrib.messages import get_messages

class IouModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.reason1 = IouReason.objects.create(id=1, terms='Travel Expenses')
        cls.reason2 = IouReason.objects.create(id=2, terms='Office Supplies')
        
        cls.employee1 = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr.')
        cls.employee2 = OfficeStaff.objects.create(emp_id='EMP002', employee_name='Jane Smith', title='Ms.')
        
        cls.iou1 = Iou.objects.create(
            comp_id=1, fin_year_id=2023, session_id='testuser',
            employee=cls.employee1, payment_date='2023-01-15', amount=Decimal('150.750'),
            reason=cls.reason1, narration='Business trip advance', authorize=False
        )
        cls.iou2 = Iou.objects.create(
            comp_id=1, fin_year_id=2023, session_id='testuser',
            employee=cls.employee2, payment_date='2023-02-20', amount=Decimal('50.000'),
            reason=cls.reason2, narration='Purchase of pens', authorize=True
        )
  
    def test_iou_creation(self):
        self.assertEqual(self.iou1.employee.employee_name, 'John Doe')
        self.assertEqual(self.iou1.amount, Decimal('150.750'))
        self.assertEqual(self.iou1.reason.terms, 'Travel Expenses')
        self.assertFalse(self.iou1.authorize)
        
    def test_is_sanctioned_method(self):
        self.assertEqual(self.iou1.is_sanctioned(), "No")
        self.assertEqual(self.iou2.is_sanctioned(), "Yes")
        
    def test_is_editable_or_deletable_method(self):
        self.assertTrue(self.iou1.is_editable_or_deletable())
        self.assertFalse(self.iou2.is_editable_or_deletable())

    def test_get_filtered_ious_method(self):
        ious = Iou.get_filtered_ious(comp_id=1, fin_year_id=2023, session_id='testuser')
        self.assertEqual(ious.count(), 2)
        
        ious_other_user = Iou.get_filtered_ious(comp_id=1, fin_year_id=2023, session_id='otheruser')
        self.assertEqual(ious_other_user.count(), 0)

    def test_update_iou_method(self):
        new_data = {
            'employee': self.employee2,
            'payment_date': date(2023, 3, 10),
            'amount': Decimal('200.000'),
            'reason': self.reason1,
            'narration': 'Updated narration',
        }
        self.iou1.update_iou(new_data)
        self.iou1.refresh_from_db()
        self.assertEqual(self.iou1.amount, Decimal('200.000'))
        self.assertEqual(self.iou1.employee.employee_name, 'Jane Smith')
        self.assertEqual(self.iou1.narration, 'Updated narration')

    def test_create_new_iou_method(self):
        initial_count = Iou.objects.count()
        new_iou_data = {
            'session_id': 'newuser',
            'comp_id': 1,
            'fin_year_id': 2023,
            'employee': self.employee1,
            'payment_date': date(2023, 4, 1),
            'amount': Decimal('75.000'),
            'reason': self.reason2,
            'narration': 'New IOU test',
        }
        new_iou = Iou.create_new_iou(new_iou_data)
        self.assertEqual(Iou.objects.count(), initial_count + 1)
        self.assertEqual(new_iou.employee.employee_name, 'John Doe')


class IouViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.reason1 = IouReason.objects.create(id=1, terms='Travel Expenses')
        cls.reason2 = IouReason.objects.create(id=2, terms='Office Supplies')
        
        cls.employee1 = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr.')
        cls.employee2 = OfficeStaff.objects.create(emp_id='EMP002', employee_name='Jane Smith', title='Ms.')
        
        cls.iou_unauthorized = Iou.objects.create(
            comp_id=1, fin_year_id=2023, session_id='testuser',
            employee=cls.employee1, payment_date='2023-01-15', amount=Decimal('150.750'),
            reason=cls.reason1, narration='Business trip advance', authorize=False
        )
        cls.iou_authorized = Iou.objects.create(
            comp_id=1, fin_year_id=2023, session_id='testuser',
            employee=cls.employee2, payment_date='2023-02-20', amount=Decimal('50.000'),
            reason=cls.reason2, narration='Purchase of pens', authorize=True
        )
    
    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023
        self.client.session['username'] = 'testuser'
        self.list_url = reverse('iou_list')
        self.add_url = reverse('iou_add')
        self.edit_url = reverse('iou_edit', args=[self.iou_unauthorized.pk])
        self.delete_url = reverse('iou_delete', args=[self.iou_unauthorized.pk])

    def test_list_view(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'iou/iou/list.html')
        self.assertIn('ious', response.context)
        self.assertEqual(response.context['ious'].count(), 2)
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Jane Smith')

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('iou_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'iou/iou/_iou_table.html')
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Jane Smith')

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.add_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'iou/iou/_iou_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add IOU')

    def test_create_view_post_htmx_success(self):
        initial_count = Iou.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'employee_name_display': 'Test Employee [EMP001]', # Display value
            'employee_emp_id': self.employee1.emp_id, # Actual ID
            'payment_date': '2023-03-01',
            'amount': '123.456',
            'reason': self.reason1.pk,
            'narration': 'New IOU from test',
        }
        response = self.client.post(self.add_url, data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.assertEqual(Iou.objects.count(), initial_count + 1)
        # Check HX-Trigger header
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshIouList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'IOU added successfully.')

    def test_create_view_post_htmx_invalid(self):
        initial_count = Iou.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'employee_name_display': '', # Invalid: required
            'payment_date': 'invalid-date', # Invalid
            'amount': '0.000', # Invalid: must be > 0
            'reason': self.reason1.pk,
            'narration': 'New IOU from test',
        }
        response = self.client.post(self.add_url, data, **headers)
        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertTemplateUsed(response, 'iou/iou/_iou_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a valid date.') # For date field
        self.assertContains(response, 'Insert Valid Amount.') # For amount field
        self.assertEqual(Iou.objects.count(), initial_count) # No new object created

    def test_update_view_get_htmx_unauthorized_iou(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.edit_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'iou/iou/_iou_form.html')
        self.assertContains(response, 'Edit IOU')
        self.assertContains(response, self.iou_unauthorized.employee.employee_name)

    def test_update_view_get_htmx_authorized_iou(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('iou_edit', args=[self.iou_authorized.pk]), **headers)
        self.assertEqual(response.status_code, 403) # Forbidden
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'This IOU has been authorized and cannot be edited.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('closeModal', response.headers['HX-Trigger'])


    def test_update_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'employee_name_display': 'Updated Name [EMP002]',
            'employee_emp_id': self.employee2.emp_id,
            'payment_date': '2023-03-05',
            'amount': '250.000',
            'reason': self.reason2.pk,
            'narration': 'Updated narration for IOU',
        }
        response = self.client.post(self.edit_url, data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.iou_unauthorized.refresh_from_db()
        self.assertEqual(self.iou_unauthorized.amount, Decimal('250.000'))
        self.assertEqual(self.iou_unauthorized.narration, 'Updated narration for IOU')
        self.assertEqual(self.iou_unauthorized.employee.emp_id, self.employee2.emp_id)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'IOU updated successfully.')

    def test_update_view_post_htmx_authorized_iou(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'employee_name_display': 'Updated Name [EMP001]',
            'employee_emp_id': self.employee1.emp_id,
            'payment_date': '2023-03-05',
            'amount': '250.000',
            'reason': self.reason1.pk,
            'narration': 'Updated narration for IOU',
        }
        response = self.client.post(reverse('iou_edit', args=[self.iou_authorized.pk]), data, **headers)
        self.assertEqual(response.status_code, 403) # Forbidden
        self.iou_authorized.refresh_from_db()
        self.assertEqual(self.iou_authorized.amount, Decimal('50.000')) # Should not be updated
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'This IOU has been authorized and cannot be edited.')

    def test_delete_view_get_htmx_unauthorized_iou(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.delete_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'iou/iou/_iou_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.iou_unauthorized.employee.employee_name)

    def test_delete_view_get_htmx_authorized_iou(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('iou_delete', args=[self.iou_authorized.pk]), **headers)
        self.assertEqual(response.status_code, 403) # Forbidden
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'This IOU has been authorized and cannot be deleted.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_delete_view_post_htmx_success(self):
        initial_count = Iou.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.delete_url, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success, No Content
        self.assertEqual(Iou.objects.count(), initial_count - 1)
        self.assertFalse(Iou.objects.filter(pk=self.iou_unauthorized.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'IOU deleted successfully.')

    def test_delete_view_post_htmx_authorized_iou(self):
        initial_count = Iou.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('iou_delete', args=[self.iou_authorized.pk]), **headers)
        self.assertEqual(response.status_code, 403) # Forbidden
        self.assertEqual(Iou.objects.count(), initial_count) # Should not be deleted
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'This IOU has been authorized and cannot be deleted.')

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'john'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe (EMP001)')
        self.assertNotContains(response, 'Jane Smith')

        response = self.client.get(reverse('employee_autocomplete'), {'q': 'smith'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Jane Smith (EMP002)')
        self.assertNotContains(response, 'John Doe')

        response = self.client.get(reverse('employee_autocomplete'), {'q': 'xyz'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No employees found.')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

1.  **HTMX for Dynamic Updates:**
    *   **List View Refresh:** The `iou/iou/list.html` page uses `hx-get="{% url 'iou_table' %}"` with `hx-trigger="load, refreshIouList from:body"` on a container div. After any CRUD operation (Create, Update, Delete), the server sends an `HX-Trigger` header with `refreshIouList` and `closeModal` events, causing the table to re-fetch its content and the modal to close.
    *   **Modal Form Loading:** Buttons for "Add New IOU", "Edit", and "Delete" use `hx-get` to fetch the respective form/confirmation partials (`_iou_form.html` or `_iou_confirm_delete.html`) into the `#modalContent` div.
    *   **Form Submission:** Forms within the modal (`_iou_form.html`, `_iou_confirm_delete.html`) use `hx-post` to submit data back to the server. `hx-swap="outerHTML"` for forms allows re-rendering the form with validation errors, or `hx-swap="none"` for delete/successful submissions to rely purely on `HX-Trigger`.
    *   **Employee Autocomplete:** The `employee_name_display` input field in `_iou_form.html` uses `hx-get` to `{% url 'employee_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms"` and `hx-target="#employee-suggestions"`. This dynamically loads suggestions as the user types.

2.  **Alpine.js for UI State Management:**
    *   **Modal Control:** The main modal div (`#modal`) uses `x-data` and `x-show` (implicitly via `hidden` class toggle and `on click if event.target.id == 'modal' remove .is-active from me`) to manage its visibility. The `closeModal` HTMX trigger event is caught by `on closeModal from body remove .is-active from me and remove children from #modalContent`.
    *   **Autocomplete Suggestions Visibility:** An `x-data` attribute on the form and `x-show="showSuggestions"` on the suggestions container div manages the visibility of the autocomplete dropdown. Click events on suggestions populate the hidden input fields and hide the suggestions.

3.  **DataTables for List Views:**
    *   The `_iou_table.html` partial includes a `<table id="iouTable" ...>` tag.
    *   A JavaScript block within this partial (`<script> $(document).ready(function() { $('#iouTable').DataTable({...}); }); </script>`) initializes DataTables. This ensures that every time the table partial is loaded via HTMX, DataTables is re-initialized correctly.
    *   Basic configuration for `pageLength` and `lengthMenu` is included, mirroring the ASP.NET `PageSize` and providing common pagination options.

4.  **No Additional JavaScript Frameworks:** The plan strictly adheres to using only HTMX for server interaction and Alpine.js for minimal client-side reactivity, avoiding jQuery beyond DataTables initialization, React, Vue, or similar large frameworks.

5.  **DRY Templates and Components:**
    *   The `list.html` acts as the main page, delegating the table content to `_iou_table.html` and forms to `_iou_form.html`. This ensures components are reusable and maintainable.
    *   The `_iou_form.html` serves both Create and Update operations.
    *   `_employee_suggestions.html` is a dedicated partial for autocomplete results.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET IOU module to Django. By leveraging AI-assisted automation, the transition will focus on systematic conversion, minimizing manual effort and potential errors. The resulting Django application will be modern, maintainable, and aligned with best practices, offering a significantly improved foundation for future development and business growth. The use of HTMX and Alpine.js ensures a highly interactive user experience without the complexities of traditional JavaScript frameworks, while DataTables provides powerful data presentation capabilities.