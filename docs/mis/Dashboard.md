## ASP.NET to Django Conversion Script:

The provided ASP.NET code is a minimalist `.aspx` page with an empty code-behind file. This page primarily sets up content placeholders for a master page and includes a `loadingNotifier.js` script. There is no explicit database interaction, business logic, or UI components (like `GridView`, `TextBox`, etc.) defined within these snippets.

Therefore, to provide a comprehensive Django modernization plan that demonstrates the full capabilities of the target framework, we will infer a common scenario for a "Dashboard" page: displaying and managing a list of "Dashboard Items." These items will serve as a concrete example to illustrate the conversion process for models, forms, views, templates, URLs, and tests, adhering strictly to the guidelines.

This plan assumes a basic underlying database table that would typically back such a dashboard, allowing us to map it to a Django model.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not contain explicit database schema definitions (like `SqlDataSource` or direct SQL commands), we will infer a common data structure for a "Dashboard" feature. We will assume a table named `tbl_dashboard_item` which stores details about various dashboard components.

*   **Inferred Table Name:** `tbl_dashboard_item`
*   **Inferred Columns:**
    *   `id` (Primary Key, auto-incremented)
    *   `dashboard_title` (e.g., `VARCHAR(255)`)
    *   `dashboard_description` (e.g., `TEXT`)
    *   `dashboard_value` (e.g., `DECIMAL(10, 2)`)
    *   `last_updated_ts` (e.g., `DATETIME`)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code does not contain any explicit backend functionality or CRUD operations. As this is a `Dashboard` page, we infer the need for standard management capabilities for the `DashboardItem` entries.

*   **Create:** Ability to add new dashboard items.
*   **Read:** Display a list of all dashboard items.
*   **Update:** Ability to modify existing dashboard items.
*   **Delete:** Ability to remove dashboard items.
*   **Validation Logic:** Basic validation for required fields (e.g., title, description) and data types (e.g., value is a number).

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Given the lack of specific UI controls in the ASP.NET code, we will infer the necessary UI components based on typical dashboard management functionality.

*   **List View:** A table (analogous to `GridView`) to display all `DashboardItem` records, including `SN` (serial number), `Title`, `Description`, `Value`, `Last Updated`, and an "Actions" column for Edit/Delete buttons. This will be implemented using DataTables for search, sort, and pagination.
*   **Input Forms:** Text fields (analogous to `TextBox`) for `Title`, `Description`, `Value`. These forms will be loaded dynamically using HTMX into a modal, and Alpine.js will manage the modal's visibility.
*   **Action Buttons:** Buttons (analogous to `Button` or `LinkButton`) for "Add New Dashboard Item," "Edit," "Delete," "Save," and "Cancel." These will trigger HTMX requests for dynamic interactions.
*   **Client-Side Interactions:** The original `loadingNotifier.js` suggests client-side interactivity. In Django, this will be handled entirely by HTMX for data fetching and form submission, and Alpine.js for UI state management (e.g., opening/closing modals).

### Step 4: Generate Django Code

We will create a new Django application named `mis` (derived from `Module_MIS_DashBoard`).

#### 4.1 Models

Task: Create a Django model based on the inferred database schema.

## Instructions:

The `DashboardItem` model will map directly to the `tbl_dashboard_item` table. It will include methods for any business logic related to dashboard items (e.g., `get_display_value`).

```python
# mis/models.py
from django.db import models

class DashboardItem(models.Model):
    """
    Represents an item or widget displayed on the dashboard.
    Maps to the existing tbl_dashboard_item in the database.
    """
    title = models.CharField(db_column='dashboard_title', max_length=255, verbose_name='Title')
    description = models.TextField(db_column='dashboard_description', blank=True, verbose_name='Description')
    value = models.DecimalField(db_column='dashboard_value', max_digits=10, decimal_places=2, null=True, blank=True, verbose_name='Value')
    last_updated = models.DateTimeField(db_column='last_updated_ts', auto_now=True, verbose_name='Last Updated')

    class Meta:
        managed = False  # Important: Django will not manage this table's schema.
        db_table = 'tbl_dashboard_item'
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['title'] # Default ordering for lists

    def __str__(self):
        """Returns the title of the dashboard item."""
        return self.title

    def get_display_value(self):
        """
        Business logic example: Format the value for display.
        Ensures value is never None if it's supposed to be displayed.
        """
        return f"${self.value:,.2f}" if self.value is not None else "N/A"

    def update_item_value(self, new_value):
        """
        Business logic example: Update the item's value and save.
        """
        if isinstance(new_value, (int, float)):
            self.value = new_value
            self.save()
            return True
        return False
```

#### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` for `DashboardItem` will handle data binding and initial validation. Tailwind CSS classes will be applied through widgets for consistent styling.

```python
# mis/forms.py
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem objects.
    Applies Tailwind CSS classes for consistent styling.
    """
    class Meta:
        model = DashboardItem
        fields = ['title', 'description', 'value'] # last_updated is auto_now
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm h-24'}),
            'value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'}),
        }
        labels = {
            'title': 'Dashboard Title',
            'description': 'Item Description',
            'value': 'Numeric Value',
        }
        
    def clean_value(self):
        """
        Custom validation for the 'value' field.
        Ensures the value is positive if provided.
        """
        value = self.cleaned_data.get('value')
        if value is not None and value < 0:
            raise forms.ValidationError("Value cannot be negative.")
        return value
```

#### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views will be thin, primarily handling HTTP requests and delegating business logic to the model. HTMX headers (`HX-Request`, `HX-Trigger`) will be used to control dynamic updates without full page reloads. A `TablePartialView` will be added to serve the DataTables content dynamically.

```python
# mis/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemListView(ListView):
    """
    Displays a list of all Dashboard Items.
    The primary view for the dashboard management page.
    """
    model = DashboardItem
    template_name = 'mis/dashboarditem/list.html'
    context_object_name = 'dashboard_items'

class DashboardItemTablePartialView(ListView):
    """
    Renders only the table portion of the Dashboard Item list,
    designed to be loaded via HTMX for dynamic updates.
    """
    model = DashboardItem
    template_name = 'mis/dashboarditem/_dashboarditem_table.html' # Note the underscore for partial
    context_object_name = 'dashboard_items'

class DashboardItemCreateView(CreateView):
    """
    Handles creation of new Dashboard Items.
    Renders a form and processes its submission.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'mis/dashboarditem/form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing beyond headers
                headers={
                    'HX-Trigger': 'refreshDashboardItemList' # Custom event to trigger table refresh
                }
            )
        return response

class DashboardItemUpdateView(UpdateView):
    """
    Handles updating existing Dashboard Items.
    Renders a form populated with existing data and processes its submission.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'mis/dashboarditem/form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

class DashboardItemDeleteView(DeleteView):
    """
    Handles deletion of Dashboard Items.
    Renders a confirmation page and processes the deletion.
    """
    model = DashboardItem
    template_name = 'mis/dashboarditem/confirm_delete.html'
    success_url = reverse_lazy('dashboarditem_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response
```

#### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will use Django's template inheritance from `core/base.html`. HTMX will be used for partial page updates (e.g., loading forms into a modal, refreshing the table). Alpine.js will manage the modal's state. DataTables will be initialized on the dynamically loaded table.

```html
{# mis/templates/mis/dashboarditem/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Item
        </button>
    </div>
    
    <div id="dashboarditemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body" {# Triggers on initial load and custom event #}
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow overflow-hidden">
        <!-- Placeholder for loading animation -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 relative">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically initiated globally via CDN in base.html
    // Any specific Alpine.js component initialization can go here if needed.
</script>
{% endblock %}
```

```html
{# mis/templates/mis/dashboarditem/_dashboarditem_table.html #}
{# This is a partial template loaded via HTMX #}
<table id="dashboarditemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for item in dashboard_items %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.title }}</td>
            <td class="py-3 px-4 text-sm text-gray-500">{{ item.description|truncatechars:70 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.get_display_value }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ item.last_updated|date:"M d, Y H:i" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white py-1.5 px-3 rounded-md shadow-sm transition duration-300 ease-in-out mr-2"
                    hx-get="{% url 'dashboarditem_edit' item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white py-1.5 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'dashboarditem_delete' item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No dashboard items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{# DataTables initialization script. Ensure jQuery and DataTables CDN are in base.html #}
<script>
    $(document).ready(function() {
        $('#dashboarditemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            "searching": true, // Enable search box
            "ordering": true,  // Enable sorting
            "paging": true,    // Enable pagination
            "info": true       // Enable info display
        });
    });
</script>
```

```html
{# mis/templates/mis/dashboarditem/form.html #}
{# This is a partial template loaded via HTMX into a modal #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" {# hx-swap="none" because form_valid returns 204 #}
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-2 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i>Save Item
            </button>
        </div>
    </form>
</div>
```

```html
{# mis/templates/mis/dashboarditem/confirm_delete.html #}
{# This is a partial template loaded via HTMX into a modal #}
<div class="p-6 text-center">
    <i class="fas fa-exclamation-triangle text-yellow-500 text-5xl mb-4"></i>
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Dashboard Item: "<span class="font-bold">{{ dashboarditem.title }}</span>"?</p>
    <p class="text-sm text-gray-500 mb-8">This action cannot be undone.</p>

    <form hx-post="{% url 'dashboarditem_delete' dashboarditem.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i>Delete Item
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns will be defined within the `mis` app's `urls.py` and included in the project's main `urls.py`.

```python
# mis/urls.py
from django.urls import path
from .views import DashboardItemListView, DashboardItemCreateView, DashboardItemUpdateView, DashboardItemDeleteView, DashboardItemTablePartialView

urlpatterns = [
    path('dashboarditems/', DashboardItemListView.as_view(), name='dashboarditem_list'),
    path('dashboarditems/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'), # HTMX partial
    path('dashboarditems/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('dashboarditems/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('dashboarditems/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]

# In your main project urls.py (e.g., myproject/urls.py), ensure this is included:
# from django.urls import path, include
# urlpatterns = [
#     path('mis/', include('mis.urls')),
#     # ... other paths
# ]
```

#### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `DashboardItem` model and integration tests for all CRUD views, including HTMX interactions, will ensure robustness and coverage.

```python
# mis/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardItem
from datetime import datetime
from decimal import Decimal

class DashboardItemModelTest(TestCase):
    """
    Unit tests for the DashboardItem model.
    """
    @classmethod
    def setUpTestData(cls):
        """
        Set up non-modified objects used by all test methods.
        """
        DashboardItem.objects.create(
            title='Sales Dashboard',
            description='Overview of sales performance',
            value=12345.67
        )
        DashboardItem.objects.create(
            title='Marketing Trends',
            description='Analysis of recent marketing campaigns',
            value=987.65
        )

    def test_dashboard_item_creation(self):
        """Verify DashboardItem creation and field values."""
        item1 = DashboardItem.objects.get(id=1)
        self.assertEqual(item1.title, 'Sales Dashboard')
        self.assertEqual(item1.description, 'Overview of sales performance')
        self.assertEqual(item1.value, Decimal('12345.67'))
        self.assertIsNotNone(item1.last_updated) # auto_now should set this

    def test_title_label(self):
        """Verify the verbose name for the 'title' field."""
        item = DashboardItem.objects.get(id=1)
        field_label = item._meta.get_field('title').verbose_name
        self.assertEqual(field_label, 'Title')

    def test_description_label(self):
        """Verify the verbose name for the 'description' field."""
        item = DashboardItem.objects.get(id=1)
        field_label = item._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Description')

    def test_value_label(self):
        """Verify the verbose name for the 'value' field."""
        item = DashboardItem.objects.get(id=1)
        field_label = item._meta.get_field('value').verbose_name
        self.assertEqual(field_label, 'Value')
        
    def test_get_display_value_method(self):
        """Test the get_display_value method for correct formatting."""
        item_with_value = DashboardItem.objects.get(id=1)
        self.assertEqual(item_with_value.get_display_value(), "$12,345.67")

        item_no_value = DashboardItem.objects.create(title='Empty Dashboard')
        self.assertEqual(item_no_value.get_display_value(), "N/A")
        
    def test_update_item_value_method(self):
        """Test the update_item_value business logic method."""
        item = DashboardItem.objects.get(id=1)
        self.assertTrue(item.update_item_value(200.00))
        item.refresh_from_db() # Reload to get updated value
        self.assertEqual(item.value, Decimal('200.00'))
        
        self.assertFalse(item.update_item_value("not_a_number"))
        self.assertEqual(item.value, Decimal('200.00')) # Value should not have changed


class DashboardItemViewsTest(TestCase):
    """
    Integration tests for DashboardItem views.
    """
    @classmethod
    def setUpTestData(cls):
        """
        Create test data for all tests.
        """
        DashboardItem.objects.create(
            title='Existing Item 1',
            description='Description for item 1',
            value=100.00
        )
        DashboardItem.objects.create(
            title='Existing Item 2',
            description='Description for item 2',
            value=200.00
        )
    
    def setUp(self):
        """
        Set up a client for each test method.
        """
        self.client = Client()

    def test_list_view_get(self):
        """Test that the list view loads correctly."""
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/list.html')
        self.assertContains(response, 'Dashboard Items Management')
        self.assertIn('dashboard_items', response.context)
        self.assertEqual(response.context['dashboard_items'].count(), 2)

    def test_table_partial_view_get(self):
        """Test that the HTMX partial table view loads correctly."""
        response = self.client.get(reverse('dashboarditem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/_dashboarditem_table.html')
        self.assertContains(response, 'Existing Item 1')
        self.assertContains(response, 'Existing Item 2')
        self.assertIn('dashboard_items', response.context)
        self.assertEqual(response.context['dashboard_items'].count(), 2)

    def test_create_view_get(self):
        """Test that the create form view loads correctly via GET."""
        response = self.client.get(reverse('dashboarditem_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/form.html')
        self.assertContains(response, 'Add Dashboard Item')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        """Test successful creation of a new Dashboard Item via POST."""
        data = {
            'title': 'New Dashboard Item',
            'description': 'A newly created item',
            'value': 500.75,
        }
        response = self.client.post(reverse('dashboarditem_add'), data)
        # For HTMX requests, views return 204 on success
        self.assertEqual(response.status_code, 204) 
        self.assertTrue(DashboardItem.objects.filter(title='New Dashboard Item').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDashboardItemList')

    def test_create_view_post_invalid(self):
        """Test creation with invalid data (e.g., missing required field)."""
        data = {
            'title': '', # Invalid: missing title
            'description': 'Invalid item',
            'value': 100.00,
        }
        response = self.client.post(reverse('dashboarditem_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'mis/dashboarditem/form.html')
        self.assertContains(response, 'This field is required.')
        self.assertFalse(DashboardItem.objects.filter(description='Invalid item').exists())

    def test_update_view_get(self):
        """Test that the update form view loads correctly via GET."""
        item = DashboardItem.objects.get(id=1)
        response = self.client.get(reverse('dashboarditem_edit', args=[item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/form.html')
        self.assertContains(response, 'Edit Dashboard Item')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.title, 'Existing Item 1')

    def test_update_view_post_success(self):
        """Test successful update of an existing Dashboard Item via POST."""
        item = DashboardItem.objects.get(id=1)
        data = {
            'title': 'Updated Item Title',
            'description': 'Updated description',
            'value': 300.00,
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[item.id]), data)
        self.assertEqual(response.status_code, 204)
        item.refresh_from_db()
        self.assertEqual(item.title, 'Updated Item Title')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDashboardItemList')

    def test_update_view_post_invalid(self):
        """Test update with invalid data."""
        item = DashboardItem.objects.get(id=1)
        data = {
            'title': '', # Invalid
            'description': 'Invalid update',
            'value': 50.00,
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[item.id]), data)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/form.html')
        self.assertContains(response, 'This field is required.')
        item.refresh_from_db()
        self.assertNotEqual(item.title, '') # Ensure title was not updated

    def test_delete_view_get(self):
        """Test that the delete confirmation view loads correctly via GET."""
        item = DashboardItem.objects.get(id=1)
        response = self.client.get(reverse('dashboarditem_delete', args=[item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/dashboarditem/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, f'delete the Dashboard Item: "{item.title}"?')

    def test_delete_view_post_success(self):
        """Test successful deletion of a Dashboard Item via POST."""
        item_to_delete_id = DashboardItem.objects.get(id=1).id
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete_id]))
        self.assertEqual(response.status_code, 204)
        self.assertFalse(DashboardItem.objects.filter(id=item_to_delete_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDashboardItemList')

    def test_delete_view_post_non_existent(self):
        """Test deletion of a non-existent item."""
        response = self.client.post(reverse('dashboarditem_delete', args=[999]))
        self.assertEqual(response.status_code, 404) # Not Found
```

### Step 5: HTMX and Alpine.js Integration

## Instructions:

*   **HTMX for Dynamic Content:**
    *   The `list.html` uses `hx-get="{% url 'dashboarditem_table' %}"` to load the table content dynamically.
    *   `hx-trigger="load, refreshDashboardItemList from:body"` ensures the table loads on page load and refreshes when a `refreshDashboardItemList` custom event is triggered (after CRUD operations).
    *   Add/Edit/Delete buttons use `hx-get` to load forms/confirmation into `#modalContent`, triggering the modal with `_ = "on click add .is-active to #modal"`.
    *   Form submissions (`form.html`, `confirm_delete.html`) use `hx-post` and `hx-swap="none"`. The views return `HTTPResponse(status=204)` with `HX-Trigger` headers to close the modal and refresh the list without reloading the page.
    *   `hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }"` is added to forms to explicitly close the modal on successful HTMX POST.
*   **Alpine.js for UI State:**
    *   The modal (`#modal`) uses `_="on click if event.target.id == 'modal' remove .is-active from me"` to close itself when clicking outside the content area.
    *   Cancel buttons in forms also use Alpine.js to close the modal.
*   **DataTables for List Views:**
    *   The `_dashboarditem_table.html` partial includes jQuery and DataTables initialization script. It targets the `dashboarditemTable` element, enabling client-side search, sort, and pagination.
    *   Ensure jQuery and DataTables CDN links are correctly placed in `core/base.html`.
*   **No Full Page Reloads:** All user interactions (adding, editing, deleting items) are handled via HTMX, preventing full page reloads and providing a smoother user experience.

## Final Notes

*   This plan uses `mis` as the Django application name. You would need to create this app (`python manage.py startapp mis`) and add it to `INSTALLED_APPS` in your `settings.py`.
*   The `core/base.html` would contain all necessary CDN links for Tailwind CSS, Font Awesome, jQuery, DataTables, HTMX, and Alpine.js.
*   The business logic demonstrated in the `DashboardItem` model is simple (e.g., `get_display_value`, `update_item_value`). In a real-world scenario, more complex logic would reside here, ensuring views remain thin.
*   This structured approach facilitates AI-assisted automation, where each step can be guided by conversational AI, and the code generation can be driven by templates and inferred details. The focus on clear separation of concerns makes future maintenance and scaling much easier.