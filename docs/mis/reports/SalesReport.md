## ASP.NET to Django Conversion Script: Sales Report Module

This comprehensive modernization plan outlines the transition of the ASP.NET Sales Report application to a modern Django 5.0+ solution. The approach prioritizes automation, leveraging Django's robust features, and integrating HTMX + Alpine.js for a highly dynamic user experience without complex JavaScript. Business logic is strictly moved to Django models, ensuring thin, maintainable views.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

**Analysis:**

The ASP.NET code interacts with several tables. For this modernization, we will define Django models that map directly to these existing tables, using `managed = False` to prevent Django from creating or modifying the database schema.

**Identified Tables and Key Columns:**

1.  **`tblACC_SalesInvoice_Master`** (Main Sales Invoice Header)
    *   `Id` (Primary Key)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `SysDate` (Date of invoice)
    *   `InvoiceNo`
    *   `CustomerCode` (Foreign Key to `SD_Cust_master`)
    *   `AddType`, `AddAmt` (Type and amount for additions)
    *   `DeductionType`, `Deduction` (Type and amount for deductions)
    *   `PFType`, `PF` (Type and amount for PF)
    *   `CENVAT` (Foreign Key to `tblExciseser_Master` for service tax)
    *   `SEDType`, `SED` (Type and amount for SED)
    *   `AEDType`, `AED` (Type and amount for AED)
    *   `VAT` (Foreign Key to `tblVAT_Master` for VAT rate)
    *   `CST` (Foreign Key to `tblVAT_Master` for CST rate)
    *   `FreightType`, `Freight` (Type and amount for freight)
    *   `Insurance` (Amount for insurance)
    *   `OtherAmt` (Other miscellaneous amount)
    *   `CompId` (Company ID - assumed to be part of the user's session/context in Django)

2.  **`tblACC_SalesInvoice_Details`** (Sales Invoice Line Items)
    *   `Id` (Primary Key - assumed for Django mapping, as original C# aggregates)
    *   `MId` (Foreign Key to `tblACC_SalesInvoice_Master.Id`)
    *   `ReqQty` (Required Quantity)
    *   `AmtInPer` (Amount in Percentage)
    *   `Rate`

3.  **`SD_Cust_master`** (Customer Master)
    *   `CustomerId` (Primary Key / Unique Identifier)
    *   `CustomerName`
    *   `CompId` (Company ID)

4.  **`tblFinancial_master`** (Financial Year Master)
    *   `FinYearId` (Primary Key)
    *   `FinYear` (e.g., "2023-24")

5.  **`tblExciseser_Master`** (Excise/Service Tax Master)
    *   `Id` (Primary Key)
    *   `Value` (Tax Rate)
    *   `Terms` (Tax Term Name)

6.  **`tblVAT_Master`** (VAT/CST Master)
    *   `Id` (Primary Key)
    *   `Value` (Tax Rate)
    *   `Terms` (Tax Term Name)

### Step 2: Identify Backend Functionality

Task: Determine the core operations and business logic from the ASP.NET code.

**Analysis:**

The ASP.NET `SalesReport` page is primarily a reporting module. Its core functionality revolves around **reading, filtering, calculating, and presenting sales data**. There are no explicit Create, Update, or Delete (CRUD) operations on the sales invoices themselves within this specific page's code.

**Key Functionality Identified:**

*   **Data Retrieval:** The application fetches sales invoice records (`tblACC_SalesInvoice_Master`) and their details (`tblACC_SalesInvoice_Details`). It also retrieves related information from customer, financial year, excise/service tax, and VAT master tables.
*   **Filtering/Search:** Users can search for sales invoices by "Customer Name" or "Invoice No." The customer search includes an autocomplete feature.
*   **Complex Financial Calculations:** A significant portion of the C# `bindgrid` method is dedicated to intricate financial calculations to derive the "Basic Amt." and "Tax Amt." (total amount). This involves:
    *   Calculating base amount from invoice details.
    *   Applying additions (fixed amount or percentage).
    *   Applying deductions (fixed amount or percentage).
    *   Applying Provident Fund (PF) (fixed amount or percentage).
    *   Calculating CENVAT (service tax).
    *   Calculating SED (special excise duty).
    *   Calculating AED (additional excise duty).
    *   Calculating VAT or CST based on selected type and value.
    *   Incorporating freight and insurance amounts.
    *   Adding other miscellaneous amounts.
    *   These calculations are the heart of the business logic and *must* be moved to the Django `SalesInvoiceMaster` model.
*   **Data Presentation:** The filtered and calculated data is displayed in a tabular format (ASP.NET `GridView`). This will be converted to a DataTables-powered HTML table in Django.
*   **Charting:** Two bar charts (`Chart1` and `Chart2`) visualize monthly sales trends for "Basic Amt." and "Tax Amt." respectively. This requires data aggregation by month.
*   **Summary Totals:** Displays total turnover for "Basic Sales" and "Tax. Sales + Other Amount."
*   **Session Management:** The application uses `Session` variables (`username`, `finyear`, `compid`) to filter data. In Django, this will involve using the request context, potentially user profiles, or custom middleware.

**Validation Logic:** The ASP.NET code implies basic validation for search inputs (e.g., checking if textboxes are empty). More comprehensive validation, if required for data entry (not in this report), would be handled by Django forms.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Analysis:**

The existing ASP.NET Web Forms controls will be replaced by standard HTML elements, enhanced with HTMX for dynamic content loading and Alpine.js for client-side UI state management, all styled with Tailwind CSS.

*   **Page Structure (`<asp:Content>` tags):** The multiple content placeholders indicate a master page layout. In Django, this directly translates to template inheritance where all specific templates extend `core/base.html`.
*   **Search and Filter Controls:**
    *   `asp:DropDownList ID="DropDownList1"`: Will become a standard HTML `<select>` element. Its `AutoPostBack="True"` behavior, which causes a full page reload, will be replaced by an `hx-post` or `hx-get` request to dynamically swap the relevant input field (`customer_name` or `invoice_no`).
    *   `asp:TextBox ID="txtCustName"`: HTML `<input type="text">`.
    *   `cc1:AutoCompleteExtender ID="txtCustName_AutoCompleteExtender"`: Replaced by an HTMX `hx-get` to a Django view that returns JSON for customer autocomplete suggestions, displayed in a simple `<div>` or `<ul>`. Alpine.js can manage the visibility and selection of these suggestions.
    *   `asp:TextBox ID="txtpoNo"`: HTML `<input type="text">`. Its `Visible="False"` attribute will be managed by Alpine.js's `x-show` or `x-if` based on the `select` element's value.
    *   `asp:Button ID="btnSearch"`: A standard HTML `<button>`. Its `onclick` event will be replaced by an HTMX `hx-get` or `hx-post` to trigger a new data load for the table and charts.
*   **Data Display:**
    *   `asp:Panel ID="Panel1"`: A simple HTML `<div>` to contain the table, potentially with `overflow-auto` for scrolling.
    *   `asp:GridView ID="GridView1"`: This will be replaced by an HTML `<table>` element. It will be powered by [DataTables.js](https://datatables.net/) for client-side pagination, searching, and sorting. The table content itself will be loaded via HTMX as a partial template.
*   **Charting:**
    *   `asp:Chart ID="Chart2"` and `asp:Chart ID="Chart1"`: These will be replaced by HTML `<canvas>` elements. [Chart.js](https://www.chartjs.org/) will be used to render the charts client-side. The data for these charts will be fetched from Django views (which aggregate data from models) as JSON via HTMX.
*   **Summary Labels:**
    *   `asp:Label ID="lblturn"` and `asp:Label ID="lblTaxturn"`: Simple HTML `<span>` or `<p>` tags whose content will be dynamically updated with total turnover values, possibly via HTMX.

**Frontend Tooling:**

*   **HTMX:** Used for all dynamic interactions, including form submissions for search, dropdown changes, table updates, and chart data loading. This eliminates the need for complex custom JavaScript.
*   **Alpine.js:** Manages lightweight UI state, such as toggling visibility of search input fields based on dropdown selection, and potentially handling modal visibility.
*   **DataTables:** Provides out-of-the-box functionality for tabular data management (pagination, sorting, filtering).
*   **Chart.js:** For rendering modern, interactive data visualizations.
*   **Tailwind CSS:** Provides a utility-first CSS framework for rapid and consistent styling across all components.

### Step 4: Generate Django Code

We will create a Django application named `sales_reports`.

#### 4.1 Models (`sales_reports/models.py`)

This section defines the Django models corresponding to the identified database tables. Crucially, the complex financial calculations from the ASP.NET `bindgrid` method are encapsulated as properties and methods within the `SalesInvoiceMaster` model, strictly adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Sum, F
from django.db.models.functions import Coalesce
import math

class FinancialYear(models.Model):
    # Maps to tblFinancial_master
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class Customer(models.Model):
    # Maps to SD_Cust_master
    customerid = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customername = models.CharField(db_column='CustomerName', max_length=255)
    compid = models.IntegerField(db_column='CompId') # Company ID, part of customer master

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customername} [{self.customerid}]"

class ExciseService(models.Model):
    # Maps to tblExciseser_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)
    value = models.FloatField(db_column='Value', default=0.0)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service'
        verbose_name_plural = 'Excise Services'

    def __str__(self):
        return f"{self.terms} ({self.value}%)"

class VAT(models.Model):
    # Maps to tblVAT_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)
    value = models.FloatField(db_column='Value', default=0.0)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT/CST Rate'
        verbose_name_plural = 'VAT/CST Rates'

    def __str__(self):
        return f"{self.terms} ({self.value}%)"

class SalesInvoiceMaster(models.Model):
    # Maps to tblACC_SalesInvoice_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    finyear = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='sales_invoices_master')
    sysdate = models.DateField(db_column='SysDate')
    invoiceno = models.CharField(db_column='InvoiceNo', max_length=50)
    customer = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerCode', to_field='customerid', related_name='sales_invoices_master')
    
    addtype = models.IntegerField(db_column='AddType', default=0)
    addamt = models.FloatField(db_column='AddAmt', default=0.0)
    deductiontype = models.IntegerField(db_column='DeductionType', default=0)
    deduction = models.FloatField(db_column='Deduction', default=0.0)
    pftype = models.IntegerField(db_column='PFType', default=0)
    pf = models.FloatField(db_column='PF', default=0.0)
    cenvat_rate = models.ForeignKey(ExciseService, on_delete=models.DO_NOTHING, db_column='CENVAT', related_name='sales_invoices_master', null=True, blank=True)
    sedtype = models.IntegerField(db_column='SEDType', default=0)
    sed = models.FloatField(db_column='SED', default=0.0)
    aedtype = models.IntegerField(db_column='AEDType', default=0)
    aed = models.FloatField(db_column='AED', default=0.0)
    vat_rate = models.ForeignKey(VAT, on_delete=models.DO_NOTHING, db_column='VAT', related_name='sales_invoices_master_vat', null=True, blank=True)
    selectedcst = models.IntegerField(db_column='SelectedCST', default=0) # Kept for schema mapping, not used in logic
    cst_rate = models.ForeignKey(VAT, on_delete=models.DO_NOTHING, db_column='CST', related_name='sales_invoices_master_cst', null=True, blank=True)
    freighttype = models.IntegerField(db_column='FreightType', default=0)
    freight = models.FloatField(db_column='Freight', default=0.0)
    insurancetype = models.IntegerField(db_column='InsuranceType', default=0) # Kept for schema mapping, not used in logic
    insurance = models.FloatField(db_column='Insurance', default=0.0)
    otheramt = models.FloatField(db_column='OtherAmt', default=0.0)
    compid = models.IntegerField(db_column='CompId') # Company ID from session in original code

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice'
        verbose_name_plural = 'Sales Invoices'

    def __str__(self):
        return self.invoiceno
    
    # --- Start of Fat Model Business Logic Properties (C# bindgrid calculations) ---

    @property
    def basic_amount(self):
        """Calculates basic amount from sales invoice details (ReqQty * AmtInPer/100 * Rate)."""
        # Coalesce handles cases where there are no details or the sum is NULL
        sum_rate = self.sales_details.aggregate(
            total_rate=Coalesce(Sum(F('reqqty') * F('amtinper') / 100 * F('rate')), 0.0)
        )['total_rate']
        return round(sum_rate, 2)

    @property
    def amount_after_additions(self):
        """Calculates amount after additions (equivalent to amount1 in ASP.NET)."""
        amount1 = self.basic_amount
        if self.addamt > 0:
            if self.addtype == 0:  # Direct amount
                amount1 += self.addamt
            else:  # Percentage
                amount1 += (self.basic_amount * self.addamt / 100)
        return amount1

    @property
    def amount_after_deductions(self):
        """Calculates amount after deductions (equivalent to amount2 in ASP.NET)."""
        amount2 = self.amount_after_additions
        if self.deduction > 0:
            if self.deductiontype == 0:  # Direct amount
                amount2 -= self.deduction
            else:  # Percentage
                amount2 -= (self.amount_after_additions * self.deduction / 100)
        return amount2

    @property
    def amount_after_pf(self):
        """Calculates amount after PF (equivalent to amount3 in ASP.NET)."""
        amount3 = self.amount_after_deductions
        if self.pf > 0:
            if self.pftype == 0:  # Direct amount
                amount3 += self.pf
            else:  # Percentage
                amount3 += (self.amount_after_deductions * self.pf / 100)
        return amount3

    @property
    def cenvat_calculated_amount(self):
        """Calculates CENVAT (equivalent to amount4 in ASP.NET)."""
        if self.cenvat_rate and self.cenvat_rate.value > 0:
            return (self.amount_after_pf * self.cenvat_rate.value / 100)
        return 0.0

    @property
    def sed_calculated_amount(self):
        """Calculates SED (equivalent to amount5 in ASP.NET)."""
        if self.sed > 0:
            if self.sedtype == 0:  # Direct amount
                return self.sed
            else:  # Percentage
                return (self.amount_after_pf * self.sed / 100)
        return 0.0

    @property
    def aed_calculated_amount(self):
        """Calculates AED (equivalent to amount6 in ASP.NET)."""
        if self.aed > 0:
            if self.aedtype == 0:  # Direct amount
                return self.aed
            else:  # Percentage
                return (self.amount_after_pf * self.aed / 100)
        return 0.0

    @property
    def intermediate_tax_base_amount(self):
        """Calculates intermediate taxable amount (equivalent to amount8 in ASP.NET)."""
        return self.amount_after_pf + self.cenvat_calculated_amount + self.sed_calculated_amount + self.aed_calculated_amount

    @property
    def freight_calculated_amount(self):
        """Calculates freight amount."""
        if self.freight > 0:
            if self.freighttype == 0: # Direct amount
                return self.freight
            else: # Percentage based on the intermediate tax base amount
                return (self.intermediate_tax_base_amount * self.freight / 100)
        return 0.0

    @property
    def vat_cst_calculated_amount(self):
        """Calculates combined VAT/CST (equivalent to AmtCV in ASP.NET)."""
        # C# logic implies that if CST is non-zero, it takes precedence, else VAT.
        # Amount for VAT/CST calculation includes freight.
        amount_for_vat_cst_calc = self.intermediate_tax_base_amount + self.freight_calculated_amount
        
        vat_cst_amt = 0.0
        if self.cst_rate and self.cst_rate.value > 0:
            vat_cst_amt = (amount_for_vat_cst_calc * self.cst_rate.value / 100)
        elif self.vat_rate and self.vat_rate.value > 0:
            vat_cst_amt = (amount_for_vat_cst_calc * self.vat_rate.value / 100)
        
        return vat_cst_amt
    
    @property
    def total_taxed_sales_amount(self):
        """Calculates the final total amount including all taxes, freight, insurance, and other amounts (equivalent to 'Amount' in ASP.NET)."""
        return round(
            self.intermediate_tax_base_amount +
            self.vat_cst_calculated_amount +
            self.insurance +
            self.otheramt,
            2
        )
    
    @property
    def sys_month_str(self):
        """Returns month as 'MM' string for charting, like ASP.NET's Monthly() helper."""
        return self.sysdate.strftime('%m')

class SalesInvoiceDetail(models.Model):
    # Maps to tblACC_SalesInvoice_Details
    # Assuming 'Id' is the primary key in the database table for details
    id = models.IntegerField(db_column='Id', primary_key=True) 
    master_invoice = models.ForeignKey(SalesInvoiceMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='sales_details')
    reqqty = models.FloatField(db_column='ReqQty', default=0.0)
    amtinper = models.FloatField(db_column='AmtInPer', default=0.0)
    rate = models.FloatField(db_column='Rate', default=0.0)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Details'
        verbose_name = 'Sales Invoice Detail'
        verbose_name_plural = 'Sales Invoice Details'

    def __str__(self):
        return f"Detail for Invoice {self.master_invoice.invoiceno}"

```

#### 4.2 Forms (`sales_reports/forms.py`)

This form will handle the search parameters for the sales report, dynamically adjusting visible fields using HTMX and Alpine.js.

```python
from django import forms
from .models import Customer

class SalesReportSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'Invoice No'),
    ]

    # For search type dropdown, triggers HTMX request to update search input
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-post': '/sales_reports/search_input_select/', # HTMX endpoint to swap search input type
            'hx-target': '#search_input_container', # Target div to swap content
            'hx-swap': 'innerHTML',
            'hx-indicator': '#search_spinner', # Optional spinner
        }),
        label="Search By"
    )
    
    # Text field for customer name search, includes HTMX for autocomplete
    customer_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name',
            'hx-get': '/sales_reports/customer_autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:300ms, search', # Trigger on keyup after delay or search input
            'hx-target': '#customer_suggestions_list', # Target for autocomplete suggestions
            'hx-swap': 'innerHTML',
            # Alpine.js for showing/hiding suggestions or handling selection
        }),
        label="Customer Name"
    )
    
    # Text field for invoice number search
    invoice_no = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Invoice No',
        }),
        label="Invoice No"
    )

```

#### 4.3 Views (`sales_reports/views.py`)

The views handle data retrieval, filtering, and chart data preparation. Following the "Thin View" principle, all complex calculations are delegated to the `SalesInvoiceMaster` model properties. The views primarily handle request processing, form validation, and rendering of appropriate templates (often partials for HTMX).

```python
from django.views.generic import TemplateView, ListView, View
from django.http import JsonResponse, HttpResponse
from django.db.models import Sum
from django.shortcuts import render
from .models import SalesInvoiceMaster, Customer, FinancialYear
from .forms import SalesReportSearchForm
from django.utils import timezone
from collections import OrderedDict

# Assuming CompId and FinYearId are obtained from a session or user profile
# For demonstration, we'll use placeholder values. In a real app, integrate with auth/session.
CURRENT_COMP_ID = 1 # Example: Replace with request.user.company.id or similar
CURRENT_FIN_YEAR_ID = 1 # Example: Replace with request.user.financial_year.id or similar

class SalesReportView(TemplateView):
    """
    Main view for the Sales Report page.
    Renders the initial search form and containers for table and charts.
    """
    template_name = 'sales_reports/report_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = SalesReportSearchForm(self.request.GET or None)
        # Initialize search type for Alpine.js
        context['initial_search_type'] = self.request.GET.get('search_type', '0')
        return context

class SalesReportTablePartialView(ListView):
    """
    Renders the sales report table, used by HTMX to refresh the table area.
    """
    model = SalesInvoiceMaster
    template_name = 'sales_reports/_sales_table.html'
    context_object_name = 'sales_invoices'

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by company and financial year (from session/context)
        queryset = queryset.filter(compid=CURRENT_COMP_ID, finyear_id=CURRENT_FIN_YEAR_ID)

        search_type = self.request.GET.get('search_type', '0')
        customer_name = self.request.GET.get('customer_name')
        invoice_no = self.request.GET.get('invoice_no')

        if search_type == '0' and customer_name:
            # Get CustomerId from the format "Customer Name [CustomerId]"
            if '[' in customer_name and ']' in customer_name:
                cust_id = customer_name.split('[')[-1].strip(']')
                queryset = queryset.filter(customer__customerid=cust_id)
            else: # Fallback if format is not as expected, try partial name match
                queryset = queryset.filter(customer__customername__icontains=customer_name)
        elif search_type == '1' and invoice_no:
            queryset = queryset.filter(invoiceno__icontains=invoice_no)

        return queryset.order_by('-id') # Order by latest first as in ASP.NET

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass total amounts for summary labels
        total_basic_sales = sum(invoice.basic_amount for invoice in context['sales_invoices'])
        total_taxed_sales = sum(invoice.total_taxed_sales_amount for invoice in context['sales_invoices'])
        
        context['total_basic_sales'] = round(total_basic_sales, 2)
        context['total_taxed_sales'] = round(total_taxed_sales, 2)
        return context

class SalesReportChartDataView(View):
    """
    Provides chart data as JSON, used by HTMX to update Chart.js instances.
    """
    def get(self, request, *args, **kwargs):
        sales_invoices = SalesInvoiceMaster.objects.filter(
            compid=CURRENT_COMP_ID, finyear_id=CURRENT_FIN_YEAR_ID
        )

        search_type = request.GET.get('search_type', '0')
        customer_name = request.GET.get('customer_name')
        invoice_no = request.GET.get('invoice_no')

        if search_type == '0' and customer_name:
            if '[' in customer_name and ']' in customer_name:
                cust_id = customer_name.split('[')[-1].strip(']')
                sales_invoices = sales_invoices.filter(customer__customerid=cust_id)
            else:
                sales_invoices = sales_invoices.filter(customer__customername__icontains=customer_name)
        elif search_type == '1' and invoice_no:
            sales_invoices = sales_invoices.filter(invoiceno__icontains=invoice_no)

        # Standard financial year months (April to March)
        month_order = ['04', '05', '06', '07', '08', '09', '10', '11', '12', '01', '02', '03']
        month_names = ['APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC', 'JAN', 'FEB', 'MAR']
        
        monthly_basic_sales = OrderedDict((m, 0.0) for m in month_order)
        monthly_taxed_sales = OrderedDict((m, 0.0) for m in month_order)

        for invoice in sales_invoices:
            month_key = invoice.sys_month_str
            if month_key in monthly_basic_sales: # Ensure month is relevant to the fiscal year chart
                monthly_basic_sales[month_key] += invoice.basic_amount
                monthly_taxed_sales[month_key] += invoice.total_taxed_sales_amount

        chart_data = {
            'labels': month_names,
            'basic_sales': list(monthly_basic_sales.values()),
            'taxed_sales': list(monthly_taxed_sales.values()),
        }
        return JsonResponse(chart_data)

class CustomerAutocompleteView(View):
    """
    Provides customer name suggestions for autocomplete via HTMX.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        if query:
            customers = Customer.objects.filter(
                customername__icontains=query,
                compid=CURRENT_COMP_ID # Filter by company ID
            )[:10] # Limit results as in ASP.NET AutoCompleteExtender
            suggestions = [
                f"{customer.customername} [{customer.customerid}]"
                for customer in customers
            ]
        else:
            suggestions = []
        
        # Render a partial HTML list of suggestions
        return render(request, 'sales_reports/_customer_suggestions.html', {'suggestions': suggestions})

class SearchInputSelectPartialView(View):
    """
    HTMX endpoint to swap the search input field based on dropdown selection.
    """
    def post(self, request, *args, **kwargs):
        search_type = request.POST.get('search_type', '0')
        # Return a partial template containing just the relevant input field.
        # Alpine.js will then manage the display based on x-show.
        return render(request, 'sales_reports/_search_input_field.html', {'search_type': search_type})

```

#### 4.4 Templates (`sales_reports/templates/sales_reports/`)

These templates render the UI components. They are designed for HTMX partial loading and leverage Tailwind CSS for styling. They always extend `core/base.html` for consistent layout.

**`sales_reports/templates/sales_reports/report_dashboard.html`** (Main page, extends `core/base.html`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchType: '{{ initial_search_type }}' }">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sales Report</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow mb-6">
        <form id="sales_search_form" hx-get="{% url 'sales_reports:sales_table' %}" hx-target="#sales_report_content" hx-swap="innerHTML" hx-indicator="#loading_indicator" hx-sync="this:abort"
              _="on htmx:afterRequest start ChartLoader for #basicSalesChart, #taxedSalesChart">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                <div>
                    <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_type.label }}
                    </label>
                    {{ form.search_type }}
                </div>
                
                <div id="search_input_container">
                    {# This div will be swapped by HTMX based on search_type selection #}
                    {# Initial content will be based on initial_search_type context #}
                    {% include 'sales_reports/_search_input_field.html' with search_type=initial_search_type %}
                </div>
                
                <div class="col-span-1 md:col-span-2 text-right">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                        <span id="search_spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div id="sales_report_content" hx-trigger="load, searchSales" hx-get="{% url 'sales_reports:sales_table' %}" hx-swap="innerHTML">
        {# Initial content for table and charts #}
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading sales data and charts...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Global function to initialize or update charts, triggered by HTMX on table load
    function initCharts(basicSalesData, taxedSalesData, labels) {
        const createChart = (canvasId, data, title, color) => {
            const ctx = document.getElementById(canvasId);
            if (ctx) {
                if (window[canvasId + 'Chart']) { // Destroy existing chart if it exists
                    window[canvasId + 'Chart'].destroy();
                }
                window[canvasId + 'Chart'] = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: title,
                            data: data,
                            backgroundColor: color,
                            borderColor: color.replace('0.2', '1'),
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            },
                            title: {
                                display: true,
                                text: title + ' (Fin. Year: {{ financial_year_name }})'
                            }
                        },
                        scales: {
                            x: {
                                title: {
                                    display: true,
                                    text: 'Month'
                                }
                            },
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: 'Amount in Rs.'
                                }
                            }
                        }
                    }
                });
            }
        };

        // Fetch data from chart endpoint using HTMX (or directly via fetch if preferred)
        htmx.ajax('GET', '{% url "sales_reports:chart_data" %}' + '?' + new URLSearchParams(new FormData(document.getElementById('sales_search_form'))).toString(), {
            swap: 'none',
            handler: function(response) {
                const data = JSON.parse(response);
                createChart('basicSalesChart', data.basic_sales, 'Monthly Basic Sales', 'rgba(75, 192, 192, 0.7)');
                createChart('taxedSalesChart', data.taxed_sales, 'Monthly Taxed Sales + Other Amount', 'rgba(153, 102, 255, 0.7)');
            }
        });
    }

    // Custom event listener for `start ChartLoader` from hyperscript
    document.addEventListener('ChartLoader', function(evt) {
        initCharts();
    });

    // Ensure DataTables is re-initialized after HTMX swaps
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'sales_table_container') {
            $('#salesReportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTables instance
            });
        }
    });

    // Handle autocomplete selection with Alpine.js
    document.addEventListener('alpine:init', () => {
        Alpine.data('autocomplete', () => ({
            suggestionsVisible: false,
            selectSuggestion(event) {
                document.getElementById('{{ form.customer_name.id_for_label }}').value = event.target.textContent.trim();
                this.suggestionsVisible = false;
            }
        }));
    });
</script>
{% endblock %}

```

**`sales_reports/templates/sales_reports/_sales_table.html`** (Partial for DataTables, loaded via HTMX)

```html
{# This template is swapped into #sales_report_content by HTMX #}
{# It does NOT extend base.html #}

{% comment %}
   This template receives the sales_invoices queryset, total_basic_sales, and total_taxed_sales
{% endcomment %}

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
    <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">Sales Overview</h3>
        <canvas id="basicSalesChart" class="h-80 w-full"></canvas>
        <p class="mt-4 text-center font-bold text-lg text-blue-600" id="lblturn">
            Total Basic Sales : {{ total_basic_sales|floatformat:2 }}
        </p>
    </div>
    <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">Taxed Sales Overview</h3>
        <canvas id="taxedSalesChart" class="h-80 w-full"></canvas>
        <p class="mt-4 text-center font-bold text-lg text-purple-600" id="lblTaxturn">
            Total Tax. Sales + Other Amount : {{ total_taxed_sales|floatformat:2 }}
        </p>
    </div>
</div>

<div class="bg-white p-6 rounded-lg shadow overflow-x-auto" id="sales_table_container">
    <h3 class="text-xl font-semibold mb-4 text-gray-800">Sales Invoice Details</h3>
    <table id="salesReportTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Basic Amt.</th>
                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tax Amt.</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if sales_invoices %}
                {% for invoice in sales_invoices %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.finyear.finyear }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.customer.customername }} [{{ invoice.customer.customerid }}]</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.invoiceno }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ invoice.sysdate|date:"d-m-Y" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-right text-gray-500">{{ invoice.basic_amount|floatformat:2 }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-right text-gray-500">{{ invoice.total_taxed_sales_amount|floatformat:2 }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="7" class="py-4 px-4 text-center text-sm text-red-500 font-bold">No data found to display</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# Initialize DataTables #}
<script>
    // DataTables initialization is handled by the `htmx:afterSwap` listener in report_dashboard.html
    // This ensures it runs after the new table HTML is inserted.
    // If you need more complex DataTables options, they would go here.
</script>

```

**`sales_reports/templates/sales_reports/_search_input_field.html`** (Partial for dynamic search input swap)

```html
{# This template is swapped into #search_input_container by HTMX #}
{# It does NOT extend base.html #}

{% comment %}
   Receives 'search_type' from the view context.
{% endcomment %}

<div x-data="autocomplete">
    {% if search_type == '0' %}
        {# Customer Name search input #}
        <label for="id_customer_name" class="block text-sm font-medium text-gray-700">
            Customer Name
        </label>
        <input type="text" name="customer_name" id="id_customer_name"
               class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
               placeholder="Enter Customer Name"
               value="{{ request.GET.customer_name|default:'' }}"
               hx-get="{% url 'sales_reports:customer_autocomplete' %}"
               hx-trigger="keyup changed delay:300ms, search"
               hx-target="#customer_suggestions_list"
               hx-swap="innerHTML"
               @focus="suggestionsVisible = true" @click.outside="suggestionsVisible = false">
        <div id="customer_suggestions_list" class="relative">
            {# Autocomplete suggestions will be loaded here by HTMX #}
            {# Controlled by Alpine.js for visibility #}
            <ul x-show="suggestionsVisible" 
                class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto"
                @click.stop="selectSuggestion($event)">
                {# Suggestions will be li elements with hx-swap-oob #}
            </ul>
        </div>
    {% elif search_type == '1' %}
        {# Invoice No search input #}
        <label for="id_invoice_no" class="block text-sm font-medium text-gray-700">
            Invoice No
        </label>
        <input type="text" name="invoice_no" id="id_invoice_no"
               class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
               placeholder="Enter Invoice No"
               value="{{ request.GET.invoice_no|default:'' }}">
    {% endif %}
</div>

```

**`sales_reports/templates/sales_reports/_customer_suggestions.html`** (Partial for autocomplete suggestions)

```html
{# This template is swapped into #customer_suggestions_list by HTMX. It contains <ul> content. #}
{# It does NOT extend base.html #}

{% if suggestions %}
    {% for suggestion in suggestions %}
        <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm text-gray-700">{{ suggestion }}</li>
    {% endfor %}
{% else %}
    <li class="px-4 py-2 text-sm text-gray-500">No suggestions</li>
{% endif %}
```

#### 4.5 URLs (`sales_reports/urls.py`)

Defines the URL patterns for the sales report application, including paths for the main dashboard, the data table partial, chart data, customer autocomplete, and dynamic search input swapping.

```python
from django.urls import path
from .views import SalesReportView, SalesReportTablePartialView, SalesReportChartDataView, CustomerAutocompleteView, SearchInputSelectPartialView

app_name = 'sales_reports'

urlpatterns = [
    # Main dashboard view
    path('', SalesReportView.as_view(), name='dashboard'),
    
    # HTMX endpoints for dynamic content
    path('sales_table/', SalesReportTablePartialView.as_view(), name='sales_table'),
    path('chart_data/', SalesReportChartDataView.as_view(), name='chart_data'),
    path('customer_autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    path('search_input_select/', SearchInputSelectPartialView.as_view(), name='search_input_select'),
]

```

#### 4.6 Tests (`sales_reports/tests.py`)

Comprehensive tests for models (unit tests for calculation properties) and views (integration tests for data loading, filtering, and HTMX responses).

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import SalesInvoiceMaster, SalesInvoiceDetail, Customer, FinancialYear, ExciseService, VAT
from datetime import date
import json

# Define placeholder values for company and financial year consistent with views.py
CURRENT_COMP_ID_TEST = 1
CURRENT_FIN_YEAR_ID_TEST = 1

class SalesReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dependent master data
        cls.fin_year = FinancialYear.objects.create(finyearid=CURRENT_FIN_YEAR_ID_TEST, finyear='2023-24')
        cls.customer1 = Customer.objects.create(customerid='CUST001', customername='Alpha Corp', compid=CURRENT_COMP_ID_TEST)
        cls.customer2 = Customer.objects.create(customerid='CUST002', customername='Beta Ltd', compid=CURRENT_COMP_ID_TEST)
        cls.excise_service_rate = ExciseService.objects.create(id=1, terms='CENVAT10', value=10.0)
        cls.vat_rate_12 = VAT.objects.create(id=1, terms='VAT12', value=12.0)
        cls.cst_rate_2 = VAT.objects.create(id=2, terms='CST2', value=2.0)

        # Create SalesInvoiceMaster instances
        # Invoice 1: Basic case
        cls.invoice1 = SalesInvoiceMaster.objects.create(
            id=1, finyear=cls.fin_year, sysdate=date(2023, 4, 15), invoiceno='INV001', 
            customer=cls.customer1, compid=CURRENT_COMP_ID_TEST,
            addtype=0, addamt=0.0, deductiontype=0, deduction=0.0, pftype=0, pf=0.0, 
            cenvat_rate=None, sedtype=0, sed=0.0, aedtype=0, aed=0.0, 
            vat_rate=cls.vat_rate_12, cst_rate=None, freighttype=0, freight=0.0, 
            insurance=0.0, otheramt=0.0
        )
        SalesInvoiceDetail.objects.create(id=1, master_invoice=cls.invoice1, reqqty=10.0, amtinper=100.0, rate=50.0) # Basic Amt: 10 * 100/100 * 50 = 500

        # Invoice 2: With additions, deductions, PF, CENVAT, SED, AED, Freight, Insurance, OtherAmt, CST
        cls.invoice2 = SalesInvoiceMaster.objects.create(
            id=2, finyear=cls.fin_year, sysdate=date(2023, 5, 20), invoiceno='INV002', 
            customer=cls.customer2, compid=CURRENT_COMP_ID_TEST,
            addtype=1, addamt=5.0, # 5% addition
            deductiontype=0, deduction=10.0, # 10 fixed deduction
            pftype=1, pf=2.0, # 2% PF
            cenvat_rate=cls.excise_service_rate, # 10% CENVAT
            sedtype=1, sed=3.0, # 3% SED
            aedtype=0, aed=5.0, # 5 fixed AED
            vat_rate=None, cst_rate=cls.cst_rate_2, # 2% CST
            freighttype=1, freight=1.0, # 1% Freight
            insurance=15.0, otheramt=25.0
        )
        SalesInvoiceDetail.objects.create(id=2, master_invoice=cls.invoice2, reqqty=20.0, amtinper=100.0, rate=100.0) # Basic Amt: 20 * 100/100 * 100 = 2000

    def test_basic_amount_calculation(self):
        # Invoice 1: 10 * 100/100 * 50 = 500
        self.assertEqual(self.invoice1.basic_amount, 500.00)
        # Invoice 2: 20 * 100/100 * 100 = 2000
        self.assertEqual(self.invoice2.basic_amount, 2000.00)

    def test_amount_after_additions(self):
        # Invoice 1: 500 (no additions)
        self.assertEqual(self.invoice1.amount_after_additions, 500.00)
        # Invoice 2: 2000 + (2000 * 0.05) = 2100
        self.assertEqual(self.invoice2.amount_after_additions, 2100.00)

    def test_amount_after_deductions(self):
        # Invoice 1: 500 (no deductions)
        self.assertEqual(self.invoice1.amount_after_deductions, 500.00)
        # Invoice 2: 2100 - 10 = 2090
        self.assertEqual(self.invoice2.amount_after_deductions, 2090.00)

    def test_amount_after_pf(self):
        # Invoice 1: 500 (no PF)
        self.assertEqual(self.invoice1.amount_after_pf, 500.00)
        # Invoice 2: 2090 + (2090 * 0.02) = 2090 + 41.8 = 2131.8
        self.assertEqual(self.invoice2.amount_after_pf, 2131.80)

    def test_cenvat_calculated_amount(self):
        # Invoice 1: No CENVAT = 0
        self.assertEqual(self.invoice1.cenvat_calculated_amount, 0.00)
        # Invoice 2: 2131.8 * 0.10 = 213.18
        self.assertEqual(self.invoice2.cenvat_calculated_amount, 213.18)

    def test_sed_calculated_amount(self):
        # Invoice 1: No SED = 0
        self.assertEqual(self.invoice1.sed_calculated_amount, 0.00)
        # Invoice 2: 2131.8 * 0.03 = 63.954 ~ 63.95
        self.assertEqual(round(self.invoice2.sed_calculated_amount, 2), 63.95)

    def test_aed_calculated_amount(self):
        # Invoice 1: No AED = 0
        self.assertEqual(self.invoice1.aed_calculated_amount, 0.00)
        # Invoice 2: Fixed AED = 5.0
        self.assertEqual(self.invoice2.aed_calculated_amount, 5.00)

    def test_intermediate_tax_base_amount(self):
        # Invoice 1: 500 + 0 + 0 + 0 = 500
        self.assertEqual(self.invoice1.intermediate_tax_base_amount, 500.00)
        # Invoice 2: 2131.8 + 213.18 + 63.954 + 5 = 2413.934 ~ 2413.93
        self.assertEqual(round(self.invoice2.intermediate_tax_base_amount, 2), 2413.93)

    def test_freight_calculated_amount(self):
        # Invoice 1: No Freight = 0
        self.assertEqual(self.invoice1.freight_calculated_amount, 0.00)
        # Invoice 2: 2413.934 * 0.01 = 24.13934 ~ 24.14
        self.assertEqual(round(self.invoice2.freight_calculated_amount, 2), 24.14)

    def test_vat_cst_calculated_amount(self):
        # Invoice 1: (Intermediate + Freight) * VAT = (500 + 0) * 0.12 = 60
        self.assertEqual(round(self.invoice1.vat_cst_calculated_amount, 2), 60.00)
        # Invoice 2: (Intermediate + Freight) * CST = (2413.934 + 24.13934) * 0.02 = 2438.07334 * 0.02 = 48.7614668 ~ 48.76
        self.assertEqual(round(self.invoice2.vat_cst_calculated_amount, 2), 48.76)

    def test_total_taxed_sales_amount(self):
        # Invoice 1: Intermediate + VAT/CST + Insurance + OtherAmt = 500 + 60 + 0 + 0 = 560
        self.assertEqual(self.invoice1.total_taxed_sales_amount, 560.00)
        # Invoice 2: Intermediate + VAT/CST + Insurance + OtherAmt
        # 2413.934 + 48.7614668 + 15 + 25 = 2502.6954668 ~ 2502.70
        self.assertEqual(self.invoice2.total_taxed_sales_amount, 2502.70)
    
    def test_sys_month_str(self):
        self.assertEqual(self.invoice1.sys_month_str, '04')
        self.assertEqual(self.invoice2.sys_month_str, '05')


class SalesReportViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Set up shared data for all tests
        cls.fin_year = FinancialYear.objects.create(finyearid=CURRENT_FIN_YEAR_ID_TEST, finyear='2023-24')
        cls.customer1 = Customer.objects.create(customerid='CUST001', customername='Alpha Corp', compid=CURRENT_COMP_ID_TEST)
        cls.customer2 = Customer.objects.create(customerid='CUST002', customername='Beta Ltd', compid=CURRENT_COMP_ID_TEST)
        cls.invoice_master = SalesInvoiceMaster.objects.create(
            id=101, finyear=cls.fin_year, sysdate=date(2023, 7, 10), invoiceno='INV-TEST-001', 
            customer=cls.customer1, compid=CURRENT_COMP_ID_TEST,
            addtype=0, addamt=0.0, deductiontype=0, deduction=0.0, pftype=0, pf=0.0, 
            cenvat_rate=None, sedtype=0, sed=0.0, aedtype=0, aed=0.0, 
            vat_rate=None, cst_rate=None, freighttype=0, freight=0.0, 
            insurance=0.0, otheramt=0.0
        )
        SalesInvoiceDetail.objects.create(id=101, master_invoice=cls.invoice_master, reqqty=100.0, amtinper=100.0, rate=10.0) # Basic Amt: 1000.0

    def test_dashboard_view_get(self):
        response = self.client.get(reverse('sales_reports:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/report_dashboard.html')
        self.assertIn('form', response.context)
        self.assertIn('initial_search_type', response.context)

    def test_sales_table_partial_view_get(self):
        response = self.client.get(reverse('sales_reports:sales_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/_sales_table.html')
        self.assertIn('sales_invoices', response.context)
        self.assertIn('total_basic_sales', response.context)
        self.assertIn('total_taxed_sales', response.context)
        self.assertContains(response, 'INV-TEST-001') # Check if invoice data is present

    def test_sales_table_partial_view_customer_filter(self):
        response = self.client.get(reverse('sales_reports:sales_table'), {'search_type': '0', 'customer_name': 'Alpha Corp [CUST001]'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/_sales_table.html')
        self.assertEqual(len(response.context['sales_invoices']), 1)
        self.assertEqual(response.context['sales_invoices'][0].invoiceno, 'INV-TEST-001')
        self.assertContains(response, 'INV-TEST-001')

        response_no_match = self.client.get(reverse('sales_reports:sales_table'), {'search_type': '0', 'customer_name': 'NonExistent Customer'})
        self.assertEqual(response_no_match.status_code, 200)
        self.assertEqual(len(response_no_match.context['sales_invoices']), 0)
        self.assertContains(response_no_match, 'No data found to display')

    def test_sales_table_partial_view_invoice_filter(self):
        response = self.client.get(reverse('sales_reports:sales_table'), {'search_type': '1', 'invoice_no': 'INV-TEST-001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/_sales_table.html')
        self.assertEqual(len(response.context['sales_invoices']), 1)
        self.assertEqual(response.context['sales_invoices'][0].invoiceno, 'INV-TEST-001')
        self.assertContains(response, 'INV-TEST-001')

        response_no_match = self.client.get(reverse('sales_reports:sales_table'), {'search_type': '1', 'invoice_no': 'NONEXISTENT'})
        self.assertEqual(response_no_match.status_code, 200)
        self.assertEqual(len(response_no_match.context['sales_invoices']), 0)
        self.assertContains(response_no_match, 'No data found to display')

    def test_chart_data_view(self):
        response = self.client.get(reverse('sales_reports:chart_data'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('labels', data)
        self.assertIn('basic_sales', data)
        self.assertIn('taxed_sales', data)
        self.assertEqual(len(data['labels']), 12) # 12 months in fiscal year
        
        # Check if sales data for July (month '07') is present, corresponding to INV-TEST-001
        self.assertEqual(data['basic_sales'][data['labels'].index('JUL')], 1000.0) # Basic amount for INV-TEST-001
        self.assertEqual(data['taxed_sales'][data['labels'].index('JUL')], 1000.0) # Total taxed amount for INV-TEST-001 (simple case)

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('sales_reports:customer_autocomplete'), {'q': 'Alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/_customer_suggestions.html')
        self.assertIn('suggestions', response.context)
        self.assertContains(response, 'Alpha Corp [CUST001]')

        response_no_query = self.client.get(reverse('sales_reports:customer_autocomplete'))
        self.assertEqual(response_no_query.status_code, 200)
        self.assertContains(response_no_query, 'No suggestions')

    def test_search_input_select_partial_view(self):
        response = self.client.post(reverse('sales_reports:search_input_select'), {'search_type': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/_search_input_field.html')
        self.assertContains(response, '<input type="text" name="customer_name"')
        self.assertNotContains(response, '<input type="text" name="invoice_no"')

        response = self.client.post(reverse('sales_reports:search_input_select'), {'search_type': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/_search_input_field.html')
        self.assertContains(response, '<input type="text" name="invoice_no"')
        self.assertNotContains(response, '<input type="text" name="customer_name"')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated Django code already incorporates HTMX and Alpine.js following the specified guidelines:

*   **HTMX for Dynamic Updates:**
    *   The main `report_dashboard.html` template uses `hx-get` to initially load the sales table and charts into `#sales_report_content`.
    *   The search form uses `hx-get` to refresh `#sales_report_content` when the search button is clicked.
    *   The `search_type` dropdown uses `hx-post` to `search_input_select` endpoint, which swaps the search input field (`_search_input_field.html`) in `#search_input_container`.
    *   Customer autocomplete uses `hx-get` to `customer_autocomplete` endpoint, swapping suggestions into `#customer_suggestions_list`.
    *   `hx-indicator` is used on the search button to show a spinner during AJAX requests.
    *   `hx-sync="this:abort"` ensures that only the latest search request is processed if the user types rapidly.
    *   `htmx:afterSwap` event listener ensures DataTables is re-initialized after a new table partial is loaded.
    *   Hyperscript (`_`) is used to trigger chart loading after the table partial is swapped.
*   **Alpine.js for UI State:**
    *   `x-data` is used on the main `div` to manage `searchType` state for initial rendering of the search input.
    *   `x-data="autocomplete"` is added to the autocomplete input partial to manage `suggestionsVisible` and `selectSuggestion` logic for customer autocomplete.
    *   `@click.outside` is used to hide autocomplete suggestions when clicking outside.
*   **DataTables for List Views:**
    *   The `_sales_table.html` partial renders a standard HTML `<table>` with the ID `salesReportTable`.
    *   A JavaScript snippet (within `report_dashboard.html`'s `htmx:afterSwap` listener) initializes this table as a DataTables instance, providing client-side searching, sorting, and pagination.
*   **Chart.js for Data Visualization:**
    *   Two `<canvas>` elements (`basicSalesChart`, `taxedSalesChart`) are included in `_sales_table.html`.
    *   A global `initCharts` JavaScript function (in `report_dashboard.html`) fetches chart data from `chart_data/` endpoint via HTMX and uses Chart.js to render the graphs. This function is triggered by a custom Hyperscript event after the table data loads.
*   **No Custom JavaScript (beyond framework usage):** All dynamic interactions are handled by HTMX, Alpine.js, Chart.js, and DataTables, minimizing custom JavaScript.
*   **DRY Template Inheritance:** All main templates (`report_dashboard.html`) extend `core/base.html`, ensuring a consistent application layout. Partial templates (`_sales_table.html`, `_search_input_field.html`, `_customer_suggestions.html`) are used for reusable, dynamically loaded components.
*   **Strict Separation of Concerns:** Business logic for financial calculations resides entirely within the `SalesInvoiceMaster` model (Fat Model). Views are thin, focusing on coordinating data flow and rendering.

---

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET Sales Report to Django. It emphasizes automated processes, adherence to modern Django best practices, and a lean, dynamic frontend. This approach not only modernizes the application but also significantly improves its maintainability, performance, and scalability for future development.