## ASP.NET to Django Conversion Script: Sales Distribution Module Modernization Plan

This document outlines a strategic plan for migrating the ASP.NET Sales Distribution module to a modern Django application. The focus is on leveraging AI-assisted automation to streamline the transition, ensuring a robust, scalable, and maintainable solution.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Business Value Proposition

Migrating the Sales Distribution module to Django offers significant business advantages:

1.  **Enhanced Performance & Responsiveness:** Utilizing HTMX for dynamic content loading means users experience faster interactions and fewer full-page reloads, leading to a smoother, more efficient reporting experience.
2.  **Improved Maintainability & Scalability:** Django's structured framework, combined with the "Fat Model, Thin View" approach, centralizes business logic, making the codebase easier to understand, debug, and extend. This reduces long-term maintenance costs and allows for future growth.
3.  **Modern User Experience:** Adopting DataTables for all tabular data provides powerful client-side features like search, sort, and pagination out-of-the-box, significantly enhancing user interaction with reports.
4.  **Reduced Development Time for Future Enhancements:** Django's "batteries-included" philosophy and the modular design enable quicker development of new features and reports, accelerating time-to-market for business innovations.
5.  **Cost Efficiency:** Leveraging open-source technologies like Django, HTMX, and Alpine.js eliminates proprietary licensing fees associated with older frameworks and provides access to a vast community for support and resources.
6.  **Future-Proof Architecture:** Transitioning to a modern, actively maintained framework like Django positions the application for long-term sustainability, enabling easier integrations with emerging technologies and ensuring security updates.

---

## Conversion Steps:

This module focuses on displaying sales distribution reports including enquiries, purchase orders, work orders, and dispatches, along with their respective monthly charts. The approach will create distinct Django components for each report section.

## Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with several database tables to fetch report data. The `clsFunctions.TotOfModule` calls and direct SQL queries in `drawgraph` reveal the primary tables and their relationships.

**Inferred Tables and Columns:**

*   **`tblFinancial_master`**:
    *   `FinYearId` (Primary Key, Integer)
    *   `FinYear` (String)
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (Primary Key, Integer)
    *   `EmployeeName` (String)
*   **`SD_Cust_Master`**:
    *   `CustomerId` (Primary Key, Integer)
    *   `CustomerName` (String)
*   **`SD_Cust_Enquiry_Master`**:
    *   `EnqId` (String, likely unique identifier)
    *   `CustomerName` (String, might be denormalized or direct from this table)
    *   `POStatus` (String)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master.CustomerId`)
    *   `SysDate` (Date)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff.EmpId`)
    *   `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`)
    *   `CompId` (Integer)
*   **`SD_Cust_PO_Master`**:
    *   `POId` (String, likely unique identifier)
    *   `PONo` (String)
    *   `EnqId` (Foreign Key to `SD_Cust_Enquiry_Master.EnqId`)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master.CustomerId`)
    *   `SysDate` (Date)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff.EmpId`)
    *   `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`)
    *   `CompId` (Integer)
*   **`SD_Cust_WorkOrder_Master`**:
    *   `WONo` (String, likely unique identifier)
    *   `PONo` (Foreign Key to `SD_Cust_PO_Master.PONo`)
    *   `EnqId` (Foreign Key to `SD_Cust_Enquiry_Master.EnqId`)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master.CustomerId`)
    *   `SysDate` (Date)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff.EmpId`)
    *   `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`)
    *   `CompId` (Integer)
*   **`SD_Cust_WorkOrder_Release`**:
    *   `WRNo` (String, likely unique identifier)
    *   `WONo` (Foreign Key to `SD_Cust_WorkOrder_Master.WONo`)
    *   `ItemId` (Integer)
*   **`SD_Cust_WorkOrder_Dispatch`**:
    *   `Id` (Primary Key, Integer)
    *   `WRNo` (Foreign Key to `SD_Cust_WorkOrder_Release.WRNo`)
    *   `SysDate` (Date)
    *   `FinYearId` (Foreign Key to `tblFinancial_master.FinYearId`)
    *   `CompId` (Integer)
    *   `ItemId` (Integer)

## Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page is a reporting dashboard.

*   **Read Operations:** All identified functions (`Enquirygrid`, `POgrid`, `WOgrid`, `DAgrid`) perform data retrieval for display in `GridView` controls. The `drawgraph` function also reads data to populate charts. There are no explicit Create, Update, or Delete operations on this page.
*   **Data Filtering:** All data retrieval is filtered by `CompId` (Company ID) and `FinYearId` (Financial Year ID) from the user's session.
*   **Data Aggregation:** The `drawgraph` function aggregates data by month to show counts for Enquiries, POs, WOs, and Dispatches, which will be implemented as model methods or manager methods in Django.
*   **Pagination:** Handled by `GridView`'s `AllowPaging` and `onpageindexchanging` events. In Django, this will be handled client-side by DataTables for initial loading, or via Django's `Paginator` if server-side pagination is desired. For this case, DataTables will handle it client-side after initial data load.

## Step 3: Infer UI Components

**Analysis:** The ASP.NET page utilizes standard Web Forms controls for display and partial updates.

*   **`asp:GridView`:** Four instances (`GridView1`, `SearchGridView1`, `GridView2`, `GridView3`) are used to display tabular data. These will be replaced by standard HTML tables enhanced with DataTables.
*   **`asp:Chart`:** Four instances (`Chart1`, `Chart2`, `Chart3`, `Chart4`) are used to display graphical data. These will be replaced by a modern JavaScript charting library (e.g., Chart.js) rendering into HTML canvas elements.
*   **`asp:UpdatePanel`:** This is used for AJAX updates, equivalent to HTMX in Django for partial page rendering and dynamic content loading.
*   **Master Page/ContentPlaceHolder:** Indicates a templating system. Django will use `base.html` for the overall layout.
*   **CSS/JavaScript Links:** Old CSS and JavaScript will be replaced by Tailwind CSS and HTMX/Alpine.js.

---

## Step 4: Generate Django Code

The Django application will be named `sales_reports`.

### 4.1 Models (`sales_reports/models.py`)

Models are defined with `managed = False` and `db_table` to map to existing database tables. Custom manager methods are added for the report-specific data retrieval and aggregation logic, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Count, F
from django.db.models.functions import ExtractMonth
from collections import OrderedDict
import calendar
from datetime import date

# Manager for common report data logic and aggregations
class SalesReportManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset()

    def get_monthly_counts(self, company_id, financial_year_id, date_field_name='sysdate'):
        """
        Calculates monthly counts for a given model instance.
        Assumes 'sysdate' field is a DateField or DateTimeField.
        Financial year typically runs from April to March.
        """
        
        # Filter by company and financial year
        queryset = self.filter(
            compid=company_id,
            finyearid=financial_year_id
        )
        
        # If the date_field_name is not 'sysdate' (e.g., for WorkOrderRelease table which doesn't have it directly)
        # then we need to adjust, but for these specific models, sysdate is direct.
        
        # Annotate with month extracted from the date field
        counts = queryset.annotate(
            month=ExtractMonth(date_field_name)
        ).values('month').annotate(
            count=Count('pk') # Count primary keys for distinct records
        ).order_by('month')

        # Initialize results for all 12 months (Apr-Mar financial year)
        months_order = [4, 5, 6, 7, 8, 9, 10, 11, 12, 1, 2, 3] # April to March
        monthly_data = OrderedDict()
        for m in months_order:
            month_abbr = calendar.month_abbr[m].upper()
            monthly_data[month_abbr] = 0 # Default to 0 if no data for month

        # Fill with actual counts
        for entry in counts:
            month_num = entry['month']
            month_abbr = calendar.month_abbr[month_num].upper()
            if month_abbr in monthly_data: # Ensure we don't add new keys
                monthly_data[month_abbr] = entry['count']
            
        return {
            'labels': list(monthly_data.keys()),
            'data': list(monthly_data.values())
        }

    def get_filtered_report_data(self, company_id, financial_year_id, order_by_field='id'):
        """
        Generic method to fetch filtered data for report tables.
        """
        return self.filter(
            compid=company_id,
            finyearid=financial_year_id
        ).order_by(order_by_field)


class FinancialYear(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear

class Employee(models.Model):
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employeename

class Customer(models.Model):
    customerid = models.IntegerField(db_column='CustomerId', primary_key=True)
    customername = models.CharField(db_column='CustomerName', max_length=255)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customername

class Enquiry(models.Model):
    # Assuming EnqId is the primary key and unique identifier
    enqid = models.CharField(db_column='EnqId', max_length=50, primary_key=True)
    customername = models.CharField(db_column='CustomerName', max_length=255)
    customerid = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='enquiries_from_enquiry', null=True, blank=True)
    po_status = models.CharField(db_column='POStatus', max_length=50, blank=True, null=True)
    sysdate = models.DateField(db_column='SysDate')
    sessionid = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='enquiries_generated', null=True, blank=True)
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='enquiries', null=True, blank=True)
    compid = models.IntegerField(db_column='CompId')

    objects = SalesReportManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_Enquiry_Master'
        verbose_name = 'Enquiry'
        verbose_name_plural = 'Enquiries'

    def __str__(self):
        return self.enqid

    @property
    def fin_year(self):
        return self.finyearid.finyear if self.finyearid else 'N/A'

    @property
    def generated_by(self):
        return self.sessionid.employeename if self.sessionid else 'N/A'

class PurchaseOrder(models.Model):
    # Assuming POId is the primary key and unique identifier
    poid = models.CharField(db_column='POId', max_length=50, primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=50)
    enqid = models.ForeignKey(Enquiry, on_delete=models.DO_NOTHING, db_column='EnqId', to_field='enqid', related_name='purchase_orders', null=True, blank=True)
    customerid = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='purchase_orders_from_po', null=True, blank=True)
    sysdate = models.DateField(db_column='SysDate')
    sessionid = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='pos_generated', null=True, blank=True)
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='purchase_orders', null=True, blank=True)
    compid = models.IntegerField(db_column='CompId')

    objects = SalesReportManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.pono
    
    @property
    def fin_year(self):
        return self.finyearid.finyear if self.finyearid else 'N/A'

    @property
    def customer_name(self):
        return self.customerid.customername if self.customerid else 'N/A'

    @property
    def generated_by(self):
        return self.sessionid.employeename if self.sessionid else 'N/A'


class WorkOrder(models.Model):
    # Assuming WONo is the primary key and unique identifier
    wono = models.CharField(db_column='WONo', max_length=50, primary_key=True)
    pono = models.ForeignKey(PurchaseOrder, on_delete=models.DO_NOTHING, db_column='PONo', to_field='pono', related_name='work_orders', null=True, blank=True)
    enqid = models.ForeignKey(Enquiry, on_delete=models.DO_NOTHING, db_column='EnqId', to_field='enqid', related_name='work_orders', null=True, blank=True)
    customerid = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='work_orders_from_wo', null=True, blank=True)
    sysdate = models.DateField(db_column='SysDate')
    sessionid = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='wos_generated', null=True, blank=True)
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='work_orders', null=True, blank=True)
    compid = models.IntegerField(db_column='CompId')

    objects = SalesReportManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono

    @property
    def fin_year(self):
        return self.finyearid.finyear if self.finyearid else 'N/A'
    
    @property
    def customer_name(self):
        return self.customerid.customername if self.customerid else 'N/A'

    @property
    def generated_by(self):
        return self.sessionid.employeename if self.sessionid else 'N/A'


class WorkOrderRelease(models.Model):
    # Assuming WRNo is the primary key and unique identifier, or adjust for composite key if needed
    wrno = models.CharField(db_column='WRNo', max_length=50, primary_key=True) 
    wono = models.ForeignKey(WorkOrder, on_delete=models.DO_NOTHING, db_column='WONo', to_field='wono', related_name='releases', null=True, blank=True)
    itemid = models.IntegerField(db_column='ItemId') # ItemId also present in this table, used in query

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Release'
        verbose_name = 'Work Order Release'
        verbose_name_plural = 'Work Order Releases'
        # If (WRNo, WONo, ItemId) is the actual composite key, unique_together should be defined:
        # unique_together = (('wrno', 'wono', 'itemid'),)

    def __str__(self):
        return self.wrno

class WorkOrderDispatch(models.Model):
    # Assuming 'Id' is the primary key for this table.
    id = models.IntegerField(db_column='Id', primary_key=True)
    wrno = models.ForeignKey(WorkOrderRelease, on_delete=models.DO_NOTHING, db_column='WRNo', to_field='wrno', related_name='dispatches', null=True, blank=True)
    sysdate = models.DateField(db_column='SysDate')
    finyearid = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='dispatches', null=True, blank=True)
    compid = models.IntegerField(db_column='CompId')
    itemid = models.IntegerField(db_column='ItemId')

    objects = SalesReportManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Dispatch'
        verbose_name = 'Work Order Dispatch'
        verbose_name_plural = 'Work Order Dispatches'

    def __str__(self):
        return f"Dispatch {self.id} for {self.wrno.wrno if self.wrno else 'N/A'}"
    
    @property
    def wono(self):
        # Traverse relationships: Dispatch -> Release -> WorkOrder
        return self.wrno.wono.wono if self.wrno and self.wrno.wono else 'N/A'

    @property
    def customer_name(self):
        # Traverse relationships: Dispatch -> Release -> WorkOrder -> Customer
        return self.wrno.wono.customerid.customername if self.wrno and self.wrno.wono and self.wrno.wono.customerid else 'N/A'
    
    @property
    def customer_id_str(self): # Renamed to avoid clash with customerid FK
        # Traverse relationships: Dispatch -> Release -> WorkOrder -> Customer
        return self.wrno.wono.customerid.customerid if self.wrno and self.wrno.wono and self.wrno.wono.customerid else 'N/A'

```

### 4.2 Forms (`sales_reports/forms.py`)

No forms are required for this module as it's a read-only reporting dashboard.

### 4.3 Views (`sales_reports/views.py`)

The views will handle the main page load and the HTMX requests for each table and chart data. A `TemplateView` will render the main dashboard. Separate `View` or `ListView` methods will serve the partial HTML for the tables and JSON data for the charts, ensuring thin view logic.

```python
from django.views.generic import TemplateView, ListView, View
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.conf import settings # To get company_id and finyear_id from settings/session
import json
import logging

from .models import (
    Enquiry, PurchaseOrder, WorkOrder, WorkOrderDispatch,
    FinancialYear # Potentially needed to get finyear display
)

logger = logging.getLogger(__name__)

# Dummy values for company_id and financial_year_id.
# In a real application, these would come from the user's session or profile.
# For demonstration purposes, we use placeholder values.
# Assuming session data would be retrieved like:
# company_id = request.session.get('compid', settings.DEFAULT_COMPANY_ID)
# finyear_id = request.session.get('finyear', settings.DEFAULT_FINYEAR_ID)
# For this example, let's hardcode for demonstration, or use a default from settings.
# Let's assume you have DEFAULT_COMPANY_ID and DEFAULT_FINYEAR_ID in Django settings.py
# If session management is in place, you would fetch these from request.session.

# Base class to handle common logic for company and financial year
class BaseReportView(View):
    def get_context_data(self, **kwargs):
        # In a real app, retrieve these from session, user profile, or configuration
        # For demonstration, we use placeholder values.
        # Replace with actual session retrieval:
        # company_id = self.request.session.get('compid', 1) 
        # finyear_id = self.request.session.get('finyear', 2023)
        # For simplicity in this example, let's assume valid default IDs.
        company_id = getattr(settings, 'DEFAULT_COMPANY_ID', 1) 
        finyear_id = getattr(settings, 'DEFAULT_FINYEAR_ID', 1) # Example ID, replace with actual

        context = super().get_context_data(**kwargs) if hasattr(super(), 'get_context_data') else {}
        context['company_id'] = company_id
        context['finyear_id'] = finyear_id
        
        try:
            fin_year_obj = FinancialYear.objects.get(finyearid=finyear_id)
            context['current_fin_year_display'] = fin_year_obj.finyear
        except FinancialYear.DoesNotExist:
            context['current_fin_year_display'] = "N/A" # Default if not found

        return context


class SalesDistributionDashboardView(BaseReportView, TemplateView):
    template_name = 'sales_reports/sales_distribution/dashboard.html'
    
    # This view is thin, just setting up the template and passing base context.
    # Content for tables and charts will be loaded via HTMX.


class EnquiryTablePartialView(BaseReportView, ListView):
    model = Enquiry
    template_name = 'sales_reports/sales_distribution/_enquiry_table.html'
    context_object_name = 'enquiries'

    def get_queryset(self):
        # Max 5 lines for get_queryset:
        company_id = self.get_context_data()['company_id']
        finyear_id = self.get_context_data()['finyear_id']
        return Enquiry.objects.get_filtered_report_data(company_id, finyear_id, order_by_field='enqid')


class POTablePartialView(BaseReportView, ListView):
    model = PurchaseOrder
    template_name = 'sales_reports/sales_distribution/_po_table.html'
    context_object_name = 'purchase_orders'

    def get_queryset(self):
        # Max 5 lines for get_queryset:
        company_id = self.get_context_data()['company_id']
        finyear_id = self.get_context_data()['finyear_id']
        return PurchaseOrder.objects.get_filtered_report_data(company_id, finyear_id, order_by_field='poid')


class WOTablePartialView(BaseReportView, ListView):
    model = WorkOrder
    template_name = 'sales_reports/sales_distribution/_wo_table.html'
    context_object_name = 'work_orders'

    def get_queryset(self):
        # Max 5 lines for get_queryset:
        company_id = self.get_context_data()['company_id']
        finyear_id = self.get_context_data()['finyear_id']
        return WorkOrder.objects.get_filtered_report_data(company_id, finyear_id, order_by_field='wono')


class DispatchTablePartialView(BaseReportView, ListView):
    model = WorkOrderDispatch
    template_name = 'sales_reports/sales_distribution/_dispatch_table.html'
    context_object_name = 'dispatches'

    def get_queryset(self):
        # Max 5 lines for get_queryset:
        company_id = self.get_context_data()['company_id']
        finyear_id = self.get_context_data()['finyear_id']
        # Dispatch order by 'Id' as per C# code
        return WorkOrderDispatch.objects.get_filtered_report_data(company_id, finyear_id, order_by_field='id')


class ChartDataView(BaseReportView, View):
    def get(self, request, chart_type, *args, **kwargs):
        # Max 5 lines in the main logic of get:
        company_id = self.get_context_data()['company_id']
        finyear_id = self.get_context_data()['finyear_id']
        current_fin_year_display = self.get_context_data()['current_fin_year_display']
        
        data = {}
        if chart_type == 'enquiry':
            data = Enquiry.objects.get_monthly_counts(company_id, finyear_id)
        elif chart_type == 'po':
            data = PurchaseOrder.objects.get_monthly_counts(company_id, finyear_id)
        elif chart_type == 'wo':
            data = WorkOrder.objects.get_monthly_counts(company_id, finyear_id)
        elif chart_type == 'dispatch':
            data = WorkOrderDispatch.objects.get_monthly_counts(company_id, finyear_id)
        
        data['title'] = f"Fin. Year {current_fin_year_display}"
        return JsonResponse(data)

```

### 4.4 Templates (`sales_reports/templates/sales_reports/sales_distribution/`)

This structure will facilitate HTMX partial loading.

**`dashboard.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6 text-center" style="background: url({% static 'images/hdbg.JPG' %}); color: white; padding: 10px;">
        Sales Distribution
    </h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <!-- Enquiry Section -->
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Enquiry Chart</h2>
            <div id="enquiry-chart-container" class="bg-gray-100 p-4 rounded-lg shadow-md flex items-center justify-center h-80">
                <canvas id="enquiryChart"></canvas>
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading chart...</p>
                </div>
            </div>
        </div>
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Enquiry Data</h2>
            <div id="enquiry-table-container" 
                 hx-get="{% url 'sales_reports:enquiry_table' %}" 
                 hx-trigger="load, refreshEnquiryData from:body"
                 hx-swap="innerHTML"
                 class="bg-white p-4 rounded-lg shadow-md min-h-[300px] flex items-center justify-center">
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading table...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <!-- PO Section -->
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Customer PO Chart</h2>
            <div id="po-chart-container" class="bg-gray-100 p-4 rounded-lg shadow-md flex items-center justify-center h-80">
                <canvas id="poChart"></canvas>
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading chart...</p>
                </div>
            </div>
        </div>
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Customer PO Data</h2>
            <div id="po-table-container" 
                 hx-get="{% url 'sales_reports:po_table' %}" 
                 hx-trigger="load, refreshPoData from:body"
                 hx-swap="innerHTML"
                 class="bg-white p-4 rounded-lg shadow-md min-h-[300px] flex items-center justify-center">
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading table...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <!-- Work Order Section -->
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Work Order Chart</h2>
            <div id="wo-chart-container" class="bg-gray-100 p-4 rounded-lg shadow-md flex items-center justify-center h-80">
                <canvas id="woChart"></canvas>
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading chart...</p>
                </div>
            </div>
        </div>
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Work Order Data</h2>
            <div id="wo-table-container" 
                 hx-get="{% url 'sales_reports:wo_table' %}" 
                 hx-trigger="load, refreshWoData from:body"
                 hx-swap="innerHTML"
                 class="bg-white p-4 rounded-lg shadow-md min-h-[300px] flex items-center justify-center">
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading table...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Dispatch Section -->
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Work Order Dispatch Chart</h2>
            <div id="dispatch-chart-container" class="bg-gray-100 p-4 rounded-lg shadow-md flex items-center justify-center h-80">
                <canvas id="dispatchChart"></canvas>
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading chart...</p>
                </div>
            </div>
        </div>
        <div>
            <h2 class="text-xl font-semibold mb-4 text-center">Work Order Dispatch Data</h2>
            <div id="dispatch-table-container" 
                 hx-get="{% url 'sales_reports:dispatch_table' %}" 
                 hx-trigger="load, refreshDispatchData from:body"
                 hx-swap="innerHTML"
                 class="bg-white p-4 rounded-lg shadow-md min-h-[300px] flex items-center justify-center">
                <div class="htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading table...</p>
                </div>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Function to fetch chart data and render chart
    async function renderChart(chartId, chartType, dataUrl, chartTitle, color) {
        try {
            const response = await fetch(dataUrl);
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const data = await response.json();
            
            const ctx = document.getElementById(chartId).getContext('2d');
            new Chart(ctx, {
                type: 'bar', // Column chart is 'bar' in Chart.js
                data: {
                    labels: data.labels,
                    datasets: [{
                        label: chartTitle,
                        data: data.data,
                        backgroundColor: color,
                        borderColor: color,
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            title: {
                                display: true,
                                text: `Fin. Year ${data.title.split(' ')[2]}` // Extract "2023-24" from "Fin. Year 2023-24"
                            },
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: chartTitle
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        } catch (error) {
            console.error(`Error loading chart data for ${chartId}:`, error);
            const container = document.getElementById(chartId).parentElement;
            container.innerHTML = `<p class="text-red-500 text-center">Failed to load chart: ${error.message}</p>`;
        }
    }

    // HTMX triggers for loading charts on page load
    document.addEventListener('htmx:afterLoad', (event) => {
        // Trigger chart rendering after the main dashboard content is loaded via HTMX
        // Or directly on initial page load if not using hx-get for the main dashboard content
        if (event.detail.target.id === 'sales-dashboard-main-content' || !event.detail.target) { // Adjust target ID if the whole dashboard is loaded via HTMX
            renderChart('enquiryChart', 'Enquiry', "{% url 'sales_reports:chart_data' 'enquiry' %}", 'Total No. of Enquiries', 'rgba(144, 238, 144, 0.8)'); // PaleGreen
            renderChart('poChart', 'PO', "{% url 'sales_reports:chart_data' 'po' %}", 'Total No. of PO', 'rgba(255, 239, 213, 0.8)'); // PapayaWhip
            renderChart('woChart', 'WO', "{% url 'sales_reports:chart_data' 'wo' %}", 'Total No. of Work Order', 'rgba(175, 238, 238, 0.8)'); // PaleTurquoise
            renderChart('dispatchChart', 'Dispatch', "{% url 'sales_reports:chart_data' 'dispatch' %}", 'Total No. of Dispatch', 'rgba(250, 128, 114, 0.8)'); // LightSalmon
        }
    });

    // Initial chart rendering on page load
    document.addEventListener('DOMContentLoaded', () => {
        renderChart('enquiryChart', 'Enquiry', "{% url 'sales_reports:chart_data' 'enquiry' %}", 'Total No. of Enquiries', 'rgba(144, 238, 144, 0.8)'); // PaleGreen
        renderChart('poChart', 'PO', "{% url 'sales_reports:chart_data' 'po' %}", 'Total No. of PO', 'rgba(255, 239, 213, 0.8)'); // PapayaWhip
        renderChart('woChart', 'WO', "{% url 'sales_reports:chart_data' 'wo' %}", 'Total No. of Work Order', 'rgba(175, 238, 238, 0.8)'); // PaleTurquoise
        renderChart('dispatchChart', 'Dispatch', "{% url 'sales_reports:chart_data' 'dispatch' %}", 'Total No. of Dispatch', 'rgba(250, 128, 114, 0.8)'); // LightSalmon
    });

</script>
{% endblock %}
```

**`_enquiry_table.html` (Partial for Enquiry DataTables)**

```html
<table id="enquiryTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr class="bg-gray-100 text-left">
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Fin Year</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Enq No</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Gen Date</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Gen By</th>
        </tr>
    </thead>
    <tbody>
        {% for enquiry in enquiries %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.fin_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ enquiry.customername }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.customerid.customerid|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.enqid }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.sysdate|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ enquiry.generated_by }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 text-center text-red-600 font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- DataTables Initialization Script -->
<script>
    $(document).ready(function() {
        $('#enquiryTable').DataTable({
            "pageLength": 15,
            "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`_po_table.html` (Partial for PO DataTables)**

```html
<table id="poTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr class="bg-gray-100 text-left">
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Enq No</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Gen Date</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Gen By</th>
        </tr>
    </thead>
    <tbody>
        {% for po in purchase_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.fin_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.customerid.customerid|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.pono }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.enqid.enqid|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.sysdate|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.generated_by }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 text-center text-red-600 font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        $('#poTable').DataTable({
            "pageLength": 15,
            "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`_wo_table.html` (Partial for WO DataTables)**

```html
<table id="woTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr class="bg-gray-100 text-left">
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Enquiry No</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">PO No</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Gen. By</th>
        </tr>
    </thead>
    <tbody>
        {% for wo in work_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ wo.fin_year }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ wo.customerid.customerid|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ wo.enqid.enqid|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ wo.pono.pono|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ wo.wono }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ wo.sysdate|date:"d-m-Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ wo.generated_by }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 text-center text-red-600 font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        $('#woTable').DataTable({
            "pageLength": 15,
            "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

**`_dispatch_table.html` (Partial for Dispatch DataTables)**

```html
<table id="dispatchTable" class="min-w-full bg-white border border-gray-300">
    <thead>
        <tr class="bg-gray-100 text-left">
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-300 text-xs font-medium text-gray-600 uppercase tracking-wider">Date of Dispatch</th>
        </tr>
    </thead>
    <tbody>
        {% for dispatch in dispatches %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ dispatch.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ dispatch.customer_id_str }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ dispatch.wono }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ dispatch.sysdate|date:"d-m-Y" }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 text-center text-red-600 font-bold">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        $('#dispatchTable').DataTable({
            "pageLength": 15,
            "lengthMenu": [[15, 25, 50, -1], [15, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

### 4.5 URLs (`sales_reports/urls.py`)

Defines the URL patterns for the main dashboard and the HTMX-loaded partials.

```python
from django.urls import path
from .views import (
    SalesDistributionDashboardView,
    EnquiryTablePartialView,
    POTablePartialView,
    WOTablePartialView,
    DispatchTablePartialView,
    ChartDataView,
)

app_name = 'sales_reports'

urlpatterns = [
    path('sales-distribution/', SalesDistributionDashboardView.as_view(), name='dashboard'),
    path('sales-distribution/enquiry-table/', EnquiryTablePartialView.as_view(), name='enquiry_table'),
    path('sales-distribution/po-table/', POTablePartialView.as_view(), name='po_table'),
    path('sales-distribution/wo-table/', WOTablePartialView.as_view(), name='wo_table'),
    path('sales-distribution/dispatch-table/', DispatchTablePartialView.as_view(), name='dispatch_table'),
    path('sales-distribution/chart-data/<str:chart_type>/', ChartDataView.as_view(), name='chart_data'),
]

```

### 4.6 Tests (`sales_reports/tests.py`)

Comprehensive tests for models (unit tests for methods and properties) and views (integration tests for HTTP responses, context, and HTMX interaction).

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch
import json
from .models import (
    FinancialYear, Employee, Customer,
    Enquiry, PurchaseOrder, WorkOrder, WorkOrderRelease, WorkOrderDispatch
)

# Mock settings for company_id and finyear_id for consistent testing
@patch.object(settings, 'DEFAULT_COMPANY_ID', 1)
@patch.object(settings, 'DEFAULT_FINYEAR_ID', 1)
class SalesReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create Financial Year and Employee test data
        cls.fin_year = FinancialYear.objects.create(finyearid=1, finyear='2023-24')
        cls.employee = Employee.objects.create(empid=1, employeename='John Doe')
        cls.customer = Customer.objects.create(customerid=101, customername='ABC Corp')

        # Create Enquiry test data
        cls.enquiry = Enquiry.objects.create(
            enqid='ENQ001', customername='ABC Corp', customerid=cls.customer,
            po_status='Open', sysdate='2023-05-15', sessionid=cls.employee,
            finyearid=cls.fin_year, compid=1
        )
        Enquiry.objects.create(
            enqid='ENQ002', customername='XYZ Ltd', customerid=cls.customer,
            po_status='Closed', sysdate='2023-06-20', sessionid=cls.employee,
            finyearid=cls.fin_year, compid=1
        )
        Enquiry.objects.create( # For chart data in another month
            enqid='ENQ003', customername='Test Co', customerid=cls.customer,
            po_status='Open', sysdate='2023-04-01', sessionid=cls.employee,
            finyearid=cls.fin_year, compid=1
        )


        # Create Purchase Order test data
        cls.po = PurchaseOrder.objects.create(
            poid='PO001', pono='PONO001', enqid=cls.enquiry, customerid=cls.customer,
            sysdate='2023-07-01', sessionid=cls.employee, finyearid=cls.fin_year, compid=1
        )
        PurchaseOrder.objects.create( # For chart data in another month
            poid='PO002', pono='PONO002', enqid=cls.enquiry, customerid=cls.customer,
            sysdate='2023-04-10', sessionid=cls.employee, finyearid=cls.fin_year, compid=1
        )

        # Create Work Order test data
        cls.wo = WorkOrder.objects.create(
            wono='WO001', pono=cls.po, enqid=cls.enquiry, customerid=cls.customer,
            sysdate='2023-08-01', sessionid=cls.employee, finyearid=cls.fin_year, compid=1
        )
        WorkOrder.objects.create( # For chart data in another month
            wono='WO002', pono=cls.po, enqid=cls.enquiry, customerid=cls.customer,
            sysdate='2023-04-20', sessionid=cls.employee, finyearid=cls.fin_year, compid=1
        )

        # Create Work Order Release test data
        cls.wo_release = WorkOrderRelease.objects.create(
            wrno='WR001', wono=cls.wo, itemid=1
        )

        # Create Work Order Dispatch test data
        cls.dispatch = WorkOrderDispatch.objects.create(
            id=1, wrno=cls.wo_release, sysdate='2023-09-01',
            finyearid=cls.fin_year, compid=1, itemid=1
        )
        WorkOrderDispatch.objects.create( # For chart data in another month
            id=2, wrno=cls.wo_release, sysdate='2023-04-25',
            finyearid=cls.fin_year, compid=1, itemid=2
        )


    def test_enquiry_properties(self):
        self.assertEqual(self.enquiry.fin_year, '2023-24')
        self.assertEqual(self.enquiry.generated_by, 'John Doe')
        self.assertEqual(self.enquiry.customerid.customername, 'ABC Corp')
        self.assertEqual(str(self.enquiry), 'ENQ001')

    def test_po_properties(self):
        self.assertEqual(self.po.fin_year, '2023-24')
        self.assertEqual(self.po.customer_name, 'ABC Corp')
        self.assertEqual(self.po.generated_by, 'John Doe')
        self.assertEqual(str(self.po), 'PONO001')

    def test_wo_properties(self):
        self.assertEqual(self.wo.fin_year, '2023-24')
        self.assertEqual(self.wo.customer_name, 'ABC Corp')
        self.assertEqual(self.wo.generated_by, 'John Doe')
        self.assertEqual(str(self.wo), 'WO001')

    def test_dispatch_properties(self):
        self.assertEqual(self.dispatch.wono, 'WO001')
        self.assertEqual(self.dispatch.customer_name, 'ABC Corp')
        self.assertEqual(self.dispatch.customer_id_str, 101)
        self.assertEqual(str(self.dispatch), 'Dispatch 1 for WR001')

    def test_enquiry_monthly_counts(self):
        counts = Enquiry.objects.get_monthly_counts(1, 1)
        self.assertIsInstance(counts, dict)
        self.assertIn('labels', counts)
        self.assertIn('data', counts)
        # Verify specific months with data (e.g., April, May, June)
        self.assertEqual(counts['data'][0], 1) # April
        self.assertEqual(counts['data'][1], 1) # May
        self.assertEqual(counts['data'][2], 1) # June

    def test_po_monthly_counts(self):
        counts = PurchaseOrder.objects.get_monthly_counts(1, 1)
        self.assertEqual(counts['data'][0], 1) # April
        self.assertEqual(counts['data'][3], 1) # July

    def test_wo_monthly_counts(self):
        counts = WorkOrder.objects.get_monthly_counts(1, 1)
        self.assertEqual(counts['data'][0], 1) # April
        self.assertEqual(counts['data'][4], 1) # August

    def test_dispatch_monthly_counts(self):
        counts = WorkOrderDispatch.objects.get_monthly_counts(1, 1)
        self.assertEqual(counts['data'][0], 1) # April
        self.assertEqual(counts['data'][5], 1) # September

    def test_get_filtered_report_data(self):
        enquiries = Enquiry.objects.get_filtered_report_data(1, 1, order_by_field='enqid')
        self.assertEqual(enquiries.count(), 3)
        self.assertEqual(enquiries.first().enqid, 'ENQ001')


@patch.object(settings, 'DEFAULT_COMPANY_ID', 1)
@patch.object(settings, 'DEFAULT_FINYEAR_ID', 1)
class SalesReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create Financial Year and Employee test data
        cls.fin_year = FinancialYear.objects.create(finyearid=1, finyear='2023-24')
        cls.employee = Employee.objects.create(empid=1, employeename='John Doe')
        cls.customer = Customer.objects.create(customerid=101, customername='ABC Corp')

        # Create Enquiry test data
        cls.enquiry = Enquiry.objects.create(
            enqid='ENQ001', customername='ABC Corp', customerid=cls.customer,
            po_status='Open', sysdate='2023-05-15', sessionid=cls.employee,
            finyearid=cls.fin_year, compid=1
        )

        # Create Purchase Order test data
        cls.po = PurchaseOrder.objects.create(
            poid='PO001', pono='PONO001', enqid=cls.enquiry, customerid=cls.customer,
            sysdate='2023-07-01', sessionid=cls.employee, finyearid=cls.fin_year, compid=1
        )

        # Create Work Order test data
        cls.wo = WorkOrder.objects.create(
            wono='WO001', pono=cls.po, enqid=cls.enquiry, customerid=cls.customer,
            sysdate='2023-08-01', sessionid=cls.employee, finyearid=cls.fin_year, compid=1
        )

        # Create Work Order Release test data
        cls.wo_release = WorkOrderRelease.objects.create(
            wrno='WR001', wono=cls.wo, itemid=1
        )

        # Create Work Order Dispatch test data
        cls.dispatch = WorkOrderDispatch.objects.create(
            id=1, wrno=cls.wo_release, sysdate='2023-09-01',
            finyearid=cls.fin_year, compid=1, itemid=1
        )

    def setUp(self):
        self.client = Client()

    def test_dashboard_view(self):
        response = self.client.get(reverse('sales_reports:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/sales_distribution/dashboard.html')
        self.assertContains(response, 'Sales Distribution')
        self.assertContains(response, 'id="enquiry-table-container"') # Check for HTMX target containers

    def test_enquiry_table_partial_view(self):
        response = self.client.get(reverse('sales_reports:enquiry_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/sales_distribution/_enquiry_table.html')
        self.assertIn('enquiries', response.context)
        self.assertContains(response, self.enquiry.enqid)
        self.assertContains(response, 'id="enquiryTable"') # Ensure DataTable element is present

    def test_po_table_partial_view(self):
        response = self.client.get(reverse('sales_reports:po_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/sales_distribution/_po_table.html')
        self.assertIn('purchase_orders', response.context)
        self.assertContains(response, self.po.pono)
        self.assertContains(response, 'id="poTable"')

    def test_wo_table_partial_view(self):
        response = self.client.get(reverse('sales_reports:wo_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/sales_distribution/_wo_table.html')
        self.assertIn('work_orders', response.context)
        self.assertContains(response, self.wo.wono)
        self.assertContains(response, 'id="woTable"')

    def test_dispatch_table_partial_view(self):
        response = self.client.get(reverse('sales_reports:dispatch_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_reports/sales_distribution/_dispatch_table.html')
        self.assertIn('dispatches', response.context)
        self.assertContains(response, self.dispatch.wono)
        self.assertContains(response, 'id="dispatchTable"')

    def test_chart_data_view_enquiry(self):
        response = self.client.get(reverse('sales_reports:chart_data', args=['enquiry']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('labels', data)
        self.assertIn('data', data)
        self.assertIn('title', data)
        self.assertEqual(data['data'][4], 1) # May enquiry count

    def test_chart_data_view_po(self):
        response = self.client.get(reverse('sales_reports:chart_data', args=['po']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('labels', data)
        self.assertIn('data', data)
        self.assertIn('title', data)
        self.assertEqual(data['data'][3], 1) # July PO count

    def test_chart_data_view_wo(self):
        response = self.client.get(reverse('sales_reports:chart_data', args=['wo']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('labels', data)
        self.assertIn('data', data)
        self.assertIn('title', data)
        self.assertEqual(data['data'][4], 1) # August WO count

    def test_chart_data_view_dispatch(self):
        response = self.client.get(reverse('sales_reports:chart_data', args=['dispatch']))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = json.loads(response.content)
        self.assertIn('labels', data)
        self.assertIn('data', data)
        self.assertIn('title', data)
        self.assertEqual(data['data'][5], 1) # September Dispatch count

    def test_chart_data_view_invalid_type(self):
        response = self.client.get(reverse('sales_reports:chart_data', args=['invalid_type']))
        self.assertEqual(response.status_code, 200) # Still 200, but data should be empty
        data = json.loads(response.content)
        self.assertEqual(data['data'], [0]*12) # All zeros if no data for type

```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX:** All tabular data (`_enquiry_table.html`, `_po_table.html`, `_wo_table.html`, `_dispatch_table.html`) are loaded into their respective containers (`dashboard.html`) using `hx-get` on `load` and custom `refresh` triggers. This provides the partial page update functionality previously handled by ASP.NET `UpdatePanel`.
*   **DataTables:** JavaScript for DataTables is included in each partial template (`<script>$(document).ready(function() {$('#tableId').DataTable();});</script>`). This ensures that DataTables initializes immediately after the HTMX content is swapped into the DOM, providing client-side sorting, searching, and pagination without further server interaction per change.
*   **Charts (Chart.js):** Chart.js is used for dynamic chart rendering. The `renderChart` JavaScript function fetches JSON data from Django views (`chart_data` endpoint) and dynamically draws the charts. This separates presentation from data retrieval, similar to the ASP.NET Chart controls. The JavaScript functions are initiated on `DOMContentLoaded` and `htmx:afterLoad` events to ensure charts load correctly after initial page render or subsequent HTMX updates if any part of the page containing the canvas is swapped.
*   **Alpine.js:** While not explicitly used in this read-only dashboard, Alpine.js would be ideal for simple UI state management, such as showing/hiding loading indicators or handling dynamic CSS classes, which are implicitly handled by HTMX's `htmx-indicator` pattern for now. No explicit Alpine.js components are introduced for this read-only page to maintain simplicity and meet the "no additional JavaScript" preference unless strictly necessary.

---

## Final Notes

*   **Placeholders:** Replace `DEFAULT_COMPANY_ID` and `DEFAULT_FINYEAR_ID` in `settings.py` with actual values or implement robust session/user-profile based retrieval in `BaseReportView`.
*   **Database Schema Precision:** The Django model field types and relationships (e.g., `primary_key`, `ForeignKey`, `null=True`, `blank=True`) are inferred based on common database patterns and the ASP.NET usage. During actual migration, this needs to be verified against the exact SQL Server schema (e.g., using `inspectdb` or direct schema analysis) to ensure perfect mapping.
*   **`SysDate` Handling:** Assumed `SysDate` columns are actual `DATE` types in SQL Server. If they are `VARCHAR`, a custom field or manual parsing in models/managers would be necessary.
*   **DRYness:** Templates are kept minimal and load reusable table structures via HTMX. Business logic is strictly contained within models.
*   **Test Coverage:** The provided tests offer a strong foundation. More edge cases and complex data scenarios should be added to reach 80% coverage.
*   **Frontend Libraries:** DataTables and Chart.js CDNs are included in `base.html` (implied) or `dashboard.html`. For production, these should be managed via npm/yarn and bundled for performance.
*   **Error Handling:** Basic error handling is included in the `renderChart` function. Robust error handling for data retrieval and display should be considered.