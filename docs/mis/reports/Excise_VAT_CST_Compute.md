## ASP.NET to Django Conversion Script: Excise/VAT/CST Computation

This modernization plan outlines the strategic transition of your legacy ASP.NET Excise/VAT/CST Computation module to a robust, scalable, and modern Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a systematic and efficient migration with minimal manual intervention. By adopting Django 5.0+, HTMX, Alpine.js, and DataTables, we will deliver a highly interactive, responsive, and maintainable application.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns involved in the ASP.NET code's data processing.

**Instructions:**
The ASP.NET code-behind performs complex SQL queries joining multiple tables. We will infer the necessary models and key fields directly used in the computation logic.

**Inferred Tables and Key Columns:**

*   **`tblACC_BillBooking_Details`**:
    *   `Id` (PK)
    *   `MId` (FK to `tblACC_BillBooking_Master`)
    *   `GQNId` (FK to `tblQc_MaterialQuality_Details`)
    *   `PODId` (FK to `tblMM_PO_Details`)
    *   `GSNId` (FK to `tblinv_MaterialServiceNote_Details`)
    *   `ExStBasic` (Decimal/Float)
    *   `ExStEducess` (Decimal/Float)
    *   `ExStShecess` (Decimal/Float)
    *   `VAT` (Decimal/Float)
    *   `CST` (Decimal/Float)
*   **`tblACC_BillBooking_Master`**:
    *   `Id` (PK)
    *   `CompId` (Integer)
    *   `SysDate` (DateTime)
*   **`tblMM_PO_Details`**:
    *   `Id` (PK)
    *   `MId` (FK to `tblMM_PO_Master`)
    *   `VAT` (Integer/FK to `tblVAT_Master`)
    *   `ExST` (Integer/FK to `tblExciseser_Master`)
*   **`tblMM_PO_Master`**:
    *   `Id` (PK)
    *   `SupplierId` (String)
*   **`tblACC_SalesInvoice_Details`**:
    *   `Id` (PK)
    *   `MId` (FK to `tblACC_SalesInvoice_Master`)
    *   `ReqQty` (Decimal/Float)
    *   `AmtInPer` (Decimal/Float)
    *   `Rate` (Decimal/Float)
    *   `PFType` (Integer)
    *   `PF` (Decimal/Float)
    *   `FreightType` (Integer)
    *   `Freight` (Decimal/Float)
    *   `CENVAT` (Integer/FK to `tblExciseser_Master`)
    *   `CST` (Integer/FK to `tblVAT_Master`)
    *   `VAT` (Integer/FK to `tblVAT_Master`)
*   **`tblACC_SalesInvoice_Master`**:
    *   `Id` (PK)
    *   `CompId` (Integer)
    *   `DateOfIssueInvoice` (DateTime)
*   **`tblVAT_Master`**:
    *   `Id` (PK)
    *   `Terms` (String)
    *   `Value` (Decimal/Float)
    *   `IsVAT` (Boolean/Integer)
    *   `IsCST` (Boolean/Integer)
*   **`tblExciseser_Master`**:
    *   `Id` (PK)
    *   `Terms` (String)
    *   `Value` (Decimal/Float)

**(Note: `tblQc_MaterialQuality_Details` and `tblinv_MaterialServiceNote_Details` are mentioned in joins but no specific fields are accessed directly for computation in the provided code snippet, so we will define them minimally for relationship purposes.)**

### Step 2: Identify Backend Functionality

Task: Determine the core operations and data processing performed by the ASP.NET code.

**Instructions:**

*   **Read/Report Generation:** The primary functionality is to generate an Excise/VAT/CST computation report. This involves:
    *   Fetching data from multiple tables (Bill Booking, Purchase Order, Sales Invoice, VAT Master, Excise Master).
    *   Aggregating purchase-related VAT, CST, and Excise duties based on different terms.
    *   Aggregating sales-related VAT, CST, and Excise duties, including calculations for PF (packing freight) and Freight.
    *   Grouping the aggregated amounts by their respective terms (e.g., VAT 5%, CST 2%, Excise 12.5%).
    *   Presenting six distinct summarized tables: Purchase VAT, Purchase CST, Purchase Excise, Sales VAT, Sales CST, Sales Excise.
*   **Input Handling:** Takes a date range (`Date From`, `Date To`).
*   **Validation:** Basic date format and range validation.
*   **Initial Load:** Sets default date range to the previous month.

### Step 3: Infer UI Components

Task: Analyze the ASP.NET controls and their intended roles in the user interface.

**Instructions:**

*   **Date Pickers:** `TextBox3` and `TextBox4` with `CalendarExtender` for date input. In Django, these will be HTML5 `input type="date"` fields, possibly enhanced by Alpine.js for a richer picker if desired (though not strictly required for basic functionality).
*   **Search Button:** `Btnsearch1` for initiating the computation. This will be an HTMX-powered button triggering a form submission.
*   **Tabbed Interface:** `TabContainer1` with `TabPanel1` (labeled "Excise/VAT/CST Computation"). This can be easily recreated using Alpine.js for tab switching, showing/hiding different report sections.
*   **Data Display:** `GridView1` (though not explicitly bound in ASPX, the C# code populates a Crystal Report Viewer). This will be replaced by multiple HTML tables, each enhanced by DataTables for client-side interactivity (search, sort, paginate).
*   **Report Viewer:** `CrystalReportViewer3` will be replaced by direct rendering of the computed data into standard HTML tables.
*   **Validation Messages:** `RequiredFieldValidator`, `RegularExpressionValidator`, `CompareValidator` and `Label2` for error messages. In Django, this will be handled by form validation errors displayed next to fields, and general messages displayed via Django's messages framework.

### Step 4: Generate Django Code

We will structure the Django application within a module, let's call it `mis_reports`.

#### 4.1 Models (`mis_reports/models.py`)

Task: Create Django models mapping to the identified database tables and implement the complex computation logic using a dedicated manager or service class.

**Instructions:**

- Define fields with appropriate Django types.
- Set `managed = False` and `db_table` for existing tables.
- Implement a `ComputationReportManager` (or similar) to encapsulate the `cryrpt_create2` logic. This manager will perform the complex queries, calculations, and aggregations.

```python
# mis_reports/models.py
from django.db import models, connection
from datetime import datetime, date
from django.db.models import Sum, F, ExpressionWrapper, DecimalField
from django.db.models.functions import Coalesce

# Placeholder models for existing database tables
# These models are set to 'managed = False' because they represent existing tables.
# You would define all relevant fields based on your full schema.
# For this example, only fields explicitly used in the ASP.NET queries are included.

class VatMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=100)
    value = models.DecimalField(db_column='Value', max_digits=18, decimal_places=4)
    is_vat = models.BooleanField(db_column='IsVAT')
    is_cst = models.BooleanField(db_column='IsCST')

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT/CST Master'
        verbose_name_plural = 'VAT/CST Masters'

    def __str__(self):
        return self.terms

class ExciseMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=100)
    value = models.DecimalField(db_column='Value', max_digits=18, decimal_places=4)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service Master'
        verbose_name_plural = 'Excise Service Masters'

    def __str__(self):
        return self.terms

class BillBookingMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    sys_date = models.DateTimeField(db_column='SysDate')

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'

class BillBookingDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(BillBookingMaster, on_delete=models.DO_NOTHING, db_column='MId')
    po_detail = models.ForeignKey('PODetail', on_delete=models.DO_NOTHING, db_column='PODId')
    # GQNId and GSNId were in the original queries but not directly used for value computation,
    # so we'll omit them for brevity in this example.
    # gq_note = models.ForeignKey('MaterialQualityDetail', on_delete=models.DO_NOTHING, db_column='GQNId', null=True, blank=True)
    # material_service_note = models.ForeignKey('MaterialServiceNoteDetail', on_delete=models.DO_NOTHING, db_column='GSNId', null=True, blank=True)
    ex_st_basic = models.DecimalField(db_column='ExStBasic', max_digits=18, decimal_places=4, default=0)
    ex_st_educess = models.DecimalField(db_column='ExStEducess', max_digits=18, decimal_places=4, default=0)
    ex_st_shecess = models.DecimalField(db_column='ExStShecess', max_digits=18, decimal_places=4, default=0)
    vat_amount = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=4, default=0) # Note: This is an amount, not an ID in tblACC_BillBooking_Details
    cst_amount = models.DecimalField(db_column='CST', max_digits=18, decimal_places=4, default=0) # Note: This is an amount, not an ID in tblACC_BillBooking_Details

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Details'

class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'

class PODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(POMaster, on_delete=models.DO_NOTHING, db_column='MId')
    vat = models.ForeignKey(VatMaster, on_delete=models.DO_NOTHING, db_column='VAT', related_name='po_details_vat')
    ex_st = models.ForeignKey(ExciseMaster, on_delete=models.DO_NOTHING, db_column='ExST', related_name='po_details_exst')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'

class SalesInvoiceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    date_of_issue_invoice = models.DateTimeField(db_column='DateOfIssueInvoice')

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'

class SalesInvoiceDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(SalesInvoiceMaster, on_delete=models.DO_NOTHING, db_column='MId')
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=4, default=0)
    amt_in_per = models.DecimalField(db_column='AmtInPer', max_digits=18, decimal_places=4, default=0)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4, default=0)
    pf_type = models.IntegerField(db_column='PFType', default=0) # 0 for fixed, 1 for percentage
    pf_amount = models.DecimalField(db_column='PF', max_digits=18, decimal_places=4, default=0)
    freight_type = models.IntegerField(db_column='FreightType', default=0) # 0 for fixed, 1 for percentage
    freight_amount = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=4, default=0)
    cenvat = models.ForeignKey(ExciseMaster, on_delete=models.DO_NOTHING, db_column='CENVAT', null=True, blank=True, related_name='sales_details_cenvat')
    cst = models.ForeignKey(VatMaster, on_delete=models.DO_NOTHING, db_column='CST', null=True, blank=True, related_name='sales_details_cst')
    vat = models.ForeignKey(VatMaster, on_delete=models.DO_NOTHING, db_column='VAT', null=True, blank=True, related_name='sales_details_vat')

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Details'

# Dummy models for tables mentioned in joins but not directly used for computation values
class MaterialQualityDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'

class MaterialServiceNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    class Meta:
        managed = False
        db_table = 'tblinv_MaterialServiceNote_Details'


class ComputationReportManager(models.Manager):
    """
    Manages complex data retrieval and aggregation for the Excise/VAT/CST report.
    This encapsulates the 'cryrpt_create2' logic from the C# code.
    """
    def compute_excise_vat_cst(self, comp_id: int, from_date: date, to_date: date):
        """
        Computes Excise, VAT, and CST for both Purchase and Sales.
        Returns a dictionary containing 6 lists of aggregated data.
        """
        results = {
            'purchase_vat': [],
            'purchase_cst': [],
            'purchase_excise': [],
            'sales_vat': [],
            'sales_cst': [],
            'sales_excise': [],
            'vat_gross_total': 0.0 # This was a separate parameter in Crystal Report
        }

        # --- Purchase Computation (Combines Cmdgrid1 and Cmdgrid logic) ---
        # The original SQL queries used a UNION-like effect by merging two datasets.
        # We'll try to achieve this with ORM or a single more complex query.
        # Given the complexity of the original SQL and the need for aggregation
        # based on dynamically fetched VAT/Excise terms, direct ORM can be verbose.
        # A more 'Django-idiomatic' way for complex reports sometimes involves
        # raw SQL queries if ORM becomes too unwieldy, but still keeping it in the manager.

        # Example of how Purchase data would be processed,
        # abstracting the very complex original multi-join query.
        # This section simulates the aggregation logic.
        
        # Purchase Bill Booking Details (Approximation of original query logic)
        purchase_data = BillBookingDetail.objects.filter(
            master__comp_id=comp_id,
            master__sys_date__range=(from_date, to_date)
        ).exclude(
            po_detail__master__supplier_id='S0098'
        ).values(
            'po_detail__vat__terms',
            'po_detail__vat__is_vat',
            'po_detail__vat__is_cst',
            'po_detail__ex_st__terms',
        ).annotate(
            total_ex_st_basic=Sum('ex_st_basic'),
            total_ex_st_educess=Sum('ex_st_educess'),
            total_ex_st_shecess=Sum('ex_st_shecess'),
            total_vat_amount=Sum('vat_amount'),
            total_cst_amount=Sum('cst_amount')
        ).order_by() # Remove any default ordering for correct grouping

        # Process aggregated purchase data
        # Mimicking the C# logic of processing `dsrs` and then grouping into `targetDt` etc.
        purchase_vat_temp = {}
        purchase_cst_temp = {}
        purchase_excise_temp = {}

        for row in purchase_data:
            vat_cst_total = 0.0
            total_excise = (
                (row['total_ex_st_basic'] or 0) +
                (row['total_ex_st_educess'] or 0) +
                (row['total_ex_st_shecess'] or 0)
            )

            if row['po_detail__vat__is_vat']:
                vat_cst_total = (row['total_vat_amount'] or 0)
            elif row['po_detail__vat__is_cst']:
                vat_cst_total = (row['total_cst_amount'] or 0)
            else: # If neither VAT nor CST, use VAT amount as fallback (as per C# logic)
                vat_cst_total = (row['total_vat_amount'] or 0)
            
            if row['po_detail__ex_st__terms']:
                term = row['po_detail__ex_st__terms']
                purchase_excise_temp[term] = purchase_excise_temp.get(term, 0) + float(total_excise)
            
            if row['po_detail__vat__is_cst']:
                term = row['po_detail__vat__terms']
                purchase_cst_temp[term] = purchase_cst_temp.get(term, 0) + float(vat_cst_total)
            else:
                term = row['po_detail__vat__terms']
                purchase_vat_temp[term] = purchase_vat_temp.get(term, 0) + float(vat_cst_total)

        for term, amt in purchase_vat_temp.items():
            results['purchase_vat'].append({'VATerms': term, 'VATAmt': round(amt, 2)})
        for term, amt in purchase_cst_temp.items():
            results['purchase_cst'].append({'CSTTerms': term, 'CSTAmt': round(amt, 2)})
        for term, amt in purchase_excise_temp.items():
            results['purchase_excise'].append({'ExciseTerm': term, 'ExBasicAmt': round(amt, 2)})

        # --- Sales Computation ---
        sales_details = SalesInvoiceDetail.objects.filter(
            master__comp_id=comp_id,
            master__date_of_issue_invoice__range=(from_date, to_date)
        ).annotate(
            # Calculate base amount (ReqQty * AmtInPer/100 * Rate)
            basic_amt=ExpressionWrapper(
                F('req_qty') * F('amt_in_per') / 100 * F('rate'),
                output_field=DecimalField()
            )
        ).order_by('master__id') # Original code had an order by Master Id

        # Count total sales details for freight/PF division (simulates cnt from C#)
        total_sales_details_count = sales_details.count()
        
        sales_vat_temp = {}
        sales_cst_temp = {}
        sales_excise_temp = {}
        vat_gross_total_sum = 0.0

        for detail in sales_details:
            basic_amt = detail.basic_amt or 0
            
            pf = 0.0
            if detail.pf_type == 0: # Fixed amount
                pf = detail.pf_amount or 0
            else: # Percentage
                pf = (basic_amt) * (detail.pf_amount or 0) / 100
            
            freight = 0.0
            if detail.freight_type == 0: # Fixed amount
                freight = detail.freight_amount or 0
            else: # Percentage
                freight = (basic_amt) * (detail.freight_amount or 0) / 100

            # Adjust PF and Freight by dividing by total count if > 0
            y = float(pf) / total_sales_details_count if total_sales_details_count > 0 else 0
            x = float(freight) / total_sales_details_count if total_sales_details_count > 0 else 0

            amt_after_pf = float(basic_amt) + y

            excise_amount = 0.0
            if detail.cenvat and detail.cenvat.value is not None:
                excise_amount = (amt_after_pf * float(detail.cenvat.value) / 100)
                sales_excise_temp[detail.cenvat.terms] = sales_excise_temp.get(detail.cenvat.terms, 0) + excise_amount
            
            amt_after_excise = amt_after_pf + excise_amount

            if detail.vat and detail.vat.value is not None:
                vat_calc_amt = (amt_after_excise + x) * float(detail.vat.value) / 100
                sales_vat_temp[detail.vat.terms] = sales_vat_temp.get(detail.vat.terms, 0) + vat_calc_amt
                vat_gross_total_sum += round(vat_calc_amt, 2)
            elif detail.cst and detail.cst.value is not None:
                cst_calc_amt = (amt_after_excise * float(detail.cst.value) / 100) + x
                sales_cst_temp[detail.cst.terms] = sales_cst_temp.get(detail.cst.terms, 0) + cst_calc_amt
            
        for term, amt in sales_vat_temp.items():
            results['sales_vat'].append({'VATTerm': term, 'Amt': round(amt, 2)})
        for term, amt in sales_cst_temp.items():
            results['sales_cst'].append({'CSTTerm': term, 'CSTAmt': round(amt, 2)})
        for term, amt in sales_excise_temp.items():
            results['sales_excise'].append({'EXTerm': term, 'EXAmt': round(amt, 2)})
        
        results['vat_gross_total'] = round(vat_gross_total_sum, 2)

        return results

# Attach the manager to a dummy model or use it as a standalone service
# For a 'fat model' approach, we can attach it to a relevant model,
# or create a dedicated 'Report' model if the report logic becomes very central.
# Here, we'll make it a static method or a service function for simplicity,
# as it doesn't directly operate on instances of a specific model.
# But adhering to "fat model", we can create a proxy model for reporting.

class ReportComputation(models.Model):
    """
    A proxy model or service container for report computation logic.
    This demonstrates putting complex 'business logic' into the 'model' layer.
    """
    objects = ComputationReportManager()

    class Meta:
        managed = False # This model won't correspond to a real database table
        verbose_name = 'Report Computation'
        verbose_name_plural = 'Report Computations'
```

#### 4.2 Forms (`mis_reports/forms.py`)

Task: Define a Django form for the date range input.

**Instructions:**

- Create a `forms.Form` with `from_date` and `to_date` fields.
- Implement custom validation to ensure `from_date` is not after `to_date`.
- Add Tailwind CSS classes to widgets.

```python
# mis_reports/forms.py
from django import forms
from datetime import date
from django.core.exceptions import ValidationError

class DateRangeForm(forms.Form):
    from_date = forms.DateField(
        label="Date From",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }, format='%Y-%m-%d'),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'] # Allow multiple input formats
    )
    to_date = forms.DateField(
        label="To",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }, format='%Y-%m-%d'),
        input_formats=['%Y-%m-%d', '%d-%m-%Y']
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date:
            if from_date > to_date:
                raise ValidationError("Invalid selected date range. 'Date From' cannot be after 'To Date'.")
        return cleaned_data
    
    def get_initial_last_month_dates(self):
        """
        Helper method to get the first and last day of the previous month.
        Mimics ASP.NET's FirstDateInLastMonth and LastDateInLastMonth.
        """
        today = date.today()
        # Go back to the first day of the current month
        first_day_of_current_month = today.replace(day=1)
        # Go back one day to get to the last day of the previous month
        last_day_of_last_month = first_day_of_current_month - forms.widgets.Timedelta(days=1)
        # Get the first day of the previous month
        first_day_of_last_month = last_day_of_last_month.replace(day=1)
        
        return first_day_of_last_month, last_day_of_last_month

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if not self.is_bound: # Only set initial values if form is not submitted
            first_date, last_date = self.get_initial_last_month_dates()
            self.fields['from_date'].initial = first_date
            self.fields['to_date'].initial = last_date

```

#### 4.3 Views (`mis_reports/views.py`)

Task: Implement the main report view and a partial view for HTMX-loaded report tables.

**Instructions:**

- Use `FormView` for the main page to handle date input.
- Use a simple `TemplateView` or `View` for the HTMX partial that renders the report results.
- Keep views thin; delegate complex data fetching to the `ComputationReportManager`.
- Use Django messages for feedback.

```python
# mis_reports/views.py
from django.views.generic import FormView, TemplateView
from django.http import HttpResponse, JsonResponse
from django.urls import reverse_lazy
from django.contrib import messages
from datetime import date
from .forms import DateRangeForm
from .models import ReportComputation # Our proxy model with the manager

class ExciseVatCstReportView(FormView):
    """
    Main view for the Excise/VAT/CST computation report.
    Handles the date range form and renders the initial page.
    """
    template_name = 'mis_reports/computation_report_list.html'
    form_class = DateRangeForm
    # We don't need a success_url as HTMX will swap parts of the page.

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initial empty report data for when the page loads
        context['report_data'] = {
            'purchase_vat': [], 'purchase_cst': [], 'purchase_excise': [],
            'sales_vat': [], 'sales_cst': [], 'sales_excise': [],
            'vat_gross_total': 0.0
        }
        # In a real app, comp_id would come from request.user or session
        context['comp_id'] = 1 # Example company ID
        context['initial_load'] = True # Flag for template to decide if it should trigger hx-get on load
        return context

    def form_valid(self, form):
        """
        Handles valid form submission via HTMX.
        This method will likely not be called for HTMX GET requests,
        but for full page POSTs or if HTMX is used for POST.
        For this report, we'll let `report_tables_partial` handle the actual data fetching.
        """
        # If this view directly triggers the calculation, it would be here.
        # But for HTMX, the partial view will be responsible for fetching data
        # based on parameters passed to it (e.g., via query params).
        # We'll just return a success message or trigger a refresh.
        messages.success(self.request, "Report parameters updated. Loading report...")
        return HttpResponse(
            status=204, # No Content
            headers={
                'HX-Trigger': 'refreshReportData' # Custom HTMX event to trigger the partial view
            }
        )

    def form_invalid(self, form):
        """
        Handles invalid form submission.
        For HTMX, we want to return the form with errors.
        """
        if self.request.headers.get('HX-Request'):
            # If it's an HTMX request, render only the form part with errors
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class ExciseVatCstReportTablePartialView(TemplateView):
    """
    HTMX-specific view to render only the report tables.
    It receives date range via GET parameters.
    """
    template_name = 'mis_reports/_computation_report_tables.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Get dates from GET parameters (e.g., /report/tables/?from_date=...&to_date=...)
        from_date_str = self.request.GET.get('from_date')
        to_date_str = self.request.GET.get('to_date')

        from_date = None
        to_date = None

        try:
            # Default to previous month if dates are not provided, or invalid
            if from_date_str and to_date_str:
                from_date = datetime.strptime(from_date_str, '%Y-%m-%d').date()
                to_date = datetime.strptime(to_date_str, '%Y-%m-%d').date()
            else:
                # Use default dates from the form's logic if not provided
                form_instance = DateRangeForm()
                first_date, last_date = form_instance.get_initial_last_month_dates()
                from_date = first_date
                to_date = last_date

        except ValueError:
            messages.error(self.request, "Invalid date format. Please use YYYY-MM-DD.")
            from_date = date.today()
            to_date = date.today() # Fallback to today to avoid errors

        # In a real app, comp_id would come from request.user.company.id or similar
        comp_id = 1 # Example company ID
        
        # Call the ComputationReportManager to get the data
        try:
            report_data = ReportComputation.objects.compute_excise_vat_cst(comp_id, from_date, to_date)
            context['report_data'] = report_data
            context['report_date_from'] = from_date.strftime('%d-%m-%Y')
            context['report_date_to'] = to_date.strftime('%d-%m-%Y')
            if not any(report_data.values()): # Check if all report data lists are empty
                messages.info(self.request, "No records found for the selected date range.")
        except Exception as e:
            messages.error(self.request, f"An error occurred while generating the report: {e}")
            context['report_data'] = {} # Empty data if error occurs

        return context

```

#### 4.4 Templates (`mis_reports/templates/mis_reports/`)

Task: Create the main list template and a partial template for the report tables, integrating HTMX, Alpine.js, and DataTables.

**Instructions:**

- `computation_report_list.html` extends `core/base.html`.
- `_computation_report_tables.html` contains the actual tables and DataTables initialization.
- Use Alpine.js for tab switching and simple UI state.
- Ensure HTMX attributes (`hx-get`, `hx-target`, `hx-swap`, `hx-trigger`) are correctly placed.

```html
{# mis_reports/templates/mis_reports/computation_report_list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Excise/VAT/CST Computation Report</h2>

        <form id="dateRangeForm" 
              hx-get="{% url 'excise_vat_cst_report_tables' %}"
              hx-target="#reportTablesContainer"
              hx-swap="innerHTML"
              hx-indicator="#loadingIndicator"
              hx-trigger="submit, load from:body, refreshReportData from:body"
              hx-include="#from_date, #to_date" {# Automatically includes form fields based on IDs #}
              class="space-y-4">
            {% csrf_token %}
            <div class="flex items-end space-x-4">
                <div class="flex-1">
                    <label for="id_from_date" class="block text-sm font-medium text-gray-700">Date From:</label>
                    <input type="date" name="from_date" id="id_from_date" 
                           value="{{ form.from_date.initial|date:'Y-m-d' }}" 
                           class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    {% if form.from_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>
                    {% endif %}
                </div>
                <div class="flex-1">
                    <label for="id_to_date" class="block text-sm font-medium text-gray-700">To:</label>
                    <input type="date" name="to_date" id="id_to_date" 
                           value="{{ form.to_date.initial|date:'Y-m-d' }}" 
                           class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                    {% if form.to_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>
                    {% endif %}
                </div>
                <div>
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Search
                    </button>
                </div>
            </div>
            {% if form.non_field_errors %}
                <div class="text-red-500 text-xs mt-1">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </form>
    </div>

    <div id="loadingIndicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Generating report...</p>
    </div>

    <div id="reportTablesContainer">
        {# Report tables will be loaded here via HTMX #}
        {% if initial_load %}
            <div class="text-center text-gray-500">
                <p>Select dates and click "Search" to generate the report.</p>
            </div>
        {% endif %}
    </div>

</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here for the main page,
        // as tabs are handled within the partial template.
        // Date inputs directly use HTML5 type="date".
    });

    // Helper for setting default date values when loading the page
    document.addEventListener('DOMContentLoaded', function() {
        const fromDateInput = document.getElementById('id_from_date');
        const toDateInput = document.getElementById('id_to_date');

        // Check if values are already set (e.g. from form errors retaining values)
        if (!fromDateInput.value || !toDateInput.value) {
            // Function to calculate first and last day of previous month
            function getPreviousMonthDates() {
                const today = new Date();
                const firstDayOfCurrentMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                const lastDayOfPreviousMonth = new Date(firstDayOfCurrentMonth.getTime() - 1);
                const firstDayOfPreviousMonth = new Date(lastDayOfPreviousMonth.getFullYear(), lastDayOfPreviousMonth.getMonth(), 1);
                
                return {
                    from: firstDayOfPreviousMonth.toISOString().slice(0, 10),
                    to: lastDayOfPreviousMonth.toISOString().slice(0, 10)
                };
            }

            const dates = getPreviousMonthDates();
            if (!fromDateInput.value) fromDateInput.value = dates.from;
            if (!toDateInput.value) toDateInput.value = dates.to;
        }
    });

</script>
{% endblock %}
```

```html
{# mis_reports/templates/mis_reports/_computation_report_tables.html #}
<div x-data="{ activeTab: 'purchase_vat' }" class="bg-white shadow-md rounded-lg p-6">
    <div class="flex border-b border-gray-200 mb-6">
        <button @click="activeTab = 'purchase_vat'" 
                :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'purchase_vat', 'text-gray-600 hover:text-gray-800': activeTab !== 'purchase_vat' }"
                class="py-2 px-4 text-sm font-medium focus:outline-none">
            Purchase VAT
        </button>
        <button @click="activeTab = 'purchase_cst'" 
                :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'purchase_cst', 'text-gray-600 hover:text-gray-800': activeTab !== 'purchase_cst' }"
                class="py-2 px-4 text-sm font-medium focus:outline-none">
            Purchase CST
        </button>
        <button @click="activeTab = 'purchase_excise'" 
                :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'purchase_excise', 'text-gray-600 hover:text-gray-800': activeTab !== 'purchase_excise' }"
                class="py-2 px-4 text-sm font-medium focus:outline-none">
            Purchase Excise
        </button>
        <button @click="activeTab = 'sales_vat'" 
                :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'sales_vat', 'text-gray-600 hover:text-gray-800': activeTab !== 'sales_vat' }"
                class="py-2 px-4 text-sm font-medium focus:outline-none">
            Sales VAT
        </button>
        <button @click="activeTab = 'sales_cst'" 
                :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'sales_cst', 'text-gray-600 hover:text-gray-800': activeTab !== 'sales_cst' }"
                class="py-2 px-4 text-sm font-medium focus:outline-none">
            Sales CST
        </button>
        <button @click="activeTab = 'sales_excise'" 
                :class="{ 'border-b-2 border-blue-600 text-blue-600': activeTab === 'sales_excise', 'text-gray-600 hover:text-gray-800': activeTab !== 'sales_excise' }"
                class="py-2 px-4 text-sm font-medium focus:outline-none">
            Sales Excise
        </button>
    </div>

    {% if report_data %}
        <p class="text-sm text-gray-600 mb-4">Report for: <strong>{{ report_date_from }}</strong> - <strong>{{ report_date_to }}</strong></p>
        <p class="text-base font-semibold text-gray-700 mb-4">Overall VAT Gross Total: <span class="text-green-700">${{ report_data.vat_gross_total|floatformat:2 }}</span></p>

        {# Purchase VAT Table #}
        <div x-show="activeTab === 'purchase_vat'" class="overflow-x-auto">
            <h3 class="text-lg font-semibold mb-3">Purchase VAT Summary</h3>
            {% if report_data.purchase_vat %}
            <table id="purchaseVatTable" class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">VAT Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.purchase_vat %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.VATerms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.VATAmt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
                <p class="text-gray-500">No Purchase VAT data found for this period.</p>
            {% endif %}
        </div>

        {# Purchase CST Table #}
        <div x-show="activeTab === 'purchase_cst'" class="overflow-x-auto">
            <h3 class="text-lg font-semibold mb-3">Purchase CST Summary</h3>
            {% if report_data.purchase_cst %}
            <table id="purchaseCstTable" class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CST Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">CST Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.purchase_cst %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.CSTTerms }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.CSTAmt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
                <p class="text-gray-500">No Purchase CST data found for this period.</p>
            {% endif %}
        </div>

        {# Purchase Excise Table #}
        <div x-show="activeTab === 'purchase_excise'" class="overflow-x-auto">
            <h3 class="text-lg font-semibold mb-3">Purchase Excise Summary</h3>
            {% if report_data.purchase_excise %}
            <table id="purchaseExciseTable" class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.purchase_excise %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.ExciseTerm }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.ExBasicAmt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
                <p class="text-gray-500">No Purchase Excise data found for this period.</p>
            {% endif %}
        </div>

        {# Sales VAT Table #}
        <div x-show="activeTab === 'sales_vat'" class="overflow-x-auto">
            <h3 class="text-lg font-semibold mb-3">Sales VAT Summary</h3>
            {% if report_data.sales_vat %}
            <table id="salesVatTable" class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">VAT Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.sales_vat %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.VATTerm }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.Amt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
                <p class="text-gray-500">No Sales VAT data found for this period.</p>
            {% endif %}
        </div>

        {# Sales CST Table #}
        <div x-show="activeTab === 'sales_cst'" class="overflow-x-auto">
            <h3 class="text-lg font-semibold mb-3">Sales CST Summary</h3>
            {% if report_data.sales_cst %}
            <table id="salesCstTable" class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CST Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">CST Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.sales_cst %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.CSTTerm }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.CSTAmt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
                <p class="text-gray-500">No Sales CST data found for this period.</p>
            {% endif %}
        </div>

        {# Sales Excise Table #}
        <div x-show="activeTab === 'sales_excise'" class="overflow-x-auto">
            <h3 class="text-lg font-semibold mb-3">Sales Excise Summary</h3>
            {% if report_data.sales_excise %}
            <table id="salesExciseTable" class="min-w-full bg-white border border-gray-200">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Terms</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Excise Amount</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in report_data.sales_excise %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ item.EXTerm }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.EXAmt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% else %}
                <p class="text-gray-500">No Sales Excise data found for this period.</p>
            {% endif %}
        </div>

    {% else %}
        <p class="text-gray-500">No report data available. Please adjust your search criteria.</p>
    {% endif %}
</div>

<script>
    // Initialize DataTables for each table that will be displayed
    // This script should be run after the content is swapped in by HTMX
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'reportTablesContainer') {
            // Check if DataTables library is loaded and then initialize
            if (typeof $.fn.DataTable !== 'undefined') {
                $('#purchaseVatTable').DataTable({ "pageLength": 5 });
                $('#purchaseCstTable').DataTable({ "pageLength": 5 });
                $('#purchaseExciseTable').DataTable({ "pageLength": 5 });
                $('#salesVatTable').DataTable({ "pageLength": 5 });
                $('#salesCstTable').DataTable({ "pageLength": 5 });
                $('#salesExciseTable').DataTable({ "pageLength": 5 });
            } else {
                console.warn('DataTables library not found. Ensure it is loaded in base.html.');
            }
        }
    });

    // Initial load/refresh of report data when page loads or refreshReportData event fires
    document.addEventListener('DOMContentLoaded', function() {
        const dateRangeForm = document.getElementById('dateRangeForm');
        if (dateRangeForm) {
            // Submit form to trigger the initial report load through hx-trigger="load"
            // This is implicitly handled by hx-trigger="load from:body" on the form.
            // If the initial form values are valid, it will trigger an HTMX GET request.
        }
    });
</script>
```

#### 4.5 URLs (`mis_reports/urls.py`)

Task: Define URL patterns for the views.

**Instructions:**

- Create paths for the main report view and the HTMX-specific partial view.
- Use a consistent naming convention.

```python
# mis_reports/urls.py
from django.urls import path
from .views import ExciseVatCstReportView, ExciseVatCstReportTablePartialView

urlpatterns = [
    path('excise-vat-cst/', ExciseVatCstReportView.as_view(), name='excise_vat_cst_report'),
    path('excise-vat-cst/tables/', ExciseVatCstReportTablePartialView.as_view(), name='excise_vat_cst_report_tables'),
]
```

#### 4.6 Tests (`mis_reports/tests.py`)

Task: Write tests for the models, forms, and views to ensure functionality and coverage.

**Instructions:**

- Include unit tests for model manager logic where applicable.
- Add unit tests for form validation.
- Add integration tests for views, covering GET, POST, and HTMX interactions.
- Mock external dependencies (like database queries) for isolated unit tests.

```python
# mis_reports/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date, timedelta
from unittest.mock import patch, MagicMock

# Import your models, forms, and views
from .models import (
    VatMaster, ExciseMaster, BillBookingMaster, BillBookingDetail,
    POMaster, PODetail, SalesInvoiceMaster, SalesInvoiceDetail,
    ReportComputation
)
from .forms import DateRangeForm

class ReportModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related tables
        cls.vat_master_vat = VatMaster.objects.create(id=1, terms='VAT 5%', value=5.0, is_vat=True, is_cst=False)
        cls.vat_master_cst = VatMaster.objects.create(id=2, terms='CST 2%', value=2.0, is_vat=False, is_cst=True)
        cls.vat_master_other = VatMaster.objects.create(id=3, terms='Other Tax', value=0.0, is_vat=False, is_cst=False)
        cls.excise_master_10 = ExciseMaster.objects.create(id=1, terms='Excise 10%', value=10.0)
        cls.excise_master_12 = ExciseMaster.objects.create(id=2, terms='Excise 12%', value=12.0)

        # Dates for testing
        cls.today = date.today()
        cls.from_date = cls.today - timedelta(days=60)
        cls.to_date = cls.today - timedelta(days=30)
        cls.comp_id = 101
        cls.supplier_id_exclude = 'S0098'
        cls.supplier_id_include = 'S001'

        # Purchase related data
        cls.po_master_include = POMaster.objects.create(id=1, supplier_id=cls.supplier_id_include)
        cls.po_master_exclude = POMaster.objects.create(id=2, supplier_id=cls.supplier_id_exclude)

        cls.po_detail_vat = PODetail.objects.create(id=1, master=cls.po_master_include, vat=cls.vat_master_vat, ex_st=cls.excise_master_10)
        cls.po_detail_cst = PODetail.objects.create(id=2, master=cls.po_master_include, vat=cls.vat_master_cst, ex_st=cls.excise_master_12)
        cls.po_detail_exclude = PODetail.objects.create(id=3, master=cls.po_master_exclude, vat=cls.vat_master_vat, ex_st=cls.excise_master_10)

        cls.bill_booking_master_1 = BillBookingMaster.objects.create(id=1, comp_id=cls.comp_id, sys_date=cls.from_date)
        cls.bill_booking_master_2 = BillBookingMaster.objects.create(id=2, comp_id=cls.comp_id, sys_date=cls.to_date)
        cls.bill_booking_master_outside_date = BillBookingMaster.objects.create(id=3, comp_id=cls.comp_id, sys_date=cls.today + timedelta(days=1))

        BillBookingDetail.objects.create(
            id=1, master=cls.bill_booking_master_1, po_detail=cls.po_detail_vat,
            ex_st_basic=100.00, ex_st_educess=10.00, ex_st_shecess=5.00,
            vat_amount=50.00, cst_amount=0.00
        )
        BillBookingDetail.objects.create(
            id=2, master=cls.bill_booking_master_2, po_detail=cls.po_detail_cst,
            ex_st_basic=200.00, ex_st_educess=20.00, ex_st_shecess=10.00,
            vat_amount=0.00, cst_amount=40.00
        )
        BillBookingDetail.objects.create( # This one should be excluded by supplier_id
            id=3, master=cls.bill_booking_master_1, po_detail=cls.po_detail_exclude,
            ex_st_basic=50.00, ex_st_educess=5.00, ex_st_shecess=2.00,
            vat_amount=25.00, cst_amount=0.00
        )
        BillBookingDetail.objects.create( # This one is outside date range
            id=4, master=cls.bill_booking_master_outside_date, po_detail=cls.po_detail_vat,
            ex_st_basic=10.00, ex_st_educess=1.00, ex_st_shecess=0.50,
            vat_amount=5.00, cst_amount=0.00
        )

        # Sales related data
        cls.sales_master_1 = SalesInvoiceMaster.objects.create(id=1, comp_id=cls.comp_id, date_of_issue_invoice=cls.from_date)
        cls.sales_master_2 = SalesInvoiceMaster.objects.create(id=2, comp_id=cls.comp_id, date_of_issue_invoice=cls.to_date)

        SalesInvoiceDetail.objects.create(
            id=1, master=cls.sales_master_1, req_qty=10.0, amt_in_per=100.0, rate=10.0, # Basic Amt: 1000
            pf_type=0, pf_amount=50.0, freight_type=1, freight_amount=5.0, # PF: 50, Freight: 50 (5% of 1000)
            cenvat=cls.excise_master_10, vat=cls.vat_master_vat, cst=None # CENVAT (10% of 1050) = 105; VAT (5% of (1050+105+25)) = 58.0
        )
        SalesInvoiceDetail.objects.create(
            id=2, master=cls.sales_master_2, req_qty=5.0, amt_in_per=100.0, rate=20.0, # Basic Amt: 1000
            pf_type=1, pf_amount=2.0, freight_type=0, freight_amount=30.0, # PF: 20 (2% of 1000), Freight: 30
            cenvat=cls.excise_master_12, vat=None, cst=cls.vat_master_cst # CENVAT (12% of 1020) = 122.4; CST (2% of (1020+122.4)) = 22.848
        )
        SalesInvoiceDetail.objects.create( # Outside date range
            id=3, master=cls.bill_booking_master_outside_date, req_qty=1.0, amt_in_per=100.0, rate=10.0,
            cenvat=cls.excise_master_10, vat=cls.vat_master_vat, cst=None
        )

    def test_purchase_computation(self):
        report_data = ReportComputation.objects.compute_excise_vat_cst(
            self.comp_id, self.from_date, self.to_date
        )

        # Assert purchase VAT
        self.assertEqual(len(report_data['purchase_vat']), 1)
        self.assertEqual(report_data['purchase_vat'][0]['VATerms'], 'VAT 5%')
        self.assertAlmostEqual(report_data['purchase_vat'][0]['VATAmt'], 50.00)

        # Assert purchase CST
        self.assertEqual(len(report_data['purchase_cst']), 1)
        self.assertEqual(report_data['purchase_cst'][0]['CSTTerms'], 'CST 2%')
        self.assertAlmostEqual(report_data['purchase_cst'][0]['CSTAmt'], 40.00)

        # Assert purchase Excise
        self.assertEqual(len(report_data['purchase_excise']), 2)
        excise_terms = {item['ExciseTerm']: item['ExBasicAmt'] for item in report_data['purchase_excise']}
        self.assertAlmostEqual(excise_terms['Excise 10%'], 115.00) # 100+10+5
        self.assertAlmostEqual(excise_terms['Excise 12%'], 230.00) # 200+20+10

    def test_sales_computation(self):
        report_data = ReportComputation.objects.compute_excise_vat_cst(
            self.comp_id, self.from_date, self.to_date
        )

        # Assert sales VAT
        self.assertEqual(len(report_data['sales_vat']), 1)
        self.assertEqual(report_data['sales_vat'][0]['VATTerm'], 'VAT 5%')
        # Detailed calculation for sales VAT:
        # 1. SalesInvoiceDetail 1: Basic=1000. PF=50/2 = 25. Freight=5% of 1000 = 50/2 = 25
        #    Amt after PF = 1000 + 25 = 1025.
        #    Excise (10% of 1025) = 102.5.
        #    VAT (5% of (1025 + 102.5 + 25)) = 5% of 1152.5 = 57.625
        self.assertAlmostEqual(report_data['sales_vat'][0]['Amt'], 57.63, places=2)

        # Assert sales CST
        self.assertEqual(len(report_data['sales_cst']), 1)
        self.assertEqual(report_data['sales_cst'][0]['CSTTerm'], 'CST 2%')
        # Detailed calculation for sales CST:
        # 2. SalesInvoiceDetail 2: Basic=1000. PF=2% of 1000 = 20/2 = 10. Freight=30/2 = 15
        #    Amt after PF = 1000 + 10 = 1010.
        #    Excise (12% of 1010) = 121.2.
        #    CST (2% of (1010 + 121.2)) + Freight = 2% of 1131.2 + 15 = 22.624 + 15 = 37.624
        self.assertAlmostEqual(report_data['sales_cst'][0]['CSTAmt'], 37.62, places=2)
        
        # Assert sales Excise
        self.assertEqual(len(report_data['sales_excise']), 2)
        sales_excise_terms = {item['EXTerm']: item['EXAmt'] for item in report_data['sales_excise']}
        self.assertAlmostEqual(sales_excise_terms['Excise 10%'], 102.50)
        self.assertAlmostEqual(sales_excise_terms['Excise 12%'], 121.20)

        self.assertAlmostEqual(report_data['vat_gross_total'], 57.63, places=2)

class DateRangeFormTest(TestCase):
    def test_form_valid(self):
        form_data = {'from_date': '2023-01-01', 'to_date': '2023-01-31'}
        form = DateRangeForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_invalid_date_range(self):
        form_data = {'from_date': '2023-01-31', 'to_date': '2023-01-01'}
        form = DateRangeForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Invalid selected date range', form.non_field_errors()[0])

    def test_initial_dates(self):
        # Mock date.today() to control the month for consistent testing
        with patch('mis_reports.forms.date') as mock_date:
            mock_date.today.return_value = date(2023, 3, 15) # Today is March 15, 2023
            mock_date.side_effect = lambda *args, **kw: date(*args, **kw) # Ensure real date object created
            mock_date.fromtimestamp.side_effect = lambda *args, **kw: date.fromtimestamp(*args, **kw) # Mock fromtimestamp too

            form = DateRangeForm()
            self.assertEqual(form.fields['from_date'].initial, date(2023, 2, 1)) # Should be Feb 1, 2023
            self.assertEqual(form.fields['to_date'].initial, date(2023, 2, 28)) # Should be Feb 28, 2023

class ExciseVatCstReportViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('excise_vat_cst_report')
        self.tables_url = reverse('excise_vat_cst_report_tables')

        # Create minimal required data for models just to pass ORM checks
        # (Detailed data for computation logic is handled by ReportModelsTest)
        VatMaster.objects.create(id=1, terms='VAT 5%', value=5.0, is_vat=True, is_cst=False)
        ExciseMaster.objects.create(id=1, terms='Excise 10%', value=10.0)

        # Mock the ReportComputation manager to control test data in views
        self.mock_report_data = {
            'purchase_vat': [{'VATerms': 'Test VAT', 'VATAmt': 100.0}],
            'purchase_cst': [], 'purchase_excise': [],
            'sales_vat': [], 'sales_cst': [], 'sales_excise': [],
            'vat_gross_total': 100.0
        }

    @patch('mis_reports.models.ReportComputation.objects.compute_excise_vat_cst')
    def test_main_report_view_get(self, mock_compute):
        mock_compute.return_value = self.mock_report_data
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/computation_report_list.html')
        self.assertContains(response, 'Excise/VAT/CST Computation Report')
        self.assertContains(response, 'id="dateRangeForm"')
        self.assertContains(response, 'id="reportTablesContainer"')
        
        # Check initial load message
        self.assertContains(response, 'Select dates and click "Search" to generate the report.')
        mock_compute.assert_not_called() # Should not be called on initial GET

    @patch('mis_reports.models.ReportComputation.objects.compute_excise_vat_cst')
    def test_report_tables_partial_view_get_with_dates(self, mock_compute):
        mock_compute.return_value = self.mock_report_data
        
        from_date = date(2023, 1, 1)
        to_date = date(2023, 1, 31)
        query_params = f"?from_date={from_date.isoformat()}&to_date={to_date.isoformat()}"
        
        response = self.client.get(self.tables_url + query_params, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/_computation_report_tables.html')
        self.assertContains(response, 'Purchase VAT Summary')
        self.assertContains(response, 'Test VAT')
        self.assertContains(response, '100.00')
        self.assertContains(response, 'Overall VAT Gross Total: <span class="text-green-700">$100.00</span>')
        
        mock_compute.assert_called_once_with(
            1, # Example comp_id
            from_date,
            to_date
        )

    @patch('mis_reports.models.ReportComputation.objects.compute_excise_vat_cst')
    def test_report_tables_partial_view_get_without_dates(self, mock_compute):
        mock_compute.return_value = self.mock_report_data
        
        # Mock date.today() for default date calculation
        with patch('mis_reports.forms.date') as mock_form_date:
            mock_form_date.today.return_value = date(2023, 3, 15) # Today is March 15, 2023
            mock_form_date.side_effect = lambda *args, **kw: date(*args, **kw)
            mock_form_date.fromtimestamp.side_effect = lambda *args, **kw: date.fromtimestamp(*args, **kw)

            response = self.client.get(self.tables_url, HTTP_HX_REQUEST='true')
            
            self.assertEqual(response.status_code, 200)
            self.assertTemplateUsed(response, 'mis_reports/_computation_report_tables.html')
            self.assertContains(response, 'Purchase VAT Summary')
            
            # Assert that compute was called with default dates (previous month)
            mock_compute.assert_called_once_with(
                1, # Example comp_id
                date(2023, 2, 1), # Feb 1, 2023
                date(2023, 2, 28) # Feb 28, 2023
            )

    @patch('mis_reports.models.ReportComputation.objects.compute_excise_vat_cst')
    def test_main_report_view_form_submission_success(self, mock_compute):
        mock_compute.return_value = self.mock_report_data
        form_data = {'from_date': '2023-01-01', 'to_date': '2023-01-31'}
        
        # Simulating HTMX form submission (hx-post) to the main view
        # This will trigger form_valid and return HX-Trigger header
        response = self.client.post(self.list_url, form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # No Content expected for HX-Trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshReportData')
        
        # The compute method is not called here, it's called by the partial view
        mock_compute.assert_not_called() 

    @patch('mis_reports.models.ReportComputation.objects.compute_excise_vat_cst')
    def test_main_report_view_form_submission_invalid(self, mock_compute):
        mock_compute.return_value = self.mock_report_data
        form_data = {'from_date': '2023-01-31', 'to_date': '2023-01-01'} # Invalid date range
        
        response = self.client.post(self.list_url, form_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Should render the form with errors
        self.assertTemplateUsed(response, 'mis_reports/computation_report_list.html')
        self.assertContains(response, 'Invalid selected date range')
        mock_compute.assert_not_called()

    @patch('mis_reports.models.ReportComputation.objects.compute_excise_vat_cst')
    def test_report_tables_partial_view_no_data_found(self, mock_compute):
        mock_compute.return_value = {
            'purchase_vat': [], 'purchase_cst': [], 'purchase_excise': [],
            'sales_vat': [], 'sales_cst': [], 'sales_excise': [],
            'vat_gross_total': 0.0
        }
        
        from_date = date(2023, 1, 1)
        to_date = date(2023, 1, 31)
        query_params = f"?from_date={from_date.isoformat()}&to_date={to_date.isoformat()}"
        
        response = self.client.get(self.tables_url + query_params, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis_reports/_computation_report_tables.html')
        self.assertContains(response, 'No Purchase VAT data found for this period.')
        self.assertContains(response, 'No records found for the selected date range.', html=True) # Check messages

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

- **HTMX:**
    - The main `computation_report_list.html` form (`#dateRangeForm`) uses `hx-get` to trigger the `excise_vat_cst_report_tables` URL.
    - `hx-target="#reportTablesContainer"` and `hx-swap="innerHTML"` ensure only the table content is updated.
    - `hx-trigger="submit, load from:body, refreshReportData from:body"` ensures the report loads on page load, on form submission, and whenever a `refreshReportData` custom event is dispatched (e.g., if another part of the UI wanted to trigger it).
    - `hx-indicator` shows a loading spinner during report generation.
- **Alpine.js:**
    - The `_computation_report_tables.html` partial uses `x-data="{ activeTab: 'purchase_vat' }"` to manage the active tab state.
    - `@click="activeTab = '...'"` changes the active tab.
    - `:class` bindings dynamically apply Tailwind CSS classes to highlight the active tab.
    - `x-show="activeTab === '...'" ` conditionally renders the content for the active tab.
- **DataTables:**
    - Each table within `_computation_report_tables.html` is given a unique ID (e.g., `id="purchaseVatTable"`).
    - A `<script>` block at the end of the partial template, wrapped in `document.addEventListener('htmx:afterSwap', function(evt) { ... })`, initializes DataTables on each of these tables *after* HTMX has swapped the content into the DOM. This is crucial for DataTables to function correctly with HTMX.
    - The `pageLength` option is set to 5 for brevity.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the Excise/VAT/CST Computation module from ASP.NET to Django. By leveraging modern Django patterns, the "Fat Model, Thin View" architecture, and dynamic frontend technologies like HTMX and Alpine.js with DataTables, your organization will benefit from:

*   **Improved Performance:** Partial page updates via HTMX reduce server load and enhance user experience.
*   **Simplified Frontend:** HTMX and Alpine.js eliminate the need for complex JavaScript frameworks, making the frontend leaner and easier to maintain.
*   **Enhanced Maintainability:** Clear separation of concerns (business logic in models, presentation in templates, minimal logic in views) leads to more organized and testable code.
*   **Scalability:** Django's robust framework and ORM provide a solid foundation for future growth.
*   **Developer Efficiency:** Automated generation techniques significantly reduce manual coding effort and accelerate the migration timeline.

This structured approach, guided by AI-assisted automation, ensures a smooth and efficient transition to a modern, high-performing Django application.