## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

The provided ASP.NET code for `Dashboard.aspx` and `Dashboard.aspx.cs` indicates a minimal, placeholder page. It primarily serves as a content container inheriting from a master page, with an empty `Page_Load` method. This means there are no explicit database interactions, UI controls, or business logic defined within these specific files.

For the purpose of demonstrating a comprehensive Django modernization plan, we will infer a common "Dashboard" functionality that might typically involve displaying a list of items or summary data. We will create a simple example model, `DashboardItem`, to showcase the full CRUD (Create, Read, Update, Delete) cycle using modern Django patterns, HTMX, Alpine.js, and DataTables, as requested. This approach allows us to provide a runnable, best-practice example that can be easily adapted once actual data structures and business logic are identified from other parts of the legacy ASP.NET application.

The business benefit of this modernization is the transition to a modular, high-performance, and maintainable web application. By using Django's ORM, HTMX, and Alpine.js, we eliminate the need for heavy client-side JavaScript frameworks, simplifying development and improving page load times. The clear separation of concerns (fat models, thin views) ensures that business logic is easily testable and reusable, accelerating future development and reducing errors.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the minimal nature of the provided ASP.NET code, no explicit database schema, table names, or column definitions can be extracted. The `Dashboard.aspx` and its code-behind are purely structural placeholders.

**Inference for Example:** To proceed with the modernization plan as requested, we will infer a simple database table named `dashboard_items` which could store various pieces of information or links that might appear on a dashboard. This allows us to demonstrate the full migration process.

*   **[TABLE_NAME]:** `dashboard_items`
*   **Columns (inferred):**
    *   `id` (Primary Key, auto-incremented by Django)
    *   `name` (Text, for the item's title)
    *   `description` (Text, for a brief explanation)
    *   `is_active` (Boolean, to control visibility)
    *   `created_at` (Timestamp, for auditing)
    *   `updated_at` (Timestamp, for auditing)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code contains no explicit backend functionality or CRUD operations. The `Page_Load` method is empty, indicating no data retrieval or manipulation on page load.

**Inference for Example:** To demonstrate the full capabilities of Django for a typical web application module, we will assume a common requirement for a dashboard to manage its constituent "items." Thus, the Django modernization plan will include:
*   **Read:** Displaying a list of `DashboardItem` records.
*   **Create:** Adding new `DashboardItem` records.
*   **Update:** Modifying existing `DashboardItem` records.
*   **Delete:** Removing `DashboardItem` records.
*   **Validation:** Basic field validation (e.g., `name` is required).

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The `Dashboard.aspx` primarily consists of content placeholders (`<asp:Content>`). It includes a reference to `loadingNotifier.js`, suggesting some client-side interaction related to page loading or asynchronous operations. Without specific ASP.NET controls like GridView, TextBox, or Button within this snippet, we must infer common dashboard UI patterns.

**Inference for Example:**
*   A **List View:** Displaying a tabular list of "Dashboard Items" will be implemented using DataTables for efficient client-side searching, sorting, and pagination.
*   **Add/Edit Forms:** Modals triggered by buttons will present forms for creating or updating items, allowing for a smooth user experience without full page reloads.
*   **Delete Confirmation:** A modal will also be used for confirming deletion, enhancing user safety.
*   **Dynamic Interactions:** All form submissions and modal loads will leverage HTMX for partial page updates. Alpine.js will manage UI state, such as modal visibility.
*   **Styling:** Tailwind CSS will be used for a modern, responsive design.

---

## Step 4: Generate Django Code

We will create a new Django application, let's call it `dashboard_app`, to encapsulate the modernized dashboard functionality.

### 4.1 Models

**File: `dashboard_app/models.py`**

```python
from django.db import models

class DashboardItem(models.Model):
    """
    Represents an item that can be displayed on a dashboard.
    This model is inferred due to the lack of specific data structures
    in the provided ASP.NET dashboard code.
    """
    name = models.CharField(
        max_length=255,
        db_column='name',
        help_text="The title or name of the dashboard item."
    )
    description = models.TextField(
        blank=True,
        null=True,
        db_column='description',
        help_text="A detailed description for the dashboard item."
    )
    is_active = models.BooleanField(
        default=True,
        db_column='is_active',
        help_text="Indicates if the dashboard item is currently active or visible."
    )
    created_at = models.DateTimeField(
        auto_now_add=True,
        db_column='created_at',
        help_text="The date and time when the item was created."
    )
    updated_at = models.DateTimeField(
        auto_now=True,
        db_column='updated_at',
        help_text="The date and time when the item was last updated."
    )

    class Meta:
        managed = False  # Set to True if Django should manage the table schema
        db_table = 'dashboard_items' # Map to the inferred database table
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['name'] # Default ordering for lists

    def __str__(self):
        return self.name

    def get_absolute_url(self):
        """Returns the URL to the detail page for this item (if applicable)."""
        # For this example, we'll return to the list view after operations.
        from django.urls import reverse
        return reverse('dashboarditem_list')

    # Example of a 'fat model' business logic method
    def toggle_active_status(self):
        """Toggles the active status of the dashboard item."""
        self.is_active = not self.is_active
        self.save()
        return self.is_active

```

### 4.2 Forms

**File: `dashboard_app/forms.py`**

```python
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem instances.
    """
    class Meta:
        model = DashboardItem
        fields = ['name', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter item name'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24',
                'placeholder': 'Provide a brief description',
                'rows': 3
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded',
                'id': 'id_is_active' # Ensure a unique ID for Alpine.js/HTMX
            }),
        }
        labels = {
            'name': 'Item Name',
            'description': 'Description',
            'is_active': 'Is Active?',
        }

    def clean_name(self):
        """Custom validation for the name field."""
        name = self.cleaned_data['name']
        if len(name) < 3:
            raise forms.ValidationError("Name must be at least 3 characters long.")
        return name

```

### 4.3 Views

**File: `dashboard_app/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm
from django.shortcuts import render

class DashboardItemListView(ListView):
    """
    Displays a list of DashboardItem objects. This is the main dashboard view.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/list.html'
    context_object_name = 'dashboard_items' # Plural for template context

class DashboardItemTablePartialView(ListView):
    """
    Returns only the table portion of DashboardItem list,
    designed to be loaded via HTMX for dynamic updates.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/_table.html' # Partial template
    context_object_name = 'dashboard_items'

class DashboardItemCreateView(CreateView):
    """
    Handles the creation of new DashboardItem objects.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_app/dashboarditem/_form.html' # Partial template for modal
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Dashboard Item "{self.object.name}" added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content status and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList' # Event to refresh the list
                }
            )
        return response

class DashboardItemUpdateView(UpdateView):
    """
    Handles the updating of existing DashboardItem objects.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_app/dashboarditem/_form.html' # Partial template for modal
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Dashboard Item "{self.object.name}" updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content status and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

class DashboardItemDeleteView(DeleteView):
    """
    Handles the deletion of DashboardItem objects.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('dashboarditem_list')

    def delete(self, request, *args, **kwargs):
        obj_name = self.get_object().name
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Dashboard Item "{obj_name}" deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content status and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

```

### 4.4 Templates

**Directory: `dashboard_app/templates/dashboard_app/dashboarditem/`**

**File: `list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Dashboard Item
        </button>
    </div>

    <div id="dashboarditemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>

    <!-- Reusable Modal Structure -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden transition-opacity duration-300 ease-in-out"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on keydown from document if event.key == 'Escape' remove .is-active from me"
         x-data="{ show: false }" x-show="show" x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto transform transition-transform duration-300 ease-in-out scale-95"
             _="on modal.htmx:afterOnLoad add .scale-100 remove .scale-95 from me"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
             <!-- HTMX loaded content goes here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is typically initiated globally and used for UI state.
        // The modal visibility (`x-show`) is controlled by the `hidden` class toggle
        // and the `is-active` class. No explicit Alpine component needed here for the modal.
    });
    // Add event listener for messages, if any
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) { // No content response, implies success after form submission
            // Check for Django messages and display them if needed
            // This would typically be handled by a global message handler on base.html
            // or by HTMX-triggering an update to a message display area.
        }
    });
</script>
{% endblock %}
```

**File: `_table.html` (Partial template for DataTables)**

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="dashboardItemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in dashboard_items %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.name }}</td>
                <td class="py-3 px-4 text-sm text-gray-500">{{ obj.description|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ obj.is_active|yesno:"Yes,No" }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.created_at|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-md hover:bg-gray-100"
                        hx-get="{% url 'dashboarditem_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-gray-100"
                        hx-get="{% url 'dashboarditem_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-sm text-gray-500">No dashboard items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization script, ensure jQuery and DataTables CDN are in base.html
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#dashboardItemTable')) {
            $('#dashboardItemTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "searching": true,
                "ordering": true,
                "info": true,
                "paging": true,
                "autoWidth": false,
                "responsive": true
            });
        }
    });
</script>
```

**File: `_form.html` (Partial template for Add/Edit modal)**

```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status == 204) { Alpine.store('modal').close(); }">
        {% csrf_token %}

        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-none p-0">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Item
            </button>
        </div>
    </form>
</div>
```

**File: `_confirm_delete.html` (Partial template for Delete confirmation modal)**

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-bold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the dashboard item "<span class="font-semibold">{{ object.name }}</span>"?</p>
    <p class="text-sm text-red-600 mb-8">This action cannot be undone.</p>

    <div class="flex justify-center space-x-4">
        <button
            type="button"
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button
            hx-delete="{% url 'dashboarditem_delete' object.pk %}"
            hx-swap="none"
            hx-on::after-request="if(event.detail.xhr.status == 204) { Alpine.store('modal').close(); }"
            class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Delete Item
        </button>
    </div>
</div>
```

### 4.5 URLs

**File: `dashboard_app/urls.py`**

```python
from django.urls import path
from .views import (
    DashboardItemListView,
    DashboardItemTablePartialView, # Added for HTMX
    DashboardItemCreateView,
    DashboardItemUpdateView,
    DashboardItemDeleteView
)

urlpatterns = [
    # Main list view (dashboard)
    path('items/', DashboardItemListView.as_view(), name='dashboarditem_list'),

    # HTMX-specific endpoint for the table content
    path('items/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'),

    # CRUD operations via modal
    path('items/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('items/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('items/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]

```
**Note on `project/urls.py`:** You'll need to include these URLs in your main Django project's `urls.py` file, for example:
```python
# project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('dashboard/', include('dashboard_app.urls')), # Include your new app's URLs
    # ... other project URLs
]
```

### 4.6 Tests

**File: `dashboard_app/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardItem
from django.forms.models import model_to_dict

class DashboardItemModelTest(TestCase):
    """
    Unit tests for the DashboardItem model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.item1 = DashboardItem.objects.create(
            name='Test Dashboard Item 1',
            description='Description for item 1.',
            is_active=True
        )
        cls.item2 = DashboardItem.objects.create(
            name='Another Item',
            description='Short description.',
            is_active=False
        )

    def test_dashboard_item_creation(self):
        """Verify DashboardItem instances are created correctly."""
        self.assertEqual(self.item1.name, 'Test Dashboard Item 1')
        self.assertEqual(self.item1.description, 'Description for item 1.')
        self.assertTrue(self.item1.is_active)
        self.assertIsNotNone(self.item1.created_at)
        self.assertIsNotNone(self.item1.updated_at)
        self.assertEqual(DashboardItem.objects.count(), 2)

    def test_name_label(self):
        """Verify the verbose name for the 'name' field."""
        field_label = self.item1._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'name') # Django default unless specified

    def test_description_label(self):
        """Verify the verbose name for the 'description' field."""
        field_label = self.item1._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'description')

    def test_str_method(self):
        """Verify the __str__ method returns the item's name."""
        self.assertEqual(str(self.item1), 'Test Dashboard Item 1')

    def test_toggle_active_status_method(self):
        """Test the custom model method to toggle active status."""
        initial_status = self.item1.is_active
        new_status = self.item1.toggle_active_status()
        self.assertEqual(new_status, not initial_status)
        self.item1.refresh_from_db() # Reload from DB to ensure persistence
        self.assertEqual(self.item1.is_active, not initial_status)

        initial_status_2 = self.item2.is_active
        new_status_2 = self.item2.toggle_active_status()
        self.assertEqual(new_status_2, not initial_status_2)
        self.item2.refresh_from_db()
        self.assertEqual(self.item2.is_active, not initial_status_2)


class DashboardItemViewsTest(TestCase):
    """
    Integration tests for DashboardItem views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test item for views that require an existing object
        cls.item = DashboardItem.objects.create(
            name='Initial Item',
            description='This is an item for view testing.',
            is_active=True
        )
        cls.list_url = reverse('dashboarditem_list')
        cls.table_partial_url = reverse('dashboarditem_table')
        cls.add_url = reverse('dashboarditem_add')
        cls.edit_url = reverse('dashboarditem_edit', args=[cls.item.pk])
        cls.delete_url = reverse('dashboarditem_delete', args=[cls.item.pk])

    def setUp(self):
        # Client for each test method
        self.client = Client()

    def test_list_view(self):
        """Test the DashboardItem list view loads correctly."""
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/list.html')
        self.assertContains(response, 'Dashboard Items Management')
        self.assertContains(response, 'Add New Dashboard Item')
        self.assertTrue('dashboard_items' in response.context)
        self.assertIn(self.item, response.context['dashboard_items'])

    def test_table_partial_view(self):
        """Test the HTMX-loaded table partial view."""
        response = self.client.get(self.table_partial_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_table.html')
        self.assertContains(response, '<table id="dashboardItemTable"')
        self.assertContains(response, self.item.name) # Check if item is in the table

    def test_create_view_get(self):
        """Test GET request for the add form (modal content)."""
        response = self.client.get(self.add_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_form.html')
        self.assertContains(response, 'Add Dashboard Item')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        """Test POST request for creating a new item with HTMX."""
        data = {
            'name': 'New Item Via Test',
            'description': 'A description for the new item.',
            'is_active': 'on'
        }
        initial_count = DashboardItem.objects.count()
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertEqual(DashboardItem.objects.count(), initial_count + 1)
        self.assertTrue(DashboardItem.objects.filter(name='New Item Via Test').exists())

    def test_create_view_post_invalid(self):
        """Test POST request for creating an item with invalid data (HTMX)."""
        data = {
            'name': 'ab', # Too short, will fail validation
            'description': 'Invalid description.',
            'is_active': 'on'
        }
        initial_count = DashboardItem.objects.count()
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_form.html')
        self.assertContains(response, 'Name must be at least 3 characters long.')
        self.assertEqual(DashboardItem.objects.count(), initial_count) # No item created

    def test_update_view_get(self):
        """Test GET request for the edit form (modal content)."""
        response = self.client.get(self.edit_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_form.html')
        self.assertContains(response, 'Edit Dashboard Item')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.item)

    def test_update_view_post_success(self):
        """Test POST request for updating an item with HTMX."""
        updated_name = 'Updated Initial Item Name'
        data = model_to_dict(self.item) # Start with existing data
        data['name'] = updated_name # Change the name
        data['is_active'] = 'off' # Change active status
        
        response = self.client.post(self.edit_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        
        self.item.refresh_from_db() # Reload object to get updated values
        self.assertEqual(self.item.name, updated_name)
        self.assertFalse(self.item.is_active)

    def test_update_view_post_invalid(self):
        """Test POST request for updating an item with invalid data (HTMX)."""
        data = model_to_dict(self.item)
        data['name'] = 'a' # Too short
        response = self.client.post(self.edit_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_form.html')
        self.assertContains(response, 'Name must be at least 3 characters long.')
        self.item.refresh_from_db()
        self.assertNotEqual(self.item.name, 'a') # Ensure name wasn't updated

    def test_delete_view_get(self):
        """Test GET request for delete confirmation (modal content)."""
        # Create a new item specifically for deletion test to avoid interfering with others
        item_to_delete = DashboardItem.objects.create(name='Item To Delete', description='Temporary')
        delete_url = reverse('dashboarditem_delete', args=[item_to_delete.pk])
        response = self.client.get(delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, item_to_delete.name)

    def test_delete_view_delete_method_success(self):
        """Test DELETE request for deleting an item with HTMX."""
        item_to_delete = DashboardItem.objects.create(name='Another Item To Delete', description='Temporary')
        delete_url = reverse('dashboarditem_delete', args=[item_to_delete.pk])
        
        initial_count = DashboardItem.objects.count()
        response = self.client.delete(delete_url, HTTP_HX_REQUEST='true') # Use client.delete for DELETE method
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        self.assertEqual(DashboardItem.objects.count(), initial_count - 1)
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())

```

---

## Step 5: HTMX and Alpine.js Integration

The generated code extensively uses HTMX and Alpine.js to achieve a dynamic, single-page application feel without complex JavaScript:

*   **HTMX for Dynamic Updates:**
    *   The `dashboarditem_list.html` uses `hx-get` on `dashboarditemTable-container` to load the table content dynamically from `{% url 'dashboarditem_table' %}`. It's triggered on `load` and by a custom event `refreshDashboardItemList`.
    *   Add/Edit/Delete buttons use `hx-get` to load forms/confirmation dialogs into the `modalContent` div.
    *   Form submissions (`hx-post`, `hx-delete`) use `hx-swap="none"` and then trigger the `refreshDashboardItemList` event using `HX-Trigger` header in the Django views, ensuring the table reloads after successful CRUD operations without a full page refresh.
    *   The `hx-on::after-request` attribute on forms is used to close the modal after a successful HTMX request (status 204).

*   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) in `list.html` uses `x-data="{ show: false }"` and `x-show="show"` for Alpine.js to manage its visibility and transitions.
    *   The `on click add .is-active to #modal` and `on click remove .is-active from me` (using `_` for Hyperscript) control the `hidden` class on the modal, working in conjunction with Alpine's `x-show` and CSS transitions to provide smooth opening/closing animations.
    *   A simple Alpine store `Alpine.store('modal').close()` is used to close the modal programmatically from HTMX's `hx-on::after-request`.

*   **DataTables for List Views:**
    *   The `_table.html` partial directly contains the `<table>` element with the ID `dashboardItemTable`.
    *   A `script` block within this partial initializes DataTables on `#dashboardItemTable`, ensuring that the DataTables plugin is applied correctly each time the table content is reloaded via HTMX. This handles client-side searching, sorting, and pagination.

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names (e.g., `DashboardItem`, `dashboard_items`).
*   **DRY Templates:** The use of `_table.html`, `_form.html`, and `_confirm_delete.html` as partials promotes DRY principles. These are loaded dynamically into a common modal structure.
*   **Fat Models, Thin Views:** Business logic, such as `toggle_active_status`, resides in the `DashboardItem` model. Views are kept concise (typically 5-15 lines for the core logic) by leveraging Django's CBVs and delegating data operations and validation to models and forms.
*   **Comprehensive Tests:** Detailed unit tests for the model and integration tests for all views (including HTMX interactions) ensure high test coverage and reliability of the migrated application.
*   **Automation Focus:** This plan is structured to be executed systematically. The generation of models, forms, views, templates, and URLs is pattern-driven, making it suitable for AI-assisted automation tools. The explicit definition of database mappings (`db_table`, `managed=False`) simplifies the connection to existing legacy databases. The use of standard Django components and HTMX patterns reduces the need for complex, hand-written JavaScript, which significantly streamlines the migration effort.