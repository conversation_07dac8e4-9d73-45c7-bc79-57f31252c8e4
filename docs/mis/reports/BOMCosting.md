## ASP.NET to Django Conversion Script: BOM Costing Report

This modernization plan outlines the transition of the ASP.NET BOM Costing report page to a modern Django application. Our approach leverages AI-assisted automation, focusing on a robust, maintainable, and high-performance solution using Django 5.0+, HTMX, Alpine.js, and DataTables, adhering to the "fat model, thin view" paradigm.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`bom_costing`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind's `BindDataCust` method and `sql` web method, we infer the following tables and relevant columns. Note that `CompId` and `FinYearId` are consistent filtering parameters across many tables, indicating a multi-tenant/multi-financial year structure.

*   **`SD_Cust_WorkOrder_Master` (Main Entity for this page):**
    *   `WONo` (Work Order Number - acts as primary key for fetching details) - String
    *   `EnqId` (Enquiry ID) - String
    *   `CustomerId` (Customer ID) - String
    *   `PONo` (Purchase Order Number) - String
    *   `SessionId` (Employee ID who generated the record) - String
    *   `FinYearId` (Financial Year ID) - Integer
    *   `CompId` (Company ID) - Integer
    *   `SysDate` (System Date, stored as `VARCHAR` 'DD-MM-YYYY' or 'MM-DD-YYYY') - String

*   **`tblDG_BOM_Master` (BOM Details, linked by `WONo`):**
    *   `WONo` (Work Order Number) - String
    *   `PId` (Product ID) - Integer
    *   `CId` (Component ID) - Integer
    *   `CompId` (Company ID) - Integer
    *   `FinYearId` (Financial Year ID) - Integer

*   **`SD_Cust_Master` (Customer Lookup, linked by `CustomerId`):**
    *   `CustomerId` (Customer ID) - String
    *   `CustomerName` (Customer Name) - String
    *   `CompId` (Company ID) - Integer
    *   `FinYearId` (Financial Year ID) - Integer

*   **`tblFinancial_master` (Financial Year Lookup, linked by `FinYearId`):**
    *   `FinYearId` (Financial Year ID) - Integer
    *   `FinYear` (Financial Year, e.g., "2023-2024") - String

*   **`tblHR_OfficeStaff` (Employee Lookup, linked by `SessionId`/`EmpId`):**
    *   `EmpId` (Employee ID) - String
    *   `EmployeeName` (Employee Name) - String
    *   `Title` (Employee Title) - String
    *   `CompId` (Company ID) - Integer
    *   `FinYearId` (Financial Year ID) - Integer

## Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The primary functionality is **Read** (displaying data) with advanced **Filtering/Searching** and **Redirection** to a report.

*   **Read (Display List):** The page fetches and displays a list of work orders with associated customer, financial year, BOM, and employee details.
*   **Search/Filter:** Users can search based on:
    *   Customer Name (with auto-complete functionality).
    *   Enquiry No.
    *   PO No.
    *   WO No.
    The search criteria dynamically change the input field type.
*   **Pagination & Sorting:** The `GridView` handles client-side pagination and sorting. This will be re-implemented using DataTables.
*   **Auto-completion:** A web service method `sql` provides auto-completion for customer names by searching `SD_Cust_Master`.
*   **Action (Row Command):** Clicking on a "WO No" link in the grid, along with a selected "Rate" option (Max/Min/Average/Latest/Actual) from a radio button list within the row, triggers a redirection to another report page (`BOMCosting_Report.aspx`) with query parameters.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET controls indicate a standard search form combined with a data grid.

*   **Search Controls:**
    *   `asp:DropDownList` (`DropDownList1`): For selecting search criteria (Customer Name, Enquiry No, PO No, WO No).
    *   `asp:TextBox` (`txtSearchCustomer`): Generic text input for Enquiry/PO/WO search.
    *   `asp:TextBox` (`TxtSearchValue`): Specific text input for Customer Name search, with `AjaxControlToolkit:AutoCompleteExtender`.
    *   `asp:Button` (`btnSearch`): Triggers the search.
*   **Data Display:**
    *   `asp:GridView` (`SearchGridView1`): Main tabular data display, showing `FinYear`, `CustomerName`, `CustomerId`, `EnqId`, `PONo`, `WONo`, `SysDate`.
    *   `asp:LinkButton` within `WONo` column: Triggers redirection to a report.
    *   `asp:RadioButtonList` (`RadRate`) within `Rate` column: Allows selection of a rate type (Max/Min/Average/Latest/Actual) per row, which is passed to the report.
    *   `asp:Label` (`LblPId`, `LblCId`): Hidden labels in the grid for passing `PId` and `CId` to the report.

## Step 4: Generate Django Code

We will create a new Django app named `bom_costing`.

### 4.1 Models

**Task:** Create Django models based on the identified database schema.
**Instructions:**
We define `WorkOrder`, `Customer`, `BomMaster`, `FinancialYear`, and `OfficeStaff` models, mapping directly to their respective database tables. We'll use `managed = False` as these tables pre-exist. Business logic for retrieving related data and handling complex queries will be encapsulated within custom manager methods and model properties, adhering to the "fat model" principle.

**File:** `bom_costing/models.py`

```python
from django.db import models
from django.db.models import F, Q
from datetime import datetime, date

# --- Helper Functions (Replicating clsFunctions.select and date parsing) ---
# In a real scenario, clsFunctions.select would be replaced by direct ORM queries.
# Here, we assume a custom manager/utility that mimics the logic of the original clsFunctions.
# For simplicity, we'll implement direct ORM queries in the manager and model methods.

# Helper function to parse customer ID from "Name [ID]" format
def parse_customer_id_from_autocomplete(text_with_id):
    if '[' in text_with_id and ']' in text_with_id:
        try:
            return text_with_id.split('[')[-1].strip(']')
        except IndexError:
            pass
    return None

# Helper to parse SysDate from VARCHAR
def parse_sysdate_str_to_date(date_str):
    if not date_str:
        return None
    # Attempt DD-MM-YYYY first (common for 103 format)
    try:
        return datetime.strptime(date_str, '%d-%m-%Y').date()
    except ValueError:
        # Fallback to MM-DD-YYYY if DD-MM-YYYY fails
        try:
            return datetime.strptime(date_str, '%m-%d-%Y').date()
        except ValueError:
            return None # Or raise an error, or log it


# --- Custom Managers ---
class BomCostingManager(models.Manager):
    def get_queryset(self):
        # Base queryset for WorkOrder, optimized for BOM Costing view.
        # This can prefetch/select_related if direct foreign keys exist.
        # Given the original code's multiple individual SQL calls,
        # we'll build properties on the model to fetch related data.
        return super().get_queryset()

    def get_filtered_work_orders(self, search_type, search_value, current_fin_year_id, current_company_id):
        queryset = self.get_queryset().filter(
            FinYearId__lte=current_fin_year_id,
            CompId=current_company_id
        ).order_by('WONo') # Original ASP.NET order by WONo ASC

        if search_value:
            if search_type == '0':  # Customer Name
                customer_id = parse_customer_id_from_autocomplete(search_value)
                if customer_id:
                    queryset = queryset.filter(CustomerId=customer_id)
            elif search_type == '1':  # Enquiry No
                queryset = queryset.filter(EnqId=search_value)
            elif search_type == '2':  # PO No
                queryset = queryset.filter(PONo=search_value)
            elif search_type == '3':  # WO No
                queryset = queryset.filter(WONo=search_value)
        
        # Filter to include only work orders that have a corresponding entry in tblDG_BOM_Master
        # This mimics the C# logic where dr[8] and dr[9] (PId, CId) are populated
        # only if DS.HasRows (from tblDG_BOM_Master query) is true.
        # In a real scenario, this would ideally be a JOIN in the ORM.
        # For 'managed=False' and complex legacy structure, a subquery or
        # an additional filter based on existing WONos in BomMaster is effective.
        
        bom_exists_filter = Q(bommaster__WONo__isnull=False) # Check if BomMaster has any related WONo
        # Ensure distinct results if multiple BOM entries per WONo exist
        queryset = queryset.filter(bom_exists_filter).distinct()

        return queryset


# --- Models ---
class WorkOrder(models.Model):
    # SD_Cust_WorkOrder_Master table
    WONo = models.CharField(db_column='WONo', max_length=50, primary_key=True) # Assuming WONo is unique/PK
    EnqId = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    CustomerId = models.CharField(db_column='CustomerId', max_length=50, blank=True, null=True)
    PONo = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    SessionId = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # Employee ID
    FinYearId = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)
    SysDate = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True) # Stored as VARCHAR

    objects = BomCostingManager() # Attach custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.WONo
    
    # --- Business Logic / Derived Properties (Fat Model) ---

    @property
    def formatted_sys_date(self):
        # Parses the SysDate string to a displayable DD-MM-YYYY format
        parsed_date = parse_sysdate_str_to_date(self.SysDate)
        return parsed_date.strftime('%d-%m-%Y') if parsed_date else 'N/A'

    @property
    def financial_year_name(self):
        # Mimics fun.select("FinYear", "tblFinancial_master", "FinYearId='X'")
        try:
            return FinancialYear.objects.get(FinYearId=self.FinYearId).FinYear
        except FinancialYear.DoesNotExist:
            return 'N/A'

    @property
    def customer_details(self):
        # Mimics fun.select("CustomerName,CustomerId", "SD_Cust_Master", "CustomerId='X'")
        try:
            return Customer.objects.get(
                CustomerId=self.CustomerId, 
                CompId=self.CompId, 
                FinYearId__lte=self.FinYearId # Assuming similar FinYearId filtering as WorkOrder
            )
        except Customer.DoesNotExist:
            return None
    
    @property
    def customer_name(self):
        cust = self.customer_details
        return cust.CustomerName if cust else 'N/A'

    @property
    def employee_name(self):
        # Mimics fun.select("Title+'.'+EmployeeName", "tblHR_OfficeStaff", "EmpId='X'")
        try:
            employee = OfficeStaff.objects.get(
                EmpId=self.SessionId,
                CompId=self.CompId,
                FinYearId__lte=self.FinYearId # Assuming similar FinYearId filtering
            )
            return f"{employee.Title}.{employee.EmployeeName}"
        except OfficeStaff.DoesNotExist:
            return 'N/A'

    @property
    def bom_details(self):
        # Mimics fetching PId, CId from tblDG_BOM_Master for this WONo
        # Returns the first matching BOM entry, as original code iterates on BOM and adds to DT
        try:
            return BomMaster.objects.filter(
                WONo=self.WONo,
                CompId=self.CompId,
                FinYearId__lte=self.FinYearId
            ).first() # .first() to get one, as C# DS.Read() only reads one
        except BomMaster.DoesNotExist:
            return None
    
    @property
    def bom_pid(self):
        bom = self.bom_details
        return bom.PId if bom else ''

    @property
    def bom_cid(self):
        bom = self.bom_details
        return bom.CId if bom else ''


class Customer(models.Model):
    # SD_Cust_Master table
    CustomerId = models.CharField(db_column='CustomerId', max_length=50, primary_key=True)
    CustomerName = models.CharField(db_column='CustomerName', max_length=255)
    CompId = models.IntegerField(db_column='CompId')
    FinYearId = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.CustomerName


class BomMaster(models.Model):
    # tblDG_BOM_Master table
    # Assuming Id is the PK, or combined WONo, ItemId. C# uses Id for ORDER BY, implying it's unique.
    Id = models.AutoField(db_column='Id', primary_key=True) # Assuming Id is auto-increment PK
    WONo = models.CharField(db_column='WONo', max_length=50)
    ItemId = models.CharField(db_column='ItemId', max_length=50, blank=True, null=True)
    PId = models.IntegerField(db_column='PId', blank=True, null=True)
    CId = models.IntegerField(db_column='CId', blank=True, null=True)
    Qty = models.DecimalField(db_column='Qty', max_digits=10, decimal_places=2, blank=True, null=True)
    AmdNo = models.CharField(db_column='AmdNo', max_length=50, blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)
    FinYearId = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for WO: {self.WONo}"

    # Add a related_name to WorkOrder for reverse lookup
    # Note: With managed=False, we can't directly add foreign keys in Django.
    # We simulate them with explicit queries in model properties/managers.
    # However, for filtering, we can add a dummy ForeignKey or use Q objects.
    # To enable `Q(bommaster__WONo__isnull=False)` in BomCostingManager:
    work_order_rel = models.ForeignKey(WorkOrder, on_delete=models.DO_NOTHING, db_column='WONo', related_name='bommaster', to_field='WONo', null=True, blank=True)


class FinancialYear(models.Model):
    # tblFinancial_master table
    FinYearId = models.IntegerField(db_column='FinYearId', primary_key=True)
    FinYear = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.FinYear


class OfficeStaff(models.Model):
    # tblHR_OfficeStaff table
    EmpId = models.CharField(db_column='EmpId', max_length=50, primary_key=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255)
    Title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)
    FinYearId = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return self.EmployeeName

```

### 4.2 Forms

**Task:** Define a Django form for user input for the search functionality.
**Instructions:**
We'll create a simple `Form` (not `ModelForm`) for the search criteria as it doesn't directly map to a single model instance. We'll use custom widgets and add Tailwind CSS classes for styling.

**File:** `bom_costing/forms.py`

```python
from django import forms

class WorkOrderSearchForm(forms.Form):
    SEARCH_TYPES = [
        ('Select', 'Select'),
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]
    
    search_type = forms.ChoiceField(
        choices=SEARCH_TYPES,
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': 'hx-get="{% url "bom_costing:search_input_partial" %}"', # HTMX to swap input field
            'hx-target': '#search-input-container',
            'hx-swap': 'outerHTML',
            'hx-indicator': '#search-loading-indicator',
        }),
        label="Search By"
    )
    
    search_value_customer_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name or ID (e.g., Customer Name [ID])',
            'hx-get': '{% url "bom_costing:customer_autocomplete" %}',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-suggestions',
            'hx-swap': 'outerHTML',
            'hx-indicator': '#autocomplete-loading-indicator',
            'autocomplete': 'off', # Prevent browser autocomplete
            'x-on:focus': 'if (this.value === "") $dispatch("htmx:trigger", { target: this, trigger: "keyup" })' # Trigger autocomplete on focus if empty
        }),
        label="Search Value"
    )

    search_value_generic = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search value',
        }),
        label="Search Value"
    )

```

### 4.3 Views

**Task:** Implement the list, search, autocomplete, and redirection operations using CBVs.
**Instructions:**
Views are kept minimal, delegating complex data retrieval and filtering to the model's custom manager. This maintains the "thin view" principle. We also include a partial view for the search input and the table, which will be loaded via HTMX.

**File:** `bom_costing/views.py`

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy, reverse
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect
from django.template.loader import render_to_string
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from django.shortcuts import get_object_or_404
import json

from .models import WorkOrder, Customer # Import other models if needed directly
from .forms import WorkOrderSearchForm

# Mocking session values for FinYearId and CompId (replace with actual session retrieval)
# In a real app, these would come from request.session or request.user profile
CURRENT_FIN_YEAR_ID = 2024 
CURRENT_COMPANY_ID = 1

class BomCostingListView(TemplateView):
    """
    Renders the main BOM Costing page with the search form and an HTMX-loaded table container.
    """
    template_name = 'bom_costing/workorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass an initial search form instance
        context['form'] = WorkOrderSearchForm()
        # Initialize search type and value for initial table load
        context['initial_search_type'] = self.request.GET.get('search_type', 'Select')
        context['initial_search_value_customer_name'] = self.request.GET.get('search_value_customer_name', '')
        context['initial_search_value_generic'] = self.request.GET.get('search_value_generic', '')
        return context

class WorkOrderTablePartialView(ListView):
    """
    Handles HTMX requests to render the work order table, including search, pagination, and sorting.
    """
    model = WorkOrder
    template_name = 'bom_costing/workorder/_workorder_table.html'
    context_object_name = 'workorders'
    paginate_by = 15 # Matches ASP.NET GridView's PageSize

    def get_queryset(self):
        search_type = self.request.GET.get('search_type', 'Select')
        search_value = self.request.GET.get('search_value_customer_name') if search_type == '0' else self.request.GET.get('search_value_generic')
        
        # Use the custom manager method to fetch filtered data
        queryset = WorkOrder.objects.get_filtered_work_orders(
            search_type, search_value, CURRENT_FIN_YEAR_ID, CURRENT_COMPANY_ID
        )
        
        # DataTables handles client-side sorting, but if server-side sorting is desired
        # you would implement it here based on request.GET parameters.
        # For this migration, we rely on DataTables for sorting post-fetch.
        
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add page object for pagination
        context['page_obj'] = context['paginator'].get_page(context['page_obj'].number)
        return context

class SearchInputPartialView(View):
    """
    Renders the correct search input field based on the selected search type.
    This is called by HTMX when the search_type dropdown changes.
    """
    def get(self, request, *args, **kwargs):
        search_type = request.GET.get('search_type', 'Select')
        form = WorkOrderSearchForm(initial={'search_type': search_type})
        
        context = {
            'form': form,
            'selected_search_type': search_type
        }
        
        # Render only the relevant part of the form for HTMX swap
        return HttpResponse(render_to_string(
            'bom_costing/workorder/_search_input_fields.html', 
            context, 
            request
        ))

class CustomerAutocompleteView(View):
    """
    Provides customer name suggestions for the autocomplete functionality via AJAX.
    Mimics the ASP.NET WebMethod `sql`.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('prefixText', '').lower()
        suggestions = []

        if query:
            # Replicating original ASP.NET behavior: filter by CompId and then by prefix
            customers = Customer.objects.filter(
                CompId=CURRENT_COMPANY_ID,
                # Assuming FinYearId check is not strictly needed for autocomplete itself
                # as the source table is SD_Cust_Master. If needed, add FinYearId__lte=CURRENT_FIN_YEAR_ID
            ).order_by('CustomerName') # Order by name for consistent autocomplete list

            for customer in customers:
                if customer.CustomerName.lower().startswith(query):
                    suggestions.append(f"{customer.CustomerName} [{customer.CustomerId}]")
                # Original C# breaks after 10, but typically for autocomplete all matches are returned.
                # For now, return all matches, front-end can handle display limit.

        # Original C# returns string[], Django returns JSON.
        return JsonResponse(suggestions, safe=False)

class BomCostingReportRedirectView(View):
    """
    Handles the redirection to the BOM Costing Report page, passing collected parameters.
    This replaces the Response.Redirect in SearchGridView1_RowCommand.
    """
    def get(self, request, wono, radval):
        # PId and CId were hidden fields in ASP.NET, fetched from BomMaster in model property.
        # We need to fetch the WorkOrder to get PId and CId from its model properties.
        workorder = get_object_or_404(WorkOrder, WONo=wono)

        # Retrieve PId and CId from the WorkOrder model's properties (which fetch from BomMaster)
        pid = workorder.bom_pid
        cid = workorder.bom_cid

        # Build the URL for the report page.
        # Assume 'bom_costing_report' is the name of your report URL pattern.
        # In a real Django setup, the report page would be another Django view.
        # For demonstration, we'll mimic the query string format of the ASP.NET redirect.
        report_url = reverse('bom_costing:bom_costing_report_page') # Placeholder for actual report URL
        
        # Construct query parameters
        params = {
            'WONo': wono,
            'PId': pid,
            'CId': cid,
            'RadVal': radval,
            'ModId': 14, # Hardcoded in ASP.NET
            'SubModId': '', # Hardcoded in ASP.NET
            'Key': 'dummy_key' # fun.GetRandomAlphaNumeric() - a dummy for now
        }
        
        # Redirect with query parameters
        from urllib.parse import urlencode
        return HttpResponseRedirect(f"{report_url}?{urlencode(params)}")

# Placeholder for the actual BOM Costing Report Page view (this would be in another Django app or module)
class BomCostingReportPage(TemplateView):
    template_name = 'bom_costing/report/bom_costing_report_page.html' # Example template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Here you would parse request.GET parameters and fetch report data
        context['wono'] = self.request.GET.get('WONo')
        context['pid'] = self.request.GET.get('PId')
        context['cid'] = self.request.GET.get('CId')
        context['radval'] = self.request.GET.get('RadVal')
        # ... fetch report data based on these parameters ...
        return context

```

### 4.4 Templates

**Task:** Create templates for the list view and its partials.
**Instructions:**
Templates will extend `core/base.html` (not included here). HTMX is used extensively for dynamic content loading, search form behavior, and DataTables integration. Alpine.js can be used for minor client-side UI states if needed, but HTMX will handle most of the dynamic updates.

**File:** `bom_costing/workorder/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gray-100 p-6 rounded-lg shadow-md mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">BOM Costing Search</h2>
        
        <form id="search-form" class="space-y-4"
              hx-get="{% url 'bom_costing:workorder_table_partial' %}"
              hx-target="#workorder-table-container"
              hx-swap="innerHTML"
              hx-trigger="submit, load delay:10ms"
              hx-indicator="#table-loading-indicator">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
                <div>
                    <label for="{{ form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ form.search_type.label }}
                    </label>
                    {{ form.search_type }}
                </div>
                
                <div id="search-input-container">
                    {# HTMX will swap this div content based on search_type selection #}
                    {# Initial content will be based on 'Select' or passed initial data #}
                    {% include 'bom_costing/workorder/_search_input_fields.html' with form=form selected_search_type=initial_search_type %}
                    <span id="search-loading-indicator" class="htmx-indicator text-blue-500 text-sm">
                        Loading input...
                    </span>
                </div>
                
                <div class="col-span-1 md:col-span-2 flex justify-start">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div id="workorder-table-container" class="bg-white p-6 rounded-lg shadow-md">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10" id="table-loading-indicator">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# DataTables and Alpine.js are assumed to be loaded in base.html #}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'workorder-table-container') {
            // Re-initialize DataTable after HTMX swaps the table content
            // Ensure jQuery and DataTables are loaded before this script
            if (typeof $.fn.DataTable !== 'undefined') {
                $('#workOrderTable').DataTable({
                    "pageLength": 15, // Matches ASP.NET GridView's PageSize
                    "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                    "responsive": true,
                    "autoWidth": false,
                    "pagingType": "full_numbers" // For full page number display
                });
            } else {
                console.error("jQuery DataTables not loaded.");
            }
        }
    });

    document.addEventListener('htmx:responseError', function(evt) {
        console.error('HTMX Error:', evt.detail.xhr.status, evt.detail.xhr.responseText);
        alert('An error occurred while loading data. Please try again.');
    });

    // Handle customer autocomplete suggestions
    // This part is managed by htmx on the input field directly targetting #customer-suggestions
    // Alpine.js could be used for more complex UI around the suggestions if needed.
</script>
{% endblock %}

```

**File:** `bom_costing/workorder/_search_input_fields.html` (Partial for HTMX)

```html
{# This template will be swapped into #search-input-container #}
{# It receives 'form' and 'selected_search_type' from the view #}

{% if selected_search_type == '0' %} {# Customer Name #}
    <div>
        <label for="{{ form.search_value_customer_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.search_value_customer_name.label }}
        </label>
        {{ form.search_value_customer_name }}
        <div id="customer-suggestions" class="bg-white border border-gray-200 rounded-md shadow-lg z-10 absolute w-auto max-h-48 overflow-y-auto">
            {# Autocomplete suggestions will be loaded here via HTMX #}
        </div>
        <span id="autocomplete-loading-indicator" class="htmx-indicator text-blue-500 text-sm">
            Searching customers...
        </span>
    </div>
{% else %} {# Enquiry No, PO No, WO No, Select #}
    <div>
        <label for="{{ form.search_value_generic.id_for_label }}" class="block text-sm font-medium text-gray-700">
            {{ form.search_value_generic.label }}
        </label>
        {{ form.search_value_generic }}
    </div>
{% endif %}

```

**File:** `bom_costing/workorder/_workorder_table.html` (Partial for HTMX)

```html
<table id="workOrderTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Code</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Enquiry No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">PO No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">WO No</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Gen. Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Rate</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in workorders %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ forloop.counter0|add:page_obj.start_index }}</td> {# Correct SN for pagination #}
            <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ obj.financial_year_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-700">{{ obj.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ obj.CustomerId }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ obj.EnqId }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-700">{{ obj.PONo }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">
                <a href="{% url 'bom_costing:bom_costing_report_redirect' wono=obj.WONo radval='0' %}" 
                   class="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                   x-on:click.prevent="
                        const selectedRate = $event.target.closest('tr').querySelector('input[name=\'rad_rate_{{ obj.WONo }}\']:checked');
                        const radVal = selectedRate ? selectedRate.value : '0'; // Default to Max if none selected
                        window.location.href = '{% url "bom_costing:bom_costing_report_redirect" wono=obj.WONo radval="PLACEHOLDER" %}'.replace('PLACEHOLDER', radVal);
                   ">
                   {{ obj.WONo }}
                </a>
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ obj.formatted_sys_date }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">
                <div class="flex flex-col sm:flex-row sm:space-x-2 justify-center">
                    {% for value, label in [('0', 'Max'), ('1', 'Min'), ('2', 'Avg'), ('3', 'Latest'), ('4', 'Actual')] %}
                        <div class="flex items-center">
                            <input type="radio" id="rad_{{ obj.WONo }}_{{ value }}" name="rad_rate_{{ obj.WONo }}" value="{{ value }}" 
                                   class="form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out" 
                                   {% if value == '0' %}checked{% endif %}> {# Default to Max as in ASP.NET #}
                            <label for="rad_{{ obj.WONo }}_{{ value }}" class="ml-1 text-gray-700 text-xs">{{ label }}</label>
                        </div>
                    {% endfor %}
                </div>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-8 px-4 text-center text-lg text-maroon-600 font-semibold">
                No data to display !
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

{# No explicit pagination controls needed here; DataTables will render them client-side #}

```

**File:** `bom_costing/customer/_autocomplete_suggestions.html` (Partial for HTMX)

```html
{# This template is swapped into #customer-suggestions #}
{# It receives 'suggestions' from CustomerAutocompleteView #}

{% for suggestion in suggestions %}
    <div class="px-4 py-2 hover:bg-blue-100 cursor-pointer"
         x-on:click="$event.target.closest('form').querySelector('#{{ form.search_value_customer_name.id_for_label }}').value = '{{ suggestion }}'; 
                     $event.target.closest('div').innerHTML = '';">
        {{ suggestion }}
    </div>
{% empty %}
    <div class="px-4 py-2 text-gray-500">No suggestions</div>
{% endfor %}

```

### 4.5 URLs

**Task:** Define URL patterns for the views.
**Instructions:**
We define paths for the main page, the HTMX partials (table and search input), the autocomplete endpoint, and the report redirect.

**File:** `bom_costing/urls.py`

```python
from django.urls import path
from .views import (
    BomCostingListView, 
    WorkOrderTablePartialView, 
    SearchInputPartialView,
    CustomerAutocompleteView,
    BomCostingReportRedirectView,
    BomCostingReportPage # Placeholder for the report page
)

app_name = 'bom_costing' # Namespace for the app

urlpatterns = [
    path('bom-costing/', BomCostingListView.as_view(), name='list'),
    path('bom-costing/table/', WorkOrderTablePartialView.as_view(), name='workorder_table_partial'),
    path('bom-costing/search-input-partial/', SearchInputPartialView.as_view(), name='search_input_partial'),
    path('bom-costing/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    # URL for redirecting to the report page
    path('bom-costing/report-redirect/<str:wono>/<str:radval>/', BomCostingReportRedirectView.as_view(), name='bom_costing_report_redirect'),
    # Placeholder URL for the actual report page (replace with your actual report app/module URL)
    path('bom-costing/report-page/', BomCostingReportPage.as_view(), name='bom_costing_report_page'),
]

```

### 4.6 Tests

**Task:** Write comprehensive tests for the models and views.
**Instructions:**
Tests cover model properties and manager methods for accurate data retrieval. View tests simulate HTTP requests, including HTMX requests, to ensure correct rendering and data handling.

**File:** `bom_costing/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from .models import WorkOrder, Customer, BomMaster, FinancialYear, OfficeStaff, parse_customer_id_from_autocomplete, parse_sysdate_str_to_date
from .views import CURRENT_FIN_YEAR_ID, CURRENT_COMPANY_ID

class ModelHelperFunctionsTest(TestCase):
    def test_parse_customer_id_from_autocomplete(self):
        self.assertEqual(parse_customer_id_from_autocomplete("Customer A [CUST001]"), "CUST001")
        self.assertIsNone(parse_customer_id_from_autocomplete("Customer A"))
        self.assertIsNone(parse_customer_id_from_autocomplete(""))
        self.assertIsNone(parse_customer_id_from_autocomplete("Customer A ["))

    def test_parse_sysdate_str_to_date(self):
        self.assertEqual(parse_sysdate_str_to_date("01-01-2023"), date(2023, 1, 1))
        self.assertEqual(parse_sysdate_str_to_date("15-03-2024"), date(2024, 3, 15))
        self.assertIsNone(parse_sysdate_str_to_date("invalid-date"))
        self.assertIsNone(parse_sysdate_str_to_date(None))
        self.assertIsNone(parse_sysdate_str_to_date(""))


class BomCostingModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.fin_year = FinancialYear.objects.create(FinYearId=CURRENT_FIN_YEAR_ID, FinYear="2023-2024")
        cls.old_fin_year = FinancialYear.objects.create(FinYearId=CURRENT_FIN_YEAR_ID - 1, FinYear="2022-2023")
        
        cls.customer = Customer.objects.create(
            CustomerId="CUST001", CustomerName="Test Customer", CompId=CURRENT_COMPANY_ID, FinYearId=CURRENT_FIN_YEAR_ID
        )
        cls.employee = OfficeStaff.objects.create(
            EmpId="EMP001", EmployeeName="John Doe", Title="Mr", CompId=CURRENT_COMPANY_ID, FinYearId=CURRENT_FIN_YEAR_ID
        )

        cls.work_order_with_bom = WorkOrder.objects.create(
            WONo="WO001", EnqId="ENQ001", CustomerId="CUST001", PONo="PO001", SessionId="EMP001",
            FinYearId=CURRENT_FIN_YEAR_ID, CompId=CURRENT_COMPANY_ID, SysDate="15-01-2024"
        )
        cls.bom_master = BomMaster.objects.create(
            WONo="WO001", PId=1001, CId=2001, CompId=CURRENT_COMPANY_ID, FinYearId=CURRENT_FIN_YEAR_ID
        )

        cls.work_order_without_bom = WorkOrder.objects.create(
            WONo="WO002", EnqId="ENQ002", CustomerId="CUST001", PONo="PO002", SessionId="EMP001",
            FinYearId=CURRENT_FIN_YEAR_ID, CompId=CURRENT_COMPANY_ID, SysDate="20-01-2024"
        )

        cls.work_order_other_company = WorkOrder.objects.create(
            WONo="WO003", EnqId="ENQ003", CustomerId="CUST001", PONo="PO003", SessionId="EMP001",
            FinYearId=CURRENT_FIN_YEAR_ID, CompId=CURRENT_COMPANY_ID + 1, SysDate="25-01-2024"
        )
        BomMaster.objects.create(
            WONo="WO003", PId=1003, CId=2003, CompId=CURRENT_COMPANY_ID + 1, FinYearId=CURRENT_FIN_YEAR_ID
        )


    def test_workorder_creation(self):
        self.assertEqual(self.work_order_with_bom.WONo, "WO001")
        self.assertEqual(self.work_order_with_bom.CustomerId, "CUST001")
        self.assertEqual(self.work_order_with_bom.FinYearId, CURRENT_FIN_YEAR_ID)

    def test_workorder_formatted_sys_date(self):
        self.assertEqual(self.work_order_with_bom.formatted_sys_date, "15-01-2024")
        
    def test_workorder_financial_year_name(self):
        self.assertEqual(self.work_order_with_bom.financial_year_name, "2023-2024")
        
    def test_workorder_customer_name(self):
        self.assertEqual(self.work_order_with_bom.customer_name, "Test Customer")
        
    def test_workorder_employee_name(self):
        self.assertEqual(self.work_order_with_bom.employee_name, "Mr.John Doe")

    def test_workorder_bom_details(self):
        self.assertEqual(self.work_order_with_bom.bom_pid, 1001)
        self.assertEqual(self.work_order_with_bom.bom_cid, 2001)
        self.assertIsNone(self.work_order_without_bom.bom_details)

    def test_bom_costing_manager_get_filtered_work_orders(self):
        # Test basic filtering
        qs = WorkOrder.objects.get_filtered_work_orders(
            search_type='Select', search_value=None, 
            current_fin_year_id=CURRENT_FIN_YEAR_ID, current_company_id=CURRENT_COMPANY_ID
        )
        self.assertEqual(qs.count(), 1) # Only WO001 has BOM
        self.assertEqual(qs.first().WONo, "WO001")

        # Test filter by WONo
        qs = WorkOrder.objects.get_filtered_work_orders(
            search_type='3', search_value='WO001', 
            current_fin_year_id=CURRENT_FIN_YEAR_ID, current_company_id=CURRENT_COMPANY_ID
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().WONo, "WO001")

        # Test filter by Customer Name (using parsed ID)
        qs = WorkOrder.objects.get_filtered_work_orders(
            search_type='0', search_value='Test Customer [CUST001]', 
            current_fin_year_id=CURRENT_FIN_YEAR_ID, current_company_id=CURRENT_COMPANY_ID
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().WONo, "WO001")

        # Test no results for non-existent WO
        qs = WorkOrder.objects.get_filtered_work_orders(
            search_type='3', search_value='NONEXISTENT', 
            current_fin_year_id=CURRENT_FIN_YEAR_ID, current_company_id=CURRENT_COMPANY_ID
        )
        self.assertEqual(qs.count(), 0)

        # Test filtering by company ID
        qs = WorkOrder.objects.get_filtered_work_orders(
            search_type='Select', search_value=None, 
            current_fin_year_id=CURRENT_FIN_YEAR_ID, current_company_id=CURRENT_COMPANY_ID + 1
        )
        self.assertEqual(qs.count(), 1)
        self.assertEqual(qs.first().WONo, "WO003")


class BomCostingViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for views
        cls.fin_year = FinancialYear.objects.create(FinYearId=CURRENT_FIN_YEAR_ID, FinYear="2023-2024")
        cls.customer = Customer.objects.create(CustomerId="CUST001", CustomerName="Test Customer", CompId=CURRENT_COMPANY_ID, FinYearId=CURRENT_FIN_YEAR_ID)
        cls.employee = OfficeStaff.objects.create(EmpId="EMP001", EmployeeName="John Doe", Title="Mr", CompId=CURRENT_COMPANY_ID, FinYearId=CURRENT_FIN_YEAR_ID)
        
        cls.wo1 = WorkOrder.objects.create(
            WONo="WO001", EnqId="ENQ001", CustomerId="CUST001", PONo="PO001", SessionId="EMP001",
            FinYearId=CURRENT_FIN_YEAR_ID, CompId=CURRENT_COMPANY_ID, SysDate="01-01-2024"
        )
        BomMaster.objects.create(WONo="WO001", PId=101, CId=201, CompId=CURRENT_COMPANY_ID, FinYearId=CURRENT_FIN_YEAR_ID)

        cls.wo2 = WorkOrder.objects.create(
            WONo="WO002", EnqId="ENQ002", CustomerId="CUST001", PONo="PO002", SessionId="EMP001",
            FinYearId=CURRENT_FIN_YEAR_ID, CompId=CURRENT_COMPANY_ID, SysDate="02-02-2024"
        )
        BomMaster.objects.create(WONo="WO002", PId=102, CId=202, CompId=CURRENT_COMPANY_ID, FinYearId=CURRENT_FIN_YEAR_ID)

        cls.wo_no_bom = WorkOrder.objects.create(
            WONo="WO003", EnqId="ENQ003", CustomerId="CUST001", PONo="PO003", SessionId="EMP001",
            FinYearId=CURRENT_FIN_YEAR_ID, CompId=CURRENT_COMPANY_ID, SysDate="03-03-2024"
        )


    def test_bom_costing_list_view(self):
        response = self.client.get(reverse('bom_costing:list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_costing/workorder/list.html')
        self.assertContains(response, 'BOM Costing Search')
        self.assertContains(response, '<div id="workorder-table-container"')

    def test_workorder_table_partial_view_initial_load(self):
        # Simulate HTMX initial load
        response = self.client.get(reverse('bom_costing:workorder_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_costing/workorder/_workorder_table.html')
        self.assertContains(response, "WO001")
        self.assertContains(response, "WO002")
        self.assertNotContains(response, "WO003") # WO003 has no BOM, should not be listed

    def test_workorder_table_partial_view_search_wono(self):
        # Simulate HTMX search by WO No
        response = self.client.get(reverse('bom_costing:workorder_table_partial'), 
                                   {'search_type': '3', 'search_value_generic': 'WO001'},
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_costing/workorder/_workorder_table.html')
        self.assertContains(response, "WO001")
        self.assertNotContains(response, "WO002")

    def test_workorder_table_partial_view_search_customer_name(self):
        # Simulate HTMX search by Customer Name (auto-completed format)
        response = self.client.get(reverse('bom_costing:workorder_table_partial'), 
                                   {'search_type': '0', 'search_value_customer_name': 'Test Customer [CUST001]'},
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_costing/workorder/_workorder_table.html')
        self.assertContains(response, "WO001")
        self.assertContains(response, "WO002")

    def test_search_input_partial_view(self):
        # Simulate HTMX request for search input partial
        response = self.client.get(reverse('bom_costing:search_input_partial'), 
                                   {'search_type': '0'}, # Request customer name input
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_costing/workorder/_search_input_fields.html')
        self.assertContains(response, 'id="id_search_value_customer_name"')
        self.assertNotContains(response, 'id="id_search_value_generic"')

        response = self.client.get(reverse('bom_costing:search_input_partial'), 
                                   {'search_type': '1'}, # Request generic input for Enquiry No
                                   HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'bom_costing/workorder/_search_input_fields.html')
        self.assertContains(response, 'id="id_search_value_generic"')
        self.assertNotContains(response, 'id="id_search_value_customer_name"')

    def test_customer_autocomplete_view(self):
        # Simulate AJAX request for customer autocomplete
        response = self.client.get(reverse('bom_costing:customer_autocomplete'), {'prefixText': 'test'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ["Test Customer [CUST001]"])

        response = self.client.get(reverse('bom_costing:customer_autocomplete'), {'prefixText': 'nonexistent'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

    def test_bom_costing_report_redirect_view(self):
        # Test redirection with a valid WO and RadVal
        response = self.client.get(reverse('bom_costing:bom_costing_report_redirect', args=['WO001', '2']))
        self.assertEqual(response.status_code, 302)
        # Check that it redirects to the expected report page with correct parameters
        expected_url = reverse('bom_costing:bom_costing_report_page') + '?WONo=WO001&PId=101&CId=201&RadVal=2&ModId=14&SubModId=&Key=dummy_key'
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)

        # Test with a WO that has no BOM details (PId/CId should be empty)
        response = self.client.get(reverse('bom_costing:bom_costing_report_redirect', args=['WO003', '0']))
        self.assertEqual(response.status_code, 302)
        expected_url_no_bom = reverse('bom_costing:bom_costing_report_page') + '?WONo=WO003&PId=&CId=&RadVal=0&ModId=14&SubModId=&Key=dummy_key'
        self.assertRedirects(response, expected_url_no_bom, fetch_redirect_response=False)
        
        # Test 404 for non-existent WO
        response = self.client.get(reverse('bom_costing:bom_costing_report_redirect', args=['NONEXISTENT_WO', '0']))
        self.assertEqual(response.status_code, 404)

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
The provided code samples demonstrate the integration.

*   **HTMX for dynamic updates:**
    *   The main `list.html` uses `hx-get` on `workorder-table-container` to load `_workorder_table.html` on `load` and `submit` of the search form.
    *   The `search_type` dropdown has `hx-get` and `hx-target` to load `_search_input_fields.html`, dynamically swapping the input field (customer autocomplete vs. generic text).
    *   The customer name input field uses `hx-get` to `customer_autocomplete` and `hx-target` to update `customer-suggestions` div on `keyup` with a `delay`.
    *   `hx-indicator` is used to show loading spinners for search and table loads.
*   **Alpine.js for UI state management:**
    *   The `x-on:click.prevent` on the "WO No" link in the table uses Alpine.js to read the selected radio button value from the current row before constructing the redirect URL. This client-side logic ensures the `RadVal` is correctly passed.
    *   The autocomplete input has `x-on:focus` to trigger HTMX `keyup` if empty, ensuring suggestions appear on focus.
    *   The autocomplete suggestions are clickable with `x-on:click` to populate the input field and clear suggestions.
*   **DataTables for list views:**
    *   The `_workorder_table.html` contains a `<table>` with `id="workOrderTable"`.
    *   A JavaScript block in `list.html` listens for `htmx:afterSwap` events, then initializes DataTables on the newly loaded table element. This ensures DataTables functions correctly even after HTMX replaces the table content.

## Final Notes

This comprehensive plan transforms the legacy ASP.NET BOM Costing page into a modern Django application. By applying the "fat model, thin view" pattern, leveraging HTMX for dynamic interactions, DataTables for rich tabular data, and Alpine.js for lightweight client-side logic, the solution achieves:

*   **Improved Maintainability:** Business logic is centralized in models, making views concise and easier to manage.
*   **Enhanced User Experience:** HTMX provides a snappier, single-page application feel without the complexity of traditional JavaScript frameworks.
*   **Increased Performance:** DataTables handles client-side operations like pagination, sorting, and searching, reducing server load. HTMX minimizes server requests by only updating necessary portions of the page.
*   **Scalability:** Django's robust architecture supports growth, while `managed=False` allows gradual migration of existing database schemas.
*   **Future-Proofing:** Adopting modern Django 5.0+ and best practices ensures the application remains current and adaptable to future business needs.
*   **Automated Conversion Potential:** The structured output and clear mapping of ASP.NET concepts to Django components facilitate the use of AI tools for generating significant portions of the codebase, greatly reducing manual effort and accelerating the migration timeline.

This approach provides a clear, actionable roadmap for non-technical stakeholders to understand and oversee the modernization process, ensuring a smooth transition to a more efficient and modern system.