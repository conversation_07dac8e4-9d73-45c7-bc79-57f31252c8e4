## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Plan Overview

This plan outlines the migration of the ASP.NET `Budget_Dist_Dept_Details.aspx` module to a modern Django application. The original ASP.NET page displayed a list of budget entries for a specific business group, allowing users to update and delete multiple entries simultaneously.

The modernized Django solution will leverage:
- **Django Models:** To interact with the existing SQL Server database tables (`tblACC_Budget_Dept` and `BusinessGroup`).
- **Django Class-Based Views (CBVs):** For handling data retrieval and processing bulk updates/deletes with minimal code.
- **HTMX:** To enable dynamic table updates and form submissions without full page reloads, providing a snappy user experience.
- **Alpine.js:** For lightweight client-side interactions, such as toggling between view and edit modes for the amount field based on checkbox selection.
- **DataTables.js:** To provide powerful client-side features like search, sort, and pagination for the budget entry list.
- **Tailwind CSS:** For a clean, modern, and responsive user interface.

**Business Value:**
This migration transforms a legacy ASP.NET page into a high-performance, maintainable, and scalable Django application. By adopting modern architectural patterns like "Fat Models, Thin Views" and integrating HTMX/Alpine.js, the system will offer:
1.  **Improved User Experience:** Faster interactions with no full-page refreshes, leading to a more fluid and responsive interface.
2.  **Enhanced Maintainability:** Clear separation of concerns (models for logic, views for coordination, templates for presentation) makes the codebase easier to understand, debug, and extend.
3.  **Reduced Development Time for Future Enhancements:** Leveraging Django's robust ecosystem, coupled with HTMX and Alpine.js, simplifies the addition of new features.
4.  **Cost Efficiency:** Automating the migration process minimizes manual coding effort, reducing development costs and potential for human error.
5.  **Modern Technology Stack:** Moving to Django positions the application on a widely supported, secure, and actively developed framework, future-proofing the investment.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts primarily with two tables: `tblACC_Budget_Dept` and `BusinessGroup`.

-   **`tblACC_Budget_Dept`**: This table stores the detailed budget entries.
    -   `Id` (Primary Key, Integer)
    -   `SysDate` (String/Varchar, stores date in 'DD-MM-YYYY' format)
    -   `SysTime` (String/Varchar, stores time in 'HH:MM:SS' format)
    -   `Amount` (Decimal/Float)
    -   `BGId` (Integer, Foreign Key to `BusinessGroup.Id`)
    -   `CompId` (Integer, stored from Session)
    -   `FinYearId` (Integer, stored from Session)
    -   `SessionId` (String/Varchar, stored from Session, likely username)

-   **`BusinessGroup`**: This table stores information about business groups (departments).
    -   `Id` (Primary Key, Integer)
    -   `Name` (String/Varchar)
    -   `Symbol` (String/Varchar, optional)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**
The ASP.NET page provides the following functionalities:

-   **Read (R):**
    -   Displays a list of budget entries from `tblACC_Budget_Dept`.
    -   Filters entries based on a `BGId` (Business Group ID) passed in the URL query string and `FinYearId` from the user's session.
    -   Retrieves and displays the `Name` of the `BusinessGroup` associated with the `BGId`.
    -   Presents data in a tabular format with pagination.
-   **Update (U):**
    -   Allows users to select multiple budget entries using checkboxes.
    -   For selected entries, the `Amount` field becomes editable.
    -   Upon clicking an "Update" button, all selected entries' `Amount` values are updated in the database.
    -   The `SysDate`, `SysTime`, `CompId`, `FinYearId`, and `SessionId` fields are also updated with current values.
    -   **Validation:** The `Amount` must be a positive number.
-   **Delete (D):**
    -   Allows users to select multiple budget entries using checkboxes.
    -   Upon clicking a "Delete" button, all selected entries are removed from the `tblACC_Budget_Dept` table.
-   **Navigation/Redirection:**
    -   If no budget entries are found for the given `BGId` and `FinYearId`, or upon clicking "Cancel", the page redirects to `Budget_Dist.aspx` (which is likely a main budget distribution list page).
    -   Client-side confirmation dialogs are used for Update/Delete actions.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The page primarily uses a `GridView` for displaying data and `TextBoxes` for input, along with `Buttons` for actions.

-   **`GridView` (`GridView2`)**: This is the core display component, showing budget entry details (SN, Checkbox, Id (hidden), Date, Time, Amount). This will be replaced by an HTML `<table>` enhanced with DataTables.js.
-   **`asp:Label` (`lbldept`, `lblDate`, `lblTime`, `lblAmount`, `lblId`, `lblMessage`)**: Used for displaying static text or data from the database. In Django, these will be rendered directly from context variables in templates.
-   **`asp:TextBox` (`TxtAmount`)**: Used for editing the `Amount` field. This will be an HTML `<input type="number">` element.
-   **`asp:CheckBox` (`CheckBox1`)**: Used for selecting rows for bulk operations. This will be an HTML `<input type="checkbox">` element, managed by Alpine.js.
-   **`asp:Button` (`BtnUpdate`, `BtnDelete`, `btnCancel`)**: Used to trigger server-side actions. These will become HTML `<button>` elements with HTMX attributes for dynamic POST requests.
-   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`**: Client-side and server-side validation for the `Amount` field. This logic will be migrated to Django Forms and Models.
-   **Client-side JavaScript (`confirmationUpdate()`, `confirmationDelete()`)**: Used for confirmation dialogs before actions. These will be replaced by HTMX's `hx-confirm` attribute or custom Alpine.js modals.

### Step 4: Generate Django Code

We will create a Django application named `budget_management`.

#### 4.1 Models (`budget_management/models.py`)

This file defines the data structures, mapping them to your existing database tables. It also includes the business logic for updating and deleting budget entries, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.utils import timezone
import datetime

class BusinessGroup(models.Model):
    """
    Represents the 'BusinessGroup' table, used for mapping BGId to department names.
    This model is 'unmanaged' as Django will not create or modify its schema.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False  # Django will not manage this table's schema (it exists)
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.name

class BudgetDepartmentEntry(models.Model):
    """
    Represents the 'tblACC_Budget_Dept' table, storing budget entries.
    This model is also 'unmanaged'.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date_str = models.CharField(db_column='SysDate', max_length=10, blank=True, null=True)
    sys_time_str = models.CharField(db_column='SysTime', max_length=8, blank=True, null=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)
    # Foreign key to BusinessGroup: DO_NOTHING means no cascading deletions in DB
    business_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGId')
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True) # Corresponds to username

    class Meta:
        managed = False
        db_table = 'tblACC_Budget_Dept'
        verbose_name = 'Budget Department Entry'
        verbose_name_plural = 'Budget Department Entries'

    def __str__(self):
        return f"Budget for {self.business_group.name} - {self.amount}"

    @property
    def sys_date(self):
        """
        Converts the stored SysDate_str (DD-MM-YYYY) to a Python date object.
        """
        try:
            return datetime.datetime.strptime(self.sys_date_str, '%d-%m-%Y').date()
        except (ValueError, TypeError):
            return None

    @property
    def sys_time(self):
        """
        Converts the stored SysTime_str (HH:MM:SS) to a Python time object.
        """
        try:
            return datetime.datetime.strptime(self.sys_time_str, '%H:%M:%S').time()
        except (ValueError, TypeError):
            return None

    def update_entry(self, new_amount, user_session_id, comp_id, fin_year_id):
        """
        Updates the budget entry's amount and system tracking fields.
        Business logic: Amount must be a positive number.
        """
        if not isinstance(new_amount, (int, float, models.Decimal)) or new_amount <= 0:
            raise ValueError("Amount must be a positive number.")

        self.amount = new_amount
        # Update SysDate and SysTime to current values, consistent with ASP.NET
        self.sys_date_str = timezone.now().strftime('%d-%m-%Y')
        self.sys_time_str = timezone.now().strftime('%H:%M:%S')
        self.company_id = comp_id
        self.financial_year_id = fin_year_id
        self.session_id = user_session_id
        self.save(update_fields=['amount', 'sys_date_str', 'sys_time_str', 'company_id', 'financial_year_id', 'session_id'])
        return True

    @classmethod
    def delete_entries(cls, entry_ids):
        """
        Deletes multiple budget entries by their IDs.
        """
        if not entry_ids:
            return 0
        deleted_count, _ = cls.objects.filter(id__in=entry_ids).delete()
        return deleted_count

```

#### 4.2 Forms (`budget_management/forms.py`)

This form is simplified because the main `Amount` editing happens inline with HTMX/Alpine.js. It's still useful for validation if a formal "edit form" modal were introduced later. For the current bulk update/delete, validation primarily happens in the model.

```python
from django import forms
from .models import BudgetDepartmentEntry

class BudgetDepartmentEntryForm(forms.ModelForm):
    """
    A simplified form, mainly to represent the validation logic for the 'amount' field.
    The primary interaction for update is via HTMX/Alpine.js directly on the table.
    """
    amount = forms.DecimalField(
        max_digits=18,
        decimal_places=2,
        min_value=0.01, # Enforce positive amount as per ASP.NET validation
        required=True,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter amount',
        })
    )

    class Meta:
        model = BudgetDepartmentEntry
        fields = ['amount'] # Only amount is directly editable through the form field here.

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        # Reinforce validation: amount must be positive.
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive number.")
        return amount

```

#### 4.3 Views (`budget_management/views.py`)

These views are kept lean, with business logic delegated to the models. They handle rendering the main page, serving the HTMX-driven table partial, and processing bulk update/delete requests. User authentication is assumed.

```python
from django.views.generic import ListView, View
from django.http import HttpResponse, HttpResponseRedirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.shortcuts import get_object_or_404, redirect, render
from django.db import transaction
from django.contrib.auth.mixins import LoginRequiredMixin # Requires user authentication

from .models import BudgetDepartmentEntry, BusinessGroup
from .forms import BudgetDepartmentEntryForm # Although not directly used for table interaction, it's defined.

# Helper to simulate session values (replace with actual session/user data in a real app)
# In a real application, you would typically fetch these from:
# request.user.profile.company_id, request.user.profile.financial_year_id, request.user.username
# This is a placeholder for demonstration purposes.
def get_user_context_data(request):
    """Retrieves mock company, financial year, and session ID."""
    # TODO: Replace with actual logic to fetch company_id, fin_year_id from user profile/session
    comp_id = 1 # Example: default company ID
    fin_year_id = 1 # Example: default financial year ID
    session_id = request.user.username if request.user.is_authenticated else "anonymous_user"
    return comp_id, fin_year_id, session_id

class BudgetDepartmentListView(LoginRequiredMixin, ListView):
    """
    Main view to display the budget distribution details page.
    It renders the initial page structure and prepares context data.
    """
    model = BudgetDepartmentEntry
    template_name = 'budget_management/budgetdepartmententry/list.html'
    context_object_name = 'budget_entries' # Not directly used for table population, but for context

    def get_queryset(self):
        """
        Filters budget entries based on BGId from query string and FinYearId.
        Handles redirection if no BGId is provided or no data found.
        """
        bg_id = self.request.GET.get('BGId')
        
        if not bg_id:
            messages.error(self.request, "Business Group ID is missing. Please provide a valid ID.")
            # Redirect to the previous budget distribution list page if BGId is missing
            return redirect(reverse_lazy('budget_management:budget_dist_list')) # Placeholder URL

        comp_id, fin_year_id, _ = get_user_context_data(self.request)

        queryset = BudgetDepartmentEntry.objects.filter(
            business_group__id=bg_id,
            financial_year_id=fin_year_id
        ).select_related('business_group')

        # Mimic ASP.NET's redirection if no data found
        if not queryset.exists() and not self.request.headers.get('HX-Request'):
            messages.info(self.request, "No budget entries found for this business group. Redirecting to main budget list.")
            return redirect(reverse_lazy('budget_management:budget_dist_list')) # Placeholder URL

        return queryset

    def get_context_data(self, **kwargs):
        """
        Adds the business group name to the context.
        """
        context = super().get_context_data(**kwargs)
        bg_id = self.request.GET.get('BGId')
        context['bg_id'] = bg_id # Pass BGId to template for HTMX partials
        
        if bg_id:
            try:
                business_group = BusinessGroup.objects.get(id=bg_id)
                context['business_group_name'] = business_group.name
            except BusinessGroup.DoesNotExist:
                context['business_group_name'] = "Unknown Business Group"
                messages.error(self.request, "Invalid Business Group ID provided.")
        else:
            context['business_group_name'] = "N/A"
        return context

class BudgetDepartmentTablePartialView(LoginRequiredMixin, ListView):
    """
    Renders only the table content. Designed to be fetched via HTMX for initial load and refreshes.
    This keeps the main page light and allows dynamic updates.
    """
    model = BudgetDepartmentEntry
    template_name = 'budget_management/budgetdepartmententry/_table.html'
    context_object_name = 'budget_entries'

    def get_queryset(self):
        """
        Provides the filtered queryset for the table.
        """
        bg_id = self.request.GET.get('BGId')
        comp_id, fin_year_id, _ = get_user_context_data(self.request)
        if not bg_id:
            # If BGId is missing in HTMX request, return empty queryset
            return BudgetDepartmentEntry.objects.none()
        
        return BudgetDepartmentEntry.objects.filter(
            business_group__id=bg_id,
            financial_year_id=fin_year_id
        ).select_related('business_group')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['bg_id'] = self.request.GET.get('BGId')
        return context


class BudgetDepartmentUpdateDeleteView(LoginRequiredMixin, View):
    """
    Handles bulk update and delete operations for budget entries.
    This view processes POST requests from the main form submission.
    """
    def post(self, request, *args, **kwargs):
        comp_id, fin_year_id, session_id = get_user_context_data(request)
        action = request.POST.get('action') # 'update' or 'delete'

        selected_ids = []
        amounts_to_update = {}
        
        # Iterate through POST data to find checked checkboxes and corresponding amounts
        for key, value in request.POST.items():
            if key.startswith('checkbox_') and value == 'on':
                try:
                    entry_id = int(key.replace('checkbox_', ''))
                    selected_ids.append(entry_id)
                    if action == 'update':
                        # Get the amount from the dynamically named input field
                        amount_key = f'amount_{entry_id}'
                        if amount_key in request.POST:
                            amounts_to_update[entry_id] = request.POST[amount_key]
                except ValueError:
                    messages.error(request, "Invalid entry ID detected in selection.")
                    return HttpResponse(status=400) # Bad Request

        if not selected_ids:
            messages.warning(request, "No entries selected for this operation.")
            # Use HX-Trigger for a client-side message or just to indicate completion
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetDepartmentList'})

        try:
            with transaction.atomic(): # Ensure atomicity for bulk operations
                if action == 'update':
                    for entry_id in selected_ids:
                        new_amount_str = amounts_to_update.get(entry_id)
                        if new_amount_str is None:
                            messages.error(request, f"Amount missing for selected entry ID {entry_id}.")
                            raise ValueError("Missing amount for update.")
                        
                        try:
                            new_amount = float(new_amount_str)
                            if new_amount <= 0:
                                raise ValueError("Amount must be a positive number.")
                        except ValueError:
                            messages.error(request, f"Invalid or non-positive amount '{new_amount_str}' for entry ID {entry_id}.")
                            raise ValueError("Invalid amount format or value.")

                        entry = get_object_or_404(BudgetDepartmentEntry, id=entry_id)
                        entry.update_entry(new_amount, session_id, comp_id, fin_year_id)
                    messages.success(request, f"{len(selected_ids)} budget entries updated successfully.")

                elif action == 'delete':
                    deleted_count = BudgetDepartmentEntry.delete_entries(selected_ids)
                    messages.success(request, f"{deleted_count} budget entries deleted successfully.")

                else:
                    messages.error(request, "Invalid action specified.")
                    return HttpResponse(status=400)

            # HTMX: Signal the client to refresh the table.
            # status=204 (No Content) is appropriate for HTMX triggers without returning content.
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetDepartmentList'})

        except ValueError as e:
            # Catch validation errors from model methods
            messages.error(request, f"Operation failed due to validation error: {e}")
            return HttpResponse(status=400)
        except Exception as e:
            # Catch any other unexpected errors
            messages.error(request, f"An unexpected error occurred during the operation: {e}")
            return HttpResponse(status=500)

class BudgetDepartmentCancelView(LoginRequiredMixin, View):
    """
    Handles the cancel action, redirecting to the main budget distribution list.
    """
    def post(self, request, *args, **kwargs):
        messages.info(request, "Operation cancelled. Redirecting to main budget list.")
        # Replace 'budget_management:budget_dist_list' with the actual URL name
        # for your Budget_Dist.aspx equivalent in Django.
        return HttpResponseRedirect(reverse_lazy('budget_management:budget_dist_list'))

```

#### 4.4 Templates

Templates will be structured within `budget_management/templates/budget_management/budgetdepartmententry/`. They adhere to DRY principles by extending `core/base.html` and using a partial for the table.

1.  **`budget_management/templates/budget_management/budgetdepartmententry/list.html`** (Main Page Template)

    This template sets up the overall page layout, includes the message display, and the form that wraps the HTMX-loaded table.

    ```html
    {% extends 'core/base.html' %}
    {% load static %}
    {% load humanize %} {# For thousands separators in amount #}

    {% block content %}
    <div class="container mx-auto px-4 py-8">
        <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg shadow-md mb-6">
            <h2 class="text-2xl font-bold">Budget Distribution Details</h2>
            <p class="text-sm">Manage budget entries for: <span class="font-bold text-yellow-300">{{ business_group_name }}</span></p>
        </div>

        {# Django Messages for user feedback #}
        {% if messages %}
            <div id="messages" class="mb-4">
                {% for message in messages %}
                    <div class="p-3 mb-2 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
            {# Script to auto-hide messages after a few seconds #}
            <script>
                setTimeout(() => {
                    const messages = document.getElementById('messages');
                    if (messages) {
                        messages.remove();
                    }
                }, 5000);
            </script>
        {% endif %}
        
        {# Form to handle bulk update/delete via HTMX POST #}
        <form id="budgetEntryForm" 
              hx-post="{% url 'budget_management:budget_update_delete' %}?BGId={{ bg_id }}" 
              hx-swap="none" {# No swap, rely on HX-Trigger to refresh table #}
              hx-target="body"> {# Target body for messages from backend #}
            {% csrf_token %} {# Django's CSRF token for security #}

            {# Container for the HTMX-loaded table #}
            <div id="budgetDepartmentTable-container"
                 hx-trigger="load, refreshBudgetDepartmentList from:body" {# Loads on page load, refreshes when 'refreshBudgetDepartmentList' event is triggered #}
                 hx-get="{% url 'budget_management:budget_table_partial' %}?BGId={{ bg_id }}" {# URL to fetch the table partial #}
                 hx-swap="innerHTML"> {# Replaces the content inside this div #}
                <!-- Initial loading state while HTMX fetches the table -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                    <p class="mt-4 text-gray-600">Loading budget entries...</p>
                </div>
            </div>

            {# Action buttons for Update, Delete, Cancel #}
            <div class="mt-6 flex justify-center space-x-4">
                <button 
                    type="submit" 
                    name="action" 
                    value="update"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-150 ease-in-out"
                    hx-confirm="Are you sure you want to update the selected entries?" {# HTMX client-side confirmation #}
                    hx-indicator="#loading-indicator"> {# Show loading indicator during request #}
                    Update Selected
                </button>
                <button 
                    type="submit" 
                    name="action" 
                    value="delete"
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-150 ease-in-out"
                    hx-confirm="Are you sure you want to delete the selected entries?"
                    hx-indicator="#loading-indicator">
                    Delete Selected
                </button>
                <button 
                    type="button" {# Type button to prevent default form submission #}
                    hx-post="{% url 'budget_management:budget_cancel' %}"
                    hx-swap="none"
                    hx-target="body"
                    class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-4 rounded shadow-md transition duration-150 ease-in-out"
                    hx-confirm="Are you sure you want to cancel and go back?">
                    Cancel
                </button>
            </div>
        </form>
        
        {# Global loading indicator for all HTMX requests #}
        <div id="loading-indicator" class="htmx-indicator fixed inset-0 bg-gray-700 bg-opacity-50 flex items-center justify-center z-50 hidden">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
            <p class="mt-4 text-white ml-4">Processing...</p>
        </div>

    </div>
    {% endblock %}

    {% block extra_js %}
    <script>
        document.addEventListener('alpine:init', () => {
            // Alpine.js component for managing checkbox state and amount field visibility per row
            Alpine.data('budgetEntryRow', (initialIsChecked, initialAmount) => ({
                isChecked: initialIsChecked,
                entryAmount: parseFloat(initialAmount).toFixed(2), // Ensure amount is a float and formatted
                toggleEditMode() {
                    // This function is called when the checkbox state changes
                    // The x-show directive on the input field handles visibility automatically
                }
            }));
        });
    </script>
    {% endblock %}
    ```

2.  **`budget_management/templates/budget_management/budgetdepartmententry/_table.html`** (Table Partial Template)

    This partial template contains only the HTML for the DataTables-enhanced table, making it ideal for HTMX to inject and refresh.

    ```html
    {% load humanize %} {# For thousands separators in amount #}
    <div class="overflow-x-auto bg-white rounded-lg shadow-md">
        <table id="budgetDepartmentEntryTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Id</th> {# Hidden column #}
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                    <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for entry in budget_entries %}
                <tr x-data="budgetEntryRow(false, '{{ entry.amount|floatformat:2 }}')" class="hover:bg-gray-50">
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap">
                        <input type="checkbox" 
                            x-model="isChecked" {# Alpine.js binds checkbox state #}
                            name="checkbox_{{ entry.id }}" {# Name for form submission #}
                            class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            @change="toggleEditMode()"> {# Trigger Alpine.js function on change #}
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 hidden">
                        <label>{{ entry.id }}</label>
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ entry.sys_date|default_if_none:"N/A"|date:"d-m-Y" }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ entry.sys_time|default_if_none:"N/A"|time:"H:i:s" }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                        {# Display mode for amount #}
                        <span x-show="!isChecked" class="block">{{ entry.amount|default:0.00|floatformat:2|intcomma }}</span> 
                        {# Edit mode for amount (only visible when checkbox is checked) #}
                        <input x-show="isChecked" 
                               x-model.lazy="entryAmount" {# Lazy binding to update model on blur/change #}
                               type="number" 
                               step="0.01" 
                               min="0.01"
                               name="amount_{{ entry.id }}" {# Dynamic name for form submission #}
                               class="w-24 text-right px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm">
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="py-4 px-4 text-center text-gray-500">No budget entries found for this business group.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    {# DataTables initialization script. Must be inside the HTMX-loaded content to run after load. #}
    <script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#budgetDepartmentEntryTable')) {
            $('#budgetDepartmentEntryTable').DataTable().destroy();
        }
        $('#budgetDepartmentEntryTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for page size
            "responsive": true, // Make table responsive
            "ordering": true, // Enable column ordering
            "info": true, // Display table information (e.g., "Showing 1 to 10 of 57 entries")
            "paging": true, // Enable pagination
            "searching": true, // Enable search box
            "columnDefs": [
                { "orderable": false, "targets": [1] }, // Disable sorting for Checkbox column
                { "visible": false, "targets": [2] }    // Hide Id column (column index 2)
            ]
        });
    });
    </script>
    ```

#### 4.5 URLs (`budget_management/urls.py`)

This file defines the URL patterns that map requests to your Django views.

```python
from django.urls import path
from .views import (
    BudgetDepartmentListView,
    BudgetDepartmentTablePartialView,
    BudgetDepartmentUpdateDeleteView,
    BudgetDepartmentCancelView
)

app_name = 'budget_management' # Namespace for URLs

urlpatterns = [
    # Main page for budget department details, e.g., /budget/details/?BGId=1
    path('budget/details/', BudgetDepartmentListView.as_view(), name='budget_dept_details'),
    
    # HTMX endpoint to fetch and refresh the table content
    path('budget/details/table/', BudgetDepartmentTablePartialView.as_view(), name='budget_table_partial'),
    
    # Endpoint for bulk update and delete operations
    path('budget/details/action/', BudgetDepartmentUpdateDeleteView.as_view(), name='budget_update_delete'),
    
    # Endpoint for the cancel action, redirects back to the main budget list
    path('budget/details/cancel/', BudgetDepartmentCancelView.as_view(), name='budget_cancel'),

    # Placeholder for Budget_Dist.aspx equivalent. You would define this in its own app's urls.py
    # and include it in your project's main urls.py
    # path('budget/dist/', BudgetDistListView.as_view(), name='budget_dist_list'),
    # For now, ensure you have a placeholder URL named 'budget_dist_list' in your project's root URLs or another app.
    # e.g., in your project's urls.py, add a dummy path: path('budget_dist/', lambda request: HttpResponse("Budget Distribution List"), name='budget_dist_list'),
]

```

**Important Note for `urls.py`:**
For `reverse_lazy('budget_management:budget_dist_list')` to work, you need to ensure that the URL name `budget_dist_list` is defined in your project's main `urls.py` or another included app. A simple placeholder for demonstration would be:
In `erp_project/urls.py`:
```python
from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse # For dummy view

urlpatterns = [
    path('admin/', admin.site.urls),
    path('budget_management/', include('budget_management.urls')),
    # Placeholder for the Budget_Dist.aspx equivalent
    path('budget_dist/', lambda request: HttpResponse("<html><body><h1 class='text-2xl font-bold'>Budget Distribution List - Placeholder</h1><p>This page represents the Budget_Dist.aspx equivalent.</p><p><a href='/budget_management/budget/details/?BGId=1'>Go to Budget Details (BGId=1)</a></p></body></html>"), name='budget_dist_list'),
]
```

#### 4.6 Tests (`budget_management/tests.py`)

Comprehensive tests for models and views are crucial for ensuring data integrity and correct functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection, transaction
from unittest.mock import patch, MagicMock
from datetime import date, time
from decimal import Decimal

from .models import BudgetDepartmentEntry, BusinessGroup

class BudgetDepartmentEntryModelTest(TestCase):
    """
    Unit tests for the BudgetDepartmentEntry model and its methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a dummy BusinessGroup first, as BudgetDepartmentEntry has a FK to it
        cls.business_group = BusinessGroup.objects.create(
            id=101, name='Marketing Department', symbol='MKD'
        )
        # Create test data for BudgetDepartmentEntry
        cls.entry1 = BudgetDepartmentEntry.objects.create(
            id=1,
            sys_date_str='25-12-2023',
            sys_time_str='10:00:00',
            amount=Decimal('1500.00'),
            business_group=cls.business_group,
            company_id=1,
            financial_year_id=1,
            session_id='testuser'
        )
        cls.entry2 = BudgetDepartmentEntry.objects.create(
            id=2,
            sys_date_str='26-12-2023',
            sys_time_str='11:30:00',
            amount=Decimal('250.75'),
            business_group=cls.business_group,
            company_id=1,
            financial_year_id=1,
            session_id='testuser'
        )

    def test_budget_entry_creation(self):
        """Verify BudgetDepartmentEntry object can be created and properties are correct."""
        entry = BudgetDepartmentEntry.objects.get(id=1)
        self.assertEqual(entry.amount, Decimal('1500.00'))
        self.assertEqual(entry.business_group.name, 'Marketing Department')
        self.assertEqual(entry.company_id, 1)
        self.assertEqual(entry.financial_year_id, 1)
        self.assertEqual(entry.session_id, 'testuser')

    def test_sys_date_property(self):
        """Test the sys_date property converts string to date object correctly."""
        entry = BudgetDepartmentEntry.objects.get(id=1)
        self.assertEqual(entry.sys_date, date(2023, 12, 25))

    def test_sys_time_property(self):
        """Test the sys_time property converts string to time object correctly."""
        entry = BudgetDepartmentEntry.objects.get(id=1)
        self.assertEqual(entry.sys_time, time(10, 0, 0))

    @patch('django.utils.timezone.now')
    def test_update_entry_method(self, mock_now):
        """Test the update_entry method with valid data."""
        mock_now.return_value = datetime.datetime(2024, 1, 1, 12, 0, 0)
        entry = BudgetDepartmentEntry.objects.get(id=1)
        original_amount = entry.amount
        
        entry.update_entry(Decimal('1750.50'), 'updateduser', 2, 2)
        entry.refresh_from_db() # Reload from DB to get updated values

        self.assertEqual(entry.amount, Decimal('1750.50'))
        self.assertEqual(entry.session_id, 'updateduser')
        self.assertEqual(entry.company_id, 2)
        self.assertEqual(entry.financial_year_id, 2)
        self.assertEqual(entry.sys_date_str, '01-01-2024')
        self.assertEqual(entry.sys_time_str, '12:00:00')

    def test_update_entry_method_invalid_amount(self):
        """Test update_entry with invalid amount (non-positive)."""
        entry = BudgetDepartmentEntry.objects.get(id=1)
        with self.assertRaises(ValueError):
            entry.update_entry(Decimal('0.00'), 'testuser', 1, 1)
        with self.assertRaises(ValueError):
            entry.update_entry(Decimal('-10.00'), 'testuser', 1, 1)
        with self.assertRaises(ValueError):
            entry.update_entry('abc', 'testuser', 1, 1) # Non-numeric

    def test_delete_entries_method(self):
        """Test static method to delete multiple entries."""
        # Create a third entry for deletion test
        entry3 = BudgetDepartmentEntry.objects.create(
            id=3, sys_date_str='27-12-2023', sys_time_str='12:00:00',
            amount=Decimal('500.00'), business_group=self.business_group,
            company_id=1, financial_year_id=1, session_id='testuser'
        )
        
        initial_count = BudgetDepartmentEntry.objects.count()
        deleted_count = BudgetDepartmentEntry.delete_entries([1, 3])
        
        self.assertEqual(deleted_count, 2)
        self.assertEqual(BudgetDepartmentEntry.objects.count(), initial_count - 2)
        self.assertFalse(BudgetDepartmentEntry.objects.filter(id=1).exists())
        self.assertFalse(BudgetDepartmentEntry.objects.filter(id=3).exists())
        self.assertTrue(BudgetDepartmentEntry.objects.filter(id=2).exists()) # Ensure others are not deleted

    def test_delete_entries_method_no_ids(self):
        """Test delete_entries with an empty list of IDs."""
        initial_count = BudgetDepartmentEntry.objects.count()
        deleted_count = BudgetDepartmentEntry.delete_entries([])
        self.assertEqual(deleted_count, 0)
        self.assertEqual(BudgetDepartmentEntry.objects.count(), initial_count)


class BudgetDepartmentViewsTest(TestCase):
    """
    Integration tests for the views using a mock client.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup data for all tests in this class
        cls.business_group_a = BusinessGroup.objects.create(id=1, name='Dept A')
        cls.business_group_b = BusinessGroup.objects.create(id=2, name='Dept B')

        BudgetDepartmentEntry.objects.create(
            id=10, sys_date_str='01-01-2024', sys_time_str='09:00:00',
            amount=Decimal('100.00'), business_group=cls.business_group_a,
            company_id=1, financial_year_id=2024, session_id='user1'
        )
        BudgetDepartmentEntry.objects.create(
            id=11, sys_date_str='02-01-2024', sys_time_str='10:00:00',
            amount=Decimal('200.00'), business_group=cls.business_group_a,
            company_id=1, financial_year_id=2024, session_id='user1'
        )
        BudgetDepartmentEntry.objects.create(
            id=12, sys_date_str='03-01-2024', sys_time_str='11:00:00',
            amount=Decimal('300.00'), business_group=cls.business_group_b,
            company_id=1, financial_year_id=2024, session_id='user1'
        )
        # Assuming a user is logged in for LoginRequiredMixin
        from django.contrib.auth.models import User
        cls.user = User.objects.create_user(username='testuser', password='password123')


    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        # Mock get_user_context_data to control session values
        self.patcher = patch('budget_management.views.get_user_context_data')
        self.mock_get_user_context_data = self.patcher.start()
        self.mock_get_user_context_data.return_value = (1, 2024, 'testuser') # comp_id, fin_year_id, session_id

    def tearDown(self):
        self.patcher.stop()


    def test_list_view_success(self):
        """Test BudgetDepartmentListView with valid BGId and data."""
        response = self.client.get(reverse('budget_management:budget_dept_details') + '?BGId=1')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_management/budgetdepartmententry/list.html')
        self.assertContains(response, 'Budget Distribution Details')
        self.assertContains(response, 'Dept A') # Business group name

    def test_list_view_no_bgid_redirects(self):
        """Test BudgetDepartmentListView redirects if BGId is missing."""
        response = self.client.get(reverse('budget_management:budget_dept_details'), follow=True)
        # Check if it redirects to the placeholder budget_dist_list
        self.assertRedirects(response, reverse('budget_management:budget_dist_list'))
        self.assertContains(response, 'Business Group ID is missing', html=True)


    def test_list_view_no_data_for_bgid_redirects(self):
        """Test BudgetDepartmentListView redirects if no data for given BGId."""
        # Assuming BGId=99 does not have any entries
        response = self.client.get(reverse('budget_management:budget_dept_details') + '?BGId=99', follow=True)
        self.assertRedirects(response, reverse('budget_management:budget_dist_list'))
        self.assertContains(response, 'No budget entries found for this business group', html=True)

    def test_table_partial_view(self):
        """Test BudgetDepartmentTablePartialView (HTMX component)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budget_management:budget_table_partial') + '?BGId=1', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_management/budgetdepartmententry/_table.html')
        self.assertContains(response, 'id="budgetDepartmentEntryTable"')
        self.assertContains(response, 'value="100.00"') # entry 10
        self.assertContains(response, 'value="200.00"') # entry 11
        self.assertNotContains(response, 'value="300.00"') # entry 12 is for Dept B

    def test_table_partial_view_no_bgid(self):
        """Test BudgetDepartmentTablePartialView with no BGId."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budget_management:budget_table_partial'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No budget entries found for this business group.')


    @patch('budget_management.models.BudgetDepartmentEntry.update_entry', MagicMock(return_value=True))
    def test_update_delete_view_update_success(self):
        """Test BudgetDepartmentUpdateDeleteView for successful update."""
        initial_amount_10 = BudgetDepartmentEntry.objects.get(id=10).amount
        initial_amount_11 = BudgetDepartmentEntry.objects.get(id=11).amount

        data = {
            'action': 'update',
            'checkbox_10': 'on',
            'amount_10': '125.50',
            'checkbox_11': 'on',
            'amount_11': '250.75',
            'BGId': '1' # Required for views.py get_queryset/context_data
        }
        response = self.client.post(reverse('budget_management:budget_update_delete'), data)
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetDepartmentList')
        
        # Verify the mock update_entry was called correctly
        # The mock prevents actual DB update, so we verify calls.
        BudgetDepartmentEntry.update_entry.assert_any_call(Decimal('125.50'), 'testuser', 1, 2024)
        BudgetDepartmentEntry.update_entry.assert_any_call(Decimal('250.75'), 'testuser', 1, 2024)
        self.assertEqual(BudgetDepartmentEntry.update_entry.call_count, 2)


    def test_update_delete_view_update_invalid_amount(self):
        """Test BudgetDepartmentUpdateDeleteView for invalid update amount."""
        data = {
            'action': 'update',
            'checkbox_10': 'on',
            'amount_10': '0.00', # Invalid amount
        }
        response = self.client.post(reverse('budget_management:budget_update_delete'), data)
        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertIn('Operation failed due to validation error: Amount must be a positive number.', response.content.decode())


    def test_update_delete_view_delete_success(self):
        """Test BudgetDepartmentUpdateDeleteView for successful delete."""
        initial_count = BudgetDepartmentEntry.objects.count()
        data = {
            'action': 'delete',
            'checkbox_10': 'on',
            'checkbox_11': 'on',
            'BGId': '1'
        }
        response = self.client.post(reverse('budget_management:budget_update_delete'), data)
        
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetDepartmentList')
        
        self.assertEqual(BudgetDepartmentEntry.objects.count(), initial_count - 2)
        self.assertFalse(BudgetDepartmentEntry.objects.filter(id=10).exists())
        self.assertFalse(BudgetDepartmentEntry.objects.filter(id=11).exists())


    def test_update_delete_view_no_selection(self):
        """Test update/delete with no entries selected."""
        data = {'action': 'update', 'BGId': '1'}
        response = self.client.post(reverse('budget_management:budget_update_delete'), data)
        self.assertEqual(response.status_code, 204) # HTMX No Content, but informs client
        self.assertIn('HX-Trigger', response.headers) # Still triggers for message display

    def test_cancel_view(self):
        """Test BudgetDepartmentCancelView redirects."""
        response = self.client.post(reverse('budget_management:budget_cancel'))
        self.assertRedirects(response, reverse('budget_management:budget_dist_list'))
        # You can add a check for messages if you can access them after redirect (e.g., using follow=True)
        # self.assertContains(response, 'Operation cancelled.')

```

### Step 5: HTMX and Alpine.js Integration

The integration strategy is built into the templates and views:

-   **HTMX:**
    -   `list.html` uses `hx-get` on `budgetDepartmentTable-container` to fetch the table content (`_table.html`) on `load` and on a custom `refreshBudgetDepartmentList` event.
    -   The "Update Selected" and "Delete Selected" buttons trigger `hx-post` requests to `budget_update_delete` endpoint.
    -   These POST requests return a `204 No Content` status with an `HX-Trigger: refreshBudgetDepartmentList` header, prompting the `budgetDepartmentTable-container` to refetch its content, thus refreshing the table.
    -   `hx-confirm` is used for client-side confirmation dialogs before POST requests.
    -   `hx-indicator` is used to show a loading spinner during HTMX requests.
    -   The "Cancel" button similarly uses `hx-post` to trigger a redirect.
-   **Alpine.js:**
    -   The `_table.html` uses `x-data="budgetEntryRow(...)"` on each table row.
    -   `x-model="isChecked"` is bound to the checkbox, managing its state.
    -   `x-show="!isChecked"` and `x-show="isChecked"` on the label and input field respectively control their visibility, effectively toggling between view and edit modes for the `Amount` field when the checkbox is clicked.
    -   `x-model.lazy="entryAmount"` binds the input field value to the Alpine.js component's data, ensuring immediate updates within the UI.
-   **DataTables.js:**
    -   The `_table.html` contains a `<script>` block that initializes `$('#budgetDepartmentEntryTable').DataTable()`. This script runs every time the partial is loaded via HTMX, ensuring the DataTables functionality is re-applied to the new content. This also handles destroying any previous instance to prevent errors.

This approach ensures a highly interactive and efficient user interface without writing complex custom JavaScript.