This comprehensive Django modernization plan outlines the strategy to transition your existing ASP.NET application, `Budget_WONo_Details.aspx`, to a modern, robust, and scalable Django 5.0+ solution. We will leverage AI-assisted automation by providing structured, clear instructions that can be systematically applied. The focus is on a "fat model, thin view" architecture, using HTMX + Alpine.js for dynamic frontends, DataTables for data presentation, and adhering strictly to Django best practices.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

**Instructions:**

From the SQL query in `FillGrid()` and the `GridView2` bindings, we identify two key tables: `tblACC_Budget_WO` and `tblMIS_BudgetCode`.

**`tblACC_Budget_WO` (Main Budget Entry Table):**
*   `Id` (Primary Key, integer)
*   `SysDate` (varchar, stores date like 'DD-MM-YYYY')
*   `SysTime` (varchar, stores time)
*   `Amount` (decimal/float)
*   `BudgetCodeId` (Foreign Key to `tblMIS_BudgetCode.Id`, integer)
*   `WONo` (Work Order Number, string)
*   `FinYearId` (Financial Year ID, integer)
*   `CompId` (Company ID, integer)
*   `SessionId` (User Session ID, string)

**`tblMIS_BudgetCode` (Budget Code Master Table):**
*   `Id` (Primary Key, integer)
*   `Description` (string)
*   `Symbol` (string)

**Relationships:**
*   `tblACC_Budget_WO.BudgetCodeId` links to `tblMIS_BudgetCode.Id`.

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Read (Display):**
    *   The `FillGrid` method fetches details for a specific `WONo` and `BudgetCodeId` (from query string) from `tblACC_Budget_WO` and `tblMIS_BudgetCode`.
    *   It populates a header with `WO No`, `Budget Code` (`Symbol` + `WONo`), and `Description`.
    *   It displays a list of entries from `tblACC_Budget_WO` in a GridView, showing `SN`, `CK` (checkbox), `Date`, `Time`, and `Amount`.
    *   Redirects to another page if no records are found for the given `WONo` and `BudgetCodeId`.

*   **Update (Bulk):**
    *   The `BtnUpdate_Click` method iterates through the `GridView2` rows.
    *   For each row where the `CheckBox1` is checked, it updates the `Amount` in `tblACC_Budget_WO` for that specific `Id`.
    *   It also updates `SysDate`, `SysTime`, `CompId`, `FinYearId`, and `SessionId` (current user/session details).
    *   Amount validation: Must be a positive number (`^[1-9]\d*(\.\d+)?$`).

*   **Delete (Bulk):**
    *   The `BtnDelete_Click` method iterates through the `GridView2` rows.
    *   For each row where `CheckBox1` is checked, it deletes the corresponding entry from `tblACC_Budget_WO` based on `Id`.

*   **Client-Side Interactions:**
    *   `CheckBox1_CheckedChanged`: Toggles visibility between a `Label` and a `TextBox` for the `Amount` field in the `GridView`. This caused a server-side postback in ASP.NET.
    *   `BtnUpdate` and `BtnDelete` have client-side confirmation (`confirmationUpdate()`, `confirmationDelete()`).
    *   Pagination is handled by the `GridView`.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Instructions:**

*   **Header Information:**
    *   `lblWONo`: Displays Work Order Number.
    *   `lblCode`: Displays Budget Code (`Symbol` + `WONo`).
    *   `lblDesc`: Displays Budget Code Description.
*   **Data Grid:**
    *   `GridView2`: The main data presentation component, displaying rows of budget entries.
        *   Columns: `SN` (serial number), `CK` (checkbox for selection), `Id` (hidden), `Date`, `Time`, `Amount`.
        *   `Amount` field: Dynamically switches between `Label` (display mode) and `TextBox` (edit mode) based on the `CK` checkbox.
*   **Action Buttons:**
    *   `BtnUpdate`: Initiates the bulk update operation.
    *   `BtnDelete`: Initiates the bulk delete operation.
    *   `BtnCancel`: Redirects to the `Budget_WONo.aspx` list page.
*   **Feedback:**
    *   `lblMessage`: Displays success or error messages.

### Step 4: Generate Django Code

We will create a new Django application named `accounts` to house this functionality, as it aligns with the `Module_Accounts` in the ASP.NET path.

#### 4.1 Models (`accounts/models.py`)

Task: Create Django models based on the identified database schema. These models will include methods for business logic, adhering to the "fat model" principle.

```python
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
import re

class BudgetCode(models.Model):
    """
    Maps to tblMIS_BudgetCode for budget code master data.
    """
    id = models.IntegerField(primary_key=True, db_column='Id')
    description = models.CharField(max_length=255, db_column='Description', blank=True, null=True)
    symbol = models.CharField(max_length=50, db_column='Symbol', blank=True, null=True)

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return f"{self.symbol} - {self.description}"

class BudgetWODetail(models.Model):
    """
    Maps to tblACC_Budget_WO for individual budget entries associated with a Work Order.
    """
    id = models.IntegerField(primary_key=True, db_column='Id')
    # Using CharField for SysDate/SysTime as they are stored as varchar in legacy DB
    sys_date = models.CharField(max_length=20, db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(max_length=20, db_column='SysTime', blank=True, null=True)
    amount = models.DecimalField(max_digits=18, decimal_places=2, db_column='Amount')
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCodeId')
    wo_no = models.CharField(max_length=50, db_column='WONo')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(max_length=50, db_column='SessionId', blank=True, null=True)

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'tblACC_Budget_WO'
        verbose_name = 'Budget WO Detail'
        verbose_name_plural = 'Budget WO Details'
        # Ordering by Id Desc as per ASP.NET query
        ordering = ['-id']

    def __str__(self):
        return f"WO: {self.wo_no}, Budget: {self.budget_code.symbol}, Amount: {self.amount}"

    # Model Properties for formatted date/time (read-only for display)
    @property
    def display_date(self):
        # The ASP.NET query was parsing 'DD-MM-YYYY' or similar and reformatting.
        # Assuming sys_date is stored as 'DD-MM-YYYY'
        try:
            return timezone.datetime.strptime(self.sys_date, '%d-%m-%Y').strftime('%d-%m-%Y')
        except (ValueError, TypeError):
            return self.sys_date # Fallback if format is unexpected

    @property
    def display_time(self):
        # Assuming SysTime is stored as 'HH:MM:SS' or similar.
        return self.sys_time

    # Business logic methods for bulk operations
    @classmethod
    def get_details_for_wo_and_budget_code(cls, wo_no, budget_code_id, fin_year_id):
        """
        Fetches main budget code details and associated WO entries.
        Equivalent to FillGrid's data fetching.
        """
        try:
            budget_code_obj = BudgetCode.objects.get(id=budget_code_id)
            wo_details = cls.objects.filter(
                wo_no=wo_no,
                budget_code=budget_code_obj,
                fin_year_id=fin_year_id
            )
            return {
                'budget_code_obj': budget_code_obj,
                'wo_details': wo_details,
                'wo_no': wo_no, # Passed directly for display
            }
        except BudgetCode.DoesNotExist:
            return None # Or raise an error

    @classmethod
    def update_multiple_amounts(cls, updates, current_user_session_id, comp_id, fin_year_id):
        """
        Performs bulk updates on amounts for selected entries.
        'updates' is a list of {'id': <entry_id>, 'amount': <new_amount_decimal>}.
        """
        updated_count = 0
        current_datetime = timezone.now()
        current_date_str = current_datetime.strftime('%d-%m-%Y') # Consistent with legacy format
        current_time_str = current_datetime.strftime('%H:%M:%S') # Consistent with legacy format

        for entry_data in updates:
            try:
                obj = cls.objects.get(id=entry_data['id'])
                if entry_data['amount'] <= 0:
                     raise ValidationError("Amount must be a positive number.")
                
                obj.amount = entry_data['amount']
                obj.sys_date = current_date_str
                obj.sys_time = current_time_str
                obj.comp_id = comp_id
                obj.fin_year_id = fin_year_id
                obj.session_id = current_user_session_id
                obj.save(update_fields=['amount', 'sys_date', 'sys_time', 'comp_id', 'fin_year_id', 'session_id'])
                updated_count += 1
            except cls.DoesNotExist:
                # Log or handle case where ID not found
                pass
            except ValidationError as e:
                # Handle validation errors per item if needed
                print(f"Validation error for ID {entry_data['id']}: {e}")
                pass
        return updated_count

    @classmethod
    def delete_multiple_entries(cls, ids_to_delete):
        """
        Performs bulk deletion of selected entries.
        'ids_to_delete' is a list of entry IDs.
        """
        deleted_count, _ = cls.objects.filter(id__in=ids_to_delete).delete()
        return deleted_count

```

#### 4.2 Forms (`accounts/forms.py`)

Task: Define a Django form for the `Amount` field to handle input and validation, primarily for the inline editing scenario.

```python
from django import forms
from .models import BudgetWODetail
from django.core.validators import RegexValidator

# Validator for positive decimal numbers (matching ASP.NET's regex ^[1-9]\d*(\.\d+)?$)
positive_decimal_validator = RegexValidator(
    regex=r'^[1-9]\d*(\.\d+)?$',
    message="Amount must be a positive number."
)

class BudgetWODetailUpdateForm(forms.ModelForm):
    """
    Form for updating the 'Amount' field.
    Designed for individual row updates submitted via HTMX.
    """
    # Override 'amount' field to add specific validation and widget
    amount = forms.DecimalField(
        max_digits=18, 
        decimal_places=2,
        validators=[positive_decimal_validator],
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-right',
            'type': 'number', # Use number type for numeric input
            'step': '0.01', # Allow decimal input
            'x-model': 'editedAmount', # Alpine.js binding for local state
            'hx-post': 'this.dataset.hxPostUrl', # Use dataset for dynamic URL
            'hx-trigger': 'blur, keyup[enter] from:event.target changed', # Trigger on blur or enter
            'hx-swap': 'outerHTML',
            'hx-target': 'closest td',
            'hx-include': 'closest tr', # Include entire row for context if needed for validation
        })
    )

    class Meta:
        model = BudgetWODetail
        fields = ['id', 'amount'] # Only 'id' and 'amount' are relevant for update here.
        # id field needs to be hidden but present for identifying the row
        widgets = {
            'id': forms.HiddenInput(),
        }
    
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be a positive number.")
        return amount

```

#### 4.3 Views (`accounts/views.py`)

Task: Implement CRUD operations using Django Class-Based Views (CBVs), keeping them thin and delegating business logic to models.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, get_object_or_404
from django.db import transaction
from django.utils import timezone
from .models import BudgetWODetail, BudgetCode
from .forms import BudgetWODetailUpdateForm

# Helper to get user session data, replace with actual session/auth logic in production
def get_user_session_data(request):
    # This is a placeholder. In a real Django app, get these from request.user,
    # or a robust session management system.
    # For now, simulate from session or default values
    comp_id = request.session.get('compid', 1) # Default to 1 if not in session
    fin_year_id = request.session.get('finyear', 2024) # Default to 2024
    session_id = request.session.get('username', 'system_user') # Default

    return {
        'comp_id': comp_id,
        'fin_year_id': fin_year_id,
        'session_id': session_id
    }

class BudgetWODetailView(TemplateView):
    """
    Main view to display budget details for a specific WO No and Budget Code.
    Corresponds to the overall Budget_WONo_Details.aspx page.
    """
    template_name = 'accounts/budgetwodetail/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        wo_no = self.kwargs.get('wo_no')
        budget_code_id = self.kwargs.get('budget_code_id')
        
        session_data = get_user_session_data(self.request)
        fin_year_id = session_data['fin_year_id']

        # Fetch header and initial data using model method
        data = BudgetWODetail.get_details_for_wo_and_budget_code(
            wo_no, budget_code_id, fin_year_id
        )

        if not data or not data['wo_details'].exists():
            # Redirect similar to ASP.NET's Response.Redirect if no data
            # Adjust 'budget_wo_list' to your actual list page for WO
            messages.info(self.request, "No budget details found for the specified WO and Budget Code.")
            return HttpResponseRedirect(reverse_lazy('budget_wo_list', kwargs={'wo_no': wo_no})) # Redirect to a generic list page
        
        context['wo_no'] = data['wo_no']
        context['budget_code'] = data['budget_code_obj']
        # The list of details will be loaded via HTMX into _table.html
        return context

class BudgetWODetailTablePartialView(View):
    """
    HTMX endpoint to render just the table content.
    """
    def get(self, request, wo_no, budget_code_id):
        session_data = get_user_session_data(request)
        fin_year_id = session_data['fin_year_id']
        
        data = BudgetWODetail.get_details_for_wo_and_budget_code(
            wo_no, budget_code_id, fin_year_id
        )
        
        context = {
            'budget_wo_details': data['wo_details'] if data else BudgetWODetail.objects.none(),
            'wo_no': wo_no, # Needed for passing to form URLs
            'budget_code_id': budget_code_id # Needed for passing to form URLs
        }
        return render(request, 'accounts/budgetwodetail/_table.html', context)

class BudgetWODetailToggleEditView(View):
    """
    HTMX endpoint to toggle amount display (Label vs. TextBox) for a single row.
    This replaces the CheckBox1_CheckedChanged server-side logic with a cleaner
    HTMX/Alpine.js pattern that only updates the relevant cell.
    """
    def post(self, request, wo_no, budget_code_id, pk):
        instance = get_object_or_404(BudgetWODetail, pk=pk)
        
        # Determine current mode (e.g., from 'x-data' attribute or an explicit field)
        # For this example, we assume the HTMX request from the checkbox implies 'edit_mode'
        # A more robust solution might pass a 'checked' parameter or a form field.
        
        # The Alpine.js solution is preferred for this interaction;
        # this HTMX view can be used for a server-side toggle if Alpine.js is not suitable.
        # For now, we'll assume Alpine.js handles the client-side toggle.
        # This view primarily exists if you want to handle individual cell updates with a form.
        
        # If we *were* to use HTMX for inline editing of a single field (not the bulk update):
        # We would create a form for a single instance, validate, save, and return the updated label.
        form = BudgetWODetailUpdateForm(request.POST, instance=instance)
        if form.is_valid():
            # If validated, save the field and return the label (non-editable view)
            form.save()
            # Return the label for the amount field
            context = {'obj': instance, 'wo_no': wo_no, 'budget_code_id': budget_code_id}
            return render(request, 'accounts/budgetwodetail/_amount_display.html', context)
        else:
            # If not valid, return the form with errors (editable view)
            context = {'form': form, 'obj': instance, 'wo_no': wo_no, 'budget_code_id': budget_code_id}
            return render(request, 'accounts/budgetwodetail/_amount_form.html', context)


class BudgetWODetailBulkActionView(View):
    """
    Handles bulk update and delete operations.
    Combines BtnUpdate_Click and BtnDelete_Click logic.
    """
    def post(self, request, wo_no, budget_code_id):
        session_data = get_user_session_data(request)
        user_session_id = session_data['session_id']
        comp_id = session_data['comp_id']
        fin_year_id = session_data['fin_year_id']
        
        action = request.POST.get('action') # 'update' or 'delete'

        if action == 'update':
            updates = []
            # Collect data for checked items from POST
            # We expect input fields named like 'amount_<id>' and 'selected_ids' for checked items
            selected_ids = request.POST.getlist('selected_ids')
            for item_id in selected_ids:
                try:
                    amount_key = f'amount_{item_id}'
                    amount_val = request.POST.get(amount_key)
                    if amount_val:
                        updates.append({
                            'id': int(item_id),
                            'amount': float(amount_val)
                        })
                except (ValueError, TypeError):
                    messages.error(request, f"Invalid amount for ID {item_id}.")
                    # Continue to next item or return an error response
            
            with transaction.atomic():
                updated_count = BudgetWODetail.update_multiple_amounts(
                    updates, user_session_id, comp_id, fin_year_id
                )
            messages.success(request, f"{updated_count} records updated successfully.")

        elif action == 'delete':
            ids_to_delete = [int(item_id) for item_id in request.POST.getlist('selected_ids')]
            
            with transaction.atomic():
                deleted_count = BudgetWODetail.delete_multiple_entries(ids_to_delete)
            messages.success(request, f"{deleted_count} records deleted successfully.")

        else:
            messages.error(request, "Invalid action specified.")
        
        # Use HX-Trigger to refresh the table without a full page reload
        response = HttpResponse(status=204) # No content, just triggers
        response['HX-Trigger'] = 'refreshBudgetWODetailList'
        return response

```

#### 4.4 Templates

Task: Create templates for each view, leveraging HTMX for dynamic interactions and DataTables for lists.

**File:** `accounts/templates/accounts/budgetwodetail/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Budget Details</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            <div class="flex items-center">
                <label class="font-semibold text-lg">WO No:</label>
                <span class="ml-2 text-lg text-blue-600 font-bold" id="lblWONo">{{ wo_no }}</span>
            </div>
            <div class="flex items-center">
                <label class="font-semibold text-lg">Budget Code:</label>
                <span class="ml-2 text-lg text-blue-600 font-bold" id="lblCode">{{ budget_code.symbol }}{{ wo_no }}</span>
            </div>
            <div class="md:col-span-2 flex items-center">
                <label class="font-semibold text-lg">Description:</label>
                <span class="ml-2 text-lg" id="lblDesc">{{ budget_code.description }}</span>
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <div id="budgetwodetailTable-container"
             hx-trigger="load, refreshBudgetWODetailList from:body"
             hx-get="{% url 'budgetwodetail_table' wo_no=wo_no budget_code_id=budget_code.id %}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading Budget Details...</p>
            </div>
        </div>

        <div class="mt-6 flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md transition duration-200"
                hx-post="{% url 'budgetwodetail_bulk_action' wo_no=wo_no budget_code_id=budget_code.id %}"
                hx-include="#budgetWODetailForm"
                hx-confirm="Are you sure you want to update the selected items?"
                name="action" value="update"
            >
                Update Selected
            </button>
            <button 
                type="button" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md transition duration-200"
                hx-post="{% url 'budgetwodetail_bulk_action' wo_no=wo_no budget_code_id=budget_code.id %}"
                hx-include="#budgetWODetailForm"
                hx-confirm="Are you sure you want to delete the selected items?"
                name="action" value="delete"
            >
                Delete Selected
            </button>
            <a href="{% url 'budget_wo_list' wo_no=wo_no %}" 
               class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-5 rounded-md transition duration-200 inline-flex items-center justify-center">
                Cancel
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here for the main page
        // Individual row toggles are handled by Alpine within the _table.html
    });
</script>
{% endblock %}
```

**File:** `accounts/templates/accounts/budgetwodetail/_table.html` (Partial for DataTables)

```html
<div x-data="{ selectAll: false }" class="overflow-x-auto">
    <form id="budgetWODetailForm">
        {% csrf_token %}
        <table id="budgetWODetailTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg">
            <thead class="bg-gray-100">
                <tr>
                    <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                    <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                        <input type="checkbox" x-model="selectAll" @change="document.querySelectorAll('input[name=\'selected_ids\']').forEach(el => el.checked = selectAll)"> CK
                    </th>
                    <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Date</th>
                    <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Time</th>
                    <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                    <!-- No 'Actions' column as Update/Delete are bulk operations -->
                </tr>
            </thead>
            <tbody>
                {% for obj in budget_wo_details %}
                <tr x-data="{ isSelected: false, amountValue: '{{ obj.amount|floatformat:2 }}' }"
                    :class="{'bg-blue-50': isSelected}">
                    <td class="py-3 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 border-b border-gray-200">
                        <input type="checkbox" 
                               name="selected_ids" 
                               value="{{ obj.id }}" 
                               x-model="isSelected" 
                               @change="if (!isSelected) selectAll = false;">
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200">{{ obj.display_date }}</td>
                    <td class="py-3 px-4 border-b border-gray-200">{{ obj.display_time }}</td>
                    <td class="py-3 px-4 border-b border-gray-200 text-right">
                        <!-- Use Alpine.js for client-side toggle of label/input visibility -->
                        <div x-show="!isSelected" class="text-gray-800" :id="'lblAmount_' + {{ obj.id }}">{{ obj.amount|floatformat:2 }}</div>
                        <div x-show="isSelected">
                            <input type="number" 
                                   name="amount_{{ obj.id }}" 
                                   step="0.01" 
                                   :value="amountValue" 
                                   @input="amountValue = $event.target.value"
                                   class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-right">
                        </div>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="5" class="py-4 px-4 text-center text-gray-500">No budget entries found.</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </form>
</div>

<script>
    // Ensure DataTables is initialized only once and correctly on content reload
    if ($.fn.DataTable.isDataTable('#budgetWODetailTable')) {
        $('#budgetWODetailTable').DataTable().destroy();
    }
    $('#budgetWODetailTable').DataTable({
        "pageLength": 10, // Default page size
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Page length options
        "columnDefs": [
            { "orderable": false, "targets": [0, 1] }, // Disable sorting for SN and CK
            { "searchable": false, "targets": [0, 1] } // Disable searching for SN and CK
        ]
    });
</script>
```

**Note on `_amount_display.html` and `_amount_form.html`:** The original ASP.NET behavior of toggling between Label and TextBox for `Amount` is best handled client-side with Alpine.js as shown above. The `BudgetWODetailToggleEditView` is thus not strictly necessary for this specific interaction if Alpine.js is fully utilized, but it could be adapted for more complex inline editing scenarios requiring server-side validation/persistence per field. For bulk update, the values from the visible textboxes will be collected via `hx-include`.

#### 4.5 URLs (`accounts/urls.py`)

Task: Define URL patterns for the views, including parameters for Work Order Number and Budget Code ID.

```python
from django.urls import path
from .views import (
    BudgetWODetailView, 
    BudgetWODetailTablePartialView, 
    BudgetWODetailBulkActionView,
    # BudgetWODetailToggleEditView # Not strictly needed if Alpine.js handles toggle
)

urlpatterns = [
    # Main page for specific WO No and Budget Code
    path('budget-wo-details/<str:wo_no>/<int:budget_code_id>/', 
         BudgetWODetailView.as_view(), 
         name='budgetwodetail_list'),

    # HTMX endpoint for the table content (for dynamic refresh)
    path('budget-wo-details/<str:wo_no>/<int:budget_code_id>/table/', 
         BudgetWODetailTablePartialView.as_view(), 
         name='budgetwodetail_table'),

    # Endpoint for bulk update/delete actions
    path('budget-wo-details/<str:wo_no>/<int:budget_code_id>/bulk-action/', 
         BudgetWODetailBulkActionView.as_view(), 
         name='budgetwodetail_bulk_action'),
    
    # Placeholder for a generic WO list, as per the cancel button redirect
    # This URL would likely be in another app or a more general accounts/urls.py
    path('budget-wo/<str:wo_no>/', 
         lambda request, wo_no: HttpResponseRedirect(reverse_lazy('budgetwodetail_list', kwargs={'wo_no': wo_no, 'budget_code_id': 1})), # Redirect to a default budget code or adjust
         name='budget_wo_list'), # This would point to the original Budget_WONo.aspx equivalent
]

```
**Note on `budget_wo_list` URL:** The original ASP.NET `BtnCancel_Click` redirects to `~/Module/MIS/Transactions/Budget_WONo.aspx?WONo=...`. This suggests a page listing all budget entries for a given `WONo` without a specific `BudgetCodeId`. For this modernization plan, we've provided a placeholder URL that redirects back to the main `budgetwodetail_list` page, assuming a default `budget_code_id` or that the main list can handle just `wo_no`. In a real application, you'd create a dedicated `BudgetWOListView` for this.

#### 4.6 Tests (`accounts/tests.py`)

Task: Write comprehensive unit tests for models and integration tests for views, ensuring high code coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection, transaction
from unittest.mock import patch
from decimal import Decimal
from .models import BudgetCode, BudgetWODetail

# Mock timezone.now() for consistent timestamps in updates
def mock_now():
    class MockDatetime:
        def strftime(self, fmt):
            if fmt == '%d-%m-%Y':
                return '01-01-2023'
            elif fmt == '%H:%M:%S':
                return '12:30:00'
            return '' # Default
    return MockDatetime()


class BudgetCodeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a mock database table and insert data
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE tblMIS_BudgetCode (
                    Id INT PRIMARY KEY,
                    Description VARCHAR(255),
                    Symbol VARCHAR(50)
                );
            """)
            cursor.execute("INSERT INTO tblMIS_BudgetCode (Id, Description, Symbol) VALUES (1, 'Office Supplies', 'OS');")
            cursor.execute("INSERT INTO tblMIS_BudgetCode (Id, Description, Symbol) VALUES (2, 'Travel Expenses', 'TRV');")

    def test_budgetcode_creation(self):
        budget_code = BudgetCode.objects.get(id=1)
        self.assertEqual(budget_code.description, 'Office Supplies')
        self.assertEqual(budget_code.symbol, 'OS')

    def test_str_representation(self):
        budget_code = BudgetCode.objects.get(id=1)
        self.assertEqual(str(budget_code), 'OS - Office Supplies')

    def test_verbose_name(self):
        self.assertEqual(BudgetCode._meta.verbose_name, 'Budget Code')
        self.assertEqual(BudgetCode._meta.verbose_name_plural, 'Budget Codes')

class BudgetWODetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create mock database tables and insert data
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE tblMIS_BudgetCode (
                    Id INT PRIMARY KEY,
                    Description VARCHAR(255),
                    Symbol VARCHAR(50)
                );
            """)
            cursor.execute("INSERT INTO tblMIS_BudgetCode (Id, Description, Symbol) VALUES (101, 'Project Alpha', 'PA');")
            cursor.execute("INSERT INTO tblMIS_BudgetCode (Id, Description, Symbol) VALUES (102, 'Marketing Campaign', 'MC');")

            cursor.execute("""
                CREATE TABLE tblACC_Budget_WO (
                    Id INT PRIMARY KEY,
                    SysDate VARCHAR(20),
                    SysTime VARCHAR(20),
                    Amount DECIMAL(18,2),
                    BudgetCodeId INT,
                    WONo VARCHAR(50),
                    FinYearId INT,
                    CompId INT,
                    SessionId VARCHAR(50)
                );
            """)
            cursor.execute("INSERT INTO tblACC_Budget_WO (Id, SysDate, SysTime, Amount, BudgetCodeId, WONo, FinYearId, CompId, SessionId) VALUES (1, '01-01-2023', '10:00:00', 100.50, 101, 'WO123', 2023, 1, 'user1');")
            cursor.execute("INSERT INTO tblACC_Budget_WO (Id, SysDate, SysTime, Amount, BudgetCodeId, WONo, FinYearId, CompId, SessionId) VALUES (2, '02-01-2023', '11:00:00', 250.75, 101, 'WO123', 2023, 1, 'user1');")
            cursor.execute("INSERT INTO tblACC_Budget_WO (Id, SysDate, SysTime, Amount, BudgetCodeId, WONo, FinYearId, CompId, SessionId) VALUES (3, '03-01-2023', '12:00:00', 50.00, 102, 'WO456', 2023, 1, 'user2');")

    def test_budgetwodetail_creation(self):
        detail = BudgetWODetail.objects.get(id=1)
        self.assertEqual(detail.wo_no, 'WO123')
        self.assertEqual(detail.amount, Decimal('100.50'))
        self.assertEqual(detail.budget_code.symbol, 'PA')

    def test_display_date_property(self):
        detail = BudgetWODetail.objects.get(id=1)
        self.assertEqual(detail.display_date, '01-01-2023')

        # Test with invalid date format
        detail.sys_date = 'invalid-date'
        self.assertEqual(detail.display_date, 'invalid-date')

    def test_display_time_property(self):
        detail = BudgetWODetail.objects.get(id=1)
        self.assertEqual(detail.display_time, '10:00:00')

    def test_get_details_for_wo_and_budget_code_found(self):
        data = BudgetWODetail.get_details_for_wo_and_budget_code('WO123', 101, 2023)
        self.assertIsNotNone(data)
        self.assertEqual(data['wo_no'], 'WO123')
        self.assertEqual(data['budget_code_obj'].symbol, 'PA')
        self.assertEqual(data['wo_details'].count(), 2)

    def test_get_details_for_wo_and_budget_code_not_found(self):
        data = BudgetWODetail.get_details_for_wo_and_budget_code('NONEXISTENT_WO', 101, 2023)
        self.assertIsNotNone(data) # It returns empty queryset, not None for details
        self.assertFalse(data['wo_details'].exists())

        data_no_budget_code = BudgetWODetail.get_details_for_wo_and_budget_code('WO123', 999, 2023)
        self.assertIsNone(data_no_budget_code) # Returns None if budget_code does not exist


    @patch('accounts.models.timezone.now', side_effect=mock_now)
    def test_update_multiple_amounts_success(self, mock_tz_now):
        updates = [
            {'id': 1, 'amount': Decimal('150.00')},
            {'id': 2, 'amount': Decimal('300.00')},
        ]
        
        with transaction.atomic():
            updated_count = BudgetWODetail.update_multiple_amounts(
                updates, 'test_user', 1, 2023
            )
        
        self.assertEqual(updated_count, 2)
        detail1 = BudgetWODetail.objects.get(id=1)
        detail2 = BudgetWODetail.objects.get(id=2)
        
        self.assertEqual(detail1.amount, Decimal('150.00'))
        self.assertEqual(detail2.amount, Decimal('300.00'))
        self.assertEqual(detail1.sys_date, '01-01-2023')
        self.assertEqual(detail1.sys_time, '12:30:00')
        self.assertEqual(detail1.session_id, 'test_user')

    @patch('accounts.models.timezone.now', side_effect=mock_now)
    def test_update_multiple_amounts_invalid_amount(self, mock_tz_now):
        updates = [
            {'id': 1, 'amount': Decimal('0.00')}, # Invalid amount
            {'id': 2, 'amount': Decimal('300.00')},
        ]
        
        with transaction.atomic():
            updated_count = BudgetWODetail.update_multiple_amounts(
                updates, 'test_user', 1, 2023
            )
        
        # Only 1 record should be updated successfully (ID 2)
        self.assertEqual(updated_count, 1)
        detail1 = BudgetWODetail.objects.get(id=1)
        detail2 = BudgetWODetail.objects.get(id=2)
        
        self.assertEqual(detail1.amount, Decimal('100.50')) # Should remain unchanged
        self.assertEqual(detail2.amount, Decimal('300.00'))


    def test_delete_multiple_entries_success(self):
        ids_to_delete = [1, 3]
        
        with transaction.atomic():
            deleted_count = BudgetWODetail.delete_multiple_entries(ids_to_delete)
        
        self.assertEqual(deleted_count, 2)
        self.assertFalse(BudgetWODetail.objects.filter(id=1).exists())
        self.assertFalse(BudgetWODetail.objects.filter(id=3).exists())
        self.assertTrue(BudgetWODetail.objects.filter(id=2).exists())


class BudgetWODetailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create mock database tables and insert data for views tests
        with connection.cursor() as cursor:
            cursor.execute("""
                CREATE TABLE tblMIS_BudgetCode (
                    Id INT PRIMARY KEY,
                    Description VARCHAR(255),
                    Symbol VARCHAR(50)
                );
            """)
            cursor.execute("INSERT INTO tblMIS_BudgetCode (Id, Description, Symbol) VALUES (101, 'Project Alpha', 'PA');")
            cursor.execute("INSERT INTO tblMIS_BudgetCode (Id, Description, Symbol) VALUES (102, 'Marketing Campaign', 'MC');")

            cursor.execute("""
                CREATE TABLE tblACC_Budget_WO (
                    Id INT PRIMARY KEY,
                    SysDate VARCHAR(20),
                    SysTime VARCHAR(20),
                    Amount DECIMAL(18,2),
                    BudgetCodeId INT,
                    WONo VARCHAR(50),
                    FinYearId INT,
                    CompId INT,
                    SessionId VARCHAR(50)
                );
            """)
            cursor.execute("INSERT INTO tblACC_Budget_WO (Id, SysDate, SysTime, Amount, BudgetCodeId, WONo, FinYearId, CompId, SessionId) VALUES (1, '01-01-2023', '10:00:00', 100.50, 101, 'WO123', 2023, 1, 'user1');")
            cursor.execute("INSERT INTO tblACC_Budget_WO (Id, SysDate, SysTime, Amount, BudgetCodeId, WONo, FinYearId, CompId, SessionId) VALUES (2, '02-01-2023', '11:00:00', 250.75, 101, 'WO123', 2023, 1, 'user1');")
            cursor.execute("INSERT INTO tblACC_Budget_WO (Id, SysDate, SysTime, Amount, BudgetCodeId, WONo, FinYearId, CompId, SessionId) VALUES (3, '03-01-2023', '12:00:00', 50.00, 102, 'WO456', 2023, 1, 'user2');")
    
    def setUp(self):
        self.client = Client()
        self.wo_no = 'WO123'
        self.budget_code_id = 101
        self.fin_year_id = 2023 # Default for testing
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023
        self.client.session['username'] = 'test_user'

    def test_main_view_success(self):
        url = reverse('budgetwodetail_list', kwargs={'wo_no': self.wo_no, 'budget_code_id': self.budget_code_id})
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgetwodetail/list.html')
        self.assertContains(response, self.wo_no)
        self.assertContains(response, 'Project Alpha') # Description

    def test_main_view_no_data_redirect(self):
        # Test case where budget code exists but no WO details
        url = reverse('budgetwodetail_list', kwargs={'wo_no': 'NONEXISTENT_WO', 'budget_code_id': self.budget_code_id})
        response = self.client.get(url, follow=True) # Follow redirect
        self.assertEqual(response.status_code, 200) # Should redirect to budget_wo_list
        self.assertRedirects(response, reverse('budget_wo_list', kwargs={'wo_no': 'NONEXISTENT_WO'}))
        self.assertContains(response, "No budget details found for the specified WO and Budget Code.") # Message after redirect

        # Test case where budget code does not exist
        url = reverse('budgetwodetail_list', kwargs={'wo_no': self.wo_no, 'budget_code_id': 999})
        response = self.client.get(url, follow=True)
        self.assertEqual(response.status_code, 200) # Should redirect to budget_wo_list
        self.assertRedirects(response, reverse('budget_wo_list', kwargs={'wo_no': self.wo_no}))


    def test_table_partial_view(self):
        url = reverse('budgetwodetail_table', kwargs={'wo_no': self.wo_no, 'budget_code_id': self.budget_code_id})
        response = self.client.get(url, HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budgetwodetail/_table.html')
        self.assertContains(response, 'WO123')
        self.assertContains(response, '100.50')
        self.assertContains(response, '250.75')
        self.assertNotContains(response, '50.00') # This is for WO456

    @patch('accounts.views.timezone.now', side_effect=mock_now)
    def test_bulk_update_action(self, mock_tz_now):
        url = reverse('budgetwodetail_bulk_action', kwargs={'wo_no': self.wo_no, 'budget_code_id': self.budget_code_id})
        data = {
            'action': 'update',
            'selected_ids': ['1', '2'],
            'amount_1': '110.00',
            'amount_2': '260.00',
            'csrfmiddlewaretoken': self.client.get(url).cookies['csrftoken'].value, # Get a valid CSRF token
        }
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetWODetailList')
        
        # Verify database update
        self.assertEqual(BudgetWODetail.objects.get(id=1).amount, Decimal('110.00'))
        self.assertEqual(BudgetWODetail.objects.get(id=2).amount, Decimal('260.00'))
        # Check that SysDate/SysTime/SessionId are updated
        self.assertEqual(BudgetWODetail.objects.get(id=1).sys_date, '01-01-2023')
        self.assertEqual(BudgetWODetail.objects.get(id=1).session_id, 'test_user')


    def test_bulk_delete_action(self):
        url = reverse('budgetwodetail_bulk_action', kwargs={'wo_no': self.wo_no, 'budget_code_id': self.budget_code_id})
        data = {
            'action': 'delete',
            'selected_ids': ['1'],
            'csrfmiddlewaretoken': self.client.get(url).cookies['csrftoken'].value,
        }
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetWODetailList')
        
        # Verify database delete
        self.assertFalse(BudgetWODetail.objects.filter(id=1).exists())
        self.assertTrue(BudgetWODetail.objects.filter(id=2).exists()) # Other record should remain


    def test_bulk_action_invalid_action(self):
        url = reverse('budgetwodetail_bulk_action', kwargs={'wo_no': self.wo_no, 'budget_code_id': self.budget_code_id})
        data = {
            'action': 'invalid_action',
            'selected_ids': ['1'],
            'csrfmiddlewaretoken': self.client.get(url).cookies['csrftoken'].value,
        }
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Still 204 for HTMX, but message should be set
        # Check for message on client-side after refresh, or potentially in logs

    # Test for validation in bulk update
    def test_bulk_update_invalid_amount_validation(self):
        url = reverse('budgetwodetail_bulk_action', kwargs={'wo_no': self.wo_no, 'budget_code_id': self.budget_code_id})
        data = {
            'action': 'update',
            'selected_ids': ['1', '2'],
            'amount_1': '0.00',  # Invalid amount
            'amount_2': '260.00',
            'csrfmiddlewaretoken': self.client.get(url).cookies['csrftoken'].value,
        }
        
        response = self.client.post(url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response
        # Verify that amount_1 was NOT updated due to validation error
        self.assertEqual(BudgetWODetail.objects.get(id=1).amount, Decimal('100.50'))
        self.assertEqual(BudgetWODetail.objects.get(id=2).amount, Decimal('260.00'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Data Loading:** `budgetwodetailTable-container` div uses `hx-get` to `{% url 'budgetwodetail_table' ... %}` to load the table content dynamically on page load and `refreshBudgetWODetailList` event.
*   **HTMX for Bulk Actions:** The "Update Selected" and "Delete Selected" buttons use `hx-post` to `{% url 'budgetwodetail_bulk_action' ... %}`. They include all data from the `budgetWODetailForm` using `hx-include="#budgetWODetailForm"`. Upon successful operation (HTTP 204 response from server), an `HX-Trigger: refreshBudgetWODetailList` header is sent, prompting the table to reload.
*   **Alpine.js for Client-side UI State:**
    *   The `_table.html` uses `x-data="{ selectAll: false }"` at the `div` level for overall state management.
    *   Each table row `<tr>` has `x-data="{ isSelected: false, amountValue: '{{ obj.amount|floatformat:2 }}' }"` to manage its individual checkbox state and the editable amount value.
    *   The "Select All" checkbox uses `x-model="selectAll"` and `@change` to toggle all individual row checkboxes.
    *   Individual row checkboxes use `x-model="isSelected"` and `@change="if (!isSelected) selectAll = false;"` to update their state and deselect "Select All" if any individual box is unchecked.
    *   The `Amount` cell (`<td>`) uses `x-show="!isSelected"` for the `Label` and `x-show="isSelected"` for the `TextBox`, making the field editable only when the row's checkbox is checked. The `TextBox` uses `x-model="amountValue"` to keep its input synchronized with the Alpine state.
*   **DataTables Integration:** A `script` block within `_table.html` initializes DataTables on the rendered table `#budgetWODetailTable`. It includes `pageLength`, `lengthMenu`, and `columnDefs` for sorting and searching control, similar to the `GridView`'s pagination and column settings.

### Final Notes

*   **Placeholders:** Replace `core/base.html` with your actual base template path. The `budget_wo_list` URL is a placeholder and should be implemented as a proper view/URL if that functionality is needed.
*   **Security:** In a production environment, ensure robust authentication and authorization. `Session["compid"]`, `Session["finyear"]`, `Session["username"]` from ASP.NET must be securely mapped to Django's `request.user` attributes or a proper session management system. The `get_user_session_data` function is a simplified representation.
*   **Database Migrations:** As `managed = False` is used for existing tables, Django won't create or modify them. You'll need to ensure your Django `settings.py` is configured to connect to the existing SQL Server database.
*   **Error Handling:** The provided code has basic error handling. In a production system, more robust logging, user feedback, and specific exception handling would be required.
*   **Styling:** The templates use basic Tailwind CSS classes. You would need to ensure Tailwind CSS is properly configured in your Django project.
*   **User Feedback:** The `messages` framework is used for user feedback, which should be displayed in `base.html` (e.g., using `{% include 'partials/_messages.html' %}`).