## ASP.NET to Django Conversion Script:

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET application, specifically the "Budget Distribution Time" module, to a modern Django 5.0+ solution. Our approach prioritizes AI-assisted automation, fat models, thin views, and a responsive frontend using HTMX, Alpine.js, and DataTables, all while maintaining a strong focus on business value and clarity for non-technical stakeholders.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
Based on the `SqlDataSource` definitions and `SQL` commands in the C# code-behind, we identify three primary tables:

- **`BusinessGroup`**: Used for the initial selection.
    - Columns: `Id` (INT, Primary Key), `Name` (NVARCHAR), `Symbol` (NVARCHAR).
    - SQL: `SELECT Id, Name, Symbol FROM BusinessGroup Where Id not in ('1')`
- **`tblHR_Grade`**: Represents "Budget Codes" or "Accounts".
    - Columns: `Id` (INT, Primary Key), `Description` (NVARCHAR, aliased as `Name`), `Symbol` (NVARCHAR).
    - SQL: `SELECT Id, Description AS Name, Symbol FROM tblHR_Grade where Id!=1`
- **`tblACC_Budget_Dept_Time`**: Stores the actual budget allocations.
    - Columns: `SysDate` (DATETIME), `SysTime` (NVARCHAR), `CompId` (INT), `FinYearId` (INT), `SessionId` (NVARCHAR), `BGGroup` (INT, likely FK to `BusinessGroup.Id`), `BudgetCodeId` (INT, likely FK to `tblHR_Grade.Id`), `Hour` (FLOAT/DECIMAL).
    - SQL: `Insert into tblACC_Budget_Dept_Time (...) VALUES (...)`

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**

-   **Read (R):**
    -   Displaying `BusinessGroup` list (`GridView2`).
    -   Displaying `tblHR_Grade` list (`GridView1`).
    -   Calculating and displaying `Budget Hour`, `Used Hour`, and `Bal Hour` for each `tblHR_Grade` item, which is a complex read operation involving aggregation from `tblACC_Budget_Dept_Time` and custom helper functions (`CalBalBudgetAmt.TotBalBudget_BG`, `Cal_Used_Hours.TotFillPart`).
    -   Loading initial `BusinessGroup` details from `Request.QueryString`.

-   **Create (C):**
    -   Inserting new budget hour allocations into `tblACC_Budget_Dept_Time` via `BtnInsert_Click`. This involves iterating through selected items in `GridView1` and saving `TxtHour` values.

-   **Update (U):**
    -   The ASP.NET code does not explicitly show an "update" for existing `tblACC_Budget_Dept_Time` records, only insertion. If a record already exists for a `BudgetCodeId` and `BGGroup` for the current `FinYearId`, inserting again would create duplicates. This suggests the "Hour" in the grid might be cumulative or represents a new allocation *addition*. For Django, we will assume an "upsert" or "add new allocation" pattern. The `TxtHour` allows users to input new values, which are then inserted. If the intention was to *update* an existing record, that logic would need to be re-evaluated. For now, we will stick to the "insert" behavior as seen.

-   **Delete (D):**
    -   No explicit delete functionality is present for budget allocations.

-   **Export:**
    -   Exporting the displayed grid data to Excel (`BtnExport_Click` using `ExportToExcel` class).

**Business Logic:**
-   **Budget Calculation:** The `CalculateBalAmt()` method is critical. It determines `Budget Hour`, `Used Hour`, and `Bal Hour` by querying `tblACC_Budget_Dept_Time` for current and previous financial years, and using external utility functions (`CalBalBudgetAmt`, `Cal_Used_Hours`). This logic needs to be faithfully recreated, preferably in the models themselves.
-   **Conditional Visibility:** The `TxtHour` (TextBox) becomes visible based on `CheckBox1` selection. This implies an "edit mode" for entering hours.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

-   **Tabs:** `TabContainer` maps to a multi-view Django template with HTMX-driven content loading or separate views. The `Work Order` tab uses an `iframe`, suggesting it's a distinct module, which we will not replicate here.
-   **Data Grids:** `GridView2` (Business Groups) and `GridView1` (Grades/Budget Codes) are prime candidates for Django templates leveraging DataTables.
-   **Selection and Filtering:** `LinkButton` "Select" in `GridView2` triggers a postback to filter `GridView1`. This will be an HTMX `hx-get` request updating the main content area.
-   **Data Entry:** `TextBox` `TxtHour` and `CheckBox` `CheckBox1` in `GridView1` for inputting budget hours. The checkbox toggles visibility of the text input. This can be handled with HTMX `hx-swap` of elements or Alpine.js for local UI state.
-   **Buttons:** `BtnInsert`, `BtnExport`, `btnCancel`. These will map to Django view actions (POST requests for insert, specific view for export, redirects for cancel). `BtnExport` will generate a CSV/Excel file.
-   **Labels:** `lblBGGroup`, `lblBG` display selected Business Group info. These will be rendered dynamically in the template based on context.

### Step 4: Generate Django Code

We will create a Django application named `budget_time`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema and embed business logic.

We will define three models: `BusinessGroup`, `Grade`, and `BudgetTimeAllocation`. The complex budget calculation logic from `CalculateBalAmt()` will be moved to the `BudgetTimeAllocation` model. Since `CompId`, `FinYearId`, and `SessionId` are system-wide or context-dependent, we'll assume they are provided or handled by the system for the purpose of these models.

**`budget_time/models.py`**

```python
from django.db import models
from django.db.models import Sum

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.name or f"Business Group {self.id}"

class Grade(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Grade'
        verbose_name = 'Grade'
        verbose_name_plural = 'Grades'

    def __str__(self):
        return self.description or f"Grade {self.id}"

class BudgetTimeAllocation(models.Model):
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=10, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    bg_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', related_name='budget_allocations')
    budget_code = models.ForeignKey(Grade, on_delete=models.DO_NOTHING, db_column='BudgetCodeId', related_name='budget_allocations')
    hour = models.FloatField(db_column='Hour', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Budget_Dept_Time'
        verbose_name = 'Budget Time Allocation'
        verbose_name_plural = 'Budget Time Allocations'
        # Consider adding unique_together constraint if allocation is per grade per business group per financial year
        # unique_together = (('bg_group', 'budget_code', 'fin_year_id'),)

    def __str__(self):
        return f"Budget for {self.budget_code.description} in {self.bg_group.name} - {self.hour} hrs"

    # Business logic methods for budget calculation
    # These methods simulate the complex logic from ASP.NET's CalculateBalAmt
    # and require re-implementation of `CalBalBudgetAmt` and `Cal_Used_Hours` utilities in Python.
    # For demonstration, placeholders are provided.
    
    @staticmethod
    def get_total_budget_for_grade(grade_id, business_group_id, comp_id, fin_year_id):
        """
        Calculates the total budget hour for a specific grade and business group
        up to the current financial year. Simulates ASP.NET's `selectBudget` logic.
        Requires re-implementation of `CalBalBudgetAmt.TotBalBudget_BG`.
        """
        # Placeholder for `CalBalBudgetAmt.TotBalBudget_BG(grade_id, comp_id, prev_year, 0)`
        # This function needs to be translated from C# to Python, querying historical data.
        # For simplicity, we'll assume it returns 0 for now or is integrated directly.
        
        # This part simulates the `selectBudget` query and aggregation
        total_current_budget = BudgetTimeAllocation.objects.filter(
            budget_code_id=grade_id,
            bg_group_id=business_group_id,
            fin_year_id__lte=fin_year_id
        ).aggregate(Sum('hour'))['hour__sum'] or 0.0

        # This `openingBalOfPrevYear` logic needs to be fully re-implemented.
        # Example: Call a utility function that performs the same DB logic as CalBalBudgetAmt.
        opening_balance_prev_year = 0.0 # Placeholder: Replace with actual calculation

        return round(total_current_budget + opening_balance_prev_year, 2)

    @staticmethod
    def get_used_hours_for_grade(grade_id, business_group_id, comp_id, fin_year_id):
        """
        Calculates the total used hours for a specific grade and business group.
        Simulates ASP.NET's `Cal_Used_Hours.TotFillPart`.
        Requires re-implementation of `Cal_Used_Hours.TotFillPart`.
        """
        # Placeholder: This function needs to be fully re-implemented in Python
        # It likely queries other tables (e.g., actual work order hours)
        # For now, return a dummy value or query a related dummy field
        
        # Example: Assuming a related table `WorkOrderHours` exists.
        # from your_app.models import WorkOrderHours # Hypothetical model
        # used_hours = WorkOrderHours.objects.filter(
        #     grade_id=grade_id,
        #     business_group_id=business_group_id,
        #     fin_year_id=fin_year_id
        # ).aggregate(Sum('hours_spent'))['hours_spent__sum'] or 0.0
        
        return round(0.0, 2) # Placeholder: Replace with actual calculation

    @property
    def budget_hour(self):
        """Calculates the total budget hour for this allocation context."""
        # For a specific grade within a specific business group and financial year context
        # The original code calculated this for each row in GridView1 (Grade)
        # based on the *selected* Business Group (HField.Text)
        # and current FinYearId.
        
        # This property should be called with the context of the selected Business Group
        # and current financial year. It's better as a static method on the Grade model
        # or a service/manager that takes `business_group_id` and `fin_year_id`
        
        # Re-evaluating based on FillGrid -> CalculateBalAmt logic:
        # It calculates budget for a GRADE (lblId) given a SELECTED BUSINESS GROUP (HField.Text)
        # So, the calculation should be on Grade model, given BG and FinYear.
        return 0.0 # Will be calculated via Grade.get_calculated_budget_details

    @property
    def used_hour(self):
        return 0.0 # Will be calculated via Grade.get_calculated_budget_details

    @property
    def balance_hour(self):
        return 0.0 # Will be calculated via Grade.get_calculated_budget_details


# Add business logic methods to the Grade model for convenience
class GradeManager(models.Manager):
    def with_budget_details(self, business_group_id, comp_id, fin_year_id):
        grades = self.filter(id__ne=1) # Original ASP.NET filter Id!=1
        results = []
        for grade in grades:
            budget_hour = BudgetTimeAllocation.get_total_budget_for_grade(
                grade.id, business_group_id, comp_id, fin_year_id
            )
            used_hour = BudgetTimeAllocation.get_used_hours_for_grade(
                grade.id, business_group_id, comp_id, fin_year_id
            )
            balance_hour = round(budget_hour - used_hour, 2)

            # Construct Budget Code Symbol (e.g., "GRD-BG")
            # Note: ViewState["BGSymbol"] is used in ASP.NET
            business_group_symbol = BusinessGroup.objects.filter(id=business_group_id).values_list('symbol', flat=True).first()
            budget_code_symbol = f"{grade.symbol}{business_group_symbol or ''}"

            results.append({
                'grade': grade,
                'budget_code_symbol': budget_code_symbol,
                'budget_hour': budget_hour,
                'used_hour': used_hour,
                'balance_hour': balance_hour,
                # Add a flag to indicate if link should be visible, based on budget_hour > 0
                'has_budget_details': budget_hour > 0 # Simulates HyperLink visibility
            })
        return results

Grade.add_to_class('objects', GradeManager())

```

#### 4.2 Forms

**Task:** Define a Django form for user input for budget allocations.

The original ASP.NET form allows entry of `TxtHour` for multiple grades simultaneously. Django's `formsets` are ideal for this. For a single allocation (which is how `BtnInsert_Click` seems to work, one row at a time if checked), we might use a simple form, but the UI implies multiple inputs. Let's create a form for a single `BudgetTimeAllocation` and note how it would be used in a formset context for the grid. For now, the "add new" button would create a single allocation. The "insert all checked" from the grid would be an HTMX POST with multiple form data.

**`budget_time/forms.py`**

```python
from django import forms
from .models import BudgetTimeAllocation, Grade, BusinessGroup

class BudgetTimeAllocationForm(forms.ModelForm):
    # These fields are hidden and populated by the view/context
    bg_group = forms.ModelChoiceField(queryset=BusinessGroup.objects.all(), widget=forms.HiddenInput(), required=True)
    budget_code = forms.ModelChoiceField(queryset=Grade.objects.all(), widget=forms.HiddenInput(), required=True)
    
    # Hour is the main input field
    hour = forms.FloatField(
        label="Budget Hour",
        min_value=0.01, # From RegularExpressionValidator "^[1-9]\d*(\.\d+)?$"
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter hours'
        })
    )

    class Meta:
        model = BudgetTimeAllocation
        fields = ['bg_group', 'budget_code', 'hour']
        # Other fields like sys_date, sys_time, comp_id, fin_year_id, session_id will be set by the view
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make hour field non-required initially if it's meant to be optional
        # For the specific use case where TxtHour is only visible when checkbox is checked,
        # we'll handle validation in the view for formset processing.
        # self.fields['hour'].required = False # Example for optionality
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs, ensuring thin views.

We will need views for:
1.  Listing Business Groups (`BusinessGroupListView`).
2.  Listing Grades with calculated budget details, filtered by selected Business Group (`BudgetTimeAllocationListView` or `GradeBudgetListView`).
3.  Handling the POST for inserting budget hours (`BudgetTimeAllocationCreateView` or `GradeBudgetAllocationUpdateView` which processes multiple inputs).
4.  A partial view for the DataTables content for Grades.
5.  An export view.

For the primary view (`Budget_Dist_Time.aspx`), which combines Business Group selection and Grade budgeting, we'll use a single view that renders the main page and uses HTMX for dynamic updates.

**`budget_time/views.py`**

```python
import datetime
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect, Http404
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.db.models import Sum

from .models import BusinessGroup, Grade, BudgetTimeAllocation
from .forms import BudgetTimeAllocationForm # We'll adapt this for formsets or individual row processing

class BudgetTimeMainView(TemplateView):
    """
    Combines the BusinessGroup selection and Grade budgeting list logic.
    This acts as the main entry point like the ASP.NET .aspx page.
    """
    template_name = 'budget_time/budget_time_main.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Simulating Session variables from ASP.NET (replace with actual logic)
        context['comp_id'] = self.request.session.get('compid', 1) # Example default
        context['fin_year_id'] = self.request.session.get('finyear', 2023) # Example default
        context['username'] = self.request.user.username if self.request.user.is_authenticated else 'anonymous'
        
        selected_bg_id = self.request.GET.get('bg_id')
        context['selected_bg_id'] = selected_bg_id
        context['selected_business_group'] = None
        context['business_groups'] = BusinessGroup.objects.filter(id__ne=1) # Filter from ASP.NET: Id not in ('1')

        if selected_bg_id:
            try:
                selected_bg_obj = BusinessGroup.objects.get(id=selected_bg_id)
                context['selected_business_group'] = selected_bg_obj
                
                # Fetch grades with calculated budget details
                context['grades_with_budgets'] = Grade.objects.with_budget_details(
                    business_group_id=selected_bg_obj.id,
                    comp_id=context['comp_id'],
                    fin_year_id=context['fin_year_id']
                )
            except BusinessGroup.DoesNotExist:
                messages.error(self.request, "Selected Business Group does not exist.")
                context['selected_bg_id'] = None # Clear invalid selection

        return context

# HTMX partial view for the Business Groups table
class BusinessGroupTablePartialView(ListView):
    model = BusinessGroup
    template_name = 'budget_time/_business_group_table.html'
    context_object_name = 'business_groups'

    def get_queryset(self):
        return super().get_queryset().filter(id__ne=1) # As per ASP.NET filter


# HTMX partial view for the Grades/Budget table
class GradeBudgetTablePartialView(View):
    """
    Renders the Grade table with budget details, to be swapped via HTMX.
    This replaces GridView1's data population logic.
    """
    def get(self, request, *args, **kwargs):
        selected_bg_id = request.GET.get('bg_id')
        
        # Simulating Session variables
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2023)

        if not selected_bg_id:
            # If no business group is selected, return an empty table or message
            return HttpResponse('<div class="text-center py-4 text-gray-500">Please select a Business Group to view budget details.</div>')

        try:
            selected_bg_obj = BusinessGroup.objects.get(id=selected_bg_id)
            grades_with_budgets = Grade.objects.with_budget_details(
                business_group_id=selected_bg_obj.id,
                comp_id=comp_id,
                fin_year_id=fin_year_id
            )
        except BusinessGroup.DoesNotExist:
            return HttpResponse('<div class="text-center py-4 text-red-500">Error: Selected Business Group not found.</div>')

        return HttpResponse(
            request.htmx.render(
                'budget_time/_grade_budget_table.html',
                {
                    'grades_with_budgets': grades_with_budgets,
                    'selected_business_group': selected_bg_obj,
                }
            )
        )

class BudgetTimeAllocationCreateView(View):
    """
    Handles the insertion of new budget hours.
    This replaces BtnInsert_Click logic, which processes multiple rows.
    """
    def post(self, request, *args, **kwargs):
        # Simulating Session variables
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2023)
        session_id = request.user.username if request.user.is_authenticated else 'anonymous'
        
        selected_bg_id = request.POST.get('selected_bg_id')
        if not selected_bg_id:
            messages.error(request, "Business Group not selected for allocation.")
            return HttpResponse(status=400)

        successful_allocations = 0
        try:
            with transaction.atomic():
                # Loop through the submitted data, expecting multiple grade_id and hour inputs
                # The keys from the frontend will be something like 'grade_<id>' and 'hour_<id>'
                for key, value in request.POST.items():
                    if key.startswith('hour_'):
                        grade_id = key.split('_')[1]
                        try:
                            hour_value = float(value)
                            if hour_value > 0: # Check for Hour > 0 as in ASP.NET
                                # Check if a checkbox was checked for this row
                                # This is crucial if we decide on a partial swap per row.
                                # For a global insert button, we assume the hour field is present
                                # if the user intended to input a value.
                                
                                # In ASP.NET, it inserts. If a record exists, it creates another.
                                # We replicate that behaviour.
                                BudgetTimeAllocation.objects.create(
                                    sys_date=datetime.date.today(),
                                    sys_time=datetime.datetime.now().strftime("%H:%M"),
                                    comp_id=comp_id,
                                    fin_year_id=fin_year_id,
                                    session_id=session_id,
                                    bg_group_id=selected_bg_id,
                                    budget_code_id=grade_id,
                                    hour=hour_value
                                )
                                successful_allocations += 1
                        except ValueError:
                            # Ignore non-numeric or empty hour fields
                            pass
        except Exception as e:
            messages.error(request, f"Error saving budget allocations: {e}")
            return HttpResponse(status=500)
        
        if successful_allocations > 0:
            messages.success(request, f"{successful_allocations} budget allocations saved successfully.")
        else:
            messages.info(request, "No budget allocations were saved.")

        # Trigger HTMX refresh for the grade budget table
        response = HttpResponse(status=204)
        response['HX-Trigger'] = 'refreshGradeBudgetList'
        return response

class BudgetTimeExportView(View):
    """
    Handles exporting the displayed grid data to Excel/CSV.
    Replaces BtnExport_Click logic.
    """
    def get(self, request, *args, **kwargs):
        selected_bg_id = request.GET.get('bg_id')
        if not selected_bg_id:
            messages.error(request, "Please select a Business Group to export.")
            return HttpResponseRedirect(reverse_lazy('budget_time_main'))

        # Simulating Session variables
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2023)

        try:
            selected_bg_obj = BusinessGroup.objects.get(id=selected_bg_id)
            grades_with_budgets = Grade.objects.with_budget_details(
                business_group_id=selected_bg_obj.id,
                comp_id=comp_id,
                fin_year_id=fin_year_id
            )
        except BusinessGroup.DoesNotExist:
            messages.error(request, "Selected Business Group not found for export.")
            return HttpResponseRedirect(reverse_lazy('budget_time_main'))

        # Create a CSV response (simulates Excel Export)
        import csv
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="Budget_BG.csv"'

        writer = csv.writer(response)
        writer.writerow(['Sr No', 'Symbol', 'Budget Code', 'Budget Hour', 'Used Hour', 'Bal Hour']) # Headers based on ASP.NET dtList

        for i, item in enumerate(grades_with_budgets):
            writer.writerow([
                i + 1,
                item['grade'].symbol,
                item['budget_code_symbol'],
                item['budget_hour'],
                item['used_hour'],
                item['balance_hour']
            ])
        return response

```

#### 4.4 Templates

**Task:** Create templates for each view, leveraging HTMX, Alpine.js, and DataTables.

We'll have a main template `budget_time_main.html` and partials for the dynamic content (`_business_group_table.html`, `_grade_budget_table.html`).

**`budget_time/templates/budget_time/budget_time_main.html`**

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ selectedBgId: '{{ selected_bg_id|default:'' }}' }">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Assign Budget</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="mb-4">
            <span class="font-bold text-blue-700">Business Group:</span>
            <span class="text-blue-700">{{ selected_business_group.name|default:'(Not Selected)' }}</span>
            <input type="hidden" name="selected_bg_id_hidden" x-bind:value="selectedBgId" id="selected_bg_id_hidden">
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Business Group Selection Panel (Left) -->
            <div>
                <h3 class="text-xl font-semibold mb-4 text-gray-700">Select Business Group</h3>
                <div id="business-group-table-container"
                     hx-get="{% url 'budget_time:business_group_table_partial' %}"
                     hx-trigger="load, refreshBusinessGroupList from:body"
                     hx-swap="innerHTML"
                     class="max-h-96 overflow-auto border border-gray-200 rounded-md">
                    <!-- Loading state for HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-500">Loading Business Groups...</p>
                    </div>
                </div>
                <div class="mt-4 flex justify-end">
                    <button class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                            onclick="window.location.href='{% url 'budget_time:budget_time_main' %}'">
                        Clear Selection
                    </button>
                </div>
            </div>

            <!-- Budget Distribution Panel (Right) -->
            <div>
                <h3 class="text-xl font-semibold mb-4 text-gray-700">Budget Details</h3>
                <div id="grade-budget-table-container"
                     hx-get="{% url 'budget_time:grade_budget_table_partial' %}?bg_id={{ selected_bg_id|default:'' }}"
                     hx-trigger="load, refreshGradeBudgetList from:body, selectedBgIdChanged from:body"
                     hx-swap="innerHTML"
                     hx-vars="js:{bg_id: selectedBgId}"
                     class="max-h-96 overflow-auto border border-gray-200 rounded-md">
                    <!-- Loading state for HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-500">Loading Budget Details...</p>
                    </div>
                </div>
                <div class="mt-4 flex justify-end space-x-2">
                    <button id="btnInsert"
                            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50"
                            x-bind:disabled="selectedBgId === ''"
                            hx-post="{% url 'budget_time:budget_time_insert' %}"
                            hx-include="#grade-budget-table-container form, #selected_bg_id_hidden"
                            hx-swap="none"
                            hx-confirm="Are you sure you want to insert these budget hours?"
                            hx-indicator="#insert-spinner">
                        <span id="insert-spinner" class="htmx-indicator inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></span>
                        Insert
                    </button>
                    <a href="{% url 'budget_time:budget_time_export' %}?bg_id={{ selected_bg_id|default:'' }}"
                       class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50 inline-flex items-center"
                       x-bind:class="{ 'pointer-events-none': selectedBgId === '' }"
                       x-bind:disabled="selectedBgId === ''">
                        Export
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/vendor/jquery.min.js' %}"></script>
<script src="{% static 'js/vendor/jquery.dataTables.min.js' %}"></script>
<link rel="stylesheet" type="text/css" href="{% static 'css/vendor/jquery.dataTables.min.css' %}">

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('budgetTimeApp', () => ({
            selectedBgId: '{{ selected_bg_id|default:'' }}',
            
            // Function to handle Business Group selection
            selectBusinessGroup(bgId) {
                this.selectedBgId = bgId;
                // Dispatch a custom event to trigger HTMX reload on the grade table
                document.body.dispatchEvent(new CustomEvent('selectedBgIdChanged'));
            }
        }));
    });

    // Make selectBusinessGroup globally accessible for HTMX
    window.selectBusinessGroup = (bgId) => {
        const component = document.querySelector('[x-data="budgetTimeApp"]');
        if (component) {
            Alpine.raw(component.__alpine.get
.data().selectBusinessGroup(bgId));
        }
    };

    // Listen for HTMX afterSwap events to initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'business-group-table-container') {
            $('#businessGroupTable').DataTable({
                "pageLength": 5,
                "lengthMenu": [[5, 10, 25, -1], [5, 10, 25, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rtip' // Custom DOM for styling
            });
        }
        if (evt.target.id === 'grade-budget-table-container') {
            $('#gradeBudgetTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rtip'
            });
        }
    });

</script>
{% endblock %}
```

**`budget_time/templates/budget_time/_business_group_table.html`** (Partial for `GridView2`)

```html
<table id="businessGroupTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for bg in business_groups %}
        <tr>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ bg.name }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ bg.symbol }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-xs"
                    onclick="window.selectBusinessGroup('{{ bg.id }}')">
                    Select
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-4 text-center text-gray-500">No Business Groups found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

```

**`budget_time/templates/budget_time/_grade_budget_table.html`** (Partial for `GridView1`)

```html
<form id="budgetAllocationForm">
    <table id="gradeBudgetTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Code</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Hour</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Used Hour</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bal Hour</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Details</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if grades_with_budgets %}
                {% for item in grades_with_budgets %}
                <tr x-data="{ isChecked: false }">
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        <input type="checkbox" 
                               name="chk_{{ item.grade.id }}" 
                               x-model="isChecked" 
                               class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded" />
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.grade.symbol }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.budget_code_symbol }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                        <span x-show="!isChecked">{{ item.budget_hour }}</span>
                        <input type="number" 
                               step="0.01" 
                               min="0.01"
                               name="hour_{{ item.grade.id }}" 
                               x-show="isChecked" 
                               class="w-24 px-2 py-1 border rounded text-sm" 
                               value="{{ item.budget_hour }}" />
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.used_hour }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.balance_hour }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {% if item.has_budget_details %}
                            <a href="#" class="text-blue-600 hover:text-blue-800 text-xs">Details</a>
                            {# Original Link: ~/Module/MIS/Transactions/Budget_Dist_Dept_Details_Time.aspx?BGId=" + HField.Text + "&Id=" + BGId + "&ModId=14 #}
                            {# This would link to a new Django view for details #}
                        {% else %}
                            <span class="text-gray-400 text-xs">N/A</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="8" class="py-4 px-4 text-center text-gray-500">
                        {% if selected_business_group %}
                            No budget details found for {{ selected_business_group.name }}.
                        {% else %}
                            Select a Business Group to view budget details.
                        {% endif %}
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
    <input type="hidden" name="selected_bg_id" value="{{ selected_business_group.id|default:'' }}">
</form>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**`budget_time/urls.py`**

```python
from django.urls import path
from .views import (
    BudgetTimeMainView,
    BusinessGroupTablePartialView,
    GradeBudgetTablePartialView,
    BudgetTimeAllocationCreateView,
    BudgetTimeExportView,
)

app_name = 'budget_time'

urlpatterns = [
    path('budget_dist_time/', BudgetTimeMainView.as_view(), name='budget_time_main'),
    # HTMX endpoints for partial table loads
    path('budget_dist_time/business_groups/table/', BusinessGroupTablePartialView.as_view(), name='business_group_table_partial'),
    path('budget_dist_time/grades/table/', GradeBudgetTablePartialView.as_view(), name='grade_budget_table_partial'),
    # Action endpoints
    path('budget_dist_time/insert/', BudgetTimeAllocationCreateView.as_view(), name='budget_time_insert'),
    path('budget_dist_time/export/', BudgetTimeExportView.as_view(), name='budget_time_export'),
    # Cancel buttons
    path('budget_dist_time/cancel/', lambda request: HttpResponseRedirect(reverse_lazy('budget_time:budget_time_main')), name='budget_time_cancel'),
    # Note: ASP.NET's "Cancel1" went to Menu.aspx. This would need a corresponding Django URL.
]

```

#### 4.6 Tests

**Task:** Write tests for the models and views, ensuring coverage.

We will write unit tests for the models and integration tests for the views. We'll mock `request.session` data for `comp_id` and `fin_year_id` as these are external to the immediate data.

**`budget_time/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from datetime import date

from .models import BusinessGroup, Grade, BudgetTimeAllocation

# Mocking external data for tests
MOCKED_COMP_ID = 123
MOCKED_FIN_YEAR_ID = 2024
MOCKED_USERNAME = 'testuser'

class BusinessGroupModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        BusinessGroup.objects.create(id=1, name='Default Group', symbol='DEF') # Excluded by filter
        BusinessGroup.objects.create(id=10, name='Business Group A', symbol='BGA')
        BusinessGroup.objects.create(id=20, name='Business Group B', symbol='BGB')

    def test_business_group_creation(self):
        bg = BusinessGroup.objects.get(id=10)
        self.assertEqual(bg.name, 'Business Group A')
        self.assertEqual(bg.symbol, 'BGA')

    def test_str_representation(self):
        bg = BusinessGroup.objects.get(id=10)
        self.assertEqual(str(bg), 'Business Group A')

class GradeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Grade.objects.create(id=1, description='Default Grade', symbol='DFLT') # Excluded by filter
        Grade.objects.create(id=101, description='Grade X', symbol='GX')
        Grade.objects.create(id=102, description='Grade Y', symbol='GY')
        
        # Create some related Business Groups and BudgetTimeAllocations for testing GradeManager
        bg_a = BusinessGroup.objects.create(id=10, name='Business Group A', symbol='BGA')
        bg_b = BusinessGroup.objects.create(id=20, name='Business Group B', symbol='BGB')
        grade_x = Grade.objects.get(id=101)
        
        BudgetTimeAllocation.objects.create(
            bg_group=bg_a, budget_code=grade_x, hour=100.0,
            comp_id=MOCKED_COMP_ID, fin_year_id=MOCKED_FIN_YEAR_ID - 1,
            sys_date=date(MOCKED_FIN_YEAR_ID - 1, 1, 1), sys_time='10:00', session_id=MOCKED_USERNAME
        )
        BudgetTimeAllocation.objects.create(
            bg_group=bg_a, budget_code=grade_x, hour=50.0,
            comp_id=MOCKED_COMP_ID, fin_year_id=MOCKED_FIN_YEAR_ID,
            sys_date=date(MOCKED_FIN_YEAR_ID, 1, 1), sys_time='11:00', session_id=MOCKED_USERNAME
        )
        # Another allocation for different BG
        BudgetTimeAllocation.objects.create(
            bg_group=bg_b, budget_code=grade_x, hour=200.0,
            comp_id=MOCKED_COMP_ID, fin_year_id=MOCKED_FIN_YEAR_ID,
            sys_date=date(MOCKED_FIN_YEAR_ID, 2, 1), sys_time='12:00', session_id=MOCKED_USERNAME
        )

    def test_grade_creation(self):
        grade = Grade.objects.get(id=101)
        self.assertEqual(grade.description, 'Grade X')
        self.assertEqual(grade.symbol, 'GX')

    def test_str_representation(self):
        grade = Grade.objects.get(id=101)
        self.assertEqual(str(grade), 'Grade X')

    @patch('budget_time.models.BudgetTimeAllocation.get_used_hours_for_grade', return_value=10.0)
    @patch('budget_time.models.BudgetTimeAllocation.get_total_budget_for_grade')
    def test_with_budget_details_manager(self, mock_get_total_budget, mock_get_used_hours):
        # Mock the `get_total_budget_for_grade` to return a predictable sum
        mock_get_total_budget.side_effect = lambda grade_id, bg_id, comp_id, fin_year_id: 150.0 if grade_id == 101 and bg_id == 10 else 0.0

        bg_id = 10
        grades_data = Grade.objects.with_budget_details(bg_id, MOCKED_COMP_ID, MOCKED_FIN_YEAR_ID)
        self.assertEqual(len(grades_data), 2) # Should include Grade X (101) and Grade Y (102)

        grade_x_data = next((item for item in grades_data if item['grade'].id == 101), None)
        self.assertIsNotNone(grade_x_data)
        self.assertEqual(grade_x_data['budget_hour'], 150.0)
        self.assertEqual(grade_x_data['used_hour'], 10.0)
        self.assertEqual(grade_x_data['balance_hour'], 140.0)
        self.assertEqual(grade_x_data['budget_code_symbol'], 'GXBGA')
        self.assertTrue(grade_x_data['has_budget_details'])

        grade_y_data = next((item for item in grades_data if item['grade'].id == 102), None)
        self.assertIsNotNone(grade_y_data)
        self.assertEqual(grade_y_data['budget_hour'], 0.0)
        self.assertEqual(grade_y_data['used_hour'], 10.0) # Mocked to return 10.0 for all grades
        self.assertEqual(grade_y_data['balance_hour'], -10.0)
        self.assertEqual(grade_y_data['budget_code_symbol'], 'GYBGA')
        self.assertFalse(grade_y_data['has_budget_details'])

class BudgetTimeAllocationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.bg_a = BusinessGroup.objects.create(id=10, name='Business Group A', symbol='BGA')
        cls.grade_x = Grade.objects.create(id=101, description='Grade X', symbol='GX')
        
        # Allocation for current year
        BudgetTimeAllocation.objects.create(
            bg_group=cls.bg_a, budget_code=cls.grade_x, hour=100.0,
            comp_id=MOCKED_COMP_ID, fin_year_id=MOCKED_FIN_YEAR_ID,
            sys_date=date(MOCKED_FIN_YEAR_ID, 1, 1), sys_time='10:00', session_id=MOCKED_USERNAME
        )
        # Allocation for previous year
        BudgetTimeAllocation.objects.create(
            bg_group=cls.bg_a, budget_code=cls.grade_x, hour=50.0,
            comp_id=MOCKED_COMP_ID, fin_year_id=MOCKED_FIN_YEAR_ID - 1,
            sys_date=date(MOCKED_FIN_YEAR_ID - 1, 1, 1), sys_time='09:00', session_id=MOCKED_USERNAME
        )

    def test_budget_allocation_creation(self):
        allocation = BudgetTimeAllocation.objects.get(
            bg_group=self.bg_a, budget_code=self.grade_x, fin_year_id=MOCKED_FIN_YEAR_ID
        )
        self.assertEqual(allocation.hour, 100.0)
        self.assertEqual(allocation.bg_group.name, 'Business Group A')
        self.assertEqual(allocation.budget_code.description, 'Grade X')

    def test_str_representation(self):
        allocation = BudgetTimeAllocation.objects.get(
            bg_group=self.bg_a, budget_code=self.grade_x, fin_year_id=MOCKED_FIN_YEAR_ID
        )
        self.assertEqual(str(allocation), 'Budget for Grade X in Business Group A - 100.0 hrs')

    def test_get_total_budget_for_grade(self):
        # This tests the internal Django query part of the method
        total_budget = BudgetTimeAllocation.get_total_budget_for_grade(
            self.grade_x.id, self.bg_a.id, MOCKED_COMP_ID, MOCKED_FIN_YEAR_ID
        )
        # Expecting sum of hours from current and previous fin_year_id for this grade and business group
        # (100.0 + 50.0) = 150.0
        self.assertEqual(total_budget, 150.0)

        # Test with no matching data
        total_budget_no_match = BudgetTimeAllocation.get_total_budget_for_grade(
            999, 999, MOCKED_COMP_ID, MOCKED_FIN_YEAR_ID
        )
        self.assertEqual(total_budget_no_match, 0.0)

    @patch('budget_time.models.BudgetTimeAllocation.get_used_hours_for_grade', return_value=0.0)
    def test_get_used_hours_for_grade(self, mock_get_used_hours):
        used_hours = BudgetTimeAllocation.get_used_hours_for_grade(
            self.grade_x.id, self.bg_a.id, MOCKED_COMP_ID, MOCKED_FIN_YEAR_ID
        )
        self.assertEqual(used_hours, 0.0) # Should return the mocked value
        mock_get_used_hours.assert_called_once_with(
            self.grade_x.id, self.bg_a.id, MOCKED_COMP_ID, MOCKED_FIN_YEAR_ID
        )

class BudgetTimeViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        cls.bg_a = BusinessGroup.objects.create(id=10, name='Business Group A', symbol='BGA')
        cls.bg_b = BusinessGroup.objects.create(id=20, name='Business Group B', symbol='BGB')
        cls.grade_x = Grade.objects.create(id=101, description='Grade X', symbol='GX')
        cls.grade_y = Grade.objects.create(id=102, description='Grade Y', symbol='GY')
        
        # Ensure session data is available for views
        cls.client.session['compid'] = MOCKED_COMP_ID
        cls.client.session['finyear'] = MOCKED_FIN_YEAR_ID

    def test_main_view_get(self):
        response = self.client.get(reverse('budget_time:budget_time_main'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_time/budget_time_main.html')
        self.assertIn('business_groups', response.context)
        self.assertEqual(len(response.context['business_groups']), 2) # BGA, BGB (ID 1 is filtered)

    def test_main_view_with_selected_bg(self):
        response = self.client.get(reverse('budget_time:budget_time_main'), {'bg_id': self.bg_a.id})
        self.assertEqual(response.status_code, 200)
        self.assertIn('selected_business_group', response.context)
        self.assertEqual(response.context['selected_business_group'].id, self.bg_a.id)
        self.assertIn('grades_with_budgets', response.context)
        self.assertGreater(len(response.context['grades_with_budgets']), 0)

    def test_business_group_table_partial(self):
        response = self.client.get(reverse('budget_time:business_group_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_time/_business_group_table.html')
        self.assertContains(response, 'Business Group A')
        self.assertContains(response, 'Business Group B')
        self.assertNotContains(response, 'Default Group') # Filtered out

    @patch('budget_time.models.GradeManager.with_budget_details', return_value=[
        {'grade': Grade.objects.get(id=101), 'budget_code_symbol': 'GXBGA', 'budget_hour': 150.0, 'used_hour': 10.0, 'balance_hour': 140.0, 'has_budget_details': True}
    ])
    def test_grade_budget_table_partial(self, mock_with_budget_details):
        response = self.client.get(reverse('budget_time:grade_budget_table_partial'), {'bg_id': self.bg_a.id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_time/_grade_budget_table.html')
        self.assertContains(response, 'Grade X')
        self.assertContains(response, '150.0')
        mock_with_budget_details.assert_called_once_with(self.bg_a.id, MOCKED_COMP_ID, MOCKED_FIN_YEAR_ID)

    def test_grade_budget_table_partial_no_bg_id(self):
        response = self.client.get(reverse('budget_time:grade_budget_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Please select a Business Group to view budget details.')
        self.assertNotContains(response, 'gradeBudgetTable') # Should not render table

    def test_budget_time_allocation_create_post(self):
        self.client.force_login(self.client.get_user()) # Simulate logged-in user for session_id
        data = {
            'selected_bg_id': self.bg_a.id,
            f'hour_{self.grade_x.id}': 75.5,
            f'chk_{self.grade_x.id}': 'on', # Simulate checkbox checked
            f'hour_{self.grade_y.id}': 25.0,
            f'chk_{self.grade_y.id}': 'on',
        }
        # Assuming HTTP_HX_REQUEST is present for HTMX trigger
        response = self.client.post(reverse('budget_time:budget_time_insert'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGradeBudgetList')

        # Verify objects were created
        self.assertEqual(BudgetTimeAllocation.objects.filter(bg_group=self.bg_a, budget_code=self.grade_x, hour=75.5).count(), 1)
        self.assertEqual(BudgetTimeAllocation.objects.filter(bg_group=self.bg_a, budget_code=self.grade_y, hour=25.0).count(), 1)
        self.assertEqual(BudgetTimeAllocation.objects.count(), 2) # Should be 2 new ones

    def test_budget_time_export_view(self):
        # Create some budget allocations for export
        BudgetTimeAllocation.objects.create(
            bg_group=self.bg_a, budget_code=self.grade_x, hour=10.0,
            comp_id=MOCKED_COMP_ID, fin_year_id=MOCKED_FIN_YEAR_ID,
            sys_date=date.today(), sys_time='10:00', session_id=MOCKED_USERNAME
        )
        BudgetTimeAllocation.objects.create(
            bg_group=self.bg_a, budget_code=self.grade_y, hour=20.0,
            comp_id=MOCKED_COMP_ID, fin_year_id=MOCKED_FIN_YEAR_ID,
            sys_date=date.today(), sys_time='10:00', session_id=MOCKED_USERNAME
        )
        
        response = self.client.get(reverse('budget_time:budget_time_export'), {'bg_id': self.bg_a.id})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment; filename="Budget_BG.csv"', response['Content-Disposition'])
        
        content = response.content.decode('utf-8')
        lines = content.strip().split('\r\n')
        self.assertEqual(len(lines), 3) # Header + 2 data rows
        self.assertIn('Budget Hour,Used Hour,Bal Hour', lines[0]) # Check header
        self.assertIn('10.0,0.0,10.0', lines[1]) # Check data for grade_x (used hour is mocked 0)
        self.assertIn('20.0,0.0,20.0', lines[2]) # Check data for grade_y

    def test_budget_time_export_view_no_bg(self):
        response = self.client.get(reverse('budget_time:budget_time_export'))
        self.assertEqual(response.status_code, 302) # Redirect to main view
        self.assertRedirects(response, reverse('budget_time:budget_time_main'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions Implemented:**
-   **HTMX for dynamic updates:**
    -   `BusinessGroupTablePartialView` and `GradeBudgetTablePartialView` are loaded via `hx-get` on page load (`load` trigger) and when explicitly requested (`refresh...List` custom events).
    -   Clicking "Select" on a Business Group dispatches an Alpine.js event (`selectedBgIdChanged`) which in turn triggers an `hx-get` on `grade-budget-table-container` with the updated `bg_id`.
    -   The "Insert" button uses `hx-post` to send the form data, including inputs from multiple rows, to `BudgetTimeAllocationCreateView`. Upon success, it returns `204 No Content` and an `HX-Trigger` to refresh the grade table.
-   **Alpine.js for UI state management:**
    -   `x-data` on the main container tracks `selectedBgId`.
    -   `x-model="isChecked"` on each row's checkbox controls the visibility of `<span>` (label) and `<input type="number">` (textbox) for `Budget Hour`.
    -   `x-bind:disabled` on buttons reacts to `selectedBgId` to enable/disable them.
    -   `onclick="window.selectBusinessGroup('{{ bg.id }}')"` on Business Group select buttons calls an Alpine.js function to update the `selectedBgId` state and dispatch a custom event.
-   **DataTables for list views:**
    -   The `_business_group_table.html` and `_grade_budget_table.html` partials contain `<table>` elements with unique IDs.
    -   JavaScript in `extra_js` block listens for HTMX `afterSwap` events on the respective containers. Once the tables are loaded via HTMX, DataTables are initialized on these elements.
-   **No full page reloads:** All interactive elements (`select` buttons, `insert` button) use HTMX, ensuring dynamic updates without full page refreshes.
-   **`HX-Trigger` responses:** `BudgetTimeAllocationCreateView` explicitly sets `response['HX-Trigger'] = 'refreshGradeBudgetList'` to inform HTMX to reload the `grade-budget-table-container` after a successful insert.

### Final Notes

This comprehensive plan transforms the ASP.NET "Budget Distribution Time" module into a robust Django application. By adhering to the principles of fat models, thin views, and a modern frontend stack (HTMX + Alpine.js + DataTables), we achieve a highly maintainable, scalable, and user-friendly solution. The business logic, especially the complex budget calculations, is refactored into model methods, keeping views concise and focused on presentation. The use of HTMX ensures a smooth, dynamic user experience akin to SPAs, but with the simplicity of server-side rendering. Comprehensive tests guarantee the reliability of the migrated functionality. The automation-first mindset guides the structured generation of code, which can be further refined and implemented with AI-assisted tools.