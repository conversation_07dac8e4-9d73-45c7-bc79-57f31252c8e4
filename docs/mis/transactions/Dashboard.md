This modernization plan details the transition of your legacy ASP.NET Dashboard module to a robust, modern Django application. Our approach prioritizes automation, leveraging Django's "Fat Model, Thin View" philosophy, HTMX for dynamic interactions, Alpine.js for lightweight frontend logic, and DataTables for superior data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the `SqlDataSource` components and `GridView` column definitions, we infer the following database schema:

**Identified Table:** `AccHead`
*   **Purpose:** Stores general accounting head categories and descriptions.
*   **Columns:**
    *   `Id` (Primary Key, Integer)
    *   `Category` (Text, e.g., 'With Material', 'Labour')
    *   `Description` (Text)
    *   `Symbol` (Text)
    *   `Abbrivation` (Text)

**Inferred Table:** `BudgetEntry`
*   **Purpose:** Stores the actual budget figures associated with `AccHead` entries. The ASP.NET GridView shows `TotalBudget`, `PO`, `Cash`, `Tax`, `Bal Budget` which are not in the `AccHead` `SELECT` statement, implying they are from a related table. The "Insert" button in the footer suggests creating or updating these budget figures.
*   **Columns:**
    *   `Id` (Primary Key, Integer)
    *   `AccHeadId` (Foreign Key to `AccHead.Id`, Integer)
    *   `TotalBudget` (Decimal/Numeric, up to 15 digits, 3 decimal places)
    *   `PO` (Decimal/Numeric)
    *   `Cash` (Decimal/Numeric)
    *   `Tax` (Decimal/Numeric)
    *   `BalBudget` (Decimal/Numeric)
    *   `IsActive` (Boolean, inferred to track the current budget for an `AccHead` if multiple historical entries are allowed)
    *   `CreatedAt` (DateTime, inferred for tracking when a budget was set)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily exhibits **Read** and **Create/Update** operations related to budget entries.

*   **Read:**
    *   Displays lists of `AccHead` entries, filtered by `Category` ('With Material' or 'Labour').
    *   For each `AccHead`, it attempts to display associated budget figures (`TotalBudget`, `PO`, `Cash`, `Tax`, `Bal Budget`).
*   **Create/Update (BudgetEntry):**
    *   The `TextBox` `TxtAmount` within the `TotalBudget` column, along with the `CheckBox` `AutoPostBack="true"` and an `Insert` button in the footer, suggests an inline editing or "add budget" functionality for `TotalBudget` specific to an `AccHead`.
    *   The `RegularExpressionValidator` implies validation for `TotalBudget` to be a numeric value.
*   **Delete:** No explicit delete operation is found in the ASP.NET code, but it's a standard CRUD operation that will be included for completeness.

### Step 3: Infer UI Components

*   **TabContainer:** Will be converted to a Django template structure leveraging HTMX to load different content (the DataTables) based on the active tab selection, avoiding full page reloads.
*   **GridViews (`GridView3`, `GridView2`):** Will be replaced by standard HTML `<table>` elements. These tables will be enhanced with DataTables.js for client-side search, sort, and pagination. Each row will feature HTMX-powered buttons/inputs for inline editing or opening modals for CRUD operations.
*   **`SqlDataSource`:** The data retrieval logic will be handled directly by Django's ORM within the models and views, querying `AccHead` and `BudgetEntry` tables.
*   **`HyperLink` (`Text="Select"`), `CheckBox`:** These imply row-level actions. The `CheckBox` with `AutoPostBack` suggests toggling an edit mode or selecting items. We will use HTMX and Alpine.js to achieve this functionality, for instance, by toggling the visibility of an input field for `TotalBudget` when the checkbox is checked, and submitting changes with HTMX.
*   **`TextBox` (`TxtAmount`):** This is for entering `TotalBudget`. Its `Visible="false"` property suggests it's conditionally displayed. In Django, we'll control its visibility with Alpine.js, triggered by a `CheckBox` or "Edit" button, and submit its value via HTMX.
*   **`Button` (`BtnInsert`, `btnCancel`):** `BtnInsert` will trigger an HTMX form submission to create/update a `BudgetEntry`. `btnCancel` will close any open modals or reset the form.

---

## Step 4: Generate Django Code

We will create a Django application named `budget_tracker` to house this functionality.

### 4.1 Models (`budget_tracker/models.py`)

We'll define two models: `AccHead` (for static categories) and `BudgetEntry` (for dynamic budget data).

```python
from django.db import models
from django.utils import timezone

class AccHead(models.Model):
    """
    Maps to the existing AccHead database table.
    Contains static information about accounting heads.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=255)
    description = models.CharField(db_column='Description', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    abbreviation = models.CharField(db_column='Abbrivation', max_length=50)

    class Meta:
        managed = False  # Tells Django not to manage this table's creation/deletion
        db_table = 'AccHead'
        verbose_name = 'Accounting Head'
        verbose_name_plural = 'Accounting Heads'

    def __str__(self):
        return f"{self.description} ({self.category})"

    def get_current_budget(self):
        """
        Retrieves the most recent active budget entry for this AccHead.
        If no active budget, returns None.
        """
        # Assuming 'IsActive' and 'CreatedAt' fields exist to determine the current budget
        # Or, if only one budget per AccHead is allowed, it's simpler.
        # For simplicity, we'll assume the latest entry is the active one if no IsActive.
        # If 'IsActive' field existed and was managed, we'd filter on that.
        return self.budget_entries.order_by('-created_at').first()

class BudgetEntry(models.Model):
    """
    Maps to an inferred BudgetEntry table.
    Stores dynamic budget figures linked to AccHead.
    """
    # Using Django's default auto-incrementing ID for new table
    acc_head = models.ForeignKey(
        AccHead,
        on_delete=models.CASCADE,
        related_name='budget_entries',
        db_column='AccHeadId' # If the column name in DB is AccHeadId
    )
    total_budget = models.DecimalField(
        db_column='TotalBudget',
        max_digits=18,  # Covers 15 digits + 3 decimal places
        decimal_places=3
    )
    po = models.DecimalField(
        db_column='PO',
        max_digits=18,
        decimal_places=3,
        default=0.0
    )
    cash = models.DecimalField(
        db_column='Cash',
        max_digits=18,
        decimal_places=3,
        default=0.0
    )
    tax = models.DecimalField(
        db_column='Tax',
        max_digits=18,
        decimal_places=3,
        default=0.0
    )
    bal_budget = models.DecimalField(
        db_column='BalBudget',
        max_digits=18,
        decimal_places=3,
        default=0.0
    )
    is_active = models.BooleanField(default=True) # Inferred to mark current budget
    created_at = models.DateTimeField(default=timezone.now) # Inferred for tracking

    class Meta:
        # managed = False # This table will be managed by Django if it's new
        db_table = 'BudgetEntry' # Name the table if it exists, otherwise Django will create with appname_modelname
        verbose_name = 'Budget Entry'
        verbose_name_plural = 'Budget Entries'
        # Add constraints if only one active budget per acc_head is desired:
        # unique_together = ('acc_head', 'is_active') # If true means only one active
        # ordering = ['-created_at'] # Default ordering

    def __str__(self):
        return f"Budget for {self.acc_head.description}: {self.total_budget}"

    def update_balance(self):
        """
        Business logic to update BalBudget based on PO, Cash, Tax.
        This would be triggered on save or a specific action.
        """
        self.bal_budget = self.total_budget - self.po - self.cash - self.tax
        # self.save() # Don't save here, let the view handle it after calling this method

    def save(self, *args, **kwargs):
        """
        Override save to automatically calculate BalBudget and manage IsActive state.
        If a new entry is created for an acc_head, set previous active ones to inactive.
        """
        # If this is a new entry or changing acc_head, ensure only one active budget per acc_head
        if self._state.adding or self.pk:
            BudgetEntry.objects.filter(acc_head=self.acc_head, is_active=True).exclude(pk=self.pk).update(is_active=False)
        self.update_balance() # Calculate balance before saving
        super().save(*args, **kwargs)

```

### 4.2 Forms (`budget_tracker/forms.py`)

A single form to handle the `BudgetEntry` creation and updates.

```python
from django import forms
from .models import BudgetEntry
import re

class BudgetEntryForm(forms.ModelForm):
    """
    Form for creating and updating BudgetEntry objects.
    Includes validation for TotalBudget.
    """
    # acc_head is typically set by the view or hidden, not directly editable
    acc_head_id = forms.IntegerField(widget=forms.HiddenInput(), required=True)

    class Meta:
        model = BudgetEntry
        fields = ['acc_head', 'total_budget', 'po', 'cash', 'tax', 'bal_budget', 'acc_head_id']
        # bal_budget is auto-calculated, so it can be excluded from direct input
        # or made read-only if it's in the form. Let's exclude for now.
        fields = ['total_budget', 'po', 'cash', 'tax', 'acc_head_id']
        widgets = {
            'total_budget': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Total Budget'
            }),
            'po': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'PO'
            }),
            'cash': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Cash'
            }),
            'tax': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Tax'
            }),
            # bal_budget is calculated, so it won't be a direct input field.
            # If displayed, it would be read-only.
        }

    def clean_total_budget(self):
        total_budget = self.cleaned_data['total_budget']
        # Replicate ASP.NET Regex validation: ^\d{1,15}(\.\d{0,3})?$
        # This regex ensures 1 to 15 digits before decimal, and 0 to 3 after.
        if not re.match(r'^\d{1,15}(\.\d{0,3})?$', str(total_budget)):
            raise forms.ValidationError(
                "Total Budget must be a numeric value with up to 15 digits before and 3 digits after the decimal point."
            )
        return total_budget

    def save(self, commit=True):
        instance = super().save(commit=False)
        acc_head_id = self.cleaned_data.get('acc_head_id')
        if acc_head_id:
            instance.acc_head = BudgetEntry.acc_head.field.related_model.objects.get(pk=acc_head_id)
        
        # Ensure only one active budget per AccHead
        BudgetEntry.objects.filter(acc_head=instance.acc_head, is_active=True).exclude(pk=instance.pk).update(is_active=False)
        instance.is_active = True
        
        # Balance will be updated in model's save method
        if commit:
            instance.save()
        return instance

```

### 4.3 Views (`budget_tracker/views.py`)

We'll use Class-Based Views for listing and HTMX-driven partial updates.

```python
from django.views.generic import ListView, View
from django.views.generic.edit import CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from .models import AccHead, BudgetEntry
from .forms import BudgetEntryForm
from django.db.models import Sum, F, ExpressionWrapper, DecimalField

class DashboardView(ListView):
    """
    Main dashboard view to display tabs and initial content.
    This view will handle the overall structure.
    """
    model = AccHead
    template_name = 'budget_tracker/dashboard.html'
    context_object_name = 'accheads'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Default tab to load on page load
        context['default_category'] = 'With Material'
        return context

class BudgetTablePartialView(ListView):
    """
    Partial view to render the budget table for a specific category via HTMX.
    """
    model = AccHead
    template_name = 'budget_tracker/_budget_table.html'
    context_object_name = 'accheads'

    def get_queryset(self):
        category = self.request.GET.get('category', 'With Material') # Default or from tab click
        queryset = AccHead.objects.filter(category=category).order_by('description')

        # Annotate queryset with the latest active budget entry details
        # For simplicity, let's assume we can directly join or fetch related.
        # This will fetch the latest BudgetEntry for each AccHead.
        # This is a common pattern for "thin views" and "fat models" if done via model methods,
        # or via annotations/select_related for query optimization.

        # A more efficient way would be to join and then annotate sums for the related data if it's complex
        # For now, we'll fetch AccHead and then access budget_entries via related_name,
        # relying on AccHead.get_current_budget() method.
        # Alternatively, a direct join/subquery can be more efficient for large datasets.
        
        # To get the latest budget entry efficiently:
        # This assumes BudgetEntry has an 'id' which is auto-incrementing and thus latest has highest ID,
        # or 'created_at' field. We added 'created_at'.
        subquery = BudgetEntry.objects.filter(
            acc_head=F('acc_head_id'),
            is_active=True # Filter for active budget
        ).order_by('-created_at').values('total_budget', 'po', 'cash', 'tax', 'bal_budget')[:1]

        # Use select_related or prefetch_related for related objects if needed for performance.
        # For a fat model approach, we can define a property on AccHead or use custom manager.
        
        # Simplified for now, relying on model's get_current_budget method:
        # The template will call obj.get_current_budget()
        return queryset

class BudgetEntryCreateUpdateView(View):
    """
    Handles creation and update of BudgetEntry via HTMX form submission.
    This replaces the ASP.NET GridView's inline editing and footer 'Insert'.
    """
    form_class = BudgetEntryForm
    template_name = 'budget_tracker/_budget_form.html' # Partial template for modal

    def get(self, request, *args, **kwargs):
        acc_head_id = kwargs.get('acc_head_id')
        budget_entry = None
        form = None

        if acc_head_id:
            acc_head = get_object_or_404(AccHead, pk=acc_head_id)
            # Try to get the active budget for this acc_head
            budget_entry = acc_head.get_current_budget()
            if budget_entry:
                form = self.form_class(instance=budget_entry, initial={'acc_head_id': acc_head_id})
            else:
                form = self.form_class(initial={'acc_head_id': acc_head_id})
        else:
            # Should not happen in this flow if triggered from an AccHead row
            form = self.form_class()

        return render(request, self.template_name, {'form': form, 'acc_head_id': acc_head_id})

    def post(self, request, *args, **kwargs):
        acc_head_id = kwargs.get('acc_head_id')
        acc_head = get_object_or_404(AccHead, pk=acc_head_id)
        budget_entry = acc_head.get_current_budget() # Check if an active budget exists

        form = self.form_class(request.POST, instance=budget_entry)
        if form.is_valid():
            # Set acc_head if it's a new entry (form.instance.pk is None)
            if not form.instance.pk:
                form.instance.acc_head = acc_head
            
            form.save() # Model's save method handles is_active and balance calculation
            messages.success(request, 'Budget entry saved successfully.')
            # HTMX response for success, trigger list refresh and close modal
            return HttpResponse(
                status=204, # No content
                headers={'HX-Trigger': 'refreshBudgetList'}
            )
        else:
            # If form is not valid, re-render the form with errors
            return render(request, self.template_name, {'form': form, 'acc_head_id': acc_head_id})


class BudgetEntryDeleteView(DeleteView):
    """
    Handles deletion of BudgetEntry via HTMX confirmation.
    """
    model = BudgetEntry
    template_name = 'budget_tracker/confirm_delete.html' # Partial template for modal

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        self.object.delete()
        messages.success(request, 'Budget entry deleted successfully.')
        return HttpResponse(
            status=204,
            headers={'HX-Trigger': 'refreshBudgetList'}
        )

```

### 4.4 Templates

**1. `budget_tracker/dashboard.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-4 rounded-t-lg shadow-md flex items-center">
        <h1 class="text-2xl font-bold">Create Budget</h1>
    </div>

    <div class="bg-white shadow-md rounded-b-lg p-6">
        <!-- Tab navigation using HTMX -->
        <div class="mb-6 border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button
                    class="tab-button group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm"
                    {% if default_category == 'With Material' %}
                        hx-get="{% url 'budget_tracker:budget_table_partial' %}?category=With Material"
                        hx-target="#budget-table-container"
                        hx-trigger="load, click"
                        class="tab-button group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm border-indigo-500 text-indigo-600"
                        aria-current="page"
                    {% else %}
                        hx-get="{% url 'budget_tracker:budget_table_partial' %}?category=With Material"
                        hx-target="#budget-table-container"
                        hx-trigger="click"
                        class="tab-button group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    {% endif %}
                >
                    <span>With Material</span>
                </button>
                <button
                    class="tab-button group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm"
                    {% if default_category == 'Labour' %}
                        hx-get="{% url 'budget_tracker:budget_table_partial' %}?category=Labour"
                        hx-target="#budget-table-container"
                        hx-trigger="load, click"
                        class="tab-button group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm border-indigo-500 text-indigo-600"
                        aria-current="page"
                    {% else %}
                        hx-get="{% url 'budget_tracker:budget_table_partial' %}?category=Labour"
                        hx-target="#budget-table-container"
                        hx-trigger="click"
                        class="tab-button group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                    {% endif %}
                >
                    <span>Labour</span>
                </button>
            </nav>
        </div>

        <!-- Budget Table Container - Content loaded via HTMX -->
        <div id="budget-table-container"
             hx-trigger="refreshBudgetList from:body"
             hx-swap="innerHTML">
            <!-- Initial content will be loaded here by the first tab's hx-trigger="load" -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading budget data...</p>
            </div>
        </div>

        <div class="mt-6 flex justify-center">
            <button
                class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                onclick="window.location.reload();">
                Cancel
            </button>
        </div>
    </div>
</div>

<!-- Modal for form (Add/Edit) and Delete Confirmation -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     x-data="{ showModal: false }"
     x-show="showModal"
     x-on:close-modal.window="showModal = false"
     x-on:open-modal.window="showModal = true"
     x-on:htmx:after-request.window="if(event.detail.successful) showModal = false;">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
         @click.outside="showModal = false">
        <!-- Content will be loaded here by HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading form...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init is handled globally if Alpine.js is included in base.html
    // This script can contain specific logic for this page if needed.
    document.addEventListener('DOMContentLoaded', function() {
        // Simple client-side logic to activate tabs visually
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('border-indigo-500', 'text-indigo-600');
                    btn.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                    btn.removeAttribute('aria-current');
                });
                this.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                this.classList.add('border-indigo-500', 'text-indigo-600');
                this.setAttribute('aria-current', 'page');

                // Dispatch custom event to close modal if open
                window.dispatchEvent(new CustomEvent('close-modal'));
            });
        });

        // Trigger the initial load for the default tab
        const defaultTab = document.querySelector('.tab-button[aria-current="page"]');
        if (defaultTab) {
            defaultTab.click(); // Simulate click to load content
        }
    });

    // Listen for custom event to trigger modal opening
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'modalContent') {
            window.dispatchEvent(new CustomEvent('open-modal'));
        }
    });

    // Listen for htmx:afterRequest on forms to close modal if successful
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.target.closest('form')) { // Check if the event originated from a form
            if (evt.detail.successful) {
                window.dispatchEvent(new CustomEvent('close-modal'));
            }
        }
    });
</script>
{% endblock %}
```

**2. `budget_tracker/_budget_table.html` (Partial for DataTable)**

```html
<table id="budgetTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total Budget</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Cash</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tax</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Bal Budget</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for acc_head_obj in accheads %}
        {% with current_budget=acc_head_obj.get_current_budget %}
        <tr x-data="{ editMode: false }">
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ acc_head_obj.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ acc_head_obj.symbol }}</td>
            
            <td class="py-2 px-4 border-b border-gray-200 text-right">
                {% if current_budget %}
                    {{ current_budget.total_budget|floatformat:3 }}
                {% else %}
                    <span class="text-gray-500 italic">No Budget</span>
                {% endif %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ current_budget.po|floatformat:3|default:"0.000" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ current_budget.cash|floatformat:3|default:"0.000" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ current_budget.tax|floatformat:3|default:"0.000" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ current_budget.bal_budget|floatformat:3|default:"0.000" }}</td>
            
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs mr-2"
                    hx-get="{% url 'budget_tracker:budget_form' acc_head_id=acc_head_obj.pk %}"
                    hx-target="#modalContent"
                    hx-swap="innerHTML"
                    _="on click window.dispatchEvent(new CustomEvent('open-modal'))">
                    Budget
                </button>
                {% if current_budget %}
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'budget_tracker:budget_delete' pk=current_budget.pk %}"
                    hx-target="#modalContent"
                    hx-swap="innerHTML"
                    _="on click window.dispatchEvent(new CustomEvent('open-modal'))">
                    Delete
                </button>
                {% endif %}
            </td>
        </tr>
        {% endwith %}
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance before reinitializing if HTMX replaces content
        if ($.fn.DataTable.isDataTable('#budgetTable')) {
            $('#budgetTable').DataTable().destroy();
        }
        $('#budgetTable').DataTable({
            "pageLength": 20, // Replicates ASP.NET PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 8] }, // Disable sorting for SN and Actions
                { "searchable": false, "targets": [0, 8] } // Disable searching for SN and Actions
            ]
        });
    });
</script>
```

**3. `budget_tracker/_budget_form.html` (Partial for Add/Edit Form)**

```html
<div class="p-6" x-data="{}">
    <h3 class="text-lg font-medium text-gray-900 mb-5">
        {% if form.instance.pk %}Edit Budget Entry{% else %}Create Budget Entry{% endif %}
    </h3>
    <form hx-post="{% url 'budget_tracker:budget_form' acc_head_id=acc_head_id %}{% if form.instance.pk %}{% else %}{% endif %}"
          hx-swap="none"
          hx-target="#modalContent"
          hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <input type="hidden" name="acc_head_id" value="{{ acc_head_id }}">
        
        <div class="space-y-4">
            {% for field in form %}
            {% if field.name != 'acc_head' and field.name != 'acc_head_id' %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endif %}
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click window.dispatchEvent(new CustomEvent('close-modal'))">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <div id="form-loading-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**4. `budget_tracker/confirm_delete.html` (Partial for Delete Confirmation)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the budget entry for "{{ object.acc_head.description }}" (ID: {{ object.pk }})?</p>
    <form hx-delete="{% url 'budget_tracker:budget_delete' pk=object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click window.dispatchEvent(new CustomEvent('close-modal'))">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`budget_tracker/urls.py`)

```python
from django.urls import path
from .views import DashboardView, BudgetTablePartialView, BudgetEntryCreateUpdateView, BudgetEntryDeleteView

app_name = 'budget_tracker'

urlpatterns = [
    path('', DashboardView.as_view(), name='dashboard'),
    path('table/', BudgetTablePartialView.as_view(), name='budget_table_partial'),
    path('budget/form/<int:acc_head_id>/', BudgetEntryCreateUpdateView.as_view(), name='budget_form'),
    path('budget/delete/<int:pk>/', BudgetEntryDeleteView.as_view(), name='budget_delete'),
]
```

### 4.6 Tests (`budget_tracker/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from .models import AccHead, BudgetEntry

class AccHeadModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for AccHead (assuming it's pre-populated, but we create for testing)
        cls.acc_head_material = AccHead.objects.create(
            id=101, category='With Material', description='Raw Material A', symbol='RMA', abbreviation='RMA'
        )
        cls.acc_head_labour = AccHead.objects.create(
            id=102, category='Labour', description='Skilled Labour', symbol='SKL', abbreviation='SKL'
        )
        # Create an initial budget entry for testing update/delete
        cls.budget_entry = BudgetEntry.objects.create(
            acc_head=cls.acc_head_material,
            total_budget=Decimal('1000.000'),
            po=Decimal('200.000'),
            cash=Decimal('100.000'),
            tax=Decimal('50.000'),
            is_active=True # Mark as active
        )
        # Ensure calculated balance is correct on save
        cls.budget_entry.refresh_from_db()

    def test_acchead_creation(self):
        self.assertEqual(self.acc_head_material.description, 'Raw Material A')
        self.assertEqual(self.acc_head_material.category, 'With Material')
        self.assertEqual(str(self.acc_head_material), 'Raw Material A (With Material)')

    def test_budget_entry_creation(self):
        self.assertEqual(self.budget_entry.acc_head, self.acc_head_material)
        self.assertEqual(self.budget_entry.total_budget, Decimal('1000.000'))
        self.assertEqual(self.budget_entry.bal_budget, Decimal('650.000')) # 1000 - 200 - 100 - 50

    def test_budget_entry_update_balance(self):
        new_budget = BudgetEntry.objects.create(
            acc_head=self.acc_head_material,
            total_budget=Decimal('2000.000'),
            po=Decimal('300.000'),
            cash=Decimal('150.000'),
            tax=Decimal('75.000')
        )
        new_budget.refresh_from_db()
        self.assertEqual(new_budget.bal_budget, Decimal('1475.000')) # 2000 - 300 - 150 - 75
        # Verify old budget entry is now inactive
        self.budget_entry.refresh_from_db()
        self.assertFalse(self.budget_entry.is_active)
        self.assertTrue(new_budget.is_active)

    def test_get_current_budget(self):
        current = self.acc_head_material.get_current_budget()
        self.assertEqual(current.total_budget, Decimal('2000.000'))
        self.assertEqual(current.acc_head, self.acc_head_material)

        # Test acc_head with no budget
        no_budget_acc_head = AccHead.objects.create(id=103, category='Other', description='Other Exp', symbol='OEX', abbreviation='OEX')
        self.assertIsNone(no_budget_acc_head.get_current_budget())

class BudgetTrackerViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.client = Client()
        cls.acc_head_material = AccHead.objects.create(
            id=201, category='With Material', description='Wood', symbol='WOD', abbreviation='WOD'
        )
        cls.acc_head_labour = AccHead.objects.create(
            id=202, category='Labour', description='Electrician', symbol='ELE', abbreviation='ELE'
        )
        cls.budget_entry_material = BudgetEntry.objects.create(
            acc_head=cls.acc_head_material,
            total_budget=Decimal('500.000'),
            po=Decimal('100.000'),
            cash=Decimal('50.000'),
            tax=Decimal('25.000')
        )
        cls.budget_entry_labour = BudgetEntry.objects.create(
            acc_head=cls.acc_head_labour,
            total_budget=Decimal('300.000'),
            po=Decimal('0.000'),
            cash=Decimal('0.000'),
            tax=Decimal('15.000')
        )

    def test_dashboard_view(self):
        response = self.client.get(reverse('budget_tracker:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_tracker/dashboard.html')
        self.assertIn('accheads', response.context)
        self.assertIn('default_category', response.context)
        self.assertEqual(response.context['default_category'], 'With Material')

    def test_budget_table_partial_view_material(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budget_tracker:budget_table_partial') + '?category=With Material', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_tracker/_budget_table.html')
        self.assertContains(response, 'Wood')
        self.assertContains(response, '500.000')
        self.assertNotContains(response, 'Electrician')

    def test_budget_table_partial_view_labour(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budget_tracker:budget_table_partial') + '?category=Labour', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_tracker/_budget_table.html')
        self.assertContains(response, 'Electrician')
        self.assertContains(response, '300.000')
        self.assertNotContains(response, 'Wood')

    def test_budget_entry_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Test for an AccHead without an existing budget
        acc_head_id_no_budget = AccHead.objects.create(id=203, category='With Material', description='Steel', symbol='STL', abbreviation='STL').pk
        response = self.client.get(reverse('budget_tracker:budget_form', args=[acc_head_id_no_budget]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_tracker/_budget_form.html')
        self.assertContains(response, 'Create Budget Entry')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['acc_head_id'], acc_head_id_no_budget)

    def test_budget_entry_create_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_acc_head = AccHead.objects.create(id=204, category='Labour', description='Plumber', symbol='PLB', abbreviation='PLB')
        data = {
            'acc_head_id': new_acc_head.pk,
            'total_budget': '750.500',
            'po': '50.000',
            'cash': '25.000',
            'tax': '10.000',
        }
        response = self.client.post(reverse('budget_tracker:budget_form', args=[new_acc_head.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetList')
        self.assertTrue(BudgetEntry.objects.filter(acc_head=new_acc_head, total_budget=Decimal('750.500')).exists())
        self.assertEqual(new_acc_head.get_current_budget().total_budget, Decimal('750.500'))

    def test_budget_entry_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budget_tracker:budget_form', args=[self.acc_head_material.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_tracker/_budget_form.html')
        self.assertContains(response, 'Edit Budget Entry')
        self.assertContains(response, 'value="500.000"') # Checks if form pre-populates

    def test_budget_entry_update_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Ensure previous budget is inactive
        self.budget_entry_material.refresh_from_db()
        self.assertTrue(self.budget_entry_material.is_active)

        data = {
            'acc_head_id': self.acc_head_material.pk,
            'total_budget': '1200.000',
            'po': '250.000',
            'cash': '100.000',
            'tax': '50.000',
        }
        response = self.client.post(reverse('budget_tracker:budget_form', args=[self.acc_head_material.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetList')

        # Verify a new entry was created and old one is inactive
        new_budget = BudgetEntry.objects.filter(acc_head=self.acc_head_material, total_budget=Decimal('1200.000')).first()
        self.assertIsNotNone(new_budget)
        self.assertTrue(new_budget.is_active)
        
        self.budget_entry_material.refresh_from_db()
        self.assertFalse(self.budget_entry_material.is_active) # Old entry should be inactive

    def test_budget_entry_form_validation_total_budget(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_acc_head = AccHead.objects.create(id=205, category='Labour', description='Painter', symbol='PNT', abbreviation='PNT')
        invalid_data = {
            'acc_head_id': new_acc_head.pk,
            'total_budget': 'abc', # Invalid input
            'po': '50',
            'cash': '25',
            'tax': '10',
        }
        response = self.client.post(reverse('budget_tracker:budget_form', args=[new_acc_head.pk]), invalid_data, **headers)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertTemplateUsed(response, 'budget_tracker/_budget_form.html')
        self.assertContains(response, 'Total Budget must be a numeric value')

    def test_budget_entry_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('budget_tracker:budget_delete', args=[self.budget_entry_labour.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_tracker/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Electrician')

    def test_budget_entry_delete_view_post(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        budget_to_delete_pk = self.budget_entry_labour.pk
        response = self.client.delete(reverse('budget_tracker:budget_delete', args=[budget_to_delete_pk]), **headers)
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetList')
        self.assertFalse(BudgetEntry.objects.filter(pk=budget_to_delete_pk).exists())
```

---

## Step 5: HTMX and Alpine.js Integration

*   **Tabs:** The `TabContainer` functionality is replaced by simple HTML buttons that trigger HTMX `hx-get` requests to load the appropriate `_budget_table.html` partial into the `#budget-table-container` div. The `hx-trigger="load"` on the default tab ensures content is loaded on initial page load. Alpine.js is used for visual tab activation.
*   **DataTables:** The `_budget_table.html` partial explicitly initializes DataTables on the `budgetTable` element using jQuery. This provides client-side searching, sorting, and pagination as in the original `GridView`.
*   **Modals (Add/Edit/Delete):** All CRUD operations (`Budget` button, `Delete` button) now open a single, reusable modal (`#modal`).
    *   They use `hx-get` to fetch the form (`_budget_form.html`) or confirmation (`confirm_delete.html`) partial into `#modalContent`.
    *   Alpine.js (with `x-data`, `x-show`, `x-on`) controls the modal's visibility. Custom events (`open-modal`, `close-modal`) are dispatched to manage the modal state, keeping the HTML cleaner and avoiding inline JavaScript.
*   **Form Submission:** Forms within the modal (in `_budget_form.html` and `confirm_delete.html`) use `hx-post` or `hx-delete` for submission.
    *   `hx-swap="none"` is crucial for form submissions. It means HTMX will not replace the target's content. Instead, the Django view returns a `204 No Content` status with an `HX-Trigger` header (`refreshBudgetList`).
    *   The `HX-Trigger` causes the main `#budget-table-container` to re-fetch its content, effectively refreshing the DataTables without a full page reload, and also triggers the modal to close.
    *   Error handling for forms (e.g., validation errors) is handled by HTMX automatically swapping the form itself with the error-laden form response.
*   **No Custom JavaScript:** Beyond the DataTables initialization and minimal Alpine.js for modal state and tab styling, no complex custom JavaScript is required, adhering to the HTMX-first principle.

---

## Final Notes

This comprehensive plan provides a clear, step-by-step guide for migrating the ASP.NET Dashboard module to Django. By following these guidelines, you will achieve:

*   **Enhanced Maintainability:** A clean, modular Django structure with clear separation of concerns (Fat Models, Thin Views).
*   **Modern User Experience:** Dynamic, responsive interactions powered by HTMX and Alpine.js, providing a single-page application feel without the complexity of traditional JavaScript frameworks.
*   **Improved Performance:** Efficient data loading and updates through HTMX, minimizing server load and page reloads.
*   **Robustness:** Comprehensive unit and integration tests ensure high code quality and reliability.
*   **Scalability:** Django's architecture provides a strong foundation for future growth and feature additions.

This plan focuses on automated conversion strategies, reducing manual coding effort and human error, making the transition smoother and more predictable for your organization.