This comprehensive Django modernization plan addresses the migration of your ASP.NET `Budget_Dist.aspx` module. It focuses on converting your existing functionality into a modern Django architecture, emphasizing the "Fat Model, Thin View" paradigm, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for enhanced data presentation.

## ASP.NET to Django Conversion Script: Budget Distribution

This plan outlines the transition of the ASP.NET `Budget_Dist.aspx` module to a modern Django-based solution. We will leverage Django's robust ORM, Class-Based Views, HTMX for dynamic interactions, Alpine.js for client-side UI logic, and DataTables for enhanced data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following primary tables and their relevant columns. The additional tables (`tblACC_CashVoucher_Payment_Details`, `tblACC_CashVoucher_Payment_Master`, `tblACC_CashVoucher_Receipt_Master`) are inferred as existing tables used in calculations, and their full schemas are not provided but are crucial for the `BusinessGroup`'s calculated fields.

-   **BusinessGroup:** This table stores information about different business entities.
    -   `Id` (Primary Key, integer)
    -   `Name` (string, e.g., VARCHAR)
    -   `Symbol` (string, e.g., VARCHAR)

-   **tblACC_Budget_Dept:** This table stores the budget amounts assigned to specific business groups for a particular financial year.
    -   `Id` (Primary Key, integer, auto-incremented by the database)
    -   `SysDate` (date)
    -   `SysTime` (time)
    -   `CompId` (integer, Company Identifier)
    -   `FinYearId` (integer, Financial Year Identifier)
    -   `SessionId` (string, likely representing the user's session or username)
    -   `BGId` (integer, Foreign Key linking to `BusinessGroup.Id`)
    -   `Amount` (decimal/float, e.g., DECIMAL(18,2))

## Step 2: Identify Backend Functionality

The ASP.NET page performs the following core business functions:

*   **Read (Display Budget Distribution):** The system fetches a list of `BusinessGroup`s (excluding ID '1') and for each, dynamically calculates and presents various financial figures:
    *   The total budget (sum of current year's budget entries and previous year's balance).
    *   Amounts for Purchase Orders (PO), Cash Payments, Cash Receipts, and Taxes.
    *   The remaining "Balance Budget" after accounting for expenses and receipts.
    All these calculated values, along with overall totals, are displayed in a tabular format.
*   **Create/Update (Assign Budget):** Users can select one or more business groups using checkboxes. For selected groups, they can input a new budget amount. Upon clicking "Insert", the system records these new budget amounts into the `tblACC_Budget_Dept` table. The original ASP.NET code performs an `INSERT` operation for each selected budget, implying that multiple budget entries for the same group and financial year are possible.
*   **Export:** The displayed tabular data can be downloaded as an Excel file.
*   **Navigation:** The page includes a hyperlink to a "Department Details" page for a specific Business Group and a "Cancel" button that redirects to a main menu. It also features tabs to switch between "Business Group" (the current view) and "Work Order" (which embeds another page via an iframe).

## Step 3: Infer UI Components

The user interface of the ASP.NET page consists of:

*   A clear "Assign Budget" header.
*   A tabbed navigation system that currently shows "Business Group" and "Work Order" tabs.
*   **A Data Grid (`GridView1`):** This is the central display, providing a paginated (20 items per page) and styled table. Each row includes:
    *   A serial number.
    *   A "Select" link to drill down to more detailed budget information for that group (conditional on the budget being positive).
    *   A "CK" checkbox which, when toggled, dynamically switches between showing a static label of the "Budget Amount" and an editable text input field (`TxtAmount`) for that budget.
    *   Columns for Business Group's Description (Name), Symbol, Budget Amount, PO, Cash Pay, Cash Rec, Tax, and Balance Budget.
    *   A footer row displaying grand totals for all numerical columns.
*   **Input Fields:** A hidden text input `TxtAmount` is associated with each row, revealed by the "CK" checkbox.
*   **Action Buttons:** "Insert", "Export", and "Cancel" buttons trigger the respective backend functionalities.
*   **Client-Side Logic:** The "CK" checkbox's ability to toggle the visibility of the budget input field is a direct candidate for Alpine.js `x-show` and `x-model`. Input validation for `TxtAmount` (positive numbers only) should be replicated.

---

## Step 4: Generate Django Code

We will create a new Django app, e.g., `accounts`, to manage this module.

### 4.1 Models (`accounts/models.py`)

The Django models will map to your existing database tables. We'll use `managed = False` to ensure Django doesn't try to create or modify these tables. The complex calculations from the C# code-behind will be encapsulated as methods within the `BusinessGroup` model, adhering to the "Fat Model" principle. We'll also include helper `GlobalFunctions` to mock the original utility classes and direct SQL interactions.

```python
from django.db import models, connection
from django.utils import timezone
from datetime import datetime
from decimal import Decimal

# Utility functions to mock the ASP.NET clsFunctions, CalBalBudgetAmt, PO_Budget_Amt
# In a real system, these would be proper service classes or integrated into models
# to interact with a dedicated data access layer or directly with the database.
class GlobalFunctions:
    @staticmethod
    def get_curr_date():
        return timezone.now().date()

    @staticmethod
    def get_curr_time():
        return timezone.now().time()

    @staticmethod
    def execute_sql_scalar(sql_query):
        """Executes a SQL query and returns the first column of the first row."""
        with connection.cursor() as cursor:
            try:
                cursor.execute(sql_query)
                result = cursor.fetchone()
                return Decimal(str(result[0])) if result and result[0] is not None else Decimal('0.00')
            except Exception as e:
                # Log the error (e.g., using Django's logging)
                print(f"SQL execution error for scalar: {e} | Query: {sql_query}")
                return Decimal('0.00')

    @staticmethod
    def execute_sql_fetch_all(sql_query):
        """Executes a SQL query and returns all rows as a list of dictionaries."""
        with connection.cursor() as cursor:
            try:
                cursor.execute(sql_query)
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
            except Exception as e:
                print(f"SQL execution error for fetch_all: {e} | Query: {sql_query}")
                return []

class BusinessGroup(models.Model):
    # 'Id' is the primary key from the existing database
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (e.g., migrations)
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.name

    def get_budget_details(self, company_id, financial_year_id, previous_financial_year_id):
        """
        Calculates and returns all budget-related amounts for this BusinessGroup.
        This method encapsulates the complex logic from ASP.NET's CalculateBalAmt().
        """
        bg_id = self.id

        # 1. Calculate opening balance from previous financial year
        # Mocking CalBalBudgetAmt.TotBalBudget_BG - this needs a more precise SQL query
        # if the original method pulls from specific tables. For demonstration, we'll
        # assume it's simply the sum of budget amounts for the previous year.
        prev_year_budget_query = f"""
            SELECT SUM(Amount) AS Budget FROM tblACC_Budget_Dept
            WHERE BGId = {bg_id} AND FinYearId = {previous_financial_year_id}
        """
        opening_bal_of_prev_year = GlobalFunctions.execute_sql_scalar(prev_year_budget_query)

        # 2. Calculate current year's budget from tblACC_Budget_Dept
        current_year_budget_query = f"""
            SELECT SUM(Amount) AS Budget FROM tblACC_Budget_Dept
            WHERE BGId = {bg_id} AND FinYearId = {financial_year_id}
            GROUP BY BGId
        """
        current_budget_amount = GlobalFunctions.execute_sql_scalar(current_year_budget_query)

        # Total Budget Amount = current year's entries + previous year's balance
        total_budget_amount = (current_budget_amount + opening_bal_of_prev_year).quantize(Decimal('0.01'))

        # 3. Calculate PO Basic and Tax Amounts (Mocking PBM.getTotal_PO_Budget_Amt)
        # These queries assume specific table names and column names based on the context.
        # Replace with your actual schema.
        po_basic_disc_amt_query = f"""
            SELECT SUM(TotalAmount) FROM tblACC_PO_Details_BasicDisc
            WHERE BGId = {bg_id} AND FinYearId = {financial_year_id} AND CompanyId = {company_id}
        """
        po_amount = GlobalFunctions.execute_sql_scalar(po_basic_disc_amt_query).quantize(Decimal('0.01'))

        po_tax_amt_query = f"""
            SELECT SUM(TaxAmount) FROM tblACC_PO_Details_Tax
            WHERE BGId = {bg_id} AND FinYearId = {financial_year_id} AND CompanyId = {company_id}
        """
        po_tax_amount = GlobalFunctions.execute_sql_scalar(po_tax_amt_query).quantize(Decimal('0.01'))

        # 4. Calculate total cash payment
        cash_pay_query = f"""
            SELECT SUM(tblACC_CashVoucher_Payment_Details.Amount) AS CashAmt FROM tblACC_CashVoucher_Payment_Details
            INNER JOIN tblACC_CashVoucher_Payment_Master ON tblACC_CashVoucher_Payment_Details.MId = tblACC_CashVoucher_Payment_Master.Id
            WHERE tblACC_CashVoucher_Payment_Details.BGGroup = {bg_id} AND tblACC_CashVoucher_Payment_Master.FinYearId = {financial_year_id}
            GROUP BY tblACC_CashVoucher_Payment_Details.BGGroup
        """
        total_cash_pay = GlobalFunctions.execute_sql_scalar(cash_pay_query).quantize(Decimal('0.01'))

        # 5. Calculate total cash receipt
        cash_rec_query = f"""
            SELECT SUM(Amount) AS CashAmt FROM tblACC_CashVoucher_Receipt_Master
            WHERE BGGroup = {bg_id} AND FinYearId = {financial_year_id}
            GROUP BY BGGroup
        """
        total_cash_rec = GlobalFunctions.execute_sql_scalar(cash_rec_query).quantize(Decimal('0.01'))

        # 6. Calculate Balance Budget
        balance_budget = (total_budget_amount - (po_amount + po_tax_amount + total_cash_pay) + total_cash_rec).quantize(Decimal('0.01'))

        return {
            'budget_amount': total_budget_amount,
            'po_amount': po_amount,
            'cash_pay': total_cash_pay,
            'cash_rec': total_cash_rec,
            'tax_amount': po_tax_amount,
            'balance_budget': balance_budget,
            'can_drilldown': total_budget_amount > 0 or opening_bal_of_prev_year > 0
        }


class BudgetDept(models.Model):
    # 'Id' is typically an IDENTITY column in SQL Server. Django will create its own 'id'
    # primary key by default. If the original 'Id' is needed for lookups, map it explicitly.
    # Here, we assume Django's default 'id' is sufficient for managing new records.
    
    sys_date = models.DateField(db_column='SysDate', default=GlobalFunctions.get_curr_date)
    sys_time = models.TimeField(db_column='SysTime', default=GlobalFunctions.get_curr_time)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    # Define a ForeignKey to BusinessGroup. BGId in tblACC_Budget_Dept maps to BusinessGroup.Id
    bg_id = models.ForeignKey(BusinessGroup, on_delete=models.PROTECT, db_column='BGId', related_name='budget_entries')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2)

    class Meta:
        managed = False  # Django will not manage this table
        db_table = 'tblACC_Budget_Dept'
        verbose_name = 'Budget Department Entry'
        verbose_name_plural = 'Budget Department Entries'
        # If the original system truly prevents multiple entries for the same BGId and FinYearId,
        # you would add: unique_together = (('bg_id', 'fin_year_id'),)
        # However, the ASP.NET `INSERT` implies additive, so we don't enforce uniqueness at DB level here.

    def __str__(self):
        return f"Budget for {self.bg_id.name} ({self.fin_year_id}): {self.amount}"

    @classmethod
    def create_budget_entry(cls, bg_id, amount, company_id, financial_year_id, session_id):
        """
        Inserts a new budget entry into tblACC_Budget_Dept.
        Mimics the exact INSERT behavior of the original C# code.
        """
        if amount <= 0: # Based on C# 'if (Amt > 0)' check
            return None # Or raise a specific exception

        try:
            business_group = BusinessGroup.objects.get(id=bg_id)
        except BusinessGroup.DoesNotExist:
            raise ValueError(f"BusinessGroup with ID {bg_id} does not exist.")

        budget_entry = cls.objects.create(
            bg_id=business_group,
            amount=amount,
            comp_id=company_id,
            fin_year_id=financial_year_id,
            session_id=session_id,
            sys_date=GlobalFunctions.get_curr_date(),
            sys_time=GlobalFunctions.get_curr_time()
        )
        return budget_entry

```

### 4.2 Forms (`accounts/forms.py`)

A Django Form will handle validation for the `Amount` field submitted via the grid.

```python
from django import forms
from .models import BudgetDept
import re

class BudgetDeptForm(forms.ModelForm):
    # This form is used for validation of the 'amount' field when submitted via HTMX.
    amount = forms.DecimalField(
        label="Budget Amount",
        min_value=Decimal('0.01'), # Enforce positive number as per original regex
        max_digits=18,
        decimal_places=2,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'number', # HTML5 number input
            'step': '0.01',   # Allow two decimal places
            # Alpine.js binding will be in the template
        })
    )

    class Meta:
        model = BudgetDept
        fields = ['amount'] # Only 'amount' is user-editable via the grid checkbox.
        # Other fields (bg_id, comp_id, fin_year_id, session_id, sys_date, sys_time)
        # are populated by the view or model method directly.

    def clean_amount(self):
        amount = self.cleaned_data['amount']
        # Replicate ASP.NET's RegularExpressionValidator: ^[1-9]\d*(\.\d+)?$
        # This regex ensures a positive number, optionally with decimals.
        # DecimalField and min_value handle numerical validity; regex ensures format strictness.
        amount_str = str(amount)
        if not re.match(r'^[1-9]\d*(\.\d+)?$', amount_str):
            raise forms.ValidationError("Please enter a positive number starting with 1-9 (e.g., 100, 5.50).")
        return amount

```

### 4.3 Views (`accounts/views.py`)

The views will be thin, primarily orchestrating data retrieval and handling HTTP responses. Business logic, especially calculations, resides in the `BusinessGroup` model.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect
from django.db import transaction
from decimal import Decimal

from .models import BusinessGroup, BudgetDept, GlobalFunctions
from .forms import BudgetDeptForm # Though not directly used in the current approach, kept for structure

class BudgetDistributionListView(ListView):
    model = BusinessGroup
    template_name = 'accounts/budget_dist/list.html'
    context_object_name = 'business_groups' # Not directly used for table data, but for page context
    
    # Keeping views thin: most logic in model.
    # The main purpose of this view is to render the initial page and handle session logic.
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Mock session variables based on ASP.NET behavior.
        # In a real app, these would come from authentication/session middleware or user profile.
        context['comp_id'] = self.request.session.get('compid', 1)  # Default 1
        context['fin_year_id'] = self.request.session.get('finyear', 2024) # Default 2024
        context['username'] = self.request.session.get('username', 'admin_user') # Default 'admin_user'
        
        # Calculate previous financial year
        context['prev_fin_year_id'] = context['fin_year_id'] - 1
        
        # Store active tab index in session, mimicking ASP.NET's TabContainer behavior
        current_tab_index = int(self.request.GET.get('tab', self.request.session.get('tab_index', 0)))
        self.request.session['tab_index'] = current_tab_index
        context['active_tab_index'] = current_tab_index
        
        return context

class BudgetDistributionTablePartialView(View):
    """
    Renders only the table content, designed to be loaded dynamically via HTMX.
    This replaces the data binding and rendering part of the ASP.NET GridView.
    """
    def get(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2024)
        prev_fin_year_id = fin_year_id - 1

        # Fetch Business Groups (excluding Id '1' as per ASP.NET SqlDataSource)
        # Ensure 'Id' is correctly mapped to the database primary key.
        business_groups = BusinessGroup.objects.exclude(id=1).order_by('name')

        # Prepare data with calculated fields for the table
        budget_data = []
        total_budget_amt = Decimal('0.00')
        total_po_amt = Decimal('0.00')
        total_cash_pay = Decimal('0.00')
        total_cash_rec = Decimal('0.00')
        total_tax_amt = Decimal('0.00')
        total_bal_budget_amt = Decimal('0.00')

        for bg in business_groups:
            details = bg.get_budget_details(comp_id, fin_year_id, prev_fin_year_id)
            budget_data.append({
                'id': bg.id,
                'name': bg.name,
                'symbol': bg.symbol,
                'budget_amount': details['budget_amount'],
                'po_amount': details['po_amount'],
                'cash_pay': details['cash_pay'],
                'cash_rec': details['cash_rec'],
                'tax_amount': details['tax_amount'],
                'balance_budget': details['balance_budget'],
                'can_drilldown': details['can_drilldown'],
            })
            total_budget_amt += details['budget_amount']
            total_po_amt += details['po_amount']
            total_cash_pay += details['cash_pay']
            total_cash_rec += details['cash_rec']
            total_tax_amt += details['tax_amount']
            total_bal_budget_amt += details['balance_budget']
        
        context = {
            'budget_data': budget_data,
            'total_budget_amt': total_budget_amt.quantize(Decimal('0.01')),
            'total_po_amt': total_po_amt.quantize(Decimal('0.01')),
            'total_cash_pay': total_cash_pay.quantize(Decimal('0.01')),
            'total_cash_rec': total_cash_rec.quantize(Decimal('0.01')),
            'total_tax_amt': total_tax_amt.quantize(Decimal('0.01')),
            'total_bal_budget_amt': total_bal_budget_amt.quantize(Decimal('0.01')),
            'fin_year_id': fin_year_id, # Pass fin_year_id for drilldown links
        }
        return render(request, 'accounts/budget_dist/_budget_table.html', context)


class BudgetDeptInsertView(View):
    """
    Handles the "Insert" button click, processing multiple budget entries from the form.
    This replaces the ASP.NET BtnInsert_Click event handler.
    """
    def post(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2024)
        username = request.session.get('username', 'admin_user')

        # The request.POST will contain data like 'amount_bg_X': 'Value'
        # from the HTMX form submission, where X is the BusinessGroup ID.
        
        budgets_to_process = []
        for key, value in request.POST.items():
            if key.startswith('amount_bg_'):
                bg_id = key.replace('amount_bg_', '')
                try:
                    bg_id = int(bg_id)
                    amount_str = value.strip()
                    if amount_str: # Only process if a value is provided
                        amount = Decimal(amount_str)
                        # Validate using the form's clean method for the 'amount' field
                        form = BudgetDeptForm({'amount': amount})
                        if form.is_valid():
                            if amount > 0: # Replicating C# `if (Amt > 0)` logic
                                budgets_to_process.append({'bg_id': bg_id, 'amount': amount})
                        else:
                            # If form is invalid, log the error but continue processing valid entries
                            messages.error(request, f"Invalid amount for Business Group {bg_id}: {form.errors['amount'].as_text()}")
                except (ValueError, TypeError):
                    messages.error(request, f"Invalid input for Business Group {bg_id}. Please enter a valid number.")
                    continue
        
        if not budgets_to_process:
            messages.warning(request, "No valid budget amounts provided for insertion.")
            # If no valid data, still send 204 for HTMX and trigger a refresh to show messages
            if request.headers.get('HX-Request'):
                return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetList'})
            return redirect(reverse_lazy('budget_dist_list'))

        try:
            with transaction.atomic():
                for budget_data in budgets_to_process:
                    # Mimicking original ASP.NET behavior of always INSERTing.
                    # For an UPSERT (update if exists, else create), you'd use BudgetDept.objects.update_or_create()
                    BudgetDept.create_budget_entry(
                        bg_id=budget_data['bg_id'],
                        amount=budget_data['amount'],
                        company_id=comp_id,
                        financial_year_id=fin_year_id,
                        session_id=username
                    )
            messages.success(request, f"{len(budgets_to_process)} budget entries processed successfully.")
        except ValueError as e:
            messages.error(request, str(e)) # For cases like BusinessGroup not found
        except Exception as e:
            messages.error(request, f"Error processing budget entries: {e}")
            # In production, use Django's logging: logger.exception("Error inserting budget entries")

        # After successful insert/update, redirect or trigger HTMX refresh
        if request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to indicate success without navigating,
            # and trigger 'refreshBudgetList' for the table to reload dynamically.
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetList'})
        return redirect(reverse_lazy('budget_dist_list'))


class BudgetDistributionExportView(View):
    """
    Handles the "Export" button click, replacing BtnExport_Click.
    This will generate an Excel file from the currently displayed data.
    Requires 'pandas' and 'openpyxl' libraries.
    """
    def get(self, request, *args, **kwargs):
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2024)
        prev_fin_year_id = fin_year_id - 1

        business_groups = BusinessGroup.objects.exclude(id=1).order_by('name')

        # Prepare data for export, similar to BudgetDistributionTablePartialView
        export_data = []
        for i, bg in enumerate(business_groups):
            details = bg.get_budget_details(comp_id, fin_year_id, prev_fin_year_id)
            export_data.append({
                'SN': i + 1,
                'Description': bg.name,
                'Symbol': bg.symbol,
                'Budget Amount': details['budget_amount'],
                'PO Amount': details['po_amount'],
                'Cash Pay Amount': details['cash_pay'],
                'Cash Rec. Amount': details['cash_rec'],
                'Tax': details['tax_amount'],
                'Bal Budget Amount': details['balance_budget'],
            })
        
        # Calculate and add totals as the last row
        total_budget_amt = sum(item['Budget Amount'] for item in export_data, Decimal('0.00'))
        total_po_amt = sum(item['PO Amount'] for item in export_data, Decimal('0.00'))
        total_cash_pay = sum(item['Cash Pay Amount'] for item in export_data, Decimal('0.00'))
        total_cash_rec = sum(item['Cash Rec. Amount'] for item in export_data, Decimal('0.00'))
        total_tax_amt = sum(item['Tax'] for item in export_data, Decimal('0.00'))
        total_bal_budget_amt = sum(item['Bal Budget Amount'] for item in export_data, Decimal('0.00'))

        export_data.append({
            'SN': '', 'Description': 'TOTAL', 'Symbol': '',
            'Budget Amount': total_budget_amt.quantize(Decimal('0.01')),
            'PO Amount': total_po_amt.quantize(Decimal('0.01')),
            'Cash Pay Amount': total_cash_pay.quantize(Decimal('0.01')),
            'Cash Rec. Amount': total_cash_rec.quantize(Decimal('0.01')),
            'Tax': total_tax_amt.quantize(Decimal('0.01')),
            'Bal Budget Amount': total_bal_budget_amt.quantize(Decimal('0.01')),
        })

        # Use pandas and openpyxl for Excel export
        try:
            import pandas as pd
            from io import BytesIO
            df = pd.DataFrame(export_data)
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, index=False, sheet_name='Budget_BG')
            output.seek(0)

            response = HttpResponse(
                output.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename="Budget_BG.xlsx"'
            return response
        except ImportError:
            messages.error(request, "Excel export requires 'pandas' and 'openpyxl'. Please install them.")
            return redirect(reverse_lazy('budget_dist_list'))
        except Exception as e:
            messages.error(request, f"An error occurred during export: {e}")
            # logger.exception("Error exporting budget data")
            return redirect(reverse_lazy('budget_dist_list'))

def cancel_budget_dist(request):
    """
    Handles the Cancel button click, replacing btnCancel_Click.
    Redirects to a main menu or dashboard page.
    """
    messages.info(request, "Budget distribution operation cancelled.")
    # Assuming a 'dashboard' or 'menu' URL exists in a 'core' app
    return redirect(reverse_lazy('core:dashboard'))

# Placeholder views for navigation links
class DepartmentBudgetDetailsView(View):
    def get(self, request, bg_id):
        # This would be the actual Django view for Budget_Dist_Dept_Details.aspx
        # It would likely be a DetailView or a custom view fetching department-level budget data.
        messages.info(request, f"Navigated to Department Budget Details for Business Group ID: {bg_id}.")
        return render(request, 'accounts/budget_dist/department_details_placeholder.html', {'bg_id': bg_id})

class WorkOrderDetailsView(View):
    def get(self, request):
        # This would be the actual Django view for Budget_Dist_WONo.aspx content
        # It would be a full page or a partial component to be included.
        messages.info(request, "Navigated to Work Order Details.")
        return render(request, 'accounts/budget_dist/work_order_placeholder.html')

```

### 4.4 Templates (`accounts/templates/accounts/budget_dist/`)

*   `list.html`: The main page template that orchestrates HTMX loading of the table.
*   `_budget_table.html`: A partial template containing only the table structure, designed to be reloaded by HTMX.
*   `department_details_placeholder.html`: A simple placeholder for the drilldown link.
*   `work_order_placeholder.html`: A simple placeholder for the iframe content.

**`accounts/templates/accounts/budget_dist/list.html`**
```html
{% extends 'core/base.html' %} {# Assumes core/base.html contains HTML boilerplate, CDNs for HTMX, Alpine.js, TailwindCSS #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Assign Budget - Business Groups</h2>
        <div class="flex flex-wrap gap-2"> {# Use flex-wrap for responsiveness #}
            <button
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50"
                hx-post="{% url 'budget_dist_insert' %}"
                hx-include="#budgetForm" {# Sends all inputs within this form #}
                hx-swap="none" {# No content swap, just status/triggers #}
                hx-indicator="#loadingIndicator" {# Shows loading indicator #}
                hx-confirm="Are you sure you want to insert/update the selected budget entries?"
                _="on htmx:afterRequest if event.detail.xhr.status == 204 then call window.location.reload() else console.error('Error inserting budget')"
                >
                <i class="fas fa-save mr-2"></i> Insert Selected Budgets
            </button>
            <a
                href="{% url 'budget_dist_export' %}"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out inline-flex items-center focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
                download="Budget_BG.xlsx">
                <i class="fas fa-file-excel mr-2"></i> Export to Excel
            </a>
            <a
                href="{% url 'budget_dist_cancel' %}"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-opacity-50">
                <i class="fas fa-times-circle mr-2"></i> Cancel
            </a>
        </div>
    </div>

    <!-- Tab Container mimicking ASP.NET TabContainer with Alpine.js -->
    <div x-data="{ activeTab: {{ active_tab_index }} }" class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6 pt-4" aria-label="Tabs">
                <a @click="activeTab = 0"
                   :class="{'border-indigo-500 text-indigo-700': activeTab === 0, 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-300': activeTab !== 0}"
                   class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm cursor-pointer"
                   hx-get="{% url 'budget_dist_list' %}?tab=0" hx-swap="none" hx-push-url="true">
                    Business Group
                </a>
                <a @click="activeTab = 1"
                   :class="{'border-indigo-500 text-indigo-700': activeTab === 1, 'border-transparent text-gray-600 hover:text-gray-800 hover:border-gray-300': activeTab !== 1}"
                   class="whitespace-nowrap py-3 px-1 border-b-2 font-medium text-sm cursor-pointer"
                   hx-get="{% url 'budget_dist_list' %}?tab=1" hx-swap="none" hx-push-url="true">
                    Work Order
                </a>
            </nav>
        </div>

        <div x-show="activeTab === 0" class="p-6">
            <form id="budgetForm">
                {% csrf_token %}
                <div id="budgetTable-container"
                     hx-trigger="load, refreshBudgetList from:body" {# Triggers on initial load and custom event #}
                     hx-get="{% url 'budget_dist_table_partial' %}" {# Fetches only the table content #}
                     hx-swap="innerHTML" {# Replaces the content inside budgetTable-container #}
                     class="overflow-x-auto"> {# For responsive table scrolling #}
                    <!-- DataTable will be loaded here via HTMX. Loading spinner as initial content. -->
                    <div class="text-center p-8">
                        <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                        <p class="mt-4 text-lg text-gray-600">Loading Business Group Budget Data...</p>
                    </div>
                </div>
            </form>
        </div>

        <div x-show="activeTab === 1" class="p-6">
            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Work Order Details</h3>
                <p class="text-gray-600 mb-4">
                    This section represents the content that was originally embedded via an iframe
                    (`Budget_Dist_WONo.aspx`). In a full Django migration, this would be converted
                    into a dedicated Django view and template.
                </p>
                <div class="h-96 w-full border border-gray-300 rounded-md flex items-center justify-center bg-white text-gray-500 overflow-auto">
                    <!-- The actual content from Budget_Dist_WONo.aspx would go here or be loaded dynamically -->
                    <p>Placeholder for Work Order content (link to: <a href="{% url 'work_order_details' %}" class="text-blue-500 hover:underline">Work Order Page</a>)</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- HTMX Global Loading Indicator -->
<div id="loadingIndicator" class="htmx-indicator fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 transition-opacity duration-300 ease-out opacity-0">
    <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-white"></div>
    <p class="text-white ml-4 text-xl">Processing...</p>
</div>
{% endblock %}

{% block extra_js %}
{# jQuery is required for DataTables #}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
{# DataTables CDN #}
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script>
    // DataTables initialization script. It's also embedded in the partial template,
    // which is the preferred way for HTMX-loaded content.
    // This block is here for completeness if you decide to load DataTables globally.
</script>
{% endblock %}

{% block extra_css %}
{# DataTables CSS #}
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.7/css/jquery.dataTables.min.css">
{% endblock %}
```

**`accounts/templates/accounts/budget_dist/_budget_table.html`** (Partial template for HTMX)
```html
{# This partial template is loaded dynamically by HTMX into list.html #}
{# It should NOT extend base.html #}

<table id="budgetDataTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Amount</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Cash Pay</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Cash Rec</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Tax</th>
            <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Bal Budget</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in budget_data %}
        {# Alpine.js x-data on each row for local state (checkbox, amount visibility) #}
        <tr x-data="{ isChecked: false, budgetAmount: '{{ obj.budget_amount|floatformat:2 }}' }">
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                {% if obj.can_drilldown %}
                <a href="{% url 'department_budget_details' bg_id=obj.id %}?ModId=14" class="text-blue-600 hover:text-blue-900 font-medium">Select</a>
                {% else %}
                <span class="text-gray-400">Select</span>
                {% endif %}
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-center">
                <input type="checkbox" x-model="isChecked"
                       class="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out border-gray-300 rounded focus:ring-indigo-500">
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ obj.name }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ obj.symbol }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">
                <span x-show="!isChecked">{{ obj.budget_amount|floatformat:2 }}</span>
                <input type="number" step="0.01" min="0.01" {# min ensures positive #}
                       name="amount_bg_{{ obj.id }}" {# Name attribute for form submission via HTMX #}
                       x-show="isChecked" {# Visibility controlled by Alpine.js #}
                       x-model="budgetAmount" {# Two-way binding for the input value #}
                       class="block w-28 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-right">
            </td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.po_amount|floatformat:2 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.cash_pay|floatformat:2 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.cash_rec|floatformat:2 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.tax_amount|floatformat:2 }}</td>
            <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.balance_budget|floatformat:2 }}</td>
        </tr>
        {% endfor %}
    </tbody>
    <tfoot class="bg-gray-50">
        <tr>
            <th colspan="5" class="py-2 px-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">Totals:</th>
            <th class="py-2 px-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">{{ total_budget_amt|floatformat:2 }}</th>
            <th class="py-2 px-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">{{ total_po_amt|floatformat:2 }}</th>
            <th class="py-2 px-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">{{ total_cash_pay|floatformat:2 }}</th>
            <th class="py-2 px-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">{{ total_cash_rec|floatformat:2 }}</th>
            <th class="py-2 px-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">{{ total_tax_amt|floatformat:2 }}</th>
            <th class="py-2 px-4 text-right text-xs font-bold text-gray-700 uppercase tracking-wider">{{ total_bal_budget_amt|floatformat:2 }}</th>
        </tr>
    </tfoot>
</table>

<script>
    // Initialize DataTables after the table is loaded
    // This script should be loaded after jQuery and DataTables CDN scripts.
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#budgetDataTable')) {
            $('#budgetDataTable').DataTable().destroy();
        }
        $('#budgetDataTable').DataTable({
            "paging": true,
            "pageLength": 20, // Matches original ASP.NET PageSize
            "searching": true, // Enables client-side search box
            "ordering": true,  // Enables column sorting
            "info": true,      // Shows table info (e.g., "Showing 1 to 10 of 50 entries")
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]], // Options for items per page
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 2] } // Disable sorting for SN, Select, CK columns
            ]
        });
    });
</script>
```

**`accounts/templates/accounts/budget_dist/department_details_placeholder.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Department Budget Details (Placeholder)</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <p class="text-lg text-gray-700 mb-4">
            This page would display detailed budget information for Business Group ID: <span class="font-semibold text-indigo-600">{{ bg_id }}</span>.
        </p>
        <p class="text-gray-600">
            In a full migration, the content from `Budget_Dist_Dept_Details.aspx` would be converted and rendered here.
        </p>
        <div class="mt-6">
            <a href="{% url 'budget_dist_list' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md inline-flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Budget Distribution
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

**`accounts/templates/accounts/budget_dist/work_order_placeholder.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Work Order Details (Placeholder)</h2>
    <div class="bg-white p-6 rounded-lg shadow-md">
        <p class="text-lg text-gray-700 mb-4">
            This page represents the content of the original `Budget_Dist_WONo.aspx` which was embedded as an iframe.
        </p>
        <p class="text-gray-600">
            In a full Django migration, this content would be converted into its own Django view and template,
            providing a native, integrated experience rather than an iframe.
        </p>
        <div class="mt-6">
            <a href="{% url 'budget_dist_list' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md inline-flex items-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Budget Distribution
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

### 4.5 URLs (`accounts/urls.py`)

This file defines the URL patterns for your `accounts` app, mapping them to the views.

```python
from django.urls import path
from .views import (
    BudgetDistributionListView,
    BudgetDistributionTablePartialView,
    BudgetDeptInsertView,
    BudgetDistributionExportView,
    cancel_budget_dist,
    DepartmentBudgetDetailsView, # Placeholder
    WorkOrderDetailsView,        # Placeholder
)

urlpatterns = [
    # Main Budget Distribution Page (Business Group tab default)
    path('budget-distribution/', BudgetDistributionListView.as_view(), name='budget_dist_list'),
    
    # HTMX endpoint to load the budget table partial (for dynamic updates)
    path('budget-distribution/table/', BudgetDistributionTablePartialView.as_view(), name='budget_dist_table_partial'),

    # Endpoint for inserting budget entries (handles form submission from the grid)
    path('budget-distribution/insert/', BudgetDeptInsertView.as_view(), name='budget_dist_insert'),

    # Endpoint for exporting data to Excel
    path('budget-distribution/export/', BudgetDistributionExportView.as_view(), name='budget_dist_export'),
    
    # Endpoint for cancelling the operation (redirects to a main menu/dashboard)
    path('budget-distribution/cancel/', cancel_budget_dist, name='budget_dist_cancel'),

    # Placeholder for the "Select" drilldown link (Budget_Dist_Dept_Details.aspx)
    # This would be a dedicated view for detailed department budget information.
    path('budget-distribution/details/<int:bg_id>/', DepartmentBudgetDetailsView.as_view(), name='department_budget_details'),

    # Placeholder for the "Work Order" iframe content (Budget_Dist_WONo.aspx)
    # This would be a dedicated view for work order details.
    path('budget-distribution/work-order/', WorkOrderDetailsView.as_view(), name='work_order_details'),
]

```
**Important:**
- You will need to include this `accounts/urls.py` in your project's main `urls.py` (e.g., `project_name/urls.py`).
  ```python
  # project_name/urls.py
  from django.contrib import admin
  from django.urls import path, include
  from django.views.generic import TemplateView # For a simple dashboard placeholder

  urlpatterns = [
      path('admin/', admin.site.urls),
      path('accounts/', include('accounts.urls')), # Your new app's URLs
      path('', TemplateView.as_view(template_name='core/dashboard.html'), name='core:dashboard'), # Placeholder for main dashboard
  ]
  ```
- Create a `core` app with a `dashboard.html` if you don't have a main dashboard for `core:dashboard`.
  
  **`core/templates/core/dashboard.html`**
  ```html
  {% extends 'core/base.html' %}
  {% block content %}
  <div class="container mx-auto px-4 py-8">
      <h2 class="text-2xl font-bold text-gray-800 mb-6">Welcome to Your AutoERP Dashboard!</h2>
      <div class="bg-white p-6 rounded-lg shadow-md">
          <p class="text-lg text-gray-700 mb-4">
              This is your main dashboard. From here, you can navigate to various modules.
          </p>
          <p class="text-gray-600 mb-4">
              Access the newly migrated Budget Distribution module:
          </p>
          <a href="{% url 'budget_dist_list' %}" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md inline-flex items-center">
              <i class="fas fa-chart-pie mr-2"></i> Go to Budget Distribution
          </a>
      </div>
  </div>
  {% endblock %}
  ```
  **`core/templates/core/base.html`** (Conceptual, as per instructions, DO NOT include in final output)
  ```html
  <!DOCTYPE html>
  <html lang="en">
  <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>{% block title %}AutoERP{% endblock %}</title>
      <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
      <script src="https://unpkg.com/htmx.org@1.9.10" integrity="sha384-D1Kt99CQGqPkmHfDnT8VlV29o4m7tGz+FhA6E+t3S7A6Bv0/tFj+k1z5f+Bv0E+c" crossorigin="anonymous"></script>
      <script src="https://unpkg.com/alpinejs@3.13.5/dist/cdn.min.js" defer></script>
      <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
      {% block extra_css %}{% endblock %}
  </head>
  <body class="bg-gray-100 font-sans leading-normal tracking-normal">
      <div class="container mx-auto">
          {% if messages %}
              <div class="mt-4 px-4 py-2">
                  {% for message in messages %}
                  <div class="mb-2 {% if message.tags %}alert-{{ message.tags }}{% endif %} p-3 rounded-md text-white
                      {% if 'success' in message.tags %}bg-green-500{% elif 'error' in message.tags %}bg-red-500{% elif 'warning' in message.tags %}bg-yellow-500{% else %}bg-blue-500{% endif %}">
                      {{ message }}
                  </div>
                  {% endfor %}
              </div>
          {% endif %}
          {% block content %}{% endblock %}
      </div>
      {% block extra_js %}{% endblock %}
  </body>
  </html>
  ```

### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for models and views to ensure functionality and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection
from unittest.mock import patch, MagicMock
from decimal import Decimal
from datetime import date, time

from .models import BusinessGroup, BudgetDept, GlobalFunctions

# Mock the database connection for testing GlobalFunctions and model methods that use raw SQL
class MockCursor:
    def __init__(self, fetch_value=None, fetch_all_values=None, description=None):
        self._fetch_value = fetch_value
        self._fetch_all_values = fetch_all_values
        self.description = description if description is not None else []
        self._call_count = 0

    def execute(self, sql_query):
        pass # We don't need to execute actual SQL in mocks

    def fetchone(self):
        self._call_count += 1
        if isinstance(self._fetch_value, list):
            # If a list of values is provided, return them sequentially
            return self._fetch_value[self._call_count - 1] if self._call_count <= len(self._fetch_value) else None
        return self._fetch_value

    def fetchall(self):
        return self._fetch_all_values

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

class MockConnection:
    def __init__(self, cursor_instance):
        self._cursor = cursor_instance

    def cursor(self):
        return self._cursor

    def close(self):
        pass

@patch('django.db.connection', new_callable=MagicMock)
class BusinessGroupModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for BusinessGroup. Since managed=False, we mock the DB behavior.
        # Ensure BusinessGroup objects are created directly for FK purposes in Django's ORM.
        cls.bg1 = BusinessGroup.objects.create(id=101, name='Test Business Group A', symbol='TGA')
        cls.bg2 = BusinessGroup.objects.create(id=102, name='Test Business Group B', symbol='TGB')

    def test_business_group_creation(self, mock_connection):
        # Test that BusinessGroup objects can be instantiated (though not saved to a managed DB)
        self.assertEqual(self.bg1.name, 'Test Business Group A')
        self.assertEqual(self.bg1.symbol, 'TGA')

    def test_business_group_str(self, mock_connection):
        self.assertEqual(str(self.bg1), 'Test Business Group A')
        
    @patch.object(GlobalFunctions, 'execute_sql_scalar')
    def test_get_budget_details(self, mock_execute_sql_scalar_method, mock_connection):
        # Mock various SQL query results for get_budget_details
        mock_execute_sql_scalar_method.side_effect = [
            Decimal('100.00'),  # opening_bal_of_prev_year
            Decimal('50.00'),   # current_budget_amount
            Decimal('20.00'),   # po_amount
            Decimal('5.00'),    # po_tax_amount
            Decimal('10.00'),   # total_cash_pay
            Decimal('3.00')     # total_cash_rec
        ]

        details = self.bg1.get_budget_details(
            company_id=1, financial_year_id=2024, previous_financial_year_id=2023
        )

        self.assertEqual(details['budget_amount'], Decimal('150.00')) # 100 + 50
        self.assertEqual(details['po_amount'], Decimal('20.00'))
        self.assertEqual(details['cash_pay'], Decimal('10.00'))
        self.assertEqual(details['cash_rec'], Decimal('3.00'))
        self.assertEqual(details['tax_amount'], Decimal('5.00'))
        # balance_budget = budget_amount - (po_amount + po_tax_amount + cash_pay) + cash_rec
        #                = 150.00 - (20.00 + 5.00 + 10.00) + 3.00 = 150.00 - 35.00 + 3.00 = 118.00
        self.assertEqual(details['balance_budget'], Decimal('118.00'))
        self.assertTrue(details['can_drilldown'])

        # Verify calls to execute_sql_scalar
        self.assertEqual(mock_execute_sql_scalar_method.call_count, 6)

@patch('django.db.connection', new_callable=MagicMock)
class BudgetDeptModelTest(TestCase):
    def setUp(self):
        # Create a mock BusinessGroup instance for foreign key relationships
        self.mock_bg = BusinessGroup.objects.create(id=99, name='Mock BG', symbol='MBG')
        # Patch BusinessGroup.objects.get to return our mock_bg when called with id=99
        self.patcher = patch('accounts.models.BusinessGroup.objects.get', return_value=self.mock_bg)
        self.patcher.start()
        
    def tearDown(self):
        self.patcher.stop()
        
    @patch('accounts.models.GlobalFunctions.get_curr_date', return_value=date(2024, 1, 15))
    @patch('accounts.models.GlobalFunctions.get_curr_time', return_value=time(10, 30, 0))
    def test_create_budget_entry(self, mock_get_curr_time, mock_get_curr_date, mock_connection):
        budget_entry = BudgetDept.create_budget_entry(
            bg_id=99,
            amount=Decimal('1234.56'),
            company_id=1,
            financial_year_id=2024,
            session_id='testuser'
        )

        self.assertIsInstance(budget_entry, BudgetDept)
        self.assertEqual(budget_entry.amount, Decimal('1234.56'))
        self.assertEqual(budget_entry.bg_id.id, 99)
        self.assertEqual(budget_entry.fin_year_id, 2024)
        self.assertEqual(budget_entry.session_id, 'testuser')
        self.assertEqual(budget_entry.sys_date, date(2024, 1, 15))
        self.assertEqual(budget_entry.sys_time, time(10, 30, 0))

    @patch('accounts.models.GlobalFunctions.get_curr_date', return_value=date(2024, 1, 15))
    @patch('accounts.models.GlobalFunctions.get_curr_time', return_value=time(10, 30, 0))
    def test_create_budget_entry_zero_or_negative_amount(self, mock_get_curr_time, mock_get_curr_date, mock_connection):
        budget_entry = BudgetDept.create_budget_entry(
            bg_id=99,
            amount=Decimal('0.00'), # Test with zero amount
            company_id=1,
            financial_year_id=2024,
            session_id='testuser'
        )
        self.assertIsNone(budget_entry)

        budget_entry_negative = BudgetDept.create_budget_entry(
            bg_id=99,
            amount=Decimal('-100.00'), # Test with negative amount
            company_id=1,
            financial_year_id=2024,
            session_id='testuser'
        )
        self.assertIsNone(budget_entry_negative)

    @patch('accounts.models.BusinessGroup.objects.get', side_effect=BusinessGroup.DoesNotExist)
    def test_create_budget_entry_business_group_not_found(self, mock_get_bg, mock_connection):
        with self.assertRaises(ValueError) as cm:
            BudgetDept.create_budget_entry(
                bg_id=999, # Non-existent BG ID
                amount=Decimal('100.00'),
                company_id=1,
                financial_year_id=2024,
                session_id='testuser'
            )
        self.assertIn("BusinessGroup with ID 999 does not exist.", str(cm.exception))


class BudgetDistributionViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session variables
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session['username'] = 'testuser'
        session.save()

        # Create test BusinessGroup instances
        self.bg1 = BusinessGroup.objects.create(id=10, name='Test BG 1', symbol='BG1')
        self.bg2 = BusinessGroup.objects.create(id=20, name='Test BG 2', symbol='BG2')
        # Create one BusinessGroup with ID 1 which should be excluded by the view logic
        self.bg_excluded = BusinessGroup.objects.create(id=1, name='Excluded BG', symbol='EXC')

    @patch('accounts.models.GlobalFunctions.execute_sql_scalar', side_effect=[Decimal('100'), Decimal('50'), Decimal('20'), Decimal('5'), Decimal('10'), Decimal('3')] * 2)
    def test_budget_distribution_list_view(self, mock_sql_scalar):
        response = self.client.get(reverse('budget_dist_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_dist/list.html')
        self.assertIn('active_tab_index', response.context)
        self.assertEqual(response.context['active_tab_index'], 0) # Default tab

    @patch('accounts.models.GlobalFunctions.execute_sql_scalar', side_effect=[Decimal('100'), Decimal('50'), Decimal('20'), Decimal('5'), Decimal('10'), Decimal('3')] * 2)
    def test_budget_distribution_table_partial_view(self, mock_sql_scalar):
        response = self.client.get(reverse('budget_dist_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_dist/_budget_table.html')
        self.assertIn('budget_data', response.context)
        self.assertEqual(len(response.context['budget_data']), 2) # Should exclude BG with id=1
        self.assertEqual(response.context['budget_data'][0]['id'], 10)
        self.assertEqual(response.context['budget_data'][1]['id'], 20)
        self.assertIn('total_budget_amt', response.context)
        
        # Test HTMX request for partial table
        hx_response = self.client.get(reverse('budget_dist_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(hx_response.status_code, 200)


    @patch('accounts.models.BudgetDept.create_budget_entry', return_value=MagicMock())
    def test_budget_dept_insert_view_post_valid_data(self, mock_create_entry):
        # Simulate form submission with multiple valid budget entries
        data = {
            'amount_bg_10': '100.50',
            'amount_bg_20': '200.75',
        }
        
        response = self.client.post(reverse('budget_dist_insert'), data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content for successful HTMX post, triggering HTMX refresh
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetList')

        # Verify that create_budget_entry was called for each valid entry
        self.assertEqual(mock_create_entry.call_count, 2)
        
        # Check specific calls
        mock_create_entry.assert_any_call(
            bg_id=10, amount=Decimal('100.50'), company_id=1, financial_year_id=2024, session_id='testuser'
        )
        mock_create_entry.assert_any_call(
            bg_id=20, amount=Decimal('200.75'), company_id=1, financial_year_id=2024, session_id='testuser'
        )

    @patch('accounts.models.BudgetDept.create_budget_entry', return_value=MagicMock())
    def test_budget_dept_insert_view_post_no_valid_data(self, mock_create_entry):
        data = {
            'amount_bg_invalid': 'abc', # Invalid number
            'amount_bg_30': '',         # Empty string
            'amount_bg_40': '0.00',     # Zero amount (should be skipped by business logic)
            'some_other_field': 'value'
        }
        response = self.client.post(reverse('budget_dist_insert'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # Still 204 for HTMX
        self.assertFalse(mock_create_entry.called) # No successful entries should be created

    @patch('accounts.models.GlobalFunctions.execute_sql_scalar', side_effect=[Decimal('100'), Decimal('50'), Decimal('20'), Decimal('5'), Decimal('10'), Decimal('3')])
    @patch('accounts.models.BusinessGroup.objects.exclude')
    @patch('pandas.DataFrame')
    @patch('pandas.ExcelWriter')
    def test_budget_distribution_export_view(self, mock_excel_writer, mock_dataframe, mock_exclude, mock_sql_scalar):
        # Mock the queryset return value and get_budget_details for BusinessGroup
        mock_bg_instance = MagicMock(spec=BusinessGroup)
        mock_bg_instance.id = 101
        mock_bg_instance.name = 'Test BG Export'
        mock_bg_instance.symbol = 'TBE'
        mock_bg_instance.get_budget_details.return_value = {
            'budget_amount': Decimal('100.00'), 'po_amount': Decimal('20.00'), 'cash_pay': Decimal('10.00'),
            'cash_rec': Decimal('5.00'), 'tax_amount': Decimal('2.00'), 'balance_budget': Decimal('73.00'),
            'can_drilldown': True
        }
        mock_exclude.return_value.order_by.return_value = [mock_bg_instance] # Ensure it returns an iterable

        response = self.client.get(reverse('budget_dist_export'))

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertIn('attachment; filename="Budget_BG.xlsx"', response['Content-Disposition'])
        mock_dataframe.assert_called_once()
        mock_excel_writer.assert_called_once()

    def test_cancel_budget_dist_view(self):
        response = self.client.get(reverse('budget_dist_cancel'))
        self.assertEqual(response.status_code, 302)
        # Assuming 'core:dashboard' is the correct redirect target
        self.assertRedirects(response, reverse('core:dashboard'))

    def test_department_budget_details_view_placeholder(self):
        response = self.client.get(reverse('department_budget_details', args=[123]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_dist/department_details_placeholder.html')
        self.assertEqual(response.context['bg_id'], 123)

    def test_work_order_details_view_placeholder(self):
        response = self.client.get(reverse('work_order_details'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/budget_dist/work_order_placeholder.html')
```

## Step 5: HTMX and Alpine.js Integration

The integration strategy leverages HTMX for backend communication and Alpine.js for frontend interactivity, eliminating the need for complex JavaScript frameworks.

*   **HTMX for Dynamic Content:**
    *   The main `list.html` uses `hx-get` to load the `_budget_table.html` partial. This happens `on load` and whenever a `refreshBudgetList` custom event is triggered from the `body` (e.g., after a successful budget insert). This ensures the table data is always up-to-date without full page reloads.
    *   The "Insert Selected Budgets" button uses `hx-post` to submit the entire form (`#budgetForm` which contains the input fields `name="amount_bg_X"`). Upon successful processing (HTTP 204 No Content), the server sends an `HX-Trigger` header with `refreshBudgetList` to tell HTMX to re-fetch the table data. A JavaScript snippet `_="on htmx:afterRequest ... call window.location.reload()"` is added to mimic the original ASP.NET's full page refresh behavior after an insert, although a pure HTMX approach would simply update the table without a full reload via the `hx-trigger`.
    *   A global `htmx-indicator` is set up to provide visual feedback during HTMX requests.

*   **Alpine.js for UI State:**
    *   Each row in `_budget_table.html` uses `x-data="{ isChecked: false, budgetAmount: '...' }"` to manage the local state for that specific row.
    *   The "CK" checkbox's state is bound using `x-model="isChecked"`.
    *   The `Budget Amount` field dynamically switches between a `<span>` (displaying the value) and an `<input type="number">` (for editing) using `x-show="!isChecked"` and `x-show="isChecked"` respectively.
    *   The `input` field's value is two-way bound using `x-model="budgetAmount"`, ensuring that initial values are displayed and user input is reflected.
    *   The tab navigation in `list.html` uses `x-data="{ activeTab: ... }"` to manage the active tab's state, and `x-show` directives conditionally display the content of each tab panel.

*   **DataTables for List Views:**
    *   The `_budget_table.html` partial includes a JavaScript snippet that initializes DataTables on the `#budgetDataTable` element. This script runs every time the partial is loaded via HTMX, ensuring the DataTables functionality (search, sort, pagination) is correctly applied to the newly loaded table content.
    *   Pagination is set to 20 rows per page, matching the original `GridView`'s `PageSize`.

## Final Notes

*   **Placeholder Views:** The `DepartmentBudgetDetailsView` and `WorkOrderDetailsView` are provided as placeholders. In a real-world migration, these would be fully implemented Django views with their own models, forms, and templates, replacing the original `.aspx` pages or iframe content.
*   **Authentication and Session:** The `compid`, `finyear`, and `username` session variables are mocked for demonstration. In a production Django application, these would typically be managed through Django's authentication system and potentially a custom user profile or settings model.
*   **Error Handling:** While basic `try-except` blocks are included, a production application would benefit from more robust error logging and user-friendly error messages, potentially leveraging Django's `messages` framework more extensively.
*   **Database Interaction:** The `GlobalFunctions` class demonstrates how to interact with the existing database directly using Django's `connection` object for complex queries that were originally embedded SQL. For simpler CRUD operations, Django's ORM is always preferred.
*   **Dependencies:** Remember to install `pandas` and `openpyxl` for the Excel export functionality (`pip install pandas openpyxl`).