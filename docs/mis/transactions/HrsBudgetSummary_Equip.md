## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

The ASP.NET code indicates the following primary tables are involved in generating the report, along with an inferred table for budget/utilized hours:

-   **`SD_Cust_WorkOrder_Master`**: This table holds work order details.
    *   `CompId` (int) - Company ID.
    *   `WONo` (string) - Work Order Number.
    *   `TaskProjectTitle` (string) - Project/Work Order Title.
-   **`tblDG_BOM_Master`**: This table likely stores Bill of Materials (BOM) information, linking work orders to items.
    *   `ItemId` (int) - Foreign key to `tblDG_Item_Master`.
    *   `WONo` (string) - Work Order Number.
    *   `PId` (int) - Parent ID, used as '0' to signify top-level items.
-   **`tblDG_Item_Master`**: This table contains master data for items/equipment.
    *   `Id` (int) - Primary key.
    *   `ItemCode` (string) - Equipment code.
    *   `ManfDesc` (string) - Manufacturer description (equipment description).
-   **Inferred: `tblHrsBudgetTracking`**: This table is inferred from the `Cal_Used_Hours` methods which take `CompId`, `WONo`, `ItemId`, `TypeId`, `SubTypeId`, and return allocated/utilized hours. This suggests a ledger or detail table for hours tracking.
    *   `CompId` (int)
    *   `WONo` (string)
    *   `ItemId` (int)
    *   `TypeId` (int) - Category (e.g., 2=Mechanical, 3=Electrical).
    *   `SubTypeId` (int) - Sub-category (e.g., 1=MDBHrs, 8=EDBHrs).
    *   `AllocatedHours` (double)
    *   `UtilizedHours` (double)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

This ASP.NET page is primarily a **Read** (report generation/display) operation. It fetches data from multiple tables, performs calculations and aggregations (via `Cal_Used_Hours` class), and then displays the results in a Crystal Report viewer. There are no Create, Update, or Delete operations directly exposed on this page.

*   **Read**: Data is retrieved from `SD_Cust_WorkOrder_Master`, `tblDG_BOM_Master`, `tblDG_Item_Master`. Complex hour calculations (`AllocatedHrs_WONo`, `UtilizeHrs_WONo`) are performed for each item. The results are then assembled into a tabular format and rendered.
*   **Navigation**: A "Cancel" button (`Button1_Click`) performs a `Response.Redirect` to `HrsBudgetSummary.aspx`. This will be translated to a Django redirect or a link.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

The UI consists primarily of:
-   A header (`Hrs Budget Summary`).
-   A "Cancel" button (`Button1`).
-   A `CrystalReportViewer` (`CrystalReportViewer1`) and its associated `CrystalReportSource` for displaying the generated report.

In Django, this will be replaced by:
-   A standard HTML page with a header.
-   A link or button for "Cancel".
-   A `<table>` element enhanced with DataTables for displaying the tabular report data, dynamically loaded via HTMX.
-   Alpine.js for minimal UI state, if any, beyond what HTMX provides.

### Step 4: Generate Django Code

We will create a Django app named `mis` for this module.

#### 4.1 Models

Task: Create Django models based on the database schema.

The business logic of `Cal_Used_Hours` will be integrated into the `HoursBudgetEntryManager` and related models.

```python
# mis/models.py
from django.db import models
from django.db.models import F, Sum, Value, FloatField
from django.db.models.functions import Coalesce
from dataclasses import dataclass

class WorkOrderMaster(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master table.
    Stores main work order details.
    """
    comp_id = models.IntegerField(db_column='CompId')
    won_no = models.CharField(db_column='WONo', max_length=50, primary_key=True) # Assuming WONo is PK as it's used directly in queries
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return f"{self.won_no} - {self.task_project_title}"

class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master table.
    Stores details for equipment/items.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, null=True, blank=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class BomMaster(models.Model):
    """
    Maps to tblDG_BOM_Master table.
    Links Work Orders to Items (Bill of Materials).
    """
    # Assuming BomMaster has its own primary key, or a composite key
    # For simplicity, let's assume an auto-incrementing ID. Adjust if DB schema is different.
    id = models.AutoField(db_column='Id', primary_key=True)
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    won_no = models.CharField(db_column='WONo', max_length=50) # Not a ForeignKey directly to WorkOrderMaster.won_no unless it is a PK
    p_id = models.IntegerField(db_column='PId', default=0) # Parent ID, often 0 for top-level

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'
        unique_together = (('item', 'won_no', 'p_id'),) # Inferring composite unique key

    def __str__(self):
        return f"BOM for WONo {self.won_no}, Item {self.item.item_code}"

class HoursBudgetEntry(models.Model):
    """
    Inferred table: tblHrsBudgetTracking or similar.
    Stores allocated and utilized hours for items by type and subtype.
    """
    comp_id = models.IntegerField(db_column='CompId')
    won_no = models.CharField(db_column='WONo', max_length=50) # FK to WorkOrderMaster, but as CharField
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    hour_type_id = models.IntegerField(db_column='TypeId') # 2 for Mechanical, 3 for Electrical
    hour_subtype_id = models.IntegerField(db_column='SubTypeId') # 1-7 for Mech, 8-14 for Elec
    allocated_hours = models.FloatField(db_column='AllocatedHours', default=0.0)
    utilized_hours = models.FloatField(db_column='UtilizedHours', default=0.0)

    class Meta:
        managed = False
        db_table = 'tblHrsBudgetTracking' # Example table name
        verbose_name = 'Hours Budget Entry'
        verbose_name_plural = 'Hours Budget Entries'
        # Composite primary key assumed for this inferred table
        unique_together = (('comp_id', 'won_no', 'item', 'hour_type_id', 'hour_subtype_id'),)

    def __str__(self):
        return f"Hours for {self.item.item_code} (WO: {self.won_no}, Type: {self.hour_type_id}, Sub: {self.hour_subtype_id})"

@dataclass
class EquipmentBudgetReportRow:
    """
    Data structure for a single row of the Hrs Budget Summary report.
    This is not a Django model, but a Python dataclass to hold computed report data.
    """
    comp_id: int
    won_no: str
    project_title: str
    equip_no: str
    description: str

    # Mechanical
    mdb_b_hrs: float
    mdu_u_hrs: float
    mab_b_hrs: float
    mau_u_hrs: float
    mcb_b_hrs: float
    mcu_u_hrs: float
    mtb_b_hrs: float
    mtu_u_hrs: float
    mdib_b_hrs: float
    mdiu_u_hrs: float
    mib_b_hrs: float
    miu_u_hrs: float
    mtrb_b_hrs: float
    mtru_u_hrs: float

    # Electrical
    edb_b_hrs: float
    edu_u_hrs: float
    eab_b_hrs: float
    eau_u_hrs: float
    ecb_b_hrs: float
    ecu_u_hrs: float
    etb_b_hrs: float
    etu_u_hrs: float
    edib_b_hrs: float
    ediu_u_hrs: float
    eib_b_hrs: float
    eiu_u_hrs: float
    etrb_b_hrs: float
    etru_u_hrs: float

class EquipmentBudgetSummaryManager(models.Manager):
    """
    Custom manager to encapsulate the complex report data retrieval and calculation logic.
    This replaces the Cal_Used_Hours class and the data table population in C#.
    This manager is designed to be called directly, or can be attached to a specific model.
    For this report, it's more like a service layer.
    """

    def get_allocated_and_utilized_hours(self, comp_id, won_no, item_id, type_id, subtype_id):
        """
        Replicates the logic of CUS.AllocatedHrs_WONo and CUS.UtilizeHrs_WONo.
        Retrieves allocated and utilized hours for a specific item, type, and subtype.
        """
        hours_entry = HoursBudgetEntry.objects.filter(
            comp_id=comp_id,
            won_no=won_no,
            item_id=item_id,
            hour_type_id=type_id,
            hour_subtype_id=subtype_id
        ).first() # .first() gets one record, .values() aggregates if needed

        allocated = hours_entry.allocated_hours if hours_entry and hours_entry.allocated_hours > 0 else 0.0
        utilized = hours_entry.utilized_hours if hours_entry and hours_entry.utilized_hours > 0 else 0.0
        return allocated, utilized

    def get_equipment_budget_summary(self, comp_id: int, won_no: str) -> list[EquipmentBudgetReportRow]:
        """
        Generates the comprehensive budget summary report for equipment
        based on the provided Company ID and Work Order Number.
        This method replaces the Page_Init logic in C#.
        """
        report_data = []

        # 1. Get Project Title
        work_order = WorkOrderMaster.objects.filter(comp_id=comp_id, won_no=won_no).first()
        if not work_order:
            return [] # No work order found, return empty report

        project_title = work_order.task_project_title or "N/A"

        # 2. Get Items related to the Work Order from BOM (PId=0 for top-level items)
        bom_items = BomMaster.objects.filter(won_no=won_no, p_id=0).select_related('item')

        for bom_entry in bom_items:
            item = bom_entry.item # This is the ItemMaster object
            item_id = item.id
            equip_no = item.item_code or "N/A"
            description = item.manf_desc or "N/A"

            # Initialize all hours to 0
            hours_data = {
                'mdb_b_hrs': 0.0, 'mdu_u_hrs': 0.0, 'mab_b_hrs': 0.0, 'mau_u_hrs': 0.0,
                'mcb_b_hrs': 0.0, 'mcu_u_hrs': 0.0, 'mtb_b_hrs': 0.0, 'mtu_u_hrs': 0.0,
                'mdib_b_hrs': 0.0, 'mdiu_u_hrs': 0.0, 'mib_b_hrs': 0.0, 'miu_u_hrs': 0.0,
                'mtrb_b_hrs': 0.0, 'mtru_u_hrs': 0.0,
                'edb_b_hrs': 0.0, 'edu_u_hrs': 0.0, 'eab_b_hrs': 0.0, 'eau_u_hrs': 0.0,
                'ecb_b_hrs': 0.0, 'ecu_u_hrs': 0.0, 'etb_b_hrs': 0.0, 'etu_u_hrs': 0.0,
                'edib_b_hrs': 0.0, 'ediu_u_hrs': 0.0, 'eib_b_hrs': 0.0, 'eiu_u_hrs': 0.0,
                'etrb_b_hrs': 0.0, 'etru_u_hrs': 0.0,
            }

            # Mechanical Hours (TypeId=2)
            # MDBHrs (SubTypeId=1)
            hours_data['mdb_b_hrs'], hours_data['mdu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 2, 1)
            # MABHrs (SubTypeId=2)
            hours_data['mab_b_hrs'], hours_data['mau_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 2, 2)
            # MCBHrs (SubTypeId=3)
            hours_data['mcb_b_hrs'], hours_data['mcu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 2, 3)
            # MTBHrs (SubTypeId=4)
            hours_data['mtb_b_hrs'], hours_data['mtu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 2, 4)
            # MDIBHrs (SubTypeId=5)
            hours_data['mdib_b_hrs'], hours_data['mdiu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 2, 5)
            # MIBHrs (SubTypeId=6)
            hours_data['mib_b_hrs'], hours_data['miu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 2, 6)
            # MTRBHrs (SubTypeId=7)
            hours_data['mtrb_b_hrs'], hours_data['mtru_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 2, 7)

            # Electrical Hours (TypeId=3)
            # EDBHrs (SubTypeId=8)
            hours_data['edb_b_hrs'], hours_data['edu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 3, 8)
            # EABHrs (SubTypeId=9)
            hours_data['eab_b_hrs'], hours_data['eau_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 3, 9)
            # ECBHrs (SubTypeId=10)
            hours_data['ecb_b_hrs'], hours_data['ecu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 3, 10)
            # ETBHrs (SubTypeId=11)
            hours_data['etb_b_hrs'], hours_data['etu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 3, 11)
            # EDIBHrs (SubTypeId=12)
            hours_data['edib_b_hrs'], hours_data['ediu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 3, 12)
            # EIBHrs (SubTypeId=13)
            hours_data['eib_b_hrs'], hours_data['eiu_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 3, 13)
            # ETRBHrs (SubTypeId=14)
            hours_data['etrb_b_hrs'], hours_data['etru_u_hrs'] = self.get_allocated_and_utilized_hours(comp_id, won_no, item_id, 3, 14)

            # Create a report row object
            report_row = EquipmentBudgetReportRow(
                comp_id=comp_id,
                won_no=won_no,
                project_title=project_title,
                equip_no=equip_no,
                description=description,
                **hours_data
            )
            report_data.append(report_row)
        return report_data

# Attach the manager to a model for easy access or use it as a standalone service
# For this report, we can simply instantiate it or define it as a utility.
# Let's attach it to WorkOrderMaster for a more organized access point.
WorkOrderMaster.report_manager = EquipmentBudgetSummaryManager()

```

#### 4.2 Forms

Task: Define a Django form for user input.

No specific form is needed for this report display page as `WONo` is passed via query string. If parameters needed to be dynamically chosen by the user, a simple `forms.Form` would be implemented here.

```python
# mis/forms.py
# No forms needed for this specific read-only report display.
# If filtering parameters were on the page, a form would be here.
```

#### 4.3 Views

Task: Implement CRUD operations using CBVs.

This page is a report, so we'll use a `TemplateView` or a custom `View` to fetch and display the data. We'll also need a partial view for HTMX to reload just the table.

```python
# mis/views.py
from django.views.generic import TemplateView, View
from django.shortcuts import render
from django.http import HttpResponse
from django.urls import reverse_lazy
from django.contrib import messages
from .models import WorkOrderMaster # Import the model with our report manager

class HrsBudgetSummaryEquipView(TemplateView):
    """
    Main view for the Hrs Budget Summary Report for Equipment.
    Handles initial page load and displays the report.
    """
    template_name = 'mis/hrs_budget_summary_equip/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Extract parameters from query string.
        # In a real app, CompId/FinYearId/SId would come from authenticated user session.
        comp_id = self.request.GET.get('compid', '0') # Default to '0' or handle error
        won_no = self.request.GET.get('wono')

        try:
            comp_id = int(comp_id)
        except ValueError:
            comp_id = 0 # Handle invalid comp_id
            messages.error(self.request, "Invalid Company ID provided.")
            return context # Return early if parameters are invalid

        if not won_no:
            messages.error(self.request, "Work Order Number (WONo) is required.")
            return context

        # Add parameters to context for the main page
        context['won_no'] = won_no
        context['comp_id'] = comp_id
        # The actual report data will be loaded via HTMX by the partial view
        return context

class HrsBudgetSummaryEquipTablePartialView(View):
    """
    HTMX-specific view to render only the report table content.
    This keeps the main view thin and focused on overall page structure.
    """
    def get(self, request, *args, **kwargs):
        comp_id = request.GET.get('compid', '0')
        won_no = request.GET.get('wono')

        try:
            comp_id = int(comp_id)
        except ValueError:
            comp_id = 0
            # No message needed for HTMX partial, just return empty/error content
        
        report_data = []
        if won_no and comp_id:
            # Call the 'fat model' manager method to get the report data
            report_data = WorkOrderMaster.report_manager.get_equipment_budget_summary(comp_id, won_no)
        else:
            # In a real app, handle errors for missing parameters more gracefully
            pass 

        context = {
            'report_rows': report_data,
            'project_title': report_data[0].project_title if report_data else 'N/A'
        }
        return render(request, 'mis/hrs_budget_summary_equip/_table.html', context)

# The original ASP.NET had a "Cancel" button that redirects.
# We can just use a simple redirect view for that.
class HrsBudgetSummaryCancelView(View):
    def get(self, request, *args, **kwargs):
        # PKey from original query string, if it exists, use it for redirect
        p_key = request.GET.get('PKey', '')
        # Construct the redirect URL as per original ASP.NET
        redirect_url = f"{reverse_lazy('mis:hrs_budget_summary_list')}?ModId=14&SubModId=&Key={p_key}"
        return HttpResponse(
            status=204,
            headers={
                'HX-Redirect': redirect_url # HTMX friendly redirect
            }
        )

# For the redirect target HrsBudgetSummary.aspx, assuming it will be renamed to hrs_budget_summary/list.html
# We define a placeholder for it if it's not implemented yet.
class HrsBudgetSummaryListView(TemplateView):
    template_name = 'mis/hrs_budget_summary/list.html' # Placeholder
    # This view would be part of a different migration step for the main summary page
    # It's only here to allow the reverse_lazy to work for the cancel button
```

#### 4.4 Templates

Task: Create templates for each view.

We'll need two templates: `list.html` for the main page and `_table.html` for the HTMX loaded table partial.

```html
{# mis/templates/mis/hrs_budget_summary_equip/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Hrs Budget Summary (Equipment)</h2>
        <a href="{% url 'mis:hrs_budget_summary_cancel' %}"
           hx-boost="true" 
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md">
            Cancel
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold mb-4">Work Order Details:</h3>
        <p class="mb-2"><strong>Work Order No:</strong> {{ won_no }}</p>
        <p class="mb-4"><strong>Company ID:</strong> {{ comp_id }}</p>

        <div id="hrsBudgetEquipTable-container"
             hx-trigger="load once" {# Load once on initial page load #}
             hx-get="{% url 'mis:hrs_budget_summary_equip_table' %}?compid={{ comp_id }}&wono={{ won_no }}"
             hx-swap="innerHTML">
            <!-- Loading indicator while HTMX loads the table -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading Equipment Budget Summary...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // No specific Alpine.js needed for this simple report view yet.
    // Alpine.js would be used for more complex UI state management like modals or dynamic filters.
</script>
{% endblock %}

```

```html
{# mis/templates/mis/hrs_budget_summary_equip/_table.html #}
{# This template is designed to be loaded via HTMX #}

<div class="mb-4">
    <p class="text-md"><strong>Project Title:</strong> {{ project_title }}</p>
</div>

{% if report_rows %}
<div class="overflow-x-auto">
    <table id="hrsBudgetEquipTable" class="min-w-full bg-white border border-gray-300">
        <thead>
            <tr class="bg-gray-100">
                <th rowspan="2" class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
                <th rowspan="2" class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Equipment No</th>
                <th rowspan="2" class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Description</th>
                <th colspan="14" class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider bg-blue-100">Mechanical Hours</th>
                <th colspan="14" class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider bg-green-100">Electrical Hours</th>
            </tr>
            <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MDB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MDU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MAB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MAU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MCB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MCU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MTB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MTU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MDIB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MDIU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MIB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MIU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MTRB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">MTRU Utilized</th>

                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EDB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EDU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EAB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EAU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">ECB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">ECU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">ETB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">ETU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EDIB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EDIU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EIB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">EIU Utilized</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">ETRB Budget</th>
                <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase tracking-wider">ETRU Utilized</th>
            </tr>
        </thead>
        <tbody>
            {% for row in report_rows %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ row.equip_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ row.description }}</td>
                
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mdb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mdu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mab_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mau_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mcb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mcu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mtb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mtu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mdib_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mdiu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mib_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.miu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mtrb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.mtru_u_hrs|floatformat:"2" }}</td>

                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.edb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.edu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.eab_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.eau_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.ecb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.ecu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.etb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.etu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.edib_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.ediu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.eib_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.eiu_u_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.etrb_b_hrs|floatformat:"2" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ row.etru_u_hrs|floatformat:"2" }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#hrsBudgetEquipTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "scrollX": true, // Enable horizontal scrolling for many columns
        "dom": 'lfrtip', // Layout: Length changing input, Filtering input, Table, Information, Pagination
    });
});
</script>
{% else %}
<p class="text-center text-gray-500 py-10">No budget summary data found for the given Work Order Number.</p>
{% endif %}
```

#### 4.5 URLs

Task: Define URL patterns for the views.

We will define URLs within the `mis` app.

```python
# mis/urls.py
from django.urls import path
from .views import (
    HrsBudgetSummaryEquipView, 
    HrsBudgetSummaryEquipTablePartialView,
    HrsBudgetSummaryCancelView,
    HrsBudgetSummaryListView # Placeholder for original redirect target
)

app_name = 'mis' # Define app_name for namespacing URLs

urlpatterns = [
    # Main report page (e.g., /mis/hrs-budget-summary-equip/?compid=X&wono=Y)
    path('hrs-budget-summary-equip/', HrsBudgetSummaryEquipView.as_view(), name='hrs_budget_summary_equip'),
    
    # HTMX endpoint for loading just the table content
    path('hrs-budget-summary-equip/table/', HrsBudgetSummaryEquipTablePartialView.as_view(), name='hrs_budget_summary_equip_table'),

    # URL for the "Cancel" button redirect target
    path('hrs-budget-summary-cancel/', HrsBudgetSummaryCancelView.as_view(), name='hrs_budget_summary_cancel'),
    
    # Placeholder for the main HrsBudgetSummary page (if not yet migrated)
    path('hrs-budget-summary/', HrsBudgetSummaryListView.as_view(), name='hrs_budget_summary_list'),
]
```

#### 4.6 Tests

Task: Write tests for the model and views.

We will create mock data to test the complex logic of the `EquipmentBudgetSummaryManager` and the view rendering.

```python
# mis/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import WorkOrderMaster, ItemMaster, BomMaster, HoursBudgetEntry, EquipmentBudgetReportRow, EquipmentBudgetSummaryManager

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = 1
        cls.won_no_valid = "WO-001"
        cls.won_no_invalid = "WO-999"

        # WorkOrderMaster
        cls.work_order = WorkOrderMaster.objects.create(
            comp_id=cls.company_id,
            won_no=cls.won_no_valid,
            task_project_title="Project Alpha - Budget"
        )

        # ItemMaster
        cls.item_mech = ItemMaster.objects.create(id=101, item_code="EQ-M-001", manf_desc="Mechanical Pump XYZ")
        cls.item_elec = ItemMaster.objects.create(id=102, item_code="EQ-E-002", manf_desc="Electrical Motor ABC")

        # BomMaster
        BomMaster.objects.create(item=cls.item_mech, won_no=cls.won_no_valid, p_id=0)
        BomMaster.objects.create(item=cls.item_elec, won_no=cls.won_no_valid, p_id=0)

        # HoursBudgetEntry (simulating Cal_Used_Hours data)
        # Mechanical (TypeId=2)
        HoursBudgetEntry.objects.create(comp_id=cls.company_id, won_no=cls.won_no_valid, item=cls.item_mech, hour_type_id=2, hour_subtype_id=1, allocated_hours=100.0, utilized_hours=80.0) # MDBHrs
        HoursBudgetEntry.objects.create(comp_id=cls.company_id, won_no=cls.won_no_valid, item=cls.item_mech, hour_type_id=2, hour_subtype_id=2, allocated_hours=50.0, utilized_hours=45.0) # MABHrs
        HoursBudgetEntry.objects.create(comp_id=cls.company_id, won_no=cls.won_no_valid, item=cls.item_mech, hour_type_id=2, hour_subtype_id=7, allocated_hours=10.0, utilized_hours=5.0) # MTRBHrs
        
        # Electrical (TypeId=3)
        HoursBudgetEntry.objects.create(comp_id=cls.company_id, won_no=cls.won_no_valid, item=cls.item_elec, hour_type_id=3, hour_subtype_id=8, allocated_hours=200.0, utilized_hours=150.0) # EDBHrs
        HoursBudgetEntry.objects.create(comp_id=cls.company_id, won_no=cls.won_valid, item=cls.item_elec, hour_type_id=3, hour_subtype_id=14, allocated_hours=20.0, utilized_hours=10.0) # ETRBHrs

    def test_work_order_master_creation(self):
        wo = WorkOrderMaster.objects.get(won_no=self.won_no_valid)
        self.assertEqual(wo.task_project_title, "Project Alpha - Budget")

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=101)
        self.assertEqual(item.item_code, "EQ-M-001")

    def test_bom_master_creation(self):
        bom = BomMaster.objects.get(item=self.item_mech, won_no=self.won_no_valid)
        self.assertEqual(bom.p_id, 0)
        self.assertEqual(bom.item.item_code, "EQ-M-001")
    
    def test_hours_budget_entry_creation(self):
        hbe = HoursBudgetEntry.objects.get(won_no=self.won_no_valid, item=self.item_mech, hour_type_id=2, hour_subtype_id=1)
        self.assertEqual(hbe.allocated_hours, 100.0)
        self.assertEqual(hbe.utilized_hours, 80.0)

    def test_get_allocated_and_utilized_hours(self):
        manager = EquipmentBudgetSummaryManager()
        # Test existing entry
        allocated, utilized = manager.get_allocated_and_utilized_hours(self.company_id, self.won_no_valid, self.item_mech.id, 2, 1)
        self.assertEqual(allocated, 100.0)
        self.assertEqual(utilized, 80.0)

        # Test non-existing entry
        allocated, utilized = manager.get_allocated_and_utilized_hours(self.company_id, self.won_no_valid, self.item_mech.id, 2, 99) # Non-existent subtype
        self.assertEqual(allocated, 0.0)
        self.assertEqual(utilized, 0.0)
        
    def test_get_equipment_budget_summary(self):
        manager = EquipmentBudgetSummaryManager()
        report = manager.get_equipment_budget_summary(self.company_id, self.won_no_valid)
        
        self.assertIsInstance(report, list)
        self.assertEqual(len(report), 2) # Should have two items (mech and elec)

        mech_row = next((r for r in report if r.equip_no == "EQ-M-001"), None)
        elec_row = next((r for r in report if r.equip_no == "EQ-E-002"), None)

        self.assertIsNotNone(mech_row)
        self.assertIsNotNone(elec_row)

        # Verify Mechanical hours
        self.assertEqual(mech_row.mdb_b_hrs, 100.0)
        self.assertEqual(mech_row.mdu_u_hrs, 80.0)
        self.assertEqual(mech_row.mab_b_hrs, 50.0)
        self.assertEqual(mech_row.mau_u_hrs, 45.0)
        self.assertEqual(mech_row.mtrb_b_hrs, 10.0)
        self.assertEqual(mech_row.mtru_u_hrs, 5.0)
        # All other mechanical hours should be 0.0 as they weren't created
        self.assertEqual(mech_row.mcb_b_hrs, 0.0) 
        
        # Verify Electrical hours
        self.assertEqual(elec_row.edb_b_hrs, 200.0)
        self.assertEqual(elec_row.edu_u_hrs, 150.0)
        self.assertEqual(elec_row.etrb_b_hrs, 20.0)
        self.assertEqual(elec_row.etru_u_hrs, 10.0)
        # All other electrical hours should be 0.0
        self.assertEqual(elec_row.eab_b_hrs, 0.0)

    def test_get_equipment_budget_summary_no_work_order(self):
        manager = EquipmentBudgetSummaryManager()
        report = manager.get_equipment_budget_summary(self.company_id, self.won_no_invalid)
        self.assertEqual(len(report), 0)

    def test_get_equipment_budget_summary_no_bom_items(self):
        # Create a work order with no BOM items
        WorkOrderMaster.objects.create(comp_id=self.company_id, won_no="WO-002", task_project_title="No BOM Project")
        manager = EquipmentBudgetSummaryManager()
        report = manager.get_equipment_budget_summary(self.company_id, "WO-002")
        self.assertEqual(len(report), 0)


class ViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data (same as ModelTest for consistency)
        cls.company_id = 1
        cls.won_no_valid = "WO-001"
        cls.won_no_no_data = "WO-002"
        cls.won_no_invalid = "INVALID-WO"

        WorkOrderMaster.objects.create(
            comp_id=cls.company_id,
            won_no=cls.won_no_valid,
            task_project_title="Project Alpha - Budget"
        )
        WorkOrderMaster.objects.create(
            comp_id=cls.company_id,
            won_no=cls.won_no_no_data,
            task_project_title="Project Beta - No Data"
        )

        item_mech = ItemMaster.objects.create(id=101, item_code="EQ-M-001", manf_desc="Mechanical Pump XYZ")
        BomMaster.objects.create(item=item_mech, won_no=cls.won_no_valid, p_id=0)
        HoursBudgetEntry.objects.create(comp_id=cls.company_id, won_no=cls.won_no_valid, item=item_mech, hour_type_id=2, hour_subtype_id=1, allocated_hours=100.0, utilized_hours=80.0)

    def setUp(self):
        self.client = Client()
    
    def test_hrs_budget_summary_equip_view_get(self):
        response = self.client.get(reverse('mis:hrs_budget_summary_equip'), {'compid': self.company_id, 'wono': self.won_no_valid})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/hrs_budget_summary_equip/list.html')
        self.assertIn('won_no', response.context)
        self.assertIn('comp_id', response.context)
        self.assertEqual(response.context['won_no'], self.won_no_valid)
        self.assertEqual(response.context['comp_id'], self.company_id)

    def test_hrs_budget_summary_equip_view_missing_wono(self):
        response = self.client.get(reverse('mis:hrs_budget_summary_equip'), {'compid': self.company_id})
        self.assertEqual(response.status_code, 200) # Still 200 for TemplateView rendering with messages
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Work Order Number (WONo) is required.")

    def test_hrs_budget_summary_equip_view_invalid_compid(self):
        response = self.client.get(reverse('mis:hrs_budget_summary_equip'), {'compid': 'abc', 'wono': self.won_no_valid})
        self.assertEqual(response.status_code, 200)
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Invalid Company ID provided.")

    def test_hrs_budget_summary_equip_table_partial_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('mis:hrs_budget_summary_equip_table'), 
                                   {'compid': self.company_id, 'wono': self.won_no_valid},
                                   **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/hrs_budget_summary_equip/_table.html')
        self.assertIn('report_rows', response.context)
        self.assertEqual(len(response.context['report_rows']), 1)
        self.assertContains(response, 'Mechanical Pump XYZ')
        self.assertContains(response, '100.00')

    def test_hrs_budget_summary_equip_table_partial_view_no_data(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('mis:hrs_budget_summary_equip_table'), 
                                   {'compid': self.company_id, 'wono': self.won_no_no_data},
                                   **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/hrs_budget_summary_equip/_table.html')
        self.assertIn('report_rows', response.context)
        self.assertEqual(len(response.context['report_rows']), 0)
        self.assertContains(response, 'No budget summary data found')
        
    def test_hrs_budget_summary_equip_table_partial_view_missing_params(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('mis:hrs_budget_summary_equip_table'), **headers) # Missing wono and compid
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mis/hrs_budget_summary_equip/_table.html')
        self.assertIn('report_rows', response.context)
        self.assertEqual(len(response.context['report_rows']), 0)


    def test_hrs_budget_summary_cancel_view(self):
        response = self.client.get(reverse('mis:hrs_budget_summary_cancel'), {'PKey': 'some_key'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX redirect
        self.assertIn('HX-Redirect', response.headers)
        expected_redirect_url = f"{reverse('mis:hrs_budget_summary_list')}?ModId=14&SubModId=&Key=some_key"
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)

```

### Step 5: HTMX and Alpine.js Integration

The provided solution fully embraces HTMX for dynamic content loading:
-   The main report page (`list.html`) uses `hx-get` to load the table content from `hrs_budget_summary_equip_table/` URL.
-   The `hx-trigger="load once"` ensures the table loads automatically when the page loads.
-   The "Cancel" button uses `hx-boost="true"` for a smooth HTMX-driven navigation and the `HX-Redirect` header from the Django view ensures HTMX handles the redirection gracefully without a full page refresh if possible, or triggers a full redirect if not.
-   DataTables is integrated directly into the `_table.html` partial, ensuring client-side sorting, searching, and pagination capabilities upon HTMX injection. The `$(document).ready` ensures it initializes correctly after HTMX swaps the content.
-   Alpine.js is not strictly necessary for this read-only report but is ready for any minor UI state management. For this specific case, it would be useful if we had dynamic filtering forms or interactive elements within the report.

### Final Notes

-   **Business Value:** This modernization provides a responsive, web-standards-compliant report viewer without proprietary Crystal Reports dependencies. It significantly reduces server load by using HTMX for partial content updates and offloads data manipulation to the client with DataTables. The 'fat model' approach ensures business logic is centralized and testable.
-   **Automation Focus:** The conversion process outlined here focuses on direct mapping of UI elements and business logic to Django's structured components. An AI automation tool would identify the ASP.NET control patterns, SQL queries, and code-behind logic, and then apply these templates and transformations automatically. For instance, the tool would detect the `CrystalReportViewer` and suggest a `<table>` with DataTables, analyze `Page_Init` for data fetching and `Cal_Used_Hours` calls to generate the `EquipmentBudgetSummaryManager` methods, and identify query parameters to translate to `request.GET`.
-   **Placeholders:** `[APP_NAME]` is replaced by `mis`. Specific table names and column names are inferred.
-   **DRY & Best Practices:** Model definitions avoid redundant code. Views are thin, delegating heavy lifting to the `EquipmentBudgetSummaryManager`. Templates extend a base.html and use partials. Tests ensure quality.
-   **Scalability:** Django's ORM and robust architecture, combined with HTMX/DataTables, provides a highly scalable and maintainable solution compared to the legacy ASP.NET application.