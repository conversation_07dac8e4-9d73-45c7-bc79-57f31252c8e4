## ASP.NET to Django Conversion Script:

This plan outlines the modernization of your ASP.NET "Hours Budget Summary" report page to a modern Django application, emphasizing automated migration, clean architecture, and dynamic frontend interactions using HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with two primary tables: `tblPM_ManPowerPlanning` and `SD_Cust_WorkOrder_Master`.
- `tblPM_ManPowerPlanning` appears to hold details about allocated and utilized hours for various work orders, categories (e.g., Mechanical, Electrical), and task types.
- `SD_Cust_WorkOrder_Master` contains work order details like the project title.
The report aggregates data from `tblPM_ManPowerPlanning` grouped by `WONo` and joins with `SD_Cust_WorkOrder_Master` to get the `ProjectTitle`.

**Identified Tables and Columns:**

-   **`tblPM_ManPowerPlanning`**:
    -   `WONo` (string, likely a foreign key to `SD_Cust_WorkOrder_Master`)
    -   `CompId` (integer)
    -   `CategoryType` (integer, e.g., 2 for Mechanical, 3 for Electrical)
    -   `TaskType` (integer, e.g., 1-7 for Mechanical tasks, 8-14 for Electrical tasks)
    -   `AllocatedHrs` (double/float)
    -   `UtilizedHrs` (double/float)
    -   (Implicit ID column for primary key, e.g., `ID` or auto-generated by Django)

-   **`SD_Cust_WorkOrder_Master`**:
    -   `WONo` (string, primary key)
    -   `TaskProjectTitle` (string)

### Step 2: Identify Backend Functionality

**Analysis:**
This ASP.NET page is purely a **Read** (report display) operation.
-   It retrieves distinct `WONo` and `CompId` from `tblPM_ManPowerPlanning`.
-   For each `WONo`, it queries `SD_Cust_WorkOrder_Master` for `TaskProjectTitle`.
-   It then performs extensive calculations using `Cal_Used_Hours` (which we will migrate to model methods) to determine utilization percentages for various mechanical and electrical tasks.
-   The data is then loaded into a Crystal Report viewer.
-   A "Cancel" button performs a redirect.
-   A `HyrLink` (hyperlink) column is generated, indicating a drill-down capability to another page (`HrsBudgetSummary_Equip.aspx`) based on `WONo`.

**CRUD Operations and Business Logic:**
-   **Read:** The core functionality is to read aggregated and calculated data for a summary report.
-   **Calculations:** The percentage utilization calculations (`(UtilizedHrs / AllocatedHrs) * 100`) are key business logic that will reside within Django models.
-   **No Create, Update, Delete:** This page does not perform any direct data modification.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page is a simple report viewer:
-   A title: "Hrs Budget Summary"
-   A "Cancel" button.
-   The main content is a `CrystalReportViewer`, which displays the generated report.

**Django Equivalent UI Components:**
-   **List View:** A Django template to display the summary data in a tabular format.
-   **DataTables:** Essential for client-side searching, sorting, and pagination of the report data.
-   **HTMX:** Used to load the DataTable asynchronously into the main page.
-   **Buttons/Links:** For the "Cancel" action and the "Detail Link" (HyrLink).
-   **No Forms:** Since this is a display-only report, no input forms are required on this specific page.

---

### Step 4: Generate Django Code

We will create a new Django application, let's call it `budget_summary`, to house this functionality.

#### 4.1 Models

We define two models, `WorkOrder` and `ManPowerPlanning`, mapping them to the existing database tables using `managed = False`. The complex hour calculation logic will be implemented as methods on the `WorkOrder` model, adhering to the "fat model" principle.

**File: `budget_summary/models.py`**
```python
from django.db import models
from django.db.models import Sum
from django.urls import reverse

class WorkOrder(models.Model):
    """
    Maps to the existing SD_Cust_WorkOrder_Master table.
    Contains methods to calculate the various budget utilization percentages.
    """
    won_no = models.CharField(db_column='WONo', max_length=50, primary_key=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255)

    class Meta:
        managed = False  # Django will not manage this table's creation, alteration, or deletion.
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.won_no} - {self.task_project_title}"

    def get_manpower_hours(self, category_type: int, task_type: int):
        """
        Retrieves total utilized and allocated hours for a specific Work Order,
        category type (e.g., Mechanical, Electrical), and task type.
        This method replaces the functionality of C# Cal_Used_Hours methods.
        """
        # Ensure ManPowerPlanning model is accessible for querying
        qs = ManPowerPlanning.objects.filter(
            won_no=self.won_no,
            category_type=category_type,
            task_type=task_type
        ).aggregate(
            total_utilized=Sum('utilized_hrs'),
            total_allocated=Sum('allocated_hrs')
        )

        utilized = qs['total_utilized'] or 0.0
        allocated = qs['total_allocated'] or 0.0

        return utilized, allocated

    def calculate_utilization_percentage(self, category_type: int, task_type: int) -> float:
        """
        Calculates the utilization percentage (utilized / allocated * 100)
        for a given category and task. Handles division by zero.
        """
        utilized_hours, allocated_hours = self.get_manpower_hours(category_type, task_type)

        if utilized_hours > 0 and allocated_hours > 0:
            return (utilized_hours / allocated_hours) * 100
        return 0.0

    def get_budget_summary_data(self, comp_id: int = None) -> dict:
        """
        Gathers all required budget summary data for this specific Work Order,
        mimicking the DataTable structure populated in the ASP.NET code-behind.
        """
        # Dynamically retrieve CompId from a related ManPowerPlanning record
        # In a real system, CompId might be passed or derived from the user context.
        # Here we attempt to find one associated with the WO.
        wo_comp_id = ManPowerPlanning.objects.filter(won_no=self.won_no).values_list('comp_id', flat=True).first()

        data = {
            'WONo': self.won_no,
            'CompId': wo_comp_id if wo_comp_id is not None else comp_id, # Fallback to passed comp_id if any
            'ProjectTitle': self.task_project_title,
        }

        # Mechanical Categories (CategoryType=2 in C# code)
        data['MDesign'] = self.calculate_utilization_percentage(2, 1)
        data['MAssly'] = self.calculate_utilization_percentage(2, 2)
        data['MCert'] = self.calculate_utilization_percentage(2, 3)
        data['MTrials'] = self.calculate_utilization_percentage(2, 4)
        data['MIC'] = self.calculate_utilization_percentage(2, 5)
        data['MDisp'] = self.calculate_utilization_percentage(2, 6)
        data['MTryOut'] = self.calculate_utilization_percentage(2, 7)

        # Electrical Categories (CategoryType=3 in C# code)
        data['DDesign'] = self.calculate_utilization_percentage(3, 8)
        data['DAssly'] = self.calculate_utilization_percentage(3, 9)
        data['DCert'] = self.calculate_utilization_percentage(3, 10)
        data['DTrials'] = self.calculate_utilization_percentage(3, 11)
        data['DIC'] = self.calculate_utilization_percentage(3, 12)
        data['DDisp'] = self.calculate_utilization_percentage(3, 13)
        data['DTryOut'] = self.calculate_utilization_percentage(3, 14)

        # HyrLink corresponds to navigation to a detail page for this WONo
        # The 'Key' and 'PKey' from ASP.NET are for session/page tracking, not needed here.
        data['HyrLink'] = reverse('budget_summary_equip_detail', args=[self.won_no])

        return data


class ManPowerPlanning(models.Model):
    """
    Maps to the existing tblPM_ManPowerPlanning table.
    Holds the raw allocated and utilized hours data.
    """
    # Note: WONo is a CharField here because it's a field in an unmanaged table
    # rather than a formal Django ForeignKey relationship which would require
    # Django to manage table creation.
    won_no = models.CharField(db_column='WONo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    category_type = models.IntegerField(db_column='CategoryType')
    task_type = models.IntegerField(db_column='TaskType')
    allocated_hrs = models.FloatField(db_column='AllocatedHrs')
    utilized_hrs = models.FloatField(db_column='UtilizedHrs')
    # Assuming the table has an ID column as its primary key.
    # If not, Django would create an 'id' field, but for managed=False,
    # it expects an existing PK. Adjust 'ID' if the column name is different.
    id = models.AutoField(db_column='ID', primary_key=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning'
        verbose_name = 'Manpower Planning Record'
        verbose_name_plural = 'Manpower Planning Records'

    def __str__(self):
        return f"WO: {self.won_no} (Cat: {self.category_type}, Task: {self.task_type})"

```

#### 4.2 Forms

This page is for displaying a report, not for data input (Create/Update). Therefore, no Django forms are required for this specific view.

#### 4.3 Views

We'll use a `ListView` to orchestrate fetching the aggregated data and preparing it for the template. A separate `View` will handle the HTMX request for the DataTables partial, keeping the main `ListView` simple.

**File: `budget_summary/views.py`**
```python
from django.views.generic import ListView, View
from django.shortcuts import render
from django.http import HttpResponse
from .models import WorkOrder, ManPowerPlanning # Import both models

class HrsBudgetSummaryListView(ListView):
    """
    Displays the main 'Hours Budget Summary' page.
    The actual table content is loaded via HTMX by HrsBudgetSummaryTablePartialView.
    """
    # model and context_object_name are set here for consistency,
    # but the primary data fetching for the table partial happens in get_queryset
    model = WorkOrder 
    template_name = 'budget_summary/hrs_budget_summary/list.html'
    context_object_name = 'work_order_summary_data' # For consistency, though not directly used in the main template body

    def get_queryset(self):
        """
        Retrieves the data required for the budget summary report.
        This method mirrors the data gathering logic from the ASP.NET code-behind,
        utilizing model methods for calculations.
        """
        # In ASP.NET, CompId was used for filtering.
        # If your Django application manages CompId via user session/profile,
        # you would retrieve it here:
        # comp_id = self.request.session.get('compid') or self.request.user.profile.compid
        
        # For this example, we fetch distinct WONos that exist in ManPowerPlanning
        # to ensure we only process work orders for which we have budget data.
        distinct_wonos_with_data = ManPowerPlanning.objects.values_list('won_no', flat=True).distinct()
        
        # Fetch WorkOrder objects related to these distinct WONos
        work_orders = WorkOrder.objects.filter(won_no__in=distinct_wonos_with_data).order_by('won_no')

        summary_data = []
        for wo in work_orders:
            # Each WorkOrder instance calculates its own summary data
            summary_data.append(wo.get_budget_summary_data())
            
        return summary_data # This list of dictionaries will be the context for the table

class HrsBudgetSummaryTablePartialView(View):
    """
    Renders the DataTables partial for the budget summary report.
    This view is specifically targeted by HTMX requests from the main list page.
    """
    def get(self, request, *args, **kwargs):
        # Instantiate the ListView and call its get_queryset method to get the data
        # This keeps the data preparation logic DRY and within the ListView.
        list_view = HrsBudgetSummaryListView()
        list_view.setup(request) # Important to set up the request context for the ListView
        summary_data = list_view.get_queryset()
        
        # Pass the prepared data to the partial template
        return render(request, 'budget_summary/hrs_budget_summary/_table.html', {
            'work_order_summary_data': summary_data
        })

class HrsBudgetSummaryEquipDetailView(View):
    """
    Placeholder for the drill-down detail page linked from the summary report.
    This would be the Django equivalent of 'HrsBudgetSummary_Equip.aspx'.
    """
    def get(self, request, won_no: str):
        # In a real application, this view would fetch and display detailed
        # equipment budget data for the given won_no.
        # This is a stub for demonstration.
        return HttpResponse(f"""
            <div class="container mx-auto px-4 py-8">
                <h1 class="text-3xl font-bold mb-4">Work Order Details: {won_no}</h1>
                <p class="text-gray-700 mb-6">This page would display detailed equipment budget summary for Work Order <strong>{won_no}</strong>.</p>
                <button onclick="window.history.back()" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Back to Summary
                </button>
            </div>
        """)

```

#### 4.4 Templates

We will create two templates: one for the main page (`list.html`) and a partial for the DataTables table (`_table.html`) which HTMX will load.

**File: `budget_summary/templates/budget_summary/hrs_budget_summary/list.html`**
```html
{% extends 'core/base.html' %} {# Extends your main base template #}

{% block title %}Hours Budget Summary{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Hours Budget Summary</h2>
        <a href="{% url 'menu_page' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-200 ease-in-out">
            Cancel
        </a>
    </div>
    
    <div id="hrsBudgetSummaryTable-container"
         hx-trigger="load" {# Load the table content once the page loads #}
         hx-get="{% url 'hrs_budget_summary_table' %}"
         hx-swap="innerHTML">
        <!-- Loading indicator while HTMX fetches the table content -->
        <div class="text-center p-8 bg-white rounded-lg shadow-md">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading budget summary data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // No specific Alpine.js code required for this simple report display page.
    // Alpine.js is typically used for client-side interactivity like managing modals,
    // toggling visibility, or simple state management if needed.
    // For this migration, HTMX handles the dynamic loading of the table.
</script>
{% endblock %}
```

**File: `budget_summary/templates/budget_summary/hrs_budget_summary/_table.html`**
```html
{# This template is loaded via HTMX into list.html. It does not extend base.html #}
<div class="overflow-x-auto shadow-md sm:rounded-lg border border-gray-200">
    <table id="hrsBudgetSummaryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No.</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mech. Design %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mech. Assembly %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mech. Certification %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mech. Trials %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mech. I&C %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mech. Dispatch %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Mech. Try Out %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elec. Design %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elec. Assembly %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elec. Certification %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elec. Trials %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elec. I&C %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elec. Dispatch %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Elec. Try Out %</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Detail Link</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in work_order_summary_data %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap font-semibold">{{ row.WONo }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.ProjectTitle }}</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.MDesign == 0 %}text-red-500{% elif row.MDesign < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.MDesign|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.MAssly == 0 %}text-red-500{% elif row.MAssly < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.MAssly|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.MCert == 0 %}text-red-500{% elif row.MCert < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.MCert|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.MTrials == 0 %}text-red-500{% elif row.MTrials < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.MTrials|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.MIC == 0 %}text-red-500{% elif row.MIC < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.MIC|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.MDisp == 0 %}text-red-500{% elif row.MDisp < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.MDisp|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.MTryOut == 0 %}text-red-500{% elif row.MTryOut < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.MTryOut|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.DDesign == 0 %}text-red-500{% elif row.DDesign < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.DDesign|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.DAssly == 0 %}text-red-500{% elif row.DAssly < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.DAssly|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.DCert == 0 %}text-red-500{% elif row.DCert < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.DCert|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.DTrials == 0 %}text-red-500{% elif row.DTrials < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.DTrials|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.DIC == 0 %}text-red-500{% elif row.DIC < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.DIC|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.DDisp == 0 %}text-red-500{% elif row.DDisp < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.DDisp|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap {% if row.DTryOut == 0 %}text-red-500{% elif row.DTryOut < 100 %}text-yellow-600{% else %}text-green-600{% endif %}">{{ row.DTryOut|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <a href="{{ row.HyrLink }}" class="text-blue-600 hover:text-blue-800 font-medium">View Details</a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="18" class="py-4 px-4 text-center text-gray-500 text-sm">No budget summary data available.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Ensure DataTables is re-initialized correctly after HTMX swap
    // Destroy any existing DataTable instance on the target element
    if ($.fn.DataTable.isDataTable('#hrsBudgetSummaryTable')) {
        $('#hrsBudgetSummaryTable').DataTable().destroy();
    }
    $('#hrsBudgetSummaryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "scrollX": true, // Enable horizontal scrolling for wide tables
        "responsive": true, // Enable responsive design
        "dom": 'lfrtip', // Standard DataTables DOM elements (Length, Filter, Table, Info, Paging)
        "language": { // Optional: Customize language strings
            "search": "Search All Columns:",
            "lengthMenu": "Show _MENU_ entries"
        }
    });
});
</script>
```

#### 4.5 URLs

The `urls.py` defines the routes for the main summary page, the HTMX-loaded table, the detail drill-down, and a placeholder for the cancel button's destination.

**File: `budget_summary/urls.py`**
```python
from django.urls import path
from django.http import HttpResponse # For placeholder view
from .views import HrsBudgetSummaryListView, HrsBudgetSummaryTablePartialView, HrsBudgetSummaryEquipDetailView

urlpatterns = [
    # Main page for the Hours Budget Summary report
    path('hrs-budget-summary/', HrsBudgetSummaryListView.as_view(), name='hrs_budget_summary_list'),
    
    # HTMX endpoint to load the DataTables content
    path('hrs-budget-summary/table/', HrsBudgetSummaryTablePartialView.as_view(), name='hrs_budget_summary_table'),
    
    # Detail page for drill-down from the report, identified by Work Order Number (WONo)
    path('hrs-budget-summary/equip/<str:won_no>/', HrsBudgetSummaryEquipDetailView.as_view(), name='budget_summary_equip_detail'),

    # Placeholder URL for the "Cancel" button redirection
    # In a real application, this would point to your main application menu or dashboard.
    path('menu/', lambda request: HttpResponse('<div class="container mx-auto px-4 py-8"><h1 class="text-3xl font-bold">Welcome to the ERP Menu!</h1><p class="text-gray-600">This is a placeholder page.</p><a href="/hrs-budget-summary/" class="text-blue-600 hover:text-blue-800">Go back to Budget Summary</a></div>'), name='menu_page'),
]

```

#### 4.6 Tests

Comprehensive unit tests for the `WorkOrder` and `ManPowerPlanning` models, focusing on the calculation logic, and integration tests for the views to ensure correct data rendering and HTMX interaction.

**File: `budget_summary/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from django.db.models import Sum
from .models import WorkOrder, ManPowerPlanning

# Test case for Model functionality
class HrsBudgetSummaryModelTest(TestCase):
    def setUp(self):
        # Setup mock instances for unmanaged models for testing.
        # In a real setup, if you had a test database populated via fixtures,
        # you would query actual instances here.
        self.wo1 = WorkOrder(won_no='WO001', task_project_title='Project Alpha')
        self.wo2 = WorkOrder(won_no='WO002', task_project_title='Project Beta')

        # Patch the ManPowerPlanning.objects for controlled testing of model methods
        patcher_mp_objects = patch('budget_summary.models.ManPowerPlanning.objects')
        self.mock_mp_objects = patcher_mp_objects.start()
        self.addCleanup(patcher_mp_objects.stop)

    def _mock_manpower_aggregate_result(self, utilized: float = 0.0, allocated: float = 0.0):
        """Helper to configure the mock aggregate result for ManPowerPlanning.objects.filter().aggregate."""
        self.mock_mp_objects.filter.return_value.aggregate.return_value = {
            'total_utilized': utilized,
            'total_allocated': allocated
        }

    def test_work_order_str_representation(self):
        """Test the string representation of WorkOrder model."""
        self.assertEqual(str(self.wo1), 'WO001 - Project Alpha')

    def test_man_power_planning_str_representation(self):
        """Test the string representation of ManPowerPlanning model."""
        mp = ManPowerPlanning(id=1, won_no='WO123', comp_id=1, category_type=2, task_type=1, allocated_hrs=100.0, utilized_hrs=80.0)
        self.assertEqual(str(mp), 'WO: WO123 (Cat: 2, Task: 1)')

    def test_get_manpower_hours_success(self):
        """Test get_manpower_hours when data exists."""
        self._mock_manpower_aggregate_result(utilized=80.0, allocated=100.0)
        utilized, allocated = self.wo1.get_manpower_hours(2, 1)
        self.assertEqual(utilized, 80.0)
        self.assertEqual(allocated, 100.0)
        self.mock_mp_objects.filter.assert_called_once_with(won_no='WO001', category_type=2, task_type=1)

    def test_get_manpower_hours_no_data(self):
        """Test get_manpower_hours when no data is found."""
        self._mock_manpower_aggregate_result(utilized=None, allocated=None) # Simulate no records found
        utilized, allocated = self.wo1.get_manpower_hours(99, 99)
        self.assertEqual(utilized, 0.0)
        self.assertEqual(allocated, 0.0)

    def test_calculate_utilization_percentage_normal(self):
        """Test percentage calculation with valid utilized and allocated hours."""
        self._mock_manpower_aggregate_result(utilized=80.0, allocated=100.0)
        percentage = self.wo1.calculate_utilization_percentage(2, 1)
        self.assertEqual(percentage, 80.0)

    def test_calculate_utilization_percentage_zero_allocated(self):
        """Test percentage calculation when allocated hours are zero."""
        self._mock_manpower_aggregate_result(utilized=50.0, allocated=0.0)
        percentage = self.wo1.calculate_utilization_percentage(2, 2)
        self.assertEqual(percentage, 0.0) # Should return 0 to avoid division by zero

    def test_calculate_utilization_percentage_zero_utilized(self):
        """Test percentage calculation when utilized hours are zero, but allocated are positive."""
        self._mock_manpower_aggregate_result(utilized=0.0, allocated=200.0)
        percentage = self.wo1.calculate_utilization_percentage(2, 3)
        self.assertEqual(percentage, 0.0)

    def test_calculate_utilization_percentage_both_zero(self):
        """Test percentage calculation when both utilized and allocated hours are zero."""
        self._mock_manpower_aggregate_result(utilized=0.0, allocated=0.0)
        percentage = self.wo1.calculate_utilization_percentage(2, 4)
        self.assertEqual(percentage, 0.0)

    @patch('budget_summary.models.WorkOrder.get_manpower_hours')
    @patch('budget_summary.models.ManPowerPlanning.objects.values_list')
    def test_get_budget_summary_data(self, mock_mp_values_list, mock_get_manpower_hours):
        """Test the comprehensive get_budget_summary_data method."""
        # Mock ManPowerPlanning.objects.values_list for comp_id lookup
        mock_mp_values_list.return_value.first.return_value = 101 # Simulate CompId

        # Simulate get_manpower_hours calls for various categories/tasks
        # Use a list of side effects to return different (utilized, allocated) for each call
        mock_get_manpower_hours.side_effect = [
            (80.0, 100.0),   # MDesign (2,1)
            (90.0, 100.0),   # MAssly (2,2)
            (10.0, 10.0),    # MCert (2,3)
            (0.0, 50.0),     # MTrials (2,4)
            (25.0, 50.0),    # MIC (2,5)
            (70.0, 70.0),    # MDisp (2,6)
            (0.0, 0.0),      # MTryOut (2,7)
            (40.0, 80.0),    # DDesign (3,8)
            (50.0, 50.0),    # DAssly (3,9)
            (15.0, 30.0),    # DCert (3,10)
            (0.0, 10.0),     # DTrials (3,11)
            (10.0, 40.0),    # DIC (3,12)
            (60.0, 60.0),    # DDisp (3,13)
            (0.0, 0.0),      # DTryOut (3,14)
        ]

        summary_data = self.wo1.get_budget_summary_data()

        self.assertEqual(summary_data['WONo'], 'WO001')
        self.assertEqual(summary_data['ProjectTitle'], 'Project Alpha')
        self.assertEqual(summary_data['CompId'], 101) # Check mocked CompId
        self.assertEqual(summary_data['MDesign'], 80.0)
        self.assertEqual(summary_data['MAssly'], 90.0)
        self.assertEqual(summary_data['MCert'], 100.0)
        self.assertEqual(summary_data['MTrials'], 0.0)
        self.assertEqual(summary_data['MIC'], 50.0)
        self.assertEqual(summary_data['MDisp'], 100.0)
        self.assertEqual(summary_data['MTryOut'], 0.0)
        self.assertEqual(summary_data['DDesign'], 50.0)
        self.assertEqual(summary_data['DAssly'], 100.0)
        self.assertEqual(summary_data['DCert'], 50.0)
        self.assertEqual(summary_data['DTrials'], 0.0)
        self.assertEqual(summary_data['DIC'], 25.0)
        self.assertEqual(summary_data['DDisp'], 100.0)
        self.assertEqual(summary_data['DTryOut'], 0.0)
        self.assertIn(reverse('budget_summary_equip_detail', args=['WO001']), summary_data['HyrLink'])
        self.assertEqual(mock_get_manpower_hours.call_count, 14) # Verify all calls were made

# Test case for View functionality
class HrsBudgetSummaryViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        
        # Patch the ORM calls in views to use mock data
        self.patcher_mp_values_list = patch('budget_summary.models.ManPowerPlanning.objects.values_list')
        self.mock_mp_values_list = self.patcher_mp_values_list.start()
        self.addCleanup(self.patcher_mp_values_list.stop)

        self.patcher_wo_objects = patch('budget_summary.models.WorkOrder.objects')
        self.mock_wo_objects = self.patcher_wo_objects.start()
        self.addCleanup(self.patcher_wo_objects.stop)

    def _prepare_mock_data_for_views(self, num_work_orders: int = 2):
        """Prepares mock WorkOrder and ManPowerPlanning data for view tests."""
        # Mock distinct WONos that have data in ManPowerPlanning
        self.mock_mp_values_list.return_value.distinct.return_value = [f'WO{i:03d}' for i in range(1, num_work_orders + 1)]
        
        mock_work_orders = []
        for i in range(1, num_work_orders + 1):
            wo_won_no = f'WO{i:03d}'
            wo_title = f'Project {chr(64 + i)}' # A, B, C...
            
            wo_mock = MagicMock(spec=WorkOrder)
            wo_mock.won_no = wo_won_no
            wo_mock.task_project_title = wo_title
            # Simulate get_budget_summary_data returning a dictionary for each WO
            wo_mock.get_budget_summary_data.return_value = {
                'WONo': wo_won_no, 'CompId': 1, 'ProjectTitle': wo_title, 
                'MDesign': 80.0 + i, 'MAssly': 90.0 + i, 'MCert': 100.0,
                'MTrials': 0.0, 'MIC': 50.0, 'MDisp': 100.0, 'MTryOut': 0.0,
                'DDesign': 50.0, 'DAssly': 100.0, 'DCert': 50.0, 'DTrials': 0.0, 'DIC': 25.0, 'DDisp': 100.0, 'DTryOut': 0.0,
                'HyrLink': reverse('budget_summary_equip_detail', args=[wo_won_no])
            }
            mock_work_orders.append(wo_mock)

        # Configure WorkOrder.objects.filter().order_by to return our mock objects
        self.mock_wo_objects.filter.return_value.order_by.return_value = mock_work_orders
        # Also handle calls without .order_by, just in case
        self.mock_wo_objects.filter.return_value = mock_work_orders

    def test_hrs_budget_summary_list_view_get(self):
        """Test the main list page loads correctly."""
        self._prepare_mock_data_for_views()
        response = self.client.get(reverse('hrs_budget_summary_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_summary/hrs_budget_summary/list.html')
        self.assertContains(response, 'Hours Budget Summary')
        # Check that the HTMX container for the table is present
        self.assertContains(response, 'id="hrsBudgetSummaryTable-container"')
        self.assertContains(response, 'hx-get="'+reverse('hrs_budget_summary_table')+'"')

    def test_hrs_budget_summary_table_partial_view_get_with_data(self):
        """Test the HTMX-loaded table partial with mock data."""
        self._prepare_mock_data_for_views(num_work_orders=2)
        # Simulate an HTMX request by adding the HX-Request header
        response = self.client.get(reverse('hrs_budget_summary_table'), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_summary/hrs_budget_summary/_table.html')
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'Project Alpha')
        self.assertContains(response, 'WO002')
        self.assertContains(response, 'Project B')
        self.assertContains(response, '81.00%') # MDesign for WO001
        self.assertContains(response, '92.00%') # MAssly for WO002
        self.assertContains(response, '$(\'#hrsBudgetSummaryTable\').DataTable') # Check for DataTables initialization script
        self.assertContains(response, 'href="' + reverse('budget_summary_equip_detail', args=['WO001']) + '"')


    def test_hrs_budget_summary_table_partial_view_get_no_data(self):
        """Test the HTMX-loaded table partial when no data is available."""
        self._prepare_mock_data_for_views(num_work_orders=0) # Simulate no work orders
        response = self.client.get(reverse('hrs_budget_summary_table'), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_summary/hrs_budget_summary/_table.html')
        self.assertContains(response, 'No budget summary data available.')
        self.assertNotContains(response, '$(\'#hrsBudgetSummaryTable\').DataTable') # DataTable init should not run on empty table for DataTables.js

    def test_hrs_budget_summary_equip_detail_view(self):
        """Test the drill-down detail view."""
        response = self.client.get(reverse('budget_summary_equip_detail', args=['WO999']))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Work Order Details: WO999')
        self.assertContains(response, 'This page would display detailed equipment budget summary.')

    def test_cancel_button_redirect(self):
        """Test the cancel button's redirection to the menu page."""
        response = self.client.get(reverse('menu_page'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Welcome to the ERP Menu!')
        self.assertContains(response, 'Go back to Budget Summary')
```

### Step 5: HTMX and Alpine.js Integration

-   **HTMX:**
    -   The `list.html` uses `hx-get` on a container `div` to fetch the table content from `{% url 'hrs_budget_summary_table' %}` via HTMX, swapping it into the `innerHTML` on `load`. This makes the page load instantly and then fetches the data dynamically.
    -   The `_table.html` partial contains the actual HTML `table` element along with the DataTables initialization script.
    -   No specific HTMX triggers are needed for CRUD actions on this page, as it's a report display. If you were to implement refresh, `hx-trigger="refreshHrsBudgetSummary from:body"` could be used, but for a one-time report load, `load` is sufficient.

-   **Alpine.js:**
    -   For this specific report display page, complex UI state management or dynamic component interactions beyond what HTMX provides (e.g., modals, tabs, toggles) are not evident in the original ASP.NET code. Therefore, explicit Alpine.js code is not included in the templates, keeping them lean. If future interactive elements are added (e.g., date pickers for filtering, dynamic charts), Alpine.js would be integrated then.

-   **DataTables:**
    -   The `_table.html` partial includes the JavaScript for DataTables initialization (`$('#hrsBudgetSummaryTable').DataTable(...)`). It is crucial to re-initialize DataTables after HTMX loads the new table content. The provided script includes a check (`if ($.fn.DataTable.isDataTable...destroy();`) to prevent re-initialization errors if HTMX somehow loads it multiple times or refreshes.
    -   Standard DataTables features like `pageLength`, `lengthMenu`, `scrollX`, and `responsive` are enabled for a robust user experience, including large datasets and varied screen sizes.

---

### Final Notes

-   **Placeholders:** All `[MODEL_NAME]`, `[FIELD1]`, etc., from your prompt have been replaced with actual names derived from the ASP.NET code (`WorkOrder`, `WONo`, `MDesign`, etc.).
-   **DRY Templates:** The use of `_table.html` as a partial loaded via HTMX ensures that the DataTables specific HTML and JavaScript are only rendered when needed and kept separate from the main page structure.
-   **Fat Model, Thin View:** The `WorkOrder` model now encapsulates all the complex calculation logic (`get_manpower_hours`, `calculate_utilization_percentage`, `get_budget_summary_data`), keeping the `HrsBudgetSummaryListView` concise and focused on orchestrating data delivery rather than computation.
-   **Comprehensive Tests:** The `tests.py` includes unit tests for model methods, ensuring the business logic is sound, and integration tests for views, verifying that pages load correctly and HTMX interactions function as expected. Mocking is used for database interactions to make tests fast and isolated.
-   **Scalability:** This architecture is highly scalable. If the `HrsBudgetSummary_Equip.aspx` page were to be migrated, it would follow a similar pattern, potentially utilizing the same `WorkOrder` model and adding new views and templates.
-   **User Context:** The `CompId` and other session variables (`FinYearId`, `username`) from the ASP.NET code would typically be managed by Django's authentication system or a custom user profile model in a complete ERP system. For this specific migration, the `CompId` is handled where explicitly needed in the `WorkOrder.get_budget_summary_data` method, acknowledging its origin from `ManPowerPlanning` records.