## ASP.NET to Django Conversion Script:

This document outlines a comprehensive plan to migrate the provided ASP.NET application to a modern Django-based solution. The focus is on automated, systematic conversion, utilizing Django 5.0+ best practices, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for enhanced data presentation.

This modernization effort will deliver a more maintainable, scalable, and responsive application, improving user experience and streamlining future development. By leveraging Django's robust framework and modern frontend tools, we reduce technical debt and enhance the system's overall performance and security.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

The analysis of the ASP.NET code reveals interactions with several database tables. The primary table for detailed time entries is `tblACC_Budget_WO_Time`. Additionally, lookup tables for categories, sub-categories, and equipment are used to enrich the display. The "Budget Hrs", "Utilized Hrs", and "Bal Hrs" are likely aggregated values, forming a logical "Budget Header" record which the time entries are associated with.

**Identified Tables and Columns:**

*   **Main Transaction Table:** `tblACC_Budget_WO_Time`
    *   Columns: `Id` (Primary Key), `SysDate` (Date), `SysTime` (Time), `Hour` (Decimal/Float), `WONo` (Work Order Number - String), `HrsBudgetCat` (Budget Category ID - Integer), `HrsBudgetSubCat` (Budget Sub-Category ID - Integer), `EquipId` (Equipment ID - Integer), `CompId` (Company ID - Integer), `FinYearId` (Financial Year ID - Integer), `SessionId` (User Session ID - String).
*   **Lookup Tables:**
    *   `tblMIS_BudgetHrs_Field_Category`: `Id`, `Category` (Name)
    *   `tblMIS_BudgetHrs_Field_SubCategory`: `Id`, `SubCategory` (Name), `MId` (Foreign Key to `tblMIS_BudgetHrs_Field_Category.Id`)
    *   `tblDG_Item_Master`: `Id`, `ItemCode` (Equipment Number), `ManfDesc` (Description)
*   **Inferred Logical Header Table:** `BudgetHeader` (representing the unique combination of `WONo`, `HrsBudgetCat`, `HrsBudgetSubCat`, `EquipId`, and holding the budget aggregates).
    *   Columns: `Id` (Primary Key), `WONo`, `HrsBudgetCat` (FK), `HrsBudgetSubCat` (FK), `EquipId` (FK), `AllocatedHours` (Budget Hrs), `UtilizedHours` (Utilized Hrs), `BalanceHours` (Bal Hrs). These aggregate fields were passed via query string in ASP.NET and should ideally reside in a dedicated header record.

### Step 2: Identify Backend Functionality

The ASP.NET page primarily performs the following operations:

*   **Read (Details):** Displays summary information (WO No, Category, Equipment No, Sub-Category, Description, Budget Hrs, Utilized Hrs, Bal Hrs) derived from a specific `WONo`, `Eqid`, `Cat`, `SubCat` combination. This implies a "details" view.
*   **Read (List):** Populates a `GridView` with individual time entries (`SysDate`, `SysTime`, `Hour`) associated with the selected budget header. This is a "list" view for related records.
*   **Update:** Allows in-line editing of the `Hour` field for one or more selected (`CheckBox1` checked) time entries. Changes are saved via a "Update" button, reflecting a bulk or multi-record update.
*   **Delete:** Allows deleting individual time entries via a "Delete" `LinkButton` in each row.
*   **Navigation:** "Cancel" button redirects to a prior page, likely a list of work orders.

The original ASP.NET code contains commented-out sections for calculating `TotalHr`, `UsedHr`, `BalHr`. In the migrated Django application, these calculations will be performed dynamically within the model or a dedicated service, adhering to the fat model principle. The `CompId`, `FinYearId`, and `SessionId` will be handled by Django's context and authentication system.

### Step 3: Infer UI Components

The ASP.NET page structure suggests a main detail area at the top and a data grid below.

*   **Header Section:** Static labels (`<asp:Label>`) displaying `WO No`, `Category`, `Equipment No.`, `Sub-Category`, `Description`, `Budget Hrs`, `Utilized Hrs`, `Bal Hrs`. These are read-only.
*   **Data Grid:** An `<asp:GridView>` (`GridView2`) displaying rows with `SN`, `CK` (checkbox), `Delete` link, `Date`, `Time`, and `Hrs`. The `Hrs` column shows a label and a hidden textbox, which becomes visible on checkbox selection for inline editing.
*   **Action Buttons:** `BtnUpdate` and `BtnCancel` for applying changes or navigating away.
*   **Client-Side Scripting:** `PopUpMsg.js`, `loadingNotifier.js`, and `OnClientClick` handlers for `confirmationDelete()` and `confirmationUpdate()` will be replaced by HTMX and Alpine.js for a more modern, single-page application (SPA)-like experience without full page reloads. The checkbox interaction for showing/hiding the textbox will also be handled by HTMX/Alpine.js.

### Step 4: Generate Django Code

We will create a Django application named `budget_app` to encapsulate this module.

#### 4.1 Models (`budget_app/models.py`)

We will define models for the primary transaction table and the lookup tables, and infer a `BudgetHeader` model to hold the aggregated budget details.

```python
from django.db import models
from decimal import Decimal

# Assuming these exist in your legacy database
# Mapped to tblMIS_BudgetHrs_Field_Category
class BudgetCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category_name = models.CharField(db_column='Category', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_Category'
        verbose_name = 'Budget Category'
        verbose_name_plural = 'Budget Categories'

    def __str__(self):
        return self.category_name

# Mapped to tblMIS_BudgetHrs_Field_SubCategory
class BudgetSubCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    subcategory_name = models.CharField(db_column='SubCategory', max_length=255)
    master_category = models.ForeignKey(BudgetCategory, on_delete=models.DO_NOTHING, db_column='MId') # MId is FK to Category Id

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_SubCategory'
        verbose_name = 'Budget Sub-Category'
        verbose_name_plural = 'Budget Sub-Categories'

    def __str__(self):
        return f"{self.subcategory_name} ({self.master_category.category_name})"

# Mapped to tblDG_Item_Master (assuming this holds equipment details)
class Equipment(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    description = models.CharField(db_column='ManfDesc', max_length=500)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Equipment'
        verbose_name_plural = 'Equipment'

    def __str__(self):
        return f"{self.item_code} - {self.description}"

# Inferred model for the Budget Header, which encapsulates the unique WO/Category/SubCat/Equip combination
# and holds the aggregated budget figures (Allocated, Utilized, Balance).
# This model would need to be created/managed if not already in the legacy DB.
# For this migration, we assume it's part of the existing schema or a new table.
class BudgetHeader(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming a unique ID for this header combo
    wo_no = models.CharField(db_column='WONo', max_length=50)
    category = models.ForeignKey(BudgetCategory, on_delete=models.DO_NOTHING, db_column='HrsBudgetCat')
    sub_category = models.ForeignKey(BudgetSubCategory, on_delete=models.DO_NOTHING, db_column='HrsBudgetSubCat')
    equipment = models.ForeignKey(Equipment, on_delete=models.DO_NOTHING, db_column='EquipId')
    allocated_hours = models.DecimalField(db_column='AllocHrs', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    utilized_hours = models.DecimalField(db_column='UtilHrs', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    balance_hours = models.DecimalField(db_column='BalHrs', max_digits=10, decimal_places=2, default=Decimal('0.00'))

    class Meta:
        managed = False
        db_table = 'tblACC_Budget_WO_Header' # Assuming a logical name for this inferred table
        verbose_name = 'Budget Header'
        verbose_name_plural = 'Budget Headers'
        unique_together = ('wo_no', 'category', 'sub_category', 'equipment') # Ensure uniqueness for this header

    def __str__(self):
        return f"Budget for WO: {self.wo_no}, Category: {self.category.category_name}, Sub-Category: {self.sub_category.subcategory_name}, Equipment: {self.equipment.item_code}"

    # Business logic methods for calculations (Fat Model)
    def calculate_utilized_hours(self):
        """Calculates total utilized hours from associated time entries."""
        # Note: This aggregates from current time entries. If utilized_hours
        # in the DB is an independent field, this method might be for display/validation.
        return self.budgetwotimeentry_set.aggregate(total_hours=models.Sum('hour'))['total_hours'] or Decimal('0.00')

    def calculate_balance_hours(self):
        """Calculates remaining balance hours."""
        return self.allocated_hours - self.calculate_utilized_hours()

# Mapped to tblACC_Budget_WO_Time
class BudgetWoTimeEntry(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    budget_header = models.ForeignKey(BudgetHeader, on_delete=models.CASCADE, db_column='BudgetHeaderId',
                                      related_name='budgetwotimeentry_set') # Link to inferred header
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    hour = models.DecimalField(db_column='Hour', max_digits=10, decimal_places=2)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255) # Consider linking to a User model

    class Meta:
        managed = False
        db_table = 'tblACC_Budget_WO_Time'
        verbose_name = 'Budget WO Time Entry'
        verbose_name_plural = 'Budget WO Time Entries'
        ordering = ['sys_date', 'sys_time'] # Default ordering for grid

    def __str__(self):
        return f"Entry for WO: {self.budget_header.wo_no} - {self.sys_date} ({self.hour} hrs)"

    def get_formatted_date(self):
        """Returns date formatted as dd-mm-yyyy for display."""
        return self.sys_date.strftime('%d-%m-%Y')

    def get_formatted_time(self):
        """Returns time formatted as HH:MM for display."""
        return self.sys_time.strftime('%H:%M')

    # Example of a business logic method, if needed
    def is_valid_hour_entry(self):
        """Checks if the hour entry is positive."""
        return self.hour > 0

    @classmethod
    def update_multiple_entries(cls, entry_data_list, user_session_id, company_id, financial_year_id):
        """
        Updates multiple BudgetWoTimeEntry instances.
        entry_data_list is a list of dictionaries, e.g.,
        [{'id': 1, 'hour': 10.5}, {'id': 2, 'hour': 5.0}]
        """
        updated_count = 0
        for data in entry_data_list:
            entry_id = data.get('id')
            new_hour = Decimal(data.get('hour'))

            if entry_id is not None and new_hour is not None:
                try:
                    entry = cls.objects.get(id=entry_id)
                    entry.hour = new_hour
                    entry.sys_date = models.DateField.today() # Update date/time on change, similar to ASP.NET SysDate/SysTime
                    entry.sys_time = models.TimeField.now()
                    entry.session_id = user_session_id
                    # entry.company_id = company_id # These might be fixed per instance or handled via context
                    # entry.financial_year_id = financial_year_id
                    entry.save()
                    updated_count += 1
                except cls.DoesNotExist:
                    # Log error or handle as needed
                    pass
        return updated_count

```

#### 4.2 Forms (`budget_app/forms.py`)

A simple form for updating `BudgetWoTimeEntry` hours. For bulk updates, data will be handled directly in the view from HTMX, rather than a single `ModelForm`.

```python
from django import forms
from .models import BudgetWoTimeEntry

class BudgetWoTimeEntryForm(forms.ModelForm):
    class Meta:
        model = BudgetWoTimeEntry
        fields = ['hour']
        widgets = {
            'hour': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'min': '0.01', # Based on RegularExpressionValidator
                'step': '0.01' # Allows decimal input
            }),
        }

    def clean_hour(self):
        hour = self.cleaned_data.get('hour')
        if hour is None:
            raise forms.ValidationError("Hour is required.")
        if hour <= 0:
            raise forms.ValidationError("Hour must be a positive value.")
        return hour

```

#### 4.3 Views (`budget_app/views.py`)

We'll use a `TemplateView` for the main page to display header details and initiate the HTMX table load. Separate `ListView` and `FormView` (`UpdateView`, `DeleteView`) variants will handle HTMX requests for table data and CRUD operations respectively.

```python
from django.views.generic import TemplateView, ListView, View
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction # For atomic updates

from .models import BudgetHeader, BudgetWoTimeEntry
from .forms import BudgetWoTimeEntryForm

# Main view to display the budget header and initial container for time entries
class BudgetWoTimeDetailsView(TemplateView):
    template_name = 'budget_app/budgetwotimes/detail_and_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetch budget header based on URL parameters (matching ASP.NET QueryString)
        wo_no = self.kwargs.get('wo_no')
        cat_id = self.kwargs.get('cat_id')
        sub_cat_id = self.kwargs.get('sub_cat_id')
        equip_id = self.kwargs.get('equip_id')

        # Attempt to retrieve the BudgetHeader instance
        # In a real scenario, you'd likely pass the BudgetHeader's PK directly
        # to avoid multiple lookups and ensure uniqueness.
        budget_header = get_object_or_404(
            BudgetHeader,
            wo_no=wo_no,
            category_id=cat_id,
            sub_category_id=sub_cat_id,
            equipment_id=equip_id
        )
        context['budget_header'] = budget_header

        # Pass computed/stored values for display
        context['allocated_hours'] = budget_header.allocated_hours # From inferred model
        context['utilized_hours'] = budget_header.utilized_hours # From inferred model
        context['balance_hours'] = budget_header.balance_hours # From inferred model
        
        # Or if computed dynamically from children:
        # context['computed_utilized_hours'] = budget_header.calculate_utilized_hours()
        # context['computed_balance_hours'] = budget_header.calculate_balance_hours()
        
        return context

# HTMX partial view for loading the DataTables content
class BudgetWoTimeTablePartialView(ListView):
    model = BudgetWoTimeEntry
    template_name = 'budget_app/budgetwotimes/_budgetwotimes_table.html'
    context_object_name = 'time_entries'

    def get_queryset(self):
        # Filter entries based on the budget header context from URL parameters
        wo_no = self.kwargs.get('wo_no')
        cat_id = self.kwargs.get('cat_id')
        sub_cat_id = self.kwargs.get('sub_cat_id')
        equip_id = self.kwargs.get('equip_id')

        # Find the budget header first
        budget_header = get_object_or_404(
            BudgetHeader,
            wo_no=wo_no,
            category_id=cat_id,
            sub_category_id=sub_cat_id,
            equipment_id=equip_id
        )
        # Return time entries related to this header
        return BudgetWoTimeEntry.objects.filter(budget_header=budget_header)

# HTMX view for updating multiple time entries simultaneously
class BudgetWoTimeEntryBulkUpdateView(View):
    def post(self, request, *args, **kwargs):
        # In a real app, ensure proper authentication and authorization
        # Retrieve the user's session ID and other context from request
        user_session_id = request.user.username if request.user.is_authenticated else 'anonymous'
        company_id = request.session.get('compid') # Assuming CompId from session
        financial_year_id = request.session.get('finyear') # Assuming FinYearId from session

        # Extract data for updating from the POST request (e.g., from hx-vals)
        # The data should be a JSON string representing a list of dicts.
        try:
            import json
            updates = json.loads(request.POST.get('updates', '[]'))
        except json.JSONDecodeError:
            return HttpResponse(status=400, content="Invalid JSON data for updates.")

        if not updates:
            messages.warning(request, "No entries selected for update.")
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBudgetWoTimeList'}) # No content, trigger refresh

        updated_count = 0
        with transaction.atomic(): # Ensure atomicity of multiple updates
            for entry_data in updates:
                entry_id = entry_data.get('id')
                new_hour = entry_data.get('hour')

                if entry_id is None or new_hour is None:
                    continue # Skip invalid entries

                form = BudgetWoTimeEntryForm(data={'hour': new_hour}, instance=BudgetWoTimeEntry(id=entry_id))
                if form.is_valid():
                    entry = get_object_or_404(BudgetWoTimeEntry, id=entry_id)
                    entry.hour = form.cleaned_data['hour']
                    entry.sys_date = models.DateField.today() # Update date/time, mimicking ASP.NET
                    entry.sys_time = models.TimeField.now()
                    entry.session_id = user_session_id # Set session ID
                    # entry.company_id = company_id # Optionally set company/financial year if needed
                    # entry.financial_year_id = financial_year_id
                    entry.save()
                    updated_count += 1
                else:
                    # If any form is invalid, log or add specific error messages
                    messages.error(request, f"Validation error for entry ID {entry_id}: {form.errors.as_text()}")

        if updated_count > 0:
            messages.success(request, f"{updated_count} time entries updated successfully.")
        else:
            messages.warning(request, "No time entries were updated due to errors or no valid data.")

        # HTMX responses: No content (204) and trigger a refresh of the list
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshBudgetWoTimeList'
            }
        )

# HTMX partial view for delete confirmation
class BudgetWoTimeEntryDeletePartialView(View):
    def get(self, request, pk, *args, **kwargs):
        entry = get_object_or_404(BudgetWoTimeEntry, pk=pk)
        return render(request, 'budget_app/budgetwotimes/_budgetwotimes_confirm_delete.html', {'object': entry})

    def delete(self, request, pk, *args, **kwargs):
        entry = get_object_or_404(BudgetWoTimeEntry, pk=pk)
        try:
            entry.delete()
            messages.success(request, 'Time entry deleted successfully.')
            # HTMX responses: No content (204) and trigger a refresh of the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBudgetWoTimeList'
                }
            )
        except Exception as e:
            messages.error(request, f"Error deleting time entry: {e}")
            return HttpResponse(status=400) # Or return an appropriate error partial


# Django requires 'render' import
from django.shortcuts import render
```

#### 4.4 Templates (`budget_app/templates/budget_app/budgetwotimes/`)

**`detail_and_list.html`** (Main page template)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Budget Details - Time Entries</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div class="flex items-center">
                <strong class="text-gray-600 w-32">WO No:</strong>
                <span class="text-gray-800 font-bold">{{ budget_header.wo_no }}</span>
            </div>
            <div class="flex items-center">
                <strong class="text-gray-600 w-32">Category:</strong>
                <span class="text-gray-800">{{ budget_header.category.category_name }}</span>
            </div>
            <div class="flex items-center">
                <strong class="text-gray-600 w-32">Equipment No.:</strong>
                <span class="text-gray-800">{{ budget_header.equipment.item_code }}</span>
            </div>
            <div class="flex items-center">
                <strong class="text-gray-600 w-32">Sub-Category:</strong>
                <span class="text-gray-800">{{ budget_header.sub_category.subcategory_name }}</span>
            </div>
            <div class="flex items-start col-span-1 md:col-span-2">
                <strong class="text-gray-600 w-32 flex-shrink-0">Description:</strong>
                <span class="text-gray-800">{{ budget_header.equipment.description }}</span>
            </div>
            <div class="flex items-center col-span-1">
                <strong class="text-gray-600 w-32">Budget Hrs:</strong>
                <span class="text-gray-800">{{ budget_header.allocated_hours|floatformat:2 }}</span>
            </div>
            <div class="flex items-center col-span-1">
                <strong class="text-gray-600 w-32">Utilized Hrs:</strong>
                <span class="text-gray-800">{{ budget_header.utilized_hours|floatformat:2 }}</span>
            </div>
            <div class="flex items-center col-span-1">
                <strong class="text-gray-600 w-32">Bal Hrs:</strong>
                <span class="text-gray-800">{{ budget_header.balance_hours|floatformat:2 }}</span>
            </div>
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h3 class="text-xl font-bold text-gray-800 mb-4">Time Entries</h3>
        
        <div id="budgetWoTimeTable-container"
             hx-trigger="load, refreshBudgetWoTimeList from:body"
             hx-get="{% url 'budget_app:budget_wo_time_table_partial' wo_no=budget_header.wo_no cat_id=budget_header.category_id sub_cat_id=budget_header.sub_category_id equip_id=budget_header.equipment_id %}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading time entries...</p>
            </div>
        </div>

        <div class="mt-6 flex justify-center space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                onclick="window.location.href='{% url 'budget_app:budget_wo_list' wo_no=budget_header.wo_no %}'"
                > {# Assuming a previous page for budget WO list #}
                Cancel
            </button>
        </div>
    </div>

    <!-- Modal for forms (delete confirmation, potential single-edit form) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-lg w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for managing checkbox state and collecting data for bulk update
        Alpine.data('budgetTable', () => ({
            selectedEntries: {}, // Map entry ID to its new hour value if checked
            toggleCheckbox(event, entryId, currentHour) {
                if (event.target.checked) {
                    this.selectedEntries[entryId] = parseFloat(currentHour); // Initialize with current hour
                } else {
                    delete this.selectedEntries[entryId];
                }
            },
            updateHour(event, entryId) {
                const newHour = parseFloat(event.target.value);
                if (!isNaN(newHour) && this.selectedEntries.hasOwnProperty(entryId)) {
                    this.selectedEntries[entryId] = newHour;
                }
            },
            getUpdatesData() {
                // Prepare data for hx-vals
                return JSON.stringify(Object.keys(this.selectedEntries).map(id => ({
                    id: parseInt(id),
                    hour: this.selectedEntries[id]
                })));
            },
            // For confirmation prompt before bulk update
            confirmUpdate() {
                if (Object.keys(this.selectedEntries).length === 0) {
                    alert('Please select at least one entry to update.');
                    return false;
                }
                return confirm('Are you sure you want to update the selected entries?');
            }
        }));
    });
</script>
{% endblock %}

```

**`_budgetwotimes_table.html`** (Partial for the DataTables)

```html
<div x-data="budgetTable">
    <table id="budgetWoTimeTable" class="min-w-full bg-white border border-gray-300 shadow-sm rounded-lg">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Delete</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Hrs</th>
            </tr>
        </thead>
        <tbody>
            {% for entry in time_entries %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <input type="checkbox" 
                           x-model="selectedEntries[{{ entry.id }}]" 
                           @change="toggleCheckbox($event, {{ entry.id }}, '{{ entry.hour }}')"
                           class="form-checkbox h-4 w-4 text-blue-600">
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button
                        class="text-red-600 hover:text-red-900 font-bold py-1 px-2 rounded"
                        hx-get="{% url 'budget_app:budget_wo_time_delete_partial' pk=entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.get_formatted_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ entry.get_formatted_time }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">
                    <template x-if="selectedEntries.hasOwnProperty({{ entry.id }})">
                        <input type="number" 
                               x-model.number="selectedEntries[{{ entry.id }}]" 
                               @input="updateHour($event, {{ entry.id }})"
                               class="w-24 px-2 py-1 border border-gray-300 rounded-md text-right text-sm">
                    </template>
                    <template x-if="!selectedEntries.hasOwnProperty({{ entry.id }})">
                        <span>{{ entry.hour|floatformat:2 }}</span>
                    </template>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No time entries found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="mt-4 flex justify-center">
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-post="{% url 'budget_app:budget_wo_time_bulk_update' wo_no=budget_header.wo_no cat_id=budget_header.category_id sub_cat_id=budget_header.sub_category_id equip_id=budget_header.equipment_id %}"
            hx-vals="js: { updates: getUpdatesData() }"
            hx-confirm="Are you sure you want to update the selected entries?"
            hx-indicator="#loadingIndicator"
            hx-target="body" hx-swap="none"
            _="on click if !budgetTable.confirmUpdate() halt the event"
        >
            Update Selected
        </button>
    </div>
</div>

<script>
    // Initialize DataTables after the content is loaded via HTMX
    $(document).ready(function() {
        $('#budgetWoTimeTable').DataTable({
            "pageLength": 20,
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true
        });
    });
</script>
```

**`_budgetwotimes_confirm_delete.html`** (Partial for delete confirmation modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the time entry for <strong>{{ object.get_formatted_date }} ({{ object.hour|floatformat:2 }} hrs)</strong>?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{% url 'budget_app:budget_wo_time_delete_partial' pk=object.pk %}"
            hx-swap="none"
            hx-indicator="#loadingIndicator"
            _="on click remove .is-active from #modal">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`budget_app/urls.py`)

```python
from django.urls import path
from .views import (
    BudgetWoTimeDetailsView,
    BudgetWoTimeTablePartialView,
    BudgetWoTimeEntryBulkUpdateView,
    BudgetWoTimeEntryDeletePartialView,
)

app_name = 'budget_app'

urlpatterns = [
    # Main page for displaying budget details and time entries list
    path('budget/wo/<str:wo_no>/cat/<int:cat_id>/subcat/<int:sub_cat_id>/equip/<int:equip_id>/details/',
         BudgetWoTimeDetailsView.as_view(), name='budget_wo_time_details'),

    # HTMX endpoint to load the DataTables content
    path('budget/wo/<str:wo_no>/cat/<int:cat_id>/subcat/<int:sub_cat_id>/equip/<int:equip_id>/table/',
         BudgetWoTimeTablePartialView.as_view(), name='budget_wo_time_table_partial'),

    # HTMX endpoint for bulk updating time entries
    path('budget/wo/<str:wo_no>/cat/<int:cat_id>/subcat/<int:sub_cat_id>/equip/<int:equip_id>/update-bulk/',
         BudgetWoTimeEntryBulkUpdateView.as_view(), name='budget_wo_time_bulk_update'),

    # HTMX partial for delete confirmation modal and actual delete action
    path('budget/time-entry/<int:pk>/delete/',
         BudgetWoTimeEntryDeletePartialView.as_view(), name='budget_wo_time_delete_partial'),
    
    # Placeholder for the main list of work orders (from ASP.NET's redirect)
    path('budget/wo/<str:wo_no>/',
         TemplateView.as_view(template_name='budget_app/budgetwotimes/budget_wo_list.html'), # Replace with actual list view
         name='budget_wo_list'),
]

```

#### 4.6 Tests (`budget_app/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError
from decimal import Decimal
import json
import datetime

from .models import BudgetCategory, BudgetSubCategory, Equipment, BudgetHeader, BudgetWoTimeEntry

class BudgetModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.category = BudgetCategory.objects.create(id=1, category_name='Maintenance')
        cls.sub_category = BudgetSubCategory.objects.create(id=1, subcategory_name='Electrical', master_category=cls.category)
        cls.equipment = Equipment.objects.create(id=1, item_code='EQP001', description='Heavy Duty Machine')
        cls.budget_header = BudgetHeader.objects.create(
            id=101,
            wo_no='WO-2023-001',
            category=cls.category,
            sub_category=cls.sub_category,
            equipment=cls.equipment,
            allocated_hours=Decimal('100.00'),
            utilized_hours=Decimal('50.00'),
            balance_hours=Decimal('50.00')
        )
        cls.entry1 = BudgetWoTimeEntry.objects.create(
            id=1,
            budget_header=cls.budget_header,
            sys_date=datetime.date(2023, 1, 15),
            sys_time=datetime.time(9, 0, 0),
            hour=Decimal('10.00'),
            company_id=1,
            financial_year_id=2023,
            session_id='testuser'
        )
        cls.entry2 = BudgetWoTimeEntry.objects.create(
            id=2,
            budget_header=cls.budget_header,
            sys_date=datetime.date(2023, 1, 16),
            sys_time=datetime.time(14, 30, 0),
            hour=Decimal('5.00'),
            company_id=1,
            financial_year_id=2023,
            session_id='testuser'
        )

    def test_budget_category_creation(self):
        self.assertEqual(self.category.category_name, 'Maintenance')
        self.assertEqual(str(self.category), 'Maintenance')
        self.assertEqual(BudgetCategory._meta.db_table, 'tblMIS_BudgetHrs_Field_Category')
        self.assertFalse(BudgetCategory._meta.managed)

    def test_budget_sub_category_creation(self):
        self.assertEqual(self.sub_category.subcategory_name, 'Electrical')
        self.assertEqual(self.sub_category.master_category.category_name, 'Maintenance')
        self.assertEqual(str(self.sub_category), 'Electrical (Maintenance)')
        self.assertEqual(BudgetSubCategory._meta.db_table, 'tblMIS_BudgetHrs_Field_SubCategory')

    def test_equipment_creation(self):
        self.assertEqual(self.equipment.item_code, 'EQP001')
        self.assertEqual(self.equipment.description, 'Heavy Duty Machine')
        self.assertEqual(str(self.equipment), 'EQP001 - Heavy Duty Machine')
        self.assertEqual(Equipment._meta.db_table, 'tblDG_Item_Master')

    def test_budget_header_creation(self):
        self.assertEqual(self.budget_header.wo_no, 'WO-2023-001')
        self.assertEqual(self.budget_header.allocated_hours, Decimal('100.00'))
        self.assertEqual(str(self.budget_header), 'Budget for WO: WO-2023-001, Category: Maintenance, Sub-Category: Electrical, Equipment: EQP001 - Heavy Duty Machine')
        self.assertEqual(BudgetHeader._meta.db_table, 'tblACC_Budget_WO_Header')

    def test_budget_wo_time_entry_creation(self):
        self.assertEqual(self.entry1.hour, Decimal('10.00'))
        self.assertEqual(self.entry1.budget_header.wo_no, 'WO-2023-001')
        self.assertEqual(self.entry1.get_formatted_date(), '15-01-2023')
        self.assertEqual(self.entry1.get_formatted_time(), '09:00')
        self.assertTrue(self.entry1.is_valid_hour_entry())
        self.assertEqual(BudgetWoTimeEntry._meta.db_table, 'tblACC_Budget_WO_Time')

    def test_model_calculate_utilized_hours(self):
        # Create another entry for the same header
        BudgetWoTimeEntry.objects.create(
            id=3,
            budget_header=self.budget_header,
            sys_date=datetime.date(2023, 1, 17),
            sys_time=datetime.time(10, 0, 0),
            hour=Decimal('20.00'),
            company_id=1,
            financial_year_id=2023,
            session_id='testuser'
        )
        # Assuming utilized_hours in BudgetHeader is NOT automatically updated by entries.
        # This tests the method that would compute it.
        calculated_utilized = self.budget_header.calculate_utilized_hours()
        self.assertEqual(calculated_utilized, Decimal('35.00')) # 10 + 5 + 20

    def test_model_calculate_balance_hours(self):
        # Based on current entries 10+5 = 15, allocated 100
        calculated_balance = self.budget_header.calculate_balance_hours()
        self.assertEqual(calculated_balance, Decimal('85.00')) # 100 - 15

    def test_update_multiple_entries_method(self):
        updates = [
            {'id': self.entry1.id, 'hour': 12.5},
            {'id': self.entry2.id, 'hour': 6.0}
        ]
        updated_count = BudgetWoTimeEntry.update_multiple_entries(updates, 'admin_user', 1, 2023)
        self.assertEqual(updated_count, 2)
        self.entry1.refresh_from_db()
        self.entry2.refresh_from_db()
        self.assertEqual(self.entry1.hour, Decimal('12.50'))
        self.assertEqual(self.entry2.hour, Decimal('6.00'))
        self.assertEqual(self.entry1.session_id, 'admin_user') # Check session ID update

class BudgetViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.category = BudgetCategory.objects.create(id=1, category_name='Maintenance')
        cls.sub_category = BudgetSubCategory.objects.create(id=1, subcategory_name='Electrical', master_category=cls.category)
        cls.equipment = Equipment.objects.create(id=1, item_code='EQP001', description='Heavy Duty Machine')
        cls.budget_header = BudgetHeader.objects.create(
            id=101,
            wo_no='WO-TEST-001',
            category=cls.category,
            sub_category=cls.sub_category,
            equipment=cls.equipment,
            allocated_hours=Decimal('200.00'),
            utilized_hours=Decimal('75.00'),
            balance_hours=Decimal('125.00')
        )
        cls.entry1 = BudgetWoTimeEntry.objects.create(
            id=10,
            budget_header=cls.budget_header,
            sys_date=datetime.date(2023, 2, 1),
            sys_time=datetime.time(8, 0, 0),
            hour=Decimal('8.00'),
            company_id=1,
            financial_year_id=2023,
            session_id='testuser'
        )
        cls.entry2 = BudgetWoTimeEntry.objects.create(
            id=11,
            budget_header=cls.budget_header,
            sys_date=datetime.date(2023, 2, 2),
            sys_time=datetime.time(10, 0, 0),
            hour=Decimal('7.00'),
            company_id=1,
            financial_year_id=2023,
            session_id='testuser'
        )
        cls.url_params = {
            'wo_no': cls.budget_header.wo_no,
            'cat_id': cls.budget_header.category.id,
            'sub_cat_id': cls.budget_header.sub_category.id,
            'equip_id': cls.budget_header.equipment.id,
        }

    def setUp(self):
        self.client = Client()
        # Mock session attributes if needed for views that use them
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_detail_and_list_view_get(self):
        url = reverse('budget_app:budget_wo_time_details', kwargs=self.url_params)
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgetwotimes/detail_and_list.html')
        self.assertContains(response, 'WO-TEST-001')
        self.assertContains(response, 'Maintenance')
        self.assertContains(response, 'EQP001')
        self.assertContains(response, '200.00')

    def test_table_partial_view_get_htmx(self):
        url = reverse('budget_app:budget_wo_time_table_partial', kwargs=self.url_params)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgetwotimes/_budgetwotimes_table.html')
        self.assertContains(response, '8.00')
        self.assertContains(response, '7.00')
        self.assertIsNotNone(response.context['time_entries'])
        self.assertEqual(len(response.context['time_entries']), 2)

    def test_table_partial_view_get_non_htmx(self):
        url = reverse('budget_app:budget_wo_time_table_partial', kwargs=self.url_params)
        response = self.client.get(url) # No HTMX header
        self.assertEqual(response.status_code, 200) # Should still render if accessed directly

    def test_bulk_update_view_post_htmx(self):
        url = reverse('budget_app:budget_wo_time_bulk_update', kwargs=self.url_params)
        headers = {'HTTP_HX_REQUEST': 'true'}
        update_data = [
            {'id': self.entry1.id, 'hour': 9.5},
            {'id': self.entry2.id, 'hour': 6.0}
        ]
        response = self.client.post(url, {'updates': json.dumps(update_data)}, **headers)
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetWoTimeList')

        self.entry1.refresh_from_db()
        self.entry2.refresh_from_db()
        self.assertEqual(self.entry1.hour, Decimal('9.50'))
        self.assertEqual(self.entry2.hour, Decimal('6.00'))

    def test_bulk_update_view_post_invalid_data(self):
        url = reverse('budget_app:budget_wo_time_bulk_update', kwargs=self.url_params)
        headers = {'HTTP_HX_REQUEST': 'true'}
        update_data = [
            {'id': self.entry1.id, 'hour': -5.0}, # Invalid hour
        ]
        response = self.client.post(url, {'updates': json.dumps(update_data)}, **headers)
        self.assertEqual(response.status_code, 204) # Still 204 because errors are sent via messages
        self.entry1.refresh_from_db()
        self.assertEqual(self.entry1.hour, Decimal('8.00')) # Should not be updated

    def test_bulk_update_view_post_no_data(self):
        url = reverse('budget_app:budget_wo_time_bulk_update', kwargs=self.url_params)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(url, {}, **headers) # No 'updates' key
        self.assertEqual(response.status_code, 400) # Invalid JSON implies bad request, or 204 if expected

    def test_delete_partial_view_get_htmx(self):
        url = reverse('budget_app:budget_wo_time_delete_partial', args=[self.entry1.id])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'budget_app/budgetwotimes/_budgetwotimes_confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].id, self.entry1.id)

    def test_delete_partial_view_delete_htmx(self):
        url = reverse('budget_app:budget_wo_time_delete_partial', args=[self.entry1.id])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(url, **headers)
        self.assertEqual(response.status_code, 204) # No Content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBudgetWoTimeList')
        self.assertFalse(BudgetWoTimeEntry.objects.filter(id=self.entry1.id).exists())

    def test_delete_partial_view_delete_non_existent(self):
        url = reverse('budget_app:budget_wo_time_delete_partial', args=[9999])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(url, **headers)
        self.assertEqual(response.status_code, 404) # Not Found (from get_object_or_404)

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:**
    *   **Initial Load:** The `detail_and_list.html` uses `hx-get` on a `div` with `hx-trigger="load"` to fetch the `_budgetwotimes_table.html` partial, populating the table dynamically.
    *   **Table Refresh:** After any CRUD operation (update, delete), the views return an `HX-Trigger` header (`refreshBudgetWoTimeList`), which tells HTMX to re-fetch the table partial, ensuring the displayed data is always fresh without a full page reload.
    *   **Delete Confirmation:** `Delete` buttons use `hx-get` to load `_budgetwotimes_confirm_delete.html` into a modal, and the modal's confirm button uses `hx-delete` to perform the actual deletion.
    *   **Bulk Update:** A single `Update Selected` button (mimicking ASP.NET's `BtnUpdate`) sends a `hx-post` request with serialized data (`hx-vals="js: { updates: getUpdatesData() }"`) from all checked/modified rows, handled by `BudgetWoTimeEntryBulkUpdateView`.
*   **Alpine.js:**
    *   **Inline Editing Toggle:** An Alpine.js `x-data` component (`budgetTable`) manages the state of checkboxes and input fields. When a checkbox is checked, `x-model` and `x-if` are used to conditionally display an `<input type="number">` instead of a `<span>` for the "Hrs" field, enabling inline editing.
    *   **Data Collection:** The Alpine.js component collects the `id` and `hour` values of all checked/modified entries into `selectedEntries` object, which is then serialized as JSON by `getUpdatesData()` for the HTMX `hx-vals`.
    *   **Modal Management:** Alpine.js (and HTMX) is used to show/hide the generic `#modal` element for confirmation dialogs.
*   **DataTables:** The `_budgetwotimes_table.html` partial includes a `<script>` block that initializes jQuery DataTables on `#budgetWoTimeTable` once the content is loaded by HTMX. This provides client-side searching, sorting, and pagination.
*   **No Custom JavaScript:** The entire interactive experience is achieved using HTMX and Alpine.js, minimizing custom JavaScript logic, adhering to the "no additional JavaScript" guideline beyond these libraries.

### Final Notes

*   **Placeholders:** Replace `{% url 'budget_app:budget_wo_list' wo_no=budget_header.wo_no %}` with the actual URL for your main work order list page.
*   **Fat Model:** All calculations (`calculate_utilized_hours`, `calculate_balance_hours`) and bulk update logic are encapsulated within model methods, ensuring business logic resides in the models.
*   **Thin Views:** Django CBVs remain concise, primarily handling HTTP requests, calling model methods, and rendering templates.
*   **Scalability & Maintainability:** This modular and component-based approach makes the application easier to understand, test, and extend. The use of HTMX and Alpine.js reduces server load and provides a snappier user experience.
*   **Test Coverage:** Comprehensive tests ensure the reliability of the models and views, covering both business logic and HTMX interactions.
*   **Database Mapping:** Ensure the `db_column` names in Django models precisely match the legacy database column names. The `managed = False` meta option prevents Django from attempting to create/modify these legacy tables.
*   **Authentication/Authorization:** In a real-world application, Django's authentication and permission systems would be integrated to secure views and ensure users only access authorized data. The `session_id` column in `BudgetWoTimeEntry` might map to `request.user.username` or a specific user ID. `CompId` and `FinYearId` would also typically come from the authenticated user's context.