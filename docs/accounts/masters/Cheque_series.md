## ASP.NET to Django Modernization Plan for Cheque Series Management

This document outlines a strategic plan to transition your legacy ASP.NET Cheque Series management module to a modern, efficient Django-based system. Our approach prioritizes automation, streamlined processes, and a user-friendly experience, ensuring a smooth and impactful modernization.

### Understanding the Business Value of This Migration

Migrating your Cheque Series module to Django brings significant benefits:

1.  **Reduced Maintenance Costs:** Legacy ASP.NET systems often incur high maintenance costs due to outdated technologies, complex dependencies, and a shrinking pool of developers. Django's modern architecture and active community ensure easier, more cost-effective maintenance.
2.  **Improved Scalability and Performance:** Django is designed for high-traffic applications and can easily scale to meet growing business demands. Modern frontend technologies like HTMX and DataTables deliver a snappier, more responsive user experience without constant page reloads.
3.  **Enhanced Security:** Django includes built-in security features that protect against common web vulnerabilities, making your application more robust and secure.
4.  **Faster Feature Development:** Django's "batteries included" philosophy, combined with its strong ORM and robust ecosystem, allows for rapid development and deployment of new features, enabling your business to adapt quickly to changing requirements.
5.  **Future-Proofing:** Moving to a widely adopted and actively maintained framework like Django, combined with a modern frontend stack (HTMX, Alpine.js), ensures your application remains relevant and capable of leveraging future technological advancements.

This plan focuses on a systematic, automated conversion process, providing clear, actionable steps that can be overseen by non-technical stakeholders, ensuring a successful transition with minimal disruption.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET `SqlDataSource` and C# code-behind, we identify two key tables:

*   **`tblACC_ChequeNo`**: This is the primary table for managing cheque series.
    *   `Id` (Primary Key, Integer)
    *   `BankId` (Foreign Key, Integer, linking to `tblACC_Bank`)
    *   `StartNo` (Integer)
    *   `EndNo` (Integer)

*   **`tblACC_Bank`**: This table provides the list of banks for the `BankId` dropdown.
    *   `Id` (Primary Key, Integer)
    *   `Name` (String/Varchar)

### Step 2: Identify Backend Functionality

**Task:** Determine the Create, Read, Update, and Delete (CRUD) operations present in the ASP.NET code.

**Instructions:**
The ASP.NET application provides a complete set of CRUD functionalities:

*   **Create (Add New Cheque Series):**
    *   Triggered by "Insert" button in `GridView` footer (`CommandName="Add"`) or in `EmptyDataTemplate` (`CommandName="Add1"`).
    *   Reads `BankId`, `StartNo`, `EndNo` from respective controls.
    *   Inserts a new record into `tblACC_ChequeNo`.
    *   **Validation:** Basic check for non-empty `StartNo` and `EndNo` values using `RequiredFieldValidator`.

*   **Read (Display Cheque Series List):**
    *   Performed on `Page_Load` via `FillGrid()` method.
    *   Uses a stored procedure `[GetChequeNo]` (inferred to select Id, BankId, StartNo, EndNo, and Bank Name).
    *   Data is bound to the `GridView1`.
    *   Includes pagination and sorting capabilities (managed by `GridView` by default, will be DataTables in Django).

*   **Update (Edit Existing Cheque Series):**
    *   Triggered by the "Edit" link in the `GridView` row.
    *   The `GridView` enters edit mode, allowing modification of `BankId`, `StartNo`, `EndNo`.
    *   `GridView1_RowUpdating` event handles saving changes, updating the record in `tblACC_ChequeNo` based on `Id`.
    *   **Validation:** Similar non-empty check for `StartNo` and `EndNo`.

*   **Delete (Remove Cheque Series):**
    *   Triggered by the "Delete" link in the `GridView` row.
    *   `GridView1_RowDeleting` event handles removal, deleting the record from `tblACC_ChequeNo` based on `Id`.
    *   Client-side confirmation pop-up is used.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, mapping them to modern web components.

**Instructions:**
The ASP.NET UI primarily uses a `GridView` for data presentation and inline editing/adding, along with standard input controls:

*   **Main Data Display:** `asp:GridView` (ID: `GridView1`). This will be replaced by an HTML `<table>` integrated with **DataTables** for robust client-side searching, sorting, and pagination.
*   **Data Fields:**
    *   "Bank Name": `asp:Label` (display), `asp:DropDownList` (edit/add). This will be a Django `ModelChoiceField` rendered as an HTML `<select>`.
    *   "Start No": `asp:Label` (display), `asp:TextBox` (edit/add). This will be a Django `IntegerField` rendered as an HTML `<input type="number">`.
    *   "End No": `asp:Label` (display), `asp:TextBox` (edit/add). This will be a Django `IntegerField` rendered as an HTML `<input type="number">`.
*   **Action Buttons/Links:**
    *   "Edit" and "Delete" `asp:CommandField` buttons. These will become standard HTML `<button>` elements with **HTMX** attributes to trigger modals for editing and confirmation.
    *   "Insert" `asp:Button` in footer/empty template. This will be a single "Add New" button in the Django list view, triggering a modal with the creation form via HTMX.
*   **Client-side Interactions:** `PopUpMsg.js` for confirmation dialogs. These will be replaced by **HTMX** for dynamic modal loading and **Alpine.js** for simple UI state management (e.g., showing/hiding modals).
*   **Styling:** `yui-datatable.css`, `StyleSheet.css`. These will be replaced by **Tailwind CSS** classes.

---

### Step 4: Generate Django Code

We will create a new Django application, for example, `accounts`, to house this module.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema. We'll map directly to existing tables to preserve data.

**Instructions:**
The `Bank` and `ChequeSeries` models will be created. We use `managed = False` to tell Django not to manage the table schema, assuming these tables already exist in your database.

**`accounts/models.py`**

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class Bank(models.Model):
    """
    Represents a bank in the system.
    Maps to the existing tblACC_Bank table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, unique=True, verbose_name="Bank Name")

    class Meta:
        managed = False  # Django will not create or delete this table
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank'
        verbose_name_plural = 'Banks'
        ordering = ['name']

    def __str__(self):
        return self.name

class ChequeSeries(models.Model):
    """
    Manages cheque series for different banks.
    Maps to the existing tblACC_ChequeNo table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    bank = models.ForeignKey(Bank, models.DO_NOTHING, db_column='BankId', verbose_name="Bank Name")
    start_no = models.IntegerField(db_column='StartNo', verbose_name="Start No")
    end_no = models.IntegerField(db_column='EndNo', verbose_name="End No")

    class Meta:
        managed = False  # Django will not create or delete this table
        db_table = 'tblACC_ChequeNo'
        verbose_name = 'Cheque Series'
        verbose_name_plural = 'Cheque Series'
        ordering = ['bank__name', 'start_no'] # Order by bank name and start number

    def __str__(self):
        return f"{self.bank.name}: {self.start_no} - {self.end_no}"

    def clean(self):
        """
        Model-level validation for ChequeSeries.
        Ensures StartNo is not greater than EndNo.
        """
        if self.start_no is not None and self.end_no is not None:
            if self.start_no > self.end_no:
                raise ValidationError(_("Start Number cannot be greater than End Number."))
            
        # Optional: Add logic to check for overlapping cheque series for the same bank
        # if self.bank and self.start_no and self.end_no:
        #     # Exclude current object in case of update
        #     queryset = ChequeSeries.objects.filter(bank=self.bank).exclude(pk=self.pk)
        #     for series in queryset:
        #         # Check for overlap: (StartA <= EndB) and (EndA >= StartB)
        #         if (self.start_no <= series.end_no) and (self.end_no >= series.start_no):
        #             raise ValidationError(_("This cheque series overlaps with an existing series for the same bank."))


```

#### 4.2 Forms

**Task:** Define a Django form for `ChequeSeries` for user input, including styling and validation.

**Instructions:**
A `ModelForm` will be created for the `ChequeSeries` model, incorporating the necessary fields and Tailwind CSS classes for consistent styling.

**`accounts/forms.py`**

```python
from django import forms
from .models import ChequeSeries, Bank

class ChequeSeriesForm(forms.ModelForm):
    """
    Django ModelForm for creating and updating ChequeSeries instances.
    """
    bank = forms.ModelChoiceField(
        queryset=Bank.objects.all(),
        empty_label="Select a Bank",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Bank Name" # Custom label for better readability
    )

    class Meta:
        model = ChequeSeries
        fields = ['bank', 'start_no', 'end_no']
        widgets = {
            'start_no': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'min': '1'}),
            'end_no': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'min': '1'}),
        }
        labels = {
            'start_no': 'Start No',
            'end_no': 'End No',
        }

    def clean(self):
        """
        Overrides the clean method to add form-level validation.
        This includes calling the model's clean method and adding specific form-level checks.
        """
        cleaned_data = super().clean()
        start_no = cleaned_data.get('start_no')
        end_no = cleaned_data.get('end_no')

        # This validation is also in the model's clean method, but useful to have at form level too.
        if start_no is not None and end_no is not None:
            if start_no > end_no:
                self.add_error('start_no', "Start Number cannot be greater than End Number.")
                # self.add_error('end_no', "End Number must be greater than or equal to Start Number.")
        
        # Example of how to trigger model-level validation (clean method) from form.
        # This is automatically called by form.is_valid() before saving if it's a ModelForm.
        # However, for deeper custom logic or cross-field validation, you might call it explicitly.
        try:
            instance = ChequeSeries(**cleaned_data)
            instance.clean() # Call the model's clean method
        except ValidationError as e:
            # Transfer validation errors from model to form
            for field, messages in e.error_dict.items():
                for msg in messages:
                    self.add_error(field, msg)
            if '__all__' in e.error_dict: # Handle non-field errors
                for msg in e.error_dict['__all__']:
                    self.add_error(None, msg) # Add to non-field errors


```

#### 4.3 Views

**Task:** Implement CRUD operations using thin Class-Based Views (CBVs), ensuring HTMX compatibility and adherence to the 15-line view limit.

**Instructions:**
Separate views will handle listing, creation, updating, and deletion. An additional partial view will be created to serve the DataTables content via HTMX. Success messages will be handled by Django's messages framework.

**`accounts/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ChequeSeries, Bank # Import Bank model if needed for context in list view
from .forms import ChequeSeriesForm
from django.shortcuts import get_object_or_404 # For TablePartialView

class ChequeSeriesListView(ListView):
    """
    Displays the main page for Cheque Series management.
    The actual table content is loaded via HTMX by ChequeSeriesTablePartialView.
    """
    model = ChequeSeries
    template_name = 'accounts/chequeseries/list.html'
    context_object_name = 'cheque_series_list' # Renamed for clarity in template

class ChequeSeriesTablePartialView(ListView):
    """
    Returns the HTML partial containing the DataTables table for Cheque Series.
    Used by HTMX to refresh the list without a full page reload.
    """
    model = ChequeSeries
    template_name = 'accounts/chequeseries/_chequeseries_table.html'
    context_object_name = 'cheque_series_list' # Renamed for clarity in template

    def get_queryset(self):
        # Override to ensure any necessary joins or specific ordering for the table
        # If GetChequeNo stored procedure did joins, replicate here if needed.
        return super().get_queryset().select_related('bank') # Efficiently fetch bank data

class ChequeSeriesCreateView(CreateView):
    """
    Handles creation of new Cheque Series entries.
    Renders a form within a modal and handles HTMX submission.
    """
    model = ChequeSeries
    form_class = ChequeSeriesForm
    template_name = 'accounts/chequeseries/_chequeseries_form.html' # Use partial for modal
    success_url = reverse_lazy('chequeseries_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Cheque Series added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to signal success without redirect/body
                headers={
                    'HX-Trigger': 'refreshChequeSeriesList' # Custom event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # HTMX expects the form HTML back for validation errors
        return response

class ChequeSeriesUpdateView(UpdateView):
    """
    Handles updating existing Cheque Series entries.
    Renders a form within a modal and handles HTMX submission.
    """
    model = ChequeSeries
    form_class = ChequeSeriesForm
    template_name = 'accounts/chequeseries/_chequeseries_form.html' # Use partial for modal
    success_url = reverse_lazy('chequeseries_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Cheque Series updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshChequeSeriesList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class ChequeSeriesDeleteView(DeleteView):
    """
    Handles deletion of Cheque Series entries.
    Renders a confirmation modal and handles HTMX submission.
    """
    model = ChequeSeries
    template_name = 'accounts/chequeseries/_chequeseries_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('chequeseries_list')

    def delete(self, request, *args, **kwargs):
        obj = self.get_object()
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Cheque Series "{obj}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshChequeSeriesList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['cheque_series'] = self.get_object() # Make object available in template
        return context

```

#### 4.4 Templates

**Task:** Create specific templates for each view, focusing on DRY principles, HTMX, and DataTables integration.

**Instructions:**
Templates will be structured as partials for HTMX functionality. They will extend `core/base.html` (which is assumed to exist) and utilize Tailwind CSS for styling.

**`accounts/templates/accounts/chequeseries/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Cheque Series Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'chequeseries_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Cheque Series
        </button>
    </div>

    <div id="chequeseriesTable-container"
         class="bg-white shadow-lg rounded-lg overflow-hidden"
         hx-trigger="load, refreshChequeSeriesList from:body"
         hx-get="{% url 'chequeseries_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading indicator -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Cheque Series data...</p>
        </div>
    </div>

    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 sm:mx-auto relative transform transition-all sm:my-8 sm:align-middle"
             x-data="{ open: false }" x-init="$watch('open', value => { if (!value) { $el.innerHTML = '' } })">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize DataTables if a new table was swapped in
        if (event.detail.target.id === 'chequeseriesTable-container') {
            $('#chequeseriesTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Destroy existing DataTables instance if any
                "responsive": true
            });
        }
        // Handle modal opening/closing
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            document.getElementById('modal').classList.add('is-active');
        }
    });

    document.addEventListener('htmx:afterRequest', function(event) {
        // Close modal after successful form submission/delete via HTMX (status 204)
        if (event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Display Django messages (e.g., success messages) as toasts
    document.addEventListener('DOMContentLoaded', function() {
        const messages = document.querySelectorAll('.django-message'); // Assuming messages are rendered with this class
        messages.forEach(msg => {
            // Implement a toast notification system with Alpine.js or a small library
            console.log("Django message:", msg.textContent); // For demonstration
            // Example: Alpine.js toast (requires Alpine.js setup in base.html)
            // window.dispatchEvent(new CustomEvent('show-toast', { detail: { message: msg.textContent, type: msg.dataset.tags } }));
            msg.remove(); // Remove message after display
        });
    });
</script>
{% endblock %}
```

**`accounts/templates/accounts/chequeseries/_chequeseries_table.html`**

```html
<div class="p-4">
    <table id="chequeseriesTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for series in cheque_series_list %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ series.bank.name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ series.start_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ series.end_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'chequeseries_edit' series.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'chequeseries_delete' series.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-6 px-4 text-center text-gray-500">No cheque series found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization handled by htmx:afterSwap event listener in list.html
</script>
```

**`accounts/templates/accounts/chequeseries/_chequeseries_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {% if form.instance.pk %}Edit Cheque Series{% else %}Add New Cheque Series{% endif %}
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}

        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            {% for field in form %}
            <div class="{% if field.name == 'bank' %}sm:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        {% if form.non_field_errors %}
        <div class="mt-6">
            {% for error in form.non_field_errors %}
            <p class="text-sm text-red-600">{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                {% if form.instance.pk %}Update{% else %}Create{% endif %} Cheque Series
            </button>
        </div>
    </form>
</div>
```

**`accounts/templates/accounts/chequeseries/_chequeseries_confirm_delete.html`**

```html
<div class="p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-6">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
    </div>
    <h3 class="text-lg font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-sm text-gray-600 mb-8">
        Are you sure you want to delete the cheque series:<br>
        <strong>"{{ cheque_series.bank.name }} - {{ cheque_series.start_no }} to {{ cheque_series.end_no }}"</strong>?
        This action cannot be undone.
    </p>

    <form hx-post="{% url 'chequeseries_delete' cheque_series.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for all `ChequeSeries` views.

**Instructions:**
URLs will be set up for the list view, the partial table view, and all CRUD operations, ensuring consistent naming.

**`accounts/urls.py`**

```python
from django.urls import path
from .views import (
    ChequeSeriesListView,
    ChequeSeriesTablePartialView,
    ChequeSeriesCreateView,
    ChequeSeriesUpdateView,
    ChequeSeriesDeleteView,
)

urlpatterns = [
    # Main list view for Cheque Series
    path('cheque-series/', ChequeSeriesListView.as_view(), name='chequeseries_list'),

    # HTMX endpoint for refreshing the DataTables table
    path('cheque-series/table/', ChequeSeriesTablePartialView.as_view(), name='chequeseries_table'),

    # HTMX endpoints for modal forms (add, edit, delete)
    path('cheque-series/add/', ChequeSeriesCreateView.as_view(), name='chequeseries_add'),
    path('cheque-series/edit/<int:pk>/', ChequeSeriesUpdateView.as_view(), name='chequeseries_edit'),
    path('cheque-series/delete/<int:pk>/', ChequeSeriesDeleteView.as_view(), name='chequeseries_delete'),
]

```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and coverage.

**Instructions:**
Tests will cover model attributes, methods, and all view behaviors (GET/POST, form submission, HTMX responses).

**`accounts/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from django.core.exceptions import ValidationError
from .models import Bank, ChequeSeries
from .forms import ChequeSeriesForm

class BankModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a Bank instance for tests. Assuming existing DB, so providing ID.
        Bank.objects.create(id=1, name='Test Bank A')
        Bank.objects.create(id=2, name='Test Bank B')

    def test_bank_creation(self):
        bank = Bank.objects.get(id=1)
        self.assertEqual(bank.name, 'Test Bank A')

    def test_bank_str_representation(self):
        bank = Bank.objects.get(id=1)
        self.assertEqual(str(bank), 'Test Bank A')

    def test_bank_name_max_length(self):
        bank = Bank.objects.get(id=1)
        max_length = bank._meta.get_field('name').max_length
        self.assertEqual(max_length, 255)

    def test_bank_name_unique(self):
        # Attempt to create a bank with an existing name should raise IntegrityError
        with self.assertRaises(IntegrityError):
            Bank.objects.create(id=3, name='Test Bank A')


class ChequeSeriesModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create Bank instances for ChequeSeries FK
        cls.bank_a = Bank.objects.create(id=10, name='Primary Bank')
        cls.bank_b = Bank.objects.create(id=20, name='Secondary Bank')

        # Create ChequeSeries instances for tests
        ChequeSeries.objects.create(id=1, bank=cls.bank_a, start_no=1001, end_no=2000)
        ChequeSeries.objects.create(id=2, bank=cls.bank_b, start_no=1, end_no=100)

    def test_cheque_series_creation(self):
        series = ChequeSeries.objects.get(id=1)
        self.assertEqual(series.bank, self.bank_a)
        self.assertEqual(series.start_no, 1001)
        self.assertEqual(series.end_no, 2000)

    def test_cheque_series_str_representation(self):
        series = ChequeSeries.objects.get(id=1)
        self.assertEqual(str(series), f"{self.bank_a.name}: 1001 - 2000")

    def test_start_no_greater_than_end_no_validation(self):
        series = ChequeSeries(bank=self.bank_a, start_no=3000, end_no=2999)
        with self.assertRaises(ValidationError) as cm:
            series.full_clean() # Calls model's clean method and field validations
        self.assertIn("Start Number cannot be greater than End Number.", str(cm.exception))

    def test_start_no_equal_to_end_no_valid(self):
        series = ChequeSeries(bank=self.bank_b, start_no=500, end_no=500)
        series.full_clean() # Should not raise validation error


class ChequeSeriesFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.bank_c = Bank.objects.create(id=30, name='Form Test Bank')

    def test_valid_form(self):
        form_data = {
            'bank': self.bank_c.id,
            'start_no': 100,
            'end_no': 200
        }
        form = ChequeSeriesForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_invalid_form_start_greater_than_end(self):
        form_data = {
            'bank': self.bank_c.id,
            'start_no': 300,
            'end_no': 200
        }
        form = ChequeSeriesForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('start_no', form.errors)
        self.assertIn("Start Number cannot be greater than End Number.", form.errors['start_no'])

    def test_required_fields(self):
        form_data = {} # Missing all required fields
        form = ChequeSeriesForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('bank', form.errors)
        self.assertIn('start_no', form.errors)
        self.assertIn('end_no', form.errors)


class ChequeSeriesViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.client = Client()
        cls.bank_alpha = Bank.objects.create(id=100, name='Alpha Bank')
        cls.cheque_series1 = ChequeSeries.objects.create(id=101, bank=cls.bank_alpha, start_no=1000, end_no=1999)
        cls.bank_beta = Bank.objects.create(id=200, name='Beta Bank')

    def test_list_view_get(self):
        response = self.client.get(reverse('chequeseries_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/chequeseries/list.html')
        # The list context object isn't directly used in list.html now, but in the partial
        # self.assertIn('cheque_series_list', response.context)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('chequeseries_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/chequeseries/_chequeseries_table.html')
        self.assertIn('cheque_series_list', response.context)
        self.assertContains(response, self.cheque_series1.bank.name)
        self.assertContains(response, str(self.cheque_series1.start_no))

    def test_create_view_get(self):
        response = self.client.get(reverse('chequeseries_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/chequeseries/_chequeseries_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_valid(self):
        initial_count = ChequeSeries.objects.count()
        form_data = {
            'bank': self.bank_beta.id,
            'start_no': 2001,
            'end_no': 3000
        }
        response = self.client.post(reverse('chequeseries_add'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertTrue(ChequeSeries.objects.filter(bank=self.bank_beta, start_no=2001).exists())
        self.assertEqual(ChequeSeries.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshChequeSeriesList')

    def test_create_view_post_invalid(self):
        initial_count = ChequeSeries.objects.count()
        form_data = {
            'bank': self.bank_beta.id,
            'start_no': 3000,
            'end_no': 2000 # Invalid
        }
        response = self.client.post(reverse('chequeseries_add'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX expects form HTML back with errors
        self.assertTemplateUsed(response, 'accounts/chequeseries/_chequeseries_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        self.assertFalse(ChequeSeries.objects.filter(bank=self.bank_beta, start_no=3000).exists())
        self.assertEqual(ChequeSeries.objects.count(), initial_count)


    def test_update_view_get(self):
        response = self.client.get(reverse('chequeseries_edit', args=[self.cheque_series1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/chequeseries/_chequeseries_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.cheque_series1)

    def test_update_view_post_valid(self):
        new_start_no = 1500
        form_data = {
            'bank': self.cheque_series1.bank.id,
            'start_no': new_start_no,
            'end_no': self.cheque_series1.end_no
        }
        response = self.client.post(reverse('chequeseries_edit', args=[self.cheque_series1.pk]), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.cheque_series1.refresh_from_db()
        self.assertEqual(self.cheque_series1.start_no, new_start_no)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshChequeSeriesList')

    def test_delete_view_get(self):
        response = self.client.get(reverse('chequeseries_delete', args=[self.cheque_series1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/chequeseries/_chequeseries_confirm_delete.html')
        self.assertIn('cheque_series', response.context)
        self.assertEqual(response.context['cheque_series'], self.cheque_series1)

    def test_delete_view_post(self):
        initial_count = ChequeSeries.objects.count()
        response = self.client.post(reverse('chequeseries_delete', args=[self.cheque_series1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ChequeSeries.objects.filter(pk=self.cheque_series1.pk).exists())
        self.assertEqual(ChequeSeries.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshChequeSeriesList')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django code already incorporates HTMX and Alpine.js principles:

*   **HTMX for Dynamic Updates:**
    *   The main list page (`list.html`) uses `hx-get` to load the table content from `{% url 'chequeseries_table' %}`. This initial load and subsequent refreshes are triggered by `hx-trigger="load, refreshChequeSeriesList from:body"`.
    *   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch the respective form/confirmation partials into the `#modalContent` div.
    *   Form submissions (`hx-post`) from the modal partials (`_chequeseries_form.html`, `_chequeseries_confirm_delete.html`) are handled via HTMX, with `hx-swap="none"` or `hx-swap="outerHTML"` as appropriate, and `hx-target` set for validation error re-rendering.
    *   Successful form submissions and deletions return an `HTTP 204 No Content` status along with an `HX-Trigger: refreshChequeSeriesList` header, prompting the main list to refresh its table.
    *   Error handling for forms returns the form HTML with validation errors, allowing HTMX to re-render the modal content without a full page refresh.

*   **Alpine.js for UI State Management:**
    *   The main modal (`#modal`) in `list.html` uses Alpine.js's `x-data` and `_="on click ..."` (via `_hyperscript`, which is often bundled with or used alongside HTMX) to manage its visibility (`add .is-active to #modal` and `remove .is-active from me`).
    *   The `x-init` on `#modalContent` ensures that the modal's content is cleared when it's closed, preventing stale data.
    *   Basic event listeners in the `extra_js` block (which would be in your `base.html` or a loaded script) handle re-initializing DataTables after HTMX swaps and managing modal visibility post-request.

*   **DataTables for List Views:**
    *   The `_chequeseries_table.html` partial contains the HTML `<table>` with the ID `chequeseriesTable`.
    *   A JavaScript snippet in `list.html` listens for the `htmx:afterSwap` event on the table container and initializes DataTables on the newly loaded table, ensuring proper functionality including search, sort, and pagination. `destroy: true` handles re-initialization if the table is swapped multiple times.

*   **DRY Template Inheritance:**
    *   All component templates extend `core/base.html` using `{% extends 'core/base.html' %}`. This ensures that common elements like CDN links for DataTables, HTMX, Alpine.js, jQuery, and Tailwind CSS are only defined once in `base.html`.
    *   Specific `{% block content %}` and `{% block extra_js %}` are used to inject module-specific content.

---

### Final Notes

*   **Database Configuration:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database (e.g., using `django-pyodbc-azure` or `mssql-django` packages). The `managed = False` setting in models is crucial for working with pre-existing tables.
*   **Django App Setup:** Remember to add `'accounts'` to your `INSTALLED_APPS` in `settings.py` and include the `accounts.urls` in your project's main `urls.py`.
*   **Frontend Libraries:** Ensure jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS are properly linked in your `core/base.html` to enable the dynamic features.
*   **Error Handling and User Feedback:** While Django's `messages` framework is integrated, consider a more visible toast notification system (e.g., using Alpine.js) to display messages like "Record added/updated/deleted successfully" to the user, similar to the original `lblMessage` in ASP.NET.
*   **Business Logic Refinement:** The `ChequeSeries` model `clean` method includes a basic `start_no <= end_no` validation. You might extend this with more complex logic (e.g., checking for overlapping cheque series within the same bank) within the model, adhering to the fat model principle.
*   **Comprehensive Testing:** The provided tests serve as a strong starting point. Expand them to cover edge cases, specific validation rules, and any custom methods added to your models. Aim for high test coverage (80%+).

This modernization plan provides a clear, automated pathway to a robust and maintainable Django application, bringing significant long-term benefits to your organization.