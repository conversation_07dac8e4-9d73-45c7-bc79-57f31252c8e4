This document outlines a strategic plan for migrating your existing ASP.NET Loan Type management functionality to a modern Django-based system. Our approach prioritizes automated conversion, clean architecture, and enhanced user experience through contemporary web technologies.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the `SqlDataSource` in the `.aspx` file:

-   `SelectCommand="SELECT * FROM [tblACC_LoanType] order by [Id] desc"`
-   `InsertCommand="INSERT INTO [tblACC_LoanType] ([Description]) VALUES (@Description)"`
-   `UpdateCommand="UPDATE [tblACC_LoanType] SET [Description] = @Description WHERE [Id] = @Id"`
-   `DeleteCommand="DELETE FROM [tblACC_LoanType] WHERE [Id] = @Id"`
-   `DataKeyNames="Id"`

**Extracted Information:**

-   **Table Name:** `tblACC_LoanType`
-   **Columns:**
    -   `Id`: Integer (Primary Key)
    -   `Description`: String

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

Analysis of the `.aspx` and `.aspx.cs` files reveals the following:

-   **Create (Insert):**
    -   Triggered by `btnInsert` (CommandName "Add" in `FooterTemplate` or "Add1" in `EmptyDataTemplate`).
    -   Handled by `GridView1_RowCommand` method in C#, which calls `LocalSqlServer.Insert()`.
    -   Input: `txtDescription1` (for "Add") or `txtDescription` (for "Add1").
    -   Validation: `RequiredFieldValidator` for `Description`.
-   **Read (Select):**
    -   Data displayed in `GridView1`.
    -   Populated via `LocalSqlServer` using `SelectCommand="SELECT * FROM [tblACC_LoanType] order by [Id] desc"`.
-   **Update (Edit):**
    -   Triggered by `CommandField` (Edit button) in `GridView1`.
    -   Handled by `GridView1_RowUpdating` method in C#, which calls `LocalSqlServer.Update()`.
    -   Input: `txtDescription0` in `EditItemTemplate`.
    -   Validation: `RequiredFieldValidator` for `Description`.
-   **Delete:**
    -   Triggered by `CommandField` (Delete button) in `GridView1`.
    -   Handled by `GridView1_RowDeleted` event and `LocalSqlServer.Delete()`.
-   **Messages:** `lblMessage` is used to display success messages ("Record Updated.", "Record Deleted.", "Record Inserted.").

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The ASP.NET page uses:

-   **GridView (ID: GridView1):** The primary component for displaying a list of "Loan Types" with built-in paging, editing, and deleting capabilities. It supports in-line editing and adding new rows via footer/empty templates.
-   **TextBox (IDs: txtDescription0, txtDescription1, txtDescription):** Used for inputting the 'Loan Type Description' during edit and insert operations.
-   **Button (ID: btnInsert):** Triggers the 'add new' operation.
-   **Label (ID: lblMessage):** Displays feedback messages to the user.
-   **SqlDataSource (ID: LocalSqlServer):** Manages the direct database interaction for the GridView.
-   **RequiredFieldValidator:** Client-side and server-side validation for the Description field.
-   **JavaScript (`PopUpMsg.js`, `loadingNotifier.js`):** Used for client-side pop-up confirmations (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) and possibly loading indicators. These will be replaced by HTMX and Alpine.js.

### Step 4: Generate Django Code

#### 4.1 Models (`accounts/models.py`)

**Task:** Create a Django model based on the database schema.

**Instructions:**

The model will represent the `tblACC_LoanType` table, mapping `Id` and `Description` columns.

```python
from django.db import models

class LoanType(models.Model):
    # 'Id' is the primary key in the ASP.NET table.
    # We map it directly to an IntegerField since managed=False.
    # Django's default 'id' field is implicitly handled if it's an AutoField,
    # but here we explicitly define it to match the existing column name 'Id'.
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255) # Assuming a reasonable max_length

    class Meta:
        managed = False  # Important: Django will not manage this table's creation/deletion.
        db_table = 'tblACC_LoanType' # Links to the existing database table.
        verbose_name = 'Loan Type'
        verbose_name_plural = 'Loan Types'

    def __str__(self):
        return self.description

    # Business logic methods will go here if any complex operations are identified later.
    # For now, it's a simple data model.
    # Example:
    # def normalize_description(self):
    #     self.description = self.description.strip().upper()
    #     self.save()
```

#### 4.2 Forms (`accounts/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**

A `ModelForm` will be used for `LoanType` creation and updates, ensuring validation rules are applied.

```python
from django import forms
from .models import LoanType

class LoanTypeForm(forms.ModelForm):
    class Meta:
        model = LoanType
        # Exclude 'id' as it's typically auto-managed or handled by Django's ORM for existing records.
        # For new records, it will be auto-assigned by the database if it's an identity column.
        fields = ['description']
        widgets = {
            'description': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter Loan Type Description'
            }),
        }
        labels = {
            'description': 'Loan Type Description',
        }
        
    # Custom validation methods (if needed, e.g., uniqueness check) would go here.
    # def clean_description(self):
    #     description = self.cleaned_data['description']
    #     # Example: Ensure description is unique (case-insensitive)
    #     if LoanType.objects.filter(description__iexact=description).exclude(id=self.instance.id).exists():
    #         raise forms.ValidationError("This Loan Type already exists.")
    #     return description
```

#### 4.3 Views (`accounts/views.py`)

**Task:** Implement CRUD operations using CBVs.

**Instructions:**

Views will be thin, delegating business logic (if any) to the model. HTMX requests will receive `204 No Content` responses with `HX-Trigger` headers to refresh the list, avoiding full page reloads.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # For partial rendering
from .models import LoanType
from .forms import LoanTypeForm

# View for listing all Loan Types
class LoanTypeListView(ListView):
    model = LoanType
    template_name = 'accounts/loantype/list.html'
    context_object_name = 'loantypes'

# View for fetching the table content via HTMX
class LoanTypeTablePartialView(ListView):
    model = LoanType
    template_name = 'accounts/loantype/_loantype_table.html'
    context_object_name = 'loantypes'

# View for creating a new Loan Type
class LoanTypeCreateView(CreateView):
    model = LoanType
    form_class = LoanTypeForm
    template_name = 'accounts/loantype/_loantype_form.html' # This will be loaded into a modal
    success_url = reverse_lazy('loantype_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # Save the new LoanType instance
        response = super().form_valid(form)
        # Add a success message for user feedback
        messages.success(self.request, 'Loan Type added successfully.')
        
        # If the request is from HTMX, return a 204 No Content response
        # with an HX-Trigger header to refresh the list on the client side.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanTypeList' # Custom event to trigger table refresh
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, re-render the form within the modal
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, self.request),
                status=400 # Indicate bad request due to validation errors
            )
        return response

# View for updating an existing Loan Type
class LoanTypeUpdateView(UpdateView):
    model = LoanType
    form_class = LoanTypeForm
    template_name = 'accounts/loantype/_loantype_form.html' # Loaded into a modal
    context_object_name = 'loantype'
    success_url = reverse_lazy('loantype_list') # Fallback

    def form_valid(self, form):
        # Save the updated LoanType instance
        response = super().form_valid(form)
        messages.success(self.request, 'Loan Type updated successfully.')
        
        # If HTMX request, return 204 with trigger
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanTypeList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form, 'loantype': self.get_object()}, self.request),
                status=400
            )
        return response

# View for deleting a Loan Type
class LoanTypeDeleteView(DeleteView):
    model = LoanType
    template_name = 'accounts/loantype/_loantype_confirm_delete.html' # Loaded into a modal
    context_object_name = 'loantype'
    success_url = reverse_lazy('loantype_list') # Fallback

    def delete(self, request, *args, **kwargs):
        # Perform the delete operation
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Loan Type deleted successfully.')
        
        # If HTMX request, return 204 with trigger
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanTypeList'
                }
            )
        return response
```

#### 4.4 Templates (`accounts/templates/accounts/loantype/`)

**Task:** Create templates for each view, leveraging HTMX for dynamic content and Alpine.js for UI state.

**Instructions:**

All templates will extend `core/base.html` and use Tailwind CSS for styling. Modals are managed by Alpine.js and populated via HTMX. DataTables will handle the listing presentation.

**`list.html`** (Main page for Loan Types)
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Loan Types Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'loantype_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i>Add New Loan Type
        </button>
    </div>
    
    <div id="loantypeTable-container"
         hx-trigger="load, refreshLoanTypeList from:body"
         hx-get="{% url 'loantype_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Loan Types...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 hidden"
         x-data="{ showModal: false }"
         x-init="$watch('showModal', value => { if (value) document.getElementById('modal').classList.remove('hidden'); else document.getElementById('modal').classList.add('hidden'); })"
         x-show="showModal"
         _="on click if event.target.id == 'modal' set showModal to false">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-lg w-full transform transition-all duration-300 scale-95"
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
             x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
             x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is typically loaded globally via base.html for the x-data/x-init attributes to work.
    // Ensure that the 'showModal' data property is correctly linked to the modal's visibility.
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            let modal = document.getElementById('modal');
            if (modal._x_data) { // Check if Alpine.js data exists
                modal._x_data.showModal = true;
            } else {
                modal.classList.remove('hidden');
            }
        }
    });

    // Close modal and refresh list on HTMX trigger for CRUD success
    document.body.addEventListener('refreshLoanTypeList', function(evt) {
        let modal = document.getElementById('modal');
        if (modal._x_data) {
            modal._x_data.showModal = false;
        } else {
            modal.classList.add('hidden');
        }
        // DataTables needs to be re-initialized after content swap,
        // or ensure hx-swap="outerHTML" on the table-container
        // and dataTables() init is in the _loantype_table.html
    });

    // If modal content fails to load, close modal
    document.body.addEventListener('htmx:responseError', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            let modal = document.getElementById('modal');
            if (modal._x_data) {
                modal._x_data.showModal = false;
            } else {
                modal.classList.add('hidden');
            }
        }
    });
</script>
{% endblock %}
```

**`_loantype_table.html`** (Partial for DataTables display)
```html
<table id="loantypeTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Loan Type Description</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in loantypes %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.description }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'loantype_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit mr-1"></i>Edit
                </button>
                <button 
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                    hx-get="{% url 'loantype_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt mr-1"></i>Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-6 text-center text-gray-500">No loan types found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#loantypeTable')) {
        $('#loantypeTable').DataTable().destroy();
    }
    $('#loantypeTable').DataTable({
        "pageLength": 10, // Default page size
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for page length
        "dom": 'lfrtip', // Layout: Length changing, Filtering, Table, Information, Pagination
        "responsive": true, // Enable responsive design
        "language": {
            "search": "Search:",
            "lengthMenu": "Show _MENU_ entries",
            "info": "Showing _START_ to _END_ of _TOTAL_ entries",
            "infoEmpty": "Showing 0 to 0 of 0 entries",
            "infoFiltered": "(filtered from _MAX_ total entries)",
            "paginate": {
                "first": "First",
                "last": "Last",
                "next": "Next",
                "previous": "Previous"
            }
        },
        // Column definitions if needed, e.g., to disable sorting on Actions column
        "columnDefs": [
            { "orderable": false, "targets": [0, 2] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

**`_loantype_form.html`** (Partial for Add/Edit forms)
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Loan Type</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4" role="alert">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}

        <div>
            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ form.description.label }}
            </label>
            {{ form.description }}
            {% if form.description.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.description.errors|join:", " }}</p>
            {% endif %}
        </div>
        
        <div class="mt-8 flex justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i>Save Loan Type
            </button>
        </div>
    </form>
</div>
```

**`_loantype_confirm_delete.html`** (Partial for Delete confirmation)
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
    <p class="text-gray-600 mb-6">
        Are you sure you want to delete the Loan Type: **{{ loantype.description }}**?
        This action cannot be undone.
    </p>
    
    <form hx-post="{% url 'loantype_delete' loantype.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i>Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**

URL patterns will be defined within the `accounts` application, adhering to a consistent naming convention.

```python
from django.urls import path
from .views import (
    LoanTypeListView, 
    LoanTypeCreateView, 
    LoanTypeUpdateView, 
    LoanTypeDeleteView,
    LoanTypeTablePartialView # New view for HTMX table content
)

urlpatterns = [
    # Main list view (full page load for initial visit, HTMX handles subsequent updates)
    path('loantypes/', LoanTypeListView.as_view(), name='loantype_list'),
    
    # HTMX-specific endpoint to get just the table content (for refresh)
    path('loantypes/table/', LoanTypeTablePartialView.as_view(), name='loantype_table'),

    # CRUD operations loaded into modals via HTMX
    path('loantypes/add/', LoanTypeCreateView.as_view(), name='loantype_add'),
    path('loantypes/edit/<int:pk>/', LoanTypeUpdateView.as_view(), name='loantype_edit'),
    path('loantypes/delete/<int:pk>/', LoanTypeDeleteView.as_view(), name='loantype_delete'),
]

```

#### 4.6 Tests (`accounts/tests.py`)

**Task:** Write tests for the model and views, ensuring good test coverage.

**Instructions:**

Unit tests for the `LoanType` model and integration tests for all CRUD views, including HTMX specific behaviors, will be implemented.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import IntegrityError # To test database constraints if any
from .models import LoanType

# Unit Tests for LoanType Model
class LoanTypeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test instance of LoanType.
        # Since 'id' is a primary key and managed=False, ensure it's provided if needed by the DB.
        # For testing, we can simulate an existing record.
        # In a real scenario with managed=False, you might mock the DB or use a test DB.
        # For simplicity, we'll create an instance directly assuming id is auto-generated by DB or explicitly set.
        LoanType.objects.create(id=1, description='Test Loan Type')
        LoanType.objects.create(id=2, description='Another Loan Type')
  
    def test_loantype_creation(self):
        """Test that a LoanType object can be created and its fields are correct."""
        loan_type = LoanType.objects.get(id=1)
        self.assertEqual(loan_type.description, 'Test Loan Type')
        self.assertEqual(str(loan_type), 'Test Loan Type') # Test __str__ method

    def test_description_label(self):
        """Test the verbose name for the 'description' field."""
        loan_type = LoanType.objects.get(id=1)
        field_label = loan_type._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'description') # Default verbose_name if not explicitly set in model
        # If you set verbose_name='Loan Type Description' in model field, assert that.

    def test_description_max_length(self):
        """Test the max length of the description field."""
        loan_type = LoanType.objects.get(id=1)
        max_length = loan_type._meta.get_field('description').max_length
        self.assertEqual(max_length, 255)

    def test_unique_description(self):
        """Illustrative test for unique description (if implemented in form or model)."""
        # This test relies on form/model validation to ensure uniqueness,
        # which isn't explicitly set in the model's Meta.
        # If we added a unique=True to the model field, this test would be relevant.
        # For now, it's a placeholder to show where such a test would go.
        pass

# Integration Tests for LoanType Views
class LoanTypeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests.
        LoanType.objects.create(id=101, description='First Loan')
        LoanType.objects.create(id=102, description='Second Loan')
    
    def setUp(self):
        # Set up a new client for each test method to ensure isolation.
        self.client = Client()
    
    def test_list_view(self):
        """Test the LoanType list view (GET request)."""
        response = self.client.get(reverse('loantype_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loantype/list.html')
        self.assertIn('loantypes', response.context)
        self.assertEqual(len(response.context['loantypes']), 2)
        self.assertContains(response, 'First Loan')
        self.assertContains(response, 'Second Loan')

    def test_table_partial_view(self):
        """Test the HTMX partial for the table content."""
        response = self.client.get(reverse('loantype_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loantype/_loantype_table.html')
        self.assertIn('loantypes', response.context)
        self.assertEqual(len(response.context['loantypes']), 2)
        self.assertContains(response, '<table id="loantypeTable"') # Check for table structure
        self.assertContains(response, 'First Loan')

    def test_create_view_get(self):
        """Test GET request to the create form."""
        response = self.client.get(reverse('loantype_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loantype/_loantype_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Loan Type')

    def test_create_view_post_success(self):
        """Test successful POST request to create a LoanType."""
        data = {'description': 'New Loan Type'}
        response = self.client.post(reverse('loantype_add'), data)
        # For non-HTMX requests, it's a redirect
        self.assertEqual(response.status_code, 302) 
        self.assertTrue(LoanType.objects.filter(description='New Loan Type').exists())
        self.assertRedirects(response, reverse('loantype_list'))

    def test_create_view_post_htmx_success(self):
        """Test successful HTMX POST request to create a LoanType."""
        data = {'description': 'HTMX Loan'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('loantype_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanTypeList')
        self.assertTrue(LoanType.objects.filter(description='HTMX Loan').exists())

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data to create a LoanType."""
        data = {'description': ''} # Empty description, should fail validation
        response = self.client.post(reverse('loantype_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertFalse(LoanType.objects.filter(description='').exists())
        self.assertContains(response, 'This field is required.')

    def test_create_view_post_htmx_invalid(self):
        """Test HTMX POST request with invalid data to create a LoanType."""
        data = {'description': ''} # Empty description
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('loantype_add'), data, **headers)
        self.assertEqual(response.status_code, 400) # Bad Request for HTMX invalid form
        self.assertTemplateUsed(response, 'accounts/loantype/_loantype_form.html')
        self.assertContains(response, 'This field is required.')

    def test_update_view_get(self):
        """Test GET request to the update form."""
        loan_type = LoanType.objects.get(id=101)
        response = self.client.get(reverse('loantype_edit', args=[loan_type.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loantype/_loantype_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, loan_type)
        self.assertContains(response, 'Edit Loan Type')

    def test_update_view_post_success(self):
        """Test successful POST request to update a LoanType."""
        loan_type = LoanType.objects.get(id=101)
        data = {'description': 'Updated Loan Type'}
        response = self.client.post(reverse('loantype_edit', args=[loan_type.id]), data)
        self.assertEqual(response.status_code, 302)
        loan_type.refresh_from_db()
        self.assertEqual(loan_type.description, 'Updated Loan Type')
        self.assertRedirects(response, reverse('loantype_list'))

    def test_update_view_post_htmx_success(self):
        """Test successful HTMX POST request to update a LoanType."""
        loan_type = LoanType.objects.get(id=102)
        data = {'description': 'Updated HTMX Loan'}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('loantype_edit', args=[loan_type.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanTypeList')
        loan_type.refresh_from_db()
        self.assertEqual(loan_type.description, 'Updated HTMX Loan')

    def test_update_view_post_invalid(self):
        """Test POST request with invalid data to update a LoanType."""
        loan_type = LoanType.objects.get(id=101)
        data = {'description': ''}
        response = self.client.post(reverse('loantype_edit', args=[loan_type.id]), data)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'This field is required.')

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation page."""
        loan_type = LoanType.objects.get(id=101)
        response = self.client.get(reverse('loantype_delete', args=[loan_type.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loantype/_loantype_confirm_delete.html')
        self.assertIn('loantype', response.context)
        self.assertEqual(response.context['loantype'], loan_type)
        self.assertContains(response, f'delete the Loan Type: **{loan_type.description}**?')

    def test_delete_view_post_success(self):
        """Test successful POST request to delete a LoanType."""
        loan_type = LoanType.objects.get(id=101)
        response = self.client.post(reverse('loantype_delete', args=[loan_type.id]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(LoanType.objects.filter(id=101).exists())
        self.assertRedirects(response, reverse('loantype_list'))

    def test_delete_view_post_htmx_success(self):
        """Test successful HTMX POST request to delete a LoanType."""
        loan_type = LoanType.objects.get(id=102)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('loantype_delete', args=[loan_type.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshLoanTypeList')
        self.assertFalse(LoanType.objects.filter(id=102).exists())
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for CRUD:** All form submissions (Add/Edit) and delete actions are handled via HTMX POST requests to the respective view URLs. `hx-swap="none"` is used on the form, relying on the `HX-Trigger` header from the backend to refresh the main list table.
-   **HTMX for Modals:** Buttons for "Add New", "Edit", and "Delete" use `hx-get` to fetch the form/confirmation partials into `#modalContent`. The `hx-target` is set to this ID.
-   **HTMX for Table Refresh:** The `loantypeTable-container` `div` uses `hx-get="{% url 'loantype_table' %}"` with `hx-trigger="load, refreshLoanTypeList from:body"`. This ensures the table loads initially and refreshes whenever the `refreshLoanTypeList` custom event is triggered (after successful CRUD operations).
-   **Alpine.js for Modal State:** An `x-data` attribute on the modal `div` (`id="modal"`) manages its `showModal` boolean state. Buttons and htmx responses then interact with this state using `_=` (hyperscript) or direct JavaScript listeners to show/hide the modal and update the `showModal` property. The modal is initially `hidden` and controlled by Alpine.js.
-   **DataTables for List Views:** The `_loantype_table.html` partial includes the JavaScript to initialize DataTables on the `loantypeTable` ID. It's crucial that this script runs every time the partial is loaded by HTMX. We include `$(document).ready` and a check for `$.fn.DataTable.isDataTable` to prevent re-initialization errors. This provides client-side searching, sorting, and pagination.
-   **Confirmation Logic:** The original `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()` JavaScript functions are naturally replaced by the HTMX-driven modal workflow, which asks for confirmation *before* submitting the actual delete request.

This comprehensive plan provides a robust and modern Django application that directly addresses the functionality of your legacy ASP.NET Loan Type module. The focus on automation-driven approaches, thin views, fat models, and a modern frontend stack ensures maintainability, scalability, and an improved user experience.