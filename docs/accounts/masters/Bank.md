This document outlines a comprehensive plan for modernizing your existing ASP.NET application, specifically the "Bank" module, to a robust and scalable Django-based solution. Our approach prioritizes automation, leverages modern web technologies like HTMX and Alpine.js for a dynamic user experience, and adheres to best practices like "Fat Models, Thin Views" to ensure maintainability and efficiency.

## ASP.NET to Django Conversion Script:

This modernization plan focuses on providing clear, actionable steps that can be understood and executed, emphasizing automation where possible.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts primarily with `tblACC_Bank`. It also references `tblCountry` via `SqlDataSource2` and implicitly relies on `tblState` and `tblCity` for the dependent dropdown functionalities (Country -> State -> City).

**Identified Tables and Columns:**

*   **`tblACC_Bank`**
    *   `Id` (Primary Key, Integer)
    *   `Name` (Bank Name, Text/String)
    *   `Address` (Text/String)
    *   `Country` (Foreign Key, Integer, references `tblCountry`)
    *   `State` (Foreign Key, Integer, references `tblState`)
    *   `City` (Foreign Key, Integer, references `tblCity`)
    *   `PINNo` (Text/String)
    *   `ContactNo` (Text/String)
    *   `FaxNo` (Text/String)
    *   `IFSC` (IFSC Code, Text/String)

*   **`tblCountry`**
    *   `CId` (Primary Key, Integer)
    *   `CountryName` (Text/String)

*   **`tblState`** (Inferred from dropdown logic)
    *   `SId` (Primary Key, Integer)
    *   `StateName` (Text/String)
    *   `CountryId` (Foreign Key, Integer, references `tblCountry`)

*   **`tblCity`** (Inferred from dropdown logic)
    *   `CityId` (Primary Key, Integer)
    *   `CityName` (Text/String)
    *   `StateId` (Foreign Key, Integer, references `tblState`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and data retrieval/manipulation logic in the ASP.NET code.

**Analysis:**
The C# code-behind handles all data interactions via the `GridView1` control events and a custom `clsFunctions` helper class.

*   **Create (Add):** Handled by `GridView1_RowCommand` with `CommandName="Add"` (for footer row) and `CommandName="Add1"` (for empty data template). It constructs and executes `INSERT` SQL statements.
*   **Read (Retrieve/List):** The `FillGrid()` method fetches data using a `SqlDataAdapter` and a stored procedure `[GetBank_Details]` and binds it to `GridView1`. This populates the list view.
*   **Update (Edit):** Managed by `GridView1_RowUpdating`. It extracts data from the edited row, constructs, and executes an `UPDATE` SQL statement. Dynamic population of Country, State, and City dropdowns during edit is handled in `GridView1_RowEditing` and specific `DrpCountry_SelectedIndexChanged`/`Drpstate_SelectedIndexChanged` methods.
*   **Delete:** Triggered by `GridView1_RowDeleting`. It identifies the record by `Id` and executes a `DELETE` SQL statement.
*   **Dependent Dropdowns:** Functions like `dropdownState` and `dropdownCity` (within `clsFunctions`) are crucial for populating cascading dropdowns (Country -> State -> City) on both initial load and during editing/adding.
*   **Validation:** `RequiredFieldValidator` controls enforce mandatory fields. After successful operations, the page is reloaded (`Response.Redirect`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting client-side interactions.

**Analysis:**
The ASP.NET markup uses standard web forms controls which will be mapped to Django's templating system and modern frontend technologies.

*   **`asp:GridView`:** This central component displays tabular data and offers inline CRUD capabilities. In Django, this will be replaced by a standard HTML `<table>` element enhanced with **DataTables.js** for client-side features (pagination, sorting, search) and **HTMX** for dynamic content updates (CRUD operations and form loading).
*   **`asp:TextBox`:** These are simple text input fields for `Name`, `Address`, `PINNo`, `ContactNo`, `FaxNo`, and `IFSC`. These map directly to Django `TextInput` widgets.
*   **`asp:DropDownList`:** Used for `Country`, `State`, and `City` selections. These will map to Django `Select` widgets. The dynamic filtering logic (State by Country, City by State) will be managed by **HTMX** requests updating partial HTML content.
*   **`asp:Button` / `asp:LinkButton`:** Used for actions like "Insert", "Edit", "Delete". These will become standard HTML `<button>` elements with **HTMX** attributes to trigger asynchronous operations and open/close modals.
*   **`asp:RequiredFieldValidator`:** This server-side validation will be replicated using Django Forms' built-in validation, which can also be enhanced with client-side feedback (e.g., via Alpine.js or simple HTML5 `required` attributes and browser validation).
*   **`lblMessage`:** This displays status messages. Django's `messages` framework will be used, integrated with HTMX for non-blocking notifications.
*   **`PopUpMsg.js`, `loadingNotifier.js`:** These client-side scripts indicate the need for dynamic UI feedback. This will be handled by **Alpine.js** for modal management and general UI state, coupled with HTMX's loading indicators (`hx-indicator`).

### Step 4: Generate Django Code

We will create a new Django application, for example, named `accounts`, to house this module.

#### 4.1 Models

We define Django models corresponding to the identified database tables. We set `managed = False` and `db_table` to align with the existing database schema. Crucially, business logic for retrieving dependent dropdown data will be placed within the `Country` and `State` models, adhering to the "Fat Model" principle.

**`accounts/models.py`**

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _

class Country(models.Model):
    # CId from ASP.NET's tblCountry.CId, set as primary_key
    cid = models.AutoField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=255, verbose_name=_('Country Name'))

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

    # Business logic: Method to get states related to this country
    def get_states(self):
        """
        Retrieves all states associated with this country, ordered by name.
        """
        return State.objects.filter(country=self).order_by('state_name')

class State(models.Model):
    # SId from inferred tblState.SId, set as primary_key
    sid = models.AutoField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=255, verbose_name=_('State Name'))
    # CountryId from inferred tblState.CountryId, related to Country model
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CountryId', verbose_name=_('Country'))

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

    # Business logic: Method to get cities related to this state
    def get_cities(self):
        """
        Retrieves all cities associated with this state, ordered by name.
        """
        return City.objects.filter(state=self).order_by('city_name')

class City(models.Model):
    # CityId from inferred tblCity.CityId, set as primary_key
    city_id = models.AutoField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=255, verbose_name=_('City Name'))
    # StateId from inferred tblCity.StateId, related to State model
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='StateId', verbose_name=_('State'))

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class Bank(models.Model):
    # Id from ASP.NET's tblACC_Bank.Id, set as primary_key
    id = models.AutoField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, verbose_name=_('Name of Bank'))
    address = models.CharField(db_column='Address', max_length=500, verbose_name=_('Address'))
    # Foreign keys directly map to the integer IDs stored in the ASP.NET table
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='Country', verbose_name=_('Country'))
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='State', verbose_name=_('State'))
    city = models.ForeignKey(City, models.DO_NOTHING, db_column='City', verbose_name=_('City'))
    pin_no = models.CharField(db_column='PINNo', max_length=50, verbose_name=_('PIN No'))
    contact_no = models.CharField(db_column='ContactNo', max_length=50, verbose_name=_('Contact No'))
    fax_no = models.CharField(db_column='FaxNo', max_length=50, verbose_name=_('Fax No'))
    ifsc = models.CharField(db_column='IFSC', max_length=50, verbose_name=_('IFSC Code'))

    class Meta:
        managed = False
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank'
        verbose_name_plural = 'Banks'

    def __str__(self):
        return self.name

    # Business logic: Class method to create a new bank instance
    @classmethod
    def create_bank(cls, name, address, country, state, city, pin_no, contact_no, fax_no, ifsc):
        """Creates and saves a new Bank instance with validation."""
        bank = cls(
            name=name,
            address=address,
            country=country,
            state=state,
            city=city,
            pin_no=pin_no,
            contact_no=contact_no,
            fax_no=fax_no,
            ifsc=ifsc
        )
        bank.full_clean()  # Runs all model field validation rules
        bank.save()
        return bank

    # Business logic: Instance method to update an existing bank
    def update_bank(self, name, address, country, state, city, pin_no, contact_no, fax_no, ifsc):
        """Updates an existing Bank instance with new data and saves it."""
        self.name = name
        self.address = address
        self.country = country
        self.state = state
        self.city = city
        self.pin_no = pin_no
        self.contact_no = contact_no
        self.fax_no = fax_no
        self.ifsc = ifsc
        self.full_clean()
        self.save()

    def clean(self):
        super().clean()
        # Example of custom validation, e.g., checking if country, state, city form a valid hierarchy
        if self.city and self.state and self.city.state != self.state:
            raise ValidationError({'city': _('City does not belong to the selected State.')})
        if self.state and self.country and self.state.country != self.country:
            raise ValidationError({'state': _('State does not belong to the selected Country.')})
```

#### 4.2 Forms

We'll define a Django `ModelForm` for the `Bank` model. Special attention is given to the `Country`, `State`, and `City` fields to implement dynamic dropdown behavior using HTMX.

**`accounts/forms.py`**

```python
from django import forms
from .models import Bank, Country, State, City

class BankForm(forms.ModelForm):
    # Define state and city as ModelChoiceFields with empty querysets initially.
    # These will be populated dynamically based on country/state selection.
    state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        required=True,
        empty_label="Select State",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            # HTMX attributes will be set in the view to handle dynamic updates
        })
    )
    city = forms.ModelChoiceField(
        queryset=City.objects.none(),
        required=True,
        empty_label="Select City",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )

    class Meta:
        model = Bank
        fields = ['name', 'address', 'country', 'state', 'city', 'pin_no', 'contact_no', 'fax_no', 'ifsc']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'address': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'country': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                # HTMX attributes for dynamic updates will be set in the view
            }),
            'pin_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contact_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'fax_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ifsc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'name': 'Name of Bank',
            'pin_no': 'PIN No',
            'contact_no': 'Contact No',
            'fax_no': 'Fax No',
            'ifsc': 'IFSC Code',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate country dropdown initially
        self.fields['country'].queryset = Country.objects.all().order_by('country_name')
        
        # If the form is bound (e.g., POST request or editing an existing instance)
        # and country/state data is available, populate dependent dropdowns
        if self.instance.pk: # For update view (existing instance)
            if self.instance.country:
                self.fields['state'].queryset = self.instance.country.get_states()
            if self.instance.state:
                self.fields['city'].queryset = self.instance.state.get_cities()
        elif 'country' in self.data: # For create view or HTMX-triggered update
            try:
                country_id = int(self.data.get('country'))
                # Populate state queryset based on selected country
                self.fields['state'].queryset = State.objects.filter(country_id=country_id).order_by('state_name')
            except (ValueError, TypeError):
                pass
        
        if 'state' in self.data: # For create view or HTMX-triggered update
            try:
                state_id = int(self.data.get('state'))
                # Populate city queryset based on selected state
                self.fields['city'].queryset = City.objects.filter(state_id=state_id).order_by('city_name')
            except (ValueError, TypeError):
                pass

    def clean(self):
        cleaned_data = super().clean()
        # Additional custom validation can be added here if necessary
        # Example: Ensure IFSC format is valid
        if 'ifsc' in cleaned_data and not cleaned_data['ifsc'].isalnum():
            self.add_error('ifsc', 'IFSC Code must be alphanumeric.')
        return cleaned_data
```

#### 4.3 Views

Views will be thin, primarily handling HTTP requests, passing data to models or forms, and rendering templates. All business logic, including data retrieval for dropdowns, is delegated to the models. HTMX requests will drive dynamic UI updates.

**`accounts/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string
from .models import Bank, Country, State, City
from .forms import BankForm

# Mixin for common HTMX response logic for CRUD operations
class HtmxResponseMixin:
    def form_valid(self, form):
        # Call model method to encapsulate business logic
        if form.instance.pk: # Update
            form.instance.update_bank(
                name=form.cleaned_data['name'],
                address=form.cleaned_data['address'],
                country=form.cleaned_data['country'],
                state=form.cleaned_data['state'],
                city=form.cleaned_data['city'],
                pin_no=form.cleaned_data['pin_no'],
                contact_no=form.cleaned_data['contact_no'],
                fax_no=form.cleaned_data['fax_no'],
                ifsc=form.cleaned_data['ifsc']
            )
        else: # Create
            Bank.create_bank(
                name=form.cleaned_data['name'],
                address=form.cleaned_data['address'],
                country=form.cleaned_data['country'],
                state=form.cleaned_data['state'],
                city=form.cleaned_data['city'],
                pin_no=form.cleaned_data['pin_no'],
                contact_no=form.cleaned_data['contact_no'],
                fax_no=form.cleaned_data['fax_no'],
                ifsc=form.cleaned_data['ifsc']
            )
        
        messages.success(self.request, f'{self.model._meta.verbose_name} saved successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content response with a trigger header
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankList' # Custom event to refresh the table on the client
                }
            )
        return super().form_valid(form) # Fallback for non-HTMX requests

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            # If it's an HTMX request and form is invalid, re-render the form partial with errors
            # This allows HTMX to swap the content of the modal with the form containing validation feedback
            self.set_dropdown_hx_attributes(form) # Re-add HTMX attributes to dropdowns for re-render
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form) # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        # Delegate delete logic to the model or keep it simple in view
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'{self.model._meta.verbose_name} deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankList'
                }
            )
        return response

    def set_dropdown_hx_attributes(self, form):
        # Helper to set HTMX attributes on dropdown fields in the form for dynamic updates
        form.fields['country'].widget.attrs['hx-get'] = reverse_lazy('accounts:get_states')
        form.fields['country'].widget.attrs['hx-target'] = '#id_state'
        form.fields['country'].widget.attrs['hx-swap'] = 'outerHTML'
        form.fields['country'].widget.attrs['hx-indicator'] = '.htmx-indicator'

        form.fields['state'].widget.attrs['hx-get'] = reverse_lazy('accounts:get_cities')
        form.fields['state'].widget.attrs['hx-target'] = '#id_city'
        form.fields['state'].widget.attrs['hx-swap'] = 'outerHTML'
        form.fields['state'].widget.attrs['hx-indicator'] = '.htmx-indicator'

class BankListView(ListView):
    model = Bank
    template_name = 'accounts/bank/list.html'
    context_object_name = 'banks' # Will hold the list of Bank objects

class BankTablePartialView(ListView):
    model = Bank
    template_name = 'accounts/bank/_bank_table.html' # This template contains only the table content
    context_object_name = 'banks'

    def get_queryset(self):
        # Efficiently fetch related Country, State, City objects to avoid N+1 queries
        return Bank.objects.select_related('country', 'state', 'city').order_by('id')

class BankCreateView(HtmxResponseMixin, CreateView):
    model = Bank
    form_class = BankForm
    template_name = 'accounts/bank/_bank_form.html' # This is a partial for the modal
    success_url = reverse_lazy('accounts:bank_list') # Fallback URL

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        self.set_dropdown_hx_attributes(context['form'])
        return context

class BankUpdateView(HtmxResponseMixin, UpdateView):
    model = Bank
    form_class = BankForm
    template_name = 'accounts/bank/_bank_form.html' # This is a partial for the modal
    context_object_name = 'bank'
    success_url = reverse_lazy('accounts:bank_list') # Fallback URL

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass current values for state and city for initial dropdown selection in edit mode
        context['form'].fields['state'].widget.attrs['hx-get'] = f"{reverse_lazy('accounts:get_states')}?selected_state={self.object.state.sid if self.object.state else ''}"
        context['form'].fields['city'].widget.attrs['hx-get'] = f"{reverse_lazy('accounts:get_cities')}?selected_city={self.object.city.city_id if self.object.city else ''}"
        self.set_dropdown_hx_attributes(context['form'])
        return context

class BankDeleteView(HtmxResponseMixin, DeleteView):
    model = Bank
    template_name = 'accounts/bank/_bank_confirm_delete.html' # This is a partial for the modal
    context_object_name = 'bank'
    success_url = reverse_lazy('accounts:bank_list') # Fallback URL

# HTMX partial views for dependent dropdowns
class GetStatesView(View):
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('country')
        selected_state_id = request.GET.get('selected_state') # For pre-selecting in edit mode
        states = State.objects.none()
        if country_id:
            try:
                country = Country.objects.get(cid=country_id)
                states = country.get_states()
            except Country.DoesNotExist:
                pass
        
        # Render only the <select> tag for states with options
        options_html = render_to_string(
            'accounts/bank/_state_options.html',
            {'states': states, 'selected_state_id': selected_state_id}
        )
        # Construct the full <select> tag with correct HTMX attributes for chaining
        return HttpResponse(f'<select name="state" id="id_state" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" hx-get="{reverse_lazy("accounts:get_cities")}" hx-target="#id_city" hx-swap="outerHTML" hx-indicator=".htmx-indicator">{options_html}</select>')

class GetCitiesView(View):
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('state')
        selected_city_id = request.GET.get('selected_city') # For pre-selecting in edit mode
        cities = City.objects.none()
        if state_id:
            try:
                state = State.objects.get(sid=state_id)
                cities = state.get_cities()
            except State.DoesNotExist:
                pass
        
        # Render only the <select> tag for cities with options
        options_html = render_to_string(
            'accounts/bank/_city_options.html',
            {'cities': cities, 'selected_city_id': selected_city_id}
        )
        # The city dropdown does not need further HTMX chaining, so no hx-get
        return HttpResponse(f'<select name="city" id="id_city" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">{options_html}</select>')

```

#### 4.4 Templates

Templates are designed to be modular and integrate seamlessly with HTMX and Alpine.js. They extend a base template (`core/base.html`) for consistent layout and CDN includes.

**`accounts/bank/list.html`** (Main page for displaying the bank list)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Banks</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'accounts:bank_add' %}" {# HTMX GET request to load the add form into the modal #}
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Alpine.js/Hyperscript to show the modal #}
            Add New Bank
        </button>
    </div>
    
    <div id="bankTable-container"
         hx-trigger="load, refreshBankList from:body" {# Load initially and refresh when 'refreshBankList' event is triggered #}
         hx-get="{% url 'accounts:bank_table' %}" {# HTMX GET request to fetch the table partial #}
         hx-swap="innerHTML">
        <!-- Initial loading indicator for the DataTable -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal structure for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me"> {# Click outside modal to close #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup if needed for complex UI state management
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if required for specific client-side interactions
    });

    // Event listener for HTMX requests completing (e.g., after form submission)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        // If a 204 No Content response is received (indicating success without content swap)
        if (evt.detail.xhr.status === 204 && evt.detail.requestHeaders['HX-Request']) {
            // Close the modal
            document.getElementById('modal').classList.remove('is-active');
            // Clear modal content to prepare for next use
            document.getElementById('modalContent').innerHTML = ''; 
        }
    });

    // Event listener for HTMX content being loaded into the modal
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        // If content was loaded into #modalContent by an HTMX request
        if (evt.target.id === 'modalContent' && evt.detail.requestHeaders['HX-Request']) {
            // Ensure the modal is visible
            document.getElementById('modal').classList.add('is-active');
        }
    });
</script>
{% endblock %}
```

**`accounts/bank/_bank_table.html`** (Partial template for the DataTables content, loaded via HTMX)

```html
<table id="bankTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Bank</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Country</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">City</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PIN No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fax No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IFSC Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for bank in banks %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.address }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.country.country_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.state.state_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.city.city_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.pin_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.contact_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.fax_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ bank.ifsc }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'accounts:bank_edit' bank.pk %}" {# HTMX GET request to load the edit form into the modal #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'accounts:bank_delete' bank.pk %}" {# HTMX GET request to load the delete confirmation into the modal #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables after the table content has been loaded by HTMX
// This script block will be evaluated each time hx-get="{% url 'accounts:bank_table' %}" is triggered
$(document).ready(function() {
    $('#bankTable').DataTable({
        "pageLength": 10, // Default number of rows per page
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]] // Options for rows per page
    });
});
</script>
```

**`accounts/bank/_bank_form.html`** (Partial template for Add/Edit forms, loaded into the modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Bank</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# Form submits via HTMX POST, hx-swap="none" to rely on HX-Trigger for UI update #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}
                    <span class="text-red-500">*</span> {# Visual indicator for required fields #}
                    {% endif %}
                </label>
                {{ field }} {# Renders the Django form field with its associated widget #}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p> {# Display validation errors #}
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Close modal using Alpine.js/Hyperscript #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`accounts/bank/_bank_confirm_delete.html`** (Partial template for Delete confirmation, loaded into the modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete the bank: <strong>{{ bank.name }}</strong>?</p>
    <form hx-post="{% url 'accounts:bank_delete' bank.pk %}" hx-swap="none"> {# Form submits via HTMX POST, hx-swap="none" to rely on HX-Trigger #}
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Close modal using Alpine.js/Hyperscript #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`accounts/bank/_state_options.html`** (Partial template for State dropdown options, used by `GetStatesView`)

```html
<option value="">Select State</option>
{% for state_obj in states %}
<option value="{{ state_obj.sid }}" {% if state_obj.sid|stringformat:"s" == selected_state_id %}selected{% endif %}>{{ state_obj.state_name }}</option>
{% endfor %}
```

**`accounts/bank/_city_options.html`** (Partial template for City dropdown options, used by `GetCitiesView`)

```html
<option value="">Select City</option>
{% for city_obj in cities %}
<option value="{{ city_obj.city_id }}" {% if city_obj.city_id|stringformat:"s" == selected_city_id %}selected{% endif %}>{{ city_obj.city_name }}</option>
{% endfor %}
```

#### 4.5 URLs

URL patterns for accessing the views. We'll use Django's `app_name` for namespacing.

**`accounts/urls.py`**

```python
from django.urls import path
from .views import (
    BankListView, BankTablePartialView, BankCreateView,
    BankUpdateView, BankDeleteView, GetStatesView, GetCitiesView
)

app_name = 'accounts' # Defines the application namespace

urlpatterns = [
    # Main list view for banks
    path('bank/', BankListView.as_view(), name='bank_list'),
    
    # HTMX endpoint to get the table content for dynamic updates
    path('bank/table/', BankTablePartialView.as_view(), name='bank_table'), 
    
    # HTMX endpoint for adding a new bank (loads form into modal, handles submission)
    path('bank/add/', BankCreateView.as_view(), name='bank_add'),
    
    # HTMX endpoint for editing an existing bank (loads form into modal, handles submission)
    path('bank/edit/<int:pk>/', BankUpdateView.as_view(), name='bank_edit'),
    
    # HTMX endpoint for deleting a bank (loads confirmation into modal, handles submission)
    path('bank/delete/<int:pk>/', BankDeleteView.as_view(), name='bank_delete'),

    # HTMX endpoint for dynamically populating state dropdown based on country selection
    path('bank/get_states/', GetStatesView.as_view(), name='get_states'), 
    
    # HTMX endpoint for dynamically populating city dropdown based on state selection
    path('bank/get_cities/', GetCitiesView.as_view(), name='get_cities'), 
]

```

#### 4.6 Tests

Comprehensive tests cover model business logic (Fat Model) and all view interactions (including HTMX-specific behavior).

**`accounts/tests.py`**

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.core.exceptions import ValidationError
from .models import Bank, Country, State, City

class ReferenceDataModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create base reference data for all tests
        cls.country1 = Country.objects.create(cid=1, country_name='India')
        cls.state1 = State.objects.create(sid=1, state_name='Maharashtra', country=cls.country1)
        cls.city1 = City.objects.create(city_id=1, city_name='Mumbai', state=cls.state1)

        cls.country2 = Country.objects.create(cid=2, country_name='USA')
        cls.state2 = State.objects.create(sid=2, state_name='California', country=cls.country2)
        cls.city2 = City.objects.create(city_id=2, city_name='Los Angeles', state=cls.state2)

    def test_country_get_states_method(self):
        """Test that get_states returns states only for the specific country."""
        states = self.country1.get_states()
        self.assertIn(self.state1, states)
        self.assertNotIn(self.state2, states)
        self.assertEqual(states.count(), 1) # Assuming only one state per country for simplicity

    def test_state_get_cities_method(self):
        """Test that get_cities returns cities only for the specific state."""
        cities = self.state1.get_cities()
        self.assertIn(self.city1, cities)
        self.assertNotIn(self.city2, cities)
        self.assertEqual(cities.count(), 1) # Assuming only one city per state for simplicity

class BankModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for Bank model tests
        cls.country1 = Country.objects.create(cid=10, country_name='Canada')
        cls.state1 = State.objects.create(sid=10, state_name='Ontario', country=cls.country1)
        cls.city1 = City.objects.create(city_id=10, city_name='Toronto', state=cls.state1)

        cls.bank_data = {
            'name': 'Royal Bank of Canada',
            'address': '200 Bay St, Toronto',
            'country': cls.country1,
            'state': cls.state1,
            'city': cls.city1,
            'pin_no': 'M5J 2W7',
            'contact_no': '123-456-7890',
            'fax_no': '098-765-4321',
            'ifsc': 'RBCN0000001'
        }
        cls.bank1 = Bank.objects.create(id=1, **cls.bank_data)
  
    def test_bank_creation(self):
        """Verify initial bank object creation and field values."""
        bank = Bank.objects.get(id=1)
        self.assertEqual(bank.name, 'Royal Bank of Canada')
        self.assertEqual(bank.country.country_name, 'Canada')
        self.assertEqual(bank.ifsc, 'RBCN0000001')

    def test_bank_str_method(self):
        """Test the string representation of the Bank model."""
        bank = Bank.objects.get(id=1)
        self.assertEqual(str(bank), 'Royal Bank of Canada')
        
    def test_create_bank_class_method(self):
        """Test the custom create_bank class method."""
        new_bank = Bank.create_bank(
            name='Toronto Dominion Bank',
            address='100 Front St, Toronto',
            country=self.country1,
            state=self.state1,
            city=self.city1,
            pin_no='M5J 1E3',
            contact_no='111-222-3333',
            fax_no='444-555-6666',
            ifsc='TDBN0000002'
        )
        self.assertIsNotNone(new_bank.pk)
        self.assertEqual(new_bank.name, 'Toronto Dominion Bank')
        self.assertEqual(Bank.objects.count(), 2) # Verify a new bank was added

    def test_update_bank_instance_method(self):
        """Test the custom update_bank instance method."""
        bank = Bank.objects.get(id=1)
        bank.update_bank(
            name='Royal Bank of Canada (Updated)',
            address='New Address, Toronto',
            country=self.country1,
            state=self.state1,
            city=self.city1,
            pin_no='M5K 1L7',
            contact_no='999-888-7777',
            fax_no='666-555-4444',
            ifsc='RBCN000UPD'
        )
        bank.refresh_from_db() # Reload data from DB to ensure changes are persisted
        self.assertEqual(bank.name, 'Royal Bank of Canada (Updated)')
        self.assertEqual(bank.address, 'New Address, Toronto')
        self.assertEqual(bank.ifsc, 'RBCN000UPD')

    def test_bank_model_clean_method_invalid_city(self):
        """Test custom validation for city not belonging to state."""
        # Create a city belonging to a different state
        country_diff = Country.objects.create(cid=11, country_name='Australia')
        state_diff = State.objects.create(sid=11, state_name='New South Wales', country=country_diff)
        city_diff = City.objects.create(city_id=11, city_name='Sydney', state=state_diff)

        bank = Bank(
            name='Invalid Bank',
            address='Test',
            country=self.country1,
            state=self.state1,
            city=city_diff, # City from different state
            pin_no='123456',
            contact_no='**********',
            fax_no='**********',
            ifsc='TEST0000001'
        )
        with self.assertRaisesMessage(ValidationError, 'City does not belong to the selected State.'):
            bank.full_clean()

    def test_bank_model_clean_method_invalid_state(self):
        """Test custom validation for state not belonging to country."""
        # Create a state belonging to a different country
        country_diff = Country.objects.create(cid=12, country_name='Germany')
        state_diff = State.objects.create(sid=12, state_name='Bavaria', country=country_diff)
        city_diff = City.objects.create(city_id=12, city_name='Munich', state=state_diff)

        bank = Bank(
            name='Invalid Bank 2',
            address='Test',
            country=self.country1,
            state=state_diff, # State from different country
            city=city_diff, 
            pin_no='123456',
            contact_no='**********',
            fax_no='**********',
            ifsc='TEST0000002'
        )
        with self.assertRaisesMessage(ValidationError, 'State does not belong to the selected Country.'):
            bank.full_clean()


class BankViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create reference data for views
        cls.country_test = Country.objects.create(cid=100, country_name='France')
        cls.state_test = State.objects.create(sid=100, state_name='Ile-de-France', country=cls.country_test)
        cls.city_test = City.objects.create(city_id=100, city_name='Paris', state=cls.state_test)

        cls.bank1 = Bank.objects.create(
            id=1, name='Crédit Agricole', address='Rue la Boétie', country=cls.country_test,
            state=cls.state_test, city=cls.city_test, pin_no='75008', contact_no='**********',
            fax_no='**********', ifsc='CRAG0000001'
        )
        cls.bank2 = Bank.objects.create(
            id=2, name='Société Générale', address='Boulevard Haussmann', country=cls.country_test,
            state=cls.state_test, city=cls.city_test, pin_no='75009', contact_no='**********',
            fax_no='**********', ifsc='SOGN0000002'
        )
    
    def setUp(self):
        self.client = Client() # Initialize a new client for each test method
    
    def test_list_view_get(self):
        """Test the main bank list page."""
        response = self.client.get(reverse('accounts:bank_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank/list.html')
        self.assertTrue('banks' in response.context)
        
    def test_bank_table_partial_view_get(self):
        """Test the HTMX partial for the bank table."""
        response = self.client.get(reverse('accounts:bank_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank/_bank_table.html')
        self.assertTrue('banks' in response.context)
        self.assertEqual(len(response.context['banks']), 2)
        self.assertContains(response, 'Crédit Agricole')
        self.assertContains(response, 'Société Générale')

    def test_create_view_get_htmx(self):
        """Test loading the add bank form via HTMX."""
        response = self.client.get(reverse('accounts:bank_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank/_bank_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Bank') # Check for form title

    def test_create_view_post_success_htmx(self):
        """Test successful bank creation via HTMX POST."""
        data = {
            'name': 'New Bank C',
            'address': '100 Main St',
            'country': self.country_test.cid,
            'state': self.state_test.sid,
            'city': self.city_test.city_id,
            'pin_no': '12345',
            'contact_no': '**********',
            'fax_no': '**********',
            'ifsc': 'NEWB0000003'
        }
        response = self.client.post(reverse('accounts:bank_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response for No Content
        self.assertTrue(Bank.objects.filter(name='New Bank C').exists())
        self.assertEqual(Bank.objects.count(), 3) # Verify total count increased
        self.assertIn('HX-Trigger', response.headers) # Check for HTMX trigger header
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankList')

    def test_create_view_post_invalid_htmx(self):
        """Test invalid bank creation via HTMX POST (should return form with errors)."""
        data = { # Missing required fields like 'name'
            'name': '',
            'address': 'Invalid Address',
            'country': self.country_test.cid,
            'state': self.state_test.sid,
            'city': self.city_test.city_id,
        }
        response = self.client.post(reverse('accounts:bank_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors (not 204)
        self.assertTemplateUsed(response, 'accounts/bank/_bank_form.html')
        self.assertContains(response, 'This field is required.') # Check for validation error message
        self.assertEqual(Bank.objects.count(), 2) # No new object created

    def test_update_view_get_htmx(self):
        """Test loading the edit bank form via HTMX."""
        bank = Bank.objects.get(id=1)
        response = self.client.get(reverse('accounts:bank_edit', args=[bank.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank/_bank_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, bank) # Form should be populated with bank data
        
    def test_update_view_post_success_htmx(self):
        """Test successful bank update via HTMX POST."""
        bank = Bank.objects.get(id=1)
        data = {
            'name': 'Crédit Agricole (Updated)',
            'address': 'New Updated Address',
            'country': self.country_test.cid,
            'state': self.state_test.sid,
            'city': self.city_test.city_id,
            'pin_no': '75001',
            'contact_no': '**********',
            'fax_no': '**********',
            'ifsc': 'CRAGUPDATED'
        }
        response = self.client.post(reverse('accounts:bank_edit', args=[bank.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        bank.refresh_from_db()
        self.assertEqual(bank.name, 'Crédit Agricole (Updated)')
        self.assertEqual(bank.address, 'New Updated Address')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankList')

    def test_delete_view_get_htmx(self):
        """Test loading delete confirmation via HTMX."""
        bank = Bank.objects.get(id=1)
        response = self.client.get(reverse('accounts:bank_delete', args=[bank.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/bank/_bank_confirm_delete.html')
        self.assertTrue('bank' in response.context)
        self.assertEqual(response.context['bank'], bank)

    def test_delete_view_post_success_htmx(self):
        """Test successful bank deletion via HTMX POST."""
        bank_count_before = Bank.objects.count()
        bank_to_delete = Bank.objects.get(id=1)
        response = self.client.post(reverse('accounts:bank_delete', args=[bank_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Bank.objects.count(), bank_count_before - 1)
        self.assertFalse(Bank.objects.filter(id=bank_to_delete.id).exists()) # Verify object is gone
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBankList')

    def test_get_states_view_htmx(self):
        """Test HTMX endpoint for getting states based on country."""
        response = self.client.get(reverse('accounts:get_states'), {'country': self.country_test.cid}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        # Should contain the correct state option
        self.assertContains(response, f'<option value="{self.state_test.sid}">{self.state_test.state_name}</option>')
        # Should contain the HTMX attributes for chaining to city dropdown
        self.assertContains(response, 'hx-target="#id_city"')
        self.assertContains(response, 'hx-get="{% url "accounts:get_cities" %}"')

    def test_get_cities_view_htmx(self):
        """Test HTMX endpoint for getting cities based on state."""
        response = self.client.get(reverse('accounts:get_cities'), {'state': self.state_test.sid}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        # Should contain the correct city option
        self.assertContains(response, f'<option value="{self.city_test.city_id}">{self.city_test.city_name}</option>')
        # Should not contain HTMX attributes for further chaining
        self.assertNotContains(response, 'hx-target')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django code provided already incorporates HTMX for all major dynamic interactions.

*   **HTMX for CRUD:**
    *   `hx-get` is used on "Add", "Edit", "Delete" buttons to fetch the respective form/confirmation partials into the `#modalContent` div.
    *   `hx-post` is used on the form itself (within the modal) to submit data asynchronously.
    *   `hx-swap="none"` is used on successful form submissions (`form_valid`) and deletions, as the view returns a `204 No Content` status.
    *   `HX-Trigger: 'refreshBankList'` header is sent by the view on successful CRUD operations. This custom event triggers a `hx-get` on the `bankTable-container` to reload the entire `_bank_table.html` partial, ensuring the DataTables instance is refreshed with the latest data.
*   **HTMX for Dependent Dropdowns:**
    *   In `_bank_form.html`, the `country` dropdown has `hx-get` and `hx-target="#id_state"` attributes (set dynamically in the view's `get_context_data`). When the country changes, HTMX fetches new state options from `accounts:get_states` and swaps them into the `state` dropdown.
    *   Similarly, the `state` dropdown has `hx-get` and `hx-target="#id_city"` to dynamically load city options from `accounts:get_cities`.
    *   `hx-indicator` attributes are used to show loading spinners during AJAX calls.
*   **Alpine.js for Modals:**
    *   The `#modal` div uses simple Hyperscript attributes (`_`) for showing/hiding (e.g., `on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me`).
    *   JavaScript listeners are added to `document.body` for `htmx:afterRequest` and `htmx:afterOnLoad` to manage the modal's visibility after HTMX operations (closing the modal after successful submission, ensuring it's visible when content loads).
*   **DataTables for List Views:**
    *   The `_bank_table.html` partial renders the `<table>` element. A small `<script>` block within this partial initializes DataTables on `$(document).ready()`. This ensures DataTables is correctly re-initialized every time the table content is refreshed via HTMX.
*   **No Custom JavaScript (beyond Alpine.js init and HTMX event listeners):** The solution relies entirely on HTMX attributes and Alpine.js directives for dynamic UI, minimizing the need for bespoke JavaScript logic.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Bank module to Django. By adopting modern practices, leveraging HTMX and Alpine.js, and strictly adhering to the "Fat Model, Thin View" architecture, the resulting application will be more maintainable, scalable, and provide a superior user experience. This systematic, automation-driven approach ensures a smooth transition, reducing manual effort and potential errors. The focus on business benefits, such as improved efficiency, reduced development costs, and a more responsive user interface, makes this a valuable modernization initiative.