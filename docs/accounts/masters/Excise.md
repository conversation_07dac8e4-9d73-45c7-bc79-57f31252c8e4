## ASP.NET to Django Conversion Script: Excise/Service Tax Module

This document outlines a comprehensive plan for modernizing your existing ASP.NET Excise/Service Tax module to a robust and scalable Django 5.0+ application. Our approach leverages AI-assisted automation to streamline the transition, focusing on business value and maintainability. We will adopt a "Fat Model, Thin View" architecture, utilizing Django's powerful ORM, HTMX for dynamic frontend interactions, Alpine.js for lightweight UI state management, and DataTables for enhanced data presentation.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
From the `SqlDataSource1` component, we identify the following:
- **Table Name:** `tblExciseser_Master`
- **Columns and Inferred Types:**
    - `Id`: Integer (Primary Key, auto-incrementing)
    - `Terms`: String
    - `Value`: String (represents a decimal number with up to 15 digits before and 3 after the decimal point, required)
    - `AccessableValue`: String (represents a decimal number, same format, required)
    - `EDUCess`: String (represents a decimal number, same format, required)
    - `SHECess`: String (represents a decimal number, same format, required)
    - `Live`: Integer (0 or 1, represents a boolean for 'Default Excise')
    - `LiveSerTax`: Integer (0 or 1, represents a boolean for 'Default Service Tax')

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
- **Create (Insert):** Handled by `GridView1_RowCommand` (for both `Add` from footer and `Add1` from EmptyDataTemplate) which calls `SqlDataSource1.Insert()`.
- **Read (Select):** Handled by `GridView1` data binding via `SqlDataSource1` using `SELECT * FROM [tblExciseser_Master] order by [Id] desc`.
- **Update:** Handled by `GridView1_RowUpdating` which calls `SqlDataSource1.Update()`.
- **Delete:** Handled by `GridView1_RowDeleted` which calls `SqlDataSource1.Delete()`.
- **Validation Logic:** Input validation is present for numeric fields using `RegularExpressionValidator` (`^\d{1,15}(\.\d{0,3})?$`) and `RequiredFieldValidator`. A custom `fun.NumberValidationQty` function is also used.
- **Unique 'Default' Flag Business Logic:** A critical rule states that if `Default Excise` (`Live`) is set to true for a record, all *other* records in the table must have their `Default Excise` flag set to false. The same applies to `Default Service Tax` (`LiveSerTax`). This ensures only one entry can be "default" for each type at a time. This logic is implemented in `GridView1_RowCommand` and `GridView1_RowUpdating` methods.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django template mapping.

**Instructions:**
- **`asp:GridView` (`GridView1`):** This is the core data display and interaction component. It will be replaced by a standard HTML `<table>` enhanced with `DataTables.js` for client-side functionality and populated via Django template loops.
    - **Pagination:** Handled by DataTables.
    - **Editing/Deleting:** Action buttons (Edit/Delete links) in each row will trigger HTMX requests to open modals for update/delete.
    - **Adding New Records:** An "Insert" button in the footer/empty template will trigger an HTMX request to open a modal for creating a new record.
- **`asp:TextBox`, `asp:Label`, `asp:CheckBox`:** These are standard input/display controls. They will map directly to Django form fields rendered with appropriate HTML input types and styled with Tailwind CSS.
- **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`:** These server-side validations will be handled by Django's ModelForm validation and model field types (e.g., `DecimalField` for numeric validation, `required=True` for mandatory fields).
- **Client-side `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()` JavaScript functions:** These will be replaced by HTMX's confirmation attributes (`hx-confirm`) or handled by the modal patterns using Alpine.js.

---

### Step 4: Generate Django Code

We will create a new Django application named `accounts_master` to house this module.

#### 4.1 Models (`accounts_master/models.py`)

**Task:** Create a Django model representing the `tblExciseser_Master` table, incorporating the unique 'default' flag business logic.

```python
from django.db import models, transaction

class ExciseMaster(models.Model):
    # 'Id' is the primary key in the ASP.NET database. Django automatically creates an 'id' field,
    # but when managed=False, it's good practice to explicitly define the PK to match the legacy table.
    id = models.AutoField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255) # Assuming a reasonable max length for terms
    
    # Decimal fields to store numeric values with precision as indicated by ASP.NET regex
    value = models.DecimalField(db_column='Value', max_digits=18, decimal_places=3)
    assessable_value = models.DecimalField(db_column='AccessableValue', max_digits=18, decimal_places=3)
    edu_cess = models.DecimalField(db_column='EDUCess', max_digits=18, decimal_places=3)
    she_cess = models.DecimalField(db_column='SHECess', max_digits=18, decimal_places=3)
    
    # Boolean fields for 'Live' (Default Excise) and 'LiveSerTax' (Default Service Tax)
    default_excise = models.BooleanField(db_column='Live', default=False)
    default_service_tax = models.BooleanField(db_column='LiveSerTax', default=False)

    class Meta:
        managed = False  # Tells Django not to manage table creation/schema modifications
        db_table = 'tblExciseser_Master'  # Maps to the existing database table
        verbose_name = 'Excise/Service Tax Entry'
        verbose_name_plural = 'Excise/Service Tax Entries'
        ordering = ['-id']  # Matches 'order by [Id] desc' from the original SqlDataSource

    def __str__(self):
        """Returns a human-readable representation of the object."""
        return f"Excise/Service Tax: {self.terms}"

    def save(self, *args, **kwargs):
        """
        Overrides the default save method to implement the business logic for
        ensuring only one 'Default Excise' and 'Default Service Tax' entry is active at a time.
        When this record is set as default_excise or default_service_tax,
        all other records should have their respective flags set to False.
        """
        with transaction.atomic():
            # Handle Default Excise logic: If this instance is being set as default,
            # clear the flag on all other existing records.
            if self.default_excise:
                # Exclude the current instance if it already exists (for updates)
                ExciseMaster.objects.exclude(pk=self.pk).update(default_excise=False)
            
            # Handle Default Service Tax logic: Similar logic for default_service_tax.
            if self.default_service_tax:
                # Exclude the current instance if it already exists (for updates)
                ExciseMaster.objects.exclude(pk=self.pk).update(default_service_tax=False)
            
            # Call the original save method to save the current instance
            super().save(*args, **kwargs)

    # Example of a 'fat model' method for potential future business logic:
    def get_display_status(self):
        """Returns a string representing the default status."""
        status = []
        if self.default_excise:
            status.append("Live Excise")
        if self.default_service_tax:
            status.append("Live Service Tax")
        return ", ".join(status) if status else "Inactive"

```

#### 4.2 Forms (`accounts_master/forms.py`)

**Task:** Define a Django ModelForm for `ExciseMaster` to handle user input and validation, applying Tailwind CSS classes.

```python
from django import forms
from .models import ExciseMaster

class ExciseMasterForm(forms.ModelForm):
    class Meta:
        model = ExciseMaster
        fields = [
            'terms', 'value', 'assessable_value', 'edu_cess',
            'she_cess', 'default_excise', 'default_service_tax'
        ]
        # Widgets apply Tailwind CSS classes for consistent styling
        widgets = {
            'terms': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}), # 'step' helps with decimal input
            'assessable_value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'edu_cess': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'she_cess': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'default_excise': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'default_service_tax': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        
    # ModelForm automatically handles required fields and numeric validation based on model definition.
    # No custom validation methods are explicitly needed here as DecimalField and BooleanField
    # along with 'required' attribute (default for non-null fields) cover the ASP.NET validators.
```

#### 4.3 Views (`accounts_master/views.py`)

**Task:** Implement CRUD operations using Django's Class-Based Views (CBVs). Views are kept thin (5-15 lines) by offloading business logic to the model's `save` method and relying on Django's built-in form handling. HTMX is integrated for dynamic partial updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # Used for rendering HTMX partials
from .models import ExciseMaster
from .forms import ExciseMasterForm

# Base view to display the main page containing the HTMX container for the table.
class ExciseMasterListView(ListView):
    model = ExciseMaster
    template_name = 'accounts_master/excisemaster/list.html'
    context_object_name = 'excisemasters' # Variable name for the list of objects in the template

# HTMX partial view to render only the DataTables content.
# This view is loaded via HTMX into the list.html container.
class ExciseMasterTablePartialView(View):
    def get(self, request, *args, **kwargs):
        excisemasters = ExciseMaster.objects.all() # Fetch all excise master entries
        context = {'excisemasters': excisemasters}
        return HttpResponse(render_to_string('accounts_master/excisemaster/_excisemaster_table.html', context, request))

# HTMX partial view for creating a new ExciseMaster entry (modal form).
class ExciseMasterCreateView(CreateView):
    model = ExciseMaster
    form_class = ExciseMasterForm
    template_name = 'accounts_master/excisemaster/_excisemaster_form.html' # Uses a partial template for the modal form
    
    def form_valid(self, form):
        # Save the form instance (triggers model's custom save logic)
        response = super().form_valid(form)
        messages.success(self.request, 'Excise/Service Tax entry added successfully.')
        # HTMX response: Status 204 (No Content) with a trigger to refresh the list
        # and display a message.
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': '{"refreshExciseMasterList":true, "showMessage":"Record Inserted"}'
            }
        )
    
    def form_invalid(self, form):
        # If form is invalid, re-render the form with errors, which HTMX will swap back.
        return self.render_to_response(self.get_context_data(form=form))


# HTMX partial view for updating an existing ExciseMaster entry (modal form).
class ExciseMasterUpdateView(UpdateView):
    model = ExciseMaster
    form_class = ExciseMasterForm
    template_name = 'accounts_master/excisemaster/_excisemaster_form.html' # Uses a partial template for the modal form

    def form_valid(self, form):
        # Save the form instance (triggers model's custom save logic)
        response = super().form_valid(form)
        messages.success(self.request, 'Excise/Service Tax entry updated successfully.')
        # HTMX response: Status 204 (No Content) with a trigger to refresh the list
        # and display a message.
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': '{"refreshExciseMasterList":true, "showMessage":"Record Updated"}'
            }
        )

    def form_invalid(self, form):
        # If form is invalid, re-render the form with errors, which HTMX will swap back.
        return self.render_to_response(self.get_context_data(form=form))


# HTMX partial view for deleting an ExciseMaster entry (modal confirmation).
class ExciseMasterDeleteView(DeleteView):
    model = ExciseMaster
    template_name = 'accounts_master/excisemaster/_excisemaster_confirm_delete.html' # Uses a partial template for the modal confirmation

    def delete(self, request, *args, **kwargs):
        # Perform the delete operation
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Excise/Service Tax entry deleted successfully.')
        # HTMX response: Status 204 (No Content) with a trigger to refresh the list
        # and display a message.
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': '{"refreshExciseMasterList":true, "showMessage":"Record Deleted"}'
            }
        )
```

#### 4.4 Templates

**Task:** Create specific HTML templates for each view, leveraging HTMX for dynamic content loading and DataTables for data presentation.

##### List Template (`accounts_master/templates/accounts_master/excisemaster/list.html`)

This is the main page that serves as the container for the HTMX-powered DataTables and modal forms.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Excise/Service Tax Entries</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'excisemaster_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Alpine.js/Hyperscript to show modal #}
            Add New Entry
        </button>
    </div>
    
    <div id="excisemasterTable-container"
         hx-trigger="load, refreshExciseMasterList from:body" {# Load on page load and on custom event #}
         hx-get="{% url 'excisemaster_table' %}" {# Fetches the table partial #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Excise/Service Tax Entries...</p>
        </div>
    </div>
    
    <!-- Modal structure for forms and delete confirmations -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"> {# Click outside modal to close #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# Include DataTables and HTMX/Alpine.js CDN links in your base.html #}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js can be used here for more complex UI state management,
        // but for simple modal open/close, Hyperscript directly on elements is sufficient.
    });

    // Custom event listener for showing messages (e.g., from HTMX triggers)
    document.body.addEventListener('showMessage', function(evt) {
        const message = evt.detail.value;
        // In a real application, replace this with a more user-friendly toast/notification system
        console.log("Message from server:", message);
        alert(message); 
    });
</script>
{% endblock %}
```

##### Table Partial Template (`accounts_master/templates/accounts_master/excisemaster/_excisemaster_table.html`)

This partial template contains the HTML structure for the DataTables. It is dynamically loaded and refreshed by HTMX.

```html
<table id="excisemasterTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Access. Value</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">EDU Cess</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SHE Cess</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Default Excise</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Default Ser. Tax</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for obj in excisemasters %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.terms }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.value }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.assessable_value }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.edu_cess }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.she_cess }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.default_excise|yesno:"Live," }}</td> {# Display "Live" for True, empty for False matching ASP.NET #}
            <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.default_service_tax|yesno:"Live," }}</td> {# Display "Live" for True, empty for False matching ASP.NET #}
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'excisemaster_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'excisemaster_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-4 px-4 text-center text-gray-500">No Excise/Service Tax entries found. Click "Add New Entry" to create one.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables after the table is loaded by HTMX
$(document).ready(function() {
    $('#excisemasterTable').DataTable({
        "pageLength": 10, // Default items per page
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for items per page
        "columnDefs": [
            { "orderable": false, "targets": [0, 8] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

##### Form Partial Template (`accounts_master/templates/accounts_master/excisemaster/_excisemaster_form.html`)

This template renders the form for both creating and updating records within the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Excise/Service Tax Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner"> {# HTMX form submission, no swap on success #}
        {% csrf_token %} {# Django's security token for forms #}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }} {# Renders the form field with its widget #}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p> {# Display field-specific errors #}
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Close modal on cancel #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-2"> {# HTMX loading indicator #}
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

##### Delete Confirmation Partial Template (`accounts_master/templates/accounts_master/excisemaster/_excisemaster_confirm_delete.html`)

This template provides a confirmation dialog for record deletion within the modal.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4">Are you sure you want to delete the entry for "<strong>{{ object.terms }}</strong>"? This action cannot be undone.</p>
    <form hx-delete="{% url 'excisemaster_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner"> {# HTMX DELETE request #}
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Close modal on cancel #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-2"> {# HTMX loading indicator #}
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts_master/urls.py`)

**Task:** Define URL patterns for the `ExciseMaster` module, mapping them to the corresponding Django views.

```python
from django.urls import path
from .views import (
    ExciseMasterListView,
    ExciseMasterTablePartialView,
    ExciseMasterCreateView,
    ExciseMasterUpdateView,
    ExciseMasterDeleteView,
)

urlpatterns = [
    # Main list view for the Excise/Service Tax page (full page load)
    path('excise/', ExciseMasterListView.as_view(), name='excisemaster_list'),

    # HTMX endpoints for dynamic loading and interactions:
    # This path returns the DataTables HTML partial, loaded into list.html via HTMX.
    path('excise/table/', ExciseMasterTablePartialView.as_view(), name='excisemaster_table'),
    # Path to open the 'Add New Entry' form in a modal.
    path('excise/add/', ExciseMasterCreateView.as_view(), name='excisemaster_add'),
    # Path to open the 'Edit Entry' form in a modal for a specific entry (identified by primary key).
    path('excise/edit/<int:pk>/', ExciseMasterUpdateView.as_view(), name='excisemaster_edit'),
    # Path to open the 'Delete Confirmation' dialog in a modal for a specific entry.
    path('excise/delete/<int:pk>/', ExciseMasterDeleteView.as_view(), name='excisemaster_delete'),
]
```

#### 4.6 Tests (`accounts_master/tests.py`)

**Task:** Write comprehensive unit tests for the `ExciseMaster` model and integration tests for all associated views to ensure functionality and business logic are correctly implemented.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import ExciseMaster
from decimal import Decimal # Used for precise decimal comparisons

class ExciseMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """
        Set up non-modified objects used by all test methods for this class.
        Creates two ExciseMaster entries for testing unique 'default' flags.
        """
        cls.entry1 = ExciseMaster.objects.create(
            terms='Standard Excise',
            value=Decimal('10.000'),
            assessable_value=Decimal('100.000'),
            edu_cess=Decimal('0.200'),
            she_cess=Decimal('0.100'),
            default_excise=False,
            default_service_tax=False,
        )
        cls.entry2 = ExciseMaster.objects.create(
            terms='Special Service Tax',
            value=Decimal('5.000'),
            assessable_value=Decimal('50.000'),
            edu_cess=Decimal('0.100'),
            she_cess=Decimal('0.050'),
            default_excise=False,
            default_service_tax=False,
        )
  
    def test_excisemaster_creation(self):
        """Verify that an ExciseMaster object is created correctly."""
        obj = ExciseMaster.objects.get(pk=self.entry1.pk)
        self.assertEqual(obj.terms, 'Standard Excise')
        self.assertEqual(obj.value, Decimal('10.000'))
        self.assertEqual(obj.assessable_value, Decimal('100.000'))
        self.assertEqual(obj.edu_cess, Decimal('0.200'))
        self.assertEqual(obj.she_cess, Decimal('0.100'))
        self.assertFalse(obj.default_excise)
        self.assertFalse(obj.default_service_tax)
        
    def test_terms_label(self):
        """Check the verbose name for the 'terms' field."""
        field_label = self.entry1._meta.get_field('terms').verbose_name
        self.assertEqual(field_label, 'terms') # Django's default if not explicitly set on field
        
    def test_model_verbose_name_plural(self):
        """Check the verbose name plural for the model."""
        self.assertEqual(ExciseMaster._meta.verbose_name_plural, 'Excise/Service Tax Entries')

    def test_str_representation(self):
        """Check the __str__ method of the model."""
        self.assertEqual(str(self.entry1), "Excise/Service Tax: Standard Excise")

    def test_save_default_excise_uniqueness(self):
        """
        Tests the business logic in the save method for 'default_excise'.
        When one entry is set as default_excise, all others should become false.
        """
        # Set entry1 as default_excise
        self.entry1.default_excise = True
        self.entry1.save()

        # Reload entry1 to confirm it's still True
        reloaded_entry1 = ExciseMaster.objects.get(pk=self.entry1.pk)
        self.assertTrue(reloaded_entry1.default_excise)

        # Reload entry2 to confirm it's False (was updated by entry1's save)
        reloaded_entry2 = ExciseMaster.objects.get(pk=self.entry2.pk)
        self.assertFalse(reloaded_entry2.default_excise)

        # Now set entry2 as default_excise, entry1 should become false
        self.entry2.default_excise = True
        self.entry2.save()

        reloaded_entry1 = ExciseMaster.objects.get(pk=self.entry1.pk)
        self.assertFalse(reloaded_entry1.default_excise)
        reloaded_entry2 = ExciseMaster.objects.get(pk=self.entry2.pk)
        self.assertTrue(reloaded_entry2.default_excise)

    def test_save_default_service_tax_uniqueness(self):
        """
        Tests the business logic in the save method for 'default_service_tax'.
        Similar to default_excise, ensures only one is true at a time.
        """
        # Set entry1 as default_service_tax
        self.entry1.default_service_tax = True
        self.entry1.save()

        # Reload entry1 to confirm it's still True
        reloaded_entry1 = ExciseMaster.objects.get(pk=self.entry1.pk)
        self.assertTrue(reloaded_entry1.default_service_tax)

        # Reload entry2 to confirm it's False
        reloaded_entry2 = ExciseMaster.objects.get(pk=self.entry2.pk)
        self.assertFalse(reloaded_entry2.default_service_tax)

        # Now set entry2 as default_service_tax, entry1 should become false
        self.entry2.default_service_tax = True
        self.entry2.save()

        reloaded_entry1 = ExciseMaster.objects.get(pk=self.entry1.pk)
        self.assertFalse(reloaded_entry1.default_service_tax)
        reloaded_entry2 = ExciseMaster.objects.get(pk=self.entry2.pk)
        self.assertTrue(reloaded_entry2.default_service_tax)
    
    def test_get_display_status_method(self):
        """Test the custom method for displaying status."""
        entry = ExciseMaster.objects.create(
            terms='Test Status', value=Decimal('1'), assessable_value=Decimal('10'),
            edu_cess=Decimal('0.1'), she_cess=Decimal('0.05'),
            default_excise=False, default_service_tax=False
        )
        self.assertEqual(entry.get_display_status(), "Inactive")

        entry.default_excise = True
        entry.save()
        self.assertEqual(entry.get_display_status(), "Live Excise")

        entry.default_service_tax = True
        entry.save()
        self.assertEqual(entry.get_display_status(), "Live Excise, Live Service Tax") # Note: Current logic doesn't make both mutually exclusive in model. This is correct based on original.


class ExciseMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        """
        Sets up a single ExciseMaster entry to be used by all view tests.
        """
        cls.entry = ExciseMaster.objects.create(
            terms='Initial Test Entry',
            value=Decimal('20.000'),
            assessable_value=Decimal('200.000'),
            edu_cess=Decimal('0.400'),
            she_cess=Decimal('0.200'),
            default_excise=False,
            default_service_tax=False,
        )
    
    def setUp(self):
        """Sets up a fresh client for each test method."""
        self.client = Client()
    
    def test_list_view_get(self):
        """Test the main list page loads correctly."""
        response = self.client.get(reverse('excisemaster_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisemaster/list.html')
        self.assertTrue('excisemasters' in response.context)
        self.assertContains(response, 'Excise/Service Tax Entries')
        
    def test_table_partial_view_get(self):
        """Test the HTMX partial for the DataTables content loads correctly."""
        response = self.client.get(reverse('excisemaster_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisemaster/_excisemaster_table.html')
        self.assertContains(response, self.entry.terms)
        self.assertContains(response, 'id="excisemasterTable"') # Confirm DataTables ID is present

    def test_create_view_get(self):
        """Test that the add form can be retrieved via HTMX."""
        response = self.client.get(reverse('excisemaster_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisemaster/_excisemaster_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Excise/Service Tax Entry')
        
    def test_create_view_post_success(self):
        """Test successful creation of a new ExciseMaster entry via HTMX POST."""
        data = {
            'terms': 'New Entry',
            'value': '15.000',
            'assessable_value': '150.000',
            'edu_cess': '0.300',
            'she_cess': '0.150',
            'default_excise': 'on', # Checkbox sends 'on' when checked
            'default_service_tax': '', # Empty string when unchecked
        }
        # Simulate HTMX request
        response = self.client.post(reverse('excisemaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects No Content for success
        self.assertIn('HX-Trigger', response.headers) # Verify HTMX-Trigger header
        self.assertTrue(ExciseMaster.objects.filter(terms='New Entry').exists())
        # Verify the business logic: old default_excise should be false
        self.entry.refresh_from_db()
        self.assertFalse(self.entry.default_excise)


    def test_create_view_post_invalid(self):
        """Test form submission with invalid data via HTMX POST."""
        data = {
            'terms': '', # Invalid: required field
            'value': 'invalid_number', # Invalid: not a number
        }
        response = self.client.post(reverse('excisemaster_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts_master/excisemaster/_excisemaster_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Enter a number.')

    def test_update_view_get(self):
        """Test that the edit form can be retrieved via HTMX."""
        response = self.client.get(reverse('excisemaster_edit', args=[self.entry.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisemaster/_excisemaster_form.html')
        self.assertContains(response, 'Edit Excise/Service Tax Entry')
        self.assertContains(response, self.entry.terms) # Verify existing data is pre-filled
        
    def test_update_view_post_success(self):
        """Test successful update of an ExciseMaster entry via HTMX POST."""
        updated_data = {
            'terms': 'Updated Test Entry',
            'value': '25.000',
            'assessable_value': '250.000',
            'edu_cess': '0.500',
            'she_cess': '0.250',
            'default_excise': '',
            'default_service_tax': 'on',
        }
        response = self.client.post(reverse('excisemaster_edit', args=[self.entry.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.entry.refresh_from_db() # Refresh object to get latest data from DB
        self.assertEqual(self.entry.terms, 'Updated Test Entry')
        self.assertTrue(self.entry.default_service_tax)

    def test_delete_view_get(self):
        """Test that the delete confirmation dialog can be retrieved via HTMX."""
        response = self.client.get(reverse('excisemaster_delete', args=[self.entry.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_master/excisemaster/_excisemaster_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, f'Are you sure you want to delete the entry for "<strong>{self.entry.terms}</strong>"?')
        
    def test_delete_view_post_success(self):
        """Test successful deletion of an ExciseMaster entry via HTMX DELETE."""
        delete_pk = self.entry.pk # Store PK before deletion
        response = self.client.delete(reverse('excisemaster_delete', args=[delete_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertFalse(ExciseMaster.objects.filter(pk=delete_pk).exists()) # Verify object is deleted
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Content:**
    - The main `list.html` uses `hx-get` to load the `_excisemaster_table.html` partial on page load and on a custom `refreshExciseMasterList` event.
    - Add, Edit, and Delete buttons use `hx-get` to fetch their respective forms/confirmations into a modal (`#modalContent`).
    - Form submissions (POST/DELETE) use `hx-post` or `hx-delete` with `hx-swap="none"` and `hx-trigger` headers. The `HX-Trigger` header signals the client to dispatch a `refreshExciseMasterList` event (and `showMessage` event), causing the table to reload without a full page refresh.
- **Alpine.js for UI State (or Hyperscript):**
    - The modal's visibility (`hidden` class toggle) is primarily managed using Hyperscript (`_=` attributes) directly on the elements, which integrates seamlessly with HTMX. This avoids the need for explicit Alpine.js components for simple modal behavior.
    - A basic JavaScript event listener for `showMessage` demonstrates how to handle custom HTMX triggers for user feedback (e.g., toast notifications).
- **DataTables for List Views:**
    - The `_excisemaster_table.html` partial includes a `script` block to initialize DataTables on the table with ID `excisemasterTable`. This ensures client-side search, sort, and pagination.
    - `columnDefs` are used to disable sorting on 'SN' and 'Actions' columns, as per common UI/UX practices.
- **No Full Page Reloads:** All CRUD operations (add, edit, delete) are designed to happen asynchronously within a modal and update the main table dynamically without full page refreshes, providing a smooth user experience.

---

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Excise/Service Tax module to a modern Django application. By adhering to the principles of fat models, thin views, HTMX-driven frontend, and comprehensive testing, the resulting application will be highly maintainable, scalable, and offer a superior user experience, aligning perfectly with modern web development best practices. The focus on AI-assisted automation in this framework reduces manual effort and accelerates the modernization process, providing significant business value by transforming legacy systems into efficient, modern solutions.