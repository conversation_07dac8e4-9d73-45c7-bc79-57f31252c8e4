This document outlines a comprehensive plan for modernizing your existing ASP.NET application, specifically the "Octori" module, to a contemporary Django-based solution. Our approach prioritizes AI-assisted automation, ensuring a smooth and efficient transition while adopting best practices like "Fat Model, Thin View" architecture, HTMX for dynamic interactions, Alpine.js for frontend state, and DataTables for superior data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET `SqlDataSource1` directly reveals the database interactions.
- **Table Name:** `tblOctroi_Master`
- **Columns:**
    - `Id`: Identified as the primary key (`DataKeyNames="Id"`), used in WHERE clauses for update/delete. It's an integer.
    - `Terms`: A string column, used for text input.
    - `Value`: A string column in the database context, but strictly validated in the UI and code-behind as a numeric value (`^\d{1,15}(\.\d{0,3})?$`), indicating a decimal number with up to 15 total digits and 3 decimal places.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD (Create, Read, Update, Delete) operations in the ASP.NET code.

**Instructions:**
- **Create (Add):** The `GridView1_RowCommand` event handles "Add" and "Add1" commands, retrieving `Terms` and `Value` from footer/empty templates and using `SqlDataSource1.Insert()` to add new records.
- **Read (Display):** The `GridView1` is populated using `SqlDataSource1.SelectCommand="SELECT * FROM [tblOctroi_Master] order by [Id] desc"`, displaying all existing Octroi entries.
- **Update (Edit):** The `GridView1_RowUpdating` event handles updates. Notably, it bypasses the `SqlDataSource` for updates, manually constructing and executing an `UPDATE` SQL query to modify `Terms` and `Value` for a specific `Id`.
- **Delete:** The `GridView1_RowDeleted` event and `SqlDataSource1.DeleteCommand` handle the removal of records based on `Id`.
- **Validation Logic:**
    - **Required Fields:** `Terms` and `Value` are marked as required using `RequiredFieldValidator`.
    - **Numeric Format:** `Value` is validated with a `RegularExpressionValidator` and server-side `fun.NumberValidationQty()`, ensuring it's a number (`^\d{1,15}(\.\d{0,3})?$`). This regex implies it should be a positive decimal number with up to 15 total digits and 3 decimal places.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
- **`GridView1`:** This control, coupled with `yui-datatable.css`, is a strong indicator for implementing DataTables in Django to provide client-side searching, sorting, and pagination for the list of Octroi entries.
- **`TextBox` (e.g., `txtTerms1`, `txtValue1`, `txtTerms2`, `txtValue2`, `txtTerms3`, `txtValue3`):** These are input fields for `Terms` and `Value`. In Django, these will translate to standard HTML `<input>` tags rendered by Django forms, styled with Tailwind CSS.
- **`Button` / `LinkButton` (e.g., `btnInsert`, `CommandField` for Edit/Delete):** These trigger actions. In Django, they will be HTML `<button>` elements enhanced with HTMX attributes to perform dynamic operations (e.g., loading forms into a modal, submitting data, triggering list refreshes).
- **`lblMessage`:** A simple label for displaying success messages. Django's `messages` framework will handle this, displayed dynamically via HTMX.
- **Client-side JavaScript (`PopUpMsg.js`, `loadingNotifier.js`, `confirmationAdd`, `confirmationUpdate`, `confirmationDelete`):** These indicate client-side interactions and confirmations. HTMX's declarative attributes (`hx-confirm`, `hx-indicator`) and Alpine.js for modal state management will replace these functionalities, reducing reliance on custom JavaScript.

---

### Step 4: Generate Django Code

For the Django application, we will create a module named `accounts` to house the `Octroi` functionality, aligning with the original ASP.NET module path `Module_Accounts_Masters_Octori`.

#### 4.1 Models (accounts/models.py)

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `Octroi` model will directly map to the `tblOctroi_Master` table. The `Id` column from the database will be explicitly mapped to Django's `id` field as the primary key. `Terms` will be a `CharField` and `Value` will be a `DecimalField` to accurately represent its numeric nature and validation constraints.

```python
from django.db import models
from decimal import Decimal
from django.core.exceptions import ValidationError

class Octroi(models.Model):
    # 'Id' in tblOctroi_Master is the primary key.
    # We map it to Django's 'id' field for consistency, using db_column to link.
    # For managed=False models, if the DB's Id is IDENTITY (auto-incrementing),
    # you might typically omit 'id' in create() calls, allowing the DB to assign.
    # For robust testing with managed=False, explicit ID setting is often helpful.
    id = models.IntegerField(db_column='Id', primary_key=True)
    
    # 'Terms' from ASP.NET
    terms = models.CharField(db_column='Terms', max_length=255) # Assuming a reasonable max length

    # 'Value' from ASP.NET, validated as decimal with max 15 digits total, 3 decimal places.
    # Django's DecimalField precisely matches this requirement.
    value = models.DecimalField(db_column='Value', max_digits=15, decimal_places=3)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblOctroi_Master'  # Explicitly points to the existing database table
        verbose_name = 'Octroi Entry'
        verbose_name_plural = 'Octroi Entries'
        # Mirror the ASP.NET 'order by [Id] desc'
        ordering = ['-id']

    def __str__(self):
        return f"Octroi Terms: {self.terms} | Value: {self.value}"

    # No complex business logic was identified beyond field validation,
    # which is handled effectively by the form in the 'clean_value' method.
    # If there were complex calculations or multi-field dependencies, they would be placed here.
```

#### 4.2 Forms (accounts/forms.py)

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for the `Octroi` model. It will include `terms` and `value` fields. Tailwind CSS classes will be applied via `widgets`. Custom validation will be added for the `value` field to ensure it is positive, replicating the `fun.NumberValidationQty()` check from the ASP.NET code-behind. The `DecimalField` type in the model, combined with `NumberInput` widget, provides robust client-side and server-side numeric validation matching the original regex.

```python
from django import forms
from .models import Octroi
from django.core.exceptions import ValidationError
import re # Not strictly needed with NumberInput for basic numeric, but good for custom regex if raw input was required.

class OctroiForm(forms.ModelForm):
    class Meta:
        model = Octroi
        fields = ['terms', 'value'] # 'id' is typically excluded as it's the primary key and often auto-generated
        widgets = {
            'terms': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter terms'
            }),
            'value': forms.NumberInput(attrs={ # Use NumberInput for better UX and built-in browser numeric validation
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter value',
                'step': '0.001', # Allows input with up to 3 decimal places
                'min': '0.001' # Optional: to enforce strictly positive input at client side
            }),
        }
        
    def clean_value(self):
        value = self.cleaned_data['value']
        
        # Replicating the ASP.NET fun.NumberValidationQty check for positivity.
        # DecimalField and NumberInput already handle general numeric parsing and max_digits/decimal_places.
        if value is not None and value <= 0:
            raise ValidationError("Value must be a positive number.")
            
        return value
```

#### 4.3 Views (accounts/views.py)

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs).

**Instructions:**
Views will be kept lean by leveraging Django's generic CBVs (ListView, CreateView, UpdateView, DeleteView). A custom `HtmxMessageMixin` is introduced to handle success messages and HTMX-specific responses (e.html. 204 No Content with `HX-Trigger`) after successful form submissions or deletions, ensuring dynamic updates without full page reloads. A `OctroiTablePartialView` is added to serve just the table content, allowing HTMX to refresh only that specific part of the page.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404

# A reusable mixin to handle HTMX responses and messages for form success/failure
class HtmxMessageMixin:
    """
    Mixin to send HTMX-specific responses (204 No Content with HX-Trigger)
    and Django messages after successful form operations or deletions.
    It also re-renders the form with errors on invalid HTMX POST requests.
    """
    success_message = "" # To be defined by the inheriting view

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, self.success_message)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOctroiList' # Custom event for HTMX to listen to
                }
            )
        return response

    def form_invalid(self, form):
        # If an HTMX request fails validation, re-render the form with errors.
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

    def delete(self, request, *args, **kwargs):
        try:
            response = super().delete(request, *args, **kwargs)
            messages.success(self.request, self.success_message)
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshOctroiList'
                    }
                )
            return response
        except Http404:
            # Handle cases where object to delete does not exist (e.g., already deleted)
            messages.error(self.request, "The Octroi entry you tried to delete was not found.")
            if request.headers.get('HX-Request'):
                return HttpResponse(status=404) # Or a 200 OK with an empty response/message
            return HttpResponse(status=404) # Or redirect to list view

class OctroiListView(ListView):
    """
    Displays the main page containing the Octroi entries.
    The actual table content is loaded via HTMX.
    """
    model = Octroi
    template_name = 'accounts/octroi/list.html'
    context_object_name = 'octrois' # The default context name for ListView is 'object_list'

class OctroiTablePartialView(ListView):
    """
    Renders only the DataTables-powered table of Octroi entries.
    This view is specifically targeted by HTMX to refresh the table.
    """
    model = Octroi
    template_name = 'accounts/octroi/_octroi_table.html'
    context_object_name = 'octrois'
    # DataTables handles pagination, sorting, and searching on the client-side,
    # so no Django pagination is typically needed here unless dealing with huge datasets.

class OctroiCreateView(HtmxMessageMixin, CreateView):
    """
    Handles the creation of new Octroi entries.
    Renders a partial form template for HTMX modal display.
    """
    model = Octroi
    form_class = OctroiForm
    template_name = 'accounts/octroi/_octroi_form.html' # Path to the partial form template
    success_url = reverse_lazy('octroi_list') # Fallback for non-HTMX requests
    success_message = 'Octroi entry added successfully.'

class OctroiUpdateView(HtmxMessageMixin, UpdateView):
    """
    Handles the updating of existing Octroi entries.
    Renders a partial form template for HTMX modal display.
    """
    model = Octroi
    form_class = OctroiForm
    template_name = 'accounts/octroi/_octroi_form.html' # Path to the partial form template
    success_url = reverse_lazy('octroi_list') # Fallback for non-HTMX requests
    success_message = 'Octroi entry updated successfully.'

class OctroiDeleteView(HtmxMessageMixin, DeleteView):
    """
    Handles the deletion of Octroi entries.
    Renders a partial confirmation template for HTMX modal display.
    """
    model = Octroi
    template_name = 'accounts/octroi/_octroi_confirm_delete.html' # Path to the partial delete confirmation template
    success_url = reverse_lazy('octroi_list') # Fallback for non-HTMX requests
    context_object_name = 'octroi' # Name for the object in the template for clarity
    success_message = 'Octroi entry deleted successfully.'
```

#### 4.4 Templates (accounts/octroi/)

**Task:** Create templates for each view.

**Instructions:**
- **`list.html`:** The main page, extends `core/base.html`. It sets up the structure for the page, including a button to trigger the "Add New" modal, and a container (`#octroiTable-container`) where the DataTables content will be loaded and refreshed via HTMX. An Alpine.js `x-data` attribute or a simple `_` attribute can manage the modal's hidden state.
- **`_octroi_table.html`:** This is a partial template that contains only the `<table>` element and the JavaScript to initialize DataTables. It's designed to be loaded dynamically by HTMX.
- **`_octroi_form.html`:** A partial template containing the Django form for both creation and updating. It's loaded into the modal for user input.
- **`_octroi_confirm_delete.html`:** A partial template for the delete confirmation dialog, also loaded into the modal.

**Note:** The `{{% %}}` syntax in template examples will be replaced by actual Django template tags `{% %}`.

```html
{# accounts/octroi/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Octroi Entries Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'octroi_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal remove .hidden from #modal"> {# Alpine.js or _ attribute for modal toggle #}
            Add New Octroi Entry
        </button>
    </div>
    
    {# Container for the DataTable, loaded and refreshed via HTMX #}
    <div id="octroiTable-container"
         hx-trigger="load, refreshOctroiList from:body" {# Load on page load, and on custom event trigger #}
         hx-get="{% url 'octroi_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md overflow-hidden">
        {# Initial loading state #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Octroi Entries...</p>
        </div>
    </div>
    
    {# Modal structure for forms and confirmations #}
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transform transition-all sm:my-8 sm:align-middle sm:w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Global event listener to close modal on specific HTMX triggers (e.g., after 204 No Content from form submission)
    document.body.addEventListener('refreshOctroiList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
            modal.classList.add('hidden'); // Ensure modal is hidden
        }
    });

    // Handle modal closing if an HTMX request results in a 204 (successful form submission/deletion)
    // and was targeting modalContent. This is a robust fallback for _ attributes.
    document.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.target && event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
                modal.classList.add('hidden');
            }
        }
    });
</script>
{% endblock %}
```

```html
{# accounts/octroi/_octroi_table.html #}
<table id="octroiTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Terms</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Value</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in octrois %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 text-sm text-gray-800">{{ obj.terms }}</td>
            <td class="py-2 px-4 text-sm text-gray-800">{{ obj.value }}</td>
            <td class="py-2 px-4 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-xs mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'octroi_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal remove .hidden from #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'octroi_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal remove .hidden from #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-4 text-center text-gray-500">No Octroi entries found. Click "Add New Octroi Entry" to create one.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization. Ensure 'destroy' is true to re-initialize correctly on HTMX swaps.
$(document).ready(function() {
    $('#octroiTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "searching": true,
        "ordering": true,
        "paging": true,
        "info": true,
        "destroy": true, // Essential for HTMX partial loads
        "language": {
            "emptyTable": "No Octroi entries available"
        }
    });
});
</script>
```

```html
{# accounts/octroi/_octroi_form.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">
        {% if form.instance.pk %}Edit Octroi Entry{% else %}Add New Octroi Entry{% endif %}
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            {# Iterate over each field in the form #}
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }} {# Renders the input widget #}
                {% if field.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc list-inside">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {# Display non-field errors if any #}
            {% if form.non_field_errors %}
            <div class="text-red-600 text-sm p-3 border border-red-300 bg-red-50 rounded-md">
                <p class="font-medium">Please correct the following errors:</p>
                <ul class="mt-1 list-disc list-inside">
                {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# accounts/octroi/_octroi_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Octroi entry for "<strong>{{ octroi.terms }}</strong>" (Value: <strong>{{ octroi.value }}</strong>)?
        This action cannot be undone.
    </p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-150 ease-in-out"
            _="on click remove .is-active from #modal add .hidden to #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'octroi_delete' octroi.pk %}" 
            hx-swap="none" {# No content swap, just trigger a refresh #}
            hx-confirm="This action is permanent and cannot be reversed. Proceed with deletion?" {# Enhanced confirmation #}
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-150 ease-in-out">
            Confirm Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (accounts/urls.py)

**Task:** Define URL patterns for the views.

**Instructions:**
URL patterns are defined for the list view, and for the add, edit, and delete actions, using Django's `path()` function. A specific URL for the `_octroi_table.html` partial is also created, as it will be loaded dynamically by HTMX.

```python
from django.urls import path
from .views import OctroiListView, OctroiCreateView, OctroiUpdateView, OctroiDeleteView, OctroiTablePartialView

urlpatterns = [
    # Main list view for Octroi entries
    path('octroi/', OctroiListView.as_view(), name='octroi_list'),
    
    # HTMX endpoint to load/refresh the DataTables table content
    path('octroi/table/', OctroiTablePartialView.as_view(), name='octroi_table'),

    # Endpoint to load and handle the 'Add New' form (for HTMX modal)
    path('octroi/add/', OctroiCreateView.as_view(), name='octroi_add'),
    
    # Endpoint to load and handle the 'Edit' form for a specific Octroi entry (for HTMX modal)
    path('octroi/edit/<int:pk>/', OctroiUpdateView.as_view(), name='octroi_edit'),
    
    # Endpoint to load and handle the 'Delete' confirmation (for HTMX modal)
    path('octroi/delete/<int:pk>/', OctroiDeleteView.as_view(), name='octroi_delete'),
]
```

#### 4.6 Tests (accounts/tests.py)

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests are provided for the `Octroi` model to ensure its properties and methods behave as expected. Integration tests cover all CRUD operations implemented in the views, including specific checks for HTMX request handling (e.g., status codes, `HX-Trigger` headers, partial template rendering, and error handling). This ensures high test coverage and reliability of the migrated application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Octroi
from decimal import Decimal # Use Decimal for precision with DecimalField

class OctroiModelTest(TestCase):
    """
    Unit tests for the Octroi model.
    Focuses on field types, default values, string representation, and manager methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data that is shared across all model test methods.
        # For managed=False models with explicit primary_key, we typically set the ID manually
        # in tests, mirroring how it might be handled if the DB truly isn't auto-incrementing
        # or if we need specific IDs for testing. In production with IDENTITY columns,
        # the DB handles ID generation.
        Octroi.objects.create(id=1, terms='Fuel Surcharge', value=Decimal('1.500'))
        Octroi.objects.create(id=2, terms='Road Tax', value=Decimal('2.750'))
        Octroi.objects.create(id=3, terms='Service Fee', value=Decimal('0.800'))

    def test_octroi_creation(self):
        """Verify that an Octroi object can be created and its attributes are correct."""
        octroi = Octroi.objects.get(id=1)
        self.assertEqual(octroi.terms, 'Fuel Surcharge')
        self.assertEqual(octroi.value, Decimal('1.500'))
        self.assertEqual(octroi.pk, 1) # Check primary key mapping

    def test_terms_field_properties(self):
        """Test properties of the 'terms' field."""
        octroi = Octroi.objects.get(id=1)
        field = octroi._meta.get_field('terms')
        self.assertIsInstance(field, models.CharField)
        self.assertEqual(field.db_column, 'Terms') # Check db_column mapping
        self.assertEqual(field.max_length, 255)

    def test_value_field_properties(self):
        """Test properties of the 'value' field."""
        octroi = Octroi.objects.get(id=1)
        field = octroi._meta.get_field('value')
        self.assertIsInstance(field, models.DecimalField)
        self.assertEqual(field.db_column, 'Value') # Check db_column mapping
        self.assertEqual(field.max_digits, 15)
        self.assertEqual(field.decimal_places, 3)

    def test_str_method(self):
        """Test the __str__ method for human-readable representation."""
        octroi = Octroi.objects.get(id=1)
        self.assertEqual(str(octroi), "Octroi Terms: Fuel Surcharge | Value: 1.500")

    def test_meta_options(self):
        """Test Meta options like db_table, managed, verbose_name, and ordering."""
        self.assertEqual(Octroi._meta.db_table, 'tblOctroi_Master')
        self.assertFalse(Octroi._meta.managed) # Ensure managed is False
        self.assertEqual(Octroi._meta.verbose_name, 'Octroi Entry')
        self.assertEqual(Octroi._meta.verbose_name_plural, 'Octroi Entries')
        self.assertEqual(Octroi._meta.ordering, ['-id'])

    def test_ordering_behavior(self):
        """Verify that objects are ordered by 'id' in descending order as per Meta.ordering."""
        # Due to '-id' ordering, id=3 should come first, then id=2, then id=1
        octrois = Octroi.objects.all()
        self.assertEqual(octrois[0].id, 3)
        self.assertEqual(octrois[1].id, 2)
        self.assertEqual(octrois[2].id, 1)


class OctroiViewsTest(TestCase):
    """
    Integration tests for Octroi views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data that is persistent across all test methods in this class.
        Octroi.objects.create(id=10, terms='Initial Entry A', value=Decimal('100.000'))
        Octroi.objects.create(id=11, terms='Initial Entry B', value=Decimal('200.500'))
    
    def setUp(self):
        # Set up for each test method: initialize a new client.
        self.client = Client()

    # --- List View Tests ---
    def test_octroi_list_view_get(self):
        """Verify the main list view renders correctly."""
        response = self.client.get(reverse('octroi_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/octroi/list.html')
        self.assertContains(response, 'Octroi Entries Management')
        # Check if the HTMX container for the table is present
        self.assertContains(response, '<div id="octroiTable-container"')

    def test_octroi_table_partial_view_get(self):
        """Verify the HTMX table partial view renders correctly with data."""
        response = self.client.get(reverse('octroi_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/octroi/_octroi_table.html')
        self.assertIn('octrois', response.context)
        self.assertEqual(response.context['octrois'].count(), 2)
        self.assertContains(response, 'Initial Entry A')
        self.assertContains(response, 'Initial Entry B')
        self.assertContains(response, "$('#octroiTable').DataTable(") # Check for DataTables JS init

    # --- Create View Tests ---
    def test_octroi_create_view_get_htmx(self):
        """Verify GET request for the add form via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('octroi_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/octroi/_octroi_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add New Octroi Entry') # Check for correct title in form

    def test_octroi_create_view_post_success_htmx(self):
        """Verify successful POST request for creating an Octroi via HTMX."""
        # Find next available ID for managed=False with primary_key=True
        # In a real DB with IDENTITY, you wouldn't pass 'id' here.
        next_id = Octroi.objects.all().order_by('-id').first().id + 1 if Octroi.objects.exists() else 1
        data = {'terms': 'New Octroi Term', 'value': Decimal('123.456')}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('octroi_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content on success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOctroiList')
        self.assertTrue(Octroi.objects.filter(terms='New Octroi Term', value=Decimal('123.456')).exists())
        self.assertContains(response, '', status_code=204) # No content in response body

    def test_octroi_create_view_post_invalid_data_htmx(self):
        """Verify POST request with invalid data via HTMX, expecting form re-render with errors."""
        data = {'terms': '', 'value': Decimal('-5.00')} # Invalid: empty terms, negative value
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('octroi_add'), data, **headers)
        
        self.assertEqual(response.status_code, 200) # HTMX expects 200 OK to swap content
        self.assertTemplateUsed(response, 'accounts/octroi/_octroi_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors) # Check if form has errors
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Value must be a positive number.')

    # --- Update View Tests ---
    def test_octroi_update_view_get_htmx(self):
        """Verify GET request for the edit form via HTMX."""
        obj = Octroi.objects.get(id=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('octroi_edit', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/octroi/_octroi_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.terms, 'Initial Entry A')
        self.assertContains(response, 'Edit Octroi Entry') # Check for correct title in form

    def test_octroi_update_view_post_success_htmx(self):
        """Verify successful POST request for updating an Octroi via HTMX."""
        obj = Octroi.objects.get(id=10)
        data = {'terms': 'Updated Term A', 'value': Decimal('150.750')}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('octroi_edit', args=[obj.id]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOctroiList')
        obj.refresh_from_db() # Reload object from DB to get updated values
        self.assertEqual(obj.terms, 'Updated Term A')
        self.assertEqual(obj.value, Decimal('150.750'))

    def test_octroi_update_view_post_invalid_data_htmx(self):
        """Verify POST request with invalid data for update via HTMX."""
        obj = Octroi.objects.get(id=10)
        data = {'terms': 'Updated', 'value': Decimal('0.000')} # Invalid: non-positive value
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('octroi_edit', args=[obj.id]), data, **headers)
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/octroi/_octroi_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertContains(response, 'Value must be a positive number.')

    # --- Delete View Tests ---
    def test_octroi_delete_view_get_htmx(self):
        """Verify GET request for the delete confirmation via HTMX."""
        obj = Octroi.objects.get(id=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('octroi_delete', args=[obj.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/octroi/_octroi_confirm_delete.html')
        self.assertIn('octroi', response.context)
        self.assertEqual(response.context['octroi'].pk, obj.pk)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, obj.terms)

    def test_octroi_delete_view_post_success_htmx(self):
        """Verify successful POST request for deleting an Octroi via HTMX."""
        obj_to_delete = Octroi.objects.get(id=10)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('octroi_delete', args=[obj_to_delete.id]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOctroiList')
        self.assertFalse(Octroi.objects.filter(id=obj_to_delete.id).exists()) # Verify object is deleted

    def test_octroi_delete_view_post_not_found(self):
        """Verify delete attempt for non-existent object results in 404."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('octroi_delete', args=[999]), **headers)
        self.assertEqual(response.status_code, 404) # Django's default behavior for object not found
        self.assertContains(response, "The Octroi entry you tried to delete was not found.", status_code=404)
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Content:** All interactions (loading forms, submitting data, refreshing the table) are driven by HTMX attributes. The main `list.html` acts as a shell, with the dynamic table and modal content loaded via `hx-get` into designated target elements (`#octroiTable-container`, `#modalContent`).
- **Modal Control:** The modal's visibility (`hidden` vs. `is-active` class) is managed by HTMX's `_` attribute (`on click add .is-active to #modal remove .hidden from #modal`) for opening, and by `hx-trigger="refreshOctroiList"` coupled with a JavaScript event listener for closing on successful form submissions.
- **DataTables:** The `_octroi_table.html` partial includes the necessary JavaScript to initialize DataTables on the `<table>` element. The `destroy: true` option is crucial for ensuring DataTables correctly re-initializes when the table partial is swapped via HTMX.
- **No Full Page Reloads:** Every CRUD operation and UI interaction is designed to happen without a full page refresh, providing a seamless user experience. `hx-swap="none"` with `hx-trigger` is used for form submissions to prevent the form from re-rendering and instead trigger a list refresh.
- **`HX-Trigger` Responses:** After successful create, update, or delete operations, the Django views respond with a `204 No Content` status code and an `HX-Trigger: refreshOctroiList` header. This custom event is then caught by `list.html`'s JavaScript, prompting HTMX to reload the `_octroi_table.html` partial, keeping the list up-to-date.

---

### Final Notes

This modernization plan provides a structured, automated, and comprehensive approach to transitioning your ASP.NET Octroi module to Django. By adhering to modern Django principles, utilizing HTMX and DataTables for a rich frontend experience, and implementing robust testing, you will achieve a scalable, maintainable, and highly performant web application. This modular and automated approach will significantly reduce manual development effort and errors, making the migration process predictable and efficient.