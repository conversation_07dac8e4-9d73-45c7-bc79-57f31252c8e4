## ASP.NET to Django Conversion Script: Freight Terms Module Modernization

This document outlines a strategic plan for transitioning your existing ASP.NET "Freight Terms" module to a modern, efficient Django-based solution. Our approach prioritizes automation, reduces manual effort, and leverages cutting-edge technologies like HTMX, Alpine.js, and DataTables to deliver a responsive and maintainable application.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- **NEVER** include base.html template code in your output - assume it already exists.
- Focus **ONLY** on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Business Value:** Understanding the foundational data structure ensures accurate migration and preserves critical business information. This automated step quickly identifies the tables and fields your new system needs to interact with.

**Analysis:**
The ASP.NET `SqlDataSource1` directly interacts with the database.

*   **Table Name:** `tblFreight_Master`
*   **Columns:**
    *   `Id`: Identified as the `DataKeyNames` and used in `WHERE [Id] = @Id` for updates/deletes. This is the primary key, likely an auto-incrementing integer.
    *   `Terms`: Used in `INSERT INTO ([Terms])`, `UPDATE SET [Terms] = @Terms`. This is a string field.

**Outcome:**
*   **[TABLE_NAME]**: `tblFreight_Master`
*   **Columns**: `Id` (Primary Key, Integer), `Terms` (String)

#### Step 2: Identify Backend Functionality

**Business Value:** Pinpointing existing business rules and operations allows us to precisely replicate and potentially enhance them in the new Django system, ensuring no loss of functionality during the transition.

**Analysis:**
The ASP.NET `GridView` and `SqlDataSource` define the core CRUD operations.

*   **Create (Insert):**
    *   Triggered by "Add" `CommandName` from `btnInsert` in `GridView1`'s `FooterTemplate` (using `txtTerms2`).
    *   Also triggered by "Add1" `CommandName` from `btnInsert` in `GridView1`'s `EmptyDataTemplate` (using `txtTerms3`).
    *   `SqlDataSource1.InsertParameters["Terms"].DefaultValue` is set before `SqlDataSource1.Insert()`.
*   **Read (Select):**
    *   `GridView1` is populated using `SqlDataSource1.SelectCommand="SELECT * FROM [tblFreight_Master] order by [Id] desc"`.
*   **Update:**
    *   Triggered by `GridView1_RowUpdating` event.
    *   Uses `txtTerms1` in the `EditItemTemplate`.
    *   Performs a direct `UPDATE` SQL command using `SqlConnection` and `SqlCommand`.
*   **Delete:**
    *   Triggered by `GridView1_RowDeleted` event (although the `DeleteCommand` is executed by `SqlDataSource1`).
*   **Validation:**
    *   `RequiredFieldValidator` is used for the `Terms` field (`ReqTerms`, `ReqTerm`, `ReqTerm0`), ensuring the field is not empty on insert/update.
*   **Messages:** `lblMessage` displays "Record Updated", "Record Deleted", "Record Inserted".
*   **Client-Side Confirmations:** JavaScript calls (`confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()`) are attached to buttons. These will be replaced by HTMX/Alpine.js modal interactions.

**Outcome:** Standard CRUD operations (Create, Read, Update, Delete) for Freight Terms, with basic required field validation.

#### Step 3: Infer UI Components

**Business Value:** Understanding the current user interface elements helps us design an intuitive and familiar Django interface, minimizing user retraining and ensuring a smooth adoption of the new system.

**Analysis:**

*   **`GridView1`**: The primary UI component for displaying and managing freight terms. This will be converted to a Django template displaying data using DataTables for enhanced user interaction, driven by HTMX for dynamic updates.
*   **`asp:Label` (`lblTerms`)**: Displays the 'Terms' value in read-only mode.
*   **`asp:TextBox` (`txtTerms1`, `txtTerms2`, `txtTerms3`)**: Used for inputting 'Terms' during edit and insert operations. These will map to Django `forms.TextInput` widgets.
*   **`asp:Button` / `asp:LinkButton` (Edit, Delete, Insert)**: Trigger CRUD actions. These will become standard HTML `<button>` elements with HTMX attributes to perform asynchronous operations.
*   **`asp:RequiredFieldValidator`**: Client-side (and server-side) validation. This will be handled by Django forms' built-in validation and HTMX's ability to re-render partial forms with error messages.
*   **`lblMessage`**: For displaying feedback messages. This will be managed by Django's messages framework, optionally displayed via HTMX.
*   **`Css/yui-datatable.css`**: Indicates a data table library. This will be replaced with DataTables.
*   **`Javascript/PopUpMsg.js`, `Javascript/loadingNotifier.js`**: Custom JavaScript. This will be replaced by HTMX and Alpine.js for modal logic and loading indicators.

**Outcome:** A single-page application experience for managing Freight Terms, featuring a data table, and modal forms for adding, editing, and deleting entries, all powered by HTMX and Alpine.js.

---

#### Step 4: Generate Django Code

We will structure the Django application within an `accounts_masters` app (consistent with `Module_Accounts_Masters_Freight`).

##### 4.1 Models (`accounts_masters/models.py`)

**Business Value:** This defines the core data structure in Django, directly mapping to your existing database. Using `managed = False` protects your legacy data while enabling Django to interact with it, minimizing risk during the transition.

```python
from django.db import models

class FreightTerm(models.Model):
    # Django's default 'id' primary key maps to the 'Id' column in tblFreight_Master
    # if it's the auto-incrementing primary key. No need to explicitly define 'id' here.
    terms = models.CharField(db_column='Terms', max_length=255, verbose_name="Terms") # Assuming a reasonable max_length

    class Meta:
        managed = False  # Important: Tells Django not to manage this table's schema (it exists)
        db_table = 'tblFreight_Master' # Link to your existing table name
        verbose_name = 'Freight Term'
        verbose_name_plural = 'Freight Terms'
        ordering = ['-id'] # Matches the 'order by [Id] desc' from the original SQL

    def __str__(self):
        return self.terms

    # Business logic methods, if any, would go here.
    # For example, a method to ensure terms are unique if not handled by DB constraint
    # def clean(self):
    #     if FreightTerm.objects.filter(terms=self.terms).exclude(pk=self.pk).exists():
    #         raise ValidationError({'terms': 'This term already exists.'})

```

##### 4.2 Forms (`accounts_masters/forms.py`)

**Business Value:** Django forms simplify data input and validation, reducing the potential for data errors. By centralizing validation, we ensure consistency and maintainability.

```python
from django import forms
from .models import FreightTerm

class FreightTermForm(forms.ModelForm):
    class Meta:
        model = FreightTerm
        fields = ['terms']
        widgets = {
            'terms': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'Enter freight terms'
            }),
        }
        
    # Custom validation can be added here if needed, e.g.,
    # def clean_terms(self):
    #     terms = self.cleaned_data['terms']
    #     if len(terms) < 3:
    #         raise forms.ValidationError("Terms must be at least 3 characters long.")
    #     return terms
```

##### 4.3 Views (`accounts_masters/views.py`)

**Business Value:** Thin views improve code readability and maintainability. By separating concerns, our Django application becomes easier to test, update, and scale, ensuring long-term agility.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import FreightTerm
from .forms import FreightTermForm

# Helper function to DRY up HTMX responses
def htmx_crud_response(request, success_message, trigger_event):
    """
    Handles HTMX responses for successful CRUD operations.
    If it's an HTMX request, returns 204 No Content with HX-Trigger.
    Otherwise, sets a Django message.
    """
    if request.headers.get('HX-Request'):
        messages.success(request, success_message) # Messages can be consumed by HTMX too
        return HttpResponse(
            status=204,  # No content to return, as HTMX will swap="none"
            headers={'HX-Trigger': trigger_event}
        )
    messages.success(request, success_message)
    return None # Indicate to the view that a conventional redirect/render should occur

class FreightTermListView(ListView):
    """
    Displays the main page for Freight Terms.
    The actual table content is loaded via HTMX from FreightTermTablePartialView.
    """
    model = FreightTerm
    template_name = 'accounts_masters/freightterm/list.html'
    context_object_name = 'freightterms'

    # Views are thin: no business logic, just rendering the main page.
    # The list.html template will handle the initial HTMX request for the table.

class FreightTermTablePartialView(ListView):
    """
    Returns the HTML partial for the Freight Terms table,
    designed to be loaded via HTMX.
    """
    model = FreightTerm
    template_name = 'accounts_masters/freightterm/_freightterm_table.html'
    context_object_name = 'freightterms'
    # This view is purely for rendering the table. No complex logic.

class FreightTermCreateView(CreateView):
    """
    Handles creation of new Freight Terms.
    Renders a form and processes its submission via HTMX.
    """
    model = FreightTerm
    form_class = FreightTermForm
    template_name = 'accounts_masters/freightterm/_freightterm_form.html' # This is a partial
    success_url = reverse_lazy('freightterm_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        # Use the helper for consistent HTMX response
        htmx_resp = htmx_crud_response(self.request, 'Freight Term added successfully.', 'refreshFreightTermList')
        if htmx_resp:
            return htmx_resp
        return response

    # Views are thin: form handling is delegated to Django's CBV and the form class.

class FreightTermUpdateView(UpdateView):
    """
    Handles updating existing Freight Terms.
    Renders a pre-filled form and processes its submission via HTMX.
    """
    model = FreightTerm
    form_class = FreightTermForm
    template_name = 'accounts_masters/freightterm/_freightterm_form.html' # This is a partial
    success_url = reverse_lazy('freightterm_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        htmx_resp = htmx_crud_response(self.request, 'Freight Term updated successfully.', 'refreshFreightTermList')
        if htmx_resp:
            return htmx_resp
        return response

    # Views are thin: form handling is delegated.

class FreightTermDeleteView(DeleteView):
    """
    Handles deletion of Freight Terms.
    Renders a confirmation prompt and processes deletion via HTMX.
    """
    model = FreightTerm
    template_name = 'accounts_masters/freightterm/_freightterm_confirm_delete.html' # This is a partial
    success_url = reverse_lazy('freightterm_list') # Fallback
    context_object_name = 'freightterm' # Renaming for clarity in template

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        htmx_resp = htmx_crud_response(self.request, 'Freight Term deleted successfully.', 'refreshFreightTermList')
        if htmx_resp:
            return htmx_resp
        return response

    # Views are thin: deletion logic is handled by Django's CBV.
```

##### 4.4 Templates (`accounts_masters/templates/accounts_masters/freightterm/`)

**Business Value:** Modern, modular templates ensure a consistent user experience and reduce code duplication. HTMX and Alpine.js power dynamic updates without full page reloads, making the application feel faster and more responsive.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Freight Terms</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'freightterm_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Freight Term
        </button>
    </div>
    
    <div id="freighttermTable-container"
         hx-trigger="load, refreshFreightTermList from:body"
         hx-get="{% url 'freightterm_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md p-4">
        <!-- Loading spinner for initial load and HTMX reloads -->
        <div class="flex flex-col items-center justify-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading freight terms...</p>
        </div>
    </div>
    
    <!-- Global Modal Structure (hidden by default) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-lg w-full p-6 transform transition-all duration-300 scale-95 opacity-0"
             _="on modal.active transition transform opacity from 0 scale-95 to 1 scale-100 and opacity from 1 scale-100 to 0 scale-95 then remove .is-active from #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed (e.g., for complex UI state)
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });

    // Handle messages shown after HTMX requests (requires a message display mechanism in base.html)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) {
            // Optional: You might want to close the modal here if form submission was successful
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });

    // Ensure DataTables is re-initialized after HTMX swaps the table content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'freighttermTable-container') {
            // Check if the table element exists before initializing DataTables
            const tableElement = document.getElementById('freighttermTable');
            if (tableElement && !$.fn.DataTable.isDataTable('#freighttermTable')) {
                $(tableElement).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": true
                });
            }
        }
    });
</script>
{% endblock %}
```

**`_freightterm_table.html` (Partial for HTMX)**

```html
<table id="freighttermTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Terms</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in freightterms %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.terms }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'freightterm_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                    hx-get="{% url 'freightterm_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="py-4 px-4 text-center text-gray-500">No freight terms found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization. This script runs when the partial is loaded by HTMX.
// It's crucial to destroy any existing DataTable instance before re-initializing to avoid warnings.
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#freighttermTable')) {
        $('#freighttermTable').DataTable().destroy();
    }
    $('#freighttermTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "responsive": true,
        "columnDefs": [
            { "orderable": false, "targets": [0, 2] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

**`_freightterm_form.html` (Partial for HTMX)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-800 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Freight Term</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="mb-4 text-red-500 text-sm">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_freightterm_confirm_delete.html` (Partial for HTMX)**

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-800 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the freight term "{{ freightterm.terms }}"?</p>
    <form hx-post="{% url 'freightterm_delete' freightterm.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

##### 4.5 URLs (`accounts_masters/urls.py`)

**Business Value:** Clearly defined URL patterns ensure proper routing and access to different parts of the application. This structured approach simplifies navigation and integration.

```python
from django.urls import path
from .views import (
    FreightTermListView, 
    FreightTermCreateView, 
    FreightTermUpdateView, 
    FreightTermDeleteView,
    FreightTermTablePartialView, # Endpoint for HTMX table loading
)

urlpatterns = [
    # Main list view (full page)
    path('freightterms/', FreightTermListView.as_view(), name='freightterm_list'),
    
    # HTMX endpoints for CRUD operations (return partials or 204)
    path('freightterms/add/', FreightTermCreateView.as_view(), name='freightterm_add'),
    path('freightterms/<int:pk>/edit/', FreightTermUpdateView.as_view(), name='freightterm_edit'),
    path('freightterms/<int:pk>/delete/', FreightTermDeleteView.as_view(), name='freightterm_delete'),
    
    # HTMX endpoint for dynamically loading the table content
    path('freightterms/table/', FreightTermTablePartialView.as_view(), name='freightterm_table'),
]
```

##### 4.6 Tests (`accounts_masters/tests.py`)

**Business Value:** Comprehensive tests ensure the reliability and correctness of your migrated application. Automated testing minimizes defects, reduces manual QA effort, and provides confidence in future enhancements.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import FreightTerm

class FreightTermModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.freight_term_1 = FreightTerm.objects.create(terms='FOB Destination')
        cls.freight_term_2 = FreightTerm.objects.create(terms='EXW')
  
    def test_freightterm_creation(self):
        obj = FreightTerm.objects.get(id=self.freight_term_1.id)
        self.assertEqual(obj.terms, 'FOB Destination')
        
    def test_terms_label(self):
        obj = FreightTerm.objects.get(id=self.freight_term_1.id)
        field_label = obj._meta.get_field('terms').verbose_name
        self.assertEqual(field_label, 'Terms')
        
    def test_object_string_representation(self):
        obj = FreightTerm.objects.get(id=self.freight_term_1.id)
        self.assertEqual(str(obj), 'FOB Destination')

    def test_ordering(self):
        # Newest item first
        freight_terms = FreightTerm.objects.all()
        self.assertEqual(freight_terms[0].terms, 'EXW')
        self.assertEqual(freight_terms[1].terms, 'FOB Destination')

class FreightTermViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.freight_term_1 = FreightTerm.objects.create(terms='FOB Shipping Point')
        cls.freight_term_2 = FreightTerm.objects.create(terms='Collect')
    
    def setUp(self):
        # Set up a client for each test method
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('freightterm_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/freightterm/list.html')
        # Check for title and main container elements
        self.assertContains(response, 'Freight Terms')
        self.assertContains(response, '<div id="freighttermTable-container"') # Check for the HTMX target div
        
    def test_table_partial_view_get(self):
        # This is the HTMX endpoint for the table content
        response = self.client.get(reverse('freightterm_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/freightterm/_freightterm_table.html')
        self.assertContains(response, 'FOB Shipping Point')
        self.assertContains(response, 'Collect')
        self.assertContains(response, '<table id="freighttermTable"') # Ensure table is present
        self.assertIn('freightterms', response.context)
        self.assertEqual(len(response.context['freightterms']), 2)

    def test_create_view_get(self):
        response = self.client.get(reverse('freightterm_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/freightterm/_freightterm_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Freight Term') # Check form title
        
    def test_create_view_post_success(self):
        data = {'terms': 'Delivered Duty Paid'}
        response = self.client.post(reverse('freightterm_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No content for HTMX success
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshFreightTermList')
        self.assertTrue(FreightTerm.objects.filter(terms='Delivered Duty Paid').exists())
        # Check for success message in session (HTMX consumes it client-side)
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Freight Term added successfully.')
        
    def test_create_view_post_invalid(self):
        data = {'terms': ''} # Invalid data (empty string)
        response = self.client.post(reverse('freightterm_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'accounts_masters/freightterm/_freightterm_form.html')
        self.assertFalse(FreightTerm.objects.filter(terms='').exists()) # Object not created
        self.assertContains(response, 'This field is required.') # Check for validation error message

    def test_update_view_get(self):
        obj = self.freight_term_1
        response = self.client.get(reverse('freightterm_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/freightterm/_freightterm_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.terms, 'FOB Shipping Point')
        self.assertContains(response, 'Edit Freight Term') # Check form title
        
    def test_update_view_post_success(self):
        obj = self.freight_term_1
        data = {'terms': 'FOB Origin, Freight Prepaid'}
        response = self.client.post(reverse('freightterm_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshFreightTermList')
        obj.refresh_from_db() # Reload object from DB to get updated value
        self.assertEqual(obj.terms, 'FOB Origin, Freight Prepaid')
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Freight Term updated successfully.')

    def test_update_view_post_invalid(self):
        obj = self.freight_term_1
        data = {'terms': ''} # Invalid data
        response = self.client.post(reverse('freightterm_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/freightterm/_freightterm_form.html')
        obj.refresh_from_db()
        self.assertNotEqual(obj.terms, '') # Ensure it wasn't updated with invalid data
        self.assertContains(response, 'This field is required.')

    def test_delete_view_get(self):
        obj = self.freight_term_2
        response = self.client.get(reverse('freightterm_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_masters/freightterm/_freightterm_confirm_delete.html')
        self.assertIn('freightterm', response.context)
        self.assertEqual(response.context['freightterm'].terms, 'Collect')
        self.assertContains(response, 'Confirm Deletion')
        
    def test_delete_view_post_success(self):
        obj_to_delete = FreightTerm.objects.create(terms='CIP') # Create a new one to delete
        initial_count = FreightTerm.objects.count()
        response = self.client.post(reverse('freightterm_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshFreightTermList')
        self.assertEqual(FreightTerm.objects.count(), initial_count - 1)
        self.assertFalse(FreightTerm.objects.filter(id=obj_to_delete.id).exists())
        messages = list(response.wsgi_request._messages)
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Freight Term deleted successfully.')

    def test_delete_view_post_non_existent(self):
        response = self.client.post(reverse('freightterm_delete', args=[9999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Object not found, Django's default behavior
```

---

#### Step 5: HTMX and Alpine.js Integration

**Business Value:** This integration creates a highly responsive, single-page application feel without the complexity of traditional JavaScript frameworks. Users experience instant feedback and seamless interactions, improving productivity and satisfaction.

**Instructions Implemented:**

*   **HTMX for all dynamic updates:**
    *   The `list.html` uses `hx-get` to load the table content into `freighttermTable-container` on page load and `refreshFreightTermList` event.
    *   "Add New" button uses `hx-get` to fetch the form into `#modalContent`.
    *   "Edit" and "Delete" buttons in the table use `hx-get` to fetch their respective forms/confirmations into `#modalContent`.
    *   Form submissions (`_freightterm_form.html`, `_freightterm_confirm_delete.html`) use `hx-post` and `hx-swap="none"`.
    *   Successful form submissions and deletions send an `HX-Trigger` header (`refreshFreightTermList`) to signal the main list to refresh its table content.
*   **Alpine.js for UI state management:**
    *   A simple `on click add .is-active to #modal` and `remove .is-active from me` (when clicking outside the modal or on cancel button) is used to control modal visibility, replacing the ASP.NET `PopUpMsg.js`.
    *   Basic Alpine.js `x-data` and `x-show` could be introduced for more complex UI states, but for simple show/hide, HTMX's `_hyperscript` integration is sufficient.
*   **DataTables for all list views:**
    *   The `_freightterm_table.html` partial explicitly initializes DataTables on the `freighttermTable` element when loaded.
    *   The `list.html` includes a `htmx:afterSwap` event listener to re-initialize DataTables whenever the table container is updated by HTMX, ensuring dynamic data works with DataTables features.
*   **No additional JavaScript:** All interactions are handled by HTMX, Alpine.js (via `_hyperscript`), and DataTables, eliminating the need for custom, module-specific JavaScript files.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for modernizing your Freight Terms module. By following these AI-assisted automation steps, you can rapidly transition to a robust Django application that is easier to maintain, scale, and enhance, directly translating to reduced operational costs and increased business agility. This approach ensures a high-quality, test-covered solution, aligning with best practices for modern web development.