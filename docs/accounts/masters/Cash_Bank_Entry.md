## ASP.NET to Django Conversion Script: Cash/Bank Entry Module

This modernization plan outlines the automated conversion of your ASP.NET `Cash_Bank_Entry.aspx` and its C# code-behind to a modern Django-based application. The focus is on leveraging AI-assisted automation to systematically transform legacy components into a robust, maintainable, and scalable Django solution.

**Business Benefits:**
By transitioning to Django, your organization will benefit from:
*   **Reduced Maintenance Costs:** Modern Python/Django code is easier to read, debug, and maintain than legacy ASP.NET Web Forms.
*   **Improved Performance & Scalability:** Django's architecture is inherently more efficient for web applications, allowing for better performance under load and easier scaling.
*   **Enhanced User Experience:** The use of HTMX and Alpine.js provides a dynamic, single-page application (SPA)-like feel without complex JavaScript frameworks, leading to a more responsive interface for users.
*   **Faster Feature Development:** Django's "batteries-included" philosophy and rich ecosystem enable rapid development of new features and functionalities.
*   **Future-Proof Technology:** Python and Django are widely adopted, actively developed, and have strong community support, ensuring long-term viability and access to skilled developers.
*   **Increased Data Integrity & Security:** Django's ORM and built-in security features reduce common vulnerabilities found in legacy systems.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET `SqlDataSource` definitions and C# code-behind interactions, we identify the following database tables:

*   **`tblACC_CashAmt_Master`**: This table stores the cash entry details.
    *   `Id` (Primary Key, Integer)
    *   `SysDate` (String, representing a date)
    *   `SysTime` (String, representing a time)
    *   `SessionId` (String)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `Amt` (Double/Decimal)
*   **`tblACC_BankAmt_Master`**: This table stores the bank entry details.
    *   `Id` (Primary Key, Integer)
    *   `SysDate` (String, representing a date)
    *   `SysTime` (String, representing a time)
    *   `SessionId` (String)
    *   `CompId` (Integer)
    *   `FinYearId` (Integer)
    *   `Amt` (Double/Decimal)
    *   `BankId` (Integer, Foreign Key to `tblACC_Bank`)
*   **`tblACC_Bank`**: This table lists available banks.
    *   `Id` (Primary Key, Integer)
    *   `Name` (String, Bank Name)

### Step 2: Identify Backend Functionality

The ASP.NET code implements the following core functionalities:

*   **Cash Entry:**
    *   **Create:** Adds a new cash amount record (`tblACC_CashAmt_Master`) with current date/time, session, company, financial year, and amount.
    *   **Read:** Displays a list of cash entries in `GridView2`.
    *   **Update:** Edits an existing cash entry (`tblACC_CashAmt_Master`), specifically updating `SysDate`, `SysTime`, and potentially `Amt` and other session-related fields as per `SqlDataSource`.
    *   **Delete:** Removes a cash entry from `tblACC_CashAmt_Master`.
    *   **Validation:** Amount field has a required field validator and a regular expression validator (`^\d{1,15}(\.\d{0,3})?$`) for numeric input.
*   **Bank Entry:**
    *   **Create:** Adds a new bank amount record (`tblACC_BankAmt_Master`) similar to cash entry, but also includes a `BankId`.
    *   **Read:** Displays a list of bank entries in `GridView1`, populated via a stored procedure `[GetBank_Entry]`. This implies a more complex query than simple `SELECT *`.
    *   **Update:** Edits an existing bank entry (`tblACC_BankAmt_Master`), allowing updates to amount and bank name.
    *   **Delete:** Removes a bank entry from `tblACC_BankAmt_Master`.
    *   **Validation:** Amount field has similar required and regex validators.
*   **Shared Logic:**
    *   Session-specific data (`username`, `compid`, `finyear`) are captured and stored with each entry.
    *   Current date and time are used for `SysDate` and `SysTime` fields.
    *   A lookup for `Bank` names is used for a dropdown list.

### Step 3: Infer UI Components

The ASP.NET UI consists of:

*   Two main sections for "Cash Entry" and "Bank Entry", presented side-by-side.
*   **Input Forms:**
    *   Text boxes for `Amount` (`txtCashAmt`, `txtBankAmt`).
    *   A dropdown list for `Bank Name` (`DrpBankName`) for bank entries.
    *   "Add" buttons (`btnAdd`, `btnBankAdd`).
*   **Data Grids:**
    *   `GridView2` for Cash Entries.
    *   `GridView1` for Bank Entries.
    *   Both GridViews display `SN`, `Date`, `Time`, `Amount`. `GridView1` also shows `BankName`.
    *   Both GridViews include "Edit" and "Delete" action buttons.
    *   `Panel` controls (`Panel1`, `Panel2`) with scrollbars suggesting scrollable data areas.
*   **Client-side:** `OnClientClick` for confirmation on add buttons. `yui-datatable-theme` implies a JavaScript-based data grid.

### Step 4: Generate Django Code

We will create a Django application named `accounts` to manage both cash and bank entries, ensuring logical grouping of related financial transactions.

#### 4.1 Models (`accounts/models.py`)

We will define three models: `Bank`, `CashEntry`, and `BankEntry`, reflecting the database schema. The `SysDate` and `SysTime` fields are mapped as `CharField` to precisely match the original database's string storage. The `Amt` field will be a `DecimalField` for precise financial calculations. Session-related fields (`SessionId`, `CompId`, `FinYearId`) are mapped as direct fields as per the original structure.

```python
from django.db import models
from datetime import datetime

class Bank(models.Model):
    """
    Represents the tblACC_Bank table.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=100)

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank'
        verbose_name_plural = 'Banks'

    def __str__(self):
        return self.name

class CashEntry(models.Model):
    """
    Represents the tblACC_CashAmt_Master table.
    Stores cash transaction details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.CharField(db_column='SysDate', max_length=10) # Stored as string, e.g., 'DD-MM-YYYY'
    sys_time = models.CharField(db_column='SysTime', max_length=8)  # Stored as string, e.g., 'HH:MM:SS'
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    amt = models.DecimalField(db_column='Amt', max_digits=18, decimal_places=3) # Max 15 digits before decimal, 3 after

    class Meta:
        managed = False
        db_table = 'tblACC_CashAmt_Master'
        verbose_name = 'Cash Entry'
        verbose_name_plural = 'Cash Entries'
        ordering = ['-id'] # Order by Id Desc, matching SqlDataSource1

    def __str__(self):
        return f"Cash Entry {self.id}: {self.amt}"

    def save(self, *args, **kwargs):
        """
        Overrides save to ensure SysDate and SysTime are updated
        with current time if not already set, mimicking ASP.NET behavior.
        """
        now = datetime.now()
        self.sys_date = now.strftime('%d-%m-%Y') # Matches assumed original format
        self.sys_time = now.strftime('%H:%M:%S')
        super().save(*args, **kwargs)
    
    # Example of a 'fat model' method for business logic
    def get_formatted_amount(self):
        return f"{self.amt:,.2f}" # Format to 2 decimal places with comma separator

class BankEntry(models.Model):
    """
    Represents the tblACC_BankAmt_Master table.
    Stores bank transaction details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.CharField(db_column='SysDate', max_length=10)
    sys_time = models.CharField(db_column='SysTime', max_length=8)
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    amt = models.DecimalField(db_column='Amt', max_digits=18, decimal_places=3)
    bank = models.ForeignKey(Bank, on_delete=models.DO_NOTHING, db_column='BankId', to_field='id') # Foreign key to Bank table

    class Meta:
        managed = False
        db_table = 'tblACC_BankAmt_Master'
        verbose_name = 'Bank Entry'
        verbose_name_plural = 'Bank Entries'
        ordering = ['-id'] # Order by Id Desc, matching stored procedure logic

    def __str__(self):
        return f"Bank Entry {self.id}: {self.amt} ({self.bank.name})"

    def save(self, *args, **kwargs):
        """
        Overrides save to ensure SysDate and SysTime are updated
        with current time if not already set, mimicking ASP.NET behavior.
        """
        now = datetime.now()
        self.sys_date = now.strftime('%d-%m-%Y') # Matches assumed original format
        self.sys_time = now.strftime('%H:%M:%S')
        super().save(*args, **kwargs)

    # Example of a 'fat model' method for business logic
    def get_bank_info(self):
        return f"{self.bank.name} - {self.amt}"

```

#### 4.2 Forms (`accounts/forms.py`)

Django forms handle input validation and rendering. We'll use `ModelForm` for convenience and custom widgets for Tailwind CSS styling. We'll also add a custom validator for the amount field's regex.

```python
from django import forms
from .models import CashEntry, BankEntry, Bank
import re

class DecimalAmountField(forms.DecimalField):
    """
    Custom DecimalField with amount specific validation and regex.
    """
    def __init__(self, *args, **kwargs):
        super().__init__(
            max_digits=18, 
            decimal_places=3, 
            min_value=0.001, # Ensure amount is not zero
            *args, **kwargs
        )
        self.regex = re.compile(r'^\d{1,15}(\.\d{0,3})?$')

    def to_python(self, value):
        if value in self.empty_values:
            return None
        value = str(value).replace(',', '') # Handle comma as thousand separator if present
        if not self.regex.match(value):
            raise forms.ValidationError("Amount must be a valid number with up to 15 digits before and 3 digits after the decimal point.")
        try:
            return super().to_python(value)
        except forms.ValidationError as e:
            # Re-raise or wrap if needed, but the regex should catch most
            raise e


class CashEntryForm(forms.ModelForm):
    """
    Form for creating and updating CashEntry instances.
    Only 'amt' is user-editable; other fields are set programmatically.
    """
    amt = DecimalAmountField(
        label="Amount",
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
        })
    )

    class Meta:
        model = CashEntry
        fields = ['amt'] # Only 'amt' is editable via form; others handled in view/model save
        # Explicitly declare widgets for custom styling.
        # Other fields like sys_date, sys_time, session_id, comp_id, fin_year_id are handled internally.

    def clean_amt(self):
        amt = self.cleaned_data['amt']
        if amt <= 0:
            raise forms.ValidationError("Amount must be greater than zero.")
        return amt

class BankEntryForm(forms.ModelForm):
    """
    Form for creating and updating BankEntry instances.
    'amt' and 'bank' are user-editable.
    """
    amt = DecimalAmountField(
        label="Amount",
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
        })
    )
    bank = forms.ModelChoiceField(
        queryset=Bank.objects.exclude(id=4).order_by('name'), # Excluding ID 4 as per ASP.NET SqlDataSource3
        label="Bank Name",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'
        }),
        empty_label="Select Bank"
    )

    class Meta:
        model = BankEntry
        fields = ['bank', 'amt'] # Editable fields

    def clean_amt(self):
        amt = self.cleaned_data['amt']
        if amt <= 0:
            raise forms.ValidationError("Amount must be greater than zero.")
        return amt

```

#### 4.3 Views (`accounts/views.py`)

Views will be thin, primarily handling HTTP requests and delegating business logic to models or forms. We'll use Django's Class-Based Views (CBVs) for CRUD operations and introduce partial views for HTMX.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming user authentication
from .models import CashEntry, BankEntry, Bank
from .forms import CashEntryForm, BankEntryForm
from django.db import connection

# This context would typically come from a user profile or global settings
# For demo purposes, we'll hardcode or pull from session if a real login is present
DEFAULT_SESSION_ID = "DEMO_USER"
DEFAULT_COMP_ID = 1
DEFAULT_FIN_YEAR_ID = 2024

class CashBankEntryDashboardView(LoginRequiredMixin, TemplateView):
    """
    Main dashboard view for both Cash and Bank entries.
    Acts as the single point of entry, similar to the original ASPX page.
    """
    template_name = 'accounts/cash_bank_entry_dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Context data for initial loads if needed, but tables are HTMX loaded.
        return context

# --- Cash Entry Views ---
class CashEntryListView(LoginRequiredMixin, ListView):
    """
    Renders the list of cash entries for HTMX partial loading.
    """
    model = CashEntry
    template_name = 'accounts/_cash_entry_table.html' # This is a partial for HTMX
    context_object_name = 'cash_entries'
    
    def get_queryset(self):
        # The ASP.NET SqlDataSource Order by Id Desc; we maintain this
        return CashEntry.objects.all().order_by('-id')

class CashEntryCreateView(LoginRequiredMixin, CreateView):
    """
    Handles creation of new cash entries.
    """
    model = CashEntry
    form_class = CashEntryForm
    template_name = 'accounts/_cash_entry_form.html' # Partial template for modal
    success_url = reverse_lazy('cash_entry_list') # Not directly used for HTMX swap, but good practice

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add Cash Entry'
        return context

    def form_valid(self, form):
        # Inject session-dependent fields before saving
        form.instance.session_id = self.request.session.get('username', DEFAULT_SESSION_ID)
        form.instance.comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        form.instance.fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Cash entry added successfully.')
        
        if self.request.headers.get('HX-Request'):
            # Return no content for HTMX to indicate success and trigger reload
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCashEntryList' # Custom event to refresh the table
                }
            )
        return response

    def form_invalid(self, form):
        # If form is invalid, return the form with errors for HTMX to swap
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class CashEntryUpdateView(LoginRequiredMixin, UpdateView):
    """
    Handles updating existing cash entries.
    """
    model = CashEntry
    form_class = CashEntryForm
    template_name = 'accounts/_cash_entry_form.html' # Partial template for modal
    context_object_name = 'cash_entry' # For template context
    success_url = reverse_lazy('cash_entry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Cash Entry'
        return context

    def form_valid(self, form):
        # Re-inject session-dependent fields as they were part of the update command in ASP.NET
        form.instance.session_id = self.request.session.get('username', DEFAULT_SESSION_ID)
        form.instance.comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        form.instance.fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Cash entry updated successfully.')

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCashEntryList'
                }
            )
        return response
    
    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class CashEntryDeleteView(LoginRequiredMixin, DeleteView):
    """
    Handles deletion of cash entries.
    """
    model = CashEntry
    template_name = 'accounts/_cash_entry_confirm_delete.html' # Partial template for modal
    context_object_name = 'cash_entry'
    success_url = reverse_lazy('cash_entry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Confirm Delete Cash Entry'
        return context

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Cash entry deleted successfully.')

        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCashEntryList'
                }
            )
        return response

# --- Bank Entry Views ---
class BankEntryListView(LoginRequiredMixin, ListView):
    """
    Renders the list of bank entries for HTMX partial loading.
    Simulates the stored procedure call from ASP.NET FillGrid().
    """
    model = BankEntry
    template_name = 'accounts/_bank_entry_table.html' # This is a partial for HTMX
    context_object_name = 'bank_entries'
    
    def get_queryset(self):
        # Replicating [GetBank_Entry] stored procedure logic.
        # Assuming it returned all bank entries with bank name.
        # In a real scenario, this might be a complex query.
        return BankEntry.objects.select_related('bank').all().order_by('-id')

class BankEntryCreateView(LoginRequiredMixin, CreateView):
    """
    Handles creation of new bank entries.
    """
    model = BankEntry
    form_class = BankEntryForm
    template_name = 'accounts/_bank_entry_form.html' # Partial template for modal
    success_url = reverse_lazy('bank_entry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add Bank Entry'
        return context

    def form_valid(self, form):
        # Inject session-dependent fields before saving
        form.instance.session_id = self.request.session.get('username', DEFAULT_SESSION_ID)
        form.instance.comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        form.instance.fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Bank entry added successfully.')
        
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankEntryList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class BankEntryUpdateView(LoginRequiredMixin, UpdateView):
    """
    Handles updating existing bank entries.
    """
    model = BankEntry
    form_class = BankEntryForm
    template_name = 'accounts/_bank_entry_form.html' # Partial template for modal
    context_object_name = 'bank_entry'
    success_url = reverse_lazy('bank_entry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Bank Entry'
        return context

    def form_valid(self, form):
        # Re-inject session-dependent fields on update as per ASP.NET
        form.instance.session_id = self.request.session.get('username', DEFAULT_SESSION_ID)
        form.instance.comp_id = self.request.session.get('compid', DEFAULT_COMP_ID)
        form.instance.fin_year_id = self.request.session.get('finyear', DEFAULT_FIN_YEAR_ID)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Bank entry updated successfully.')

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankEntryList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class BankEntryDeleteView(LoginRequiredMixin, DeleteView):
    """
    Handles deletion of bank entries.
    """
    model = BankEntry
    template_name = 'accounts/_bank_entry_confirm_delete.html' # Partial template for modal
    context_object_name = 'bank_entry'
    success_url = reverse_lazy('bank_entry_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Confirm Delete Bank Entry'
        return context

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Bank entry deleted successfully.')

        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBankEntryList'
                }
            )
        return response
```

#### 4.4 Templates (`accounts/templates/accounts/`)

The templates are structured for HTMX-driven dynamic content. The main dashboard template loads the initial structure and modal placeholders. Partial templates are used for forms and table content, which are swapped in by HTMX.

**`accounts/templates/accounts/cash_bank_entry_dashboard.html`**
This is the main page for Cash/Bank Entry, serving as the `Cash_Bank_Entry.aspx` equivalent.

```html
{% extends 'core/base.html' %}

{% block title %}Cash / Bank Entry - AutoERP{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-extrabold text-gray-900 mb-6 text-center">Cash / Bank Entry Management</h1>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Cash Entry Section -->
        <div class="bg-white rounded-lg shadow-xl p-6">
            <div class="flex justify-between items-center mb-5 border-b pb-3">
                <h2 class="text-2xl font-semibold text-gray-800">Cash Entry</h2>
                <button 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                    hx-get="{% url 'cash_entry_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Cash Entry
                </button>
            </div>
            
            <div id="cashEntryTable-container"
                 hx-trigger="load, refreshCashEntryList from:body"
                 hx-get="{% url 'cash_entry_list' %}"
                 hx-swap="innerHTML"
                 class="min-h-[200px] flex items-center justify-center">
                <!-- DataTables for Cash Entry will be loaded here via HTMX -->
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Cash Entries...</p>
                </div>
            </div>
        </div>

        <!-- Bank Entry Section -->
        <div class="bg-white rounded-lg shadow-xl p-6">
            <div class="flex justify-between items-center mb-5 border-b pb-3">
                <h2 class="text-2xl font-semibold text-gray-800">Bank Entry</h2>
                <button 
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                    hx-get="{% url 'bank_entry_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Add New Bank Entry
                </button>
            </div>
            
            <div id="bankEntryTable-container"
                 hx-trigger="load, refreshBankEntryList from:body"
                 hx-get="{% url 'bank_entry_list' %}"
                 hx-swap="innerHTML"
                 class="min-h-[200px] flex items-center justify-center">
                <!-- DataTables for Bank Entry will be loaded here via HTMX -->
                <div class="text-center">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                    <p class="mt-2 text-gray-600">Loading Bank Entries...</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 transform transition-all duration-300 scale-95 opacity-0"
             _="on load add .scale-100 .opacity-100 to me"
             ></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for modal visibility (already handled by _ attribute)
    // No additional JS needed beyond DataTables and Alpine's direct `_` attributes
</script>
{% endblock %}
```

**`accounts/templates/accounts/_cash_entry_table.html`**
This partial template renders the DataTables for cash entries.

```html
<div class="overflow-x-auto">
    <table id="cashEntryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th scope="col" class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in cash_entries %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ entry.sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ entry.sys_time }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ entry.get_formatted_amount }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium flex justify-center space-x-2">
                    <button 
                        class="text-yellow-600 hover:text-yellow-900 px-3 py-1 border border-yellow-600 rounded-md transition duration-300 ease-in-out hover:bg-yellow-100"
                        hx-get="{% url 'cash_entry_edit' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 px-3 py-1 border border-red-600 rounded-md transition duration-300 ease-in-out hover:bg-red-100"
                        hx-get="{% url 'cash_entry_delete' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500 text-lg">No cash entries to display.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once, or destroyed and re-initialized
    // This script block runs when HTMX loads the content.
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#cashEntryTable')) {
            $('#cashEntryTable').DataTable().destroy();
        }
        $('#cashEntryTable').DataTable({
            "paging": true,
            "searching": true,
            "info": true,
            "order": [[ 0, "asc" ]], // Order by SN by default
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [4] } // Disable sorting on Actions column
            ]
        });
    });
</script>
```

**`accounts/templates/accounts/_bank_entry_table.html`**
This partial template renders the DataTables for bank entries.

```html
<div class="overflow-x-auto">
    <table id="bankEntryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bank Name</th>
                <th scope="col" class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th scope="col" class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in bank_entries %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ entry.sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ entry.sys_time }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ entry.bank.name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ entry.get_formatted_amount }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium flex justify-center space-x-2">
                    <button 
                        class="text-yellow-600 hover:text-yellow-900 px-3 py-1 border border-yellow-600 rounded-md transition duration-300 ease-in-out hover:bg-yellow-100"
                        hx-get="{% url 'bank_entry_edit' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900 px-3 py-1 border border-red-600 rounded-md transition duration-300 ease-in-out hover:bg-red-100"
                        hx-get="{% url 'bank_entry_delete' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500 text-lg">No bank entries to display.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#bankEntryTable')) {
            $('#bankEntryTable').DataTable().destroy();
        }
        $('#bankEntryTable').DataTable({
            "paging": true,
            "searching": true,
            "info": true,
            "order": [[ 0, "asc" ]], // Order by SN by default
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [5] } // Disable sorting on Actions column
            ]
        });
    });
</script>
```

**`accounts/templates/accounts/_cash_entry_form.html`**
This partial template renders the form for adding/editing Cash entries.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`accounts/templates/accounts/_bank_entry_form.html`**
This partial template renders the form for adding/editing Bank entries.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`accounts/templates/accounts/_cash_entry_confirm_delete.html`**
This partial template provides a confirmation dialog for deleting cash entries.

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ title }}</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this cash entry?</p>
    <p class="text-gray-600 mb-6">Amount: <span class="font-bold">{{ cash_entry.get_formatted_amount }}</span> on <span class="font-bold">{{ cash_entry.sys_date }}</span></p>

    <form hx-post="{% url 'cash_entry_delete' cash_entry.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4 mt-6">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`accounts/templates/accounts/_bank_entry_confirm_delete.html`**
This partial template provides a confirmation dialog for deleting bank entries.

```html
<div class="p-6 text-center">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">{{ title }}</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this bank entry?</p>
    <p class="text-gray-600 mb-6">Bank: <span class="font-bold">{{ bank_entry.bank.name }}</span>, Amount: <span class="font-bold">{{ bank_entry.get_formatted_amount }}</span> on <span class="font-bold">{{ bank_entry.sys_date }}</span></p>

    <form hx-post="{% url 'bank_entry_delete' bank_entry.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4 mt-6">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

This file defines the URL patterns for accessing the Cash and Bank entry features.

```python
from django.urls import path
from .views import (
    CashBankEntryDashboardView,
    CashEntryListView, CashEntryCreateView, CashEntryUpdateView, CashEntryDeleteView,
    BankEntryListView, BankEntryCreateView, BankEntryUpdateView, BankEntryDeleteView,
)

urlpatterns = [
    # Main dashboard for both cash and bank entries
    path('cash_bank_entry/', CashBankEntryDashboardView.as_view(), name='cash_bank_entry_dashboard'),

    # Cash Entry URLs (HTMX targets for partials)
    path('cash_entry/list/', CashEntryListView.as_view(), name='cash_entry_list'),
    path('cash_entry/add/', CashEntryCreateView.as_view(), name='cash_entry_add'),
    path('cash_entry/edit/<int:pk>/', CashEntryUpdateView.as_view(), name='cash_entry_edit'),
    path('cash_entry/delete/<int:pk>/', CashEntryDeleteView.as_view(), name='cash_entry_delete'),

    # Bank Entry URLs (HTMX targets for partials)
    path('bank_entry/list/', BankEntryListView.as_view(), name='bank_entry_list'),
    path('bank_entry/add/', BankEntryCreateView.as_view(), name='bank_entry_add'),
    path('bank_entry/edit/<int:pk>/', BankEntryUpdateView.as_view(), name='bank_entry_edit'),
    path('bank_entry/delete/<int:pk>/', BankEntryDeleteView.as_view(), name='bank_entry_delete'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for models and views ensure the migrated application functions as expected and maintains data integrity.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import Bank, CashEntry, BankEntry
from datetime import datetime
import json

# Setup mock session for tests
# In a real application, you'd use Django's auth system or session middleware effectively.
class MockSession:
    def __init__(self):
        self.session_data = {
            'username': 'testuser',
            'compid': 1,
            'finyear': 2024,
        }
    def get(self, key, default=None):
        return self.session_data.get(key, default)

class AccountsTestSetup(TestCase):
    """
    Base setup for accounts tests to create common test data.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a Bank for testing BankEntry
        Bank.objects.create(id=1, name='Test Bank A')
        Bank.objects.create(id=2, name='Test Bank B')
        Bank.objects.create(id=4, name='Excluded Bank') # This bank should be excluded from dropdown

        # Create initial CashEntry data
        CashEntry.objects.create(
            id=101,
            sys_date='01-01-2024',
            sys_time='10:00:00',
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            amt=100.50
        )
        CashEntry.objects.create(
            id=102,
            sys_date='02-01-2024',
            sys_time='11:00:00',
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            amt=200.75
        )

        # Create initial BankEntry data
        BankEntry.objects.create(
            id=201,
            sys_date='01-01-2024',
            sys_time='10:30:00',
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            amt=500.00,
            bank=Bank.objects.get(id=1)
        )
        BankEntry.objects.create(
            id=202,
            sys_date='02-01-2024',
            sys_time='11:30:00',
            session_id='testuser',
            comp_id=1,
            fin_year_id=2024,
            amt=750.25,
            bank=Bank.objects.get(id=2)
        )

class BankModelTest(AccountsTestSetup):
    def test_bank_creation(self):
        bank = Bank.objects.get(id=1)
        self.assertEqual(bank.name, 'Test Bank A')
        self.assertEqual(str(bank), 'Test Bank A')

class CashEntryModelTest(AccountsTestSetup):
    def test_cash_entry_creation(self):
        entry = CashEntry.objects.get(id=101)
        self.assertEqual(entry.amt, 100.50)
        self.assertEqual(entry.session_id, 'testuser')
        self.assertIsNotNone(entry.sys_date)
        self.assertIsNotNone(entry.sys_time)

    def test_get_formatted_amount(self):
        entry = CashEntry.objects.get(id=101)
        self.assertEqual(entry.get_formatted_amount(), '100.50') # Assuming locale formats .50 as .50

    def test_save_updates_sys_date_time(self):
        # Create a new entry and check date/time
        new_entry = CashEntry.objects.create(
            id=103,
            session_id='anotheruser',
            comp_id=2,
            fin_year_id=2025,
            amt=300.00
        )
        current_date = datetime.now().strftime('%d-%m-%Y')
        current_time_prefix = datetime.now().strftime('%H:') # Compare hour and minute
        self.assertEqual(new_entry.sys_date, current_date)
        self.assertTrue(new_entry.sys_time.startswith(current_time_prefix))

class BankEntryModelTest(AccountsTestSetup):
    def test_bank_entry_creation(self):
        entry = BankEntry.objects.get(id=201)
        self.assertEqual(entry.amt, 500.00)
        self.assertEqual(entry.bank.name, 'Test Bank A')

    def test_get_bank_info(self):
        entry = BankEntry.objects.get(id=201)
        self.assertEqual(entry.get_bank_info(), 'Test Bank A - 500.00')

    def test_save_updates_sys_date_time(self):
        new_bank_entry = BankEntry.objects.create(
            id=203,
            session_id='anotheruser',
            comp_id=2,
            fin_year_id=2025,
            amt=400.00,
            bank=Bank.objects.get(id=2)
        )
        current_date = datetime.now().strftime('%d-%m-%Y')
        current_time_prefix = datetime.now().strftime('%H:')
        self.assertEqual(new_bank_entry.sys_date, current_date)
        self.assertTrue(new_bank_entry.sys_time.startswith(current_time_prefix))


class CashEntryViewsTest(AccountsTestSetup):
    def setUp(self):
        self.client = Client()
        # Mocking session for LoginRequiredMixin and session-dependent fields
        self.client.session['username'] = 'testuser'
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2024

    def test_dashboard_view(self):
        response = self.client.get(reverse('cash_bank_entry_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cash_bank_entry_dashboard.html')

    def test_cash_entry_list_view_htmx(self):
        response = self.client.get(reverse('cash_entry_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_cash_entry_table.html')
        self.assertContains(response, '100.50') # Check for existing data
        self.assertContains(response, '200.75')

    def test_cash_entry_create_view_get_htmx(self):
        response = self.client.get(reverse('cash_entry_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_cash_entry_form.html')
        self.assertContains(response, 'Add Cash Entry')

    def test_cash_entry_create_view_post_htmx_success(self):
        initial_count = CashEntry.objects.count()
        data = {'amt': '350.20'}
        response = self.client.post(reverse('cash_entry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success (No Content)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCashEntryList', response.headers['HX-Trigger'])
        self.assertEqual(CashEntry.objects.count(), initial_count + 1)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Cash entry added successfully.')
        self.assertTrue(CashEntry.objects.filter(amt=350.20).exists())

    def test_cash_entry_create_view_post_htmx_invalid(self):
        initial_count = CashEntry.objects.count()
        data = {'amt': 'invalid_amount'} # Invalid amount
        response = self.client.post(reverse('cash_entry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'accounts/_cash_entry_form.html')
        self.assertContains(response, 'Amount must be a valid number')
        self.assertEqual(CashEntry.objects.count(), initial_count) # No new object created

    def test_cash_entry_update_view_get_htmx(self):
        entry = CashEntry.objects.get(id=101)
        response = self.client.get(reverse('cash_entry_edit', args=[entry.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_cash_entry_form.html')
        self.assertContains(response, 'Edit Cash Entry')
        self.assertContains(response, 'value="100.500"') # Check if current amount is pre-filled

    def test_cash_entry_update_view_post_htmx_success(self):
        entry = CashEntry.objects.get(id=101)
        data = {'amt': '150.00'}
        response = self.client.post(reverse('cash_entry_edit', args=[entry.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCashEntryList', response.headers['HX-Trigger'])
        entry.refresh_from_db()
        self.assertEqual(entry.amt, 150.00)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Cash entry updated successfully.')

    def test_cash_entry_delete_view_get_htmx(self):
        entry = CashEntry.objects.get(id=101)
        response = self.client.get(reverse('cash_entry_delete', args=[entry.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_cash_entry_confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete this cash entry?')
        self.assertContains(response, 'Amount: 100.50')

    def test_cash_entry_delete_view_post_htmx_success(self):
        entry = CashEntry.objects.get(id=101)
        initial_count = CashEntry.objects.count()
        response = self.client.post(reverse('cash_entry_delete', args=[entry.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCashEntryList', response.headers['HX-Trigger'])
        self.assertEqual(CashEntry.objects.count(), initial_count - 1)
        self.assertFalse(CashEntry.objects.filter(id=101).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Cash entry deleted successfully.')

class BankEntryViewsTest(AccountsTestSetup):
    def setUp(self):
        self.client = Client()
        self.client.session['username'] = 'testuser'
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2024

    def test_bank_entry_list_view_htmx(self):
        response = self.client.get(reverse('bank_entry_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_bank_entry_table.html')
        self.assertContains(response, 'Test Bank A')
        self.assertContains(response, '500.00')

    def test_bank_entry_create_view_get_htmx(self):
        response = self.client.get(reverse('bank_entry_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_bank_entry_form.html')
        self.assertContains(response, 'Add Bank Entry')
        self.assertNotContains(response, 'Excluded Bank') # Check if excluded bank is not in dropdown

    def test_bank_entry_create_view_post_htmx_success(self):
        initial_count = BankEntry.objects.count()
        data = {'amt': '450.75', 'bank': Bank.objects.get(id=1).id}
        response = self.client.post(reverse('bank_entry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBankEntryList', response.headers['HX-Trigger'])
        self.assertEqual(BankEntry.objects.count(), initial_count + 1)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Bank entry added successfully.')
        self.assertTrue(BankEntry.objects.filter(amt=450.75, bank__id=1).exists())

    def test_bank_entry_create_view_post_htmx_invalid(self):
        initial_count = BankEntry.objects.count()
        data = {'amt': 'invalid', 'bank': Bank.objects.get(id=1).id}
        response = self.client.post(reverse('bank_entry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_bank_entry_form.html')
        self.assertContains(response, 'Amount must be a valid number')
        self.assertEqual(BankEntry.objects.count(), initial_count)

    def test_bank_entry_update_view_get_htmx(self):
        entry = BankEntry.objects.get(id=201)
        response = self.client.get(reverse('bank_entry_edit', args=[entry.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_bank_entry_form.html')
        self.assertContains(response, 'Edit Bank Entry')
        self.assertContains(response, 'value="500.000"')
        self.assertContains(response, '<option value="1" selected>') # Check selected bank

    def test_bank_entry_update_view_post_htmx_success(self):
        entry = BankEntry.objects.get(id=201)
        data = {'amt': '600.00', 'bank': Bank.objects.get(id=2).id}
        response = self.client.post(reverse('bank_entry_edit', args=[entry.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBankEntryList', response.headers['HX-Trigger'])
        entry.refresh_from_db()
        self.assertEqual(entry.amt, 600.00)
        self.assertEqual(entry.bank.id, 2)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Bank entry updated successfully.')

    def test_bank_entry_delete_view_get_htmx(self):
        entry = BankEntry.objects.get(id=201)
        response = self.client.get(reverse('bank_entry_delete', args=[entry.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/_bank_entry_confirm_delete.html')
        self.assertContains(response, 'Are you sure you want to delete this bank entry?')
        self.assertContains(response, 'Bank: Test Bank A, Amount: 500.00')

    def test_bank_entry_delete_view_post_htmx_success(self):
        entry = BankEntry.objects.get(id=201)
        initial_count = BankEntry.objects.count()
        response = self.client.post(reverse('bank_entry_delete', args=[entry.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshBankEntryList', response.headers['HX-Trigger'])
        self.assertEqual(BankEntry.objects.count(), initial_count - 1)
        self.assertFalse(BankEntry.objects.filter(id=201).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Bank entry deleted successfully.')

```

### Step 5: HTMX and Alpine.js Integration

The provided templates demonstrate core HTMX and Alpine.js integration:

*   **Modals:** A single modal (`#modal`) is used for all add, edit, and delete operations. HTMX `hx-get` loads the partial form/confirmation template into `#modalContent`, and Alpine.js's `_=` attributes control the modal's `hidden`/`is-active` class for display and dismissal.
*   **Dynamic Table Loading/Refreshing:**
    *   `hx-get` on `<div>` elements (`#cashEntryTable-container`, `#bankEntryTable-container`) loads the respective DataTables partials (`_cash_entry_table.html`, `_bank_entry_table.html`) on page load (`hx-trigger="load"`).
    *   After a successful form submission (create/update/delete), the server responds with `HTTP 204 No Content` and an `HX-Trigger` header (e.g., `refreshCashEntryList`). This custom event is listened for by the table containers (`hx-trigger="load, refreshCashEntryList from:body"`) to trigger a refresh of the table data.
*   **Form Submission:** Forms use `hx-post` with `hx-swap="none"` to prevent the entire page from swapping after submission, relying solely on `HX-Trigger` for updates.
*   **DataTables:** Initialized within the partial templates using jQuery, ensuring that tables are correctly rendered and become interactive upon HTMX content swap. The `destroy()` method is called before re-initialization to prevent errors if the table is swapped multiple times.
*   **Loading Indicators:** Simple `htmx-indicator` classes are added to buttons and containers to show loading spinners during HTMX requests, providing visual feedback to the user.

---

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET Cash/Bank Entry module to Django. By following these steps, and utilizing AI-assisted automation, the transition can be executed efficiently, resulting in a modern, maintainable, and high-performing application. Remember to configure your Django project's `settings.py` for database connection, `INSTALLED_APPS`, and template directories to ensure this code runs successfully. Ensure your `urls.py` in the main project includes `path('accounts/', include('accounts.urls'))`.