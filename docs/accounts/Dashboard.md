The ASP.NET code provided (Dashboard.aspx and Dashboard.aspx.cs) is remarkably minimal, essentially a placeholder page with no specific business logic, database interactions, or UI components defined within the given snippets. The `Page_Load` method is empty, and the ASPX only contains empty `asp:Content` tags.

Given this lack of concrete information, this modernization plan will proceed by inferring a common scenario for a "Dashboard" page within an "Accounts" module (as suggested by `Module_Accounts_Dashboard`). We will assume the dashboard's purpose is to display a summary of `DashboardItem` entries, which might represent key performance indicators, alerts, or quick access items. We will demonstrate a full CRUD (Create, Read, Update, Delete) cycle for these `DashboardItem`s using modern Django, HTMX, Alpine.js, and DataTables, adhering strictly to the "Fat Model, Thin View" principle and automation-first migration strategy.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The initial ASP.NET code provides no functional details. Therefore, we will simulate a common dashboard requirement: managing simple dashboard items.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code lacks explicit database interactions, we will infer a common data structure for a dashboard: `DashboardItem`.

**Inferred Table Name:** `tbl_dashboard_items`

**Inferred Columns:**
*   `id` (Primary Key, integer)
*   `item_name` (Text, e.g., 'Total Sales', 'New Customers')
*   `item_value` (Decimal, e.g., '12345.67', '42')
*   `item_category` (Text, e.g., 'Financial', 'Customer', 'Operational')
*   `description` (Text, optional longer description)
*   `is_active` (Boolean, to enable/disable dashboard items)
*   `last_updated` (DateTime, timestamp of last modification)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Based on the inferred `DashboardItem` schema and typical dashboard needs, we assume standard CRUD operations are required for managing these items:

*   **Create**: Add new `DashboardItem` records (e.g., a new KPI to track).
*   **Read**: Display a list of all `DashboardItem`s with their details.
*   **Update**: Modify existing `DashboardItem` records (e.g., change a value, description, or category).
*   **Delete**: Remove `DashboardItem` records.
*   **Validation**: Ensure `item_name` and `item_value` are present and correctly formatted during create/update.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Given the Dashboard context and our target Django/HTMX/Alpine.js stack:

*   **GridView/List Display**: This will be replaced by a DataTables-powered HTML table, dynamically loaded and updated via HTMX.
*   **Form Input (TextBox, DropDownList)**: User input for `DashboardItem` creation/editing will be handled via Django forms, rendered into a modal (`_dashboarditem_form.html`) and submitted via HTMX.
*   **Buttons (Button, LinkButton)**: Actions like "Add New Item", "Edit", and "Delete" will be implemented as HTML buttons with HTMX attributes to trigger modal display and form submissions without full page reloads.
*   **Client-Side Interaction**: All client-side interactions (modal toggling, dynamic content loading, form submission feedback) will be managed exclusively by HTMX and Alpine.js, eliminating the need for custom JavaScript.

## Step 4: Generate Django Code

For this migration, we will create a new Django application named `dashboard`.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `DashboardItem` model will map to `tbl_dashboard_items`. It includes fields for name, value, category, description, active status, and a last updated timestamp. It also includes business logic for calculation and display.

**File:** `dashboard/models.py`

```python
from django.db import models
from django.utils import timezone

class DashboardItem(models.Model):
    """
    Represents a single item or metric displayed on the dashboard.
    Corresponds to 'tbl_dashboard_items' in the legacy database.
    """
    id = models.AutoField(db_column='id', primary_key=True)
    name = models.CharField(db_column='item_name', max_length=255, verbose_name="Item Name")
    value = models.DecimalField(db_column='item_value', max_digits=10, decimal_places=2, verbose_name="Value")
    category = models.CharField(db_column='item_category', max_length=100, blank=True, null=True, verbose_name="Category")
    description = models.TextField(db_column='description', blank=True, null=True, verbose_name="Description")
    is_active = models.BooleanField(db_column='is_active', default=True, verbose_name="Active")
    last_updated = models.DateTimeField(db_column='last_updated', auto_now=True, verbose_name="Last Updated")

    class Meta:
        managed = False  # Set to True if Django should manage the table, False for existing
        db_table = 'tbl_dashboard_items'
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['name'] # Default ordering for lists

    def __str__(self):
        return f"{self.name}: {self.value}"

    def get_display_value(self):
        """
        Business logic: Returns a formatted value suitable for display.
        This method is a placeholder; real dashboards might have complex formatting.
        """
        # Example: Add currency or percentage formatting based on category
        if self.category == 'Financial':
            return f"${self.value:,.2f}"
        elif self.category == 'Percentage':
            return f"{self.value:.2f}%"
        return f"{self.value:,.0f}" # Default format

    def activate(self):
        """
        Business logic: Activates the dashboard item.
        """
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False

    def deactivate(self):
        """
        Business logic: Deactivates the dashboard item.
        """
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for `DashboardItem`, including all editable fields and Tailwind CSS classes for styling.

**File:** `dashboard/forms.py`

```python
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem objects.
    """
    class Meta:
        model = DashboardItem
        fields = ['name', 'value', 'category', 'description', 'is_active']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'category': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'name': 'Item Name',
            'value': 'Value',
            'category': 'Category',
            'description': 'Description',
            'is_active': 'Is Active?',
        }
        
    def clean_value(self):
        """
        Custom validation for the value field.
        Ensures the value is positive.
        """
        value = self.cleaned_data.get('value')
        if value is not None and value < 0:
            raise forms.ValidationError("Value cannot be negative.")
        return value

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We'll define views for listing, creating, updating, and deleting `DashboardItem`s. A special `TablePartialView` is added to handle HTMX requests for refreshing the DataTables content independently. Views remain thin, delegating complex logic to models or forms.

**File:** `dashboard/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemListView(ListView):
    """
    Displays a list of all DashboardItem objects.
    Main view, loads the full page template and uses HTMX to load the table.
    """
    model = DashboardItem
    template_name = 'dashboard/dashboarditem_list.html'
    context_object_name = 'dashboard_items' # Renamed for clarity in template

class DashboardItemTablePartialView(ListView):
    """
    Returns only the table portion of the DashboardItem list,
    designed to be loaded via HTMX for dynamic updates.
    """
    model = DashboardItem
    template_name = 'dashboard/_dashboarditem_table.html' # Partial template
    context_object_name = 'dashboard_items' # Renamed for clarity in template
    
class DashboardItemCreateView(CreateView):
    """
    Handles the creation of new DashboardItem objects.
    Uses HTMX for form submission and modal interaction.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard/_dashboarditem_form.html' # Partial template for modal
    success_url = reverse_lazy('dashboarditem_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Business logic can be called here or in model. Example:
        # form.instance.activate() # Call a method on the model instance
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, if form is invalid, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class DashboardItemUpdateView(UpdateView):
    """
    Handles the updating of existing DashboardItem objects.
    Uses HTMX for form submission and modal interaction.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard/_dashboarditem_form.html' # Partial template for modal
    success_url = reverse_lazy('dashboarditem_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Business logic can be called here or in model. Example:
        # form.instance.deactivate() # Call a method on the model instance
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, if form is invalid, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class DashboardItemDeleteView(DeleteView):
    """
    Handles the deletion of DashboardItem objects.
    Uses HTMX for confirmation and deletion.
    """
    model = DashboardItem
    template_name = 'dashboard/_dashboarditem_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('dashboarditem_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates are designed with DRY principles, utilizing partials for HTMX dynamic content. All templates extend `core/base.html` (not shown here, as per instructions).

**File:** `dashboard/templates/dashboard/dashboarditem_list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal then add .pointer-events-auto to #modal">
            Add New Item
        </button>
    </div>

    <!-- Container for the dynamically loaded table -->
    <div id="dashboardItemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- Initial loading state -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>

    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal"
         class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden items-center justify-center transition-opacity duration-300 opacity-0 pointer-events-none"
         _="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from me then remove .pointer-events-auto from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 relative">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- No specific Alpine.js component initialization needed if only used for UI state toggles -->
{% endblock %}
```

**File:** `dashboard/templates/dashboard/_dashboarditem_table.html`

```html
<div class="overflow-x-auto">
    <table id="dashboardItemTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for item in dashboard_items %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ item.name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ item.get_display_value }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ item.category|default:"-" }}</td>
                <td class="py-3 px-4 text-sm text-gray-500 max-w-xs truncate">{{ item.description|default:"-" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if item.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ item.is_active|yesno:"Yes,No" }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ item.last_updated|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'dashboarditem_edit' item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .pointer-events-auto to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-600 hover:bg-red-700 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-get="{% url 'dashboarditem_delete' item.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then add .opacity-100 to #modal then add .pointer-events-auto to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-6 text-center text-gray-500">No dashboard items found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table is loaded via HTMX
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#dashboardItemTable')) {
            $('#dashboardItemTable').DataTable().destroy();
        }
        $('#dashboardItemTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Enable responsive design
            "columnDefs": [ // Adjust column widths and visibility
                { "orderable": false, "targets": [0, 7] } // Disable ordering for SN and Actions
            ]
        });
    });
</script>
```

**File:** `dashboard/templates/dashboard/_dashboarditem_form.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-600 text-sm mt-1 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal then remove .pointer-events-auto from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                Save Item
            </button>
        </div>
    </form>
</div>
```

**File:** `dashboard/templates/dashboard/_dashboarditem_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the dashboard item: <span class="font-bold text-red-600">{{ object.name }}</span>?
        This action cannot be undone.
    </p>

    <form hx-post="{% url 'dashboarditem_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-gray-700 bg-gray-200 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal then remove .pointer-events-auto from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                Delete Item
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

This includes URLs for the main list page, and separate URLs for the partial views loaded via HTMX (add/edit/delete forms, and the table itself).

**File:** `dashboard/urls.py`

```python
from django.urls import path
from .views import (
    DashboardItemListView,
    DashboardItemTablePartialView,
    DashboardItemCreateView,
    DashboardItemUpdateView,
    DashboardItemDeleteView
)

urlpatterns = [
    # Main list page
    path('dashboard-items/', DashboardItemListView.as_view(), name='dashboarditem_list'),

    # HTMX partial views for CRUD operations and table refresh
    path('dashboard-items/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'),
    path('dashboard-items/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('dashboard-items/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('dashboard-items/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]
```
**Registering App URLs (in your project's `urls.py`):**
```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('', include('dashboard.urls')), # Include your dashboard app URLs
    # path('accounts/', include('accounts.urls')), # If you have a separate accounts app
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for model methods and integration tests for all views, including HTMX interactions, are crucial for ensuring the migrated functionality is robust and correct.

**File:** `dashboard/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from .models import DashboardItem
from .forms import DashboardItemForm
from django.contrib.messages import get_messages

class DashboardItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects used by all test methods
        cls.item1 = DashboardItem.objects.create(
            name='Total Sales',
            value=Decimal('150000.75'),
            category='Financial',
            description='Total sales revenue for the quarter.',
            is_active=True
        )
        cls.item2 = DashboardItem.objects.create(
            name='New Customers',
            value=Decimal('123'),
            category='Customer',
            description='Number of new customer sign-ups this month.',
            is_active=False
        )

    def test_dashboard_item_creation(self):
        """Test that DashboardItem instances are created correctly."""
        self.assertEqual(self.item1.name, 'Total Sales')
        self.assertEqual(self.item1.value, Decimal('150000.75'))
        self.assertEqual(self.item1.category, 'Financial')
        self.assertTrue(self.item1.is_active)
        self.assertEqual(self.item1.pk, self.item1.id) # Ensure PK is correctly mapped

    def test_name_label(self):
        """Test the verbose name for the 'name' field."""
        field_label = self.item1._meta.get_field('name').verbose_name
        self.assertEqual(field_label, 'Item Name')

    def test_db_table_and_managed(self):
        """Verify Meta options are correctly set."""
        self.assertEqual(DashboardItem._meta.db_table, 'tbl_dashboard_items')
        self.assertFalse(DashboardItem._meta.managed)

    def test_str_method(self):
        """Test the __str__ method provides expected output."""
        self.assertEqual(str(self.item1), "Total Sales: 150000.75")

    def test_get_display_value_financial(self):
        """Test get_display_value for Financial category."""
        self.assertEqual(self.item1.get_display_value(), "$150,000.75")

    def test_get_display_value_default(self):
        """Test get_display_value for non-Financial category."""
        self.assertEqual(self.item2.get_display_value(), "123") # Default no currency

    def test_activate_method(self):
        """Test the activate method."""
        initial_active_status = self.item2.is_active
        self.assertFalse(initial_active_status)
        self.assertTrue(self.item2.activate()) # Should return True as it changed
        self.assertTrue(self.item2.is_active)
        self.assertFalse(self.item2.activate()) # Should return False as it didn't change

    def test_deactivate_method(self):
        """Test the deactivate method."""
        initial_active_status = self.item1.is_active
        self.assertTrue(initial_active_status)
        self.assertTrue(self.item1.deactivate()) # Should return True as it changed
        self.assertFalse(self.item1.is_active)
        self.assertFalse(self.item1.deactivate()) # Should return False as it didn't change

class DashboardItemFormTest(TestCase):
    def test_form_valid(self):
        """Test that the form is valid with correct data."""
        form = DashboardItemForm(data={
            'name': 'Test Item',
            'value': 100.50,
            'category': 'Test',
            'description': 'A test description.',
            'is_active': True
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_missing_name(self):
        """Test form invalidity when name is missing."""
        form = DashboardItemForm(data={
            'value': 100.50,
            'category': 'Test',
            'description': 'A test description.',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)

    def test_form_invalid_negative_value(self):
        """Test custom validation for negative value."""
        form = DashboardItemForm(data={
            'name': 'Negative Item',
            'value': -50.00,
            'category': 'Test',
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('value', form.errors)
        self.assertIn('Value cannot be negative.', form.errors['value'])

class DashboardItemViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.item = DashboardItem.objects.create(
            name='Test KPI',
            value=Decimal('999.99'),
            category='General',
            description='A generic test dashboard item.',
            is_active=True
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test the DashboardItem list view GET request."""
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboarditem_list.html')
        self.assertContains(response, 'Dashboard Items')
        self.assertIn('dashboard_items', response.context)
        self.assertEqual(len(response.context['dashboard_items']), 1)
        self.assertEqual(response.context['dashboard_items'][0], self.item)

    def test_table_partial_view_get(self):
        """Test the HTMX partial table view GET request."""
        response = self.client.get(reverse('dashboarditem_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/_dashboarditem_table.html')
        self.assertIn('dashboard_items', response.context)
        self.assertContains(response, self.item.name) # Check if item is in the table

    def test_create_view_get(self):
        """Test the create view GET request (for modal content)."""
        response = self.client.get(reverse('dashboarditem_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/_dashboarditem_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Dashboard Item')

    def test_create_view_post_valid(self):
        """Test valid POST request for creating a DashboardItem."""
        data = {
            'name': 'New Metric',
            'value': '500.00',
            'category': 'Marketing',
            'description': 'New customer acquisition cost.',
            'is_active': 'on'
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        # Expect 204 No Content for successful HTMX POST
        self.assertEqual(response.status_code, 204)
        self.assertTrue(DashboardItem.objects.filter(name='New Metric').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item added successfully.')

    def test_create_view_post_invalid(self):
        """Test invalid POST request for creating a DashboardItem (should re-render form)."""
        data = {
            'name': '', # Invalid data
            'value': '-100',
            'category': 'Marketing',
            'is_active': 'on'
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form again with errors
        self.assertTemplateUsed(response, 'dashboard/_dashboarditem_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Value cannot be negative.')

    def test_update_view_get(self):
        """Test the update view GET request."""
        response = self.client.get(reverse('dashboarditem_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/_dashboarditem_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Dashboard Item')
        self.assertContains(response, self.item.name) # Check if form pre-fills with item data

    def test_update_view_post_valid(self):
        """Test valid POST request for updating a DashboardItem."""
        updated_name = 'Updated KPI'
        data = {
            'name': updated_name,
            'value': '1234.56',
            'category': 'Updated',
            'description': 'An updated description.',
            'is_active': 'off' # Change status
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, updated_name)
        self.assertEqual(self.item.value, Decimal('1234.56'))
        self.assertFalse(self.item.is_active)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item updated successfully.')

    def test_delete_view_get(self):
        """Test the delete view GET request (for confirmation modal)."""
        response = self.client.get(reverse('dashboarditem_delete', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/_dashboarditem_confirm_delete.html')
        self.assertContains(response, f'Are you sure you want to delete the dashboard item: {self.item.name}?')

    def test_delete_view_post(self):
        """Test valid POST request for deleting a DashboardItem."""
        # Create a new item to delete so we don't mess with self.item for other tests
        item_to_delete = DashboardItem.objects.create(name='To Be Deleted', value=100.00)
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Item deleted successfully.')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The generated Django templates and views are designed for seamless HTMX and Alpine.js integration:

*   **HTMX for Dynamic Content**:
    *   The `dashboarditem_list.html` uses `hx-get` to load `_dashboarditem_table.html` into its `dashboardItemTable-container` on `load` and `refreshDashboardItemList` events.
    *   Buttons for "Add New Item", "Edit", and "Delete" use `hx-get` to fetch the respective `_dashboarditem_form.html` or `_dashboarditem_confirm_delete.html` into a modal container (`#modalContent`).
    *   Form submissions (`hx-post`) in `_dashboarditem_form.html` and `_dashboarditem_confirm_delete.html` use `hx-swap="none"` and rely on the Django view to send an `HX-Trigger` header (`refreshDashboardItemList`) upon success. This triggers the main list's HTMX container to re-fetch the table, ensuring the list is always up-to-date without a full page refresh.
*   **Alpine.js for UI State Management**:
    *   The `_` attribute (hyperscript, which is closely integrated with HTMX) is used to control the visibility of the modal. `on click add .flex to #modal then add .opacity-100 to #modal then add .pointer-events-auto to #modal` handles opening the modal, and similar logic is used for closing it when clicking outside or pressing cancel. This is a very lightweight way to handle UI state without full Alpine.js components unless more complex reactivity is needed.
*   **DataTables for List Views**:
    *   The `_dashboarditem_table.html` partial includes a `script` block that initializes DataTables on the `#dashboardItemTable` ID. This ensures client-side searching, sorting, and pagination are enabled. The script runs each time the table partial is loaded by HTMX, ensuring DataTables is re-initialized correctly. `$.fn.DataTable.isDataTable('#dashboardItemTable')` and `.destroy()` are used to prevent re-initialization errors.
*   **No Full Page Reloads**: All CRUD operations and list refreshes are handled dynamically using HTMX, providing a smooth, single-page application-like experience without the complexity of a full JavaScript framework.

## Final Notes

This comprehensive plan transforms the non-functional ASP.NET Dashboard stub into a modern, data-driven Django application. By leveraging AI-assisted automation, an organization can systematically:

1.  **Automate Database Mapping**: Tools can infer `db_table` and column mappings from existing schema.
2.  **Automate Model Generation**: Based on inferred schema, Django `models.py` can be largely generated.
3.  **Automate Form Generation**: `forms.py` can be generated directly from models.
4.  **Automate Basic CRUD Views**: Standard `ListView`, `CreateView`, `UpdateView`, `DeleteView` patterns are highly repetitive and suitable for automated generation, along with their `HX-Trigger` headers.
5.  **Automate Template Structure**: The `list.html`, `_table.html`, `_form.html`, and `_confirm_delete.html` patterns are highly consistent and can be generated with placeholders for fields.
6.  **Automate URL Configuration**: `urls.py` entries follow predictable patterns.
7.  **Automate Test Stubs**: Basic test cases can be generated, requiring only specific assertions to be filled in.

This approach significantly reduces manual coding effort, accelerates the migration timeline, and minimizes human error, allowing business stakeholders to oversee a transparent, predictable modernization process. The use of Django, HTMX, and Alpine.js ensures a robust, performant, and maintainable application with a modern user experience.