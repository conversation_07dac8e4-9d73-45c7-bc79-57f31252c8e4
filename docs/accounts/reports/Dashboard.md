## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

Based on the provided ASP.NET `.aspx` and C# code-behind files, it's evident that the `Dashboard.aspx` page is currently a placeholder with no explicit business logic, data interactions, or UI components defined within its scope. It primarily hooks into a master page and includes a generic `loadingNotifier.js` script. The C# code-behind only contains an empty `Page_Load` method.

Therefore, for this modernization plan, we will demonstrate how a typical "Dashboard" module, which often displays summary information or key performance indicators, would be structured in Django. We will assume a hypothetical `DashboardItem` entity that might represent configurable dashboard widgets or data points. This allows us to illustrate the full stack (Model, Form, View, Template, URL, Test) adhering to all specified modernization guidelines.

---

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code does not contain any database interaction elements (like `SqlDataSource`, direct SQL commands, or data-bound controls). It's a blank dashboard page.

**Inferred Schema (Hypothetical):**
To provide a complete example, we will infer a common scenario for a dashboard which might display various "items" or "widgets". We will assume a table named `dashboard_items` with the following columns:

*   `id` (Primary Key)
*   `name` (VARCHAR, e.g., "Total Sales", "Pending Orders")
*   `value` (VARCHAR, e.g., "$10,000", "50") - Stored as text for flexibility in displaying various data types.
*   `description` (TEXT, e.g., "Current sales figures for the month")
*   `order_display` (INTEGER, for sorting display on dashboard)

**Result:**
*   **[TABLE_NAME]:** `dashboard_items`
*   **Columns:** `id`, `name`, `value`, `description`, `order_display`

---

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET `Dashboard.aspx.cs` file has an empty `Page_Load` method, indicating no explicit backend functionality or CRUD operations are implemented directly within this page.

**Inferred Functionality (Hypothetical):**
Assuming a dashboard will eventually display data, and that data needs to be managed, we will design for the full CRUD (Create, Read, Update, Delete) for the `DashboardItem` entity. This allows for a complete demonstration of the Django modernization process.

*   **Read:** Displaying a list of `DashboardItem` objects.
*   **Create:** Adding new `DashboardItem` objects.
*   **Update:** Modifying existing `DashboardItem` objects.
*   **Delete:** Removing `DashboardItem` objects.
*   **Validation:** Basic field validation (e.g., `name` is required).

---

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The `Dashboard.aspx` page is empty of any ASP.NET controls (like `GridView`, `TextBox`, `Button`, etc.). It only defines content placeholders.

**Inferred UI Components (Hypothetical):**
For a Django modernization, we will design the following UI components to manage `DashboardItem`s:

*   **Dashboard Item List:** A table (rendered with DataTables) displaying all `DashboardItem`s, with columns for `Name`, `Value`, `Description`, and `Actions` (Edit/Delete buttons).
*   **Add New Dashboard Item Button:** A button that triggers a modal form for creating a new `DashboardItem`.
*   **Edit Dashboard Item Button:** A button next to each item that triggers a modal form for editing an existing `DashboardItem`.
*   **Delete Dashboard Item Button:** A button next to each item that triggers a modal confirmation for deleting an `DashboardItem`.
*   **Modal Forms:** HTML forms for `Create` and `Update` operations, loaded dynamically via HTMX into a modal.
*   **Confirmation Dialog:** A simple confirmation for `Delete` operations, also loaded dynamically via HTMX into a modal.

---

## Step 4: Generate Django Code

We will create a Django application named `dashboard_module` (as it's a "Module_Accounts_Reports_Dashboard" in ASP.NET, `dashboard_module` seems appropriate).

### 4.1 Models (`dashboard_module/models.py`)

```python
from django.db import models
from django.utils.text import slugify

class DashboardItem(models.Model):
    """
    Represents a configurable item or widget to be displayed on the dashboard.
    This model assumes existing database structure for modernization.
    """
    id = models.AutoField(db_column='id', primary_key=True)
    name = models.CharField(db_column='name', max_length=255, verbose_name="Item Name")
    value = models.CharField(db_column='value', max_length=255, blank=True, null=True, verbose_name="Display Value")
    description = models.TextField(db_column='description', blank=True, null=True, verbose_name="Description")
    order_display = models.IntegerField(db_column='order_display', default=0, verbose_name="Display Order")

    class Meta:
        managed = False  # Set to True if Django should manage this table
        db_table = 'dashboard_items'
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['order_display', 'name']

    def __str__(self):
        """Returns the name of the dashboard item."""
        return self.name if self.name else f"Dashboard Item {self.id}"

    def get_display_summary(self):
        """
        Business logic to format the display summary for the dashboard.
        This method keeps view logic thin.
        """
        if self.value:
            return f"{self.name}: {self.value}"
        return self.name

    def set_next_order(self):
        """
        Business logic to automatically set the display order for new items.
        """
        if not self.pk: # Only for new objects
            max_order = DashboardItem.objects.aggregate(models.Max('order_display'))['order_display__max']
            self.order_display = (max_order or 0) + 1
```

### 4.2 Forms (`dashboard_module/forms.py`)

```python
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem instances.
    Includes Tailwind CSS classes for styling.
    """
    class Meta:
        model = DashboardItem
        fields = ['name', 'value', 'description', 'order_display']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'e.g., Total Sales'
            }),
            'value': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'e.g., $1,234,567'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'rows': 3,
                'placeholder': 'A brief description of this dashboard item.'
            }),
            'order_display': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
                'placeholder': 'e.g., 1'
            }),
        }

    def clean_name(self):
        """
        Custom validation for the name field to ensure it's not empty.
        """
        name = self.cleaned_data.get('name')
        if not name:
            raise forms.ValidationError("Item Name is required.")
        return name

    def save(self, commit=True):
        """
        Override save to call model's business logic method before saving.
        """
        instance = super().save(commit=False)
        instance.set_next_order() # Example business logic in model
        if commit:
            instance.save()
        return instance
```

### 4.3 Views (`dashboard_module/views.py`)

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

class DashboardItemListView(ListView):
    """
    Displays a list of all DashboardItem objects.
    Renders the main page with a placeholder for the HTMX-loaded table.
    """
    model = DashboardItem
    template_name = 'dashboard_module/dashboarditem/list.html'
    context_object_name = 'dashboard_items'

class DashboardItemTablePartialView(ListView):
    """
    Renders only the table portion of DashboardItem list for HTMX updates.
    """
    model = DashboardItem
    template_name = 'dashboard_module/dashboarditem/_dashboarditem_table.html'
    context_object_name = 'dashboard_items'

class DashboardItemCreateView(CreateView):
    """
    Handles creation of new DashboardItem objects.
    Returns an HTMX response to refresh the list on success.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_module/dashboarditem/_dashboarditem_form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        # Business logic for saving is encapsulated in form.save() or model methods.
        # This view remains thin, focusing on orchestrating the request.
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to close modal and trigger refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Add New Dashboard Item'
        return context

class DashboardItemUpdateView(UpdateView):
    """
    Handles updating existing DashboardItem objects.
    Returns an HTMX response to refresh the list on success.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_module/dashboarditem/_dashboarditem_form.html'
    success_url = reverse_lazy('dashboarditem_list')

    def form_valid(self, form):
        # Business logic for saving is encapsulated in form.save() or model methods.
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form_title'] = 'Edit Dashboard Item'
        return context

class DashboardItemDeleteView(DeleteView):
    """
    Handles deletion of DashboardItem objects.
    Returns an HTMX response to refresh the list on success.
    """
    model = DashboardItem
    template_name = 'dashboard_module/dashboarditem/_dashboarditem_confirm_delete.html'
    success_url = reverse_lazy('dashboarditem_list')

    def delete(self, request, *args, **kwargs):
        # Business logic for deletion (if any) would be in the model.
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList'
                }
            )
        return response
```

### 4.4 Templates

#### List Template (`dashboard_module/dashboarditem/list.html`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then add .opacity-100 to #modal backdrop then add .scale-100 to #modalContent">
            <i class="fas fa-plus-circle mr-2"></i> Add New Item
        </button>
    </div>

    <div id="dashboardItemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-lg shadow-xl border border-gray-200">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>

    <!-- Modal Structure -->
    <div id="modal" class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 transition-opacity duration-300 ease-out"
         _="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from #modal backdrop then remove .scale-100 from #modalContent">
        <div id="modalContent" class="bg-white p-8 rounded-lg shadow-2xl max-w-2xl w-full transform scale-95 transition-transform duration-300 ease-out"
             _="on hx:afterOnLoad add .scale-100 to #modalContent">
            <!-- Form/Delete Confirmation loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is primarily for UI state and direct DOM manipulation not easily covered by HTMX.
    // In this specific case, HTMX handles the modal visibility and content loading,
    // so explicit Alpine.js components might not be strictly necessary beyond what HTMX offers.
    // However, if we needed to manage local form states or complex UI toggles, Alpine would be used here.
    document.addEventListener('alpine:init', () => {
        // Example: If a component needs to expose a state, e.g., for a more complex filter panel
        // Alpine.data('dashboardItemsFilter', () => ({
        //     searchTerm: '',
        //     applyFilter() {
        //         // Logic to filter the table, potentially triggering HTMX reload
        //     }
        // }));
    });

    // JavaScript for DataTables initialization (runs after HTMX loads the table content)
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'dashboardItemTable-container') {
            $('#dashboardItemTable').DataTable({
                "pagingType": "full_numbers", // For full pagination controls
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>', // Custom DOM for styling
                "language": {
                    "search": "Filter records:",
                    "lengthMenu": "Show _MENU_ entries"
                },
                "responsive": true // Enable responsive behavior
            });
        }
    });
</script>
{% endblock %}
```

#### Table Partial Template (`dashboard_module/dashboarditem/_dashboarditem_table.html`)

```html
<table id="dashboardItemTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr class="bg-gray-100 border-b border-gray-200">
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Item Name</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Display Value</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Order</th>
            <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in dashboard_items %}
        <tr class="hover:bg-gray-50 border-b border-gray-100">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.value|default:"-" }}</td>
            <td class="py-3 px-4 text-sm text-gray-700 max-w-xs truncate">{{ obj.description|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.order_display }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-medium py-1.5 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out mr-2"
                    hx-get="{% url 'dashboarditem_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal backdrop then add .scale-100 to #modalContent">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-medium py-1.5 px-3 rounded-md text-xs shadow-sm transition duration-150 ease-in-out"
                    hx-get="{% url 'dashboarditem_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal then add .opacity-100 to #modal backdrop then add .scale-100 to #modalContent">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No dashboard items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
```

#### Form Partial Template (`dashboard_module/dashboarditem/_dashboarditem_form.html`)

```html
<div class="p-4">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6 border-b pb-3">{{ form_title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}

        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <div class="mt-1 text-sm text-red-600">
                {% for error in field.errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        {% endfor %}

        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error:</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal backdrop then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save Changes
            </button>
        </div>
    </form>
</div>
```

#### Delete Confirmation Partial Template (`dashboard_module/dashboarditem/_dashboarditem_confirm_delete.html`)

```html
<div class="p-4">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Dashboard Item:
        <strong class="font-semibold">{{ object.name }}</strong>?
        This action cannot be undone.
    </p>

    <form hx-post="{% url 'dashboarditem_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then remove .opacity-100 from #modal backdrop then remove .scale-100 from #modalContent">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`dashboard_module/urls.py`)

```python
from django.urls import path
from .views import (
    DashboardItemListView,
    DashboardItemTablePartialView,
    DashboardItemCreateView,
    DashboardItemUpdateView,
    DashboardItemDeleteView
)

urlpatterns = [
    # Main list view (renders the container for the table)
    path('dashboard-items/', DashboardItemListView.as_view(), name='dashboarditem_list'),

    # HTMX endpoint for loading/refreshing the table content
    path('dashboard-items/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'),

    # HTMX endpoint for loading the create form in a modal
    path('dashboard-items/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),

    # HTMX endpoint for loading the edit form in a modal and handling submission
    path('dashboard-items/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),

    # HTMX endpoint for loading the delete confirmation in a modal and handling submission
    path('dashboard-items/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]
```

### 4.6 Tests (`dashboard_module/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardItem
from .forms import DashboardItemForm
import json

class DashboardItemModelTest(TestCase):
    """
    Unit tests for the DashboardItem model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for all tests
        cls.item1 = DashboardItem.objects.create(
            name='Total Sales',
            value='$1,234,567',
            description='Overall sales for the current fiscal year.',
            order_display=1
        )
        cls.item2 = DashboardItem.objects.create(
            name='New Customers',
            value='500',
            description='Number of new customers acquired this month.',
            order_display=2
        )

    def test_dashboard_item_creation(self):
        """Test that a DashboardItem can be created and its attributes are correct."""
        item = DashboardItem.objects.get(pk=self.item1.pk)
        self.assertEqual(item.name, 'Total Sales')
        self.assertEqual(item.value, '$1,234,567')
        self.assertEqual(item.description, 'Overall sales for the current fiscal year.')
        self.assertEqual(item.order_display, 1)

    def test_str_method(self):
        """Test the __str__ method returns the item's name."""
        item = DashboardItem.objects.get(pk=self.item1.pk)
        self.assertEqual(str(item), 'Total Sales')

    def test_get_display_summary_method(self):
        """Test the get_display_summary method returns correct formatted string."""
        item = DashboardItem.objects.get(pk=self.item1.pk)
        self.assertEqual(item.get_display_summary(), 'Total Sales: $1,234,567')

        item_no_value = DashboardItem.objects.create(name='Uptime', description='System uptime')
        self.assertEqual(item_no_value.get_display_summary(), 'Uptime')

    def test_set_next_order_method(self):
        """Test that set_next_order correctly assigns the next available order."""
        # Ensure there are at least two items from setUpTestData
        max_order_before = DashboardItem.objects.aggregate(models.Max('order_display'))['order_display__max']
        self.assertEqual(max_order_before, 2)

        new_item = DashboardItem(name='New Metric', value='10')
        new_item.set_next_order()
        self.assertEqual(new_item.order_display, 3) # Should be max_order_before + 1
        new_item.save()
        self.assertIsNotNone(new_item.pk) # Check if saved

    def test_verbose_names(self):
        """Test verbose names for model and fields."""
        self.assertEqual(DashboardItem._meta.verbose_name, 'Dashboard Item')
        self.assertEqual(DashboardItem._meta.verbose_name_plural, 'Dashboard Items')
        self.assertEqual(DashboardItem._meta.get_field('name').verbose_name, 'Item Name')
        self.assertEqual(DashboardItem._meta.get_field('value').verbose_name, 'Display Value')
        self.assertEqual(DashboardItem._meta.get_field('order_display').verbose_name, 'Display Order')

class DashboardItemFormTest(TestCase):
    """
    Unit tests for the DashboardItemForm.
    """
    def test_form_valid_data(self):
        """Test form with valid data."""
        form = DashboardItemForm(data={
            'name': 'Active Users',
            'value': '150',
            'description': 'Number of active users in the last 24 hours.',
            'order_display': 5
        })
        self.assertTrue(form.is_valid(), form.errors)

    def test_form_missing_name(self):
        """Test form with missing name (required field)."""
        form = DashboardItemForm(data={
            'name': '', # Missing name
            'value': '150',
            'description': 'Number of active users in the last 24 hours.',
            'order_display': 5
        })
        self.assertFalse(form.is_valid())
        self.assertIn('name', form.errors)
        self.assertEqual(form.errors['name'], ['Item Name is required.'])

    def test_form_save_new_item_sets_order(self):
        """Test that saving a new item via form correctly sets order_display."""
        DashboardItem.objects.create(name='Existing Item 1', order_display=1)
        DashboardItem.objects.create(name='Existing Item 2', order_display=2)

        form = DashboardItemForm(data={
            'name': 'New Item for Order Test',
            'value': 'ABC',
            'description': 'Description for new item.',
            'order_display': 0 # This should be overridden by model's set_next_order
        })
        self.assertTrue(form.is_valid())
        item = form.save()
        self.assertEqual(item.order_display, 3) # Should be max_order_before + 1

class DashboardItemViewsTest(TestCase):
    """
    Integration tests for DashboardItem views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all view tests
        cls.item = DashboardItem.objects.create(
            name='Test Item',
            value='100',
            description='A description.',
            order_display=1
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test the DashboardItem list view GET request."""
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_module/dashboarditem/list.html')
        # The actual items are loaded via HTMX, so we check the container
        self.assertContains(response, 'id="dashboardItemTable-container"')
        self.assertContains(response, 'Add New Item')

    def test_table_partial_view_get(self):
        """Test the HTMX partial for the table content."""
        response = self.client.get(reverse('dashboarditem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_module/dashboarditem/_dashboarditem_table.html')
        self.assertContains(response, self.item.name)
        self.assertContains(response, self.item.value)
        self.assertContains(response, 'id="dashboardItemTable"') # Check if DataTables container is there
        self.assertContains(response, 'Edit')
        self.assertContains(response, 'Delete')

    def test_create_view_get(self):
        """Test GET request for DashboardItem create form (HTMX modal)."""
        response = self.client.get(reverse('dashboarditem_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_module/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'Add New Dashboard Item')
        self.assertContains(response, '<form hx-post')

    def test_create_view_post_success(self):
        """Test POST request for DashboardItem create with valid data (HTMX)."""
        initial_count = DashboardItem.objects.count()
        data = {
            'name': 'New Dashboard Item',
            'value': '200',
            'description': 'Newly created item.',
            'order_display': 0 # Should be set by model logic
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code for no content swap
        self.assertTrue(DashboardItem.objects.filter(name='New Dashboard Item').exists())
        self.assertEqual(DashboardItem.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """Test POST request for DashboardItem create with invalid data (HTMX)."""
        initial_count = DashboardItem.objects.count()
        data = {
            'name': '', # Invalid: Missing name
            'value': 'Invalid Value',
        }
        response = self.client.post(reverse('dashboarditem_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX renders form again on validation error
        self.assertTemplateUsed(response, 'dashboard_module/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'Item Name is required.')
        self.assertEqual(DashboardItem.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test GET request for DashboardItem update form (HTMX modal)."""
        response = self.client.get(reverse('dashboarditem_edit', args=[self.item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_module/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'Edit Dashboard Item')
        self.assertContains(response, self.item.name) # Pre-filled data

    def test_update_view_post_success(self):
        """Test POST request for DashboardItem update with valid data (HTMX)."""
        updated_name = 'Updated Test Item'
        data = {
            'name': updated_name,
            'value': '200',
            'description': 'An updated description.',
            'order_display': self.item.order_display # Keep original order or set to new value
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        """Test POST request for DashboardItem update with invalid data (HTMX)."""
        original_name = self.item.name
        data = {
            'name': '', # Invalid: Missing name
            'value': 'Invalid Value',
            'order_display': self.item.order_display
        }
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_module/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'Item Name is required.')
        self.item.refresh_from_db()
        self.assertEqual(self.item.name, original_name) # Ensure name was not changed

    def test_delete_view_get(self):
        """Test GET request for DashboardItem delete confirmation (HTMX modal)."""
        item_to_delete = DashboardItem.objects.create(name='Item to Delete', value='XYZ')
        response = self.client.get(reverse('dashboarditem_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_module/dashboarditem/_dashboarditem_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, item_to_delete.name)

    def test_delete_view_post_success(self):
        """Test POST request for DashboardItem delete (HTMX)."""
        item_to_delete = DashboardItem.objects.create(name='Another Item to Delete', value='PQR')
        initial_count = DashboardItem.objects.count()
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(DashboardItem.objects.count(), initial_count - 1)
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardItemList', response.headers['HX-Trigger'])
```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated Django code fully embraces HTMX and Alpine.js for a dynamic, modern user experience without relying on traditional JavaScript frameworks.

*   **HTMX for Dynamic Updates:**
    *   The main `list.html` template uses `hx-get` to load the table content from `{% url 'dashboarditem_table' %}` on `load` and `refreshDashboardItemList` events. This means the list updates without a full page reload when a CRUD operation completes.
    *   Buttons for "Add New Item", "Edit", and "Delete" use `hx-get` to fetch the respective forms or confirmation dialogs.
    *   `hx-target="#modalContent"` ensures these partials are loaded directly into the modal's content area.
    *   `hx-trigger="click"` makes these actions fire on button click.
    *   Form submissions (`_dashboarditem_form.html`, `_dashboarditem_confirm_delete.html`) use `hx-post` to submit data asynchronously.
    *   Views return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshDashboardItemList'})` on successful form submission or deletion. This HTTP 204 status tells HTMX to do nothing with the response body (it's a modal, so it just closes), and the `HX-Trigger` header broadcasts an event to refresh the main list table.

*   **Alpine.js for UI State Management:**
    *   Alpine.js is used with the `_=` (hyperscript) attribute for simple DOM manipulation directly on the modal elements.
    *   `_="on click add .flex to #modal then add .opacity-100 to #modal backdrop then add .scale-100 to #modalContent"`: When a button triggers an HTMX request for the modal, this line adds Tailwind CSS classes to show the modal (making it visible, opaque, and scaling its content for a smooth transition).
    *   `_="on click if event.target.id == 'modal' remove .flex from me then remove .opacity-100 from #modal backdrop then remove .scale-100 from #modalContent"`: This handles closing the modal when clicking outside its content area, by removing the visibility and transition classes.

*   **DataTables for List Views:**
    *   The `_dashboarditem_table.html` partial contains the `<table>` element with `id="dashboardItemTable"`.
    *   After the HTMX swap (`htmx:afterSwap` event listener in `list.html`), a JavaScript snippet initializes DataTables on this table ID.
    *   This ensures client-side searching, sorting, and pagination are handled efficiently by DataTables.

*   **No Additional JavaScript:**
    *   Beyond the DataTables library and the minimal Alpine.js for modal animation/state, no custom JavaScript files or complex frontend frameworks are required. All dynamic interactions are managed declaratively with HTMX and Alpine.js.

---

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values from the original request have been replaced with concrete names relevant to the `DashboardItem` example.
*   **DRY Templates:** Template inheritance (`{% extends 'core/base.html' %}`) is used. Partial templates (`_dashboarditem_table.html`, `_dashboarditem_form.html`, `_dashboarditem_confirm_delete.html`) are used for reusable components loaded via HTMX, avoiding repetition.
*   **Fat Model, Thin View:**
    *   `DashboardItem` model includes business logic like `get_display_summary` and `set_next_order`.
    *   `DashboardItemForm` handles specific form validation (`clean_name`) and orchestrates the model's business logic (`save` override).
    *   Views (e.g., `DashboardItemCreateView`, `DashboardItemUpdateView`) are kept concise, typically 5-15 lines per method, primarily dispatching requests and handling HTMX triggers. They do not contain direct database calls or complex logic.
*   **Comprehensive Tests:** Unit tests for model methods and properties are included. Integration tests cover all CRUD views and explicitly test HTMX request/response headers, ensuring the dynamic interactions work as expected. The goal is to achieve at least 80% test coverage for this module.
*   **Automated Conversion:** This plan demonstrates how an AI-assisted automation tool would infer a plausible Django structure given an empty ASP.NET placeholder. In a real scenario, the tool would extract actual database schemas, UI controls (like `GridView` columns), and C# business logic from the ASP.NET code to populate these templates and code blocks automatically, drastically reducing manual effort and potential errors. The conversational AI aspect would then guide the user through validating these inferred structures and deploying them.