## ASP.NET to Django Conversion Script:

This document outlines a strategic plan to modernize your legacy ASP.NET application to a robust, scalable Django 5.0+ solution. Our approach prioritizes AI-assisted automation, leveraging Django's "Fat Model, Thin View" philosophy, HTMX, Alpine.js, and DataTables for a seamless user experience without extensive custom JavaScript.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`Module_Accounts_Reports_Search_Details`).
- Always include complete unit tests for models/services and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database views using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database views and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code utilizes database views: `View_PVEVNo_Item`, `View_PVEVNo_Item_Pending`, and `View_PVEVNo_Item_GSN`. The `sp_columns` stored procedure is used to dynamically fetch column names, and the `myhash` function reveals the actual SQL expressions and corresponding column names, including complex date string manipulations.

**Inferred Database Views and Key Columns:**

*   **Primary Views:**
    *   `View_PVEVNo_Item` (used when `flag2 == 1`)
    *   `View_PVEVNo_Item_GSN` (used when `flag2 == 0`)
    *   `View_PVEVNo_Item_Pending` is a filtered version of `View_PVEVNo_Item` (where `PVEVNo is null`), handled by `flag == 0`.
    *   The `View_PVEVNo_Item_GSN` also has `PVEVNo is null` filtering when `flag == 0`.

*   **Common Columns (from `myhash` function):**
    `SrNo`, `ItemCode`, `Description`, `UOM`, `StockQty`, `PONo`, `PODate`, `WONO`, `Qty`, `Rate`, `Discount`, `SupplierName`, `AcceptedQty`, `PVEVNo`, `PVEVDate`, `BillNo`, `BillDate`, `CENVATEntryNo`, `CENVATEntryDate`, `OtherCharges`, `OtherChaDesc`, `Narration`, `DebitAmt`, `DiscountType`, `PVEVDiscount`, `EmpName`, `Authorize`, `AuthorizeDate`, `AuthorizeBy`, `PFAmt`, `ExStBasicInPer`, `ExStBasic`, `ExStEducessInPer`, `ExStEducess`, `ExStShecessInPer`, `ExStShecess`, `CustomDuty`, `VAT`, `CST`, `Freight`, `TarrifNo`, `ACHead`, `ChallanNo`, `CompId`, `Code`.

*   **Conditional Columns:**
    *   `GQNNo`, `GQNDate` (for `View_PVEVNo_Item`)
    *   `GSNNo`, `GSNDate` (for `View_PVEVNo_Item_GSN`)

**Note:** The ASP.NET code heavily relies on string manipulation for date fields. For a robust Django solution, it's highly recommended to modify the underlying SQL views to return proper `DATE` or `DATETIME` types. For this conversion, we will initially define them as `CharField` in Django models to match the string nature, and handle Python-side formatting.

### Step 2: Identify Backend Functionality

**Task:** Determine the data retrieval, filtering, and export operations.

**Instructions:**
The ASP.NET code primarily focuses on data *reading* and *exporting*, not full CRUD operations.

*   **Read (Display Data):**
    *   The `GetData()` method is the core logic for fetching data.
    *   It dynamically constructs SQL `SELECT` statements based on:
        *   Query string parameters (`type`, `No`, `FDate`, `TDate`, `SupId`, `Code`, `WONo`, `accval`).
        *   `CompId` from session.
        *   Selected columns from `chkFields`.
        *   Two main report "modes" (`flag2`): `View_PVEVNo_Item` or `View_PVEVNo_Item_GSN`.
        *   "Pending" vs. "Completed" status (`flag`): Filtering on `PVEVNo` being null or not.
    *   The `myhash` function performs complex SQL string manipulations for selected columns, including `ROW_NUMBER()` for `SrNo` and various date formatting conversions.
*   **Export (Excel):**
    *   The `btnExport_Click()` method retrieves the currently displayed `DataTable` (`ViewState["dtList"]`).
    *   It renames columns for display in Excel and uses an external utility (`ExportToExcel.ExportDataToExcel`) to generate the file.
*   **Dynamic Column Selection:**
    *   The `BindTableColumns()` method populates `chkFields` with available column names from `sp_columns` and assigns user-friendly `HeaderText`.
    *   The `ShowGrid()` method uses these selected columns to dynamically build the `GridView` columns and fetch data.
*   **Cancel Action:** The `btnCancel_Click()` performs a simple redirect.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for Django template mapping.

**Instructions:**

*   **Data Display:**
    *   `GridView1`: This will be replaced by a DataTables-enabled HTML `<table>`.
*   **User Input/Control:**
    *   `CheckBox checkAll`: To select/deselect all columns. This will be implemented with Alpine.js.
    *   `CheckBoxList chkFields`: Used for dynamic column selection. This will become an HTML checkbox group, dynamically rendered from Python, with Alpine.js for "check all" functionality.
    *   `Button btnSub` ("Show"): Triggers data display. This will be an HTMX `hx-post` or `hx-get` button to refresh the DataTables partial.
    *   `Button btnExport` ("Export To Excel"): Triggers file download. This will be a standard `<a>` tag or button with `hx-get` to a Django view returning an Excel file.
    *   `Button btnCancel` ("Cancel"): Redirects to `Search.aspx`. This will be a standard `<a>` tag linking to a Django URL.
    *   Query String Parameters (`FDate`, `TDate`, `SupId`, `Code`, `WONo`, `accval`, `type`, `No`, `RAd`, `RAd2`): These will become `GET` parameters in the Django URL and handled by a `ReportFilterForm` in the view.

*   **Styling & Interactions:**
    *   `yui-datatable.css`, `StyleSheet.css`: Will be replaced by Tailwind CSS and potentially DataTables' own CSS.
    *   The complex JavaScript for `GridView1` manipulation: Fully replaced by DataTables' built-in features and HTMX.

### Step 4: Generate Django Code

The Django application will be named `reports`.

#### 4.1 Models (`reports/models.py`)

We'll define Django models that map directly to the existing database views using `managed = False`. This ensures Django interacts with the views without attempting to manage their schema.

```python
from django.db import models

# Define a base class for the PVEV views as they share many fields
class PVEVBaseView(models.Model):
    # These fields correspond to columns derived from the original C# myhash function.
    # We use CharField as the original code indicates string manipulation for dates,
    # or for columns that might not have a strong type in the view.
    # For a true modernization, these would ideally be DateField/DecimalField/etc.
    # if the underlying SQL views were updated to return correct types.
    SrNo = models.IntegerField(db_column='SrNo', blank=True, null=True) # Derived by ROW_NUMBER() in SQL
    ItemCode = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    Description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)
    UOM = models.CharField(db_column='UOM', max_length=50, blank=True, null=True)
    StockQty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=4, blank=True, null=True)
    PONo = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    PODate = models.CharField(db_column='PODate', max_length=50, blank=True, null=True) # Original is string
    WONO = models.CharField(db_column='WONO', max_length=255, blank=True, null=True)
    Qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=4, blank=True, null=True)
    Rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4, blank=True, null=True)
    Discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4, blank=True, null=True)
    SupplierName = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    AcceptedQty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=4, blank=True, null=True)
    PVEVNo = models.CharField(db_column='PVEVNo', max_length=255, blank=True, null=True)
    PVEVDate = models.CharField(db_column='PVEVDate', max_length=50, blank=True, null=True) # Original is string
    BillNo = models.CharField(db_column='BillNo', max_length=255, blank=True, null=True)
    BillDate = models.CharField(db_column='BillDate', max_length=50, blank=True, null=True) # Original is string
    CENVATEntryNo = models.CharField(db_column='CENVATEntryNo', max_length=255, blank=True, null=True)
    CENVATEntryDate = models.CharField(db_column='CENVATEntryDate', max_length=50, blank=True, null=True) # Original is string
    OtherCharges = models.DecimalField(db_column='OtherCharges', max_digits=18, decimal_places=4, blank=True, null=True)
    OtherChaDesc = models.CharField(db_column='OtherChaDesc', max_length=255, blank=True, null=True)
    Narration = models.CharField(db_column='Narration', max_length=255, blank=True, null=True)
    DebitAmt = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=4, blank=True, null=True)
    DiscountType = models.CharField(db_column='DiscountType', max_length=50, blank=True, null=True)
    PVEVDiscount = models.DecimalField(db_column='PVEVDiscount', max_digits=18, decimal_places=4, blank=True, null=True)
    EmpName = models.CharField(db_column='EmpName', max_length=255, blank=True, null=True)
    Authorize = models.BooleanField(db_column='Authorize', blank=True, null=True)
    AuthorizeDate = models.CharField(db_column='AuthorizeDate', max_length=50, blank=True, null=True) # Original is string
    AuthorizeBy = models.CharField(db_column='AuthorizeBy', max_length=255, blank=True, null=True)
    PFAmt = models.DecimalField(db_column='PFAmt', max_digits=18, decimal_places=4, blank=True, null=True)
    ExStBasicInPer = models.DecimalField(db_column='ExStBasicInPer', max_digits=18, decimal_places=4, blank=True, null=True)
    ExStBasic = models.DecimalField(db_column='ExStBasic', max_digits=18, decimal_places=4, blank=True, null=True)
    ExStEducessInPer = models.DecimalField(db_column='ExStEducessInPer', max_digits=18, decimal_places=4, blank=True, null=True)
    ExStEducess = models.DecimalField(db_column='ExStEducess', max_digits=18, decimal_places=4, blank=True, null=True)
    ExStShecessInPer = models.DecimalField(db_column='ExStShecessInPer', max_digits=18, decimal_places=4, blank=True, null=True)
    ExStShecess = models.DecimalField(db_column='ExStShecess', max_digits=18, decimal_places=4, blank=True, null=True)
    CustomDuty = models.DecimalField(db_column='CustomDuty', max_digits=18, decimal_places=4, blank=True, null=True)
    VAT = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=4, blank=True, null=True)
    CST = models.DecimalField(db_column='CST', max_digits=18, decimal_places=4, blank=True, null=True)
    Freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=4, blank=True, null=True)
    TarrifNo = models.CharField(db_column='TarrifNo', max_length=255, blank=True, null=True)
    ACHead = models.CharField(db_column='ACHead', max_length=255, blank=True, null=True)
    ChallanNo = models.CharField(db_column='ChallanNo', max_length=255, blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId', blank=True, null=True)
    Code = models.CharField(db_column='Code', max_length=255, blank=True, null=True) # Original refers to Supplier/Item Code

    class Meta:
        abstract = True # This is a base class, not a table itself

class PVEVItemView(PVEVBaseView):
    # Specific fields for View_PVEVNo_Item
    GQNNo = models.CharField(db_column='GQNNo', max_length=255, blank=True, null=True)
    GQNDate = models.CharField(db_column='GQNDate', max_length=50, blank=True, null=True) # Original is string

    class Meta:
        managed = False
        db_table = 'View_PVEVNo_Item' # Map to the specific database view
        verbose_name = 'PVEV Item Report'
        verbose_name_plural = 'PVEV Item Reports'

    def __str__(self):
        return f"PVEV Item: {self.ItemCode or 'N/A'} - {self.PVEVNo or 'N/A'}"

class PVEVItemGSNView(PVEVBaseView):
    # Specific fields for View_PVEVNo_Item_GSN
    GSNNo = models.CharField(db_column='GSNNo', max_length=255, blank=True, null=True)
    GSNDate = models.CharField(db_column='GSNDate', max_length=50, blank=True, null=True) # Original is string

    class Meta:
        managed = False
        db_table = 'View_PVEVNo_Item_GSN' # Map to the specific database view
        verbose_name = 'PVEV GSN Report'
        verbose_name_plural = 'PVEV GSN Reports'

    def __str__(self):
        return f"PVEV GSN: {self.ItemCode or 'N/A'} - {self.GSNNo or 'N/A'}"

```

#### 4.2 Forms (`reports/forms.py`)

We'll create forms for filtering and column selection. No `ModelForm` is needed as we're dealing with reports, not direct model creation/editing.

```python
from django import forms
from datetime import datetime

# Constants for column mapping and report types, derived from original ASP.NET logic
REPORT_TYPE_PVEV = 1 # Corresponds to flag2 = 1, using View_PVEVNo_Item
REPORT_TYPE_PVEV_GSN = 0 # Corresponds to flag2 = 0, using View_PVEVNo_Item_GSN

COLUMN_MAP = {
    'SrNo': {'display': 'Sr No', 'db_field': 'SrNo', 'is_default': True},
    'ItemCode': {'display': 'Item Code', 'db_field': 'ItemCode', 'is_default': True},
    'Description': {'display': 'Description', 'db_field': 'Description', 'is_default': True},
    'UOM': {'display': 'UOM', 'db_field': 'UOM', 'is_default': True},
    'StockQty': {'display': 'Stock Qty', 'db_field': 'StockQty', 'is_default': True},
    'PONo': {'display': 'PO No', 'db_field': 'PONo', 'is_default': False},
    'PODate': {'display': 'PO Date', 'db_field': 'PODate', 'is_default': False},
    'WONO': {'display': 'WO No', 'db_field': 'WONO', 'is_default': False},
    'Qty': {'display': 'Qty', 'db_field': 'Qty', 'is_default': False},
    'Rate': {'display': 'Rate', 'db_field': 'Rate', 'is_default': False},
    'Discount': {'display': 'Discount', 'db_field': 'Discount', 'is_default': False},
    'SupplierName': {'display': 'Supplier Name', 'db_field': 'SupplierName', 'is_default': False},
    'GQNNo': {'display': 'GQN No', 'db_field': 'GQNNo', 'is_default': False, 'report_type': REPORT_TYPE_PVEV},
    'GQNDate': {'display': 'GQN Date', 'db_field': 'GQNDate', 'is_default': False, 'report_type': REPORT_TYPE_PVEV},
    'GSNNo': {'display': 'GSN No', 'db_field': 'GSNNo', 'is_default': False, 'report_type': REPORT_TYPE_PVEV_GSN},
    'GSNDate': {'display': 'GSN Date', 'db_field': 'GSNDate', 'is_default': False, 'report_type': REPORT_TYPE_PVEV_GSN},
    'AcceptedQty': {'display': 'Accepted Qty', 'db_field': 'AcceptedQty', 'is_default': False},
    'PVEVNo': {'display': 'PVEV No', 'db_field': 'PVEVNo', 'is_default': False, 'hide_flag_0': True}, # Hidden if flag=0 (pending)
    'PVEVDate': {'display': 'PVEV Date', 'db_field': 'PVEVDate', 'is_default': False, 'hide_flag_0': True},
    'BillNo': {'display': 'Bill No', 'db_field': 'BillNo', 'is_default': False, 'hide_flag_0': True},
    'BillDate': {'display': 'Bill Date', 'db_field': 'BillDate', 'is_default': False, 'hide_flag_0': True},
    'CENVATEntryNo': {'display': 'CEN/VAT Entry No', 'db_field': 'CENVATEntryNo', 'is_default': False, 'hide_flag_0': True},
    'CENVATEntryDate': {'display': 'CEN/VAT Entry Date', 'db_field': 'CENVATEntryDate', 'is_default': False, 'hide_flag_0': True},
    'OtherCharges': {'display': 'Other Charges', 'db_field': 'OtherCharges', 'is_default': False, 'hide_flag_0': True},
    'OtherChaDesc': {'display': 'Other Charges Desc.', 'db_field': 'OtherChaDesc', 'is_default': False, 'hide_flag_0': True},
    'Narration': {'display': 'Narration', 'db_field': 'Narration', 'is_default': False, 'hide_flag_0': True},
    'DebitAmt': {'display': 'Debit Amount', 'db_field': 'DebitAmt', 'is_default': False, 'hide_flag_0': True},
    'DiscountType': {'display': 'Discount Type', 'db_field': 'DiscountType', 'is_default': False, 'hide_flag_0': True},
    'PVEVDiscount': {'display': 'PVEV Discount', 'db_field': 'PVEVDiscount', 'is_default': False, 'hide_flag_0': True},
    'EmpName': {'display': 'Gen. By', 'db_field': 'EmpName', 'is_default': False, 'hide_flag_0': True},
    'Authorize': {'display': 'Authorize', 'db_field': 'Authorize', 'is_default': False, 'hide_flag_0': True},
    'AuthorizeDate': {'display': 'Authorized Date', 'db_field': 'AuthorizeDate', 'is_default': False, 'hide_flag_0': True},
    'AuthorizeBy': {'display': 'Authorized By', 'db_field': 'AuthorizeBy', 'is_default': False, 'hide_flag_0': True},
    'PFAmt': {'display': 'PF Amount', 'db_field': 'PFAmt', 'is_default': False, 'hide_flag_0': True},
    'ExStBasicInPer': {'display': 'ExSt Basic In %', 'db_field': 'ExStBasicInPer', 'is_default': False, 'hide_flag_0': True},
    'ExStBasic': {'display': 'ExSt Basic', 'db_field': 'ExStBasic', 'is_default': False, 'hide_flag_0': True},
    'ExStEducessInPer': {'display': 'ExSt Educess In %', 'db_field': 'ExStEducessInPer', 'is_default': False, 'hide_flag_0': True},
    'ExStEducess': {'display': 'ExSt Educess', 'db_field': 'ExStEducess', 'is_default': False, 'hide_flag_0': True},
    'ExStShecessInPer': {'display': 'ExSt Shecess In %', 'db_field': 'ExStShecessInPer', 'is_default': False, 'hide_flag_0': True},
    'ExStShecess': {'display': 'ExSt Shecess', 'db_field': 'ExStShecess', 'is_default': False, 'hide_flag_0': True},
    'CustomDuty': {'display': 'Custom Duty', 'db_field': 'CustomDuty', 'is_default': False, 'hide_flag_0': True},
    'VAT': {'display': 'VAT', 'db_field': 'VAT', 'is_default': False},
    'CST': {'display': 'CST', 'db_field': 'CST', 'is_default': False},
    'Freight': {'display': 'Freight', 'db_field': 'Freight', 'is_default': False},
    'TarrifNo': {'display': 'Tarrif No', 'db_field': 'TarrifNo', 'is_default': False, 'hide_flag_0': True},
    'ACHead': {'display': 'Ac Head', 'db_field': 'ACHead', 'is_default': False},
    'ChallanNo': {'display': 'Challan No', 'db_field': 'ChallanNo', 'is_default': False},
    'CompId': {'display': 'Company ID', 'db_field': 'CompId', 'is_default': False, 'exclude_selection': True},
    'Code': {'display': 'Code', 'db_field': 'Code', 'is_default': False, 'exclude_selection': True},
}

class ReportFilterForm(forms.Form):
    # Query parameters from original ASP.NET
    report_type = forms.ChoiceField(
        choices=[(REPORT_TYPE_PVEV_GSN, 'GSN Report'), (REPORT_TYPE_PVEV, 'PVEV Report')],
        label="Report Type",
        initial=REPORT_TYPE_PVEV_GSN, # flag2 default is 0
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    report_status = forms.ChoiceField(
        choices=[(0, 'Pending'), (1, 'Completed')], # flag: 0 for pending, 1 for completed
        label="Report Status",
        initial=0, # flag default is 0
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    document_type = forms.IntegerField(
        label="Document Type",
        required=False,
        widget=forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    document_no = forms.CharField(
        max_length=255,
        label="Document No.",
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    from_date = forms.CharField( # Using CharField for flexibility with original string format, will parse in service
        label="From Date (DD-MM-YYYY)",
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'})
    )
    to_date = forms.CharField( # Using CharField for flexibility with original string format, will parse in service
        label="To Date (DD-MM-YYYY)",
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'DD-MM-YYYY'})
    )
    supplier_id = forms.CharField(
        max_length=255,
        label="Supplier ID",
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    item_code = forms.CharField(
        max_length=255,
        label="Item Code",
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    wo_no = forms.CharField(
        max_length=255,
        label="WO No.",
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    account_value = forms.IntegerField(
        label="Account Head Value",
        required=False,
        widget=forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def clean_from_date(self):
        data = self.cleaned_data['from_date']
        if data:
            try:
                # Assuming DD-MM-YYYY format based on C#
                return datetime.strptime(data, '%d-%m-%Y').date()
            except ValueError:
                raise forms.ValidationError("Invalid date format. Please use DD-MM-YYYY.")
        return data

    def clean_to_date(self):
        data = self.cleaned_data['to_date']
        if data:
            try:
                # Assuming DD-MM-YYYY format based on C#
                return datetime.strptime(data, '%d-%m-%Y').date()
            except ValueError:
                raise forms.ValidationError("Invalid date format. Please use DD-MM-YYYY.")
        return data


class ColumnSelectionForm(forms.Form):
    # Dynamically populate choices based on COLUMN_MAP, excluding 'CompId' and 'Code'
    # and respecting 'report_type' and 'report_status' for visibility.
    columns = forms.MultipleChoiceField(
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-10 gap-x-4'}),
        choices=[] # Populated in __init__
    )

    def __init__(self, *args, **kwargs):
        report_type = kwargs.pop('report_type', REPORT_TYPE_PVEV_GSN)
        report_status = kwargs.pop('report_status', 0) # 0 for pending (flag=0)
        super().__init__(*args, **kwargs)

        # Filter columns based on report_type and report_status
        available_columns = []
        for key, value in COLUMN_MAP.items():
            if value.get('exclude_selection'): # Skip CompId, Code
                continue
            
            # Filter by report_type
            if 'report_type' in value and value['report_type'] != report_type:
                continue

            # Filter by report_status (flag == 0 hides PVEVNo, PVEVDate, etc.)
            if report_status == 0 and value.get('hide_flag_0'):
                continue
            
            available_columns.append((key, value['display']))
        
        self.fields['columns'].choices = available_columns
        
        # Set initial selected columns based on 'is_default'
        initial_selected_columns = [
            key for key, value in COLUMN_MAP.items()
            if value.get('is_default') and not value.get('exclude_selection') and
               ('report_type' not in value or value['report_type'] == report_type) and
               (not value.get('hide_flag_0') or report_status != 0)
        ]
        self.fields['columns'].initial = initial_selected_columns

```

#### 4.3 Services (`reports/services.py`)

This class encapsulates the complex data retrieval and filtering logic, keeping views thin.

```python
import datetime
import openpyxl
from django.db.models import Q, F, Window
from django.db.models.functions import RowNumber
from django.db import connection # For raw SQL if needed
from django.http import HttpResponse

# Import models and constants from forms to avoid redundancy
from .models import PVEVItemView, PVEVItemGSNView
from .forms import COLUMN_MAP, REPORT_TYPE_PVEV, REPORT_TYPE_PVEV_GSN

class ReportService:
    def __init__(self, request):
        self.request = request
        self.company_id = request.user.company_id # Assuming CompId from request.user
        self.filters = self._parse_filters(request.GET)
        self.report_type = int(self.filters.get('report_type', REPORT_TYPE_PVEV_GSN))
        self.report_status = int(self.filters.get('report_status', 0)) # 0 for pending, 1 for completed

    def _parse_filters(self, query_params):
        # Maps query string parameters to internal filter keys
        # The ReportFilterForm handles initial parsing and validation.
        parsed_data = {}
        # Manually parse date strings to date objects if needed, for direct DB querying
        # For dates, the form returns date objects if valid, or None/string if invalid.
        # We need to ensure they are in a format suitable for DB queries if applied directly.
        
        filter_form = self.get_filter_form()
        if filter_form.is_valid():
            parsed_data = filter_form.cleaned_data
            
            # Apply common filters
            conditions = Q(CompId=self.company_id)

            if parsed_data.get('document_type') and parsed_data.get('document_no'):
                doc_type = parsed_data['document_type']
                doc_no = parsed_data['document_no']
                # This maps the original ASP.NET switch logic
                if self.report_type == REPORT_TYPE_PVEV: # RAd2=1
                    if doc_type == 1: conditions &= Q(GQNNo=doc_no)
                    elif doc_type == 3: conditions &= Q(PONo=doc_no)
                    elif doc_type == 4: conditions &= Q(PVEVNo=doc_no)
                else: # RAd2=0
                    if doc_type == 2: conditions &= Q(GSNNo=doc_no)
                    elif doc_type == 3: conditions &= Q(PONo=doc_no)
                    elif doc_type == 4: conditions &= Q(PVEVNo=doc_no)

            if parsed_data.get('from_date') and parsed_data.get('to_date'):
                # Assuming PVEVDate is the primary date for filtering
                # This might need adjustment based on specific report type and original query.
                # If DB views return DateField, use __range. If CharField, might need RawSQL.
                # For this example, assuming the view can be filtered on `PVEVDate` as string comparison.
                from_date_str = parsed_data['from_date'].strftime('%d-%m-%Y') if parsed_data['from_date'] else None
                to_date_str = parsed_data['to_date'].strftime('%d-%m-%Y') if parsed_data['to_date'] else None

                if from_date_str and to_date_str:
                    # This is a simplification; for string dates, direct comparison might not be robust.
                    # A better approach involves modifying the DB view to return actual date types.
                    conditions &= Q(PVEVDate__gte=from_date_str, PVEVDate__lte=to_date_str)
            
            if parsed_data.get('supplier_id'):
                conditions &= Q(Code=parsed_data['supplier_id']) # Code is supplier_id from query string
            if parsed_data.get('item_code'):
                conditions &= Q(ItemCode=parsed_data['item_code'])
            if parsed_data.get('wo_no'):
                conditions &= Q(WONO=parsed_data['wo_no'])
            if parsed_data.get('account_value'):
                # Original C# fetches Symbol from AccHead table based on accval
                # This needs to be implemented. For simplicity, assume it's direct mapping
                # or a pre-fetched symbol.
                # Let's assume we have a way to map accval to AHSymb (e.g., from a master data model).
                # Example: AccHead.objects.get(id=parsed_data['account_value']).symbol
                # For now, let's skip the explicit DB lookup here, assume it's already mapped
                # or ACHead in the view can be filtered directly (less likely).
                # A proper model for AccHead would be needed.
                # conditions &= Q(ACHead=ACC_HEAD_SYMBOLS.get(parsed_data['account_value']))
                pass # Skipping AccHead filtering for now to keep focus on report
        else:
            # Handle form validation errors, e.g., for initial page load or debugging
            print("Filter form errors:", filter_form.errors)

        parsed_data['conditions'] = conditions
        return parsed_data

    def get_filter_form(self):
        # Create and return the filter form with current request data
        return ReportFilterForm(self.request.GET)

    def get_column_selection_form(self):
        # Create and return the column selection form
        return ColumnSelectionForm(self.request.GET, report_type=self.report_type, report_status=self.report_status)

    def get_selected_columns(self):
        # Get selected columns from the form, defaulting to initial if not submitted
        column_form = self.get_column_selection_form()
        if column_form.is_valid():
            return column_form.cleaned_data['columns']
        # If form is not valid (e.g., initial load without columns param), use defaults
        return column_form.fields['columns'].initial

    def get_report_data(self):
        selected_columns = self.get_selected_columns()
        
        # Determine which base model to use
        if self.report_type == REPORT_TYPE_PVEV:
            base_model = PVEVItemView
            # GQN related columns
            column_name_map = {k: v['db_field'] for k, v in COLUMN_MAP.items() if 'db_field' in v and ('report_type' not in v or v['report_type'] == REPORT_TYPE_PVEV)}
        else: # REPORT_TYPE_PVEV_GSN
            base_model = PVEVItemGSNView
            # GSN related columns
            column_name_map = {k: v['db_field'] for k, v in COLUMN_MAP.items() if 'db_field' in v and ('report_type' not in v or v['report_type'] == REPORT_TYPE_PVEV_GSN)}

        # Apply report status filter (pending vs. completed)
        conditions = self.filters['conditions']
        if self.report_status == 0: # Pending (flag = 0)
            conditions &= Q(PVEVNo__isnull=True)
            if self.report_type == REPORT_TYPE_PVEV_GSN:
                # Specific group by for GSN pending report (from original C#)
                # This complex group by might require raw SQL or careful ORM annotations.
                # Simplification: For ORM, we'll aim for distinct on relevant fields if possible.
                pass # No additional ORM filter for this, it's about GROUP BY.
        else: # Completed (flag = 1)
            conditions &= Q(PVEVNo__isnull=False)
            
        # Build list of fields to select from the DB view
        # Ensure that 'SrNo' is dynamically added by ROW_NUMBER in the DB if not present
        # Or handled here with ORM Window function.
        # For managed=False views, the 'SrNo' field in model should exist in the view.
        db_fields_to_select = [column_name_map[col] for col in selected_columns if col in column_name_map]
        
        # Ensure PK is always selected for ORM operations, even if not displayed
        pk_field = base_model._meta.pk.name
        if pk_field not in db_fields_to_select:
             db_fields_to_select.append(pk_field)


        # Execute query
        try:
            # Using .values() to select only chosen fields directly from the database view
            # This is more efficient for reports with many columns.
            queryset = base_model.objects.filter(conditions).values(*db_fields_to_select)

            # Apply RowNumber if 'SrNo' is requested and not natively handled by the view for all rows.
            # If the DB view already computes SrNo correctly, this is not needed.
            # Assuming the view handles SrNo as per original SQL. If not, this is how you'd add it:
            # queryset = queryset.annotate(SrNo=Window(expression=RowNumber(), order_by=F('ItemCode').asc()))

            data = []
            for i, row in enumerate(queryset):
                # Format data for display, e.g., dates
                formatted_row = {}
                for col_key in selected_columns:
                    db_field_name = COLUMN_MAP[col_key]['db_field']
                    value = row.get(db_field_name) # Get value using DB field name
                    
                    # Apply specific formatting (e.g., date formats from myhash)
                    if col_key in ['PODate', 'GQNDate', 'GSNDate', 'PVEVDate', 'BillDate', 'CENVATEntryDate', 'AuthorizeDate']:
                        try:
                            # Attempt to parse as DD-MM-YYYY or similar string format for display
                            if value:
                                # This is a guess based on the original C# date parsing.
                                # A more robust solution involves proper DateField in DB view.
                                dt_obj = datetime.datetime.strptime(value, '%d-%m-%Y')
                                formatted_row[col_key] = dt_obj.strftime('%d/%m/%Y') # Common display format
                            else:
                                formatted_row[col_key] = ''
                        except (ValueError, TypeError):
                            formatted_row[col_key] = value # Fallback to raw value
                    elif col_key == 'Authorize':
                        formatted_row[col_key] = 'Yes' if value else ''
                    else:
                        formatted_row[col_key] = value
                data.append(formatted_row)
            return data

        except Exception as e:
            # Log the error, provide user-friendly message
            print(f"Error fetching report data: {e}")
            messages.error(self.request, f"Failed to retrieve report data: {e}")
            return []

    def get_report_headers(self):
        selected_columns = self.get_selected_columns()
        headers = [COLUMN_MAP[col]['display'] for col in selected_columns]
        return headers

    def export_to_excel(self):
        headers = self.get_report_headers()
        data = self.get_report_data()

        # Create a new workbook and select the active sheet
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "Report Data"

        # Write headers
        ws.append(headers)
        for cell in ws[1]:
            cell.font = Font(bold=True)
            cell.alignment = Alignment(horizontal='center')

        # Write data rows
        for row_data in data:
            # Ensure the order of columns matches the headers
            ordered_row = [row_data.get(col_key, '') for col_key in self.get_selected_columns()]
            ws.append(ordered_row)

        # Set content type and attachment header for download
        filename = "PVEV_Report.xlsx"
        if self.report_status == 1:
            filename = "PVEV_Completed.xlsx"
        else:
            filename = "PVEV_Pending.xlsx"

        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename={filename}'
        wb.save(response)
        return response

```

#### 4.4 Views (`reports/views.py`)

Thin views that delegate heavy lifting to the `ReportService`.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.shortcuts import render
from django.contrib import messages
from django.http import HttpResponse

from .forms import ReportFilterForm, ColumnSelectionForm
from .services import ReportService

class ReportListView(TemplateView):
    template_name = 'reports/search_details/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        service = ReportService(self.request)
        
        # Populate forms with current GET parameters or defaults
        context['filter_form'] = service.get_filter_form()
        context['column_selection_form'] = service.get_column_selection_form()
        
        # Initial table load (can be empty, DataTables will load via HTMX)
        context['report_headers'] = []
        context['report_data'] = []
        
        return context

class ReportTablePartialView(View):
    # This view will be fetched via HTMX to update the table
    def get(self, request, *args, **kwargs):
        service = ReportService(request)
        
        # Data will be fetched based on filter and column selection from request.GET
        report_data = service.get_report_data()
        report_headers = service.get_report_headers()

        context = {
            'report_headers': report_headers,
            'report_data': report_data,
        }
        return render(request, 'reports/search_details/_table.html', context)

class ReportExcelExportView(View):
    # This view handles the Excel export
    def get(self, request, *args, **kwargs):
        service = ReportService(request)
        try:
            return service.export_to_excel()
        except Exception as e:
            messages.error(request, f"Error during export: {e}")
            # For HTMX requests, a 204 with HX-Trigger is often preferred
            # For non-HTMX, a redirect back with error message.
            if request.headers.get('HX-Request'):
                return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'})
            return HttpResponse(f"Error exporting data: {e}", status=500)

class ReportCancelView(View):
    # Mimics btnCancel_Click redirect
    def get(self, request, *args, **kwargs):
        # Assuming 'Search.aspx' maps to a Django URL 'reports:search_page'
        return HttpResponse(status=204, headers={'HX-Redirect': reverse_lazy('reports:search_page')})
        # If not HTMX, use redirect: return redirect(reverse_lazy('reports:search_page'))

```

#### 4.5 Templates (`reports/templates/reports/search_details/`)

**`list.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">PVEV Search Details Report</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Report Filters</h3>
        <form id="report-filter-form" 
              hx-get="{% url 'reports:report_table_partial' %}"
              hx-target="#reportTable-container"
              hx-indicator="#table-loading-indicator"
              hx-swap="innerHTML"
              hx-trigger="submit">
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                {% for field in filter_form %}
                {% if field.name != 'report_type' and field.name != 'report_status' %} {# Hide these for now if directly setting #}
                <div class="mb-4">
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ field.label }}
                    </label>
                    {{ field }}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endif %}
                {% endfor %}

                {# Hidden fields for report type and status from URL/initial load #}
                <input type="hidden" name="report_type" value="{{ filter_form.report_type.value }}">
                <input type="hidden" name="report_status" value="{{ filter_form.report_status.value }}">
            </div>

            <div class="mb-6 bg-gray-50 p-4 rounded-md shadow-inner" x-data="{ checkAll: false }">
                <div class="flex items-center mb-4">
                    <strong class="text-gray-700 mr-4">Select columns to show in the GridView:</strong>
                    <label class="inline-flex items-center">
                        <input type="checkbox" x-model="checkAll" @change="
                            document.querySelectorAll('#column_selection_form input[type=checkbox]').forEach(el => {
                                if (el.name !== 'CompId' && el.name !== 'Code') { // Exclude CompId, Code
                                    el.checked = checkAll;
                                }
                            });
                            $dispatch('columns-changed'); // Trigger HTMX update
                        " class="form-checkbox h-4 w-4 text-blue-600">
                        <span class="ml-2 text-gray-700 font-bold">Check All</span>
                    </label>
                </div>
                
                <div id="column_selection_form" x-data="{}" 
                     hx-post="{% url 'reports:report_update_columns' %}" {# New endpoint to update column list based on report_status/type #}
                     hx-target="#column_selection_form_content"
                     hx-swap="innerHTML"
                     hx-trigger="load, change from #report-filter-form select[name='report_type'], change from #report-filter-form select[name='report_status']"
                     _="on htmx:afterSwap remove .is-active from #modal if event.detail.target.id == 'column_selection_form_content' and the target's firstElementChild is not a form"
                     >
                    {# Initial content will be loaded here via HTMX on page load #}
                    <div id="column_selection_form_content">
                        <div class="text-center py-4">
                            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                            <p class="mt-2 text-sm text-gray-600">Loading column options...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-lg transition duration-300">
                    Show Report
                </button>
                <a href="{% url 'reports:excel_export' %}?{{ request.GET.urlencode }}" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-5 rounded-md shadow-lg transition duration-300">
                    Export To Excel
                </a>
                <a href="{% url 'reports:search_page' %}" class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-5 rounded-md shadow-lg transition duration-300">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Report Data</h3>
        <div id="reportTable-container"
             hx-trigger="load, click from #report-filter-form button[type='submit']"
             hx-get="{% url 'reports:report_table_partial' %}?{{ request.GET.urlencode }}&{% for col in column_selection_form.columns.value %}columns={{ col }}&{% endfor %}"
             hx-swap="innerHTML">
            <!-- Initial loading indicator -->
            <div id="table-loading-indicator" class="text-center py-4" hx-indicator="on">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading report data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js init is handled globally if Alpine is included in base.html
    document.addEventListener('alpine:init', () => {
        // No specific component for this page as it's primarily HTMX driven
    });

    // Event listener to trigger HTMX request when column checkboxes change
    document.addEventListener('change', function(event) {
        if (event.target.closest('#column_selection_form') && event.target.type === 'checkbox') {
            // Trigger the main form submission implicitly to refresh the table
            const filterForm = document.getElementById('report-filter-form');
            if (filterForm) {
                htmx.trigger(filterForm, 'submit');
            }
        }
    });

    // Listen for HTMX events after report table load to initialize DataTables
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        const targetId = evt.detail.target.id;
        if (targetId === 'reportTable-container') {
            // Check if DataTables library is loaded and the table exists
            if (typeof $.fn.DataTable !== 'undefined' && $('#reportTable').length) {
                $('#reportTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Destroy existing DataTables instance if any
                });
            } else {
                console.warn('DataTables not found or table element not present for initialization.');
            }
        }
    });

    // Listen for custom event to show messages (e.g. from Excel export error)
    document.body.addEventListener('showMessage', function(evt) {
        if (evt.detail && evt.detail.message) {
            alert(evt.detail.message); // Simple alert for demonstration
        }
    });

</script>
{% endblock %}
```

**`_table.html` (Partial for DataTables Content)**

```html
{% if report_data %}
<div class="overflow-x-auto">
    <table id="reportTable" class="min-w-full divide-y divide-gray-200 bg-white">
        <thead class="bg-gray-50">
            <tr>
                {% for header in report_headers %}
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {{ header }}
                </th>
                {% endfor %}
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for row in report_data %}
            <tr>
                {% for header in report_headers %}
                {% comment %} Map header back to original column key for data access {% endcomment %}
                {% with original_column_key=None %}
                    {% for key, col_info in COLUMN_MAP.items %}
                        {% if col_info.display == header %}
                            {% with original_column_key=key %}{% endwith %}
                        {% endif %}
                    {% endfor %}
                {% endwith %}
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                    {{ row|get_item:original_column_key }}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<p class="text-gray-600 text-center py-8">No records to display based on your selection and filters.</p>
{% endif %}

{# Custom Django template filter to access dictionary item by key %}
{% load custom_filters %} 

```
**`reports/templatetags/custom_filters.py`**
```python
from django import template

register = template.Library()

@register.filter(name='get_item')
def get_item(dictionary, key):
    return dictionary.get(key)
```

**`_column_selection_form.html` (Partial for Column Selection Form)**

This partial is loaded dynamically by HTMX to provide the `ColumnSelectionForm` content.

```html
<table class="style2">
    <tr>
        <td valign="top">
            <div id="Panel2" class="border border-gray-300 rounded-md p-4">
                <table width="100%">
                    <tr>
                        <td align="center">
                            <span class="font-bold text-gray-800">Available Columns:</span>
                        </td>
                    </tr>
                </table>
                <div class="flex flex-wrap gap-x-6 gap-y-2 mt-4">
                    {% for choice in column_selection_form.columns.field.choices %}
                    <label class="inline-flex items-center text-sm font-medium text-gray-700">
                        <input type="checkbox" name="{{ column_selection_form.columns.name }}" value="{{ choice.0 }}"
                            {% if choice.0 in column_selection_form.columns.value %}checked{% endif %}
                            class="form-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 rounded-sm"
                            hx-get="{% url 'reports:report_table_partial' %}" {# Trigger table update on checkbox change #}
                            hx-target="#reportTable-container"
                            hx-indicator="#table-loading-indicator"
                            hx-swap="innerHTML"
                            hx-trigger="change"
                        >
                        <span class="ml-2">{{ choice.1 }}</span>
                    </label>
                    {% endfor %}
                </div>
            </div>
        </td>
    </tr>
</table>
```

#### 4.6 URLs (`reports/urls.py`)

```python
from django.urls import path
from .views import ReportListView, ReportTablePartialView, ReportExcelExportView, ReportCancelView
from .forms import ColumnSelectionForm
from django.shortcuts import render # For the simple column selection partial view

app_name = 'reports'

urlpatterns = [
    path('pvev-report/', ReportListView.as_view(), name='pvev_report_list'),
    path('pvev-report/table/', ReportTablePartialView.as_view(), name='report_table_partial'),
    path('pvev-report/excel/', ReportExcelExportView.as_view(), name='excel_export'),
    path('pvev-report/cancel/', ReportCancelView.as_view(), name='cancel_report'),
    
    # Placeholder for the search page redirect from cancel button
    path('search/', lambda request: render(request, 'reports/search_page.html'), name='search_page'),

    # HTMX endpoint for dynamically updating column selection options
    path('pvev-report/update-columns/', lambda request: render(
        request, 
        'reports/search_details/_column_selection_form.html', 
        {
            'column_selection_form': ColumnSelectionForm(request.POST, report_type=int(request.POST.get('report_type', 0)), report_status=int(request.POST.get('report_status', 0))),
            'COLUMN_MAP': ColumnSelectionForm.COLUMN_MAP # Pass the map for dynamic rendering logic
        }
    ), name='report_update_columns'),
]

```

#### 4.7 Tests (`reports/tests.py`)

This section provides comprehensive unit and integration tests for the new Django application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User # Assuming Django's User model for company_id
from datetime import date
from io import BytesIO
import openpyxl

from .models import PVEVItemView, PVEVItemGSNView
from .forms import ReportFilterForm, ColumnSelectionForm, REPORT_TYPE_PVEV, REPORT_TYPE_PVEV_GSN, COLUMN_MAP
from .services import ReportService

# Mock User and company_id for testing purposes
class MockUser:
    def __init__(self, company_id):
        self.company_id = company_id

class ReportServiceTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup mock data for PVEVItemView and PVEVItemGSNView
        # Since models are unmanaged, we need to mock DB interaction or ensure DB has data
        # For testing, we'll mock the queryset directly.
        pass # Actual database setup is needed if models are truly unmanaged

    def setUp(self):
        # Create a mock request object for the service
        self.mock_user = MockUser(company_id=123)
        self.client = Client()
        
        # Patch the model querysets for isolation
        # This is crucial for testing managed=False models without a real DB
        self.pvev_patch = self.patch_queryset(PVEVItemView, [
            {'SrNo': 1, 'ItemCode': 'ITEM001', 'Description': 'Test Desc A', 'UOM': 'KG', 'StockQty': 100.0,
             'PONo': 'PO001', 'PODate': '01-01-2023', 'WONO': 'WO001', 'Qty': 50.0, 'Rate': 10.0,
             'Discount': 0.0, 'SupplierName': 'Supp A', 'AcceptedQty': 50.0, 'PVEVNo': 'PVEV001',
             'PVEVDate': '05-01-2023', 'BillNo': 'BILL001', 'BillDate': '04-01-2023', 'CompId': 123, 'id': 1,
             'GQNNo': 'GQN001', 'GQNDate': '03-01-2023'},
            {'SrNo': 2, 'ItemCode': 'ITEM002', 'Description': 'Test Desc B', 'UOM': 'LBS', 'StockQty': 200.0,
             'PONo': 'PO002', 'PODate': '02-01-2023', 'WONO': 'WO002', 'Qty': 75.0, 'Rate': 15.0,
             'Discount': 1.0, 'SupplierName': 'Supp B', 'AcceptedQty': 70.0, 'PVEVNo': None,
             'PVEVDate': None, 'BillNo': None, 'BillDate': None, 'CompId': 123, 'id': 2,
             'GQNNo': 'GQN002', 'GQNDate': '04-01-2023'},
        ])
        
        self.pvev_gsn_patch = self.patch_queryset(PVEVItemGSNView, [
            {'SrNo': 1, 'ItemCode': 'ITEM003', 'Description': 'Test Desc C', 'UOM': 'UNIT', 'StockQty': 50.0,
             'PONo': 'PO003', 'PODate': '03-01-2023', 'WONO': 'WO003', 'Qty': 25.0, 'Rate': 5.0,
             'Discount': 0.0, 'SupplierName': 'Supp C', 'AcceptedQty': 25.0, 'PVEVNo': 'PVEV003',
             'PVEVDate': '06-01-2023', 'BillNo': 'BILL003', 'BillDate': '05-01-2023', 'CompId': 123, 'id': 3,
             'GSNNo': 'GSN001', 'GSNDate': '04-01-2023'},
            {'SrNo': 2, 'ItemCode': 'ITEM004', 'Description': 'Test Desc D', 'UOM': 'BOX', 'StockQty': 150.0,
             'PONo': 'PO004', 'PODate': '04-01-2023', 'WONO': 'WO004', 'Qty': 60.0, 'Rate': 20.0,
             'Discount': 2.0, 'SupplierName': 'Supp D', 'AcceptedQty': 55.0, 'PVEVNo': None,
             'PVEVDate': None, 'BillNo': None, 'BillDate': None, 'CompId': 123, 'id': 4,
             'GSNNo': 'GSN002', 'GSNDate': '05-01-2023'},
        ])
        self.pvev_patch.start()
        self.pvev_gsn_patch.start()

    def tearDown(self):
        self.pvev_patch.stop()
        self.pvev_gsn_patch.stop()

    def patch_queryset(self, model, data):
        # Helper to mock QuerySet for unmanaged models
        from unittest.mock import patch, MagicMock
        mock_qs = MagicMock()
        mock_qs.filter.return_value = mock_qs # filter returns itself
        mock_qs.values.return_value = data # values returns the mock data

        # Ensure that `pk` or `id` is part of the `values` return if used in ORM
        # For simplicity, we are just returning flat dicts.
        
        # If order_by or annotate with Window is called, ensure it also returns mock_qs
        mock_qs.order_by.return_value = mock_qs
        mock_qs.annotate.return_value = mock_qs

        return patch.object(model, 'objects', mock_qs)

    def test_report_service_initialization(self):
        request = self.client.get('/')
        request.user = self.mock_user
        service = ReportService(request)
        self.assertEqual(service.company_id, 123)
        self.assertEqual(service.report_type, REPORT_TYPE_PVEV_GSN) # Default
        self.assertEqual(service.report_status, 0) # Default

    def test_get_selected_columns_default(self):
        request = self.client.get('/')
        request.user = self.mock_user
        service = ReportService(request)
        selected_columns = service.get_selected_columns()
        # Check initial default columns for GSN pending report
        expected_defaults = [
            k for k, v in COLUMN_MAP.items() 
            if v.get('is_default') and not v.get('exclude_selection') and
               ('report_type' not in v or v['report_type'] == REPORT_TYPE_PVEV_GSN) and
               (not v.get('hide_flag_0') or 0 != 0) # report_status = 0
        ]
        self.assertListEqual(sorted(selected_columns), sorted(expected_defaults))

    def test_get_report_data_pvev_completed(self):
        request = self.client.get('/?report_type=1&report_status=1&columns=ItemCode&columns=PVEVNo')
        request.user = self.mock_user
        service = ReportService(request)
        data = service.get_report_data()
        self.assertEqual(len(data), 1) # Only PVEV001 from PVEVItemView is completed
        self.assertEqual(data[0]['ItemCode'], 'ITEM001')
        self.assertEqual(data[0]['PVEVNo'], 'PVEV001')

    def test_get_report_data_gsn_pending(self):
        request = self.client.get('/?report_type=0&report_status=0&columns=ItemCode&columns=GSNNo')
        request.user = self.mock_user
        service = ReportService(request)
        data = service.get_report_data()
        self.assertEqual(len(data), 1) # Only ITEM004 from PVEVItemGSNView is pending
        self.assertEqual(data[0]['ItemCode'], 'ITEM004')
        self.assertEqual(data[0]['GSNNo'], 'GSN002')

    def test_export_to_excel(self):
        request = self.client.get('/?report_type=1&report_status=1&columns=ItemCode&columns=PVEVNo')
        request.user = self.mock_user
        service = ReportService(request)
        response = service.export_to_excel()

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertTrue(response['Content-Disposition'].startswith('attachment; filename="PVEV_Completed.xlsx"'))

        # Verify content of the Excel file
        workbook = openpyxl.load_workbook(BytesIO(response.content))
        sheet = workbook.active
        self.assertEqual(sheet['A1'].value, 'Item Code')
        self.assertEqual(sheet['B1'].value, 'PVEV No')
        self.assertEqual(sheet['A2'].value, 'ITEM001')
        self.assertEqual(sheet['B2'].value, 'PVEV001')

class ReportViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(username='testuser', password='password')
        self.client.force_login(self.user)
        
        # Mock the company_id for the user
        self.user.company_id = 123 # Assuming User model has a company_id field
        
        # Patch ReportService for views testing to control data flow
        from unittest.mock import patch, MagicMock
        self.mock_service = MagicMock(spec=ReportService)
        self.mock_service.get_report_data.return_value = [
            {'ItemCode': 'MOCKED_ITEM', 'Description': 'Mocked Desc'}
        ]
        self.mock_service.get_report_headers.return_value = ['Item Code', 'Description']
        self.mock_service.get_filter_form.return_value = ReportFilterForm()
        self.mock_service.get_column_selection_form.return_value = ColumnSelectionForm()
        
        self.service_patch = patch('reports.views.ReportService', return_value=self.mock_service)
        self.service_patch.start()

    def tearDown(self):
        self.service_patch.stop()

    def test_report_list_view_get(self):
        response = self.client.get(reverse('reports:pvev_report_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/search_details/list.html')
        self.assertIn('filter_form', response.context)
        self.assertIn('column_selection_form', response.context)
        self.mock_service.get_filter_form.assert_called_once()
        self.mock_service.get_column_selection_form.assert_called_once()

    def test_report_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('reports:report_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reports/search_details/_table.html')
        self.assertIn('report_headers', response.context)
        self.assertIn('report_data', response.context)
        self.mock_service.get_report_data.assert_called_once()
        self.mock_service.get_report_headers.assert_called_once()
        self.assertContains(response, 'MOCKED_ITEM')

    def test_excel_export_view(self):
        # Mock export_to_excel to return a dummy HttpResponse
        self.mock_service.export_to_excel.return_value = HttpResponse(b"dummy_excel_data", content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        
        response = self.client.get(reverse('reports:excel_export'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.mock_service.export_to_excel.assert_called_once()

    def test_excel_export_view_error(self):
        self.mock_service.export_to_excel.side_effect = Exception("Export failed")
        response = self.client.get(reverse('reports:excel_export'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 for HTMX error with HX-Trigger
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'showMessage')

    def test_report_cancel_view(self):
        response = self.client.get(reverse('reports:cancel_report'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Redirect'], reverse('reports:search_page'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic updates:**
    *   The main `ReportListView` (`list.html`) contains a `<div id="reportTable-container">` which is an HTMX target.
    *   The `ReportFilterForm` submits via `hx-get` to `{% url 'reports:report_table_partial' %}` (`ReportTablePartialView`). This refreshes the `reportTable-container` with the new filtered data.
    *   `hx-trigger="load, click from #report-filter-form button[type='submit']"` ensures the table loads on page load and when the "Show Report" button is clicked.
    *   Column selection checkboxes within `_column_selection_form.html` (which is itself loaded via HTMX) also trigger the main form submission (`htmx.trigger(filterForm, 'submit')` in `extra_js`) when changed, leading to a table refresh.
    *   The Excel export button is a standard `<a>` tag with the current `GET` parameters, triggering a file download.

*   **Alpine.js for UI state management:**
    *   `checkAll` checkbox: An `x-data="{ checkAll: false }"` directive is used to manage the state of the "Check All" checkbox. Its `@change` handler iterates through the column checkboxes to check/uncheck them. This is a common Alpine.js pattern for simple UI states.
    *   No complex modals or other Alpine.js state needed, as CRUD operations are not present here.

*   **DataTables for list views:**
    *   The `_table.html` partial directly contains the `<table>` element with `id="reportTable"`.
    *   A JavaScript block within `extra_js` in `list.html` listens for `htmx:afterOnLoad` event on `reportTable-container`. Once the table HTML is loaded, it initializes DataTables on `#reportTable`. `destroy: true` is crucial for re-initializing DataTables when the content is swapped.

*   **No full page reloads:** All report filtering and table updates happen dynamically via HTMX, replacing only the necessary parts of the DOM. The Excel export triggers a file download, not a full page reload.

*   **DRY Template Inheritance:** `list.html` extends `core/base.html` and includes `extra_js` block for page-specific scripts. No `base.html` code is duplicated.

## Final Notes

*   **Placeholders:** `request.user.company_id` is a placeholder for how `CompId` would be sourced from the authenticated user. You would need to ensure your user model (or a profile linked to it) contains this information.
*   **Database Views:** The most significant simplification here is assuming the database views (`View_PVEVNo_Item`, `View_PVEVNo_Item_GSN`) are either already returning fields with correct types (e.g., actual `DATE` fields) or that the `CharField` with Python-side `strptime` is acceptable for string-based dates. For optimal performance and data integrity, converting these views to return proper `DATE` or `DATETIME` types is highly recommended as a separate modernization step.
*   **`AccHead` Lookup:** The original `accval` parameter involved a database lookup for `Symbol` in `AccHead` table. This `AccHead` model and its corresponding lookup (`AccHead.objects.get(id=parsed_data['account_value']).symbol`) would need to be created and integrated into the `ReportService` for full feature parity.
*   **Error Handling:** The `ReportService` includes basic `try-except` blocks. In a production environment, this would be expanded with proper logging and potentially more sophisticated user feedback.
*   **`search_page`:** The `ReportCancelView` points to a placeholder `reports:search_page` URL. You would replace this with the actual Django URL for the original `Search.aspx` functionality.
*   **Testing `managed=False` Models:** The tests include patching `objects` on the `PVEVItemView` and `PVEVItemGSNView` models. This is a common and effective way to test `managed=False` models without relying on a live database, ensuring tests are fast and isolated. For a real project, ensure your `settings.py` correctly points to your SQL Server database.