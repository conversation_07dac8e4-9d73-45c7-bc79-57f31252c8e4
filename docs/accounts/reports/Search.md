## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan for Search Functionality

This document outlines a strategic plan to modernize your existing ASP.NET Search functionality into a robust, scalable, and user-friendly Django application. We'll leverage modern Django 5.0+ patterns, HTMX for dynamic interactions, Alpine.js for client-side reactivity, and Tailwind CSS for a sleek, responsive design. Our focus is on automation-driven approaches to minimize manual effort and ensure a smooth transition.

### Business Value & Outcomes:

1.  **Enhanced User Experience:** A modern, highly responsive interface with instant feedback without full page reloads, making the search process faster and more intuitive.
2.  **Improved Performance:** By reducing server-side processing for minor UI updates and utilizing efficient data fetching, the application will feel significantly snappier.
3.  **Simplified Maintenance:** Django's clear architecture, "Fat Model, Thin View" principle, and built-in ORM simplify debugging, updates, and future enhancements compared to legacy ASP.NET.
4.  **Reduced Development Costs:** Adopting a standardized, modern framework like Django with strong community support lowers the learning curve for new developers and provides access to a vast ecosystem of reusable components.
5.  **Future-Proofing:** Moving away from dated technologies ensures your application remains relevant and adaptable to new business requirements and technological advancements.
6.  **Scalability:** Django's design principles support building applications that can handle increased user loads and data volumes effectively.

---

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following database interactions for lookup data and general application context:

*   **`tblMM_Supplier_master`**: Used for supplier autocomplete.
    *   Columns: `SupplierId` (likely `INT`), `SupplierName` (likely `NVARCHAR`).
*   **`AccHead`**: Used for populating the "Account Head" dropdown.
    *   Columns: `Id` (likely `INT`), `Symbol` (likely `NVARCHAR`), `Description` (likely `NVARCHAR`), `Category` (likely `NVARCHAR`, e.g., "Labour", "With Material").
*   **Session Data**: `HttpContext.Current.Session["compid"]` indicates a `CompId` (Company ID) is used in database queries. This will need to be handled by Django's session management or context.

**Inferred Tables and Columns:**

*   **Table Name**: `tblMM_Supplier_master`
    *   `SupplierId` (Primary Key, Integer)
    *   `SupplierName` (String)
*   **Table Name**: `AccHead`
    *   `Id` (Primary Key, Integer)
    *   `Symbol` (String)
    *   `Description` (String)
    *   `Category` (String)

The actual search results (GQN, GSN, PO, PVEV) are generated by `Search_details.aspx` and are not directly queried on this page. This page primarily acts as a form to collect parameters for the results page.

### Step 2: Identify Backend Functionality

The `Search.aspx` page's primary function is to collect search criteria and then redirect the user to a results page (`Search_details.aspx`) with those criteria as URL parameters.

*   **Read/Lookup Operations:**
    *   **Supplier Autocomplete (`sql` web method):** Fetches `SupplierName` and `SupplierId` from `tblMM_Supplier_master` based on a prefix, filtered by `CompId`.
    *   **Account Head Dropdown (`AcHead` method):** Populates `DropDownList1` with `Head` (derived from `Symbol` and `Description`) and `Id` from `AccHead`, filtered by `Category` (Labour, With Material, Expenses, Service Provider).
*   **Form Submission:**
    *   The `BtnSearch_Click` event gathers all selected and entered criteria.
    *   It performs basic validation (e.g., ensuring both date fields are entered if one is, or that certain fields are entered if their corresponding checkboxes are checked).
    *   It constructs a URL with numerous query parameters and performs a client-side `Response.Redirect`.
*   **Dynamic UI Logic (handled by JavaScript in ASP.NET, to be migrated to Alpine.js/HTMX):**
    *   Enabling/disabling `txtNo` based on `Drpoption` selection.
    *   Enabling/disabling specific items in `Drpoption` based on `radGqn`/`radgsn` selection.
    *   Enabling/disabling `txtItemcode`, `txtwono`, `TxtSearchValue` based on `CkItem`, `CKwono`, `CkSupplier` checkbox states.
    *   Repopulating `DropDownList1` when any "Account Head" radio button is changed.

### Step 3: Infer UI Components

The ASP.NET page is primarily a search form. Key components and their Django/HTMX/Alpine.js equivalents:

*   **Master Page (`MasterPage.master`)**: Replaced by Django template inheritance (`{% extends 'core/base.html' %}`).
*   **Tab Container (`TabContainer1`)**: Can be implemented using Alpine.js for tab switching, or if tabs contain heavy dynamic content, HTMX for lazy loading tab content. For this simple case, Alpine.js is sufficient.
*   **Form Inputs**:
    *   `asp:DropDownList` (`Drpoption`, `DropDownList1`): Django `forms.ChoiceField` or `forms.ModelChoiceField`, rendered as `<select>`.
    *   `asp:TextBox` (`txtNo`, `txtItemcode`, etc.): Django `forms.CharField`, rendered as `<input type="text">`.
    *   `asp:RadioButton` (`radGqn`, `radgsn`, `RbtnLabour`, etc.): Django `forms.RadioSelect`, rendered as `<input type="radio">`.
    *   `asp:CheckBox` (`CkItem`, `CKwono`, `CkSupplier`, `CkACCHead`): Django `forms.BooleanField`, rendered as `<input type="checkbox">`.
*   **Date Pickers (`CalendarExtender`)**: Modern HTML5 `type="date"` input or a lightweight JavaScript library like Flatpickr integrated with Alpine.js if more advanced functionality is needed.
*   **Autocomplete (`AutoCompleteExtender`)**: Handled by an HTMX request to a Django view, displaying suggestions with Alpine.js.
*   **Validation Controls (`RegularExpressionValidator`, `CompareValidator`)**: Django form validation (`forms.DateField` with custom validation in `clean_` methods or `clean`). Client-side validation can be enhanced with Alpine.js.
*   **Search Button (`BtnSearch`)**: A standard `<button type="submit">` that triggers the form submission (GET request).

---

### Step 4: Generate Django Code

We will create a new Django app, let's call it `accounts_reports`, to house this functionality.

#### 4.1 Models (for Lookup Data)

We will define models for the lookup tables (`Supplier` and `AccHead`) that are referenced by the search form. While the `Search` page doesn't directly perform CRUD on these, their definition is crucial for data retrieval and form population.

**`accounts_reports/models.py`**

```python
from django.db import models

# Model for Suppliers (from tblMM_Supplier_master)
class Supplier(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

    @classmethod
    def get_supplier_id_by_name(cls, name):
        """
        Retrieves SupplierId from SupplierName.
        This mirrors clsFunctions.getCode(TxtSearchValue.Text)
        """
        try:
            return cls.objects.get(supplier_name__iexact=name).supplier_id
        except cls.DoesNotExist:
            return None # Or raise an exception, depending on error handling strategy

# Model for Account Heads (from AccHead)
class AccHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)
    category = models.CharField(db_column='Category', max_length=50)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}"

    @classmethod
    def get_heads_by_category(cls, category):
        """
        Returns Account Heads filtered by category.
        This mirrors the AcHead method logic.
        """
        return cls.objects.filter(category=category).order_by('description')

```

#### 4.2 Forms

We'll define a Django `Form` to represent the search criteria. This is a regular `Form` because it doesn't directly map to a single database model for CRUD operations; it's for collecting input parameters.

**`accounts_reports/forms.py`**

```python
from django import forms
from .models import AccHead, Supplier
import datetime

class SearchForm(forms.Form):
    # Drpoption equivalent
    SEARCH_OPTIONS = [
        ('0', 'All'),
        ('1', 'GQN No'),
        ('2', 'GSN No'),
        ('3', 'PO No'),
        ('4', 'PVEV No'),
    ]
    option = forms.ChoiceField(
        choices=SEARCH_OPTIONS,
        initial='0',
        label='Enter No.',
        widget=forms.Select(attrs={'class': 'box3', 'x-model': 'search_option', 'x-on:change': 'updateNumberInputState()'}) # x-model for Alpine.js
    )
    number = forms.CharField(
        max_length=100,
        required=False,
        label='', # Label is handled by template
        widget=forms.TextInput(attrs={'class': 'box3', 'x-bind:disabled': "search_option === '0'"}) # x-bind:disabled for Alpine.js
    )

    # Report For Radio Buttons
    report_for = forms.ChoiceField(
        choices=[('1', 'GQN'), ('0', 'GSN')], # 1 for GQN, 0 for GSN as per C# logic
        initial='1',
        widget=forms.RadioSelect(attrs={'x-model': 'report_type', 'x-on:change': 'updateOptionDropdownState()'}) # x-model for Alpine.js
    )

    # Item Code Checkbox & Textbox
    item_code_checked = forms.BooleanField(
        required=False,
        label='ItemCode',
        widget=forms.CheckboxInput(attrs={'x-model': 'item_code_enabled'}) # x-model for Alpine.js
    )
    item_code = forms.CharField(
        max_length=100,
        required=False,
        label='', # Label handled by template
        widget=forms.TextInput(attrs={'class': 'box3', 'x-bind:disabled': '!item_code_enabled'}) # x-bind:disabled for Alpine.js
    )

    # Work Order No Checkbox & Textbox
    wo_no_checked = forms.BooleanField(
        required=False,
        label='WorkOrder No',
        widget=forms.CheckboxInput(attrs={'x-model': 'wo_no_enabled'}) # x-model for Alpine.js
    )
    wo_no = forms.CharField(
        max_length=100,
        required=False,
        label='',
        widget=forms.TextInput(attrs={'class': 'box3', 'x-bind:disabled': '!wo_no_enabled'}) # x-bind:disabled for Alpine.js
    )

    # Supplier Name Checkbox & Autocomplete Textbox
    supplier_checked = forms.BooleanField(
        required=False,
        label='Supplier Name',
        widget=forms.CheckboxInput(attrs={'x-model': 'supplier_enabled'}) # x-model for Alpine.js
    )
    supplier_name = forms.CharField(
        max_length=255,
        required=False,
        label='',
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'x-bind:disabled': '!supplier_enabled',
            'placeholder': 'Start typing supplier name...',
            # HTMX attributes for autocomplete
            'hx-get': '/accounts-reports/suppliers-autocomplete/', # Endpoint for suggestions
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup after delay
            'hx-target': '#supplier-suggestions', # Target div for suggestions
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
            'x-on:focus': 'showSuggestions = true', # Alpine for showing suggestions
            'x-on:blur.debounce.500ms': 'showSuggestions = false', # Alpine for hiding suggestions
        })
    )
    # Hidden field to store supplier ID once selected (can be populated by Alpine.js on selection)
    supplier_id = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput(attrs={'x-model': 'selected_supplier_id'})
    )


    # Account Head Checkbox & Radio Buttons & Dropdown
    acc_head_checked = forms.BooleanField(
        required=False,
        label='Account Head',
        widget=forms.CheckboxInput(attrs={'x-model': 'acc_head_enabled', 'x-on:change': 'updateAccHeadState()'}) # Alpine.js
    )
    ACC_HEAD_CATEGORIES = [
        ('Labour', 'Labour'),
        ('With Material', 'With Material'),
        ('Expenses', 'Expenses'),
        ('Service Provider', 'Service Provider'),
    ]
    acc_head_category = forms.ChoiceField(
        choices=ACC_HEAD_CATEGORIES,
        initial='Labour',
        widget=forms.RadioSelect(attrs={
            'x-model': 'acc_head_category', # Alpine.js
            'hx-get': '/accounts-reports/acc-heads/', # HTMX endpoint
            'hx-trigger': 'change', # Trigger on change
            'hx-target': '#id_acc_head_dropdown', # Target the dropdown
            'hx-swap': 'innerHTML',
            'x-bind:disabled': '!acc_head_enabled' # Alpine.js
        })
    )
    acc_head_dropdown = forms.ChoiceField(
        choices=[], # Will be populated dynamically by HTMX/JS
        required=False,
        label='',
        widget=forms.Select(attrs={
            'class': 'box3',
            'id': 'id_acc_head_dropdown', # HTMX target ID
            'x-bind:disabled': '!acc_head_enabled' # Alpine.js
        })
    )

    # Date Range
    from_date = forms.DateField(
        required=False,
        label='From',
        input_formats=['%d-%m-%Y'], # Match ASP.NET format
        widget=forms.TextInput(attrs={'class': 'box3', 'type': 'date'}) # HTML5 date picker
    )
    to_date = forms.DateField(
        required=False,
        label='To',
        input_formats=['%d-%m-%Y'],
        widget=forms.TextInput(attrs={'class': 'box3', 'type': 'date'})
    )

    # Status Radio Buttons
    STATUS_OPTIONS = [
        ('1', 'Completed'), # 1 for GIN (Completed), 0 for PO (Pending)
        ('0', 'Pending'),
    ]
    status = forms.ChoiceField(
        choices=STATUS_OPTIONS,
        initial='1',
        widget=forms.RadioSelect(attrs={'class': 'group-apr'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initial population of acc_head_dropdown for "Labour" category
        self.fields['acc_head_dropdown'].choices = [
            (str(head.id), str(head)) for head in AccHead.get_heads_by_category('Labour')
        ]

        # Add Tailwind classes to all fields
        for name, field in self.fields.items():
            if name not in ['option', 'number', 'item_code', 'wo_no', 'supplier_name', 'acc_head_dropdown', 'from_date', 'to_date']:
                 # Apply default text input style to relevant fields, handle others specifically
                if isinstance(field.widget, (forms.TextInput, forms.Textarea, forms.Select)):
                    current_classes = field.widget.attrs.get('class', '')
                    field.widget.attrs['class'] = f'{current_classes} block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'.strip()
            # Special handling for checkboxes and radio buttons to maintain their distinct styling
            if isinstance(field.widget, (forms.CheckboxInput, forms.RadioSelect)):
                 current_classes = field.widget.attrs.get('class', '')
                 field.widget.attrs['class'] = f'{current_classes} h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500'.strip()


    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        # Date range validation (from_date must be <= to_date)
        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', 'From date must be less than or equal to To date.')

        # ASP.NET original had: "Incorrect date entry!" if one date was present but not the other
        if (from_date and not to_date) or (not from_date and to_date):
            self.add_error('from_date', 'Both From and To dates must be provided if one is entered.')
            self.add_error('to_date', 'Both From and To dates must be provided if one is entered.')

        # Validate that if a checkbox is checked, the corresponding text field is not empty
        if cleaned_data.get('item_code_checked') and not cleaned_data.get('item_code'):
            self.add_error('item_code', 'Item Code is required if checkbox is checked.')
        
        if cleaned_data.get('wo_no_checked') and not cleaned_data.get('wo_no'):
            self.add_error('wo_no', 'Work Order No is required if checkbox is checked.')
            
        if cleaned_data.get('supplier_checked') and not cleaned_data.get('supplier_name'):
            self.add_error('supplier_name', 'Supplier Name is required if checkbox is checked.')
        elif cleaned_data.get('supplier_checked') and cleaned_data.get('supplier_name'):
            # Convert supplier name to ID for the redirect URL
            supplier_id = Supplier.get_supplier_id_by_name(cleaned_data['supplier_name'])
            if supplier_id is None:
                self.add_error('supplier_name', 'Enter a valid Supplier Name.')
            else:
                cleaned_data['supplier_id'] = supplier_id

        # Validate that if option is not 'All', then number must be provided
        if cleaned_data.get('option') != '0' and not cleaned_data.get('number'):
            self.add_error('number', f'Enter valid {dict(self.SEARCH_OPTIONS).get(cleaned_data["option"])}.')

        return cleaned_data

```

#### 4.3 Views

We'll have a main `SearchView` to render the form, and separate views to handle HTMX requests for dynamic form elements. The main search form will submit a GET request to a `SearchDetailsView` (not implemented here, but implied by `Search_details.aspx`).

**`accounts_reports/views.py`**

```python
from django.views.generic import FormView, View
from django.urls import reverse_lazy
from django.http import HttpResponse, HttpResponseRedirect
from django.template.loader import render_to_string
from django.utils.http import urlencode
from django.contrib import messages
from .forms import SearchForm
from .models import AccHead, Supplier

class SearchView(FormView):
    """
    Main view for the search form.
    Handles rendering the form and processing its GET submission.
    """
    template_name = 'accounts_reports/search/search_form.html'
    form_class = SearchForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass initial Alpine.js state to the template if needed
        context['initial_alpine_state'] = {
            'search_option': '0',
            'report_type': '1',
            'item_code_enabled': False,
            'wo_no_enabled': False,
            'supplier_enabled': False,
            'acc_head_enabled': False,
            'acc_head_category': 'Labour',
            'showSuggestions': False,
            'selected_supplier_id': '', # To hold the resolved supplier ID
        }
        return context

    def form_valid(self, form):
        """
        Handles valid form submission. Constructs URL for SearchDetailsView.
        """
        cleaned_data = form.cleaned_data

        # Construct query parameters mirroring the ASP.NET Response.Redirect logic
        query_params = {
            'type': cleaned_data.get('option', '0'),
            'No': cleaned_data.get('number', ''),
            'RAd2': '1' if cleaned_data.get('report_for') == '1' else '0', # GQN (1) or GSN (0)
            'RAd': '1' if cleaned_data.get('status') == '1' else '0', # Completed (1) or Pending (0)
            'Code': cleaned_data.get('item_code', ''),
            'WONo': cleaned_data.get('wo_no', ''),
            'SupId': cleaned_data.get('supplier_id', ''), # Use resolved ID
            'accval': cleaned_data.get('acc_head_dropdown', ''), # The selected account head ID
            'FDate': cleaned_data.get('from_date').strftime('%d-%m-%Y') if cleaned_data.get('from_date') else '',
            'TDate': cleaned_data.get('to_date').strftime('%d-%m-%Y') if cleaned_data.get('to_date') else '',
        }
        # Filter out empty parameters if preferred, though ASP.NET included them as empty strings
        query_string = urlencode({k: v for k, v in query_params.items() if v is not None and v != ''})

        # This should redirect to the actual search results page URL
        # For demonstration, we'll redirect to a dummy URL.
        # In a real app, this would be reverse_lazy('accounts_reports:search_details')
        # where 'search_details' is the name of the URL pattern for your results page.
        redirect_url = reverse_lazy('accounts_reports:search_details') + '?' + query_string
        return HttpResponseRedirect(redirect_url)

    def form_invalid(self, form):
        """
        Handles invalid form submission. Re-renders the form with errors.
        """
        # For HTMX, if this were a modal form, we might render a partial.
        # For a full-page form, simply re-rendering is fine.
        # Add messages for errors if desired, similar to ASP.NET alerts.
        for field, errors in form.errors.items():
            for error in errors:
                messages.error(self.request, f"Error in {form[field].label or field}: {error}")
        return super().form_invalid(form)


class AccHeadOptionsView(View):
    """
    HTMX endpoint to return options for the Account Head dropdown based on category.
    Mirrors the AcHead method functionality.
    """
    def get(self, request, *args, **kwargs):
        category = request.GET.get('category', 'Labour') # Default to Labour if not specified
        acc_heads = AccHead.get_heads_by_category(category)
        
        # Render just the <option> tags
        options_html = render_to_string(
            'accounts_reports/partials/_acchead_options.html',
            {'acc_heads': acc_heads}
        )
        return HttpResponse(options_html)

class SupplierAutoCompleteView(View):
    """
    HTMX endpoint to return autocomplete suggestions for supplier names.
    Mirrors the sql web method functionality.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        # In a real app, you'd get comp_id from session/user profile
        # For now, let's assume a default or mock it
        # comp_id = request.session.get('compid') # Example if stored in session
        # For this example, we'll assume CompId is not strictly enforced or is 1
        
        suggestions = []
        if prefix_text:
            # Query the database for matching suppliers, case-insensitive
            suppliers = Supplier.objects.filter(supplier_name__icontains=prefix_text)[:10] # Limit to 10 suggestions
            suggestions = [
                {'name': s.supplier_name, 'id': s.supplier_id}
                for s in suppliers
            ]

        # Render suggestions as an unordered list within a partial template
        suggestions_html = render_to_string(
            'accounts_reports/partials/_supplier_autocomplete_suggestions.html',
            {'suggestions': suggestions}
        )
        return HttpResponse(suggestions_html)


# Dummy view for search results to demonstrate redirection
class SearchDetailsView(View):
    def get(self, request, *args, **kwargs):
        params = request.GET.dict()
        return HttpResponse(f"<h1>Search Results Page</h1><p>Parameters: {params}</p><a href='{reverse_lazy('accounts_reports:search')}'>Back to Search</a>")

```

#### 4.4 Templates

We will create a main template for the search form and partial templates for HTMX-driven updates.

**`accounts_reports/templates/accounts_reports/search/search_form.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Search</h2>

        <form method="GET" action="{% url 'accounts_reports:search' %}" 
              x-data='{% json_script "initial_alpine_state" initial_alpine_state %}'
              x-init="
                alpineState = JSON.parse(document.getElementById('initial_alpine_state').textContent);
                search_option = alpineState.search_option;
                report_type = alpineState.report_type;
                item_code_enabled = alpineState.item_code_enabled;
                wo_no_enabled = alpineState.wo_no_enabled;
                supplier_enabled = alpineState.supplier_enabled;
                acc_head_enabled = alpineState.acc_head_enabled;
                acc_head_category = alpineState.acc_head_category;
                showSuggestions = alpineState.showSuggestions;
                selected_supplier_id = alpineState.selected_supplier_id;

                updateNumberInputState = () => {
                    $nextTick(() => {
                        $refs.number_input.disabled = (search_option === '0');
                        // The original ASP.NET code also modified dropdown options,
                        // which is complex client-side with Alpine.js alone.
                        // We'll rely on server-side validation for missing input if not 'All'.
                    });
                };

                updateOptionDropdownState = () => {
                    $nextTick(() => {
                        const optionSelect = $refs.option_select;
                        // Based on ASP.NET logic:
                        // if radGqn (report_type == '1') is checked:
                        //   Drpoption.Items[2].Enabled = false (GSN No)
                        //   Drpoption.Items[1].Enabled = true (GQN No)
                        // else (radgsn checked, report_type == '0'):
                        //   Drpoption.Items[1].Enabled = false (GQN No)
                        //   Drpoption.Items[2].Enabled = true (GSN No)
                        
                        // Note: Disabling specific <option> elements client-side is tricky
                        // as they might still be submittable in some browsers.
                        // Django's form validation is the ultimate guard.
                        // For pure UI, we can disable/enable by selecting it and then
                        // physically disabling if not needed
                        Array.from(optionSelect.options).forEach(opt => {
                            if (opt.value === '1') { // GQN No
                                opt.disabled = (report_type === '0');
                            } else if (opt.value === '2') { // GSN No
                                opt.disabled = (report_type === '1');
                            }
                        });
                        // If current selected option becomes disabled, reset to 'All'
                        if (optionSelect.selectedOptions[0].disabled) {
                            optionSelect.value = '0';
                            search_option = '0'; // Update Alpine model
                            updateNumberInputState(); // Recalculate number input state
                        }
                    });
                };

                updateAccHeadState = () => {
                    $nextTick(() => {
                        // HTMX handles populating dropdown when category changes
                        // We just manage the overall enabled state with Alpine
                        $refs.acc_head_category_radios.querySelectorAll('input[type=radio]').forEach(radio => {
                            radio.disabled = !acc_head_enabled;
                        });
                        $refs.acc_head_dropdown_select.disabled = !acc_head_enabled;
                    });
                };

                // Initial state updates
                updateNumberInputState();
                updateOptionDropdownState();
                updateAccHeadState();

                // Alpine.js function to handle supplier selection from suggestions
                selectSupplier = (name, id) => {
                    $refs.supplier_name_input.value = name;
                    selected_supplier_id = id;
                    showSuggestions = false;
                };
              "
              >
            {% csrf_token %}
            
            {% if messages %}
                <div class="mb-4">
                    {% for message in messages %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                            <span class="block sm:inline">{{ message }}</span>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mb-6">
                <!-- Enter No. / Drpoption -->
                <div class="col-span-2 flex items-center space-x-2">
                    <label for="{{ form.option.id_for_label }}" class="text-sm font-medium text-gray-700 whitespace-nowrap">Enter No.</label>
                    {{ form.option }}
                    <label for="{{ form.number.id_for_label }}" class="sr-only">Number</label>
                    {{ form.number|attr:'x-ref="number_input"' }}
                    {% if form.number.errors %}<p class="text-red-500 text-xs mt-1">{{ form.number.errors }}</p>{% endif %}

                    <span class="ml-auto text-sm font-medium text-gray-700 whitespace-nowrap">Report For :</span>
                    <div x-ref="report_for_radios">
                        {% for radio in form.report_for %}
                        <label class="inline-flex items-center mr-4">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                        {% endfor %}
                    </div>
                </div>

                <!-- Item Code -->
                <div class="flex items-center space-x-2">
                    <label for="{{ form.item_code_checked.id_for_label }}" class="inline-flex items-center cursor-pointer">
                        {{ form.item_code_checked }}
                        <span class="ml-2 text-sm font-medium text-gray-700">{{ form.item_code_checked.label }}</span>
                    </label>
                    <label for="{{ form.item_code.id_for_label }}" class="sr-only">Item Code</label>
                    {{ form.item_code }}
                    {% if form.item_code.errors %}<p class="text-red-500 text-xs mt-1">{{ form.item_code.errors }}</p>{% endif %}
                </div>

                <!-- Work Order No -->
                <div class="flex items-center space-x-2">
                    <label for="{{ form.wo_no_checked.id_for_label }}" class="inline-flex items-center cursor-pointer">
                        {{ form.wo_no_checked }}
                        <span class="ml-2 text-sm font-medium text-gray-700">{{ form.wo_no_checked.label }}</span>
                    </label>
                    <label for="{{ form.wo_no.id_for_label }}" class="sr-only">Work Order No</label>
                    {{ form.wo_no }}
                    {% if form.wo_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.wo_no.errors }}</p>{% endif %}
                </div>

                <!-- Supplier Name with Autocomplete -->
                <div class="flex items-start space-x-2 relative col-span-2">
                    <label for="{{ form.supplier_checked.id_for_label }}" class="inline-flex items-center cursor-pointer mt-2">
                        {{ form.supplier_checked }}
                        <span class="ml-2 text-sm font-medium text-gray-700">{{ form.supplier_checked.label }}</span>
                    </label>
                    <div class="flex-grow">
                        <label for="{{ form.supplier_name.id_for_label }}" class="sr-only">Supplier Name</label>
                        {{ form.supplier_name|attr:'x-ref="supplier_name_input"' }}
                        {{ form.supplier_id }} {# Hidden input for resolved ID #}
                        {% if form.supplier_name.errors %}<p class="text-red-500 text-xs mt-1">{{ form.supplier_name.errors }}</p>{% endif %}
                        <div id="supplier-suggestions" 
                             class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto"
                             x-show="showSuggestions && $refs.supplier_name_input.value.length > 0"
                             @click.away="showSuggestions = false">
                             <!-- Suggestions loaded here via HTMX -->
                        </div>
                    </div>
                </div>

                <!-- Account Head -->
                <div class="col-span-2 flex flex-wrap items-center space-x-4">
                    <label for="{{ form.acc_head_checked.id_for_label }}" class="inline-flex items-center cursor-pointer">
                        {{ form.acc_head_checked }}
                        <span class="ml-2 text-sm font-medium text-gray-700">{{ form.acc_head_checked.label }}</span>
                    </label>
                    <div x-ref="acc_head_category_radios">
                        {% for radio in form.acc_head_category %}
                        <label class="inline-flex items-center mr-4">
                            {{ radio.tag }}
                            <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                        </label>
                        {% endfor %}
                    </div>
                    <label for="{{ form.acc_head_dropdown.id_for_label }}" class="sr-only">Account Head Dropdown</label>
                    {{ form.acc_head_dropdown|attr:'x-ref="acc_head_dropdown_select"' }}
                    {% if form.acc_head_dropdown.errors %}<p class="text-red-500 text-xs mt-1">{{ form.acc_head_dropdown.errors }}</p>{% endif %}
                </div>

                <!-- Select Date -->
                <div class="col-span-2 flex items-center space-x-4">
                    <span class="text-sm font-medium text-gray-700 whitespace-nowrap">Select Date</span>
                    <label for="{{ form.from_date.id_for_label }}" class="ml-4 text-sm font-medium text-gray-700">From:</label>
                    {{ form.from_date }}
                    {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}

                    <label for="{{ form.to_date.id_for_label }}" class="text-sm font-medium text-gray-700">To:</label>
                    {{ form.to_date }}
                    {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                </div>

                <!-- Status Radio Buttons -->
                <div class="col-span-2 flex items-center space-x-4">
                    {% for radio in form.status %}
                    <label class="inline-flex items-center mr-4">
                        {{ radio.tag }}
                        <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                    </label>
                    {% endfor %}
                </div>
            </div>

            <div class="mt-6 text-center">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Search
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components are initialized directly within the x-data attribute
        // No additional global Alpine.js component setup is needed here unless complex.
    });
</script>
{% endblock %}
```

**`accounts_reports/templates/accounts_reports/partials/_acchead_options.html`**

```html
{% for head in acc_heads %}
<option value="{{ head.id }}">{{ head }}</option>
{% endfor %}
```

**`accounts_reports/templates/accounts_reports/partials/_supplier_autocomplete_suggestions.html`**

```html
{% if suggestions %}
    <ul class="divide-y divide-gray-200">
        {% for suggestion in suggestions %}
        <li class="p-2 cursor-pointer hover:bg-gray-100" @click="selectSupplier('{{ suggestion.name }}', {{ suggestion.id }})">
            {{ suggestion.name }}
        </li>
        {% endfor %}
    </ul>
{% else %}
    <div class="p-2 text-gray-500">No suggestions</div>
{% endif %}
```

#### 4.5 URLs

Define URL patterns for the main search view and the HTMX endpoints.

**`accounts_reports/urls.py`**

```python
from django.urls import path
from .views import SearchView, AccHeadOptionsView, SupplierAutoCompleteView, SearchDetailsView

app_name = 'accounts_reports' # Define app_name for namespacing

urlpatterns = [
    path('search/', SearchView.as_view(), name='search'),
    path('search/acc-heads/', AccHeadOptionsView.as_view(), name='acc_heads_options'),
    path('search/suppliers-autocomplete/', SupplierAutoCompleteView.as_view(), name='suppliers_autocomplete'),
    # Dummy URL for the search results page that the form redirects to
    path('search-details/', SearchDetailsView.as_view(), name='search_details'),
]
```

#### 4.6 Tests

Comprehensive unit tests for models and form validation, and integration tests for views including HTMX interactions.

**`accounts_reports/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Supplier, AccHead
from .forms import SearchForm
from datetime import date

class ModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for Supplier
        Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier A')
        Supplier.objects.create(supplier_id=2, supplier_name='Another Supplier B')
        
        # Create test data for AccHead
        AccHead.objects.create(id=101, symbol='LBR', description='Labour Charge', category='Labour')
        AccHead.objects.create(id=102, symbol='MAT', description='Material Cost', category='With Material')
        AccHead.objects.create(id=103, symbol='EXP', description='Office Expenses', category='Expenses')
        AccHead.objects.create(id=104, symbol='SRV', description='IT Services', category='Service Provider')
        AccHead.objects.create(id=105, symbol='LBR2', description='Another Labour', category='Labour')

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(supplier_id=1)
        self.assertEqual(supplier.supplier_name, 'Test Supplier A')
        self.assertEqual(str(supplier), 'Test Supplier A')

    def test_acchead_creation(self):
        acchead = AccHead.objects.get(id=101)
        self.assertEqual(acchead.description, 'Labour Charge')
        self.assertEqual(acchead.category, 'Labour')
        self.assertEqual(str(acchead), '[LBR] Labour Charge')

    def test_get_supplier_id_by_name(self):
        supplier_id = Supplier.get_supplier_id_by_name('Test Supplier A')
        self.assertEqual(supplier_id, 1)
        supplier_id_case_insensitive = Supplier.get_supplier_id_by_name('test supplier a')
        self.assertEqual(supplier_id_case_insensitive, 1)
        supplier_id_not_found = Supplier.get_supplier_id_by_name('NonExistent')
        self.assertIsNone(supplier_id_not_found)

    def test_get_heads_by_category(self):
        labour_heads = AccHead.get_heads_by_category('Labour')
        self.assertEqual(len(labour_heads), 2)
        self.assertIn(AccHead.objects.get(id=101), labour_heads)
        self.assertIn(AccHead.objects.get(id=105), labour_heads)
        
        material_heads = AccHead.get_heads_by_category('With Material')
        self.assertEqual(len(material_heads), 1)
        self.assertIn(AccHead.objects.get(id=102), material_heads)


class SearchFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        AccHead.objects.create(id=101, symbol='LBR', description='Labour Charge', category='Labour')
        AccHead.objects.create(id=102, symbol='MAT', description='Material Cost', category='With Material')

    def test_form_valid_data(self):
        form_data = {
            'option': '1', 'number': 'GQN123',
            'report_for': '1',
            'item_code_checked': True, 'item_code': 'ITEM001',
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': True, 'supplier_name': 'Test Supplier', 'supplier_id': '1', # supplier_id typically populated by JS
            'acc_head_checked': True, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '01-01-2023', 'to_date': '31-01-2023',
            'status': '1',
        }
        form = SearchForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(form.cleaned_data['supplier_id'], 1) # Ensure ID is resolved

    def test_form_invalid_date_range(self):
        form_data = {
            'option': '0', 'number': '',
            'report_for': '1',
            'item_code_checked': False, 'item_code': '',
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': False, 'supplier_name': '',
            'acc_head_checked': False, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '31-01-2023', 'to_date': '01-01-2023', # Invalid range
            'status': '1',
        }
        form = SearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('From date must be less than or equal to To date.', form.errors['to_date'])

    def test_form_partial_date_entry(self):
        form_data = {
            'option': '0', 'number': '',
            'report_for': '1',
            'item_code_checked': False, 'item_code': '',
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': False, 'supplier_name': '',
            'acc_head_checked': False, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '01-01-2023', 'to_date': '', # Missing 'to_date'
            'status': '1',
        }
        form = SearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Both From and To dates must be provided if one is entered.', form.errors['from_date'])
        self.assertIn('Both From and To dates must be provided if one is entered.', form.errors['to_date'])

    def test_form_required_field_if_checked(self):
        form_data = {
            'option': '0', 'number': '',
            'report_for': '1',
            'item_code_checked': True, 'item_code': '', # Item Code checked but empty
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': False, 'supplier_name': '',
            'acc_head_checked': False, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '', 'to_date': '',
            'status': '1',
        }
        form = SearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Item Code is required if checkbox is checked.', form.errors['item_code'])
        
    def test_form_number_required_if_not_all(self):
        form_data = {
            'option': '1', 'number': '', # GQN No selected but number empty
            'report_for': '1',
            'item_code_checked': False, 'item_code': '',
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': False, 'supplier_name': '',
            'acc_head_checked': False, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '', 'to_date': '',
            'status': '1',
        }
        form = SearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Enter valid GQN No.', form.errors['number'])

    def test_form_supplier_name_validation(self):
        form_data = {
            'option': '0', 'number': '',
            'report_for': '1',
            'item_code_checked': False, 'item_code': '',
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': True, 'supplier_name': 'Non-existent Supplier', # Invalid supplier
            'acc_head_checked': False, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '', 'to_date': '',
            'status': '1',
        }
        form = SearchForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Enter a valid Supplier Name.', form.errors['supplier_name'])


class SearchViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier A')
        AccHead.objects.create(id=101, symbol='LBR', description='Labour Charge', category='Labour')
        AccHead.objects.create(id=102, symbol='MAT', description='Material Cost', category='With Material')

    def setUp(self):
        self.client = Client()

    def test_search_view_get(self):
        response = self.client.get(reverse('accounts_reports:search'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts_reports/search/search_form.html')
        self.assertIsInstance(response.context['form'], SearchForm)

    def test_search_view_valid_submit_redirects(self):
        form_data = {
            'option': '0', 'number': '',
            'report_for': '1',
            'item_code_checked': False, 'item_code': '',
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': False, 'supplier_name': '', 'supplier_id': '',
            'acc_head_checked': False, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '', 'to_date': '',
            'status': '1',
        }
        response = self.client.get(reverse('accounts_reports:search'), form_data)
        self.assertEqual(response.status_code, 302) # Should be a redirect
        self.assertRedirects(response, reverse('accounts_reports:search_details') + '?type=0&No=&RAd2=1&RAd=1&Code=&WONo=&SupId=&accval=101&FDate=&TDate=')

    def test_search_view_invalid_submit_renders_form_with_errors(self):
        form_data = {
            'option': '1', 'number': '', # Invalid: option 1 (GQN No) needs a number
            'report_for': '1',
            'item_code_checked': False, 'item_code': '',
            'wo_no_checked': False, 'wo_no': '',
            'supplier_checked': False, 'supplier_name': '', 'supplier_id': '',
            'acc_head_checked': False, 'acc_head_category': 'Labour', 'acc_head_dropdown': '101',
            'from_date': '', 'to_date': '',
            'status': '1',
        }
        response = self.client.get(reverse('accounts_reports:search'), form_data)
        self.assertEqual(response.status_code, 200) # Stays on the same page
        self.assertIn('Enter valid GQN No.', response.content.decode()) # Check for error message in rendered content


class HtmxViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier A')
        Supplier.objects.create(supplier_id=2, supplier_name='Another Supplier B')
        AccHead.objects.create(id=101, symbol='LBR', description='Labour Charge', category='Labour')
        AccHead.objects.create(id=102, symbol='MAT', description='Material Cost', category='With Material')
        AccHead.objects.create(id=103, symbol='EXP', description='Office Expenses', category='Expenses')
        AccHead.objects.create(id=104, symbol='SRV', description='IT Services', category='Service Provider')

    def setUp(self):
        self.client = Client()

    def test_acc_head_options_view(self):
        response = self.client.get(reverse('accounts_reports:acc_heads_options') + '?category=Labour')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="101">[LBR] Labour Charge</option>')
        self.assertNotContains(response, '<option value="102">[MAT] Material Cost</option>') # Should only contain Labour

    def test_supplier_autocomplete_view_with_query(self):
        response = self.client.get(reverse('accounts_reports:suppliers_autocomplete') + '?q=test')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Supplier A')
        self.assertNotContains(response, 'Another Supplier B')
        
    def test_supplier_autocomplete_view_no_query(self):
        response = self.client.get(reverse('accounts_reports:suppliers_autocomplete') + '?q=')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions')
        self.assertNotContains(response, 'Test Supplier A')
```

---

### Step 5: HTMX and Alpine.js Integration

The template `search_form.html` demonstrates the integration:

*   **Alpine.js (`x-data`, `x-model`, `x-bind:disabled`, `x-on:change`, `x-ref`):**
    *   Manages the state of checkboxes (`item_code_checked`, `wo_no_checked`, `supplier_checked`, `acc_head_checked`).
    *   Conditionally enables/disables text input fields based on checkbox states.
    *   Handles the `Drpoption` logic (enabling/disabling specific items) and `txtNo` enabled state based on `search_option` and `report_type`.
    *   Controls the visibility of supplier autocomplete suggestions (`x-show`).
    *   Facilitates selecting a supplier from suggestions and populating the hidden `supplier_id` field.
*   **HTMX (`hx-get`, `hx-trigger`, `hx-target`, `hx-swap`):**
    *   **Account Head Dropdown:** The `acc_head_category` radio buttons have `hx-get` to `{% url 'accounts_reports:acc_heads_options' %}`. When a radio button changes, HTMX sends a GET request with the `category` as a parameter. The response (a partial template containing new `<option>` tags) is then swapped into the `id_acc_head_dropdown` element.
    *   **Supplier Autocomplete:** The `supplier_name` input has `hx-get` to `{% url 'accounts_reports:suppliers_autocomplete' %}`. It triggers on `keyup changed delay:500ms`, targeting `#supplier-suggestions` to swap in the list of matching suppliers. Alpine.js then handles showing/hiding this suggestion list based on input focus and selection.

This setup ensures:
*   All dynamic interactions happen without full page reloads, providing a smooth user experience.
*   UI state management is handled efficiently client-side by Alpine.js.
*   Server-side data fetches for dynamic dropdowns and autocomplete are handled by HTMX, keeping Django views focused.

---

### Final Notes for Business Stakeholders:

This modernization plan transforms your ASP.NET Search page into a highly efficient and modern Django application. By leveraging cutting-edge technologies like HTMX and Alpine.js, we are building a user interface that is not only visually appealing but also incredibly responsive. The "Fat Model, Thin View" approach ensures that your business logic is centralized and easily maintainable, leading to a more robust and future-proof system.

The automated conversion focuses on systematically translating the existing functionality, ensuring that your core search capabilities are preserved while significantly upgrading the underlying technology stack. This strategic move will set the foundation for a more agile development process, lower operational costs, and unlock future innovation.