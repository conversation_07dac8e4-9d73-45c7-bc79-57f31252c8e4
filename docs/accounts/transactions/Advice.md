## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This plan outlines the strategic migration of your legacy ASP.NET application to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI for execution, and focuses on delivering a robust, maintainable, and scalable system with enhanced user experience.

### Business Benefits of Django Modernization:

*   **Reduced Costs**: By transitioning away from proprietary technologies and reducing manual coding through automation, we lower licensing fees and development overhead.
*   **Improved Scalability**: Django's architecture and Python's ecosystem provide a highly scalable platform that can grow with your business needs.
*   **Enhanced User Experience**: The adoption of HTMX and Alpine.js ensures a highly interactive and responsive front-end, similar to Single Page Applications (SPAs) but with significantly less complexity, leading to greater user satisfaction.
*   **Increased Development Agility**: Django's "batteries included" philosophy and clear structure, combined with Python's rapid development capabilities, enable quicker iteration and deployment of new features.
*   **Future-Proofing**: Moving to an open-source, widely supported framework like Django mitigates the risks associated with aging technologies and ensures long-term viability and access to a vast developer community.
*   **Simplified Maintenance**: Fat models and thin views enforce a strict separation of concerns, making the codebase easier to understand, debug, and extend.
*   **Robustness**: Comprehensive test coverage ensures high code quality and reduces the likelihood of regressions, leading to a more reliable application.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we identify several core transactional tables and their supporting lookup tables. The migration will focus on bringing these schemas into Django's ORM, mapping them to existing tables using `managed = False` as per the re-platforming strategy.

**Identified Tables (Simplified for key examples):**

1.  **`tblACC_Advice_Payment_Master` (Django Model: `PaymentAdviceHeader`)**
    *   `Id` (PK, int)
    *   `SysDate` (date)
    *   `SysTime` (time)
    *   `SessionId` (varchar, maps to user identifier)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `ADNo` (varchar, e.g., "0001")
    *   `Type` (int, e.g., 1=Advance, 2=Salary, 3=Others, 4=Creditors)
    *   `PayTo` (varchar, stores code/ID of Payee)
    *   `ChequeNo` (varchar)
    *   `ChequeDate` (date)
    *   `PayAt` (varchar)
    *   `Bank` (int, FK to `tblACC_Bank.Id`)
    *   `ECSType` (int)

2.  **`tblACC_Advice_Payment_Details` (Django Model: `PaymentAdviceDetail`)**
    *   `Id` (PK, int)
    *   `MId` (int, FK to `tblACC_Advice_Payment_Master.Id`)
    *   `ProformaInvNo` (varchar)
    *   `InvDate` (date)
    *   `PONo` (varchar)
    *   `Particular` (varchar)
    *   `Amount` (float)
    *   `PVEVNO` (varchar)
    *   `BillAgainst` (varchar)
    *   `WONo` (varchar)
    *   `BG` (int, FK to `BusinessGroup.Id`)
    *   `WithinGroup` (varchar)

3.  **`tblACC_Advice_Payment_Temp` (Django Model: `PaymentAdviceTemporaryItem`)**
    *   `Id` (PK, int)
    *   `ProformaInvNo` (varchar)
    *   `InvDate` (date)
    *   `PONo` (varchar)
    *   `Amount` (float)
    *   `Particular` (varchar)
    *   `CompId` (int)
    *   `SessionId` (varchar)
    *   `Types` (int, 1=Advance, 2=Salary, 3=Others)
    *   `WONo` (varchar)
    *   `BG` (int, FK to `BusinessGroup.Id`)
    *   `WithinGroup` (varchar)

4.  **`tblACC_Advice_Received_Masters` (Django Model: `ReceiptAdviceMaster`)**
    *   `Id` (PK, int)
    *   `SysDate` (date)
    *   `SysTime` (time)
    *   `SessionId` (varchar)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `ADRNo` (varchar, e.g., "0001")
    *   `Types` (int, FK to `tblACC_ReceiptAgainst.Id`)
    *   `ReceivedFrom` (varchar)
    *   `InvoiceNo` (varchar)
    *   `ChequeNo` (varchar)
    *   `ChequeDate` (date)
    *   `ChequeReceivedBy` (varchar)
    *   `BankName` (varchar)
    *   `BankAccNo` (varchar)
    *   `ChequeClearanceDate` (date)
    *   `Narration` (varchar)
    *   `Amount` (float)

5.  **Lookup Tables:**
    *   `tblACC_Bank` (Django Model: `Bank`)
    *   `BusinessGroup` (Django Model: `BusinessGroup`)
    *   `tblHR_OfficeStaff` (Django Model: `Employee`)
    *   `SD_Cust_master` (Django Model: `Customer`)
    *   `tblMM_Supplier_master` (Django Model: `Supplier`)
    *   `tblACC_ReceiptAgainst` (Django Model: `ReceiptType`)
    *   `tblFinancial_master` (Django Model: `FinancialYear`)

#### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic.

The ASP.NET application provides a comprehensive "Advice" module, handling both payments and receipts across multiple categories (Advance, Salary, Creditors, Others). The core functionalities identified are:

*   **Payment Management (Advance, Salary, Others):**
    *   **Temporary Item Handling**: A temporary grid allows users to add, edit, and delete individual payment line items (`tblACC_Advice_Payment_Temp`) before finalizing the payment. This simulates a "shopping cart" pattern.
    *   **Payment Finalization**: Upon "Proceed," temporary items are consolidated, a unique Advice Number (`ADNo`) is generated, and data is moved to permanent master (`tblACC_Advice_Payment_Master`) and detail (`tblACC_Advice_Payment_Details`) tables.
    *   **Dynamic Payee Selection**: Auto-completion and dropdowns allow selecting employees, customers, or suppliers as payees, with different internal 'types' dictating the lookup source.
    *   **WO No/Business Group Logic**: For "Others" payments, conditional logic determines if a Work Order number or a Business Group is applicable.
*   **Creditor Payment Management:**
    *   **Creditor Search**: Users can search for outstanding bills by supplier.
    *   **Bill Selection & Temporary Addition**: Bills are selected from a list and added to a temporary creditor payment grid, calculating balances.
    *   **Creditor Payment Finalization**: Similar to other payments, temporary creditor payments are processed, an `ADNo` is generated, and data is saved to master/details tables.
*   **Receipt Management:**
    *   **Receipt Entry**: Users can input details for receipts against various types (e.g., sales, advance).
    *   **Receipt Finalization**: A unique Receipt Advice Number (`ADRNo`) is generated, and the receipt data is stored in the permanent `tblACC_Advice_Received_Masters` table.
    *   **Receipt Listing & Deletion**: Existing receipts can be viewed and deleted.
*   **Common Functionality:**
    *   **Session Management**: User, company, and financial year context are maintained via session variables.
    *   **Unique ID Generation**: Sequential advice numbers (`ADNo`, `ADRNo`) are generated.
    *   **Data Validation**: Extensive input validation for dates, numbers, and payee existence.
    *   **Database Interactions**: Direct SQL `INSERT`, `UPDATE`, `DELETE`, `SELECT` commands are used, suggesting a simple data access layer.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

The ASP.NET user interface is built around a tabbed interface (`TabContainer`) containing several forms and `GridView` components.

*   **Navigation**:
    *   Top-level tabs: "Payment" and "Receipt".
    *   Sub-tabs within "Payment": "Advance", "Creditors", "Salary", "Others".
*   **Input Fields**:
    *   Text inputs for `Cheque No`, `Cheque Date`, `Pay At`, `Invoice No`, `Amount`, `Particulars`, `Narration`, `Bank Name`, `Bank Account No`.
    *   Dropdowns for `Pay To` type (Employee/Customer/Supplier), `Drawn On` (Bank), `WO No/BG Group` selection, `Receipt Against`.
    *   Date pickers (CalendarExtender) for date fields.
    *   Autocomplete extenders for `Pay To` fields.
*   **Data Grids (`GridView`)**:
    *   `GridView1` (Advance Payment): Displays temporary advance items, with inline add (footer row), edit, and delete.
    *   `GridView2` (Salary Payment): Displays temporary salary items, with inline add, edit, and delete.
    *   `GridView3` (Others Payment): Displays temporary "Others" items, with inline add, edit, and delete; includes dynamic WO No/BG input.
    *   `GridView4` (Creditors Search): Displays search results for outstanding bills. Allows selection via checkbox and adding to a temporary grid.
    *   `GridView5` (Creditors Temp): Displays temporary creditor payment items, with delete functionality.
    *   `GridView6` (Receipt List): Displays a list of submitted receipts, with delete functionality.
*   **Action Buttons**:
    *   "Proceed": Finalizes payments/receipts, moving data from temporary to permanent tables.
    *   "Insert", "Add": Adds new items to temporary grids.
    *   "Edit", "Update", "Delete", "Cancel": Standard CRUD actions within grid rows.
    *   "Search", "Refresh": For creditor bill lookup.
    *   "Submit": Finalizes receipts.
*   **Validation Display**: Asterisks (*) next to required fields, and alert pop-ups for invalid data or missing records.

This detailed breakdown will guide the creation of precise Django models, forms, class-based views, HTMX interactions, and Jinja2 templates, ensuring a seamless migration while leveraging modern web practices.

---

#### Step 4: Generate Django Code

We will create a Django application named `account` to encapsulate this module's functionality.

**Project Structure (`account/` directory):**

```
account/
├── migrations/
├── templates/
│   └── account/
│       └── base.html (extended from core/base.html, but NOT provided here)
│       └── paymentadviceheader/
│           ├── list.html
│           ├── _paymentadviceheader_table.html
│           ├── _paymentadvicetemporaryitem_form.html (partial for advance item add/edit)
│           └── _paymentadvicetemporaryitem_table.html (partial for advance item list)
│       └── receiptadvicemaster/
│           ├── list.html
│           ├── _receiptadvicemaster_form.html (partial for receipt add)
│           └── _receiptadvicemaster_table.html (partial for receipt list)
│       └── common/
│           └── autocomplete_list.html (for autocomplete suggestions)
├── __init__.py
├── admin.py
├── apps.py
├── models.py
├── forms.py
├── urls.py
├── views.py
└── tests.py
```

---

##### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**
Models are defined with `managed = False` and `db_table` to connect to the existing database tables. Business logic is embedded directly within the models as methods or custom managers to ensure a "fat model" approach.

```python
# account/models.py
from django.db import models
from django.utils import timezone
from django.db.models import Max, Sum
from django.core.exceptions import ValidationError

# --- Lookup / Supporting Models ---

class Bank(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Bank'
        verbose_name = 'Bank'
        verbose_name_plural = 'Banks'

    def __str__(self):
        return self.name or f"Bank {self.id}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or f"Business Group {self.id}"

class Employee(models.Model):
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employeename or f"Employee {self.empid}"

class Customer(models.Model):
    customerid = models.IntegerField(db_column='CustomerId', primary_key=True)
    customername = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customername or f"Customer {self.customerid}"

class Supplier(models.Model):
    supplierid = models.IntegerField(db_column='SupplierId', primary_key=True)
    suppliername = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.suppliername or f"Supplier {self.supplierid}"

class ReceiptType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_ReceiptAgainst'
        verbose_name = 'Receipt Type'
        verbose_name_plural = 'Receipt Types'

    def __str__(self):
        return self.description or f"Receipt Type {self.id}"

class FinancialYear(models.Model):
    finyearid = models.IntegerField(db_column='FinYearId', primary_key=True)
    finyear = models.CharField(db_column='FinYear', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.finyear or f"Financial Year {self.finyearid}"

# --- Main Transactional Models ---

class PaymentAdviceHeaderManager(models.Manager):
    def get_next_ad_no(self, comp_id, fin_year_id):
        # Generates the next ADNo (e.g., '0001') based on the last one for the company and financial year
        last_ad_no = self.filter(comp_id=comp_id, fin_year_id=fin_year_id).aggregate(Max('ad_no'))['ad_no__max']
        if last_ad_no:
            try:
                next_num = int(last_ad_no) + 1
            except ValueError:
                next_num = 1
        else:
            next_num = 1
        return f"{next_num:04d}"

class PaymentAdviceHeader(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    ad_no = models.CharField(db_column='ADNo', max_length=10, blank=True, null=True) # e.g., '0001'
    type = models.IntegerField(db_column='Type') # 1=Advance, 2=Salary, 3=Others, 4=Creditors
    pay_to = models.CharField(db_column='PayTo', max_length=255, blank=True, null=True) # Stores code/ID
    cheque_no = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    cheque_date = models.DateField(db_column='ChequeDate', blank=True, null=True)
    pay_at = models.CharField(db_column='PayAt', max_length=255, blank=True, null=True)
    bank = models.ForeignKey(Bank, on_delete=models.SET_NULL, db_column='Bank', null=True, blank=True)
    ecs_type = models.IntegerField(db_column='ECSType') # Corresponds to drptype.SelectedValue

    objects = PaymentAdviceHeaderManager()

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Payment_Master'
        verbose_name = 'Payment Advice Header'
        verbose_name_plural = 'Payment Advice Headers'

    def __str__(self):
        return f"Payment Advice {self.ad_no} ({self.get_type_display()})"

    def get_type_display(self):
        types = {
            1: 'Advance',
            2: 'Salary',
            3: 'Others',
            4: 'Creditors',
        }
        return types.get(self.type, 'Unknown')

    @classmethod
    def get_payee_instance(cls, payee_code, ecs_type, comp_id):
        # This method simulates fun.chkEmpCustSupplierCode and related lookups
        # In a real app, this would involve more robust parsing and lookups
        # For simplicity, assuming payee_code might include "[ID]" part for lookup
        # Extract ID from 'Name [ID]' format
        import re
        match = re.search(r'\[(\d+)\]', payee_code)
        if match:
            entity_id = int(match.group(1))
            if ecs_type == 1: # Employee
                return Employee.objects.filter(empid=entity_id).first()
            elif ecs_type == 2: # Customer
                return Customer.objects.filter(customerid=entity_id).first()
            elif ecs_type == 3: # Supplier
                return Supplier.objects.filter(supplierid=entity_id).first()
        return None

class PaymentAdviceDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(PaymentAdviceHeader, on_delete=models.CASCADE, db_column='MId')
    proforma_inv_no = models.CharField(db_column='ProformaInvNo', max_length=255, blank=True, null=True)
    inv_date = models.DateField(db_column='InvDate', blank=True, null=True)
    pono = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    particular = models.CharField(db_column='Particular', max_length=500, blank=True, null=True)
    amount = models.FloatField(db_column='Amount')
    # Fields for Creditors/Others type payments
    pvevno = models.CharField(db_column='PVEVNO', max_length=255, blank=True, null=True)
    bill_against = models.CharField(db_column='BillAgainst', max_length=255, blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    bg = models.ForeignKey(BusinessGroup, on_delete=models.SET_NULL, db_column='BG', null=True, blank=True)
    within_group = models.CharField(db_column='WithinGroup', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Payment_Details'
        verbose_name = 'Payment Advice Detail'
        verbose_name_plural = 'Payment Advice Details'

    def __str__(self):
        return f"Detail for {self.mid.ad_no}: {self.particular} - {self.amount}"

class PaymentAdviceTemporaryItemManager(models.Manager):
    def clear_session_data(self, session_id, comp_id, item_type=None):
        # Clears temporary items for the current session and company
        queryset = self.filter(session_id=session_id, comp_id=comp_id)
        if item_type is not None:
            queryset = queryset.filter(types=item_type)
        queryset.delete()

    def get_items_for_session(self, session_id, comp_id, item_type=None):
        queryset = self.filter(session_id=session_id, comp_id=comp_id)
        if item_type is not None:
            queryset = queryset.filter(types=item_type)
        return queryset

class PaymentAdviceTemporaryItem(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    proforma_inv_no = models.CharField(db_column='ProformaInvNo', max_length=255, blank=True, null=True)
    inv_date = models.DateField(db_column='InvDate', blank=True, null=True)
    pono = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    amount = models.FloatField(db_column='Amount')
    particular = models.CharField(db_column='Particular', max_length=500, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    types = models.IntegerField(db_column='Types') # 1=Advance, 2=Salary, 3=Others
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    bg = models.ForeignKey(BusinessGroup, on_delete=models.SET_NULL, db_column='BG', null=True, blank=True)
    within_group = models.CharField(db_column='WithinGroup', max_length=255, blank=True, null=True)

    objects = PaymentAdviceTemporaryItemManager()

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Payment_Temp'
        verbose_name = 'Payment Advice Temporary Item'
        verbose_name_plural = 'Payment Advice Temporary Items'

    def __str__(self):
        return f"Temp Item {self.id} for {self.session_id} - Type {self.types}"

    def clean(self):
        # Example of model-level validation: Ensure amount is positive
        if self.amount is not None and self.amount <= 0:
            raise ValidationError({'amount': 'Amount must be positive.'})
        # Additional complex validation (e.g., CheckValidWONo, NumberValidationQty)
        # would go here, possibly calling helper methods.

class ReceiptAdviceMasterManager(models.Manager):
    def get_next_adr_no(self, comp_id, fin_year_id):
        # Generates the next ADRNo (e.g., '0001')
        last_adr_no = self.filter(comp_id=comp_id, fin_year_id=fin_year_id).aggregate(Max('adr_no'))['adr_no__max']
        if last_adr_no:
            try:
                next_num = int(last_adr_no) + 1
            except ValueError:
                next_num = 1
        else:
            next_num = 1
        return f"{next_num:04d}"

class ReceiptAdviceMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    adr_no = models.CharField(db_column='ADRNo', max_length=10, blank=True, null=True) # e.g., '0001'
    types = models.ForeignKey(ReceiptType, on_delete=models.SET_NULL, db_column='Types', null=True, blank=True)
    received_from = models.CharField(db_column='ReceivedFrom', max_length=255, blank=True, null=True)
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=255, blank=True, null=True)
    cheque_no = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    cheque_date = models.DateField(db_column='ChequeDate', blank=True, null=True)
    cheque_received_by = models.CharField(db_column='ChequeReceivedBy', max_length=255, blank=True, null=True) # Stores code/ID
    bank_name = models.CharField(db_column='BankName', max_length=255, blank=True, null=True)
    bank_acc_no = models.CharField(db_column='BankAccNo', max_length=50, blank=True, null=True)
    cheque_clearance_date = models.DateField(db_column='ChequeClearanceDate', blank=True, null=True)
    narration = models.TextField(db_column='Narration', blank=True, null=True)
    amount = models.FloatField(db_column='Amount')

    objects = ReceiptAdviceMasterManager()

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Received_Masters'
        verbose_name = 'Receipt Advice Master'
        verbose_name_plural = 'Receipt Advice Masters'

    def __str__(self):
        return f"Receipt Advice {self.adr_no} from {self.received_from}"

    def clean(self):
        # Example of model-level validation: Cheque date vs Clearance date
        if self.cheque_date and self.cheque_clearance_date and self.cheque_date > self.cheque_clearance_date:
            raise ValidationError({
                'cheque_date': 'Cheque date cannot be after clearance date.',
                'cheque_clearance_date': 'Clearance date cannot be before cheque date.'
            })
        if self.amount is not None and self.amount <= 0:
            raise ValidationError({'amount': 'Amount must be positive.'})

```

##### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
ModelForms are used for easy mapping to models. Widgets are added for styling with Tailwind CSS and for integrating date pickers (`type="date"`) or specialized HTMX autocomplete fields.

```python
# account/forms.py
from django import forms
from .models import PaymentAdviceHeader, PaymentAdviceTemporaryItem, ReceiptAdviceMaster, Bank, ReceiptType

# Utility for common widget attributes
COMMON_WIDGET_ATTRS = {
    'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
}

class PaymentAdviceHeaderForm(forms.ModelForm):
    pay_to_name = forms.CharField(label='Pay To', max_length=255, required=True,
                                  widget=forms.TextInput(attrs={**COMMON_WIDGET_ATTRS, 'hx-get': '/account/autocomplete-payee/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#payee-suggestions', 'hx-swap': 'innerHTML'}))
    
    pay_to_type = forms.ChoiceField(label='Pay To Type', choices=[
        ('0', 'Select'), ('1', 'Employee'), ('2', 'Customer'), ('3', 'Supplier')
    ], initial='0', widget=forms.Select(attrs={**COMMON_WIDGET_ATTRS, 'hx-post': '/account/set-payee-type/', 'hx-trigger': 'change', 'hx-target': 'this', 'hx-swap': 'none', 'hx-vals': 'js:{payee_type: event.target.value}', '_': 'on hx:response set #pay_to_name.value = ""'}))

    class Meta:
        model = PaymentAdviceHeader
        fields = ['pay_to_type', 'pay_to_name', 'cheque_no', 'cheque_date', 'pay_at', 'bank']
        widgets = {
            'cheque_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'cheque_date': forms.DateInput(attrs={**COMMON_WIDGET_ATTRS, 'type': 'date'}),
            'pay_at': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'bank': forms.Select(attrs=COMMON_WIDGET_ATTRS),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['bank'].queryset = Bank.objects.all()
        # Initial value for pay_to_name if instance exists
        if self.instance.pk:
            self.fields['pay_to_name'].initial = self.instance.pay_to # assuming pay_to stores the full "Name [ID]" string
            self.fields['pay_to_type'].initial = self.instance.ecs_type
        
        # Disable pay_to_type if editing existing, or handle with JS/Alpine.js
        # self.fields['pay_to_type'].widget.attrs['disabled'] = 'disabled' if self.instance.pk else None

    def clean(self):
        cleaned_data = super().clean()
        # Perform additional validation
        return cleaned_data

class PaymentAdviceTemporaryItemForm(forms.ModelForm):
    class Meta:
        model = PaymentAdviceTemporaryItem
        fields = ['proforma_inv_no', 'inv_date', 'pono', 'particular', 'amount']
        widgets = {
            'proforma_inv_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'inv_date': forms.DateInput(attrs={**COMMON_WIDGET_ATTRS, 'type': 'date'}),
            'pono': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'particular': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'amount': forms.NumberInput(attrs=COMMON_WIDGET_ATTRS),
        }
    
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be positive.")
        return amount

class ReceiptAdviceMasterForm(forms.ModelForm):
    class Meta:
        model = ReceiptAdviceMaster
        fields = [
            'types', 'received_from', 'invoice_no', 'amount', 'cheque_no', 
            'cheque_date', 'cheque_received_by', 'bank_name', 'bank_acc_no', 
            'cheque_clearance_date', 'narration'
        ]
        widgets = {
            'types': forms.Select(attrs=COMMON_WIDGET_ATTRS),
            'received_from': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'invoice_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'amount': forms.NumberInput(attrs=COMMON_WIDGET_ATTRS),
            'cheque_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'cheque_date': forms.DateInput(attrs={**COMMON_WIDGET_ATTRS, 'type': 'date'}),
            'cheque_received_by': forms.TextInput(attrs={**COMMON_WIDGET_ATTRS, 'hx-get': '/account/autocomplete-employee/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#employee-suggestions', 'hx-swap': 'innerHTML'}),
            'bank_name': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'bank_acc_no': forms.TextInput(attrs=COMMON_WIDGET_ATTRS),
            'cheque_clearance_date': forms.DateInput(attrs={**COMMON_WIDGET_ATTRS, 'type': 'date'}),
            'narration': forms.Textarea(attrs={**COMMON_WIDGET_ATTRS, 'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['types'].queryset = ReceiptType.objects.all()

    def clean(self):
        cleaned_data = super().clean()
        cheque_date = cleaned_data.get('cheque_date')
        cheque_clearance_date = cleaned_data.get('cheque_clearance_date')
        amount = cleaned_data.get('amount')

        if cheque_date and cheque_clearance_date and cheque_date > cheque_clearance_date:
            self.add_error('cheque_date', 'Cheque date must be less than or equal to Clearance date.')
            self.add_error('cheque_clearance_date', 'Clearance date cannot be before cheque date.')
        
        if amount is not None and amount <= 0:
            self.add_error('amount', 'Amount must be positive.')
            
        return cleaned_data

```

##### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views are kept "thin" by delegating complex logic to models. HTMX requests are handled to return `status=204` with `HX-Trigger` headers for client-side updates without full page reloads.

```python
# account/views.py
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db import transaction
from django.db.models import Q # For autocomplete

from .models import (
    PaymentAdviceHeader, PaymentAdviceTemporaryItem, ReceiptAdviceMaster,
    Bank, BusinessGroup, Employee, Customer, Supplier, ReceiptType, FinancialYear
)
from .forms import (
    PaymentAdviceHeaderForm, PaymentAdviceTemporaryItemForm, ReceiptAdviceMasterForm
)

# Helper function to get context variables (CompId, FinYearId, SessionId)
# In a real application, these would come from the user's session/profile
def get_user_context(request):
    # Dummy values for demonstration. Replace with actual session logic.
    comp_id = getattr(request.user, 'company_id', 1)  
    fin_year_id = getattr(request.user, 'financial_year_id', 1) 
    session_id = request.user.username if request.user.is_authenticated else 'anonymous'
    return comp_id, fin_year_id, session_id

class AdviceDashboardView(TemplateView):
    # This view will serve the main advice.html with its tabs
    template_name = 'account/advice_dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize forms for the main dashboard elements if needed
        context['payment_header_form'] = PaymentAdviceHeaderForm()
        context['receipt_form'] = ReceiptAdviceMasterForm()
        return context

# --- Payment Advice (Advance Tab Example) ---
class PaymentAdviceTemporaryItemListView(ListView):
    model = PaymentAdviceTemporaryItem
    template_name = 'account/paymentadviceheader/_paymentadvicetemporaryitem_table.html'
    context_object_name = 'temporary_items'

    def get_queryset(self):
        comp_id, _, session_id = get_user_context(self.request)
        # Assuming type 1 for Advance Payment tab
        return PaymentAdviceTemporaryItem.objects.get_items_for_session(session_id, comp_id, item_type=1)

class PaymentAdviceTemporaryItemCreateUpdateView(CreateView):
    model = PaymentAdviceTemporaryItem
    form_class = PaymentAdviceTemporaryItemForm
    template_name = 'account/paymentadviceheader/_paymentadvicetemporaryitem_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Add' if not self.object else 'Edit'
        return context

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Set initial values for comp_id, session_id, types for creation
        if not self.object: # Only for new objects
            comp_id, _, session_id = get_user_context(self.request)
            kwargs['initial'] = {
                'comp_id': comp_id,
                'session_id': session_id,
                'types': 1 # Advance Payment type
            }
        return kwargs

    def form_valid(self, form):
        comp_id, _, session_id = get_user_context(self.request)
        form.instance.comp_id = comp_id
        form.instance.session_id = session_id
        form.instance.types = 1 # Set type to Advance Payment

        is_new = not form.instance.pk
        response = super().form_valid(form)
        
        messages.success(self.request, f"Item {'added' if is_new else 'updated'} successfully.")
        
        # HTMX response for partial update
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAdvanceTempList'
                }
            )
        return response # Fallback for non-HTMX requests (unlikely in this setup)

class PaymentAdviceTemporaryItemDeleteView(DeleteView):
    model = PaymentAdviceTemporaryItem
    template_name = 'account/paymentadviceheader/confirm_delete.html' # Reusable confirm delete template

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Ensure only current user's temp items can be deleted
        comp_id, _, session_id = get_user_context(request)
        if self.object.session_id != session_id or self.object.comp_id != comp_id:
            return HttpResponse(status=403, content="Unauthorized")
        
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAdvanceTempList'
                }
            )
        return response

class PaymentAdviceProceedView(TemplateView):
    # This view handles the "Proceed" logic for Advance Payment type
    template_name = 'account/message.html' # A simple message template, or just HTMX response

    def post(self, request, *args, **kwargs):
        comp_id, fin_year_id, session_id = get_user_context(request)
        header_form = PaymentAdviceHeaderForm(request.POST)

        if not header_form.is_valid():
            # If header form is invalid, re-render it with errors for HTMX
            return render(request, 'account/paymentadviceheader/_paymentadviceheader_form.html', {'form': header_form})
        
        temporary_items = PaymentAdviceTemporaryItem.objects.get_items_for_session(session_id, comp_id, item_type=1)
        if not temporary_items.exists():
            messages.error(request, 'No items found to proceed with payment.')
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'showMessage'} # Trigger a message display
            )
        
        with transaction.atomic():
            # Generate ADNo
            ad_no = PaymentAdviceHeader.objects.get_next_ad_no(comp_id, fin_year_id)

            # Create PaymentAdviceHeader
            header = header_form.save(commit=False)
            header.comp_id = comp_id
            header.fin_year_id = fin_year_id
            header.session_id = session_id
            header.ad_no = ad_no
            header.type = 1 # Advance Payment Type
            # Resolve pay_to_name to pay_to code if needed, and ecs_type from form
            # For this example, assuming pay_to stores the name from form
            header.pay_to = header_form.cleaned_data['pay_to_name'] 
            header.ecs_type = int(header_form.cleaned_data['pay_to_type'])
            header.save()

            # Move temporary items to PaymentAdviceDetail
            for temp_item in temporary_items:
                PaymentAdviceDetail.objects.create(
                    mid=header,
                    proforma_inv_no=temp_item.proforma_inv_no,
                    inv_date=temp_item.inv_date,
                    pono=temp_item.pono,
                    particular=temp_item.particular,
                    amount=temp_item.amount,
                    # No PVEVNO, BillAgainst, WONo, BG, WithinGroup for Advance type
                )
            
            # Clear temporary items for this type
            PaymentAdviceTemporaryItem.objects.clear_session_data(session_id, comp_id, item_type=1)
        
        messages.success(request, f'Payment Advice {ad_no} processed successfully!')
        # Trigger a full page reload or a specific component refresh for dashboard/header form
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshAdvanceTab reloadPage' # Clear the form and refresh grid
            }
        )

# --- Receipt Advice ---
class ReceiptAdviceMasterListView(ListView):
    model = ReceiptAdviceMaster
    template_name = 'account/receiptadvicemaster/_receiptadvicemaster_table.html'
    context_object_name = 'receipts'
    paginate_by = 15 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        comp_id, fin_year_id, _ = get_user_context(self.request)
        # Filter by company and financial year, similar to Loaddata
        return ReceiptAdviceMaster.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id).order_by('-id')

    def render_to_response(self, context, **response_kwargs):
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, context)
        return super().render_to_response(context, **response_kwargs)

class ReceiptAdviceMasterCreateView(CreateView):
    model = ReceiptAdviceMaster
    form_class = ReceiptAdviceMasterForm
    template_name = 'account/receiptadvicemaster/_receiptadvicemaster_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['action'] = 'Add' # For form title in template
        return context

    def form_valid(self, form):
        comp_id, fin_year_id, session_id = get_user_context(self.request)
        
        # Generate ADRNo
        adr_no = ReceiptAdviceMaster.objects.get_next_adr_no(comp_id, fin_year_id)

        receipt = form.save(commit=False)
        receipt.comp_id = comp_id
        receipt.fin_year_id = fin_year_id
        receipt.session_id = session_id
        receipt.adr_no = adr_no
        receipt.sys_date = timezone.now().date()
        receipt.sys_time = timezone.now().time()
        receipt.save()

        messages.success(self.request, f'Receipt Advice {adr_no} submitted successfully!')
        
        if self.request.headers.get('HX-Request'):
            # Return 204 to indicate success and no content swap, triggering client-side refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReceiptList'
                }
            )
        return super().form_valid(form)

class ReceiptAdviceMasterDeleteView(DeleteView):
    model = ReceiptAdviceMaster
    template_name = 'account/receiptadvicemaster/confirm_delete.html'

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        comp_id, _, _ = get_user_context(request)
        if self.object.comp_id != comp_id: # Basic security check
            return HttpResponse(status=403, content="Unauthorized")
            
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Receipt deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshReceiptList'
                }
            )
        return response

# --- Autocomplete Views ---
class AutocompletePayeeView(ListView):
    # This view powers the autocomplete for 'Pay To' fields (Employee, Customer, Supplier)
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        payee_type = request.session.get('current_payee_type', '0') # From drptype or drptypeOther

        results = []
        if payee_type == '1': # Employee
            queryset = Employee.objects.filter(employeename__icontains=prefix_text).order_by('employeename')[:15]
            results = [{'id': obj.empid, 'name': obj.employeename} for obj in queryset]
        elif payee_type == '2': # Customer
            queryset = Customer.objects.filter(customername__icontains=prefix_text).order_by('customername')[:15]
            results = [{'id': obj.customerid, 'name': obj.customername} for obj in queryset]
        elif payee_type == '3': # Supplier
            queryset = Supplier.objects.filter(suppliername__icontains=prefix_text).order_by('suppliername')[:15]
            results = [{'id': obj.supplierid, 'name': obj.suppliername} for obj in queryset]
        
        # Render a simple list for HTMX to swap into the suggestions div
        return render(request, 'account/common/autocomplete_list.html', {'results': results})

class AutocompleteEmployeeView(ListView):
    # This view powers the autocomplete for 'Cheque Received By' (always Employee)
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        queryset = Employee.objects.filter(employeename__icontains=prefix_text).order_by('employeename')[:15]
        results = [{'id': obj.empid, 'name': obj.employeename} for obj in queryset]
        return render(request, 'account/common/autocomplete_list.html', {'results': results})

# HTMX endpoint to set the payee type in session
def set_payee_type(request):
    if request.method == 'POST' and request.headers.get('HX-Request'):
        payee_type = request.POST.get('payee_type', '0')
        request.session['current_payee_type'] = payee_type
        return HttpResponse(status=204) # No content, just set session
    return HttpResponse(status=400)
```

##### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will extend `core/base.html` (not provided here). HTMX attributes are used for dynamic content loading, form submissions, and modal management. DataTables are initialized for list views.

```html
<!-- account/templates/account/advice_dashboard.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-gray-800 mb-6">Advice Management</h1>

    <!-- Tab Container mimicking AjaxControlToolkit TabContainer -->
    <div x-data="{ activeTab: 'payment' }" class="bg-white shadow-md rounded-lg p-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="#" @click.prevent="activeTab = 'payment'" 
                   :class="activeTab === 'payment' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Payment
                </a>
                <a href="#" @click.prevent="activeTab = 'receipt'" 
                   :class="activeTab === 'receipt' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                   class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Receipt
                </a>
            </nav>
        </div>

        <div x-show="activeTab === 'payment'" class="py-4">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">Payment Advice</h2>

            <!-- Sub-tabs for Payment -->
            <div x-data="{ activeSubTab: 'advance' }" class="border rounded-md bg-gray-50">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-4 p-2" aria-label="Sub-Tabs">
                        <a href="#" @click.prevent="activeSubTab = 'advance'" 
                           :class="activeSubTab === 'advance' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-600 hover:bg-gray-100'"
                           class="py-2 px-3 rounded-md text-sm font-medium">Advance</a>
                        <a href="#" @click.prevent="activeSubTab = 'creditors'" 
                           :class="activeSubTab === 'creditors' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-600 hover:bg-gray-100'"
                           class="py-2 px-3 rounded-md text-sm font-medium">Creditors</a>
                        <a href="#" @click.prevent="activeSubTab = 'salary'" 
                           :class="activeSubTab === 'salary' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-600 hover:bg-gray-100'"
                           class="py-2 px-3 rounded-md text-sm font-medium">Salary</a>
                        <a href="#" @click.prevent="activeSubTab = 'others'" 
                           :class="activeSubTab === 'others' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-600 hover:bg-gray-100'"
                           class="py-2 px-3 rounded-md text-sm font-medium">Others</a>
                    </nav>
                </div>

                <div x-show="activeSubTab === 'advance'" class="p-4"
                     hx-trigger="load, refreshAdvanceTab from:body"
                     hx-get="{% url 'payment_advance_tab' %}"
                     hx-target="this" hx-swap="innerHTML">
                    <!-- Advance tab content will be loaded here via HTMX -->
                    <div class="text-center py-8">
                        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                        <p class="mt-2 text-gray-600">Loading Advance Payment tab...</p>
                    </div>
                </div>
                <!-- Other sub-tabs would be similar, loaded via HTMX -->
            </div>
        </div>

        <div x-show="activeTab === 'receipt'" class="py-4"
             hx-trigger="load, refreshReceiptTab from:body"
             hx-get="{% url 'receipt_tab' %}"
             hx-target="this" hx-swap="innerHTML">
            <!-- Receipt tab content will be loaded here via HTMX -->
            <div class="text-center py-8">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Receipt tab...</p>
            </div>
        </div>
    </div>
</div>

<!-- Global Modal -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     _="on click if event.target.id == 'modal' remove .is-active from me, set #modalContent.innerHTML = ''">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-3/4 overflow-y-auto"></div>
</div>

<!-- Toast messages -->
<div id="messages" class="fixed top-4 right-4 z-50 space-y-2">
    <!-- Messages will be injected here via HX-Trigger 'showMessage' -->
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize Alpine.js components after HTMX swaps new content
        if (event.detail.target.hasAttribute('x-data')) {
            Alpine.initTree(event.detail.target);
        }
    });

    // Handle messages triggered by HX-Trigger 'showMessage'
    document.body.addEventListener('showMessage', function(event) {
        const messageContainer = document.getElementById('messages');
        const messages = {{ messages|json_script:"django-messages" }};
        if (messages.innerHTML) { // Check if there are messages from Django
            const parsedMessages = JSON.parse(messages.innerHTML);
            parsedMessages.forEach(msg => {
                let typeClass = '';
                if (msg.tags.includes('success')) typeClass = 'bg-green-500';
                else if (msg.tags.includes('error')) typeClass = 'bg-red-500';
                else if (msg.tags.includes('warning')) typeClass = 'bg-yellow-500';
                else typeClass = 'bg-blue-500';

                const toast = document.createElement('div');
                toast.className = `${typeClass} text-white px-4 py-2 rounded shadow-lg flex items-center`;
                toast.innerHTML = `
                    <span>${msg.message}</span>
                    <button class="ml-auto focus:outline-none" onclick="this.parentElement.remove()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                `;
                messageContainer.appendChild(toast);
                setTimeout(() => toast.remove(), 5000); // Auto-remove after 5 seconds
            });
        }
    });
</script>
{% endblock %}
```

```html
<!-- account/templates/account/paymentadviceheader/advance_tab_content.html -->
<h3 class="text-lg font-semibold text-gray-700 mb-4">Advance Payment</h3>

<form hx-post="{% url 'payment_proceed_advance' %}" hx-target="#messages" hx-swap="beforeend"
      _="on hx:afterRequest if event.detail.xhr.status == 204 then call clearAdvancePaymentForm()" >
    {% csrf_token %}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ payment_header_form.pay_to_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Payment Against</label>
            {{ payment_header_form.pay_to_type }}
        </div>
        <div>
            <label for="{{ payment_header_form.pay_to_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Pay To</label>
            {{ payment_header_form.pay_to_name }}
            <div id="payee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded shadow-lg max-h-48 overflow-y-auto w-1/2"></div>
            {% if payment_header_form.pay_to_name.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_header_form.pay_to_name.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ payment_header_form.cheque_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Cheque No./ D.D.No.</label>
            {{ payment_header_form.cheque_no }}
            {% if payment_header_form.cheque_no.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_header_form.cheque_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ payment_header_form.cheque_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Cheque Date</label>
            {{ payment_header_form.cheque_date }}
            {% if payment_header_form.cheque_date.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_header_form.cheque_date.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ payment_header_form.bank.id_for_label }}" class="block text-sm font-medium text-gray-700">Drawn On</label>
            {{ payment_header_form.bank }}
            {% if payment_header_form.bank.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_header_form.bank.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ payment_header_form.pay_at.id_for_label }}" class="block text-sm font-medium text-gray-700">Payable At</label>
            {{ payment_header_form.pay_at }}
            {% if payment_header_form.pay_at.errors %}<p class="text-red-500 text-xs mt-1">{{ payment_header_form.pay_at.errors }}</p>{% endif %}
        </div>
    </div>
    
    <div class="mt-6">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Proceed
        </button>
    </div>
</form>

<hr class="my-6 border-gray-300">

<h3 class="text-lg font-semibold text-gray-700 mb-4">Advance Payment Items</h3>
<div id="advanceTempTableContainer"
     hx-trigger="load, refreshAdvanceTempList from:body"
     hx-get="{% url 'payment_advance_temp_list' %}"
     hx-target="this"
     hx-swap="innerHTML">
    <!-- Temporary items table loaded via HTMX -->
    <div class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading temporary items...</p>
    </div>
</div>

<script>
    // Alpine.js function to clear the header form fields
    function clearAdvancePaymentForm() {
        const form = document.querySelector('form[hx-post="{% url 'payment_proceed_advance' %}"]');
        if (form) {
            form.reset(); // Resets all form fields
            // Manually clear specific fields or reset dropdowns if needed
            form.querySelector('select[name="pay_to_type"]').value = '0';
        }
    }
</script>
```

```html
<!-- account/templates/account/paymentadviceheader/_paymentadvicetemporaryitem_table.html -->
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="advanceTempTable" class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="py-3 px-6">SN</th>
                <th scope="col" class="py-3 px-6">Proforma Inv No</th>
                <th scope="col" class="py-3 px-6">Date</th>
                <th scope="col" class="py-3 px-6">PO No</th>
                <th scope="col" class="py-3 px-6">Particulars</th>
                <th scope="col" class="py-3 px-6 text-right">Amount</th>
                <th scope="col" class="py-3 px-6 text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for item in temporary_items %}
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="py-4 px-6">{{ forloop.counter }}</td>
                <td class="py-4 px-6">{{ item.proforma_inv_no }}</td>
                <td class="py-4 px-6">{{ item.inv_date|date:"d-m-Y" }}</td>
                <td class="py-4 px-6">{{ item.pono }}</td>
                <td class="py-4 px-6">{{ item.particular }}</td>
                <td class="py-4 px-6 text-right">{{ item.amount|floatformat:2 }}</td>
                <td class="py-4 px-6 text-center">
                    <button class="font-medium text-blue-600 dark:text-blue-500 hover:underline mr-2"
                            hx-get="{% url 'payment_advance_temp_edit' item.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button class="font-medium text-red-600 dark:text-red-500 hover:underline"
                            hx-get="{% url 'payment_advance_temp_delete' item.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No temporary items added.</td>
            </tr>
            {% endfor %}
        </tbody>
        <tfoot>
            <tr class="bg-gray-50 dark:bg-gray-700">
                <td colspan="7" class="py-3 px-6 text-left">
                    <button class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded"
                            hx-get="{% url 'payment_advance_temp_add' %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                        Add New Item
                    </button>
                </td>
            </tr>
        </tfoot>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#advanceTempTable')) {
            $('#advanceTempTable').DataTable().destroy();
        }
        $('#advanceTempTable').DataTable({
            "paging": false,
            "searching": false,
            "info": false,
            "columnDefs": [
                { "orderable": false, "targets": [0, 6] } // Disable ordering for SN and Actions
            ]
        });
    });
</script>
```

```html
<!-- account/templates/account/paymentadviceheader/_paymentadvicetemporaryitem_form.html (reusable for add/edit) -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ action }} Payment Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          _="on hx:afterRequest if event.detail.xhr.status == 204 then remove .is-active from #modal">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save Item
            </button>
        </div>
    </form>
</div>
```

```html
<!-- account/templates/account/paymentadviceheader/confirm_delete.html (reusable for delete) -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete this item?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none"
          _="on hx:afterRequest if event.detail.xhr.status == 204 then remove .is-active from #modal">
        {% csrf_token %}
        <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mr-2">
            Delete
        </button>
        <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
            Cancel
        </button>
    </form>
</div>
```

```html
<!-- account/templates/account/receiptadvicemaster/receipt_tab_content.html -->
<h3 class="text-xl font-semibold text-gray-700 mb-4">Receipt Entry</h3>

<form hx-post="{% url 'receipt_add' %}" hx-target="#messages" hx-swap="beforeend"
      _="on hx:afterRequest if event.detail.xhr.status == 204 then call clearReceiptForm()">
    {% csrf_token %}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label for="{{ receipt_form.types.id_for_label }}" class="block text-sm font-medium text-gray-700">Receipt Against</label>
            {{ receipt_form.types }}
            {% if receipt_form.types.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.types.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.received_from.id_for_label }}" class="block text-sm font-medium text-gray-700">Received From</label>
            {{ receipt_form.received_from }}
            {% if receipt_form.received_from.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.received_from.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.invoice_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Invoice No</label>
            {{ receipt_form.invoice_no }}
            {% if receipt_form.invoice_no.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.invoice_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.amount.id_for_label }}" class="block text-sm font-medium text-gray-700">Amount</label>
            {{ receipt_form.amount }}
            {% if receipt_form.amount.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.amount.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.cheque_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Cheque No</label>
            {{ receipt_form.cheque_no }}
            {% if receipt_form.cheque_no.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.cheque_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.cheque_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Cheque Date</label>
            {{ receipt_form.cheque_date }}
            {% if receipt_form.cheque_date.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.cheque_date.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.cheque_received_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Cheque Received By</label>
            {{ receipt_form.cheque_received_by }}
            <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded shadow-lg max-h-48 overflow-y-auto w-1/2"></div>
            {% if receipt_form.cheque_received_by.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.cheque_received_by.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.bank_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Name</label>
            {{ receipt_form.bank_name }}
            {% if receipt_form.bank_name.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.bank_name.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.bank_acc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">Bank Account No</label>
            {{ receipt_form.bank_acc_no }}
            {% if receipt_form.bank_acc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.bank_acc_no.errors }}</p>{% endif %}
        </div>
        <div>
            <label for="{{ receipt_form.cheque_clearance_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Cheque Clearance Date</label>
            {{ receipt_form.cheque_clearance_date }}
            {% if receipt_form.cheque_clearance_date.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.cheque_clearance_date.errors }}</p>{% endif %}
        </div>
        <div class="md:col-span-2">
            <label for="{{ receipt_form.narration.id_for_label }}" class="block text-sm font-medium text-gray-700">Narration</label>
            {{ receipt_form.narration }}
            {% if receipt_form.narration.errors %}<p class="text-red-500 text-xs mt-1">{{ receipt_form.narration.errors }}</p>{% endif %}
        </div>
    </div>
    
    <div class="mt-6 text-center">
        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Submit Receipt
        </button>
    </div>
</form>

<hr class="my-6 border-gray-300">

<h3 class="text-xl font-semibold text-gray-700 mb-4">Receipt List</h3>
<div id="receiptListTableContainer"
     hx-trigger="load, refreshReceiptList from:body"
     hx-get="{% url 'receipt_list' %}"
     hx-target="this"
     hx-swap="innerHTML">
    <!-- Receipt list table loaded via HTMX -->
    <div class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading receipt list...</p>
    </div>
</div>

<script>
    function clearReceiptForm() {
        const form = document.querySelector('form[hx-post="{% url 'receipt_add' %}"]');
        if (form) {
            form.reset();
            form.querySelector('select[name="types"]').value = ''; // Reset dropdowns
            document.getElementById('employee-suggestions').innerHTML = ''; // Clear autocomplete suggestions
        }
    }
</script>
```

```html
<!-- account/templates/account/receiptadvicemaster/_receiptadvicemaster_table.html -->
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    <table id="receiptListTable" class="w-full text-sm text-left text-gray-500 dark:text-gray-400">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
            <tr>
                <th scope="col" class="py-3 px-6">SN</th>
                <th scope="col" class="py-3 px-6">Fin Year</th>
                <th scope="col" class="py-3 px-6">ADR No</th>
                <th scope="col" class="py-3 px-6">Receipt Against</th>
                <th scope="col" class="py-3 px-6">Received From</th>
                <th scope="col" class="py-3 px-6">Invoice No</th>
                <th scope="col" class="py-3 px-6">Cheque No</th>
                <th scope="col" class="py-3 px-6">Cheque Date</th>
                <th scope="col" class="py-3 px-6">Cheque Recd By</th>
                <th scope="col" class="py-3 px-6">Bank Name</th>
                <th scope="col" class="py-3 px-6">Bank Acc No</th>
                <th scope="col" class="py-3 px-6">Clearance Date</th>
                <th scope="col" class="py-3 px-6">Narration</th>
                <th scope="col" class="py-3 px-6 text-right">Amount</th>
                <th scope="col" class="py-3 px-6 text-center">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for receipt in receipts %}
            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                <td class="py-4 px-6">{{ forloop.counter }}</td>
                <td class="py-4 px-6">{{ receipt.fin_year_id }}</td> {# In a real app, join with FinancialYear model #}
                <td class="py-4 px-6">{{ receipt.adr_no }}</td>
                <td class="py-4 px-6">{{ receipt.types.description }}</td>
                <td class="py-4 px-6">{{ receipt.received_from }}</td>
                <td class="py-4 px-6">{{ receipt.invoice_no }}</td>
                <td class="py-4 px-6">{{ receipt.cheque_no }}</td>
                <td class="py-4 px-6">{{ receipt.cheque_date|date:"d-m-Y" }}</td>
                <td class="py-4 px-6">{{ receipt.cheque_received_by }}</td> {# In a real app, resolve to name #}
                <td class="py-4 px-6">{{ receipt.bank_name }}</td>
                <td class="py-4 px-6">{{ receipt.bank_acc_no }}</td>
                <td class="py-4 px-6">{{ receipt.cheque_clearance_date|date:"d-m-Y" }}</td>
                <td class="py-4 px-6">{{ receipt.narration }}</td>
                <td class="py-4 px-6 text-right">{{ receipt.amount|floatformat:2 }}</td>
                <td class="py-4 px-6 text-center">
                    <button class="font-medium text-red-600 dark:text-red-500 hover:underline"
                            hx-get="{% url 'receipt_delete' receipt.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="15" class="py-4 px-6 text-center text-gray-500">No receipts to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#receiptListTable')) {
            $('#receiptListTable').DataTable().destroy();
        }
        $('#receiptListTable').DataTable({
            "pageLength": 15,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>
```

```html
<!-- account/templates/account/receiptadvicemaster/confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-4">Are you sure you want to delete this receipt?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none"
          _="on hx:afterRequest if event.detail.xhr.status == 204 then remove .is-active from #modal">
        {% csrf_token %}
        <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mr-2">
            Delete
        </button>
        <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
            Cancel
        </button>
    </form>
</div>
```

```html
<!-- account/templates/account/common/autocomplete_list.html -->
{% if results %}
<ul class="border-t border-gray-200">
    {% for item in results %}
    <li class="px-4 py-2 hover:bg-blue-100 cursor-pointer"
        onclick="this.closest('div').previousElementSibling.value = '{{ item.name }} [{{ item.id }}]'; this.closest('div').innerHTML = '';">
        {{ item.name }} [{{ item.id }}]
    </li>
    {% endfor %}
</ul>
{% else %}
<div class="px-4 py-2 text-gray-500">No results found.</div>
{% endif %}
```

##### 4.5 URLs

**Task:** Define URL patterns for the views.

```python
# account/urls.py
from django.urls import path
from .views import (
    AdviceDashboardView,
    PaymentAdviceTemporaryItemListView, PaymentAdviceTemporaryItemCreateUpdateView, PaymentAdviceTemporaryItemDeleteView, PaymentAdviceProceedView,
    ReceiptAdviceMasterListView, ReceiptAdviceMasterCreateView, ReceiptAdviceMasterDeleteView,
    AutocompletePayeeView, AutocompleteEmployeeView, set_payee_type
)

urlpatterns = [
    # Main dashboard view
    path('', AdviceDashboardView.as_view(), name='advice_dashboard'),

    # HTMX partials for tabs
    path('payment/advance/', TemplateView.as_view(template_name='account/paymentadviceheader/advance_tab_content.html',
                                                 extra_context={'payment_header_form': PaymentAdviceHeaderForm()}), 
         name='payment_advance_tab'),
    # Add paths for other payment tabs (creditors, salary, others) similarly
    path('receipt/', TemplateView.as_view(template_name='account/receiptadvicemaster/receipt_tab_content.html',
                                           extra_context={'receipt_form': ReceiptAdviceMasterForm()}), 
         name='receipt_tab'),

    # Payment Advice Temporary Items (Advance type CRUD)
    path('payment/advance/items/', PaymentAdviceTemporaryItemListView.as_view(), name='payment_advance_temp_list'),
    path('payment/advance/items/add/', PaymentAdviceTemporaryItemCreateUpdateView.as_view(), name='payment_advance_temp_add'),
    path('payment/advance/items/edit/<int:pk>/', PaymentAdviceTemporaryItemCreateUpdateView.as_view(), name='payment_advance_temp_edit'),
    path('payment/advance/items/delete/<int:pk>/', PaymentAdviceTemporaryItemDeleteView.as_view(), name='payment_advance_temp_delete'),
    path('payment/advance/proceed/', PaymentAdviceProceedView.as_view(), name='payment_proceed_advance'),

    # Receipt Advice (CRUD)
    path('receipt/list/', ReceiptAdviceMasterListView.as_view(), name='receipt_list'),
    path('receipt/add/', ReceiptAdviceMasterCreateView.as_view(), name='receipt_add'),
    path('receipt/delete/<int:pk>/', ReceiptAdviceMasterDeleteView.as_view(), name='receipt_delete'),

    # Autocomplete Endpoints
    path('autocomplete-payee/', AutocompletePayeeView.as_view(), name='autocomplete_payee'),
    path('autocomplete-employee/', AutocompleteEmployeeView.as_view(), name='autocomplete_employee'),
    path('set-payee-type/', set_payee_type, name='set_payee_type'),
]
```

##### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Tests cover model functionality and view interactions, including HTMX specific responses. Mocking `comp_id`, `fin_year_id`, and `session_id` is crucial for isolated testing.

```python
# account/tests.py
from django.test import TestCase, Client, RequestFactory
from django.urls import reverse
from django.contrib.messages import get_messages
from django.db.utils import IntegrityError
import datetime

from .models import (
    PaymentAdviceHeader, PaymentAdviceTemporaryItem, ReceiptAdviceMaster,
    Bank, BusinessGroup, Employee, Customer, Supplier, ReceiptType, FinancialYear
)
from .views import (
    PaymentAdviceTemporaryItemListView, PaymentAdviceTemporaryItemCreateUpdateView,
    PaymentAdviceTemporaryItemDeleteView, PaymentAdviceProceedView,
    ReceiptAdviceMasterListView, ReceiptAdviceMasterCreateView, ReceiptAdviceMasterDeleteView,
    AutocompletePayeeView, AutocompleteEmployeeView, set_payee_type
)

# Mock user for testing authentication-dependent logic
class MockUser:
    def __init__(self, username='testuser', company_id=1, financial_year_id=1):
        self.username = username
        self.company_id = company_id
        self.financial_year_id = financial_year_id
        self.is_authenticated = True

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs and initial states
        Bank.objects.create(id=1, name='Test Bank 1')
        Bank.objects.create(id=2, name='Test Bank 2')
        BusinessGroup.objects.create(id=1, symbol='BG1')
        Employee.objects.create(empid=101, employeename='John Doe')
        Customer.objects.create(customerid=201, customername='Acme Corp')
        Supplier.objects.create(supplierid=301, suppliername='Supplier A')
        ReceiptType.objects.create(id=1, description='Sales Receipt')
        FinancialYear.objects.create(finyearid=1, finyear='2023-2024')

        # Create a sample PaymentAdviceTemporaryItem
        PaymentAdviceTemporaryItem.objects.create(
            id=1,
            proforma_inv_no='PI123',
            inv_date='2023-01-01',
            pono='PO456',
            amount=100.50,
            particular='Advance for project',
            comp_id=1,
            session_id='testuser',
            types=1 # Advance
        )
        # Create a sample ReceiptAdviceMaster
        ReceiptAdviceMaster.objects.create(
            id=1,
            sys_date='2023-01-01',
            sys_time='10:00:00',
            session_id='testuser',
            comp_id=1,
            fin_year_id=1,
            adr_no='0001',
            types_id=1, # Sales Receipt
            received_from='Client X',
            invoice_no='INV001',
            cheque_no='CHQ001',
            cheque_date='2023-01-01',
            cheque_received_by='101', # John Doe
            bank_name='Bank of Django',
            bank_acc_no='12345',
            cheque_clearance_date='2023-01-05',
            narration='Payment for services',
            amount=500.00
        )

    def test_payment_advice_temp_item_creation(self):
        item = PaymentAdviceTemporaryItem.objects.get(id=1)
        self.assertEqual(item.proforma_inv_no, 'PI123')
        self.assertEqual(item.amount, 100.50)
        self.assertEqual(item.session_id, 'testuser')

    def test_payment_advice_temp_item_amount_validation(self):
        with self.assertRaises(ValidationError):
            item = PaymentAdviceTemporaryItem(
                proforma_inv_no='PI124', inv_date='2023-01-02', pono='PO457',
                amount=0, particular='Zero amount', comp_id=1, session_id='testuser', types=1
            )
            item.full_clean() # Triggers model's clean method

    def test_receipt_advice_master_creation(self):
        receipt = ReceiptAdviceMaster.objects.get(id=1)
        self.assertEqual(receipt.adr_no, '0001')
        self.assertEqual(receipt.amount, 500.00)
        self.assertEqual(receipt.received_from, 'Client X')

    def test_receipt_advice_master_date_validation(self):
        receipt = ReceiptAdviceMaster(
            sys_date='2023-01-01', sys_time='10:00:00', session_id='testuser',
            comp_id=1, fin_year_id=1, adr_no='0002', types_id=1, received_from='Client Y',
            invoice_no='INV002', cheque_no='CHQ002', cheque_date='2023-01-10',
            cheque_received_by='101', bank_name='Bank A', bank_acc_no='67890',
            cheque_clearance_date='2023-01-05', narration='Invalid date', amount=200.00
        )
        with self.assertRaises(ValidationError) as cm:
            receipt.full_clean()
        self.assertIn('Cheque date cannot be after clearance date.', str(cm.exception))

    def test_get_next_ad_no(self):
        # Create another header to test increment
        PaymentAdviceHeader.objects.create(
            id=10, sys_date='2023-01-01', sys_time='10:00:00', session_id='testuser',
            comp_id=1, fin_year_id=1, ad_no='0005', type=1, pay_to='Dummy [999]',
            cheque_no='CHQ001', cheque_date='2023-01-01', pay_at='Location',
            bank_id=1, ecs_type=1
        )
        next_ad_no = PaymentAdviceHeader.objects.get_next_ad_no(1, 1)
        self.assertEqual(next_ad_no, '0006')
    
    def test_get_next_adr_no(self):
        # Create another receipt to test increment
        ReceiptAdviceMaster.objects.create(
            id=10, sys_date='2023-01-01', sys_time='10:00:00', session_id='testuser',
            comp_id=1, fin_year_id=1, adr_no='0005', types_id=1, received_from='Test',
            invoice_no='INV001', cheque_no='CHQ001', cheque_date='2023-01-01',
            cheque_received_by='101', bank_name='Bank of Django', bank_acc_no='12345',
            cheque_clearance_date='2023-01-05', narration='Payment for services',
            amount=500.00
        )
        next_adr_no = ReceiptAdviceMaster.objects.get_next_adr_no(1, 1)
        self.assertEqual(next_adr_no, '0006')

class ViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.factory = RequestFactory()
        self.user = MockUser() # Using the MockUser for tests

        # Ensure base data exists for FKs etc.
        Bank.objects.create(id=1, name='Test Bank')
        Employee.objects.create(empid=101, employeename='Test Employee')
        Customer.objects.create(customerid=201, customername='Test Customer')
        Supplier.objects.create(supplierid=301, suppliername='Test Supplier')
        ReceiptType.objects.create(id=1, description='General')
        FinancialYear.objects.create(finyearid=1, finyear='2023-2024')

    def _get_request_with_user(self, path, method='get', data=None, **kwargs):
        request = getattr(self.factory, method)(path, data=data, **kwargs)
        request.user = self.user
        request.session = self.client.session
        request.session['current_payee_type'] = '1' # Default for payee autocomplete tests
        return request

    # --- Payment Advice Temporary Item Tests ---
    def test_payment_advance_temp_list_view(self):
        PaymentAdviceTemporaryItem.objects.create(
            id=2, proforma_inv_no='PI002', inv_date='2023-02-01', pono='PO002',
            amount=200.00, particular='Test item', comp_id=self.user.company_id,
            session_id=self.user.username, types=1
        )
        request = self._get_request_with_user(reverse('payment_advance_temp_list'), HTTP_HX_REQUEST='true')
        response = PaymentAdviceTemporaryItemListView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'PI002')
        self.assertEqual(len(response.context_data['temporary_items']), 1)

    def test_payment_advance_temp_add_view(self):
        request = self._get_request_with_user(reverse('payment_advance_temp_add'), method='post', data={
            'proforma_inv_no': 'PI003', 'inv_date': '2023-03-01', 'pono': 'PO003',
            'particular': 'New item', 'amount': 300.00
        }, HTTP_HX_REQUEST='true')
        response = PaymentAdviceTemporaryItemCreateUpdateView.as_view()(request)
        self.assertEqual(response.status_code, 204) # HTMX success status
        self.assertTrue(PaymentAdviceTemporaryItem.objects.filter(proforma_inv_no='PI003').exists())
        messages = list(get_messages(request))
        self.assertIn('Item added successfully.', [m.message for m in messages])

    def test_payment_advance_temp_edit_view(self):
        item = PaymentAdviceTemporaryItem.objects.create(
            id=3, proforma_inv_no='PI004', inv_date='2023-04-01', pono='PO004',
            amount=400.00, particular='Item to edit', comp_id=self.user.company_id,
            session_id=self.user.username, types=1
        )
        request = self._get_request_with_user(reverse('payment_advance_temp_edit', args=[item.pk]), method='post', data={
            'proforma_inv_no': 'PI004_Edited', 'inv_date': '2023-04-01', 'pono': 'PO004',
            'particular': 'Item to edit', 'amount': 450.00
        }, HTTP_HX_REQUEST='true')
        response = PaymentAdviceTemporaryItemCreateUpdateView.as_view()(request, pk=item.pk)
        self.assertEqual(response.status_code, 204)
        item.refresh_from_db()
        self.assertEqual(item.proforma_inv_no, 'PI004_Edited')
        self.assertEqual(item.amount, 450.00)
        messages = list(get_messages(request))
        self.assertIn('Item updated successfully.', [m.message for m in messages])

    def test_payment_advance_temp_delete_view(self):
        item = PaymentAdviceTemporaryItem.objects.create(
            id=4, proforma_inv_no='PI005', inv_date='2023-05-01', pono='PO005',
            amount=500.00, particular='Item to delete', comp_id=self.user.company_id,
            session_id=self.user.username, types=1
        )
        request = self._get_request_with_user(reverse('payment_advance_temp_delete', args=[item.pk]), method='post', HTTP_HX_REQUEST='true')
        response = PaymentAdviceTemporaryItemDeleteView.as_view()(request, pk=item.pk)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(PaymentAdviceTemporaryItem.objects.filter(id=item.pk).exists())
        messages = list(get_messages(request))
        self.assertIn('Item deleted successfully.', [m.message for m in messages])

    def test_payment_proceed_advance_view(self):
        PaymentAdviceTemporaryItem.objects.create(
            id=5, proforma_inv_no='PI006', inv_date='2023-06-01', pono='PO006',
            amount=600.00, particular='Finalize item', comp_id=self.user.company_id,
            session_id=self.user.username, types=1
        )
        data = {
            'pay_to_type': '1', # Employee
            'pay_to_name': 'Test Employee [101]',
            'cheque_no': 'CHQ123',
            'cheque_date': '2023-06-15',
            'pay_at': 'City Bank',
            'bank': '1' # Test Bank
        }
        request = self._get_request_with_user(reverse('payment_proceed_advance'), method='post', data=data, HTTP_HX_REQUEST='true')
        response = PaymentAdviceProceedView.as_view()(request)
        self.assertEqual(response.status_code, 204)
        self.assertTrue(PaymentAdviceHeader.objects.filter(cheque_no='CHQ123').exists())
        self.assertFalse(PaymentAdviceTemporaryItem.objects.filter(session_id=self.user.username, comp_id=self.user.company_id, types=1).exists())
        messages = list(get_messages(request))
        self.assertIn('Payment Advice', [m.message for m in messages][0]) # Check for success message content

    # --- Receipt Advice Tests ---
    def test_receipt_list_view(self):
        ReceiptAdviceMaster.objects.create(
            id=2, sys_date='2023-01-01', sys_time='10:00:00', session_id='testuser',
            comp_id=self.user.company_id, fin_year_id=self.user.financial_year_id,
            adr_no='0002', types_id=1, received_from='Client Y', invoice_no='INV002',
            cheque_no='CHQ002', cheque_date='2023-01-01', cheque_received_by='101',
            bank_name='Bank B', bank_acc_no='67890', cheque_clearance_date='2023-01-05',
            narration='Some receipt', amount=250.00
        )
        request = self._get_request_with_user(reverse('receipt_list'), HTTP_HX_REQUEST='true')
        response = ReceiptAdviceMasterListView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'INV002')
        self.assertEqual(len(response.context_data['receipts']), 1) # Assuming previous data was for different CompId/FinYear or cleared

    def test_receipt_add_view(self):
        data = {
            'types': '1',
            'received_from': 'New Client',
            'invoice_no': 'INV003',
            'amount': 750.00,
            'cheque_no': 'CHQ003',
            'cheque_date': '2023-07-01',
            'cheque_received_by': 'Test Employee [101]',
            'bank_name': 'New Bank',
            'bank_acc_no': '98765',
            'cheque_clearance_date': '2023-07-05',
            'narration': 'New receipt entry'
        }
        request = self._get_request_with_user(reverse('receipt_add'), method='post', data=data, HTTP_HX_REQUEST='true')
        response = ReceiptAdviceMasterCreateView.as_view()(request)
        self.assertEqual(response.status_code, 204)
        self.assertTrue(ReceiptAdviceMaster.objects.filter(invoice_no='INV003').exists())
        messages = list(get_messages(request))
        self.assertIn('Receipt Advice', [m.message for m in messages][0])

    def test_receipt_delete_view(self):
        receipt = ReceiptAdviceMaster.objects.create(
            id=3, sys_date='2023-01-01', sys_time='10:00:00', session_id='testuser',
            comp_id=self.user.company_id, fin_year_id=self.user.financial_year_id,
            adr_no='0003', types_id=1, received_from='Client Z', invoice_no='INV004',
            cheque_no='CHQ004', cheque_date='2023-01-01', cheque_received_by='101',
            bank_name='Bank C', bank_acc_no='11223', cheque_clearance_date='2023-01-05',
            narration='Delete test', amount=150.00
        )
        request = self._get_request_with_user(reverse('receipt_delete', args=[receipt.pk]), method='post', HTTP_HX_REQUEST='true')
        response = ReceiptAdviceMasterDeleteView.as_view()(request, pk=receipt.pk)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ReceiptAdviceMaster.objects.filter(id=receipt.pk).exists())
        messages = list(get_messages(request))
        self.assertIn('Receipt deleted successfully.', [m.message for m in messages])

    # --- Autocomplete Tests ---
    def test_autocomplete_payee_employee(self):
        request = self._get_request_with_user(reverse('autocomplete_payee'), data={'q': 'john'})
        request.session['current_payee_type'] = '1' # Set session for employee type
        response = AutocompletePayeeView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe [101]')

    def test_autocomplete_payee_customer(self):
        request = self._get_request_with_user(reverse('autocomplete_payee'), data={'q': 'acme'})
        request.session['current_payee_type'] = '2' # Set session for customer type
        response = AutocompletePayeeView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Acme Corp [201]')

    def test_autocomplete_payee_supplier(self):
        request = self._get_request_with_user(reverse('autocomplete_payee'), data={'q': 'supplier'})
        request.session['current_payee_type'] = '3' # Set session for supplier type
        response = AutocompletePayeeView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Supplier A [301]')

    def test_autocomplete_employee(self):
        request = self._get_request_with_user(reverse('autocomplete_employee'), data={'q': 'test'})
        response = AutocompleteEmployeeView.as_view()(request)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Employee [101]')

    def test_set_payee_type(self):
        request = self._get_request_with_user(reverse('set_payee_type'), method='post', data={'payee_type': '2'}, HTTP_HX_REQUEST='true')
        response = set_payee_type(request)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(self.client.session['current_payee_type'], '2')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates provided heavily leverage HTMX for dynamic content and form submissions without full page reloads. Alpine.js is integrated for simple UI state management, such as showing/hiding modals. DataTables are used for enhanced list view capabilities.

*   **HTMX:**
    *   **Tab Switching**: `hx-get` and `hx-target` attributes on the main tab links load the respective tab content dynamically.
    *   **Temporary Item CRUD**: "Add New Item," "Edit," and "Delete" buttons trigger `hx-get` to load forms/confirmation modals into `#modalContent`. Form submissions use `hx-post` with `hx-swap="none"` and `hx-trigger="refreshAdvanceTempList"` to signal the main temporary item list to refresh after successful operations.
    *   **"Proceed" Button**: `hx-post` sends the main payment/receipt form data. On success (204 status), it triggers `refreshAdvanceTab` or `refreshReceiptList` to clear the form and refresh the main content area.
    *   **List Refresh**: `hx-trigger="load, refreshAdvanceTempList from:body"` ensures that the temporary item lists (and receipt lists) load on page load and refresh when specific custom events (like `refreshAdvanceTempList`) are triggered from anywhere on the page (e.g., after a successful form submission).
    *   **Autocomplete**: `hx-get` with `hx-trigger="keyup changed delay:500ms"` on text input fields sends user input to a Django view, which returns a list of suggestions. `hx-target` and `hx-swap="innerHTML"` are used to update a suggestion `div` below the input. JavaScript `onclick` handles selecting a suggestion and populating the input field.
*   **Alpine.js:**
    *   **Tab State**: `x-data="{ activeTab: 'payment' }"` and `x-show` directives are used to manage which tab's content is currently visible, providing instant client-side switching.
    *   **Modal Management**: `x-data` on the global modal div, combined with `on click add .is-active to #modal` for opening and `on click if event.target.id == 'modal' remove .is-active from me` for closing (by clicking outside).
*   **DataTables:**
    *   Initialized on `_paymentadvicetemporaryitem_table.html` and `_receiptadvicemaster_table.html` via `$(document).ready()`. This ensures client-side searching, sorting, and pagination for the tables without additional backend logic.
    *   The `destroy()` method is called before re-initializing to handle HTMX content swaps gracefully.
*   **DRY Templates:** Common modal structure (`#modal`, `#modalContent`) is in the base template, while specific form content is loaded as partials. Messages are handled globally.

**Final Notes:**

*   **Placeholders**: Remember to replace `[TABLE_NAME]`, `[MODEL_NAME]`, `[FIELD_TYPE]`, `[COLUMN_NAME]`, `[APP_NAME]`, `[TEST_VALUE]` etc., with actual values derived from your ASP.NET application.
*   **Security**: Ensure proper authentication, authorization, and CSRF protection are in place. The `Hx-Request` headers are useful but not a substitute for robust server-side security checks.
*   **Error Handling**: Expand on error messaging beyond simple alerts. Django's `messages` framework is used, which Alpine.js can display as toast notifications for a better user experience.
*   **Completeness**: This plan covers the core structure and two representative CRUD flows. The "Creditors," "Salary," and "Others" payment tabs would follow similar patterns, requiring additional models, forms, views, and templates, but the architectural approach remains consistent.
*   **Database Interactions**: The `fun` class methods for SQL operations are replaced by Django ORM queries and methods within model managers, ensuring all database interactions are handled Pythonically and securely.
*   **Deployment**: This setup is ready for deployment on standard Python/Django hosting environments (e.g., Gunicorn/Nginx, Heroku, AWS Elastic Beanstalk).