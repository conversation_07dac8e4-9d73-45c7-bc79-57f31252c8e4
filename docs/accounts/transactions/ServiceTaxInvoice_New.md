This modernization plan outlines the transition of your ASP.NET `ServiceTaxInvoice_New.aspx` application to a modern Django-based solution. We will leverage Django's powerful ORM, Class-Based Views, and integrate HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience without relying on traditional JavaScript frameworks.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`ServiceTaxInvoice_New`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with several database tables to display Purchase Orders (`PO`) and related Work Orders (`WO`). The primary table being displayed is `SD_Cust_PO_Master`. Data is joined from `tblFinancial_master`, `SD_Cust_master`, `SD_Cust_PO_Details`, `tblACC_ServiceTaxInvoice_Master`, `tblACC_ServiceTaxInvoice_Details`, and `SD_Cust_WorkOrder_Master`.

**Inferred Tables and Key Columns:**

*   **`SD_Cust_PO_Master`** (Primary entity for listing)
    *   `POId` (Primary Key, integer)
    *   `PONo` (string)
    *   `CustomerId` (Foreign Key to `SD_Cust_master`)
    *   `SysDate` (datetime)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `CompId` (integer, used for company context)
    *   `PODate` (datetime, likely related to `SysDate`)

*   **`SD_Cust_master`** (Customer details)
    *   `CustomerId` (Primary Key, integer)
    *   `CustomerName` (string)
    *   `CompId` (integer)

*   **`tblFinancial_master`** (Financial year details)
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (string)

*   **`SD_Cust_PO_Details`** (Purchase Order line items)
    *   `Id` (Primary Key, integer)
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `TotalQty` (decimal/float)

*   **`tblACC_ServiceTaxInvoice_Master`** (Service Tax Invoice Header)
    *   `Id` (Primary Key, integer)
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `CompId` (integer)

*   **`tblACC_ServiceTaxInvoice_Details`** (Service Tax Invoice Line Items)
    *   `Id` (Primary Key, integer)
    *   `MId` (Foreign Key to `tblACC_ServiceTaxInvoice_Master`)
    *   `ItemId` (Foreign Key to `SD_Cust_PO_Details`, linking invoice lines to PO lines)
    *   `ReqQty` (decimal/float, 'requested' or 'invoiced' quantity)

*   **`SD_Cust_WorkOrder_Master`** (Work Order details)
    *   `Id` (Primary Key, integer)
    *   `WONo` (string)
    *   `TaskProjectTitle` (string)
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `CompId` (integer)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page primarily implements **Read** (listing/searching Purchase Orders) and a complex selection mechanism for Work Orders linked to a PO, leading to a redirection for a "Service Tax Invoice Details" page.

*   **Read (Listing/Search):**
    *   Displays a list of Purchase Orders in a `GridView`.
    *   Allows searching by "Customer Name" or "PO No".
    *   Filters Purchase Orders based on `CompId`, `FinYearId`, and a crucial business rule: **only POs with a remaining quantity to be invoiced (`rmnqty > 0`) are displayed.** This calculation involves summing `TotalQty` from `SD_Cust_PO_Details` and `ReqQty` from `tblACC_ServiceTaxInvoice_Details` for each PO line item.
    *   Customer Name search uses an AJAX autocomplete feature.

*   **Custom UI Interaction (Work Order Selection):**
    *   Each row in the grid has a custom dropdown (`DropDownExtender` with `ListBox1`) for selecting multiple "Work Orders" (`WONo` + `TaskProjectTitle`) associated with that specific Purchase Order.
    *   Selections in this listbox update a display textbox and a hidden field (`hfWOno`) in the same row.

*   **Action (Select PO):**
    *   A "Select" button on each row triggers a `Response.Redirect` to `ServiceTaxInvoice_New_Details.aspx`.
    *   The redirect passes encrypted `POId`, `WONoId` (selected Work Order IDs), `PONo`, `Date`, `CustomerId` as query parameters. If `WONoId` is empty, an alert is shown.

**Validation Logic:**
- If no Work Order is selected, an alert `Select WONo.` is displayed on clicking "Select".

### Step 3: Infer UI Components

**Analysis:**
The page combines search filters with a data grid.

*   **Search/Filter Area:**
    *   **Dropdown (`DropDownList1`):** HTML `<select>` for search type (Customer Name / PO No).
    *   **Textboxes (`txtCustName`, `txtpoNo`):** HTML `<input type="text>`. `txtCustName` has an AJAX autocomplete.
    *   **Button (`btnSearch`):** HTML `<button>` to trigger the search.

*   **Data Display Area:**
    *   **Grid (`GridView1`):** This maps directly to a `<table>` element, which will be initialized as a DataTables instance.
    *   **Columns:**
        *   "SN" (row number)
        *   "FinYear" (read-only label)
        *   "Name of Customer" (read-only label)
        *   "PO No" (read-only label)
        *   "Date" (read-only label)
        *   "WO No" (complex multi-select, will become an HTMX-powered modal interaction)
        *   "Select" (LinkButton, will be an HTMX-powered button triggering a redirect).

*   **AJAX Elements:** `AutoCompleteExtender` will be replaced by a Django view returning JSON, consumed by HTMX and Alpine.js. The dynamic population of `ListBox1` per row will be handled by HTMX.

### Step 4: Generate Django Code

We will create a Django application named `accounts` to house this functionality.

#### 4.1 Models (`accounts/models.py`)

We will define models for all identified database tables, setting `managed = False` and `db_table` to map them directly to your existing database schema. The core logic for `remaining_quantity` will be encapsulated within the `PurchaseOrder` model.

```python
from django.db import models
from django.db.models import Sum, F
from decimal import Decimal

# Helper for session-based company and financial year IDs (replace with actual session retrieval in views)
def get_session_comp_id(request):
    return request.session.get('compid', 1) # Default to 1 if not found
def get_session_fin_year_id(request):
    return request.session.get('finyear', 1) # Default to 1 if not found

class FinancialYearMaster(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class CustomerMaster(models.Model):
    """
    Maps to SD_Cust_master for customer details.
    """
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId') # Assuming CompId links to a Company model if exists

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class PurchaseOrder(models.Model):
    """
    Maps to SD_Cust_PO_Master for Purchase Order details.
    This is the main model for the list display.
    """
    poid = models.IntegerField(db_column='POId', primary_key=True)
    pono = models.CharField(db_column='PONo', max_length=100)
    customer = models.ForeignKey(
        CustomerMaster,
        on_delete=models.DO_NOTHING, # Or SET_NULL, CASCADE depending on business logic
        db_column='CustomerId',
        related_name='purchase_orders'
    )
    sys_date = models.DateTimeField(db_column='SysDate')
    podate = models.DateTimeField(db_column='PODate') # Assuming PODate is also DateTimeField
    fin_year = models.ForeignKey(
        FinancialYearMaster,
        on_delete=models.DO_NOTHING,
        db_column='FinYearId',
        related_name='purchase_orders'
    )
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'
        ordering = ['-poid'] # Order By POId Desc as in original code

    def __str__(self):
        return self.pono

    def get_remaining_invoice_quantity(self, request):
        """
        Calculates the remaining quantity to be invoiced for this Purchase Order.
        Replicates the complex logic from bindgrid method in ASP.NET.
        This calculation requires querying related tables.
        """
        total_po_qty = PurchaseOrderDetail.objects.filter(
            po=self
        ).aggregate(Sum('total_qty'))['total_qty__sum'] or Decimal('0.000')

        # Get total quantity already invoiced for this PO across all its details
        invoiced_qty = ServiceTaxInvoiceDetail.objects.filter(
            service_tax_invoice_master__po_id=self.poid,
            service_tax_invoice_master__comp_id=get_session_comp_id(request),
            item__po=self # Join through PurchaseOrderDetail.po
        ).aggregate(Sum('req_qty'))['req_qty__sum'] or Decimal('0.000')

        remaining_qty = total_po_qty - invoiced_qty
        return remaining_qty

    def get_formatted_sys_date(self):
        """Formats SysDate to DMY format."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

    def get_customer_name_with_id(self):
        """Returns CustomerName [CustomerId] string."""
        return f"{self.customer.customer_name} [{self.customer.customer_id}]"

    # Property to hold temporarily selected WO IDs (for HTMX OOB swap)
    _selected_wo_ids = models.CharField(max_length=500, blank=True, default='')

    @property
    def selected_wo_ids(self):
        return self._selected_wo_ids

    @selected_wo_ids.setter
    def selected_wo_ids(self, value):
        self._selected_wo_ids = value

    @property
    def selected_wo_text(self):
        """Generates the display text for selected Work Orders."""
        if not self._selected_wo_ids:
            return ""
        
        # Split IDs, fetch corresponding WO objects and format
        wo_ids = [int(x) for x in self._selected_wo_ids.split(',') if x]
        work_orders = WorkOrderMaster.objects.filter(id__in=wo_ids).order_by('won_o')
        
        return ",".join([wo.get_display_text() for wo in work_orders])


class PurchaseOrderDetail(models.Model):
    """
    Maps to SD_Cust_PO_Details for Purchase Order line items.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    po = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.DO_NOTHING,
        db_column='POId',
        related_name='details'
    )
    total_qty = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3) # Assuming Decimal

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

class ServiceTaxInvoiceMaster(models.Model):
    """
    Maps to tblACC_ServiceTaxInvoice_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    po = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.DO_NOTHING,
        db_column='POId',
        related_name='service_tax_invoices'
    )
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Master'
        verbose_name = 'Service Tax Invoice Header'
        verbose_name_plural = 'Service Tax Invoices Headers'

class ServiceTaxInvoiceDetail(models.Model):
    """
    Maps to tblACC_ServiceTaxInvoice_Details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    service_tax_invoice_master = models.ForeignKey(
        ServiceTaxInvoiceMaster,
        on_delete=models.DO_NOTHING,
        db_column='MId',
        related_name='details'
    )
    item = models.ForeignKey(
        PurchaseOrderDetail,
        on_delete=models.DO_NOTHING,
        db_column='ItemId',
        related_name='invoiced_details'
    ) # ItemId links to SD_Cust_PO_Details.Id
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblACC_ServiceTaxInvoice_Details'
        verbose_name = 'Service Tax Invoice Detail'
        verbose_name_plural = 'Service Tax Invoice Details'

class WorkOrderMaster(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    won_o = models.CharField(db_column='WONo', max_length=100)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255)
    po = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.DO_NOTHING,
        db_column='POId',
        related_name='work_orders'
    )
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.won_o} - {self.task_project_title}"

    def get_display_text(self):
        """Returns the format 'WONo-TaskProjectTitle' as in ASP.NET."""
        return f"{self.won_o}-{self.task_project_title}"
```

#### 4.2 Forms (`accounts/forms.py`)

A simple Django `Form` will be used for the search functionality, as it's not directly tied to a model for CRUD.

```python
from django import forms
from .models import CustomerMaster

class PurchaseOrderSearchForm(forms.Form):
    SEARCH_BY_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'PO No'),
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': 'hx-get="{% url \'accounts:purchaseorder_toggle_search_field\' %}" hx-target="#search-input-container" hx-swap="outerHTML"'})
    )
    customer_name = forms.CharField(
        max_length=350,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter Customer Name',
            'id': 'txtCustName', # Match original ID for autocomplete
            'hx-get': '{% url "accounts:customer_autocomplete" %}',
            'hx-trigger': 'keyup changed delay:500ms, search', # Trigger on keyup with delay
            'hx-target': '#autocomplete-results', # Target to display results
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            'style': 'width: 350px;'
        })
    )
    po_no = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter PO No',
            'id': 'txtpoNo', # Match original ID
            'style': 'display: none;' # Initially hidden
        })
    )

    def __init__(self, *args, **kwargs):
        initial_search_by = kwargs.pop('initial_search_by', '0')
        super().__init__(*args, **kwargs)
        self.fields['search_by'].initial = initial_search_by
        if initial_search_by == '0':
            self.fields['customer_name'].widget.attrs['style'] = 'width: 350px;'
            self.fields['po_no'].widget.attrs['style'] = 'display: none;'
        else: # '1' for PO No
            self.fields['customer_name'].widget.attrs['style'] = 'display: none;'
            self.fields['po_no'].widget.attrs['style'] = ''

    def get_customer_id(self):
        if self.cleaned_data.get('customer_name'):
            # Extract customer ID from the "Name [ID]" format
            parts = self.cleaned_data['customer_name'].strip().split(' [')
            if len(parts) > 1:
                try:
                    customer_id_str = parts[-1].rstrip(']')
                    return int(customer_id_str)
                except ValueError:
                    pass
        return None

# Form for the Work Order Selection Modal (very simple, just for context)
class WorkOrderSelectForm(forms.Form):
    selected_wo_ids = forms.MultipleChoiceField(
        required=False,
        widget=forms.CheckboxSelectMultiple(attrs={'class': 'form-checkbox h-5 w-5 text-blue-600'}),
        choices=[] # Will be populated in the view
    )

    def __init__(self, *args, **kwargs):
        work_orders = kwargs.pop('work_orders', [])
        super().__init__(*args, **kwargs)
        self.fields['selected_wo_ids'].choices = [(wo.id, wo.get_display_text()) for wo in work_orders]
```

#### 4.3 Views (`accounts/views.py`)

We'll use Class-Based Views for the main listing and function-based views or `JsonView` for HTMX-specific interactions like autocomplete and partial table updates.

```python
from django.views.generic import TemplateView, ListView, View
from django.http import JsonResponse, HttpResponseRedirect, HttpResponse
from django.urls import reverse_lazy, reverse
from django.shortcuts import render, get_object_or_404
from django.db.models import Q
from django.template.loader import render_to_string
from django.contrib import messages
import urllib.parse
from decimal import Decimal

from .models import PurchaseOrder, CustomerMaster, WorkOrderMaster
from .forms import PurchaseOrderSearchForm, WorkOrderSelectForm

# Helper for session-based company and financial year IDs
def get_session_comp_id(request):
    return request.session.get('compid', 1) # Default value if not in session
def get_session_fin_year_id(request):
    return request.session.get('finyear', 1) # Default value if not in session


class ServiceTaxInvoiceListView(TemplateView):
    """
    Renders the main Service Tax Invoice page with search form and container for the PO list.
    Corresponds to the initial ServiceTaxInvoice_New.aspx load.
    """
    template_name = 'accounts/servicetaxinvoice/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form
        context['search_form'] = PurchaseOrderSearchForm()
        return context

class PurchaseOrderTablePartialView(ListView):
    """
    Handles fetching and rendering the Purchase Order data table.
    This view is specifically targeted by HTMX to update the table content.
    Replicates the 'bindgrid' logic.
    """
    model = PurchaseOrder
    template_name = 'accounts/servicetaxinvoice/_po_list_table.html'
    context_object_name = 'purchase_orders'

    def get_queryset(self):
        comp_id = get_session_comp_id(self.request)
        fin_year_id = get_session_fin_year_id(self.request)

        queryset = PurchaseOrder.objects.filter(
            comp_id=comp_id,
            fin_year__fin_year_id__lte=fin_year_id # Filter by FinYearId less than or equal
        )

        search_by = self.request.GET.get('search_by', '0')
        customer_name_search = self.request.GET.get('customer_name', '').strip()
        po_no_search = self.request.GET.get('po_no', '').strip()

        if search_by == '1' and po_no_search: # Search by PO No
            queryset = queryset.filter(pono__iexact=po_no_search)
        elif search_by == '0' and customer_name_search: # Search by Customer Name
            # Extract customer ID from input if in "Name [ID]" format
            customer_id = None
            parts = customer_name_search.split(' [')
            if len(parts) > 1:
                try:
                    customer_id = int(parts[-1].rstrip(']'))
                except ValueError:
                    pass
            
            if customer_id:
                queryset = queryset.filter(customer__customer_id=customer_id)
            else: # Fallback to name contains if ID not extractable
                queryset = queryset.filter(customer__customer_name__icontains=customer_name_search)
        
        # Filter based on remaining quantity > 0 (complex logic from ASP.NET)
        filtered_pos = []
        for po in queryset:
            if po.get_remaining_invoice_quantity(self.request) > Decimal('0.000'):
                filtered_pos.append(po)
        
        # Manually set the selected WO IDs back into the objects for display/hidden input
        # This is because HTMX won't re-render entire form, so we must manually restore state
        for po in filtered_pos:
            # Check if this PO's WO IDs were previously stored in session for the current user
            session_key = f'selected_wo_for_po_{po.poid}_{self.request.session.session_key}'
            if session_key in self.request.session:
                po.selected_wo_ids = self.request.session[session_key]

        return filtered_pos # Returns a list of PurchaseOrder objects

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # We need to make sure the search form is correctly instantiated here for template rendering
        search_by = self.request.GET.get('search_by', '0')
        customer_name_search = self.request.GET.get('customer_name', '')
        po_no_search = self.request.GET.get('po_no', '')
        
        form_data = {
            'search_by': search_by,
            'customer_name': customer_name_search,
            'po_no': po_no_search,
        }
        context['search_form'] = PurchaseOrderSearchForm(form_data, initial_search_by=search_by)
        return context


class CustomerAutocompleteView(View):
    """
    Provides customer name suggestions for the autocomplete functionality.
    Replicates the 'sql' WebMethod from ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('query', '').strip()
        comp_id = get_session_comp_id(request)
        
        customers = CustomerMaster.objects.filter(
            comp_id=comp_id,
            customer_name__istartswith=prefix_text
        ).order_by('customer_name')[:10] # Limit to 10 suggestions

        results = [str(customer) for customer in customers] # customer.__str__ already formats "Name [ID]"
        return JsonResponse(results, safe=False)

class ToggleSearchFieldView(View):
    """
    Handles the dynamic visibility of search input fields based on dropdown selection.
    This is triggered by HTMX on DropDownList1_SelectedIndexChanged.
    """
    def get(self, request, *args, **kwargs):
        selected_value = request.GET.get('search_by', '0')
        customer_name_val = request.GET.get('customer_name', '')
        po_no_val = request.GET.get('po_no', '')

        search_form = PurchaseOrderSearchForm(
            initial={
                'search_by': selected_value,
                'customer_name': customer_name_val,
                'po_no': po_no_val,
            },
            initial_search_by=selected_value
        )
        
        return render(request, 'accounts/servicetaxinvoice/_search_input_fields.html', {'search_form': search_form})

class WorkOrderSelectModalView(View):
    """
    Renders the modal content for selecting Work Orders for a specific Purchase Order.
    Corresponds to the dynamic population and interaction with ListBox1 in ASP.NET.
    """
    def get(self, request, po_id, *args, **kwargs):
        purchase_order = get_object_or_404(PurchaseOrder, poid=po_id)
        comp_id = get_session_comp_id(request)

        # Get work orders relevant to this PO and company
        work_orders = WorkOrderMaster.objects.filter(
            po=purchase_order,
            comp_id=comp_id
        ).order_by('won_o')

        # Pre-select based on session state if available
        session_key = f'selected_wo_for_po_{po_id}_{request.session.session_key}'
        initial_selected_wo_ids = []
        if session_key in request.session:
            initial_selected_wo_ids = [int(x) for x in request.session[session_key].split(',') if x]

        form = WorkOrderSelectForm(
            work_orders=work_orders,
            initial={'selected_wo_ids': initial_selected_wo_ids}
        )
        
        return render(request, 'accounts/servicetaxinvoice/_wo_select_modal.html', {
            'form': form,
            'purchase_order': purchase_order,
        })

    def post(self, request, po_id, *args, **kwargs):
        purchase_order = get_object_or_404(PurchaseOrder, poid=po_id)
        
        # Bind the form to the POST data
        form = WorkOrderSelectForm(request.POST)
        if form.is_valid():
            selected_ids = form.cleaned_data['selected_wo_ids']
            selected_ids_str = ','.join(str(x) for x in selected_ids)
            
            # Store selected WO IDs in session for later retrieval on redirect or for subsequent table refresh
            session_key = f'selected_wo_for_po_{po_id}_{request.session.session_key}'
            request.session[session_key] = selected_ids_str

            # Generate the HTML for the OOB swap to update the specific row
            # Use a dummy PurchaseOrder object or retrieve the actual one
            # and set its temporary selected_wo_ids property for rendering
            purchase_order.selected_wo_ids = selected_ids_str

            context = {'obj': purchase_order}
            updated_wo_column_html = render_to_string(
                'accounts/servicetaxinvoice/_wo_display_and_input.html',
                context,
                request=request
            )

            # Return HTTP 200 OK with OOB swap headers
            response = HttpResponse(status=200)
            response['HX-Trigger'] = f'updateWORow:{po_id}' # Custom event to trigger re-render of just the WO column
            response['HX-Reswap'] = 'outerHTML' # This combined with HX-Trigger is often used for OOB swap without explicit header
            response['HX-Swap-OOB'] = f'outerHTML:#wo-column-{po_id}' # Direct OOB swap
            response.content = updated_wo_column_html # Content for the OOB swap

            # Optionally, if modal should close after submit, add an HX-Trigger for Alpine.js
            response['HX-Trigger'] += ', closeModal'

            return response
        
        # If form is invalid, re-render the modal with errors
        work_orders = WorkOrderMaster.objects.filter(po=purchase_order, comp_id=get_session_comp_id(request)).order_by('won_o')
        form.fields['selected_wo_ids'].choices = [(wo.id, wo.get_display_text()) for wo in work_orders]
        return render(request, 'accounts/servicetaxinvoice/_wo_select_modal.html', {
            'form': form,
            'purchase_order': purchase_order,
        }, status=400) # Bad Request if form invalid


class SelectPoAndRedirectView(View):
    """
    Handles the 'Select' command for a Purchase Order, redirecting to the details page.
    Replicates the GridView1_RowCommand logic.
    """
    def get(self, request, po_id, *args, **kwargs):
        purchase_order = get_object_or_404(PurchaseOrder, poid=po_id)

        # Retrieve selected WO IDs from session
        session_key = f'selected_wo_for_po_{po_id}_{request.session.session_key}'
        wo_ids_str = request.session.get(session_key, '')

        if not wo_ids_str:
            # Replicate ASP.NET alert logic with HTMX client-side alert
            response = HttpResponse(status=200)
            response['HX-Trigger'] = 'showWoSelectAlert' # Custom event for Alpine to trigger alert
            return response

        # Prepare parameters for the redirect
        # In a real scenario, consider more robust URL signing or direct PKs instead of encryption for security
        params = {
            'poid': purchase_order.poid,
            'wn': wo_ids_str,
            'pn': purchase_order.pono,
            'date': purchase_order.get_formatted_sys_date(),
            'cid': purchase_order.customer.customer_id,
            'ModId': '11', # These might be hardcoded module IDs from ASP.NET
            'SubModId': '52',
        }
        
        # Clear selected WO from session after use
        if session_key in request.session:
            del request.session[session_key]

        # Build the redirect URL
        # Assuming 'service_tax_invoice_details' is the name of the URL for the next page
        # which would typically be in another app/module
        next_page_url = reverse_lazy('service_tax_invoice:details') # Replace with actual URL name
        redirect_url = f"{next_page_url}?{urllib.parse.urlencode(params)}"
        
        return HttpResponseRedirect(redirect_url)

```

#### 4.4 Templates (`accounts/templates/accounts/servicetaxinvoice/`)

The templates are designed for HTMX partial updates and DataTables integration.

**`list.html`** (Main page, extends `core/base.html`)

```html
{% extends 'core/base.html' %}

{% block title %}Service Tax Invoice - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 bg-white rounded-lg shadow-md">
    <div class="bg-blue-600 text-white font-bold py-2 px-4 rounded-t-lg mb-4">
        &nbsp;<b>Service Tax Invoice - New</b>
    </div>

    <div class="mb-6 p-4 border border-gray-200 rounded-lg">
        <form id="searchForm" hx-get="{% url 'accounts:purchaseorder_table_partial' %}" hx-target="#poTable-container" hx-swap="innerHTML" hx-trigger="submit">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div class="flex-grow">
                    {{ search_form.search_by.label_tag }}
                    {{ search_form.search_by }}
                </div>
                <div id="search-input-container" class="flex-grow-2" hx-swap="outerHTML">
                    {% include 'accounts/servicetaxinvoice/_search_input_fields.html' with search_form=search_form %}
                </div>
                <div class="flex-shrink-0">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                        Search
                    </button>
                </div>
            </div>
        </form>
        <div id="autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded shadow-lg mt-1 w-80"></div>
    </div>
    
    <div id="poTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'accounts:purchaseorder_table_partial' %}"
         hx-indicator="#table-loading-indicator"
         hx-swap="innerHTML">
        <!-- Loading indicator -->
        <div id="table-loading-indicator" class="htmx-indicator text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Purchase Orders...</p>
        </div>
    </div>
    
    <!-- Global Modal for forms/selection -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         x-data="{ showModal: false }" x-show="showModal"
         @show-modal.window="showModal = true" @close-modal.window="showModal = false"
         @click.self="showModal = false"
         hx-on::after-swap="if(event.detail.target.id == 'modalContent') $dispatch('show-modal')">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full max-h-screen overflow-y-auto"
             @click.stop>
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('customerAutocomplete', () => ({
            query: '',
            results: [],
            showResults: false,
            init() {
                this.$watch('query', (value) => {
                    if (value.length >= 1) {
                        // HTMX handles fetching, we just need to react to its results
                        // and manage visibility.
                        // For auto-completion, we're relying on HTMX hx-target and hx-swap
                        // directly to update the #autocomplete-results div.
                    } else {
                        this.results = [];
                        this.showResults = false;
                    }
                });
                document.addEventListener('click', (e) => {
                    if (!this.$el.contains(e.target)) {
                        this.showResults = false;
                    }
                });
            },
            selectResult(result) {
                document.getElementById('txtCustName').value = result;
                this.query = result;
                this.showResults = false;
                // Trigger an input event so HTMX sees the change if needed for next steps
                document.getElementById('txtCustName').dispatchEvent(new Event('input', { bubbles: true }));
                // Trigger search implicitly
                document.getElementById('searchForm').requestSubmit();
            }
        }));

        // Event listener for the WO selection modal
        document.body.addEventListener('showWoSelectAlert', function() {
            alert('Please select a Work Order.');
        });
        document.body.addEventListener('closeModal', function() {
            document.getElementById('modal')._x_data_stack[0].showModal = false; // Access Alpine data directly
        });
    });

    // Handle HTMX after swap event for the autocomplete results
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'autocomplete-results') {
            const resultsDiv = document.getElementById('autocomplete-results');
            if (resultsDiv.innerHTML.trim() !== '') {
                // Assuming Alpine.js is watching this.showResults, we can directly manipulate it
                // Or if we want to integrate more deeply, make the autocomplete-results an Alpine component
                document.getElementById('modal')._x_data_stack[0].showResults = true; // This is a hacky way, better to use Alpine data on the input itself
            } else {
                document.getElementById('modal')._x_data_stack[0].showResults = false;
            }
        }
    });

</script>
{% endblock %}
```

**`_search_input_fields.html`** (Partial for search input fields, dynamically swapped by HTMX)

```html
<div id="search-input-container">
    {% if search_form.search_by.value == '0' %}
        {{ search_form.customer_name }}
        <div id="autocomplete-results" class="absolute bg-white border border-gray-300 rounded shadow-lg mt-1 z-10 w-80"
             x-data="customerAutocomplete" x-show="showResults" @click.outside="showResults = false">
            <template x-for="result in results" :key="result">
                <div @click="selectResult(result)" x-text="result" class="p-2 cursor-pointer hover:bg-gray-100"></div>
            </template>
        </div>
    {% else %}
        {{ search_form.po_no }}
    {% endif %}
</div>
```

**`_po_list_table.html`** (Partial for the DataTables content, loaded via HTMX)

```html
<div class="overflow-x-auto rounded-lg shadow">
    <table id="purchaseOrderTable" class="min-w-full bg-white table-auto">
        <thead>
            <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <th class="py-2 px-4 border-b border-gray-200 w-2%">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 w-5%">FinYear</th>
                <th class="py-2 px-4 border-b border-gray-200 w-22%">Name of Customer</th>
                <th class="py-2 px-4 border-b border-gray-200 w-5%">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 w-5%">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 w-5%">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 w-3%">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in purchase_orders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.fin_year.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_customer_name_with_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.pono }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200" id="wo-column-{{ obj.poid }}">
                    {% include 'accounts/servicetaxinvoice/_wo_display_and_input.html' with obj=obj %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'accounts:purchaseorder_select_redirect' obj.poid %}"
                        hx-swap="none" {# We want a full redirect if WO is selected, otherwise handle client-side alert #}
                        _="on htmx:responseError alert('An error occurred during selection.')"
                        hx-on::after-request="if(event.detail.xhr.status === 200 && event.detail.xhr.headers['HX-Trigger'] === 'showWoSelectAlert') alert('Select WONo.');"
                    >
                        Select
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-8 px-4 text-center text-lg text-maroon-700">
                    No data to display !
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#purchaseOrderTable').DataTable({
            "pageLength": 18, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 18, 25, 50, -1], [10, 18, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 5, 6] } // Disable sorting for SN, WO No, Actions
            ]
        });
    });
</script>
```

**`_wo_display_and_input.html`** (Partial for the Work Order column content, to be swapped OOB)

```html
<div id="wo-display-{{ obj.poid }}">
    {% if obj.selected_wo_text %}
        {{ obj.selected_wo_text }}
    {% else %}
        No WO Selected
    {% endif %}
</div>
<input type="hidden" id="wo-hidden-{{ obj.poid }}" name="selected_wo_ids" value="{{ obj.selected_wo_ids }}">
<button
    class="text-blue-500 hover:text-blue-700 text-sm py-1 px-2 rounded mt-1"
    hx-get="{% url 'accounts:purchaseorder_wo_select_modal' obj.poid %}"
    hx-target="#modalContent"
    hx-trigger="click"
    _="on click add .is-active to #modal $dispatch('show-modal')"
>
    Change WO
</button>
```

**`_wo_select_modal.html`** (Partial for the Work Order selection modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Select Work Orders for PO: {{ purchase_order.pono }}</h3>
    <form hx-post="{% url 'accounts:purchaseorder_wo_select_modal' purchase_order.poid %}"
          hx-swap="none" {# No swap on modal itself, trigger OOB for table row #}
          hx-indicator="#modal-loading-indicator"
          hx-on::after-request="if(event.detail.xhr.status >= 200 && event.detail.xhr.status < 300) $dispatch('close-modal')">
        {% csrf_token %}
        
        <div class="max-h-60 overflow-y-auto border border-gray-300 p-3 rounded-md space-y-2">
            {% for choice in form.selected_wo_ids %}
                <div class="flex items-center">
                    {{ choice.tag }}
                    <label for="{{ choice.id_for_label }}" class="ml-2 text-sm text-gray-700">{{ choice.choice_label }}</label>
                </div>
            {% endfor %}
        </div>
        
        {% if form.errors %}
            <div class="text-red-500 text-xs mt-2">
                {% for field, errors in form.errors.items %}
                    {% for error in errors %}
                        <p>{{ field }}: {{ error }}</p>
                    {% endfor %}
                {% endfor %}
            </div>
        {% endif %}

        <div id="modal-loading-indicator" class="htmx-indicator text-center mt-4">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-1 text-gray-600">Updating...</p>
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click $dispatch('close-modal')">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Apply Selection
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

```python
from django.urls import path
from .views import (
    ServiceTaxInvoiceListView,
    PurchaseOrderTablePartialView,
    CustomerAutocompleteView,
    ToggleSearchFieldView,
    WorkOrderSelectModalView,
    SelectPoAndRedirectView,
)

app_name = 'accounts' # Define app_name for namespacing URLs

urlpatterns = [
    # Main page for listing service tax invoices (Purchase Orders)
    path('servicetaxinvoice/', ServiceTaxInvoiceListView.as_view(), name='servicetaxinvoice_list'),
    
    # HTMX endpoint to load/refresh the Purchase Order table
    path('servicetaxinvoice/table/', PurchaseOrderTablePartialView.as_view(), name='purchaseorder_table_partial'),
    
    # HTMX endpoint for customer autocomplete
    path('servicetaxinvoice/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
    
    # HTMX endpoint to dynamically toggle search input fields (Customer Name vs PO No)
    path('servicetaxinvoice/toggle-search-field/', ToggleSearchFieldView.as_view(), name='purchaseorder_toggle_search_field'),

    # HTMX endpoint for Work Order selection modal
    path('servicetaxinvoice/po/<int:po_id>/select-wo/', WorkOrderSelectModalView.as_view(), name='purchaseorder_wo_select_modal'),
    
    # Endpoint to handle 'Select' action and redirect
    path('servicetaxinvoice/po/<int:po_id>/select/', SelectPoAndRedirectView.as_view(), name='purchaseorder_select_redirect'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests covering models and views are crucial for ensuring the migration's success and maintainability.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.test.utils import override_settings
from decimal import Decimal
import datetime

from .models import (
    FinancialYearMaster, CustomerMaster, PurchaseOrder,
    PurchaseOrderDetail, ServiceTaxInvoiceMaster, ServiceTaxInvoiceDetail,
    WorkOrderMaster
)

# Helper to add session to request in tests
def add_session_to_request(request, session_data=None):
    middleware = SessionMiddleware(lambda req: None)
    middleware.process_request(request)
    request.session.save()
    if session_data:
        for key, value in session_data.items():
            request.session[key] = value

class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.fin_year = FinancialYearMaster.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.customer = CustomerMaster.objects.create(customer_id=101, customer_name='Test Customer A', comp_id=1)
        cls.customer_b = CustomerMaster.objects.create(customer_id=102, customer_name='Test Customer B', comp_id=1)

        # Create PO with remaining quantity > 0
        cls.po_with_remaining = PurchaseOrder.objects.create(
            poid=1, pono='PO001', customer=cls.customer,
            sys_date=datetime.datetime.now(), podate=datetime.datetime.now(),
            fin_year=cls.fin_year, comp_id=1
        )
        cls.po_detail_1 = PurchaseOrderDetail.objects.create(id=1001, po=cls.po_with_remaining, total_qty=Decimal('100.00'))
        
        # Partially invoiced PO
        cls.po_partially_invoiced = PurchaseOrder.objects.create(
            poid=2, pono='PO002', customer=cls.customer_b,
            sys_date=datetime.datetime.now(), podate=datetime.datetime.now(),
            fin_year=cls.fin_year, comp_id=1
        )
        cls.po_detail_2 = PurchaseOrderDetail.objects.create(id=1002, po=cls.po_partially_invoiced, total_qty=Decimal('50.00'))
        cls.st_invoice_master_2 = ServiceTaxInvoiceMaster.objects.create(id=2002, po=cls.po_partially_invoiced, comp_id=1)
        ServiceTaxInvoiceDetail.objects.create(id=3002, service_tax_invoice_master=cls.st_invoice_master_2, item=cls.po_detail_2, req_qty=Decimal('20.00'))

        # Fully invoiced PO (should not appear in list)
        cls.po_fully_invoiced = PurchaseOrder.objects.create(
            poid=3, pono='PO003', customer=cls.customer,
            sys_date=datetime.datetime.now(), podate=datetime.datetime.now(),
            fin_year=cls.fin_year, comp_id=1
        )
        cls.po_detail_3 = PurchaseOrderDetail.objects.create(id=1003, po=cls.po_fully_invoiced, total_qty=Decimal('75.00'))
        cls.st_invoice_master_3 = ServiceTaxInvoiceMaster.objects.create(id=2003, po=cls.po_fully_invoiced, comp_id=1)
        ServiceTaxInvoiceDetail.objects.create(id=3003, service_tax_invoice_master=cls.st_invoice_master_3, item=cls.po_detail_3, req_qty=Decimal('75.00'))

        # Work orders for PO001
        cls.wo_1a = WorkOrderMaster.objects.create(id=1, won_o='WO-A', task_project_title='Project Alpha', po=cls.po_with_remaining, comp_id=1)
        cls.wo_1b = WorkOrderMaster.objects.create(id=2, won_o='WO-B', task_project_title='Project Beta', po=cls.po_with_remaining, comp_id=1)


    def setUp(self):
        self.client = Client()
        # Mock session data for company and financial year IDs
        self.session_data = {'compid': 1, 'finyear': 2023}
        self.request = self.client.get('/')
        add_session_to_request(self.request, self.session_data)
        self.client.cookies = self.request.session.session_key # Store session_key in client cookies

    def test_purchase_order_remaining_quantity(self):
        # Test PO with remaining quantity > 0
        self.assertGreater(self.po_with_remaining.get_remaining_invoice_quantity(self.request), Decimal('0'))
        self.assertEqual(self.po_with_remaining.get_remaining_invoice_quantity(self.request), Decimal('100.000'))

        # Test partially invoiced PO
        self.assertGreater(self.po_partially_invoiced.get_remaining_invoice_quantity(self.request), Decimal('0'))
        self.assertEqual(self.po_partially_invoiced.get_remaining_invoice_quantity(self.request), Decimal('30.000')) # 50 - 20

        # Test fully invoiced PO
        self.assertEqual(self.po_fully_invoiced.get_remaining_invoice_quantity(self.request), Decimal('0.000'))

    def test_purchase_order_formatted_date(self):
        self.assertEqual(self.po_with_remaining.get_formatted_sys_date(), self.po_with_remaining.sys_date.strftime('%d/%m/%Y'))

    def test_purchase_order_customer_name_with_id(self):
        self.assertEqual(self.po_with_remaining.get_customer_name_with_id(), f"{self.customer.customer_name} [{self.customer.customer_id}]")

    def test_work_order_display_text(self):
        self.assertEqual(self.wo_1a.get_display_text(), 'WO-A-Project Alpha')

class ServiceTaxInvoiceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for views
        cls.fin_year = FinancialYearMaster.objects.create(fin_year_id=2023, fin_year='2023-24')
        cls.customer = CustomerMaster.objects.create(customer_id=101, customer_name='Test Customer', comp_id=1)
        cls.customer_search = CustomerMaster.objects.create(customer_id=102, customer_name='Search Customer', comp_id=1)

        cls.po1 = PurchaseOrder.objects.create(
            poid=1, pono='PO-001', customer=cls.customer,
            sys_date=datetime.datetime.now(), podate=datetime.datetime.now(),
            fin_year=cls.fin_year, comp_id=1
        )
        PurchaseOrderDetail.objects.create(id=1001, po=cls.po1, total_qty=Decimal('100.00'))

        cls.po2 = PurchaseOrder.objects.create(
            poid=2, pono='PO-002', customer=cls.customer_search,
            sys_date=datetime.datetime.now(), podate=datetime.datetime.now(),
            fin_year=cls.fin_year, comp_id=1
        )
        PurchaseOrderDetail.objects.create(id=1002, po=cls.po2, total_qty=Decimal('50.00'))

        cls.wo_po1_a = WorkOrderMaster.objects.create(id=10, won_o='WO-X', task_project_title='X Proj', po=cls.po1, comp_id=1)
        cls.wo_po1_b = WorkOrderMaster.objects.create(id=11, won_o='WO-Y', task_project_title='Y Proj', po=cls.po1, comp_id=1)

    def setUp(self):
        self.client = Client()
        # Mock session data
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_servicetaxinvoice_list_view(self):
        response = self.client.get(reverse('accounts:servicetaxinvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/list.html')
        self.assertIn('search_form', response.context)

    def test_purchaseorder_table_partial_view_no_search(self):
        response = self.client.get(reverse('accounts:purchaseorder_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_po_list_table.html')
        self.assertIn('purchase_orders', response.context)
        self.assertEqual(len(response.context['purchase_orders']), 2) # PO1 and PO2 should be listed

    def test_purchaseorder_table_partial_view_search_by_customer_name(self):
        # Search by full customer name
        response = self.client.get(reverse('accounts:purchaseorder_table_partial'), {'search_by': '0', 'customer_name': 'Test Customer [101]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['purchase_orders']), 1)
        self.assertEqual(response.context['purchase_orders'][0].poid, self.po1.poid)

        # Search by partial customer name (no ID)
        response = self.client.get(reverse('accounts:purchaseorder_table_partial'), {'search_by': '0', 'customer_name': 'Search'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['purchase_orders']), 1)
        self.assertEqual(response.context['purchase_orders'][0].poid, self.po2.poid)

    def test_purchaseorder_table_partial_view_search_by_po_no(self):
        response = self.client.get(reverse('accounts:purchaseorder_table_partial'), {'search_by': '1', 'po_no': 'PO-002'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['purchase_orders']), 1)
        self.assertEqual(response.context['purchase_orders'][0].poid, self.po2.poid)
    
    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('accounts:customer_autocomplete'), {'query': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn(str(self.customer), response.json())
        self.assertNotIn(str(self.customer_search), response.json())

        response = self.client.get(reverse('accounts:customer_autocomplete'), {'query': 'Search'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(str(self.customer_search), response.json())
        self.assertNotIn(str(self.customer), response.json())

    def test_toggle_search_field_view(self):
        response = self.client.get(reverse('accounts:purchaseorder_toggle_search_field'), {'search_by': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_search_input_fields.html')
        self.assertContains(response, 'id="txtpoNo"')
        self.assertNotContains(response, 'id="txtCustName"')

        response = self.client.get(reverse('accounts:purchaseorder_toggle_search_field'), {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_search_input_fields.html')
        self.assertContains(response, 'id="txtCustName"')
        self.assertNotContains(response, 'id="txtpoNo"')

    def test_work_order_select_modal_view_get(self):
        response = self.client.get(reverse('accounts:purchaseorder_wo_select_modal', args=[self.po1.poid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/servicetaxinvoice/_wo_select_modal.html')
        self.assertContains(response, 'WO-X-Project Alpha')
        self.assertContains(response, 'WO-Y-Project Beta')
        self.assertIn('form', response.context)
        self.assertIn('purchase_order', response.context)

    def test_work_order_select_modal_view_post(self):
        # Test selecting one WO
        data = {'selected_wo_ids': [self.wo_po1_a.id]}
        response = self.client.post(reverse('accounts:purchaseorder_wo_select_modal', args=[self.po1.poid]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        # Check OOB swap header
        self.assertIn('HX-Swap-OOB', response.headers)
        self.assertContains(response, self.wo_po1_a.get_display_text())
        self.assertContains(response, f'id="wo-hidden-{self.po1.poid}" name="selected_wo_ids" value="{self.wo_po1_a.id}"')
        # Verify session was updated
        self.assertEqual(self.client.session.get(f'selected_wo_for_po_{self.po1.poid}_{self.client.session.session_key}'), str(self.wo_po1_a.id))

        # Test selecting multiple WOs
        data = {'selected_wo_ids': [self.wo_po1_a.id, self.wo_po1_b.id]}
        response = self.client.post(reverse('accounts:purchaseorder_wo_select_modal', args=[self.po1.poid]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('HX-Swap-OOB', response.headers)
        self.assertContains(response, self.wo_po1_a.get_display_text())
        self.assertContains(response, self.wo_po1_b.get_display_text())
        self.assertContains(response, f'id="wo-hidden-{self.po1.poid}" name="selected_wo_ids" value="{self.wo_po1_a.id},{self.wo_po1_b.id}"')
        self.assertEqual(self.client.session.get(f'selected_wo_for_po_{self.po1.poid}_{self.client.session.session_key}'), f'{self.wo_po1_a.id},{self.wo_po1_b.id}')

    def test_select_po_and_redirect_view_success(self):
        # First, set a selected WO in session
        session_key = f'selected_wo_for_po_{self.po1.poid}_{self.client.session.session_key}'
        self.client.session[session_key] = str(self.wo_po1_a.id)
        self.client.session.save()

        # Simulate clicking 'Select'
        response = self.client.get(reverse('accounts:purchaseorder_select_redirect', args=[self.po1.poid]), follow=True)
        self.assertEqual(response.status_code, 200) # Should be 200 after redirect following 302
        # Check if it redirected to the correct URL (mocking next page name)
        self.assertRedirects(response, f"/service-tax-invoice/details/?poid={self.po1.poid}&wn={self.wo_po1_a.id}&pn={self.po1.pono}&date={self.po1.get_formatted_sys_date()}&cid={self.po1.customer.customer_id}&ModId=11&SubModId=52")
        # Verify selected WO was cleared from session
        self.assertNotIn(session_key, self.client.session)

    def test_select_po_and_redirect_view_no_wo_selected(self):
        # Do not set selected WO in session
        response = self.client.get(reverse('accounts:purchaseorder_select_redirect', args=[self.po1.poid]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTTP 200 for HTMX client-side action
        # Check for HX-Trigger for alert
        self.assertEqual(response.headers.get('HX-Trigger'), 'showWoSelectAlert')
        self.assertNotIn(f'selected_wo_for_po_{self.po1.poid}_{self.client.session.session_key}', self.client.session)

```

### Step 5: HTMX and Alpine.js Integration

**Summary of Integration:**

1.  **Full Page Load (`list.html`):** Loads the initial page structure and an empty container for the Purchase Order table (`#poTable-container`).
2.  **Initial Data Load:** `hx-get="{% url 'accounts:purchaseorder_table_partial' %}" hx-trigger="load"` on `#poTable-container` fetches the initial table content.
3.  **Search Functionality:**
    *   The `search_form` is wrapped in an HTMX form.
    *   `hx-get="{% url 'accounts:purchaseorder_table_partial' %}" hx-target="#poTable-container" hx-swap="innerHTML" hx-trigger="submit"` on the `searchForm` ensures that submitting the form reloads only the table content.
    *   `DropDownList1` (now `search_by` field) has `hx-get="{% url 'accounts:purchaseorder_toggle_search_field' %}" hx-target="#search-input-container" hx-swap="outerHTML"` to dynamically show/hide the `customer_name` or `po_no` input field.
    *   `txtCustName` has `hx-get` to `customer_autocomplete` view with `hx-trigger="keyup changed delay:500ms"` to provide live autocomplete suggestions. Alpine.js manages the display and selection of these suggestions.
4.  **Work Order (WO) Selection:**
    *   In `_po_list_table.html`, the "Change WO" button inside each row's WO column triggers `hx-get="{% url 'accounts:purchaseorder_wo_select_modal' obj.poid %}" hx-target="#modalContent"` to load the WO selection form into a global modal. Alpine.js manages the modal's visibility (`@show-modal.window`, `@close-modal.window`).
    *   The modal's form (`_wo_select_modal.html`) uses `hx-post` back to the same `WorkOrderSelectModalView`.
    *   On successful POST, the `WorkOrderSelectModalView` sends an `HX-Swap-OOB` header (`HX-Swap-OOB: outerHTML:#wo-column-{{ po_id }}`) and renders `_wo_display_and_input.html`. This ensures that *only* the specific WO column in the *original table row* is updated with the selected WO text and hidden IDs, without reloading the entire table. A `HX-Trigger: closeModal` is also sent to signal Alpine.js to close the modal.
5.  **"Select" Action:**
    *   The "Select" button on each row in `_po_list_table.html` triggers `hx-get="{% url 'accounts:purchaseorder_select_redirect' obj.poid %}" hx-swap="none"`.
    *   If Work Orders are selected (checked in session), the view performs a standard Django redirect (`HttpResponseRedirect`).
    *   If no Work Orders are selected, the view returns an `HttpResponse` with an `HX-Trigger: showWoSelectAlert` header. An Alpine.js event listener `document.body.addEventListener('showWoSelectAlert', ...)` catches this event and displays an `alert('Select WONo.')` to the user, replicating the original ASP.NET behavior.
6.  **DataTables:**
    *   Initialized on `#purchaseOrderTable` after it's loaded via HTMX. The `$(document).ready` is inside the partial template to ensure it runs *after* HTMX swaps the content.
7.  **General HTMX:**
    *   `hx-indicator` used for loading spinners.
    *   `hx-swap="innerHTML"` for replacing content, `hx-swap="outerHTML"` for replacing the element itself.
    *   `hx-trigger="load, refreshPurchaseOrderList from:body"` allows refreshing the table on page load and via custom events (e.g., after future CRUD operations on related data).

This comprehensive plan covers all aspects of the migration, adhering to modern Django practices and leveraging HTMX/Alpine.js for a seamless user experience, while keeping business logic firmly in the models.