## ASP.NET to Django Conversion Script: Sundry Creditors Module Modernization

This modernization plan outlines the strategic transition of the ASP.NET Sundry Creditors module to a robust, scalable Django 5.0+ application. Our approach leverages AI-assisted automation, focusing on creating a system that is easy to maintain, extend, and understand, even for non-technical stakeholders. By adopting a "Fat Model, Thin View" architecture, HTMX for dynamic interactions, and DataTables for data presentation, we ensure a highly efficient and user-friendly experience.

### Business Value Proposition:

This modernization will deliver significant business benefits:
- **Improved Performance:** Eliminate full page reloads, resulting in a snappier, more responsive user interface.
- **Reduced Maintenance Costs:** Transition from a complex, often fragile ASP.NET Web Forms codebase to a streamlined, Python-based Django application that is inherently more readable and maintainable.
- **Enhanced Scalability:** Django's robust framework is designed to handle increasing user loads and data volumes, ensuring your application grows with your business needs.
- **Future-Proofing:** Move away from legacy technologies to a modern, open-source stack with a large, active community and continuous development.
- **Streamlined Development:** By adopting clear architectural patterns (Fat Models, Thin Views) and a consistent technology stack (Django, HTMX, Alpine.js), future feature development becomes faster and more predictable.
- **Better User Experience:** Interactive search, filtering, and pagination without full page refreshes improve user satisfaction and productivity.
- **Simplified Data Management:** Centralize complex business logic within Django models, making data interactions clearer and less error-prone.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following primary tables and their inferred columns relevant to the Sundry Creditors module:

**`tblMM_Supplier_master` (Supplier Information)**
- **Purpose:** Stores details about suppliers.
- **Columns:**
    - `SupId` (Integer, likely Primary Key)
    - `SupplierId` (String, unique identifier/code for the supplier)
    - `SupplierName` (String, name of the supplier)
    - `CompId` (Integer, Company ID)

**`tblACC_Creditors_Master` (Creditor Account Balances)**
- **Purpose:** Stores opening balances for creditors (suppliers).
- **Columns:**
    - `Id` (Integer, inferred Primary Key for this table)
    - `SupplierId` (String, links to `tblMM_Supplier_master.SupplierId`)
    - `OpeningAmt` (Double/Float, opening balance amount)
    - `CompId` (Integer, Company ID)
    - `FinYearId` (Integer, Financial Year ID)

**Inferred Relationships:**
- `tblMM_Supplier_master.SupplierId` is linked to `tblACC_Creditors_Master.SupplierId`.
- Other transaction tables (e.g., for booked bills, payments, cash payments) are implied by the `fun.FillGrid_CreditorsBookedBill`, `fun.FillGrid_CreditorsPayment`, `fun.FillGrid_CreditorsCashPayment` functions, but their specific schema is not directly exposed in the provided code. For this migration, we will model the core entities and use placeholder logic for the complex financial calculations, demonstrating where the "fat model" approach would encapsulate this business logic.

### Step 2: Identify Backend Functionality

The ASP.NET module provides the following core functionalities:

-   **Read (Display List):** The `GridView1` is populated by the `FillGrid_Creditors()` method. This method queries `tblMM_Supplier_master` and `tblACC_Creditors_Master`, and then calls several external functions (`fun.FillGrid_CreditorsBookedBill`, `fun.FillGrid_CreditorsPayment`, `fun.FillGrid_CreditorsCashPayment`) to calculate aggregated Debit and Credit amounts for each supplier. Only suppliers with a `BookBillAmt` greater than 0 are displayed. The `Page_Load` event initializes this display, including calculating and showing `Credit Total`, `Debit Total`, and `Closing Bal`.
-   **Search/Filter:** Users can search by "Supplier Name" using `TextBox1` and `btn_Search_Click`. The search applies a filter to the `FillGrid_Creditors()` logic.
-   **Autocomplete:** The `AutoCompleteExtender` uses the `sql3` web method to provide suggestions for "Supplier Name" as the user types.
-   **Drill-Down:** Clicking on a `SupplierName` (`LinkButton` with `CommandName="LnkBtn"`) redirects to `SundryCreditors_InDetailList.aspx` with the `SupplierId` and other context.
-   **Export:** The `Button2_Click` event exports the currently displayed data (`ViewState["ToExport"]`) to an Excel file.
-   **Cancel/Navigation:** `Button1_Click` redirects to `SundryCreditors.aspx`, and `btnCancel_Click` (though not used in the ASPX, it's present in C#) redirects to `Dashboard.aspx`.

### Step 3: Infer UI Components

The ASP.NET controls translate to Django concepts as follows:

-   **`GridView1`**: This will be migrated to an HTML `<table>` element, dynamically enhanced by **DataTables.js** for client-side sorting, filtering, and pagination. The data will be loaded via **HTMX** into a partial template.
-   **`TextBox1` (Supplier Name)**: A standard HTML `<input type="text">` with **HTMX** attributes to trigger live search/autocomplete requests to a Django view.
-   **`AutoCompleteExtender`**: This AJAX functionality will be replaced by **HTMX** requests (`hx-get`) to a dedicated Django `JsonResponse` view, with results displayed in a dynamic dropdown or list (possibly styled with **Alpine.js** for UI state).
-   **`btn_Search`**: An HTML `<button>` with **HTMX** attributes to trigger a `hx-get` request to refresh the `_table.html` partial, passing the search parameter.
-   **`Button2` (Export)**: An HTML `<button>` or `<a>` tag linked to a Django view that generates and serves an Excel file.
-   **`Button1` (Cancel) / `btnCancel_Click`**: Standard HTML `<a>` tags for navigation to other Django URLs.
-   **`lblOf`, `lblTotal`, `lblTotal1`, `lblTotal2`**: These `asp:Label` controls will be replaced by dynamic values rendered directly in the Django template, updated as part of the HTMX-driven partial table render.
-   **`LinkButton` (`lblTerms` for SupplierName)**: An HTML `<a>` tag or `<button>` that triggers navigation to the detailed view, passing the supplier ID.

---

### Step 4: Generate Django Code

The following sections detail the conversion into distinct Django application files, adhering to the "Fat Model, Thin View" principle and modern Django practices. We will assume an app name `accounts` for this module.

#### 4.1 Models (accounts/models.py)

We define two models to map to the identified database tables: `Supplier` and `CreditorAccount`. The complex financial aggregation logic (from `FillGrid_Creditors`) is encapsulated within methods of the `Supplier` model, making it "fat".

```python
# accounts/models.py
from django.db import models
from django.db.models import Sum
# from transactions.models import PurchaseInvoice, Payment  # Example of related models if they existed

class Supplier(models.Model):
    # Maps to tblMM_Supplier_master
    sup_id = models.IntegerField(db_column='SupId', primary_key=True)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, unique=True, help_text="Unique ID for the supplier")
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', help_text="Company ID associated with the supplier")

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'
        ordering = ['supplier_name'] # Default ordering

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

    # Business logic methods for sundry creditor details (simulating C# 'fun' class)
    def get_opening_balance(self, comp_id, fin_year_id):
        """
        Retrieves the opening balance for this supplier from CreditorAccount.
        In a real system, this would fetch the relevant entry based on date/financial year logic.
        """
        try:
            # Assumes CreditorAccount has a single relevant entry per supplier_id, comp_id, fin_year_id
            creditor_acc = CreditorAccount.objects.filter(
                supplier_id=self.supplier_id,
                comp_id=comp_id,
                fin_year_id=fin_year_id
            ).first() # Or .latest('date_field') if applicable
            
            if creditor_acc and creditor_acc.opening_amount is not None:
                return round(float(creditor_acc.opening_amount), 2)
        except CreditorAccount.DoesNotExist:
            pass
        return 0.0

    def get_booked_bill_amount(self, comp_id, fin_year_id, category):
        """
        Simulates fun.FillGrid_CreditorsBookedBill.
        This would involve querying related transaction tables (e.g., purchase invoices, bills issued to this supplier).
        Example: self.purchaseinvoice_set.filter(comp_id=comp_id, fin_year_id=fin_year_id, category=category).aggregate(Sum('amount'))['amount__sum'] or similar.
        """
        # Placeholder logic: Replace with actual database queries and aggregation.
        # This should return the sum of all credit-related transactions (e.g., bills booked against the supplier).
        return round(self.pk * 100.50, 2) if self.pk % 2 == 0 else round(self.pk * 150.75, 2)

    def get_payment_amount(self, comp_id, fin_year_id, category):
        """
        Simulates fun.FillGrid_CreditorsPayment (non-cash payments).
        This would involve querying related payment tables (e.g., bank payments).
        """
        # Placeholder logic: Replace with actual database queries and aggregation.
        return round(self.pk * 50.25, 2)

    def get_cash_payment_amount(self, comp_id, fin_year_id, category):
        """
        Simulates fun.FillGrid_CreditorsCashPayment.
        This would involve querying related cash payment tables.
        """
        # Placeholder logic: Replace with actual database queries and aggregation.
        return round(self.pk * 10.75, 2)

    def get_sundry_creditor_summary(self, comp_id, fin_year_id, category):
        """
        Aggregates all financial data for this supplier, similar to the logic within
        the C# FillGrid_Creditors method for a single row.
        Returns a dictionary representing a summary row for the display grid.
        """
        opening_amt = self.get_opening_balance(comp_id, fin_year_id)
        booked_bill_amt = self.get_booked_bill_amount(comp_id, fin_year_id, category)
        payment_amt = self.get_payment_amount(comp_id, fin_year_id, category)
        cash_payment_amt = self.get_cash_payment_amount(comp_id, fin_year_id, category)

        total_payment_amt = round(payment_amt + cash_payment_amt, 2) # Total Debit
        
        # Credit total includes opening balance and booked bills
        credit_total_for_supplier = round(booked_bill_amt + opening_amt, 2)

        debit_total_for_supplier = total_payment_amt
        closing_balance = round(credit_total_for_supplier - debit_total_for_supplier, 2)

        return {
            'id': self.pk,
            'supplier_name': self.supplier_name,
            'supplier_id': self.supplier_id,
            'opening_amt': opening_amt,
            'booked_bill_amt': booked_bill_amt,
            'payment_amt': debit_total_for_supplier, # This maps to 'Debit' column in ASP.NET
            'credit_amount_display': credit_total_for_supplier, # This maps to 'Credit' column in ASP.NET
            'credit_total_for_supplier': credit_total_for_supplier, # Used for overall sum calculation
            'debit_total_for_supplier': debit_total_for_supplier, # Used for overall sum calculation
            'closing_balance': closing_balance,
        }

class CreditorAccount(models.Model):
    # Maps to tblACC_Creditors_Master
    creditor_id = models.AutoField(db_column='Id', primary_key=True) # Assuming an auto-incrementing PK
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # Foreign key to Supplier.supplier_id
    opening_amount = models.FloatField(db_column='OpeningAmt')
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblACC_Creditors_Master'
        verbose_name = 'Creditor Account'
        verbose_name_plural = 'Creditor Accounts'

    def __str__(self):
        return f"Creditor Acc for {self.supplier_id} (Opening: {self.opening_amount})"

```

#### 4.2 Forms (accounts/forms.py)

For this specific Sundry Creditors Details page, we don't have a traditional data entry form for CRUD operations. The search functionality is handled directly by an input field in the template and HTMX.

```python
# accounts/forms.py
# No dedicated forms are needed for this particular reporting/list view.
# Form functionality is handled via simple input fields and HTMX in templates.
```

#### 4.3 Views (accounts/views.py)

Views are kept "thin" (aiming for 5-15 lines per method) by delegating complex data aggregation to the models. We use `LoginRequiredMixin` for security.

```python
# accounts/views.py
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.db.models import Q
from django.contrib import messages
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming authentication is in place

import openpyxl # For Excel export
from openpyxl.styles import Font, Alignment, Border, Side
from io import BytesIO

from .models import Supplier, CreditorAccount

class SundryCreditorListView(LoginRequiredMixin, TemplateView):
    """
    Main view for Sundry Creditors details page.
    This page loads the initial layout and triggers HTMX to load the table content.
    Corresponds to SundryCreditors_Details.aspx.
    """
    template_name = 'accounts/sundry_creditors/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get 'lnkFor' from query string, similar to Request.QueryString["lnkFor"]
        context['category'] = self.request.GET.get('lnkFor', 'General')
        return context

class SundryCreditorTablePartialView(LoginRequiredMixin, View):
    """
    HTMX endpoint to render the sundry creditors table.
    Handles search filtering and data aggregation for display.
    Corresponds to the data loading and binding logic in FillGrid_Creditors().
    """
    def get(self, request, *args, **kwargs):
        supplier_search_term = request.GET.get('supplier_name_search', '').strip()
        category = request.GET.get('lnkFor', 'General')

        # Retrieve session context similar to C# Session variables
        # These would typically be part of user's profile or session management in Django.
        comp_id = request.session.get('compid') # Example: request.user.profile.comp_id
        fin_year_id = request.session.get('finyear') # Example: request.user.profile.fin_year_id

        if not comp_id or not fin_year_id:
            messages.error(request, "Company ID or Financial Year not found in session. Please log in again.")
            return render(request, 'accounts/sundry_creditors/_table.html', {'sundry_creditor_summaries': []})

        # Start with all suppliers (filtered by company if necessary, but C# code implies CompId in inner queries)
        suppliers_queryset = Supplier.objects.all()

        # Apply search filter
        if supplier_search_term:
            # The ASP.NET code used fun.getCode(TextBox1.Text) which implies converting name to ID.
            # Assuming the search term might be 'Name [ID]' or just 'Name'.
            # We search against both name and ID for flexibility.
            id_part = supplier_search_term.split('[')[-1].strip(']') if '[' in supplier_search_term else ''
            
            suppliers_queryset = suppliers_queryset.filter(
                Q(supplier_name__icontains=supplier_search_term) |
                Q(supplier_id__icontains=id_part)
            )

        sundry_creditor_summaries = []
        total_credit_sum = 0.0
        total_debit_sum = 0.0

        for supplier in suppliers_queryset:
            summary = supplier.get_sundry_creditor_summary(comp_id, fin_year_id, category)
            # Apply the C# filter: if (BookBillAmt > 0)
            if summary['booked_bill_amt'] > 0:
                sundry_creditor_summaries.append(summary)
                total_credit_sum += summary['credit_total_for_supplier']
                total_debit_sum += summary['debit_total_for_supplier']
        
        context = {
            'sundry_creditor_summaries': sundry_creditor_summaries,
            'total_credit_sum': round(total_credit_sum, 2),
            'total_debit_sum': round(total_debit_sum, 2),
            'closing_balance_sum': round(total_credit_sum - total_debit_sum, 2),
            'category': category,
        }
        return render(request, 'accounts/sundry_creditors/_table.html', context)

class SupplierAutoCompleteView(LoginRequiredMixin, View):
    """
    HTMX endpoint for supplier name autocomplete functionality.
    Corresponds to the static web method sql3 in the C# code-behind.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        context_key = request.GET.get('contextKey', 'key1') # 'key1' for supplier as per ASPX TextBox1
        
        comp_id = request.session.get('compid')

        if not comp_id:
            return JsonResponse([], safe=False)

        suggestions = []
        if context_key == 'key1': # Based on ASPX 'key1' for Supplier
            # Assuming Supplier model covers tblMM_Supplier_master
            queryset = Supplier.objects.filter(
                comp_id=comp_id,
                supplier_name__icontains=prefix_text
            ).order_by('supplier_name')[:10] # Limit suggestions to 10
            for obj in queryset:
                suggestions.append({'value': f"{obj.supplier_name} [{obj.supplier_id}]", 'id': obj.supplier_id})
        elif context_key == 'key2': # Based on C# sql3 logic 'key2' for Customer
            # This would typically be a Customer model if it existed.
            # For demonstration, using Supplier as a placeholder if Customer model is not defined.
            # Replace with actual Customer.objects.filter logic if you have a Customer model.
            queryset = Supplier.objects.filter( # Placeholder for Customer model
                comp_id=comp_id,
                supplier_name__icontains=prefix_text # Placeholder for CustomerName
            ).order_by('supplier_name')[:10]
            for obj in queryset:
                suggestions.append({'value': f"{obj.supplier_name} [{obj.supplier_id}]", 'id': obj.supplier_id})

        return JsonResponse(suggestions, safe=False)


class SundryCreditorDetailRedirectView(LoginRequiredMixin, View):
    """
    Handles the drill-down link, mimicking the Response.Redirect logic.
    This will redirect to the next detailed page, passing the supplier_id.
    """
    def get(self, request, supplier_id, *args, **kwargs):
        # The original ASP.NET redirected with ModId, SubModId, Key, lnkFor
        mod_id = request.GET.get('ModId', '')
        sub_mod_id = request.GET.get('SubModId', '')
        lnk_for = request.GET.get('lnkFor', '')

        # In a real Django application, you would define a proper URL for 'SundryCreditors_InDetailList.aspx'
        # e.g., reverse('accounts:sundry_creditors_in_detail_list', kwargs={'supplier_id': supplier_id})
        # For this example, we return an HX-Location header to signal HTMX to perform a client-side redirect.
        # This allows the modal to close and the page to navigate.
        # Ensure the target URL exists in your Django project.
        redirect_url = reverse_lazy('accounts:sundry_creditors_in_detail_list', kwargs={'supplier_id': supplier_id})
        
        # Build query parameters for the redirect URL
        query_params = f"ModId={mod_id}&SubModId={sub_mod_id}&lnkFor={lnk_for}"
        
        messages.info(request, f"Navigating to detailed view for Supplier ID: {supplier_id}")
        return HttpResponse(
            status=200, # Return 200 OK as we are sending HTMX headers, not direct redirect
            headers={
                'HX-Location': f"{redirect_url}?{query_params}" # This triggers client-side redirect
            }
        )

# Placeholder for the detail view that the drill-down would lead to
class SundryCreditorsInDetailListView(LoginRequiredMixin, TemplateView):
    template_name = 'accounts/sundry_creditors/detail_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['supplier_id'] = self.kwargs['supplier_id']
        context['mod_id'] = self.request.GET.get('ModId')
        context['sub_mod_id'] = self.request.GET.get('SubModId')
        context['lnk_for'] = self.request.GET.get('lnkFor')
        # Here, fetch detailed transactions for the supplier_id
        return context


class SundryCreditorExportView(LoginRequiredMixin, View):
    """
    View to export the current grid data to an Excel file.
    Corresponds to Button2_Click and ExportToExcel class.
    """
    def get(self, request, *args, **kwargs):
        supplier_search_term = request.GET.get('supplier_name_search', '').strip()
        category = request.GET.get('lnkFor', 'General')

        comp_id = request.session.get('compid')
        fin_year_id = request.session.get('finyear')

        if not comp_id or not fin_year_id:
            messages.error(request, "Company ID or Financial Year not found in session. Export failed.")
            return HttpResponse("Authentication context missing for export.", status=400)


        suppliers_queryset = Supplier.objects.all()

        if supplier_search_term:
            id_part = supplier_search_term.split('[')[-1].strip(']') if '[' in supplier_search_term else ''
            suppliers_queryset = suppliers_queryset.filter(
                Q(supplier_name__icontains=supplier_search_term) |
                Q(supplier_id__icontains=id_part)
            )

        sundry_creditor_summaries = []
        for supplier in suppliers_queryset:
            summary = supplier.get_sundry_creditor_summary(comp_id, fin_year_id, category)
            if summary['booked_bill_amt'] > 0: # Apply the same filter as FillGrid_Creditors
                sundry_creditor_summaries.append(summary)

        # Calculate final totals for the export
        export_credit_total = sum(s['credit_total_for_supplier'] for s in sundry_creditor_summaries)
        export_debit_total = sum(s['debit_total_for_supplier'] for s in sundry_creditor_summaries)
        export_closing_balance = export_credit_total - export_debit_total

        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        )
        response['Content-Disposition'] = 'attachment; filename="Ledger_Extracts.xlsx"'

        wb = Workbook()
        ws = wb.active
        ws.title = "Ledger Extracts"

        # Headers
        headers = ["SN", "Particulars (Supplier Name)", "Debit", "Credit"]
        ws.append(headers)

        # Apply header styling
        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center')
            cell.border = Border(left=Side(style='thin'), right=Side(style='thin'), top=Side(style='thin'), bottom=Side(style='thin'))

        # Data rows
        for idx, summary in enumerate(sundry_creditor_summaries):
            ws.append([
                idx + 1,
                f"{summary['supplier_name']} [{summary['supplier_id']}]",
                summary['payment_amt'], # Corresponds to 'Debit' in ASP.NET GridView
                summary['credit_amount_display'] # Corresponds to 'Credit' in ASP.NET GridView
            ])
            # Apply number format
            ws.cell(row=ws.max_row, column=3).number_format = '#,##0.00'
            ws.cell(row=ws.max_row, column=4).number_format = '#,##0.00'

        # Totals rows
        ws.append([]) # Blank row for separation
        ws.append(["", "Credit Total:", export_credit_total, ""])
        ws.append(["", "Debit Total:", export_debit_total, ""])
        ws.append(["", "Closing Bal:", export_closing_balance, ""])

        # Apply bold and number format to total values
        total_rows_start_idx = ws.max_row - 2
        for row_idx in range(total_rows_start_idx, ws.max_row + 1):
            ws.cell(row=row_idx, column=2).font = Font(bold=True) # Labels
            ws.cell(row=row_idx, column=3).font = Font(bold=True) # Values
            ws.cell(row=row_idx, column=3).number_format = '#,##0.00'

        # Adjust column widths
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width

        wb.save(response)
        return response

```

#### 4.4 Templates

Templates are structured for reusability and HTMX integration. `list.html` acts as the main page container, and `_table.html` is the partial loaded via HTMX. The `_autocomplete_suggestions.html` is a tiny partial for the autocomplete dropdown.

**`accounts/sundry_creditors/list.html`**
This is the main page that loads the initial structure and then uses HTMX to fetch the actual table content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sundry Creditors: <span id="lblOf" class="text-blue-600">{{ category }}</span></h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <div class="flex flex-wrap items-center gap-4">
            <label for="supplier_name_search" class="text-gray-700 font-medium">Supplier Name:</label>
            <div class="relative">
                <input 
                    type="text" 
                    id="supplier_name_search" 
                    name="supplier_name_search" 
                    placeholder="Enter Supplier Name"
                    class="box3 w-80 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-gray-800"
                    hx-get="{% url 'accounts:supplier_autocomplete' %}?contextKey=key1"
                    hx-trigger="keyup changed delay:300ms from:event.target"
                    hx-target="#supplier_suggestions_container"
                    hx-swap="innerHTML"
                    hx-indicator="#autocomplete-loading-indicator"
                    autocomplete="off"
                    value="{{ request.GET.supplier_name_search|default:'' }}"
                >
                <div id="supplier_suggestions_container" class="absolute bg-white border border-gray-200 rounded-md shadow-lg z-10 w-full mt-1 max-h-60 overflow-y-auto">
                    <!-- Autocomplete suggestions will be loaded here -->
                </div>
                <span id="autocomplete-loading-indicator" class="htmx-indicator absolute top-1/2 right-3 -translate-y-1/2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-gray-500"></div>
                </span>
            </div>

            <button 
                id="btn_Search" 
                class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                hx-get="{% url 'accounts:sundry_creditors_table' %}"
                hx-target="#sundryCreditorTable-container"
                hx-swap="innerHTML"
                hx-include="#supplier_name_search"
                hx-trigger="click"
                _="on click put the value of #supplier_name_search into the query string for hx-get"
            >
                Search
            </button>
            <a href="{% url 'accounts:sundry_creditor_export' %}?supplier_name_search={{ request.GET.supplier_name_search|default:'' }}&lnkFor={{ category }}" 
                class="redbox bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out"
                target="_blank"
            >
                Export
            </a>
            <a href="{% url 'accounts:sundry_creditors_list' %}" class="redbox bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out">
                Clear
            </a>
        </div>
    </div>
    
    <div id="sundryCreditorTable-container"
         hx-trigger="load, searchFilter from:body"
         hx-get="{% url 'accounts:sundry_creditors_table' %}?lnkFor={{ category }}&supplier_name_search={{ request.GET.supplier_name_search|default:'' }}"
         hx-target="this"
         hx-swap="innerHTML">
        <!-- Initial loading indicator -->
        <div class="text-center py-10 bg-white rounded-lg shadow-lg">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Sundry Creditors...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables CDN - ensure base.html does not duplicate -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css" rel="stylesheet">
<!-- Alpine.js CDN - ensure base.html does not duplicate -->
<script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is not strictly needed for this page with HTMX, but included if future UI state management is needed
        // e.g., for managing modal visibility or complex client-side state
    });

    // Custom HTMX event listener for autocomplete selection
    htmx.on("#supplier_suggestions_container", "click", function(evt) {
        if (evt.target.matches('div.suggestion-item')) {
            document.getElementById('supplier_name_search').value = evt.target.dataset.value;
            evt.target.parentElement.innerHTML = ''; // Clear suggestions
            // Optionally trigger a search immediately after selection
            htmx.trigger(document.getElementById('btn_Search'), 'click');
        }
    });

    // Close suggestions if clicked outside
    document.addEventListener('click', function(event) {
        const searchInput = document.getElementById('supplier_name_search');
        const suggestionsBox = document.getElementById('supplier_suggestions_container');
        if (suggestionsBox && !suggestionsBox.contains(event.target) && event.target !== searchInput) {
            suggestionsBox.innerHTML = '';
        }
    });

    // Handle hx-get for table content to include current search parameters
    htmx.on("#sundryCreditorTable-container", "htmx:configRequest", function(evt) {
        const searchInput = document.getElementById('supplier_name_search');
        if (searchInput && searchInput.value) {
            evt.detail.parameters['supplier_name_search'] = searchInput.value;
        }
        // 'lnkFor' (category) is typically passed via the URL query string already
    });
</script>
{% endblock %}
```

**`accounts/sundry_creditors/_table.html`**
This partial template renders the DataTables content and the total summaries. It's designed to be loaded dynamically via HTMX.

```html
<!-- accounts/sundry_creditors/_table.html -->
<div class="overflow-x-auto bg-white p-4 rounded-lg shadow-lg">
    <table id="sundryCreditorTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credit</th>
                <!-- Hidden columns for DataTables internal data access -->
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Id</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">SupplierId</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for summary in sundry_creditor_summaries %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left">
                    <a href="{% url 'accounts:sundry_creditor_drilldown' supplier_id=summary.supplier_id %}?ModId=11&SubModId=135&lnkFor={{ category }}" 
                       class="text-blue-600 hover:text-blue-800 hover:underline">
                        {{ summary.supplier_name }}
                    </a>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ summary.payment_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ summary.credit_amount_display|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left hidden">{{ summary.id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-left hidden">{{ summary.supplier_id }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No sundry creditors found matching your criteria.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<div class="mt-6">
    <table class="w-full max-w-lg mx-auto bg-white border border-gray-200 rounded-lg shadow-sm">
        <tbody>
            <tr>
                <td class="py-2 px-4 text-right font-bold text-gray-700 w-3/4">Credit Total:</td>
                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal">{{ total_credit_sum|floatformat:2 }}</td>
            </tr>
            <tr>
                <td class="py-2 px-4 text-right font-bold text-gray-700 w-3/4">Debit Total:</td>
                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal1">{{ total_debit_sum|floatformat:2 }}</td>
            </tr>
            <tr>
                <td class="py-2 px-4 text-right font-bold text-gray-700 w-3/4">Closing Bal:</td>
                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal2">{{ closing_balance_sum|floatformat:2 }}</td>
            </tr>
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the table is loaded into the DOM
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#sundryCreditorTable')) {
            $('#sundryCreditorTable').DataTable().destroy();
        }
        $('#sundryCreditorTable').DataTable({
            "pageLength": 18, // Corresponds to PageSize="18" in ASP.NET GridView
            "lengthMenu": [[10, 18, 25, 50, -1], [10, 18, 25, 50, "All"]],
            "pagingType": "full_numbers", // Provides First, Prev, Next, Last, and page numbers
            "columnDefs": [
                { "orderable": false, "targets": 0 }, // Disable sorting for 'SN' column
                { "visible": false, "targets": [4, 5] } // Hide 'Id' and 'SupplierId' columns
            ]
        });
    });
</script>
```

**`accounts/sundry_creditors/_autocomplete_suggestions.html`**
This partial renders the suggestions for the autocomplete input.

```html
<!-- accounts/sundry_creditors/_autocomplete_suggestions.html -->
{% for item in suggestions %}
<div class="suggestion-item p-2 hover:bg-blue-100 cursor-pointer text-gray-800" data-value="{{ item.value }}" data-id="{{ item.id }}">
    {{ item.value }}
</div>
{% empty %}
    {% if request.GET.q %}
        <div class="p-2 text-gray-500">No suggestions found.</div>
    {% endif %}
{% endfor %}
```

**`accounts/sundry_creditors/detail_list.html` (Placeholder)**
This would be the target for the drill-down link.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800">Sundry Creditors Details for Supplier: <span class="text-blue-600">{{ supplier_id }}</span></h2>
    <p class="mt-4 text-gray-700">This page would display the detailed transactions for supplier {{ supplier_id }}.</p>
    <p class="text-gray-700">Context: ModId={{ mod_id }}, SubModId={{ sub_mod_id }}, LnkFor={{ lnk_for }}</p>

    <!-- Implement detailed data display, e.g., another DataTables for transactions -->
    <div class="mt-8">
        <a href="{% url 'accounts:sundry_creditors_list' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Back to Sundry Creditors Summary
        </a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (accounts/urls.py)

The `urls.py` defines the mapping between URL paths and their corresponding views. We use an app namespace for clarity.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    SundryCreditorListView,
    SundryCreditorTablePartialView,
    SupplierAutoCompleteView,
    SundryCreditorDetailRedirectView,
    SundryCreditorsInDetailListView, # Placeholder for the detail view
    SundryCreditorExportView
)

app_name = 'accounts' # Namespace for this application's URLs

urlpatterns = [
    # Main Sundry Creditors List Page
    path('sundry-creditors/', SundryCreditorListView.as_view(), name='sundry_creditors_list'),
    
    # HTMX Endpoint for the dynamic table content (search/pagination)
    path('sundry-creditors/table/', SundryCreditorTablePartialView.as_view(), name='sundry_creditors_table'),

    # HTMX Endpoint for supplier name autocomplete suggestions
    path('autocomplete/suppliers/', SupplierAutoCompleteView.as_view(), name='supplier_autocomplete'),

    # Endpoint for drill-down link (mimics Response.Redirect to a detail page)
    path('sundry-creditors/<str:supplier_id>/drilldown/', SundryCreditorDetailRedirectView.as_view(), name='sundry_creditor_drilldown'),

    # Placeholder for the actual detail view page
    path('sundry-creditors/<str:supplier_id>/details/', SundryCreditorsInDetailListView.as_view(), name='sundry_creditors_in_detail_list'),

    # Endpoint for Export to Excel functionality
    path('sundry-creditors/export/', SundryCreditorExportView.as_view(), name='sundry_creditor_export'),

    # If there are other navigation points like Dashboard.aspx or SundryCreditors.aspx,
    # you would define their respective Django URLs here.
    # Example: path('dashboard/', SomeDashboardView.as_view(), name='dashboard'),
]

```

#### 4.6 Tests (accounts/tests.py)

Comprehensive tests are crucial. We include unit tests for model methods (especially the complex aggregation logic) and integration tests for all views to ensure correct functionality and HTMX interactions.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
import io
import openpyxl

from .models import Supplier, CreditorAccount

# --- Model Tests ---
class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create common test data for models
        cls.comp_id = 1
        cls.fin_year_id = 2023

        cls.supplier1 = Supplier.objects.create(
            sup_id=1,
            supplier_id='SUP001',
            supplier_name='Alpha Corp',
            comp_id=cls.comp_id
        )
        cls.supplier2 = Supplier.objects.create(
            sup_id=2,
            supplier_id='SUP002',
            supplier_name='Beta Ltd',
            comp_id=cls.comp_id
        )
        cls.supplier3_zero_bill = Supplier.objects.create(
            sup_id=3,
            supplier_id='SUP003',
            supplier_name='Zero Bill Supplier',
            comp_id=cls.comp_id
        )

        CreditorAccount.objects.create(
            creditor_id=101,
            supplier_id='SUP001',
            opening_amount=1000.00,
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id
        )
        CreditorAccount.objects.create(
            creditor_id=102,
            supplier_id='SUP002',
            opening_amount=500.00,
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id
        )
        CreditorAccount.objects.create(
            creditor_id=103,
            supplier_id='SUP003',
            opening_amount=0.00, # This supplier will have 0 booked bill amount in mock
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id
        )

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(sup_id=1)
        self.assertEqual(supplier.supplier_name, 'Alpha Corp')
        self.assertEqual(supplier.supplier_id, 'SUP001')
        self.assertEqual(supplier.comp_id, self.comp_id)

    def test_creditor_account_creation(self):
        creditor_acc = CreditorAccount.objects.get(creditor_id=101)
        self.assertEqual(creditor_acc.supplier_id, 'SUP001')
        self.assertEqual(creditor_acc.opening_amount, 1000.00)

    @patch('accounts.models.CreditorAccount.objects.filter')
    def test_get_opening_balance(self, mock_filter):
        # Mocking the filter call to CreditorAccount model
        mock_creditor_account = MagicMock(spec=CreditorAccount)
        mock_creditor_account.opening_amount = 999.99
        mock_filter.return_value.first.return_value = mock_creditor_account

        balance = self.supplier1.get_opening_balance(self.comp_id, self.fin_year_id)
        self.assertAlmostEqual(balance, 999.99)
        mock_filter.assert_called_with(
            supplier_id=self.supplier1.supplier_id,
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id
        )
        # Test case where no CreditorAccount exists
        mock_filter.return_value.first.return_value = None
        balance_none = self.supplier1.get_opening_balance(self.comp_id, self.fin_year_id)
        self.assertEqual(balance_none, 0.0)

    # Patch the aggregation methods to return predictable values for summary testing
    @patch('accounts.models.Supplier.get_opening_balance', side_effect=lambda s, c, f: { 'SUP001': 100.0, 'SUP002': 50.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_booked_bill_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 200.0, 'SUP002': 300.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 50.0, 'SUP002': 100.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_cash_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 25.0, 'SUP002': 0.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    def test_get_sundry_creditor_summary(self, mock_cash_payment, mock_payment, mock_booked_bill, mock_opening_balance):
        summary = self.supplier1.get_sundry_creditor_summary(self.comp_id, self.fin_year_id, 'Bills')
        self.assertEqual(summary['id'], self.supplier1.pk)
        self.assertEqual(summary['supplier_name'], 'Alpha Corp')
        self.assertEqual(summary['supplier_id'], 'SUP001')
        self.assertAlmostEqual(summary['opening_amt'], 100.00)
        self.assertAlmostEqual(summary['booked_bill_amt'], 200.00)
        self.assertAlmostEqual(summary['payment_amt'], 75.00) # (50.00 + 25.00) combined debit
        self.assertAlmostEqual(summary['credit_amount_display'], 300.00) # (100.00 + 200.00) combined credit for display
        self.assertAlmostEqual(summary['credit_total_for_supplier'], 300.00) # For sum aggregation
        self.assertAlmostEqual(summary['debit_total_for_supplier'], 75.00) # For sum aggregation
        self.assertAlmostEqual(summary['closing_balance'], 225.00) # (300.00 - 75.00)

# --- View Tests ---
class SundryCreditorViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.comp_id = 1
        self.fin_year_id = 2023
        # Setup a session for the client, mimicking a logged-in state
        session = self.client.session
        session['compid'] = self.comp_id
        session['finyear'] = self.fin_year_id
        session['username'] = 'testuser'
        session.save()

        # Create test data for models
        self.supplier1 = Supplier.objects.create(sup_id=1, supplier_id='SUP001', supplier_name='Alpha Corp', comp_id=self.comp_id)
        self.supplier2 = Supplier.objects.create(sup_id=2, supplier_id='SUP002', supplier_name='Beta Industries', comp_id=self.comp_id)
        self.supplier3_zero_bill = Supplier.objects.create(sup_id=3, supplier_id='SUP003', supplier_name='Zero Bill Corp', comp_id=self.comp_id)
        Supplier.objects.create(sup_id=4, supplier_id='SUP004', supplier_name='Gamma Co', comp_id=999) # Different company ID

        CreditorAccount.objects.create(creditor_id=101, supplier_id='SUP001', opening_amount=100.00, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        CreditorAccount.objects.create(creditor_id=102, supplier_id='SUP002', opening_amount=50.00, comp_id=self.comp_id, fin_year_id=self.fin_year_id)
        CreditorAccount.objects.create(creditor_id=103, supplier_id='SUP003', opening_amount=0.00, comp_id=self.comp_id, fin_year_id=self.fin_year_id)

    # Patch the aggregation methods within views for consistent test results
    @patch('accounts.models.Supplier.get_opening_balance', side_effect=lambda s, c, f: { 'SUP001': 100.0, 'SUP002': 50.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_booked_bill_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 200.0, 'SUP002': 300.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 50.0, 'SUP002': 100.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_cash_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 25.0, 'SUP002': 0.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    def test_sundry_creditor_list_view_get(self, mock_cash_payment, mock_payment, mock_booked_bill, mock_opening_balance):
        response = self.client.get(reverse('accounts:sundry_creditors_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundry_creditors/list.html')
        self.assertContains(response, 'Sundry Creditors:')
        self.assertContains(response, '<div id="sundryCreditorTable-container"') # Check for HTMX container

    @patch('accounts.models.Supplier.get_opening_balance', side_effect=lambda s, c, f: { 'SUP001': 100.0, 'SUP002': 50.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_booked_bill_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 200.0, 'SUP002': 300.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 50.0, 'SUP002': 100.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_cash_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 25.0, 'SUP002': 0.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    def test_sundry_creditor_table_partial_view_no_search(self, mock_cash_payment, mock_payment, mock_booked_bill, mock_opening_balance):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:sundry_creditors_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/sundry_creditors/_table.html')
        self.assertContains(response, 'Alpha Corp')
        self.assertContains(response, 'Beta Industries')
        self.assertNotContains(response, 'Zero Bill Corp') # Should be filtered out by booked_bill_amt > 0

        # Verify calculated totals
        # Alpha: Credit (100+200)=300, Debit (50+25)=75
        # Beta: Credit (50+300)=350, Debit (100+0)=100
        # Total Credit: 300 + 350 = 650
        # Total Debit: 75 + 100 = 175
        self.assertContains(response, 'Credit Total:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal">650.00</td>')
        self.assertContains(response, 'Debit Total:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal1">175.00</td>')
        self.assertContains(response, 'Closing Bal:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal2">475.00</td>')


    @patch('accounts.models.Supplier.get_opening_balance', side_effect=lambda s, c, f: { 'SUP001': 100.0, 'SUP002': 50.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_booked_bill_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 200.0, 'SUP002': 300.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 50.0, 'SUP002': 100.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_cash_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 25.0, 'SUP002': 0.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    def test_sundry_creditor_table_partial_view_with_search_name(self, mock_cash_payment, mock_payment, mock_booked_bill, mock_opening_balance):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:sundry_creditors_table') + '?supplier_name_search=Alpha', headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Alpha Corp')
        self.assertNotContains(response, 'Beta Industries')
        # Check totals for Alpha Corp only
        self.assertContains(response, 'Credit Total:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal">300.00</td>')
        self.assertContains(response, 'Debit Total:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal1">75.00</td>')
        self.assertContains(response, 'Closing Bal:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal2">225.00</td>')

    @patch('accounts.models.Supplier.get_opening_balance', side_effect=lambda s, c, f: { 'SUP001': 100.0, 'SUP002': 50.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_booked_bill_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 200.0, 'SUP002': 300.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 50.0, 'SUP002': 100.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_cash_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 25.0, 'SUP002': 0.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    def test_sundry_creditor_table_partial_view_with_search_id(self, mock_cash_payment, mock_payment, mock_booked_bill, mock_opening_balance):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:sundry_creditors_table') + '?supplier_name_search=[SUP002]', headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Beta Industries')
        self.assertNotContains(response, 'Alpha Corp')
        # Check totals for Beta Industries only
        self.assertContains(response, 'Credit Total:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal">350.00</td>')
        self.assertContains(response, 'Debit Total:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal1">100.00</td>')
        self.assertContains(response, 'Closing Bal:</th>\n                <td class="py-2 px-4 text-right font-bold text-gray-900 w-1/4" id="lblTotal2">250.00</td>')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('accounts:supplier_autocomplete') + '?q=beta&contextKey=key1')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn({'value': 'Beta Industries [SUP002]', 'id': 'SUP002'}, data)
        self.assertNotIn({'value': 'Alpha Corp [SUP001]', 'id': 'SUP001'}, data)

        response_all = self.client.get(reverse('accounts:supplier_autocomplete') + '?q=corp&contextKey=key1')
        self.assertEqual(response_all.status_code, 200)
        data_all = response_all.json()
        self.assertIn({'value': 'Alpha Corp [SUP001]', 'id': 'SUP001'}, data_all)
        self.assertIn({'value': 'Zero Bill Corp [SUP003]', 'id': 'SUP003'}, data_all)


    def test_sundry_creditor_drilldown_view(self):
        response = self.client.get(reverse('accounts:sundry_creditor_drilldown', args=['SUP001']), {'ModId': '11', 'SubModId': '135', 'lnkFor': 'Bills'})
        self.assertEqual(response.status_code, 200) # HTMX returns 200 with HX-Location
        self.assertIn('HX-Location', response.headers)
        expected_location = reverse('accounts:sundry_creditors_in_detail_list', kwargs={'supplier_id': 'SUP001'}) + '?ModId=11&SubModId=135&lnkFor=Bills'
        self.assertEqual(response.headers['HX-Location'], expected_location)
        self.assertContains(response, 'Navigating to detailed view for Supplier ID: SUP001')


    @patch('accounts.models.Supplier.get_opening_balance', side_effect=lambda s, c, f: { 'SUP001': 100.0, 'SUP002': 50.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_booked_bill_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 200.0, 'SUP002': 300.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 50.0, 'SUP002': 100.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    @patch('accounts.models.Supplier.get_cash_payment_amount', side_effect=lambda s, c, f, cat: { 'SUP001': 25.0, 'SUP002': 0.0, 'SUP003': 0.0 }.get(s.supplier_id, 0.0))
    def test_sundry_creditor_export_view(self, mock_cash_payment, mock_payment, mock_booked_bill, mock_opening_balance):
        response = self.client.get(reverse('accounts:sundry_creditor_export') + '?supplier_name_search=Alpha&lnkFor=Test')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="Ledger_Extracts.xlsx"')

        workbook = openpyxl.load_workbook(io.BytesIO(response.content))
        sheet = workbook.active
        
        # Check headers
        self.assertEqual(sheet['A1'].value, 'SN')
        self.assertEqual(sheet['B1'].value, 'Particulars (Supplier Name)')
        self.assertEqual(sheet['C1'].value, 'Debit')
        self.assertEqual(sheet['D1'].value, 'Credit')

        # Check data for Alpha Corp (only one matching search term)
        self.assertEqual(sheet['A2'].value, 1)
        self.assertEqual(sheet['B2'].value, 'Alpha Corp [SUP001]')
        self.assertAlmostEqual(sheet['C2'].value, 75.00) # PaymentAmt + CashPaymentAmt
        self.assertAlmostEqual(sheet['D2'].value, 300.00) # BookedBillAmt + OpeningAmt

        # Check total sums (for filtered data - Alpha Corp only)
        self.assertAlmostEqual(sheet['C4'].value, 300.00) # Credit Total for Alpha
        self.assertAlmostEqual(sheet['C5'].value, 75.00)  # Debit Total for Alpha
        self.assertAlmostEqual(sheet['C6'].value, 225.00) # Closing Balance for Alpha

        # Test export without search (all relevant suppliers included)
        response_all = self.client.get(reverse('accounts:sundry_creditor_export') + '?lnkFor=Test')
        workbook_all = openpyxl.load_workbook(io.BytesIO(response_all.content))
        sheet_all = workbook_all.active

        # Should contain 2 data rows (Alpha, Beta), as Zero Bill Supplier is filtered out (booked_bill_amt=0)
        self.assertEqual(sheet_all.max_row, 6) # Headers + 2 data rows + 1 blank row + 3 total rows = 7, but max_row is last row with content, so 6 is fine.
        self.assertEqual(sheet_all['A2'].value, 1)
        self.assertEqual(sheet_all['A3'].value, 2)
        self.assertContains(sheet_all['B2'].value, 'Alpha Corp')
        self.assertContains(sheet_all['B3'].value, 'Beta Industries')

        # Total sums for Alpha + Beta
        # Alpha: Credit 300, Debit 75
        # Beta: Credit 350, Debit 100
        # Total Credit: 650, Total Debit: 175, Closing: 475
        self.assertAlmostEqual(sheet_all['C4'].value, 650.00)
        self.assertAlmostEqual(sheet_all['C5'].value, 175.00)
        self.assertAlmostEqual(sheet_all['C6'].value, 475.00)

```

### Step 5: HTMX and Alpine.js Integration

-   **HTMX for dynamic updates:**
    -   The entire data table (`_table.html`) is loaded via `hx-get` on page load (`hx-trigger="load"`) and upon search button clicks (`hx-trigger="click"` on `btn_Search`).
    -   The autocomplete suggestions are fetched live using `hx-get` on `keyup changed delay:300ms` from the search input, targeting the `supplier_suggestions_container`.
    -   The drill-down links in the table `<a>` tags use standard navigation but could be `hx-get` to load a modal if a more in-page detail experience was desired. For now, it mimics the ASP.NET `Response.Redirect`.
-   **Alpine.js for UI state management:**
    -   While not strictly necessary for this module's current functionality (HTMX handles most interactivity), Alpine.js is included in `base.html` for future complex client-side state management, e.g., modal handling, tabbed interfaces, or dynamic form validation feedback beyond HTMX's capabilities.
-   **DataTables for list views:**
    -   Initialized in the `_table.html` partial's `<script>` block (`$(document).ready()`). It automatically adds client-side pagination, search box, and sorting to the rendered table.
    -   `pageLength` is set to 18 to match the ASP.NET `GridView`'s `PageSize`.
-   **DRY Template Inheritance:**
    -   All module-specific templates (`list.html`, `_table.html`, `detail_list.html`) `{% extends 'core/base.html' %}` to inherit common layout, CDN links (jQuery, HTMX, Alpine.js, Tailwind CSS), and other global components. `base.html` itself is assumed to contain all CDN links, keeping the module templates clean.
-   **No Custom JavaScript:**
    -   All dynamic interactions are handled through HTMX attributes (`hx-*`) and standard DOM manipulation (`htmx.on` events), adhering to the principle of minimal or no custom JavaScript.

### Final Notes

-   **Placeholders:** Replace placeholder logic in model methods (`get_booked_bill_amount`, `get_payment_amount`, `get_cash_payment_amount`) with actual Django ORM queries and aggregations that map to your underlying transaction tables.
-   **Session Management:** Ensure `compid` and `finyear` are correctly managed in the Django session upon user login, as these are critical for filtering data.
-   **Authentication:** Views are protected with `LoginRequiredMixin`. Ensure your Django project has a proper authentication setup.
-   **Error Handling:** Implement robust error logging and user-friendly error messages beyond basic `try-catch` blocks from ASP.NET. Django's `messages` framework is a good start.
-   **URL Naming:** Maintain consistent and readable URL patterns (e.g., `sundry-creditors/`, `sundry-creditors/table/`).
-   **Code Coverage:** The provided tests demonstrate how to achieve good test coverage. Extend these tests to cover edge cases and specific business rules as you implement the full financial logic.