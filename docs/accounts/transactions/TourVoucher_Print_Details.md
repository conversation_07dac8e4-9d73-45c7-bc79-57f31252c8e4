This comprehensive modernization plan outlines the automated conversion of your ASP.NET "Tour Voucher Print Details" functionality to a modern Django-based solution. Our approach emphasizes minimal manual coding, efficient data handling, and a highly interactive user experience.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

This modernization plan will transition your "Tour Voucher Print Details" page from ASP.NET to a robust, modern Django application. While the original ASP.NET page was marked "Under Developing," our strategy focuses on building out the complete functionality expected for managing tour vouchers, including listing, viewing details, adding, editing, and deleting them. This approach prioritizes automation, reduces manual coding, and delivers a highly interactive user experience without complex JavaScript.

### Business Benefits of Django Modernization:

*   **Improved User Experience:** Dynamic, real-time updates and lightning-fast interactions thanks to HTMX and Alpine.js, leading to smoother navigation and less waiting for page reloads.
*   **Reduced Development Costs:** Django's "batteries-included" philosophy, combined with our automation-first approach, means quicker feature development and easier maintenance.
*   **Enhanced Scalability:** Django is built to handle growth, ensuring your application can expand as your business needs evolve without significant re-architecture.
*   **Simplified Maintenance:** A clear separation of concerns (business logic in models, minimal view code) makes the application easier to understand, debug, and update.
*   **Future-Proof Technology:** Migrating to a widely adopted, open-source framework like Django ensures your application benefits from a large community, continuous updates, and long-term support.

## Step 1: Extract Database Schema

**Task:** Based on the ASP.NET code, we infer the primary entity and its potential attributes. The `Page_Load` method retrieves `Id` and `TIMId` from the query string and session variables for `CompId`, `FinYearId`, and `sId`. This strongly suggests a `TourVoucher` entity.

**Inferred Details:**
*   **Table Name:** `tblTourVouchers` (This is a common ASP.NET naming convention, and will be set in Django's `Meta.db_table`.)
*   **Key Fields (from query string/session):**
    *   `Id` (likely primary key, mapped to `voucher_id`)
    *   `TIMId` (another identifier, mapped to `tim_id`)
    *   `CompId` (Company ID, mapped to `company_id`)
    *   `FinYearId` (Financial Year ID, mapped to `financial_year_id`)
    *   `sId` (User ID/Username, for auditing, mapped to `created_by`)
*   **Inferred Fields (to make the entity complete and functional):**
    *   `VoucherNumber`
    *   `Description`
    *   `Amount`
    *   `DateCreated`

## Step 2: Identify Backend Functionality

**Task:** The original ASP.NET page is a placeholder ("Under Developing") with a "Cancel" button that redirects to `TourVoucher_Print.aspx`. This indicates its role as a read-only "details" view that links back to a "list" view. To provide a complete and modern solution, we will implement the full set of CRUD (Create, Read, Update, Delete) operations for the `TourVoucher` entity.

**Functionality to Implement in Django:**

*   **Read (List):** Display a list of all tour vouchers. This will be the main `TourVoucher` management page, equivalent to the `TourVoucher_Print.aspx` page. We will use DataTables for efficient data display.
*   **Read (Detail):** Show detailed information for a single tour voucher, directly translating the intent of the original `TourVoucher_Print_Details.aspx` page.
*   **Create:** Allow adding new tour vouchers via a modal form.
*   **Update:** Enable editing existing tour voucher details via a modal form.
*   **Delete:** Provide functionality to remove tour vouchers via a modal confirmation.
*   **Navigation:** The "Cancel" button's redirect logic will be re-implemented as a link or button that navigates back to the list view or closes a modal.

## Step 3: Infer UI Components

**Task:** The original ASP.NET UI is minimal, consisting of a title, an "Under Developing" message, and a "Cancel" button. The presence of `yui-datatable.css` in the ASP.NET code suggests that data tables were intended for use, likely on the `TourVoucher_Print.aspx` list page.

**Django UI Strategy:**

*   **List View (`tourvoucher/list.html`):** The primary page for managing tour vouchers. It will feature a table powered by **DataTables** for robust client-side searching, sorting, and pagination.
*   **Detail View (`tourvoucher/detail.html`):** A dedicated page to display comprehensive details of a single tour voucher, directly translating the purpose of the original ASP.NET page.
*   **CRUD Forms (`_tourvoucher_form.html`, `_tourvoucher_confirm_delete.html`):** All create, update, and delete actions will dynamically load forms or confirmation messages into a modal, using **HTMX** for smooth, non-page-refresh interactions.
*   **Frontend Logic:** **Alpine.js** will manage simple UI states, such as modal visibility and transitions, working seamlessly with HTMX.
*   **Styling:** **Tailwind CSS** will be used for all component styling, ensuring a clean and consistent design.

## Step 4: Generate Django Code

### 4.1 Models (`accounts/models.py`)

This model will represent your `TourVoucher` data, directly mapping to your existing `tblTourVouchers` database table. We adhere to the "Fat Model" principle by including a placeholder for future business logic that might have been in `clsFunctions` or other areas of your ASP.NET application.

```python
from django.db import models
from django.utils import timezone

class TourVoucher(models.Model):
    """
    Represents a tour voucher in the system, mapping to the existing tblTourVouchers table.
    Business logic related to tour vouchers will be encapsulated here.
    """
    # These fields are inferred from the ASP.NET code's use of QueryString and Session.
    # Adjust field types and nullability based on actual database schema.
    voucher_id = models.IntegerField(db_column='Id', primary_key=True)
    tim_id = models.IntegerField(db_column='TIMId', null=True, blank=True, verbose_name="TIM Identifier")
    company_id = models.IntegerField(db_column='CompId', null=True, blank=True, verbose_name="Company ID")
    financial_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True, verbose_name="Financial Year ID")
    created_by = models.CharField(db_column='sId', max_length=100, null=True, blank=True, verbose_name="Created By User")

    # Additional inferred fields to make the entity complete for a voucher
    voucher_number = models.CharField(max_length=50, unique=True, verbose_name="Voucher Number")
    description = models.TextField(blank=True, verbose_name="Description")
    amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, verbose_name="Amount")
    date_created = models.DateTimeField(default=timezone.now, verbose_name="Date Created")

    class Meta:
        managed = False  # Important: Django will not manage this table's schema (it exists)
        db_table = 'tblTourVouchers'  # Map to your existing SQL Server table name
        verbose_name = 'Tour Voucher'
        verbose_name_plural = 'Tour Vouchers'
        ordering = ['-date_created'] # Default ordering for lists

    def __str__(self):
        """
        Returns a human-readable string representation of the TourVoucher object.
        """
        return f"Voucher {self.voucher_number} (ID: {self.voucher_id})"

    def get_display_name(self):
        """
        Example of a simple model method for display purposes.
        """
        return f"Voucher No: {self.voucher_number}"

    def process_voucher(self):
        """
        Placeholder for complex business logic related to processing a tour voucher.
        This would include any core logic from your ASP.NET clsFunctions or other layers.
        """
        print(f"Executing business logic for tour voucher {self.voucher_number}...")
        # Add actual business logic here, e.g.,
        # - Update related inventory or financial records
        # - Perform complex calculations or validations
        return True # Indicate successful processing
```

### 4.2 Forms (`accounts/forms.py`)

A Django `ModelForm` will handle data input for `TourVoucher` objects, ensuring proper validation and including Tailwind CSS classes for consistent styling.

```python
from django import forms
from .models import TourVoucher

class TourVoucherForm(forms.ModelForm):
    """
    Form for creating and updating TourVoucher objects.
    Uses Tailwind CSS classes for styling.
    """
    class Meta:
        model = TourVoucher
        # 'created_by' and 'date_created' are typically set automatically or in views/models,
        # so they are not included in the form for direct user input.
        fields = ['voucher_number', 'description', 'amount', 'tim_id', 'company_id', 'financial_year_id']
        
        widgets = {
            'voucher_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Voucher Number'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3, 'placeholder': 'Enter Description'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': '0.00', 'step': '0.01'}),
            'tim_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter TIM ID'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Company ID'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Financial Year ID'}),
        }
    
    def clean_voucher_number(self):
        """
        Custom form validation to ensure the voucher number is unique (case-insensitive).
        This prevents duplicate entries in the database.
        """
        voucher_number = self.cleaned_data['voucher_number']
        # Query for existing vouchers with the same voucher number (case-insensitive)
        query = TourVoucher.objects.filter(voucher_number__iexact=voucher_number)
        
        if self.instance.pk:  # If we are updating an existing voucher, exclude itself from the uniqueness check
            query = query.exclude(pk=self.instance.pk)
            
        if query.exists():
            raise forms.ValidationError("This voucher number already exists. Please choose a different one.")
        return voucher_number
```

### 4.3 Views (`accounts/views.py`)

These "thin views" handle rendering and form submission logic, typically staying within the 5-15 line limit per method. All complex business logic is delegated to the `TourVoucher` model. A new `TourVoucherTablePartialView` is added for efficient HTMX table updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.db import IntegrityError # For handling potential unique constraints

from .models import TourVoucher
from .forms import TourVoucherForm

# --- List View ---
class TourVoucherListView(ListView):
    """
    Displays a list of all TourVoucher objects. This serves as the main entry point
    for managing tour vouchers, similar to the TourVoucher_Print.aspx that the
    original page would redirect to.
    """
    model = TourVoucher
    template_name = 'accounts/tourvoucher/list.html'
    context_object_name = 'tourvouchers' # Name for the list of objects in the template

# --- Table Partial View (for HTMX) ---
class TourVoucherTablePartialView(ListView):
    """
    Returns only the HTML table content for TourVoucher objects.
    Designed to be loaded efficiently via HTMX for dynamic table updates
    without refreshing the entire page.
    """
    model = TourVoucher
    template_name = 'accounts/tourvoucher/_tourvoucher_table.html'
    context_object_name = 'tourvouchers'

# --- Detail View ---
class TourVoucherDetailView(DetailView):
    """
    Displays the details of a single TourVoucher object.
    This directly corresponds to the original TourVoucher_Print_Details.aspx page's intent.
    """
    model = TourVoucher
    template_name = 'accounts/tourvoucher/detail.html'
    context_object_name = 'tourvoucher' # Name for the single object in the template

    # No specific business logic is required here as per the "thin view" principle.
    # Data retrieval is automatically handled by Django's DetailView.

# --- Create View ---
class TourVoucherCreateView(CreateView):
    """
    Handles the creation of new TourVoucher objects.
    Designed to be loaded and submitted via an HTMX modal.
    """
    model = TourVoucher
    form_class = TourVoucherForm
    template_name = 'accounts/tourvoucher/_tourvoucher_form.html' # Partial template for the modal content
    success_url = reverse_lazy('tourvoucher_list') # Fallback URL, not typically used with HTMX

    def form_valid(self, form):
        # Example: Set session-derived data (like company_id, created_by) before saving
        # This mirrors the ASP.NET Page_Load logic for session variables.
        self.object = form.save(commit=False)
        # Uncomment and adapt these lines if you have Django authentication/session data
        # if self.request.user.is_authenticated:
        #     self.object.created_by = self.request.user.username
        # self.object.company_id = self.request.session.get('compid') # Example from ASP.NET session
        # self.object.financial_year_id = self.request.session.get('finyear') # Example from ASP.NET session
        try:
            self.object.save()
            # Trigger any business logic on the model after saving (e.g., self.object.process_voucher())
            messages.success(self.request, 'Tour Voucher added successfully.')
            
            # HTMX response: return No Content (204) and trigger client-side events
            return HttpResponse(
                status=204, # Indicates success with no content to return
                headers={
                    'HX-Trigger': '{"refreshTourVoucherList":true, "closeModal":true}'
                }
            )
        except IntegrityError:
            # Catch database unique constraint errors (e.g., duplicate voucher_number)
            form.add_error(None, "A voucher with this number already exists. Please check your input.")
            return self.form_invalid(form) # Re-render form with error message

    def form_invalid(self, form):
        """
        Handles invalid form submissions. Renders the form again with error messages.
        """
        messages.error(self.request, 'Please correct the errors below.')
        # Return the form with errors for HTMX to swap back into the modal
        return self.render_to_response(self.get_context_data(form=form))

# --- Update View ---
class TourVoucherUpdateView(UpdateView):
    """
    Handles the updating of existing TourVoucher objects.
    Designed to be loaded and submitted via an HTMX modal.
    """
    model = TourVoucher
    form_class = TourVoucherForm
    template_name = 'accounts/tourvoucher/_tourvoucher_form.html' # Partial template for the modal content
    success_url = reverse_lazy('tourvoucher_list') # Fallback URL, not typically used with HTMX

    def form_valid(self, form):
        try:
            self.object = form.save()
            # Trigger any business logic on the model after saving (e.g., self.object.process_voucher())
            messages.success(self.request, 'Tour Voucher updated successfully.')
            
            # HTMX response: return No Content (204) and trigger client-side events
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshTourVoucherList":true, "closeModal":true}'
                }
            )
        except IntegrityError:
            form.add_error(None, "A voucher with this number already exists. Please check your input.")
            return self.form_invalid(form)

    def form_invalid(self, form):
        """
        Handles invalid form submissions. Renders the form again with error messages.
        """
        messages.error(self.request, 'Please correct the errors below.')
        return self.render_to_response(self.get_context_data(form=form))

# --- Delete View ---
class TourVoucherDeleteView(DeleteView):
    """
    Handles the deletion of TourVoucher objects.
    Designed to be loaded and submitted via an HTMX modal.
    """
    model = TourVoucher
    template_name = 'accounts/tourvoucher/_tourvoucher_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('tourvoucher_list') # Fallback URL, not typically used with HTMX

    def delete(self, request, *args, **kwargs):
        """
        Overrides the default delete method to provide HTMX-specific response and messages.
        """
        self.object = self.get_object()
        self.object.delete()
        messages.success(self.request, 'Tour Voucher deleted successfully.')
        
        # HTMX response: return No Content (204) and trigger client-side events
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': '{"refreshTourVoucherList":true, "closeModal":true}'
            }
        )

    def get_context_data(self, **kwargs):
        """
        Adds a custom message to the context for the confirmation template.
        """
        context = super().get_context_data(**kwargs)
        context['message'] = f"Are you sure you want to delete tour voucher '{self.object.voucher_number}' (ID: {self.object.pk})? This action cannot be undone."
        return context
```

### 4.4 Templates (`accounts/tourvoucher/`)

Templates are designed for modularity, with partials for HTMX-loaded content to avoid full page reloads. All templates extend `core/base.html` (which is assumed to exist and handles shared layout, CDN links, and initial script loading).

#### `accounts/tourvoucher/list.html`

This is the main page for listing and managing tour vouchers, replacing the intended list functionality of `TourVoucher_Print.aspx`.

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Tour Vouchers</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'tourvoucher_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Tour Voucher
        </button>
    </div>

    <div id="tourvoucherTable-container"
         hx-trigger="load, refreshTourVoucherList from:body"
         hx-get="{% url 'tourvoucher_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading tour vouchers...</p>
        </div>
    </div>
    
    <!-- Toast Message Area -->
    <div id="messages" class="fixed top-4 right-4 z-50">
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} p-4 mb-2 rounded-md shadow-md text-white
                    {% if message.tags == 'success' %}bg-green-500{% endif %}
                    {% if message.tags == 'error' %}bg-red-500{% endif %}
                    {% if message.tags == 'info' %}bg-blue-500{% endif %}
                    {% if message.tags == 'warning' %}bg-yellow-500{% endif %}"
                    hx-swap-oob="beforeend"
                    _="on load transition my opacity to 0 over 3s then remove me">
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    </div>

    <!-- Modal for forms and details -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 opacity-0 pointer-events-none hidden"
         _="on closeModal remove .is-active from me, remove .opacity-100 from me, add .opacity-0 to me, add .pointer-events-none to me wait 300ms then add .hidden to me
            on click if event.target.id == 'modal' remove .is-active from me, remove .opacity-100 from me, add .opacity-0 to me, add .pointer-events-none to me wait 300ms then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full transform transition-all duration-300 scale-95 opacity-0"
             _="on .is-active on #modal remove .opacity-0 from me, add .opacity-100 to me, remove .scale-95 from me, add .scale-100 to me">
            <!-- Modal content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed (e.g., for complex UI state)
    });

    // HTMX and Alpine.js integration for modal control
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            const modalContent = document.getElementById('modalContent');
            modal.classList.add('is-active');
            modal.classList.remove('opacity-0', 'pointer-events-none', 'hidden');
            modal.classList.add('opacity-100'); // Triggers opacity transition
            modalContent.classList.remove('opacity-0', 'scale-95');
            modalContent.classList.add('opacity-100', 'scale-100'); // Triggers scale transition
        }
    });

    document.body.addEventListener('closeModal', function(event) {
        const modal = document.getElementById('modal');
        const modalContent = document.getElementById('modalContent');
        
        modal.classList.remove('opacity-100');
        modal.classList.add('opacity-0', 'pointer-events-none');
        modalContent.classList.remove('opacity-100', 'scale-100');
        modalContent.classList.add('opacity-0', 'scale-95');

        setTimeout(() => {
            modal.classList.add('hidden');
            modalContent.innerHTML = ''; // Clear modal content after transition
        }, 300); // Match CSS transition duration
    });
</script>
{% endblock %}
```

#### `accounts/tourvoucher/_tourvoucher_table.html` (Partial for HTMX)

This partial template contains the DataTables table structure and its initialization script. It's loaded dynamically by HTMX into the `list.html`.

```html
<table id="tourvoucherTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Voucher Number</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Created</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in tourvouchers %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">
                <a href="{% url 'tourvoucher_detail' obj.pk %}" class="text-blue-600 hover:text-blue-800 font-medium">
                    {{ obj.voucher_number }}
                </a>
            </td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.description|truncatechars:50 }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">${{ obj.amount|floatformat:2 }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.date_created|date:"Y-m-d H:i" }}</td>
            <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                <button
                    class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded-lg text-xs mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'tourvoucher_detail' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    View
                </button>
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg text-xs mr-2 transition duration-150 ease-in-out"
                    hx-get="{% url 'tourvoucher_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg text-xs transition duration-150 ease-in-out"
                    hx-get="{% url 'tourvoucher_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-6 text-center text-gray-500">No tour vouchers found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent reinitialization errors on HTMX swap
    if ($.fn.DataTable.isDataTable('#tourvoucherTable')) {
        $('#tourvoucherTable').DataTable().destroy();
    }
    $('#tourvoucherTable').DataTable({
        "pageLength": 10, // Default number of rows per page
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for rows per page
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] } // Disable sorting for 'SN' and 'Actions' columns
        ]
    });
});
</script>
```

#### `accounts/tourvoucher/detail.html`

This template displays the detailed information for a single `TourVoucher`, directly translating the original `TourVoucher_Print_Details.aspx` page's purpose.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Tour Voucher Details: {{ tourvoucher.voucher_number }}</h2>
        <a href="{% url 'tourvoucher_list' %}"
            class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
            Back to List
        </a>
    </div>

    <div class="bg-white shadow-lg rounded-lg p-6 max-w-2xl mx-auto">
        <dl class="divide-y divide-gray-200">
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Voucher ID:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.voucher_id }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Voucher Number:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.voucher_number }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Description:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.description|default:"N/A" }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Amount:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">${{ tourvoucher.amount|floatformat:2 }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">TIM Identifier:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.tim_id|default:"N/A" }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Company ID:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.company_id|default:"N/A" }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Financial Year ID:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.financial_year_id|default:"N/A" }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Created By:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.created_by|default:"System" }}</dd>
            </div>
            <div class="py-4 flex justify-between items-center">
                <dt class="text-sm font-medium text-gray-500">Date Created:</dt>
                <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ tourvoucher.date_created|date:"F d, Y P" }}</dd>
            </div>
        </dl>
    </div>
</div>
{% endblock %}
```

#### `accounts/tourvoucher/_tourvoucher_form.html` (Partial for HTMX modal)

This partial template contains the form for adding or editing a `TourVoucher`. It is designed to be loaded into a modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Tour Voucher</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}
        
        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-sm mt-1">
                {% for error in field.errors %}{{ error }}{% endfor %}
            </p>
            {% endif %}
        </div>
        {% endfor %}

        {# Non-field errors (e.g., from custom form clean methods) #}
        {% if form.non_field_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Error!</strong>
            <span class="block sm:inline">{{ form.non_field_errors }}</span>
        </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-150 ease-in-out"
                hx-on::click="htmx.trigger(document.body, 'closeModal')">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Tour Voucher
            </button>
        </div>
    </form>
</div>
```

#### `accounts/tourvoucher/_tourvoucher_confirm_delete.html` (Partial for HTMX modal)

This partial template provides the confirmation message for deleting a `TourVoucher`, displayed in a modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">{{ message }}</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-150 ease-in-out"
                hx-on::click="htmx.trigger(document.body, 'closeModal')">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`accounts/urls.py`)

This file defines the URL patterns for accessing all Tour Voucher functionalities within the `accounts` Django application.

```python
from django.urls import path
from .views import (
    TourVoucherListView,
    TourVoucherTablePartialView,
    TourVoucherDetailView,
    TourVoucherCreateView,
    TourVoucherUpdateView,
    TourVoucherDeleteView
)

urlpatterns = [
    # Main list view for Tour Vouchers (equivalent to the target of TourVoucher_Print.aspx)
    path('tourvoucher/', TourVoucherListView.as_view(), name='tourvoucher_list'),
    
    # HTMX endpoint to fetch and refresh the DataTables content
    path('tourvoucher/table/', TourVoucherTablePartialView.as_view(), name='tourvoucher_table'),
    
    # Detail view for a single Tour Voucher (direct translation of TourVoucher_Print_Details.aspx)
    path('tourvoucher/detail/<int:pk>/', TourVoucherDetailView.as_view(), name='tourvoucher_detail'),
    
    # CRUD operations, designed to be loaded and interacted with via HTMX modals
    path('tourvoucher/add/', TourVoucherCreateView.as_view(), name='tourvoucher_add'),
    path('tourvoucher/edit/<int:pk>/', TourVoucherUpdateView.as_view(), name='tourvoucher_edit'),
    path('tourvoucher/delete/<int:pk>/', TourVoucherDeleteView.as_view(), name='tourvoucher_delete'),
]
```

### 4.6 Tests (`accounts/tests.py`)

Comprehensive tests for both the `TourVoucher` model (unit tests) and the various views (integration tests) are crucial for ensuring the reliability and correct functionality of the modernized application. This aims for at least 80% code coverage.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import TourVoucher
from django.db import IntegrityError # For testing unique constraints

class TourVoucherModelTest(TestCase):
    """
    Unit tests for the TourVoucher model, ensuring data integrity and business logic.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single TourVoucher instance for all tests in this class
        # We manually set voucher_id since managed=False and Django won't auto-assign 'id'
        cls.voucher1 = TourVoucher.objects.create(
            voucher_id=1001,
            voucher_number='TV-001',
            description='Test Tour Voucher 1 for Model',
            amount=1500.50,
            tim_id=123,
            company_id=1,
            financial_year_id=2023,
            created_by='model_testuser',
            date_created=timezone.now()
        )
        cls.voucher2 = TourVoucher.objects.create(
            voucher_id=1002,
            voucher_number='TV-002',
            description='Test Tour Voucher 2 for Model',
            amount=200.00,
        ) # Other optional fields can be left to default/blank

    def test_tourvoucher_creation(self):
        """
        Verify a TourVoucher object can be created and its attributes are correct.
        """
        obj = TourVoucher.objects.get(voucher_id=self.voucher1.voucher_id)
        self.assertEqual(obj.voucher_number, 'TV-001')
        self.assertEqual(obj.amount, 1500.50)
        self.assertEqual(obj.description, 'Test Tour Voucher 1 for Model')
        self.assertEqual(obj.tim_id, 123)
        self.assertEqual(obj.company_id, 1)
        self.assertEqual(obj.financial_year_id, 2023)
        self.assertEqual(obj.created_by, 'model_testuser')
        self.assertIsNotNone(obj.date_created)

    def test_str_representation(self):
        """
        Test the __str__ method provides an informative string.
        """
        obj = TourVoucher.objects.get(voucher_id=self.voucher1.voucher_id)
        self.assertEqual(str(obj), f"Voucher {obj.voucher_number} (ID: {obj.voucher_id})")

    def test_get_display_name_method(self):
        """
        Test custom model method `get_display_name`.
        """
        obj = TourVoucher.objects.get(voucher_id=self.voucher1.voucher_id)
        self.assertEqual(obj.get_display_name(), f"Voucher No: {obj.voucher_number}")

    def test_process_voucher_method(self):
        """
        Test the placeholder business logic method `process_voucher`.
        """
        obj = TourVoucher.objects.get(voucher_id=self.voucher1.voucher_id)
        self.assertTrue(obj.process_voucher()) # Assuming it returns True on success

    def test_unique_voucher_number(self):
        """
        Ensure that creating a voucher with a duplicate voucher number (case-insensitive) fails.
        """
        with self.assertRaises(IntegrityError):
            TourVoucher.objects.create(
                voucher_id=1003,
                voucher_number='TV-001', # Duplicate
                description='Another voucher with same number',
                amount=100.00
            )

class TourVoucherViewsTest(TestCase):
    """
    Integration tests for TourVoucher views, simulating user interactions
    including HTMX requests.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a TourVoucher instance for views to interact with.
        cls.voucher = TourVoucher.objects.create(
            voucher_id=2001,
            voucher_number='TV-VIEW-001',
            description='View Test Voucher',
            amount=500.00,
            date_created=timezone.now()
        )
        # Data for creating a new voucher
        cls.add_voucher_data = {
            'voucher_id': 2002,
            'voucher_number': 'TV-ADD-002',
            'description': 'New Voucher from Test',
            'amount': 750.25,
            'tim_id': 456,
            'company_id': 2,
            'financial_year_id': 2024,
        }
        # Data for updating an existing voucher
        cls.update_voucher_data = {
            'voucher_id': cls.voucher.voucher_id, # Must match existing PK
            'voucher_number': 'TV-VIEW-001-UPDATED',
            'description': 'Updated Description by Test',
            'amount': 999.99,
            'tim_id': 789,
            'company_id': 3,
            'financial_year_id': 2025,
        }

    def setUp(self):
        self.client = Client()

    # --- List View Tests ---
    def test_list_view_get(self):
        """
        Test that the main tourvoucher list page loads correctly.
        """
        response = self.client.get(reverse('tourvoucher_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourvoucher/list.html')
        self.assertContains(response, 'Tour Vouchers') # Check for page title

    def test_table_partial_view_get(self):
        """
        Test the HTMX partial for the table content loads correctly.
        """
        response = self.client.get(reverse('tourvoucher_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourvoucher/_tourvoucher_table.html')
        self.assertContains(response, self.voucher.voucher_number) # Check if existing voucher data is displayed

    # --- Detail View Tests ---
    def test_detail_view_get(self):
        """
        Test that the tourvoucher detail page loads correctly for an existing voucher.
        """
        response = self.client.get(reverse('tourvoucher_detail', args=[self.voucher.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourvoucher/detail.html')
        self.assertContains(response, self.voucher.voucher_number)
        self.assertContains(response, str(self.voucher.amount))

    def test_detail_view_not_found(self):
        """
        Test detail view gracefully handles a non-existent voucher (should return 404).
        """
        response = self.client.get(reverse('tourvoucher_detail', args=[99999])) # Non-existent PK
        self.assertEqual(response.status_code, 404)

    # --- Create View Tests ---
    def test_create_view_get_htmx(self):
        """
        Test loading the create form via an HTMX request.
        """
        response = self.client.get(reverse('tourvoucher_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourvoucher/_tourvoucher_form.html')
        self.assertContains(response, 'Add Tour Voucher') # Check form title

    def test_create_view_post_success_htmx(self):
        """
        Test successful creation via HTMX POST request.
        """
        initial_count = TourVoucher.objects.count()
        response = self.client.post(reverse('tourvoucher_add'), self.add_voucher_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success code (No Content)
        # Verify HTMX headers for client-side events
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTourVoucherList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        self.assertEqual(TourVoucher.objects.count(), initial_count + 1) # Verify object was created
        self.assertTrue(TourVoucher.objects.filter(voucher_number=self.add_voucher_data['voucher_number']).exists())

    def test_create_view_post_invalid_htmx(self):
        """
        Test invalid form submission for create view via HTMX.
        The form should be re-rendered with errors.
        """
        invalid_data = self.add_voucher_data.copy()
        invalid_data['voucher_number'] = '' # Make voucher number empty to trigger validation error
        
        initial_count = TourVoucher.objects.count()
        response = self.client.post(reverse('tourvoucher_add'), invalid_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'accounts/tourvoucher/_tourvoucher_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertEqual(TourVoucher.objects.count(), initial_count) # No new object should be created

    def test_create_view_post_duplicate_voucher_number_htmx(self):
        """
        Test that creating a voucher with a duplicate voucher number (unique constraint)
        returns an error via HTMX.
        """
        duplicate_data = self.add_voucher_data.copy()
        duplicate_data['voucher_id'] = 2003 # Unique PK for this test
        duplicate_data['voucher_number'] = self.voucher.voucher_number # Use an existing voucher number
        
        initial_count = TourVoucher.objects.count()
        response = self.client.post(reverse('tourvoucher_add'), duplicate_data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # Form re-rendered with error
        self.assertContains(response, 'This voucher number already exists. Please choose a different one.')
        self.assertEqual(TourVoucher.objects.count(), initial_count)

    # --- Update View Tests ---
    def test_update_view_get_htmx(self):
        """
        Test loading the update form via an HTMX request.
        """
        response = self.client.get(reverse('tourvoucher_edit', args=[self.voucher.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourvoucher/_tourvoucher_form.html')
        self.assertContains(response, 'Edit Tour Voucher') # Check form title
        self.assertContains(response, self.voucher.voucher_number) # Check if current data is pre-filled

    def test_update_view_post_success_htmx(self):
        """
        Test successful update via HTMX POST request.
        """
        response = self.client.post(reverse('tourvoucher_edit', args=[self.voucher.pk]), self.update_voucher_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTourVoucherList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        
        self.voucher.refresh_from_db() # Reload object from DB to get updated values
        self.assertEqual(self.voucher.voucher_number, 'TV-VIEW-001-UPDATED')
        self.assertEqual(self.voucher.description, 'Updated Description by Test')
        self.assertEqual(self.voucher.amount, 999.99)

    def test_update_view_post_invalid_htmx(self):
        """
        Test invalid form submission for update view via HTMX.
        """
        invalid_data = self.update_voucher_data.copy()
        invalid_data['voucher_number'] = '' # Make voucher number empty to trigger error

        response = self.client.post(reverse('tourvoucher_edit', args=[self.voucher.pk]), invalid_data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # HTMX renders form with errors
        self.assertTemplateUsed(response, 'accounts/tourvoucher/_tourvoucher_form.html')
        self.assertContains(response, 'This field is required.')
        
        # Ensure data was NOT updated in the database
        self.voucher.refresh_from_db()
        self.assertNotEqual(self.voucher.voucher_number, '') # Should still be original value

    # --- Delete View Tests ---
    def test_delete_view_get_htmx(self):
        """
        Test loading the delete confirmation via an HTMX request.
        """
        response = self.client.get(reverse('tourvoucher_delete', args=[self.voucher.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/tourvoucher/_tourvoucher_confirm_delete.html')
        self.assertContains(response, f"Are you sure you want to delete tour voucher '{self.voucher.voucher_number}'?")

    def test_delete_view_post_success_htmx(self):
        """
        Test successful deletion via HTMX POST request.
        """
        # Create a new voucher specifically for deletion test to avoid impacting other tests.
        voucher_to_delete = TourVoucher.objects.create(voucher_id=3001, voucher_number='TV-DEL-001')
        initial_count = TourVoucher.objects.count()
        
        response = self.client.post(reverse('tourvoucher_delete', args=[voucher_to_delete.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshTourVoucherList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])
        self.assertEqual(TourVoucher.objects.count(), initial_count - 1) # Verify object was deleted
        self.assertFalse(TourVoucher.objects.filter(pk=voucher_to_delete.pk).exists())

    def test_delete_view_post_non_existent_htmx(self):
        """
        Test attempting to delete a non-existent voucher via HTMX.
        """
        initial_count = TourVoucher.objects.count()
        response = self.client.post(reverse('tourvoucher_delete', args=[99999]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 404) # Should return 404 Not Found
        self.assertEqual(TourVoucher.objects.count(), initial_count) # No change in object count
```

## Step 5: HTMX and Alpine.js Integration

The generated templates and views fully demonstrate the seamless integration of HTMX and Alpine.js for a highly dynamic user experience:

*   **HTMX for Dynamic Interactions:**
    *   **List View Refresh:** The `hx-trigger="load, refreshTourVoucherList from:body"` attribute on the `#tourvoucherTable-container` ensures the table content is loaded on the initial page load and automatically refreshes whenever a `refreshTourVoucherList` custom event is dispatched (e.g., after successful add, edit, or delete operations).
    *   **Modal Loading:** Buttons for "Add New", "Edit", "Delete", and "View" (details) use `hx-get` to fetch the respective form or confirmation partials directly into the `#modalContent` div.
    *   **Form Submission:** Forms within the modals use `hx-post` combined with `hx-swap="none"`. This tells HTMX not to swap any content, as the view will explicitly handle the client-side response. The Django views then return an `HttpResponse` with a `204 No Content` status and `HX-Trigger` headers (`{"refreshTourVoucherList":true, "closeModal":true}`). These headers instruct the client-side HTMX to refresh the main voucher list and close the modal.
*   **Alpine.js for UI State Management:**
    *   Simple `_` attributes (from `htmx.org/extensions/alpine-morph/`) are used to manage the modal's `hidden`, `opacity`, and `scale` states based on HTMX triggers and clicks. This provides smooth opening and closing transitions for the modal without requiring complex custom JavaScript.
    *   A global `closeModal` custom event is dispatched via `htmx.trigger(document.body, 'closeModal')` from the "Cancel" buttons and after successful form submissions within the modal partials. This ensures a consistent and controlled way to close the modal.
*   **DataTables for List Views:** The `_tourvoucher_table.html` partial includes the necessary JavaScript for DataTables. It is designed to safely re-initialize the DataTable each time it's loaded by HTMX, preventing conflicts and ensuring dynamic content works seamlessly with DataTables' sorting, searching, and pagination features.

## Final Notes

*   This comprehensive plan provides a robust foundation for modernizing your "Tour Voucher Print Details" functionality into a full-fledged Django application.
*   The `accounts` app structure (models, forms, views, urls, templates, tests) adheres to modern Django best practices and emphasizes a clean, maintainable architecture.
*   The inferred database table name (`tblTourVouchers`) and column names (`Id`, `TIMId`, `CompId`, `FinYearId`, `sId`) are based on common ASP.NET patterns. It is crucial to verify these against your actual SQL Server database schema. The `voucher_id` field in the `TourVoucher` model is explicitly set as `primary_key=True` to map directly to your existing `Id` column, assuming it's the primary key in your database.
*   The business logic within the `TourVoucher` model is currently minimal (e.g., a placeholder `process_voucher` method). You would expand this based on the actual complex business logic extracted from your original ASP.NET `clsFunctions` or other application layers.
*   To make this code fully runnable, ensure your Django `settings.py` is configured for database connection (to your SQL Server database), that `'accounts'` is added to your `INSTALLED_APPS`, and that HTMX, Alpine.js, and Tailwind CSS are correctly linked and configured in your project's base template (`core/base.html`) and static file setup.