This modernization plan outlines the automated conversion of your existing ASP.NET Mail Merge functionality to a modern Django application. The focus is on leveraging AI-assisted tools to streamline the process, ensuring a robust, scalable, and user-friendly solution.

## ASP.NET to Django Conversion Script: Mail Merge Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code primarily interacts with `tblMM_Supplier_master`. It also joins with `tblFinancial_master` and `tblHR_OfficeStaff` for filtering purposes (`CompId`, `FinYearId`) and potentially retrieving additional information (`FinYear`, `EmployeeName`), though these specific joined fields are not used in the UI display in this ASP.NET page. The core entity for the Mail Merge functionality is the supplier.

**Extracted Table & Columns:**
- **Primary Table:** `tblMM_Supplier_master`
- **Identified Columns from `tblMM_Supplier_master`:**
    - `SupplierId` (likely an integer, possibly the primary key)
    - `SupplierName` (text)
    - `Email` (text, for recipient email)
    - `SysDate` (text, storing date string, e.g., '01-01-2023')
    - `CompId` (integer, used for filtering company-specific data)
    - `FinYearId` (integer, used for filtering financial year data)
    - `SessionId` (integer, likely linked to `EmpId` in `tblHR_OfficeStaff`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core logic in the ASP.NET code.

**Analysis:**
- **Read (Display):** The `SearchGridView1` displays a list of suppliers. This list is populated from `tblMM_Supplier_master` filtered by `CompId` and `FinYearId` from the user's session. The data is sorted by `SupplierName`.
- **Create/Update/Delete:** No direct CRUD operations on supplier records are performed from this page. Its purpose is to use existing supplier data.
- **Mail Sending:**
    - The page captures "From" email, "Subject", and "Message" content.
    - It is designed to send an email. The original code had a commented-out loop to send to multiple recipients based on the `GridView` data, but the active code hardcodes a single recipient (`<EMAIL>`).
    - For the Django migration, we will implement the intended "Mail Merge" functionality: sending the email to *all* listed suppliers with valid email addresses.
- **Validation:**
    - "From" email: Required, valid email format.
    - "Subject": Required.
    - "Message": Required.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, inferring corresponding Django UI patterns.

**Analysis:**
- **Data Display:** The `asp:GridView` will be replaced by a standard HTML `<table>` enhanced with **DataTables.js** for client-side features like pagination, sorting, and searching.
- **Input Fields:**
    - `asp:TextBox` for "From" email (`txtFrom`), "Subject" (`txtSub`), and "Message" (`txtMsg`) will become Django form fields (`EmailInput`, `TextInput`, `Textarea` widgets respectively).
- **Validation:** ASP.NET `RequiredFieldValidator` and `RegularExpressionValidator` will be handled by Django's robust form validation framework.
- **Actions:** The `asp:Button` ("Send Mail") will become a standard HTML `<button type="submit">` within a Django form.
- **Asynchronous Updates:** ASP.NET's `UpdatePanel` and `ModalPopupExtender` for loading progress will be replaced by **HTMX**'s declarative `hx-trigger` and `hx-get` attributes for partial page updates (e.g., loading the supplier list) and basic loading indicators. **Alpine.js** can be used for any remaining client-side UI state management, though for this module, HTMX largely suffices.

---

### Step 4: Generate Django Code

**Assumed Django Application Name:** `mailmerge`

### 4.1 Models (`mailmerge/models.py`)

This model will map directly to your existing `tblMM_Supplier_master` database table. We mark `managed = False` to ensure Django uses the existing table without trying to create or alter it.

```python
from django.db import models
from django.core.mail import EmailMultiAlternatives # Used for sending emails
from django.template.loader import render_to_string # For rendering HTML email templates
from django.utils.html import strip_tags # For creating plain text fallback from HTML

class Supplier(models.Model):
    """
    Represents a supplier record from the tblMM_Supplier_master table.
    Mapped to existing database structure using managed=False.
    """
    # Assuming SupplierId is the primary key in the original database.
    # If it's a unique code but not the DB's actual PK, use models.IntegerField(unique=True, db_column='SupplierId')
    # and let Django manage its own 'id' PK. Here we assume it's the primary_key.
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    email = models.EmailField(db_column='Email', max_length=255, null=True, blank=True)
    sys_date = models.CharField(db_column='SysDate', max_length=50, null=True, blank=True) # Date stored as string in original DB
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.IntegerField(db_column='SessionId')

    class Meta:
        managed = False  # Tells Django not to manage this table (create, alter, drop)
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'
        ordering = ['supplier_name'] # Matches 'Order by tblMM_Supplier_master.SupplierName Asc'

    def __str__(self):
        return self.supplier_name

    @classmethod
    def get_filtered_suppliers(cls, comp_id, fin_year_id):
        """
        Retrieves supplier records filtered by Company ID and Financial Year ID.
        This method encapsulates the data retrieval logic from the ASP.NET BindData() method.
        The FinYearId condition `FyId` is used as `fin_year_id__lte` (less than or equal to).
        """
        return cls.objects.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id
        )

    @classmethod
    def send_bulk_email(cls, from_email, subject, message_body, recipient_emails):
        """
        Sends an email to a list of supplier email addresses.
        This method replaces the email sending logic from Button1_Click,
        implementing the bulk mail merge functionality.
        """
        if not recipient_emails:
            return False, "No valid recipients found for email."

        # Prepare individual EmailMultiAlternatives messages for each recipient
        # This allows for HTML emails and better error handling per email.
        # For very large lists, consider an asynchronous task queue like Celery.
        messages = []
        for recipient_email in recipient_emails:
            if not recipient_email or '@' not in recipient_email: # Basic email format check
                continue # Skip invalid emails

            # Render the HTML email content from a template
            html_content = render_to_string('mailmerge/email_template.html', {
                'message_body': message_body,
                'subject': subject,
                # Add any other dynamic content needed for the email template
            })
            plain_content = strip_tags(html_content) # Create a plain text fallback

            msg = EmailMultiAlternatives(
                subject=subject,
                body=plain_content,
                from_email=from_email,
                to=[recipient_email], # Each message targets one recipient
            )
            msg.attach_alternative(html_content, "text/html")
            messages.append(msg)
        
        if not messages:
            return False, "No valid email addresses to send to after filtering."

        try:
            # Use connection to send all messages in one go for efficiency if supported by backend
            # or iterate and send them if a task queue is not used.
            # For simplicity, we'll iterate and send, mimicking how an SmtpClient would handle multiple sends.
            sent_count = 0
            for message in messages:
                message.send()
                sent_count += 1
            return True, f"Successfully sent {sent_count} emails."
        except Exception as e:
            # Log the full exception details in a real application
            return False, f"Failed to send emails: {e}"

```

### 4.2 Forms (`mailmerge/forms.py`)

This form specifically handles the input fields for the mail merge message, not for managing `Supplier` records.

```python
from django import forms
from django.conf import settings # To get default from email if available

class MailMergeForm(forms.Form):
    """
    Form for capturing mail merge email details (From, Subject, Message Body).
    Replicates ASP.NET textboxes and validators.
    """
    from_email = forms.EmailField(
        label='From:',
        max_length=255,
        required=True, # Matches RequiredFieldValidator
        widget=forms.EmailInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': '<EMAIL>'
        })
    )
    subject = forms.CharField(
        label='Subject:',
        max_length=255,
        required=True, # Matches RequiredFieldValidator
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )
    message_body = forms.CharField(
        label='Message:',
        required=True, # Matches RequiredFieldValidator
        widget=forms.Textarea(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-64',
            'rows': 10 # Matches ASP.NET Height="250px" TextMode="MultiLine"
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default 'from_email' from Django settings if available
        if not self.is_bound and hasattr(settings, 'DEFAULT_FROM_EMAIL'):
            self.fields['from_email'].initial = settings.DEFAULT_FROM_EMAIL

        # Set default message body from ASP.NET if not provided in initial data
        default_asp_message = """Dear Sir/Mam,

We request you to send Ledger statement F.Y 13-14 with stamp for statutory Audit of our company SYNERGYTECH AUTOMATION PVT. LTD. PUNE  Auditors required ledger statement copies to match the ledger for Balance – sheet F.Y.2013-2014.

So Kindly forward us the ledger statement with stamp on or before 31st March 14 & please revert if you have any queries…


Regards,

 
Thanks & Regards,
TEJAS LASURKAR.
Finance & Accounts Dept.
For Synergytech Automation Pvt.Ltd.
Gat No.205,Kasurdi,Khed Shivapur,
Off Pune Bangalore Highway,
Pune - 412 205

Email: <EMAIL>/<EMAIL>
Web: www.synergytechs.com
Cell: **********.
"""
        if not self.is_bound and 'message_body' not in self.initial:
            self.fields['message_body'].initial = default_asp_message

```

### 4.3 Views (`mailmerge/views.py`)

The main view (`MailMergeView`) handles displaying the mail form and processing the email sending. A separate `SupplierTablePartialView` is used to load the supplier list via HTMX.

```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponseRedirect # For redirecting after POST
from django.shortcuts import render # For rendering template
from django.conf import settings # For default email if needed

from .models import Supplier
from .forms import MailMergeForm

class MailMergeView(TemplateView):
    """
    Handles the main Mail Merge page, displaying the email form and supplier list.
    Processes the email sending request.
    """
    template_name = 'mailmerge/mailmerge_page.html' # Main page template

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the form. Initial values for 'from_email' and 'message_body' are set in form's __init__.
        context['form'] = MailMergeForm()
        
        # Dummy session values for compid and finyear, as in ASP.NET.
        # In a real application, these would come from request.session or request.user profile.
        comp_id = self.request.session.get('compid', 1) 
        fin_year_id = self.request.session.get('finyear', 2024) 
        
        # The list of suppliers is loaded by a separate HTMX partial view for dynamic loading
        # The initial context might not need 'suppliers' directly here if only the partial loads it.
        # However, for initial server-side render consistency, it's good to include it.
        context['suppliers'] = Supplier.get_filtered_suppliers(comp_id, fin_year_id)
        return context

    def post(self, request, *args, **kwargs):
        form = MailMergeForm(request.POST)
        if form.is_valid():
            from_email = form.cleaned_data['from_email']
            subject = form.cleaned_data['subject']
            message_body = form.cleaned_data['message_body']

            # Retrieve filtering parameters from session
            comp_id = request.session.get('compid', 1)
            fin_year_id = request.session.get('finyear', 2024)

            # Get target supplier emails using the model method
            all_suppliers = Supplier.get_filtered_suppliers(comp_id, fin_year_id)
            # Filter for valid emails before sending
            recipient_emails = [s.email for s in all_suppliers if s.email and '@' in s.email]

            # Call the model method to send emails
            success, msg = Supplier.send_bulk_email(from_email, subject, message_body, recipient_emails)

            if success:
                messages.success(request, msg)
            else:
                messages.error(request, msg)
            
            # Redirect to the same page to prevent form resubmission and display messages
            return HttpResponseRedirect(reverse_lazy('mailmerge_page'))
        else:
            # If form is not valid, re-render the page with errors and a general error message
            messages.error(request, "Please correct the errors below.")
            # Re-initialize context to pass form with errors and suppliers to the template
            context = self.get_context_data()
            context['form'] = form # Overwrite with the invalid form
            return render(request, self.template_name, context)


class SupplierTablePartialView(ListView):
    """
    A partial view specifically for rendering the supplier list table.
    This is loaded via HTMX into the main MailMerge page.
    """
    model = Supplier
    template_name = 'mailmerge/_supplier_table.html'
    context_object_name = 'suppliers' # Name of the queryset in the template context

    def get_queryset(self):
        # Retrieve filtering parameters from session (same as in MailMergeView)
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2024)
        return Supplier.get_filtered_suppliers(comp_id, fin_year_id)

```

### 4.4 Templates (`mailmerge/templates/mailmerge/`)

**`mailmerge_page.html`** (Main page template for Mail Merge functionality)

```html
{% extends 'core/base.html' %}
{% load static %}

{% block head_content %}
    <title>Mail Merge - ERP</title>
    <!-- DataTables CSS for table styling. Assume core/base.html includes Tailwind CSS. -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Mail Merge</h2>
    
    <!-- Messages display area (for Django messages) -->
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
                <div class="p-4 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% endif %}">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <!-- Left Column: Supplier List (DataTables via HTMX) -->
        <div class="md:col-span-1 bg-white p-6 rounded-lg shadow-md overflow-auto h-96"> {# Simulates ASP.NET Panel Height and ScrollBars #}
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Available Suppliers</h3>
            <div id="supplierTable-container"
                 hx-trigger="load, refreshSupplierList from:body" {# Loads on page load, and refreshes on custom event #}
                 hx-get="{% url 'supplier_table_partial' %}" {# URL to fetch the table partial #}
                 hx-swap="innerHTML">
                <!-- Initial loading state while HTMX fetches content -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading supplier data...</p>
                </div>
            </div>
        </div>

        <!-- Right Column: Mail Form -->
        <div class="md:col-span-1 bg-white p-6 rounded-lg shadow-md">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Compose Email</h3>
            <form method="post" action="{% url 'mailmerge_page' %}" class="space-y-4">
                {% csrf_token %}
                
                {# From Email Field #}
                <div>
                    <label for="{{ form.from_email.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.from_email.label }}</label>
                    {{ form.from_email }}
                    {% if form.from_email.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.from_email.errors.0 }}</p> {# Display first error for simplicity #}
                    {% endif %}
                </div>
                
                {# Subject Field #}
                <div>
                    <label for="{{ form.subject.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.subject.label }}</label>
                    {{ form.subject }}
                    {% if form.subject.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.subject.errors.0 }}</p>
                    {% endif %}
                </div>

                {# Message Body Field #}
                <div>
                    <label for="{{ form.message_body.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.message_body.label }}</label>
                    {{ form.message_body }}
                    {% if form.message_body.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.message_body.errors.0 }}</p>
                    {% endif %}
                </div>
                
                {# Submit Button #}
                <div class="mt-6 flex justify-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Send Mail
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- jQuery is required for DataTables -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- DataTables JS -->
<script type="text/javascript" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components would be defined here if needed for client-side reactivity
        // beyond HTMX's capabilities (e.g., dynamic UI elements, complex state management).
        // No specific complex UI state for Mail Merge from ASP.NET code.
    });
</script>
{% endblock %}

```

**`_supplier_table.html`** (HTMX partial for the DataTables content)

```html
<table id="supplierTable" class="min-w-full bg-white table-auto border border-gray-200">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Supplier Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Code</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Email Id</th>
        </tr>
    </thead>
    <tbody>
        {% for supplier in suppliers %}
        <tr class="{% cycle 'bg-white' 'bg-gray-50' %} hover:bg-gray-100">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700 text-right">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700 text-left">{{ supplier.supplier_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700 text-center">{{ supplier.supplier_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700 text-left">{{ supplier.email|default:"N/A" }}</td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="4" class="py-4 px-4 text-center text-lg text-red-600">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance before re-initializing to prevent warnings on HTMX re-swap
        if ($.fn.DataTable.isDataTable('#supplierTable')) {
            $('#supplierTable').DataTable().destroy();
        }
        $('#supplierTable').DataTable({
            "pageLength": 20, // Matches ASP.NET GridView PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true, // Makes the table responsive for smaller screens
            "searching": true,  // Enables the search/filter box
            "ordering": true,   // Enables column sorting
            "paging": true      // Enables pagination controls
        });
    });
</script>
```

**`email_template.html`** (Template for the actual email body sent to recipients)

```html
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<style>
    body { font-family: Verdana, Arial, Helvetica, sans-serif; font-size: 11px; line-height: 1.5; color: #333; }
    h1, h2, h3 { color: #0056b3; }
    p { margin-bottom: 1em; }
    .footer { font-size: 0.9em; color: #666; margin-top: 20px; border-top: 1px solid #eee; padding-top: 10px; }
</style>
</head>
<body>
    <p>Dear Sir/Madam,</p>
    {# message_body is rendered as preformatted text, preserving line breaks #}
    <p style="white-space: pre-wrap; font-family: inherit;">{{ message_body }}</p> 
    <div class="footer">
        <p>This email was sent from the ERP Mail Merge system.</p>
        <p>If you have any queries, please revert.</p>
    </div>
</body>
</html>
```

### 4.5 URLs (`mailmerge/urls.py`)

These URL patterns define how web requests are mapped to your Django views.

```python
from django.urls import path
from .views import MailMergeView, SupplierTablePartialView

urlpatterns = [
    # Main Mail Merge page
    path('mailmerge/', MailMergeView.as_view(), name='mailmerge_page'),
    # HTMX endpoint for loading the supplier list table dynamically
    path('mailmerge/suppliers-table/', SupplierTablePartialView.as_view(), name='supplier_table_partial'),
]
```
**Integration with Project's `urls.py`:**
To include these URLs in your Django project, add the following line to your main `project_name/urls.py` file:
`path('accounts/', include('mailmerge.urls')),` (assuming it's part of an 'accounts' module, or simply `path('mailmerge/', include('mailmerge.urls')),` if it's a standalone app).

### 4.6 Tests (`mailmerge/tests.py`)

Comprehensive tests for both the `Supplier` model's logic and the `MailMergeView`'s functionality are crucial for verifying the migration's success and ensuring future stability.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch # To mock external dependencies like email sending
from django.core import mail # Django's built-in email testing outbox

from .models import Supplier
from .forms import MailMergeForm

class SupplierModelTest(TestCase):
    """
    Unit tests for the Supplier model and its class methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data that will be available for all tests in this class
        Supplier.objects.create(
            supplier_id=101, 
            supplier_name='Alpha Supplier', 
            email='<EMAIL>', 
            sys_date='01-01-2023',
            comp_id=1,
            fin_year_id=2023,
            session_id=1
        )
        Supplier.objects.create(
            supplier_id=102, 
            supplier_name='Beta Supplier', 
            email='<EMAIL>', 
            sys_date='02-01-2023',
            comp_id=1,
            fin_year_id=2024,
            session_id=1
        )
        Supplier.objects.create(
            supplier_id=103, 
            supplier_name='Gamma Supplier', 
            email=None, # Test supplier without email
            sys_date='03-01-2023',
            comp_id=1,
            fin_year_id=2024,
            session_id=1
        )
        Supplier.objects.create(
            supplier_id=104, 
            supplier_name='Delta Supplier', 
            email='<EMAIL>', 
            sys_date='04-01-2023',
            comp_id=2, # Different company ID
            fin_year_id=2024,
            session_id=2
        )
  
    def test_supplier_creation(self):
        """Verify that a supplier object can be created and its attributes are correct."""
        supplier = Supplier.objects.get(supplier_id=101)
        self.assertEqual(supplier.supplier_name, 'Alpha Supplier')
        self.assertEqual(supplier.email, '<EMAIL>')
        self.assertEqual(supplier.comp_id, 1)
        self.assertEqual(supplier.fin_year_id, 2023)
        self.assertEqual(supplier.session_id, 1)
        
    def test_get_filtered_suppliers_correctly_filters(self):
        """Test the get_filtered_suppliers method with different filter parameters."""
        # Test filtering for comp_id=1, fin_year_id=2024 (should include 2023 and 2024 for same company)
        suppliers_filtered = Supplier.get_filtered_suppliers(comp_id=1, fin_year_id=2024)
        self.assertEqual(suppliers_filtered.count(), 3) 
        self.assertIn(Supplier.objects.get(supplier_id=101), suppliers_filtered) # fin_year_id <= 2024
        self.assertIn(Supplier.objects.get(supplier_id=102), suppliers_filtered)
        self.assertIn(Supplier.objects.get(supplier_id=103), suppliers_filtered)
        self.assertNotIn(Supplier.objects.get(supplier_id=104), suppliers_filtered) # Different comp_id

        # Test filtering for comp_id=2, fin_year_id=2024
        suppliers_comp2 = Supplier.get_filtered_suppliers(comp_id=2, fin_year_id=2024)
        self.assertEqual(suppliers_comp2.count(), 1)
        self.assertIn(Supplier.objects.get(supplier_id=104), suppliers_comp2)

        # Test filtering for a specific financial year
        suppliers_2023 = Supplier.get_filtered_suppliers(comp_id=1, fin_year_id=2023)
        self.assertEqual(suppliers_2023.count(), 1)
        self.assertIn(Supplier.objects.get(supplier_id=101), suppliers_2023)


    @patch('mailmerge.models.EmailMultiAlternatives.send') # Mock the actual send method of EmailMultiAlternatives
    def test_send_bulk_email_success(self, mock_email_send):
        """Test successful bulk email sending to multiple valid recipients."""
        recipient_emails = ['<EMAIL>', '<EMAIL>']
        from_email = '<EMAIL>'
        subject = 'Test Subject'
        message_body = 'This is a test message.'

        success, msg = Supplier.send_bulk_email(from_email, subject, message_body, recipient_emails)
        
        self.assertTrue(success)
        self.assertIn("Successfully sent 2 emails.", msg)
        self.assertEqual(mock_email_send.call_count, 2) # Verify send() was called twice

        # Verify email content in Django's outbox (if not mocking EmailMultiAlternatives.send)
        # For this test, we are mocking the send method, so mail.outbox will be empty.
        # To test the actual email object creation and content, you'd not mock `send_mass_mail` directly.
        # Instead, allow it to populate `mail.outbox` and then check `len(mail.outbox)` and their content.
        # Let's adjust this test to use Django's `mail.outbox` for more comprehensive testing.
        
        # Clear outbox before test
        mail.outbox = []
        success, msg = Supplier.send_bulk_email(from_email, subject, message_body, recipient_emails)
        
        self.assertTrue(success)
        self.assertEqual(len(mail.outbox), 2)
        self.assertEqual(mail.outbox[0].to, ['<EMAIL>'])
        self.assertEqual(mail.outbox[1].to, ['<EMAIL>'])
        self.assertEqual(mail.outbox[0].subject, subject)
        self.assertIn(message_body, mail.outbox[0].body) # Check plain text body
        self.assertIn(message_body, mail.outbox[0].alternatives[0][0]) # Check HTML body

    def test_send_bulk_email_no_recipients(self):
        """Test sending when no recipients are provided."""
        success, msg = Supplier.send_bulk_email('<EMAIL>', 'Subject', 'Body', [])
        self.assertFalse(success)
        self.assertEqual(msg, "No valid recipients found for email.")

    def test_send_bulk_email_invalid_recipients(self):
        """Test sending when recipients list contains invalid email addresses."""
        mail.outbox = [] # Clear outbox before test
        recipient_emails = ['invalid-email', '<EMAIL>', '<EMAIL>']
        success, msg = Supplier.send_bulk_email('<EMAIL>', 'Subject', 'Body', recipient_emails)
        
        self.assertTrue(success) # It sends to the valid ones
        self.assertEqual(len(mail.outbox), 2) # Only the two valid emails should be processed
        self.assertIn('<EMAIL>', mail.outbox[0].to)
        self.assertIn('<EMAIL>', mail.outbox[1].to)
        self.assertIn("Successfully sent 2 emails.", msg)

    @patch('mailmerge.models.EmailMultiAlternatives.send', side_effect=Exception("SMTP error"))
    def test_send_bulk_email_failure(self, mock_email_send):
        """Test handling of exceptions during email sending."""
        recipient_emails = ['<EMAIL>']
        success, msg = Supplier.send_bulk_email('<EMAIL>', 'Subject', 'Body', recipient_emails)
        
        self.assertFalse(success)
        self.assertIn("Failed to send emails: SMTP error", msg)
        mock_email_send.assert_called_once() # Ensure send was attempted

class MailMergeViewsTest(TestCase):
    """
    Integration tests for MailMergeView and SupplierTablePartialView.
    """
    @classmethod
    def setUpTestData(cls):
        # Create common test data for views
        Supplier.objects.create(
            supplier_id=1, supplier_name='View Test Supplier A', email='<EMAIL>', 
            sys_date='01-01-2023', comp_id=1, fin_year_id=2024, session_id=1
        )
        Supplier.objects.create(
            supplier_id=2, supplier_name='View Test Supplier B', email='<EMAIL>', 
            sys_date='01-01-2023', comp_id=1, fin_year_id=2024, session_id=1
        )
        Supplier.objects.create(
            supplier_id=3, supplier_name='View Test Supplier C', email=None, # No email
            sys_date='01-01-2023', comp_id=1, fin_year_id=2024, session_id=1
        )
    
    def setUp(self):
        self.client = Client()
        # Set dummy session values that views will use for filtering
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()
        mail.outbox = [] # Clear Django's email outbox before each test

    def test_mailmerge_page_get_request(self):
        """Test GET request to the main mail merge page."""
        response = self.client.get(reverse('mailmerge_page'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mailmerge/mailmerge_page.html')
        self.assertIsInstance(response.context['form'], MailMergeForm)
        self.assertContains(response, 'Mail Merge') # Check for page title
        self.assertContains(response, 'Loading supplier data...') # HTMX placeholder message

    def test_supplier_table_partial_view_get_request(self):
        """Test HTMX partial view for supplier table."""
        response = self.client.get(reverse('supplier_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mailmerge/_supplier_table.html')
        
        # Check if expected suppliers are in the response content
        self.assertContains(response, 'View Test Supplier A')
        self.assertContains(response, '<EMAIL>')
        self.assertContains(response, 'View Test Supplier B')
        self.assertContains(response, '<EMAIL>')
        self.assertContains(response, 'View Test Supplier C') # Should still appear in table even without email
        self.assertContains(response, 'N/A') # For supplier with no email

        # Verify the correct number of suppliers are passed to the template context
        self.assertEqual(len(response.context['suppliers']), 3)

    @patch('mailmerge.models.Supplier.send_bulk_email')
    def test_mailmerge_page_post_success(self, mock_send_bulk_email):
        """Test successful POST request to send mail."""
        mock_send_bulk_email.return_value = (True, "Emails sent successfully.")
        
        data = {
            'from_email': '<EMAIL>',
            'subject': 'Important Update',
            'message_body': 'This is a test message from the new system.',
        }
        response = self.client.post(reverse('mailmerge_page'), data, follow=True) # follow=True to follow redirect
        
        self.assertEqual(response.status_code, 200) # After redirect
        self.assertRedirects(response, reverse('mailmerge_page'))
        
        # Check if `send_bulk_email` was called with the correct arguments
        # Expected emails: <EMAIL>, <EMAIL> (Supplier C has no email)
        expected_emails = ['<EMAIL>', '<EMAIL>']
        mock_send_bulk_email.assert_called_once_with(
            data['from_email'],
            data['subject'],
            data['message_body'],
            expected_emails
        )
        
        # Check for success message after redirect
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Emails sent successfully.")
        self.assertEqual(messages[0].tags, 'success')

    @patch('mailmerge.models.Supplier.send_bulk_email')
    def test_mailmerge_page_post_failure_from_model(self, mock_send_bulk_email):
        """Test POST request when bulk email sending fails at the model layer."""
        mock_send_bulk_email.return_value = (False, "Failed to connect to SMTP server.")
        
        data = {
            'from_email': '<EMAIL>',
            'subject': 'Important Update',
            'message_body': 'This is a test message from the new system.',
        }
        response = self.client.post(reverse('mailmerge_page'), data, follow=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertRedirects(response, reverse('mailmerge_page'))

        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Failed to connect to SMTP server.")
        self.assertEqual(messages[0].tags, 'error')

    def test_mailmerge_page_post_form_invalid(self):
        """Test POST request with invalid form data."""
        data = {
            'from_email': 'invalid-email-format', # Invalid email
            'subject': '', # Missing subject
            'message_body': 'Some message content.',
        }
        response = self.client.post(reverse('mailmerge_page'), data)
        
        self.assertEqual(response.status_code, 200) # Should re-render the page with errors
        self.assertTemplateUsed(response, 'mailmerge/mailmerge_page.html')
        self.assertContains(response, 'Enter a valid email address.')
        self.assertContains(response, 'This field is required.')
        
        # Check for error message indicating form issues
        messages = list(response.context['messages'])
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Please correct the errors below.")
        self.assertEqual(messages[0].tags, 'error')

    def test_session_defaults_for_filtering(self):
        """Ensure default session values are used if `compid` and `finyear` are not set."""
        # Clear session to test default values
        session = self.client.session
        del session['compid']
        del session['finyear']
        session.save()

        response = self.client.get(reverse('supplier_table_partial')) # Test the partial view directly
        self.assertEqual(response.status_code, 200)
        # Should still fetch suppliers using default values (1 and 2024) set in get_queryset
        self.assertEqual(len(response.context['suppliers']), 3) # Alpha, Beta, Gamma (all comp_id=1, fin_year_id <= 2024)

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions & Implementation:**

- **HTMX for Dynamic Updates:**
    - The `_supplier_table.html` partial is loaded asynchronously using `hx-get` on the main page (`mailmerge_page.html`).
    - The `hx-trigger="load"` ensures the table loads when the page first opens.
    - `hx-trigger="refreshSupplierList from:body"` allows other parts of the application (e.g., if a supplier record was updated elsewhere) to trigger a refresh of this table by dispatching a custom `refreshSupplierList` event from the `body`.
    - This entirely replaces the `asp:UpdatePanel` and associated AJAX plumbing.
- **DataTables for List Views:**
    - The `_supplier_table.html` includes a `<script>` block that initializes jQuery DataTables on the rendered table.
    - `pageLength: 20` mimics the original ASP.NET `PageSize`.
    - `responsive: true`, `searching: true`, `ordering: true`, `paging: true` are added for modern client-side capabilities.
- **Alpine.js for UI State:**
    - While no complex UI state was evident in the original ASP.NET, `Alpine.js` is included in `base.html` and a basic `alpine:init` listener is present in the `mailmerge_page.html` `extra_js` block. This allows for easy integration of small client-side behaviors (like modals, toggles, etc.) should the need arise in the future, without writing verbose JavaScript. For this specific conversion, HTMX and DataTables handle most dynamic needs.
- **No Full Page Reloads (for table):** The supplier table dynamically loads and can be refreshed without a full page reload, improving user experience.
- **`HX-Trigger` Responses:** After a form submission (sending email), the page redirects via `HttpResponseRedirect`. If the form submission itself were done via HTMX, a 204 response with an `HX-Trigger` header (`HX-Trigger: 'refreshSupplierList'`) would be sent back to trigger the table refresh. For the current mail merge functionality, a full page redirect after email send is acceptable as the list itself isn't directly modified.

---

## Final Notes

- **Placeholders:** All `[PLACEHOLDER]` values from the prompt have been replaced with appropriate Django conventions and derived values from the ASP.NET code analysis.
- **DRY Principle:**
    - `base.html` (assumed to exist) is extended by all templates, ensuring a consistent site structure and shared static file inclusions.
    - The `_supplier_table.html` is a reusable partial, demonstrating efficient rendering.
    - Business logic (filtering, email sending) is encapsulated within the `Supplier` model methods, keeping views lean and focused on request handling.
- **Thin Views:** The `MailMergeView` methods (e.g., `get_context_data`, `post`) are concise, primarily orchestrating data fetching and form processing by delegating complex operations to the `Supplier` model.
- **Comprehensive Tests:** Both model unit tests and view integration tests are provided to ensure functional correctness and prevent regressions.
- **Tailwind CSS:** All HTML templates are structured with Tailwind CSS utility classes, providing a modern and responsive design.

This comprehensive plan provides a clear, actionable roadmap for migrating the Mail Merge functionality to a modern Django application, emphasizing automated, maintainable, and user-friendly solutions.