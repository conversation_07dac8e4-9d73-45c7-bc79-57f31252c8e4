## ASP.NET to Django Conversion Script: Proforma Invoice New

This document outlines a strategic plan to modernize the `ProformaInvoice_New.aspx` ASP.NET application to a robust, scalable, and maintainable Django-based solution. Our focus is on leveraging AI-assisted automation to transform the existing logic into a modern Django architecture, emphasizing a 'Fat Model, Thin View' approach, HTMX for dynamic interactions, and DataTables for superior data presentation.

This modernization focuses on the "Proforma Invoice - New" list view functionality, which primarily serves as a selection interface for existing Purchase Orders (POs) to initiate Proforma Invoice creation.

### Business Benefits of Django Modernization:

*   **Enhanced Performance & Scalability:** Django's optimized ORM and efficient request handling, combined with HTMX, will reduce server load and improve user responsiveness by minimizing full page reloads.
*   **Improved Maintainability:** Adhering to Django's clear structure (Models, Views, Templates) and the 'Fat Model, Thin View' principle centralizes business logic, making the codebase easier to understand, debug, and extend.
*   **Future-Proofing:** Transitioning from legacy ASP.NET Web Forms to a modern, actively developed framework like Django ensures long-term support, access to a vast ecosystem of tools, and a larger talent pool for development.
*   **Modern User Experience:** The combination of HTMX and Alpine.js delivers a highly interactive and intuitive user interface without the complexities of traditional JavaScript frameworks, providing a desktop-like experience in a web browser.
*   **Reduced Development Costs:** AI-assisted automation, coupled with Django's "batteries included" philosophy, significantly speeds up development and reduces the manual effort required for migration, leading to faster feature delivery.
*   **Stronger Security:** Django's built-in security features (e.g., CSRF protection, SQL injection prevention) inherently protect against common web vulnerabilities, enhancing data integrity and user trust.

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables. We'll map these to Django models, ensuring `managed = False` as per the existing database.

*   **Primary Table:** `SD_Cust_PO_Master` (representing Purchase Orders)
    *   `POId` (Primary Key, INT)
    *   `SysDate` (DATETIME)
    *   `PONo` (NVARCHAR)
    *   `CustomerId` (INT, Foreign Key to `SD_Cust_master`)
    *   `PODate` (DATETIME)
    *   `FinYearId` (INT, Foreign Key to `tblFinancial_master`)
    *   `CompId` (INT, Foreign Key to a Company master table, likely from session)

*   **Related Tables:**
    *   `SD_Cust_WorkOrder_Master`:
        *   `Id` (Primary Key, INT)
        *   `WONo` (NVARCHAR)
        *   `TaskProjectTitle` (NVARCHAR)
        *   `POId` (INT, Foreign Key to `SD_Cust_PO_Master`)
        *   `CompId` (INT)
    *   `tblACC_InvoiceAgainst`:
        *   `Id` (Primary Key, INT)
        *   `Against` (NVARCHAR)
    *   `tblFinancial_master`:
        *   `FinYearId` (Primary Key, INT)
        *   `FinYear` (NVARCHAR)
    *   `SD_Cust_master`:
        *   `CustomerId` (Primary Key, INT)
        *   `CustomerName` (NVARCHAR)
        *   `CompId` (INT)
    *   `SD_Cust_PO_Details`:
        *   `Id` (Primary Key, INT)
        *   `POId` (INT, Foreign Key to `SD_Cust_PO_Master`)
        *   `TotalQty` (DECIMAL)
    *   `tblACC_ProformaInvoice_Master`:
        *   `Id` (Primary Key, INT)
        *   `CompId` (INT)
        *   `POId` (INT, Foreign Key to `SD_Cust_PO_Master`)
    *   `tblACC_ProformaInvoice_Details`:
        *   `Id` (Primary Key, INT - inferred, as it's not explicitly in the ASP.NET schema but is good practice)
        *   `MId` (INT, Foreign Key to `tblACC_ProformaInvoice_Master.Id`)
        *   `ReqQty` (DECIMAL)
        *   `ItemId` (INT, Foreign Key to `SD_Cust_PO_Details.Id`)

### Step 2: Identify Backend Functionality

The core functionality of this ASP.NET page is:

*   **Read (Display List):** Fetching and displaying a list of Purchase Orders.
*   **Search/Filter:** Allowing users to search by Customer Name or PO Number.
*   **Dynamic Data Fetching:** Fetching Work Orders (`WONo` + `TaskProjectTitle`) associated with a specific Purchase Order dynamically.
*   **Data Aggregation:** Calculating remaining quantities and "Invoice Count" for each PO based on linked `ProformaInvoice_Details`. This complex logic will be encapsulated in Django models.
*   **Selection & Redirection:** Allowing users to select a Purchase Order, associated Work Orders, and an "Against" type, then redirecting to a "Proforma Invoice Details" page (which is outside the scope of this particular migration plan, but its parameters are identified).
*   **Customer Auto-Complete:** Providing real-time suggestions for customer names during search.

### Step 3: Infer UI Components

The ASP.NET page uses various Web Forms controls to build its interface:

*   **Search/Filter Bar:**
    *   A dropdown (`DropDownList1`) to switch between "Customer Name" and "PO No" search modes.
    *   Two text boxes (`txtCustName`, `txtpoNo`) for input, dynamically shown/hidden.
    *   An `AutoCompleteExtender` for `txtCustName` for customer lookup.
    *   A search button (`btnSearch`).
*   **Main Data Grid:**
    *   A `GridView` (`GridView1`) displays the list of Purchase Orders with columns like:
        *   "SN" (Serial Number)
        *   "FinYear" (Financial Year)
        *   "Name of Customer"
        *   "PO No" (Purchase Order Number)
        *   "Date" (System Date)
        *   "WO No" (Work Order Number - a complex field with a multi-select dropdown)
        *   "PO Id" (hidden)
        *   "Against" (a dropdown list)
        *   Action: A "Select" `LinkButton` to initiate redirection.
        *   "CustomerId" (hidden)
        *   "Inv. Count" (Invoice Count)
*   **Pagination:** Handled by the `GridView`.
*   **Empty Data Message:** Displays "No data to display !".

This will be mapped to a single Django list template using DataTables for the grid and HTMX for all dynamic interactions, including search and the complex Work Order selection.

---

### Step 4: Generate Django Code

We will structure the Django application as `transactions` (assuming this module falls under transaction management) and focus on the `PurchaseOrder` as the primary entity for this view.

**App Name:** `transactions`
**Model Name:** `PurchaseOrder`
**Model Name Plural:** `PurchaseOrders`
**Model Name Lower:** `purchaseorder`
**Model Name Plural Lower:** `purchaseorders`

#### 4.1 Models (`transactions/models.py`)

We'll define all related models, emphasizing the `PurchaseOrder` model with a custom manager to encapsulate the complex data retrieval and calculation logic (like remaining quantity and invoice count) from the `bindgrid` method.

```python
from django.db import models
from django.db.models import Sum, F, OuterRef, Subquery, Value
from django.db.models.functions import Coalesce
from datetime import datetime

# Custom Manager for PurchaseOrder to handle complex queries and business logic
class PurchaseOrderManager(models.Manager):
    def get_queryset(self):
        # Base queryset for PurchaseOrder
        return super().get_queryset()

    def with_invoice_details(self, company_id, financial_year_id):
        # Subquery to calculate total required quantity from proforma invoices
        # for each purchase order detail item.
        # This mirrors the 'sqlrmn' query in the original code.
        proforma_invoice_details_subquery = ProformaInvoiceLineItem.objects.filter(
            purchase_order_detail=OuterRef('pk'), # Referencing SD_Cust_PO_Details.Id
            proforma_invoice__purchase_order=OuterRef('purchase_order__pk'), # Referencing SD_Cust_PO_Master.POId
            proforma_invoice__company_id=company_id
        ).values('purchase_order_detail').annotate(
            total_req_qty=Coalesce(Sum('required_quantity'), 0.0)
        ).values('total_req_qty')

        # Annotate PurchaseOrderLineItem with remaining quantity
        po_details_with_remaining_qty = PurchaseOrderLineItem.objects.filter(
            purchase_order=OuterRef('pk')
        ).annotate(
            remaining_qty=F('total_quantity') - Coalesce(Subquery(proforma_invoice_details_subquery, output_field=models.DecimalField()), 0.0)
        )

        # Filter PurchaseOrders based on remaining quantity > 0
        # This is complex to do directly in the ORM for the main PO query.
        # We'll filter the main POs first and then re-evaluate the remaining quantity in Python
        # or simplify the filter if possible. For now, we fetch all and filter in Python
        # if the ORM query becomes too unwieldy.
        # Let's try to simulate the Y1 logic from the original code within the manager.

        # Annotate purchase orders with financial year and customer name for display
        queryset = self.get_queryset().filter(
            company_id=company_id,
            financial_year_id__lte=financial_year_id # Assuming FinYearId check
        ).select_related('customer', 'financial_year').order_by('-po_id')

        # Manual filtering for remaining quantity logic (rmnqty > 0) as it's intricate
        # It's better to fetch related details and then iterate.
        # This is where 'Fat Model' comes in; the filtering could be a method on the PO.
        return queryset

    def get_filterable_purchase_orders(self, company_id, financial_year_id, customer_id=None, po_number=None):
        queryset = self.get_queryset().filter(
            company_id=company_id,
            financial_year_id__lte=financial_year_id # Assuming FinYearId check
        ).select_related('customer', 'financial_year').order_by('-po_id')

        if customer_id:
            queryset = queryset.filter(customer__customer_id=customer_id)
        if po_number:
            queryset = queryset.filter(po_number=po_number)

        # Apply the complex business logic here:
        # Only include POs that have at least one line item with remaining quantity > 0
        # and annotate with the invoice count for that PO.
        final_pos = []
        for po in queryset:
            has_remaining_qty = False
            for detail in po.purchaseorderlineitem_set.all():
                # Calculate required quantity for this detail from proforma invoices
                total_req_qty = detail.proformainvoicelineitem_set.filter(
                    proforma_invoice__company_id=company_id
                ).aggregate(Sum('required_quantity'))['required_quantity__sum'] or 0.0

                if detail.total_quantity - total_req_qty > 0:
                    has_remaining_qty = True
                    break # Found at least one item with remaining quantity

            if has_remaining_qty:
                # Get the invoice count for this PO
                invoice_count = ProformaInvoice.objects.filter(
                    company_id=company_id,
                    purchase_order=po
                ).count()
                po.invoice_count = invoice_count # Attach as an attribute for rendering
                final_pos.append(po)

        return final_pos

class Company(models.Model):
    # Assuming a Company master table, ID is often 'CompId'
    # Define fields as per your actual Company table
    company_id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCompany_master' # Replace with actual table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name

class FinancialYear(models.Model):
    financial_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    financial_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.financial_year

class Customer(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', related_name='customers') # Assuming FK relationship

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class PurchaseOrder(models.Model):
    # Corresponds to SD_Cust_PO_Master
    po_id = models.IntegerField(db_column='POId', primary_key=True)
    system_date = models.DateTimeField(db_column='SysDate')
    po_number = models.CharField(db_column='PONo', max_length=100)
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId')
    po_date = models.DateTimeField(db_column='PODate', null=True, blank=True)
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    company_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')

    objects = PurchaseOrderManager() # Attach our custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_number

    def get_formatted_sysdate(self):
        return self.system_date.strftime('%d/%m/%Y') if self.system_date else ''

    def get_customer_display_name(self):
        return f"{self.customer.customer_name} [{self.customer.customer_id}]" if self.customer else ""

class PurchaseOrderLineItem(models.Model):
    # Corresponds to SD_Cust_PO_Details
    id = models.IntegerField(db_column='Id', primary_key=True)
    purchase_order = models.ForeignKey(PurchaseOrder, models.DO_NOTHING, db_column='POId')
    total_quantity = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"PO Detail {self.id} for {self.purchase_order.po_number}"

class WorkOrder(models.Model):
    # Corresponds to SD_Cust_WorkOrder_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_number = models.CharField(db_column='WONo', max_length=100)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255)
    purchase_order = models.ForeignKey(PurchaseOrder, models.DO_NOTHING, db_column='POId')
    company_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.wo_number}-{self.task_project_title}"

class InvoiceAgainst(models.Model):
    # Corresponds to tblACC_InvoiceAgainst
    id = models.IntegerField(db_column='Id', primary_key=True)
    against_name = models.CharField(db_column='Against', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblACC_InvoiceAgainst'
        verbose_name = 'Invoice Against'
        verbose_name_plural = 'Invoice Againsts'

    def __str__(self):
        return self.against_name

class ProformaInvoice(models.Model):
    # Corresponds to tblACC_ProformaInvoice_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    company_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    purchase_order = models.ForeignKey(PurchaseOrder, models.DO_NOTHING, db_column='POId')
    # Add other fields as per tblACC_ProformaInvoice_Master

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Master'
        verbose_name = 'Proforma Invoice'
        verbose_name_plural = 'Proforma Invoices'

    def __str__(self):
        return f"Proforma Invoice {self.id} for PO {self.purchase_order.po_number}"

class ProformaInvoiceLineItem(models.Model):
    # Corresponds to tblACC_ProformaInvoice_Details
    id = models.IntegerField(db_column='Id', primary_key=True) # Inferred
    proforma_invoice = models.ForeignKey(ProformaInvoice, models.DO_NOTHING, db_column='MId')
    required_quantity = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3)
    purchase_order_detail = models.ForeignKey(PurchaseOrderLineItem, models.DO_NOTHING, db_column='ItemId')

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Details'
        verbose_name = 'Proforma Invoice Detail'
        verbose_name_plural = 'Proforma Invoice Details'

    def __str__(self):
        return f"Proforma Invoice Detail {self.id}"

```

#### 4.2 Forms (`transactions/forms.py`)

For the search functionality, we'll create a simple form that manages the search criteria. Since this page primarily lists and selects, we won't need a `ModelForm` for `PurchaseOrder` itself here.

```python
from django import forms
from .models import Customer

class PurchaseOrderSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('customer_name', 'Customer Name'),
        ('po_number', 'PO No'),
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 hx-post hx-target="#purchaseOrderTable-container" hx-swap="innerHTML" hx-trigger="change"'})
    )
    customer_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[350px] data-[search-type="customer_name"]:block hidden',
            'placeholder': 'Enter Customer Name',
            'list': 'customer_suggestions', # For datalist based autocomplete
            'hx-post': '/transactions/customer-autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#customer_suggestions',
            'hx-swap': 'innerHTML'
        })
    )
    po_number = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 data-[search-type="po_number"]:block hidden',
            'placeholder': 'Enter PO Number'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initial visibility based on search_by
        initial_search_by = self.initial.get('search_by', 'customer_name')
        if initial_search_by == 'po_number':
            self.fields['customer_name'].widget.attrs['class'] += ' hidden'
            self.fields['po_number'].widget.attrs['class'] = self.fields['po_number'].widget.attrs['class'].replace(' hidden', '')
        else: # Default to customer_name
            self.fields['customer_name'].widget.attrs['class'] = self.fields['customer_name'].widget.attrs['class'].replace(' hidden', '')
            self.fields['po_number'].widget.attrs['class'] += ' hidden'


    # We will use Alpine.js for showing/hiding fields based on dropdown selection
    # and HTMX for the autocomplete via a dedicated endpoint.

```

#### 4.3 Views (`transactions/views.py`)

The main view will be a `ListView` for `PurchaseOrder`. We'll also need a separate HTMX endpoint for the dynamic Work Order dropdown and for customer auto-complete.
Session values for `CompId` and `FinYearId` will be accessed from `request.session`.

```python
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.db.models import Q

from .models import PurchaseOrder, WorkOrder, InvoiceAgainst, Customer
from .forms import PurchaseOrderSearchForm

# Define current company_id and financial_year_id (replace with actual session/context retrieval)
# For demonstration, using placeholders. In a real app, retrieve from request.session.
# Mock values:
CURRENT_COMPANY_ID = 1 # Example company ID from session
CURRENT_FIN_YEAR_ID = 2023 # Example financial year ID from session

class PurchaseOrderListView(TemplateView):
    template_name = 'transactions/purchaseorder/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['search_form'] = PurchaseOrderSearchForm(self.request.GET)
        context['invoice_against_options'] = InvoiceAgainst.objects.all() # For dropdown in table
        return context

# HTMX endpoint for the purchase order table content (for search/refresh)
class PurchaseOrderTablePartialView(ListView):
    model = PurchaseOrder
    template_name = 'transactions/purchaseorder/_purchaseorder_table.html'
    context_object_name = 'purchaseorders'

    def get_queryset(self):
        # Retrieve company_id and financial_year_id from session or context
        company_id = self.request.session.get('compid', CURRENT_COMPANY_ID)
        financial_year_id = self.request.session.get('finyear', CURRENT_FIN_YEAR_ID)

        search_form = PurchaseOrderSearchForm(self.request.GET)
        customer_id = None
        po_number = None

        if search_form.is_valid():
            search_by = search_form.cleaned_data.get('search_by')
            if search_by == 'customer_name':
                customer_name_input = search_form.cleaned_data.get('customer_name')
                # Need to lookup customer_id from customer_name string.
                # The original ASP.NET had a `fun.getCode` for this.
                # We'll try to find the Customer by name, assuming uniqueness or picking first match.
                if customer_name_input:
                    customer_obj = Customer.objects.filter(customer_name=customer_name_input, company_id=company_id).first()
                    if customer_obj:
                        customer_id = customer_obj.customer_id
            elif search_by == 'po_number':
                po_number = search_form.cleaned_data.get('po_number')

        # Use the custom manager to get filtered and annotated data
        queryset = PurchaseOrder.objects.get_filterable_purchase_orders(
            company_id=company_id,
            financial_year_id=financial_year_id,
            customer_id=customer_id,
            po_number=po_number
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['invoice_against_options'] = InvoiceAgainst.objects.all()
        return context


# HTMX endpoint for customer autocomplete suggestions
class CustomerAutocompleteView(View):
    def post(self, request, *args, **kwargs): # Using POST as HTMX suggests for form submissions
        # company_id = request.session.get('compid', CURRENT_COMPANY_ID)
        prefix_text = request.POST.get('customer_name_input', '').strip() # The name of the input field
        company_id = self.request.session.get('compid', CURRENT_COMPANY_ID)

        if not prefix_text:
            return HttpResponse('<datalist id="customer_suggestions"></datalist>')

        customers = Customer.objects.filter(
            customer_name__icontains=prefix_text,
            company_id=company_id
        )[:10] # Limit to top 10 results, similar to original

        options_html = '<datalist id="customer_suggestions">'
        for customer in customers:
            options_html += f'<option value="{customer.customer_name} [{customer.customer_id}]"></option>'
        options_html += '</datalist>'

        return HttpResponse(options_html)


# HTMX endpoint to fetch Work Orders for a specific PO
class WorkOrderSelectPartialView(View):
    def get(self, request, po_id, *args, **kwargs):
        # company_id = request.session.get('compid', CURRENT_COMPANY_ID)
        company_id = self.request.session.get('compid', CURRENT_COMPANY_ID)

        work_orders = WorkOrder.objects.filter(
            purchase_order__po_id=po_id,
            company_id=company_id
        )
        return render(request, 'transactions/purchaseorder/_workorder_select.html', {
            'work_orders': work_orders,
            'po_id': po_id # Pass po_id to ensure context
        })

```

#### 4.4 Templates (`transactions/templates/transactions/purchaseorder/`)

We'll create the main list template and partials for the table and the dynamic Work Order selector. Remember, `base.html` is assumed to exist and is not included here.

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg mb-4 shadow-md">
        <h2 class="text-2xl font-bold">Proforma Invoice - New</h2>
    </div>

    <div class="bg-white p-6 rounded-b-lg shadow-md mb-6" x-data="{ searchBy: '{{ search_form.search_by.value|default:'customer_name' }}' }">
        <form hx-get="{% url 'purchaseorder_table' %}" hx-target="#purchaseOrderTable-container" hx-swap="innerHTML" hx-trigger="submit, change from:#id_search_by">
            {% csrf_token %}
            <div class="flex items-center space-x-4 mb-4">
                <label for="id_search_by" class="font-medium text-gray-700">Search By:</label>
                {{ search_form.search_by }}
                <div class="flex-grow">
                    <input type="text" name="customer_name" id="id_customer_name"
                           x-bind:class="{'hidden': searchBy !== 'customer_name', 'block': searchBy === 'customer_name'}"
                           x-model="searchBy === 'customer_name' ? $refs.custNameInput.value : ''"
                           x-ref="custNameInput"
                           value="{{ search_form.customer_name.value|default:'' }}"
                           class="box3 w-[350px] px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                           placeholder="Enter Customer Name" list="customer_suggestions"
                           hx-post="{% url 'customer_autocomplete' %}"
                           hx-trigger="keyup changed delay:500ms from:#id_customer_name"
                           hx-target="#customer_suggestions_wrapper"
                           hx-swap="innerHTML">
                    <div id="customer_suggestions_wrapper" class="absolute z-10 bg-white border border-gray-200 rounded-md shadow-lg w-full">
                        <datalist id="customer_suggestions"></datalist>
                    </div>

                    <input type="text" name="po_number" id="id_po_number"
                           x-bind:class="{'hidden': searchBy !== 'po_number', 'block': searchBy === 'po_number'}"
                           x-model="searchBy === 'po_number' ? $refs.poNoInput.value : ''"
                           x-ref="poNoInput"
                           value="{{ search_form.po_number.value|default:'' }}"
                           class="box3 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                           placeholder="Enter PO Number">
                </div>
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Search
                </button>
            </div>
            <script>
                // Alpine.js to toggle visibility based on dropdown selection
                document.addEventListener('alpine:init', () => {
                    Alpine.data('searchForm', () => ({
                        searchBy: '{{ search_form.search_by.value|default:'customer_name' }}',
                        init() {
                            this.$watch('searchBy', value => {
                                const custNameInput = document.getElementById('id_customer_name');
                                const poNoInput = document.getElementById('id_po_number');
                                if (value === 'customer_name') {
                                    custNameInput.classList.remove('hidden');
                                    poNoInput.classList.add('hidden');
                                    poNoInput.value = ''; // Clear other field
                                } else {
                                    custNameInput.classList.add('hidden');
                                    poNoInput.classList.remove('hidden');
                                    custNameInput.value = ''; // Clear other field
                                }
                            });
                        }
                    }));
                });
            </script>
        </form>
    </div>

    <div id="purchaseOrderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body, submit from:form"
         hx-get="{% url 'purchaseorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Purchase Orders...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DataTables initialization (will be triggered on HTMX swap)
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'purchaseOrderTable-container') {
            $('#purchaseOrderTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex flex-col sm:flex-row justify-between mb-4"lf><"overflow-x-auto"t><"flex flex-col sm:flex-row justify-between mt-4"ip>',
                "language": {
                    "lengthMenu": "Show _MENU_ entries",
                    "search": "Search:",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                },
                "columnDefs": [
                    { "orderable": false, "targets": [0, 5, 7, 8] } // SN, WO No, Against, Actions
                ]
            });
        }
    });

    // Handle form submission via HTMX on dropdown/textbox change
    document.addEventListener('DOMContentLoaded', function() {
        const searchByDropdown = document.getElementById('id_search_by');
        const customerNameInput = document.getElementById('id_customer_name');
        const poNumberInput = document.getElementById('id_po_number');

        searchByDropdown.addEventListener('change', function() {
            // Trigger HTMX GET request when search_by changes
            // This is handled by hx-trigger="change from:#id_search_by" on the form
        });

        customerNameInput.addEventListener('change', function() {
            // Trigger HTMX GET request when customer_name changes (after autocomplete selection)
            htmx.trigger(this.closest('form'), 'submit');
        });

        poNumberInput.addEventListener('change', function() {
            // Trigger HTMX GET request when po_number changes
            htmx.trigger(this.closest('form'), 'submit');
        });
    });
</script>
{% endblock %}
```

**`_purchaseorder_table.html` (Partial for HTMX swapping)**

```html
<div class="overflow-x-auto rounded-lg shadow-md">
    <table id="purchaseOrderTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Customer</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Against</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inv. Count</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% if purchaseorders %}
                {% for po in purchaseorders %}
                <tr class="hover:bg-gray-50" x-data="{ selectedWOs: '', selectedWOIds: '' }">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.financial_year.financial_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ po.get_customer_display_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.po_number }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.get_formatted_sysdate }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        <div x-data="{ open: false }" class="relative inline-block text-left w-full">
                            <input type="text" x-model="selectedWOs" readonly
                                   class="box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 cursor-pointer focus:outline-none"
                                   placeholder="Select Work Orders..."
                                   @click="open = !open; if(open) $dispatch('fetch-wo-{{ po.po_id }}')"
                            >
                            <input type="hidden" x-model="selectedWOIds" name="hfWOno-{{ po.po_id }}">

                            <div x-show="open"
                                 x-cloak
                                 @click.away="open = false"
                                 class="origin-top-right absolute right-0 mt-2 w-full rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-20"
                                 hx-get="{% url 'workorder_select' po.po_id %}"
                                 hx-target="this"
                                 hx-swap="innerHTML"
                                 hx-trigger="fetch-wo-{{ po.po_id }} from:body once"
                            >
                                <div class="py-1">
                                    <!-- Work orders will be loaded here via HTMX -->
                                    <div class="text-center py-4">
                                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-400"></div>
                                        <p class="mt-1 text-xs text-gray-500">Loading...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        <select name="drp1-{{ po.po_id }}" class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            {% for option in invoice_against_options %}
                                <option value="{{ option.id }}">{{ option.against_name }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.invoice_count|default:0 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <button type="button"
                                class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-3 rounded text-sm"
                                @click="
                                    const selectedAgainst = $event.target.closest('tr').querySelector('select[name^=drp1]').value;
                                    const poId = '{{ po.po_id }}';
                                    const poNo = '{{ po.po_number }}';
                                    const sysDate = '{{ po.get_formatted_sysdate }}';
                                    const customerId = '{{ po.customer.customer_id }}';

                                    if (selectedWOIds === '') {
                                        alert('Select WONo and Against.');
                                    } else {
                                        // Construct URL for the Proforma Invoice Details page
                                        // This assumes a Django URL 'proforma_invoice_details' that handles these params
                                        const redirectUrl = `{% url 'proforma_invoice_details' %}?poid=${poId}&wn=${selectedWOIds}&pn=${poNo}&date=${sysDate}&ty=${selectedAgainst}&cid=${customerId}`;
                                        window.location.href = redirectUrl;
                                    }
                                ">
                            Select
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-4 px-4 border-b border-gray-200 text-center text-red-500 text-lg font-semibold">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>
```

**`_workorder_select.html` (Partial for Work Order dropdown content)**

```html
<ul class="max-h-60 overflow-y-auto">
    {% if work_orders %}
        {% for wo in work_orders %}
        <li class="px-4 py-2 hover:bg-blue-100 cursor-pointer">
            <label class="inline-flex items-center">
                <input type="checkbox" value="{{ wo.id }}" data-text="{{ wo.wo_number }}-{{ wo.task_project_title }}"
                       @change="
                           let selectedTexts = [];
                           let selectedValues = [];
                           $el.closest('ul').querySelectorAll('input[type=checkbox]:checked').forEach(cb => {
                               selectedTexts.push(cb.dataset.text);
                               selectedValues.push(cb.value);
                           });
                           $el.closest('tr').querySelector('input[x-model^=selectedWOs]').value = selectedTexts.join(',');
                           $el.closest('tr').querySelector('input[x-model^=selectedWOIds]').value = selectedValues.join(',');
                       "
                       class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                <span class="ml-2 text-gray-700">{{ wo.wo_number }}-{{ wo.task_project_title }}</span>
            </label>
        </li>
        {% endfor %}
    {% else %}
        <li class="px-4 py-2 text-gray-500 text-center">No Work Orders found.</li>
    {% endif %}
</ul>
```

#### 4.5 URLs (`transactions/urls.py`)

```python
from django.urls import path
from .views import (
    PurchaseOrderListView,
    PurchaseOrderTablePartialView,
    CustomerAutocompleteView,
    WorkOrderSelectPartialView,
)

urlpatterns = [
    # Main page for Proforma Invoice New
    path('proforma-invoice-new/', PurchaseOrderListView.as_view(), name='proforma_invoice_new_list'),

    # HTMX endpoint for the main purchase order table (for search/refresh)
    path('proforma-invoice-new/table/', PurchaseOrderTablePartialView.as_view(), name='purchaseorder_table'),

    # HTMX endpoint for customer autocomplete suggestions
    path('customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # HTMX endpoint to fetch Work Orders for a specific PO dynamically
    path('work-orders/<int:po_id>/select/', WorkOrderSelectPartialView.as_view(), name='workorder_select'),

    # Placeholder for the details page redirect. This URL should be defined in its own app.
    # For example: path('proforma-invoice-details/', YourProformaInvoiceDetailsView.as_view(), name='proforma_invoice_details'),
    # This URL would receive the query parameters from the 'Select' button.
]
```

#### 4.6 Tests (`transactions/tests.py`)

Comprehensive tests for models, managers, and views are crucial.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    Company, FinancialYear, Customer, PurchaseOrder,
    PurchaseOrderLineItem, WorkOrder, InvoiceAgainst,
    ProformaInvoice, ProformaInvoiceLineItem
)
from datetime import datetime
from decimal import Decimal

# Mock constants for session data in tests
TEST_COMPANY_ID = 99
TEST_FIN_YEAR_ID = 2024

class ModelSetupMixin(TestCase):
    """Mixin to set up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create base data
        cls.company = Company.objects.create(company_id=TEST_COMPANY_ID, name="Test Company")
        cls.financial_year = FinancialYear.objects.create(financial_year_id=TEST_FIN_YEAR_ID, financial_year="2024-2025")
        cls.customer1 = Customer.objects.create(customer_id=101, customer_name="Customer A", company_id=cls.company)
        cls.customer2 = Customer.objects.create(customer_id=102, customer_name="Customer B", company_id=cls.company)

        # Create InvoiceAgainst options
        cls.invoice_against_type1 = InvoiceAgainst.objects.create(id=1, against_name="Against WO")
        cls.invoice_against_type2 = InvoiceAgainst.objects.create(id=2, against_name="Against PO")

        # Create Purchase Order 1 (with remaining qty)
        cls.po1 = PurchaseOrder.objects.create(
            po_id=1,
            system_date=datetime(2024, 1, 1),
            po_number="PO-001",
            customer=cls.customer1,
            po_date=datetime(2023, 12, 15),
            financial_year=cls.financial_year,
            company_id=cls.company
        )
        cls.po1_detail1 = PurchaseOrderLineItem.objects.create(id=1, purchase_order=cls.po1, total_quantity=Decimal('10.000'))
        cls.po1_detail2 = PurchaseOrderLineItem.objects.create(id=2, purchase_order=cls.po1, total_quantity=Decimal('5.000'))
        # Create some work orders for PO1
        cls.po1_wo1 = WorkOrder.objects.create(id=1, wo_number="WO-001", task_project_title="Project A", purchase_order=cls.po1, company_id=cls.company)
        cls.po1_wo2 = WorkOrder.objects.create(id=2, wo_number="WO-002", task_project_title="Project B", purchase_order=cls.po1, company_id=cls.company)

        # Create Proforma Invoice for PO1, partially fulfilling po1_detail1
        cls.pi1 = ProformaInvoice.objects.create(id=1, company_id=cls.company, purchase_order=cls.po1)
        ProformaInvoiceLineItem.objects.create(id=1, proforma_invoice=cls.pi1, required_quantity=Decimal('3.000'), purchase_order_detail=cls.po1_detail1)

        # Create Purchase Order 2 (no remaining qty - fully invoiced or 0 total qty)
        cls.po2 = PurchaseOrder.objects.create(
            po_id=2,
            system_date=datetime(2024, 1, 2),
            po_number="PO-002",
            customer=cls.customer2,
            po_date=datetime(2023, 12, 20),
            financial_year=cls.financial_year,
            company_id=cls.company
        )
        cls.po2_detail1 = PurchaseOrderLineItem.objects.create(id=3, purchase_order=cls.po2, total_quantity=Decimal('8.000'))
        # Fully invoice po2_detail1
        cls.pi2 = ProformaInvoice.objects.create(id=2, company_id=cls.company, purchase_order=cls.po2)
        ProformaInvoiceLineItem.objects.create(id=2, proforma_invoice=cls.pi2, required_quantity=Decimal('8.000'), purchase_order_detail=cls.po2_detail1)

        # Create Purchase Order 3 (no line items, so no remaining qty)
        cls.po3 = PurchaseOrder.objects.create(
            po_id=3,
            system_date=datetime(2024, 1, 3),
            po_number="PO-003",
            customer=cls.customer1,
            po_date=datetime(2024, 1, 1),
            financial_year=cls.financial_year,
            company_id=cls.company
        )

class PurchaseOrderModelTest(ModelSetupMixin):
    def test_po_creation(self):
        self.assertEqual(self.po1.po_number, "PO-001")
        self.assertEqual(self.po1.customer.customer_name, "Customer A")
        self.assertEqual(self.po1.company_id.name, "Test Company")

    def test_get_formatted_sysdate(self):
        self.assertEqual(self.po1.get_formatted_sysdate(), "01/01/2024")

    def test_get_customer_display_name(self):
        self.assertEqual(self.po1.get_customer_display_name(), "Customer A [101]")

    def test_purchase_order_manager_get_filterable_purchase_orders(self):
        # PO1 should be included as it has remaining quantity
        pos = PurchaseOrder.objects.get_filterable_purchase_orders(TEST_COMPANY_ID, TEST_FIN_YEAR_ID)
        self.assertEqual(len(pos), 1) # Only PO1 should have remaining qty
        self.assertEqual(pos[0].po_id, self.po1.po_id)
        self.assertEqual(pos[0].invoice_count, 1) # PI1 is linked to PO1

        # PO2 should NOT be included (fully invoiced)
        # PO3 should NOT be included (no line items)
        
        # Test search by customer
        pos_by_cust = PurchaseOrder.objects.get_filterable_purchase_orders(
            TEST_COMPANY_ID, TEST_FIN_YEAR_ID, customer_id=self.customer1.customer_id
        )
        self.assertEqual(len(pos_by_cust), 1)
        self.assertEqual(pos_by_cust[0].po_id, self.po1.po_id)

        # Test search by PO number
        pos_by_po_num = PurchaseOrder.objects.get_filterable_purchase_orders(
            TEST_COMPANY_ID, TEST_FIN_YEAR_ID, po_number="PO-001"
        )
        self.assertEqual(len(pos_by_po_num), 1)
        self.assertEqual(pos_by_po_num[0].po_id, self.po1.po_id)

        # Test no matching criteria
        pos_no_match = PurchaseOrder.objects.get_filterable_purchase_orders(
            TEST_COMPANY_ID, TEST_FIN_YEAR_ID, po_number="NON_EXISTENT_PO"
        )
        self.assertEqual(len(pos_no_match), 0)

class PurchaseOrderViewsTest(ModelSetupMixin):
    def setUp(self):
        self.client = Client()
        # Set session variables for views to pick up
        session = self.client.session
        session['compid'] = TEST_COMPANY_ID
        session['finyear'] = TEST_FIN_YEAR_ID
        session.save()

    def test_purchaseorder_list_view_get(self):
        response = self.client.get(reverse('proforma_invoice_new_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/purchaseorder/list.html')
        self.assertIn('search_form', response.context)
        self.assertIn('invoice_against_options', response.context)
        self.assertEqual(len(response.context['invoice_against_options']), 2)

    def test_purchaseorder_table_partial_view_get_initial(self):
        response = self.client.get(reverse('purchaseorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/purchaseorder/_purchaseorder_table.html')
        # Only PO1 should be in the initial list because of remaining quantity filter
        self.assertEqual(len(response.context['purchaseorders']), 1)
        self.assertEqual(response.context['purchaseorders'][0].po_id, self.po1.po_id)

    def test_purchaseorder_table_partial_view_search_by_customer_name(self):
        response = self.client.get(reverse('purchaseorder_table'), {
            'search_by': 'customer_name',
            'customer_name': 'Customer A [101]' # This format is expected from autocomplete
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['purchaseorders']), 1)
        self.assertEqual(response.context['purchaseorders'][0].po_id, self.po1.po_id)

    def test_purchaseorder_table_partial_view_search_by_po_number(self):
        response = self.client.get(reverse('purchaseorder_table'), {
            'search_by': 'po_number',
            'po_number': 'PO-001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['purchaseorders']), 1)
        self.assertEqual(response.context['purchaseorders'][0].po_id, self.po1.po_id)

    def test_customer_autocomplete_view(self):
        response = self.client.post(reverse('customer_autocomplete'), {'customer_name_input': 'Cust'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Customer A [101]')
        self.assertContains(response, 'Customer B [102]')
        self.assertEqual(response['Content-Type'], 'text/html; charset=utf-8')

    def test_customer_autocomplete_view_empty_prefix(self):
        response = self.client.post(reverse('customer_autocomplete'), {'customer_name_input': ''})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<datalist id="customer_suggestions"></datalist>')

    def test_workorder_select_partial_view(self):
        response = self.client.get(reverse('workorder_select', args=[self.po1.po_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/purchaseorder/_workorder_select.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 2)
        self.assertContains(response, 'WO-001-Project A')
        self.assertContains(response, 'WO-002-Project B')

    def test_workorder_select_partial_view_no_work_orders(self):
        # Create a PO with no work orders
        po_no_wo = PurchaseOrder.objects.create(
            po_id=4, system_date=datetime(2024, 1, 4), po_number="PO-004", customer=self.customer1,
            po_date=datetime(2024, 1, 1), financial_year=self.financial_year, company_id=self.company
        )
        response = self.client.get(reverse('workorder_select', args=[po_no_wo.po_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'transactions/purchaseorder/_workorder_select.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 0)
        self.assertContains(response, 'No Work Orders found.')

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Search & Table Refresh:** The `list.html` uses `hx-get` on the form to `purchaseorder_table` and `hx-target` on `#purchaseOrderTable-container` to update the grid dynamically. This replaces `AutoPostBack` and `btnSearch_Click`. `hx-trigger="load, refreshPurchaseOrderList from:body, submit from:form"` ensures the table loads on page load and refreshes when the search form is submitted or an external trigger `refreshPurchaseOrderList` is fired (e.g., if a related CRUD operation elsewhere necessitates it).
*   **HTMX for Customer Autocomplete:** The `customer_name` input uses `hx-post` to `customer_autocomplete` with `hx-trigger="keyup changed delay:500ms"` to fetch suggestions. The `hx-target` and `hx-swap` are set to update a `datalist` element, providing native browser autocomplete functionality.
*   **HTMX for Work Order Selection:** Each "WO No" cell in the table (within `_purchaseorder_table.html`) includes a hidden `div` that is populated via `hx-get` to `workorder_select` endpoint when its associated dropdown/input is clicked. This fetches the Work Orders *for that specific PO only*, reducing unnecessary data transfer. `hx-trigger="fetch-wo-{{ po.po_id }} from:body once"` ensures it only loads once.
*   **Alpine.js for UI State:**
    *   **Search Form Toggle:** `x-data` and `x-bind:class` are used on the search inputs to toggle their visibility based on the `searchBy` value of the `search_by` dropdown, mimicking the `Visible="False"` logic.
    *   **Work Order Multi-select:** `x-data` on the table row (specifically `x-data="{ selectedWOs: '', selectedWOIds: '' }"`) manages the local state for the visible Work Orders string and the hidden Work Order IDs. The checkboxes within `_workorder_select.html` update these Alpine variables when clicked.
    *   **Modal Behavior (if applicable):** Though not strictly a CRUD form here, the modal logic for `add`/`edit`/`delete` found in the template examples demonstrates how Alpine.js manages modal open/close states.
*   **DataTables Integration:** The `_purchaseorder_table.html` defines a standard HTML table. The `extra_js` block in `list.html` includes a `htmx:afterSwap` listener. This listener ensures that DataTables is initialized *after* the HTMX swap completes and the table content is loaded into the DOM, providing client-side sorting, filtering, and pagination.
*   **No Additional JavaScript:** All dynamic interactions are handled by HTMX for server communication and Alpine.js for lightweight client-side UI state, avoiding heavier JavaScript frameworks.
*   **DRY Templates:** Use of partial templates (`_purchaseorder_table.html`, `_workorder_select.html`) ensures reusability and clean separation of concerns.
*   **Tailwind CSS:** All generated HTML includes Tailwind CSS classes for consistent styling.

---
This comprehensive plan enables a systematic and automated transition, delivering a modern, high-performance Django application with a superior user experience.