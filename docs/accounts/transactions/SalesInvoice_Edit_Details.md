This comprehensive modernization plan outlines the strategic transition of your ASP.NET `SalesInvoice_Edit_Details` module to a modern Django application, leveraging AI-assisted automation, fat models, thin views, and cutting-edge frontend technologies like HTMX and Alpine.js. Our focus is on business value, reduced manual effort, and a scalable, maintainable architecture.

---

## ASP.NET to Django Conversion Script: Sales Invoice Edit Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET .aspx and C# code-behind, we've identified the primary tables involved in the "Sales Invoice - Edit" functionality, along with several lookup tables and a customer master table.

**Primary Tables:**
-   `tblACC_SalesInvoice_Master` (Main invoice header data)
-   `tblACC_SalesInvoice_Details` (Line items for each invoice)

**Lookup Tables (for dropdowns):**
-   `tblACC_Service_Category`
-   `tblExciseCommodity_Master`
-   `tblACC_TransportMode`
-   `tblACC_Removable_Nature`
-   `Unit_Master`
-   `tblExciseser_Master`
-   `tblVAT_Master`

**Related Master Data Tables:**
-   `SD_Cust_master` (Customer Master for Buyer/Consignee search)
-   `SD_Cust_PO_Master` (Purchase Order Master)
-   `SD_Cust_PO_Details` (Purchase Order Details)
-   `SD_Cust_WorkOrder_Master` (Work Order Master)
-   `Tbl_Country_Master` (Inferred from dropdownCountry functions)
-   `Tbl_State_Master` (Inferred from dropdownState functions)
-   `Tbl_City_Master` (Inferred from dropdownCity functions)

**Key Columns Identified (simplified to core fields for example):**

**`tblACC_SalesInvoice_Master` (Target Django Model: `SalesInvoice`)**
-   `Id` (PK)
-   `InvoiceNo` (VARCHAR)
-   `SysDate` (DATETIME)
-   `DateOfIssueInvoice` (DATETIME)
-   `TimeOfIssueInvoice` (VARCHAR)
-   `DateOfRemoval` (DATETIME)
-   `TimeOfRemoval` (VARCHAR)
-   `PONo` (VARCHAR)
-   `POId` (INT)
-   `WONo` (VARCHAR)
-   `InvoiceMode` (INT - FK to `tblACC_SalesInvoice_Master_Type`)
-   `CustomerCategory` (INT - FK to `tblACC_Service_Category`)
-   `NatureOfRemoval` (INT - FK to `tblACC_Removable_Nature`)
-   `Commodity` (INT - FK to `tblExciseCommodity_Master`)
-   `TariffHeading` (VARCHAR)
-   `DutyRate` (DECIMAL)
-   `ModeOfTransport` (INT - FK to `tblACC_TransportMode`)
-   `RRGCNo` (VARCHAR)
-   `VehiRegNo` (VARCHAR)
-   `Buyer_name` (VARCHAR)
-   `Buyer_add` (TEXT)
-   `Buyer_country` (INT - FK to `Tbl_Country_Master`)
-   `Buyer_state` (INT - FK to `Tbl_State_Master`)
-   `Buyer_city` (INT - FK to `Tbl_City_Master`)
-   `Buyer_cotper` (VARCHAR)
-   `Buyer_ph` (VARCHAR)
-   `Buyer_mob` (VARCHAR)
-   `Buyer_email` (VARCHAR)
-   `Buyer_fax` (VARCHAR)
-   `Buyer_ecc` (VARCHAR)
-   `Buyer_tin` (VARCHAR)
-   `Buyer_vat` (VARCHAR)
-   `Cong_name` (VARCHAR)
-   `Cong_add` (TEXT)
-   `Cong_country` (INT - FK to `Tbl_Country_Master`)
-   `Cong_state` (INT - FK to `Tbl_State_Master`)
-   `Cong_city` (INT - FK to `Tbl_City_Master`)
-   `Cong_cotper` (VARCHAR)
-   `Cong_ph` (VARCHAR)
-   `Cong_mob` (VARCHAR)
-   `Cong_email` (VARCHAR)
-   `Cong_fax` (VARCHAR)
-   `Cong_ecc` (VARCHAR)
-   `Cong_tin` (VARCHAR)
-   `Cong_vat` (VARCHAR)
-   `AddType` (INT)
-   `AddAmt` (DECIMAL)
-   `DeductionType` (INT)
-   `Deduction` (DECIMAL)
-   `PFType` (INT)
-   `PF` (DECIMAL)
-   `CENVAT` (INT - FK to `tblExciseser_Master`)
-   `SED` (DECIMAL)
-   `SEDType` (INT)
-   `AED` (DECIMAL)
-   `AEDType` (INT)
-   `VAT` (INT - FK to `tblVAT_Master`)
-   `SelectedCST` (INT)
-   `CST` (INT - FK to `tblVAT_Master` - *Note: ASP.NET uses VAT column for CST value*)
-   `FreightType` (INT)
-   `Freight` (DECIMAL)
-   `InsuranceType` (INT)
-   `Insurance` (DECIMAL)
-   `OtherAmt` (DECIMAL)
-   `CompId` (INT)
-   `FinYearId` (INT)
-   `SessionId` (VARCHAR)

**`tblACC_SalesInvoice_Details` (Target Django Model: `SalesInvoiceDetail`)**
-   `Id` (PK)
-   `MId` (INT - FK to `tblACC_SalesInvoice_Master`)
-   `ItemId` (INT - FK to `SD_Cust_PO_Details`)
-   `Unit` (INT - FK to `Unit_Master`)
-   `Qty` (DECIMAL)
-   `ReqQty` (DECIMAL)
-   `AmtInPer` (DECIMAL)
-   `Rate` (DECIMAL)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

The primary functionality of `SalesInvoice_Edit_Details.aspx` is to **Update** an existing Sales Invoice record.
-   **Read (Loading Data):** The `Page_Load` event fetches all details of a specific invoice (master and line items) and populates the various input controls and labels. This involves complex joins and lookups across many tables. The `LoadData` method specifically populates the "Goods" GridView.
-   **Update (Saving Data):** The `BtnUpdate_Click` method is the core update logic. It saves changes from all tabs (Invoice Details, Buyer, Consignee, Taxation) into `tblACC_SalesInvoice_Master` and updates selected line items in `tblACC_SalesInvoice_Details`.
-   **No explicit Create or Delete on this page:** While a "SalesInvoice_Edit.aspx" likely handles listing and perhaps creation/deletion, this specific page focuses on editing.
-   **Validation Logic:** Extensive client-side (e.g., `RequiredFieldValidator`, `RegularExpressionValidator`) and server-side validation (`fun.DateValidation`, `fun.EmailValidation`, `fun.NumberValidationQty`) is present. This will be integrated into Django Forms.
-   **Business Logic & Helper Functions:**
    -   **`fun.decrypt`:** Decrypting query string parameters. Django should handle secure IDs (e.g., UUIDs or robust ID schemes).
    -   **`fun.select`:** Generic database select function used for numerous lookups. In Django, this translates to `Model.objects.filter()` or `Model.objects.get()`.
    -   **`fun.dropdownCountry`, `fun.dropdownState`, `fun.dropdownCity`:** Cascading dropdown population. This will be handled by HTMX for dynamic updates.
    -   **`fun.ExciseCommodity`:** Logic to derive 'Tariff Head No/Exemption Notif. No.' based on commodity. This logic will be moved to the `SalesInvoice` model or a helper method.
    -   **Date/Time Formatting:** `fun.FromDateDMY`, `fun.FromDate`, `fun.getCurrDate`, `fun.getCurrTime`. Django's `datetime` objects and template filters will handle this.
    -   **`fun.getCode`:** Extracts customer ID from a display string (e.g., "Customer Name [ID]"). This helper will be recreated.
    -   **`AutoPostBack` functionality for dropdowns:** (e.g., `DrpCommodity_SelectedIndexChanged`, `DrpByCountry_SelectedIndexChanged`) which trigger server-side updates and re-renders parts of the page. This is a prime candidate for HTMX `hx-get` or `hx-post` requests.
    -   **Customer Search (`Button1_Click`, `Button4_Click`):** Populates buyer/consignee address details based on selected customer. This will be HTMX driven.
    -   **Copy from Buyer (`Button6_Click`):** Copies buyer details to consignee. Also an HTMX interaction.
    -   **Gridview Checkbox Logic (`CheckBox1_CheckedChanged`):** Toggles visibility of labels vs. textboxes/dropdowns for `ReqQty`, `AmtInPer`, `UnitOfQty` within the "Goods" grid. This requires a dynamic HTMX component that can re-render a single row.
    -   **Quantity and Amount Calculations:** The `BtnUpdate_Click` includes calculations like `TotInvQty` and `RemnAmt` for validation of `ReqQty` and `AmtInPer`. These should reside as properties or methods within `SalesInvoiceDetail` model.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The ASP.NET page is a single, multi-section form structured using an `AjaxControlToolkit:TabContainer`. Each tab (`TabPanel`) contains various input controls.

-   **`TabContainer` (`TabPanel1`, `TabPanel2`, `TabPanel3`, `TabPanel4`):** Represents distinct sections of the Sales Invoice (Buyer, Consignee, Goods, Taxation). In Django, this will be represented by an Alpine.js-controlled tab interface, with each tab potentially loading content via HTMX.
-   **`asp:Label`:** Used for displaying static text and read-only invoice header details (`LblInv`, `LblInvDate`, `LblMode`, etc.) and data within `GridView1`.
-   **`asp:TextBox`:** Standard text input fields for various data types (e.g., names, addresses, numbers, dates). These will map to Django `forms.TextInput` with appropriate `type` attributes (e.g., `type="number"`, `type="email"`, `type="date"`).
-   **`asp:DropDownList`:** Used for selecting values from lookup tables (`DrpCategory`, `DrpCommodity`, `DrpTransport`, `DrpNatureremovable`, `DrpUnitQty`, `DrpServiceTax`, `DrpVAT`, `DrpCst`, `DrpAdd`, `DrpDed`, `DrpPAF`, `DrpSED`, `DrpAED`, `DrpFreight`, `DrpInsurance`). These will map to Django `forms.Select` or `forms.ModelChoiceField`. Cascading behavior will be handled by HTMX.
-   **`cc1:CalendarExtender`:** For date input (`TxtDateofIssueInvoice`, `TxtDateRemoval`). This will be replaced by standard `type="date"` input with browser native pickers, or an Alpine.js-driven custom date picker if advanced features are needed.
-   **`MKB:TimeSelector`:** For time input (`TimeOfIssue`, `TimeOfRemove`). This will be replaced by standard `type="time"` input or an Alpine.js custom time picker.
-   **`cc1:AutoCompleteExtender`:** For customer name search (`TxtBYName`, `TxtCName`). This will be replaced by an HTMX-driven search input (e.g., loading suggestions from a Django API endpoint or using a library like `django-select2` with HTMX integration).
-   **`asp:GridView` (`GridView1`):** Displays invoice line items in the "Goods" tab. This will be replaced by a DataTables-enabled HTML `<table>`, with HTMX used for dynamic updates (e.g., when a checkbox is toggled, or the table needs to be refreshed).
-   **`asp:Button` (`BtnBuy`, `BtnUpdate`, `ButtonCancel`, etc.):** Triggers server-side postbacks. These will be replaced by standard HTML `<button>` elements with `hx-post` or `hx-get` attributes for HTMX-driven interactions (e.g., form submissions, navigation, data fetches).
-   **`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`:** Client and server-side validation. This logic will be migrated to Django Forms (using `required=True`, `RegexValidator`, and custom `clean_` methods).

---

### Step 4: Generate Django Code

We will create a Django application named `sales`. The core models will be `SalesInvoice` and `SalesInvoiceDetail`. We will also infer and create placeholder models for the necessary lookup tables and `Customer` for search functionality.

#### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**
We'll define `SalesInvoice` and `SalesInvoiceDetail` models, mapping directly to `tblACC_SalesInvoice_Master` and `tblACC_SalesInvoice_Details`. We'll also define the essential lookup models to ensure proper foreign key relationships. All models will use `managed = False` and `db_table` to connect to the existing database. Business logic related to invoice calculations and data integrity will be added to the `SalesInvoice` and `SalesInvoiceDetail` models.

**`sales/models.py`**
```python
from django.db import models
from django.utils import timezone
from decimal import Decimal

# --- Lookup Models (Simplified for example, expand as needed) ---

class SalesInvoiceMode(models.Model):
    """Maps to tblACC_SalesInvoice_Master_Type for invoice mode."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master_Type'
        verbose_name = 'Sales Invoice Mode'
        verbose_name_plural = 'Sales Invoice Modes'

    def __str__(self):
        return self.description

class ServiceCategory(models.Model):
    """Maps to tblACC_Service_Category for customer category."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_Service_Category'
        verbose_name = 'Service Category'
        verbose_name_plural = 'Service Categories'

    def __str__(self):
        return self.description

class ExciseCommodity(models.Model):
    """Maps to tblExciseCommodity_Master."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    # Assuming TariffHeading is derived from this model, add relevant field if exists
    # e.g., tariff_heading = models.CharField(db_column='TariffHeading', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblExciseCommodity_Master'
        verbose_name = 'Excise Commodity'
        verbose_name_plural = 'Excise Commodities'

    def __str__(self):
        return self.terms

    def get_tariff_heading(self):
        """
        Mimics fun.ExciseCommodity logic.
        This would query a specific table or field based on the commodity ID.
        Placeholder for actual logic.
        """
        # Example: Replace with actual database lookup or pre-defined logic
        if self.id == 1:
            return "Tariff A"
        elif self.id == 2:
            return "Tariff B"
        return "N/A"

class TransportMode(models.Model):
    """Maps to tblACC_TransportMode."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_TransportMode'
        verbose_name = 'Transport Mode'
        verbose_name_plural = 'Transport Modes'

    def __str__(self):
        return self.description

class RemovalNature(models.Model):
    """Maps to tblACC_Removable_Nature."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblACC_Removable_Nature'
        verbose_name = 'Removal Nature'
        verbose_name_plural = 'Removal Natures'

    def __str__(self):
        return self.description

class Unit(models.Model):
    """Maps to Unit_Master."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class ExciseService(models.Model):
    """Maps to tblExciseser_Master for CENVAT."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    live = models.BooleanField(db_column='Live', default=True) # Assuming 'Live' maps to a boolean

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service'
        verbose_name_plural = 'Excise Services'

    def __str__(self):
        return self.terms

class VATMaster(models.Model):
    """Maps to tblVAT_Master for VAT/CST."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Master'
        verbose_name_plural = 'VAT Masters'

    def __str__(self):
        return self.terms

class Country(models.Model):
    """Maps to Tbl_Country_Master."""
    id = models.IntegerField(db_column='CId', primary_key=True)
    name = models.CharField(db_column='CountryName', max_length=100)

    class Meta:
        managed = False
        db_table = 'Tbl_Country_Master'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.name

class State(models.Model):
    """Maps to Tbl_State_Master."""
    id = models.IntegerField(db_column='SId', primary_key=True)
    country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='CId', related_name='states')
    name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'Tbl_State_Master'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.name

class City(models.Model):
    """Maps to Tbl_City_Master."""
    id = models.IntegerField(db_column='CityId', primary_key=True)
    state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='SId', related_name='cities')
    name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'Tbl_City_Master'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.name

class Customer(models.Model):
    """Maps to SD_Cust_master for buyer/consignee search."""
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming 'Id' is auto-increment PK
    customer_id = models.CharField(db_column='CustomerId', max_length=50, unique=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    material_del_address = models.TextField(db_column='MaterialDelAddress', blank=True, null=True)
    material_del_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='MaterialDelCountry', blank=True, null=True, related_name='delivery_customers')
    material_del_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='MaterialDelState', blank=True, null=True, related_name='delivery_customers')
    material_del_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='MaterialDelCity', blank=True, null=True, related_name='delivery_customers')
    material_del_contact_no = models.CharField(db_column='MaterialDelContactNo', max_length=50, blank=True, null=True)
    material_del_fax_no = models.CharField(db_column='MaterialDelFaxNo', max_length=50, blank=True, null=True)
    contact_person = models.CharField(db_column='ContactPerson', max_length=100, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=100, blank=True, null=True)
    tin_vat_no = models.CharField(db_column='TinVatNo', max_length=50, blank=True, null=True)
    ecc_no = models.CharField(db_column='EccNo', max_length=50, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True) # Assuming this is mobile
    tin_cst_no = models.CharField(db_column='TinCstNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

    @classmethod
    def get_customer_by_code_from_display(cls, display_text):
        """
        Mimics fun.getCode(TxtBYName.Text) functionality.
        Extracts customer code from 'CustomerName [CustomerId]' format.
        """
        if '[' in display_text and ']' in display_text:
            start_index = display_text.rfind('[') + 1
            end_index = display_text.rfind(']')
            if start_index > 0 and end_index > start_index:
                customer_code = display_text[start_index:end_index]
                try:
                    return cls.objects.get(customer_id=customer_code)
                except cls.DoesNotExist:
                    pass
        return None

# --- Main Invoice Models ---

class SalesInvoice(models.Model):
    """
    Maps to tblACC_SalesInvoice_Master.
    This model holds the main invoice header information.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate', default=timezone.now)
    sys_time = models.CharField(db_column='SysTime', max_length=50, default=timezone.now().strftime('%H:%M:%S'))
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=50, unique=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # Assuming POId for relation
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Comma-separated WO IDs
    invoice_mode = models.ForeignKey(SalesInvoiceMode, on_delete=models.DO_NOTHING, db_column='InvoiceMode', blank=True, null=True)
    date_of_issue_invoice = models.DateField(db_column='DateOfIssueInvoice')
    date_of_removal = models.DateField(db_column='DateOfRemoval')
    time_of_issue_invoice = models.CharField(db_column='TimeOfIssueInvoice', max_length=50)
    time_of_removal = models.CharField(db_column='TimeOfRemoval', max_length=50)
    nature_of_removal = models.ForeignKey(RemovalNature, on_delete=models.DO_NOTHING, db_column='NatureOfRemoval', blank=True, null=True)
    commodity = models.ForeignKey(ExciseCommodity, on_delete=models.DO_NOTHING, db_column='Commodity', blank=True, null=True)
    tariff_heading = models.CharField(db_column='TariffHeading', max_length=255, blank=True, null=True)
    mode_of_transport = models.ForeignKey(TransportMode, on_delete=models.DO_NOTHING, db_column='ModeOfTransport', blank=True, null=True)
    rrgc_no = models.CharField(db_column='RRGCNo', max_length=100, blank=True, null=True)
    vehi_reg_no = models.CharField(db_column='VehiRegNo', max_length=100, blank=True, null=True)
    duty_rate = models.DecimalField(db_column='DutyRate', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    
    # Buyer Details (denormalized as per ASP.NET schema)
    # customer_code = models.CharField(db_column='CustomerCode', max_length=50, blank=True, null=True) # Might be CustomerId from SD_Cust_master
    customer_category = models.ForeignKey(ServiceCategory, on_delete=models.DO_NOTHING, db_column='CustomerCategory', blank=True, null=True)
    buyer_name = models.CharField(db_column='Buyer_name', max_length=255, blank=True, null=True)
    buyer_add = models.TextField(db_column='Buyer_add', blank=True, null=True)
    buyer_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Buyer_city', blank=True, null=True, related_name='buyer_invoices')
    buyer_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Buyer_state', blank=True, null=True, related_name='buyer_invoices')
    buyer_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Buyer_country', blank=True, null=True, related_name='buyer_invoices')
    buyer_cotper = models.CharField(db_column='Buyer_cotper', max_length=100, blank=True, null=True) # Contact Person
    buyer_ph = models.CharField(db_column='Buyer_ph', max_length=50, blank=True, null=True)
    buyer_email = models.EmailField(db_column='Buyer_email', max_length=100, blank=True, null=True)
    buyer_ecc = models.CharField(db_column='Buyer_ecc', max_length=50, blank=True, null=True)
    buyer_tin = models.CharField(db_column='Buyer_tin', max_length=50, blank=True, null=True) # TIN/CST No.
    buyer_mob = models.CharField(db_column='Buyer_mob', max_length=50, blank=True, null=True)
    buyer_fax = models.CharField(db_column='Buyer_fax', max_length=50, blank=True, null=True)
    buyer_vat = models.CharField(db_column='Buyer_vat', max_length=50, blank=True, null=True) # TIN/VAT No.

    # Consignee Details (denormalized as per ASP.NET schema)
    cong_name = models.CharField(db_column='Cong_name', max_length=255, blank=True, null=True)
    cong_add = models.TextField(db_column='Cong_add', blank=True, null=True)
    cong_city = models.ForeignKey(City, on_delete=models.DO_NOTHING, db_column='Cong_city', blank=True, null=True, related_name='consignee_invoices')
    cong_state = models.ForeignKey(State, on_delete=models.DO_NOTHING, db_column='Cong_state', blank=True, null=True, related_name='consignee_invoices')
    cong_country = models.ForeignKey(Country, on_delete=models.DO_NOTHING, db_column='Cong_country', blank=True, null=True, related_name='consignee_invoices')
    cong_cotper = models.CharField(db_column='Cong_cotper', max_length=100, blank=True, null=True) # Contact Person
    cong_ph = models.CharField(db_column='Cong_ph', max_length=50, blank=True, null=True)
    cong_email = models.EmailField(db_column='Cong_email', max_length=100, blank=True, null=True)
    cong_ecc = models.CharField(db_column='Cong_ecc', max_length=50, blank=True, null=True)
    cong_tin = models.CharField(db_column='Cong_tin', max_length=50, blank=True, null=True) # TIN/CST No.
    cong_mob = models.CharField(db_column='Cong_mob', max_length=50, blank=True, null=True)
    cong_fax = models.CharField(db_column='Cong_fax', max_length=50, blank=True, null=True)
    cong_vat = models.CharField(db_column='Cong_vat', max_length=50, blank=True, null=True) # TIN/VAT No.

    # Taxation Details
    add_type = models.IntegerField(db_column='AddType', blank=True, null=True) # 0=Amt(Rs), 1=Per(%)
    add_amt = models.DecimalField(db_column='AddAmt', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    deduction_type = models.IntegerField(db_column='DeductionType', blank=True, null=True)
    deduction = models.DecimalField(db_column='Deduction', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    pf_type = models.IntegerField(db_column='PFType', blank=True, null=True)
    pf = models.DecimalField(db_column='PF', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    cenvat = models.ForeignKey(ExciseService, on_delete=models.DO_NOTHING, db_column='CENVAT', blank=True, null=True)
    sed = models.DecimalField(db_column='SED', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    sed_type = models.IntegerField(db_column='SEDType', blank=True, null=True)
    aed = models.DecimalField(db_column='AED', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    aed_type = models.IntegerField(db_column='AEDType', blank=True, null=True)
    vat = models.ForeignKey(VATMaster, on_delete=models.DO_NOTHING, db_column='VAT', blank=True, null=True, related_name='vat_invoices') # Used for VAT
    selected_cst = models.IntegerField(db_column='SelectedCST', blank=True, null=True) # 0=With C Form, 1=Without C Form
    cst = models.ForeignKey(VATMaster, on_delete=models.DO_NOTHING, db_column='CST', blank=True, null=True, related_name='cst_invoices') # Used for CST
    freight_type = models.IntegerField(db_column='FreightType', blank=True, null=True)
    freight = models.DecimalField(db_column='Freight', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    insurance_type = models.IntegerField(db_column='InsuranceType', blank=True, null=True)
    insurance = models.DecimalField(db_column='Insurance', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))
    other_amt = models.DecimalField(db_column='OtherAmt', max_digits=18, decimal_places=3, blank=True, null=True, default=Decimal('0.000'))

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice'
        verbose_name_plural = 'Sales Invoices'

    def __str__(self):
        return self.invoice_no

    # Fat Model: Business logic methods
    def get_po_date(self):
        """
        Retrieves PO Date based on POId. Mimics sqlPODt.
        Requires a PO_Master model mapping.
        """
        # Placeholder logic: In real app, query SD_Cust_PO_Master
        return "N/A" # Or fetch from related PO object

    def get_wo_numbers(self):
        """
        Parses comma-separated WONo and retrieves actual WO numbers. Mimics LblWONo logic.
        Requires SD_Cust_WorkOrder_Master model mapping.
        """
        if not self.wo_no:
            return ""
        wo_ids = [int(x) for x in self.wo_no.split(',') if x.strip()]
        # Placeholder logic: In real app, query SD_Cust_WorkOrder_Master
        wo_numbers = []
        # for wo_id in wo_ids:
        #     try:
        #         wo = WorkOrder.objects.get(id=wo_id, comp_id=self.comp_id)
        #         wo_numbers.append(wo.wo_number)
        #     except WorkOrder.DoesNotExist:
        #         pass
        return ", ".join(wo_numbers) if wo_numbers else "N/A"

    def update_invoice_details(self, **kwargs):
        """
        Centralized method to update invoice master details.
        This would encapsulate much of the BtnUpdate_Click logic for the master table.
        """
        for field, value in kwargs.items():
            setattr(self, field, value)
        self.sys_date = timezone.now().date() # Use timezone.now().date() for date only
        self.sys_time = timezone.now().strftime('%H:%M:%S')
        # self.session_id = request.user.username # Assuming session_id maps to username
        self.save()

    def get_invoice_mode_description(self):
        """Helper to get description of invoice mode."""
        return self.invoice_mode.description if self.invoice_mode else 'N/A'

class SalesInvoiceDetail(models.Model):
    """
    Maps to tblACC_SalesInvoice_Details.
    Represents individual line items of a sales invoice.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master_invoice = models.ForeignKey(SalesInvoice, on_delete=models.CASCADE, db_column='MId', related_name='details')
    item_id = models.IntegerField(db_column='ItemId') # Placeholder, ideally FK to Item_Master or PO_Details
    unit = models.ForeignKey(Unit, on_delete=models.DO_NOTHING, db_column='Unit')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # Original Quantity
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3) # Required Quantity
    amt_in_per = models.DecimalField(db_column='AmtInPer', max_digits=18, decimal_places=3) # Amount in Percentage
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Details'
        verbose_name = 'Sales Invoice Detail'
        verbose_name_plural = 'Sales Invoice Details'

    def __str__(self):
        return f"Detail for Invoice {self.master_invoice.invoice_no}, Item {self.item_id}"

    # Fat Model: Business logic methods
    def get_item_description_and_unit(self, po_id, comp_id):
        """
        Mimics sql1 logic for ItemDesc and Unit symbol (Symbol1).
        Requires SD_Cust_PO_Details and Unit_Master models.
        """
        # Placeholder logic: Query SD_Cust_PO_Details and Unit_Master
        # Assuming SD_Cust_PO_Details has an 'item_description' field and 'unit_id' field.
        # This would require linking to SD_Cust_PO_Details model.
        # Example:
        # try:
        #     po_detail = SD_Cust_PO_Details.objects.get(id=self.item_id, po_id=po_id)
        #     unit_symbol = po_detail.unit.symbol if po_detail.unit else 'N/A'
        #     return po_detail.item_description, unit_symbol
        # except SD_Cust_PO_Details.DoesNotExist:
        #     return "Unknown Item", "N/A"
        return "Generic Item", self.unit.symbol if self.unit else "N/A"

    @property
    def remaining_qty(self):
        """
        Calculates remaining quantity. Mimics rmnqty logic in LoadData and BtnUpdate_Click.
        This represents the quantity that has *not* yet been invoiced across all details for this item.
        """
        # This calculation needs to consider *all* ReqQty for a given ItemId across all invoices
        # or specifically for items against a PO if that's the business rule.
        # The ASP.NET code suggests "TotInvQty" is sum of ReqQty for this ItemId across ALL sales invoices.
        # This is a complex cross-invoice calculation, requiring careful consideration of data model.
        # For direct translation: sum of `req_qty` for this `item_id` across *all* `SalesInvoiceDetail`
        # linked to `master_invoice.comp_id` that are not *this* detail.
        
        # Current invoice's requested quantity for this item
        current_detail_req_qty = self.req_qty

        # Sum of requested quantity for this item from other details (or all past details)
        # This needs to be precisely defined based on business rules.
        # The ASP.NET code sums all ReqQty for that ItemId. This could be problematic if not carefully scoped.
        
        # Sum of ReqQty for this ItemId from all SalesInvoiceDetails
        total_invoiced_qty_for_item = SalesInvoiceDetail.objects.filter(
            item_id=self.item_id,
            master_invoice__comp_id=self.master_invoice.comp_id
        ).exclude(id=self.id).aggregate(total=models.Sum('req_qty'))['total'] or Decimal('0.000')

        # Original quantity for this item (assuming from some master or PO detail)
        # This 'Qty' seems to be the original available quantity, possibly from PO.
        # For simplicity, using self.qty as the *original* quantity for this detail's context,
        # but the ASP.NET code's 'Qty' means the total available for that ItemId.
        original_available_qty = self.qty # This needs to come from the source of the item (e.g., PO_Details)

        return original_available_qty - total_invoiced_qty_for_item

    @property
    def remaining_amount_percentage(self):
        """
        Calculates remaining amount percentage. Mimics RemnAmt logic in BtnUpdate_Click.
        This is typically 100 - (sum of AmtInPer for this item across all related invoice details).
        """
        # Sum of AmtInPer for this ItemId from all SalesInvoiceDetails
        total_applied_amt_per = SalesInvoiceDetail.objects.filter(
            item_id=self.item_id,
            master_invoice__comp_id=self.master_invoice.comp_id
        ).exclude(id=self.id).aggregate(total=models.Sum('amt_in_per'))['total'] or Decimal('0.000')
        
        return Decimal('100.00') - total_applied_amt_per

    def update_detail(self, req_qty, unit_id, amt_in_per):
        """
        Updates a specific SalesInvoiceDetail record.
        Encapsulates parts of the GridView row update logic from BtnUpdate_Click.
        """
        # Basic validation against remaining quantity/amount.
        # More robust validation should be in form clean methods.
        
        # Check against total original quantity vs. requested quantity for this item across all invoices
        # (This is a simplified check, full logic from ASP.NET is complex and context-dependent)
        
        # if (self.remaining_qty + self.req_qty) < req_qty: # Original: (rmnqty + ReqQty) >= txtreqQty
        #     raise ValueError(f"Requested quantity {req_qty} exceeds remaining available quantity.")
        
        # if (self.remaining_amount_percentage + self.amt_in_per) < amt_in_per: # Original: Amt <= (lblAmt + RemnAmt)
        #     raise ValueError(f"Amount percentage {amt_in_per} exceeds remaining available percentage.")
        
        self.req_qty = req_qty
        self.unit_id = unit_id
        self.amt_in_per = amt_in_per
        self.save()

```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
We'll create a `ModelForm` for `SalesInvoice` to handle the primary invoice details, buyer, consignee, and taxation. Due to the complex nature of the `GridView` (Goods) section, it will be handled by a custom approach combining HTMX with either a separate formset or individual HTMX-driven row updates, rather than a single large formset for `SalesInvoiceDetail`.

**`sales/forms.py`**
```python
from django import forms
from django.core.validators import RegexValidator, EmailValidator
from django.forms.widgets import DateInput, TimeInput, Textarea
from .models import (
    SalesInvoice, SalesInvoiceMode, ServiceCategory, ExciseCommodity,
    TransportMode, RemovalNature, Unit, ExciseService, VATMaster,
    Country, State, City, Customer
)
from decimal import Decimal

# Custom date/time validation helper, mimics fun.DateValidation
def validate_date_format(value):
    date_regex = r"^\d{2}-\d{2}-\d{4}$" # dd-MM-yyyy
    if not RegexValidator(regex=date_regex)(value):
        raise forms.ValidationError("Enter date in DD-MM-YYYY format.")

# Custom number validation helper, mimics fun.NumberValidationQty
def validate_number_format(value):
    num_regex = r"^\d{1,15}(\.\d{0,3})?$" # 15 digits integer, 3 digits decimal
    if not RegexValidator(regex=num_regex)(str(value)):
        raise forms.ValidationError("Enter a valid number (up to 15 digits, 3 decimal places).")

# Custom email validation for buyer/consignee
class CustomEmailValidator(EmailValidator):
    message = "Enter a valid email address."

class SalesInvoiceForm(forms.ModelForm):
    # Overriding fields for better control and custom widgets/validation
    date_of_issue_invoice = forms.CharField(
        validators=[validate_date_format],
        widget=DateInput(attrs={'type': 'date', 'class': 'box3'}),
        label='Date Of Issue Of Invoice'
    )
    time_of_issue_invoice = forms.CharField(
        widget=TimeInput(attrs={'type': 'time', 'class': 'box3'}),
        label='Time Of Issue Of Invoice'
    )
    date_of_removal = forms.CharField(
        validators=[validate_date_format],
        widget=DateInput(attrs={'type': 'date', 'class': 'box3'}),
        label='Date Of Removal'
    )
    time_of_removal = forms.CharField(
        widget=TimeInput(attrs={'type': 'time', 'class': 'box3'}),
        label='Time Of Removal'
    )
    duty_rate = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Rate of Duty',
        required=False # ASP.NET had RequiredFieldValidator, but might be conditional
    )
    rrgc_no = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='R.R.G.C. No',
        required=False # ASP.NET had RequiredFieldValidator
    )
    vehi_reg_no = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='If by motor vehicle, its regist. number',
        required=False # ASP.NET had RequiredFieldValidator
    )
    
    # Buyer Fields
    buyer_name_display = forms.CharField(
        max_length=255, 
        required=True,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Search Buyer...'}),
        label='Name'
    )
    buyer_add = forms.CharField(
        widget=Textarea(attrs={'class': 'box3 h-28', 'cols': 50, 'rows': 5}), # Match height 110px
        label='Address',
        required=True
    )
    buyer_cotper = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Contact person',
        required=True
    )
    buyer_ph = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Phone No.',
        required=True
    )
    buyer_mob = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Mobile No.',
        required=True
    )
    buyer_email = forms.EmailField(
        validators=[CustomEmailValidator()],
        widget=forms.EmailInput(attrs={'class': 'box3'}),
        label='E-mail',
        required=True
    )
    buyer_fax = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Fax No.',
        required=False # ReqFaxNo was present, but allowing blank if the field is not always required
    )
    buyer_tin = forms.CharField( # TIN/CST No.
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='TIN / CST No.',
        required=True
    )
    buyer_vat = forms.CharField( # TIN / VAT No.
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='TIN / VAT No',
        required=True
    )
    buyer_ecc = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Customer\'s ECC.No.',
        required=True
    )

    # Consignee Fields
    cong_name_display = forms.CharField(
        max_length=255, 
        required=True,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Search Consignee...'}),
        label='Name'
    )
    cong_add = forms.CharField(
        widget=Textarea(attrs={'class': 'box3 h-28', 'cols': 50, 'rows': 5}),
        label='Address',
        required=True
    )
    cong_cotper = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Contact person',
        required=True
    )
    cong_ph = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Phone No.',
        required=True
    )
    cong_mob = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Mobile No.',
        required=True
    )
    cong_email = forms.EmailField(
        validators=[CustomEmailValidator()],
        widget=forms.EmailInput(attrs={'class': 'box3'}),
        label='E-mail',
        required=True
    )
    cong_fax = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Fax No.',
        required=False
    )
    cong_tin = forms.CharField( # TIN/CST No.
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='TIN / CST No.',
        required=True
    )
    cong_vat = forms.CharField( # TIN / VAT No.
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='TIN / VAT No.',
        required=True
    )
    cong_ecc = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Customer\'s ECC.No.',
        required=True
    )

    # Taxation Fields
    add_amt = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Add',
        required=True,
        initial=0.00
    )
    deduction = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Deduction',
        required=True,
        initial=0.00
    )
    pf = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='P&F',
        required=True,
        initial=0.00
    )
    sed = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='SED',
        required=True,
        initial=0.00
    )
    aed = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='AED',
        required=True,
        initial=0.00
    )
    freight = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Freight',
        required=True,
        initial=0.00
    )
    insurance = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Insurance',
        required=True,
        initial=0.00
    )
    other_amt = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3'}),
        label='Other Amount',
        required=True,
        initial=0.00 # Defaulted to 0 in ASP.NET
    )

    # Choice fields for type (Amount/Percentage)
    AMOUNT_PERCENTAGE_CHOICES = [(0, 'Amt(Rs)'), (1, 'Per(%)')]
    add_type = forms.ChoiceField(choices=AMOUNT_PERCENTAGE_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))
    deduction_type = forms.ChoiceField(choices=AMOUNT_PERCENTAGE_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))
    pf_type = forms.ChoiceField(choices=AMOUNT_PERCENTAGE_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))
    sed_type = forms.ChoiceField(choices=AMOUNT_PERCENTAGE_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))
    aed_type = forms.ChoiceField(choices=AMOUNT_PERCENTAGE_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))
    freight_type = forms.ChoiceField(choices=AMOUNT_PERCENTAGE_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))
    insurance_type = forms.ChoiceField(choices=AMOUNT_PERCENTAGE_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))

    # CST Type dropdown
    CST_CHOICES = [
        (0, 'C.S.T.(With C Form)'),
        (1, 'C.S.T.(Without C Form)')
    ]
    selected_cst = forms.ChoiceField(choices=CST_CHOICES, widget=forms.Select(attrs={'class': 'box3'}))


    class Meta:
        model = SalesInvoice
        fields = [
            'invoice_mode', 'customer_category', 'nature_of_removal', 'commodity',
            'tariff_heading', 'duty_rate', 'mode_of_transport', 'rrgc_no', 'vehi_reg_no',
            'date_of_issue_invoice', 'time_of_issue_invoice', 'date_of_removal', 'time_of_removal',
            # Buyer Fields
            'buyer_name', 'buyer_add', 'buyer_country', 'buyer_state', 'buyer_city',
            'buyer_cotper', 'buyer_ph', 'buyer_mob', 'buyer_email', 'buyer_fax',
            'buyer_tin', 'buyer_vat', 'buyer_ecc',
            # Consignee Fields
            'cong_name', 'cong_add', 'cong_country', 'cong_state', 'cong_city',
            'cong_cotper', 'cong_ph', 'cong_mob', 'cong_email', 'cong_fax',
            'cong_tin', 'cong_vat', 'cong_ecc',
            # Taxation Fields
            'add_type', 'add_amt', 'deduction_type', 'deduction', 'pf_type', 'pf',
            'cenvat', 'sed_type', 'sed', 'aed_type', 'aed', 'freight_type', 'freight',
            'insurance_type', 'insurance', 'other_amt', 'vat', 'selected_cst', 'cst'
        ]
        # Remove fields from Meta.fields that are handled separately or by buyer/cong_name_display
        exclude = ['sys_date', 'sys_time', 'comp_id', 'fin_year_id', 'session_id', 'invoice_no', 'po_no', 'po_id', 'wo_no']
        
        widgets = {
            'invoice_mode': forms.Select(attrs={'class': 'box3'}),
            'customer_category': forms.Select(attrs={'class': 'box3'}),
            'nature_of_removal': forms.Select(attrs={'class': 'box3'}),
            'commodity': forms.Select(attrs={'class': 'box3', 'hx-get': '/sales/invoice/commodity-tariff/', 'hx-trigger': 'change', 'hx-target': '#tariff_heading_label', 'hx-swap': 'innerHTML'}),
            'mode_of_transport': forms.Select(attrs={'class': 'box3'}),
            'buyer_country': forms.Select(attrs={'class': 'box3', 'hx-get': '/sales/invoice/get-states/buyer/', 'hx-trigger': 'change', 'hx-target': '#buyer_state_select', 'hx-swap': 'innerHTML'}),
            'buyer_state': forms.Select(attrs={'class': 'box3', 'hx-get': '/sales/invoice/get-cities/buyer/', 'hx-trigger': 'change', 'hx-target': '#buyer_city_select', 'hx-swap': 'innerHTML', 'id': 'buyer_state_select'}),
            'buyer_city': forms.Select(attrs={'class': 'box3', 'id': 'buyer_city_select'}),
            'cong_country': forms.Select(attrs={'class': 'box3', 'hx-get': '/sales/invoice/get-states/consignee/', 'hx-trigger': 'change', 'hx-target': '#consignee_state_select', 'hx-swap': 'innerHTML'}),
            'cong_state': forms.Select(attrs={'class': 'box3', 'hx-get': '/sales/invoice/get-cities/consignee/', 'hx-trigger': 'change', 'hx-target': '#consignee_city_select', 'hx-swap': 'innerHTML', 'id': 'consignee_state_select'}),
            'cong_city': forms.Select(attrs={'class': 'box3', 'id': 'consignee_city_select'}),
            'cenvat': forms.Select(attrs={'class': 'box3'}),
            'vat': forms.Select(attrs={'class': 'box3'}),
            'cst': forms.Select(attrs={'class': 'box3'})
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values for buyer_name_display and cong_name_display
        if self.instance and self.instance.pk:
            self.fields['buyer_name_display'].initial = self.instance.buyer_name
            self.fields['cong_name_display'].initial = self.instance.cong_name
            
        # Populate dropdown choices from models
        self.fields['invoice_mode'].queryset = SalesInvoiceMode.objects.all().order_by('id')
        self.fields['customer_category'].queryset = ServiceCategory.objects.all().order_by('id')
        self.fields['commodity'].queryset = ExciseCommodity.objects.all().order_by('id')
        self.fields['mode_of_transport'].queryset = TransportMode.objects.all().order_by('id')
        self.fields['nature_of_removal'].queryset = RemovalNature.objects.all().order_by('id')
        self.fields['cenvat'].queryset = ExciseService.objects.filter(live=True).order_by('-id') # Order by desc
        self.fields['vat'].queryset = VATMaster.objects.all().order_by('-id') # Order by desc
        self.fields['cst'].queryset = VATMaster.objects.all().order_by('-id') # Used for CST, same source as VAT

        # Dynamically set country/state/city initial values and querysets based on instance data
        self.fields['buyer_country'].queryset = Country.objects.all()
        self.fields['cong_country'].queryset = Country.objects.all()

        if self.instance and self.instance.buyer_country:
            self.fields['buyer_state'].queryset = State.objects.filter(country=self.instance.buyer_country)
        else:
            self.fields['buyer_state'].queryset = State.objects.none()

        if self.instance and self.instance.buyer_state:
            self.fields['buyer_city'].queryset = City.objects.filter(state=self.instance.buyer_state)
        else:
            self.fields['buyer_city'].queryset = City.objects.none()

        if self.instance and self.instance.cong_country:
            self.fields['cong_state'].queryset = State.objects.filter(country=self.instance.cong_country)
        else:
            self.fields['cong_state'].queryset = State.objects.none()
        
        if self.instance and self.instance.cong_state:
            self.fields['cong_city'].queryset = City.objects.filter(state=self.instance.cong_state)
        else:
            self.fields['cong_city'].queryset = City.objects.none()

        # Update initial values for specific dropdowns if instance exists
        if self.instance.pk:
            if self.instance.vat_id != 0: # ASP.NET uses VAT column for VAT value if not 0
                 self.fields['vat'].initial = self.instance.vat_id
            else: # If VAT is 0, ASP.NET uses CST column for CST value
                self.fields['cst'].initial = self.instance.cst_id
            
            # Set tariff_heading on init based on commodity
            if self.instance.commodity:
                self.fields['tariff_heading'].initial = self.instance.commodity.get_tariff_heading()
            else:
                self.fields['tariff_heading'].initial = "N/A" # Default if no commodity selected


    def clean(self):
        cleaned_data = super().clean()
        
        # Example of combined validation (date, email, number) as per ASP.NET logic
        # Date validations are handled by custom validators on fields
        # Email validations are handled by CustomEmailValidator on fields
        # Number validations are handled by custom validators on fields

        # Additional cross-field validation if needed
        # e.g., if self.cleaned_data['invoice_mode'] == '2' (VAT mode) then self.cleaned_data['vat'] must be selected
        
        return cleaned_data

class SalesInvoiceDetailForm(forms.ModelForm):
    # Fields that become editable in the grid
    req_qty = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3 w-full'}),
        label='Req Qty'
    )
    amt_in_per = forms.DecimalField(
        validators=[validate_number_format],
        widget=forms.TextInput(attrs={'class': 'box3 w-full'}),
        label='Amt In Per'
    )
    # Unit needs to be a dropdown
    unit_qty = forms.ModelChoiceField(
        queryset=Unit.objects.all().order_by('id'),
        to_field_name='id', # Map to Id column
        widget=forms.Select(attrs={'class': 'box3 w-full'}),
        label='Unit Of Qty'
    )

    class Meta:
        model = SalesInvoiceDetail
        fields = ['req_qty', 'amt_in_per', 'unit'] # 'unit' is the FK to Unit_Master
        # Exclude 'id', 'master_invoice', 'item_id', 'qty', 'rate' as they are labels or handled by parent form
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['unit_qty'].initial = self.instance.unit_id if self.instance else None
        # ASP.NET code refers to 'Symbol1' for the unit label, which is 'Unit_Master.Symbol' from PO_Details.
        # This can be handled in template by accessing obj.unit.symbol.
        
    def clean(self):
        cleaned_data = super().clean()
        # Complex validation logic from BtnUpdate_Click for ReqQty and AmtInPer goes here
        # E.g., checking against remaining_qty and remaining_amount_percentage properties of the model instance
        
        # This assumes 'self.instance' is available for the current row being validated
        if self.instance and self.instance.pk:
            new_req_qty = cleaned_data.get('req_qty')
            new_amt_in_per = cleaned_data.get('amt_in_per')

            # Original ASP.NET validation: (rmnqty + ReqQty) >= txtreqQty AND Amt <= (lblAmt + RemnAmt)
            # rmnqty: remaining quantity across all invoices for this item
            # ReqQty: current row's original requested quantity
            # txtreqQty: current row's NEW requested quantity
            
            # This requires careful re-evaluation of 'remaining_qty' and 'remaining_amount_percentage'
            # to reflect the exact ASP.NET logic (which seems to sum across *all* instances of ItemId).
            
            # Simplified check:
            if new_req_qty is not None and new_req_qty < 0:
                self.add_error('req_qty', 'Quantity cannot be negative.')
            if new_amt_in_per is not None and (new_amt_in_per < 0 or new_amt_in_per > 100):
                self.add_error('amt_in_per', 'Amount percentage must be between 0 and 100.')

        return cleaned_data
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
We'll primarily focus on the `SalesInvoiceUpdateView` as the ASP.NET page is for editing. We'll add helper views for HTMX interactions like cascading dropdowns, customer search/copy, and dynamic grid row editing. All views will be kept thin, delegating business logic to models.

**`sales/views.py`**
```python
from django.views.generic import ListView, UpdateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.template.loader import render_to_string
from django.db import transaction # For atomic updates

from .models import (
    SalesInvoice, SalesInvoiceDetail, SalesInvoiceMode, ServiceCategory, ExciseCommodity,
    TransportMode, RemovalNature, Unit, ExciseService, VATMaster,
    Country, State, City, Customer
)
from .forms import SalesInvoiceForm, SalesInvoiceDetailForm
import json
from datetime import datetime

# Helper function to get the current company/financial year ID (mimics Session["compid"])
# In a real Django app, this would come from request.user or middleware.
def get_user_context(request):
    # Placeholder for actual context retrieval (e.g., from user session, middleware)
    return {'comp_id': 1, 'fin_year_id': 1} # Dummy values

class SalesInvoiceListView(ListView):
    """
    Placeholder for listing all sales invoices.
    The ASP.NET code was for editing a specific invoice, not listing.
    """
    model = SalesInvoice
    template_name = 'sales/salesinvoice/list.html'
    context_object_name = 'sales_invoices'

class SalesInvoiceUpdateView(UpdateView):
    """
    Handles updating the SalesInvoice master record.
    This replaces the core functionality of SalesInvoice_Edit_Details.aspx.cs BtnUpdate_Click.
    It will handle the main form submission (excluding the Goods grid which is separate HTMX).
    """
    model = SalesInvoice
    form_class = SalesInvoiceForm
    template_name = 'sales/salesinvoice/edit_form.html' # Main form with tabs
    success_url = reverse_lazy('salesinvoice_list') # Redirect after successful update

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass necessary lookup data to the template for initial rendering
        context['invoice_modes'] = SalesInvoiceMode.objects.all().order_by('id')
        context['service_categories'] = ServiceCategory.objects.all().order_by('id')
        context['excise_commodities'] = ExciseCommodity.objects.all().order_by('id')
        context['transport_modes'] = TransportMode.objects.all().order_by('id')
        context['removal_natures'] = RemovalNature.objects.all().order_by('id')
        context['excise_services'] = ExciseService.objects.filter(live=True).order_by('-id')
        context['vat_masters'] = VATMaster.objects.all().order_by('-id')
        context['countries'] = Country.objects.all()
        
        # Initial data for the "Goods" tab
        context['sales_invoice_details'] = self.object.details.all()
        context['units'] = Unit.objects.all()

        # Determine if VAT or CST is active based on InvoiceMode, mimicking ASP.NET logic
        if self.object.invoice_mode and self.object.invoice_mode.id == 2: # Assuming 2 is VAT
            context['is_vat_mode'] = True
        elif self.object.invoice_mode and self.object.invoice_mode.id == 3: # Assuming 3 is CST
            context['is_cst_mode'] = True
        else:
            context['is_vat_mode'] = False
            context['is_cst_mode'] = False

        # Pre-populate dynamic labels if necessary, for initial load (e.g. tariff_heading)
        if self.object.commodity:
            context['initial_tariff_heading'] = self.object.commodity.get_tariff_heading()
        else:
            context['initial_tariff_heading'] = "N/A"
            
        context['invoice_mode_description'] = self.object.get_invoice_mode_description()
        context['po_date'] = self.object.get_po_date()
        context['wo_numbers'] = self.object.get_wo_numbers()

        return context

    def get_initial(self):
        initial = super().get_initial()
        # Format dates/times from model to form's expected string format (YYYY-MM-DD, HH:MM)
        if self.object.date_of_issue_invoice:
            initial['date_of_issue_invoice'] = self.object.date_of_issue_invoice.strftime('%Y-%m-%d')
        if self.object.date_of_removal:
            initial['date_of_removal'] = self.object.date_of_removal.strftime('%Y-%m-%d')
        
        # Populate buyer/consignee display names for autocomplete
        initial['buyer_name_display'] = self.object.buyer_name
        initial['cong_name_display'] = self.object.cong_name
        
        return initial

    def form_valid(self, form):
        user_context = get_user_context(self.request)
        invoice = form.save(commit=False)
        invoice.sys_date = datetime.now().date()
        invoice.sys_time = datetime.now().strftime('%H:%M:%S')
        invoice.comp_id = user_context['comp_id']
        invoice.session_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous'
        
        # Special handling for VAT/CST based on InvoiceMode, mimicking ASP.NET
        # If lblmodeid.Text == "2" (VAT) then VAT column is used.
        # If lblmodeid.Text == "3" (CST) then CST column is used.
        # In Django, both are FKs, so need to set one to None if the other is used based on mode.
        if invoice.invoice_mode and invoice.invoice_mode.id == 2: # VAT
            invoice.cst = None
        elif invoice.invoice_mode and invoice.invoice_mode.id == 3: # CST
            invoice.vat = None
        else: # Default or other modes
            invoice.vat = None
            invoice.cst = None

        invoice.save()
        messages.success(self.request, f'Sales Invoice {invoice.invoice_no} updated successfully.')

        if self.request.headers.get('HX-Request'):
            # If HTMX request, respond with a trigger to refresh relevant parts of the UI
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': json.dumps({
                        'refreshSalesInvoiceHeader': None, # Trigger header refresh
                        'showMessage': messages.get_messages(self.request)._loaded_messages[0].message
                    })
                }
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            # If HTMX request, re-render the form with errors
            # Ensure the form's template is a partial
            return render(self.request, self.template_name, self.get_context_data(form=form))
        return super().form_invalid(form)


# --- HTMX specific views for dynamic content ---

class SalesInvoiceTablePartialView(ListView):
    """
    Renders only the Sales Invoice Details table (Goods tab content) for HTMX requests.
    This replaces the GridView1 loading logic.
    """
    model = SalesInvoiceDetail
    template_name = 'sales/salesinvoice/_salesinvoicedetail_table.html'
    context_object_name = 'sales_invoice_details'

    def get_queryset(self):
        invoice_id = self.kwargs.get('pk') # Assuming PK of SalesInvoice
        return SalesInvoiceDetail.objects.filter(master_invoice_id=invoice_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['units'] = Unit.objects.all() # Needed for DrpUnitQty
        return context

class SalesInvoiceDetailRowToggleView(View):
    """
    Handles toggling a Sales Invoice Detail row between display and edit mode via HTMX.
    Mimics CheckBox1_CheckedChanged in ASP.NET GridView.
    """
    def post(self, request, pk):
        detail = get_object_or_404(SalesInvoiceDetail, pk=pk)
        is_checked = request.POST.get('is_checked') == 'true'

        context = {'detail': detail, 'is_checked': is_checked}
        context['units'] = Unit.objects.all() # For dropdown in edit mode

        # Render the specific row with its updated state
        html = render_to_string('sales/salesinvoice/_salesinvoicedetail_row.html', context, request=request)
        return HttpResponse(html)

class SalesInvoiceDetailUpdateView(View):
    """
    Handles updating a single Sales Invoice Detail row via HTMX.
    Part of BtnUpdate_Click logic that iterates GridView rows.
    """
    def post(self, request, pk):
        detail = get_object_or_404(SalesInvoiceDetail, pk=pk)
        form = SalesInvoiceDetailForm(request.POST, instance=detail)
        
        if form.is_valid():
            try:
                # Assuming the form's clean method handles the complex validation
                # from BtnUpdate_Click (remaining_qty, remaining_amount_percentage)
                form.save() # This will update the 'unit' field based on 'unit_qty' from form
                messages.success(request, f'Item {detail.item_id} updated successfully.')
                # Re-render the row in display mode after successful update
                html = render_to_string('sales/salesinvoice/_salesinvoicedetail_row.html', {'detail': detail, 'is_checked': False, 'units': Unit.objects.all()}, request=request)
                return HttpResponse(html) # Return the updated row HTML
            except ValueError as e:
                form.add_error(None, str(e)) # Add model-level validation errors to form
        
        # If form is invalid or exception, re-render the row in edit mode with errors
        html = render_to_string('sales/salesinvoice/_salesinvoicedetail_row.html', {'detail': detail, 'form': form, 'is_checked': True, 'units': Unit.objects.all()}, request=request)
        return HttpResponse(html)

class CustomerSearchAutoComplete(View):
    """
    Provides customer name suggestions for AutoCompleteExtender.
    Mimics sql web method.
    """
    def get(self, request):
        query = request.GET.get('term', '')
        comp_id = get_user_context(request)['comp_id'] # Get CompId from context
        
        customers = Customer.objects.filter(
            customer_name__icontains=query,
            comp_id=comp_id
        ).values('customer_name', 'customer_id')[:10] # Limit suggestions

        results = [f"{c['customer_name']} [{c['customer_id']}]" for c in customers]
        return JsonResponse(results, safe=False) # Return as JSON array

class CustomerDetailsFetchView(View):
    """
    Fetches customer details based on name/code and populates form fields.
    Mimics Button1_Click (Buyer Search) and Button4_Click (Consignee Search).
    """
    def post(self, request, customer_type): # customer_type can be 'buyer' or 'consignee'
        customer_display_name = request.POST.get(f'{customer_type}_name_display', '').strip()
        comp_id = get_user_context(request)['comp_id']

        customer = Customer.get_customer_by_code_from_display(customer_display_name)
        
        if customer and customer.comp_id == comp_id:
            # Construct a dictionary of fields to be updated in the form
            data_to_update = {
                'address': customer.material_del_address,
                'country_id': customer.material_del_country_id,
                'state_id': customer.material_del_state_id,
                'city_id': customer.material_del_city_id,
                'contact_person': customer.contact_person,
                'phone': customer.material_del_contact_no,
                'mobile': customer.contact_no, # Assuming contact_no is mobile
                'email': customer.email,
                'fax': customer.material_del_fax_no,
                'tin_cst_no': customer.tin_cst_no,
                'tin_vat_no': customer.tin_vat_no,
                'ecc_no': customer.ecc_no,
            }
            # Return JSON that Alpine.js can use to update form fields
            return JsonResponse({'success': True, 'data': data_to_update})
        else:
            return JsonResponse({'success': False, 'message': 'Invalid selection of Customer data.'})

class CascadingDropdownView(View):
    """
    Provides states/cities for cascading dropdowns.
    Mimics DrpByCountry_SelectedIndexChanged, DrpByState_SelectedIndexChanged etc.
    """
    def get(self, request, type, parent_id):
        options = []
        if type == 'states':
            states = State.objects.filter(country_id=parent_id).order_by('name')
            options = [{'id': state.id, 'name': state.name} for state in states]
        elif type == 'cities':
            cities = City.objects.filter(state_id=parent_id).order_by('name')
            options = [{'id': city.id, 'name': city.name} for city in cities]
        
        # Render dropdown options as HTML partial
        html = render_to_string(
            'sales/salesinvoice/_dropdown_options.html',
            {'options': options, 'empty_label': 'Select one' if options else 'No options available'}
        )
        return HttpResponse(html)

class CommodityTariffView(View):
    """
    Returns tariff heading based on selected commodity.
    Mimics DrpCommodity_SelectedIndexChanged.
    """
    def get(self, request):
        commodity_id = request.GET.get('commodity_id')
        tariff_heading = "N/A"
        if commodity_id:
            try:
                commodity = ExciseCommodity.objects.get(id=commodity_id)
                tariff_heading = commodity.get_tariff_heading()
            except ExciseCommodity.DoesNotExist:
                pass
        return HttpResponse(tariff_heading) # HTMX will swap this directly into lbltrafficNo

```

#### 4.4 Templates

**Task:** Create templates for each view, focusing on HTMX and Alpine.js.

**Instructions:**
We'll create the main edit form template with Alpine.js for tab management, and separate partials for the Goods table, individual Goods rows, and cascading dropdown options, all driven by HTMX.

**`sales/templates/sales/salesinvoice/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Sales Invoices</h2>
        <!-- Assuming you would have a create button on a list page -->
        <a href="{% url 'salesinvoice_edit' pk=1 %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Edit Sales Invoice 1 (Demo)
        </a>
    </div>
    
    <div id="salesInvoiceTable-container"
         hx-trigger="load, refreshSalesInvoiceList from:body"
         hx-get="{% url 'salesinvoice_list_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Sales Invoices...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // This example list.html uses a placeholder for now as the original ASP.NET was an edit page.
    // A true list page would display a DataTables of invoices.
    $(document).ready(function() {
        // DataTables initialization for the list if needed
        // $('#salesInvoiceListTable').DataTable({});
    });
</script>
{% endblock %}
```

**`sales/templates/sales/salesinvoice/_salesinvoice_header_info.html`**
```html
<table align="left" cellpadding="0" cellspacing="0" class="style2" width="100%">
    <tr>
        <td style="background:url(/static/images/hdbg.JPG); height:21px" 
            class="fontcsswhite" colspan="3" >&nbsp;<b>Sales Invoice - Edit</b></td>
    </tr>
    <tr id="invoice_main_info">
        <td width="25%" height="24">
            &nbsp;Invoice No.&nbsp;:&nbsp;<span class="font-bold">{{ salesinvoice.invoice_no }}</span>
        </td>
        <td>
                        Date :
                        <span class="font-bold">{{ salesinvoice.sys_date|date:"d-m-Y" }}</span>
        </td>
        <td>
                        Mode of Invoice :
                        <span class="font-bold">{{ salesinvoice.get_invoice_mode_description }}</span>
                        <span class="hidden" id="lblmodeid">{{ salesinvoice.invoice_mode.id }}</span>
        </td>
    </tr>
    <tr>
        <td width="25%" height="24">
            &nbsp;PO No.:&nbsp;<span class="font-bold">{{ salesinvoice.po_no|default:"N/A" }}</span>
        </td>
        <td>
                        Date :
                        <span class="font-bold">{{ salesinvoice.get_po_date }}</span>
        </td>
        <td>
                        WO No. :
                        <span class="font-bold">{{ salesinvoice.get_wo_numbers|default:"N/A" }}</span>
        </td>
    </tr>
</table>
```

**`sales/templates/sales/salesinvoice/edit_form.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div id="sales-invoice-container" hx-ext="json-enc" x-data="{
        activeTab: parseInt(sessionStorage.getItem('salesInvoiceActiveTab') || '0'),
        setActiveTab(index) {
            this.activeTab = index;
            sessionStorage.setItem('salesInvoiceActiveTab', index);
        },
        showMessage: (message) => {
            alert(message); // Simple alert for messages
        },
        populateCustomerFields(data, type) {
            if (data.success) {
                // Update form fields dynamically based on customer_type ('buyer' or 'consignee')
                const prefix = type === 'buyer' ? 'buyer_' : 'cong_';
                Object.keys(data.data).forEach(key => {
                    let fieldName = key.replace(/_id$/, ''); // Remove _id suffix for field name
                    if (key.endsWith('_id')) { // Handle dropdowns
                        const selectElement = document.getElementById(prefix + fieldName + '_select');
                        if (selectElement) {
                            selectElement.value = data.data[key];
                            // Trigger HTMX change on dropdowns to load dependent states/cities
                            htmx.trigger(selectElement, 'change');
                        }
                    } else { // Handle text inputs
                        const inputElement = document.querySelector(`[name="${prefix}${fieldName}"]`);
                        if (inputElement) {
                            inputElement.value = data.data[key];
                        }
                    }
                });
            } else {
                alert(data.message);
            }
        }
    }"
    @refreshSalesInvoiceHeader.window="
        htmx.ajax('GET', '{% url 'salesinvoice_header_partial' salesinvoice.pk %}', {target: '#invoice_main_info', swap: 'outerHTML'})
    "
    @showMessage.window="showMessage($event.detail)"
    >
        <form hx-post="{% url 'salesinvoice_edit' pk=salesinvoice.pk %}" hx-swap="none" hx-trigger="submit">
            {% csrf_token %}

            <div id="invoice_main_info_container" hx-trigger="load" hx-get="{% url 'salesinvoice_header_partial' salesinvoice.pk %}" hx-swap="innerHTML">
                <!-- Header Info (Invoice No, Date, PO No etc.) will be loaded here -->
            </div>

            <div class="mt-6">
                <div class="flex border-b border-gray-200" role="tablist">
                    <button type="button" :class="{'bg-blue-500 text-white': activeTab === 0, 'bg-gray-200 text-gray-700': activeTab !== 0}"
                        @click="setActiveTab(0)" class="px-4 py-2 rounded-t-lg font-medium text-sm focus:outline-none" role="tab">Invoice Details</button>
                    <button type="button" :class="{'bg-blue-500 text-white': activeTab === 1, 'bg-gray-200 text-gray-700': activeTab !== 1}"
                        @click="setActiveTab(1)" class="ml-2 px-4 py-2 rounded-t-lg font-medium text-sm focus:outline-none" role="tab">Buyer</button>
                    <button type="button" :class="{'bg-blue-500 text-white': activeTab === 2, 'bg-gray-200 text-gray-700': activeTab !== 2}"
                        @click="setActiveTab(2)" class="ml-2 px-4 py-2 rounded-t-lg font-medium text-sm focus:outline-none" role="tab">Consignee</button>
                    <button type="button" :class="{'bg-blue-500 text-white': activeTab === 3, 'bg-gray-200 text-gray-700': activeTab !== 3}"
                        @click="setActiveTab(3)" class="ml-2 px-4 py-2 rounded-t-lg font-medium text-sm focus:outline-none" role="tab">Goods</button>
                    <button type="button" :class="{'bg-blue-500 text-white': activeTab === 4, 'bg-gray-200 text-gray-700': activeTab !== 4}"
                        @click="setActiveTab(4)" class="ml-2 px-4 py-2 rounded-t-lg font-medium text-sm focus:outline-none" role="tab">Taxation</button>
                </div>

                <div class="border border-gray-200 p-6 rounded-b-lg shadow-sm bg-white">
                    <!-- Tab 0: Invoice Details -->
                    <div x-show="activeTab === 0" role="tabpanel" class="space-y-4">
                        <h3 class="text-xl font-semibold mb-4">Invoice Details</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="{{ form.customer_category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category</label>
                                {{ form.customer_category }}
                                {% if form.customer_category.errors %}<p class="text-red-500 text-xs mt-1">{{ form.customer_category.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.commodity.id_for_label }}" class="block text-sm font-medium text-gray-700">Excisable Commodity</label>
                                {{ form.commodity }}
                                {% if form.commodity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.commodity.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700">Tariff Head No/Exemption Notif. No.</label>
                                <span id="tariff_heading_label" class="box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50">{{ form.tariff_heading.initial }}</span>
                                {% comment %} Hidden field to ensure data is submitted {% endcomment %}
                                <input type="hidden" name="{{ form.tariff_heading.name }}" value="{{ form.tariff_heading.initial }}" id="{{ form.tariff_heading.id_for_label }}">
                            </div>
                            <div>
                                <label for="{{ form.duty_rate.id_for_label }}" class="block text-sm font-medium text-gray-700">Rate of Duty</label>
                                {{ form.duty_rate }}
                                {% if form.duty_rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.duty_rate.errors }}</p>{% endif %}
                                <span class="text-gray-500 text-xs mt-1">[Tariff Rate/Notifi No. & Date]</span>
                            </div>
                            <div>
                                <label for="{{ form.mode_of_transport.id_for_label }}" class="block text-sm font-medium text-gray-700">Mode of Transport</label>
                                {{ form.mode_of_transport }}
                                {% if form.mode_of_transport.errors %}<p class="text-red-500 text-xs mt-1">{{ form.mode_of_transport.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.rrgc_no.id_for_label }}" class="block text-sm font-medium text-gray-700">R.R.G.C. No</label>
                                {{ form.rrgc_no }}
                                {% if form.rrgc_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rrgc_no.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.vehi_reg_no.id_for_label }}" class="block text-sm font-medium text-gray-700">If by motor vehicle, its regist. number</label>
                                {{ form.vehi_reg_no }}
                                {% if form.vehi_reg_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vehi_reg_no.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.nature_of_removal.id_for_label }}" class="block text-sm font-medium text-gray-700">Nature Of Removal</label>
                                {{ form.nature_of_removal }}
                                {% if form.nature_of_removal.errors %}<p class="text-red-500 text-xs mt-1">{{ form.nature_of_removal.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.date_of_issue_invoice.id_for_label }}" class="block text-sm font-medium text-gray-700">Date Of Issue Of Invoice</label>
                                {{ form.date_of_issue_invoice }}
                                {% if form.date_of_issue_invoice.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_of_issue_invoice.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.time_of_issue_invoice.id_for_label }}" class="block text-sm font-medium text-gray-700">Time Of Issue Of Invoice</label>
                                {{ form.time_of_issue_invoice }}
                                {% if form.time_of_issue_invoice.errors %}<p class="text-red-500 text-xs mt-1">{{ form.time_of_issue_invoice.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.date_of_removal.id_for_label }}" class="block text-sm font-medium text-gray-700">Date Of Removal</label>
                                {{ form.date_of_removal }}
                                {% if form.date_of_removal.errors %}<p class="text-red-500 text-xs mt-1">{{ form.date_of_removal.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.time_of_removal.id_for_label }}" class="block text-sm font-medium text-gray-700">Time Of Removal</label>
                                {{ form.time_of_removal }}
                                {% if form.time_of_removal.errors %}<p class="text-red-500 text-xs mt-1">{{ form.time_of_removal.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end">
                            <button type="button" @click="setActiveTab(1)" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                        </div>
                    </div>

                    <!-- Tab 1: Buyer -->
                    <div x-show="activeTab === 1" role="tabpanel" class="space-y-4">
                        <h3 class="text-xl font-semibold mb-4">Buyer Details</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="{{ form.buyer_name_display.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                                {{ form.buyer_name_display }}
                                {% if form.buyer_name_display.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_name_display.errors }}</p>{% endif %}
                                <button type="button" class="ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                                    hx-post="{% url 'customer_details_fetch' customer_type='buyer' %}"
                                    hx-vals='{"buyer_name_display": document.getElementById("{{ form.buyer_name_display.id_for_label }}").value}'
                                    hx-target="this" hx-swap="none"
                                    _="on htmx:afterRequest call populateCustomerFields(JSON.parse(event.detail.xhr.responseText), 'buyer')"
                                >Search</button>
                            </div>
                            <div>
                                <label for="{{ form.buyer_add.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                                {{ form.buyer_add }}
                                {% if form.buyer_add.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_add.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                                {{ form.buyer_country }}
                                {% if form.buyer_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_country.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="buyer_state_select" class="block text-sm font-medium text-gray-700">State</label>
                                <select id="buyer_state_select" name="{{ form.buyer_state.name }}" class="box3" 
                                    hx-get="{% url 'cascading_dropdown' type='cities' parent_id='HX_VAL' %}"
                                    hx-trigger="change"
                                    hx-target="#buyer_city_select"
                                    hx-swap="innerHTML">
                                    {% if form.buyer_state.value %}
                                        <option value="{{ form.buyer_state.value }}" selected>{{ form.buyer_state.field.queryset.get(id=form.buyer_state.value) }}</option>
                                    {% else %}
                                        <option value="">Select a state</option>
                                    {% endif %}
                                    {% for option in form.buyer_state.field.queryset %}
                                        <option value="{{ option.id }}" {% if option.id == form.buyer_state.value %}selected{% endif %}>{{ option.name }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.buyer_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_state.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="buyer_city_select" class="block text-sm font-medium text-gray-700">City</label>
                                <select id="buyer_city_select" name="{{ form.buyer_city.name }}" class="box3">
                                    {% if form.buyer_city.value %}
                                        <option value="{{ form.buyer_city.value }}" selected>{{ form.buyer_city.field.queryset.get(id=form.buyer_city.value) }}</option>
                                    {% else %}
                                        <option value="">Select a city</option>
                                    {% endif %}
                                    {% for option in form.buyer_city.field.queryset %}
                                        <option value="{{ option.id }}" {% if option.id == form.buyer_city.value %}selected{% endif %}>{{ option.name }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.buyer_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_city.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_cotper.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact person</label>
                                {{ form.buyer_cotper }}
                                {% if form.buyer_cotper.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_cotper.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_ph.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone No.</label>
                                {{ form.buyer_ph }}
                                {% if form.buyer_ph.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_ph.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_mob.id_for_label }}" class="block text-sm font-medium text-gray-700">Mobile No.</label>
                                {{ form.buyer_mob }}
                                {% if form.buyer_mob.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_mob.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail</label>
                                {{ form.buyer_email }}
                                {% if form.buyer_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_email.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No.</label>
                                {{ form.buyer_fax }}
                                {% if form.buyer_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_fax.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_tin.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / CST No.</label>
                                {{ form.buyer_tin }}
                                {% if form.buyer_tin.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_tin.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / VAT No</label>
                                {{ form.buyer_vat }}
                                {% if form.buyer_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_vat.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.buyer_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer's ECC.No.</label>
                                {{ form.buyer_ecc }}
                                {% if form.buyer_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.buyer_ecc.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="button" @click="setActiveTab(2)" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                            <a href="{% url 'salesinvoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
                        </div>
                    </div>

                    <!-- Tab 2: Consignee -->
                    <div x-show="activeTab === 2" role="tabpanel" class="space-y-4">
                        <h3 class="text-xl font-semibold mb-4">Consignee Details</h3>
                        <div class="flex justify-end mb-4">
                            <button type="button" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded"
                                @click="
                                    // Copy all buyer fields to consignee fields
                                    $refs.congNameDisplay.value = $refs.buyerNameDisplay.value;
                                    $refs.congAdd.value = $refs.buyerAdd.value;
                                    $refs.congCountry.value = $refs.buyerCountry.value; htmx.trigger($refs.congCountry, 'change');
                                    $refs.congState.value = $refs.buyerState.value; htmx.trigger($refs.congState, 'change');
                                    $refs.congCity.value = $refs.buyerCity.value;
                                    $refs.congContactPerson.value = $refs.buyerContactPerson.value;
                                    $refs.congPhone.value = $refs.buyerPhone.value;
                                    $refs.congMobile.value = $refs.buyerMobile.value;
                                    $refs.congEmail.value = $refs.buyerEmail.value;
                                    $refs.congFax.value = $refs.buyerFax.value;
                                    $refs.congTinCst.value = $refs.buyerTinCst.value;
                                    $refs.congTinVat.value = $refs.buyerTinVat.value;
                                    $refs.congEcc.value = $refs.buyerEcc.value;
                                "
                            >Copy from Buyer</button>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label for="{{ form.cong_name_display.id_for_label }}" class="block text-sm font-medium text-gray-700">Name</label>
                                {{ form.cong_name_display }}
                                {% if form.cong_name_display.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_name_display.errors }}</p>{% endif %}
                                <button type="button" class="ml-2 bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                                    hx-post="{% url 'customer_details_fetch' customer_type='consignee' %}"
                                    hx-vals='{"consignee_name_display": document.getElementById("{{ form.cong_name_display.id_for_label }}").value}'
                                    hx-target="this" hx-swap="none"
                                    _="on htmx:afterRequest call populateCustomerFields(JSON.parse(event.detail.xhr.responseText), 'consignee')"
                                >Search</button>
                            </div>
                            <div>
                                <label for="{{ form.cong_add.id_for_label }}" class="block text-sm font-medium text-gray-700">Address</label>
                                {{ form.cong_add }}
                                {% if form.cong_add.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_add.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country</label>
                                {{ form.cong_country }}
                                {% if form.cong_country.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_country.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="consignee_state_select" class="block text-sm font-medium text-gray-700">State</label>
                                <select id="consignee_state_select" name="{{ form.cong_state.name }}" class="box3"
                                    hx-get="{% url 'cascading_dropdown' type='cities' parent_id='HX_VAL' %}"
                                    hx-trigger="change"
                                    hx-target="#consignee_city_select"
                                    hx-swap="innerHTML">
                                    {% if form.cong_state.value %}
                                        <option value="{{ form.cong_state.value }}" selected>{{ form.cong_state.field.queryset.get(id=form.cong_state.value) }}</option>
                                    {% else %}
                                        <option value="">Select a state</option>
                                    {% endif %}
                                    {% for option in form.cong_state.field.queryset %}
                                        <option value="{{ option.id }}" {% if option.id == form.cong_state.value %}selected{% endif %}>{{ option.name }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.cong_state.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_state.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="consignee_city_select" class="block text-sm font-medium text-gray-700">City</label>
                                <select id="consignee_city_select" name="{{ form.cong_city.name }}" class="box3">
                                    {% if form.cong_city.value %}
                                        <option value="{{ form.cong_city.value }}" selected>{{ form.cong_city.field.queryset.get(id=form.cong_city.value) }}</option>
                                    {% else %}
                                        <option value="">Select a city</option>
                                    {% endif %}
                                    {% for option in form.cong_city.field.queryset %}
                                        <option value="{{ option.id }}" {% if option.id == form.cong_city.value %}selected{% endif %}>{{ option.name }}</option>
                                    {% endfor %}
                                </select>
                                {% if form.cong_city.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_city.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_cotper.id_for_label }}" class="block text-sm font-medium text-gray-700">Contact person</label>
                                {{ form.cong_cotper }}
                                {% if form.cong_cotper.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_cotper.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_ph.id_for_label }}" class="block text-sm font-medium text-gray-700">Phone No.</label>
                                {{ form.cong_ph }}
                                {% if form.cong_ph.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_ph.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_mob.id_for_label }}" class="block text-sm font-medium text-gray-700">Mobile No.</label>
                                {{ form.cong_mob }}
                                {% if form.cong_mob.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_mob.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_email.id_for_label }}" class="block text-sm font-medium text-gray-700">E-mail</label>
                                {{ form.cong_email }}
                                {% if form.cong_email.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_email.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_fax.id_for_label }}" class="block text-sm font-medium text-gray-700">Fax No.</label>
                                {{ form.cong_fax }}
                                {% if form.cong_fax.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_fax.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_tin.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / CST No.</label>
                                {{ form.cong_tin }}
                                {% if form.cong_tin.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_tin.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_vat.id_for_label }}" class="block text-sm font-medium text-gray-700">TIN / VAT No.</label>
                                {{ form.cong_vat }}
                                {% if form.cong_vat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_vat.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cong_ecc.id_for_label }}" class="block text-sm font-medium text-gray-700">Customer's ECC.No.</label>
                                {{ form.cong_ecc }}
                                {% if form.cong_ecc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cong_ecc.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="button" @click="setActiveTab(3)" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                            <a href="{% url 'salesinvoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
                        </div>
                    </div>

                    <!-- Tab 3: Goods (SalesInvoiceDetail Grid) -->
                    <div x-show="activeTab === 3" role="tabpanel" class="space-y-4">
                        <h3 class="text-xl font-semibold mb-4">Goods Details</h3>
                        <div id="goods_table_container"
                            hx-trigger="load, refreshSalesInvoiceDetails from:body"
                            hx-get="{% url 'salesinvoice_table_partial' pk=salesinvoice.pk %}"
                            hx-swap="innerHTML"
                            class="overflow-x-auto">
                            <!-- Goods table will be loaded here via HTMX -->
                            <div class="text-center">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                <p class="mt-2">Loading Goods...</p>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="button" @click="setActiveTab(4)" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Next</button>
                            <a href="{% url 'salesinvoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
                        </div>
                    </div>

                    <!-- Tab 4: Taxation -->
                    <div x-show="activeTab === 4" role="tabpanel" class="space-y-4">
                        <h3 class="text-xl font-semibold mb-4">Taxation Details</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <div>
                                <label for="{{ form.add_amt.id_for_label }}" class="block text-sm font-medium text-gray-700">Add</label>
                                <div class="flex">
                                    {{ form.add_amt }}
                                    {{ form.add_type }}
                                </div>
                                {% if form.add_amt.errors %}<p class="text-red-500 text-xs mt-1">{{ form.add_amt.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.other_amt.id_for_label }}" class="block text-sm font-medium text-gray-700">Other Amount</label>
                                {{ form.other_amt }}
                                {% if form.other_amt.errors %}<p class="text-red-500 text-xs mt-1">{{ form.other_amt.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.deduction.id_for_label }}" class="block text-sm font-medium text-gray-700">Deduction</label>
                                <div class="flex">
                                    {{ form.deduction }}
                                    {{ form.deduction_type }}
                                </div>
                                {% if form.deduction.errors %}<p class="text-red-500 text-xs mt-1">{{ form.deduction.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700">P&F</label>
                                <div class="flex">
                                    {{ form.pf }}
                                    {{ form.pf_type }}
                                </div>
                                {% if form.pf.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.cenvat.id_for_label }}" class="block text-sm font-medium text-gray-700">CENVAT/Excise</label>
                                {{ form.cenvat }}
                                {% if form.cenvat.errors %}<p class="text-red-500 text-xs mt-1">{{ form.cenvat.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.sed.id_for_label }}" class="block text-sm font-medium text-gray-700">SED</label>
                                <div class="flex">
                                    {{ form.sed }}
                                    {{ form.sed_type }}
                                </div>
                                {% if form.sed.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sed.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.aed.id_for_label }}" class="block text-sm font-medium text-gray-700">AED</label>
                                <div class="flex">
                                    {{ form.aed }}
                                    {{ form.aed_type }}
                                </div>
                                {% if form.aed.errors %}<p class="text-red-500 text-xs mt-1">{{ form.aed.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label for="{{ form.freight.id_for_label }}" class="block text-sm font-medium text-gray-700">Freight</label>
                                <div class="flex">
                                    {{ form.freight }}
                                    {{ form.freight_type }}
                                </div>
                                {% if form.freight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.freight.errors }}</p>{% endif %}
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700" id="vat_cst_label">
                                    {% if is_vat_mode %}VAT{% elif is_cst_mode %}CST{% else %}VAT/CST{% endif %}
                                </label>
                                {% comment %} Assuming lblVAT from ASP.NET is driven by InvoiceMode, this is now dynamic via JS/Alpine {% endcomment %}
                                <div x-data="{ invoiceMode: {{ salesinvoice.invoice_mode.id|default:0 }} }" x-init="
                                    $watch('$store.salesinvoice.invoiceMode', value => {
                                        if (value === 2) { // Assuming 2 is VAT
                                            $refs.vatSelect.classList.remove('hidden');
                                            $refs.cstSelect.classList.add('hidden');
                                            document.getElementById('vat_cst_label').textContent = 'VAT';
                                        } else if (value === 3) { // Assuming 3 is CST
                                            $refs.vatSelect.classList.add('hidden');
                                            $refs.cstSelect.classList.remove('hidden');
                                            document.getElementById('vat_cst_label').textContent = 'CST';
                                        } else {
                                            $refs.vatSelect.classList.add('hidden');
                                            $refs.cstSelect.classList.add('hidden');
                                            document.getElementById('vat_cst_label').textContent = 'VAT/CST';
                                        }
                                    });
                                    // Initial state based on current invoice mode
                                    if (invoiceMode === 2) {
                                        $refs.vatSelect.classList.remove('hidden');
                                        $refs.cstSelect.classList.add('hidden');
                                        document.getElementById('vat_cst_label').textContent = 'VAT';
                                    } else if (invoiceMode === 3) {
                                        $refs.vatSelect.classList.add('hidden');
                                        $refs.cstSelect.classList.remove('hidden');
                                        document.getElementById('vat_cst_label').textContent = 'CST';
                                    } else {
                                        $refs.vatSelect.classList.add('hidden'); // Show VAT by default if no mode
                                        $refs.cstSelect.classList.add('hidden');
                                        document.getElementById('vat_cst_label').textContent = 'VAT/CST';
                                    }
                                ">
                                    <select name="{{ form.selected_cst.name }}" id="{{ form.selected_cst.id_for_label }}" class="box3" x-ref="cstSelect"
                                        {% if not is_cst_mode %}class="hidden"{% endif %}>
                                        {% for value, label in form.selected_cst.field.choices %}
                                            <option value="{{ value }}" {% if value|stringformat:"s" == form.selected_cst.value|stringformat:"s" %}selected{% endif %}>{{ label }}</option>
                                        {% endfor %}
                                    </select>
                                    <select name="{{ form.vat.name }}" id="{{ form.vat.id_for_label }}" class="box3" x-ref="vatSelect"
                                        {% if not is_vat_mode %}class="hidden"{% endif %}>
                                        <option value="">---------</option>
                                        {% for option in form.vat.field.queryset %}
                                            <option value="{{ option.id }}" {% if option.id == form.vat.value %}selected{% endif %}>{{ option.terms }}</option>
                                        {% endfor %}
                                    </select>
                                    {% if form.vat.errors or form.selected_cst.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vat.errors|default:form.selected_cst.errors }}</p>{% endif %}
                                </div>
                            </div>
                            <div>
                                <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700">Insurance</label>
                                <div class="flex">
                                    {{ form.insurance }}
                                    {{ form.insurance_type }}
                                </div>
                                {% if form.insurance.errors %}<p class="text-red-500 text-xs mt-1">{{ form.insurance.errors }}</p>{% endif %}
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Update</button>
                            <a href="{% url 'salesinvoice_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('salesinvoice', {
            invoiceMode: {{ salesinvoice.invoice_mode.id|default:0 }}
        });
        
        // Listen for changes on the invoice_mode dropdown and update Alpine store
        document.getElementById('{{ form.invoice_mode.id_for_label }}').addEventListener('change', (event) => {
            Alpine.store('salesinvoice').invoiceMode = parseInt(event.target.value);
        });

        // Initialize autocomplete for buyer and consignee names
        // Assumes jQuery UI Autocomplete or similar, replace with proper library if needed.
        // For HTMX, this could be a simple hx-get to a search endpoint
        // Example with jQuery UI (if included in base.html):
        $("#{{ form.buyer_name_display.id_for_label }}").autocomplete({
            source: "{% url 'customer_search_autocomplete' %}",
            minLength: 1,
            select: function(event, ui) {
                // Auto-fill form fields after selection if needed
                // For HTMX, a separate hx-post on blur/select would trigger a server-side fill
            }
        });
        $("#{{ form.cong_name_display.id_for_label }}").autocomplete({
            source: "{% url 'customer_search_autocomplete' %}",
            minLength: 1,
            select: function(event, ui) {
                // Auto-fill form fields after selection if needed
            }
        });
    });
</script>
{% endblock %}
```

**`sales/templates/sales/salesinvoice/_salesinvoicedetail_table.html` (Partial for Goods grid)**
```html
<table id="salesInvoiceDetailTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            <th class="py-2 px-4 border-b">SN</th>
            <th class="py-2 px-4 border-b"></th>{# Checkbox header #}
            <th class="py-2 px-4 border-b hidden">Id</th>
            <th class="py-2 px-4 border-b">Item Desc</th>
            <th class="py-2 px-4 border-b">Unit</th>
            <th class="py-2 px-4 border-b text-right">Qty</th>
            <th class="py-2 px-4 border-b text-right">Rem Qty</th>
            <th class="py-2 px-4 border-b text-right">Req Qty</th>
            <th class="py-2 px-4 border-b text-center">Unit Of Qty</th>
            <th class="py-2 px-4 border-b hidden">ItemId</th>
            <th class="py-2 px-4 border-b text-right">Amt In Per</th>
            <th class="py-2 px-4 border-b text-right">Rate</th>
        </tr>
    </thead>
    <tbody>
        {% for detail in sales_invoice_details %}
        {% include 'sales/salesinvoice/_salesinvoicedetail_row.html' with detail=detail is_checked=False units=units %}
        {% empty %}
        <tr>
            <td colspan="12" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    $(document).ready(function() {
        $('#salesInvoiceDetailTable').DataTable({
            "paging": false, // ASP.NET GridView had PageSize="8", but DataTables handles pagination,
            "info": false, // No info showing "Showing 1 of N entries"
            "searching": false, // No search bar
            "order": [], // Disable initial sorting
            "columnDefs": [
                { "orderable": false, "targets": [0, 1, 3, 4, 7, 8, 10] }, // Columns that are not sortable
                { "visible": false, "targets": [2, 9] } // Hidden columns Id, ItemId
            ],
            "language": { // Optional: Customize DataTables messages
                "emptyTable": "No data available in table"
            }
        });
    });
</script>
```

**`sales/templates/sales/salesinvoice/_salesinvoicedetail_row.html` (Partial for single Goods row)**
```html
<tr id="detail-row-{{ detail.pk }}">
    <td class="py-2 px-4 border-b">{{ forloop.counter }}</td>
    <td class="py-2 px-4 border-b">
        <input type="checkbox" id="checkbox-{{ detail.pk }}" name="select_detail" value="{{ detail.pk }}"
            {% if is_checked %}checked{% endif %}
            hx-post="{% url 'salesinvoice_detail_row_toggle' pk=detail.pk %}"
            hx-target="#detail-row-{{ detail.pk }}"
            hx-swap="outerHTML"
            hx-vals='{"is_checked": this.checked}'
        >
    </td>
    <td class="py-2 px-4 border-b hidden">{{ detail.pk }}</td>
    <td class="py-2 px-4 border-b">{{ detail.get_item_description_and_unit.0 }}</td>
    <td class="py-2 px-4 border-b text-center">{{ detail.get_item_description_and_unit.1 }}</td>{# Symbol from PO_Details #}
    <td class="py-2 px-4 border-b text-right">{{ detail.qty|floatformat:3 }}</td>
    <td class="py-2 px-4 border-b text-right">{{ detail.remaining_qty|floatformat:3 }}</td>
    <td class="py-2 px-4 border-b text-right">
        {% if is_checked %}
        <form hx-post="{% url 'salesinvoice_detail_update' pk=detail.pk %}" hx-target="closest tr" hx-swap="outerHTML">
            {% csrf_token %}
            {% with form=form|default:None %} {# Allow form to be passed, or create new if not present #}
            {% if form %}
                {% with form.req_qty as req_qty_field %}
                    {{ req_qty_field.as_widget }}
                    {% if req_qty_field.errors %}<p class="text-red-500 text-xs mt-1">{{ req_qty_field.errors }}</p>{% endif %}
                {% endwith %}
            {% else %}
                {% with form=SalesInvoiceDetailForm(instance=detail) %}
                    <input type="number" step="0.001" name="{{ form.req_qty.name }}" value="{{ detail.req_qty|floatformat:3 }}" class="box3 w-full" required>
                {% endwith %}
            {% endif %}
            <button type="submit" class="hidden"></button> {# Hidden submit button for form submission on change/enter #}
        </form>
        {% else %}
        {{ detail.req_qty|floatformat:3 }}
        {% endif %}
    </td>
    <td class="py-2 px-4 border-b text-center">
        {% if is_checked %}
        <form hx-post="{% url 'salesinvoice_detail_update' pk=detail.pk %}" hx-target="closest tr" hx-swap="outerHTML">
            {% csrf_token %}
            {% with form=form|default:None %} {# Allow form to be passed, or create new if not present #}
            {% if form %}
                {% with form.unit_qty as unit_qty_field %}
                    {{ unit_qty_field.as_widget }}
                    {% if unit_qty_field.errors %}<p class="text-red-500 text-xs mt-1">{{ unit_qty_field.errors }}</p>{% endif %}
                {% endwith %}
            {% else %}
                {% with form=SalesInvoiceDetailForm(instance=detail) %}
                    <select name="{{ form.unit_qty.name }}" class="box3 w-full">
                        {% for unit in units %}
                            <option value="{{ unit.id }}" {% if unit.id == detail.unit.id %}selected{% endif %}>{{ unit.symbol }}</option>
                        {% endfor %}
                    </select>
                {% endwith %}
            {% endif %}
            <button type="submit" class="hidden"></button> {# Hidden submit button for form submission on change #}
        </form>
        {% else %}
        {{ detail.unit.symbol }}
        {% endif %}
    </td>
    <td class="py-2 px-4 border-b hidden">{{ detail.item_id }}</td>
    <td class="py-2 px-4 border-b text-right">
        {% if is_checked %}
        <form hx-post="{% url 'salesinvoice_detail_update' pk=detail.pk %}" hx-target="closest tr" hx-swap="outerHTML">
            {% csrf_token %}
            {% with form=form|default:None %} {# Allow form to be passed, or create new if not present #}
            {% if form %}
                {% with form.amt_in_per as amt_in_per_field %}
                    {{ amt_in_per_field.as_widget }}
                    {% if amt_in_per_field.errors %}<p class="text-red-500 text-xs mt-1">{{ amt_in_per_field.errors }}</p>{% endif %}
                {% endwith %}
            {% else %}
                {% with form=SalesInvoiceDetailForm(instance=detail) %}
                    <input type="number" step="0.001" name="{{ form.amt_in_per.name }}" value="{{ detail.amt_in_per|floatformat:3 }}" class="box3 w-full" required>
                {% endwith %}
            {% endif %}
            <button type="submit" class="hidden"></button> {# Hidden submit button for form submission on change/enter #}
        </form>
        {% else %}
        {{ detail.amt_in_per|floatformat:3 }}
        {% endif %}
    </td>
    <td class="py-2 px-4 border-b text-right">{{ detail.rate|floatformat:3 }}</td>
</tr>
```

**`sales/templates/sales/salesinvoice/_dropdown_options.html` (Partial for cascading dropdowns)**
```html
<option value="">{{ empty_label }}</option>
{% for option in options %}
    <option value="{{ option.id }}">{{ option.name }}</option>
{% endfor %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
We'll define URLs for the main invoice edit page and all the necessary HTMX endpoints.

**`sales/urls.py`**
```python
from django.urls import path
from .views import (
    SalesInvoiceListView, SalesInvoiceUpdateView, SalesInvoiceTablePartialView,
    SalesInvoiceDetailRowToggleView, SalesInvoiceDetailUpdateView,
    CustomerSearchAutoComplete, CustomerDetailsFetchView, CascadingDropdownView,
    CommodityTariffView
)

urlpatterns = [
    # Main Invoice URLs
    path('invoice/', SalesInvoiceListView.as_view(), name='salesinvoice_list'),
    path('invoice/edit/<int:pk>/', SalesInvoiceUpdateView.as_view(), name='salesinvoice_edit'),
    
    # HTMX Partial URLs for Invoice Details
    path('invoice/header-partial/<int:pk>/', CommodityTariffView.as_view(), name='salesinvoice_header_partial'), # This view will be simple, just return the header part
    path('invoice/commodity-tariff/', CommodityTariffView.as_view(), name='commodity_tariff'), # For commodity dropdown auto-update

    # HTMX Partial URLs for Goods Grid
    path('invoice/details-table/<int:pk>/', SalesInvoiceTablePartialView.as_view(), name='salesinvoice_table_partial'),
    path('invoice/detail-row-toggle/<int:pk>/', SalesInvoiceDetailRowToggleView.as_view(), name='salesinvoice_detail_row_toggle'),
    path('invoice/detail-update/<int:pk>/', SalesInvoiceDetailUpdateView.as_view(), name='salesinvoice_detail_update'),

    # HTMX URLs for Customer Search/Copy
    path('customer/search/', CustomerSearchAutoComplete.as_view(), name='customer_search_autocomplete'),
    path('customer/fetch/<str:customer_type>/', CustomerDetailsFetchView.as_view(), name='customer_details_fetch'), # type can be 'buyer' or 'consignee'

    # HTMX URLs for Cascading Dropdowns (Country/State/City)
    path('invoice/get-states/<str:customer_type>/<int:parent_id>/', CascadingDropdownView.as_view(), {'type': 'states'}, name='cascading_dropdown_states'),
    path('invoice/get-cities/<str:customer_type>/<int:parent_id>/', CascadingDropdownView.as_view(), {'type': 'cities'}, name='cascading_dropdown_cities'),

    # General cascading dropdown, can be refined based on specific needs
    path('common/dropdown/<str:type>/<int:parent_id>/', CascadingDropdownView.as_view(), name='cascading_dropdown'),
]
```
*Note: The `salesinvoice_header_partial` in `urls.py` points to `CommodityTariffView` as a placeholder. You'd need a dedicated simple view for just rendering `_salesinvoice_header_info.html` with the `salesinvoice` object passed, to allow refreshing that section via HTMX.*

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
We'll include unit tests for the `SalesInvoice` and `SalesInvoiceDetail` models, focusing on their business logic methods and properties. We'll also add integration tests for the `SalesInvoiceUpdateView` and key HTMX endpoints, ensuring correct responses and data manipulation.

**`sales/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from .models import (
    SalesInvoice, SalesInvoiceDetail, SalesInvoiceMode, ServiceCategory,
    ExciseCommodity, TransportMode, RemovalNature, Unit, ExciseService,
    VATMaster, Country, State, City, Customer
)
from .forms import SalesInvoiceForm, SalesInvoiceDetailForm
import json

class SalesInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data for tests
        cls.country = Country.objects.create(id=1, name='India')
        cls.state = State.objects.create(id=1, country=cls.country, name='Maharashtra')
        cls.city = City.objects.create(id=1, state=cls.state, name='Mumbai')
        cls.invoice_mode_vat = SalesInvoiceMode.objects.create(id=2, description='VAT Invoice')
        cls.invoice_mode_cst = SalesInvoiceMode.objects.create(id=3, description='CST Invoice')
        cls.service_category = ServiceCategory.objects.create(id=1, description='Retail')
        cls.excise_commodity = ExciseCommodity.objects.create(id=1, terms='Electronic Goods')
        cls.transport_mode = TransportMode.objects.create(id=1, description='Road')
        cls.removal_nature = RemovalNature.objects.create(id=1, description='Sale')
        cls.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        cls.excise_service = ExciseService.objects.create(id=1, terms='Service Tax 10%', live=True)
        cls.vat_master = VATMaster.objects.create(id=1, terms='VAT 12.5%')
        cls.cst_master = VATMaster.objects.create(id=2, terms='CST 2%')

        cls.customer_buyer = Customer.objects.create(
            id=1, customer_id='CUST001', customer_name='Buyer Corp [CUST001]',
            material_del_address='123 Buyer St', material_del_country=cls.country,
            material_del_state=cls.state, material_del_city=cls.city,
            contact_person='John Doe', email='<EMAIL>', comp_id=1
        )
        cls.customer_consignee = Customer.objects.create(
            id=2, customer_id='CON002', customer_name='Consignee Ltd [CON002]',
            material_del_address='456 Consignee Ave', material_del_country=cls.country,
            material_del_state=cls.state, material_del_city=cls.city,
            contact_person='Jane Smith', email='<EMAIL>', comp_id=1
        )

        cls.sales_invoice = SalesInvoice.objects.create(
            id=1,
            comp_id=1,
            fin_year_id=1,
            invoice_no='INV001',
            sys_date=timezone.now().date(),
            sys_time=timezone.now().strftime('%H:%M:%S'),
            date_of_issue_invoice='2023-01-15',
            time_of_issue_invoice='10:00:00 AM',
            date_of_removal='2023-01-15',
            time_of_removal='11:00:00 AM',
            invoice_mode=cls.invoice_mode_vat,
            customer_category=cls.service_category,
            nature_of_removal=cls.removal_nature,
            commodity=cls.excise_commodity,
            tariff_heading='T001',
            mode_of_transport=cls.transport_mode,
            rrgc_no='RRGC123',
            vehi_reg_no='MH01AB1234',
            duty_rate=Decimal('5.000'),
            buyer_name='Buyer Corp [CUST001]', buyer_add='123 Buyer St', buyer_country=cls.country, buyer_state=cls.state, buyer_city=cls.city,
            buyer_cotper='John Doe', buyer_ph='1234567890', buyer_mob='9876543210', buyer_email='<EMAIL>',
            cong_name='Consignee Ltd [CON002]', cong_add='456 Consignee Ave', cong_country=cls.country, cong_state=cls.state, cong_city=cls.city,
            cong_cotper='Jane Smith', cong_ph='0987654321', cong_mob='0123456789', cong_email='<EMAIL>',
            add_type=0, add_amt=Decimal('100.00'), deduction_type=1, deduction=Decimal('5.00'),
            pf_type=0, pf=Decimal('20.00'), cenvat=cls.excise_service, sed=Decimal('0.00'), aed=Decimal('0.00'),
            vat=cls.vat_master, freight_type=0, freight=Decimal('50.00'), insurance_type=0, insurance=Decimal('30.00'),
            other_amt=Decimal('0.10')
        )
        cls.sales_invoice_detail = SalesInvoiceDetail.objects.create(
            id=1,
            master_invoice=cls.sales_invoice,
            item_id=101,
            unit=cls.unit_pcs,
            qty=Decimal('100.000'),
            req_qty=Decimal('10.000'),
            amt_in_per=Decimal('5.000'),
            rate=Decimal('25.000')
        )

    def test_sales_invoice_creation(self):
        self.assertEqual(self.sales_invoice.invoice_no, 'INV001')
        self.assertEqual(self.sales_invoice.buyer_name, 'Buyer Corp [CUST001]')
        self.assertEqual(self.sales_invoice.invoice_mode.description, 'VAT Invoice')

    def test_sales_invoice_detail_creation(self):
        self.assertEqual(self.sales_invoice_detail.item_id, 101)
        self.assertEqual(self.sales_invoice_detail.unit.symbol, 'PCS')

    def test_sales_invoice_mode_description_method(self):
        self.assertEqual(self.sales_invoice.get_invoice_mode_description(), 'VAT Invoice')

    def test_excise_commodity_get_tariff_heading(self):
        self.assertEqual(self.excise_commodity.get_tariff_heading(), 'Tariff A')
        # Add another commodity to test different cases
        excise_commodity_2 = ExciseCommodity.objects.create(id=2, terms='Other Goods')
        self.assertEqual(excise_commodity_2.get_tariff_heading(), 'Tariff B')

    def test_customer_get_customer_by_code_from_display(self):
        customer = Customer.get_customer_by_code_from_display('Buyer Corp [CUST001]')
        self.assertEqual(customer.customer_id, 'CUST001')
        self.assertIsNone(Customer.get_customer_by_code_from_display('NonExistent [XYZ]'))

    def test_sales_invoice_detail_remaining_qty(self):
        # Create another detail for the same item_id to test sum logic
        SalesInvoiceDetail.objects.create(
            id=2,
            master_invoice=self.sales_invoice, # Same master invoice
            item_id=101,
            unit=self.unit_pcs,
            qty=Decimal('100.000'), # Original total available
            req_qty=Decimal('20.000'), # Another requested quantity
            amt_in_per=Decimal('10.000'),
            rate=Decimal('25.000')
        )
        # Re-fetch the first detail to get updated related data
        detail_1 = SalesInvoiceDetail.objects.get(id=1)
        # Initial qty (100) - other_details_sum_req_qty (20) = 80
        self.assertEqual(detail_1.remaining_qty, Decimal('80.000'))

    def test_sales_invoice_detail_remaining_amount_percentage(self):
        # Create another detail for the same item_id
        SalesInvoiceDetail.objects.create(
            id=3,
            master_invoice=self.sales_invoice,
            item_id=101,
            unit=self.unit_pcs,
            qty=Decimal('100.000'),
            req_qty=Decimal('5.000'),
            amt_in_per=Decimal('10.000'), # Another amount percentage
            rate=Decimal('25.000')
        )
        detail_1 = SalesInvoiceDetail.objects.get(id=1)
        # 100 - other_details_sum_amt_in_per (10) = 90
        self.assertEqual(detail_1.remaining_amount_percentage, Decimal('90.00'))


class SalesInvoiceViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create all necessary lookup data as in ModelTest setUpTestData
        self.country = Country.objects.create(id=1, name='India')
        self.state = State.objects.create(id=1, country=self.country, name='Maharashtra')
        self.city = City.objects.create(id=1, state=self.state, name='Mumbai')
        self.invoice_mode_vat = SalesInvoiceMode.objects.create(id=2, description='VAT Invoice')
        self.invoice_mode_cst = SalesInvoiceMode.objects.create(id=3, description='CST Invoice')
        self.service_category = ServiceCategory.objects.create(id=1, description='Retail')
        self.excise_commodity = ExciseCommodity.objects.create(id=1, terms='Electronic Goods')
        self.transport_mode = TransportMode.objects.create(id=1, description='Road')
        self.removal_nature = RemovalNature.objects.create(id=1, description='Sale')
        self.unit_pcs = Unit.objects.create(id=1, symbol='PCS')
        self.excise_service = ExciseService.objects.create(id=1, terms='Service Tax 10%', live=True)
        self.vat_master = VATMaster.objects.create(id=1, terms='VAT 12.5%')
        self.cst_master = VATMaster.objects.create(id=2, terms='CST 2%') # For CST dropdown

        self.customer_buyer = Customer.objects.create(
            id=1, customer_id='CUST001', customer_name='Buyer Corp [CUST001]',
            material_del_address='123 Buyer St', material_del_country=self.country,
            material_del_state=self.state, material_del_city=self.city,
            contact_person='John Doe', email='<EMAIL>', comp_id=1
        )
        self.customer_consignee = Customer.objects.create(
            id=2, customer_id='CON002', customer_name='Consignee Ltd [CON002]',
            material_del_address='456 Consignee Ave', material_del_country=self.country,
            material_del_state=self.state, material_del_city=self.city,
            contact_person='Jane Smith', email='<EMAIL>', comp_id=1
        )

        self.sales_invoice = SalesInvoice.objects.create(
            id=1, comp_id=1, fin_year_id=1, invoice_no='INV001',
            sys_date=timezone.now().date(), sys_time=timezone.now().strftime('%H:%M:%S'),
            date_of_issue_invoice='2023-01-15', time_of_issue_invoice='10:00:00 AM',
            date_of_removal='2023-01-15', time_of_removal='11:00:00 AM',
            invoice_mode=self.invoice_mode_vat, customer_category=self.service_category,
            nature_of_removal=self.removal_nature, commodity=self.excise_commodity,
            tariff_heading='T001', mode_of_transport=self.transport_mode, rrgc_no='RRGC123',
            vehi_reg_no='MH01AB1234', duty_rate=Decimal('5.000'),
            buyer_name='Buyer Corp [CUST001]', buyer_add='123 Buyer St', buyer_country=self.country, buyer_state=self.state, buyer_city=self.city,
            buyer_cotper='John Doe', buyer_ph='1234567890', buyer_mob='9876543210', buyer_email='<EMAIL>',
            cong_name='Consignee Ltd [CON002]', cong_add='456 Consignee Ave', cong_country=self.country, cong_state=self.state, cong_city=self.city,
            cong_cotper='Jane Smith', cong_ph='0987654321', cong_mob='0123456789', cong_email='<EMAIL>',
            add_type=0, add_amt=Decimal('100.00'), deduction_type=1, deduction=Decimal('5.00'),
            pf_type=0, pf=Decimal('20.00'), cenvat=self.excise_service, sed=Decimal('0.00'), aed=Decimal('0.00'),
            vat=self.vat_master, freight_type=0, freight=Decimal('50.00'), insurance_type=0, insurance=Decimal('30.00'),
            other_amt=Decimal('0.10')
        )
        self.sales_invoice_detail = SalesInvoiceDetail.objects.create(
            id=1, master_invoice=self.sales_invoice, item_id=101, unit=self.unit_pcs,
            qty=Decimal('100.000'), req_qty=Decimal('10.000'), amt_in_per=Decimal('5.000'),
            rate=Decimal('25.000')
        )
        # Mock user for session data (comp_id, username)
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(username='testuser', password='password')
        self.client.force_login(self.user)

    def test_update_view_get(self):
        response = self.client.get(reverse('salesinvoice_edit', args=[self.sales_invoice.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/salesinvoice/edit_form.html')
        self.assertContains(response, 'INV001') # Check for invoice number in content

    def test_update_view_post_success(self):
        data = {
            'date_of_issue_invoice': '2023-01-20',
            'time_of_issue_invoice': '12:00:00 PM',
            'date_of_removal': '2023-01-20',
            'time_of_removal': '13:00:00 PM',
            'duty_rate': '6.000',
            'rrgc_no': 'RRGC456',
            'vehi_reg_no': 'MH01CD5678',
            'invoice_mode': self.invoice_mode_cst.id, # Change mode to CST
            'customer_category': self.service_category.id,
            'nature_of_removal': self.removal_nature.id,
            'commodity': self.excise_commodity.id,
            'mode_of_transport': self.transport_mode.id,
            # Buyer/Consignee (from current object for simplicity)
            'buyer_name': self.sales_invoice.buyer_name, 'buyer_name_display': self.sales_invoice.buyer_name,
            'buyer_add': self.sales_invoice.buyer_add, 'buyer_country': self.sales_invoice.buyer_country.id,
            'buyer_state': self.sales_invoice.buyer_state.id, 'buyer_city': self.sales_invoice.buyer_city.id,
            'buyer_cotper': self.sales_invoice.buyer_cotper, 'buyer_ph': self.sales_invoice.buyer_ph,
            'buyer_mob': self.sales_invoice.buyer_mob, 'buyer_email': self.sales_invoice.buyer_email,
            'buyer_fax': self.sales_invoice.buyer_fax, 'buyer_tin': self.sales_invoice.buyer_tin, 'buyer_vat': self.sales_invoice.buyer_vat, 'buyer_ecc': self.sales_invoice.buyer_ecc,
            'cong_name': self.sales_invoice.cong_name, 'cong_name_display': self.sales_invoice.cong_name,
            'cong_add': self.sales_invoice.cong_add, 'cong_country': self.sales_invoice.cong_country.id,
            'cong_state': self.sales_invoice.cong_state.id, 'cong_city': self.sales_invoice.cong_city.id,
            'cong_cotper': self.sales_invoice.cong_cotper, 'cong_ph': self.sales_invoice.cong_ph,
            'cong_mob': self.sales_invoice.cong_mob, 'cong_email': self.sales_invoice.cong_email,
            'cong_fax': self.sales_invoice.cong_fax, 'cong_tin': self.sales_invoice.cong_tin, 'cong_vat': self.sales_invoice.cong_vat, 'cong_ecc': self.sales_invoice.cong_ecc,
            # Taxation
            'add_type': '0', 'add_amt': '110.00', 'deduction_type': '0', 'deduction': '6.00',
            'pf_type': '0', 'pf': '22.00', 'cenvat': self.excise_service.id, 'sed_type': '0', 'sed': '1.00',
            'aed_type': '0', 'aed': '0.50', 'freight_type': '0', 'freight': '55.00',
            'insurance_type': '0', 'insurance': '33.00', 'other_amt': '0.20',
            'vat': '', # VAT should be empty if mode is CST
            'selected_cst': '0', # CST with C form
            'cst': self.cst_master.id # Assign CST master
        }
        response = self.client.post(reverse('salesinvoice_edit', args=[self.sales_invoice.pk]), data)
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.sales_invoice.refresh_from_db()
        self.assertEqual(self.sales_invoice.duty_rate, Decimal('6.000'))
        self.assertEqual(self.sales_invoice.invoice_mode, self.invoice_mode_cst)
        self.assertIsNone(self.sales_invoice.vat) # Should be None when CST mode

    def test_update_view_post_invalid(self):
        data = {
            'date_of_issue_invoice': 'invalid-date', # Invalid date
            # ... other required fields (even if valid, one invalid should trigger error)
            'buyer_name_display': self.sales_invoice.buyer_name, 'buyer_add': '', # Missing required address
            'buyer_country': self.country.id, 'buyer_state': self.state.id, 'buyer_city': self.city.id,
            'buyer_cotper': '', 'buyer_ph': '', 'buyer_mob': '', 'buyer_email': '',
            'cong_name_display': self.sales_invoice.cong_name, 'cong_add': '',
            'cong_country': self.country.id, 'cong_state': self.state.id, 'cong_city': self.city.id,
            'cong_cotper': '', 'cong_ph': '', 'cong_mob': '', 'cong_email': '',
            'add_type': '0', 'add_amt': '100.00', 'deduction_type': '0', 'deduction': '5.00',
            'pf_type': '0', 'pf': '20.00', 'cenvat': self.excise_service.id, 'sed_type': '0', 'sed': '0.00',
            'aed_type': '0', 'aed': '0.00', 'freight_type': '0', 'freight': '0.00',
            'insurance_type': '0', 'insurance': '0.00', 'other_amt': '0.00',
            'vat': self.vat_master.id, 'selected_cst': '0', 'cst': ''
        }
        response = self.client.post(reverse('salesinvoice_edit', args=[self.sales_invoice.pk]), data)
        self.assertEqual(response.status_code, 200) # Should render form with errors
        self.assertIn('This field is required.', response.content.decode())

    def test_sales_invoice_table_partial_view(self):
        response = self.client.get(reverse('salesinvoice_table_partial', args=[self.sales_invoice.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/salesinvoice/_salesinvoicedetail_table.html')
        self.assertContains(response, '<td>INV001</td>') # Check if invoice details are rendered

    def test_sales_invoice_detail_row_toggle_view(self):
        # Test toggling to edit mode
        response = self.client.post(reverse('salesinvoice_detail_row_toggle', args=[self.sales_invoice_detail.pk]), {'is_checked': 'true'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/salesinvoice/_salesinvoicedetail_row.html')
        self.assertContains(response, '<input type="number" step="0.001" name="req_qty"') # Check for input field
        
        # Test toggling back to display mode
        response = self.client.post(reverse('salesinvoice_detail_row_toggle', args=[self.sales_invoice_detail.pk]), {'is_checked': 'false'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/salesinvoice/_salesinvoicedetail_row.html')
        self.assertContains(response, self.sales_invoice_detail.req_qty|stringformat:"0.3f") # Check for display value

    def test_sales_invoice_detail_update_view(self):
        new_req_qty = Decimal('15.000')
        new_amt_in_per = Decimal('7.500')
        data = {
            'req_qty': str(new_req_qty),
            'amt_in_per': str(new_amt_in_per),
            'unit': self.unit_pcs.id # Must send the ID of the unit
        }
        response = self.client.post(reverse('salesinvoice_detail_update', args=[self.sales_invoice_detail.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.sales_invoice_detail.refresh_from_db()
        self.assertEqual(self.sales_invoice_detail.req_qty, new_req_qty)
        self.assertEqual(self.sales_invoice_detail.amt_in_per, new_amt_in_per)
        self.assertContains(response, str(new_req_qty)) # Check for updated value in returned HTML

    def test_customer_search_autocomplete(self):
        response = self.client.get(reverse('customer_search_autocomplete'), {'term': 'buyer'}, HTTP_ACCEPT='application/json')
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIn('Buyer Corp [CUST001]', json_response)

    def test_customer_details_fetch_view(self):
        data = {'buyer_name_display': 'Buyer Corp [CUST001]'}
        response = self.client.post(reverse('customer_details_fetch', args=['buyer']), data, HTTP_HX_REQUEST='true', content_type='application/x-www-form-urlencoded')
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertTrue(json_response['success'])
        self.assertEqual(json_response['data']['address'], '123 Buyer St')

    def test_cascading_dropdown_view_states(self):
        response = self.client.get(reverse('cascading_dropdown', args=['states', self.country.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.state.pk}">Maharashtra</option>')

    def test_cascading_dropdown_view_cities(self):
        response = self.client.get(reverse('cascading_dropdown', args=['cities', self.state.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f'<option value="{self.city.pk}">Mumbai</option>')

    def test_commodity_tariff_view(self):
        response = self.client.get(reverse('commodity_tariff'), {'commodity_id': self.excise_commodity.pk}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode(), 'Tariff A')
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided Django code snippets (Forms, Views, Templates, URLs) already incorporate HTMX and Alpine.js where appropriate, as detailed below:

-   **HTMX for Tab Navigation (Implicit):** While Alpine.js handles the `x-show` for tabs, HTMX can be used within each tab's content area if those tabs need to lazy-load or dynamically update their content from the server on tab switch (e.g., `hx-get` on `x-show` trigger). For this, the `setActiveTab` function in Alpine.js could also trigger an HTMX request if the tab content is not loaded initially. We used Alpine.js to manage `activeTab` state for client-side fluidity.
-   **HTMX for Cascading Dropdowns:**
    -   `buyer_country`/`cong_country` dropdowns have `hx-get` to `cascading_dropdown` URL.
    -   `hx-target` points to the respective state dropdown (`#buyer_state_select`, `#consignee_state_select`).
    -   `hx-trigger="change"` ensures the request is sent when the selection changes.
    -   The state dropdowns similarly trigger city dropdown updates.
-   **HTMX for Customer Search:**
    -   The "Search" buttons for buyer and consignee are `hx-post` to `customer_details_fetch` URL.
    -   `hx-vals` passes the current value of the search input.
    -   `hx-target="this" hx-swap="none"` indicates that the button itself is the target, but no swapping of content happens. Instead, a custom `_` (hyperscript) action `on htmx:afterRequest call populateCustomerFields(...)` is used to trigger an Alpine.js function that parses the JSON response and updates relevant form fields.
    -   The `AutoCompleteExtender` is replaced by `jQuery UI Autocomplete` (as a placeholder) which could also use the `customer_search_autocomplete` HTMX endpoint.
-   **HTMX for Goods Grid (DataTables):**
    -   The `goods_table_container` `div` uses `hx-trigger="load, refreshSalesInvoiceDetails from:body"` and `hx-get` to `salesinvoice_table_partial` to load the entire `GridView1` content dynamically.
    -   Each row's checkbox has `hx-post` to `salesinvoice_detail_row_toggle` and `hx-target="closest tr" hx-swap="outerHTML"`. This allows toggling a single row's state (display vs. edit) without re-rendering the whole table.
    -   The input fields (`req_qty`, `amt_in_per`, `unit`) within the `_salesinvoicedetail_row.html` partial, when in edit mode, are wrapped in a form that `hx-post` to `salesinvoice_detail_update` to save individual row changes.
-   **HTMX for Form Submission & Notifications:**
    -   The main `SalesInvoiceForm` has `hx-post` and `hx-swap="none"`. This means on successful submission, the page won't refresh.
    -   The Django `form_valid` method sends an `HX-Trigger` header (`refreshSalesInvoiceHeader` and `showMessage`) which Alpine.js picks up to update the header section and display success messages.
-   **Alpine.js for UI State Management:**
    -   Manages the `activeTab` state, persisted using `sessionStorage`.
    -   `populateCustomerFields` function in Alpine.js handles parsing JSON from customer search and updating form fields.
    -   The `VAT/CST` label and dropdown visibility are dynamically controlled by Alpine.js reacting to changes in the `invoice_mode` field, demonstrating client-side reactivity.
    -   The `showMessage` Alpine.js method provides a simple way to display messages based on HTMX triggers.

This architecture ensures a highly interactive and responsive user experience without full page reloads, aligning perfectly with modern web application patterns and significantly reducing the complexity often associated with traditional ASP.NET Web Forms postback models.

---

## Final Notes

This plan provides a detailed blueprint for migrating your ASP.NET `SalesInvoice_Edit_Details` module to Django. Remember to:

-   **Replace placeholders:** `[TABLE_NAME]`, `[MODEL_NAME]`, `[APP_NAME]`, `[FIELD]` with your actual values.
-   **Complete all Models:** The lookup models and customer models provided are minimal examples; ensure all necessary fields from your existing schema are mapped.
-   **Implement `clsFunctions`:** All helper functions from `clsFunctions.cs` (e.g., `Connection`, `Decrypt`, `DateValidation`, `EmailValidation`, `NumberValidationQty`, `ExciseCommodity`, `getCode`, `dropdownCountry`, `dropdownState`, `dropdownCity`) must be re-implemented as Python helper functions in `sales/utils.py` or as methods on your Django models (as shown in `SalesInvoice` and `SalesInvoiceDetail` for `get_tariff_heading`, `remaining_qty`, etc.).
-   **Database Connection:** Ensure your Django `settings.py` is configured to connect to your existing SQL Server database using `django-pyodbc-azure` or `django-mssql-backend` and that `managed=False` is set for all mapped legacy models.
-   **Error Handling and Edge Cases:** While basic validation is included, a full enterprise application will require robust error handling and consideration of all edge cases found in the original ASP.NET application.
-   **Security:** Ensure proper authentication and authorization (`@login_required`, Django permissions) are applied to views.
-   **Frontend Libraries:** DataTables, HTMX, and Alpine.js CDN links should be included in your `core/base.html` as per the guidelines.
-   **Tailwind CSS:** Ensure your Django project is set up with Tailwind CSS for consistent styling.

This systematic, automation-driven approach, guided by conversational AI, will enable a smooth and efficient transition to a highly performant and maintainable Django application.