This comprehensive Django modernization plan outlines the strategic approach to transitioning your legacy ASP.NET `ProformaInvoice_Print` module to a robust, modern Django application. Our focus is on leveraging AI-assisted automation to streamline the conversion process, ensuring a smooth, efficient, and low-risk migration. This plan prioritizes business benefits by delivering a highly maintainable, scalable, and user-friendly solution, eliminating technical debt, and improving performance.

### Business Value Proposition

Migrating this ASP.NET module to Django offers significant advantages:

1.  **Reduced Technical Debt:** Moves away from outdated ASP.NET Web Forms, which are complex and expensive to maintain, to a modern, open-source framework.
2.  **Improved Performance and Scalability:** Django's architecture, combined with HTMX for dynamic interactions, provides a snappier user experience and better handles increased user loads.
3.  **Enhanced User Experience:** Modern front-end elements like DataTables for interactive lists and Alpine.js for subtle UI enhancements provide a more intuitive and responsive interface.
4.  **Simplified Development and Maintenance:** Django's "batteries-included" philosophy, combined with the "Fat Model, Thin View" pattern, makes code easier to read, understand, and extend, reducing future development costs.
5.  **Cost-Effectiveness:** Open-source technologies like Django, HTMX, and PostgreSQL (if applicable) eliminate licensing costs associated with proprietary software.
6.  **Future-Proofing:** Adopting a widely supported and actively developed framework like Django ensures your application remains relevant and adaptable to future business needs.
7.  **Increased Developer Productivity:** Django's conventions and comprehensive tooling allow developers to build features faster and with fewer errors.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

The ASP.NET code interacts with several tables. Based on the `bindgrid` method's SQL queries and `GridView` bindings, we identify the following:

-   `tblACC_ProformaInvoice_Master`: This is the main table for Proforma Invoices.
    -   Columns: `Id`, `FinYearId`, `SysDate`, `InvoiceNo`, `WONo`, `PONo`, `CustomerCode`, `CompId`.
-   `tblFinancial_master`: Used to look up the financial year name.
    -   Columns: `FinYearId`, `FinYear`.
-   `SD_Cust_master`: Used to look up customer names and IDs.
    -   Columns: `CustomerId`, `CustomerName`, `CompId`.
-   `SD_Cust_WorkOrder_Master`: Used to look up work order numbers from a comma-separated list of IDs.
    -   Columns: `Id`, `WONo`, `CompId`.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

The provided ASP.NET page is primarily a "read" (display and search) and "action" (print/details redirection) module.

-   **Read (List and Search):**
    -   Displays a list of `ProformaInvoice` records.
    -   Allows searching by "Customer Name," "PO No," or "Invoice No" via a dropdown and textbox.
    -   Handles pagination (`AllowPaging=True`) and client-side presentation (implied by `yui-datatable.css`).
    -   Performs data enrichment by joining `ProformaInvoice` with `tblFinancial_master`, `SD_Cust_master`, and `SD_Cust_WorkOrder_Master` to display related names and numbers.
-   **Action (Print/Details Redirection):**
    -   A "Select" `LinkButton` on each row triggers a redirect to `ProformaInvoice_Print_Details.aspx` with various query parameters (`InvNo`, `InvId`, `cid`, `PT`, `ModId`, `SubModId`, `Key`). The `PT` (Print Type) is selected from a dropdown within the grid row.
-   **Autocomplete:**
    -   Provides auto-completion for "Customer Name" using an `AutoCompleteExtender` and a `WebMethod` that queries `SD_Cust_master`.

No explicit "Create," "Update," or "Delete" operations are present for the `ProformaInvoice` entity directly on this page.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

-   **Search Controls:**
    -   `DropDownList1` (Search By): A `select` element for choosing search criteria (Customer Name, PO No, Invoice No).
    -   `txtCustName` (Customer Name Textbox): A `text input` field, dynamically shown/hidden.
    -   `txtCustName_AutoCompleteExtender`: An AJAX-powered autocomplete feature for `txtCustName`.
    -   `txtpoNo` (PO No/Invoice No Textbox): A `text input` field, dynamically shown/hidden.
    -   `btnSearch`: A `button` to trigger the search operation.
-   **Data Display Grid:**
    -   `GridView1`: A `table` displaying `Proforma Invoice` data.
        -   Columns include: `SN`, `Print Type` (a `dropdown` per row), `FinYear`, `InVoice No`, `Date`, `Customer Name`, `WO No`, `PO No`, and a `Select` `LinkButton`.
        -   Includes client-side paging (will be handled by DataTables).

## Step 4: Generate Django Code

We will create a new Django application, for example, named `invoices`, to house this module.

### 4.1 Models (`invoices/models.py`)

This file defines the Django models that map to your existing database tables. Note the use of `managed = False` and `db_table` to connect to pre-existing tables. We also add "Fat Model" methods to encapsulate complex data retrieval and transformation logic (like fetching associated names and parsing comma-separated IDs), keeping views clean.

```python
from django.db import models
from django.db.models import F, Value
from django.db.models.functions import Concat
import logging

logger = logging.getLogger(__name__)

# NOTE: In a fresh Django application, these would ideally be ForeignKey relationships
# and proper data types (e.g., IntegerField for WorkOrder IDs, not CharField for WO_No).
# However, for a direct migration, we're mimicking the existing schema and logic.

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master.
    Used to get the display name for FinYearId.
    """
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class Customer(models.Model):
    """
    Maps to SD_Cust_master.
    Used for customer name display and autocomplete.
    """
    id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    name = models.CharField(db_column='CustomerName', max_length=255)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.name} [{self.id}]"

class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    Used to resolve comma-separated Work Order IDs to actual WO numbers.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    work_order_number = models.CharField(db_column='WONo', max_length=100)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.work_order_number

class ProformaInvoice(models.Model):
    """
    Maps to tblACC_ProformaInvoice_Master, the main entity for this module.
    Includes methods to transform raw database fields into display-ready values.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year_id = models.IntegerField(db_column='FinYearId')
    system_date = models.DateField(db_column='SysDate')
    invoice_number = models.CharField(db_column='InvoiceNo', max_length=100)
    work_order_numbers = models.CharField(db_column='WONo', max_length=255, blank=True, null=True) # Comma-separated IDs
    po_number = models.CharField(db_column='PONo', max_length=100, blank=True, null=True)
    customer_code = models.CharField(db_column='CustomerCode', max_length=50)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblACC_ProformaInvoice_Master'
        verbose_name = 'Proforma Invoice'
        verbose_name_plural = 'Proforma Invoices'

    def __str__(self):
        return self.invoice_number

    # Business logic methods (Fat Model approach)
    def get_display_customer_name(self):
        """Fetches and formats customer name and ID for display."""
        try:
            customer = Customer.objects.get(id=self.customer_code, company_id=self.company_id)
            return f"{customer.name} [{customer.id}]"
        except Customer.DoesNotExist:
            logger.warning(f"Customer with code '{self.customer_code}' not found for Proforma Invoice ID {self.id}. Displaying 'N/A'.")
            return "N/A"
        except Exception as e:
            logger.error(f"Error fetching customer '{self.customer_code}' for Invoice ID {self.id}: {e}")
            return "N/A"

    def get_financial_year_name(self):
        """Fetches the financial year name from tblFinancial_master."""
        try:
            fin_year = FinancialYear.objects.get(id=self.fin_year_id)
            return fin_year.year_name
        except FinancialYear.DoesNotExist:
            logger.warning(f"Financial year with ID '{self.fin_year_id}' not found for Proforma Invoice ID {self.id}. Displaying 'N/A'.")
            return "N/A"
        except Exception as e:
            logger.error(f"Error fetching financial year '{self.fin_year_id}' for Invoice ID {self.id}: {e}")
            return "N/A"

    def get_formatted_system_date(self):
        """Formats the system date as 'dd-mm-yyyy'."""
        return self.system_date.strftime('%d-%m-%Y') if self.system_date else 'N/A'

    def get_display_work_order_numbers(self):
        """Parses comma-separated work order IDs and fetches their corresponding numbers."""
        if not self.work_order_numbers:
            return ""
        
        # Split, clean, and convert to integers, filtering out empty strings or non-digits
        wo_ids = [int(x.strip()) for x in self.work_order_numbers.split(',') if x.strip().isdigit()]
        if not wo_ids:
            return ""

        try:
            # Filter by company_id as per original ASP.NET code
            work_orders = WorkOrder.objects.filter(id__in=wo_ids, company_id=self.company_id)
            return ",".join([wo.work_order_number for wo in work_orders])
        except Exception as e:
            logger.error(f"Error fetching work orders for invoice ID {self.id}, WO IDs: {self.work_order_numbers}: {e}")
            return ""

    @classmethod
    def search_invoices(cls, search_by, search_value, company_id, fin_year_id):
        """
        Performs dynamic invoice search based on criteria, encapsulating the 'bindgrid' logic.
        This class method allows for robust query building.
        """
        # Base query: filter by company and financial year (less than or equal to, as in ASP.NET)
        queryset = cls.objects.filter(company_id=company_id, fin_year_id__lte=fin_year_id).order_by('-invoice_number')

        if search_value:
            if search_by == '0':  # Customer Name
                # Find customer codes whose names contain the search value
                customer_name_part = search_value.split('[')[0].strip() # Handle 'Name [ID]' format
                customer_ids_matching_name = Customer.objects.filter(
                    name__icontains=customer_name_part,
                    company_id=company_id
                ).values_list('id', flat=True) # Get just the CustomerId
                
                # Filter ProformaInvoices by these matching customer codes
                queryset = queryset.filter(customer_code__in=list(customer_ids_matching_name))
            elif search_by == '2':  # PO No
                queryset = queryset.filter(po_number__icontains=search_value)
            elif search_by == '3':  # Invoice No
                queryset = queryset.filter(invoice_number__icontains=search_value)
        
        return queryset

```

### 4.2 Forms (`invoices/forms.py`)

This module requires a search form rather than a CRUD form for `ProformaInvoice`. This form defines the search criteria and inputs, with attributes for HTMX to enable dynamic behavior.

```python
from django import forms

class ProformaInvoiceSearchForm(forms.Form):
    """
    Form for searching Proforma Invoices by different criteria.
    Includes HTMX attributes for dynamic input field toggling and autocomplete.
    """
    SEARCH_BY_CHOICES = [
        ('0', 'Customer Name'),
        ('2', 'PO No'),
        ('3', 'Invoice No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        initial='0', # Default to Customer Name
        widget=forms.Select(attrs={
            'class': 'box3', # Custom CSS class from original ASP.NET
            'hx-get': "{% url 'invoices:toggle_search_input' %}", # HTMX to load correct input field
            'hx-target': '#search-input-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change', # Trigger on dropdown change
            'hx-push-url': 'false' # Prevent URL change on this interaction
        })
    )
    
    # Text input for Customer Name (with autocomplete)
    search_customer_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter Customer Name',
            'hx-get': "{% url 'invoices:autocomplete_customer_name' %}", # HTMX for autocomplete suggestions
            'hx-target': '#customer-suggestions',
            'hx-trigger': 'keyup changed delay:500ms', # Trigger on keyup, with debounce
            'hx-indicator': '#loading-indicator', # Show loading spinner
            'hx-swap': 'innerHTML'
        })
    )
    
    # Text input for PO No or Invoice No
    search_po_invoice_no = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter PO No/Invoice No'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply consistent Tailwind CSS styling and initial visibility logic
        current_search_by = self.initial.get('search_by', '0')

        if current_search_by == '0':
            # Initially show customer name field
            self.fields['search_po_invoice_no'].widget.attrs['style'] = 'display:none;'
            self.fields['search_po_invoice_no'].required = False
        else:
            # Initially show PO/Invoice no field
            self.fields['search_customer_name'].widget.attrs['style'] = 'display:none;'
            self.fields['search_customer_name'].required = False
            
        # Add Tailwind classes to all relevant form fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, (forms.TextInput, forms.Select)):
                current_classes = field.widget.attrs.get('class', '')
                field.widget.attrs['class'] = f"{current_classes} block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"

```

### 4.3 Views (`invoices/views.py`)

Django Class-Based Views (CBVs) are used here. The `ProformaInvoiceListView` serves the main page, while `ProformaInvoiceTablePartialView` handles the dynamic loading of the DataTables content via HTMX. Additional views manage the dynamic search input and customer autocomplete.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.shortcuts import render
from .models import ProformaInvoice, Customer, FinancialYear, WorkOrder
from .forms import ProformaInvoiceSearchForm
import json
import random
import string

# Default values for Company ID and Financial Year ID, mimicking ASP.NET Session variables.
# In a production environment, these should be securely obtained from the authenticated user's profile
# or session management integrated with Django's auth system.
DEFAULT_COMPANY_ID = 1
DEFAULT_FIN_YEAR_ID = 2023

class ProformaInvoiceListView(ListView):
    """
    Main view for displaying the Proforma Invoice print selection page.
    It renders the search form and a container for the HTMX-loaded table.
    """
    model = ProformaInvoice
    template_name = 'invoices/proformainvoice/list.html'
    context_object_name = 'invoices'
    paginate_by = 20 # For initial query (though DataTables handles its own pagination via JS)

    def get_queryset(self):
        # This queryset is primarily for initializing the page.
        # The actual data for DataTables is fetched by `ProformaInvoiceTablePartialView` via HTMX.
        # We return an empty queryset to avoid loading all data on initial page load.
        return ProformaInvoice.objects.none() 

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate the search form with initial data based on GET parameters or defaults.
        initial_search_by = self.request.GET.get('search_by', '0')
        initial_data = {
            'search_by': initial_search_by,
            'search_customer_name': self.request.GET.get('search_customer_name', ''),
            'search_po_invoice_no': self.request.GET.get('search_po_invoice_no', '')
        }
        context['search_form'] = ProformaInvoiceSearchForm(initial=initial_data)
        return context

class ProformaInvoiceTablePartialView(View):
    """
    Renders the HTML for the DataTables table, intended for HTMX requests.
    This view performs the actual data filtering and preparation.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.session.get('compid', DEFAULT_COMPANY_ID)
        fin_year_id = request.session.get('finyear', DEFAULT_FIN_YEAR_ID)

        # Retrieve search parameters from GET request (sent by HTMX form submission)
        search_by = request.GET.get('search_by', '0')
        search_customer_name = request.GET.get('search_customer_name', '')
        search_po_invoice_no = request.GET.get('search_po_invoice_no', '')

        # Determine the effective search value based on 'search_by' selection
        search_value = search_customer_name if search_by == '0' else search_po_invoice_no
        
        # Use the 'Fat Model' method to get filtered invoices
        invoices = ProformaInvoice.search_invoices(search_by, search_value, company_id, fin_year_id)
        
        # Define the print types for the dropdown within each table row
        print_types = [
            "ORIGINAL FOR BUYER",
            "DUPLICATE FOR TRANSPORTER",
            "TRIPLICATE FOR ASSESSEE",
            "EXTRA COPY [NOT FOR CENVAT PURPOSE]",
        ]

        context = {
            'invoices': invoices,
            'print_types': print_types
        }
        return render(request, 'invoices/proformainvoice/_proformainvoice_table.html', context)

class ToggleSearchInputView(View):
    """
    HTMX endpoint to dynamically switch between the customer name input and
    the PO/Invoice number input field in the search form.
    """
    def get(self, request, *args, **kwargs):
        selected_value = request.GET.get('search_by')
        context = {}
        # Create a dummy form instance to access field widgets and render them
        form_instance = ProformaInvoiceSearchForm() 

        if selected_value == '0': # Customer Name selected
            context['field_type'] = 'customer_name'
            context['field'] = form_instance.fields['search_customer_name']
        else: # PO No or Invoice No selected
            context['field_type'] = 'po_invoice_no'
            context['field'] = form_instance.fields['search_po_invoice_no']
        
        return render(request, 'invoices/proformainvoice/_search_input_field.html', context)

class CustomerAutoCompleteView(View):
    """
    HTMX-friendly endpoint for customer name autocomplete.
    Mimics the ASP.NET WebMethod 'sql'. Returns HTML partial with suggestions.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '')
        company_id = request.session.get('compid', DEFAULT_COMPANY_ID)

        if not prefix_text:
            return HttpResponse("") # Return empty response if no prefix

        # Filter customers whose names contain the prefix text, for the specific company
        customers = Customer.objects.filter(
            name__icontains=prefix_text, 
            company_id=company_id
        ).annotate(
            # Create the combined display name as seen in ASP.NET
            customer_full_name=Concat(F('name'), Value(' ['), F('id'), Value(']'))
        ).values('id', 'name', 'customer_full_name')[:10] # Limit suggestions to 10

        context = {'suggestions': customers}
        return render(request, 'invoices/proformainvoice/_customer_autocomplete_suggestions.html', context)

class ProformaInvoicePrintView(View):
    """
    Handles the 'Select' action from a table row.
    Mimics the ASP.NET Response.Redirect logic, using HX-Redirect for client-side navigation.
    """
    def get(self, request, pk, *args, **kwargs):
        invoice_id = pk # Invoice ID from URL path
        print_type = request.GET.get('print_type') # Value from the dropdown in the table row

        try:
            invoice = ProformaInvoice.objects.get(id=invoice_id)
            
            # Generate a random key, mimicking ASP.NET's fun.GetRandomAlphaNumeric()
            random_key = ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))

            # Construct the redirect URL with all necessary query parameters
            redirect_url = reverse_lazy('invoices:proforma_print_details', kwargs={'pk': invoice_id}) + \
                           f"?InvNo={invoice.invoice_number}&InvId={invoice.id}&cid={invoice.customer_code}&PT={print_type}&ModId=11&SubModId=104&Key={random_key}"
            
            messages.success(request, f"Redirecting to print details for Invoice {invoice.invoice_number}.")
            return HttpResponse(
                status=200, # HTMX expects 200 OK for headers
                headers={
                    'HX-Redirect': str(redirect_url) # Instruct HTMX to perform a client-side redirect
                }
            )
        except ProformaInvoice.DoesNotExist:
            messages.error(request, "Proforma Invoice not found.")
            return HttpResponse("Invoice not found.", status=404)
        except Exception as e:
            messages.error(request, f"An unexpected error occurred: {e}")
            return HttpResponse(f"Error: {e}", status=500)

class ProformaInvoiceDetailsPrintView(View):
    """
    Placeholder for the actual Proforma Invoice print details page.
    This view would render the detailed, print-ready layout of an invoice.
    """
    def get(self, request, pk, *args, **kwargs):
        try:
            invoice = ProformaInvoice.objects.get(id=pk)
            context = {
                'invoice': invoice,
                'print_type': request.GET.get('PT'), # Retrieve print type from query parameter
                # Add other query parameters to context as needed by the print template
                'invoice_number_param': request.GET.get('InvNo'),
                'customer_id_param': request.GET.get('cid'),
                'mod_id': request.GET.get('ModId'),
                'submod_id': request.GET.get('SubModId'),
                'key': request.GET.get('Key'),
            }
            return render(request, 'invoices/proformainvoice/print_details.html', context)
        except ProformaInvoice.DoesNotExist:
            messages.error(request, "Proforma Invoice not found for printing.")
            return HttpResponse("Invoice not found.", status=404)
        except Exception as e:
            messages.error(request, f"Failed to load print details: {e}")
            return HttpResponse(f"Error loading print details: {e}", status=500)

```

### 4.4 Templates (`invoices/templates/invoices/proformainvoice/`)

Templates are designed using DRY principles, with partials for dynamic content (table, search input, autocomplete suggestions). They leverage HTMX for seamless interactions and DataTables for advanced table features.

#### `list.html`

This is the main page template, extending `core/base.html`. It sets up the search form and the container for the dynamically loaded invoice table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-6">Proforma Invoice - Print Selection</h2>

    {# Search Form Area #}
    <div class="bg-white shadow-lg rounded-xl p-6 mb-8 border border-gray-200">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Search Invoices</h3>
        <form id="proforma-search-form" 
              hx-get="{% url 'invoices:proformainvoice_table' %}" 
              hx-target="#proformainvoiceTable-container" 
              hx-swap="innerHTML"
              hx-indicator="#loading-spinner-table"
              class="space-y-4">
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
                {# Search By Dropdown #}
                <div>
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Search By</label>
                    {{ search_form.search_by }}
                </div>
                
                {# Dynamic Search Input Container #}
                <div id="search-input-container">
                    {# This content is swapped by HTMX based on 'search_by' dropdown selection #}
                    {% comment %}
                        Initial load will include the customer name field by default,
                        as dictated by ProformaInvoiceSearchForm's __init__ method's initial logic.
                    {% endcomment %}
                    {% include 'invoices/proformainvoice/_search_input_field.html' with field=search_form.search_customer_name field_type='customer_name' %}
                </div>
                
                {# Search Button #}
                <div>
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2.5 px-4 rounded-md shadow-sm transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <i class="fas fa-search mr-2"></i> Search
                    </button>
                </div>
            </div>
            {# Autocomplete Suggestions Container (visible only when customer name input is active) #}
            <div id="customer-suggestions" class="relative z-10 w-full"></div>
        </form>
    </div>
    
    {# Proforma Invoice Table Container #}
    <div id="proformainvoiceTable-container"
         hx-trigger="load, refreshProformaInvoiceList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'invoices:proformainvoice_table' %}"
         hx-swap="innerHTML">
        {# Initial loading state for the table #}
        <div class="text-center py-12 bg-white rounded-xl shadow-md border border-gray-200">
            <div id="loading-spinner-table" class="htmx-indicator inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Proforma Invoices...</p>
        </div>
    </div>
    
    {# Modal for any future interactions (not directly used by this page's core logic) #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# DataTables JS and its TailwindCSS integration #}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.tailwindcss.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('proformaInvoicePrint', () => ({
            init() {
                // Initialize DataTables when the HTMX-loaded table content is swapped into the container.
                // This ensures DataTables correctly applies to the new DOM elements.
                htmx.on('#proformainvoiceTable-container', 'htmx:afterSwap', (evt) => {
                    const tableElement = $(evt.target).find('#proformainvoiceTable');
                    if (tableElement.length) {
                        // Destroy any existing DataTable instance before re-initializing to prevent errors
                        if ($.fn.DataTable.isDataTable(tableElement)) {
                            tableElement.DataTable().destroy();
                        }
                        tableElement.DataTable({
                            "pageLength": 20, // Matches original ASP.NET PageSize
                            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                            "responsive": true, // Make table responsive
                            "language": {
                                "emptyTable": "No data to display !", // Matches ASP.NET EmptyDataTemplate
                                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                                "infoEmpty": "Showing 0 to 0 of 0 entries",
                                "infoFiltered": "(filtered from _MAX_ total entries)",
                                "lengthMenu": "Show _MENU_ entries",
                                "loadingRecords": "Loading...",
                                "processing": "Processing...",
                                "search": "Search:",
                                "zeroRecords": "No matching records found"
                            }
                        });
                    }
                });
            },
            // This function is called from the autocomplete suggestions to populate the input field
            selectCustomer: function(fullCustomerName) {
                document.getElementById('id_search_customer_name').value = fullCustomerName;
                document.getElementById('customer-suggestions').innerHTML = ''; // Clear suggestions list
            }
        }));
    });
</script>
{% endblock %}

{% block extra_head %}
{# Required for DataTables styling using TailwindCSS and Font Awesome for icons #}
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css" rel="stylesheet">
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
{% endblock %}
```

#### `_proformainvoice_table.html`

This partial template renders only the HTML table structure and data, which is loaded dynamically into `list.html` via HTMX.

```html
<div class="bg-white shadow-lg rounded-xl overflow-hidden border border-gray-200">
    <table id="proformainvoiceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Print Type</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if invoices %}
                {% for invoice in invoices %}
                <tr>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                        <select id="printType-{{ invoice.id }}" 
                                name="print_type" 
                                class="block w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                            {# Default selected value matches ASP.NET's initial state if any #}
                            {% comment %}
                            The ASP.NET code had commented out ALL COPIES. 
                            If 'ORIGINAL FOR BUYER' is the default, it should be marked 'selected'.
                            For this example, we default to the first option as per list.
                            {% endcomment %}
                            {% for pt in print_types %}
                                <option value="{{ pt }}">{{ pt }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.get_financial_year_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.invoice_number }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.get_formatted_system_date }}</td>
                    <td class="py-3 px-4 text-sm text-gray-900">{{ invoice.get_display_customer_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.get_display_work_order_numbers }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ invoice.po_number|default:"" }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                        <button 
                            class="bg-green-600 hover:bg-green-700 text-white font-bold py-1.5 px-3 rounded text-xs inline-flex items-center justify-center transition duration-150 ease-in-out"
                            hx-get="{% url 'invoices:proforma_print' invoice.pk %}"
                            hx-include="#printType-{{ invoice.id }}" {# Includes the selected value of THIS row's dropdown #}
                            hx-swap="none" {# No swap, HTMX will handle the redirect via HX-Redirect header #}
                            hx-trigger="click">
                            <i class="fas fa-print mr-1"></i> Select
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-6 text-center text-gray-500 text-base">
                        <i class="fas fa-info-circle mr-2"></i> No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# DataTables initialization is handled by the parent list.html's Alpine.js component.
   No <script> tag needed directly in this partial to avoid re-execution issues. #}
```

#### `_search_input_field.html`

This partial dynamically renders either the customer name input field (with autocomplete) or the PO/Invoice number input field, based on the user's `search_by` selection.

```html
{# This partial is loaded into the #search-input-container by HTMX #}
{% if field_type == 'customer_name' %}
    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
    {{ field }}
{% else %}
    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">PO No / Invoice No</label>
    {{ field }}
{% endif %}
{# Loading indicator for HTMX requests on this field #}
<span id="loading-indicator" class="htmx-indicator ml-2 text-blue-500">
    <i class="fas fa-spinner fa-spin"></i>
</span>
```

#### `_customer_autocomplete_suggestions.html`

This partial displays the autocomplete suggestions for the customer name input, loaded via HTMX.

```html
{# This partial is loaded into the #customer-suggestions container by HTMX #}
{% for customer in suggestions %}
    <div class="px-4 py-2 cursor-pointer hover:bg-blue-50 transition duration-100 ease-in-out text-gray-800 border-b border-gray-100 last:border-b-0"
         onclick="Alpine.store('proformaInvoicePrint').selectCustomer('{{ customer.customer_full_name|escapejs }}');">
        {{ customer.customer_full_name }}
    </div>
{% empty %}
    {# Optional: Display a message if no suggestions, but usually better to just hide if empty #}
{% endfor %}
```

#### `print_details.html`

This is a placeholder for the actual print-ready invoice details page. Its content would depend on the specific layout and data required for printing.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-6">Proforma Invoice Print Details</h2>

    <div class="bg-white shadow-lg rounded-xl p-6 border border-gray-200">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Invoice: {{ invoice.invoice_number }} (ID: {{ invoice.id }})</h3>
        <p class="text-gray-700 mb-2"><strong>Print Type:</strong> {{ print_type }}</p>
        <p class="text-gray-700 mb-2"><strong>Customer:</strong> {{ invoice.get_display_customer_name }}</p>
        <p class="text-gray-700 mb-2"><strong>Date:</strong> {{ invoice.get_formatted_system_date }}</p>
        <p class="text-gray-700 mb-2"><strong>PO Number:</strong> {{ invoice.po_number }}</p>
        <p class="text-gray-700 mb-2"><strong>Work Order(s):</strong> {{ invoice.get_display_work_order_numbers }}</p>
        <p class="text-gray-700 mb-4"><strong>Financial Year:</strong> {{ invoice.get_financial_year_name }}</p>

        <hr class="my-6 border-gray-200">

        <h4 class="text-lg font-semibold text-gray-800 mb-3">Additional Parameters (from URL):</h4>
        <ul class="list-disc list-inside text-gray-700">
            <li>ModId: {{ mod_id }}</li>
            <li>SubModId: {{ submod_id }}</li>
            <li>Key: {{ key }}</li>
        </ul>

        <div class="mt-8 flex justify-end">
            <button onclick="window.print()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2.5 px-5 rounded-md shadow-sm transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-print mr-2"></i> Print this page
            </button>
            <a href="{% url 'invoices:proformainvoice_list' %}" class="ml-4 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2.5 px-5 rounded-md shadow-sm transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400">
                <i class="fas fa-arrow-left mr-2"></i> Back to List
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

### 4.5 URLs (`invoices/urls.py`)

This file defines the URL patterns for accessing the various views and partials in your `invoices` application.

```python
from django.urls import path
from .views import (
    ProformaInvoiceListView,
    ProformaInvoiceTablePartialView,
    ToggleSearchInputView,
    CustomerAutoCompleteView,
    ProformaInvoicePrintView,
    ProformaInvoiceDetailsPrintView,
)

app_name = 'invoices' # Define app_name for namespacing URLs

urlpatterns = [
    # Main page for Proforma Invoice print selection
    path('proforma/', ProformaInvoiceListView.as_view(), name='proformainvoice_list'),
    
    # HTMX endpoint to load/refresh the DataTables table content
    path('proforma/table/', ProformaInvoiceTablePartialView.as_view(), name='proformainvoice_table'),
    
    # HTMX endpoint to dynamically toggle search input fields
    path('proforma/toggle-search-input/', ToggleSearchInputView.as_view(), name='toggle_search_input'),
    
    # HTMX endpoint for customer name autocomplete suggestions
    path('proforma/autocomplete-customer-name/', CustomerAutoCompleteView.as_view(), name='autocomplete_customer_name'),
    
    # Endpoint to handle the 'Select' button click and initiate print redirection
    path('proforma/print/<int:pk>/', ProformaInvoicePrintView.as_view(), name='proforma_print'),
    
    # Placeholder for the actual Proforma Invoice print details page
    path('proforma/print-details/<int:pk>/', ProformaInvoiceDetailsPrintView.as_view(), name='proforma_print_details'),
]

```

### 4.6 Tests (`invoices/tests.py`)

Comprehensive unit tests for model methods and integration tests for all views ensure the functionality is correctly migrated and robust. This promotes at least 80% test coverage.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages # Used to check success/error messages
from .models import ProformaInvoice, Customer, FinancialYear, WorkOrder
from datetime import date

# Set up mock values for session variables that are expected by views
# In a real application, consider Django's authentication system for user-specific data.
DEFAULT_COMPANY_ID = 1
DEFAULT_FIN_YEAR_ID = 2023

class ProformaInvoiceModelTest(TestCase):
    """
    Unit tests for the ProformaInvoice model and its related helper models.
    Focuses on business logic encapsulated within the 'Fat Model'.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company_id = DEFAULT_COMPANY_ID
        cls.fin_year = FinancialYear.objects.create(id=DEFAULT_FIN_YEAR_ID, year_name='2023-2024')
        cls.customer1 = Customer.objects.create(id='CUST001', name='Test Customer One', company_id=cls.company_id)
        cls.customer2 = Customer.objects.create(id='CUST002', name='Another Customer', company_id=cls.company_id)
        cls.work_order1 = WorkOrder.objects.create(id=101, work_order_number='WO-2023-001', company_id=cls.company_id)
        cls.work_order2 = WorkOrder.objects.create(id=102, work_order_number='WO-2023-002', company_id=cls.company_id)

        cls.invoice1 = ProformaInvoice.objects.create(
            id=1,
            fin_year_id=cls.fin_year.id,
            system_date=date(2023, 1, 15),
            invoice_number='INV-2023-001',
            work_order_numbers=f"{cls.work_order1.id},{cls.work_order2.id},", # Mimic ASP.NET trailing comma
            po_number='PO-001',
            customer_code=cls.customer1.id,
            company_id=cls.company_id
        )
        cls.invoice2 = ProformaInvoice.objects.create(
            id=2,
            fin_year_id=cls.fin_year.id,
            system_date=date(2023, 2, 10),
            invoice_number='INV-2023-002',
            work_order_numbers=f"{cls.work_order1.id},",
            po_number='PO-002',
            customer_code=cls.customer2.id,
            company_id=cls.company_id
        )
        cls.invoice3 = ProformaInvoice.objects.create(
            id=3,
            fin_year_id=cls.fin_year.id,
            system_date=date(2023, 3, 5),
            invoice_number='INV-2023-003',
            work_order_numbers='', # Empty WO
            po_number='PO-003',
            customer_code=cls.customer1.id,
            company_id=cls.company_id
        )
        cls.invoice_future_fin_year = ProformaInvoice.objects.create(
            id=4,
            fin_year_id=DEFAULT_FIN_YEAR_ID + 1, # Future fin year
            system_date=date(2024, 1, 1),
            invoice_number='INV-2024-001',
            work_order_numbers='',
            po_number='PO-004',
            customer_code=cls.customer1.id,
            company_id=cls.company_id
        )

    def test_proforma_invoice_creation(self):
        """Verify basic ProformaInvoice object creation."""
        obj = ProformaInvoice.objects.get(id=self.invoice1.id)
        self.assertEqual(obj.invoice_number, 'INV-2023-001')
        self.assertEqual(obj.customer_code, self.customer1.id)
        self.assertEqual(obj.company_id, self.company_id)

    def test_get_display_customer_name(self):
        """Test the customer name formatting method."""
        obj = ProformaInvoice.objects.get(id=self.invoice1.id)
        self.assertEqual(obj.get_display_customer_name(), f"Test Customer One [{self.customer1.id}]")
        
        # Test for non-existent customer
        obj.customer_code = 'NONEXISTENT'
        self.assertEqual(obj.get_display_customer_name(), "N/A")

    def test_get_financial_year_name(self):
        """Test the financial year name retrieval method."""
        obj = ProformaInvoice.objects.get(id=self.invoice1.id)
        self.assertEqual(obj.get_financial_year_name(), self.fin_year.year_name)
        
        # Test for non-existent financial year
        obj.fin_year_id = 9999
        self.assertEqual(obj.get_financial_year_name(), "N/A")

    def test_get_formatted_system_date(self):
        """Test the date formatting method."""
        obj = ProformaInvoice.objects.get(id=self.invoice1.id)
        self.assertEqual(obj.get_formatted_system_date(), '15-01-2023')
        obj.system_date = None # Test with None date
        self.assertEqual(obj.get_formatted_system_date(), 'N/A')

    def test_get_display_work_order_numbers(self):
        """Test the work order parsing and retrieval method."""
        obj1 = ProformaInvoice.objects.get(id=self.invoice1.id)
        self.assertEqual(obj1.get_display_work_order_numbers(), f"{self.work_order1.work_order_number},{self.work_order2.work_order_number}")

        obj2 = ProformaInvoice.objects.get(id=self.invoice2.id)
        self.assertEqual(obj2.get_display_work_order_numbers(), self.work_order1.work_order_number)

        obj3 = ProformaInvoice.objects.get(id=self.invoice3.id)
        self.assertEqual(obj3.get_display_work_order_numbers(), "") # Empty work order string

        # Test with non-numeric content
        obj_bad_wo = ProformaInvoice.objects.create(
            id=5, fin_year_id=self.fin_year.id, system_date=date(2023, 1, 1), 
            invoice_number='INV-BAD-WO', work_order_numbers='abc,101', po_number='PO-005', 
            customer_code=self.customer1.id, company_id=self.company_id
        )
        self.assertEqual(obj_bad_wo.get_display_work_order_numbers(), self.work_order1.work_order_number)


    # Tests for the search_invoices class method
    def test_search_invoices_by_customer_name(self):
        """Test searching by customer name."""
        results = ProformaInvoice.search_invoices(
            '0', 'Test Customer One', self.company_id, self.fin_year.id
        )
        self.assertEqual(results.count(), 2) # invoice1 and invoice3 belong to Test Customer One
        self.assertIn(self.invoice1, results)
        self.assertIn(self.invoice3, results)

        # Test with partial name
        results = ProformaInvoice.search_invoices(
            '0', 'Another', self.company_id, self.fin_year.id
        )
        self.assertEqual(results.count(), 1)
        self.assertIn(self.invoice2, results)

    def test_search_invoices_by_po_no(self):
        """Test searching by PO number."""
        results = ProformaInvoice.search_invoices(
            '2', 'PO-001', self.company_id, self.fin_year.id
        )
        self.assertEqual(results.count(), 1)
        self.assertIn(self.invoice1, results)

        results = ProformaInvoice.search_invoices(
            '2', 'PO-00', self.company_id, self.fin_year.id
        )
        self.assertEqual(results.count(), 3) # PO-001, PO-002, PO-003

    def test_search_invoices_by_invoice_no(self):
        """Test searching by invoice number."""
        results = ProformaInvoice.search_invoices(
            '3', 'INV-2023-002', self.company_id, self.fin_year.id
        )
        self.assertEqual(results.count(), 1)
        self.assertIn(self.invoice2, results)

    def test_search_invoices_no_value(self):
        """Test searching with an empty search value (should return all relevant for company/year)."""
        results = ProformaInvoice.search_invoices(
            '0', '', self.company_id, self.fin_year.id
        )
        self.assertEqual(results.count(), 3) # Should return invoice1, invoice2, invoice3 (not invoice_future_fin_year)

    def test_search_invoices_fin_year_filter(self):
        """Test financial year filtering (FinYearId <= current FinYearId)."""
        results = ProformaInvoice.search_invoices(
            '0', '', self.company_id, DEFAULT_FIN_YEAR_ID
        )
        self.assertNotIn(self.invoice_future_fin_year, results)
        self.assertEqual(results.count(), 3)


class ProformaInvoiceViewsTest(TestCase):
    """
    Integration tests for the Proforma Invoice views.
    Tests HTTP responses, template usage, context data, and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all views tests
        cls.company_id = DEFAULT_COMPANY_ID
        cls.fin_year = FinancialYear.objects.create(id=DEFAULT_FIN_YEAR_ID, year_name='2023-2024')
        cls.customer1 = Customer.objects.create(id='CUST001', name='Test Customer One', company_id=cls.company_id)
        cls.work_order1 = WorkOrder.objects.create(id=101, work_order_number='WO-2023-001', company_id=cls.company_id)

        cls.invoice1 = ProformaInvoice.objects.create(
            id=1,
            fin_year_id=cls.fin_year.id,
            system_date=date(2023, 1, 15),
            invoice_number='INV-2023-001',
            work_order_numbers=f"{cls.work_order1.id},",
            po_number='PO-001',
            customer_code=cls.customer1.id,
            company_id=cls.company_id
        )
        
    def setUp(self):
        """Set up for each test method."""
        self.client = Client()
        # Set session variables required by views
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.fin_year.id
        session.save()

    def test_list_view_get(self):
        """Test the main ProformaInvoice list page."""
        response = self.client.get(reverse('invoices:proformainvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/proformainvoice/list.html')
        self.assertIn('search_form', response.context)
        # Check if the container for the HTMX-loaded table is present
        self.assertContains(response, 'id="proformainvoiceTable-container"')
        # Check for initial loading spinner
        self.assertContains(response, 'id="loading-spinner-table"')


    def test_table_partial_view_get(self):
        """Test the HTMX endpoint for the table content."""
        response = self.client.get(reverse('invoices:proformainvoice_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/proformainvoice/_proformainvoice_table.html')
        self.assertIn('invoices', response.context)
        self.assertContains(response, self.invoice1.invoice_number) # Check if invoice data is rendered

    def test_table_partial_view_search_customer(self):
        """Test search functionality through the table partial view."""
        response = self.client.get(reverse('invoices:proformainvoice_table'), 
                                   {'search_by': '0', 'search_customer_name': 'Test Customer'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.customer1.name)
        self.assertContains(response, self.invoice1.invoice_number)

    def test_table_partial_view_search_po_no(self):
        """Test search functionality for PO number."""
        response = self.client.get(reverse('invoices:proformainvoice_table'),
                                   {'search_by': '2', 'search_po_invoice_no': 'PO-001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice1.po_number)
        self.assertContains(response, self.invoice1.invoice_number)


    def test_toggle_search_input_view(self):
        """Test the HTMX endpoint that switches search input fields."""
        # Request for customer name field
        response = self.client.get(reverse('invoices:toggle_search_input'), {'search_by': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/proformainvoice/_search_input_field.html')
        self.assertContains(response, 'id="id_search_customer_name"')
        self.assertContains(response, 'Customer Name</label>')
        
        # Request for PO/Invoice no field
        response = self.client.get(reverse('invoices:toggle_search_input'), {'search_by': '2'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/proformainvoice/_search_input_field.html')
        self.assertContains(response, 'id="id_search_po_invoice_no"')
        self.assertContains(response, 'PO No / Invoice No</label>')


    def test_customer_autocomplete_view(self):
        """Test the HTMX endpoint for customer autocomplete suggestions."""
        # Simulate HTMX request for autocomplete
        response = self.client.get(reverse('invoices:autocomplete_customer_name'), {'prefixText': 'Test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/proformainvoice/_customer_autocomplete_suggestions.html')
        self.assertContains(response, 'Test Customer One [CUST001]')

        # Test with no prefix text
        response = self.client.get(reverse('invoices:autocomplete_customer_name'), {'prefixText': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode().strip(), "") # Should return empty HTML

        # Test with no matching prefix
        response = self.client.get(reverse('invoices:autocomplete_customer_name'), {'prefixText': 'XYZ'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode().strip(), "")

    def test_proforma_print_view(self):
        """Test the 'Select' button logic which triggers a redirect."""
        # Simulate HTMX request with print_type parameter
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoices:proforma_print', args=[self.invoice1.id]), 
                                   {'print_type': 'ORIGINAL FOR BUYER'}, **headers)
        
        self.assertEqual(response.status_code, 200)
        # Check for the HX-Redirect header which HTMX uses for client-side redirection
        self.assertIn('HX-Redirect', response.headers)
        self.assertTrue(response.headers['HX-Redirect'].startswith(f'/invoices/proforma/print-details/{self.invoice1.id}/?InvNo='))
        
        # Check if success message was added to the response
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), f"Redirecting to print details for Invoice {self.invoice1.invoice_number}.")
        
    def test_proforma_print_view_not_found(self):
        """Test 'Select' action for a non-existent invoice."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('invoices:proforma_print', args=[9999]), 
                                   {'print_type': 'ORIGINAL FOR BUYER'}, **headers)
        self.assertEqual(response.status_code, 404)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Proforma Invoice not found.")
        
    def test_proforma_details_print_view(self):
        """Test the placeholder print details view."""
        response = self.client.get(reverse('invoices:proforma_print_details', args=[self.invoice1.id]),
                                   {'PT': 'ORIGINAL FOR BUYER', 'InvNo': 'INV-2023-001', 'cid': 'CUST001', 'ModId': '11', 'SubModId': '104', 'Key': 'RANDOMKEY'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoices/proformainvoice/print_details.html')
        self.assertIn('invoice', response.context)
        self.assertIn('print_type', response.context)
        self.assertEqual(response.context['invoice'].id, self.invoice1.id)
        self.assertEqual(response.context['print_type'], 'ORIGINAL FOR BUYER')
        self.assertEqual(response.context['mod_id'], '11')
        self.assertContains(response, 'Proforma Invoice Print Details')

```

## Step 5: HTMX and Alpine.js Integration

The core of this modernization relies on HTMX for all dynamic interactions, replacing traditional JavaScript AJAX and full page postbacks. Alpine.js is integrated for client-side UI state management, though for this specific module, its primary role is to initialize DataTables on HTMX content swap.

-   **Search Form Submission:** The search form (`proforma-search-form`) uses `hx-get` to submit its data to `{% url 'invoices:proformainvoice_table' %}`. This request fetches only the updated table HTML, which is then swapped into the `proformainvoiceTable-container`.
-   **Dynamic Search Input:** The `search_by` dropdown utilizes `hx-get` to call `{% url 'invoices:toggle_search_input' %}`. This endpoint returns a partial HTML snippet (`_search_input_field.html`) containing either the customer name input or the PO/Invoice number input, which is then `hx-swap`ped into `#search-input-container`.
-   **Customer Autocomplete:** The `search_customer_name` input uses `hx-get` to `{% url 'invoices:autocomplete_customer_name' %}` with a `keyup changed delay:500ms` trigger. The returned HTML suggestions (`_customer_autocomplete_suggestions.html`) are swapped into `#customer-suggestions`. An `onclick` attribute on each suggestion uses Alpine.js (via `Alpine.store`) to populate the input field and clear suggestions, demonstrating seamless interaction.
-   **DataTables Initialization:** The DataTables library is initialized within the `list.html`'s `Alpine.data` component. A crucial `htmx:afterSwap` event listener ensures that DataTables is correctly re-initialized every time the `proformainvoiceTable-container` content is updated by an HTMX request (e.g., after a search or initial load). This maintains DataTables' client-side sorting, filtering, and pagination capabilities.
-   **"Select" Action:** The "Select" button in each table row uses `hx-get` to call `{% url 'invoices:proforma_print' invoice.pk %}`. It dynamically includes the selected `print_type` from that row's dropdown using `hx-include="#printType-{{ invoice.id }}"`. Instead of a full page redirect, the Django view returns an `HX-Redirect` header, prompting the browser to perform the navigation client-side, making the interaction feel faster and smoother.
-   **Loading Indicators:** `htmx-indicator` classes are used with `hx-indicator` attributes to show visual feedback (spinners) during HTMX requests, improving user experience.

This comprehensive plan, with its clear breakdown and runnable code examples, provides a robust foundation for an automated and efficient migration from legacy ASP.NET to a modern Django/HTMX/Alpine.js stack.

## Final Notes

-   **Placeholders:** Remember to replace `DEFAULT_COMPANY_ID` and `DEFAULT_FIN_YEAR_ID` in `views.py` and `tests.py` with actual logic to retrieve these from the authenticated user's session or profile in a production environment.
-   **Database Routing:** For Django to connect to your existing ASP.NET database tables (which are typically SQL Server), you'll need to configure a database router in your Django project's `settings.py` and potentially use a `django-mssql-backend` or similar library.
-   **CSS Integration:** Tailwind CSS classes are extensively used for styling. Ensure Tailwind CSS is properly configured in your Django project.
-   **Error Handling:** The `try-except` blocks in the models and views capture exceptions, providing `N/A` or error messages as appropriate. In a production system, these should be logged comprehensively and potentially presented to the user more gracefully.
-   **Security:** Django's ORM inherently protects against SQL injection, but always ensure proper input validation and user authentication/authorization are in place.
-   **Refinement:** After this automated migration, further refinement can include transforming `CharField` fields like `fin_year_id`, `customer_code`, and parsing `work_order_numbers` into proper `ForeignKey` relationships in a new Django database if you choose to migrate the data itself. This would make the models even cleaner and leverage Django's ORM capabilities more fully.