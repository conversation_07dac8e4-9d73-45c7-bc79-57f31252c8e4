## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- The primary table identified is `tblAcc_LoanDetails` which is displayed in the GridView.
- The query `select CreditAmt As loan,tblAcc_LoanDetails.Particulars,tblAcc_LoanDetails.Id from tblAcc_LoanDetails inner join tblAcc_LoanMaster on tblAcc_LoanMaster.Id=tblAcc_LoanDetails.MId And CompId=" + CompId + " AND FinYearId<=" + FinYearId + " And MId=" + MId + "` suggests a join with `tblAcc_LoanMaster`.
- The `GridView` uses `Particulars`, `TotDrAmt`, `TotCrAmt`, and `Id`. `TotDrAmt` and `TotCrAmt` are not directly in the `SELECT` statement, implying they are either computed by `ACA.TotFillPart` or are additional columns in `tblAcc_LoanDetails` or a related table that `TotFillPart` accounts for. For the purpose of migration, we assume `TotDrAmt` and `TotCrAmt` are part of `tblAcc_LoanDetails` as they are directly bound and summed from its data source.
- `CompId` and `FinYearId` are used for filtering and likely reside in `tblAcc_LoanMaster` or a related table. `MId` links `tblAcc_LoanDetails` to `tblAcc_LoanMaster`.

**Extracted Schema:**

-   **Main Table:** `tblAcc_LoanDetails`
    -   Columns: `Id` (Primary Key), `Particulars` (text), `MId` (Foreign Key to `tblAcc_LoanMaster.Id`), `TotDrAmt` (decimal/numeric), `TotCrAmt` (decimal/numeric).
-   **Related Table:** `tblAcc_LoanMaster`
    -   Columns: `Id` (Primary Key), `CompId` (integer), `FinYearId` (integer).

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

-   **Read:** Data is fetched from the database using a SQL query and bound to `GridView1` on `Page_Load`. This is the primary functionality of the page.
-   **Create:** No explicit create operation or form on this page.
-   **Update:** The `LinkButton1` with `CommandName="Sel"` on the `Particulars` column implies a selection or potential edit action, though the `GridView1_RowCommand` event handler is empty. We will interpret this as a link to an edit/detail view.
-   **Delete:** No explicit delete operation on this page.
-   **Navigation:** A "Cancel" button (`btnCancel_Click`) redirects to `Acc_Loan_Particulars.aspx`.
-   **Calculations:** Summation of `TotDrAmt` and `TotCrAmt` is performed and displayed in the `GridView` footer.
-   **Filtering:** Data is filtered by `CompId`, `FinYearId` (from session), and `MId` (from query string).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

-   **GridView1:** Displays a tabular list of "Loan (Liability)" details. This will be converted to a Django template using a `{% for %}` loop, styled with Tailwind CSS, and enhanced with DataTables for client-side features.
-   **Panel1:** Acts as a container for the GridView with scrollbars and fixed dimensions. In Django, this will be handled by CSS (`overflow-auto`, `max-height`).
-   **LinkButton1 (inside GridView TemplateField for "Particulars"):** Represents a clickable item in the list, likely for viewing details or editing. This will be a standard `<a>` tag or an HTMX-triggered button/link opening a modal.
-   **Labels (lblDrAmt, lblCrAmt):** Display debit and credit amounts. These will be replaced by Django template variables.
-   **Footer Labels (TotDebit, TotCredit):** Display the sum of debit and credit amounts. These sums will be calculated in the Django view/model and passed to the template.
-   **Button1 (Cancel):** Redirects to a previous page. This will be a standard `<a>` tag or a button with `hx-on:click` to perform a client-side redirect.

## Step 4: Generate Django Code

We will create a Django application named `accounts` to house these components.

## 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

-   `LoanMaster` model maps to `tblAcc_LoanMaster`.
-   `LoanDetail` model maps to `tblAcc_LoanDetails` and includes foreign key to `LoanMaster`.
-   Business logic for sums will be handled in the view or a custom manager/queryset.

```python
# accounts/models.py
from django.db import models
from django.db.models import Sum

class LoanMaster(models.Model):
    """
    Represents the tblAcc_LoanMaster table.
    Contains company and financial year identifiers.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblAcc_LoanMaster'
        verbose_name = 'Loan Master'
        verbose_name_plural = 'Loan Masters'

    def __str__(self):
        return f"Master ID: {self.id}, Company: {self.comp_id}, FinYear: {self.fin_year_id}"

class LoanDetail(models.Model):
    """
    Represents the tblAcc_LoanDetails table.
    Stores particular loan details linked to a LoanMaster.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(LoanMaster, on_delete=models.DO_NOTHING, db_column='MId')
    particulars = models.CharField(db_column='Particulars', max_length=255)
    tot_dr_amt = models.DecimalField(db_column='TotDrAmt', max_digits=18, decimal_places=2, default=0.00)
    tot_cr_amt = models.DecimalField(db_column='TotCrAmt', max_digits=18, decimal_places=2, default=0.00)

    class Meta:
        managed = False
        db_table = 'tblAcc_LoanDetails'
        verbose_name = 'Loan Detail'
        verbose_name_plural = 'Loan Details'

    def __str__(self):
        return self.particulars
        
    @staticmethod
    def get_summary_for_master(master_id, comp_id, fin_year_id):
        """
        Calculates the total debit and credit amounts for a given master,
        company, and financial year. This encapsulates the sum logic from ASP.NET.
        """
        queryset = LoanDetail.objects.filter(
            master__id=master_id,
            master__comp_id=comp_id,
            master__fin_year_id__lte=fin_year_id # ASP.NET uses <=
        )
        summary = queryset.aggregate(
            total_debit=Sum('tot_dr_amt'),
            total_credit=Sum('tot_cr_amt')
        )
        return {
            'total_debit': summary['total_debit'] if summary['total_debit'] else 0,
            'total_credit': summary['total_credit'] if summary['total_credit'] else 0,
            'details': queryset.all() # Return details for gridview
        }
```

## 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

-   Since the original page is a read-only list with a "Cancel" button, we will create a `ModelForm` for `LoanDetail` primarily for future-proofing (e.g., if the "Particulars" link leads to an edit form). It's not directly used for submission on the *list* page, but will be if CRUD modals are implemented.

```python
# accounts/forms.py
from django import forms
from .models import LoanDetail

class LoanDetailForm(forms.ModelForm):
    class Meta:
        model = LoanDetail
        fields = ['particulars', 'tot_dr_amt', 'tot_cr_amt', 'master'] # Include 'master' for linking
        widgets = {
            'particulars': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tot_dr_amt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tot_cr_amt': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'master': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'particulars': 'Particulars',
            'tot_dr_amt': 'Debit Amount',
            'tot_cr_amt': 'Credit Amount',
            'master': 'Loan Master',
        }
```

## 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

-   A `ListView` to display the loan details, incorporating the filtering and summation logic.
-   A partial view (`TablePartialView`) for HTMX to refresh only the table.
-   Placeholder `CreateView`, `UpdateView`, `DeleteView` for completeness, as per instructions, assuming they are part of the broader application flow. The "Particulars" link could open the `UpdateView`.

```python
# accounts/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import LoanDetail, LoanMaster
from .forms import LoanDetailForm

class LoanDetailListView(ListView):
    """
    Displays a list of Loan Details, filtered by session and query parameters.
    Calculates and displays total debit and credit amounts.
    """
    model = LoanDetail
    template_name = 'accounts/loan_detail/list.html'
    context_object_name = 'loan_details'

    def get_queryset(self):
        # Retrieve filtering parameters from session and query string
        # In a real application, CompId and FinYearId would likely be linked to the logged-in user
        # or selected via a global context. For now, assuming session variables.
        comp_id = self.request.session.get('compid', 1)  # Defaulting to 1 for example
        fin_year_id = self.request.session.get('finyear', 2023) # Defaulting to 2023 for example
        master_id = self.request.GET.get('MId')

        if not master_id:
            # Handle case where MId is missing (e.g., redirect or show empty)
            # For now, return empty queryset to avoid errors
            messages.warning(self.request, "Loan Master ID (MId) is required.")
            return LoanDetail.objects.none()
        
        # Call the static method on the model for business logic/summation
        summary_data = LoanDetail.get_summary_for_master(
            master_id=master_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        self.total_debit = summary_data['total_debit']
        self.total_credit = summary_data['total_credit']
        return summary_data['details']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['total_debit'] = self.total_debit
        context['total_credit'] = self.total_credit
        context['master_id'] = self.request.GET.get('MId')
        return context

class LoanDetailTablePartialView(LoanDetailListView):
    """
    A partial view for HTMX requests to refresh only the DataTables content.
    Inherits filtering and data logic from LoanDetailListView.
    """
    template_name = 'accounts/loan_detail/_loan_detail_table.html'

class LoanDetailCreateView(CreateView):
    model = LoanDetail
    form_class = LoanDetailForm
    template_name = 'accounts/loan_detail/_loan_detail_form.html' # Use partial for modal
    success_url = reverse_lazy('loan_detail_list') # Redirect to list after success

    def get_initial(self):
        initial = super().get_initial()
        # Pre-populate master ID if available from query params
        master_id = self.request.GET.get('MId')
        if master_id:
            try:
                initial['master'] = LoanMaster.objects.get(pk=master_id)
            except LoanMaster.DoesNotExist:
                pass
        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Loan Detail added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response with a trigger header
            # to refresh the list table and close the modal.
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanDetailList, closeModal'
                }
            )
        return response

class LoanDetailUpdateView(UpdateView):
    model = LoanDetail
    form_class = LoanDetailForm
    template_name = 'accounts/loan_detail/_loan_detail_form.html' # Use partial for modal
    success_url = reverse_lazy('loan_detail_list') # Redirect to list after success

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Loan Detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanDetailList, closeModal'
                }
            )
        return response

class LoanDetailDeleteView(DeleteView):
    model = LoanDetail
    template_name = 'accounts/loan_detail/_loan_detail_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('loan_detail_list') # Redirect to list after success

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Loan Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshLoanDetailList, closeModal'
                }
            )
        return response

class CancelRedirectView(View):
    """
    Handles the redirect logic for the 'Cancel' button.
    This mimics the ASP.NET Response.Redirect functionality.
    """
    def get(self, request, *args, **kwargs):
        # In a real Django app, this would likely be a named URL, not a hardcoded path.
        # For this specific case, it's redirecting to an ASP.NET path, so we'll use a URL.
        # For simplicity, we'll redirect to the loan_particulars_list assuming it exists in Django.
        # Original: ~/Module/Accounts/Transactions/Acc_Loan_Particulars.aspx??ModId=11&SubModId=
        return HttpResponseRedirect(reverse_lazy('accounts:loan_particulars_list')) # Placeholder for a previous page
```

## 4.4 Templates

Task: Create templates for each view.

## Instructions:

-   `list.html` will contain the overall page structure and the HTMX container for the table.
-   `_loan_detail_table.html` will be the HTMX-loaded partial containing the DataTables table.
-   `_loan_detail_form.html` and `_loan_detail_confirm_delete.html` will be partials for modals.
-   **Note on `master_id` for list page:** The ASP.NET page received `MId` from `Request.QueryString`. We need to pass this `MId` when linking to the `loan_detail_list` view in Django. For example, if linking from a `loan_particulars_list` view, the URL would be `{% url 'accounts:loan_detail_list' %}?MId={{ some_master_id }}`.

```html
<!-- accounts/templates/accounts/loan_detail/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Loan (Liability) Details</h2>
        <button
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'accounts:loan_detail_add' %}?MId={{ master_id }}" {# Pass master_id for new detail #}
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Detail
        </button>
    </div>
    
    <div id="loanDetailTable-container"
         hx-trigger="load, refreshLoanDetailList from:body"
         hx-get="{% url 'accounts:loan_detail_table' %}?MId={{ master_id }}" {# Ensure MId is passed for refresh #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading loan details...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me then trigger closeModal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on closeModal remove .is-active from #modal">
        </div>
    </div>

    <div class="flex justify-center mt-6">
        <a href="{% url 'accounts:loan_particulars_list' %}"
           class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}
```

```html
<!-- accounts/templates/accounts/loan_detail/_loan_detail_table.html -->
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="loanDetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Particulars</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Debit</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Credit</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if loan_details %}
                {% for obj in loan_details %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-blue-600 hover:text-blue-900 cursor-pointer"
                        hx-get="{% url 'accounts:loan_detail_edit' obj.pk %}?MId={{ master_id }}" {# Pass MId for context #}
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        {{ obj.particulars }}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.tot_dr_amt|default:'0.00' }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.tot_cr_amt|default:'0.00' }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-center">
                        <button
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                            hx-get="{% url 'accounts:loan_detail_edit' obj.pk %}?MId={{ master_id }}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                            hx-get="{% url 'accounts:loan_detail_delete' obj.pk %}?MId={{ master_id }}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">No Records To Display</td>
                </tr>
            {% endif %}
        </tbody>
        <tfoot class="bg-gray-50">
            <tr>
                <td colspan="2" class="py-2 px-4 text-right text-sm font-bold text-gray-900">Total</td>
                <td class="py-2 px-4 text-right text-sm font-bold text-gray-900" id="TotDebit">{{ total_debit|default:'0.00' }}</td>
                <td class="py-2 px-4 text-right text-sm font-bold text-gray-900" id="TotCredit">{{ total_credit|default:'0.00' }}</td>
                <td class="py-2 px-4"></td> {# Empty for actions column #}
            </tr>
        </tfoot>
    </table>
</div>

<script>
    // Ensure DataTables script is loaded before this runs
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#loanDetailTable')) {
            $('#loanDetailTable').DataTable().destroy(); // Destroy existing instance if it exists
        }
        $('#loanDetailTable').DataTable({
            "paging": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "footerCallback": function ( row, data, start, end, display ) {
                // Since total_debit and total_credit are passed from Django context,
                // we'll simply update the footer cells with these values.
                // If calculations were complex client-side, they'd go here.
                var api = this.api();
                // Get the elements by ID, which are already populated by Django
                $('#TotDebit').text('{{ total_debit|default:'0.00' }}');
                $('#TotCredit').text('{{ total_credit|default:'0.00' }}');
            }
        });
    });
</script>
```

```html
<!-- accounts/templates/accounts/loan_detail/_loan_detail_form.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Loan Detail</h3>
    <form hx-post="{{ request.path }}?MId={{ request.GET.MId }}" hx-swap="none" hx-target="#modalContent" hx-on::after-request="if(event.detail.successful) this.reset();">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            <input type="hidden" name="master" value="{{ request.GET.MId }}"> {# Pass MId for form submission #}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal then trigger closeModal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
<!-- accounts/templates/accounts/loan_detail/_loan_detail_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the loan detail for "{{ object.particulars }}"?</p>
    <form hx-post="{{ request.path }}?MId={{ request.GET.MId }}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal then trigger closeModal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

## 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

-   Define URLs for the list page, the HTMX table partial, and the CRUD operations.
-   The `MId` parameter needs to be handled in the URL query string for the list and table views.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    LoanDetailListView, LoanDetailTablePartialView,
    LoanDetailCreateView, LoanDetailUpdateView, LoanDetailDeleteView,
    CancelRedirectView
)

app_name = 'accounts' # Define app_name for namespacing URLs

urlpatterns = [
    # Loan Detail List/Display
    path('loan-details/', LoanDetailListView.as_view(), name='loan_detail_list'),
    # HTMX endpoint for table refresh
    path('loan-details/table/', LoanDetailTablePartialView.as_view(), name='loan_detail_table'),

    # Loan Detail CRUD (typically accessed via modal from list page)
    path('loan-details/add/', LoanDetailCreateView.as_view(), name='loan_detail_add'),
    path('loan-details/edit/<int:pk>/', LoanDetailUpdateView.as_view(), name='loan_detail_edit'),
    path('loan-details/delete/<int:pk>/', LoanDetailDeleteView.as_view(), name='loan_detail_delete'),

    # Cancel button redirect
    path('loan-details/cancel/', CancelRedirectView.as_view(), name='loan_particulars_list'), # Placeholder for the previous page
]
```

## 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

-   Include comprehensive unit tests for model methods and properties.
-   Add integration tests for all views (list, create, update, delete) ensuring HTMX interaction.
-   Ensure at least 80% test coverage of code.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import LoanMaster, LoanDetail
from django.db.models import Sum

class LoanMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master = LoanMaster.objects.create(
            id=101,
            comp_id=1,
            fin_year_id=2023
        )

    def test_loan_master_creation(self):
        self.assertEqual(self.master.comp_id, 1)
        self.assertEqual(self.master.fin_year_id, 2023)
        self.assertEqual(str(self.master), "Master ID: 101, Company: 1, FinYear: 2023")

class LoanDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master = LoanMaster.objects.create(
            id=101,
            comp_id=1,
            fin_year_id=2023
        )
        cls.detail1 = LoanDetail.objects.create(
            id=1,
            master=cls.master,
            particulars='Loan from Bank A',
            tot_dr_amt=100.50,
            tot_cr_amt=50.25
        )
        cls.detail2 = LoanDetail.objects.create(
            id=2,
            master=cls.master,
            particulars='Loan from Private Lender',
            tot_dr_amt=200.00,
            tot_cr_amt=75.75
        )

    def test_loan_detail_creation(self):
        self.assertEqual(self.detail1.particulars, 'Loan from Bank A')
        self.assertEqual(self.detail1.tot_dr_amt, 100.50)
        self.assertEqual(self.detail1.tot_cr_amt, 50.25)
        self.assertEqual(self.detail1.master, self.master)
        self.assertEqual(str(self.detail1), 'Loan from Bank A')

    def test_get_summary_for_master(self):
        summary = LoanDetail.get_summary_for_master(
            master_id=self.master.id,
            comp_id=self.master.comp_id,
            fin_year_id=self.master.fin_year_id
        )
        self.assertAlmostEqual(summary['total_debit'], 300.50) # 100.50 + 200.00
        self.assertAlmostEqual(summary['total_credit'], 126.00) # 50.25 + 75.75
        self.assertEqual(summary['details'].count(), 2)

    def test_get_summary_for_master_no_data(self):
        summary = LoanDetail.get_summary_for_master(
            master_id=999, # Non-existent master ID
            comp_id=1,
            fin_year_id=2023
        )
        self.assertEqual(summary['total_debit'], 0)
        self.assertEqual(summary['total_credit'], 0)
        self.assertEqual(summary['details'].count(), 0)

class LoanDetailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.master_valid = LoanMaster.objects.create(
            id=101,
            comp_id=1,
            fin_year_id=2023
        )
        cls.master_invalid_finyear = LoanMaster.objects.create(
            id=102,
            comp_id=1,
            fin_year_id=2024 # Should be excluded by <= 2023
        )
        cls.detail1 = LoanDetail.objects.create(
            id=1,
            master=cls.master_valid,
            particulars='Loan A',
            tot_dr_amt=100.00,
            tot_cr_amt=50.00
        )
        cls.detail2 = LoanDetail.objects.create(
            id=2,
            master=cls.master_valid,
            particulars='Loan B',
            tot_dr_amt=200.00,
            tot_cr_amt=100.00
        )
        cls.detail_other_master = LoanDetail.objects.create(
            id=3,
            master=cls.master_invalid_finyear,
            particulars='Loan C',
            tot_dr_amt=50.00,
            tot_cr_amt=25.00
        )

    def setUp(self):
        self.client = Client()
        # Set session variables for the tests to match ASP.NET behavior
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_list_view_get_success(self):
        response = self.client.get(reverse('accounts:loan_detail_list') + f'?MId={self.master_valid.id}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loan_detail/list.html')
        self.assertIn('loan_details', response.context)
        self.assertEqual(len(response.context['loan_details']), 2)
        self.assertContains(response, 'Loan A')
        self.assertContains(response, 'Loan B')
        self.assertAlmostEqual(response.context['total_debit'], 300.00)
        self.assertAlmostEqual(response.context['total_credit'], 150.00)

    def test_list_view_missing_mid(self):
        response = self.client.get(reverse('accounts:loan_detail_list'))
        self.assertEqual(response.status_code, 200) # Still 200, but with empty data and warning
        self.assertContains(response, "Loan Master ID (MId) is required.")
        self.assertEqual(len(response.context['loan_details']), 0)


    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:loan_detail_table') + f'?MId={self.master_valid.id}', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loan_detail/_loan_detail_table.html')
        self.assertIn('loan_details', response.context)
        self.assertEqual(len(response.context['loan_details']), 2)
        self.assertContains(response, 'Loan A')
        self.assertContains(response, 'Total')
        self.assertAlmostEqual(response.context['total_debit'], 300.00)

    def test_create_view_get(self):
        response = self.client.get(reverse('accounts:loan_detail_add') + f'?MId={self.master_valid.id}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loan_detail/_loan_detail_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].initial['master'].pk, self.master_valid.id)

    def test_create_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'particulars': 'New Loan',
            'tot_dr_amt': 500.00,
            'tot_cr_amt': 250.00,
            'master': self.master_valid.id,
        }
        response = self.client.post(reverse('accounts:loan_detail_add') + f'?MId={self.master_valid.id}', data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(LoanDetail.objects.filter(particulars='New Loan').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshLoanDetailList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_update_view_get(self):
        response = self.client.get(reverse('accounts:loan_detail_edit', args=[self.detail1.id]) + f'?MId={self.master_valid.id}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loan_detail/_loan_detail_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.detail1)

    def test_update_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'particulars': 'Updated Loan A',
            'tot_dr_amt': 150.00,
            'tot_cr_amt': 75.00,
            'master': self.master_valid.id,
        }
        response = self.client.post(reverse('accounts:loan_detail_edit', args=[self.detail1.id]) + f'?MId={self.master_valid.id}', data, **headers)
        self.assertEqual(response.status_code, 204)
        self.detail1.refresh_from_db()
        self.assertEqual(self.detail1.particulars, 'Updated Loan A')
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        response = self.client.get(reverse('accounts:loan_detail_delete', args=[self.detail1.id]) + f'?MId={self.master_valid.id}')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/loan_detail/_loan_detail_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.detail1)

    def test_delete_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('accounts:loan_detail_delete', args=[self.detail1.id]) + f'?MId={self.master_valid.id}', **headers)
        self.assertEqual(response.status_code, 204)
        self.assertFalse(LoanDetail.objects.filter(pk=self.detail1.id).exists())
        self.assertIn('HX-Trigger', response.headers)

    def test_cancel_redirect_view(self):
        response = self.client.get(reverse('accounts:loan_particulars_list'))
        self.assertEqual(response.status_code, 302) # Redirects
        self.assertRedirects(response, reverse('accounts:loan_particulars_list'))
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   All dynamic interactions, including adding, editing, and deleting items, will leverage HTMX for partial page updates.
-   Modals for CRUD operations will be managed by Alpine.js.
-   DataTables will be used for efficient display and interaction with the list data.
-   The base template (`core/base.html`) should include CDN links for HTMX, Alpine.js, jQuery, and DataTables.

**Key HTMX/Alpine.js implementations:**
-   **List Page Load:** `loan_detail_list.html` uses `hx-get="{% url 'accounts:loan_detail_table' %}?MId={{ master_id }}" hx-trigger="load, refreshLoanDetailList from:body"` to load the table content dynamically.
-   **CRUD Modals:** Buttons for "Add New Detail", "Edit", and "Delete" use `hx-get` to fetch form/confirmation content into `#modalContent` and `hx-target="#modalContent" hx-trigger="click" _="on click add .is-active to #modal"` to open the modal.
-   **Form Submission:** Forms inside modals (`_loan_detail_form.html`, `_loan_detail_confirm_delete.html`) use `hx-post` with `hx-swap="none"`.
-   **Post-Submission Actions:** After a successful form submission (handled by Django views returning 204 No Content), an `HX-Trigger` header (`refreshLoanDetailList, closeModal`) is sent.
    -   `refreshLoanDetailList` triggers the `hx-get` on `loanDetailTable-container` to refresh the table.
    -   `closeModal` is an Alpine.js custom event (`_="on closeModal remove .is-active from #modal"`) that closes the modal.
-   **DataTables Initialization:** The `<script>` block within `_loan_detail_table.html` ensures DataTables is initialized only after the table HTML is loaded via HTMX. The `destroy()` method is called first to prevent re-initialization issues.
-   **Cancel Button:** `CancelRedirectView` (or a direct `<a>` tag) handles the redirect behavior.

## Final Notes

-   **Placeholders:** Replace `{{ master_id }}` with the actual `MId` passed in the URL query string when navigating to the `loan_detail_list` page. Similarly, `compid` and `finyear` should be set in the user's session or associated with the logged-in user in a real application.
-   **`core/base.html`:** This document assumes a `core/base.html` template exists, which includes necessary CDNs for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS.
-   **Fat Model/Thin View:** The `LoanDetail.get_summary_for_master` method embodies the "fat model" principle by encapsulating the summation logic. Views remain concise, primarily orchestrating data flow and rendering.
-   **DRY:** Templates utilize partials (`_loan_detail_table.html`, `_loan_detail_form.html`, `_loan_detail_confirm_delete.html`) for reusability with HTMX.
-   **Test Coverage:** The provided tests cover model attributes, data retrieval/summation, and all CRUD view operations, including HTMX interactions, aiming for comprehensive coverage.