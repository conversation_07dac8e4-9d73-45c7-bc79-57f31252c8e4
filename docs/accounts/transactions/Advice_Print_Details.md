## ASP.NET to Django Conversion Script: `Advice_Print_Details` Page

This plan outlines the modernization of your ASP.NET `Advice_Print_Details.aspx` page to a modern Django-based solution. The original page serves primarily as a report viewer, integrating with Crystal Reports to display financial advice details. Our strategy will focus on recreating this reporting capability within Django, emphasizing data integrity, clear separation of concerns, and a highly maintainable architecture using Django's best practices.

### Business Value of Django Modernization:

Migrating to Django offers significant benefits beyond just technical updates:

1.  **Reduced Licensing Costs:** Eliminates reliance on proprietary reporting tools like Crystal Reports and commercial SQL Server licenses, leading to substantial savings.
2.  **Improved Maintainability & Scalability:** Django's structured, open-source framework simplifies code maintenance and allows for easier scaling as your business grows, reducing long-term operational costs.
3.  **Enhanced User Experience:** Modern frontend technologies (HTMX, Alpine.js) enable dynamic, responsive interfaces without complex JavaScript, offering a smoother and faster experience for your users.
4.  **Future-Proofing:** Moving to a widely adopted, community-driven framework ensures your application remains relevant and adaptable to future technological advancements.
5.  **Simplified Reporting:** Replacing Crystal Reports with integrated Python-based reporting means all your business logic, including report generation, lives within a single, consistent codebase, making changes and new reports easier to develop.

### Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database tables to compile the report data. We will define Django models that map directly to these existing tables, ensuring data consistency with your current database.

**Identified Tables and Inferred Columns:**

*   **`tblACC_Advice_Payment_Master`**: This is the primary table for payment advice information.
    *   `Id` (Primary Key)
    *   `CompId` (Company ID)
    *   `FinYearId` (Financial Year ID)
    *   `ADNo` (Advice Number)
    *   `Type` (Integer representing payment type, e.g., Proforma, Bill Booking)
    *   `ECSType` (Integer representing ECS type, e.g., Employee, Customer, Supplier)
    *   `PayTo` (Code identifying the payee, used with `ECSType`)
    *   `ChequeDate` (Date of cheque)
    *   `ChequeNo` (Cheque number)
    *   `SysDate` (System entry date)
*   **`tblACC_Advice_Payment_Details`**: Contains line-item details for each payment advice.
    *   `Id` (Primary Key)
    *   `MId` (Foreign Key to `tblACC_Advice_Payment_Master.Id`)
    *   `ProformaInvNo` (Proforma Invoice Number, if `Type` is relevant)
    *   `Amount` (Amount for the detail line)
    *   `BillBookingMasterId` (Inferred: ID linking to `tblACC_BillBooking_Master` when `Type` is 4. The original code's `DS1.Tables[0].Rows[i][0]` is ambiguously used here; we'll assume a proper FK field for clarity and correctness.)
*   **`tblACC_BillBooking_Master`**: Contains bill booking details, specifically the bill number.
    *   `Id` (Primary Key)
    *   `BillNo` (Bill Number)
*   **Auxiliary Tables (Inferred from `fun.ECSNames`, `fun.ECSAddress`, `fun.CompAdd`):**
    *   **`tblCompany`**: Contains company information, including address.
        *   `Id` (Primary Key)
        *   `Name`
        *   `Address`
    *   **`tblEmployee` / `tblCustomer` / `tblSupplier`**: These tables likely hold details for different payee types.
        *   `Id` (Primary Key)
        *   `Code` (Used as `PayTo`)
        *   `Name`
        *   `Address`

### Step 2: Identify Backend Functionality

The ASP.NET page is a **read-only report viewer**. It performs complex data retrieval and transformation but no direct Create, Update, or Delete (CRUD) operations on the underlying data.

*   **Read Operation:** The primary function is to query `tblACC_Advice_Payment_Master`, join with `tblACC_Advice_Payment_Details`, and conditionally retrieve information from `tblACC_BillBooking_Master` and other auxiliary tables (`tblCompany`, `tblEmployee`, `tblCustomer`, `tblSupplier`) based on `Type` and `ECSType` fields. All the conditional logic in the C# code-behind to construct the final `DataTable` will be replicated in Django model methods or custom managers.

### Step 3: Infer UI Components

The ASP.NET page uses `CrystalReportViewer` components. In Django, this will be replaced by:

*   **HTML Table:** A dynamic HTML table, enhanced with DataTables.js, will display the generated report data.
*   **"Cancel" Button:** This will be a standard Django link or button, potentially utilizing HTMX for a smoother navigation back to a list of advice entries.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `accounts`.

#### 4.1 Models (`accounts/models.py`)

We will define Django models for each identified database table. These models will adhere to the `managed = False` and `db_table` settings to connect to your existing database. Crucially, the complex data processing logic from the C# code will be encapsulated within a custom manager for `AdvicePaymentMaster`, embodying the "fat model" principle.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# --- Auxiliary Models (inferred lookups) ---

class Company(models.Model):
    """
    Maps to tblCompany for company details like address.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany' # Adjust if actual table name is different
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class Employee(models.Model):
    """
    Maps to tblEmployee for employee payee details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    code = models.CharField(db_column='Code', max_length=50, unique=True)
    name = models.CharField(db_column='Name', max_length=255)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblEmployee' # Adjust if actual table name is different
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.name} ({self.code})"

class Customer(models.Model):
    """
    Maps to tblCustomer for customer payee details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    code = models.CharField(db_column='Code', max_length=50, unique=True)
    name = models.CharField(db_column='Name', max_length=255)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCustomer' # Adjust if actual table name is different
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.name} ({self.code})"

class Supplier(models.Model):
    """
    Maps to tblSupplier for supplier payee details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    code = models.CharField(db_column='Code', max_length=50, unique=True)
    name = models.CharField(db_column='Name', max_length=255)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSupplier' # Adjust if actual table name is different
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.name} ({self.code})"

# --- Core Reporting Models ---

class BillBookingMaster(models.Model):
    """
    Maps to tblACC_BillBooking_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    bill_no = models.CharField(db_column='BillNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking Master'
        verbose_name_plural = 'Bill Booking Masters'

    def __str__(self):
        return self.bill_no or f"Bill Booking {self.id}"

class AdvicePaymentMasterManager(models.Manager):
    def get_advice_print_details(self, advice_id, company_id, financial_year_id):
        """
        Replicates the complex data retrieval and transformation logic
        from the ASP.NET Page_Init method to generate the report data.
        Returns a list of dictionaries, each representing a row in the final report.
        """
        try:
            master_entry = self.get(
                id=advice_id,
                compid=company_id,
                finyearid__lte=financial_year_id # Using __lte as per original 'FinYearId<='
            )
        except AdvicePaymentMaster.DoesNotExist:
            return [] # No master record found

        details = master_entry.advicepaymentdetail_set.all()
        
        report_data = []

        for detail_row in details:
            paid_to = ""
            address = ""
            
            # Replicate fun.ECSNames and fun.ECSAddress logic
            if master_entry.ecs_type == 1: # Employee
                try:
                    payee = Employee.objects.get(code=master_entry.pay_to_code)
                    paid_to = payee.name
                    address = payee.address
                except Employee.DoesNotExist:
                    pass
            elif master_entry.ecs_type == 2: # Customer
                try:
                    payee = Customer.objects.get(code=master_entry.pay_to_code)
                    paid_to = payee.name
                    address = payee.address
                except Customer.DoesNotExist:
                    pass
            elif master_entry.ecs_type == 3: # Supplier
                try:
                    payee = Supplier.objects.get(code=master_entry.pay_to_code)
                    paid_to = payee.name
                    address = payee.address
                except Supplier.DoesNotExist:
                    pass

            bill_no = "-"
            invoice_no = "-"

            if master_entry.payment_type == 1: # Proforma Invoice
                invoice_no = detail_row.proforma_invoice_no
            elif master_entry.payment_type == 4: # Bill Booking
                if detail_row.bill_booking_master: # Assuming FK exists now
                    bill_no = detail_row.bill_booking_master.bill_no
            # Cases 2 and 3 result in "-" for both bill_no and invoice_no, handled by default

            report_data.append({
                'PaidTo': paid_to,
                'CompId': master_entry.compid,
                'ChequeDate': master_entry.cheque_date.strftime('%d/%m/%Y') if master_entry.cheque_date else '',
                'Amount': float(detail_row.amount), # Convert Decimal to float for Crystal Reports like output
                'Address': address,
                'ADNo': master_entry.advice_no,
                'ChequeNo': master_entry.cheque_no,
                'SysDate': master_entry.system_date.strftime('%d/%m/%Y') if master_entry.system_date else '',
                'BillNo': bill_no,
                'TypeECS': {1: "Employee Code", 2: "Customer Code", 3: "Supplier Code"}.get(master_entry.ecs_type, ""),
                'ECS': master_entry.pay_to_code, # This maps to ECS column in original output
                'InvoiceNo': invoice_no,
            })
        return report_data


class AdvicePaymentMaster(models.Model):
    """
    Maps to tblACC_Advice_Payment_Master.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    advice_no = models.CharField(db_column='ADNo', max_length=50, blank=True, null=True)
    payment_type = models.IntegerField(db_column='Type', blank=True, null=True) # 1, 2, 3, 4
    ecs_type = models.IntegerField(db_column='ECSType', blank=True, null=True) # 1, 2, 3
    pay_to_code = models.CharField(db_column='PayTo', max_length=50, blank=True, null=True)
    cheque_date = models.DateTimeField(db_column='ChequeDate', blank=True, null=True) # Assuming DateTime for dates
    cheque_no = models.CharField(db_column='ChequeNo', max_length=50, blank=True, null=True)
    system_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)

    objects = AdvicePaymentMasterManager()

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Payment_Master'
        verbose_name = 'Advice Payment Master'
        verbose_name_plural = 'Advice Payment Masters'

    def __str__(self):
        return self.advice_no or f"Advice {self.id}"

class AdvicePaymentDetail(models.Model):
    """
    Maps to tblACC_Advice_Payment_Details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(AdvicePaymentMaster, models.DO_NOTHING, db_column='MId')
    proforma_invoice_no = models.CharField(db_column='ProformaInvNo', max_length=50, blank=True, null=True)
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3, blank=True, null=True)
    # This FK is an assumption to correct the original ASP.NET logic's ambiguity for BillNo lookup.
    # Adjust db_column if the actual FK column name is different or remove if not applicable.
    bill_booking_master = models.ForeignKey(BillBookingMaster, models.DO_NOTHING, db_column='BillBookingMasterId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Advice_Payment_Details'
        verbose_name = 'Advice Payment Detail'
        verbose_name_plural = 'Advice Payment Details'

    def __str__(self):
        return f"Detail {self.id} for Advice {self.master.advice_no}"

```

#### 4.2 Forms (`accounts/forms.py`)

No forms are needed for this particular page, as it is a read-only report viewer. If interactive filters were to be added to the report, a `forms.Form` would be created for those.

```python
# No forms required for this read-only report page.
# If interactive filters were needed, they would be defined here.
```

#### 4.3 Views (`accounts/views.py`)

The view will be a thin `TemplateView` responsible for retrieving the report data from the `AdvicePaymentMaster` manager and passing it to the template. We will also include a partial view for HTMX to dynamically load the report table if needed.

```python
from django.views.generic import TemplateView, View
from django.shortcuts import get_object_or_404
from django.urls import reverse_lazy
from django.http import HttpResponse, Http404
from django.template.loader import render_to_string
from .models import AdvicePaymentMaster
from django.conf import settings # For default company_id/financial_year_id, or from session/user profile

class AdvicePrintDetailsView(TemplateView):
    """
    Main view for displaying the Advice Print Details report.
    This view orchestrates fetching data using the AdvicePaymentMaster manager.
    It's kept thin by offloading complex data logic to the model.
    """
    template_name = 'accounts/advicepaymentmaster/details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        advice_id = self.kwargs.get('pk')
        
        # In a real application, comp_id and fin_year_id would come from
        # the authenticated user's session, profile, or query parameters.
        # For demonstration, we'll use dummy values or settings.
        # Original C# used Session["compid"] and Session["finyear"].
        company_id = self.request.session.get('compid', settings.DEFAULT_COMPANY_ID) 
        financial_year_id = self.request.session.get('finyear', settings.DEFAULT_FINANCIAL_YEAR_ID) 

        if not advice_id:
            raise Http404("Advice ID not provided.")

        # The core report generation logic is handled by the model manager
        report_data = AdvicePaymentMaster.objects.get_advice_print_details(
            advice_id=advice_id,
            company_id=company_id,
            financial_year_id=financial_year_id
        )
        
        # Fetch the master object if exists, for display of ADNo etc.
        master_obj = None
        if report_data:
            master_obj = AdvicePaymentMaster.objects.filter(id=advice_id).first()

        context['report_data'] = report_data
        context['master_obj'] = master_obj # For displaying ADNo etc on the page

        return context

class AdvicePrintDetailsTablePartialView(View):
    """
    HTMX-driven partial view to load just the report table content.
    This is useful if the report table needs to be dynamically updated
    (e.g., after applying filters, though not in original scope).
    """
    def get(self, request, pk, *args, **kwargs):
        advice_id = pk
        company_id = self.request.session.get('compid', settings.DEFAULT_COMPANY_ID) 
        financial_year_id = self.request.session.get('finyear', settings.DEFAULT_FINANCIAL_YEAR_ID) 

        report_data = AdvicePaymentMaster.objects.get_advice_print_details(
            advice_id=advice_id,
            company_id=company_id,
            financial_year_id=financial_year_id
        )

        context = {
            'report_data': report_data,
        }
        return HttpResponse(render_to_string('accounts/advicepaymentmaster/_report_table.html', context, request))

```

#### 4.4 Templates

Templates will display the report data in a clean, readable format. `details.html` will be the main page, which includes `_report_table.html` as a partial. `base.html` is assumed to exist.

**`accounts/templates/accounts/advicepaymentmaster/details.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 md:mb-0">Advice Print Details Report</h2>
        <div class="flex space-x-2">
            <button
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300"
                onclick="window.print()">
                Print Report
            </button>
            <button
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300"
                hx-get="{% url 'advice_print_list' %}" {# Assuming a list view for advice print is available #}
                hx-swap="outerHTML" {# Or hx-redirect if it's a full page navigation #}
                hx-push-url="true">
                Cancel / Back to List
            </button>
        </div>
    </div>

    {% if master_obj %}
        <div class="bg-white shadow-lg rounded-lg p-6 mb-8 border border-gray-200">
            <h3 class="text-xl font-semibold text-gray-700 mb-4">Advice Details</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <p><strong class="text-gray-600">Advice No:</strong> {{ master_obj.advice_no }}</p>
                <p><strong class="text-gray-600">Cheque No:</strong> {{ master_obj.cheque_no }}</p>
                <p><strong class="text-gray-600">Cheque Date:</strong> {{ master_obj.cheque_date|date:"d M Y" }}</p>
                <p><strong class="text-gray-600">System Date:</strong> {{ master_obj.system_date|date:"d M Y" }}</p>
                <p><strong class="text-gray-600">Payment Type:</strong> 
                    {% if master_obj.payment_type == 1 %}Proforma Inv{% elif master_obj.payment_type == 2 %}Other{% elif master_obj.payment_type == 3 %}Another Type{% elif master_obj.payment_type == 4 %}Bill Booking{% else %}N/A{% endif %}</p>
                <p><strong class="text-gray-600">ECS Type:</strong> 
                    {% if master_obj.ecs_type == 1 %}Employee{% elif master_obj.ecs_type == 2 %}Customer{% elif master_obj.ecs_type == 3 %}Supplier{% else %}N/A{% endif %}</p>
            </div>
        </div>
    {% endif %}

    <div id="report-table-container"
         hx-trigger="load" {# Loads the table once the main page loads #}
         hx-get="{% url 'accounts:advice_print_details_table_partial' pk=master_obj.id %}" {# Use pk for the advice ID #}
         hx-swap="innerHTML">
        <!-- Report table will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Report Data...</p>
        </div>
    </div>

</div>
{% endblock %}

{% block extra_js %}
{# DataTables and Alpine.js are already included in core/base.html #}
<script>
    // This script runs after HTMX has swapped the table into the DOM.
    // It will initialize DataTables on the loaded table.
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'report-table-container') {
            const table = document.getElementById('advicePrintDetailsTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "responsive": true,
                    "autoWidth": false,
                    "columnDefs": [
                        { "orderable": false, "targets": [0] } // Disable ordering for SN column
                    ]
                });
            }
        }
    });

    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}

```

**`accounts/templates/accounts/advicepaymentmaster/_report_table.html`** (Partial template for the table)

```html
<div class="overflow-x-auto bg-white shadow-lg rounded-lg border border-gray-200">
    {% if report_data %}
    <table id="advicePrintDetailsTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Paid To</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cheque No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Advice No</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">System Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ECS Type</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ECS Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in report_data %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.PaidTo }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ row.Amount|floatformat:3 }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.ChequeDate }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.ChequeNo }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.BillNo }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.InvoiceNo }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.Address }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.ADNo }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.SysDate }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.TypeECS }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.ECS }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ row.CompId }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <p class="text-center text-gray-600 py-8">No report data found for this advice ID.</p>
    {% endif %}
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

This file will define the URL patterns that map to our Django views, providing clear and semantic paths for accessing the report.

```python
from django.urls import path
from .views import AdvicePrintDetailsView, AdvicePrintDetailsTablePartialView

app_name = 'accounts' # Namespace for this app's URLs

urlpatterns = [
    # Main view to display the advice print details report for a given ID
    path('advice-print/<int:pk>/', AdvicePrintDetailsView.as_view(), name='advice_print_details'),
    
    # HTMX endpoint to load just the report table (e.g., for dynamic filtering/refresh)
    path('advice-print/<int:pk>/table/', AdvicePrintDetailsTablePartialView.as_view(), name='advice_print_details_table_partial'),

    # Assuming a list view for advice print, redirect target for "Cancel" button
    # You would need to create this view (e.g., an AdvicePrintListView)
    path('advice-print-list/', AdvicePrintDetailsView.as_view(), name='advice_print_list'), # Placeholder for redirection
]
```

#### 4.6 Tests (`accounts/tests.py`)

Comprehensive unit tests for the model's report generation logic and integration tests for the view will ensure the correctness and reliability of the migrated functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch, MagicMock
from .models import (
    Company, Employee, Customer, Supplier, 
    BillBookingMaster, AdvicePaymentMaster, AdvicePaymentDetail
)

# Set up dummy default settings for testing
settings.configure(
    DEBUG=True,
    USE_TZ=True,
    DATABASES={'default': {'ENGINE': 'django.db.backends.sqlite3', 'NAME': ':memory:'}},
    DEFAULT_COMPANY_ID=1,
    DEFAULT_FINANCIAL_YEAR_ID=2023,
    INSTALLED_APPS=[
        'django.contrib.auth',
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'accounts', # Your app name
    ],
    MIDDLEWARE=[
        'django.contrib.sessions.middleware.SessionMiddleware',
        'django.contrib.messages.middleware.MessageMiddleware',
    ],
    TEMPLATES=[{
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
    }],
    ROOT_URLCONF='your_project.urls', # Placeholder for your project's urls
)

# Mock django.db.connection to prevent actual database access during tests
# This is crucial when using managed=False models that might not have tables
# in the test database. Instead, we'll mock queryset results.
class MockQuerySet:
    def __init__(self, data):
        self._data = data

    def get(self, *args, **kwargs):
        for item in self._data:
            match = True
            for key, value in kwargs.items():
                if '__lte' in key:
                    field = key.split('__')[0]
                    if not (field in item and item[field] <= value):
                        match = False
                        break
                elif key not in item or item[key] != value:
                    match = False
                    break
            if match:
                return MagicMock(**item) # Return a mock object mimicking model instance
        raise AdvicePaymentMaster.DoesNotExist

    def all(self):
        return [MagicMock(**item) for item in self._data]
    
    def filter(self, *args, **kwargs):
        return self.all() # Simple mock, can be extended for complex filters


class AdvicePaymentMasterModelTest(TestCase):
    def setUp(self):
        # Setup mock data instead of real database entries
        self.mock_company_data = [{'id': 1, 'name': 'Test Co', 'address': '123 Test St'}]
        self.mock_employee_data = [{'id': 101, 'code': 'EMP001', 'name': 'John Doe', 'address': 'Emp Address'}]
        self.mock_customer_data = [{'id': 201, 'code': 'CUST001', 'name': 'Acme Corp', 'address': 'Cust Address'}]
        self.mock_supplier_data = [{'id': 301, 'code': 'SUP001', 'name': 'Globex Inc', 'address': 'Supp Address'}]
        self.mock_bill_booking_data = [{'id': 1, 'bill_no': 'BB123'}]

        self.mock_master_data = [{
            'id': 1, 'compid': 1, 'finyearid': 2023, 'advice_no': 'ADV001',
            'payment_type': 1, 'ecs_type': 2, 'pay_to_code': 'CUST001',
            'cheque_date': datetime(2023, 1, 10), 'cheque_no': 'CHK123',
            'system_date': datetime(2023, 1, 15)
        }, {
            'id': 2, 'compid': 1, 'finyearid': 2023, 'advice_no': 'ADV002',
            'payment_type': 4, 'ecs_type': 1, 'pay_to_code': 'EMP001',
            'cheque_date': datetime(2023, 2, 10), 'cheque_no': 'CHK456',
            'system_date': datetime(2023, 2, 15)
        }]

        self.mock_detail_data = [{
            'id': 1, 'master_id': 1, 'proforma_invoice_no': 'PI101', 'amount': 100.50, 'bill_booking_master_id': None
        }, {
            'id': 2, 'master_id': 1, 'proforma_invoice_no': 'PI102', 'amount': 200.00, 'bill_booking_master_id': None
        }, {
            'id': 3, 'master_id': 2, 'proforma_invoice_no': None, 'amount': 500.00, 'bill_booking_master_id': 1
        }]

        # Patch the objects manager for each model
        self.patcher_company = patch('accounts.models.Company.objects', new=MockQuerySet(self.mock_company_data))
        self.patcher_employee = patch('accounts.models.Employee.objects', new=MockQuerySet(self.mock_employee_data))
        self.patcher_customer = patch('accounts.models.Customer.objects', new=MockQuerySet(self.mock_customer_data))
        self.patcher_supplier = patch('accounts.models.Supplier.objects', new=MockQuerySet(self.mock_supplier_data))
        self.patcher_bill_booking = patch('accounts.models.BillBookingMaster.objects', new=MockQuerySet(self.mock_bill_booking_data))
        self.patcher_advice_master = patch('accounts.models.AdvicePaymentMaster.objects', new=MockQuerySet(self.mock_master_data))
        self.patcher_advice_detail = patch('accounts.models.AdvicePaymentDetail.objects', new=MockQuerySet(self.mock_detail_data))

        # Start patches
        self.mock_company = self.patcher_company.start()
        self.mock_employee = self.patcher_employee.start()
        self.mock_customer = self.patcher_customer.start()
        self.mock_supplier = self.patcher_supplier.start()
        self.mock_bill_booking = self.patcher_bill_booking.start()
        self.mock_advice_master = self.patcher_advice_master.start()
        self.mock_advice_detail = self.patcher_advice_detail.start()

        # Mock the related manager `advicepaymentdetail_set` on the master object
        # When master_entry.advicepaymentdetail_set.all() is called, it returns filtered mock details
        def mock_all_details_for_master_id(master_id):
            return [
                MagicMock(**d, bill_booking_master=self.mock_bill_booking.get(id=d['bill_booking_master_id']) if d['bill_booking_master_id'] else None)
                for d in self.mock_detail_data if d['master_id'] == master_id
            ]
        
        self.mock_advice_master.get.return_value.advicepaymentdetail_set.all.side_effect = \
            lambda: mock_all_details_for_master_id(self.mock_advice_master.get.return_value.id)


    def tearDown(self):
        # Stop patches
        self.patcher_company.stop()
        self.patcher_employee.stop()
        self.patcher_customer.stop()
        self.patcher_supplier.stop()
        self.patcher_bill_booking.stop()
        self.patcher_advice_master.stop()
        self.patcher_advice_detail.stop()

    def test_get_advice_print_details_type_1_customer(self):
        # Test case for payment_type 1 (Proforma Inv) and ecs_type 2 (Customer)
        # We need to explicitly set the return value of get for specific id
        self.mock_advice_master.get.return_value = MagicMock(
            id=1, compid=1, finyearid=2023, advice_no='ADV001',
            payment_type=1, ecs_type=2, pay_to_code='CUST001',
            cheque_date=datetime(2023, 1, 10), cheque_no='CHK123',
            system_date=datetime(2023, 1, 15),
            # Mock the related manager for details
            advicepaymentdetail_set=MagicMock(all=lambda: [
                MagicMock(id=1, proforma_invoice_no='PI101', amount=100.50, bill_booking_master=None),
                MagicMock(id=2, proforma_invoice_no='PI102', amount=200.00, bill_booking_master=None)
            ])
        )
        
        report_data = AdvicePaymentMaster.objects.get_advice_print_details(
            advice_id=1, company_id=1, financial_year_id=2023
        )

        self.assertEqual(len(report_data), 2)
        self.assertEqual(report_data[0]['PaidTo'], 'Acme Corp')
        self.assertEqual(report_data[0]['Address'], 'Cust Address')
        self.assertEqual(report_data[0]['InvoiceNo'], 'PI101')
        self.assertEqual(report_data[0]['BillNo'], '-')
        self.assertEqual(report_data[0]['TypeECS'], 'Customer Code')
        self.assertEqual(report_data[0]['Amount'], 100.50)

        self.assertEqual(report_data[1]['PaidTo'], 'Acme Corp')
        self.assertEqual(report_data[1]['InvoiceNo'], 'PI102')


    def test_get_advice_print_details_type_4_employee(self):
        # Test case for payment_type 4 (Bill Booking) and ecs_type 1 (Employee)
        self.mock_advice_master.get.return_value = MagicMock(
            id=2, compid=1, finyearid=2023, advice_no='ADV002',
            payment_type=4, ecs_type=1, pay_to_code='EMP001',
            cheque_date=datetime(2023, 2, 10), cheque_no='CHK456',
            system_date=datetime(2023, 2, 15),
            advicepaymentdetail_set=MagicMock(all=lambda: [
                MagicMock(id=3, proforma_invoice_no=None, amount=500.00, 
                          bill_booking_master=self.mock_bill_booking.get(id=1))
            ])
        )

        report_data = AdvicePaymentMaster.objects.get_advice_print_details(
            advice_id=2, company_id=1, financial_year_id=2023
        )

        self.assertEqual(len(report_data), 1)
        self.assertEqual(report_data[0]['PaidTo'], 'John Doe')
        self.assertEqual(report_data[0]['Address'], 'Emp Address')
        self.assertEqual(report_data[0]['InvoiceNo'], '-')
        self.assertEqual(report_data[0]['BillNo'], 'BB123')
        self.assertEqual(report_data[0]['TypeECS'], 'Employee Code')
        self.assertEqual(report_data[0]['Amount'], 500.00)

    def test_get_advice_print_details_no_master_found(self):
        self.mock_advice_master.get.side_effect = AdvicePaymentMaster.DoesNotExist
        report_data = AdvicePaymentMaster.objects.get_advice_print_details(
            advice_id=999, company_id=1, financial_year_id=2023
        )
        self.assertEqual(report_data, [])

    def test_get_advice_print_details_no_details_found(self):
        self.mock_advice_master.get.return_value = MagicMock(
            id=1, compid=1, finyearid=2023, advice_no='ADV001',
            payment_type=1, ecs_type=2, pay_to_code='CUST001',
            cheque_date=datetime(2023, 1, 10), cheque_no='CHK123',
            system_date=datetime(2023, 1, 15),
            advicepaymentdetail_set=MagicMock(all=lambda: []) # No details
        )
        report_data = AdvicePaymentMaster.objects.get_advice_print_details(
            advice_id=1, company_id=1, financial_year_id=2023
        )
        self.assertEqual(report_data, [])

class AdvicePrintDetailsViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Mock the AdvicePaymentMaster manager to control test data
        self.patcher_apm_objects = patch('accounts.models.AdvicePaymentMaster.objects')
        self.mock_apm_objects = self.patcher_apm_objects.start()
        
        # Define mock report data for tests
        self.mock_report_data = [{
            'PaidTo': 'Mock Customer', 'CompId': 1, 'ChequeDate': '10/01/2023',
            'Amount': 100.50, 'Address': 'Mock Address', 'ADNo': 'ADV001',
            'ChequeNo': 'CHK123', 'SysDate': '15/01/2023', 'BillNo': '-',
            'TypeECS': 'Customer Code', 'ECS': 'CUST001', 'InvoiceNo': 'PI101',
        }]
        
        self.mock_master_obj = MagicMock(
            id=1, compid=1, finyearid=2023, advice_no='ADV001',
            payment_type=1, ecs_type=2, pay_to_code='CUST001',
            cheque_date=datetime(2023, 1, 10), cheque_no='CHK123',
            system_date=datetime(2023, 1, 15)
        )
        
        self.mock_apm_objects.get_advice_print_details.return_value = self.mock_report_data
        self.mock_apm_objects.filter.return_value.first.return_value = self.mock_master_obj

    def tearDown(self):
        self.patcher_apm_objects.stop()

    def test_advice_print_details_view_get_success(self):
        # Configure the project's url patterns for testing
        from django.urls import re_path
        from accounts.views import AdvicePrintDetailsView, AdvicePrintDetailsTablePartialView
        settings.ROOT_URLCONF = MagicMock(urlpatterns=[
            re_path(r'^accounts/advice-print/(?P<pk>\d+)/$', AdvicePrintDetailsView.as_view(), name='accounts:advice_print_details'),
            re_path(r'^accounts/advice-print/(?P<pk>\d+)/table/$', AdvicePrintDetailsTablePartialView.as_view(), name='accounts:advice_print_details_table_partial'),
        ])
        
        with self.client.session as session:
            session['compid'] = 1
            session['finyear'] = 2023
            session.save()
            response = self.client.get(reverse('accounts:advice_print_details', args=[1]))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/advicepaymentmaster/details.html')
        self.assertIn('report_data', response.context)
        self.assertIn('master_obj', response.context)
        self.assertEqual(response.context['report_data'], self.mock_report_data)
        self.assertEqual(response.context['master_obj'].advice_no, 'ADV001')
        self.mock_apm_objects.get_advice_print_details.assert_called_with(
            advice_id=1, company_id=1, financial_year_id=2023
        )

    def test_advice_print_details_view_get_no_id(self):
        # Test direct access without ID should raise 404
        # Need to reconfigure URLConf to avoid regex issues on missing PK
        from django.urls import path
        from accounts.views import AdvicePrintDetailsView
        settings.ROOT_URLCONF = MagicMock(urlpatterns=[
            path('accounts/advice-print/', AdvicePrintDetailsView.as_view()), # Test without PK
        ])
        
        response = self.client.get('/accounts/advice-print/')
        self.assertEqual(response.status_code, 404) # Check for Http404

    def test_advice_print_details_table_partial_view_get_success(self):
        from django.urls import re_path
        from accounts.views import AdvicePrintDetailsTablePartialView
        settings.ROOT_URLCONF = MagicMock(urlpatterns=[
            re_path(r'^accounts/advice-print/(?P<pk>\d+)/table/$', AdvicePrintDetailsTablePartialView.as_view(), name='accounts:advice_print_details_table_partial'),
        ])

        with self.client.session as session:
            session['compid'] = 1
            session['finyear'] = 2023
            session.save()
            response = self.client.get(reverse('accounts:advice_print_details_table_partial', args=[1]))
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/advicepaymentmaster/_report_table.html')
        self.assertIn(b'<tbody>', response.content) # Check for table body
        self.assertIn(b'Mock Customer', response.content) # Check for data
        self.mock_apm_objects.get_advice_print_details.assert_called_with(
            advice_id=1, company_id=1, financial_year_id=2023
        )

    def test_advice_print_details_view_no_report_data(self):
        self.mock_apm_objects.get_advice_print_details.return_value = []
        self.mock_apm_objects.filter.return_value.first.return_value = None # No master obj either
        
        from django.urls import re_path
        from accounts.views import AdvicePrintDetailsView
        settings.ROOT_URLCONF = MagicMock(urlpatterns=[
            re_path(r'^accounts/advice-print/(?P<pk>\d+)/$', AdvicePrintDetailsView.as_view(), name='accounts:advice_print_details'),
        ])
        
        with self.client.session as session:
            session['compid'] = 1
            session['finyear'] = 2023
            session.save()
            response = self.client.get(reverse('accounts:advice_print_details', args=[1]))
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'No report data found for this advice ID.', response.content)
        self.assertIsNone(response.context['master_obj'])

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic loading:** The main `details.html` template uses `hx-get` on a `div` to load the `_report_table.html` partial, which contains the DataTables-enabled report table. This ensures the table loads dynamically.
*   **DataTables:** The `_report_table.html` partial includes the JavaScript for DataTables initialization. A JavaScript event listener in `details.html` ensures DataTables is initialized `htmx:afterSwap`, which fires after HTMX injects the table into the DOM.
*   **Alpine.js:** While not extensively used for this specific report page (which is largely static once loaded), Alpine.js would be leveraged for more complex UI interactions if, for example, the report had interactive filters or collapsing sections. For now, it's included via `base.html` and available for future enhancements.
*   **No full page reloads:** All data fetching for the report table is handled by HTMX, ensuring a smooth user experience. The "Cancel" button uses `hx-get` and `hx-push-url` to navigate without a full page refresh if possible, assuming the target page is also HTMX-enabled.

### Final Notes

*   **Configuration:** You'll need to add `'accounts'` to your `INSTALLED_APPS` in `settings.py` and include `path('accounts/', include('accounts.urls'))` in your main `urls.py`.
*   **Session Management:** The original ASP.NET code heavily relies on `Session["compid"]` and `Session["finyear"]`. In Django, these would typically be stored in the session middleware or derived from the authenticated user's profile. For the provided code, `settings.DEFAULT_COMPANY_ID` and `settings.DEFAULT_FINANCIAL_YEAR_ID` are placeholders, but these should be replaced with actual session or user-derived values in a production system.
*   **Error Handling:** The C# code had basic `try-catch` blocks. The Django views and model managers include checks for missing data (e.g., `AdvicePaymentMaster.DoesNotExist`) and can be extended with more robust error handling and logging as needed.
*   **CSS Styling:** The templates use Tailwind CSS classes, assuming Tailwind is configured in your Django project.
*   **Report Export:** If the "Print Details" implies generating an actual printable document (e.g., PDF), you would integrate a Python library like `xhtml2pdf` or `ReportLab` in a separate view/utility function to render the HTML report to a PDF. This is a common next step for such reporting pages.