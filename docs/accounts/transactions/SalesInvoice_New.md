## ASP.NET to Django Conversion Script: Sales Invoice - New

This modernization plan outlines the strategy to transition the ASP.NET Sales Invoice - New module to a modern Django-based solution. The focus is on leveraging Django's robust features, promoting a fat model/thin view architecture, and integrating dynamic front-end interactions using HTMX and Alpine.js, all while ensuring data display is handled efficiently with DataTables.

### Business Benefits of Django Modernization:

*   **Enhanced Performance:** Django's optimized ORM and efficient server-side rendering combined with HTMX for partial page updates leads to faster loading times and a snappier user experience compared to traditional ASP.NET PostBacks.
*   **Improved Maintainability:** Adopting clear separation of concerns (models for business logic, thin views, templates for UI) makes the codebase easier to understand, debug, and extend.
*   **Scalability:** Django is highly scalable, capable of handling increased user loads and data volumes, ensuring the application grows with your business needs.
*   **Modern User Experience:** HTMX and Alpine.js provide a responsive, single-page application-like feel without the complexity of traditional JavaScript frameworks, improving user satisfaction.
*   **Reduced Development Costs:** Python's readability and Django's "batteries-included" philosophy mean faster development cycles and easier onboarding for new team members.
*   **Future-Proofing:** Moving from a legacy framework to a widely supported, open-source technology reduces vendor lock-in and ensures long-term viability and access to a vibrant developer community.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several tables. Based on SQL queries and data bindings, the following schema is inferred:

*   **`tblFinancial_master`**
    *   `FinYearId` (Primary Key, Integer)
    *   `FinYear` (String)
*   **`SD_Cust_master`**
    *   `CustomerId` (Primary Key, Integer)
    *   `CustomerName` (String)
    *   `CompId` (Integer) - Represents Company ID
*   **`SD_Cust_PO_Master`**
    *   `POId` (Primary Key, Integer)
    *   `PONo` (String)
    *   `SysDate` (DateTime)
    *   `PODate` (DateTime)
    *   `CustomerId` (Foreign Key to `SD_Cust_master`)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `CompId` (Integer)
*   **`SD_Cust_PO_Details`**
    *   `Id` (Primary Key, Integer) - This serves as `ItemId` when linked to sales invoice details.
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `TotalQty` (Decimal)
*   **`SD_Cust_WorkOrder_Master`**
    *   `Id` (Primary Key, Integer)
    *   `WONo` (String)
    *   `TaskProjectTitle` (String)
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `CompId` (Integer)
*   **`tblACC_SalesInvoice_Master`**
    *   `Id` (Primary Key, Integer) - This serves as `MId` when linked to sales invoice details.
    *   `POId` (Foreign Key to `SD_Cust_PO_Master`)
    *   `CompId` (Integer)
*   **`tblACC_SalesInvoice_Details`**
    *   `MId` (Foreign Key to `tblACC_SalesInvoice_Master`)
    *   `ItemId` (Foreign Key to `SD_Cust_PO_Details`)
    *   `ReqQty` (Decimal)
*   **`tblACC_SalesInvoice_Master_Type`**
    *   `Id` (Primary Key, Integer)
    *   `Description` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
The `SalesInvoice_New.aspx` page primarily functions as a **"Search and Select"** interface. It allows users to:

*   **Read (R):** Display a filtered list of Purchase Orders (POs). The crucial business logic dictates that only POs which have *any* remaining quantity to be invoiced (i.e., `TotalQty` in `SD_Cust_PO_Details` is greater than `ReqQty` already invoiced in `tblACC_SalesInvoice_Details`) are shown.
*   **Filter/Search:** Filter the PO list by Customer Name (with autocomplete) or PO Number.
*   **Retrieve Related Data:** For each displayed PO, it fetches associated Financial Year, Customer Name, and Work Orders.
*   **Navigation/Redirection:** Upon selecting a PO and associated Work Orders and Invoice Type, the page redirects the user to a separate "Sales Invoice Details" page (`SalesInvoice_New_Details.aspx`) to proceed with invoice creation, passing various parameters. There are no direct Create, Update, or Delete operations on this page for the POs themselves.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET UI components translate to the following Django/HTMX/Alpine.js structure:

*   **Search and Filter:**
    *   **Search Type Dropdown (`DropDownList1`):** A standard HTML `<select>` with HTMX/Alpine.js to dynamically show/hide the Customer Name or PO No input fields.
    *   **Customer Name Textbox (`txtCustName`):** An HTML `<input type="text">` with HTMX for `hx-get` to a Django view providing JSON for autocomplete suggestions. Alpine.js will manage the display and selection of these suggestions.
    *   **PO No Textbox (`txtpoNo`):** An HTML `<input type="text">`.
    *   **Search Button (`btnSearch`):** An HTML `<button>` that triggers an HTMX request to refresh the main data table.
*   **Main Data Display:**
    *   **Data Grid (`GridView1`):** Replaced by a `<table>` enhanced with DataTables for client-side pagination, sorting, and filtering. The table content will be loaded and updated via HTMX.
    *   **Work Order (WO) Multi-select (`ListBox1` with `DropDownExtender`):** This complex component will be implemented using Alpine.js for UI state management (dropdown visibility, selected items) and HTMX to dynamically load the list of Work Orders for a specific PO from a Django endpoint. Selected values will update a hidden field and a display textbox.
    *   **Invoice Type Dropdown (`drp1`):** A standard HTML `<select>` populated from a Django query for `tblACC_SalesInvoice_Master_Type`.
*   **Actions:**
    *   **"Select" Button (`Btn1`):** An HTML `<button>` that uses HTMX to send a POST request with selected data. If validation passes (Type is not 1 and Work Orders are selected), HTMX will trigger a client-side redirect to the "Sales Invoice Details" page. Otherwise, it will trigger an Alpine.js alert.

---

## Step 4: Generate Django Code

The Django application will be named `salesinvoice`.

### 4.1 Models (`salesinvoice/models.py`)

This section defines the Django models for the identified database tables. Each model maps directly to its corresponding existing database table using `managed = False` and `db_table`. The crucial business logic for filtering Purchase Orders with remaining invoice quantities is encapsulated within a custom `SdCustPoMasterQuerySet` manager, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Sum, F, OuterRef, Subquery, DecimalField
from django.db.models.functions import Coalesce

class FinancialMaster(models.Model):
    """Maps to tblFinancial_master"""
    fin_year_id = models.AutoField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"ID: {self.fin_year_id}"

class SdCustMaster(models.Model):
    """Maps to SD_Cust_master"""
    customer_id = models.AutoField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Company ID

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class SdCustPoMasterQuerySet(models.QuerySet):
    """
    Custom QuerySet for SdCustPoMaster to include business logic
    for filtering POs with remaining invoice quantity.
    """
    def with_remaining_invoice_quantity(self, comp_id, fin_year_id):
        """
        Filters Purchase Orders that have at least one detail item with
        remaining quantity available for invoicing. This replicates the core
        filtering logic from the ASP.NET bindgrid method (where y1 > 0).
        """
        # Subquery to calculate the sum of ReqQty (invoiced quantity) for each PO Detail item
        # from tblACC_SalesInvoice_Details, considering the Company ID.
        invoiced_qty_subquery = AccSalesInvoiceDetail.objects.filter(
            item=OuterRef('pk'), # `item` refers to SdCustPoDetail.id
            accsalesinvoicemaster__comp_id=comp_id
        ).values('item').annotate(
            total_invoiced_qty=Coalesce(Sum('req_qty'), 0, output_field=DecimalField())
        ).values('total_invoiced_qty')

        # Filter PO Details where TotalQty > InvoicedQty (i.e., remaining quantity > 0)
        # Then, get the PKs of PO Masters that have at least one such detail.
        po_masters_with_remaining_details_pks = SdCustPoDetail.objects.filter(
            po_master=OuterRef('pk')
        ).annotate(
            invoiced_qty=Coalesce(
                Subquery(invoiced_qty_subquery, output_field=DecimalField()),
                0,
                output_field=DecimalField()
            )
        ).filter(
            total_qty__gt=F('invoiced_qty')
        ).values('po_master_id') # Get the POId from these details

        return self.filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # FinYearId <= current fin year (as per ASP.NET)
            po_id__in=Subquery(po_masters_with_remaining_details_pks) # Filter by POs that have remaining details
        ).order_by('-po_id') # Order By POId Desc as per ASP.NET

class SdCustPoMaster(models.Model):
    """Maps to SD_Cust_PO_Master"""
    po_id = models.AutoField(db_column='POId', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    po_date = models.DateTimeField(db_column='PODate', blank=True, null=True)
    customer = models.ForeignKey(SdCustMaster, models.DO_NOTHING, db_column='CustomerId', related_name='po_masters', blank=True, null=True)
    fin_year = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId', related_name='po_masters', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Company ID

    objects = SdCustPoMasterQuerySet.as_manager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no or f"PO ID: {self.po_id}"

    @property
    def formatted_sys_date(self):
        """Returns SysDate in DD/MM/YYYY format, replicating ASP.NET's FromDateDMY."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

class SdCustPoDetail(models.Model):
    """Maps to SD_Cust_PO_Details"""
    id = models.AutoField(db_column='Id', primary_key=True) # This is the ItemId in SalesInvoiceDetails
    po_master = models.ForeignKey(SdCustPoMaster, models.DO_NOTHING, db_column='POId', related_name='po_details', blank=True, null=True)
    total_qty = models.DecimalField(db_column='TotalQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Detail ID: {self.id} for PO {self.po_master.po_no if self.po_master else 'N/A'}"

class SdCustWorkOrderMaster(models.Model):
    """Maps to SD_Cust_WorkOrder_Master"""
    id = models.AutoField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255, blank=True, null=True)
    po_master = models.ForeignKey(SdCustPoMaster, models.DO_NOTHING, db_column='POId', related_name='work_orders', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.wo_no}-{self.task_project_title}"

class AccSalesInvoiceMaster(models.Model):
    """Maps to tblACC_SalesInvoice_Master"""
    id = models.AutoField(db_column='Id', primary_key=True) # This is the MId in SalesInvoiceDetails
    po_master = models.ForeignKey(SdCustPoMaster, models.DO_NOTHING, db_column='POId', related_name='sales_invoices', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice Master'
        verbose_name_plural = 'Sales Invoice Masters'

    def __str__(self):
        return f"Invoice ID: {self.id}"

class AccSalesInvoiceDetail(models.Model):
    """Maps to tblACC_SalesInvoice_Details"""
    # Assuming a default Django AutoField as primary key if no explicit PK is given in the table.
    # If (MId, ItemId) forms a composite primary key, adjust `unique_together` in Meta.
    accsalesinvoicemaster = models.ForeignKey(AccSalesInvoiceMaster, models.DO_NOTHING, db_column='MId', related_name='invoice_details', blank=True, null=True)
    item = models.ForeignKey(SdCustPoDetail, models.DO_NOTHING, db_column='ItemId', related_name='invoice_details_from_po', blank=True, null=True)
    req_qty = models.DecimalField(db_column='ReqQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Details'
        verbose_name = 'Sales Invoice Detail'
        verbose_name_plural = 'Sales Invoice Details'
        # unique_together = (('accsalesinvoicemaster', 'item'),) # Uncomment if composite PK

    def __str__(self):
        return f"Detail for Invoice {self.accsalesinvoicemaster.id if self.accsalesinvoicemaster else 'N/A'}"

class AccSalesInvoiceMasterType(models.Model):
    """Maps to tblACC_SalesInvoice_Master_Type"""
    id = models.AutoField(db_column='Id', primary_key=True)
    description = models.CharField(db_column='Description', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master_Type'
        verbose_name = 'Sales Invoice Type'
        verbose_name_plural = 'Sales Invoice Types'

    def __str__(self):
        return self.description or f"Type ID: {self.id}"

```

### 4.2 Forms (`salesinvoice/forms.py`)

This form defines the structure for the search controls on the `Sales Invoice - New` page. It does not represent a model form for `SdCustPoMaster` itself, as this page is for searching and selecting existing POs.

```python
from django import forms

class SalesInvoiceSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'PO No'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3',
            'x-model': 'searchType', # Alpine.js to control visibility of other fields
            'hx-trigger': 'change',  # Trigger HTMX to re-render table on type change
            'hx-get': '{% url "salesinvoice_table_partial" %}', # Re-fetch table content
            'hx-target': '#salesInvoiceTable-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#salesInvoiceTable-container .htmx-indicator' # Show spinner on table
        }),
        label="Search By"
    )
    customer_name = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 w-[350px] autocomplete-input',
            'placeholder': 'Enter Customer Name',
            'autocomplete': 'off',
            'x-model': 'customerSearchInput', # Alpine.js model for input value
            '@focus': 'if (!customerSearchInput) $el.dispatchEvent(new Event("input"))', # Trigger search on focus if empty
            '@click': 'if (!customerSearchInput) $el.dispatchEvent(new Event("input"))' # Trigger search on click if empty
        }),
        label="Customer Name"
    )
    po_no = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Enter PO Number'
        }),
        label="PO No"
    )
    # The `customer_id` is implicitly handled by the autocomplete mechanism and
    # the parsing logic in the view's get_queryset method.

```

### 4.3 Views (`salesinvoice/views.py`)

This section defines the Django views responsible for handling requests, processing data, and rendering templates. Views are kept thin, delegating complex data retrieval and business logic to the models and custom managers.

```python
import json
import base64
from urllib.parse import quote, unquote

from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.http import JsonResponse, HttpResponse
from django.shortcuts import render
from django.contrib import messages # For Django messages (optional, if not using HTMX headers)

from .models import (
    SdCustPoMaster, SdCustMaster, SdCustWorkOrderMaster,
    AccSalesInvoiceMasterType, AccSalesInvoiceDetail, SdCustPoDetail
)
from .forms import SalesInvoiceSearchForm

# Helper to retrieve session-specific context (Company ID, Financial Year ID)
# In a full application, these would be managed by a robust authentication/profile system.
def get_session_context(request):
    """Retrieves company and financial year IDs from the session.
    Provides default values for demonstration purposes if not set."""
    comp_id = request.session.get('compid', 1)  # Default to 1 if not in session
    fin_year_id = request.session.get('finyear', 2023) # Default to 2023 if not in session
    return comp_id, fin_year_id

class SalesInvoiceNewListView(TemplateView):
    """
    Main view for the Sales Invoice - New page.
    Renders the search form and a container for the HTMX-loaded table.
    """
    template_name = 'salesinvoice/salesinvoice_new_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['form'] = SalesInvoiceSearchForm(self.request.GET or None)
        # Pass the initial search type to Alpine.js for correct field visibility
        context['initial_search_type'] = self.request.GET.get('search_type', '0')
        return context

class SalesInvoiceTablePartialView(ListView):
    """
    HTMX-driven partial view to render the data table.
    Handles filtering and data retrieval logic from ASP.NET's bindgrid.
    """
    model = SdCustPoMaster
    template_name = 'salesinvoice/_salesinvoice_new_table.html'
    context_object_name = 'po_masters' # Renamed from 'objects_list' for clarity

    def get_queryset(self):
        comp_id, fin_year_id = get_session_context(self.request)

        # Use the custom manager method to get initial filtered POs with remaining quantity
        queryset = SdCustPoMaster.objects.with_remaining_invoice_quantity(
            comp_id=comp_id,
            fin_year_id=fin_year_id
        ).select_related('customer', 'fin_year') # Efficiently fetch related Customer and FinancialYear objects

        # Apply additional search filters from GET parameters (simulating PostBack search)
        search_type = self.request.GET.get('search_type', '0')
        customer_name_input = self.request.GET.get('customer_name', '').strip()
        po_no = self.request.GET.get('po_no', '').strip()

        if search_type == '0' and customer_name_input: # Search by Customer Name
            # Replicate ASP.NET's `fun.getCode` which likely parsed "Name [ID]" or looked up ID.
            # We will try to parse ID if present, otherwise search by name.
            customer_id = None
            if '[' in customer_name_input and customer_name_input.endswith(']'):
                try:
                    customer_id = int(customer_name_input.split('[')[-1][:-1])
                except ValueError:
                    pass # Not a valid customer ID format, fall through to name search

            if customer_id:
                queryset = queryset.filter(customer__customer_id=customer_id)
            else: # Fallback to name contains search if ID parsing fails or no ID in input
                queryset = queryset.filter(customer__customer_name__icontains=customer_name_input)

        elif search_type == '1' and po_no: # Search by PO No
            queryset = queryset.filter(po_no__icontains=po_no)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetch Sales Invoice Types for the dropdown in each table row
        context['invoice_types'] = AccSalesInvoiceMasterType.objects.all().order_by('id')
        return context

class CustomerAutocompleteView(View):
    """
    Provides customer name suggestions for the autocomplete field via HTMX.
    Replicates the ASP.NET `sql` WebMethod.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('prefixText', '').strip()
        comp_id, _ = get_session_context(request) # Get company ID for filtering

        if len(prefix_text) < 1: # Minimum prefix length as per ASP.NET `MinimumPrefixLength="1"`
            # If prefix is too short, return an empty list of suggestions
            return render(request, 'salesinvoice/_customer_autocomplete_results.html', {'suggestions': []})

        customers = SdCustMaster.objects.filter(
            comp_id=comp_id,
            customer_name__icontains=prefix_text # Case-insensitive contains search
        ).values('customer_id', 'customer_name').order_by('customer_name')[:10] # Limit to 10 suggestions

        # Format as "CustomerName [CustomerId]" as in ASP.NET
        suggestions = [f"{c['customer_name']} [{c['customer_id']}]" for c in customers]
        return render(request, 'salesinvoice/_customer_autocomplete_results.html', {'suggestions': suggestions})


class WorkOrderSelectPartialView(View):
    """
    HTMX endpoint to render the Work Order multi-select dropdown content
    for a given PO. Replicates `getWONOInDRP` logic for a single PO.
    """
    def get(self, request, pk, *args, **kwargs):
        comp_id, _ = get_session_context(request)
        try:
            po_master = SdCustPoMaster.objects.get(po_id=pk) # Use po_id as PK
        except SdCustPoMaster.DoesNotExist:
            return HttpResponse("Purchase Order not found", status=404)

        work_orders = SdCustWorkOrderMaster.objects.filter(
            po_master=po_master,
            comp_id=comp_id
        ).order_by('wo_no', 'task_project_title') # Order consistently

        context = {
            'work_orders': work_orders,
            'po_id': pk # Pass PO ID for context in template if needed
        }
        return render(request, 'salesinvoice/_salesinvoice_wo_select.html', context)

# Helper function for "encryption" (mimics ASP.NET's fun.Encrypt + UrlEncode)
# This is a very basic obfuscation using Base64 encoding and URL quoting.
# For true security, Django's built-in signing (django.core.signing) or
# a more robust encryption library should be used.
def encrypt_value(value):
    """Base64 encodes and URL-quotes a value to mimic ASP.NET's encryption/encoding."""
    # Ensure value is string before encoding
    encoded_bytes = base64.b64encode(str(value).encode('utf-8'))
    return quote(encoded_bytes.decode('utf-8')) # URL-encode the base64 string

class SalesInvoiceRedirectView(View):
    """
    Handles the 'Select' button click, performs validation, and redirects to
    the Sales Invoice Details page. Replicates GridView1_RowCommand logic.
    """
    def post(self, request, *args, **kwargs):
        # Extract data from POST request
        po_id = request.POST.get('po_id')
        po_no = request.POST.get('po_no')
        podate_str = request.POST.get('podate') # Date string, e.g., 'DD/MM/YYYY'
        wo_ids_str = request.POST.get('wo_ids') # Comma-separated string of WO IDs
        invoice_type_id = request.POST.get('invoice_type_id')
        customer_id = request.POST.get('customer_id')

        # Convert invoice_type_id to integer for comparison
        try:
            invoice_type = int(invoice_type_id)
        except (ValueError, TypeError):
            invoice_type = None

        # ASP.NET logic: if (type != 1 && WoNoId != "")
        # Type 1 is 'Normal Sales'. The logic means: if it's not a normal sale AND
        # Work Orders were selected (i.e., wo_ids_str is not empty).
        if invoice_type != 1 and wo_ids_str:
            # Prepare encrypted values for redirection parameters
            encrypted_po_id = encrypt_value(po_id)
            encrypted_wo_ids = encrypt_value(wo_ids_str)
            encrypted_po_no = encrypt_value(po_no)
            encrypted_podate = encrypt_value(podate_str)
            encrypted_type = encrypt_value(invoice_type)
            encrypted_customer_id = encrypt_value(customer_id)

            # Construct the URL for the next page
            # `salesinvoice_details_new` is a placeholder URL name,
            # which would typically be defined in another Django app or the project's root urls.
            next_page_url = reverse_lazy('salesinvoice_details_new')
            redirect_url = (
                f"{next_page_url}?"
                f"poid={encrypted_po_id}&wn={encrypted_wo_ids}&pn={encrypted_po_no}&date={encrypted_podate}"
                f"&ty={encrypted_type}&cid={encrypted_customer_id}&ModId=11&SubModId=51"
            )
            # Use HX-Redirect header for HTMX-driven redirection
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})
        else:
            # Replicate ASP.NET's `ClientScript.RegisterStartupScript` alert.
            # For HTMX, we can send a header to trigger a client-side event.
            messages.error(request, 'Select WONo and Type.') # Add a Django message for non-HTMX use or debug
            return HttpResponse(status=204, headers={'HX-Trigger': 'showAlert'}) # Trigger Alpine.js for alert

```

### 4.4 Templates (`salesinvoice/templates/salesinvoice/`)

#### `salesinvoice_new_list.html`

This is the main page template, handling the search form and the container for the HTMX-loaded data table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-500 to-blue-700 text-white p-4 rounded-t-lg mb-4">
        <h1 class="text-xl font-bold">Sales Invoice - New</h1>
    </div>

    <div class="bg-white p-6 rounded-b-lg shadow-lg">
        {# Alpine.js x-data for managing search type visibility and customer autocomplete input #}
        <form id="searchForm"
              hx-get="{% url 'salesinvoice_table_partial' %}"
              hx-target="#salesInvoiceTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_search_type" {# Re-fetch table on form submit or search type change #}
              class="mb-6"
              x-data="{ searchType: '{{ initial_search_type }}', customerSearchInput: '{{ form.customer_name.value|default:"" }}' }">
            
            <div class="flex items-center space-x-4">
                {{% csrf_token %}}
                
                {# Search Type Dropdown #}
                <div>
                    <label for="{{ form.search_type.id_for_label }}" class="sr-only">Search By</label>
                    <select id="{{ form.search_type.id_for_label }}" name="{{ form.search_type.name }}" class="box3" x-model="searchType">
                        {{% for value, label in form.search_type.field.choices %}}
                            <option value="{{{{ value }}}}">{{{{ label }}}}</option>
                        {{% endfor %}}
                    </select>
                </div>

                {# Customer Name Search Field (conditionally shown with Alpine.js) #}
                <div x-show="searchType === '0'" x-transition:enter.duration.500ms x-transition:leave.duration.300ms class="relative flex-grow">
                    <label for="{{ form.customer_name.id_for_label }}" class="sr-only">Customer Name</label>
                    <input type="text" id="{{ form.customer_name.id_for_label }}" name="{{ form.customer_name.name }}"
                           class="box3 w-full autocomplete-input" placeholder="Enter Customer Name"
                           hx-get="{% url 'customer_autocomplete' %}"
                           hx-trigger="keyup changed delay:500ms from:#{{ form.customer_name.id_for_label }}" {# Trigger autocomplete search #}
                           hx-target="#customer-autocomplete-results" {# Target autocomplete results container #}
                           hx-indicator="#customer-autocomplete-spinner" {# Show loading spinner #}
                           hx-swap="innerHTML"
                           autocomplete="off"
                           x-model="customerSearchInput" {# Bind input to Alpine.js model #}
                           @focus="if (!customerSearchInput) $el.dispatchEvent(new Event('input'))" {# Trigger search on focus if empty #}
                           @click="if (!customerSearchInput) $el.dispatchEvent(new Event('input'))" {# Trigger search on click if empty #}
                           @keydown.tab.prevent="$event.target.value = $event.target.closest('.relative').querySelector('[data-selected-suggestion]').textContent; $dispatch('close-autocomplete'); document.getElementById('searchForm').requestSubmit();" {# Select and submit on Tab #}
                           @keydown.arrow-down.prevent="$event.target.closest('.relative').querySelector('.autocomplete-results').focus()" {# Move focus to results #}
                           @keydown.esc.prevent="$dispatch('close-autocomplete')" {# Close autocomplete on Escape #}
                    >
                    <div id="customer-autocomplete-spinner" class="htmx-indicator absolute right-3 top-1/2 -translate-y-1/2">
                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                    </div>
                    {# Autocomplete results list - populated by HTMX, managed by Alpine.js #}
                    <ul id="customer-autocomplete-results" 
                        class="autocomplete-results absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto focus:outline-none"
                        tabindex="-1"
                        x-data="{ activeIndex: -1 }" {# Alpine.js for keyboard navigation in results #}
                        @keydown.arrow-down.prevent="activeIndex = Math.min(activeIndex + 1, $el.children.length - 1); $el.children[activeIndex].focus()"
                        @keydown.arrow-up.prevent="activeIndex = Math.max(activeIndex - 1, 0); $el.children[activeIndex].focus()"
                        @keydown.enter.prevent="$event.target.click()"
                        @focusout="setTimeout(() => $dispatch('close-autocomplete'), 100)" {# Close on focus out #}
                        x-show="customerSearchInput.length >= 1 && $el.children.length > 0" {# Show only if input has text and results exist #}
                        x-transition {# Alpine.js transition #}
                        x-cloak>
                        <!-- Autocomplete results loaded here by HTMX into <li> elements -->
                    </ul>
                </div>
                
                {# PO No Search Field (conditionally shown with Alpine.js) #}
                <div x-show="searchType === '1'" x-transition:enter.duration.500ms x-transition:leave.duration.300ms class="flex-grow">
                    <label for="{{ form.po_no.id_for_label }}" class="sr-only">PO No</label>
                    <input type="text" id="{{ form.po_no.id_for_label }}" name="{{ form.po_no.name }}" class="box3 w-full" placeholder="Enter PO Number">
                </div>

                <button type="submit" class="redbox py-2 px-4 rounded">Search</button>
            </div>
        </form>

        {# Container for the main DataTables content, loaded via HTMX #}
        <div id="salesInvoiceTable-container"
             hx-trigger="load, refreshSalesInvoiceList from:body, submit from:#searchForm" {# Initial load, custom event, or form submit #}
             hx-get="{% url 'salesinvoice_table_partial' %}"
             hx-swap="innerHTML">
            <!-- Initial loading spinner -->
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading sales invoices...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for customer autocomplete behavior
        Alpine.magic('closeAutocomplete', () => () => {
            const resultsContainer = document.getElementById('customer-autocomplete-results');
            if (resultsContainer) {
                resultsContainer.innerHTML = ''; // Clear results
            }
        });

        // Global event listener for `showAlert` triggered by HX-Trigger header
        document.body.addEventListener('showAlert', (event) => {
            // Mimics ASP.NET's ClientScript.RegisterStartupScript alert.
            // For production, integrate with a proper toast/modal system.
            const messageElement = document.querySelector('.messages .error'); // Assuming Django messages are rendered
            if (messageElement) {
                alert(messageElement.innerText);
                messageElement.remove(); // Clear message after showing
            } else {
                alert('Select WONo and Type.'); // Fallback if no specific message
            }
        });

        // Event listener for selecting autocomplete suggestion (delegated)
        document.addEventListener('click', function(event) {
            const suggestionItem = event.target.closest('#customer-autocomplete-results li[data-selected-suggestion]');
            if (suggestionItem) {
                const selectedValue = suggestionItem.dataset.selectedSuggestion;
                const customerInput = document.getElementById('id_customer_name');
                if (customerInput) {
                    customerInput.value = selectedValue;
                    customerInput._x_data_customerAutocomplete.customerSearchInput = selectedValue; // Update Alpine model
                    document.getElementById('customer-autocomplete-results').innerHTML = ''; // Clear results
                    document.getElementById('searchForm').requestSubmit(); // Submit form to refresh table
                }
            }
        });

        // Event listener for fetching WO options per row, triggered by Alpine @click
        document.body.addEventListener('fetch-wo-options', (event) => {
            const poId = event.detail.poId;
            const targetElement = event.target.closest('div[x-data]').querySelector('div[hx-get]'); // Find the WO container
            if (targetElement) {
                // Manually trigger HTMX request
                htmx.trigger(targetElement, 'get');
            }
        });
    });
</script>
<style>
    /* Tailwind-like utility classes or custom styles */
    .box3 {
        @apply block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm;
    }
    .redbox {
        @apply bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded transition duration-150 ease-in-out;
    }
    .autocomplete-results {
        @apply bg-white border border-gray-300 rounded-md shadow-lg;
    }
    .autocomplete-results li {
        @apply px-4 py-2 cursor-pointer hover:bg-blue-100;
    }
    .htmx-indicator {
        opacity: 0;
        transition: opacity 200ms ease-in;
    }
    .htmx-request .htmx-indicator {
        opacity: 1;
    }
    .htmx-request.htmx-indicator {
        opacity: 1;
    }
    [x-cloak] { display: none !important; }
</style>
```

#### `_salesinvoice_new_table.html` (Partial for HTMX)

This template renders the main data table, designed to be loaded dynamically into `salesinvoice_new_list.html` via HTMX. It includes DataTables initialization.

```html
{% load humanize %} {# Optional: for comma formatting of numbers if needed #}
<div class="overflow-x-auto">
    <table id="salesInvoiceTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-50">
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Customer</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for po in po_masters %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{{{ forloop.counter }}}}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{{{ po.fin_year.fin_year|default:"N/A" }}}}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{{{ po.customer.customer_name|default:"N/A" }}}}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{{{ po.po_no|default:"N/A" }}}}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{{{ po.formatted_sys_date }}}}</td>
                
                {# WO No column with multi-select dropdown like functionality, using Alpine.js for state #}
                <td class="py-2 px-4 border-b border-gray-200 text-left">
                    <div x-data="{ open: false, selectedWOsText: '', selectedWOIds: '' }" class="relative inline-block w-full">
                        <input type="text" x-model="selectedWOsText" readonly
                               class="box3 w-full cursor-pointer bg-gray-50"
                               placeholder="Select Work Orders"
                               @click="open = !open; if(open) $nextTick(() => $dispatch('fetch-wo-options', { poId: {{ po.po_id }} }))" {# Trigger WO options fetch #}
                               @keydown.escape="open = false">
                        
                        {# Hidden input to hold comma-separated WO IDs for form submission #}
                        <input type="hidden" name="hfWOno_{{{{ po.po_id }}}}" x-model="selectedWOIds" x-ref="woIds">

                        {# Container for WO options, loaded via HTMX #}
                        <div x-show="open" @click.away="open = false" x-cloak
                             class="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"
                             x-transition:enter="transition ease-out duration-200"
                             x-transition:enter-start="opacity-0 scale-95"
                             x-transition:enter-end="opacity-100 scale-100"
                             x-transition:leave="transition ease-in duration-150"
                             x-transition:leave-start="opacity-100 scale-100"
                             x-transition:leave-end="opacity-0 scale-95"
                             hx-get="{% url 'work_order_select_partial' pk=po.po_id %}" {# HTMX endpoint to fetch WOs #}
                             hx-trigger="get" {# Manual trigger from Alpine.js via $dispatch('fetch-wo-options') #}
                             hx-target="this"
                             hx-swap="innerHTML"
                             >
                            {# Initial loading spinner for WO options #}
                            <div class="text-center py-4 htmx-indicator">
                                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                                <p class="mt-2 text-gray-600">Loading Work Orders...</p>
                            </div>
                        </div>
                    </div>
                </td>

                {# Type dropdown #}
                <td class="py-2 px-4 border-b border-gray-200">
                    <select name="drp1_{{{{ po.po_id }}}}" class="box3 w-full">
                        {{% for type_obj in invoice_types %}}
                            <option value="{{{{ type_obj.id }}}}">{{{{ type_obj.description }}}}</option>
                        {{% endfor %}}
                    </select>
                </td>
                
                {# Actions column #}
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded"
                        hx-post="{% url 'salesinvoice_redirect' %}" {# HTMX POST to redirect handler #}
                        hx-swap="none" {# HTMX will only trigger redirect via HX-Redirect header or client-side alert #}
                        hx-vals='{ {# HTMX values from row controls #}
                            "po_id": "{{{{ po.po_id }}}}",
                            "po_no": "{{{{ po.po_no }}}}",
                            "podate": "{{{{ po.formatted_sys_date }}}}",
                            "customer_id": "{{{{ po.customer.customer_id|default:"" }}}}",
                            "wo_ids": $event.target.closest("tr").querySelector("input[name^=\\"hfWOno_\\"]").value,
                            "invoice_type_id": $event.target.closest("tr").querySelector("select[name^=\\"drp1_\\"]").value
                        }'
                        hx-on::after-request=" {# After request, check for HX-Redirect header #}
                            if(event.detail.xhr.status === 204 && event.detail.xhr.getResponseHeader('HX-Redirect')) {
                                window.location.href = event.detail.xhr.getResponseHeader('HX-Redirect');
                            }
                        "
                    >
                        Select
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="text-center py-8 text-lg text-red-700 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the content is loaded via HTMX
    $(document).ready(function() {
        $('#salesInvoiceTable').DataTable({
            "pageLength": 15, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
            "pagingType": "simple_numbers", // Smaller pagination as per ASP.NET PagerSettings
            "columnDefs": [
                { "orderable": false, "targets": [0, 5, 6, 7] } // SN, WO No, Type, Actions columns not sortable
            ],
            "drawCallback": function(settings) {
                // Re-initialize Alpine.js for new content loaded by DataTables pagination/sorting
                // This is crucial if DataTables re-draws rows.
                if (window.Alpine) {
                    window.Alpine.discoverUninitialized();
                }
            }
        });
    });
</script>
```

#### `_salesinvoice_wo_select.html` (Partial for HTMX)

This template renders the list of Work Order options for the custom multi-select dropdown within each table row.

```html
{# This partial is loaded into the div with x-data in _salesinvoice_new_table.html #}
{# It inherits the Alpine.js context from the parent (selectedWOsText, selectedWOIds) #}

<ul role="listbox" aria-multiselectable="true" class="listbox-scroll">
    {% if work_orders %}
        {% for wo in work_orders %}
            <li role="option"
                class="px-4 py-2 cursor-pointer hover:bg-blue-100 text-gray-800"
                x-init="$el.classList.toggle('bg-blue-200', $parent.selectedWOIds.split(',').includes('{{{{ wo.id }}}}'));" {# Set initial selected state #}
                @click="
                    const selected = $el.classList.toggle('bg-blue-200'); {# Toggle visual selection #}
                    let currentText = $parent.selectedWOsText.split(', ').filter(Boolean);
                    let currentIds = $parent.selectedWOIds.split(',').filter(Boolean);

                    if (selected) {
                        currentText.push('{{{{ wo.wo_no|escapejs }}}}-{{{{ wo.task_project_title|escapejs }}}}');
                        currentIds.push('{{{{ wo.id }}}}');
                    } else {
                        currentText = currentText.filter(item => item !== '{{{{ wo.wo_no|escapejs }}}}-{{{{ wo.task_project_title|escapejs }}}}');
                        currentIds = currentIds.filter(item => item !== '{{{{ wo.id|escapejs }}}}');
                    }
                    $parent.selectedWOsText = currentText.join(', ');
                    $parent.selectedWOIds = currentIds.join(',');
                ">
                {{{{ wo.wo_no }}}} - {{{{ wo.task_project_title }}}}
            </li>
        {% endfor %}
    {% else %}
        <li class="px-4 py-2 text-gray-500 italic">No Work Orders found.</li>
    {% endif %}
</ul>

<style>
    /* Styling for the ListBox-like dropdown */
    .listbox-scroll {
        max-height: 150px; /* Equivalent to ASP.NET ListBox Height */
        overflow-y: auto;
    }
    .listbox-scroll li.bg-blue-200 {
        @apply bg-blue-200; /* Style for selected items */
    }
</style>
```

#### `_customer_autocomplete_results.html` (Partial for HTMX)

This template renders the autocomplete suggestions for the customer search input.

```html
{# This partial is loaded into the ul#customer-autocomplete-results #}
{% if suggestions %}
    {% for suggestion in suggestions %}
        <li tabindex="0"
            role="option"
            data-selected-suggestion="{{{{ suggestion }}}}" {# Store full suggestion for easy retrieval #}
            class="px-4 py-2 cursor-pointer hover:bg-blue-100 focus:outline-none focus:bg-blue-100"
            @click="$dispatch('select-suggestion', '{{{{ suggestion|escapejs }}}}');" {# Dispatch Alpine.js event to select #}
            @keydown.enter.prevent="$dispatch('select-suggestion', '{{{{ suggestion|escapejs }}}}');" {# Select on Enter key #}
            @keydown.tab.prevent="$event.target.closest('[x-data]').customerSearchInput = $event.target.dataset.selectedSuggestion; $dispatch('close-autocomplete')"
            >
            {{{{ suggestion }}}}
        </li>
    {% endfor %}
{% else %}
    <li class="px-4 py-2 text-gray-500 italic">No customers found.</li>
{% endif %}
```

### 4.5 URLs (`salesinvoice/urls.py`)

This file defines the URL patterns for the `salesinvoice` application, mapping URLs to their corresponding views.

```python
from django.urls import path
from .views import (
    SalesInvoiceNewListView,
    SalesInvoiceTablePartialView,
    CustomerAutocompleteView,
    WorkOrderSelectPartialView,
    SalesInvoiceRedirectView
)

urlpatterns = [
    # Main page for Sales Invoice - New
    path('salesinvoice/', SalesInvoiceNewListView.as_view(), name='salesinvoice_new_list'),

    # HTMX endpoint to load/refresh the main data table
    path('salesinvoice/table/', SalesInvoiceTablePartialView.as_view(), name='salesinvoice_table_partial'),

    # HTMX endpoint for customer autocomplete suggestions
    path('salesinvoice/customers/autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # HTMX endpoint to load Work Orders for a specific PO (used by the multi-select dropdown)
    path('salesinvoice/workorders/<int:pk>/', WorkOrderSelectPartialView.as_view(), name='work_order_select_partial'),

    # Endpoint to handle the 'Select' button click and perform redirection or validation
    path('salesinvoice/redirect/', SalesInvoiceRedirectView.as_view(), name='salesinvoice_redirect'),

    # Placeholder URL for the Sales Invoice Details page.
    # This URL should be defined in your main project's urls.py or another app.
    # Example: path('salesinvoice/details/new/', SalesInvoiceDetailsNewView.as_view(), name='salesinvoice_details_new'),
    # For testing, ensure 'salesinvoice_details_new' exists in your project's urlpatterns
    # or mock its existence in tests.
]

```

### 4.6 Tests (`salesinvoice/tests.py`)

This section provides comprehensive unit tests for the models and integration tests for the views, ensuring high test coverage and verifying the migrated functionality.

```python       
import json
import base64
from urllib.parse import quote, unquote

from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta

# Import all models needed for testing
from .models import (
    FinancialMaster, SdCustMaster, SdCustPoMaster, SdCustPoDetail,
    SdCustWorkOrderMaster, AccSalesInvoiceMaster, AccSalesInvoiceDetail,
    AccSalesInvoiceMasterType
)

class SalesInvoiceModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create Financial Year instances
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year_2022 = FinancialMaster.objects.create(fin_year_id=2022, fin_year='2022-2023')

        # Create Customer instances
        cls.customer_1 = SdCustMaster.objects.create(customer_id=1, customer_name='Alpha Corp', comp_id=1)
        cls.customer_2 = SdCustMaster.objects.create(customer_id=2, customer_name='Beta Industries', comp_id=1)
        cls.customer_3 = SdCustMaster.objects.create(customer_id=3, customer_name='Gamma Ltd', comp_id=2) # Different company

        # Create Purchase Order (PO) Master instances
        cls.po_master_1 = SdCustPoMaster.objects.create(
            po_id=1, po_no='PO001', sys_date=timezone.now(), po_date=timezone.now(),
            customer=cls.customer_1, fin_year=cls.fin_year_2023, comp_id=1
        )
        cls.po_master_2 = SdCustPoMaster.objects.create(
            po_id=2, po_no='PO002', sys_date=timezone.now(), po_date=timezone.now(),
            customer=cls.customer_1, fin_year=cls.fin_year_2023, comp_id=1
        )
        cls.po_master_3 = SdCustPoMaster.objects.create(
            po_id=3, po_no='PO003', sys_date=timezone.now(), po_date=timezone.now(),
            customer=cls.customer_2, fin_year=cls.fin_year_2023, comp_id=1
        )
        cls.po_master_old_finyear = SdCustPoMaster.objects.create(
            po_id=4, po_no='PO004', sys_date=timezone.now(), po_date=timezone.now(),
            customer=cls.customer_1, fin_year=cls.fin_year_2022, comp_id=1 # Older financial year
        )
        cls.po_master_diff_comp = SdCustPoMaster.objects.create(
            po_id=5, po_no='PO005', sys_date=timezone.now(), po_date=timezone.now(),
            customer=cls.customer_3, fin_year=cls.fin_year_2023, comp_id=2 # Different company
        )
        cls.po_master_fully_invoiced = SdCustPoMaster.objects.create(
            po_id=6, po_no='PO006', sys_date=timezone.now(), po_date=timezone.now(),
            customer=cls.customer_1, fin_year=cls.fin_year_2023, comp_id=1
        )

        # Create PO Details
        cls.po_detail_1_1 = SdCustPoDetail.objects.create(id=101, po_master=cls.po_master_1, total_qty=100)
        cls.po_detail_1_2 = SdCustPoDetail.objects.create(id=102, po_master=cls.po_master_1, total_qty=50)
        cls.po_detail_2_1 = SdCustPoDetail.objects.create(id=201, po_master=cls.po_master_2, total_qty=75)
        cls.po_detail_3_1 = SdCustPoDetail.objects.create(id=301, po_master=cls.po_master_3, total_qty=200)
        cls.po_detail_4_1 = SdCustPoDetail.objects.create(id=401, po_master=cls.po_master_old_finyear, total_qty=10)
        cls.po_detail_6_1 = SdCustPoDetail.objects.create(id=601, po_master=cls.po_master_fully_invoiced, total_qty=30)

        # Create Work Order instances
        cls.wo_1_1 = SdCustWorkOrderMaster.objects.create(id=1001, wo_no='WO-A', task_project_title='Project X', po_master=cls.po_master_1, comp_id=1)
        cls.wo_1_2 = SdCustWorkOrderMaster.objects.create(id=1002, wo_no='WO-B', task_project_title='Project Y', po_master=cls.po_master_1, comp_id=1)
        cls.wo_2_1 = SdCustWorkOrderMaster.objects.create(id=2001, wo_no='WO-C', task_project_title='Project Z', po_master=cls.po_master_2, comp_id=1)

        # Create Sales Invoice Type instances
        cls.type_normal = AccSalesInvoiceMasterType.objects.create(id=1, description='Normal Sales')
        cls.type_return = AccSalesInvoiceMasterType.objects.create(id=2, description='Sales Return')

        # Create Sales Invoices to test remaining quantity logic
        # Invoice 1 for PO 1, item 101, fully invoiced (PO 1 should still show due to detail 102)
        cls.inv_master_1 = AccSalesInvoiceMaster.objects.create(id=100, po_master=cls.po_master_1, comp_id=1)
        AccSalesInvoiceDetail.objects.create(accsalesinvoicemaster=cls.inv_master_1, item=cls.po_detail_1_1, req_qty=100)

        # Invoice 2 for PO 2, item 201, partially invoiced (PO 2 should show)
        cls.inv_master_2 = AccSalesInvoiceMaster.objects.create(id=101, po_master=cls.po_master_2, comp_id=1)
        AccSalesInvoiceDetail.objects.create(accsalesinvoicemaster=cls.inv_master_2, item=cls.po_detail_2_1, req_qty=50)

        # Invoice 3 for PO 6, item 601, fully invoiced (PO 6 should NOT show)
        cls.inv_master_3 = AccSalesInvoiceMaster.objects.create(id=102, po_master=cls.po_master_fully_invoiced, comp_id=1)
        AccSalesInvoiceDetail.objects.create(accsalesinvoicemaster=cls.inv_master_3, item=cls.po_detail_6_1, req_qty=30)


    def test_model_creation(self):
        self.assertEqual(self.customer_1.customer_name, 'Alpha Corp')
        self.assertEqual(self.po_master_1.po_no, 'PO001')
        self.assertEqual(self.po_detail_1_1.total_qty, 100)
        self.assertEqual(self.wo_1_1.wo_no, 'WO-A')
        self.assertEqual(self.type_normal.description, 'Normal Sales')
        self.assertEqual(self.inv_master_1.po_master.po_no, 'PO001')
        self.assertEqual(AccSalesInvoiceDetail.objects.get(item=self.po_detail_1_1).req_qty, 100)

    def test_po_master_formatted_sys_date_property(self):
        self.assertEqual(self.po_master_1.formatted_sys_date, self.po_master_1.sys_date.strftime('%d/%m/%Y'))
        self.assertEqual(SdCustPoMaster(sys_date=None).formatted_sys_date, '')

    def test_po_master_with_remaining_invoice_quantity_manager(self):
        session_comp_id = 1
        session_fin_year_id = 2023

        remaining_pos = SdCustPoMaster.objects.with_remaining_invoice_quantity(session_comp_id, session_fin_year_id)
        po_ids = [po.po_id for po in remaining_pos]

        # PO1: detail 101 fully invoiced (100/100), detail 102 (50/0) remaining -> should show
        self.assertIn(self.po_master_1.po_id, po_ids)
        # PO2: detail 201 partially invoiced (75/50) remaining -> should show
        self.assertIn(self.po_master_2.po_id, po_ids)
        # PO3: detail 301 (200/0) remaining -> should show
        self.assertIn(self.po_master_3.po_id, po_ids)
        # PO4: old financial year but detail 401 has remaining -> should show
        self.assertIn(self.po_master_old_finyear.po_id, po_ids)
        
        # PO5: different company -> should NOT show
        self.assertNotIn(self.po_master_diff_comp.po_id, po_ids)
        # PO6: fully invoiced -> should NOT show
        self.assertNotIn(self.po_master_fully_invoiced.po_id, po_ids)

class SalesInvoiceViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set session variables for company and financial year
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

        # Set up test data using the class method from the models test
        SalesInvoiceModelsTest.setUpTestData()

        # Mock the salesinvoice_details_new URL for redirect tests
        # In a real project, ensure this URL is defined in your root urls.py
        # For simplicity, we add it dynamically here for testing purposes.
        from django.urls import re_path
        from django.http import HttpResponse
        self.temp_url_name = 'salesinvoice_details_new'
        self.urlpatterns_backup = list(self.client.handler.resolver.url_patterns)
        self.client.handler.resolver.url_patterns.append(
            re_path(r'^salesinvoice/details/new/$', lambda request: HttpResponse("Sales Invoice Details Page"), name=self.temp_url_name)
        )

    def tearDown(self):
        # Restore original urlpatterns to avoid interfering with other tests
        self.client.handler.resolver.url_patterns = self.urlpatterns_backup

    def test_salesinvoice_new_list_view_get(self):
        response = self.client.get(reverse('salesinvoice_new_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesinvoice/salesinvoice_new_list.html')
        self.assertContains(response, 'Sales Invoice - New')
        self.assertContains(response, 'id="salesInvoiceTable-container"') # Check for HTMX container
        self.assertContains(response, 'id="searchForm"') # Check for search form

    def test_salesinvoice_table_partial_view_no_filter(self):
        response = self.client.get(reverse('salesinvoice_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesinvoice/_salesinvoice_new_table.html')
        self.assertTrue('po_masters' in response.context)
        
        # Verify POs with remaining quantity are present
        self.assertContains(response, SalesInvoiceModelsTest.po_master_1.po_no)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_2.po_no)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_3.po_no)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_old_finyear.po_no)
        
        # Verify POs from different company or fully invoiced are NOT present
        self.assertNotContains(response, SalesInvoiceModelsTest.po_master_diff_comp.po_no)
        self.assertNotContains(response, SalesInvoiceModelsTest.po_master_fully_invoiced.po_no)

    def test_salesinvoice_table_partial_view_filter_customer_name(self):
        # Filter by customer name
        response = self.client.get(reverse('salesinvoice_table_partial'), {'search_type': '0', 'customer_name': 'Alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_1.po_no)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_2.po_no)
        self.assertNotContains(response, SalesInvoiceModelsTest.po_master_3.po_no) # Customer B's PO

        # Filter by Customer ID (from autocomplete format)
        response = self.client.get(reverse('salesinvoice_table_partial'), {'search_type': '0', 'customer_name': 'Alpha Corp [1]'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_1.po_no)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_2.po_no)
        self.assertNotContains(response, SalesInvoiceModelsTest.po_master_3.po_no)

    def test_salesinvoice_table_partial_view_filter_po_no(self):
        # Filter by PO No
        response = self.client.get(reverse('salesinvoice_table_partial'), {'search_type': '1', 'po_no': 'PO003'})
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, SalesInvoiceModelsTest.po_master_1.po_no)
        self.assertContains(response, SalesInvoiceModelsTest.po_master_3.po_no)

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete'), {'prefixText': 'al'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesinvoice/_customer_autocomplete_results.html')
        self.assertContains(response, 'Alpha Corp [1]')
        self.assertNotContains(response, 'Beta Industries [2]')
        self.assertNotContains(response, 'Gamma Ltd [3]') # Different company

        response = self.client.get(reverse('customer_autocomplete'), {'prefixText': ''})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesinvoice/_customer_autocomplete_results.html')
        self.assertNotContains(response, '<li') # Should render empty list

    def test_work_order_select_partial_view(self):
        response = self.client.get(reverse('work_order_select_partial', args=[SalesInvoiceModelsTest.po_master_1.po_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'salesinvoice/_salesinvoice_wo_select.html')
        self.assertTrue('work_orders' in response.context)
        self.assertContains(response, 'WO-A - Project X')
        self.assertContains(response, 'WO-B - Project Y')
        self.assertNotContains(response, 'WO-C - Project Z') # Belongs to PO002

    def test_salesinvoice_redirect_view_success(self):
        # Data for a successful redirect (type != 1 and WO_IDs present)
        post_data = {
            'po_id': str(SalesInvoiceModelsTest.po_master_1.po_id),
            'po_no': SalesInvoiceModelsTest.po_master_1.po_no,
            'podate': SalesInvoiceModelsTest.po_master_1.formatted_sys_date,
            'wo_ids': f"{SalesInvoiceModelsTest.wo_1_1.id},{SalesInvoiceModelsTest.wo_1_2.id}",
            'invoice_type_id': str(SalesInvoiceModelsTest.type_return.id), # Type 2 != 1
            'customer_id': str(SalesInvoiceModelsTest.customer_1.customer_id)
        }

        response = self.client.post(reverse('salesinvoice_redirect'), post_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success status with redirect header
        self.assertIn('HX-Redirect', response.headers)
        
        redirect_url = response.headers['HX-Redirect']
        self.assertIn('/salesinvoice/details/new/', redirect_url) # Placeholder URL
        # Verify content of encrypted parameters (basic check for presence)
        self.assertIn(f"poid={quote(base64.b64encode(post_data['po_id'].encode()).decode())}", redirect_url)
        self.assertIn(f"wn={quote(base64.b64encode(post_data['wo_ids'].encode()).decode())}", redirect_url)
        self.assertIn(f"pn={quote(base64.b64encode(post_data['po_no'].encode()).decode())}", redirect_url)
        self.assertIn(f"date={quote(base64.b64encode(post_data['podate'].encode()).decode())}", redirect_url)
        self.assertIn(f"ty={quote(base64.b64encode(post_data['invoice_type_id'].encode()).decode())}", redirect_url)
        self.assertIn(f"cid={quote(base64.b64encode(post_data['customer_id'].encode()).decode())}", redirect_url)

    def test_salesinvoice_redirect_view_failure_type_1(self):
        # Case 1: invoice_type_id == 1 (Type 'Normal Sales'), should fail
        post_data_type1 = {
            'po_id': str(SalesInvoiceModelsTest.po_master_1.po_id),
            'po_no': SalesInvoiceModelsTest.po_master_1.po_no,
            'podate': SalesInvoiceModelsTest.po_master_1.formatted_sys_date,
            'wo_ids': f"{SalesInvoiceModelsTest.wo_1_1.id},{SalesInvoiceModelsTest.wo_1_2.id}",
            'invoice_type_id': str(SalesInvoiceModelsTest.type_normal.id), # Type 1
            'customer_id': str(SalesInvoiceModelsTest.customer_1.customer_id)
        }
        response = self.client.post(reverse('salesinvoice_redirect'), post_data_type1, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers) # Should trigger client-side alert
        self.assertIn('showAlert', response.headers['HX-Trigger'])

    def test_salesinvoice_redirect_view_failure_no_wo(self):
        # Case 2: wo_ids is empty string, should fail
        post_data_no_wo = {
            'po_id': str(SalesInvoiceModelsTest.po_master_1.po_id),
            'po_no': SalesInvoiceModelsTest.po_master_1.po_no,
            'podate': SalesInvoiceModelsTest.po_master_1.formatted_sys_date,
            'wo_ids': '', # Empty WO IDs
            'invoice_type_id': str(SalesInvoiceModelsTest.type_return.id), # Not Type 1
            'customer_id': str(SalesInvoiceModelsTest.customer_1.customer_id)
        }
        response = self.client.post(reverse('salesinvoice_redirect'), post_data_no_wo, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showAlert', response.headers['HX-Trigger'])

```

---

## Step 5: HTMX and Alpine.js Integration

The integration strategy emphasizes a highly dynamic, SPA-like experience without full page reloads, achieved through HTMX and Alpine.js:

*   **HTMX for Server Communication:**
    *   **Initial Page Load:** The `salesinvoice_new_list.html` loads the main search form and an empty container for the table.
    *   **Table Content Load:** The `salesinvoiceTable-container` div uses `hx-get="{% url 'salesinvoice_table_partial' %}"` with `hx-trigger="load, refreshSalesInvoiceList from:body, submit from:#searchForm"` to dynamically load and update the table content.
    *   **Search Form Submission:** The `searchForm` uses `hx-get` to `salesinvoice_table_partial`, ensuring that when the form is submitted or the search type dropdown changes, only the table portion is re-rendered.
    *   **Customer Autocomplete:** The customer name input uses `hx-get` to `customer_autocomplete` with `hx-trigger="keyup changed delay:500ms"` to fetch suggestions. The results are swapped into a designated `ul` element.
    *   **Work Order (WO) Multi-select:** The WO column in each table row contains an Alpine.js component. When the WO input is clicked, Alpine.js dispatches a custom event (`fetch-wo-options`) which an HTMX attribute (`hx-trigger="fetch-wo-options from:body"`) picks up on the WO container. This triggers an `hx-get` to `work_order_select_partial` to load the WO list into the dropdown.
    *   **"Select" Button Action:** The "Select" button in each row uses `hx-post` to `salesinvoice_redirect`. It sends all necessary row data (PO ID, PO No, WO IDs, Invoice Type, Customer ID). Upon successful validation, the view responds with an `HX-Redirect` header, which HTMX interprets to navigate the browser to the next page. If validation fails, an `HX-Trigger: showAlert` header is sent, prompting Alpine.js to display a client-side message.

*   **Alpine.js for Client-Side Interactivity:**
    *   **Search Field Visibility:** `x-data` on the search form combined with `x-show` and `x-model="searchType"` dynamically shows either the Customer Name or PO No input field based on the selected search type.
    *   **Customer Autocomplete UI:** Alpine.js manages the visibility of the autocomplete results list (`x-show`) and provides keyboard navigation (`@keydown.arrow-down`, `@keydown.arrow-up`, `@keydown.enter`) and selection logic (`@click`, `$dispatch('select-suggestion')`).
    *   **WO Multi-select Logic:** Each WO column in the table uses `x-data` to manage its own `open` state (for the dropdown), `selectedWOsText` (for the display input), and `selectedWOIds` (for the hidden input). Alpine.js handles toggling the dropdown, adding/removing selected WO items to the text and ID strings, and updating the hidden input.
    *   **Dynamic Alerts:** An Alpine.js event listener for `showAlert` (`document.body.addEventListener('showAlert', ...)`) intercepts the HTMX trigger and displays a user-friendly alert, mimicking the original ASP.NET behavior.

*   **DataTables for List Views:**
    *   The `_salesinvoice_new_table.html` partial includes the JavaScript for DataTables initialization (`$('#salesInvoiceTable').DataTable(...)`).
    *   DataTables provides client-side searching, sorting, and pagination, which aligns perfectly with the performance goals for this list view.
    *   The `drawCallback` for DataTables ensures that Alpine.js is re-initialized on new content drawn by DataTables, maintaining interactivity.

This approach ensures that all interactions are handled without full page reloads, providing a smooth and modern user experience.

## Final Notes

*   **Placeholder Replacement:** Remember to replace any remaining placeholders (e.g., `[APP_NAME]`, specific database connection details if not already handled by Django settings) with your actual project values.
*   **Django Settings:** Ensure your Django project's `settings.py` includes the `salesinvoice` app, configures a database connection that can access the existing tables (and sets `OPTIONS: {'trust_server_certificate': True}` for SQL Server if needed), and has HTMX and Alpine.js configured in your `base.html`.
*   **Security:** The `encrypt_value` function provided is a basic obfuscation. For production, consider using Django's built-in `django.core.signing` module for secure URL parameters if the data passed needs integrity protection, or a robust encryption library.
*   **Session Management:** The `get_session_context` helper assumes `compid` and `finyear` are in the user's session. In a real application, ensure these values are properly set upon user authentication or system configuration.
*   **Scalability for Autocomplete:** For very large customer databases, consider a more robust server-side search backend (e.g., Elasticsearch with Django Haystack) for autocomplete, though `icontains` with proper indexing is often sufficient.
*   **Error Handling:** While `try-except` blocks are in views, robust error logging and user feedback mechanisms should be in place for production environments.