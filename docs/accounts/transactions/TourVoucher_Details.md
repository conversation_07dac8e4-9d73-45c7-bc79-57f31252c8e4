This comprehensive Django modernization plan addresses the transition of your ASP.NET Tour Voucher Details application to a modern Django-based solution. Our approach prioritizes automation, leverages contemporary web technologies like HTMX and Alpine.js, and strictly adheres to the "fat model, thin view" paradigm for maintainability and scalability.

By moving from legacy ASP.NET Web Forms to Django, your organization will benefit from:
- **Enhanced Performance:** Django's ORM and optimized rendering minimize database calls and improve page load times.
- **Improved Maintainability:** Clean separation of concerns (models for business logic, thin views, and modular templates) makes the codebase easier to understand, debug, and extend.
- **Modern User Experience:** HTMX and Alpine.js deliver dynamic, interactive interfaces without the complexity of traditional JavaScript frameworks, providing a smooth user experience akin to single-page applications.
- **Scalability:** Django's robust architecture and built-in features support growth and increased user loads.
- **Reduced Development Time & Cost:** AI-assisted automation, coupled with Django's convention-over-configuration, significantly reduces manual coding effort and accelerates development cycles.
- **Testability:** Structured models and views enable comprehensive unit and integration testing, leading to more reliable software.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the provided ASP.NET code, the following database tables and their relevant columns are identified as being involved in the "Tour Voucher Details" functionality. This forms the foundation for our Django models.

**Core Tables Involved:**

*   **`tblACC_TourIntimation_Master`**:
    *   `Id` (PK), `EmpId` (FK), `WONo`, `BGGroupId` (FK), `ProjectName`,
    *   `PlaceOfTourCity` (FK), `PlaceOfTourState` (FK), `PlaceOfTourCountry` (FK),
    *   `TourStartDate`, `TourStartTime`, `TourEndDate`, `TourEndTime`, `NoOfDays`,
    *   `NameAddressSerProvider`, `ContactPerson`, `ContactNo`, `Email`,
    *   `CompId`, `FinYearId`
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (PK), `Title`, `EmployeeName`, `CompId`, `FinYearId`
*   **`tblCity`**: `CityId` (PK), `CityName`
*   **`tblState`**: `SId` (PK), `StateName`
*   **`tblCountry`**: `CId` (PK), `CountryName`
*   **`BusinessGroup`**: `Id` (PK), `Name`, `Symbol`
*   **`tblACC_TourExpencessType`**: `Id` (PK), `Terms`
*   **`tblACC_TourAdvance_Details`** (Source for Advance Details tab):
    *   `TADId` (PK), `MId` (FK to `tblACC_TourIntimation_Master.Id`), `ExpencessId` (FK), `Amount`, `Remarks`
*   **`tblACC_TourAdvance`** (Source for Advance Trans. To tab):
    *   `TATId` (PK), `MId` (FK to `tblACC_TourIntimation_Master.Id`), `EmpId` (FK), `Amount`, `Remarks`
*   **`tblACC_TourVoucher_Master`** (The new voucher being created):
    *   `Id` (PK), `TIMId` (FK to `tblACC_TourIntimation_Master.Id`), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `TVNo` (generated), `AmtBalTowardsCompany`, `AmtBalTowardsEmployee`
*   **`tblACC_TourVoucherAdvance_Details`** (Details linked to the new voucher from Advance Details tab inputs):
    *   `Id` (PK), `MId` (FK to `tblACC_TourVoucher_Master.Id`), `TDMId` (FK to `tblACC_TourAdvance_Details.TADId`), `SanctionedAmount`, `Remarks`
*   **`tblACC_TourVoucherAdvance`** (Details linked to the new voucher from Advance Trans. To tab inputs):
    *   `Id` (PK), `MId` (FK to `tblACC_TourVoucher_Master.Id`), `TAMId` (FK to `tblACC_TourAdvance.TATId`), `SanctionedAmount`, `Remarks`

### Step 2: Identify Backend Functionality

The ASP.NET page's functionality revolves around displaying a Tour Intimation's details and then allowing the user to "sanction" advances, which results in the creation of a new "Tour Voucher" and associated detail records.

*   **Read (Display):** Retrieves comprehensive details for a specific `TourIntimation` from `tblACC_TourIntimation_Master` and its related tables (employees, projects, locations, advance details).
*   **Create (Transaction):** When the "Submit" button is clicked:
    *   A unique `TVNo` (Tour Voucher Number) is generated.
    *   A new record is inserted into `tblACC_TourVoucher_Master`.
    *   Multiple records are inserted into `tblACC_TourVoucherAdvance_Details` and `tblACC_TourVoucherAdvance` based on the user-entered sanctioned amounts and remarks from the "Advance Details" and "Advance Trans. To" grids.
    *   Calculations for `AmtBalTowardsCompany` and `AmtBalTowardsEmployee` are performed based on original advance amounts and user-entered sanctioned amounts.
*   **Calculate (Interactive):** The "Calculate" button triggers a recalculation of the "Total Sanctioned Amount" and the "Amount Balance" fields based on current user inputs in the grids. This is a crucial interactive element.
*   **Validation:** Input fields (sanctioned amounts) are validated for numeric values and required status.
*   **Redirection:** After successful submission, the user is redirected to a list view of tour vouchers.

### Step 3: Infer UI Components

The ASP.NET UI components translate to Django's template system, enhanced with HTMX and Alpine.js for dynamic behavior.

*   **Static Labels:** All `asp:Label` controls displaying tour intimation details will be rendered directly using Django template variables.
*   **Tabbed Interface (`AjaxControlToolkit:TabContainer`):** This will be replaced by an Alpine.js component for client-side tab switching, with content inside each tab potentially loaded/refreshed via HTMX.
*   **Data Grids (`asp:GridView`):** These will be rendered as standard HTML `<table>` elements and then enhanced with the DataTables.js library for client-side sorting, searching, and pagination. The editable "Sanctioned Amount" and "Remarks" columns will use Django Formsets.
*   **Input Fields (`asp:TextBox` within GridView):** These map directly to Django form fields rendered within the formsets.
*   **Buttons (`asp:Button`):**
    *   "Calculate" (`btnSum`): Will be an HTMX button that triggers a server-side calculation and swaps updated totals back into the page without a full reload.
    *   "Submit" (`btnSubmit`): Will be an HTMX-enabled form submission that validates, saves data (using a transaction), and triggers a UI refresh or redirection.
    *   "Cancel" (`btnCancel`): A simple link or button that redirects to the list view.
*   **Client-Side Validation (`RequiredFieldValidator`, `RegularExpressionValidator`):** These will be handled by Django's form validation on the server side, and optionally supplemented with client-side validation using Alpine.js or HTML5 attributes for immediate feedback.

### Step 4: Generate Django Code

We will create a Django application named `tourvouchers`.

#### 4.1 Models

This section defines the Django models that map to your existing database tables. All models are set to `managed = False` because they reflect an existing schema. Relationships are defined using `ForeignKey`.

```python
# tourvouchers/models.py
from django.db import models
from django.utils import timezone
from decimal import Decimal
import math

# --- Source Data Models (for display and initial formset data) ---

class Employee(models.Model):
    """Maps to tblHR_OfficeStaff for employee details."""
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''}. {self.employeename} [{self.empid}]"

class City(models.Model):
    """Maps to tblCity for city names."""
    cityid = models.IntegerField(db_column='CityId', primary_key=True)
    cityname = models.CharField(db_column='CityName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.cityname or ''

class State(models.Model):
    """Maps to tblState for state names."""
    sid = models.IntegerField(db_column='SId', primary_key=True)
    statename = models.CharField(db_column='StateName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.statename or ''

class Country(models.Model):
    """Maps to tblCountry for country names."""
    cid = models.IntegerField(db_column='CId', primary_key=True)
    countryname = models.CharField(db_column='CountryName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.countryname or ''

class BusinessGroup(models.Model):
    """Maps to BusinessGroup for business group details."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return f"{self.name} [{self.symbol}]"

class TourIntimation(models.Model):
    """Maps to tblACC_TourIntimation_Master, providing the base details for the voucher."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    empid = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId', to_field='empid')
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    bggroupid = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroupId', blank=True, null=True)
    projectname = models.CharField(db_column='ProjectName', max_length=255, blank=True, null=True)
    placeoftourcity = models.ForeignKey(City, models.DO_NOTHING, db_column='PlaceOfTourCity', blank=True, null=True)
    placeoftourstate = models.ForeignKey(State, models.DO_NOTHING, db_column='PlaceOfTourState', blank=True, null=True)
    placeoftourcountry = models.ForeignKey(Country, models.DO_NOTHING, db_column='PlaceOfTourCountry', blank=True, null=True)
    tourstartdate = models.DateField(db_column='TourStartDate', blank=True, null=True)
    tourstarttime = models.CharField(db_column='TourStartTime', max_length=50, blank=True, null=True)
    tourenddate = models.DateField(db_column='TourEndDate', blank=True, null=True)
    tourendtime = models.CharField(db_column='TourEndTime', max_length=50, blank=True, null=True)
    noofdays = models.IntegerField(db_column='NoOfDays', blank=True, null=True)
    nameaddressserprovider = models.CharField(db_column='NameAddressSerProvider', max_length=500, blank=True, null=True)
    contactperson = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    contactno = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblACC_TourIntimation_Master'
        verbose_name = 'Tour Intimation'
        verbose_name_plural = 'Tour Intimations'

    def __str__(self):
        return f"Intimation {self.id} for {self.empid}"

    def get_place_of_tour_display(self):
        """Constructs the full place of tour string."""
        parts = []
        if self.placeoftourcountry:
            parts.append(self.placeoftourcountry.countryname)
        if self.placeoftourstate:
            parts.append(self.placeoftourstate.statename)
        if self.placeoftourcity:
            parts.append(self.placeoftourcity.cityname)
        return ", ".join(parts)

class TourExpenseType(models.Model):
    """Maps to tblACC_TourExpencessType, a lookup for expense terms."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourExpencessType'
        verbose_name = 'Tour Expense Type'
        verbose_name_plural = 'Tour Expense Types'

    def __str__(self):
        return self.terms or ''

class TourAdvanceDetail(models.Model):
    """Maps to tblACC_TourAdvance_Details, representing initial advance details for an intimation."""
    tadid = models.IntegerField(db_column='TADId', primary_key=True)
    mid = models.ForeignKey(TourIntimation, models.DO_NOTHING, db_column='MId')
    expencessid = models.ForeignKey(TourExpenseType, models.DO_NOTHING, db_column='ExpencessId')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance_Details'
        verbose_name = 'Tour Advance Detail'
        verbose_name_plural = 'Tour Advance Details'

    def __str__(self):
        return f"Advance Detail {self.tadid} for {self.mid}"

class TourAdvanceTo(models.Model):
    """Maps to tblACC_TourAdvance, representing initial advances to employees for an intimation."""
    tatid = models.IntegerField(db_column='TATId', primary_key=True)
    mid = models.ForeignKey(TourIntimation, models.DO_NOTHING, db_column='MId')
    empid = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId', to_field='empid')
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=3, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourAdvance'
        verbose_name = 'Tour Advance To Employee'
        verbose_name_plural = 'Tour Advance To Employees'

    def __str__(self):
        return f"Advance To {self.empid} for {self.mid}"

# --- Target Data Models (for Tour Voucher creation and its associated sanctioned details) ---

class TourVoucherManager(models.Manager):
    """Custom manager for TourVoucher to handle TVNo generation."""
    def get_next_tv_no(self, compid, finyearid):
        # This mirrors the ASP.NET logic: find max TVNo for compid/finyear and increment
        # Assumes TVNo is an integer formatted as D4. Using F() expression for robustness.
        from django.db.models import Max, F
        last_voucher = self.filter(compid=compid, finyearid=finyearid).aggregate(max_tvno=Max(F('tvno')))
        max_tv_num = 0
        if last_voucher['max_tvno']:
            try:
                max_tv_num = int(last_voucher['max_tvno'])
            except ValueError:
                # Fallback if TVNo is not purely numeric, consider it 0 to start from 0001
                pass
        next_tv_num = max_tv_num + 1
        return f"{next_tv_num:04d}"

class TourVoucher(models.Model):
    """Maps to tblACC_TourVoucher_Master, representing the main tour voucher."""
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming ID is auto-incremented by DB
    tmid = models.ForeignKey(TourIntimation, models.DO_NOTHING, db_column='TIMId')
    sysdate = models.DateField(db_column='SysDate')
    systime = models.CharField(db_column='SysTime', max_length=50) # Use CharField to match old system's time format
    sessionid = models.CharField(db_column='SessionId', max_length=255)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    tvno = models.CharField(db_column='TVNo', max_length=50, unique=True)
    amtbaltowardscompany = models.DecimalField(db_column='AmtBalTowardsCompany', max_digits=18, decimal_places=3)
    amtbaltowardsemployee = models.DecimalField(db_column='AmtBalTowardsEmployee', max_digits=18, decimal_places=3)

    objects = TourVoucherManager() # Use custom manager for TVNo generation

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucher_Master'
        verbose_name = 'Tour Voucher'
        verbose_name_plural = 'Tour Vouchers'

    def __str__(self):
        return f"TV No: {self.tvno} for Intimation {self.tmid.id}"

    def calculate_balances(self, total_initial_advance, total_sanctioned_amount):
        """
        Calculates AmtBalTowardsCompany and AmtBalTowardsEmployee.
        This business logic resides within the model for "fat model" approach.
        """
        if (total_initial_advance - total_sanctioned_amount) > 0:
            self.amtbaltowardsemployee = round(total_initial_advance - total_sanctioned_amount, 3)
            self.amtbaltowardscompany = Decimal('0.000')
        else:
            self.amtbaltowardscompany = round(total_sanctioned_amount - total_initial_advance, 3)
            self.amtbaltowardsemployee = Decimal('0.000')

class TourVoucherAdvanceDetail(models.Model):
    """Maps to tblACC_TourVoucherAdvance_Details, representing sanctioned advance details linked to a voucher."""
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming ID is auto-incremented by DB
    mid = models.ForeignKey(TourVoucher, models.DO_NOTHING, db_column='MId')
    tdmid = models.ForeignKey(TourAdvanceDetail, models.DO_NOTHING, db_column='TDMId')
    sanctionedamount = models.DecimalField(db_column='SanctionedAmount', max_digits=18, decimal_places=3, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucherAdvance_Details'
        verbose_name = 'Tour Voucher Advance Detail'
        verbose_name_plural = 'Tour Voucher Advance Details'

    def __str__(self):
        return f"Voucher Detail {self.id} for {self.mid}"

class TourVoucherAdvanceTo(models.Model):
    """Maps to tblACC_TourVoucherAdvance, representing sanctioned advances to employees linked to a voucher."""
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming ID is auto-incremented by DB
    mid = models.ForeignKey(TourVoucher, models.DO_NOTHING, db_column='MId')
    tamid = models.ForeignKey(TourAdvanceTo, models.DO_NOTHING, db_column='TAMId')
    sanctionedamount = models.DecimalField(db_column='SanctionedAmount', max_digits=18, decimal_places=3, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_TourVoucherAdvance'
        verbose_name = 'Tour Voucher Advance To'
        verbose_name_plural = 'Tour Voucher Advance To'

    def __str__(self):
        return f"Voucher Advance To {self.id} for {self.mid}"

```

#### 4.2 Forms

Django forms will manage user input and validation. For the "grid" data, `modelformset_factory` is used to handle multiple rows of input efficiently. Helper fields are added to form classes to display original data, which is not submitted.

```python
# tourvouchers/forms.py
from django import forms
from django.forms import modelformset_factory
from .models import (
    TourVoucher, TourVoucherAdvanceDetail, TourVoucherAdvanceTo,
    TourAdvanceDetail, TourAdvanceTo, TourExpenseType, Employee
)
from decimal import Decimal

# Helper for common Tailwind CSS widget attributes
TEXT_INPUT_CLASSES = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
NUMBER_INPUT_CLASSES = 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm text-right'

class TourVoucherForm(forms.ModelForm):
    """
    Main form for TourVoucher. No direct user input fields here, as system fields like
    SysDate, SysTime, SessionId, CompId, FinYearId, TVNo are set programmatically.
    Balance fields are calculated by model methods.
    """
    class Meta:
        model = TourVoucher
        fields = [] # No direct fields for the user to input for the main voucher initially

class TourVoucherAdvanceDetailForm(forms.ModelForm):
    """
    Form for individual rows in the 'Advance Details' grid (GridView2 equivalent).
    Includes fields for display of original details.
    """
    # These fields are for display only, to show the original details
    original_terms = forms.CharField(
        label="Terms",
        required=False,
        widget=forms.TextInput(attrs={'class': TEXT_INPUT_CLASSES + ' bg-gray-50', 'readonly': 'readonly'})
    )
    original_amount = forms.DecimalField(
        label="Amount",
        required=False,
        widget=forms.NumberInput(attrs={'class': NUMBER_INPUT_CLASSES + ' bg-gray-50', 'readonly': 'readonly'})
    )
    
    class Meta:
        model = TourVoucherAdvanceDetail
        fields = ['sanctionedamount', 'remarks']
        widgets = {
            'sanctionedamount': forms.NumberInput(attrs={'class': NUMBER_INPUT_CLASSES, 'placeholder': '0', 'min': '0'}),
            'remarks': forms.TextInput(attrs={'class': TEXT_INPUT_CLASSES}),
        }

    def clean_sanctionedamount(self):
        """Ensures sanctioned amount is not negative and defaults empty to 0."""
        amount = self.cleaned_data.get('sanctionedamount')
        if amount is None or amount == '': # Treat empty as 0, similar to ASP.NET's default "0"
            return Decimal('0.000')
        try:
            amount = Decimal(amount)
        except (ValueError, TypeError):
            raise forms.ValidationError("Invalid number format.")
        if amount < 0:
            raise forms.ValidationError("Sanctioned amount cannot be negative.")
        return amount

# Formset for handling multiple TourVoucherAdvanceDetail forms
TourVoucherAdvanceDetailFormSet = modelformset_factory(
    TourVoucherAdvanceDetail,
    form=TourVoucherAdvanceDetailForm,
    extra=0, # No extra blank forms by default
    can_delete=False # Rows are for sanctioning, not deletion
)

class TourVoucherAdvanceToForm(forms.ModelForm):
    """
    Form for individual rows in the 'Advance Trans. To' grid (GridView1 equivalent).
    Includes fields for display of original details.
    """
    # These fields are for display only, to show the original details
    original_employee_name = forms.CharField(
        label="Employee Name",
        required=False,
        widget=forms.TextInput(attrs={'class': TEXT_INPUT_CLASSES + ' bg-gray-50', 'readonly': 'readonly'})
    )
    original_amount = forms.DecimalField(
        label="Amount",
        required=False,
        widget=forms.NumberInput(attrs={'class': NUMBER_INPUT_CLASSES + ' bg-gray-50', 'readonly': 'readonly'})
    )

    class Meta:
        model = TourVoucherAdvanceTo
        fields = ['sanctionedamount', 'remarks']
        widgets = {
            'sanctionedamount': forms.NumberInput(attrs={'class': NUMBER_INPUT_CLASSES, 'placeholder': '0', 'min': '0'}),
            'remarks': forms.TextInput(attrs={'class': TEXT_INPUT_CLASSES}),
        }

    def clean_sanctionedamount(self):
        """Ensures sanctioned amount is not negative and defaults empty to 0."""
        amount = self.cleaned_data.get('sanctionedamount')
        if amount is None or amount == '':
            return Decimal('0.000')
        try:
            amount = Decimal(amount)
        except (ValueError, TypeError):
            raise forms.ValidationError("Invalid number format.")
        if amount < 0:
            raise forms.ValidationError("Sanctioned amount cannot be negative.")
        return amount

# Formset for handling multiple TourVoucherAdvanceTo forms
TourVoucherAdvanceToFormSet = modelformset_factory(
    TourVoucherAdvanceTo,
    form=TourVoucherAdvanceToForm,
    extra=0,
    can_delete=False
)

```

#### 4.3 Views

The view will be a custom `FormView` to handle the complexity of displaying details from `TourIntimation` and processing multiple formsets for `TourVoucher` creation. Keeping views thin by delegating complex calculations to models is crucial.

```python
# tourvouchers/views.py
from django.views.generic import FormView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.db import transaction
from django.shortcuts import get_object_or_404, redirect
from datetime import datetime
from decimal import Decimal

from .models import (
    TourIntimation, TourAdvanceDetail, TourAdvanceTo,
    TourVoucher, TourVoucherAdvanceDetail, TourVoucherAdvanceTo,
    TourExpenseType, Employee
)
from .forms import (
    TourVoucherForm, TourVoucherAdvanceDetailFormSet, TourVoucherAdvanceToFormSet
)

class TourVoucherCreateView(FormView):
    """
    This view handles the creation of a Tour Voucher based on an existing Tour Intimation.
    It displays intimation details and allows sanctioning of advances through formsets.
    """
    template_name = 'tourvouchers/tourvoucher_create.html'
    form_class = TourVoucherForm
    success_url = reverse_lazy('tourvouchers:tourvoucher_list') # Redirect to general list of vouchers

    def dispatch(self, request, *args, **kwargs):
        self.tour_intimation_id = kwargs.get('tour_intimation_id')
        self.tour_intimation = get_object_or_404(
            TourIntimation.objects.select_related(
                'empid', 'bggroupid', 'placeoftourcity', 'placeoftourstate', 'placeoftourcountry'
            ),
            pk=self.tour_intimation_id
        )
        return super().dispatch(request, *args, **kwargs)

    def get_initial_formset_data(self):
        """
        Prepares initial data for the advance detail formsets from the Tour Intimation.
        This function handles the logic previously found in fillgrid() and FillGridAdvanceTo().
        """
        # Data for TourVoucherAdvanceDetailFormSet (from tblACC_TourAdvance_Details and tblACC_TourExpencessType)
        advance_details_data = []
        all_expense_types = {exp.id: exp for exp in TourExpenseType.objects.all()}
        tour_advance_details = TourAdvanceDetail.objects.filter(mid=self.tour_intimation).select_related('expencessid')

        for detail in tour_advance_details:
            advance_details_data.append({
                'tdmid': detail.tadid, # Store original TADId for linking
                'original_terms': all_expense_types.get(detail.expencessid_id).terms if detail.expencessid_id else '',
                'original_amount': detail.amount,
                'sanctionedamount': detail.amount or Decimal('0.000'), # Default sanctioned to original or 0
                'remarks': detail.remarks
            })

        # Data for TourVoucherAdvanceToFormSet (from tblACC_TourAdvance and tblHR_OfficeStaff)
        advance_to_data = []
        tour_advance_to_employees = TourAdvanceTo.objects.filter(mid=self.tour_intimation).select_related('empid')

        for advance_to in tour_advance_to_employees:
            advance_to_data.append({
                'tamid': advance_to.tatid, # Store original TATId for linking
                'original_employee_name': advance_to.empid.employeename if advance_to.empid else '',
                'original_amount': advance_to.amount,
                'sanctionedamount': advance_to.amount or Decimal('0.000'), # Default sanctioned to original or 0
                'remarks': advance_to.remarks
            })
        
        return advance_details_data, advance_to_data

    def get_context_data(self, **kwargs):
        """
        Populates the context with tour intimation details and initializes formsets.
        This replaces the complex data fetching in Page_Load.
        """
        context = super().get_context_data(**kwargs)
        context['tour_intimation'] = self.tour_intimation

        advance_details_initial, advance_to_initial = self.get_initial_formset_data()

        # Initialize formsets for GET requests or if not posted
        if self.request.method == 'GET':
            context['advance_details_formset'] = TourVoucherAdvanceDetailFormSet(
                queryset=TourVoucherAdvanceDetail.objects.none(),
                initial=advance_details_initial,
                prefix='advance_details'
            )
            context['advance_to_formset'] = TourVoucherAdvanceToFormSet(
                queryset=TourVoucherAdvanceTo.objects.none(),
                initial=advance_to_initial,
                prefix='advance_to'
            )
            # Calculate initial totals for display
            context['total_initial_advance'] = self._calculate_total_initial_advance(
                advance_details_initial, advance_to_initial
            )
            context['total_sanctioned_amount'] = self._calculate_total_sanctioned_amount(
                context['advance_details_formset'], context['advance_to_formset']
            )
            # Initial balance calculation
            calculated_balances = self._calculate_balance_amounts(
                context['total_initial_advance'], context['total_sanctioned_amount']
            )
            context.update(calculated_balances)

        else: # For POST requests (form submission or HTMX recalculation)
            context['advance_details_formset'] = TourVoucherAdvanceDetailFormSet(
                self.request.POST,
                queryset=TourVoucherAdvanceDetail.objects.none(),
                prefix='advance_details'
            )
            context['advance_to_formset'] = TourVoucherAdvanceToFormSet(
                self.request.POST,
                queryset=TourVoucherAdvanceTo.objects.none(),
                prefix='advance_to'
            )
            # Recalculate totals based on submitted data
            context['total_initial_advance'] = self._calculate_total_initial_advance(
                advance_details_initial, advance_to_initial
            )
            context['total_sanctioned_amount'] = self._calculate_total_sanctioned_amount(
                context['advance_details_formset'], context['advance_to_formset']
            )
            calculated_balances = self._calculate_balance_amounts(
                context['total_initial_advance'], context['total_sanctioned_amount']
            )
            context.update(calculated_balances)

        return context

    def _calculate_total_initial_advance(self, advance_details_initial, advance_to_initial):
        """Calculates total initial advance amount from both sources."""
        total_from_details = sum(item['original_amount'] for item in advance_details_initial if item.get('original_amount'))
        total_from_to = sum(item['original_amount'] for item in advance_to_initial if item.get('original_amount'))
        return round(total_from_details + total_from_to, 3)

    def _calculate_total_sanctioned_amount(self, advance_details_formset, advance_to_formset):
        """Calculates total sanctioned amount from formset data."""
        total_sanctioned = Decimal('0.000')
        
        # Only iterate valid forms, but for HTMX calculate, we might need to iterate all
        # to get current user input, even if not yet fully validated.
        for form in advance_details_formset:
            try:
                sanctioned_val = Decimal(form['sanctionedamount'].value() or '0.000')
                total_sanctioned += sanctioned_val
            except (ValueError, TypeError):
                pass # Ignore invalid entries for calculation, validation handles it later
        
        for form in advance_to_formset:
            try:
                sanctioned_val = Decimal(form['sanctionedamount'].value() or '0.000')
                total_sanctioned += sanctioned_val
            except (ValueError, TypeError):
                pass
        
        return round(total_sanctioned, 3)

    def _calculate_balance_amounts(self, total_initial_advance, total_sanctioned_amount):
        """Calculates balance amounts towards company/employee."""
        balance_towards_employee = Decimal('0.000')
        balance_towards_company = Decimal('0.000')

        if (total_initial_advance - total_sanctioned_amount) > 0:
            balance_towards_employee = round(total_initial_advance - total_sanctioned_amount, 3)
        else:
            balance_towards_company = round(total_sanctioned_amount - total_initial_advance, 3)
        
        return {
            'amt_bal_towards_company': balance_towards_company,
            'amt_bal_towards_employee': balance_towards_employee
        }

    def post(self, request, *args, **kwargs):
        """
        Handles POST requests, including HTMX triggers for recalculation
        or full form submission.
        """
        main_form = self.get_form() # Get the main form
        advance_details_initial, advance_to_initial = self.get_initial_formset_data()

        # Re-instantiate formsets with POST data
        advance_details_formset = TourVoucherAdvanceDetailFormSet(
            request.POST,
            queryset=TourVoucherAdvanceDetail.objects.none(),
            initial=advance_details_initial, # Need initial for original fields
            prefix='advance_details'
        )
        advance_to_formset = TourVoucherAdvanceToFormSet(
            request.POST,
            queryset=TourVoucherAdvanceTo.objects.none(),
            initial=advance_to_initial, # Need initial for original fields
            prefix='advance_to'
        )

        if 'hx_calculate' in request.POST:
            # Handle "Calculate" button via HTMX
            context = self.get_context_data(
                main_form=main_form,
                advance_details_formset=advance_details_formset,
                advance_to_formset=advance_to_formset
            )
            # Render only the totals section
            return self.render_to_response(
                self.get_context_data(
                    main_form=main_form,
                    advance_details_formset=advance_details_formset,
                    advance_to_formset=advance_to_formset
                ),
                template_name='tourvouchers/_totals_section.html' # Partial for totals
            )
        
        # If not hx_calculate, it's a full form submission attempt
        if main_form.is_valid() and advance_details_formset.is_valid() and advance_to_formset.is_valid():
            return self.forms_valid(main_form, advance_details_formset, advance_to_formset)
        else:
            messages.error(self.request, "Invalid data entry. Please correct the errors.")
            context = self.get_context_data(
                main_form=main_form,
                advance_details_formset=advance_details_formset,
                advance_to_formset=advance_to_formset
            )
            # If HTMX request, render the form with errors, otherwise render full page
            if self.request.headers.get('HX-Request'):
                return self.render_to_response(context)
            else:
                return self.form_invalid(main_form) # Fallback for non-HTMX full page post

    @transaction.atomic
    def forms_valid(self, main_form, advance_details_formset, advance_to_formset):
        """
        Handles the successful submission of all forms and formsets.
        All database operations are wrapped in an atomic transaction.
        """
        comp_id = self.tour_intimation.compid # Or from session/user profile
        fin_year_id = self.tour_intimation.finyearid # Or from session/user profile
        session_id = self.request.user.username if self.request.user.is_authenticated else 'anonymous'

        # 1. Generate TVNo
        tv_no = TourVoucher.objects.get_next_tv_no(comp_id, fin_year_id)

        # 2. Calculate final balances (re-calculate on submission to be safe)
        total_initial_advance = self._calculate_total_initial_advance(
            self.get_initial_formset_data()[0], self.get_initial_formset_data()[1]
        )
        total_sanctioned_amount = self._calculate_total_sanctioned_amount(
            advance_details_formset, advance_to_formset
        )

        # Create a dummy TourVoucher instance to use its calculation method
        dummy_voucher = TourVoucher(tmid=self.tour_intimation)
        dummy_voucher.calculate_balances(total_initial_advance, total_sanctioned_amount)
        
        amt_bal_towards_company = dummy_voucher.amtbaltowardscompany
        amt_bal_towards_employee = dummy_voucher.amtbaltowardsemployee

        # 3. Create TourVoucher_Master record
        tour_voucher = TourVoucher.objects.create(
            tmid=self.tour_intimation,
            sysdate=timezone.now().date(),
            systime=timezone.now().strftime('%H:%M:%S'),
            sessionid=session_id,
            compid=comp_id,
            finyearid=fin_year_id,
            tvno=tv_no,
            amtbaltowardscompany=amt_bal_towards_company,
            amtbaltowardsemployee=amt_bal_towards_employee,
            # Assuming 'id' is auto-incremented by the database
            id=TourVoucher.objects.aggregate(Max('id'))['id__max'] + 1 if TourVoucher.objects.exists() else 1
        )

        # 4. Save TourVoucherAdvance_Details
        for form in advance_details_formset:
            if form.cleaned_data: # Only save if form has data (e.g., sanctionedamount > 0)
                original_tad_id = form.cleaned_data['tdmid'] # Use the hidden field for original ID
                original_tad = TourAdvanceDetail.objects.get(tadid=original_tad_id) # Fetch original
                
                TourVoucherAdvanceDetail.objects.create(
                    mid=tour_voucher,
                    tdmid=original_tad,
                    sanctionedamount=form.cleaned_data['sanctionedamount'],
                    remarks=form.cleaned_data['remarks'],
                    id=TourVoucherAdvanceDetail.objects.aggregate(Max('id'))['id__max'] + 1 if TourVoucherAdvanceDetail.objects.exists() else 1
                )

        # 5. Save TourVoucherAdvance
        for form in advance_to_formset:
            if form.cleaned_data:
                original_tat_id = form.cleaned_data['tamid']
                original_tat = TourAdvanceTo.objects.get(tatid=original_tat_id)
                
                TourVoucherAdvanceTo.objects.create(
                    mid=tour_voucher,
                    tamid=original_tat,
                    sanctionedamount=form.cleaned_data['sanctionedamount'],
                    remarks=form.cleaned_data['remarks'],
                    id=TourVoucherAdvanceTo.objects.aggregate(Max('id'))['id__max'] + 1 if TourVoucherAdvanceTo.objects.exists() else 1
                )

        messages.success(self.request, f"Tour Voucher {tv_no} created successfully.")
        
        # HTMX response for success, triggering a redirect or refresh if needed
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Redirect': self.get_success_url()} # Redirect client-side
            )
        return super().form_valid(main_form) # Normal Django redirect

# Helper view for the partial table updates if needed (e.g., for refresh)
class TourVoucherTablePartialView(FormView): # Reusing FormView for context logic
    template_name = 'tourvouchers/_tour_voucher_details_section.html'
    form_class = TourVoucherForm

    def dispatch(self, request, *args, **kwargs):
        self.tour_intimation_id = kwargs.get('tour_intimation_id')
        self.tour_intimation = get_object_or_404(TourIntimation, pk=self.tour_intimation_id)
        return super().dispatch(request, *args, **kwargs)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This will reload the formsets and totals based on current POST data (if any)
        # or initial data (for GET).
        return super(TourVoucherCreateView, self).get_context_data(**kwargs) # Call the parent's get_context_data to reuse logic
```

#### 4.4 Templates

Templates are structured for reusability and HTMX interactions. The main page includes placeholders for dynamic content loaded or updated via HTMX.

```html
{# tourvouchers/tourvoucher_create.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold text-gray-800 mb-6">Tour Voucher Details</h2>

    <form method="post" 
          hx-post="{% url 'tourvouchers:tourvoucher_create' tour_intimation.id %}" 
          hx-swap="outerHTML" 
          hx-target="#main-content-form" 
          hx-indicator="#loading-indicator">
        {% csrf_token %}
        <div id="main-content-form"> {# Target for HTMX form re-render on validation errors #}

            {# Tour Intimation Header Details #}
            <div class="bg-white shadow-md rounded-lg p-6 mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-4">Tour Intimation Overview</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-3 gap-x-6 text-sm text-gray-700">
                    <div><span class="font-medium">Employee Name:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.empid }}</span></div>
                    <div><span class="font-medium">{% if tour_intimation.wono == "NA" %}BG Group{% else %}WO No{% endif %}:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.wono|default:tour_intimation.bggroupid }}</span></div>
                    <div><span class="font-medium">Project Name:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.projectname }}</span></div>
                    <div><span class="font-medium">Place of Tour:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.get_place_of_tour_display }}</span></div>
                    <div><span class="font-medium">Tour Start:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.tourstartdate|date:"d-M-Y" }} Time: {{ tour_intimation.tourstarttime }}</span></div>
                    <div><span class="font-medium">Tour End:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.tourenddate|date:"d-M-Y" }} Time: {{ tour_intimation.tourendtime }}</span></div>
                    <div><span class="font-medium">No of Days:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.noofdays }}</span></div>
                    <div><span class="font-medium">Accommodation Provider:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.nameaddressserprovider }}</span></div>
                    <div><span class="font-medium">Contact Person:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.contactperson }}</span></div>
                    <div><span class="font-medium">Contact No:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.contactno }}</span></div>
                    <div><span class="font-medium">Email:</span> <span class="ml-2 text-gray-900">{{ tour_intimation.email }}</span></div>
                </div>
            </div>

            {# Tab Container for Advance Details and Advance Trans. To #}
            <div x-data="{ activeTab: 'advance-details' }" class="bg-white shadow-md rounded-lg p-6 mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button type="button" @click="activeTab = 'advance-details'"
                                :class="{'border-indigo-500 text-indigo-600': activeTab === 'advance-details', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advance-details'}"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Advance Details
                        </button>
                        <button type="button" @click="activeTab = 'advance-trans-to'"
                                :class="{'border-indigo-500 text-indigo-600': activeTab === 'advance-trans-to', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'advance-trans-to'}"
                                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                            Advance Trans. To
                        </button>
                    </nav>
                </div>

                <div x-show="activeTab === 'advance-details'" class="pt-6">
                    {% include 'tourvouchers/_advance_details_table.html' %}
                </div>

                <div x-show="activeTab === 'advance-trans-to'" class="pt-6">
                    {% include 'tourvouchers/_advance_trans_to_table.html' %}
                </div>
            </div>

            {# Totals and Balance Section #}
            <div id="totals-section" class="bg-white shadow-md rounded-lg p-6 mb-6">
                {% include 'tourvouchers/_totals_section.html' %}
            </div>

            {# Action Buttons #}
            <div class="flex justify-center space-x-4">
                <button type="button"
                        name="hx_calculate"
                        value="true"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm"
                        hx-post="{% url 'tourvouchers:tourvoucher_create' tour_intimation.id %}"
                        hx-target="#totals-section"
                        hx-swap="outerHTML"
                        hx-include="#main-content-form [name^='advance_details-'], #main-content-form [name^='advance_to-']"
                        hx-indicator="#loading-indicator">
                    Calculate
                </button>
                <button type="submit"
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-sm">
                    Submit
                </button>
                <a href="{% url 'tourvouchers:tourvoucher_list' %}"
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-sm flex items-center justify-center">
                    Cancel
                </a>
            </div>
            
            <div id="loading-indicator" class="htmx-indicator fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
                <p class="ml-3 text-white text-lg">Processing...</p>
            </div>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTables after content is loaded/swapped
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'advance-details-table-container' || evt.target.id === 'advance-trans-to-table-container') {
            $('#advanceDetailsTable').DataTable({
                "paging": false,
                "searching": false,
                "info": false,
                "lengthChange": false,
                "order": [] // Disable default ordering
            });
            $('#advanceTransToTable').DataTable({
                "paging": false,
                "searching": false,
                "info": false,
                "lengthChange": false,
                "order": [] // Disable default ordering
            });
        }
    });

    // Initial DataTables setup on page load
    document.addEventListener('DOMContentLoaded', function() {
        $('#advanceDetailsTable').DataTable({
            "paging": false,
            "searching": false,
            "info": false,
            "lengthChange": false,
            "order": []
        });
        $('#advanceTransToTable').DataTable({
            "paging": false,
            "searching": false,
            "info": false,
            "lengthChange": false,
            "order": []
        });
    });
</script>
{% endblock %}

```

```html
{# tourvouchers/_advance_details_table.html #}
{# This partial renders the Advance Details grid (GridView2 equivalent) #}
<div id="advance-details-table-container">
    <h4 class="text-md font-medium text-gray-800 mb-4">Advance Details Sanctioning</h4>
    <div class="overflow-x-auto">
        <table id="advanceDetailsTable" class="min-w-full divide-y divide-gray-200 border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[2%]">SN</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Terms</th>
                    <th class="py-2 px-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Amount</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Original Remarks</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">Sanctioned Amount</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Sanction Remarks</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {{ advance_details_formset.management_form }}
                {% for form in advance_details_formset %}
                <tr>
                    <td class="py-2 px-3 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">
                        {{ form.original_terms.value }}
                        {{ form.tdmid }} {# Hidden field to carry the original TADId #}
                    </td>
                    <td class="py-2 px-3 whitespace-nowrap text-right text-sm text-gray-900">
                        {{ form.original_amount.value|floatformat:3 }}
                    </td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">{{ form.remarks.initial }}</td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">
                        {{ form.sanctionedamount }}
                        {% if form.sanctionedamount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sanctionedamount.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">
                        {{ form.remarks }}
                        {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="py-4 text-center text-gray-500">No advance details to display!</td>
                </tr>
                {% endfor %}
            </tbody>
            {# Footer for total advance amount, similar to ASP.NET GridView footer #}
            <tfoot class="bg-gray-50">
                <tr>
                    <td colspan="2" class="py-2 px-3 text-right font-bold text-gray-800">Total Advance Amount:</td>
                    <td class="py-2 px-3 text-right font-bold text-gray-800" colspan="4">
                        {# This value is the sum of original_amount, not sanctioned. It's for display consistency. #}
                        {# If you need total sanctioned, it will be calculated in _totals_section.html #}
                        {{ advance_details_formset|total_original_advance_amount:True|floatformat:3 }}
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

```
```html
{# tourvouchers/_advance_trans_to_table.html #}
{# This partial renders the Advance Trans. To grid (GridView1 equivalent) #}
<div id="advance-trans-to-table-container">
    <h4 class="text-md font-medium text-gray-800 mb-4">Advance Transfer To Sanctioning</h4>
    <div class="overflow-x-auto">
        <table id="advanceTransToTable" class="min-w-full divide-y divide-gray-200 border border-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[2%]">SN</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Employee Name</th>
                    <th class="py-2 px-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider w-[10%]">Amount</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Original Remarks</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[15%]">Sanctioned Amount</th>
                    <th class="py-2 px-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[20%]">Sanction Remarks</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {{ advance_to_formset.management_form }}
                {% for form in advance_to_formset %}
                <tr>
                    <td class="py-2 px-3 whitespace-nowrap text-right text-sm text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">
                        {{ form.original_employee_name.value }}
                        {{ form.tamid }} {# Hidden field to carry the original TATId #}
                    </td>
                    <td class="py-2 px-3 whitespace-nowrap text-right text-sm text-gray-900">
                        {{ form.original_amount.value|floatformat:3 }}
                    </td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">{{ form.remarks.initial }}</td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">
                        {{ form.sanctionedamount }}
                        {% if form.sanctionedamount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.sanctionedamount.errors }}</p>{% endif %}
                    </td>
                    <td class="py-2 px-3 whitespace-nowrap text-sm text-gray-900">
                        {{ form.remarks }}
                        {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="6" class="py-4 text-center text-gray-500">No advance transfers to display!</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot class="bg-gray-50">
                <tr>
                    <td colspan="2" class="py-2 px-3 text-right font-bold text-gray-800">Total Advance Amount:</td>
                    <td class="py-2 px-3 text-right font-bold text-gray-800" colspan="4">
                        {# This value is the sum of original_amount, not sanctioned. #}
                        {{ advance_to_formset|total_original_advance_amount:False|floatformat:3 }}
                    </td>
                </tr>
            </tfoot>
        </table>
    </div>
</div>

```
```html
{# tourvouchers/_totals_section.html #}
{# This partial is swapped via HTMX for "Calculate" button functionality #}
<div class="grid grid-cols-1 md:grid-cols-2 gap-y-3 gap-x-6 text-sm text-gray-700">
    <div>
        <span class="font-bold">Total Advance Amt:</span>
        <span class="ml-2 font-bold text-gray-900">{{ total_initial_advance|floatformat:3 }}</span>
    </div>
    <div>
        <span class="font-bold">Total Sanctioned Amount:</span>
        <span class="ml-2 font-bold text-gray-900">{{ total_sanctioned_amount|floatformat:3 }}</span>
    </div>
    <div>
        <span class="font-bold">Amt. Bal Towards Company:</span>
        <input type="text" class="box3 w-24 px-2 py-1 border border-gray-300 rounded-md bg-gray-100 text-right font-bold"
               value="{{ amt_bal_towards_company|floatformat:3 }}" readonly />
    </div>
    <div>
        <span class="font-bold">Amt. Bal Towards Employee:</span>
        <input type="text" class="box3 w-24 px-2 py-1 border border-gray-300 rounded-md bg-gray-100 text-right font-bold"
               value="{{ amt_bal_towards_employee|floatformat:3 }}" readonly />
    </div>
</div>

```

**Custom Template Filter for Formset Totals:**

To calculate totals for `original_amount` within the template, you'll need a custom template filter.

```python
# tourvouchers/templatetags/tourvoucher_filters.py
from django import template
from decimal import Decimal

register = template.Library()

@register.filter
def total_original_advance_amount(formset, is_detail_formset):
    """
    Calculates the total original advance amount for a given formset.
    `is_detail_formset` is True for advance_details_formset (original_amount)
    and False for advance_to_formset (original_amount).
    """
    total = Decimal('0.000')
    if is_detail_formset:
        for form in formset:
            if form.initial and 'original_amount' in form.initial:
                amount = form.initial['original_amount']
                if isinstance(amount, Decimal):
                    total += amount
    else: # advance_to_formset
        for form in formset:
            if form.initial and 'original_amount' in form.initial:
                amount = form.initial['original_amount']
                if isinstance(amount, Decimal):
                    total += amount
    return total

```
Remember to create an empty `__init__.py` file in the `tourvouchers/templatetags/` directory and load the filter in your templates using `{% load tourvoucher_filters %}`.

#### 4.5 URLs

URL patterns define how requests are routed to your views.

```python
# tourvouchers/urls.py
from django.urls import path
from .views import TourVoucherCreateView, TourVoucherTablePartialView

app_name = 'tourvouchers' # Namespace for URLs

urlpatterns = [
    # URL for creating a Tour Voucher based on a Tour Intimation ID
    path('intimation/<int:tour_intimation_id>/create/', TourVoucherCreateView.as_view(), name='tourvoucher_create'),
    
    # Placeholder for a list view of tour vouchers (success_url points here)
    path('list/', TourVoucherTablePartialView.as_view(), name='tourvoucher_list'), # Renamed for clarity for a simple list view
    
    # HTMX endpoint for dynamic table or section reloads (if needed separately)
    # The current setup uses the main view's post method for recalculation directly.
    # If a separate partial reload of the main table was needed, it would look like:
    # path('intimation/<int:tour_intimation_id>/details_table/', TourVoucherTablePartialView.as_view(), name='tourvoucher_details_table'),
]

```

#### 4.6 Tests

Comprehensive tests ensure the reliability of your models and views. Aim for at least 80% code coverage.

```python
# tourvouchers/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
import datetime

# Import all models needed for testing
from .models import (
    Employee, City, State, Country, BusinessGroup, TourIntimation,
    TourExpenseType, TourAdvanceDetail, TourAdvanceTo,
    TourVoucher, TourVoucherAdvanceDetail, TourVoucherAdvanceTo
)
from .forms import TourVoucherAdvanceDetailForm, TourVoucherAdvanceToForm

class TourVoucherModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for FKs
        cls.country = Country.objects.create(cid=1, countryname='India')
        cls.state = State.objects.create(sid=1, statename='Karnataka')
        cls.city = City.objects.create(cityid=1, cityname='Bangalore')
        cls.business_group = BusinessGroup.objects.create(id=1, name='AutoERP Group', symbol='AEG')
        cls.employee = Employee.objects.create(empid='EMP001', title='Mr', employeename='John Doe', compid=1, finyearid=2024)
        cls.expense_type = TourExpenseType.objects.create(id=101, terms='Travel Expenses')
        cls.expense_type_2 = TourExpenseType.objects.create(id=102, terms='Accommodation')

        # Create a TourIntimation
        cls.tour_intimation = TourIntimation.objects.create(
            id=1,
            empid=cls.employee,
            wono='WO-1234',
            bggroupid=cls.business_group,
            projectname='ERP Migration',
            placeoftourcity=cls.city,
            placeoftourstate=cls.state,
            placeoftourcountry=cls.country,
            tourstartdate=datetime.date(2024, 7, 1),
            tourstarttime='09:00',
            tourenddate=datetime.date(2024, 7, 5),
            tourendtime='18:00',
            noofdays=5,
            nameaddressserprovider='Hotel ABC',
            contactperson='Jane Smith',
            contactno='**********',
            email='<EMAIL>',
            compid=1,
            finyearid=2024
        )

        # Create TourAdvanceDetails related to the intimation
        cls.advance_detail_1 = TourAdvanceDetail.objects.create(
            tadid=1, mid=cls.tour_intimation, expencessid=cls.expense_type, amount=Decimal('500.000'), remarks='Airfare'
        )
        cls.advance_detail_2 = TourAdvanceDetail.objects.create(
            tadid=2, mid=cls.tour_intimation, expencessid=cls.expense_type_2, amount=Decimal('300.000'), remarks='Hotel stay'
        )

        # Create TourAdvanceTo related to the intimation
        cls.employee_2 = Employee.objects.create(empid='EMP002', title='Ms', employeename='Alice Brown', compid=1, finyearid=2024)
        cls.advance_to_1 = TourAdvanceTo.objects.create(
            tatid=1, mid=cls.tour_intimation, empid=cls.employee_2, amount=Decimal('200.000'), remarks='Cash advance'
        )

    def test_tour_intimation_display_methods(self):
        self.assertEqual(self.tour_intimation.get_place_of_tour_display(), 'India, Karnataka, Bangalore')
        self.assertEqual(str(self.tour_intimation), 'Intimation 1 for Mr. John Doe [EMP001]')

    def test_tour_voucher_creation_and_tv_no_generation(self):
        initial_count = TourVoucher.objects.count()
        comp_id = 1
        fin_year_id = 2024
        next_tv_no = TourVoucher.objects.get_next_tv_no(comp_id, fin_year_id)
        self.assertEqual(next_tv_no, '0001') # First one should be 0001

        tour_voucher = TourVoucher.objects.create(
            id=1, # Manually assign for initial test, or adapt to use max+1
            tmid=self.tour_intimation,
            sysdate=timezone.now().date(),
            systime=timezone.now().strftime('%H:%M:%S'),
            sessionid='testuser',
            compid=comp_id,
            finyearid=fin_year_id,
            tvno=next_tv_no,
            amtbaltowardscompany=Decimal('0.000'),
            amtbaltowardsemployee=Decimal('0.000')
        )
        self.assertEqual(TourVoucher.objects.count(), initial_count + 1)
        self.assertEqual(tour_voucher.tvno, '0001')
        self.assertEqual(str(tour_voucher), 'TV No: 0001 for Intimation 1')

        # Test next TVNo generation
        next_tv_no_2 = TourVoucher.objects.get_next_tv_no(comp_id, fin_year_id)
        self.assertEqual(next_tv_no_2, '0002')

    def test_tour_voucher_balance_calculation(self):
        # Scenario 1: Initial Advance > Sanctioned (Balance Towards Employee)
        tour_voucher = TourVoucher(tmid=self.tour_intimation)
        tour_voucher.calculate_balances(Decimal('1000.000'), Decimal('800.000'))
        self.assertEqual(tour_voucher.amtbaltowardsemployee, Decimal('200.000'))
        self.assertEqual(tour_voucher.amtbaltowardscompany, Decimal('0.000'))

        # Scenario 2: Sanctioned > Initial Advance (Balance Towards Company)
        tour_voucher.calculate_balances(Decimal('800.000'), Decimal('1000.000'))
        self.assertEqual(tour_voucher.amtbaltowardsemployee, Decimal('0.000'))
        self.assertEqual(tour_voucher.amtbaltowardscompany, Decimal('200.000'))

        # Scenario 3: Equal amounts (No balance)
        tour_voucher.calculate_balances(Decimal('900.000'), Decimal('900.000'))
        self.assertEqual(tour_voucher.amtbaltowardsemployee, Decimal('0.000'))
        self.assertEqual(tour_voucher.amtbaltowardscompany, Decimal('0.000'))
        
    def test_tour_voucher_advance_detail_form_validation(self):
        # Valid amount
        form = TourVoucherAdvanceDetailForm(data={'sanctionedamount': '150.50', 'remarks': 'Test'})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['sanctionedamount'], Decimal('150.500'))
        
        # Empty amount should be 0
        form = TourVoucherAdvanceDetailForm(data={'sanctionedamount': '', 'remarks': 'Test'})
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['sanctionedamount'], Decimal('0.000'))

        # Negative amount
        form = TourVoucherAdvanceDetailForm(data={'sanctionedamount': '-100', 'remarks': 'Test'})
        self.assertFalse(form.is_valid())
        self.assertIn('Sanctioned amount cannot be negative.', form.errors['sanctionedamount'])
        
        # Non-numeric amount
        form = TourVoucherAdvanceDetailForm(data={'sanctionedamount': 'abc', 'remarks': 'Test'})
        self.assertFalse(form.is_valid())
        self.assertIn('Invalid number format.', form.errors['sanctionedamount'])

class TourVoucherViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Create minimal required data for FKs
        self.country = Country.objects.create(cid=1, countryname='India')
        self.state = State.objects.create(sid=1, statename='Karnataka')
        self.city = City.objects.create(cityid=1, cityname='Bangalore')
        self.business_group = BusinessGroup.objects.create(id=1, name='AutoERP Group', symbol='AEG')
        self.employee = Employee.objects.create(empid='EMP001', title='Mr', employeename='John Doe', compid=1, finyearid=2024)
        self.expense_type = TourExpenseType.objects.create(id=101, terms='Travel Expenses')
        self.expense_type_2 = TourExpenseType.objects.create(id=102, terms='Accommodation')

        self.tour_intimation = TourIntimation.objects.create(
            id=1,
            empid=self.employee,
            wono='WO-1234',
            bggroupid=self.business_group,
            projectname='ERP Migration',
            placeoftourcity=self.city,
            placeoftourstate=self.state,
            placeoftourcountry=self.country,
            tourstartdate=datetime.date(2024, 7, 1),
            tourstarttime='09:00',
            tourenddate=datetime.date(2024, 7, 5),
            tourendtime='18:00',
            noofdays=5,
            nameaddressserprovider='Hotel ABC',
            contactperson='Jane Smith',
            contactno='**********',
            email='<EMAIL>',
            compid=1,
            finyearid=2024
        )
        self.advance_detail_1 = TourAdvanceDetail.objects.create(
            tadid=1, mid=self.tour_intimation, expencessid=self.expense_type, amount=Decimal('500.000'), remarks='Airfare'
        )
        self.advance_detail_2 = TourAdvanceDetail.objects.create(
            tadid=2, mid=self.tour_intimation, expencessid=self.expense_type_2, amount=Decimal('300.000'), remarks='Hotel stay'
        )
        self.employee_2 = Employee.objects.create(empid='EMP002', title='Ms', employeename='Alice Brown', compid=1, finyearid=2024)
        self.advance_to_1 = TourAdvanceTo.objects.create(
            tatid=1, mid=self.tour_intimation, empid=self.employee_2, amount=Decimal('200.000'), remarks='Cash advance'
        )

        self.create_url = reverse('tourvouchers:tourvoucher_create', args=[self.tour_intimation.id])
        self.list_url = reverse('tourvouchers:tourvoucher_list')

    def test_tour_voucher_create_view_get(self):
        response = self.client.get(self.create_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tourvouchers/tourvoucher_create.html')
        self.assertContains(response, 'Tour Voucher Details')
        self.assertContains(response, 'John Doe')
        self.assertContains(response, 'Advance Details')
        self.assertContains(response, 'Advance Trans. To')
        self.assertIn('tour_intimation', response.context)
        self.assertIn('advance_details_formset', response.context)
        self.assertIn('advance_to_formset', response.context)
        self.assertEqual(response.context['total_initial_advance'], Decimal('1000.000')) # 500 + 300 + 200

    def test_tour_voucher_create_view_post_calculate_htmx(self):
        # Simulate HTMX POST for calculation
        data = {
            'advance_details-TOTAL_FORMS': 2,
            'advance_details-INITIAL_FORMS': 2,
            'advance_details-MIN_NUM_FORMS': 0,
            'advance_details-MAX_NUM_FORMS': 1000,
            'advance_details-0-sanctionedamount': '450.00', # Changed from 500
            'advance_details-0-remarks': 'Airfare adjusted',
            'advance_details-0-tdmid': self.advance_detail_1.tadid,
            'advance_details-1-sanctionedamount': '300.00', # Same
            'advance_details-1-remarks': 'Hotel stay',
            'advance_details-1-tdmid': self.advance_detail_2.tadid,

            'advance_to-TOTAL_FORMS': 1,
            'advance_to-INITIAL_FORMS': 1,
            'advance_to-MIN_NUM_FORMS': 0,
            'advance_to-MAX_NUM_FORMS': 1000,
            'advance_to-0-sanctionedamount': '250.00', # Changed from 200
            'advance_to-0-remarks': 'Cash advance revised',
            'advance_to-0-tamid': self.advance_to_1.tatid,

            'hx_calculate': 'true', # Trigger calculate action
        }
        response = self.client.post(self.create_url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'tourvouchers/_totals_section.html')
        
        # Expected total sanctioned: 450 + 300 + 250 = 1000
        # Expected total initial: 500 + 300 + 200 = 1000
        # So, balances should be 0.
        self.assertContains(response, 'Total Advance Amt: <span class="ml-2 font-bold text-gray-900">1000.000</span>')
        self.assertContains(response, 'Total Sanctioned Amount: <span class="ml-2 font-bold text-gray-900">1000.000</span>')
        self.assertContains(response, 'Amt. Bal Towards Company:</span>\n        <input type="text" class="box3 w-24 px-2 py-1 border border-gray-300 rounded-md bg-gray-100 text-right font-bold"\n               value="0.000" readonly />')
        self.assertContains(response, 'Amt. Bal Towards Employee:</span>\n        <input type="text" class="box3 w-24 px-2 py-1 border border-gray-300 rounded-md bg-gray-100 text-right font-bold"\n               value="0.000" readonly />')

    def test_tour_voucher_create_view_post_submit_success(self):
        initial_voucher_count = TourVoucher.objects.count()
        initial_adv_detail_count = TourVoucherAdvanceDetail.objects.count()
        initial_adv_to_count = TourVoucherAdvanceTo.objects.count()

        data = {
            'advance_details-TOTAL_FORMS': 2,
            'advance_details-INITIAL_FORMS': 2,
            'advance_details-MIN_NUM_FORMS': 0,
            'advance_details-MAX_NUM_FORMS': 1000,
            'advance_details-0-sanctionedamount': '400.00',
            'advance_details-0-remarks': 'Airfare adjusted',
            'advance_details-0-tdmid': self.advance_detail_1.tadid, # Must include original FK ID
            'advance_details-1-sanctionedamount': '250.00',
            'advance_details-1-remarks': 'Hotel stay less',
            'advance_details-1-tdmid': self.advance_detail_2.tadid, # Must include original FK ID

            'advance_to-TOTAL_FORMS': 1,
            'advance_to-INITIAL_FORMS': 1,
            'advance_to-MIN_NUM_FORMS': 0,
            'advance_to-MAX_NUM_FORMS': 1000,
            'advance_to-0-sanctionedamount': '150.00',
            'advance_to-0-remarks': 'Cash advance small',
            'advance_to-0-tamid': self.advance_to_1.tatid, # Must include original FK ID
        }
        response = self.client.post(self.create_url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # HTMX redirect status
        self.assertEqual(response['HX-Redirect'], self.list_url)

        self.assertEqual(TourVoucher.objects.count(), initial_voucher_count + 1)
        new_voucher = TourVoucher.objects.order_by('-id').first()
        self.assertEqual(new_voucher.tmid, self.tour_intimation)
        self.assertEqual(new_voucher.tvno, '0001') # Assuming it's the first one created in this test

        # Total initial advance = 500 + 300 + 200 = 1000
        # Total sanctioned = 400 + 250 + 150 = 800
        # Balance towards employee = 1000 - 800 = 200
        self.assertEqual(new_voucher.amtbaltowardscompany, Decimal('0.000'))
        self.assertEqual(new_voucher.amtbaltowardsemployee, Decimal('200.000'))

        self.assertEqual(TourVoucherAdvanceDetail.objects.count(), initial_adv_detail_count + 2)
        self.assertEqual(TourVoucherAdvanceTo.objects.count(), initial_adv_to_count + 1)
        
        # Verify details
        adv_det_1 = TourVoucherAdvanceDetail.objects.get(mid=new_voucher, tdmid=self.advance_detail_1)
        self.assertEqual(adv_det_1.sanctionedamount, Decimal('400.000'))
        adv_to_1 = TourVoucherAdvanceTo.objects.get(mid=new_voucher, tamid=self.advance_to_1)
        self.assertEqual(adv_to_1.sanctionedamount, Decimal('150.000'))

    def test_tour_voucher_create_view_post_submit_invalid_data(self):
        initial_voucher_count = TourVoucher.objects.count()
        data = {
            'advance_details-TOTAL_FORMS': 2,
            'advance_details-INITIAL_FORMS': 2,
            'advance_details-MIN_NUM_FORMS': 0,
            'advance_details-MAX_NUM_FORMS': 1000,
            'advance_details-0-sanctionedamount': '-100.00', # Invalid negative amount
            'advance_details-0-remarks': 'Invalid',
            'advance_details-0-tdmid': self.advance_detail_1.tadid,
            'advance_details-1-sanctionedamount': '200.00',
            'advance_details-1-remarks': 'Valid',
            'advance_details-1-tdmid': self.advance_detail_2.tadid,

            'advance_to-TOTAL_FORMS': 1,
            'advance_to-INITIAL_FORMS': 1,
            'advance_to-MIN_NUM_FORMS': 0,
            'advance_to-MAX_NUM_FORMS': 1000,
            'advance_to-0-sanctionedamount': '50.00',
            'advance_to-0-remarks': 'Valid',
            'advance_to-0-tamid': self.advance_to_1.tatid,
        }
        response = self.client.post(self.create_url, data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'tourvouchers/tourvoucher_create.html')
        self.assertContains(response, 'Invalid data entry. Please correct the errors.')
        self.assertContains(response, 'Sanctioned amount cannot be negative.')
        self.assertEqual(TourVoucher.objects.count(), initial_voucher_count) # No new voucher created

```