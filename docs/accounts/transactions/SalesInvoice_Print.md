This document outlines a comprehensive plan for modernizing an ASP.NET sales invoice printing module to a modern Django-based solution. The focus is on leveraging AI-assisted automation by providing clear, structured instructions and complete, runnable code examples that adhere to modern Django best practices, including a "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for data presentation.

## ASP.NET to Django Conversion Script: Sales Invoice Print Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (combined with Django's Paginator for server-side paging).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with multiple SQL Server tables to display sales invoice information. The primary table is `tblACC_SalesInvoice_Master`, and it joins with `tblFinancial_master`, `SD_Cust_master`, and `SD_Cust_WorkOrder_Master` for lookup data. Filtering is based on `CompId` and `FinYearId` from the user's session, plus dynamic search criteria.

**Identified Tables and Columns:**

*   **`tblACC_SalesInvoice_Master`**:
    *   `Id` (Primary Key, Integer)
    *   `FinYearId` (Foreign Key, Integer)
    *   `SysDate` (DateTime)
    *   `InvoiceNo` (String)
    *   `WONo` (String, contains comma-separated Work Order IDs)
    *   `PONo` (String)
    *   `CustomerCode` (Foreign Key, String/Integer)
    *   `CompId` (Integer)
*   **`tblFinancial_master`**:
    *   `FinYearId` (Primary Key, Integer)
    *   `FinYear` (String)
*   **`SD_Cust_master`**:
    *   `CustomerId` (Primary Key, String/Integer)
    *   `CustomerName` (String)
    *   `CompId` (Integer)
*   **`SD_Cust_WorkOrder_Master`**:
    *   `Id` (Primary Key, Integer)
    *   `WONo` (String)
    *   `CompId` (Integer)

### Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page's primary function is to list sales invoices based on search criteria and allow navigation to a detailed print view. It does not perform standard "Create, Update, Delete" (CRUD) operations on the `SalesInvoice_Master` entity itself, but rather "Read" (search and display) and a "Navigate" (to print details) action.

*   **Read (Sales Invoice List):**
    *   Fetches `Id`, `FinYearId`, `SysDate`, `InvoiceNo`, `WONo`, `PONo`, `CustomerCode` from `tblACC_SalesInvoice_Master`.
    *   Filters by `CompId` and `FinYearId` (sourced from user session data).
    *   Applies dynamic filters for `CustomerCode`, `PONo`, or `InvoiceNo` based on user selection in the dropdown.
    *   Performs lookups for `FinYear` (from `tblFinancial_master`), `CustomerName` (from `SD_Cust_master`), and concatenates `WONo` values (from `SD_Cust_WorkOrder_Master`) based on comma-separated IDs.
    *   Implements server-side pagination.
*   **Read (Customer Autocomplete):**
    *   Fetches `CustomerId` and `CustomerName` from `SD_Cust_master`.
    *   Filters by `CompId` and `CustomerName` starting with the user's input (`prefixText`).
    *   Returns results in "CustomerName [CustomerId]" format.
*   **Navigate (Print Details):**
    *   When a "Select" button is clicked in a row, it captures the Invoice ID, Customer Code, and selected Print Type.
    *   Redirects the user to a separate "Sales Invoice Print Details" page, passing these parameters via query string.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET page uses standard Web Forms controls for user interaction and data display.

*   **Search/Filter Section:**
    *   A dropdown (`DropDownList1`) to select the search criteria (Customer Name, PO No, Invoice No).
    *   Two text boxes (`txtCustName`, `txtpoNo`) whose visibility is toggled by the dropdown selection. `txtCustName` is augmented with `AutoCompleteExtender`.
    *   A "Search" button (`btnSearch`) to trigger data retrieval.
*   **Data Display Grid:**
    *   A `GridView` (`GridView1`) is used to display the paginated list of sales invoices.
    *   Each row includes: Serial Number, a "Select" `LinkButton`, a `DropDownList` for "Print Type", and `Label` controls to display Invoice No, Date, Customer Name, WO No, and PO No.
    *   Pagination controls are automatically generated by the `GridView`.

---

## Step 4: Generate Django Code

We will create a new Django application, for example, named `accounts`, to house this module.

### 4.1 Models (`accounts/models.py`)

The models will mirror the database tables with `managed = False` as per guidelines. Complex data fetching and processing logic from the ASP.NET code-behind (e.g., fetching `FinYear`, `CustomerName`, and concatenating `WONo`s) will be encapsulated as methods within the `SalesInvoiceMaster` model or a custom manager.

```python
# accounts/models.py
from django.db import models

class FinancialMaster(models.Model):
    """
    Maps to tblFinancial_master.
    Contains financial year details.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=200)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class CustomerMaster(models.Model):
    """
    Maps to SD_Cust_master.
    Contains customer details.
    """
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50) 
    customer_name = models.CharField(db_column='CustomerName', max_length=200)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class WorkOrderMaster(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master.
    Contains work order details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=200)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wo_no

class SalesInvoiceManager(models.Manager):
    """
    Custom manager for SalesInvoiceMaster to encapsulate complex filtering.
    """
    def get_sales_invoices_for_display(self, comp_id, fin_year_id, search_type=None, search_value=None):
        queryset = self.get_queryset().filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id # Matching ASP.NET's FinYearId <= current FinYearId
        )

        if search_value:
            if search_type == 'customer_code': # From autocomplete selection
                queryset = queryset.filter(customer_id=search_value)
            elif search_type == 'customer_name': # From manual text input
                # This assumes customer_name in SalesInvoiceMaster.customer foreign key.
                # Since customer_code is the actual FK, we'll try to find customer_id by name.
                customer_ids = CustomerMaster.objects.filter(
                    customer_name__icontains=search_value,
                    comp_id=comp_id
                ).values_list('customer_id', flat=True)
                queryset = queryset.filter(customer_id__in=list(customer_ids))
            elif search_type == 'po_no':
                queryset = queryset.filter(po_no__icontains=search_value)
            elif search_type == 'invoice_no':
                queryset = queryset.filter(invoice_no__icontains=search_value)

        return queryset.order_by('-id')

class SalesInvoiceMaster(models.Model):
    """
    Maps to tblACC_SalesInvoice_Master.
    Represents the main sales invoice master data.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year = models.ForeignKey(FinancialMaster, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='sales_invoices')
    sys_date = models.DateTimeField(db_column='SysDate')
    invoice_no = models.CharField(db_column='InvoiceNo', max_length=200)
    wo_no_ids = models.CharField(db_column='WONo', max_length=500, blank=True, null=True) # Stores comma-separated WO_IDs
    po_no = models.CharField(db_column='PONo', max_length=200, blank=True, null=True)
    customer = models.ForeignKey(CustomerMaster, on_delete=models.DO_NOTHING, db_column='CustomerCode', related_name='sales_invoices')
    comp_id = models.IntegerField(db_column='CompId')

    objects = SalesInvoiceManager()

    class Meta:
        managed = False
        db_table = 'tblACC_SalesInvoice_Master'
        verbose_name = 'Sales Invoice'
        verbose_name_plural = 'Sales Invoices'

    def __str__(self):
        return self.invoice_no
    
    @property
    def formatted_sys_date(self):
        """Formats SysDate to DD/MM/YYYY as seen in ASP.NET code (fun.FromDateDMY)."""
        return self.sys_date.strftime('%d/%m/%Y')

    @property
    def customer_display_name(self):
        """Returns customer name with ID as seen in ASP.NET code."""
        # Use select_related('customer') in queryset for efficiency
        return f"{self.customer.customer_name} [{self.customer.customer_id}]"

    def get_concatenated_wo_nos(self, current_comp_id):
        """
        Retrieves and concatenates Work Order Numbers from SD_Cust_WorkOrder_Master.
        This mimics the C# loop logic.
        """
        if not self.wo_no_ids:
            return ""
        
        wo_id_list = [int(wo_id.strip()) for wo_id in self.wo_no_ids.split(',') if wo_id.strip()]
        
        if not wo_id_list:
            return ""

        wo_numbers = WorkOrderMaster.objects.filter(
            id__in=wo_id_list, 
            comp_id=current_comp_id # Filter by current CompId from session
        ).values_list('wo_no', flat=True)
        
        return ",".join(wo_numbers)

    @classmethod
    def get_customer_autocomplete_suggestions(cls, prefix_text, comp_id):
        """
        Provides customer name suggestions for autocomplete.
        Mimics the `sql` WebMethod.
        """
        customers = CustomerMaster.objects.filter(
            comp_id=comp_id,
            customer_name__istartswith=prefix_text
        ).order_by('customer_name') 
        
        return [f"{c.customer_name} [{c.customer_id}]" for c in customers]

```

### 4.2 Forms (`accounts/forms.py`)

A Django `Form` will be used for the search/filter criteria. This form will not be a `ModelForm` as it's purely for input filtering, not direct model creation/update.

```python
# accounts/forms.py
from django import forms

class SalesInvoiceSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Customer Name'),
        ('2', 'PO No'),
        ('3', 'Invoice No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3'}),
        initial='0'
    )
    search_value = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'placeholder': 'Enter search value'})
    )
    
    # Hidden field to store selected customer ID from autocomplete.
    # This value will be passed from the frontend for customer-code based search.
    customer_id_hidden = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.HiddenInput()
    )

    def clean(self):
        cleaned_data = super().clean()
        search_by = cleaned_data.get('search_by')
        search_value = cleaned_data.get('search_value')
        customer_id_hidden = cleaned_data.get('customer_id_hidden')

        # Determine the actual search type and value to be used by the model manager
        if search_by == '0': # Customer Name
            if customer_id_hidden:
                # If a hidden customer ID is provided (from autocomplete selection), use it
                cleaned_data['actual_search_value'] = customer_id_hidden
                cleaned_data['actual_search_type'] = 'customer_code'
            else:
                # If only search_value (typed text) is present, search by customer name
                cleaned_data['actual_search_value'] = search_value
                cleaned_data['actual_search_type'] = 'customer_name'
        elif search_by == '2': # PO No
            cleaned_data['actual_search_value'] = search_value
            cleaned_data['actual_search_type'] = 'po_no'
        elif search_by == '3': # Invoice No
            cleaned_data['actual_search_value'] = search_value
            cleaned_data['actual_search_type'] = 'invoice_no'
        else: # Default or 'Select'
            cleaned_data['actual_search_value'] = None
            cleaned_data['actual_search_type'] = None

        return cleaned_data

```

### 4.3 Views (`accounts/views.py`)

Views will be lean, primarily handling request dispatch and passing control to models or forms. Session data for `CompId` and `FinYearId` will be mimicked for the migration context.

```python
# accounts/views.py
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from .models import SalesInvoiceMaster, CustomerMaster
from .forms import SalesInvoiceSearchForm

# Helper to simulate ASP.NET session data. In a real app, use request.user.
def get_session_data(request):
    """Retrieves session-dependent CompId and FinYearId."""
    comp_id = request.session.get('compid', 1) 
    fin_year_id = request.session.get('finyear', 1) 
    return comp_id, fin_year_id

class SalesInvoicePrintView(TemplateView):
    """
    Main view for the Sales Invoice Print page.
    Renders the search form and sets up the container for the HTMX-loaded table.
    """
    template_name = 'accounts/salesinvoices/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form with GET data for sticky filters
        context['search_form'] = SalesInvoiceSearchForm(self.request.GET or None)
        return context

class SalesInvoiceTablePartialView(ListView):
    """
    HTMX-targeted view to render the sales invoice table dynamically.
    Handles searching and pagination.
    """
    model = SalesInvoiceMaster
    template_name = 'accounts/salesinvoices/_salesinvoice_table.html'
    context_object_name = 'sales_invoices'
    paginate_by = 17 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        comp_id, fin_year_id = get_session_data(self.request)
        
        form = SalesInvoiceSearchForm(self.request.GET)
        search_type = None
        search_value = None
        if form.is_valid():
            search_type = form.cleaned_data.get('actual_search_type')
            search_value = form.cleaned_data.get('actual_search_value')

        queryset = SalesInvoiceMaster.objects.get_sales_invoices_for_display(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            search_type=search_type,
            search_value=search_value
        ).select_related('fin_year', 'customer') # Optimize lookups for related objects
        
        return queryset

class CustomerAutocompleteView(View):
    """
    AJAX endpoint for customer name autocomplete functionality.
    Returns JSON data for HTMX/Alpine.js to consume.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '') # 'term' is a common param for autocomplete
        comp_id, _ = get_session_data(request)

        suggestions = SalesInvoiceMaster.get_customer_autocomplete_suggestions(prefix_text, comp_id)
        
        return JsonResponse(suggestions, safe=False)

class SalesInvoicePrintDetailsRedirectView(View):
    """
    Handles the 'Select' action from the table, mimicking the ASP.NET redirect.
    It will redirect the browser to a mock print details page.
    """
    def get(self, request, *args, **kwargs):
        invoice_id = kwargs.get('pk')
        print_type = request.GET.get('pt') 
        
        try:
            # Verify the invoice exists before attempting to redirect
            SalesInvoiceMaster.objects.get(id=invoice_id)
        except SalesInvoiceMaster.DoesNotExist:
            messages.error(request, "Selected invoice not found.")
            # For HTMX requests, a 404 with a message is appropriate
            if request.headers.get('HX-Request'):
                return HttpResponse(status=404, content="Selected invoice not found.")
            # For non-HTMX, redirect to list view
            return redirect(reverse('accounts:salesinvoice_list')) 

        messages.success(request, f"Preparing print for Invoice ID: {invoice_id} with Print Type: {print_type}")
        
        # Construct the URL for the mock print details page
        redirect_url = reverse('accounts:salesinvoice_print_details_mock', kwargs={'pk': invoice_id, 'pt': print_type})
        
        # Use HX-Redirect header for HTMX to perform a full page navigation
        return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})

class SalesInvoicePrintDetailsMockView(TemplateView):
    """
    A mock view to simulate the SalesInvoice_Print_Details.aspx page.
    In a real scenario, this would render the actual printable invoice.
    """
    template_name = 'accounts/salesinvoices/print_details_mock.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['invoice_id'] = self.kwargs['pk']
        context['print_type'] = self.kwargs['pt']
        # In a real application, you would fetch actual invoice data here
        return context

```

### 4.4 Templates

Templates will be structured to support HTMX partials for dynamic updates and Alpine.js for frontend UI logic, while inheriting from a base template (`core/base.html`).

```html
<!-- accounts/templates/accounts/salesinvoices/list.html -->
{% extends 'core/base.html' %}

{% block title %}Sales Invoice - Print{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sales Invoice - Print</h2>
    </div>

    <!-- Search Form -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <form hx-get="{% url 'accounts:salesinvoice_table_partial' %}" hx-target="#salesInvoiceTable-container" hx-swap="innerHTML">
            <div class="flex flex-col sm:flex-row items-center space-y-4 sm:space-y-0 sm:space-x-4">
                <div>
                    <label for="id_search_by" class="sr-only">Search By</label>
                    <select class="box3" name="search_by" id="id_search_by" 
                            x-data="{ searchBy: '{{ search_form.search_by.value|default:'0' }}' }" 
                            x-ref="searchBySelect" x-model="searchBy">
                        {% for value, label in search_form.search_by.field.choices %}
                            <option value="{{ value }}">{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="flex-grow w-full sm:w-auto">
                    <div x-data="customerAutocomplete" x-cloak x-show="searchBy === '0'" class="relative" @click.outside="closeResults()">
                        <label for="id_search_value_customer" class="sr-only">Customer Name</label>
                        <input type="text" 
                            name="search_value" 
                            id="id_search_value_customer" 
                            class="box3 w-full" 
                            placeholder="Enter Customer Name"
                            value="{{ search_form.search_value.value|default:'' }}"
                            autocomplete="off"
                            hx-get="{% url 'accounts:customer_autocomplete' %}"
                            hx-trigger="keyup changed delay:500ms from:#id_search_value_customer"
                            hx-swap="none" {# Alpine.js processes the JSON response #}
                            hx-on::after-request="if(event.detail.successful) { results = JSON.parse(event.detail.xhr.responseText); showResults = true; }"
                            hx-indicator="#autocomplete-spinner">
                        <input type="hidden" name="customer_id_hidden" x-ref="customer_id_hidden" value="{{ search_form.customer_id_hidden.value|default:'' }}">
                        
                        <div x-show="showResults && results.length > 0" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full mt-1 max-h-60 overflow-y-auto">
                            <ul class="list-none p-0 m-0">
                                <template x-for="result in results" :key="result">
                                    <li class="py-2 px-3 hover:bg-gray-100 cursor-pointer text-gray-800" 
                                        @click="selectCustomer(result)">
                                        <span x-text="result"></span>
                                    </li>
                                </template>
                            </ul>
                        </div>
                        <span id="autocomplete-spinner" class="htmx-indicator absolute right-3 top-1/2 -translate-y-1/2">
                            <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                        </span>
                    </div>

                    <div x-cloak x-show="searchBy === '2' || searchBy === '3'">
                        <label for="id_search_value_po_inv" class="sr-only" x-text="searchBy === '2' ? 'PO No' : 'Invoice No'"></label>
                        <input type="text" 
                            name="search_value" 
                            id="id_search_value_po_inv" 
                            class="box3 w-full" 
                            x-bind:placeholder="searchBy === '2' ? 'Enter PO No' : 'Enter Invoice No'"
                            value="{{ search_form.search_value.value|default:'' }}">
                    </div>
                </div>
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">Search</button>
            </div>
        </form>
    </div>

    <!-- Data Table Container -->
    <div id="salesInvoiceTable-container"
         hx-trigger="load, reloadSalesInvoiceList from:body"
         hx-get="{% url 'accounts:salesinvoice_table_partial' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML">
        <!-- Initial loading state, replaced by actual table via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-700">Loading sales invoices...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('customerAutocomplete', () => ({
            results: [],
            showResults: false,
            selectCustomer(fullText) {
                const customerName = fullText.split(' [')[0];
                const customerId = fullText.split('[')[1].replace(']', '');
                
                document.getElementById('id_search_value_customer').value = fullText;
                this.$refs.customer_id_hidden.value = customerId;
                this.showResults = false;
            },
            closeResults() {
                // Delay to allow click events on result items to register before hiding
                setTimeout(() => {
                    this.showResults = false;
                }, 100);
            }
        }));
    });
</script>
{% endblock %}
```

```html
<!-- accounts/templates/accounts/salesinvoices/_salesinvoice_table.html -->
{% load humanize %} 

<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="salesInvoiceTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Print Type</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
            </tr>
        </thead>
        <tbody>
            {% if sales_invoices %}
                {% for invoice in sales_invoices %}
                <tr x-data="{ printType: 'ORIGINAL FOR BUYER' }" class="hover:bg-gray-50">
                    <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ forloop.counter|add:page_obj.start_index|add:-1 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-sm"
                                hx-get="{% url 'accounts:salesinvoice_print_redirect' pk=invoice.id %}?pt={{ printType }}"
                                hx-target="body" hx-swap="none"
                                hx-trigger="click"
                                hx-indicator="#global-spinner" {# Global spinner in base.html #}
                        >
                            Select
                        </button>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 align-top">
                        <select class="box3 w-full text-sm" x-model="printType">
                            <option value="ORIGINAL FOR BUYER">ORIGINAL FOR BUYER</option>
                            <option value="DUPLICATE FOR TRANSPORTER">DUPLICATE FOR TRANSPORTER</option>
                            <option value="TRIPLICATE FOR ASSESSEE">TRIPLICATE FOR ASSESSEE</option>
                            <option value="EXTRA COPY">EXTRA COPY [NOT FOR CENVAT PURPOSE]</option>
                        </select>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ invoice.fin_year.fin_year }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ invoice.invoice_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ invoice.formatted_sys_date }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-700">{{ invoice.customer_display_name }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ invoice.get_concatenated_wo_nos(invoice.comp_id) }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ invoice.po_no }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-4 text-center text-lg text-red-700 font-semibold">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# Pagination controls, using HTMX for partial updates #}
<div class="mt-4 flex flex-col sm:flex-row justify-between items-center text-sm text-gray-700">
    <div class="mb-2 sm:mb-0">
        Page {{ page_obj.number }} of {{ page_obj.num_pages }}.
    </div>
    <div class="pagination flex space-x-2">
        {% if page_obj.has_previous %}
            <button class="px-3 py-1 rounded-md bg-gray-200 hover:bg-gray-300 transition"
                    hx-get="{% url 'accounts:salesinvoice_table_partial' %}?page={{ page_obj.previous_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}"
                    hx-target="#salesInvoiceTable-container" hx-swap="innerHTML">
                Previous
            </button>
        {% endif %}

        {% for i in paginator.page_range %}
            {% if page_obj.number == i %}
                <span class="px-3 py-1 rounded-md bg-blue-500 text-white font-medium">{{ i }}</span>
            {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %} 
                <button class="px-3 py-1 rounded-md bg-gray-200 hover:bg-gray-300 transition"
                        hx-get="{% url 'accounts:salesinvoice_table_partial' %}?page={{ i }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}"
                        hx-target="#salesInvoiceTable-container" hx-swap="innerHTML">
                    {{ i }}
                </button>
            {% endif %}
        {% endfor %}

        {% if page_obj.has_next %}
            <button class="px-3 py-1 rounded-md bg-gray-200 hover:bg-gray-300 transition"
                    hx-get="{% url 'accounts:salesinvoice_table_partial' %}?page={{ page_obj.next_page_number }}{% if request.GET.urlencode %}&{{ request.GET.urlencode }}{% endif %}"
                    hx-target="#salesInvoiceTable-container" hx-swap="innerHTML">
                Next
            </button>
        {% endif %}
    </div>
</div>

<script>
    // DataTables initialization
    $(document).ready(function() {
        // Destroy existing DataTable instance to prevent re-initialization errors on HTMX swap
        if ($.fn.DataTable.isDataTable('#salesInvoiceTable')) {
            $('#salesInvoiceTable').DataTable().destroy();
        }
        $('#salesInvoiceTable').DataTable({
            "paging": false,    // Handled by Django's Paginator and HTMX
            "searching": false, // Handled by Django search form
            "info": false,      // Disable DataTables info display
            "columnDefs": [
                { "orderable": false, "targets": [1, 2] } // Disable sorting for 'Select' and 'Print Type'
            ],
            "language": {
                "emptyTable": "No data to display !" // Custom message for empty table
            }
        });
    });
</script>
```

```html
<!-- accounts/templates/accounts/salesinvoices/print_details_mock.html -->
{% extends 'core/base.html' %}

{% block title %}Sales Invoice Print Details{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-4 text-gray-800">Sales Invoice Print Details (Mock)</h2>
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <p class="text-lg text-gray-700">Invoice ID: <span class="font-semibold text-gray-900">{{ invoice_id }}</span></p>
        <p class="text-lg text-gray-700">Print Type: <span class="font-semibold text-gray-900">{{ print_type }}</span></p>
        <p class="mt-4 text-gray-600">
            This is a mock page for the "Sales Invoice Print Details". 
            In a real application, this would render the actual invoice document, 
            potentially as a PDF or a comprehensive HTML report based on the selected invoice and print type.
        </p>
        <div class="mt-6">
            <a href="{% url 'accounts:salesinvoice_list' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded transition">
                Back to Sales Invoice Print
            </a>
        </div>
    </div>
</div>
{% endblock %}

```

### 4.5 URLs (`accounts/urls.py`)

URL patterns are defined for the main page, the HTMX-powered table partial, the autocomplete endpoint, and the print redirection handler.

```python
# accounts/urls.py
from django.urls import path
from .views import (
    SalesInvoicePrintView,
    SalesInvoiceTablePartialView,
    CustomerAutocompleteView,
    SalesInvoicePrintDetailsRedirectView,
    SalesInvoicePrintDetailsMockView,
)

app_name = 'accounts' # Define app_name for namespacing URLs

urlpatterns = [
    # Main page for Sales Invoice Print functionality
    path('salesinvoice/print/', SalesInvoicePrintView.as_view(), name='salesinvoice_list'),
    
    # HTMX endpoint for the sales invoice table (for search and pagination)
    path('salesinvoice/print/table/', SalesInvoiceTablePartialView.as_view(), name='salesinvoice_table_partial'),
    
    # HTMX endpoint for customer autocomplete
    path('salesinvoice/print/autocomplete/customer/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Endpoint to handle the "Select" action and redirect to print details (simulated)
    # The 'pt' (Print Type) is passed as a query parameter
    path('salesinvoice/print/details-redirect/<int:pk>/', SalesInvoicePrintDetailsRedirectView.as_view(), name='salesinvoice_print_redirect'),
    
    # Mock endpoint for the actual SalesInvoice_Print_Details.aspx equivalent
    # The print type is included in the URL for clarity in this mock.
    path('salesinvoice/print/details/<int:pk>/<str:pt>/', SalesInvoicePrintDetailsMockView.as_view(), name='salesinvoice_print_details_mock'),
]

```

### 4.6 Tests (`accounts/tests.py`)

Comprehensive unit tests for model methods and integration tests for all view interactions, including HTMX-specific behavior, ensure reliability and maintainability.

```python       
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.test.utils import setup_test_environment

from .models import SalesInvoiceMaster, FinancialMaster, CustomerMaster, WorkOrderMaster
from .views import SalesInvoiceTablePartialView # For mocking paginate_by

setup_test_environment() # Ensures test environment is set up

class SalesInvoiceModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all models
        cls.financial_year_2023 = FinancialMaster.objects.create(fin_year_id=1, fin_year='2023-24')
        cls.customer_alpha = CustomerMaster.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=1)
        cls.customer_beta = CustomerMaster.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=1)
        cls.work_order_1 = WorkOrderMaster.objects.create(id=1, wo_no='WO/2023/001', comp_id=1)
        cls.work_order_2 = WorkOrderMaster.objects.create(id=2, wo_no='WO/2023/002', comp_id=1)
        
        # Create sales invoices with various scenarios
        SalesInvoiceMaster.objects.create(
            id=1,
            fin_year=cls.financial_year_2023,
            sys_date='2023-10-26 10:00:00',
            invoice_no='INV/2023/001',
            wo_no_ids=f'{cls.work_order_1.id},{cls.work_order_2.id},', # Mimic ASP.NET comma-separated with trailing comma
            po_no='PO/ABC/123',
            customer=cls.customer_alpha,
            comp_id=1
        )
        SalesInvoiceMaster.objects.create(
            id=2,
            fin_year=cls.financial_year_2023,
            sys_date='2023-10-27 11:00:00',
            invoice_no='INV/2023/002',
            wo_no_ids='', # No work orders
            po_no='PO/XYZ/456',
            customer=cls.customer_beta,
            comp_id=1
        )
        SalesInvoiceMaster.objects.create(
            id=3,
            fin_year=cls.financial_year_2023,
            sys_date='2023-10-28 12:00:00',
            invoice_no='INV/2023/003',
            wo_no_ids=f'{cls.work_order_1.id},',
            po_no='PO/LMN/789',
            customer=cls.customer_alpha,
            comp_id=2 # Different company ID, should not appear in default search
        )

    def test_invoice_creation(self):
        """Test that an invoice instance is correctly created."""
        invoice = SalesInvoiceMaster.objects.get(id=1)
        self.assertEqual(invoice.invoice_no, 'INV/2023/001')
        self.assertEqual(invoice.customer.customer_name, 'Alpha Corp')
        self.assertEqual(invoice.fin_year.fin_year, '2023-24')

    def test_formatted_sys_date_property(self):
        """Test the date formatting property."""
        invoice = SalesInvoiceMaster.objects.get(id=1)
        self.assertEqual(invoice.formatted_sys_date, '26/10/2023')

    def test_customer_display_name_property(self):
        """Test the customer display name property."""
        invoice = SalesInvoiceMaster.objects.get(id=1)
        self.assertEqual(invoice.customer_display_name, 'Alpha Corp [CUST001]')

    def test_get_concatenated_wo_nos(self):
        """Test the method that concatenates work order numbers."""
        invoice = SalesInvoiceMaster.objects.get(id=1)
        self.assertEqual(invoice.get_concatenated_wo_nos(current_comp_id=1), 'WO/2023/001,WO/2023/002')

        invoice_no_wo = SalesInvoiceMaster.objects.get(id=2)
        self.assertEqual(invoice_no_wo.get_concatenated_wo_nos(current_comp_id=1), '')
        
        invoice_single_wo = SalesInvoiceMaster.objects.get(id=3)
        self.assertEqual(invoice_single_wo.get_concatenated_wo_nos(current_comp_id=2), 'WO/2023/001')
        self.assertEqual(invoice_single_wo.get_concatenated_wo_nos(current_comp_id=1), '') # WO not found for this comp_id

    def test_get_customer_autocomplete_suggestions(self):
        """Test the customer autocomplete suggestion method."""
        suggestions = SalesInvoiceMaster.get_customer_autocomplete_suggestions('alp', comp_id=1)
        self.assertIn('Alpha Corp [CUST001]', suggestions)
        self.assertNotIn('Beta Industries [CUST002]', suggestions) 
        self.assertEqual(len(suggestions), 1)

        suggestions_all = SalesInvoiceMaster.get_customer_autocomplete_suggestions('', comp_id=1)
        self.assertIn('Alpha Corp [CUST001]', suggestions_all)
        self.assertIn('Beta Industries [CUST002]', suggestions_all)
        self.assertEqual(len(suggestions_all), 2)
        
        suggestions_empty = SalesInvoiceMaster.get_customer_autocomplete_suggestions('xyz', comp_id=1)
        self.assertEqual(len(suggestions_empty), 0)

class SalesInvoiceViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set up a session for the client
        self.middleware = SessionMiddleware(lambda r: None)
        self.middleware.process_request(self.client.request().request)
        self.client.request().request.session.save()
        
        # Populate session with dummy data mimicking ASP.NET Session values
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 1
        self.client.session.save()

        # Create test data (similar to ModelTest for consistency)
        self.financial_year_2023 = FinancialMaster.objects.create(fin_year_id=1, fin_year='2023-24')
        self.customer_alpha = CustomerMaster.objects.create(customer_id='CUST001', customer_name='Alpha Corp', comp_id=1)
        self.customer_beta = CustomerMaster.objects.create(customer_id='CUST002', customer_name='Beta Industries', comp_id=1)
        self.work_order_1 = WorkOrderMaster.objects.create(id=1, wo_no='WO/2023/001', comp_id=1)
        self.work_order_2 = WorkOrderMaster.objects.create(id=2, wo_no='WO/2023/002', comp_id=1)
        
        self.invoice1 = SalesInvoiceMaster.objects.create(
            id=1,
            fin_year=self.financial_year_2023,
            sys_date='2023-10-26 10:00:00',
            invoice_no='INV/2023/001',
            wo_no_ids=f'{self.work_order_1.id},{self.work_order_2.id},',
            po_no='PO/ABC/123',
            customer=self.customer_alpha,
            comp_id=1
        )
        self.invoice2 = SalesInvoiceMaster.objects.create(
            id=2,
            fin_year=self.financial_year_2023,
            sys_date='2023-10-27 11:00:00',
            invoice_no='INV/2023/002',
            wo_no_ids='',
            po_no='PO/XYZ/456',
            customer=self.customer_beta,
            comp_id=1
        )
        self.invoice3 = SalesInvoiceMaster.objects.create(
            id=3,
            fin_year=self.financial_year_2023,
            sys_date='2023-10-28 12:00:00',
            invoice_no='INV/2023/003',
            wo_no_ids=f'{self.work_order_1.id},',
            po_no='PO/LMN/789',
            customer=self.customer_alpha,
            comp_id=2 # Different company ID
        )

    def test_salesinvoice_list_view_get(self):
        """Test the main sales invoice list page renders correctly."""
        response = self.client.get(reverse('accounts:salesinvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/salesinvoices/list.html')
        self.assertContains(response, 'Sales Invoice - Print')
        # Check if the search form is present
        self.assertContains(response, '<form hx-get="')

    def test_salesinvoice_table_partial_view_get_initial_load(self):
        """Test the HTMX partial for the table, showing initial data."""
        response = self.client.get(reverse('accounts:salesinvoice_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/salesinvoices/_salesinvoice_table.html')
        self.assertContains(response, self.invoice1.invoice_no)
        self.assertContains(response, self.invoice2.invoice_no)
        self.assertNotContains(response, self.invoice3.invoice_no) # Should not be included due to comp_id

    def test_salesinvoice_table_partial_view_search_customer_code(self):
        """Test searching by customer ID from autocomplete."""
        # Simulate selection from autocomplete which sends customer_id
        response = self.client.get(reverse('accounts:salesinvoice_table_partial'), 
                                   {'search_by': '0', 'customer_id_hidden': self.customer_alpha.customer_id})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice1.invoice_no)
        self.assertNotContains(response, self.invoice2.invoice_no)
        self.assertContains(response, 'Alpha Corp [CUST001]')

    def test_salesinvoice_table_partial_view_search_customer_name_typed(self):
        """Test searching by customer name typed manually."""
        response = self.client.get(reverse('accounts:salesinvoice_table_partial'), 
                                   {'search_by': '0', 'search_value': 'Alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice1.invoice_no)
        self.assertNotContains(response, self.invoice2.invoice_no)
        self.assertContains(response, 'Alpha Corp [CUST001]')

    def test_salesinvoice_table_partial_view_search_po_no(self):
        """Test searching by PO Number."""
        response = self.client.get(reverse('accounts:salesinvoice_table_partial'), 
                                   {'search_by': '2', 'search_value': 'PO/ABC/123'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice1.invoice_no)
        self.assertNotContains(response, self.invoice2.invoice_no)

    def test_salesinvoice_table_partial_view_search_invoice_no(self):
        """Test searching by Invoice Number."""
        response = self.client.get(reverse('accounts:salesinvoice_table_partial'), 
                                   {'search_by': '3', 'search_value': 'INV/2023/002'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.invoice2.invoice_no)
        self.assertNotContains(response, self.invoice1.invoice_no)

    def test_salesinvoice_table_partial_view_pagination(self):
        """Test pagination functionality for the table."""
        # Create enough invoices to span multiple pages
        for i in range(4, 20): # Add 16 more invoices, total 18 for compid 1
            SalesInvoiceMaster.objects.create(
                id=i, fin_year=self.financial_year_2023, sys_date=f'2023-11-{i:02d}',
                invoice_no=f'INV/2023/{i:03d}', wo_no_ids='', po_no=f'PO/TEST/{i:03d}',
                customer=self.customer_alpha, comp_id=1
            )
        
        # Test first page
        response = self.client.get(reverse('accounts:salesinvoice_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Page 1 of 2') # 18 items, paginate_by=17
        self.assertContains(response, 'Next')
        self.assertNotContains(response, 'Previous')

        # Test second page
        response = self.client.get(reverse('accounts:salesinvoice_table_partial'), {'page': 2})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Page 2 of 2')
        self.assertNotContains(response, 'Next')
        self.assertContains(response, 'Previous')

    def test_customer_autocomplete_view(self):
        """Test the customer autocomplete AJAX endpoint."""
        response = self.client.get(reverse('accounts:customer_autocomplete'), {'term': 'al'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIn('Alpha Corp [CUST001]', data)
        self.assertNotIn('Beta Industries [CUST002]', data) 

        response = self.client.get(reverse('accounts:customer_autocomplete'), {'term': 'beta'})
        data = response.json()
        self.assertIn('Beta Industries [CUST002]', data)
        self.assertNotIn('Alpha Corp [CUST001]', data)

    def test_salesinvoice_print_details_redirect_view(self):
        """Test the redirect view for print details (HTMX-enabled)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        print_type = 'ORIGINAL FOR BUYER'
        response = self.client.get(reverse('accounts:salesinvoice_print_redirect', kwargs={'pk': self.invoice1.id}) + f'?pt={print_type}', **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX successful no content
        self.assertIn('HX-Redirect', response.headers)
        expected_redirect_url = reverse('accounts:salesinvoice_print_details_mock', kwargs={'pk': self.invoice1.id, 'pt': print_type})
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)
        
    def test_salesinvoice_print_details_redirect_view_invoice_not_found(self):
        """Test redirect view handles non-existent invoice gracefully."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('accounts:salesinvoice_print_redirect', kwargs={'pk': 999}) + '?pt=ANY', **headers)
        self.assertEqual(response.status_code, 404)
        self.assertEqual(response.content.decode(), "Selected invoice not found.")

    def test_salesinvoice_print_details_mock_view(self):
        """Test the mock print details page renders correctly."""
        print_type = 'DUPLICATE FOR TRANSPORTER'
        response = self.client.get(reverse('accounts:salesinvoice_print_details_mock', kwargs={'pk': self.invoice1.id, 'pt': print_type}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/salesinvoices/print_details_mock.html')
        self.assertContains(response, f'Invoice ID: {self.invoice1.id}')
        self.assertContains(response, f'Print Type: {print_type}')

```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions and Implementation:**

*   **Dynamic Table Loading:** The main `SalesInvoicePrintView` (`list.html`) contains a `div` (`#salesInvoiceTable-container`) that uses `hx-trigger="load, reloadSalesInvoiceList from:body"` and `hx-get="{% url 'accounts:salesinvoice_table_partial' %}"`. This ensures the table is loaded on page load and can be refreshed by a custom HTMX event `reloadSalesInvoiceList` (e.g., after a successful search or when new data is available).
*   **Search Form Submission:** The search form uses `hx-get` to trigger a request to `salesinvoice_table_partial` and `hx-target="#salesInvoiceTable-container"` with `hx-swap="innerHTML"`. This replaces only the table content, avoiding full page reloads.
*   **Pagination:** Pagination links within `_salesinvoice_table.html` also use `hx-get` to the `salesinvoice_table_partial` view, passing the new page number, and targeting `#salesInvoiceTable-container` for seamless page navigation without full reloads.
*   **Customer Autocomplete:**
    *   The `txtCustName` input in `list.html` uses `hx-get="{% url 'accounts:customer_autocomplete' %}"` with `hx-trigger="keyup changed delay:500ms"`.
    *   `hx-swap="none"` is crucial here because the `CustomerAutocompleteView` returns JSON, not HTML.
    *   `hx-on::after-request="if(event.detail.successful) { results = JSON.parse(event.detail.xhr.responseText); showResults = true; }"` is an HTMX extension that allows processing the JSON response and updating the Alpine.js `results` array and `showResults` flag.
    *   Alpine.js (`x-data="customerAutocomplete"`) dynamically renders the autocomplete suggestions based on the `results` array and manages their visibility. It also handles the `selectCustomer` action to populate the input and a hidden field with the actual customer ID, facilitating precise search queries.
*   **Print Type Dropdown & Select Button:** Each row's "Print Type" dropdown is managed by Alpine.js (`x-data="{ printType: '...' }"`) using `x-model="printType"`. When the "Select" button is clicked, it uses `hx-get` to `salesinvoice_print_redirect`, passing the selected `printType` as a query parameter.
*   **Print Redirect (HTMX):** The `SalesInvoicePrintDetailsRedirectView` responds with `status=204` (No Content) and an `HX-Redirect` header. HTMX automatically detects this header and performs a full browser redirect to the specified URL, mimicking the ASP.NET `Response.Redirect`.
*   **DataTables Integration:** `_salesinvoice_table.html` includes a `<script>` block to initialize DataTables on the loaded table. Importantly, `paging: false` and `searching: false` are set as these functionalities are handled by Django's server-side pagination and the search form/HTMX. This ensures DataTables only provides client-side sorting and visual enhancements. The `destroy()` method is used before re-initialization to prevent errors when HTMX swaps in new table content.

---

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET Sales Invoice Print module to Django. By adhering to the specified architectural patterns and leveraging modern frontend techniques, the resulting application will be more performant, maintainable, and scalable, offering significant business benefits through a more responsive and streamlined user experience. The emphasis on automation and clear instructions ensures that this migration can be executed effectively, potentially with AI-assisted tools, reducing manual effort and potential errors.