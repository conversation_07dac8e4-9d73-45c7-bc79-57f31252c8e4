This comprehensive modernization plan outlines the strategic transition of your legacy ASP.NET application, specifically the Creditors/Debitors module, to a robust and scalable Django 5.0+ solution. Our approach prioritizes automation, leverages modern web technologies, and ensures a clean, maintainable codebase with strong test coverage.

The goal is to deliver a user experience that is dynamic and responsive, similar to a single-page application, but built with a simplified stack focusing on server-rendered HTML enhanced by HTMX and Alpine.js. This reduces development complexity and ongoing maintenance costs.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Value:** Understanding the existing data structure is the foundation for a stable and efficient new system. This step ensures that all critical business data is accurately represented in the new Django application without requiring a full database migration initially.

**Instructions:**

From the ASP.NET code, we identify the primary tables involved in managing creditors and debitors, along with their relationships and relevant fields.

*   **Creditors Master Table:** `tblACC_Creditors_Master`
    *   `Id` (Primary Key - Integer)
    *   `SysDate` (String - for conversion to DateTime)
    *   `SysTime` (String - for conversion to Time)
    *   `SessionId` (String)
    *   `CompId` (Integer - Foreign Key to Company)
    *   `FinYearId` (Integer - Foreign Key to Financial Year)
    *   `SupplierId` (String - Foreign Key to `tblMM_Supplier_master`)
    *   `OpeningAmt` (Double)

*   **Debitors Master Table:** `tblACC_Debitors_Master`
    *   `Id` (Primary Key - Integer)
    *   `SysDate` (String - for conversion to DateTime)
    *   `SysTime` (String - for conversion to Time)
    *   `SessionId` (String)
    *   `CompId` (Integer - Foreign Key to Company)
    *   `FinYearId` (Integer - Foreign Key to Financial Year)
    *   `CustomerId` (String - Foreign Key to `SD_Cust_master`)
    *   `OpeningAmt` (Double)

*   **Related Master Tables (assumed from context/autocomplete):**
    *   `tblMM_Supplier_master`: `SupplierId`, `SupplierName`
    *   `SD_Cust_master`: `CustomerId`, `CustomerName`

*   **Related Transaction/Calculation Tables (implied by C# logic):**
    *   `tblACC_BillBooking_Master`, `tblACC_BillBooking_Details`, `tblMM_PO_Details`, `tblMM_PO_Master`, `tblQc_MaterialQuality_Details`, `tblinv_MaterialServiceNote_Details` (for Creditors Booked Bill)
    *   `tblACC_BankVoucher_Payment_Master`, `tblACC_BankVoucher_Payment_Details` (for Creditors Payment)
    *   `tblExciseser_Master`, `tblVAT_Master`, `tblACC_SalesInvoice_Details`, `tblACC_SalesInvoice_Master`, `tblACC_ServiceTaxInvoice_Details`, `tblACC_ServiceTaxInvoice_Master`, `tblACC_ProformaInvoice_Details`, `tblACC_ProformaInvoice_Master`, `Unit_Master` (for Debitors Invoices)
    *   `TotalTdsDeduct` (Implied class/function for TDS calculation)

### Step 2: Identify Backend Functionality

**Business Value:** This step clarifies the core operations (creating, reading, updating, and deleting records) that drive the application. By explicitly mapping these, we ensure that all existing business processes are fully supported in the new Django system, preventing any loss of functionality during the transition.

**Instructions:**

The ASP.NET code reveals the following functionalities:

*   **Creditors Module:**
    *   **Read (Display/Search):** Populates `GridView1` based on `tblACC_Creditors_Master` and `tblMM_Supplier_master`. Includes search by `SupplierName` and calculates `OpeningAmt`, `BookBillAmt`, `PaymentAmt`, `TDSAmt`, and `ClosingAmt` dynamically.
    *   **Create (Add):** Inserts new records into `tblACC_Creditors_Master` from the GridView's footer. Includes validation for duplicate suppliers and valid input.
    *   **Delete:** Removes records from `tblACC_Creditors_Master`.
    *   **Detail Link (`LnkBtn`):** Redirects to a detail page (`CreditorsDebitors_InDetailList.aspx`), implying a separate view for detailed Creditor information.

*   **Debitors Module:**
    *   **Read (Display/Search):** Populates `GridView2` based on `tblACC_Debitors_Master` and `SD_Cust_master`. Includes search by `CustomerName` and calculates `OpeningAmt`, `SalesInvAmt`, `ServiceInvAmt`, and `PerformaInvAmt`.
    *   **Create (Add):** Inserts new records into `tblACC_Debitors_Master` from the GridView's footer. Includes validation for duplicate customers and valid input.
    *   **Delete:** Removes records from `tblACC_Debitors_Master`.

*   **Common Functionality:**
    *   **Autocomplete:** `sql3` web method provides suggestions for both supplier and customer names, extracting IDs from the "Name [ID]" format.
    *   **Tab Switching:** The `TabContainer` dictates switching between Creditors and Debitors views.
    *   **Session Management:** `CompId`, `FinYearId`, `SessionId` are used across operations, implying a global context or user-specific data.

### Step 3: Infer UI Components

**Business Value:** By understanding the existing user interface, we can design a modern, intuitive Django frontend that mimics or improves upon the original experience. This minimizes user retraining and ensures a smooth transition to the new system.

**Instructions:**

The ASP.NET controls translate to the following Django UI components:

*   **Overall Structure:**
    *   Main page (`CreditorsDebitors.aspx`) with a master page (Django `base.html` extension).
    *   A primary `div` or container to hold the tab content.

*   **Tab Interface:**
    *   `TabContainer` (`Creditors` and `Debitors` tabs): Will be implemented using HTMX to swap content in a `div` when a tab is clicked, allowing for a single page with dynamic content.

*   **Search & Filtering:**
    *   `TextBox` with `AutoCompleteExtender` for `Supplier Name` and `Debitors Name`: Will be a standard Django form input combined with HTMX for autocomplete suggestions (returning partial HTML or JSON). Search buttons (`btn_Search`, `btn_deb_search`) will trigger HTMX `hx-get` requests to refresh the table.

*   **Data Presentation & Manipulation:**
    *   `GridView` (`GridView1` and `GridView2`): Replaced by a `<table>` element initialized with DataTables. This provides client-side pagination, sorting, and searching.
    *   `TemplateField` (for SN, Creditors/Debitors Name, Amounts, Actions): Standard table columns.
    *   `LinkButton` (`LinkBtnDel`, `LinkBtnDelD`, `lblTerms`): HTMX `hx-get` for delete confirmation modals, and a standard `<a>` tag for detail links (Creditors).
    *   `FooterTemplate` with `TextBox` and `Button` (`btnInsert`, `btnInsertD`): HTMX-powered form for adding new records, likely within a modal.
    *   `EmptyDataTemplate`: HTMX-powered form for adding new records when the table is empty, also within a modal.

*   **Validation & Feedback:**
    *   `RequiredFieldValidator`, `RegularExpressionValidator`: Django form validation and model field validation. Error messages displayed inline.
    *   `Label` (`lblMessage`, `lblMessage2`): Django messages framework, with HTMX triggers to display toast-style notifications.

### Step 4: Generate Django Code

**Business Value:** This is where the actual code is written to bring the application to life in Django. By following established patterns and best practices, we ensure the new system is maintainable, scalable, and performs optimally.

#### 4.1 Models (`accounts/models.py`)

**Business Value:** Models define the core data structure and encapsulate business logic related to that data. By making models "fat," we ensure that complex calculations and validations are handled directly within the data layer, leading to cleaner, more reusable code and easier maintainability. This is critical for accurate financial reporting and operational integrity.

**Instructions:**

We will create `Supplier` and `Customer` models to represent the master data, and `CreditorAccount` and `DebitorAccount` for the transactional data. The complex calculations will be implemented as methods within the `CreditorAccount` and `DebitorAccount` models, adhering to the "fat model" principle.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# Assume these master models exist or are created as part of the overall ERP.
# For simplicity, we are mapping directly to assumed table names for FKs.
# In a real scenario, these would be proper Django models for supplier/customer.

class Company(models.Model):
    # Dummy model for CompId
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(max_length=255, db_column='CompName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assumed table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class FinancialYear(models.Model):
    # Dummy model for FinYearId
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_name = models.CharField(max_length=50, db_column='FinYearName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancialYearMaster' # Assumed table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name or f"Financial Year {self.id}"

class Supplier(models.Model):
    # This maps to tblMM_Supplier_master
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    # Add other fields from tblMM_Supplier_master as needed
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', related_name='suppliers') # Assumed FK
    
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class Customer(models.Model):
    # This maps to SD_Cust_master
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    # Add other fields from SD_Cust_master as needed
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', related_name='customers') # Assumed FK

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class CreditorAccount(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=20, blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    fin_year_id = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id')
    opening_amt = models.FloatField(db_column='OpeningAmt')

    class Meta:
        managed = False
        db_table = 'tblACC_Creditors_Master'
        verbose_name = 'Creditor Account'
        verbose_name_plural = 'Creditor Accounts'
        unique_together = ('supplier', 'comp_id', 'fin_year_id',) # Assuming unique per supplier per company per year

    def __str__(self):
        return f"Creditor: {self.supplier.supplier_name} - {self.opening_amt}"

    def save(self, *args, **kwargs):
        # Automatically set SysDate, SysTime before saving
        current_time = timezone.now()
        self.sys_date = current_time.strftime("%d/%m/%Y") # Example format from ASP.NET
        self.sys_time = current_time.strftime("%H:%M:%S") # Example format from ASP.NET
        # SessionId and CompId, FinYearId should be set by the view/middleware based on user context
        super().save(*args, **kwargs)

    # Business Logic Methods (Fat Model)
    # These methods encapsulate the complex SQL queries from the C# code.
    # Note: Full SQL to ORM translation for these is complex and requires full schema
    # for tblACC_BillBooking_Master, tblMM_PO_Details, etc. Placeholders provided.

    def calculate_booked_bill_amount(self):
        """
        Calculates the booked bill amount for this creditor.
        Translates C# `FillGrid_CreditorsBookedBill`.
        This would involve complex ORM queries across multiple tables.
        """
        # Example placeholder. In a real scenario, this would be a detailed
        # ORM query or raw SQL query to sum up booked bills.
        # e.g., using Q objects for conditional sums, annotations.
        # This logic is critical and requires careful translation of the original SQL.
        try:
            # Simplified example (replace with actual complex query)
            # You would query tblACC_BillBooking_Master, tblACC_BillBooking_Details, etc.
            # to sum up values based on supplier, company, and financial year.
            # Example:
            # from django.db.models import Sum, F
            # total_booked_bill = SomeBillBookingModel.objects.filter(
            #     supplier=self.supplier, comp_id=self.comp_id, fin_year_id__lte=self.fin_year_id
            # ).aggregate(
            #     total=Sum(F('item_value') + F('pf_amt') + F('taxes_etc'))
            # )['total'] or 0
            
            # For demonstration, returning a dummy value
            return round(float(self.pk * 100 + 500) if self.pk else 0, 2)
        except Exception as e:
            print(f"Error calculating booked bill amount for {self.supplier.supplier_name}: {e}")
            return 0.0

    def calculate_payment_amount(self):
        """
        Calculates the total payment amount for this creditor.
        Translates C# `FillGrid_CreditorsPayment`.
        This would involve querying tblACC_BankVoucher_Payment_Master and Details.
        """
        try:
            # Simplified example (replace with actual complex query)
            # Example:
            # from django.db.models import Sum
            # total_payment = PaymentMaster.objects.filter(
            #     pay_to=self.supplier.supplier_id, comp_id=self.comp_id, fin_year_id__lte=self.fin_year_id
            # ).aggregate(
            #     total=Sum('pay_amt') + Sum('details_amount_from_subquery')
            # )['total'] or 0
            
            # For demonstration, returning a dummy value
            return round(float(self.pk * 50 + 100) if self.pk else 0, 2)
        except Exception as e:
            print(f"Error calculating payment amount for {self.supplier.supplier_name}: {e}")
            return 0.0

    def calculate_tds_amount(self):
        """
        Calculates the TDS amount for this creditor.
        Translates C# `TotalTdsDeduct.Check_TDSAmt`.
        """
        try:
            # This would call a service or another model method
            # For demonstration, returning a dummy value
            return round(float(self.pk * 5) if self.pk else 0, 2)
        except Exception as e:
            print(f"Error calculating TDS amount for {self.supplier.supplier_name}: {e}")
            return 0.0

    @property
    def booked_bill_amt(self):
        return self.calculate_booked_bill_amount()

    @property
    def payment_amt(self):
        return self.calculate_payment_amount()

    @property
    def tds_amt(self):
        return self.calculate_tds_amount()

    @property
    def closing_amt(self):
        """
        Calculates the closing amount.
        Formula: OpeningAmt + BookedBillAmt - (TDSAmt + PaymentAmt)
        """
        return round(self.opening_amt + self.booked_bill_amt - (self.tds_amt + self.payment_amt), 2)

    @property
    def has_transactions(self):
        """Checks if there are any booked bills or payments for this creditor."""
        return self.booked_bill_amt > 0 or self.payment_amt > 0


class DebitorAccount(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.CharField(db_column='SysDate', max_length=20, blank=True, null=True)
    sys_time = models.CharField(db_column='SysTime', max_length=20, blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    fin_year_id = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId')
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId', to_field='customer_id')
    opening_amt = models.FloatField(db_column='OpeningAmt')

    class Meta:
        managed = False
        db_table = 'tblACC_Debitors_Master'
        verbose_name = 'Debitor Account'
        verbose_name_plural = 'Debitor Accounts'
        unique_together = ('customer', 'comp_id', 'fin_year_id',) # Assuming unique per customer per company per year

    def __str__(self):
        return f"Debitor: {self.customer.customer_name} - {self.opening_amt}"

    def save(self, *args, **kwargs):
        # Automatically set SysDate, SysTime before saving
        current_time = timezone.now()
        self.sys_date = current_time.strftime("%d/%m/%Y") # Example format from ASP.NET
        self.sys_time = current_time.strftime("%H:%M:%S") # Example format from ASP.NET
        # SessionId and CompId, FinYearId should be set by the view/middleware based on user context
        super().save(*args, **kwargs)

    # Business Logic Methods (Fat Model)
    # These methods encapsulate the complex SQL queries from the C# code.
    # Note: Full SQL to ORM translation for these is complex and requires full schema
    # for tblACC_SalesInvoice_Details, tblExciseser_Master, etc. Placeholders provided.

    def calculate_sales_invoice_amount(self):
        """
        Calculates the total sales invoice amount for this debitor.
        Translates C# `Cal_SalesInvoice`.
        """
        try:
            # Example placeholder (replace with actual complex query)
            return round(float(self.pk * 200 + 800) if self.pk else 0, 2)
        except Exception as e:
            print(f"Error calculating sales invoice amount for {self.customer.customer_name}: {e}")
            return 0.0

    def calculate_service_invoice_amount(self):
        """
        Calculates the total service invoice amount for this debitor.
        Translates C# `Cal_ServiceInvoice`.
        """
        try:
            # Example placeholder (replace with actual complex query)
            return round(float(self.pk * 75 + 150) if self.pk else 0, 2)
        except Exception as e:
            print(f"Error calculating service invoice amount for {self.customer.customer_name}: {e}")
            return 0.0

    def calculate_performa_invoice_amount(self):
        """
        Calculates the total performa invoice amount for this debitor.
        Translates C# `Cal_PerformaInvoice`.
        """
        try:
            # Example placeholder (replace with actual complex query)
            return round(float(self.pk * 25 + 50) if self.pk else 0, 2)
        except Exception as e:
            print(f"Error calculating performa invoice amount for {self.customer.customer_name}: {e}")
            return 0.0

    @property
    def sales_inv_amt(self):
        return self.calculate_sales_invoice_amount()

    @property
    def service_inv_amt(self):
        return self.calculate_service_invoice_amount()

    @property
    def performa_inv_amt(self):
        return self.calculate_performa_invoice_amount()
```

#### 4.2 Forms (`accounts/forms.py`)

**Business Value:** Forms streamline user input and ensure data integrity through built-in validation. By using Django's ModelForms, we rapidly create user-friendly interfaces that align with our data models, reducing development effort and minimizing errors. The custom `clean_supplier_name` and `clean_customer_name` methods ensure that the "Name [ID]" format from the old system is correctly parsed and linked to the underlying IDs.

**Instructions:**

Create ModelForms for `CreditorAccount` and `DebitorAccount`. Implement custom `clean` methods to handle the "Name [ID]" parsing from the autocomplete input, which was a key part of the original ASP.NET logic (`fun.getCode`).

```python
from django import forms
from django.core.exceptions import ValidationError
from .models import CreditorAccount, DebitorAccount, Supplier, Customer
import re # For parsing "Name [ID]"

# Custom form field for Autocomplete, if needed, or handle in clean() directly.
# For simplicity, we will handle parsing in the clean method of the form fields.

class CreditorAccountForm(forms.ModelForm):
    # This field will receive "SupplierName [SupplierId]" from the autocomplete
    supplier_display_name = forms.CharField(
        label="Supplier Name",
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    opening_amt = forms.DecimalField(
        label="Opening Amount",
        max_digits=18, # Assuming 15 digits integer + 3 decimal places
        decimal_places=2,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = CreditorAccount
        fields = ['supplier_display_name', 'opening_amt'] # Note: 'supplier' FK field is excluded
        # The actual 'supplier' FK will be set in the form's clean method or view's form_valid.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            # If editing an existing instance, pre-fill the display name
            self.initial['supplier_display_name'] = str(self.instance.supplier)
            # Disable supplier_display_name for existing records if it has transactions
            # This replicates the ASP.NET logic where `lblTerms` visibility depends on transactions.
            if self.instance.has_transactions: # using our model method
                self.fields['supplier_display_name'].widget.attrs['readonly'] = 'readonly'
                self.fields['supplier_display_name'].help_text = "Cannot change supplier for accounts with transactions."


    def clean_supplier_display_name(self):
        supplier_input = self.cleaned_data['supplier_display_name']
        match = re.search(r'\[(.*?)\]$', supplier_input)
        
        if not match:
            raise ValidationError("Invalid format. Please select from the autocomplete suggestions (e.g., 'Name [ID]').")
        
        supplier_id = match.group(1)
        
        try:
            supplier_obj = Supplier.objects.get(supplier_id=supplier_id)
        except Supplier.DoesNotExist:
            raise ValidationError(f"Supplier with ID '{supplier_id}' not found. Please select a valid supplier.")

        # Check for duplicates, replicating 'chkDuplicate' logic
        # This check is for new entries. For updates, it should not check against itself.
        if not self.instance.pk: # Only for new objects
            current_comp_id = 1 # Replace with actual CompId from session/user context
            current_fin_year_id = 1 # Replace with actual FinYearId from session/user context
            
            if CreditorAccount.objects.filter(supplier=supplier_obj, comp_id=current_comp_id, fin_year_id=current_fin_year_id).exists():
                raise ValidationError("This supplier already has a Creditor Account for the current financial year.")
                
        self.cleaned_data['supplier'] = supplier_obj # Store the actual supplier object for later use
        return supplier_input # Return the original string for display purposes, but the object is ready

    def save(self, commit=True):
        # Override save to correctly assign the supplier FK
        instance = super().save(commit=False)
        instance.supplier = self.cleaned_data['supplier']
        # Set comp_id, fin_year_id, session_id from request in view's form_valid
        if commit:
            instance.save()
        return instance


class DebitorAccountForm(forms.ModelForm):
    # This field will receive "CustomerName [CustomerId]" from the autocomplete
    customer_display_name = forms.CharField(
        label="Debitor Name",
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    opening_amt = forms.DecimalField(
        label="Opening Amount",
        max_digits=18, # Assuming 15 digits integer + 3 decimal places
        decimal_places=2,
        required=True,
        widget=forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = DebitorAccount
        fields = ['customer_display_name', 'opening_amt']
        # The actual 'customer' FK will be set in the form's clean method or view's form_valid.

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        if self.instance.pk:
            # If editing an existing instance, pre-fill the display name
            self.initial['customer_display_name'] = str(self.instance.customer)

    def clean_customer_display_name(self):
        customer_input = self.cleaned_data['customer_display_name']
        match = re.search(r'\[(.*?)\]$', customer_input)
        
        if not match:
            raise ValidationError("Invalid format. Please select from the autocomplete suggestions (e.g., 'Name [ID]').")
        
        customer_id = match.group(1)
        
        try:
            customer_obj = Customer.objects.get(customer_id=customer_id)
        except Customer.DoesNotExist:
            raise ValidationError(f"Customer with ID '{customer_id}' not found. Please select a valid customer.")

        # Check for duplicates, replicating 'chkDuplicate' logic
        if not self.instance.pk: # Only for new objects
            current_comp_id = 1 # Replace with actual CompId from session/user context
            current_fin_year_id = 1 # Replace with actual FinYearId from session/user context
            
            if DebitorAccount.objects.filter(customer=customer_obj, comp_id=current_comp_id, fin_year_id=current_fin_year_id).exists():
                raise ValidationError("This customer already has a Debitor Account for the current financial year.")

        self.cleaned_data['customer'] = customer_obj
        return customer_input

    def save(self, commit=True):
        instance = super().save(commit=False)
        instance.customer = self.cleaned_data['customer']
        if commit:
            instance.save()
        return instance
```

#### 4.3 Views (`accounts/views.py`)

**Business Value:** Views act as the traffic controllers of the application, directing requests to the right data and templates. By keeping them "thin" (minimal logic) and leveraging Django's Class-Based Views (CBVs), we ensure that the application's flow is clear, efficient, and easily scalable. HTMX integration provides a modern, interactive user experience without complex JavaScript.

**Instructions:**

Implement main view (`CreditorsDebitorsView`) to handle tab switching. Create dedicated List, Create, Update, Delete views for both Creditor and Debitor accounts. Also, an API view for autocomplete functionality. Keep views concise (5-15 lines) by delegating complex logic to models.

```python
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.shortcuts import get_object_or_404
from .models import CreditorAccount, DebitorAccount, Supplier, Customer, Company, FinancialYear
from .forms import CreditorAccountForm, DebitorAccountForm

# Helper to get session-based company and financial year (replace with actual session/middleware logic)
def get_user_context(request):
    # In a real application, CompId and FinYearId would come from user session/profile.
    # For this example, assuming a default:
    current_comp_id = 1 
    current_fin_year_id = 1 
    # Ensure these Company and FinancialYear instances exist in your DB or mock them for testing
    company = Company.objects.get_or_create(id=current_comp_id, defaults={'name': 'Default Company'})[0]
    fin_year = FinancialYear.objects.get_or_create(id=current_fin_year_id, defaults={'year_name': '2023-2024'})[0]
    return {
        'comp_id': company,
        'fin_year_id': fin_year,
        'session_id': request.user.username if request.user.is_authenticated else 'Guest'
    }

class CreditorsDebitorsView(TemplateView):
    """
    Main view to display the Creditors/Debitors tabs.
    Loads the initial Creditors list.
    """
    template_name = 'accounts/creditors_debitors/main.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # We don't load the full list here, as it will be loaded via HTMX
        # context['creditor_accounts'] = CreditorAccount.objects.all() # Or filter by user context
        return context

# --- Creditor Account Views ---

class CreditorAccountListView(ListView):
    """
    Returns the partial HTML for the Creditor Accounts DataTable.
    Used by HTMX to refresh the table.
    """
    model = CreditorAccount
    template_name = 'accounts/creditors_debitors/_creditoraccount_table.html'
    context_object_name = 'creditor_accounts'

    def get_queryset(self):
        user_context = get_user_context(self.request)
        queryset = CreditorAccount.objects.filter(
            comp_id=user_context['comp_id'],
            fin_year_id__lte=user_context['fin_year_id']
        ).order_by('supplier__supplier_id')

        # Implement search functionality
        search_query = self.request.GET.get('search_supplier_name')
        if search_query:
            # Extract ID from "Name [ID]" format if present
            match = re.search(r'\[(.*?)\]$', search_query)
            if match:
                supplier_id = match.group(1)
                queryset = queryset.filter(Q(supplier__supplier_id=supplier_id) | Q(supplier__supplier_name__icontains=search_query.split(' [')[0]))
            else:
                queryset = queryset.filter(supplier__supplier_name__icontains=search_query)

        return queryset

class CreditorAccountCreateView(CreateView):
    model = CreditorAccount
    form_class = CreditorAccountForm
    template_name = 'accounts/creditors_debitors/_creditoraccount_form.html'
    # success_url is not strictly needed for HTMX, as we return 204
    # and trigger a refresh. But good practice for non-HTMX use.
    success_url = reverse_lazy('creditor_account_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the CompId and FinYearId context to the form for unique validation
        kwargs['request'] = self.request # If form needs access to request for session data
        return kwargs

    def form_valid(self, form):
        user_context = get_user_context(self.request)
        form.instance.comp_id = user_context['comp_id']
        form.instance.fin_year_id = user_context['fin_year_id']
        form.instance.session_id = user_context['session_id']

        response = super().form_valid(form)
        messages.success(self.request, 'Creditor Account added successfully.')
        if self.request.headers.get('HX-Request'):
            # Clear form on success and trigger refresh
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': '{"refreshCreditorAccountList":{}, "closeModal":{}}'
                }
            )
        return response

    def form_invalid(self, form):
        # Render the form again with errors for HTMX
        return self.render_to_response(self.get_context_data(form=form))

class CreditorAccountUpdateView(UpdateView):
    model = CreditorAccount
    form_class = CreditorAccountForm
    template_name = 'accounts/creditors_debitors/_creditoraccount_form.html'
    success_url = reverse_lazy('creditor_account_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Creditor Account updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshCreditorAccountList":{}, "closeModal":{}}'
                }
            )
        return response

    def form_invalid(self, form):
        return self.render_to_response(self.get_context_data(form=form))

class CreditorAccountDeleteView(DeleteView):
    model = CreditorAccount
    template_name = 'accounts/creditors_debitors/_creditoraccount_confirm_delete.html'
    success_url = reverse_lazy('creditor_account_list')

    def delete(self, request, *args, **kwargs):
        # ASP.NET original logic checked for transactions before allowing delete.
        # This should be implemented in the model or via custom delete method.
        self.object = self.get_object()
        if self.object.has_transactions: # using our model method
            messages.error(request, "Cannot delete Creditor Account with existing transactions.")
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': '{"closeModal":{}}'
                    }
                )
            return self.render_to_response(self.get_context_data(object=self.object)) # Render delete page with error

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Creditor Account deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshCreditorAccountList":{}, "closeModal":{}}'
                }
            )
        return response


# --- Debitor Account Views ---

class DebitorAccountListView(ListView):
    """
    Returns the partial HTML for the Debitor Accounts DataTable.
    Used by HTMX to refresh the table.
    """
    model = DebitorAccount
    template_name = 'accounts/creditors_debitors/_debitoraccount_table.html'
    context_object_name = 'debitor_accounts'

    def get_queryset(self):
        user_context = get_user_context(self.request)
        queryset = DebitorAccount.objects.filter(
            comp_id=user_context['comp_id'],
            fin_year_id__lte=user_context['fin_year_id']
        ).order_by('customer__customer_id')

        # Implement search functionality
        search_query = self.request.GET.get('search_customer_name')
        if search_query:
            match = re.search(r'\[(.*?)\]$', search_query)
            if match:
                customer_id = match.group(1)
                queryset = queryset.filter(Q(customer__customer_id=customer_id) | Q(customer__customer_name__icontains=search_query.split(' [')[0]))
            else:
                queryset = queryset.filter(customer__customer_name__icontains=search_query)
        return queryset

class DebitorAccountCreateView(CreateView):
    model = DebitorAccount
    form_class = DebitorAccountForm
    template_name = 'accounts/creditors_debitors/_debitoraccount_form.html'
    success_url = reverse_lazy('debitor_account_list')

    def form_valid(self, form):
        user_context = get_user_context(self.request)
        form.instance.comp_id = user_context['comp_id']
        form.instance.fin_year_id = user_context['fin_year_id']
        form.instance.session_id = user_context['session_id']

        response = super().form_valid(form)
        messages.success(self.request, 'Debitor Account added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshDebitorAccountList":{}, "closeModal":{}}'
                }
            )
        return response

    def form_invalid(self, form):
        return self.render_to_response(self.get_context_data(form=form))

class DebitorAccountUpdateView(UpdateView):
    model = DebitorAccount
    form_class = DebitorAccountForm
    template_name = 'accounts/creditors_debitors/_debitoraccount_form.html'
    success_url = reverse_lazy('debitor_account_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Debitor Account updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshDebitorAccountList":{}, "closeModal":{}}'
                }
            )
        return response

    def form_invalid(self, form):
        return self.render_to_response(self.get_context_data(form=form))

class DebitorAccountDeleteView(DeleteView):
    model = DebitorAccount
    template_name = 'accounts/creditors_debitors/_debitoraccount_confirm_delete.html'
    success_url = reverse_lazy('debitor_account_list')

    def delete(self, request, *args, **kwargs):
        # No specific transaction check for Debitor delete in original code, but good practice
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Debitor Account deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshDebitorAccountList":{}, "closeModal":{}}'
                }
            )
        return response

# --- Autocomplete API View ---

class AutocompleteSuggestionsView(View):
    """
    Provides autocomplete suggestions for Supplier and Customer names.
    Replicates the functionality of the ASP.NET `sql3` WebMethod.
    Returns JSON that can be used by HTMX or Alpine.js for dropdowns.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        context_key = request.GET.get('context_key') # 'key1' for supplier, 'key2' for customer
        
        suggestions = []
        comp_id = get_user_context(request)['comp_id']

        if context_key == 'key1': # Supplier
            # Ensure supplier__supplier_name__startswith if direct match is needed, else icontains
            query_set = Supplier.objects.filter(
                comp_id=comp_id,
                supplier_name__icontains=prefix_text
            ).order_by('supplier_name')[:10] # Limit results for performance
            
            suggestions = [
                f"{supplier.supplier_name} [{supplier.supplier_id}]" 
                for supplier in query_set
            ]
        elif context_key == 'key2': # Customer
            query_set = Customer.objects.filter(
                comp_id=comp_id,
                customer_name__icontains=prefix_text
            ).order_by('customer_name')[:10]
            
            suggestions = [
                f"{customer.customer_name} [{customer.customer_id}]" 
                for customer in query_set
            ]
        
        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates (`accounts/templates/accounts/creditors_debitors/`)

**Business Value:** Templates are the user-facing part of the application. By building them with a modular approach (extending `base.html` and using partials), we ensure consistency, rapid development, and efficient dynamic updates via HTMX, providing a smooth and modern user experience.

**Instructions:**

Create main template for the page (`main.html`), partials for the DataTable (`_creditoraccount_table.html`, `_debitoraccount_table.html`), and partials for the forms (`_creditoraccount_form.html`, `_debitoraccount_form.html`, `_creditoraccount_confirm_delete.html`, `_debitoraccount_confirm_delete.html`).

**`main.html`** (Main page, extends `core/base.html`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'creditors', showModal: false, modalContent: '' }"
    @close-modal.window="showModal = false; modalContent = ''"
>
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Creditors / Debitors Accounts</h2>
    </div>

    <!-- Tab Buttons -->
    <div class="flex space-x-4 border-b border-gray-200 mb-6">
        <button 
            @click="activeTab = 'creditors'"
            :class="{ 'border-b-2 border-blue-500 text-blue-600 font-semibold': activeTab === 'creditors', 'text-gray-500': activeTab !== 'creditors' }"
            class="py-2 px-4 focus:outline-none"
            hx-get="{% url 'creditor_account_list_partial' %}"
            hx-target="#tabContent"
            hx-trigger="click once[activeTab == 'creditors'], refreshCreditorAccountList from:body"
            hx-swap="innerHTML"
        >
            Creditors
        </button>
        <button 
            @click="activeTab = 'debitors'"
            :class="{ 'border-b-2 border-blue-500 text-blue-600 font-semibold': activeTab === 'debitors', 'text-gray-500': activeTab !== 'debitors' }"
            class="py-2 px-4 focus:outline-none"
            hx-get="{% url 'debitor_account_list_partial' %}"
            hx-target="#tabContent"
            hx-trigger="click once[activeTab == 'debitors'], refreshDebitorAccountList from:body"
            hx-swap="innerHTML"
        >
            Debitors
        </button>
    </div>

    <!-- Tab Content Container -->
    <div id="tabContent" class="bg-white p-6 rounded-lg shadow-lg">
        <!-- Initial content for Creditors tab will be loaded here via HTMX on page load -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Creditors Data...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div x-show="showModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
        <div @click.away="showModal = false" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
            x-transition:enter="ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-90"
            x-transition:enter-end="opacity-100 scale-100"
            x-transition:leave="ease-in duration-200"
            x-transition:leave-start="opacity-100 scale-100"
            x-transition:leave-end="opacity-0 scale-90">
            <div id="modalContent" x-html="modalContent"></div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'tabContent') {
            // Re-initialize DataTables if a new table partial is loaded into tabContent
            if (event.detail.target.querySelector('#creditorAccountTable') || event.detail.target.querySelector('#debitorAccountTable')) {
                // Ensure DataTables is re-initialized for the newly loaded table
                // This requires a specific selector and destroy() if already initialized
                // In partials, we ensure `$(selector).DataTable()` is called again.
            }
        }
        if (event.detail.target.id === 'modalContent') {
            // After modal content is swapped, make sure Alpine.js knows modal is visible
            document.querySelector('[x-data]').__alpine.$data.showModal = true;
        }
    });

    document.addEventListener('htmx:afterRequest', function(event) {
        // Handle global messages and modal closing after a successful form submission
        if (event.detail.xhr.status === 204) { // No Content, indicates success
            if (event.detail.request.headers['HX-Trigger'] && 
                event.detail.request.headers['HX-Trigger'].includes('closeModal')) {
                document.querySelector('[x-data]').__alpine.$data.showModal = false;
                document.querySelector('[x-data]').__alpine.$data.modalContent = ''; // Clear content
            }
        }
    });
</script>
{% endblock %}
```

**`_creditoraccount_table.html`** (Partial for Creditors DataTable)

```html
<div class="mb-4 flex items-center space-x-2">
    <input type="text" id="search_supplier_name" name="search_supplier_name"
           class="block w-96 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
           placeholder="Search Supplier Name [ID]"
           hx-get="{% url 'autocomplete_suggestions' %}"
           hx-trigger="keyup changed delay:500ms, search"
           hx-target="#supplier-autocomplete-results"
           hx-swap="innerHTML"
           hx-vals='{"context_key": "key1"}'
           autocomplete="off"
           _="on keyup if event.key is not 'Enter' remove .hidden from #supplier-autocomplete-results else add .hidden to #supplier-autocomplete-results"
    >
    <div id="supplier-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg hidden">
        <!-- Autocomplete suggestions will be loaded here -->
    </div>
    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'creditor_account_list_partial' %}"
            hx-target="#tabContent"
            hx-indicator="#creditorAccountTable-loading-indicator"
            hx-swap="innerHTML">
        Search
    </button>
    <button
        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded ml-auto"
        hx-get="{% url 'creditor_account_add' %}"
        hx-target="#modalContent"
        hx-trigger="click"
        _="on click showModal = true"
    >
        Add New Creditor
    </button>
</div>

<div id="creditorAccountTable-container">
    <table id="creditorAccountTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Creditor</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Amt</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Booked Bill</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">TDS Amount</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Closing Amt</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in creditor_accounts %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    {% if obj.has_transactions %}
                        <a href="{% url 'creditor_account_detail' obj.pk %}" class="text-blue-600 hover:text-blue-800">
                            {{ obj.supplier.supplier_name }} [{{ obj.supplier.supplier_id }}]
                        </a>
                    {% else %}
                        {{ obj.supplier.supplier_name }} [{{ obj.supplier.supplier_id }}]
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.opening_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.booked_bill_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.tds_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.payment_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right font-semibold">{{ obj.closing_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                        hx-get="{% url 'creditor_account_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click showModal = true"
                    >
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                        hx-get="{% url 'creditor_account_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click showModal = true"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 text-center">No Creditor Accounts found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <div id="creditorAccountTable-loading-indicator" class="htmx-indicator text-center mt-4">
        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading data...</p>
    </div>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#creditorAccountTable')) {
            $('#creditorAccountTable').DataTable().destroy();
        }
        $('#creditorAccountTable').DataTable({
            "paging": true,
            "searching": false, // Search handled by HTMX input
            "ordering": true,
            "info": true,
            "pageLength": 18, // Replicating PageSize
            "lengthMenu": [[10, 18, 25, 50, -1], [10, 18, 25, 50, "All"]],
            "responsive": true,
        });

        // Event listener for autocomplete selection
        document.getElementById('supplier-autocomplete-results').addEventListener('click', function(event) {
            if (event.target.tagName === 'DIV' && event.target.dataset.value) {
                document.getElementById('search_supplier_name').value = event.target.dataset.value;
                this.classList.add('hidden'); // Hide results after selection
                // Optionally trigger the search button click here
                document.querySelector('#tabContent button[hx-get*="creditor"]').click();
            }
        });
    });
</script>
```

**`_debitoraccount_table.html`** (Partial for Debitors DataTable)

```html
<div class="mb-4 flex items-center space-x-2">
    <input type="text" id="search_customer_name" name="search_customer_name"
           class="block w-96 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
           placeholder="Search Debitor Name [ID]"
           hx-get="{% url 'autocomplete_suggestions' %}"
           hx-trigger="keyup changed delay:500ms, search"
           hx-target="#customer-autocomplete-results"
           hx-swap="innerHTML"
           hx-vals='{"context_key": "key2"}'
           autocomplete="off"
           _="on keyup if event.key is not 'Enter' remove .hidden from #customer-autocomplete-results else add .hidden to #customer-autocomplete-results"
    >
    <div id="customer-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg hidden">
        <!-- Autocomplete suggestions will be loaded here -->
    </div>
    <button class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'debitor_account_list_partial' %}"
            hx-target="#tabContent"
            hx-indicator="#debitorAccountTable-loading-indicator"
            hx-swap="innerHTML">
        Search
    </button>
    <button
        class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded ml-auto"
        hx-get="{% url 'debitor_account_add' %}"
        hx-target="#modalContent"
        hx-trigger="click"
        _="on click showModal = true"
    >
        Add New Debitor
    </button>
</div>

<div id="debitorAccountTable-container">
    <table id="debitorAccountTable" class="min-w-full bg-white border border-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Debitor</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Amt</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Sales Invoice</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Service Invoice</th>
                <th class="py-2 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Performa Invoice</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in debitor_accounts %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.customer.customer_name }} [{{ obj.customer.customer_id }}]</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.opening_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.sales_inv_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.service_inv_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.performa_inv_amt|floatformat:2 }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-sm"
                        hx-get="{% url 'debitor_account_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click showModal = true"
                    >
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-sm"
                        hx-get="{% url 'debitor_account_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click showModal = true"
                    >
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 text-center">No Debitor Accounts found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <div id="debitorAccountTable-loading-indicator" class="htmx-indicator text-center mt-4">
        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <p class="mt-2">Loading data...</p>
    </div>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#debitorAccountTable')) {
            $('#debitorAccountTable').DataTable().destroy();
        }
        $('#debitorAccountTable').DataTable({
            "paging": true,
            "searching": false, // Search handled by HTMX input
            "ordering": true,
            "info": true,
            "pageLength": 18, // Replicating PageSize
            "lengthMenu": [[10, 18, 25, 50, -1], [10, 18, 25, 50, "All"]],
            "responsive": true,
        });

        // Event listener for autocomplete selection
        document.getElementById('customer-autocomplete-results').addEventListener('click', function(event) {
            if (event.target.tagName === 'DIV' && event.target.dataset.value) {
                document.getElementById('search_customer_name').value = event.target.dataset.value;
                this.classList.add('hidden'); // Hide results after selection
                // Optionally trigger the search button click here
                document.querySelector('#tabContent button[hx-get*="debitor"]').click();
            }
        });
    });
</script>
```

**`_creditoraccount_form.html`** (Partial for Creditor Form)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Creditor Account</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.supplier_display_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.supplier_display_name.label }}
                </label>
                {{ form.supplier_display_name }}
                {% if form.supplier_display_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.supplier_display_name.errors }}</p>
                {% endif %}
                {% if form.supplier_display_name.help_text %}
                <p class="text-gray-500 text-xs mt-1">{{ form.supplier_display_name.help_text }}</p>
                {% endif %}
                <div id="id_supplier_display_name_autocomplete_results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg hidden"
                     hx-get="{% url 'autocomplete_suggestions' %}"
                     hx-trigger="keyup changed delay:300ms from:#{{ form.supplier_display_name.id_for_label }}"
                     hx-target="#id_supplier_display_name_autocomplete_results"
                     hx-swap="innerHTML"
                     hx-vals='{"context_key": "key1", "q": event.target.value}'
                     _="on click if event.target.dataset.value set #{{ form.supplier_display_name.id_for_label }}.value to event.target.dataset.value then add .hidden to me"
                >
                    <!-- Autocomplete results will be loaded here -->
                </div>
            </div>

            <div>
                <label for="{{ form.opening_amt.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.opening_amt.label }}
                </label>
                {{ form.opening_amt }}
                {% if form.opening_amt.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.opening_amt.errors }}</p>
                {% endif %}
            </div>
            
            {# Non-field errors #}
            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mt-2">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click showModal = false"
            >
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_debitoraccount_form.html`** (Partial for Debitor Form)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Debitor Account</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.customer_display_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.customer_display_name.label }}
                </label>
                {{ form.customer_display_name }}
                {% if form.customer_display_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.customer_display_name.errors }}</p>
                {% endif %}
                <div id="id_customer_display_name_autocomplete_results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg hidden"
                     hx-get="{% url 'autocomplete_suggestions' %}"
                     hx-trigger="keyup changed delay:300ms from:#{{ form.customer_display_name.id_for_label }}"
                     hx-target="#id_customer_display_name_autocomplete_results"
                     hx-swap="innerHTML"
                     hx-vals='{"context_key": "key2", "q": event.target.value}'
                     _="on click if event.target.dataset.value set #{{ form.customer_display_name.id_for_label }}.value to event.target.dataset.value then add .hidden to me"
                >
                    <!-- Autocomplete results will be loaded here -->
                </div>
            </div>

            <div>
                <label for="{{ form.opening_amt.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.opening_amt.label }}
                </label>
                {{ form.opening_amt }}
                {% if form.opening_amt.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.opening_amt.errors }}</p>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="text-red-500 text-sm mt-2">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click showModal = false"
            >
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_creditoraccount_confirm_delete.html`** (Partial for Creditor Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4 text-gray-700">Are you sure you want to delete the Creditor Account for **{{ object.supplier.supplier_name }} [{{ object.supplier.supplier_id }}]**?</p>
    
    {% if messages %}
    <ul class="mb-4">
        {% for message in messages %}
        <li{% if message.tags %} class="{{ message.tags }} text-red-600 font-semibold"{% endif %}>{{ message }}</li>
        {% endfor %}
    </ul>
    {% endif %}

    <form hx-post="{% url 'creditor_account_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click showModal = false"
            >
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`_debitoraccount_confirm_delete.html`** (Partial for Debitor Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4 text-gray-700">Are you sure you want to delete the Debitor Account for **{{ object.customer.customer_name }} [{{ object.customer.customer_id }}]**?</p>

    <form hx-post="{% url 'debitor_account_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click showModal = false"
            >
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`accounts/urls.py`)

**Business Value:** A well-structured URL configuration makes the application's navigation intuitive and supports clear separation of concerns. This ensures that every part of the application is easily accessible and correctly mapped to its corresponding function.

**Instructions:**

Define URL patterns for the main page, list partials, and CRUD operations for both Creditor and Debitor accounts. Include the autocomplete endpoint.

```python
from django.urls import path
from .views import (
    CreditorsDebitorsView,
    CreditorAccountListView, CreditorAccountCreateView, CreditorAccountUpdateView, CreditorAccountDeleteView,
    DebitorAccountListView, DebitorAccountCreateView, DebitorAccountUpdateView, DebitorAccountDeleteView,
    AutocompleteSuggestionsView,
)

urlpatterns = [
    # Main page for Creditors/Debitors tabs
    path('creditors-debitors/', CreditorsDebitorsView.as_view(), name='creditors_debitors_main'),

    # HTMX Partial Views for Lists
    path('creditors-debitors/creditors/table/', CreditorAccountListView.as_view(), name='creditor_account_list_partial'),
    path('creditors-debitors/debitors/table/', DebitorAccountListView.as_view(), name='debitor_account_list_partial'),

    # Creditor Account CRUD
    path('creditors-debitors/creditors/add/', CreditorAccountCreateView.as_view(), name='creditor_account_add'),
    path('creditors-debitors/creditors/edit/<int:pk>/', CreditorAccountUpdateView.as_view(), name='creditor_account_edit'),
    path('creditors-debitors/creditors/delete/<int:pk>/', CreditorAccountDeleteView.as_view(), name='creditor_account_delete'),
    path('creditors-debitors/creditors/detail/<int:pk>/', TemplateView.as_view(template_name='accounts/creditors_debitors/creditor_detail.html'), name='creditor_account_detail'), # Placeholder for detail page

    # Debitor Account CRUD
    path('creditors-debitors/debitors/add/', DebitorAccountCreateView.as_view(), name='debitor_account_add'),
    path('creditors-debitors/debitors/edit/<int:pk>/', DebitorAccountUpdateView.as_view(), name='debitor_account_edit'),
    path('creditors-debitors/debitors/delete/<int:pk>/', DebitorAccountDeleteView.as_view(), name='debitor_account_delete'),

    # Autocomplete API
    path('autocomplete/suggestions/', AutocompleteSuggestionsView.as_view(), name='autocomplete_suggestions'),
]
```

#### 4.6 Tests (`accounts/tests.py`)

**Business Value:** Comprehensive testing is fundamental to delivering a high-quality, reliable application. By writing unit tests for models and integration tests for views, we ensure that every piece of logic functions as expected, minimize regressions, and provide confidence in the new system's accuracy and performance. Aim for at least 80% test coverage.

**Instructions:**

Include comprehensive unit tests for model methods (especially the complex calculation properties) and integration tests for all views (list, create, update, delete, and autocomplete).

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import CreditorAccount, DebitorAccount, Supplier, Customer, Company, FinancialYear
from unittest.mock import patch

class AccountModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy Company and FinancialYear instances for FKs
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.fin_year_old = FinancialYear.objects.create(id=0, year_name='2022-2023') # For lte filtering

        # Create test data for Supplier and Customer
        cls.supplier1 = Supplier.objects.create(supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.company)
        cls.supplier2 = Supplier.objects.create(supplier_id='SUP002', supplier_name='Supplier B', comp_id=cls.company)
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Customer X', comp_id=cls.company)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Customer Y', comp_id=cls.company)

        # Create test Creditor and Debitor accounts
        cls.creditor1 = CreditorAccount.objects.create(
            id=1, supplier=cls.supplier1, opening_amt=1000.00,
            comp_id=cls.company, fin_year_id=cls.fin_year, session_id='testuser'
        )
        cls.creditor2 = CreditorAccount.objects.create(
            id=2, supplier=cls.supplier2, opening_amt=500.00,
            comp_id=cls.company, fin_year_id=cls.fin_year, session_id='testuser'
        )
        cls.creditor_old_year = CreditorAccount.objects.create(
            id=3, supplier=cls.supplier1, opening_amt=2000.00,
            comp_id=cls.company, fin_year_id=cls.fin_year_old, session_id='testuser'
        )

        cls.debitor1 = DebitorAccount.objects.create(
            id=4, customer=cls.customer1, opening_amt=2500.00,
            comp_id=cls.company, fin_year_id=cls.fin_year, session_id='testuser'
        )
        cls.debitor2 = DebitorAccount.objects.create(
            id=5, customer=cls.customer2, opening_amt=750.00,
            comp_id=cls.company, fin_year_id=cls.fin_year, session_id='testuser'
        )

    def test_creditor_account_creation(self):
        self.assertEqual(self.creditor1.supplier.supplier_name, 'Supplier A')
        self.assertEqual(self.creditor1.opening_amt, 1000.00)
        self.assertIsNotNone(self.creditor1.sys_date)
        self.assertIsNotNone(self.creditor1.sys_time)

    def test_debitor_account_creation(self):
        self.assertEqual(self.debitor1.customer.customer_name, 'Customer X')
        self.assertEqual(self.debitor1.opening_amt, 2500.00)
        self.assertIsNotNone(self.debitor1.sys_date)
        self.assertIsNotNone(self.debitor1.sys_time)

    def test_creditor_account_str(self):
        expected_str = f"Creditor: {self.creditor1.supplier.supplier_name} - {self.creditor1.opening_amt}"
        self.assertEqual(str(self.creditor1), expected_str)

    def test_debitor_account_str(self):
        expected_str = f"Debitor: {self.debitor1.customer.customer_name} - {self.debitor1.opening_amt}"
        self.assertEqual(str(self.debitor1), expected_str)

    # Test Creditor Account Business Logic Methods
    @patch('accounts.models.CreditorAccount.calculate_booked_bill_amount', return_value=200.00)
    @patch('accounts.models.CreditorAccount.calculate_payment_amount', return_value=150.00)
    @patch('accounts.models.CreditorAccount.calculate_tds_amount', return_value=10.00)
    def test_creditor_closing_amount(self, mock_tds, mock_payment, mock_booked_bill):
        # OpeningAmt = 1000, BookedBill = 200, Payment = 150, TDS = 10
        # Expected: 1000 + 200 - (10 + 150) = 1000 + 200 - 160 = 1040.00
        closing_amt = self.creditor1.closing_amt
        self.assertEqual(closing_amt, 1040.00)
        self.assertTrue(self.creditor1.has_transactions) # Due to mocked values

    @patch('accounts.models.CreditorAccount.calculate_booked_bill_amount', return_value=0.0)
    @patch('accounts.models.CreditorAccount.calculate_payment_amount', return_value=0.0)
    @patch('accounts.models.CreditorAccount.calculate_tds_amount', return_value=0.0)
    def test_creditor_no_transactions(self, mock_tds, mock_payment, mock_booked_bill):
        self.assertFalse(self.creditor1.has_transactions)

    # Test Debitor Account Business Logic Methods
    @patch('accounts.models.DebitorAccount.calculate_sales_invoice_amount', return_value=100.00)
    @patch('accounts.models.DebitorAccount.calculate_service_invoice_amount', return_value=50.00)
    @patch('accounts.models.DebitorAccount.calculate_performa_invoice_amount', return_value=20.00)
    def test_debitor_invoice_amounts(self, mock_performa, mock_service, mock_sales):
        self.assertEqual(self.debitor1.sales_inv_amt, 100.00)
        self.assertEqual(self.debitor1.service_inv_amt, 50.00)
        self.assertEqual(self.debitor1.performa_inv_amt, 20.00)


class AccountViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy Company and FinancialYear instances
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year = FinancialYear.objects.create(id=1, year_name='2023-2024')
        cls.fin_year_old = FinancialYear.objects.create(id=0, year_name='2022-2023')

        # Create test data for Supplier and Customer
        cls.supplier1 = Supplier.objects.create(supplier_id='SUP001', supplier_name='Supplier A', comp_id=cls.company)
        cls.supplier2 = Supplier.objects.create(supplier_id='SUP002', supplier_name='Supplier B', comp_id=cls.company)
        cls.customer1 = Customer.objects.create(customer_id='CUST001', customer_name='Customer X', comp_id=cls.company)
        cls.customer2 = Customer.objects.create(customer_id='CUST002', customer_name='Customer Y', comp_id=cls.company)

        # Create test Creditor and Debitor accounts
        cls.creditor1 = CreditorAccount.objects.create(
            id=1, supplier=cls.supplier1, opening_amt=1000.00,
            comp_id=cls.company, fin_year_id=cls.fin_year, session_id='testuser'
        )
        cls.creditor2 = CreditorAccount.objects.create(
            id=2, supplier=cls.supplier2, opening_amt=500.00,
            comp_id=cls.company, fin_year_id=cls.fin_year, session_id='testuser'
        )
        cls.debitor1 = DebitorAccount.objects.create(
            id=3, customer=cls.customer1, opening_amt=2500.00,
            comp_id=cls.company, fin_year_id=cls.fin_year, session_id='testuser'
        )
        
        # Patch the get_user_context to return consistent values for testing
        cls.get_user_context_patcher = patch('accounts.views.get_user_context', return_value={
            'comp_id': cls.company,
            'fin_year_id': cls.fin_year,
            'session_id': 'testuser'
        })
        cls.mock_get_user_context = cls.get_user_context_patcher.start()

    @classmethod
    def tearDownClass(cls):
        super().tearDownClass()
        cls.get_user_context_patcher.stop()

    def setUp(self):
        self.client = Client()

    # --- Main View Test ---
    def test_main_creditors_debitors_view(self):
        response = self.client.get(reverse('creditors_debitors_main'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditors_debitors/main.html')
        self.assertContains(response, 'Creditors / Debitors Accounts')
        self.assertContains(response, 'Creditors')
        self.assertContains(response, 'Debitors')


    # --- Creditor Account List View Tests ---
    def test_creditor_account_list_partial(self):
        response = self.client.get(reverse('creditor_account_list_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditors_debitors/_creditoraccount_table.html')
        self.assertContains(response, 'Supplier A')
        self.assertContains(response, 'Supplier B')
        self.assertEqual(len(response.context['creditor_accounts']), 2) # Should only show current fin year

    def test_creditor_account_list_search_by_name(self):
        response = self.client.get(reverse('creditor_account_list_partial'), {'search_supplier_name': 'Supplier A'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Supplier A')
        self.assertNotContains(response, 'Supplier B')
        self.assertEqual(len(response.context['creditor_accounts']), 1)

    def test_creditor_account_list_search_by_id_format(self):
        response = self.client.get(reverse('creditor_account_list_partial'), {'search_supplier_name': 'Supplier A [SUP001]'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Supplier A')
        self.assertNotContains(response, 'Supplier B')
        self.assertEqual(len(response.context['creditor_accounts']), 1)

    # --- Creditor Account Create View Tests ---
    def test_creditor_account_create_get(self):
        response = self.client.get(reverse('creditor_account_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditors_debitors/_creditoraccount_form.html')
        self.assertContains(response, 'Add Creditor Account')

    def test_creditor_account_create_post_success(self):
        data = {
            'supplier_display_name': f'Supplier C [SUP003]',
            'opening_amt': '1500.00'
        }
        Supplier.objects.create(supplier_id='SUP003', supplier_name='Supplier C', comp_id=self.company) # Ensure supplier exists
        
        response = self.client.post(reverse('creditor_account_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(response.headers['HX-Trigger'], '{"refreshCreditorAccountList":{}, "closeModal":{}}')
        self.assertTrue(CreditorAccount.objects.filter(supplier__supplier_id='SUP003', opening_amt=1500.00).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Creditor Account added successfully.')

    def test_creditor_account_create_post_invalid_supplier(self):
        data = {
            'supplier_display_name': 'NonExistent [NON001]',
            'opening_amt': '100.00'
        }
        response = self.client.post(reverse('creditor_account_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'Supplier with ID &#x27;NON001&#x27; not found. Please select a valid supplier.')
        self.assertFalse(CreditorAccount.objects.filter(opening_amt=100.00).exists())

    def test_creditor_account_create_post_duplicate_supplier(self):
        data = {
            'supplier_display_name': f'Supplier A [SUP001]', # Already exists for this fin year
            'opening_amt': '200.00'
        }
        response = self.client.post(reverse('creditor_account_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'This supplier already has a Creditor Account for the current financial year.')
        self.assertEqual(CreditorAccount.objects.filter(supplier=self.supplier1).count(), 1) # Only one entry for SUP001 for current fin year

    # --- Creditor Account Update View Tests ---
    @patch('accounts.models.CreditorAccount.has_transactions', new_callable=patch.PropertyMock, return_value=False)
    def test_creditor_account_update_get(self, mock_has_transactions):
        response = self.client.get(reverse('creditor_account_edit', args=[self.creditor1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditors_debitors/_creditoraccount_form.html')
        self.assertContains(response, 'Edit Creditor Account')
        self.assertContains(response, 'Supplier A [SUP001]')
        self.assertContains(response, 'value="1000.0"')

    @patch('accounts.models.CreditorAccount.has_transactions', new_callable=patch.PropertyMock, return_value=False)
    def test_creditor_account_update_post_success(self, mock_has_transactions):
        data = {
            'supplier_display_name': 'Supplier A [SUP001]', # Should be disabled in form if has_transactions
            'opening_amt': '1200.50'
        }
        response = self.client.post(reverse('creditor_account_edit', args=[self.creditor1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], '{"refreshCreditorAccountList":{}, "closeModal":{}}')
        self.creditor1.refresh_from_db()
        self.assertEqual(self.creditor1.opening_amt, 1200.50)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Creditor Account updated successfully.')

    @patch('accounts.models.CreditorAccount.has_transactions', new_callable=patch.PropertyMock, return_value=True)
    def test_creditor_account_update_supplier_field_readonly(self, mock_has_transactions):
        response = self.client.get(reverse('creditor_account_edit', args=[self.creditor1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'readonly') # Check if the input is readonly

    # --- Creditor Account Delete View Tests ---
    @patch('accounts.models.CreditorAccount.has_transactions', new_callable=patch.PropertyMock, return_value=False)
    def test_creditor_account_delete_get(self, mock_has_transactions):
        response = self.client.get(reverse('creditor_account_delete', args=[self.creditor1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditors_debitors/_creditoraccount_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')

    @patch('accounts.models.CreditorAccount.has_transactions', new_callable=patch.PropertyMock, return_value=False)
    def test_creditor_account_delete_post_success(self, mock_has_transactions):
        response = self.client.post(reverse('creditor_account_delete', args=[self.creditor1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], '{"refreshCreditorAccountList":{}, "closeModal":{}}')
        self.assertFalse(CreditorAccount.objects.filter(pk=self.creditor1.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Creditor Account deleted successfully.')

    @patch('accounts.models.CreditorAccount.has_transactions', new_callable=patch.PropertyMock, return_value=True)
    def test_creditor_account_delete_post_with_transactions(self, mock_has_transactions):
        response = self.client.post(reverse('creditor_account_delete', args=[self.creditor1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content, but trigger only closeModal
        self.assertEqual(response.headers['HX-Trigger'], '{"closeModal":{}}')
        self.assertTrue(CreditorAccount.objects.filter(pk=self.creditor1.pk).exists()) # Should not be deleted
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Cannot delete Creditor Account with existing transactions.')


    # --- Debitor Account List View Tests ---
    def test_debitor_account_list_partial(self):
        response = self.client.get(reverse('debitor_account_list_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/creditors_debitors/_debitoraccount_table.html')
        self.assertContains(response, 'Customer X')
        self.assertContains(response, 'Customer Y')
        self.assertEqual(len(response.context['debitor_accounts']), 2)

    def test_debitor_account_list_search_by_name(self):
        response = self.client.get(reverse('debitor_account_list_partial'), {'search_customer_name': 'Customer X'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Customer X')
        self.assertNotContains(response, 'Customer Y')
        self.assertEqual(len(response.context['debitor_accounts']), 1)

    def test_debitor_account_list_search_by_id_format(self):
        response = self.client.get(reverse('debitor_account_list_partial'), {'search_customer_name': 'Customer X [CUST001]'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Customer X')
        self.assertNotContains(response, 'Customer Y')
        self.assertEqual(len(response.context['debitor_accounts']), 1)

    # --- Debitor Account Create View Tests ---
    def test_debitor_account_create_post_success(self):
        data = {
            'customer_display_name': 'Customer Z [CUST003]',
            'opening_amt': '3000.00'
        }
        Customer.objects.create(customer_id='CUST003', customer_name='Customer Z', comp_id=self.company)
        response = self.client.post(reverse('debitor_account_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue(DebitorAccount.objects.filter(customer__customer_id='CUST003', opening_amt=3000.00).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Debitor Account added successfully.')

    def test_debitor_account_create_post_duplicate_customer(self):
        data = {
            'customer_display_name': f'Customer X [CUST001]', # Already exists for this fin year
            'opening_amt': '200.00'
        }
        response = self.client.post(reverse('debitor_account_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertContains(response, 'This customer already has a Debitor Account for the current financial year.')
        self.assertEqual(DebitorAccount.objects.filter(customer=self.customer1).count(), 1) # Only one entry for CUST001 for current fin year

    # --- Debitor Account Delete View Tests ---
    def test_debitor_account_delete_post_success(self):
        response = self.client.post(reverse('debitor_account_delete', args=[self.debitor1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(DebitorAccount.objects.filter(pk=self.debitor1.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Debitor Account deleted successfully.')

    # --- Autocomplete Suggestions View Test ---
    def test_autocomplete_suggestions_supplier(self):
        response = self.client.get(reverse('autocomplete_suggestions'), {'q': 'Supp', 'context_key': 'key1'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['Supplier A [SUP001]', 'Supplier B [SUP002]'])

    def test_autocomplete_suggestions_customer(self):
        response = self.client.get(reverse('autocomplete_suggestions'), {'q': 'Cust', 'context_key': 'key2'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['Customer X [CUST001]', 'Customer Y [CUST002]'])

    def test_autocomplete_suggestions_empty_query(self):
        response = self.client.get(reverse('autocomplete_suggestions'), {'q': '', 'context_key': 'key1'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), ['Supplier A [SUP001]', 'Supplier B [SUP002]']) # Returns all if empty prefix
```

### Step 5: HTMX and Alpine.js Integration

**Business Value:** This integration transforms a traditional web application into a dynamic, app-like experience. HTMX enables seamless partial page updates, while Alpine.js provides lightweight client-side interactivity, all without the overhead of heavy JavaScript frameworks. This leads to faster loading times, a more fluid user interface, and reduced maintenance complexity for frontend development.

**Instructions:**

*   **Tabs:** The main `main.html` template uses Alpine.js for `activeTab` state and HTMX `hx-get` to load `_creditoraccount_table.html` or `_debitoraccount_table.html` into the `#tabContent` div.
    *   `hx-trigger="click once[activeTab == 'creditors']"` ensures the content is loaded only once when the tab is first clicked, but `refreshCreditorAccountList from:body` on the button allows for explicit refreshes.
*   **Search & DataTables:**
    *   Search inputs (`search_supplier_name`, `search_customer_name`) use `hx-get` to `hx-target` the respective table partial (`#tabContent`), triggering a refresh of the table with filtered data.
    *   The table partials (`_creditoraccount_table.html`, `_debitoraccount_table.html`) include `<script>` tags to initialize DataTables on `$(document).ready()`. `DataTable().destroy()` is called first to prevent re-initialization errors when HTMX reloads the partial.
*   **CRUD Operations with Modals:**
    *   "Add New" buttons (in `_table.html`) use `hx-get` to fetch the form partial (`_form.html`) into `#modalContent` and trigger Alpine.js `showModal = true` to display the modal.
    *   "Edit" and "Delete" buttons in the table rows (`_table.html`) similarly fetch their respective form/confirmation partials into `#modalContent` and show the modal.
    *   Form submissions (`_form.html`, `_confirm_delete.html`) use `hx-post` with `hx-swap="none"`. Upon successful submission (HTTP 204 No Content from Django view), the Django view sends an `HX-Trigger` header:
        *   `refreshCreditorAccountList` or `refreshDebitorAccountList` to automatically re-fetch and update the main table.
        *   `closeModal` (an Alpine.js custom event) to hide the modal. This is handled by a global listener in `main.html` that sets `showModal = false`.
*   **Autocomplete:**
    *   Autocomplete input fields in `_form.html` and `_table.html` use `hx-get` to `{% url 'autocomplete_suggestions' %}`.
    *   `hx-trigger="keyup changed delay:300ms"` ensures suggestions are fetched dynamically as the user types.
    *   `hx-target` points to a `div` below the input (`id_supplier_display_name_autocomplete_results` etc.) where suggestions are displayed.
    *   Alpine.js `_` (hyperscript) on the suggestion container handles hiding the dropdown after a selection is made and populating the input field.

---

### Final Notes

This modernization plan provides a clear, actionable roadmap for transitioning your ASP.NET Creditors/Debitors module to a modern Django application. By focusing on AI-assisted automation, adhering to the "Fat Model, Thin View" architecture, and leveraging HTMX/Alpine.js for a dynamic frontend, we ensure a high-quality, maintainable, and user-friendly system.

Remember to systematically translate the complex SQL queries within the `calculate_*` methods in your Django models. These are placeholders and represent significant business logic that must be accurately replicated using Django ORM, raw SQL queries, or database views as appropriate for optimal performance and data integrity. The `CompId` and `FinYearId` should be dynamically fetched from the user's session or profile in a real-world application, rather than hardcoded.