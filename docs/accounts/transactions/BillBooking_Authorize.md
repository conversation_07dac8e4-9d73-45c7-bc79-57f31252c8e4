This comprehensive modernization plan outlines the transition of your ASP.NET application, specifically the "Bill Booking - Authorize" module, to a modern, efficient, and maintainable Django-based solution. Our focus is on leveraging AI-assisted automation to streamline the migration process, ensuring a smooth transition with minimal manual effort and maximum adherence to contemporary web development best practices.

The goal is to provide a clear, actionable roadmap, presented in plain English, that empowers business stakeholders to understand and oversee the transformation. We will prioritize a "Fat Model, Thin View" architecture, utilizing Django's Class-Based Views (CBVs), HTMX for dynamic frontend interactions, Alpine.js for lightweight UI state management, and DataTables for enhanced data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET code primarily interacts with `tblACC_BillBooking_Master` for bill booking authorization. It also performs lookups against `tblFinancial_master`, `tblMM_Supplier_master`, and `tblHR_OfficeStaff` to enrich the displayed data.

**Identified Tables and Key Columns:**

*   **Main Table:** `tblACC_BillBooking_Master`
    *   `Id` (Primary Key, Integer)
    *   `FinYearId` (Integer, relates to `tblFinancial_master`)
    *   `PVEVNo` (String)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `SupplierId` (String, relates to `tblMM_Supplier_master`)
    *   `Authorize` (Boolean/Integer, 0 for unauthorized, 1 for authorized)
    *   `AuthorizeBy` (String, relates to `tblHR_OfficeStaff`, Nullable)
    *   `AuthorizeDate` (Date, Nullable)
    *   `AuthorizeTime` (Time, Nullable)
    *   *Other columns (e.g., `BillNo`, `BillDate`, `Narration`, etc.) are present but not directly used in the authorization UI.*

*   **Lookup Tables:**
    *   `tblFinancial_master`: `FinYearId`, `FinYear`
    *   `tblMM_Supplier_master`: `SupplierId`, `SupplierName`
    *   `tblHR_OfficeStaff`: `EmpId`, `Title`, `EmployeeName`

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The "Bill Booking - Authorize" module primarily focuses on **Read** and **Update** operations on existing bill booking records. There are no explicit creation or deletion functionalities exposed on this page.

*   **Read (Display/Search):**
    *   The `loadData` method fetches records from `tblACC_BillBooking_Master`.
    *   Filtering is supported based on `SupplierName`, `PVEVNo`, or `PO No` (though PO No is not explicitly mapped in `tblACC_BillBooking_Master` columns in the `SELECT` statement, it's a search option).
    *   A "View All" checkbox controls whether to display only unauthorized bills (`Authorize='0'`) or all bills.
    *   The `GridView` handles pagination.
    *   Supplier name search includes an autocomplete feature, fetching data from `tblMM_Supplier_master`.

*   **Update (Authorize Status):**
    *   The `CkAuth_CheckedChanged` event handles the authorization toggle.
    *   When the checkbox is changed, it updates the `Authorize` status (0 or 1), sets `AuthorizeBy` (current user), `AuthorizeDate` (current date), and `AuthorizeTime` (current time) in `tblACC_BillBooking_Master`.
    *   After update, the page performs a full redirect to refresh the data.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The UI consists of a search/filter section and a data grid.

*   **Search/Filter Controls:**
    *   `DropDownList1`: Allows selecting search criteria (Supplier Name, PVEVNo, PO No). This will be a standard Django `select` element.
    *   `txtSupplier`: Textbox for supplier name, with `AutoCompleteExtender` for suggestions. This will be a standard `input` field with an HTMX-driven autocomplete/typeahead.
    *   `Txtfield`: Textbox for PVEVNo or PO No. This will be a standard `input` field.
    *   `ViewAll`: Checkbox to filter between authorized/unauthorized bills. This will be a standard `checkbox` input.
    *   `Button1` (Search): Triggers the search. This will be an HTMX-powered button to refresh the table.

*   **Data Presentation:**
    *   `GridView2`: Displays the list of bill bookings. Columns include: Serial Number, Financial Year, PVEV No (hyperlink to details), System Date, Name of Supplier, Authorize checkbox, Authorized By, Authorized Date, Authorized Time. This will be converted to a Django template using a `<table>` tag, enhanced with DataTables for client-side features.
    *   `CkAuth`: Checkbox within the `GridView` rows, allowing inline authorization. This will be an HTMX-powered checkbox that sends a request to update the authorization status.

### Step 4: Generate Django Code

We will create a new Django application, for example, `accounts`, to house this module.

#### 4.1 Models

**File:** `accounts/models.py`

This section defines the Django models that map to your existing database tables. We'll use `managed = False` to ensure Django doesn't try to create or modify these tables, as they already exist. We'll also define helper models for the lookup tables to establish relationships and simplify data retrieval.

```python
from django.db import models
from django.db.models import F
from django.db.connections import ConnectionWrapper
from django.db import connection as django_connection # Renamed to avoid conflict with local 'con'

class FinancialYear(models.Model):
    # Maps to tblFinancial_master
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

class Supplier(models.Model):
    # Maps to tblMM_Supplier_master
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId exists

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

class Employee(models.Model):
    # Maps to tblHR_OfficeStaff
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId exists

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name}".strip()

class BillBookingManager(models.Manager):
    """
    Custom manager for BillBooking model to handle complex filtering and
    data enrichment, replacing the ASP.NET loadData logic.
    """
    def get_authorized_bills(self, current_comp_id, current_fin_year_id,
                             search_type=None, search_query=None, view_all=False):
        """
        Fetches Bill Booking records with filtering similar to ASP.NET loadData.
        This method is designed to replicate the complex SQL logic from the
        original application within the Django ORM, utilizing joins for efficiency.
        """
        queryset = self.select_related('financial_year', 'supplier', 'authorized_by').filter(
            comp_id=current_comp_id,
            financial_year__fin_year_id__lte=current_fin_year_id # ASP.NET uses FinYearId<=
        ).order_by('-id') # Order by Id Desc

        if not view_all:
            queryset = queryset.filter(authorize=False) # Only unauthorized bills

        if search_query:
            if search_type == '1': # Supplier Name
                # Fun.getCode was used, implies searching by SupplierName and then filtering by SupplierId
                # Assuming SupplierId is part of the Supplier model, we can directly filter by supplier_name
                # The original code filters by SupplierId after getting it from SupplierName.
                # Here, we directly filter by supplier name on the related supplier object.
                # For exact match behavior as original fun.getCode, ensure supplier_id matches the extracted part.
                # For now, a simple contains search on supplier name.
                queryset = queryset.filter(supplier__supplier_name__icontains=search_query)
            elif search_type == '2': # PVEVNo
                queryset = queryset.filter(pvev_no__iexact=search_query) # Exact match as original
            # elif search_type == '3': # PO No - not directly mapped in current fields, so skip for now.

        return queryset

    def toggle_authorization(self, bill_id, authorize_status, user_id):
        """
        Updates the authorization status for a Bill Booking record.
        This method replaces the CkAuth_CheckedChanged logic.
        """
        try:
            bill = self.get(pk=bill_id)
            bill.authorize = authorize_status
            
            from django.utils import timezone
            now = timezone.now()

            if authorize_status:
                bill.authorized_by_id = user_id # Assign user ID (string in original)
                bill.authorize_date = now.date()
                bill.authorize_time = now.time()
            else:
                bill.authorized_by = None
                bill.authorize_date = None
                bill.authorize_time = None
            
            bill.save()
            return True
        except self.model.DoesNotExist:
            return False
        except Exception as e:
            # Log the exception for debugging
            print(f"Error toggling authorization for Bill ID {bill_id}: {e}")
            return False

class BillBooking(models.Model):
    # Maps to tblACC_BillBooking_Master
    # Assuming 'Id' is the primary key and an IDENTITY column
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', related_name='bill_bookings')
    pvev_no = models.CharField(db_column='PVEVNo', max_length=50)
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId', related_name='bill_bookings')
    bill_no = models.CharField(db_column='BillNo', max_length=50, blank=True, null=True)
    bill_date = models.DateField(db_column='BillDate', blank=True, null=True)
    cen_vat_entry_no = models.CharField(db_column='CENVATEntryNo', max_length=50, blank=True, null=True)
    cen_vat_entry_date = models.DateField(db_column='CENVATEntryDate', blank=True, null=True)
    other_charges = models.DecimalField(db_column='OtherCharges', max_digits=18, decimal_places=2, blank=True, null=True)
    other_cha_desc = models.CharField(db_column='OtherChaDesc', max_length=255, blank=True, null=True)
    narration = models.CharField(db_column='Narration', max_length=500, blank=True, null=True)
    debit_amt = models.DecimalField(db_column='DebitAmt', max_digits=18, decimal_places=2, blank=True, null=True)
    discount_type = models.CharField(db_column='DiscountType', max_length=10, blank=True, null=True)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, blank=True, null=True)
    authorize = models.BooleanField(db_column='Authorize', default=False) # Maps to BIT/TINYINT in SQL Server, 0/1
    authorized_by = models.ForeignKey(Employee, models.DO_NOTHING, db_column='AuthorizeBy', blank=True, null=True, related_name='authorized_bills')
    authorize_date = models.DateField(db_column='AuthorizeDate', blank=True, null=True)
    authorize_time = models.TimeField(db_column='AuthorizeTime', blank=True, null=True)

    objects = BillBookingManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblACC_BillBooking_Master'
        verbose_name = 'Bill Booking'
        verbose_name_plural = 'Bill Bookings'

    def __str__(self):
        return f"Bill: {self.pvev_no} ({self.supplier.supplier_name})"

    # Properties to replicate original display values, if not covered by ForeignKey __str__
    @property
    def display_sys_date_dmy(self):
        """Format SysDate to DD/MM/YYYY as in original 'fun.FromDateDMY'"""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

    @property
    def display_authorize_date_dmy(self):
        """Format AuthorizeDate to DD/MM/YYYY as in original 'fun.FromDateDMY'"""
        return self.authorize_date.strftime('%d/%m/%Y') if self.authorize_date else ''

    @property
    def display_authorized_by_name(self):
        """Replicates 'By' column display"""
        return str(self.authorized_by) if self.authorized_by else ''

    @property
    def display_authorize_time(self):
        """Replicates 'Time' column display"""
        return self.authorize_time.strftime('%H:%M:%S') if self.authorize_time else '' # Adjust format as needed
```

#### 4.2 Forms

**File:** `accounts/forms.py`

For this module, we primarily need a simple form to handle the search parameters, not a `ModelForm` for the `BillBooking` itself.

```python
from django import forms

class BillBookingSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('1', 'Supplier Name'),
        ('2', 'PVEVNo'),
        ('3', 'PO No'), # PO No not directly mapped, but kept for UI consistency
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': 'htmx:reload', 'hx-trigger': 'change', 'hx-target': '#billBookingSearchForm', 'hx-swap': 'outerHTML'}),
        required=False,
        label="Search By"
    )
    search_query_text = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter search query'}),
        label="Search Term"
    )
    search_query_supplier = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter Supplier Name', 'hx-get': '/accounts/billbooking-authorize/supplier-autocomplete/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#supplier-suggestions', 'hx-swap': 'innerHTML', 'autocomplete': 'off'}),
        label="Supplier Name"
    )
    view_all = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded'}),
        label="View All"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Control visibility of search fields based on selected search_type
        selected_type = self.initial.get('search_type', self.data.get('search_type', '1')) # Default to 'Supplier Name'

        self.fields['search_query_text'].widget.attrs['class'] += ' hidden'
        self.fields['search_query_supplier'].widget.attrs['class'] += ' hidden'

        if selected_type == '1':
            self.fields['search_query_supplier'].widget.attrs['class'] = self.fields['search_query_supplier'].widget.attrs['class'].replace(' hidden', '')
        elif selected_type in ['2', '3']:
            self.fields['search_query_text'].widget.attrs['class'] = self.fields['search_query_text'].widget.attrs['class'].replace(' hidden', '')
```

#### 4.3 Views

**File:** `accounts/views.py`

These views handle displaying the bill booking list, the search functionality, the authorization toggle, and the supplier autocomplete. They are designed to be thin, delegating complex logic to the `BillBookingManager`.

```python
from django.views.generic import ListView, View
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.utils import timezone
from django.db import connection # For raw SQL/date formatting if needed
from django.template.loader import render_to_string
from django.shortcuts import render # To render form partial

from .models import BillBooking, Supplier, Employee # Import Employee for AuthorizeBy context
from .forms import BillBookingSearchForm

# Assume these are obtained from session/context.
# In a real application, these would come from authentication system or request.session.
# For demo, use placeholder values.
# IMPORTANT: Replace with actual session/user data in a live system.
CURRENT_COMP_ID = 1 # Example company ID
CURRENT_FIN_YEAR_ID = 2024 # Example financial year ID
CURRENT_USER_ID = "admin_user" # Example user ID (string, matching EmpId in tblHR_OfficeStaff)


class BillBookingAuthorizeListView(ListView):
    """
    Main view to display the Bill Booking Authorization page.
    It renders the search form and an HTMX container for the table.
    """
    model = BillBooking
    template_name = 'accounts/billbooking/authorize_list.html'
    context_object_name = 'bill_bookings' # Renamed from original for clarity
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Initialize form with data from request.GET for persistence
        form = BillBookingSearchForm(self.request.GET)
        
        # Ensure search_query_text/supplier visibility based on selected type
        search_type = self.request.GET.get('search_type', '1') # Default to 'Supplier Name'
        
        # Set initial values for form fields to reflect current state
        initial_data = {
            'search_type': search_type,
            'search_query_text': self.request.GET.get('search_query_text', ''),
            'search_query_supplier': self.request.GET.get('search_query_supplier', ''),
            'view_all': self.request.GET.get('view_all') == 'on' or self.request.GET.get('view_all') == 'True'
        }
        form = BillBookingSearchForm(initial=initial_data)

        # Set specific fields visible/hidden based on selected_type
        if search_type == '1':
            form.fields['search_query_text'].widget.attrs['class'] += ' hidden'
            form.fields['search_query_supplier'].widget.attrs['class'] = form.fields['search_query_supplier'].widget.attrs['class'].replace(' hidden', '')
        elif search_type in ['2', '3']:
            form.fields['search_query_supplier'].widget.attrs['class'] += ' hidden'
            form.fields['search_query_text'].widget.attrs['class'] = form.fields['search_query_text'].widget.attrs['class'].replace(' hidden', '')
        else: # Default or 'Select'
            form.fields['search_query_text'].widget.attrs['class'] += ' hidden'
            form.fields['search_query_supplier'].widget.attrs['class'] += ' hidden'

        context['search_form'] = form
        return context

class BillBookingAuthorizeTablePartialView(ListView):
    """
    Returns only the HTML table content for HTMX swaps.
    This view processes search parameters and fetches filtered data.
    """
    model = BillBooking
    template_name = 'accounts/billbooking/_authorize_table.html'
    context_object_name = 'bill_bookings'

    def get_queryset(self):
        # Access current user and session data
        # For this example, using constants. In real app, use request.user, request.session
        current_comp_id = CURRENT_COMP_ID
        current_fin_year_id = CURRENT_FIN_YEAR_ID

        search_type = self.request.GET.get('search_type', '1') # Default to Supplier Name
        search_query_text = self.request.GET.get('search_query_text', '')
        search_query_supplier = self.request.GET.get('search_query_supplier', '')
        view_all = self.request.GET.get('view_all') == 'on' or self.request.GET.get('view_all') == 'True' # Checkbox returns 'on'

        search_query = ''
        if search_type == '1':
            search_query = search_query_supplier
        elif search_type in ['2', '3']:
            search_query = search_query_text

        # Call the custom manager method to get filtered and enriched data
        queryset = self.model.objects.get_authorized_bills(
            current_comp_id=current_comp_id,
            current_fin_year_id=current_fin_year_id,
            search_type=search_type,
            search_query=search_query,
            view_all=view_all
        )
        return queryset

class BillBookingAuthorizeToggleAuthView(View):
    """
    Handles the authorization toggle via HTMX POST request.
    This view updates the BillBooking record's authorization status.
    """
    def post(self, request, pk, *args, **kwargs):
        # Extract status from request data. HTMX often sends form data
        # Checkbox value is typically 'on' if checked, absent if unchecked
        # Assuming the POST body sends 'authorize_status=on' or 'authorize_status=off' or 'authorize_status=true/false'
        # For a checkbox, typically only 'on' is sent when checked. If not present, it means unchecked.
        authorize_status = request.POST.get('authorize_status') == 'on' or request.POST.get('authorize_status') == 'true'

        # Get current user ID from session/authentication context
        # For this example, using a constant. In a real app, use request.user.username or similar.
        user_id = CURRENT_USER_ID # This must match EmpId from tblHR_OfficeStaff

        success = BillBooking.objects.toggle_authorization(pk, authorize_status, user_id)

        if success:
            messages.success(request, 'Bill authorization status updated successfully.')
            # Signal HTMX to refresh the main table after successful update
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshBillBookingList'})
        else:
            messages.error(request, 'Failed to update bill authorization status.')
            return HttpResponse(status=400, content="Failed to update authorization.")

class SupplierAutoCompleteView(View):
    """
    Provides supplier name suggestions for the autocomplete functionality via HTMX.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('prefixText', '')
        comp_id = CURRENT_COMP_ID # Assuming CompId for filtering suppliers

        if query:
            # Filter suppliers by name containing the query
            # Order by supplier_name for consistent results
            suppliers = Supplier.objects.filter(
                supplier_name__icontains=query,
                comp_id=comp_id # Filter by company ID
            ).values_list('supplier_name', 'supplier_id').order_by('supplier_name')[:10] # Limit to 10 suggestions

            # Format as "SupplierName [SupplierId]" as per original ASP.NET
            suggestions = [f"{name} [{sid}]" for name, sid in suppliers]
            return JsonResponse(suggestions, safe=False)
        return JsonResponse([], safe=False)

```

#### 4.4 Templates

**File:** `accounts/billbooking/authorize_list.html`

```html
{% extends 'core/base.html' %}

{% block title %}Bill Booking - Authorize{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gray-100 p-6 rounded-lg shadow-md mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Bill Booking - Authorize</h2>
        
        <form id="billBookingSearchForm" class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end"
              hx-get="{% url 'accounts:billbooking_authorize_table' %}"
              hx-target="#billBookingTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:select[name='search_type'], click from:input[name='view_all']">
            {% csrf_token %}
            
            <div class="col-span-1">
                <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                {{ search_form.search_type }}
            </div>

            <div class="col-span-1" id="searchQueryFields">
                {% if search_form.search_type.value == '1' %}
                    <label for="{{ search_form.search_query_supplier.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier Name</label>
                    {{ search_form.search_query_supplier }}
                    <div id="supplier-suggestions" class="bg-white border border-gray-300 rounded-md shadow-lg z-10 absolute w-full"></div>
                {% elif search_form.search_type.value == '2' or search_form.search_type.value == '3' %}
                    <label for="{{ search_form.search_query_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Query</label>
                    {{ search_form.search_query_text }}
                {% endif %}
            </div>

            <div class="col-span-1 flex items-center pt-5">
                {{ search_form.view_all }}
                <label for="{{ search_form.view_all.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700">{{ search_form.view_all.label }}</label>
            </div>
            
            <div class="col-span-1 flex justify-start md:justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                    Search
                </button>
            </div>
        </form>
    </div>
    
    <div id="billBookingTable-container"
         hx-trigger="load, refreshBillBookingList from:body"
         hx-get="{% url 'accounts:billbooking_authorize_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML">
        <!-- DataTables table will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Bill Bookings...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js integration for UI state management (if needed, e.g., for modal states)
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js needed for this simple list and HTMX interactions,
        // but it would be initiated here for more complex UI components.
    });
</script>
{% endblock %}
```

**File:** `accounts/billbooking/_authorize_table.html` (Partial for HTMX)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="billBookingTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PVEV No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorize</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date (Auth)</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time (Auth)</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if bill_bookings %}
                {% for bill in bill_bookings %}
                <tr>
                    <td class="py-3 px-4 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center">{{ bill.financial_year.fin_year }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center">
                        <a href="{% url 'bill_booking_details_print' bill.id %}" target="_blank" class="text-blue-600 hover:text-blue-800">
                            {{ bill.pvev_no }}
                        </a>
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap text-center">{{ bill.display_sys_date_dmy }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-left">{{ bill.supplier.supplier_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center">
                        <input type="checkbox"
                                name="authorize_status"
                                {% if bill.authorize %}checked{% endif %}
                                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                hx-post="{% url 'accounts:billbooking_authorize_toggle_auth' bill.id %}"
                                hx-trigger="change"
                                hx-swap="none" {# No swap, trigger will refresh table #}
                                >
                    </td>
                    <td class="py-3 px-4 whitespace-nowrap text-left">{{ bill.display_authorized_by_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center">{{ bill.display_authorize_date_dmy }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center">{{ bill.display_authorize_time }}</td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="9" class="py-6 px-4 text-center text-lg font-medium text-red-700">No data to display!</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#billBookingTable').DataTable({
            "pageLength": 10, // Default page size
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 5] } // SN and Authorize columns not orderable
            ]
        });
    });
</script>
```
**Note:** The URL for `bill_booking_details_print` is a placeholder. You would need to create a corresponding Django view and URL for that functionality.

#### 4.5 URLs

**File:** `accounts/urls.py`

This file defines the URL patterns for your `accounts` Django application.

```python
from django.urls import path
from .views import (
    BillBookingAuthorizeListView,
    BillBookingAuthorizeTablePartialView,
    BillBookingAuthorizeToggleAuthView,
    SupplierAutoCompleteView,
)

app_name = 'accounts' # Namespace for this app's URLs

urlpatterns = [
    path('billbooking-authorize/', BillBookingAuthorizeListView.as_view(), name='billbooking_authorize_list'),
    path('billbooking-authorize/table/', BillBookingAuthorizeTablePartialView.as_view(), name='billbooking_authorize_table'),
    path('billbooking-authorize/toggle-auth/<int:pk>/', BillBookingAuthorizeToggleAuthView.as_view(), name='billbooking_authorize_toggle_auth'),
    path('billbooking-authorize/supplier-autocomplete/', SupplierAutoCompleteView.as_view(), name='billbooking_authorize_supplier_autocomplete'),
    # Placeholder for BillBooking_Print_Details.aspx
    path('billbooking-details-print/<int:pk>/', View.as_view(template_name='accounts/billbooking/details_print.html'), name='bill_booking_details_print'), # Replace with actual view
]

```
**Note:** You would need to include this `accounts/urls.py` in your project's main `urls.py`. For example:
`path('accounts/', include('accounts.urls')),`

#### 4.6 Tests

**File:** `accounts/tests.py`

This section provides comprehensive tests for your models and views, ensuring functionality and adherence to requirements.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock

from .models import BillBooking, FinancialYear, Supplier, Employee
from .views import CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, CURRENT_USER_ID

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialYear.objects.create(fin_year_id=2024, fin_year='2024-2025')
        FinancialYear.objects.create(fin_year_id=2023, fin_year='2023-2024')

    def test_financial_year_creation(self):
        fy = FinancialYear.objects.get(fin_year_id=2024)
        self.assertEqual(fy.fin_year, '2024-2025')
        self.assertEqual(str(fy), '2024-2025')
        self.assertEqual(fy._meta.db_table, 'tblFinancial_master')
        self.assertFalse(fy._meta.managed)

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Supplier.objects.create(supplier_id='SUP001', supplier_name='Test Supplier A', comp_id=CURRENT_COMP_ID)
        Supplier.objects.create(supplier_id='SUP002', supplier_name='Test Supplier B', comp_id=CURRENT_COMP_ID)

    def test_supplier_creation(self):
        sup = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(sup.supplier_name, 'Test Supplier A')
        self.assertEqual(str(sup), 'Test Supplier A [SUP001]')
        self.assertEqual(sup._meta.db_table, 'tblMM_Supplier_master')
        self.assertFalse(sup._meta.managed)

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Employee.objects.create(emp_id='EMP001', title='Mr.', employee_name='John Doe', comp_id=CURRENT_COMP_ID)
        Employee.objects.create(emp_id=CURRENT_USER_ID, title='Ms.', employee_name='Jane Smith', comp_id=CURRENT_COMP_ID)


    def test_employee_creation(self):
        emp = Employee.objects.get(emp_id='EMP001')
        self.assertEqual(emp.employee_name, 'John Doe')
        self.assertEqual(str(emp), 'Mr. John Doe')
        self.assertEqual(emp._meta.db_table, 'tblHR_OfficeStaff')
        self.assertFalse(emp._meta.managed)

class BillBookingModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fy_2024 = FinancialYear.objects.create(fin_year_id=CURRENT_FIN_YEAR_ID, fin_year='2024-2025')
        cls.supplier_a = Supplier.objects.create(supplier_id='SUPA', supplier_name='Supplier Alpha', comp_id=CURRENT_COMP_ID)
        cls.supplier_b = Supplier.objects.create(supplier_id='SUPB', supplier_name='Supplier Beta', comp_id=CURRENT_COMP_ID)
        cls.employee_admin = Employee.objects.create(emp_id=CURRENT_USER_ID, title='Mr.', employee_name='Admin User', comp_id=CURRENT_COMP_ID)
        cls.employee_other = Employee.objects.create(emp_id='OTHER', title='Ms.', employee_name='Other Person', comp_id=CURRENT_COMP_ID)

        cls.bill1 = BillBooking.objects.create(
            id=1, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=CURRENT_COMP_ID, financial_year=cls.fy_2024, pvev_no='PVEV001',
            supplier=cls.supplier_a, authorize=False
        )
        cls.bill2 = BillBooking.objects.create(
            id=2, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=CURRENT_COMP_ID, financial_year=cls.fy_2024, pvev_no='PVEV002',
            supplier=cls.supplier_b, authorize=True, authorized_by=cls.employee_other,
            authorize_date=timezone.localdate(), authorize_time=timezone.localtime().time()
        )
        # Bill from a different company/fin year to test filtering
        BillBooking.objects.create(
            id=3, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=CURRENT_COMP_ID + 1, financial_year=cls.fy_2024, pvev_no='PVEV003',
            supplier=cls.supplier_a, authorize=False
        )
        BillBooking.objects.create(
            id=4, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=CURRENT_COMP_ID, financial_year=FinancialYear.objects.create(fin_year_id=2020, fin_year='2020-2021'), pvev_no='PVEV004',
            supplier=cls.supplier_b, authorize=False
        )

    def test_bill_booking_creation(self):
        bill = BillBooking.objects.get(id=1)
        self.assertFalse(bill.authorize)
        self.assertEqual(bill.supplier.supplier_name, 'Supplier Alpha')
        self.assertEqual(bill.financial_year.fin_year, '2024-2025')
        self.assertEqual(bill._meta.db_table, 'tblACC_BillBooking_Master')
        self.assertFalse(bill._meta.managed)

    def test_display_properties(self):
        bill = BillBooking.objects.get(id=2)
        self.assertEqual(bill.display_sys_date_dmy, timezone.localdate().strftime('%d/%m/%Y'))
        self.assertEqual(bill.display_authorize_date_dmy, timezone.localdate().strftime('%d/%m/%Y'))
        self.assertEqual(bill.display_authorized_by_name, str(self.employee_other))
        # Test time format, may need to adjust if exact format differs from default strftime
        self.assertIsNotNone(bill.display_authorize_time)

    def test_toggle_authorization_true(self):
        bill = BillBooking.objects.get(id=1)
        self.assertFalse(bill.authorize)
        BillBooking.objects.toggle_authorization(bill.id, True, CURRENT_USER_ID)
        bill.refresh_from_db()
        self.assertTrue(bill.authorize)
        self.assertEqual(bill.authorized_by, self.employee_admin)
        self.assertEqual(bill.authorize_date, timezone.localdate())
        self.assertIsNotNone(bill.authorize_time)

    def test_toggle_authorization_false(self):
        bill = BillBooking.objects.get(id=2)
        self.assertTrue(bill.authorize)
        BillBooking.objects.toggle_authorization(bill.id, False, CURRENT_USER_ID)
        bill.refresh_from_db()
        self.assertFalse(bill.authorize)
        self.assertIsNone(bill.authorized_by)
        self.assertIsNone(bill.authorize_date)
        self.assertIsNone(bill.authorize_time)

    def test_get_authorized_bills_default_filter(self):
        # Default should only show unauthorized bills for current comp/fin year
        bills = BillBooking.objects.get_authorized_bills(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, view_all=False)
        self.assertEqual(bills.count(), 1) # Only bill1 is unauthorized and in current comp/fin year
        self.assertEqual(bills.first().id, self.bill1.id)

    def test_get_authorized_bills_view_all(self):
        # View all should show authorized and unauthorized for current comp/fin year
        bills = BillBooking.objects.get_authorized_bills(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, view_all=True)
        self.assertEqual(bills.count(), 2) # bill1 (unauth), bill2 (auth)
        self.assertIn(self.bill1, bills)
        self.assertIn(self.bill2, bills)

    def test_get_authorized_bills_search_supplier(self):
        bills = BillBooking.objects.get_authorized_bills(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, search_type='1', search_query='Alpha', view_all=True)
        self.assertEqual(bills.count(), 1)
        self.assertEqual(bills.first().supplier.supplier_name, 'Supplier Alpha')

    def test_get_authorized_bills_search_pvevno(self):
        bills = BillBooking.objects.get_authorized_bills(CURRENT_COMP_ID, CURRENT_FIN_YEAR_ID, search_type='2', search_query='PVEV002', view_all=True)
        self.assertEqual(bills.count(), 1)
        self.assertEqual(bills.first().pvev_no, 'PVEV002')


class BillBookingAuthorizeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fy = FinancialYear.objects.create(fin_year_id=CURRENT_FIN_YEAR_ID, fin_year='2024-2025')
        cls.supplier = Supplier.objects.create(supplier_id='SUPTEST', supplier_name='Test Supplier', comp_id=CURRENT_COMP_ID)
        cls.employee = Employee.objects.create(emp_id=CURRENT_USER_ID, title='Mr.', employee_name='Test User', comp_id=CURRENT_COMP_ID)

        cls.bill = BillBooking.objects.create(
            id=100, sys_date=timezone.localdate(), sys_time=timezone.localtime().time(),
            comp_id=CURRENT_COMP_ID, financial_year=cls.fy, pvev_no='PVEV100',
            supplier=cls.supplier, authorize=False
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('accounts:billbooking_authorize_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/authorize_list.html')
        self.assertIn('search_form', response.context)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('accounts:billbooking_authorize_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/_authorize_table.html')
        self.assertIn('bill_bookings', response.context)
        self.assertEqual(response.context['bill_bookings'].count(), 1) # Only bill 100 is unauth

    def test_table_partial_view_get_view_all(self):
        response = self.client.get(reverse('accounts:billbooking_authorize_table'), {'view_all': 'on'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/_authorize_table.html')
        self.assertIn('bill_bookings', response.context)
        self.assertEqual(response.context['bill_bookings'].count(), 1) # Bill 100 is still the only one in the current setup.
                                                                        # This relies on setUpTestData to have more bills.

    def test_table_partial_view_get_search_supplier(self):
        response = self.client.get(reverse('accounts:billbooking_authorize_table'), {'search_type': '1', 'search_query_supplier': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/_authorize_table.html')
        self.assertIn('bill_bookings', response.context)
        self.assertEqual(response.context['bill_bookings'].count(), 1)
        self.assertEqual(response.context['bill_bookings'].first().supplier.supplier_name, 'Test Supplier')

    def test_table_partial_view_get_search_pvevno(self):
        response = self.client.get(reverse('accounts:billbooking_authorize_table'), {'search_type': '2', 'search_query_text': 'PVEV100'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/billbooking/_authorize_table.html')
        self.assertIn('bill_bookings', response.context)
        self.assertEqual(response.context['bill_bookings'].count(), 1)
        self.assertEqual(response.context['bill_bookings'].first().pvev_no, 'PVEV100')

    def test_toggle_auth_view_post_authorize(self):
        self.assertFalse(self.bill.authorize)
        response = self.client.post(reverse('accounts:billbooking_authorize_toggle_auth', args=[self.bill.id]), {'authorize_status': 'on'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshBillBookingList')
        self.bill.refresh_from_db()
        self.assertTrue(self.bill.authorize)
        self.assertIsNotNone(self.bill.authorized_by)
        self.assertIsNotNone(self.bill.authorize_date)
        self.assertIsNotNone(self.bill.authorize_time)

    def test_toggle_auth_view_post_unauthorize(self):
        # First authorize the bill
        BillBooking.objects.toggle_authorization(self.bill.id, True, CURRENT_USER_ID)
        self.bill.refresh_from_db()
        self.assertTrue(self.bill.authorize)

        # Then unauthorize it
        response = self.client.post(reverse('accounts:billbooking_authorize_toggle_auth', args=[self.bill.id]), {'authorize_status': 'off'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshBillBookingList')
        self.bill.refresh_from_db()
        self.assertFalse(self.bill.authorize)
        self.assertIsNone(self.bill.authorized_by)
        self.assertIsNone(self.bill.authorize_date)
        self.assertIsNone(self.bill.authorize_time)

    def test_supplier_autocomplete_view(self):
        Supplier.objects.create(supplier_id='ANOTHER', supplier_name='Another Supplier', comp_id=CURRENT_COMP_ID)
        response = self.client.get(reverse('accounts:billbooking_authorize_supplier_autocomplete'), {'prefixText': 'Test'})
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response.json(), list)
        self.assertIn('Test Supplier [SUPTEST]', response.json())
        self.assertNotIn('Another Supplier [ANOTHER]', response.json())

    def test_supplier_autocomplete_view_empty_query(self):
        response = self.client.get(reverse('accounts:billbooking_authorize_supplier_autocomplete'), {'prefixText': ''})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates and views are designed with HTMX and Alpine.js in mind, ensuring a highly dynamic and responsive user experience without traditional full-page reloads.

*   **HTMX for Dynamic Updates:**
    *   **Search and Filter:** The search form (`#billBookingSearchForm`) uses `hx-get` to trigger a request to `{% url 'accounts:billbooking_authorize_table' %}` whenever the form is submitted, or `search_type` dropdown changes, or `view_all` checkbox is clicked. The response (the partial `_authorize_table.html`) is swapped into `#billBookingTable-container`. This replaces the ASP.NET PostBack mechanism.
    *   **Authorization Toggle:** Each authorization checkbox (`input[name="authorize_status"]`) within the table has `hx-post` pointing to `{% url 'accounts:billbooking_authorize_toggle_auth' bill.id %}`. When the checkbox's state changes (`hx-trigger="change"`), a POST request is sent. The view responds with `status=204` (No Content) and `HX-Trigger: 'refreshBillBookingList'`, which tells HTMX to re-fetch and re-render the entire table, thus updating the "By", "Date", "Time" columns.
    *   **Autocomplete:** The `search_query_supplier` input uses `hx-get` to `{% url 'accounts:billbooking_authorize_supplier_autocomplete' %}` on `keyup` events (with a delay). The response is a JSON array of suggestions, which is then displayed in the `#supplier-suggestions` div. You would typically use a simple Alpine.js component or a custom JS snippet to bind these suggestions back to the input field on click.
*   **Alpine.js for UI State Management:**
    *   While explicit Alpine.js usage is minimal in these specific templates (as HTMX handles most dynamic interactions), it's available and recommended for more complex client-side UI states, such as managing modal visibility, tab switching, or simple form validation feedback without server-side roundtrips. For instance, the `_authorize_table.html` could use Alpine.js to toggle sorting icons or similar minor visual states if DataTables alone doesn't suffice for all UI needs.
*   **DataTables for List Views:**
    *   The `_authorize_table.html` partial explicitly initializes DataTables on the `<table>` element (`#billBookingTable`) using jQuery. This provides client-side searching, sorting, and pagination, mirroring the functionality of the ASP.NET `GridView` but with enhanced modern UI/UX. The necessary DataTables CSS and JS (including jQuery) would be loaded in `core/base.html` from CDNs.

## Final Notes

*   **Placeholders:** Ensure all placeholders like `CURRENT_COMP_ID`, `CURRENT_FIN_YEAR_ID`, `CURRENT_USER_ID` are replaced with actual dynamic values from your Django authentication and session management.
*   **DRY Templates:** The use of `{% extends 'core/base.html' %}` and a partial template (`_authorize_table.html`) ensures a DRY approach, preventing repetitive code.
*   **Fat Models, Thin Views:** Business logic, such as data filtering and authorization updates, is encapsulated within the `BillBookingManager` and `BillBooking` model, keeping views concise and focused on orchestrating requests and responses.
*   **Comprehensive Tests:** The provided tests aim for high coverage, verifying model methods, view responses, and HTMX interactions, crucial for maintaining code quality during and after migration.
*   **Scalability:** This architecture supports horizontal scaling by separating backend logic (Django) from frontend interactivity (HTMX/Alpine.js), enabling independent development and deployment of components.

This structured migration plan, combining AI-driven analysis with modern Django patterns, provides a clear pathway for transforming your legacy ASP.NET application into a robust, maintainable, and performant Django solution.