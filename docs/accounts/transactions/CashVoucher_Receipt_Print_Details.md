This document outlines a comprehensive modernization plan to transition the provided ASP.NET Crystal Report display page to a modern Django-based solution. The focus is on leveraging Django's robust features, promoting the "fat model, thin view" architecture, and utilizing modern frontend technologies like HTMX and Alpine.js for dynamic interactions.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination (where applicable, for list views)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code retrieves data from `tblACC_CashVoucher_Receipt_Master` and performs joins with `AccHead` and `BusinessGroup`. It also conditionally fetches data from `tblMIS_BudgetCode`. Furthermore, the `fun.EmpCustSupplierNames` helper function suggests lookup tables for Employees, Customers, and Suppliers, while `fun.CompAdd` points to a Company table for address details. The data is then transformed and presented in a report-specific `DataSet`.

**Primary Table for this Module:**
- `tblACC_CashVoucher_Receipt_Master`

**Related Lookup Tables (inferred from joins and helper functions):**
- `AccHead`
- `BusinessGroup`
- `tblMIS_BudgetCode`
- `tbl_Employees` (hypothesized for `EmpCustSupplierNames` type 1)
- `tbl_Customers` (hypothesized for `EmpCustSupplierNames` type 2)
- `tbl_Suppliers` (hypothesized for `EmpCustSupplierNames` type 3)
- `Company` (hypothesized for `fun.CompAdd`)

**Key Columns (from `tblACC_CashVoucher_Receipt_Master`):**
- `Id` (Primary Key, Integer)
- `BudgetCode` (Integer, FK to `tblMIS_BudgetCode`)
- `SysDate` (String, needs date parsing)
- `SysTime` (String)
- `CompId` (Integer, FK to `Company`)
- `FinYearId` (Integer)
- `SessionId` (Integer)
- `CVRNo` (String)
- `CashReceivedAgainst` (String/Integer, ID for Employee/Customer/Supplier lookup)
- `CashReceivedBy` (String/Integer, ID for Employee/Customer/Supplier lookup)
- `WONo` (String)
- `BGGroup` (Integer, FK to `BusinessGroup`)
- `AcHead` (Integer, FK to `AccHead`)
- `Amount` (Float/Double)
- `Others` (String, used for Remarks)
- `CodeTypeRA` (Integer, 1: Employee, 2: Customer, 3: Supplier)
- `CodeTypeRB` (Integer, 1: Employee, 2: Customer, 3: Supplier)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET page `CashVoucher_Receipt_Print_Details.aspx` is designed purely for **displaying a single detailed report** (Cash Voucher Receipt) based on an ID (`CVRId`) passed via the query string. It performs extensive data retrieval, joining, and transformation before rendering it via Crystal Reports. There are no Create, Update, or Delete operations on this page itself. The `btnCancel_Click` event simply redirects the user away from this report view.

**Functionality Summary:**
- **Read (Detailed Report):** Fetches a specific `CashVoucherReceiptMaster` record and associated lookup data (Account Head, Business Group, Budget Code, Employee/Customer/Supplier names, Company Address).
- **Data Transformation:** Processes raw database fields (e.g., `SysDate` string to formatted date, `CashReceivedAgainst` code to a displayable name and type) to prepare them for the report.
- **Redirection:** A "Cancel" button to navigate back to a list view.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page's primary UI component is a `CrystalReportViewer`, indicating its role as a dedicated report display interface. It contains a `Panel` for scrolling, suggesting the report can be extensive. A `Button` (`btnCancel`) provides navigation.

**Django Equivalent UI Components:**
-   **HTML Template:** A dedicated Django template (`print_details.html`) structured to present the report data, designed with print-friendly CSS.
-   **No DataTables:** Since this is a single detailed report, not a list, DataTables are not applicable here.
-   **No HTMX/Alpine.js for Report Content:** The report content itself will be rendered server-side. HTMX/Alpine.js could be used for surrounding UI elements (e.g., a print button, a "back" button) but not for the report's dynamic generation, which is handled by the server. The "Cancel" button will be a standard link or form submission.

### Step 4: Generate Django Code

We will create a new Django application (e.g., `accounts`) to house these components.

#### 4.1 Models

The models will reflect the database schema, with `managed = False` for existing tables. The `CashVoucherReceiptMaster` model will be "fat" by including methods for data transformation and lookup, encapsulating the complex logic previously found in the ASP.NET code-behind.

```python
# accounts/models.py
from django.db import models
import datetime

# --- Auxiliary Models (Placeholders, assume existing tables) ---
# These models are crucial for mapping foreign keys and performing lookups.
# In a real migration, their fields would be fully defined based on actual schema.

class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)
    address = models.TextField(db_column='Address', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Company' # Assumed table name for company details
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

    @classmethod
    def get_company_address(cls, comp_id):
        """Retrieves company address, mimicking fun.CompAdd."""
        try:
            return cls.objects.get(id=comp_id).address
        except cls.DoesNotExist:
            return "N/A"

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol or f"Account Head {self.id}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or f"Business Group {self.id}"

class BudgetCode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return self.symbol or f"Budget Code {self.id}"

# Generic Base class for Employee, Customer, Supplier for common fields (ID, Name)
class BaseParty(models.Model):
    id = models.CharField(db_column='Id', primary_key=True, max_length=50) # Assuming IDs can be strings
    name = models.CharField(db_column='Name', max_length=255, blank=True, null=True)

    class Meta:
        abstract = True # This is a base class, not a direct table mapping

    def __str__(self):
        return self.name or f"Party {self.id}"

class Employee(BaseParty):
    class Meta:
        managed = False
        db_table = 'tbl_Employees' # Example table name based on common ERP patterns
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

class Customer(BaseParty):
    class Meta:
        managed = False
        db_table = 'tbl_Customers' # Example table name
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

class Supplier(BaseParty):
    class Meta:
        managed = False
        db_table = 'tbl_Suppliers' # Example table name
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'


# --- Main Model for the Report ---

class CashVoucherReceiptMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', blank=True, null=True)
    sys_date = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True) # Original stored as string 'MM-DD-YYYY' or 'DD-MM-YYYY'
    sys_time = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    comp_id = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    session_id = models.IntegerField(db_column='SessionId', blank=True, null=True)
    cvr_no = models.CharField(db_column='CVRNo', max_length=50, blank=True, null=True)
    cash_received_against_code = models.CharField(db_column='CashReceivedAgainst', max_length=50, blank=True, null=True) # This is the ID/code
    cash_received_by_code = models.CharField(db_column='CashReceivedBy', max_length=50, blank=True, null=True) # This is the ID/code
    wo_no = models.CharField(db_column='WONo', max_length=100, blank=True, null=True)
    bg_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', blank=True, null=True)
    ac_head = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='AcHead', blank=True, null=True)
    amount = models.FloatField(db_column='Amount', blank=True, null=True)
    others = models.CharField(db_column='Others', max_length=255, blank=True, null=True) # This is the Remarks field
    code_type_ra = models.IntegerField(db_column='CodeTypeRA', blank=True, null=True) # 1=Employee, 2=Customer, 3=Supplier
    code_type_rb = models.IntegerField(db_column='CodeTypeRB', blank=True, null=True) # 1=Employee, 2=Customer, 3=Supplier

    class Meta:
        managed = False
        db_table = 'tblACC_CashVoucher_Receipt_Master'
        verbose_name = 'Cash Voucher Receipt'
        verbose_name_plural = 'Cash Voucher Receipts'

    def __str__(self):
        return f"CVR No: {self.cvr_no or 'N/A'} (ID: {self.id})"

    # --- Business Logic Methods (Fat Model Approach) ---

    def _get_party_name_and_type(self, code_type, party_code):
        """
        Helper method to look up party name based on type code and ID.
        Mimics original fun.EmpCustSupplierNames.
        """
        party_name = "N/A"
        party_type_str = "N/A"

        if party_code:
            try:
                if code_type == 1: # Employee
                    party_obj = Employee.objects.get(id=party_code)
                    party_name = party_obj.name
                    party_type_str = "Employee"
                elif code_type == 2: # Customer
                    party_obj = Customer.objects.get(id=party_code)
                    party_name = party_obj.name
                    party_type_str = "Customer"
                elif code_type == 3: # Supplier
                    party_obj = Supplier.objects.get(id=party_code)
                    party_name = party_obj.name
                    party_type_str = "Supplier"
            except (Employee.DoesNotExist, Customer.DoesNotExist, Supplier.DoesNotExist):
                party_name = "Code not found"
            except Exception as e:
                # Log this error in a real application
                party_name = "Lookup error"

        return f"{party_name} ( {party_type_str.upper()} )"

    @property
    def formatted_sys_date(self):
        """
        Transforms the 'SysDate' string from the database to a standard 'DD-MM-YYYY' format.
        This mimics the complex SQL CONVERT/REPLACE logic.
        """
        if self.sys_date:
            try:
                # Attempt to parse 'MM-DD-YYYY' or 'DD-MM-YYYY'
                # The original C# code uses format 103 (DD/MM/YYYY)
                # It means the raw string `SysDate` could be `MM-DD-YYYY` or `DD-MM-YYYY`
                
                # First, try parsing as DD-MM-YYYY
                try:
                    dt_obj = datetime.datetime.strptime(self.sys_date, '%d-%m-%Y').date()
                    return dt_obj.strftime('%d-%m-%Y')
                except ValueError:
                    # If that fails, try parsing as MM-DD-YYYY
                    dt_obj = datetime.datetime.strptime(self.sys_date, '%m-%d-%Y').date()
                    return dt_obj.strftime('%d-%m-%Y')
            except ValueError:
                # If all parsing fails, return original or a default
                return self.sys_date 
        return "N/A"

    @property
    def cash_received_against_display(self):
        """Returns the formatted name for 'Cash Received Against'."""
        return self._get_party_name_and_type(self.code_type_ra, self.cash_received_against_code)

    @property
    def cash_received_by_display(self):
        """Returns the formatted name for 'Cash Received By'."""
        return self._get_party_name_and_type(self.code_type_rb, self.cash_received_by_code)

    @property
    def budget_code_symbol_display(self):
        """Returns the symbol for the associated Budget Code or 'NA'."""
        if self.budget_code:
            return self.budget_code.symbol
        return "NA"

    @property
    def company_address(self):
        """Returns the address of the associated Company."""
        if self.comp_id:
            return self.comp_id.address
        return "N/A"

    def get_report_data(self):
        """
        Consolidates and transforms all necessary data into a dictionary
        suitable for rendering the detailed report, mimicking the DataTable
        creation and population in the original C# code.
        """
        return {
            'id': self.id,
            'date': self.formatted_sys_date,
            'cvr_no': self.cvr_no,
            'cash_received_against': self.cash_received_against_display,
            'cash_received_by': self.cash_received_by_display,
            'wo_no': self.wo_no,
            'bg_group': self.bg_group.symbol if self.bg_group else 'N/A',
            'ac_head': self.ac_head.symbol if self.ac_head else 'N/A',
            'budget_code': self.budget_code_symbol_display,
            'amount': self.amount,
            'remarks': self.others,
            'comp_id': self.comp_id.id if self.comp_id else 'N/A',
            'company_address': self.company_address,
        }

```

#### 4.2 Forms

**Analysis:**
The original ASP.NET page is purely a report display, not a data entry or modification form. Therefore, no Django forms are required for `CashVoucherReceiptMaster` on this specific page.

#### 4.3 Views

We will use a Django `DetailView` to fetch the specific `CashVoucherReceiptMaster` record and prepare its data for the report template. A `RedirectView` will handle the "Cancel" functionality.

```python
# accounts/views.py
from django.views.generic import DetailView, RedirectView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404, redirect
from django.http import Http404, HttpResponseBadRequest
from .models import CashVoucherReceiptMaster, Company # Ensure Company model is imported for clarity

class CashVoucherReceiptPrintView(DetailView):
    """
    Displays a detailed report for a specific Cash Voucher Receipt.
    Mimics the CashVoucher_Receipt_Print_Details.aspx functionality.
    """
    model = CashVoucherReceiptMaster
    template_name = 'accounts/cashvoucherreceiptmaster/print_details.html'
    context_object_name = 'cvr_report_data' # Name for the transformed data in the template

    def get_object(self, queryset=None):
        """
        Retrieves the CashVoucherReceiptMaster object based on 'pk' from URL.
        This 'pk' corresponds to the 'CVRId' query string parameter in ASP.NET.
        """
        pk = self.kwargs.get(self.pk_url_kwarg)
        if not pk:
            # This should ideally be caught by URL routing, but good for robustness
            raise HttpResponseBadRequest("A valid Cash Voucher Receipt ID (pk) must be provided.")
        
        # Original C# code fetches a single record, so we ensure that.
        # Use select_related to fetch related objects in one query for efficiency.
        try:
            return self.model.objects.select_related(
                'budget_code', 'bg_group', 'ac_head', 'comp_id'
            ).get(pk=pk)
        except self.model.DoesNotExist:
            raise Http404(f"Cash Voucher Receipt with ID {pk} not found.")

    def get_context_data(self, **kwargs):
        """
        Adds the transformed report data (from the fat model's method)
        and the company address to the template context.
        """
        context = super().get_context_data(**kwargs)
        cvr_instance = self.object # The CashVoucherReceiptMaster object fetched by get_object()
        
        # Populate context with the transformed report data from the model
        context['cvr_report_data'] = cvr_instance.get_report_data()
        
        # Company address parameter for the report (mimicking Crystal Reports parameter)
        context['company_address'] = cvr_instance.company_address
        
        return context

class CashVoucherPrintRedirectView(RedirectView):
    """
    Handles the "Cancel" button click, redirecting to the main Cash Voucher print list.
    """
    # Assuming 'accounts:cashvoucher_list' is the URL name for the target list page.
    # This URL should correspond to the page ASP.NET redirects to:
    # "~/Module/Accounts/Transactions/CashVoucher_Print.aspx?ModId=11&SubModId=113"
    # Replace 'accounts:cashvoucher_list' with the actual URL name for your Cash Voucher list view.
    permanent = False # It's a temporary redirect, not permanent move
    query_string = True # Pass original query string params if needed
    url = reverse_lazy('accounts:cashvoucher_list') 
    
    def get_redirect_url(self, *args, **kwargs):
        # You might want to reconstruct specific query parameters if needed
        # For simplicity, we just use the reverse_lazy defined URL.
        # If 'ModId' and 'SubModId' are required in the new Django URL,
        # adjust reverse_lazy or override this method to add them.
        return super().get_redirect_url(*args, **kwargs)

```

#### 4.4 Templates

The template will display the structured report data. It will be designed for clear presentation and print-friendliness. DataTables are not used here as it's a single record detail view.

```html
<!-- accounts/templates/accounts/cashvoucherreceiptmaster/print_details.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col items-center justify-center min-h-screen-nonav print:block">
        <div class="bg-white p-8 rounded-lg shadow-lg w-full max-w-4xl print:shadow-none print:p-0">
            <!-- Header Section (Company Address, Title) -->
            <div class="text-center mb-8 print:mb-4">
                <h1 class="text-3xl font-bold text-gray-800 mb-2 print:text-xl">{{ cvr_report_data.company_address }}</h1>
                <h2 class="text-2xl font-semibold text-gray-700 mb-4 print:text-lg">Cash Voucher / Receipt Details</h2>
                <hr class="border-t-2 border-gray-300 print:hidden">
            </div>

            <!-- Report Details Section -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-gray-700 text-lg print:text-base print:gap-x-4 print:gap-y-2">
                <div>
                    <strong class="font-medium">CVR No:</strong> {{ cvr_report_data.cvr_no }}
                </div>
                <div>
                    <strong class="font-medium">Date:</strong> {{ cvr_report_data.date }}
                </div>
                <div>
                    <strong class="font-medium">Cash Received Against:</strong> {{ cvr_report_data.cash_received_against }}
                </div>
                <div>
                    <strong class="font-medium">Cash Received By:</strong> {{ cvr_report_data.cash_received_by }}
                </div>
                <div>
                    <strong class="font-medium">WO No:</strong> {{ cvr_report_data.wo_no }}
                </div>
                <div>
                    <strong class="font-medium">BG Group:</strong> {{ cvr_report_data.bg_group }}
                </div>
                <div>
                    <strong class="font-medium">Account Head:</strong> {{ cvr_report_data.ac_head }}
                </div>
                <div>
                    <strong class="font-medium">Budget Code:</strong> {{ cvr_report_data.budget_code }}
                </div>
                <div class="md:col-span-2">
                    <strong class="font-medium">Amount:</strong> <span class="text-green-600 font-bold">₹ {{ cvr_report_data.amount|floatformat:2 }}</span>
                </div>
                <div class="md:col-span-2">
                    <strong class="font-medium">Remarks:</strong> {{ cvr_report_data.remarks|default:"N/A" }}
                </div>
            </div>

            <!-- Action Buttons (Hidden in Print) -->
            <div class="mt-8 flex justify-center space-x-4 print:hidden">
                <a href="{% url 'accounts:cashvoucher_list' %}"
                   class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-lg shadow-md transition duration-300">
                    Cancel
                </a>
                <button onclick="window.print()"
                        class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-lg shadow-md transition duration-300">
                    Print Report
                </button>
            </div>
        </div>
    </div>
</div>

<style>
    /* Basic print styles */
    @media print {
        body {
            background-color: #fff;
            margin: 0;
            padding: 0;
            font-size: 12pt;
        }
        .container {
            width: 100%;
            padding: 0;
            margin: 0;
        }
        .print\\:shadow-none {
            box-shadow: none !important;
        }
        .print\\:p-0 {
            padding: 0 !important;
        }
        .print\\:block {
            display: block !important;
        }
        .print\\:mb-4 {
            margin-bottom: 1rem !important;
        }
        .print\\:text-xl {
            font-size: 1.25rem !important;
        }
        .print\\:text-lg {
            font-size: 1.125rem !important;
        }
        .print\\:text-base {
            font-size: 1rem !important;
        }
        .print\\:gap-x-4 {
            column-gap: 1rem !important;
        }
        .print\\:gap-y-2 {
            row-gap: 0.5rem !important;
        }
        .print\\:hidden {
            display: none !important;
        }
        .min-h-screen-nonav {
            min-height: auto !important; /* Override layout height for print */
        }
    }
</style>
{% endblock %}

{% comment %}
No specific Alpine.js or HTMX for this read-only report view,
beyond potential interactions with the base template or global components.
{% endcomment %}
```

#### 4.5 URLs

We'll define a URL path for the report view and for the "Cancel" redirection.

```python
# accounts/urls.py
from django.urls import path
from .views import CashVoucherReceiptPrintView, CashVoucherPrintRedirectView

app_name = 'accounts' # Namespace for the accounts app

urlpatterns = [
    # URL for displaying the detailed Cash Voucher Receipt report
    # The <int:pk> captures the CVRId from the URL, mimicking Request.QueryString["CVRId"]
    path('cashvoucherreceipt/print/<int:pk>/', CashVoucherReceiptPrintView.as_view(), name='cashvoucherreceipt_print_details'),
    
    # URL for the "Cancel" button, redirecting to a general cash voucher list view
    # This assumes 'cashvoucher_list' is the name of the URL pattern for your main CVR list.
    path('cashvoucher/print/cancel/', CashVoucherPrintRedirectView.as_view(), name='cashvoucher_list_redirect'),
    
    # Placeholder for the actual cash voucher list view, if it exists in this app.
    # If not, ensure 'accounts:cashvoucher_list' points to the correct app/view.
    # path('cashvoucher/list/', CashVoucherListView.as_view(), name='cashvoucher_list'), 
]

```

#### 4.6 Tests

Comprehensive tests are crucial to ensure the correctness of the migration. This includes unit tests for the `CashVoucherReceiptMaster` model's business logic and integration tests for the `CashVoucherReceiptPrintView`.

```python
# accounts/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    CashVoucherReceiptMaster, Company, AccountHead, BusinessGroup, BudgetCode,
    Employee, Customer, Supplier
)
import datetime

class CashVoucherReceiptMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St.')
        cls.acc_head = AccountHead.objects.create(id=101, symbol='CASH')
        cls.bus_group = BusinessGroup.objects.create(id=201, symbol='FINANCE')
        cls.budget_code_obj = BudgetCode.objects.create(id=301, symbol='PRJ-001')
        cls.employee = Employee.objects.create(id='EMP001', name='John Doe')
        cls.customer = Customer.objects.create(id='CUST001', name='ABC Corp')
        cls.supplier = Supplier.objects.create(id='SUPP001', name='XYZ Ltd')

        # Create a CashVoucherReceiptMaster instance for testing
        cls.cvr = CashVoucherReceiptMaster.objects.create(
            id=1,
            budget_code=cls.budget_code_obj,
            sys_date='01-03-2023', # DD-MM-YYYY format for testing parsing
            sys_time='10:30:00',
            comp_id=cls.company,
            fin_year_id=2023,
            session_id=1,
            cvr_no='CVR/2023/001',
            cash_received_against_code='EMP001',
            cash_received_by_code='CUST001',
            wo_no='WO-ABC-123',
            bg_group=cls.bus_group,
            ac_head=cls.acc_head,
            amount=1500.75,
            others='Payment for services rendered.',
            code_type_ra=1, # Employee
            code_type_rb=2, # Customer
        )

    def test_cvr_creation(self):
        """Ensure CVR object is created correctly."""
        cvr = CashVoucherReceiptMaster.objects.get(id=1)
        self.assertEqual(cvr.cvr_no, 'CVR/2023/001')
        self.assertEqual(cvr.amount, 1500.75)
        self.assertEqual(cvr.comp_id.id, self.company.id)

    def test_formatted_sys_date_property(self):
        """Test the date formatting property."""
        self.assertEqual(self.cvr.formatted_sys_date, '01-03-2023')
        
        # Test with a different date format in DB (MM-DD-YYYY) if applicable
        cvr2 = CashVoucherReceiptMaster.objects.create(
            id=2, budget_code=None, sys_date='03-01-2024', sys_time='11:00:00',
            comp_id=self.company, cvr_no='CVR/2024/002', amount=500,
            cash_received_against_code='EMP001', cash_received_by_code='CUST001',
            code_type_ra=1, code_type_rb=2
        )
        self.assertEqual(cvr2.formatted_sys_date, '03-01-2024')

    def test_cash_received_against_display_property(self):
        """Test getting formatted 'Cash Received Against' name."""
        self.assertEqual(self.cvr.cash_received_against_display, 'John Doe ( EMPLOYEE )')

        # Test for non-existent code
        self.cvr.cash_received_against_code = 'NONEXISTENT'
        self.cvr.code_type_ra = 1
        self.assertEqual(self.cvr.cash_received_against_display, 'Code not found ( EMPLOYEE )')

        # Test for invalid code_type (e.g., 99)
        self.cvr.cash_received_against_code = 'EMP001'
        self.cvr.code_type_ra = 99
        self.assertEqual(self.cvr.cash_received_against_display, 'N/A ( N/A )')


    def test_cash_received_by_display_property(self):
        """Test getting formatted 'Cash Received By' name."""
        self.assertEqual(self.cvr.cash_received_by_display, 'ABC Corp ( CUSTOMER )')

    def test_budget_code_symbol_display_property(self):
        """Test getting Budget Code symbol."""
        self.assertEqual(self.cvr.budget_code_symbol_display, 'PRJ-001')
        # Test when budget_code is None
        self.cvr.budget_code = None
        self.assertEqual(self.cvr.budget_code_symbol_display, 'NA')

    def test_company_address_property(self):
        """Test getting company address."""
        self.assertEqual(self.cvr.company_address, '123 Test St.')
        # Test when comp_id is None
        self.cvr.comp_id = None
        self.assertEqual(self.cvr.company_address, 'N/A')

    def test_get_report_data_method(self):
        """Test the full report data aggregation method."""
        report_data = self.cvr.get_report_data()
        self.assertIn('cvr_no', report_data)
        self.assertEqual(report_data['cvr_no'], 'CVR/2023/001')
        self.assertEqual(report_data['amount'], 1500.75)
        self.assertEqual(report_data['cash_received_against'], 'John Doe ( EMPLOYEE )')
        self.assertEqual(report_data['company_address'], '123 Test St.')
        self.assertEqual(report_data['budget_code'], 'PRJ-001') # Ensure it's still correct after other tests modify the instance

class CashVoucherReceiptPrintViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St.')
        cls.acc_head = AccountHead.objects.create(id=101, symbol='CASH')
        cls.bus_group = BusinessGroup.objects.create(id=201, symbol='FINANCE')
        cls.budget_code_obj = BudgetCode.objects.create(id=301, symbol='PRJ-001')
        cls.employee = Employee.objects.create(id='EMP001', name='John Doe')
        cls.customer = Customer.objects.create(id='CUST001', name='ABC Corp')
        cls.supplier = Supplier.objects.create(id='SUPP001', name='XYZ Ltd')

        # Create a CashVoucherReceiptMaster instance for testing views
        cls.cvr_to_print = CashVoucherReceiptMaster.objects.create(
            id=1,
            budget_code=cls.budget_code_obj,
            sys_date='01-03-2023',
            sys_time='10:30:00',
            comp_id=cls.company,
            fin_year_id=2023,
            session_id=1,
            cvr_no='CVR/2023/VIEWTEST',
            cash_received_against_code='EMP001',
            cash_received_by_code='CUST001',
            wo_no='WO-VIEW-456',
            bg_group=cls.bus_group,
            ac_head=cls.acc_head,
            amount=2500.50,
            others='View test report.',
            code_type_ra=1, # Employee
            code_type_rb=2, # Customer
        )

    def setUp(self):
        self.client = Client()

    def test_print_view_success(self):
        """Test if the print view loads successfully with correct data."""
        url = reverse('accounts:cashvoucherreceipt_print_details', args=[self.cvr_to_print.id])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'accounts/cashvoucherreceiptmaster/print_details.html')
        
        self.assertIn('cvr_report_data', response.context)
        report_data = response.context['cvr_report_data']
        self.assertEqual(report_data['cvr_no'], 'CVR/2023/VIEWTEST')
        self.assertEqual(report_data['amount'], 2500.50)
        self.assertIn('John Doe ( EMPLOYEE )', response.content.decode())
        self.assertIn('123 Test St.', response.content.decode())

    def test_print_view_not_found(self):
        """Test if the view returns 404 for a non-existent CVR ID."""
        url = reverse('accounts:cashvoucherreceipt_print_details', args=[99999]) # Non-existent ID
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_cancel_redirect_view(self):
        """Test if the cancel view redirects correctly."""
        # Note: You need to ensure 'accounts:cashvoucher_list' is defined in your urls.py
        # For testing, we can mock it or define a simple placeholder.
        # Here, we assume it's defined.
        
        # Add a dummy URL for redirection target for testing purposes if 'cashvoucher_list' is not fully implemented
        from django.urls import re_path
        from django.views.generic import TemplateView
        self.old_urlpatterns = self.client.get_resolver().url_patterns
        self.client.get_resolver().url_patterns = [
            re_path(r'^cashvoucher/list/$', TemplateView.as_view(template_name='dummy_list.html'), name='cashvoucher_list'),
        ]

        url = reverse('accounts:cashvoucher_list_redirect')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Redirect status code
        self.assertRedirects(response, reverse('accounts:cashvoucher_list'), fetch_redirect_response=False)
        
        # Restore original url patterns
        self.client.get_resolver().url_patterns = self.old_urlpatterns

```

### Step 5: HTMX and Alpine.js Integration

**Analysis:**
The original ASP.NET page is a static report display. There are no dynamic CRUD forms, client-side filtering, or data tables to be updated dynamically. The only "interaction" is a simple redirect for the "Cancel" button.

**HTMX/Alpine.js Strategy:**
-   **No HTMX/Alpine.js for report content:** The report itself is rendered server-side and presented as standard HTML, designed for printing.
-   **Print Button:** A standard `onclick="window.print()"` JavaScript call is sufficient for the "Print Report" button, as it performs a native browser function.
-   **Cancel Button:** This is a simple link (`<a href="...">`) or a form submission that triggers a full page redirect (handled by `RedirectView`). No HTMX required here.
-   **Overall Application:** While this specific page does not utilize HTMX or Alpine.js heavily for its core function, the overall Django application should still adhere to the HTMX + Alpine.js pattern for other list views, forms, and dynamic components, as stipulated in the guidelines. This particular report page simply stands as a non-interactive display.

---

## Final Notes

This comprehensive plan provides a clear pathway for modernizing the ASP.NET Crystal Report page to a Django-based solution. Key benefits for business stakeholders include:

-   **Reduced Licensing Costs:** Eliminates reliance on proprietary Crystal Reports software.
-   **Enhanced Accessibility:** Provides a web-native report view, accessible from any device with a browser.
-   **Improved Maintainability:** Transitions legacy C# and ASP.NET markup to a modern, Pythonic, and open-source framework, making it easier for new developers to understand and maintain.
-   **Scalability & Performance:** Leverages Django's efficient ORM and Python's flexibility for data processing, leading to potentially better performance and scalability compared to the legacy setup.
-   **Automation-Ready:** The detailed breakdown into distinct files and the emphasis on structured, testable code facilitates future automation efforts for similar migration tasks.
-   **Future-Proofing:** Adopting Django, HTMX, and Tailwind CSS positions the application on a modern, widely supported technology stack, ensuring long-term viability and easier integration with other systems.

The "fat model, thin view" principle ensures that business logic is centralized and testable, leading to a more robust and maintainable codebase. While this specific report page is primarily static, the overall architecture encourages dynamic, responsive user interfaces across the wider application using HTMX and Alpine.js.