## ASP.NET to Django Conversion Script: Asset Register Module

This plan outlines the systematic migration of your ASP.NET Asset Register functionality to a modern Django application, emphasizing automation, a clean architecture, and dynamic frontend interactions using HTMX and Alpine.js. Our goal is to create a robust, maintainable, and scalable solution while adhering to best practices and minimizing manual coding.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   **NEVER** include `base.html` template code in your output - assume it already exists and is extended.
*   Focus **ONLY** on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

### AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following database tables and their implied relationships:

*   **`tblACC_Asset_Register` (Primary Table)**
    *   `Id` (Primary Key, integer)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`, integer)
    *   `AssetId` (Foreign Key to `tblACC_Asset`, integer)
    *   `BGGroupId` (Foreign Key to `BusinessGroup`, integer)
    *   `AssetNumber` (string, likely formatted as '0001')
    *   `SysDate` (date)
    *   `SysTime` (time)
    *   `CompId` (integer, Company ID from session)
    *   `SessionId` (string, Username from session)

*   **`tblFinancial_master`**
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (string, e.g., '2023-2024')

*   **`tblACC_Asset`**
    *   `Id` (Primary Key, integer)
    *   `Abbrivation` (string, Asset abbreviation/name)

*   **`BusinessGroup`**
    *   `Id` (Primary Key, integer)
    *   `Symbol` (string, Business Group symbol/name)

#### Step 2: Identify Backend Functionality

The ASP.NET code performs the following operations:

*   **Read (List View):** Displays a paginated list of assets from `tblACC_Asset_Register`, joining with `tblFinancial_master`, `tblACC_Asset`, and `BusinessGroup` to display human-readable names (`FinYear`, `Asset` abbreviation, `BGGroup` symbol). This is done via `FillData()`.
*   **Create (Add):** Allows adding new asset records.
    *   **Method 1 (Footer "Add"):** Inserts a new record using values from dropdowns (`ddlAsset`, `ddlBGGroup`) and a dynamically generated `AssetNumber` from the footer.
    *   **Method 2 (Empty Data Template "Add1"):** If no records exist, a separate insert form allows adding the first record, with `AssetNumber` defaulting to "0001".
*   **Delete:** Allows deleting existing asset records based on their `Id`.
*   **Search:** Filters the asset list based on selected `FinYear`, `Asset`, or `BGGroup`. This is handled by `FillDataSearch()`.
*   **Dynamic Asset Number Generation:** When a new "Asset" is selected in the insert form, `ddlAsset_SelectedIndexChanged` fetches the last `AssetNumber` for that `AssetId` and increments it (or sets to "0001" if it's the first).
*   **Conditional UI Visibility:** The search dropdowns (`ddlFinYear`, `ddlAsset2`, `ddlBGGroup2`) are shown/hidden based on the selection in `ddlSearch`.
*   **Export to Excel:** Exports the currently filtered search results to an Excel file.

#### Step 3: Infer UI Components

The ASP.NET controls will be replaced by equivalent Django template constructs, enhanced with HTMX and Alpine.js for dynamic behavior.

*   **`GridView2` (Main List):** Replaced by a `<table>` element rendered via HTMX, with DataTables for client-side features. The "footer" for insertion will be a Django form rendered within the table or in a dedicated "add" section.
*   **`GridView3` (Search Results):** Another `<table>` element, also managed by DataTables, loaded via HTMX for search results.
*   **`Panel1`, `Panel2`, `Panel3`:** Replaced by `<div>` elements with conditional rendering based on data availability or Alpine.js `x-show` for visibility.
*   **`asp:DropDownList` (`ddlAsset`, `ddlBGGroup`, `ddlFinYear`, `ddlAsset2`, `ddlBGGroup2`, `ddlSearch`):** Replaced by Django `forms.ModelChoiceField` widgets, styled with Tailwind CSS. HTMX will handle `onchange` events.
*   **`asp:Label` (`lblAssetNumber`, `lblId`, etc.):** Replaced by Django template variables. Dynamic labels will use HTMX.
*   **`asp:Button`, `asp:LinkButton` (`btnInsert`, `btnSearch`, `LinkBtnDel`, `btnExportToExcel`):** Replaced by `<button>` or `<a>` elements with HTMX attributes (`hx-post`, `hx-get`, `hx-delete`, `hx-trigger`, `hx-target`, `hx-swap`).
*   **Client-side `alert` messages:** Replaced by Django messages integrated with HTMX `HX-Trigger`.

---

#### Step 4: Generate Django Code

We'll assume the Django project structure has an app named `asset_management`.

### 4.1 Models (`asset_management/models.py`)

This file will define the Django models that map to your existing database tables. The `AssetRegisterManager` will encapsulate business logic, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Max
from django.utils import timezone

class FinancialMaster(models.Model):
    """
    Maps to tblFinancial_master for financial year data.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"ID: {self.fin_year_id}"

class Asset(models.Model):
    """
    Maps to tblACC_Asset for asset types.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    abbreviation = models.CharField(db_column='Abbrivation', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblACC_Asset'
        verbose_name = 'Asset Type'
        verbose_name_plural = 'Asset Types'

    def __str__(self):
        return self.abbreviation or f"ID: {self.id}"

class BusinessGroup(models.Model):
    """
    Maps to BusinessGroup for business group symbols.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or f"ID: {self.id}"

class AssetRegisterManager(models.Manager):
    """
    Custom manager for AssetRegister model to encapsulate business logic.
    """
    def get_all_asset_data(self, company_id, financial_year_id):
        """
        Retrieves all asset register entries with related data, optimized for display.
        Mimics FillData().
        """
        return self.select_related('financial_year', 'asset_type', 'business_group').filter(
            company_id=company_id,
            financial_year_id__lte=financial_year_id  # Assuming FinYearId <= FyId logic
        ).order_by('-id')

    def filter_assets(self, company_id, financial_year_id, search_type=None, search_value=None):
        """
        Filters asset register entries based on search criteria.
        Mimics FillDataSearch().
        """
        qs = self.select_related('financial_year', 'asset_type', 'business_group').filter(
            company_id=company_id,
            financial_year_id__lte=financial_year_id
        ).order_by('-id')

        if search_type == '1' and search_value: # Fin Year
            qs = qs.filter(financial_year_id=search_value)
        elif search_type == '2' and search_value and search_value != '1': # Asset
            qs = qs.filter(asset_type_id=search_value)
        elif search_type == '3' and search_value and search_value != '1': # BG Group
            qs = qs.filter(business_group_id=search_value)
        
        return qs

    def get_next_asset_number(self, asset_type_id, company_id):
        """
        Generates the next sequential asset number for a given asset type and company.
        Mimics ddlAsset_SelectedIndexChanged logic.
        """
        if not asset_type_id or str(asset_type_id) == '1': # Assuming '1' means "Select"
            return "0000" # Equivalent to original "0000" or "NA"

        last_asset_number = self.filter(
            asset_type_id=asset_type_id,
            company_id=company_id
        ).aggregate(max_asset_num=Max('asset_number'))['max_asset_num']

        if last_asset_number:
            try:
                next_num = int(last_asset_number) + 1
                return f"{next_num:04d}" # Format as '0001'
            except ValueError:
                return "0001" # Fallback if asset_number is not purely numeric
        else:
            return "0001" # First asset of this type

    def add_asset_record(self, asset_type_id, bg_group_id, asset_number, company_id, financial_year_id, session_id):
        """
        Creates a new asset register record. Mimics GridView2_RowCommand (Add/Add1).
        Includes basic validation logic.
        """
        if str(asset_type_id) == '1' or str(bg_group_id) == '1':
            return False, "Invalid data entry. Please select valid Asset and Business Group."

        self.create(
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=company_id,
            financial_year_id=financial_year_id,
            session_id=session_id,
            asset_type_id=asset_type_id,
            business_group_id=bg_group_id,
            asset_number=asset_number
        )
        return True, "Asset added successfully."

    def delete_asset_record(self, asset_id):
        """
        Deletes an asset register record by ID. Mimics GridView2_RowCommand (Del).
        """
        try:
            asset = self.get(id=asset_id)
            asset.delete()
            return True, "Asset deleted successfully."
        except self.model.DoesNotExist:
            return False, "Asset not found."
        except Exception as e:
            return False, f"Error deleting asset: {e}"


class AssetRegister(models.Model):
    """
    Maps to tblACC_Asset_Register for asset register entries.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialMaster, models.DO_NOTHING, db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    asset_type = models.ForeignKey(Asset, models.DO_NOTHING, db_column='AssetId')
    business_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='BGGroupId')
    asset_number = models.CharField(db_column='AssetNumber', max_length=10, blank=True, null=True)

    objects = AssetRegisterManager()

    class Meta:
        managed = False
        db_table = 'tblACC_Asset_Register'
        verbose_name = 'Asset Register Entry'
        verbose_name_plural = 'Asset Register Entries'

    def __str__(self):
        return f"{self.asset_type.abbreviation} - {self.asset_number} ({self.financial_year.fin_year})"

    def get_absolute_url(self):
        return reverse('assetregister_edit', kwargs={'pk': self.pk})

    def get_display_data(self):
        """
        Returns a dictionary of display-friendly data for table rendering,
        avoiding N+1 queries by relying on select_related.
        """
        return {
            'id': self.id,
            'fin_year': self.financial_year.fin_year if self.financial_year else 'N/A',
            'asset': self.asset_type.abbreviation if self.asset_type else 'N/A',
            'bg_group': self.business_group.symbol if self.business_group else 'N/A',
            'asset_number': self.asset_number if self.asset_number else 'N/A',
        }

```

### 4.2 Forms (`asset_management/forms.py`)

This file defines the Django forms used for creating and updating `AssetRegister` entries.

```python
from django import forms
from .models import AssetRegister, Asset, BusinessGroup, FinancialMaster

class AssetRegisterForm(forms.ModelForm):
    """
    Form for creating and updating Asset Register entries.
    Includes validation logic for dropdown selections.
    """
    # Use ModelChoiceField for foreign key relationships
    asset_type = forms.ModelChoiceField(
        queryset=Asset.objects.all().order_by('abbreviation'),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-get': "{% url 'assetregister_get_next_asset_number' 0 %}", 'hx-target': '#id_asset_number_display', 'hx-swap': 'innerHTML', 'hx-trigger': 'change', 'name': 'asset_type_id', 'id': 'ddlAsset'}),
        label="Asset"
    )
    business_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('symbol'),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'name': 'business_group_id', 'id': 'ddlBGGroup'}),
        label="BG Group"
    )
    # Asset number will be dynamically updated
    asset_number = forms.CharField(
        required=False, # It's displayed, not directly input for new records
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'readonly': 'readonly'}),
        label="Asset No."
    )
    # The actual financial year for the record is usually taken from session, not form input
    # However, if it needs to be chosen for *new* records, we'd add it here.
    # Based on ASP.NET, FinYearId is from session, so we won't put it in the form for general use.
    # For EmptyDataTemplate, there's a FinYear label.

    class Meta:
        model = AssetRegister
        fields = ['asset_type', 'business_group', 'asset_number'] # Only fields relevant for user input

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If this is an existing instance (update), set the initial asset_number
        if self.instance.pk:
            self.fields['asset_number'].initial = self.instance.asset_number
            # If it's an update, the asset type and business group fields are read-only
            self.fields['asset_type'].widget.attrs['disabled'] = 'disabled'
            self.fields['business_group'].widget.attrs['disabled'] = 'disabled'
            self.fields['asset_number'].widget.attrs['disabled'] = 'disabled'

    def clean(self):
        cleaned_data = super().clean()
        asset_type = cleaned_data.get('asset_type')
        business_group = cleaned_data.get('business_group')

        # Mimic ASP.NET's "Invalid data entry" check (where 1 is "Select")
        if asset_type and str(asset_type.id) == '1':
            self.add_error('asset_type', 'Please select a valid Asset.')
        if business_group and str(business_group.id) == '1':
            self.add_error('business_group', 'Please select a valid Business Group.')

        return cleaned_data

class AssetRegisterSearchForm(forms.Form):
    """
    Form for search criteria.
    """
    SEARCH_CHOICES = [
        ('0', 'Select'),
        ('1', 'Fin Year'),
        ('2', 'Asset'),
        ('3', 'BG Group'),
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': "{% url 'assetregister_search_options' %}", 'hx-target': '#search-options-container', 'hx-swap': 'innerHTML', 'hx-trigger': 'change', 'id': 'ddlSearch'})
    )
    
    fin_year = forms.ModelChoiceField(
        queryset=FinancialMaster.objects.all().order_by('-fin_year_id'),
        empty_label="Select",
        required=False,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    asset = forms.ModelChoiceField(
        queryset=Asset.objects.all().order_by('abbreviation'),
        empty_label="Select",
        required=False,
        widget=forms.Select(attrs={'class': 'box3'})
    )
    business_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('symbol'),
        empty_label="Select",
        required=False,
        widget=forms.Select(attrs={'class': 'box3'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Hide fields initially, Alpine.js or HTMX partials will manage visibility
        self.fields['fin_year'].widget.attrs['class'] += ' hidden'
        self.fields['asset'].widget.attrs['class'] += ' hidden'
        self.fields['business_group'].widget.attrs['class'] += ' hidden'

```

### 4.3 Views (`asset_management/views.py`)

These views implement the CRUD operations and search functionality using Django's Class-Based Views, keeping them thin by delegating logic to the models and forms. HTMX responses are central to the dynamic interactions.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render, get_object_or_404
from django.template.loader import render_to_string
from django.db.models import Max # For generating asset number
import csv # For CSV export
from io import StringIO

from .models import AssetRegister, Asset, BusinessGroup, FinancialMaster
from .forms import AssetRegisterForm, AssetRegisterSearchForm

# Assuming company_id, financial_year_id, and session_id are retrieved from session/request context
# For demonstration, we'll hardcode them or use simple placeholders.
# In a real app, integrate with your authentication/session system.
def get_user_context(request):
    """Placeholder for retrieving user-specific context."""
    return {
        'company_id': request.session.get('compid', 1), # Default to 1 if not in session
        'financial_year_id': request.session.get('finyear', 1), # Default to 1
        'session_id': request.session.get('username', 'system_user'),
        'current_fin_year': FinancialMaster.objects.get(fin_year_id=request.session.get('finyear', 1)).fin_year if FinancialMaster.objects.filter(fin_year_id=request.session.get('finyear', 1)).exists() else 'N/A'
    }

class AssetRegisterListView(ListView):
    """
    Main view for the Asset Register list page.
    This view primarily renders the container for the HTMX-loaded table.
    """
    model = AssetRegister
    template_name = 'asset_management/assetregister/list.html'
    context_object_name = 'asset_registers'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context.update(get_user_context(self.request))
        context['search_form'] = AssetRegisterSearchForm()
        return context

class AssetRegisterTablePartialView(ListView):
    """
    HTMX-driven view to render the asset register table, including the add row.
    """
    model = AssetRegister
    template_name = 'asset_management/assetregister/_assetregister_table.html'
    context_object_name = 'asset_registers'

    def get_queryset(self):
        user_context = get_user_context(self.request)
        return self.model.objects.get_all_asset_data(
            company_id=user_context['company_id'],
            financial_year_id=user_context['financial_year_id']
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_context = get_user_context(self.request)
        context['add_form'] = AssetRegisterForm()
        context['current_fin_year'] = user_context['current_fin_year']
        # Set initial asset number for the add form
        if not context['asset_registers'].exists():
            context['initial_asset_number'] = '0001' # For empty template case
        else:
            context['initial_asset_number'] = '0000' # Placeholder, will be updated by HTMX/JS
        return context

class AssetRegisterCreateView(CreateView):
    """
    Handles creation of new Asset Register entries via HTMX modal.
    """
    model = AssetRegister
    form_class = AssetRegisterForm
    template_name = 'asset_management/assetregister/_assetregister_form.html'
    # No success_url needed directly as HTMX handles redirect via HX-Trigger

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['initial_asset_number'] = AssetRegister.objects.get_next_asset_number(
            asset_type_id=None, # Initial call, no asset selected yet
            company_id=get_user_context(self.request)['company_id']
        )
        return context

    def post(self, request, *args, **kwargs):
        form = self.get_form()
        if form.is_valid():
            user_context = get_user_context(self.request)
            asset_type_id = form.cleaned_data['asset_type'].id
            business_group_id = form.cleaned_data['business_group'].id
            
            # The asset_number from the form is read-only, we need to generate it.
            # In an HTMX context, we'd have already dynamically updated the field.
            # Here, we regenerate or get the value that would have been displayed.
            asset_number_to_save = form.cleaned_data.get('asset_number') # Use if manually entered or read-only was dynamic
            if not asset_number_to_save or asset_number_to_save == '0000':
                asset_number_to_save = AssetRegister.objects.get_next_asset_number(
                    asset_type_id=asset_type_id,
                    company_id=user_context['company_id']
                )

            success, msg = self.model.objects.add_asset_record(
                asset_type_id=asset_type_id,
                bg_group_id=business_group_id,
                asset_number=asset_number_to_save,
                company_id=user_context['company_id'],
                financial_year_id=user_context['financial_year_id'],
                session_id=user_context['session_id']
            )
            if success:
                messages.success(self.request, msg)
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'refreshAssetRegisterList' # Trigger refresh on success
                    }
                )
            else:
                messages.error(self.request, msg)
                # Re-render form with errors if validation failed at model level
                return render(self.request, self.template_name, {'form': form})
        else:
            # Form validation failed
            messages.error(self.request, "Please correct the errors below.")
            return render(self.request, self.template_name, {'form': form})

class AssetRegisterUpdateView(UpdateView):
    """
    Handles updating existing Asset Register entries via HTMX modal.
    """
    model = AssetRegister
    form_class = AssetRegisterForm
    template_name = 'asset_management/assetregister/_assetregister_form.html'
    # No success_url needed directly as HTMX handles redirect via HX-Trigger

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Asset Register entry updated successfully.')
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshAssetRegisterList'
            }
        )
    
    def form_invalid(self, form):
        messages.error(self.request, "Please correct the errors below.")
        return render(self.request, self.template_name, {'form': form})

class AssetRegisterDeleteView(DeleteView):
    """
    Handles deletion of Asset Register entries via HTMX modal confirmation.
    """
    model = AssetRegister
    template_name = 'asset_management/assetregister/_assetregister_confirm_delete.html'

    def delete(self, request, *args, **kwargs):
        asset_id = self.get_object().id
        success, msg = self.model.objects.delete_asset_record(asset_id)
        if success:
            messages.success(self.request, msg)
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshAssetRegisterList'
                }
            )
        else:
            messages.error(self.request, msg)
            return HttpResponse(status=400, content=msg) # Or render error partial

class AssetRegisterGetNextAssetNumberView(View):
    """
    HTMX endpoint to dynamically get the next asset number based on selected asset type.
    """
    def get(self, request, asset_id, *args, **kwargs):
        user_context = get_user_context(request)
        next_asset_number = AssetRegister.objects.get_next_asset_number(
            asset_type_id=asset_id,
            company_id=user_context['company_id']
        )
        return HttpResponse(next_asset_number)

class AssetRegisterSearchOptionsView(View):
    """
    HTMX endpoint to dynamically render search filter dropdowns based on selected search type.
    Mimics ddlSearch_SelectedIndexChanged.
    """
    def get(self, request, *args, **kwargs):
        form = AssetRegisterSearchForm(request.GET)
        context = {
            'form': form,
            'selected_search_by': request.GET.get('search_by', '0')
        }
        return render(request, 'asset_management/assetregister/_search_options.html', context)

class AssetRegisterSearchResultsPartialView(ListView):
    """
    HTMX-driven view to render the search results table.
    """
    model = AssetRegister
    template_name = 'asset_management/assetregister/_assetregister_search_results.html'
    context_object_name = 'search_results'

    def get_queryset(self):
        user_context = get_user_context(self.request)
        search_type = self.request.GET.get('search_by')
        search_value = None
        if search_type == '1':
            search_value = self.request.GET.get('fin_year')
        elif search_type == '2':
            search_value = self.request.GET.get('asset')
        elif search_type == '3':
            search_value = self.request.GET.get('business_group')

        return self.model.objects.filter_assets(
            company_id=user_context['company_id'],
            financial_year_id=user_context['financial_year_id'],
            search_type=search_type,
            search_value=search_value
        )
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure form is passed to display selected values
        context['search_form'] = AssetRegisterSearchForm(self.request.GET)
        return context


class AssetRegisterExportExcelView(View):
    """
    View to export filtered asset data to a CSV file (simple Excel substitute).
    """
    def get(self, request, *args, **kwargs):
        user_context = get_user_context(request)
        search_type = request.GET.get('search_by')
        search_value = None
        if search_type == '1':
            search_value = request.GET.get('fin_year')
        elif search_type == '2':
            search_value = request.GET.get('asset')
        elif search_type == '3':
            search_value = request.GET.get('business_group')

        queryset = AssetRegister.objects.filter_assets(
            company_id=user_context['company_id'],
            financial_year_id=user_context['financial_year_id'],
            search_type=search_type,
            search_value=search_value
        )

        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="asset_register_export_{timezone.now().strftime("%Y%m%d_%H%M%S")}.csv"'

        writer = csv.writer(response)
        writer.writerow(['SN', 'Fin Year', 'Asset', 'BG Group', 'Asset No.']) # Headers

        for i, obj in enumerate(queryset):
            writer.writerow([
                i + 1,
                obj.financial_year.fin_year if obj.financial_year else 'N/A',
                obj.asset_type.abbreviation if obj.asset_type else 'N/A',
                obj.business_group.symbol if obj.business_group else 'N/A',
                obj.asset_number if obj.asset_number else 'N/A',
            ])
        
        if not queryset.exists():
             messages.warning(request, "No records to export.")
             # For HTMX, you might return 204 or a specific partial with message
             # For direct download, this message won't be seen by user on page
             return HttpResponse("No data to export.", status=200) # Or specific HX-Trigger

        return response

```

### 4.4 Templates (`asset_management/templates/asset_management/assetregister/`)

This directory will contain the HTML templates, using HTMX for dynamic content loading and Alpine.js for UI state.

#### `list.html`

This is the main page that extends `core/base.html` and sets up the HTMX containers for the table and modals.

```html
{% extends 'core/base.html' %}

{% block title %}Asset Register{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-extrabold text-gray-900 mb-6">Asset Register</h2>

    <!-- Search and Export Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="flex flex-wrap items-end gap-4 mb-4">
            <div class="flex-grow">
                <label for="ddlSearch" class="block text-sm font-medium text-gray-700 mb-1">Search By:</label>
                {{ search_form.search_by }}
            </div>
            
            <div id="search-options-container" class="flex flex-grow-0 gap-4">
                <!-- Dynamic search dropdowns will be loaded here via HTMX from _search_options.html -->
                <!-- Initial state: all hidden via form widget attrs -->
                {{ search_form.fin_year }}
                {{ search_form.asset }}
                {{ search_form.business_group }}
            </div>

            <button 
                id="btnSearch"
                class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'assetregister_search_results' %}"
                hx-target="#searchResultsContainer"
                hx-trigger="click"
                hx-swap="innerHTML"
                hx-include="#ddlSearch, #id_fin_year, #id_asset, #id_business_group"
                _="on click toggle .hidden on #no-data-found-panel">
                Search
            </button>
            
            <button 
                id="btnExportToExcel"
                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'assetregister_export_excel' %}"
                hx-include="#ddlSearch, #id_fin_year, #id_asset, #id_business_group"
                hx-indicator="#export-spinner"
                hx-target="body" hx-swap="none"> {# hx-target="body" hx-swap="none" for file download #}
                Export To Excel
                <span id="export-spinner" class="htmx-indicator ml-2 inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></span>
            </button>
        </div>
        
        <div id="searchResultsContainer" class="mt-4">
            <!-- Search results GridView3 equivalent will be loaded here via HTMX -->
            <div id="no-data-found-panel" class="box3 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative text-center hidden">
                <p class="font-bold">No data found!</p>
            </div>
        </div>
    </div>

    <!-- Main Asset Register List and Add Section -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-2xl font-semibold text-gray-800">Asset Register List</h3>
            <button 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'assetregister_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Asset
            </button>
        </div>
        
        <div id="assetRegisterTable-container"
             hx-trigger="load, refreshAssetRegisterList from:body"
             hx-get="{% url 'assetregister_table' %}"
             hx-swap="innerHTML">
            <!-- DataTables table for Asset Register will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-lg text-gray-600">Loading Asset Register...</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) and delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto"
             _="on htmx:afterSwap remove .is-active from #modal end">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed here, x-show on modals is enough
    });

    // Global HTMX event listener for messages
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target && event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // This is for cases where hx-swap="none" is used on a form submission.
            // We want to close the modal after successful submission and trigger a list refresh.
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
            }
        }
    });

    // Initialize DataTables after HTMX loads content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target && (event.detail.target.id === 'assetRegisterTable-container' || event.detail.target.id === 'searchResultsContainer')) {
            // Check if the table element exists within the swapped content
            const table = event.detail.target.querySelector('table');
            if (table && !$.fn.DataTable.isDataTable(table)) { // Ensure DataTables is not already initialized
                $(table).DataTable({
                    "pageLength": 10, // Default items per page
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true // Allow re-initialization if content is swapped again
                });
            }
        }
    });
</script>
{% endblock %}
```

#### `_assetregister_table.html`

This partial template displays the main asset list using DataTables and includes the "Add" row.

```html
<table id="assetRegisterTable" class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset No.</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in asset_registers %}
        <tr>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.financial_year.fin_year }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.asset_type.abbreviation }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.business_group.symbol }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.asset_number }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                <button 
                    class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-md hover:bg-gray-100 transition duration-150 ease-in-out"
                    hx-get="{% url 'assetregister_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-gray-100 transition duration-150 ease-in-out"
                    hx-get="{% url 'assetregister_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <!-- EmptyDataTemplate logic for inserting first record -->
        <tr class="bg-blue-50">
            <td colspan="6" class="py-4 px-6 text-center text-sm font-semibold text-gray-700">
                <p class="mb-4">No asset records found. Add the first one:</p>
                <form hx-post="{% url 'assetregister_add' %}" hx-swap="none">
                    {% csrf_token %}
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-center">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Fin Year</label>
                            <span class="text-lg font-bold text-gray-800">{{ current_fin_year }}</span>
                        </div>
                        <div>
                            <label for="{{ add_form.asset_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Asset</label>
                            {{ add_form.asset_type }}
                        </div>
                        <div>
                            <label for="{{ add_form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">BG Group</label>
                            {{ add_form.business_group }}
                        </div>
                        <div>
                            <label for="id_asset_number_display" class="block text-sm font-medium text-gray-700">Asset No.</label>
                            <span id="id_asset_number_display" class="text-lg font-bold text-gray-800">{{ initial_asset_number }}</span>
                            <input type="hidden" name="asset_number" value="{{ initial_asset_number }}"> {# Hidden input for form submission #}
                        </div>
                    </div>
                    <div class="mt-6">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                            Insert First Asset
                        </button>
                    </div>
                    {% if add_form.errors %}
                        <div class="text-red-500 text-sm mt-4">
                            {% for field in add_form %}
                                {% for error in field.errors %}
                                    <p>{{ error }}</p>
                                {% endfor %}
                            {% endfor %}
                            {% for error in add_form.non_field_errors %}
                                <p>{{ error }}</p>
                            {% endfor %}
                        </div>
                    {% endif %}
                </form>
            </td>
        </tr>
        {% endfor %}
    </tbody>
    {% if asset_registers %}
    <tfoot>
        <tr class="bg-gray-50">
            <td colspan="4" class="py-3 px-6 text-right text-xs font-bold text-gray-800 uppercase tracking-wider">
                Insert New:
            </td>
            <td class="py-3 px-6 text-center">
                <label for="id_asset_number_display_footer" class="block text-sm font-medium text-gray-700 sr-only">Asset No.</label>
                <span id="id_asset_number_display_footer" class="text-lg font-bold text-gray-800">{{ initial_asset_number }}</span>
            </td>
            <td class="py-3 px-6 text-center">
                <form hx-post="{% url 'assetregister_add' %}" hx-swap="none">
                    {% csrf_token %}
                    <input type="hidden" name="asset_type" id="id_asset_type_footer" value="{{ add_form.asset_type.value|default:1 }}">
                    <input type="hidden" name="business_group" id="id_business_group_footer" value="{{ add_form.business_group.value|default:1 }}">
                    <input type="hidden" name="asset_number" value="{{ initial_asset_number }}"> {# Hidden input for form submission #}

                    {# Dropdowns for the footer insert row, using same hx-trigger logic #}
                    <div class="flex flex-col space-y-2 mb-2">
                        <label for="{{ add_form.asset_type.id_for_label }}" class="sr-only">Asset</label>
                        {{ add_form.asset_type.as_widget }}
                        <label for="{{ add_form.business_group.id_for_label }}" class="sr-only">BG Group</label>
                        {{ add_form.business_group.as_widget }}
                    </div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded-md shadow-sm">
                        Insert
                    </button>
                </form>
            </td>
        </tr>
    </tfoot>
    {% endif %}
</table>
```

#### `_assetregister_form.html`

This partial template is loaded into the modal for Add/Edit operations.

```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-800 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Asset Register Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% if form.instance.pk %}
            <div class="mb-4">
                <label for="{{ form.asset_type.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.asset_type.label }}</label>
                <p class="mt-1 text-base text-gray-900 font-semibold">{{ form.instance.asset_type.abbreviation }}</p>
                <input type="hidden" name="{{ form.asset_type.name }}" value="{{ form.instance.asset_type.id }}">
            </div>
            <div class="mb-4">
                <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.business_group.label }}</label>
                <p class="mt-1 text-base text-gray-900 font-semibold">{{ form.instance.business_group.symbol }}</p>
                <input type="hidden" name="{{ form.business_group.name }}" value="{{ form.instance.business_group.id }}">
            </div>
            <div class="mb-4">
                <label for="{{ form.asset_number.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.asset_number.label }}</label>
                <p class="mt-1 text-base text-gray-900 font-semibold">{{ form.instance.asset_number }}</p>
                <input type="hidden" name="{{ form.asset_number.name }}" value="{{ form.instance.asset_number }}">
            </div>
        {% else %}
            <div class="mb-4">
                <label for="{{ form.asset_type.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.asset_type.label }}</label>
                {{ form.asset_type }}
                {% if form.asset_type.errors %}<p class="text-red-500 text-xs mt-1">{{ form.asset_type.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.business_group.label }}</label>
                {{ form.business_group }}
                {% if form.business_group.errors %}<p class="text-red-500 text-xs mt-1">{{ form.business_group.errors }}</p>{% endif %}
            </div>
            <div class="mb-4">
                <label for="id_asset_number_display_modal" class="block text-sm font-medium text-gray-700">{{ form.asset_number.label }}</label>
                <span id="id_asset_number_display_modal" class="text-lg font-bold text-gray-800">{{ initial_asset_number }}</span>
                <input type="hidden" name="{{ form.asset_number.name }}" value="{{ initial_asset_number }}"> {# Hidden input for form submission #}
                {% if form.asset_number.errors %}<p class="text-red-500 text-xs mt-1">{{ form.asset_number.errors }}</p>{% endif %}
            </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            {% if form.instance.pk %}
            <!-- For Edit, we might not allow editing key fields directly via form if they drive asset number.
                 Based on original, edit functionality is not clearly shown, only delete and add.
                 If edit was intended to change related foreign keys, the asset number recalculation
                 would be complex and probably handled by a more involved form logic.
                 For this implementation, we assume editing just confirms existing details. -->
                <button 
                    type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Confirm Edit
                </button>
            {% else %}
                <button 
                    type="submit" 
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                    Add Asset
                </button>
            {% endif %}
        </div>
    </form>
</div>
```

#### `_assetregister_confirm_delete.html`

This partial template is for the delete confirmation modal.

```html
<div class="p-6 text-center">
    <h3 class="text-2xl font-bold text-red-700 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 text-lg mb-6">Are you sure you want to delete the Asset Register entry for:</p>
    <p class="text-xl font-semibold text-gray-900 mb-8">
        "{{ object.asset_type.abbreviation }} - {{ object.asset_number }}" (Fin. Year: {{ object.financial_year.fin_year }})?
    </p>
    <form hx-delete="{% url 'assetregister_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-6">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-md shadow-md transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### `_search_options.html`

This partial dynamically renders the correct search dropdown based on `ddlSearch` selection.

```html
{% comment %}
    This template is swapped into #search-options-container.
    It contains the search dropdowns (fin_year, asset, business_group)
    with their visibility managed by Alpine.js x-show.
{% endcomment %}

<div x-data="{ 
    searchBy: '{{ selected_search_by }}',
    init() {
        this.$watch('searchBy', value => {
            // Update the actual form's selected_search_by for submission
            document.getElementById('ddlSearch').value = value;
        });
        // Set initial value for Alpine.js from HTMX context
        this.searchBy = document.getElementById('ddlSearch').value;
    }
}" class="flex flex-grow-0 gap-4">

    <div x-show="searchBy === '1'" class="flex-grow">
        <label for="{{ form.fin_year.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Financial Year:</label>
        {{ form.fin_year }}
    </div>

    <div x-show="searchBy === '2'" class="flex-grow">
        <label for="{{ form.asset.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Asset:</label>
        {{ form.asset }}
    </div>

    <div x-show="searchBy === '3'" class="flex-grow">
        <label for="{{ form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Business Group:</label>
        {{ form.business_group }}
    </div>

</div>
```

#### `_assetregister_search_results.html`

This partial displays the results of the search query in a DataTable.

```html
{% comment %}
    This template displays the search results in a DataTable.
{% endcomment %}

{% if search_results %}
<table id="searchResultsTable" class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
            <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Asset No.</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in search_results %}
        <tr>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.financial_year.fin_year }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.asset_type.abbreviation }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.business_group.symbol }}</td>
            <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.asset_number }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% else %}
<div class="box3 bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded relative text-center">
    <p class="font-bold">No data to display !</p>
</div>
{% endif %}

<script>
    // This script runs when the partial is loaded via HTMX
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#searchResultsTable')) {
            $('#searchResultsTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true
            });
        }
    });
</script>
```

### 4.5 URLs (`asset_management/urls.py`)

This file defines the URL patterns for accessing the Asset Register views.

```python
from django.urls import path
from .views import (
    AssetRegisterListView, 
    AssetRegisterTablePartialView,
    AssetRegisterCreateView, 
    AssetRegisterUpdateView, 
    AssetRegisterDeleteView,
    AssetRegisterGetNextAssetNumberView,
    AssetRegisterSearchOptionsView,
    AssetRegisterSearchResultsPartialView,
    AssetRegisterExportExcelView
)

urlpatterns = [
    path('assetregister/', AssetRegisterListView.as_view(), name='assetregister_list'),
    path('assetregister/table/', AssetRegisterTablePartialView.as_view(), name='assetregister_table'),
    path('assetregister/add/', AssetRegisterCreateView.as_view(), name='assetregister_add'),
    path('assetregister/edit/<int:pk>/', AssetRegisterUpdateView.as_view(), name='assetregister_edit'),
    path('assetregister/delete/<int:pk>/', AssetRegisterDeleteView.as_view(), name='assetregister_delete'),
    path('assetregister/get_next_asset_number/<int:asset_id>/', AssetRegisterGetNextAssetNumberView.as_view(), name='assetregister_get_next_asset_number'),
    path('assetregister/search_options/', AssetRegisterSearchOptionsView.as_view(), name='assetregister_search_options'),
    path('assetregister/search_results/', AssetRegisterSearchResultsPartialView.as_view(), name='assetregister_search_results'),
    path('assetregister/export_excel/', AssetRegisterExportExcelView.as_view(), name='assetregister_export_excel'),
]
```

### 4.6 Tests (`asset_management/tests.py`)

Comprehensive tests for models and views ensure the migrated functionality is correct and robust.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import AssetRegister, FinancialMaster, Asset, BusinessGroup

# --- Model Tests ---

class FinancialMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')

    def test_fin_year_content(self):
        fin_year = FinancialMaster.objects.get(fin_year_id=2023)
        self.assertEqual(fin_year.fin_year, '2023-2024')
        self.assertEqual(str(fin_year), '2023-2024')

class AssetModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Asset.objects.create(id=1, abbreviation='Laptop')

    def test_asset_content(self):
        asset = Asset.objects.get(id=1)
        self.assertEqual(asset.abbreviation, 'Laptop')
        self.assertEqual(str(asset), 'Laptop')

class BusinessGroupModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        BusinessGroup.objects.create(id=1, symbol='ITD')

    def test_bg_group_content(self):
        bg_group = BusinessGroup.objects.get(id=1)
        self.assertEqual(bg_group.symbol, 'ITD')
        self.assertEqual(str(bg_group), 'ITD')

class AssetRegisterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year_2022 = FinancialMaster.objects.create(fin_year_id=2022, fin_year='2022-2023')
        cls.asset_laptop = Asset.objects.create(id=1, abbreviation='Laptop')
        cls.asset_monitor = Asset.objects.create(id=2, abbreviation='Monitor')
        cls.bg_itd = BusinessGroup.objects.create(id=1, symbol='ITD')
        cls.bg_hr = BusinessGroup.objects.create(id=2, symbol='HRD')

        # Create initial asset register entries
        AssetRegister.objects.create(
            id=1,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            financial_year=cls.fin_year_2023,
            session_id='testuser',
            asset_type=cls.asset_laptop,
            business_group=cls.bg_itd,
            asset_number='0001'
        )
        AssetRegister.objects.create(
            id=2,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            financial_year=cls.fin_year_2023,
            session_id='testuser',
            asset_type=cls.asset_laptop,
            business_group=cls.bg_itd,
            asset_number='0002'
        )
        AssetRegister.objects.create(
            id=3,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=2, # Different company
            financial_year=cls.fin_year_2023,
            session_id='testuser',
            asset_type=cls.asset_monitor,
            business_group=cls.bg_hr,
            asset_number='0001'
        )
        AssetRegister.objects.create(
            id=4,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            financial_year=cls.fin_year_2022, # Older financial year
            session_id='testuser',
            asset_type=cls.asset_monitor,
            business_group=cls.bg_itd,
            asset_number='0001'
        )

    def setUp(self):
        self.company_id = 1
        self.financial_year_id = 2023
        self.session_id = 'testuser'
        self.client = Client()
        # Mock session attributes for tests
        self.client.session['compid'] = self.company_id
        self.client.session['finyear'] = self.financial_year_id
        self.client.session['username'] = self.session_id

    def test_asset_register_creation(self):
        asset_reg = AssetRegister.objects.get(id=1)
        self.assertEqual(asset_reg.asset_type.abbreviation, 'Laptop')
        self.assertEqual(asset_reg.asset_number, '0001')
        self.assertEqual(asset_reg.financial_year.fin_year, '2023-2024')

    def test_get_all_asset_data(self):
        assets = AssetRegister.objects.get_all_asset_data(self.company_id, self.financial_year_id)
        # Should get assets for comp_id=1 and fin_year_id <= 2023
        self.assertEqual(assets.count(), 3) # IDs 1, 2, 4
        self.assertIn(AssetRegister.objects.get(id=1), assets)
        self.assertIn(AssetRegister.objects.get(id=2), assets)
        self.assertIn(AssetRegister.objects.get(id=4), assets)
        self.assertNotIn(AssetRegister.objects.get(id=3), assets) # Different company

    def test_get_next_asset_number_existing(self):
        # Laptop (id=1) in company 1 has '0001', '0002'
        next_num = AssetRegister.objects.get_next_asset_number(self.asset_laptop.id, self.company_id)
        self.assertEqual(next_num, '0003')

    def test_get_next_asset_number_new_asset_type(self):
        # Create a new asset type not used yet
        new_asset = Asset.objects.create(id=99, abbreviation='Keyboard')
        next_num = AssetRegister.objects.get_next_asset_number(new_asset.id, self.company_id)
        self.assertEqual(next_num, '0001')

    def test_get_next_asset_number_no_asset_type_selected(self):
        next_num = AssetRegister.objects.get_next_asset_number(None, self.company_id)
        self.assertEqual(next_num, '0000') # Or 'NA' based on original

    def test_add_asset_record_success(self):
        initial_count = AssetRegister.objects.count()
        success, msg = AssetRegister.objects.add_asset_record(
            asset_type_id=self.asset_monitor.id,
            bg_group_id=self.bg_itd.id,
            asset_number='0003',
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            session_id=self.session_id
        )
        self.assertTrue(success)
        self.assertEqual(msg, 'Asset added successfully.')
        self.assertEqual(AssetRegister.objects.count(), initial_count + 1)
        self.assertTrue(AssetRegister.objects.filter(asset_type=self.asset_monitor, asset_number='0003').exists())

    def test_add_asset_record_invalid_input(self):
        initial_count = AssetRegister.objects.count()
        success, msg = AssetRegister.objects.add_asset_record(
            asset_type_id=1, # Assuming '1' means "Select"
            bg_group_id=self.bg_itd.id,
            asset_number='0003',
            company_id=self.company_id,
            financial_year_id=self.financial_year_id,
            session_id=self.session_id
        )
        self.assertFalse(success)
        self.assertEqual(msg, 'Invalid data entry. Please select valid Asset and Business Group.')
        self.assertEqual(AssetRegister.objects.count(), initial_count)

    def test_delete_asset_record_success(self):
        asset_to_delete = AssetRegister.objects.get(id=1)
        success, msg = AssetRegister.objects.delete_asset_record(asset_to_delete.id)
        self.assertTrue(success)
        self.assertEqual(msg, 'Asset deleted successfully.')
        self.assertFalse(AssetRegister.objects.filter(id=1).exists())

    def test_delete_asset_record_not_found(self):
        success, msg = AssetRegister.objects.delete_asset_record(999) # Non-existent ID
        self.assertFalse(success)
        self.assertEqual(msg, 'Asset not found.')

    def test_filter_assets_no_filter(self):
        assets = AssetRegister.objects.filter_assets(self.company_id, self.financial_year_id)
        self.assertEqual(assets.count(), 3) # IDs 1, 2, 4

    def test_filter_assets_by_fin_year(self):
        assets = AssetRegister.objects.filter_assets(self.company_id, self.financial_year_id, search_type='1', search_value=self.fin_year_2022.fin_year_id)
        self.assertEqual(assets.count(), 1)
        self.assertEqual(assets.first().id, 4)

    def test_filter_assets_by_asset(self):
        assets = AssetRegister.objects.filter_assets(self.company_id, self.financial_year_id, search_type='2', search_value=self.asset_laptop.id)
        self.assertEqual(assets.count(), 2)
        self.assertIn(AssetRegister.objects.get(id=1), assets)
        self.assertIn(AssetRegister.objects.get(id=2), assets)

    def test_filter_assets_by_bg_group(self):
        assets = AssetRegister.objects.filter_assets(self.company_id, self.financial_year_id, search_type='3', search_value=self.bg_hr.id)
        self.assertEqual(assets.count(), 0) # No HR assets in company 1
        # Test with company 2 where HR asset exists
        assets_comp2 = AssetRegister.objects.filter_assets(2, self.financial_year_id, search_type='3', search_value=self.bg_hr.id)
        self.assertEqual(assets_comp2.count(), 1)
        self.assertEqual(assets_comp2.first().id, 3)


# --- View Tests ---

class AssetRegisterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fin_year_2023 = FinancialMaster.objects.create(fin_year_id=2023, fin_year='2023-2024')
        cls.fin_year_2022 = FinancialMaster.objects.create(fin_year_id=2022, fin_year='2022-2023')
        cls.asset_laptop = Asset.objects.create(id=1, abbreviation='Laptop')
        cls.asset_monitor = Asset.objects.create(id=2, abbreviation='Monitor')
        cls.bg_itd = BusinessGroup.objects.create(id=1, symbol='ITD')
        cls.bg_hr = BusinessGroup.objects.create(id=2, symbol='HRD')

        AssetRegister.objects.create(
            id=1,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            financial_year=cls.fin_year_2023,
            session_id='testuser',
            asset_type=cls.asset_laptop,
            business_group=cls.bg_itd,
            asset_number='0001'
        )
        AssetRegister.objects.create(
            id=2,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            financial_year=cls.fin_year_2023,
            session_id='testuser',
            asset_type=cls.asset_laptop,
            business_group=cls.bg_itd,
            asset_number='0002'
        )

    def setUp(self):
        self.client = Client()
        # Set mock session for all view tests
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023
        self.client.session['username'] = 'testuser'

    def test_list_view(self):
        response = self.client.get(reverse('assetregister_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/list.html')
        # Check if basic context is present
        self.assertIn('asset_registers', response.context) # This is from ListView itself, might be empty here.
        self.assertIn('search_form', response.context)

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_table.html')
        self.assertIn('asset_registers', response.context)
        self.assertIn('add_form', response.context)
        self.assertEqual(len(response.context['asset_registers']), 2) # IDs 1, 2

    def test_table_partial_view_htmx_empty_data(self):
        AssetRegister.objects.all().delete() # Clear existing data
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_table.html')
        self.assertIn('asset_registers', response.context)
        self.assertFalse(response.context['asset_registers'].exists())
        self.assertEqual(response.context['initial_asset_number'], '0001')


    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['initial_asset_number'], '0000') # Default when no asset selected

    def test_create_view_post_htmx_success(self):
        initial_count = AssetRegister.objects.count()
        data = {
            'asset_type': self.asset_monitor.id,
            'business_group': self.bg_hr.id,
            'asset_number': '0001' # This would be dynamically set by HTMX/JS
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('assetregister_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshAssetRegisterList')
        self.assertEqual(AssetRegister.objects.count(), initial_count + 1)
        self.assertTrue(AssetRegister.objects.filter(asset_type=self.asset_monitor, asset_number='0001').exists())

    def test_create_view_post_htmx_invalid(self):
        initial_count = AssetRegister.objects.count()
        data = {
            'asset_type': 1, # "Select" value
            'business_group': self.bg_hr.id,
            'asset_number': '0001'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('assetregister_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertEqual(AssetRegister.objects.count(), initial_count)

    def test_update_view_get_htmx(self):
        asset_reg = AssetRegister.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_edit', args=[asset_reg.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.id, asset_reg.id)

    def test_update_view_post_htmx_success(self):
        asset_reg = AssetRegister.objects.get(id=1)
        # Note: In our current form setup for "Edit", these fields are read-only and passed as hidden.
        # So we pass the current values back. True updates would need different form logic.
        data = {
            'asset_type': asset_reg.asset_type.id,
            'business_group': asset_reg.business_group.id,
            'asset_number': '0001' # No actual change to asset_number in this test
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('assetregister_edit', args=[asset_reg.id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshAssetRegisterList')
        # No actual change, just confirming update flow.
        updated_asset_reg = AssetRegister.objects.get(id=1)
        self.assertEqual(updated_asset_reg.asset_number, '0001')


    def test_delete_view_get_htmx(self):
        asset_reg = AssetRegister.objects.get(id=1)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_delete', args=[asset_reg.id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'].id, asset_reg.id)

    def test_delete_view_delete_htmx_success(self):
        asset_to_delete = AssetRegister.objects.get(id=1)
        initial_count = AssetRegister.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('assetregister_delete', args=[asset_to_delete.id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshAssetRegisterList')
        self.assertEqual(AssetRegister.objects.count(), initial_count - 1)
        self.assertFalse(AssetRegister.objects.filter(id=1).exists())

    def test_get_next_asset_number_view_htmx(self):
        asset_id = self.asset_laptop.id # Has '0001', '0002'
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_get_next_asset_number', args=[asset_id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode('utf-8'), '0003')

    def test_search_options_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_search_options'), {'search_by': '1'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_search_options.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['selected_search_by'], '1')
        self.assertContains(response, 'x-show="searchBy === \'1\'"') # Check Alpine.js attribute

    def test_search_results_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_search_results'), {
            'search_by': '2', # Asset
            'asset': self.asset_laptop.id # Laptop
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_search_results.html')
        self.assertIn('search_results', response.context)
        self.assertEqual(len(response.context['search_results']), 2) # Laptop 0001, 0002

    def test_search_results_partial_view_htmx_no_data(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('assetregister_search_results'), {
            'search_by': '2',
            'asset': 999 # Non-existent asset
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'asset_management/assetregister/_assetregister_search_results.html')
        self.assertIn('search_results', response.context)
        self.assertFalse(response.context['search_results'].exists())
        self.assertContains(response, 'No data to display !')

    def test_export_excel_view(self):
        # Ensure there's data to export
        AssetRegister.objects.create(
            id=3,
            sys_date=timezone.now().date(),
            sys_time=timezone.now().time(),
            company_id=1,
            financial_year=self.fin_year_2023,
            session_id='testuser',
            asset_type=self.asset_monitor,
            business_group=self.bg_itd,
            asset_number='0003'
        )
        response = self.client.get(reverse('assetregister_export_excel'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertIn('attachment; filename="asset_register_export_', response['Content-Disposition'])
        
        content = response.content.decode('utf-8')
        lines = content.strip().split('\r\n')
        self.assertEqual(len(lines), 4) # Header + 3 data rows
        self.assertIn('SN,Fin Year,Asset,BG Group,Asset No.', lines[0])
        self.assertIn('1,2023-2024,Laptop,ITD,0001', lines[1])
        self.assertIn('2,2023-2024,Laptop,ITD,0002', lines[2])
        self.assertIn('3,2023-2024,Monitor,ITD,0003', lines[3])

    def test_export_excel_view_no_data(self):
        AssetRegister.objects.all().delete() # Clear existing data
        response = self.client.get(reverse('assetregister_export_excel'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode('utf-8'), 'No data to export.')

```

### Step 5: HTMX and Alpine.js Integration

The provided Django code heavily relies on HTMX for dynamic content loading, form submissions, and UI updates without full page reloads. Alpine.js is used minimally, primarily for toggling the visibility of search dropdowns.

*   **HTMX for CRUD:**
    *   The `list.html` loads the main table (`_assetregister_table.html`) using `hx-get` on page load and on a custom `refreshAssetRegisterList` trigger.
    *   Add/Edit/Delete buttons trigger `hx-get` requests to load forms/confirmations into a modal via `hx-target="#modalContent"`.
    *   Form submissions (`hx-post`, `hx-delete`) use `hx-swap="none"` and rely on `HX-Trigger` headers from the server to close the modal (`_="on htmx:afterSwap remove .is-active from #modal end"`) and refresh the main list.
*   **DataTables:** The `$(document).ready(function() { $('#tableName').DataTable(); });` initialization is placed within the partial templates (`_assetregister_table.html`, `_assetregister_search_results.html`). This ensures DataTables is re-initialized correctly whenever the content is swapped in by HTMX. The `destroy: true` option is crucial for re-initialization.
*   **Dynamic Asset Number:** The `ddlAsset` (Asset dropdown) in `_assetregister_table.html`'s add section has `hx-get="{% url 'assetregister_get_next_asset_number' 0 %}"`, `hx-target='#id_asset_number_display'`, and `hx-trigger="change"`. The `0` in the URL placeholder will be dynamically replaced by `htmx.ext.include-vals` or a similar approach, or the URL could be `assetregister_get_next_asset_number/` and `hx-include` could pass the selected value. (Revised the form widget attrs to use the `hx-get` directly, Alpine.js could also help pass the selected value but a simple HTMX request is cleaner here).
*   **Search Filters:** The `ddlSearch` dropdown triggers an `hx-get` to `assetregister_search_options`, which swaps in the correct dynamic dropdowns (`_search_options.html`). The `search_options` partial then uses Alpine.js `x-show` to control the visibility of the financial year, asset, or business group dropdown based on the `searchBy` value. The "Search" button then collects all relevant form inputs using `hx-include` to send to `assetregister_search_results`.
*   **Alerts/Messages:** Django's `messages` framework is used. The `HX-Trigger` header can be configured to send a message payload to the client, which can be picked up by a global HTMX listener to display notifications (e.g., toast messages, not implemented explicitly here but is the standard approach).

### Final Notes

This comprehensive plan transforms your ASP.NET Asset Register into a modern Django application. Key advantages include:

*   **Clear Separation of Concerns:** Business logic resides in models, UI logic in templates/HTMX/Alpine.js, and thin views orchestrate them.
*   **Enhanced User Experience:** HTMX and Alpine.js provide dynamic interactions without full page reloads, making the application feel snappier.
*   **Improved Maintainability:** Django's structured approach, coupled with strong adherence to DRY principles and comprehensive testing, makes the codebase easier to understand, debug, and extend.
*   **Scalability:** Django's robust ORM and architecture are well-suited for growing applications.
*   **Automated Conversion Potential:** The systematic mapping and template-driven generation approach are ideal for AI-assisted migration tools, reducing manual effort and human error significantly.
*   **Modern Frontend:** Leveraging HTMX and Alpine.js keeps frontend development lean, avoiding complex JavaScript frameworks while still delivering a rich user experience.