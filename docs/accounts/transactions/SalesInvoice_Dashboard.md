## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The provided ASP.NET `SalesInvoice_Dashboard.aspx` and its code-behind are currently empty shells, primarily setting up content placeholders for a master page. This indicates a foundational structure for a dashboard page related to Sales Invoices, but no active functionality (like displaying data, forms, or business logic) is present in the snippet itself.

To provide a meaningful modernization plan, we will proceed by **simulating** the most common and logical functionality for a "Sales Invoice Dashboard": displaying a list of sales invoices with capabilities to create, edit, and delete them. This allows us to demonstrate the full power of Django, HTMX, and Alpine.js for a typical business application module, even when the source code is a placeholder.

This approach focuses on building a robust, maintainable, and highly interactive Sales Invoice management module in Django, emphasizing automation-friendly design patterns and a modern user experience.

---

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not contain explicit database interactions or schema definitions, we will infer a typical database structure for a "Sales Invoice Dashboard." We assume there's a table named `tblSalesInvoice` that stores information about sales invoices.

-   **Table Name:** `tblSalesInvoice`
-   **Inferred Columns:**
    *   `InvoiceID` (Primary Key, e.g., `INT`)
    *   `InvoiceNumber` (Unique identifier, e.g., `NVARCHAR(50)`)
    *   `CustomerID` (Foreign Key to a Customers table, e.g., `INT`)
    *   `InvoiceDate` (Date of invoice, e.g., `DATETIME`)
    *   `TotalAmount` (Total amount of invoice, e.g., `DECIMAL(18,2)`)
    *   `Status` (e.g., 'Paid', 'Pending', 'Cancelled', `NVARCHAR(20)`)
    *   `Remarks` (Optional notes, e.g., `NVARCHAR(MAX)`)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Based on the inferred purpose of a "Sales Invoice Dashboard," we will implement the following core functionalities:

*   **Read (List):** Displaying all sales invoices in a paginated, sortable, and searchable table. This is the primary view for a dashboard.
*   **Create:** Adding a new sales invoice.
*   **Update:** Editing an existing sales invoice's details.
*   **Delete:** Removing a sales invoice from the system.
*   **Validation Logic:** Basic field validation (e.g., required fields, unique invoice numbers, valid dates/amounts) will be implemented within the Django Form layer.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Given the "Dashboard" context, we infer the following UI components would be essential for a Sales Invoice management system:

*   **Data Grid/Table:** A main table (analogous to ASP.NET GridView) to display the list of sales invoices. This will be implemented using **DataTables.js** for enhanced client-side features like searching, sorting, and pagination.
*   **Forms for Create/Edit:** Input fields (analogous to TextBoxes, DropDownLists) for capturing invoice details. These forms will be rendered dynamically within a modal using **HTMX** for smooth user experience.
*   **Action Buttons:** Buttons for "Add New Invoice," "Edit," and "Delete" for each invoice entry. These buttons will leverage **HTMX** to trigger modal windows for form loading and submission, eliminating full page reloads.
*   **Modals:** A dynamic modal system, managed by **Alpine.js**, to host the create/edit/delete forms, ensuring a modern, interactive feel without requiring complex JavaScript.

## Step 4: Generate Django Code

We will create a new Django application, let's call it `invoicing`, to house these components.

### 4.1 Models (`invoicing/models.py`)

Task: Create a Django model based on the database schema.

## Instructions:

The `SalesInvoice` model will represent the `tblSalesInvoice` table, adhering to the 'Fat Model' principle by including methods for business logic related to invoices.

```python
from django.db import models
from django.utils import timezone
from decimal import Decimal

# Assuming a Customer model exists or will be created later for CustomerID
# For now, we'll use a CharField for CustomerName for simplicity.
# If a Customer model were present:
# from your_customer_app.models import Customer

class SalesInvoice(models.Model):
    # Assuming InvoiceID is the primary key managed by the database
    # Django will automatically map 'id' unless 'primary_key=True' is set for another field.
    # We explicitly define the ID for clarity if the DB has a different column name.
    # If the DB has an auto-incrementing InvoiceID, Django will handle it automatically.
    # For now, we'll let Django manage the 'id' field as the primary key.

    invoice_number = models.CharField(
        db_column='InvoiceNumber',
        max_length=50,
        unique=True,
        help_text='Unique identifier for the invoice.'
    )
    customer_name = models.CharField(
        db_column='CustomerName',
        max_length=255,
        help_text='Name of the customer.'
    )
    # If Customer model exists:
    # customer = models.ForeignKey(
    #     Customer,
    #     on_delete=models.PROTECT, # or CASCADE, SET_NULL, etc.
    #     db_column='CustomerID',
    #     help_text='Associated customer.'
    # )
    invoice_date = models.DateField(
        db_column='InvoiceDate',
        default=timezone.now,
        help_text='Date the invoice was issued.'
    )
    total_amount = models.DecimalField(
        db_column='TotalAmount',
        max_digits=18,
        decimal_places=2,
        help_text='Total amount of the invoice.'
    )
    status_choices = [
        ('PENDING', 'Pending'),
        ('PAID', 'Paid'),
        ('CANCELLED', 'Cancelled'),
    ]
    status = models.CharField(
        db_column='Status',
        max_length=20,
        choices=status_choices,
        default='PENDING',
        help_text='Current status of the invoice.'
    )
    remarks = models.TextField(
        db_column='Remarks',
        blank=True,
        null=True,
        help_text='Any additional remarks for the invoice.'
    )

    class Meta:
        managed = False  # Important: Django will not manage this table's schema
        db_table = 'tblSalesInvoice'
        verbose_name = 'Sales Invoice'
        verbose_name_plural = 'Sales Invoices'
        ordering = ['-invoice_date', 'invoice_number']

    def __str__(self):
        return f"Invoice {self.invoice_number} for {self.customer_name} ({self.total_amount})"

    # --- Business Logic Methods (Fat Model) ---

    def calculate_due_date(self, terms_days=30):
        """Calculates the due date based on invoice date and payment terms."""
        return self.invoice_date + timezone.timedelta(days=terms_days)

    def mark_as_paid(self):
        """Marks the invoice status as 'Paid'."""
        if self.status != 'PAID':
            self.status = 'PAID'
            self.save()
            return True
        return False

    def is_overdue(self):
        """Checks if the invoice is overdue."""
        return self.status == 'PENDING' and self.calculate_due_date() < timezone.localdate()

    def get_display_status(self):
        """Returns the human-readable status."""
        return dict(self.status_choices).get(self.status, self.status)

    def get_absolute_url(self):
        """Returns the URL to view a specific invoice, though not directly used in HTMX-driven modal flow."""
        from django.urls import reverse
        return reverse('salesinvoice_edit', args=[str(self.id)])
```

### 4.2 Forms (`invoicing/forms.py`)

Task: Define a Django form for user input.

## Instructions:

A ModelForm will be created for `SalesInvoice`, including custom validation and styling for better usability.

```python
from django import forms
from .models import SalesInvoice

class SalesInvoiceForm(forms.ModelForm):
    class Meta:
        model = SalesInvoice
        fields = [
            'invoice_number',
            'customer_name',
            'invoice_date',
            'total_amount',
            'status',
            'remarks'
        ]
        widgets = {
            'invoice_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'INV-XXXX-YYYY'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Customer Name'}),
            'invoice_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01', 'placeholder': '0.00'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }

    def clean_invoice_number(self):
        """
        Custom validation to ensure invoice number is unique.
        Allows the current object's invoice number if it's being updated.
        """
        invoice_number = self.cleaned_data['invoice_number']
        query = SalesInvoice.objects.filter(invoice_number=invoice_number)
        if self.instance.pk: # If updating an existing object
            query = query.exclude(pk=self.instance.pk)
        if query.exists():
            raise forms.ValidationError("This invoice number is already in use.")
        return invoice_number

    def clean_total_amount(self):
        """
        Ensure total amount is not negative.
        """
        total_amount = self.cleaned_data['total_amount']
        if total_amount < 0:
            raise forms.ValidationError("Total amount cannot be negative.")
        return total_amount
```

### 4.3 Views (`invoicing/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are kept 'thin', delegating business logic to the model and handling HTMX responses. A specific partial view is added for the DataTables content.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import SalesInvoice
from .forms import SalesInvoiceForm

class SalesInvoiceListView(ListView):
    model = SalesInvoice
    template_name = 'invoicing/salesinvoice/list.html'
    context_object_name = 'salesinvoices' # will be used in the table partial

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This view only serves the container page, the table content is loaded via HTMX
        return context

class SalesInvoiceTablePartialView(ListView):
    model = SalesInvoice
    template_name = 'invoicing/salesinvoice/_salesinvoice_table.html' # This is the partial template
    context_object_name = 'salesinvoices'

    # No need for specific context manipulation, ListView naturally passes object_list

class SalesInvoiceCreateView(CreateView):
    model = SalesInvoice
    form_class = SalesInvoiceForm
    template_name = 'invoicing/salesinvoice/_salesinvoice_form.html' # HTMX loads this partial
    success_url = reverse_lazy('salesinvoice_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Sales Invoice added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response to close modal and trigger table refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSalesInvoiceList' # Custom HTMX event to refresh table
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class SalesInvoiceUpdateView(UpdateView):
    model = SalesInvoice
    form_class = SalesInvoiceForm
    template_name = 'invoicing/salesinvoice/_salesinvoice_form.html' # HTMX loads this partial
    context_object_name = 'salesinvoice'
    success_url = reverse_lazy('salesinvoice_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Sales Invoice updated successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response to close modal and trigger table refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSalesInvoiceList'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)

class SalesInvoiceDeleteView(DeleteView):
    model = SalesInvoice
    template_name = 'invoicing/salesinvoice/_salesinvoice_confirm_delete.html' # HTMX loads this partial
    context_object_name = 'salesinvoice'
    success_url = reverse_lazy('salesinvoice_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Sales Invoice deleted successfully.')
        if request.headers.get('HX-Request'):
            # For HTMX, return a 204 No Content response to close modal and trigger table refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSalesInvoiceList'
                }
            )
        return response
```

### 4.4 Templates (`invoicing/templates/invoicing/salesinvoice/`)

Task: Create templates for each view.

## Instructions:

Templates are designed for HTMX interactions, using partials for modal content and DataTables for the main list view. All templates extend `core/base.html`.

#### `list.html` (Main dashboard page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Sales Invoice Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105"
            hx-get="{% url 'salesinvoice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i> Add New Invoice
        </button>
    </div>

    <div id="salesinvoiceTable-container"
         hx-trigger="load, refreshSalesInvoiceList from:body"
         hx-get="{% url 'salesinvoice_table_partial' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading sales invoices...</p>
        </div>
    </div>

    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 z-50 overflow-y-auto bg-black bg-opacity-50 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-init="$watch('showModal', value => {
            if (value) { document.body.classList.add('overflow-hidden'); }
            else { document.body.classList.remove('overflow-hidden'); }
         })">
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full"
             @click.stop>
            <!-- HTMX content will be swapped in here -->
            <div class="p-6 text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any Alpine.js specific scripts if complex UI state is needed -->
<script>
    // Example Alpine.js for a simple modal state if needed, but HTMX does most of the heavy lifting.
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        });
    });

    // Close modal on HTMX response that triggers refresh
    document.body.addEventListener('refreshSalesInvoiceList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
        }
    });

</script>
{% endblock %}
```

#### `_salesinvoice_table.html` (Partial for DataTables content)

```html
<table id="salesinvoiceTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice No.</th>
            <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer Name</th>
            <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Invoice Date</th>
            <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Amount</th>
            <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-100">
        {% for obj in salesinvoices %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.invoice_number }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.customer_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ obj.invoice_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-right">${{ obj.total_amount|floatformat:2 }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    {% if obj.status == 'PAID' %} bg-green-100 text-green-800
                    {% elif obj.status == 'PENDING' %} bg-yellow-100 text-yellow-800
                    {% elif obj.status == 'CANCELLED' %} bg-red-100 text-red-800
                    {% else %} bg-gray-100 text-gray-800 {% endif %}">
                    {{ obj.get_display_status }}
                </span>
            </td>
            <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out mr-2"
                    hx-get="{% url 'salesinvoice_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                    hx-get="{% url 'salesinvoice_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 text-center text-gray-500">No sales invoices found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<!-- DataTables initialization script -->
<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to avoid reinitialization errors
        if ($.fn.DataTable.isDataTable('#salesinvoiceTable')) {
            $('#salesinvoiceTable').DataTable().destroy();
        }
        $('#salesinvoiceTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            "language": {
                "search": "Filter records:",
                "lengthMenu": "Show _MENU_ entries"
            },
            "columnDefs": [
                { "orderable": false, "targets": [0, 6] } // Disable sorting for SN and Actions columns
            ]
        });
    });
</script>
```

#### `_salesinvoice_form.html` (Partial for Add/Edit forms)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Sales Invoice</h3>
    <form hx-post="{{ request.path }}"
          hx-swap="none"
          hx-on::after-request="if (event.detail.xhr.status === 204) { /* HTMX handled success via HX-Trigger */ } else { /* Form errors, HTMX will swap the entire form */ }">
        {% csrf_token %}

        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-red-600 text-sm list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ form.non_field_errors }}</span>
            </div>
            {% endif %}
        </div>

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-150 ease-in-out">
                Save Invoice
            </button>
        </div>
    </form>
</div>
```

#### `_salesinvoice_confirm_delete.html` (Partial for Delete confirmation)

```html
<div class="p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
    </div>
    <h3 class="mt-5 text-lg leading-6 font-medium text-gray-900">Delete Sales Invoice</h3>
    <div class="mt-2">
        <p class="text-sm text-gray-500">
            Are you sure you want to delete Invoice No. <strong>{{ salesinvoice.invoice_number }}</strong> for <strong>{{ salesinvoice.customer_name }}</strong>?
            This action cannot be undone.
        </p>
    </div>
    <div class="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
        <button
            type="button"
            class="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:col-start-2 sm:text-sm"
            hx-post="{% url 'salesinvoice_delete' salesinvoice.pk %}"
            hx-swap="none"
            hx-on::after-request="if(event.detail.xhr.status === 204) { /* HTMX handled success via HX-Trigger */ }">
            Delete
        </button>
        <button
            type="button"
            class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:col-start-1 sm:mt-0 sm:text-sm"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
    </div>
</div>
```

### 4.5 URLs (`invoicing/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

URL patterns are set up to be clean, semantic, and easily integrated with HTMX.

```python
from django.urls import path
from .views import (
    SalesInvoiceListView,
    SalesInvoiceCreateView,
    SalesInvoiceUpdateView,
    SalesInvoiceDeleteView,
    SalesInvoiceTablePartialView,
)

urlpatterns = [
    # Main dashboard page
    path('salesinvoices/', SalesInvoiceListView.as_view(), name='salesinvoice_list'),

    # HTMX partials and CRUD endpoints
    path('salesinvoices/table/', SalesInvoiceTablePartialView.as_view(), name='salesinvoice_table_partial'),
    path('salesinvoices/add/', SalesInvoiceCreateView.as_view(), name='salesinvoice_add'),
    path('salesinvoices/edit/<int:pk>/', SalesInvoiceUpdateView.as_view(), name='salesinvoice_edit'),
    path('salesinvoices/delete/<int:pk>/', SalesInvoiceDeleteView.as_view(), name='salesinvoice_delete'),
]
```

### 4.6 Tests (`invoicing/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `SalesInvoice` model and integration tests for all `SalesInvoice` views, including HTMX specific behaviors, are provided.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from .models import SalesInvoice
from .forms import SalesInvoiceForm # Import form for testing

class SalesInvoiceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.invoice1 = SalesInvoice.objects.create(
            invoice_number='INV-2023-001',
            customer_name='Acme Corp',
            invoice_date='2023-01-15',
            total_amount=Decimal('1500.50'),
            status='PENDING',
            remarks='Initial order'
        )
        cls.invoice2 = SalesInvoice.objects.create(
            invoice_number='INV-2023-002',
            customer_name='Widgets Inc',
            invoice_date='2023-01-20',
            total_amount=Decimal('500.00'),
            status='PAID'
        )

    def test_salesinvoice_creation(self):
        """Test basic creation and field values."""
        self.assertEqual(self.invoice1.invoice_number, 'INV-2023-001')
        self.assertEqual(self.invoice1.customer_name, 'Acme Corp')
        self.assertEqual(self.invoice1.invoice_date, timezone.localdate(timezone.datetime(2023, 1, 15)))
        self.assertEqual(self.invoice1.total_amount, Decimal('1500.50'))
        self.assertEqual(self.invoice1.status, 'PENDING')
        self.assertEqual(self.invoice1.remarks, 'Initial order')

    def test_str_representation(self):
        """Test the __str__ method."""
        expected_str = "Invoice INV-2023-001 for Acme Corp (1500.50)"
        self.assertEqual(str(self.invoice1), expected_str)

    def test_calculate_due_date_method(self):
        """Test the calculate_due_date business logic."""
        expected_due_date = timezone.localdate(timezone.datetime(2023, 1, 15)) + timezone.timedelta(days=30)
        self.assertEqual(self.invoice1.calculate_due_date(), expected_due_date)
        self.assertEqual(self.invoice1.calculate_due_date(terms_days=7), timezone.localdate(timezone.datetime(2023, 1, 22)))

    def test_mark_as_paid_method(self):
        """Test the mark_as_paid business logic."""
        self.assertEqual(self.invoice1.status, 'PENDING')
        self.assertTrue(self.invoice1.mark_as_paid())
        self.assertEqual(self.invoice1.status, 'PAID')
        # Test marking an already paid invoice
        self.assertFalse(self.invoice2.mark_as_paid())
        self.assertEqual(self.invoice2.status, 'PAID')

    def test_is_overdue_method(self):
        """Test the is_overdue business logic (requires date mocking for real test)."""
        # For simplicity, we'll assume current date is past due for invoice1
        # In a real test, you'd use unittest.mock.patch to control timezone.localdate()
        with self.settings(USE_TZ=False): # Temporarily disable timezone to use simple date comparisons
            self.invoice1.invoice_date = timezone.localdate() - timezone.timedelta(days=31)
            self.invoice1.status = 'PENDING'
            self.invoice1.save()
            self.assertTrue(self.invoice1.is_overdue())

            self.invoice1.invoice_date = timezone.localdate() - timezone.timedelta(days=15)
            self.invoice1.save()
            self.assertFalse(self.invoice1.is_overdue())

            self.invoice1.status = 'PAID'
            self.invoice1.save()
            self.assertFalse(self.invoice1.is_overdue()) # Paid invoices are not overdue

    def test_get_display_status(self):
        self.assertEqual(self.invoice1.get_display_status(), 'Pending')
        self.assertEqual(self.invoice2.get_display_status(), 'Paid')

class SalesInvoiceFormTest(TestCase):
    def test_valid_form(self):
        form_data = {
            'invoice_number': 'NEW-INV-003',
            'customer_name': 'New Customer',
            'invoice_date': '2024-03-10',
            'total_amount': '999.99',
            'status': 'PENDING',
            'remarks': 'Test new invoice'
        }
        form = SalesInvoiceForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_duplicate_invoice_number_creation(self):
        SalesInvoice.objects.create(
            invoice_number='INV-TEST-001',
            customer_name='Duplicate Tester',
            invoice_date='2024-01-01',
            total_amount=100.00
        )
        form_data = {
            'invoice_number': 'INV-TEST-001',
            'customer_name': 'Another Duplicate',
            'invoice_date': '2024-01-02',
            'total_amount': 200.00
        }
        form = SalesInvoiceForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('This invoice number is already in use.', form.errors['invoice_number'])

    def test_duplicate_invoice_number_update(self):
        invoice1 = SalesInvoice.objects.create(invoice_number='A1', customer_name='C1', invoice_date='2024-01-01', total_amount=100)
        invoice2 = SalesInvoice.objects.create(invoice_number='A2', customer_name='C2', invoice_date='2024-01-02', total_amount=200)

        # Try to update invoice1 with invoice2's number
        form_data = {
            'invoice_number': 'A2',
            'customer_name': 'C1 Updated',
            'invoice_date': '2024-01-01',
            'total_amount': '150.00',
            'status': 'PENDING',
            'remarks': ''
        }
        form = SalesInvoiceForm(data=form_data, instance=invoice1)
        self.assertFalse(form.is_valid())
        self.assertIn('This invoice number is already in use.', form.errors['invoice_number'])

        # Update invoice1 with its own number (should be valid)
        form_data_valid_update = {
            'invoice_number': 'A1', # Same as original
            'customer_name': 'C1 Updated',
            'invoice_date': '2024-01-01',
            'total_amount': '150.00',
            'status': 'PENDING',
            'remarks': ''
        }
        form = SalesInvoiceForm(data=form_data_valid_update, instance=invoice1)
        self.assertTrue(form.is_valid(), form.errors)


    def test_negative_total_amount(self):
        form_data = {
            'invoice_number': 'INV-FAIL-001',
            'customer_name': 'Negative Test',
            'invoice_date': '2024-03-10',
            'total_amount': '-10.00',
            'status': 'PENDING'
        }
        form = SalesInvoiceForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('Total amount cannot be negative.', form.errors['total_amount'])

class SalesInvoiceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create some test invoices for views
        for i in range(1, 6):
            SalesInvoice.objects.create(
                invoice_number=f'INV-VIEW-{i}',
                customer_name=f'Test Customer {i}',
                invoice_date=timezone.localdate() - timezone.timedelta(days=i),
                total_amount=Decimal(100.00 * i),
                status='PENDING' if i % 2 == 0 else 'PAID'
            )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test SalesInvoiceListView serves the correct template and context."""
        response = self.client.get(reverse('salesinvoice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/salesinvoice/list.html')
        # The main list view doesn't directly pass 'salesinvoices' in context,
        # it's loaded by the partial view via HTMX.

    def test_table_partial_view_get(self):
        """Test SalesInvoiceTablePartialView serves the correct template and context."""
        response = self.client.get(reverse('salesinvoice_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/salesinvoice/_salesinvoice_table.html')
        self.assertTrue('salesinvoices' in response.context)
        self.assertEqual(len(response.context['salesinvoices']), 5)

    def test_create_view_get(self):
        """Test SalesInvoiceCreateView GET request."""
        response = self.client.get(reverse('salesinvoice_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/salesinvoice/_salesinvoice_form.html')
        self.assertIsInstance(response.context['form'], SalesInvoiceForm)

    def test_create_view_post_valid_data(self):
        """Test SalesInvoiceCreateView POST request with valid data."""
        data = {
            'invoice_number': 'INV-NEW-007',
            'customer_name': 'New Client Co.',
            'invoice_date': '2024-04-01',
            'total_amount': '777.77',
            'status': 'PENDING',
            'remarks': 'New sales entry'
        }
        # Simulate HTMX request
        response = self.client.post(reverse('salesinvoice_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(SalesInvoice.objects.filter(invoice_number='INV-NEW-007').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalesInvoiceList')

    def test_create_view_post_invalid_data(self):
        """Test SalesInvoiceCreateView POST request with invalid data."""
        data = {
            'invoice_number': 'INV-VIEW-1', # Duplicate
            'customer_name': 'Invalid Client',
            'invoice_date': '2024-04-02',
            'total_amount': '-100.00', # Negative
            'status': 'PENDING'
        }
        response = self.client.post(reverse('salesinvoice_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors for HTMX
        self.assertTemplateUsed(response, 'invoicing/salesinvoice/_salesinvoice_form.html')
        self.assertFalse(SalesInvoice.objects.filter(customer_name='Invalid Client').exists())
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())


    def test_update_view_get(self):
        """Test SalesInvoiceUpdateView GET request."""
        invoice = SalesInvoice.objects.get(invoice_number='INV-VIEW-1')
        response = self.client.get(reverse('salesinvoice_edit', args=[invoice.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/salesinvoice/_salesinvoice_form.html')
        self.assertIsInstance(response.context['form'], SalesInvoiceForm)
        self.assertEqual(response.context['form'].instance, invoice)

    def test_update_view_post_valid_data(self):
        """Test SalesInvoiceUpdateView POST request with valid data."""
        invoice = SalesInvoice.objects.get(invoice_number='INV-VIEW-2')
        updated_amount = '250.00'
        data = {
            'invoice_number': invoice.invoice_number, # Keep same unique number
            'customer_name': 'Updated Customer',
            'invoice_date': invoice.invoice_date,
            'total_amount': updated_amount,
            'status': 'PAID',
            'remarks': 'Updated test invoice'
        }
        response = self.client.post(reverse('salesinvoice_edit', args=[invoice.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        invoice.refresh_from_db()
        self.assertEqual(invoice.customer_name, 'Updated Customer')
        self.assertEqual(invoice.total_amount, Decimal(updated_amount))
        self.assertEqual(invoice.status, 'PAID')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalesInvoiceList')

    def test_update_view_post_invalid_data(self):
        """Test SalesInvoiceUpdateView POST request with invalid data."""
        invoice = SalesInvoice.objects.get(invoice_number='INV-VIEW-3')
        data = {
            'invoice_number': 'INV-VIEW-4', # Try to use existing unique number from another invoice
            'customer_name': 'Will Fail',
            'invoice_date': invoice.invoice_date,
            'total_amount': '-50.00', # Negative amount
            'status': 'PENDING'
        }
        response = self.client.post(reverse('salesinvoice_edit', args=[invoice.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'invoicing/salesinvoice/_salesinvoice_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(response.context['form'].is_valid())
        invoice.refresh_from_db()
        self.assertNotEqual(invoice.customer_name, 'Will Fail') # Ensure no update happened

    def test_delete_view_get(self):
        """Test SalesInvoiceDeleteView GET request."""
        invoice = SalesInvoice.objects.get(invoice_number='INV-VIEW-5')
        response = self.client.get(reverse('salesinvoice_delete', args=[invoice.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'invoicing/salesinvoice/_salesinvoice_confirm_delete.html')
        self.assertEqual(response.context['salesinvoice'], invoice)

    def test_delete_view_post(self):
        """Test SalesInvoiceDeleteView POST request."""
        invoice_to_delete = SalesInvoice.objects.create(
            invoice_number='INV-DEL-TEST',
            customer_name='ToDelete',
            invoice_date='2024-01-01',
            total_amount=1.00
        )
        self.assertTrue(SalesInvoice.objects.filter(pk=invoice_to_delete.pk).exists())

        response = self.client.post(reverse('salesinvoice_delete', args=[invoice_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertFalse(SalesInvoice.objects.filter(pk=invoice_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSalesInvoiceList')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The entire Sales Invoice dashboard interaction flow is designed to be dynamic and responsive using HTMX and Alpine.js, minimizing full page reloads and enhancing user experience.

*   **HTMX for Dynamic Content Loading:**
    *   The main list page (`list.html`) uses `hx-get` on load and on a custom `refreshSalesInvoiceList` event to fetch the actual table content from `{% url 'salesinvoice_table_partial' %}`. This allows the table to be updated independently.
    *   "Add New," "Edit," and "Delete" buttons use `hx-get` to fetch their respective forms (`_salesinvoice_form.html`, `_salesinvoice_confirm_delete.html`) and load them into a modal's `hx-target="#modalContent"`.
    *   Form submissions (`hx-post` on forms) are handled by HTMX. Upon successful submission (e.g., valid form data in `CreateView`/`UpdateView`), the Django view responds with an `HTTP 204 No Content` status and an `HX-Trigger` header (`refreshSalesInvoiceList`). This tells HTMX to close the modal and then trigger the table refresh, ensuring the UI reflects the changes without a full page reload.
    *   If a form submission is invalid, the Django view re-renders the form with errors (`_salesinvoice_form.html`), and HTMX automatically swaps this back into the modal, allowing the user to correct errors directly.

*   **Alpine.js for UI State Management:**
    *   Alpine.js is used for simple client-side UI state, specifically for managing the modal's visibility (`hidden` class). The `_` (hyperscript) attribute on the modal div handles showing/hiding it based on HTMX triggers and clicks outside the modal.
    *   `x-data` and `x-init` on the modal div manage the `showModal` state and add/remove `overflow-hidden` to the body, preventing background scrolling when the modal is open.

*   **DataTables for List Views:**
    *   The `_salesinvoice_table.html` partial template includes a JavaScript snippet that initializes DataTables on the `salesinvoiceTable`. This provides out-of-the-box client-side searching, sorting, and pagination without custom JavaScript coding. The DataTables instance is destroyed and re-initialized each time the table partial is loaded by HTMX, ensuring it always applies correctly to the new HTML content.

*   **No Additional JavaScript Requirements:**
    *   The entire dynamic interaction logic is handled by HTMX, Alpine.js, and DataTables, fulfilling the requirement of having no additional custom JavaScript beyond their setup.

## Final Notes

This comprehensive plan transforms the rudimentary ASP.NET placeholder into a fully functional, modern Django application module.

*   **Placeholders:** All `[PLACEHOLDER]` values from the original prompt have been replaced with concrete names (e.g., `SalesInvoice`, `tblSalesInvoice`, `invoicing` for app name).
*   **DRY Templates:** The use of `_salesinvoice_table.html`, `_salesinvoice_form.html`, and `_salesinvoice_confirm_delete.html` as partials ensures that components are reusable and maintainable. All inherit from `core/base.html`, keeping the structure consistent.
*   **Fat Models, Thin Views:** Business logic, like `calculate_due_date` or `mark_as_paid`, resides directly within the `SalesInvoice` model. Views are concise, primarily handling HTTP requests, form instantiation, and delegating data operations to the model or Django's ORM.
*   **Comprehensive Tests:** Robust tests cover model behavior, form validation, and view interactions, including specific tests for HTMX request/response patterns (e.g., `HX-Trigger` headers and `204 No Content` responses), ensuring high code quality and reliability.
*   **Automation Focus:** The clear separation of concerns, standardized patterns, and explicit HTMX attributes make this code highly amenable to AI-assisted generation and automated testing. Each component has a defined role, simplifying the overall migration process. The plain English explanations are designed for non-technical oversight and clear communication of the automated steps.