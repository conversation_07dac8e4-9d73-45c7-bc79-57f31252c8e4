This document outlines a comprehensive plan for modernizing your ASP.NET application module, "Material Planning - New," to a robust and scalable Django-based solution. Our approach leverages AI-assisted automation, focusing on a clean, modern Django 5.0+ architecture, HTMX for dynamic interactions, Alpine.js for UI state management, and DataTables for superior data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with several tables, primarily for displaying and filtering Work Order data.

*   **`tblSD_WO_Category`**: Used for the "WO Category" dropdown (`DDLTaskWOType`).
    *   Columns: `CId` (PK), `Symbol`, `CName`.
*   **`SD_Cust_master`**: Used for customer name autocomplete (`TxtSearchValue`).
    *   Columns: `CustomerId` (PK), `CustomerName`, `CompId`.
*   **`SD_Cust_WorkOrder_Master`**: The main table for displaying work orders in the `GridView`.
    *   Columns: `WONo` (PK), `FinYear`, `CustomerName`, `CustomerId` (FK to `SD_Cust_master`), `EnqId`, `PONo`, `SysDate`, `EmployeeName`, `CId` (FK to `tblSD_WO_Category`).
*   **`tblDG_BOM_Master`**: Used in a subquery to filter `WorkOrder` records. The original C# code uses `SD_Cust_WorkOrder_Master.WONo in (select WONo from tblDG_BOM_Master)`, which means it filters for Work Orders that *have* a corresponding entry in `tblDG_BOM_Master`.
    *   Columns: `WONo` (PK, likely `VARCHAR` as it's often a combination of characters and numbers).

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business logic.

**Analysis:**
The ASP.NET page `Planning_New.aspx` primarily serves as a search and listing interface for Work Orders, enabling users to find specific work orders for potential "material planning" activities. It does *not* provide CRUD operations for `WorkOrder` itself on this page, but a hyperlink suggests navigation to a detail/planning page.

*   **Read (List & Filter):** The main functionality is to retrieve and display a list of Work Orders.
    *   **Dynamic Filtering:** Users can filter by:
        *   Customer Name (with autocomplete)
        *   Enquiry No
        *   PO No
        *   WO No
        *   Work Order Category
    *   **Database Interaction:** The filtering logic is complex, involving conditional SQL queries and a stored procedure `Sp_WONO_NotInBom` (though the actual filtering condition in C# was `WONo IN (select WONo from tblDG_BOM_Master)`).
    *   **Session Context:** `CompId` and `FinYearId` are retrieved from the session, implying these are global company/financial year contexts for filtering.
    *   **Temporary Data Cleanup:** The `Page_Load` event includes logic to delete temporary material planning data associated with the current session. This suggests the current page is a prerequisite for a different module where temporary planning data is stored. This specific cleanup is outside the scope of *this* page's core function (searching/listing) and should be handled by the module that creates/manages this temporary data. For this migration, we focus on the search and display.
*   **Autocomplete:** A server-side web method (`sql`) provides customer name suggestions for `TxtSearchValue`.
*   **UI Dynamics:** The visibility of search input fields (`txtSearchCustomer` and `TxtSearchValue`) changes based on the selected search type (`DropDownList1`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
The page is built around a search form and a data grid.

*   **Search Controls:**
    *   `DropDownList1` (Search By): Django `forms.ChoiceField` with `hx-post` for dynamic form updates.
    *   `TxtSearchValue` (Customer Name): Django `forms.CharField` with `hx-get` for autocomplete suggestions.
    *   `txtSearchCustomer` (Enquiry/PO/WO No): Django `forms.CharField`.
    *   `DDLTaskWOType` (WO Category): Django `forms.ModelChoiceField`.
    *   `btnSearch`: Django `button` (can be implicit via `hx-trigger="submit"` on form).
*   **Data Display:**
    *   `SearchGridView1`: Will be replaced by a `<table>` rendered via DataTables for client-side pagination, sorting, and search.
    *   `HyperLinkField` for `WONo`: Will be a standard `<a>` tag in Django, linking to the `pdt.aspx` equivalent.

### Step 4: Generate Django Code

We will create a new Django app, e.g., `materialplanning`.

#### 4.1 Models (`materialplanning/models.py`)

```python
import re
from django.db import models
from django.db.models.manager import BaseManager
from django.urls import reverse

class WOCategory(models.Model):
    # Corresponds to tblSD_WO_Category
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}"

class Customer(models.Model):
    # Corresponds to SD_Cust_master
    customerid = models.IntegerField(db_column='CustomerId', primary_key=True)
    customername = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customername} [{self.customerid}]"

class BomMaster(models.Model):
    # Corresponds to tblDG_BOM_Master, used for filtering WorkOrders
    # Assuming WONo is a unique identifier or primary key in this context for filtering
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=50)

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master Entry'
        verbose_name_plural = 'BOM Master Entries'

    def __str__(self):
        return self.wono

class WorkOrderManager(BaseManager):
    """
    Custom manager to encapsulate the complex Work Order filtering logic
    from the original ASP.NET BindDataCust method and the stored procedure.
    """
    def get_filtered_workorders(self, comp_id, fin_year_id, search_type, search_value, wo_category_id):
        # Start with all Work Orders that have an entry in tblDG_BOM_Master
        # This replicates the C# code's `L` parameter: "WONo in (select WONo from tblDG_BOM_Master)"
        queryset = self.filter(wono__in=BomMaster.objects.values('wono'))

        # Assuming CompId and FinYearId are session-based global filters applicable to WorkOrder
        # If these are columns on SD_Cust_WorkOrder_Master, add filters like:
        # queryset = queryset.filter(comp_id=comp_id, fin_year_id=fin_year_id)
        # For now, we omit them as they weren't explicitly filtered on the WorkOrder table itself in the original SQL.

        # Apply search filters based on search_type
        if search_type == '1':  # Enquiry No
            if search_value:
                queryset = queryset.filter(enqid=search_value)
        elif search_type == '2':  # PO No
            if search_value:
                queryset = queryset.filter(pono=search_value)
        elif search_type == '3':  # WO No
            if search_value:
                queryset = queryset.filter(wono=search_value)
        elif search_type == '0':  # Customer Name
            if search_value:
                # Attempt to parse CustomerId from "CustomerName [CustomerId]" format
                customer_id = WorkOrder.parse_customer_input(search_value)
                if customer_id:
                    queryset = queryset.filter(customerid=customer_id)
                else:
                    # Fallback to broad search if ID not found, replicating approximate original behavior
                    queryset = queryset.filter(customername__icontains=search_value)

        # Apply WO Category filter
        if wo_category_id and wo_category_id != 'WO Category': # 'WO Category' was the default empty label
            queryset = queryset.filter(cid=wo_category_id)

        # Order results similar to how GridView would display or by a logical key
        return queryset.order_by('sysdate', 'wono') # Order by Gen. Date, then WO No

class WorkOrder(models.Model):
    # Corresponds to SD_Cust_WorkOrder_Master
    wono = models.CharField(db_column='WONo', primary_key=True, max_length=50) # Assuming WO No is string
    finyear = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)
    customername = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    customerid = models.ForeignKey(Customer, on_delete=models.DO_NOTHING, db_column='CustomerId', related_name='workorders', blank=True, null=True)
    enqid = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True)
    pono = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    sysdate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    cid = models.ForeignKey(WOCategory, on_delete=models.DO_NOTHING, db_column='CId', related_name='workorders', blank=True, null=True)

    # Attach the custom manager
    objects = WorkOrderManager()

    class Meta:
        managed = False # Django will not manage this table's schema
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono

    def get_absolute_url(self):
        # Replicates the HyperLinkField DataNavigateUrlFormatString
        # Assuming 'materialplanning:workorder_detail' is the URL name for the detail page
        # The original URL had ?ModId=4&SubModId=33, which might be context for the target page.
        # This can be passed as query parameters in Django if needed.
        return reverse('materialplanning:workorder_detail', kwargs={'wono': self.wono}) + '?ModId=4&SubModId=33'

    @staticmethod
    def parse_customer_input(input_string):
        """
        Parses a string like 'CustomerName [CustomerId]' to extract the CustomerId.
        Used for the autocomplete functionality from the TxtSearchValue.
        """
        if not input_string:
            return None
        match = re.search(r'\[(\d+)\]$', input_string)
        return match.group(1) if match else None

```

#### 4.2 Forms (`materialplanning/forms.py`)

```python
from django import forms
from .models import WOCategory

class WorkOrderSearchForm(forms.Form):
    SEARCH_TYPE_CHOICES = [
        ('0', 'Customer Name'),
        ('1', 'Enquiry No'),
        ('2', 'PO No'),
        ('3', 'WO No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_TYPE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'id': 'id_search_by', # For Alpine.js targeting
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'x-model': 'searchByType' # Alpine.js model
        })
    )

    # These fields will be shown/hidden by Alpine.js based on search_by
    txt_search_customer = forms.CharField(
        label="Search Value", # Label will be dynamic in template based on search_by
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Enquiry/PO/WO No...'
        })
    )

    txt_search_value = forms.CharField(
        label="Customer Name",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Customer Name...',
            'hx-get': '{{% url "materialplanning:customer_autocomplete" %}}', # HTMX for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off' # Disable browser autocomplete
        })
    )

    wo_category = forms.ModelChoiceField(
        queryset=WOCategory.objects.all().order_by('cname'),
        required=False,
        empty_label="WO Category",
        label="WO Category",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set default value for search_by if not provided, to match ASP.NET behavior
        if 'search_by' not in self.initial:
            self.initial['search_by'] = '0' # Default to Customer Name
        
        # Adjusting the labels for dynamic display in the template
        self.fields['txt_search_customer'].label = '' # Handled dynamically in template
        self.fields['txt_search_value'].label = '' # Handled dynamically in template

```

#### 4.3 Views (`materialplanning/views.py`)

```python
from django.views.generic import ListView, TemplateView
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from django.contrib import messages
from django.db.models import Q # For OR queries if needed, though WorkOrderManager handles logic
from .models import WorkOrder, Customer, WOCategory
from .forms import WorkOrderSearchForm

# Define a thin view for the main listing page
class WorkOrderPlanningNewView(TemplateView):
    template_name = 'materialplanning/planning_new/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with GET data or defaults
        form = WorkOrderSearchForm(self.request.GET)
        context['form'] = form
        
        # Initially load the table content
        # For the first load, we render the table with default filters.
        # HTMX will then take over for subsequent updates.
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        
        # Initial call to get filtered data (before form submission)
        # This assumes initial load needs to display some data.
        # If no search is applied on initial load, provide empty string/None for values.
        search_type = self.request.GET.get('search_by', '0') # Default to Customer Name
        search_value = self.request.GET.get('txt_search_customer') or self.request.GET.get('txt_search_value')
        wo_category_id = self.request.GET.get('wo_category')
        
        # Pass dummy session data for initial load if not set
        if comp_id is None: comp_id = 0 # Default or retrieve from a more robust source
        if fin_year_id is None: fin_year_id = 0 # Default or retrieve from a more robust source

        workorders = WorkOrder.objects.get_filtered_workorders(
            comp_id, fin_year_id, search_type, search_value, wo_category_id
        )
        context['workorders'] = workorders
        return context

# View for dynamically updating the search input fields based on dropdown selection
class SearchInputPartialView(TemplateView):
    template_name = 'materialplanning/planning_new/_search_inputs.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        form = WorkOrderSearchForm(self.request.POST or None) # Use POST data to populate the form
        context['form'] = form
        context['search_by'] = form.data.get('search_by', '0') # Current selected search type
        return context
    
    def post(self, request, *args, **kwargs):
        # This view only responds to POST (hx-post) for rendering partial HTML
        context = self.get_context_data(**kwargs)
        html = render_to_string(self.template_name, context, request=request)
        return HttpResponse(html)

# View for dynamically updating the Work Order table
class WorkOrderTablePartialView(ListView):
    model = WorkOrder
    template_name = 'materialplanning/planning_new/_workorder_table.html'
    context_object_name = 'workorders'
    paginate_by = 20 # Replicate original PageSize=20, though DataTables handles pagination

    def get_queryset(self):
        # Get search parameters from GET request (for HTMX refresh)
        search_by = self.request.GET.get('search_by', '0') # Default to Customer Name
        txt_search_customer = self.request.GET.get('txt_search_customer')
        txt_search_value = self.request.GET.get('txt_search_value')
        wo_category_id = self.request.GET.get('wo_category')

        # Determine which text input to use based on search_by
        search_value = None
        if search_by in ['1', '2', '3']:
            search_value = txt_search_customer
        elif search_by == '0':
            search_value = txt_search_value

        # Retrieve session data for CompId and FinYearId
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')
        
        # Pass dummy session data if not available
        if comp_id is None: comp_id = 0
        if fin_year_id is None: fin_year_id = 0

        # Call the custom manager method to get filtered data
        queryset = WorkOrder.objects.get_filtered_workorders(
            comp_id, fin_year_id, search_by, search_value, wo_category_id
        )
        return queryset

    def get(self, request, *args, **kwargs):
        # This view handles both initial load and HTMX requests for table update
        response = super().get(request, *args, **kwargs)
        return response

# View for Customer Autocomplete
class CustomerAutocompleteView(ListView):
    model = Customer
    context_object_name = 'customers'

    def get_queryset(self):
        query = self.request.GET.get('q', '')
        if query:
            # Filter by customer name starting with prefixText (case-insensitive)
            # Replicates the original C# WebMethod's logic for prefix matching
            queryset = Customer.objects.filter(customername__istartswith=query).order_by('customername')
        else:
            queryset = Customer.objects.none()
        # Limit the number of suggestions if needed (original C# had count parameter, but commented out break at 10)
        return queryset[:10] # Limit to 10 suggestions as a common practice

    def render_to_response(self, context, **response_kwargs):
        # Return an HTML partial for HTMX
        html_content = render_to_string(
            'materialplanning/planning_new/_customer_autocomplete_results.html',
            context,
            request=self.request
        )
        return HttpResponse(html_content)

```

#### 4.4 Templates

**App structure:** `materialplanning/templates/materialplanning/planning_new/`

**`materialplanning/planning_new/list.html`** (Main page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Planning - New</h2>
        <!-- No "Add New" button on this page, as it's a search/selection page -->
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6" x-data="{ searchByType: '{{ form.search_by.value }}' }">
        <form hx-get="{% url 'materialplanning:workorder_table' %}" 
              hx-target="#workorder-list-container" 
              hx-swap="innerHTML" 
              hx-indicator="#loading-indicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.search_by.label }}</label>
                    {{ form.search_by }}
                </div>
                
                <div x-show="searchByType === '0'">
                    <label for="{{ form.txt_search_value.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.txt_search_value.label }}</label>
                    {{ form.txt_search_value }}
                    <div id="autocomplete-results" class="relative">
                        <!-- Autocomplete results will be loaded here -->
                    </div>
                </div>

                <div x-show="searchByType === '1' || searchByType === '2' || searchByType === '3'">
                    <label for="{{ form.txt_search_customer.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        <span x-text="searchByType === '1' ? 'Enquiry No' : (searchByType === '2' ? 'PO No' : 'WO No')"></span>
                    </label>
                    {{ form.txt_search_customer }}
                </div>

                <div>
                    <label for="{{ form.wo_category.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.wo_category.label }}</label>
                    {{ form.wo_category }}
                </div>
                
                <div>
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div id="workorder-list-container" 
         hx-trigger="load, reloadWorkOrderList from:body" 
         hx-get="{% url 'materialplanning:workorder_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}" 
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div id="loading-indicator" class="text-center p-4 htmx-indicator">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
        <!-- Initial table render from backend -->
        {% include 'materialplanning/planning_new/_workorder_table.html' %}
    </div>

    <!-- Modal placeholder (not strictly needed for this page, but good practice for future CRUD) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed beyond x-data for searchByType
    });

    // Initialize DataTables after HTMX loads the table content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'workorder-list-container') {
            $('#workorderTable').DataTable({
                "pageLength": 20, // Matches original PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "destroy": true, // Destroy existing table if already initialized
            });
        }
    });

</script>
{% endblock %}
```

**`materialplanning/planning_new/_search_inputs.html`** (Partial for dynamic search input fields - *optional, could be done purely with Alpine.js*)
*Self-correction: The Alpine.js solution for `x-show` is more idiomatic and simpler than an HTMX swap for this specific dynamic input field visibility. The `list.html` template above already implements the Alpine.js `x-show` logic directly.*

**`materialplanning/planning_new/_workorder_table.html`** (Partial for DataTables content)

```html
<div class="overflow-x-auto">
    <table id="workorderTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Customer Name</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider hidden">Enquiry No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider hidden">PO No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">WO No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Gen. Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Gen. By</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for wo in workorders %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b text-center">{{ wo.finyear|default:"-" }}</td>
                <td class="py-2 px-4 border-b text-left">{{ wo.customername|default:"-" }}</td>
                <td class="py-2 px-4 border-b text-center">{{ wo.customerid.customerid|default:"-" }}</td>
                <td class="py-2 px-4 border-b text-center hidden">{{ wo.enqid|default:"-" }}</td>
                <td class="py-2 px-4 border-b text-left hidden">{{ wo.pono|default:"-" }}</td>
                <td class="py-2 px-4 border-b text-center">
                    <a href="{{ wo.get_absolute_url }}" class="text-blue-600 hover:text-blue-800 font-medium">
                        {{ wo.wono }}
                    </a>
                </td>
                <td class="py-2 px-4 border-b text-center">{{ wo.sysdate|date:"Y-m-d"|default:"-" }}</td>
                <td class="py-2 px-4 border-b text-left">{{ wo.employeename|default:"-" }}</td>
                <td class="py-2 px-4 border-b text-center">
                    <!-- Actions would go here if there were edit/delete on this page -->
                    <!-- E.g., a button to initiate planning for this WO -->
                    <button class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-2 rounded text-sm">
                        Plan
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 px-4 border-b text-center text-lg text-red-700">No data to display!</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- DataTables initialization will be triggered by htmx:afterSwap event in list.html -->
```

**`materialplanning/planning_new/_customer_autocomplete_results.html`** (Partial for customer autocomplete)

```html
<ul class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 max-h-60 overflow-auto">
    {% for customer in customers %}
    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-800"
        hx-on--click="document.getElementById('id_txt_search_value').value = '{{ customer.customername }} [{{ customer.customerid }}]'; this.closest('div').innerHTML = '';"
        tabindex="0">
        {{ customer.customername }} [{{ customer.customerid }}]
    </li>
    {% empty %}
    <li class="px-4 py-2 text-gray-500">No results found.</li>
    {% endfor %}
</ul>
```

#### 4.5 URLs (`materialplanning/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderPlanningNewView,
    WorkOrderTablePartialView,
    CustomerAutocompleteView,
)

app_name = 'materialplanning' # Define app_name for namespacing URLs

urlpatterns = [
    # Main page for material planning new
    path('planning/new/', WorkOrderPlanningNewView.as_view(), name='planning_new'),
    
    # HTMX endpoint for the Work Order table
    path('planning/new/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),

    # HTMX endpoint for customer autocomplete
    path('planning/new/customer-autocomplete/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),

    # Placeholder for the detail page (equivalent to pdt.aspx)
    # The WONo is likely a string, so using <str:wono>
    path('planning/<str:wono>/detail/', TemplateView.as_view(template_name='materialplanning/workorder_detail.html'), name='workorder_detail'),
]

```

#### 4.6 Tests (`materialplanning/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import ProgrammingError # For handling managed=False
from .models import WorkOrder, WOCategory, Customer, BomMaster

class MaterialPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for unmanaged models
        # Note: For managed=False models, you typically ensure the database
        # has these tables and data, or use a test database setup that
        # includes these tables. For isolated unit tests, we can mock or
        # use an in-memory SQLite with schema created for tests.
        # Here, we'll create directly assuming the models can interact with DB.

        try:
            # Create categories
            cls.category1 = WOCategory.objects.create(cid=1, symbol='CAT1', cname='Category One')
            cls.category2 = WOCategory.objects.create(cid=2, symbol='CAT2', cname='Category Two')

            # Create customers
            cls.customer1 = Customer.objects.create(customerid=101, customername='Alpha Customer', compid=1)
            cls.customer2 = Customer.objects.create(customerid=102, customername='Beta Company', compid=1)

            # Create BOM entries (Work orders that "are in BOM")
            cls.bom_entry1 = BomMaster.objects.create(wono='WO-001')
            cls.bom_entry2 = BomMaster.objects.create(wono='WO-002')
            cls.bom_entry3 = BomMaster.objects.create(wono='WO-003')
            cls.bom_entry4 = BomMaster.objects.create(wono='WO-004') # This WO will NOT be in BOM_Master and thus not returned

            # Create Work Orders
            cls.wo1 = WorkOrder.objects.create(
                wono='WO-001', finyear='2023-24', customername='Alpha Customer', customerid=cls.customer1,
                enqid='ENQ-A', pono='PO-X', sysdate='2023-01-15T10:00:00Z', employeename='John Doe', cid=cls.category1
            )
            cls.wo2 = WorkOrder.objects.create(
                wono='WO-002', finyear='2023-24', customername='Beta Company', customerid=cls.customer2,
                enqid='ENQ-B', pono='PO-Y', sysdate='2023-01-20T11:00:00Z', employeename='Jane Smith', cid=cls.category2
            )
            cls.wo3 = WorkOrder.objects.create(
                wono='WO-003', finyear='2023-24', customername='Alpha Customer', customerid=cls.customer1,
                enqid='ENQ-C', pono='PO-Z', sysdate='2023-01-25T12:00:00Z', employeename='John Doe', cid=cls.category1
            )
            cls.wo4_not_in_bom = WorkOrder.objects.create( # This WO should not be returned by default filter
                wono='WO-004', finyear='2023-24', customername='Gamma Corp', customerid=cls.customer1,
                enqid='ENQ-D', pono='PO-D', sysdate='2023-01-26T12:00:00Z', employeename='Jane Smith', cid=cls.category2
            )
        except ProgrammingError as e:
            # Handle cases where the database might not have the tables for managed=False models
            print(f"Skipping model tests due to database error (managed=False tables not found/created): {e}")
            raise

    def test_wocategory_creation(self):
        self.assertEqual(self.category1.cname, 'Category One')
        self.assertEqual(str(self.category1), 'CAT1 - Category One')

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customername, 'Alpha Customer')
        self.assertEqual(str(self.customer1), 'Alpha Customer [101]')

    def test_workorder_creation(self):
        self.assertEqual(self.wo1.wono, 'WO-001')
        self.assertEqual(self.wo1.customerid, self.customer1)
        self.assertEqual(self.wo1.cid, self.category1)

    def test_workorder_get_absolute_url(self):
        expected_url = reverse('materialplanning:workorder_detail', kwargs={'wono': 'WO-001'}) + '?ModId=4&SubModId=33'
        self.assertEqual(self.wo1.get_absolute_url(), expected_url)

    def test_parse_customer_input(self):
        self.assertEqual(WorkOrder.parse_customer_input('Alpha Customer [101]'), '101')
        self.assertIsNone(WorkOrder.parse_customer_input('Alpha Customer'))
        self.assertIsNone(WorkOrder.parse_customer_input(''))

    def test_get_filtered_workorders_initial(self):
        # Default filter should return only WOs that are in BomMaster
        filtered_wos = WorkOrder.objects.get_filtered_workorders(
            comp_id=1, fin_year_id=2023, search_type='0', search_value=None, wo_category_id=None
        )
        self.assertEqual(filtered_wos.count(), 3)
        self.assertIn(self.wo1, filtered_wos)
        self.assertIn(self.wo2, filtered_wos)
        self.assertIn(self.wo3, filtered_wos)
        self.assertNotIn(self.wo4_not_in_bom, filtered_wos)

    def test_get_filtered_workorders_by_customer_id(self):
        filtered_wos = WorkOrder.objects.get_filtered_workorders(
            comp_id=1, fin_year_id=2023, search_type='0', search_value='Alpha Customer [101]', wo_category_id=None
        )
        self.assertEqual(filtered_wos.count(), 2)
        self.assertIn(self.wo1, filtered_wos)
        self.assertIn(self.wo3, filtered_wos)
        self.assertNotIn(self.wo2, filtered_wos)

    def test_get_filtered_workorders_by_customer_name_no_id(self):
        # Fallback to icontains if no ID parsed
        filtered_wos = WorkOrder.objects.get_filtered_workorders(
            comp_id=1, fin_year_id=2023, search_type='0', search_value='alpha', wo_category_id=None
        )
        self.assertEqual(filtered_wos.count(), 2) # WO1, WO3 from Alpha Customer
        self.assertIn(self.wo1, filtered_wos)
        self.assertIn(self.wo3, filtered_wos)

    def test_get_filtered_workorders_by_enquiry_no(self):
        filtered_wos = WorkOrder.objects.get_filtered_workorders(
            comp_id=1, fin_year_id=2023, search_type='1', search_value='ENQ-B', wo_category_id=None
        )
        self.assertEqual(filtered_wos.count(), 1)
        self.assertIn(self.wo2, filtered_wos)

    def test_get_filtered_workorders_by_wo_category(self):
        filtered_wos = WorkOrder.objects.get_filtered_workorders(
            comp_id=1, fin_year_id=2023, search_type='0', search_value=None, wo_category_id=self.category2.cid
        )
        self.assertEqual(filtered_wos.count(), 1)
        self.assertIn(self.wo2, filtered_wos)

class MaterialPlanningViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Ensure test data exists in the database for view tests
        # Use the same setup as model tests
        MaterialPlanningModelTest.setUpTestData()

    def setUp(self):
        self.client = Client()
        # Mock session data as original ASP.NET uses it
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    def test_planning_new_view_get(self):
        response = self.client.get(reverse('materialplanning:planning_new'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialplanning/planning_new/list.html')
        self.assertIsInstance(response.context['form'], WorkOrderSearchForm)
        self.assertContains(response, 'Material Planning - New')
        # Check if initial data is loaded into the table container
        self.assertContains(response, 'WO-001') # Check for data in initial table render

    def test_workorder_table_partial_view_get(self):
        # Test basic HTMX table refresh
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplanning:workorder_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialplanning/planning_new/_workorder_table.html')
        self.assertContains(response, 'WO-001') # Should contain initial WO data

    def test_workorder_table_partial_view_filter_by_enquiry_no(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('materialplanning:workorder_table'),
            {'search_by': '1', 'txt_search_customer': 'ENQ-B'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-002')
        self.assertNotContains(response, 'WO-001')

    def test_workorder_table_partial_view_filter_by_customer_name(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('materialplanning:workorder_table'),
            {'search_by': '0', 'txt_search_value': 'Alpha Customer [101]'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-001')
        self.assertContains(response, 'WO-003')
        self.assertNotContains(response, 'WO-002')

    def test_customer_autocomplete_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('materialplanning:customer_autocomplete'),
            {'q': 'alp'},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialplanning/planning_new/_customer_autocomplete_results.html')
        self.assertContains(response, 'Alpha Customer [101]')
        self.assertNotContains(response, 'Beta Company')

    def test_customer_autocomplete_view_no_query(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(
            reverse('materialplanning:customer_autocomplete'),
            {'q': ''},
            **headers
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No results found.')
        self.assertNotContains(response, 'Alpha Customer')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX:** All dynamic content updates, including search filtering and table refreshes, will use HTMX.
    *   The main list view (`list.html`) contains the search form.
    *   The form uses `hx-get` to target the `workorder-list-container` and swap its `innerHTML` with the response from `workorder_table` URL.
    *   The `hx-trigger="load, reloadWorkOrderList from:body"` on `workorder-list-container` ensures the table is loaded on page load and can be manually refreshed (e.g., if another part of the system adds/updates a WO).
    *   `hx-indicator` is used to show a loading spinner.
    *   `hx-on--click` is used in the autocomplete results to set the input field value and clear the results.
*   **Alpine.js:** Used for simple UI state management, specifically for showing/hiding the appropriate search input field (`txt_search_customer` vs. `txt_search_value`) based on the `search_by` dropdown selection.
    *   `x-data="{ searchByType: '{{ form.search_by.value }}' }"` initializes the Alpine.js component.
    *   `x-model="searchByType"` binds the dropdown value to the `searchByType` variable.
    *   `x-show` directives conditionally display the input fields.
*   **DataTables:** The `_workorder_table.html` partial contains the HTML table.
    *   The DataTables JavaScript initialization (`$('#workorderTable').DataTable()`) is placed within a `htmx:afterSwap` event listener in `list.html`. This ensures DataTables is initialized *after* HTMX has swapped the new table HTML into the DOM, making it fully functional.
    *   `"destroy": true` is important to re-initialize DataTables if the table is swapped multiple times.
*   **No full page reloads:** All search and filter operations will trigger HTMX requests, updating only the necessary parts of the page.

## Final Notes

*   **Placeholders:** `{{% url "materialplanning:workorder_detail" wono=wo.wono %}}` is a placeholder for the actual detail page URL. The original `pdt.aspx?WONo={0}&ModId=4&SubModId=33` implies `ModId` and `SubModId` might be needed as query parameters for the destination page. This is handled by appending them to the Django URL.
*   **DRY Templates:** The use of `_workorder_table.html` as a partial is a good example of DRY, allowing the table content to be updated independently via HTMX.
*   **Fat Model, Thin View:** The complex filtering logic from `BindDataCust` has been encapsulated within the `WorkOrderManager.get_filtered_workorders` method, keeping the Django views concise and focused on orchestrating the request/response.
*   **Session Data (`CompId`, `FinYearId`):** The assumption is made that these are already available in `request.session`. If not, a custom middleware or context processor would be needed to populate them.
*   **Temporary Data Cleanup:** The ASP.NET `Page_Load` had logic to clean up temporary material planning tables. This is outside the scope of *this* page's core functionality (searching/listing) and should be managed by the module responsible for creating/using that temporary data.
*   **Error Handling:** The `try-catch` blocks in ASP.NET are implicitly handled by Django's robust error reporting and middleware. Specific user-facing error messages can be added using Django's `messages` framework if required for certain scenarios.