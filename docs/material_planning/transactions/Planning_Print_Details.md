This comprehensive modernization plan outlines the strategy for transitioning your ASP.NET application's "Planning_Print_Details.aspx" functionality to a modern Django-based solution. Our approach focuses on automated conversion, clean architecture, and enhanced user experience using HTMX, Alpine.js, and DataTables, eliminating the need for Crystal Reports and complex manual JavaScript.

## ASP.NET to Django Conversion Plan: BOM Material Planning - Print

This plan addresses the conversion of a specific ASP.NET page responsible for displaying a detailed report from various material planning and master data tables. Since this is a *report viewing* page and not a direct CRUD (Create, Read, Update, Delete) interface for a single database entity, we will adapt the standard Django CRUD pattern to a robust data display solution.

**Key Modernization Principles Applied:**
*   **Django 5.0+:** Utilizing the latest Django features and best practices.
*   **Fat Model, Thin View:** Complex data aggregation logic is encapsulated within Django Managers or dedicated service classes, keeping views concise (under 15 lines of code).
*   **HTMX + Alpine.js:** For dynamic, client-side interactions without writing traditional JavaScript frameworks.
*   **DataTables Integration:** All tabular data will be presented using DataTables for powerful searching, sorting, and pagination.
*   **DRY Templates:** Efficient template inheritance and partials.
*   **Managed = False Models:** Mapping directly to your existing database tables.
*   **Automated Conversion Focus:** Guiding you through steps that can be largely automated using AI-assisted tools.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns accessed by the ASP.NET code.

**Instructions:**
The ASP.NET code-behind extensively queries multiple SQL Server tables. We will create Django models for each of these tables, setting `managed = False` to ensure they interact with your existing database without Django attempting to manage their schema.

**Identified Tables and Inferred Columns:**

1.  **`tblMP_Material_Master`**
    *   `Id` (int, Primary Key)
    *   `SysDate` (datetime, maps to `PLDate`)
    *   `WONo` (string)
    *   `CompId` (int, Foreign Key to Company)
    *   `PLNo` (string)

2.  **`tblMM_PR_Master`**
    *   `PRNo` (string)
    *   `SysDate` (datetime, maps to `PRDate`)
    *   `PLNId` (int, Foreign Key to `tblMP_Material_Master.Id`)

3.  **`tblMP_Material_Detail`**
    *   `Id` (int, Primary Key)
    *   `Mid` (int, Foreign Key to `tblMP_Material_Master.Id`)
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master.Id`)
    *   `RM` (boolean/int, Raw Material flag)
    *   `PRO` (boolean/int, Process flag)
    *   `FIN` (boolean/int, Finish Material flag)

4.  **`tblMP_Material_Finish`**
    *   `Id` (int, Primary Key, inferred, typically tables have one)
    *   `DMid` (int, Foreign Key to `tblMP_Material_Detail.Id`)
    *   `DelDate` (datetime)
    *   `Discount` (float/double)
    *   `Rate` (float/double)
    *   `Qty` (float/double)
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master.Id`)
    *   `SupplierId` (int, Foreign Key to `tblMM_Supplier_master.SupplierId`)

5.  **`tblMP_Material_RawMaterial`**
    *   `Id` (int, Primary Key, inferred)
    *   `DMid` (int, Foreign Key to `tblMP_Material_Detail.Id`)
    *   `DelDate` (datetime)
    *   `Discount` (float/double)
    *   `Rate` (float/double)
    *   `Qty` (float/double)
    *   `SupplierId` (int, Foreign Key to `tblMM_Supplier_master.SupplierId`)
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master.Id`)

6.  **`tblMP_Material_Process`**
    *   `Id` (int, Primary Key, inferred)
    *   `DMid` (int, Foreign Key to `tblMP_Material_Detail.Id`)
    *   `Discount` (float/double)
    *   `DelDate` (datetime)
    *   `Rate` (float/double)
    *   `Qty` (float/double)
    *   `SupplierId` (int, Foreign Key to `tblMM_Supplier_master.SupplierId`)
    *   `ItemId` (int, Foreign Key to `tblDG_Item_Master.Id`)

7.  **`tblMM_Supplier_master`**
    *   `SupplierId` (int, Primary Key)
    *   `SupplierName` (string)

8.  **`tblDG_Item_Master`**
    *   `Id` (int, Primary Key)
    *   `ItemCode` (string)
    *   `UOMBasic` (int, Foreign Key to `Unit_Master.Id`)
    *   `ManfDesc` (string)

9.  **`Unit_Master`**
    *   `Id` (int, Primary Key)
    *   `Symbol` (string)

10. **`Company`** (Inferred from `Session["compid"]` and `fun.getCompany`/`fun.CompAdd`)
    *   `Id` (int, Primary Key, maps to `CompId`)
    *   `Name` (string, for `Company` parameter in report)
    *   `Address` (string, for `Address` parameter in report)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations performed by the ASP.NET page.

**Instructions:**
The primary function of `Planning_Print_Details.aspx` is to act as a **report viewer**. It:
*   Retrieves various parameters from the URL (e.g., `FinYearId`, `plno`, `MId`, `WONo`).
*   Executes complex SQL queries to gather data from multiple related tables.
*   Aggregates and formats this data into a structured dataset.
*   Passes the dataset and additional parameters to a Crystal Report for display.
*   Provides a "Cancel" button for navigation.

**CRUD Operations:** This page is fundamentally a **Read-only** operation. It does not involve creating, updating, or deleting database records via user input on this specific page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to modern Django/HTMX/Alpine.js components.

**Instructions:**
*   **`CrystalReportViewer`:** This will be replaced by a dynamic HTML table rendered using Django templates, enhanced with **DataTables** for client-side functionality (searching, sorting, pagination).
*   **`asp:Panel` with `ScrollBars="Auto"`:** DataTables handles overflow and pagination automatically, making a dedicated scrollable panel less necessary for standard table views.
*   **`asp:Button ID="Cancel"`:** This will be a simple HTML `<button>` or `<a>` tag that triggers a navigation back to the previous page or a specified URL.

### Step 4: Generate Django Code

We will create a Django application named `material_planning`. The main "entity" for this report view will be conceptualized as `PlanningPrintDetail` to align with the original ASP.NET page name.

#### 4.1 Models (in `material_planning/models.py`)

We will define Django models for each identified database table. These models will have `managed = False` to connect directly to your existing database schema.

```python
from django.db import models

# Inferred Company model (from fun.getCompany, fun.CompAdd, Session["compid"])
class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming CompId maps to Id
    name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True) # Inferred name
    address = models.TextField(db_column='CompanyAddress', blank=True, null=True) # Inferred address

    class Meta:
        managed = False
        db_table = 'CompanyMaster' # Placeholder, actual table name needed
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

    # Business logic methods for company details could be added here
    @classmethod
    def get_company_details(cls, comp_id):
        """Retrieves company name and address by ID."""
        try:
            company = cls.objects.get(id=comp_id)
            return {'name': company.name, 'address': company.address}
        except cls.DoesNotExist:
            return {'name': 'N/A', 'address': 'N/A'}


class MaterialPlanningMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    pl_no = models.CharField(db_column='PLNo', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMP_Material_Master'
        verbose_name = 'Material Planning Master'
        verbose_name_plural = 'Material Planning Masters'

    def __str__(self):
        return f"{self.pl_no} - {self.wo_no}"

class PRMaster(models.Model):
    # Assuming Id is the PK, or that PLNId is unique enough for this context
    # If PLNId is FK and not PK, then a composite PK or auto-incrementing ID would be needed
    # For now, let's assume a conceptual ID or actual primary key in DB
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an actual PK column
    pr_no = models.CharField(db_column='PRNo', max_length=255, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    pln_id = models.IntegerField(db_column='PLNId', blank=True, null=True) # FK to MaterialPlanningMaster

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'Purchase Requisition Master'
        verbose_name_plural = 'Purchase Requisition Masters'

    def __str__(self):
        return self.pr_no or f"PR Master {self.id}"

    @classmethod
    def get_pr_details(cls, pln_id):
        """Retrieves PR No and SysDate for a given PLNId."""
        try:
            pr_entry = cls.objects.filter(pln_id=pln_id).first() # Use .first() as per original logic implies one
            if pr_entry:
                return {'pr_no': pr_entry.pr_no, 'pr_date': pr_entry.sys_date}
            return {'pr_no': '', 'pr_date': ''}
        except Exception: # Broad catch for robustness
            return {'pr_no': '', 'pr_date': ''}


class MaterialPlanningDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.IntegerField(db_column='Mid', blank=True, null=True) # FK to MaterialPlanningMaster
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster
    rm = models.BooleanField(db_column='RM', blank=True, null=True)
    pro = models.BooleanField(db_column='PRO', blank=True, null=True)
    fin = models.BooleanField(db_column='FIN', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMP_Material_Detail'
        verbose_name = 'Material Planning Detail'
        verbose_name_plural = 'Material Planning Details'

    def __str__(self):
        return f"Detail {self.id} for Master {self.mid}"

class MaterialPlanningFinish(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id column exists
    dmid = models.IntegerField(db_column='DMid', blank=True, null=True) # FK to MaterialPlanningDetail
    del_date = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    discount = models.FloatField(db_column='Discount', blank=True, null=True)
    rate = models.FloatField(db_column='Rate', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster
    supplier_id = models.IntegerField(db_column='SupplierId', blank=True, null=True) # FK to SupplierMaster

    class Meta:
        managed = False
        db_table = 'tblMP_Material_Finish'
        verbose_name = 'Material Planning Finish'
        verbose_name_plural = 'Material Planning Finishes'

    def __str__(self):
        return f"Finish {self.id} for Detail {self.dmid}"

class MaterialPlanningRawMaterial(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id column exists
    dmid = models.IntegerField(db_column='DMid', blank=True, null=True) # FK to MaterialPlanningDetail
    del_date = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    discount = models.FloatField(db_column='Discount', blank=True, null=True)
    rate = models.FloatField(db_column='Rate', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    supplier_id = models.IntegerField(db_column='SupplierId', blank=True, null=True) # FK to SupplierMaster
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster

    class Meta:
        managed = False
        db_table = 'tblMP_Material_RawMaterial'
        verbose_name = 'Material Planning Raw Material'
        verbose_name_plural = 'Material Planning Raw Materials'

    def __str__(self):
        return f"Raw Material {self.id} for Detail {self.dmid}"

class MaterialPlanningProcess(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id column exists
    dmid = models.IntegerField(db_column='DMid', blank=True, null=True) # FK to MaterialPlanningDetail
    discount = models.FloatField(db_column='Discount', blank=True, null=True)
    del_date = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    rate = models.FloatField(db_column='Rate', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    supplier_id = models.IntegerField(db_column='SupplierId', blank=True, null=True) # FK to SupplierMaster
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster

    class Meta:
        managed = False
        db_table = 'tblMP_Material_Process'
        verbose_name = 'Material Planning Process'
        verbose_name_plural = 'Material Planning Processes'

    def __str__(self):
        return f"Process {self.id} for Detail {self.dmid}"

class SupplierMaster(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.supplier_name or f"Supplier {self.supplier_id}"


class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    uom_basic = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # FK to UnitMaster
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or f"Item {self.id}"


class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

```

#### 4.2 Forms (Not Applicable for this Report Page)

**Task:** Define a Django form for user input.

**Instructions:**
This page's primary function is to *display* an aggregated report, not to provide user input for creating or updating a single database record. Therefore, a Django `ModelForm` is **not applicable** for this specific conversion. The data is driven by URL parameters, not an editable form.

#### 4.3 Views (in `material_planning/views.py`)

**Task:** Implement the report display logic using a Class-Based View.

**Instructions:**
We will use a `ListView` to display the aggregated report data. The complex data fetching and joining logic (originally in `Page_Init`) will be moved into a dedicated service class (`MaterialPlanningReportService`), adhering to the "fat model, thin view" principle. This keeps the view concise and focused on orchestrating the display.

```python
from django.views.generic import ListView
from django.shortcuts import render
from django.http import HttpResponse
from datetime import datetime
from decimal import Decimal

# Import all models from models.py
from .models import (
    Company, MaterialPlanningMaster, PRMaster, MaterialPlanningDetail,
    MaterialPlanningFinish, MaterialPlanningRawMaterial, MaterialPlanningProcess,
    SupplierMaster, ItemMaster, UnitMaster
)

# Custom utility for date formatting similar to fun.FromDateDMY
def format_date_dmy(date_obj):
    if isinstance(date_obj, datetime):
        return date_obj.strftime('%d/%m/%Y')
    return ''

class MaterialPlanningReportService:
    """
    Service class to encapsulate the complex data aggregation logic
    originally found in the ASP.NET Page_Init method.
    This acts as the 'fat model' for report generation.
    """
    def __init__(self, comp_id, fin_year_id, pl_no, mid, wo_no, request_key):
        self.comp_id = comp_id
        self.fin_year_id = fin_year_id # Not directly used in DB queries for this page, but kept for context
        self.pl_no = pl_no
        self.mid = mid
        self.wo_no = wo_no
        self.request_key = request_key # Placeholder, not used in Django
        self.report_data = []
        self.report_header_info = {
            'company': 'N/A',
            'address': 'N/A',
            'plan_no': pl_no,
            'plan_date': '',
            'wo_no': wo_no,
            'pr_no': '',
            'pr_date': ''
        }

    def _get_pr_details(self):
        """Fetches PRNo and PRDate based on MId."""
        pr_details = PRMaster.get_pr_details(self.mid)
        self.report_header_info['pr_no'] = pr_details['pr_no']
        self.report_header_info['pr_date'] = format_date_dmy(pr_details['pr_date'])

    def _get_company_details(self):
        """Fetches company name and address."""
        company_info = Company.get_company_details(self.comp_id)
        self.report_header_info['company'] = company_info['name']
        self.report_header_info['address'] = company_info['address']

    def _get_item_details(self, item_id):
        """Fetches ItemCode, UOMBasic, ManfDesc for an ItemId."""
        try:
            item = ItemMaster.objects.get(id=item_id)
            unit = UnitMaster.objects.filter(id=item.uom_basic).first()
            return {
                'item_code': item.item_code,
                'uom_symbol': unit.symbol if unit else '',
                'manf_desc': item.manf_desc
            }
        except ItemMaster.DoesNotExist:
            return {'item_code': '', 'uom_symbol': '', 'manf_desc': ''}

    def _get_supplier_details(self, supplier_id):
        """Fetches SupplierName for a SupplierId."""
        try:
            supplier = SupplierMaster.objects.get(supplier_id=supplier_id)
            return f"{supplier.supplier_name}[{supplier.supplier_id}]"
        except SupplierMaster.DoesNotExist:
            return ''

    def _get_bom_qty(self, comp_id, wono, item_id, fin_year_id):
        """
        Placeholder for AllComponentBOMQty.
        This would be a complex business logic function, likely involving
        recursive BOM structure traversal or a pre-calculated table.
        For demonstration, we'll return a dummy value.
        """
        # In a real system, this would query BOM tables
        # Example: return BOMComponent.objects.filter(comp_id=comp_id, ...).aggregate(Sum('qty'))['qty__sum']
        return 1.0 # Placeholder for actual BOM calculation


    def generate_report_data(self):
        """
        Aggregates data based on the logic in the original Page_Init.
        """
        self._get_company_details()
        self._get_pr_details()

        # Step 1: Get master planning details
        master_entries = MaterialPlanningMaster.objects.filter(
            comp_id=self.comp_id,
            pl_no=self.pl_no,
            wo_no=self.wo_no,
            id=self.mid # Directly filtering by MId as per the main query
        )

        if not master_entries.exists():
            return [] # No master entries found

        # Take the first master entry sys_date as PLDate
        master_entry = master_entries.first()
        self.report_header_info['plan_date'] = format_date_dmy(master_entry.sys_date)


        # Iterate through MaterialPlanningDetail entries linked to this master
        detail_entries = MaterialPlanningDetail.objects.filter(mid=master_entry.id)

        for detail in detail_entries:
            pln_type = ""
            source_queryset = None

            if detail.fin:
                source_queryset = MaterialPlanningFinish.objects.filter(dmid=detail.id)
                pln_type = "Finish"
            elif detail.rm:
                source_queryset = MaterialPlanningRawMaterial.objects.filter(dmid=detail.id)
                pln_type = "RM"
            elif detail.pro:
                source_queryset = MaterialPlanningProcess.objects.filter(dmid=detail.id)
                pln_type = "Process"

            if source_queryset:
                for src_item in source_queryset:
                    row = {}
                    row['PLDate'] = self.report_header_info['plan_date']
                    row['PLNo'] = self.pl_no
                    row['FinishQty'] = float(src_item.qty) if src_item.qty is not None else 0.0
                    row['Rate'] = float(src_item.rate) if src_item.rate is not None else 0.0
                    row['Discount'] = float(src_item.discount) if src_item.discount is not None else 0.0
                    row['CompId'] = self.comp_id
                    row['PLNType'] = pln_type
                    row['DelDateFinish'] = format_date_dmy(src_item.del_date)

                    # Item details for the specific (RM/PRO/FIN) item
                    item_details = self._get_item_details(src_item.item_id)
                    supplier_name = self._get_supplier_details(src_item.supplier_id)

                    row['ItemCodeRMPR'] = item_details['item_code'] if (supplier_name and src_item.del_date) else ""
                    row['ManfDesc'] = item_details['manf_desc'] if (supplier_name and src_item.del_date) else ""
                    row['Symbol'] = item_details['uom_symbol'] if (supplier_name and src_item.del_date) else ""
                    row['SupplierName'] = supplier_name

                    # Item details for the *detail* item (Expr1 in original query)
                    # This corresponds to the primary item being planned for
                    detail_item_details = self._get_item_details(detail.item_id)
                    row['ItemCode'] = detail_item_details['item_code']

                    # BOM Qty
                    row['BOMQty'] = self._get_bom_qty(self.comp_id, self.wo_no, detail.item_id, self.fin_year_id)

                    self.report_data.append(row)
        return self.report_data


class PlanningPrintDetailListView(ListView):
    """
    Displays the aggregated material planning report.
    This view orchestrates fetching data from the service.
    """
    model = MaterialPlanningMaster # A nominal model, as actual data is aggregated
    template_name = 'material_planning/planningprintdetail/list.html'
    context_object_name = 'report_data' # Name for the list of dictionaries

    def get_queryset(self):
        """
        Fetches the aggregated report data using the service.
        This method replaces the Page_Init logic.
        """
        # Extract query parameters (simulating Request.QueryString)
        comp_id = self.request.session.get('compid', 0) # From session
        fin_year_id = self.request.GET.get('FinYearId', 0)
        pl_no = self.request.GET.get('plno', '')
        mid = self.request.GET.get('MId', '')
        wo_no = self.request.GET.get('WONo', '')
        request_key = self.request.GET.get('Key', '') # Not used in Django equivalent

        # Convert MId to int for filtering, handle potential errors
        try:
            mid = int(mid)
        except (ValueError, TypeError):
            mid = 0 # Default or error handling

        # Instantiate the report service and generate data
        service = MaterialPlanningReportService(
            comp_id, fin_year_id, pl_no, mid, wo_no, request_key
        )
        report_items = service.generate_report_data()
        self.report_header_info = service.report_header_info
        return report_items

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add header information to the context
        context['header_info'] = self.report_header_info
        return context

    # HTMX partial view for the table content
    def get(self, request, *args, **kwargs):
        if request.headers.get('HX-Request'):
            # Only return the partial for HTMX requests
            self.object_list = self.get_queryset() # Populate self.object_list
            context = self.get_context_data()
            return render(request, 'material_planning/planningprintdetail/_report_table.html', context)
        return super().get(request, *args, **kwargs)

# There are no CreateView, UpdateView, DeleteView for this conceptual report object.
# The page is for display/print only.
```

#### 4.4 Templates

**Task:** Create templates for the report view.

**Instructions:**
*   `list.html`: The main page template that sets up the overall structure and includes the HTMX-loaded partial for the report table.
*   `_report_table.html`: A partial template containing the DataTables structure and the actual report data loop. This will be loaded dynamically by HTMX.
*   **Note:** `form.html` and `confirm_delete.html` are **not applicable** to this specific report viewing page as it does not involve CRUD operations on the displayed data.

**File: `material_planning/templates/material_planning/planningprintdetail/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">BOM Material Planning - Print Details</h2>
        <a href="{% url 'planning_print_cancel' %}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    <!-- Report Header Info -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">{{ header_info.company }}</h3>
        <p class="text-gray-600">{{ header_info.address }}</p>
        <div class="grid grid-cols-2 gap-4 mt-4 text-sm text-gray-700">
            <div><strong>Plan No:</strong> {{ header_info.plan_no }}</div>
            <div><strong>Plan Date:</strong> {{ header_info.plan_date }}</div>
            <div><strong>WO No:</strong> {{ header_info.wo_no }}</div>
            <div><strong>PR No:</strong> {{ header_info.pr_no }}</div>
            <div><strong>PR Date:</strong> {{ header_info.pr_date }}</div>
        </div>
    </div>
    
    <div id="planningReportTable-container"
         hx-trigger="load"
         hx-get="{% url 'planning_print_detail_table' %}?plno={{ request.GET.plno|default:'' }}&MId={{ request.GET.MId|default:'' }}&WONo={{ request.GET.WONo|default:'' }}&FinYearId={{ request.GET.FinYearId|default:'' }}&Key={{ request.GET.Key|default:'' }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Modal for future use (e.g., if we add print to PDF options) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management (e.g., modal visibility)
    });
</script>
{% endblock %}
```

**File: `material_planning/templates/material_planning/planningprintdetail/_report_table.html`**
```html
<div class="bg-white shadow-md rounded-lg overflow-x-auto p-4">
    <table id="planningPrintDetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PL No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code (Master)</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf. Desc.</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code (Detail)</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for row in report_data %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.PLDate }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.PLNo }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.ItemCode }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.Symbol }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.ManfDesc }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.SupplierName }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.DelDateFinish }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ row.FinishQty|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ row.Rate|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.ItemCodeRMPR }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ row.BOMQty|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ row.PLNType }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-right">{{ row.Discount|floatformat:2 }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="14" class="py-4 px-6 text-center text-gray-500">No data available for this report.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    // Only initialize DataTable if the table exists and hasn't been initialized
    if (!$.fn.DataTable.isDataTable('#planningPrintDetailTable')) {
        $('#planningPrintDetailTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true // Add responsiveness
        });
    }
});
</script>
```

#### 4.5 URLs (in `material_planning/urls.py`)

**Task:** Define URL patterns for the report view.

**Instructions:**
We will define a URL for the main report page and a separate URL for the HTMX-loaded table partial.

```python
from django.urls import path
from .views import PlanningPrintDetailListView

urlpatterns = [
    # Main report view
    path('planning/print/details/', PlanningPrintDetailListView.as_view(), name='planning_print_detail'),
    # HTMX endpoint for the table partial, includes query parameters for data fetching
    path('planning/print/details/table/', PlanningPrintDetailListView.as_view(), name='planning_print_detail_table'),
    # URL for the Cancel button redirect
    path('planning/print/cancel/', lambda request: HttpResponse(status=204, headers={'HX-Redirect': '/module/materialplanning/transactions/planning_print/?ModId=4&SubModId=33'}), name='planning_print_cancel'),
    # Note: The 'HX-Redirect' header ensures a full page redirect if HTMX initiated,
    # otherwise a standard redirect occurs. The specific URL path for the redirect
    # might need to be adjusted based on your overall Django URL structure.
]
```

#### 4.6 Tests (in `material_planning/tests.py`)

**Task:** Write tests for the models and the report generation service/view.

**Instructions:**
We will include unit tests for the utility functions and methods within the service, and integration tests for the `PlanningPrintDetailListView` to ensure it correctly fetches and renders data based on various parameters.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from unittest.mock import patch, MagicMock

# Import all models and the service class
from .models import (
    Company, MaterialPlanningMaster, PRMaster, MaterialPlanningDetail,
    MaterialPlanningFinish, MaterialPlanningRawMaterial, MaterialPlanningProcess,
    SupplierMaster, ItemMaster, UnitMaster
)
from .views import MaterialPlanningReportService, format_date_dmy

class UtilsTest(TestCase):
    def test_format_date_dmy(self):
        self.assertEqual(format_date_dmy(datetime(2023, 10, 26)), '26/10/2023')
        self.assertEqual(format_date_dmy(None), '')
        self.assertEqual(format_date_dmy("not a date"), '') # Should handle non-datetime gracefully

class MaterialPlanningReportServiceTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy data for testing the service
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St')
        cls.master = MaterialPlanningMaster.objects.create(
            id=101, sys_date=datetime(2023, 1, 15), wo_no='WO001', comp_id=1, pl_no='PLN001'
        )
        cls.pr_master = PRMaster.objects.create(
            id=201, pr_no='PR001', sys_date=datetime(2023, 1, 10), pln_id=cls.master.id
        )
        cls.item_master_detail = ItemMaster.objects.create(id=301, item_code='ITEM-A', uom_basic=1, manf_desc='Desc A')
        cls.item_master_rm = ItemMaster.objects.create(id=302, item_code='ITEM-B', uom_basic=1, manf_desc='Desc B')
        cls.unit_master = UnitMaster.objects.create(id=1, symbol='KG')
        cls.supplier = SupplierMaster.objects.create(supplier_id=401, supplier_name='Test Supplier')

        cls.detail_fin = MaterialPlanningDetail.objects.create(
            id=501, mid=cls.master.id, item_id=cls.item_master_detail.id, rm=False, pro=False, fin=True
        )
        cls.finish_item = MaterialPlanningFinish.objects.create(
            id=601, dmid=cls.detail_fin.id, del_date=datetime(2023, 2, 1), discount=5.0, rate=10.0, qty=100.0,
            item_id=cls.item_master_rm.id, supplier_id=cls.supplier.supplier_id
        )

        cls.detail_rm = MaterialPlanningDetail.objects.create(
            id=502, mid=cls.master.id, item_id=cls.item_master_detail.id, rm=True, pro=False, fin=False
        )
        cls.raw_material_item = MaterialPlanningRawMaterial.objects.create(
            id=701, dmid=cls.detail_rm.id, del_date=datetime(2023, 2, 5), discount=2.0, rate=5.0, qty=50.0,
            item_id=cls.item_master_rm.id, supplier_id=cls.supplier.supplier_id
        )


    @patch('material_planning.views.MaterialPlanningReportService._get_bom_qty', return_value=5.5)
    def test_generate_report_data(self, mock_get_bom_qty):
        service = MaterialPlanningReportService(
            comp_id=self.company.id,
            fin_year_id=2023,
            pl_no=self.master.pl_no,
            mid=self.master.id,
            wo_no=self.master.wo_no,
            request_key='testkey'
        )
        report_data = service.generate_report_data()

        self.assertGreater(len(report_data), 0)
        self.assertEqual(len(report_data), 2) # Expecting 2 rows based on our setup (finish and raw material)

        # Test first row (finish item)
        row1 = report_data[0]
        self.assertEqual(row1['PLNo'], 'PLN001')
        self.assertEqual(row1['FinishQty'], 100.0)
        self.assertEqual(row1['Rate'], 10.0)
        self.assertEqual(row1['PLNType'], 'Finish')
        self.assertEqual(row1['ItemCodeRMPR'], 'ITEM-B')
        self.assertEqual(row1['ManfDesc'], 'Desc B')
        self.assertEqual(row1['Symbol'], 'KG')
        self.assertEqual(row1['SupplierName'], 'Test Supplier[401]')
        self.assertEqual(row1['ItemCode'], 'ITEM-A') # ItemCode for the detail item (Expr1)
        self.assertEqual(row1['BOMQty'], 5.5)
        self.assertEqual(row1['Discount'], 5.0)

        # Test second row (raw material item)
        row2 = report_data[1]
        self.assertEqual(row2['PLNType'], 'RM')
        self.assertEqual(row2['FinishQty'], 50.0)
        self.assertEqual(row2['Rate'], 5.0)
        self.assertEqual(row2['ItemCodeRMPR'], 'ITEM-B')
        self.assertEqual(row2['Discount'], 2.0)

        # Test header info
        self.assertEqual(service.report_header_info['company'], 'Test Company')
        self.assertEqual(service.report_header_info['pr_no'], 'PR001')
        self.assertEqual(service.report_header_info['plan_date'], '15/01/2023')

class PlanningPrintDetailListViewTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal data required for views to run
        cls.company = Company.objects.create(id=1, name='Test Company', address='123 Test St')
        cls.master = MaterialPlanningMaster.objects.create(
            id=101, sys_date=datetime(2023, 1, 15), wo_no='WO001', comp_id=1, pl_no='PLN001'
        )
        cls.pr_master = PRMaster.objects.create(
            id=201, pr_no='PR001', sys_date=datetime(2023, 1, 10), pln_id=cls.master.id
        )
        cls.item_master_detail = ItemMaster.objects.create(id=301, item_code='ITEM-A', uom_basic=1, manf_desc='Desc A')
        cls.item_master_rm = ItemMaster.objects.create(id=302, item_code='ITEM-B', uom_basic=1, manf_desc='Desc B')
        cls.unit_master = UnitMaster.objects.create(id=1, symbol='KG')
        cls.supplier = SupplierMaster.objects.create(supplier_id=401, supplier_name='Test Supplier')

        cls.detail_fin = MaterialPlanningDetail.objects.create(
            id=501, mid=cls.master.id, item_id=cls.item_master_detail.id, rm=False, pro=False, fin=True
        )
        cls.finish_item = MaterialPlanningFinish.objects.create(
            id=601, dmid=cls.detail_fin.id, del_date=datetime(2023, 2, 1), discount=5.0, rate=10.0, qty=100.0,
            item_id=cls.item_master_rm.id, supplier_id=cls.supplier.supplier_id
        )
        cls.detail_rm = MaterialPlanningDetail.objects.create(
            id=502, mid=cls.master.id, item_id=cls.item_master_detail.id, rm=True, pro=False, fin=False
        )
        cls.raw_material_item = MaterialPlanningRawMaterial.objects.create(
            id=701, dmid=cls.detail_rm.id, del_date=datetime(2023, 2, 5), discount=2.0, rate=5.0, qty=50.0,
            item_id=cls.item_master_rm.id, supplier_id=cls.supplier.supplier_id
        )

    def setUp(self):
        self.client = Client()
        # Mock session compid, as it's typically set by a login process
        self.client.session['compid'] = self.company.id

    @patch('material_planning.views.MaterialPlanningReportService.generate_report_data', return_value=[
        {'PLDate': '15/01/2023', 'PLNo': 'PLN001', 'ItemCode': 'ITEM-A', 'Symbol': 'KG', 'ManfDesc': 'Desc B', 'SupplierName': 'Test Supplier[401]', 'DelDateFinish': '01/02/2023', 'CompId': 1, 'FinishQty': 100.0, 'Rate': 10.0, 'ItemCodeRMPR': 'ITEM-B', 'BOMQty': 5.5, 'PLNType': 'Finish', 'Discount': 5.0}
    ])
    def test_list_view_get(self, mock_generate_report_data):
        url = reverse('planning_print_detail')
        response = self.client.get(url, {
            'plno': 'PLN001',
            'MId': self.master.id,
            'WONo': 'WO001',
            'FinYearId': 2023,
            'Key': 'testkey'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/planningprintdetail/list.html')
        self.assertIn('report_data', response.context)
        self.assertIn('header_info', response.context)
        self.assertEqual(len(response.context['report_data']), 1)
        mock_generate_report_data.assert_called_once()

    @patch('material_planning.views.MaterialPlanningReportService.generate_report_data', return_value=[
        {'PLDate': '15/01/2023', 'PLNo': 'PLN001', 'ItemCode': 'ITEM-A', 'Symbol': 'KG', 'ManfDesc': 'Desc B', 'SupplierName': 'Test Supplier[401]', 'DelDateFinish': '01/02/2023', 'CompId': 1, 'FinishQty': 100.0, 'Rate': 10.0, 'ItemCodeRMPR': 'ITEM-B', 'BOMQty': 5.5, 'PLNType': 'Finish', 'Discount': 5.0}
    ])
    def test_list_view_htmx_get(self, mock_generate_report_data):
        url = reverse('planning_print_detail_table')
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, {
            'plno': 'PLN001',
            'MId': self.master.id,
            'WONo': 'WO001',
            'FinYearId': 2023,
            'Key': 'testkey'
        }, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/planningprintdetail/_report_table.html')
        self.assertIn('report_data', response.context)
        self.assertIn('header_info', response.context) # Header info is also passed to partial template
        self.assertEqual(len(response.context['report_data']), 1)
        mock_generate_report_data.assert_called_once()
        self.assertContains(response, 'ITEM-A')
        self.assertContains(response, 'DataTables') # Verify DataTables script is referenced

    def test_cancel_button_redirect(self):
        url = reverse('planning_print_cancel')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 204) # HTTP 204 No Content for HTMX redirects
        self.assertEqual(response.headers['HX-Redirect'], '/module/materialplanning/transactions/planning_print/?ModId=4&SubModId=33')

        # Test non-HTMX request (optional, but good for robustness)
        response_normal = self.client.get(url, HTTP_ACCEPT='text/html')
        self.assertEqual(response_normal.status_code, 204) # Still 204 as it's an HX-Redirect helper
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX:** The main `list.html` uses `hx-get` and `hx-trigger="load"` to fetch the `_report_table.html` partial. This ensures the heavy data table is loaded dynamically after the initial page render.
*   **DataTables:** The `_report_table.html` partial includes the JavaScript to initialize DataTables on the rendered table. This provides out-of-the-box searching, sorting, and pagination.
*   **Alpine.js:** While not strictly necessary for this read-only report view, the structure is provided in `list.html` within the `extra_js` block. Alpine.js would be used for client-side UI states, such as managing modal visibility (if a "Print to PDF" modal were added) or dynamic form elements in a CRUD scenario. For this report view, it's primarily a placeholder adhering to the tech stack preference.
*   **No custom JavaScript:** All dynamic interactions are handled via HTMX attributes and DataTables initialization, minimizing the need for bespoke JavaScript code.

### Final Notes

*   **Placeholders:** Ensure you replace placeholder table names (e.g., `CompanyMaster`) with your actual database table names if they differ from the inferred ones.
*   **Data Type Mapping:** Verify Django field types match your SQL Server column types precisely. For example, `Int32` in C# maps to `models.IntegerField`, `double` to `models.FloatField`, `datetime` to `models.DateTimeField`, `string` to `models.CharField` or `models.TextField`.
*   **Date Formatting:** The `format_date_dmy` helper mirrors the original ASP.NET `fun.FromDateDMY` behavior for consistent date display.
*   **BOM Quantity Logic:** The `_get_bom_qty` method in `MaterialPlanningReportService` is a placeholder. You will need to implement the actual logic for `AllComponentBOMQty` based on its source code, likely involving complex recursive queries or pre-calculated data.
*   **Error Handling:** The provided code includes basic `try-except` blocks. Enhance these with specific exception handling and logging for production readiness.
*   **User Authentication:** The `CompId` is retrieved from `request.session.get('compid', 0)`. Ensure your Django authentication system correctly populates this session variable after user login.
*   **Business Value:** This migration transforms a legacy Crystal Report viewer into a modern, interactive web page. This enhances user experience with instant filtering/sorting capabilities, reduces server load by offloading UI logic to the client, and makes the application more maintainable and extensible. The systematic, automated approach minimizes manual effort and ensures a high-quality transition.