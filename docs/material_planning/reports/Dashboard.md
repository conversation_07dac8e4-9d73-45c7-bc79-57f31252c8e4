## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Given the empty `Page_Load` method and lack of direct database interaction or UI controls in the provided ASP.NET code, we will infer a common data structure for a "Material Planning Dashboard." This allows us to demonstrate the full migration process for a typical CRUD component. For the purpose of this modernization plan, we'll assume the dashboard displays a list of 'Material Plans'.

- **Inferred Table Name:** `material_plans`
- **Inferred Column Names:**
    - `id` (Primary Key, auto-incremented)
    - `plan_name` (e.g., "Q3 Production Plan 2024")
    - `status` (e.g., "Approved", "Pending", "Rejected")
    - `start_date`
    - `end_date`
    - `budget_allocated`

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Since the provided ASP.NET code-behind is empty, no explicit CRUD operations are defined within this specific file. However, a dashboard typically allows for viewing, and potentially adding, editing, or deleting underlying data. For a "Material Planning Dashboard," the inferred functionality will include:

-   **Read (R):** Displaying a list of existing material plans with their details (e.g., `plan_name`, `status`, `start_date`, `end_date`, `budget_allocated`).
-   **Create (C):** Ability to add new material plans through a form.
-   **Update (U):** Ability to edit existing material plan details through a form.
-   **Delete (D):** Ability to remove material plans from the system.
-   **Validation:** Basic field validations (e.g., required fields, date formats) for create/update operations.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET `.aspx` file primarily defines content placeholders and includes a `loadingNotifier.js` script, but no direct UI controls like `GridView` or `TextBox` are present within the `MainContent` placeholder. For a typical dashboard and based on the inferred backend functionality, we'll assume the following UI components would be present or desired:

-   **Data List Display:** A tabular display (akin to a `GridView`) showing material plans, with columns for `plan_name`, `status`, `start_date`, `end_date`, `budget_allocated`, and an 'Actions' column for 'Edit' and 'Delete' buttons. This will be replaced by a DataTables-powered HTML table.
-   **Form for Input:** Input fields (similar to `TextBox`, `CalendarExtender` for dates, `DropDownList` for status) for creating and updating material plans. These will be rendered as standard HTML form elements styled with Tailwind CSS.
-   **Action Buttons:** Buttons (like `Button` or `LinkButton`) for triggering 'Add New Plan', 'Edit', 'Delete', 'Save', and 'Cancel' actions. These will be implemented using HTMX attributes to trigger dynamic content loading in modals.
-   **Client-Side Notifications:** The `loadingNotifier.js` suggests client-side feedback during operations. This will be handled gracefully by HTMX's inherent loading states and Alpine.js for modal management.

## Step 4: Generate Django Code

### 4.1 Models (`material_planning/models.py`)

Task: Create a Django model based on the database schema.

## Instructions:

The `MaterialPlan` model will map to the existing `material_plans` table. It will include business logic methods to demonstrate the "fat model" approach.

```python
from django.db import models
from django.utils import timezone

class MaterialPlan(models.Model):
    id = models.AutoField(db_column='id', primary_key=True)
    plan_name = models.CharField(db_column='plan_name', max_length=255, verbose_name="Plan Name")
    status = models.CharField(db_column='status', max_length=50, default='Pending', verbose_name="Status")
    start_date = models.DateField(db_column='start_date', verbose_name="Start Date")
    end_date = models.DateField(db_column='end_date', verbose_name="End Date")
    budget_allocated = models.DecimalField(db_column='budget_allocated', max_digits=10, decimal_places=2, null=True, blank=True, verbose_name="Budget Allocated")

    class Meta:
        managed = False  # Set to True if Django should manage table creation
        db_table = 'material_plans'
        verbose_name = 'Material Plan'
        verbose_name_plural = 'Material Plans'
        ordering = ['-start_date', 'plan_name']

    def __str__(self):
        return f"{self.plan_name} ({self.status})"
        
    def is_active(self):
        """
        Business logic: Checks if the plan's end date is in the future or today.
        """
        return self.end_date >= timezone.now().date()
        
    def get_status_color(self):
        """
        Business logic: Returns a Tailwind CSS color class based on the plan's status.
        """
        if self.status == 'Approved':
            return 'bg-green-100 text-green-800'
        elif self.status == 'Pending':
            return 'bg-yellow-100 text-yellow-800'
        elif self.status == 'Rejected':
            return 'bg-red-100 text-red-800'
        else:
            return 'bg-gray-100 text-gray-800'
            
    def update_plan_status(self, new_status):
        """
        Business logic: Updates the plan's status.
        """
        valid_statuses = ['Approved', 'Pending', 'Rejected', 'Completed']
        if new_status in valid_statuses:
            self.status = new_status
            self.save()
            return True
        return False
```

### 4.2 Forms (`material_planning/forms.py`)

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for the `MaterialPlan` model, including fields for user input and basic validation.

```python
from django import forms
from .models import MaterialPlan

class MaterialPlanForm(forms.ModelForm):
    class Meta:
        model = MaterialPlan
        fields = ['plan_name', 'status', 'start_date', 'end_date', 'budget_allocated']
        widgets = {
            'plan_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., Q3 Production Plan'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}, 
                                   choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected'), ('Completed', 'Completed')]),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'budget_allocated': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., 10000.00'}),
        }
        
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and start_date > end_date:
            self.add_error('end_date', "End date cannot be before start date.")
        return cleaned_data
        
    def clean_plan_name(self):
        plan_name = self.cleaned_data['plan_name']
        # Example custom validation: Plan name must be unique (case-insensitive)
        # Exclude current instance for update operations
        query = MaterialPlan.objects.filter(plan_name__iexact=plan_name)
        if self.instance.pk:
            query = query.exclude(pk=self.instance.pk)
        if query.exists():
            raise forms.ValidationError("A plan with this name already exists.")
        return plan_name
```

### 4.3 Views (`material_planning/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

Views will be kept thin, delegating business logic to the `MaterialPlan` model. HTMX headers will be used to trigger frontend updates.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialPlan
from .forms import MaterialPlanForm

# Thin view for listing material plans
class MaterialPlanListView(ListView):
    model = MaterialPlan
    template_name = 'material_planning/materialplan/list.html'
    context_object_name = 'materialplans'

# Thin view for creating material plans
class MaterialPlanCreateView(CreateView):
    model = MaterialPlan
    form_class = MaterialPlanForm
    template_name = 'material_planning/materialplan/_materialplan_form.html' # Rendered as a partial for modal
    success_url = reverse_lazy('materialplan_list') # Not directly used for HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Plan added successfully.')
        # HTMX-specific response: return 204 No Content with HX-Trigger to refresh list
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialPlanList":true, "closeModal":true}' 
                }
            )
        return response
    
    def form_invalid(self, form):
        # HTMX-specific response: re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


# Thin view for updating material plans
class MaterialPlanUpdateView(UpdateView):
    model = MaterialPlan
    form_class = MaterialPlanForm
    template_name = 'material_planning/materialplan/_materialplan_form.html' # Rendered as a partial for modal
    success_url = reverse_lazy('materialplan_list') # Not directly used for HTMX, but good practice

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Plan updated successfully.')
        # HTMX-specific response: return 204 No Content with HX-Trigger to refresh list
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialPlanList":true, "closeModal":true}'
                }
            )
        return response

    def form_invalid(self, form):
        # HTMX-specific response: re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


# Thin view for deleting material plans
class MaterialPlanDeleteView(DeleteView):
    model = MaterialPlan
    template_name = 'material_planning/materialplan/_materialplan_confirm_delete.html' # Rendered as a partial for modal
    success_url = reverse_lazy('materialplan_list') # Not directly used for HTMX, but good practice

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Plan deleted successfully.')
        # HTMX-specific response: return 204 No Content with HX-Trigger to refresh list
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialPlanList":true, "closeModal":true}'
                }
            )
        return response

# View to render only the DataTable part, used by HTMX
class MaterialPlanTablePartialView(ListView):
    model = MaterialPlan
    template_name = 'material_planning/materialplan/_materialplan_table.html'
    context_object_name = 'materialplans' # This will be the list of objects for the template
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will use Django's templating language, extend `core/base.html`, and incorporate HTMX attributes and DataTables for dynamic interactions.

**`material_planning/templates/material_planning/materialplan/list.html`**
This is the main page that loads the DataTables content dynamically and manages the modal for forms.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Plans</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200 ease-in-out"
            hx-get="{% url 'materialplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Material Plan
        </button>
    </div>
    
    <div id="materialplanTable-container"
         hx-trigger="load, refreshMaterialPlanList from:body"
         hx-get="{% url 'materialplan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex justify-center items-center h-48">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading material plans...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden"
         _="on closeModal remove .is-active from me
            on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-auto"
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-90"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-90"
             _="on load add .is-active to #modal then add .is-active to me">
            <!-- Form/Confirm Delete content loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is primarily used for modal visibility.
    // The 'on closeModal' trigger will be sent by HTMX.
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            // Activate modal only if the content is successfully loaded into it
            document.getElementById('modal').classList.add('is-active');
        }
    });

    document.addEventListener('DOMContentLoaded', () => {
        // Event listener for global triggers for modal close
        document.body.addEventListener('closeModal', () => {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modalContent').innerHTML = ''; // Clear modal content
        });
    });
</script>
{% endblock %}
```

**`material_planning/templates/material_planning/materialplan/_materialplan_table.html`**
This partial template contains the DataTables structure and is loaded dynamically by HTMX.

```html
<div class="overflow-x-auto bg-white shadow-lg rounded-lg">
    <table id="materialplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in materialplans %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium text-blue-600">{{ obj.plan_name }}</td>
                <td class="py-3 px-6 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ obj.get_status_color }}">
                        {{ obj.status }}
                    </span>
                </td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.start_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.end_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm text-gray-500">${{ obj.budget_allocated|default:"0.00"|floatformat:2 }}</td>
                <td class="py-3 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'materialplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded transition duration-150 ease-in-out"
                        hx-get="{% url 'materialplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#materialplanTable')) {
            $('#materialplanTable').DataTable().destroy(); // Destroy existing instance if any
        }
        $('#materialplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": -1 } // Disable sorting on 'Actions' column
            ]
        });
    });
</script>
```

**`material_planning/templates/material_planning/materialplan/_materialplan_form.html`**
This partial template is rendered inside the modal for both create and update operations.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Material Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="relative">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                    <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}

            {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ form.non_field_errors }}</span>
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Material Plan
            </button>
        </div>
    </form>
</div>
```

**`material_planning/templates/material_planning/materialplan/_materialplan_confirm_delete.html`**
This partial template is rendered inside the modal for delete confirmation.

```html
<div class="p-6 text-center">
    <svg class="mx-auto h-16 w-16 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
    <h3 class="mt-5 text-xl font-semibold text-gray-900">Delete Material Plan "{{ object.plan_name }}"?</h3>
    <p class="mt-2 text-sm text-gray-500">Are you sure you want to delete this material plan? This action cannot be undone.</p>
    
    <div class="mt-6 flex justify-center space-x-4">
        <button 
            type="button" 
            class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            _="on click trigger closeModal from body">
            Cancel
        </button>
        <button 
            type="button" 
            hx-post="{% url 'materialplan_delete' object.pk %}" 
            hx-swap="none"
            class="inline-flex justify-center py-2 px-5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Delete
        </button>
    </div>
</div>
```

### 4.5 URLs (`material_planning/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

URL patterns will be created for listing, adding, editing, and deleting material plans, as well as a specific endpoint for the HTMX-driven table partial.

```python
from django.urls import path
from .views import (
    MaterialPlanListView, 
    MaterialPlanCreateView, 
    MaterialPlanUpdateView, 
    MaterialPlanDeleteView,
    MaterialPlanTablePartialView
)

urlpatterns = [
    path('materialplans/', MaterialPlanListView.as_view(), name='materialplan_list'),
    path('materialplans/add/', MaterialPlanCreateView.as_view(), name='materialplan_add'),
    path('materialplans/edit/<int:pk>/', MaterialPlanUpdateView.as_view(), name='materialplan_edit'),
    path('materialplans/delete/<int:pk>/', MaterialPlanDeleteView.as_view(), name='materialplan_delete'),
    # HTMX-specific endpoint for refreshing the table content
    path('materialplans/table/', MaterialPlanTablePartialView.as_view(), name='materialplan_table'),
]
```

### 4.6 Tests (`material_planning/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests will cover the `MaterialPlan` model's fields and business logic, while integration tests will validate the functionality of the views, including HTMX interactions and success messages.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from datetime import date, timedelta
from decimal import Decimal
from .models import MaterialPlan

class MaterialPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        MaterialPlan.objects.create(
            plan_name='Test Plan 1',
            status='Pending',
            start_date=date(2024, 1, 1),
            end_date=date(2024, 12, 31),
            budget_allocated=Decimal('15000.00')
        )
        MaterialPlan.objects.create(
            plan_name='Completed Plan',
            status='Completed',
            start_date=date(2023, 1, 1),
            end_date=date(2023, 12, 31),
            budget_allocated=Decimal('5000.00')
        )
  
    def test_material_plan_creation(self):
        obj = MaterialPlan.objects.get(plan_name='Test Plan 1')
        self.assertEqual(obj.plan_name, 'Test Plan 1')
        self.assertEqual(obj.status, 'Pending')
        self.assertEqual(obj.start_date, date(2024, 1, 1))
        self.assertEqual(obj.end_date, date(2024, 12, 31))
        self.assertEqual(obj.budget_allocated, Decimal('15000.00'))
        
    def test_str_method(self):
        obj = MaterialPlan.objects.get(plan_name='Test Plan 1')
        self.assertEqual(str(obj), 'Test Plan 1 (Pending)')
        
    def test_is_active_method(self):
        # Test active plan
        obj = MaterialPlan.objects.get(plan_name='Test Plan 1')
        self.assertTrue(obj.is_active())
        
        # Test inactive plan (end date in past)
        inactive_plan = MaterialPlan.objects.create(
            plan_name='Past Plan',
            status='Completed',
            start_date=date(2023, 1, 1),
            end_date=date(2023, 1, 1),
            budget_allocated=Decimal('100.00')
        )
        self.assertFalse(inactive_plan.is_active())

        # Test plan ending today
        today_plan = MaterialPlan.objects.create(
            plan_name='Today Plan',
            status='Pending',
            start_date=date.today(),
            end_date=date.today(),
            budget_allocated=Decimal('100.00')
        )
        self.assertTrue(today_plan.is_active())

    def test_get_status_color_method(self):
        pending_plan = MaterialPlan.objects.get(plan_name='Test Plan 1')
        self.assertEqual(pending_plan.get_status_color(), 'bg-yellow-100 text-yellow-800')
        
        approved_plan = MaterialPlan.objects.create(
            plan_name='Approved Plan', status='Approved', start_date=date(2024,1,1), end_date=date(2024,12,31)
        )
        self.assertEqual(approved_plan.get_status_color(), 'bg-green-100 text-green-800')

        rejected_plan = MaterialPlan.objects.create(
            plan_name='Rejected Plan', status='Rejected', start_date=date(2024,1,1), end_date=date(2024,12,31)
        )
        self.assertEqual(rejected_plan.get_status_color(), 'bg-red-100 text-red-800')

    def test_update_plan_status_method(self):
        plan = MaterialPlan.objects.get(plan_name='Test Plan 1')
        self.assertTrue(plan.update_plan_status('Approved'))
        self.assertEqual(plan.status, 'Approved')
        
        self.assertFalse(plan.update_plan_status('Invalid Status'))
        self.assertEqual(plan.status, 'Approved') # Status should not change

class MaterialPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.material_plan = MaterialPlan.objects.create(
            plan_name='Initial Plan',
            status='Pending',
            start_date=date(2024, 6, 1),
            end_date=date(2024, 7, 31),
            budget_allocated=Decimal('20000.00')
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('materialplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplan/list.html')
        self.assertIn('materialplans', response.context)
        self.assertContains(response, self.material_plan.plan_name)
        
    def test_table_partial_view(self):
        response = self.client.get(reverse('materialplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplan/_materialplan_table.html')
        self.assertIn('materialplans', response.context)
        self.assertContains(response, self.material_plan.plan_name)
        # Ensure it renders only the table part, not full HTML
        self.assertNotContains(response, '<!DOCTYPE html>')

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplan_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplan/_materialplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Material Plan') # Check modal title
        
    def test_create_view_post_success_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'plan_name': 'New HTMX Plan',
            'status': 'Approved',
            'start_date': (date.today() + timedelta(days=10)).isoformat(),
            'end_date': (date.today() + timedelta(days=20)).isoformat(),
            'budget_allocated': '5000.00',
        }
        response = self.client.post(reverse('materialplan_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertTrue(MaterialPlan.objects.filter(plan_name='New HTMX Plan').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialPlanList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Material Plan added successfully.')

    def test_create_view_post_invalid_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'plan_name': '', # Invalid: required field
            'status': 'Pending',
            'start_date': '2024-07-01',
            'end_date': '2024-06-01', # Invalid: end date before start date
        }
        response = self.client.post(reverse('materialplan_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'material_planning/materialplan/_materialplan_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End date cannot be before start date.')
        self.assertFalse(MaterialPlan.objects.filter(plan_name='').exists())


    def test_update_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplan_edit', args=[self.material_plan.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplan/_materialplan_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.material_plan)
        self.assertContains(response, 'Edit Material Plan')
        
    def test_update_view_post_success_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_name = 'Updated HTMX Plan'
        data = {
            'plan_name': updated_name,
            'status': 'Approved',
            'start_date': self.material_plan.start_date.isoformat(),
            'end_date': self.material_plan.end_date.isoformat(),
            'budget_allocated': '25000.00',
        }
        response = self.client.post(reverse('materialplan_edit', args=[self.material_plan.pk]), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.material_plan.refresh_from_db()
        self.assertEqual(self.material_plan.plan_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialPlanList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Material Plan updated successfully.')

    def test_delete_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('materialplan_delete', args=[self.material_plan.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_planning/materialplan/_materialplan_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.material_plan)
        self.assertContains(response, f'Delete Material Plan "{self.material_plan.plan_name}"?')
        
    def test_delete_view_post_success_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        pk_to_delete = self.material_plan.pk
        response = self.client.post(reverse('materialplan_delete', args=[pk_to_delete]), **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertFalse(MaterialPlan.objects.filter(pk=pk_to_delete).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialPlanList', response.headers['HX-Trigger'])
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Material Plan deleted successfully.')
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for Dynamic Content:**
    -   The main `list.html` uses `hx-get` to load `_materialplan_table.html` into a `div` (`#materialplanTable-container`) on page load and whenever a `refreshMaterialPlanList` custom event is triggered (after successful CRUD operations).
    -   'Add', 'Edit', and 'Delete' buttons on the `_materialplan_table.html` use `hx-get` to fetch their respective forms (`_materialplan_form.html` or `_materialplan_confirm_delete.html`) into a modal container (`#modalContent`).
    -   Form submissions (POST requests) from `_materialplan_form.html` and `_materialplan_confirm_delete.html` are handled by `hx-post`. Upon success (HTTP 204 No Content), the server sends `HX-Trigger` headers (`refreshMaterialPlanList`, `closeModal`) to update the table and close the modal without a full page reload.
    -   `hx-swap="none"` on form POST ensures that HTMX doesn't try to swap content from the 204 response, relying instead on `HX-Trigger` for client-side actions.
    -   Error handling for forms with HTMX means the view re-renders the form with validation errors if `form_invalid` is called, allowing the user to correct input without losing the modal context.

-   **Alpine.js for UI State Management:**
    -   A simple `x-data="{ showModal: false }"` is implied on the modal element (`#modal`) or can be directly implemented using the `hidden` class toggle and `on click` handlers.
    -   `_ = "on click add .is-active to #modal"` directly controls the modal's visibility when buttons are clicked.
    -   The `on closeModal` event triggered by HTMX is used to remove the `is-active` class from the modal, effectively hiding it. This ensures Alpine.js works seamlessly with HTMX for modal lifecycle management.

-   **DataTables for List Views:**
    -   The `_materialplan_table.html` partial includes a `<table>` with the ID `materialplanTable`.
    -   A `<script>` block within this partial initializes DataTables on this table using `$(document).ready(function() { $('#materialplanTable').DataTable({...}); });`. This script runs every time the partial is loaded by HTMX, ensuring the DataTable is correctly initialized on new data. `destroy()` is called first to prevent re-initialization issues.
    -   DataTables provides client-side features like searching, sorting, and pagination out-of-the-box.

-   **No Additional JavaScript:** The entire dynamic interaction stack (CRUD forms, table refresh, modal handling) is achieved exclusively with HTMX and Alpine.js, fulfilling the "no additional JavaScript" requirement beyond what DataTables provides.

## Final Notes

-   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete examples derived from the assumed "Material Planning" module.
-   **DRY Templates:** Templates are designed to be DRY. `_materialplan_table.html`, `_materialplan_form.html`, and `_materialplan_confirm_delete.html` are partials intended for reuse and dynamic loading. The `list.html` is the main entry point extending `core/base.html`.
-   **Fat Models, Thin Views:** The `MaterialPlan` model includes `is_active()`, `get_status_color()`, and `update_plan_status()` methods to demonstrate embedding business logic within the model. Views are kept minimal, focusing on handling HTTP requests and responses.
-   **Comprehensive Tests:** Unit tests for model methods and integration tests for views (including HTMX-specific response headers and template usage) are provided to ensure high test coverage and reliability.
-   **HTMX/Alpine.js Interactions:** The entire user experience is built around HTMX for server communication and Alpine.js for local UI state (like modal visibility), ensuring a smooth, single-page application feel without complex JavaScript frameworks. Tailwind CSS classes are consistently applied for modern styling.