This document outlines a comprehensive modernization plan to transition your legacy ASP.NET application, specifically the "MR Office" module, to a modern, efficient, and maintainable Django-based solution. Our approach prioritizes automation, leverages contemporary web technologies like HTMX and Alpine.js, and adheres to Django's best practices, including the "Fat Model, Thin View" architecture. This strategy ensures a systematic migration with clear, actionable steps, making it understandable for both technical and non-technical stakeholders.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**

From the ASP.NET code, we identify two primary tables:

1.  **`tblMROffice`**: This table stores the MR Office documents and their metadata.
    *   Columns inferred from usage (`GridView2` bindings, `fun.insert`):
        *   `Id` (Primary Key, Integer)
        *   `ForModule` (Integer, likely Foreign Key to `tblModule_Master.ModId`)
        *   `Format` (String)
        *   `FileName` (String)
        *   `Size` (Integer, for file size)
        *   `ContentType` (String, for file MIME type)
        *   `Data` (Binary, for the actual file content)
        *   `SysDate` (Date, system entry date)
        *   `SysTime` (Time, system entry time)
        *   `CompId` (String, Company ID from session)
        *   `SessionId` (String, User Session ID/Username from session)
        *   `FinYearId` (String, Financial Year ID from session)

2.  **`tblModule_Master`**: This table is used as a lookup for the "For Module" dropdown.
    *   Columns inferred from `SqlDataSource1`:
        *   `ModId` (Primary Key, Integer)
        *   `ModName` (String)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

*   **Read (R):** The `BindData()` method fetches all records from `tblMROffice` (filtered by `CompId`) and displays them in `GridView2`. It also performs a lookup to `tblModule_Master` for `ModName`. Pagination is handled by `GridView2_PageIndexChanging`.
*   **Create (C):** The `GridView2_RowCommand` method handles two "Add" commands (`Add` and `AddEmpty`). These commands extract data from the footer row (or empty template row), including a file upload, and use `fun.insert` to save a new record to `tblMROffice`. The file's binary content, name, size, and content type are stored directly in the database.
*   **Update (U):** No explicit "Update" functionality is observed in the provided code. The `GridView` setup primarily supports Create, Read, and Delete. If update is needed, it would be a separate enhancement. For this migration, we will assume it's a future enhancement or done elsewhere. However, a standard `UpdateView` will be included in the Django plan for completeness of CRUD.
*   **Delete (D):** The `GridView2_RowCommand` method handles the "Delete" command, which extracts the record `Id` from a `HiddenField` and uses `fun.delete` to remove the record from `tblMROffice`.
*   **Download:** The `GridView2_RowCommand` also handles a "Download" command, redirecting to a generic `DownloadFile.aspx` handler, passing the record ID and table/field names. This implies serving stored binary data as a file.

**Validation Logic:** No explicit validation logic is visible in the provided code-behind; it seems to rely on database constraints or implicit ASP.NET controls. Django forms will provide robust validation.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **`GridView2`**: This is the central data display and interaction component. It's used for:
    *   Displaying a list of "MR Office" documents (`tblMROffice`).
    *   Showing serial numbers.
    *   Displaying "For Module" (converted from `ModId` to `ModName` using `tblModule_Master`).
    *   Displaying "Format/Document".
    *   Providing a "View" link for "Attachment" (download).
    *   Providing a "Delete" link.
    *   Presenting an "Add" row in the footer (or empty data template) with:
        *   `DropDownList` (`drpFor`) for "For Module" (populated from `tblModule_Master`).
        *   `TextBox` (`txtFormat`) for "Format/Document".
        *   `FileUpload` (`FileUploadAtt`) for "Attachment".
        *   `Button` (`Add`) to trigger insertion.

**Client-Side Interactions:** The ASP.NET GridView relies on postbacks for all interactions (paging, add, delete, download). In Django, these will be replaced by HTMX requests for dynamic, partial updates, and Alpine.js for UI state (e.g., modal visibility). DataTables will manage the client-side aspects of the list view (search, sort, pagination).

### Step 4: Generate Django Code

We will structure the Django application within a new app named `mroffice`.

#### 4.1 Models (`mroffice/models.py`)

We create two models: `ModuleMaster` for the lookup table and `MROffice` for the main document table. We will implement custom manager methods on `MROffice` to encapsulate business logic (creating, deleting, retrieving for download).

```python
from django.db import models
from django.utils import timezone
import os

class ModuleMaster(models.Model):
    """
    Maps to tblModule_Master for 'For Module' lookup.
    """
    mod_id = models.IntegerField(db_column='ModId', primary_key=True)
    mod_name = models.CharField(db_column='ModName', max_length=255)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblModule_Master'
        verbose_name = 'Module Master'
        verbose_name_plural = 'Module Masters'

    def __str__(self):
        return self.mod_name

class MROfficeManager(models.Manager):
    """
    Custom manager for MROffice model to encapsulate business logic.
    Keeps views thin by moving complex operations here.
    """
    def create_mroffice_document(self, for_module_id, format_document, uploaded_file, company_id, session_id, financial_year_id):
        """
        Creates a new MROffice document, handling file data and metadata.
        """
        file_name = None
        file_size = 0
        content_type = None
        file_data = None

        if uploaded_file:
            file_name = os.path.basename(uploaded_file.name)
            file_size = uploaded_file.size
            content_type = uploaded_file.content_type
            file_data = uploaded_file.read()

        # Assuming timezone.now() and timezone.localdate() give the correct local time for SysDate/SysTime
        current_datetime = timezone.now()
        sys_date = current_datetime.date()
        sys_time = current_datetime.time()

        mroffice_obj = self.create(
            for_module_id=for_module_id,
            format_document=format_document,
            file_name=file_name,
            file_size=file_size,
            content_type=content_type,
            file_data=file_data,
            sys_date=sys_date,
            sys_time=sys_time,
            company_id=company_id,
            session_id=session_id,
            financial_year_id=financial_year_id,
        )
        return mroffice_obj

    def delete_mroffice_document(self, pk):
        """
        Deletes an MROffice document by its primary key.
        """
        try:
            obj = self.get(pk=pk)
            obj.delete()
            return True
        except MROffice.DoesNotExist:
            return False

    def get_document_for_download(self, pk):
        """
        Retrieves document data for download.
        """
        try:
            obj = self.get(pk=pk)
            return {
                'file_data': obj.file_data,
                'file_name': obj.file_name,
                'content_type': obj.content_type
            }
        except MROffice.DoesNotExist:
            return None

class MROffice(models.Model):
    """
    Maps to tblMROffice for MR Office documents.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is primary key and auto-incremented by DB
    for_module = models.ForeignKey(ModuleMaster, models.DO_NOTHING, db_column='ForModule', related_name='mroffice_documents')
    format_document = models.CharField(db_column='Format', max_length=255)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.BigIntegerField(db_column='Size', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='Data', blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    company_id = models.CharField(db_column='CompId', max_length=255)
    session_id = models.CharField(db_column='SessionId', max_length=255)
    financial_year_id = models.CharField(db_column='FinYearId', max_length=255)

    objects = MROfficeManager() # Attach the custom manager

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMROffice'
        verbose_name = 'MR Office Document'
        verbose_name_plural = 'MR Office Documents'
        ordering = ['for_module__mod_name'] # Order by module name as in ASP.NET

    def __str__(self):
        return f"{self.format_document} ({self.for_module.mod_name})"

    # No additional business logic methods needed on the instance,
    # as they are moved to the custom manager for a thinner model.
```

#### 4.2 Forms (`mroffice/forms.py`)

A single `ModelForm` will handle the "Add" and "Edit" operations, including file uploads and linking to the `ModuleMaster`.

```python
from django import forms
from .models import MROffice, ModuleMaster

class MROfficeForm(forms.ModelForm):
    """
    Form for creating and updating MROffice documents.
    Handles 'For Module' selection and file upload.
    """
    for_module = forms.ModelChoiceField(
        queryset=ModuleMaster.objects.all().order_by('mod_name'),
        label="For Module",
        to_field_name="mod_id",
        empty_label="Select Module",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    attachment = forms.FileField(
        label="Attachment", 
        required=False,
        widget=forms.FileInput(attrs={'class': 'block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    class Meta:
        model = MROffice
        # Fields that the user directly inputs via the form
        fields = ['for_module', 'format_document'] 
        widgets = {
            'format_document': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If editing an existing object, the attachment field should not be pre-filled
        # but rather indicate if a file exists. No file input field is pre-filled.
        # The logic to display existing file name is handled in template.

    def clean(self):
        cleaned_data = super().clean()
        # No specific custom form validation needed based on ASP.NET analysis
        return cleaned_data
```

#### 4.3 Views (`mroffice/views.py`)

Views are kept intentionally thin, delegating most logic to the model's custom manager. This adheres to the "Fat Model, Thin View" principle and the 15-line view method limit.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404, FileResponse
from .models import MROffice, ModuleMaster # Import ModuleMaster for ModelChoiceField queryset
from .forms import MROfficeForm
from django.template.loader import render_to_string # For HTMX partials

class MROfficeListView(ListView):
    """
    Displays the main page for MR Office documents, including the HTMX container
    for the DataTables component.
    """
    model = MROffice
    template_name = 'mroffice/mroffice_list.html'
    context_object_name = 'mroffices' # Not directly used for table, but good practice

    # This view primarily serves the base page structure for HTMX to load content into.
    # No direct data fetching here to keep it lean.

class MROfficeTablePartialView(ListView):
    """
    Renders the DataTables partial, loaded via HTMX.
    This view fetches data and renders only the table HTML.
    """
    model = MROffice
    template_name = 'mroffice/_mroffice_table.html'
    context_object_name = 'mroffices'

    def get_queryset(self):
        """
        Filters documents by company_id and orders them, mimicking ASP.NET behavior.
        Assumes company_id is available from the request or a session variable.
        """
        # In a real application, you'd get these from request.user or session.
        # For demonstration, using dummy values. Adapt as per your authentication setup.
        company_id = self.request.session.get('compid', 'DUMMY_COMP_ID')
        
        # Optimize with select_related to avoid N+1 query for ModuleMaster name
        return MROffice.objects.filter(company_id=company_id).select_related('for_module').order_by('for_module__mod_name')

class MROfficeCreateView(CreateView):
    """
    Handles creation of new MR Office documents. Rendered in a modal via HTMX.
    """
    model = MROffice
    form_class = MROfficeForm
    template_name = 'mroffice/_mroffice_form.html' # Use partial template
    success_url = reverse_lazy('mroffice_list') # Fallback, HTMX will handle refresh

    def form_valid(self, form):
        """
        Handles saving the form data and file upload.
        Leverages the custom manager method for business logic.
        """
        uploaded_file = self.request.FILES.get('attachment')
        
        # In a real application, get these from request.user or session.
        company_id = self.request.session.get('compid', 'DUMMY_COMP_ID')
        session_id = self.request.session.get('username', 'DUMMY_USERNAME')
        financial_year_id = self.request.session.get('finyear', 'DUMMY_FINYEAR')

        MROffice.objects.create_mroffice_document(
            for_module_id=form.cleaned_data['for_module'].mod_id, # Get ModId from selected ModuleMaster object
            format_document=form.cleaned_data['format_document'],
            uploaded_file=uploaded_file,
            company_id=company_id,
            session_id=session_id,
            financial_year_id=financial_year_id
        )
        messages.success(self.request, 'MR Office Document added successfully.')
        
        # HTMX response for success, triggering list refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content
                headers={'HX-Trigger': 'refreshMROfficeList'}
            )
        return super().form_valid(form) # Fallback for non-HTMX requests

class MROfficeUpdateView(UpdateView):
    """
    Handles updating existing MR Office documents. Rendered in a modal via HTMX.
    """
    model = MROffice
    form_class = MROfficeForm
    template_name = 'mroffice/_mroffice_form.html' # Use partial template
    context_object_name = 'mroffice'
    success_url = reverse_lazy('mroffice_list') # Fallback

    def form_valid(self, form):
        """
        Handles saving the updated form data.
        File updates would require specific logic if the file itself can be replaced.
        For this simple update, we assume file data is not updated via this form.
        """
        # If file upload logic is to be included for update, it needs to be added here.
        # For simplicity, assuming file is attached only on creation or handled separately.
        # If the file_data is updated, handle self.request.FILES.get('attachment') here.
        
        # Update the for_module_id, which is a ForeignKey.
        self.object.for_module = form.cleaned_data['for_module']
        self.object.format_document = form.cleaned_data['format_document']
        self.object.save() # Saves the instance
        
        messages.success(self.request, 'MR Office Document updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMROfficeList'}
            )
        return super().form_valid(form)

class MROfficeDeleteView(DeleteView):
    """
    Handles deletion of MR Office documents. Rendered in a modal via HTMX for confirmation.
    """
    model = MROffice
    template_name = 'mroffice/_mroffice_confirm_delete.html' # Use partial template
    context_object_name = 'mroffice'
    success_url = reverse_lazy('mroffice_list') # Fallback

    def delete(self, request, *args, **kwargs):
        """
        Overrides delete to use custom manager and handle HTMX response.
        """
        self.object = self.get_object()
        success = MROffice.objects.delete_mroffice_document(self.object.pk)
        if success:
            messages.success(self.request, 'MR Office Document deleted successfully.')
        else:
            messages.error(self.request, 'Failed to delete MR Office Document.')

        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshMROfficeList'}
            )
        return super().delete(request, *args, **kwargs) # Fallback

class MROfficeDownloadView(View):
    """
    Handles downloading the binary file content stored in the database.
    Mimics the ASP.NET DownloadFile.aspx functionality.
    """
    def get(self, request, pk):
        document_data = MROffice.objects.get_document_for_download(pk)

        if not document_data or not document_data['file_data']:
            raise Http404("Document not found or no file data.")

        file_data = document_data['file_data']
        file_name = document_data['file_name'] if document_data['file_name'] else f"document_{pk}"
        content_type = document_data['content_type'] if document_data['content_type'] else 'application/octet-stream'

        response = HttpResponse(file_data, content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response
```

#### 4.4 Templates (`mroffice/templates/mroffice/`)

Templates are designed for HTMX interactions, using partials for modal content and table refreshes. They adhere to DRY principles and leverage Tailwind CSS for styling.

**`mroffice/templates/mroffice/mroffice_list.html`**
This is the main page for listing documents.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">MR Office Documents - ISO</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'mroffice_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New MR Document
        </button>
    </div>
    
    <div class="bg-white shadow-lg rounded-lg p-6">
        <div id="mrofficeTable-container"
             hx-trigger="load, refreshMROfficeList from:body"
             hx-get="{% url 'mroffice_table' %}"
             hx-swap="innerHTML">
            <!-- Initial loading state -->
            <div class="text-center p-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading MR Office documents...</p>
            </div>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for more complex UI state.
        // For basic modal toggle with HTMX, it's often handled directly by htmx _'s
    });

    // Simple event listener to close modal if HTMX request indicates success (e.g., 204 status with HX-Trigger)
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Handle messages (e.g., success/error banners) from Django's messages framework
    document.body.addEventListener('htmx:afterSwap', function(event) {
        // Assuming messages are swapped into a common element, e.g., #messages-container
        if (event.detail.target.id === 'messages-container') {
            // Optional: add logic to auto-hide messages after a delay
        }
    });
</script>
{% endblock %}
```

**`mroffice/templates/mroffice/_mroffice_table.html`**
This partial template contains the DataTables structure and is dynamically loaded.

```html
<div class="overflow-x-auto">
    <table id="mrofficeTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For Module</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Format/Document</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attachment</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in mroffices %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.for_module.mod_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.format_document }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                    {% if obj.file_data %}
                        <a href="{% url 'mroffice_download' obj.pk %}" class="text-blue-600 hover:text-blue-900 font-semibold" target="_blank">
                            View ({{ obj.file_name|default:'File' }})
                        </a>
                    {% else %}
                        N/A
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg text-xs mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'mroffice_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'mroffice_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">No MR Office documents found. Click "Add New MR Document" to get started.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#mrofficeTable')) {
            $('#mrofficeTable').DataTable({
                "pageLength": 20, // Default page size from ASP.NET
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "dom": 'lfrtip', // Layout for DataTables: Length changing, Filtering, Table, Information, Pagination
                "responsive": true // Make table responsive
            });
        }
    });
</script>
```

**`mroffice/templates/mroffice/_mroffice_form.html`**
This partial template is used for both adding and editing documents within the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} MR Office Document</h3>
    <form hx-post="{{ request.path }}" hx-encoding="multipart/form-data" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status !== 204 && event.detail.xhr.status !== 302) this.outerHTML = event.detail.xhr.responseText; else document.getElementById('modal').classList.remove('is-active');">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6">
            <div>
                <label for="{{ form.for_module.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.for_module.label }}
                </label>
                {{ form.for_module }}
                {% if form.for_module.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.for_module.errors.as_text }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.format_document.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.format_document.label }}
                </label>
                {{ form.format_document }}
                {% if form.format_document.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.format_document.errors.as_text }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.attachment.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.attachment.label }}
                </label>
                {{ form.attachment }}
                {% if form.instance.file_name %}
                    <p class="text-sm text-gray-500 mt-1">Current file: {{ form.instance.file_name }}</p>
                {% endif %}
                {% if form.attachment.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.attachment.errors.as_text }}</p>
                {% endif %}
            </div>
            
            {% if form.non_field_errors %}
            <div class="text-red-500 text-xs mt-1">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Document
            </button>
        </div>
    </form>
</div>
```

**`mroffice/templates/mroffice/_mroffice_confirm_delete.html`**
This partial template handles the delete confirmation within the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the document: <strong>"{{ mroffice.format_document }}"</strong> for module <strong>"{{ mroffice.for_module.mod_name }}"</strong>?</p>
    
    <form hx-post="{% url 'mroffice_delete' mroffice.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`mroffice/urls.py`)

Define the URL patterns for accessing the list, forms, and download functionality.

```python
from django.urls import path
from .views import (
    MROfficeListView, 
    MROfficeTablePartialView, 
    MROfficeCreateView, 
    MROfficeUpdateView, 
    MROfficeDeleteView,
    MROfficeDownloadView,
)

urlpatterns = [
    # Main list view (loads initial page)
    path('documents/', MROfficeListView.as_view(), name='mroffice_list'),
    
    # HTMX partial for the DataTables content
    path('documents/table/', MROfficeTablePartialView.as_view(), name='mroffice_table'),
    
    # HTMX partial for the add form (rendered in modal)
    path('documents/add/', MROfficeCreateView.as_view(), name='mroffice_add'),
    
    # HTMX partial for the edit form (rendered in modal)
    path('documents/edit/<int:pk>/', MROfficeUpdateView.as_view(), name='mroffice_edit'),
    
    # HTMX partial for the delete confirmation (rendered in modal)
    path('documents/delete/<int:pk>/', MROfficeDeleteView.as_view(), name='mroffice_delete'),
    
    # View for downloading the attached file
    path('documents/download/<int:pk>/', MROfficeDownloadView.as_view(), name='mroffice_download'),
]
```

#### 4.6 Tests (`mroffice/tests.py`)

Comprehensive tests cover both model business logic and view interactions, including HTMX-specific responses.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.core.files.uploadedfile import SimpleUploadedFile
from datetime import date, time
from .models import MROffice, ModuleMaster

class MROfficeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up non-managed models for testing (Django won't create tables)
        # We assume these tables exist in the test database for managed=False
        cls.module_master = ModuleMaster.objects.create(mod_id=1, mod_name="Production")
        cls.module_master_2 = ModuleMaster.objects.create(mod_id=2, mod_name="Quality")

        # Create test data for MROffice using the custom manager
        cls.mroffice_doc = MROffice.objects.create_mroffice_document(
            for_module_id=cls.module_master.mod_id,
            format_document="Test Document 1",
            uploaded_file=None, # No file for initial creation
            company_id="COMP001",
            session_id="user123",
            financial_year_id="FY2023"
        )
        
        cls.mroffice_doc_with_file = MROffice.objects.create_mroffice_document(
            for_module_id=cls.module_master_2.mod_id,
            format_document="Test Document 2 with File",
            uploaded_file=SimpleUploadedFile("test_file.txt", b"file content", content_type="text/plain"),
            company_id="COMP001",
            session_id="user123",
            financial_year_id="FY2023"
        )
  
    def test_mroffice_creation(self):
        doc = MROffice.objects.get(pk=self.mroffice_doc.pk)
        self.assertEqual(doc.format_document, "Test Document 1")
        self.assertEqual(doc.for_module.mod_name, "Production")
        self.assertEqual(doc.company_id, "COMP001")
        self.assertEqual(doc.session_id, "user123")
        self.assertEqual(doc.financial_year_id, "FY2023")
        self.assertIsNone(doc.file_data)
        
    def test_mroffice_creation_with_file(self):
        doc = MROffice.objects.get(pk=self.mroffice_doc_with_file.pk)
        self.assertEqual(doc.format_document, "Test Document 2 with File")
        self.assertEqual(doc.file_name, "test_file.txt")
        self.assertEqual(doc.file_size, 12) # "file content" is 12 bytes
        self.assertEqual(doc.content_type, "text/plain")
        self.assertEqual(doc.file_data, b"file content")

    def test_delete_mroffice_document(self):
        initial_count = MROffice.objects.count()
        success = MROffice.objects.delete_mroffice_document(self.mroffice_doc.pk)
        self.assertTrue(success)
        self.assertEqual(MROffice.objects.count(), initial_count - 1)
        self.assertFalse(MROffice.objects.filter(pk=self.mroffice_doc.pk).exists())

    def test_delete_non_existent_document(self):
        success = MROffice.objects.delete_mroffice_document(99999) # Non-existent PK
        self.assertFalse(success)

    def test_get_document_for_download(self):
        data = MROffice.objects.get_document_for_download(self.mroffice_doc_with_file.pk)
        self.assertIsNotNone(data)
        self.assertEqual(data['file_name'], "test_file.txt")
        self.assertEqual(data['content_type'], "text/plain")
        self.assertEqual(data['file_data'], b"file content")
        
    def test_get_document_for_download_no_file(self):
        data = MROffice.objects.get_document_for_download(self.mroffice_doc.pk)
        self.assertIsNotNone(data)
        self.assertIsNone(data['file_data'])
        
    def test_get_document_for_download_non_existent(self):
        data = MROffice.objects.get_document_for_download(99999)
        self.assertIsNone(data)

class MROfficeViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.module_master_1 = ModuleMaster.objects.create(mod_id=1, mod_name="Sales")
        cls.module_master_2 = ModuleMaster.objects.create(mod_id=2, mod_name="Marketing")
        
        cls.mroffice_doc_1 = MROffice.objects.create_mroffice_document(
            for_module_id=cls.module_master_1.mod_id,
            format_document="Sales Report Q1",
            uploaded_file=None,
            company_id="COMP001",
            session_id="testuser",
            financial_year_id="FY2024"
        )
        cls.mroffice_doc_2 = MROffice.objects.create_mroffice_document(
            for_module_id=cls.module_master_2.mod_id,
            format_document="Marketing Strategy",
            uploaded_file=SimpleUploadedFile("strategy.pdf", b"pdf content", content_type="application/pdf"),
            company_id="COMP001",
            session_id="testuser",
            financial_year_id="FY2024"
        )
    
    def setUp(self):
        self.client = Client()
        # Mock session variables that the views rely on
        session = self.client.session
        session['compid'] = 'COMP001'
        session['username'] = 'testuser'
        session['finyear'] = 'FY2024'
        session.save()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('mroffice_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/mroffice_list.html')
        
    def test_table_partial_view_get(self):
        response = self.client.get(reverse('mroffice_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/_mroffice_table.html')
        self.assertContains(response, "Sales Report Q1")
        self.assertContains(response, "Marketing Strategy")
        self.assertContains(response, "Sales") # Check for related module name
        self.assertContains(response, "Marketing")

    def test_create_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('mroffice_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/_mroffice_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, "Add MR Office Document")
        self.assertContains(response, '<form hx-post=')

    def test_create_view_post_htmx_no_file(self):
        initial_count = MROffice.objects.count()
        data = {
            'for_module': self.module_master_1.mod_id,
            'format_document': 'New Document No File',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('mroffice_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMROfficeList')
        self.assertEqual(MROffice.objects.count(), initial_count + 1)
        self.assertTrue(MROffice.objects.filter(format_document='New Document No File').exists())
        
    def test_create_view_post_htmx_with_file(self):
        initial_count = MROffice.objects.count()
        file_content = b"This is a dummy PDF file content."
        uploaded_file = SimpleUploadedFile("dummy.pdf", file_content, content_type="application/pdf")
        data = {
            'for_module': self.module_master_2.mod_id,
            'format_document': 'Another Doc with PDF',
            'attachment': uploaded_file,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('mroffice_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(MROffice.objects.count(), initial_count + 1)
        new_doc = MROffice.objects.get(format_document='Another Doc with PDF')
        self.assertEqual(new_doc.file_name, "dummy.pdf")
        self.assertEqual(new_doc.content_type, "application/pdf")
        self.assertEqual(new_doc.file_data, file_content)

    def test_update_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('mroffice_edit', args=[self.mroffice_doc_1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/_mroffice_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, "Edit MR Office Document")
        self.assertContains(response, 'value="Sales Report Q1"')
        self.assertContains(response, '<option value="1" selected>Sales</option>') # Check pre-selected module

    def test_update_view_post_htmx(self):
        data = {
            'for_module': self.module_master_2.mod_id, # Change module
            'format_document': 'Sales Report Q1 - UPDATED',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('mroffice_edit', args=[self.mroffice_doc_1.pk]), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMROfficeList')
        
        updated_doc = MROffice.objects.get(pk=self.mroffice_doc_1.pk)
        self.assertEqual(updated_doc.format_document, 'Sales Report Q1 - UPDATED')
        self.assertEqual(updated_doc.for_module.mod_name, 'Marketing') # Check if module updated

    def test_delete_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('mroffice_delete', args=[self.mroffice_doc_1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'mroffice/_mroffice_confirm_delete.html')
        self.assertTrue('mroffice' in response.context)
        self.assertContains(response, "Confirm Delete")
        self.assertContains(response, "Sales Report Q1")

    def test_delete_view_post_htmx(self):
        initial_count = MROffice.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('mroffice_delete', args=[self.mroffice_doc_1.pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMROfficeList')
        self.assertEqual(MROffice.objects.count(), initial_count - 1)
        self.assertFalse(MROffice.objects.filter(pk=self.mroffice_doc_1.pk).exists())

    def test_download_view(self):
        response = self.client.get(reverse('mroffice_download', args=[self.mroffice_doc_2.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="strategy.pdf"')
        self.assertEqual(response.content, b"pdf content")
        
    def test_download_view_not_found(self):
        response = self.client.get(reverse('mroffice_download', args=[99999]))
        self.assertEqual(response.status_code, 404)
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:**
    *   The `mroffice_list.html` uses `hx-get="{% url 'mroffice_table' %}"` with `hx-trigger="load, refreshMROfficeList from:body"` to initially load and periodically refresh the DataTables content without a full page reload.
    *   "Add New", "Edit", and "Delete" buttons trigger `hx-get` requests to their respective URLs, targeting `#modalContent` and activating the modal using `_` (hyperscript).
    *   Forms inside the modal (e.g., `_mroffice_form.html`, `_mroffice_confirm_delete.html`) use `hx-post` to submit data. Upon successful submission (HTTP 204 No Content), the server sends `HX-Trigger: refreshMROfficeList`, which tells the main list view to reload its table.
    *   Error handling for form submission (e.g., validation errors) is managed by `hx-on::after-request` in the form partial, which re-swaps the form if a non-204/302 status is returned.
*   **Alpine.js for UI State Management:**
    *   The main modal `div` (`#modal`) uses `Alpine.js` `x-data="{ showModal: false }"` for its visibility state, combined with HTMX `_` attributes for class toggling (`add .is-active to #modal` and `remove .is-active from #modal`). This allows Alpine.js to manage the `.is-active` class to show/hide the modal. A click outside the modal content also closes it.
*   **DataTables for List Views:**
    *   The `_mroffice_table.html` partial includes the `<script>` tag to initialize `DataTables` on the `mrofficeTable` element. This handles client-side searching, sorting, and pagination for the displayed data, providing a rich user experience without requiring server-side pagination.
*   **No Custom JavaScript:** All dynamic interactions are managed via HTMX attributes and a minimal Alpine.js for modal state. The only traditional JavaScript is for DataTables initialization, which is a library-specific requirement.

### Final Notes

This modernization plan provides a robust and scalable solution for your "MR Office" module. By adhering to Django's best practices, leveraging HTMX and Alpine.js, and emphasizing a "Fat Model, Thin View" architecture, the resulting application will be:

*   **Maintainable:** Clean separation of concerns simplifies debugging and future enhancements.
*   **Performant:** HTMX minimizes page reloads, providing a faster user experience.
*   **Modern:** Utilizes current industry standards and libraries (Django 5.0+, HTMX, Alpine.js, Tailwind CSS).
*   **Testable:** Comprehensive unit and integration tests ensure high code quality and reliability.
*   **Automated:** The structured approach facilitates AI-assisted conversion and reduces manual development effort.

This plan focuses solely on the `MROffice` module as provided, but the principles can be systematically applied to other modules of your legacy ASP.NET application for a phased, automated modernization.