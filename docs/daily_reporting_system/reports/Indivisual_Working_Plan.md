## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The provided ASP.NET `.aspx` and C# code-behind files are boilerplate and do not contain any specific database interactions, UI controls, or business logic. This indicates a very early stage or an empty placeholder.

**In a real-world scenario, the AI-assisted automation would perform the following:**

-   **Deep Scan:** Analyze `web.config` for connection strings, `App_Code` for data access layers, and all `.cs` files for `SqlDataSource`, `SqlCommand`, `DataReader`, Entity Framework contexts, or LINQ queries.
-   **Schema Inference:** Based on the presence of `Page_Load` and the file name `Indivisual_Working_Plan.aspx`, it's highly probable this page is intended to display or manage individual working plans. Without explicit database calls, we must infer a logical schema.

**Inferred Database Schema (Hypothetical based on `INDIVISUAL_WORKING_PLAN`):**

-   **[TABLE_NAME]**: `TblWorkingPlan` (Common ASP.NET prefix `Tbl` and descriptive name)
-   **Columns:**
    -   `ID` (int, Primary Key, Identity)
    -   `PlanName` (nvarchar(255))
    -   `Description` (nvarchar(MAX))
    -   `StartDate` (datetime)
    -   `EndDate` (datetime)
    -   `Status` (nvarchar(50), e.g., 'Draft', 'InProgress', 'Completed', 'Canceled')
    -   `AssignedTo` (nvarchar(100))

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Since the provided ASP.NET code is empty boilerplate, no specific CRUD operations are present.

**In a real-world scenario, the AI-assisted automation would perform the following:**

-   **Event Handler Analysis:** Scan `CodeFile` (`.aspx.cs`) for methods like `GridView_RowCommand`, `Button_Click`, `Page_Load` (if it populates data), and `ObjectDataSource` configurations.
-   **SQL/LINQ Pattern Recognition:** Identify `INSERT`, `SELECT`, `UPDATE`, `DELETE` statements or their ORM equivalents.
-   **Validation Identification:** Look for `RequiredFieldValidator`, `ValidationSummary`, or programmatic validation logic within event handlers.

**Inferred Backend Functionality (Hypothetical):**

-   **Read:** Likely populates a `GridView` or similar control to display a list of working plans.
-   **Create:** A form or dialog would allow new working plans to be added.
-   **Update:** Existing plans could be edited through an edit form or in-place editing within a `GridView`.
-   **Delete:** A mechanism (button/link) to remove specific working plans.
-   **Validation:** Basic validation for required fields (Plan Name, Start Date, End Date) and date range validity.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The provided ASP.NET `.aspx` file contains no UI controls within its `<body>` tag, only an empty `<div>` inside a `form`.

**In a real-world scenario, the AI-assisted automation would perform the following:**

-   **Control Tree Parsing:** Analyze the `.aspx` markup to identify `asp:` prefixed controls.
-   **Property & Event Analysis:** Extract `DataSourceID`, `DataTextField`, `DataValueField`, `OnItemCommand`, `OnRowEditing`, etc.
-   **Client-side Scripting:** Look for `<script>` blocks or `OnClientClick` attributes for JavaScript.

**Inferred UI Components (Hypothetical):**

-   **Data Display:** A `GridView` to list `TblWorkingPlan` records.
-   **Data Input:** `TextBox` controls for `PlanName`, `Description`, `AssignedTo`. `Calendar` or `TextBox` with date pickers for `StartDate`, `EndDate`. `DropDownList` for `Status`.
-   **Actions:** `Button` controls for "Add New Plan," "Save," "Edit," "Delete."
-   **Modals:** Likely use of an AJAX Control Toolkit `ModalPopupExtender` or similar for add/edit forms to avoid full page reloads.

### Step 4: Generate Django Code

Based on the inferred structure and functionality, we will now generate the corresponding Django components.

**Django Application Name:** `project_planning`
**Django Model Name:** `WorkingPlan`

## 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

We'll define the `WorkingPlan` model with the inferred fields, setting `managed = False` and `db_table` to map to the existing database table. We will also include business logic methods (fat model).

```python
# project_planning/models.py
from django.db import models
from django.utils import timezone
from datetime import timedelta

class WorkingPlan(models.Model):
    # Choices for the 'Status' field
    STATUS_CHOICES = [
        ('Draft', 'Draft'),
        ('InProgress', 'In Progress'),
        ('Completed', 'Completed'),
        ('Canceled', 'Canceled'),
    ]

    # Map to existing database columns
    id = models.AutoField(primary_key=True, db_column='ID') # Assuming ID is auto-increment PK
    plan_name = models.CharField(max_length=255, db_column='PlanName', verbose_name='Plan Name')
    description = models.TextField(db_column='Description', blank=True, verbose_name='Description')
    start_date = models.DateField(db_column='StartDate', verbose_name='Start Date')
    end_date = models.DateField(db_column='EndDate', verbose_name='End Date')
    status = models.CharField(max_length=50, db_column='Status', choices=STATUS_CHOICES, default='Draft', verbose_name='Status')
    assigned_to = models.CharField(max_length=100, db_column='AssignedTo', blank=True, verbose_name='Assigned To')

    class Meta:
        managed = False  # Important: Django won't manage this table's creation/deletion
        db_table = 'TblWorkingPlan'  # Map to the existing ASP.NET database table
        verbose_name = 'Working Plan'
        verbose_name_plural = 'Working Plans'
        ordering = ['start_date', 'plan_name'] # Default ordering for lists

    def __str__(self):
        return self.plan_name

    # Business logic methods (Fat Model)
    def is_current(self):
        """Checks if the plan is currently active."""
        today = timezone.localdate()
        return self.start_date <= today <= self.end_date and self.status == 'InProgress'

    def is_past_due(self):
        """Checks if the plan's end date has passed and it's not completed/canceled."""
        today = timezone.localdate()
        return self.end_date < today and self.status not in ['Completed', 'Canceled']

    def days_remaining(self):
        """Calculates the number of days remaining until the end date."""
        today = timezone.localdate()
        if self.end_date >= today:
            return (self.end_date - today).days
        return 0 # Plan is already past due

    def mark_as_completed(self):
        """Marks the plan as completed."""
        if self.status != 'Completed':
            self.status = 'Completed'
            self.save()
            return True
        return False # Already completed

    @classmethod
    def get_active_plans(cls):
        """Returns all plans that are currently in progress."""
        return cls.objects.filter(status='InProgress', end_date__gte=timezone.localdate())

```

## 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

Create a `ModelForm` for `WorkingPlan`. Include editable fields and add widgets with Tailwind CSS classes for styling. Use `DateInput` for date fields.

```python
# project_planning/forms.py
from django import forms
from .models import WorkingPlan

class WorkingPlanForm(forms.ModelForm):
    class Meta:
        model = WorkingPlan
        fields = ['plan_name', 'description', 'start_date', 'end_date', 'status', 'assigned_to']
        widgets = {
            'plan_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm datepicker', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'assigned_to': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date:
            if end_date < start_date:
                self.add_error('end_date', "End date cannot be before start date.")
        return cleaned_data

```

## 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Define `ListView`, `CreateView`, `UpdateView`, `DeleteView`, and a `TemplateView` for the HTMX-loaded table partial. Keep views thin (5-15 lines) and move business logic to models.

```python
# project_planning/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import WorkingPlan
from .forms import WorkingPlanForm

class WorkingPlanListView(ListView):
    model = WorkingPlan
    template_name = 'project_planning/working_plan/list.html'
    context_object_name = 'working_plans' # This will be the list of all plans

class WorkingPlanTablePartialView(TemplateView):
    # This view is specifically for HTMX to fetch and render the table body
    template_name = 'project_planning/working_plan/_working_plan_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetching all objects for the table
        context['working_plans'] = WorkingPlan.objects.all()
        return context

class WorkingPlanCreateView(CreateView):
    model = WorkingPlan
    form_class = WorkingPlanForm
    template_name = 'project_planning/working_plan/_working_plan_form.html' # This is a partial for modal
    success_url = reverse_lazy('working_plan_list') # Redirect after successful form submission

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Working Plan added successfully.')
        if self.request.headers.get('HX-Request'): # Check if it's an HTMX request
            # Return a 204 No Content response for HTMX and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkingPlanList'
                }
            )
        return response

class WorkingPlanUpdateView(UpdateView):
    model = WorkingPlan
    form_class = WorkingPlanForm
    template_name = 'project_planning/working_plan/_working_plan_form.html' # Partial for modal
    success_url = reverse_lazy('working_plan_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Working Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkingPlanList'
                }
            )
        return response

class WorkingPlanDeleteView(DeleteView):
    model = WorkingPlan
    template_name = 'project_planning/working_plan/_working_plan_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('working_plan_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Working Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkingPlanList'
                }
            )
        return response
```

## 4.4 Templates

Task: Create templates for each view.

## Instructions:

List Template (`list.html`): Extends `core/base.html`, uses HTMX to load the table.
Table Partial (`_working_plan_table.html`): Contains the DataTables markup.
Form Partial (`_working_plan_form.html`): Used for add/edit forms within a modal.
Delete Confirmation Partial (`_working_plan_confirm_delete.html`): Used for delete confirmation within a modal.

```html
<!-- project_planning/templates/project_planning/working_plan/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Working Plans</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'working_plan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Working Plan
        </button>
    </div>
    
    <div id="working_planTable-container"
         hx-trigger="load, refreshWorkingPlanList from:body"
         hx-get="{% url 'working_plan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Working Plans...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Alpine.js can be initialized here if more complex UI state is needed -->
<script>
    document.addEventListener('alpine:init', () => {
        // Example: x-data="someComponent()" if needed
    });
</script>
{% endblock %}
```

```html
<!-- project_planning/templates/project_planning/working_plan/_working_plan_table.html -->
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="working_planTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Assigned To</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in working_plans %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.plan_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.description|truncatechars:50 }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.start_date|date:"M d, Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.end_date|date:"M d, Y" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.status == 'Completed' %}bg-green-100 text-green-800
                        {% elif obj.status == 'InProgress' %}bg-blue-100 text-blue-800
                        {% elif obj.status == 'Draft' %}bg-gray-100 text-gray-800
                        {% elif obj.status == 'Canceled' %}bg-red-100 text-red-800
                        {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                        {{ obj.status }}
                    </span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.assigned_to }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                        hx-get="{% url 'working_plan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                        hx-get="{% url 'working_plan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization must happen after the table is loaded into the DOM
    $(document).ready(function() {
        $('#working_planTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

```html
<!-- project_planning/templates/project_planning/working_plan/_working_plan_form.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Working Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>

<script>
    // Initialize date pickers if using a library like flatpickr (assuming CDNs in base.html)
    // flatpickr(".datepicker", {
    //     dateFormat: "Y-m-d",
    // });
</script>
```

```html
<!-- project_planning/templates/project_planning/working_plan/_working_plan_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the working plan: <strong>"{{ object.plan_name }}"</strong>?</p>
    
    <form hx-post="{% url 'working_plan_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

## 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create paths for list, create, update, delete, and the HTMX table partial.

```python
# project_planning/urls.py
from django.urls import path
from .views import WorkingPlanListView, WorkingPlanCreateView, WorkingPlanUpdateView, WorkingPlanDeleteView, WorkingPlanTablePartialView

urlpatterns = [
    path('working-plans/', WorkingPlanListView.as_view(), name='working_plan_list'),
    path('working-plans/table/', WorkingPlanTablePartialView.as_view(), name='working_plan_table'), # For HTMX partial load
    path('working-plans/add/', WorkingPlanCreateView.as_view(), name='working_plan_add'),
    path('working-plans/edit/<int:pk>/', WorkingPlanUpdateView.as_view(), name='working_plan_edit'),
    path('working-plans/delete/<int:pk>/', WorkingPlanDeleteView.as_view(), name='working_plan_delete'),
]

```

## 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and properties. Add integration tests for all views (list, create, update, delete), including HTMX interactions.

```python
# project_planning/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import WorkingPlan
from .forms import WorkingPlanForm

class WorkingPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.plan1 = WorkingPlan.objects.create(
            plan_name='Q1 Project Review',
            description='Review of Q1 project progress with team.',
            start_date=timezone.localdate() - timedelta(days=10),
            end_date=timezone.localdate() + timedelta(days=5),
            status='InProgress',
            assigned_to='John Doe'
        )
        cls.plan2 = WorkingPlan.objects.create(
            plan_name='Annual Report Preparation',
            description='Gather data and draft annual report.',
            start_date=timezone.localdate() - timedelta(days=30),
            end_date=timezone.localdate() - timedelta(days=1),
            status='Completed',
            assigned_to='Jane Smith'
        )
        cls.plan3 = WorkingPlan.objects.create(
            plan_name='New Initiative Brainstorm',
            description='Brainstorming session for next quarter initiatives.',
            start_date=timezone.localdate() + timedelta(days=7),
            end_date=timezone.localdate() + timedelta(days=10),
            status='Draft',
            assigned_to='Alice Brown'
        )

    def test_working_plan_creation(self):
        self.assertEqual(self.plan1.plan_name, 'Q1 Project Review')
        self.assertEqual(self.plan1.status, 'InProgress')
        self.assertEqual(self.plan1.assigned_to, 'John Doe')
        self.assertEqual(WorkingPlan.objects.count(), 3)

    def test_plan_name_label(self):
        field_label = self.plan1._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')

    def test_is_current_method(self):
        self.assertTrue(self.plan1.is_current()) # InProgress, within dates
        self.assertFalse(self.plan2.is_current()) # Completed
        self.assertFalse(self.plan3.is_current()) # Draft, future date

    def test_is_past_due_method(self):
        # Temporarily change plan2 status to 'InProgress' to test past_due for an in progress plan
        old_status = self.plan2.status
        self.plan2.status = 'InProgress'
        self.plan2.save()
        self.assertTrue(self.plan2.is_past_due()) # End date passed, InProgress
        self.plan2.status = old_status # Revert
        self.plan2.save()
        
        self.assertFalse(self.plan1.is_past_due()) # Not past due
        self.assertFalse(self.plan2.is_past_due()) # Completed, so not considered past due
        self.assertFalse(self.plan3.is_past_due()) # Future date

    def test_days_remaining_method(self):
        # Test for plan that is in progress
        self.assertGreaterEqual(self.plan1.days_remaining(), 0)
        
        # Test for past due plan
        future_plan = WorkingPlan.objects.create(
            plan_name='Future Test',
            start_date=timezone.localdate() + timedelta(days=20),
            end_date=timezone.localdate() + timedelta(days=25),
            status='Draft'
        )
        self.assertEqual(future_plan.days_remaining(), 25)

    def test_mark_as_completed_method(self):
        plan = WorkingPlan.objects.create(
            plan_name='To Complete',
            start_date=timezone.localdate(),
            end_date=timezone.localdate(),
            status='InProgress'
        )
        self.assertTrue(plan.mark_as_completed())
        self.assertEqual(plan.status, 'Completed')
        self.assertFalse(plan.mark_as_completed()) # Already completed

    def test_get_active_plans_class_method(self):
        active_plans = WorkingPlan.get_active_plans()
        self.assertIn(self.plan1, active_plans)
        self.assertNotIn(self.plan2, active_plans)
        self.assertNotIn(self.plan3, active_plans) # Draft is not active

class WorkingPlanFormTest(TestCase):
    def test_form_valid(self):
        form_data = {
            'plan_name': 'New Plan',
            'description': 'A new plan description',
            'start_date': timezone.localdate(),
            'end_date': timezone.localdate() + timedelta(days=10),
            'status': 'Draft',
            'assigned_to': 'Test User'
        }
        form = WorkingPlanForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_form_invalid_end_date_before_start_date(self):
        form_data = {
            'plan_name': 'Bad Plan',
            'start_date': timezone.localdate() + timedelta(days=10),
            'end_date': timezone.localdate(),
            'status': 'Draft',
            'assigned_to': 'Test User'
        }
        form = WorkingPlanForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('end_date', form.errors)
        self.assertEqual(form.errors['end_date'], ['End date cannot be before start date.'])

class WorkingPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.plan = WorkingPlan.objects.create(
            plan_name='Test Plan',
            description='Test Description',
            start_date=timezone.localdate(),
            end_date=timezone.localdate() + timedelta(days=7),
            status='InProgress',
            assigned_to='Test User'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('working_plan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/working_plan/list.html')
        self.assertIn('working_plans', response.context)
        self.assertContains(response, self.plan.plan_name)

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('working_plan_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/working_plan/_working_plan_table.html')
        self.assertIn('working_plans', response.context)
        self.assertContains(response, self.plan.plan_name) # Ensure data is rendered

    def test_create_view_get(self):
        response = self.client.get(reverse('working_plan_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/working_plan/_working_plan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Working Plan')

    def test_create_view_post_success(self):
        new_plan_data = {
            'plan_name': 'New Project X',
            'description': 'Description for X',
            'start_date': timezone.localdate() + timedelta(days=10),
            'end_date': timezone.localdate() + timedelta(days=20),
            'status': 'Draft',
            'assigned_to': 'Another User'
        }
        response = self.client.post(reverse('working_plan_add'), new_plan_data, follow=True)
        # Should redirect to list view on success (non-HTMX)
        self.assertRedirects(response, reverse('working_plan_list'))
        self.assertTrue(WorkingPlan.objects.filter(plan_name='New Project X').exists())
        self.assertContains(response, 'Working Plan added successfully.') # Check success message

    def test_create_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_plan_data = {
            'plan_name': 'New Project Y',
            'description': 'Description for Y',
            'start_date': timezone.localdate() + timedelta(days=5),
            'end_date': timezone.localdate() + timedelta(days=15),
            'status': 'Draft',
            'assigned_to': 'HTMX User'
        }
        response = self.client.post(reverse('working_plan_add'), new_plan_data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkingPlanList')
        self.assertTrue(WorkingPlan.objects.filter(plan_name='New Project Y').exists())

    def test_create_view_post_invalid(self):
        invalid_data = {
            'plan_name': 'Invalid Plan',
            'start_date': timezone.localdate() + timedelta(days=10),
            'end_date': timezone.localdate(), # End date before start date
            'status': 'Draft',
            'assigned_to': 'User'
        }
        response = self.client.post(reverse('working_plan_add'), invalid_data)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'project_planning/working_plan/_working_plan_form.html')
        self.assertFalse(WorkingPlan.objects.filter(plan_name='Invalid Plan').exists())
        self.assertContains(response, 'End date cannot be before start date.') # Check for validation error

    def test_update_view_get(self):
        response = self.client.get(reverse('working_plan_edit', args=[self.plan.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/working_plan/_working_plan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Working Plan')
        self.assertEqual(response.context['form'].instance, self.plan)

    def test_update_view_post_success(self):
        updated_data = {
            'plan_name': 'Updated Test Plan',
            'description': 'Updated Description',
            'start_date': self.plan.start_date,
            'end_date': self.plan.end_date,
            'status': 'Completed',
            'assigned_to': self.plan.assigned_to
        }
        response = self.client.post(reverse('working_plan_edit', args=[self.plan.pk]), updated_data, follow=True)
        self.assertRedirects(response, reverse('working_plan_list'))
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, 'Updated Test Plan')
        self.assertEqual(self.plan.status, 'Completed')
        self.assertContains(response, 'Working Plan updated successfully.')

    def test_update_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_data = {
            'plan_name': 'HTMX Updated Plan',
            'description': 'HTMX Description',
            'start_date': self.plan.start_date,
            'end_date': self.plan.end_date,
            'status': 'Completed',
            'assigned_to': self.plan.assigned_to
        }
        response = self.client.post(reverse('working_plan_edit', args=[self.plan.pk]), updated_data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkingPlanList')
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, 'HTMX Updated Plan')

    def test_delete_view_get(self):
        response = self.client.get(reverse('working_plan_delete', args=[self.plan.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/working_plan/_working_plan_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.plan.plan_name)

    def test_delete_view_post_success(self):
        plan_to_delete_pk = self.plan.pk
        response = self.client.post(reverse('working_plan_delete', args=[plan_to_delete_pk]), follow=True)
        self.assertRedirects(response, reverse('working_plan_list'))
        self.assertFalse(WorkingPlan.objects.filter(pk=plan_to_delete_pk).exists())
        self.assertContains(response, 'Working Plan deleted successfully.')

    def test_delete_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_plan = WorkingPlan.objects.create(
            plan_name='Temp Plan to Delete',
            description='Temp',
            start_date=timezone.localdate(),
            end_date=timezone.localdate(),
            status='Draft',
            assigned_to='Me'
        )
        plan_to_delete_pk = new_plan.pk
        response = self.client.post(reverse('working_plan_delete', args=[plan_to_delete_pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkingPlanList')
        self.assertFalse(WorkingPlan.objects.filter(pk=plan_to_delete_pk).exists())

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The generated templates, views, and URL structures already incorporate HTMX for all dynamic interactions:
-   **List View:** Uses `hx-get` to load the `working_plan_table` partial on `load` and `refreshWorkingPlanList` custom event.
-   **Modals (Add/Edit/Delete):** `hx-get` on buttons triggers loading of `_working_plan_form.html` or `_working_plan_confirm_delete.html` into a modal.
-   **Form Submissions:** `hx-post` on forms with `hx-swap="none"` and `hx-trigger="refreshWorkingPlanList"` from the view's `HttpResponse(status=204)` indicates success and updates the list.
-   **Alpine.js:** Integrated via `_` (Hyperscript) for basic modal open/close logic (`on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me`). This handles UI state without extra JavaScript.
-   **DataTables:** The `_working_plan_table.html` partial includes the JavaScript for DataTables initialization, ensuring client-side searching, sorting, and pagination are enabled once the table is loaded via HTMX.

This approach ensures a highly interactive user experience without full page reloads, aligning with modern web application standards and leveraging the power of HTMX and Alpine.js for minimal custom JavaScript.

## Final Notes

-   **Placeholders:** All `[PLACEHOLDER]` values from the original templates have been replaced with concrete names (e.g., `[MODEL_NAME]` -> `WorkingPlan`, `[FIELD1]` -> `plan_name`).
-   **DRY Templates:** Achieved through `extends 'core/base.html'` and the use of partials (`_working_plan_table.html`, `_working_plan_form.html`, `_working_plan_confirm_delete.html`) for reusable components loaded via HTMX.
-   **Fat Model, Thin Views:** Business logic like `is_current()`, `is_past_due()`, `days_remaining()`, and `mark_as_completed()` is encapsulated within the `WorkingPlan` model. Views are kept concise, primarily handling HTTP requests, rendering templates, and passing data.
-   **Comprehensive Tests:** Unit tests for model methods and integration tests for all view endpoints (including HTMX-specific interactions) are provided to ensure functionality and maintainability.
-   **Automation-Driven:** This plan outlines a systematic conversion. An AI-assisted tool could identify the boilerplate, infer the most likely application domain (Working Plan), suggest a common schema, and then automatically generate these Django files, reducing manual effort significantly. The generated code is directly runnable after setting up the Django project and database connection (assuming the existing `TblWorkingPlan` table).