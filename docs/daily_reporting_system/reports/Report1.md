## ASP.NET to Django Conversion Script: Daily Reporting System

This document outlines a comprehensive plan for migrating your existing ASP.NET `Report1.aspx` application to a modern Django-based solution. Our approach emphasizes automation, clear steps, and leveraging contemporary web technologies to deliver a robust, maintainable, and user-friendly system.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists and is correctly configured.
- Focus ONLY on component-specific code for the current module (`DailyReportingSystem`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code, we identify the following tables and their primary columns:

-   **`DRT_Sys_New`**: This is the main table for the daily report entries.
    -   **Inferred Columns:** `ID` (Primary Key), `E_name`, `Designation`, `Department`, `DOR` (Date of Report), `SALW`, `TCW`, `APC`, `APNC`, `AUC`, `PNW`, `IdDate`, `IdWo`, `IdActivity`, `IDET`, `IdStatus`, `IDperc`, `Idrmk`. Data types will be inferred based on common usage (e.g., string for names, date for dates, decimal for percentage).
-   **`tblHR_OfficeStaff`**: Used for populating the Employee Name dropdown.
    -   **Inferred Columns:** `EmployeeName`.
-   **`tblHR_Designation`**: Used for populating the Designation dropdown.
    -   **Inferred Columns:** `Type`.
-   **`tblHR_Departments`**: Used for populating the Department dropdown.
    -   **Inferred Columns:** `Description`.
-   **`tblFinancial_master`**: Used for determining the financial year's start and end dates.
    -   **Inferred Columns:** `CompId`, `FinYearId`, `FinYearFrom`, `FinYearTo`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**
The primary functionality of the provided ASP.NET page is a **Read** (reporting) operation, allowing users to filter and view daily report entries. Although explicit Create, Update, and Delete operations for `DRT_Sys_New` are not shown, they are essential for a complete system and will be included in our Django plan as per the automation guidelines.

-   **Read/Reporting:**
    -   Filtering `DRT_Sys_New` records based on:
        -   Date range (`TxtFromDate`, `TxtToDate`)
        -   Employee Name (`DropDownList3`)
        -   Designation (`DropDownList2`)
        -   Department (`DropDownList1`)
        -   General keyword search (`TextBox2` functionality, inferred)
    -   Displaying results in a tabular format (`GridView1`).
-   **Initial Data Loading:**
    -   Retrieving financial year dates from `tblFinancial_master` based on `Session` variables (`CompId`, `FinYearId`) to set default report dates.
-   **Validation:**
    -   Required fields for dates.
    -   Date format validation (`dd-MM-yyyy`).
    -   Date range validation (From Date <= To Date).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET UI components will be transformed into standard HTML inputs and selects, leveraging HTMX and Alpine.js for dynamic behavior, and DataTables for enhanced data presentation.

-   **Date Inputs:** `TxtFromDate`, `TxtToDate` (ASP.NET `TextBox` with `CalendarExtender`) -> HTML `<input type="date">`.
-   **Dropdowns:** `DropDownList3` (Employee), `DropDownList2` (Designation), `DropDownList1` (Department) (ASP.NET `DropDownList` bound to `SqlDataSource`) -> HTML `<select>` elements dynamically populated from Django models.
-   **Search Button:** `search` (ASP.NET `Button`) -> HTML `<button>` that triggers an HTMX POST request for filtering.
-   **Data Grid:** `GridView1` (ASP.NET `GridView`) -> HTML `<table>` integrated with DataTables for client-side functionality.
-   **Messages:** `lblMessage` -> Django messages framework displayed dynamically (e.g., via HTMX).
-   **Layout:** ASP.NET Master Pages map to Django template inheritance (`extends 'core/base.html'`).

---

### Step 4: Generate Django Code

The Django application will be named `dailyreporting`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
Models are defined with `managed = False` and `db_table` to map directly to your existing database tables. Business logic for filtering is included as a class method within the `DrtSysNew` model, adhering to the "Fat Model" principle.

```python
# dailyreporting/models.py
from django.db import models

class FinancialMaster(models.Model):
    # Assuming CompId and FinYearId form a unique identifier or are part of one.
    # For simplicity, let's assume `CompId` is the primary key and `FinYearId` is just a field.
    # Adjust `primary_key=True` based on your actual database schema's primary key.
    comp_id = models.IntegerField(db_column='CompId', primary_key=True) 
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    fin_year_from = models.DateField(db_column='FinYearFrom', null=True, blank=True)
    fin_year_to = models.DateField(db_column='FinYearTo', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'
        # If (CompId, FinYearId) is a composite PK, use:
        # unique_together = (('comp_id', 'fin_year_id'),) 

    def __str__(self):
        return f"FY {self.fin_year_id} ({self.fin_year_from} - {self.fin_year_to})"

class HrOfficeStaff(models.Model):
    # Assuming EmployeeName itself might be unique or there's an implicit ID.
    # If there's an ID column, it should be the primary key. For this example, we assume `EmployeeName` is unique.
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, unique=True, primary_key=True)
    
    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class HrDesignation(models.Model):
    # Assuming 'Type' is unique or an implicit ID exists.
    designation_type = models.CharField(db_column='Type', max_length=255, unique=True, primary_key=True)
    
    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.designation_type

class HrDepartment(models.Model):
    # Assuming 'Description' is unique or an implicit ID exists.
    description = models.CharField(db_column='Description', max_length=255, unique=True, primary_key=True)
    
    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class DrtSysNew(models.Model):
    drt_id = models.IntegerField(db_column='ID', primary_key=True) # Assuming 'ID' is the primary key
    e_name = models.CharField(db_column='E_name', max_length=255, null=True, blank=True)
    designation = models.CharField(db_column='Designation', max_length=255, null=True, blank=True)
    department = models.CharField(db_column='Department', max_length=255, null=True, blank=True)
    dor = models.DateField(db_column='DOR', null=True, blank=True) # Date of Report
    salw = models.CharField(db_column='SALW', max_length=255, null=True, blank=True) 
    tcw = models.CharField(db_column='TCW', max_length=255, null=True, blank=True)
    apc = models.CharField(db_column='APC', max_length=255, null=True, blank=True)
    apnc = models.CharField(db_column='APNC', max_length=255, null=True, blank=True)
    auc = models.CharField(db_column='AUC', max_length=255, null=True, blank=True)
    pnw = models.CharField(db_column='PNW', max_length=255, null=True, blank=True)
    id_date = models.DateField(db_column='IdDate', null=True, blank=True)
    id_wo = models.CharField(db_column='IdWo', max_length=255, null=True, blank=True)
    id_activity = models.CharField(db_column='IdActivity', max_length=255, null=True, blank=True)
    idet = models.CharField(db_column='IDET', max_length=255, null=True, blank=True)
    id_status = models.CharField(db_column='IdStatus', max_length=255, null=True, blank=True)
    id_perc = models.DecimalField(db_column='IDperc', max_digits=5, decimal_places=2, null=True, blank=True)
    id_rmk = models.TextField(db_column='Idrmk', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'DRT_Sys_New'
        verbose_name = 'Daily Report Entry'
        verbose_name_plural = 'Daily Report Entries'

    def __str__(self):
        return f"Report {self.drt_id} by {self.e_name or 'N/A'}"
        
    @classmethod
    def filter_reports(cls, employee_name=None, designation=None, department=None, from_date=None, to_date=None, search_query=None):
        """
        Filters DrtSysNew records based on provided criteria.
        This method encapsulates the report filtering business logic.
        """
        queryset = cls.objects.all()

        # Apply specific filters, mimicking ASP.NET dropdown logic
        if employee_name and employee_name != "ERP System": # Mimic ASP.NET default "ERP System"
            queryset = queryset.filter(e_name__icontains=employee_name)
        if designation and designation != "Not Applicable": # Mimic ASP.NET default "Not Applicable"
            queryset = queryset.filter(designation__icontains=designation)
        if department and department != "Not Applicable": # Mimic ASP.NET default "Not Applicable"
            queryset = queryset.filter(department__icontains=department)
        
        # Apply date range filter
        if from_date:
            queryset = queryset.filter(dor__gte=from_date)
        if to_date:
            queryset = queryset.filter(dor__lte=to_date)

        # Apply general keyword search (inferred from TextBox2)
        if search_query:
            from django.db.models import Q
            queryset = queryset.filter(
                Q(e_name__icontains=search_query) |
                Q(designation__icontains=search_query) |
                Q(department__icontains=search_query) |
                Q(salw__icontains=search_query) |
                Q(tcw__icontains=search_query) |
                Q(apc__icontains=search_query) |
                Q(apnc__icontains=search_query) |
                Q(auc__icontains=search_query) |
                Q(pnw__icontains=search_query) |
                Q(id_wo__icontains=search_query) |
                Q(id_activity__icontains=search_query) |
                Q(idet__icontains=search_query) |
                Q(id_status__icontains=search_query) |
                Q(id_rmk__icontains=search_query)
            )

        return queryset.order_by('-dor') # Default ordering by report date
```

#### 4.2 Forms

**Task:** Define Django forms for user input, including the main report entry form and a separate filter form.

**Instructions:**
`DrtSysNewForm` will be used for CRUD operations, and `ReportFilterForm` for the main report's search/filter functionality. Widgets are styled with Tailwind CSS classes. Custom validation ensures date range integrity.

```python
# dailyreporting/forms.py
from django import forms
from .models import DrtSysNew, HrOfficeStaff, HrDesignation, HrDepartment
import datetime

class DrtSysNewForm(forms.ModelForm):
    class Meta:
        model = DrtSysNew
        fields = ['e_name', 'designation', 'department', 'dor', 'salw', 'tcw', 'apc', 'apnc', 'auc', 'pnw', 'id_date', 'id_wo', 'id_activity', 'idet', 'id_status', 'id_perc', 'id_rmk']
        widgets = {
            'e_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'designation': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'department': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'dor': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'salw': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tcw': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'apc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'apnc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'auc': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pnw': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'id_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'id_wo': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'id_activity': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idet': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'id_status': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'id_perc': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'id_rmk': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }

class ReportFilterForm(forms.Form):
    from_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label='From Date'
    )
    to_date = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label='To Date'
    )
    employee_name = forms.ModelChoiceField(
        queryset=HrOfficeStaff.objects.all(),
        to_field_name='employee_name',
        empty_label="ERP System", # Mimics ASP.NET default dropdown value
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    designation = forms.ModelChoiceField(
        queryset=HrDesignation.objects.all(),
        to_field_name='designation_type',
        empty_label="Not Applicable", # Mimics ASP.NET default dropdown value
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    department = forms.ModelChoiceField(
        queryset=HrDepartment.objects.all(),
        to_field_name='description',
        empty_label="Not Applicable", # Mimics ASP.NET default dropdown value
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    search_query = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={'placeholder': 'Enter keyword', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label='Keyword Search'
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('from_date', 'From date cannot be after To date.')
            self.add_error('to_date', 'To date cannot be before From date.')
        return cleaned_data
```

#### 4.3 Views

**Task:** Implement CRUD operations and the reporting view using Django Class-Based Views (CBVs).

**Instructions:**
Views are kept "thin," delegating complex logic (like report filtering) to the model. HTMX headers are used to manage partial updates and trigger front-end events.

```python
# dailyreporting/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import DrtSysNew, FinancialMaster
from .forms import DrtSysNewForm, ReportFilterForm
from datetime import date

class DrtSysNewListView(ListView):
    model = DrtSysNew
    template_name = 'dailyreporting/drtsysnew/list.html'
    context_object_name = 'drtsysnews'

    def get_queryset(self):
        # Initial queryset; actual filtering happens via HTMX requests to DrtSysNewTablePartialView
        return DrtSysNew.objects.all().order_by('-dor') # Default ordering for display

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize filter form with default values (e.g., current financial year)
        if 'filter_form' not in context:
            initial_from_date = None
            initial_to_date = date.today() # Default to current date for 'To Date'
            try:
                # Attempt to retrieve financial year dates, e.g., for the first company/year
                # In a real app, CompId/FinYearId would come from user session/profile
                fin_year_data = FinancialMaster.objects.first() 
                if fin_year_data:
                    initial_from_date = fin_year_data.fin_year_from
                    if fin_year_data.fin_year_to: # Only overwrite if FinYearTo is valid
                        initial_to_date = fin_year_data.fin_year_to
            except FinancialMaster.DoesNotExist:
                pass # No financial year data found, use default empty form

            context['filter_form'] = ReportFilterForm(initial={
                'from_date': initial_from_date,
                'to_date': initial_to_date,
            })
        return context

class DrtSysNewTablePartialView(View):
    """
    Handles HTMX requests for rendering and filtering the DataTable content.
    """
    def get(self, request, *args, **kwargs):
        form = ReportFilterForm(request.GET) # Bind GET data for initial load/refresh via HTMX
        drtsysnews = DrtSysNew.objects.all() # Default if form not valid

        if form.is_valid():
            drtsysnews = DrtSysNew.filter_reports(
                employee_name=form.cleaned_data.get('employee_name'),
                designation=form.cleaned_data.get('designation'),
                department=form.cleaned_data.get('department'),
                from_date=form.cleaned_data.get('from_date'),
                to_date=form.cleaned_data.get('to_date'),
                search_query=form.cleaned_data.get('search_query')
            )
        
        context = {'drtsysnews': drtsysnews}
        return render(request, 'dailyreporting/drtsysnew/_drtsysnew_table.html', context)

    def post(self, request, *args, **kwargs):
        """
        Handles POST requests from the filter form to refresh the table.
        It re-renders the table partial and sends it back.
        """
        form = ReportFilterForm(request.POST) # Bind POST data for filter submission
        drtsysnews = DrtSysNew.objects.all() # Default if form not valid

        if form.is_valid():
            drtsysnews = DrtSysNew.filter_reports(
                employee_name=form.cleaned_data.get('employee_name'),
                designation=form.cleaned_data.get('designation'),
                department=form.cleaned_data.get('department'),
                from_date=form.cleaned_data.get('from_date'),
                to_date=form.cleaned_data.get('to_date'),
                search_query=form.cleaned_data.get('search_query')
            )
        else:
            # If form is invalid, return the form with errors, potentially with an empty queryset
            messages.error(request, 'Please correct the errors in the filter form.')
            # To display form errors in the partial, you might need to render the filter form part
            # or handle errors on the client side with Alpine.js
            # For simplicity here, we will render an empty table and let the main list view handle form errors.
            drtsysnews = DrtSysNew.objects.none() # Return empty queryset on invalid filter

        context = {'drtsysnews': drtsysnews} # Pass form as well if you want to retain state or show errors
        return render(request, 'dailyreporting/drtsysnew/_drtsysnew_table.html', context)


class DrtSysNewCreateView(CreateView):
    model = DrtSysNew
    form_class = DrtSysNewForm
    template_name = 'dailyreporting/drtsysnew/form.html'
    success_url = reverse_lazy('drtsysnew_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Daily Report Entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # HTMX expects 204 No Content for successful updates that don't swap content
                headers={
                    'HX-Trigger': 'refreshDrtSysNewList' # Custom HTMX event to refresh the list
                }
            )
        return response

class DrtSysNewUpdateView(UpdateView):
    model = DrtSysNew
    form_class = DrtSysNewForm
    template_name = 'dailyreporting/drtsysnew/form.html'
    success_url = reverse_lazy('drtsysnew_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Daily Report Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDrtSysNewList'
                }
            )
        return response

class DrtSysNewDeleteView(DeleteView):
    model = DrtSysNew
    template_name = 'dailyreporting/drtsysnew/confirm_delete.html'
    success_url = reverse_lazy('drtsysnew_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Daily Report Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDrtSysNewList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, including partials for HTMX-driven content.

**Instructions:**
Templates follow DRY principles by extending `core/base.html`. HTMX attributes manage dynamic loading, form submissions, and modal interactions. DataTables is initialized for the main list view.

```html
{# dailyreporting/drtsysnew/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Daily Report Entries</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'drtsysnew_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Alpine.js/Hyperscript for modal toggle #}
            Add New Daily Report
        </button>
    </div>

    <!-- Filter Form -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Reports</h3>
        <form hx-post="{% url 'drtsysnew_table' %}" 
              hx-target="#drtsysnewTable-container" 
              hx-swap="innerHTML" 
              class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
              _="on submit wait for end of htmx.process then trigger refreshDrtSysNewList from #drtsysnewTable-container">
            {% csrf_token %}
            {% for field in filter_form %}
            <div class="mb-2">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
            <div class="col-span-full mt-4 flex justify-end">
                <button 
                    type="submit" 
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Apply Filter
                </button>
            </div>
        </form>
    </div>
    
    <div id="drtsysnewTable-container"
         hx-trigger="load, refreshDrtSysNewList from:body" {# Loads on page load, refreshes on custom event #}
         hx-get="{% url 'drtsysnew_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad(evt) if evt.detail.xhr.status != 204 remove .hidden from #modal"> {# Show modal when content is loaded, if not 204 #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# DataTables and HTMX related JavaScript #}
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.tailwindcss.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization can go here for any specific UI state
        // For example, managing local state for filter visibility or input focus.
    });

    // Re-initialize DataTable after HTMX swaps in new content
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'drtsysnewTable-container') {
            $('#drtsysnewTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true, // Essential to destroy existing DataTable instance before re-initializing
                "autoWidth": false // Prevents column width issues
            });
        }
    });

    // Close modal after successful HTMX form submission (status 204 with HX-Trigger)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.xhr.getResponseHeader('HX-Trigger') === 'refreshDrtSysNewList') {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active'); // Hyperscript class
                modal.classList.add('hidden'); // Tailwind class
                document.getElementById('modalContent').innerHTML = ''; // Clear modal content
            }
        }
    });
</script>
{% endblock %}

{% block extra_css %}
{# DataTables CSS #}
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

```html
{# dailyreporting/drtsysnew/_drtsysnew_table.html #}
{# This template is a partial loaded via HTMX #}
<div class="overflow-x-auto">
    <table id="drtsysnewTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Designation</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Date</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in drtsysnews %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.e_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.designation }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.department }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.dor|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.id_status }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.id_rmk|truncatechars:50 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'drtsysnew_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'drtsysnew_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-gray-500">No daily report entries found with current filters.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTable initialization handled in list.html after HTMX swap #}
```

```html
{# dailyreporting/drtsysnew/form.html #}
{# This template is a partial loaded into a modal via HTMX #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Daily Report Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" important for 204 No Content response #}
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-2">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Hyperscript to close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

```html
{# dailyreporting/drtsysnew/confirm_delete.html #}
{# This template is a partial loaded into a modal via HTMX #}
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="mb-4 text-gray-700">Are you sure you want to delete the daily report entry: <strong>"{{ drtsysnew }}"</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views, including paths for HTMX partials.

**Instructions:**
A clear URL structure is established, mapping to the Django views for list, create, update, delete, and the HTMX-specific table partial.

```python
# dailyreporting/urls.py
from django.urls import path
from .views import DrtSysNewListView, DrtSysNewCreateView, DrtSysNewUpdateView, DrtSysNewDeleteView, DrtSysNewTablePartialView

urlpatterns = [
    # Main report list view
    path('reports/daily/', DrtSysNewListView.as_view(), name='drtsysnew_list'),
    
    # HTMX endpoint for rendering the table content (with filters)
    path('reports/daily/table/', DrtSysNewTablePartialView.as_view(), name='drtsysnew_table'),

    # CRUD operations for DrtSysNew (modal forms via HTMX)
    path('reports/daily/add/', DrtSysNewCreateView.as_view(), name='drtsysnew_add'),
    path('reports/daily/edit/<int:pk>/', DrtSysNewUpdateView.as_view(), name='drtsysnew_edit'),
    path('reports/daily/delete/<int:pk>/', DrtSysNewDeleteView.as_view(), name='drtsysnew_delete'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the models and views to ensure functionality and maintain code quality.

**Instructions:**
Unit tests cover model logic, and integration tests verify view behavior, including HTMX interactions and form submissions. Aim for high test coverage (80%+).

```python
# dailyreporting/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import DrtSysNew, HrOfficeStaff, HrDesignation, HrDepartment, FinancialMaster
import datetime

class FinancialMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialMaster.objects.create(
            comp_id=1,
            fin_year_id=2023,
            fin_year_from=datetime.date(2023, 4, 1),
            fin_year_to=datetime.date(2024, 3, 31)
        )

    def test_financial_master_creation(self):
        obj = FinancialMaster.objects.get(comp_id=1) # Assuming comp_id is PK
        self.assertEqual(obj.fin_year_from, datetime.date(2023, 4, 1))
        self.assertEqual(obj.fin_year_to, datetime.date(2024, 3, 31))
        self.assertEqual(str(obj), "FY 2023 (2023-04-01 - 2024-03-31)")

class HrOfficeStaffModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        HrOfficeStaff.objects.create(employee_name='John Doe')
        HrOfficeStaff.objects.create(employee_name='Jane Smith')

    def test_hr_office_staff_creation(self):
        obj = HrOfficeStaff.objects.get(employee_name='John Doe')
        self.assertEqual(obj.employee_name, 'John Doe')
        self.assertEqual(str(obj), 'John Doe')

class HrDesignationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        HrDesignation.objects.create(designation_type='Manager')
        HrDesignation.objects.create(designation_type='Developer')

    def test_hr_designation_creation(self):
        obj = HrDesignation.objects.get(designation_type='Manager')
        self.assertEqual(obj.designation_type, 'Manager')
        self.assertEqual(str(obj), 'Manager')

class HrDepartmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        HrDepartment.objects.create(description='IT')
        HrDepartment.objects.create(description='HR')

    def test_hr_department_creation(self):
        obj = HrDepartment.objects.get(description='IT')
        self.assertEqual(obj.description, 'IT')
        self.assertEqual(str(obj), 'IT')

class DrtSysNewModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        DrtSysNew.objects.create(
            drt_id=1, e_name='Employee A', designation='Type X', department='Dept 1',
            dor=datetime.date(2023, 1, 15), salw='Data1', tcw='Data2', apc='Data3', apnc='Data4',
            auc='Data5', pnw='Data6', id_date=datetime.date(2023, 1, 10), id_wo='WO-001',
            id_activity='Coding', idet='Task', id_status='Completed', id_perc=95.50, id_rmk='Initial report'
        )
        DrtSysNew.objects.create(
            drt_id=2, e_name='Employee B', designation='Type Y', department='Dept 2',
            dor=datetime.date(2023, 1, 20), salw='Data7', tcw='Data8', apc='Data9', apnc='Data10',
            auc='Data11', pnw='Data12', id_date=datetime.date(2023, 1, 18), id_wo='WO-002',
            id_activity='Testing', idet='Bugfix', id_status='In Progress', id_perc=50.00, id_rmk='Mid-report'
        )
        DrtSysNew.objects.create(
            drt_id=3, e_name='Employee A', designation='Type Z', department='Dept 1',
            dor=datetime.date(2023, 2, 10), salw='Data13', tcw='Data14', apc='Data15', apnc='Data16',
            auc='Data17', pnw='Data18', id_date=datetime.date(2023, 2, 5), id_wo='WO-003',
            id_activity='Review', idet='Meeting', id_status='Pending', id_perc=20.00, id_rmk='Final report'
        )

    def test_drtsysnew_creation(self):
        obj = DrtSysNew.objects.get(drt_id=1)
        self.assertEqual(obj.e_name, 'Employee A')
        self.assertEqual(obj.designation, 'Type X')
        self.assertEqual(obj.dor, datetime.date(2023, 1, 15))
        self.assertEqual(str(obj), "Report 1 by Employee A")

    def test_filter_reports_employee_name(self):
        reports = DrtSysNew.filter_reports(employee_name='Employee A')
        self.assertEqual(reports.count(), 2)
        self.assertTrue(reports.filter(drt_id=1).exists())
        self.assertTrue(reports.filter(drt_id=3).exists())

    def test_filter_reports_date_range(self):
        reports = DrtSysNew.filter_reports(
            from_date=datetime.date(2023, 1, 1),
            to_date=datetime.date(2023, 1, 31)
        )
        self.assertEqual(reports.count(), 2)
        self.assertTrue(reports.filter(drt_id=1).exists())
        self.assertTrue(reports.filter(drt_id=2).exists())

    def test_filter_reports_with_search_query(self):
        reports = DrtSysNew.filter_reports(search_query='Coding')
        self.assertEqual(reports.count(), 1)
        self.assertTrue(reports.filter(drt_id=1).exists())

    def test_filter_reports_all_criteria(self):
        reports = DrtSysNew.filter_reports(
            employee_name='Employee A',
            department='Dept 1',
            from_date=datetime.date(2023, 1, 1),
            to_date=datetime.date(2023, 1, 31),
            search_query='Initial'
        )
        self.assertEqual(reports.count(), 1)
        self.assertTrue(reports.filter(drt_id=1).exists())

class DrtSysNewViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data for forms
        HrOfficeStaff.objects.create(employee_name='Employee A')
        HrOfficeStaff.objects.create(employee_name='Employee B')
        HrDesignation.objects.create(designation_type='Manager')
        HrDepartment.objects.create(description='IT')
        FinancialMaster.objects.create(
            comp_id=1,
            fin_year_id=2023,
            fin_year_from=datetime.date(2023, 4, 1),
            fin_year_to=datetime.date(2024, 3, 31)
        )

        cls.obj1 = DrtSysNew.objects.create(
            drt_id=101, e_name='Initial Employee', designation='Manager', department='IT',
            dor=datetime.date(2023, 5, 1), salw='S1', tcw='T1', apc='A1', apnc='AP1',
            auc='AU1', pnw='PN1', id_date=datetime.date(2023, 4, 25), id_wo='INIT-001',
            id_activity='Init', idet='Init Type', id_status='New', id_perc=10.00, id_rmk='Initial record'
        )
        cls.obj2 = DrtSysNew.objects.create(
            drt_id=102, e_name='Another Employee', designation='Manager', department='IT',
            dor=datetime.date(2023, 5, 5), salw='S2', tcw='T2', apc='A2', apnc='AP2',
            auc='AU2', pnw='PN2', id_date=datetime.date(2023, 5, 1), id_wo='INIT-002',
            id_activity='Another', idet='Another Type', id_status='Pending', id_perc=60.00, id_rmk='Another record'
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('drtsysnew_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/drtsysnew/list.html')
        self.assertTrue('drtsysnews' in response.context)
        self.assertIsInstance(response.context['filter_form'], ReportFilterForm)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('drtsysnew_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/drtsysnew/_drtsysnew_table.html')
        self.assertTrue('drtsysnews' in response.context)
        self.assertContains(response, 'Initial Employee')

    def test_table_partial_view_post_filter(self):
        data = {
            'employee_name': 'Initial Employee',
            'from_date': '2023-05-01',
            'to_date': '2023-05-01'
        }
        response = self.client.post(reverse('drtsysnew_table'), data)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/drtsysnew/_drtsysnew_table.html')
        self.assertContains(response, 'Initial Employee')
        self.assertNotContains(response, 'Another Employee')
        
    def test_create_view_get(self):
        response = self.client.get(reverse('drtsysnew_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/drtsysnew/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post(self):
        initial_count = DrtSysNew.objects.count()
        data = {
            'drt_id': 103, # New unique ID for creation
            'e_name': 'New Employee', 'designation': 'Manager', 'department': 'IT',
            'dor': '2023-06-01', 'salw': 'S3', 'tcw': 'T3', 'apc': 'A3', 'apnc': 'AP3',
            'auc': 'AU3', 'pnw': 'PN3', 'id_date': '2023-05-28', 'id_wo': 'NEW-001',
            'id_activity': 'New Task', 'idet': 'New Event', 'id_status': 'Planned',
            'id_perc': 0.00, 'id_rmk': 'New record'
        }
        response = self.client.post(reverse('drtsysnew_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertEqual(DrtSysNew.objects.count(), initial_count + 1)
        self.assertTrue(DrtSysNew.objects.filter(e_name='New Employee').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDrtSysNewList')
        
    def test_update_view_get(self):
        obj = self.obj1
        response = self.client.get(reverse('drtsysnew_edit', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/drtsysnew/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.e_name, 'Initial Employee')
        
    def test_update_view_post(self):
        obj = self.obj1
        updated_name = 'Updated Employee'
        data = {
            'drt_id': obj.drt_id, # PK must be passed back for update
            'e_name': updated_name, 'designation': obj.designation, 'department': obj.department,
            'dor': obj.dor.strftime('%Y-%m-%d'), 'salw': obj.salw, 'tcw': obj.tcw, 'apc': obj.apc, 'apnc': obj.apnc,
            'auc': obj.auc, 'pnw': obj.pnw, 'id_date': obj.id_date.strftime('%Y-%m-%d'), 'id_wo': obj.id_wo,
            'id_activity': obj.id_activity, 'idet': obj.idet, 'id_status': obj.id_status,
            'id_perc': obj.id_perc, 'id_rmk': obj.id_rmk
        }
        response = self.client.post(reverse('drtsysnew_edit', args=[obj.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.e_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDrtSysNewList')

    def test_delete_view_get(self):
        obj = self.obj1
        response = self.client.get(reverse('drtsysnew_delete', args=[obj.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/drtsysnew/confirm_delete.html')
        self.assertTrue('drtsysnew' in response.context)
        self.assertEqual(response.context['drtsysnew'].pk, obj.pk)
        
    def test_delete_view_post(self):
        obj_to_delete = DrtSysNew.objects.create(drt_id=999, e_name='Temp Delete')
        initial_count = DrtSysNew.objects.count()
        response = self.client.post(reverse('drtsysnew_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(DrtSysNew.objects.count(), initial_count - 1)
        self.assertFalse(DrtSysNew.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDrtSysNewList')

    def test_invalid_filter_date_range(self):
        data = {
            'from_date': '2023-06-01', # After to_date
            'to_date': '2023-05-01'
        }
        response = self.client.post(reverse('drtsysnew_table'), data)
        self.assertEqual(response.status_code, 200)
        # Check for error messages returned within the partial
        self.assertContains(response, 'From date cannot be after To date.')
        self.assertContains(response, 'To date cannot be before From date.')
        # Ensure that no data is displayed as the filter is invalid
        self.assertContains(response, 'No daily report entries found with current filters.')
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
HTMX and Alpine.js are crucial for delivering a modern, dynamic user experience without requiring full page reloads or complex JavaScript frameworks.

-   **HTMX for Dynamic Content:**
    -   The `drtsysnew_table` URL is an HTMX endpoint responsible for rendering only the table data (`_drtsysnew_table.html`).
    -   The main list page (`list.html`) uses `hx-get` to load this table on initial page load and `hx-trigger="refreshDrtSysNewList from:body"` to refresh it after any CRUD operation.
    -   Filter form submissions use `hx-post` to send data to `drtsysnew_table` and update the table `div`.
    -   Buttons for Add, Edit, Delete are configured with `hx-get` to fetch partial form/confirmation templates into a modal, targeting `#modalContent`.
    -   Successful form submissions (Add/Edit/Delete) return a `204 No Content` response with an `HX-Trigger` header (`refreshDrtSysNewList`) to inform the client to refresh the table.
-   **Alpine.js for UI State:**
    -   Used with Hyperscript (`_`) for simple UI toggles, like showing/hiding the modal. The modal state (`.is-active` class) is managed directly in the template.
-   **DataTables for List Views:**
    -   DataTables is initialized in `list.html` within a `htmx:afterSwap` event listener. This ensures that whenever HTMX replaces the table content, DataTables is re-applied to the new DOM, providing client-side sorting, searching, and pagination.
-   **No Full Page Reloads:** All user interactions (filtering, adding, editing, deleting) occur seamlessly without full page reloads, thanks to HTMX.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Daily Reporting System to Django. By adhering to the principles of "Fat Model, Thin View," leveraging HTMX for dynamic interactions, DataTables for enhanced data presentation, and focusing on automated, test-driven development, you will achieve a modern, efficient, and maintainable application. Remember to replace placeholders (e.g., `[FIELD_TYPE]`, `[TABLE_NAME]`) with accurate values based on your actual database schema and application specifics.