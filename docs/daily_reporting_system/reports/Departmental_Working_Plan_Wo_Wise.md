## ASP.NET to Django Conversion Script: Departmental Working Plan Report

This document outlines a strategic plan to modernize your ASP.NET Departmental Working Plan Report application into a robust, scalable, and maintainable Django solution. We will leverage AI-assisted automation to systematically convert components, focusing on a "Fat Model, Thin View" architecture, HTMX for dynamic interactions, Alpine.js for lightweight UI state, and DataTables for enhanced data presentation.

This approach prioritizes efficient development and future-proofing, allowing your team to focus on business logic rather than boilerplate code.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include `base.html` code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET `SqlDataSource` elements, we identify the following tables and their implied columns:

*   **`tblHR_Departments`**
    *   `Description`: (Text, used for dropdown display)
*   **`DRT_Sys_New`**
    *   `Department`: (Text, matches `tblHR_Departments.Description`)
    *   `IdWo`: (Text/Varchar, Work Order ID)
    *   `IDperc`: (Numeric, Percentage Completed)
    *   `IdStatus`: (Text/Varchar, Status)
    *   `IdActivity`: (Text/Varchar, Activity Description)
    *   `E_name`: (Text/Varchar, Employee Name)
    *   `IdDate`: (Date, Report Date)
    *   `Idrmk`: (Text/Varchar, Remarks)

### Step 2: Identify Backend Functionality

The ASP.NET application primarily provides a **Read/Report** functionality with dynamic filtering:

*   **Read (Display Data):** The `asp:GridView` (`SearchGridView1`) displays data from the `DRT_Sys_New` table.
*   **Filtering by Department:** The `department` dropdown filters the `DRT_Sys_New` data based on the selected `Department`. This action (`OnSelectedIndexChanged`) reloads the data.
*   **Search by Category and Value:** The `D_cat` dropdown (categories: WoNo, Name, Date) combined with an unnamed `textbox` and `TxtSearchValue` button allows searching within `DRT_Sys_New` based on the chosen category and input value.
*   **Paging & Sorting:** The `GridView` supports pagination and sorting, which will be handled by DataTables in Django.

There are no explicit Create, Update, or Delete operations visible in the provided ASP.NET code snippet. The primary focus for modernization will be the robust filtering and display of reports.

### Step 3: Infer UI Components

The ASP.NET page's UI elements translate directly to standard HTML and Django template components:

*   **Page Title/Heading:** A prominent label for "DEPARTMENTAL WORKING PLAN".
*   **Department Filter:** A dropdown list (`asp:dropdownlist department`) populated from `tblHR_Departments`. This will be an HTML `<select>` element.
*   **Search Category Dropdown:** A dropdown list (`asp:dropdownlist D_cat`) with static options ("WoNo", "Name", "Date"). This will also be an HTML `<select>` element.
*   **Search Value Input:** A text input field (`asp:textbox`). This will be an HTML `<input type="text">`.
*   **Search Button:** A button (`asp:Button TxtSearchValue`) to trigger the search. This will be an HTML `<button>`.
*   **Data Table:** The `asp:GridView` will be replaced by an HTML `<table>` element enhanced with DataTables for client-side features (pagination, sorting, search). The columns will include "SrNo", "Department", "IdWo", "IDperc", "IdStatus", "IdActivity", "Idrmk", "IdDate".

---

### Step 4: Generate Django Code

We will create a new Django application named `dailyreport` to house these components.

#### 4.1 Models (`dailyreport/models.py`)

We'll define two models, `Department` and `DailyReport`, mapping them directly to the existing database tables using `managed = False`.

```python
from django.db import models

class Department(models.Model):
    """
    Maps to the existing tblHR_Departments table.
    Used to populate the department filter dropdown.
    """
    description = models.CharField(db_column='Description', max_length=255, primary_key=True) # Assuming Description is unique/PK
    # Add other fields if tblHR_Departments has more columns

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class DailyReport(models.Model):
    """
    Maps to the existing DRT_Sys_New table.
    Represents a daily working plan entry.
    """
    # Assuming an implicit ID field if not specified, otherwise remove primary_key=True
    # For managed=False, Django usually needs a primary key explicitly stated or inferred.
    # If there's no single PK, a composite key can be inferred by Django, or an auto-incrementing ID can be assumed.
    # Based on DataKeyNames="IdWo,E_name,IdDate", this suggests a composite key or a missing explicit PK.
    # For simplicity, let's assume an 'id' column or that Django will infer one if suitable.
    # If 'IdWo' is guaranteed unique, it could be a primary key. For existing databases, it's safer to let Django infer or create a synthetic PK.
    # If the original database truly has no PK, Django will warn and behave uniquely.
    # For now, let's assume a hidden 'id' column or composite key where unique combinations are used for identification.

    # Primary key from DataKeyNames implies unique combination. For simplicity in Django ORM,
    # let's map existing fields and Django will likely infer an 'id' for CRUD operations
    # if it doesn't find a single primary_key=True. If a specific column IS the PK, set it.
    # Given the ASP.NET code doesn't show a clear PK column, we'll let Django infer an 'id' based on
    # typical `managed = False` behavior, or if necessary, define an explicit `pk` column if one exists.
    # For this example, we'll assume a composite key behavior or that Django handles it.

    department = models.CharField(db_column='Department', max_length=255)
    work_order_id = models.CharField(db_column='IdWo', max_length=255)
    percentage_completed = models.DecimalField(db_column='IDperc', max_digits=5, decimal_places=2) # Assuming percentage up to 999.99
    status = models.CharField(db_column='IdStatus', max_length=255)
    activity = models.CharField(db_column='IdActivity', max_length=255)
    employee_name = models.CharField(db_column='E_name', max_length=255)
    report_date = models.DateField(db_column='IdDate')
    remarks = models.CharField(db_column='Idrmk', max_length=500, blank=True, null=True) # Assuming string, might be TextField

    class Meta:
        managed = False
        db_table = 'DRT_Sys_New'
        # The DataKeyNames in ASP.NET (IdWo, E_name, IdDate) imply a composite key.
        # In Django, this is represented by unique_together.
        unique_together = (('work_order_id', 'employee_name', 'report_date'),)
        verbose_name = 'Daily Report Entry'
        verbose_name_plural = 'Daily Report Entries'

    def __str__(self):
        return f"{self.work_order_id} - {self.employee_name} ({self.report_date})"

    @classmethod
    def get_filtered_reports(cls, department_name=None, search_category=None, search_value=None):
        """
        Retrieves daily reports based on filter criteria.
        This business logic is placed in the model, adhering to 'fat model'.
        """
        queryset = cls.objects.all()

        if department_name:
            queryset = queryset.filter(department=department_name)

        if search_value and search_category:
            if search_category == 'WoNo':
                queryset = queryset.filter(work_order_id__icontains=search_value)
            elif search_category == 'Name':
                queryset = queryset.filter(employee_name__icontains=search_value)
            elif search_category == 'Date':
                # Attempt to parse date; handle potential errors gracefully
                try:
                    # Assuming search_value could be 'YYYY-MM-DD' for date matching
                    # Or it could be a partial match on year/month/day.
                    # For a strict match, use exact. For contains, use __icontains on string repr.
                    # Or use __year, __month, __day for more robust date filtering.
                    # Let's assume exact date string for now for simplicity, or handle parsing.
                    # Example: `report_date__icontains=search_value` for partial string search,
                    # or `report_date=datetime.strptime(search_value, '%Y-%m-%d').date()` for exact date.
                    # For production, robust date parsing and validation is essential.
                    from datetime import datetime
                    parsed_date = datetime.strptime(search_value, '%Y-%m-%d').date()
                    queryset = queryset.filter(report_date=parsed_date)
                except ValueError:
                    # If date parsing fails, log or ignore, or return empty queryset
                    queryset = queryset.none() # Or log error and continue with other filters
            # Ensure search_value is only applied if category is matched
            else:
                pass # No category, no specific search filter

        return queryset.order_by('report_date', 'department', 'work_order_id') # Default ordering
```

#### 4.2 Forms (`dailyreport/forms.py`)

We'll define a simple form for the filtering logic, as it's not directly tied to a model for CRUD.

```python
from django import forms
from .models import Department

class DailyReportFilterForm(forms.Form):
    """
    Form for filtering DailyReport entries.
    """
    department = forms.ModelChoiceField(
        queryset=Department.objects.all(),
        to_field_name='description', # Use 'description' as the value for the option
        empty_label="All Departments",
        required=False,
        label="Select Department",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm', 'hx-get': 'hx-current-url', 'hx-target': '#dailyreport-table-container', 'hx-swap': 'innerHTML', 'hx-trigger': 'change, refreshDailyReportList from:body', 'hx-indicator': '#loading-indicator'})
    )
    search_category = forms.ChoiceField(
        choices=[
            ('WoNo', 'Work Order No.'),
            ('Name', 'Employee Name'),
            ('Date', 'Report Date (YYYY-MM-DD)'),
        ],
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )
    search_value = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm'})
    )

    # Note: No ModelForm is used as this form is purely for filtering, not creating/updating a model instance.
```

#### 4.3 Views (`dailyreport/views.py`)

We'll implement a `ListView` for the main page and a `TemplateView` (or specialized `ListView`) for the HTMX-loaded table partial. As no CRUD operations were explicitly shown, we'll provide the standard Create/Update/Delete views as placeholders, but the core functionality implemented is the report viewing.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DailyReport, Department
from .forms import DailyReportFilterForm
from django.db.models import Q # For complex queries if needed

class DailyReportListView(ListView):
    """
    Main view to display the daily reports and filtering controls.
    """
    model = DailyReport
    template_name = 'dailyreport/dailyreport_list.html'
    context_object_name = 'daily_reports' # Will be used in _daily_report_table.html
    paginate_by = 16 # Matches ASP.NET GridView PageSize

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the filter form with current GET parameters
        context['filter_form'] = DailyReportFilterForm(self.request.GET)
        context['departments'] = Department.objects.all().order_by('description')
        return context
    
    def get_queryset(self):
        # This method is primarily for the initial load of the main list view.
        # The HTMX partial view will handle its own queryset more dynamically.
        form = DailyReportFilterForm(self.request.GET)
        if form.is_valid():
            department_obj = form.cleaned_data.get('department')
            department_name = department_obj.description if department_obj else None
            search_category = form.cleaned_data.get('search_category')
            search_value = form.cleaned_data.get('search_value')
            
            # Delegate filtering logic to the model
            return DailyReport.get_filtered_reports(
                department_name=department_name,
                search_category=search_category,
                search_value=search_value
            )
        return DailyReport.objects.all() # Fallback

class DailyReportTablePartialView(ListView):
    """
    Renders only the table content, to be loaded via HTMX.
    """
    model = DailyReport
    template_name = 'dailyreport/_dailyreport_table.html'
    context_object_name = 'daily_reports'
    paginate_by = 16 # Matches ASP.NET GridView PageSize

    def get_queryset(self):
        # Retrieve filter parameters from GET request (sent by HTMX)
        department_name = self.request.GET.get('department')
        search_category = self.request.GET.get('search_category')
        search_value = self.request.GET.get('search_value')
        
        # In HTMX context, department value will be the description text directly from select
        department_obj = Department.objects.filter(description=department_name).first()
        department_name_clean = department_obj.description if department_obj else None

        # Delegate filtering logic to the model
        return DailyReport.get_filtered_reports(
            department_name=department_name_clean,
            search_category=search_category,
            search_value=search_value
        )
    
    # Keeping generic CRUD views as per template, though not strictly required by original code
    # (These views would typically use a ModelForm if there were CUD operations)
class DailyReportCreateView(CreateView):
    model = DailyReport
    # Placeholder form as no creation logic was present in ASP.NET
    # For actual implementation, a form mapping to DailyReport fields would be needed.
    fields = ['department', 'work_order_id', 'percentage_completed', 'status', 'activity', 'employee_name', 'report_date', 'remarks']
    template_name = 'dailyreport/_dailyreport_form.html'
    success_url = reverse_lazy('dailyreport_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Daily Report entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDailyReportList'
                }
            )
        return response

class DailyReportUpdateView(UpdateView):
    model = DailyReport
    # Placeholder form as no update logic was present in ASP.NET
    fields = ['department', 'work_order_id', 'percentage_completed', 'status', 'activity', 'employee_name', 'report_date', 'remarks']
    template_name = 'dailyreport/_dailyreport_form.html'
    success_url = reverse_lazy('dailyreport_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Daily Report entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDailyReportList'
                }
            )
        return response

class DailyReportDeleteView(DeleteView):
    model = DailyReport
    template_name = 'dailyreport/_dailyreport_confirm_delete.html'
    success_url = reverse_lazy('dailyreport_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Daily Report entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDailyReportList'
                }
            )
        return response
```

#### 4.4 Templates (`dailyreport/templates/dailyreport/`)

**`dailyreport/dailyreport_list.html`** (Main page, extends `core/base.html`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">DEPARTMENTAL WORKING PLAN</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'dailyreport_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then transition opacity to 1 over 0.3s">
            Add New Report Entry
        </button>
    </div>

    <!-- Filter/Search Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form hx-get="{% url 'dailyreport_table' %}" 
              hx-target="#dailyreport-table-container" 
              hx-swap="innerHTML" 
              hx-trigger="submit, change from:#id_department, refreshDailyReportList from:body"
              class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end"
              id="filter-form">
            
            <div class="col-span-1">
                <label for="id_department" class="block text-sm font-medium text-gray-700">Select Department</label>
                <select name="department" id="id_department" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">All Departments</option>
                    {% for dept in departments %}
                    <option value="{{ dept.description }}" {% if request.GET.department == dept.description %}selected{% endif %}>
                        {{ dept.description }}
                    </option>
                    {% endfor %}
                </select>
            </div>

            <div class="col-span-1">
                <label for="id_search_category" class="block text-sm font-medium text-gray-700">Search By</label>
                <select name="search_category" id="id_search_category" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">None</option>
                    <option value="WoNo" {% if request.GET.search_category == 'WoNo' %}selected{% endif %}>Work Order No.</option>
                    <option value="Name" {% if request.GET.search_category == 'Name' %}selected{% endif %}>Employee Name</option>
                    <option value="Date" {% if request.GET.search_category == 'Date' %}selected{% endif %}>Report Date (YYYY-MM-DD)</option>
                </select>
            </div>
            
            <div class="col-span-1">
                <label for="id_search_value" class="block text-sm font-medium text-gray-700">Search Value</label>
                <input type="text" name="search_value" id="id_search_value" 
                       value="{{ request.GET.search_value|default:'' }}"
                       placeholder="Enter search term..."
                       class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>

            <div class="col-span-1">
                <button type="submit" 
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out">
                    Search
                </button>
            </div>
        </form>
        <div id="loading-indicator" class="htmx-indicator mt-4 text-center text-blue-600">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <p class="mt-1 text-sm">Loading...</p>
        </div>
    </div>

    <!-- Data Table Container (HTMX Target) -->
    <div id="dailyreport-table-container"
         hx-get="{% url 'dailyreport_table' %}?{{ request.GET.urlencode }}"
         hx-trigger="load, refreshDailyReportList from:body"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden items-center justify-center z-50"
         _="on click if event.target.id == 'modal' remove .flex from me then transition opacity to 0 over 0.3s">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 my-8 relative"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for broader page state
    });

    // Handle HTMX response for messages
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.elt.id === 'modalContent') {
            // If modal content is swapped to empty (204 usually means successful form submission without content)
            // Or if form submission was handled by HTMX and a trigger header was sent.
            // Close modal after successful form submission/delete via HTMX
            document.getElementById('modal').classList.remove('flex');
            document.getElementById('modalContent').innerHTML = ''; // Clear content
        }
    });

    // Optional: Re-initialize DataTables when HTMX swaps in new content
    // This is crucial. DataTables needs to be initialized on the new table element.
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'dailyreport-table-container') {
            const table = document.getElementById('dailyreportTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 16, // Matches ASP.NET GridView PageSize
                    "lengthMenu": [[10, 16, 25, 50, -1], [10, 16, 25, 50, "All"]],
                    "initComplete": function(settings, json) {
                        console.log('DataTable initialized.');
                    }
                });
            }
        }
    });

    // Ensure DataTables is initialized on initial page load if table exists
    $(document).ready(function() {
        const initialTable = document.getElementById('dailyreportTable');
        if (initialTable && !$.fn.DataTable.isDataTable(initialTable)) {
             $(initialTable).DataTable({
                "pageLength": 16,
                "lengthMenu": [[10, 16, 25, 50, -1], [10, 16, 25, 50, "All"]],
                "initComplete": function(settings, json) {
                    console.log('Initial DataTable initialized.');
                }
            });
        }
    });
</script>
{% endblock %}
```

**`dailyreport/_dailyreport_table.html`** (Partial for HTMX table update)

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg p-4">
    <table id="dailyreportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order ID</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Perc. Completed</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Activity</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Report Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for report in daily_reports %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ report.department }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ report.work_order_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ report.percentage_completed }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ report.status }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ report.activity }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ report.employee_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ report.report_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-900">{{ report.remarks|default:"-" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md transition duration-300 ease-in-out text-xs mr-1"
                        hx-get="{% url 'dailyreport_edit' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then transition opacity to 1 over 0.3s">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md transition duration-300 ease-in-out text-xs"
                        hx-get="{% url 'dailyreport_delete' report.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then transition opacity to 1 over 0.3s">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 text-center text-gray-500">
                    No data to display! Adjust your filters or add new entries.
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// This script block will run each time HTMX swaps this partial in.
// It ensures DataTables is re-initialized on the new table.
$(document).ready(function() {
    const tableElement = $('#dailyreportTable');
    if (tableElement.length && !$.fn.DataTable.isDataTable(tableElement)) {
        tableElement.DataTable({
            "pageLength": 16,
            "lengthMenu": [[10, 16, 25, 50, -1], [10, 16, 25, 50, "All"]]
        });
    }
});
</script>
```

**`dailyreport/_dailyreport_form.html`** (Partial for add/edit modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Daily Report Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-4">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field.errors }} {# Display field-specific errors #}
            {{ field }}
        </div>
        {% endfor %}
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then transition opacity to 0 over 0.3s">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`dailyreport/_dailyreport_confirm_delete.html`** (Partial for delete confirmation modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-red-700 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Daily Report entry for:</p>
    <p class="font-bold text-lg text-gray-900 mb-6">"{{ dailyreport }}"</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then transition opacity to 0 over 0.3s">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`dailyreport/urls.py`)

```python
from django.urls import path
from .views import DailyReportListView, DailyReportTablePartialView, DailyReportCreateView, DailyReportUpdateView, DailyReportDeleteView

urlpatterns = [
    # Main list view for reports
    path('dailyreport/', DailyReportListView.as_view(), name='dailyreport_list'),
    
    # HTMX endpoint for loading/refreshing the table content
    path('dailyreport/table/', DailyReportTablePartialView.as_view(), name='dailyreport_table'),

    # CRUD operations (placeholders for future implementation if needed)
    path('dailyreport/add/', DailyReportCreateView.as_view(), name='dailyreport_add'),
    path('dailyreport/edit/<int:pk>/', DailyReportUpdateView.as_view(), name='dailyreport_edit'),
    path('dailyreport/delete/<int:pk>/', DailyReportDeleteView.as_view(), name='dailyreport_delete'),
]
```

#### 4.6 Tests (`dailyreport/tests.py`)

Comprehensive tests ensuring model functionality and view integration, including HTMX interactions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Department, DailyReport
from datetime import date

class DepartmentModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data (using a PK value that matches the 'description' field)
        Department.objects.create(description='IT Department')
        Department.objects.create(description='HR Department')

    def test_department_creation(self):
        dept = Department.objects.get(description='IT Department')
        self.assertEqual(dept.description, 'IT Department')
        self.assertEqual(str(dept), 'IT Department')

    def test_department_verbose_name(self):
        self.assertEqual(Department._meta.verbose_name, 'Department')
        self.assertEqual(Department._meta.verbose_name_plural, 'Departments')

class DailyReportModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a Department for foreign key (if it were one) or lookup
        Department.objects.create(description='Operations')
        
        # Create test data for DailyReport (ensure unique_together constraint is met)
        DailyReport.objects.create(
            department='Operations',
            work_order_id='WO-001',
            percentage_completed=75.50,
            status='In Progress',
            activity='Data Entry',
            employee_name='John Doe',
            report_date=date(2023, 1, 15),
            remarks='Initial entry'
        )
        DailyReport.objects.create(
            department='HR Department',
            work_order_id='WO-002',
            percentage_completed=100.00,
            status='Completed',
            activity='Interview Scheduling',
            employee_name='Jane Smith',
            report_date=date(2023, 1, 16),
            remarks='Finalized interviews'
        )
        DailyReport.objects.create(
            department='Operations',
            work_order_id='WO-003',
            percentage_completed=50.00,
            status='Pending',
            activity='Hardware Installation',
            employee_name='John Doe',
            report_date=date(2023, 1, 17),
            remarks='Waiting for parts'
        )

    def test_daily_report_creation(self):
        report = DailyReport.objects.get(work_order_id='WO-001')
        self.assertEqual(report.department, 'Operations')
        self.assertEqual(report.employee_name, 'John Doe')
        self.assertEqual(report.report_date, date(2023, 1, 15))
        self.assertAlmostEqual(report.percentage_completed, 75.50)
        self.assertEqual(str(report), 'WO-001 - John Doe (2023-01-15)')

    def test_get_filtered_reports_no_filters(self):
        reports = DailyReport.get_filtered_reports()
        self.assertEqual(reports.count(), 3)

    def test_get_filtered_reports_by_department(self):
        reports = DailyReport.get_filtered_reports(department_name='Operations')
        self.assertEqual(reports.count(), 2)
        self.assertTrue(all(r.department == 'Operations' for r in reports))

    def test_get_filtered_reports_by_work_order_id(self):
        reports = DailyReport.get_filtered_reports(search_category='WoNo', search_value='001')
        self.assertEqual(reports.count(), 1)
        self.assertEqual(reports.first().work_order_id, 'WO-001')

    def test_get_filtered_reports_by_employee_name(self):
        reports = DailyReport.get_filtered_reports(search_category='Name', search_value='John Doe')
        self.assertEqual(reports.count(), 2)
        self.assertTrue(all(r.employee_name == 'John Doe' for r in reports))
        
    def test_get_filtered_reports_by_date(self):
        reports = DailyReport.get_filtered_reports(search_category='Date', search_value='2023-01-16')
        self.assertEqual(reports.count(), 1)
        self.assertEqual(reports.first().report_date, date(2023, 1, 16))

    def test_get_filtered_reports_department_and_search(self):
        reports = DailyReport.get_filtered_reports(
            department_name='Operations',
            search_category='Name',
            search_value='John Doe'
        )
        self.assertEqual(reports.count(), 2) # WO-001 and WO-003 are both John Doe in Operations

    def test_get_filtered_reports_invalid_date(self):
        reports = DailyReport.get_filtered_reports(search_category='Date', search_value='invalid-date')
        self.assertEqual(reports.count(), 0)

class DailyReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Department.objects.create(description='Sales')
        Department.objects.create(description='Marketing')

        DailyReport.objects.create(
            department='Sales',
            work_order_id='SR-001',
            percentage_completed=80.00,
            status='Ongoing',
            activity='Client Meeting',
            employee_name='Alice Brown',
            report_date=date(2024, 5, 1),
            remarks='First meeting'
        )
        DailyReport.objects.create(
            department='Marketing',
            work_order_id='MK-001',
            percentage_completed=60.00,
            status='Planned',
            activity='Campaign Setup',
            employee_name='Bob Green',
            report_date=date(2024, 5, 2),
            remarks='New campaign'
        )
        DailyReport.objects.create(
            department='Sales',
            work_order_id='SR-002',
            percentage_completed=90.00,
            status='Ongoing',
            activity='Follow-up',
            employee_name='Alice Brown',
            report_date=date(2024, 5, 3),
            remarks='Follow-up call'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('dailyreport_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/dailyreport_list.html')
        self.assertTrue('daily_reports' in response.context)
        self.assertEqual(response.context['daily_reports'].count(), 3)
        self.assertTrue('filter_form' in response.context)

    def test_table_partial_view_no_filters(self):
        response = self.client.get(reverse('dailyreport_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/_dailyreport_table.html')
        self.assertTrue('daily_reports' in response.context)
        self.assertEqual(response.context['daily_reports'].count(), 3)
        self.assertContains(response, 'Alice Brown')
        self.assertContains(response, 'Bob Green')

    def test_table_partial_view_filter_by_department(self):
        response = self.client.get(reverse('dailyreport_table'), {'department': 'Sales'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/_dailyreport_table.html')
        self.assertTrue('daily_reports' in response.context)
        self.assertEqual(response.context['daily_reports'].count(), 2)
        self.assertContains(response, 'Alice Brown')
        self.assertNotContains(response, 'Bob Green')

    def test_table_partial_view_filter_by_search_category_and_value_wono(self):
        response = self.client.get(reverse('dailyreport_table'), {'search_category': 'WoNo', 'search_value': 'SR-001'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['daily_reports'].count(), 1)
        self.assertContains(response, 'SR-001')
        self.assertNotContains(response, 'MK-001')

    def test_table_partial_view_filter_by_search_category_and_value_name(self):
        response = self.client.get(reverse('dailyreport_table'), {'search_category': 'Name', 'search_value': 'Alice'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['daily_reports'].count(), 2)
        self.assertContains(response, 'Alice Brown')
        self.assertNotContains(response, 'Bob Green')

    def test_table_partial_view_filter_by_search_category_and_value_date(self):
        response = self.client.get(reverse('dailyreport_table'), {'search_category': 'Date', 'search_value': '2024-05-02'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['daily_reports'].count(), 1)
        self.assertContains(response, '2024-05-02')

    def test_create_view_get(self):
        response = self.client.get(reverse('dailyreport_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/_dailyreport_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Daily Report Entry')

    def test_create_view_post_htmx(self):
        data = {
            'department': 'Sales',
            'work_order_id': 'SR-003',
            'percentage_completed': 25.00,
            'status': 'New',
            'activity': 'Lead Generation',
            'employee_name': 'New Employee',
            'report_date': '2024-05-04',
            'remarks': 'New entry for testing'
        }
        response = self.client.post(reverse('dailyreport_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDailyReportList')
        self.assertTrue(DailyReport.objects.filter(work_order_id='SR-003').exists())

    def test_update_view_get(self):
        report = DailyReport.objects.get(work_order_id='SR-001')
        response = self.client.get(reverse('dailyreport_edit', args=[report.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/_dailyreport_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Daily Report Entry')
        self.assertContains(response, 'value="SR-001"') # Ensure pre-filled form

    def test_update_view_post_htmx(self):
        report = DailyReport.objects.get(work_order_id='SR-001')
        data = {
            'department': 'Sales',
            'work_order_id': 'SR-001', # PK should remain
            'percentage_completed': 95.00, # Updated value
            'status': 'Completed',
            'activity': 'Client Meeting',
            'employee_name': 'Alice Brown',
            'report_date': '2024-05-01',
            'remarks': 'First meeting updated'
        }
        response = self.client.post(reverse('dailyreport_edit', args=[report.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDailyReportList')
        report.refresh_from_db()
        self.assertEqual(report.percentage_completed, 95.00)
        self.assertEqual(report.status, 'Completed')

    def test_delete_view_get(self):
        report = DailyReport.objects.get(work_order_id='SR-001')
        response = self.client.get(reverse('dailyreport_delete', args=[report.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/_dailyreport_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'SR-001 - Alice Brown (2024-05-01)')

    def test_delete_view_post_htmx(self):
        report = DailyReport.objects.get(work_order_id='SR-001')
        response = self.client.post(reverse('dailyreport_delete', args=[report.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDailyReportList')
        self.assertFalse(DailyReport.objects.filter(work_order_id='SR-001').exists())
```

### Step 5: HTMX and Alpine.js Integration

As demonstrated in the templates and views:

*   **HTMX for Dynamic Updates:**
    *   The `department` dropdown and `Search` button on `dailyreport_list.html` use `hx-get` to trigger a request to `dailyreport_table/` and swap the content of `#dailyreport-table-container`.
    *   `hx-trigger="change"` on the department dropdown ensures automatic table update on selection change, mirroring ASP.NET's `OnSelectedIndexChanged`.
    *   `hx-trigger="submit"` on the form ensures the search button triggers the same HTMX request.
    *   `hx-trigger="load, refreshDailyReportList from:body"` on `#dailyreport-table-container` ensures the table loads on page load and refreshes when any CRUD operation (add/edit/delete) sends the `HX-Trigger: refreshDailyReportList` header.
    *   Modal forms (add/edit/delete) are loaded via `hx-get` and `hx-target="#modalContent"`, and form submissions are handled via `hx-post` with `hx-swap="none"` and `HX-Trigger` headers to refresh the list.
    *   An `htmx-indicator` is used to provide visual feedback during HTMX requests.
*   **Alpine.js for UI State:**
    *   Alpine.js is used for simple modal toggling (`_=` attribute on buttons and modal container) to add/remove `hidden`/`flex` classes for display. This keeps JS minimal and localized to the HTML.
*   **DataTables for List Views:**
    *   The `_dailyreport_table.html` partial contains an `<table>` with `id="dailyreportTable"`.
    *   A JavaScript block within this partial (and in `dailyreport_list.html` for initial load) uses jQuery and DataTables CDN links to initialize the table.
    *   The `htmx:afterSwap` event listener ensures that DataTables is re-initialized whenever the table content is updated via HTMX, preventing issues with dynamically loaded content.
    *   Client-side search, sorting, and pagination are handled automatically by DataTables.

---

## Final Notes

*   **No Explicit PK for `DailyReport`:** In the provided ASP.NET code, no single column for `DRT_Sys_New` was explicitly identified as a primary key. The `DataKeyNames="IdWo,E_name,IdDate"` suggests a composite key. In the Django model, `unique_together` is used to enforce this uniqueness. Django's ORM typically works best with a single primary key; if a true single PK column exists in your legacy database for `DRT_Sys_New`, it should be explicitly set (`primary_key=True`) in the model. If not, Django often implicitly adds an `id` column for internal use with `managed=False`.
*   **Date Parsing:** The C# code's date filtering was ambiguous. The Django solution assumes a `YYYY-MM-DD` format for `search_value` when `Date` category is selected. Robust error handling for date parsing in production is crucial.
*   **Business Logic in Models:** All filtering logic for `DailyReport` is encapsulated within the `DailyReport` model's class method `get_filtered_reports`, keeping views concise and focused on request handling.
*   **Template Reusability:** Partial templates (`_dailyreport_table.html`, `_dailyreport_form.html`, `_dailyreport_confirm_delete.html`) are used for reusability with HTMX, adhering to DRY principles.
*   **Error Handling:** The C# code used empty `try-catch` blocks, suppressing errors. The Django solution implicitly handles many common errors via the framework and raises exceptions for unhandled issues, which can then be logged and monitored properly. For user-facing errors (e.g., invalid date format in search), explicit handling (as shown in `get_filtered_reports`) is implemented.
*   **Security:** Django's ORM naturally prevents SQL injection vulnerabilities present in the original ASP.NET code's direct string concatenation for SQL queries.
*   **Extensibility:** This structure is easily extensible. If new CRUD operations are required for `DailyReport`, the placeholder views and forms can be fully fleshed out, leveraging the existing HTMX/Alpine.js modal pattern.