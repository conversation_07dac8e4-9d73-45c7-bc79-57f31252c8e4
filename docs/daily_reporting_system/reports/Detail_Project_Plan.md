## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET "Detail_Project_Plan" application to a modern Django-based solution. Our approach prioritizes automation, efficiency, and a clean, maintainable architecture using conversational AI guidance.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- **NEVER** include `base.html` template code in your output - assume it already exists and is properly configured for Tailwind CSS, HTMX, and Alpine.js.
- Focus **ONLY** on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several database tables, primarily for data retrieval and filtering. We've identified the following key tables and their probable structures, which will be mapped directly in Django.

*   **`tblDG_Item_Master`**: This is the core table for item details, likely representing inventory or project items.
    *   Columns: `Id` (Primary Key), `ItemCode` (string), `ManfDesc` (string, manufacturer description), `UOMBasic` (string, Unit of Measurement), `StockQty` (numeric), `FileName` (string, for image), `FileData` (binary, actual image data), `ContentType` (string, image MIME type), `AttName` (string, for spec sheet), `AttData` (binary, actual spec sheet data), `AttContentType` (string, spec sheet MIME type), `Location` (string, potentially a foreign key to `tblHR_Departments`), `CompId` (integer, Company ID), `FinYearId` (integer, Financial Year ID).
*   **`tblFinancial_master`**: Stores financial year ranges.
    *   Columns: `FinYearId` (Primary Key), `CompId` (integer), `FinYearFrom` (date), `FinYearTo` (date).
*   **`tblHR_Designation`**: Stores employee designations.
    *   Columns: `Id` (Primary Key), `Type` (string, designation name).
*   **`tblHR_Departments`**: Stores department descriptions.
    *   Columns: `Id` (Primary Key), `Description` (string, department name).
*   **`aspnet_Users`**: Standard ASP.NET membership table, used here for employee IDs/UserNames.
    *   Columns: `UserId` (Primary Key, GUID), `UserName` (string).
*   **`tblHR_Offer_Master`**: Contains employee offer details, specifically `EmployeeName`.
    *   Columns: `Id` (Primary Key), `EmployeeName` (string), `CompId` (integer).

#### Step 2: Identify Backend Functionality

**Task:** Determine the core operations in the ASP.NET code for the "Daily Reporting Tracker System" tab.

**Instructions:**
The primary functionality is **Read** (displaying data) with extensive filtering.

*   **Data Retrieval:**
    *   Initial page load fetches financial year start/end dates from `tblFinancial_master`.
    *   The main data grid (`GridView2`) is populated by the `Fillgrid` C# method, which in turn calls a stored procedure named `GetAllItem`. This stored procedure takes multiple parameters for filtering (search type, search text, category/WO items, specific codes like ItemCode, ManfDesc, Location).
*   **Filtering and Search:**
    *   Filtering by date range (`TxtFromDate`, `TxtToDate`).
    *   Filtering by "Type" (`DrpType`): "Name" (implying employee-related search) or "WONo" (work order).
    *   Contextual search fields (`DrpSearchCode`, `txtSearchItemCode`, `Drpdep`, `Drpdesi`, `Drpempid`, `Dropwo`) dynamically appear/disappear based on selections in `DrpType` and `DrpSearchCode`.
*   **Actions on Grid Data:**
    *   **"Select" (Sel) Link Button:** Redirects to `StockLedger_Details.aspx` with `Id`, `FD` (from date), and `TD` (to date) as query parameters. In Django, this will be a link to a new view/page.
    *   **"Image" (downloadImg) Link Button:** Downloads an image file associated with the item, from `tblDG_Item_Master`'s `FileData` column.
    *   **"Spec. Sheet" (downloadSpec) Link Button:** Downloads a specification sheet associated with the item, from `tblDG_Item_Master`'s `AttData` column.
*   **Validation:** Client-side and server-side date validation (from date <= to date, and from date >= financial year opening date).

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, and map them to Django UI equivalents.

**Instructions:**
The user interface is centered around a multi-tabbed layout, with the "Daily Reporting Tracker System" tab containing several input fields and a data grid.

*   **Main Layout:** The `TabContainer` will be replaced with standard HTML/CSS tabs, managed by Alpine.js for state.
*   **Date Inputs:** `TxtFromDate` and `TxtToDate` (with `CalendarExtender`) will be standard HTML `<input type="date">` fields. Date labels (`lblFromDate`, `lblToDate`) will display current financial year boundaries.
*   **Dropdowns (`<asp:DropDownList>`):**
    *   `DrpType` (Select, Name, WONo): Maps to a simple HTML `<select>`.
    *   `DrpSearchCode` (Designation, Departments, Employee Id): Maps to a simple HTML `<select>`, its options depend on `DrpType`.
    *   `Drpdesi` (Designation): Maps to HTML `<select>`, populated from `tblHR_Designation`.
    *   `Drpdep` (Departments): Maps to HTML `<select>`, populated from `tblHR_Departments` (or a location field).
    *   `Drpempid` (Employee ID), `Dropwo`: These will also be HTML `<select>` elements, their population would depend on backend logic not fully detailed, but they'll be included as placeholders.
*   **Text Inputs (`<asp:TextBox>`):**
    *   `txtSearchItemCode`: Simple HTML `<input type="text">`.
    *   `txtSupplier`, `txtPONo` (in other tabs, with `AutoCompleteExtender`): These will be standard HTML `<input type="text>` with HTMX-driven auto-completion if needed, or simple search fields.
*   **Buttons (`<asp:Button>`):**
    *   `Btnsearch`: Simple HTML `<button type="submit">`.
*   **Labels (`<asp:Label>`):** Used for messages and display. Will be standard HTML `<span>` or `<div>`.
*   **Data Grid (`<asp:GridView>`):** `GridView2` will be replaced by an HTML `<table>` enhanced with **DataTables.js** for client-side functionality (searching, sorting, pagination). HTMX will be used to load and refresh this table.
*   **File Download Links:** `LinkButton` elements (`btnlnkImg`, `btnlnkSpec`) will become standard HTML `<a>` tags pointing to Django download views.

---

### Step 4: Generate Django Code

We'll organize the Django code within an application, let's call it `project_planning`.

#### 4.1 Models (`project_planning/models.py`)

We'll define models for each identified database table, ensuring `managed = False` for existing databases and `db_table` to map to the original table names. We'll also add a custom manager to `ItemMaster` to encapsulate the complex filtering logic previously handled by the `GetAllItem` stored procedure.

```python
from django.db import models
from datetime import date

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    id = models.AutoField(db_column='FinYearId', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    from_date = models.DateField(db_column='FinYearFrom')
    to_date = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"FY {self.from_date.year}-{self.to_date.year}"

class Designation(models.Model):
    """
    Maps to tblHR_Designation for employee designations.
    """
    id = models.AutoField(primary_key=True) # Assuming PK is 'Id'
    type = models.CharField(db_column='Type', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.type

class Department(models.Model):
    """
    Maps to tblHR_Departments for department descriptions.
    """
    id = models.AutoField(primary_key=True) # Assuming PK is 'Id'
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblHR_Departments'
        verbose_name = 'Department'
        verbose_name_plural = 'Departments'

    def __str__(self):
        return self.description

class EmployeeUser(models.Model):
    """
    Maps to aspnet_Users for employee user names/IDs.
    Note: aspnet_Users often uses GUIDs for UserID. Assuming an Integer PK for simplicity here.
    """
    id = models.UUIDField(db_column='UserId', primary_key=True) # Often GUID
    username = models.CharField(db_column='UserName', max_length=256)

    class Meta:
        managed = False
        db_table = 'aspnet_Users'
        verbose_name = 'Employee User'
        verbose_name_plural = 'Employee Users'

    def __str__(self):
        return self.username

class OfferMaster(models.Model):
    """
    Maps to tblHR_Offer_Master for employee names.
    """
    id = models.AutoField(primary_key=True) # Assuming PK is 'Id'
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_Offer_Master'
        verbose_name = 'Offer Master'
        verbose_name_plural = 'Offer Masters'

    def __str__(self):
        return self.employee_name

class ItemMasterQuerySet(models.QuerySet):
    def get_filtered_items(self, from_date, to_date, search_type, search_code, search_text, comp_id, fin_year_id):
        """
        Translates the GetAllItem stored procedure logic into Django ORM.
        This method handles the complex filtering based on ASP.NET 'Fillgrid' logic.
        """
        items = self.all() # Start with all items, or apply base filters if always present

        # Apply company and financial year filters if they were always present
        # Note: The original code had FinYearId <= fin_year_id, which is unusual.
        # Assuming for now it's FinYearId = fin_year_id or similar common logic.
        # For simplicity, we'll assume these are passed in the request or from session.
        # items = items.filter(comp_id=comp_id, fin_year_id=fin_year_id)

        # Date validation (already handled in form, but good to have in model too)
        if from_date and to_date:
            # The original code's date logic was complex.
            # Assuming these dates are for filtering items based on some internal date field,
            # which is not explicitly listed as a column in tblDG_Item_Master.
            # If items have a 'created_date' or 'last_updated_date', we'd filter by that.
            # For this example, we'll assume item records are relevant within this period.
            pass # No direct date field in ItemMaster for filtering

        # Apply search type and search code filters
        if search_type == "Category": # Original code indicates 'Category' was implied but not a DrpType option
             # ASP.NET code had 'Category' handling, but 'DrpType' itself had 'Select', 'Name', 'WONo'
             # This suggests a misunderstanding in the original code or an implied filter source.
             # We'll map 'Category' logic to the 'Name' branch as it handles Designation, Dept, Employee.
             # If 'DrpSearchCode' is for 'Category' (Designation, Dept, Employee Id) and search_text is for ItemCode/ManfDesc
            if search_code == "tblHR_Designation.Type": # Maps to 'Designation'
                # Filter by designation. This would require a join or related field.
                # Assuming 'Location' field in ItemMaster maps to Department/Designation conceptually for now.
                # Or a direct 'designation' field. Let's assume a conceptual mapping.
                pass # ItemMaster does not directly have designation. This needs a FK if Designations are on ItemMaster
            elif search_code == "tblHR_Departments.Description": # Maps to 'Departments'
                items = items.filter(location=search_text) # Assuming Location field maps to department name
            elif search_code == "aspnet_Users.UserName": # Maps to 'Employee Id'
                # This would require an FK to EmployeeUser on ItemMaster or related table.
                pass # ItemMaster does not directly have employee ID.
            elif search_code == "tblDG_Item_Master.ItemCode" and search_text:
                items = items.filter(item_code__icontains=search_text)
            elif search_code == "tblDG_Item_Master.ManfDesc" and search_text:
                items = items.filter(manf_desc__icontains=search_text)

        elif search_type == "Name": # Corresponds to Name search logic
            if search_code == "tblHR_Designation.Type":
                # Assuming this implies filtering by employee designation associated with an item.
                # Requires a ManyToMany or ForeignKey relationship on ItemMaster to Designation.
                # Since not explicitly in schema, we'll assume it's a conceptual link for now.
                pass
            elif search_code == "tblHR_Departments.Description":
                items = items.filter(location__icontains=search_text) # Filter by Location as text
            elif search_code == "aspnet_Users.UserName":
                # Assuming employee associated with item. Needs FK.
                pass
            elif search_text: # General search on description if no specific code
                items = items.filter(manf_desc__icontains=search_text)


        elif search_type == "WONo": # Corresponds to WOItems search logic
            if search_code == "tblDG_Item_Master.ItemCode" and search_text:
                items = items.filter(item_code__icontains=search_text)
            elif search_code == "tblDG_Item_Master.ManfDesc" and search_text:
                items = items.filter(manf_desc__icontains=search_text)
            elif search_text: # General search on description if no specific code
                items = items.filter(manf_desc__icontains=search_text)

        # The original `GetAllItem` was a SP. This ORM query attempts to replicate the common filters.
        # It's crucial to map the exact database fields and joins for accurate results.
        return items


class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master for detailed item information.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    manf_desc = models.TextField(db_column='ManfDesc') # Textfield for potentially longer descriptions
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50)
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=10, decimal_places=2)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True) # For storing file content directly
    content_type = models.CharField(db_column='ContentType', max_length=100, blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    att_data = models.BinaryField(db_column='AttData', blank=True, null=True) # For storing attachment content directly
    att_content_type = models.CharField(db_column='AttContentType', max_length=100, blank=True, null=True)
    location = models.CharField(db_column='Location', max_length=255, blank=True, null=True) # Assuming this is a text field based on original code, not FK
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    objects = ItemMasterQuerySet.as_manager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

    def get_absolute_url(self):
        """
        Provides a URL for an individual item's details, if 'Sel' action is implemented.
        """
        # This corresponds to "StockLedger_Details.aspx" redirect
        # In Django, this would be a URL to a new view, e.g., 'stock_ledger_details'
        # For simplicity, returning a placeholder URL.
        from django.urls import reverse
        return reverse('stock_ledger_details', args=[self.pk]) + \
               f"?FD={date.today().strftime('%d-%m-%Y')}&TD={date.today().strftime('%d-%m-%Y')}"

    def get_image_download_url(self):
        """
        Returns the URL to download the associated image.
        """
        if self.file_data:
            from django.urls import reverse
            return reverse('item_master_download_file', args=[self.pk, 'image'])
        return None

    def get_spec_sheet_download_url(self):
        """
        Returns the URL to download the associated specification sheet.
        """
        if self.att_data:
            from django.urls import reverse
            return reverse('item_master_download_file', args=[self.pk, 'spec_sheet'])
        return None

    # Example of a business logic method (if ItemMaster had more complex rules)
    def calculate_current_value(self, unit_cost):
        """
        Calculates the current value of the stock.
        """
        return self.stock_qty * unit_cost

```

#### 4.2 Forms (`project_planning/forms.py`)

A form to handle the filtering and search criteria. We'll use a standard `forms.Form` as it's not directly tied to a single model for CRUD, but rather for query parameters.

```python
from django import forms
from datetime import date
from .models import Designation, Department, EmployeeUser

class ItemMasterFilterForm(forms.Form):
    """
    Form for filtering ItemMaster records based on various criteria
    from the Daily Reporting Tracker System tab.
    """
    from_date = forms.DateField(
        label="From Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-[100px] inline-block'}),
        required=False,
        input_formats=['%Y-%m-%d', '%d-%m-%Y'] # Allow different formats
    )
    to_date = forms.DateField(
        label="To Date",
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'box3 w-[100px] inline-block'}),
        required=False,
        input_formats=['%Y-%m-%d', '%d-%m-%Y']
    )
    
    # DrpType (Name, WONo)
    TYPE_CHOICES = [
        ('Select', 'Select'),
        ('Name', 'Name'),
        ('WONo', 'WONo'),
    ]
    type_selection = forms.ChoiceField(
        choices=TYPE_CHOICES,
        label="Select Type",
        required=False,
        widget=forms.Select(attrs={'class': 'box3 w-[150px] inline-block'})
    )

    # DrpSearchCode (Designation, Departments, Employee Id)
    SEARCH_CODE_CHOICES_BASE = [
        ('Select', 'Select'),
    ]
    SEARCH_CODE_CHOICES_NAME = [
        ('tblHR_Designation.Type', 'Designation'),
        ('tblHR_Departments.Description', 'Departments'),
        ('aspnet_Users.UserName', 'Employee Id'),
    ]
    SEARCH_CODE_CHOICES_WONO = [
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
    ]
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES_BASE + SEARCH_CODE_CHOICES_NAME + SEARCH_CODE_CHOICES_WONO,
        label="Search Field",
        required=False,
        widget=forms.Select(attrs={'class': 'box3 w-[200px] inline-block'})
    )

    # Drpdesi (Designation dropdown)
    designation = forms.ModelChoiceField(
        queryset=Designation.objects.all().order_by('type'), # Assuming 'type' is the display field
        label="Designation",
        required=False,
        empty_label="Select Designation",
        widget=forms.Select(attrs={'class': 'box3 w-[200px] inline-block'})
    )

    # Drpdep (Department dropdown)
    department = forms.ModelChoiceField(
        queryset=Department.objects.all().order_by('description'),
        label="Department",
        required=False,
        empty_label="Select Department",
        widget=forms.Select(attrs={'class': 'box3 w-[155px] inline-block'})
    )

    # Drpempid (Employee ID dropdown) - Placeholder
    employee_id = forms.ModelChoiceField(
        queryset=EmployeeUser.objects.all().order_by('username'),
        label="Employee ID",
        required=False,
        empty_label="Select Employee ID",
        widget=forms.Select(attrs={'class': 'box3 w-[200px] inline-block'})
    )

    # txtSearchItemCode (general text search)
    search_text = forms.CharField(
        label="Search Text",
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-[207px] inline-block'})
    )

    # Dropwo (Work Order dropdown) - Placeholder
    # This dropdown's source is unclear from ASP.NET; it might be dynamically populated.
    # For now, it's a simple CharField to represent its presence.
    work_order = forms.CharField(
        label="Work Order",
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-[150px] inline-block'})
    )


    def clean(self):
        """
        Custom validation for date range.
        """
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date:
            if from_date > to_date:
                self.add_error('from_date', 'From date must be less than or equal to To date.')
                self.add_error('to_date', 'To date must be greater than or equal to From date.')

            # Original ASP.NET had `Convert.ToDateTime(fun.FromDate(fd1)) <= Convert.ToDateTime(fun.FromDate(fd)))`
            # This needs Financial Year info, which we'll fetch in the view.
            # For form validation, we can assume `financial_year_start` is passed to the form.
            # Example: self.financial_year_start = kwargs.pop('financial_year_start', None)
            # if self.financial_year_start and from_date < self.financial_year_start:
            #     self.add_error('from_date', f'From date should not be less than Financial Year opening date ({self.financial_year_start.strftime("%d-%m-%Y")})!')

        return cleaned_data

```

#### 4.3 Views (`project_planning/views.py`)

We'll define three views: one for the main page, one for the HTMX-loaded table content, and one for file downloads.

```python
from django.views.generic import TemplateView, ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse, Http404
from django.utils.html import format_html
from django.db.models import Q # For complex ORM queries
from datetime import date
import io # For file operations

from .models import ItemMaster, FinancialYear, Designation, Department, EmployeeUser
from .forms import ItemMasterFilterForm

# Helper to get current financial year (assuming a default or session/user context)
def get_current_financial_year_info(comp_id=1, fin_year_id=1):
    try:
        fin_year = FinancialYear.objects.get(comp_id=comp_id, id=fin_year_id)
        return fin_year.from_date, fin_year.to_date
    except FinancialYear.DoesNotExist:
        # Fallback to current year if not found or handle error
        today = date.today()
        return date(today.year, 1, 1), date(today.year, 12, 31)

class ProjectPlanSummaryView(TemplateView):
    """
    Renders the main Project Plan Summary page with filter controls and a container
    for the HTMX-loaded item master table.
    """
    template_name = 'project_planning/project_plan_summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Populate initial form state and financial year info
        # In a real app, comp_id and fin_year_id would come from user session/profile
        comp_id = self.request.session.get('comp_id', 1) 
        fin_year_id = self.request.session.get('fin_year_id', 1) 

        fin_from_date, fin_to_date = get_current_financial_year_info(comp_id, fin_year_id)
        
        initial_data = {
            'from_date': fin_from_date.strftime('%Y-%m-%d'),
            'to_date': date.today().strftime('%Y-%m-%d'),
        }
        
        # Populate dropdowns with all available options for Alpine.js to manage visibility
        context['designations'] = list(Designation.objects.values('type'))
        context['departments'] = list(Department.objects.values('description'))
        context['employee_users'] = list(EmployeeUser.objects.values('username'))

        context['form'] = ItemMasterFilterForm(initial=initial_data)
        context['lbl_from_date'] = fin_from_date.strftime('%d-%m-%Y')
        context['lbl_to_date'] = fin_to_date.strftime('%d-%m-%Y')
        
        return context

class ItemMasterTablePartialView(ListView):
    """
    Handles HTMX requests to render only the Item Master data table.
    Filters applied based on GET parameters from the form.
    """
    model = ItemMaster
    template_name = 'project_planning/_item_master_table.html'
    context_object_name = 'item_masters'
    
    # We'll let DataTables handle client-side pagination, so no Django pagination here
    # If server-side pagination needed for large datasets, enable ListView.paginate_by

    def get_queryset(self):
        """
        Applies filters based on request GET parameters.
        This method replaces the logic from the ASP.NET Fillgrid method.
        """
        # In a real app, comp_id and fin_year_id would come from user session/profile
        comp_id = self.request.session.get('comp_id', 1) 
        fin_year_id = self.request.session.get('fin_year_id', 1)

        # Get filter parameters from GET request
        form = ItemMasterFilterForm(self.request.GET)
        if not form.is_valid():
            # If form is not valid, return empty queryset or handle errors
            # For HTMX, we might want to return messages.
            messages.error(self.request, "Invalid filter criteria provided.")
            return ItemMaster.objects.none()

        cleaned_data = form.cleaned_data

        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')
        search_type = cleaned_data.get('type_selection')
        search_code = cleaned_data.get('search_code')
        search_text = cleaned_data.get('search_text')
        
        # Original ASP.NET code had date validation against financial year.
        # This can be done here or in the form's clean method.
        fin_from_date, _ = get_current_financial_year_info(comp_id, fin_year_id)
        if from_date and from_date < fin_from_date:
            messages.error(self.request, f"From date should not be less than Financial Year opening date ({fin_from_date.strftime('%d-%m-%Y')})!")
            return ItemMaster.objects.none()

        # Call the custom manager method to get filtered items
        queryset = ItemMaster.objects.get_filtered_items(
            from_date=from_date,
            to_date=to_date,
            search_type=search_type,
            search_code=search_code,
            search_text=search_text,
            comp_id=comp_id, # Pass comp_id to model method if filtering by it
            fin_year_id=fin_year_id # Pass fin_year_id to model method if filtering by it
        )
        return queryset

    def render_to_response(self, context, **response_kwargs):
        """
        Handle messages for HTMX requests.
        """
        response = super().render_to_response(context, **response_kwargs)
        if self.request.headers.get('HX-Request'):
            # Clear messages if no new messages are added, or keep if an error occurred.
            pass
        return response

class DownloadFileView(View):
    """
    Handles file downloads for images and specification sheets.
    Corresponds to downloadImg and downloadSpec LinkButton actions.
    """
    def get(self, request, pk, file_type):
        try:
            item = ItemMaster.objects.get(pk=pk)
        except ItemMaster.DoesNotExist:
            raise Http404("Item not found.")

        file_data = None
        file_name = None
        content_type = 'application/octet-stream' # Default content type

        if file_type == 'image':
            file_data = item.file_data
            file_name = item.file_name
            content_type = item.content_type if item.content_type else 'image/jpeg'
        elif file_type == 'spec_sheet':
            file_data = item.att_data
            file_name = item.att_name
            content_type = item.att_content_type if item.att_content_type else 'application/pdf'
        else:
            raise Http404("Invalid file type.")

        if not file_data:
            messages.error(request, f"No {file_type.replace('_', ' ')} available for this item.")
            return HttpResponse(status=204, headers={'HX-Trigger': 'showMessage'}) # HTMX-specific response

        # Use io.BytesIO to create a file-like object from binary data
        response = FileResponse(io.BytesIO(file_data), content_type=content_type)
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response

# Placeholder for StockLedger_Details equivalent
class StockLedgerDetailsView(TemplateView):
    template_name = 'project_planning/stock_ledger_details.html' # Create this template
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_id = self.kwargs['pk']
        from_date = self.request.GET.get('FD')
        to_date = self.request.GET.get('TD')
        
        context['item_id'] = item_id
        context['from_date'] = from_date
        context['to_date'] = to_date
        messages.info(self.request, f"Navigated to Stock Ledger for Item ID: {item_id} from {from_date} to {to_date}")
        return context

```

#### 4.4 Templates (`project_planning/`)

We'll create the main page template and a partial for the DataTables content. All templates extend `core/base.html` (which is assumed to exist).

##### `project_planning/project_plan_summary.html` (Main Page Template)

This template integrates the filter form and the HTMX container for the DataTables table. Alpine.js manages the dynamic visibility of form fields.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Project Summary - Daily Reporting Tracker System</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8" x-data="{
        type_selection: '{{ form.type_selection.value|default:'Select' }}',
        search_code_selection: '{{ form.search_code.value|default:'Select' }}',
        show_search_text: true,
        show_designation_dropdown: false,
        show_department_dropdown: false,
        show_employee_id_dropdown: false,
        
        updateVisibility() {
            this.show_search_text = true;
            this.show_designation_dropdown = false;
            this.show_department_dropdown = false;
            this.show_employee_id_dropdown = false;

            if (this.type_selection === 'Name') {
                if (this.search_code_selection === 'tblHR_Designation.Type') {
                    this.show_designation_dropdown = true;
                    this.show_search_text = false;
                } else if (this.search_code_selection === 'tblHR_Departments.Description') {
                    this.show_department_dropdown = true;
                    this.show_search_text = false;
                } else if (this.search_code_selection === 'aspnet_Users.UserName') {
                    this.show_employee_id_dropdown = true;
                    this.show_search_text = false;
                } else {
                    this.show_search_text = true; // General search for Item Code / Manf Desc
                }
            } else if (this.type_selection === 'WONo') {
                this.show_search_text = true; // Always show for WONo type
                // No specific dropdowns for WONo based on original code, general text search for item code/desc
            } else if (this.type_selection === 'Select') {
                // Reset everything if 'Select' is chosen
                this.show_search_text = false; 
                this.search_code_selection = 'Select'; // Reset search code dropdown
            }
        },

        getSearchCodeOptions() {
            if (this.type_selection === 'Name') {
                return [
                    {value: 'Select', text: 'Select'},
                    {value: 'tblHR_Designation.Type', text: 'Designation'},
                    {value: 'tblHR_Departments.Description', text: 'Departments'},
                    {value: 'aspnet_Users.UserName', text: 'Employee Id'}
                ];
            } else if (this.type_selection === 'WONo') {
                return [
                    {value: 'Select', text: 'Select'},
                    {value: 'tblDG_Item_Master.ItemCode', text: 'Item Code'},
                    {value: 'tblDG_Item_Master.ManfDesc', text: 'Description'}
                ];
            }
            return [{value: 'Select', text: 'Select'}];
        },
        
        init() {
            this.$watch('type_selection', () => {
                this.search_code_selection = 'Select'; // Reset search_code on type change
                this.updateVisibility();
            });
            this.$watch('search_code_selection', () => this.updateVisibility());
            this.updateVisibility(); // Initial visibility setup
        }
    }">
        <div class="mb-4">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">Filter Items</h3>
            <form id="itemFilterForm" hx-get="{% url 'item_master_table_partial' %}" hx-target="#itemMasterTableContainer" hx-swap="innerHTML">
                {% csrf_token %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="{{ form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Financial Year: From <span class="font-bold text-blue-700">{{ lbl_from_date }}</span> To <span class="font-bold text-blue-700">{{ lbl_to_date }}</span>
                        </label>
                        <div class="mt-1 flex items-center space-x-2">
                            <label for="{{ form.from_date.id_for_label }}" class="text-sm text-gray-700">From Date:</label>
                            {{ form.from_date }}
                            <label for="{{ form.to_date.id_for_label }}" class="text-sm text-gray-700">To:</label>
                            {{ form.to_date }}
                        </div>
                        {% if form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.from_date.errors }}</p>{% endif %}
                        {% if form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.to_date.errors }}</p>{% endif %}
                        {% if messages %}
                            {% for message in messages %}
                                <div class="text-red-500 text-xs mt-1">{{ message }}</div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label for="{{ form.type_selection.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Type</label>
                        <select name="{{ form.type_selection.name }}" id="{{ form.type_selection.id_for_label }}" 
                                class="{{ form.type_selection.css_classes }}" x-model="type_selection">
                            {% for value, label in form.type_selection.field.choices %}
                                <option value="{{ value }}" {% if value == form.type_selection.value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div x-show="type_selection !== 'Select'">
                        <label for="{{ form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Code</label>
                        <select name="{{ form.search_code.name }}" id="{{ form.search_code.id_for_label }}" 
                                class="{{ form.search_code.css_classes }}" x-model="search_code_selection">
                            <template x-for="option in getSearchCodeOptions()" :key="option.value">
                                <option :value="option.value" x-text="option.text" :selected="option.value === search_code_selection"></option>
                            </template>
                        </select>
                    </div>

                    <div class="flex items-end gap-2">
                        <div x-show="show_search_text && type_selection !== 'Select'" class="w-full">
                            <label for="{{ form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Item/Description</label>
                            {{ form.search_text }}
                        </div>
                        <div x-show="show_designation_dropdown && type_selection === 'Name'" class="w-full">
                            <label for="{{ form.designation.id_for_label }}" class="block text-sm font-medium text-gray-700">Designation</label>
                            <select name="{{ form.designation.name }}" id="{{ form.designation.id_for_label }}" class="{{ form.designation.css_classes }}">
                                {% for obj in designations %}
                                    <option value="{{ obj.type }}">{{ obj.type }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div x-show="show_department_dropdown && type_selection === 'Name'" class="w-full">
                            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">Department</label>
                            <select name="{{ form.department.name }}" id="{{ form.department.id_for_label }}" class="{{ form.department.css_classes }}">
                                {% for obj in departments %}
                                    <option value="{{ obj.description }}">{{ obj.description }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div x-show="show_employee_id_dropdown && type_selection === 'Name'" class="w-full">
                            <label for="{{ form.employee_id.id_for_label }}" class="block text-sm font-medium text-gray-700">Employee ID</label>
                            <select name="{{ form.employee_id.name }}" id="{{ form.employee_id.id_for_label }}" class="{{ form.employee_id.css_classes }}">
                                {% for obj in employee_users %}
                                    <option value="{{ obj.username }}">{{ obj.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        {# Placeholder for Dropwo - visibility depends on type_selection #}
                        <div x-show="type_selection === 'WONo'" class="w-full">
                            <label for="{{ form.work_order.id_for_label }}" class="block text-sm font-medium text-gray-700">Work Order No.</label>
                            {{ form.work_order }}
                        </div>
                        
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">Search</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Tab Container Placeholder for other tabs -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs" x-data="{ currentTab: 'Daily Reporting Tracker System' }">
                <button @click="currentTab = 'Daily Reporting Tracker System'" :class="{ 'border-indigo-500 text-indigo-600': currentTab === 'Daily Reporting Tracker System', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': currentTab !== 'Daily Reporting Tracker System' }"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Daily Reporting Tracker System
                </button>
                <button @click="currentTab = 'Design Plan'" :class="{ 'border-indigo-500 text-indigo-600': currentTab === 'Design Plan', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': currentTab !== 'Design Plan' }"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Design Plan
                </button>
                <button @click="currentTab = 'Manufacturing Plan'" :class="{ 'border-indigo-500 text-indigo-600': currentTab === 'Manufacturing Plan', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': currentTab !== 'Manufacturing Plan' }"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Manufacturing Plan
                </button>
                <button @click="currentTab = 'Vendor Plan'" :class="{ 'border-indigo-500 text-indigo-600': currentTab === 'Vendor Plan', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': currentTab !== 'Vendor Plan' }"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm">
                    Vendor Plan
                </button>
            </nav>
        </div>

        <div x-show="currentTab === 'Daily Reporting Tracker System'" class="mt-6">
            <h3 class="text-xl font-semibold mb-4 text-gray-700">Project Items</h3>
            <div id="itemMasterTableContainer" 
                 hx-trigger="load, reloadItemMasterTable from:body, showMessage"
                 hx-get="{% url 'item_master_table_partial' %}" 
                 hx-indicator="#table-loading-indicator"
                 hx-target="this" 
                 hx-swap="innerHTML">
                <!-- DataTables table will be loaded here via HTMX -->
                <div id="table-loading-indicator" class="htmx-indicator flex items-center justify-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="ml-3 text-gray-600">Loading data...</p>
                </div>
            </div>
        </div>
        
        {# Placeholder for other tab content #}
        <div x-show="currentTab === 'Design Plan'" class="mt-6">
            <p class="text-gray-600">Design Plan content goes here. This would be a separate set of Django models, forms, and views.</p>
        </div>
        <div x-show="currentTab === 'Manufacturing Plan'" class="mt-6">
            <p class="text-gray-600">Manufacturing Plan content goes here. This would be a separate set of Django models, forms, and views.</p>
        </div>
        <div x-show="currentTab === 'Vendor Plan'" class="mt-6">
            <p class="text-gray-600">Vendor Plan content goes here. This would be a separate set of Django models, forms, and views.</p>
        </div>

    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css" rel="stylesheet" type="text/css" />
<script>
    // Global function to show messages, triggered by HX-Trigger: 'showMessage'
    document.body.addEventListener('showMessage', function(evt) {
        // You would typically have a dedicated message display area or a toast notification system.
        // For demonstration, we'll log to console or append to a div.
        console.log('Message triggered by HTMX:', evt.detail.value || 'Operation completed.');
        // Example: Append a message to a div, assuming you have one for messages
        // var messagesDiv = document.getElementById('global-messages');
        // if (messagesDiv) {
        //     messagesDiv.innerHTML = '<div class="alert alert-info">' + (evt.detail.value || 'Operation completed.') + '</div>';
        // }
    });
</script>
{% endblock %}
```

##### `project_planning/_item_master_table.html` (Partial Template for DataTables)

This partial template will be loaded via HTMX into the `itemMasterTableContainer` div.

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="itemMasterDataTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for item in item_masters %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.item_code }}</td>
                <td class="py-3 px-4 text-sm text-gray-900">{{ item.manf_desc }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-center">{{ item.uom_basic }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500 text-right">{{ item.stock_qty }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-center">
                    <a href="{{ item.get_absolute_url }}" class="text-indigo-600 hover:text-indigo-900 mr-2">Select</a>
                    {% if item.file_data %}
                    <a href="{{ item.get_image_download_url }}" class="text-blue-600 hover:text-blue-900 mr-2">Image</a>
                    {% endif %}
                    {% if item.att_data %}
                    <a href="{{ item.get_spec_sheet_download_url }}" class="text-green-600 hover:text-green-900">Spec. Sheet</a>
                    {% endif %}
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    // Check if DataTable is already initialized on this table to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#itemMasterDataTable')) {
        $('#itemMasterDataTable').DataTable().destroy(); // Destroy existing instance
    }
    $('#itemMasterDataTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "searching": true, // Enable search box provided by DataTables
        "paging": true,    // Enable pagination
        "ordering": true   // Enable sorting
    });
</script>

```

#### 4.5 URLs (`project_planning/urls.py`)

Define the URL patterns to map requests to the new Django views.

```python
from django.urls import path
from .views import ProjectPlanSummaryView, ItemMasterTablePartialView, DownloadFileView, StockLedgerDetailsView

urlpatterns = [
    # Main project plan summary page (Daily Reporting Tracker System tab)
    path('project-plan-summary/', ProjectPlanSummaryView.as_view(), name='project_plan_summary'),
    
    # HTMX endpoint for the item master data table
    path('project-plan-summary/table/', ItemMasterTablePartialView.as_view(), name='item_master_table_partial'),
    
    # Endpoint for downloading files (images/spec sheets)
    path('item-master/<int:pk>/download/<str:file_type>/', DownloadFileView.as_view(), name='item_master_download_file'),

    # Placeholder for the StockLedger_Details.aspx equivalent
    path('stock-ledger-details/<int:pk>/', StockLedgerDetailsView.as_view(), name='stock_ledger_details'),
]

```

#### 4.6 Tests (`project_planning/tests.py`)

Comprehensive unit tests for models and integration tests for views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from io import BytesIO

from .models import ItemMaster, FinancialYear, Designation, Department, EmployeeUser

class ModelSetupMixin:
    """Helper mixin to set up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.financial_year = FinancialYear.objects.create(
            id=1, comp_id=1, from_date=date(2023, 1, 1), to_date=date(2023, 12, 31)
        )
        cls.designation_dev = Designation.objects.create(id=1, type='Developer')
        cls.designation_qa = Designation.objects.create(id=2, type='QA Engineer')
        cls.department_it = Department.objects.create(id=1, description='IT')
        cls.department_hr = Department.objects.create(id=2, description='HR')
        cls.employee_john = EmployeeUser.objects.create(id='A0000000-0000-0000-0000-000000000001', username='john.doe')
        cls.employee_jane = EmployeeUser.objects.create(id='A0000000-0000-0000-0000-000000000002', username='jane.smith')

        # Create ItemMaster instances
        cls.item1 = ItemMaster.objects.create(
            id=1, item_code='ITEM001', manf_desc='Test Item Description 1', uom_basic='PCS',
            stock_qty=100.50, file_name='image1.jpg', file_data=b'imagedata1', content_type='image/jpeg',
            att_name='spec1.pdf', att_data=b'specdata1', att_content_type='application/pdf',
            location='IT', comp_id=1, fin_year_id=1
        )
        cls.item2 = ItemMaster.objects.create(
            id=2, item_code='ITEM002', manf_desc='Another Test Item', uom_basic='KG',
            stock_qty=50.00, file_name=None, file_data=None, content_type=None,
            att_name=None, att_data=None, att_content_type=None,
            location='HR', comp_id=1, fin_year_id=1
        )
        cls.item3 = ItemMaster.objects.create(
            id=3, item_code='ITEM003-WO', manf_desc='Work Order Related Item', uom_basic='MTR',
            stock_qty=25.75, file_name='image3.png', file_data=b'imagedata3', content_type='image/png',
            att_name=None, att_data=None, att_content_type=None,
            location='IT', comp_id=1, fin_year_id=1
        )

class ItemMasterModelTest(ModelSetupMixin, TestCase):
    def test_item_master_creation(self):
        item = ItemMaster.objects.get(id=self.item1.id)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.manf_desc, 'Test Item Description 1')
        self.assertEqual(item.stock_qty, 100.50)
        self.assertEqual(item.file_name, 'image1.jpg')
        self.assertEqual(item.file_data, b'imagedata1')
        self.assertEqual(item.location, 'IT')

    def test_item_master_str(self):
        item = ItemMaster.objects.get(id=self.item1.id)
        self.assertEqual(str(item), 'ITEM001')

    def test_get_absolute_url(self):
        item = ItemMaster.objects.get(id=self.item1.id)
        url = item.get_absolute_url()
        self.assertIn(reverse('stock_ledger_details', args=[item.id]), url)
        self.assertIn("FD=", url)
        self.assertIn("TD=", url)

    def test_get_image_download_url(self):
        item = ItemMaster.objects.get(id=self.item1.id)
        self.assertIsNotNone(item.get_image_download_url())
        item_no_image = ItemMaster.objects.get(id=self.item2.id)
        self.assertIsNone(item_no_image.get_image_download_url())

    def test_get_spec_sheet_download_url(self):
        item = ItemMaster.objects.get(id=self.item1.id)
        self.assertIsNotNone(item.get_spec_sheet_download_url())
        item_no_spec = ItemMaster.objects.get(id=self.item2.id)
        self.assertIsNone(item_no_spec.get_spec_sheet_download_url())

    def test_calculate_current_value(self):
        item = ItemMaster.objects.get(id=self.item1.id)
        self.assertEqual(item.calculate_current_value(5.00), 100.50 * 5.00)

    def test_get_filtered_items_no_filters(self):
        items = ItemMaster.objects.get_filtered_items(None, None, 'Select', 'Select', '', 1, 1)
        self.assertEqual(items.count(), 3) # All items initially

    def test_get_filtered_items_by_item_code(self):
        items = ItemMaster.objects.get_filtered_items(None, None, 'WONo', 'tblDG_Item_Master.ItemCode', 'ITEM001', 1, 1)
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM001')

    def test_get_filtered_items_by_manf_desc(self):
        items = ItemMaster.objects.get_filtered_items(None, None, 'WONo', 'tblDG_Item_Master.ManfDesc', 'Test Item', 1, 1)
        self.assertEqual(items.count(), 2) # ITEM001 and ITEM002 descriptions both contain 'Test Item' (if 'Test Item' is assumed to be common)
        self.assertIn(self.item1, items)
        self.assertIn(self.item2, items) # Assuming 'Another Test Item' matches 'Test Item' due to `icontains`

    def test_get_filtered_items_by_location(self):
        items = ItemMaster.objects.get_filtered_items(None, None, 'Name', 'tblHR_Departments.Description', 'IT', 1, 1)
        self.assertEqual(items.count(), 2)
        self.assertIn(self.item1, items)
        self.assertIn(self.item3, items)


class ProjectPlanViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        self.project_plan_summary_url = reverse('project_plan_summary')
        self.item_master_table_partial_url = reverse('item_master_table_partial')
        self.download_image_url = reverse('item_master_download_file', args=[self.item1.id, 'image'])
        self.download_spec_sheet_url = reverse('item_master_download_file', args=[self.item1.id, 'spec_sheet'])
        self.stock_ledger_details_url = reverse('stock_ledger_details', args=[self.item1.id])

    def test_project_plan_summary_view_get(self):
        response = self.client.get(self.project_plan_summary_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/project_plan_summary.html')
        self.assertContains(response, 'Daily Reporting Tracker System')
        self.assertContains(response, 'Search Text')
        self.assertContains(response, '<table id="itemMasterDataTable"') # Container for HTMX load

    def test_item_master_table_partial_view_get_no_filters(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.item_master_table_partial_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/_item_master_table.html')
        self.assertContains(response, self.item1.item_code)
        self.assertContains(response, self.item2.item_code)
        self.assertContains(response, self.item3.item_code)

    def test_item_master_table_partial_view_get_with_filters(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Filter by Item Code
        response = self.client.get(self.item_master_table_partial_url + '?type_selection=WONo&search_code=tblDG_Item_Master.ItemCode&search_text=ITEM001', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.item1.item_code)
        self.assertNotContains(response, self.item2.item_code)

        # Filter by Description (case-insensitive)
        response = self.client.get(self.item_master_table_partial_url + '?type_selection=WONo&search_code=tblDG_Item_Master.ManfDesc&search_text=test item', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.item1.item_code)
        self.assertContains(response, self.item2.item_code)

        # Filter by Location (Department)
        response = self.client.get(self.item_master_table_partial_url + '?type_selection=Name&search_code=tblHR_Departments.Description&search_text=IT', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.item1.item_code)
        self.assertContains(response, self.item3.item_code)
        self.assertNotContains(response, self.item2.item_code)

    def test_item_master_table_partial_view_invalid_form(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Invalid date range
        response = self.client.get(self.item_master_table_partial_url + '?from_date=2024-01-01&to_date=2023-01-01', **headers)
        self.assertEqual(response.status_code, 200) # Still 200 for HTMX, but content should be empty/error
        self.assertContains(response, 'No data to display !')
        self.assertContains(response, 'Invalid filter criteria provided.') # Message should be present

    def test_download_image_file_view(self):
        response = self.client.get(self.download_image_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'image/jpeg')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="image1.jpg"')
        self.assertEqual(response.content, b'imagedata1')

    def test_download_spec_sheet_file_view(self):
        response = self.client.get(self.download_spec_sheet_url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="spec1.pdf"')
        self.assertEqual(response.content, b'specdata1')

    def test_download_file_not_found(self):
        # Test item without image/spec sheet
        url = reverse('item_master_download_file', args=[self.item2.id, 'image'])
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(url, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content status
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showMessage', response.headers['HX-Trigger'])

    def test_download_file_item_not_found(self):
        url = reverse('item_master_download_file', args=[9999, 'image'])
        with self.assertRaises(Http404):
            self.client.get(url)

    def test_stock_ledger_details_view(self):
        from_date_str = date(2023, 1, 1).strftime('%d-%m-%Y')
        to_date_str = date(2023, 1, 31).strftime('%d-%m-%Y')
        response = self.client.get(f"{self.stock_ledger_details_url}?FD={from_date_str}&TD={to_date_str}")
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_planning/stock_ledger_details.html')
        self.assertContains(response, f"Item ID: {self.item1.id}")
        self.assertContains(response, f"from {from_date_str} to {to_date_str}")
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The modernization utilizes HTMX for dynamic content loading and Alpine.js for interactive UI elements without writing custom JavaScript.

*   **HTMX for Table Refresh:**
    *   The main `project_plan_summary.html` uses `hx-get="{% url 'item_master_table_partial' %}"` on the `itemMasterTableContainer` div.
    *   `hx-trigger="load, reloadItemMasterTable from:body, showMessage"` ensures the table loads on page entry, and refreshes when `reloadItemMasterTable` or `showMessage` custom events are triggered from any part of the body.
    *   The filter form (`#itemFilterForm`) has `hx-get="{% url 'item_master_table_partial' %}"` and `hx-target="#itemMasterTableContainer" hx-swap="innerHTML"`. Submitting this form will re-fetch and replace the table content.
    *   Download links directly point to Django views, returning `FileResponse`. For actions like "Select", the link points to a new Django view (`stock_ledger_details`).
*   **Alpine.js for UI State Management:**
    *   The `x-data` attribute on the main filter `div` (`<div x-data="{ ... }">`) defines the state variables (`type_selection`, `search_code_selection`, `show_search_text`, etc.).
    *   `x-model` binds `type_selection` and `search_code_selection` to the respective dropdowns, updating the Alpine.js state when their values change.
    *   `x-show` attributes dynamically hide/show related input fields (e.g., `txtSearchItemCode`, `Drpdep`, `Drpdesi`, `Drpempid`) based on the `type_selection` and `search_code_selection` state variables, as determined by the `updateVisibility()` function.
    *   The `getSearchCodeOptions()` function dynamically changes the options available in the `search_code` dropdown based on `type_selection`, directly within Alpine.js.
*   **DataTables for List Views:**
    *   The `_item_master_table.html` partial uses `<table id="itemMasterDataTable" ...>` and contains the JavaScript for DataTables initialization: `$('#itemMasterDataTable').DataTable({...});`.
    *   The DataTables initialization is placed within the partial itself, ensuring it runs every time the table content is loaded/refreshed via HTMX, thus always correctly initializing the DataTables plugin on the newly loaded HTML.

### Final Notes

*   **Placeholders:** The "Quantity Wise," "Shortage Wise," and "Supplier Wise" tabs are represented as placeholders in `project_plan_summary.html`. Each of these would follow a similar migration pattern: identify models, forms, views, and templates based on their specific ASP.NET code, often involving separate data grids and filter logic.
*   **Database Mapping:** The `db_column` and `db_table` in models are crucial for mapping to your existing database structure without requiring Django migrations. Ensure these are accurately set for all your tables.
*   **Security:** Always implement proper authentication and authorization in Django. The ASP.NET code shows `Session["compid"]` and `Session["finyear"]`, which would typically be derived from the logged-in user's profile or session in Django.
*   **Error Handling:** The current setup includes basic error messages. For production, implement robust logging and user-friendly error displays.
*   **Scalability:** Django, HTMX, and DataTables provide a highly scalable foundation. For very large datasets, consider server-side processing for DataTables if client-side performance becomes an issue.
*   **Refinement:** The `ItemMasterQuerySet.get_filtered_items` method is a direct translation of the `Fillgrid` logic. This can be further optimized and refined using Django's `Q` objects and more advanced ORM features to handle complex conditions efficiently.