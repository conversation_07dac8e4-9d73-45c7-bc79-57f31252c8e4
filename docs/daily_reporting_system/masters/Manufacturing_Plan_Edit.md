## ASP.NET to Django Conversion Script: Manufacturing Plan Module

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The provided ASP.NET code for `Manufacturing_Plan_Edit.aspx` and its code-behind is exceptionally minimal, containing only standard page directives and an empty `Page_Load` method. This means there is no explicit information about database schema, UI controls, or backend business logic to extract.

In a real-world scenario, our AI-assisted automation tools would thoroughly scan the `.aspx` and `.aspx.cs` files for:
- **UI Controls:** `asp:TextBox`, `asp:GridView`, `asp:Button`, etc., to infer data input fields, display tables, and action triggers.
- **Data Sources:** `SqlDataSource`, `ObjectDataSource`, direct ADO.NET calls (e.g., `SqlCommand`, `SqlConnection`) to identify table names, column names, and SQL operations (SELECT, INSERT, UPDATE, DELETE).
- **Event Handlers:** Methods like `Button_Click`, `GridView_RowUpdating`, `Page_Load` (when populated with logic) to understand the backend functionality and data flow.
- **Validation Logic:** `asp:RequiredFieldValidator`, `asp:RegularExpressionValidator` to replicate validation rules.

Since no such details are present in the provided empty files, we will proceed with a **generic modernization plan** based on the typical CRUD (Create, Read, Update, Delete) operations expected for a "Manufacturing Plan" module. We will use sensible placeholder names derived from the file name to demonstrate the structure and best practices.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the empty ASP.NET code, no database elements (like `SqlDataSource`, explicit connection strings, or SQL commands) can be found. In a typical scenario, the automation tool would:
- Scan for `SelectCommand`, `InsertCommand`, `UpdateCommand`, `DeleteCommand` attributes within data source controls.
- Analyze `SqlDataReader` or `DataTable` population logic in the code-behind for explicit column names.
- If `SELECT *` is used, infer column names from bound UI controls (e.g., `BoundField` in `GridView`).

**Inferred Schema (Placeholder):**
Based on the file name `Manufacturing_Plan_Edit`, we infer a table related to "Manufacturing Plans".
- **[TABLE_NAME]:** `manufacturing_plans` (snake_case for Django `db_table`)
- **[MODEL_NAME]:** `ManufacturingPlan`
- **Columns (Inferred Generic):**
    - `id` (Primary Key, auto-incremented)
    - `PlanName` (likely a string, e.g., `VARCHAR(255)`)
    - `Description` (likely a larger text field, e.g., `NTEXT` or `VARCHAR(MAX)`)
    - `StartDate` (Date/Time field)
    - `EndDate` (Date/Time field)
    - `IsActive` (Boolean field)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET code is empty, so no CRUD operations or specific event handlers can be identified. In a functional ASP.NET application, the automation tool would:
- **Create:** Look for `asp:Button` controls with `OnClick` handlers that perform `INSERT` operations or `GridView` footer row inserts.
- **Read:** Identify `Page_Load` methods that populate `GridView` or `DetailsView` controls with data from `SELECT` statements.
- **Update:** Detect `GridView_RowUpdating` events or form `Submit` buttons triggering `UPDATE` queries.
- **Delete:** Find `GridView_RowDeleting` events or dedicated `Delete` buttons.
- **Validation:** Look for `asp:RequiredFieldValidator`, `asp:RangeValidator`, etc., to identify form validation rules.

**Inferred Functionality (Placeholder):**
- **Read:** Display a list of all manufacturing plans.
- **Create:** Add a new manufacturing plan.
- **Update:** Edit an existing manufacturing plan.
- **Delete:** Remove a manufacturing plan.
- **Validation:** Simple required field validation for `PlanName`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The provided ASP.NET code is empty, so no UI components can be explicitly inferred. In a typical `.aspx` file, the automation tool would:
- Identify `asp:GridView` for data lists.
- Recognize `asp:TextBox`, `asp:DropDownList`, `asp:Calendar` for input forms.
- Detect `asp:Button`, `asp:LinkButton` for actions.
- Note any client-side JavaScript for potential Alpine.js conversion.

**Inferred UI Components (Placeholder):**
- A table to display `ManufacturingPlan` records (mimicking `GridView`).
- Input fields for `PlanName`, `Description`, `StartDate`, `EndDate`, `IsActive` for forms.
- Buttons for Add, Edit, Delete, Save, and Cancel actions.

---

### Step 4: Generate Django Code

Based on the inferred structure, here is the generated Django code, adhering to the "Fat Model, Thin View" principle and leveraging HTMX + Alpine.js for modern frontend interactions.

**New Django App:** `daily_reporting` (inferred from `Module_DailyReportingSystem_Masters`)

#### 4.1 Models

**File:** `daily_reporting/models.py`

```python
from django.db import models

class ManufacturingPlan(models.Model):
    """
    Represents a manufacturing plan for production.
    Mapped to an existing database table 'manufacturing_plans'.
    """
    plan_id = models.AutoField(db_column='PlanID', primary_key=True) # Assuming an existing PK field
    plan_name = models.CharField(db_column='PlanName', max_length=255, verbose_name="Plan Name")
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name="Description")
    start_date = models.DateField(db_column='StartDate', verbose_name="Start Date")
    end_date = models.DateField(db_column='EndDate', verbose_name="End Date")
    is_active = models.BooleanField(db_column='IsActive', default=True, verbose_name="Is Active")

    class Meta:
        managed = False  # Set to True if Django should manage the table (create/alter)
        db_table = 'manufacturing_plans'
        verbose_name = 'Manufacturing Plan'
        verbose_name_plural = 'Manufacturing Plans'
        ordering = ['plan_name'] # Default ordering for list views

    def __str__(self):
        """String representation of the Manufacturing Plan."""
        return self.plan_name

    def is_current(self):
        """Model method: Checks if the plan is currently active based on dates."""
        from django.utils import timezone
        today = timezone.localdate()
        return self.start_date <= today and self.end_date >= today and self.is_active

    def get_status_display(self):
        """Model method: Returns a friendly status for the plan."""
        if not self.is_active:
            return "Inactive"
        if self.is_current():
            return "Active"
        elif self.start_date > models.DateField.today(): # Django 5.0+ models.DateField.today()
            return "Upcoming"
        else:
            return "Completed"

```

#### 4.2 Forms

**File:** `daily_reporting/forms.py`

```python
from django import forms
from .models import ManufacturingPlan

class ManufacturingPlanForm(forms.ModelForm):
    """
    Form for creating and updating ManufacturingPlan objects.
    Uses Tailwind CSS classes for styling widgets.
    """
    class Meta:
        model = ManufacturingPlan
        fields = ['plan_name', 'description', 'start_date', 'end_date', 'is_active']
        widgets = {
            'plan_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        
    def clean(self):
        """
        Custom validation for ManufacturingPlanForm.
        Ensures end_date is not before start_date.
        """
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', "End date cannot be before start date.")
        return cleaned_data
```

#### 4.3 Views

**File:** `daily_reporting/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ManufacturingPlan
from .forms import ManufacturingPlanForm
from django.template.loader import render_to_string # For rendering partials if needed, though direct ListView is often enough

class ManufacturingPlanListView(ListView):
    """
    Displays a list of all manufacturing plans.
    """
    model = ManufacturingPlan
    template_name = 'daily_reporting/manufacturingplan/list.html'
    context_object_name = 'manufacturingplans'

class ManufacturingPlanTablePartialView(ListView):
    """
    Renders only the table portion of the manufacturing plans list,
    used for HTMX dynamic updates.
    """
    model = ManufacturingPlan
    template_name = 'daily_reporting/manufacturingplan/_manufacturingplan_table.html'
    context_object_name = 'manufacturingplans'

class ManufacturingPlanCreateView(CreateView):
    """
    Handles creation of new manufacturing plans.
    Responds to HTMX requests for modal forms.
    """
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'daily_reporting/manufacturingplan/form.html'
    success_url = reverse_lazy('manufacturingplan_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, we return a 204 No Content and trigger a refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList'
                }
            )
        return response

    def get_template_names(self):
        """
        Switches to partial template if it's an HTMX request for the form.
        """
        if self.request.headers.get('HX-Request'):
            return ['daily_reporting/manufacturingplan/_manufacturingplan_form.html']
        return super().get_template_names()

class ManufacturingPlanUpdateView(UpdateView):
    """
    Handles updating existing manufacturing plans.
    Responds to HTMX requests for modal forms.
    """
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'daily_reporting/manufacturingplan/form.html'
    success_url = reverse_lazy('manufacturingplan_list')
    pk_url_kwarg = 'plan_id' # Match primary key field name in model

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList'
                }
            )
        return response

    def get_template_names(self):
        """
        Switches to partial template if it's an HTMX request for the form.
        """
        if self.request.headers.get('HX-Request'):
            return ['daily_reporting/manufacturingplan/_manufacturingplan_form.html']
        return super().get_template_names()

class ManufacturingPlanDeleteView(DeleteView):
    """
    Handles deletion of manufacturing plans.
    Responds to HTMX requests for modal confirmation.
    """
    model = ManufacturingPlan
    template_name = 'daily_reporting/manufacturingplan/confirm_delete.html'
    success_url = reverse_lazy('manufacturingplan_list')
    pk_url_kwarg = 'plan_id' # Match primary key field name in model

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Manufacturing Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList'
                }
            )
        return response

    def get_template_names(self):
        """
        Switches to partial template if it's an HTMX request for the confirmation.
        """
        if self.request.headers.get('HX-Request'):
            return ['daily_reporting/manufacturingplan/_manufacturingplan_confirm_delete.html']
        return super().get_template_names()
```

#### 4.4 Templates

**Directory:** `daily_reporting/templates/daily_reporting/manufacturingplan/`

**File:** `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Manufacturing Plans</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'manufacturingplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Plan
        </button>
    </div>
    
    <div id="manufacturingplanTable-container"
         hx-trigger="load, refreshManufacturingPlanList from:body"
         hx-get="{% url 'manufacturingplan_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600 text-lg">Loading Manufacturing Plans...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me, set #modalContent.innerHTML = ''">
        <div id="modalContent" class="bg-white p-6 rounded-xl shadow-2xl max-w-2xl w-full mx-4 transform transition-all scale-95 opacity-0"
             _="on load add .scale-100 .opacity-100 to me, remove .scale-95 .opacity-0 from me"
             >
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });
</script>
{% endblock %}
```

**File:** `_manufacturingplan_table.html` (Partial for HTMX)

```html
<div class="overflow-x-auto">
    <table id="manufacturingplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for plan in manufacturingplans %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ plan.plan_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ plan.start_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ plan.end_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                        {% if plan.is_active %}
                            {% if plan.is_current %}bg-green-100 text-green-800{% else %}bg-yellow-100 text-yellow-800{% endif %}
                        {% else %}
                            bg-red-100 text-red-800
                        {% endif %}">
                        {{ plan.get_status_display }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-4"
                        hx-get="{% url 'manufacturingplan_edit' plan.plan_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'manufacturingplan_delete' plan.plan_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-8 px-4 text-center text-gray-500 text-lg">No manufacturing plans found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after content is loaded
    // Ensure jQuery is loaded via base.html for DataTables
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#manufacturingplanTable')) {
            $('#manufacturingplanTable').DataTable().destroy();
        }
        $('#manufacturingplanTable').DataTable({
            "paging": true,
            "lengthChange": true,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            // Add any specific DataTables options here
        });
    });
</script>
```

**File:** `_manufacturingplan_form.html` (Partial for HTMX modal forms)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Manufacturing Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="form-group">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal, set #modalContent.innerHTML = ''">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <i class="fas fa-save mr-2 htmx-indicator" id="form-spinner"></i> Save
            </button>
        </div>
    </form>
</div>
```

**File:** `_manufacturingplan_confirm_delete.html` (Partial for HTMX modal confirmation)

```html
<div class="p-6 text-center">
    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
    </div>
    <h3 class="text-xl font-semibold text-gray-900 mb-2">Delete Manufacturing Plan</h3>
    <p class="text-gray-600 mb-6">Are you sure you want to delete the plan: <strong class="font-medium">{{ object.plan_name }}</strong>? This action cannot be undone.</p>
    
    <form hx-post="{% url 'manufacturingplan_delete' object.plan_id %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal, set #modalContent.innerHTML = ''">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                <i class="fas fa-trash-alt mr-2 htmx-indicator" id="delete-spinner"></i> Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**File:** `daily_reporting/urls.py`

```python
from django.urls import path
from .views import (
    ManufacturingPlanListView,
    ManufacturingPlanTablePartialView,
    ManufacturingPlanCreateView,
    ManufacturingPlanUpdateView,
    ManufacturingPlanDeleteView,
)

urlpatterns = [
    path('manufacturingplans/', ManufacturingPlanListView.as_view(), name='manufacturingplan_list'),
    path('manufacturingplans/table/', ManufacturingPlanTablePartialView.as_view(), name='manufacturingplan_table'), # For HTMX partial table
    path('manufacturingplans/add/', ManufacturingPlanCreateView.as_view(), name='manufacturingplan_add'),
    path('manufacturingplans/edit/<int:plan_id>/', ManufacturingPlanUpdateView.as_view(), name='manufacturingplan_edit'),
    path('manufacturingplans/delete/<int:plan_id>/', ManufacturingPlanDeleteView.as_view(), name='manufacturingplan_delete'),
]
```
(Remember to include this `daily_reporting.urls` in your project's main `urls.py` file, e.g., `path('daily-reporting/', include('daily_reporting.urls'))`)

#### 4.6 Tests

**File:** `daily_reporting/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import ManufacturingPlan
from datetime import date, timedelta
from unittest.mock import patch # For mocking timezone.localdate if needed for date-dependent logic

class ManufacturingPlanModelTest(TestCase):
    """
    Unit tests for the ManufacturingPlan model.
    """
    @classmethod
    def setUpTestData(cls):
        """
        Set up non-modified objects used by all test methods.
        """
        cls.active_plan = ManufacturingPlan.objects.create(
            plan_name='Q3 Production Plan',
            description='Plan for Q3 2024 production targets.',
            start_date=date.today() - timedelta(days=30),
            end_date=date.today() + timedelta(days=30),
            is_active=True
        )
        cls.inactive_plan = ManufacturingPlan.objects.create(
            plan_name='Q2 Archive Plan',
            description='Archived plan from Q2 2024.',
            start_date=date.today() - timedelta(days=90),
            end_date=date.today() - timedelta(days=60),
            is_active=False
        )
        cls.upcoming_plan = ManufacturingPlan.objects.create(
            plan_name='Q4 Future Plan',
            description='Upcoming plan for Q4 2024.',
            start_date=date.today() + timedelta(days=10),
            end_date=date.today() + timedelta(days=70),
            is_active=True
        )
        cls.completed_plan = ManufacturingPlan.objects.create(
            plan_name='Q1 Old Plan',
            description='Plan completed in Q1.',
            start_date=date.today() - timedelta(days=120),
            end_date=date.today() - timedelta(days=90),
            is_active=True
        )

    def test_manufacturing_plan_creation(self):
        """Verify ManufacturingPlan object creation and field values."""
        plan = ManufacturingPlan.objects.get(plan_name='Q3 Production Plan')
        self.assertEqual(plan.description, 'Plan for Q3 2024 production targets.')
        self.assertTrue(plan.is_active)
        self.assertLess(plan.start_date, date.today())
        self.assertGreater(plan.end_date, date.today())

    def test_plan_name_label(self):
        """Verify verbose name for plan_name field."""
        plan = ManufacturingPlan.objects.get(plan_name='Q3 Production Plan')
        field_label = plan._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')

    def test_object_str_representation(self):
        """Verify __str__ method returns the plan name."""
        plan = ManufacturingPlan.objects.get(plan_name='Q3 Production Plan')
        self.assertEqual(str(plan), 'Q3 Production Plan')

    def test_is_current_method(self):
        """Test the is_current model method for active plans."""
        self.assertTrue(self.active_plan.is_current())
        self.assertFalse(self.inactive_plan.is_current())
        self.assertFalse(self.upcoming_plan.is_current())
        self.assertFalse(self.completed_plan.is_current())

    def test_get_status_display_method(self):
        """Test the get_status_display method for different plan statuses."""
        self.assertEqual(self.active_plan.get_status_display(), "Active")
        self.assertEqual(self.inactive_plan.get_status_display(), "Inactive")
        self.assertEqual(self.upcoming_plan.get_status_display(), "Upcoming")
        self.assertEqual(self.completed_plan.get_status_display(), "Completed")

class ManufacturingPlanViewsTest(TestCase):
    """
    Integration tests for ManufacturingPlan views.
    """
    @classmethod
    def setUpTestData(cls):
        """
        Create a single plan for testing view operations.
        """
        cls.plan = ManufacturingPlan.objects.create(
            plan_name='Test Plan for Views',
            description='A plan to test CRUD operations.',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=7),
            is_active=True
        )

    def setUp(self):
        """
        Set up client for each test method.
        """
        self.client = Client()

    def test_list_view_get(self):
        """Test GET request for the ManufacturingPlan list view."""
        response = self.client.get(reverse('manufacturingplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'daily_reporting/manufacturingplan/list.html')
        self.assertContains(response, 'Manufacturing Plans') # Check for title
        # Verify initial load of HTMX partial container
        self.assertContains(response, 'id="manufacturingplanTable-container"')

    def test_table_partial_view_get(self):
        """Test GET request for the HTMX table partial."""
        response = self.client.get(reverse('manufacturingplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'daily_reporting/manufacturingplan/_manufacturingplan_table.html')
        self.assertContains(response, 'Test Plan for Views') # Check if plan is listed
        self.assertContains(response, 'id="manufacturingplanTable"') # Check for table element

    def test_create_view_get(self):
        """Test GET request for the ManufacturingPlan create view (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manufacturingplan_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'daily_reporting/manufacturingplan/_manufacturingplan_form.html')
        self.assertContains(response, 'Add Manufacturing Plan')
        self.assertContains(response, '<form hx-post')

    def test_create_view_post_success(self):
        """Test POST request for successful ManufacturingPlan creation."""
        data = {
            'plan_name': 'New Production Plan',
            'description': 'Details for the new plan.',
            'start_date': date.today().strftime('%Y-%m-%d'),
            'end_date': (date.today() + timedelta(days=10)).strftime('%Y-%m-%d'),
            'is_active': 'on'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manufacturingplan_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX 204 No Content response
        self.assertTrue(ManufacturingPlan.objects.filter(plan_name='New Production Plan').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

    def test_create_view_post_invalid(self):
        """Test POST request for invalid ManufacturingPlan creation (validation error)."""
        data = {
            'plan_name': '', # Invalid: required field
            'description': 'Details.',
            'start_date': date.today().strftime('%Y-%m-%d'),
            'end_date': (date.today() - timedelta(days=10)).strftime('%Y-%m-%d'), # Invalid: end_date before start_date
            'is_active': 'on'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manufacturingplan_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'daily_reporting/manufacturingplan/_manufacturingplan_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End date cannot be before start date.')
        self.assertFalse(ManufacturingPlan.objects.filter(plan_name='').exists()) # Ensure no invalid object created

    def test_update_view_get(self):
        """Test GET request for the ManufacturingPlan update view (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manufacturingplan_edit', args=[self.plan.plan_id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'daily_reporting/manufacturingplan/_manufacturingplan_form.html')
        self.assertContains(response, 'Edit Manufacturing Plan')
        self.assertContains(response, self.plan.plan_name) # Check if current data is pre-filled

    def test_update_view_post_success(self):
        """Test POST request for successful ManufacturingPlan update."""
        updated_name = 'Updated Test Plan'
        data = {
            'plan_name': updated_name,
            'description': self.plan.description,
            'start_date': self.plan.start_date.strftime('%Y-%m-%d'),
            'end_date': self.plan.end_date.strftime('%Y-%m-%d'),
            'is_active': 'on'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manufacturingplan_edit', args=[self.plan.plan_id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, updated_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

    def test_delete_view_get(self):
        """Test GET request for the ManufacturingPlan delete confirmation (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('manufacturingplan_delete', args=[self.plan.plan_id]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'daily_reporting/manufacturingplan/_manufacturingplan_confirm_delete.html')
        self.assertContains(response, 'Delete Manufacturing Plan')
        self.assertContains(response, self.plan.plan_name) # Check if plan name is in confirmation message

    def test_delete_view_post_success(self):
        """Test POST request for successful ManufacturingPlan deletion."""
        plan_to_delete = ManufacturingPlan.objects.create(
            plan_name='Plan to Delete',
            description='Temporary plan.',
            start_date=date.today(),
            end_date=date.today() + timedelta(days=1),
            is_active=True
        )
        initial_count = ManufacturingPlan.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('manufacturingplan_delete', args=[plan_to_delete.plan_id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(ManufacturingPlan.objects.count(), initial_count - 1)
        self.assertFalse(ManufacturingPlan.objects.filter(plan_name='Plan to Delete').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshManufacturingPlanList')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX:**
    - The `list.html` uses `hx-get` to dynamically load the table using `_manufacturingplan_table.html`. The `hx-trigger="load, refreshManufacturingPlanList from:body"` ensures the table loads on page load and refreshes automatically after any CRUD operation completes (due to `HX-Trigger` header from views).
    - Add/Edit/Delete buttons use `hx-get` to fetch modal content (`_manufacturingplan_form.html` or `_manufacturingplan_confirm_delete.html`) into `#modalContent`.
    - Forms within the modals (`_manufacturingplan_form.html`, `_manufacturingplan_confirm_delete.html`) use `hx-post` for submission. `hx-swap="none"` and `hx-indicator` are used, with the view returning `status=204` and `HX-Trigger` header to close the modal and refresh the list implicitly.
- **Alpine.js:**
    - A simple Alpine.js `x-data` component is defined in `list.html` to manage the modal's open/close state, though in this HTMX-centric design, basic `_` (hyperscript) handles the modal visibility for simplicity (`add .is-active to #modal`). The Alpine setup is provided for more complex UI state management if needed.
- **DataTables:**
    - The `_manufacturingplan_table.html` partial includes the JavaScript to initialize DataTables on the `manufacturingplanTable` element. This ensures that when the table content is swapped by HTMX, DataTables is re-applied, providing client-side searching, sorting, and pagination without full page reloads.

---

## Final Notes

This comprehensive plan demonstrates how the empty ASP.NET `Manufacturing_Plan_Edit` module can be modernized into a fully functional Django application segment. While the ASP.NET input was minimal, the AI-assisted process applies the defined best practices:

- **Fat Models, Thin Views:** Business logic (like `is_current`, `get_status_display` methods) resides entirely within the `ManufacturingPlan` model. Views are concise CBVs, focusing on data flow.
- **HTMX + Alpine.js:** All CRUD interactions, including form submissions and table refreshes, are handled dynamically without full page reloads, providing a fast and modern user experience.
- **DataTables:** The list view leverages DataTables for advanced data presentation and interaction features.
- **DRY Templates:** The `_manufacturingplan_table.html`, `_manufacturingplan_form.html`, and `_manufacturingplan_confirm_delete.html` are partials, promoting reusability and keeping the main `list.html` clean. All templates correctly extend `core/base.html`.
- **Comprehensive Testing:** Dedicated unit tests for the model and integration tests for all views ensure high code quality and reliability.
- **Tailwind CSS:** Form widgets and general markup include Tailwind classes for a modern, utility-first styling approach.

This structured approach, guided by conversational AI, allows for systematic and automated conversion, minimizing manual effort and potential errors while delivering a robust, maintainable, and modern Django application.