## ASP.NET to Django Conversion Script: Design Plan Management Modernization

This document outlines a strategic plan to migrate your existing ASP.NET "Design Plan Edit Details" module to a modern Django-based solution. Our approach prioritizes automation, leveraging conversational AI for clear, actionable steps, and focuses on tangible business benefits.

### Business Value Proposition

Migrating this module to Django will deliver significant business advantages:

1.  **Enhanced User Experience:** Modern, responsive interfaces using HTMX and Alpine.js provide a smoother, faster interaction without full page reloads, improving user satisfaction and productivity.
2.  **Improved Maintainability & Scalability:** Django's structured, "fat model, thin view" architecture promotes cleaner code, making future enhancements easier and reducing the cost of ownership. It's built for growth.
3.  **Future-Proof Technology Stack:** Moving to Django, HTMX, and Alpine.js positions your application on a robust, open-source stack with a vibrant community and continuous innovation, reducing vendor lock-in.
4.  **Reduced Development Time & Cost:** By leveraging AI-assisted automation, we significantly cut down manual coding efforts during migration, leading to faster deployment and more efficient resource allocation.
5.  **Data Integrity & Performance:** Django's ORM (Object-Relational Mapper) provides a secure and efficient way to interact with your existing database, ensuring data integrity while optimizing performance for data retrieval and manipulation.
6.  **Standardized Development Practices:** Adopting Django's best practices, including robust testing (aiming for 80%+ coverage), ensures high-quality, reliable software.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The ASP.NET code reveals the following:
-   **Primary Table:** `DRTS_Desing_Plan_New` (from `SqlDataSource1` `SelectCommand` and C# `fun.select`).
-   **Columns in `DRTS_Desing_Plan_New` (as displayed by GridView or selected):**
    -   `idwono` (Work Order No.)
    -   `idsr` (Sr. No.)
    -   `idfxn` (Fixture No.)
    -   `idconcpd` (Concept Design)
    -   `idintrnrw` (Internal Review)
    -   `iddaps` (DAP Send)
    -   Implicitly from `fun.select` and C# logic: `Id` (likely PK), `FinYearId`, `CompId`, `SessionId`, `SysDate`, `iddapr`, `idcrr`.
-   **Lookup Table for Autocomplete:** `SD_Cust_master` (from C# `sql` WebMethod).
    -   **Columns:** `CustomerId`, `CustomerName`, `CompId`.
-   **Other Related Tables (for data transformation/lookups, not direct migration target here):**
    -   `tblFinancial_master`
    -   `tblHR_OfficeStaff`
    -   `tblcountry`, `tblState`, `tblCity` (commented out in C# but indicative of data relationships).

**Instructions:**
The core of this module is managing entries in `DRTS_Desing_Plan_New`. We will create a Django model for this table and for `SD_Cust_master` to support the autocomplete functionality.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
-   **Read (R):** The primary functionality is to display a list of "Design Plan" entries from `DRTS_Desing_Plan_New`. This list supports:
    -   **Search/Filter:** By `WoNo` (`idwono`) via `TxtSearchValue` and `Search` button.
    -   **Pagination:** Handled by `SearchGridView1_PageIndexChanging`.
    -   **Autocomplete:** For `TxtSearchValue` using `SD_Cust_master` data. (Note: The C# logic is inconsistent, using autocomplete for customer data but filtering for `idwono`). We will implement autocomplete for a generic search field and ensure the search filters `idwono`.
-   **Create (C), Update (U), Delete (D):** The provided ASP.NET code **does not explicitly show** UI or backend logic for creating, updating, or deleting `DRTS_Desing_Plan_New` records. The page title "Design Plan - Edit" and a "Submit" button suggest an update capability, but no editable fields are provided beyond the search box. For a comprehensive modernization, and aligning with common application requirements, we will **infer and implement standard CRUD operations** to allow full management of these records.

**Instructions:**
We will implement a Django `ListView` with integrated search, and standard `CreateView`, `UpdateView`, and `DeleteView` for full management. The search field will support autocomplete for `WoNo` or Customer Names as required.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
-   **Search Input:** `TxtSearchValue` (ASP.NET TextBox) with `AutoCompleteExtender`. This will become an HTML `<input>` field with HTMX attributes for live search and a separate HTMX endpoint for autocomplete suggestions.
-   **Search Button:** `Search` (ASP.NET Button). This will be an HTMX-triggered button or an implicit trigger on the search input.
-   **Data Display Grid:** `SearchGridView1` (ASP.NET GridView). This will be replaced by a standard HTML `<table>` element initialized with DataTables for rich client-side features (pagination, sorting, filtering).
-   **Submit Button:** `Button1` (ASP.NET Button). Its explicit role is unclear without an associated form, but it implies a submission action. In Django, this would typically be tied to a form.

**Instructions:**
The Django templates will use Tailwind CSS for styling. HTMX will handle all dynamic content loading (e.g., table refresh, modal forms), and Alpine.js will manage UI state (e.g., modal visibility).

---

### Step 4: Generate Django Code

We will create a Django application named `dailyreporting` to house this module.

#### 4.1 Models

**Task:** Create Django models based on the identified database schemas.

**Instructions:**
We'll create `DesignPlanNew` to map to `DRTS_Desing_Plan_New` and `CustomerMaster` for `SD_Cust_master` (for the autocomplete functionality).

**File: `dailyreporting/models.py`**
```python
from django.db import models

# Model for the main Design Plan records
class DesignPlanNew(models.Model):
    # 'Id' is often the primary key, assuming it's an identity column
    id = models.IntegerField(db_column='Id', primary_key=True)
    idwono = models.CharField(db_column='idwono', max_length=255, blank=True, null=True, verbose_name="WoNo")
    idsr = models.CharField(db_column='idsr', max_length=255, blank=True, null=True, verbose_name="Sr.No.")
    idfxn = models.CharField(db_column='idfxn', max_length=255, blank=True, null=True, verbose_name="FIXTURE NO.")
    idconcpd = models.CharField(db_column='idconcpd', max_length=255, blank=True, null=True, verbose_name="CONCEPT DESIGN")
    idintrnrw = models.CharField(db_column='idintrnrw', max_length=255, blank=True, null=True, verbose_name="INTERNAL REVIEW")
    iddaps = models.CharField(db_column='iddaps', max_length=255, blank=True, null=True, verbose_name="DAP SEND")
    iddapr = models.CharField(db_column='iddapr', max_length=255, blank=True, null=True) # Inferred from C# select
    idcrr = models.CharField(db_column='idcrr', max_length=255, blank=True, null=True) # Inferred from C# select
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True) # Inferred from C# select
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Inferred from C# select
    session_id = models.IntegerField(db_column='SessionId', blank=True, null=True) # Inferred from C# select
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True) # Inferred from C# select

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'DRTS_Desing_Plan_New' # Explicitly map to the existing table name
        verbose_name = 'Design Plan Entry'
        verbose_name_plural = 'Design Plan Entries'

    def __str__(self):
        return f"{self.idwono or 'N/A'} - {self.idfxn or 'N/A'}"

    # Business logic methods related to a DesignPlanNew entry go here
    # Example: A method to format the display of a field
    def get_formatted_concept_design(self):
        """Returns the concept design with a prefix if available."""
        if self.idconcpd:
            return f"CD: {self.idconcpd}"
        return "Not specified"

# Model for Customer Master (used for autocomplete)
class CustomerMaster(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, verbose_name="Customer Name")
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

    # Business logic methods for CustomerMaster if needed
```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
Create a `ModelForm` for `DesignPlanNew`.

**File: `dailyreporting/forms.py`**
```python
from django import forms
from .models import DesignPlanNew

class DesignPlanNewForm(forms.ModelForm):
    class Meta:
        model = DesignPlanNew
        fields = ['idwono', 'idsr', 'idfxn', 'idconcpd', 'idintrnrw', 'iddaps']
        widgets = {
            'idwono': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idsr': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idfxn': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idconcpd': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'idintrnrw': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'iddaps': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_idwono(self):
        idwono = self.cleaned_data['idwono']
        if not idwono:
            raise forms.ValidationError("Work Order Number cannot be empty.")
        return idwono

    # Add custom validation methods for other fields as needed
```

#### 4.3 Views

**Task:** Implement CRUD operations and data display using CBVs.

**Instructions:**
We'll define views for listing, creating, updating, deleting, and an HTMX partial for the table itself. Also, an autocomplete view for the search box.

**File: `dailyreporting/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.template.loader import render_to_string

from .models import DesignPlanNew, CustomerMaster
from .forms import DesignPlanNewForm

# View for the main list page
class DesignPlanNewListView(ListView):
    model = DesignPlanNew
    template_name = 'dailyreporting/designplannew/list.html'
    context_object_name = 'designplan_entries' # Renamed for clarity

    def get_queryset(self):
        # Initial queryset, possibly filtered by session data (like FinYearId, CompId in ASP.NET)
        # For demonstration, we'll assume these are available or can be hardcoded for now.
        # comp_id = self.request.session.get('compid') # Example of getting from session
        # fin_year_id = self.request.session.get('finyear')
        # queryset = DesignPlanNew.objects.filter(comp_id=comp_id, fin_year_id__lte=fin_year_id)

        # For now, let's just get all, as session setup is outside this scope
        queryset = DesignPlanNew.objects.all().order_by('-id') # Order by Id Desc as in ASP.NET

        search_value = self.request.GET.get('search_wo', '').strip()
        if search_value:
            # Filter by idwono, case-insensitive
            queryset = queryset.filter(idwono__icontains=search_value)
            
        return queryset

# HTMX Partial view for loading the table content dynamically
class DesignPlanNewTablePartialView(DesignPlanNewListView):
    template_name = 'dailyreporting/designplannew/_designplannew_table.html'

# View for adding a new Design Plan entry (via modal)
class DesignPlanNewCreateView(CreateView):
    model = DesignPlanNew
    form_class = DesignPlanNewForm
    template_name = 'dailyreporting/designplannew/_designplannew_form.html'
    success_url = reverse_lazy('designplannew_list') # Not strictly used with HTMX swap="none"

    def form_valid(self, form):
        # Any pre-save logic can go here (e.g., setting FinYearId, CompId, SessionId, SysDate)
        # form.instance.fin_year_id = self.request.session.get('finyear')
        # form.instance.comp_id = self.request.session.get('compid')
        # form.instance.session_id = self.request.session.get('username') # Assuming session id is username
        # form.instance.sys_date = timezone.now() # Requires `from django.utils import timezone`

        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success for HTMX
                headers={
                    'HX-Trigger': 'refreshDesignPlanNewList' # Custom event to trigger table refresh
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If form is invalid, return the form with errors for HTMX to swap in
            return HttpResponse(response.render().content, status=400) # Use 400 for bad request
        return response

# View for editing an existing Design Plan entry (via modal)
class DesignPlanNewUpdateView(UpdateView):
    model = DesignPlanNew
    form_class = DesignPlanNewForm
    template_name = 'dailyreporting/designplannew/_designplannew_form.html'
    success_url = reverse_lazy('designplannew_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanNewList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(response.render().content, status=400)
        return response

# View for deleting a Design Plan entry (via modal)
class DesignPlanNewDeleteView(DeleteView):
    model = DesignPlanNew
    template_name = 'dailyreporting/designplannew/_designplannew_confirm_delete.html'
    success_url = reverse_lazy('designplannew_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Design Plan entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDesignPlanNewList'
                }
            )
        return response

# View for providing autocomplete suggestions for customer names/ids
class CustomerAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '').strip()
        # comp_id = request.session.get('compid') # Example: filter by company ID
        suggestions = []

        if query:
            # Filter by customer name or customer ID
            # Assuming CompId is always available or handled globally.
            # For demonstration, filtering without CompId
            customers = CustomerMaster.objects.filter(
                Q(customer_name__icontains=query) | Q(customer_id__icontains=query)
            ).values('customer_id', 'customer_name')[:10] # Limit to 10 suggestions

            for customer in customers:
                suggestions.append(f"{customer['customer_name']} [{customer['customer_id']}]")
        
        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring HTMX and Alpine.js integration.

**Instructions:**
These templates are partials or extend `core/base.html` for a modular approach.

**File: `dailyreporting/templates/dailyreporting/designplannew/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 space-y-4 sm:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Design Plan Entries - Edit/View</h2>
        <div class="flex space-x-2">
            <!-- Search Input with HTMX -->
            <input type="text" id="searchWoNo" name="search_wo"
                   class="block w-full sm:w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                   placeholder="Search by WoNo..."
                   hx-get="{% url 'designplannew_table' %}"
                   hx-target="#designplannewTable-container"
                   hx-trigger="keyup changed delay:500ms, search"
                   hx-indicator="#search-indicator"
                   hx-swap="innerHTML"
                   _="on keyup send search event to #searchWoNo">
            <span id="search-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>

            <!-- Autocomplete for TxtSearchValue, demonstrating the C# AutocompleteExtender functionality -->
            <!-- Note: This example shows autocomplete for a separate field or context, as the original logic was mixed.
                 If you want autocomplete for WoNo, you'd adjust the CustomerAutocompleteView or create a WoNoAutocompleteView. -->
            <input type="text" id="customerSearch" name="customer_search"
                   class="block w-full sm:w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                   placeholder="Customer Autocomplete..."
                   hx-get="{% url 'customer_autocomplete' %}"
                   hx-trigger="keyup changed delay:300ms, search"
                   hx-target="#customer-suggestions"
                   hx-swap="innerHTML"
                   hx-indicator="#customer-indicator">
            <div id="customer-suggestions" class="absolute bg-white border border-gray-300 rounded-md shadow-lg z-10 w-64 mt-1"></div>
            <span id="customer-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>


            <button
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                hx-get="{% url 'designplannew_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Entry
            </button>
        </div>
    </div>

    <!-- Data Table Container -->
    <div id="designplannewTable-container"
         hx-trigger="load, refreshDesignPlanNewList from:body"
         hx-get="{% url 'designplannew_table' %}"
         hx-swap="innerHTML">
        <!-- Initial loading state -->
        <div class="text-center p-8 bg-white rounded-lg shadow-md">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Design Plan entries...</p>
        </div>
    </div>

    <!-- Global Modal for Add/Edit/Delete Forms -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for modal management
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false },
        }));
    });
    // Global listener for messages
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // If the modal content is swapped due to a successful form submission (204 No Content)
            document.getElementById('modal').classList.remove('is-active'); // Close modal
        }
    });

    // Handle autocomplete suggestions click
    document.body.addEventListener('click', function(event) {
        if (event.target.closest('#customer-suggestions')) {
            const selectedText = event.target.innerText;
            document.getElementById('customerSearch').value = selectedText;
            document.getElementById('customer-suggestions').innerHTML = ''; // Clear suggestions
        }
    });
</script>
{% endblock %}
```

**File: `dailyreporting/templates/dailyreporting/designplannew/_designplannew_table.html`**
```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    <table id="designPlanNewTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WoNo</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sr.No.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FIXTURE NO.</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CONCEPT DESIGN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">INTERNAL REVIEW</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">DAP SEND</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in designplan_entries %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.idwono }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.idsr }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.idfxn }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.idconcpd }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.idintrnrw }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ entry.iddaps }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-center">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out mr-2"
                        hx-get="{% url 'designplannew_edit' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'designplannew_delete' entry.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-3 px-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTable after HTMX has swapped in the new table content
    // Check if DataTables is already initialized, destroy if so, then re-initialize
    if ($.fn.DataTable.isDataTable('#designPlanNewTable')) {
        $('#designPlanNewTable').DataTable().destroy();
    }
    $('#designPlanNewTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "pagingType": "full_numbers", // For full pagination controls
        "responsive": true, // Make table responsive
        "columnDefs": [
            { "orderable": false, "targets": [0, 7] } // Disable sorting for SN and Actions columns
        ]
    });
</script>
```

**File: `dailyreporting/templates/dailyreporting/designplannew/_designplannew_form.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Design Plan Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="outerHTML" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-4 gap-x-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

**File: `dailyreporting/templates/dailyreporting/designplannew/_designplannew_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Design Plan entry: <span class="font-bold">{{ object.idwono }} - {{ object.idfxn }}</span>?</p>
    <p class="text-sm text-red-600 mb-6">This action cannot be undone.</p>

    <form hx-post="{% url 'designplannew_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `dailyreporting/templates/dailyreporting/designplannew/_customer_suggestions.html`**
```html
<!-- This template would be used if customer autocomplete needs a more complex display -->
<!-- For now, it's handled directly in JS, but keeping this for future reference -->
{% for suggestion in suggestions %}
    <div class="p-2 hover:bg-blue-100 cursor-pointer text-gray-800">{{ suggestion }}</div>
{% endfor %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
Set up paths for all CRUD operations, the table partial, and the autocomplete.

**File: `dailyreporting/urls.py`**
```python
from django.urls import path
from .views import (
    DesignPlanNewListView,
    DesignPlanNewTablePartialView,
    DesignPlanNewCreateView,
    DesignPlanNewUpdateView,
    DesignPlanNewDeleteView,
    CustomerAutocompleteView,
)

urlpatterns = [
    path('design-plan/', DesignPlanNewListView.as_view(), name='designplannew_list'),
    path('design-plan/table/', DesignPlanNewTablePartialView.as_view(), name='designplannew_table'),
    path('design-plan/add/', DesignPlanNewCreateView.as_view(), name='designplannew_add'),
    path('design-plan/edit/<int:pk>/', DesignPlanNewUpdateView.as_view(), name='designplannew_edit'),
    path('design-plan/delete/<int:pk>/', DesignPlanNewDeleteView.as_view(), name='designplannew_delete'),
    path('autocomplete/customers/', CustomerAutocompleteView.as_view(), name='customer_autocomplete'),
]

```
**Important:** Remember to include these URLs in your project's main `urls.py` (e.g., `path('daily-reporting/', include('dailyreporting.urls'))`).

#### 4.6 Tests

**Task:** Write tests for the model and views, ensuring comprehensive coverage.

**Instructions:**
Include unit tests for models and integration tests for all views and HTMX interactions.

**File: `dailyreporting/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import HttpResponse
from .models import DesignPlanNew, CustomerMaster
from .forms import DesignPlanNewForm
from django.utils import timezone
import json

class DesignPlanNewModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.entry1 = DesignPlanNew.objects.create(
            id=1,
            idwono='WO001',
            idsr='SR001',
            idfxn='FXN001',
            idconcpd='Concept A',
            idintrnrw='Reviewed',
            iddaps='Sent',
            fin_year_id=2023,
            comp_id=1,
            session_id=101,
            sys_date=timezone.now()
        )
        cls.entry2 = DesignPlanNew.objects.create(
            id=2,
            idwono='WO002',
            idsr='SR002',
            idfxn='FXN002',
            idconcpd='Concept B',
            idintrnrw='Pending',
            iddaps='Not Sent',
            fin_year_id=2023,
            comp_id=1,
            session_id=102,
            sys_date=timezone.now()
        )

    def test_designplannew_creation(self):
        obj = DesignPlanNew.objects.get(id=1)
        self.assertEqual(obj.idwono, 'WO001')
        self.assertEqual(obj.idfxn, 'FXN001')
        self.assertEqual(obj.idconcpd, 'Concept A')
        self.assertIsNotNone(obj.sys_date)

    def test_idwono_label(self):
        obj = DesignPlanNew.objects.get(id=1)
        field_label = obj._meta.get_field('idwono').verbose_name
        self.assertEqual(field_label, 'WoNo')
        
    def test_str_method(self):
        obj = DesignPlanNew.objects.get(id=1)
        self.assertEqual(str(obj), 'WO001 - FXN001')

    def test_get_formatted_concept_design_method(self):
        obj = DesignPlanNew.objects.get(id=1)
        self.assertEqual(obj.get_formatted_concept_design(), 'CD: Concept A')
        obj.idconcpd = None
        self.assertEqual(obj.get_formatted_concept_design(), 'Not specified')


class CustomerMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.cust1 = CustomerMaster.objects.create(customer_id=101, customer_name='Alpha Corp', comp_id=1)
        cls.cust2 = CustomerMaster.objects.create(customer_id=102, customer_name='Beta Industries', comp_id=1)

    def test_customer_creation(self):
        obj = CustomerMaster.objects.get(customer_id=101)
        self.assertEqual(obj.customer_name, 'Alpha Corp')

    def test_str_method(self):
        obj = CustomerMaster.objects.get(customer_id=101)
        self.assertEqual(str(obj), 'Alpha Corp [101]')


class DesignPlanNewViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.entry1 = DesignPlanNew.objects.create(
            id=1,
            idwono='WO_TEST_001',
            idsr='SR_TEST_001',
            idfxn='FXN_TEST_001',
            idconcpd='Concept Test A',
            idintrnrw='Reviewed Test',
            iddaps='Sent Test'
        )
        # Ensure PK sequence is not an issue for new creates by using a high PK value for existing data
        # For auto-incrementing PKs, this is usually handled by the database itself.
        # Since we use `managed=False` and `id=models.IntegerField(db_column='Id', primary_key=True)`,
        # we need to manually ensure unique and appropriate IDs for tests.
        cls.entry2 = DesignPlanNew.objects.create(
            id=2, # Use a different ID
            idwono='WO_TEST_002',
            idsr='SR_TEST_002',
            idfxn='FXN_TEST_002',
            idconcpd='Concept Test B',
            idintrnrw='Reviewed Test',
            iddaps='Sent Test'
        )

        CustomerMaster.objects.create(customer_id=201, customer_name='Test Customer One', comp_id=1)
        CustomerMaster.objects.create(customer_id=202, customer_name='Another Test Customer', comp_id=1)

    def setUp(self):
        self.client = Client()
        # Simulate session data if necessary for views that depend on it
        # self.client.session['compid'] = 1
        # self.client.session['finyear'] = 2023
        
    def test_list_view_get(self):
        response = self.client.get(reverse('designplannew_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplannew/list.html')
        self.assertIn('designplan_entries', response.context)
        self.assertContains(response, 'WO_TEST_001')
        self.assertContains(response, 'Design Plan Entries - Edit/View')

    def test_list_view_search(self):
        response = self.client.get(reverse('designplannew_list') + '?search_wo=WO_TEST_001')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO_TEST_001')
        self.assertNotContains(response, 'WO_TEST_002') # Ensure only filtered results are shown

    def test_table_partial_view_get(self):
        # This view is typically called via HTMX
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('designplannew_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplannew/_designplannew_table.html')
        self.assertContains(response, 'WO_TEST_001')
        self.assertContains(response, 'WO_TEST_002')
        self.assertIsInstance(response, HttpResponse) # Check it's an HTTP response

    def test_table_partial_view_search(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('designplannew_table') + '?search_wo=WO_TEST_001', **headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO_TEST_001')
        self.assertNotContains(response, 'WO_TEST_002')

    def test_create_view_get(self):
        response = self.client.get(reverse('designplannew_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplannew/_designplannew_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Design Plan Entry')

    def test_create_view_post_success(self):
        initial_count = DesignPlanNew.objects.count()
        data = {
            'id': 99, # Manually provide an ID for managed=False PK
            'idwono': 'WO_NEW',
            'idsr': 'SR_NEW',
            'idfxn': 'FXN_NEW',
            'idconcpd': 'New Concept',
            'idintrnrw': 'New Review',
            'iddaps': 'New DAP',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplannew_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(DesignPlanNew.objects.count(), initial_count + 1)
        self.assertTrue(DesignPlanNew.objects.filter(idwono='WO_NEW').exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDesignPlanNewList')

    def test_create_view_post_invalid(self):
        initial_count = DesignPlanNew.objects.count()
        data = {
            'id': 100,
            'idwono': '', # Invalid data
            'idsr': 'SR_NEW',
            'idfxn': 'FXN_NEW',
            'idconcpd': 'New Concept',
            'idintrnrw': 'New Review',
            'iddaps': 'New DAP',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplannew_add'), data, **headers)
        
        self.assertEqual(response.status_code, 400) # Bad Request for invalid form
        self.assertEqual(DesignPlanNew.objects.count(), initial_count) # No new object created
        self.assertContains(response, 'Work Order Number cannot be empty.') # Error message
        self.assertTemplateUsed(response, 'dailyreporting/designplannew/_designplannew_form.html')

    def test_update_view_get(self):
        obj = DesignPlanNew.objects.get(idwono='WO_TEST_001')
        response = self.client.get(reverse('designplannew_edit', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplannew/_designplannew_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Design Plan Entry')
        self.assertContains(response, 'WO_TEST_001')

    def test_update_view_post_success(self):
        obj = DesignPlanNew.objects.get(idwono='WO_TEST_001')
        updated_data = {
            'id': obj.id, # Must include primary key for update
            'idwono': 'WO_UPDATED',
            'idsr': obj.idsr,
            'idfxn': obj.idfxn,
            'idconcpd': obj.idconcpd,
            'idintrnrw': 'Updated Review',
            'iddaps': obj.iddaps,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplannew_edit', args=[obj.id]), updated_data, **headers)
        
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.idwono, 'WO_UPDATED')
        self.assertEqual(obj.idintrnrw, 'Updated Review')
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDesignPlanNewList')

    def test_update_view_post_invalid(self):
        obj = DesignPlanNew.objects.get(idwono='WO_TEST_001')
        updated_data = {
            'id': obj.id,
            'idwono': '', # Invalid
            'idsr': obj.idsr,
            'idfxn': obj.idfxn,
            'idconcpd': obj.idconcpd,
            'idintrnrw': obj.idintrnrw,
            'iddaps': obj.iddaps,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplannew_edit', args=[obj.id]), updated_data, **headers)
        
        self.assertEqual(response.status_code, 400)
        self.assertContains(response, 'Work Order Number cannot be empty.')
        self.assertTemplateUsed(response, 'dailyreporting/designplannew/_designplannew_form.html')

    def test_delete_view_get(self):
        obj = DesignPlanNew.objects.get(idwono='WO_TEST_001')
        response = self.client.get(reverse('designplannew_delete', args=[obj.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplannew/_designplannew_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'WO_TEST_001')

    def test_delete_view_post_success(self):
        obj_to_delete = DesignPlanNew.objects.get(idwono='WO_TEST_002')
        initial_count = DesignPlanNew.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('designplannew_delete', args=[obj_to_delete.id]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(DesignPlanNew.objects.count(), initial_count - 1)
        self.assertFalse(DesignPlanNew.objects.filter(id=obj_to_delete.id).exists())
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDesignPlanNewList')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('customer_autocomplete') + '?q=test')
        self.assertEqual(response.status_code, 200)
        self.assertIsInstance(response, JsonResponse)
        data = json.loads(response.content)
        self.assertIn('Test Customer One [201]', data)
        self.assertIn('Another Test Customer [202]', data)

        response = self.client.get(reverse('customer_autocomplete') + '?q=alpha')
        data = json.loads(response.content)
        self.assertFalse(data) # No match since test customers are not 'alpha'

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already include HTMX and Alpine.js.

-   **HTMX for dynamic table updates:** The `designplannew_table` URL endpoint, combined with `hx-get` and `hx-trigger="load, refreshDesignPlanNewList from:body"` on the table container, ensures the table content is dynamically loaded and refreshed without full page reloads.
-   **HTMX for modal forms:** Add, Edit, and Delete buttons use `hx-get` to fetch the form content into `#modalContent` and `hx-target="#modalContent"`. `hx-swap="outerHTML"` on the form ensures the entire form (including potential validation errors) is swapped back.
-   **HTMX for form submission:** Forms use `hx-post`, `hx-swap="none"` (for 204 No Content responses), and `hx-trigger="refreshDesignPlanNewList"` to notify the table to refresh after successful CRUD operations.
-   **Alpine.js for modal state:** The `_on click add .is-active to #modal` and `_on click remove .is-active from #modal` directives (using the `_` syntax from htmx-alpine.js extension or similar) manage the modal's visibility.
-   **DataTables:** The `_designplannew_table.html` partial includes a JavaScript block to initialize the `designPlanNewTable` as a DataTables instance, providing client-side search, sorting, and pagination. It includes logic to destroy and re-initialize DataTables when the table content is swapped by HTMX, preventing issues.
-   **Autocomplete:** The `customerSearch` input uses `hx-get` to `customer_autocomplete` and `hx-target` to `#customer-suggestions`, dynamically populating a list of suggestions. A simple Alpine.js/JS listener handles selecting a suggestion.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET "Design Plan Edit Details" module to a modern Django application. By focusing on automated processes, leveraging powerful tools like HTMX, Alpine.js, and DataTables, and adhering to strict architectural principles, we ensure a high-quality, maintainable, and scalable solution. Remember to integrate these Django app files into your overall Django project, configure database settings appropriately, and run migrations (`python manage.py migrate --fake-initial` if connecting to an existing DB with `managed=False`).