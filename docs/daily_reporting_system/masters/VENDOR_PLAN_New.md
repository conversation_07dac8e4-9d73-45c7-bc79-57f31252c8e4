## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Analysis of Provided ASP.NET Code:

The provided ASP.NET `.aspx` file and its corresponding C# code-behind are exceptionally minimal.
The `.aspx` file primarily defines content placeholders, indicating it integrates with a master page but provides no specific UI controls or data binding logic.
The C# code-behind file contains only the `Page_Load` event, which is empty.

**Conclusion:**
There is no explicit database schema, backend functionality (CRUD operations, business logic), or UI components visible in the provided ASP.NET code snippet. This typically means the page is a placeholder for future development or its content is dynamically generated in ways not shown here, or it's a very basic entry point.

Given the page name `VENDOR_PLAN_New.aspx` within a `Module_DailyReportingSystem_Masters` namespace, we will infer that this page is intended for managing "Vendor Plans." For a comprehensive Django modernization plan, we will proceed by defining a **hypothetical `VendorPlan` entity** and generating a full CRUD (Create, Read, Update, Delete) module around it, adhering to all the specified Django modernization principles. This demonstrates how a conversion tool would proceed when minimal information is provided, by applying common patterns.

---

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since no database-related elements (like `SqlDataSource`, `GridView` bindings, or explicit SQL commands) are present in the provided ASP.NET snippet, we will infer a common schema for a "Vendor Plan" entity to proceed with the modernization.

**Inferred Details:**
*   **Table Name:** `tbl_vendor_plan`
*   **Model Name:** `VendorPlan`
*   **Fields:**
    *   `plan_id` (Primary Key, typically auto-incremented by the database)
    *   `plan_name` (e.g., "Annual Contract", "Project X Plan")
    *   `description` (Detailed explanation of the plan)
    *   `start_date` (When the plan becomes effective)
    *   `end_date` (When the plan expires)
    *   `vendor_name` (Associated vendor, simplified to text for this example)
    *   `is_active` (Boolean to indicate if the plan is currently active)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

As there is no visible code, we infer the standard CRUD operations expected for a "New" page context, coupled with typical master data management:
*   **Create:** Adding a new `VendorPlan`.
*   **Read:** Listing existing `VendorPlan` records, likely with search/sort/pagination.
*   **Update:** Modifying an existing `VendorPlan`.
*   **Delete:** Removing a `VendorPlan`.
*   **Validation:** Basic field validation (e.g., required fields, date formats) will be applied.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Given the page context, we infer the need for:
*   A **List View** (equivalent to a `GridView`): To display a table of all `VendorPlan` records, with columns for `plan_name`, `start_date`, `end_date`, `is_active`, and an 'Actions' column for Edit/Delete buttons.
*   A **Form View** (equivalent to `TextBox`, `DropDownList`, `Button`s): For creating new plans or editing existing ones, containing fields for `plan_name`, `description`, `start_date`, `end_date`, `vendor_name`, and `is_active`.
*   **Action Buttons:** For "Add New Plan," "Edit," and "Delete."
*   **Modals:** All form interactions (Add, Edit, Delete Confirmation) will occur within HTMX-driven modals to prevent full page reloads.

---

## Step 4: Generate Django Code

We will create a new Django application named `dailysystem` to house this module.

### 4.1 Models (`dailysystem/models.py`)

Task: Create a Django model based on the database schema.

## Instructions:

- Name the model `VendorPlan`.
- Define fields with appropriate Django field types based on inferred column data types.
- Use `db_column` if field names differ from column names.
- Set `managed = False` and `db_table = 'tbl_vendor_plan'` in the `Meta` class.
- Include model methods for business logic (fat model approach), such as checking if a plan is currently active.

```python
from django.db import models
from django.utils import timezone

class VendorPlan(models.Model):
    """
    Represents a vendor's plan or contract within the Daily Reporting System.
    """
    # Assuming 'plan_id' is the primary key managed by the database and Django's 'id' field
    # maps to it by default. If it's a specific column name, use db_column.
    # For this example, we'll let Django's default 'id' handle the PK.

    plan_name = models.CharField(
        max_length=255, 
        db_column='PlanName', 
        verbose_name='Plan Name'
    )
    description = models.TextField(
        db_column='Description', 
        blank=True, 
        null=True, 
        verbose_name='Description'
    )
    start_date = models.DateField(
        db_column='StartDate', 
        verbose_name='Start Date'
    )
    end_date = models.DateField(
        db_column='EndDate', 
        verbose_name='End Date'
    )
    vendor_name = models.CharField(
        max_length=255, 
        db_column='VendorName', 
        verbose_name='Vendor Name'
    )
    is_active = models.BooleanField(
        default=True, 
        db_column='IsActive', 
        verbose_name='Is Active'
    )

    class Meta:
        managed = False  # Django will not manage this table's creation, alteration, or deletion.
        db_table = 'tbl_vendor_plan'  # Map to the existing database table name.
        verbose_name = 'Vendor Plan'
        verbose_name_plural = 'Vendor Plans'
        ordering = ['-start_date', 'plan_name'] # Default ordering for lists

    def __str__(self):
        """Returns the plan name as the string representation."""
        return self.plan_name

    def is_current_plan(self):
        """
        Model business logic: Checks if the plan is currently active based on dates.
        """
        today = timezone.localdate()
        return self.start_date <= today <= self.end_date and self.is_active

    def can_be_edited(self):
        """
        Model business logic: Determines if the plan can still be edited.
        Example: Cannot edit if the end date has passed and it's inactive.
        """
        return not (self.end_date < timezone.localdate() and not self.is_active)

    def deactivate_plan(self):
        """
        Model business logic: Deactivates the plan.
        """
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False
```

### 4.2 Forms (`dailysystem/forms.py`)

Task: Define a Django form for user input.

## Instructions:

- Create a `ModelForm` for `VendorPlan`.
- Include editable fields.
- Add widgets with Tailwind CSS classes for styling.
- Implement form validation logic.

```python
from django import forms
from .models import VendorPlan

class VendorPlanForm(forms.ModelForm):
    """
    Form for creating and updating VendorPlan objects.
    """
    class Meta:
        model = VendorPlan
        fields = ['plan_name', 'description', 'start_date', 'end_date', 'vendor_name', 'is_active']
        widgets = {
            'plan_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'vendor_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        
    def clean(self):
        """
        Custom form validation for start and end dates.
        """
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', "End date cannot be before start date.")
        return cleaned_data
```

### 4.3 Views (`dailysystem/views.py`)

Task: Implement CRUD operations using CBVs.

## Instructions:

- Define `ListView`, `CreateView`, `UpdateView`, `DeleteView`.
- Create a `TablePartialView` to serve the DataTables HTML via HTMX.
- Use `VendorPlan` as the model, `VendorPlanForm` for forms, and set appropriate `template_name` and `success_url`.
- Add success messages using `messages.success`.
- Keep views thin (5-15 lines) and move business logic to models.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import VendorPlan
from .forms import VendorPlanForm

class VendorPlanListView(ListView):
    """
    Displays a list of all Vendor Plans.
    The main view for the Vendor Plan module, loading the initial page.
    """
    model = VendorPlan
    template_name = 'dailysystem/vendorplan/list.html'
    context_object_name = 'vendorplans' # Used by the table partial view

class VendorPlanTablePartialView(ListView):
    """
    Renders only the DataTables table for Vendor Plans.
    Intended to be loaded via HTMX to refresh the table content dynamically.
    """
    model = VendorPlan
    template_name = 'dailysystem/vendorplan/_vendorplan_table.html'
    context_object_name = 'vendorplans'

class VendorPlanCreateView(CreateView):
    """
    Handles the creation of a new Vendor Plan.
    Renders a form and processes its submission.
    """
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailysystem/vendorplan/_vendorplan_form.html' # Partial for modal
    success_url = reverse_lazy('vendorplan_list') # Not strictly needed for HTMX, but good practice

    def form_valid(self, form):
        """
        Called when the form is valid. Saves the new object and triggers HTMX refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to close modal and trigger refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList' # Custom event to refresh the list table
                }
            )
        return response

class VendorPlanUpdateView(UpdateView):
    """
    Handles the updating of an existing Vendor Plan.
    Renders a pre-filled form and processes its submission.
    """
    model = VendorPlan
    form_class = VendorPlanForm
    template_name = 'dailysystem/vendorplan/_vendorplan_form.html' # Partial for modal
    success_url = reverse_lazy('vendorplan_list') # Not strictly needed for HTMX

    def form_valid(self, form):
        """
        Called when the form is valid. Saves the updated object and triggers HTMX refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Vendor Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList'
                }
            )
        return response

class VendorPlanDeleteView(DeleteView):
    """
    Handles the deletion of a Vendor Plan.
    Renders a confirmation page and processes the deletion.
    """
    model = VendorPlan
    template_name = 'dailysystem/vendorplan/_vendorplan_confirm_delete.html' # Partial for modal
    success_url = reverse_lazy('vendorplan_list') # Not strictly needed for HTMX

    def delete(self, request, *args, **kwargs):
        """
        Deletes the object and triggers HTMX refresh.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Vendor Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshVendorPlanList'
                }
            )
        return response
```

### 4.4 Templates (`dailysystem/templates/dailysystem/vendorplan/`)

Task: Create templates for each view.

## Instructions:

- List Template (`list.html`):
    - Extends `core/base.html`.
    - Contains a container for the DataTables table, loaded via HTMX.
    - Includes a modal structure for HTMX-loaded forms.
- Table Partial Template (`_vendorplan_table.html`):
    - Contains the actual `<table>` structure, meant to be injected by HTMX.
    - Initializes DataTables with jQuery.
- Form Partial Template (`_vendorplan_form.html`):
    - Renders the `ModelForm` fields.
    - Includes HTMX attributes for submission and modal closing.
- Delete Confirmation Partial Template (`_vendorplan_confirm_delete.html`):
    - Simple confirmation message with HTMX buttons for deletion.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Vendor Plans</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200"
            hx-get="{% url 'vendorplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Vendor Plan
        </button>
    </div>
    
    <div id="vendorplanTable-container"
         hx-trigger="load, refreshVendorPlanList from:body" {# Listen for custom event to refresh list #}
         hx-get="{% url 'vendorplan_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Vendor Plans...</p>
        </div>
    </div>
    
    <!-- Universal Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad add .block to #modal and remove .hidden from #modal">
            <!-- Content loaded via HTMX will appear here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For simple modal open/close, htmx + _hyperscript is often sufficient.
        // Example: Alpine for a dynamic search input or tabbed interface within the modal.
    });

    // Handle closing modal after HTMX success trigger (e.g., after form submission)
    document.body.addEventListener('htmx:afterRequest', function(event) {
        if (event.detail.xhr.status === 204) { // HTMX success (No Content)
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active'); // Close modal
            }
        }
    });

    // Reinitialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'vendorplanTable-container') {
            // Reinitialize DataTables for the newly loaded table
            if ($.fn.DataTable.isDataTable('#vendorplanTable')) {
                $('#vendorplanTable').DataTable().destroy(); // Destroy previous instance if it exists
            }
            $('#vendorplanTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "responsive": true, // Make table responsive
                "autoWidth": false, // Disable auto width to allow for responsive design
            });
        }
    });
</script>
{% endblock %}
```

#### `_vendorplan_table.html` (Partial for HTMX)

```html
<div class="p-4">
    <table id="vendorplanTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor Name</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in vendorplans %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.plan_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.vendor_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.start_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.end_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">
                    {% if obj.is_active %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                    {% else %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="text-indigo-600 hover:text-indigo-900 mr-3"
                        hx-get="{% url 'vendorplan_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'vendorplan_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization script, ensure it's executed AFTER the table is in the DOM
    // This script will be re-executed by htmx:afterSwap listener in list.html
    // It's placed here to ensure it's loaded with the table, but the actual re-init is in list.html
</script>
```

#### `_vendorplan_form.html` (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Vendor Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            <div class="col-span-full">
                <label for="{{ form.plan_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.plan_name.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.plan_name }}
                {% if form.plan_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.plan_name.errors }}</p>
                {% endif %}
            </div>

            <div class="col-span-full">
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.description.label }}
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.description.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.start_date.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.start_date }}
                {% if form.start_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.start_date.errors }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.end_date.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.end_date }}
                {% if form.end_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.end_date.errors }}</p>
                {% endif %}
            </div>

            <div class="col-span-full">
                <label for="{{ form.vendor_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.vendor_name.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.vendor_name }}
                {% if form.vendor_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.vendor_name.errors }}</p>
                {% endif %}
            </div>
            
            <div class="col-span-full flex items-center mt-2">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 block text-sm text-gray-900">
                    {{ form.is_active.label }}
                </label>
                {% if form.is_active.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.is_active.errors }}</p>
                {% endif %}
            </div>

            {% if form.non_field_errors %}
                <div class="col-span-full text-red-500 text-sm">
                    {% for error in form.non_field_errors %}
                        <p>{{ error }}</p>
                    {% endfor %}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `_vendorplan_confirm_delete.html` (Partial for HTMX)

```html
<div class="p-6 text-center">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Vendor Plan: <strong class="font-bold">"{{ object.plan_name }}"</strong>?</p>
    <form hx-post="{% url 'vendorplan_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-center space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`dailysystem/urls.py`)

Task: Define URL patterns for the views.

## Instructions:

Create paths for list, create, update, delete, and the HTMX partial for the table.
Use appropriate naming patterns and consistent URL structure.

```python
from django.urls import path
from .views import (
    VendorPlanListView, 
    VendorPlanCreateView, 
    VendorPlanUpdateView, 
    VendorPlanDeleteView,
    VendorPlanTablePartialView # New view for HTMX partial
)

urlpatterns = [
    # Main list page
    path('vendorplan/', VendorPlanListView.as_view(), name='vendorplan_list'),
    
    # HTMX partial for the table (loaded dynamically into list.html)
    path('vendorplan/table/', VendorPlanTablePartialView.as_view(), name='vendorplan_table'),

    # CRUD operations, rendered in modals via HTMX
    path('vendorplan/add/', VendorPlanCreateView.as_view(), name='vendorplan_add'),
    path('vendorplan/edit/<int:pk>/', VendorPlanUpdateView.as_view(), name='vendorplan_edit'),
    path('vendorplan/delete/<int:pk>/', VendorPlanDeleteView.as_view(), name='vendorplan_delete'),
]
```

### 4.6 Tests (`dailysystem/tests.py`)

Task: Write tests for the model and views.

## Instructions:

Include comprehensive unit tests for model methods and properties.
Add integration tests for all views (list, create, update, delete), including HTMX interactions.
Ensure at least 80% test coverage of code.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import VendorPlan
from .forms import VendorPlanForm
import datetime

class VendorPlanModelTest(TestCase):
    """
    Unit tests for the VendorPlan model and its methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a test VendorPlan for all tests in this class
        cls.active_plan = VendorPlan.objects.create(
            plan_name='Active Test Plan',
            description='This is an active plan.',
            start_date=timezone.localdate() - datetime.timedelta(days=30),
            end_date=timezone.localdate() + datetime.timedelta(days=30),
            vendor_name='Vendor A',
            is_active=True
        )
        cls.inactive_plan = VendorPlan.objects.create(
            plan_name='Inactive Test Plan',
            description='This plan has expired.',
            start_date=timezone.localdate() - datetime.timedelta(days=60),
            end_date=timezone.localdate() - datetime.timedelta(days=10),
            vendor_name='Vendor B',
            is_active=False
        )

    def test_vendor_plan_creation(self):
        """Test that a VendorPlan object can be created."""
        self.assertEqual(self.active_plan.plan_name, 'Active Test Plan')
        self.assertTrue(self.active_plan.is_active)
        self.assertEqual(VendorPlan.objects.count(), 2)

    def test_plan_name_label(self):
        """Test the verbose name for the plan_name field."""
        field_label = self.active_plan._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')

    def test_description_label(self):
        """Test the verbose name for the description field."""
        field_label = self.active_plan._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Description')

    def test_db_table_name(self):
        """Test that the model maps to the correct database table."""
        self.assertEqual(self.active_plan._meta.db_table, 'tbl_vendor_plan')

    def test_is_current_plan_method(self):
        """Test the is_current_plan business logic."""
        self.assertTrue(self.active_plan.is_current_plan())
        self.assertFalse(self.inactive_plan.is_current_plan()) # Inactive plan is not current

        # Create a future plan that is not current yet
        future_plan = VendorPlan.objects.create(
            plan_name='Future Plan',
            start_date=timezone.localdate() + datetime.timedelta(days=10),
            end_date=timezone.localdate() + datetime.timedelta(days=20),
            vendor_name='Vendor C',
            is_active=True
        )
        self.assertFalse(future_plan.is_current_plan())

    def test_can_be_edited_method(self):
        """Test the can_be_edited business logic."""
        self.assertTrue(self.active_plan.can_be_edited())
        
        # An expired and inactive plan should not be editable
        self.assertFalse(self.inactive_plan.can_be_edited())

        # An active plan that has expired should still be editable to deactivate
        expired_but_active = VendorPlan.objects.create(
            plan_name='Expired Active Plan',
            start_date=timezone.localdate() - datetime.timedelta(days=60),
            end_date=timezone.localdate() - datetime.timedelta(days=1),
            vendor_name='Vendor D',
            is_active=True
        )
        self.assertTrue(expired_but_active.can_be_edited()) # Still active, can be deactivated

    def test_deactivate_plan_method(self):
        """Test the deactivate_plan business logic."""
        self.assertTrue(self.active_plan.is_active)
        deactivated = self.active_plan.deactivate_plan()
        self.assertTrue(deactivated)
        self.assertFalse(self.active_plan.is_active)
        self.active_plan.refresh_from_db() # Refresh instance to get latest state
        self.assertFalse(self.active_plan.is_active)

        # Try to deactivate an already inactive plan
        deactivated_again = self.inactive_plan.deactivate_plan()
        self.assertFalse(deactivated_again)
        self.assertFalse(self.inactive_plan.is_active)


class VendorPlanViewsTest(TestCase):
    """
    Integration tests for VendorPlan views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for views (one active, one inactive)
        cls.plan1 = VendorPlan.objects.create(
            plan_name='First Plan',
            description='Description for first plan.',
            start_date=datetime.date(2023, 1, 1),
            end_date=datetime.date(2023, 12, 31),
            vendor_name='Vendor X',
            is_active=True
        )
        cls.plan2 = VendorPlan.objects.create(
            plan_name='Second Plan',
            description='Description for second plan.',
            start_date=datetime.date(2022, 1, 1),
            end_date=datetime.date(2022, 12, 31),
            vendor_name='Vendor Y',
            is_active=False
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        """Test the VendorPlan list view (main page)."""
        response = self.client.get(reverse('vendorplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/list.html')
        # The actual data is loaded via HTMX, so context_object_name won't be directly on this response
        # We check for the presence of the table container
        self.assertContains(response, '<div id="vendorplanTable-container"')

    def test_table_partial_view(self):
        """Test the HTMX partial for the table."""
        response = self.client.get(reverse('vendorplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_table.html')
        self.assertTrue('vendorplans' in response.context)
        self.assertContains(response, 'First Plan')
        self.assertContains(response, 'Second Plan')
        self.assertContains(response, '<td>Vendor X</td>') # Check if vendor name is present

    def test_create_view_get(self):
        """Test GET request to the create form."""
        response = self.client.get(reverse('vendorplan_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Vendor Plan')

    def test_create_view_post_success(self):
        """Test successful POST request to create a new VendorPlan."""
        initial_count = VendorPlan.objects.count()
        data = {
            'plan_name': 'New Vendor Plan',
            'description': 'Description for new plan.',
            'start_date': '2024-01-01',
            'end_date': '2024-12-31',
            'vendor_name': 'New Vendor',
            'is_active': 'on'
        }
        response = self.client.post(reverse('vendorplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success (No Content)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')
        self.assertEqual(VendorPlan.objects.count(), initial_count + 1)
        self.assertTrue(VendorPlan.objects.filter(plan_name='New Vendor Plan').exists())

    def test_create_view_post_invalid(self):
        """Test invalid POST request to create a new VendorPlan."""
        initial_count = VendorPlan.objects.count()
        data = { # Missing required plan_name
            'description': 'Invalid plan.',
            'start_date': '2024-01-01',
            'end_date': '2023-12-31', # Invalid date range
            'vendor_name': 'Test Vendor',
            'is_active': 'on'
        }
        response = self.client.post(reverse('vendorplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_form.html')
        self.assertContains(response, 'This field is required.') # Check for plan_name error
        self.assertContains(response, 'End date cannot be before start date.') # Check for date validation error
        self.assertEqual(VendorPlan.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """Test GET request to the update form."""
        response = self.client.get(reverse('vendorplan_edit', args=[self.plan1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Vendor Plan')
        self.assertContains(response, self.plan1.plan_name) # Check if current data is pre-filled

    def test_update_view_post_success(self):
        """Test successful POST request to update an existing VendorPlan."""
        updated_name = 'Updated First Plan'
        data = {
            'plan_name': updated_name,
            'description': 'Updated description.',
            'start_date': '2023-01-01',
            'end_date': '2024-12-31', # Changed end date
            'vendor_name': 'Vendor X',
            'is_active': 'on'
        }
        response = self.client.post(reverse('vendorplan_edit', args=[self.plan1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success (No Content)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')
        self.plan1.refresh_from_db()
        self.assertEqual(self.plan1.plan_name, updated_name)

    def test_update_view_post_invalid(self):
        """Test invalid POST request to update an existing VendorPlan."""
        original_name = self.plan1.plan_name
        data = { # Invalid end date
            'plan_name': original_name,
            'description': 'Still the same description.',
            'start_date': '2023-01-01',
            'end_date': '2022-12-31', # Invalid date range
            'vendor_name': 'Vendor X',
            'is_active': 'on'
        }
        response = self.client.post(reverse('vendorplan_edit', args=[self.plan1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_form.html')
        self.assertContains(response, 'End date cannot be before start date.')
        self.plan1.refresh_from_db()
        self.assertEqual(self.plan1.plan_name, original_name) # Name should not have changed

    def test_delete_view_get(self):
        """Test GET request to the delete confirmation."""
        response = self.client.get(reverse('vendorplan_delete', args=[self.plan2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailysystem/vendorplan/_vendorplan_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, f'Are you sure you want to delete the Vendor Plan: "{self.plan2.plan_name}"?')

    def test_delete_view_post_success(self):
        """Test successful POST request to delete a VendorPlan."""
        initial_count = VendorPlan.objects.count()
        response = self.client.post(reverse('vendorplan_delete', args=[self.plan1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success (No Content)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshVendorPlanList')
        self.assertEqual(VendorPlan.objects.count(), initial_count - 1)
        self.assertFalse(VendorPlan.objects.filter(pk=self.plan1.pk).exists())
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

- **HTMX for Dynamic Interactions:**
    - The main `list.html` page uses `hx-get` to load the `_vendorplan_table.html` partial on `load` and on a custom `refreshVendorPlanList` event. This ensures the table is dynamic and refreshes after CRUD operations.
    - `Add`, `Edit`, and `Delete` buttons use `hx-get` to fetch their respective form/confirmation partials into a shared modal (`#modalContent`).
    - Form submissions (`hx-post`) return `204 No Content` and an `HX-Trigger` header (`refreshVendorPlanList`) to signal the main list view to reload its table without a full page refresh.
    - `_hyperscript` is used for simple UI manipulations like adding/removing the `is-active` class to show/hide the modal.

- **Alpine.js for UI State Management:**
    - While the current simple modal functionality is handled effectively by HTMX and `_hyperscript`, Alpine.js is included (`{% block extra_js %}`) for potential future needs, such as managing complex form states, filters, or client-side validation that goes beyond basic HTMX. A placeholder `document.addEventListener('alpine:init', () => {});` is in `list.html`.

- **DataTables for List Views:**
    - The `_vendorplan_table.html` partial contains the `<table>` element with a unique ID (`#vendorplanTable`).
    - The `list.html` includes JavaScript that listens for `htmx:afterSwap` events on the table container. When the table partial is swapped in, it re-initializes `DataTables` on `#vendorplanTable`, ensuring client-side searching, sorting, and pagination work correctly even after dynamic updates. Old DataTable instances are destroyed before new ones are created to prevent conflicts.

- **No Additional JavaScript:**
    - The solution relies exclusively on HTMX, Alpine.js (for future complex UI), and the DataTables library's jQuery dependency. No custom vanilla JavaScript is required beyond the DataTables re-initialization logic.

## Final Notes

This comprehensive Django modernization plan provides a full CRUD module for a hypothetical "Vendor Plan" entity, directly addressing the requirements for converting legacy ASP.NET applications to modern Django solutions. By focusing on AI-assisted automation, the process would involve:

1.  **Automated Schema Inference:** Tools would analyze database connections (if available) or existing ASP.NET code to infer table names and column structures, as we did hypothetically.
2.  **Automated Functionality Identification:** CRUD patterns and business logic would be detected from C# code-behind methods, `SqlDataSource` configurations, and control events.
3.  **Automated UI Component Mapping:** ASP.NET controls like `GridView`, `TextBox`, `Button` would be mapped to Django's `ModelForm` fields and HTML structures (e.g., DataTables).
4.  **Code Generation:** Using templates similar to those provided, the Python models, forms, views, URLs, and HTML templates would be automatically generated.
5.  **Test Generation:** Based on identified functionalities, boilerplate unit and integration tests would be generated to ensure code quality.

This approach significantly reduces manual effort and accelerates the modernization process, delivering a robust, maintainable, and modern Django application ready for further development.