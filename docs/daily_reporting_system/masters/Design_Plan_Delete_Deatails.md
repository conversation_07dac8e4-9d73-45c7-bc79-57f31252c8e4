The provided ASP.NET code for `Design_Plan_Delete_Deatails.aspx` and its C# code-behind file are largely empty, containing only boilerplate content and a standard `Page_Load` event without any custom logic. This indicates that the core functionality for this specific page was either never implemented, is handled entirely client-side (unlikely for server-side ASP.NET), or relies heavily on master page content or external controls not provided.

However, the file name `Design_Plan_Delete_Deatails.aspx` strongly suggests a module related to managing "Design Plans," specifically implying a screen for viewing details related to deleting such plans, or perhaps a confirmation page before deletion. Given this context and the request for a comprehensive Django modernization plan that includes full CRUD (Create, Read, Update, Delete) operations, we will infer the common functionality expected from a "master data management" screen for "Design Plans."

Our modernization approach will transform this potential placeholder into a fully functional, modern Django application, adhering to the "fat model, thin view" principle, leveraging HTMX and Alpine.js for dynamic interfaces, and using DataTables for efficient data presentation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Since the provided ASP.NET code is empty, we must infer the database schema based on the file name `Design_Plan_Delete_Deatails`. This suggests a master table for `Design Plans`.

**Inferred Database Schema:**
*   **Table Name:** `tbl_design_plan`
*   **Columns:**
    *   `design_plan_id` (Primary Key, Integer)
    *   `design_plan_name` (Text, e.g., VARCHAR(255))
    *   `description` (Text, e.g., TEXT)
    *   `is_active` (Boolean, e.g., BIT or TINYINT)
    *   `created_date` (DateTime)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Given the typical use of `Masters` modules in ASP.NET applications for managing core data, we infer that the module `Design_Plan_Delete_Deatails` would ideally support full CRUD operations, even if the provided ASP.NET code only explicitly hints at 'delete details'.

*   **Create:** Ability to add new `DesignPlan` records.
*   **Read:** Display a list of all `DesignPlan` records and view details of a specific record.
*   **Update:** Modify existing `DesignPlan` records.
*   **Delete:** Remove `DesignPlan` records.
*   **Validation Logic:** Basic field validations (e.g., required fields) would be expected.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Based on the inferred functionality, we envision the following UI components:

*   **List View:** Equivalent to an ASP.NET GridView, displaying a table of `DesignPlan` records with columns for `design_plan_name`, `description`, `is_active`, and actions (Edit, Delete). This will be implemented using DataTables.
*   **Form for Create/Update:** Equivalent to ASP.NET TextBoxes for `design_plan_name`, `description`, and a CheckBox for `is_active`. Action Buttons (Save, Cancel). These forms will be loaded dynamically via HTMX into a modal.
*   **Delete Confirmation:** A simple confirmation dialog before deleting a record, also loaded via HTMX into a modal.

### Step 4: Generate Django Code

We will structure the Django application within a module named `dailyreporting` (inferred from `Module_DailyReportingSystem`).

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The model `DesignPlan` will represent the `tbl_design_plan` table. We use `managed = False` and `db_table` to connect to the existing database table.

**File: `dailyreporting/models.py`**
```python
from django.db import models

class DesignPlan(models.Model):
    design_plan_id = models.IntegerField(db_column='design_plan_id', primary_key=True)
    design_plan_name = models.CharField(db_column='design_plan_name', max_length=255)
    description = models.TextField(db_column='description', blank=True, null=True)
    is_active = models.BooleanField(db_column='is_active', default=True)
    created_date = models.DateTimeField(db_column='created_date', auto_now_add=True)

    class Meta:
        managed = False
        db_table = 'tbl_design_plan'
        verbose_name = 'Design Plan'
        verbose_name_plural = 'Design Plans'

    def __str__(self):
        return self.design_plan_name
        
    def get_status_display(self):
        """
        Business logic: Returns a user-friendly string for the active status.
        """
        return "Active" if self.is_active else "Inactive"
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for `DesignPlan` to handle input for `design_plan_name`, `description`, and `is_active`. Widgets will be styled with Tailwind CSS classes.

**File: `dailyreporting/forms.py`**
```python
from django import forms
from .models import DesignPlan

class DesignPlanForm(forms.ModelForm):
    class Meta:
        model = DesignPlan
        fields = ['design_plan_name', 'description', 'is_active']
        widgets = {
            'design_plan_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        labels = {
            'design_plan_name': 'Design Plan Name',
            'description': 'Description',
            'is_active': 'Is Active',
        }
        
    def clean_design_plan_name(self):
        """
        Custom validation for design_plan_name to ensure it's not empty and unique.
        """
        name = self.cleaned_data['design_plan_name']
        if not name:
            raise forms.ValidationError("Design Plan Name cannot be empty.")
        
        # Check for uniqueness, excluding the current instance if it's an update
        qs = DesignPlan.objects.filter(design_plan_name__iexact=name)
        if self.instance.pk: # If updating an existing object
            qs = qs.exclude(pk=self.instance.pk)
        if qs.exists():
            raise forms.ValidationError("A design plan with this name already exists.")
            
        return name
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
We'll create `ListView`, `CreateView`, `UpdateView`, and `DeleteView`. A `TemplateView` will also be added to serve the HTMX-loaded table partial. Views remain thin, delegating logic to the model or form.

**File: `dailyreporting/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DesignPlan
from .forms import DesignPlanForm

class DesignPlanListView(ListView):
    """
    Displays the main page with the list of Design Plans.
    The actual table content is loaded via HTMX.
    """
    model = DesignPlan
    template_name = 'dailyreporting/designplan/list.html'
    context_object_name = 'designplans' # Although not directly used by list.html, good practice

class DesignPlanTablePartialView(ListView):
    """
    Renders the DataTables partial for Design Plans.
    Used by HTMX to refresh the table content.
    """
    model = DesignPlan
    template_name = 'dailyreporting/designplan/_designplan_table.html'
    context_object_name = 'designplans'

class DesignPlanCreateView(CreateView):
    """
    Handles creation of new Design Plan records.
    Supports HTMX requests for dynamic form loading and list refresh.
    """
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'dailyreporting/designplan/_designplan_form.html' # This is a partial
    success_url = reverse_lazy('designplan_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success without navigating
                headers={
                    'HX-Trigger': 'refreshDesignPlanList' # HTMX trigger to refresh the list
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors
            return response
        return response


class DesignPlanUpdateView(UpdateView):
    """
    Handles updating existing Design Plan records.
    Supports HTMX requests for dynamic form loading and list refresh.
    """
    model = DesignPlan
    form_class = DesignPlanForm
    template_name = 'dailyreporting/designplan/_designplan_form.html' # This is a partial
    success_url = reverse_lazy('designplan_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Design Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response
    
    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class DesignPlanDeleteView(DeleteView):
    """
    Handles deletion of Design Plan records.
    Supports HTMX requests for dynamic confirmation and list refresh.
    """
    model = DesignPlan
    template_name = 'dailyreporting/designplan/_designplan_confirm_delete.html' # This is a partial
    success_url = reverse_lazy('designplan_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        # Additional logic can be added here before deletion, e.g., checking dependencies
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Design Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshDesignPlanList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring DRY principles and HTMX integration.

**Instructions:**
Templates will extend `core/base.html`, use DataTables for lists, and employ HTMX for dynamic interactions (modal loading, form submission, table refreshing).

**File: `dailyreporting/templates/dailyreporting/designplan/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Design Plans</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'designplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Design Plan
        </button>
    </div>
    
    <div id="designplanTable-container"
         hx-trigger="load, refreshDesignPlanList from:body"
         hx-get="{% url 'designplan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Design Plans...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}
```

**File: `dailyreporting/templates/dailyreporting/designplan/_designplan_table.html`**
```html
<table id="designplanTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Design Plan Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in designplans %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.design_plan_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.description|default:"-" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_status_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.created_date|date:"Y-m-d H:i" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'designplan_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'designplan_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No design plans found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    // Only initialize DataTables if it's not already initialized
    if (!$.fn.DataTable.isDataTable('#designplanTable')) {
        $('#designplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers",
            "columnDefs": [
                { "orderable": false, "targets": [0, 5] } // Disable sorting for SN and Actions columns
            ]
        });
    }
});
</script>
```

**File: `dailyreporting/templates/dailyreporting/designplan/_designplan_form.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Design Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <span id="form-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

**File: `dailyreporting/templates/dailyreporting/designplan/_designplan_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Design Plan "{{ object.design_plan_name }}"?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'designplan_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <span id="delete-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URL patterns are created for the main list view, and dedicated endpoints for adding, editing, deleting, and fetching the table partial, supporting HTMX interactions.

**File: `dailyreporting/urls.py`**
```python
from django.urls import path
from .views import (
    DesignPlanListView,
    DesignPlanCreateView,
    DesignPlanUpdateView,
    DesignPlanDeleteView,
    DesignPlanTablePartialView
)

urlpatterns = [
    path('designplan/', DesignPlanListView.as_view(), name='designplan_list'),
    path('designplan/add/', DesignPlanCreateView.as_view(), name='designplan_add'),
    path('designplan/edit/<int:pk>/', DesignPlanUpdateView.as_view(), name='designplan_edit'),
    path('designplan/delete/<int:pk>/', DesignPlanDeleteView.as_view(), name='designplan_delete'),
    path('designplan/table/', DesignPlanTablePartialView.as_view(), name='designplan_table'), # HTMX endpoint for table
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests cover model methods and field properties. Integration tests verify the functionality of all views, including form submission, object creation/update/deletion, and HTMX responses.

**File: `dailyreporting/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DesignPlan
from .forms import DesignPlanForm

class DesignPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        DesignPlan.objects.create(
            design_plan_id=1,
            design_plan_name='Test Design Plan 1',
            description='Description for Test Plan 1',
            is_active=True,
            # created_date is auto_now_add
        )
        DesignPlan.objects.create(
            design_plan_id=2,
            design_plan_name='Test Design Plan 2',
            description='Another description',
            is_active=False,
        )
  
    def test_design_plan_creation(self):
        obj = DesignPlan.objects.get(design_plan_id=1)
        self.assertEqual(obj.design_plan_name, 'Test Design Plan 1')
        self.assertEqual(obj.description, 'Description for Test Plan 1')
        self.assertTrue(obj.is_active)
        self.assertIsNotNone(obj.created_date)
        
    def test_design_plan_name_label(self):
        obj = DesignPlan.objects.get(design_plan_id=1)
        field_label = obj._meta.get_field('design_plan_name').verbose_name
        self.assertEqual(field_label, 'design plan name') # Django's default verbose_name
        
    def test_get_status_display_method(self):
        obj_active = DesignPlan.objects.get(design_plan_id=1)
        self.assertEqual(obj_active.get_status_display(), 'Active')
        
        obj_inactive = DesignPlan.objects.get(design_plan_id=2)
        self.assertEqual(obj_inactive.get_status_display(), 'Inactive')

    def test_str_method(self):
        obj = DesignPlan.objects.get(design_plan_id=1)
        self.assertEqual(str(obj), 'Test Design Plan 1')

    def test_design_plan_form_validation_unique_name(self):
        form = DesignPlanForm(data={'design_plan_name': 'Test Design Plan 1', 'description': 'Some description', 'is_active': True})
        self.assertFalse(form.is_valid())
        self.assertIn('design_plan_name', form.errors)
        self.assertIn("A design plan with this name already exists.", form.errors['design_plan_name'])

    def test_design_plan_form_validation_empty_name(self):
        form = DesignPlanForm(data={'design_plan_name': '', 'description': 'Some description', 'is_active': True})
        self.assertFalse(form.is_valid())
        self.assertIn('design_plan_name', form.errors)
        self.assertIn("Design Plan Name cannot be empty.", form.errors['design_plan_name'])


class DesignPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        DesignPlan.objects.create(
            design_plan_id=101, # Use a different PK to avoid conflict with model tests
            design_plan_name='Existing Design Plan',
            description='This is an existing plan.',
            is_active=True,
        )
    
    def setUp(self):
        # Set up client for each test method
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('designplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/list.html')
        # We don't check context_object_name directly for list view, as content is loaded via HTMX

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('designplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_table.html')
        self.assertIn('designplans', response.context)
        self.assertEqual(len(response.context['designplans']), 1)
        self.assertContains(response, 'Existing Design Plan')
        
    def test_create_view_get(self):
        response = self.client.get(reverse('designplan_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'design_plan_id': 102, # Need to provide a PK if it's not auto-incrementing
            'design_plan_name': 'New Design Plan',
            'description': 'This is a new plan.',
            'is_active': True,
        }
        response = self.client.post(reverse('designplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        self.assertTrue(DesignPlan.objects.filter(design_plan_name='New Design Plan').exists())
        self.assertGreater(DesignPlan.objects.count(), 1) # Ensure a new one was added

    def test_create_view_post_invalid(self):
        data = {
            'design_plan_name': '', # Invalid name
            'description': 'Invalid attempt',
            'is_active': False,
        }
        response = self.client.post(reverse('designplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form with errors returned via HTMX
        self.assertContains(response, "Design Plan Name cannot be empty.")
        self.assertFalse(DesignPlan.objects.filter(description='Invalid attempt').exists())

    def test_update_view_get(self):
        obj = DesignPlan.objects.get(design_plan_id=101)
        response = self.client.get(reverse('designplan_edit', args=[obj.design_plan_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.design_plan_id, obj.design_plan_id)
        
    def test_update_view_post_success(self):
        obj = DesignPlan.objects.get(design_plan_id=101)
        data = {
            'design_plan_id': obj.design_plan_id,
            'design_plan_name': 'Updated Design Plan',
            'description': 'Description has been updated.',
            'is_active': False,
        }
        response = self.client.post(reverse('designplan_edit', args=[obj.design_plan_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        
        obj.refresh_from_db()
        self.assertEqual(obj.design_plan_name, 'Updated Design Plan')
        self.assertFalse(obj.is_active)

    def test_update_view_post_invalid(self):
        DesignPlan.objects.create(design_plan_id=103, design_plan_name='Another Plan', description='Temp', is_active=True)
        obj_to_update = DesignPlan.objects.get(design_plan_id=101)
        
        data = {
            'design_plan_id': obj_to_update.design_plan_id,
            'design_plan_name': 'Another Plan', # Duplicate name
            'description': 'Attempting update',
            'is_active': True,
        }
        response = self.client.post(reverse('designplan_edit', args=[obj_to_update.design_plan_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "A design plan with this name already exists.")
        obj_to_update.refresh_from_db()
        self.assertNotEqual(obj_to_update.design_plan_name, 'Another Plan') # Should not have updated

    def test_delete_view_get(self):
        obj = DesignPlan.objects.get(design_plan_id=101)
        response = self.client.get(reverse('designplan_delete', args=[obj.design_plan_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreporting/designplan/_designplan_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].design_plan_id, obj.design_plan_id)
        
    def test_delete_view_post_success(self):
        obj_to_delete = DesignPlan.objects.get(design_plan_id=101)
        response = self.client.post(reverse('designplan_delete', args=[obj_to_delete.design_plan_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDesignPlanList')
        self.assertFalse(DesignPlan.objects.filter(design_plan_id=obj_to_delete.design_plan_id).exists())
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX:** Used for all dynamic interactions:
    *   Loading the DataTables partial (`designplan_table`) on page load and after any CRUD operation using `hx-get` and `hx-trigger="load, refreshDesignPlanList from:body"`.
    *   Loading create/edit/delete forms into a modal using `hx-get` on button clicks.
    *   Submitting forms and delete confirmations using `hx-post`, with `hx-swap="none"` and `HX-Trigger` headers for list refresh.
    *   Visual feedback during requests with `hx-indicator`.
-   **Alpine.js:** Used for simple UI state management, specifically for showing/hiding the modal using `_=` attributes:
    *   `_="on click add .is-active to #modal"` to open the modal.
    *   `_="on click remove .is-active from me"` to close the modal when clicking outside its content.
-   **DataTables:** Initialized on the `_designplan_table.html` partial after it's loaded by HTMX, providing client-side searching, sorting, and pagination for the list of `Design Plans`.
-   All interactions are designed to work without full page reloads, providing a smooth user experience.

### Final Notes

This comprehensive Django modernization plan provides a robust and modern replacement for the ASP.NET `Design_Plan_Delete_Deatails` module. By leveraging Django's powerful ORM, Class-Based Views, and modern frontend techniques like HTMX and Alpine.js, we achieve a highly interactive and maintainable application. The emphasis on "fat models, thin views" ensures business logic is encapsulated efficiently, while comprehensive testing guarantees reliability and facilitates future development. This structured approach is ideal for AI-assisted automation, as placeholders are clearly defined and filled, and the overall architecture promotes systematic conversion and consistency across modules.