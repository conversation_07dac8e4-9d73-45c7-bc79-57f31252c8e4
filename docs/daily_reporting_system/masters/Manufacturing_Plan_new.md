The provided ASP.NET code is a minimal placeholder, indicating a master page structure but lacking any specific UI controls, database interactions, or backend logic. This means there are no concrete elements to "convert" directly.

Therefore, for this modernization plan, we will proceed by making **reasonable assumptions** about a typical "Manufacturing Plan" module. We will infer a database table, common fields, and standard CRUD (Create, Read, Update, Delete) operations that would be present in such a system. The focus will be on demonstrating the **automated generation of Django components** based on these inferred details, adhering strictly to the specified architecture (Fat Model, Thin View, HTMX, Alpine.js, DataTables).

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code does not contain explicit database interaction details (e.g., `SqlDataSource` or SQL commands). Based on the `Manufacturing_Plan_new` module name, we infer the following:

*   **Database Table Name:** `tbl_manufacturing_plan`
*   **Inferred Columns:**
    *   `id` (Primary Key, Auto-incrementing)
    *   `plan_name` (VARCHAR/NVARCHAR, e.g., 255 characters, required)
    *   `start_date` (DATE, required)
    *   `end_date` (DATE, required)
    *   `target_quantity` (INTEGER, optional)
    *   `status` (VARCHAR/NVARCHAR, e.g., 50 characters, e.g., 'Planned', 'In Progress', 'Completed', required, with default)
    *   `created_at` (DATETIME, auto-set on creation)
    *   `updated_at` (DATETIME, auto-set on update)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided C# code-behind only contains an empty `Page_Load` method, indicating no explicit backend functionality defined in the snippet. However, for a "Manufacturing Plan" module, standard CRUD operations are implicitly required for managing plans.

*   **Create:** Functionality to add new manufacturing plans.
*   **Read:** Functionality to list all existing manufacturing plans and view details of a specific plan.
*   **Update:** Functionality to modify an existing manufacturing plan.
*   **Delete:** Functionality to remove a manufacturing plan.
*   **Validation:** Basic validation for required fields (`plan_name`, `start_date`, `end_date`, `status`) will be implemented.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The `.aspx` file only contains content placeholders and no specific UI controls. Based on common "Manufacturing Plan" management interfaces, we infer the following Django equivalents:

*   **List View:** A table (Django template rendered, enhanced with DataTables) to display `plan_name`, `start_date`, `end_date`, `target_quantity`, and `status`, along with "Edit" and "Delete" action buttons for each row.
*   **Form for Create/Update:** Input fields for `plan_name` (TextBox), `start_date` (Date picker), `end_date` (Date picker), `target_quantity` (Number input), and `status` (Dropdown/Select list). "Save" and "Cancel" buttons will trigger form submission or modal dismissal.
*   **Delete Confirmation:** A modal dialog with a confirmation message and "Confirm Delete" and "Cancel" buttons.
*   **Dynamic Interactions:** All form submissions and table refreshes will utilize HTMX to avoid full page reloads. Modals for forms and delete confirmations will be managed by Alpine.js.

---

### Step 4: Generate Django Code

We will create a new Django app named `dailyreport` to house this module, aligning with the `Module_DailyReportingSystem` namespace in the ASP.NET code.

#### 4.1 Models

**File:** `dailyreport/models.py`

```python
from django.db import models
from django.utils import timezone

class ManufacturingPlan(models.Model):
    """
    Represents a manufacturing plan for production scheduling.
    """
    id = models.AutoField(db_column='id', primary_key=True)
    plan_name = models.CharField(db_column='plan_name', max_length=255, verbose_name='Plan Name')
    start_date = models.DateField(db_column='start_date', verbose_name='Start Date')
    end_date = models.DateField(db_column='end_date', verbose_name='End Date')
    target_quantity = models.IntegerField(db_column='target_quantity', null=True, blank=True, verbose_name='Target Quantity')
    status = models.CharField(db_column='status', max_length=50, default='Planned', verbose_name='Status')
    created_at = models.DateTimeField(db_column='created_at', auto_now_add=True, verbose_name='Created At')
    updated_at = models.DateTimeField(db_column='updated_at', auto_now=True, verbose_name='Updated At')

    class Meta:
        managed = False  # Set to True if Django should manage table creation/alteration
        db_table = 'tbl_manufacturing_plan'
        verbose_name = 'Manufacturing Plan'
        verbose_name_plural = 'Manufacturing Plans'
        ordering = ['start_date', 'plan_name']

    def __str__(self):
        return self.plan_name

    def is_active(self):
        """
        Checks if the plan is currently active based on dates.
        """
        return self.start_date <= timezone.localdate() <= self.end_date

    def get_status_display(self):
        """
        Returns a more user-friendly display for the status.
        """
        status_map = {
            'Planned': 'Planned',
            'In Progress': 'In Progress',
            'Completed': 'Completed',
            'Cancelled': 'Cancelled',
        }
        return status_map.get(self.status, self.status)

    def save(self, *args, **kwargs):
        """
        Custom save method to include any pre-save business logic.
        """
        # Example: Ensure end_date is not before start_date (basic validation)
        if self.start_date and self.end_date and self.end_date < self.start_date:
            from django.core.exceptions import ValidationError
            raise ValidationError("End date cannot be before start date.")
        super().save(*args, **kwargs)

```

#### 4.2 Forms

**File:** `dailyreport/forms.py`

```python
from django import forms
from .models import ManufacturingPlan

class ManufacturingPlanForm(forms.ModelForm):
    """
    Django form for creating and updating ManufacturingPlan objects.
    """
    class Meta:
        model = ManufacturingPlan
        fields = ['plan_name', 'start_date', 'end_date', 'target_quantity', 'status']
        widgets = {
            'plan_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'target_quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(choices=[
                ('Planned', 'Planned'),
                ('In Progress', 'In Progress'),
                ('Completed', 'Completed'),
                ('Cancelled', 'Cancelled'),
            ], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    def clean(self):
        """
        Custom validation for form fields.
        """
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', "End date cannot be before start date.")

        return cleaned_data
```

#### 4.3 Views

**File:** `dailyreport/views.py`

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import ManufacturingPlan
from .forms import ManufacturingPlanForm

class ManufacturingPlanListView(ListView):
    """
    Displays a list of all manufacturing plans.
    """
    model = ManufacturingPlan
    template_name = 'dailyreport/manufacturingplan/list.html'
    context_object_name = 'manufacturingplans' # Name for the list of objects in template

class ManufacturingPlanTablePartialView(ListView):
    """
    Renders only the DataTables-enhanced table content for HTMX requests.
    """
    model = ManufacturingPlan
    template_name = 'dailyreport/manufacturingplan/_manufacturingplan_table.html'
    context_object_name = 'manufacturingplans' # Name for the list of objects in template

class ManufacturingPlanCreateView(CreateView):
    """
    Handles creation of new manufacturing plans via a form.
    """
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'dailyreport/manufacturingplan/_manufacturingplan_form.html' # Use partial for modal
    success_url = reverse_lazy('manufacturingplan_list') # Fallback, HTMX handles refresh

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to swap
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList' # Custom HTMX trigger
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # Return the form with errors to HX-Target
        return response

class ManufacturingPlanUpdateView(UpdateView):
    """
    Handles updating existing manufacturing plans via a form.
    """
    model = ManufacturingPlan
    form_class = ManufacturingPlanForm
    template_name = 'dailyreport/manufacturingplan/_manufacturingplan_form.html' # Use partial for modal
    success_url = reverse_lazy('manufacturingplan_list') # Fallback, HTMX handles refresh

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Manufacturing Plan updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to swap
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList' # Custom HTMX trigger
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # Return the form with errors to HX-Target
        return response

class ManufacturingPlanDeleteView(DeleteView):
    """
    Handles deletion of a manufacturing plan.
    """
    model = ManufacturingPlan
    template_name = 'dailyreport/manufacturingplan/_manufacturingplan_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('manufacturingplan_list') # Fallback, HTMX handles refresh

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Manufacturing Plan deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to swap
                headers={
                    'HX-Trigger': 'refreshManufacturingPlanList' # Custom HTMX trigger
                }
            )
        return response
```

#### 4.4 Templates

**Directory:** `dailyreport/templates/dailyreport/manufacturingplan/`

**File:** `dailyreport/templates/dailyreport/manufacturingplan/list.html`
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Manufacturing Plans</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'manufacturingplan_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Manufacturing Plan
        </button>
    </div>

    <div id="manufacturingplanTable-container"
         hx-trigger="load, refreshManufacturingPlanList from:body"
         hx-get="{% url 'manufacturingplan_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading manufacturing plans...</p>
        </div>
    </div>

    <!-- Modal for forms (Create/Update/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal"
         x-init="() => { $watch('showModal', value => { if (!value) { document.getElementById('modalContent').innerHTML = ''; } }); }"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
         x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
         x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-auto"
             _="on hx-after-swap add .is-active to #modal">
            <!-- Content loaded by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            show: false,
            open() { this.show = true },
            close() { this.show = false },
        }));
    });
    // Add event listener for HTMX triggers to control modal visibility
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').style.display = 'flex'; // Ensure it's visible
        }
    });
    document.body.addEventListener('htmx:afterRequest', function(event) {
        // Close modal if a 204 response (success without content swap) is received from form submission
        if (event.detail.xhr.status === 204 && event.detail.xhr.getResponseHeader('HX-Trigger') === 'refreshManufacturingPlanList') {
            document.getElementById('modal').classList.remove('is-active');
            document.getElementById('modal').style.display = 'none'; // Hide it
            document.getElementById('modalContent').innerHTML = ''; // Clear modal content
        }
    });
</script>
{% endblock %}
```

**File:** `dailyreport/templates/dailyreport/manufacturingplan/_manufacturingplan_table.html`
```html
<table id="manufacturingplanTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Target Quantity</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in manufacturingplans %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.plan_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.start_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.end_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.target_quantity|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.get_status_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'manufacturingplan_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'manufacturingplan_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 border-b border-gray-200 text-center text-gray-500">No manufacturing plans found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only after the table HTML is loaded
    $(document).ready(function() {
        $('#manufacturingplanTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true,
            "info": true
        });
    });
</script>
```

**File:** `dailyreport/templates/dailyreport/manufacturingplan/_manufacturingplan_form.html`
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Manufacturing Plan</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent">
        {% csrf_token %}

        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal hide #modal"
                >
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File:** `dailyreport/templates/dailyreport/manufacturingplan/_manufacturingplan_confirm_delete.html`
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the manufacturing plan "{{ object.plan_name }}"?</p>
    <form hx-post="{% url 'manufacturingplan_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal hide #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**File:** `dailyreport/urls.py` (needs to be included in your project's main `urls.py`)

```python
from django.urls import path
from .views import (
    ManufacturingPlanListView,
    ManufacturingPlanTablePartialView,
    ManufacturingPlanCreateView,
    ManufacturingPlanUpdateView,
    ManufacturingPlanDeleteView
)

urlpatterns = [
    path('manufacturing-plans/', ManufacturingPlanListView.as_view(), name='manufacturingplan_list'),
    path('manufacturing-plans/table/', ManufacturingPlanTablePartialView.as_view(), name='manufacturingplan_table'), # For HTMX refresh
    path('manufacturing-plans/add/', ManufacturingPlanCreateView.as_view(), name='manufacturingplan_add'),
    path('manufacturing-plans/edit/<int:pk>/', ManufacturingPlanUpdateView.as_view(), name='manufacturingplan_edit'),
    path('manufacturing-plans/delete/<int:pk>/', ManufacturingPlanDeleteView.as_view(), name='manufacturingplan_delete'),
]
```

#### 4.6 Tests

**File:** `dailyreport/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import date, timedelta
from .models import ManufacturingPlan
from .forms import ManufacturingPlanForm

class ManufacturingPlanModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.plan1 = ManufacturingPlan.objects.create(
            plan_name='Q1 Production Plan',
            start_date=date(2023, 1, 1),
            end_date=date(2023, 3, 31),
            target_quantity=1000,
            status='Planned'
        )
        cls.plan2 = ManufacturingPlan.objects.create(
            plan_name='Q2 Production Plan',
            start_date=date(2023, 4, 1),
            end_date=date(2023, 6, 30),
            target_quantity=1500,
            status='In Progress'
        )

    def test_manufacturing_plan_creation(self):
        """Test that a ManufacturingPlan object can be created and its fields are correct."""
        obj = ManufacturingPlan.objects.get(id=self.plan1.id)
        self.assertEqual(obj.plan_name, 'Q1 Production Plan')
        self.assertEqual(obj.start_date, date(2023, 1, 1))
        self.assertEqual(obj.end_date, date(2023, 3, 31))
        self.assertEqual(obj.target_quantity, 1000)
        self.assertEqual(obj.status, 'Planned')
        self.assertIsNotNone(obj.created_at)
        self.assertIsNotNone(obj.updated_at)
        self.assertTrue(obj.created_at <= obj.updated_at) # Check timestamps consistency

    def test_plan_name_label(self):
        """Test the verbose name for plan_name field."""
        obj = ManufacturingPlan.objects.get(id=self.plan1.id)
        field_label = obj._meta.get_field('plan_name').verbose_name
        self.assertEqual(field_label, 'Plan Name')

    def test_str_method(self):
        """Test the __str__ method returns the plan name."""
        obj = ManufacturingPlan.objects.get(id=self.plan1.id)
        self.assertEqual(str(obj), 'Q1 Production Plan')

    def test_is_active_method(self):
        """Test the is_active method."""
        # Assume current date is within the plan dates for plan1
        with self.settings(USE_TZ=True, TIME_ZONE='UTC'): # Ensure consistent timezone for testing
            today = timezone.localdate()
            if today >= self.plan1.start_date and today <= self.plan1.end_date:
                self.assertTrue(self.plan1.is_active())
            else:
                self.assertFalse(self.plan1.is_active())

            # Test a plan that definitely isn't active (future or past)
            future_plan = ManufacturingPlan.objects.create(
                plan_name='Future Plan',
                start_date=today + timedelta(days=365),
                end_date=today + timedelta(days=366)
            )
            self.assertFalse(future_plan.is_active())

    def test_get_status_display_method(self):
        """Test the get_status_display method."""
        obj_planned = ManufacturingPlan.objects.get(plan_name='Q1 Production Plan')
        self.assertEqual(obj_planned.get_status_display(), 'Planned')

        obj_in_progress = ManufacturingPlan.objects.get(plan_name='Q2 Production Plan')
        self.assertEqual(obj_in_progress.get_status_display(), 'In Progress')

        # Test an invalid status for robustness (though forms should prevent this)
        obj_planned.status = 'Invalid'
        self.assertEqual(obj_planned.get_status_display(), 'Invalid') # Should return original if not in map

    def test_model_validation_end_date_before_start_date(self):
        """Test model-level validation for end_date before start_date."""
        with self.assertRaises(Exception) as cm: # Should raise ValidationError from save()
            ManufacturingPlan.objects.create(
                plan_name='Bad Date Plan',
                start_date=date(2023, 3, 1),
                end_date=date(2023, 2, 28)
            )
        self.assertIn("End date cannot be before start date.", str(cm.exception))


class ManufacturingPlanFormTest(TestCase):
    def test_form_valid_data(self):
        """Test the form with valid data."""
        form = ManufacturingPlanForm(data={
            'plan_name': 'Test Form Plan',
            'start_date': '2024-01-01',
            'end_date': '2024-01-31',
            'target_quantity': 500,
            'status': 'Planned'
        })
        self.assertTrue(form.is_valid())

    def test_form_invalid_end_date_before_start_date(self):
        """Test form validation when end_date is before start_date."""
        form = ManufacturingPlanForm(data={
            'plan_name': 'Invalid Date Plan',
            'start_date': '2024-03-01',
            'end_date': '2024-02-28',
            'target_quantity': 100,
            'status': 'Planned'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('end_date', form.errors)
        self.assertIn('End date cannot be before start date.', form.errors['end_date'])

    def test_form_missing_required_fields(self):
        """Test form validation with missing required fields."""
        form = ManufacturingPlanForm(data={
            'plan_name': '', # Missing
            'start_date': '', # Missing
            'end_date': '2024-01-31',
            'status': 'Planned'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('plan_name', form.errors)
        self.assertIn('start_date', form.errors)


class ManufacturingPlanViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.plan = ManufacturingPlan.objects.create(
            plan_name='Initial Test Plan',
            start_date=date(2023, 1, 1),
            end_date=date(2023, 1, 31),
            target_quantity=200,
            status='Planned'
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        """Test the ManufacturingPlan list view."""
        response = self.client.get(reverse('manufacturingplan_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/manufacturingplan/list.html')
        self.assertIn('manufacturingplans', response.context)
        self.assertContains(response, self.plan.plan_name)

    def test_table_partial_view(self):
        """Test the HTMX partial for the table."""
        response = self.client.get(reverse('manufacturingplan_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/manufacturingplan/_manufacturingplan_table.html')
        self.assertIn('manufacturingplans', response.context)
        self.assertContains(response, self.plan.plan_name)
        # Check for DataTables script
        self.assertContains(response, "$(document).ready(function() { $('#manufacturingplanTable').DataTable");

    def test_create_view_get(self):
        """Test GET request for the create form."""
        response = self.client.get(reverse('manufacturingplan_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/manufacturingplan/_manufacturingplan_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_valid(self):
        """Test POST request with valid data for creation."""
        data = {
            'plan_name': 'New Mfg Plan',
            'start_date': '2024-07-01',
            'end_date': '2024-07-31',
            'target_quantity': 300,
            'status': 'Planned'
        }
        # Simulate HTMX request
        response = self.client.post(reverse('manufacturingplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshManufacturingPlanList')
        self.assertTrue(ManufacturingPlan.objects.filter(plan_name='New Mfg Plan').exists())
        self.assertEqual(messages.get_messages(response.wsgi_request)._loaded_messages[0].message, 'Manufacturing Plan added successfully.')

    def test_create_view_post_invalid(self):
        """Test POST request with invalid data for creation."""
        data = {
            'plan_name': '', # Invalid
            'start_date': '2024-03-01',
            'end_date': '2024-02-28', # Invalid
            'target_quantity': 100,
            'status': 'Planned'
        }
        response = self.client.post(reverse('manufacturingplan_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'dailyreport/manufacturingplan/_manufacturingplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End date cannot be before start date.')
        self.assertFalse(ManufacturingPlan.objects.filter(plan_name='').exists())

    def test_update_view_get(self):
        """Test GET request for the update form."""
        response = self.client.get(reverse('manufacturingplan_edit', args=[self.plan.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/manufacturingplan/_manufacturingplan_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.plan)

    def test_update_view_post_valid(self):
        """Test POST request with valid data for update."""
        updated_name = 'Updated Test Plan'
        data = {
            'plan_name': updated_name,
            'start_date': '2023-01-01',
            'end_date': '2023-02-28',
            'target_quantity': 250,
            'status': 'In Progress'
        }
        response = self.client.post(reverse('manufacturingplan_edit', args=[self.plan.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshManufacturingPlanList')
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, updated_name)
        self.assertEqual(messages.get_messages(response.wsgi_request)._loaded_messages[0].message, 'Manufacturing Plan updated successfully.')


    def test_update_view_post_invalid(self):
        """Test POST request with invalid data for update."""
        original_name = self.plan.plan_name
        data = {
            'plan_name': '', # Invalid
            'start_date': '2023-03-01',
            'end_date': '2023-02-28', # Invalid
            'target_quantity': 100,
            'status': 'Planned'
        }
        response = self.client.post(reverse('manufacturingplan_edit', args=[self.plan.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/manufacturingplan/_manufacturingplan_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'End date cannot be before start date.')
        self.plan.refresh_from_db()
        self.assertEqual(self.plan.plan_name, original_name) # Ensure no update happened

    def test_delete_view_get(self):
        """Test GET request for delete confirmation."""
        response = self.client.get(reverse('manufacturingplan_delete', args=[self.plan.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dailyreport/manufacturingplan/_manufacturingplan_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.plan)

    def test_delete_view_post(self):
        """Test POST request for deletion."""
        plan_to_delete = ManufacturingPlan.objects.create(
            plan_name='Plan to Delete',
            start_date=date(2023, 10, 1),
            end_date=date(2023, 10, 31),
            status='Planned'
        )
        response = self.client.post(reverse('manufacturingplan_delete', args=[plan_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshManufacturingPlanList')
        self.assertFalse(ManufacturingPlan.objects.filter(pk=plan_to_delete.pk).exists())
        self.assertEqual(messages.get_messages(response.wsgi_request)._loaded_messages[0].message, 'Manufacturing Plan deleted successfully.')

    def test_delete_view_not_found(self):
        """Test delete view for a non-existent object."""
        response = self.client.post(reverse('manufacturingplan_delete', args=[999]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Object does not exist, so it returns 404
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:**
    *   The main `list.html` uses `hx-get` on `manufacturingplanTable-container` with `hx-trigger="load, refreshManufacturingPlanList from:body"` to dynamically load the table content from `{% url 'manufacturingplan_table' %}`.
    *   "Add New", "Edit", and "Delete" buttons in `list.html` and `_manufacturingplan_table.html` use `hx-get` to fetch form/confirmation partials into `#modalContent`.
    *   Form submissions (`_manufacturingplan_form.html`, `_manufacturingplan_confirm_delete.html`) use `hx-post` with `hx-swap="none"`. Upon successful submission, the Django view returns a `204 No Content` response with an `HX-Trigger` header (`refreshManufacturingPlanList`), which then triggers the `manufacturingplanTable-container` to reload its content, effectively refreshing the DataTables display.
*   **Alpine.js for UI State Management (Modals):**
    *   The main `list.html` includes a modal structure with `x-data` and `x-show` to control its visibility.
    *   The `_` (Alpine.js) attribute on buttons handles adding/removing the `.is-active` class to the modal, which can be hooked into a CSS class to control display (e.g., `display: flex;` for active, `display: none;` for inactive).
    *   Additional JavaScript listens for `htmx:afterSwap` on `#modalContent` to ensure the modal becomes visible when content is loaded, and `htmx:afterRequest` for `204` responses to hide the modal after successful form submissions.
*   **DataTables for List Views:**
    *   The `_manufacturingplan_table.html` partial directly contains the `<table>` element and the JavaScript initialization code `$(document).ready(function() { $('#manufacturingplanTable').DataTable({...}); });`. This ensures that when the partial is loaded via HTMX, DataTables is initialized on the new table element.
    *   The DataTables configuration includes client-side searching, sorting, and pagination.

### Final Notes

*   **Placeholders Replaced:** All `[PLACEHOLDER]` values have been replaced with concrete names inferred from the ASP.NET module name.
*   **DRY Templates:** We use a base template (`core/base.html`, assumed to exist) and partial templates (`_manufacturingplan_table.html`, `_manufacturingplan_form.html`, `_manufacturingplan_confirm_delete.html`) for reusable components, following DRY principles.
*   **Fat Model, Thin View:** Business logic, such as date validation, is primarily handled within the `ManufacturingPlan` model's `save` method or the `ManufacturingPlanForm`'s `clean` method. Django Class-Based Views (`ListView`, `CreateView`, `UpdateView`, `DeleteView`) are used to keep the view logic minimal and focused on routing requests and rendering appropriate templates.
*   **Comprehensive Tests:** Unit tests for model methods and properties, as well as integration tests for all CRUD views (including HTMX interactions), are provided to ensure functionality and maintainability.
*   **AI-Assisted Automation Focus:** This entire plan is structured to be systematically generated. Given sufficient input (e.g., a formal schema definition, UI control mappings), an AI could automate the generation of these Django files, reducing manual effort and potential errors significantly.