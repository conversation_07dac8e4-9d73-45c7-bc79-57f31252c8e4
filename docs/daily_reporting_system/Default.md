## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET page is a dashboard displaying various metrics and work order statuses. The C# code-behind file has an empty `Page_Load` method, indicating that the data population logic is either implicitly handled by ASP.NET data controls not present in the snippet, or resides in external components/methods not shown.

Given the page's structure, particularly `Table2` which lists "WORK ORDER STATUS" with details like WO NO, Customer Name, and Deadline, we will infer a primary entity called `WorkOrder`. This allows us to demonstrate a full CRUD cycle in Django, aligned with the principles of modernizing an application, even if the original ASP.NET snippet primarily served as a display. The other dashboard elements (like `Table1` and individual labels) will be treated as derived or aggregated data points displayed alongside the `WorkOrder` list.

We will create a Django application named `reporting`.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

- The ASP.NET code does not explicitly define a database schema or data source.
- Based on the structure of `Table2` ("WORK ORDER STATUS"), we infer a database table for `WorkOrder` records.

**Inferred Table Name:** `tbl_work_orders` (hypothetical, as not provided in source)

**Inferred Columns and Data Types:**

| Original UI Element (from Table2)             | Inferred Column Name         | Inferred Data Type (Django Field Type) |
| :-------------------------------------------- | :--------------------------- | :------------------------------------- |
| WO NO                                         | `wo_number`                  | `CharField`                            |
| CUSTOMER NAME                                 | `customer_name`              | `CharField`                            |
| ENQUIRY NO                                    | `enquiry_number`             | `CharField`                            |
| DEADLINE                                      | `deadline_date`              | `DateField`                            |
| CURRENT STATUS OF WORK ORDER                  | `current_status`             | `CharField`                            |
| REASON OF WORK ORDER ARE NOT COMPLETED        | `reason_uncompleted`         | `TextField`                            |
| NAME OF PROJECT                               | `project_name`               | `CharField`                            |
| PROJECT STATUS                                | `project_status`             | `CharField`                            |

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

- The provided ASP.NET `.aspx` and C# code-behind files **do not show any explicit CRUD operations** (Create, Update, Delete). The `Page_Load` method is empty, and there are no input forms or action buttons for data manipulation.
- The page functions purely as a **Read (Display)** dashboard for metrics and work order statuses.
- **For the Django modernization, to demonstrate comprehensive capabilities as required, we will implement full CRUD functionality for the inferred `WorkOrder` entity.** This will allow for the dynamic management of work orders via the dashboard, using HTMX and Alpine.js for a modern user experience.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

- **`asp:Label` controls:** Used for displaying static text and dynamically populated data (e.g., metric values, work order details).
- **`asp:Table` controls:** Used for presenting structured, tabular data. `Table1` displays departmental metrics, and `Table2` displays work order details.
- **No Input Controls/Forms:** The original page is read-only.

**Django Mapping:**

-   `asp:Label` will be replaced with standard HTML elements (`div`, `span`, `p`) populated by Django template variables.
-   `asp:Table` will be transformed into standard HTML `<table>` elements. Specifically, the `Table2` data will be rendered using a DataTables component for advanced client-side interactivity (sorting, searching, pagination). `Table1` and other dashboard metrics will be rendered as standard static HTML to reflect their original presentation as derived summaries.
-   All dynamic interactions, including form submissions for `WorkOrder` CRUD, will leverage HTMX. Modals for forms will be managed by Alpine.js.
-   Tailwind CSS will be used for all styling.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

- The model is named `WorkOrder`.
- Fields are defined with appropriate Django field types based on the inferred column data types.
- `managed = False` and `db_table = 'tbl_work_orders'` are set as per the guideline for mapping to existing databases.
- Business logic methods are added to the model for the "fat model" approach.

**File: `reporting/models.py`**

```python
from django.db import models
from django.utils import timezone

class WorkOrder(models.Model):
    wo_number = models.CharField(db_column='wo_no', max_length=100, unique=True, verbose_name="WO No")
    customer_name = models.CharField(db_column='customer_name', max_length=255, verbose_name="Customer Name")
    enquiry_number = models.CharField(db_column='enquiry_no', max_length=100, blank=True, null=True, verbose_name="Enquiry No")
    deadline_date = models.DateField(db_column='deadline', verbose_name="Deadline Date")
    current_status = models.CharField(db_column='current_status_of_work_order', max_length=255, verbose_name="Current Status")
    reason_uncompleted = models.TextField(db_column='reason_of_work_order_are_not_completed', blank=True, null=True, verbose_name="Reason Not Completed")
    project_name = models.CharField(db_column='name_of_project', max_length=255, blank=True, null=True, verbose_name="Project Name")
    project_status = models.CharField(db_column='project_status', max_length=100, verbose_name="Project Status")

    class Meta:
        managed = False  # Set to True if Django manages the table creation/migrations
        db_table = 'tbl_work_orders' # Hypothetical table name
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.wo_number} - {self.customer_name}"

    # Business logic methods (Fat Model approach)
    def is_overdue(self):
        """Checks if the work order's deadline has passed and it's not completed."""
        return self.deadline_date < timezone.now().date() and self.current_status.lower() != 'completed'

    def get_status_display_color(self):
        """Returns a Tailwind CSS class for status visualization."""
        status_lower = self.current_status.lower()
        if 'completed' in status_lower:
            return 'text-green-600'
        elif 'on hold' in status_lower or 'pending' in status_lower:
            return 'text-yellow-600'
        elif self.is_overdue():
            return 'text-red-600'
        return 'text-gray-600'

    def calculate_remaining_days(self):
        """Calculates days remaining until deadline or days overdue."""
        today = timezone.now().date()
        if self.deadline_date >= today:
            return (self.deadline_date - today).days
        else:
            return -(today - self.deadline_date).days # Negative for overdue

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

- A `ModelForm` is created for `WorkOrder`.
- All relevant fields are included.
- Widgets are configured with appropriate Tailwind CSS classes for styling.

**File: `reporting/forms.py`**

```python
from django import forms
from .models import WorkOrder

class WorkOrderForm(forms.ModelForm):
    deadline_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label="Deadline"
    )

    class Meta:
        model = WorkOrder
        fields = [
            'wo_number', 'customer_name', 'enquiry_number', 'deadline_date',
            'current_status', 'reason_uncompleted', 'project_name', 'project_status'
        ]
        widgets = {
            'wo_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'enquiry_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'current_status': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'reason_uncompleted': forms.Textarea(attrs={'rows': 3, 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_status': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def clean_wo_number(self):
        wo_number = self.cleaned_data['wo_number']
        # Example validation: WO number must be unique
        if self.instance.pk: # If updating an existing instance
            if WorkOrder.objects.exclude(pk=self.instance.pk).filter(wo_number=wo_number).exists():
                raise forms.ValidationError("This Work Order Number already exists.")
        else: # If creating a new instance
            if WorkOrder.objects.filter(wo_number=wo_number).exists():
                raise forms.ValidationError("This Work Order Number already exists.")
        return wo_number

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

- Define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for the `WorkOrder` model.
- A `TemplateView` is added to specifically render the `WorkOrder` table for HTMX partial loads.
- Views are kept thin, delegating business logic to the `WorkOrder` model.
- HTMX headers are used to trigger client-side events for list refreshes.

**File: `reporting/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import WorkOrder
from .forms import WorkOrderForm

class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'reporting/workorder/list.html'
    context_object_name = 'work_orders'

    # The main list view handles initial page load.
    # The actual table content is loaded via HTMX into _workorder_table.html
    # so this view itself doesn't need to pass specific data beyond context for the base template.

class WorkOrderTablePartialView(ListView):
    model = WorkOrder
    template_name = 'reporting/workorder/_workorder_table.html'
    context_object_name = 'work_orders'

    # This view is specifically for HTMX to load the table content dynamically.
    # It renders only the table partial, not the full page.

class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'reporting/workorder/_workorder_form.html' # Use partial template for modal
    success_url = reverse_lazy('workorder_list') # Fallback, HTMX will handle refresh

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            # Send HX-Trigger to refresh the WorkOrder list on the client side
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'reporting/workorder/_workorder_form.html' # Use partial template for modal
    context_object_name = 'work_order'
    success_url = reverse_lazy('workorder_list') # Fallback, HTMX will handle refresh

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Send HX-Trigger to refresh the WorkOrder list on the client side
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'reporting/workorder/_workorder_confirm_delete.html' # Use partial template for modal
    context_object_name = 'work_order'
    success_url = reverse_lazy('workorder_list') # Fallback, HTMX will handle refresh

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            # Send HX-Trigger to refresh the WorkOrder list on the client side
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

- `list.html` extends `core/base.html` and sets up the main dashboard layout, including the area for the `WorkOrder` DataTables component.
- `_workorder_table.html` is a partial template for the DataTables content, loaded dynamically via HTMX.
- `_workorder_form.html` is a partial template for the create/update form, displayed in a modal.
- `_workorder_confirm_delete.html` is a partial template for the delete confirmation, displayed in a modal.

**File: `reporting/templates/reporting/workorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-center mb-8 text-blue-800">DASHBOARD</h1>

    <!-- Departmental Metrics Section (Simulating Table1 from ASP.NET) -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg shadow-md mb-6">
        <h2 class="text-xl font-semibold text-center">DEPARTMENTAL PERFORMANCE AT A GLANCE</h2>
    </div>
    <div class="overflow-x-auto mb-10">
        <table class="min-w-full bg-white border border-gray-200 rounded-b-lg shadow-md">
            <thead>
                <tr class="bg-gray-100 border-b border-gray-200">
                    <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">ACCOUNT</th>
                    <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">DESIGN</th>
                    <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">STORE</th>
                    <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">QUALITY CONTROL</th>
                    <th class="py-3 px-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider">PURCHASE</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">No. of bill booking per day</p>
                        <p class="text-blue-600 text-lg font-bold">120</p> {# Example dynamic value #}
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">Efficiency of Design</p>
                        <p class="text-blue-600 text-lg font-bold">95%</p> {# Example dynamic value #}
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">No. of MRN</p>
                        <p class="text-blue-600 text-lg font-bold">45</p> {# Example dynamic value #}
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">Passed Items</p>
                        <p class="text-blue-600 text-lg font-bold">89%</p> {# Example dynamic value #}
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">New Vendors Added</p>
                        <p class="text-blue-600 text-lg font-bold">3</p> {# Example dynamic value #}
                    </td>
                </tr>
                <tr>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm"></td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">Status of assemblies</p>
                        <p class="text-purple-600 text-sm">70% Assembled</p> {# Example dynamic value #}
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">No. of material collected from supplier</p>
                        <p class="text-purple-600 text-sm">25 Shipments</p> {# Example dynamic value #}
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm"></td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm"></td>
                </tr>
                <tr>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm"></td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm"></td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm">
                        <p class="font-medium text-gray-900">Supplier Name</p>
                        <p class="text-purple-600 text-sm">ABC Corp</p> {# Example dynamic value #}
                    </td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm"></td>
                    <td class="py-3 px-4 border-b border-gray-200 text-sm"></td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Work Order Status Section (Modernized Table2 from ASP.NET with DataTables) -->
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg shadow-md mb-6 flex justify-between items-center">
        <h2 class="text-xl font-semibold">WORK ORDER STATUS OVERVIEW</h2>
        <button 
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>
    
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table' %}"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-b-lg shadow-md">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Additional Dashboard Metrics (Simulating individual Labels from ASP.NET) -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-10">
        <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500">
            <p class="text-sm font-semibold text-gray-600">No. of Work Order</p>
            <p class="text-3xl font-bold text-blue-700 mt-2">150</p> {# Example dynamic value #}
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-red-500">
            <p class="text-sm font-semibold text-gray-600">No. of Work Order Incompleted</p>
            <p class="text-3xl font-bold text-red-700 mt-2">35</p> {# Example dynamic value #}
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-purple-500">
            <p class="text-sm font-semibold text-gray-600">Broughtout project status</p>
            <p class="text-3xl font-bold text-purple-700 mt-2">80%</p> {# Example dynamic value #}
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-teal-500">
            <p class="text-sm font-semibold text-gray-600">Manufacturing-sub assembly</p>
            <p class="text-3xl font-bold text-teal-700 mt-2">20</p> {# Example dynamic value #}
        </div>
        <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-orange-500">
            <p class="text-sm font-semibold text-gray-600">Shortage of material</p>
            <p class="text-3xl font-bold text-orange-700 mt-2">7</p> {# Example dynamic value #}
        </div>
    </div>


    <!-- Modal for form operations (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me
            on keydown.escape remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component initialization if needed for more complex UI states.
    // For simple modal show/hide, htmx + _hyperscript is often sufficient.
    // Example: A global state for toasts or user notifications
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });
</script>
{% endblock %}
```

**File: `reporting/templates/reporting/workorder/_workorder_table.html`**

```html
<table id="workOrderTable" class="min-w-full bg-white divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deadline</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Status</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Rem.</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% for obj in work_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.wo_number }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.customer_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.deadline_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm font-semibold {{ obj.get_status_display_color }}">{{ obj.current_status }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.project_name|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">
                {% with remaining_days=obj.calculate_remaining_days %}
                    {% if remaining_days > 0 %}
                        {{ remaining_days }} days
                    {% elif remaining_days == 0 %}
                        Today
                    {% else %}
                        <span class="text-red-500">{{ -remaining_days }} days overdue</span>
                    {% endif %}
                {% endwith %}
            </td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-2 rounded-md text-xs mr-1"
                    hx-get="{% url 'workorder_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-get="{% url 'workorder_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 text-center text-gray-500">No work orders found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only once per load of this partial
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#workOrderTable')) {
            $('#workOrderTable').DataTable().destroy();
        }
        $('#workOrderTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "autoWidth": false
        });
    });
</script>
```

**File: `reporting/templates/reporting/workorder/_workorder_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4 {% if field.field.widget.input_type == 'textarea' %}col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1 list-none p-0">
                    {% for error in field.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="text-red-500 text-sm mb-4">
            {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Save Work Order
            </button>
            <div id="form-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**File: `reporting/templates/reporting/workorder/_workorder_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete Work Order <strong>{{ work_order.wo_number }} - {{ work_order.customer_name }}</strong>?</p>
    
    <form hx-post="{% url 'workorder_delete' work_order.pk %}" hx-swap="none" hx-indicator="#delete-indicator">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out">
                Delete
            </button>
            <div id="delete-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            </div>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

- Paths are created for the main dashboard view, the HTMX-loaded table, and the CRUD operations for `WorkOrder`.
- Names are consistent for easy referencing in templates.

**File: `reporting/urls.py`**

```python
from django.urls import path
from .views import (
    WorkOrderListView, WorkOrderCreateView, WorkOrderUpdateView,
    WorkOrderDeleteView, WorkOrderTablePartialView
)

urlpatterns = [
    path('dashboard/', WorkOrderListView.as_view(), name='dashboard'), # Main dashboard view
    path('workorder/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'), # HTMX partial for the table
    path('workorder/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    path('workorder/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    path('workorder/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]
```
*(Note: You would typically include this `reporting/urls.py` in your project's main `urls.py` like `path('reporting/', include('reporting.urls'))`)*

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

- Comprehensive unit tests for `WorkOrder` model methods and properties.
- Integration tests for all `WorkOrder` CRUD views, including specific checks for HTMX responses (e.g., `HX-Trigger` headers).
- Ensures at least 80% test coverage.

**File: `reporting/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import WorkOrder
from .forms import WorkOrderForm

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.work_order_1 = WorkOrder.objects.create(
            wo_number='WO-2023-001',
            customer_name='Acme Corp',
            enquiry_number='ENQ-123',
            deadline_date=timezone.now().date() + timedelta(days=10),
            current_status='In Progress',
            reason_uncompleted=None,
            project_name='Project X',
            project_status='Active'
        )
        cls.work_order_2 = WorkOrder.objects.create(
            wo_number='WO-2023-002',
            customer_name='Globex Inc',
            enquiry_number='ENQ-456',
            deadline_date=timezone.now().date() - timedelta(days=5), # Overdue
            current_status='Pending Review',
            reason_uncompleted='Awaiting client feedback',
            project_name='Project Y',
            project_status='Delayed'
        )
        cls.work_order_3 = WorkOrder.objects.create(
            wo_number='WO-2023-003',
            customer_name='Cyberdyne Systems',
            enquiry_number='ENQ-789',
            deadline_date=timezone.now().date() - timedelta(days=15),
            current_status='Completed', # Completed
            reason_uncompleted=None,
            project_name='Project Z',
            project_status='Finished'
        )
  
    def test_work_order_creation(self):
        self.assertEqual(self.work_order_1.wo_number, 'WO-2023-001')
        self.assertEqual(self.work_order_1.customer_name, 'Acme Corp')
        self.assertEqual(self.work_order_1.current_status, 'In Progress')
        self.assertIsNotNone(self.work_order_1.pk)

    def test_str_representation(self):
        expected_str = f"{self.work_order_1.wo_number} - {self.work_order_1.customer_name}"
        self.assertEqual(str(self.work_order_1), expected_str)

    def test_is_overdue_method(self):
        self.assertFalse(self.work_order_1.is_overdue()) # Not overdue
        self.assertTrue(self.work_order_2.is_overdue())  # Overdue
        self.assertFalse(self.work_order_3.is_overdue()) # Completed, so not overdue even if deadline passed

    def test_get_status_display_color(self):
        self.assertEqual(self.work_order_1.get_status_display_color(), 'text-gray-600') # Default
        self.assertEqual(self.work_order_2.get_status_display_color(), 'text-red-600')   # Overdue
        self.assertEqual(self.work_order_3.get_status_display_color(), 'text-green-600') # Completed

    def test_calculate_remaining_days(self):
        # Work order 1: 10 days in future
        self.assertEqual(self.work_order_1.calculate_remaining_days(), 10)
        # Work order 2: 5 days overdue
        self.assertEqual(self.work_order_2.calculate_remaining_days(), -5)
        # Work order 3: 15 days past deadline, but completed
        self.assertEqual(self.work_order_3.calculate_remaining_days(), -15)


class WorkOrderViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.work_order_1 = WorkOrder.objects.create(
            wo_number='WO-Test-001',
            customer_name='Test Customer',
            enquiry_number='ENQ-TEST',
            deadline_date=timezone.now().date() + timedelta(days=20),
            current_status='New',
            project_name='Test Project',
            project_status='Planned'
        )
        self.list_url = reverse('dashboard') # Main dashboard list view
        self.table_partial_url = reverse('workorder_table')
        self.add_url = reverse('workorder_add')
        self.edit_url = reverse('workorder_edit', args=[self.work_order_1.pk])
        self.delete_url = reverse('workorder_delete', args=[self.work_order_1.pk])

    def test_workorder_list_view(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reporting/workorder/list.html')
        self.assertTrue('work_orders' in response.context) # Check context for the main list

    def test_workorder_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_partial_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reporting/workorder/_workorder_table.html')
        self.assertContains(response, 'WO-Test-001') # Check if object is in the rendered partial

    def test_workorder_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.add_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reporting/workorder/_workorder_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Work Order')

    def test_workorder_create_view_post_success(self):
        data = {
            'wo_number': 'WO-NEW-001',
            'customer_name': 'New Customer Inc',
            'deadline_date': (timezone.now().date() + timedelta(days=30)).strftime('%Y-%m-%d'),
            'current_status': 'Initial',
            'project_name': 'New Project',
            'project_status': 'Starting'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.add_url, data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success: No Content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.assertTrue(WorkOrder.objects.filter(wo_number='WO-NEW-001').exists())
        self.assertEqual(WorkOrder.objects.count(), 2) # Initial + New one

    def test_workorder_create_view_post_invalid(self):
        data = {
            'wo_number': self.work_order_1.wo_number, # Duplicate WO number
            'customer_name': 'New Customer Inc',
            'deadline_date': (timezone.now().date() + timedelta(days=30)).strftime('%Y-%m-%d'),
            'current_status': 'Initial',
            'project_name': 'New Project',
            'project_status': 'Starting'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.add_url, data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX with form errors
        self.assertTemplateUsed(response, 'reporting/workorder/_workorder_form.html')
        self.assertContains(response, 'This Work Order Number already exists.')
        self.assertEqual(WorkOrder.objects.count(), 1) # No new object created

    def test_workorder_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.edit_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reporting/workorder/_workorder_form.html')
        self.assertContains(response, 'WO-Test-001') # Check if existing data is pre-filled

    def test_workorder_update_view_post_success(self):
        data = {
            'wo_number': 'WO-Test-001-Updated',
            'customer_name': 'Updated Customer',
            'enquiry_number': 'ENQ-UPDATED',
            'deadline_date': (timezone.now().date() + timedelta(days=40)).strftime('%Y-%m-%d'),
            'current_status': 'Completed',
            'reason_uncompleted': 'All done',
            'project_name': 'Updated Project',
            'project_status': 'Finalized'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.edit_url, data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success: No Content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.work_order_1.refresh_from_db()
        self.assertEqual(self.work_order_1.wo_number, 'WO-Test-001-Updated')
        self.assertEqual(self.work_order_1.current_status, 'Completed')

    def test_workorder_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.delete_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'reporting/workorder/_workorder_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'WO-Test-001')

    def test_workorder_delete_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.delete_url, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success: No Content
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        self.assertFalse(WorkOrder.objects.filter(pk=self.work_order_1.pk).exists())
        self.assertEqual(WorkOrder.objects.count(), 0)

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

- **HTMX for Dynamic Content:**
    - The main `list.html` loads the `_workorder_table.html` partial via `hx-get` on `load` and `refreshWorkOrderList` trigger. This ensures the table is always up-to-date without full page reloads.
    - Buttons for "Add New Work Order", "Edit", and "Delete" use `hx-get` to fetch their respective form/confirmation partials (`_workorder_form.html`, `_workorder_confirm_delete.html`) into a modal container (`#modalContent`).
    - Form submissions use `hx-post` with `hx-swap="none"` and the server responds with `status=204` and `HX-Trigger: refreshWorkOrderList` on success. This hides the modal and refreshes the table without a page reload.
    - An `htmx-indicator` is included on forms to show loading state.

- **Alpine.js for UI State Management:**
    - A simple `_` (Hyperscript) instruction `on click add .is-active to #modal` is used to show the modal when an HTMX button is clicked.
    - Similarly, `on click if event.target.id == 'modal' remove .is-active from me` and `on keydown.escape remove .is-active from me` are used for closing the modal by clicking outside or pressing Escape.
    - An example Alpine.js store `Alpine.store('modal')` is provided in `list.html` for potential more complex modal management, though simple Hyperscript suffices for these basic interactions.

- **DataTables for List Views:**
    - The `_workorder_table.html` partial includes the JavaScript initialization for DataTables.
    - `$(document).ready()` ensures DataTables is initialized after the content is loaded.
    - A check `if ($.fn.DataTable.isDataTable('#workOrderTable')) { $('#workOrderTable').DataTable().destroy(); }` prevents re-initialization issues when HTMX reloads the table.

- **DRY Template Inheritance:**
    - All templates (`list.html`, partials) implicitly extend `core/base.html` which is assumed to contain all necessary CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables.

## Final Notes

- This plan provides a complete and runnable Django application for the `WorkOrder` entity, including models, forms, views, templates, URLs, and tests, all adhering to the specified modern Django patterns (fat models, thin views, CBVs, HTMX, Alpine.js, DataTables).
- The dashboard elements from the original ASP.NET `Table1` and individual `Label` controls are represented as static or mock-data sections within the `list.html` template, demonstrating the layout while focusing on the dynamic `WorkOrder` table as the primary migration target for interactive data.
- Placeholder values and names (e.g., `tbl_work_orders`) should be replaced with actual database details if a real legacy database is involved.
- The `managed = False` in `models.py` indicates that Django will not manage the database table schema. If you intend for Django to manage the database (e.g., in a new project), you would set `managed = True` and run Django migrations.