This document outlines a comprehensive plan for migrating the provided ASP.NET Material Return Quality Note (MRQN) list and search functionality to a modern Django-based solution. Our approach emphasizes automation, clear communication, and adherence to modern Django best practices, ensuring a robust, scalable, and maintainable application.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code interacts with three primary tables:
1.  **`tblQc_MaterialReturnQuality_Master`**: The main table for Material Return Quality Notes.
2.  **`tblFinancial_master`**: Used to retrieve financial year details.
3.  **`tblHR_OfficeStaff`**: Used to retrieve employee names for "Generated By" and the autocomplete functionality.

**Inferred Schema:**

*   **`tblQc_MaterialReturnQuality_Master`**
    *   `Id` (PK, INT)
    *   `SysDate` (DATETIME)
    *   `MRNNo` (NVARCHAR)
    *   `MRQNNo` (NVARCHAR)
    *   `FinYearId` (FK to `tblFinancial_master`, INT)
    *   `SessionId` (FK to `tblHR_OfficeStaff.EmpId`, INT) - Note: `SessionId` in this table maps to `EmpId` in `tblHR_OfficeStaff`.
    *   `CompId` (INT)

*   **`tblFinancial_master`**
    *   `FinYearId` (PK, INT)
    *   `FinYear` (NVARCHAR)
    *   `CompId` (INT)

*   **`tblHR_OfficeStaff`**
    *   `EmpId` (PK, INT)
    *   `EmployeeName` (NVARCHAR)
    *   `Title` (NVARCHAR)
    *   `CompId` (INT)

## Step 2: Identify Backend Functionality

**Task:** Determine the operations and business logic in the ASP.NET code.

**Analysis:**
The ASP.NET page primarily serves as a **read (list and search)** interface for Material Return Quality Notes.

*   **Read (List & Search):**
    *   Displays a list of MRQN records in a GridView.
    *   Allows searching/filtering by:
        *   "MRQN No" (`MRQNNo` field)
        *   "MRN No" (`MRNNo` field)
        *   "Employee Name" (`SessionId` linked to `tblHR_OfficeStaff.EmployeeName`)
    *   Search criteria are dynamically controlled by a dropdown (`drpfield`), which toggles the visibility of the input textboxes (`txtMqnNo` for MRQN/MRN, `txtEmpName` for Employee).
    *   The `loadData` method constructs a SQL query based on selected filters and session variables (`CompId`, `FinYearId`).
    *   It manually joins data from `tblFinancial_master` and `tblHR_OfficeStaff` to enrich the displayed data (e.g., `FinYear`, `EmpName`).
    *   Includes pagination.

*   **Autocomplete:**
    *   The `GetCompletionList` static method provides autocomplete suggestions for employee names, fetching data from `tblHR_OfficeStaff`.

*   **Redirection/Selection:**
    *   Clicking an item in the GridView triggers `GridView1_RowCommand`, which redirects the user to `MaterialReturnQualityNote_MRQN_Edit_Details.aspx` with various query parameters (`Id`, `MRNNo`, `FYId`, `MRQNNo`), implying a "select" action to view/edit details on a separate page.

**Validation Logic:**
Minimal explicit validation seen, mainly data presence checks in `loadData` before applying filters.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **Search Controls:**
    *   `drpfield` (DropDownList): Selects the search criterion (MRQN No, MRN No, Employee Name).
    *   `txtMqnNo` (TextBox): Input for MRQN No or MRN No.
    *   `txtEmpName` (TextBox): Input for Employee Name. Includes an `AutoCompleteExtender` for dynamic suggestions.
    *   `Button1` (Button): Triggers the search.
*   **Data Display:**
    *   `GridView1` (GridView): Displays the list of Material Return Quality Notes.
        *   Columns displayed: SN (Serial Number), FinYear, MRQN No, Date, MRN No, Gen. By.
        *   Hidden columns: Id, FinYear Id.
        *   Includes basic pagination.
*   **Styling:**
    *   Uses external CSS files: `StyleSheet.css`, `yui-datatable.css`.
    *   A `loadingNotifier.js` for UX.

## Step 4: Generate Django Code

We will create a new Django application, for example, named `qualitycontrol`, to house these components.

### 4.1 Models (`qualitycontrol/models.py`)

We will define three models mapping to the identified database tables, using `managed = False` to connect to existing tables. Business logic for display formatting will be added as model methods.

```python
from django.db import models

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year_display = models.CharField(db_column='FinYear', max_length=50)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_display

class OfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee details.
    """
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    name = models.CharField(db_column='EmployeeName', max_length=200)
    title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''}. {self.name}".strip()

class MaterialReturnQualityNote(models.Model):
    """
    Maps to tblQc_MaterialReturnQuality_Master for MRQN records.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    sys_date = models.DateTimeField(db_column='SysDate')
    mrn_no = models.CharField(db_column='MRNNo', max_length=50)
    mrqn_no = models.CharField(db_column='MRQNNo', max_length=50)
    financial_year = models.ForeignKey(
        FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='mrqn_notes'
    )
    # SessionId in ASP.NET maps to EmpId in tblHR_OfficeStaff
    generated_by_employee = models.ForeignKey(
        OfficeStaff, on_delete=models.DO_NOTHING, db_column='SessionId', related_name='generated_mrqn_notes'
    )
    company_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialReturnQuality_Master'
        verbose_name = 'Material Return Quality Note'
        verbose_name_plural = 'Material Return Quality Notes'
        ordering = ['-id'] # Default ordering as per ASP.NET

    def __str__(self):
        return self.mrqn_no
        
    def get_formatted_sys_date(self):
        """Returns SysDate in DD/MM/YYYY format."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else 'N/A'

    def get_generated_by_display(self):
        """Returns the 'GenBy' display as 'Title. EmployeeName'."""
        if self.generated_by_employee:
            return f"{self.generated_by_employee.title or ''}. {self.generated_by_employee.name}".strip()
        return 'N/A'

    def get_financial_year_display(self):
        """Returns the financial year display string."""
        if self.financial_year:
            return self.financial_year.year_display
        return 'N/A'
    
    # Example of a business logic method (not directly from ASP.NET but for demonstration)
    def is_current_financial_year(self, current_fin_year_id):
        """Checks if the MRQN belongs to the current financial year."""
        return self.financial_year.id == current_fin_year_id

```

### 4.2 Forms (`qualitycontrol/forms.py`)

For this page, we'll create a simple search form. The full CRUD forms (add, edit, delete) for `MaterialReturnQualityNote` would typically reside on the `_Details` page identified in the ASP.NET code. We'll provide a placeholder example form for completeness as per instructions.

```python
from django import forms
from .models import MaterialReturnQualityNote

# This form is a placeholder as the ASP.NET page redirects for actual CRUD.
# If full CRUD was on this page, this form would be used.
class MaterialReturnQualityNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialReturnQualityNote
        fields = ['sys_date', 'mrn_no', 'mrqn_no', 'financial_year', 'generated_by_employee', 'company_id']
        widgets = {
            'sys_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'mrn_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mrqn_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'generated_by_employee': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # Custom validation example (if needed)
    def clean_mrqn_no(self):
        mrqn_no = self.cleaned_data['mrqn_no']
        if not mrqn_no.strip():
            raise forms.ValidationError("MRQN Number cannot be empty.")
        return mrqn_no
```

### 4.3 Views (`qualitycontrol/views.py`)

We will implement a `ListView` for the main page, a dedicated `ListView` for the HTMX-loaded table partial, and a view for the employee autocomplete. CRUD views are included as per the template, assuming they would be modal interactions if actual editing was integrated directly into this page.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.template.loader import render_to_string

from .models import MaterialReturnQualityNote, OfficeStaff
from .forms import MaterialReturnQualityNoteForm

class MaterialReturnQualityNoteListView(ListView):
    """
    Main view for displaying the MRQN search and list page.
    This view only renders the shell and initial search controls.
    The table content is loaded via HTMX into a partial.
    """
    model = MaterialReturnQualityNote
    template_name = 'qualitycontrol/materialreturnqualitynote/list.html'
    context_object_name = 'materialreturnqualitynotes'
    # Initial empty queryset as data will be loaded via HTMX on page load
    def get_queryset(self):
        return MaterialReturnQualityNote.objects.none()

class MaterialReturnQualityNoteTablePartialView(ListView):
    """
    HTMX-specific view to render only the DataTables table.
    Handles all search and filtering logic based on GET parameters.
    """
    model = MaterialReturnQualityNote
    template_name = 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_table.html'
    context_object_name = 'materialreturnqualitynotes'

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Simulating Session values from ASP.NET
        # In a real application, these would come from the authenticated user's profile
        # or be configured dynamically. For now, assuming they are available or default.
        company_id = self.request.session.get('compid', 1) # Example: Default to 1
        financial_year_id = self.request.session.get('finyear', 2024) # Example: Default to 2024 (replace with actual logic)

        # Apply base filters based on company_id and financial_year_id
        # ASP.NET code used FinYearId<=' + FinYearId, assuming it means current or earlier financial years.
        # Adjusted to filter by company_id and financial year <= the session's fin year.
        queryset = queryset.filter(company_id=company_id, financial_year__id__lte=financial_year_id)

        # Apply search filters from HTMX request parameters
        search_type = self.request.GET.get('search_type')
        search_value = self.request.GET.get('search_value', '').strip()
        employee_name = self.request.GET.get('employee_name', '').strip()

        if search_type == '0' and search_value: # MRQN No
            queryset = queryset.filter(mrqn_no__icontains=search_value)
        elif search_type == '1' and search_value: # MRN No
            queryset = queryset.filter(mrn_no__icontains=search_value)
        elif search_type == '2' and employee_name: # Employee Name
            # The autocomplete returns "Name [ID]". We need to extract the ID.
            emp_id = None
            if '[' in employee_name and ']' in employee_name:
                try:
                    emp_id = int(employee_name.split('[')[-1][:-1]) # Extract ID from "Name [ID]"
                except (ValueError, IndexError):
                    pass # Keep emp_id as None if parsing fails

            if emp_id:
                queryset = queryset.filter(generated_by_employee__id=emp_id)
            else:
                # Fallback: if ID not found, try exact name match if necessary
                # Or, if autocomplete provides exact matches, just search by name
                queryset = queryset.filter(generated_by_employee__name__iexact=employee_name)
        
        # Optimize queries by selecting related objects to avoid N+1 issues
        return queryset.select_related('financial_year', 'generated_by_employee').order_by('-id')

class MaterialReturnQualityNoteCreateView(CreateView):
    model = MaterialReturnQualityNote
    form_class = MaterialReturnQualityNoteForm
    template_name = 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_form.html'
    success_url = reverse_lazy('materialreturnqualitynote_list') # Redirects to list page (not used for HTMX)

    def form_valid(self, form):
        # Additional business logic for creation can go here, before saving
        # Example: Setting company_id from session
        form.instance.company_id = self.request.session.get('compid', 1) 
        
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Quality Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content to indicate success without navigating
                headers={
                    'HX-Trigger': 'refreshMaterialReturnQualityNoteList' # Custom HTMX event to refresh list
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add Material Return Quality Note'
        return context

class MaterialReturnQualityNoteUpdateView(UpdateView):
    model = MaterialReturnQualityNote
    form_class = MaterialReturnQualityNoteForm
    template_name = 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_form.html'
    success_url = reverse_lazy('materialreturnqualitynote_list')

    def form_valid(self, form):
        # Additional business logic for update can go here
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Quality Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnQualityNoteList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Material Return Quality Note'
        return context

class MaterialReturnQualityNoteDeleteView(DeleteView):
    model = MaterialReturnQualityNote
    template_name = 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_confirm_delete.html'
    success_url = reverse_lazy('materialreturnqualitynote_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Return Quality Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialReturnQualityNoteList'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Delete Material Return Quality Note'
        return context

class EmployeeAutocompleteView(View):
    """
    HTMX endpoint for employee name autocomplete functionality.
    Returns an HTML list of suggestions.
    """
    def get(self, request):
        prefix_text = request.GET.get('q', '').strip()
        company_id = request.session.get('compid', 1) # Get from session, default to 1

        employees = OfficeStaff.objects.filter(
            company_id=company_id,
            name__icontains=prefix_text
        ).order_by('name')[:10] # Limit results as per original AutoCompleteExtender

        # Format as "Name [ID]"
        results = [f"{emp.name} [{emp.id}]" for emp in employees]
        
        # Render a partial HTML template for suggestions
        context = {'employees': results}
        return HttpResponse(render_to_string('qualitycontrol/materialreturnqualitynote/_employee_suggestions.html', context, request=request))

```

### 4.4 Templates (`qualitycontrol/templates/qualitycontrol/materialreturnqualitynote/`)

We will create the main list template and partials for the table, form, and delete confirmation, suitable for HTMX interactions.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Return Quality Notes - Search</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'materialreturnqualitynote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New MRQN
        </button>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <div x-data="{ searchType: 'Select', mqnValue: '', empNameValue: '', showMqn: true, showEmp: false, showSuggestions: false }" class="space-y-4">
            <div class="flex items-center space-x-4">
                <label for="drpfield" class="text-gray-700 font-medium">Search By:</label>
                <select x-model="searchType" @change="showMqn = (searchType === '0' || searchType === '1' || searchType === 'Select'); showEmp = (searchType === '2');" class="block w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" id="drpfield">
                    <option value="Select">Select</option>
                    <option value="0">MRQN No</option>
                    <option value="1">MRN No</option>
                    <option value="2">Employee Name</option>
                </select>

                <div x-show="showMqn" class="relative flex-grow">
                    <input x-model="mqnValue" type="text" placeholder="Enter MRQN/MRN No." class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" id="txtMqnNo">
                </div>

                <div x-show="showEmp" class="relative flex-grow">
                    <input x-model="empNameValue" type="text" placeholder="Enter Employee Name" class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm" id="txtEmpName"
                           hx-get="{% url 'materialreturnqualitynote_autocomplete_employee' %}"
                           hx-trigger="keyup changed delay:300ms, focus"
                           hx-target="#employee-suggestions"
                           hx-indicator="#autocomplete-loading"
                           hx-swap="innerHTML"
                           @focus="showSuggestions = true"
                           @blur="setTimeout(() => showSuggestions = false, 100)"
                           x-ref="empInput">
                    <div id="employee-suggestions" x-show="showSuggestions && empNameValue.length > 0" x-cloak class="absolute bg-white border border-gray-300 rounded shadow-lg z-10 w-full mt-1 max-h-48 overflow-y-auto">
                        <!-- Suggestions loaded here via HTMX -->
                    </div>
                    <span id="autocomplete-loading" class="htmx-indicator ml-2 text-gray-500">Loading...</span>
                </div>

                <button type="button" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-150 ease-in-out" 
                        hx-get="{% url 'materialreturnqualitynote_table' %}"
                        hx-target="#materialreturnqualitynoteTable-container"
                        hx-swap="innerHTML"
                        :hx-vals="`{'search_type': searchType, 'search_value': mqnValue, 'employee_name': empNameValue}`">
                    Search
                </button>
            </div>
        </div>
    </div>
    
    <div id="materialreturnqualitynoteTable-container"
         hx-trigger="load, refreshMaterialReturnQualityNoteList from:body"
         hx-get="{% url 'materialreturnqualitynote_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Material Return Quality Notes...</p>
        </div>
    </div>
    
    <!-- Modal for forms (add/edit/delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 flex items-center justify-center p-4 z-50 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Modal content loaded via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-md text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js components needed here, x-data handles it.
        // But Alpine.js is ready for use if more complex interactivity is required.
    });

    // Handle click on autocomplete suggestion to populate input
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'employee-suggestions') {
            event.detail.target.querySelectorAll('li').forEach(item => {
                item.addEventListener('click', function() {
                    const empNameInput = document.getElementById('txtEmpName');
                    // Get the full string "Name [ID]"
                    const selectedValue = this.textContent.trim(); 
                    empNameInput.value = selectedValue;
                    
                    // Update Alpine.js model value directly if available
                    if (empNameInput.__alpine) {
                        empNameInput.__alpine.scope.empNameValue = selectedValue;
                    }
                    
                    // Hide suggestions after selection
                    document.getElementById('employee-suggestions').classList.add('hidden');
                    // Optionally trigger a search immediately after selection
                    // document.querySelector('button[hx-vals]').click(); 
                });
            });
        }
    });

    // Handle messages (Django messages framework)
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.xhr.getResponseHeader('HX-Trigger')) {
            const triggers = JSON.parse(event.detail.xhr.getResponseHeader('HX-Trigger'));
            if (triggers && triggers.messages) {
                // Assuming a global way to display messages, e.g., Alpine component
                // This would be handled by your base.html or a dedicated message component
                // For demonstration, logging to console:
                console.log('Django Messages:', triggers.messages);
                // Example of how to show:
                // Alpine.store('notifications').add(triggers.messages.text, triggers.messages.tags);
            }
        }
    });

    // Close modal on escape key
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape') {
            const modal = document.getElementById('modal');
            if (modal && modal.classList.contains('is-active')) {
                modal.classList.remove('is-active');
            }
        }
    });
</script>
{% endblock %}
```

#### `_materialreturnqualitynote_table.html` (Partial)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    {% if materialreturnqualitynotes %}
    <table id="materialreturnqualitynoteTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRQN No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRN No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FinYear</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in materialreturnqualitynotes %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.mrqn_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.mrn_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_formatted_sys_date }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_financial_year_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_generated_by_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    {# This button simulates the ASP.NET "Select" functionality to a details page #}
                    <button class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-150 ease-in-out"
                            hx-redirect="/materialreturnqualitynote/details/{{ obj.pk }}/">
                        Select
                    </button>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-xs ml-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'materialreturnqualitynote_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-xs ml-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'materialreturnqualitynote_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-8 text-gray-600">
        <p class="text-lg">No data to display!</p>
        <p class="text-sm mt-2">Try adjusting your search criteria.</p>
    </div>
    {% endif %}
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#materialreturnqualitynoteTable').DataTable({
            "paging": true,
            "pageLength": 20, // As per ASP.NET GridView PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "searching": true, // Enable built-in search
            "ordering": true,  // Enable sorting
            "info": true,      // Show info (e.g., "Showing 1 to 10 of 57 entries")
            "autoWidth": false // Disable auto-width for better responsive behavior
        });
    });
</script>
```

#### `_materialreturnqualitynote_form.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-medium text-gray-900 mb-6">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-loading">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 text-red-600">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
        <div id="form-loading" class="htmx-indicator ml-4 text-gray-500">Saving...</div>
    </form>
</div>
```

#### `_materialreturnqualitynote_confirm_delete.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-medium text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Material Return Quality Note 
        <span class="font-semibold text-red-600">'{{ object.mrqn_no }}'</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-loading">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-sm transition duration-150 ease-in-out">
                Confirm Delete
            </button>
        </div>
        <div id="delete-loading" class="htmx-indicator ml-4 text-gray-500">Deleting...</div>
    </form>
</div>
```

#### `_employee_suggestions.html` (Partial for autocomplete)

```html
{% if employees %}
    <ul class="list-none p-0 m-0">
        {% for employee_str in employees %}
        <li class="p-2 hover:bg-blue-100 cursor-pointer border-b border-gray-200 last:border-b-0 text-sm" data-value="{{ employee_str }}">
            {{ employee_str }}
        </li>
        {% endfor %}
    </ul>
{% else %}
    <div class="p-2 text-gray-500 text-sm">No suggestions found.</div>
{% endif %}
```

### 4.5 URLs (`qualitycontrol/urls.py`)

Define the URL patterns for the views, including specific paths for HTMX partials and the autocomplete functionality.

```python
from django.urls import path
from .views import (
    MaterialReturnQualityNoteListView, 
    MaterialReturnQualityNoteTablePartialView,
    MaterialReturnQualityNoteCreateView, 
    MaterialReturnQualityNoteUpdateView, 
    MaterialReturnQualityNoteDeleteView,
    EmployeeAutocompleteView
)

urlpatterns = [
    # Main list page for MRQN
    path('materialreturnqualitynote/', MaterialReturnQualityNoteListView.as_view(), name='materialreturnqualitynote_list'),
    
    # HTMX endpoint for the DataTables table content
    path('materialreturnqualitynote/table/', MaterialReturnQualityNoteTablePartialView.as_view(), name='materialreturnqualitynote_table'),
    
    # HTMX endpoint for employee autocomplete suggestions
    path('materialreturnqualitynote/autocomplete_employee/', EmployeeAutocompleteView.as_view(), name='materialreturnqualitynote_autocomplete_employee'),

    # CRUD operations (assumed to be modal interactions via HTMX)
    path('materialreturnqualitynote/add/', MaterialReturnQualityNoteCreateView.as_view(), name='materialreturnqualitynote_add'),
    path('materialreturnqualitynote/edit/<int:pk>/', MaterialReturnQualityNoteUpdateView.as_view(), name='materialreturnqualitynote_edit'),
    path('materialreturnqualitynote/delete/<int:pk>/', MaterialReturnQualityNoteDeleteView.as_view(), name='materialreturnqualitynote_delete'),

    # Placeholder for the details page (as per ASP.NET redirect)
    path('materialreturnqualitynote/details/<int:pk>/', lambda request, pk: HttpResponse(f"<h1>Details Page for MRQN ID: {pk}</h1><p>This would be MaterialReturnQualityNote_MRQN_Edit_Details.aspx equivalent.</p>"), name='materialreturnqualitynote_details'),
]
```

### 4.6 Tests (`qualitycontrol/tests.py`)

Comprehensive tests for models and views, ensuring functionality and maintainability.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from unittest.mock import patch

from .models import MaterialReturnQualityNote, FinancialYear, OfficeStaff

class QualityControlModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create FinancialYear and OfficeStaff data first as they are foreign keys
        cls.fin_year_2023 = FinancialYear.objects.create(id=2023, year_display='2023-2024', company_id=1)
        cls.fin_year_2024 = FinancialYear.objects.create(id=2024, year_display='2024-2025', company_id=1)
        
        cls.employee1 = OfficeStaff.objects.create(id=101, name='John Doe', title='Mr', company_id=1)
        cls.employee2 = OfficeStaff.objects.create(id=102, name='Jane Smith', title='Ms', company_id=1)

        # Create MaterialReturnQualityNote test data
        cls.mrqn1 = MaterialReturnQualityNote.objects.create(
            id=1, sys_date=datetime(2023, 10, 26, 10, 0, 0), mrn_no='MRN/001/23', mrqn_no='MRQN/001/23',
            financial_year=cls.fin_year_2023, generated_by_employee=cls.employee1, company_id=1
        )
        cls.mrqn2 = MaterialReturnQualityNote.objects.create(
            id=2, sys_date=datetime(2024, 1, 15, 11, 30, 0), mrn_no='MRN/002/24', mrqn_no='MRQN/002/24',
            financial_year=cls.fin_year_2024, generated_by_employee=cls.employee2, company_id=1
        )
        cls.mrqn3 = MaterialReturnQualityNote.objects.create(
            id=3, sys_date=datetime(2024, 3, 1, 9, 0, 0), mrn_no='MRN/003/24', mrqn_no='MRQN/003/24',
            financial_year=cls.fin_year_2024, generated_by_employee=cls.employee1, company_id=1
        )
  
    def test_mrqn_creation(self):
        obj = MaterialReturnQualityNote.objects.get(id=1)
        self.assertEqual(obj.mrqn_no, 'MRQN/001/23')
        self.assertEqual(obj.mrn_no, 'MRN/001/23')
        self.assertEqual(obj.financial_year, self.fin_year_2023)
        self.assertEqual(obj.generated_by_employee, self.employee1)
        
    def test_mrqn_formatted_sys_date(self):
        obj = MaterialReturnQualityNote.objects.get(id=1)
        self.assertEqual(obj.get_formatted_sys_date(), '26/10/2023')

    def test_mrqn_generated_by_display(self):
        obj = MaterialReturnQualityNote.objects.get(id=1)
        self.assertEqual(obj.get_generated_by_display(), 'Mr. John Doe')
        obj2 = MaterialReturnQualityNote.objects.get(id=2)
        self.assertEqual(obj2.get_generated_by_display(), 'Ms. Jane Smith')

    def test_mrqn_financial_year_display(self):
        obj = MaterialReturnQualityNote.objects.get(id=1)
        self.assertEqual(obj.get_financial_year_display(), '2023-2024')

    def test_mrqn_is_current_financial_year(self):
        obj = MaterialReturnQualityNote.objects.get(id=2)
        self.assertTrue(obj.is_current_financial_year(2024))
        self.assertFalse(obj.is_current_financial_year(2023))

    def test_financial_year_str(self):
        self.assertEqual(str(self.fin_year_2023), '2023-2024')

    def test_office_staff_str(self):
        self.assertEqual(str(self.employee1), 'Mr. John Doe')
        self.assertEqual(str(self.employee2), 'Ms. Jane Smith')

class MaterialReturnQualityNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.fin_year_2023 = FinancialYear.objects.create(id=2023, year_display='2023-2024', company_id=1)
        cls.fin_year_2024 = FinancialYear.objects.create(id=2024, year_display='2024-2025', company_id=1)
        cls.employee1 = OfficeStaff.objects.create(id=101, name='John Doe', title='Mr', company_id=1)
        cls.employee2 = OfficeStaff.objects.create(id=102, name='Jane Smith', title='Ms', company_id=1)
        
        cls.mrqn1 = MaterialReturnQualityNote.objects.create(
            id=1, sys_date=datetime(2023, 10, 26, 10, 0, 0), mrn_no='MRN/001/23', mrqn_no='MRQN/001/23',
            financial_year=cls.fin_year_2023, generated_by_employee=cls.employee1, company_id=1
        )
        cls.mrqn2 = MaterialReturnQualityNote.objects.create(
            id=2, sys_date=datetime(2024, 1, 15, 11, 30, 0), mrn_no='MRN/002/24', mrqn_no='MRQN/002/24',
            financial_year=cls.fin_year_2024, generated_by_employee=cls.employee2, company_id=1
        )
        cls.mrqn3 = MaterialReturnQualityNote.objects.create(
            id=3, sys_date=datetime(2024, 3, 1, 9, 0, 0), mrn_no='MRN/003/24', mrqn_no='MRQN/003/24',
            financial_year=cls.fin_year_2024, generated_by_employee=cls.employee1, company_id=1
        )
    
    def setUp(self):
        self.client = Client()
        # Mock session variables to simulate ASP.NET behavior
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024 # Current financial year for tests
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('materialreturnqualitynote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/materialreturnqualitynote/list.html')
        # Initial list view should not contain data directly, but the container for HTMX
        self.assertContains(response, 'id="materialreturnqualitynoteTable-container"')
        self.assertNotContains(response, 'MRQN/001/23') # Data not loaded directly by list view

    def test_table_partial_view_load_all(self):
        response = self.client.get(reverse('materialreturnqualitynote_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_table.html')
        self.assertContains(response, 'MRQN/001/23')
        self.assertContains(response, 'MRQN/002/24')
        self.assertContains(response, 'MRQN/003/24')
        self.assertEqual(response.context['materialreturnqualitynotes'].count(), 3)

    def test_table_partial_view_search_mrqn_no(self):
        response = self.client.get(reverse('materialreturnqualitynote_table'), {
            'search_type': '0', 'search_value': 'MRQN/001'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRQN/001/23')
        self.assertNotContains(response, 'MRQN/002/24')
        self.assertEqual(response.context['materialreturnqualitynotes'].count(), 1)

    def test_table_partial_view_search_mrn_no(self):
        response = self.client.get(reverse('materialreturnqualitynote_table'), {
            'search_type': '1', 'search_value': 'MRN/002'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRQN/002/24')
        self.assertNotContains(response, 'MRQN/001/23')
        self.assertEqual(response.context['materialreturnqualitynotes'].count(), 1)

    def test_table_partial_view_search_employee_name_by_id_format(self):
        response = self.client.get(reverse('materialreturnqualitynote_table'), {
            'search_type': '2', 'employee_name': 'John Doe [101]'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRQN/001/23') # John Doe's MRQN
        self.assertContains(response, 'MRQN/003/24') # John Doe's MRQN
        self.assertNotContains(response, 'MRQN/002/24') # Jane Smith's MRQN
        self.assertEqual(response.context['materialreturnqualitynotes'].count(), 2)

    def test_table_partial_view_search_employee_name_by_exact_name(self):
        # Test fallback to exact name if ID parsing fails or if only name is passed
        response = self.client.get(reverse('materialreturnqualitynote_table'), {
            'search_type': '2', 'employee_name': 'Jane Smith'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'MRQN/002/24')
        self.assertEqual(response.context['materialreturnqualitynotes'].count(), 1)


    def test_autocomplete_employee_view(self):
        response = self.client.get(reverse('materialreturnqualitynote_autocomplete_employee'), {
            'q': 'john'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'John Doe [101]')
        self.assertNotContains(response, 'Jane Smith')
        self.assertTemplateUsed(response, 'qualitycontrol/materialreturnqualitynote/_employee_suggestions.html')

    def test_create_view_get(self):
        response = self.client.get(reverse('materialreturnqualitynote_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Material Return Quality Note')
        
    @patch('qualitycontrol.models.MaterialReturnQualityNote.objects.create', wraps=MaterialReturnQualityNote.objects.create)
    def test_create_view_post_success(self, mock_create):
        new_mrqn_data = {
            'sys_date': '2024-05-10', 
            'mrn_no': 'MRN/NEW/005', 
            'mrqn_no': 'MRQN/NEW/005',
            'financial_year': self.fin_year_2024.id, 
            'generated_by_employee': self.employee1.id, 
            'company_id': 1
        }
        response = self.client.post(reverse('materialreturnqualitynote_add'), new_mrqn_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshMaterialReturnQualityNoteList', response.headers['HX-Trigger'])
        self.assertTrue(MaterialReturnQualityNote.objects.filter(mrqn_no='MRQN/NEW/005').exists())
        mock_create.assert_called_once()

    def test_create_view_post_invalid(self):
        # Missing required field mrqn_no
        invalid_data = {
            'sys_date': '2024-05-10', 
            'mrn_no': 'MRN/INVALID/006', 
            'mrqn_no': '', # Invalid
            'financial_year': self.fin_year_2024.id, 
            'generated_by_employee': self.employee1.id, 
            'company_id': 1
        }
        response = self.client.post(reverse('materialreturnqualitynote_add'), invalid_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_form.html')
        self.assertContains(response, 'This field is required.') # Or custom validation error
        self.assertFalse(MaterialReturnQualityNote.objects.filter(mrqn_no='').exists()) # Object not created

    def test_update_view_get(self):
        obj = MaterialReturnQualityNote.objects.get(id=1)
        response = self.client.get(reverse('materialreturnqualitynote_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Material Return Quality Note')
        self.assertContains(response, 'MRQN/001/23') # Check if form pre-populates

    def test_update_view_post_success(self):
        obj = MaterialReturnQualityNote.objects.get(id=1)
        updated_data = {
            'sys_date': obj.sys_date.strftime('%Y-%m-%d'), 
            'mrn_no': 'MRN/UPDATED', 
            'mrqn_no': obj.mrqn_no,
            'financial_year': obj.financial_year.id, 
            'generated_by_employee': obj.generated_by_employee.id, 
            'company_id': obj.company_id
        }
        response = self.client.post(reverse('materialreturnqualitynote_edit', args=[obj.id]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshMaterialReturnQualityNoteList', response.headers['HX-Trigger'])
        obj.refresh_from_db()
        self.assertEqual(obj.mrn_no, 'MRN/UPDATED')

    def test_delete_view_get(self):
        obj = MaterialReturnQualityNote.objects.get(id=1)
        response = self.client.get(reverse('materialreturnqualitynote_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/materialreturnqualitynote/_materialreturnqualitynote_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'MRQN/001/23')

    def test_delete_view_post_success(self):
        obj_to_delete = MaterialReturnQualityNote.objects.get(id=1)
        response = self.client.post(reverse('materialreturnqualitynote_delete', args=[obj_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertIn('refreshMaterialReturnQualityNoteList', response.headers['HX-Trigger'])
        self.assertFalse(MaterialReturnQualityNote.objects.filter(id=obj_to_delete.id).exists())

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX for dynamic updates:**
    *   The main list page (`list.html`) uses `hx-get` on `onload` and custom `refreshMaterialReturnQualityNoteList` trigger to load the table content from `materialreturnqualitynote_table` URL.
    *   The search button uses `hx-get` to re-fetch the table content with updated parameters, ensuring only the table area is refreshed.
    *   Add/Edit/Delete buttons use `hx-get` to load the respective forms/confirmations into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) are set to `hx-swap="none"` and trigger `HX-Trigger: refreshMaterialReturnQualityNoteList` headers, signaling the list to refresh without a full page reload.
    *   Employee autocomplete uses `hx-get` to fetch suggestions on `keyup` into a dedicated `div`.
    *   "Select" action uses `hx-redirect` to navigate to the details page, mimicking the ASP.NET `Response.Redirect`.
*   **Alpine.js for UI state management:**
    *   `x-data` is used to manage the visibility of search input fields (`mqnValue`, `empNameValue`) based on the `searchType` dropdown selection (`drpfield`).
    *   It also manages the visibility of the autocomplete suggestions.
    *   `x-model` binds input values to Alpine.js data properties, allowing `hx-vals` to dynamically include search parameters in HTMX requests.
*   **DataTables for list views:**
    *   The `_materialreturnqualitynote_table.html` partial includes the `<table id="materialreturnqualitynoteTable">` tag.
    *   A `<script>` block within this partial ensures `$('#materialreturnqualitynoteTable').DataTable()` is initialized after the HTMX content is swapped into the DOM. This provides client-side sorting, searching, and pagination.
*   **DRY Template Inheritance:**
    *   All templates extend `core/base.html` to inherit common layout, CDN links (for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables), and other global components. The `base.html` itself is *not* included in this output as per instructions.

## Final Notes

*   **Business Value:** This modernized Django solution provides a significantly improved user experience with dynamic, partial page updates (HTMX), faster load times, and a more interactive interface than the traditional ASP.NET PostBack model. It centralizes business logic in models, making the application easier to understand, test, and maintain. The use of standard, modern web technologies (Django, HTMX, Alpine.js, Tailwind CSS) ensures long-term viability and reduces reliance on proprietary Microsoft stacks, opening up recruitment to a broader talent pool.
*   **Automation Focus:** The plan details specific file structures and code snippets that can be generated or transformed automatically using AI tools. For instance, schema extraction, model generation, basic view structure, and form creation can be largely automated, with human experts refining the business logic within models and setting up detailed test cases.
*   **Scalability & Maintainability:** The 'Fat Model, Thin View' architecture with clear separation of concerns (no HTML in views, no business logic in templates) significantly enhances maintainability. DataTables handles client-side performance for large datasets, and HTMX/Alpine.js minimize server load by avoiding full page reloads.
*   **Security:** Django's built-in CSRF protection, secure session management, and robust ORM features inherently provide stronger security compared to older ASP.NET Web Forms patterns that often exposed direct SQL or allowed for common vulnerabilities.
*   **Future Enhancements:** The modular Django design makes it easy to add more features, integrate with other systems, or evolve the UI (e.g., more complex charting or reporting) without disrupting existing functionality. The detailed `_Details` page identified in the ASP.NET code would be the next logical step to implement in Django.