## ASP.NET to Django Conversion Script: Goods Rejection Note

This document outlines a strategic plan for migrating the ASP.NET Goods Rejection Note (GRN) module to a modern Django application. The focus is on leveraging AI-assisted automation, adhering to a "Fat Model, Thin View" architecture, and utilizing modern web technologies like HTMX and Alpine.js for highly interactive user interfaces without traditional JavaScript complexities.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:** The ASP.NET code-behind's `loadData` method performs complex joins across multiple tables to generate the data displayed in the `GridView`. The primary table appears to be `tblQc_MaterialQuality_Master`, and the displayed columns are derived from it and several related tables.

**Inferred Tables and Key Columns:**

*   **`tblQc_MaterialQuality_Master` (Main Entity: Goods Rejection Note)**
    *   `Id` (Primary Key)
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `GQNNo` (Goods Quality Note Number)
    *   `GRRNo` (Goods Return Register Number)
    *   `SysDate` (System Date / Creation Date)
    *   `GRRId` (Foreign Key to `tblinv_MaterialReceived_Master.Id`)
    *   `CompId` (Foreign Key to a Company/Company ID table, derived from session)

*   **`tblQc_MaterialQuality_Details`**
    *   `MId` (Foreign Key to `tblQc_MaterialQuality_Master.Id`)
    *   `RejectedQty` (Decimal/Float) - Used for filtering `RejectedQty > 0`.

*   **`tblinv_MaterialReceived_Master`**
    *   `Id` (Primary Key)
    *   `GINNo` (Goods Inward Note Number)
    *   `GRRNo` (Goods Return Register Number - used for linking)
    *   `GINId` (Integer ID, used to link to `tblInv_Inward_Master`)
    *   `CompId`

*   **`tblInv_Inward_Master`**
    *   `Id` (Primary Key, linked via `GINId` from `tblinv_MaterialReceived_Master`)
    *   `PONo` (Purchase Order Number)
    *   `ChallanNo`
    *   `ChallanDate`
    *   `POId` (Integer ID, used to link to `tblMM_PO_Master`)
    *   `CompId`

*   **`tblMM_PO_Master`**
    *   `Id` (Primary Key, linked via `POId` from `tblInv_Inward_Master`)
    *   `SupplierId` (Foreign Key to `tblMM_Supplier_master`)
    *   `CompId`

*   **`tblMM_Supplier_master`**
    *   `SupplierId` (Primary Key)
    *   `SupplierName`
    *   `CompId`

*   **`tblFinancial_master`**
    *   `FinYearId` (Primary Key)
    *   `FinYear`

*   **Company Table (Inferred)**
    *   `CompId` (Primary Key) - Used as `Session["compid"]`.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Analysis:**

*   **Read (Primary Operation):** The core functionality is displaying a list of "Goods Rejection Notes" (`GridView2`). This involves a highly complex read operation that joins multiple tables, applies filters (e.g., `RejectedQty > 0`), and dynamically filters based on user input (search type, search term). Pagination is also handled server-side.
*   **Search/Filter:** Implemented using `DropDownList1` (search type), `Txtfield` (search term), `txtSupplier` (supplier autocomplete), and `btnSearch`. The `DropDownList1_SelectedIndexChanged` and `btnSearch_Click` events trigger `loadData` with different parameters.
*   **Select/Redirect:** The `GridView2_RowCommand` event with `CommandName="Sel"` extracts data from the selected row and redirects to `GoodsRejection_GRN_Print_Details.aspx` with query parameters. This implies a "Read One / Detail" functionality.
*   **Autocomplete:** The `sql` WebMethod provides supplier name suggestions for `txtSupplier`.
*   **Validation:** No explicit validation logic is visible in the provided snippets, but implied data consistency checks from database operations.

**Key Business Logic:**

*   Only display goods rejection notes where `RejectedQty` is greater than 0 in `tblQc_MaterialQuality_Details`.
*   Filter by `CompId` and `FinYearId` from session variables.
*   Dynamic filtering based on user-selected criteria (GQN No, GRR No, PO No, Supplier Name).
*   Date formatting (`fun.FromDateDMY`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**

*   **`DropDownList1` (Search Type Selector):** A dropdown for selecting the field to search by (Supplier Name, GQN No, GRR No, PO No).
*   **`Txtfield` (General Search Input):** A textbox for entering GQN No, GRR No, or PO No. Its visibility is toggled.
*   **`txtSupplier` (Supplier Autocomplete Input):** A textbox for entering Supplier Name, with an `AutoCompleteExtender`. Its visibility is toggled.
*   **`btnSearch` (Search Button):** Triggers the filtering.
*   **`GridView2` (Data Display Table):**
    *   Displays Goods Rejection Note details in a tabular format.
    *   Includes columns: SN, Select (LinkButton), Id (hidden), Fin Year Id (hidden), Fin Year, GQN No, Date, GRR No, GIN No, PONo, Name of Supplier, Sup Id (hidden), Challan No, Challan Date.
    *   Supports pagination (`AllowPaging="True"`, `PageSize="20"`).
    *   "Select" `LinkButton` for viewing details.

**UI Interactions to Replicate:**

*   Toggling visibility of search input fields (`Txtfield` vs `txtSupplier`) based on `DropDownList1` selection. This will be handled by Alpine.js.
*   Dynamic data loading and refreshing of the table (`GridView2`) based on search, pagination, and dropdown changes. This will be handled by HTMX for partial updates.
*   Supplier autocomplete in `txtSupplier` will be replaced by an HTMX-driven typeahead.
*   Detail view navigation on "Select".

### Step 4: Generate Django Code

We will create a new Django application named `inventory_transactions`. All files will reside within this application.

**Application Name:** `inventory_transactions`
**Main Model Name:** `MaterialQualityMaster` (representing `tblQc_MaterialQuality_Master`)
**Model for displayed data:** `GoodsRejectionEntry` (This will be a pseudo-model or a data structure generated by the `MaterialQualityManager` for easier display in templates, rather than a direct database table)

---

#### 4.1 Models (`inventory_transactions/models.py`)

**Task:** Create Django models based on the identified database schema. We'll define all relevant models to reflect the complex data relationships implied by the `loadData` method. A custom manager will be implemented for `MaterialQualityMaster` to encapsulate the complex data retrieval and filtering logic.

```python
# inventory_transactions/models.py
from django.db import models
from django.db.models import F, Q, Sum, Case, When, Value, CharField
from django.db.models.functions import Coalesce

# Placeholder models for related tables as inferred from SQL queries.
# These models are assumed to exist in the database and are managed by Django.

class Company(models.Model):
    id = models.AutoField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompName', max_length=100) # Inferred column name
    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

class FinancialYear(models.Model):
    id = models.AutoField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)
    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

class Supplier(models.Model):
    id = models.AutoField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    # Assuming supplier is linked to a company, though not explicitly in ASP.NET
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='suppliers')
    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'
    def __str__(self):
        return self.supplier_name

# Note: The ASP.NET code shows a complex chain of IDs (GRRId -> GINId -> POId).
# We'll model these as foreign keys where a logical one-to-one or one-to-many relationship
# can be inferred for efficient ORM queries. If the actual DB schema is different,
# more complex joins or raw SQL might be necessary.
class MaterialReceivedMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    gin_id_link = models.IntegerField(db_column='GINId') # This is the ID used to link to InwardMaster
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='received_materials')
    # Add an inferred OneToOneField to InwardMaster based on GINId
    inward_master = models.OneToOneField('InwardMaster', on_delete=models.DO_NOTHING, to_field='id', related_name='material_received_master', db_column='GINId_FK_Hack', null=True, blank=True) # Placeholder for the hacky GINId link
    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received'
        verbose_name_plural = 'Materials Received'
    def __str__(self):
        return self.gin_no

class InwardMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    challan_no = models.CharField(db_column='ChallanNo', max_length=50)
    challan_date = models.DateField(db_column='ChallanDate')
    po_id_link = models.IntegerField(db_column='POId') # This is the ID used to link to PurchaseOrderMaster
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='inward_masters')
    # Add an inferred OneToOneField to PurchaseOrderMaster based on POId
    po_master = models.OneToOneField('PurchaseOrderMaster', on_delete=models.DO_NOTHING, to_field='id', related_name='inward_master', db_column='POId_FK_Hack', null=True, blank=True) # Placeholder for the hacky POId link
    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward'
        verbose_name_plural = 'Inwards'
    def __str__(self):
        return self.po_no

class PurchaseOrderMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', related_name='purchase_orders')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='purchase_orders_by_company')
    po_no = models.CharField(db_column='PONo', max_length=50, unique=True, null=True) # Assuming PONo is on master, for simpler lookup
    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'
    def __str__(self):
        return self.po_no or f"PO {self.id}"

class MaterialQualityDetail(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    material_quality_master = models.ForeignKey('MaterialQualityMaster', on_delete=models.CASCADE, db_column='MId', related_name='details')
    rejected_qty = models.DecimalField(db_column='RejectedQty', max_digits=10, decimal_places=3)
    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Material Quality Detail'
        verbose_name_plural = 'Material Quality Details'

class MaterialQualityManager(models.Manager):
    """
    Custom manager for MaterialQualityMaster to handle complex data retrieval
    and filtering as seen in the ASP.NET loadData method.
    """
    def get_goods_rejection_list(self, company_id, fin_year_id, search_type=None, search_term=None, supplier_id=None):
        # Base queryset filtering by company and financial year
        queryset = self.get_queryset().filter(
            company_id=company_id,
            financial_year_id__lte=fin_year_id # Filter for FinYearId <= session finyear
        )

        # Annotate with total rejected quantity from details and filter based on it.
        # This handles the 'RejectedQty > 0' check from the ASP.NET code.
        queryset = queryset.annotate(
            _total_rejected_qty=Sum('details__rejected_qty', filter=Q(details__rejected_qty__gt=0))
        ).filter(_total_rejected_qty__gt=0)

        # Apply conditional filtering based on search parameters
        if search_term:
            if search_type == '1':  # GQN No
                queryset = queryset.filter(gqn_no__icontains=search_term)
            elif search_type == '2':  # GRR No (from MaterialReceivedMaster)
                queryset = queryset.filter(grr_master__grr_no__icontains=search_term)
            elif search_type == '3':  # PO No (chain through InwardMaster, PurchaseOrderMaster)
                # This assumes MaterialReceivedMaster.inward_master and InwardMaster.po_master are correctly linked
                # If these are not direct FKs, this might need a RawSQL or custom joins.
                queryset = queryset.filter(grr_master__inward_master__po_master__po_no__icontains=search_term)

        if search_type == '0' and supplier_id: # Supplier Name search
            # This assumes chain through MaterialReceivedMaster, InwardMaster, PurchaseOrderMaster, Supplier
            queryset = queryset.filter(grr_master__inward_master__po_master__supplier_id=supplier_id)

        # Select related fields for efficient data retrieval to avoid N+1 queries.
        # The chain here mirrors the complex lookups in the ASP.NET C# code.
        queryset = queryset.select_related(
            'financial_year',
            'grr_master', # MaterialReceivedMaster
            'grr_master__inward_master', # InwardMaster linked via MaterialReceivedMaster.GINId
            'grr_master__inward_master__po_master', # PurchaseOrderMaster linked via InwardMaster.POId
            'grr_master__inward_master__po_master__supplier' # Supplier linked via PurchaseOrderMaster.SupplierId
        )

        return queryset.order_by('-id') # Order by Id Desc as in ASP.NET

class MaterialQualityMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', related_name='material_quality_notes')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', related_name='material_quality_notes')
    gqn_no = models.CharField(db_column='GQNNo', max_length=50)
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    sys_date = models.DateField(db_column='SysDate')
    # Link to MaterialReceivedMaster based on GRRId.
    grr_master = models.ForeignKey(MaterialReceivedMaster, on_delete=models.DO_NOTHING, db_column='GRRId', related_name='material_quality_entries')

    objects = MaterialQualityManager() # Attach our custom manager

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'
        verbose_name = 'Goods Rejection Note'
        verbose_name_plural = 'Goods Rejection Notes'

    def __str__(self):
        return self.gqn_no or f"GRN-{self.id}"

    # Properties to expose derived fields for template display, similar to ASP.NET GridView
    @property
    def fin_year_display(self):
        return self.financial_year.fin_year if self.financial_year else ''

    @property
    def gin_no_display(self):
        return self.grr_master.gin_no if self.grr_master else ''

    @property
    def po_no_display(self):
        # Access through the linked chain: grr_master -> inward_master -> po_master
        if self.grr_master and hasattr(self.grr_master, 'inward_master') and self.grr_master.inward_master and hasattr(self.grr_master.inward_master, 'po_master') and self.grr_master.inward_master.po_master:
            return self.grr_master.inward_master.po_master.po_no
        return ''

    @property
    def supplier_display(self):
        # Access through the linked chain to get supplier name and ID
        if self.grr_master and hasattr(self.grr_master, 'inward_master') and self.grr_master.inward_master and \
           hasattr(self.grr_master.inward_master, 'po_master') and self.grr_master.inward_master.po_master and \
           hasattr(self.grr_master.inward_master.po_master, 'supplier') and self.grr_master.inward_master.po_master.supplier:
            sup = self.grr_master.inward_master.po_master.supplier
            return f"{sup.supplier_name} [{sup.id}]"
        return ''

    @property
    def sup_id_display(self):
        # Access through the linked chain to get supplier ID
        if self.grr_master and hasattr(self.grr_master, 'inward_master') and self.grr_master.inward_master and \
           hasattr(self.grr_master.inward_master, 'po_master') and self.grr_master.inward_master.po_master and \
           hasattr(self.grr_master.inward_master.po_master, 'supplier') and self.grr_master.inward_master.po_master.supplier:
            return self.grr_master.inward_master.po_master.supplier.id
        return ''

    @property
    def challan_no_display(self):
        if self.grr_master and hasattr(self.grr_master, 'inward_master') and self.grr_master.inward_master:
            return self.grr_master.inward_master.challan_no
        return ''

    @property
    def challan_date_display(self):
        if self.grr_master and hasattr(self.grr_master, 'inward_master') and self.grr_master.inward_master and self.grr_master.inward_master.challan_date:
            return self.grr_master.inward_master.challan_date.strftime('%d/%m/%Y')
        return ''

    @property
    def sys_date_display(self):
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''
```

---

#### 4.2 Forms (`inventory_transactions/forms.py`)

**Task:** Define Django forms. The ASP.NET code does not show forms for creating/updating `GoodsRejection` directly, only for searching. However, a typical Django application would have CRUD forms. For this migration, we will focus on the search form and a placeholder for potential future CRUD forms.

```python
# inventory_transactions/forms.py
from django import forms
from .models import MaterialQualityMaster, Supplier

class GoodsRejectionSearchForm(forms.Form):
    """
    Form for handling the search/filter inputs.
    This doesn't map directly to a model, but captures search criteria.
    """
    SEARCH_CHOICES = [
        ('Select', 'Select'),
        ('0', 'Supplier Name'),
        ('1', 'GQN No'),
        ('2', 'GRR No'),
        ('3', 'PO No'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                   'x-model': 'selectedSearchType', 'hx-get': 'hx-trigger="change delay:100ms" hx-target="#goodsRejectionTable-container" hx-swap="innerHTML" hx-include="#searchForm"'})
    )
    search_text_field = forms.CharField(
        max_length=255,
        required=False,
        label="Search Text",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                      'placeholder': 'Enter search term',
                                      'x-show': "selectedSearchType !== '0'"})
    )
    supplier_autocomplete = forms.CharField(
        max_length=255,
        required=False,
        label="Supplier",
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                                      'placeholder': 'Start typing supplier name...',
                                      'hx-get': '/inventory_transactions/suppliers/autocomplete/', # HTMX endpoint for autocomplete
                                      'hx-trigger': 'keyup changed delay:500ms, search',
                                      'hx-target': '#supplier-suggestions',
                                      'hx-swap': 'innerHTML',
                                      'x-show': "selectedSearchType === '0'",
                                      'x-model': 'supplierSearchText',
                                      '@input': 'selectedSupplierId = null'}) # Reset selected ID on input
    )
    # Hidden field to store selected supplier ID for search
    supplier_id_hidden = forms.IntegerField(
        required=False,
        widget=forms.HiddenInput(attrs={'x-model': 'selectedSupplierId'})
    )

    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        search_text = cleaned_data.get('search_text_field')
        supplier_id = cleaned_data.get('supplier_id_hidden')

        if search_type == '0' and not supplier_id:
            # If supplier name is selected, but no supplier is picked from autocomplete
            # This validation will be handled by the client-side selection from autocomplete.
            # For direct form submission (less likely with HTMX), this would be important.
            pass
        elif search_type in ['1', '2', '3'] and not search_text:
            # For GQN, GRR, PO search, require a search term
            # This validation is also typically client-side with HTMX forms
            pass
        return cleaned_data

# Placeholder for actual MaterialQualityMaster CRUD form
class MaterialQualityMasterForm(forms.ModelForm):
    class Meta:
        model = MaterialQualityMaster
        fields = ['gqn_no', 'grr_no', 'sys_date', 'financial_year', 'grr_master'] # Example fields
        widgets = {
            'gqn_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'grr_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'grr_master': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
```

---

#### 4.3 Views (`inventory_transactions/views.py`)

**Task:** Implement CRUD operations and search functionality using CBVs. We will have a main `ListView` to render the page, an HTMX-specific `ListView` for the table content, and other standard CRUD views (placeholder for now).

```python
# inventory_transactions/views.py
from django.views.generic import TemplateView, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import F, Q
from django.shortcuts import get_object_or_404
from .models import MaterialQualityMaster, Supplier, Company, FinancialYear
from .forms import GoodsRejectionSearchForm, MaterialQualityMasterForm
import logging

logger = logging.getLogger(__name__)

class GoodsRejectionListView(TemplateView):
    """
    Main view to render the Goods Rejection Notes search page.
    This view itself does not fetch data, it sets up the container for HTMX to fill.
    """
    template_name = 'inventory_transactions/goods_rejection/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['search_form'] = GoodsRejectionSearchForm(self.request.GET)
        # Dummy session values for CompId and FinYearId for demonstration.
        # In a real app, these would come from authentication/session.
        context['current_comp_id'] = self.request.session.get('compid', 1) # Default to 1
        context['current_fin_year_id'] = self.request.session.get('finyear', 2024) # Default to 2024
        return context

class GoodsRejectionTablePartialView(ListView):
    """
    HTMX-specific view to load and refresh the DataTables content.
    This replaces the complex 'loadData' method in ASP.NET.
    """
    model = MaterialQualityMaster
    template_name = 'inventory_transactions/goods_rejection/_goods_rejection_table.html'
    context_object_name = 'goods_rejections'

    def get_queryset(self):
        # Fetch session variables - crucial for filtering
        company_id = self.request.session.get('compid', 1)  # Default to 1
        fin_year_id = self.request.session.get('finyear', 2024) # Default to 2024

        # Extract search parameters from GET request
        search_form = GoodsRejectionSearchForm(self.request.GET)
        search_type = self.request.GET.get('search_type')
        search_term = self.request.GET.get('search_text_field')
        supplier_id = self.request.GET.get('supplier_id_hidden') # From the hidden field updated by Alpine.js

        # Use the custom manager to get the filtered and joined queryset
        queryset = MaterialQualityMaster.objects.get_goods_rejection_list(
            company_id, fin_year_id, search_type, search_term, supplier_id
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The queryset (self.object_list) is already filtered and annotated by get_queryset
        return context

class SupplierAutocompleteView(ListView):
    """
    HTMX endpoint for supplier autocomplete functionality.
    Returns HTML snippets for suggestions.
    """
    model = Supplier
    template_name = 'inventory_transactions/goods_rejection/_supplier_suggestions.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        query = self.request.GET.get('supplier_autocomplete', '')
        company_id = self.request.session.get('compid', 1) # Assuming CompId is needed for supplier filtering
        if query:
            return Supplier.objects.filter(
                supplier_name__icontains=query,
                company_id=company_id
            )[:10] # Limit suggestions to 10
        return Supplier.objects.none()

# Standard CRUD views (placeholder for potential future expansion of GRN module)
# The ASP.NET code shows only a list and redirect to print, not direct CRUD on GRN.
# However, a modern Django app would typically have these.

class GoodsRejectionCreateView(CreateView):
    model = MaterialQualityMaster
    form_class = MaterialQualityMasterForm
    template_name = 'inventory_transactions/goods_rejection/_goods_rejection_form.html'
    success_url = reverse_lazy('goods_rejection_list') # Redirect to list view

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass company_id and fin_year_id to the form for initial values or validation
        # context['form'].fields['company'].initial = self.request.session.get('compid', 1)
        # context['form'].fields['financial_year'].initial = self.request.session.get('finyear', 2024)
        return context

    def form_valid(self, form):
        # Set company and financial year from session, assuming they are part of the model
        form.instance.company_id = self.request.session.get('compid', 1)
        form.instance.financial_year_id = self.request.session.get('finyear', 2024)
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Rejection Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, HTMX will handle refresh via trigger
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionList'
                }
            )
        return response

class GoodsRejectionUpdateView(UpdateView):
    model = MaterialQualityMaster
    form_class = MaterialQualityMasterForm
    template_name = 'inventory_transactions/goods_rejection/_goods_rejection_form.html'
    success_url = reverse_lazy('goods_rejection_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Goods Rejection Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionList'
                }
            )
        return response

class GoodsRejectionDeleteView(DeleteView):
    model = MaterialQualityMaster
    template_name = 'inventory_transactions/goods_rejection/_goods_rejection_confirm_delete.html'
    success_url = reverse_lazy('goods_rejection_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Goods Rejection Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshGoodsRejectionList'
                }
            )
        return response

class GoodsRejectionDetailView(TemplateView):
    """
    View for displaying details of a selected Goods Rejection Note.
    This replaces the redirect to 'GoodsRejection_GRN_Print_Details.aspx'.
    """
    template_name = 'inventory_transactions/goods_rejection/detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        grn_id = self.kwargs['pk'] # ID from URL
        company_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2024)

        # Retrieve the specific GRN entry using the custom manager for comprehensive data
        grn_entry = MaterialQualityMaster.objects.get_goods_rejection_list(
            company_id, fin_year_id
        ).filter(id=grn_id).first()

        if not grn_entry:
            # Handle case where GRN is not found or doesn't meet rejection criteria
            raise Http404("Goods Rejection Note not found or not eligible for display.")

        context['goods_rejection'] = grn_entry
        # You would typically fetch related detail lines here as well if the print page shows them
        # context['goods_rejection_details'] = grn_entry.details.all()
        return context
```

---

#### 4.4 Templates

**Task:** Create templates for each view, including partials for HTMX. All templates will extend `core/base.html`.

**`inventory_transactions/goods_rejection/list.html`**
This is the main page for Goods Rejection Notes. It contains the search form and a container that HTMX will fill with the table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ selectedSearchType: 'Select', supplierSearchText: '', selectedSupplierId: null }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Goods Rejection Notes [GRN]</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'goods_rejection_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New GRN
        </button>
    </div>

    <form id="searchForm" class="bg-white p-6 rounded-lg shadow-md mb-6" hx-trigger="submit, change from:body" hx-target="#goodsRejectionTable-container" hx-swap="innerHTML">
        {% csrf_token %}
        <table class="w-full">
            <tr>
                <td class="style3 py-2">
                    <label for="{{ search_form.search_type.id_for_label }}" class="sr-only">{{ search_form.search_type.label }}</label>
                    {{ search_form.search_type }}
                </td>
            </tr>
            <tr>
                <td class="py-2">
                    <div x-show="selectedSearchType !== '0'" class="mb-4">
                        <label for="{{ search_form.search_text_field.id_for_label }}" class="sr-only">{{ search_form.search_text_field.label }}</label>
                        {{ search_form.search_text_field }}
                    </div>
                    <div x-show="selectedSearchType === '0'" class="mb-4 relative">
                        <label for="{{ search_form.supplier_autocomplete.id_for_label }}" class="sr-only">{{ search_form.supplier_autocomplete.label }}</label>
                        {{ search_form.supplier_autocomplete }}
                        <div id="supplier-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full mt-1 max-h-60 overflow-y-auto">
                            <!-- Autocomplete suggestions will be loaded here by HTMX -->
                        </div>
                        {{ search_form.supplier_id_hidden }} {# Hidden field for selected supplier ID #}
                    </div>
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox"
                        hx-get="{% url 'goods_rejection_table' %}"
                        hx-target="#goodsRejectionTable-container"
                        hx-swap="innerHTML"
                        hx-include="#searchForm">
                        Search
                    </button>
                </td>
            </tr>
        </table>
    </form>

    <div id="goodsRejectionTable-container"
         hx-trigger="load, refreshGoodsRejectionList from:body" {# Initial load and refresh trigger for HTMX #}
         hx-get="{% url 'goods_rejection_table' %}?{{ request.GET.urlencode }}" {# Pass initial search params #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Goods Rejection Notes...</p>
        </div>
    </div>

    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('goodsRejectionPage', () => ({
            selectedSearchType: 'Select',
            supplierSearchText: '',
            selectedSupplierId: null,
            init() {
                // Initialize search type from form data if present
                const initialSearchType = document.querySelector('#id_search_type').value;
                if (initialSearchType) {
                    this.selectedSearchType = initialSearchType;
                }
            }
        }));
    });
</script>
{% endblock %}
```

**`inventory_transactions/goods_rejection/_goods_rejection_table.html`**
This partial template contains the actual DataTables structure. It's loaded dynamically by HTMX.

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md p-4">
    {% if goods_rejections %}
    <table id="goodsRejectionTable" class="min-w-full bg-white yui-datatable-theme">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GQN No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GRR No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in goods_rejections %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200">
                    <button
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'goods_rejection_detail' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Select
                    </button>
                    {# Example for Edit/Delete if those were applicable to this list view directly #}
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'goods_rejection_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'goods_rejection_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.fin_year_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.gqn_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.sys_date_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.grr_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.gin_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.po_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.challan_no_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.challan_date_display }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <script>
    // DataTables initialization
    // This script block will run after HTMX inserts the table into the DOM.
    // Ensure jQuery and DataTables CDN are included in base.html
    $(document).ready(function() {
        $('#goodsRejectionTable').DataTable({
            "pageLength": 20, // Page size as in ASP.NET GridView
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 1] }, // Disable sorting on SN and Actions
                { "visible": false, "targets": [
                    // Corresponding hidden columns from ASP.NET
                    // Assuming Id, FinYearId, SupId are not displayed directly
                    // You might need to map exact column indices if they were truly hidden
                ] }
            ]
        });
    });
    </script>
    {% else %}
    <div class="text-center py-8">
        <p class="fontcss text-maroon text-lg">No data to display !</p>
    </div>
    {% endif %}
</div>
```

**`inventory_transactions/goods_rejection/_supplier_suggestions.html`**
Partial for supplier autocomplete suggestions.

```html
{% for supplier in suppliers %}
    <div class="p-2 hover:bg-gray-100 cursor-pointer text-gray-800 text-sm"
         hx-on:click="selectedSupplierId = {{ supplier.id }}; supplierSearchText = '{{ supplier.supplier_name }} [{{ supplier.id }}]'; hx.trigger('#searchForm', 'submit')">
        {{ supplier.supplier_name }} [{{ supplier.id }}]
    </div>
{% empty %}
    <div class="p-2 text-gray-500 text-sm">No suggestions</div>
{% endfor %}
```

**`inventory_transactions/goods_rejection/_goods_rejection_form.html`**
Form for adding/editing Material Quality Master records.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Goods Rejection Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" allows HX-Trigger to control UI updates #}
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Alpine.js to close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`inventory_transactions/goods_rejection/_goods_rejection_confirm_delete.html`**
Confirmation template for deleting Material Quality Master records.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Goods Rejection Note with GQN No: <strong>{{ object.gqn_no }}</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" allows HX-Trigger to control UI updates #}
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal"> {# Alpine.js to close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`inventory_transactions/goods_rejection/detail.html`**
Detail/Print view for a single Goods Rejection Note.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Goods Rejection Note Details: {{ goods_rejection.gqn_no }}</h3>
    <div class="space-y-4 text-gray-700">
        <p><strong>Fin Year:</strong> {{ goods_rejection.fin_year_display }}</p>
        <p><strong>GQN No:</strong> {{ goods_rejection.gqn_no }}</p>
        <p><strong>Date:</strong> {{ goods_rejection.sys_date_display }}</p>
        <p><strong>GRR No:</strong> {{ goods_rejection.grr_no }}</p>
        <p><strong>GIN No:</strong> {{ goods_rejection.gin_no_display }}</p>
        <p><strong>PO No:</strong> {{ goods_rejection.po_no_display }}</p>
        <p><strong>Name of Supplier:</strong> {{ goods_rejection.supplier_display }}</p>
        <p><strong>Challan No:</strong> {{ goods_rejection.challan_no_display }}</p>
        <p><strong>Challan Date:</strong> {{ goods_rejection.challan_date_display }}</p>
        {# Add other details from related models or if GRN has its own detailed fields #}
    </div>
    
    <div class="mt-6 flex items-center justify-end">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Close
        </button>
    </div>
</div>
```

---

#### 4.5 URLs (`inventory_transactions/urls.py`)

**Task:** Define URL patterns for the views.

```python
# inventory_transactions/urls.py
from django.urls import path
from .views import (
    GoodsRejectionListView, GoodsRejectionTablePartialView, SupplierAutocompleteView,
    GoodsRejectionCreateView, GoodsRejectionUpdateView, GoodsRejectionDeleteView,
    GoodsRejectionDetailView
)

urlpatterns = [
    path('goods_rejection/', GoodsRejectionListView.as_view(), name='goods_rejection_list'),
    path('goods_rejection/table/', GoodsRejectionTablePartialView.as_view(), name='goods_rejection_table'),
    path('goods_rejection/add/', GoodsRejectionCreateView.as_view(), name='goods_rejection_add'),
    path('goods_rejection/edit/<int:pk>/', GoodsRejectionUpdateView.as_view(), name='goods_rejection_edit'),
    path('goods_rejection/delete/<int:pk>/', GoodsRejectionDeleteView.as_view(), name='goods_rejection_delete'),
    path('goods_rejection/detail/<int:pk>/', GoodsRejectionDetailView.as_view(), name='goods_rejection_detail'),
    path('suppliers/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
]
```

---

#### 4.6 Tests (`inventory_transactions/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views.

```python
# inventory_transactions/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db.models import Sum
from datetime import date
from .models import (
    Company, FinancialYear, Supplier, MaterialReceivedMaster,
    InwardMaster, PurchaseOrderMaster, MaterialQualityDetail, MaterialQualityMaster
)

class ModelSetupMixin:
    """Helper mixin to set up common test data for models."""
    @classmethod
    def setUpTestData(cls):
        # Create common test data
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.fin_year_2023 = FinancialYear.objects.create(id=2023, fin_year='2023-2024')
        cls.fin_year_2024 = FinancialYear.objects.create(id=2024, fin_year='2024-2025')
        
        cls.supplier1 = Supplier.objects.create(id=101, supplier_name='Supplier A', company=cls.company)
        cls.supplier2 = Supplier.objects.create(id=102, supplier_name='Supplier B', company=cls.company)

        cls.po_master1 = PurchaseOrderMaster.objects.create(id=1001, supplier=cls.supplier1, company=cls.company, po_no='PO001')
        cls.po_master2 = PurchaseOrderMaster.objects.create(id=1002, supplier=cls.supplier2, company=cls.company, po_no='PO002')

        # Create InwardMaster instances linking to PurchaseOrderMaster
        # Assuming InwardMaster.po_master points to PurchaseOrderMaster.id
        cls.inward_master1 = InwardMaster.objects.create(
            id=2001, po_no='PO001', challan_no='CH001', challan_date='2024-01-15',
            po_id_link=cls.po_master1.id, company=cls.company,
            po_master=cls.po_master1 # Direct link for testing ORM traversal
        )
        cls.inward_master2 = InwardMaster.objects.create(
            id=2002, po_no='PO002', challan_no='CH002', challan_date='2024-02-20',
            po_id_link=cls.po_master2.id, company=cls.company,
            po_master=cls.po_master2
        )

        # Create MaterialReceivedMaster instances linking to InwardMaster
        # Assuming MaterialReceivedMaster.inward_master points to InwardMaster.id
        cls.mr_master1 = MaterialReceivedMaster.objects.create(
            id=3001, gin_no='GIN001', grr_no='GRR001', gin_id_link=cls.inward_master1.id,
            company=cls.company,
            inward_master=cls.inward_master1 # Direct link for testing ORM traversal
        )
        cls.mr_master2 = MaterialReceivedMaster.objects.create(
            id=3002, gin_no='GIN002', grr_no='GRR002', gin_id_link=cls.inward_master2.id,
            company=cls.company,
            inward_master=cls.inward_master2
        )

        # Create MaterialQualityMaster instances linking to MaterialReceivedMaster
        cls.mq_master1 = MaterialQualityMaster.objects.create(
            id=4001, gqn_no='GQN001', grr_no='GRR001', sys_date='2024-03-01',
            financial_year=cls.fin_year_2024, company=cls.company, grr_master=cls.mr_master1
        )
        cls.mq_master2 = MaterialQualityMaster.objects.create(
            id=4002, gqn_no='GQN002', grr_no='GRR002', sys_date='2024-03-10',
            financial_year=cls.fin_year_2024, company=cls.company, grr_master=cls.mr_master2
        )
        cls.mq_master3 = MaterialQualityMaster.objects.create(
            id=4003, gqn_no='GQN003', grr_no='GRR003', sys_date='2024-03-15',
            financial_year=cls.fin_year_2023, company=cls.company, grr_master=cls.mr_master1 # Same GRR but different GQN
        )

        # Create MaterialQualityDetail with rejected quantities
        MaterialQualityDetail.objects.create(id=5001, material_quality_master=cls.mq_master1, rejected_qty=5.0)
        MaterialQualityDetail.objects.create(id=5002, material_quality_master=cls.mq_master1, rejected_qty=0.0) # Not counted
        MaterialQualityDetail.objects.create(id=5003, material_quality_master=cls.mq_master2, rejected_qty=10.0)
        MaterialQualityDetail.objects.create(id=5004, material_quality_master=cls.mq_master3, rejected_qty=0.0) # Not counted, should not appear in list

class MaterialQualityMasterModelTest(ModelSetupMixin, TestCase):
    def test_material_quality_master_creation(self):
        mq_master = MaterialQualityMaster.objects.get(id=self.mq_master1.id)
        self.assertEqual(mq_master.gqn_no, 'GQN001')
        self.assertEqual(mq_master.grr_no, 'GRR001')
        self.assertEqual(mq_master.company.name, 'Test Company')
        self.assertEqual(mq_master.financial_year.fin_year, '2024-2025')

    def test_derived_properties(self):
        mq_master = MaterialQualityMaster.objects.get(id=self.mq_master1.id)
        self.assertEqual(mq_master.fin_year_display, '2024-2025')
        self.assertEqual(mq_master.gin_no_display, 'GIN001')
        self.assertEqual(mq_master.po_no_display, 'PO001')
        self.assertEqual(mq_master.supplier_display, f'Supplier A [{self.supplier1.id}]')
        self.assertEqual(mq_master.sup_id_display, self.supplier1.id)
        self.assertEqual(mq_master.challan_no_display, 'CH001')
        self.assertEqual(mq_master.challan_date_display, '15/01/2024')
        self.assertEqual(mq_master.sys_date_display, '01/03/2024')

    def test_get_goods_rejection_list_base_filter(self):
        # Should only return MQM with rejected_qty > 0 and correct company/fin_year
        queryset = MaterialQualityMaster.objects.get_goods_rejection_list(
            company_id=self.company.id, fin_year_id=self.fin_year_2024.id
        )
        self.assertEqual(queryset.count(), 2) # mq_master1 and mq_master2
        self.assertIn(self.mq_master1, queryset)
        self.assertIn(self.mq_master2, queryset)
        self.assertNotIn(self.mq_master3, queryset) # Rejected qty is 0, also older fin year

    def test_get_goods_rejection_list_gqn_filter(self):
        queryset = MaterialQualityMaster.objects.get_goods_rejection_list(
            company_id=self.company.id, fin_year_id=self.fin_year_2024.id,
            search_type='1', search_term='GQN001'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.mq_master1, queryset)

    def test_get_goods_rejection_list_grr_filter(self):
        queryset = MaterialQualityMaster.objects.get_goods_rejection_list(
            company_id=self.company.id, fin_year_id=self.fin_year_2024.id,
            search_type='2', search_term='GRR001'
        )
        self.assertEqual(queryset.count(), 1) # Only mq_master1 matches GRR001 and has rejected qty > 0
        self.assertIn(self.mq_master1, queryset)

    def test_get_goods_rejection_list_po_filter(self):
        queryset = MaterialQualityMaster.objects.get_goods_rejection_list(
            company_id=self.company.id, fin_year_id=self.fin_year_2024.id,
            search_type='3', search_term='PO002'
        )
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.mq_master2, queryset)

    def test_get_goods_rejection_list_supplier_filter(self):
        queryset = MaterialQualityMaster.objects.get_goods_rejection_list(
            company_id=self.company.id, fin_year_id=self.fin_year_2024.id,
            search_type='0', supplier_id=self.supplier1.id
        )
        self.assertEqual(queryset.count(), 1)
        self.assertIn(self.mq_master1, queryset)

class GoodsRejectionViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Mock session data
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.fin_year_2024.id
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('goods_rejection_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goods_rejection/list.html')
        self.assertContains(response, 'Goods Rejection Notes [GRN]')
        self.assertIsInstance(response.context['search_form'], GoodsRejectionSearchForm)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('goods_rejection_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goods_rejection/_goods_rejection_table.html')
        self.assertTrue('goods_rejections' in response.context)
        # Check that filtered data is present
        self.assertContains(response, 'GQN001')
        self.assertContains(response, 'GQN002')
        self.assertNotContains(response, 'GQN003')

    def test_table_partial_view_search_gqn(self):
        response = self.client.get(reverse('goods_rejection_table'), {
            'search_type': '1', 'search_text_field': 'GQN001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GQN001')
        self.assertNotContains(response, 'GQN002')

    def test_table_partial_view_search_supplier(self):
        response = self.client.get(reverse('goods_rejection_table'), {
            'search_type': '0', 'supplier_id_hidden': self.supplier2.id
        })
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GQN002')
        self.assertNotContains(response, 'GQN001')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_autocomplete': 'Sup'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goods_rejection/_supplier_suggestions.html')
        self.assertContains(response, 'Supplier A')
        self.assertContains(response, 'Supplier B')

    def test_create_view_get(self):
        response = self.client.get(reverse('goods_rejection_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goods_rejection/_goods_rejection_form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_htmx(self):
        form_data = {
            'gqn_no': 'GQNNEW',
            'grr_no': 'GRRNEW',
            'sys_date': date.today(),
            'financial_year': self.fin_year_2024.id,
            'grr_master': self.mr_master1.id,
        }
        response = self.client.post(reverse('goods_rejection_add'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX should return 204 No Content
        self.assertTrue(MaterialQualityMaster.objects.filter(gqn_no='GQNNEW').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsRejectionList')

    def test_update_view_get(self):
        response = self.client.get(reverse('goods_rejection_edit', args=[self.mq_master1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goods_rejection/_goods_rejection_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.mq_master1)

    def test_update_view_post_htmx(self):
        updated_gqn_no = 'GQN001_UPDATED'
        form_data = {
            'gqn_no': updated_gqn_no,
            'grr_no': self.mq_master1.grr_no,
            'sys_date': self.mq_master1.sys_date,
            'financial_year': self.mq_master1.financial_year.id,
            'grr_master': self.mq_master1.grr_master.id,
        }
        response = self.client.post(reverse('goods_rejection_edit', args=[self.mq_master1.id]), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.mq_master1.refresh_from_db()
        self.assertEqual(self.mq_master1.gqn_no, updated_gqn_no)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsRejectionList')

    def test_delete_view_get(self):
        response = self.client.get(reverse('goods_rejection_delete', args=[self.mq_master1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goods_rejection/_goods_rejection_confirm_delete.html')
        self.assertEqual(response.context['object'], self.mq_master1)

    def test_delete_view_post_htmx(self):
        response = self.client.post(reverse('goods_rejection_delete', args=[self.mq_master1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialQualityMaster.objects.filter(id=self.mq_master1.id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGoodsRejectionList')

    def test_detail_view(self):
        response = self.client.get(reverse('goods_rejection_detail', args=[self.mq_master1.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory_transactions/goods_rejection/detail.html')
        self.assertEqual(response.context['goods_rejection'], self.mq_master1)
        self.assertContains(response, 'GQN001')
        self.assertContains(response, 'Supplier A')

    def test_detail_view_not_found(self):
        # Test an ID that doesn't exist or doesn't meet the rejection criteria
        with self.assertRaises(Http404):
            self.client.get(reverse('goods_rejection_detail', args=[99999]), HTTP_HX_REQUEST='true')
        
        # Test an existing GRN that doesn't have rejected items (should not be in list)
        mq_master_no_rejection = MaterialQualityMaster.objects.create(
            id=4004, gqn_no='GQNNoRej', grr_no='GRRNoRej', sys_date='2024-03-20',
            financial_year=self.fin_year_2024, company=self.company, grr_master=self.mr_master1
        )
        # No MaterialQualityDetail for this GRN, or all rejected_qty=0
        with self.assertRaises(Http404):
            self.client.get(reverse('goods_rejection_detail', args=[mq_master_no_rejection.id]), HTTP_HX_REQUEST='true')
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic updates:**
    *   The main list page (`list.html`) uses `hx-get` to load the table content from `goods_rejection_table/` on page `load` and `refreshGoodsRejectionList` trigger.
    *   Search button (`btnSearch`) and dropdown changes will also trigger `hx-get` to `goods_rejection_table/` with `hx-include="#searchForm"` to pass search parameters.
    *   `Add`, `Edit`, `Delete`, `Select` buttons on the table all use `hx-get` to load forms/details into a modal (`#modalContent`).
    *   Form submissions within the modal (`_goods_rejection_form.html`, `_goods_rejection_confirm_delete.html`) use `hx-post` and `hx-swap="none"`. The Django views then send an `HX-Trigger` header (`refreshGoodsRejectionList`) to update the main table after successful CRUD operations.
    *   Supplier autocomplete uses `hx-get` and `hx-trigger` to fetch suggestions dynamically.
*   **Alpine.js for UI state management:**
    *   `x-data` on the main container in `list.html` manages `selectedSearchType`, `supplierSearchText`, and `selectedSupplierId`.
    *   `x-show` directives control the visibility of the general search text field vs. the supplier autocomplete field based on `selectedSearchType`.
    *   `hx-on:click` directives on supplier suggestions update Alpine.js variables (`selectedSupplierId`, `supplierSearchText`) and trigger a form submission to refresh the table.
*   **DataTables for list views:**
    *   `_goods_rejection_table.html` includes a `<script>` block that initializes DataTables on `#goodsRejectionTable` once HTMX inserts the table into the DOM. This provides client-side searching, sorting, and pagination.
*   **No custom JavaScript:** All dynamic interactions are handled through HTMX attributes and basic Alpine.js directives for UI state, eliminating the need for complex custom JavaScript code.

---

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the ASP.NET Goods Rejection Note module to Django. By adhering to the principles of "Fat Model, Thin View," leveraging HTMX for dynamic interactions, and structuring the code for testability and maintainability, the new Django application will be robust, scalable, and easy to modernize further. The non-technical language and automation-focused steps ensure that business stakeholders can understand and oversee the process effectively.