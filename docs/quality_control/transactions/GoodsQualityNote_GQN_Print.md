## ASP.NET to Django Conversion Script: Goods Quality Note [GQN] - Print

This document outlines a comprehensive modernization plan to transition the existing ASP.NET "Goods Quality Note [GQN] - Print" module to a modern Django-based solution. The focus is on leveraging AI-assisted automation to streamline the migration, emphasizing a `Fat Model, Thin View` architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for enhanced data presentation.

This plan is designed for clarity, using plain English to ensure all stakeholders, technical and non-technical, can understand the proposed approach and its business benefits.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The ASP.NET code primarily uses a stored procedure `Sp_GQN_Edit` for fetching data and `tblMM_Supplier_master` for supplier autocomplete. Based on the `GridView2` columns, we can infer the structure of the data presented and, by extension, the primary table(s) involved. For the purpose of modernization, we will map to the logical database entities as directly as possible.

**Analysis:**
- The `GridView2` displays columns like `Id`, `GQNNo`, `SysDate`, `GRRNo`, `GINNo`, `PONo`, `Supplier`, `SupId`, `ChNO`, `ChDT`, `FinYearId`, `FinYear`, `GINId`.
- The `AutoCompleteExtender` uses `tblMM_Supplier_master` with `SupplierId` and `SupplierName`.
- The search logic in `loadData` references `tblQc_MaterialQuality_Master.GQNNo`, `tblinv_MaterialReceived_Master.GRRNo`, `tblInv_Inward_Master.PONo`, and `tblMM_PO_Master.SupplierId`. This indicates a complex data source (likely a view or a set of joined tables).

**Inferred Tables:**
1.  **`tblQc_MaterialQuality_Master` (for `GoodsQualityNote` Model):** This appears to be the primary table for GQN data, containing `Id`, `GQNNo`, `SysDate`. Other fields displayed in the `GridView2` (like `FinYear`, `GRRNo`, `GINNo`, `PONo`, `Supplier`, `ChNO`, `ChDT`) are likely derived from joins or denormalized. For simplicity and to match the data presented, we will create a `GoodsQualityNote` model with all these fields, assuming they either exist directly or are part of a database view that aggregates this information.
    - **Columns:** `Id` (Primary Key), `GQNNo`, `SysDate`, `GRRNo`, `GINNo`, `PONo`, `ChNO`, `ChDT`, `FinYearId`, `FinYear`, `GINId`, `SupId`, `Supplier`.
    - **Note on `Supplier` and `SupId`:** These clearly link to the `Supplier` table. We'll represent `SupId` as a `ForeignKey` to the `Supplier` model. `Supplier` (name) will be accessed via this relationship.

2.  **`tblMM_Supplier_master` (for `Supplier` Model):** This table holds supplier information.
    - **Columns:** `SupplierId` (Primary Key), `SupplierName`.

### Step 2: Identify Backend Functionality

**Task:** Determine the business operations in the ASP.NET code.

**Analysis:**
The ASP.NET page is titled "Goods Quality Note [GQN] - Print". The primary functionality is to display a list of GQN records with search and pagination capabilities.
-   **Read (List & Search):** The page allows users to search GQN records by `Supplier Name`, `GQN No`, `GRR No`, or `PO No`. Results are displayed in a paginated grid. This will translate to a Django `ListView` with robust filtering logic and a dedicated HTMX endpoint for the table content.
-   **Select (Details Redirection):** The "Select" `LinkButton` in the `GridView` redirects to `GoodsQualityNote_GQN_Print_Details.aspx` with various query parameters (`Id`, `GINId`, `GQNNo`, etc.). In Django, this will be represented by a standard link (`<a>` tag) to a detail page, leveraging the existing `Id` as the primary key. There are no direct Create, Update, or Delete operations on this specific page.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles for conversion to Django templates with HTMX/Alpine.js.

**Analysis:**
-   **Search Filters:**
    -   `DropDownList1` (Search by type): Will be an HTML `<select>` element, managed by Alpine.js to conditionally show/hide text input fields.
    -   `txtSupplier` (Supplier Name search) with `AutoCompleteExtender`: Will be an HTML `<input type="text">` with HTMX for dynamic suggestions (autocomplete).
    -   `Txtfield` (GQN No, GRR No, PO No search): Will be an HTML `<input type="text">`, conditionally visible based on search type, also managed by Alpine.js.
    -   `btnSearch`: Will be an HTML `<button>` triggering an HTMX request to refresh the table.
-   **Data Display:**
    -   `GridView2`: Will be replaced by an HTML `<table>` element, dynamically populated and enhanced by `DataTables.js` for client-side functionality (pagination, sorting, filtering). The table content will be loaded and refreshed via HTMX.
-   **Row Actions:**
    -   "Select" `LinkButton`: Will be an HTML `<a>` tag or `<button>` that navigates to a GQN detail page (similar to `GoodsQualityNote_GQN_Print_Details.aspx`).

### Step 4: Generate Django Code
The following Django files will be generated within an assumed `inventory` application.

#### 4.1 Models (inventory/models.py)

**Task:** Create Django models based on the identified database schema.

**Instructions:**
-   `Supplier` model mapped to `tblMM_Supplier_master`.
-   `GoodsQualityNote` model mapped to `tblQc_MaterialQuality_Master`. We will include all fields from the `GridView2` columns, assuming they are either directly in this table or are accessible through a database view that `Sp_GQN_Edit` leverages. `SupId` will be a `ForeignKey` to `Supplier`.
-   `managed = False` and `db_table` are crucial for mapping to existing legacy databases without Django managing schema migrations.

```python
from django.db import models

# Model for tblMM_Supplier_master
class Supplier(models.Model):
    # SupplierId is the primary key in the legacy database
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    # Assuming CompId exists but might not be directly used for this view
    # comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's creation/deletion
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or f"Supplier ID: {self.supplier_id}"

    # Business logic methods can be added here if needed for Supplier-specific operations

# Model for tblQc_MaterialQuality_Master (or a view containing this data)
# This model represents the aggregated data displayed in the ASP.NET GridView.
class GoodsQualityNote(models.Model):
    # Primary Key
    id = models.IntegerField(db_column='Id', primary_key=True)
    
    # Financial Year details
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    
    # Goods Quality Note (GQN) details
    gqn_no = models.CharField(db_column='GQNNo', max_length=50, blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True) # Assuming SysDate is a Date
    
    # Related document IDs
    gin_id = models.IntegerField(db_column='GINId', blank=True, null=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    
    # Supplier details - ForeignKey to Supplier model
    # The ASP.NET code used 'SupId' as a label and 'Supplier' for name.
    # We will map 'SupId' to the foreign key.
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupId', to_field='supplier_id', blank=True, null=True)
    # The 'Supplier' field in GridView was the name, which we get from the FK.
    # If the database denormalizes and stores supplier name directly, it would be:
    # supplier_name_denormalized = models.CharField(db_column='Supplier', max_length=255, blank=True, null=True)
    
    # Challan details
    challan_no = models.CharField(db_column='ChNO', max_length=100, blank=True, null=True)
    challan_date = models.DateField(db_column='ChDT', blank=True, null=True) # Assuming ChDT is a Date

    class Meta:
        managed = False  # Important for existing legacy tables
        # Assuming the main GQN data is in tblQc_MaterialQuality_Master,
        # or that there's a database view with these columns named similarly.
        # If Sp_GQN_Edit directly generates a temporary result set,
        # a custom manager or raw SQL will be needed for querying.
        db_table = 'tblQc_MaterialQuality_Master' # Placeholder table name for GQN data
        verbose_name = 'Goods Quality Note'
        verbose_name_plural = 'Goods Quality Notes'

    def __str__(self):
        return f"GQN No: {self.gqn_no} - {self.supplier.supplier_name if self.supplier else 'N/A'}"
        
    # Business logic for GQN data goes here.
    # Example: A method to get detailed info or perform calculations.
    # @classmethod
    # def get_filtered_data(cls, company_id, financial_year_id, search_type, search_value):
    #     queryset = cls.objects.all()
    #     # Apply filters based on search_type and search_value
    #     # This logic would mimic the Sp_GQN_Edit parameters.
    #     return queryset
```

#### 4.2 Forms (inventory/forms.py)

**Task:** Define a Django form for the search functionality.

**Instructions:**
A simple `forms.Form` will be used to handle the search criteria, as we are not creating or updating a `GoodsQualityNote` object directly from this page. We'll use a `ChoiceField` for the search type and a `CharField` for the search value. For the supplier autocomplete, we won't use a Django form field with a predefined widget, but rather handle it with HTMX directly in the template and a separate endpoint.

```python
from django import forms
from .models import GoodsQualityNote, Supplier

class GoodsQualityNoteSearchForm(forms.Form):
    # Mimics DropDownList1 options
    SEARCH_CHOICES = [
        ('0', 'Supplier Name'),
        ('1', 'GQN No'),
        ('2', 'GRR No'),
        ('3', 'PO No'),
    ]

    search_by = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    # This field will be used for GQN No, GRR No, PO No search
    search_text = forms.CharField(
        max_length=255,
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search value',
            'x-show': "searchType !== '0'" # Alpine.js control for visibility
        })
    )

    # This field will be used for Supplier Name search with autocomplete
    supplier_name = forms.CharField(
        max_length=255,
        required=False,
        label="Supplier Name",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'x-show': "searchType === '0'", # Alpine.js control for visibility
            'hx-get': "/inventory/gqn/supplier-autocomplete/", # HTMX endpoint for autocomplete
            'hx-trigger': "keyup changed delay:500ms, search",
            'hx-target': "#autocomplete-results",
            'hx-swap': "innerHTML",
            'hx-vals': "js:{'comp_id': document.getElementById('comp_id').value, 'fin_year_id': document.getElementById('fin_year_id').value}", # Pass context
            'autocomplete': 'off' # Disable browser autocomplete
        })
    )
    
    # Hidden field to store selected supplier ID from autocomplete (if needed for backend logic)
    # For now, we'll just pass the supplier name to the view and let the view resolve it.
    
    # Contextual fields for session data (Company ID, Financial Year ID)
    # These will be passed from the view to the form implicitly or explicitly.
    company_id = forms.IntegerField(widget=forms.HiddenInput(attrs={'id': 'comp_id'}))
    financial_year_id = forms.IntegerField(widget=forms.HiddenInput(attrs={'id': 'fin_year_id'}))

    # Add custom validation methods here if needed, but search forms are usually lenient.
```

#### 4.3 Views (inventory/views.py)

**Task:** Implement the list view, search functionality, and autocomplete using CBVs and custom HTMX endpoints.

**Instructions:**
-   `GoodsQualityNoteListView`: Handles the initial page load and displays the base template.
-   `GoodsQualityNoteTablePartialView`: An HTMX-specific view that renders only the table content, handling search parameters and pagination for `DataTables`. This is crucial for dynamic updates without full page reloads.
-   `SupplierAutocompleteView`: An HTMX endpoint that returns JSON/HTML for supplier name suggestions.
-   Views remain thin, delegating complex query logic to model managers or helper functions.

```python
from django.views.generic import ListView, View
from django.shortcuts import render
from django.http import JsonResponse, HttpResponse
from django.db.models import Q
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming user authentication
from .models import GoodsQualityNote, Supplier
from .forms import GoodsQualityNoteSearchForm

# Helper function to get session data (mimics ASP.NET Session)
# In a real Django app, this would come from request.user or specific context.
# For demonstration, we'll hardcode or retrieve from request.session if available.
def get_session_context(request):
    # Placeholder for session data. In a real app, this would be dynamic.
    # For now, assume a dummy CompId and FinYearId.
    # In a production system, this would come from request.user or actual session management.
    company_id = request.session.get('compid', 1) # Default to 1
    financial_year_id = request.session.get('finyear', 1) # Default to 1
    return {'compid': company_id, 'finyear': financial_year_id}


class GoodsQualityNoteListView(LoginRequiredMixin, ListView):
    model = GoodsQualityNote
    template_name = 'inventory/goodsqualitynote/list.html'
    context_object_name = 'goods_quality_notes' # This will be the initial, empty list
    paginate_by = 20 # Initial page size, DataTables will take over client-side pagination

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        session_context = get_session_context(self.request)
        
        # Initialize the form with session context, even if not directly bound to GET params
        form = GoodsQualityNoteSearchForm(initial={
            'company_id': session_context['compid'],
            'financial_year_id': session_context['finyear']
        })
        context['search_form'] = form
        
        # Initial empty queryset or load first 20 records
        # The actual table content will be loaded via HTMX from GoodsQualityNoteTablePartialView
        context['goods_quality_notes'] = [] 
        return context

# This view will be targeted by HTMX to render the table content
class GoodsQualityNoteTablePartialView(LoginRequiredMixin, ListView):
    model = GoodsQualityNote
    template_name = 'inventory/goodsqualitynote/_goodsqualitynote_table.html'
    context_object_name = 'goods_quality_notes'
    # paginate_by is not used here, DataTables handles pagination client-side,
    # but we might still limit the query for performance on large datasets.

    def get_queryset(self):
        # Apply filters based on GET parameters from the search form
        queryset = super().get_queryset()
        
        # Get context from session (CompId, FinYearId)
        session_context = get_session_context(self.request)
        company_id = session_context['compid']
        financial_year_id = session_context['finyear']
        
        # Apply mandatory filters first (CompanyId, FinancialYearId)
        # Assuming these fields exist in GoodsQualityNote model or related tables
        queryset = queryset.filter(Q(company_id=company_id) & Q(fin_year_id=financial_year_id)) # Adjust field names if different
        
        # Extract search parameters from GET request
        search_by = self.request.GET.get('search_by', '0') # Default to Supplier Name
        search_value = self.request.GET.get('search_text', '')
        supplier_name_search = self.request.GET.get('supplier_name', '')

        # Build dynamic query mimicking ASP.NET's Sp_GQN_Edit logic
        if search_value:
            if search_by == '1': # GQN No
                queryset = queryset.filter(gqn_no__icontains=search_value)
            elif search_by == '2': # GRR No
                queryset = queryset.filter(grr_no__icontains=search_value)
            elif search_by == '3': # PO No
                queryset = queryset.filter(po_no__icontains=search_value)
        
        # Handle Supplier Name search (assuming it comes from the autocomplete field)
        if search_by == '0' and supplier_name_search:
            # We need to resolve the supplier name to its SupId
            # In a real scenario, the autocomplete would ideally pass SupId directly.
            # For now, we search by name and filter by the resolved SupId.
            # This is a less efficient approach; ideally, autocomplete sends the ID.
            try:
                # Find supplier IDs matching the name (case-insensitive)
                matching_suppliers = Supplier.objects.filter(supplier_name__icontains=supplier_name_search)
                if matching_suppliers.exists():
                    supplier_ids = [s.supplier_id for s in matching_suppliers]
                    queryset = queryset.filter(supplier__in=supplier_ids) # Filter by SupId
                else:
                    queryset = queryset.none() # No matching suppliers
            except Exception as e:
                # Handle cases where supplier model/db might be misconfigured
                print(f"Error filtering by supplier: {e}")
                queryset = queryset.none() # Return empty queryset on error
        
        return queryset

# HTMX endpoint for supplier autocomplete suggestions
class SupplierAutocompleteView(LoginRequiredMixin, View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('supplier_name', '')
        comp_id = request.GET.get('comp_id') # From hidden field
        fin_year_id = request.GET.get('fin_year_id') # From hidden field

        suggestions = []
        if query:
            # Filter suppliers by name and company_id if available
            # Assuming 'comp_id' is a field in the Supplier model or related for filtering
            # For now, we'll just filter by name as CompId was from Session in ASP.NET
            suppliers = Supplier.objects.filter(supplier_name__icontains=query).order_by('supplier_name')[:10]
            
            # Return as HTML list items for HTMX swap="innerHTML"
            for supplier in suppliers:
                # The value passed back to the input would typically be the full name,
                # but the underlying ID might be used for actual filtering.
                # Here, we'll just show the name.
                suggestions.append(f"""
                    <div class="p-2 hover:bg-gray-100 cursor-pointer" 
                         hx-on:click="document.getElementById('id_supplier_name').value='{supplier.supplier_name}'; 
                                     this.closest('#autocomplete-results').innerHTML=''">
                        {supplier.supplier_name}
                    </div>
                """)
        
        # Return HTML partial for HTMX to swap into the target div
        return HttpResponse(''.join(suggestions))

# This is an example of a detail view that the "Select" button would link to.
# This view is NOT part of the original ASP.NET page's functionality, but is the target.
class GoodsQualityNoteDetailView(LoginRequiredMixin, View):
    def get(self, request, pk):
        # Retrieve the GoodsQualityNote object based on the primary key
        try:
            gqn = GoodsQualityNote.objects.get(pk=pk)
        except GoodsQualityNote.DoesNotExist:
            return HttpResponse("Goods Quality Note not found.", status=404)
        
        # Here, you would render a detail template for the GQN.
        # The ASP.NET page passed many parameters, so a robust detail page
        # would display all relevant information.
        context = {
            'gqn': gqn,
            # Add other related data if needed from different models
        }
        return render(request, 'inventory/goodsqualitynote/detail.html', context)

```

#### 4.4 Templates (inventory/templates/inventory/goodsqualitynote/)

**Task:** Create templates for the main list page, the table partial, and the detail page placeholder.

**Instructions:**
-   `list.html`: The main page template, extends `core/base.html`, includes the search form, and a container for the HTMX-loaded table. Uses Alpine.js for UI state.
-   `_goodsqualitynote_table.html`: A partial template containing only the `DataTables` table, loaded via HTMX.
-   `detail.html`: A placeholder for the GQN detail page (which was `GoodsQualityNote_GQN_Print_Details.aspx` in ASP.NET).

**list.html**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchType: '0' }"> {# Default to 'Supplier Name' #}
    <div class="bg-blue-600 text-white p-3 rounded-t-lg mb-4">
        <h2 class="text-xl font-bold">Goods Quality Note [GQN] - Print</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form id="searchForm" hx-get="{% url 'inventory:gqn_table' %}" hx-target="#gqn-table-container" hx-swap="innerHTML" hx-indicator="#loading-spinner">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                <div class="col-span-1">
                    <label for="{{ search_form.search_by.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By</label>
                    <select name="{{ search_form.search_by.name }}" id="{{ search_form.search_by.id_for_label }}" 
                            class="{{ search_form.search_by.field.widget.attrs.class }}" 
                            x-model="searchType" 
                            hx-get="{% url 'inventory:gqn_table' %}" 
                            hx-target="#gqn-table-container" 
                            hx-swap="innerHTML" 
                            hx-indicator="#loading-spinner"
                            hx-include="#searchForm" {# Include form values when dropdown changes #}>
                        {% for value, label in search_form.search_by.field.choices %}
                            <option value="{{ value }}">{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div class="col-span-1" x-show="searchType === '0'">
                    <label for="{{ search_form.supplier_name.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier Name</label>
                    <input type="text" name="{{ search_form.supplier_name.name }}" id="{{ search_form.supplier_name.id_for_label }}" 
                           class="{{ search_form.supplier_name.field.widget.attrs.class }}" 
                           placeholder="{{ search_form.supplier_name.field.widget.attrs.placeholder }}"
                           hx-get="{{ search_form.supplier_name.field.widget.attrs.hx_get }}"
                           hx-trigger="{{ search_form.supplier_name.field.widget.attrs.hx_trigger }}"
                           hx-target="{{ search_form.supplier_name.field.widget.attrs.hx_target }}"
                           hx-swap="{{ search_form.supplier_name.field.widget.attrs.hx_swap }}"
                           hx-vals="{{ search_form.supplier_name.field.widget.attrs.hx_vals }}"
                           autocomplete="{{ search_form.supplier_name.field.widget.attrs.autocomplete }}"
                           >
                    <div id="autocomplete-results" class="relative z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto"></div>
                </div>

                <div class="col-span-1" x-show="searchType !== '0'">
                    <label for="{{ search_form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Value</label>
                    <input type="text" name="{{ search_form.search_text.name }}" id="{{ search_form.search_text.id_for_label }}" 
                           class="{{ search_form.search_text.field.widget.attrs.class }}" 
                           placeholder="{{ search_form.search_text.field.widget.attrs.placeholder }}"
                           >
                </div>

                <div class="col-span-1">
                    <button type="submit" 
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full"
                            >
                        <span id="loading-spinner" class="inline-block h-4 w-4 border-b-2 border-white rounded-full animate-spin hidden mr-2"></span>
                        Search
                    </button>
                </div>
            </div>
            {# Hidden inputs for session context, read by forms.py and views.py #}
            {{ search_form.company_id }}
            {{ search_form.financial_year_id }}
        </form>
    </div>

    <div id="gqn-table-container"
         hx-trigger="load, refreshGQNList from:body"
         hx-get="{% url 'inventory:gqn_table' %}"
         hx-target="#gqn-table-container"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Goods Quality Notes...</p>
        </div>
    </div>
    
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'gqn-table-container' && evt.detail.elt.querySelector('#gqnTable')) {
            $('#gqnTable').DataTable({
                "pageLength": 20, // Match ASP.NET PageSize
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "dom": 'lfrtip' // Layout of DataTables controls (length, filter, table, info, pagination)
            });
        }
    });

    document.addEventListener('htmx:beforeRequest', function(evt) {
        if (evt.detail.target.id === 'searchForm' || evt.detail.elt.closest('#searchForm')) {
            document.getElementById('loading-spinner').classList.remove('hidden');
        }
    });

    document.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.target.id === 'searchForm' || evt.detail.elt.closest('#searchForm')) {
            document.getElementById('loading-spinner').classList.add('hidden');
        }
    });
</script>
{% endblock %}
```

**_goodsqualitynote_table.html**
```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="gqnTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GQN No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GRR No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GIN No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan No</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Challan Date</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if goods_quality_notes %}
                {% for gqn in goods_quality_notes %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.gqn_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.sys_date|date:"Y-m-d" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.grr_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.gin_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.po_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.supplier.supplier_name|default:"N/A" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.challan_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ gqn.challan_date|date:"Y-m-d" }}</td>
                    <td class="py-2 px-4 border-b border-gray-200">
                        <a href="{% url 'inventory:gqn_detail' pk=gqn.id %}" 
                           class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded">
                            Select
                        </a>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="10" class="py-4 text-center text-gray-500">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>
```

**detail.html (Placeholder for the redirected details page)**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-3 rounded-t-lg mb-4">
        <h2 class="text-xl font-bold">Goods Quality Note [GQN] - Details: {{ gqn.gqn_no }}</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">GQN Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div><strong>ID:</strong> {{ gqn.id }}</div>
            <div><strong>GQN No:</strong> {{ gqn.gqn_no }}</div>
            <div><strong>Date:</strong> {{ gqn.sys_date|date:"Y-m-d" }}</div>
            <div><strong>Supplier:</strong> {{ gqn.supplier.supplier_name|default:"N/A" }} (ID: {{ gqn.supplier.supplier_id|default:"N/A" }})</div>
            <div><strong>GRR No:</strong> {{ gqn.grr_no }}</div>
            <div><strong>GIN No:</strong> {{ gqn.gin_no }}</div>
            <div><strong>PO No:</strong> {{ gqn.po_no }}</div>
            <div><strong>Challan No:</strong> {{ gqn.challan_no }}</div>
            <div><strong>Challan Date:</strong> {{ gqn.challan_date|date:"Y-m-d" }}</div>
            <div><strong>Financial Year:</strong> {{ gqn.fin_year }} (ID: {{ gqn.fin_year_id }})</div>
            <div><strong>GIN ID:</strong> {{ gqn.gin_id }}</div>
            {# Add more fields as needed for the full details #}
        </div>
        
        <div class="mt-6 text-right">
            <a href="{% url 'inventory:gqn_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Back to List
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (inventory/urls.py)

**Task:** Define URL patterns for all views, including HTMX endpoints.

**Instructions:**
-   Map the main list view.
-   Map the partial table view for HTMX updates.
-   Map the supplier autocomplete endpoint.
-   Map the detail view.

```python
from django.urls import path
from .views import (
    GoodsQualityNoteListView, 
    GoodsQualityNoteTablePartialView, 
    SupplierAutocompleteView,
    GoodsQualityNoteDetailView, # Added for the 'Select' action
)

app_name = 'inventory' # Namespace for URLs

urlpatterns = [
    # Main GQN List page
    path('gqn/', GoodsQualityNoteListView.as_view(), name='gqn_list'),
    
    # HTMX endpoint for the GQN table partial (for search/pagination)
    path('gqn/table/', GoodsQualityNoteTablePartialView.as_view(), name='gqn_table'),
    
    # HTMX endpoint for supplier name autocomplete
    path('gqn/supplier-autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # GQN Detail page (target of the 'Select' action)
    path('gqn/detail/<int:pk>/', GoodsQualityNoteDetailView.as_view(), name='gqn_detail'),
]
```

#### 4.6 Tests (inventory/tests.py)

**Task:** Write comprehensive unit tests for models and integration tests for views.

**Instructions:**
-   Test model field attributes and string representations.
-   Test view responses, template usage, context data, and HTMX interactions.
-   Ensure search and filtering logic works correctly.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import OperationalError
from .models import GoodsQualityNote, Supplier
from unittest.mock import patch # For mocking session data if needed

class SupplierModelTest(TestCase):
    # Set up test data for all tests in this class
    # For managed=False models, you might need to use real DB inserts or mock the ORM.
    # For testing purposes, we can simulate existing data by directly calling
    # objects.create if a test DB is set up that doesn't strictly enforce managed=False,
    # or by patching the queryset. For simplicity, we'll assume a test DB.
    @classmethod
    def setUpTestData(cls):
        try:
            cls.supplier1 = Supplier.objects.create(supplier_id=101, supplier_name='Alpha Corp')
            cls.supplier2 = Supplier.objects.create(supplier_id=102, supplier_name='Beta Industries')
        except OperationalError as e:
            # Handle cases where managed=False prevents direct creation in a test DB
            print(f"Warning: Could not create test Supplier due to managed=False. Error: {e}")
            print("Tests for Supplier model will assume data exists or mock queries.")

    def test_supplier_creation(self):
        try:
            s = Supplier.objects.get(supplier_id=101)
            self.assertEqual(s.supplier_name, 'Alpha Corp')
            self.assertEqual(str(s), 'Alpha Corp')
        except Supplier.DoesNotExist:
            self.fail("Supplier was not found, possibly due to managed=False setup in test environment.")

    def test_supplier_name_label(self):
        field_label = Supplier._meta.get_field('supplier_name').verbose_name
        self.assertEqual(field_label, 'SupplierName') # This would be 'Supplier name' normally, but matches db_column name

class GoodsQualityNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        try:
            # Create a test supplier first, as GQN has a ForeignKey to it
            cls.test_supplier = Supplier.objects.create(supplier_id=200, supplier_name='Test Supplier Inc.')
            
            # Create test GQN data
            cls.gqn1 = GoodsQualityNote.objects.create(
                id=1,
                fin_year_id=2023, fin_year='2023-24',
                gqn_no='GQN/001/23', sys_date='2023-01-15',
                gin_id=1001, grr_no='GRR/A/001', gin_no='GIN/X/001', po_no='PO/Y/001',
                supplier=cls.test_supplier, # Link to the test supplier
                challan_no='CH/Z/001', challan_date='2023-01-10'
            )
            cls.gqn2 = GoodsQualityNote.objects.create(
                id=2,
                fin_year_id=2023, fin_year='2023-24',
                gqn_no='GQN/002/23', sys_date='2023-02-20',
                gin_id=1002, grr_no='GRR/A/002', gin_no='GIN/X/002', po_no='PO/Y/002',
                supplier=cls.test_supplier,
                challan_no='CH/Z/002', challan_date='2023-02-18'
            )
            cls.gqn3 = GoodsQualityNote.objects.create(
                id=3,
                fin_year_id=2024, fin_year='2024-25',
                gqn_no='GQN/003/24', sys_date='2024-03-01',
                gin_id=1003, grr_no='GRR/B/001', gin_no='GIN/P/001', po_no='PO/Q/001',
                supplier=Supplier.objects.create(supplier_id=201, supplier_name='Another Supplier'),
                challan_no='CH/R/001', challan_date='2024-02-28'
            )

        except OperationalError as e:
            print(f"Warning: Could not create test GoodsQualityNote due to managed=False. Error: {e}")
            print("Tests for GoodsQualityNote model will assume data exists or mock queries.")

    def test_gqn_creation(self):
        try:
            gqn = GoodsQualityNote.objects.get(id=1)
            self.assertEqual(gqn.gqn_no, 'GQN/001/23')
            self.assertEqual(gqn.supplier.supplier_name, 'Test Supplier Inc.')
        except GoodsQualityNote.DoesNotExist:
             self.fail("GoodsQualityNote was not found, possibly due to managed=False setup in test environment.")

    def test_gqn_str_representation(self):
        try:
            gqn = GoodsQualityNote.objects.get(id=1)
            self.assertEqual(str(gqn), 'GQN No: GQN/001/23 - Test Supplier Inc.')
        except GoodsQualityNote.DoesNotExist:
             self.fail("GoodsQualityNote was not found for str test.")

class GoodsQualityNoteViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Ensure test data exists, if not already created by setUpTestData
        try:
            self.test_supplier = Supplier.objects.get_or_create(supplier_id=200, defaults={'supplier_name': 'Test Supplier Inc.'})[0]
            self.another_supplier = Supplier.objects.get_or_create(supplier_id=201, defaults={'supplier_name': 'Another Supplier'})[0]

            self.gqn1 = GoodsQualityNote.objects.get_or_create(
                id=1,
                defaults={
                    'fin_year_id': 2023, 'fin_year': '2023-24',
                    'gqn_no': 'GQN/001/23', 'sys_date': '2023-01-15',
                    'gin_id': 1001, 'grr_no': 'GRR/A/001', 'gin_no': 'GIN/X/001', 'po_no': 'PO/Y/001',
                    'supplier': self.test_supplier,
                    'challan_no': 'CH/Z/001', 'challan_date': '2023-01-10'
                }
            )[0]
            self.gqn2 = GoodsQualityNote.objects.get_or_create(
                id=2,
                defaults={
                    'fin_year_id': 2023, 'fin_year': '2023-24',
                    'gqn_no': 'GQN/002/23', 'sys_date': '2023-02-20',
                    'gin_id': 1002, 'grr_no': 'GRR/A/002', 'gin_no': 'GIN/X/002', 'po_no': 'PO/Y/002',
                    'supplier': self.test_supplier,
                    'challan_no': 'CH/Z/002', 'challan_date': '2023-02-18'
                }
            )[0]
            self.gqn3 = GoodsQualityNote.objects.get_or_create(
                id=3,
                defaults={
                    'fin_year_id': 2024, 'fin_year': '2024-25',
                    'gqn_no': 'GQN/003/24', 'sys_date': '2024-03-01',
                    'gin_id': 1003, 'grr_no': 'GRR/B/001', 'gin_no': 'GIN/P/001', 'po_no': 'PO/Q/001',
                    'supplier': self.another_supplier,
                    'challan_no': 'CH/R/001', 'challan_date': '2024-02-28'
                }
            )[0]
        except OperationalError:
            self.skipTest("Skipping view tests because managed=False models could not be created in test DB.")
        
        # Simulate user login for LoginRequiredMixin
        self.client.force_login(self.get_or_create_user())

    def get_or_create_user(self):
        # Helper to get or create a user for login
        from django.contrib.auth.models import User
        user, created = User.objects.get_or_create(username='testuser', defaults={'password': 'testpassword'})
        return user

    # Mock get_session_context to control session values
    @patch('inventory.views.get_session_context')
    def test_list_view_initial_load(self, mock_get_session_context):
        mock_get_session_context.return_value = {'compid': 1, 'finyear': 2023}
        response = self.client.get(reverse('inventory:gqn_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsqualitynote/list.html')
        self.assertIn('search_form', response.context)
        # Initial list should be empty as table is loaded via HTMX
        self.assertEqual(len(response.context['goods_quality_notes']), 0)

    @patch('inventory.views.get_session_context')
    def test_table_partial_view_no_search(self, mock_get_session_context):
        mock_get_session_context.return_value = {'compid': 1, 'finyear': 2023}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:gqn_table'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsqualitynote/_goodsqualitynote_table.html')
        self.assertIn('goods_quality_notes', response.context)
        # Should return GQN objects for compid=1 and finyear=2023
        self.assertEqual(len(response.context['goods_quality_notes']), 2) 
        self.assertContains(response, 'GQN/001/23')
        self.assertContains(response, 'GQN/002/23')

    @patch('inventory.views.get_session_context')
    def test_table_partial_view_gqn_no_search(self, mock_get_session_context):
        mock_get_session_context.return_value = {'compid': 1, 'finyear': 2023}
        headers = {'HTTP_HX_REQUEST': 'true'}
        params = {'search_by': '1', 'search_text': 'GQN/001'}
        response = self.client.get(reverse('inventory:gqn_table'), params, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GQN/001/23')
        self.assertNotContains(response, 'GQN/002/23')
        self.assertEqual(len(response.context['goods_quality_notes']), 1)

    @patch('inventory.views.get_session_context')
    def test_table_partial_view_supplier_name_search(self, mock_get_session_context):
        mock_get_session_context.return_value = {'compid': 1, 'finyear': 2023}
        headers = {'HTTP_HX_REQUEST': 'true'}
        params = {'search_by': '0', 'supplier_name': 'Test Supplier Inc.'}
        response = self.client.get(reverse('inventory:gqn_table'), params, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GQN/001/23')
        self.assertContains(response, 'GQN/002/23')
        self.assertNotContains(response, 'GQN/003/24')
        self.assertEqual(len(response.context['goods_quality_notes']), 2)

    @patch('inventory.views.get_session_context')
    def test_supplier_autocomplete_view(self, mock_get_session_context):
        mock_get_session_context.return_value = {'compid': 1, 'finyear': 2023}
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('inventory:supplier_autocomplete'), {'supplier_name': 'alp'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        # Check if the HTML snippet contains the expected suggestion
        self.assertContains(response, 'Alpha Corp')
        self.assertNotContains(response, 'Beta Industries') # Only 'alp' matches

    @patch('inventory.views.get_session_context')
    def test_gqn_detail_view(self, mock_get_session_context):
        mock_get_session_context.return_value = {'compid': 1, 'finyear': 2023}
        response = self.client.get(reverse('inventory:gqn_detail', args=[self.gqn1.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'inventory/goodsqualitynote/detail.html')
        self.assertIn('gqn', response.context)
        self.assertEqual(response.context['gqn'].id, self.gqn1.id)
        self.assertContains(response, self.gqn1.gqn_no)
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for dynamic search:** The search form (`#searchForm`) will use `hx-get` to `{% url 'inventory:gqn_table' %}` targeting `#gqn-table-container`. This allows refreshing only the table portion. `hx-include="#searchForm"` ensures all form fields are sent with the GET request.
-   **HTMX for autocomplete:** The `supplier_name` input field uses `hx-get` to `{% url 'inventory:supplier_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms"` and `hx-target="#autocomplete-results"`.
-   **Alpine.js for UI state:**
    -   `x-data="{ searchType: '0' }"` on the main container manages the `searchType` variable.
    -   `x-model="searchType"` on the `search_by` dropdown updates `searchType`.
    -   `x-show="searchType === '0'"` and `x-show="searchType !== '0'"` on the supplier name input and generic search text input, respectively, control their visibility based on the selected search type.
-   **DataTables integration:** `list.html` includes jQuery and DataTables CDN links. A JavaScript snippet listens for `htmx:afterSwap` events on the table container. Once the partial table is loaded, it initializes DataTables on the `#gqnTable` element.
-   **Loading Indicator:** An `hx-indicator` on the search form and a manual `htmx:beforeRequest` / `htmx:afterRequest` listener for the spinner provide visual feedback during HTMX requests.
-   **No full page reloads:** All search and table updates are handled via HTMX. The "Select" action is a standard link to a detail page, which is a full page navigation, mimicking the original ASP.NET behavior.

---

## Final Notes

This modernization plan provides a structured approach to transforming the ASP.NET "Goods Quality Note [GQN] - Print" module into a robust Django application. By focusing on:

-   **AI-assisted automation:** The plan generates complete code for models, forms, views, URLs, and tests, which can be directly used or adapted by automation tools.
-   **Business Value:** The transition to Django with HTMX, Alpine.js, and DataTables will result in a more modern, responsive, and maintainable application. Users will experience faster interactions due to partial page updates (no full page reloads for search/pagination). The clear separation of concerns in Django (models for business logic, thin views, dedicated templates) will significantly improve code organization, making future enhancements easier and reducing development time and costs. The strong testing framework ensures high code quality and reliability.
-   **Non-technical language:** The explanations are designed to be accessible, allowing business stakeholders to understand the "what" and "why" of the migration without getting bogged down in "how" the code works.

This plan serves as a blueprint for implementing the GQN module in Django, adhering to best practices and setting the stage for future modernization efforts across the entire ERP system.