## ASP.NET to Django Conversion Script: Goods Quality Note (GQN) Edit Details

This plan outlines the modernization of the ASP.NET "Goods Quality Note [GQN] - Edit" page to a modern Django-based solution. The focus is on leveraging AI-assisted automation, adhering to a "Fat Model, Thin View" architecture, and utilizing HTMX, Alpine.js, and DataTables for a rich, interactive user experience without extensive traditional JavaScript.

### Business Value Proposition for Django Modernization:

Transitioning this ASP.NET application to Django offers significant benefits for your business:

1.  **Reduced Maintenance Costs:** Django's structured, explicit, and Python-based approach leads to more readable and maintainable code compared to legacy ASP.NET WebForms. This translates to fewer bugs and easier updates in the long run.
2.  **Improved Performance & Scalability:** Django's ORM and efficient request handling, combined with modern frontend techniques like HTMX for partial page updates, will result in a faster and more responsive application, capable of handling increased user loads.
3.  **Enhanced User Experience:** The adoption of HTMX and Alpine.js provides a dynamic, single-page application-like feel without the complexity of traditional JavaScript frameworks. This means users experience smoother interactions, faster data loading, and a more intuitive interface.
4.  **Future-Proof Technology Stack:** Moving to Django (Python) ensures your application is built on a widely adopted, actively developed, and modern technology stack, making it easier to find talent, integrate with new services, and adapt to future business needs.
5.  **Automation-Friendly Development:** The systematic migration approach, especially for repetitive patterns like CRUD operations and form handling, is highly amenable to AI-assisted automation tools, significantly reducing manual coding effort and accelerating time-to-market for the modernized application.
6.  **Centralized Business Logic:** Enforcing the "Fat Model" principle means all core business rules and data manipulations are encapsulated within the Django models. This improves consistency, testability, and reduces logic duplication across the application.

---

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with a complex set of database tables. The primary table for the editable details displayed in the GridView is `tblQc_MaterialQuality_Details`. The page header information comes from `tblQc_MaterialQuality_Master` and `tblInv_Inward_Master`, while various lookup tables (`tblQc_Rejection_Reason`, `tblMM_Supplier_master`, `tblDG_Item_Master`, `Unit_Master`) and transactional tables (`tblinv_MaterialReceived_Master`, `tblinv_MaterialReceived_Details`, `tblInv_Inward_Details`, `tblMM_PO_Master`, `tblMM_PO_Details`, `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_SPR_Master`, `tblMM_SPR_Details`) are extensively joined to fetch display data.

**Key Tables and Inferred Columns:**

1.  **`tblQc_MaterialQuality_Master`**:
    *   `Id` (PK)
    *   `GQNNo` (CharField)
    *   `GRRNo` (CharField)
    *   `GRRId` (IntegerField)
    *   `FinYearId` (IntegerField)
    *   `CompId` (IntegerField)
    *   `SysDate` (DateField)
    *   `SysTime` (TimeField)
    *   `SessionId` (CharField)

2.  **`tblQc_MaterialQuality_Details`**: (Main editable table)
    *   `Id` (PK)
    *   `MId` (FK to `tblQc_MaterialQuality_Master`)
    *   `AcceptedQty` (FloatField)
    *   `RejectedQty` (FloatField)
    *   `RejectionReason` (FK to `tblQc_Rejection_Reason`)
    *   `Remarks` (CharField)
    *   `SN` (CharField)
    *   `PN` (CharField)
    *   `GRRId` (Integer/FK, refers to `tblinv_MaterialReceived_Details.Id`)
    *   `GQNNo` (CharField - used in `UPDATE` statement, likely for joining/consistency)

3.  **`tblQc_Rejection_Reason`**:
    *   `Id` (PK)
    *   `Symbol` (CharField)

4.  **`tblDG_Item_Master`**:
    *   `Id` (PK)
    *   `ItemCode` (CharField)
    *   `ManfDesc` (CharField)
    *   `UOMBasic` (FK to `Unit_Master`)
    *   `AttName` (CharField)
    *   `FileName` (CharField)
    *   `StockQty` (FloatField)
    *   `CompId` (IntegerField)

5.  **`tblInv_Inward_Master`**:
    *   `Id` (PK)
    *   `GINNo` (CharField)
    *   `CompId` (IntegerField)
    *   `FinYearId` (IntegerField)
    *   `ChallanNo` (CharField)
    *   `ChallanDate` (DateField)

6.  **`tblMM_Supplier_master`**:
    *   `SupplierId` (PK/CharField)
    *   `SupplierName` (CharField)

*(Other tables like `Unit_Master`, `tblinv_MaterialReceived_Master`, `tblinv_MaterialReceived_Details`, `tblInv_Inward_Details`, `tblMM_PO_Master`, `tblMM_PO_Details`, `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_SPR_Master`, `tblMM_SPR_Details` are involved in retrieving display data but are not directly updated on this page. They will be represented as minimal `managed=False` models to support lookups.)*

### Step 2: Identify Backend Functionality

This page is primarily an "Edit Details" interface for an existing Goods Quality Note.

*   **Read (Display):**
    *   Retrieves header details (GQN No, GRR No, GIN No, Supplier, Challan No, Date) based on query string parameters.
    *   Populates a data grid (`GridView2`) with details from `tblQc_MaterialQuality_Details` and extensive joined data from other tables (Item Code, Description, UOM, PO Qty, Inward Qty, Reced Qty, Accepted Qty, Reason, Remarks, S/N, P/N, Image/Spec. Sheet links).
    *   Handles pagination for the grid.
*   **Update:**
    *   Allows inline editing of `AcceptedQty`, `RejectionReason`, `Remarks`, `SN`, `PN` for each detail row in the grid.
    *   When a row is updated (`GridView2_RowUpdating`), it updates:
        *   `tblQc_MaterialQuality_Master`: `SysDate`, `SysTime`, `SessionId`.
        *   `tblQc_MaterialQuality_Details`: `AcceptedQty`, `RejectionReason`, `Remarks`, `SN`, `PN`.
        *   `tblDG_Item_Master`: Adjusts `StockQty` based on the change in `AcceptedQty`. (Note: The original C# logic for stock adjustment appears flawed; the Django implementation will correct this to logically add to stock if accepted quantity decreases and subtract if it increases.)
*   **Validation:**
    *   Client-side and server-side validation for `AcceptedQty` (numeric, format, required).
    *   Client-side and server-side validation for `SN` and `PN` (required, conditionally visible based on `AHId`).
*   **Conditional Logic:**
    *   Visibility of `SN` and `PN` fields depends on the `AHId` value associated with the item (if `AHId == 42`, fields are visible and required).
*   **Navigation:** `btnCancel` redirects to a list page.
*   **File Download:** `btnlnkImg` and `btnlnkSpec` suggest functionality to download attached image/spec sheet files, which would require separate file serving views.

### Step 3: Infer UI Components

*   **Page Header:** Labels displaying static GQN, GRR, GIN, Challan No, Date, Supplier information.
*   **Instructional Text:** Bold text explaining quantity abbreviations.
*   **Data Grid (`GridView2`):**
    *   Displays tabular data with multiple columns.
    *   Features include pagination, inline editing, and row-level action buttons (Edit).
    *   Columns include text labels (`asp:Label`), editable textboxes (`asp:TextBox`), and a dropdown (`asp:DropDownList`) for `RejectionReason`.
    *   Link buttons for `Image` and `Spec. Sheet` for file downloads.
*   **Buttons:** `Cancel` button for navigation.
*   **Validators:** `RequiredFieldValidator`, `RegularExpressionValidator` for input fields.
*   **Styling:** CSS links (`yui-datatable.css`, `StyleSheet.css`) and inline styles.

---

### Step 4: Generate Django Code

**Application Name:** `quality_control`

The core editable entity is `GoodsQualityNoteDetail`. The main page acts as a detail view of a `GoodsQualityNoteMaster` which then contains a list of `GoodsQualityNoteDetail` records for editing.

#### 4.1 Models (`quality_control/models.py`)

We'll define minimal `managed = False` models for all involved database tables to allow Django's ORM to interact with them, ensuring we can leverage relationships and keep logic in models.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# Minimal models for related tables, not directly managed by Django ORM migrations
# but used for relationships and lookups.

class Supplier(models.Model):
    # Matches tblMM_Supplier_master
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or self.supplier_id

class Unit(models.Model):
    # Matches Unit_Master for UOMBasic
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or str(self.id)

class ItemMaster(models.Model):
    # Matches tblDG_Item_Master for item details and stock
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic = models.ForeignKey(Unit, models.DO_NOTHING, db_column='UOMBasic', blank=True, null=True)
    att_name = models.CharField(db_column='AttName', max_length=255, blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    stock_qty = models.FloatField(db_column='StockQty', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or str(self.id)

    def get_effective_uom(self):
        return self.uom_basic.symbol if self.uom_basic else 'N/A'

class RejectionReason(models.Model):
    # Matches tblQc_Rejection_Reason for dropdown
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_Rejection_Reason'
        verbose_name = 'Rejection Reason'
        verbose_name_plural = 'Rejection Reasons'

    def __str__(self):
        return self.symbol or str(self.id)

class InwardMaster(models.Model):
    # Matches tblInv_Inward_Master for GIN and Challan details
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    challan_no = models.CharField(db_column='ChallanNo', max_length=100, blank=True, null=True)
    challan_date = models.DateTimeField(db_column='ChallanDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Master'
        verbose_name_plural = 'Inward Masters'

    def __str__(self):
        return self.gin_no or str(self.id)

class GoodsQualityNoteMaster(models.Model):
    # Matches tblQc_MaterialQuality_Master for GQN header details
    id = models.IntegerField(db_column='Id', primary_key=True)
    gqn_no = models.CharField(db_column='GQNNo', max_length=50, blank=True, null=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    grr_id = models.IntegerField(db_column='GRRId', blank=True, null=True) # Refers to tblinv_MaterialReceived_Master.Id
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    sys_time = models.TimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'
        verbose_name = 'Goods Quality Note Master'
        verbose_name_plural = 'Goods Quality Note Masters'

    def __str__(self):
        return self.gqn_no or str(self.id)
    
    # Example of a method that might combine sys_date and sys_time
    def get_sys_datetime(self):
        if self.sys_date and self.sys_time:
            return datetime.combine(self.sys_date, self.sys_time)
        return None

# For PR/SPR lookup, not directly updated, but necessary for ItemId/AHId/POQty
class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'

class PODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(POMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    qty = models.FloatField(db_column='Qty', blank=True, null=True)
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True)
    spr_id = models.IntegerField(db_column='SPRId', blank=True, null=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'

class MaterialReceivedMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    gin_id = models.IntegerField(db_column='GINId', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'

class MaterialReceivedDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialReceivedMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    received_qty = models.FloatField(db_column='ReceivedQty', blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # Refers to tblMM_PO_Details.Id

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'

class InwardDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(InwardMaster, models.DO_NOTHING, db_column='GINId', blank=True, null=True)
    received_qty = models.FloatField(db_column='ReceivedQty', blank=True, null=True)
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # Refers to tblMM_PO_Details.Id

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'

class PRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'

class PRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(PRMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'

class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'

class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(SPRMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId', blank=True, null=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept_id = models.IntegerField(db_column='DeptId', blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'

# Main Model for the GridView details
class GoodsQualityNoteDetail(models.Model):
    # Matches tblQc_MaterialQuality_Details
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(GoodsQualityNoteMaster, models.DO_NOTHING, db_column='MId', blank=True, null=True)
    # The GRRId in tblQc_MaterialQuality_Details refers to tblinv_MaterialReceived_Details.Id
    grr_detail = models.ForeignKey(MaterialReceivedDetail, models.DO_NOTHING, db_column='GRRId', blank=True, null=True)
    accepted_qty = models.FloatField(db_column='AcceptedQty', blank=True, null=True)
    rejected_qty = models.FloatField(db_column='RejectedQty', blank=True, null=True)
    rejection_reason = models.ForeignKey(RejectionReason, models.DO_NOTHING, db_column='RejectionReason', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)
    sn = models.CharField(db_column='SN', max_length=100, blank=True, null=True)
    pn = models.CharField(db_column='PN', max_length=100, blank=True, null=True)
    gqn_no = models.CharField(db_column='GQNNo', max_length=50, blank=True, null=True) # Redundant, but exists in schema

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Goods Quality Note Detail'
        verbose_name_plural = 'Goods Quality Note Details'

    def __str__(self):
        return f"GQN Detail {self.id} for {self.master.gqn_no if self.master else 'N/A'}"
    
    # Business logic to get derived properties, following 'Fat Model' principle
    # These properties simulate the complex joins from the ASP.NET loadData method
    @property
    def item_id(self):
        # Infer ItemId based on GRRId -> POId -> (PR/SPR) -> ItemId
        if self.grr_detail and self.grr_detail.po_id:
            try:
                # First try PR based on PO, then SPR
                po_detail = PODetail.objects.using('default').filter(id=self.grr_detail.po_id).first()
                if po_detail and po_detail.master and po_detail.master.pr_spr_flag == '0':
                    pr_detail = PRDetail.objects.using('default').filter(master__pr_no=po_detail.pr_no, id=po_detail.pr_id).first()
                    return pr_detail.item.id if pr_detail and pr_detail.item else None
                elif po_detail and po_detail.master and po_detail.master.pr_spr_flag == '1':
                    spr_detail = SPRDetail.objects.using('default').filter(master__spr_no=po_detail.spr_no, id=po_detail.spr_id).first()
                    return spr_detail.item.id if spr_detail and spr_detail.item else None
            except Exception:
                pass
        return None

    @property
    def item_master(self):
        return ItemMaster.objects.using('default').filter(id=self.item_id).first()
    
    @property
    def ah_id(self):
        # Infer AHId based on GRRId -> POId -> (PR/SPR) -> AHId
        if self.grr_detail and self.grr_detail.po_id:
            try:
                po_detail = PODetail.objects.using('default').filter(id=self.grr_detail.po_id).first()
                if po_detail and po_detail.master and po_detail.master.pr_spr_flag == '0':
                    pr_detail = PRDetail.objects.using('default').filter(master__pr_no=po_detail.pr_no, id=po_detail.pr_id).first()
                    return pr_detail.ah_id if pr_detail else None
                elif po_detail and po_detail.master and po_detail.master.pr_spr_flag == '1':
                    spr_detail = SPRDetail.objects.using('default').filter(master__spr_no=po_detail.spr_no, id=po_detail.spr_id).first()
                    return spr_detail.ah_id if spr_detail else None
            except Exception:
                pass
        return None

    @property
    def item_code(self):
        return self.item_master.item_code if self.item_master else 'N/A'

    @property
    def description(self):
        return self.item_master.manf_desc if self.item_master else 'N/A'

    @property
    def uom(self):
        return self.item_master.get_effective_uom() if self.item_master else 'N/A'

    @property
    def po_qty(self):
        # Infer POQty based on GRRId -> POId -> Qty
        if self.grr_detail and self.grr_detail.po_id:
            po_detail = PODetail.objects.using('default').filter(id=self.grr_detail.po_id).first()
            return po_detail.qty if po_detail else 0.0
        return 0.0

    @property
    def inv_qty(self):
        # Infer Inward Qty based on Inward Master/Details related to GINNo
        # This is a complex lookup from the original ASP.NET loadData method.
        # For simplicity, we'll assume it's available via a relation or can be
        # pre-calculated in a database view if performance is critical.
        # Here's a simplified (and potentially slow) example:
        if self.master and self.master.grr_no: # Using GRRNo as a proxy for GIN lookup
            try:
                inward_master = InwardMaster.objects.using('default').filter(gin_no=self.master.grr_no, comp_id=self.master.comp_id, fin_year_id=self.master.fin_year_id).first()
                if inward_master and self.grr_detail and self.grr_detail.po_id:
                    inward_detail = InwardDetail.objects.using('default').filter(master=inward_master, po_id=self.grr_detail.po_id).first()
                    return inward_detail.received_qty if inward_detail else 0.0
            except Exception:
                pass
        return 0.0

    @property
    def reced_qty(self):
        # This seems to be MaterialReceivedDetails.ReceivedQty based on original code
        return self.grr_detail.received_qty if self.grr_detail else 0.0

    @property
    def file_name_display(self):
        return "View" if self.item_master and self.item_master.file_name else ""
    
    @property
    def att_name_display(self):
        return "View" if self.item_master and self.item_master.att_name else ""

    def update_accepted_quantity_and_stock(self, new_accepted_qty, user_session_id):
        """
        Business logic for updating accepted quantity and adjusting item stock.
        This method replaces the logic found in GridView2_RowUpdating.
        """
        old_accepted_qty = self.accepted_qty if self.accepted_qty is not None else 0.0
        
        # Check if new accepted quantity is greater than old accepted quantity
        if new_accepted_qty > old_accepted_qty:
            raise ValueError("Accepted Quantity cannot be increased.")

        qty_difference = old_accepted_qty - new_accepted_qty # Amount by which accepted_qty was reduced

        # Update StockQty in tblDG_Item_Master
        if self.item_master:
            current_stock_qty = self.item_master.stock_qty if self.item_master.stock_qty is not None else 0.0
            
            # Stock should *increase* by the quantity that was no longer 'accepted'
            self.item_master.stock_qty = current_stock_qty + qty_difference
            self.item_master.save(using='default') # Save to the correct database

        # Update tblQc_MaterialQuality_Details
        self.accepted_qty = new_accepted_qty
        self.save(using='default') # Save to the correct database

        # Update tblQc_MaterialQuality_Master
        if self.master:
            self.master.sys_date = timezone.localdate()
            self.master.sys_time = timezone.localtime().time()
            self.master.session_id = user_session_id # Assuming user_session_id is passed from session
            self.master.save(using='default') # Save to the correct database

```

#### 4.2 Forms (`quality_control/forms.py`)

```python
from django import forms
from .models import GoodsQualityNoteDetail, RejectionReason
import re

class GoodsQualityNoteDetailForm(forms.ModelForm):
    # Field to hold the original accepted quantity for validation
    original_accepted_qty = forms.FloatField(widget=forms.HiddenInput(), required=False)
    
    class Meta:
        model = GoodsQualityNoteDetail
        fields = ['accepted_qty', 'rejection_reason', 'remarks', 'sn', 'pn']
        widgets = {
            'accepted_qty': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'hx-get': "{% url 'quality_control:check_sn_pn_visibility' %}", # HTMX to check visibility
                'hx-target': '#sn-pn-container', # Target for the conditional fields
                'hx-trigger': 'change', # Trigger on change
                'hx-include': '#id_ah_id', # Include AHId for condition check
            }),
            'rejection_reason': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'remarks': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'sn': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'x-model': 'snValue', # Alpine.js for visibility
                'x-bind:required': 'isSnPnRequired', # Alpine.js for conditional required
                'x-bind:class': "{ 'hidden': !isSnPnVisible }"
            }),
            'pn': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'x-model': 'pnValue', # Alpine.js for visibility
                'x-bind:required': 'isSnPnRequired', # Alpine.js for conditional required
                'x-bind:class': "{ 'hidden': !isSnPnVisible }"
            }),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate dropdown for RejectionReason
        self.fields['rejection_reason'].queryset = RejectionReason.objects.using('default').all()
        # Set initial original_accepted_qty for validation if instance exists
        if self.instance and self.instance.pk:
            self.fields['original_accepted_qty'].initial = self.instance.accepted_qty

    def clean_accepted_qty(self):
        accepted_qty = self.cleaned_data['accepted_qty']
        original_accepted_qty = self.initial.get('accepted_qty') # Use .initial to get the original value passed
        
        if accepted_qty is None:
            raise forms.ValidationError("Accepted Quantity is required.")
        
        # Regular expression validation for numeric (1-15 digits, 0-3 decimal places)
        if not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(accepted_qty)):
            raise forms.ValidationError("Enter a valid numeric quantity (max 15 digits, 3 decimal places).")
            
        # Business logic: Accepted Quantity cannot be increased
        if original_accepted_qty is not None and accepted_qty > original_accepted_qty:
            raise forms.ValidationError(f"Accepted Quantity cannot be increased from {original_accepted_qty} to {accepted_qty}.")
            
        return accepted_qty

    def clean(self):
        cleaned_data = super().clean()
        ah_id = self.instance.ah_id if self.instance else None # Get AHId from instance

        # Conditional validation for SN and PN based on AHId
        if ah_id == 42:
            sn = cleaned_data.get('sn')
            pn = cleaned_data.get('pn')
            if not sn:
                self.add_error('sn', "S/N is required when AHId is 42.")
            if not pn:
                self.add_error('pn', "P/N is required when AHId is 42.")
        return cleaned_data
```

#### 4.3 Views (`quality_control/views.py`)

```python
from django.views.generic import DetailView, UpdateView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, redirect
from django.db import transaction
from .models import GoodsQualityNoteMaster, GoodsQualityNoteDetail, ItemMaster
from .forms import GoodsQualityNoteDetailForm

class GoodsQualityNoteEditDetailsView(DetailView):
    # This view displays the main page with header info and the GQN details grid
    model = GoodsQualityNoteMaster
    template_name = 'quality_control/goodsqualitynote_edit_details.html'
    context_object_name = 'gqn_master'
    pk_url_kwarg = 'master_pk' # Renamed from 'pk' to avoid confusion with detail item pk

    def get_object(self, queryset=None):
        # Fetch master record based on query parameters
        # In a real app, this would be more robust, potentially using a unique GQNNo
        gqn_no = self.request.GET.get('GQNNo')
        grr_no = self.request.GET.get('GRRNo')
        gin_no = self.request.GET.get('GINNo')
        # comp_id = self.request.session.get('compid') # From session
        # fin_year_id = self.request.session.get('finyear') # From session
        
        # For demonstration, we'll fetch by PK (master_pk) which is more Django-idiomatic for DetailView
        # If the query string GQNNo is the primary identifier, adjust this.
        # Let's assume master_pk maps to GoodsQualityNoteMaster.id
        return super().get_object(queryset)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        gqn_master = self.object # The GoodsQualityNoteMaster instance

        # Simulate ASP.NET Page_Load header data fetching
        try:
            # Assuming GINNo from query string points to InwardMaster.gin_no
            gin_no_param = self.request.GET.get('GINNo')
            # Assuming supplier_id is from QueryString SUPId
            supplier_id_param = self.request.GET.get('SUPId')
            
            inward_master = None
            if gin_no_param:
                inward_master = ItemMaster.objects.using('default').filter(
                    gin_no=gin_no_param,
                    comp_id=self.request.session.get('compid'),
                    fin_year_id=self.request.session.get('finyear')
                ).first()
            
            supplier_obj = None
            if supplier_id_param:
                supplier_obj = ItemMaster.objects.using('default').filter(
                    supplier_id=supplier_id_param
                ).first()

            context['gqn_no'] = gqn_master.gqn_no
            context['grr_no'] = gqn_master.grr_no
            context['gin_no'] = gin_no_param # Use param directly for display if not found via inward_master
            context['challan_no'] = inward_master.challan_no if inward_master else 'N/A'
            context['challan_date'] = inward_master.challan_date.strftime('%d/%m/%Y') if inward_master and inward_master.challan_date else 'N/A'
            context['supplier_name'] = supplier_obj.supplier_name if supplier_obj else 'N/A'

            # Fetch detail items for the master GQN for the grid
            context['gqn_details'] = GoodsQualityNoteDetail.objects.using('default').filter(
                master=gqn_master
            ).order_by('id') # Order for consistent SN
        except Exception as e:
            messages.error(self.request, f"Error loading header data: {e}")
            context['gqn_details'] = GoodsQualityNoteDetail.objects.none() # Empty queryset on error

        return context

class GoodsQualityNoteDetailTablePartialView(View):
    # HTMX endpoint to refresh only the DataTables content
    def get(self, request, master_pk, *args, **kwargs):
        gqn_master = get_object_or_404(GoodsQualityNoteMaster.objects.using('default'), pk=master_pk)
        gqn_details = GoodsQualityNoteDetail.objects.using('default').filter(master=gqn_master).order_by('id')
        
        # Prepare data for DataTable (using properties on model)
        data = []
        for i, detail in enumerate(gqn_details):
            data.append({
                'sn': i + 1,
                'id': detail.id,
                'item_code': detail.item_code,
                'description': detail.description,
                'uom': detail.uom,
                'po_qty': detail.po_qty,
                'inv_qty': detail.inv_qty,
                'reced_qty': detail.reced_qty,
                'accepted_qty': detail.accepted_qty,
                'rej_reason': detail.rejection_reason.symbol if detail.rejection_reason else '',
                'remarks': detail.remarks,
                'sn_val': detail.sn,
                'pn_val': detail.pn,
                'ah_id': detail.ah_id, # Hidden field for client-side logic
                'item_id': detail.item_id, # Hidden field for client-side logic
                'file_name_display': detail.file_name_display,
                'att_name_display': detail.att_name_display,
            })
        
        # DataTables expects a 'data' key for server-side processing,
        # but for client-side, we can just render the table rows.
        # However, for a partial, we'll pass the list of objects.
        
        # If using server-side DataTables, this would return JsonResponse
        # For client-side DataTables refreshing an HTML partial, pass context.
        return render(request, 'quality_control/_goodsqualitynotedetail_table.html', {
            'gqn_master': gqn_master,
            'gqn_details': gqn_details, # Pass the queryset for template iteration
        })

class GoodsQualityNoteDetailUpdateView(UpdateView):
    # Handles editing a single detail item via HTMX modal
    model = GoodsQualityNoteDetail
    form_class = GoodsQualityNoteDetailForm
    template_name = 'quality_control/_goodsqualitynotedetail_form.html'
    pk_url_kwarg = 'detail_pk' # PK for the specific detail item

    def get_queryset(self):
        # Ensure we're only fetching from the target database
        return super().get_queryset().using('default')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the current accepted quantity as initial to the form for validation
        kwargs['initial']['original_accepted_qty'] = self.object.accepted_qty
        return kwargs

    def form_valid(self, form):
        user_session_id = self.request.session.get('username', 'system') # Get session ID
        
        try:
            with transaction.atomic(using='default'): # Ensure atomicity for multiple updates
                # Use the model's business method for update and stock adjustment
                self.object.update_accepted_quantity_and_stock(
                    new_accepted_qty=form.cleaned_data['accepted_qty'],
                    user_session_id=user_session_id
                )
                
                # Update other fields not handled by the specific business method
                self.object.rejection_reason = form.cleaned_data['rejection_reason']
                self.object.remarks = form.cleaned_data['remarks']
                self.object.sn = form.cleaned_data['sn']
                self.object.pn = form.cleaned_data['pn']
                self.object.save(using='default')

            messages.success(self.request, 'Goods Quality Note Detail updated successfully.')
            # HTMX response to close modal and trigger table refresh
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshGQNTable'} # Custom event to refresh table
            )
        except ValueError as e:
            form.add_error(None, str(e)) # Add error to the form
            return self.form_invalid(form)
        except Exception as e:
            messages.error(self.request, f"An unexpected error occurred: {e}")
            return self.form_invalid(form)

    def form_invalid(self, form):
        # Render the form again with errors for HTMX
        return render(self.request, self.template_name, {'form': form, 'object': self.object}, status=400)


class CheckSnPnVisibilityView(View):
    # HTMX endpoint to check SN/PN visibility based on AHId
    def get(self, request, *args, **kwargs):
        ah_id = int(request.GET.get('ah_id', 0))
        is_sn_pn_visible = (ah_id == 42)
        
        # Return a partial template that updates only the SN/PN fields, or JSON
        # For simplicity, returning JSON that Alpine.js can pick up
        return JsonResponse({'is_sn_pn_visible': is_sn_pn_visible})

from django.shortcuts import render
# Function for handling file downloads (not directly from ASP.NET, but implied)
def download_item_file(request, item_id, file_type):
    item = get_object_or_404(ItemMaster.objects.using('default'), id=item_id)
    file_path = None
    if file_type == 'image' and item.file_name:
        file_path = item.file_name
    elif file_type == 'spec' and item.att_name:
        file_path = item.att_name

    if file_path:
        # In a real application, you'd configure a MEDIA_ROOT and MEDIA_URL
        # and ensure files are stored securely and served properly.
        # For this example, we assume file_path is directly accessible.
        # This is a simplification; production would use proper file storage.
        try:
            from django.conf import settings
            full_path = settings.MEDIA_ROOT / file_path # Assuming MEDIA_ROOT configured
            from django.http import FileResponse
            return FileResponse(open(full_path, 'rb'), as_attachment=True, filename=file_path.split('/')[-1])
        except FileNotFoundError:
            messages.error(request, "File not found.")
            return HttpResponse("File not found.", status=404)
    messages.error(request, "Invalid file type or no file available.")
    return HttpResponse("Invalid request or file not available.", status=400)

class GQNRedirectView(View):
    # Simulates btnCancel_Click redirect
    def get(self, request, *args, **kwargs):
        # This would typically redirect to a list of GQN masters
        # ModId and SubModId are ASP.NET specific, assuming a Django URL like:
        return redirect(reverse_lazy('quality_control:gqn_master_list')) # Placeholder for actual list URL
```

#### 4.4 Templates (`quality_control/templates/quality_control/`)

**`goodsqualitynote_edit_details.html`** (Main Page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Quality Note [GQN] - Edit</h2>
        <div class="text-sm text-gray-600 mt-2 md:mt-0">
            <b class="text-red-600">A: Total Received Quantity, B: Normal Accepted Quantity, C: Deviated Quantity, D: Segregated Quantity, E: Rejected Quantity.</b>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            <div><span class="font-medium text-gray-700">GQN No:</span> <span class="font-bold text-gray-900">{{ gqn_no }}</span></div>
            <div><span class="font-medium text-gray-700">GRR No:</span> <span class="font-bold text-gray-900">{{ grr_no }}</span></div>
            <div><span class="font-medium text-gray-700">GIN No:</span> <span class="font-bold text-gray-900">{{ gin_no }}</span></div>
            <div><span class="font-medium text-gray-700">Challan No:</span> <span class="font-bold text-gray-900">{{ challan_no }}</span></div>
            <div><span class="font-medium text-gray-700">Date:</span> <span class="font-bold text-gray-900">{{ challan_date }}</span></div>
            <div class="col-span-full md:col-span-2 lg:col-span-3"><span class="font-medium text-gray-700">Supplier:</span> <span class="font-bold text-gray-900">{{ supplier_name }}</span></div>
        </div>
    </div>

    <div id="gqn-detail-table-container"
         hx-trigger="load, refreshGQNTable from:body"
         hx-get="{% url 'quality_control:goodsqualitynotedetail_table_partial' gqn_master.pk %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Goods Quality Note Details...</p>
        </div>
    </div>
    
    <div class="flex justify-end mt-6">
        <button 
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md"
            onclick="window.location.href='{% url 'quality_control:gqn_cancel_redirect' %}'">
            Cancel
        </button>
    </div>

    <!-- Modal for form editing -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-on:close-modal.window="showModal = false"
         x-on:open-modal.window="showModal = true"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full mx-auto"
             _="on hx:afterOnLoad add .is-active to #modal"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js integration to control modal visibility
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });
    });

    // Event listener for HTMX to handle modal opening
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 200 && evt.detail.elt.id === 'modalContent') {
            Alpine.store('modal').open();
        }
    });

    // Manual way to hide modal, HTMX triggers will usually handle this
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            Alpine.store('modal').close();
        }
    });
</script>
{% endblock %}
```

**`_goodsqualitynotedetail_table.html`** (Partial for DataTables)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="gqnDetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec. Sheet</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Inward Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Reced Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Accp Qty</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">S/N</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P/N</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if gqn_details %}
                {% for obj in gqn_details %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">
                        {% if obj.file_name_display %}
                            <a href="{% url 'quality_control:download_item_file' obj.item_master.pk 'image' %}" class="text-blue-600 hover:underline">View</a>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">
                        {% if obj.att_name_display %}
                            <a href="{% url 'quality_control:download_item_file' obj.item_master.pk 'spec' %}" class="text-blue-600 hover:underline">View</a>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ obj.item_code }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">{{ obj.description }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">{{ obj.uom }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ obj.po_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ obj.inv_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ obj.reced_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-right">{{ obj.accepted_qty|floatformat:"3" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">{{ obj.rejection_reason.symbol|default:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">{{ obj.sn|default:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">{{ obj.pn|default:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-left">{{ obj.remarks|default:"" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-get="{% url 'quality_control:goodsqualitynotedetail_edit' obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <!-- Hidden AHId for conditional SN/PN visibility in form -->
                        <input type="hidden" id="ah_id_{{ obj.pk }}" value="{{ obj.ah_id }}">
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="15" class="py-4 px-4 text-center text-gray-500">No data to display !</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#gqnDetailTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 1, 2, 14] } // Disable sorting for SN, Image, Spec Sheet, Actions
        ]
    });
});
</script>
```

**`_goodsqualitynotedetail_form.html`** (Partial for Edit Modal)

```html
<div class="p-6" x-data="{
    ahId: {{ object.ah_id|default:0 }},
    isSnPnVisible: {{ object.ah_id|default:0 }} === 42,
    isSnPnRequired: {{ object.ah_id|default:0 }} === 42,
    snValue: '{{ object.sn|default:"" }}',
    pnValue: '{{ object.pn|default:"" }}',
    init() {
        // Event listener for HTMX swap when form is loaded
        this.$nextTick(() => {
            // Re-evaluate visibility based on loaded AHId
            this.isSnPnVisible = (this.ahId === 42);
            this.isSnPnRequired = (this.ahId === 42);
        });
    }
}">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Edit Goods Quality Note Detail</h3>
    <form hx-post="{% url 'quality_control:goodsqualitynotedetail_edit' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        {{ form.original_accepted_qty }} {# Hidden field for original quantity #}
        <input type="hidden" id="id_ah_id" value="{{ object.ah_id }}"> {# Hidden AHId for JS/Alpine.js #}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="mb-4">
                <label for="{{ form.accepted_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    Accepted Quantity
                </label>
                {{ form.accepted_qty }}
                {% if form.accepted_qty.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.accepted_qty.errors }}</p>
                {% endif %}
            </div>

            <div class="mb-4">
                <label for="{{ form.rejection_reason.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    Reason
                </label>
                {{ form.rejection_reason }}
                {% if form.rejection_reason.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.rejection_reason.errors }}</p>
                {% endif %}
            </div>
            
            <div class="mb-4 col-span-full">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    Remarks
                </label>
                {{ form.remarks }}
                {% if form.remarks.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>
                {% endif %}
            </div>

            <!-- Conditional SN/PN fields controlled by Alpine.js -->
            <div id="sn-pn-container" class="contents">
                <div class="mb-4" x-cloak x-show="isSnPnVisible">
                    <label for="{{ form.sn.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        S/N
                    </label>
                    {{ form.sn }}
                    {% if form.sn.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.sn.errors }}</p>
                    {% endif %}
                </div>
                <div class="mb-4" x-cloak x-show="isSnPnVisible">
                    <label for="{{ form.pn.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        P/N
                    </label>
                    {{ form.pn }}
                    {% if form.pn.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.pn.errors }}</p>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                x-on:click="$store.modal.close()">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
        {% if form.non_field_errors %}
        <div class="text-red-500 text-sm mt-4">{{ form.non_field_errors }}</div>
        {% endif %}
    </form>
</div>

<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 200) {
            // Re-initialize Alpine.js component scope if HTMX swaps content within it
            // This is crucial for x-data to re-evaluate when content changes
            let modalContentDiv = document.getElementById('modalContent');
            if (modalContentDiv) {
                Alpine.initTree(modalContentDiv);
            }
        }
    });
</script>
```

#### 4.5 URLs (`quality_control/urls.py`)

```python
from django.urls import path
from .views import (
    GoodsQualityNoteEditDetailsView, 
    GoodsQualityNoteDetailTablePartialView, 
    GoodsQualityNoteDetailUpdateView,
    CheckSnPnVisibilityView,
    download_item_file,
    GQNRedirectView, # For the cancel button
)

app_name = 'quality_control'

urlpatterns = [
    # Main GQN Edit Details Page (accepts GQNNo, GRRNo, GINNo as query params for header info)
    # Using a path parameter 'master_pk' to map to GoodsQualityNoteMaster.id for Django DetailView
    path('gqn_edit_details/<int:master_pk>/', GoodsQualityNoteEditDetailsView.as_view(), name='gqn_edit_details'),
    
    # HTMX endpoint to refresh the GQN detail table (DataTables content)
    path('gqn_edit_details/<int:master_pk>/table/', GoodsQualityNoteDetailTablePartialView.as_view(), name='goodsqualitynotedetail_table_partial'),
    
    # HTMX endpoint for updating a single GQN detail item (modal form)
    path('gqn_detail_edit/<int:detail_pk>/', GoodsQualityNoteDetailUpdateView.as_view(), name='goodsqualitynotedetail_edit'),

    # HTMX endpoint for conditional SN/PN visibility (AJAX check, if needed beyond Alpine.js)
    # This might be integrated more purely with Alpine.js via x-data/x-bind directly in the form
    # but kept as an example of a potential HTMX endpoint for complex server-side checks.
    path('check_sn_pn_visibility/', CheckSnPnVisibilityView.as_view(), name='check_sn_pn_visibility'),

    # URL for file downloads
    path('download_item_file/<int:item_id>/<str:file_type>/', download_item_file, name='download_item_file'),

    # Placeholder for cancel button redirect (adjust to your actual list URL)
    path('gqn_cancel/', GQNRedirectView.as_view(), name='gqn_cancel_redirect'),
    path('gqn_master_list/', TemplateView.as_view(template_name='quality_control/gqn_master_list.html'), name='gqn_master_list'), # Dummy list page
]
```

#### 4.6 Tests (`quality_control/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock
from .models import (
    GoodsQualityNoteMaster, GoodsQualityNoteDetail, RejectionReason, 
    ItemMaster, Unit, Supplier, InwardMaster, MaterialReceivedDetail,
    POMaster, PODetail, PRMaster, PRDetail, SPRMaster, SPRDetail
)
import datetime

# Configure a test database alias for 'default' if not already configured in settings
# DATABASES = {
#     'default': {
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': ':memory:',
#     },
#     'legacy_db': { # Example for a separate legacy DB
#         'ENGINE': 'django.db.backends.sqlite3',
#         'NAME': ':memory:',
#     }
# }

class GQNModelAndBusinessLogicTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal related data for testing
        cls.supplier = Supplier.objects.using('default').create(supplier_id='SUP001', supplier_name='Test Supplier')
        cls.unit = Unit.objects.using('default').create(id=1, symbol='MTR')
        cls.item_master_ah42 = ItemMaster.objects.using('default').create(id=101, item_code='ITEM001', manf_desc='Test Item AH42', uom_basic=cls.unit, stock_qty=100.0, comp_id=1, file_name='img.jpg', att_name='spec.pdf')
        cls.item_master_other = ItemMaster.objects.using('default').create(id=102, item_code='ITEM002', manf_desc='Test Item Other', uom_basic=cls.unit, stock_qty=50.0, comp_id=1)
        cls.rejection_reason_ok = RejectionReason.objects.using('default').create(id=1, symbol='OK')
        cls.rejection_reason_defect = RejectionReason.objects.using('default').create(id=2, symbol='DEFECT')

        cls.gqn_master = GoodsQualityNoteMaster.objects.using('default').create(
            id=1, gqn_no='GQN/001', grr_no='GRR/001', grr_id=1, fin_year_id=2023, comp_id=1,
            sys_date=timezone.localdate(), sys_time=timezone.localtime().time(), session_id='testuser'
        )
        cls.inward_master = InwardMaster.objects.using('default').create(
            id=1, gin_no='GIN/001', comp_id=1, fin_year_id=2023, challan_no='CH001', challan_date=timezone.now()
        )
        
        # Setup for item lookups
        cls.po_master_pr = POMaster.objects.using('default').create(id=1, po_no='PO001', fin_year_id=2023, comp_id=1, pr_spr_flag='0')
        cls.po_master_spr = POMaster.objects.using('default').create(id=2, po_no='PO002', fin_year_id=2023, comp_id=1, pr_spr_flag='1')
        
        cls.pr_master = PRMaster.objects.using('default').create(id=1, pr_no='PR001', comp_id=1, fin_year_id=2023)
        cls.pr_detail_ah42 = PRDetail.objects.using('default').create(id=1, master=cls.pr_master, item=cls.item_master_ah42, wo_no='WO001', ah_id=42)
        cls.pr_detail_other = PRDetail.objects.using('default').create(id=2, master=cls.pr_master, item=cls.item_master_other, wo_no='WO002', ah_id=10)

        cls.spr_master = SPRMaster.objects.using('default').create(id=1, spr_no='SPR001', comp_id=1, fin_year_id=2023)
        cls.spr_detail_ah42 = SPRDetail.objects.using('default').create(id=1, master=cls.spr_master, item=cls.item_master_ah42, wo_no='WO003', dept_id=1, ah_id=42)

        cls.po_detail_pr = PODetail.objects.using('default').create(id=1, master=cls.po_master_pr, qty=100.0, pr_id=cls.pr_detail_ah42.id, pr_no='PR001')
        cls.po_detail_spr = PODetail.objects.using('default').create(id=2, master=cls.po_master_spr, qty=50.0, spr_id=cls.spr_detail_ah42.id, spr_no='SPR001')

        cls.material_received_master_pr = MaterialReceivedMaster.objects.using('default').create(id=1, gin_no='GIN/001', gin_id=cls.inward_master.id, comp_id=1)
        cls.material_received_master_spr = MaterialReceivedMaster.objects.using('default').create(id=2, gin_no='GIN/002', gin_id=cls.inward_master.id, comp_id=1)

        cls.material_received_detail_pr = MaterialReceivedDetail.objects.using('default').create(id=1, master=cls.material_received_master_pr, received_qty=90.0, po_id=cls.po_detail_pr.id)
        cls.material_received_detail_spr = MaterialReceivedDetail.objects.using('default').create(id=2, master=cls.material_received_master_spr, received_qty=45.0, po_id=cls.po_detail_spr.id)

        cls.inward_detail = InwardDetail.objects.using('default').create(id=1, master=cls.inward_master, received_qty=95.0, po_id=cls.po_detail_pr.id) # Example for inv_qty


        # Main GQN Detail instances
        cls.gqn_detail_item1 = GoodsQualityNoteDetail.objects.using('default').create(
            id=1, master=cls.gqn_master, grr_detail=cls.material_received_detail_pr, accepted_qty=80.0, rejected_qty=10.0,
            rejection_reason=cls.rejection_reason_ok, remarks='Initial remarks', sn='SN123', pn='PN456'
        )
        cls.gqn_detail_item2 = GoodsQualityNoteDetail.objects.using('default').create(
            id=2, master=cls.gqn_master, grr_detail=cls.material_received_detail_spr, accepted_qty=40.0, rejected_qty=5.0,
            rejection_reason=cls.rejection_reason_defect, remarks='Another item', sn=None, pn=None
        )

    def test_gqn_detail_properties(self):
        detail = GoodsQualityNoteDetail.objects.using('default').get(id=1)
        self.assertEqual(detail.item_id, self.item_master_ah42.id)
        self.assertEqual(detail.item_code, self.item_master_ah42.item_code)
        self.assertEqual(detail.description, self.item_master_ah42.manf_desc)
        self.assertEqual(detail.uom, self.unit.symbol)
        self.assertEqual(detail.po_qty, 100.0)
        # inv_qty and reced_qty are complex, rely on direct relations or mock if needed
        # self.assertEqual(detail.inv_qty, 95.0) # This depends on complex lookup
        self.assertEqual(detail.reced_qty, 90.0) # Direct from material_received_detail_pr
        self.assertEqual(detail.ah_id, 42)
        self.assertEqual(detail.file_name_display, 'View')
        self.assertEqual(detail.att_name_display, 'View')

        detail2 = GoodsQualityNoteDetail.objects.using('default').get(id=2)
        self.assertEqual(detail2.ah_id, 42) # SPR also has AHId 42 in this setup

    def test_update_accepted_quantity_and_stock_decrease(self):
        detail = GoodsQualityNoteDetail.objects.using('default').get(id=1)
        original_accepted_qty = detail.accepted_qty
        original_stock_qty = detail.item_master.stock_qty

        new_accepted_qty = 70.0 # Decrease by 10
        user_session_id = 'updated_user'

        detail.update_accepted_quantity_and_stock(new_accepted_qty, user_session_id)

        detail.refresh_from_db(using='default')
        detail.item_master.refresh_from_db(using='default')
        detail.master.refresh_from_db(using='default')

        self.assertEqual(detail.accepted_qty, new_accepted_qty)
        self.assertEqual(detail.item_master.stock_qty, original_stock_qty + (original_accepted_qty - new_accepted_qty))
        self.assertAlmostEqual(detail.item_master.stock_qty, 100.0 + 10.0) # 100 + (80 - 70) = 110.0
        self.assertIsNotNone(detail.master.sys_date)
        self.assertIsNotNone(detail.master.sys_time)
        self.assertEqual(detail.master.session_id, user_session_id)

    def test_update_accepted_quantity_and_stock_no_change(self):
        detail = GoodsQualityNoteDetail.objects.using('default').get(id=1)
        original_accepted_qty = detail.accepted_qty
        original_stock_qty = detail.item_master.stock_qty
        user_session_id = 'no_change_user'

        detail.update_accepted_quantity_and_stock(original_accepted_qty, user_session_id)

        detail.refresh_from_db(using='default')
        detail.item_master.refresh_from_db(using='default')
        
        self.assertEqual(detail.accepted_qty, original_accepted_qty)
        self.assertEqual(detail.item_master.stock_qty, original_stock_qty)
        self.assertEqual(detail.master.session_id, user_session_id)


    def test_update_accepted_quantity_increase_raises_error(self):
        detail = GoodsQualityNoteDetail.objects.using('default').get(id=1)
        new_accepted_qty = 90.0 # Increase
        user_session_id = 'test_user'

        with self.assertRaises(ValueError) as cm:
            detail.update_accepted_quantity_and_stock(new_accepted_qty, user_session_id)
        self.assertEqual(str(cm.exception), "Accepted Quantity cannot be increased.")

class GQNFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal related data for testing forms
        cls.rejection_reason_ok = RejectionReason.objects.using('default').create(id=1, symbol='OK')
        cls.rejection_reason_defect = RejectionReason.objects.using('default').create(id=2, symbol='DEFECT')
        cls.item_master = ItemMaster.objects.using('default').create(id=101, item_code='ITEM001', stock_qty=100.0, comp_id=1)
        cls.unit = Unit.objects.using('default').create(id=1, symbol='MTR')
        cls.gqn_master = GoodsQualityNoteMaster.objects.using('default').create(id=1, gqn_no='GQN/001', grr_id=1, fin_year_id=2023, comp_id=1)
        cls.material_received_master = MaterialReceivedMaster.objects.using('default').create(id=1)
        cls.po_master = POMaster.objects.using('default').create(id=1, pr_spr_flag='0')
        cls.pr_master = PRMaster.objects.using('default').create(id=1)
        cls.pr_detail = PRDetail.objects.using('default').create(id=1, master=cls.pr_master, item=cls.item_master, ah_id=42)
        cls.po_detail = PODetail.objects.using('default').create(id=1, master=cls.po_master, pr_id=cls.pr_detail.id)
        cls.material_received_detail = MaterialReceivedDetail.objects.using('default').create(id=1, master=cls.material_received_master, po_id=cls.po_detail.id)

        cls.gqn_detail = GoodsQualityNoteDetail.objects.using('default').create(
            id=1, master=cls.gqn_master, grr_detail=cls.material_received_detail, accepted_qty=80.0, rejected_qty=0.0,
            rejection_reason=cls.rejection_reason_ok, remarks='Initial remarks', sn='ABC', pn='XYZ'
        )

    def test_form_valid_data(self):
        form_data = {
            'accepted_qty': 75.0,
            'rejection_reason': self.rejection_reason_defect.id,
            'remarks': 'Updated remarks',
            'sn': 'NEW SN',
            'pn': 'NEW PN',
            'original_accepted_qty': 80.0, # Passed from instance in view
        }
        form = GoodsQualityNoteDetailForm(data=form_data, instance=self.gqn_detail)
        self.assertTrue(form.is_valid(), form.errors.as_json())

    def test_form_invalid_accepted_qty_increase(self):
        form_data = {
            'accepted_qty': 85.0, # Increased
            'rejection_reason': self.rejection_reason_ok.id,
            'remarks': 'Test',
            'sn': 'ABC',
            'pn': 'XYZ',
            'original_accepted_qty': 80.0,
        }
        form = GoodsQualityNoteDetailForm(data=form_data, instance=self.gqn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('Accepted Quantity cannot be increased', form.errors['accepted_qty'][0])

    def test_form_invalid_accepted_qty_format(self):
        form_data = {
            'accepted_qty': 'abc',
            'rejection_reason': self.rejection_reason_ok.id,
            'remarks': 'Test',
            'sn': 'ABC',
            'pn': 'XYZ',
            'original_accepted_qty': 80.0,
        }
        form = GoodsQualityNoteDetailForm(data=form_data, instance=self.gqn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('Enter a valid numeric quantity', form.errors['accepted_qty'][0])

    def test_form_conditional_required_sn_pn_when_ah42(self):
        # Simulate AHId=42 for the instance
        self.gqn_detail.grr_detail.po_id = self.po_detail.id # Ensure AHId lookup works
        self.gqn_detail.save(using='default')
        self.assertEqual(self.gqn_detail.ah_id, 42)

        form_data = {
            'accepted_qty': 70.0,
            'rejection_reason': self.rejection_reason_ok.id,
            'remarks': 'Test',
            'sn': '', # Missing
            'pn': 'XYZ',
            'original_accepted_qty': 80.0,
        }
        form = GoodsQualityNoteDetailForm(data=form_data, instance=self.gqn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('S/N is required when AHId is 42.', form.errors['sn'][0])

        form_data = {
            'accepted_qty': 70.0,
            'rejection_reason': self.rejection_reason_ok.id,
            'remarks': 'Test',
            'sn': 'ABC',
            'pn': '', # Missing
            'original_accepted_qty': 80.0,
        }
        form = GoodsQualityNoteDetailForm(data=form_data, instance=self.gqn_detail)
        self.assertFalse(form.is_valid())
        self.assertIn('P/N is required when AHId is 42.', form.errors['pn'][0])


class GQNViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create full test data for views, similar to model tests
        cls.supplier = Supplier.objects.using('default').create(supplier_id='SUP001', supplier_name='Test Supplier')
        cls.unit = Unit.objects.using('default').create(id=1, symbol='MTR')
        cls.item_master_ah42 = ItemMaster.objects.using('default').create(id=101, item_code='ITEM001', manf_desc='Test Item AH42', uom_basic=cls.unit, stock_qty=100.0, comp_id=1, file_name='img.jpg', att_name='spec.pdf')
        cls.rejection_reason_ok = RejectionReason.objects.using('default').create(id=1, symbol='OK')
        cls.rejection_reason_defect = RejectionReason.objects.using('default').create(id=2, symbol='DEFECT')

        cls.gqn_master = GoodsQualityNoteMaster.objects.using('default').create(
            id=1, gqn_no='GQN/001', grr_no='GRR/001', grr_id=1, fin_year_id=2023, comp_id=1,
            sys_date=timezone.localdate(), sys_time=timezone.localtime().time(), session_id='testuser'
        )
        cls.inward_master = InwardMaster.objects.using('default').create(
            id=1, gin_no='GIN/001', comp_id=1, fin_year_id=2023, challan_no='CH001', challan_date=timezone.now()
        )
        cls.po_master = POMaster.objects.using('default').create(id=1, po_no='PO001', fin_year_id=2023, comp_id=1, pr_spr_flag='0')
        cls.pr_master = PRMaster.objects.using('default').create(id=1, pr_no='PR001', comp_id=1, fin_year_id=2023)
        cls.pr_detail = PRDetail.objects.using('default').create(id=1, master=cls.pr_master, item=cls.item_master_ah42, ah_id=42)
        cls.po_detail = PODetail.objects.using('default').create(id=1, master=cls.po_master, qty=100.0, pr_id=cls.pr_detail.id, pr_no='PR001')
        cls.material_received_master = MaterialReceivedMaster.objects.using('default').create(id=1, gin_no='GIN/001', gin_id=cls.inward_master.id, comp_id=1)
        cls.material_received_detail = MaterialReceivedDetail.objects.using('default').create(id=1, master=cls.material_received_master, received_qty=90.0, po_id=cls.po_detail.id)
        cls.inward_detail = InwardDetail.objects.using('default').create(id=1, master=cls.inward_master, received_qty=95.0, po_id=cls.po_detail.id)

        cls.gqn_detail = GoodsQualityNoteDetail.objects.using('default').create(
            id=1, master=cls.gqn_master, grr_detail=cls.material_received_detail, accepted_qty=80.0, rejected_qty=10.0,
            rejection_reason=cls.rejection_reason_ok, remarks='Initial remarks', sn='SN123', pn='PN456'
        )

    def setUp(self):
        self.client.session = self.client.session # Ensure session is available
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023
        self.client.session['username'] = 'testuser'
        self.client.session.save()

    def test_gqn_edit_details_view(self):
        url = reverse('quality_control:gqn_edit_details', args=[self.gqn_master.pk])
        response = self.client.get(url, {'GQNNo': self.gqn_master.gqn_no, 'GRRNo': self.gqn_master.grr_no, 'GINNo': self.inward_master.gin_no, 'SUPId': self.supplier.supplier_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/goodsqualitynote_edit_details.html')
        self.assertContains(response, 'GQN/001')
        self.assertContains(response, 'Test Supplier')
        self.assertIn('gqn_master', response.context)
        self.assertIn('gqn_details', response.context)
        self.assertEqual(response.context['gqn_master'].pk, self.gqn_master.pk)
        self.assertTrue(len(response.context['gqn_details']) > 0)

    def test_goodsqualitynotedetail_table_partial_view(self):
        url = reverse('quality_control:goodsqualitynotedetail_table_partial', args=[self.gqn_master.pk])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/_goodsqualitynotedetail_table.html')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, '80.000') # Accepted Qty
        self.assertContains(response, 'Edit')

    def test_goodsqualitynotedetail_update_view_get(self):
        url = reverse('quality_control:goodsqualitynotedetail_edit', args=[self.gqn_detail.pk])
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/_goodsqualitynotedetail_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form']['accepted_qty'].value(), self.gqn_detail.accepted_qty)

    @patch('quality_control.models.timezone.localdate', return_value=datetime.date(2024, 1, 1))
    @patch('quality_control.models.timezone.localtime', return_value=MagicMock(time=lambda: datetime.time(10, 30, 0)))
    def test_goodsqualitynotedetail_update_view_post_success(self, mock_localtime, mock_localdate):
        url = reverse('quality_control:goodsqualitynotedetail_edit', args=[self.gqn_detail.pk])
        original_stock_qty = self.item_master_ah42.stock_qty
        
        form_data = {
            'accepted_qty': 70.0,
            'rejection_reason': self.rejection_reason_defect.id,
            'remarks': 'Updated test',
            'sn': 'UPD_SN',
            'pn': 'UPD_PN',
            'original_accepted_qty': 80.0,
        }
        
        response = self.client.post(url, form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshGQNTable')

        self.gqn_detail.refresh_from_db(using='default')
        self.item_master_ah42.refresh_from_db(using='default')
        self.gqn_master.refresh_from_db(using='default')

        self.assertEqual(self.gqn_detail.accepted_qty, 70.0)
        self.assertEqual(self.gqn_detail.rejection_reason.symbol, 'DEFECT')
        self.assertEqual(self.gqn_detail.remarks, 'Updated test')
        self.assertEqual(self.gqn_detail.sn, 'UPD_SN')
        self.assertEqual(self.gqn_detail.pn, 'UPD_PN')
        self.assertEqual(self.item_master_ah42.stock_qty, original_stock_qty + (80.0 - 70.0)) # 100 + 10 = 110.0
        self.assertEqual(self.gqn_master.sys_date, datetime.date(2024, 1, 1))
        self.assertEqual(self.gqn_master.sys_time, datetime.time(10, 30, 0))
        self.assertEqual(self.gqn_master.session_id, 'testuser')


    def test_goodsqualitynotedetail_update_view_post_invalid(self):
        url = reverse('quality_control:goodsqualitynotedetail_edit', args=[self.gqn_detail.pk])
        form_data = {
            'accepted_qty': 90.0, # Invalid: increased quantity
            'rejection_reason': self.rejection_reason_ok.id,
            'remarks': 'Invalid test',
            'sn': 'SN123',
            'pn': 'PN456',
            'original_accepted_qty': 80.0,
        }
        response = self.client.post(url, form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad Request due to form errors
        self.assertContains(response, 'Accepted Quantity cannot be increased from 80.0 to 90.0.')
        self.assertTemplateUsed(response, 'quality_control/_goodsqualitynotedetail_form.html')

    def test_check_sn_pn_visibility_view(self):
        url = reverse('quality_control:check_sn_pn_visibility')
        response = self.client.get(url, {'ah_id': 42})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {'is_sn_pn_visible': True})

        response = self.client.get(url, {'ah_id': 10})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), {'is_sn_pn_visible': False})

    def test_download_item_file(self):
        url = reverse('quality_control:download_item_file', args=[self.item_master_ah42.pk, 'image'])
        # Mock settings.MEDIA_ROOT to point to a temporary directory for testing file existence
        with patch('django.conf.settings.MEDIA_ROOT', new=self.get_temp_media_root()):
            # Create a dummy file
            dummy_file_path = self.get_temp_media_root() / self.item_master_ah42.file_name
            dummy_file_path.parent.mkdir(parents=True, exist_ok=True)
            with open(dummy_file_path, 'w') as f:
                f.write('dummy image content')

            response = self.client.get(url)
            self.assertEqual(response.status_code, 200)
            self.assertEqual(response['Content-Type'], 'application/octet-stream') # Default for FileResponse
            self.assertEqual(response['Content-Disposition'], 'attachment; filename="img.jpg"')

    def get_temp_media_root(self):
        import tempfile
        import shutil
        from pathlib import Path
        temp_dir = Path(tempfile.mkdtemp())
        self.addCleanup(shutil.rmtree, temp_dir)
        return temp_dir
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for DataTables Refresh:** The `_goodsqualitynotedetail_table.html` partial is loaded via `hx-get` on page load and triggered by `refreshGQNTable` event after a successful update. This ensures only the table area is reloaded, not the entire page.
*   **HTMX for Modals:** Edit buttons use `hx-get` to fetch the `_goodsqualitynotedetail_form.html` partial into a designated `div` (`#modalContent`), acting as a modal. `hx-target` and `hx-swap` control this.
*   **HTMX Form Submission:** The form inside the modal uses `hx-post` to submit. Upon successful submission (HTTP 204 No Content), an `HX-Trigger` header (`refreshGQNTable`) is sent to close the modal and refresh the table.
*   **Alpine.js for Modal State:** An Alpine.js store `Alpine.store('modal')` manages the `isOpen` state for the modal, toggling a CSS class (`hidden`) to show/hide it. `x-on:click` directives on the cancel button and modal backdrop control this.
*   **Alpine.js for Conditional Fields:** The `_goodsqualitynotedetail_form.html` uses `x-data` to hold the `ahId`, `isSnPnVisible`, `isSnPnRequired`, `snValue`, `pnValue`. `x-show` and `x-bind:required` dynamically control the visibility and required status of `SN` and `PN` fields based on `ahId === 42`. The `ahId` is passed as a hidden input field from the backend, and Alpine.js picks it up on form load.
*   **DataTables:** The `_goodsqualitynotedetail_table.html` initializes DataTables on the `gqnDetailTable` ID. This provides client-side searching, sorting, and pagination.

---

### Final Notes

*   **Database Configuration:** Remember to configure your `DATABASES` setting in `settings.py` to connect to your legacy SQL Server database (e.g., using `django-pyodbc-azure` or `django-mssql-pyodbc` engine). The `using('default')` calls in models and views assume the configured database alias for these legacy tables is 'default'. If you use a separate alias like 'legacy\_db', adjust the `using()` calls accordingly.
*   **Settings (`settings.py`):**
    *   Add `quality_control` to `INSTALLED_APPS`.
    *   Configure `MEDIA_ROOT` and `MEDIA_URL` for file downloads.
    *   Ensure Django's template loaders are configured for app templates.
    *   Add `django.middleware.csrf.CsrfViewMiddleware` (default) and `django.contrib.messages.middleware.MessageMiddleware`.
*   **URL Root (`project/urls.py`):**
    ```python
    from django.contrib import admin
    from django.urls import path, include

    urlpatterns = [
        path('admin/', admin.site.urls),
        path('quality-control/', include('quality_control.urls')),
        # Add a root redirect or homepage if needed
    ]
    ```
*   **CSS/JS:** Ensure Tailwind CSS is set up and compiled. CDN links for HTMX, Alpine.js, jQuery, and DataTables are assumed to be in your `base.html` as per the guidelines.
*   **Error Handling:** The provided code has basic try-except blocks. In a production system, comprehensive error logging and user-friendly error messages should be implemented.
*   **Authentication/Authorization:** The original ASP.NET used `Session["username"]`, `Session["compid"]`, `Session["finyear"]`. In Django, this would typically involve Django's authentication system, custom middleware, or signals to set user/company/financial year context. For this conversion, session variables are accessed directly for `compid`, `finyear`, `username`.
*   **Further Refinements:**
    *   The complex `loadData` logic currently simulated by model properties could benefit from a custom Django Manager method or a database view for better performance, especially if the data volume is large.
    *   The `download_item_file` view is a basic placeholder; robust file serving would involve proper storage backend configuration (e.g., S3, Azure Blob Storage) and more secure file paths.
    *   The `ItemMaster` properties that infer `ItemCode`, `Description`, `UOM`, etc., are making multiple small queries. For performance, these could be optimized using `select_related` or `prefetch_related` in the initial queryset for `GoodsQualityNoteDetail` to fetch all related data in one go.