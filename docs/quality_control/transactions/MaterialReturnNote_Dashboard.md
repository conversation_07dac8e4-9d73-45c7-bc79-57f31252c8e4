## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code for `MaterialReturnNote_Dashboard.aspx` and its empty C# code-behind offers minimal direct information about its functionality, database interaction, or UI components. It primarily defines content placeholders within a master page and includes a `loadingNotifier.js` script.

Based on the file name `MaterialReturnNote_Dashboard` and common ERP patterns, we infer that this page is likely responsible for displaying a list of "Material Return Notes" and providing a dashboard view, which typically includes options for managing these notes (create, read, update, delete).

### Business Value of Django Modernization:

Migrating this ASP.NET component to Django offers significant business advantages:

1.  **Reduced Technical Debt:** Moves away from legacy ASP.NET Web Forms, which are harder to maintain, scale, and integrate with modern web technologies.
2.  **Enhanced Performance & User Experience:** HTMX and Alpine.js create highly responsive, dynamic interfaces without full page reloads, mirroring the feel of a Single Page Application (SPA) with simpler development. This directly improves user satisfaction and operational efficiency.
3.  **Future-Proofing & Scalability:** Django is a robust, well-supported, and scalable framework, allowing for easier integration with new services, adoption of microservices architectures, and handling increased user loads.
4.  **Developer Efficiency:** Django's "batteries-included" philosophy, clear structure, and thriving ecosystem accelerate development, reduce bugs, and simplify maintenance. The fat model/thin view approach centralizes business logic, making it easier to understand and test.
5.  **Cost Savings:** Lower development and maintenance costs due to increased efficiency, reduced training needs for modern skillsets, and better resource utilization.
6.  **Improved Security:** Django's built-in security features help protect against common web vulnerabilities, leading to a more secure application environment.

This modernization focuses on a systematic, automated approach, where the generated code serves as a blueprint for AI-driven conversion tools, significantly reducing manual effort and potential human errors in the transition.

---

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the lack of explicit database elements in the provided ASP.NET code, we will infer the schema based on the component name `MaterialReturnNote_Dashboard`.

**Inferred Schema:**

*   **Table Name:** `TblMaterialReturnNote` (common prefix in older ASP.NET applications)
*   **Model Name:** `MaterialReturnNote`
*   **Columns:**
    *   `ReturnNoteID` (Integer, Primary Key)
    *   `ReturnDate` (Date)
    *   `MaterialCode` (String, e.g., product code)
    *   `Quantity` (Decimal)
    *   `Reason` (Text)
    *   `Status` (String, e.g., 'Pending', 'Approved', 'Rejected')
    *   `CreatedBy` (String)
    *   `CreatedDate` (DateTime)

---

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Based on the `_Dashboard` suffix, the primary functionality is to display a list of Material Return Notes. For any dashboard related to business entities, standard CRUD (Create, Read, Update, Delete) operations are implicitly required.

*   **Create:** Ability to add new Material Return Notes.
*   **Read:** Display a list of all existing Material Return Notes. (This is the primary function of a "Dashboard").
*   **Update:** Ability to modify details of an existing Material Return Note.
*   **Delete:** Ability to remove a Material Return Note.
*   **Validation Logic:** Implicitly, fields like `ReturnDate`, `MaterialCode`, `Quantity`, and `Status` would have validation (e.g., required fields, valid formats, quantity greater than zero). This will be replicated in Django forms and models.

---

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Since no ASP.NET controls are directly in the .aspx file, we infer standard UI elements for a dashboard page with CRUD functionality:

*   **List View (Dashboard):** A table (equivalent to a `GridView` or `Repeater`) to display Material Return Notes with columns for `ReturnNoteID`, `ReturnDate`, `MaterialCode`, `Quantity`, `Status`, and "Actions" (Edit, Delete buttons). This will be implemented using DataTables for search/sort/pagination.
*   **Form for Create/Update:** Input fields (equivalent to `TextBox`, `DropDownList`) for `ReturnDate`, `MaterialCode`, `Quantity`, `Reason`, `Status`. These will be presented within a modal using HTMX for dynamic loading.
*   **Action Buttons:**
    *   "Add New Material Return Note" button to open the creation form modal.
    *   "Edit" button for each row to open the update form modal.
    *   "Delete" button for each row to open a confirmation modal.
*   **Loading Indicator:** The presence of `loadingNotifier.js` implies a visual cue during async operations, which HTMX handles automatically with `hx-indicator` or can be customized with Alpine.js.

---

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The `MaterialReturnNote` model will map to the `TblMaterialReturnNote` table in the existing database.

```python
# material_return/models.py
from django.db import models
from django.urls import reverse

class MaterialReturnNote(models.Model):
    return_note_id = models.IntegerField(db_column='ReturnNoteID', primary_key=True, verbose_name='Return Note ID')
    return_date = models.DateField(db_column='ReturnDate', verbose_name='Return Date')
    material_code = models.CharField(db_column='MaterialCode', max_length=50, verbose_name='Material Code')
    quantity = models.DecimalField(db_column='Quantity', max_digits=10, decimal_places=2, verbose_name='Quantity')
    reason = models.TextField(db_column='Reason', blank=True, null=True, verbose_name='Reason for Return')
    status = models.CharField(db_column='Status', max_length=20, default='Pending', verbose_name='Status')
    created_by = models.CharField(db_column='CreatedBy', max_length=100, blank=True, null=True, verbose_name='Created By')
    created_date = models.DateTimeField(db_column='CreatedDate', auto_now_add=True, blank=True, null=True, verbose_name='Created Date')

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'TblMaterialReturnNote'
        verbose_name = 'Material Return Note'
        verbose_name_plural = 'Material Return Notes'
        ordering = ['-return_note_id'] # Order by ID descending for dashboard

    def __str__(self):
        return f"MRN-{self.return_note_id} ({self.material_code})"

    def get_absolute_url(self):
        return reverse('material_return_note_detail', args=[str(self.return_note_id)])
    
    # Example business logic method (Fat Model)
    def update_status(self, new_status, user):
        # In a real app, validate new_status against allowed values
        if self.status != new_status:
            self.status = new_status
            # Log who changed the status and when
            # self.last_modified_by = user.username # Assuming user object
            # self.last_modified_date = timezone.now()
            self.save()
            return True
        return False
```

### 4.2 Forms

**Task:** Define a Django form for user input for Material Return Notes.

**Instructions:**
A `ModelForm` will be created for the `MaterialReturnNote` model, incorporating Tailwind CSS classes for styling.

```python
# material_return/forms.py
from django import forms
from .models import MaterialReturnNote

class MaterialReturnNoteForm(forms.ModelForm):
    class Meta:
        model = MaterialReturnNote
        fields = ['return_date', 'material_code', 'quantity', 'reason', 'status']
        widgets = {
            'return_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter material code'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01', 'min': '0'}),
            'reason': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'status': forms.Select(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'return_date': 'Return Date',
            'material_code': 'Material Code',
            'quantity': 'Quantity',
            'reason': 'Reason for Return',
            'status': 'Status',
        }

    # Add custom validation methods here if needed (e.g., to check quantity against stock)
    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive value.")
        return quantity
```

### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), ensuring thin views and business logic in models. A partial view for the DataTables content is added for HTMX.

**Instructions:**
Views will be kept concise, delegating business logic to the `MaterialReturnNote` model. HTMX headers are checked for partial responses.

```python
# material_return/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import MaterialReturnNote
from .forms import MaterialReturnNoteForm

class MaterialReturnNoteListView(ListView):
    model = MaterialReturnNote
    template_name = 'material_return/materialreturnnote/list.html'
    context_object_name = 'material_return_notes'

class MaterialReturnNoteTablePartialView(TemplateView):
    template_name = 'material_return/materialreturnnote/_materialreturnnote_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['material_return_notes'] = MaterialReturnNote.objects.all()
        return context

class MaterialReturnNoteCreateView(CreateView):
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'material_return/materialreturnnote/_materialreturnnote_form.html'
    success_url = reverse_lazy('material_return_note_list') # Not directly used for HTMX, but good practice

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # For 'managed=False' models, if the PK is not auto-generated by the DB,
        # you might need to handle it in form_valid or clean_data.
        # Assuming ReturnNoteID is auto-generated by the database.
        return kwargs

    def form_valid(self, form):
        # Additional logic can go here (e.g., form.instance.created_by = self.request.user)
        # Business logic should be in the model if complex
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Note added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': '{"refreshMaterialReturnNoteList":true, "hideModal":true}'
                }
            )
        return response

class MaterialReturnNoteUpdateView(UpdateView):
    model = MaterialReturnNote
    form_class = MaterialReturnNoteForm
    template_name = 'material_return/materialreturnnote/_materialreturnnote_form.html'
    success_url = reverse_lazy('material_return_note_list') # Not directly used for HTMX

    def form_valid(self, form):
        # Example: Using a model method for business logic
        # if form.has_changed() and 'status' in form.changed_data:
        #     self.object.update_status(form.cleaned_data['status'], self.request.user)
        response = super().form_valid(form)
        messages.success(self.request, 'Material Return Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': '{"refreshMaterialReturnNoteList":true, "hideModal":true}'
                }
            )
        return response

class MaterialReturnNoteDeleteView(DeleteView):
    model = MaterialReturnNote
    template_name = 'material_return/materialreturnnote/_materialreturnnote_confirm_delete.html'
    success_url = reverse_lazy('material_return_note_list') # Not directly used for HTMX

    def delete(self, request, *args, **kwargs):
        # Any pre-delete business logic can go here (e.g., check if deletable)
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Return Note deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': '{"refreshMaterialReturnNoteList":true, "hideModal":true}'
                }
            )
        return response
```

### 4.4 Templates

**Task:** Create templates for each view, leveraging HTMX, Alpine.js, and DataTables.

**Instructions:**
Templates will extend `core/base.html` and use partials for dynamic content. HTMX attributes will drive interactions, and Alpine.js will manage modal visibility.

#### `material_return/materialreturnnote/list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Return Notes</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'material_return_note_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on htmx:afterOnLoad add .is-active to #modal">
            Add New Note
        </button>
    </div>
    
    <div id="materialreturnnoteTable-container"
         hx-trigger="load, refreshMaterialReturnNoteList from:body"
         hx-get="{% url 'material_return_note_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex items-center justify-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="ml-3 text-gray-600">Loading Material Return Notes...</p>
        </div>
    </div>
    
    <!-- Modal for forms and delete confirmation -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active:flex"
         x-data="{ showModal: false }"
         x-init="$watch('showModal', value => { if(value) { document.getElementById('modal').classList.add('is-active'); } else { document.getElementById('modal').classList.remove('is-active'); } })"
         _="on hx-trigger[hideModal] set showModal to false then remove .is-active from me"
         @click.self="showModal = false"> <!-- Close modal on click outside content -->
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Place DataTables JS initialization here or in base.html if universal -->
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true; },
            close() { this.isOpen = false; },
        });
    });
</script>
{% endblock %}
```

#### `material_return/materialreturnnote/_materialreturnnote_table.html`

```html
<div class="bg-white shadow-md rounded-lg overflow-hidden">
    <table id="materialReturnNoteTable" class="min-w-full leading-normal">
        <thead>
            <tr>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Note ID</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Return Date</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Material Code</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Quantity</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="py-3 px-5 border-b-2 border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for note in material_return_notes %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-5 border-b border-gray-200 text-sm">{{ forloop.counter }}</td>
                <td class="py-3 px-5 border-b border-gray-200 text-sm">{{ note.return_note_id }}</td>
                <td class="py-3 px-5 border-b border-gray-200 text-sm">{{ note.return_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-5 border-b border-gray-200 text-sm">{{ note.material_code }}</td>
                <td class="py-3 px-5 border-b border-gray-200 text-sm">{{ note.quantity }}</td>
                <td class="py-3 px-5 border-b border-gray-200 text-sm">
                    <span class="relative inline-block px-3 py-1 font-semibold leading-tight">
                        <span aria-hidden="true" class="absolute inset-0 opacity-50 rounded-full 
                            {% if note.status == 'Pending' %}bg-yellow-200{% elif note.status == 'Approved' %}bg-green-200{% else %}bg-red-200{% endif %}">
                        </span>
                        <span class="relative">{{ note.status }}</span>
                    </span>
                </td>
                <td class="py-3 px-5 border-b border-gray-200 text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg text-xs transition duration-300 ease-in-out mr-2"
                        hx-get="{% url 'material_return_note_edit' note.return_note_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on htmx:afterOnLoad add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg text-xs transition duration-300 ease-in-out"
                        hx-get="{% url 'material_return_note_delete' note.return_note_id %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on htmx:afterOnLoad add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#materialReturnNoteTable')) {
            $('#materialReturnNoteTable').DataTable().destroy();
        }
        $('#materialReturnNoteTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]],
            "responsive": true,
            "ordering": true,
            "info": true,
            "searching": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 6] } // Disable ordering on SN and Actions
            ]
        });
    });
</script>
```

#### `material_return/materialreturnnote/_materialreturnnote_form.html`

```html
<div class="p-6" x-data="{ returnDate: '{{ form.return_date.value|date:"Y-m-d" }}' }">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Material Return Note</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <label for="{{ form.return_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.return_date.label }}
                </label>
                {{ form.return_date }}
                {% if form.return_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.return_date.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.material_code.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.material_code.label }}
                </label>
                {{ form.material_code }}
                {% if form.material_code.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.material_code.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.quantity.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.quantity.label }}
                </label>
                {{ form.quantity }}
                {% if form.quantity.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.quantity.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.status.label }}
                </label>
                {{ form.status }}
                {% if form.status.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.status.errors }}</p>
                {% endif %}
            </div>
        </div>

        <div class="mt-6">
            <label for="{{ form.reason.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.reason.label }}
            </label>
            {{ form.reason }}
            {% if form.reason.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.reason.errors }}</p>
            {% endif %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 text-red-500 text-sm">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Note
                <span id="form-spinner" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

#### `material_return/materialreturnnote/_materialreturnnote_confirm_delete.html`

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Material Return Note
        <strong>MRN-{{ object.return_note_id }} ({{ object.material_code }})</strong>?
        This action cannot be undone.
    </p>

    <div class="flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-delete="{% url 'material_return_note_delete' object.return_note_id %}" 
            hx-swap="none"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
            Confirm Delete
        </button>
    </div>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs will be defined for the list, create, update, delete operations, and a specific endpoint for the DataTables partial view.

```python
# material_return/urls.py
from django.urls import path
from .views import (
    MaterialReturnNoteListView,
    MaterialReturnNoteCreateView,
    MaterialReturnNoteUpdateView,
    MaterialReturnNoteDeleteView,
    MaterialReturnNoteTablePartialView,
)

urlpatterns = [
    path('material-return-notes/', MaterialReturnNoteListView.as_view(), name='material_return_note_list'),
    path('material-return-notes/add/', MaterialReturnNoteCreateView.as_view(), name='material_return_note_add'),
    path('material-return-notes/<int:pk>/edit/', MaterialReturnNoteUpdateView.as_view(), name='material_return_note_edit'),
    path('material-return-notes/<int:pk>/delete/', MaterialReturnNoteDeleteView.as_view(), name='material_return_note_delete'),
    path('material-return-notes/table/', MaterialReturnNoteTablePartialView.as_view(), name='material_return_note_table'),
]
```

### 4.6 Tests

**Task:** Write tests for the model and views to ensure functionality and coverage.

**Instructions:**
Comprehensive unit tests for model methods and properties, and integration tests for all views (list, create, update, delete) will be included.

```python
# material_return/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import MaterialReturnNote
from datetime import date
from decimal import Decimal

class MaterialReturnNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        MaterialReturnNote.objects.create(
            return_note_id=1001, # Explicitly set PK for managed=False
            return_date=date(2023, 1, 15),
            material_code='MAT001',
            quantity=Decimal('10.50'),
            reason='Damaged goods',
            status='Pending',
            created_by='testuser',
            created_date='2023-01-15T10:00:00Z'
        )
        MaterialReturnNote.objects.create(
            return_note_id=1002,
            return_date=date(2023, 1, 16),
            material_code='MAT002',
            quantity=Decimal('5.00'),
            reason='Wrong item',
            status='Approved',
            created_by='anotheruser',
            created_date='2023-01-16T11:00:00Z'
        )
  
    def test_material_return_note_creation(self):
        obj = MaterialReturnNote.objects.get(return_note_id=1001)
        self.assertEqual(obj.return_note_id, 1001)
        self.assertEqual(obj.material_code, 'MAT001')
        self.assertEqual(obj.quantity, Decimal('10.50'))
        self.assertEqual(obj.status, 'Pending')
        
    def test_return_date_label(self):
        obj = MaterialReturnNote.objects.get(return_note_id=1001)
        field_label = obj._meta.get_field('return_date').verbose_name
        self.assertEqual(field_label, 'Return Date')
        
    def test_quantity_label(self):
        obj = MaterialReturnNote.objects.get(return_note_id=1001)
        field_label = obj._meta.get_field('quantity').verbose_name
        self.assertEqual(field_label, 'Quantity')

    def test_str_representation(self):
        obj = MaterialReturnNote.objects.get(return_note_id=1001)
        self.assertEqual(str(obj), 'MRN-1001 (MAT001)')

    def test_update_status_method(self):
        obj = MaterialReturnNote.objects.get(return_note_id=1001)
        initial_status = obj.status
        self.assertTrue(obj.update_status('Approved', 'adminuser'))
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Approved')
        self.assertFalse(obj.update_status('Approved', 'adminuser')) # No change, returns False

class MaterialReturnNoteViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        MaterialReturnNote.objects.create(
            return_note_id=2001,
            return_date=date(2023, 2, 1),
            material_code='TEST001',
            quantity=Decimal('20.00'),
            reason='Test reason',
            status='Pending',
            created_by='testuser',
            created_date='2023-02-01T12:00:00Z'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('material_return_note_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_return/materialreturnnote/list.html')
        self.assertIn('material_return_notes', response.context)
        self.assertContains(response, 'Material Return Notes') # Check for page title

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('material_return_note_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_return/materialreturnnote/_materialreturnnote_table.html')
        self.assertIn('material_return_notes', response.context)
        self.assertContains(response, 'TEST001') # Ensure data is present

    def test_create_view_get(self):
        response = self.client.get(reverse('material_return_note_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_return/materialreturnnote/_materialreturnnote_form.html')
        self.assertIn('form', response.context)
        
    def test_create_view_post_success(self):
        data = {
            'return_date': '2023-03-01',
            'material_code': 'NEWITEM',
            'quantity': '15.75',
            'reason': 'New entry test',
            'status': 'Pending',
            # return_note_id will be handled by DB or set by model if not auto-inc
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('material_return_note_add'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content on success
        self.assertIn('HX-Trigger', response.headers)
        self.assertContains(response, '{"refreshMaterialReturnNoteList":true, "hideModal":true}', status_code=204)
        
        # Verify object was created (assuming return_note_id was auto-generated or handled)
        # This part might need adjustment based on how 'ReturnNoteID' is handled in the DB
        # For 'managed=False', we rely on the database's primary key generation.
        # So, we check for other unique fields.
        self.assertTrue(MaterialReturnNote.objects.filter(material_code='NEWITEM').exists())
        self.assertEqual(MaterialReturnNote.objects.filter(material_code='NEWITEM').count(), 1)
        
    def test_create_view_post_invalid(self):
        data = {
            'return_date': '2023-03-01',
            'material_code': '', # Invalid
            'quantity': '0', # Invalid
            'reason': 'Invalid entry test',
            'status': 'Pending',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('material_return_note_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # HTMX returns 200 with form errors
        self.assertTemplateUsed(response, 'material_return/materialreturnnote/_materialreturnnote_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertContains(response, 'This field is required')
        self.assertContains(response, 'Quantity must be a positive value.')

    def test_update_view_get(self):
        obj = MaterialReturnNote.objects.get(return_note_id=2001)
        response = self.client.get(reverse('material_return_note_edit', args=[obj.return_note_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_return/materialreturnnote/_materialreturnnote_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        obj = MaterialReturnNote.objects.get(return_note_id=2001)
        data = {
            'return_date': '2023-02-01',
            'material_code': 'UPDATED001',
            'quantity': '25.00',
            'reason': 'Updated test reason',
            'status': 'Approved',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('material_return_note_edit', args=[obj.return_note_id]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertContains(response, '{"refreshMaterialReturnNoteList":true, "hideModal":true}', status_code=204)
        obj.refresh_from_db()
        self.assertEqual(obj.material_code, 'UPDATED001')
        self.assertEqual(obj.quantity, Decimal('25.00'))

    def test_delete_view_get(self):
        obj = MaterialReturnNote.objects.get(return_note_id=2001)
        response = self.client.get(reverse('material_return_note_delete', args=[obj.return_note_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_return/materialreturnnote/_materialreturnnote_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_success(self):
        obj_to_delete = MaterialReturnNote.objects.create(
            return_note_id=3001,
            return_date=date(2023, 3, 1),
            material_code='TOBEDELETED',
            quantity=Decimal('5.00'),
            reason='Delete test',
            status='Pending',
            created_by='deleter',
            created_date='2023-03-01T13:00:00Z'
        )
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.delete(reverse('material_return_note_delete', args=[obj_to_delete.return_note_id]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertContains(response, '{"refreshMaterialReturnNoteList":true, "hideModal":true}', status_code=204)
        self.assertFalse(MaterialReturnNote.objects.filter(return_note_id=3001).exists())
```

---

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Updates:** All form submissions (`hx-post`) and content loads (`hx-get`) are handled via HTMX. The `_materialreturnnote_table.html` partial is reloaded dynamically after any CRUD operation using `HX-Trigger: refreshMaterialReturnNoteList`. Modals are opened and closed by HTMX's `hx-target` and `hx-onLoad` attributes, and `HX-Trigger: hideModal` from the server.
*   **Alpine.js for UI State Management:** Used to manage the visibility of the main modal (`#modal`) and ensure it can be closed by clicking outside its content. The `is-active` class is toggled, showing or hiding the modal.
*   **DataTables for List Views:** The `_materialreturnnote_table.html` partial includes a JavaScript snippet that initializes DataTables on the rendered table. This provides client-side searching, sorting, and pagination without full page reloads. The `$(document).ready` is wrapped in an `if ($.fn.DataTable.isDataTable())` check to prevent re-initialization issues common with HTMX swapping.
*   **No Full Page Reloads:** All CRUD operations and data table refreshes occur via AJAX, providing a smooth user experience.
*   **DRY Template Inheritance:** All component templates (`list.html`) extend `core/base.html`, ensuring consistent header, footer, and shared resources (like CDN links for DataTables, HTMX, Alpine.js, Tailwind CSS) without duplicating code.

---

## Final Notes

*   This comprehensive plan provides a direct conversion path for the implied functionality of the ASP.NET `MaterialReturnNote_Dashboard`.
*   Placeholders were replaced with inferred values. For `ReturnNoteID`, it's assumed to be the primary key managed by the existing database. If it's not auto-incrementing, custom logic in `form_valid` might be needed to generate it (though this is less common for `managed=False` scenarios where the DB typically handles PKs).
*   The business logic for `MaterialReturnNote` (e.g., `update_status` method) demonstrates the "fat model" approach, centralizing operations where they are most relevant and testable.
*   The use of HTMX and Alpine.js minimizes custom JavaScript, focusing on declarative HTML for interactivity.
*   The provided tests aim for high coverage, ensuring that the migration maintains application integrity and functionality.
*   This structured output is designed to be easily digestible by business stakeholders and directly actionable for AI-assisted automation tools.