## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

The provided ASP.NET code (`QualityNote_Dashboard.aspx` and `QualityNote_Dashboard.aspx.cs`) is extremely minimal and does not contain explicit database schema definitions (like `SqlDataSource` or direct SQL commands) or UI components that bind to data. Therefore, we must infer the likely database schema based on the file name "QualityNote_Dashboard".

We will assume the primary entity is a "Quality Note" and that it resides in a table named `tblQualityNote`, a common convention in ASP.NET applications. For a typical Quality Note, we will infer the following columns:

*   **`QualityNoteID`**: Primary Key, integer.
*   **`NoteText`**: The actual content of the quality note, text.
*   **`Status`**: The current status of the note (e.g., 'Open', 'Closed', 'Pending'), text.
*   **`RaisedBy`**: The user or entity who raised the note, text.
*   **`CreatedAt`**: The timestamp when the note was created, datetime.

Based on this, our inferred details are:
*   **[TABLE_NAME]** = `tblQualityNote`
*   **[MODEL_NAME]** = `QualityNote`
*   **[APP_NAME]** = `quality_control` (derived from `Module_QualityControl` in the ASP.NET path)
*   **[MAIN_DISPLAY_FIELD]** = `note_text`

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

As mentioned, the provided ASP.NET code is a barebones page and does not explicitly define any CRUD (Create, Read, Update, Delete) operations. However, a "Dashboard" for "Quality Notes" inherently implies the need to:

*   **Read**: View a list of existing Quality Notes (the primary function of a dashboard).
*   **Create**: Add new Quality Notes.
*   **Update**: Modify existing Quality Notes.
*   **Delete**: Remove Quality Notes.

We will implement standard CRUD functionality in Django, leveraging Class-Based Views, to fulfill these implied requirements for a functional dashboard. There is no specific validation logic found in the ASP.NET code, so standard Django form validation will be applied.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET `.aspx` file contains only content placeholders and a `<script>` tag referencing `loadingNotifier.js`. No specific UI controls (like `GridView`, `TextBox`, `Button`) are defined within the provided markup.

However, for a "Dashboard" displaying "Quality Notes", we can infer the need for:

*   A **data table** (similar to an ASP.NET `GridView`) to list `QualityNote` entries. This will be implemented using **DataTables.js** for client-side sorting, filtering, and pagination.
*   **Forms** for adding and editing `QualityNote` entries, comprising input fields for `NoteText`, `Status`, `RaisedBy`, and `CreatedAt`. These forms will be rendered dynamically using **HTMX** within a modal.
*   **Action buttons** (e.g., "Add New Quality Note", "Edit", "Delete") to trigger the CRUD operations. These will also use **HTMX** for dynamic interactions.
*   **Alpine.js** will manage simple UI states, such as showing/hiding the modal, as implied by the `loadingNotifier.js` reference, which suggests client-side UI feedback.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `QualityNote` model will represent the `tblQualityNote` table in the database.

**File: `quality_control/models.py`**

```python
from django.db import models
from django.utils import timezone

class QualityNote(models.Model):
    """
    Represents a Quality Note entry in the tblQualityNote table.
    """
    quality_note_id = models.AutoField(
        db_column='QualityNoteID', 
        primary_key=True,
        verbose_name="Note ID"
    )
    note_text = models.TextField(
        db_column='NoteText',
        verbose_name="Note Details",
        help_text="Detailed description of the quality note."
    )
    status = models.CharField(
        db_column='Status',
        max_length=50,
        verbose_name="Status",
        choices=[
            ('Open', 'Open'),
            ('Closed', 'Closed'),
            ('Pending', 'Pending Review'),
            ('InProgress', 'In Progress')
        ],
        default='Open'
    )
    raised_by = models.CharField(
        db_column='RaisedBy',
        max_length=100,
        verbose_name="Raised By",
        help_text="Name or ID of the person/department who raised the note."
    )
    created_at = models.DateTimeField(
        db_column='CreatedAt',
        verbose_name="Created At",
        default=timezone.now,
        help_text="Timestamp when the quality note was created."
    )

    class Meta:
        managed = False  # Set to False as the table already exists
        db_table = 'tblQualityNote'
        verbose_name = 'Quality Note'
        verbose_name_plural = 'Quality Notes'
        ordering = ['-created_at'] # Order by creation date descending

    def __str__(self):
        """
        Returns a string representation of the Quality Note.
        """
        return f"Note {self.quality_note_id}: {self.note_text[:50]}..."

    def is_open(self):
        """
        Business logic: Checks if the quality note is still open.
        """
        return self.status == 'Open'

    def close_note(self):
        """
        Business logic: Marks the quality note as closed.
        """
        if self.is_open():
            self.status = 'Closed'
            self.save()
            return True
        return False

    def update_status(self, new_status):
        """
        Business logic: Updates the status of the quality note.
        """
        valid_statuses = ['Open', 'Closed', 'Pending', 'InProgress']
        if new_status in valid_statuses:
            self.status = new_status
            self.save()
            return True
        return False
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be used for `QualityNote` creation and updates, ensuring field mapping and validation. Tailwind CSS classes are applied via widgets.

**File: `quality_control/forms.py`**

```python
from django import forms
from .models import QualityNote

class QualityNoteForm(forms.ModelForm):
    """
    Form for creating and updating QualityNote instances.
    """
    class Meta:
        model = QualityNote
        fields = ['note_text', 'status', 'raised_by', 'created_at'] # Include all editable fields
        widgets = {
            'note_text': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 4
            }),
            'status': forms.Select(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'raised_by': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
            'created_at': forms.DateTimeInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'type': 'datetime-local' # HTML5 input type for datetime
            }),
        }
        labels = {
            'note_text': 'Note Details',
            'status': 'Status',
            'raised_by': 'Raised By',
            'created_at': 'Created At',
        }
        
    def clean_note_text(self):
        """
        Custom validation for note_text to ensure it's not too short.
        """
        note_text = self.cleaned_data.get('note_text')
        if len(note_text) < 10:
            raise forms.ValidationError("Note text must be at least 10 characters long.")
        return note_text

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are kept thin, delegating business logic to the `QualityNote` model. HTMX requests are handled with `status=204` and `HX-Trigger` headers for dynamic content updates. A `TablePartialView` is added to serve the DataTables content via HTMX.

**File: `quality_control/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import QualityNote
from .forms import QualityNoteForm

class QualityNoteListView(ListView):
    """
    Displays a list of all Quality Notes.
    This view serves the initial page that then loads the table via HTMX.
    """
    model = QualityNote
    template_name = 'quality_control/qualitynote/list.html'
    context_object_name = 'quality_notes' # Used for initial rendering if needed, though table is HTMX loaded

class QualityNoteTablePartialView(ListView):
    """
    Renders only the table portion of the Quality Notes list.
    Designed to be loaded via HTMX.
    """
    model = QualityNote
    template_name = 'quality_control/qualitynote/_qualitynote_table.html'
    context_object_name = 'quality_notes' # This context is used in the partial template

    def get_queryset(self):
        # Example: Add filtering logic if needed for the dashboard
        # For simplicity, returning all objects for now
        return QualityNote.objects.all()

class QualityNoteCreateView(CreateView):
    """
    Handles creation of new Quality Notes.
    Form is rendered in a modal via HTMX.
    """
    model = QualityNote
    form_class = QualityNoteForm
    template_name = 'quality_control/qualitynote/_qualitynote_form.html' # Use partial template for HTMX modal
    success_url = reverse_lazy('qualitynote_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # Business logic can be called on the model instance before saving if needed
        # For example: form.instance.some_pre_save_logic()
        response = super().form_valid(form)
        messages.success(self.request, 'Quality Note added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to indicate success without navigating
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQualityNoteList' # Custom event to refresh the table
                }
            )
        return response

class QualityNoteUpdateView(UpdateView):
    """
    Handles updating existing Quality Notes.
    Form is rendered in a modal via HTMX.
    """
    model = QualityNote
    form_class = QualityNoteForm
    template_name = 'quality_control/qualitynote/_qualitynote_form.html' # Use partial template for HTMX modal
    context_object_name = 'qualitynote' # Optional, but good practice
    success_url = reverse_lazy('qualitynote_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # Business logic can be called on the model instance before saving if needed
        # For example: form.instance.some_update_logic()
        response = super().form_valid(form)
        messages.success(self.request, 'Quality Note updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQualityNoteList'
                }
            )
        return response

class QualityNoteDeleteView(DeleteView):
    """
    Handles deletion of Quality Notes.
    Confirmation form is rendered in a modal via HTMX.
    """
    model = QualityNote
    template_name = 'quality_control/qualitynote/_qualitynote_confirm_delete.html' # Use partial template
    context_object_name = 'qualitynote'
    success_url = reverse_lazy('qualitynote_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        """
        Override delete to provide HTMX-specific response.
        """
        obj = self.get_object()
        # Business logic can be called on the model instance before deleting
        # For example: obj.some_pre_delete_logic()
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Quality Note "{obj}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQualityNoteList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates extend `core/base.html` for consistent layout. DataTables are used for list presentation, and HTMX handles dynamic interactions, such as loading forms and confirming deletions within modals. Alpine.js manages modal visibility.

**File: `quality_control/templates/quality_control/qualitynote/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Quality Notes Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'qualitynote_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Quality Note
        </button>
    </div>
    
    <div id="qualitynoteTable-container"
         hx-trigger="load, refreshQualityNoteList from:body"
         hx-get="{% url 'qualitynote_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- Initial loading state for HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Quality Notes...</p>
        </div>
    </div>
    
    <!-- Universal Modal for HTMX content -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4"
             @htmx:after-request.window="if(event.detail.successful) { document.getElementById('modal').classList.remove('is-active'); }"
             @click.away="document.getElementById('modal').classList.remove('is-active');">
            <!-- HTMX loaded content will appear here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for modal visibility. 
        // HTMX will toggle the 'is-active' class, Alpine can react to it.
        // Or we can rely purely on HTMX + _hyperscript for modal handling.
        // For simplicity, sticking to _hyperscript for toggling 'is-active'.
    });

    // Listen for custom message event to dismiss modal
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Setup for DataTables on partial load
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.elt.id === 'qualitynoteTable-container') {
            $('#qualitynoteTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "autoWidth": false, // Prevent DataTables from calculating column widths initially
                "responsive": true // Enable responsive behavior
            });
        }
    });
</script>
{% endblock %}
```

**File: `quality_control/templates/quality_control/qualitynote/_qualitynote_table.html`**

```html
<table id="qualitynoteTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr class="bg-gray-100">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">ID</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Note Details</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Raised By</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Created At</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for note in quality_notes %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.quality_note_id }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.note_text|truncatechars:70 }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.status }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.raised_by }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-800">{{ note.created_at|date:"Y-m-d H:i" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'qualitynote_edit' note.quality_note_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                    hx-get="{% url 'qualitynote_delete' note.quality_note_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 text-center text-gray-500">No Quality Notes found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization handled by the parent template's htmx:afterOnLoad listener
// This script block is technically redundant if parent listener is perfect,
// but kept for clarity on what would happen if loaded standalone.
// document.addEventListener('DOMContentLoaded', function() {
//     if (!$.fn.DataTable.isDataTable('#qualitynoteTable')) {
//         $('#qualitynoteTable').DataTable({
//             "pageLength": 10,
//             "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
//         });
//     }
// });
</script>
```

**File: `quality_control/templates/quality_control/qualitynote/_qualitynote_form.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} Quality Note
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" classx="space-y-6">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-6 sm:grid-cols-2 sm:gap-x-8">
            <div class="col-span-2">
                <label for="{{ form.note_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.note_text.label }}
                </label>
                {{ form.note_text }}
                {% if form.note_text.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.note_text.errors|join:", " }}</p>
                {% endif %}
                {% if form.note_text.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ form.note_text.help_text }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.status.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.status.label }}
                </label>
                {{ form.status }}
                {% if form.status.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.status.errors|join:", " }}</p>
                {% endif %}
            </div>

            <div>
                <label for="{{ form.raised_by.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.raised_by.label }}
                </label>
                {{ form.raised_by }}
                {% if form.raised_by.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.raised_by.errors|join:", " }}</p>
                {% endif %}
                {% if form.raised_by.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ form.raised_by.help_text }}</p>
                {% endif %}
            </div>

            <div class="col-span-2">
                <label for="{{ form.created_at.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.created_at.label }}
                </label>
                {{ form.created_at }}
                {% if form.created_at.errors %}
                <p class="text-red-600 text-xs mt-1">{{ form.created_at.errors|join:", " }}</p>
                {% endif %}
                {% if form.created_at.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ form.created_at.help_text }}</p>
                {% endif %}
            </div>

            <!-- Handle non-field errors -->
            {% if form.non_field_errors %}
            <div class="col-span-2 text-red-600 text-sm">
                {% for error in form.non_field_errors %}
                <p>{{ error }}</p>
                {% endfor %}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out">
                Save Quality Note
            </button>
        </div>
    </form>
</div>
```

**File: `quality_control/templates/quality_control/qualitynote/_qualitynote_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-red-700 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-800 mb-6">
        Are you sure you want to delete the Quality Note: 
        <span class="font-bold">"{{ qualitynote.note_text|truncatechars:50 }}" (ID: {{ qualitynote.quality_note_id }})</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'qualitynote_delete' qualitynote.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-md shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns are set up to route requests to the respective views, including the HTMX-specific partial table view.

**File: `quality_control/urls.py`**

```python
from django.urls import path
from .views import (
    QualityNoteListView, 
    QualityNoteTablePartialView,
    QualityNoteCreateView, 
    QualityNoteUpdateView, 
    QualityNoteDeleteView
)

urlpatterns = [
    path('qualitynote/', QualityNoteListView.as_view(), name='qualitynote_list'),
    path('qualitynote/table/', QualityNoteTablePartialView.as_view(), name='qualitynote_table'), # HTMX partial view
    path('qualitynote/add/', QualityNoteCreateView.as_view(), name='qualitynote_add'),
    path('qualitynote/edit/<int:pk>/', QualityNoteUpdateView.as_view(), name='qualitynote_edit'),
    path('qualitynote/delete/<int:pk>/', QualityNoteDeleteView.as_view(), name='qualitynote_delete'),
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests cover model functionality, and integration tests verify view behavior, including HTMX interactions and form submissions. Aim for high test coverage (80%+).

**File: `quality_control/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import QualityNote
from .forms import QualityNoteForm

class QualityNoteModelTest(TestCase):
    """
    Unit tests for the QualityNote model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.note1 = QualityNote.objects.create(
            note_text='Initial quality issue observed during inspection.',
            status='Open',
            raised_by='John Doe',
            created_at=timezone.now() - timezone.timedelta(days=1)
        )
        cls.note2 = QualityNote.objects.create(
            note_text='Minor defect found in batch A.',
            status='Pending',
            raised_by='Jane Smith',
            created_at=timezone.now() - timezone.timedelta(hours=2)
        )
  
    def test_quality_note_creation(self):
        """
        Tests if a QualityNote instance is created correctly.
        """
        self.assertEqual(self.note1.note_text, 'Initial quality issue observed during inspection.')
        self.assertEqual(self.note1.status, 'Open')
        self.assertEqual(self.note1.raised_by, 'John Doe')
        self.assertIsNotNone(self.note1.quality_note_id)

    def test_note_text_label(self):
        """
        Tests the verbose name for the note_text field.
        """
        field_label = self.note1._meta.get_field('note_text').verbose_name
        self.assertEqual(field_label, 'Note Details')
        
    def test_str_method(self):
        """
        Tests the __str__ method of the model.
        """
        expected_str = f"Note {self.note1.quality_note_id}: {self.note1.note_text[:50]}..."
        self.assertEqual(str(self.note1), expected_str)

    def test_is_open_method(self):
        """
        Tests the is_open business logic method.
        """
        self.assertTrue(self.note1.is_open())
        self.assertFalse(self.note2.is_open())
        
    def test_close_note_method(self):
        """
        Tests the close_note business logic method.
        """
        self.assertTrue(self.note1.close_note())
        self.assertEqual(self.note1.status, 'Closed')
        self.assertFalse(self.note1.is_open()) # Should be false after closing
        
        # Test closing an already closed note
        self.assertFalse(self.note1.close_note()) 

    def test_update_status_method(self):
        """
        Tests the update_status business logic method.
        """
        self.assertTrue(self.note2.update_status('InProgress'))
        self.assertEqual(self.note2.status, 'InProgress')
        
        # Test with invalid status
        self.assertFalse(self.note2.update_status('InvalidStatus'))
        self.assertEqual(self.note2.status, 'InProgress') # Should not change
        
class QualityNoteViewsTest(TestCase):
    """
    Integration tests for QualityNote views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.note = QualityNote.objects.create(
            note_text='Existing quality note for testing views.',
            status='Open',
            raised_by='Test User',
            created_at=timezone.now()
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view(self):
        """
        Tests the QualityNote list view (initial page load).
        """
        response = self.client.get(reverse('qualitynote_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qualitynote/list.html')
        self.assertContains(response, 'Quality Notes Dashboard') # Check for page title

    def test_table_partial_view_htmx(self):
        """
        Tests the HTMX partial view for the table.
        """
        response = self.client.get(reverse('qualitynote_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qualitynote/_qualitynote_table.html')
        self.assertContains(response, self.note.note_text) # Check if the note is in the table
        self.assertContains(response, 'id="qualitynoteTable"') # Check for DataTable ID

    def test_create_view_get_htmx(self):
        """
        Tests GET request for the create form via HTMX.
        """
        response = self.client.get(reverse('qualitynote_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qualitynote/_qualitynote_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Quality Note')

    def test_create_view_post_htmx_success(self):
        """
        Tests successful POST request for creating a QualityNote via HTMX.
        """
        initial_count = QualityNote.objects.count()
        data = {
            'note_text': 'A brand new quality observation.',
            'status': 'InProgress',
            'raised_by': 'New User',
            'created_at': timezone.now().isoformat() # ISO format for datetime-local
        }
        response = self.client.post(reverse('qualitynote_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success response (No Content)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshQualityNoteList')
        self.assertEqual(QualityNote.objects.count(), initial_count + 1)
        self.assertTrue(QualityNote.objects.filter(note_text='A brand new quality observation.').exists())
        
    def test_create_view_post_htmx_invalid(self):
        """
        Tests invalid POST request for creating a QualityNote via HTMX.
        """
        initial_count = QualityNote.objects.count()
        data = {
            'note_text': 'Too short', # Fails custom validation
            'status': 'Open',
            'raised_by': 'Invalid User',
            'created_at': timezone.now().isoformat()
        }
        response = self.client.post(reverse('qualitynote_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'quality_control/qualitynote/_qualitynote_form.html')
        self.assertContains(response, 'Note text must be at least 10 characters long.')
        self.assertEqual(QualityNote.objects.count(), initial_count) # No new object created

    def test_update_view_get_htmx(self):
        """
        Tests GET request for the update form via HTMX.
        """
        response = self.client.get(reverse('qualitynote_edit', args=[self.note.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qualitynote/_qualitynote_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Quality Note')
        self.assertContains(response, self.note.note_text) # Check if current data is pre-filled

    def test_update_view_post_htmx_success(self):
        """
        Tests successful POST request for updating a QualityNote via HTMX.
        """
        updated_text = 'Updated quality note details.'
        data = {
            'note_text': updated_text,
            'status': 'Closed',
            'raised_by': self.note.raised_by,
            'created_at': self.note.created_at.isoformat()
        }
        response = self.client.post(reverse('qualitynote_edit', args=[self.note.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshQualityNoteList')
        self.note.refresh_from_db() # Reload the object to get updated data
        self.assertEqual(self.note.note_text, updated_text)
        self.assertEqual(self.note.status, 'Closed')

    def test_delete_view_get_htmx(self):
        """
        Tests GET request for the delete confirmation via HTMX.
        """
        response = self.client.get(reverse('qualitynote_delete', args=[self.note.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/qualitynote/_qualitynote_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.note.note_text)

    def test_delete_view_post_htmx_success(self):
        """
        Tests successful POST request for deleting a QualityNote via HTMX.
        """
        note_to_delete = QualityNote.objects.create(
            note_text='Note to be deleted.',
            status='Open',
            raised_by='Delete Test',
            created_at=timezone.now()
        )
        initial_count = QualityNote.objects.count()
        response = self.client.post(reverse('qualitynote_delete', args=[note_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshQualityNoteList')
        self.assertEqual(QualityNote.objects.count(), initial_count - 1)
        self.assertFalse(QualityNote.objects.filter(pk=note_to_delete.pk).exists())

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The Django modernization plan leverages HTMX and Alpine.js to create a highly dynamic and responsive user experience, moving away from traditional full-page reloads.

*   **HTMX for Dynamic Content Loading:**
    *   The `qualitynote_list.html` page initially loads, and then uses `hx-get="{% url 'qualitynote_table' %}" hx-trigger="load, refreshQualityNoteList from:body" hx-swap="innerHTML"` on a container element. This means the actual table content (`_qualitynote_table.html`) is fetched and inserted into the page *after* the initial page load, and is automatically refreshed whenever the `refreshQualityNoteList` custom HTMX event is triggered from any part of the `body`.
    *   CRUD operations (Add, Edit, Delete) are all triggered by `hx-get` attributes on buttons. These requests fetch the respective form/confirmation partials (`_qualitynote_form.html`, `_qualitynote_confirm_delete.html`) and load them into a modal container (`#modalContent`) using `hx-target` and `hx-trigger="click"`.
    *   Form submissions (`hx-post`) from within the modal use `hx-swap="none"`. Upon successful processing in the Django view, a `status=204` (No Content) response is sent, which tells HTMX not to swap any content. Crucially, an `HX-Trigger: refreshQualityNoteList` header is included, which then causes the main list table to refresh dynamically, showing the updated data without a full page reload or even closing the modal by HTMX itself.
*   **Alpine.js for UI State Management:**
    *   The modal's visibility (`#modal`) is primarily controlled by `_hyperscript` (`_="on click add .is-active to #modal"` and `_="on click remove .is-active from me"`). This lightweight library is perfect for simple DOM manipulations related to HTMX.
    *   While `x-data` and `x-show` are included as examples for Alpine.js, for this specific modal behavior, `_hyperscript` is more directly integrated with the HTMX flow. Alpine.js would be used for more complex client-side state, like tabbed interfaces, dynamic filtering, or displaying local component data. The `loadingNotifier.js` from the original ASP.NET hints at such client-side feedback, which Alpine.js would elegantly replace.
*   **DataTables for List Views:**
    *   The `_qualitynote_table.html` partial includes the `<table id="qualitynoteTable">` and the necessary JavaScript to initialize DataTables.
    *   The `$(document).ready(function() { ... });` is wrapped in a `htmx:afterOnLoad` event listener in `list.html`. This ensures that DataTables is initialized only *after* the HTMX request successfully loads the table HTML into the DOM, making the table searchable, sortable, and paginated dynamically.
*   **Seamless Interaction:** All user interactions (adding, editing, deleting) occur without a full page refresh. The user stays on the dashboard, and only the relevant parts of the UI are updated, providing a modern Single-Page Application (SPA) like experience with minimal custom JavaScript.

## Final Notes

This comprehensive plan transforms the rudimentary ASP.NET dashboard into a modern, efficient Django application.

*   **Placeholders have been replaced:** All `[PLACEHOLDER]` values have been replaced with `QualityNote`, `quality_control`, `tblQualityNote`, and relevant field names.
*   **DRY templates:** Partial templates (`_qualitynote_table.html`, `_qualitynote_form.html`, `_qualitynote_confirm_delete.html`) are used for reusable components, extending `core/base.html` for a consistent layout.
*   **Fat Model, Thin View:** Business logic (e.g., `is_open`, `close_note`, `update_status` methods) resides within the `QualityNote` model, keeping views concise and focused on request handling.
*   **Comprehensive Tests:** Unit tests validate model methods and properties, while integration tests verify the functionality of all views and their HTMX interactions, ensuring reliability and maintainability.
*   **HTMX/Alpine.js Integration:** The design ensures all dynamic interactions, form submissions, and data presentations are handled exclusively using HTMX and Alpine.js, minimizing custom JavaScript and adhering to the specified technology stack.