## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

Given the provided ASP.NET code is a placeholder dashboard page without explicit UI controls or backend logic, we'll infer a common scenario for a "Masters" module within a "Quality Control" system. We will assume the dashboard serves to manage "Quality Parameters" – a fundamental master data entity. This approach allows us to demonstrate a complete modernization strategy, including CRUD operations, data display, and modern frontend interactions, even from minimal starting code.

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code for "Dashboard.aspx" and its code-behind is empty of specific data operations or UI bindings, we infer a typical master data table relevant to a "Quality Control Masters" module.

**Inferred Database Table:** `tblQualityParameter`

**Inferred Columns:**
- `QualityParameterID` (Primary Key, Integer)
- `ParameterName` (String, e.g., NVARCHAR(255))
- `Description` (String, e.g., NVARCHAR(MAX) or Text)
- `IsActive` (Boolean)

**[TABLE_NAME] = `tblQualityParameter`**

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the ASP.NET code is a placeholder dashboard page, no specific CRUD operations are present. For a "Masters" module, we infer the need for full CRUD functionality for the `tblQualityParameter` table.

- **Create:** Ability to add new `Quality Parameters`.
- **Read:** Display a list of all `Quality Parameters` on the dashboard.
- **Update:** Ability to edit existing `Quality Parameters`.
- **Delete:** Ability to remove `Quality Parameters`.
- **Validation:** Basic field validation (e.g., `ParameterName` is required, `IsActive` is a boolean).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

Since no specific ASP.NET controls are present in the provided `.aspx` file, we infer the following standard UI components for a master data management dashboard:

- **List View:** A table (analogous to an ASP.NET `GridView`) to display all `Quality Parameters`, featuring client-side search, sort, and pagination. This will be implemented using **DataTables**.
- **Form for Input:** A modal form (replacing typical `TextBox`, `DropDownList`, `Button` interactions) for creating and editing `Quality Parameters`. This form will be loaded dynamically using **HTMX**.
- **Action Buttons:** Buttons for "Add New", "Edit", and "Delete" for each `Quality Parameter` record, triggering HTMX requests to load forms in a modal.
- **Client-Side Interactions:** Use **HTMX** for all dynamic content loading and form submissions, and **Alpine.js** for modal state management (e.g., showing/hiding the modal).

## Step 4: Generate Django Code

We will create a new Django application named `qualitycontrol` for this module.

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

The `QualityParameter` model will directly map to the `tblQualityParameter` database table. We will include a simple business logic method `toggle_active_status` to demonstrate the "fat model" principle.

**File: `qualitycontrol/models.py`**
```python
from django.db import models

class QualityParameter(models.Model):
    """
    Represents a master quality parameter in the system.
    Maps to the existing tblQualityParameter database table.
    """
    quality_parameter_id = models.AutoField(
        db_column='QualityParameterID',
        primary_key=True
    )
    parameter_name = models.CharField(
        db_column='ParameterName',
        max_length=255,
        unique=True,
        verbose_name='Parameter Name'
    )
    description = models.TextField(
        db_column='Description',
        blank=True,
        null=True,
        verbose_name='Description'
    )
    is_active = models.BooleanField(
        db_column='IsActive',
        default=True,
        verbose_name='Is Active'
    )

    class Meta:
        managed = False  # Important: Django won't manage this table's creation/alteration
        db_table = 'tblQualityParameter'
        verbose_name = 'Quality Parameter'
        verbose_name_plural = 'Quality Parameters'
        ordering = ['parameter_name']

    def __str__(self):
        return self.parameter_name

    def toggle_active_status(self):
        """
        Business logic: Toggles the active status of the quality parameter.
        Returns True if successful, False otherwise.
        """
        if not self.quality_parameter_id:
            # Cannot toggle if the object is not yet saved to the database
            return False
        self.is_active = not self.is_active
        self.save()
        return True

    @property
    def status_display(self):
        """
        Returns a user-friendly string for the active status.
        """
        return "Active" if self.is_active else "Inactive"

    def get_absolute_url(self):
        """
        Returns the URL to access a particular instance of QualityParameter.
        """
        from django.urls import reverse
        return reverse('qualityparameter_detail', args=[str(self.quality_parameter_id)])
```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be used for the `QualityParameter` model, including fields for `parameter_name`, `description`, and `is_active`. Widgets will be styled with Tailwind CSS classes.

**File: `qualitycontrol/forms.py`**
```python
from django import forms
from .models import QualityParameter

class QualityParameterForm(forms.ModelForm):
    """
    Form for creating and updating QualityParameter instances.
    """
    class Meta:
        model = QualityParameter
        fields = ['parameter_name', 'description', 'is_active']
        widgets = {
            'parameter_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        labels = {
            'parameter_name': 'Parameter Name',
            'description': 'Description',
            'is_active': 'Is Active',
        }
        
    def clean_parameter_name(self):
        """
        Custom validation for parameter_name to ensure uniqueness (case-insensitive).
        """
        parameter_name = self.cleaned_data['parameter_name']
        
        # Check if updating an existing object and the name hasn't changed
        if self.instance.pk and self.instance.parameter_name.lower() == parameter_name.lower():
            return parameter_name
        
        # Check for uniqueness for new objects or if name has changed
        if QualityParameter.objects.filter(parameter_name__iexact=parameter_name).exists():
            raise forms.ValidationError("A quality parameter with this name already exists.")
        return parameter_name
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

We'll define `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for `QualityParameter`. A special `TablePartialView` will be added to render just the DataTables portion, enabling HTMX-driven table refreshes. Views are kept lean, pushing logic to models and forms.

**File: `qualitycontrol/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import QualityParameter
from .forms import QualityParameterForm

# --- Main Views ---
class QualityParameterListView(ListView):
    """
    Displays a list of all Quality Parameters.
    This view serves the initial full page load for the dashboard.
    """
    model = QualityParameter
    template_name = 'qualitycontrol/qualityparameter/list.html'
    context_object_name = 'qualityparameters' # Renamed for clarity and consistency

class QualityParameterCreateView(CreateView):
    """
    Handles creation of a new Quality Parameter.
    Accessed via HTMX for modal form submission.
    """
    model = QualityParameter
    form_class = QualityParameterForm
    template_name = 'qualitycontrol/qualityparameter/_qualityparameter_form.html' # Partial template
    success_url = reverse_lazy('qualityparameter_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Quality Parameter added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, return a 204 No Content with a trigger header
            # This allows the modal to close and the list to refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQualityParameterList'
                }
            )
        return response # For standard POST requests (unlikely with HTMX)

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors inside the modal
        return self.render_to_response(self.get_context_data(form=form))


class QualityParameterUpdateView(UpdateView):
    """
    Handles updating an existing Quality Parameter.
    Accessed via HTMX for modal form submission.
    """
    model = QualityParameter
    form_class = QualityParameterForm
    template_name = 'qualitycontrol/qualityparameter/_qualityparameter_form.html' # Partial template
    pk_url_kwarg = 'pk' # Ensure correct primary key lookup
    success_url = reverse_lazy('qualityparameter_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Quality Parameter updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQualityParameterList'
                }
            )
        return response

    def form_invalid(self, form):
        return self.render_to_response(self.get_context_data(form=form))


class QualityParameterDeleteView(DeleteView):
    """
    Handles deletion of a Quality Parameter.
    Accessed via HTMX for modal confirmation and deletion.
    """
    model = QualityParameter
    template_name = 'qualitycontrol/qualityparameter/_qualityparameter_confirm_delete.html' # Partial template
    pk_url_kwarg = 'pk'
    success_url = reverse_lazy('qualityparameter_list') # Fallback

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Quality Parameter deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshQualityParameterList'
                }
            )
        return response

# --- HTMX Partial Views ---
class QualityParameterTablePartialView(ListView):
    """
    Renders only the DataTables portion of the Quality Parameter list.
    Designed to be fetched via HTMX to dynamically update the table.
    """
    model = QualityParameter
    template_name = 'qualitycontrol/qualityparameter/_qualityparameter_table.html'
    context_object_name = 'qualityparameters'

    def get_queryset(self):
        # Optionally add filtering/ordering logic here if needed by DataTables AJAX requests
        return super().get_queryset()

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

- **List Template (`qualitycontrol/qualityparameter/list.html`):** The main page that extends `core/base.html`, sets up the HTMX modal, and defines the area where the DataTables content will be loaded.
- **Table Partial Template (`qualitycontrol/qualityparameter/_qualityparameter_table.html`):** Contains the actual `<table>` structure, iterating through `qualityparameters` for display. This is the component loaded by HTMX. Includes DataTables initialization script.
- **Form Partial Template (`qualitycontrol/qualityparameter/_qualityparameter_form.html`):** Used for both create and update operations, rendered inside the modal via HTMX. Includes form rendering and submit/cancel buttons.
- **Delete Partial Template (`qualitycontrol/qualityparameter/_qualityparameter_confirm_delete.html`):** Displays a confirmation message for deletion, also rendered inside the modal.

**File: `qualitycontrol/templates/qualitycontrol/qualityparameter/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Quality Parameters</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'qualityparameter_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Quality Parameter
        </button>
    </div>
    
    <div id="qualityparameterTable-container"
         hx-trigger="load, refreshQualityParameterList from:body"
         hx-get="{% url 'qualityparameter_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Quality Parameters...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For this simple modal, HTMX handles the content and Alpine for basic state
        // For example, if you wanted Alpine to manage a global modal state:
        // Alpine.data('modal', () => ({
        //     isOpen: false,
        //     open() { this.isOpen = true },
        //     close() { this.isOpen = false },
        // }));
    });

    // Listen for HTMX triggers to close modal and handle messages
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            // If HTMX returns 204 (success for form submission/delete)
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active'); // Close the modal
            }
        }
    });

    // Manually close modal on escape key for accessibility
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const modal = document.getElementById('modal');
            if (modal && modal.classList.contains('is-active')) {
                modal.classList.remove('is-active');
                modal.querySelector('#modalContent').innerHTML = ''; // Clear content
            }
        }
    });

</script>
{% endblock %}
```

**File: `qualitycontrol/templates/qualitycontrol/qualityparameter/_qualityparameter_table.html`**
```html
<div class="overflow-x-auto bg-white rounded-lg shadow overflow-y-auto relative">
    <table id="qualityparameterTable" class="min-w-full bg-white text-sm">
        <thead>
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter Name</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in qualityparameters %}
            <tr>
                <td class="py-3 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200">{{ obj.parameter_name }}</td>
                <td class="py-3 px-4 border-b border-gray-200">{{ obj.description|default:"-" }}</td>
                <td class="py-3 px-4 border-b border-gray-200">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                 {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ obj.status_display }}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2 text-xs"
                        hx-get="{% url 'qualityparameter_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                        hx-get="{% url 'qualityparameter_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500">No quality parameters found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization errors
    if ($.fn.DataTable.isDataTable('#qualityparameterTable')) {
        $('#qualityparameterTable').DataTable().destroy();
    }
    $('#qualityparameterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 4] }, // Disable sorting on SN and Actions columns
            { "searchable": false, "targets": [0, 4] } // Disable searching on SN and Actions columns
        ]
    });
});
</script>
```

**File: `qualitycontrol/templates/qualitycontrol/qualityparameter/_qualityparameter_form.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Quality Parameter</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 gap-y-4">
            {% for field in form %}
            <div class="flex flex-col">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {% if field.field.widget.input_type == 'checkbox' %}
                    <div class="flex items-center">
                        {{ field }}
                        <span class="ml-2 text-sm text-gray-900">{{ field.label }}</span>
                    </div>
                {% else %}
                    {{ field }}
                {% endif %}
                {% if field.help_text %}
                    <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="list-none p-0 mt-1">
                    {% for error in field.errors %}
                    <li class="text-red-500 text-xs">{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `qualitycontrol/templates/qualitycontrol/qualityparameter/_qualityparameter_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Quality Parameter: 
        <strong class="font-medium text-red-600">{{ object.parameter_name }}</strong>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'qualityparameter_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URLs are defined for the main list view, and then separate paths for HTMX-loaded partials (add, edit, delete forms, and the table itself).

**File: `qualitycontrol/urls.py`**
```python
from django.urls import path
from .views import (
    QualityParameterListView,
    QualityParameterCreateView,
    QualityParameterUpdateView,
    QualityParameterDeleteView,
    QualityParameterTablePartialView, # For HTMX partial table refresh
)

urlpatterns = [
    # Main page for Quality Parameters dashboard
    path('qualityparameters/', QualityParameterListView.as_view(), name='qualityparameter_list'),
    
    # HTMX endpoints for modal forms and table
    path('qualityparameters/add/', QualityParameterCreateView.as_view(), name='qualityparameter_add'),
    path('qualityparameters/edit/<int:pk>/', QualityParameterUpdateView.as_view(), name='qualityparameter_edit'),
    path('qualityparameters/delete/<int:pk>/', QualityParameterDeleteView.as_view(), name='qualityparameter_delete'),
    path('qualityparameters/table/', QualityParameterTablePartialView.as_view(), name='qualityparameter_table'),
]
```
**Don't forget to include these URLs in your project's main `urls.py`:**
```python
# In your project's urls.py (e.g., myproject/urls.py)
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('qualitycontrol/', include('qualitycontrol.urls')), # Include the app's URLs
    # ... other project urls
]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `QualityParameter` model, covering its creation, field values, verbose names, and the `toggle_active_status` method. Integration tests for all views, covering status codes, template usage, form rendering, and successful/unsuccessful POST requests, including HTMX-specific responses.

**File: `qualitycontrol/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import QualityParameter
from .forms import QualityParameterForm
import json # To check for HTMX JSON responses if any, though our HTMX returns 204

class QualityParameterModelTest(TestCase):
    """
    Tests for the QualityParameter model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.quality_parameter_active = QualityParameter.objects.create(
            parameter_name='Test Parameter One',
            description='This is a description for parameter one.',
            is_active=True
        )
        cls.quality_parameter_inactive = QualityParameter.objects.create(
            parameter_name='Test Parameter Two',
            description='This is a description for parameter two.',
            is_active=False
        )
  
    def test_quality_parameter_creation(self):
        """Test basic object creation and field values."""
        self.assertEqual(self.quality_parameter_active.parameter_name, 'Test Parameter One')
        self.assertEqual(self.quality_parameter_active.description, 'This is a description for parameter one.')
        self.assertTrue(self.quality_parameter_active.is_active)
        self.assertFalse(self.quality_parameter_inactive.is_active)
        self.assertEqual(QualityParameter.objects.count(), 2)

    def test_parameter_name_label(self):
        """Test verbose name for parameter_name field."""
        field_label = self.quality_parameter_active._meta.get_field('parameter_name').verbose_name
        self.assertEqual(field_label, 'Parameter Name')
        
    def test_description_label(self):
        """Test verbose name for description field."""
        field_label = self.quality_parameter_active._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Description')

    def test_is_active_label(self):
        """Test verbose name for is_active field."""
        field_label = self.quality_parameter_active._meta.get_field('is_active').verbose_name
        self.assertEqual(field_label, 'Is Active')

    def test_str_method(self):
        """Test the __str__ method returns the parameter name."""
        self.assertEqual(str(self.quality_parameter_active), 'Test Parameter One')

    def test_db_table_and_managed_false(self):
        """Test Meta options for db_table and managed."""
        self.assertEqual(self.quality_parameter_active._meta.db_table, 'tblQualityParameter')
        self.assertFalse(self.quality_parameter_active._meta.managed)

    def test_toggle_active_status(self):
        """Test the business logic method for toggling active status."""
        # Test from active to inactive
        self.assertTrue(self.quality_parameter_active.is_active)
        self.assertTrue(self.quality_parameter_active.toggle_active_status())
        self.assertFalse(self.quality_parameter_active.is_active)
        
        # Test from inactive to active
        self.assertFalse(self.quality_parameter_inactive.is_active)
        self.assertTrue(self.quality_parameter_inactive.toggle_active_status())
        self.assertTrue(self.quality_parameter_inactive.is_active)

    def test_toggle_active_status_unsaved_object(self):
        """Test toggling status on an unsaved object."""
        new_param = QualityParameter(parameter_name="Unsaved Param", description="Desc")
        self.assertFalse(new_param.toggle_active_status()) # Should return False as it's not saved
        self.assertIsNone(new_param.pk) # Still unsaved

    def test_status_display_property(self):
        """Test the status_display property."""
        self.assertEqual(self.quality_parameter_active.status_display, 'Active')
        self.assertEqual(self.quality_parameter_inactive.status_display, 'Inactive')

class QualityParameterViewsTest(TestCase):
    """
    Integration tests for QualityParameter views using Django's test client.
    """
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all view tests
        cls.quality_parameter_1 = QualityParameter.objects.create(
            parameter_name='View Test Param 1',
            description='Description for view test param 1.',
            is_active=True
        )
        cls.quality_parameter_2 = QualityParameter.objects.create(
            parameter_name='View Test Param 2',
            description='Description for view test param 2.',
            is_active=False
        )
    
    def setUp(self):
        # Set up a new client for each test method
        self.client = Client()

    def test_list_view_get(self):
        """Test the main list view (full page load)."""
        response = self.client.get(reverse('qualityparameter_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/list.html')
        self.assertIn('qualityparameters', response.context)
        self.assertEqual(len(response.context['qualityparameters']), 2)
        self.assertContains(response, 'View Test Param 1')
        self.assertContains(response, 'Add New Quality Parameter') # Check for add button

    def test_table_partial_view_get(self):
        """Test the HTMX partial for the table itself."""
        response = self.client.get(reverse('qualityparameter_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_table.html')
        self.assertIn('qualityparameters', response.context)
        self.assertContains(response, 'View Test Param 1')
        self.assertContains(response, 'Edit') # Check for action buttons

    def test_create_view_get_htmx(self):
        """Test GET request for create form via HTMX (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('qualityparameter_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add Quality Parameter') # Check for form title

    def test_create_view_post_success_htmx(self):
        """Test successful POST request for create via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'parameter_name': 'New HTMX Param',
            'description': 'Description for new HTMX param.',
            'is_active': 'on'
        }
        response = self.client.post(reverse('qualityparameter_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshQualityParameterList')
        self.assertTrue(QualityParameter.objects.filter(parameter_name='New HTMX Param').exists())
        self.assertEqual(QualityParameter.objects.count(), 3) # Verify object was created

    def test_create_view_post_invalid_htmx(self):
        """Test invalid POST request for create via HTMX (form with errors)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'parameter_name': '', # Invalid: empty name
            'description': 'Description for new HTMX param.',
            'is_active': 'on'
        }
        response = self.client.post(reverse('qualityparameter_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Should return form with errors
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'This field is required.') # Check for validation error message
        self.assertFalse(QualityParameter.objects.filter(parameter_name='').exists()) # Object not created
        self.assertEqual(QualityParameter.objects.count(), 2) # Count remains unchanged

    def test_create_view_post_duplicate_name_htmx(self):
        """Test POST request for create with duplicate name via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'parameter_name': 'View Test Param 1', # Duplicate name
            'description': 'Another description',
            'is_active': 'on'
        }
        response = self.client.post(reverse('qualityparameter_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Should return form with errors
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'A quality parameter with this name already exists.')
        self.assertEqual(QualityParameter.objects.count(), 2) # Count remains unchanged

    def test_update_view_get_htmx(self):
        """Test GET request for update form via HTMX (modal)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('qualityparameter_edit', args=[self.quality_parameter_1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Edit Quality Parameter') # Check for form title
        self.assertContains(response, 'View Test Param 1') # Check for existing data

    def test_update_view_post_success_htmx(self):
        """Test successful POST request for update via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'parameter_name': 'Updated HTMX Param',
            'description': 'Updated description.',
            'is_active': 'off'
        }
        response = self.client.post(reverse('qualityparameter_edit', args=[self.quality_parameter_1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshQualityParameterList')
        
        self.quality_parameter_1.refresh_from_db() # Reload object from DB
        self.assertEqual(self.quality_parameter_1.parameter_name, 'Updated HTMX Param')
        self.assertFalse(self.quality_parameter_1.is_active)
        self.assertEqual(QualityParameter.objects.count(), 2) # Count remains unchanged

    def test_update_view_post_invalid_htmx(self):
        """Test invalid POST request for update via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'parameter_name': '', # Invalid: empty name
            'description': 'Updated description.',
            'is_active': 'on'
        }
        response = self.client.post(reverse('qualityparameter_edit', args=[self.quality_parameter_1.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'This field is required.')
        self.quality_parameter_1.refresh_from_db()
        self.assertNotEqual(self.quality_parameter_1.parameter_name, '') # Ensure it wasn't updated
        self.assertEqual(QualityParameter.objects.count(), 2)

    def test_update_view_post_duplicate_name_htmx(self):
        """Test POST request for update with duplicate name via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'parameter_name': self.quality_parameter_2.parameter_name, # Try to set to another existing name
            'description': 'Description.',
            'is_active': 'on'
        }
        response = self.client.post(reverse('qualityparameter_edit', args=[self.quality_parameter_1.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_form.html')
        self.assertContains(response, 'A quality parameter with this name already exists.')
        self.quality_parameter_1.refresh_from_db()
        self.assertNotEqual(self.quality_parameter_1.parameter_name, self.quality_parameter_2.parameter_name) # Ensure not updated

    def test_delete_view_get_htmx(self):
        """Test GET request for delete confirmation via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('qualityparameter_delete', args=[self.quality_parameter_1.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/qualityparameter/_qualityparameter_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.quality_parameter_1)
        self.assertContains(response, f'Are you sure you want to delete the Quality Parameter: {self.quality_parameter_1.parameter_name}?')

    def test_delete_view_post_success_htmx(self):
        """Test successful POST request for delete via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Create a new object to delete, so it doesn't affect other tests
        param_to_delete = QualityParameter.objects.create(parameter_name='To Delete', description='Temporary')
        
        response = self.client.post(reverse('qualityparameter_delete', args=[param_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshQualityParameterList')
        self.assertFalse(QualityParameter.objects.filter(pk=param_to_delete.pk).exists())
        self.assertEqual(QualityParameter.objects.count(), 2) # Count should be back to initial 2
```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

- **HTMX for all dynamic updates:**
    - The main `list.html` loads the table content using `hx-get="{% url 'qualityparameter_table' %}"` on `load` and on a custom `refreshQualityParameterList` trigger.
    - "Add New", "Edit", and "Delete" buttons use `hx-get` to load forms/confirmation dialogs into the `#modalContent` div.
    - Form submissions use `hx-post` with `hx-swap="none"` and return a `204 No Content` status with an `HX-Trigger` header (`refreshQualityParameterList`) to signal the client to close the modal and refresh the table.
    - Delete operations similarly use `hx-post` and trigger a list refresh.
- **Alpine.js for UI state management:**
    - The modal visibility (`hidden` class) is controlled using `_` (hyperscript syntax provided by HTMX and Alpine.js) on click events: `_="on click add .is-active to #modal"` and `_="on click if event.target.id == 'modal' remove .is-active from me"`. This is a compact way to manage simple UI state.
- **DataTables for all list views:**
    - The `_qualityparameter_table.html` partial template contains the `<table>` element with an `id="qualityparameterTable"`.
    - A `<script>` block within this partial initializes DataTables on `$(document).ready()`, ensuring proper re-initialization when the partial is re-loaded via HTMX. This handles client-side searching, sorting, and pagination automatically.
- **No full page reloads:** All CRUD operations and table refreshes are handled dynamically without full page reloads, providing a smooth user experience.
- **DRY template inheritance:** All templates implicitly extend `core/base.html` (as per rules, its content is not provided here but assumed to include necessary CDN links for HTMX, Alpine.js, jQuery, and DataTables).

## Final Notes

- This plan provides a robust, modern Django solution for managing `Quality Parameters`, demonstrating the capabilities of fat models, thin views, HTMX, Alpine.js, and DataTables, all while adhering to the specified architecture and best practices.
- The `QualityParameterID` being an `AutoField` and `primary_key=True` aligns with the likely scenario of an auto-incrementing primary key in the legacy ASP.NET database.
- Remember to configure your Django `settings.py` to connect to your existing ASP.NET database and add `qualitycontrol` to `INSTALLED_APPS`.
- Ensure all necessary CDN links for HTMX, Alpine.js, jQuery, and DataTables are present in your `core/base.html` for the frontend to function correctly.
- This comprehensive structure allows for automated generation and systematic migration of similar master data management modules within the legacy ASP.NET application.