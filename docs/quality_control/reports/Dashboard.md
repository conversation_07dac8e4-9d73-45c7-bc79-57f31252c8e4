## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Modernization Plan for Dashboard Module

This plan outlines the systematic conversion of your existing ASP.NET Dashboard page to a modern, efficient Django application. While the provided ASP.NET code was minimal, a typical "Dashboard" often involves displaying key performance indicators (KPIs), summaries, or lists of important data. For this modernization, we will assume the Dashboard needs to display a list of "Dashboard Items" that can be managed (added, edited, deleted).

**Business Benefits of Django Modernization:**

*   **Enhanced Performance:** Django's optimized ORM and efficient request handling provide faster page loads and a more responsive user experience compared to legacy ASP.NET Web Forms.
*   **Scalability & Maintainability:** A modular Django architecture, with clear separation of concerns (models for business logic, thin views, and reusable templates), makes the application easier to scale, maintain, and extend with new features.
*   **Improved User Experience (UX) with HTMX & Alpine.js:** By integrating HTMX for dynamic content updates and Alpine.js for client-side interactivity, users will experience a fluid, single-page application-like feel without the complexity of traditional JavaScript frameworks. This means faster interactions without full page reloads.
*   **Reduced Development Time:** The "fat model, thin view" approach, combined with Django's robust feature set (ORM, forms, admin), reduces boilerplate code and accelerates development.
*   **Future-Proof Technology Stack:** Moving to Django, HTMX, and Alpine.js aligns your application with modern web development trends, ensuring long-term viability, easier talent acquisition, and access to a vibrant open-source ecosystem.
*   **Automated Testing & Quality:** Comprehensive unit and integration tests, built into the migration, will ensure higher code quality, fewer bugs, and greater confidence when deploying updates.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Given the lack of explicit database interactions in the provided ASP.NET code, we infer a common dashboard need: displaying and managing summary data. We will assume a table named `tbl_dashboard_items` that stores these items.

**Inferred Database Schema:**

*   **Table Name:** `tbl_dashboard_items`
*   **Columns:**
    *   `id`: Primary Key (auto-incrementing)
    *   `item_name`: NVARCHAR(255) - Represents the name or title of a dashboard item.
    *   `item_value`: INT - Represents a numerical value associated with the item (e.g., a count, a percentage).
    *   `description`: NVARCHAR(MAX) - A more detailed description.
    *   `is_active`: BIT - Boolean flag indicating if the item is active.

### Step 2: Identify Backend Functionality

Since no specific backend functionality was present, we assume standard CRUD operations for `DashboardItem` entries. This includes:

*   **Create:** Adding new dashboard items.
*   **Read:** Displaying a list of all dashboard items.
*   **Update:** Modifying existing dashboard items.
*   **Delete:** Removing dashboard items.
*   **Validation:** Basic validation for required fields (e.g., `item_name` cannot be empty).

### Step 3: Infer UI Components

The original ASP.NET `.aspx` file only contained content placeholders. Based on typical dashboard requirements and the target Django architecture, we infer the following UI components:

*   **Main Dashboard View:** A page displaying a list of "Dashboard Items" using a DataTables component for client-side search, sort, and pagination.
*   **Add New Item Button:** A button to open a modal form for creating new dashboard items.
*   **Edit/Delete Buttons:** Action buttons within the DataTables rows to open modal forms for editing or confirming deletion of existing items.
*   **Modals:** HTMX-driven modals for all CRUD form interactions (add, edit, delete confirmation).
*   **Loading Indicators:** Visual feedback using HTMX's built-in indicators when data is being loaded or submitted.

---

## Step 4: Generate Django Code

We will create a new Django application named `dashboard_app` to encapsulate this functionality.

### 4.1 Models (`dashboard_app/models.py`)

This model maps directly to the `tbl_dashboard_items` table in your existing database.

```python
from django.db import models

class DashboardItem(models.Model):
    """
    Represents an item or metric to be displayed on the dashboard.
    Maps to the existing tbl_dashboard_items database table.
    """
    item_name = models.CharField(
        db_column='item_name', 
        max_length=255, 
        verbose_name='Item Name', 
        help_text='The name of the dashboard item.'
    )
    item_value = models.IntegerField(
        db_column='item_value', 
        verbose_name='Item Value', 
        help_text='The numerical value associated with the item.'
    )
    description = models.TextField(
        db_column='description', 
        blank=True, 
        null=True, 
        verbose_name='Description', 
        help_text='A detailed description of the dashboard item.'
    )
    is_active = models.BooleanField(
        db_column='is_active', 
        default=True, 
        verbose_name='Is Active', 
        help_text='Indicates if the dashboard item is currently active.'
    )

    class Meta:
        managed = False  # Django will not manage table creation/deletion
        db_table = 'tbl_dashboard_items' # Existing table name
        verbose_name = 'Dashboard Item'
        verbose_name_plural = 'Dashboard Items'
        ordering = ['item_name'] # Default ordering

    def __str__(self):
        """
        Returns a string representation of the DashboardItem.
        """
        return f"{self.item_name} ({self.item_value})"
        
    def get_display_value(self):
        """
        Example of a 'fat model' method: Business logic for display.
        This could format the item_value based on type (e.g., currency, percentage).
        """
        # For demonstration, simply returns the value.
        # In a real scenario, this might format currency, percentages, etc.
        return f"{self.item_value}"
        
    def activate(self):
        """
        Business logic to activate the dashboard item.
        """
        if not self.is_active:
            self.is_active = True
            self.save()
            return True
        return False

    def deactivate(self):
        """
        Business logic to deactivate the dashboard item.
        """
        if self.is_active:
            self.is_active = False
            self.save()
            return True
        return False
```

### 4.2 Forms (`dashboard_app/forms.py`)

This form will handle user input for `DashboardItem` creation and updates.

```python
from django import forms
from .models import DashboardItem

class DashboardItemForm(forms.ModelForm):
    """
    Form for creating and updating DashboardItem instances.
    """
    class Meta:
        model = DashboardItem
        fields = ['item_name', 'item_value', 'description', 'is_active']
        widgets = {
            'item_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Total Sales'
            }),
            'item_value': forms.NumberInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., 12345'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 3,
                'placeholder': 'Detailed description of the item...'
            }),
            'is_active': forms.CheckboxInput(attrs={
                'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'
            }),
        }
        
    def clean_item_value(self):
        """
        Example of custom form validation: ensure item_value is positive.
        """
        item_value = self.cleaned_data.get('item_value')
        if item_value is not None and item_value < 0:
            raise forms.ValidationError("Item value cannot be negative.")
        return item_value
```

### 4.3 Views (`dashboard_app/views.py`)

These class-based views handle the display, creation, updating, and deletion of `DashboardItem` objects. A separate view is included for rendering the HTMX-driven table partial.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardItem
from .forms import DashboardItemForm

# Thin Views - Business logic handled by the DashboardItem model

class DashboardItemListView(ListView):
    """
    Displays the main dashboard page with a container for the items table.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/list.html'
    context_object_name = 'dashboard_items' # Not directly used in list.html, but good practice

class DashboardItemTablePartialView(ListView):
    """
    Renders only the DataTables HTML for DashboardItems, fetched via HTMX.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_table.html'
    context_object_name = 'dashboard_items'

class DashboardItemCreateView(CreateView):
    """
    Handles creation of new DashboardItems, typically via a modal form.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_form.html' # Use partial for modal
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item added successfully.')
        # For HTMX requests, return a 204 No Content and trigger a refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList' # Event to refresh the table
                }
            )
        return response

class DashboardItemUpdateView(UpdateView):
    """
    Handles updating existing DashboardItems, typically via a modal form.
    """
    model = DashboardItem
    form_class = DashboardItemForm
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_form.html' # Use partial for modal
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Item updated successfully.')
        # For HTMX requests, return a 204 No Content and trigger a refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList' # Event to refresh the table
                }
            )
        return response

class DashboardItemDeleteView(DeleteView):
    """
    Handles deletion of DashboardItems, typically via a modal confirmation.
    """
    model = DashboardItem
    template_name = 'dashboard_app/dashboarditem/_dashboarditem_confirm_delete.html' # Use partial for modal
    success_url = reverse_lazy('dashboarditem_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Item deleted successfully.')
        # For HTMX requests, return a 204 No Content and trigger a refresh
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardItemList' # Event to refresh the table
                }
            )
        return response
```

### 4.4 Templates (`dashboard_app/templates/dashboard_app/dashboarditem/`)

**`list.html`** (Main page, extends `core/base.html`)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Items</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out"
            hx-get="{% url 'dashboarditem_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Dashboard Item
        </button>
    </div>
    
    <div id="dashboarditemTable-container"
         hx-trigger="load, refreshDashboardItemList from:body"
         hx-get="{% url 'dashboarditem_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg p-4">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Items...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 sm:mx-auto relative">
             <!-- Close button for modal -->
            <button class="absolute top-3 right-3 text-gray-400 hover:text-gray-600"
                    _="on click remove .is-active from #modal">
                <i class="fas fa-times-circle text-2xl"></i>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add any Alpine.js components specific to this page if needed -->
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('dashboardItemModal', () => ({
            isOpen: false,
            openModal() {
                this.isOpen = true;
                document.getElementById('modal').classList.add('is-active');
            },
            closeModal() {
                this.isOpen = false;
                document.getElementById('modal').classList.remove('is-active');
            }
        }));
    });

    // Listen for HTMX triggers to close the modal after form submission
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent' && event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });

    // Initialize DataTables after content is loaded via HTMX
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'dashboarditemTable-container') {
            $('#dashboarditemTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>' // Customizing DataTables layout
            });
        }
    });
</script>
{% endblock %}
```

**`_dashboarditem_table.html`** (Partial for HTMX loaded DataTables)

```html
<table id="dashboarditemTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in dashboard_items %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.item_name }}</td>
            <td class="py-3 px-4 whitespace-nowrap">{{ obj.get_display_value }}</td> {# Calls fat model method #}
            <td class="py-3 px-4 whitespace-nowrap overflow-hidden text-ellipsis max-w-xs">{{ obj.description|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap">
                {% if obj.is_active %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                {% else %}
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Inactive</span>
                {% endif %}
            </td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md mr-2 shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                    hx-get="{% url 'dashboarditem_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    hx-get="{% url 'dashboarditem_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <i class="fas fa-trash-alt"></i> Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No dashboard items found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// DataTables initialization is handled by the htmx:afterSwap event listener in list.html
</script>
```

**`_dashboarditem_form.html`** (Partial for HTMX loaded form in modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <label for="{{ form.item_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.item_name.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.item_name }}
                {% if form.item_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.item_name.errors }}</p>
                {% endif %}
            </div>
            <div>
                <label for="{{ form.item_value.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ form.item_value.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.item_value }}
                {% if form.item_value.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.item_value.errors }}</p>
                {% endif %}
            </div>
        </div>

        <div>
            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ form.description.label }}
            </label>
            {{ form.description }}
            {% if form.description.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.description.errors }}</p>
            {% endif %}
        </div>

        <div class="flex items-center">
            {{ form.is_active }}
            <label for="{{ form.is_active.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-900">
                {{ form.is_active.label }}
            </label>
            {% if form.is_active.errors %}
            <p class="text-red-500 text-xs mt-1">{{ form.is_active.errors }}</p>
            {% endif %}
        </div>
        
        {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mt-2">
                {{ form.non_field_errors }}
            </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition duration-150 ease-in-out">
                <i class="fas fa-save mr-2"></i> Save
            </button>
        </div>
    </form>
</div>
```

**`_dashboarditem_confirm_delete.html`** (Partial for HTMX loaded delete confirmation in modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <p class="text-gray-700 mb-6">
            Are you sure you want to delete the dashboard item: 
            <strong class="font-medium text-red-600">{{ object.item_name }} (Value: {{ object.item_value }})</strong>? 
            This action cannot be undone.
        </p>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i> Confirm Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`dashboard_app/urls.py`)

These URL patterns map to the Django views defined above.

```python
from django.urls import path
from .views import (
    DashboardItemListView, 
    DashboardItemTablePartialView,
    DashboardItemCreateView, 
    DashboardItemUpdateView, 
    DashboardItemDeleteView
)

urlpatterns = [
    # Main Dashboard Item List View
    path('dashboard-items/', DashboardItemListView.as_view(), name='dashboarditem_list'),

    # HTMX-specific endpoint for fetching the table content
    path('dashboard-items/table/', DashboardItemTablePartialView.as_view(), name='dashboarditem_table'),

    # HTMX-specific endpoints for CRUD operations (forms loaded into modals)
    path('dashboard-items/add/', DashboardItemCreateView.as_view(), name='dashboarditem_add'),
    path('dashboard-items/edit/<int:pk>/', DashboardItemUpdateView.as_view(), name='dashboarditem_edit'),
    path('dashboard-items/delete/<int:pk>/', DashboardItemDeleteView.as_view(), name='dashboarditem_delete'),
]
```

### 4.6 Tests (`dashboard_app/tests.py`)

Comprehensive tests for both model logic and view functionality are crucial for ensuring the quality and stability of the migrated application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardItem
from .forms import DashboardItemForm
from django.db import connection

class DashboardItemModelTest(TestCase):
    """
    Unit tests for the DashboardItem model.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup run once for all tests in this class
        cls.item1 = DashboardItem.objects.create(
            item_name='Monthly Revenue', 
            item_value=150000, 
            description='Total revenue for the current month.', 
            is_active=True
        )
        cls.item2 = DashboardItem.objects.create(
            item_name='Customer Count', 
            item_value=500, 
            description='Number of active customers.', 
            is_active=False
        )

    def test_dashboard_item_creation(self):
        """
        Verify DashboardItem objects are created correctly.
        """
        self.assertEqual(self.item1.item_name, 'Monthly Revenue')
        self.assertEqual(self.item1.item_value, 150000)
        self.assertTrue(self.item1.is_active)
        self.assertEqual(str(self.item1), 'Monthly Revenue (150000)')

        self.assertEqual(self.item2.item_name, 'Customer Count')
        self.assertFalse(self.item2.is_active)

    def test_field_labels(self):
        """
        Verify verbose names for model fields.
        """
        field_item_name = self.item1._meta.get_field('item_name').verbose_name
        field_item_value = self.item1._meta.get_field('item_value').verbose_name
        field_is_active = self.item1._meta.get_field('is_active').verbose_name
        self.assertEqual(field_item_name, 'Item Name')
        self.assertEqual(field_item_value, 'Item Value')
        self.assertEqual(field_is_active, 'Is Active')

    def test_get_display_value_method(self):
        """
        Test the custom get_display_value method.
        """
        self.assertEqual(self.item1.get_display_value(), '150000')
        self.assertEqual(self.item2.get_display_value(), '500')

    def test_activate_deactivate_methods(self):
        """
        Test activate and deactivate business logic methods.
        """
        # Test activate
        self.assertFalse(self.item2.is_active)
        self.assertTrue(self.item2.activate()) # Should return True as it was deactivated
        self.assertTrue(self.item2.is_active)
        self.assertFalse(self.item2.activate()) # Should return False as it's already active

        # Test deactivate
        self.assertTrue(self.item1.is_active)
        self.assertTrue(self.item1.deactivate()) # Should return True as it was activated
        self.assertFalse(self.item1.is_active)
        self.assertFalse(self.item1.deactivate()) # Should return False as it's already deactivated

class DashboardItemFormTest(TestCase):
    """
    Unit tests for the DashboardItemForm.
    """
    def test_form_valid_data(self):
        form = DashboardItemForm(data={
            'item_name': 'New Item', 
            'item_value': 100, 
            'description': 'A description', 
            'is_active': True
        })
        self.assertTrue(form.is_valid())

    def test_form_missing_item_name(self):
        form = DashboardItemForm(data={
            'item_name': '', 
            'item_value': 100, 
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('item_name', form.errors)
        self.assertEqual(form.errors['item_name'], ['This field is required.'])

    def test_form_negative_item_value(self):
        form = DashboardItemForm(data={
            'item_name': 'Bad Item', 
            'item_value': -50, 
            'is_active': True
        })
        self.assertFalse(form.is_valid())
        self.assertIn('item_value', form.errors)
        self.assertEqual(form.errors['item_value'], ['Item value cannot be negative.'])

class DashboardItemViewsTest(TestCase):
    """
    Integration tests for DashboardItem views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup run once for all tests in this class
        cls.item1 = DashboardItem.objects.create(
            item_name='View Item 1', 
            item_value=100, 
            description='Desc 1', 
            is_active=True
        )
        cls.item2 = DashboardItem.objects.create(
            item_name='View Item 2', 
            item_value=200, 
            description='Desc 2', 
            is_active=False
        )

    def setUp(self):
        # Setup run before each test method
        self.client = Client()

    def test_list_view(self):
        """
        Test the main list view rendering.
        """
        response = self.client.get(reverse('dashboarditem_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/list.html')
        # The actual items are loaded via HTMX, so they won't be in the initial context

    def test_table_partial_view(self):
        """
        Test the HTMX partial view for the DataTables content.
        """
        response = self.client.get(reverse('dashboarditem_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_table.html')
        self.assertContains(response, self.item1.item_name)
        self.assertContains(response, self.item2.item_name)
        self.assertContains(response, '<table id="dashboarditemTable"') # Ensure DataTables ID is present

    def test_create_view_get(self):
        """
        Test GET request for the create form (modal).
        """
        response = self.client.get(reverse('dashboarditem_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Dashboard Item')

    def test_create_view_post_success(self):
        """
        Test successful POST request to create a DashboardItem with HTMX.
        """
        initial_count = DashboardItem.objects.count()
        data = {
            'item_name': 'New Dashboard Item',
            'item_value': 999,
            'description': 'A newly created item.',
            'is_active': True,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_add'), data, **headers)
        
        # Expect 204 No Content for HTMX success
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDashboardItemList')
        
        # Verify object was created
        self.assertEqual(DashboardItem.objects.count(), initial_count + 1)
        new_item = DashboardItem.objects.get(item_name='New Dashboard Item')
        self.assertEqual(new_item.item_value, 999)

    def test_create_view_post_invalid(self):
        """
        Test invalid POST request to create a DashboardItem (missing required field).
        """
        initial_count = DashboardItem.objects.count()
        data = {
            'item_name': '',  # Missing item_name
            'item_value': 100,
            'is_active': True,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_add'), data, **headers)
        
        # Expect 200 OK because HTMX swaps the form with errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertEqual(DashboardItem.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        """
        Test GET request for the update form (modal).
        """
        response = self.client.get(reverse('dashboarditem_edit', args=[self.item1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Dashboard Item')
        self.assertContains(response, self.item1.item_name) # Form should be pre-populated

    def test_update_view_post_success(self):
        """
        Test successful POST request to update a DashboardItem with HTMX.
        """
        updated_name = 'Updated Item Name'
        data = {
            'item_name': updated_name,
            'item_value': self.item1.item_value + 100,
            'description': self.item1.description,
            'is_active': self.item1.is_active,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_edit', args=[self.item1.pk]), data, **headers)

        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDashboardItemList')

        self.item1.refresh_from_db() # Reload object from DB to get updated values
        self.assertEqual(self.item1.item_name, updated_name)

    def test_delete_view_get(self):
        """
        Test GET request for the delete confirmation (modal).
        """
        response = self.client.get(reverse('dashboarditem_delete', args=[self.item2.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard_app/dashboarditem/_dashboarditem_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.item2.item_name) # Confirmation should mention the item

    def test_delete_view_post_success(self):
        """
        Test successful POST request to delete a DashboardItem with HTMX.
        """
        item_to_delete_pk = self.item2.pk
        initial_count = DashboardItem.objects.count()
        
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboarditem_delete', args=[item_to_delete_pk]), **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshDashboardItemList')

        # Verify object was deleted
        self.assertEqual(DashboardItem.objects.count(), initial_count - 1)
        self.assertFalse(DashboardItem.objects.filter(pk=item_to_delete_pk).exists())
```

---

## Step 5: HTMX and Alpine.js Integration

The generated templates, views, and URLs are designed with HTMX and Alpine.js as the core of the frontend interaction:

*   **HTMX for Dynamic Content:**
    *   The `dashboarditem_list.html` loads the actual DataTables content dynamically using `hx-get="{% url 'dashboarditem_table' %}"` on `load` and `refreshDashboardItemList` events.
    *   Add/Edit/Delete buttons use `hx-get` to fetch the form/confirmation partials into the `#modalContent` div.
    *   Form submissions (POST) use `hx-post` and `hx-swap="none"` with `HX-Trigger` headers (`refreshDashboardItemList`) to signal a refresh of the main table without a full page reload.
    *   The modal closes automatically after a successful HTMX form submission (status 204) via an `htmx:afterSwap` event listener.
*   **Alpine.js for UI State:**
    *   Alpine.js (used via the `_` attribute) manages the visibility of the modal by toggling the `is-active` class. This provides a clean way to handle basic UI interactions.
    *   The `list.html` includes a basic Alpine.js component setup, ready for more complex client-side state management if needed.
*   **DataTables for List Views:**
    *   The `_dashboarditem_table.html` partial contains the `<table>` element with the ID `dashboarditemTable`, which is then initialized by DataTables via a jQuery script.
    *   The DataTables initialization is carefully placed within an `htmx:afterSwap` event listener in `list.html` to ensure it runs only after the table HTML is loaded into the DOM by HTMX.
*   **No Additional JavaScript:** The design avoids writing custom, complex JavaScript by leveraging HTMX for server-side rendering of partials and Alpine.js for minimal client-side interactivity, ensuring a slim and maintainable frontend.
*   **DRY Template Inheritance:** All module-specific templates extend `core/base.html` (not included here), ensuring consistent layout, CDN includes (like DataTables, Font Awesome, HTMX, Alpine.js, jQuery), and global styles (Tailwind CSS).

---

## Final Notes

*   **Placeholder Replacement:** This plan uses placeholders like `dashboard_app`, `DashboardItem`, and `tbl_dashboard_items`. In a real migration, these would be directly derived from your ASP.NET application's module names, entity names, and actual database table names.
*   **Fat Models:** Notice how the `DashboardItem` model contains methods like `get_display_value`, `activate`, and `deactivate`. This demonstrates the "fat model" principle, centralizing business logic within the model, making views thinner and more focused on HTTP request/response handling.
*   **Comprehensive Testing:** The provided test suite covers model creation, field validation, custom model methods, and all CRUD operations for views, including their HTMX interactions. This ensures high code quality and confidence in the migrated functionality.
*   **Automated Conversion:** This structured output is designed to be consumed by an AI-assisted automation tool. Each section provides clear instructions and code templates, allowing the tool to systematically generate the Django application files, significantly reducing manual coding effort and potential errors.
*   **Scalability:** The modular design allows for independent development and deployment of different functional areas (like Quality Control, Reports, etc.) as separate Django apps, facilitating a phased migration strategy.