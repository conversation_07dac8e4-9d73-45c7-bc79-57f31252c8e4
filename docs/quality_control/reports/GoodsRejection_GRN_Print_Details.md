## ASP.NET to Django Conversion Script: Goods Rejection GRN Print Details

This document outlines a strategic plan to modernize your legacy ASP.NET Goods Rejection GRN Print Details module by migrating it to a robust and scalable Django 5.0+ application. Our approach leverages automation to transform your existing functionalities into modern, maintainable, and efficient Django components, focusing on business value and seamless user experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

The ASP.NET page `GoodsRejection_GRN_Print_Details.aspx` primarily serves as a report viewer, dynamically compiling data from multiple tables to display goods rejection details. While it doesn't directly support CRUD operations on a single entity, the modernization plan will establish the underlying data models and demonstrate how a "report view" can be built upon a primary data entity (Goods Rejection Detail) that encapsulates all necessary business logic and data aggregation. For completeness and adherence to the prompt's structure, standard CRUD views for this primary entity are also provided.

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in generating the Goods Rejection GRN Print report.

**Instructions:**
The ASP.NET code extensively queries several tables to compile the report. The central entity for each report line item is derived from `tblQc_MaterialQuality_Details`, with information pulled from numerous related tables.

For the purpose of this modernization, we designate `tblQc_MaterialQuality_Details` as the primary table for our main model, `MaterialQualityDetail`. Other relevant tables are listed as auxiliary models, providing the necessary data relationships for the "fat model" approach.

**Primary Model Table:**
- **[TABLE_NAME]:** `tblQc_MaterialQuality_Details`
- **Inferred Columns for `MaterialQualityDetail`:**
    - `Id` (Primary Key)
    - `MId` (Foreign Key to `tblQc_MaterialQuality_Master.Id`)
    - `AcceptedQty` (decimal)
    - `RejectedQty` (decimal)
    - `RejectionReason` (Foreign Key to `tblQc_Rejection_Reason.Id`)
    - `Remarks` (string)
    - `GRRId` (Foreign Key to `tblinv_MaterialReceived_Master.Id` - represents the received GRN ID for the detail)
    - `DGRRId` (Foreign Key to `tblinv_MaterialReceived_Details.Id` - represents the received GRN Detail ID)

**Auxiliary Tables (for data aggregation in fat model):**
- `tblQc_MaterialQuality_Master`
- `tblDG_Item_Master`
- `Unit_Master`
- `tblQc_Rejection_Reason`
- `tblMM_Supplier_master`
- `tblinv_MaterialReceived_Master`
- `tblinv_MaterialReceived_Details`
- `tblInv_Inward_Master`
- `tblInv_Inward_Details`
- `tblMM_PO_Details`
- `tblMM_PO_Master`
- `tblMM_PR_Details`
- `tblMM_PR_Master`
- `tblMM_SPR_Details`
- `tblMM_SPR_Master`
- `tblCompany_Master` (inferred for `fun.CompAdd`)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations of the ASP.NET code.

**Instructions:**
The provided ASP.NET page is a read-only report viewer. Its primary function is to:
- **Read:** Retrieve and aggregate complex data from multiple related tables based on query string parameters (`GRRNo`, `GINNo`, `PONo`, `FyId`, `Id`, `Key`, `CompId`, `FinYearId`).
- **Display:** Present the aggregated data in a formatted report (previously Crystal Report).

While this specific page doesn't expose Create, Update, or Delete operations, the Django modernization plan will include these standard CRUD functionalities for the `MaterialQualityDetail` model to ensure a comprehensive, reusable component, assuming these operations exist elsewhere or are a future requirement for the `tblQc_MaterialQuality_Details` entity. The focus for *this report page* will be on the `ListView` equivalent.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to inform Django template design.

**Instructions:**
- **`CrystalReportViewer`:** This will be replaced by a dynamic HTML table powered by DataTables, HTMX for partial updates, and Alpine.js for interactive UI elements.
- **`Panel` with `ScrollBars="Auto"`:** This implies that the report content can be long and requires scrolling. This will be replicated using Tailwind CSS classes like `overflow-y-auto` and `max-h-96`.
- **`asp:Button ID="btnCancel"`:** This button performs a client-side redirect. In Django, this will be a simple HTML link styled as a button.
- **`MasterPage.master`:** This maps directly to Django's template inheritance, where all specific templates will `{% extends 'core/base.html' %}`.
- **`Css/StyleSheet.css` & `Javascript/loadingNotifier.js`:** Custom CSS will be integrated with Tailwind CSS. JavaScript functionality will be handled by HTMX and Alpine.js.

### Step 4: Generate Django Code

We will create a new Django application, `quality_control`, to house the modernized `Goods Rejection GRN Print Details` module.

#### 4.1 Models

**Task:** Create Django models representing the database schema.

**Instructions:**
We define `MaterialQualityDetail` as our primary model for the report line items. We also define essential auxiliary models with minimal fields required for establishing relationships and enabling the `MaterialQualityDetail` model to fetch its derived report attributes using "fat model" principles. All models will use `managed = False` and `db_table` to map to existing legacy tables.

**File: `quality_control/models.py`**
```python
from django.db import models

# --- Auxiliary Models (for relationships and data lookup) ---

class CompanyMaster(models.Model):
    """
    Represents tblCompany_Master (inferred for fun.CompAdd)
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    address = models.CharField(db_column='Address', max_length=500, blank=True, null=True)
    
    class Meta:
        managed = False
        db_table = 'tblCompany_Master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name or f"Company {self.id}"

    @staticmethod
    def get_company_address(comp_id):
        """
        Mimics fun.CompAdd(CompId)
        """
        try:
            company = CompanyMaster.objects.get(id=comp_id)
            return company.address
        except CompanyMaster.DoesNotExist:
            return "Company Address Not Found"


class MaterialQualityMaster(models.Model):
    """
    Represents tblQc_MaterialQuality_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    finyearid = models.IntegerField(db_column='FinYearId')
    gqn_no = models.CharField(db_column='GQNNo', max_length=50, blank=True, null=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'
        verbose_name = 'Material Quality Master'
        verbose_name_plural = 'Material Quality Masters'

    def __str__(self):
        return self.grr_no or f"MQM {self.id}"


class ItemMaster(models.Model):
    """
    Represents tblDG_Item_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=250, blank=True, null=True)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # FK to Unit_Master

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code or f"Item {self.id}"

    def get_item_code_part_no(self, comp_id):
        """
        Mimics fun.GetItemCode_PartNo(CompId, ItemId)
        This would be a more complex logic, simplified for demonstration.
        """
        return f"{self.item_code}-{self.manf_desc[:10]}"


class UnitMaster(models.Model):
    """
    Represents Unit_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"


class RejectionReason(models.Model):
    """
    Represents tblQc_Rejection_Reason
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)
    description = models.CharField(db_column='Description', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_Rejection_Reason'
        verbose_name = 'Rejection Reason'
        verbose_name_plural = 'Rejection Reasons'

    def __str__(self):
        return f"{self.symbol}-{self.description}"


class SupplierMaster(models.Model):
    """
    Represents tblMM_Supplier_master
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' is the PK, C# uses SupplierId
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True) # Used for lookup
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.supplier_name or f"Supplier {self.id}"


class MaterialReceivedMaster(models.Model):
    """
    Represents tblinv_MaterialReceived_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    gin_no = models.CharField(db_column='GINNo', max_length=50, blank=True, null=True)
    gin_id = models.IntegerField(db_column='GINId', blank=True, null=True) # FK to InwardMaster.id

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'
        verbose_name = 'Material Received Master'
        verbose_name_plural = 'Material Received Masters'

    def __str__(self):
        return self.gin_no or f"MRM {self.id}"


class MaterialReceivedDetail(models.Model):
    """
    Represents tblinv_MaterialReceived_Details
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(MaterialReceivedMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, blank=True, null=True)
    poid = models.IntegerField(db_column='POId', blank=True, null=True) # FK to PoDetail.id

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'
        verbose_name = 'Material Received Detail'
        verbose_name_plural = 'Material Received Details'

    def __str__(self):
        return f"MRD {self.id} for MRM {self.mid_id}"


class InwardMaster(models.Model):
    """
    Represents tblInv_Inward_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    challan_no = models.CharField(db_column='ChallanNo', max_length=50, blank=True, null=True)
    challan_date = models.DateField(db_column='ChallanDate', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'
        verbose_name = 'Inward Master'
        verbose_name_plural = 'Inward Masters'

    def __str__(self):
        return self.challan_no or f"Inward {self.id}"


class InwardDetail(models.Model):
    """
    Represents tblInv_Inward_Details
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_id = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId', related_name='details')
    po_id = models.IntegerField(db_column='POId', blank=True, null=True) # FK to PoDetail.id
    received_qty = models.DecimalField(db_column='ReceivedQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'
        verbose_name = 'Inward Detail'
        verbose_name_plural = 'Inward Details'

    def __str__(self):
        return f"Inward Det {self.id} for Inward {self.gin_id_id}"


class PoMaster(models.Model):
    """
    Represents tblMM_PO_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    supplier_id_fk = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True) # Actual column name, but not necessarily FK
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1, blank=True, null=True) # '0' for PR, '1' for SPR

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

    def __str__(self):
        return self.po_no or f"PO {self.id}"

    @property
    def supplier_name(self):
        """Fetches supplier name based on supplier_id_fk."""
        try:
            return SupplierMaster.objects.get(supplier_id=self.supplier_id_fk, compid=self.compid).supplier_name
        except SupplierMaster.DoesNotExist:
            return "N/A"


class PoDetail(models.Model):
    """
    Represents tblMM_PO_Details
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(PoMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True) # Redundant with FK
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True) # Used if PRSPRFlag is '0'
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # FK to PrDetail.id
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True) # Used if PRSPRFlag is '1'
    spr_id = models.IntegerField(db_column='SPRId', blank=True, null=True) # FK to SprDetail.id
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Det {self.id} for {self.po_no}"


class PrMaster(models.Model):
    """
    Represents tblMM_PR_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no or f"PR {self.id}"


class PrDetail(models.Model):
    """
    Represents tblMM_PR_Details
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(PrMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True) # Redundant with FK
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster.id
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR Det {self.id} for {self.pr_no}"


class SprMaster(models.Model):
    """
    Represents tblMM_SPR_Master
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    compid = models.IntegerField(db_column='CompId')
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no or f"SPR {self.id}"


class SprDetail(models.Model):
    """
    Represents tblMM_SPR_Details
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(SprMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    spr_no = models.CharField(db_column='SPRNo', max_length=50, blank=True, null=True) # Redundant with FK
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster.id
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    dept_id = models.IntegerField(db_column='DeptId', blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"SPR Det {self.id} for {self.spr_no}"


# --- Primary Model for the Report Line Item ---

class MaterialQualityDetail(models.Model):
    """
    Represents tblQc_MaterialQuality_Details.
    This model acts as the primary entity for our report line items.
    All derived report fields are handled as properties/methods on this model (Fat Model).
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Foreign key to MaterialQualityMaster
    master = models.ForeignKey(MaterialQualityMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details', blank=True, null=True)
    accepted_qty = models.DecimalField(db_column='AcceptedQty', max_digits=18, decimal_places=3, default=0.000)
    rejected_qty = models.DecimalField(db_column='RejectedQty', max_digits=18, decimal_places=3, default=0.000)
    # Foreign key to RejectionReason
    rejection_reason_id = models.IntegerField(db_column='RejectionReason', blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=500, blank=True, null=True)
    # GRRId from tblQc_MaterialQuality_Master (which is tblinv_MaterialReceived_Master.Id)
    grr_id = models.IntegerField(db_column='GRRId', blank=True, null=True)
    # DGRRId from tblQc_MaterialQuality_Details (which is tblinv_MaterialReceived_Details.Id)
    dgrr_id = models.IntegerField(db_column='DGRRId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'
        verbose_name = 'Material Quality Detail'
        verbose_name_plural = 'Material Quality Details'

    def __str__(self):
        return f"MQD {self.id} (Rejected: {self.rejected_qty})"

    @property
    def item_info(self):
        """
        Aggregates ItemCode, Description, UOM.
        This mimics the complex JOINs and lookups in the ASP.NET code.
        """
        item_code = "N/A"
        description = "N/A"
        uom = "N/A"
        
        try:
            # Step 1: Find related MaterialReceivedDetail via dgrr_id
            mrd = MaterialReceivedDetail.objects.select_related('mid').get(id=self.dgrr_id)
            
            # Step 2: Find related InwardDetail (via MaterialReceivedMaster.GINId -> InwardMaster.Id -> InwardDetail.GINId)
            # This logic path is complex and might need specific ID mapping.
            # Simplified: Assuming mrd.poid can directly lead to po_details and then item info.
            po_detail = PoDetail.objects.select_related('mid').get(id=mrd.poid)
            po_master = po_detail.mid
            
            if po_master.pr_spr_flag == '0': # PR
                pr_detail = PrDetail.objects.select_related('mid').get(id=po_detail.pr_id)
                item_master = ItemMaster.objects.get(id=pr_detail.item_id)
            elif po_master.pr_spr_flag == '1': # SPR
                spr_detail = SprDetail.objects.select_related('mid').get(id=po_detail.spr_id)
                item_master = ItemMaster.objects.get(id=spr_detail.item_id)
            else:
                item_master = None

            if item_master:
                item_code = item_master.get_item_code_part_no(self.master.compid) if self.master else item_master.item_code
                description = item_master.manf_desc
                if item_master.uom_basic_id:
                    uom_obj = UnitMaster.objects.get(id=item_master.uom_basic_id)
                    uom = uom_obj.symbol
        except (MaterialReceivedDetail.DoesNotExist, PoDetail.DoesNotExist, PrDetail.DoesNotExist, 
                SprDetail.DoesNotExist, ItemMaster.DoesNotExist, UnitMaster.DoesNotExist) as e:
            # Log this error for debugging
            pass
        
        return {
            'item_code': item_code,
            'description': description,
            'uom': uom
        }

    @property
    def po_qty(self):
        """Mimics fetching POQty from tblMM_PO_Details."""
        try:
            mrd = MaterialReceivedDetail.objects.get(id=self.dgrr_id)
            po_detail = PoDetail.objects.get(id=mrd.poid)
            return po_detail.qty
        except (MaterialReceivedDetail.DoesNotExist, PoDetail.DoesNotExist):
            return 0.000

    @property
    def inv_qty(self):
        """Mimics fetching InvQty from tblInv_Inward_Details."""
        try:
            mrd = MaterialReceivedDetail.objects.get(id=self.dgrr_id)
            # Find InwardDetail linked to MaterialReceivedMaster's GINId and PoDetail's POId
            # This logic is very complex in C#, simplifying to direct lookup if possible.
            # Assuming a direct mapping might not be robust.
            # A more accurate mapping would involve:
            # 1. mrd.mid (MaterialReceivedMaster) -> .gin_id (InwardMaster ID)
            # 2. po_detail.id (PoDetail ID)
            # Then query InwardDetail with (GINId=mrd.mid.gin_id, POId=po_detail.id)
            
            # Simplified direct lookup, assuming POId in InwardDetail maps to the same POId found via MRD.
            # This is a simplification and would need careful mapping based on actual DB schema.
            inward_detail = InwardDetail.objects.get(po_id=mrd.poid, gin_id=mrd.mid.gin_id) # Needs the GINId from InwardMaster
            return inward_detail.received_qty
        except (MaterialReceivedDetail.DoesNotExist, InwardDetail.DoesNotExist):
            return 0.000

    @property
    def reced_qty(self):
        """Mimics fetching RecedQty from tblinv_MaterialReceived_Details."""
        try:
            mrd = MaterialReceivedDetail.objects.get(id=self.dgrr_id)
            return mrd.received_qty
        except MaterialReceivedDetail.DoesNotExist:
            return 0.000

    @property
    def rejection_reason_display(self):
        """Mimics fetching RejectionReason description."""
        try:
            reason = RejectionReason.objects.get(id=self.rejection_reason_id)
            return f"{reason.symbol}-{reason.description}"
        except RejectionReason.DoesNotExist:
            return "N/A"
            
    @property
    def challan_info(self):
        """Mimics fetching ChallanNo and ChallanDate."""
        challan_no = "N/A"
        challan_date = "N/A"
        try:
            mrd = MaterialReceivedDetail.objects.select_related('mid').get(id=self.dgrr_id)
            # mrd.mid is MaterialReceivedMaster
            # MaterialReceivedMaster.gin_id is InwardMaster.Id
            inward_master = InwardMaster.objects.get(id=mrd.mid.gin_id)
            challan_no = inward_master.challan_no
            challan_date = inward_master.challan_date.strftime('%d/%m/%Y') if inward_master.challan_date else "N/A"
        except (MaterialReceivedDetail.DoesNotExist, InwardMaster.DoesNotExist):
            pass
        return {'challan_no': challan_no, 'challan_date': challan_date}

    @property
    def supplier_name(self):
        """Mimics fetching SupplierName."""
        try:
            # Need to get SupplierId from PoMaster
            mrd = MaterialReceivedDetail.objects.get(id=self.dgrr_id)
            po_detail = PoDetail.objects.get(id=mrd.poid)
            po_master = PoMaster.objects.get(id=po_detail.mid_id)
            supplier = SupplierMaster.objects.get(supplier_id=po_master.supplier_id_fk, compid=po_master.compid)
            return supplier.supplier_name
        except (MaterialReceivedDetail.DoesNotExist, PoDetail.DoesNotExist, PoMaster.DoesNotExist, SupplierMaster.DoesNotExist):
            return "N/A"

```

#### 4.2 Forms

**Task:** Define a Django form for `MaterialQualityDetail` for potential CRUD operations.

**Instructions:**
A basic `ModelForm` is created for `MaterialQualityDetail`. Since the original page was read-only, this form serves as a placeholder for potential future editing capabilities via HTMX modals.

**File: `quality_control/forms.py`**
```python
from django import forms
from .models import MaterialQualityDetail, RejectionReason

class MaterialQualityDetailForm(forms.ModelForm):
    # Dynamically populate rejection_reason choices
    rejection_reason = forms.ModelChoiceField(
        queryset=RejectionReason.objects.all(),
        empty_label="Select Reason",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False
    )

    class Meta:
        model = MaterialQualityDetail
        fields = ['accepted_qty', 'rejected_qty', 'rejection_reason', 'remarks']
        widgets = {
            'accepted_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'rejected_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        labels = {
            'accepted_qty': 'Accepted Quantity',
            'rejected_qty': 'Rejected Quantity',
            'rejection_reason': 'Rejection Reason',
            'remarks': 'Remarks',
        }
    
    # Custom validation example (can be expanded based on ASP.NET logic)
    def clean(self):
        cleaned_data = super().clean()
        accepted_qty = cleaned_data.get('accepted_qty')
        rejected_qty = cleaned_data.get('rejected_qty')

        if accepted_qty is not None and accepted_qty < 0:
            self.add_error('accepted_qty', 'Accepted quantity cannot be negative.')
        
        if rejected_qty is not None and rejected_qty < 0:
            self.add_error('rejected_qty', 'Rejected quantity cannot be negative.')
            
        return cleaned_data
```

#### 4.3 Views

**Task:** Implement a report view and placeholder CRUD views using Django Class-Based Views (CBVs).

**Instructions:**
A `MaterialQualityDetailReportView` (inheriting from `ListView`) is the primary view for this page, designed to fetch and display the aggregated report data. Other CRUD views are provided for completeness but wouldn't be directly accessible from the "print details" page. Query string parameters (`Id`, `GRRNo`, `GINNo`, etc.) are captured to filter the report.

**File: `quality_control/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import MaterialQualityDetail, MaterialQualityMaster, SupplierMaster, CompanyMaster, InwardMaster
from .forms import MaterialQualityDetailForm

# --- Views for the Report (Main functionality) ---

class MaterialQualityDetailReportView(ListView):
    """
    Displays the Goods Rejection GRN Print Details report.
    This view retrieves and presents aggregated data, similar to the Crystal Report.
    It captures parameters from the URL to filter the report.
    """
    model = MaterialQualityDetail
    template_name = 'quality_control/materialqualitydetail/report_list.html'
    context_object_name = 'report_items' # Renamed for clarity in report context
    paginate_by = 10 # Example pagination, DataTables handles its own.

    def get_queryset(self):
        """
        Builds the complex queryset based on URL parameters, mimicking ASP.NET logic.
        Filters details based on master ID, and only shows rejected items.
        """
        comp_id = self.request.session.get('compid') # Company ID from session
        master_id = self.request.GET.get('Id') # Master ID from query string
        
        if not comp_id or not master_id:
            # Handle cases where required parameters are missing
            return MaterialQualityDetail.objects.none() # Return empty queryset

        queryset = MaterialQualityDetail.objects.filter(
            master__compid=comp_id, 
            master__id=master_id,
            rejected_qty__gt=0 # Only show rejected items, as per C# logic
        ).select_related('master') # Eager load master for efficiency

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        comp_id = self.request.session.get('compid')
        master_id = self.request.GET.get('Id')
        
        # Populate additional report parameters, mimicking Crystal Report parameters
        grr_no = self.request.GET.get('GRRNo', 'N/A')
        gin_no = self.request.GET.get('GINNo', 'N/A')
        po_no = self.request.GET.get('PONo', 'N/A')
        
        gqn_no = 'N/A'
        supplier_name = 'N/A'
        challan_no = 'N/A'
        challan_date = 'N/A'
        address = 'N/A'

        if master_id and comp_id:
            try:
                master_obj = MaterialQualityMaster.objects.get(id=master_id, compid=comp_id)
                gqn_no = master_obj.gqn_no
                
                # Try to get supplier name and challan info from the first detail item,
                # as they are usually consistent across the report for a given GRR.
                first_detail = self.get_queryset().first()
                if first_detail:
                    supplier_name = first_detail.supplier_name
                    challan_info = first_detail.challan_info
                    challan_no = challan_info['challan_no']
                    challan_date = challan_info['challan_date']

                address = CompanyMaster.get_company_address(comp_id)

            except MaterialQualityMaster.DoesNotExist:
                pass # Master not found, keep N/A values

        context['grr_no'] = grr_no
        context['gin_no'] = gin_no
        context['gqn_no'] = gqn_no
        context['po_no'] = po_no # This might need to be derived from details or passed if not available directly
        context['supplier_name'] = supplier_name
        context['challan_no'] = challan_no
        context['challan_date'] = challan_date
        context['company_address'] = address
        
        return context

# --- HTMX Partial View for the Report Table ---

class MaterialQualityDetailReportTablePartialView(MaterialQualityDetailReportView):
    """
    Returns only the table content for HTMX swaps.
    """
    template_name = 'quality_control/materialqualitydetail/_report_table.html'

# --- Standard CRUD Views (Provided for completeness as per template, not direct use by original ASPX) ---

class MaterialQualityDetailCreateView(CreateView):
    model = MaterialQualityDetail
    form_class = MaterialQualityDetailForm
    template_name = 'quality_control/materialqualitydetail/_materialqualitydetail_form.html'
    success_url = reverse_lazy('quality_control:materialqualitydetail_report') # Redirect to report view

    def form_valid(self, form):
        # Assign default master for demonstration, in a real app, this would be set from context/URL
        form.instance.master_id = self.request.GET.get('master_id', 1) 
        # Assign placeholder grr_id and dgrr_id for demonstration
        form.instance.grr_id = 1 
        form.instance.dgrr_id = 1
        response = super().form_valid(form)
        messages.success(self.request, 'Material Quality Detail added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialQualityDetailReport'
                }
            )
        return response

class MaterialQualityDetailUpdateView(UpdateView):
    model = MaterialQualityDetail
    form_class = MaterialQualityDetailForm
    template_name = 'quality_control/materialqualitydetail/_materialqualitydetail_form.html'
    context_object_name = 'materialqualitydetail'
    success_url = reverse_lazy('quality_control:materialqualitydetail_report') # Redirect to report view

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Material Quality Detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialQualityDetailReport'
                }
            )
        return response

class MaterialQualityDetailDeleteView(DeleteView):
    model = MaterialQualityDetail
    template_name = 'quality_control/materialqualitydetail/_materialqualitydetail_confirm_delete.html'
    context_object_name = 'materialqualitydetail'
    success_url = reverse_lazy('quality_control:materialqualitydetail_report') # Redirect to report view

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Quality Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshMaterialQualityDetailReport'
                }
            )
        return response

```

#### 4.4 Templates

**Task:** Create templates for the report view and partials for HTMX-driven modals.

**Instructions:**
The `report_list.html` will display the overall report structure. `_report_table.html` is an HTMX partial for the DataTables content. Form and delete confirmation templates are also provided as HTMX partials.

**File: `quality_control/templates/quality_control/materialqualitydetail/report_list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Goods Rejection GRN Print Details</h2>
        <a href="{% url 'home' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6 text-sm text-gray-700">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div><strong>Company Address:</strong> <span class="text-gray-900">{{ company_address }}</span></div>
            <div><strong>GRR No:</strong> <span class="text-gray-900">{{ grr_no }}</span></div>
            <div><strong>GIN No:</strong> <span class="text-gray-900">{{ gin_no }}</span></div>
            <div><strong>GQN No:</strong> <span class="text-gray-900">{{ gqn_no }}</span></div>
            <div><strong>PO No:</strong> <span class="text-gray-900">{{ po_no }}</span></div>
            <div><strong>Supplier Name:</strong> <span class="text-gray-900">{{ supplier_name }}</span></div>
            <div><strong>Challan No:</strong> <span class="text-gray-900">{{ challan_no }}</span></div>
            <div><strong>Challan Date:</strong> <span class="text-gray-900">{{ challan_date }}</span></div>
        </div>
    </div>

    <div id="reportTable-container"
         hx-trigger="load, refreshMaterialQualityDetailReport from:body"
         hx-get="{% url 'quality_control:materialqualitydetail_report_table' %}?{{ request.GET.urlencode }}" {# Pass all query params #}
         hx-swap="innerHTML"
         class="overflow-x-auto bg-white shadow-md rounded-lg p-4 max-h-[450px] overflow-y-auto" style="height: 450px;">
        <!-- Report DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
    
    <!-- Modal for form (Add/Edit) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
        // For example, if you have any client-side UI states or interactions.
    });
</script>
{% endblock %}

```

**File: `quality_control/templates/quality_control/materialqualitydetail/_report_table.html`**
```html
<table id="materialQualityDetailReportTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">PO Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Inv Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Recd Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Accepted Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rejected Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rejection Reason</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for item in report_items %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.item_info.item_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.item_info.description }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.item_info.uom }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.po_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.inv_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.reced_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.accepted_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-right">{{ item.rejected_qty|floatformat:"3" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.rejection_reason_display }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ item.remarks }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded text-xs mr-1"
                    hx-get="{% url 'quality_control:materialqualitydetail_edit' item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                    hx-get="{% url 'quality_control:materialqualitydetail_delete' item.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="12" class="py-4 px-4 text-center text-gray-500">No goods rejection details found for this report criteria.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
// Initialize DataTables only if the table is present
if ($.fn.DataTable.isDataTable('#materialQualityDetailReportTable')) {
    $('#materialQualityDetailReportTable').DataTable().destroy();
}
$(document).ready(function() {
    $('#materialQualityDetailReportTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "scrollX": true,
        "scrollCollapse": true,
        "fixedColumns": {
            leftColumns: 0,
            rightColumns: 1
        }
    });
});
</script>
```

**File: `quality_control/templates/quality_control/materialqualitydetail/_materialqualitydetail_form.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Material Quality Detail</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            </span>
        </div>
    </form>
</div>
```

**File: `quality_control/templates/quality_control/materialqualitydetail/_materialqualitydetail_confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete this Material Quality Detail (ID: {{ materialqualitydetail.pk }})?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
            </span>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the report view and its HTMX partial. Standard CRUD URLs are also included for completeness under a dedicated namespace.

**File: `quality_control/urls.py`**
```python
from django.urls import path
from .views import (
    MaterialQualityDetailReportView, 
    MaterialQualityDetailReportTablePartialView,
    MaterialQualityDetailCreateView, 
    MaterialQualityDetailUpdateView, 
    MaterialQualityDetailDeleteView
)

app_name = 'quality_control' # Namespace for this app's URLs

urlpatterns = [
    # Report View (main page for GoodsRejection_GRN_Print_Details.aspx equivalent)
    path('goods-rejection-report/', MaterialQualityDetailReportView.as_view(), name='materialqualitydetail_report'),
    path('goods-rejection-report/table/', MaterialQualityDetailReportTablePartialView.as_view(), name='materialqualitydetail_report_table'),

    # Standard CRUD operations for MaterialQualityDetail (for completeness, not direct from original ASPX page)
    path('material-quality-details/add/', MaterialQualityDetailCreateView.as_view(), name='materialqualitydetail_add'),
    path('material-quality-details/edit/<int:pk>/', MaterialQualityDetailUpdateView.as_view(), name='materialqualitydetail_edit'),
    path('material-quality-details/delete/<int:pk>/', MaterialQualityDetailDeleteView.as_view(), name='materialqualitydetail_delete'),
]
```

#### 4.6 Tests

**Task:** Write unit tests for the model and integration tests for views.

**Instructions:**
Comprehensive tests are provided for the `MaterialQualityDetail` model, covering its properties and methods that derive report data. Integration tests cover the report view and standard CRUD operations, including HTMX interactions.

**File: `quality_control/tests.py`**
```python       
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    MaterialQualityMaster, MaterialQualityDetail, ItemMaster, UnitMaster, RejectionReason,
    SupplierMaster, MaterialReceivedMaster, MaterialReceivedDetail, InwardMaster, InwardDetail,
    PoMaster, PoDetail, PrMaster, PrDetail, SprMaster, SprDetail, CompanyMaster
)
from decimal import Decimal

# --- Helper function for creating dummy data (mimicking the complex data relationships) ---
def create_dummy_data():
    comp = CompanyMaster.objects.create(id=1, company_name='Test Co', address='123 Test St')
    
    # Master records
    mq_master = MaterialQualityMaster.objects.create(id=1, compid=1, finyearid=2023, gqn_no='GQN001', grr_no='GRR001', sys_date='2023-01-01')
    inward_master = InwardMaster.objects.create(id=101, compid=1, challan_no='CHALLAN001', challan_date='2023-01-02')
    mr_master = MaterialReceivedMaster.objects.create(id=201, compid=1, gin_no='GIN001', gin_id=inward_master.id)
    po_master = PoMaster.objects.create(id=301, compid=1, po_no='PO001', finyearid=2023, supplier_id_fk='SUP001', pr_spr_flag='0')
    pr_master = PrMaster.objects.create(id=401, compid=1, pr_no='PR001')
    spr_master = SprMaster.objects.create(id=501, compid=1, spr_no='SPR001') # Not used in this specific PO chain, but for completeness

    # Lookup data
    item = ItemMaster.objects.create(id=601, compid=1, item_code='ITEM-A', manf_desc='Test Item A', uom_basic_id=701)
    unit = UnitMaster.objects.create(id=701, symbol='KGS')
    reason = RejectionReason.objects.create(id=801, symbol='DMG', description='Damaged Goods')
    supplier = SupplierMaster.objects.create(id=901, supplier_id='SUP001', supplier_name='Test Supplier Inc.', compid=1)

    # Detail records (related to master)
    mr_detail = MaterialReceivedDetail.objects.create(id=1001, mid=mr_master, received_qty=Decimal('100.000'), poid=1101)
    inward_detail = InwardDetail.objects.create(id=1201, gin_id=inward_master, po_id=1101, received_qty=Decimal('100.000'))
    po_detail = PoDetail.objects.create(id=1101, mid=po_master, po_no='PO001', pr_no='PR001', pr_id=1301, qty=Decimal('100.000'))
    pr_detail = PrDetail.objects.create(id=1301, mid=pr_master, pr_no='PR001', item_id=item.id)

    # The actual MaterialQualityDetail (the report line item)
    mq_detail = MaterialQualityDetail.objects.create(
        id=1,
        master=mq_master,
        accepted_qty=Decimal('50.000'),
        rejected_qty=Decimal('25.000'),
        rejection_reason_id=reason.id,
        remarks='Scratches on surface',
        grr_id=mr_master.id, # This seems to be MaterialReceivedMaster.Id based on C#
        dgrr_id=mr_detail.id # This is MaterialReceivedDetail.Id based on C#
    )

    return mq_detail, mq_master, comp, item, unit, reason, supplier, mr_master, mr_detail, inward_master, inward_detail, po_master, po_detail, pr_master, pr_detail

class MaterialQualityDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.mq_detail, cls.mq_master, cls.company, cls.item, cls.unit, cls.reason, cls.supplier, \
        cls.mr_master, cls.mr_detail, cls.inward_master, cls.inward_detail, cls.po_master, \
        cls.po_detail, cls.pr_master, cls.pr_detail = create_dummy_data()

    def test_material_quality_detail_creation(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        self.assertEqual(obj.accepted_qty, Decimal('50.000'))
        self.assertEqual(obj.rejected_qty, Decimal('25.000'))
        self.assertEqual(obj.rejection_reason_id, self.reason.id)
        self.assertEqual(obj.remarks, 'Scratches on surface')
        self.assertEqual(obj.master.gqn_no, 'GQN001') # Test master relationship
        self.assertEqual(obj.grr_id, self.mr_master.id)
        self.assertEqual(obj.dgrr_id, self.mr_detail.id)

    def test_item_info_property(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        item_info = obj.item_info
        self.assertEqual(item_info['item_code'], f"{self.item.item_code}-{self.item.manf_desc[:10]}")
        self.assertEqual(item_info['description'], 'Test Item A')
        self.assertEqual(item_info['uom'], 'KGS')

    def test_po_qty_property(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        self.assertEqual(obj.po_qty, Decimal('100.000'))

    def test_inv_qty_property(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        self.assertEqual(obj.inv_qty, Decimal('100.000'))

    def test_reced_qty_property(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        self.assertEqual(obj.reced_qty, Decimal('100.000'))

    def test_rejection_reason_display_property(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        self.assertEqual(obj.rejection_reason_display, 'DMG-Damaged Goods')

    def test_challan_info_property(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        challan_info = obj.challan_info
        self.assertEqual(challan_info['challan_no'], 'CHALLAN001')
        self.assertEqual(challan_info['challan_date'], '02/01/2023') # FromDateDMY format

    def test_supplier_name_property(self):
        obj = MaterialQualityDetail.objects.get(id=1)
        self.assertEqual(obj.supplier_name, 'Test Supplier Inc.')

    def test_invalid_relationships(self):
        # Test case where a related object does not exist
        # Temporarily delete a related object to test error handling
        temp_dgrr_id = self.mq_detail.dgrr_id
        MaterialReceivedDetail.objects.get(id=temp_dgrr_id).delete()
        self.mq_detail.refresh_from_db() # Reload to clear cached properties

        obj = MaterialQualityDetail.objects.get(id=1)
        self.assertEqual(obj.item_info['item_code'], 'N/A')
        self.assertEqual(obj.po_qty, 0.000)
        self.assertEqual(obj.reced_qty, 0.000)
        self.assertEqual(obj.inv_qty, 0.000) # This might still pass if it can derive from other paths

        # Restore for other tests
        create_dummy_data()

class MaterialQualityDetailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.mq_detail, cls.mq_master, cls.company, cls.item, cls.unit, cls.reason, cls.supplier, \
        cls.mr_master, cls.mr_detail, cls.inward_master, cls.inward_detail, cls.po_master, \
        cls.po_detail, cls.pr_master, cls.pr_detail = create_dummy_data()

    def setUp(self):
        self.client = Client()
        # Set session variables required by the view
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.mq_master.finyearid
        session['username'] = 'testuser'
        session.save()

    def test_report_view_get(self):
        # URL with required query parameters
        report_url = reverse('quality_control:materialqualitydetail_report')
        params = {
            'Id': self.mq_master.id, 
            'GRRNo': self.mq_master.grr_no,
            'GINNo': self.mr_master.gin_no,
            'PONo': self.po_master.po_no,
            'FyId': self.mq_master.finyearid, # Inferred from ASP.NET code
            'Key': 'some_key' # Inferred from ASP.NET code
        }
        response = self.client.get(report_url, params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialqualitydetail/report_list.html')
        self.assertTrue('report_items' in response.context)
        self.assertEqual(len(response.context['report_items']), 1)
        self.assertEqual(response.context['report_items'][0].id, self.mq_detail.id)
        self.assertEqual(response.context['grr_no'], self.mq_master.grr_no)
        self.assertEqual(response.context['company_address'], self.company.address)

    def test_report_view_no_params(self):
        response = self.client.get(reverse('quality_control:materialqualitydetail_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialqualitydetail/report_list.html')
        self.assertEqual(len(response.context['report_items']), 0) # Should return empty if no master_id

    def test_report_table_partial_view_get(self):
        # Test HTMX partial endpoint
        report_table_url = reverse('quality_control:materialqualitydetail_report_table')
        params = {'Id': self.mq_master.id}
        headers = {'HTTP_HX_REQUEST': 'true'} # Simulate HTMX request
        response = self.client.get(report_table_url, params, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialqualitydetail/_report_table.html')
        self.assertTrue('report_items' in response.context)
        self.assertEqual(len(response.context['report_items']), 1)
        self.assertContains(response, 'Test Item A')
        self.assertContains(response, 'Scratches on surface')

    # --- CRUD View Tests (for completeness, not direct use from original ASPX page) ---

    def test_create_view_get(self):
        response = self.client.get(reverse('quality_control:materialqualitydetail_add'), headers={'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialqualitydetail/_materialqualitydetail_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Material Quality Detail')

    def test_create_view_post(self):
        data = {
            'accepted_qty': '10.000',
            'rejected_qty': '5.000',
            'rejection_reason': self.reason.id, # Use existing reason ID
            'remarks': 'New rejected item',
            'master_id': self.mq_master.id # Passed as GET param in URL, used for form.instance.master_id
        }
        # Simulate HTMX POST
        response = self.client.post(reverse('quality_control:materialqualitydetail_add'), data, headers={'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertTrue(MaterialQualityDetail.objects.filter(remarks='New rejected item').exists())
        # Check if HX-Trigger header is present for list refresh
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialQualityDetailReport')

    def test_update_view_get(self):
        obj = MaterialQualityDetail.objects.get(id=self.mq_detail.id)
        response = self.client.get(reverse('quality_control:materialqualitydetail_edit', args=[obj.id]), headers={'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialqualitydetail/_materialqualitydetail_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Material Quality Detail')
        self.assertContains(response, 'Scratches on surface') # Check existing data

    def test_update_view_post(self):
        obj = MaterialQualityDetail.objects.get(id=self.mq_detail.id)
        data = {
            'accepted_qty': '55.000',
            'rejected_qty': '20.000',
            'rejection_reason': self.reason.id,
            'remarks': 'Updated remarks for item',
        }
        response = self.client.post(reverse('quality_control:materialqualitydetail_edit', args=[obj.id]), data, headers={'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 204) # HTMX No Content
        obj.refresh_from_db()
        self.assertEqual(obj.remarks, 'Updated remarks for item')
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self):
        obj = MaterialQualityDetail.objects.get(id=self.mq_detail.id)
        response = self.client.get(reverse('quality_control:materialqualitydetail_delete', args=[obj.id]), headers={'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'quality_control/materialqualitydetail/_materialqualitydetail_confirm_delete.html')
        self.assertTrue('materialqualitydetail' in response.context)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post(self):
        obj = MaterialQualityDetail.objects.get(id=self.mq_detail.id)
        response = self.client.post(reverse('quality_control:materialqualitydetail_delete', args=[obj.id]), headers={'HTTP_HX_REQUEST': 'true'})
        self.assertEqual(response.status_code, 204) # HTMX No Content
        self.assertFalse(MaterialQualityDetail.objects.filter(id=obj.id).exists())
        self.assertIn('HX-Trigger', response.headers)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
HTMX and Alpine.js are integral to the modernized Django application, ensuring a highly responsive user experience without full page reloads.

- **HTMX for Report Loading:**
    - The `report_list.html` uses `hx-get` to initially load the `_report_table.html` partial.
    - `hx-trigger="load, refreshMaterialQualityDetailReport from:body"` ensures the report table loads on page load and refreshes when a `refreshMaterialQualityDetailReport` custom event is triggered (e.g., after a CRUD operation via modals).
- **HTMX for Modals:**
    - "Edit" and "Delete" buttons in the `_report_table.html` use `hx-get` to fetch their respective forms (`_materialqualitydetail_form.html` or `_materialqualitydetail_confirm_delete.html`) into the `#modalContent` div.
    - The `_="on click add .is-active to #modal"` Alpine.js/Hyperscript directive shows the modal when the button is clicked.
    - Form submissions (`hx-post`) from the modal partials use `hx-swap="none"` and return an `HTTP 204 No Content` response with an `HX-Trigger` header (`refreshMaterialQualityDetailReport`) to tell the client to refresh the main report table.
- **Alpine.js for UI State:**
    - The `_="on click if event.target.id == 'modal' remove .is-active from me"` directive on the modal container (`#modal`) allows clicking outside the modal content to close it.
    - Alpine.js can be further used for client-side validation, toggling UI elements, or managing form states if more complex interactions are needed.
- **DataTables:**
    - The `_report_table.html` initializes a `DataTable` on the fetched table. This provides built-in client-side searching, sorting, and pagination capabilities.
    - Re-initialization logic is included to ensure DataTables functions correctly with HTMX reloads.
- **DRY Templates:**
    - The approach uses `core/base.html` for overall layout and partials for specific content (`_report_table.html`, `_materialqualitydetail_form.html`, `_materialqualitydetail_confirm_delete.html`), promoting reusability and maintainability.

### Final Notes

This comprehensive modernization plan provides a clear roadmap for transitioning your ASP.NET Goods Rejection GRN Print Details module to Django. By adhering to the 'Fat Model, Thin View' principle, utilizing HTMX and Alpine.js for dynamic interfaces, and focusing on automated testing, you will achieve:

-   **Enhanced Maintainability:** Business logic resides exclusively in models, making it easier to understand, test, and update.
-   **Improved Performance:** HTMX and DataTables ensure that user interactions are fast and fluid, reducing server load and eliminating full page reloads.
-   **Future-Proof Architecture:** Django's robust framework combined with modern frontend tools (HTMX, Alpine.js, Tailwind CSS) provides a solid foundation for future development and scalability.
-   **Reduced Technical Debt:** Systematic conversion reduces the manual effort of code rewriting, minimizing human error and accelerating the modernization process.

This approach transforms a static report viewer into a dynamic, interactive, and maintainable component, fully integrated into a modern Django ecosystem.