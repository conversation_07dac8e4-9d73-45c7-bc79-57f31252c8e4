The provided ASP.NET page is primarily a reporting and detail display page, not a direct CRUD interface. It aggregates complex data from multiple tables using nested SQL queries and presents it via a Crystal Report viewer.

For this modernization, we will transform this into a modern Django application (`qualitycontrol`) that displays the "Scrap Report Details" dynamically using HTMX, Alpine.js, and DataTables. While the original page is read-only, the prompt explicitly requires generating comprehensive CRUD operations for the inferred model. Therefore, we will identify `tblQC_Scrapregister` as the primary model (`ScrapRegister`) for standard CRUD operations, and then implement a dedicated view (`ScrapReportDetailView`) to replicate the complex report data aggregation and display, adhering to the "fat model, thin view" principle.

## ASP.NET to Django Conversion Script:

This document outlines the step-by-step conversion of your ASP.NET Scrap Material Report Details page to a modern Django-based solution, focusing on automated, repeatable processes and adherence to best practices.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

Based on the C# code, the application interacts with several database tables to compile the "Scrap Report." The primary table for this report's details is `tblQC_Scrapregister`. Other tables are joined to fetch related information like item details, unit of measurement, and department.

**Primary Model for CRUD:** `tblQC_Scrapregister`
*   `Id` (Primary Key, int)
*   `ScrapNo` (String, unique identifier for a scrap record)
*   `Qty` (Double, quantity of scrap)
*   `SysDate` (DateTime, system entry date)
*   `CompId` (Integer, Company ID)
*   `FinYearId` (Integer, Financial Year ID)
*   `MRQNId` (Integer, reference to Material Return Quality Note ID)

**Related Tables (Inferred for Report Data Aggregation):**
*   `tblQc_MaterialReturnQuality_Master`
*   `tblQc_MaterialReturnQuality_Details`
*   `tblInv_MaterialReturn_Master`
*   `tblInv_MaterialReturn_Details`
*   `tblDG_Item_Master`
*   `Unit_Master`
*   `tblHR_Departments`

For the purpose of this migration, we will define a Django model for `tblQC_Scrapregister` to handle standard CRUD operations. The complex data aggregation logic for the report will be encapsulated within a static method of this model, following the fat model principle.

## Step 2: Identify Backend Functionality

The ASP.NET `ScrapMaterial_Report_Details.aspx` page's core functionality is **Read (Report Generation and Display)**. It retrieves a detailed dataset based on query parameters (`ScrapNo`, `FYId`, `CompId`) and renders it using a Crystal Report. There are no direct Create, Update, or Delete operations on this specific page.

*   **Create:** Not implemented on this page.
*   **Read:** This is the primary function. Data is fetched from multiple tables, processed, and displayed.
*   **Update:** Not implemented on this page.
*   **Delete:** Not implemented on this page.

The `btnCancel_Click` event merely redirects the user to a different page (`ScrapMaterial_Report.aspx`), indicating navigation rather than a data manipulation.

## Step 3: Infer UI Components

The user interface of the ASP.NET page is minimalist, primarily serving as a container for the Crystal Report.

*   **`CrystalReportViewer1`**: This is the main component, responsible for rendering the report. In Django, this will be replaced by a dynamic HTML table powered by **DataTables**, offering client-side sorting, searching, and pagination.
*   **`Panel1`**: A container that provides scrollbars for the report viewer. This will be achieved using standard HTML `div` elements and Tailwind CSS for styling and responsiveness.
*   **`btnCancel`**: A button to navigate back. This will be converted into an HTML anchor tag or an HTMX-driven button, maintaining the navigation functionality.
*   **Styling**: The ASP.NET page uses inline styles and references `yui-datatable.css`. In Django, all styling will be managed by **Tailwind CSS**, and dynamic interactions will be handled by **HTMX** and **Alpine.js**.

The modernized Django application will provide a fluid, single-page application (SPA) feel for CRUD operations and report viewing without full page reloads.

## Step 4: Generate Django Code

We will create a Django application named `qualitycontrol`.

### 4.1 Models

The `ScrapRegister` model will represent the `tblQC_Scrapregister` table. We will include a static method `get_detailed_report_data` to encapsulate the complex report data retrieval logic, adhering to the "fat model" principle. For demonstration purposes, this method will return mocked data representing the aggregated report. In a real migration, each related table would become a Django model, and this method would use `select_related`/`prefetch_related` or custom SQL queries to perform the joins.

**`qualitycontrol/models.py`**

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

class ScrapRegister(models.Model):
    """
    Represents a scrap material registration entry (tblQC_Scrapregister).
    This model is used for CRUD operations and encapsulates report logic.
    """
    id = models.AutoField(db_column='Id', primary_key=True)
    scrap_no = models.CharField(db_column='ScrapNo', max_length=50, help_text="Unique identifier for the scrap register.")
    quantity = models.FloatField(db_column='Qty', help_text="Total quantity of scrap.")
    system_date = models.DateField(db_column='SysDate', default=timezone.now, help_text="Date when the scrap was registered.")
    company_id = models.IntegerField(db_column='CompId', help_text="Company ID associated with the scrap.")
    financial_year_id = models.IntegerField(db_column='FinYearId', help_text="Financial year ID.")
    mrqn_id = models.IntegerField(db_column='MRQNId', null=True, blank=True, help_text="Material Return Quality Note ID (FK to MaterialReturnQualityMaster.Id).")

    class Meta:
        managed = False  # Set to True if Django manages this table, False if it's an existing table
        db_table = 'tblQC_Scrapregister'
        verbose_name = 'Scrap Register'
        verbose_name_plural = 'Scrap Registers'
        unique_together = ('scrap_no', 'company_id', 'financial_year_id',) # Assuming unique per company/year

    def __str__(self):
        return f"Scrap No: {self.scrap_no} (Qty: {self.quantity})"

    # --- Business logic methods for the 'Fat Model' approach ---
    
    @staticmethod
    def get_company_address(company_id):
        """
        Mimics fun.CompAdd(CompId). In a real scenario, this would query a Company master table.
        For demonstration, returns a placeholder.
        """
        # In a real system, you would query your Company model here:
        # try:
        #     company = Company.objects.get(id=company_id)
        #     return company.address
        # except Company.DoesNotExist:
        #     return "Company Address Not Found"
        return f"ABC Textiles Ltd. (Company ID: {company_id}), 123 Industrial Area, City, Country"

    @staticmethod
    def get_item_code_part_no(company_id, item_id):
        """
        Mimics fun.GetItemCode_PartNo(CompId, ItemId).
        In a real scenario, this would query tblDG_Item_Master.
        """
        # In a real system, you would query your ItemMaster model here:
        # try:
        #     item = ItemMaster.objects.get(id=item_id, company_id=company_id)
        #     return f"{item.item_code} / {item.part_no}" # Assuming part_no exists
        # except ItemMaster.DoesNotExist:
        #     return "N/A"
        return f"ITEM-{item_id:04d}-PN" # Placeholder formatted like ITEM-0123-PN

    @classmethod
    def get_detailed_report_data(cls, scrap_no_param, company_id_param, financial_year_id_param):
        """
        Mimics the complex data retrieval logic from the ASP.NET code-behind's Page_Init.
        This method retrieves detailed scrap report data by performing a series of joins
        across multiple related tables. For this demonstration, we simulate data.

        In a real migration, this would involve:
        1. Defining Django models for all tables mentioned in the C# joins.
        2. Using Django ORM's `select_related` and `prefetch_related` for efficient joins.
        3. Aggregating data similar to how `dt` was constructed.
        """
        report_data = []
        
        # Fetch the main scrap entry (or entries if scrap_no is not unique for the report)
        scrap_entries = cls.objects.filter(
            financial_year_id=financial_year_id_param,
            company_id=company_id_param,
            scrap_no=scrap_no_param
        ).order_by('id')

        # Simulate the complex nested data fetching and aggregation.
        # This part requires significant domain knowledge of the original system's data.
        # For demonstration, we construct a list of dictionaries matching the 'dt' columns
        # using mock data and the available ScrapRegister fields.
        
        for index, entry in enumerate(scrap_entries):
            # Mocking complex join results based on the original DataTable columns:
            # Id, ItemCode, ManfDesc, UOMBasic, Symbol, WONo, AcceptedQty, RetQty, Remarks, Qty, ScrapNo, CompId, SysDate
            
            # These values would come from related models via ORM joins in a real scenario
            mock_item_id = entry.id + 1000 # Just a placeholder
            mock_dept_id = entry.id % 5 + 1 # Just a placeholder
            
            report_item = {
                'Id': entry.id,
                'ItemCode': cls.get_item_code_part_no(entry.company_id, mock_item_id),
                'ManfDesc': f"Component Description {entry.id}",
                'UOMBasic': "Units", # From Unit_Master.Symbol
                'Symbol': f"Dept {mock_dept_id}", # From tblHR_Departments.Symbol
                'WONo': f"WO-XYZ-{entry.id:03d}", # From tblInv_MaterialReturn_Details.WONo
                'AcceptedQty': round(entry.quantity * 0.8, 3), # From tblQc_MaterialReturnQuality_Details.AcceptedQty
                'RetQty': round(entry.quantity * 0.2, 3), # From tblInv_MaterialReturn_Details.RetQty
                'Remarks': f"Standard remarks for scrap {entry.scrap_no}.",
                'Qty': entry.quantity,
                'ScrapNo': entry.scrap_no,
                'CompId': entry.company_id,
                'SysDate': entry.system_date.strftime('%d/%m/%Y'), # Formatted as DMY
            }
            report_data.append(report_item)
            
        return report_data

    def some_model_business_logic(self):
        """Example of a model method encapsulating business logic."""
        return f"This scrap entry was recorded on {self.system_date.strftime('%Y-%m-%d')} for company {self.company_id}."

```

### 4.2 Forms

A Django `ModelForm` will be created for the `ScrapRegister` model, allowing for user input with appropriate widgets and validation.

**`qualitycontrol/forms.py`**

```python
from django import forms
from .models import ScrapRegister

class ScrapRegisterForm(forms.ModelForm):
    """
    Form for creating and updating ScrapRegister instances.
    Includes custom validation and Tailwind CSS styling via widgets.
    """
    class Meta:
        model = ScrapRegister
        fields = ['scrap_no', 'quantity', 'system_date', 'company_id', 'financial_year_id', 'mrqn_id']
        widgets = {
            'scrap_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'system_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mrqn_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'scrap_no': 'Scrap Number',
            'quantity': 'Quantity',
            'system_date': 'System Date',
            'company_id': 'Company ID',
            'financial_year_id': 'Financial Year ID',
            'mrqn_id': 'MRQN ID',
        }
        
    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Quantity must be greater than zero.")
        return quantity
    
    def clean(self):
        """
        Custom validation for uniqueness of scrap_no per company and financial year.
        """
        cleaned_data = super().clean()
        scrap_no = cleaned_data.get('scrap_no')
        company_id = cleaned_data.get('company_id')
        financial_year_id = cleaned_data.get('financial_year_id')
        
        if scrap_no and company_id and financial_year_id:
            query = ScrapRegister.objects.filter(
                scrap_no=scrap_no, 
                company_id=company_id, 
                financial_year_id=financial_year_id
            )
            if self.instance.pk: # If updating an existing instance
                query = query.exclude(pk=self.instance.pk)
            
            if query.exists():
                self.add_error('scrap_no', "A scrap register with this number already exists for this company and financial year.")
        
        return cleaned_data

```

### 4.3 Views

We will define standard Django Class-Based Views (CBVs) for `ScrapRegister` CRUD operations (`ListView`, `CreateView`, `UpdateView`, `DeleteView`). Additionally, a `ScrapReportDetailView` will be created to specifically handle the display of the detailed scrap report data, mimicking the original ASP.NET page's primary function. A `ScrapRegisterTablePartialView` will render the DataTables content for HTMX.

**`qualitycontrol/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from .models import ScrapRegister
from .forms import ScrapRegisterForm

class ScrapRegisterListView(ListView):
    """
    Displays a list of all ScrapRegister entries.
    This view is the main entry point for managing scrap registers.
    """
    model = ScrapRegister
    template_name = 'qualitycontrol/scrapregister/list.html'
    context_object_name = 'scrap_registers' # Renamed for clarity

    def get_queryset(self):
        """
        Return the queryset for the list view. 
        Ordered by system_date descending and then scrap_no.
        """
        return ScrapRegister.objects.all().order_by('-system_date', 'scrap_no')

class ScrapRegisterTablePartialView(ListView):
    """
    Renders only the table portion of the ScrapRegister list for HTMX requests.
    This allows for dynamic updates of the table without full page reloads.
    """
    model = ScrapRegister
    template_name = 'qualitycontrol/scrapregister/_scrapregister_table.html'
    context_object_name = 'scrap_registers'

    def get_queryset(self):
        """
        Return the queryset for the partial table view. 
        Ordered by system_date descending and then scrap_no.
        """
        return ScrapRegister.objects.all().order_by('-system_date', 'scrap_no')

class ScrapRegisterCreateView(CreateView):
    """
    Handles the creation of a new ScrapRegister entry.
    Supports HTMX for modal form submission.
    """
    model = ScrapRegister
    form_class = ScrapRegisterForm
    template_name = 'qualitycontrol/scrapregister/_scrapregister_form.html'
    success_url = reverse_lazy('scrapregister_list') # Redirects to list on full page load

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Scrap Register added successfully.')
        # HTMX-specific response: return 204 No Content and trigger refresh
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScrapRegisterList'
                }
            )
        return response # For non-HTMX requests

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add New Scrap Register'
        return context

class ScrapRegisterUpdateView(UpdateView):
    """
    Handles the updating of an existing ScrapRegister entry.
    Supports HTMX for modal form submission.
    """
    model = ScrapRegister
    form_class = ScrapRegisterForm
    template_name = 'qualitycontrol/scrapregister/_scrapregister_form.html'
    success_url = reverse_lazy('scrapregister_list') # Redirects to list on full page load

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Scrap Register updated successfully.')
        # HTMX-specific response
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScrapRegisterList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Scrap Register'
        return context

class ScrapRegisterDeleteView(DeleteView):
    """
    Handles the deletion of a ScrapRegister entry.
    Supports HTMX for modal confirmation and deletion.
    """
    model = ScrapRegister
    template_name = 'qualitycontrol/scrapregister/confirm_delete.html'
    success_url = reverse_lazy('scrapregister_list') # Redirects to list on full page load

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Scrap Register deleted successfully.')
        # HTMX-specific response
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshScrapRegisterList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Confirm Delete'
        return context

class ScrapReportDetailView(TemplateView):
    """
    Replicates the ASP.NET ScrapMaterial_Report_Details.aspx functionality.
    Fetches complex report data using the ScrapRegister model's static method
    and displays it in a DataTables-powered HTML table.
    """
    template_name = 'qualitycontrol/scrapregister/report_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters from query string, mimicking ASP.NET Request.QueryString
        scrap_no = self.request.GET.get('ScrapNo', '')
        # Default company_id and financial_year_id, or retrieve from session as in ASP.NET
        # For demonstration, use default or mock session values
        company_id = int(self.request.GET.get('CompId', self.request.session.get('compid', 1))) 
        financial_year_id = int(self.request.GET.get('FYId', self.request.session.get('finyear', 2023)))

        # Validate required parameters
        if not scrap_no:
            messages.error(self.request, "Scrap Number is required to view the report details.")
            context['report_items'] = []
            context['report_error'] = "Scrap Number is missing."
            return context

        # Use the fat model method to get the aggregated report data
        report_items = ScrapRegister.get_detailed_report_data(
            scrap_no_param=scrap_no,
            company_id_param=company_id,
            financial_year_id_param=financial_year_id
        )

        context['report_items'] = report_items
        context['scrap_no'] = scrap_no
        context['company_address'] = ScrapRegister.get_company_address(company_id)
        
        return context

```

### 4.4 Templates

Templates will follow the DRY principle, using inheritance and partials for HTMX.

**`qualitycontrol/scrapregister/list.html`**
(Main page for listing and triggering CRUD modals)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Scrap Registers</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'scrapregister_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal and remove .hidden from #modal">
            Add New Scrap Register
        </button>
    </div>
    
    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="p-3 rounded-md {{ message.tags|yesno:'bg-green-100 text-green-700,bg-red-100 text-red-700' }} text-sm" role="alert">
                {{ message }}
            </div>
            {% endfor %}
        </div>
    {% endif %}

    <div id="scrapregister-table-container"
         hx-trigger="load, refreshScrapRegisterList from:body"
         hx-get="{% url 'scrapregister_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading scrap registers...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-600 bg-opacity-75 hidden items-center justify-center z-50"
         _="on click if event.target.id == 'modal' remove .flex from me and add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 transform transition-all duration-300 scale-95 opacity-0"
             _="on load add .scale-100 .opacity-100 to me then remove .scale-95 .opacity-0 from me
                on htmx:afterOnLoad remove .flex from #modal and add .hidden to #modal end">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js component for modal handling
    document.addEventListener('alpine:init', () => {
        Alpine.data('modal', () => ({
            open: false,
            show() { this.open = true },
            hide() { this.open = false },
        }))
    });
</script>
{% endblock %}
```

**`qualitycontrol/scrapregister/_scrapregister_table.html`**
(Partial for DataTables, loaded by HTMX)

```html
<div class="bg-white shadow-lg rounded-lg overflow-hidden">
    <table id="scrapRegisterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scrap No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">FY ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MRQN ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in scrap_registers %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.scrap_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.quantity }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.system_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.company_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.financial_year_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ obj.mrqn_id|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm mr-2 transition duration-300 ease-in-out"
                        hx-get="{% url 'scrapregister_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal and remove .hidden from #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'scrapregister_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal and remove .hidden from #modal">
                        Delete
                    </button>
                    <a href="{% url 'scrap_report_detail' %}?ScrapNo={{ obj.scrap_no }}&CompId={{ obj.company_id }}&FYId={{ obj.financial_year_id }}"
                       class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded-lg shadow-sm ml-2 transition duration-300 ease-in-out">
                       View Report
                    </a>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 px-4 text-center text-gray-500">No scrap registers found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables on the loaded table
    $(document).ready(function() {
        $('#scrapRegisterTable').DataTable({
            "pageLength": 10, // Default entries per page
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]], // Options for entries per page
            "ordering": true,    // Enable sorting
            "searching": true,   // Enable search box
            "paging": true,      // Enable pagination
            "info": true         // Show "Showing X of Y entries"
        });
    });
</script>
```

**`qualitycontrol/scrapregister/_scrapregister_form.html`**
(Partial for CRUD forms, loaded by HTMX into modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}

        {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal and add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

**`qualitycontrol/scrapregister/confirm_delete.html`**
(Partial for delete confirmation, loaded by HTMX into modal)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">{{ title }}</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the scrap register with ID: <span class="font-bold">{{ object.pk }}</span> (Scrap No: <span class="font-bold">{{ object.scrap_no }}</span>)?</p>
    
    <form hx-post="{% url 'scrapregister_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal and add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`qualitycontrol/scrapregister/report_detail.html`**
(Main page for the specific report view)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Scrap Material Report Details (Scrap No: {{ scrap_no }})</h2>
        <a href="{% url 'scrapregister_list' %}" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
            &larr; Back to Scrap Registers
        </a>
    </div>

    {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="p-3 rounded-md bg-red-100 text-red-700 text-sm" role="alert">
                {{ message }}
            </div>
            {% endfor %}
        </div>
    {% endif %}

    {% if report_items %}
        <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
            <p class="text-center text-lg font-semibold text-gray-700 mb-4">{{ company_address }}</p>
            <h3 class="text-xl font-bold text-center text-gray-800 mb-6">Detailed Scrap Report for Scrap No: {{ scrap_no }}</h3>
            
            <div class="overflow-x-auto">
                <table id="scrapReportDetailTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf. Desc</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM Basic</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Dept/Symbol</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Accepted Qty</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Returned Qty</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Remarks</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scrap Qty</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scrap No</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
                            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">System Date</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in report_items %}
                        <tr class="hover:bg-gray-50">
                            <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.ItemCode }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.ManfDesc }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.UOMBasic }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.Symbol }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.WONo }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.AcceptedQty }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.RetQty }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.Remarks }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.Qty }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.ScrapNo }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.CompId }}</td>
                            <td class="py-3 px-4 whitespace-nowrap">{{ item.SysDate }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <div class="bg-white shadow-lg rounded-lg p-6 text-center text-gray-600">
            <p class="text-xl">No detailed scrap report data found for Scrap Number: <span class="font-bold">{{ scrap_no }}</span>.</p>
            {% if report_error %}
                <p class="text-red-500 mt-2">{{ report_error }}</p>
            {% endif %}
            <p class="mt-4">Please ensure the correct Scrap Number, Company ID, and Financial Year ID are provided.</p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize DataTables on the report detail table
    $(document).ready(function() {
        $('#scrapReportDetailTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "ordering": true,
            "searching": true,
            "paging": true,
            "info": true
        });
    });
</script>
{% endblock %}
```

### 4.5 URLs

URL patterns for all views will be defined in `qualitycontrol/urls.py`.

**`qualitycontrol/urls.py`**

```python
from django.urls import path
from .views import (
    ScrapRegisterListView, ScrapRegisterCreateView, ScrapRegisterUpdateView, 
    ScrapRegisterDeleteView, ScrapRegisterTablePartialView, ScrapReportDetailView
)

urlpatterns = [
    # CRUD operations for ScrapRegister
    path('scrapregister/', ScrapRegisterListView.as_view(), name='scrapregister_list'),
    path('scrapregister/table/', ScrapRegisterTablePartialView.as_view(), name='scrapregister_table'),
    path('scrapregister/add/', ScrapRegisterCreateView.as_view(), name='scrapregister_add'),
    path('scrapregister/edit/<int:pk>/', ScrapRegisterUpdateView.as_view(), name='scrapregister_edit'),
    path('scrapregister/delete/<int:pk>/', ScrapRegisterDeleteView.as_view(), name='scrapregister_delete'),
    
    # Specific report detail view
    path('scrapreport/detail/', ScrapReportDetailView.as_view(), name='scrap_report_detail'),
]

```

### 4.6 Tests

Comprehensive tests will cover model methods and view functionality, ensuring at least 80% test coverage.

**`qualitycontrol/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import ScrapRegister
from datetime import date

class ScrapRegisterModelTest(TestCase):
    """
    Unit tests for the ScrapRegister model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.scrap_register1 = ScrapRegister.objects.create(
            scrap_no='SCR001',
            quantity=100.50,
            system_date=date(2023, 1, 15),
            company_id=1,
            financial_year_id=2023,
            mrqn_id=101
        )
        cls.scrap_register2 = ScrapRegister.objects.create(
            scrap_no='SCR002',
            quantity=50.25,
            system_date=date(2023, 2, 20),
            company_id=1,
            financial_year_id=2023,
            mrqn_id=102
        )
        cls.scrap_register3 = ScrapRegister.objects.create(
            scrap_no='SCR001', # Same scrap_no, different company/year
            quantity=75.00,
            system_date=date(2024, 3, 10),
            company_id=2,
            financial_year_id=2024,
            mrqn_id=103
        )

    def test_scrap_register_creation(self):
        """Test that a ScrapRegister object is created correctly."""
        self.assertEqual(self.scrap_register1.scrap_no, 'SCR001')
        self.assertEqual(self.scrap_register1.quantity, 100.50)
        self.assertEqual(self.scrap_register1.company_id, 1)
        self.assertTrue(isinstance(self.scrap_register1.system_date, date))

    def test_verbose_name_plural(self):
        """Test verbose_name_plural for the model."""
        self.assertEqual(str(ScrapRegister._meta.verbose_name_plural), 'Scrap Registers')

    def test_str_method(self):
        """Test the __str__ method of the model."""
        expected_str = "Scrap No: SCR001 (Qty: 100.5)"
        self.assertEqual(str(self.scrap_register1), expected_str)

    def test_get_company_address_method(self):
        """Test the static method for getting company address."""
        address = ScrapRegister.get_company_address(1)
        self.assertIn("ABC Textiles Ltd. (Company ID: 1)", address)
        self.assertIsInstance(address, str)

    def test_get_item_code_part_no_method(self):
        """Test the static method for getting item code/part number."""
        item_code = ScrapRegister.get_item_code_part_no(1, 1000)
        self.assertIn("ITEM-1000-PN", item_code)
        self.assertIsInstance(item_code, str)
        
    def test_get_detailed_report_data(self):
        """Test the static method for retrieving detailed report data."""
        report_data = ScrapRegister.get_detailed_report_data('SCR001', 1, 2023)
        self.assertIsInstance(report_data, list)
        self.assertEqual(len(report_data), 1) # Only SCR001 for company 1, FY 2023
        self.assertEqual(report_data[0]['ScrapNo'], 'SCR001')
        self.assertEqual(report_data[0]['Qty'], 100.50)
        self.assertIn('ItemCode', report_data[0])
        self.assertIn('ManfDesc', report_data[0])
        self.assertIn('SysDate', report_data[0])
        
        # Test with a different scrap_no, company_id, financial_year_id
        report_data_2 = ScrapRegister.get_detailed_report_data('SCR001', 2, 2024)
        self.assertEqual(len(report_data_2), 1)
        self.assertEqual(report_data_2[0]['ScrapNo'], 'SCR001')
        self.assertEqual(report_data_2[0]['CompanyID'], 2)
        
        # Test for non-existent report
        report_data_empty = ScrapRegister.get_detailed_report_data('NONEXISTENT', 1, 2023)
        self.assertEqual(len(report_data_empty), 0)


class ScrapRegisterViewsTest(TestCase):
    """
    Integration tests for ScrapRegister views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.scrap_register = ScrapRegister.objects.create(
            scrap_no='VIEWTEST1',
            quantity=123.45,
            system_date=date(2023, 7, 1),
            company_id=1,
            financial_year_id=2023,
            mrqn_id=200
        )
        cls.scrap_register2 = ScrapRegister.objects.create(
            scrap_no='VIEWTEST2',
            quantity=67.89,
            system_date=date(2023, 8, 1),
            company_id=1,
            financial_year_id=2023,
            mrqn_id=201
        )

    def setUp(self):
        # Setup for each test method
        self.client = Client()

    def test_list_view_get(self):
        """Test the ScrapRegister list view (GET request)."""
        response = self.client.get(reverse('scrapregister_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/list.html')
        self.assertIn('scrap_registers', response.context)
        self.assertEqual(list(response.context['scrap_registers']), [self.scrap_register2, self.scrap_register]) # Ordered by date desc

    def test_table_partial_view_get(self):
        """Test the ScrapRegister table partial view for HTMX (GET request)."""
        response = self.client.get(reverse('scrapregister_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/_scrapregister_table.html')
        self.assertIn('scrap_registers', response.context)
        self.assertEqual(list(response.context['scrap_registers']), [self.scrap_register2, self.scrap_register])

    def test_create_view_get(self):
        """Test the ScrapRegister create view (GET request)."""
        response = self.client.get(reverse('scrapregister_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/_scrapregister_form.html')
        self.assertIn('form', response.context)
        self.assertIn('title', response.context)
        self.assertEqual(response.context['title'], 'Add New Scrap Register')

    def test_create_view_post_success(self):
        """Test successful creation of a ScrapRegister via POST."""
        data = {
            'scrap_no': 'NEW001',
            'quantity': 99.99,
            'system_date': '2024-01-01',
            'company_id': 1,
            'financial_year_id': 2024,
            'mrqn_id': 300
        }
        response = self.client.post(reverse('scrapregister_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on full page load
        self.assertTrue(ScrapRegister.objects.filter(scrap_no='NEW001').exists())
        self.assertEqual(ScrapRegister.objects.filter(scrap_no='NEW001').first().quantity, 99.99)
        
        # Test HTMX success response (204 No Content)
        response_htmx = self.client.post(reverse('scrapregister_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response_htmx.status_code, 204)
        self.assertIn('HX-Trigger', response_htmx.headers)
        self.assertIn('refreshScrapRegisterList', response_htmx.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        """Test invalid form submission for ScrapRegister creation."""
        data = {
            'scrap_no': 'SCR001', # Duplicate
            'quantity': -5,      # Invalid
            'system_date': '2023-01-01',
            'company_id': 1,
            'financial_year_id': 2023,
            'mrqn_id': 101
        }
        response = self.client.post(reverse('scrapregister_add'), data)
        self.assertEqual(response.status_code, 200) # Should render form again
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/_scrapregister_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertIn('quantity', response.context['form'].errors)
        self.assertIn('scrap_no', response.context['form'].errors)

    def test_update_view_get(self):
        """Test the ScrapRegister update view (GET request)."""
        response = self.client.get(reverse('scrapregister_edit', args=[self.scrap_register.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/_scrapregister_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.scrap_register)
        self.assertEqual(response.context['title'], 'Edit Scrap Register')

    def test_update_view_post_success(self):
        """Test successful update of a ScrapRegister via POST."""
        data = {
            'scrap_no': 'VIEWTEST1_UPDATED',
            'quantity': 150.00,
            'system_date': '2023-07-01',
            'company_id': 1,
            'financial_year_id': 2023,
            'mrqn_id': 200
        }
        response = self.client.post(reverse('scrapregister_edit', args=[self.scrap_register.pk]), data)
        self.assertEqual(response.status_code, 302) # Redirect on full page load
        self.scrap_register.refresh_from_db()
        self.assertEqual(self.scrap_register.scrap_no, 'VIEWTEST1_UPDATED')
        self.assertEqual(self.scrap_register.quantity, 150.00)

        # Test HTMX success response (204 No Content)
        response_htmx = self.client.post(reverse('scrapregister_edit', args=[self.scrap_register2.pk]), {
            'scrap_no': 'VIEWTEST2_HTMX',
            'quantity': 70.00,
            'system_date': '2023-08-01',
            'company_id': 1,
            'financial_year_id': 2023,
            'mrqn_id': 201
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response_htmx.status_code, 204)
        self.assertIn('HX-Trigger', response_htmx.headers)
        self.assertIn('refreshScrapRegisterList', response_htmx.headers['HX-Trigger'])
        self.scrap_register2.refresh_from_db()
        self.assertEqual(self.scrap_register2.scrap_no, 'VIEWTEST2_HTMX')

    def test_delete_view_get(self):
        """Test the ScrapRegister delete confirmation view (GET request)."""
        response = self.client.get(reverse('scrapregister_delete', args=[self.scrap_register.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.scrap_register)
        self.assertEqual(response.context['title'], 'Confirm Delete')

    def test_delete_view_post_success(self):
        """Test successful deletion of a ScrapRegister via POST."""
        initial_count = ScrapRegister.objects.count()
        response = self.client.post(reverse('scrapregister_delete', args=[self.scrap_register.pk]))
        self.assertEqual(response.status_code, 302) # Redirect on full page load
        self.assertEqual(ScrapRegister.objects.count(), initial_count - 1)
        self.assertFalse(ScrapRegister.objects.filter(pk=self.scrap_register.pk).exists())
        
        # Test HTMX success response (204 No Content)
        initial_count_htmx = ScrapRegister.objects.count()
        response_htmx = self.client.post(reverse('scrapregister_delete', args=[self.scrap_register2.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response_htmx.status_code, 204)
        self.assertIn('HX-Trigger', response_htmx.headers)
        self.assertIn('refreshScrapRegisterList', response_htmx.headers['HX-Trigger'])
        self.assertEqual(ScrapRegister.objects.count(), initial_count_htmx - 1)
        self.assertFalse(ScrapRegister.objects.filter(pk=self.scrap_register2.pk).exists())

    def test_scrap_report_detail_view(self):
        """Test the ScrapReportDetailView."""
        # Create a specific scrap register for report testing
        report_scrap = ScrapRegister.objects.create(
            scrap_no='REPORT001',
            quantity=200.0,
            system_date=date(2023, 10, 5),
            company_id=1,
            financial_year_id=2023,
            mrqn_id=400
        )
        
        url = reverse('scrap_report_detail') + '?ScrapNo=REPORT001&CompId=1&FYId=2023'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/report_detail.html')
        self.assertIn('report_items', response.context)
        self.assertIn('scrap_no', response.context)
        self.assertIn('company_address', response.context)
        
        # Verify content of report_items (based on mocked data)
        self.assertGreater(len(response.context['report_items']), 0)
        self.assertEqual(response.context['report_items'][0]['ScrapNo'], 'REPORT001')
        self.assertEqual(response.context['report_items'][0]['Qty'], 200.0)

    def test_scrap_report_detail_view_missing_params(self):
        """Test ScrapReportDetailView with missing parameters."""
        url = reverse('scrap_report_detail') # Missing ScrapNo
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'qualitycontrol/scrapregister/report_detail.html')
        self.assertIn('report_error', response.context)
        self.assertContains(response, "Scrap Number is required to view the report details.")
        self.assertEqual(len(response.context['report_items']), 0)
```

## Step 5: HTMX and Alpine.js Integration

The integration strategy focuses on creating a highly interactive user experience without full page reloads:

*   **HTMX for dynamic updates:**
    *   The `scrapregister_list` page loads the initial table content via `hx-get="{% url 'scrapregister_table' %}"`.
    *   After any CRUD operation (add, edit, delete), the views return an `HttpResponse` with `status=204` (No Content) and an `HX-Trigger` header (`refreshScrapRegisterList`). This signal is caught by the `hx-trigger="load, refreshScrapRegisterList from:body"` on the table container, automatically reloading only the table.
    *   Add/Edit/Delete buttons use `hx-get` to fetch partial templates (`_scrapregister_form.html`, `confirm_delete.html`) into a modal (`#modalContent`).
    *   Form submissions within the modal use `hx-post` to the same URL, which then triggers the table refresh.
*   **Alpine.js for UI state management:**
    *   Alpine.js (used with `_`) is used to control the visibility of the modal (`on click add .flex to #modal and remove .hidden from #modal` to show, `on click if event.target.id == 'modal' remove .flex from me and add .hidden to me` to hide on backdrop click, and `on htmx:afterOnLoad remove .flex from #modal and add .hidden to #modal end` for modal auto-close after HTMX response).
*   **DataTables for list views:**
    *   The `_scrapregister_table.html` and `report_detail.html` partials contain a standard HTML `<table>` element.
    *   A `<script>` block within these partials initializes DataTables on the table ID (`$('#scrapRegisterTable').DataTable()`, `$('#scrapReportDetailTable').DataTable()`) once the content is loaded into the DOM by HTMX. This provides immediate client-side search, sort, and pagination capabilities.
    *   The necessary DataTables CDN links (CSS and JS) are assumed to be present in `core/base.html`.

This setup ensures that all primary interactions are fast, responsive, and avoid unnecessary full page reloads, aligning with modern web application patterns.

## Final Notes

*   **Placeholders:** `[APP_NAME]` is `qualitycontrol`, `[MODEL_NAME]` is `ScrapRegister`, `[MODEL_NAME_LOWER]` is `scrapregister`, `[MODEL_NAME_PLURAL]` is `Scrap Registers`, `[MODEL_NAME_PLURAL_LOWER]` is `scrap_registers`. `[TABLE_NAME]` is `tblQC_Scrapregister`. `[FIELD_TYPE]`s and `[COLUMN]`s are derived from the analysis.
*   **Database Integration:** The `managed = False` in `models.Meta` indicates that Django will not manage table creation/alteration, assuming the database tables already exist from the ASP.NET application.
*   **Error Handling:** Basic error handling is included in forms and views. More robust logging and user feedback mechanisms would be implemented in a full production system.
*   **Security:** This plan assumes standard Django security features (CSRF protection is included in forms). Authentication and authorization would be built upon Django's built-in user system.
*   **Scalability:** The fat model approach centralizes business logic, making the application easier to maintain and scale. HTMX and DataTables offload significant rendering and interaction logic to the client, improving server performance.
*   **Reporting:** The Crystal Report viewer is replaced by a dynamic HTML table powered by DataTables. For complex, print-ready reports, a separate PDF generation library (e.g., ReportLab, WeasyPrint) could be integrated.