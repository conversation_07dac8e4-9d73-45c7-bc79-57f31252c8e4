## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with three primary database tables, likely representing master data for Material Credit Notes, Work Orders, and Customers.

*   **`tblPM_MaterialCreditNote_Master` (for Material Credit Notes)**
    *   **Columns:**
        *   `Id` (Primary Key, Integer)
        *   `MCNNo` (String/NVARCHAR)
        *   `SysDate` (DateTime, used for `MCNDate`)
        *   `WONo` (String/NVARCHAR, links to `SD_Cust_WorkOrder_Master`)
        *   `FinYearId` (Integer)
        *   `CompId` (Integer)

*   **`SD_Cust_WorkOrder_Master` (for Work Orders)**
    *   **Columns:**
        *   `Id` (Primary Key, Integer)
        *   `WONo` (String/NVARCHAR)
        *   `TaskProjectTitle` (String/NVARCHAR)
        *   `CustomerId` (Integer, links to `SD_Cust_Master`)
        *   `CompId` (Integer)
        *   `FinYearId` (Integer)

*   **`SD_Cust_Master` (for Customers)**
    *   **Columns:**
        *   `CustomerId` (Primary Key, Integer)
        *   `CustomerName` (String/NVARCHAR)
        *   `CompId` (Integer)

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data retrieval in the ASP.NET code.

**Instructions:**
The ASP.NET page is a display-only screen, primarily for viewing details related to a Work Order and a list of associated Material Credit Notes. There are no direct Create, Update, or Delete operations on the Material Credit Notes themselves from this page.

*   **Read Operation (Data Retrieval):**
    *   Retrieves Work Order details (`WO No`, `Project Name`) based on `WOId` and `WONo` passed in the URL, filtered by `CompId` and `FinYearId` from the user's session.
    *   Retrieves Customer details (`Customer Name`, `Customer ID`) based on `CustomerId` obtained from the Work Order details, filtered by `CompId`.
    *   Retrieves a list of Material Credit Notes (`MCN No`, `Date`, `Id`) associated with the specific `WONo`, filtered by `CompId` and `FinYearId`. This data populates a data grid.

*   **Action (Navigation/Redirection):**
    *   **"Print" Action:** When a "Print" link button is clicked for a specific Material Credit Note, the page redirects to `MaterialCreditNote_MCN_Print_Report.aspx`, passing the Material Credit Note `Id`, `WONo`, `WOId`, and other system parameters. This is effectively a trigger to view a report for that specific MCN.
    *   **"Cancel" Action:** A "Cancel" button redirects the user back to a previous Material Credit Note list page (`MaterialCreditNote_MCN_Print.aspx`).

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to modern Django equivalents.

**Instructions:**
The ASP.NET page uses standard Web Forms controls for display and interaction.

*   **Display Components:**
    *   `asp:Label` controls (`lblWono`, `lblProjectTitle`, `lblCustName`): These will be replaced by direct Django template variable rendering (e.g., `{{ work_order.wo_no }}`).
    *   `asp:GridView` (`GridView1`): This is the main data display. It will be replaced by a modern HTML `<table>` element integrated with **DataTables.js** for client-side features (sorting, searching, pagination). The data will be loaded dynamically via **HTMX**.

*   **Action Components:**
    *   `asp:LinkButton` (for "Print"): This will be replaced by a standard HTML `<a>` tag or `<button>` element with appropriate `hx-get` attributes if the report generation also moves to HTMX, or a standard `target="_blank"` link if it's a new tab/window. Given the ASP.NET `Response.Redirect`, a direct link is more appropriate, potentially opening in a new tab for reports.
    *   `asp:Button` (`btnCancel`): This will be replaced by a standard HTML `<a>` tag or `<button>` element that redirects to the previous list page.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `project_management`, to house the migrated functionality.

#### 4.1 Models (`project_management/models.py`)

We map the identified database tables to Django models. Since we're migrating from an existing database, `managed = False` is crucial. We also add `verbose_name` for better readability in the Django admin and `__str__` methods for object representation. `@property` methods are added to models for business logic like date formatting, adhering to the "Fat Model" principle.

```python
from django.db import models
from django.urls import reverse

# In a production environment, CompId and FinYearId would typically come from the authenticated user's profile
# or a global context manager. For this example, we'll assume they are passed or configured.

class Customer(models.Model):
    # Maps to SD_Cust_Master
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255) # Assuming max_length
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return f"{self.customer_name} [{self.customer_id}]"

class WorkOrder(models.Model):
    # Maps to SD_Cust_WorkOrder_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=50) # Assuming max_length
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255) # Assuming max_length
    customer_id = models.IntegerField(db_column='CustomerId') # Links to Customer model
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return f"{self.wo_no} - {self.task_project_title}"

    def get_customer_details(self, comp_id):
        """Retrieves customer details associated with this work order."""
        try:
            return Customer.objects.get(customer_id=self.customer_id, comp_id=comp_id)
        except Customer.DoesNotExist:
            return None

    def get_material_credit_notes(self, comp_id, fin_year_id):
        """Retrieves material credit notes for this work order based on WONo, CompId, and FinYearId."""
        return MaterialCreditNote.objects.filter(
            wo_no=self.wo_no,
            fin_year_id=fin_year_id,
            comp_id=comp_id
        ).order_by('id') # Order by ID for consistent SN generation

class MaterialCreditNote(models.Model):
    # Maps to tblPM_MaterialCreditNote_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    mcn_no = models.CharField(db_column='MCNNo', max_length=50) # Assuming max_length
    sys_date = models.DateTimeField(db_column='SysDate')
    wo_no = models.CharField(db_column='WONo', max_length=50) # Assuming max_length, links to WorkOrder
    fin_year_id = models.IntegerField(db_column='FinYearId')
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Master'
        verbose_name = 'Material Credit Note'
        verbose_name_plural = 'Material Credit Notes'

    def __str__(self):
        return self.mcn_no

    @property
    def formatted_mcn_date(self):
        """Formats the SysDate to DD/MM/YYYY as per original ASP.NET behavior (FromDateDMY)."""
        return self.sys_date.strftime('%d/%m/%Y')

    def get_print_report_url(self, wo_id):
        """
        Constructs the URL for the MCN print report.
        In a real application, this would point to a Django report view or an external legacy report.
        We'll assume a Django report view is also being migrated.
        """
        return reverse('project_management:mcn_print_report', kwargs={'mcn_id': self.id, 'wo_id': wo_id}) + f"?wono={self.wo_no}"

```

#### 4.2 Forms (`project_management/forms.py`)

For this particular page, there are no user input forms or data modifications. Thus, no Django `forms.py` file is needed for this specific migration.

```python
# No forms needed for this display-only page.
```

#### 4.3 Views (`project_management/views.py`)

Django Class-Based Views (CBVs) are used to implement the display logic. We'll use a `TemplateView` for the main page to show Work Order and Customer details, and a `ListView` that renders an HTMX partial for the Material Credit Notes table. `RedirectView` handles the navigation actions.

```python
from django.views.generic import TemplateView, ListView, RedirectView
from django.urls import reverse_lazy, reverse
from django.shortcuts import get_object_or_404
from django.http import HttpResponse # For HTMX responses if needed, though not strictly required here
# from django.conf import settings # Uncomment if CompId/FinYearId are read from settings

from .models import WorkOrder, MaterialCreditNote, Customer

class MaterialCreditNotePrintDetailView(TemplateView):
    model = WorkOrder # Though a TemplateView, setting model for clarity
    template_name = 'project_management/materialcreditnote/details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract WOId and WONo from URL query parameters, mimicking ASP.NET Request.QueryString
        wo_id = self.request.GET.get('WOId')
        wo_no = self.request.GET.get('WONo')
        
        # In a real system, CompId and FinYearId would likely come from the authenticated user's session/profile.
        # For demonstration and to match ASP.NET's session usage:
        # Assuming current_comp_id and current_fin_year_id are globally available or from user's context.
        # For this example, we'll use placeholder values that you would replace with your application's logic.
        current_comp_id = 1 # Placeholder: Replace with actual company ID from session/user profile
        current_fin_year_id = 2023 # Placeholder: Replace with actual financial year ID from session/user profile

        work_order = None
        customer = None

        if wo_id and wo_no:
            try:
                # Fetch WorkOrder details, matching ASP.NET's query criteria
                work_order = WorkOrder.objects.get(
                    id=wo_id,
                    wo_no=wo_no,
                    comp_id=current_comp_id,
                    fin_year_id=current_fin_year_id
                )
                customer = work_order.get_customer_details(current_comp_id) # Use fat model method
            except WorkOrder.DoesNotExist:
                # If Work Order not found, context remains None, template handles display
                pass 
        
        context['work_order'] = work_order
        context['customer'] = customer
        context['wo_id'] = wo_id # Passed to template for MCN print URL generation
        context['wo_no'] = wo_no # Passed to template for MCN list and print URL generation
        context['current_comp_id'] = current_comp_id # Passed to template for MCN list filtering
        context['current_fin_year_id'] = current_fin_year_id # Passed to template for MCN list filtering

        return context

class MaterialCreditNoteTablePartialView(ListView):
    """
    Renders the DataTables partial for Material Credit Notes.
    This view is designed to be loaded via HTMX into the main details page.
    """
    model = MaterialCreditNote
    template_name = 'project_management/materialcreditnote/_mcn_table.html'
    context_object_name = 'material_credit_notes' # Renamed for clarity in template

    def get_queryset(self):
        # Retrieve filtering parameters from GET request, mimicking ASP.NET behavior
        wo_no = self.request.GET.get('wo_no')
        comp_id = self.request.GET.get('comp_id')
        fin_year_id = self.request.GET.get('fin_year_id')
        
        if wo_no and comp_id and fin_year_id:
            # Query MCNs using the WorkOrder model's fat method for consistency and logic encapsulation
            # Note: This assumes WorkOrder.objects.get could retrieve the WO just by wo_no/comp_id/fin_year_id
            # If WorkOrder is needed for its ID, this would be a slightly different lookup.
            # For direct replication of original SQL query which was on MaterialCreditNote table:
            return MaterialCreditNote.objects.filter(
                wo_no=wo_no,
                fin_year_id=fin_year_id,
                comp_id=comp_id
            ).order_by('id')
        return MaterialCreditNote.objects.none() # Return empty queryset if parameters are missing

class MaterialCreditNotePrintRedirectView(RedirectView):
    """
    Handles the 'Print' action, redirecting to the actual report page.
    This view serves as an intermediary for generating the report URL.
    """
    permanent = False # This is a temporary redirect, not permanent.

    def get_redirect_url(self, *args, **kwargs):
        mcn_id = kwargs.get('mcn_id')
        wo_id = kwargs.get('wo_id')
        # wo_no is passed as a query parameter in the original ASP.NET redirect
        wo_no = self.request.GET.get('wono', '') 

        # This URL should point to your actual Django report view or an external legacy report system.
        # Assuming the report view (MCNReportView) is also migrated to Django.
        return reverse('project_management:mcn_print_report', kwargs={'mcn_id': mcn_id, 'wo_id': wo_id}) + f"?wono={wo_no}&ModId=7&SubModId=127"

class MaterialCreditNoteCancelRedirectView(RedirectView):
    """
    Handles the 'Cancel' action, redirecting back to the MCN list page.
    """
    permanent = False
    
    def get_redirect_url(self, *args, **kwargs):
        # Original redirect was to "~/Module/ProjectManagement/Transactions/MaterialCreditNote_MCN_Print.aspx"
        # Assuming this maps to a Django list view, e.g., 'mcn_list'.
        return reverse_lazy('project_management:mcn_list') + "?ModId=7&SubModId=127"

# Placeholder for the actual MCN Report View
class MCNReportView(TemplateView):
    """
    This view would render the detailed printable report for a specific MCN.
    It fetches all necessary data for the report based on MCN ID and WO ID.
    """
    template_name = 'project_management/materialcreditnote/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        mcn_id = kwargs.get('mcn_id')
        wo_id = kwargs.get('wo_id')
        wo_no = self.request.GET.get('wono') # Passed as query param

        # Fetch required objects, raising 404 if not found
        mcn = get_object_or_404(MaterialCreditNote, id=mcn_id)
        # Fetch WorkOrder using WOId as it was a query parameter to original MCN Print Details page
        work_order = get_object_or_404(WorkOrder, id=wo_id)
        
        # Assuming CompId is consistent and can be retrieved from work_order or session/user context
        current_comp_id = work_order.comp_id # Or from session/user if work_order might not have it
        customer = work_order.get_customer_details(current_comp_id)

        context['mcn'] = mcn
        context['work_order'] = work_order
        context['customer'] = customer
        context['wo_no'] = wo_no # Keep for clarity if needed, though work_order has it

        return context

```

#### 4.4 Templates (`project_management/templates/project_management/materialcreditnote/`)

All templates extend `core/base.html` for consistency. HTMX is used for dynamic loading of the DataTables list. Alpine.js is included for general UI state management but not strictly required for this specific page's logic.

##### `details.html` (Main page for Work Order and MCN list)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-4"
            style="background:url(/static/images/hdbg.JPG) no-repeat center; background-size: cover; color:white; padding:10px;">
            Material Credit Note [MCN] - Print
        </h2>
        
        {% if work_order %}
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6 text-gray-700 text-sm">
            <div>
                <span class="font-semibold">WO No: </span><span id="lblWono">{{ work_order.wo_no }}</span>
            </div>
            <div>
                <span class="font-semibold">Project Name: </span><span id="lblProjectTitle">{{ work_order.task_project_title }}</span>
            </div>
            <div>
                <span class="font-semibold">Customer Name: </span>
                <span id="lblCustName">{% if customer %}{{ customer.customer_name }} [ {{ customer.customer_id }} ]{% else %}N/A{% endif %}</span>
            </div>
        </div>
        {% else %}
        <p class="text-red-500 font-bold text-center">Work Order details not found or invalid parameters. Please check the URL.</p>
        {% endif %}

        <div class="mt-8">
            <h3 class="text-xl font-semibold text-gray-800 mb-4">Material Credit Notes List</h3>
            <div id="mcn-table-container"
                 hx-get="{% url 'project_management:mcn_table_partial' %}?wo_no={{ wo_no|default:'' }}&comp_id={{ current_comp_id|default:'' }}&fin_year_id={{ current_fin_year_id|default:'' }}"
                 hx-trigger="load, refreshMCNList from:body"
                 hx-swap="innerHTML">
                <!-- DataTables will be loaded here via HTMX -->
                <div class="text-center py-10">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2 text-gray-600">Loading Material Credit Notes...</p>
                </div>
            </div>
        </div>
    </div>

    <div class="flex justify-center mt-6">
        <a href="{% url 'project_management:mcn_cancel' %}" 
           id="btnCancel"
           class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states.
        // For simple data display and HTMX, Alpine might not be strictly necessary here.
    });
</script>
{% endblock %}

```

##### `_mcn_table.html` (Partial for DataTables MCN list)

```html
<div class="overflow-x-auto shadow-sm rounded-lg border border-gray-200" id="Panel1">
    <table id="mcnTable" class="min-w-full divide-y divide-gray-200 yui-datatable-theme">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">MCN No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if material_credit_notes %}
                {% for mcn in material_credit_notes %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900 text-right w-3%">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center w-25%" id="lblMCNNo_{{ mcn.id }}">{{ mcn.mcn_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center w-15%" id="lblDate_{{ mcn.id }}">{{ mcn.formatted_mcn_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-center text-sm font-medium w-15%">
                        <!-- LinkButton1 for Print, id=lblId (hidden) -->
                        <a href="{% url 'project_management:mcn_print_redirect' mcn_id=mcn.id wo_id=wo_id %}?wono={{ wo_no }}"
                           target="_blank" id="LinkButton1_{{ mcn.id }}" class="text-blue-600 hover:text-blue-900 font-semibold">Print</a>
                        <span id="lblId_{{ mcn.id }}" class="hidden">{{ mcn.id }}</span>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="4" class="py-4 px-6 text-center text-sm text-red-500 font-bold">No data found to display</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTable after HTMX swap
    $(document).ready(function() {
        $('#mcnTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Makes the table responsive
            "paging": true,
            "searching": true,
            "info": true,
            "ordering": true
        });
    });
</script>
```

##### `report.html` (Placeholder for the print report content)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8 print-area">
    <div class="bg-white shadow-md rounded-lg p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6 text-center">Material Credit Note Report</h1>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6 text-gray-700">
            <div><span class="font-semibold">MCN No:</span> {{ mcn.mcn_no }}</div>
            <div><span class="font-semibold">MCN Date:</span> {{ mcn.formatted_mcn_date }}</div>
            <div><span class="font-semibold">Work Order No:</span> {{ work_order.wo_no }}</div>
            <div><span class="font-semibold">Project Title:</span> {{ work_order.task_project_title }}</div>
            <div><span class="font-semibold">Customer Name:</span> {% if customer %}{{ customer.customer_name }} [{{ customer.customer_id }}]{% else %}N/A{% endif %}</div>
        </div>

        <div class="mt-8 border-t pt-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">MCN Line Items (Example Data)</h2>
            <table class="min-w-full divide-y divide-gray-200 shadow-sm rounded-lg">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                        <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr>
                        <td class="py-2 px-4">Steel Bars (12mm)</td>
                        <td class="py-2 px-4">100 kg</td>
                        <td class="py-2 px-4">$1.20/kg</td>
                        <td class="py-2 px-4">$120.00</td>
                    </tr>
                    <tr>
                        <td class="py-2 px-4">Cement Bags (50kg)</td>
                        <td class="py-2 px-4">20 bags</td>
                        <td class="py-2 px-4">$7.50/bag</td>
                        <td class="py-2 px-4">$150.00</td>
                    </tr>
                    <!-- More MCN details would be dynamically loaded here -->
                </tbody>
            </table>
            
            <div class="mt-8 text-center">
                <button onclick="window.print()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md">Print this Report</button>
                <a href="javascript:history.back()" class="ml-4 bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-md">Go Back</a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Styling for print output, hiding non-print elements */
    @media print {
        body > *:not(.print-area) {
            display: none !important;
        }
        .print-area {
            width: 100%;
            margin: 0;
            padding: 0;
            box-shadow: none;
            /* Ensure background images are printed if needed for specific reports */
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
        }
        button, a {
            display: none !important; /* Hide print/back buttons on actual printout */
        }
    }
</style>
{% endblock %}
```

#### 4.5 URLs (`project_management/urls.py`)

This file defines the URL patterns for accessing the views. `app_name` is set for easy reversal of URLs in templates and views.

```python
from django.urls import path
from .views import (
    MaterialCreditNotePrintDetailView,
    MaterialCreditNoteTablePartialView,
    MaterialCreditNotePrintRedirectView,
    MaterialCreditNoteCancelRedirectView,
    MCNReportView, 
)

app_name = 'project_management' # Namespace for URLs

urlpatterns = [
    # Main MCN print details page. Mimics the original ASP.NET .aspx page.
    # Accepts WOId and WONo as query parameters.
    path('mcn/print_details/', MaterialCreditNotePrintDetailView.as_view(), name='mcn_print_details'),
    
    # HTMX endpoint for the MCN list table. This is a partial view.
    # Accepts wo_no, comp_id, fin_year_id as query parameters for filtering.
    path('mcn/table_partial/', MaterialCreditNoteTablePartialView.as_view(), name='mcn_table_partial'),

    # Redirect view for the "Print" action. This generates the URL for the report page.
    # Takes mcn_id and wo_id as path parameters, and wo_no as a query parameter.
    path('mcn/print_redirect/<int:mcn_id>/<int:wo_id>/', MaterialCreditNotePrintRedirectView.as_view(), name='mcn_print_redirect'),
    
    # Placeholder for the actual MCN report view. This is where the printable content is rendered.
    path('mcn/print_report/<int:mcn_id>/<int:wo_id>/', MCNReportView.as_view(), name='mcn_print_report'),

    # Redirect view for the "Cancel" action. Navigates back to a presumed MCN list page.
    path('mcn/cancel/', MaterialCreditNoteCancelRedirectView.as_view(), name='mcn_cancel'),

    # Placeholder for the previous MCN list page. This URL is used by the 'Cancel' button.
    # Replace this with the actual URL for your main MCN list view if it's different.
    path('mcn/list/', MaterialCreditNotePrintDetailView.as_view(), name='mcn_list'), 
]

```

#### 4.6 Tests (`project_management/tests.py`)

Comprehensive tests for models (unit tests) and views (integration tests) ensure functionality, data integrity, and proper HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch
from datetime import datetime
from .models import MaterialCreditNote, WorkOrder, Customer

class MaterialCreditNoteModelTest(TestCase):
    """
    Unit tests for the MaterialCreditNote, WorkOrder, and Customer models.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all tests in this class
        cls.comp_id = 1 # Example company ID
        cls.fin_year_id = 2023 # Example financial year ID

        cls.customer = Customer.objects.create(
            customer_id=101,
            customer_name='Test Customer Alpha',
            comp_id=cls.comp_id
        )
        cls.work_order = WorkOrder.objects.create(
            id=1,
            wo_no='WO-TEST-001',
            task_project_title='Project Alpha Integration',
            customer_id=cls.customer.customer_id,
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id
        )
        cls.mcn1 = MaterialCreditNote.objects.create(
            id=1001,
            mcn_no='MCN-TEST-A',
            sys_date=datetime(2023, 1, 15, 10, 0, 0),
            wo_no=cls.work_order.wo_no,
            fin_year_id=cls.fin_year_id,
            comp_id=cls.comp_id
        )
        cls.mcn2 = MaterialCreditNote.objects.create(
            id=1002,
            mcn_no='MCN-TEST-B',
            sys_date=datetime(2023, 2, 20, 11, 30, 0),
            wo_no=cls.work_order.wo_no,
            fin_year_id=cls.fin_year_id,
            comp_id=cls.comp_id
        )
  
    def test_materialcreditnote_creation(self):
        """Test that MaterialCreditNote object is created correctly."""
        mcn = MaterialCreditNote.objects.get(id=self.mcn1.id)
        self.assertEqual(mcn.mcn_no, 'MCN-TEST-A')
        self.assertEqual(mcn.wo_no, 'WO-TEST-001')
        self.assertEqual(mcn.sys_date, datetime(2023, 1, 15, 10, 0, 0))
        
    def test_materialcreditnote_formatted_date_property(self):
        """Test the formatted_mcn_date property."""
        mcn = MaterialCreditNote.objects.get(id=self.mcn1.id)
        self.assertEqual(mcn.formatted_mcn_date, '15/01/2023')

    def test_materialcreditnote_get_print_report_url(self):
        """Test the generation of the print report URL."""
        mcn = MaterialCreditNote.objects.get(id=self.mcn1.id)
        expected_url = reverse('project_management:mcn_print_report', 
                               kwargs={'mcn_id': mcn.id, 'wo_id': self.work_order.id}) + f"?wono={self.work_order.wo_no}"
        self.assertEqual(mcn.get_print_report_url(self.work_order.id), expected_url)

    def test_workorder_creation(self):
        """Test that WorkOrder object is created correctly."""
        wo = WorkOrder.objects.get(id=self.work_order.id)
        self.assertEqual(wo.wo_no, 'WO-TEST-001')
        self.assertEqual(wo.task_project_title, 'Project Alpha Integration')
        self.assertEqual(wo.customer_id, self.customer.customer_id)

    def test_workorder_get_customer_details(self):
        """Test retrieving customer details from WorkOrder."""
        wo = WorkOrder.objects.get(id=self.work_order.id)
        customer = wo.get_customer_details(self.comp_id)
        self.assertIsNotNone(customer)
        self.assertEqual(customer.customer_name, 'Test Customer Alpha')
        self.assertEqual(customer.customer_id, self.customer.customer_id)

    def test_workorder_get_material_credit_notes(self):
        """Test retrieving associated MCNs from WorkOrder."""
        wo = WorkOrder.objects.get(id=self.work_order.id)
        mcns = wo.get_material_credit_notes(self.comp_id, self.fin_year_id)
        self.assertEqual(mcns.count(), 2)
        self.assertIn(self.mcn1, mcns)
        self.assertIn(self.mcn2, mcns)
        # Check ordering
        self.assertEqual(mcns.first().mcn_no, 'MCN-TEST-A') 
        self.assertEqual(mcns.last().mcn_no, 'MCN-TEST-B')

    def test_customer_creation(self):
        """Test that Customer object is created correctly."""
        cust = Customer.objects.get(customer_id=self.customer.customer_id)
        self.assertEqual(cust.customer_name, 'Test Customer Alpha')
        self.assertEqual(cust.comp_id, self.comp_id)

class MaterialCreditNoteViewsTest(TestCase):
    """
    Integration tests for the Material Credit Note views.
    """
    @classmethod
    def setUpTestData(cls):
        # Setup common test data for all tests in this class
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.customer = Customer.objects.create(
            customer_id=201,
            customer_name='Test Customer Beta',
            comp_id=cls.comp_id
        )
        cls.work_order = WorkOrder.objects.create(
            id=2,
            wo_no='WO-TEST-002',
            task_project_title='Project Beta Development',
            customer_id=cls.customer.customer_id,
            comp_id=cls.comp_id,
            fin_year_id=cls.fin_year_id
        )
        cls.mcn = MaterialCreditNote.objects.create(
            id=2001,
            mcn_no='MCN-TEST-C',
            sys_date=datetime(2023, 3, 10, 14, 0, 0),
            wo_no=cls.work_order.wo_no,
            fin_year_id=cls.fin_year_id,
            comp_id=cls.comp_id
        )
    
    def setUp(self):
        self.client = Client()
        # Patch the hardcoded comp_id and fin_year_id in views for consistent testing
        self.patcher_detail_comp_id = patch('project_management.views.MaterialCreditNotePrintDetailView.current_comp_id', self.comp_id)
        self.patcher_detail_fin_year_id = patch('project_management.views.MaterialCreditNotePrintDetailView.current_fin_year_id', self.fin_year_id)
        self.patcher_table_comp_id = patch('project_management.views.MaterialCreditNoteTablePartialView.current_comp_id', self.comp_id)
        self.patcher_table_fin_year_id = patch('project_management.views.MaterialCreditNoteTablePartialView.current_fin_year_id', self.fin_year_id)
        
        self.patcher_detail_comp_id.start()
        self.patcher_detail_fin_year_id.start()
        self.patcher_table_comp_id.start()
        self.patcher_table_fin_year_id.start()
        
        self.addCleanup(self.patcher_detail_comp_id.stop)
        self.addCleanup(self.patcher_detail_fin_year_id.stop)
        self.addCleanup(self.patcher_table_comp_id.stop)
        self.addCleanup(self.patcher_table_fin_year_id.stop)

    def test_mcn_print_details_view_get_success(self):
        """Test the main details view renders correctly with valid data."""
        url = reverse('project_management:mcn_print_details') + f'?WOId={self.work_order.id}&WONo={self.work_order.wo_no}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnote/details.html')
        self.assertIn('work_order', response.context)
        self.assertIn('customer', response.context)
        self.assertEqual(response.context['work_order'].wo_no, self.work_order.wo_no)
        self.assertEqual(response.context['customer'].customer_name, self.customer.customer_name)
        self.assertContains(response, self.work_order.wo_no)
        self.assertContains(response, self.work_order.task_project_title)
        self.assertContains(response, self.customer.customer_name)
        self.assertContains(response, 'Material Credit Notes List') # Check for the table section header

    def test_mcn_print_details_view_get_no_work_order(self):
        """Test the main details view handles missing/invalid work order gracefully."""
        url = reverse('project_management:mcn_print_details') + '?WOId=9999&WONo=NONEXISTENT'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Work Order details not found or invalid parameters.')
        self.assertIsNone(response.context['work_order']) # Ensure context is None

    def test_mcn_table_partial_view_get_htmx(self):
        """Test the HTMX-loaded MCN table partial view."""
        url = reverse('project_management:mcn_table_partial') + f'?wo_no={self.work_order.wo_no}&comp_id={self.comp_id}&fin_year_id={self.fin_year_id}'
        # Simulate an HTMX request by adding the HX-Request header
        response = self.client.get(url, HTTP_HX_REQUEST='true') 
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnote/_mcn_table.html')
        self.assertIn('material_credit_notes', response.context)
        self.assertEqual(len(response.context['material_credit_notes']), 1)
        self.assertContains(response, self.mcn.mcn_no)
        self.assertContains(response, self.mcn.formatted_mcn_date)
        self.assertContains(response, 'mcnTable') # Verify DataTable ID is present
        self.assertContains(response, 'Print') # Verify Print link is present

    def test_mcn_table_partial_view_get_no_data(self):
        """Test MCN table partial when no MCNs are found for the work order."""
        # Create a work order with no associated MCNs
        wo_no_other = 'WO-NO-MCN'
        WorkOrder.objects.create(
            id=999,
            wo_no=wo_no_other,
            task_project_title='Project with No MCNs',
            customer_id=self.customer.customer_id, # Reusing customer
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id
        )
        url = reverse('project_management:mcn_table_partial') + f'?wo_no={wo_no_other}&comp_id={self.comp_id}&fin_year_id={self.fin_year_id}'
        response = self.client.get(url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data found to display')


    def test_mcn_print_redirect_view(self):
        """Test the 'Print' redirect view correctly redirects to the report page."""
        url = reverse('project_management:mcn_print_redirect', 
                      kwargs={'mcn_id': self.mcn.id, 'wo_id': self.work_order.id}) + f'?wono={self.work_order.wo_no}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Expect a redirect
        # Verify the redirect URL matches the expected report URL
        expected_redirect_url = reverse('project_management:mcn_print_report', 
                                        kwargs={'mcn_id': self.mcn.id, 'wo_id': self.work_order.id}) + f'?wono={self.work_order.wo_no}&ModId=7&SubModId=127'
        self.assertRedirects(response, expected_redirect_url, fetch_redirect_response=False)

    def test_mcn_cancel_redirect_view(self):
        """Test the 'Cancel' redirect view correctly navigates back to the MCN list."""
        url = reverse('project_management:mcn_cancel')
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302) # Expect a redirect
        # Verify the redirect URL matches the presumed MCN list page
        expected_redirect_url = reverse('project_management:mcn_list') + '?ModId=7&SubModId=127'
        self.assertRedirects(response, expected_redirect_url, fetch_redirect_response=False)

    def test_mcn_report_view(self):
        """Test the actual MCN report view renders correctly."""
        url = reverse('project_management:mcn_print_report', 
                      kwargs={'mcn_id': self.mcn.id, 'wo_id': self.work_order.id}) + f'?wono={self.work_order.wo_no}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnote/report.html')
        self.assertIn('mcn', response.context)
        self.assertIn('work_order', response.context)
        self.assertIn('customer', response.context)
        self.assertContains(response, 'Material Credit Note Report')
        self.assertContains(response, self.mcn.mcn_no)
        self.assertContains(response, self.work_order.wo_no)
        self.assertContains(response, self.customer.customer_name)
        self.assertContains(response, 'Print this Report')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for dynamic content:** The `_mcn_table.html` partial is loaded into `details.html` using `hx-get` on `div#mcn-table-container`. This ensures the table can be refreshed independently without a full page reload if needed (e.g., `hx-trigger="load, refreshMCNList from:body"`).
*   **DataTables for list views:** The `_mcn_table.html` partial initializes DataTables on the `mcnTable` ID upon being loaded. This provides client-side searching, sorting, and pagination.
*   **Alpine.js for UI state:** While this specific page is primarily display-oriented and HTMX-driven, Alpine.js remains available via `core/base.html` for any future dynamic UI elements (e.g., showing/hiding elements, simple client-side validation, modal control) without extra JavaScript. For this page, basic Alpine initialization is included but no specific Alpine logic is demonstrated.
*   **No custom JavaScript:** All dynamic interactions (data loading, table features) are handled by HTMX, DataTables, and potentially Alpine.js, minimizing the need for custom JS code.
*   **DRY Template Structure:** The `details.html` and `report.html` templates extend `core/base.html` (not provided in this output as per instructions), inheriting common headers, footers, and CDN links (for Tailwind CSS, HTMX, Alpine.js, jQuery, DataTables). This promotes reusability and maintainability.
*   **Strict Separation of Concerns:** Views remain thin, handling request/response and delegating data retrieval and business logic to the models. HTML rendering is solely handled by templates.

## Final Notes

*   This plan provides a direct, automated translation of the ASP.NET page's functionality into a modern Django stack.
*   Placeholders like `current_comp_id` and `current_fin_year_id` in views should be replaced with actual logic to retrieve these values from the authenticated user's session or profile in a real application.
*   The `mcn_list` URL is a placeholder and should point to your actual Material Credit Note list page if it differs from the `mcn_print_details` page.
*   The `MCNReportView` and its `report.html` are basic placeholders. The actual report generation logic (e.g., generating PDFs, complex layouts) would need to be implemented within this view, potentially leveraging Django's rendering capabilities or dedicated reporting libraries.
*   The ASP.NET code used paths like `../../../images/hdbg.JPG` for styling. In Django, this implies collecting static files and referencing them correctly (e.g., `/static/images/hdbg.JPG`). I've updated the template to reflect this.