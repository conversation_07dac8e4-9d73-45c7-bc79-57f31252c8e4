## ASP.NET to Django Conversion Script: Material Credit Note New Details

This modernization plan outlines the strategic migration of your ASP.NET Material Credit Note (MCN) creation page to a modern Django-based solution. Our approach prioritizes automation, leveraging AI-assisted tools for code generation and adhering to Django's best practices, including a fat model/thin view architecture, HTMX for dynamic interactions, Alpine.js for UI state, and DataTables for enhanced data presentation.

The goal is to deliver a robust, maintainable, and scalable application while ensuring a smooth transition from your legacy system. All technical concepts are presented in plain English, focusing on business benefits and actionable steps for your team.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (e.g., `core/base.html`).
- Focus ONLY on component-specific code for the current module (`MaterialCreditNote`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code that are relevant to the Material Credit Note creation process.

**Instructions:**
The ASP.NET code interacts with several tables to display existing data and record new MCN entries. We'll define Django models for these tables, ensuring they map directly to your existing database schema using `managed = False`.

**Identified Tables and Core Columns:**

1.  **`tblPM_MaterialCreditNote_Master` (for Material Credit Note Header):**
    *   `Id` (Primary Key, auto-incrementing)
    *   `SysDate` (Date of entry)
    *   `SysTime` (Time of entry)
    *   `SessionId` (User session ID)
    *   `CompId` (Company ID)
    *   `FinYearId` (Financial Year ID)
    *   `MCNNo` (Material Credit Note Number, generated sequentially)
    *   `WONo` (Work Order Number, foreign key/reference)

2.  **`tblPM_MaterialCreditNote_Details` (for Material Credit Note Line Items):**
    *   `Id` (Primary Key, auto-incrementing)
    *   `MId` (Foreign Key to `tblPM_MaterialCreditNote_Master.Id`)
    *   `PId` (Parent ID from BOM or Item, context-dependent)
    *   `CId` (Child ID from BOM or Item, context-dependent)
    *   `MCNQty` (Quantity for credit note)

3.  **`tblDG_BOM_Master` (Bill of Material Master, for listing items):**
    *   `Id` (Primary Key)
    *   `PId` (Parent Item ID)
    *   `CId` (Component Item ID)
    *   `WONo` (Work Order Number)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`)
    *   `Qty` (Quantity in BOM, referred to as `BOMQty`)
    *   `CompId`, `FinYearId`

4.  **`tblDG_Item_Master` (Item Master, for item details):**
    *   `Id` (Primary Key)
    *   `ItemCode`
    *   `PartNo`
    *   `ManfDesc` (Description)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`)
    *   `FileName` (Drawing/Image filename)
    *   `AttName` (Specification Sheet filename)
    *   `FileData` (Binary data for drawing/image)
    *   `AttData` (Binary data for specification sheet)
    *   `ContentType` (MIME type for drawing/image)
    *   `AttContentType` (MIME type for specification sheet)

5.  **`Unit_Master` (Unit of Measure Master):**
    *   `Id` (Primary Key)
    *   `Symbol` (Unit symbol, e.g., "PC")

6.  **`SD_Cust_WorkOrder_Master` (Work Order Master):**
    *   `Id` (Primary Key)
    *   `WONo`
    *   `TaskProjectTitle` (Project Name)
    *   `CustomerId` (Foreign Key to `SD_Cust_Master.CustomerId`)
    *   `CompId`, `FinYearId`

7.  **`SD_Cust_Master` (Customer Master):**
    *   `CustomerId` (Primary Key)
    *   `CustomerName`

### Step 2: Identify Backend Functionality

**Task:** Determine the business logic and data operations in the ASP.NET code.

**Instructions:**
The page's primary function is to create a new Material Credit Note.

*   **Read Operations (Data Display):**
    *   Retrieving Work Order details (WONo, Project Name, Customer Name) from `SD_Cust_WorkOrder_Master` and `SD_Cust_Master` based on `WOId` and `WONo` from query parameters.
    *   Populating the main grid: This is a complex query that involves:
        *   Selecting base items from `tblDG_BOM_Master` (filtered by `WONo`, `CompId`, `FinYearId`, `PId='0'`).
        *   Joining with `tblDG_Item_Master` to get `ItemCode`/`PartNo`, `Description`, `UOMBasic`, `FileName`, `AttName`.
        *   Joining with `Unit_Master` to get `UOM Symbol`.
        *   Calculating `BOM Qty` from `tblDG_BOM_Master.Qty`.
        *   Calculating `Total MCN Qty` by summing `MCNQty` from `tblPM_MaterialCreditNote_Details` (joined with `tblPM_MaterialCreditNote_Master`) for each item's `PId` and `CId`.
        *   Presenting an input field for `MCN Qty` (initialized to 0) for user input.
        *   Conditional hiding of `MCN Qty` input if `BOM Qty - Total MCN Qty` is 0.
*   **Create Operations (Submit Action `btnSubmit_Click`):**
    *   Generating a new `MCNNo` based on the last existing MCN number for the current company and financial year.
    *   Validating user-entered `MCNQty`:
        *   Must not be empty.
        *   Must be a valid positive number (regex `^\d{1,15}(\.\d{0,3})?$`).
        *   Must not exceed the remaining BOM quantity (`BOMQty - TotalMCNQty >= MCNQty`).
    *   If validation passes, inserting a new record into `tblPM_MaterialCreditNote_Master`.
    *   Retrieving the `Id` of the newly created master record.
    *   For each grid row where `MCNQty` was entered and valid, inserting a new record into `tblPM_MaterialCreditNote_Details`, linking it to the new master record (`MId`).
    *   Redirecting to a success page upon successful saving.
*   **File Download Operations (`GridView1_RowCommand`):**
    *   Handling commands to download `Draw/Img` or `Spec.sheet` by redirecting to a generic `DownloadFile.aspx` page, passing `ItemId` and other parameters. This will be replaced by a Django view returning a file response.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles to map them to Django templates, HTMX, and Alpine.js.

**Identified UI Components and Their Django Equivalents:**

*   **Layout:** Content placeholders indicate a master page structure. In Django, this translates to `core/base.html` with `{% block content %}`.
*   **Header:** `<b>Material Credit Note [MCN] - New</b>` becomes an `h2` tag.
*   **Information Labels:** `lblWono`, `lblProjectTitle`, `lblCustName` will be rendered directly from Django view context variables.
*   **Data Grid (`asp:GridView`):** This is the central component.
    *   Will be replaced by a standard HTML `<table>` element.
    *   Enhanced with **DataTables.js** for client-side search, sort, and pagination.
    *   The entire table (or its `<tbody>`) will be loaded and updated dynamically using **HTMX** (e.g., via `hx-get` on page load, or `hx-post` for form submission results).
    *   Input fields (`asp:TextBox ID="txtqty"`) will become standard `<input type="number">` elements within the table, managed by a Django formset.
    *   Download links (`asp:LinkButton ID="lbtnDownload"`) will be standard `<a href="...">` links pointing to Django download views.
*   **Action Buttons (`asp:Button`):** `btnSubmit` and `btnCancel` will be standard `<button type="submit">` and `<button type="button">` respectively.
    *   `btnSubmit` will have HTMX attributes (`hx-post`, `hx-swap="none"`, `hx-trigger="submit"`) for asynchronous submission.
    *   `btnCancel` will simply redirect or close a modal (if used).
*   **Validation (`asp:RegularExpressionValidator`):** Will be handled by Django forms' `clean()` methods, `RegexValidator`, and potentially formset `clean()` methods.
*   **CSS:** The inline styles and external CSS links will be replaced by **Tailwind CSS** classes and potentially a minimal custom CSS file if needed.
*   **JavaScript:** `loadingNotifier.js`, `PopUpMsg.js` will be replaced by native browser features, HTMX, and Alpine.js for interactive elements and notifications.

### Step 4: Generate Django Code

We will create a new Django application, for example, `project_management`, to house these components.

#### 4.1 Models (`project_management/models.py`)

We'll define the core models that map to your existing database tables. These models will encapsulate business logic specific to Material Credit Notes.

```python
from django.db import models
from django.utils import timezone
from django.db.models import Sum, F
from django.core.exceptions import ValidationError

# Assuming these are lookup/master tables, so minimal fields for context
class WorkOrderMaster(models.Model):
    # Id is implicitly added by Django unless primary_key=True is set.
    # Assuming 'Id' is the primary key in the database for simplicity
    # WOId is the primary key in the ASP.NET context often. Let's align.
    id = models.IntegerField(db_column='Id', primary_key=True)
    wono = models.CharField(db_column='WONo', max_length=50)
    task_project_title = models.CharField(db_column='TaskProjectTitle', max_length=255)
    customer_id = models.IntegerField(db_column='CustomerId') # This would be a ForeignKey in a managed DB
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.wono

    @property
    def project_name(self):
        return self.task_project_title

    @property
    def customer_name_full(self):
        try:
            customer = CustomerMaster.objects.get(customer_id=self.customer_id)
            return f"{customer.customer_name} [ {self.customer_id} ]"
        except CustomerMaster.DoesNotExist:
            return f"Unknown Customer [ {self.customer_id} ]"

class CustomerMaster(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)

    class Meta:
        managed = False
        db_table = 'SD_Cust_Master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return self.customer_name

class BomMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Or auto-incrementing if new records
    p_id = models.IntegerField(db_column='PId', default=0) # Parent ID
    c_id = models.IntegerField(db_column='CId', default=0) # Component ID
    wono = models.CharField(db_column='WONo', max_length=50)
    item_id = models.IntegerField(db_column='ItemId') # Foreign key to tblDG_Item_Master
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # BOM Quantity
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'BOM Master'
        verbose_name_plural = 'BOM Masters'

    def __str__(self):
        return f"BOM for WO {self.wono} - Item {self.item_id}"

    @property
    def item(self):
        # This relationship should ideally be defined as ForeignKey if managed=True
        # For managed=False, we fetch manually.
        try:
            return ItemMaster.objects.get(id=self.item_id)
        except ItemMaster.DoesNotExist:
            return None

    def get_total_mcn_qty(self, comp_id, fin_year_id):
        # Replicates the logic from ASP.NET loaddata for TotalMCNQty
        # Sums MCNQty for this PId, CId across all existing MCNs
        total_qty = MaterialCreditNoteDetail.objects.filter(
            p_id=self.p_id,
            c_id=self.c_id,
            material_credit_note__comp_id=comp_id,
            material_credit_note__fin_year_id__lte=fin_year_id # ASP.NET uses <= finyear
        ).aggregate(Sum('mcn_qty'))['mcn_qty__sum']
        return float(total_qty) if total_qty is not None else 0.0

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, null=True, blank=True)
    part_no = models.CharField(db_column='PartNo', max_length=100, null=True, blank=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=500, null=True, blank=True) # Description
    uom_basic_id = models.IntegerField(db_column='UOMBasic', null=True) # Foreign key to Unit_Master
    file_name = models.CharField(db_column='FileName', max_length=255, null=True, blank=True) # Draw/Img file name
    att_name = models.CharField(db_column='AttName', max_length=255, null=True, blank=True) # Spec.sheet file name
    # FileData, AttData, ContentType, AttContentType would typically be FileFields in Django
    # We'll assume they exist for the download view.

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code if self.item_code else self.part_no if self.part_no else str(self.id)

    @property
    def display_item_code(self):
        return self.item_code if self.item_code else self.part_no

    @property
    def uom_symbol(self):
        try:
            return UnitMaster.objects.get(id=self.uom_basic_id).symbol
        except UnitMaster.DoesNotExist:
            return ''

    @property
    def has_drawing(self):
        return bool(self.file_name)

    @property
    def has_spec_sheet(self):
        return bool(self.att_name)

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol


class MaterialCreditNoteManager(models.Manager):
    def get_next_mcn_no(self, comp_id, fin_year_id):
        last_mcn = self.filter(comp_id=comp_id, fin_year_id=fin_year_id).order_by('-id').first()
        if last_mcn and last_mcn.mcn_no:
            try:
                next_num = int(last_mcn.mcn_no) + 1
            except ValueError:
                next_num = 1 # Fallback if MCNNo is not purely numeric
        else:
            next_num = 1
        return f"{next_num:04d}" # Format as D4


class MaterialCreditNote(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is auto-incrementing in DB
    sys_date = models.DateField(db_column='SysDate', default=timezone.now)
    sys_time = models.TimeField(db_column='SysTime', default=timezone.now)
    session_id = models.CharField(db_column='SessionId', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    mcn_no = models.CharField(db_column='MCNNo', max_length=10, unique=True)
    wono = models.CharField(db_column='WONo', max_length=50) # Link to WorkOrderMaster by WONo
    
    objects = MaterialCreditNoteManager()

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Master'
        verbose_name = 'Material Credit Note'
        verbose_name_plural = 'Material Credit Notes'

    def __str__(self):
        return f"MCN {self.mcn_no} for WO {self.wono}"

    def save(self, *args, **kwargs):
        # Auto-generate MCNNo if not set (for new records)
        if not self.pk and not self.mcn_no:
            self.mcn_no = MaterialCreditNote.objects.get_next_mcn_no(self.comp_id, self.fin_year_id)
        if not self.sys_date:
            self.sys_date = timezone.now().date()
        if not self.sys_time:
            self.sys_time = timezone.now().time()
        super().save(*args, **kwargs)

    # Business logic methods here (e.g., to summarize details)

class MaterialCreditNoteDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming Id is auto-incrementing in DB
    material_credit_note = models.ForeignKey(
        MaterialCreditNote, 
        on_delete=models.CASCADE, 
        db_column='MId', 
        related_name='details'
    )
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    mcn_qty = models.DecimalField(db_column='MCNQty', max_digits=18, decimal_places=3)

    class Meta:
        managed = False
        db_table = 'tblPM_MaterialCreditNote_Details'
        verbose_name = 'Material Credit Note Detail'
        verbose_name_plural = 'Material Credit Note Details'

    def __str__(self):
        return f"MCN Detail {self.id} for MCN {self.material_credit_note.mcn_no}"

```

#### 4.2 Forms (`project_management/forms.py`)

We'll define a form for the main `MaterialCreditNote` and a custom form for each detail line item. We'll use a `formset_factory` to manage multiple detail items.

```python
from django import forms
from django.forms import formset_factory
from .models import MaterialCreditNote, MaterialCreditNoteDetail
import re

# Custom validator for quantities
def validate_qty_input(value):
    if value is None:
        raise forms.ValidationError("Quantity cannot be empty.")
    # ASP.NET regex: ^\d{1,15}(\.\d{0,3})?$
    if not re.match(r"^\d{1,15}(\.\d{0,3})?$", str(value)):
        raise forms.ValidationError("Invalid quantity format. Up to 15 digits, 0-3 decimal places.")
    if value < 0:
        raise forms.ValidationError("Quantity must be positive.")

class MaterialCreditNoteForm(forms.ModelForm):
    # These fields are usually hidden or populated by system/context
    # In Django, MCNNo would be generated in save() method if not provided
    # WONo, CompId, FinYearId might come from URL/session/context
    class Meta:
        model = MaterialCreditNote
        fields = [] # No direct user input fields for the header on this page
        # The WONo, CompId, FinYearId will be passed to the instance in the view

class MaterialCreditNoteItemForm(forms.Form):
    # Hidden fields to carry BOM item context
    bom_id = forms.IntegerField(widget=forms.HiddenInput())
    item_id = forms.IntegerField(widget=forms.HiddenInput())
    p_id = forms.IntegerField(widget=forms.HiddenInput())
    c_id = forms.IntegerField(widget=forms.HiddenInput())
    bom_qty = forms.DecimalField(max_digits=18, decimal_places=3, widget=forms.HiddenInput())
    total_mcn_qty = forms.DecimalField(max_digits=18, decimal_places=3, widget=forms.HiddenInput())

    # Display fields (read-only) - not part of form.fields but for context in template
    item_code = forms.CharField(max_length=100, required=False, widget=forms.HiddenInput())
    description = forms.CharField(max_length=500, required=False, widget=forms.HiddenInput())
    uom = forms.CharField(max_length=50, required=False, widget=forms.HiddenInput())

    # User input field
    mcn_qty = forms.DecimalField(
        max_digits=18,
        decimal_places=3,
        required=False, # Allow empty, then validate
        validators=[validate_qty_input],
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm',
            'placeholder': 'Enter Qty'
        })
    )

    def clean(self):
        cleaned_data = super().clean()
        mcn_qty = cleaned_data.get('mcn_qty')
        bom_qty = cleaned_data.get('bom_qty')
        total_mcn_qty = cleaned_data.get('total_mcn_qty')

        if mcn_qty is not None and mcn_qty > 0:
            available_qty = bom_qty - total_mcn_qty
            if mcn_qty > available_qty:
                self.add_error('mcn_qty', f"MCN Quantity ({mcn_qty}) cannot exceed remaining BOM quantity ({available_qty:.3f}).")
        
        # If mcn_qty is entered but then found to be 0 or less after validation,
        # it's considered not entered for the purpose of MCN creation.
        # This aligns with ASP.NET's "Convert.ToDouble(...) > 0" check.
        if mcn_qty is not None and mcn_qty <= 0:
            cleaned_data['mcn_qty'] = None # Treat as not entered for detail creation

        return cleaned_data

# Base formset to handle overall validation for multiple items
class BaseMaterialCreditNoteItemFormSet(forms.BaseFormSet):
    def clean(self):
        if any(self.errors):
            # Don't bother validating the formset unless each form is valid.
            return

        total_entered_qty_items = 0
        for form in self.forms:
            if form.cleaned_data and form.cleaned_data.get('mcn_qty') is not None:
                total_entered_qty_items += 1
        
        if total_entered_qty_items == 0:
            # Replicates ASP.NET's check: "count" of valid positive entries.
            # If no items have a valid MCNQty > 0, then raise error.
            raise forms.ValidationError("No valid Material Credit Note quantities entered. Please enter at least one positive quantity.")

# Create the formset factory
MaterialCreditNoteItemFormSet = formset_factory(
    MaterialCreditNoteItemForm,
    formset=BaseMaterialCreditNoteItemFormSet,
    extra=0, # We populate forms dynamically, not adding extra empty forms
    can_delete=False # Not allowing deletion of rows
)

```

#### 4.3 Views (`project_management/views.py`)

This page is a `CreateView` for the main `MaterialCreditNote` object, but it also handles a formset for its `MaterialCreditNoteDetail` children. The complex data retrieval for the grid will be moved to a service layer.

```python
from django.views.generic import CreateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, FileResponse, Http404
from django.shortcuts import get_object_or_404
from django.db import transaction

from .models import MaterialCreditNote, MaterialCreditNoteDetail, WorkOrderMaster, BomMaster, ItemMaster
from .forms import MaterialCreditNoteForm, MaterialCreditNoteItemFormSet
from .services import MaterialCreditNoteService # We'll define this below
from django.conf import settings
import os

class MaterialCreditNoteCreateView(CreateView):
    model = MaterialCreditNote
    form_class = MaterialCreditNoteForm
    template_name = 'project_management/materialcreditnote/create.html'
    success_url = reverse_lazy('materialcreditnote_list') # Redirect to the MCN list page

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Retrieve WOId and WONo from query parameters, similar to ASP.NET Request.QueryString
        wo_id = self.request.GET.get('WOId')
        wono = self.request.GET.get('WONo')
        
        # In a real app, CompId and FinYearId would come from session or user profile
        # For demonstration, use placeholders or fetch from a config
        comp_id = self.request.session.get('compid', 1) # Example: default to 1
        fin_year_id = self.request.session.get('finyear', 2023) # Example: default to 2023
        session_id = self.request.session.session_key or 'default_session_id' # Example

        context['comp_id'] = comp_id
        context['fin_year_id'] = fin_year_id
        context['session_id'] = session_id

        work_order_data = {}
        if wo_id:
            try:
                # Use a service to fetch the work order details
                work_order_data = MaterialCreditNoteService.get_work_order_details(
                    wo_id=int(wo_id),
                    comp_id=comp_id,
                    fin_year_id=fin_year_id
                )
            except WorkOrderMaster.DoesNotExist:
                messages.error(self.request, "Work Order not found.")
                # Optionally redirect or show an error page
        
        context['wo_no'] = work_order_data.get('wono', wono)
        context['project_title'] = work_order_data.get('project_name', 'N/A')
        context['customer_name'] = work_order_data.get('customer_name_full', 'N/A')
        
        # Initialize formset
        if self.request.POST:
            context['formset'] = MaterialCreditNoteItemFormSet(self.request.POST)
        else:
            # Prepare initial data for the formset based on BOM items, similar to loaddata()
            initial_data = MaterialCreditNoteService.prepare_bom_items_for_mcn_formset(
                wo_no=context['wo_no'],
                comp_id=comp_id,
                fin_year_id=fin_year_id
            )
            context['formset'] = MaterialCreditNoteItemFormSet(initial=initial_data)
            
        return context

    def post(self, request, *args, **kwargs):
        # In a real app, CompId and FinYearId would come from session or user profile
        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2023)
        session_id = self.request.session.session_key or 'default_session_id'
        
        form = self.get_form()
        formset = MaterialCreditNoteItemFormSet(request.POST)
        
        if form.is_valid() and formset.is_valid():
            # Pass session/context variables to the service layer for creating the MCN
            # The WONo comes from the initial GET request for context
            wo_no = request.GET.get('WONo') # Assuming WONo is present in query string on POST too

            try:
                MaterialCreditNoteService.create_material_credit_note(
                    main_form_data=form.cleaned_data,
                    detail_formset_data=formset.cleaned_data,
                    wo_no=wo_no,
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    session_id=session_id
                )
                messages.success(request, 'Material Credit Note created successfully.')
                
                # HTMX specific response for successful creation
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        status=204, # No Content
                        headers={
                            # Trigger an event on the client side to potentially refresh an MCN list elsewhere
                            'HX-Trigger': 'refreshMaterialCreditNoteList' 
                        }
                    )
                return self.form_valid(form) # Fallback for non-HTMX requests

            except ValidationError as e:
                # Catch specific validation errors from service layer
                messages.error(request, f"Error creating MCN: {e.message}")
                return self.form_invalid(form, formset)
            except Exception as e:
                messages.error(request, f"An unexpected error occurred: {e}")
                return self.form_invalid(form, formset)
        else:
            messages.error(request, "Please correct the errors in the form.")
            return self.form_invalid(form, formset)

    def form_invalid(self, form, formset):
        # Re-render the template with errors for HTMX and standard requests
        return self.render_to_response(self.get_context_data(form=form, formset=formset))


class MaterialCreditNoteTablePartialView(View):
    # This view will be called via HTMX to load the table content
    # It takes WOId/WONo as query parameters and renders the formset
    def get(self, request, *args, **kwargs):
        wo_id = request.GET.get('WOId')
        wono = request.GET.get('WONo')

        comp_id = self.request.session.get('compid', 1)
        fin_year_id = self.request.session.get('finyear', 2023)

        initial_data = MaterialCreditNoteService.prepare_bom_items_for_mcn_formset(
            wo_no=wono,
            comp_id=comp_id,
            fin_year_id=fin_year_id
        )
        formset = MaterialCreditNoteItemFormSet(initial=initial_data)

        # We pass only the formset to the partial template
        context = {
            'formset': formset,
            'comp_id': comp_id, # Needed for conditional rendering (hide if Diff == 0)
            'fin_year_id': fin_year_id,
            'wo_id': wo_id,
            'wono': wono,
        }
        return self.render_to_response('project_management/materialcreditnote/_mcn_item_table.html', context)


class FileDownloadView(View):
    def get(self, request, item_id, file_type):
        item = get_object_or_404(ItemMaster, id=item_id)

        file_data = None
        file_name = None
        content_type = None

        if file_type == 'drawing':
            # In a real scenario, FileData would be a FileField,
            # or we would retrieve the path from the ItemMaster and serve
            # Assuming files are stored on disk as per ASP.NET's DownloadFile.aspx behavior
            # which likely reads binary data from DB or serves from path
            # For `managed = False`, we assume `FileData` and `AttData` columns exist and contain bytes.
            
            # This is a placeholder. You'd need to retrieve actual file data from the DB or disk.
            # Example:
            # from django.db import connection
            # with connection.cursor() as cursor:
            #     cursor.execute("SELECT FileData, ContentType FROM tblDG_Item_Master WHERE Id = %s", [item_id])
            #     row = cursor.fetchone()
            #     if row:
            #         file_data = row[0]
            #         content_type = row[1]
            #     else:
            #         raise Http404("File not found")
            # file_name = item.file_name

            # For demonstration, let's assume files are stored in MEDIA_ROOT/drawings or MEDIA_ROOT/specs
            # and ItemMaster.file_name and .att_name store just the filename.
            
            if item.file_name:
                file_path = os.path.join(settings.MEDIA_ROOT, 'drawings', item.file_name)
                if os.path.exists(file_path):
                    file_data = open(file_path, 'rb').read() # Read binary data
                    file_name = item.file_name
                    content_type = 'application/octet-stream' # Or retrieve from DB if stored
                else:
                    raise Http404("Drawing file not found on disk.")
            else:
                raise Http404("No drawing file associated with this item.")


        elif file_type == 'spec_sheet':
            if item.att_name:
                file_path = os.path.join(settings.MEDIA_ROOT, 'specs', item.att_name)
                if os.path.exists(file_path):
                    file_data = open(file_path, 'rb').read()
                    file_name = item.att_name
                    content_type = 'application/octet-stream' # Or retrieve from DB if stored
                else:
                    raise Http404("Specification sheet file not found on disk.")
            else:
                raise Http404("No specification sheet associated with this item.")
        else:
            raise Http404("Invalid file type.")

        if file_data:
            response = FileResponse(file_data, content_type=content_type)
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            return response
        else:
            raise Http404("File data not available.")

# Service Layer to encapsulate complex business logic and DB queries
# project_management/services.py
class MaterialCreditNoteService:
    @staticmethod
    def get_work_order_details(wo_id, comp_id, fin_year_id):
        # Mimics ASP.NET's fetching of WO, Project, Customer info
        work_order = get_object_or_404(
            WorkOrderMaster, 
            id=wo_id, 
            comp_id=comp_id, 
            fin_year_id__lte=fin_year_id # ASP.NET uses <= finyear
        )
        return {
            'wono': work_order.wono,
            'project_name': work_order.project_name,
            'customer_name_full': work_order.customer_name_full,
        }

    @staticmethod
    def prepare_bom_items_for_mcn_formset(wo_no, comp_id, fin_year_id):
        # Replicates the complex data loading logic from ASP.NET's loaddata()
        # Filters BOM items by WONo and PId=0, then fetches related item/unit data
        # and calculates TotalMCNQty.
        bom_items = BomMaster.objects.filter(
            wono=wo_no, 
            p_id=0, # As per ASP.NET code: And PId='0'
            comp_id=comp_id, 
            fin_year_id__lte=fin_year_id # ASP.NET uses <= finyear
        ).order_by('id') # Order by Id as per ASP.NET

        initial_data = []
        for bom_item in bom_items:
            item = bom_item.item # Use the @property defined in BomMaster
            if not item: continue # Skip if item not found

            total_mcn_qty = bom_item.get_total_mcn_qty(comp_id, fin_year_id)
            
            # Decide if MCNQty textbox should be visible based on remaining quantity
            # This logic is usually handled in the template or form, but we can pass a flag.
            display_mcn_qty_field = (bom_item.qty - total_mcn_qty) > 0

            initial_data.append({
                'bom_id': bom_item.id,
                'item_id': bom_item.item_id,
                'p_id': bom_item.p_id,
                'c_id': bom_item.c_id,
                'bom_qty': bom_item.qty,
                'total_mcn_qty': total_mcn_qty,
                'item_code': item.display_item_code,
                'description': item.manf_desc,
                'uom': item.uom_symbol,
                'mcn_qty': 0 if display_mcn_qty_field else None, # Pre-fill 0 if editable, else None
                '_display_mcn_qty_field': display_mcn_qty_field # Custom flag for template
            })
        return initial_data

    @staticmethod
    @transaction.atomic # Ensures all DB operations are committed or rolled back together
    def create_material_credit_note(main_form_data, detail_formset_data, wo_no, comp_id, fin_year_id, session_id):
        # 1. Create MaterialCreditNote Master record
        mcn_master = MaterialCreditNote(
            # Id is auto-generated by DB if identity is set, otherwise handled by Django
            # For managed=False, if the DB's Id isn't auto-incrementing, you might need a custom ID gen.
            # Assuming DB handles PK auto-increment if not specified
            session_id=session_id,
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            wono=wo_no,
            # mcn_no will be generated in MaterialCreditNote.save()
            # sys_date, sys_time will be set in MaterialCreditNote.save()
        )
        mcn_master.save() # This triggers MCNNo generation and sets date/time

        # 2. Create MaterialCreditNote Details records
        s_count = 0 # Counter for successfully saved detail rows, like ASP.NET's 's'
        for item_data in detail_formset_data:
            mcn_qty = item_data.get('mcn_qty')
            
            # Only create detail if MCNQty is entered and positive after cleaning
            if mcn_qty is not None and mcn_qty > 0:
                MaterialCreditNoteDetail.objects.create(
                    material_credit_note=mcn_master,
                    p_id=item_data['p_id'],
                    c_id=item_data['c_id'],
                    mcn_qty=mcn_qty
                )
                s_count += 1
        
        if s_count == 0:
            # If no detail items were saved, it means the formset was valid but no items
            # met the criteria for MCN creation (e.g., all quantities were 0 or None).
            # This can be a business rule validation.
            raise ValidationError("No valid material quantities were provided for the credit note details.")

```

#### 4.4 Templates (`project_management/templates/project_management/materialcreditnote/`)

We'll have `create.html` for the main page and `_mcn_item_table.html` as an HTMX partial for the grid.

**`create.html`** (Main page template)

```html
{% extends 'core/base.html' %}

{% block title %}Material Credit Note - New{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-4">Material Credit Note [MCN] - New</h2>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-gray-700 mb-6">
            <div>
                <span class="font-semibold">WO No: </span><span id="lblWono">{{ wo_no }}</span>
            </div>
            <div>
                <span class="font-semibold">Project Name: </span><span id="lblProjectTitle">{{ project_title }}</span>
            </div>
            <div>
                <span class="font-semibold">Customer Name: </span><span id="lblCustName">{{ customer_name }}</span>
            </div>
        </div>

        <form hx-post="{% url 'materialcreditnote_new' %}?WOId={{ request.GET.WOId }}&WONo={{ request.GET.WONo }}" 
              hx-swap="none" 
              hx-trigger="submit" 
              id="mcn-form">
            {% csrf_token %}
            
            {# Hidden fields for session/context data #}
            <input type="hidden" name="comp_id" value="{{ comp_id }}">
            <input type="hidden" name="fin_year_id" value="{{ fin_year_id }}">
            <input type="hidden" name="session_id" value="{{ session_id }}">
            <input type="hidden" name="wono" value="{{ wo_no }}"> {# Pass WONo for context in POST #}

            <div id="mcn-item-table-container" 
                 hx-get="{% url 'materialcreditnote_item_table_partial' %}?WOId={{ request.GET.WOId }}&WONo={{ request.GET.WONo }}" 
                 hx-trigger="load" 
                 hx-swap="innerHTML">
                {# Loading indicator while HTMX fetches the table #}
                <div class="flex justify-center items-center h-48">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="ml-3 text-gray-600">Loading Material Credit Note Items...</p>
                </div>
            </div>

            <div class="mt-8 flex justify-center space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md">
                    Submit
                </button>
                <a href="{% url 'materialcreditnote_list' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-md">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Listen for HTMX success event to show toast notification
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 204 && evt.detail.requestConfig.verb === 'post') {
            // Success response for form submission
            // We can show a simple notification here or rely on a global success trigger
            // For a full system, you might have a dedicated toast component
            console.log('MCN submitted successfully!');
            // Simulate redirect by navigating away (replace with actual logic for MCN list)
            window.location.href = "{% url 'materialcreditnote_list' %}"; // Redirect to the MCN list page as in ASP.NET
        } else if (evt.detail.successful && evt.detail.requestConfig.verb === 'post') {
             // Handle partial success where form errors might be returned for display
             // This assumes HTMX swaps the form back with errors.
             console.log('Form submission processed, but not a 204. Likely form errors.');
        }
    });

    // Custom event listener for refreshing the MCN list (if it's on a different page)
    // This assumes the success_url for MCN creation will trigger this if redirected
    document.body.addEventListener('refreshMaterialCreditNoteList', function() {
        console.log('Global event: refreshMaterialCreditNoteList received. Refreshing list...');
        // If an MCN list is on the same page, trigger its HTMX refresh
        // For this scenario, we are redirecting, so this is more for other parts of the app
    });

    // Basic Alpine.js for general UI control (e.g., modals if they were used for form errors)
    document.addEventListener('alpine:init', () => {
        Alpine.data('mcnPage', () => ({
            // Example of a simple modal if validation errors were shown in a modal
            showModal: false,
            openModal() { this.showModal = true; },
            closeModal() { this.showModal = false; }
        }));
    });
</script>
{% endblock %}
```

**`_mcn_item_table.html`** (HTMX partial for the grid)

```html
{% load custom_filters %} {# For optional custom filter if needed, e.g., toFixed #}

<div class="overflow-x-auto border border-gray-200 rounded-md">
    <table id="mcnItemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Draw/Img</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Spec.sheet</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">MCN Qty</th>
                <th class="py-3 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Total MCN Qty</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for form in formset %}
            {% with item_data=form.initial %} {# Accessing the initial data passed to the form #}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm">
                    {% if item_data.has_drawing %}
                        <a href="{% url 'file_download' item_data.item_id 'drawing' %}" class="text-blue-600 hover:underline">View</a>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm">
                    {% if item_data.has_spec_sheet %}
                        <a href="{% url 'file_download' item_data.item_id 'spec_sheet' %}" class="text-blue-600 hover:underline">View</a>
                    {% else %}
                        -
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ item_data.item_code }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-left">{{ item_data.description }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm text-gray-800">{{ item_data.uom }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-800">{{ item_data.bom_qty|floatformat:3 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    {# Hidden fields for formset management and context #}
                    {{ form.bom_id }}
                    {{ form.item_id }}
                    {{ form.p_id }}
                    {{ form.c_id }}
                    {{ form.bom_qty }}
                    {{ form.total_mcn_qty }}
                    {{ form.item_code }}
                    {{ form.description }}
                    {{ form.uom }}

                    {# Only show the input field if remaining quantity is > 0 #}
                    {% if item_data._display_mcn_qty_field %}
                        {{ form.mcn_qty }}
                        {% if form.mcn_qty.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.mcn_qty.errors|join:", " }}</p>
                        {% endif %}
                    {% else %}
                        <span class="text-gray-500">N/A</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-right text-sm text-gray-800">{{ item_data.total_mcn_qty|floatformat:3 }}</td>
            </tr>
            {% endwith %}
            {% endfor %}
            {% if not formset.forms %}
                <tr>
                    <td colspan="9" class="py-4 text-center text-gray-500">No data found to display.</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

{# Display formset errors, if any (e.g., "No valid quantities entered") #}
{% if formset.non_form_errors %}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mt-4" role="alert">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline">{{ formset.non_form_errors }}</span>
    </div>
{% endif %}

<script>
    // Initialize DataTables after content is loaded via HTMX
    // Ensure jQuery is loaded for DataTables to work
    if (typeof jQuery !== 'undefined' && typeof jQuery.fn.DataTable !== 'undefined') {
        $(document).ready(function() {
            // Destroy existing DataTable instance if it exists to avoid re-initialization errors
            if ($.fn.DataTable.isDataTable('#mcnItemTable')) {
                $('#mcnItemTable').DataTable().destroy();
            }
            $('#mcnItemTable').DataTable({
                "paging": true,
                "searching": true,
                "info": true,
                "order": [], // Disable initial ordering
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, 1, 2, 7] } // Disable sorting for SN, Draw/Img, Spec.sheet, MCN Qty
                ]
            });
        });
    } else {
        console.warn("jQuery or DataTables not loaded. Please ensure CDN links are in base.html.");
    }
</script>

```
**Note for Templates:**
*   You will need to ensure jQuery and DataTables CDN links are correctly included in your `core/base.html` template.
*   The `floatformat:3` filter is standard Django.
*   `item_data.has_drawing` and `item_data.has_spec_sheet` assume you've added properties or methods to your `ItemMaster` model to check for file existence, replicating the ASP.NET `!string.IsNullOrEmpty` check. (Added in `ItemMaster` model in section 4.1).

#### 4.5 URLs (`project_management/urls.py`)

Define the URL patterns for accessing the Material Credit Note creation page, the HTMX-loaded table, and file downloads.

```python
from django.urls import path
from .views import MaterialCreditNoteCreateView, MaterialCreditNoteTablePartialView, FileDownloadView

urlpatterns = [
    # Main page for creating a new Material Credit Note
    path('materialcreditnote/new/', MaterialCreditNoteCreateView.as_view(), name='materialcreditnote_new'),
    
    # HTMX endpoint to load the table of BOM items with MCN quantity inputs
    path('materialcreditnote/new/items/', MaterialCreditNoteTablePartialView.as_view(), name='materialcreditnote_item_table_partial'),

    # URL for downloading drawing files (Draw/Img)
    path('materialcreditnote/download/<int:item_id>/drawing/', FileDownloadView.as_view(), {'file_type': 'drawing'}, name='file_download_drawing'),
    
    # URL for downloading specification sheets (Spec.sheet)
    path('materialcreditnote/download/<int:item_id>/spec_sheet/', FileDownloadView.as_view(), {'file_type': 'spec_sheet'}, name='file_download_spec_sheet'),
    
    # Placeholder for the main MCN list page where successful creation redirects
    path('materialcreditnote/list/', MaterialCreditNoteCreateView.as_view(), name='materialcreditnote_list'), # Replace with actual ListView later
]

```

#### 4.6 Tests (`project_management/tests.py`)

Comprehensive tests for models, services, and views are crucial for maintainability and correctness.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import (
    MaterialCreditNote, MaterialCreditNoteDetail, BomMaster, 
    ItemMaster, UnitMaster, WorkOrderMaster, CustomerMaster
)
from .forms import MaterialCreditNoteItemForm, BaseMaterialCreditNoteItemFormSet
from .services import MaterialCreditNoteService

class MaterialCreditNoteModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models to ensure lookups work
        UnitMaster.objects.create(id=1, symbol='PC')
        ItemMaster.objects.create(id=101, item_code='ITEM001', part_no='PART001', 
                                  manf_desc='Test Item Desc', uom_basic_id=1,
                                  file_name='drawing.pdf', att_name='spec.pdf')
        ItemMaster.objects.create(id=102, item_code='ITEM002', part_no='PART002', 
                                  manf_desc='Another Item', uom_basic_id=1)
        WorkOrderMaster.objects.create(id=1, wono='WO001', task_project_title='Project Alpha', 
                                       customer_id=100, comp_id=1, fin_year_id=2023)
        CustomerMaster.objects.create(customer_id=100, customer_name='Test Customer')
        BomMaster.objects.create(id=1, p_id=0, c_id=0, wono='WO001', item_id=101, qty=Decimal('10.000'), comp_id=1, fin_year_id=2023)
        BomMaster.objects.create(id=2, p_id=0, c_id=0, wono='WO001', item_id=102, qty=Decimal('5.000'), comp_id=1, fin_year_id=2023)

        # Create an initial MCN for MCNNo generation test
        MaterialCreditNote.objects.create(
            id=1, mcn_no='0001', wono='WO_OLD', comp_id=1, fin_year_id=2022,
            sys_date=timezone.now().date(), sys_time=timezone.now().time(), session_id='test_session_old'
        )

    def test_material_credit_note_creation_and_mcn_no_generation(self):
        mcn = MaterialCreditNote.objects.create(
            wono='WO001', comp_id=1, fin_year_id=2023, session_id='test_session_id'
        )
        self.assertIsNotNone(mcn.pk)
        self.assertEqual(mcn.mcn_no, '0001') # First MCN for 2023, so starts at 0001
        
        mcn2 = MaterialCreditNote.objects.create(
            wono='WO002', comp_id=1, fin_year_id=2023, session_id='test_session_id_2'
        )
        self.assertEqual(mcn2.mcn_no, '0002') # Next MCN for 2023

    def test_material_credit_note_detail_creation(self):
        mcn = MaterialCreditNote.objects.create(
            wono='WO001', comp_id=1, fin_year_id=2023, session_id='test_session_id'
        )
        detail = MaterialCreditNoteDetail.objects.create(
            material_credit_note=mcn, p_id=0, c_id=0, mcn_qty=Decimal('2.500')
        )
        self.assertEqual(detail.material_credit_note, mcn)
        self.assertEqual(detail.mcn_qty, Decimal('2.500'))
    
    def test_bom_master_get_total_mcn_qty(self):
        mcn = MaterialCreditNote.objects.create(wono='WO001', comp_id=1, fin_year_id=2023, session_id='s1')
        MaterialCreditNoteDetail.objects.create(material_credit_note=mcn, p_id=0, c_id=0, mcn_qty=Decimal('1.000'))
        
        bom_item = BomMaster.objects.get(id=1) # Item ID 101, PId=0, CId=0
        total_mcn_qty = bom_item.get_total_mcn_qty(comp_id=1, fin_year_id=2023)
        self.assertEqual(total_mcn_qty, 1.0)
        
        # Test with multiple MCNs for the same BOM item
        mcn2 = MaterialCreditNote.objects.create(wono='WO001', comp_id=1, fin_year_id=2023, session_id='s2')
        MaterialCreditNoteDetail.objects.create(material_credit_note=mcn2, p_id=0, c_id=0, mcn_qty=Decimal('0.500'))
        total_mcn_qty_after_second = bom_item.get_total_mcn_qty(comp_id=1, fin_year_id=2023)
        self.assertEqual(total_mcn_qty_after_second, 1.5)

    def test_item_master_properties(self):
        item1 = ItemMaster.objects.get(id=101) # Has file_name and att_name
        self.assertEqual(item1.display_item_code, 'ITEM001')
        self.assertEqual(item1.uom_symbol, 'PC')
        self.assertTrue(item1.has_drawing)
        self.assertTrue(item1.has_spec_sheet)

        item2 = ItemMaster.objects.get(id=102) # No file_name or att_name
        self.assertEqual(item2.display_item_code, 'ITEM002')
        self.assertEqual(item2.uom_symbol, 'PC')
        self.assertFalse(item2.has_drawing)
        self.assertFalse(item2.has_spec_sheet)


class MaterialCreditNoteFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models
        UnitMaster.objects.create(id=1, symbol='PC')
        ItemMaster.objects.create(id=101, item_code='ITEM001', manf_desc='Test Item Desc', uom_basic_id=1)
        BomMaster.objects.create(id=1, p_id=0, c_id=0, wono='WO001', item_id=101, qty=Decimal('10.000'), comp_id=1, fin_year_id=2023)

    def test_material_credit_note_item_form_valid(self):
        data = {
            'bom_id': 1, 'item_id': 101, 'p_id': 0, 'c_id': 0,
            'bom_qty': Decimal('10.000'), 'total_mcn_qty': Decimal('2.000'),
            'mcn_qty': '5.000' # String input to mimic form
        }
        form = MaterialCreditNoteItemForm(data=data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['mcn_qty'], Decimal('5.000'))

    def test_material_credit_note_item_form_invalid_qty_format(self):
        data = {
            'bom_id': 1, 'item_id': 101, 'p_id': 0, 'c_id': 0,
            'bom_qty': Decimal('10.000'), 'total_mcn_qty': Decimal('2.000'),
            'mcn_qty': 'abc'
        }
        form = MaterialCreditNoteItemForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('mcn_qty', form.errors)
        self.assertIn("Invalid quantity format.", form.errors['mcn_qty'][0])

    def test_material_credit_note_item_form_mcn_qty_exceeds_available(self):
        data = {
            'bom_id': 1, 'item_id': 101, 'p_id': 0, 'c_id': 0,
            'bom_qty': Decimal('10.000'), 'total_mcn_qty': Decimal('9.000'), # Only 1.000 available
            'mcn_qty': '2.000' # Tries to enter 2, but only 1 is left
        }
        form = MaterialCreditNoteItemForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('mcn_qty', form.errors)
        self.assertIn("MCN Quantity (2.000) cannot exceed remaining BOM quantity (1.000).", form.errors['mcn_qty'][0])
    
    def test_base_material_credit_note_item_formset_no_items_entered(self):
        # All forms are empty or invalid
        formset = BaseMaterialCreditNoteItemFormSet(data={
            'form-TOTAL_FORMS': '1', 'form-INITIAL_FORMS': '1', 'form-MIN_NUM_FORMS': '0', 'form-MAX_NUM_FORMS': '',
            'form-0-mcn_qty': '', # Empty quantity
            'form-0-bom_id': '1', 'form-0-item_id': '101', 'form-0-p_id': '0', 'form-0-c_id': '0',
            'form-0-bom_qty': '10.000', 'form-0-total_mcn_qty': '0.000',
        })
        self.assertFalse(formset.is_valid())
        self.assertIn("No valid Material Credit Note quantities entered. Please enter at least one positive quantity.", formset.non_form_errors)

    def test_base_material_credit_note_item_formset_some_items_entered(self):
        formset = BaseMaterialCreditNoteItemFormSet(data={
            'form-TOTAL_FORMS': '2', 'form-INITIAL_FORMS': '2', 'form-MIN_NUM_FORMS': '0', 'form-MAX_NUM_FORMS': '',
            'form-0-mcn_qty': '1.000',
            'form-0-bom_id': '1', 'form-0-item_id': '101', 'form-0-p_id': '0', 'form-0-c_id': '0',
            'form-0-bom_qty': '10.000', 'form-0-total_mcn_qty': '0.000',
            'form-1-mcn_qty': '', # Empty quantity
            'form-1-bom_id': '2', 'form-1-item_id': '102', 'form-1-p_id': '0', 'form-1-c_id': '0',
            'form-1-bom_qty': '5.000', 'form-1-total_mcn_qty': '0.000',
        })
        self.assertTrue(formset.is_valid())

class MaterialCreditNoteServiceTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        UnitMaster.objects.create(id=1, symbol='PC')
        ItemMaster.objects.create(id=101, item_code='ITEM001', manf_desc='Test Item Desc', uom_basic_id=1)
        ItemMaster.objects.create(id=102, item_code='ITEM002', manf_desc='Another Item', uom_basic_id=1)
        WorkOrderMaster.objects.create(id=1, wono='WO001', task_project_title='Project Alpha', customer_id=100, comp_id=1, fin_year_id=2023)
        CustomerMaster.objects.create(customer_id=100, customer_name='Test Customer')
        BomMaster.objects.create(id=1, p_id=0, c_id=0, wono='WO001', item_id=101, qty=Decimal('10.000'), comp_id=1, fin_year_id=2023)
        BomMaster.objects.create(id=2, p_id=0, c_id=0, wono='WO001', item_id=102, qty=Decimal('5.000'), comp_id=1, fin_year_id=2023)
    
    def test_prepare_bom_items_for_mcn_formset(self):
        initial_data = MaterialCreditNoteService.prepare_bom_items_for_mcn_formset(
            wo_no='WO001', comp_id=1, fin_year_id=2023
        )
        self.assertEqual(len(initial_data), 2)
        self.assertEqual(initial_data[0]['item_code'], 'ITEM001')
        self.assertEqual(initial_data[0]['bom_qty'], Decimal('10.000'))
        self.assertEqual(initial_data[0]['total_mcn_qty'], 0.0) # No MCNs yet
        self.assertEqual(initial_data[0]['mcn_qty'], 0) # Should be 0 for new input

    def test_create_material_credit_note_success(self):
        main_data = {} # No direct fields on main form for this page
        detail_data = [
            {'bom_id': 1, 'item_id': 101, 'p_id': 0, 'c_id': 0, 'bom_qty': Decimal('10.000'), 'total_mcn_qty': Decimal('0.000'), 'mcn_qty': Decimal('2.000')},
            {'bom_id': 2, 'item_id': 102, 'p_id': 0, 'c_id': 0, 'bom_qty': Decimal('5.000'), 'total_mcn_qty': Decimal('0.000'), 'mcn_qty': Decimal('1.000')},
        ]
        
        MaterialCreditNoteService.create_material_credit_note(
            main_data, detail_data, 'WO001', 1, 2023, 'test_session'
        )
        
        mcn = MaterialCreditNote.objects.get(wono='WO001', comp_id=1, fin_year_id=2023)
        self.assertIsNotNone(mcn)
        self.assertEqual(mcn.details.count(), 2)
        self.assertEqual(mcn.details.first().mcn_qty, Decimal('2.000'))

    def test_create_material_credit_note_no_details_entered(self):
        main_data = {}
        detail_data = [
            {'bom_id': 1, 'item_id': 101, 'p_id': 0, 'c_id': 0, 'bom_qty': Decimal('10.000'), 'total_mcn_qty': Decimal('0.000'), 'mcn_qty': None}, # No qty
        ]
        
        with self.assertRaises(ValidationError) as cm:
            MaterialCreditNoteService.create_material_credit_note(
                main_data, detail_data, 'WO001', 1, 2023, 'test_session'
            )
        self.assertIn("No valid material quantities were provided for the credit note details.", str(cm.exception))


class MaterialCreditNoteViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Setup similar to model tests to ensure data is available for views
        UnitMaster.objects.create(id=1, symbol='PC')
        ItemMaster.objects.create(id=101, item_code='ITEM001', manf_desc='Test Item Desc', uom_basic_id=1, file_name='drawing.pdf', att_name='spec.pdf')
        ItemMaster.objects.create(id=102, item_code='ITEM002', manf_desc='Another Item', uom_basic_id=1)
        WorkOrderMaster.objects.create(id=1, wono='WO001', task_project_title='Project Alpha', customer_id=100, comp_id=1, fin_year_id=2023)
        CustomerMaster.objects.create(customer_id=100, customer_name='Test Customer')
        BomMaster.objects.create(id=1, p_id=0, c_id=0, wono='WO001', item_id=101, qty=Decimal('10.000'), comp_id=1, fin_year_id=2023)
        BomMaster.objects.create(id=2, p_id=0, c_id=0, wono='WO001', item_id=102, qty=Decimal('5.000'), comp_id=1, fin_year_id=2023)
        
        # Simulate session data
        cls.session_data = {'compid': 1, 'finyear': 2023}

    def test_material_credit_note_create_view_get(self):
        # Set session data
        session = self.client.session
        session.update(self.session_data)
        session.save()

        response = self.client.get(reverse('materialcreditnote_new'), {'WOId': 1, 'WONo': 'WO001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnote/create.html')
        self.assertIn('wo_no', response.context)
        self.assertIn('project_title', response.context)
        self.assertIn('customer_name', response.context)
        self.assertEqual(response.context['wo_no'], 'WO001')
        self.assertContains(response, 'Material Credit Note [MCN] - New')
        self.assertContains(response, 'id="mcn-item-table-container"') # Check if HTMX container is present

    @patch('project_management.services.MaterialCreditNoteService.create_material_credit_note')
    def test_material_credit_note_create_view_post_success(self, mock_create_mcn):
        # Set session data
        session = self.client.session
        session.update(self.session_data)
        session.save()
        
        post_data = {
            'comp_id': '1', 'fin_year_id': '2023', 'session_id': 'test_session', 'wono': 'WO001',
            'form-TOTAL_FORMS': '2', 'form-INITIAL_FORMS': '2', 'form-MIN_NUM_FORMS': '0', 'form-MAX_NUM_FORMS': '',
            'form-0-bom_id': '1', 'form-0-item_id': '101', 'form-0-p_id': '0', 'form-0-c_id': '0',
            'form-0-bom_qty': '10.000', 'form-0-total_mcn_qty': '0.000', 'form-0-mcn_qty': '2.000',
            'form-1-bom_id': '2', 'form-1-item_id': '102', 'form-1-p_id': '0', 'form-1-c_id': '0',
            'form-1-bom_qty': '5.000', 'form-1-total_mcn_qty': '0.000', 'form-1-mcn_qty': '1.000',
        }
        
        response = self.client.post(reverse('materialcreditnote_new') + '?WOId=1&WONo=WO001', post_data, HTTP_HX_REQUEST='true')
        
        # Expect 204 No Content for HTMX success
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshMaterialCreditNoteList')
        mock_create_mcn.assert_called_once()
        
    def test_material_credit_note_create_view_post_invalid_formset(self):
        # Set session data
        session = self.client.session
        session.update(self.session_data)
        session.save()

        post_data = {
            'comp_id': '1', 'fin_year_id': '2023', 'session_id': 'test_session', 'wono': 'WO001',
            'form-TOTAL_FORMS': '1', 'form-INITIAL_FORMS': '1', 'form-MIN_NUM_FORMS': '0', 'form-MAX_NUM_FORMS': '',
            'form-0-bom_id': '1', 'form-0-item_id': '101', 'form-0-p_id': '0', 'form-0-c_id': '0',
            'form-0-bom_qty': '10.000', 'form-0-total_mcn_qty': '0.000',
            'form-0-mcn_qty': 'invalid_qty' # Invalid quantity
        }
        
        response = self.client.post(reverse('materialcreditnote_new') + '?WOId=1&WONo=WO001', post_data, HTTP_HX_REQUEST='true')
        
        # Expect 200 OK because the form is re-rendered with errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnote/create.html')
        self.assertContains(response, 'Invalid quantity format.') # Check for error message

    def test_material_credit_note_table_partial_view(self):
        # Set session data
        session = self.client.session
        session.update(self.session_data)
        session.save()

        response = self.client.get(reverse('materialcreditnote_item_table_partial'), {'WOId': 1, 'WONo': 'WO001'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/materialcreditnote/_mcn_item_table.html')
        self.assertContains(response, 'id="mcnItemTable"') # Check if table is rendered
        self.assertContains(response, 'ITEM001') # Check for item data
        self.assertContains(response, 'data: [') # Check for DataTables initialization (if any)

    @patch('project_management.views.os.path.exists')
    @patch('builtins.open', new_callable=MagicMock)
    def test_file_download_view_drawing_success(self, mock_open, mock_exists):
        mock_exists.return_value = True
        mock_open.return_value.__enter__.return_value.read.return_value = b'test drawing data'
        
        response = self.client.get(reverse('file_download_drawing', args=[101]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/octet-stream')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="drawing.pdf"')
        self.assertEqual(response.content, b'test drawing data')

    def test_file_download_view_drawing_not_found(self):
        response = self.client.get(reverse('file_download_drawing', args=[999])) # Non-existent item
        self.assertEqual(response.status_code, 404)

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content:**
    *   The `mcn-item-table-container` div in `create.html` uses `hx-get` and `hx-trigger="load"` to asynchronously fetch `_mcn_item_table.html` when the page loads. This mimics the `loaddata()` call from ASP.NET.
    *   The main form in `create.html` uses `hx-post`, `hx-swap="none"`, and `hx-trigger="submit"` for submitting the form data without a full page refresh. `hx-swap="none"` indicates that the client-side will handle the UI update (e.g., redirect or show a message) based on `HX-Trigger` headers.
    *   On successful form submission, the view returns a `204 No Content` status with an `HX-Trigger` header (`refreshMaterialCreditNoteList`). This tells HTMX to dispatch a custom event on the client, which can be listened to by other HTMX elements or Alpine.js components (though for this specific scenario, we're simply redirecting after success).
*   **Alpine.js for UI State:**
    *   A basic `Alpine.data` object `mcnPage` is provided in `create.html`'s `extra_js` block. While not heavily utilized for this specific page (as HTMX handles most dynamic changes), it demonstrates how Alpine.js would be integrated for client-side reactivity (e.g., managing modal visibility for error messages or loading states).
*   **DataTables for List Views:**
    *   The `_mcn_item_table.html` partial includes a `<script>` block that initializes DataTables on the `#mcnItemTable` element immediately after the HTMX content is loaded into the DOM. This provides client-side sorting, searching, and pagination as in the original `GridView`.

### Final Notes

*   **Session Management:** The ASP.NET code relies on `Session["username"]`, `Session["compid"]`, `Session["finyear"]`. In Django, these would typically be handled by Django's built-in session framework, user authentication (e.g., `request.user`), or custom middleware for `CompId` and `FinYearId`. For this conversion, we've used `request.session.get()` as a placeholder.
*   **File Storage:** The `DownloadFile.aspx` implies files are either stored in the database or on the file system with paths/metadata in the database. The `FileDownloadView` demonstrates serving from the file system, which is generally more performant. You'd configure `MEDIA_ROOT` and `MEDIA_URL` in your Django `settings.py` for this.
*   **Error Handling:** The ASP.NET `try-catch` blocks are replaced by Django's exception handling within views and forms, with `messages.error` for user feedback. The service layer uses `transaction.atomic` for database integrity.
*   **Refinement:** This plan provides a solid foundation. Further refinements would include:
    *   Robust user authentication and authorization.
    *   More sophisticated handling of `CompId` and `FinYearId` (e.g., per-user settings or context processors).
    *   A dedicated `MaterialCreditNoteListView` for the `success_url` redirect.
    *   Potentially moving some display logic for "Draw/Img" and "Spec.sheet" availability directly into the `ItemMaster` model as properties for cleaner templates.
    *   For `managed=False` models, defining `db_index=True` for frequently queried fields like `WONo`, `CompId`, `FinYearId`, `ItemId` where appropriate for performance.