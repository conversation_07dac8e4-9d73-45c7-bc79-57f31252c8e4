## ASP.NET to Django Conversion Script: Man Power Planning Module

This document outlines a comprehensive modernization plan to transition your existing ASP.NET Man Power Planning module to a modern Django-based solution. Our approach prioritizes automation, leverages contemporary web technologies like Django 5.0+, HTMX, and Alpine.js, and adheres to strict best practices to ensure a scalable, maintainable, and highly performant application.

The current ASP.NET page, `ManPowerPlanning_Edit.aspx`, appears to be an interface for searching, selecting, and updating manpower planning records, including a detailed amendment process. While its title suggests "Delete," the functionality points strongly towards "Edit" and "Update." We will proceed with the latter interpretation.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
From the ASP.NET code-behind, we infer the following primary tables and their relationships:

*   **`tblPM_ManPowerPlanning` (Master Records):**
    *   `Id` (Primary Key, INT)
    *   `SysDate` (DATETIME)
    *   `SysTime` (NVARCHAR)
    *   `SessionId` (NVARCHAR)
    *   `CompId` (INT)
    *   `FinYearId` (INT)
    *   `EmpId` (INT, Foreign Key to `tblHR_OfficeStaff.EmpId`)
    *   `Date` (DATETIME)
    *   `WONo` (NVARCHAR)
    *   `Dept` (INT, Foreign Key to `BusinessGroup.Id`)
    *   `Types` (INT, Represents 1=Present, 2=Absent, 3=Onsite, 4=PL)
    *   `AmendmentNo` (INT)

*   **`tblPM_ManPowerPlanning_Details` (Detail Records):**
    *   `Id` (Primary Key, INT)
    *   `MId` (INT, Foreign Key to `tblPM_ManPowerPlanning.Id`)
    *   `EquipId` (INT, Foreign Key to `tblDG_Item_Master.Id`)
    *   `Category` (INT, Foreign Key to `tblMIS_BudgetHrs_Field_Category.Id`)
    *   `SubCategory` (INT, Foreign Key to `tblMIS_BudgetHrs_Field_SubCategory.Id`)
    *   `PlannedDesc` (NVARCHAR)
    *   `ActualDesc` (NVARCHAR)
    *   `Hour` (FLOAT)

**Auxiliary/Lookup Tables (essential for data display and filtering):**

*   **`tblFinancial_master`:** `CompId`, `FinYearId`, `FinYearFrom`, `FinYearTo`
*   **`tblHR_OfficeStaff`:** `EmpId`, `OfferId`, `Title`, `EmployeeName`, `Designation` (FK to `tblHR_Designation.Id`), `BGGroup` (FK to `BusinessGroup.Id`), `CompId`
*   **`tblHR_Designation`:** `Id`, `Symbol`
*   **`BusinessGroup`:** `Id`, `Symbol`
*   **`tblMIS_BudgetHrs_Field_Category`:** `Id`, `Category`
*   **`tblMIS_BudgetHrs_Field_SubCategory`:** `Id`, `SubCategory`, `MId` (FK to `tblMIS_BudgetHrs_Field_Category.Id`)
*   **`tblDG_Item_Master`:** `Id`, `ItemCode`, `ManfDesc`
*   **`tblPM_ManPowerPlanning_Amd` (Amendment Log for Master):** Mirror of `tblPM_ManPowerPlanning` with an additional `MId` (original record ID).
*   **`tblPM_ManPowerPlanning_Details_Amd` (Amendment Log for Details):** Mirror of `tblPM_ManPowerPlanning_Details` with `MId` (FK to `tblPM_ManPowerPlanning_Amd.Id`) and `DMId` (original detail record ID).

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and business rules from the ASP.NET code-behind.

**Instructions:**

*   **Read (Search & Display):**
    *   Retrieves `ManPowerPlanning` records based on filters: Employee Name, Month, Business Group (BG) or Work Order Number (WONo), Type (Present, Absent, Onsite, PL), and Date Range (From/To Date).
    *   Populates a "Master" grid (`GridView2`) with summarized information (Employee Name, Designation, Date, WONo, BG, Type).
    *   Upon selection of a "Master" record, retrieves and displays its associated `ManPowerPlanning_Details` in a "Detail" grid (`GridView3`).
    *   Employee Name search uses an auto-complete feature.
    *   Month dropdown is dynamically populated based on financial year.
*   **Update (Edit & Amend):**
    *   Allows editing `PlannedDesc`, `ActualDesc`, and `Hour` for selected detail records within `GridView3`.
    *   Client-side toggling of input fields based on a checkbox.
    *   Server-side validation for updated fields (required, numeric, hours within range).
    *   **Crucial Business Logic (Amendment):** When a `ManPowerPlanning` record (via its `ManPowerPlanning_Details`) is updated:
        1.  The original `ManPowerPlanning` record is copied to `tblPM_ManPowerPlanning_Amd`.
        2.  The original `ManPowerPlanning_Details` records related to the updated master are copied to `tblPM_ManPowerPlanning_Details_Amd`.
        3.  The `SysDate`, `SysTime`, `SessionId`, and `AmendmentNo` fields of the original `tblPM_ManPowerPlanning` record are updated.
        4.  The `PlannedDesc`, `ActualDesc`, and `Hour` fields of the original `tblPM_ManPowerPlanning_Details` records are updated.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, translating them to Django/HTMX/Alpine.js.

**Instructions:**

*   **Search/Filter Section:**
    *   `ddlSelectBG_WONo` (Dropdown): Will be a Django `forms.ChoiceField` rendered as `<select>`. Its `AutoPostBack` behavior will be replaced by HTMX `hx-post` or `hx-get` to dynamically update the visibility of `TxtWONo` or `DrpCategory` (or just toggle visibility with Alpine.js based on selected value).
    *   `TxtWONo` (TextBox): Django `forms.CharField` rendered as `<input type="text">`.
    *   `DrpCategory` (Dropdown): Django `forms.ModelChoiceField` for `BusinessGroup` model.
    *   `DrpMonths` (Dropdown): Django `forms.ChoiceField`, populated dynamically in the search form's `__init__`.
    *   `Drptype` (Dropdown): Django `forms.ChoiceField` for Man Power Types.
    *   `Txtfromdate`, `TxtTodate` (TextBoxes with CalendarExtender): Django `forms.DateField` rendered as `<input type="date">`.
    *   `TxtEmpName` (TextBox with AutoCompleteExtender): Django `forms.CharField` rendered as `<input type="text">` with HTMX `hx-get` to a dedicated autocomplete endpoint and `datalist` or a more advanced HTMX/Alpine.js driven custom autocomplete.
    *   `BtnSearch` (Button): Replaced by `hx-get` or `hx-post` on the form to trigger search and partial table refresh.
*   **Master Data Grid (`GridView2`):**
    *   Will be a standard HTML `<table>` wrapped in a DataTables JavaScript initialization.
    *   "Select" `LinkButton`: Replaced by an HTMX-enabled button that triggers a `hx-get` to load the detail grid based on the selected master record's ID.
*   **Detail Data Grid (`GridView3`):**
    *   Will be an HTML `<table>` wrapped in a DataTables JavaScript initialization.
    *   `CheckBox (ck)`: Each row's checkbox will be managed by Alpine.js to dynamically toggle visibility of `Planned`, `Actual`, `Hours` labels/textboxes for that specific row.
    *   `Planned`, `Actual`, `Hours` (Labels and TextBoxes with Validators): Rendered as both `<span>` (for view mode) and `<input type="text">` (for edit mode), with Alpine.js `x-show` to control visibility. Validation (RequiredFieldValidator, RegularExpressionValidator) will be handled by Django forms on submit and potentially client-side with Alpine.js.
*   **`btnUpdate` (Button):** Will be an HTMX-enabled `hx-post` button to submit the form containing all modified detail rows.

---

### Step 4: Generate Django Code

We will create a new Django application, `manpower_planning`, to house this module.

#### 4.1 Models (`manpower_planning/models.py`)

This file will define the Django ORM models, mapping to your existing database tables. The core business logic, including the amendment process, will be implemented here.

```python
from django.db import models, transaction
from django.utils import timezone
import datetime

# --- Lookup Models (Essential for relationships and data display) ---

class FinancialMaster(models.Model):
    # Assuming these fields are enough for fetching financial year range
    compid = models.IntegerField(db_column='CompId', primary_key=True) # Assuming CompId is part of a composite PK or unique
    finyearid = models.IntegerField(db_column='FinYearId')
    finyearfrom = models.DateField(db_column='FinYearFrom')
    finyearto = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        unique_together = (('compid', 'finyearid'),) # Assuming compid, finyearid form a unique key
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return f"FY {self.finyearfrom.year}-{self.finyearto.year}"

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or 'N/A'

class Designation(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_Designation'
        verbose_name = 'Designation'
        verbose_name_plural = 'Designations'

    def __str__(self):
        return self.symbol or 'N/A'

class OfficeStaffManager(models.Manager):
    def get_employee_id_from_name_with_id(self, full_name_str):
        # Extracts employee ID from "EmployeeName [EmpId]" format
        if '[' in full_name_str and ']' in full_name_str:
            try:
                emp_id = int(full_name_str.split('[')[-1].replace(']', ''))
                return emp_id
            except ValueError:
                pass
        return None

class OfficeStaff(models.Model):
    objects = OfficeStaffManager() # Custom manager for employee ID extraction
    empid = models.IntegerField(db_column='EmpId', primary_key=True)
    offerid = models.CharField(db_column='OfferId', max_length=50, blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=10, blank=True, null=True)
    employeename = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    designation = models.ForeignKey(Designation, on_delete=models.DO_NOTHING, db_column='Designation', blank=True, null=True)
    bggroup = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BGGroup', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''}. {self.employeename or ''} [{self.empid}]".strip()

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    itemcode = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manfdesc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.itemcode} - {self.manfdesc}"

class BudgetCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_Category'
        verbose_name = 'Budget Category'
        verbose_name_plural = 'Budget Categories'

    def __str__(self):
        return self.category or 'N/A'

class BudgetSubCategory(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    subcategory = models.CharField(db_column='SubCategory', max_length=255, blank=True, null=True)
    mid = models.ForeignKey(BudgetCategory, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetHrs_Field_SubCategory'
        verbose_name = 'Budget Subcategory'
        verbose_name_plural = 'Budget Subcategories'

    def __str__(self):
        return self.subcategory or 'N/A'

# --- Core Man Power Planning Models ---

class ManPowerPlanning(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    sysdate = models.DateField(db_column='SysDate', blank=True, null=True)
    systime = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    sessionid = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    empid = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', blank=True, null=True)
    date = models.DateField(db_column='Date', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    dept = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='Dept', blank=True, null=True)
    types = models.IntegerField(db_column='Types', blank=True, null=True) # 1=Present, 2=Absent, 3=Onsite, 4=PL
    amendmentno = models.IntegerField(db_column='AmendmentNo', blank=True, null=True)

    class ManPowerTypes(models.IntegerChoices):
        NA = 0, 'NA'
        PRESENT = 1, 'Present'
        ABSENT = 2, 'Absent'
        ONSITE = 3, 'Onsite'
        PL = 4, 'PL'

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning'
        verbose_name = 'Man Power Planning'
        verbose_name_plural = 'Man Power Plannings'

    def __str__(self):
        return f"MPP for {self.empid.employeename if self.empid else 'N/A'} on {self.date}"

class ManPowerPlanningDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mid = models.ForeignKey(ManPowerPlanning, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True)
    equipid = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='EquipId', blank=True, null=True)
    category = models.ForeignKey(BudgetCategory, on_delete=models.DO_NOTHING, db_column='Category', blank=True, null=True)
    subcategory = models.ForeignKey(BudgetSubCategory, on_delete=models.DO_NOTHING, db_column='SubCategory', blank=True, null=True)
    planneddesc = models.CharField(db_column='PlannedDesc', max_length=255, blank=True, null=True)
    actualdesc = models.CharField(db_column='ActualDesc', max_length=255, blank=True, null=True)
    hour = models.FloatField(db_column='Hour', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Details'
        verbose_name = 'Man Power Planning Detail'
        verbose_name_plural = 'Man Power Planning Details'

    def __str__(self):
        return f"Detail for {self.mid.id if self.mid else 'N/A'} - {self.equipid.itemcode if self.equipid else 'N/A'}"

# --- Amendment Models (for historical tracking) ---

class ManPowerPlanningAmd(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # This is the PK for the _Amd table itself
    mid = models.ForeignKey(ManPowerPlanning, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True, related_name='amendment_records') # Original ManPowerPlanning Id
    sysdate = models.DateField(db_column='SysDate', blank=True, null=True)
    systime = models.CharField(db_column='SysTime', max_length=50, blank=True, null=True)
    sessionid = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    empid = models.ForeignKey(OfficeStaff, on_delete=models.DO_NOTHING, db_column='EmpId', blank=True, null=True)
    date = models.DateField(db_column='Date', blank=True, null=True)
    wono = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    dept = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='Dept', blank=True, null=True)
    types = models.IntegerField(db_column='Types', blank=True, null=True)
    amendmentno = models.IntegerField(db_column='AmendmentNo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Amd'
        verbose_name = 'Man Power Planning Amendment'
        verbose_name_plural = 'Man Power Planning Amendments'

class ManPowerPlanningDetailAmd(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # PK for this _Amd table
    mid = models.ForeignKey(ManPowerPlanningAmd, on_delete=models.DO_NOTHING, db_column='MId', blank=True, null=True) # FK to ManPowerPlanningAmd
    dmid = models.ForeignKey(ManPowerPlanningDetail, on_delete=models.DO_NOTHING, db_column='DMId', blank=True, null=True, related_name='amendment_records') # Original ManPowerPlanningDetail Id
    equipid = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='EquipId', blank=True, null=True)
    category = models.ForeignKey(BudgetCategory, on_delete=models.DO_NOTHING, db_column='Category', blank=True, null=True)
    subcategory = models.ForeignKey(BudgetSubCategory, on_delete=models.DO_NOTHING, db_column='SubCategory', blank=True, null=True)
    planneddesc = models.CharField(db_column='PlannedDesc', max_length=255, blank=True, null=True)
    actualdesc = models.CharField(db_column='ActualDesc', max_length=255, blank=True, null=True)
    hour = models.FloatField(db_column='Hour', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPM_ManPowerPlanning_Details_Amd'
        verbose_name = 'Man Power Planning Detail Amendment'
        verbose_name_plural = 'Man Power Planning Detail Amendments'


class ManPowerPlanningService:
    """
    A service class to encapsulate the complex amendment and update business logic.
    This keeps the model methods focused on data integrity and the views thin.
    """
    @staticmethod
    @transaction.atomic
    def process_detail_updates(master_planning_id, detail_updates_data, session_id, comp_id, fin_year_id):
        """
        Handles the amendment process and updates ManPowerPlanningDetail records.
        :param master_planning_id: The ID of the master ManPowerPlanning record.
        :param detail_updates_data: A list of dictionaries, each containing 'id', 'planned', 'actual', 'hours'
                                    for the ManPowerPlanningDetail records that were checked and modified.
        :param session_id, comp_id, fin_year_id: Contextual data for amendment logging.
        :return: True if successful, False otherwise.
        """
        if not detail_updates_data:
            return True # No details to update

        master_record = ManPowerPlanning.objects.get(id=master_planning_id)

        # 1. Create ManPowerPlanning_Amd record
        current_amendment_no = master_record.amendmentno if master_record.amendmentno is not None else 0
        new_amendment_no = current_amendment_no + 1

        master_amd = ManPowerPlanningAmd.objects.create(
            mid=master_record,
            sysdate=master_record.sysdate,
            systime=master_record.systime,
            sessionid=master_record.sessionid,
            compid=master_record.compid,
            finyearid=master_record.finyearid,
            empid=master_record.empid,
            date=master_record.date,
            wono=master_record.wono,
            dept=master_record.dept,
            types=master_record.types,
            amendmentno=current_amendment_no # Log the *old* amendment number for historical context
        )

        # 2. Update original ManPowerPlanning record with new amendment number and system info
        master_record.sysdate = timezone.now().date()
        master_record.systime = timezone.now().strftime('%H:%M:%S')
        master_record.sessionid = session_id
        master_record.amendmentno = new_amendment_no
        master_record.save(update_fields=['sysdate', 'systime', 'sessionid', 'amendmentno'])

        # 3. Process each detail update
        for detail_data in detail_updates_data:
            detail_id = detail_data.get('id')
            planned = detail_data.get('planned')
            actual = detail_data.get('actual')
            hours = detail_data.get('hours')

            if not all([detail_id, planned is not None, actual is not None, hours is not None]):
                raise ValueError("Missing data for detail update")

            try:
                detail_record = ManPowerPlanningDetail.objects.get(id=detail_id, mid=master_record)

                # Validate hours before proceeding, similar to ASP.NET logic
                if not (0 < float(hours) <= detail_record.hour): # Original ASP.NET checked against lblHrs.Text, which was the existing hour.
                    # This logic was: if `new_hours > 0 && new_hours <= existing_hours`
                    # It's an unusual check (cannot increase hours?). We replicate it.
                    # If this condition is not met, the ASP.NET code would show "Invalid data input".
                    # Here we might raise an error or skip. For now, we raise.
                    raise ValueError(f"Invalid hours for detail ID {detail_id}. Must be > 0 and <= original ({detail_record.hour}).")

                # Create ManPowerPlanning_Details_Amd record
                ManPowerPlanningDetailAmd.objects.create(
                    mid=master_amd, # Link to the new master amendment record
                    dmid=detail_record, # Link to the original detail record
                    equipid=detail_record.equipid,
                    category=detail_record.category,
                    subcategory=detail_record.subcategory,
                    planneddesc=detail_record.planneddesc,
                    actualdesc=detail_record.actualdesc,
                    hour=detail_record.hour
                )

                # Update original ManPowerPlanningDetail record
                detail_record.planneddesc = planned
                detail_record.actualdesc = actual
                detail_record.hour = hours
                detail_record.save(update_fields=['planneddesc', 'actualdesc', 'hour'])

            except ManPowerPlanningDetail.DoesNotExist:
                print(f"Warning: Detail record with ID {detail_id} not found for Master ID {master_planning_id}")
                # Log or handle error as appropriate
            except ValueError as e:
                raise e # Re-raise for view to catch and report

        return True

```

#### 4.2 Forms (`manpower_planning/forms.py`)

This file will contain the Django forms for the search criteria and for updating `ManPowerPlanningDetail` records.

```python
from django import forms
from .models import ManPowerPlanning, ManPowerPlanningDetail, BusinessGroup, OfficeStaff, FinancialMaster

class ManPowerPlanningSearchForm(forms.Form):
    # Mimics ddlSelectBG_WONo options
    CRITERIA_CHOICES = [
        ('0', 'Select WONo or BG Group'),
        ('1', 'BG Group'),
        ('2', 'WONo'),
    ]
    search_criteria = forms.ChoiceField(
        choices=CRITERIA_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/manpowerplanning/toggle_search_fields/', 'hx-trigger': 'change', 'hx-target': '#search_toggle_fields', 'hx-swap': 'outerHTML'})
    )

    # Corresponds to TxtWONo
    wono = forms.CharField(
        max_length=255,
        required=False,
        label="Work Order No.",
        widget=forms.TextInput(attrs={'class': 'box3', 'style': 'display:none;', 'id': 'id_wono_field'})
    )

    # Corresponds to DrpCategory
    bg_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('symbol'), # Fetch all business groups
        required=False,
        label="Business Group",
        empty_label="Select BG Group",
        widget=forms.Select(attrs={'class': 'box3', 'style': 'display:none;', 'id': 'id_bg_group_field'})
    )

    # Corresponds to DrpMonths
    month = forms.ChoiceField(
        required=False,
        label="Month",
        choices=[('0', 'Select')] + [], # Populated dynamically in __init__
        widget=forms.Select(attrs={'class': 'box3'})
    )

    # Corresponds to Drptype
    TYPE_CHOICES = [
        ('0', 'NA'),
        ('1', 'Present'),
        ('2', 'Absent'),
        ('3', 'Onsite'),
        ('4', 'PL'),
    ]
    mpp_type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=False,
        label="Type",
        widget=forms.Select(attrs={'class': 'box3'})
    )

    # Corresponds to Txtfromdate
    from_date = forms.DateField(
        required=False,
        label="From Date",
        widget=forms.DateInput(attrs={'class': 'box3', 'type': 'date'})
    )

    # Corresponds to TxtTodate
    to_date = forms.DateField(
        required=False,
        label="To Date",
        widget=forms.DateInput(attrs={'class': 'box3', 'type': 'date'})
    )

    # Corresponds to TxtEmpName (for autocomplete)
    employee_name = forms.CharField(
        max_length=250,
        required=False,
        label="Emp Name",
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'hx-get': '/manpowerplanning/employee-autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#employee_name_suggestions',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off', # Prevent browser autocomplete
            'list': 'employee_name_datalist' # HTML5 datalist
        })
    )

    def __init__(self, *args, **kwargs):
        comp_id = kwargs.pop('comp_id', None)
        fin_year_id = kwargs.pop('fin_year_id', None)
        super().__init__(*args, **kwargs)

        # Populate DrpMonths dynamically
        months_choices = [('0', 'Select')]
        if comp_id is not None and fin_year_id is not None:
            try:
                financial_year = FinancialMaster.objects.get(compid=comp_id, finyearid=fin_year_id)
                start_date = financial_year.finyearfrom
                end_date = financial_year.finyearto

                current_date = start_date
                while current_date <= end_date:
                    months_choices.append((str(current_date.month), current_date.strftime('%B')))
                    # Move to the next month
                    if current_date.month == 12:
                        current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
                    else:
                        current_date = current_date.replace(month=current_date.month + 1, day=1)
                
                # Remove duplicates if any
                unique_months = []
                seen_values = set()
                for value, label in months_choices:
                    if value not in seen_values:
                        unique_months.append((value, label))
                        seen_values.add(value)
                self.fields['month'].choices = unique_months

            except FinancialMaster.DoesNotExist:
                # Handle case where financial year data is not found
                pass
        self.fields['month'].choices = months_choices

    def clean(self):
        cleaned_data = super().clean()
        search_criteria = cleaned_data.get('search_criteria')
        wono = cleaned_data.get('wono')
        bg_group = cleaned_data.get('bg_group')

        # Mimic ASP.NET validation for BG/WONo selection
        if search_criteria == '1' and not bg_group:
            self.add_error('bg_group', "Please Select BG Group.")
        elif search_criteria == '2' and not wono:
            self.add_error('wono', "Please Enter WONo.")
        
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', "To Date cannot be before From Date.")

        return cleaned_data


class ManPowerPlanningDetailForm(forms.ModelForm):
    # These fields are normally hidden and toggled visible by checkbox
    # They are part of the detail grid and updated in batch
    planned_desc = forms.CharField(
        required=False,
        label="Planned",
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'x-model': 'planned_desc', 'x-show': 'checked', 'name': 'planned_desc', 'data-planned-label': ''})
    )
    actual_desc = forms.CharField(
        required=False,
        label="Actual",
        widget=forms.TextInput(attrs={'class': 'box3 w-full', 'x-model': 'actual_desc', 'x-show': 'checked', 'name': 'actual_desc', 'data-actual-label': ''})
    )
    hour_val = forms.FloatField(
        required=False,
        label="Hours",
        widget=forms.NumberInput(attrs={'class': 'box3 w-full', 'x-model': 'hour_val', 'x-show': 'checked', 'name': 'hour_val', 'step': '0.01', 'data-hours-label': ''})
    )
    # Checkbox to trigger edit mode for the row. This will be managed by Alpine.js.
    # Its value won't be directly submitted with the form but controls which fields are sent.
    edit_checked = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'x-model': 'checked', '@change': 'toggleEdit($event)'})
    )

    # Use a hidden field for the detail ID so we know which row is being updated
    detail_id = forms.IntegerField(widget=forms.HiddenInput())

    class Meta:
        model = ManPowerPlanningDetail
        fields = [] # We're using custom fields for the editable parts

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize custom fields with instance data
        if self.instance and self.instance.pk:
            self.fields['detail_id'].initial = self.instance.pk
            self.fields['planned_desc'].initial = self.instance.planneddesc
            self.fields['actual_desc'].initial = self.instance.actualdesc
            self.fields['hour_val'].initial = self.instance.hour
            # Set data attributes on widgets for Alpine.js to pick up label values
            self.fields['planned_desc'].widget.attrs['data-planned-label'] = self.instance.planneddesc or ''
            self.fields['actual_desc'].widget.attrs['data-actual-label'] = self.instance.actualdesc or ''
            self.fields['hour_val'].widget.attrs['data-hours-label'] = self.instance.hour if self.instance.hour is not None else ''
        else:
            # For new forms (though this is an update-only scenario)
            self.fields['detail_id'].initial = None

    def clean(self):
        cleaned_data = super().clean()
        
        # Only validate if the checkbox was effectively checked and fields are meant to be updated
        # The actual validation comes from the form submission logic in the view
        # or a separate formset if dealing with multiple forms.
        # For simplicity, we'll let the view handle the batch validation loop.
        
        # This form is designed for a single detail row. When batch updating,
        # we'll parse submitted data and create these forms for each row.
        return cleaned_data


# This form will be used to process the entire batch of updates from GridView3
class ManPowerPlanningDetailBatchUpdateForm(forms.Form):
    # This field will be a JSON string or similar structure from HTMX submission
    # representing all modified detail rows.
    updates = forms.CharField(widget=forms.HiddenInput())

    def clean_updates(self):
        updates_json = self.cleaned_data['updates']
        import json
        try:
            updates_data = json.loads(updates_json)
            # You might want to add more validation here for the structure of each update item
            # e.g., ensure 'id', 'planned', 'actual', 'hours' keys exist.
            return updates_data
        except json.JSONDecodeError:
            raise forms.ValidationError("Invalid update data format.")
```

#### 4.3 Views (`manpower_planning/views.py`)

Views will handle the requests, interact with models and forms, and render templates. We use Class-Based Views (CBVs) for clean structure and keep methods concise.

```python
from django.views.generic import ListView, TemplateView, View
from django.shortcuts import render, get_object_or_404
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q, F
from django.utils import timezone
import datetime
import json

from .models import ManPowerPlanning, ManPowerPlanningDetail, OfficeStaff, BusinessGroup, ManPowerPlanningService
from .forms import ManPowerPlanningSearchForm, ManPowerPlanningDetailBatchUpdateForm

# Helper to get session info (CompId, FinYearId)
def get_session_context(request):
    # Replace with your actual session variable names
    comp_id = request.session.get('compid', 1)  # Default to 1 if not found
    fin_year_id = request.session.get('finyear', 1) # Default to 1 if not found
    session_id = request.session.get('username', 'anonymous')
    return comp_id, fin_year_id, session_id

class ManPowerPlanningListView(TemplateView):
    """
    Main view for Man Power Planning. Renders the search form and containers
    for the master and detail tables.
    """
    template_name = 'manpower_planning/manpower_planning_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        comp_id, fin_year_id, _ = get_session_context(self.request)
        context['search_form'] = ManPowerPlanningSearchForm(comp_id=comp_id, fin_year_id=fin_year_id)
        return context

class ManPowerPlanningTablePartialView(ListView):
    """
    HTMX endpoint to render the master Man Power Planning list (GridView2 equivalent).
    This view is responsible for applying search filters.
    """
    model = ManPowerPlanning
    template_name = 'manpower_planning/_manpower_planning_table.html'
    context_object_name = 'manpower_plannings'

    def get_queryset(self):
        queryset = super().get_queryset()
        comp_id, _, _ = get_session_context(self.request)
        queryset = queryset.filter(compid=comp_id) # Filter by CompId

        form = ManPowerPlanningSearchForm(self.request.GET, comp_id=comp_id, fin_year_id=self.request.session.get('finyear'))

        if form.is_valid():
            employee_name_full = form.cleaned_data.get('employee_name')
            month = form.cleaned_data.get('month')
            search_criteria = form.cleaned_data.get('search_criteria')
            bg_group = form.cleaned_data.get('bg_group')
            wono = form.cleaned_data.get('wono')
            mpp_type = form.cleaned_data.get('mpp_type')
            from_date = form.cleaned_data.get('from_date')
            to_date = form.cleaned_data.get('to_date')

            # Filter by Employee
            if employee_name_full:
                emp_id = OfficeStaff.objects.get_employee_id_from_name_with_id(employee_name_full)
                if emp_id:
                    queryset = queryset.filter(empid__empid=emp_id)

            # Filter by Month
            if month and month != '0':
                # Convert date field to string for month comparison like ASP.NET's `Like '%-MM-%'`
                # This is less efficient than date range, but mirrors the original logic
                queryset = queryset.filter(date__month=int(month))

            # Filter by BG Group / WONo
            if search_criteria == '1' and bg_group:
                queryset = queryset.filter(empid__bggroup=bg_group)
            elif search_criteria == '2' and wono:
                queryset = queryset.filter(wono=wono)
            
            # Filter by Type
            if mpp_type and mpp_type != '0':
                queryset = queryset.filter(types=int(mpp_type))

            # Filter by Date Range
            if from_date and to_date:
                queryset = queryset.filter(date__range=(from_date, to_date))
            elif from_date: # Only from_date given
                queryset = queryset.filter(date__gte=from_date)
            elif to_date: # Only to_date given
                queryset = queryset.filter(date__lte=to_date)

        return queryset.order_by('date') # Order by Date ASC

class ToggleSearchFieldsPartialView(View):
    """
    HTMX endpoint to dynamically show/hide WONo or BG Group fields based on
    ddlSelectBG_WONo selection, mirroring ASP.NET's AutoPostBack behavior.
    """
    def get(self, request, *args, **kwargs):
        # This view is for showing/hiding fields, not rendering the full form.
        # It's an HTMX endpoint that swaps HTML based on selection.
        selected_value = request.GET.get('search_criteria', '0')
        comp_id, fin_year_id, _ = get_session_context(request)
        form = ManPowerPlanningSearchForm(request.GET, comp_id=comp_id, fin_year_id=fin_year_id)
        
        # Render only the relevant fields as a partial
        context = {
            'wono_field': form['wono'],
            'bg_group_field': form['bg_group'],
            'selected_value': selected_value
        }
        return render(request, 'manpower_planning/_search_toggle_fields.html', context)

class EmployeeAutoCompleteView(View):
    """
    HTMX endpoint for employee name autocomplete.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('employee_name', '')
        comp_id, _, _ = get_session_context(request)
        
        if len(prefix_text) < 1: # Minimum prefix length 1
            return JsonResponse({'results': []})

        employees = OfficeStaff.objects.filter(
            compid=comp_id,
            employeename__icontains=prefix_text
        ).values('employeename', 'empid')[:10] # Limit to 10 results

        results = [
            f"{emp['employeename']} [{emp['empid']}]" for emp in employees
        ]
        
        return render(request, 'manpower_planning/_employee_datalist_options.html', {'results': results})

class ManPowerPlanningDetailPartialView(ListView):
    """
    HTMX endpoint to render the detail Man Power Planning list (GridView3 equivalent)
    for a selected master record.
    """
    model = ManPowerPlanningDetail
    template_name = 'manpower_planning/_manpower_planning_detail_table.html'
    context_object_name = 'manpower_planning_details'

    def get_queryset(self):
        master_id = self.kwargs['pk']
        queryset = super().get_queryset().filter(mid__id=master_id)
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass forms for each detail object for rendering with Alpine.js controls
        context['detail_forms'] = [
            # ManPowerPlanningDetailForm(instance=obj) is usually for single object.
            # Here we need a generic form instance that Alpine.js can use to render dynamic fields.
            # We will pass the data to Alpine.js and it will manage visibility and initial values.
            # The form itself will only be used on submission for batch validation.
            {'id': obj.id, 
             'equip_no': obj.equipid.itemcode if obj.equipid else 'N/A',
             'description': obj.equipid.manfdesc if obj.equipid else 'N/A',
             'category': obj.category.category if obj.category else 'N/A',
             'subcategory': obj.subcategory.subcategory if obj.subcategory else 'N/A',
             'planned_desc': obj.planneddesc or '',
             'actual_desc': obj.actualdesc or '',
             'hour': obj.hour if obj.hour is not None else 0.0,
             'master_id': obj.mid.id if obj.mid else 'N/A',
             'wono': obj.mid.wono if obj.mid else 'N/A', # Included for visibility like ASP.NET
             'equip_id': obj.equipid.id if obj.equipid else 'N/A',
             'cat_id': obj.category.id if obj.category else 'N/A',
             'subcat_id': obj.subcategory.id if obj.subcategory else 'N/A',
            } for obj in self.object_list
        ]
        context['master_id'] = self.kwargs['pk']
        return context


class ManPowerPlanningDetailBatchUpdateView(View):
    """
    Handles the batch update of ManPowerPlanningDetail records,
    including the amendment business logic.
    """
    def post(self, request, *args, **kwargs):
        master_id = kwargs.get('pk')
        if not master_id:
            messages.error(request, "Master record ID is missing.")
            return HttpResponse(status=400)

        batch_form = ManPowerPlanningDetailBatchUpdateForm(request.POST)
        if batch_form.is_valid():
            detail_updates_data = batch_form.cleaned_data['updates']
            comp_id, fin_year_id, session_id = get_session_context(request)

            try:
                ManPowerPlanningService.process_detail_updates(
                    master_id, detail_updates_data, session_id, comp_id, fin_year_id
                )
                messages.success(request, "Man Power Planning details updated successfully.")
                # After successful update, trigger a refresh of the detail table
                return HttpResponse(
                    status=204, # No Content
                    headers={
                        'HX-Trigger': json.dumps({
                            'refreshManPowerPlanningDetailTable': {'master_id': master_id},
                            'refreshManPowerPlanningMasterTable': True # Master table might need refresh if amendment number is displayed
                        })
                    }
                )
            except ValueError as e:
                messages.error(request, f"Invalid data input: {e}")
                return HttpResponse(status=400) # Bad Request with error message
            except Exception as e:
                messages.error(request, f"An unexpected error occurred: {e}")
                return HttpResponse(status=500) # Internal Server Error
        else:
            messages.error(request, "Invalid form submission for updates.")
            # For HTMX, you might return specific error messages or re-render a part of the form
            # For batch forms, collecting errors for each sub-item would be more complex.
            # Simpler to return a generic error or log detailed errors.
            return HttpResponse(status=400)

```

#### 4.4 Templates (`manpower_planning/templates/manpower_planning/`)

The templates are designed for HTMX integration, with partials for dynamic updates and Alpine.js for client-side UI state.

**`manpower_planning_list.html`** (Main page, extends `core/base.html`)

```html
{% extends 'core/base.html' %}

{% block title %}Man Power Planning - Edit{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">Man Power Planning - Edit</h2>

    <!-- Search Form Section -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <form hx-get="{% url 'manpowerplanning_master_table' %}"
              hx-target="#masterTableContainer"
              hx-swap="innerHTML"
              hx-indicator="#master_spinner"
              class="space-y-4">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_criteria.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.search_criteria.label }}
                    </label>
                    {{ search_form.search_criteria }}
                </div>
                
                <div id="search_toggle_fields">
                    <!-- HTMX will swap these fields -->
                    {% include 'manpower_planning/_search_toggle_fields.html' with wono_field=search_form.wono bg_group_field=search_form.bg_group selected_value=search_form.search_criteria.value %}
                </div>

                <div>
                    <label for="{{ search_form.month.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.month.label }}
                    </label>
                    {{ search_form.month }}
                </div>
                <div>
                    <label for="{{ search_form.mpp_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.mpp_type.label }}
                    </label>
                    {{ search_form.mpp_type }}
                </div>
                <div>
                    <label for="{{ search_form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.from_date.label }}
                    </label>
                    {{ search_form.from_date }}
                </div>
                <div>
                    <label for="{{ search_form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.to_date.label }}
                    </label>
                    {{ search_form.to_date }}
                </div>
                <div>
                    <label for="{{ search_form.employee_name.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.employee_name.label }}
                    </label>
                    {{ search_form.employee_name }}
                    <datalist id="employee_name_datalist">
                        <!-- Employee autocomplete suggestions will load here -->
                    </datalist>
                    <div id="employee_name_suggestions"></div> {# Target for the actual <option> rendering, htmx needs to swap options in datalist directly, or render a custom dropdown with Alpine #}
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            {% if search_form.errors %}
                <div class="text-red-600 mt-4">
                    {% for field_name, errors in search_form.errors.items %}
                        <p>{{ search_form.fields|get_item:field_name }}: {{ errors|join:", " }}</p>
                    {% endfor %}
                    {% if search_form.non_field_errors %}
                        <p>{{ search_form.non_field_errors }}</p>
                    {% endif %}
                </div>
            {% endif %}
        </form>
    </div>

    <!-- Master Table Section (GridView2 equivalent) -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-700">Master Man Power Planning Records</h3>
        <div id="master_spinner" class="htmx-indicator text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading master records...</p>
        </div>
        <div id="masterTableContainer"
             hx-trigger="load, refreshManPowerPlanningMasterTable from:body"
             hx-get="{% url 'manpowerplanning_master_table' %}"
             hx-swap="innerHTML">
            <!-- Master table content loaded here by HTMX -->
        </div>
    </div>

    <!-- Detail Table Section (GridView3 equivalent) -->
    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold mb-4 text-gray-700">Man Power Planning Details</h3>
        <div id="detail_spinner" class="htmx-indicator text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Select a master record to load details...</p>
        </div>
        <div id="detailTableContainer"
             hx-trigger="refreshManPowerPlanningDetailTable from:body"
             hx-swap="innerHTML">
            <!-- Detail table content loaded here by HTMX -->
            <p class="text-center text-gray-500">No details selected.</p>
        </div>
        <div class="mt-6 text-center">
            <button id="btnUpdate" type="button"
                    class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-sm hidden"
                    hx-post="" {# URL dynamically set by Alpine.js on selection #}
                    hx-include="#detailTableContainer form" {# Include all form fields from within the detail table #}
                    hx-target="#detailTableContainer"
                    hx-swap="innerHTML"
                    hx-indicator="#detail_spinner"
                    _="on click set #btnUpdate.hx-post to `/manpowerplanning/${selectedMasterId}/update_details/` then add .submitting to #btnUpdate then call document.getElementById('detail_form').submit()"
            >
                Update
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Custom template filter equivalent for get_item for form fields
    // This is a workaround as Django doesn't directly allow dict-like access to form.fields in template
    // Or you can map fields in your view's context.
    // For this template, we manually access search_form.wono, search_form.bg_group etc.
</script>
<script>
    // Alpine.js data for global state, specifically for managing the update button's URL
    document.addEventListener('alpine:init', () => {
        Alpine.data('appState', () => ({
            selectedMasterId: null,
            isDetailSelected: false,
            updateButton: null, // Reference to the update button element

            init() {
                // Initialize updateButton reference once DOM is ready
                this.updateButton = document.getElementById('btnUpdate');
            },

            setSelectedMasterId(id) {
                this.selectedMasterId = id;
                this.isDetailSelected = true;
                // Dynamically update the hx-post URL for the update button
                if (this.updateButton) {
                    this.updateButton.classList.remove('hidden');
                }
            },
            
            // Function to gather all checked rows data for batch submission
            collectDetailUpdates() {
                const checkedRowsData = [];
                document.querySelectorAll('#detailTableContainer tbody tr').forEach(row => {
                    // Check if the row's Alpine.js state indicates it's checked for edit
                    const rowScope = Alpine.$data(row);
                    if (rowScope && rowScope.checked) {
                        checkedRowsData.push({
                            id: rowScope.detail_id,
                            planned: rowScope.planned_desc,
                            actual: rowScope.actual_desc,
                            hours: parseFloat(rowScope.hour_val) // Ensure correct type
                        });
                    }
                });
                return JSON.stringify(checkedRowsData);
            }
        }));
    });
</script>
<script>
    // Initialize DataTables after HTMX loads content
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'masterTableContainer') {
            $('#manpowerplanningTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before re-initializing
            });
        }
        if (evt.target.id === 'detailTableContainer') {
            $('#manpowerplanningDetailTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable before re-initializing
            });
            // Show the update button only if details are loaded
            Alpine.raw(document.getElementById('btnUpdate')).classList.remove('hidden');
        }
    });

    // Handle htmx:beforeRequest to attach JSON data for batch update
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        if (evt.detail.elt.id === 'btnUpdate') {
            // Get the Alpine.js appState to access the collectDetailUpdates function
            const appState = Alpine.$data(document.body);
            const updatesData = appState.collectDetailUpdates();
            
            // Set the dynamic hx-post URL
            const masterId = appState.selectedMasterId;
            evt.detail.elt.setAttribute('hx-post', `/manpowerplanning/${masterId}/update_details/`);

            // Append the collected JSON data to the request body
            evt.detail.parameters = {
                updates: updatesData
            };
        }
    });

    // Handle htmx:afterRequest for success/error messages
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.successful) {
            // Check for success messages from Django messages framework
            // You might need a custom HTMX extension or a small script to parse messages from headers/body
            // For now, we rely on HX-Trigger to refresh the relevant parts
            // For a more robust message display, consider a custom HTMX response header for messages
            // e.g., HX-Trigger: {"showMessages": "Updated successfully!"}
            // and a JS listener for "showMessages"
        } else {
            // Handle errors, possibly re-render the detail table with errors if validation failed
            // and the server returned a non-2xx status code.
            // Django messages framework already handles this with messages.error
        }
    });
</script>
{% endblock %}
```

**`_search_toggle_fields.html`** (Partial for dynamic search fields)

```html
<div id="search_toggle_fields">
    {% if selected_value == '1' %} {# BG Group selected #}
        <div>
            <label for="{{ bg_group_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ bg_group_field.label }}
            </label>
            {{ bg_group_field }}
        </div>
    {% elif selected_value == '2' %} {# WONo selected #}
        <div>
            <label for="{{ wono_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                {{ wono_field.label }}
            </label>
            {{ wono_field }}
        </div>
    {% endif %}
</div>
```

**`_employee_datalist_options.html`** (Partial for employee autocomplete datalist options)

```html
{% for result in results %}
    <option value="{{ result }}">
{% endfor %}
```

**`_manpower_planning_table.html`** (Partial for master DataTables)

```html
<div class="overflow-x-auto">
    <table id="manpowerplanningTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-100">
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">SN</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Actions</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Employee Name</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Designation</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Date</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">WO No</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">BG</th>
                <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Type</th>
            </tr>
        </thead>
        <tbody>
            {% for planning in manpower_plannings %}
            <tr class="hover:bg-gray-50">
                <td class="py-2 px-4 border-b text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b text-sm">
                    <button
                        class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-1 px-3 rounded-md text-xs"
                        hx-get="{% url 'manpowerplanning_detail_table' pk=planning.id %}"
                        hx-target="#detailTableContainer"
                        hx-swap="innerHTML"
                        hx-indicator="#detail_spinner"
                        _="on click call Alpine.$data(document.body).setSelectedMasterId({{ planning.id }})"
                    >
                        Select
                    </button>
                </td>
                <td class="py-2 px-4 border-b text-sm text-gray-800">{{ planning.empid.title|default_if_none:"" }}. {{ planning.empid.employeename|default_if_none:"N/A" }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-800">{{ planning.empid.designation.symbol|default_if_none:"N/A" }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-800">{{ planning.date|date:"d-m-Y" }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-800">{{ planning.wono|default_if_none:"NA" }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-800">{{ planning.dept.symbol|default_if_none:"NA" }}</td>
                <td class="py-2 px-4 border-b text-sm text-gray-800">{{ planning.get_types_display }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-4 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization for this partial
    $(document).ready(function() {
        // This re-initialization is handled by htmx:afterSwap event in main template
    });
</script>
```

**`_manpower_planning_detail_table.html`** (Partial for detail DataTables with inline edit)

```html
<div class="overflow-x-auto">
    <form id="detail_form"> {# Wrap details in a form for batch submission #}
        {% csrf_token %}
        <input type="hidden" name="updates" x-bind:value="JSON.stringify(collectDetailUpdates())"> {# This is filled by Alpine.js before hx-post #}

        <table id="manpowerplanningDetailTable" class="min-w-full bg-white border border-gray-200">
            <thead>
                <tr class="bg-gray-100">
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">SN</th>
                    <th class="py-2 px-4 border-b text-center text-xs font-medium text-gray-600 uppercase">Edit</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Equip No</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Description</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">Category</th>
                    <th class="py-2 px-4 border-b text-left text-xs font-medium text-gray-600 uppercase">SubCategory</th>
                    <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-600 uppercase">Planned</th>
                    <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-600 uppercase">Actual</th>
                    <th class="py-2 px-4 border-b text-right text-xs font-medium text-gray-600 uppercase">Hours</th>
                </tr>
            </thead>
            <tbody>
                {% for detail in detail_forms %}
                <tr x-data="{ checked: false, 
                                detail_id: {{ detail.id }},
                                planned_desc: '{{ detail.planned_desc|escapejs }}',
                                actual_desc: '{{ detail.actual_desc|escapejs }}',
                                hour_val: {{ detail.hour|floatformat:"2" }},
                                original_hour: {{ detail.hour|floatformat:"2" }}, {# Store original hour for validation #}
                                toggleEdit() {
                                    if (this.checked) {
                                        // On check, populate textboxes with current values
                                        this.$refs.planned_input.value = this.planned_desc;
                                        this.$refs.actual_input.value = this.actual_desc;
                                        this.$refs.hour_input.value = this.hour_val;
                                    } else {
                                        // On uncheck, clear textboxes and revert to label visibility
                                        this.$refs.planned_input.value = '';
                                        this.$refs.actual_input.value = '';
                                        this.$refs.hour_input.value = '';
                                    }
                                }
                            }"
                    class="hover:bg-gray-50"
                >
                    <td class="py-2 px-4 border-b text-sm text-gray-800">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b text-center text-sm">
                        <input type="checkbox" x-model="checked" @change="toggleEdit()">
                    </td>
                    <td class="py-2 px-4 border-b text-sm text-gray-800">{{ detail.equip_no }}</td>
                    <td class="py-2 px-4 border-b text-sm text-gray-800">{{ detail.description }}</td>
                    <td class="py-2 px-4 border-b text-sm text-gray-800">{{ detail.category }}</td>
                    <td class="py-2 px-4 border-b text-sm text-gray-800">{{ detail.subcategory }}</td>
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-800">
                        <span x-show="!checked">{{ detail.planned_desc }}</span>
                        <input type="text" x-ref="planned_input" x-show="checked" x-model="planned_desc" 
                               class="box3 w-full border border-gray-300 rounded-md p-1 text-sm {% if form.planned_desc.errors %}border-red-500{% endif %}"
                               name="planned_desc_{{ detail.id }}" {# Unique name for each input field #}
                        >
                        {# This is where validation errors would appear, but for batch update, it's harder #}
                    </td>
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-800">
                        <span x-show="!checked">{{ detail.actual_desc }}</span>
                        <input type="text" x-ref="actual_input" x-show="checked" x-model="actual_desc" 
                               class="box3 w-full border border-gray-300 rounded-md p-1 text-sm {% if form.actual_desc.errors %}border-red-500{% endif %}"
                               name="actual_desc_{{ detail.id }}"
                        >
                    </td>
                    <td class="py-2 px-4 border-b text-right text-sm text-gray-800">
                        <span x-show="!checked">{{ detail.hour|floatformat:"2" }}</span>
                        <input type="number" x-ref="hour_input" x-show="checked" x-model="hour_val" step="0.01" 
                               class="box3 w-full border border-gray-300 rounded-md p-1 text-sm {% if form.hour_val.errors %}border-red-500{% endif %}"
                               name="hour_val_{{ detail.id }}"
                        >
                        {# Validation error display would be complex here for inline editing #}
                    </td>
                    {# Hidden fields for detail ID - used by Alpine.js to gather data #}
                    <input type="hidden" name="detail_id_{{ detail.id }}" value="{{ detail.id }}">
                </tr>
                {% empty %}
                <tr>
                    <td colspan="9" class="py-4 text-center text-gray-500">No data to display !</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </form>
</div>

<script>
    // DataTables initialization for this partial
    $(document).ready(function() {
        // This re-initialization is handled by htmx:afterSwap event in main template
    });
</script>
```

#### 4.5 URLs (`manpower_planning/urls.py`)

This file defines the URL patterns for your Django application, connecting URLs to your views.

```python
from django.urls import path
from .views import (
    ManPowerPlanningListView,
    ManPowerPlanningTablePartialView,
    ManPowerPlanningDetailPartialView,
    ManPowerPlanningDetailBatchUpdateView,
    EmployeeAutoCompleteView,
    ToggleSearchFieldsPartialView,
)

urlpatterns = [
    # Main page for search and display
    path('manpowerplanning/', ManPowerPlanningListView.as_view(), name='manpowerplanning_list'),
    
    # HTMX endpoint for master table refresh (GridView2)
    path('manpowerplanning/master_table/', ManPowerPlanningTablePartialView.as_view(), name='manpowerplanning_master_table'),
    
    # HTMX endpoint for employee autocomplete
    path('manpowerplanning/employee-autocomplete/', EmployeeAutoCompleteView.as_view(), name='employee_autocomplete'),
    
    # HTMX endpoint for dynamically showing/hiding search fields (ddlSelectBG_WONo)
    path('manpowerplanning/toggle_search_fields/', ToggleSearchFieldsPartialView.as_view(), name='toggle_search_fields'),

    # HTMX endpoint for detail table refresh (GridView3) based on master selection
    path('manpowerplanning/<int:pk>/detail_table/', ManPowerPlanningDetailPartialView.as_view(), name='manpowerplanning_detail_table'),
    
    # HTMX endpoint for batch updating detail records
    path('manpowerplanning/<int:pk>/update_details/', ManPowerPlanningDetailBatchUpdateView.as_view(), name='manpowerplanning_update_details'),
]

```

#### 4.6 Tests (`manpower_planning/tests.py`)

Comprehensive tests are crucial for ensuring the correctness and reliability of the modernized application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock
import datetime
import json

from .models import (
    ManPowerPlanning, ManPowerPlanningDetail, OfficeStaff, BusinessGroup,
    Designation, ItemMaster, BudgetCategory, BudgetSubCategory, FinancialMaster,
    ManPowerPlanningAmd, ManPowerPlanningDetailAmd, ManPowerPlanningService
)

class ManPowerPlanningModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create FinancialMaster for month dropdown
        FinancialMaster.objects.create(compid=1, finyearid=1, finyearfrom=datetime.date(2023, 4, 1), finyearto=datetime.date(2024, 3, 31))

        # Create lookup data
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='BG-Alpha')
        cls.designation = Designation.objects.create(id=1, symbol='Engineer')
        cls.office_staff = OfficeStaff.objects.create(empid=101, employeename='John Doe', designation=cls.designation, bggroup=cls.business_group, compid=1)
        cls.item_master = ItemMaster.objects.create(id=1, itemcode='EQP001', manfdesc='Heavy Equipment')
        cls.budget_cat = BudgetCategory.objects.create(id=1, category='Construction')
        cls.budget_subcat = BudgetSubCategory.objects.create(id=1, subcategory='Roadwork', mid=cls.budget_cat)

        # Create a master ManPowerPlanning record
        cls.mpp_master = ManPowerPlanning.objects.create(
            id=1,
            sysdate=timezone.now().date(),
            systime=timezone.now().strftime('%H:%M:%S'),
            sessionid='testuser',
            compid=1,
            finyearid=1,
            empid=cls.office_staff,
            date=datetime.date(2023, 10, 26),
            wono='WO-001',
            dept=cls.business_group,
            types=ManPowerPlanning.ManPowerTypes.PRESENT,
            amendmentno=0
        )

        # Create detail ManPowerPlanning records
        cls.mpp_detail1 = ManPowerPlanningDetail.objects.create(
            id=1, mid=cls.mpp_master, equipid=cls.item_master,
            category=cls.budget_cat, subcategory=cls.budget_subcat,
            planneddesc='Task A', actualdesc='Actual A', hour=8.0
        )
        cls.mpp_detail2 = ManPowerPlanningDetail.objects.create(
            id=2, mid=cls.mpp_master, equipid=cls.item_master,
            category=cls.budget_cat, subcategory=cls.budget_subcat,
            planneddesc='Task B', actualdesc='Actual B', hour=6.0
        )

    def test_manpower_planning_creation(self):
        self.assertEqual(self.mpp_master.wono, 'WO-001')
        self.assertEqual(self.mpp_master.empid.employeename, 'John Doe')
        self.assertEqual(self.mpp_master.get_types_display(), 'Present')

    def test_manpower_planning_detail_creation(self):
        self.assertEqual(self.mpp_detail1.planneddesc, 'Task A')
        self.assertEqual(self.mpp_detail1.hour, 8.0)
        self.assertEqual(self.mpp_detail1.mid, self.mpp_master)

    def test_amendment_process_successful(self):
        # Initial state checks
        self.assertEqual(ManPowerPlanningAmd.objects.count(), 0)
        self.assertEqual(ManPowerPlanningDetailAmd.objects.count(), 0)
        self.assertEqual(self.mpp_master.amendmentno, 0)

        updates_data = [
            {'id': self.mpp_detail1.id, 'planned': 'New Task A', 'actual': 'New Actual A', 'hours': 7.5},
            {'id': self.mpp_detail2.id, 'planned': 'New Task B', 'actual': 'New Actual B', 'hours': 5.0},
        ]
        
        # Mock timezone.now to ensure consistent dates for test
        mock_date = datetime.date(2023, 11, 1)
        mock_time = '12:30:00'
        with patch('django.utils.timezone.now') as mock_now:
            mock_now.return_value = datetime.datetime.combine(mock_date, datetime.time.fromisoformat(mock_time))
            ManPowerPlanningService.process_detail_updates(
                self.mpp_master.id, updates_data, 'testuser_new', 1, 1
            )

        # Verify master record amendment
        self.mpp_master.refresh_from_db()
        self.assertEqual(self.mpp_master.amendmentno, 1)
        self.assertEqual(self.mpp_master.sysdate, mock_date)
        self.assertEqual(self.mpp_master.systime, mock_time)
        self.assertEqual(self.mpp_master.sessionid, 'testuser_new')

        # Verify master amendment log record
        master_amd = ManPowerPlanningAmd.objects.get(mid=self.mpp_master)
        self.assertEqual(master_amd.amendmentno, 0) # Should be the old amendment number
        self.assertEqual(master_amd.sessionid, 'testuser') # Should be the old session ID

        # Verify detail records updated
        self.mpp_detail1.refresh_from_db()
        self.assertEqual(self.mpp_detail1.planneddesc, 'New Task A')
        self.assertEqual(self.mpp_detail1.actualdesc, 'New Actual A')
        self.assertEqual(self.mpp_detail1.hour, 7.5)

        self.mpp_detail2.refresh_from_db()
        self.assertEqual(self.mpp_detail2.planneddesc, 'New Task B')
        self.assertEqual(self.mpp_detail2.actualdesc, 'New Actual B')
        self.assertEqual(self.mpp_detail2.hour, 5.0)

        # Verify detail amendment log records
        self.assertEqual(ManPowerPlanningDetailAmd.objects.count(), 2)
        detail_amd1 = ManPowerPlanningDetailAmd.objects.get(dmid=self.mpp_detail1)
        self.assertEqual(detail_amd1.mid, master_amd) # Links to the new master amendment record
        self.assertEqual(detail_amd1.planneddesc, 'Task A') # Should be the old planned desc

    def test_amendment_process_validation_failure_hours(self):
        updates_data = [
            {'id': self.mpp_detail1.id, 'planned': 'New Task A', 'actual': 'New Actual A', 'hours': 9.0}, # Invalid hour
        ]
        
        with self.assertRaisesMessage(ValueError, "Invalid hours for detail ID 1. Must be > 0 and <= original (8.0)."):
            ManPowerPlanningService.process_detail_updates(
                self.mpp_master.id, updates_data, 'testuser_new', 1, 1
            )
        
        # Ensure no changes were committed due to transaction.atomic rollback
        self.assertEqual(ManPowerPlanningAmd.objects.count(), 0)
        self.assertEqual(ManPowerPlanningDetailAmd.objects.count(), 0)
        self.mpp_master.refresh_from_db()
        self.assertEqual(self.mpp_master.amendmentno, 0)
        self.mpp_detail1.refresh_from_db()
        self.assertEqual(self.mpp_detail1.planneddesc, 'Task A')


class ManPowerPlanningViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Set up a mock session for compid and finyear
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 1
        session['username'] = 'testuser'
        session.save()

        # Create FinancialMaster for month dropdown
        FinancialMaster.objects.create(compid=1, finyearid=1, finyearfrom=datetime.date(2023, 4, 1), finyearto=datetime.date(2024, 3, 31))

        # Create lookup data
        self.business_group = BusinessGroup.objects.create(id=1, symbol='BG-Alpha')
        self.designation = Designation.objects.create(id=1, symbol='Engineer')
        self.office_staff = OfficeStaff.objects.create(empid=101, employeename='Jane Doe', designation=self.designation, bggroup=self.business_group, compid=1)
        self.item_master = ItemMaster.objects.create(id=1, itemcode='EQP001', manfdesc='Heavy Equipment')
        self.budget_cat = BudgetCategory.objects.create(id=1, category='Construction')
        self.budget_subcat = BudgetSubCategory.objects.create(id=1, subcategory='Roadwork', mid=self.budget_cat)

        # Create a master ManPowerPlanning record
        self.mpp_master = ManPowerPlanning.objects.create(
            id=1,
            sysdate=timezone.now().date(),
            systime=timezone.now().strftime('%H:%M:%S'),
            sessionid='testuser',
            compid=1,
            finyearid=1,
            empid=self.office_staff,
            date=datetime.date(2023, 10, 26),
            wono='WO-001',
            dept=self.business_group,
            types=ManPowerPlanning.ManPowerTypes.PRESENT,
            amendmentno=0
        )

        # Create detail ManPowerPlanning records
        self.mpp_detail1 = ManPowerPlanningDetail.objects.create(
            id=1, mid=self.mpp_master, equipid=self.item_master,
            category=self.budget_cat, subcategory=self.budget_subcat,
            planneddesc='Task A', actualdesc='Actual A', hour=8.0
        )
        self.mpp_detail2 = ManPowerPlanningDetail.objects.create(
            id=2, mid=self.mpp_master, equipid=self.item_master,
            category=self.budget_cat, subcategory=self.budget_subcat,
            planneddesc='Task B', actualdesc='Actual B', hour=6.0
        )


    def test_list_view_get(self):
        response = self.client.get(reverse('manpowerplanning_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/manpower_planning_list.html')
        self.assertIsInstance(response.context['search_form'], type(ManPowerPlanningSearchForm()))

    def test_master_table_partial_view_get(self):
        response = self.client.get(reverse('manpowerplanning_master_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_manpower_planning_table.html')
        self.assertContains(response, 'Jane Doe')
        self.assertContains(response, 'WO-001')

    def test_master_table_partial_view_search(self):
        # Search by Employee Name
        response = self.client.get(reverse('manpowerplanning_master_table'), {'employee_name': 'Jane Doe [101]'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Jane Doe')
        
        # Search by Month (October)
        response = self.client.get(reverse('manpowerplanning_master_table'), {'month': '10'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Jane Doe')

        # Search by WONo
        response = self.client.get(reverse('manpowerplanning_master_table'), {'search_criteria': '2', 'wono': 'WO-001'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'WO-001')

        # Search by BG Group (via employee's BGGroup)
        response = self.client.get(reverse('manpowerplanning_master_table'), {'search_criteria': '1', 'bg_group': self.business_group.id})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'BG-Alpha')

        # No results search
        response = self.client.get(reverse('manpowerplanning_master_table'), {'employee_name': 'NonExistent'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No data to display !')

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'employee_name': 'jane'})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Jane Doe [101]')

    def test_detail_table_partial_view_get(self):
        response = self.client.get(reverse('manpowerplanning_detail_table', args=[self.mpp_master.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_manpower_planning_detail_table.html')
        self.assertContains(response, 'Task A')
        self.assertContains(response, 'Task B')
        self.assertContains(response, 'EQP001')

    @patch('manpower_planning.models.ManPowerPlanningService.process_detail_updates')
    def test_detail_batch_update_view_post_success(self, mock_process_detail_updates):
        # Mock the service method to simulate success
        mock_process_detail_updates.return_value = True

        updates_data = [
            {'id': self.mpp_detail1.id, 'planned': 'Updated Task A', 'actual': 'Updated Actual A', 'hours': 7.0},
            {'id': self.mpp_detail2.id, 'planned': 'Updated Task B', 'actual': 'Updated Actual B', 'hours': 5.5},
        ]
        
        # HTMX request headers
        headers = {'HTTP_HX_REQUEST': 'true'}
        
        response = self.client.post(
            reverse('manpowerplanning_update_details', args=[self.mpp_master.id]),
            {'updates': json.dumps(updates_data)},
            **headers
        )
        self.assertEqual(response.status_code, 204) # No Content, as per HTMX success convention
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshManPowerPlanningDetailTable', response.headers['HX-Trigger'])
        self.assertIn('refreshManPowerPlanningMasterTable', response.headers['HX-Trigger'])

        # Verify that the service method was called with correct arguments
        mock_process_detail_updates.assert_called_once()
        args, kwargs = mock_process_detail_updates.call_args
        self.assertEqual(args[0], self.mpp_master.id) # master_planning_id
        self.assertEqual(args[1], updates_data) # detail_updates_data
        self.assertEqual(args[2], 'testuser') # session_id
        self.assertEqual(args[3], 1) # comp_id
        self.assertEqual(args[4], 1) # fin_year_id

    @patch('manpower_planning.models.ManPowerPlanningService.process_detail_updates')
    def test_detail_batch_update_view_post_validation_error(self, mock_process_detail_updates):
        # Simulate a ValueError from the service method (e.g., hours validation)
        mock_process_detail_updates.side_effect = ValueError("Invalid hours provided.")

        updates_data = [
            {'id': self.mpp_detail1.id, 'planned': 'Updated Task A', 'actual': 'Updated Actual A', 'hours': 9.0}, # Invalid value
        ]
        headers = {'HTTP_HX_REQUEST': 'true'}

        response = self.client.post(
            reverse('manpowerplanning_update_details', args=[self.mpp_master.id]),
            {'updates': json.dumps(updates_data)},
            **headers
        )
        self.assertEqual(response.status_code, 400) # Bad Request
        self.assertIn("Invalid data input: Invalid hours provided.", [msg.message for msg in list(response.context['messages'])])

    def test_toggle_search_fields_partial_view_get(self):
        # Test when BG Group is selected
        response = self.client.get(reverse('toggle_search_fields'), {'search_criteria': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_search_toggle_fields.html')
        self.assertContains(response, 'id_bg_group_field')
        self.assertNotContains(response, 'id_wono_field')

        # Test when WONo is selected
        response = self.client.get(reverse('toggle_search_fields'), {'search_criteria': '2'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_search_toggle_fields.html')
        self.assertContains(response, 'id_wono_field')
        self.assertNotContains(response, 'id_bg_group_field')

        # Test when "Select WONo or BG Group" is selected
        response = self.client.get(reverse('toggle_search_fields'), {'search_criteria': '0'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'manpower_planning/_search_toggle_fields.html')
        self.assertNotContains(response, 'id_wono_field')
        self.assertNotContains(response, 'id_bg_group_field')

```

### Step 5: HTMX and Alpine.js Integration

The templates and views are designed for a smooth, dynamic user experience without full page reloads.

*   **HTMX for dynamic content:**
    *   `hx-get` on search form submits filters to `manpowerplanning_master_table/` to update the master grid.
    *   `hx-trigger="load"` and `hx-trigger="refreshManPowerPlanningMasterTable from:body"` on `masterTableContainer` ensure initial load and dynamic refresh.
    *   "Select" buttons in the master table use `hx-get` to `manpowerplanning_detail_table/<pk>/` to load the detail grid into `detailTableContainer`.
    *   `btnUpdate` uses `hx-post` to `manpowerplanning_update_details/<pk>/` to submit the batch of detail updates.
    *   `hx-include="#detailTableContainer form"` ensures all necessary form fields within the detail table are included in the POST request for batch update.
    *   `HX-Trigger` headers are sent back from the `ManPowerPlanningDetailBatchUpdateView` after a successful update to instruct HTMX to refresh the master and detail tables.
    *   The `search_criteria` dropdown uses HTMX to `hx-get` a partial update to show/hide the `WONo` or `BG Group` fields.

*   **Alpine.js for UI state management:**
    *   Each row in the detail table uses `x-data` to manage its own `checked` state, `planned_desc`, `actual_desc`, `hour_val`, and `original_hour`.
    *   `x-show` directives are used to toggle visibility between the label (`<span>`) and input (`<input>`) fields based on the `checked` state.
    *   `@change="toggleEdit()"` on the checkbox calls a local Alpine.js function to populate/clear input fields when toggling edit mode.
    *   The global `appState` Alpine.js component tracks `selectedMasterId` and provides `collectDetailUpdates()` function to gather data from all checked rows for batch submission, simplifying data aggregation.
    *   The `btnUpdate` button's visibility and `hx-post` URL are dynamically managed by Alpine.js to ensure it's only active when a master record is selected and points to the correct endpoint.

*   **DataTables integration:**
    *   jQuery DataTables is initialized on both the master and detail tables after they are loaded or swapped into the DOM by HTMX. The `htmx:afterSwap` event listener ensures correct re-initialization.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET Man Power Planning module to Django. By following these structured steps and leveraging modern web technologies, you will achieve a more maintainable, performant, and user-friendly application, reducing technical debt and enabling future development with greater agility. The emphasis on AI-assisted automation means these structured code blocks can be easily fed into tools for rapid conversion.