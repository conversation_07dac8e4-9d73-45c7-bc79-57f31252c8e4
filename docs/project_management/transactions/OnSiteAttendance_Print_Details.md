## ASP.NET to Django Conversion Script: On-Site Attendance Print Details

This modernization plan outlines the automated conversion of your ASP.NET On-Site Attendance Print Details page to a modern Django-based solution. Our focus is on replicating the core functionality of displaying attendance reports, utilizing efficient data handling, and providing a dynamic, user-friendly interface with HTMX and Alpine.js.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`project_management`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The ASP.NET code primarily retrieves data using a `SqlDataAdapter` executing a stored procedure named `GetOnSiteEmp_Print`. This stored procedure takes several parameters (`@m`, `@z`, `@p`, `@q`, `@CompId`) and returns a `DataSet` that is then bound to a Crystal Report. The report is titled "OnSiteAttendance.rpt".

Since we don't have the definition of the stored procedure or the exact schema of the `OnSiteAttendance.xsd` DataSet, we infer the likely underlying tables and the structure of the data returned by the stored procedure for an "On-Site Attendance" report. We will assume a primary table for attendance records and a related table for company profiles.

**Inferred Database Components:**

*   **Main Report Data Source (Conceptual / Derived):** The `GetOnSiteEmp_Print` stored procedure likely queries an underlying table or view that contains attendance records and related employee/project information. For our `managed=False` model, we will define a conceptual `db_table` representing the main attendance data source. Let's name it `tbl_on_site_attendance_data` (or it could be a database view like `vw_onsite_attendance_report`).
    *   **Inferred Columns (based on report context and SP parameters):**
        *   `EmployeeId` (int) - related to `@p` parameter
        *   `EmployeeName` (string)
        *   `AttendanceDate` (date) - related to `@z` parameter
        *   `InTime` (time)
        *   `OutTime` (time)
        *   `Status` (string, e.g., 'Present', 'Absent', 'HalfDay')
        *   `ProjectName` (string) - related to `@m` parameter
        *   `Location` (string)
        *   `CompId` (int) - related to `@CompId` parameter
        *   `FinYearId` (int) - from session
        *   `GroupCategory` (string) - related to `@q` parameter (optional, if `q` denotes a grouping)
        *   (Implicit Primary Key for records)

*   **Company Information Source:** The `fun.CompAdd(CompId)` function indicates a separate lookup for company address.
    *   **Inferred Table:** `tbl_company_profile`
    *   **Inferred Columns:**
        *   `CompId` (PK, int)
        *   `CompanyName` (string)
        *   `Address` (text)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The ASP.NET page is a **reporting** page, not a CRUD management interface for individual attendance records.

*   **Read (Primary Functionality):**
    *   Retrieves attendance data using the `GetOnSiteEmp_Print` stored procedure.
    *   Filters data based on query string parameters (`m`, `p`, `q`, `z`) and session variables (`CompId`, `FinYearId`).
    *   Fetches company address using `fun.CompAdd()`.
    *   Binds the filtered data and company address to a Crystal Report for display/printing.

*   **Create, Update, Delete:**
    *   There are **no direct Create, Update, or Delete operations** for `OnSiteAttendance` records exposed on this specific ASP.NET page. The page is purely for viewing/printing a report.
    *   While the Django modernization template requires placeholders for CRUD views, their primary utility would be for generic management of the underlying `OnSiteAttendanceRecord` entity elsewhere, not for this reporting interface.

*   **Business Logic:**
    *   Database connection management.
    *   Retrieval of session variables (`CompId`, `FinYearId`).
    *   Parsing of query string parameters.
    *   Execution of `GetOnSiteEmp_Print` stored procedure with dynamic parameters.
    *   Company address lookup based on `CompId`.
    *   Report data binding and parameter setting.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The ASP.NET page is minimal, focusing on report display.

*   **`CR:CrystalReportViewer`:** This is the main component, responsible for rendering the Crystal Report.
    *   **Django Equivalent:** This will be replaced by a DataTables-powered list view within a standard HTML template. Filtering will be handled via HTMX with input fields on the page.
*   **`loadingNotifier.js`:** A client-side JavaScript file to show a loading indicator.
    *   **Django Equivalent:** HTMX provides built-in `hx-indicator` functionality for showing loading states, making external JavaScript for this purpose redundant. Alpine.js can further manage UI state if more complex loading scenarios are needed.
*   **`QueryString` parameters (m, p, q, z):** These implicitly act as UI inputs for filtering the report.
    *   **Django Equivalent:** These will be represented by HTML `<input>` fields (e.g., text, date pickers) that trigger HTMX requests with `hx-get` to refresh the DataTables content based on user input.

### Step 4: Generate Django Code

We will create a new Django application named `project_management` to house these components.

#### 4.1 Models

**Task:** Create Django models based on the database schema.

**Instructions:**
We'll create two models: `OnSiteAttendanceRecord` representing the conceptual data returned by the stored procedure, and `CompanyProfile` for company address lookup. Both will use `managed = False` as per the instruction, assuming they map to existing tables/views.

**`project_management/models.py`:**

```python
from django.db import models
from django.db import connection
from datetime import datetime, date

class OnSiteAttendanceRecord(models.Model):
    """
    Represents a record in the On-Site Attendance Report.
    This model's fields are inferred from the likely output of the
    'GetOnSiteEmp_Print' stored procedure.
    It's set to managed=False, assuming the underlying database table/view
    (e.g., 'tbl_on_site_attendance_data' or a reporting view) already exists.
    """
    # Assuming primary key (e.g., an identity column) is handled by the database
    # and not explicitly declared here for managed=False models unless needed for specific lookups.
    # We will use employee_id and attendance_date as a pseudo-unique identifier for simplicity in tests.
    employee_id = models.IntegerField(db_column='EmployeeId')
    employee_name = models.CharField(max_length=255, db_column='EmployeeName')
    attendance_date = models.DateField(db_column='AttendanceDate')
    in_time = models.TimeField(db_column='InTime', null=True, blank=True)
    out_time = models.TimeField(db_column='OutTime', null=True, blank=True)
    status = models.CharField(max_length=50, db_column='Status')  # e.g., 'Present', 'Absent', 'HalfDay'
    project_name = models.CharField(max_length=255, db_column='ProjectName', null=True, blank=True)
    location = models.CharField(max_length=255, db_column='Location', null=True, blank=True)
    company_id = models.IntegerField(db_column='CompId')
    financial_year_id = models.IntegerField(db_column='FinYearId')
    group_category = models.CharField(max_length=255, db_column='GroupCategory', null=True, blank=True) # Assuming 'q' maps here

    class Meta:
        managed = False
        # This is a placeholder for the actual database table or view that holds
        # the aggregated data for the on-site attendance report.
        # In a real migration, this would be determined by analyzing the SP definition.
        db_table = 'tbl_on_site_attendance_data' # Example: Could be a view like 'vw_onsite_attendance_report'
        verbose_name = 'On-Site Attendance Record'
        verbose_name_plural = 'On-Site Attendance Records'
        # If no explicit PK, Django requires a unique_together or can infer one from data.
        # If the table has a composite PK, define it here.
        # unique_together = (('employee_id', 'attendance_date', 'company_id', 'financial_year_id'),)

    def __str__(self):
        return f"{self.employee_name} on {self.attendance_date} ({self.status})"

    @classmethod
    def get_report_data(cls, company_id, financial_year_id, m_param, z_param, p_param, q_param):
        """
        Retrieves on-site attendance report data, simulating the 'GetOnSiteEmp_Print' stored procedure.
        This method should encapsulate the complex logic of the original stored procedure.
        For a true managed=False model, this might involve raw SQL queries or direct SP calls.
        For demonstration, we'll filter directly on inferred model fields.
        """
        # Start with base filters for company and financial year
        queryset = cls.objects.filter(company_id=company_id, financial_year_id=financial_year_id)

        # Apply additional filters based on query parameters
        if m_param and m_param.lower() != 'null': # Project/Month filter
            # Assuming 'm' could be a project name, ID, or part of a date string for month
            # For simplicity, let's assume it's a project name fragment or ID for now.
            try: # Try as int for project ID
                m_int = int(m_param)
                queryset = queryset.filter(project_id=m_int) # Requires project_id field
            except ValueError: # Otherwise, treat as project name contains
                queryset = queryset.filter(project_name__icontains=m_param)

        if z_param and z_param.lower() != 'null': # Onsite Date Wise filter
            try:
                # Assuming date format is YYYY-MM-DD
                parsed_date = datetime.strptime(z_param, '%Y-%m-%d').date()
                queryset = queryset.filter(attendance_date=parsed_date)
            except ValueError:
                # Handle cases where z_param might be an invalid date or a month string
                try:
                    # If z_param is a month (e.g., '2023-01'), filter by month and year
                    parsed_month_year = datetime.strptime(z_param, '%Y-%m').date()
                    queryset = queryset.filter(attendance_date__year=parsed_month_year.year,
                                              attendance_date__month=parsed_month_year.month)
                except ValueError:
                    pass # Invalid date/month format, ignore filter

        if p_param and p_param.lower() != 'null': # Employee Wise filter
            # Assuming 'p' could be EmployeeId or EmployeeName
            try:
                p_int = int(p_param)
                queryset = queryset.filter(employee_id=p_int)
            except ValueError:
                queryset = queryset.filter(employee_name__icontains=p_param)

        if q_param and q_param.lower() != 'null': # BG (Group) filter
            queryset = queryset.filter(group_category__icontains=q_param)

        # Return the filtered data. In a real scenario, this might involve
        # executing a raw stored procedure call and mapping the results.
        # Example for raw SP call (requires database-specific syntax):
        """
        with connection.cursor() as cursor:
            # Adjust SP name and parameter types/order as per actual DB
            cursor.execute("EXEC GetOnSiteEmp_Print @m=%s, @z=%s, @p=%s, @q=%s, @CompId=%s",
                           [m_param, z_param, p_param, q_param, company_id])
            columns = [col[0] for col in cursor.description]
            return [cls(**dict(zip(columns, row))) for row in cursor.fetchall()]
        """
        return list(queryset) # Execute the query and return as a list of model instances


class CompanyProfile(models.Model):
    """
    Represents company profile information, used for retrieving company address.
    Set to managed=False, assuming 'tbl_company_profile' already exists in DB.
    """
    company_id = models.IntegerField(db_column='CompId', primary_key=True)
    company_name = models.CharField(max_length=255, db_column='CompanyName')
    address = models.TextField(db_column='Address')

    class Meta:
        managed = False
        db_table = 'tbl_company_profile'
        verbose_name = 'Company Profile'
        verbose_name_plural = 'Company Profiles'

    def __str__(self):
        return self.company_name

    @classmethod
    def get_company_address(cls, comp_id):
        """
        Equivalent to the ASP.NET fun.CompAdd(CompId) function.
        Retrieves the address for a given company ID.
        """
        try:
            company = cls.objects.get(company_id=comp_id)
            return company.address
        except cls.DoesNotExist:
            return "Company Address Not Available"
        except Exception as e:
            # Log the exception for debugging
            return f"Error retrieving address: {e}"

```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
For the report display, we primarily need a form for filters. However, the prompt also asks for a `[MODEL_NAME]Form`. We will provide a dummy `OnSiteAttendanceRecordForm` (which has no editable fields as this model represents report output) and a dedicated `OnSiteAttendanceFilterForm` for the query parameters.

**`project_management/forms.py`:**

```python
from django import forms
from .models import OnSiteAttendanceRecord

class OnSiteAttendanceRecordForm(forms.ModelForm):
    """
    Placeholder form for OnSiteAttendanceRecord.
    As this model primarily represents data for a report display
    and not direct CRUD, it has no editable fields defined here.
    If direct management of attendance records were needed,
    fields would be added here.
    """
    class Meta:
        model = OnSiteAttendanceRecord
        fields = [] # No editable fields for a report output model
        # Example if fields were editable:
        # fields = ['status', 'location', 'remarks']
        # widgets = {
        #     'status': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        #     'location': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        # }

class OnSiteAttendanceFilterForm(forms.Form):
    """
    Form to capture filter parameters for the On-Site Attendance Report.
    These correspond to the ASP.NET QueryString parameters (m, z, p, q).
    """
    m = forms.CharField(
        label='Project/Month',
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Project Name/ID or YYYY-MM'})
    )
    z = forms.DateField(
        label='Attendance Date',
        required=False,
        widget=forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
        input_formats=['%Y-%m-%d']
    )
    p = forms.CharField(
        label='Employee',
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Employee Name/ID'})
    )
    q = forms.CharField(
        label='Group/Category',
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Group Name'})
    )

    def clean(self):
        cleaned_data = super().clean()
        # Custom validation logic if needed, e.g., date range checks
        return cleaned_data

```

#### 4.3 Views

**Task:** Implement report display and placeholder CRUD operations using CBVs.

**Instructions:**
The main view `OnSiteAttendanceReportView` will serve the initial page with the filter form and the HTMX container for the DataTables. The `OnSiteAttendanceRecordTablePartialView` will handle the HTMX requests for refreshing the table data. The CRUD views are included as per the template requirement but are noted as being for generic record management, not the report itself.

**`project_management/views.py`:**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render
from .models import OnSiteAttendanceRecord, CompanyProfile
from .forms import OnSiteAttendanceRecordForm, OnSiteAttendanceFilterForm
from datetime import datetime, date

class OnSiteAttendanceReportView(TemplateView):
    """
    Main view for displaying the On-Site Attendance Report.
    This replaces the ASP.NET .aspx page, providing a filter form
    and an HTMX-driven container for the report table.
    """
    template_name = 'project_management/onsiteattendancerecord/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize filter form with existing query parameters, if any
        context['filter_form'] = OnSiteAttendanceFilterForm(self.request.GET)
        
        # Simulating ASP.NET Session variables. In a real application,
        # 'compid' and 'finyear' would come from an authenticated session or user profile.
        # Defaults provided for demonstration/testing.
        context['company_id'] = self.request.session.get('compid', 1) 
        context['financial_year_id'] = self.request.session.get('finyear', 2023)
        
        # Get company address immediately for display on the main page
        context['company_address'] = CompanyProfile.get_company_address(context['company_id'])

        return context

class OnSiteAttendanceRecordTablePartialView(ListView):
    """
    HTMX partial view to render only the DataTables content based on filters.
    This view is responsible for fetching the report data.
    """
    model = OnSiteAttendanceRecord
    template_name = 'project_management/onsiteattendancerecord/_onsiteattendancerecord_table.html'
    context_object_name = 'onsiteattendancerecords'
    
    def get_queryset(self):
        # Retrieve company and financial year IDs from session
        company_id = self.request.session.get('compid', 1)
        financial_year_id = self.request.session.get('finyear', 2023)

        # Retrieve filter parameters from GET request (submitted by filter form)
        m_param = self.request.GET.get('m', 'null')
        z_param = self.request.GET.get('z', 'null')
        p_param = self.request.GET.get('p', 'null')
        q_param = self.request.GET.get('q', 'null')
        
        # Call the model method to get the filtered report data
        return OnSiteAttendanceRecord.get_report_data(
            company_id=company_id,
            financial_year_id=financial_year_id,
            m_param=m_param,
            z_param=z_param,
            p_param=p_param,
            q_param=q_param
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass company address to the partial table for display
        company_id = self.request.session.get('compid', 1)
        context['company_address'] = CompanyProfile.get_company_address(company_id)
        return context

# --- Generic CRUD Views (Included as per prompt's template, but not for the original report view's purpose) ---
# These views provide standard Django CRUD for the OnSiteAttendanceRecord model,
# should a direct management interface for these records be required elsewhere.
class OnSiteAttendanceRecordCreateView(CreateView):
    model = OnSiteAttendanceRecord
    form_class = OnSiteAttendanceRecordForm
    template_name = 'project_management/onsiteattendancerecord/form.html'
    success_url = reverse_lazy('onsiteattendancerecord_list') # Redirect to report list after creation

    def form_valid(self, form):
        # Add logic here to set company_id, finyear_id if required before saving
        # For a managed=False model, you might be inserting into the actual table here.
        # As the form has no fields, this will likely be a no-op unless custom logic is added.
        messages.success(self.request, 'On-Site Attendance Record (if applicable) added successfully.')
        response = super().form_valid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, triggers client-side refresh
                headers={
                    'HX-Trigger': 'refreshOnSiteAttendanceRecordList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add On-Site Attendance Record'
        return context

class OnSiteAttendanceRecordUpdateView(UpdateView):
    model = OnSiteAttendanceRecord
    form_class = OnSiteAttendanceRecordForm
    template_name = 'project_management/onsiteattendancerecord/form.html'
    success_url = reverse_lazy('onsiteattendancerecord_list') # Redirect to report list after update

    def form_valid(self, form):
        # As the form has no fields, this will likely be a no-op unless custom logic is added.
        messages.success(self.request, 'On-Site Attendance Record (if applicable) updated successfully.')
        response = super().form_valid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOnSiteAttendanceRecordList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit On-Site Attendance Record'
        return context

class OnSiteAttendanceRecordDeleteView(DeleteView):
    model = OnSiteAttendanceRecord
    template_name = 'project_management/onsiteattendancerecord/confirm_delete.html'
    success_url = reverse_lazy('onsiteattendancerecord_list') # Redirect to report list after delete

    def delete(self, request, *args, **kwargs):
        # For managed=False, ensure this correctly deletes from the actual DB table.
        messages.success(self.request, 'On-Site Attendance Record (if applicable) deleted successfully.')
        response = super().delete(request, *args, **kwargs)
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshOnSiteAttendanceRecordList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Delete On-Site Attendance Record'
        return context

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
The templates adhere to DRY principles, use HTMX for dynamic content loading (especially for the DataTables partial), and incorporate Tailwind CSS for styling. The `base.html` is extended but its content is not included.

**`project_management/templates/project_management/onsiteattendancerecord/list.html`:**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">On-Site Attendance Report</h2>
        <!-- This button is for generic CRUD as per template, not directly for report generation -->
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'onsiteattendancerecord_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New On-Site Attendance Record
        </button>
    </div>
    
    <!-- Filter Form for the Report -->
    <div class="mb-6 bg-white p-6 rounded-lg shadow-md">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Report Data</h3>
        <form hx-get="{% url 'onsiteattendancerecord_table' %}" hx-target="#onsiteattendancerecordTable-container" hx-swap="innerHTML">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {% for field in filter_form %}
                <div>
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ field.label }}
                    </label>
                    {{ field }}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            <div class="mt-6 text-right">
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">Apply Filters</button>
            </div>
        </form>
    </div>

    <!-- HTMX Container for the DataTables content -->
    <div id="onsiteattendancerecordTable-container"
         hx-trigger="load, refreshOnSiteAttendanceRecordList from:body"
         hx-get="{% url 'onsiteattendancerecord_table' %}{% if request.GET %}?{{ request.GET.urlencode }}{% endif %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading On-Site Attendance Records...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me then remove innerHTML from #modalContent">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states.
        // For example, managing modal visibility or form states more robustly.
    });

    // Event listener to clear modal content when it's closed, ensuring fresh form load.
    document.getElementById('modal').addEventListener('htmx:afterRequest', function(event) {
        if (!event.detail.target.closest('#modalContent')) {
            // Only clear if the request was not filling the modal content itself
            // (e.g., if a form submit from modal triggered a close event, but not a new modal load)
            const modal = document.getElementById('modal');
            if (!modal.classList.contains('is-active')) {
                document.getElementById('modalContent').innerHTML = '';
            }
        }
    });

    // Handle messages from Django
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target && event.detail.target.id === 'messages-container') {
            setTimeout(() => {
                const messages = document.getElementById('messages-container');
                if (messages) {
                    messages.innerHTML = '';
                }
            }, 5000); // Messages disappear after 5 seconds
        }
    });
</script>
{% endblock %}

```

**`project_management/templates/project_management/onsiteattendancerecord/_onsiteattendancerecord_table.html`:**

```html
<div class="bg-white p-6 rounded-lg shadow-md mb-6">
    <h3 class="text-lg font-semibold text-gray-800 mb-2">Company Address:</h3>
    <p class="text-gray-700">{{ company_address }}</p>
</div>

{% if onsiteattendancerecords %}
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="onsiteattendancerecordTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee Name</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">In Time</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Out Time</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in onsiteattendancerecords %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.employee_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.attendance_date|date:"Y-m-d" }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.in_time|default:"N/A" }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.out_time|default:"N/A" }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.status }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.project_name|default:"N/A" }}</td>
                <td class="py-2 px-4 whitespace-nowrap">{{ obj.location|default:"N/A" }}</td>
                <td class="py-2 px-4 whitespace-nowrap">
                    <!-- Actions for individual records if they were editable.
                         Note: These buttons are for generic CRUD if this model had editable instances.
                         The original page was a report, not a CRUD interface. -->
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-sm mr-2"
                        hx-get="{% url 'onsiteattendancerecord_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-sm"
                        hx-get="{% url 'onsiteattendancerecord_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% else %}
<div class="bg-white p-6 rounded-lg shadow-md text-center text-gray-600">
    <p>No on-site attendance records found for the selected criteria. Please adjust your filters.</p>
</div>
{% endif %}

<script>
// Initialize DataTable after HTMX swaps the content
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent reinitialization errors
    if ($.fn.DataTable.isDataTable('#onsiteattendancerecordTable')) {
        $('#onsiteattendancerecordTable').DataTable().destroy();
    }
    // Initialize new DataTable
    $('#onsiteattendancerecordTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "searching": true,   // Enable search box
        "ordering": true,    // Enable column ordering
        "paging": true,      // Enable pagination
        "info": true         // Display info about showing X of Y entries
    });
});
</script>

```

**`project_management/templates/project_management/onsiteattendancerecord/form.html`:**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} On-Site Attendance Record</h3>
    <!-- This form is a placeholder as the report data is not directly editable via CRUD. -->
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="json-enc" onsubmit="this.closest('#modal').classList.remove('is-active');">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% if form.fields %}
                {% for field in form %}
                <div class="mb-4">
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ field.label }}
                    </label>
                    {{ field }}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            {% else %}
                <p class="text-sm text-gray-600">This form is currently not configured for direct editing of On-Site Attendance Records as they are primarily generated by a reporting process.</p>
                <p class="text-sm text-gray-600">Please refer to the underlying data management forms for attendance records.</p>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                {% if not form.fields %} disabled title="No editable fields" {% endif %}>
                Save
            </button>
        </div>
    </form>
</div>
```

**`project_management/templates/project_management/onsiteattendancerecord/confirm_delete.html`:**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the On-Site Attendance Record for "{{ object.employee_name }}" on "{{ object.attendance_date }}"?</p>
    <form hx-post="{{ request.path }}" hx-swap="none" onsubmit="this.closest('#modal').classList.remove('is-active');">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main report view, the HTMX partial for the table, and placeholder URLs for the generic CRUD operations (add, edit, delete).

**`project_management/urls.py`:**

```python
from django.urls import path
from .views import (
    OnSiteAttendanceReportView,
    OnSiteAttendanceRecordTablePartialView,
    OnSiteAttendanceRecordCreateView,
    OnSiteAttendanceRecordUpdateView,
    OnSiteAttendanceRecordDeleteView
)

urlpatterns = [
    # Main report display view, equivalent to the original ASPX page
    path('onsiteattendance/', OnSiteAttendanceReportView.as_view(), name='onsiteattendancerecord_list'),
    
    # HTMX endpoint for refreshing the DataTables content with filters
    path('onsiteattendance/table/', OnSiteAttendanceRecordTablePartialView.as_view(), name='onsiteattendancerecord_table'),
    
    # Generic CRUD paths (as per template requirements, not primary function of original report page)
    path('onsiteattendance/add/', OnSiteAttendanceRecordCreateView.as_view(), name='onsiteattendancerecord_add'),
    path('onsiteattendance/edit/<int:pk>/', OnSiteAttendanceRecordUpdateView.as_view(), name='onsiteattendancerecord_edit'),
    path('onsiteattendance/delete/<int:pk>/', OnSiteAttendanceRecordDeleteView.as_view(), name='onsiteattendancerecord_delete'),
]

```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and integration tests for views are provided. Mocking is used where necessary to simulate database interactions for `managed=False` models and session data.

**`project_management/tests.py`:**

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date, time, datetime
from .models import OnSiteAttendanceRecord, CompanyProfile

# Mock data for managed=False models
MOCK_ATTENDANCE_DATA = [
    {'employee_id': 101, 'employee_name': 'Alice Smith', 'attendance_date': date(2023, 10, 1),
     'in_time': time(9, 0), 'out_time': time(17, 0), 'status': 'Present', 'project_name': 'Alpha',
     'location': 'Site A', 'company_id': 1, 'financial_year_id': 2023, 'group_category': 'Group X'},
    {'employee_id': 102, 'employee_name': 'Bob Johnson', 'attendance_date': date(2023, 10, 1),
     'in_time': time(9, 30), 'out_time': time(17, 30), 'status': 'Present', 'project_name': 'Beta',
     'location': 'Site B', 'company_id': 1, 'financial_year_id': 2023, 'group_category': 'Group Y'},
    {'employee_id': 101, 'employee_name': 'Alice Smith', 'attendance_date': date(2023, 10, 2),
     'in_time': time(9, 0), 'out_time': time(17, 0), 'status': 'Absent', 'project_name': 'Alpha',
     'location': 'Site A', 'company_id': 1, 'financial_year_id': 2023, 'group_category': 'Group X'},
    {'employee_id': 103, 'employee_name': 'Charlie Brown', 'attendance_date': date(2023, 11, 5),
     'in_time': time(8, 45), 'out_time': time(16, 45), 'status': 'Present', 'project_name': 'Gamma',
     'location': 'Site C', 'company_id': 1, 'financial_year_id': 2023, 'group_category': 'Group Z'},
    {'employee_id': 104, 'employee_name': 'David Green', 'attendance_date': date(2023, 10, 15),
     'in_time': time(9, 0), 'out_time': time(17, 0), 'status': 'HalfDay', 'project_name': 'Alpha',
     'location': 'Site A', 'company_id': 2, 'financial_year_id': 2023, 'group_category': 'Group X'},
]

MOCK_COMPANY_DATA = [
    {'company_id': 1, 'company_name': 'Tech Corp', 'address': '123 Tech Lane, Innovation City'},
    {'company_id': 2, 'company_name': 'Global Solutions', 'address': '456 World Blvd, Global Town'},
]


class OnSiteAttendanceRecordTest(TestCase):
    """
    Unit tests for the OnSiteAttendanceRecord model and its methods.
    Mocks the database interactions for managed=False models.
    """
    @patch('project_management.models.OnSiteAttendanceRecord.objects.filter')
    def test_get_report_data_no_filters(self, mock_filter):
        """Test retrieving all report data for a given company and financial year."""
        # Configure the mock to return a mock queryset that behaves like a list
        mock_qs = MagicMock()
        mock_qs.filter.return_value = list(filter(
            lambda x: x['company_id'] == 1 and x['financial_year_id'] == 2023,
            MOCK_ATTENDANCE_DATA
        ))
        mock_filter.return_value = mock_qs

        records = OnSiteAttendanceRecord.get_report_data(1, 2023, 'null', 'null', 'null', 'null')
        self.assertEqual(len(records), 3) # Expected: Alice (2), Bob (1) for company 1, FY 2023

        # Assert that the initial filter was called
        mock_filter.assert_called_once_with(company_id=1, financial_year_id=2023)
        mock_qs.filter.assert_not_called() # No additional filters applied

    @patch('project_management.models.OnSiteAttendanceRecord.objects.filter')
    def test_get_report_data_with_all_filters(self, mock_filter):
        """Test retrieving report data with all parameters applied."""
        mock_qs_step1 = MagicMock()
        mock_qs_step2 = MagicMock()
        mock_qs_step3 = MagicMock()
        mock_qs_step4 = MagicMock()

        # Simulate progressive filtering
        initial_filtered_data = list(filter(
            lambda x: x['company_id'] == 1 and x['financial_year_id'] == 2023,
            MOCK_ATTENDANCE_DATA
        ))
        
        mock_filter.return_value = mock_qs_step1
        mock_qs_step1.filter.return_value = mock_qs_step2
        mock_qs_step2.filter.return_value = mock_qs_step3
        mock_qs_step3.filter.return_value = mock_qs_step4
        
        # Manually filter the mock data as the "filter" chain would do
        filtered_by_m = list(filter(lambda x: 'Alpha' in x['project_name'], initial_filtered_data))
        filtered_by_z = list(filter(lambda x: x['attendance_date'] == date(2023, 10, 1), filtered_by_m))
        filtered_by_p = list(filter(lambda x: x['employee_id'] == 101, filtered_by_z))
        filtered_by_q = list(filter(lambda x: 'Group X' in x['group_category'], filtered_by_p))

        mock_qs_step4.__iter__.return_value = iter(filtered_by_q)

        records = OnSiteAttendanceRecord.get_report_data(
            company_id=1,
            financial_year_id=2023,
            m_param='Alpha',
            z_param='2023-10-01',
            p_param='101',
            q_param='Group X'
        )
        self.assertEqual(len(records), 1)
        self.assertEqual(records[0]['employee_name'], 'Alice Smith')
        self.assertEqual(records[0]['attendance_date'], date(2023, 10, 1))

        # Assert calls
        mock_filter.assert_called_once_with(company_id=1, financial_year_id=2023)
        mock_qs_step1.filter.assert_any_call(project_name__icontains='Alpha')
        mock_qs_step2.filter.assert_any_call(attendance_date=date(2023, 10, 1))
        mock_qs_step3.filter.assert_any_call(employee_id=101)
        mock_qs_step4.filter.assert_any_call(group_category__icontains='Group X')


class CompanyProfileTest(TestCase):
    """
    Unit tests for the CompanyProfile model and its methods.
    Mocks the database interactions for managed=False models.
    """
    @patch('project_management.models.CompanyProfile.objects.get')
    def test_get_company_address_success(self, mock_get):
        """Test successful retrieval of company address."""
        mock_company = MagicMock()
        mock_company.address = MOCK_COMPANY_DATA[0]['address']
        mock_get.return_value = mock_company

        address = CompanyProfile.get_company_address(1)
        self.assertEqual(address, MOCK_COMPANY_DATA[0]['address'])
        mock_get.assert_called_once_with(company_id=1)

    @patch('project_management.models.CompanyProfile.objects.get')
    def test_get_company_address_not_found(self, mock_get):
        """Test retrieval when company ID does not exist."""
        mock_get.side_effect = CompanyProfile.DoesNotExist

        address = CompanyProfile.get_company_address(999)
        self.assertEqual(address, "Company Address Not Available")
        mock_get.assert_called_once_with(company_id=999)

    @patch('project_management.models.CompanyProfile.objects.get')
    def test_get_company_address_exception(self, mock_get):
        """Test retrieval when an unexpected exception occurs."""
        mock_get.side_effect = Exception("Database error")

        address = CompanyProfile.get_company_address(1)
        self.assertIn("Error retrieving address: Database error", address)
        mock_get.assert_called_once_with(company_id=1)


class OnSiteAttendanceViewsTest(TestCase):
    """
    Integration tests for the On-Site Attendance views.
    Mocks the underlying model methods to control data.
    """
    def setUp(self):
        self.client = Client()
        # Set up a mock session for the client
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2023
        session.save()

    @patch('project_management.models.OnSiteAttendanceRecord.get_report_data')
    @patch('project_management.models.CompanyProfile.get_company_address')
    def test_report_view_get(self, mock_get_company_address, mock_get_report_data):
        """Test the main report display view (GET request)."""
        mock_get_report_data.return_value = [
            OnSiteAttendanceRecord(**MOCK_ATTENDANCE_DATA[0]),
            OnSiteAttendanceRecord(**MOCK_ATTENDANCE_DATA[1])
        ]
        mock_get_company_address.return_value = MOCK_COMPANY_DATA[0]['address']

        response = self.client.get(reverse('onsiteattendancerecord_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendancerecord/list.html')
        self.assertIn('filter_form', response.context)
        self.assertIn('company_address', response.context)
        self.assertEqual(response.context['company_address'], MOCK_COMPANY_DATA[0]['address'])
        
        # Verify that get_report_data was called (for initial table load via HTMX)
        mock_get_report_data.assert_called_with(
            company_id=1, financial_year_id=2023, m_param='null', z_param='null', p_param='null', q_param='null'
        )
        mock_get_company_address.assert_called_with(1)

    @patch('project_management.models.OnSiteAttendanceRecord.get_report_data')
    @patch('project_management.models.CompanyProfile.get_company_address')
    def test_report_table_partial_view_htmx(self, mock_get_company_address, mock_get_report_data):
        """Test the HTMX partial view for the report table."""
        mock_get_report_data.return_value = [
            OnSiteAttendanceRecord(**MOCK_ATTENDANCE_DATA[0])
        ]
        mock_get_company_address.return_value = MOCK_COMPANY_DATA[0]['address']

        # Simulate HTMX request with filters
        response = self.client.get(
            reverse('onsiteattendancerecord_table'),
            {'m': 'Alpha', 'z': '2023-10-01'},
            HTTP_HX_REQUEST='true' # Important: simulate HTMX request
        )

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendancerecord/_onsiteattendancerecord_table.html')
        self.assertIn('onsiteattendancerecords', response.context)
        self.assertEqual(len(response.context['onsiteattendancerecords']), 1)
        self.assertContains(response, 'Alice Smith')
        self.assertContains(response, 'Alpha')
        self.assertContains(response, MOCK_COMPANY_DATA[0]['address'])

        mock_get_report_data.assert_called_with(
            company_id=1, financial_year_id=2023, m_param='Alpha', z_param='2023-10-01', p_param='null', q_param='null'
        )
        mock_get_company_address.assert_called_with(1)

    @patch('project_management.models.OnSiteAttendanceRecord.get_report_data')
    @patch('project_management.models.CompanyProfile.get_company_address')
    def test_report_table_partial_view_empty_data(self, mock_get_company_address, mock_get_report_data):
        """Test the HTMX partial view when no data is found."""
        mock_get_report_data.return_value = []
        mock_get_company_address.return_value = MOCK_COMPANY_DATA[0]['address']

        response = self.client.get(
            reverse('onsiteattendancerecord_table'),
            {'m': 'NonExistentProject'},
            HTTP_HX_REQUEST='true'
        )

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendancerecord/_onsiteattendancerecord_table.html')
        self.assertContains(response, 'No on-site attendance records found for the selected criteria.')
        mock_get_report_data.assert_called_with(
            company_id=1, financial_year_id=2023, m_param='NonExistentProject', z_param='null', p_param='null', q_param='null'
        )

    # --- Generic CRUD Views Tests (for completeness as per prompt) ---
    # These tests assume a direct mapping to DB for CRUD which is not the primary use-case
    # for the original report page, but required by the provided template.
    # We mock the ORM behavior for OnSiteAttendanceRecord to simulate interaction.

    @patch('project_management.models.OnSiteAttendanceRecord.objects.create')
    def test_create_view_get(self, mock_create):
        response = self.client.get(reverse('onsiteattendancerecord_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendancerecord/form.html')
        self.assertContains(response, 'Add On-Site Attendance Record')

    @patch('project_management.models.OnSiteAttendanceRecord.objects.create')
    def test_create_view_post_htmx(self, mock_create):
        mock_instance = MagicMock()
        mock_instance.pk = 1
        mock_create.return_value = mock_instance # Ensure create returns an instance with pk

        # Since our form has no fields, we send empty data, which should still be valid.
        # If the model had fields, we'd include them here.
        data = {} 
        response = self.client.post(reverse('onsiteattendancerecord_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX POST
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnSiteAttendanceRecordList')
        mock_create.assert_called_once() # Verify create was called.

    @patch('project_management.models.OnSiteAttendanceRecord.objects.get')
    @patch('project_management.models.OnSiteAttendanceRecord.objects.filter') # For save() method on ModelForm
    def test_update_view_get(self, mock_filter, mock_get):
        mock_record = MagicMock(pk=1, employee_name='Test', attendance_date=date(2023,1,1))
        mock_get.return_value = mock_record
        mock_filter.return_value = MagicMock(exists=MagicMock(return_value=True)) # For update view to find object
        
        response = self.client.get(reverse('onsiteattendancerecord_edit', args=[1]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendancerecord/form.html')
        self.assertContains(response, 'Edit On-Site Attendance Record')
        mock_get.assert_called_once_with(pk=1)

    @patch('project_management.models.OnSiteAttendanceRecord.objects.get')
    @patch('project_management.models.OnSiteAttendanceRecord.save') # Patch the save method of the instance
    def test_update_view_post_htmx(self, mock_save, mock_get):
        mock_record = MagicMock(pk=1, employee_name='Test', attendance_date=date(2023,1,1))
        mock_get.return_value = mock_record

        data = {} # Empty data as form has no fields
        response = self.client.post(reverse('onsiteattendancerecord_edit', args=[1]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnSiteAttendanceRecordList')
        mock_save.assert_called_once() # Verify save was called on the instance

    @patch('project_management.models.OnSiteAttendanceRecord.objects.get')
    def test_delete_view_get(self, mock_get):
        mock_record = MagicMock(pk=1, employee_name='Test', attendance_date=date(2023,1,1))
        mock_get.return_value = mock_record

        response = self.client.get(reverse('onsiteattendancerecord_delete', args=[1]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/onsiteattendancerecord/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        mock_get.assert_called_once_with(pk=1)

    @patch('project_management.models.OnSiteAttendanceRecord.objects.get')
    def test_delete_view_post_htmx(self, mock_get):
        mock_record = MagicMock(pk=1, employee_name='Test', attendance_date=date(2023,1,1))
        mock_record.delete = MagicMock() # Mock the delete method of the instance
        mock_get.return_value = mock_record

        response = self.client.post(reverse('onsiteattendancerecord_delete', args=[1]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnSiteAttendanceRecordList')
        mock_record.delete.assert_called_once() # Verify delete was called on the instance

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates are designed to leverage HTMX for all dynamic interactions, specifically:

*   **Initial Load & Filtering:** The `list.html` uses `hx-get` on a `div` container to load the initial DataTables content (`_onsiteattendancerecord_table.html`) on page load. The filter form also uses `hx-get` to refresh this container when filters are applied, without a full page reload.
*   **CRUD Modals:** Buttons for "Add New", "Edit", and "Delete" use `hx-get` to fetch their respective forms (`form.html`, `confirm_delete.html`) into a modal (`#modalContent`).
*   **Form Submission:** Forms within the modal use `hx-post` with `hx-swap="none"` and `hx-trigger="refreshOnSiteAttendanceRecordList"` in the HTTP headers (from the view's `HttpResponse`) to signal the main list to re-render after a successful operation.
*   **Modal Management:** Alpine.js is used via `_="on click add .is-active to #modal"` to open the modal and `_="on click if event.target.id == 'modal' remove .is-active from me"` to close it when clicking outside.
*   **DataTables:** The `_onsiteattendancerecord_table.html` partial includes a `script` block to re-initialize DataTables after HTMX loads new table content, ensuring client-side searching, sorting, and pagination work seamlessly. This script also destroys any previous DataTable instance to prevent conflicts.
*   **Loading Indicators:** HTMX provides implicit loading indicators (e.g., `hx-indicator` attribute can be used, or CSS classes applied to target elements) automatically when requests are in progress, eliminating the need for `loadingNotifier.js`. A simple loading spinner is included in the `list.html` as fallback.

**Business Benefits:**
This modernization provides significant business value:

1.  **Enhanced User Experience:** Replacing Crystal Reports with dynamic HTML tables powered by DataTables, HTMX, and Alpine.js offers a much faster, more interactive, and modern user interface. Users can filter, sort, and paginate data instantly without full page refreshes, improving productivity and satisfaction.
2.  **Cost Reduction & Maintainability:** Migrating away from proprietary Crystal Reports reduces licensing costs and reliance on a specialized reporting tool. Django provides a robust, open-source, and widely supported framework, making the application easier and cheaper to maintain, extend, and find developers for.
3.  **Modern Architecture:** The "Fat Model, Thin View" approach with strict separation of concerns makes the codebase more organized, testable, and scalable. Business logic is centralized in models, leading to more reliable and reusable code.
4.  **Automation-Ready:** The systematic breakdown into distinct Django components and the emphasis on HTMX for frontend interactions naturally lends itself to AI-assisted automation, significantly reducing manual coding effort during the migration process.
5.  **Improved Performance:** By fetching only the necessary data for the table via HTMX and rendering it client-side with DataTables, the application reduces server load and bandwidth usage, leading to faster response times.

**Final Notes:**

*   **Placeholders:** Remember to replace hypothetical table names (`tbl_on_site_attendance_data`, `tbl_company_profile`) and column names (`EmployeeId`, `ProjectName`, etc.) with their actual values from your database schema after a thorough analysis.
*   **Data Loading for `managed=False`:** For the `OnSiteAttendanceRecord` model, the `get_report_data` method currently simulates data retrieval via Django ORM on the assumed underlying table. In a real scenario, this method would be updated to execute the actual `GetOnSiteEmp_Print` stored procedure using Django's raw SQL execution capabilities (`connection.cursor()`) or a more complex ORM query that mimics the SP's joins and logic.
*   **Session Management:** The retrieval of `company_id` and `financial_year_id` from `request.session` needs to be properly integrated with your Django authentication and session management system.
*   **Error Handling:** Enhance error handling in model methods and views for robustness (e.g., handling invalid date formats from query parameters, database connection issues).
*   **Authentication/Authorization:** Implement Django's authentication and authorization mechanisms (`LoginRequiredMixin`, permissions) to secure the report access.