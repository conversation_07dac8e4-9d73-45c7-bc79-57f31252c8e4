## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code for `Dashboard.aspx` and its empty `Page_Load` method in `Dashboard.aspx.cs` does not explicitly contain any database-related elements, `SqlDataSource`, connection strings, or direct SQL commands. Therefore, we cannot directly extract the database schema from the given input.

**Assumption for Modernization:**
Since the page is named "Dashboard," it's logical to assume it would display various components or "widgets" that summarize data. For the purpose of demonstrating a comprehensive Django migration, we will infer a hypothetical database table and its corresponding Django model, representing configurable dashboard widgets.

*   **Identified Table Name:** `tbl_dashboard_widgets`
*   **Inferred Columns:**
    *   `id` (Primary Key, Auto-increment)
    *   `widget_title` (e.g., `VARCHAR(255)`)
    *   `widget_description` (e.g., `TEXT`)
    *   `widget_type` (e.g., `VARCHAR(50)`, e.g., 'chart', 'table', 'kpi')
    *   `display_order` (e.g., `INT`)
    *   `is_active` (e.g., `BOOLEAN`)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The provided ASP.NET `Page_Load` method is empty, and the `.aspx` file only contains content placeholders without any server-side controls like `GridView`, `Button`, or `TextBox` that would indicate specific data operations. Therefore, no explicit Create, Read, Update, or Delete (CRUD) operations can be identified from the given code.

**Assumption for Modernization:**
While the existing code doesn't show CRUD, a modern dashboard system would typically allow administrators to manage (Create, Read, Update, Delete) the individual widgets displayed on it. We will implement full CRUD functionality for our hypothetical `DashboardWidget` model to provide a complete and robust solution in Django. Read operations (displaying the list of widgets) would be the primary function of a dashboard.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Instructions:**
The `Dashboard.aspx` file only contains `<asp:Content>` tags mapping to master page placeholders, without any specific UI controls such as `GridView`, `TextBox`, `DropDownlist`, or `Button`. No client-side JavaScript is explicitly defined beyond a `loadingNotifier.js` script, which is a generic utility.

**Assumption for Modernization:**
In a modern Django application serving a dashboard, we would expect:
*   A main view displaying a list of "dashboard widgets" (or whatever the components are).
*   Buttons or links to add new widgets, edit existing ones, and delete them.
*   Forms for capturing widget details.
*   Client-side interactions for modal pop-ups for forms, and dynamic content updates without full page reloads.

### Step 4: Generate Django Code

Based on the analysis and assumptions, we will generate the Django code for a `DashboardWidget` module.

**App Name:** `dashboard` (within the `Module_ProjectManagement` logical grouping, we can consider `dashboard` as a specific app).

### 4.1 Models

Task: Create a Django model based on the database schema.

**Instructions:**
The model `DashboardWidget` will correspond to the `tbl_dashboard_widgets` table.

```python
# dashboard/models.py
from django.db import models

class DashboardWidget(models.Model):
    widget_id = models.AutoField(db_column='id', primary_key=True)
    title = models.CharField(db_column='widget_title', max_length=255, verbose_name="Widget Title")
    description = models.TextField(db_column='widget_description', blank=True, null=True, verbose_name="Description")
    widget_type = models.CharField(db_column='widget_type', max_length=50, verbose_name="Type")
    display_order = models.IntegerField(db_column='display_order', default=0, verbose_name="Display Order")
    is_active = models.BooleanField(db_column='is_active', default=True, verbose_name="Active")

    class Meta:
        managed = False  # Set to False because the table already exists in the legacy database
        db_table = 'tbl_dashboard_widgets'
        verbose_name = 'Dashboard Widget'
        verbose_name_plural = 'Dashboard Widgets'
        ordering = ['display_order', 'title'] #sensible default ordering

    def __str__(self):
        return self.title
        
    # Business logic methods related to a Dashboard Widget could go here.
    # For instance, a method to determine if a widget should be displayed based on its type and activity status.
    def should_display(self):
        """
        Determines if the widget is active and ready for display.
        """
        return self.is_active and self.widget_type in ['chart', 'table', 'kpi'] # Example logic
```

### 4.2 Forms

Task: Define a Django form for user input.

**Instructions:**
Create a `ModelForm` for `DashboardWidget` to handle data input and validation.

```python
# dashboard/forms.py
from django import forms
from .models import DashboardWidget

class DashboardWidgetForm(forms.ModelForm):
    class Meta:
        model = DashboardWidget
        fields = ['title', 'description', 'widget_type', 'display_order', 'is_active']
        widgets = {
            'title': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'widget_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'display_order': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        labels = {
            'title': 'Widget Title',
            'description': 'Description',
            'widget_type': 'Type',
            'display_order': 'Order',
            'is_active': 'Active',
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Example: Populate widget_type choices dynamically or restrict them
        self.fields['widget_type'].choices = [
            ('chart', 'Chart'),
            ('table', 'Table'),
            ('kpi', 'KPI'),
            ('text', 'Text Block'),
        ]

    # Example of custom validation: ensure display_order is positive
    def clean_display_order(self):
        display_order = self.cleaned_data.get('display_order')
        if display_order is not None and display_order < 0:
            raise forms.ValidationError("Display order cannot be negative.")
        return display_order
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

**Instructions:**
Define `ListView`, `CreateView`, `UpdateView`, `DeleteView` for `DashboardWidget`, along with a partial view for HTMX table loading.

```python
# dashboard/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardWidget
from .forms import DashboardWidgetForm

class DashboardWidgetListView(ListView):
    model = DashboardWidget
    template_name = 'dashboard/dashboardwidget/list.html'
    context_object_name = 'dashboard_widgets' # Renamed for clarity

class DashboardWidgetTablePartialView(ListView):
    model = DashboardWidget
    template_name = 'dashboard/dashboardwidget/_dashboardwidget_table.html'
    context_object_name = 'dashboard_widgets'

class DashboardWidgetCreateView(CreateView):
    model = DashboardWidget
    form_class = DashboardWidgetForm
    template_name = 'dashboard/dashboardwidget/form.html'
    success_url = reverse_lazy('dashboardwidget_list') # Redirects to main list view after successful creation

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Widget added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success for HTMX
                headers={
                    'HX-Trigger': 'refreshDashboardWidgetList' # Custom HTMX event to refresh the list
                }
            )
        return response

class DashboardWidgetUpdateView(UpdateView):
    model = DashboardWidget
    form_class = DashboardWidgetForm
    template_name = 'dashboard/dashboardwidget/form.html'
    success_url = reverse_lazy('dashboardwidget_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Widget updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardWidgetList'
                }
            )
        return response

class DashboardWidgetDeleteView(DeleteView):
    model = DashboardWidget
    template_name = 'dashboard/dashboardwidget/confirm_delete.html'
    success_url = reverse_lazy('dashboardwidget_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Widget deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshDashboardWidgetList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

**Instructions:**
Templates will be created under `dashboard/templates/dashboard/dashboardwidget/`.

**List Template (`dashboard/templates/dashboard/dashboardwidget/list.html`):**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Dashboard Widgets</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'dashboardwidget_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Widget
        </button>
    </div>
    
    <div id="dashboardwidgetTable-container"
         hx-trigger="load, refreshDashboardWidgetList from:body"
         hx-get="{% url 'dashboardwidget_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Dashboard Widgets...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirm_delete -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
             _="on htmx:afterSwap remove .is-active from #modal end">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
        // For example, if you had togglable sections or interactive elements
        // that require local state management beyond HTMX's capabilities.
    });
</script>
{% endblock %}
```

**Table Partial Template (`dashboard/templates/dashboard/dashboardwidget/_dashboardwidget_table.html`):**

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="dashboardwidgetTable" class="min-w-full bg-white divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in dashboard_widgets %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ obj.title }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ obj.widget_type }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">{{ obj.display_order }}</td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm text-gray-900">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                        {{ obj.is_active|yesno:"Yes,No" }}
                    </span>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2"
                        hx-get="{% url 'dashboardwidget_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md"
                        hx-get="{% url 'dashboardwidget_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No dashboard widgets found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// Ensure DataTables is initialized only once per HTMX swap if necessary,
// or destroy existing instance before re-initializing.
// Best practice is to run this on htmx:afterSwap on the parent element
// or ensure the script tag is inside the hx-swap target.
$(document).ready(function() {
    // Destroy existing DataTable instance if it exists to prevent re-initialization issues
    if ($.fn.DataTable.isDataTable('#dashboardwidgetTable')) {
        $('#dashboardwidgetTable').DataTable().destroy();
    }
    $('#dashboardwidgetTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

**Form Partial Template (`dashboard/templates/dashboard/dashboardwidget/_dashboardwidget_form.html`):**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Widget</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="response-targets">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**Delete Confirmation Template (`dashboard/templates/dashboard/dashboardwidget/_dashboardwidget_confirm_delete.html`):**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the dashboard widget: <strong>{{ object.title }}</strong>?</p>
    
    <form hx-post="{% url 'dashboardwidget_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

**Instructions:**
Create a `urls.py` file within the `dashboard` app.

```python
# dashboard/urls.py
from django.urls import path
from .views import (
    DashboardWidgetListView, 
    DashboardWidgetCreateView, 
    DashboardWidgetUpdateView, 
    DashboardWidgetDeleteView,
    DashboardWidgetTablePartialView
)

urlpatterns = [
    path('dashboardwidgets/', DashboardWidgetListView.as_view(), name='dashboardwidget_list'),
    path('dashboardwidgets/add/', DashboardWidgetCreateView.as_view(), name='dashboardwidget_add'),
    path('dashboardwidgets/edit/<int:pk>/', DashboardWidgetUpdateView.as_view(), name='dashboardwidget_edit'),
    path('dashboardwidgets/delete/<int:pk>/', DashboardWidgetDeleteView.as_view(), name='dashboardwidget_delete'),
    path('dashboardwidgets/table/', DashboardWidgetTablePartialView.as_view(), name='dashboardwidget_table'), # HTMX partial view
]
```
**Remember to include this app's URLs in your project's main `urls.py`:**
```python
# project_name/urls.py (main project urls.py)
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('projectmanagement/', include('dashboard.urls')), # Example inclusion
]
```

### 4.6 Tests

Task: Write tests for the model and views.

**Instructions:**
Include comprehensive unit tests for model methods and properties, and integration tests for all views.

```python       
# dashboard/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import DashboardWidget

class DashboardWidgetModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.widget1 = DashboardWidget.objects.create(
            title='Sales Chart',
            description='Monthly sales overview',
            widget_type='chart',
            display_order=10,
            is_active=True
        )
        cls.widget2 = DashboardWidget.objects.create(
            title='Inactive Report',
            description='Report that should not be displayed',
            widget_type='table',
            display_order=20,
            is_active=False
        )
  
    def test_dashboard_widget_creation(self):
        obj = DashboardWidget.objects.get(title='Sales Chart')
        self.assertEqual(obj.description, 'Monthly sales overview')
        self.assertEqual(obj.widget_type, 'chart')
        self.assertTrue(obj.is_active)
        self.assertEqual(obj.display_order, 10)
        
    def test_title_label(self):
        obj = DashboardWidget.objects.get(widget_id=self.widget1.widget_id)
        field_label = obj._meta.get_field('title').verbose_name
        self.assertEqual(field_label, 'Widget Title')
        
    def test_str_method(self):
        obj = DashboardWidget.objects.get(widget_id=self.widget1.widget_id)
        self.assertEqual(str(obj), 'Sales Chart')

    def test_should_display_method(self):
        self.assertTrue(self.widget1.should_display())
        self.assertFalse(self.widget2.should_display()) # Inactive widget
        
        # Test a widget type not in display list
        widget3 = DashboardWidget.objects.create(
            title='Draft Widget',
            description='Just a draft',
            widget_type='draft', # Not 'chart', 'table', 'kpi'
            display_order=30,
            is_active=True
        )
        self.assertFalse(widget3.should_display())


class DashboardWidgetViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.widget = DashboardWidget.objects.create(
            title='Existing Widget',
            description='This widget exists',
            widget_type='kpi',
            display_order=1,
            is_active=True
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view_get(self):
        response = self.client.get(reverse('dashboardwidget_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboardwidget/list.html')
        self.assertContains(response, 'Dashboard Widgets')
        self.assertIsInstance(response.context['dashboard_widgets'].first(), DashboardWidget)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('dashboardwidget_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboardwidget/_dashboardwidget_table.html')
        self.assertContains(response, self.widget.title)
        self.assertContains(response, 'dashboardwidgetTable') # Check for DataTables ID

    def test_create_view_get(self):
        response = self.client.get(reverse('dashboardwidget_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboardwidget/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Dashboard Widget')
        
    def test_create_view_post_success(self):
        data = {
            'title': 'New Test Widget',
            'description': 'A description for the new widget',
            'widget_type': 'chart',
            'display_order': 5,
            'is_active': 'on', # Checkbox sends 'on' or nothing
        }
        response = self.client.post(reverse('dashboardwidget_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX success should return 204 No Content with HX-Trigger
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDashboardWidgetList')
        
        self.assertTrue(DashboardWidget.objects.filter(title='New Test Widget').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Widget added successfully.')

    def test_create_view_post_invalid(self):
        data = {
            'title': '', # Invalid data
            'description': 'Invalid widget',
            'widget_type': 'chart',
            'display_order': -1, # Invalid order
        }
        response = self.client.post(reverse('dashboardwidget_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors
        self.assertTemplateUsed(response, 'dashboard/dashboardwidget/form.html')
        self.assertFormError(response.context['form'], 'title', ['This field is required.'])
        self.assertFormError(response.context['form'], 'display_order', ['Display order cannot be negative.'])

    def test_update_view_get(self):
        response = self.client.get(reverse('dashboardwidget_edit', args=[self.widget.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboardwidget/form.html')
        self.assertContains(response, 'Edit Dashboard Widget')
        self.assertContains(response, self.widget.title)
        
    def test_update_view_post_success(self):
        data = {
            'title': 'Updated Widget Title',
            'description': 'Updated description',
            'widget_type': 'table',
            'display_order': 2,
            'is_active': 'on',
        }
        response = self.client.post(reverse('dashboardwidget_edit', args=[self.widget.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDashboardWidgetList')
        
        self.widget.refresh_from_db()
        self.assertEqual(self.widget.title, 'Updated Widget Title')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Widget updated successfully.')

    def test_delete_view_get(self):
        response = self.client.get(reverse('dashboardwidget_delete', args=[self.widget.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'dashboard/dashboardwidget/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.widget.title)

    def test_delete_view_post_success(self):
        # Create a new widget to delete so we don't interfere with setUpTestData's widget
        widget_to_delete = DashboardWidget.objects.create(
            title='Temp Widget to Delete',
            description='Temporary',
            widget_type='chart',
            display_order=99,
            is_active=True
        )
        initial_count = DashboardWidget.objects.count()
        response = self.client.post(reverse('dashboardwidget_delete', args=[widget_to_delete.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshDashboardWidgetList')
        
        self.assertEqual(DashboardWidget.objects.count(), initial_count - 1)
        self.assertFalse(DashboardWidget.objects.filter(pk=widget_to_delete.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Dashboard Widget deleted successfully.')

    def test_hx_request_header_handling(self):
        # Test that non-HTMX requests behave normally (e.g., redirect)
        data = {
            'title': 'Another Widget',
            'description': 'Non-HTMX test',
            'widget_type': 'kpi',
            'display_order': 7,
            'is_active': 'on',
        }
        response = self.client.post(reverse('dashboardwidget_add'), data) # No HTTP_HX_REQUEST header
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertTrue(DashboardWidget.objects.filter(title='Another Widget').exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
As demonstrated in the templates and views:

*   **HTMX for dynamic updates:**
    *   The `dashboardwidget_list.html` uses `hx-get="{% url 'dashboardwidget_table' %}"` on a container `div` with `hx-trigger="load, refreshDashboardWidgetList from:body"` to load the DataTable via AJAX on page load and whenever the `refreshDashboardWidgetList` custom event is triggered (e.g., after a CRUD operation).
    *   Buttons for "Add New Widget", "Edit", and "Delete" use `hx-get` to fetch the form or confirmation partial into a modal (`#modalContent`).
    *   Forms (in `_dashboardwidget_form.html` and `_dashboardwidget_confirm_delete.html`) use `hx-post` for submission. The views respond with `status=204` and `HX-Trigger` to instruct HTMX to refresh the main list after successful operations without a full page reload or content swap on the form itself. This makes the modal disappear and the list update automatically.
*   **Alpine.js for UI state management:**
    *   Alpine.js is used for simple UI state, such as managing the visibility of the modal: `_="on click add .is-active to #modal"` to show it and `_="on click if event.target.id == 'modal' remove .is-active from me"` to close it when clicking outside. `_="on htmx:afterSwap remove .is-active from #modal end"` ensures the modal closes after the form is submitted and processed by HTMX.
*   **DataTables for list views:**
    *   The `_dashboardwidget_table.html` partial directly contains the `<table>` element with the ID `dashboardwidgetTable`. A JavaScript block within this partial initializes DataTables on this table once it's loaded by HTMX. This ensures client-side searching, sorting, and pagination. Logic is included to re-initialize DataTables safely on subsequent HTMX loads.
*   **No full page reloads:** All CRUD operations and data list updates are handled dynamically using HTMX.
*   **DRY templates:** The use of partial templates (`_dashboardwidget_table.html`, `_dashboardwidget_form.html`, `_dashboardwidget_confirm_delete.html`) ensures that reusable components are not duplicated.
*   **No custom JavaScript requirements (beyond HTMX/Alpine/DataTables CDNs):** The solution relies purely on the declared libraries and HTMX attributes.

---

## Final Notes

This comprehensive plan provides a blueprint for migrating the conceptual "Dashboard" functionality from ASP.NET to Django. Even with minimal initial code, this approach demonstrates the modernization principles:

*   **Business-Oriented:** The focus remains on "Dashboard Widgets" as a business entity, rather than low-level technical details.
*   **Automation Ready:** The structure and patterns provided are highly amenable to AI-assisted code generation, where models, forms, views, URLs, and tests can be systematically produced once the database schema and desired functionality are identified or inferred.
*   **Modern Stack:** Leverages Django 5.0+, HTMX, Alpine.js, and Tailwind CSS for a highly efficient, responsive, and maintainable web application.
*   **Clear Separation of Concerns:** Business logic is concentrated in the model (fat model), views are thin, and presentation logic is strictly handled in templates, using HTMX for dynamic interactions.
*   **Robustness:** Includes comprehensive tests to ensure reliability and maintainability of the migrated components.

This detailed plan allows for systematic conversion and provides a clear pathway for non-technical stakeholders to understand the benefits and progression of the modernization initiative.