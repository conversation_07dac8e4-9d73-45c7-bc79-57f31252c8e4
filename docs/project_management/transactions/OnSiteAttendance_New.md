This modernization plan outlines the transition of your ASP.NET On-Site Attendance module to a robust, scalable, and modern Django application. Our approach prioritizes automation and leverages Django's 'Fat Model, Thin View' architecture, HTMX for dynamic interactions, and DataTables for superior data presentation, all while ensuring a clean and maintainable codebase.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with several database entities: `BusinessGroup`, inferred `Employee` data via a stored procedure (`GetBGEmp`), and `tblOnSiteAttendance_Master` for storing attendance records.

**Identified Tables and Columns:**

1.  **`BusinessGroup`** (from `SqlDataBG`):
    *   `Id` (Primary Key, integer)
    *   `Symbol` (text, e.g., varchar)

2.  **Inferred `Employee` Data** (from `GetBGEmp` stored procedure and `GridView4` bindings):
    *   `UserID` (Primary Key, integer or string, inferred as Employee ID)
    *   `EmpName` (text)
    *   `BG` (Business Group ID, foreign key to `BusinessGroup.Id`)
    *   `Hours` (integer, Shift Hours)

3.  **`tblOnSiteAttendance_Master`** (where records are inserted by `btnAdd_Click`):
    *   `Id` (Primary Key, auto-increment integer)
    *   `CompId` (Company ID, integer)
    *   `SessionId` (Session ID/Username, string)
    *   `SysDate` (System Date, date)
    *   `SysTime` (System Time, time)
    *   `OnSiteDate` (Attendance Date, date)
    *   `EmpId` (Employee ID, foreign key to inferred `Employee.UserID`)
    *   `Shift` (integer, 0 for Day, 1 for Night)
    *   `Status` (integer, 0 for Present, 1 for Absent)
    *   `Onsite` (text, details if on-site)
    *   `FromTime` (Time, start time of shift)
    *   `FinYearId` (Financial Year ID, integer)

### Step 2: Identify Backend Functionality

The application primarily performs read (search/filter) and create (bulk insert/update) operations on attendance records.

*   **Read Operation (`FillGrid`):**
    *   Retrieves a list of employees for a specific `OnSiteDate` and `BusinessGroup` using the `GetBGEmp` stored procedure.
    *   The `GridView4` then displays these employees along with their current attendance status (if already recorded) and input fields for new entries.

*   **Create/Update Operation (`btnAdd_Click`):**
    *   Validates the selected `OnSiteDate` (must be current or future).
    *   Iterates through each employee row displayed in the `GridView4`.
    *   If an employee's checkbox (`ck`) is checked and 'If On Site' text box (`txtOnsite`) is filled (if applicable), it extracts their `EmpId`, `Shift`, `Status`, `Onsite` details, and `FromTime`.
    *   Checks if an attendance record for that employee and `OnSiteDate` already exists in `tblOnSiteAttendance_Master`.
    *   If no record exists, a new attendance record is inserted into `tblOnSiteAttendance_Master`. The original code implies an insert-only approach for new records; existing records are not explicitly updated here, but the check for existence suggests it avoids duplicate inserts. For Django, we will handle this as an `update_or_create` or similar pattern for robustness.

*   **Validation Logic:**
    *   Date input (`textChequeDate`): Required, specific format, and must be on or after the current system date.
    *   'If On Site' text (`txtOnsite`): Required only if the corresponding employee checkbox (`ck`) is selected.
    *   Overall form submission requires at least one valid entry with `ck` checked and `txtOnsite` filled if `ck` is checked.

### Step 3: Infer UI Components

The ASP.NET controls map directly to standard HTML form elements, which Django forms can easily represent. HTMX and Alpine.js will handle the dynamic aspects.

*   **Date Selection:** `asp:TextBox ID="textChequeDate"` with `CalendarExtender` converts to an HTML `<input type="date">` for native browser pickers, or an Alpine.js-driven date picker.
*   **Business Group Selection:** `asp:DropDownList ID="drpGroupF"` converts to an HTML `<select>` element.
*   **Search Button:** `asp:Button ID="btnProceed"` converts to an HTML `<button>` with HTMX attributes to trigger data loading.
*   **Employee List/Attendance Form:** `asp:GridView ID="GridView4"` is a complex component. This will be converted into a Django `FormSet` rendered as an HTML `<table>` with DataTables enabled for enhanced client-side features.
    *   `asp:CheckBox ID="ck"` -> HTML `<input type="checkbox">`
    *   `asp:Label ID="lblEmp"` -> Display employee name.
    *   `asp:RadioButtonList` (Shift, Status) -> HTML `<input type="radio">` groups.
    *   `asp:TextBox ID="txtOnsite"` -> HTML `<textarea>`.
    *   `MKB:TimeSelector ID="FTime"` -> HTML `<input type="time">` or two `<input type="number">` fields for hour and minute, possibly with Alpine.js for an enhanced picker. We'll opt for standard HTML `type="time"` for simplicity unless a full custom picker is specifically required.
*   **Save Button:** `asp:Button ID="btnAdd"` converts to an HTML `<button type="submit">` within the formset, using HTMX to submit the entire attendance form.

### Step 4: Generate Django Code

We'll define an Django application named `attendance`.

#### 4.1 Models (`attendance/models.py`)

We will define three models to reflect the database schema, adhering to `managed = False` as these tables likely pre-exist.

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, time

# Constants for choices
SHIFT_CHOICES = [
    (0, 'Day'),
    (1, 'Night'),
]

STATUS_CHOICES = [
    (0, 'Present'),
    (1, 'Absent'),
]

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=100) # Assuming a reasonable max_length

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class Employee(models.Model):
    # UserID is likely the primary key for the Employee
    user_id = models.CharField(db_column='UserID', primary_key=True, max_length=50) # Assuming UserID is a string/varchar
    emp_name = models.CharField(db_column='EmpName', max_length=255)
    bg_group = models.ForeignKey(BusinessGroup, on_delete=models.DO_NOTHING, db_column='BG', related_name='employees')
    hours = models.IntegerField(db_column='Hours', null=True, blank=True) # Shift Hours

    class Meta:
        managed = False
        db_table = 'Employee' # Assuming a table name for employees
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.emp_name

class OnSiteAttendance(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True)
    company_id = models.IntegerField(db_column='CompId')
    session_id = models.CharField(db_column='SessionId', max_length=255)
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    on_site_date = models.DateField(db_column='OnSiteDate')
    employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='EmpId', related_name='on_site_attendances')
    shift_type = models.IntegerField(db_column='Shift', choices=SHIFT_CHOICES)
    status = models.IntegerField(db_column='Status', choices=STATUS_CHOICES)
    on_site_details = models.TextField(db_column='Onsite', null=True, blank=True)
    from_time = models.TimeField(db_column='FromTime', null=True, blank=True)
    financial_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblOnSiteAttendance_Master'
        verbose_name = 'On-Site Attendance'
        verbose_name_plural = 'On-Site Attendances'
        unique_together = (('company_id', 'on_site_date', 'employee'),) # Ensure no duplicate entries for same company, date, employee

    def __str__(self):
        return f"{self.employee.emp_name} - {self.on_site_date}"

    @classmethod
    def process_attendance_records(cls, formset, user, company_id, financial_year_id):
        """
        Processes a formset of OnSiteAttendance forms, handling creation/update and business logic.
        This method encapsulates the logic from btnAdd_Click.
        """
        current_datetime = timezone.now()
        sys_date = current_datetime.date()
        sys_time = current_datetime.time()
        session_id = user.username # Assuming user.username maps to SessionId

        records_processed = 0
        errors = []

        # Convert formset.cleaned_data to a dictionary for easier lookup
        # This is for formset.changed_objects and formset.new_objects
        valid_forms_data = {
            f.instance.employee.user_id if f.instance.pk else f.cleaned_data['employee'].user_id: f for f in formset.forms if f.is_valid()
        }

        for form in formset.forms:
            if not form.is_valid():
                errors.append(f"Validation error for employee {form.instance.employee.emp_name if form.instance.pk else 'new employee'}: {form.errors}")
                continue # Skip invalid forms

            # Only process forms that are selected or have existing data being updated
            if not form.cleaned_data.get('is_selected') and not form.instance.pk:
                continue # Skip unselected new rows

            on_site_date = form.cleaned_data['on_site_date']

            # Business rule: Attendance date must be current or future
            if on_site_date < sys_date:
                errors.append(f"Invalid date for {form.cleaned_data['employee'].emp_name}: Attendance date cannot be in the past.")
                continue

            # Original ASP.NET code specifically checked for inserts, not updates.
            # We'll use update_or_create to handle both scenarios robustly.
            employee_obj = form.cleaned_data['employee']

            # Prepare data for insertion/update
            data = {
                'company_id': company_id,
                'session_id': session_id,
                'sys_date': sys_date,
                'sys_time': sys_time,
                'on_site_date': on_site_date,
                'employee': employee_obj,
                'shift_type': form.cleaned_data['shift_type'],
                'status': form.cleaned_data['status'],
                'on_site_details': form.cleaned_data['on_site_details'] if form.cleaned_data.get('is_selected') else '', # Only save details if selected
                'from_time': form.cleaned_data['from_time'],
                'financial_year_id': financial_year_id,
            }

            try:
                # Use update_or_create based on unique_together constraints
                obj, created = cls.objects.update_or_create(
                    company_id=company_id,
                    on_site_date=on_site_date,
                    employee=employee_obj,
                    defaults=data # Data to set or update
                )
                records_processed += 1
            except Exception as e:
                errors.append(f"Database error for {employee_obj.emp_name}: {e}")

        return records_processed, errors

```

#### 4.2 Forms (`attendance/forms.py`)

We'll need a search form and a form for each attendance record in the grid, bundled into a formset.

```python
from django import forms
from django.forms import modelformset_factory
from .models import BusinessGroup, Employee, OnSiteAttendance, SHIFT_CHOICES, STATUS_CHOICES
from django.utils import timezone
from datetime import date

class AttendanceSearchForm(forms.Form):
    on_site_date = forms.DateField(
        label="Select Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'readonly': 'readonly' # Match ASP.NET readonly attribute
        }),
        initial=timezone.now().date(),
        required=True
    )
    business_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all(),
        label="Select BG Group",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
        }),
        required=True
    )

    def clean_on_site_date(self):
        selected_date = self.cleaned_data['on_site_date']
        if selected_date < timezone.now().date():
            raise forms.ValidationError("Attendance date cannot be in the past.")
        return selected_date

class OnSiteAttendanceForm(forms.ModelForm):
    # Add a checkbox field that corresponds to the ASP.NET 'ck' checkbox
    is_selected = forms.BooleanField(
        required=False,
        label="", # No label needed for this checkbox
        widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-4 w-4 text-blue-600'})
    )
    # The employee field will be used to pass the employee object for the form
    employee = forms.ModelChoiceField(queryset=Employee.objects.all(), widget=forms.HiddenInput())
    # The on_site_date will be pre-filled from the search form and passed here
    on_site_date = forms.DateField(widget=forms.HiddenInput(), required=True)

    # Override shift_type and status to use RadioSelect for better UI mirroring
    shift_type = forms.ChoiceField(
        choices=SHIFT_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'inline-block mr-2'})
    )
    status = forms.ChoiceField(
        choices=STATUS_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'inline-block mr-2'})
    )

    class Meta:
        model = OnSiteAttendance
        # Note: 'company_id', 'session_id', 'sys_date', 'sys_time', 'financial_year_id'
        # will be set in the view/model method, not directly from the form.
        fields = ['is_selected', 'employee', 'on_site_date', 'shift_type', 'status', 'on_site_details', 'from_time']
        widgets = {
            'on_site_details': forms.Textarea(attrs={
                'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 2 # Mimic Height="40px"
            }),
            'from_time': forms.TimeInput(attrs={
                'type': 'time', # Use HTML5 time input for simplicity
                'class': 'box3 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'
            }),
        }

    def __init__(self, *args, **kwargs):
        # Extract employee_instance if passed to initialize the form
        employee_instance = kwargs.pop('employee_instance', None)
        # Extract initial_on_site_date
        initial_on_site_date = kwargs.pop('initial_on_site_date', None)

        super().__init__(*args, **kwargs)

        if employee_instance:
            self.initial['employee'] = employee_instance.pk
            # Optionally pre-fill other fields based on existing attendance
            # If an existing OnSiteAttendance object is passed as 'instance',
            # ModelForm will handle pre-filling. We just need to handle the checkbox.
            if self.instance.pk: # If this form is bound to an existing attendance record
                self.initial['is_selected'] = True

        if initial_on_site_date:
            self.initial['on_site_date'] = initial_on_site_date

        # Adjust field labels
        self.fields['shift_type'].label = "Shift"
        self.fields['status'].label = "Status"
        self.fields['on_site_details'].label = "If On Site"
        self.fields['from_time'].label = "From Time"

    def clean(self):
        cleaned_data = super().clean()
        is_selected = cleaned_data.get('is_selected')
        on_site_details = cleaned_data.get('on_site_details')

        # Mimic RequiredFieldValidator for txtOnsite if ck is checked
        if is_selected and not on_site_details:
            self.add_error('on_site_details', "This field is required if selected.")
        return cleaned_data

# Create a formset factory for bulk attendance processing
OnSiteAttendanceFormSet = modelformset_factory(
    OnSiteAttendance,
    form=OnSiteAttendanceForm,
    extra=0, # No extra blank forms by default
    can_delete=False, # Not supporting delete per row in this context
    fields=[
        'shift_type', 'status', 'on_site_details', 'from_time'
    ], # These are the fields the formset will directly manage based on existing instances
    # We will manually add 'is_selected', 'employee', 'on_site_date' as extra fields in the form
)
```

#### 4.3 Views (`attendance/views.py`)

The views will be structured to support the main page, the HTMX-loaded table, and the HTMX-driven save operation.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming authentication for Session/CompId
from django.db.models import Prefetch

from .models import BusinessGroup, Employee, OnSiteAttendance
from .forms import AttendanceSearchForm, OnSiteAttendanceForm, OnSiteAttendanceFormSet

class OnSiteAttendanceListView(LoginRequiredMixin, TemplateView):
    """
    Main view for the On-Site Attendance page.
    Displays the search form and acts as a container for the HTMX-loaded table.
    """
    template_name = 'attendance/onsiteattendance_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form with today's date and a default business group if available
        # The master page Label3.Text updates with textChequeDate.Text, so we pass current date
        context['search_form'] = AttendanceSearchForm(initial={
            'on_site_date': timezone.now().date(),
            # 'business_group': BusinessGroup.objects.first() # Or a default value if needed
        })
        # Pass the initial date to the template for display
        context['initial_date'] = timezone.now().date()
        return context

class OnSiteAttendanceTablePartialView(LoginRequiredMixin, View):
    """
    HTMX-driven view to load the attendance table based on search criteria.
    Mimics the FillGrid logic from ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        search_form = AttendanceSearchForm(request.GET)
        employees = Employee.objects.none()
        attendance_formset = None
        on_site_date = None

        if search_form.is_valid():
            on_site_date = search_form.cleaned_data['on_site_date']
            business_group = search_form.cleaned_data['business_group']

            # Simulate GetBGEmp stored procedure:
            # Fetch employees belonging to the selected business group,
            # and prefetch their attendance records for the selected date.
            employees = Employee.objects.filter(
                bg_group=business_group
            ).prefetch_related(
                Prefetch(
                    'on_site_attendances',
                    queryset=OnSiteAttendance.objects.filter(on_site_date=on_site_date),
                    to_attr='current_attendance'
                )
            ).order_by('emp_name') # Order as needed

            # Prepare initial data for the formset based on employees and existing attendance
            initial_form_data = []
            for emp in employees:
                # Get the existing attendance object for this employee and date, if any
                attendance_obj = emp.current_attendance[0] if emp.current_attendance else None
                
                # If an attendance object exists, we'll bind the formset to it directly
                # Otherwise, prepare initial data for a new form for this employee
                if not attendance_obj:
                    initial_form_data.append({
                        'employee': emp.pk,
                        'on_site_date': on_site_date,
                        # Default values for new entry:
                        'shift_type': 0, # Day
                        'status': 0,     # Present
                        'is_selected': False, # Not selected by default for new entries
                    })
            
            # Create the formset. For existing records, pass queryset and instance=attendance_obj
            # For new records, pass initial data. This requires careful handling with formset_factory.
            # Using modelformset_factory: it's designed to manage a queryset of model instances.
            # We need to construct a queryset of existing attendance records or an empty queryset.
            # Then, for employees without existing records, we add "extra" forms.
            # A more robust approach might be to use a custom formset, or manage initial data carefully.

            # Simplified approach for formset:
            # 1. Get existing attendance records for the selected date and group's employees
            existing_attendance_qs = OnSiteAttendance.objects.filter(
                on_site_date=on_site_date,
                employee__in=employees # Filter by employees already fetched
            )

            # 2. Create the formset
            attendance_formset = OnSiteAttendanceFormSet(
                queryset=existing_attendance_qs,
                initial=[
                    # Add initial data for employees WITHOUT existing attendance
                    {'employee': emp.pk, 'on_site_date': on_site_date, 'shift_type': 0, 'status': 0}
                    for emp in employees if not OnSiteAttendance.objects.filter(on_site_date=on_site_date, employee=emp).exists()
                ]
            )

            # For each form in the formset, ensure it gets the correct employee instance and initial date
            # This is crucial because modelformset_factory doesn't automatically pass these to individual forms
            for i, form in enumerate(attendance_formset):
                # Try to get the employee associated with this form instance
                # If it's an existing instance (pk exists), the employee is already set.
                # If it's a new form (no pk), get employee from initial data
                emp_instance = None
                if form.instance.pk:
                    emp_instance = form.instance.employee
                elif form.initial.get('employee'):
                    emp_instance = Employee.objects.get(pk=form.initial['employee'])
                
                if emp_instance:
                    form.fields['employee'].initial = emp_instance.pk
                    form.fields['on_site_date'].initial = on_site_date
                    form.emp_name = emp_instance.emp_name # Attach for display in template
                    form.emp_bg = emp_instance.bg_group.symbol # Attach for display
                    form.emp_hours = emp_instance.hours # Attach for display

                    # Set initial 'is_selected' based on existing data
                    if form.instance.pk:
                        form.initial['is_selected'] = True
                else:
                    form.emp_name = "N/A" # Fallback
                    form.emp_bg = "N/A"
                    form.emp_hours = "N/A"
                    
        else:
            # Handle invalid search form (e.g., display errors or return empty table)
            messages.error(request, "Invalid search criteria provided.")

        context = {
            'attendance_formset': attendance_formset,
            'employees': employees, # This is now actually used in the template rendering logic
            'on_site_date': on_site_date, # Pass the date for the save action
            'search_form': search_form, # Pass for re-rendering if needed
        }
        return render(request, 'attendance/_onsiteattendance_table.html', context)

class OnSiteAttendanceSaveView(LoginRequiredMixin, View):
    """
    HTMX-driven view to handle the submission and saving of attendance records.
    Mimics the btnAdd_Click logic.
    """
    def post(self, request, *args, **kwargs):
        # We need to reconstruct the formset with data from POST
        # And ensure the queryset is correct for existing instances
        on_site_date_str = request.POST.get('on_site_date')
        on_site_date = None
        if on_site_date_str:
            try:
                on_site_date = timezone.datetime.strptime(on_site_date_str, '%Y-%m-%d').date()
            except ValueError:
                messages.error(request, "Invalid date format received.")
                return HttpResponse(status=400)

        # Get the IDs of employees that were in the original grid
        # This is a bit tricky with HTMX if not sent explicitly.
        # For simplicity, let's assume we can re-query based on selected BG if available.
        # Or even better, pass hidden fields for employee IDs in the formset.
        # For now, let's just get all existing attendance records for the date
        # and then also include employees from the search form if available.
        
        # A robust solution requires passing the list of `employee_ids` and `on_site_date`
        # as hidden fields within the POST request for the formset, to accurately
        # reconstruct the queryset for the modelformset_factory.
        
        # For this example, let's assume we re-query for all employees matching the
        # submitted attendance items' associated business groups to build the queryset correctly.
        
        # Simplified: Assume the submitted forms are for employees that were initially displayed.
        # The formset will handle which instances to update/create.
        
        # Re-fetch the queryset for the formset from the database
        # based on the IDs of submitted forms, and the on_site_date
        
        # Collect submitted employee IDs to filter the queryset
        submitted_employee_ids = []
        for key, value in request.POST.items():
            if key.endswith('-employee'):
                submitted_employee_ids.append(value)
        
        # Get existing attendance records for these employees and date
        existing_attendance_for_formset = OnSiteAttendance.objects.filter(
            on_site_date=on_site_date,
            employee__user_id__in=submitted_employee_ids
        )

        attendance_formset = OnSiteAttendanceFormSet(
            request.POST, 
            queryset=existing_attendance_for_formset
        )

        # Before calling is_valid on the formset, ensure each form gets its `employee` and `on_site_date`
        # This is critical for custom validation and processing.
        for i, form in enumerate(attendance_formset):
            # If form.instance.pk exists, employee and on_site_date are already loaded by modelformset
            # If it's a new form, need to extract from POST data manually
            prefix = attendance_formset.prefix
            employee_pk_key = f"{prefix}-{i}-employee"
            on_site_date_key = f"{prefix}-{i}-on_site_date"
            
            if employee_pk_key in request.POST:
                try:
                    form.cleaned_data['employee'] = Employee.objects.get(pk=request.POST[employee_pk_key])
                except Employee.DoesNotExist:
                    form.add_error('employee', "Invalid employee.")
            if on_site_date_key in request.POST:
                try:
                    form.cleaned_data['on_site_date'] = timezone.datetime.strptime(request.POST[on_site_date_key], '%Y-%m-%d').date()
                except ValueError:
                    form.add_error('on_site_date', "Invalid date format.")
            
            # The 'is_selected' checkbox is also a non-model field, ensure it's picked up
            is_selected_key = f"{prefix}-{i}-is_selected"
            form.cleaned_data['is_selected'] = is_selected_key in request.POST
            
            # Manually trigger full_clean for forms that are not changing but are part of the formset
            # This is complex. The best practice for formsets is to let `is_valid()` handle everything.
            # The custom clean method will run for each form when `is_valid()` is called on the formset.

        if attendance_formset.is_valid():
            # Get company_id and financial_year_id from session or user profile
            # Assuming these are available via request.user or session
            company_id = self.request.session.get('compid', 1) # Example default
            financial_year_id = self.request.session.get('finyear', 2024) # Example default

            # Process valid forms using the model's class method
            records_processed, errors = OnSiteAttendance.process_attendance_records(
                attendance_formset,
                self.request.user,
                company_id,
                financial_year_id
            )

            if not errors:
                messages.success(request, f"Successfully processed {records_processed} attendance records.")
                # Trigger a refresh of the table partial to show updated data
                return HttpResponse(
                    status=204, # No content, HTMX will handle triggering
                    headers={'HX-Trigger': 'refreshOnSiteAttendanceTable'}
                )
            else:
                for error_msg in errors:
                    messages.error(request, error_msg)
                # If there are errors, re-render the table with errors
                # Re-create the formset with the POST data to show errors
                # This requires passing the invalid formset back to the template
                return render(request, 'attendance/_onsiteattendance_table.html', {
                    'attendance_formset': attendance_formset,
                    'employees': Employee.objects.filter(user_id__in=submitted_employee_ids).order_by('emp_name'), # Re-fetch employees
                    'on_site_date': on_site_date,
                    'search_form': AttendanceSearchForm(request.POST) # Re-render search form state
                }, status=400) # Indicate bad request due to validation errors
        else:
            # Formset is not valid, re-render the table with errors
            # It's important to pass the invalid formset back so form.errors are displayed
            messages.error(request, "Please correct the errors in the attendance form.")
            
            # Re-fetch employees to ensure names are displayed correctly
            submitted_employee_ids = []
            for i, form in enumerate(attendance_formset):
                prefix = attendance_formset.prefix
                employee_pk_key = f"{prefix}-{i}-employee"
                if employee_pk_key in request.POST:
                    submitted_employee_ids.append(request.POST[employee_pk_key])
            
            employees_for_display = Employee.objects.filter(user_id__in=submitted_employee_ids).order_by('emp_name')
            
            # Ensure each form has its employee and date context for rendering
            for i, form in enumerate(attendance_formset):
                emp_instance = None
                if form.instance.pk:
                    emp_instance = form.instance.employee
                elif form.initial.get('employee'):
                    emp_instance = Employee.objects.get(pk=form.initial['employee'])
                elif f"{attendance_formset.prefix}-{i}-employee" in request.POST:
                    try:
                        emp_instance = Employee.objects.get(pk=request.POST[f"{attendance_formset.prefix}-{i}-employee"])
                    except Employee.DoesNotExist:
                        pass # Handle gracefully
                
                if emp_instance:
                    form.emp_name = emp_instance.emp_name
                    form.emp_bg = emp_instance.bg_group.symbol
                    form.emp_hours = emp_instance.hours

            return render(request, 'attendance/_onsiteattendance_table.html', {
                'attendance_formset': attendance_formset,
                'employees': employees_for_display,
                'on_site_date': on_site_date,
                'search_form': AttendanceSearchForm(request.POST) # Pass the invalid search form state
            }, status=400)

```

#### 4.4 Templates (`attendance/templates/attendance/`)

We'll create a main list template and a partial for the table, to be loaded via HTMX.

**`onsiteattendance_list.html`**

```html
{% extends 'core/base.html' %}
{% load tailwind_filters %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4">Attendance Onsite Details for Date: <span id="current_date_display">{{ initial_date|date:"d-m-Y" }}</span></h2>
        <form id="search_form" hx-get="{% url 'attendance:onsiteattendance_table' %}" hx-target="#attendance_table_container" hx-swap="innerHTML" hx-trigger="submit">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div class="md:col-span-1">
                    <label for="{{ search_form.on_site_date.id_for_label }}" class="block text-sm font-medium text-gray-700">Select Date:</label>
                    {{ search_form.on_site_date }}
                    {% if search_form.on_site_date.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.on_site_date.errors }}</p>
                    {% endif %}
                </div>
                <div class="md:col-span-1">
                    <label for="{{ search_form.business_group.id_for_label }}" class="block text-sm font-medium text-gray-700">Select BG Group:</label>
                    {{ search_form.business_group }}
                    {% if search_form.business_group.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ search_form.business_group.errors }}</p>
                    {% endif %}
                </div>
                <div class="md:col-span-1">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded w-full">Search</button>
                </div>
                <div class="md:col-span-1"></div> {# Spacer #}
            </div>
        </form>
    </div>

    <div id="attendance_table_container"
         hx-trigger="load, refreshOnSiteAttendanceTable from:body"
         hx-get="{% url 'attendance:onsiteattendance_table' %}?{{ search_form.on_site_date.html_name }}={{ search_form.on_site_date.value|date:"Y-m-d" }}&{{ search_form.business_group.html_name }}={{ search_form.business_group.value }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading attendance data...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for custom date picker or other UI states
        // Example for showing/hiding required field based on checkbox, if not handled by Django form validation directly
        // This can be applied directly within the _onsiteattendance_table.html partial for specific rows.
    });

    // Update the date display label when the date input changes
    document.addEventListener('DOMContentLoaded', function() {
        const dateInput = document.getElementById('{{ search_form.on_site_date.id_for_label }}');
        const dateDisplayLabel = document.getElementById('current_date_display');
        
        if (dateInput && dateDisplayLabel) {
            dateInput.addEventListener('change', function() {
                const selectedDate = new Date(this.value);
                if (!isNaN(selectedDate.getTime())) {
                    const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
                    dateDisplayLabel.textContent = selectedDate.toLocaleDateString('en-GB', options).replace(/\//g, '-');
                }
            });
        }
    });

    // Re-initialize DataTables when HTMX swaps content
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'attendance_table_container') {
            const table = document.getElementById('onsiteAttendanceTable');
            if (table && !$.fn.DataTable.isDataTable('#onsiteAttendanceTable')) {
                $(table).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
                });
            }
        }
    });
</script>
{% endblock %}
```

**`_onsiteattendance_table.html`** (Partial template for the DataTables table)

```html
{% load tailwind_filters %}

<form id="attendance_formset_form" hx-post="{% url 'attendance:onsiteattendance_save' %}" hx-swap="none" hx-trigger="submit">
    {% csrf_token %}
    {{ attendance_formset.management_form }}
    <input type="hidden" name="on_site_date" value="{{ on_site_date|date:"Y-m-d" }}"> {# Pass the date for saving #}

    <div class="overflow-x-auto bg-white rounded-lg shadow-md">
        {% if attendance_formset.forms %}
        <table id="onsiteAttendanceTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"></th> {# Checkbox header #}
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name Of Employee</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">If On Site</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift Hrs</th>
                    <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">From Time</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for form in attendance_formset %}
                <tr x-data="{ isSelected: {% if form.initial.is_selected %}true{% else %}false{% endif %} }" :class="{ 'bg-blue-50': isSelected }">
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ form.id }} {# Hidden ID field for existing instances #}
                        {{ form.employee }} {# Hidden employee ID #}
                        {{ form.on_site_date }} {# Hidden on_site_date #}
                        <input type="checkbox" name="{{ form.prefix }}-is_selected" id="{{ form.fields.is_selected.id_for_label }}" 
                                class="form-checkbox h-4 w-4 text-blue-600" 
                                {% if form.initial.is_selected %}checked{% endif %}
                                x-model="isSelected"
                                @change="if (!isSelected) { $nextTick(() => { document.getElementById('{{ form.on_site_details.id_for_label }}').value = ''; }); }"
                                >
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200">{{ form.emp_name }}</td> {# Employee Name from context #}
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ form.emp_bg }}</td> {# BG Group from context #}
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ form.shift_type }}
                        {% if form.shift_type.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.shift_type.errors }}</p>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">
                        {{ form.status }}
                        {% if form.status.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.status.errors }}</p>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">
                        {{ form.on_site_details }}
                        {% if form.on_site_details.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.on_site_details.errors }}</p>
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ form.emp_hours }}</td> {# Shift Hours from context #}
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        {{ form.from_time }}
                        {% if form.from_time.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ form.from_time.errors }}</p>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
        <div class="mt-6 flex justify-center pb-4">
            <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-lg">Proceed</button>
        </div>
        {% else %}
        <div class="fontcss text-center p-8">
            <p class="text-xl font-bold text-maroon">No data to display!</p>
        </div>
        {% endif %}
    </div>
</form>

<script>
    // DataTables initialization (will be re-run by htmx:afterSwap in base template)
    // This part is mainly for the initial load if HTMX doesn't catch it right away,
    // but the main re-init logic is in onsiteattendance_list.html's extra_js.
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#onsiteAttendanceTable')) {
            $('#onsiteAttendanceTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
        }
    });

    // Alpine.js for conditional validation / field clearing
    document.addEventListener('alpine:init', () => {
        // Alpine.js components are now defined inline using x-data, so no global setup needed here.
        // The logic for clearing 'If On Site' field if checkbox is unchecked is handled within the row's x-data.
    });
</script>
```

#### 4.5 URLs (`attendance/urls.py`)

```python
from django.urls import path
from .views import OnSiteAttendanceListView, OnSiteAttendanceTablePartialView, OnSiteAttendanceSaveView

app_name = 'attendance' # Namespace for URLs

urlpatterns = [
    path('onsiteattendance/', OnSiteAttendanceListView.as_view(), name='onsiteattendance_list'),
    path('onsiteattendance/table/', OnSiteAttendanceTablePartialView.as_view(), name='onsiteattendance_table'),
    path('onsiteattendance/save/', OnSiteAttendanceSaveView.as_view(), name='onsiteattendance_save'),
]
```

#### 4.6 Tests (`attendance/tests.py`)

Comprehensive tests for models and views.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import date, time, timedelta

from .models import BusinessGroup, Employee, OnSiteAttendance, SHIFT_CHOICES, STATUS_CHOICES
from .forms import AttendanceSearchForm, OnSiteAttendanceForm, OnSiteAttendanceFormSet

class AttendanceModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='BG1')
        cls.employee1 = Employee.objects.create(user_id='EMP001', emp_name='John Doe', bg_group=cls.business_group, hours=8)
        cls.employee2 = Employee.objects.create(user_id='EMP002', emp_name='Jane Smith', bg_group=cls.business_group, hours=9)
        cls.user = User.objects.create_user(username='testuser', password='password')

        # Example existing attendance
        cls.today = timezone.now().date()
        cls.existing_attendance = OnSiteAttendance.objects.create(
            company_id=1,
            session_id=cls.user.username,
            sys_date=cls.today,
            sys_time=timezone.now().time(),
            on_site_date=cls.today,
            employee=cls.employee1,
            shift_type=0, # Day
            status=0,     # Present
            on_site_details='Working from client site',
            from_time=time(9, 0),
            financial_year_id=2024
        )
  
    def test_business_group_creation(self):
        bg = BusinessGroup.objects.get(id=1)
        self.assertEqual(bg.symbol, 'BG1')
        self.assertEqual(str(bg), 'BG1')

    def test_employee_creation(self):
        emp = Employee.objects.get(user_id='EMP001')
        self.assertEqual(emp.emp_name, 'John Doe')
        self.assertEqual(emp.bg_group, self.business_group)
        self.assertEqual(str(emp), 'John Doe')

    def test_onsite_attendance_creation(self):
        att = OnSiteAttendance.objects.get(id=self.existing_attendance.id)
        self.assertEqual(att.employee, self.employee1)
        self.assertEqual(att.on_site_date, self.today)
        self.assertEqual(att.on_site_details, 'Working from client site')
        self.assertEqual(str(att), f"{self.employee1.emp_name} - {self.today}")

    def test_onsite_attendance_unique_together(self):
        # Attempt to create a duplicate entry
        with self.assertRaises(Exception): # Will raise IntegrityError or similar depending on DB
            OnSiteAttendance.objects.create(
                company_id=1,
                session_id=self.user.username,
                sys_date=self.today,
                sys_time=timezone.now().time(),
                on_site_date=self.today,
                employee=self.employee1,
                shift_type=1,
                status=1,
                on_site_details='Attempted duplicate',
                from_time=time(10, 0),
                financial_year_id=2024
            )

    def test_process_attendance_records_create_new(self):
        future_date = self.today + timedelta(days=7)
        # Create a formset for employee2 for a future date
        form_data = {
            'form-0-employee': self.employee2.pk,
            'form-0-on_site_date': future_date.strftime('%Y-%m-%d'),
            'form-0-shift_type': '0',
            'form-0-status': '0',
            'form-0-on_site_details': 'New entry for future date',
            'form-0-from_time': '09:00',
            'form-0-is_selected': 'on', # Mark as selected
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '0',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
        }
        formset = OnSiteAttendanceFormSet(form_data, queryset=OnSiteAttendance.objects.none()) # Empty queryset as new
        
        # Manually ensure cleaned_data gets populated for non-model fields
        for i, form in enumerate(formset):
            form.cleaned_data = form.data # In a real POST request, is_valid populates this
            form.cleaned_data['employee'] = Employee.objects.get(pk=form.data[f'{form.prefix}-{i}-employee'])
            form.cleaned_data['on_site_date'] = future_date
            form.cleaned_data['is_selected'] = f'{form.prefix}-{i}-is_selected' in form.data

        self.assertTrue(formset.is_valid())
        
        records_processed, errors = OnSiteAttendance.process_attendance_records(
            formset, self.user, 1, 2024
        )
        self.assertEqual(records_processed, 1)
        self.assertEqual(len(errors), 0)
        self.assertTrue(OnSiteAttendance.objects.filter(employee=self.employee2, on_site_date=future_date).exists())

    def test_process_attendance_records_update_existing(self):
        # Create a formset for employee1 (who has an existing record)
        form_data = {
            'form-0-id': self.existing_attendance.id, # Pass existing ID
            'form-0-employee': self.employee1.pk,
            'form-0-on_site_date': self.today.strftime('%Y-%m-%d'),
            'form-0-shift_type': '1', # Change from Day to Night
            'form-0-status': '0',
            'form-0-on_site_details': 'Updated details',
            'form-0-from_time': '18:00',
            'form-0-is_selected': 'on',
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '1',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
        }
        formset = OnSiteAttendanceFormSet(form_data, queryset=OnSiteAttendance.objects.filter(id=self.existing_attendance.id))
        
        for i, form in enumerate(formset):
            form.cleaned_data = form.data
            form.cleaned_data['employee'] = Employee.objects.get(pk=form.data[f'{form.prefix}-{i}-employee'])
            form.cleaned_data['on_site_date'] = self.today
            form.cleaned_data['is_selected'] = f'{form.prefix}-{i}-is_selected' in form.data

        self.assertTrue(formset.is_valid())
        
        records_processed, errors = OnSiteAttendance.process_attendance_records(
            formset, self.user, 1, 2024
        )
        self.assertEqual(records_processed, 1)
        self.assertEqual(len(errors), 0)
        updated_att = OnSiteAttendance.objects.get(id=self.existing_attendance.id)
        self.assertEqual(updated_att.shift_type, 1) # Verify update
        self.assertEqual(updated_att.on_site_details, 'Updated details')

    def test_process_attendance_records_past_date_validation(self):
        past_date = self.today - timedelta(days=1)
        form_data = {
            'form-0-employee': self.employee2.pk,
            'form-0-on_site_date': past_date.strftime('%Y-%m-%d'), # Past date
            'form-0-shift_type': '0',
            'form-0-status': '0',
            'form-0-on_site_details': 'Past date entry',
            'form-0-from_time': '09:00',
            'form-0-is_selected': 'on',
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '0',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
        }
        formset = OnSiteAttendanceFormSet(form_data, queryset=OnSiteAttendance.objects.none())

        for i, form in enumerate(formset):
            form.cleaned_data = form.data
            form.cleaned_data['employee'] = Employee.objects.get(pk=form.data[f'{form.prefix}-{i}-employee'])
            form.cleaned_data['on_site_date'] = past_date
            form.cleaned_data['is_selected'] = f'{form.prefix}-{i}-is_selected' in form.data

        self.assertTrue(formset.is_valid()) # Formset might be valid, but model method will error
        
        records_processed, errors = OnSiteAttendance.process_attendance_records(
            formset, self.user, 1, 2024
        )
        self.assertEqual(records_processed, 0)
        self.assertEqual(len(errors), 1)
        self.assertIn("Attendance date cannot be in the past.", errors[0])

    def test_onsite_details_required_if_selected_validation(self):
        future_date = self.today + timedelta(days=7)
        form_data = {
            'form-0-employee': self.employee2.pk,
            'form-0-on_site_date': future_date.strftime('%Y-%m-%d'),
            'form-0-shift_type': '0',
            'form-0-status': '0',
            'form-0-from_time': '09:00',
            'form-0-is_selected': 'on', # Selected but no on_site_details
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '0',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
        }
        formset = OnSiteAttendanceFormSet(form_data, queryset=OnSiteAttendance.objects.none())
        
        # For ModelForm, is_valid() will run the clean method
        self.assertFalse(formset.is_valid())
        self.assertIn('This field is required if selected.', formset.forms[0].errors['on_site_details'])


class AttendanceViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='testuser', password='password')
        cls.business_group = BusinessGroup.objects.create(id=1, symbol='BG1')
        cls.employee1 = Employee.objects.create(user_id='EMP001', emp_name='John Doe', bg_group=cls.business_group, hours=8)
        cls.employee2 = Employee.objects.create(user_id='EMP002', emp_name='Jane Smith', bg_group=cls.business_group, hours=9)
        cls.today = timezone.now().date()
        cls.tomorrow = cls.today + timedelta(days=1)
        
        # Existing attendance for employee1 for today
        OnSiteAttendance.objects.create(
            company_id=1,
            session_id=cls.user.username,
            sys_date=cls.today,
            sys_time=timezone.now().time(),
            on_site_date=cls.today,
            employee=cls.employee1,
            shift_type=0, status=0,
            on_site_details='Office',
            from_time=time(9,0),
            financial_year_id=2024
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password')
        # Set session data if needed, mimicking ASP.NET
        session = self.client.session
        session['compid'] = 1
        session['finyear'] = 2024
        session.save()

    def test_list_view_get(self):
        response = self.client.get(reverse('attendance:onsiteattendance_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/onsiteattendance_list.html')
        self.assertIn('search_form', response.context)
        self.assertIn('initial_date', response.context)
        self.assertEqual(response.context['initial_date'], self.today)

    def test_table_partial_view_get(self):
        # Test initial load without specific search params (should use today/default BG)
        response = self.client.get(reverse('attendance:onsiteattendance_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/_onsiteattendance_table.html')
        self.assertIn('attendance_formset', response.context)
        self.assertIn('employees', response.context)
        
        # Test with specific search parameters
        response = self.client.get(reverse('attendance:onsiteattendance_table'), {
            'on_site_date': self.today.strftime('%Y-%m-%d'),
            'business_group': self.business_group.id
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'attendance/_onsiteattendance_table.html')
        self.assertIn('attendance_formset', response.context)
        self.assertIn('employees', response.context)
        self.assertEqual(response.context['on_site_date'], self.today)
        
        # Verify employee1 is pre-filled, employee2 is a new empty form
        formset = response.context['attendance_formset']
        self.assertEqual(len(formset), 2) # Should contain forms for both employees
        
        # Employee1's form should be bound to existing attendance
        emp1_form = next((f for f in formset if f.emp_name == 'John Doe'), None)
        self.assertIsNotNone(emp1_form)
        self.assertTrue(emp1_form.instance.pk) # Should be an existing instance
        self.assertTrue(emp1_form.initial['is_selected']) # Should be selected
        
        # Employee2's form should be a new one
        emp2_form = next((f for f in formset if f.emp_name == 'Jane Smith'), None)
        self.assertIsNotNone(emp2_form)
        self.assertFalse(emp2_form.instance.pk) # Should be a new instance
        self.assertFalse(emp2_form.initial['is_selected']) # Should not be selected by default for new

    def test_save_view_post_new_entry(self):
        initial_attendance_count = OnSiteAttendance.objects.count()
        form_data = {
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '0',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
            'on_site_date': self.tomorrow.strftime('%Y-%m-%d'),
            f'form-0-employee': self.employee2.pk,
            f'form-0-on_site_date': self.tomorrow.strftime('%Y-%m-%d'),
            f'form-0-shift_type': '0',
            f'form-0-status': '0',
            f'form-0-on_site_details': 'New on-site details for Jane',
            f'form-0-from_time': '09:00',
            f'form-0-is_selected': 'on', # This indicates it's selected
        }
        
        response = self.client.post(reverse('attendance:onsiteattendance_save'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Success, no content for HTMX trigger
        self.assertEqual(OnSiteAttendance.objects.count(), initial_attendance_count + 1)
        self.assertTrue(OnSiteAttendance.objects.filter(employee=self.employee2, on_site_date=self.tomorrow).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshOnSiteAttendanceTable')

    def test_save_view_post_update_entry(self):
        initial_attendance_count = OnSiteAttendance.objects.count()
        # Get the existing attendance instance
        existing_att = OnSiteAttendance.objects.get(employee=self.employee1, on_site_date=self.today)
        
        form_data = {
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '1', # Indicate one initial form
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
            'on_site_date': self.today.strftime('%Y-%m-%d'),
            f'form-0-id': existing_att.id, # Include ID for update
            f'form-0-employee': self.employee1.pk,
            f'form-0-on_site_date': self.today.strftime('%Y-%m-%d'),
            f'form-0-shift_type': '1', # Change shift to Night
            f'form-0-status': '0',
            f'form-0-on_site_details': 'Updated details for John',
            f'form-0-from_time': '17:00',
            f'form-0-is_selected': 'on',
        }
        
        response = self.client.post(reverse('attendance:onsiteattendance_save'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(OnSiteAttendance.objects.count(), initial_attendance_count) # No new record, just update
        updated_att = OnSiteAttendance.objects.get(id=existing_att.id)
        self.assertEqual(updated_att.shift_type, 1) # Verify update
        self.assertEqual(updated_att.on_site_details, 'Updated details for John')
        self.assertIn('HX-Trigger', response.headers)

    def test_save_view_post_validation_error_past_date(self):
        past_date = self.today - timedelta(days=1)
        form_data = {
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '0',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
            'on_site_date': past_date.strftime('%Y-%m-%d'), # Past date
            f'form-0-employee': self.employee2.pk,
            f'form-0-on_site_date': past_date.strftime('%Y-%m-%d'),
            f'form-0-shift_type': '0',
            f'form-0-status': '0',
            f'form-0-on_site_details': 'Attempting to save past date',
            f'form-0-from_time': '09:00',
            f'form-0-is_selected': 'on',
        }
        
        response = self.client.post(reverse('attendance:onsiteattendance_save'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400) # Bad request due to validation error
        self.assertTemplateUsed(response, 'attendance/_onsiteattendance_table.html')
        self.assertContains(response, "Attendance date cannot be in the past.")

    def test_save_view_post_validation_error_onsite_details_missing(self):
        form_data = {
            'form-TOTAL_FORMS': '1',
            'form-INITIAL_FORMS': '0',
            'form-MIN_NUM_FORMS': '0',
            'form-MAX_NUM_FORMS': '1000',
            'on_site_date': self.tomorrow.strftime('%Y-%m-%d'),
            f'form-0-employee': self.employee2.pk,
            f'form-0-on_site_date': self.tomorrow.strftime('%Y-%m-%d'),
            f'form-0-shift_type': '0',
            f'form-0-status': '0',
            # No on_site_details
            f'form-0-from_time': '09:00',
            f'form-0-is_selected': 'on', # Checkbox is ON
        }
        
        response = self.client.post(reverse('attendance:onsiteattendance_save'), form_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 400)
        self.assertTemplateUsed(response, 'attendance/_onsiteattendance_table.html')
        self.assertContains(response, "This field is required if selected.")

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:** All interactions (searching/filtering, saving attendance) are driven by HTMX.
    *   The `onsiteattendance_list.html` uses `hx-get` on form submission to load `_onsiteattendance_table.html` into a `div`.
    *   The `_onsiteattendance_table.html` uses `hx-post` on its form submission to send data to `onsiteattendance_save`.
    *   Upon successful save, `OnSiteAttendanceSaveView` returns `status=204` with an `HX-Trigger` header (`refreshOnSiteAttendanceTable`) to signal the main `onsiteattendance_list.html` to reload the table, ensuring data consistency.
*   **Alpine.js:** Used for simple client-side UI state management, specifically for the `is_selected` checkbox and conditionally clearing the 'If On Site' text box when the checkbox is unchecked.
    *   `x-data="{ isSelected: ... }"` binds the checkbox state.
    *   `x-model="isSelected"` keeps the UI and Alpine.js state in sync.
    *   `@change="if (!isSelected) { $nextTick(() => { document.getElementById('{{ form.on_site_details.id_for_label }}').value = ''; }); }"` clears the text box when the checkbox is unchecked.
*   **DataTables:** Initialized on the `_onsiteattendance_table.html` table. The `htmx:afterSwap` event listener in `onsiteattendance_list.html` ensures DataTables is re-initialized every time the table content is reloaded by HTMX, maintaining full functionality.

### Final Notes

*   **Authentication:** The views include `LoginRequiredMixin` to ensure only authenticated users can access them, mirroring the typical ASP.NET security context. You would configure Django's authentication system (e.g., `settings.LOGIN_URL`).
*   **Session Data:** Company ID (`CompId`) and Financial Year ID (`FinYearId`) are assumed to be available in the Django session (e.g., set after user login or selection), similar to how ASP.NET retrieves them from `Session["compid"]`.
*   **`fun` Helper Functions:** The logic from `fun.FromDate`, `fun.FromDateMDY`, `fun.DateValidation`, `fun.getCode`, `fun.chkEmpCustSupplierCode` has been replaced with native Python `datetime` operations and Django ORM queries within the models and views, following the 'Fat Model' principle. `fun.getCode` and `fun.chkEmpCustSupplierCode` were inferred to be simple ID/name lookups or existence checks, handled by `pk` or `objects.get()/filter().exists()`.
*   **Error Handling:** Django's `messages` framework is used for user feedback, replacing `ClientScript.RegisterStartupScript(this.GetType(), "myalert", "alert(...)")`.
*   **Tailwind CSS:** All generated HTML includes standard Tailwind CSS classes (e.g., `block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm`). This assumes Tailwind CSS is configured and available in your `base.html`.
*   **Extensibility:** This structure is highly extensible. If new features are required (e.g., individual attendance record deletion, more complex reporting), they can be added as new views, model methods, and HTMX partials.

This comprehensive plan provides a clear, actionable roadmap for automatically converting your ASP.NET On-Site Attendance module to a modern Django application, focusing on efficiency, maintainability, and user experience.