This modernization plan details the conversion of the provided ASP.NET Project Summary Details Bought report to a modern Django-based solution. The focus is on leveraging Django's "Fat Model, Thin View" paradigm, HTMX for dynamic interactions, Alpine.js for frontend state management, and DataTables for efficient data presentation, all delivered in plain English.

---

## ASP.NET to Django Conversion Script: Project Summary Details Bought

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code performs complex joins and nested queries across multiple tables to generate the report. We've identified the following key tables and columns used to construct the report data, though a full ERP system would have many more:

*   **`tblDG_Item_Master`**: `Id`, `ItemCode`, `PartNo`, `StockQty`, `ManfDesc`, `UOMBasic` (FK to `Unit_Master`), `CompId`, `FinYearId`
*   **`tblDG_BOM_Master`**: `ItemId` (FK to `tblDG_Item_Master.Id`), `WONo`, `CId` (parent ID)
*   **`Unit_Master`**: `Id`, `Symbol`
*   **`tblMM_PR_Master`**: `Id`, `PRNo`, `SysDate`, `WONo`
*   **`tblMM_PR_Details`**: `MId` (FK to `tblMM_PR_Master.Id`), `ItemId`, `Qty`
*   **`tblMM_PO_Master`**: `Id`, `PONo`, `SysDate`, `SupplierId`, `Authorize`
*   **`tblMM_PO_Details`**: `MId` (FK to `tblMM_PO_Master.Id`), `PRId` (FK to `tblMM_PR_Details.Id`), `Qty`
*   **`tblMM_Supplier_master`**: `CompId`, `SupplierId`, `SupplierName`
*   **`tblInv_Inward_Master`**: `Id`, `GINNo`, `SysDate`, `PONo`, `CompId`
*   **`tblInv_Inward_Details`**: `GINId` (FK to `tblInv_Inward_Master.Id`), `POId` (FK to `tblMM_PO_Details.Id`), `ReceivedQty`
*   **`tblinv_MaterialReceived_Master`**: `Id`, `GRRNo`, `SysDate`, `CompId`, `GINId` (FK to `tblInv_Inward_Master.Id`)
*   **`tblinv_MaterialReceived_Details`**: `Id`, `MId` (FK to `tblinv_MaterialReceived_Master.Id`), `POId` (FK to `tblInv_Inward_Details.POId`), `ReceivedQty`
*   **`tblQc_MaterialQuality_Master`**: `Id`, `SysDate`, `GQNNo`, `CompId`, `GRRId` (FK to `tblinv_MaterialReceived_Master.Id`)
*   **`tblQc_MaterialQuality_Details`**: `MId` (FK to `tblQc_MaterialQuality_Master.Id`), `GRRId` (FK to `tblinv_MaterialReceived_Details.Id`), `AcceptedQty`

### Step 2: Identify Backend Functionality

**Task:** Determine the operations performed by the ASP.NET code.

**Instructions:**
This ASP.NET page primarily functions as a **Read (Report Generation)** system with an **Export** capability.

*   **Read:** The `FillGrid_Creditors()` method is the core logic. It retrieves a comprehensive report of "bought" items related to a specific Work Order (`WONo`). This involves complex, nested SQL queries to gather item details, BOM quantities, stock levels, and related Purchase Requisition (PR), Purchase Order (PO), Goods Inward Note (GIN), Goods Receipt/Return (GRR), and Goods Quality Note (GQN) information. The data for related documents (PR, PO, GIN, etc.) is concatenated into single strings with `<br>` tags in the original output.
*   **Export:** The `btnExport_Click` event dynamically prunes columns from the generated report data based on user selections in the `CheckBoxList1` and then exports this filtered data as an HTML table wrapped in an Excel file.
*   **UI Control:** The `CheckAll_CheckedChanged` event handles selecting/deselecting all columns for the report display/export.
*   **Navigation:** The `btnCancel_Click` event redirects the user to another page.

There are no direct Create, Update, or Delete operations on the data presented in this report, as it is purely an aggregation view.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **Label (`lblWo`):** Displays the Work Order Number (`WONo`), retrieved from the URL query string.
*   **Checkbox (`CheckAll`):** A "Select All" checkbox to toggle the selection of all report columns.
*   **CheckboxList (`CheckBoxList1`):** A list of checkboxes, each representing a column in the report (e.g., `Item Code`, `Description`, `PR No`, `PO Date`). Users can select which columns they want to see in the report or export.
*   **Button (`btnExport`):** Triggers the export of the report data to an Excel-compatible file.
*   **Button (`btnCancel`):** Redirects the user away from the report page.

The report data itself is not explicitly bound to an `asp:GridView` in the ASPX, but the `FillGrid_Creditors` method populates a `DataTable` which implies a tabular display. The client-side styling (`.container`, `.grdview_headers`) also suggests a scrollable, header-fixed table.

### Step 4: Generate Django Code

We will create a new Django app named `project_reports` to house this functionality.

#### 4.1 Models (`project_reports/models.py`)

**Task:** Create Django models based on the identified database schema and a service class for report generation.

**Instructions:**
We'll define Django models for `tblDG_Item_Master` and `Unit_Master` with `managed = False` to connect to the existing database tables. The complex report aggregation logic will be encapsulated within a `ProjectSummaryReportService` class, living within `models.py` (adhering to the "fat model" principle, where "model" encompasses business logic even if it's not a direct Django ORM model). This service will use raw SQL queries to replicate the original C# data retrieval and aggregation, including the string concatenation for multi-value fields.

```python
# project_reports/models.py
from django.db import models, connections
from django.utils.formats import date_format
import datetime
import logging

logger = logging.getLogger(__name__)

# Define Django models for the existing database tables with managed=False
# This ensures Django can interact with them without trying to create/alter schemas.

class ItemMaster(models.Model):
    # Primary key from the existing table
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, null=True, blank=True)
    part_no = models.CharField(db_column='PartNo', max_length=100, null=True, blank=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, null=True, blank=True)
    stock_qty = models.FloatField(db_column='StockQty', null=True, blank=True)
    # Assuming UOMBasic is a foreign key to UnitMaster.Id
    uom_basic_id = models.IntegerField(db_column='UOMBasic', null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)

    class Meta:
        managed = False  # Important: Django won't manage this table's schema
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_desc}"

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

# This service class encapsulates the complex report generation logic, making it part of the "fat model" concept.
# It directly replicates the nested query logic from the ASP.NET code-behind to ensure data consistency.
class ProjectSummaryReportService:
    """
    Service class to encapsulate the complex business logic for generating the Project Summary Details Bought report.
    This class leverages raw SQL queries to mimic the original ASP.NET data retrieval and aggregation patterns,
    including the concatenation of multiple related records into single string fields.
    """

    @staticmethod
    def _format_date_dmy(date_str):
        """
        Replicates the ASP.NET date formatting logic (to DD-MM-YYYY).
        Handles various input formats to be robust.
        """
        if not date_str:
            return ""
        try:
            # Try parsing as YYYY-MM-DD (common DB format)
            dt_obj = datetime.datetime.strptime(str(date_str).split(' ')[0], '%Y-%m-%d').date()
        except ValueError:
            try:
                # Try parsing as DD-MM-YYYY
                dt_obj = datetime.datetime.strptime(str(date_str).split(' ')[0], '%d-%m-%Y').date()
            except ValueError:
                try:
                    # Try parsing as MM/DD/YYYY (common ASP.NET date input)
                    dt_obj = datetime.datetime.strptime(str(date_str).split(' ')[0], '%m/%d/%Y').date()
                except ValueError:
                    logger.warning(f"Could not parse date string: {date_str}")
                    return str(date_str) # Return original if cannot parse

        return date_format(dt_obj, "d-m-Y") # Django's date_format helper

    @staticmethod
    def _execute_sql_query(sql_query, params=None):
        """Helper to execute raw SQL and return results as list of dictionaries."""
        with connections['default'].cursor() as cursor:
            try:
                cursor.execute(sql_query, params if params is not None else [])
                columns = [col[0] for col in cursor.description]
                return [dict(zip(columns, row)) for row in cursor.fetchall()]
            except Exception as e:
                logger.error(f"SQL execution error for query: {sql_query} with params {params}. Error: {e}")
                return []

    @staticmethod
    def _get_bom_qty(comp_id, won_no, item_id, fin_year_id):
        """Replicates fun.AllComponentBOMQty - sums quantities from BOM."""
        sql = """
            SELECT SUM(T2.Qty) AS TotalQty
            FROM tblDG_BOM_Master AS T1
            INNER JOIN tblDG_BOM_Details AS T2 ON T1.Id = T2.MId
            WHERE T1.WONo = %s AND T2.ItemId = %s AND T1.CompId = %s AND T1.FinYearId <= %s
        """
        result = ProjectSummaryReportService._execute_sql_query(sql, [won_no, item_id, comp_id, fin_year_id])
        return result[0]['TotalQty'] if result and result[0]['TotalQty'] is not None else 0

    @staticmethod
    def _get_wis_qty(comp_id, won_no, item_id):
        """Replicates fun.CalWISQty - sums 'What Is Stock' quantity."""
        sql = """
            SELECT SUM(T2.Qty) AS TotalQty
            FROM tblDG_WIS_Master AS T1
            INNER JOIN tblDG_WIS_Details AS T2 ON T1.Id = T2.MId
            WHERE T1.WONo = %s AND T2.ItemId = %s AND T1.CompId = %s
        """
        result = ProjectSummaryReportService._execute_sql_query(sql, [won_no, item_id, comp_id])
        return result[0]['TotalQty'] if result and result[0]['TotalQty'] is not None else 0

    @staticmethod
    def get_report_data(won_no, comp_id, fin_year_id, switch_to="1"):
        """
        Generates the comprehensive Project Summary Details Bought report data.
        This method mirrors the nested data retrieval and concatenation logic found in the ASP.NET FillGrid_Creditors.
        """
        report_data = []
        sn = 1

        # Initial ItemMaster query (replicates the 'if (SwitchTo == "1")' block)
        item_master_sql = """
            SELECT DISTINCT
                T1.ItemCode,
                T1.PartNo,
                T1.CId,
                T1.Id,
                T1.StockQty,
                T1.ManfDesc,
                T2.Symbol AS UOMBasic
            FROM
                tblDG_Item_Master AS T1
            INNER JOIN
                tblDG_BOM_Master AS T3 ON T1.Id = T3.ItemId
            INNER JOIN
                Unit_Master AS T2 ON T1.UOMBasic = T2.Id
            WHERE
                T1.CId IS NOT NULL
                AND T3.WONo = %s
                AND T1.CompId = %s
                AND T1.FinYearId <= %s
                AND T3.CId NOT IN (SELECT PId FROM tblDG_BOM_Master WHERE WONo = %s AND CompId = %s AND FinYearId <= %s)
            ORDER BY
                T1.Id ASC
        """

        item_master_items = ProjectSummaryReportService._execute_sql_query(
            item_master_sql, [won_no, comp_id, fin_year_id, won_no, comp_id, fin_year_id]
        )

        for item_row in item_master_items:
            item_id = item_row['Id']
            
            # Initialize a dictionary for the current report row
            current_row_data = {
                'sn': sn,
                'item_code': item_row['ItemCode'],
                'description': item_row['ManfDesc'],
                'uom': item_row['UOMBasic'],
                'bom_qty': ProjectSummaryReportService._get_bom_qty(comp_id, won_no, item_id, fin_year_id),
                'wis_qty': ProjectSummaryReportService._get_wis_qty(comp_id, won_no, item_id),
                'stock_qty': item_row['StockQty'],
                'pr_no': [], 'pr_date': [], 'pr_qty': [],
                'po_no': [], 'po_date': [], 'supplier': [], 'authorized': [], 'po_qty': [],
                'gin_no': [], 'gin_date': [], 'gin_qty': [],
                'grr_no': [], 'grr_date': [], 'grr_qty': [],
                'gqn_no': [], 'gqn_date': [], 'gqn_qty': [],
            }

            # Fetch PR details (nested query level 1)
            pr_sql = """
                SELECT T1.Id AS PRId, T1.PRNo, T1.SysDate AS PRDate, T2.Qty AS PRQty
                FROM tblMM_PR_Master AS T1
                INNER JOIN tblMM_PR_Details AS T2 ON T1.Id = T2.MId
                WHERE T1.WONo = %s AND T2.ItemId = %s
            """
            pr_records = ProjectSummaryReportService._execute_sql_query(pr_sql, [won_no, item_id])

            for pr in pr_records:
                current_row_data['pr_no'].append(pr['PRNo'])
                current_row_data['pr_date'].append(ProjectSummaryReportService._format_date_dmy(pr['PRDate']))
                current_row_data['pr_qty'].append(str(pr['PRQty']))

                # Fetch PO details (nested query level 2)
                po_sql = """
                    SELECT T1.Id, T1.PONo, T1.SysDate, T1.SupplierId, T1.Authorize, T2.Qty
                    FROM tblMM_PO_Master AS T1
                    INNER JOIN tblMM_PO_Details AS T2 ON T1.Id = T2.MId
                    WHERE T2.PRId = %s
                """
                po_records = ProjectSummaryReportService._execute_sql_query(po_sql, [pr['PRId']])

                for po in po_records:
                    # Fetch Supplier Name
                    supplier_sql = "SELECT SupplierName+'['+SupplierId+']' AS SupplierName FROM tblMM_Supplier_master WHERE CompId=%s AND SupplierId=%s"
                    supplier_result = ProjectSummaryReportService._execute_sql_query(supplier_sql, [comp_id, po['SupplierId']])
                    supplier_name = supplier_result[0]['SupplierName'] if supplier_result else ""

                    current_row_data['authorized'].append("Yes" if po['Authorize'] == 1 else "No")
                    current_row_data['po_no'].append(po['PONo'])
                    current_row_data['po_date'].append(ProjectSummaryReportService._format_date_dmy(po['SysDate']))
                    current_row_data['supplier'].append(supplier_name)
                    current_row_data['po_qty'].append(str(po['Qty']))

                    # Fetch GIN details (nested query level 3)
                    gin_sql = """
                        SELECT T2.ReceivedQty AS GINQty, T1.GINNo, T1.SysDate AS GINDate, T1.Id, T2.POId
                        FROM tblInv_Inward_Master AS T1
                        INNER JOIN tblInv_Inward_Details AS T2 ON T1.Id = T2.GINId
                        WHERE T2.POId = %s AND T1.PONo = %s AND T1.CompId = %s
                    """
                    gin_records = ProjectSummaryReportService._execute_sql_query(gin_sql, [po['Id'], po['PONo'], comp_id])

                    for gin in gin_records:
                        current_row_data['gin_no'].append(gin['GINNo'])
                        current_row_data['gin_date'].append(ProjectSummaryReportService._format_date_dmy(gin['GINDate']))
                        current_row_data['gin_qty'].append(str(gin['GINQty']))

                        # Fetch GRR details (nested query level 4)
                        grr_sql = """
                            SELECT T2.ReceivedQty AS GRRQty, T1.GRRNo, T1.SysDate AS GRRDate, T1.Id, T2.Id AS DId
                            FROM tblinv_MaterialReceived_Master AS T1
                            INNER JOIN tblinv_MaterialReceived_Details AS T2 ON T1.Id = T2.MId
                            WHERE T1.CompId = %s AND T1.GINId = %s AND T2.POId = %s
                        """
                        grr_records = ProjectSummaryReportService._execute_sql_query(grr_sql, [comp_id, gin['Id'], gin['POId']])

                        for grr in grr_records:
                            current_row_data['grr_no'].append(grr['GRRNo'])
                            current_row_data['grr_date'].append(ProjectSummaryReportService._format_date_dmy(grr['GRRDate']))
                            current_row_data['grr_qty'].append(str(grr['GRRQty']))

                            # Fetch GQN details (nested query level 5)
                            gqn_sql = """
                                SELECT T1.Id, T1.SysDate AS GQNDate, T1.GQNNo, T2.AcceptedQty AS GQNQty
                                FROM tblQc_MaterialQuality_Master AS T1
                                INNER JOIN tblQc_MaterialQuality_Details AS T2 ON T1.Id = T2.MId
                                WHERE T1.CompId = %s AND T1.GRRId = %s AND T2.GRRId = %s
                            """
                            gqn_records = ProjectSummaryReportService._execute_sql_query(gqn_sql, [comp_id, grr['Id'], grr['DId']])

                            for gqn in gqn_records:
                                current_row_data['gqn_no'].append(gqn['GQNNo'])
                                current_row_data['gqn_date'].append(ProjectSummaryReportService._format_date_dmy(gqn['GQNDate']))
                                current_row_data['gqn_qty'].append(str(gqn['GQNQty']))
            
            # After all nested loops, convert lists of related data to newline-separated strings
            for key in current_row_data:
                if isinstance(current_row_data[key], list):
                    current_row_data[key] = "<br>".join(filter(None, current_row_data[key]))

            report_data.append(current_row_data)
            sn += 1
        
        return report_data

```

#### 4.2 Forms (`project_reports/forms.py`)

**Task:** Define a Django form for selecting report columns.

**Instructions:**
Instead of a `ModelForm`, we'll create a simple `Form` class to represent the `CheckBoxList1` functionality. This form will allow users to select which report columns they want to display or export.

```python
# project_reports/forms.py
from django import forms

class ReportColumnSelectionForm(forms.Form):
    """
    Form to allow users to select which columns to display or export in the report.
    This replicates the functionality of the ASP.NET CheckBoxList.
    """
    # Define all possible columns as choices, mirroring the ASP.NET CheckBoxList
    COLUMN_CHOICES = [
        ('sn', 'Sr.No'),
        ('item_code', 'Item Code'),
        ('description', 'Description'),
        ('uom', 'UOM'),
        ('bom_qty', 'BOM Qty'),
        ('wis_qty', 'WIS Qty'),
        ('stock_qty', 'Stock Qty'),
        ('pr_no', 'PR No'),
        ('pr_date', 'PR Date'),
        ('pr_qty', 'PR Qty'),
        ('po_no', 'PO No'),
        ('po_date', 'PO Date'),
        ('supplier', 'Supplier Name'),
        ('authorized', 'Authorized'),
        ('po_qty', 'PO Qty'),
        ('gin_no', 'GIN No'),
        ('gin_date', 'GIN Date'),
        ('gin_qty', 'GIN Qty'),
        ('grr_no', 'GRR No'),
        ('grr_date', 'GRR Date'),
        ('grr_qty', 'GRR Qty'),
        ('gqn_no', 'GQN No'),
        ('gqn_date', 'GQN Date'),
        ('gqn_qty', 'GQN Qty'),
    ]

    selected_columns = forms.MultipleChoiceField(
        choices=COLUMN_CHOICES,
        widget=forms.CheckboxSelectMultiple(attrs={
            'class': 'form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500',
            'x-model': 'selectedColumns' # Alpine.js binding for check/uncheck all
        }),
        required=False,
        initial=[choice[0] for choice in COLUMN_CHOICES if choice[0] != 'sr_no_placeholder'] # All selected by default, similar to ASP.NET initial state
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Ensure 'Sr.No' is always effectively selected for display but not for removal from CheckBoxList
        # The 'Sr.No' (sn) column is always present in the report data,
        # but the original ASP.NET allowed unselecting other columns.
        # Here we'll ensure it's always included in the UI if selected.
        pass

```

#### 4.3 Views (`project_reports/views.py`)

**Task:** Implement report display and export operations using Django CBVs.

**Instructions:**
We'll use `TemplateView` for the main report page and a separate `View` for the export functionality. The data table content will be loaded via HTMX into a partial template using another `TemplateView`. All complex data retrieval will be delegated to the `ProjectSummaryReportService`.

```python
# project_reports/views.py
import csv
from io import StringIO
from django.views.generic import TemplateView, View
from django.http import HttpResponse, HttpResponseRedirect
from django.urls import reverse
from django.contrib import messages
from .models import ProjectSummaryReportService
from .forms import ReportColumnSelectionForm
from django.conf import settings

class ProjectSummaryReportView(TemplateView):
    """
    Main view for displaying the Project Summary Details Bought report.
    This view renders the page with column selection options and a placeholder for the DataTable.
    """
    template_name = 'project_reports/projectsummary/report_list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Fetch WONo and SwitchTo from query parameters, similar to ASP.NET Request.QueryString
        context['won_no'] = self.request.GET.get('WONo', 'N/A')
        context['switch_to'] = self.request.GET.get('SwitchTo', '1') # Default to "1" as per ASP.NET code

        # Initialize the form for column selection.
        # If there's a POST request for export, the form will be bound.
        # Otherwise, for the initial load, it will be unbound with default selections.
        context['form'] = ReportColumnSelectionForm(self.request.GET or None)

        # For demonstration, retrieve CompId and FinYearId from settings or a dummy session
        # In a real app, these would come from user session/profile.
        context['comp_id'] = getattr(settings, 'DEFAULT_COMP_ID', 1)
        context['fin_year_id'] = getattr(settings, 'DEFAULT_FIN_YEAR_ID', 1)

        # Pre-select all columns for initial display if not explicitly requested otherwise
        if not self.request.GET.get('selected_columns'):
            context['selected_columns_initial'] = [col[0] for col in ReportColumnSelectionForm.COLUMN_CHOICES]
        else:
            context['selected_columns_initial'] = self.request.GET.getlist('selected_columns')

        return context

class ProjectSummaryTablePartialView(TemplateView):
    """
    HTMX-driven partial view to render the DataTables content of the report.
    This view is called dynamically to load or refresh the table.
    """
    template_name = 'project_reports/projectsummary/_report_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        won_no = self.request.GET.get('WONo', '')
        switch_to = self.request.GET.get('SwitchTo', '1')
        
        # In a real application, CompId and FinYearId would be dynamic (e.g., from user session)
        comp_id = getattr(settings, 'DEFAULT_COMP_ID', 1)
        fin_year_id = getattr(settings, 'DEFAULT_FIN_YEAR_ID', 1)

        report_data = []
        if won_no:
            try:
                report_data = ProjectSummaryReportService.get_report_data(won_no, comp_id, fin_year_id, switch_to)
            except Exception as e:
                messages.error(self.request, f"Error generating report: {e}")
                report_data = [] # Ensure report_data is empty on error
                
        context['report_items'] = report_data

        # Determine which columns to display based on form selection
        form = ReportColumnSelectionForm(self.request.GET or None)
        if form.is_valid():
            selected_columns = form.cleaned_data.get('selected_columns', [])
        else:
            selected_columns = [col[0] for col in ReportColumnSelectionForm.COLUMN_CHOICES] # Default to all

        # Ensure 'sn' (Sr.No) is always included for display if not explicitly in initial selection
        if 'sn' not in selected_columns and 'sn' in [col[0] for col in ReportColumnSelectionForm.COLUMN_CHOICES]:
            selected_columns.insert(0, 'sn') # Add 'sn' at the beginning if not present
        
        context['selected_columns'] = selected_columns
        context['column_headers'] = {choice[0]: choice[1] for choice in ReportColumnSelectionForm.COLUMN_CHOICES}
        
        return context

class ProjectSummaryExportView(View):
    """
    View to handle the export of report data to CSV/Excel.
    """
    def post(self, request, *args, **kwargs):
        form = ReportColumnSelectionForm(request.POST)
        if not form.is_valid():
            messages.error(request, "Invalid column selection for export.")
            return HttpResponseRedirect(reverse('project_summary_report') + f"?WONo={request.GET.get('WONo', '')}&SwitchTo={request.GET.get('SwitchTo', '1')}")
        
        selected_columns = form.cleaned_data.get('selected_columns', [])
        won_no = request.GET.get('WONo', '')
        switch_to = request.GET.get('SwitchTo', '1')
        
        # In a real application, CompId and FinYearId would be dynamic
        comp_id = getattr(settings, 'DEFAULT_COMP_ID', 1)
        fin_year_id = getattr(settings, 'DEFAULT_FIN_YEAR_ID', 1)

        try:
            report_data = ProjectSummaryReportService.get_report_data(won_no, comp_id, fin_year_id, switch_to)
            if not report_data:
                messages.warning(request, "No records to export.")
                # This response will trigger an alert via HTMX due to hx-target="body" hx-swap="none"
                # And then the HX-Trigger will refresh the list.
                # A 204 status with HX-Trigger is better for HTMX.
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': 'showMessageAndRefreshList:No records to export.',
                        'HX-Redirect': reverse('project_summary_report') + f"?WONo={won_no}&SwitchTo={switch_to}"
                    }
                )

            # Ensure 'sn' (Sr.No) is always the first column in the export
            if 'sn' not in selected_columns and 'sn' in [col[0] for col in ReportColumnSelectionForm.COLUMN_CHOICES]:
                selected_columns.insert(0, 'sn')

            output = StringIO()
            writer = csv.writer(output)

            # Write headers based on selected columns
            headers = [ReportColumnSelectionForm.COLUMN_CHOICES_DICT.get(col, col.replace('_', ' ').title()) for col in selected_columns]
            writer.writerow(headers)

            # Write data rows
            for item in report_data:
                row = []
                for col_key in selected_columns:
                    # Remove <br> tags and replace with newline for CSV if necessary, or just keep content.
                    # For CSV, <br> is typically not desired, so we'll remove it.
                    cell_content = str(item.get(col_key, '')).replace('<br>', '\n').strip()
                    row.append(cell_content)
                writer.writerow(row)

            file_name = f"ProjectSummary_Bought_WONo_{won_no}_{datetime.date.today().strftime('%Y_%m_%d')}.csv"
            response = HttpResponse(output.getvalue(), content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="{file_name}"'
            messages.success(request, "Report exported successfully.")
            return response

        except Exception as e:
            messages.error(request, f"An error occurred during export: {e}")
            # Redirect back to the report page on error
            return HttpResponseRedirect(reverse('project_summary_report') + f"?WONo={won_no}&SwitchTo={switch_to}")

    # Add COLUMN_CHOICES_DICT to the class for easy lookup
    COLUMN_CHOICES_DICT = {choice[0]: choice[1] for choice in ReportColumnSelectionForm.COLUMN_CHOICES}

```

#### 4.4 Templates (`project_reports/templates/project_reports/projectsummary/`)

**Task:** Create templates for the report display.

**Instructions:**
The main template `report_list.html` will extend `core/base.html` and define the structure, including the column selection checkboxes, `WONo` display, and buttons. The actual report table will be in a partial template `_report_table.html` loaded via HTMX. Alpine.js will handle the "Check All" functionality.

**`project_reports/templates/project_reports/projectsummary/report_list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Project Summary: Bought Items</h2>
        <p class="text-lg font-semibold text-gray-700 mt-2 md:mt-0">
            WO No: <span class="text-blue-600">{{ won_no }}</span>
        </p>
    </div>

    <!-- Column Selection and Actions -->
    <div class="bg-white shadow-md rounded-lg p-6 mb-8" x-data="{ 
        allSelected: true, 
        selectedColumns: {{ selected_columns_initial|safe|json_script:"selected-columns-data" }},
        init() {
            this.updateAllSelected();
            this.$watch('selectedColumns', () => this.updateAllSelected());
        },
        toggleAll() {
            this.allSelected = !this.allSelected;
            const allColumnKeys = Array.from(document.querySelectorAll('#columnSelectionForm input[type="checkbox"]')).map(cb => cb.value);
            this.selectedColumns = this.allSelected ? allColumnKeys : [];
        },
        updateAllSelected() {
            const allColumnKeys = Array.from(document.querySelectorAll('#columnSelectionForm input[type="checkbox"]')).map(cb => cb.value);
            this.allSelected = this.selectedColumns.length === allColumnKeys.length;
        },
        submitForm() {
            // Manually trigger the form submission for export
            const form = document.getElementById('columnSelectionForm');
            const hiddenInput = document.createElement('input');
            hiddenInput.type = 'hidden';
            hiddenInput.name = 'WONo';
            hiddenInput.value = '{{ won_no }}';
            form.appendChild(hiddenInput);

            const hiddenInput2 = document.createElement('input');
            hiddenInput2.type = 'hidden';
            hiddenInput2.name = 'SwitchTo';
            hiddenInput2.value = '{{ switch_to }}';
            form.appendChild(hiddenInput2);
            
            form.submit();
        }
    }">
        <h3 class="text-xl font-semibold text-gray-800 mb-4">Select Report Columns:</h3>
        
        <form id="columnSelectionForm" method="post" 
              hx-get="{% url 'project_reports:project_summary_table_partial' %}" 
              hx-target="#reportTable-container" 
              hx-swap="innerHTML"
              hx-indicator="#table-loading-indicator"
              class="space-y-4">
            {% csrf_token %}
            
            <div class="flex items-center mb-4">
                <input type="checkbox" id="checkAll" x-model="allSelected" @change="toggleAll" 
                       class="form-checkbox h-5 w-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2">
                <label for="checkAll" class="text-sm font-medium text-gray-700 cursor-pointer">Check All</label>
            </div>

            <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {% for choice_name, choice_label in form.selected_columns.field.choices %}
                <div class="flex items-center">
                    <input type="checkbox" id="id_selected_columns_{{ forloop.counter0 }}" 
                           name="selected_columns" value="{{ choice_name }}" 
                           x-model="selectedColumns"
                           class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 mr-2">
                    <label for="id_selected_columns_{{ forloop.counter0 }}" class="text-sm font-medium text-gray-700">{{ choice_label }}</label>
                </div>
                {% endfor %}
            </div>

            <div class="mt-6 flex justify-center space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md transition duration-300 ease-in-out">
                    Show Report
                </button>
                <button type="button" @click="submitForm()" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-md transition duration-300 ease-in-out">
                    Export to Excel
                </button>
                <a href="{% url 'some_other_project_summary_page' %}" class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-6 rounded-md shadow-md transition duration-300 ease-in-out flex items-center justify-center">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <!-- Report Table Container (loaded by HTMX) -->
    <div id="reportTable-container"
         hx-trigger="load delay:100ms, change from:#columnSelectionForm input, submit from:#columnSelectionForm"
         hx-get="{% url 'project_reports:project_summary_table_partial' %}?WONo={{ won_no }}&SwitchTo={{ switch_to }}&{{ form.selected_columns.name }}[]={{ form.selected_columns.value|urlencode }}"
         hx-swap="innerHTML"
         hx-indicator="#table-loading-indicator"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- Initial Loading Indicator -->
        <div id="table-loading-indicator" class="flex justify-center items-center h-40" hx-indicator hx-target="this" hx-swap="outerHTML">
            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="ml-4 text-gray-600">Loading Report Data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Add DataTables and Alpine.js here (already in base.html) -->
<!-- Additional scripts if any for this specific page -->
<script>
    // This script ensures selected_columns_data from json_script is parsed by Alpine.js
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportLogic', () => ({
            allSelected: true,
            selectedColumns: JSON.parse(document.getElementById('selected-columns-data').textContent),
            init() {
                this.updateAllSelected();
                this.$watch('selectedColumns', () => this.updateAllSelected());
            },
            toggleAll() {
                this.allSelected = !this.allSelected;
                const allColumnKeys = Array.from(document.querySelectorAll('#columnSelectionForm input[type="checkbox"]')).map(cb => cb.value);
                this.selectedColumns = this.allSelected ? allColumnKeys : [];
            },
            updateAllSelected() {
                const allColumnKeys = Array.from(document.querySelectorAll('#columnSelectionForm input[type="checkbox"]')).map(cb => cb.value);
                // Ensure 'sn' (Sr.No) is not considered when checking if all are selected for the 'toggleAll' logic
                const selectableColumns = allColumnKeys.filter(col => col !== 'sn'); 
                this.allSelected = selectableColumns.every(col => this.selectedColumns.includes(col));
            },
            submitForm() {
                const form = document.getElementById('columnSelectionForm');
                form.action = "{% url 'project_reports:project_summary_export' %}?WONo={{ won_no }}&SwitchTo={{ switch_to }}";
                form.method = "POST"; // Ensure it's a POST for export
                form.submit();
            }
        }));
    });

    // Custom event listener for messages from HTMX (e.g., "No records to export.")
    document.body.addEventListener('showMessageAndRefreshList', function(e) {
        const message = e.detail; // Get the message from the custom event
        alert(message); // Display as a simple alert
        // You can integrate this with a more sophisticated toast notification system
        // The HX-Redirect header in the response will handle the redirection.
    });

    // Event listener for HTMX afterSwap to re-initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'reportTable-container') {
            $('#reportTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "scrollX": true, // Enable horizontal scrolling for wide tables
                "autoWidth": false, // Disable auto-width to allow more control over columns
                // Optionally define column widths here if needed
            });
        }
    });

</script>
{% endblock %}
```

**`project_reports/templates/project_reports/projectsummary/_report_table.html`**

```html
<div class="overflow-x-auto relative shadow-md sm:rounded-lg">
    {% if report_items %}
    <table id="reportTable" class="w-full text-sm text-left text-gray-500">
        <thead class="text-xs text-gray-700 uppercase bg-gray-50">
            <tr>
                {% for col_key in selected_columns %}
                <th scope="col" class="py-3 px-6 whitespace-nowrap">{{ column_headers|get_item:col_key|default:col_key|linebreaksbr }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for item in report_items %}
            <tr class="bg-white border-b hover:bg-gray-50">
                {% for col_key in selected_columns %}
                <td class="py-2 px-6 whitespace-nowrap text-gray-900">
                    {{ item|get_item:col_key|safe }} {# Use safe filter because the data contains <br> tags #}
                </td>
                {% endfor %}
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="p-6 text-center text-gray-500">
        {% if request.GET.WONo %}
        <p>No records found for WO No: {{ request.GET.WONo }}.</p>
        {% else %}
        <p>Please enter a Work Order Number to generate the report.</p>
        {% endif %}
    </div>
    {% endif %}
</div>

{% load project_reports_tags %} {# Load custom template tag for dictionary lookup #}

```
**`project_reports/templatetags/project_reports_tags.py`**
(Create this file in `project_reports/templatetags/` and `__init__.py` in the same directory)

```python
# project_reports/templatetags/project_reports_tags.py
from django import template

register = template.Library()

@register.filter(name='get_item')
def get_item(dictionary, key):
    """
    Custom template filter to get an item from a dictionary by its key.
    Useful for dynamic column rendering when column names are strings.
    """
    return dictionary.get(key)
```
After creating this file, ensure you restart your Django development server.

#### 4.5 URLs (`project_reports/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**
We'll define URLs for the main report page, the HTMX-loaded table partial, and the export endpoint.

```python
# project_reports/urls.py
from django.urls import path
from .views import ProjectSummaryReportView, ProjectSummaryTablePartialView, ProjectSummaryExportView
from .forms import ReportColumnSelectionForm # Import the form to access COLUMN_CHOICES

app_name = 'project_reports'

# Dynamically create the COLUMN_CHOICES_DICT for easy access
ProjectSummaryExportView.COLUMN_CHOICES_DICT = {choice[0]: choice[1] for choice in ReportColumnSelectionForm.COLUMN_CHOICES}


urlpatterns = [
    # Main report page
    path('project-summary-bought/', ProjectSummaryReportView.as_view(), name='project_summary_report'),
    
    # HTMX endpoint for loading the report table content
    path('project-summary-bought/table/', ProjectSummaryTablePartialView.as_view(), name='project_summary_table_partial'),
    
    # Endpoint for exporting the report data
    path('project-summary-bought/export/', ProjectSummaryExportView.as_view(), name='project_summary_export'),

    # Placeholder for the "Cancel" button redirection
    path('some-other-project-summary-page/', TemplateView.as_view(template_name='core/dashboard.html'), name='some_other_project_summary_page'),
]

```
**Important:** You need to include `project_reports.urls` in your project's main `urls.py`:

```python
# your_project/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('reports/', include('project_reports.urls')), # Include the new app's URLs
    # ... other project urls
]
```

#### 4.6 Tests (`project_reports/tests.py`)

**Task:** Write tests for the models (service logic) and views.

**Instructions:**
We'll create `TestCase` classes for both the `ProjectSummaryReportService` (unit tests for the complex data aggregation logic) and the views (integration tests to ensure correct rendering, HTMX responses, and export functionality). Since `managed=False` is used, we'll need to mock database interactions or use an in-memory SQLite database populated with test data that mimics the real schema. For simplicity in this example, we'll mock cursor execution in the service layer tests, and for view tests, we'll assume a database with dummy data is set up or mock the service call.

```python
# project_reports/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from django.conf import settings
from .models import ProjectSummaryReportService, ItemMaster, UnitMaster
from .forms import ReportColumnSelectionForm
import datetime
import csv
from io import StringIO

# Ensure Django settings are configured for tests (e.g., a test database)
# You might need to add a TEST dictionary to your DATABASES setting for more complex setups.
settings.configure(
    DEBUG=True,
    TEMPLATES=[{
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {'context_processors': ['django.template.context_processors.request', 'django.contrib.messages.context_processors.messages']},
    }],
    INSTALLED_APPS=[
        'django.contrib.contenttypes',
        'django.contrib.sessions',
        'django.contrib.messages',
        'project_reports', # Your app
        'core', # Assuming 'core' app exists for base.html
    ],
    DATABASES={
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': ':memory:',
        }
    },
    ROOT_URLCONF='your_project.urls', # Replace with your project's root urls.py
    DEFAULT_COMP_ID=1,
    DEFAULT_FIN_YEAR_ID=2023,
    SECRET_KEY='a-very-secret-key-for-testing-only',
)

class ProjectSummaryReportServiceTest(TestCase):
    """
    Unit tests for the ProjectSummaryReportService's data aggregation logic.
    We will mock the database cursor to control the data returned by SQL queries.
    """

    @patch('project_reports.models.connections')
    def test_get_report_data_basic(self, mock_connections):
        mock_cursor = MagicMock()
        mock_connections.__getitem__.return_value.cursor.return_value.__enter__.return_value = mock_cursor

        # Mock _execute_sql_query for the service
        mock_execute_sql = MagicMock(side_effect=[
            # Mock for item_master_sql
            [{
                'Id': 101, 'ItemCode': 'ITM001', 'ManfDesc': 'Test Item A',
                'StockQty': 100.0, 'UOMBasic': 'PCS', 'CId': 1, 'PartNo': 'P123'
            }],
            # Mock for _get_bom_qty
            [{'TotalQty': 50.0}],
            # Mock for _get_wis_qty
            [{'TotalQty': 45.0}],
            # Mock for pr_sql (no PRs for simplicity)
            [],
            # Mock for _get_bom_qty again for second item
            [{'TotalQty': 30.0}],
            # Mock for _get_wis_qty again for second item
            [{'TotalQty': 25.0}],
            # Mock for item_master_sql (second item)
            [{
                'Id': 102, 'ItemCode': 'ITM002', 'ManfDesc': 'Test Item B',
                'StockQty': 200.0, 'UOMBasic': 'KG', 'CId': 1, 'PartNo': 'P456'
            }],
            # Mock for pr_sql for second item (with one PR)
            [{'PRId': 1, 'PRNo': 'PR001', 'PRDate': '2023-01-15', 'PRQty': 10}],
            # Mock for po_sql for the PR (with one PO)
            [{'Id': 1001, 'PONo': 'PO001', 'SysDate': '2023-01-20', 'SupplierId': 'SUP001', 'Authorize': 1, 'Qty': 10}],
            # Mock for supplier_sql
            [{'SupplierName': 'Test Supplier [SUP001]'}],
            # Mock for gin_sql (no GIN for simplicity)
            [],
            # ... and so on for other nested queries if they were expected
        ])
        
        # Patch the internal _execute_sql_query and date formatter
        with patch.object(ProjectSummaryReportService, '_execute_sql_query', new=mock_execute_sql):
            with patch.object(ProjectSummaryReportService, '_format_date_dmy', return_value='15-01-2023'):
                report = ProjectSummaryReportService.get_report_data(
                    won_no='WO123', comp_id=1, fin_year_id=2023
                )

                self.assertEqual(len(report), 1) # Only one item is mocked initially for simplicity
                self.assertEqual(report[0]['sn'], 1)
                self.assertEqual(report[0]['item_code'], 'ITM001')
                self.assertEqual(report[0]['bom_qty'], 50.0)
                self.assertEqual(report[0]['wis_qty'], 45.0)
                self.assertEqual(report[0]['description'], 'Test Item A')
                self.assertEqual(report[0]['pr_no'], '') # No PRs for this mocked item
                self.assertEqual(report[0]['po_no'], '') # No POs either

                # Check if correct SQL queries were attempted (order might vary slightly based on actual execution flow)
                # We need to be careful with exact mock call count for deeply nested loops.
                # A better approach might be to test each helper method separately and then the main method.
                # Here, we verify the main SQL was called.
                mock_connections.__getitem__.return_value.cursor.return_value.__enter__.return_value.execute.assert_called_with(
                    patch.ANY, ['WO123', 1, 2023, 'WO123', 1, 2023]
                )

    def test_format_date_dmy(self):
        self.assertEqual(ProjectSummaryReportService._format_date_dmy('2023-03-15'), '15-03-2023')
        self.assertEqual(ProjectSummaryReportService._format_date_dmy('15-03-2023'), '15-03-2023')
        self.assertEqual(ProjectSummaryReportService._format_date_dmy('03/15/2023'), '15-03-2023')
        self.assertEqual(ProjectSummaryReportService._format_date_dmy(''), '')
        self.assertEqual(ProjectSummaryReportService._format_date_dmy(None), '')
        self.assertEqual(ProjectSummaryReportService._format_date_dmy('Invalid Date'), 'Invalid Date') # Should return original if unparseable


class ProjectSummaryViewsTest(TestCase):
    """
    Integration tests for the Project Summary views.
    We will mock the ProjectSummaryReportService to control report data for view tests.
    """
    def setUp(self):
        self.client = Client()
        self.won_no = 'WO_TEST_001'
        self.base_url = reverse('project_reports:project_summary_report')
        self.table_partial_url = reverse('project_reports:project_summary_table_partial')
        self.export_url = reverse('project_reports:project_summary_export')

        # Mocked report data for consistent testing
        self.mock_report_data = [
            {
                'sn': 1, 'item_code': 'ITM001', 'description': 'Description A', 'uom': 'PCS',
                'bom_qty': 100.0, 'wis_qty': 90.0, 'stock_qty': 50.0,
                'pr_no': 'PR001<br>PR002', 'pr_date': '01-01-2023<br>05-01-2023', 'pr_qty': '10<br>20',
                'po_no': 'PO001', 'po_date': '10-01-2023', 'supplier': 'Sup A', 'authorized': 'Yes', 'po_qty': '30',
                'gin_no': '', 'gin_date': '', 'gin_qty': '',
                'grr_no': '', 'grr_date': '', 'grr_qty': '',
                'gqn_no': '', 'gqn_date': '', 'gqn_qty': '',
            },
            {
                'sn': 2, 'item_code': 'ITM002', 'description': 'Description B', 'uom': 'KG',
                'bom_qty': 200.0, 'wis_qty': 180.0, 'stock_qty': 150.0,
                'pr_no': '', 'pr_date': '', 'pr_qty': '',
                'po_no': '', 'po_date': '', 'supplier': '', 'authorized': '', 'po_qty': '',
                'gin_no': '', 'gin_date': '', 'gin_qty': '',
                'grr_no': '', 'grr_date': '', 'grr_qty': '',
                'gqn_no': '', 'gqn_date': '', 'gqn_qty': '',
            }
        ]

    @patch('project_reports.models.ProjectSummaryReportService.get_report_data')
    def test_report_view_get(self, mock_get_report_data):
        mock_get_report_data.return_value = self.mock_report_data
        response = self.client.get(f"{self.base_url}?WONo={self.won_no}&SwitchTo=1")
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_reports/projectsummary/report_list.html')
        self.assertContains(response, self.won_no)
        self.assertContains(response, 'Sr.No') # Check if column headers are present

    @patch('project_reports.models.ProjectSummaryReportService.get_report_data')
    def test_report_table_partial_view_htmx(self, mock_get_report_data):
        mock_get_report_data.return_value = self.mock_report_data
        
        # Simulate HTMX request by adding HTTP_HX_REQUEST header
        response = self.client.get(
            f"{self.table_partial_url}?WONo={self.won_no}&SwitchTo=1",
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_reports/projectsummary/_report_table.html')
        self.assertContains(response, self.mock_report_data[0]['item_code'])
        self.assertContains(response, self.mock_report_data[1]['description'])
        self.assertContains(response, '<table id="reportTable"') # Verify DataTables table structure
        
        # Test with specific columns selected
        selected_cols = ['sn', 'item_code', 'description']
        response_filtered = self.client.get(
            f"{self.table_partial_url}?WONo={self.won_no}&SwitchTo=1&selected_columns={'&selected_columns='.join(selected_cols)}",
            HTTP_HX_REQUEST='true'
        )
        self.assertEqual(response_filtered.status_code, 200)
        self.assertContains(response_filtered, 'Item Code')
        self.assertNotContains(response_filtered, 'UOM') # Should not contain unselected column

    @patch('project_reports.models.ProjectSummaryReportService.get_report_data')
    def test_report_table_partial_view_no_won(self, mock_get_report_data):
        mock_get_report_data.return_value = []
        response = self.client.get(self.table_partial_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "Please enter a Work Order Number to generate the report.")
        mock_get_report_data.assert_not_called()

    @patch('project_reports.models.ProjectSummaryReportService.get_report_data')
    def test_report_export_view_post(self, mock_get_report_data):
        mock_get_report_data.return_value = self.mock_report_data
        
        # Get all column keys from the form for full export
        all_column_keys = [choice[0] for choice in ReportColumnSelectionForm.COLUMN_CHOICES]
        
        data = {
            'selected_columns': all_column_keys,
            'WONo': self.won_no,
            'SwitchTo': '1',
            # Add csrf_token if you're not skipping it for tests
        }
        
        # Simulate a POST request for export
        response = self.client.post(f"{self.export_url}?WONo={self.won_no}&SwitchTo=1", data)

        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'text/csv')
        self.assertTrue('Content-Disposition' in response)
        self.assertTrue(f'filename="ProjectSummary_Bought_WONo_{self.won_no}' in response['Content-Disposition'])

        content = response.content.decode('utf-8')
        csv_reader = csv.reader(StringIO(content))
        rows = list(csv_reader)

        # Verify headers
        expected_headers = [header[1] for header in ReportColumnSelectionForm.COLUMN_CHOICES]
        self.assertEqual(rows[0], expected_headers)
        
        # Verify data row 1 (remove <br> tags from expected data for comparison)
        expected_row1_data = [str(self.mock_report_data[0].get(col, '')).replace('<br>', '\n') for col in all_column_keys]
        self.assertEqual(rows[1], expected_row1_data)

        # Verify data row 2
        expected_row2_data = [str(self.mock_report_data[1].get(col, '')).replace('<br>', '\n') for col in all_column_keys]
        self.assertEqual(rows[2], expected_row2_data)

    @patch('project_reports.models.ProjectSummaryReportService.get_report_data')
    def test_report_export_view_no_data(self, mock_get_report_data):
        mock_get_report_data.return_value = []
        data = {
            'selected_columns': [choice[0] for choice in ReportColumnSelectionForm.COLUMN_CHOICES],
            'WONo': self.won_no,
            'SwitchTo': '1',
        }
        response = self.client.post(f"{self.export_url}?WONo={self.won_no}&SwitchTo=1", data)
        
        # Expect a 204 No Content for HTMX with a trigger and redirect
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response)
        self.assertIn('HX-Redirect', response)
        self.assertIn('No records to export.', response['HX-Trigger'])
        self.assertIn(self.base_url, response['HX-Redirect'])

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
*   **HTMX:** All dynamic content loading (the report table) and form submissions (for showing report and export) are handled by HTMX.
    *   The main `report_list.html` uses `hx-get` to fetch `_report_table.html` when the page loads, or when the column selection form is submitted/changed.
    *   The `hx-target` and `hx-swap` attributes ensure only the table content is updated without a full page refresh.
    *   The "Show Report" button implicitly triggers the HTMX form submission.
    *   The "Export to Excel" button uses JavaScript to manually trigger the form submission as a `POST` request to the dedicated export URL, ensuring a file download.
    *   HTMX custom events (`htmx:afterSwap`) are used to re-initialize DataTables after new table content is loaded.
    *   For the "No records to export" scenario, an `HX-Trigger` is sent back to the client to display an alert, and an `HX-Redirect` is sent to refresh the main page.
*   **Alpine.js:** Manages the UI state for the "Check All" checkbox.
    *   An `x-data` attribute on the containing `div` initializes Alpine.js.
    *   `x-model` binds the `allSelected` property to the "Check All" checkbox and `selectedColumns` array to individual column checkboxes.
    *   `@change` and `$watch` directives react to user interactions to keep the "Check All" state synchronized with individual column selections.
    *   It also handles the manual form submission for export, setting the correct `action` and `method`.
*   **DataTables:** The `_report_table.html` partial template includes the `<table id="reportTable">` and a JavaScript block to initialize DataTables on it. This block will be executed each time HTMX swaps in the new table content.
    *   `scrollX: true` is included to handle wide tables, which is likely given the number of columns.

---

## Final Notes

This comprehensive plan provides a robust and modern Django solution for the ASP.NET report. Key elements include:

*   **Fat Model, Thin View:** Complex data aggregation resides in `ProjectSummaryReportService`, keeping views concise and focused on request/response handling.
*   **HTMX-first approach:** Ensures a highly responsive user experience by avoiding full page reloads for common interactions.
*   **Alpine.js for UI logic:** Manages simple client-side state like the "Check All" functionality directly in HTML, reducing reliance on larger JavaScript frameworks.
*   **DataTables for powerful UI:** Provides client-side searching, sorting, and pagination for large datasets.
*   **Clear Separation of Concerns:** Database interaction, business logic, form handling, view logic, and presentation are all in their dedicated files.
*   **Test Coverage:** Included unit tests for the critical report service logic and integration tests for view functionality, ensuring reliability.

Remember to replace placeholders like `your_project.urls` and adjust `DEFAULT_COMP_ID`, `DEFAULT_FIN_YEAR_ID` in Django settings to match your actual environment. For the "Cancel" button, `some_other_project_summary_page` should be replaced with the actual URL you wish to redirect to.