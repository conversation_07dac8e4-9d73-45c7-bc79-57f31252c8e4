## ASP.NET to Django Conversion Script: Project Summary Details

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

## Instructions:

The ASP.NET code for "Project Summary Details" primarily interacts with several database tables to display project Bill of Materials (BOM) information, work-in-progress quantities, and material issue details. The core operations involve retrieving and calculating aggregated data for display in a chart and a detailed grid.

**Identified Tables and Inferred Columns:**

-   **`tblDG_BOM_Master` (Main table for Project/Work Order BOM)**
    -   `Id` (Primary Key, inferred)
    -   `ItemId` (Integer, foreign key to `tblDG_Item_Master`)
    -   `WONo` (String, Work Order Number)
    -   `PId` (Integer, Parent ID in BOM hierarchy)
    -   `CId` (Integer, Component ID in BOM hierarchy)
    -   `Qty` (Decimal/Float, represents Unit Quantity)
    -   `Weldments` (String, inferred from `Weld` in GridView)
    -   `CompId` (Integer, Company ID from session)
    -   `FinYearId` (Integer, Financial Year ID from session)

-   **`tblDG_Item_Master` (Item Details)**
    -   `Id` (Primary Key, matches `ItemId` in `tblDG_BOM_Master`)
    -   `ItemCode` (String)
    -   `ManfDesc` (String, corresponds to 'Description' in GridView)
    -   `UOMBasic` (Integer, foreign key to `Unit_Master`)
    -   `StockQty` (Decimal/Float)
    -   `CompId` (Integer, Company ID)

-   **`Unit_Master` (Unit of Measurement)**
    -   `Id` (Primary Key, matches `UOMBasic` in `tblDG_Item_Master`)
    -   `Symbol` (String, represents 'UOM' in GridView)

-   **`tblInv_WIS_Master` / `tblInv_WIS_Details` (Work In progress / Issue Details)**
    -   `tblInv_WIS_Master`: `WISNo`, `WONo`, `CompId` (inferred)
    -   `tblInv_WIS_Details`: `WISNo` (FK), `ItemId`, `PId`, `CId`, `IssuedQty` (Decimal/Float)

-   **`tblInv_MaterialIssue_Master` / `tblInv_MaterialIssue_Details` (Material Issue)**
    -   `tblInv_MaterialIssue_Master`: `Id` (PK), `MRSId` (FK), `CompId` (inferred)
    -   `tblInv_MaterialIssue_Details`: `MId` (FK), `MRSId` (FK), `ItemId`, `IssueQty` (Decimal/Float)

-   **`tblInv_MaterialRequisition_Master` / `tblInv_MaterialRequisition_Details` (Material Requisition)**
    -   `tblInv_MaterialRequisition_Master`: `Id` (PK), `CompId` (inferred)
    -   `tblInv_MaterialRequisition_Details`: `MId` (FK), `Id` (PK), `ItemId`, `WONo` (inferred)

**Mapping for Django Models:**

-   `[TABLE_NAME]` will be `tblDG_BOM_Master`, `tblDG_Item_Master`, `Unit_Master`, `tblInv_WIS_Details`, `tblInv_MaterialIssue_Details`, `tblInv_MaterialRequisition_Details` (and their respective masters for relationships).
-   `[MODEL_NAME]` will be `ProjectBOMItem` for `tblDG_BOM_Master` and corresponding names for others.

## Step 2: Identify Backend Functionality

Task: Determine the core operations in the ASP.NET code.

## Instructions:

The ASP.NET page `ProjectSummary_Details.aspx` is primarily a "Read" operation (displaying data). It aggregates and presents information from various tables in two main formats: a graphical view (chart) and a detailed tabular view (grid).

-   **Read (Display):**
    -   Data is retrieved from `tblDG_BOM_Master` filtered by `WONo`, `PId='0'`, and `CompId`.
    -   This primary data is then enriched by looking up details from `tblDG_Item_Master` (ItemCode, Description, StockQty) and `Unit_Master` (UOM).
    -   Complex calculations are performed:
        -   `BOMQty`: Calculated recursively using `fun.BOMRecurQty` and `fun.BOMTreeQty` based on `tblDG_BOM_Master` hierarchy. This represents the total quantity required for a BOM item.
        -   `TotWISQty`: Total Work-In-Store (WIS) issued quantity, summed from `tblInv_WIS_Details`.
        -   `MINQty`: Material Issue Note (MIN) quantity, summed from `tblInv_MaterialIssue_Details`.
        -   `Progress`: Calculated as `((TotWISQty + MINQty) * 100) / BomQty`.
        -   `BalanceBOMQty`: `BomQty - (TotWISQty + CalIssueQty + MINQty)`.
        -   `DryRunQty` (CalIssueQty): Quantity to be issued based on stock vs. BOM balance.
        -   `AfterStockQty` (CalStockQty): Remaining stock after ideal issue.
    -   The processed data is then bound to `Chart1` (Graphical View) and `GridView1` (Detail View).

-   **Navigation/Redirection:**
    -   `BtnCancel_Click`: Redirects to `ProjectSummary.aspx`.
    -   `GridView1_RowCommand` (on "Item Code" link): Redirects to `Componant_Details.aspx` with specific `Id`, `PID`, `CID`, `WONO` query parameters for a drill-down view of a component.

**Validation Logic:**
-   The C# code includes checks for `DBNull.Value` before type conversion for quantities, which should be handled by Django's ORM and model field types (e.g., `DecimalField(null=True, blank=True)`).
-   `try-catch` blocks for general exceptions, which Django handles gracefully with appropriate error pages or logging.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET controls indicate a clear structure for displaying information:

-   **`TabContainer` (`cc1:TabContainer`):** This is a key UI element that organizes content into "Graphical View" and "Detail View" tabs.
    -   **Django Conversion:** Will be replaced by a combination of HTMX and Alpine.js. HTMX will handle fetching tab content dynamically, and Alpine.js will manage the active tab state and simple UI interactions.
-   **`Label` (`asp:Label lblWo`):** Displays the Work Order Number.
    -   **Django Conversion:** Simple Django template variable `{{ wono }}`.
-   **`Button` (`asp:Button BtnCancel`):** Used for navigation.
    -   **Django Conversion:** An `<a>` tag styled as a button, or a `<button>` with `hx-redirect` for navigation.
-   **`Chart` (`asp:Chart Chart1`):** A server-side control for rendering a bar chart of project progress.
    -   **Django Conversion:** The data for the chart will be provided by a Django view returning a HTMX partial. The actual "chart" can be rendered using basic HTML/CSS (e.g., `div` elements with dynamic widths/heights controlled by Alpine.js for a simple bar graph) or by providing the data as JSON for a modern client-side charting library (which would violate "no additional JavaScript" if it's a *new* library). Given the strict `HTMX + Alpine.js` rule, we'll aim for a pure HTML/CSS representation of the bar chart using Alpine.js for data binding and styling.
-   **`GridView` (`asp:GridView GridView1`):** Displays detailed tabular data with custom columns and a "RowCommand" event for interaction.
    -   **Django Conversion:** Replaced by a standard HTML `<table>` element enhanced with DataTables for client-side features (searching, sorting, pagination). The table content will be loaded via HTMX from a Django partial view.
-   **`LinkButton` (`asp:LinkButton` inside GridView `ItemTemplate`):** Triggers a "sel" command to navigate to a component detail page.
    -   **Django Conversion:** An `<a>` tag with `href` pointing to the component detail URL. If a modal is desired, HTMX can load the detail page into a modal.

**Client-Side Interactions (HTMX & Alpine.js):**
-   The original ASP.NET code uses `AutoEventWireup`, `onclick` attributes, and `onrowcommand` for server-side postbacks.
-   **Django Conversion:** All these interactions will be reimagined using HTMX for partial page updates (e.g., loading tab content, refreshing the grid) and Alpine.js for local UI state management (e.g., showing/hiding modals, managing active tabs).

## Step 4: Generate Django Code

The following Django files will be created within a new Django app, let's call it `project_summary`.

### 4.1 Models (`project_summary/models.py`)

Task: Create Django models based on the database schema, adhering to `managed=False` and incorporating business logic.

## Instructions:

We will define Django models for the identified tables. Crucially, the complex calculation logic (`BOMQty`, `TotWISQty`, `MINQty`, `Progress`, `BalanceBOMQty`, `DryRunQty`, `AfterStockQty`) from the C# code-behind will be implemented as methods on the `ProjectBOMItem` model, following the "fat model" principle. For recursive BOM calculations (`BOMTreeQty`, `BOMRecurQty`), we will provide simplified placeholder implementations, noting that a full production implementation might require more advanced database features like CTEs or raw SQL for performance on large hierarchies.

```python
from django.db import models
from django.db.models import Sum, F, OuterRef, Subquery, DecimalField, Value
from django.db.models.functions import Coalesce
from decimal import Decimal

# --- Supporting Models (for relationships and lookups) ---
# Assuming these tables exist and are unmanaged in the database

class ItemMaster(models.Model):
    """
    Maps to tblDG_Item_Master, holding item details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    description = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic_id = models.IntegerField(db_column='UOMBasic') # FK to Unit_Master
    stock_qty = models.DecimalField(db_column='StockQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))
    comp_id = models.IntegerField(db_column='CompId', null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code

class UnitMaster(models.Model):
    """
    Maps to Unit_Master, holding unit of measurement symbols.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit of Measure'
        verbose_name_plural = 'Units of Measure'

    def __str__(self):
        return self.symbol

class WISDetail(models.Model):
    """
    Maps to tblInv_WIS_Details, holding Work In Store issued quantities.
    """
    # Assuming primary key might be composite or an auto-incrementing Id not directly referenced
    # Using composite key equivalent or single ID if available for unmanaged
    id = models.AutoField(primary_key=True) # Placeholder, adjust based on actual PK
    wis_no = models.CharField(db_column='WISNo', max_length=50) # FK to tblInv_WIS_Master
    item_id = models.IntegerField(db_column='ItemId')
    p_id = models.IntegerField(db_column='PId')
    c_id = models.IntegerField(db_column='CId')
    issued_qty = models.DecimalField(db_column='IssuedQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))

    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Details'
        verbose_name = 'WIS Detail'
        verbose_name_plural = 'WIS Details'

class MaterialIssueDetail(models.Model):
    """
    Maps to tblInv_MaterialIssue_Details, holding material issue quantities.
    """
    id = models.AutoField(primary_key=True) # Placeholder, adjust based on actual PK
    m_id = models.IntegerField(db_column='MId') # FK to tblInv_MaterialIssue_Master
    mrs_id = models.IntegerField(db_column='MRSId') # FK to tblInv_MaterialRequisition_Details
    item_id = models.IntegerField(db_column='ItemId')
    issue_qty = models.DecimalField(db_column='IssueQty', max_digits=18, decimal_places=3, default=Decimal('0.000'))

    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Details'
        verbose_name = 'Material Issue Detail'
        verbose_name_plural = 'Material Issue Details'

# --- Main Model for Project Summary ---

class ProjectBOMItem(models.Model):
    """
    Maps to tblDG_BOM_Master, representing a Bill of Material item within a project.
    All complex calculations are embedded as methods here.
    """
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming 'Id' as PK, adjust if different
    item_id = models.IntegerField(db_column='ItemId') # FK to ItemMaster
    wo_no = models.CharField(db_column='WONo', max_length=50)
    p_id = models.IntegerField(db_column='PId') # Parent ID in BOM hierarchy
    c_id = models.IntegerField(db_column='CId') # Component ID in BOM hierarchy
    unit_qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # This is Qty in tblDG_BOM_Master
    weldments = models.CharField(db_column='Weldments', max_length=50, null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')

    class Meta:
        managed = False
        db_table = 'tblDG_BOM_Master'
        verbose_name = 'Project BOM Item'
        verbose_name_plural = 'Project BOM Items'
        # Consider adding a unique_together constraint if WONo, ItemId, PId, CId form a unique key

    def __str__(self):
        return f"BOM: {self.wo_no} - Item {self.item_id}"

    # --- Derived Attributes (Business Logic - "Fat Model") ---

    @property
    def item_details(self):
        """Fetches associated ItemMaster details."""
        try:
            return ItemMaster.objects.get(id=self.item_id, comp_id=self.comp_id)
        except ItemMaster.DoesNotExist:
            return None

    @property
    def uom_symbol(self):
        """Fetches the UOM symbol from UnitMaster."""
        item = self.item_details
        if item and item.uom_basic_id:
            try:
                return UnitMaster.objects.get(id=item.uom_basic_id).symbol
            except UnitMaster.DoesNotExist:
                pass
        return "N/A"
    
    @property
    def item_code(self):
        return self.item_details.item_code if self.item_details else "N/A"

    @property
    def description(self):
        return self.item_details.description if self.item_details else "N/A"
        
    @property
    def stock_qty(self):
        return self.item_details.stock_qty if self.item_details else Decimal('0.000')

    def get_bom_tree_qty(self):
        """
        Calculates BOM Tree Quantity.
        This is a placeholder for the complex recursive logic of fun.BOMTreeQty.
        A real implementation would involve a recursive CTE or specialized queries.
        For demonstration, assuming a flat structure or direct Qty for simplicity,
        or a limited recursive lookup.
        The C# code iterates through `g = fun.BOMTreeQty(...)` and multiplies.
        This implies `fun.BOMTreeQty` returns a list of quantities from the tree path.
        We'll simulate it by just returning the item's own quantity, or a more complex sum if needed.
        """
        # This is a simplified placeholder.
        # The original fun.BOMTreeQty appears to calculate product of Qty in BOM hierarchy.
        # For a full implementation, this would require querying child BOM items recursively
        # and multiplying their quantities.
        # Example: product of all 'Qty' values from parent to current item in the BOM tree.
        
        # If it's the top level (PId=0 as in original code), it's just its own Qty.
        # If it's a component, it might be this Qty * parent's BOM Qty.
        
        # Simplified: Just return the item's own unit_qty.
        # In a real system, you might fetch parent items and multiply their Qty
        # or use a database CTE for full BOM explosion.
        
        # Placeholder for full recursive BOM quantity calculation
        # This would require fetching parent items recursively based on PId/CId
        # and multiplying their quantities up the tree.
        # For simplicity, returning the item's own quantity.
        # Real implementation: consider database views or stored procedures for complex BOM explosions.
        return self.unit_qty

    @property
    def bom_qty(self):
        """
        Calculates the Bill of Materials (BOM) quantity for this item.
        This combines the recursive BOM quantity and might scale it.
        The C# code calls `fun.BOMRecurQty` for Chart1 and then `fun.BOMTreeQty` for GridView1,
        and multiplies `h = h * g[j]` where `g` is the result of `fun.BOMTreeQty`.
        This suggests `h` is an accumulator and `g` is a list of quantities along the path.
        Let's assume `BomQty` is effectively `get_bom_tree_qty()`.
        """
        return self.get_bom_tree_qty()

    @property
    def total_wis_issued_qty(self):
        """
        Calculates the total Work-In-Store (WIS) issued quantity for this item.
        Maps to 'TotWISQty' in the ASP.NET code.
        """
        # Sum of IssuedQty from tblInv_WIS_Details for this item, WO, PId, CId
        total_qty = WISDetail.objects.filter(
            wis_no__in=Subquery(
                WISMaster.objects.filter(
                    wo_no=self.wo_no, comp_id=self.comp_id
                ).values('wis_no')
            ),
            item_id=self.item_id,
            p_id=self.p_id,
            c_id=self.c_id
        ).aggregate(
            sum_issued_qty=Coalesce(Sum('issued_qty'), Decimal('0.000'))
        )['sum_issued_qty']
        return total_qty

    @property
    def min_qty(self):
        """
        Calculates the total Material Issue Note (MIN) quantity for this item.
        Maps to 'MINQty' in the ASP.NET code.
        """
        # Sum of IssueQty from tblInv_MaterialIssue_Details based on complex join logic
        # Original query: tblInv_MaterialIssue_Details,tblInv_MaterialIssue_Master,
        # tblInv_MaterialRequisition_Master,tblInv_MaterialRequisition_Details
        # WHERE clauses: tblInv_MaterialIssue_Master.Id=tblInv_MaterialIssue_Details.MId
        # AND tblInv_MaterialIssue_Master.CompId='CompId'
        # AND tblInv_MaterialRequisition_Master.Id=tblInv_MaterialRequisition_Details.MId
        # AND tblInv_MaterialRequisition_Master.Id=tblInv_MaterialIssue_Master.MRSId
        # AND tblInv_MaterialRequisition_Details.Id=tblInv_MaterialIssue_Details.MRSId
        # AND tblInv_MaterialRequisition_Details.ItemId='ItemId'
        # AND tblInv_MaterialRequisition_Details.WONo='WONo'

        # This will be a complex subquery or direct filter if relations are defined
        # We need MaterialIssueDetail connected to RequisitionDetail, which is connected to RequisitionMaster
        # and MaterialIssueMaster connected to RequisitionMaster via MRSId.
        
        # Assuming MaterialRequisitionDetail.won_no and ItemId are available
        # And MaterialIssueDetail.mrs_id links to MaterialRequisitionDetail.id
        
        # This subquery aims to mimic the original JOIN logic.
        # Requires definition of WISMaster, MaterialIssueMaster, MaterialRequisitionMaster
        # models if not already present. For brevity, assuming they are just tables.
        
        # A full ORM translation would be more complex and depend on explicit FK definitions.
        # For 'managed=False', we rely on field names matching DB columns and IDs.
        
        # Placeholder for complex MINQty calculation
        # This assumes a simplified lookup. A full ORM query for this complex join would be:
        """
        from tblInv_MaterialIssue_Master.objects.filter(
            comp_id=self.comp_id,
            mrs_id__in=Subquery(
                MaterialRequisitionMaster.objects.filter(
                    id=OuterRef('id_from_issue_master') # Need to link these
                ).values('id')
            )
        ).aggregate(sum_issue_qty=Sum('materialissuedetail__issue_qty'))['sum_issue_qty']
        """
        # For simplicity, let's assume a direct query on MaterialIssueDetail if possible,
        # or mock a value. Given the complexity, this might warrant a database view or function.

        # Simplified approach: query MaterialIssueDetail where item_id matches this BOM item
        # and won_no is derived from MaterialRequisitionDetail
        # (This is an approximation due to lack of full schema and clsFunctions logic)
        
        # To get the WONo for MaterialRequisitionDetail, we would need to join back to
        # MaterialRequisitionMaster and then somehow to the WONo. This is a significant
        # logical gap without a full schema.
        
        # For now, let's simplify based on the ItemId and WONo, acknowledging it's an approximation.
        total_min_qty = MaterialIssueDetail.objects.filter(
             item_id=self.item_id,
             mrs_id__in=Subquery( # This tries to link via MRSId in Requisition Details
                 MaterialRequisitionDetail.objects.filter(
                     item_id=self.item_id,
                     # WONo is in MaterialRequisition_Details, not MaterialIssue_Details directly
                     # This join needs MaterialRequisition_Details.won_no which isn't explicitly listed in its schema
                     # Let's assume MaterialRequisitionDetail also has a WONo.
                     # If not, this needs deeper schema inference or raw SQL.
                     # Example: MaterialRequisitionDetail.objects.filter(item_id=OuterRef('item_id'), won_no=self.wo_no)
                     # For demonstration, we'll try to directly filter by item_id.
                     # This simplification might not perfectly match original logic.
                 ).values('id') # Returns MRSId from RequisitionDetails
             )
        ).aggregate(
            sum_issue_qty=Coalesce(Sum('issue_qty'), Decimal('0.000'))
        )['sum_issue_qty']
        
        # A more robust solution might involve direct SQL or a custom manager.
        # As per instructions, move logic to model, so ORM preferred if possible.
        # Given the complex join in C# for MINQty, a raw SQL query or DB view could be optimal.
        # For now, a simplified subquery based on common fields.
        return total_min_qty

    @property
    def total_work_progress_percent(self):
        """
        Calculates the total work progress percentage.
        Maps to 'TotWM' and 'Progress in(%)' in the ASP.NET code.
        Formula: (((TotWISQty + MINQty) * 100) / BomQty)
        """
        bom_qty = self.bom_qty
        if bom_qty == Decimal('0.000'):
            return Decimal('0.000') # Avoid division by zero
        
        total_issued = self.total_wis_issued_qty + self.min_qty
        progress = (total_issued * Decimal('100.000')) / bom_qty
        return progress.quantize(Decimal('0.000')) # Round to 3 decimal places

    @property
    def balance_bom_qty(self):
        """
        Calculates the balance BOM quantity to issue.
        Maps to 'Bal.BOM Qty' in the ASP.NET code.
        Formula: (BomQty - (TotWISQty + CalIssueQty + MINQty))
        Here, CalIssueQty is self.dry_run_qty.
        """
        bom_qty = self.bom_qty
        total_issued_and_dry_run = self.total_wis_issued_qty + self.dry_run_qty + self.min_qty
        balance = bom_qty - total_issued_and_dry_run
        return balance.quantize(Decimal('0.000'))

    @property
    def dry_run_qty(self):
        """
        Calculates the quantity to issue (Dry Run Qty in ASP.NET).
        Based on stock vs. remaining BOM quantity.
        Maps to 'DryRunQty' in the ASP.NET code.
        """
        stock_qty = self.stock_qty
        remaining_bom_qty_pre_dryrun = self.bom_qty - self.total_wis_issued_qty - self.min_qty

        if remaining_bom_qty_pre_dryrun <= Decimal('0.000'):
            return Decimal('0.000') # No more needed from stock if BOM is covered

        if stock_qty >= remaining_bom_qty_pre_dryrun:
            return remaining_bom_qty_pre_dryrun
        else:
            return stock_qty # Can only issue what's in stock
            
    @property
    def after_stock_qty(self):
        """
        Calculates the remaining stock quantity after the 'dry run' issue.
        Maps to 'After Stock Qty' in the ASP.NET code.
        """
        stock_qty = self.stock_qty
        dry_run = self.dry_run_qty
        
        return (stock_qty - dry_run).quantize(Decimal('0.000'))

# Add placeholder models for Master tables used in joins if necessary for ORM to work correctly
# (e.g., tblInv_WIS_Master, tblInv_MaterialIssue_Master, tblInv_MaterialRequisition_Master)
# For managed=False, they only need ID and the fields they're filtered/joined on.

class WISMaster(models.Model):
    wis_no = models.CharField(db_column='WISNo', primary_key=True, max_length=50)
    won_no = models.CharField(db_column='WONo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblInv_WIS_Master'

class MaterialIssueMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    mrs_id = models.IntegerField(db_column='MRSId')
    comp_id = models.IntegerField(db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblInv_MaterialIssue_Master'

class MaterialRequisitionMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    comp_id = models.IntegerField(db_column='CompId')
    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Master'

class MaterialRequisitionDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.IntegerField(db_column='MId') # FK to MaterialRequisitionMaster
    item_id = models.IntegerField(db_column='ItemId')
    wo_no = models.CharField(db_column='WONo', max_length=50) # Inferred based on usage
    class Meta:
        managed = False
        db_table = 'tblInv_MaterialRequisition_Details'

```

### 4.2 Forms (`project_summary/forms.py`)

Task: Define Django forms for user input. As this ASP.NET page is primarily for display, a full CRUD form for `ProjectBOMItem` isn't directly needed for its *current* functionality. However, following the template, a generic form for the `ProjectBOMItem` model is provided for future CRUD capabilities.

```python
from django import forms
from .models import ProjectBOMItem

class ProjectBOMItemForm(forms.ModelForm):
    """
    Generic form for ProjectBOMItem. Not directly used by the read-only Project Summary page,
    but provided for future or general CRUD operations on BOM items.
    """
    class Meta:
        model = ProjectBOMItem
        # Fields that could be edited for a BOM item.
        # Note: WONo, PId, CId are often part of a composite key or identifier,
        # and ItemId is a lookup. Adjust fields based on actual editability.
        fields = ['item_id', 'wo_no', 'p_id', 'c_id', 'unit_qty', 'weldments'] 
        widgets = {
            'item_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'p_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'c_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'unit_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'weldments': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    # No custom validation for this generic form at this stage, as original page is read-only.
```

### 4.3 Views (`project_summary/views.py`)

Task: Implement the core functionality (data retrieval and presentation for summary and detail views) using Class-Based Views. Views are kept thin by offloading complex data processing to the `ProjectBOMItem` model.

```python
from django.views.generic import TemplateView, View, ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import ProjectBOMItem, ItemMaster
from .forms import ProjectBOMItemForm
import json

class ProjectSummaryView(TemplateView):
    """
    Main view for the Project Summary Details page.
    This replaces the overall ProjectSummary_Details.aspx.
    It fetches initial data like WONo and sets up the context for tabbed content.
    """
    template_name = 'project_summary/projectbomitem/summary.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Retrieve WONo from query string, similar to Request.QueryString["WONo"]
        wono = self.request.GET.get('WONo', '')
        context['wo_no'] = wono
        # Company ID and Financial Year ID from session, similar to ASP.NET Session["compid"]
        # Assuming session is configured and 'compid', 'finyear' are set.
        context['comp_id'] = self.request.session.get('compid', 1)  # Default to 1 if not found
        context['fin_year_id'] = self.request.session.get('finyear', 2023) # Default to 2023 if not found
        return context

class ProjectSummaryChartPartialView(View):
    """
    HTMX endpoint to load the graphical view (chart data).
    This will fetch data for the chart and render a partial HTML.
    """
    def get(self, request, *args, **kwargs):
        wono = request.GET.get('WONo', '')
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', 2023)

        # Filter for top-level BOM items (PId='0') as per C# logic for chart
        bom_items = ProjectBOMItem.objects.filter(
            wo_no=wono, 
            p_id=0, 
            comp_id=comp_id, 
            fin_year_id=fin_year_id # Include fin_year_id if applicable for this query
        ).order_by('-c_id') # Order by CId DESC as in original

        chart_data = []
        for item in bom_items:
            # All complex calculations are handled by model properties
            chart_data.append({
                'item_code': item.item_code, # Use the property from item_details
                'progress': item.total_work_progress_percent, # Model method
            })

        return HttpResponse(
            self.render_to_string(
                'project_summary/projectbomitem/_chart_data.html',
                {'chart_data': chart_data}
            ),
            headers={'HX-Trigger': 'chartDataLoaded'} # Custom HTMX trigger for Alpine.js
        )

    # Helper method to render template string (similar to render_to_response but for string)
    def render_to_string(self, template_name, context):
        from django.template.loader import render_to_string
        return render_to_string(template_name, context, self.request)


class ProjectSummaryDetailTablePartialView(ListView):
    """
    HTMX endpoint to load the detailed view (DataTables grid).
    This replaces the GridView population logic.
    """
    model = ProjectBOMItem
    template_name = 'project_summary/projectbomitem/_detail_table.html'
    context_object_name = 'project_bom_items'
    paginate_by = 20 # Original PageSize="20"

    def get_queryset(self):
        wono = self.request.GET.get('WONo', '')
        comp_id = self.request.session.get('compid', 1)
        # Filter for top-level BOM items (PId='0') as per C# logic for grid
        queryset = ProjectBOMItem.objects.filter(
            wo_no=wono, 
            p_id=0, 
            comp_id=comp_id
        ).order_by('p_id') # Order By PId ASC as in original GetDataTable

        # Enhance queryset with annotations for derived fields to optimize query performance
        # instead of calling properties in a loop in template for large datasets.
        # For simplicity, we keep properties, but for performance, consider this:
        # Example annotation:
        # .annotate(
        #     item_code=Subquery(
        #         ItemMaster.objects.filter(id=OuterRef('item_id'), comp_id=OuterRef('comp_id'))
        #         .values('item_code')[:1]
        #     ),
        #     # ... other annotations
        # )
        
        # However, since the model properties already encapsulate the lookup logic
        # and are called per item in the template, we rely on that for simplicity
        # of the view code, adhering to the "thin view" principle.
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # This view is explicitly for HTMX requests, so it returns the partial.
        # DataTables JS initialization will happen in the template.
        return super().render_to_response(context, **response_kwargs)

class ComponentDetailView(View):
    """
    View for displaying details of a specific component,
    triggered by clicking "Item Code" in the grid.
    This replaces the Response.Redirect to Componant_Details.aspx.
    It can either render a full page or a HTMX modal.
    For this example, we'll return a full page for simplicity, as it's a redirect.
    """
    def get(self, request, item_id, p_id, c_id, wo_no, *args, **kwargs):
        # Retrieve the specific BOM item or related component details
        component = get_object_or_404(ProjectBOMItem, 
                                      item_id=item_id, 
                                      p_id=p_id, 
                                      c_id=c_id, 
                                      wo_no=wo_no,
                                      comp_id=request.session.get('compid', 1))
        
        # In a real scenario, you would fetch more detailed component data
        # based on item_id and the hierarchy (PId, CId).
        # For now, pass the retrieved component data.
        context = {
            'component': component,
            'item_master_details': component.item_details # Access ItemMaster via property
        }
        return self.render_to_string('project_summary/projectbomitem/component_detail.html', context)
    
    # Helper method to render template string (similar to render_to_response but for string)
    def render_to_string(self, template_name, context):
        from django.template.loader import render_to_string
        return render_to_string(template_name, context, self.request)


# --- Generic CRUD Views (as per template, not directly from original ASPX functionality) ---

class ProjectBOMItemListView(ListView):
    model = ProjectBOMItem
    template_name = 'project_summary/projectbomitem/list.html'
    context_object_name = 'projectbomitems'

    def get_queryset(self):
        # This list view will show all BOM items for general management
        # (different from the filtered summary view)
        return ProjectBOMItem.objects.all()

class ProjectBOMItemCreateView(CreateView):
    model = ProjectBOMItem
    form_class = ProjectBOMItemForm
    template_name = 'project_summary/projectbomitem/form.html'
    success_url = reverse_lazy('project_summary_list') # Redirect to generic list

    def form_valid(self, form):
        # Set session-dependent fields if necessary (CompId, FinYearId)
        form.instance.comp_id = self.request.session.get('compid', 1)
        form.instance.fin_year_id = self.request.session.get('finyear', 2023)
        response = super().form_valid(form)
        messages.success(self.request, 'Project BOM Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectBOMItemList'
                }
            )
        return response

class ProjectBOMItemUpdateView(UpdateView):
    model = ProjectBOMItem
    form_class = ProjectBOMItemForm
    template_name = 'project_summary/projectbomitem/form.html'
    success_url = reverse_lazy('project_summary_list') # Redirect to generic list

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Project BOM Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectBOMItemList'
                }
            )
        return response

class ProjectBOMItemDeleteView(DeleteView):
    model = ProjectBOMItem
    template_name = 'project_summary/projectbomitem/confirm_delete.html'
    success_url = reverse_lazy('project_summary_list') # Redirect to generic list

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Project BOM Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectBOMItemList'
                }
            )
        return response

```

### 4.4 Templates (`project_summary/templates/project_summary/projectbomitem/`)

Task: Create templates for each view, incorporating HTMX, Alpine.js, and DataTables.

## Instructions:

The templates will be structured to support the tabbed interface using HTMX for content loading and Alpine.js for UI state. DataTables will be used for the grid.

**`summary.html` (Main Page - replaces ProjectSummary_Details.aspx)**

This is the main page that hosts the tabs and their content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: 'graphical' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Project Summary for WO No: <span class="text-blue-600">{{ wo_no }}</span></h2>
        <a href="{% url 'project_summary_main_list' %}" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    <div class="border-b border-gray-200">
        <nav class="-mb-px flex space-x-8" aria-label="Tabs">
            <button
                @click="activeTab = 'graphical'"
                :class="{'border-indigo-500 text-indigo-600': activeTab === 'graphical', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'graphical'}"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                hx-get="{% url 'project_summary_chart_partial' %}?WONo={{ wo_no }}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click, load once delay:10ms"
            >
                Graphical View
            </button>
            <button
                @click="activeTab = 'detail'"
                :class="{'border-indigo-500 text-indigo-600': activeTab === 'detail', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'detail'}"
                class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                hx-get="{% url 'project_summary_detail_table_partial' %}?WONo={{ wo_no }}"
                hx-target="#tab-content"
                hx-swap="innerHTML"
                hx-trigger="click"
            >
                Detail View
            </button>
        </nav>
    </div>

    <div id="tab-content" class="mt-8">
        <!-- Content for active tab will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading data...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // No specific Alpine.js component needed beyond x-data for activeTab
    });
</script>
{% endblock %}
```

**`_chart_data.html` (Partial for Graphical View Tab)**

This partial template will display the chart data in a simple HTML/CSS bar graph representation.

```html
<div class="p-4 bg-white rounded-lg shadow-md">
    <h3 class="text-xl font-semibold mb-4">Project Progress Summary</h3>
    {% if chart_data %}
    <div class="flex flex-col space-y-2" style="width: 100%; max-width: 1300px; height: 700px; overflow-x: auto; padding-bottom: 10px;">
        <div class="flex items-end justify-between bg-white border border-gray-200 p-2 rounded-md">
            <!-- X-axis labels (Assembly No) -->
            <div class="flex flex-col items-center justify-end h-full">
                <span class="text-xs font-bold text-gray-700 mb-2">Assembly No</span>
            </div>
            <!-- Y-axis (Work Progress in %) labels - simplified vertical axis -->
            <div class="flex flex-col justify-between h-full text-right text-xs text-gray-500">
                <span>100%</span>
                <span>75%</span>
                <span>50%</span>
                <span>25%</span>
                <span>0%</span>
            </div>
        </div>
        <div class="flex flex-row items-end h-full" style="height: calc(100% - 40px); overflow-x: auto;">
            {% for item in chart_data %}
            <div class="flex flex-col items-center mx-2" style="width: 50px; flex-shrink: 0;">
                <div 
                    class="bg-blue-500 hover:bg-blue-600 rounded-t-md relative group" 
                    style="height: calc({{ item.progress }}%); width: 100%; min-height: 5px;">
                    <span class="absolute -top-6 text-sm font-semibold text-gray-800 w-full text-center">{{ item.progress|floatformat:1 }}%</span>
                </div>
                <div class="mt-2 text-xs text-gray-600 font-medium whitespace-nowrap overflow-hidden text-ellipsis w-full text-center">{{ item.item_code }}</div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% else %}
    <p class="text-center text-gray-600 py-10">No graphical data to display for this project.</p>
    {% endif %}
</div>
```

**`_detail_table.html` (Partial for Detail View Tab)**

This partial template will render the DataTables grid.

```html
<div class="p-4 bg-white rounded-lg shadow-md">
    <h3 class="text-xl font-semibold mb-4">Project Item Details</h3>
    {% if project_bom_items %}
    <div class="overflow-x-auto">
        <table id="projectBOMItemTable" class="min-w-full bg-white border-collapse yui-datatable-theme">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <!-- Hidden columns will be rendered but might be hidden by DataTables config or CSS -->
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">ItemId</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">WONo</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">PId</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">CId</th>
                    
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Unit Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">BOM Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Weld</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Stock Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Tot.WIS Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden">Dry Run Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Bal.BOM Qty</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider hidden">After Stock Qty</th>             
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Progress in(%)</th>  
                </tr>
            </thead>
            <tbody>
                {% for obj in project_bom_items %}
                <tr>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 hidden">{{ obj.item_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 hidden">{{ obj.wo_no }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 hidden">{{ obj.p_id }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 hidden">{{ obj.c_id }}</td>
                    
                    <td class="py-2 px-4 border-b border-gray-200 text-center">
                        <a href="{% url 'component_detail' item_id=obj.item_id p_id=obj.p_id c_id=obj.c_id wo_no=obj.wo_no %}" 
                           class="text-blue-600 hover:text-blue-800 hover:underline">
                           {{ obj.item_code }}
                        </a>
                    </td>
                    <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.description }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.uom_symbol }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right hidden">{{ obj.unit_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.bom_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 hidden">{{ obj.weldments }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right hidden">{{ obj.stock_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right hidden">{{ obj.total_wis_issued_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right hidden">{{ obj.dry_run_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.balance_bom_qty|floatformat:3 }}</td>
                    <td class="py-2 px-4 border-b border-gray-200 text-right hidden">{{ obj.after_stock_qty|floatformat:3 }}</td>             
                    <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.total_work_progress_percent|floatformat:2 }}%</td>  
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <p class="text-center text-gray-600 py-10">No data to display.</p>
    {% endif %}
</div>

<script>
    // DataTables initialization, ensuring it runs after HTMX loads the content
    $(document).ready(function() {
        $('#projectBOMItemTable').DataTable({
            "pageLength": 20, // As per original PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            // DataTables config to hide columns by default
            "columnDefs": [
                { "visible": false, "targets": [1, 2, 3, 4, 7, 9, 10, 11, 13] } // Column indices for hidden fields
            ]
        });
    });
</script>
```

**`component_detail.html` (Page for Component Details - replaces Componant_Details.aspx)**

This template provides a simple detail view for a component.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Component Details: <span class="text-blue-600">{{ component.item_code }}</span></h2>
    
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
        <div class="px-4 py-5 sm:px-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
                Item Information
            </h3>
            <p class="mt-1 max-w-2xl text-sm text-gray-500">
                Detailed information about the selected BOM component.
            </p>
        </div>
        <div class="border-t border-gray-200">
            <dl>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Item Code</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.item_code }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Description</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.description }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Work Order No</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.wo_no }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Parent ID</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.p_id }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Component ID</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.c_id }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Unit Quantity</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.unit_qty|floatformat:3 }}</dd>
                </div>
                <div class="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">BOM Quantity</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.bom_qty|floatformat:3 }}</dd>
                </div>
                <div class="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                    <dt class="text-sm font-medium text-gray-500">Progress (%)</dt>
                    <dd class="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">{{ component.total_work_progress_percent|floatformat:2 }}%</dd>
                </div>
                <!-- Add more details as needed -->
            </dl>
        </div>
    </div>

    <div class="mt-6 flex justify-end">
        <a href="javascript:history.back()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
            Back to Project Summary
        </a>
    </div>
</div>
{% endblock %}
```

**Generic CRUD Templates (as per template structure, not directly derived from ProjectSummary_Details.aspx):**

**`list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Project BOM Items</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'project_summary_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Project BOM Item
        </button>
    </div>
    
    <div id="projectbomitemTable-container"
         hx-trigger="load, refreshProjectBOMItemList from:body"
         hx-get="{% url 'project_summary_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**`_projectbomitem_table.html` (Partial for Generic List View DataTables)**

```html
<table id="projectbomitemTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Work Order No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Component ID</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit Qty</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in projectbomitems %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.wo_no }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.p_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.c_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.unit_qty|floatformat:3 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'project_summary_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'project_summary_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#projectbomitemTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**`form.html` (Partial for Create/Update Modals)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Project BOM Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`confirm_delete.html` (Partial for Delete Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700">Are you sure you want to delete this Project BOM Item (ID: {{ projectbomitem.pk }})?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-delete="{% url 'project_summary_delete' projectbomitem.pk %}" 
            hx-swap="none"
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

### 4.5 URLs (`project_summary/urls.py`)

Task: Define URL patterns for all views.

```python
from django.urls import path
from .views import (
    ProjectSummaryView,
    ProjectSummaryChartPartialView,
    ProjectSummaryDetailTablePartialView,
    ComponentDetailView,
    ProjectBOMItemListView, # Generic list view
    ProjectBOMItemCreateView,
    ProjectBOMItemUpdateView,
    ProjectBOMItemDeleteView
)

urlpatterns = [
    # Main Project Summary Page (replaces ProjectSummary_Details.aspx)
    path('summary/', ProjectSummaryView.as_view(), name='project_summary_details'),
    
    # HTMX Endpoints for tab content
    path('summary/chart-data/', ProjectSummaryChartPartialView.as_view(), name='project_summary_chart_partial'),
    path('summary/detail-table/', ProjectSummaryDetailTablePartialView.as_view(), name='project_summary_detail_table_partial'),

    # Component Detail Page (replaces Componant_Details.aspx)
    path('component-detail/<int:item_id>/<int:p_id>/<int:c_id>/<str:wo_no>/', 
         ComponentDetailView.as_view(), 
         name='component_detail'),

    # --- Generic CRUD URLs for ProjectBOMItem (as per template) ---
    path('projectbomitems/', ProjectBOMItemListView.as_view(), name='project_summary_list'),
    path('projectbomitems/add/', ProjectBOMItemCreateView.as_view(), name='project_summary_add'),
    path('projectbomitems/edit/<int:pk>/', ProjectBOMItemUpdateView.as_view(), name='project_summary_edit'),
    path('projectbomitems/delete/<int:pk>/', ProjectBOMItemDeleteView.as_view(), name='project_summary_delete'),
    path('projectbomitems/table/', ProjectBOMItemListView.as_view(), name='project_summary_table'), # HTMX endpoint for generic list
]
```

### 4.6 Tests (`project_summary/tests.py`)

Task: Write comprehensive unit tests for models and integration tests for views.

## Instructions:

Tests will cover model properties and methods (especially the complex calculations) and view responses, including HTMX interactions.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.test.client import RequestFactory
from decimal import Decimal

# Import all models to ensure they are testable
from .models import (
    ProjectBOMItem, ItemMaster, UnitMaster, WISDetail,
    MaterialIssueDetail, WISMaster, MaterialIssueMaster, MaterialRequisitionMaster,
    MaterialRequisitionDetail
)

class ProjectBOMItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all related models
        cls.comp_id = 101
        cls.fin_year_id = 2024
        cls.wo_no = "WO-001"

        # Unit Master
        UnitMaster.objects.create(id=1, symbol="PCS")
        UnitMaster.objects.create(id=2, symbol="KG")

        # Item Master
        ItemMaster.objects.create(id=1001, item_code="ITEM-A", description="Product A", uom_basic_id=1, stock_qty=Decimal('50.000'), comp_id=cls.comp_id)
        ItemMaster.objects.create(id=1002, item_code="ITEM-B", description="Component B", uom_basic_id=1, stock_qty=Decimal('10.000'), comp_id=cls.comp_id)
        ItemMaster.objects.create(id=1003, item_code="ITEM-C", description="Raw Material C", uom_basic_id=2, stock_qty=Decimal('100.000'), comp_id=cls.comp_id)

        # Project BOM Item (Top-level BOM for testing summary)
        cls.bom_item_a = ProjectBOMItem.objects.create(
            id=1, item_id=1001, wo_no=cls.wo_no, p_id=0, c_id=1, unit_qty=Decimal('1.000'),
            weldments="N/A", comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        # Nested BOM Item (Component B for Product A)
        cls.bom_item_b = ProjectBOMItem.objects.create(
            id=2, item_id=1002, wo_no=cls.wo_no, p_id=1, c_id=2, unit_qty=Decimal('5.000'), # 5 of ITEM-B needed for 1 ITEM-A
            weldments="Yes", comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        # Nested BOM Item (Raw Material C for Component B)
        cls.bom_item_c = ProjectBOMItem.objects.create(
            id=3, item_id=1003, wo_no=cls.wo_no, p_id=2, c_id=3, unit_qty=Decimal('2.000'), # 2 of ITEM-C needed for 1 ITEM-B
            weldments="No", comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )

        # WIS Data
        WISMaster.objects.create(wis_no="WIS-001", won_no=cls.wo_no, comp_id=cls.comp_id)
        WISDetail.objects.create(wis_no="WIS-001", item_id=1001, p_id=0, c_id=1, issued_qty=Decimal('0.500')) # Half of Product A issued
        WISDetail.objects.create(wis_no="WIS-001", item_id=1002, p_id=1, c_id=2, issued_qty=Decimal('2.000')) # 2 of Component B issued

        # Material Requisition and Issue Data (Complex, simplified for test)
        MaterialRequisitionMaster.objects.create(id=1, comp_id=cls.comp_id)
        MaterialRequisitionDetail.objects.create(id=1, m_id=1, item_id=1001, wo_no=cls.wo_no)
        MaterialIssueMaster.objects.create(id=1, mrs_id=1, comp_id=cls.comp_id)
        MaterialIssueDetail.objects.create(m_id=1, mrs_id=1, item_id=1001, issue_qty=Decimal('0.200')) # 0.2 of Product A issued via MIN

    def test_project_bom_item_creation(self):
        item = ProjectBOMItem.objects.get(id=1)
        self.assertEqual(item.wo_no, self.wo_no)
        self.assertEqual(item.item_id, 1001)
        self.assertEqual(item.p_id, 0)
        self.assertEqual(item.c_id, 1)
        self.assertEqual(item.unit_qty, Decimal('1.000'))

    def test_item_details_property(self):
        item_master = self.bom_item_a.item_details
        self.assertIsNotNone(item_master)
        self.assertEqual(item_master.item_code, "ITEM-A")

    def test_uom_symbol_property(self):
        self.assertEqual(self.bom_item_a.uom_symbol, "PCS")
        self.assertEqual(self.bom_item_c.uom_symbol, "KG")

    def test_stock_qty_property(self):
        self.assertEqual(self.bom_item_a.stock_qty, Decimal('50.000'))

    def test_bom_qty_property(self):
        # Simplified get_bom_tree_qty just returns unit_qty.
        # So for bom_item_a (qty 1), bom_qty should be 1.
        self.assertEqual(self.bom_item_a.bom_qty, Decimal('1.000'))
        self.assertEqual(self.bom_item_b.bom_qty, Decimal('5.000'))
        # If get_bom_tree_qty were truly recursive, this would be 1 * 5 = 5 for B (relative to A)
        # And 1 * 5 * 2 = 10 for C (relative to A) if Qty is multiplicative up the tree.
        # But per current get_bom_tree_qty, it's just its own Qty.
        self.assertEqual(self.bom_item_c.bom_qty, Decimal('2.000'))

    def test_total_wis_issued_qty_property(self):
        # For bom_item_a (ITEM-A), IssuedQty = 0.500
        self.assertEqual(self.bom_item_a.total_wis_issued_qty, Decimal('0.500'))
        # For bom_item_b (ITEM-B), IssuedQty = 2.000
        self.assertEqual(self.bom_item_b.total_wis_issued_qty, Decimal('2.000'))
        # For bom_item_c (ITEM-C), No WIS issued
        self.assertEqual(self.bom_item_c.total_wis_issued_qty, Decimal('0.000'))

    def test_min_qty_property(self):
        # For bom_item_a (ITEM-A), MINQty = 0.200
        # This test relies on the simplified MINQty implementation
        self.assertEqual(self.bom_item_a.min_qty, Decimal('0.200'))
        self.assertEqual(self.bom_item_b.min_qty, Decimal('0.000')) # No MIN for B
        self.assertEqual(self.bom_item_c.min_qty, Decimal('0.000')) # No MIN for C

    def test_total_work_progress_percent_property(self):
        # For bom_item_a:
        # bom_qty = 1.000
        # total_issued = WIS (0.500) + MIN (0.200) = 0.700
        # Progress = (0.700 * 100) / 1.000 = 70.000
        self.assertEqual(self.bom_item_a.total_work_progress_percent, Decimal('70.000'))

        # For bom_item_b:
        # bom_qty = 5.000
        # total_issued = WIS (2.000) + MIN (0.000) = 2.000
        # Progress = (2.000 * 100) / 5.000 = 40.000
        self.assertEqual(self.bom_item_b.total_work_progress_percent, Decimal('40.000'))

        # For bom_item_c: bom_qty = 2.000, total_issued = 0 -> Progress = 0
        self.assertEqual(self.bom_item_c.total_work_progress_percent, Decimal('0.000'))

    def test_dry_run_qty_property(self):
        # bom_item_a:
        # stock_qty = 50.000
        # remaining_bom_qty_pre_dryrun = BOM(1.000) - WIS(0.500) - MIN(0.200) = 0.300
        # DryRunQty = min(stock_qty, remaining_bom_qty_pre_dryrun) = min(50.000, 0.300) = 0.300
        self.assertEqual(self.bom_item_a.dry_run_qty, Decimal('0.300'))

        # bom_item_b:
        # stock_qty = 10.000
        # remaining_bom_qty_pre_dryrun = BOM(5.000) - WIS(2.000) - MIN(0.000) = 3.000
        # DryRunQty = min(10.000, 3.000) = 3.000
        self.assertEqual(self.bom_item_b.dry_run_qty, Decimal('3.000'))
        
        # bom_item_c:
        # stock_qty = 100.000
        # remaining_bom_qty_pre_dryrun = BOM(2.000) - WIS(0.000) - MIN(0.000) = 2.000
        # DryRunQty = min(100.000, 2.000) = 2.000
        self.assertEqual(self.bom_item_c.dry_run_qty, Decimal('2.000'))


    def test_balance_bom_qty_property(self):
        # For bom_item_a:
        # bom_qty = 1.000
        # total_issued_and_dry_run = WIS(0.500) + DryRun(0.300) + MIN(0.200) = 1.000
        # Balance = 1.000 - 1.000 = 0.000
        self.assertEqual(self.bom_item_a.balance_bom_qty, Decimal('0.000'))

        # For bom_item_b:
        # bom_qty = 5.000
        # total_issued_and_dry_run = WIS(2.000) + DryRun(3.000) + MIN(0.000) = 5.000
        # Balance = 5.000 - 5.000 = 0.000
        self.assertEqual(self.bom_item_b.balance_bom_qty, Decimal('0.000'))
        
        # For bom_item_c:
        # bom_qty = 2.000
        # total_issued_and_dry_run = WIS(0.000) + DryRun(2.000) + MIN(0.000) = 2.000
        # Balance = 2.000 - 2.000 = 0.000
        self.assertEqual(self.bom_item_c.balance_bom_qty, Decimal('0.000'))


    def test_after_stock_qty_property(self):
        # For bom_item_a:
        # stock_qty = 50.000, dry_run = 0.300
        # AfterStock = 50.000 - 0.300 = 49.700
        self.assertEqual(self.bom_item_a.after_stock_qty, Decimal('49.700'))
        
        # For bom_item_b:
        # stock_qty = 10.000, dry_run = 3.000
        # AfterStock = 10.000 - 3.000 = 7.000
        self.assertEqual(self.bom_item_b.after_stock_qty, Decimal('7.000'))

        # For bom_item_c:
        # stock_qty = 100.000, dry_run = 2.000
        # AfterStock = 100.000 - 2.000 = 98.000
        self.assertEqual(self.bom_item_c.after_stock_qty, Decimal('98.000'))


class ProjectSummaryViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data (same as model tests for consistency)
        cls.comp_id = 101
        cls.fin_year_id = 2024
        cls.wo_no = "WO-001"

        UnitMaster.objects.create(id=1, symbol="PCS")
        ItemMaster.objects.create(id=1001, item_code="ITEM-A", description="Product A", uom_basic_id=1, stock_qty=Decimal('50.000'), comp_id=cls.comp_id)
        ProjectBOMItem.objects.create(
            id=1, item_id=1001, wo_no=cls.wo_no, p_id=0, c_id=1, unit_qty=Decimal('1.000'),
            weldments="N/A", comp_id=cls.comp_id, fin_year_id=cls.fin_year_id
        )
        WISMaster.objects.create(wis_no="WIS-001", won_no=cls.wo_no, comp_id=cls.comp_id)
        WISDetail.objects.create(wis_no="WIS-001", item_id=1001, p_id=0, c_id=1, issued_qty=Decimal('0.500'))
        MaterialRequisitionMaster.objects.create(id=1, comp_id=cls.comp_id)
        MaterialRequisitionDetail.objects.create(id=1, m_id=1, item_id=1001, wo_no=cls.wo_no)
        MaterialIssueMaster.objects.create(id=1, mrs_id=1, comp_id=cls.comp_id)
        MaterialIssueDetail.objects.create(m_id=1, mrs_id=1, item_id=1001, issue_qty=Decimal('0.200'))

    def setUp(self):
        self.client = Client()
        # Set up session data for each request
        self.factory = RequestFactory()
        self.request = self.factory.get('/')
        middleware = SessionMiddleware(lambda request: None)
        middleware.process_request(self.request)
        self.request.session.save()
        self.request.session['compid'] = self.comp_id
        self.request.session['finyear'] = self.fin_year_id

    def test_project_summary_view(self):
        url = reverse('project_summary_details') + f'?WONo={self.wo_no}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/summary.html')
        self.assertContains(response, self.wo_no)
        self.assertContains(response, 'Graphical View')
        self.assertContains(response, 'Detail View')

    def test_chart_partial_view_htmx(self):
        url = reverse('project_summary_chart_partial') + f'?WONo={self.wo_no}'
        # Simulate HTMX request by adding HTTP_HX_REQUEST header
        response = self.client.get(url, HTTP_HX_REQUEST='true', **{'HTTP_X_REQUESTED_WITH': 'XMLHttpRequest'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/_chart_data.html')
        self.assertContains(response, 'Project Progress Summary')
        self.assertContains(response, 'ITEM-A')
        self.assertContains(response, '70.0%') # Based on model calculation

    def test_detail_table_partial_view_htmx(self):
        url = reverse('project_summary_detail_table_partial') + f'?WONo={self.wo_no}'
        # Simulate HTMX request
        response = self.client.get(url, HTTP_HX_REQUEST='true', **{'HTTP_X_REQUESTED_WITH': 'XMLHttpRequest'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/_detail_table.html')
        self.assertContains(response, 'Project Item Details')
        self.assertContains(response, 'ITEM-A')
        self.assertContains(response, 'Product A')
        self.assertContains(response, '70.00%')

    def test_component_detail_view(self):
        # Assuming ProjectBOMItem with id=1 is the one we want to detail
        item = ProjectBOMItem.objects.get(id=1)
        url = reverse('component_detail', args=[item.item_id, item.p_id, item.c_id, item.wo_no])
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/component_detail.html')
        self.assertContains(response, 'Component Details: ITEM-A')
        self.assertContains(response, 'Product A')

    # --- Generic CRUD View Tests ---
    def test_generic_list_view(self):
        response = self.client.get(reverse('project_summary_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/list.html')
        self.assertTrue('projectbomitems' in response.context)
        self.assertContains(response, 'Add New Project BOM Item')

    def test_generic_create_view_get(self):
        response = self.client.get(reverse('project_summary_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/form.html')
        self.assertTrue('form' in response.context)

    def test_generic_create_view_post_htmx(self):
        data = {
            'item_id': 1002, # Use existing item_id
            'wo_no': 'WO-002',
            'p_id': 0,
            'c_id': 1,
            'unit_qty': Decimal('1.000'),
            'weldments': 'N/A',
        }
        response = self.client.post(reverse('project_summary_add'), data, HTTP_HX_REQUEST='true')
        # Expect 204 No Content for HTMX form submission with HX-Trigger
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectBOMItemList')
        self.assertTrue(ProjectBOMItem.objects.filter(wo_no='WO-002', item_id=1002).exists())

    def test_generic_update_view_get(self):
        obj = ProjectBOMItem.objects.get(id=1)
        response = self.client.get(reverse('project_summary_edit', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.wo_no, self.wo_no)

    def test_generic_update_view_post_htmx(self):
        obj = ProjectBOMItem.objects.get(id=1)
        data = {
            'item_id': obj.item_id,
            'wo_no': obj.wo_no,
            'p_id': obj.p_id,
            'c_id': obj.c_id,
            'unit_qty': Decimal('1.500'), # Update quantity
            'weldments': obj.weldments,
        }
        response = self.client.post(reverse('project_summary_edit', args=[obj.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectBOMItemList')
        obj.refresh_from_db()
        self.assertEqual(obj.unit_qty, Decimal('1.500'))

    def test_generic_delete_view_get(self):
        obj = ProjectBOMItem.objects.get(id=1)
        response = self.client.get(reverse('project_summary_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_summary/projectbomitem/confirm_delete.html')
        self.assertTrue('projectbomitem' in response.context)

    def test_generic_delete_view_delete_htmx(self):
        obj = ProjectBOMItem.objects.get(id=1)
        response = self.client.delete(reverse('project_summary_delete', args=[obj.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertTrue('HX-Trigger' in response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectBOMItemList')
        self.assertFalse(ProjectBOMItem.objects.filter(id=obj.id).exists())

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for dynamic updates:**
    -   Tab switching (`hx-get`, `hx-target`, `hx-swap`, `hx-trigger="click"`) to load `_chart_data.html` and `_detail_table.html`partials into the `#tab-content` div.
    -   Initial load of the default tab content (`hx-trigger="load once delay:10ms"` on the "Graphical View" tab button).
    -   CRUD operations for generic `ProjectBOMItem` (`hx-get` for modals, `hx-post`/`hx-delete` for form/delete submissions) refreshing the main list table via `HX-Trigger` and `refreshProjectBOMItemList`.
-   **Alpine.js for UI state:**
    -   `x-data="{ activeTab: 'graphical' }"` on the main container to manage the active tab state.
    -   `@click="activeTab = 'graphical'"` to update the `activeTab` variable when tab buttons are clicked.
    -   `:class` bindings are used to apply active/inactive styles to tab buttons based on `activeTab`.
    -   Alpine.js (combined with HTMX) also manages modal visibility (`on click add .is-active to #modal`).
-   **DataTables for list views:**
    -   The `_detail_table.html` and `_projectbomitem_table.html` partials contain a `<table id="...">` which is then initialized by `$(document).ready(function() { $('#...').DataTable({...}); });` after HTMX loads it. This ensures client-side searching, sorting, and pagination.
-   **No full page reloads:** All tab switches and modal interactions leverage HTMX to fetch and swap only necessary HTML fragments, ensuring a smooth, single-page application feel without complex JavaScript frameworks.
-   **Strict separation of concerns:** All business logic remains in Django models. Views are concise, primarily handling HTTP requests and responses, and rendering templates. No HTML is embedded in views.
-   **DRY templates:** `base.html` is extended for common layout. Partial templates (`_chart_data.html`, `_detail_table.html`, `_projectbomitem_table.html`, `form.html`, `confirm_delete.html`) are used to render specific UI components dynamically via HTMX, avoiding duplication.

## Final Notes

This modernization plan provides a clear, actionable path to transition the ASP.NET `ProjectSummary_Details.aspx` page to a modern Django application.

-   **Business Value:** This transition moves the application to an open-source, highly scalable, and maintainable framework, reducing vendor lock-in and dependency on legacy technologies. The use of HTMX and Alpine.js provides a modern, interactive user experience without the complexity of traditional JavaScript frameworks, simplifying development and maintenance. The "fat model" approach ensures business logic is centralized and testable, improving application robustness and clarity. The emphasis on automation and clear steps enables an efficient migration process with reduced manual effort and potential errors.
-   **Placeholders:** The recursive BOM quantity calculations (`get_bom_tree_qty`, `bom_qty`) and the `min_qty` property in `ProjectBOMItem` are simplified placeholders. For a production environment, these complex database-level operations might require database views, stored procedures, or advanced Django ORM techniques (like recursive CTEs if supported by the database and ORM version, or raw SQL queries) for optimal performance and correctness given the recursive nature of BOMs.
-   **Session Management:** The plan assumes Django session middleware is configured and that `compid` and `finyear` are set in the session, mirroring the ASP.NET `Session` usage.
-   **Error Handling:** While `try-catch` was present in C#, Django handles exceptions gracefully through its default error pages and logging. More specific error handling (e.g., for `DoesNotExist` exceptions) is managed within models or views as `get_object_or_404`.