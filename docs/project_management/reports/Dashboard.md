## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

The provided ASP.NET code for `Dashboard.aspx` and its code-behind `Dashboard.aspx.cs` is extremely minimal, essentially serving as a placeholder with no discernible database interactions, business logic, or UI elements. The `Page_Load` method is empty, and the `.aspx` file only defines content placeholders and includes one JavaScript file (`loadingNotifier.js`).

Given the lack of concrete information, we will proceed by *inferring* a typical "Project Management Dashboard" functionality based on the file path `Module_ProjectManagement_Reports_Dashboard`. We will assume the dashboard would display a list of "Projects" and allow basic management.

For this modernization plan, we will create a Django application named `project_management` and a central model called `Project` to represent the core entity that might be displayed on such a dashboard.

---

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not contain any database interaction (like `SqlDataSource` or direct SQL commands), we are inferring the schema based on the application's context (`Module_ProjectManagement_Reports_Dashboard`). We will assume there's a primary table for projects.

-   **[TABLE_NAME]:** `tblProjects` (inferred)
-   **Columns (inferred):**
    -   `ProjectID` (Primary Key, integer)
    -   `ProjectName` (string)
    -   `ProjectDescription` (string)
    -   `StartDate` (date)
    -   `EndDate` (date)
    -   `Status` (string, e.g., 'Active', 'Completed', 'On Hold')
    -   `Budget` (decimal/numeric)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The provided ASP.NET code does not explicitly define any CRUD (Create, Read, Update, Delete) operations. As a dashboard, the primary function would be **Read** (displaying project data). However, for a comprehensive management module, we will assume standard CRUD operations are intended for the `Project` entity:

-   **Create:** Ability to add new projects.
-   **Read:** Displaying a list of projects on the dashboard, likely with details.
-   **Update:** Ability to modify existing project details.
-   **Delete:** Ability to remove projects.

No specific validation logic was found, but common validations (e.g., required fields, date formats) will be replicated in Django forms.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET code contains no UI controls. We infer the typical UI components for a project management dashboard in a modern Django application:

-   **GridView equivalent:** A DataTables-enabled HTML `<table>` for displaying a list of `Project` records, allowing for client-side sorting, searching, and pagination.
-   **Input controls:** Text fields (`TextBox`), potentially date pickers, dropdowns for status (`DropDownList`) for project creation/editing.
-   **Action buttons:** Buttons or links to trigger Add, Edit, and Delete operations for projects.
-   **Modals:** HTMX-driven modals for Add/Edit/Delete forms to provide a seamless user experience without full page reloads.
-   **Loading Indicator:** The `loadingNotifier.js` suggests a need for visual feedback during asynchronous operations, which HTMX handles elegantly by default and can be enhanced with Alpine.js if complex UI state is needed.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

## Instructions:

We create a `Project` model in `project_management/models.py` based on the inferred `tblProjects` schema. The model will include basic fields and a `__str__` method for representation. While no specific business logic was found in the ASP.NET snippet, we will include a placeholder `is_active` method to demonstrate the "fat model" principle.

```python
# project_management/models.py
from django.db import models
from django.utils import timezone

class Project(models.Model):
    project_id = models.AutoField(db_column='ProjectID', primary_key=True)
    project_name = models.CharField(db_column='ProjectName', max_length=255, verbose_name='Project Name')
    project_description = models.TextField(db_column='ProjectDescription', blank=True, null=True, verbose_name='Description')
    start_date = models.DateField(db_column='StartDate', verbose_name='Start Date')
    end_date = models.DateField(db_column='EndDate', blank=True, null=True, verbose_name='End Date')
    status = models.CharField(db_column='Status', max_length=50, default='Active', verbose_name='Status')
    budget = models.DecimalField(db_column='Budget', max_digits=12, decimal_places=2, blank=True, null=True, verbose_name='Budget')

    class Meta:
        managed = False  # Set to True if Django should manage the table, False if it pre-exists
        db_table = 'tblProjects'
        verbose_name = 'Project'
        verbose_name_plural = 'Projects'
        ordering = ['project_name']

    def __str__(self):
        return self.project_name

    # Business logic methods go here (Fat Model Principle)
    def is_active(self):
        """
        Checks if the project is currently active based on its status and dates.
        """
        if self.status == 'Active' and (self.end_date is None or self.end_date >= timezone.now().date()):
            return True
        return False

    def calculate_remaining_budget(self):
        """
        Placeholder for calculating remaining budget.
        Assumes other related models (e.g., Expenses) would exist.
        """
        if self.budget is not None:
            # For demonstration, let's assume some expenses are recorded elsewhere
            # This would typically involve querying related Expense models
            total_expenses = 0 # Example: self.expenses.aggregate(models.Sum('amount'))['amount__sum'] or self.get_total_expenses()
            return self.budget - total_expenses
        return None

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A `ModelForm` will be created for the `Project` model in `project_management/forms.py`, allowing easy creation and updating of `Project` instances. Widgets are defined to apply Tailwind CSS classes for consistent styling.

```python
# project_management/forms.py
from django import forms
from .models import Project

class ProjectForm(forms.ModelForm):
    class Meta:
        model = Project
        fields = ['project_name', 'project_description', 'start_date', 'end_date', 'status', 'budget']
        widgets = {
            'project_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'project_description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'start_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'end_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}, choices=[('Active', 'Active'), ('Completed', 'Completed'), ('On Hold', 'On Hold'), ('Cancelled', 'Cancelled')]),
            'budget': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
        }
        
    def clean(self):
        cleaned_data = super().clean()
        start_date = cleaned_data.get('start_date')
        end_date = cleaned_data.get('end_date')

        if start_date and end_date and end_date < start_date:
            self.add_error('end_date', "End date cannot be before start date.")
        return cleaned_data
```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

## Instructions:

Views are defined in `project_management/views.py`. We'll implement `ListView`, `CreateView`, `UpdateView`, and `DeleteView` for projects. A crucial addition is `ProjectTablePartialView` which serves the DataTables component via HTMX, allowing for dynamic updates without full page reloads.

```python
# project_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string

from .models import Project
from .forms import ProjectForm

class ProjectListView(ListView):
    model = Project
    template_name = 'project_management/project/list.html'
    context_object_name = 'projects'

class ProjectTablePartialView(ListView):
    model = Project
    template_name = 'project_management/project/_project_table.html' # This template will be loaded by HTMX
    context_object_name = 'projects'

    def get_queryset(self):
        # Example: Add filtering based on request.GET if needed for DataTables server-side processing
        return super().get_queryset()

class ProjectCreateView(CreateView):
    model = Project
    form_class = ProjectForm
    template_name = 'project_management/project/_project_form.html' # Partial for HTMX modal
    success_url = reverse_lazy('project_list') # Fallback, HTMX will handle refresh

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Project added successfully.')
        if self.request.headers.get('HX-Request'):
            # This triggers a custom HTMX event on the client side,
            # which can then refresh the project list table.
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshProjectList' # Custom event name
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If form is invalid, return the rendered form partial itself for HTMX
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response

class ProjectUpdateView(UpdateView):
    model = Project
    form_class = ProjectForm
    template_name = 'project_management/project/_project_form.html' # Partial for HTMX modal
    success_url = reverse_lazy('project_list') # Fallback, HTMX will handle refresh
    pk_url_kwarg = 'project_id' # Match primary key column name

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Project updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(render_to_string(self.template_name, {'form': form}, request=self.request))
        return response

class ProjectDeleteView(DeleteView):
    model = Project
    template_name = 'project_management/project/_project_confirm_delete.html' # Partial for HTMX modal
    success_url = reverse_lazy('project_list') # Fallback, HTMX will handle refresh
    pk_url_kwarg = 'project_id' # Match primary key column name

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Project deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshProjectList'
                }
            )
        return response
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates are structured under `project_management/project/`.
-   `list.html` is the main page extending `core/base.html`.
-   `_project_table.html`, `_project_form.html`, and `_project_confirm_delete.html` are partial templates designed to be loaded dynamically via HTMX into modals.

```html
{# project_management/project/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Project Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'project_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Project
        </button>
    </div>
    
    <div id="projectTable-container"
         hx-trigger="load, refreshProjectList from:body" {# This listens for the custom event #}
         hx-get="{% url 'project_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Projects...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full mx-4 sm:mx-6 md:mx-auto relative">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js can be used here for more complex UI state management
        // For simple modal open/close, htmx and hyperscript are often enough
    });
</script>
{% endblock %}
```

```html
{# project_management/project/_project_table.html #}
<table id="projectTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Project Name</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Description</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Start Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">End Date</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Budget</th>
            <th class="py-3 px-4 border-b border-gray-200 bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in projects %}
        <tr class="hover:bg-gray-50">
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.project_name }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.project_description|truncatechars:50 }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.status }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.start_date|date:"Y-m-d" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.end_date|date:"Y-m-d"|default:"N/A" }}</td>
            <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.budget|floatformat:2|default:"N/A" }}</td>
            <td class="py-3 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-lg text-xs mr-2 transition duration-300 ease-in-out"
                    hx-get="{% url 'project_edit' obj.project_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-lg text-xs transition duration-300 ease-in-out"
                    hx-get="{% url 'project_delete' obj.project_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="8" class="py-4 px-4 text-center text-gray-500">No projects found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#projectTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

```html
{# project_management/project/_project_form.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Project</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Project
            </button>
            <span id="form-spinner" class="htmx-indicator ml-4">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

```html
{# project_management/project/_project_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the project "{{ object.project_name }}"? This action cannot be undone.</p>
    
    <form hx-post="{% url 'project_delete' object.project_id %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete Project
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-4">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns are defined in `project_management/urls.py` to map URLs to the Django views. This includes URLs for the main list page, and separate endpoints for HTMX-loaded partials (table, add form, edit form, delete confirmation).

```python
# project_management/urls.py
from django.urls import path
from .views import (
    ProjectListView, 
    ProjectCreateView, 
    ProjectUpdateView, 
    ProjectDeleteView,
    ProjectTablePartialView,
)

urlpatterns = [
    path('projects/', ProjectListView.as_view(), name='project_list'),
    path('projects/table/', ProjectTablePartialView.as_view(), name='project_table'), # HTMX endpoint for table
    path('projects/add/', ProjectCreateView.as_view(), name='project_add'),
    path('projects/edit/<int:project_id>/', ProjectUpdateView.as_view(), name='project_edit'),
    path('projects/delete/<int:project_id>/', ProjectDeleteView.as_view(), name='project_delete'),
]

```
**Remember to include this `urls.py` in your main project's `urls.py`:**

```python
# your_project_name/urls.py
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('project_management/', include('project_management.urls')),
    # Other app URLs
]
```
And add `project_management` to `INSTALLED_APPS` in `your_project_name/settings.py`.

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests for the `Project` model and integration tests for all `Project` views are created in `project_management/tests.py`. These tests cover CRUD functionality and ensure HTMX interactions behave as expected, contributing to high test coverage.

```python
# project_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from .models import Project
from .forms import ProjectForm

class ProjectModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.project1 = Project.objects.create(
            project_name='Test Project One',
            project_description='Description for test project one.',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=30),
            status='Active',
            budget=1000.00
        )
        cls.project2 = Project.objects.create(
            project_name='Completed Project',
            project_description='This project is completed.',
            start_date=timezone.now().date() - timedelta(days=60),
            end_date=timezone.now().date() - timedelta(days=30),
            status='Completed',
            budget=500.00
        )

    def test_project_creation(self):
        self.assertEqual(self.project1.project_name, 'Test Project One')
        self.assertEqual(self.project1.status, 'Active')
        self.assertAlmostEqual(self.project1.budget, 1000.00)
        self.assertTrue(Project.objects.filter(project_name='Completed Project').exists())

    def test_project_name_label(self):
        field_label = self.project1._meta.get_field('project_name').verbose_name
        self.assertEqual(field_label, 'Project Name')

    def test_str_method(self):
        self.assertEqual(str(self.project1), 'Test Project One')

    def test_is_active_method(self):
        self.assertTrue(self.project1.is_active())
        self.assertFalse(self.project2.is_active()) # Completed project is not active

        # Test project that starts today and ends in future
        future_project = Project.objects.create(
            project_name='Future Active Project',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=10),
            status='Active'
        )
        self.assertTrue(future_project.is_active())

        # Test project that ends in the past
        past_end_date_project = Project.objects.create(
            project_name='Past End Date Project',
            start_date=timezone.now().date() - timedelta(days=30),
            end_date=timezone.now().date() - timedelta(days=1),
            status='Active'
        )
        self.assertFalse(past_end_date_project.is_active())

    def test_calculate_remaining_budget_method(self):
        # Since there's no actual expense model, this tests the placeholder logic
        self.assertEqual(self.project1.calculate_remaining_budget(), 1000.00)
        self.assertIsNone(Project.objects.create(project_name='No Budget', start_date=timezone.now().date()).calculate_remaining_budget())

class ProjectFormTest(TestCase):
    def test_project_form_valid(self):
        form_data = {
            'project_name': 'New Valid Project',
            'project_description': 'A new project for testing.',
            'start_date': timezone.now().date(),
            'end_date': timezone.now().date() + timedelta(days=7),
            'status': 'Active',
            'budget': 200.00
        }
        form = ProjectForm(data=form_data)
        self.assertTrue(form.is_valid())

    def test_project_form_invalid_dates(self):
        form_data = {
            'project_name': 'Invalid Date Project',
            'start_date': timezone.now().date(),
            'end_date': timezone.now().date() - timedelta(days=7), # End date before start date
            'status': 'Active',
            'budget': 100.00
        }
        form = ProjectForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('end_date', form.errors)
        self.assertEqual(form.errors['end_date'], ["End date cannot be before start date."])

    def test_project_form_missing_required_fields(self):
        form_data = {
            'project_description': 'Missing name and start date',
            'status': 'Active'
        }
        form = ProjectForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('project_name', form.errors)
        self.assertIn('start_date', form.errors)


class ProjectViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.project = Project.objects.create(
            project_name='Initial Project',
            project_description='Description for initial project.',
            start_date=timezone.now().date(),
            end_date=timezone.now().date() + timedelta(days=30),
            status='Active',
            budget=1000.00
        )
        cls.list_url = reverse('project_list')
        cls.table_url = reverse('project_table')
        cls.add_url = reverse('project_add')
        cls.edit_url = reverse('project_edit', args=[cls.project.project_id])
        cls.delete_url = reverse('project_delete', args=[cls.project.project_id])

    def setUp(self):
        self.client = Client()

    def test_project_list_view(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/list.html')
        self.assertIn('projects', response.context)
        self.assertContains(response, self.project.project_name)

    def test_project_table_partial_view(self):
        # This view is typically called via HTMX
        response = self.client.get(self.table_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_table.html')
        self.assertIn('projects', response.context)
        self.assertContains(response, self.project.project_name)

    def test_project_create_view_get(self):
        response = self.client.get(self.add_url, HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_form.html')
        self.assertIn('form', response.context)

    def test_project_create_view_post_valid(self):
        data = {
            'project_name': 'New Project from Test',
            'project_description': 'Testing new project creation.',
            'start_date': timezone.now().date(),
            'end_date': timezone.now().date() + timedelta(days=14),
            'status': 'Active',
            'budget': 750.00
        }
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success -> No Content
        self.assertTrue(Project.objects.filter(project_name='New Project from Test').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')

    def test_project_create_view_post_invalid(self):
        data = {
            'project_name': '', # Missing required field
            'start_date': timezone.now().date() + timedelta(days=7),
            'end_date': timezone.now().date(), # Invalid date
            'status': 'Active'
        }
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX invalid form -> return form partial
        self.assertTemplateUsed(response, 'project_management/project/_project_form.html')
        self.assertIn('form', response.context)
        self.assertFalse(Project.objects.filter(project_name='').exists()) # Should not create

    def test_project_update_view_get(self):
        response = self.client.get(self.edit_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.project)

    def test_project_update_view_post_valid(self):
        updated_name = 'Updated Project Name'
        data = {
            'project_name': updated_name,
            'project_description': self.project.project_description,
            'start_date': self.project.start_date,
            'end_date': self.project.end_date,
            'status': 'On Hold',
            'budget': self.project.budget
        }
        response = self.client.post(self.edit_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.project.refresh_from_db()
        self.assertEqual(self.project.project_name, updated_name)
        self.assertEqual(self.project.status, 'On Hold')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')

    def test_project_update_view_post_invalid(self):
        data = {
            'project_name': self.project.project_name,
            'start_date': self.project.start_date,
            'end_date': self.project.start_date - timedelta(days=1), # Invalid date
            'status': self.project.status,
            'budget': self.project.budget
        }
        response = self.client.post(self.edit_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Invalid form returns form partial
        self.assertTemplateUsed(response, 'project_management/project/_project_form.html')
        self.assertIn('form', response.context)
        self.project.refresh_from_db()
        self.assertNotEqual(self.project.end_date, data['end_date']) # Ensure not updated

    def test_project_delete_view_get(self):
        response = self.client.get(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/project/_project_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.project)

    def test_project_delete_view_post(self):
        project_to_delete_id = self.project.project_id # Get ID before deletion
        response = self.client.post(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Project.objects.filter(project_id=project_to_delete_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshProjectList')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The Django modernization emphasizes a highly dynamic, single-page application-like experience using HTMX and Alpine.js, minimizing the need for complex JavaScript frameworks.

-   **HTMX for dynamic updates:**
    -   The main `project_management/project/list.html` page uses `hx-get="{% url 'project_table' %}" hx-trigger="load, refreshProjectList from:body"` to load the project list table dynamically on page load and whenever a `refreshProjectList` custom event is triggered (after successful CRUD operations).
    -   "Add New", "Edit", and "Delete" buttons use `hx-get` to fetch their respective forms (`_project_form.html` or `_project_confirm_delete.html`) and `hx-target="#modalContent"` to load them into a shared modal.
    -   Form submissions (`hx-post`) from the modal are handled by HTMX. Upon success, the Django views respond with `status=204` and an `HX-Trigger` header (`refreshProjectList`), instructing HTMX to re-fetch the project list table.
    -   Form validation errors cause the view to return the form partial with errors, which HTMX swaps back into the modal, allowing immediate user feedback without modal closure.
    -   Loading indicators (`htmx-indicator`) are used on forms to provide visual feedback during asynchronous operations.

-   **Alpine.js for UI state management:**
    -   While the modal open/close logic is primarily handled by `_hyperscript` (`_="on click add .is-active to #modal"`), Alpine.js would be leveraged for more complex local UI state, such as toggling visibility of elements, handling more intricate form interactions, or managing client-side data filtering if DataTables were configured for server-side processing and needed custom Alpine components. For this specific case, its direct use is minimal, aligning with the preference for HTMX where possible.

-   **DataTables for list views:**
    -   The `project_management/project/_project_table.html` partial includes the JavaScript initialization for DataTables (`$('#projectTable').DataTable();`). This provides client-side searching, sorting, and pagination without any custom JavaScript development, simply by adding the necessary DataTables CDN links in the `base.html` (which is assumed to exist).

-   **DRY Template Inheritance:**
    -   All templates extend `core/base.html` to inherit common layout, CDN links (HTMX, Alpine.js, DataTables, Tailwind CSS), and other global components, ensuring a consistent and efficient template structure.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the `Dashboard` functionality to Django. By inferring a `Project` model and associated CRUD operations, we've demonstrated how to apply modern Django patterns, including:

-   **Fat Models, Thin Views:** Business logic (like `is_active` or `calculate_remaining_budget`) resides in the `Project` model, keeping views focused on request handling and rendering.
-   **HTMX + Alpine.js:** A powerful combination for creating dynamic, interactive user interfaces without the overhead of heavy JavaScript frameworks, directly addressing the implicit need for responsiveness suggested by `loadingNotifier.js`.
-   **DataTables:** Simplifies complex data presentation with out-of-the-box sorting, filtering, and pagination.
-   **Robust Testing:** Ensures the reliability and maintainability of the new Django application.

This approach minimizes manual coding effort by focusing on well-established patterns and tools, making the modernization process more efficient and less prone to errors, suitable for AI-assisted automation. The clear separation of concerns also aids in future maintenance and scalability.