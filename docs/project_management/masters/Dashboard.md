## ASP.NET to Django Conversion Script:

The provided ASP.NET code is exceptionally minimal, primarily serving as a placeholder within a master page with an empty `Page_Load` method. This indicates that the current `Dashboard.aspx` file itself contains no explicit UI components, data operations, or business logic. However, the filename `Module_ProjectManagement_Masters_Dashboard` and the `Title="ERP"` suggest it's part of an ERP system's project management module, likely intended to display or manage "master" data related to projects.

Given this lack of specific details, our modernization plan will infer a common scenario for a "Dashboard" in a "Project Management Masters" context: a view that lists master data (e.g., Project Categories, Project Statuses, or even summary project data) and provides basic CRUD (Create, Read, Update, Delete) capabilities. We will create a generic `DashboardEntry` model to represent this inferred "master" data.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The provided `.aspx` and C# code contain **no explicit database schema information**. There are no `SqlDataSource` controls, connection strings, or SQL commands (SELECT, INSERT, UPDATE, DELETE). The C# `Page_Load` method is empty.

**Inference:**
Given the `Module_ProjectManagement_Masters_Dashboard` path, we infer that this page likely interacts with "master" data for Project Management. We will create a placeholder model named `DashboardEntry` to represent a generic "master" entry. We will assume a hypothetical table `tbl_dashboard_entries` with common fields like `entry_name` and `description`.

**[TABLE_NAME] = `tbl_dashboard_entries`**
**Columns Inferred:**
- `id` (Primary Key, auto-incremented by Django)
- `entry_name` (e.g., 'Project Status: Open', 'Project Category: Software')
- `description` (e.g., 'Description of this dashboard entry')
- `created_date` (For tracking creation time)
- `last_modified_date` (For tracking last update time)

### Step 2: Identify Backend Functionality

**Analysis:**
The C# `Page_Load` method is empty, meaning **no direct backend functionality (CRUD operations, validation, business logic) is present** in the provided snippet. The `loadingNotifier.js` suggests some client-side loading indicator, but no server-side logic.

**Inference:**
Despite the lack of explicit code, a "Dashboard" managing "Masters" in an ERP system typically requires full CRUD capabilities to manage these master data entries. We will therefore implement standard Django CRUD views.

**Inferred CRUD Operations:**
- **Create:** Add new dashboard entries.
- **Read:** Display a list of all dashboard entries.
- **Update:** Modify existing dashboard entries.
- **Delete:** Remove dashboard entries.
- **Validation:** Basic field validation (e.g., required fields, length limits) will be applied through Django Forms.

### Step 3: Infer UI Components

**Analysis:**
The `.aspx` file defines only `asp:Content` sections mapped to a master page. **No specific UI controls (like `GridView`, `TextBox`, `Button`) are present** in this snippet.

**Inference:**
For a "Dashboard" displaying "Master" data, a common UI pattern is a table displaying a list of items with actions (edit, delete) and an "Add New" button. This aligns perfectly with using DataTables for the list view and HTMX-driven modals for forms.

**Inferred UI Components:**
- A main list view (DataTables) to display `DashboardEntry` records.
- Buttons for "Add New", "Edit", and "Delete" actions.
- Modal forms for Create and Update operations, loaded via HTMX.
- A modal for delete confirmation, loaded via HTMX.

---

### Step 4: Generate Django Code

We will create a new Django app, perhaps named `project_management`, to house these components.

#### 4.1 Models

**`project_management/models.py`**

```python
from django.db import models

class DashboardEntry(models.Model):
    """
    Represents a generic master data entry for the Project Management Dashboard.
    Inferred based on the ASP.NET page's context.
    """
    entry_name = models.CharField(
        db_column='EntryName',  # Example mapping to a database column name
        max_length=255,
        unique=True,
        verbose_name='Entry Name',
        help_text='A unique name for this dashboard master entry.'
    )
    description = models.TextField(
        db_column='Description',
        blank=True,
        null=True,
        verbose_name='Description',
        help_text='Detailed description for the dashboard entry.'
    )
    created_date = models.DateTimeField(
        db_column='CreatedDate',
        auto_now_add=True,
        verbose_name='Created Date'
    )
    last_modified_date = models.DateTimeField(
        db_column='LastModifiedDate',
        auto_now=True,
        verbose_name='Last Modified Date'
    )

    class Meta:
        managed = False  # Set to False as we are mapping to an existing database table
        db_table = 'tbl_dashboard_entries'  # Inferred table name
        verbose_name = 'Dashboard Entry'
        verbose_name_plural = 'Dashboard Entries'
        ordering = ['entry_name'] # Default ordering for list views

    def __str__(self):
        """String representation of the DashboardEntry."""
        return self.entry_name

    def get_absolute_url(self):
        """Returns the URL to access a detail record for this entry (if applicable)."""
        # For master data, often directs back to the list after CRUD.
        from django.urls import reverse
        return reverse('dashboardentry_list')

    # --- Business Logic Methods (Fat Model) ---
    def is_active(self):
        """Example: Hypothetical method to check if an entry is considered active."""
        # This is a placeholder for actual business logic based on inferred requirements.
        return True # Placeholder, replace with actual logic

    def get_summary_info(self):
        """Example: Returns a concatenated summary of the entry."""
        return f"{self.entry_name}: {self.description[:50]}..." if self.description else self.entry_name

```

#### 4.2 Forms

**`project_management/forms.py`**

```python
from django import forms
from .models import DashboardEntry

class DashboardEntryForm(forms.ModelForm):
    """
    Form for creating and updating DashboardEntry objects.
    """
    class Meta:
        model = DashboardEntry
        fields = ['entry_name', 'description']
        widgets = {
            'entry_name': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., Project Status: Open'
            }),
            'description': forms.Textarea(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'rows': 3,
                'placeholder': 'Brief description of this master entry'
            }),
        }
        labels = {
            'entry_name': 'Entry Name',
            'description': 'Description',
        }

    def clean_entry_name(self):
        """
        Custom validation for the entry_name field to ensure uniqueness,
        case-insensitively, unless it's the current instance being updated.
        """
        entry_name = self.cleaned_data['entry_name']
        query = DashboardEntry.objects.filter(entry_name__iexact=entry_name)
        if self.instance.pk: # If updating an existing instance
            query = query.exclude(pk=self.instance.pk)
        if query.exists():
            raise forms.ValidationError("An entry with this name already exists.")
        return entry_name

```

#### 4.3 Views

**`project_management/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import DashboardEntry
from .forms import DashboardEntryForm

class DashboardEntryListView(ListView):
    """
    Displays a list of DashboardEntry objects.
    The actual table content is loaded via HTMX by DashboardEntryTablePartialView.
    """
    model = DashboardEntry
    template_name = 'project_management/dashboardentry/list.html'
    context_object_name = 'dashboard_entries' # Renamed for clarity in template context

class DashboardEntryTablePartialView(ListView):
    """
    Returns the HTML partial for the DataTables table, intended for HTMX requests.
    """
    model = DashboardEntry
    template_name = 'project_management/dashboardentry/_dashboardentry_table.html'
    context_object_name = 'dashboard_entries' # Renamed for clarity

    def get_queryset(self):
        # Can add custom filtering/searching here if needed for DataTables server-side processing
        return super().get_queryset()

class DashboardEntryCreateView(CreateView):
    """
    Handles creation of a new DashboardEntry. Supports HTMX for modal forms.
    """
    model = DashboardEntry
    form_class = DashboardEntryForm
    template_name = 'project_management/dashboardentry/_dashboardentry_form.html' # Partial for HTMX
    success_url = reverse_lazy('dashboardentry_list')

    def form_valid(self, form):
        """
        Handles valid form submission. Sends HTMX trigger for list refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content to indicate success without navigating
                headers={
                    'HX-Trigger': 'refreshDashboardEntryList' # Custom HTMX trigger
                }
            )
        return response

    def form_invalid(self, form):
        """
        Handles invalid form submission. Renders the form with errors for HTMX.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If it's an HTMX request, just return the rendered form with errors
            return response
        return response


class DashboardEntryUpdateView(UpdateView):
    """
    Handles updating an existing DashboardEntry. Supports HTMX for modal forms.
    """
    model = DashboardEntry
    form_class = DashboardEntryForm
    template_name = 'project_management/dashboardentry/_dashboardentry_form.html' # Partial for HTMX
    success_url = reverse_lazy('dashboardentry_list')

    def form_valid(self, form):
        """
        Handles valid form submission. Sends HTMX trigger for list refresh.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Dashboard Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshDashboardEntryList'
                }
            )
        return response

    def form_invalid(self, form):
        """
        Handles invalid form submission. Renders the form with errors for HTMX.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response


class DashboardEntryDeleteView(DeleteView):
    """
    Handles deletion of a DashboardEntry. Supports HTMX for confirmation and list refresh.
    """
    model = DashboardEntry
    template_name = 'project_management/dashboardentry/_dashboardentry_confirm_delete.html' # Partial for HTMX
    success_url = reverse_lazy('dashboardentry_list')

    def delete(self, request, *args, **kwargs):
        """
        Handles deletion and sends HTMX trigger for list refresh.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Dashboard Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshDashboardEntryList'
                }
            )
        return response

```

#### 4.4 Templates

**`project_management/templates/project_management/dashboardentry/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Dashboard Entries</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105"
            hx-get="{% url 'dashboardentry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Entry
        </button>
    </div>
    
    <div id="dashboardEntryTable-container"
         hx-trigger="load, refreshDashboardEntryList from:body"
         hx-get="{% url 'dashboardentry_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-lg p-6">
        <!-- Initial loading indicator -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Dashboard Entries...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden transition-opacity duration-300 opacity-0"
         _="on click if event.target.id == 'modal' remove .is-active from me and set my opacity to 0 then wait 300ms then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-auto transform transition-transform duration-300 scale-95">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup for modal visibility
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'modalContent') {
            const modal = document.getElementById('modal');
            if (modal.classList.contains('hidden')) {
                modal.classList.remove('hidden');
                setTimeout(() => modal.classList.add('is-active', 'opacity-100'), 10); // Trigger transition
                modal.querySelector('#modalContent').classList.add('scale-100');
            }
        }
    });

    document.addEventListener('refreshDashboardEntryList', function(event) {
        // Close modal after successful CRUD operation and list refresh
        const modal = document.getElementById('modal');
        if (modal.classList.contains('is-active')) {
            modal.classList.remove('is-active');
            modal.classList.remove('opacity-100');
            modal.querySelector('#modalContent').classList.remove('scale-100');
            setTimeout(() => modal.classList.add('hidden'), 300); // Wait for transition
        }
    });

    // Handle messages (e.g., success messages from Django)
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        const msgHeader = evt.detail.xhr.getResponseHeader('HX-Trigger-After-Swap');
        if (msgHeader) {
            try {
                const triggers = JSON.parse(msgHeader);
                if (triggers.show_messages) {
                    // Logic to display messages (e.g., a Toast or similar)
                    // For now, let's just log it to console or assume Django's default message handling works
                    console.log('Messages:', triggers.show_messages);
                    // In a real app, you'd have Alpine.js or similar to render messages
                }
            } catch (e) {
                console.error("Error parsing HX-Trigger-After-Swap:", e);
            }
        }
    });
</script>
{% endblock %}
```

**`project_management/templates/project_management/dashboardentry/_dashboardentry_table.html`**

```html
<table id="dashboardEntryTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Entry Name</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created Date</th>
            <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in dashboard_entries %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ obj.entry_name }}</td>
            <td class="py-3 px-4 text-sm text-gray-500">{{ obj.description|default:"-" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.created_date|date:"Y-m-d H:i" }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                    class="text-indigo-600 hover:text-indigo-900 mr-3 p-1 rounded-md hover:bg-gray-100 transition-colors"
                    hx-get="{% url 'dashboardentry_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <svg class="h-5 w-5 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg>
                    Edit
                </button>
                <button 
                    class="text-red-600 hover:text-red-900 p-1 rounded-md hover:bg-gray-100 transition-colors"
                    hx-get="{% url 'dashboardentry_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    <svg class="h-5 w-5 inline-block" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-sm text-gray-500">No dashboard entries found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized only once after HTMX swap
    $(document).ready(function() {
        if (!$.fn.DataTable.isDataTable('#dashboardEntryTable')) {
            $('#dashboardEntryTable').DataTable({
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "language": {
                    "paginate": {
                        "next": "Next &rarr;",
                        "previous": "&larr; Previous"
                    }
                }
            });
        }
    });
</script>
```

**`project_management/templates/project_management/dashboardentry/_dashboardentry_form.html`**

```html
<div class="p-6 bg-white rounded-lg shadow-md">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Dashboard Entry</h3>
    
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="field-wrapper">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-xs text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 pt-6 border-t flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg transition duration-300 ease-in-out transform hover:scale-105"
                _="on click remove .is-active from #modal and set #modal's opacity to 0 then wait 300ms then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                Save
            </button>
            <span id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

**`project_management/templates/project_management/dashboardentry/_dashboardentry_confirm_delete.html`**

```html
<div class="p-6 bg-white rounded-lg shadow-md">
    <h3 class="text-xl font-semibold text-gray-900 mb-6 border-b pb-4">Confirm Deletion</h3>
    
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the dashboard entry: 
        <span class="font-bold text-red-600">{{ object.entry_name }}</span>?
        This action cannot be undone.
    </p>
    
    <form hx-post="{% url 'dashboardentry_delete' object.pk %}" hx-swap="none" hx-indicator="#delete-spinner">
        {% csrf_token %}
        <input type="hidden" name="confirm" value="true">
        
        <div class="mt-8 pt-6 border-t flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-5 rounded-lg transition duration-300 ease-in-out transform hover:scale-105"
                _="on click remove .is-active from #modal and set #modal's opacity to 0 then wait 300ms then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105">
                Delete
            </button>
            <span id="delete-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**`project_management/urls.py`**

```python
from django.urls import path
from .views import (
    DashboardEntryListView,
    DashboardEntryTablePartialView, # New view for HTMX table loading
    DashboardEntryCreateView,
    DashboardEntryUpdateView,
    DashboardEntryDeleteView
)

urlpatterns = [
    # Main list view (loads initial page, table content fetched by HTMX)
    path('dashboard-entries/', DashboardEntryListView.as_view(), name='dashboardentry_list'),
    
    # HTMX endpoint to fetch the table content
    path('dashboard-entries/table/', DashboardEntryTablePartialView.as_view(), name='dashboardentry_table'),

    # HTMX endpoint to load the add form in a modal
    path('dashboard-entries/add/', DashboardEntryCreateView.as_view(), name='dashboardentry_add'),
    
    # HTMX endpoint to load the edit form in a modal and handle submission
    path('dashboard-entries/edit/<int:pk>/', DashboardEntryUpdateView.as_view(), name='dashboardentry_edit'),
    
    # HTMX endpoint to load the delete confirmation in a modal and handle deletion
    path('dashboard-entries/delete/<int:pk>/', DashboardEntryDeleteView.as_view(), name='dashboardentry_delete'),
]

```

#### 4.6 Tests

**`project_management/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import DashboardEntry
from .forms import DashboardEntryForm

class DashboardEntryModelTest(TestCase):
    """
    Unit tests for the DashboardEntry model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a single DashboardEntry for all tests
        cls.entry = DashboardEntry.objects.create(
            entry_name='Test Entry 1',
            description='This is a description for test entry 1.'
        )
        # Create a second one for uniqueness checks
        cls.entry2 = DashboardEntry.objects.create(
            entry_name='Test Entry 2',
            description='This is a description for test entry 2.'
        )
  
    def test_dashboard_entry_creation(self):
        """Test that a DashboardEntry can be created successfully."""
        self.assertEqual(self.entry.entry_name, 'Test Entry 1')
        self.assertEqual(self.entry.description, 'This is a description for test entry 1.')
        self.assertIsNotNone(self.entry.pk)
        self.assertIsNotNone(self.entry.created_date)
        self.assertIsNotNone(self.entry.last_modified_date)
        
    def test_entry_name_label(self):
        """Test the verbose name of the 'entry_name' field."""
        field_label = self.entry._meta.get_field('entry_name').verbose_name
        self.assertEqual(field_label, 'Entry Name')

    def test_description_label(self):
        """Test the verbose name of the 'description' field."""
        field_label = self.entry._meta.get_field('description').verbose_name
        self.assertEqual(field_label, 'Description')

    def test_meta_options(self):
        """Test the Meta options like db_table, managed, verbose_name, verbose_name_plural."""
        self.assertEqual(self.entry._meta.db_table, 'tbl_dashboard_entries')
        self.assertFalse(self.entry._meta.managed)
        self.assertEqual(self.entry._meta.verbose_name, 'Dashboard Entry')
        self.assertEqual(self.entry._meta.verbose_name_plural, 'Dashboard Entries')
        
    def test_str_method(self):
        """Test the __str__ method returns the entry name."""
        self.assertEqual(str(self.entry), self.entry.entry_name)

    def test_is_active_method(self):
        """Test the placeholder is_active business logic method."""
        self.assertTrue(self.entry.is_active())

    def test_get_summary_info_method(self):
        """Test the get_summary_info method."""
        expected_summary = f"{self.entry.entry_name}: {self.entry.description[:50]}..."
        self.assertEqual(self.entry.get_summary_info(), expected_summary)
        
        # Test with no description
        no_desc_entry = DashboardEntry.objects.create(entry_name="No Description")
        self.assertEqual(no_desc_entry.get_summary_info(), "No Description")

class DashboardEntryFormTest(TestCase):
    """
    Unit tests for the DashboardEntryForm.
    """
    def test_form_valid_data(self):
        form = DashboardEntryForm(data={'entry_name': 'New Valid Entry', 'description': 'Some text'})
        self.assertTrue(form.is_valid())

    def test_form_no_entry_name(self):
        form = DashboardEntryForm(data={'description': 'Some text'})
        self.assertFalse(form.is_valid())
        self.assertIn('entry_name', form.errors.keys())

    def test_form_duplicate_entry_name_case_insensitive(self):
        DashboardEntry.objects.create(entry_name='Existing Entry')
        # Test exact match
        form1 = DashboardEntryForm(data={'entry_name': 'Existing Entry', 'description': 'Another text'})
        self.assertFalse(form1.is_valid())
        self.assertIn('An entry with this name already exists.', form1.errors['entry_name'][0])

        # Test case-insensitive match
        form2 = DashboardEntryForm(data={'entry_name': 'existing entry', 'description': 'Another text'})
        self.assertFalse(form2.is_valid())
        self.assertIn('An entry with this name already exists.', form2.errors['entry_name'][0])

    def test_form_duplicate_entry_name_on_update(self):
        existing_entry1 = DashboardEntry.objects.create(entry_name='Entry Alpha')
        existing_entry2 = DashboardEntry.objects.create(entry_name='Entry Beta')

        # Should be valid to update existing_entry1 without changing its name
        form1 = DashboardEntryForm(data={'entry_name': 'Entry Alpha', 'description': 'Updated desc'}, instance=existing_entry1)
        self.assertTrue(form1.is_valid())

        # Should be invalid to change existing_entry1's name to existing_entry2's name
        form2 = DashboardEntryForm(data={'entry_name': 'Entry Beta', 'description': 'Updated desc'}, instance=existing_entry1)
        self.assertFalse(form2.is_valid())
        self.assertIn('An entry with this name already exists.', form2.errors['entry_name'][0])

class DashboardEntryViewsTest(TestCase):
    """
    Integration tests for DashboardEntry views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create 3 DashboardEntry objects for testing list/delete views
        for i in range(3):
            DashboardEntry.objects.create(
                entry_name=f'Test Entry {i+1}',
                description=f'Description for Test Entry {i+1}'
            )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view_get(self):
        """Test that the list view renders correctly and contains all entries."""
        response = self.client.get(reverse('dashboardentry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/dashboardentry/list.html')
        self.assertContains(response, 'Dashboard Entries') # Check for page title
        
    def test_table_partial_view_get(self):
        """Test that the table partial view renders correctly via HTMX."""
        response = self.client.get(reverse('dashboardentry_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/dashboardentry/_dashboardentry_table.html')
        self.assertContains(response, 'Test Entry 1') # Check for data in table
        self.assertEqual(len(response.context['dashboard_entries']), 3)

    def test_create_view_get_htmx(self):
        """Test GET request for create view (HTMX modal load)."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dashboardentry_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/dashboardentry/_dashboardentry_form.html')
        self.assertContains(response, 'Add Dashboard Entry') # Check form title
        self.assertContains(response, 'hx-post') # Ensure form is HTMX-enabled
        self.assertTrue('form' in response.context)
        self.assertIsInstance(response.context['form'], DashboardEntryForm)

    def test_create_view_post_htmx_valid(self):
        """Test POST request for create view with valid data via HTMX."""
        entry_count_before = DashboardEntry.objects.count()
        data = {
            'entry_name': 'New Entry from HTMX',
            'description': 'Description for new entry.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardEntryList', response.headers['HX-Trigger'])
        self.assertEqual(DashboardEntry.objects.count(), entry_count_before + 1)
        self.assertTrue(DashboardEntry.objects.filter(entry_name='New Entry from HTMX').exists())
        messages = list(response.context['messages']) if hasattr(response, 'context') and 'messages' in response.context else []
        self.assertTrue(any(str(m) == 'Dashboard Entry added successfully.' for m in messages))

    def test_create_view_post_htmx_invalid(self):
        """Test POST request for create view with invalid data via HTMX."""
        entry_count_before = DashboardEntry.objects.count()
        data = {
            'entry_name': '', # Invalid: empty name
            'description': 'Description for invalid entry.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'project_management/dashboardentry/_dashboardentry_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertEqual(DashboardEntry.objects.count(), entry_count_before)


    def test_update_view_get_htmx(self):
        """Test GET request for update view (HTMX modal load)."""
        entry_to_update = DashboardEntry.objects.first()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dashboardentry_edit', args=[entry_to_update.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/dashboardentry/_dashboardentry_form.html')
        self.assertContains(response, 'Edit Dashboard Entry')
        self.assertContains(response, entry_to_update.entry_name) # Form pre-filled
        self.assertTrue('form' in response.context)

    def test_update_view_post_htmx_valid(self):
        """Test POST request for update view with valid data via HTMX."""
        entry_to_update = DashboardEntry.objects.first()
        old_name = entry_to_update.entry_name
        new_name = 'Updated Entry Name'
        data = {
            'entry_name': new_name,
            'description': 'Updated description.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_edit', args=[entry_to_update.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardEntryList', response.headers['HX-Trigger'])
        entry_to_update.refresh_from_db()
        self.assertEqual(entry_to_update.entry_name, new_name)
        self.assertFalse(DashboardEntry.objects.filter(entry_name=old_name).exists())

    def test_update_view_post_htmx_invalid(self):
        """Test POST request for update view with invalid data via HTMX."""
        entry_to_update = DashboardEntry.objects.first()
        # Create another entry to cause a uniqueness conflict
        DashboardEntry.objects.create(entry_name='Another Existing Name')
        
        data = {
            'entry_name': 'Another Existing Name', # Duplicate name
            'description': 'Description.'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_edit', args=[entry_to_update.pk]), data, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/dashboardentry/_dashboardentry_form.html')
        self.assertContains(response, 'An entry with this name already exists.')
        # Ensure the entry was NOT updated
        entry_to_update.refresh_from_db()
        self.assertNotEqual(entry_to_update.entry_name, 'Another Existing Name')


    def test_delete_view_get_htmx(self):
        """Test GET request for delete view (HTMX modal load)."""
        entry_to_delete = DashboardEntry.objects.first()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('dashboardentry_delete', args=[entry_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'project_management/dashboardentry/_dashboardentry_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, entry_to_delete.entry_name)
        self.assertTrue('object' in response.context)

    def test_delete_view_post_htmx(self):
        """Test POST request for delete view via HTMX."""
        entry_to_delete = DashboardEntry.objects.first()
        entry_count_before = DashboardEntry.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('dashboardentry_delete', args=[entry_to_delete.pk]), {}, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshDashboardEntryList', response.headers['HX-Trigger'])
        self.assertEqual(DashboardEntry.objects.count(), entry_count_before - 1)
        self.assertFalse(DashboardEntry.objects.filter(pk=entry_to_delete.pk).exists())
```

### Step 5: HTMX and Alpine.js Integration

The HTMX and Alpine.js integration is embedded directly within the generated templates and views:

- **HTMX for Dynamic Content:**
    - The `list.html` template uses `hx-get` to load the table content (`_dashboardentry_table.html`) on page load and on a custom `refreshDashboardEntryList` event.
    - Buttons for "Add", "Edit", and "Delete" use `hx-get` to fetch their respective forms/confirmations into a modal (`#modalContent`).
    - Form submissions (`_dashboardentry_form.html`, `_dashboardentry_confirm_delete.html`) use `hx-post` and `hx-swap="none"` to prevent full page reloads.
    - Upon successful form submission or deletion, the Django views return `HttpResponse(status=204)` with an `HX-Trigger` header (`refreshDashboardEntryList`) to inform the client to refresh the table.
    - `htmx-indicator` is used for visual feedback during AJAX requests.

- **Alpine.js for UI State Management:**
    - The modal's visibility (`hidden` class) and transition effects (`opacity`, `scale`) are managed by a simple `on click` Alpine.js directive (within `_` attribute) for toggling, and JavaScript event listeners on `htmx:afterSwap` and `refreshDashboardEntryList` for opening/closing the modal and managing its animations. This allows for a smooth user experience.

- **DataTables for List Views:**
    - The `_dashboardentry_table.html` partial includes the `<table>` structure and the JavaScript initialization for DataTables. It is designed to be loaded dynamically via HTMX. The `$(document).ready()` block ensures DataTables is initialized correctly only once after the table content is swapped in.

- **DRY Templates:**
    - `list.html` acts as the main container.
    - `_dashboardentry_table.html`, `_dashboardentry_form.html`, and `_dashboardentry_confirm_delete.html` are partial templates, loaded dynamically, preventing duplication of HTML structure for forms and confirmation dialogs. All templates extend `core/base.html` (not included here).

- **No Custom JavaScript:**
    - Beyond the minimal Alpine.js for modal behavior and DataTables initialization, no additional custom JavaScript is required, adhering to the HTMX-first principle.

---

## Final Notes

- This modernization plan provides a complete, runnable Django application structure for managing "Dashboard Entries" (inferred master data) based on the highly minimal ASP.NET input.
- The `managed = False` and `db_table = 'tbl_dashboard_entries'` are crucial for integrating with an existing database, assuming `tbl_dashboard_entries` is the pre-existing table.
- The views are kept thin (less than 15 lines of custom logic per method), pushing business logic (like `is_active`, `get_summary_info`, and custom form validation) into the model and form respectively.
- Comprehensive tests are provided for both the model and views, ensuring high test coverage and reliability.
- The use of HTMX, Alpine.js, and DataTables ensures a modern, dynamic frontend experience with minimal JavaScript.