## ASP.NET to Django Conversion Script:

This comprehensive modernization plan outlines the automated conversion of your ASP.NET Material Live Cost application to a modern Django-based solution. We focus on leveraging AI-assisted automation to streamline the process, ensuring a robust, scalable, and maintainable system.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code utilizes `SqlDataSource` to interact with a SQL Server database. The primary table involved is `tblMLC_LiveCost`, which holds the Material Live Cost data. It performs a `JOIN` with `tblDG_Material` to retrieve material names. System-related fields (`SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`) are updated on record modification.

**Identified Tables and Columns:**

**Primary Table:** `tblMLC_LiveCost`
- `Id`: Primary Key, Integer (Maps to `id` in Django)
- `Material`: Foreign Key, Integer (Links to `tblDG_Material.Id`)
- `EffDate`: Effective Date, stored as String (`dd-MM-yyyy`), but logically a Date. (Maps to `eff_date` as `DateField`)
- `LiveCost`: Live Cost, stored as String, but logically a Decimal. (Maps to `live_cost` as `DecimalField`)
- `SysDate`: System Date, String (Maps to `sys_date` as `DateField`)
- `SysTime`: System Time, String (Maps to `sys_time` as `TimeField`)
- `CompId`: Company ID, Integer (Maps to `comp_id` as `IntegerField`)
- `FinYearId`: Financial Year ID, Integer (Maps to `fin_year_id` as `IntegerField`)
- `SessionId`: Session ID (User ID/Name), String (Maps to `session_id` as `CharField`)

**Related Table:** `tblDG_Material`
- `Id`: Primary Key, Integer (Maps to `id` in Django)
- `Material`: Material Name, String (Maps to `material_name` as `CharField`)

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET `GridView` and `SqlDataSource` define the core backend functionalities.

- **Read (R):** The `SelectCommand` retrieves data from `tblMLC_LiveCost` and `tblDG_Material`, which is then bound to the `GridView1` for display. This corresponds to listing existing Material Live Costs.
- **Update (U):** The `UpdateCommand` allows modification of `EffDate` and `LiveCost` for existing records. The `GridView1_RowCommand` event handler sets `SysDate`, `SysTime`, `CompId`, `FinYearId`, and `SessionId` parameters before executing the update.
- **Delete (D):** The `DeleteCommand` is present, suggesting deletion functionality, although no explicit delete UI element is visible in the provided `.aspx`. We will implement a delete action for completeness and adherence to CRUD.
- **Create (C):** An `InsertCommand` is defined in `SqlDataSource`, but no UI for adding new records is present in the `.aspx` file. We will include a "Add New" feature for a full CRUD implementation.

**Validation Logic:**
- `RequiredFieldValidator` ensures `TxtDate` (EffDate) and `txtLiveCost` (LiveCost) are not empty. This will be replicated in Django forms.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET `.aspx` file uses a `GridView` for displaying and editing tabular data. `TextBox` controls are used for input, and `CalendarExtender` provides a date picker for `EffDate`.

- **GridView1:** Maps directly to a Django template using a HTML `<table>` and DataTables.
- **TextBoxes:** Will be rendered as standard HTML `<input type="text" />` or `<input type="number" />` with Tailwind CSS styling, handled by Django's form widgets.
- **CalendarExtender:** Replaced by HTML5 `<input type="date">` or a custom date picker integration (though `type='date'` is preferred for simplicity and browser native support).
- **Buttons/Links:** Actions like "Edit", "Delete", "Add" will be standard HTML `<button>` or `<a>` elements with HTMX attributes to trigger dynamic content loading (modals) and form submissions.
- **`lblMessage`:** Replaced by Django's `messages` framework, displayed via HTMX on successful operations.

### Step 4: Generate Django Code

We will create a new Django app named `materialcosting`.

#### 4.1 Models (`materialcosting/models.py`)

This file defines the Django models that map to your existing database tables. We set `managed = False` to ensure Django doesn't try to create or modify these tables, as they already exist.

```python
from django.db import models
from django.utils import timezone

# Using get_user_model() for potential SessionId mapping
# In a real application, you'd likely have a custom User model
# or a profile linked to the default User model for CompId/FinYearId.
# For simplicity, we'll use placeholder values for these in the save method.

class Material(models.Model):
    """
    Maps to tblDG_Material.
    Holds the definition of materials, referenced by MaterialLiveCost.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    material_name = models.CharField(db_column='Material', max_length=255) # Assuming max_length based on common material names

    class Meta:
        managed = False  # Django will not create or delete this table
        db_table = 'tblDG_Material'
        verbose_name = 'Material Definition'
        verbose_name_plural = 'Material Definitions'
        ordering = ['material_name'] # Order for dropdowns

    def __str__(self):
        return self.material_name

class MaterialLiveCost(models.Model):
    """
    Maps to tblMLC_LiveCost.
    Holds the live cost information for materials.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    material = models.ForeignKey(Material, on_delete=models.PROTECT, db_column='Material', 
                                 related_name='live_costs', verbose_name='Material Name')
    eff_date = models.DateField(db_column='EffDate', verbose_name='Effective Date')
    live_cost = models.DecimalField(db_column='LiveCost', max_digits=10, decimal_places=2, verbose_name='Live Cost')
    
    # System fields updated by the ASP.NET application
    sys_date = models.DateField(db_column='SysDate', null=True, blank=True)
    sys_time = models.TimeField(db_column='SysTime', null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)
    fin_year_id = models.IntegerField(db_column='FinYearId', null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=255, null=True, blank=True) # User ID/Username

    class Meta:
        managed = False  # Django will not create or delete this table
        db_table = 'tblMLC_LiveCost'
        verbose_name = 'Material Live Cost'
        verbose_name_plural = 'Material Live Costs'
        # Order by Id Desc as per the ASP.NET SQL query
        ordering = ['-id']

    def __str__(self):
        return f"{self.material.material_name} - {self.eff_date.strftime('%d-%m-%Y')} - {self.live_cost}"

    def save(self, *args, **kwargs):
        """
        Overrides the save method to automatically populate system fields
        before saving the instance, mimicking the ASP.NET code-behind logic.
        """
        # Set system date and time to current
        self.sys_date = timezone.now().date()
        self.sys_time = timezone.now().time()

        # Placeholder for CompId, FinYearId, SessionId from context (e.g., current user)
        # In a real application, you would pass these via a custom manager method or directly
        # from the view (e.g., self.request.user.profile.company_id).
        # For this example, we'll use dummy values if they are not already set.
        if not self.comp_id:
            self.comp_id = 1  # Example Company ID
        if not self.fin_year_id:
            self.fin_year_id = 2024  # Example Financial Year ID
        if not self.session_id:
            # Assuming 'SessionId' stores username or user ID.
            # In a real Django app, you'd get this from request.user
            # For demonstration, a static value.
            self.session_id = 'automated_migration' 

        super().save(*args, **kwargs)

    # Example of a fat model method (business logic)
    def is_cost_above_threshold(self, threshold_value):
        """Checks if the live cost is above a specified threshold."""
        return self.live_cost > threshold_value
```

#### 4.2 Forms (`materialcosting/forms.py`)

This file defines the Django forms used for creating and updating `MaterialLiveCost` instances. We use `ModelForm` for convenience and add custom widgets for styling and date input.

```python
from django import forms
from .models import MaterialLiveCost, Material

class MaterialLiveCostForm(forms.ModelForm):
    """
    Form for creating and updating Material Live Cost records.
    """
    # Override material field to use ModelChoiceField for dropdown
    material = forms.ModelChoiceField(
        queryset=Material.objects.all().order_by('material_name'),
        empty_label="--- Select Material ---",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    # Use HTML5 date input type for browser native date picker
    eff_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date' # This makes the browser display a date picker
        }),
        # Define formats for parsing input (client-side date formats vary)
        input_formats=['%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y']
    )
    
    # Use NumberInput for live_cost for appropriate input type
    live_cost = forms.DecimalField(
        max_digits=10, decimal_places=2,
        widget=forms.NumberInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'step': '0.01' # Suggests increment/decrement for decimal numbers
        })
    )

    class Meta:
        model = MaterialLiveCost
        fields = ['material', 'eff_date', 'live_cost']
        # No `widgets` mapping here as they are explicitly defined above for customization

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set fields as required based on ASP.NET RequiredFieldValidator
        self.fields['material'].required = True
        self.fields['eff_date'].required = True
        self.fields['live_cost'].required = True

    def clean_live_cost(self):
        """
        Custom validation for live_cost: ensures it's not negative.
        """
        live_cost = self.cleaned_data.get('live_cost')
        if live_cost is not None and live_cost < 0:
            raise forms.ValidationError("Live cost cannot be negative.")
        return live_cost
```

#### 4.3 Views (`materialcosting/views.py`)

These class-based views handle the logic for displaying, creating, updating, and deleting Material Live Cost records. They are kept thin, delegating business logic to the models and relying on HTMX for dynamic interactions.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse

from .models import MaterialLiveCost
from .forms import MaterialLiveCostForm

class MaterialLiveCostListView(ListView):
    """
    Displays the main Material Live Cost page.
    The actual data table is loaded via HTMX from MaterialLiveCostTablePartialView.
    """
    model = MaterialLiveCost
    template_name = 'materialcosting/materiallivecost/list.html'
    context_object_name = 'material_live_costs'

class MaterialLiveCostTablePartialView(ListView):
    """
    Renders the Material Live Cost table fragment for HTMX requests.
    This view is called by HTMX to refresh the table without a full page reload.
    """
    model = MaterialLiveCost
    template_name = 'materialcosting/materiallivecost/_materiallivecost_table.html'
    context_object_name = 'material_live_costs'

class MaterialLiveCostCreateView(CreateView):
    """
    Handles the creation of new Material Live Cost records.
    Uses HTMX for form submission and modal interaction.
    """
    model = MaterialLiveCost
    form_class = MaterialLiveCostForm
    template_name = 'materialcosting/materiallivecost/_materiallivecost_form.html' # Partial template for modal
    success_url = reverse_lazy('materiallivecost_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # The save method of the model handles system fields (SysDate, SysTime, etc.)
        response = super().form_valid(form)
        messages.success(self.request, 'Material Live Cost added successfully.')
        
        # Respond to HTMX request by triggering events on the client
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,  # No content, just send headers
                headers={
                    'HX-Trigger': '{"refreshMaterialLiveCostList": {}, "closeModal": {}}'
                    # 'refreshMaterialLiveCostList' tells HTMX to reload the table
                    # 'closeModal' tells Alpine.js/Hyperscript to close the modal
                }
            )
        return response

    def form_invalid(self, form):
        # If validation fails, return the form with errors to HTMX
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response # HTMX will swap this back into the modal
        return response

class MaterialLiveCostUpdateView(UpdateView):
    """
    Handles the updating of existing Material Live Cost records.
    Uses HTMX for form submission and modal interaction.
    """
    model = MaterialLiveCost
    form_class = MaterialLiveCostForm
    template_name = 'materialcosting/materiallivecost/_materiallivecost_form.html' # Partial template for modal
    success_url = reverse_lazy('materiallivecost_list') # Fallback for non-HTMX requests

    def form_valid(self, form):
        # The save method of the model handles system fields
        response = super().form_valid(form)
        messages.success(self.request, 'Material Live Cost updated successfully.')

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialLiveCostList": {}, "closeModal": {}}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response

class MaterialLiveCostDeleteView(DeleteView):
    """
    Handles the deletion of Material Live Cost records.
    Uses HTMX for confirmation and table refresh.
    """
    model = MaterialLiveCost
    template_name = 'materialcosting/materiallivecost/_materiallivecost_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('materiallivecost_list') # Fallback for non-HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Material Live Cost deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': '{"refreshMaterialLiveCostList": {}, "closeModal": {}}'
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['object'] = self.get_object() # Ensure object is passed to the delete confirmation template
        return context
```

#### 4.4 Templates

Templates follow a DRY approach using Django's template inheritance and partial templates for HTMX.

**`materialcosting/materiallivecost/list.html`**
This is the main page template that loads the list view. It acts as a container for the HTMX-loaded table and modal.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Material Live Costs</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'materiallivecost_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal"> {# Hyperscript to show modal #}
            Add New Material Live Cost
        </button>
    </div>
    
    {# Container for the HTMX-loaded DataTable #}
    <div id="materiallivecostTable-container"
         hx-trigger="load, refreshMaterialLiveCostList from:body" {# Load on page load and on custom event #}
         hx-get="{% url 'materiallivecost_table' %}" {# URL to fetch the table partial #}
         hx-swap="innerHTML">
        {# Loading indicator while content is fetched #}
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Material Live Costs...</p>
        </div>
    </div>
    
    {# Modal structure for forms and delete confirmation #}
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on closeModal remove .is-active from me"> {# Hyperscript to hide modal on 'closeModal' event #}
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"
             _="on click if event.target.id == 'modalContent' stopPropagation"> {# Prevent modal content from closing modal on click #}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{# CDN for jQuery and DataTables #}
<script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<link href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css" rel="stylesheet">
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed.
        // For this specific pattern, Alpine.js might not be strictly necessary
        // as Hyperscript handles modal state, but it's available for complex UI logic.
    });
</script>
{% endblock %}
```

**`materialcosting/materiallivecost/_materiallivecost_table.html`**
This partial template contains the actual DataTables structure, designed to be swapped in by HTMX.

```html
<table id="materiallivecostTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material Name</th>
            <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective Date</th>
            <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Live Cost</th>
            <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in material_live_costs %}
        <tr class="hover:bg-gray-50">
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.material.material_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-sm text-gray-800">{{ obj.eff_date|date:"d-m-Y" }}</td> {# Format date to dd-MM-YYYY #}
            <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-800">{{ obj.live_cost }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-center">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md text-xs mr-2"
                    hx-get="{% url 'materiallivecost_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs"
                    hx-get="{% url 'materiallivecost_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="5" class="py-4 px-4 text-center text-gray-500 text-base">No data to display !</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Initialize DataTable after HTMX content swap.
    // This script runs each time the partial is loaded by HTMX.
    $(document).ready(function() {
        // Only initialize if not already a DataTable instance
        if (!$.fn.DataTable.isDataTable('#materiallivecostTable')) {
            $('#materiallivecostTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "paging": true,
                "searching": true,
                "ordering": true,
                "info": true,
                "responsive": true
            });
        }
    });
</script>
```

**`materialcosting/materiallivecost/_materiallivecost_form.html`**
This partial template renders the form for both adding and editing.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">
        {% if form.instance.pk %}Edit{% else %}Add{% endif %} Material Live Cost
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" allows HX-Trigger to manage content #}
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span> {% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                _="on click trigger closeModal from #modalContent"> {# Trigger custom event to close modal #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save
            </button>
        </div>
    </form>
</div>
```

**`materialcosting/materiallivecost/_materiallivecost_confirm_delete.html`**
This partial template is for the delete confirmation modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">
        Are you sure you want to delete the Material Live Cost record for 
        <strong>{{ object.material.material_name }}</strong> 
        with Effective Date <strong>{{ object.eff_date|date:"d-m-Y" }}</strong> 
        and Live Cost <strong>{{ object.live_cost }}</strong>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'materiallivecost_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                _="on click trigger closeModal from #modalContent">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`materialcosting/urls.py`)

This file defines the URL patterns that map requests to your Django views.

```python
from django.urls import path
from .views import (
    MaterialLiveCostListView,
    MaterialLiveCostCreateView,
    MaterialLiveCostUpdateView,
    MaterialLiveCostDeleteView,
    MaterialLiveCostTablePartialView,
)

urlpatterns = [
    # Main list view (full page load, then HTMX loads table)
    path('material-live-costs/', MaterialLiveCostListView.as_view(), name='materiallivecost_list'),

    # HTMX endpoints for CRUD operations (load into modal)
    path('material-live-costs/add/', MaterialLiveCostCreateView.as_view(), name='materiallivecost_add'),
    path('material-live-costs/edit/<int:pk>/', MaterialLiveCostUpdateView.as_view(), name='materiallivecost_edit'),
    path('material-live-costs/delete/<int:pk>/', MaterialLiveCostDeleteView.as_view(), name='materiallivecost_delete'),

    # HTMX-specific endpoint for refreshing the DataTables content
    path('material-live-costs/table/', MaterialLiveCostTablePartialView.as_view(), name='materiallivecost_table'),
]
```

#### 4.6 Tests (`materialcosting/tests.py`)

These tests ensure the correctness of your models, forms, and views, covering unit and integration aspects.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from decimal import Decimal
from .models import Material, MaterialLiveCost
from .forms import MaterialLiveCostForm

class MaterialModelTest(TestCase):
    """
    Unit tests for the Material model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a dummy Material instance for testing
        cls.material1 = Material.objects.create(id=1, material_name='Test Material A')
        cls.material2 = Material.objects.create(id=2, material_name='Test Material B')

    def test_material_creation(self):
        """Test Material model creation and field values."""
        self.assertEqual(self.material1.id, 1)
        self.assertEqual(self.material1.material_name, 'Test Material A')
        self.assertEqual(str(self.material1), 'Test Material A')
        self.assertEqual(self.material1._meta.db_table, 'tblDG_Material')
        self.assertFalse(self.material1._meta.managed)

    def test_material_verbose_name(self):
        """Test Material model verbose names."""
        self.assertEqual(self.material1._meta.verbose_name, 'Material Definition')
        self.assertEqual(self.material1._meta.verbose_name_plural, 'Material Definitions')

class MaterialLiveCostModelTest(TestCase):
    """
    Unit tests for the MaterialLiveCost model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a dummy Material for the FK
        cls.material = Material.objects.create(id=101, material_name='Test Material X')
        
        # Create a dummy MaterialLiveCost instance
        cls.live_cost = MaterialLiveCost.objects.create(
            id=1,
            material=cls.material,
            eff_date=timezone.now().date(),
            live_cost=Decimal('123.45'),
            # Sys fields are auto-populated by save(), so don't set here for initial create
        )

    def test_material_live_cost_creation(self):
        """Test MaterialLiveCost model creation and field values."""
        obj = MaterialLiveCost.objects.get(id=1)
        self.assertEqual(obj.material, self.material)
        self.assertEqual(obj.live_cost, Decimal('123.45'))
        self.assertEqual(obj.eff_date, timezone.now().date())
        self.assertEqual(obj._meta.db_table, 'tblMLC_LiveCost')
        self.assertFalse(obj._meta.managed)

    def test_material_live_cost_auto_fields_on_save(self):
        """Test that system fields are auto-populated on save."""
        obj = MaterialLiveCost.objects.get(id=1)
        # Check if fields were set by the overridden save method
        self.assertIsNotNone(obj.sys_date)
        self.assertIsNotNone(obj.sys_time)
        self.assertIsNotNone(obj.comp_id)
        self.assertIsNotNone(obj.fin_year_id)
        self.assertIsNotNone(obj.session_id)
        
        # Verify the placeholder values
        self.assertEqual(obj.comp_id, 1)
        self.assertEqual(obj.fin_year_id, 2024)
        self.assertEqual(obj.session_id, 'automated_migration')

        # Test updating and saving again
        obj.live_cost = Decimal('99.99')
        obj.save()
        updated_obj = MaterialLiveCost.objects.get(id=1)
        self.assertEqual(updated_obj.live_cost, Decimal('99.99'))
        # System fields should be updated to current time/date
        self.assertEqual(updated_obj.sys_date, timezone.now().date())


    def test_material_live_cost_string_representation(self):
        """Test the __str__ method of MaterialLiveCost."""
        obj = MaterialLiveCost.objects.get(id=1)
        expected_str = f"{self.material.material_name} - {obj.eff_date.strftime('%d-%m-%Y')} - {obj.live_cost}"
        self.assertEqual(str(obj), expected_str)

    def test_is_cost_above_threshold_method(self):
        """Test the custom business logic method."""
        obj = MaterialLiveCost.objects.get(id=1)
        self.assertTrue(obj.is_cost_above_threshold(Decimal('100.00')))
        self.assertFalse(obj.is_cost_above_threshold(Decimal('200.00')))
        self.assertFalse(obj.is_cost_above_threshold(Decimal('123.45'))) # Should be strictly greater

class MaterialLiveCostFormTest(TestCase):
    """
    Unit tests for the MaterialLiveCostForm.
    """
    @classmethod
    def setUpTestData(cls):
        cls.material = Material.objects.create(id=102, material_name='Form Test Material')

    def test_form_valid_data(self):
        """Test form with valid data."""
        form = MaterialLiveCostForm(data={
            'material': self.material.pk,
            'eff_date': '2023-01-15',
            'live_cost': '50.75'
        })
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['material'], self.material)
        self.assertEqual(form.cleaned_data['eff_date'], timezone.datetime(2023, 1, 15).date())
        self.assertEqual(form.cleaned_data['live_cost'], Decimal('50.75'))

    def test_form_missing_required_fields(self):
        """Test form with missing required fields."""
        form = MaterialLiveCostForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('material', form.errors)
        self.assertIn('eff_date', form.errors)
        self.assertIn('live_cost', form.errors)

    def test_form_live_cost_negative(self):
        """Test form validation for negative live cost."""
        form = MaterialLiveCostForm(data={
            'material': self.material.pk,
            'eff_date': '2023-01-15',
            'live_cost': '-10.00'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('live_cost', form.errors)
        self.assertIn('Live cost cannot be negative.', form.errors['live_cost'])

    def test_form_eff_date_invalid_format(self):
        """Test form validation for invalid date format."""
        form = MaterialLiveCostForm(data={
            'material': self.material.pk,
            'eff_date': '15/01/2023-invalid', # Not in expected format
            'live_cost': '50.00'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('eff_date', form.errors)
        self.assertIn('Enter a valid date.', form.errors['eff_date'])


class MaterialLiveCostViewsTest(TestCase):
    """
    Integration tests for MaterialLiveCost views.
    """
    @classmethod
    def setUpTestData(cls):
        cls.material = Material.objects.create(id=201, material_name='View Test Material')
        cls.live_cost1 = MaterialLiveCost.objects.create(
            id=1, material=cls.material, eff_date='2023-01-01', live_cost=Decimal('100.00')
        )
        cls.live_cost2 = MaterialLiveCost.objects.create(
            id=2, material=cls.material, eff_date='2023-02-01', live_cost=Decimal('200.00')
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test GET request to MaterialLiveCost list view."""
        response = self.client.get(reverse('materiallivecost_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materiallivecost/list.html')
        # Check that the main list view doesn't directly contain the table content
        # It should rely on HTMX to load _materiallivecost_table.html
        self.assertNotContains(response, '<table id="materiallivecostTable"')

    def test_table_partial_view_get(self):
        """Test GET request to MaterialLiveCost table partial view (HTMX)."""
        response = self.client.get(reverse('materiallivecost_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materiallivecost/_materiallivecost_table.html')
        self.assertContains(response, '<table id="materiallivecostTable"')
        self.assertContains(response, self.live_cost1.material.material_name)
        self.assertContains(response, str(self.live_cost2.live_cost))

    def test_create_view_get_htmx(self):
        """Test GET request to create view (HTMX for modal)."""
        response = self.client.get(reverse('materiallivecost_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materiallivecost/_materiallivecost_form.html')
        self.assertContains(response, 'Add Material Live Cost')
        self.assertContains(response, 'type="date"') # Check for date widget

    def test_create_view_post_htmx_success(self):
        """Test POST request to create view (HTMX success)."""
        initial_count = MaterialLiveCost.objects.count()
        data = {
            'material': self.material.pk,
            'eff_date': '2024-03-15',
            'live_cost': '350.00'
        }
        response = self.client.post(reverse('materiallivecost_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(MaterialLiveCost.objects.filter(live_cost='350.00').exists())
        self.assertEqual(MaterialLiveCost.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshMaterialLiveCostList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_create_view_post_htmx_invalid(self):
        """Test POST request to create view (HTMX invalid data)."""
        initial_count = MaterialLiveCost.objects.count()
        data = {
            'material': self.material.pk,
            'eff_date': '', # Invalid
            'live_cost': '-50.00' # Invalid
        }
        response = self.client.post(reverse('materiallivecost_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form with errors returned
        self.assertTemplateUsed(response, 'materialcosting/materiallivecost/_materiallivecost_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Live cost cannot be negative.')
        self.assertEqual(MaterialLiveCost.objects.count(), initial_count)
        self.assertNotIn('HX-Trigger', response.headers) # No trigger on invalid form

    def test_update_view_get_htmx(self):
        """Test GET request to update view (HTMX for modal)."""
        response = self.client.get(reverse('materiallivecost_edit', args=[self.live_cost1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materiallivecost/_materiallivecost_form.html')
        self.assertContains(response, 'Edit Material Live Cost')
        self.assertContains(response, 'value="100.00"') # Check if existing data is pre-filled

    def test_update_view_post_htmx_success(self):
        """Test POST request to update view (HTMX success)."""
        data = {
            'material': self.live_cost1.material.pk,
            'eff_date': '2023-01-01',
            'live_cost': '125.50'
        }
        response = self.client.post(reverse('materiallivecost_edit', args=[self.live_cost1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        updated_cost = MaterialLiveCost.objects.get(pk=self.live_cost1.pk)
        self.assertEqual(updated_cost.live_cost, Decimal('125.50'))
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get_htmx(self):
        """Test GET request to delete view (HTMX for modal)."""
        response = self.client.get(reverse('materiallivecost_delete', args=[self.live_cost1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialcosting/materiallivecost/_materiallivecost_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, f'delete the Material Live Cost record for <strong>{self.live_cost1.material.material_name}</strong>')

    def test_delete_view_post_htmx_success(self):
        """Test POST request to delete view (HTMX success)."""
        initial_count = MaterialLiveCost.objects.count()
        response = self.client.post(reverse('materiallivecost_delete', args=[self.live_cost1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(MaterialLiveCost.objects.filter(pk=self.live_cost1.pk).exists())
        self.assertEqual(MaterialLiveCost.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)

```

### Step 5: HTMX and Alpine.js Integration

The provided Django code templates demonstrate full integration with HTMX and Alpine.js (via Hyperscript for modal control):

- **HTMX for Dynamic Content:**
    - The `list.html` loads the `_materiallivecost_table.html` via `hx-get` on page `load` and on a custom `refreshMaterialLiveCostList` event.
    - "Add", "Edit", and "Delete" buttons trigger `hx-get` requests to load forms/confirmation messages into the `#modalContent` div.
    - Form submissions (`hx-post`) on `_materiallivecost_form.html` and `_materiallivecost_confirm_delete.html` use `hx-swap="none"` and rely on `HX-Trigger` headers from the Django views.
    - The `HX-Trigger` contains `{"refreshMaterialLiveCostList":{}, "closeModal":{}}` which ensures the table is reloaded and the modal is dismissed after successful CRUD operations.

- **Alpine.js/Hyperscript for UI State:**
    - The modal's visibility is managed using Hyperscript (`_`) attributes.
    - `_="on click add .is-active to #modal"` shows the modal.
    - `_="on closeModal remove .is-active from me"` hides the modal. The `closeModal` event is triggered by HTMX after a successful form submission.
    - A simple click outside content to close the modal is added with `_="on click if event.target.id == 'modal' remove .is-active from me"`.

- **DataTables for List Views:**
    - The `_materiallivecost_table.html` snippet includes JavaScript to initialize DataTables on the `materiallivecostTable` after the HTMX swap. This provides client-side pagination, searching, and sorting without server-side reloads.
    - Required jQuery and DataTables CDN links are placed in `extra_js` block in `list.html`.

This setup ensures a highly interactive and responsive user experience, moving away from traditional ASP.NET postbacks to modern, efficient partial updates.