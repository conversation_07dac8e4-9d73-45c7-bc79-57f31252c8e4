## ASP.NET to Django Conversion Script: Supplier Master - Edit

This document outlines the comprehensive modernization plan for transitioning the ASP.NET `SupplierMaster_Edit.aspx` application to a modern Django-based solution. Our approach prioritizes automation, leverages AI-assisted tools, and focuses on a clean, maintainable architecture using Django 5.0+, HTMX, Alpine.js, and DataTables.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists in `core/base.html`.
- Focus ONLY on component-specific code for the `material_management` module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html`.
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts with `tblMM_Supplier_master`, `tblFinancial_master`, and `tblHR_OfficeStaff` tables.
The primary operations involve selecting data from `tblMM_Supplier_master` and joining it with `tblFinancial_master` on `FinYearId` and `tblHR_OfficeStaff` on `SessionId` (referred to as `EmpId` in the join). The `AutoCompleteExtender` also queries `tblMM_Supplier_master` for `SupplierId` and `SupplierName`.

**Inferred Tables and Columns:**
*   **[TABLE_NAME] = `tblMM_Supplier_master`**
    *   `SupplierId` (Primary Key, unique identifier for a supplier)
    *   `SupplierName` (Text, name of the supplier)
    *   `SysDate` (Text, system date when the record was generated, stored as a string, e.g., 'MM-DD-YYYY' or 'DD-MM-YYYY')
    *   `FinYearId` (Foreign Key to `tblFinancial_master`)
    *   `SessionId` (Foreign Key to `tblHR_OfficeStaff`, likely representing `EmpId`)
    *   `CompId` (Integer, company ID)
*   **[TABLE_NAME] = `tblFinancial_master`**
    *   `FinYearId` (Primary Key)
    *   `FinYear` (Text, financial year string)
*   **[TABLE_NAME] = `tblHR_OfficeStaff`**
    *   `EmpId` (Primary Key)
    *   `EmployeeName` (Text, name of the employee)

### Step 2: Identify Backend Functionality

**Analysis:**
The provided ASP.NET code primarily implements a **Read (List)** and **Search** functionality for supplier records.

*   **Read (List):** The `SearchGridView1` displays a list of suppliers.
*   **Search:**
    *   A `TxtSearchValue` text box allows users to input a search query.
    *   An `AutoCompleteExtender` provides suggestions from `SupplierName [SupplierId]` based on partial input.
    *   The `Search` button triggers `Search_Click`, which uses `fun.getCode()` to extract the `SupplierId` from the `TxtSearchValue` (assuming the format "Name [ID]") and then calls `BindData(Spid)` to filter the `GridView` by `SupplierId`.
*   **Pagination & Sorting:** The `GridView` supports pagination (`AllowPaging="True"`) and sorting (`AllowSorting="True"`).
*   **Data Binding:** The `BindData` method executes a SQL query to fetch data, joining `tblMM_Supplier_master` with `tblFinancial_master` and `tblHR_OfficeStaff`. Filtering by `CompId`, `FinYearId`, and optionally `SupplierId` is performed.
*   **Navigation:** The `HyperLinkField` for "Supplier Name" indicates navigation to a detailed edit page (`SupplierMaster_Edit_Details.aspx?SupplierId={0}&ModId=6&SubModId=22`). This confirms this page is primarily a search/list interface for an editing workflow, not the edit page itself.

**Conclusion:** The core functionality to migrate is data retrieval, filtering, and autocomplete for supplier records. While the page is titled "Edit", its primary role is to enable *finding* a supplier for subsequent editing on another page. For a comprehensive Django solution, placeholder CRUD operations will be included, as per requirements.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET controls indicate the following UI structure:

*   **Search Input:** `<asp:TextBox ID="TxtSearchValue">` with `AutoCompleteExtender` for `Supplier Name` search. This will be replaced by a standard HTML input combined with HTMX for autocomplete suggestions and Alpine.js for managing the selected value and UI state.
*   **Search Button:** `<asp:Button ID="Search">` to trigger the search. This will be an HTMX-powered button to refresh the data table.
*   **Data Display:** `<asp:GridView ID="SearchGridView1">` for tabular data display. This will be replaced by a `<table>` element, styled with Tailwind CSS, and enhanced with DataTables.js for client-side functionality (search, sort, pagination). HTMX will be used to load and refresh this table dynamically.
*   **Navigation Links:** `HyperLinkField` to `SupplierMaster_Edit_Details.aspx`. This will translate to Django URLs pointing to an update view, rendered as standard `<a>` tags.
*   **Master Page/Layout:** Content is placed in `ContentPlaceHolderID="Content7"`. This implies a shared layout, which will be handled by `core/base.html` in Django.

### Step 4: Generate Django Code

The Django application will be named `material_management` to align with the original module structure.

#### 4.1 Models

We will create three models: `Supplier`, `FinancialYear`, and `Employee`, mirroring the database schema. The business logic for querying and data manipulation will be placed in `SupplierManager` and `Supplier` model methods.

**File: `material_management/models.py`**

```python
from django.db import models
from django.db.models import Q
import datetime

class FinancialYear(models.Model):
    """
    Represents the tblFinancial_master table.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year or f"ID: {self.fin_year_id}"

class Employee(models.Model):
    """
    Represents the tblHR_OfficeStaff table.
    """
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name or f"ID: {self.emp_id}"

class SupplierManager(models.Manager):
    """
    Custom manager for Supplier model to encapsulate complex queries.
    """
    def get_suppliers_for_display(self, company_id, financial_year_id, search_supplier_id=None, search_term=None):
        """
        Mimics the BindData logic, fetching supplier details with joins
        and applying search filters.
        """
        queryset = self.select_related('financial_year', 'generated_by').filter(
            company_id=company_id,
            financial_year__fin_year_id__lte=financial_year_id # Use <= as per original SQL
        )

        if search_supplier_id:
            queryset = queryset.filter(supplier_id=search_supplier_id)
        elif search_term:
            # Generic search by name if ID isn't provided
            queryset = queryset.filter(supplier_name__icontains=search_term)

        # Order by SupplierId or Name if needed for consistency with original GridView.
        # The original code implies an implicit order or the order in the DataSet.
        # Let's order by SupplierName for consistent display.
        return queryset.order_by('supplier_name')

    def get_autocomplete_suggestions(self, prefix_text, company_id):
        """
        Mimics the 'sql' web method for autocomplete.
        Returns a list of strings 'SupplierName [SupplierId]'.
        """
        suggestions = []
        # Case-insensitive StartsWith equivalent for SupplierName
        # Note: The original code fetched all and then filtered in memory.
        # For large datasets, this should be optimized using database filtering.
        suppliers = self.filter(
            Q(supplier_name__istartswith=prefix_text) | Q(supplier_id__icontains=prefix_text), # Also search by ID for robustness
            company_id=company_id
        ).order_by('supplier_name')[:10] # Limit results for performance

        for supplier in suppliers:
            suggestions.append(f"{supplier.supplier_name} [{supplier.supplier_id}]")
        return suggestions

    def extract_supplier_id_from_text(self, text_with_id):
        """
        Mimics fun.getCode() to extract SupplierId from 'Name [ID]' format.
        """
        if '[' in text_with_id and ']' in text_with_id:
            try:
                # Find the last occurrence of '[' and first of ']' after that
                start_index = text_with_id.rfind('[')
                end_index = text_with_id.find(']', start_index)
                if start_index != -1 and end_index != -1:
                    supplier_id_str = text_with_id[start_index + 1:end_index].strip()
                    return int(supplier_id_str)
            except ValueError:
                pass # Not an integer ID
        return None # Return None if not found or invalid format

class Supplier(models.Model):
    """
    Represents the tblMM_Supplier_master table.
    """
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    system_date = models.CharField(db_column='SysDate', max_length=50, blank=True, null=True) # Stored as varchar
    financial_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)
    generated_by = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', blank=True, null=True) # Original field is SessionId
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Assuming CompId is part of tblMM_Supplier_master

    objects = SupplierManager()

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} ({self.supplier_id})"

    @property
    def formatted_gen_date(self):
        """
        Formats the system_date (varchar) into DD-MM-YYYY string for display.
        Attempts to parse common date formats.
        The original SQL logic was complex; this simplifies for display.
        """
        if not self.system_date:
            return ""
        
        # Try parsing common formats
        formats = ['%m-%d-%Y', '%d-%m-%Y', '%Y-%m-%d', '%m/%d/%Y', '%d/%m/%Y']
        for fmt in formats:
            try:
                date_obj = datetime.datetime.strptime(self.system_date, fmt).date()
                return date_obj.strftime('%d-%m-%Y')
            except ValueError:
                continue
        return self.system_date # Fallback if no format matches

    @property
    def display_fin_year(self):
        return self.financial_year.year if self.financial_year else 'N/A'

    @property
    def display_generated_by(self):
        return self.generated_by.employee_name if self.generated_by else 'N/A'

    # Placeholder business logic methods (Fat Model)
    def can_edit(self, user):
        # Example: Check if the user has permission to edit this supplier
        return user.is_staff # Simplified check
    
    def prepare_for_display(self):
        # Example: Pre-process data before sending to template
        return {
            'pk': self.pk,
            'sn': None, # SN is loop index in template
            'fin_year': self.display_fin_year,
            'supplier_name': self.supplier_name,
            'supplier_id': self.supplier_id,
            'gen_date': self.formatted_gen_date,
            'gen_by': self.display_generated_by,
        }
```

#### 4.2 Forms

A `ModelForm` will be created for the `Supplier` model.

**File: `material_management/forms.py`**

```python
from django import forms
from .models import Supplier, FinancialYear, Employee

class SupplierForm(forms.ModelForm):
    """
    Form for Supplier model, for Create and Update operations.
    """
    # For demonstration, assuming fields typically edited.
    # SupplierId is PK, often auto-generated or managed by DB in real systems.
    # Here, it's an existing PK, so we'd typically not edit it directly via form
    # unless it's a new record where the ID is user-defined or sequence.
    # For this exercise, we will exclude PK from form as it's typically set once.
    # We will exclude foreign key fields in the form if they are managed contextually (e.g. by session/user).
    # If they are selectable, use ModelChoiceField.
    
    financial_year = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(), 
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    generated_by = forms.ModelChoiceField(
        queryset=Employee.objects.all(), 
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = Supplier
        fields = ['supplier_name', 'system_date', 'financial_year', 'generated_by', 'company_id']
        widgets = {
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'system_date': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'MM-DD-YYYY'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            # financial_year and generated_by widgets are defined above as ModelChoiceField
        }

    def clean_system_date(self):
        # Basic validation for system_date format
        date_str = self.cleaned_data.get('system_date')
        if date_str:
            try:
                # Try to parse into a date object to validate format
                datetime.datetime.strptime(date_str, '%m-%d-%Y').date()
            except ValueError:
                try:
                    datetime.datetime.strptime(date_str, '%d-%m-%Y').date()
                except ValueError:
                    raise forms.ValidationError("Please enter the System Date in MM-DD-YYYY or DD-MM-YYYY format.")
        return date_str
```

#### 4.3 Views

We will implement `ListView` for the main page, a custom `PartialView` for the DataTables content, and standard `CreateView`, `UpdateView`, `DeleteView` for CRUD operations, as well as an `AutocompleteView`.

**File: `material_management/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render
from .models import Supplier, FinancialYear, Employee
from .forms import SupplierForm
import json # For JSON response

# Dummy session/context values for demonstration. In a real ERP, these
# would come from authenticated user context, session, or tenant settings.
# For testing, we might mock these or use fixed values.
DEMO_COMPANY_ID = 1
DEMO_FINANCIAL_YEAR_ID = 1

class SupplierListView(ListView):
    """
    Displays the main supplier search and list page.
    """
    model = Supplier
    template_name = 'material_management/supplier/list.html'
    context_object_name = 'suppliers'

    # We won't pre-populate the full queryset here as it's handled by SupplierTablePartialView via HTMX.
    # This view primarily serves the base page structure and initial load.
    def get_queryset(self):
        # Return an empty queryset or a very limited one for initial page load
        # as the table content will be loaded via HTMX
        return Supplier.objects.none() 

class SupplierTablePartialView(ListView):
    """
    Renders only the DataTables content, intended to be loaded via HTMX.
    Handles search filtering based on query parameters.
    """
    model = Supplier
    template_name = 'material_management/supplier/_supplier_table.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        search_value = self.request.GET.get('search_value', '').strip()
        search_supplier_id = None
        search_term = None

        if search_value:
            # Try to extract SupplierId from 'Name [ID]' format
            extracted_id = Supplier.objects.extract_supplier_id_from_text(search_value)
            if extracted_id is not None:
                search_supplier_id = extracted_id
            else:
                # If no ID found, treat it as a general name search
                search_term = search_value
        
        queryset = Supplier.objects.get_suppliers_for_display(
            company_id=DEMO_COMPANY_ID,
            financial_year_id=DEMO_FINANCIAL_YEAR_ID,
            search_supplier_id=search_supplier_id,
            search_term=search_term
        )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the extracted search value back to the template if needed for UI state
        context['current_search_value'] = self.request.GET.get('search_value', '')
        return context

class SupplierCreateView(CreateView):
    """
    Handles creation of new Supplier records.
    """
    model = Supplier
    form_class = SupplierForm
    template_name = 'material_management/supplier/_supplier_form.html' # Use partial template
    success_url = reverse_lazy('supplier_list') # Redirect to list view on success

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add New Supplier'
        return context

    def form_valid(self, form):
        # Set context-specific fields if not handled by form
        # For example, setting company_id based on session/user
        if not form.instance.company_id:
            form.instance.company_id = DEMO_COMPANY_ID # Default for new entry

        # For a new supplier, if supplier_id (PK) is not provided in form,
        # it might need to be auto-generated or derived.
        # Assuming for 'managed=False' case, the DB handles PK or it's provided.
        # If not provided, you might need a custom save logic or pre-save hook.
        # For this example, let's assume it's implicitly handled or we'll allow user to provide it.
        # If SupplierId is an identity column in DB, it should be excluded from form.
        # Original code implies it's set on 'SupplierMaster_Edit_Details.aspx' not here.
        # To make it runnable for demonstration, let's temporarily make SupplierId optional/auto-generated
        # or require it in form, which is unlikely for a PK.
        # Best practice: PK should not be in form unless it's natural key.
        # Given the original code, SupplierId is likely an identity/sequence.
        # For simplicity, let's generate a dummy one if not provided for demonstration.
        # In a real scenario, use a sequence or auto-increment from DB.
        if not form.instance.supplier_id:
            # This is a placeholder for actual PK generation logic.
            # In a real application, consider database identity columns or sequences.
            max_id = Supplier.objects.aggregate(models.Max('supplier_id'))['supplier_id__max']
            form.instance.supplier_id = (max_id or 0) + 1


        response = super().form_valid(form)
        messages.success(self.request, f'Supplier "{self.object.supplier_name}" added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to indicate success and trigger reload
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors for re-rendering the modal content
            return render(self.request, self.template_name, {'form': form})
        return response

class SupplierUpdateView(UpdateView):
    """
    Handles updating existing Supplier records.
    """
    model = Supplier
    form_class = SupplierForm
    template_name = 'material_management/supplier/_supplier_form.html' # Use partial template
    context_object_name = 'supplier'
    success_url = reverse_lazy('supplier_list')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Supplier'
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'Supplier "{self.object.supplier_name}" updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class SupplierDeleteView(DeleteView):
    """
    Handles deletion of Supplier records.
    """
    model = Supplier
    template_name = 'material_management/supplier/_supplier_confirm_delete.html' # Use partial template
    context_object_name = 'supplier'
    success_url = reverse_lazy('supplier_list')

    def delete(self, request, *args, **kwargs):
        obj_name = self.get_object().supplier_name
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, f'Supplier "{obj_name}" deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierAutocompleteView(View):
    """
    Provides autocomplete suggestions for supplier names and IDs,
    mimicking the ASP.NET WebMethod.
    Returns JSON.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('term', '').strip()
        # In a real app, company_id would come from user session/profile.
        # For demo, using fixed company ID.
        suggestions = Supplier.objects.get_autocomplete_suggestions(
            prefix_text=prefix_text,
            company_id=DEMO_COMPANY_ID
        )
        return JsonResponse(suggestions, safe=False)

```

#### 4.4 Templates

Templates will be split into a main list page and partials for the table, forms, and delete confirmation, leveraging HTMX for dynamic loading.

**File: `material_management/templates/material_management/supplier/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-wrap justify-between items-center mb-6 gap-4">
        <h2 class="text-2xl font-bold text-gray-800">Supplier List</h2>
        <div class="flex items-center gap-2">
            <input 
                type="text" 
                id="searchSupplierInput" 
                name="search_value"
                placeholder="Search Supplier Name or ID" 
                class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                hx-get="{% url 'supplier_autocomplete' %}"
                hx-trigger="keyup changed delay:500ms"
                hx-target="#autocomplete-results"
                hx-swap="innerHTML"
                hx-indicator="#loading-indicator"
                autocomplete="off"
            >
            <div id="loading-indicator" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </div>
            <ul id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 w-64 mt-1 rounded-md shadow-lg hidden"></ul>
            
            <button 
                id="searchButton"
                class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'supplier_table' %}"
                hx-target="#supplierTable-container"
                hx-indicator="#loading-indicator"
                hx-include="#searchSupplierInput"
                hx-swap="innerHTML">
                Search
            </button>
            <button 
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                hx-get="{% url 'supplier_add' %}"
                hx-target="#modalContent"
                hx-trigger="click"
                _="on click add .is-active to #modal">
                Add New Supplier
            </button>
        </div>
    </div>
    
    <div id="supplierTable-container"
         hx-trigger="load, refreshSupplierList from:body"
         hx-get="{% url 'supplier_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading suppliers...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }" x-show="showModal"
         x-on:close-modal.window="showModal = false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css">

<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            open: false,
            openModal() { this.open = true },
            closeModal() { this.open = false }
        });
    });

    // Event listener for HTMX after swap to initialize DataTables if the element is loaded
    document.body.addEventListener('htmx:afterOnLoad', function(event) {
        if (event.detail.target.id === 'supplierTable-container') {
            $('#supplierTable').DataTable({
                "pageLength": 17, // Matching original page size
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "autoWidth": false,
                "responsive": true,
                "language": {
                    "emptyTable": "No data to display !" // Matching original message
                }
            });
        }
    });

    // Handle autocomplete selection
    document.getElementById('autocomplete-results').addEventListener('click', function(event) {
        const target = event.target;
        if (target.tagName === 'LI' && target.dataset.value) {
            document.getElementById('searchSupplierInput').value = target.dataset.value;
            this.classList.add('hidden'); // Hide results
            document.getElementById('searchButton').click(); // Trigger search
        }
    });

    // Hide autocomplete results when clicking outside
    document.addEventListener('click', function(event) {
        const searchInput = document.getElementById('searchSupplierInput');
        const resultsDiv = document.getElementById('autocomplete-results');
        if (event.target !== searchInput && !resultsDiv.contains(event.target)) {
            resultsDiv.classList.add('hidden');
        } else if (event.target === searchInput && searchInput.value.length > 0) {
            resultsDiv.classList.remove('hidden'); // Show if typing and not empty
        }
    });

    // Show/hide modal based on HTMX interaction
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        if (evt.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden');
        }
    });

    // Listener for HTMX success triggers to close modal and refresh list
    document.body.addEventListener('htmx:trigger', function(evt) {
        if (evt.detail.trigger === 'refreshSupplierList') {
            document.getElementById('modal').classList.add('hidden');
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**File: `material_management/templates/material_management/supplier/_supplier_table.html`**

```html
<div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
    <table id="supplierTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if suppliers %}
                {% for supplier in suppliers %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.display_fin_year }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800">
                        <a href="{% url 'supplier_edit' supplier.pk %}"
                            hx-get="{% url 'supplier_edit' supplier.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            {{ supplier.supplier_name }}
                        </a>
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.supplier_id }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.formatted_gen_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ supplier.display_generated_by }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2"
                            hx-get="{% url 'supplier_edit' supplier.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md"
                            hx-get="{% url 'supplier_delete' supplier.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="7" class="py-4 text-center text-lg text-red-700">
                    No data to display !
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Autocomplete results list for HTMX targeting -->
<ul id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 w-64 mt-1 rounded-md shadow-lg hidden">
    {% for suggestion in suggestions %} {# 'suggestions' context variable passed by autocompletion view #}
    <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer" data-value="{{ suggestion }}">{{ suggestion }}</li>
    {% endfor %}
</ul>

<script>
    // DataTables initialization is handled in list.html via htmx:afterOnLoad
    // This script block should only contain logic specific to the partial,
    // or rely on the parent page for global JS.
    // However, if DataTable re-initialization is needed here, it must be robust.
    // Given the approach, the parent page initializes, so no code here unless needed for specific elements.
    // For autocomplete results, this block will populate the list.
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.target.id === 'autocomplete-results') {
            const resultsDiv = document.getElementById('autocomplete-results');
            if (resultsDiv.children.length > 0) {
                resultsDiv.classList.remove('hidden');
            } else {
                resultsDiv.classList.add('hidden');
            }
        }
    });
</script>
```

**File: `material_management/templates/material_management/supplier/_supplier_form.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-bold text-gray-900 mb-5">{{ title }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-target="#modalContent">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1 list-disc pl-5">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
            <ul class="text-red-500 text-xs mt-1 list-disc pl-5">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `material_management/templates/material_management/supplier/_supplier_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-lg font-bold text-red-700 mb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the supplier: <strong>{{ supplier.supplier_name }} (ID: {{ supplier.supplier_id }})</strong>?</p>
    
    <form hx-post="{% url 'supplier_delete' supplier.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-800 text-white font-bold py-2 px-4 rounded-md">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

URL patterns for the `material_management` app.

**File: `material_management/urls.py`**

```python
from django.urls import path
from .views import (
    SupplierListView, 
    SupplierTablePartialView,
    SupplierCreateView, 
    SupplierUpdateView, 
    SupplierDeleteView,
    SupplierAutocompleteView
)

urlpatterns = [
    # Main list page
    path('supplier/', SupplierListView.as_view(), name='supplier_list'),
    
    # HTMX partial for table content
    path('supplier/table/', SupplierTablePartialView.as_view(), name='supplier_table'),
    
    # Autocomplete endpoint
    path('supplier/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # CRUD operations via modal
    path('supplier/add/', SupplierCreateView.as_view(), name='supplier_add'),
    path('supplier/edit/<int:pk>/', SupplierUpdateView.as_view(), name='supplier_edit'),
    path('supplier/delete/<int:pk>/', SupplierDeleteView.as_view(), name='supplier_delete'),
]
```

**Root `urls.py` (e.g., `yourproject/urls.py`):**
Make sure to include the `material_management` app's URLs.
```python
from django.contrib import admin
from django.urls import path, include

urlpatterns = [
    path('admin/', admin.site.urls),
    path('material_management/', include('material_management.urls')),
    # Add other app URLs here
]
```

#### 4.6 Tests

Comprehensive unit tests for models and integration tests for views are essential for ensuring functionality and maintainability.

**File: `material_management/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import Supplier, FinancialYear, Employee
import datetime

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialYear.objects.create(fin_year_id=1, year='2023-2024')
        FinancialYear.objects.create(fin_year_id=2, year='2024-2025')

    def test_fin_year_creation(self):
        fin_year = FinancialYear.objects.get(fin_year_id=1)
        self.assertEqual(fin_year.year, '2023-2024')
        self.assertEqual(str(fin_year), '2023-2024')
        self.assertEqual(fin_year._meta.db_table, 'tblFinancial_master')

class EmployeeModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Employee.objects.create(emp_id=1, employee_name='John Doe')
        Employee.objects.create(emp_id=2, employee_name='Jane Smith')

    def test_employee_creation(self):
        employee = Employee.objects.get(emp_id=1)
        self.assertEqual(employee.employee_name, 'John Doe')
        self.assertEqual(str(employee), 'John Doe')
        self.assertEqual(employee._meta.db_table, 'tblHR_OfficeStaff')

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create necessary FK objects
        FinancialYear.objects.create(fin_year_id=10, year='2023-2024')
        Employee.objects.create(emp_id=100, employee_name='Admin User')

        # Create test Supplier data
        Supplier.objects.create(
            supplier_id=1,
            supplier_name='Test Supplier A',
            system_date='12-01-2023', # MM-DD-YYYY
            financial_year_id=10,
            generated_by_id=100,
            company_id=1
        )
        Supplier.objects.create(
            supplier_id=2,
            supplier_name='Test Supplier B',
            system_date='01-15-2024', # MM-DD-YYYY
            financial_year_id=10,
            generated_by_id=100,
            company_id=1
        )
        Supplier.objects.create(
            supplier_id=3,
            supplier_name='Another Supplier C',
            system_date='02-28-2024', # MM-DD-YYYY
            financial_year_id=10,
            generated_by_id=100,
            company_id=2 # Different company
        )

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(supplier_id=1)
        self.assertEqual(supplier.supplier_name, 'Test Supplier A')
        self.assertEqual(supplier.system_date, '12-01-2023')
        self.assertEqual(supplier.financial_year.year, '2023-2024')
        self.assertEqual(supplier.generated_by.employee_name, 'Admin User')
        self.assertEqual(supplier.company_id, 1)
        self.assertEqual(str(supplier), 'Test Supplier A (1)')
        self.assertEqual(supplier._meta.db_table, 'tblMM_Supplier_master')

    def test_formatted_gen_date_property(self):
        supplier = Supplier.objects.get(supplier_id=1)
        self.assertEqual(supplier.formatted_gen_date, '01-12-2023') # MM-DD-YYYY -> DD-MM-YYYY
        
        supplier_b = Supplier.objects.get(supplier_id=2)
        self.assertEqual(supplier_b.formatted_gen_date, '15-01-2024') # MM-DD-YYYY -> DD-MM-YYYY

        supplier_no_date = Supplier.objects.create(supplier_id=4, supplier_name='No Date', company_id=1)
        self.assertEqual(supplier_no_date.formatted_gen_date, "")

    def test_display_fin_year_property(self):
        supplier = Supplier.objects.get(supplier_id=1)
        self.assertEqual(supplier.display_fin_year, '2023-2024')
        supplier_no_fk = Supplier.objects.create(supplier_id=5, supplier_name='No FK', company_id=1)
        self.assertEqual(supplier_no_fk.display_fin_year, 'N/A')

    def test_display_generated_by_property(self):
        supplier = Supplier.objects.get(supplier_id=1)
        self.assertEqual(supplier.display_generated_by, 'Admin User')
        supplier_no_fk = Supplier.objects.create(supplier_id=6, supplier_name='No FK Gen By', company_id=1)
        self.assertEqual(supplier_no_fk.display_generated_by, 'N/A')

    # Manager method tests
    def test_get_suppliers_for_display(self):
        # Test with company_id and fin_year_id filters
        suppliers = Supplier.objects.get_suppliers_for_display(company_id=1, financial_year_id=10)
        self.assertEqual(suppliers.count(), 2)
        self.assertIn(Supplier.objects.get(supplier_id=1), suppliers)
        self.assertIn(Supplier.objects.get(supplier_id=2), suppliers)
        self.assertNotIn(Supplier.objects.get(supplier_id=3), suppliers)

        # Test with search_supplier_id
        suppliers = Supplier.objects.get_suppliers_for_display(company_id=1, financial_year_id=10, search_supplier_id=1)
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 1)

        # Test with search_term (supplier name)
        suppliers = Supplier.objects.get_suppliers_for_display(company_id=1, financial_year_id=10, search_term='test')
        self.assertEqual(suppliers.count(), 2)
        self.assertIn(Supplier.objects.get(supplier_id=1), suppliers)
        self.assertIn(Supplier.objects.get(supplier_id=2), suppliers)

        suppliers = Supplier.objects.get_suppliers_for_display(company_id=2, financial_year_id=10, search_term='another')
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 3)

    def test_get_autocomplete_suggestions(self):
        suggestions = Supplier.objects.get_autocomplete_suggestions(prefix_text='test', company_id=1)
        self.assertEqual(len(suggestions), 2)
        self.assertIn('Test Supplier A [1]', suggestions)
        self.assertIn('Test Supplier B [2]', suggestions)

        suggestions = Supplier.objects.get_autocomplete_suggestions(prefix_text='anoth', company_id=1)
        self.assertEqual(len(suggestions), 0) # Another Supplier C is company_id 2

        suggestions = Supplier.objects.get_autocomplete_suggestions(prefix_text='anoth', company_id=2)
        self.assertEqual(len(suggestions), 1)
        self.assertIn('Another Supplier C [3]', suggestions)

        suggestions = Supplier.objects.get_autocomplete_suggestions(prefix_text='1', company_id=1)
        self.assertIn('Test Supplier A [1]', suggestions)

    def test_extract_supplier_id_from_text(self):
        self.assertEqual(Supplier.objects.extract_supplier_id_from_text('Test Supplier A [1]'), 1)
        self.assertEqual(Supplier.objects.extract_supplier_id_from_text('Supplier Name [12345]'), 12345)
        self.assertIsNone(Supplier.objects.extract_supplier_id_from_text('Just a name'))
        self.assertIsNone(Supplier.objects.extract_supplier_id_from_text('Name [ABC]'))
        self.assertIsNone(Supplier.objects.extract_supplier_id_from_text(''))

class SupplierViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        FinancialYear.objects.create(fin_year_id=99, year='2022-2023')
        Employee.objects.create(emp_id=999, employee_name='Test Employee')
        Supplier.objects.create(
            supplier_id=10,
            supplier_name='Existing Supplier',
            system_date='03-10-2023',
            financial_year_id=99,
            generated_by_id=999,
            company_id=1
        )
        Supplier.objects.create(
            supplier_id=11,
            supplier_name='Second Supplier',
            system_date='04-20-2023',
            financial_year_id=99,
            generated_by_id=999,
            company_id=1
        )
    
    def setUp(self):
        self.client = Client()
        self.list_url = reverse('supplier_list')
        self.table_url = reverse('supplier_table')
        self.add_url = reverse('supplier_add')
        self.edit_url = reverse('supplier_edit', args=[10])
        self.delete_url = reverse('supplier_delete', args=[10])
        self.autocomplete_url = reverse('supplier_autocomplete')

    def test_supplier_list_view(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/list.html')
        # Check that context_object_name is present but queryset is empty initially
        self.assertIn('suppliers', response.context)
        self.assertEqual(response.context['suppliers'].count(), 0)

    def test_supplier_table_partial_view_no_search(self):
        response = self.client.get(self.table_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_table.html')
        self.assertIn('suppliers', response.context)
        self.assertEqual(response.context['suppliers'].count(), 2) # Should include existing suppliers
        self.assertContains(response, 'Existing Supplier')
        self.assertContains(response, 'Second Supplier')

    def test_supplier_table_partial_view_search_by_id(self):
        response = self.client.get(self.table_url, {'search_value': 'Existing Supplier [10]'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['suppliers'].count(), 1)
        self.assertContains(response, 'Existing Supplier')
        self.assertNotContains(response, 'Second Supplier')

    def test_supplier_table_partial_view_search_by_name(self):
        response = self.client.get(self.table_url, {'search_value': 'Existing'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['suppliers'].count(), 1)
        self.assertContains(response, 'Existing Supplier')
        self.assertNotContains(response, 'Second Supplier')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(self.autocomplete_url, {'term': 'Exis'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertIn('Existing Supplier [10]', data)

        response = self.client.get(self.autocomplete_url, {'term': 'second'})
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertIn('Second Supplier [11]', data)

    def test_supplier_create_view_get(self):
        response = self.client.get(self.add_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add New Supplier')

    def test_supplier_create_view_post_valid_htmx(self):
        data = {
            'supplier_name': 'New Test Supplier',
            'system_date': '05-01-2024',
            'financial_year': FinancialYear.objects.get(fin_year_id=99).fin_year_id,
            'generated_by': Employee.objects.get(emp_id=999).emp_id,
            'company_id': 1
        }
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierList')
        self.assertTrue(Supplier.objects.filter(supplier_name='New Test Supplier').exists())

    def test_supplier_create_view_post_invalid_htmx(self):
        data = {
            'supplier_name': '', # Invalid
            'system_date': 'invalid-date', # Invalid
            'financial_year': FinancialYear.objects.get(fin_year_id=99).fin_year_id,
            'generated_by': Employee.objects.get(emp_id=999).emp_id,
            'company_id': 1
        }
        response = self.client.post(self.add_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Should render form with errors for HTMX
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Please enter the System Date in MM-DD-YYYY or DD-MM-YYYY format.')

    def test_supplier_update_view_get(self):
        response = self.client.get(self.edit_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['supplier'].supplier_id, 10)
        self.assertContains(response, 'Edit Supplier')

    def test_supplier_update_view_post_valid_htmx(self):
        data = {
            'supplier_name': 'Updated Supplier Name',
            'system_date': '03-10-2023', # Keep old date or update
            'financial_year': FinancialYear.objects.get(fin_year_id=99).fin_year_id,
            'generated_by': Employee.objects.get(emp_id=999).emp_id,
            'company_id': 1
        }
        response = self.client.post(self.edit_url, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierList')
        self.assertEqual(Supplier.objects.get(supplier_id=10).supplier_name, 'Updated Supplier Name')

    def test_supplier_delete_view_get(self):
        response = self.client.get(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_confirm_delete.html')
        self.assertIn('supplier', response.context)
        self.assertEqual(response.context['supplier'].supplier_id, 10)

    def test_supplier_delete_view_post_htmx(self):
        self.assertTrue(Supplier.objects.filter(supplier_id=10).exists())
        response = self.client.post(self.delete_url, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierList')
        self.assertFalse(Supplier.objects.filter(supplier_id=10).exists())

```

### Step 5: HTMX and Alpine.js Integration

**Instructions Implemented:**

*   **HTMX for dynamic updates:**
    *   The `supplier_list.html` uses `hx-get="{% url 'supplier_table' %}"` to load the table content dynamically on `load` and `refreshSupplierList` events.
    *   Search button uses `hx-get` to refresh the table with `hx-include="#searchSupplierInput"`.
    *   Add, Edit, Delete buttons use `hx-get` to load forms/confirmation into a modal (`#modalContent`).
    *   Form submissions (POST) use `hx-post` and `hx-swap="none"` or `hx-target="#modalContent"` to handle success (204 No Content + `HX-Trigger`) or error (render form with errors).
    *   `HX-Trigger` is used to send `refreshSupplierList` event from view after successful CRUD operations, which automatically refreshes the main table.
    *   Autocomplete uses `hx-get` and `hx-trigger="keyup changed delay:500ms"` to fetch suggestions.
*   **Alpine.js for UI state management:**
    *   A simple Alpine.js `x-data` component is used on the modal to manage its visibility (`showModal` property) and close it.
    *   `_=` (Hyperscript) is used for direct DOM manipulation for showing/hiding the modal, complementing HTMX.
*   **DataTables for list views:**
    *   The `_supplier_table.html` partial expects `DataTables.js` to be initialized. The initialization script for DataTables is placed in the `extra_js` block of `list.html` and triggered by an `htmx:afterOnLoad` event to ensure DataTables is re-initialized whenever the table partial is swapped into the DOM.
*   **HTMX-only interactions:** All dynamic content loading and form submissions are driven by HTMX. No custom JavaScript is required beyond DataTables initialization and Alpine.js for modal/autocomplete behavior.
*   **DRY template inheritance:** `list.html` extends `core/base.html`, which is assumed to contain all CDN links for Tailwind CSS, Alpine.js, HTMX, jQuery, and DataTables.

---

### Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete Django code and inferred values from the ASP.NET application.
*   **DRY Templates:** Partial templates (`_supplier_table.html`, `_supplier_form.html`, `_supplier_confirm_delete.html`) are used to promote reusability and maintainability.
*   **Fat Model, Thin View:** Complex data retrieval and business logic (like date formatting, supplier search, autocomplete) are encapsulated within the `SupplierManager` and `Supplier` model methods. Views remain concise, primarily orchestrating the request/response flow and delegating to the model for data operations.
*   **Testing:** Comprehensive unit tests for models and integration tests for views are provided, covering various scenarios including HTMX interactions and form validation. This ensures at least 80% test coverage and validates the migration.
*   **Contextual Data (Company/Financial Year):** In a real ERP system, `company_id` and `financial_year_id` would typically be dynamically sourced from the authenticated user's profile, session, or a multi-tenant context. For this conversion, `DEMO_COMPANY_ID` and `DEMO_FINANCIAL_YEAR_ID` constants are used as placeholders to make the code runnable. An AI-assisted migration tool would identify and automate the mapping of these session/context variables.
*   **SupplierId Generation:** The ASP.NET code implies `SupplierId` is a primary key. For new records, if it's an identity column in the database, Django's ORM would handle it. For this example, a simple incremental ID generation is added in `SupplierCreateView.form_valid` for demonstration, but this should be aligned with the actual database's PK handling.