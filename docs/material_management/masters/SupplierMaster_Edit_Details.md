## ASP.NET to Django Conversion Script: Supplier Master Edit

This document outlines a detailed modernization plan to transition the provided ASP.NET Supplier Master Edit functionality to a modern Django-based solution. Our approach prioritizes automation, emphasizes a "Fat Model, Thin View" architecture, and leverages HTMX + Alpine.js for highly interactive, yet compact, frontend experiences.

### Key Business Benefits of Django Modernization:
*   **Reduced Development Costs:** By adopting Django's "batteries included" philosophy and promoting code reusability (DRY), future feature development and maintenance will be significantly faster and less expensive.
*   **Improved User Experience:** HTMX and Alpine.js provide a snappy, modern web experience without complex JavaScript frameworks, leading to faster page loads and more intuitive interactions.
*   **Enhanced Maintainability:** Django's structured approach, coupled with our "Fat Model, Thin View" strategy, ensures business logic is centralized and easily understood, reducing the risk of bugs and simplifying future updates.
*   **Scalability & Performance:** Django is a highly performant framework, capable of handling complex enterprise-level applications, ensuring your system can grow with your business needs.
*   **Automation Readiness:** The modular and well-defined structure of Django lends itself perfectly to AI-assisted code generation and automated testing, further accelerating development and deployment cycles.
*   **Stronger Security:** Django includes robust built-in security features, safeguarding your sensitive supplier data more effectively than legacy systems.

---

### Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts primarily with `tblMM_Supplier_master` and several lookup tables.

**Main Table:** `tblMM_Supplier_master`

**Inferred Columns from `tblMM_Supplier_master`:**

*   `SupplierId` (Primary Key, assumed `INT` or `BIGINT`)
*   `CompId` (`INT`, Company ID)
*   `SysDate` (`DATETIME`, for audit/creation date)
*   `SysTime` (`VARCHAR`, for audit/creation time, but should be `DATETIME` combined with `SysDate`)
*   `SessionId` (`VARCHAR`, user session ID/username)
*   `SupplierName` (`NVARCHAR`)
*   `ScopeOfSupply` (`NVARCHAR`)
*   `RegdAddress` (`NVARCHAR`)
*   `RegdCountry` (`INT`, Foreign Key to `Country` table)
*   `RegdState` (`INT`, Foreign Key to `State` table)
*   `RegdCity` (`INT`, Foreign Key to `City` table)
*   `RegdPinNo` (`NVARCHAR`)
*   `RegdContactNo` (`NVARCHAR`)
*   `RegdFaxNo` (`NVARCHAR`)
*   `WorkAddress` (`NVARCHAR`)
*   `WorkCountry` (`INT`, Foreign Key to `Country` table)
*   `WorkState` (`INT`, Foreign Key to `State` table)
*   `WorkCity` (`INT`, Foreign Key to `City` table)
*   `WorkPinNo` (`NVARCHAR`)
*   `WorkContactNo` (`NVARCHAR`)
*   `WorkFaxNo` (`NVARCHAR`)
*   `MaterialDelAddress` (`NVARCHAR`)
*   `MaterialDelCountry` (`INT`, Foreign Key to `Country` table)
*   `MaterialDelState` (`INT`, Foreign Key to `State` table)
*   `MaterialDelCity` (`INT`, Foreign Key to `City` table)
*   `MaterialDelPinNo` (`NVARCHAR`)
*   `MaterialDelContactNo` (`NVARCHAR`)
*   `MaterialDelFaxNo` (`NVARCHAR`)
*   `ContactPerson` (`NVARCHAR`)
*   `JuridictionCode` (`NVARCHAR`)
*   `Commissionurate` (`NVARCHAR`)
*   `TinVatNo` (`NVARCHAR`)
*   `Email` (`NVARCHAR`)
*   `EccNo` (`NVARCHAR`)
*   `Divn` (`NVARCHAR`)
*   `TinCstNo` (`NVARCHAR`)
*   `ContactNo` (`NVARCHAR`)
*   `Range` (`NVARCHAR`)
*   `PanNo` (`NVARCHAR`)
*   `TDSCode` (`NVARCHAR`)
*   `Remark` (`NVARCHAR`)
*   `ModVatApplicable` (`INT`, 0 or 1, representing `BIT`/`BOOLEAN`)
*   `ModVatInvoice` (`INT`, 0 or 1, representing `BIT`/`BOOLEAN`)
*   `BankAccNo` (`NVARCHAR`)
*   `BankName` (`NVARCHAR`)
*   `BankBranch` (`NVARCHAR`)
*   `BankAddress` (`NVARCHAR`)
*   `BankAccType` (`NVARCHAR`)
*   `BusinessType` (`NVARCHAR`, comma-separated IDs, implying Many-to-Many)
*   `BusinessNature` (`NVARCHAR`, comma-separated IDs, implying Many-to-Many)
*   `ServiceCoverage` (`INT`, Foreign Key to `tblMM_Supplier_ServiceCoverage`)
*   `PF` (`INT`, Foreign Key to `tblPacking_Master`)
*   `ExST` (`INT`, Foreign Key to `tblExciseser_Master`)
*   `VAT` (`INT`, Foreign Key to `tblVAT_Master`)

**Lookup Tables (Inferred):**

*   `tblPacking_Master` (columns: `Id`, `Terms`)
*   `tblExciseser_Master` (columns: `Id`, `Terms`)
*   `tblVAT_Master` (columns: `Id`, `Terms`)
*   `tblMM_Supplier_ServiceCoverage` (columns: `Id`, `Type`)
*   `tblMM_Supplier_BusinessType` (columns: `Id`, `Type`)
*   `tblMM_Supplier_BusinessNature` (columns: `Id`, `Nature`)
*   `Country` (columns: `CId`, `CName`) - inferred from `fun.dropdownCountry`
*   `State` (columns: `SId`, `SName`, `CId`) - inferred from `fun.dropdownState`
*   `City` (columns: `CityId`, `CityName`, `SId`) - inferred from `fun.dropdownCity`

### Step 2: Identify Backend Functionality

The ASP.NET code primarily implements an **Update** (edit) operation for a supplier record.
*   **Read:** Data is read from `tblMM_Supplier_master` on `Page_Load` using `SELECT *` based on `SupplierId` from the query string and `CompId` from the session. This populates all form fields.
*   **Update:** On `Update_Click`, all form data is gathered, validated, and an `UPDATE` SQL command is constructed and executed against `tblMM_Supplier_master`.
*   **Validation:**
    *   Required Field Validators (`RequiredFieldValidator`).
    *   Email format validator (`RegularExpressionValidator`).
    *   Custom business logic: `SupplierName` must start with the same character as `SupplierId` (CustCode).
    *   Comprehensive `if` condition checking all required fields.
*   **Cascading Dropdowns:** `DDListEditRegdCountry_SelectedIndexChanged`, `DDListEditRegdState_SelectedIndexChanged`, etc., dynamically load dependent dropdowns (State based on Country, City based on State).

### Step 3: Infer UI Components

The ASP.NET page uses standard Web Forms controls:
*   **Text Boxes (`asp:TextBox`):** For single-line and multi-line text input (e.g., `txtSupplierName`, `txtEditRegdAdd`, `txtEditEmail`). These will map to Django `forms.CharField` with `TextInput` or `Textarea` widgets.
*   **Dropdown Lists (`asp:DropDownList`):** For single-selection from master data (e.g., Countries, States, Cities, Service Coverage, P&F). These will map to Django `forms.ModelChoiceField` or `forms.ChoiceField` with `Select` widgets.
*   **Radio Buttons (`asp:RadioButton`):** For binary choices (e.g., `ModVatApplicable`, `ModVatInvoice`). These map to Django `forms.BooleanField` or `forms.ChoiceField` with `RadioSelect` widgets.
*   **Check Box Lists (`asp:CheckBoxList`):** For multi-selection from master data (e.g., `CBLBusinessNature`, `CBLBusinessType`). These map to Django `forms.ModelMultipleChoiceField` or `forms.MultipleChoiceField` with `CheckboxSelectMultiple` widgets.
*   **Buttons (`asp:Button`):** For submitting (`Update`) or cancelling (`Button1`). These will map to standard HTML `<button>` elements, with HTMX attributes for dynamic submission.
*   **Validators (`asp:RequiredFieldValidator`, `asp:RegularExpressionValidator`):** These handle client-side and server-side validation. Django forms have built-in validation mechanisms and custom `clean` methods.
*   **Table Layout:** The page uses extensive `<table>` elements for layout. This will be replaced by modern CSS frameworks (Tailwind CSS) for responsive layouts using `div`s.

### Step 4: Generate Django Code

We will create a new Django application, for example, `material_management`, to house this functionality.

#### 4.1 Models (`material_management/models.py`)

First, we define the lookup tables and then the main `Supplier` model. All models will be `managed = False` and map to their existing `db_table` names to integrate with the legacy database.

```python
from django.db import models
from django.utils import timezone
import datetime

# Models for lookup tables based on SqlDataSource and CheckBoxList data sources

class Company(models.Model):
    # Assuming 'CompId' in Supplier relates to a Company table
    # This is a placeholder, actual fields depend on the 'Company' table structure
    compid = models.IntegerField(db_column='CompId', primary_key=True)
    compname = models.CharField(db_column='CompName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming a table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.compname or f"Company {self.compid}"

class PackingTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.terms

class ExciseServiceTaxTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Tax Term'
        verbose_name_plural = 'Excise/Service Tax Terms'

    def __str__(self):
        return self.terms

class VATTaxTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'

    def __str__(self):
        return self.terms

class ServiceCoverageType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_ServiceCoverage'
        verbose_name = 'Service Coverage Type'
        verbose_name_plural = 'Service Coverage Types'

    def __str__(self):
        return self.type

class BusinessType(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    type = models.CharField(db_column='Type', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_BusinessType'
        verbose_name = 'Business Type'
        verbose_name_plural = 'Business Types'

    def __str__(self):
        return self.type

class BusinessNature(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    nature = models.CharField(db_column='Nature', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_BusinessNature'
        verbose_name = 'Business Nature'
        verbose_name_plural = 'Business Natures'

    def __str__(self):
        return self.nature

# Models for Country, State, City (assuming a common structure)
class Country(models.Model):
    cid = models.IntegerField(db_column='CId', primary_key=True)
    cname = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblCountry' # Inferred table name
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.cname

class State(models.Model):
    sid = models.IntegerField(db_column='SId', primary_key=True)
    sname = models.CharField(db_column='SName', max_length=255)
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CId')

    class Meta:
        managed = False
        db_table = 'tblState' # Inferred table name
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.sname

class City(models.Model):
    cityid = models.IntegerField(db_column='CityId', primary_key=True)
    cityname = models.CharField(db_column='CityName', max_length=255)
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='SId')

    class Meta:
        managed = False
        db_table = 'tblCity' # Inferred table name
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.cityname

# Main Supplier Model
class Supplier(models.Model):
    supplierid = models.CharField(db_column='SupplierId', primary_key=True, max_length=50) # Changed to CharField as per usage 'CustCode.Substring(0,1)'
    compid = models.IntegerField(db_column='CompId') # Placeholder, could be FK to Company
    sysdate = models.DateField(db_column='SysDate', auto_now=True) # auto_now updates on each save
    systime = models.TimeField(db_column='SysTime', auto_now=True) # auto_now updates on each save
    sessionid = models.CharField(db_column='SessionId', max_length=255, blank=True, null=True)
    suppliername = models.CharField(db_column='SupplierName', max_length=255)
    scopeofsupply = models.TextField(db_column='ScopeOfSupply')

    # Registered Office Address
    regdaddress = models.TextField(db_column='RegdAddress')
    regdcountry = models.ForeignKey(Country, models.DO_NOTHING, db_column='RegdCountry', related_name='regd_suppliers')
    regdstate = models.ForeignKey(State, models.DO_NOTHING, db_column='RegdState', related_name='regd_suppliers')
    regdcity = models.ForeignKey(City, models.DO_NOTHING, db_column='RegdCity', related_name='regd_suppliers')
    regdpinno = models.CharField(db_column='RegdPinNo', max_length=50)
    regdcontactno = models.CharField(db_column='RegdContactNo', max_length=50)
    regdfaxno = models.CharField(db_column='RegdFaxNo', max_length=50)

    # Works/Factory Address
    workaddress = models.TextField(db_column='WorkAddress')
    workcountry = models.ForeignKey(Country, models.DO_NOTHING, db_column='WorkCountry', related_name='work_suppliers')
    workstate = models.ForeignKey(State, models.DO_NOTHING, db_column='WorkState', related_name='work_suppliers')
    workcity = models.ForeignKey(City, models.DO_NOTHING, db_column='WorkCity', related_name='work_suppliers')
    workpinno = models.CharField(db_column='WorkPinNo', max_length=50)
    workcontactno = models.CharField(db_column='WorkContactNo', max_length=50)
    workfaxno = models.CharField(db_column='WorkFaxNo', max_length=50)

    # Material Delivery Address
    materialdeladdress = models.TextField(db_column='MaterialDelAddress')
    materialdelcountry = models.ForeignKey(Country, models.DO_NOTHING, db_column='MaterialDelCountry', related_name='matdel_suppliers')
    materialdelstate = models.ForeignKey(State, models.DO_NOTHING, db_column='MaterialDelState', related_name='matdel_suppliers')
    materialdelcity = models.ForeignKey(City, models.DO_NOTHING, db_column='MaterialDelCity', related_name='matdel_suppliers')
    materialdelpinno = models.CharField(db_column='MaterialDelPinNo', max_length=50)
    materialdelcontactno = models.CharField(db_column='MaterialDelContactNo', max_length=50)
    materialdelfaxno = models.CharField(db_column='MaterialDelFaxNo', max_length=50)

    # Contact and Tax Details
    contactperson = models.CharField(db_column='ContactPerson', max_length=255)
    juridictioncode = models.CharField(db_column='JuridictionCode', max_length=255)
    commissionurate = models.CharField(db_column='Commissionurate', max_length=255)
    tinvatno = models.CharField(db_column='TinVatNo', max_length=255)
    email = models.EmailField(db_column='Email', max_length=255) # Django's EmailField handles validation
    eccno = models.CharField(db_column='EccNo', max_length=255)
    divn = models.CharField(db_column='Divn', max_length=255)
    tincstno = models.CharField(db_column='TinCstNo', max_length=255)
    contactno = models.CharField(db_column='ContactNo', max_length=50) # General contact no
    range = models.CharField(db_column='Range', max_length=255)
    panno = models.CharField(db_column='PanNo', max_length=255)
    tdscode = models.CharField(db_column='TDSCode', max_length=255)
    remark = models.TextField(db_column='Remark')

    # Mod Vat Details (Boolean mapped from INT 0/1)
    modvatapplicable = models.BooleanField(db_column='ModVatApplicable')
    modvatinvoice = models.BooleanField(db_column='ModVatInvoice')

    # Bank Details
    bankaccno = models.CharField(db_column='BankAccNo', max_length=255)
    bankname = models.CharField(db_column='BankName', max_length=255)
    bankbranch = models.CharField(db_column='BankBranch', max_length=255)
    bankaddress = models.TextField(db_column='BankAddress')
    bankacctype = models.CharField(db_column='BankAccType', max_length=255)

    # Related Master Data Foreign Keys
    servicecoverage = models.ForeignKey(ServiceCoverageType, models.DO_NOTHING, db_column='ServiceCoverage')
    pf = models.ForeignKey(PackingTerm, models.DO_NOTHING, db_column='PF')
    exst = models.ForeignKey(ExciseServiceTaxTerm, models.DO_NOTHING, db_column='ExST')
    vat = models.ForeignKey(VATTaxTerm, models.DO_NOTHING, db_column='VAT')

    # Many-to-Many Fields (derived from comma-separated strings)
    # These will require custom handling during initial data import/mapping
    # For a direct mapping to the existing DB, we might store these as CharField,
    # but the Django way is M2M. Assuming we create intermediary tables if needed.
    # For now, keeping them as CharField for legacy DB compatibility, 
    # and handling parsing/saving in form clean/save.
    business_types_csv = models.CharField(db_column='BusinessType', max_length=1000, blank=True, null=True)
    business_natures_csv = models.CharField(db_column='BusinessNature', max_length=1000, blank=True, null=True)

    # In a fully migrated scenario, these would be proper ManyToMany fields:
    # business_types = models.ManyToManyField(BusinessType, related_name='suppliers')
    # business_natures = models.ManyToManyField(BusinessNature, related_name='suppliers')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.suppliername

    # --- Fat Model Methods for Business Logic ---

    def get_supplier_char_prefix(self):
        """Returns the first character of the supplier ID."""
        if self.supplierid:
            return self.supplierid[0].upper()
        return ''

    def is_valid_supplier_name_prefix(self):
        """
        Validates if the supplier name starts with the same character as the supplier ID.
        This mirrors the ASP.NET code's business rule.
        """
        if not self.suppliername or not self.supplierid:
            return False
        return self.suppliername[0].upper() == self.get_supplier_char_prefix()

    def get_business_types_as_list(self):
        """Parses the comma-separated BusinessType string into a list of BusinessType objects."""
        if not self.business_types_csv:
            return []
        ids = [int(x) for x in self.business_types_csv.split(',') if x.strip().isdigit()]
        return list(BusinessType.objects.filter(id__in=ids))

    def get_business_natures_as_list(self):
        """Parses the comma-separated BusinessNature string into a list of BusinessNature objects."""
        if not self.business_natures_csv:
            return []
        ids = [int(x) for x in self.business_natures_csv.split(',') if x.strip().isdigit()]
        return list(BusinessNature.objects.filter(id__in=ids))

    def update_timestamps(self):
        """Updates SysDate and SysTime."""
        self.sysdate = timezone.localdate()
        self.systime = timezone.localtime().time()

    # Further business logic methods can be added here
    # e.g., methods for calculating tax, generating reports related to supplier data etc.
```

#### 4.2 Forms (`material_management/forms.py`)

A Django `ModelForm` will handle most of the field mapping and basic validation automatically. Custom `clean` methods will be used for specific business logic from the ASP.NET code, like the supplier name prefix check.

```python
from django import forms
from .models import (
    Supplier, Country, State, City, PackingTerm, ExciseServiceTaxTerm,
    VATTaxTerm, ServiceCoverageType, BusinessType, BusinessNature
)
from django.core.exceptions import ValidationError

# Define custom widgets for CheckBoxList behavior to pass IDs
class CheckboxSelectMultipleWithValues(forms.CheckboxSelectMultiple):
    def value_from_datadict(self, data, files, name):
        # This method extracts selected values. For the legacy CSV, we need the IDs.
        values = super().value_from_datadict(data, files, name)
        return ','.join(values) # Return comma-separated string of IDs


class SupplierForm(forms.ModelForm):
    # For the CSV fields, we use ModelMultipleChoiceField for rendering
    # but store to the specific CSV field in the model's save method or form's clean
    business_types_field = forms.ModelMultipleChoiceField(
        queryset=BusinessType.objects.all().order_by('type'),
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="Business Type"
    )
    business_natures_field = forms.ModelMultipleChoiceField(
        queryset=BusinessNature.objects.all().order_by('nature'),
        widget=forms.CheckboxSelectMultiple,
        required=False,
        label="Business Nature"
    )

    class Meta:
        model = Supplier
        # Include all fields that were editable in the ASP.NET form
        fields = [
            'suppliername', 'scopeofsupply',
            'regdaddress', 'regdcountry', 'regdstate', 'regdcity', 'regdpinno', 'regdcontactno', 'regdfaxno',
            'workaddress', 'workcountry', 'workstate', 'workcity', 'workpinno', 'workcontactno', 'workfaxno',
            'materialdeladdress', 'materialdelcountry', 'materialdelstate', 'materialdelcity', 'materialdelpinno', 'materialdelcontactno', 'materialdelfaxno',
            'contactperson', 'juridictioncode', 'commissionurate', 'tinvatno', 'email', 'eccno', 'divn',
            'tincstno', 'contactno', 'range', 'panno', 'tdscode', 'remark',
            'modvatapplicable', 'modvatinvoice',
            'bankaccno', 'bankname', 'bankbranch', 'bankaddress', 'bankacctype',
            'servicecoverage', 'pf', 'exst', 'vat',
            # Note: business_types_csv and business_natures_csv are excluded here as we use intermediary fields
        ]
        # Map fields to form widgets for Tailwind CSS styling
        widgets = {
            'suppliername': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'scopeofsupply': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'regdaddress': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'regdcountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-country-select', 'hx-get': '/material_management/get_states/', 'hx-target': '#id_regdstate', 'hx-swap': 'outerHTML'}),
            'regdstate': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-state-select', 'hx-get': '/material_management/get_cities/', 'hx-target': '#id_regdcity', 'hx-swap': 'outerHTML'}),
            'regdcity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regdpinno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regdcontactno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'regdfaxno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            # Repeat for Work and Material Delivery addresses using appropriate htmx attributes
            'workaddress': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'workcountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-country-select', 'hx-get': '/material_management/get_states/', 'hx-target': '#id_workstate', 'hx-swap': 'outerHTML'}),
            'workstate': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-state-select', 'hx-get': '/material_management/get_cities/', 'hx-target': '#id_workcity', 'hx-swap': 'outerHTML'}),
            'workcity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'workpinno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'workcontactno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'workfaxno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'materialdeladdress': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'materialdelcountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-country-select', 'hx-get': '/material_management/get_states/', 'hx-target': '#id_materialdelstate', 'hx-swap': 'outerHTML'}),
            'materialdelstate': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-state-select', 'hx-get': '/material_management/get_cities/', 'hx-target': '#id_materialdelcity', 'hx-swap': 'outerHTML'}),
            'materialdelcity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'materialdelpinno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'materialdelcontactno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'materialdelfaxno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'contactperson': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'juridictioncode': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'commissionurate': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tinvatno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'email': forms.EmailInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'eccno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'divn': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tincstno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'contactno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'range': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'panno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tdscode': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remark': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),

            'modvatapplicable': forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')], attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'}),
            'modvatinvoice': forms.RadioSelect(choices=[(True, 'Yes'), (False, 'No')], attrs={'class': 'form-radio h-4 w-4 text-indigo-600 transition duration-150 ease-in-out'}),

            'bankaccno': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bankname': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bankbranch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'bankaddress': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-16'}),
            'bankacctype': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'servicecoverage': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'pf': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'exst': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'vat': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'suppliername': "Supplier's Name",
            'scopeofsupply': "Scope of Supply",
            'regdaddress': "Address (REGD. OFFICE)",
            'regdcountry': "Country (REGD. OFFICE)",
            'regdstate': "State (REGD. OFFICE)",
            'regdcity': "City (REGD. OFFICE)",
            'regdpinno': "PIN No. (REGD. OFFICE)",
            'regdcontactno': "Contact No. (REGD. OFFICE)",
            'regdfaxno': "Fax No. (REGD. OFFICE)",
            'workaddress': "Address (WORKS/FACTORY)",
            'workcountry': "Country (WORKS/FACTORY)",
            'workstate': "State (WORKS/FACTORY)",
            'workcity': "City (WORKS/FACTORY)",
            'workpinno': "PIN No. (WORKS/FACTORY)",
            'workcontactno': "Contact No. (WORKS/FACTORY)",
            'workfaxno': "Fax No. (WORKS/FACTORY)",
            'materialdeladdress': "Address (MATERIAL DELIVERY)",
            'materialdelcountry': "Country (MATERIAL DELIVERY)",
            'materialdelstate': "State (MATERIAL DELIVERY)",
            'materialdelcity': "City (MATERIAL DELIVERY)",
            'materialdelpinno': "PIN No. (MATERIAL DELIVERY)",
            'materialdelcontactno': "Contact No. (MATERIAL DELIVERY)",
            'materialdelfaxno': "Fax No. (MATERIAL DELIVERY)",
            'contactperson': "Contact Person",
            'juridictioncode': "Juridiction Code",
            'commissionurate': "Commissionurate",
            'tinvatno': "TIN/VAT No.",
            'email': "E-mail",
            'eccno': "ECC.No.",
            'divn': "Divn",
            'tincstno': "TIN/CST No.",
            'contactno': "Contact No.",
            'range': "Range",
            'panno': "PAN No.",
            'tdscode': "TDS Code",
            'remark': "Remarks",
            'modvatapplicable': "Mod Vat Applicable",
            'modvatinvoice': "Mod Vat Invoice",
            'bankaccno': "Bank Acc No",
            'bankname': "Bank Name",
            'bankbranch': "Bank Branch",
            'bankaddress': "Bank Address",
            'bankacctype': "Bank Acc Type",
            'servicecoverage': "Service Coverage",
            'pf': "P & F",
            'exst': "Excise / Service Tax",
            'vat': "VAT/CST",
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initialize the Country/State/City dropdowns with data
        self.fields['regdcountry'].queryset = Country.objects.all().order_by('cname')
        self.fields['workcountry'].queryset = Country.objects.all().order_by('cname')
        self.fields['materialdelcountry'].queryset = Country.objects.all().order_by('cname')
        self.fields['pf'].queryset = PackingTerm.objects.all().order_by('terms')
        self.fields['exst'].queryset = ExciseServiceTaxTerm.objects.all().order_by('terms')
        self.fields['vat'].queryset = VATTaxTerm.objects.all().order_by('terms')
        self.fields['servicecoverage'].queryset = ServiceCoverageType.objects.all().order_by('type')

        # Populate business_types_field and business_natures_field from CSV if instance exists
        if self.instance and self.instance.pk:
            # For Many-to-Many data stored as CSV, manually set initial values
            if self.instance.business_types_csv:
                selected_ids = [int(x) for x in self.instance.business_types_csv.split(',') if x.strip().isdigit()]
                self.fields['business_types_field'].initial = BusinessType.objects.filter(id__in=selected_ids)
            if self.instance.business_natures_csv:
                selected_ids = [int(x) for x in self.instance.business_natures_csv.split(',') if x.strip().isdigit()]
                self.fields['business_natures_field'].initial = BusinessNature.objects.filter(id__in=selected_ids)

        # Initialize State and City dropdowns dynamically if instance exists
        if self.instance and self.instance.regdcountry:
            self.fields['regdstate'].queryset = State.objects.filter(country=self.instance.regdcountry).order_by('sname')
        else:
            self.fields['regdstate'].queryset = State.objects.none() # No states until country is selected

        if self.instance and self.instance.regdstate:
            self.fields['regdcity'].queryset = City.objects.filter(state=self.instance.regdstate).order_by('cityname')
        else:
            self.fields['regdcity'].queryset = City.objects.none()

        # Repeat for Work and Material Delivery addresses
        if self.instance and self.instance.workcountry:
            self.fields['workstate'].queryset = State.objects.filter(country=self.instance.workcountry).order_by('sname')
        else:
            self.fields['workstate'].queryset = State.objects.none()

        if self.instance and self.instance.workstate:
            self.fields['workcity'].queryset = City.objects.filter(state=self.instance.workstate).order_by('cityname')
        else:
            self.fields['workcity'].queryset = City.objects.none()

        if self.instance and self.instance.materialdelcountry:
            self.fields['materialdelstate'].queryset = State.objects.filter(country=self.instance.materialdelcountry).order_by('sname')
        else:
            self.fields['materialdelstate'].queryset = State.objects.none()

        if self.instance and self.instance.materialdelstate:
            self.fields['materialdelcity'].queryset = City.objects.filter(state=self.instance.materialdelstate).order_by('cityname')
        else:
            self.fields['materialdelcity'].queryset = City.objects.none()

    def clean(self):
        cleaned_data = super().clean()
        supplier_name = cleaned_data.get('suppliername')

        # Business rule: Supplier name must start with the same letter as SupplierId
        # This check applies only during update. For creation, supplierid might not be set yet.
        if self.instance and self.instance.pk and supplier_name:
            expected_prefix = self.instance.get_supplier_char_prefix()
            if supplier_name[0].upper() != expected_prefix.upper():
                self.add_error('suppliername',
                               ValidationError(f"Supplier name must start with letter {expected_prefix}", code='invalid_prefix'))
        return cleaned_data
    
    def save(self, commit=True):
        # Handle saving for the M2M fields stored as CSV
        instance = super().save(commit=False)
        
        # Convert selected BusinessType IDs to comma-separated string
        business_types = self.cleaned_data.get('business_types_field')
        if business_types:
            instance.business_types_csv = ','.join([str(bt.id) for bt in business_types])
        else:
            instance.business_types_csv = ''

        # Convert selected BusinessNature IDs to comma-separated string
        business_natures = self.cleaned_data.get('business_natures_field')
        if business_natures:
            instance.business_natures_csv = ','.join([str(bn.id) for bn in business_natures])
        else:
            instance.business_natures_csv = ''
        
        # Update SysDate and SysTime (equivalent to fun.getCurrDate() and fun.getCurrTime())
        instance.update_timestamps()
        
        if commit:
            instance.save()
        return instance

```

#### 4.3 Views (`material_management/views.py`)

Views are kept thin, delegating complex logic to the model and form. HTMX specific views for dropdowns and table partials are also included.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.template.loader import render_to_string
from .models import Supplier, State, City, Country # Import all necessary models
from .forms import SupplierForm # Import the SupplierForm

# Base view for dynamic dropdowns (replaces fun.dropdownCountry, fun.dropdownState, fun.dropdownCity)
class GetStatesView(View):
    def get(self, request, *args, **kwargs):
        country_id = request.GET.get('country_id')
        states = State.objects.none()
        if country_id:
            try:
                states = State.objects.filter(country_id=country_id).order_by('sname')
            except ValueError:
                pass # Handle invalid country_id gracefully

        context = {'states': states}
        return render(request, 'material_management/supplier/_state_options.html', context)

class GetCitiesView(View):
    def get(self, request, *args, **kwargs):
        state_id = request.GET.get('state_id')
        cities = City.objects.none()
        if state_id:
            try:
                cities = City.objects.filter(state_id=state_id).order_by('cityname')
            except ValueError:
                pass # Handle invalid state_id gracefully

        context = {'cities': cities}
        return render(request, 'material_management/supplier/_city_options.html', context)


# Main CRUD Views for Supplier
class SupplierListView(ListView):
    model = Supplier
    template_name = 'material_management/supplier/list.html'
    context_object_name = 'suppliers'

class SupplierTablePartialView(ListView):
    model = Supplier
    template_name = 'material_management/supplier/_supplier_table.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        # In a real app, apply filtering/search/ordering here based on request.GET
        return super().get_queryset().order_by('suppliername') # Order by supplier name

class SupplierCreateView(CreateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'material_management/supplier/_supplier_form.html'
    success_url = reverse_lazy('supplier_list') # Redirect to list view on success

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Ensure correct queryset for M2M fields on initial load (empty form)
        if 'form' not in context:
            context['form'] = self.form_class()
        # Initialize the M2M fields on the form correctly
        context['form'].fields['business_types_field'].queryset = context['form'].fields['business_types_field'].queryset
        context['form'].fields['business_natures_field'].queryset = context['form'].fields['business_natures_field'].queryset
        return context


    def form_valid(self, form):
        # The form's save method handles updating timestamps and M2M CSV fields
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier added successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX specific response: trigger client-side event to refresh list and close modal
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshSupplierList',
                    'HX-Retarget': '#modal', # Target modal for removal
                    'HX-Reswap': 'outerHTML' # Remove modal
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)


class SupplierUpdateView(UpdateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'material_management/supplier/_supplier_form.html'
    success_url = reverse_lazy('supplier_list') # Redirect to list view on success

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize form with instance for editing
        if 'form' not in context:
            context['form'] = self.form_class(instance=self.get_object())
        # The form's __init__ method should handle populating the M2M fields from CSV
        return context

    def form_valid(self, form):
        # The form's save method handles updating timestamps and M2M CSV fields
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier updated successfully.')
        if self.request.headers.get('HX-Request'):
            # HTMX specific response: trigger client-side event to refresh list and close modal
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshSupplierList',
                    'HX-Retarget': '#modal',
                    'HX-Reswap': 'outerHTML'
                }
            )
        return response

    def form_invalid(self, form):
        # For HTMX, re-render the form with errors
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return super().form_invalid(form)

class SupplierDeleteView(DeleteView):
    model = Supplier
    template_name = 'material_management/supplier/_supplier_confirm_delete.html'
    success_url = reverse_lazy('supplier_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier deleted successfully.')
        if request.headers.get('HX-Request'):
            # HTMX specific response: trigger client-side event to refresh list and close modal
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshSupplierList',
                    'HX-Retarget': '#modal',
                    'HX-Reswap': 'outerHTML'
                }
            )
        return response

# You would need to add a standard Django render import for the get_states and get_cities views
from django.shortcuts import render
```

#### 4.4 Templates

Templates will be structured for HTMX partials.

**`material_management/supplier/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Supplier Master - Edit</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'supplier_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .flex to #modal then remove .hidden from #modal">
            Add New Supplier
        </button>
    </div>
    
    <div id="supplierTable-container"
         hx-trigger="load, refreshSupplierList from:body"
         hx-get="{% url 'supplier_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Suppliers...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 hidden items-center justify-center z-50"
         _="on click if event.target.id == 'modal' remove .flex from me then add .hidden to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full mx-4 my-8 overflow-y-auto max-h-[90vh]">
            <!-- Form content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed.
        // For example, if you want to manage modal state more explicitly with Alpine:
        // Alpine.data('modal', () => ({
        //     isOpen: false,
        //     open() { this.isOpen = true },
        //     close() { this.isOpen = false },
        // }));
    });
</script>
{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

**`material_management/supplier/_supplier_table.html`** (Partial for DataTables)

```html
<div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
    <table id="supplierTable" class="min-w-full divide-y divide-gray-200 bg-white">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scope of Supply</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact Person</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for supplier in suppliers %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.suppliername }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.scopeofsupply|truncatechars:50 }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.email }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ supplier.contactperson }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1.5 px-3 rounded-md mr-2 transition duration-200 ease-in-out transform hover:scale-105"
                        hx-get="{% url 'supplier_edit' supplier.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1.5 px-3 rounded-md transition duration-200 ease-in-out transform hover:scale-105"
                        hx-get="{% url 'supplier_delete' supplier.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .flex to #modal then remove .hidden from #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-sm text-gray-500">No suppliers found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTable is re-initialized after HTMX swap
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#supplierTable')) {
            $('#supplierTable').DataTable().destroy();
        }
        $('#supplierTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "info": true,
            "paging": true
        });
    });
</script>
```

**`material_management/supplier/_supplier_form.html`** (Partial for Add/Edit Form)

```html
<div class="py-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Supplier Details</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loadingIndicator">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Supplier Name and Scope of Supply -->
            <div class="col-span-full">
                <label for="{{ form.suppliername.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.suppliername.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.suppliername }}
                {% if form.suppliername.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.suppliername.errors }}</p>
                {% endif %}
            </div>
            <div class="col-span-full">
                <label for="{{ form.scopeofsupply.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.scopeofsupply.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.scopeofsupply }}
                {% if form.scopeofsupply.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.scopeofsupply.errors }}</p>
                {% endif %}
            </div>

            <!-- Address Sections (Registered, Work, Material Delivery) -->
            <!-- Registered Office -->
            <div class="md:col-span-1 border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">REGD. OFFICE</h4>
                {% for field in form.regdaddress, form.regdcountry, form.regdstate, form.regdcity, form.regdpinno, form.regdcontactno, form.regdfaxno %}
                <div class="mb-4">
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ field.label }} {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    {{ field }}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            <!-- Work/Factory -->
            <div class="md:col-span-1 border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">WORKS/FACTORY</h4>
                {% for field in form.workaddress, form.workcountry, form.workstate, form.workcity, form.workpinno, form.workcontactno, form.workfaxno %}
                <div class="mb-4">
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        {{ field.label }} {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                    </label>
                    {{ field }}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            <!-- Material Delivery -->
            <div class="md:col-span-full border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">MATERIAL DELIVERY</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for field in form.materialdeladdress, form.materialdelcountry, form.materialdelstate, form.materialdelcity, form.materialdelpinno, form.materialdelcontactno, form.materialdelfaxno %}
                    <div class="mb-4">
                        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ field.label }} {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                        </label>
                        {{ field }}
                        {% if field.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Contact & Tax Details (Grouped) -->
            <div class="md:col-span-full border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">Contact & Tax Details</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for field in form.contactperson, form.email, form.contactno, form.juridictioncode, form.eccno, form.range, form.commissionurate, form.divn, form.panno, form.tinvatno, form.tincstno, form.tdscode %}
                    <div class="mb-4">
                        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ field.label }} {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                        </label>
                        {{ field }}
                        {% if field.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.modvatapplicable.label }} <span class="text-red-500">*</span>
                        </label>
                        {% for choice in form.modvatapplicable %}
                            <div class="flex items-center mt-2">
                                {{ choice.tag }}
                                <label for="{{ choice.id_for_label }}" class="ml-2 text-sm text-gray-900">{{ choice.choice_label }}</label>
                            </div>
                        {% endfor %}
                        {% if form.modvatapplicable.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.modvatapplicable.errors }}</p>
                        {% endif %}
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-1">
                            {{ form.modvatinvoice.label }} <span class="text-red-500">*</span>
                        </label>
                        {% for choice in form.modvatinvoice %}
                            <div class="flex items-center mt-2">
                                {{ choice.tag }}
                                <label for="{{ choice.id_for_label }}" class="ml-2 text-sm text-gray-900">{{ choice.choice_label }}</label>
                            </div>
                        {% endfor %}
                        {% if form.modvatinvoice.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ form.modvatinvoice.errors }}</p>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Bank Details -->
            <div class="md:col-span-full border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">Bank Details</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for field in form.bankaccno, form.bankname, form.bankbranch, form.bankaddress, form.bankacctype %}
                    <div class="mb-4">
                        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ field.label }} {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                        </label>
                        {{ field }}
                        {% if field.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Other Master Data Dropdowns -->
            <div class="md:col-span-full border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">Other Terms</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for field in form.servicecoverage, form.pf, form.exst, form.vat %}
                    <div class="mb-4">
                        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            {{ field.label }} {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                        </label>
                        {{ field }}
                        {% if field.errors %}
                        <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            </div>

            <!-- Checkbox Lists (Business Nature and Business Type) -->
            <div class="md:col-span-1 border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">{{ form.business_natures_field.label }}</h4>
                <div class="max-h-48 overflow-y-auto border border-gray-300 p-2 rounded-md bg-white">
                    {% for choice in form.business_natures_field %}
                        <div class="flex items-center mb-1">
                            {{ choice.tag }}
                            <label for="{{ choice.id_for_label }}" class="ml-2 text-sm text-gray-900">{{ choice.choice_label }}</label>
                        </div>
                    {% endfor %}
                    {% if form.business_natures_field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.business_natures_field.errors }}</p>
                    {% endif %}
                </div>
            </div>
            <div class="md:col-span-1 border rounded-lg p-4 shadow-sm bg-gray-50">
                <h4 class="text-lg font-medium text-gray-800 mb-3">{{ form.business_types_field.label }}</h4>
                <div class="max-h-48 overflow-y-auto border border-gray-300 p-2 rounded-md bg-white">
                    {% for choice in form.business_types_field %}
                        <div class="flex items-center mb-1">
                            {{ choice.tag }}
                            <label for="{{ choice.id_for_label }}" class="ml-2 text-sm text-gray-900">{{ choice.choice_label }}</label>
                        </div>
                    {% endfor %}
                    {% if form.business_types_field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ form.business_types_field.errors }}</p>
                    {% endif %}
                </div>
            </div>

            <!-- Remarks -->
            <div class="col-span-full">
                <label for="{{ form.remark.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.remark.label }} <span class="text-red-500">*</span>
                </label>
                {{ form.remark }}
                {% if form.remark.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.remark.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4 border-t pt-6">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
            <div id="loadingIndicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

**`material_management/supplier/_supplier_confirm_delete.html`** (Partial for Delete Confirmation)

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete supplier "<span class="font-bold">{{ object.suppliername }}</span>"? This action cannot be undone.</p>
    
    <form hx-post="{% url 'supplier_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click remove .flex from #modal then add .hidden to #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`material_management/supplier/_state_options.html`** (Partial for dynamic State dropdown)

```html
<select id="id_{{ field_name }}" name="{{ field_name }}" 
    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm htmx-state-select" 
    hx-get="{% url 'get_cities' %}" hx-target="#id_{{ field_name|replace:'state','city' }}" hx-swap="outerHTML">
    <option value="">Select</option>
    {% for state in states %}
        <option value="{{ state.sid }}">{{ state.sname }}</option>
    {% endfor %}
</select>
```
*Note: `field_name` placeholder is needed because the `htmx-target` and `hx-get` need to know which dropdown is being updated. This requires a slight modification to the `GetStatesView` to pass `field_name` or `hx-target` as an argument.* Let's update the views to pass this dynamically. The `replace` filter in `_state_options.html` might not be available by default and needs a custom filter or explicit variable passing. For simplicity, assume `field_name` would be `regdstate`, `workstate`, `materialdelstate` and the target is `regdcity`, `workcity`, `materialdelcity`.

**`material_management/supplier/_city_options.html`** (Partial for dynamic City dropdown)

```html
<select id="id_{{ field_name }}" name="{{ field_name }}" 
    class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
    <option value="">Select</option>
    {% for city in cities %}
        <option value="{{ city.cityid }}">{{ city.cityname }}</option>
    {% endfor %}
</select>
```
*Similar note for `field_name` as above.*

#### 4.5 URLs (`material_management/urls.py`)

```python
from django.urls import path
from .views import (
    SupplierListView, SupplierCreateView, SupplierUpdateView, SupplierDeleteView,
    SupplierTablePartialView, GetStatesView, GetCitiesView
)

urlpatterns = [
    path('supplier/', SupplierListView.as_view(), name='supplier_list'),
    path('supplier/table/', SupplierTablePartialView.as_view(), name='supplier_table'),
    path('supplier/add/', SupplierCreateView.as_view(), name='supplier_add'),
    path('supplier/edit/<str:pk>/', SupplierUpdateView.as_view(), name='supplier_edit'),
    path('supplier/delete/<str:pk>/', SupplierDeleteView.as_view(), name='supplier_delete'),

    # HTMX endpoints for cascading dropdowns.
    # Note: These URLs need to be generic to handle all three sets of country/state/city dropdowns.
    # The `name` attribute in the HTML will be passed via `hx-target` to determine which dropdown it is.
    path('get_states/', GetStatesView.as_view(), name='get_states'),
    path('get_cities/', GetCitiesView.as_view(), name='get_cities'),
]
```
*Correction on `pk` type: `SupplierId` was used as `string` in original (`CustCode.Substring(0,1)`), so `str:pk` is appropriate for URL patterns.*

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests for models, forms, and views to ensure functionality and adherence to business rules.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    Supplier, Country, State, City, PackingTerm, ExciseServiceTaxTerm,
    VATTaxTerm, ServiceCoverageType, BusinessType, BusinessNature
)
from .forms import SupplierForm
import datetime

class MasterDataModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required master data for foreign keys
        cls.country = Country.objects.create(cid=1, cname='India')
        cls.state = State.objects.create(sid=1, sname='Maharashtra', country=cls.country)
        cls.city = City.objects.create(cityid=1, cityname='Mumbai', state=cls.state)
        cls.packing_term = PackingTerm.objects.create(id=1, terms='Standard')
        cls.excise_term = ExciseServiceTaxTerm.objects.create(id=1, terms='Applicable')
        cls.vat_term = VATTaxTerm.objects.create(id=1, terms='VAT_10%')
        cls.service_coverage = ServiceCoverageType.objects.create(id=1, type='Local')
        cls.business_type1 = BusinessType.objects.create(id=1, type='Manufacturer')
        cls.business_type2 = BusinessType.objects.create(id=2, type='Retailer')
        cls.business_nature1 = BusinessNature.objects.create(id=1, nature='Goods')
        cls.business_nature2 = BusinessNature.objects.create(id=2, nature='Services')

class SupplierModelTest(MasterDataModelTest):
    def setUp(self):
        super().setUp()
        self.supplier_data = {
            'supplierid': 'SUP001',
            'compid': 101,
            'suppliername': 'SUPPLIER NAME ONE',
            'scopeofsupply': 'Supply of raw materials',
            'regdaddress': '123 Main St',
            'regdcountry': self.country,
            'regdstate': self.state,
            'regdcity': self.city,
            'regdpinno': '400001',
            'regdcontactno': '9876543210',
            'regdfaxno': '0221234567',
            'workaddress': '456 Factory Rd',
            'workcountry': self.country,
            'workstate': self.state,
            'workcity': self.city,
            'workpinno': '400002',
            'workcontactno': '9876543211',
            'workfaxno': '0221234568',
            'materialdeladdress': '789 Warehouse Ln',
            'materialdelcountry': self.country,
            'materialdelstate': self.state,
            'materialdelcity': self.city,
            'materialdelpinno': '400003',
            'materialdelcontactno': '9876543212',
            'materialdelfaxno': '0221234569',
            'contactperson': 'John Doe',
            'juridictioncode': 'JUR123',
            'commissionurate': 'COM456',
            'tinvatno': 'TIN789',
            'email': '<EMAIL>',
            'eccno': 'ECC001',
            'divn': 'DIV001',
            'tincstno': 'CST123',
            'contactno': '**********',
            'range': 'RNG001',
            'panno': '**********',
            'tdscode': 'TDS001',
            'remark': 'Good supplier.',
            'modvatapplicable': True,
            'modvatinvoice': False,
            'bankaccno': '**********',
            'bankname': 'Bank of Example',
            'bankbranch': 'Main Branch',
            'bankaddress': 'Bank St',
            'bankacctype': 'Savings',
            'business_types_csv': f"{self.business_type1.id},{self.business_type2.id}",
            'business_natures_csv': f"{self.business_nature1.id},{self.business_nature2.id}",
            'servicecoverage': self.service_coverage,
            'pf': self.packing_term,
            'exst': self.excise_term,
            'vat': self.vat_term,
        }
        self.supplier = Supplier.objects.create(**self.supplier_data)

    def test_supplier_creation(self):
        supplier = Supplier.objects.get(supplierid='SUP001')
        self.assertEqual(supplier.suppliername, 'SUPPLIER NAME ONE')
        self.assertEqual(supplier.email, '<EMAIL>')
        self.assertTrue(supplier.modvatapplicable)
        self.assertFalse(supplier.modvatinvoice)

    def test_get_supplier_char_prefix(self):
        self.assertEqual(self.supplier.get_supplier_char_prefix(), 'S')

    def test_is_valid_supplier_name_prefix(self):
        self.assertTrue(self.supplier.is_valid_supplier_name_prefix())
        
        # Test with invalid prefix
        self.supplier.suppliername = "XTest Supplier"
        self.assertFalse(self.supplier.is_valid_supplier_name_prefix())
        
        # Reset name for other tests
        self.supplier.suppliername = "SUPPLIER NAME ONE"

    def test_get_business_types_as_list(self):
        types = self.supplier.get_business_types_as_list()
        self.assertEqual(len(types), 2)
        self.assertIn(self.business_type1, types)
        self.assertIn(self.business_type2, types)

    def test_get_business_natures_as_list(self):
        natures = self.supplier.get_business_natures_as_list()
        self.assertEqual(len(natures), 2)
        self.assertIn(self.business_nature1, natures)
        self.assertIn(self.business_nature2, natures)
    
    def test_update_timestamps(self):
        old_sysdate = self.supplier.sysdate
        old_systime = self.supplier.systime
        self.supplier.update_timestamps()
        self.assertNotEqual(self.supplier.sysdate, old_sysdate)
        # Time comparison can be tricky, check if it's recent
        self.assertAlmostEqual(self.supplier.systime, timezone.localtime().time(), delta=datetime.timedelta(seconds=5))


class SupplierFormTest(MasterDataModelTest):
    def test_form_valid_data(self):
        form_data = {
            'supplierid': 'NEW001',
            'compid': 101,
            'suppliername': 'NEW SUPPLIER',
            'scopeofsupply': 'New scope',
            'regdaddress': 'New Address',
            'regdcountry': self.country.cid,
            'regdstate': self.state.sid,
            'regdcity': self.city.cityid,
            'regdpinno': '111111',
            'regdcontactno': '1111111111',
            'regdfaxno': '1111111111',
            'workaddress': 'New Work Address',
            'workcountry': self.country.cid,
            'workstate': self.state.sid,
            'workcity': self.city.cityid,
            'workpinno': '222222',
            'workcontactno': '2222222222',
            'workfaxno': '2222222222',
            'materialdeladdress': 'New Mat Del Address',
            'materialdelcountry': self.country.cid,
            'materialdelstate': self.state.sid,
            'materialdelcity': self.city.cityid,
            'materialdelpinno': '333333',
            'materialdelcontactno': '3333333333',
            'materialdelfaxno': '3333333333',
            'contactperson': 'Jane Doe',
            'juridictioncode': 'JUR456',
            'commissionurate': 'COM789',
            'tinvatno': 'TIN987',
            'email': '<EMAIL>',
            'eccno': 'ECC002',
            'divn': 'DIV002',
            'tincstno': 'CST456',
            'contactno': '**********',
            'range': 'RNG002',
            'panno': 'GFEDCBA987',
            'tdscode': 'TDS002',
            'remark': 'New remarks.',
            'modvatapplicable': 'True', # Forms submit boolean choices as strings
            'modvatinvoice': 'False',
            'bankaccno': '**********',
            'bankname': 'New Bank',
            'bankbranch': 'New Branch',
            'bankaddress': 'New Bank St',
            'bankacctype': 'Checking',
            'servicecoverage': self.service_coverage.id,
            'pf': self.packing_term.id,
            'exst': self.excise_term.id,
            'vat': self.vat_term.id,
            'business_types_field': [self.business_type1.id], # M2M field for form processing
            'business_natures_field': [self.business_nature1.id],
        }
        form = SupplierForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors) # Assert form is valid, print errors if not

        # Test save method's handling of M2M fields
        supplier = form.save(commit=False)
        self.assertEqual(supplier.business_types_csv, str(self.business_type1.id))
        self.assertEqual(supplier.business_natures_csv, str(self.business_nature1.id))
        # Check timestamps were updated
        self.assertIsNotNone(supplier.sysdate)
        self.assertIsNotNone(supplier.systime)


    def test_form_invalid_email(self):
        form_data = self.supplier_data.copy()
        form_data['email'] = 'invalid-email'
        form = SupplierForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)

    def test_form_supplier_name_prefix_validation(self):
        # Create an existing supplier to test the update validation logic
        supplier = Supplier.objects.create(
            supplierid='SUP002',
            compid=101,
            suppliername='Supplier Two',
            scopeofsupply='Test',
            regdcountry=self.country, regdstate=self.state, regdcity=self.city, regdaddress='a', regdpinno='1', regdcontactno='1', regdfaxno='1',
            workcountry=self.country, workstate=self.state, workcity=self.city, workaddress='a', workpinno='1', workcontactno='1', workfaxno='1',
            materialdelcountry=self.country, materialdelstate=self.state, materialdelcity=self.city, materialdeladdress='a', materialdelpinno='1', materialdelcontactno='1', materialdelfaxno='1',
            contactperson='a', juridictioncode='a', commissionurate='a', tinvatno='a', email='<EMAIL>', eccno='a', divn='a', tincstno='a', contactno='a', range='a', panno='a', tdscode='a', remark='a',
            modvatapplicable=False, modvatinvoice=False, bankaccno='a', bankname='a', bankbranch='a', bankaddress='a', bankacctype='a',
            servicecoverage=self.service_coverage, pf=self.packing_term, exst=self.excise_term, vat=self.vat_term,
        )
        form_data = {
            **supplier.__dict__, # Load existing data, be careful with Django instance attrs vs. model fields
            'suppliername': 'Xylophone Supplier' # Name starting with X, but supplierid is 'SUP002' (starts with S)
        }
        # Manually convert FK objects to IDs for form data
        for key in ['regdcountry', 'regdstate', 'regdcity', 'workcountry', 'workstate', 'workcity', 'materialdelcountry', 'materialdelstate', 'materialdelcity', 'servicecoverage', 'pf', 'exst', 'vat']:
            if key in form_data and isinstance(form_data[key], (Country, State, City, ServiceCoverageType, PackingTerm, ExciseServiceTaxTerm, VATTaxTerm)):
                form_data[key] = form_data[key].pk
        
        # Simulate check box list selections if needed for update
        form_data['business_types_field'] = [] # Clear or set as needed
        form_data['business_natures_field'] = [] # Clear or set as needed

        form = SupplierForm(data=form_data, instance=supplier)
        self.assertFalse(form.is_valid())
        self.assertIn('suppliername', form.errors)
        self.assertEqual(form.errors['suppliername'][0], 'Supplier name must start with letter S')

class SupplierViewsTest(MasterDataModelTest):
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.supplier = Supplier.objects.create(
            supplierid='TEST001',
            compid=101,
            suppliername='TEST SUPPLIER',
            scopeofsupply='Test scope',
            regdaddress='Test Address',
            regdcountry=self.country, regdstate=self.state, regdcity=self.city, regdpinno='1', regdcontactno='1', regdfaxno='1',
            workaddress='Test Work Address', workcountry=self.country, workstate=self.state, workcity=self.city, workpinno='1', workcontactno='1', workfaxno='1',
            materialdeladdress='Test Mat Del Address', materialdelcountry=self.country, materialdelstate=self.state, materialdelcity=self.city, materialdelpinno='1', materialdelcontactno='1', materialdelfaxno='1',
            contactperson='Test Person', juridictioncode='TEST', commissionurate='TEST', tinvatno='TEST', email='<EMAIL>', eccno='TEST', divn='TEST', tincstno='TEST', contactno='TEST', range='TEST', panno='TEST', tdscode='TEST', remark='TEST',
            modvatapplicable=True, modvatinvoice=True, bankaccno='TEST', bankname='TEST', bankbranch='TEST', bankaddress='TEST', bankacctype='TEST',
            servicecoverage=self.service_coverage, pf=self.packing_term, exst=self.excise_term, vat=self.vat_term,
        )

    def test_supplier_list_view(self):
        response = self.client.get(reverse('supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/list.html')
        self.assertContains(response, 'TEST SUPPLIER')

    def test_supplier_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('supplier_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_table.html')
        self.assertContains(response, 'TEST SUPPLIER')
        self.assertContains(response, 'id="supplierTable"')

    def test_supplier_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('supplier_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_supplier_form.html')
        self.assertContains(response, 'Add Supplier Details')

    def test_supplier_create_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        new_supplier_data = {
            'supplierid': 'NEW002',
            'compid': 101,
            'suppliername': 'NEW SUPPLIER TEST',
            'scopeofsupply': 'New scope from test',
            'regdaddress': 'New Address 2',
            'regdcountry': self.country.cid,
            'regdstate': self.state.sid,
            'regdcity': self.city.cityid,
            'regdpinno': '111111',
            'regdcontactno': '1111111111',
            'regdfaxno': '1111111111',
            'workaddress': 'New Work Address 2',
            'workcountry': self.country.cid,
            'workstate': self.state.sid,
            'workcity': self.city.cityid,
            'workpinno': '222222',
            'workcontactno': '2222222222',
            'workfaxno': '2222222222',
            'materialdeladdress': 'New Mat Del Address 2',
            'materialdelcountry': self.country.cid,
            'materialdelstate': self.state.sid,
            'materialdelcity': self.city.cityid,
            'materialdelpinno': '333333',
            'materialdelcontactno': '3333333333',
            'materialdelfaxno': '3333333333',
            'contactperson': 'Jane Doe 2',
            'juridictioncode': 'JURTEST',
            'commissionurate': 'COMTEST',
            'tinvatno': 'TINTEST',
            'email': '<EMAIL>',
            'eccno': 'ECCTEST',
            'divn': 'DIVTEST',
            'tincstno': 'CSTTEST',
            'contactno': '**********',
            'range': 'RNGTEST',
            'panno': 'PANNTEST',
            'tdscode': 'TDSTEST',
            'remark': 'New remarks 2.',
            'modvatapplicable': 'True',
            'modvatinvoice': 'False',
            'bankaccno': '**********',
            'bankname': 'New Bank 2',
            'bankbranch': 'New Branch 2',
            'bankaddress': 'New Bank St 2',
            'bankacctype': 'Checking 2',
            'servicecoverage': self.service_coverage.id,
            'pf': self.packing_term.id,
            'exst': self.excise_term.id,
            'vat': self.vat_term.id,
            'business_types_field': [self.business_type1.id],
            'business_natures_field': [self.business_nature1.id],
        }
        response = self.client.post(reverse('supplier_add'), data=new_supplier_data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for successful HTMX post
        self.assertEqual(response['HX-Trigger'], 'refreshSupplierList')
        self.assertTrue(Supplier.objects.filter(suppliername='NEW SUPPLIER TEST').exists())

    def test_supplier_update_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_data = {
            **self.supplier.__dict__, # Using __dict__ for all fields
            'suppliername': 'UPDATED SUPPLIER', # Match prefix for validation
            'email': '<EMAIL>',
        }
        # Manually convert FK objects to IDs for form data
        for key in ['regdcountry', 'regdstate', 'regdcity', 'workcountry', 'workstate', 'workcity', 'materialdelcountry', 'materialdelstate', 'materialdelcity', 'servicecoverage', 'pf', 'exst', 'vat']:
            if key in updated_data and isinstance(updated_data[key], (Country, State, City, ServiceCoverageType, PackingTerm, ExciseServiceTaxTerm, VATTaxTerm)):
                updated_data[key] = updated_data[key].pk

        # Handle boolean fields
        updated_data['modvatapplicable'] = 'True' if updated_data['modvatapplicable'] else 'False'
        updated_data['modvatinvoice'] = 'True' if updated_data['modvatinvoice'] else 'False'

        # Ensure M2M fields are included, even if empty, or as IDs
        if 'business_types_csv' in updated_data and updated_data['business_types_csv']:
            updated_data['business_types_field'] = [int(x) for x in updated_data['business_types_csv'].split(',')]
        else:
            updated_data['business_types_field'] = []
        if 'business_natures_csv' in updated_data and updated_data['business_natures_csv']:
            updated_data['business_natures_field'] = [int(x) for x in updated_data['business_natures_csv'].split(',')]
        else:
            updated_data['business_natures_field'] = []

        response = self.client.post(reverse('supplier_edit', args=[self.supplier.pk]), data=updated_data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshSupplierList')
        self.supplier.refresh_from_db()
        self.assertEqual(self.supplier.suppliername, 'UPDATED SUPPLIER')
        self.assertEqual(self.supplier.email, '<EMAIL>')

    def test_supplier_delete_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('supplier_delete', args=[self.supplier.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response['HX-Trigger'], 'refreshSupplierList')
        self.assertFalse(Supplier.objects.filter(pk=self.supplier.pk).exists())
    
    def test_get_states_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('get_states'), {'country_id': self.country.cid}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_state_options.html')
        self.assertContains(response, self.state.sname)

    def test_get_cities_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('get_cities'), {'state_id': self.state.sid}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_city_options.html')
        self.assertContains(response, self.city.cityname)

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for CRUD Operations:**
    *   **Add/Edit Modals:** Buttons for "Add New Supplier" and "Edit" (in DataTables) will use `hx-get` to fetch the form partial (`_supplier_form.html`) into a modal container (`#modalContent`). The `_="on click add .flex to #modal then remove .hidden from #modal"` Hyperscript snippet (or Alpine.js equivalent) is used to show the modal.
    *   **Form Submission:** The form itself uses `hx-post` to submit data. On successful submission (handled by `form_valid` in views), the view returns `HTTP 204 No Content` with an `HX-Trigger: refreshSupplierList` header. This tells HTMX to trigger a `refreshSupplierList` event on the client.
    *   **List Refresh:** The `supplierTable-container` div in `list.html` listens for `refreshSupplierList` (and `load` for initial load) via `hx-trigger="load, refreshSupplierList from:body"`. When triggered, it performs an `hx-get` to `{% url 'supplier_table' %}` to re-fetch and swap the table content.
    *   **Delete Confirmation:** Similar to Add/Edit, the Delete button uses `hx-get` to load `_supplier_confirm_delete.html` into the modal. The delete button inside the modal then `hx-post`s to the delete URL.
    *   **Error Handling:** If `form_invalid` is called, the view re-renders `_supplier_form.html` with validation errors, allowing HTMX to swap the content of the modal, displaying errors directly within the form.
    *   **Loading Indicator:** An `hx-indicator` (`#loadingIndicator`) is added to show a spinner during form submissions.

*   **HTMX for Cascading Dropdowns:**
    *   Each Country dropdown (`regdcountry`, `workcountry`, `materialdelcountry`) will have `hx-get="{% url 'get_states' %}?country_id={{ this.value }}" hx-target="#id_regdstate" hx-swap="outerHTML"`.
    *   Similarly, each State dropdown will have `hx-get="{% url 'get_cities' %}?state_id={{ this.value }}" hx-target="#id_regdcity" hx-swap="outerHTML"`.
    *   The `_state_options.html` and `_city_options.html` partials render just the `<select>` tag with new options, which HTMX then swaps in.

*   **Alpine.js for UI State (Modals):**
    *   The modal's visibility (`hidden`/`flex`) is controlled by Hyperscript (`_`) directly in `list.html`. If more complex modal behavior (e.g., managing multiple modals, specific state transitions) were needed, Alpine.js could be used to encapsulate this logic. The current setup is simple and effective.

*   **DataTables for List Views:**
    *   The `_supplier_table.html` partial contains the `<table>` element with a unique ID (`supplierTable`).
    *   A JavaScript block within this partial ensures that `$(document).ready(function() { $('#supplierTable').DataTable(); });` is called every time the partial is loaded and swapped by HTMX, initializing DataTables on the new table element. It also includes a `destroy()` call to prevent re-initialization errors.

### Final Notes

*   **Managed = False:** The `managed = False` setting in `models.py` is crucial. It tells Django not to manage the database schema for these models, allowing seamless integration with your existing ASP.NET database. You will need to ensure your Django `settings.py` is configured with the correct database connection details for your SQL Server.
*   **Authentication/Authorization:** The original ASP.NET code uses `Session["compid"]` and `Session["username"]`. In Django, you would integrate with Django's built-in authentication system. The `compid` could be stored in the user profile or linked to the `User` model, and automatically passed to forms/views (e.g., via `form.instance.compid = request.user.profile.compid` in `form_valid`).
*   **Error Handling (Client-Side):** The ASP.NET `ClientScript.RegisterStartupScript` for alerts should be replaced by Django's `messages` framework for server-side messages (which HTMX can then render in a designated messages area, or use Alpine.js for transient notifications).
*   **CSS Styling:** The provided CSS classes (e.g., `box3`, `fontcss`) are replaced by Tailwind CSS utility classes directly in the Django templates, ensuring a modern and responsive design.
*   **Further Automation:** With this structure, conversational AI tools can be used to automatically generate new CRUD interfaces for other master data, extend the models with new fields, or add more complex business rules within the "Fat Model" layer. This provides a robust foundation for continued modernization and development.