## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using `managed = False` and `db_table`
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend `core/base.html` (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

**Analysis:**
The `SqlDataSource1` component reveals the core database interaction.

- **Table Name:** `tblMM_Supplier_ServiceCoverage`
- **Columns:**
    - `Id` (identified as `DataKeyNames="Id"` and used in `DeleteCommand`, `UpdateCommand`)
    - `Type` (used in `SelectCommand`, `InsertCommand`, `UpdateCommand`, and bound to `lblServiceCoverage` / `txtServiceCoverage`)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The `SqlDataSource1` explicitly defines all standard CRUD operations:
- **Create (Insert):** `InsertCommand="INSERT INTO [tblMM_Supplier_ServiceCoverage] ([Type]) VALUES (@Type)"`. Triggered by `GridView1_RowCommand` (commands "Add" and "Add1") from footer or empty data template.
- **Read (Select):** `SelectCommand="SELECT * FROM [tblMM_Supplier_ServiceCoverage] ORDER BY [Id] DESC"`. Populates `GridView1`.
- **Update:** `UpdateCommand="UPDATE [tblMM_Supplier_ServiceCoverage] SET [Type] = @Type WHERE [Id] = @Id"`. Implicitly handled by `GridView1`'s edit functionality.
- **Delete:** `DeleteCommand="DELETE FROM [tblMM_Supplier_ServiceCoverage] WHERE [Id] = @Id"`. Implicitly handled by `GridView1`'s delete functionality.

**Validation Logic:**
- `RequiredFieldValidator` is used for the `Type` field in both the edit and insert templates, indicating that the `Type` field is mandatory.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

**Analysis:**
- **`GridView1`:** This is the primary display component, providing a tabular view of data with built-in paging, sorting, and inline CRUD capabilities. This will be replaced by a Django `ListView` paired with `DataTables.js` for enhanced client-side features.
- **`asp:TextBox` (e.g., `txtServiceCoverage`, `lblServiceCoverage0`):** Used for inputting or editing the `Type` field. These will map to Django form fields (e.g., `forms.CharField` with `widgets.TextInput`).
- **`asp:Button`/`asp:LinkButton` (e.g., `btnInsert`, command fields for Edit/Delete):** These trigger server-side actions. In Django, these will be replaced by HTMX-driven buttons that load forms or trigger actions in a modal, minimizing full page reloads.
- **`asp:Label` (`lblMessage`):** Displays status messages (e.g., "Record Inserted"). This will be handled by Django's messages framework and displayed in the main template, possibly with Alpine.js for transient notifications.
- **Client-side JavaScript (e.g., `confirmationAdd()`, `confirmationDelete()`, `confirmationUpdate()`):** Used for confirmation prompts. These will be replaced by HTMX's `hx-confirm` attribute or by loading confirmation partials in a modal.

### Step 4: Generate Django Code

Based on the analysis, we'll create a new Django application, let's call it `servicemgmt`.

#### 4.1 Models

Task: Create a Django model based on the database schema.

We will create a `ServiceCoverage` model that maps directly to the `tblMM_Supplier_ServiceCoverage` table. The `Id` column will be mapped to `id` as the primary key, and `Type` to `service_type` for better readability and to avoid Python keyword conflicts.

```python
# servicemgmt/models.py
from django.db import models

class ServiceCoverage(models.Model):
    # 'Id' from ASP.NET is the primary key. Django typically uses 'id' automatically,
    # but for an existing table where the column is 'Id', we explicitly map it.
    id = models.IntegerField(db_column='Id', primary_key=True)
    # 'Type' from ASP.NET maps to 'service_type' for clarity and to avoid Python keyword clash.
    # Assuming a reasonable max_length for the Type field.
    service_type = models.CharField(db_column='Type', max_length=255, verbose_name="Service Type")

    class Meta:
        # 'managed = False' tells Django not to manage table creation/deletion
        # as it's an existing table.
        managed = False
        db_table = 'tblMM_Supplier_ServiceCoverage'
        verbose_name = 'Service Coverage'
        verbose_name_plural = 'Service Coverages'
        # Matching the ORDER BY [Id] DESC from the original SQL
        ordering = ['-id']

    def __str__(self):
        return self.service_type

    # Fat model principle: Any specific business logic for ServiceCoverage
    # (e.g., custom validation beyond basic form validation, derived properties)
    # would be implemented here. For this simple CRUD, no complex logic was found.
```

#### 4.2 Forms

Task: Define a Django form for user input.

A `ModelForm` will be used for `ServiceCoverage` to handle creation and updates. We'll enforce the "Type" field as required, mirroring the `RequiredFieldValidator` from ASP.NET.

```python
# servicemgmt/forms.py
from django import forms
from .models import ServiceCoverage

class ServiceCoverageForm(forms.ModelForm):
    class Meta:
        model = ServiceCoverage
        fields = ['service_type'] # Only 'service_type' is editable/insertable by user
        widgets = {
            'service_type': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'e.g., IT Support, Consulting, Maintenance'
            }),
        }
        
    def clean_service_type(self):
        # Replicating the RequiredFieldValidator logic from ASP.NET
        service_type = self.cleaned_data.get('service_type')
        if not service_type:
            raise forms.ValidationError("Service Type cannot be empty.")
        # Additional business rules or sanitization for 'service_type' can be added here
        return service_type.strip() # Example: remove leading/trailing whitespace
```

#### 4.3 Views

Task: Implement CRUD operations using CBVs.

We will use Django's `ListView`, `CreateView`, `UpdateView`, and `DeleteView`. A dedicated `TablePartialView` will render the DataTables content via HTMX to enable dynamic list refreshes. Views will be kept concise (thin view principle).

```python
# servicemgmt/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.template.loader import render_to_string # For HTMX partials
from .models import ServiceCoverage
from .forms import ServiceCoverageForm

class ServiceCoverageListView(ListView):
    """
    Renders the main page displaying the list of Service Coverages.
    This view loads the base template which then uses HTMX to load the table.
    """
    model = ServiceCoverage
    template_name = 'servicemgmt/servicecoverage/list.html'
    context_object_name = 'service_coverages' # Name for the list of objects in the template

class ServiceCoverageTablePartialView(ListView):
    """
    Renders only the DataTables HTML table content.
    Designed to be loaded via HTMX into a container on the main list page.
    """
    model = ServiceCoverage
    template_name = 'servicemgmt/servicecoverage/_servicecoverage_table.html'
    context_object_name = 'service_coverages'
    # No custom get_context_data needed for simple display

class ServiceCoverageCreateView(CreateView):
    """
    Handles the creation of a new Service Coverage.
    Renders a form within a modal and responds with HTMX triggers.
    """
    model = ServiceCoverage
    form_class = ServiceCoverageForm
    template_name = 'servicemgmt/servicecoverage/_servicecoverage_form.html' # Partial template for modal
    success_url = reverse_lazy('servicecoverage_list') # Fallback, not strictly used with HTMX hx-swap="none"

    def form_valid(self, form):
        """
        Processes valid form submission. Sends a success message and
        an HTMX trigger to refresh the list table.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Service Coverage added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # HTTP 204 No Content for HTMX to indicate success and close modal
                headers={
                    'HX-Trigger': 'refreshServiceCoverageList' # Custom HTMX event to refresh table
                }
            )
        return response
    
    def form_invalid(self, form):
        """
        Processes invalid form submission. Renders the form again with errors
        for HTMX, keeping the modal open.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, self.request),
                status=200 # HTTP 200 OK because we're returning content (the form with errors)
            )
        return response

class ServiceCoverageUpdateView(UpdateView):
    """
    Handles the updating of an existing Service Coverage.
    Renders a form within a modal and responds with HTMX triggers.
    """
    model = ServiceCoverage
    form_class = ServiceCoverageForm
    template_name = 'servicemgmt/servicecoverage/_servicecoverage_form.html' # Partial template for modal
    success_url = reverse_lazy('servicecoverage_list') # Fallback

    def form_valid(self, form):
        """
        Processes valid form submission. Sends a success message and
        an HTMX trigger to refresh the list table.
        """
        response = super().form_valid(form)
        messages.success(self.request, 'Service Coverage updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # HTTP 204 No Content
                headers={
                    'HX-Trigger': 'refreshServiceCoverageList'
                }
            )
        return response

    def form_invalid(self, form):
        """
        Processes invalid form submission. Renders the form again with errors.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                render_to_string(self.template_name, {'form': form}, self.request),
                status=200
            )
        return response

class ServiceCoverageDeleteView(DeleteView):
    """
    Handles the deletion of a Service Coverage.
    Renders a confirmation prompt within a modal and responds with HTMX triggers.
    """
    model = ServiceCoverage
    template_name = 'servicemgmt/servicecoverage/_servicecoverage_confirm_delete.html' # Partial template for modal
    success_url = reverse_lazy('servicecoverage_list') # Fallback

    def delete(self, request, *args, **kwargs):
        """
        Performs the deletion. Sends a success message and
        an HTMX trigger to refresh the list table.
        """
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Service Coverage deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # HTTP 204 No Content
                headers={
                    'HX-Trigger': 'refreshServiceCoverageList'
                }
            )
        return response

    # No specific get_context_data needed here beyond what DeleteView provides
```

#### 4.4 Templates

Task: Create templates for each view.

We'll use three templates: a main list page (`list.html`), a partial for the DataTables content (`_servicecoverage_table.html`), and a partial for forms/delete confirmations (`_servicecoverage_form.html`, `_servicecoverage_confirm_delete.html`) loaded via HTMX into a modal.

**List Template (`servicemgmt/servicecoverage/list.html`):**
This is the main page for displaying service coverages. It extends `core/base.html` and uses HTMX to load the actual table content.

```html
{% extends 'core/base.html' %} {# Assumes core/base.html contains all necessary CDNs like jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS setup #}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Service Coverages</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-150 ease-in-out"
            hx-get="{% url 'servicecoverage_add' %}" {# HTMX GET request to load the create form #}
            hx-target="#modalContent" {# Target the modal content area #}
            hx-trigger="click" {# Trigger on button click #}
            _="on click add .flex to #modal and remove .hidden from #modal"> {# Alpine.js/Hyperscript to show the modal #}
            Add New Service Coverage
        </button>
    </div>
    
    <div id="servicecoverageTable-container"
         hx-trigger="load, refreshServiceCoverageList from:body" {# Load on page load and on custom HTMX event #}
         hx-get="{% url 'servicecoverage_table' %}" {# HTMX GET request to load the table partial #}
         hx-swap="innerHTML" {# Replace content inside this div #}
         class="bg-white shadow-md rounded-lg p-6">
        <!-- Initial loading indicator, replaced by the table once HTMX loads it -->
        <div class="flex justify-center items-center h-48">
            <div class="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-500"></div>
            <p class="ml-4 text-lg text-gray-600">Loading Service Coverages...</p>
        </div>
    </div>
    
    <!-- Modal structure for forms and confirmations -->
    <div id="modal" 
         class="fixed inset-0 z-50 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center p-4"
         _="on click if event.target.id == 'modal' remove .flex from me and add .hidden to me"> {# Close modal when clicking outside #}
        <div id="modalContent" class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-auto my-auto overflow-hidden">
            <!-- Content (form or confirmation) will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be initialized here if more complex client-side state
        // or interactions are needed that HTMX alone doesn't cover.
    });

    // HTMX listener to close the modal after successful form submission (204 No Content response)
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status === 204) { 
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
            }
        }
    });

    // Re-initialize DataTables after HTMX swaps in the table content
    // This is crucial because DataTables needs to be re-applied to the new DOM element.
    document.body.addEventListener('htmx:afterOnLoad', function(evt) {
        // Ensure this only runs for the service coverage table container
        if (evt.detail.elt.id === 'servicecoverageTable-container') {
            $('#servicecoverageTable').DataTable({
                "pageLength": 17, // Matches ASP.NET GridView's PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "destroy": true, // Allows re-initialization on subsequent HTMX loads
                "responsive": true // Makes the table responsive
            });
        }
    });
</script>
{% endblock %}
```

**Table Partial Template (`servicemgmt/servicecoverage/_servicecoverage_table.html`):**
This template contains only the HTML for the DataTables table. It's designed to be swapped in by HTMX.

```html
<table id="servicecoverageTable" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service Type</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% for obj in service_coverages %}
        <tr>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.service_type }}</td>
            <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out mr-2"
                    hx-get="{% url 'servicecoverage_edit' obj.pk %}" {# HTMX GET for edit form #}
                    hx-target="#modalContent" 
                    hx-trigger="click"
                    _="on click add .flex to #modal and remove .hidden from #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-150 ease-in-out"
                    hx-get="{% url 'servicecoverage_delete' obj.pk %}" {# HTMX GET for delete confirmation #}
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .flex to #modal and remove .hidden from #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="3" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                No Service Coverages found.
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>
<!-- DataTables initialization script is handled by the main list.html's htmx:afterOnLoad listener -->
```

**Form Partial Template (`servicemgmt/servicecoverage/_servicecoverage_form.html`):**
This template contains the form for both creating and updating a `ServiceCoverage`. It's loaded dynamically into the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Service Coverage</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-indicator"> {# hx-swap="none" means HTMX doesn't swap content, relies on 204/HX-Trigger #}
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }} {# Renders the Django form field with its widget #}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600 space-y-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .flex from #modal and add .hidden to #modal"> {# Close modal on cancel #}
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
        </div>
        <!-- HTMX loading indicator -->
        <div id="form-indicator" class="htmx-indicator mt-4 text-center">
            <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            <span class="ml-2 text-gray-600">Saving...</span>
        </div>
    </form>
</div>
```

**Delete Confirmation Partial Template (`servicemgmt/servicecoverage/_servicecoverage_confirm_delete.html`):**
This template provides a confirmation dialog for deletion, loaded dynamically into the modal.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Service Coverage: <strong>{{ object.service_type }}</strong>?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            _="on click remove .flex from #modal and add .hidden to #modal"> {# Close modal on cancel #}
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-post="{% url 'servicecoverage_delete' object.pk %}" {# HTMX POST to trigger deletion #}
            hx-swap="none" {# No content swap, relies on 204 and HX-Trigger #}
            hx-confirm="This action cannot be undone. Confirm deletion?" {# Client-side confirmation #}
            hx-indicator="#delete-indicator"> {# Show loading indicator #}
            Delete
        </button>
    </div>
    <!-- HTMX loading indicator -->
    <div id="delete-indicator" class="htmx-indicator mt-4 text-center">
        <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-red-500"></div>
        <span class="ml-2 text-gray-600">Deleting...</span>
    </div>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

We'll define URL paths for the main list page, the partial table view, and the CRUD operations.

```python
# servicemgmt/urls.py
from django.urls import path
from .views import (
    ServiceCoverageListView, 
    ServiceCoverageTablePartialView,
    ServiceCoverageCreateView, 
    ServiceCoverageUpdateView, 
    ServiceCoverageDeleteView
)

urlpatterns = [
    # Main list page
    path('service-coverages/', ServiceCoverageListView.as_view(), name='servicecoverage_list'),
    
    # HTMX endpoint for the DataTables content
    path('service-coverages/table/', ServiceCoverageTablePartialView.as_view(), name='servicecoverage_table'),
    
    # CRUD endpoints, typically loaded into a modal via HTMX
    path('service-coverages/add/', ServiceCoverageCreateView.as_view(), name='servicecoverage_add'),
    path('service-coverages/edit/<int:pk>/', ServiceCoverageUpdateView.as_view(), name='servicecoverage_edit'),
    path('service-coverages/delete/<int:pk>/', ServiceCoverageDeleteView.as_view(), name='servicecoverage_delete'),
]
```

#### 4.6 Tests

Task: Write tests for the model and views.

Comprehensive tests will cover model field properties, `__str__` method, and all view interactions including HTMX responses.

```python       
# servicemgmt/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import ServiceCoverage

class ServiceCoverageModelTest(TestCase):
    """
    Unit tests for the ServiceCoverage model.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects used by all test methods
        # Use a higher ID to avoid conflicts with auto-increment behavior if adding more objects
        cls.service_coverage1 = ServiceCoverage.objects.create(id=101, service_type='IT Support')
        cls.service_coverage2 = ServiceCoverage.objects.create(id=102, service_type='Consulting')
  
    def test_service_coverage_creation(self):
        """Test that ServiceCoverage objects are created correctly."""
        self.assertEqual(self.service_coverage1.service_type, 'IT Support')
        self.assertEqual(self.service_coverage2.service_type, 'Consulting')
        self.assertEqual(ServiceCoverage.objects.count(), 2)
        
    def test_service_type_label(self):
        """Test the verbose name for the service_type field."""
        field_label = self.service_coverage1._meta.get_field('service_type').verbose_name
        self.assertEqual(field_label, 'Service Type')
        
    def test_db_table_and_managed(self):
        """Verify the Meta options for the model."""
        self.assertEqual(ServiceCoverage._meta.db_table, 'tblMM_Supplier_ServiceCoverage')
        self.assertFalse(ServiceCoverage._meta.managed)

    def test_str_method(self):
        """Test the __str__ representation of the model."""
        self.assertEqual(str(self.service_coverage1), 'IT Support')
        
    def test_ordering(self):
        """Test that objects are ordered by ID in descending order."""
        # Due to default ordering in Meta class, latest created (highest ID) should come first
        latest_coverage = ServiceCoverage.objects.first()
        self.assertEqual(latest_coverage, self.service_coverage2) # id=102

class ServiceCoverageViewsTest(TestCase):
    """
    Integration tests for ServiceCoverage views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        cls.service_coverage = ServiceCoverage.objects.create(id=201, service_type='Initial Service')
    
    def setUp(self):
        # Set up client for each test method
        self.client = Client()
    
    def test_list_view(self):
        """Test the ServiceCoverage list view."""
        response = self.client.get(reverse('servicecoverage_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'servicemgmt/servicecoverage/list.html')
        self.assertContains(response, 'Service Coverages') # Check for heading
        self.assertTrue('service_coverages' in response.context) # Check context
        
    def test_table_partial_view(self):
        """Test the HTMX partial for the DataTables content."""
        response = self.client.get(reverse('servicecoverage_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'servicemgmt/servicecoverage/_servicecoverage_table.html')
        self.assertContains(response, self.service_coverage.service_type) # Check if data is present
        self.assertContains(response, 'id="servicecoverageTable"') # Check if table ID is present
        
    def test_create_view_get(self):
        """Test GET request to the create form."""
        response = self.client.get(reverse('servicecoverage_add'), HTTP_HX_REQUEST='true') # Simulate HTMX request
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'servicemgmt/servicecoverage/_servicecoverage_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Service Coverage')
        
    def test_create_view_post_valid(self):
        """Test POST request with valid data to create a Service Coverage."""
        data = {'service_type': 'New Service Type'}
        # Simulate HTMX request
        response = self.client.post(reverse('servicecoverage_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # Expect 204 No Content for HTMX success
        self.assertTrue(ServiceCoverage.objects.filter(service_type='New Service Type').exists())
        self.assertIn('HX-Trigger', response.headers) # Check for HX-Trigger header
        self.assertIn('refreshServiceCoverageList', response.headers['HX-Trigger'])
        
    def test_create_view_post_invalid(self):
        """Test POST request with invalid data (empty service_type)."""
        data = {'service_type': ''} # Empty string for required field
        response = self.client.post(reverse('servicecoverage_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Expect 200 OK because we're returning the form with errors
        self.assertTemplateUsed(response, 'servicemgmt/servicecoverage/_servicecoverage_form.html')
        self.assertContains(response, 'Service Type cannot be empty.') # Check for validation error message
        self.assertFalse(ServiceCoverage.objects.filter(service_type='').exists()) # Ensure no object created

    def test_update_view_get(self):
        """Test GET request to the update form."""
        response = self.client.get(reverse('servicecoverage_edit', args=[self.service_coverage.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'servicemgmt/servicecoverage/_servicecoverage_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.service_type, 'Initial Service')
        self.assertContains(response, 'Edit Service Coverage')

    def test_update_view_post_valid(self):
        """Test POST request with valid data to update a Service Coverage."""
        new_service_type = 'Updated Service'
        data = {'service_type': new_service_type}
        response = self.client.post(reverse('servicecoverage_edit', args=[self.service_coverage.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.service_coverage.refresh_from_db()
        self.assertEqual(self.service_coverage.service_type, new_service_type)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshServiceCoverageList', response.headers['HX-Trigger'])

    def test_update_view_post_invalid(self):
        """Test POST request with invalid data to update a Service Coverage."""
        original_service_type = self.service_coverage.service_type
        data = {'service_type': ''}
        response = self.client.post(reverse('servicecoverage_edit', args=[self.service_coverage.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'servicemgmt/servicecoverage/_servicecoverage_form.html')
        self.assertContains(response, 'Service Type cannot be empty.')
        self.service_coverage.refresh_from_db()
        self.assertEqual(self.service_coverage.service_type, original_service_type) # Ensure not updated
        
    def test_delete_view_get(self):
        """Test GET request to the delete confirmation page."""
        response = self.client.get(reverse('servicecoverage_delete', args=[self.service_coverage.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'servicemgmt/servicecoverage/_servicecoverage_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post(self):
        """Test POST request to delete a Service Coverage."""
        # Create an object to delete specifically for this test, to avoid interfering with others
        obj_to_delete = ServiceCoverage.objects.create(id=301, service_type='Service to Delete')
        
        response = self.client.post(reverse('servicecoverage_delete', args=[obj_to_delete.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(ServiceCoverage.objects.filter(pk=obj_to_delete.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshServiceCoverageList', response.headers['HX-Trigger'])
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for Dynamic Interactions:** All form submissions (create, update) and delete operations are performed via HTMX `hx-post`. Loading of forms and confirmation dialogs happens via `hx-get` into a modal. List table refreshing is orchestrated using `HX-Trigger` headers (`refreshServiceCoverageList`) on successful CRUD operations and `hx-trigger="load, refreshServiceCoverageList from:body"` on the table container.
- **Alpine.js for UI State:** Alpine.js (or Hyperscript in this case `_="on click..."`) is used for managing the modal's visibility (showing/hiding based on button clicks and HTMX completion).
- **DataTables for List Views:** The main list view's table is managed by DataTables, providing client-side search, sorting, and pagination. It's re-initialized whenever the table partial is re-loaded by HTMX, ensuring dynamic data updates are handled correctly.
- **No Full Page Reloads:** All CRUD operations and view updates are designed to happen asynchronously without full page reloads, providing a modern, responsive user experience.
- **DRY Templates:** Use of partial templates (`_servicecoverage_table.html`, `_servicecoverage_form.html`, `_servicecoverage_confirm_delete.html`) ensures reusability and clean separation of concerns.

### Final Notes

This modernization plan systematically transforms the ASP.NET `ServiceCoverage` module into a modern Django application. By adhering to the principles of fat models, thin views, and HTMX/Alpine.js for frontend, the solution is robust, maintainable, and highly responsive. The focus on automation-driven approaches (like using `managed=False` for existing databases and leveraging HTMX for interactions) significantly reduces manual effort in future migrations. The detailed, runnable code examples ensure clarity for both technical and non-technical stakeholders, facilitating a smooth transition process.