## ASP.NET to Django Conversion Script: Supplier Master Print Details

This modernization plan outlines the automated conversion of your ASP.NET Supplier Master Print Details page to a robust, modern Django application using a fat model/thin view architecture, HTMX, Alpine.js, and DataTables. The focus is on replacing the legacy Crystal Reports functionality with native web rendering, providing a seamless user experience and leveraging the power of Django's ORM for efficient data access.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module (`material_mgt.supplier_master`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination where applicable (for list views).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

The ASP.NET code interacts with multiple tables to gather comprehensive supplier details. Based on the SQL queries and data manipulation, the following tables and their relevant columns have been identified:

-   **`tblMM_Supplier_master`**: The primary table for supplier information.
    -   `SupplierId` (Primary Key, inferred)
    -   `CompId` (Foreign Key to Company, inferred)
    -   `SysDate` (Registration Date)
    -   `RegdCity`, `RegdState`, `RegdCountry` (Foreign Keys to City, State, Country tables for registered address)
    -   `WorkCity`, `WorkState`, `WorkCountry` (Foreign Keys to City, State, Country tables for work address)
    -   `MaterialDelCity`, `MaterialDelState`, `MaterialDelCountry` (Foreign Keys to City, State, Country tables for material delivery address)
    -   `ServiceCoverage` (Foreign Key to `tblMM_Supplier_ServiceCoverage`)
    -   `PF` (Foreign Key to `tblPacking_Master`)
    -   `ExST` (Foreign Key to `tblExciseser_Master`)
    -   `VAT` (Foreign Key to `tblVAT_Master`)
    -   `BusinessType` (Comma-separated string of IDs from `tblMM_Supplier_BusinessType`)
    -   `BusinessNature` (Comma-separated string of IDs from `tblMM_Supplier_BusinessNature`)
    -   `ModVatApplicable` (Integer, 0 for No, >0 for Yes)
    -   `ModVatInvoice` (Integer, 0 for No, >0 for Yes)

-   **`tblMM_Supplier_BusinessType`**: Lookup table for business types.
    -   `Id` (Primary Key)
    -   `Type` (Business type name)

-   **`tblMM_Supplier_BusinessNature`**: Lookup table for business nature.
    -   `Id` (Primary Key)
    -   `Nature` (Business nature name)

-   **`tblMM_Supplier_ServiceCoverage`**: Lookup table for service coverage types.
    -   `Id` (Primary Key)
    -   `Type` (Service coverage type name)

-   **`tblCity`**: Lookup table for cities.
    -   `CityId` (Primary Key)
    -   `CityName` (City name)

-   **`tblState`**: Lookup table for states.
    -   `SId` (Primary Key)
    -   `StateName` (State name)

-   **`tblCountry`**: Lookup table for countries.
    -   `CId` (Primary Key)
    -   `CountryName` (Country name)

-   **`tblPacking_Master`**: Lookup table for packing terms.
    -   `Id` (Primary Key)
    -   `Terms` (Packing terms description)

-   **`tblExciseser_Master`**: Lookup table for excise service terms.
    -   `Id` (Primary Key)
    -   `Terms` (Excise service terms description)

-   **`tblVAT_Master`**: Lookup table for VAT terms.
    -   `Id` (Primary Key)
    -   `Terms` (VAT terms description)

-   **Inferred `tblCompany`**: Based on `Session["compid"]` and `fun.getCompany`, `fun.CompAdd`.
    -   `CompId` (Primary Key)
    -   `CompanyName`
    -   `CompanyAddress`

### Step 2: Identify Backend Functionality

The ASP.NET page `SupplierMaster_Print_Details.aspx` primarily performs a **Read** operation. It fetches a single supplier record and numerous related lookup values to assemble a comprehensive view (formerly for a Crystal Report).

-   **Read**: Retrieves a `SupplierMaster` record based on `SupplierId` and `CompId`. It then performs multiple lookups against various auxiliary tables (`tblCity`, `tblState`, `tblCountry`, `tblMM_Supplier_BusinessType`, etc.) to resolve IDs to names and concatenate values for display.
-   **No explicit Create, Update, Delete** operations are present on this specific page. However, for a complete modernization, Django's CRUD capabilities will be included for the `SupplierMaster` entity.
-   **Business Logic**: The C# code includes logic for:
    -   Formatting dates (`fun.FromDateDMY`).
    -   Parsing comma-separated IDs from `BusinessType` and `BusinessNature` fields to retrieve and concatenate corresponding names.
    -   Converting boolean-like integers (0/1) to "Yes"/"No" strings (`ModVatApplicable`, `ModVatInvoice`).
    -   Retrieving company name and address based on `CompId`.
    These transformations will be moved into the Django models as methods or properties for a "fat model" approach.

### Step 3: Infer UI Components

The original ASP.NET page displayed a Crystal Report viewer. The modernization will replace this with a direct HTML rendering of the supplier's details.

-   **Display Area**: Replaced by structured HTML to present supplier information, registered address, work address, delivery address, business details, tax details, and other relevant lookup data.
-   **"Cancel" Button**: Will be replaced by a standard button that redirects back to the supplier list or a previous page.

For a comprehensive Django solution, a typical `SupplierMaster` module would also include:
-   **List View**: A table (using DataTables) to display all suppliers, with search, sort, and pagination.
-   **Add/Edit Forms**: Modals (using HTMX) for creating and updating supplier records.
-   **Delete Confirmation**: A modal (using HTMX) for confirming deletions.

### Step 4: Generate Django Code

We will create a new Django application named `material_mgt` and structure the files within it.

#### 4.1 Models (`material_mgt/supplier_master/models.py`)

This file defines the Django models that map to your existing database tables. The `SupplierMaster` model will be "fat," containing methods to encapsulate the complex data lookups and transformations previously handled in the C# code.

```python
from django.db import models
from django.utils import timezone
from django.utils.html import format_html_join

# --- Helper Models (Lookups and related entities) ---

class Company(models.Model):
    # This model is inferred from 'CompId' and 'fun.getCompany', 'fun.CompAdd'
    # Assume it has fields like CompanyName, CompanyAddress
    CompId = models.IntegerField(db_column='CompId', primary_key=True)
    CompanyName = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    CompanyAddress = models.CharField(db_column='CompanyAddress', max_length=500, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany' # Inferred table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.CompanyName or f"Company ID: {self.CompId}"

class City(models.Model):
    CityId = models.IntegerField(db_column='CityId', primary_key=True)
    CityName = models.CharField(db_column='CityName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.CityName or ''

class State(models.Model):
    SId = models.IntegerField(db_column='SId', primary_key=True)
    StateName = models.CharField(db_column='StateName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.StateName or ''

class Country(models.Model):
    CId = models.IntegerField(db_column='CId', primary_key=True)
    CountryName = models.CharField(db_column='CountryName', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.CountryName or ''

class SupplierBusinessType(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Type = models.CharField(db_column='Type', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_BusinessType'
        verbose_name = 'Supplier Business Type'
        verbose_name_plural = 'Supplier Business Types'

    def __str__(self):
        return self.Type or ''

class SupplierBusinessNature(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Nature = models.CharField(db_column='Nature', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_BusinessNature'
        verbose_name = 'Supplier Business Nature'
        verbose_name_plural = 'Supplier Business Natures'

    def __str__(self):
        return self.Nature or ''

class SupplierServiceCoverage(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Type = models.CharField(db_column='Type', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_ServiceCoverage'
        verbose_name = 'Supplier Service Coverage'
        verbose_name_plural = 'Supplier Service Coverages'

    def __str__(self):
        return self.Type or ''

class PackingMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.Terms or ''

class ExciseMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Service Term'
        verbose_name_plural = 'Excise Service Terms'

    def __str__(self):
        return self.Terms or ''

class VATMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'

    def __str__(self):
        return self.Terms or ''


# --- Main Supplier Master Model ---

class SupplierMaster(models.Model):
    SupplierId = models.IntegerField(db_column='SupplierId', primary_key=True)
    CompId = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    SysDate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    
    # Registered Address FKs
    RegdCity = models.ForeignKey(City, models.DO_NOTHING, db_column='RegdCity', related_name='regd_suppliers', blank=True, null=True)
    RegdState = models.ForeignKey(State, models.DO_NOTHING, db_column='RegdState', related_name='regd_suppliers', blank=True, null=True)
    RegdCountry = models.ForeignKey(Country, models.DO_NOTHING, db_column='RegdCountry', related_name='regd_suppliers', blank=True, null=True)
    
    # Work Address FKs
    WorkCity = models.ForeignKey(City, models.DO_NOTHING, db_column='WorkCity', related_name='work_suppliers', blank=True, null=True)
    WorkState = models.ForeignKey(State, models.DO_NOTHING, db_column='WorkState', related_name='work_suppliers', blank=True, null=True)
    WorkCountry = models.ForeignKey(Country, models.DO_NOTHING, db_column='WorkCountry', related_name='work_suppliers', blank=True, null=True)
    
    # Material Delivery Address FKs
    MaterialDelCity = models.ForeignKey(City, models.DO_NOTHING, db_column='MaterialDelCity', related_name='del_suppliers', blank=True, null=True)
    MaterialDelState = models.ForeignKey(State, models.DO_NOTHING, db_column='MaterialDelState', related_name='del_suppliers', blank=True, null=True)
    MaterialDelCountry = models.ForeignKey(Country, models.DO_NOTHING, db_column='MaterialDelCountry', related_name='del_suppliers', blank=True, null=True)
    
    ServiceCoverage = models.ForeignKey(SupplierServiceCoverage, models.DO_NOTHING, db_column='ServiceCoverage', blank=True, null=True)
    PF = models.ForeignKey(PackingMaster, models.DO_NOTHING, db_column='PF', blank=True, null=True)
    ExST = models.ForeignKey(ExciseMaster, models.DO_NOTHING, db_column='ExST', blank=True, null=True)
    VAT = models.ForeignKey(VATMaster, models.DO_NOTHING, db_column='VAT', blank=True, null=True)
    
    BusinessType = models.CharField(db_column='BusinessType', max_length=500, blank=True, null=True) # Comma-separated IDs
    BusinessNature = models.CharField(db_column='BusinessNature', max_length=500, blank=True, null=True) # Comma-separated IDs
    ModVatApplicable = models.IntegerField(db_column='ModVatApplicable', blank=True, null=True) # 0 or 1
    ModVatInvoice = models.IntegerField(db_column='ModVatInvoice', blank=True, null=True) # 0 or 1
    
    # Add other potentially relevant fields if they exist in tblMM_Supplier_master
    # For example, SupplierName, ContactPerson, Address etc.
    # Assuming there's a primary supplier name field. Let's add one for demonstration:
    SupplierName = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    SupplierAddress = models.CharField(db_column='SupplierAddress', max_length=500, blank=True, null=True)


    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return self.SupplierName or f"Supplier ID: {self.SupplierId}"

    # --- Business Logic Methods (Fat Model) ---

    @property
    def formatted_sys_date(self):
        """Formats the SysDate to DD/MM/YYYY similar to fun.FromDateDMY."""
        if self.SysDate:
            return self.SysDate.strftime('%d/%m/%Y')
        return "N/A"

    @property
    def get_regd_address(self):
        """Returns the full registered address string."""
        parts = [
            self.RegdCity.CityName if self.RegdCity else '',
            self.RegdState.StateName if self.RegdState else '',
            self.RegdCountry.CountryName if self.RegdCountry else ''
        ]
        return ', '.join(filter(None, parts)) or 'N/A'
    
    @property
    def get_work_address(self):
        """Returns the full work address string."""
        parts = [
            self.WorkCity.CityName if self.WorkCity else '',
            self.WorkState.StateName if self.WorkState else '',
            self.WorkCountry.CountryName if self.WorkCountry else ''
        ]
        return ', '.join(filter(None, parts)) or 'N/A'
    
    @property
    def get_del_address(self):
        """Returns the full material delivery address string."""
        parts = [
            self.MaterialDelCity.CityName if self.MaterialDelCity else '',
            self.MaterialDelState.StateName if self.MaterialDelState else '',
            self.MaterialDelCountry.CountryName if self.MaterialDelCountry else ''
        ]
        return ', '.join(filter(None, parts)) or 'N/A'

    @property
    def get_business_types_str(self):
        """Parses comma-separated BusinessType IDs and returns concatenated names."""
        if not self.BusinessType:
            return "N/A"
        
        ids = [int(i.strip()) for i in self.BusinessType.split(',') if i.strip().isdigit()]
        types = SupplierBusinessType.objects.filter(Id__in=ids).values_list('Type', flat=True)
        return ', '.join(types) or "N/A"

    @property
    def get_business_nature_str(self):
        """Parses comma-separated BusinessNature IDs and returns concatenated names."""
        if not self.BusinessNature:
            return "N/A"

        ids = [int(i.strip()) for i in self.BusinessNature.split(',') if i.strip().isdigit()]
        natures = SupplierBusinessNature.objects.filter(Id__in=ids).values_list('Nature', flat=True)
        return ', '.join(natures) or "N/A"
    
    @property
    def get_service_coverage_type(self):
        """Returns the ServiceCoverage Type name."""
        return self.ServiceCoverage.Type if self.ServiceCoverage else "N/A"

    @property
    def get_pf_terms(self):
        """Returns the Packing Terms."""
        return self.PF.Terms if self.PF else "N/A"

    @property
    def get_exst_terms(self):
        """Returns the Excise Service Terms."""
        return self.ExST.Terms if self.ExST else "N/A"

    @property
    def get_vat_terms(self):
        """Returns the VAT Terms."""
        return self.VAT.Terms if self.VAT else "N/A"

    @property
    def mod_vat_applicable_status(self):
        """Converts ModVatApplicable (int) to 'Yes'/'No'."""
        return 'Yes' if self.ModVatApplicable and self.ModVatApplicable > 0 else 'No'

    @property
    def mod_vat_invoice_status(self):
        """Converts ModVatInvoice (int) to 'Yes'/'No'."""
        return 'Yes' if self.ModVatInvoice and self.ModVatInvoice > 0 else 'No'

    @property
    def company_name(self):
        """Returns the associated company name."""
        return self.CompId.CompanyName if self.CompId else "N/A"

    @property
    def company_address(self):
        """Returns the associated company address."""
        return self.CompId.CompanyAddress if self.CompId else "N/A"

```

#### 4.2 Forms (`material_mgt/supplier_master/forms.py`)

While the original page was for printing, a modern Django application requires forms for CRUD operations. Here's a `ModelForm` for `SupplierMaster`. Note that for fields like `BusinessType` and `BusinessNature` which are stored as comma-separated IDs, you might need custom form fields or overridden `save` methods if you want to handle them as multi-selects in the UI. For simplicity, this example treats them as plain text inputs for now.

```python
from django import forms
from .models import SupplierMaster, City, State, Country, SupplierBusinessType, SupplierBusinessNature, \
    SupplierServiceCoverage, PackingMaster, ExciseMaster, VATMaster

class SupplierMasterForm(forms.ModelForm):
    class Meta:
        model = SupplierMaster
        fields = [
            'SupplierName', 'SupplierAddress', 'SysDate', 'CompId',
            'RegdCity', 'RegdState', 'RegdCountry',
            'WorkCity', 'WorkState', 'WorkCountry',
            'MaterialDelCity', 'MaterialDelState', 'MaterialDelCountry',
            'ServiceCoverage', 'PF', 'ExST', 'VAT',
            'BusinessType', 'BusinessNature', 'ModVatApplicable', 'ModVatInvoice'
        ]
        widgets = {
            'SupplierName': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SupplierAddress': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'SysDate': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'CompId': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            
            'RegdCity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'RegdState': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'RegdCountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'WorkCity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'WorkState': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'WorkCountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'MaterialDelCity': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'MaterialDelState': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'MaterialDelCountry': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),

            'ServiceCoverage': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'PF': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ExST': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'VAT': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            
            # For BusinessType and BusinessNature, if they are comma-separated IDs, 
            # a CharField/TextInput is used here. For actual multi-select,
            # a custom form field or multiple choice field would be needed.
            'BusinessType': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g. 1,5,10'}),
            'BusinessNature': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g. 2,3'}),

            'ModVatApplicable': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
            'ModVatInvoice': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
        
    def clean_ModVatApplicable(self):
        # Convert boolean to 0 or 1 for database storage if needed
        return 1 if self.cleaned_data.get('ModVatApplicable') else 0

    def clean_ModVatInvoice(self):
        # Convert boolean to 0 or 1 for database storage if needed
        return 1 if self.cleaned_data.get('ModVatInvoice') else 0

```

#### 4.3 Views (`material_mgt/supplier_master/views.py`)

The views will be thin, delegating all data retrieval and business logic to the models. A `DetailView` is provided for the "print details" functionality, alongside standard CRUD views for `SupplierMaster`.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import SupplierMaster, City, State, Country, SupplierBusinessType, SupplierBusinessNature, \
    SupplierServiceCoverage, PackingMaster, ExciseMaster, VATMaster, Company
from .forms import SupplierMasterForm
import logging

logger = logging.getLogger(__name__)

class SupplierMasterListView(ListView):
    model = SupplierMaster
    template_name = 'material_mgt/supplier_master/list.html'
    context_object_name = 'supplier_masters'

class SupplierMasterTablePartialView(TemplateView):
    # This view is for HTMX to load the DataTables content dynamically
    template_name = 'material_mgt/supplier_master/_supplier_master_table.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['supplier_masters'] = SupplierMaster.objects.all() # Fetch all suppliers for the table
        return context

class SupplierMasterDetailView(DetailView):
    model = SupplierMaster
    template_name = 'material_mgt/supplier_master/detail.html'
    context_object_name = 'supplier'
    # No extra logic here, all data preparation is in the model properties

class SupplierMasterCreateView(CreateView):
    model = SupplierMaster
    form_class = SupplierMasterForm
    template_name = 'material_mgt/supplier_master/_supplier_master_form.html'
    success_url = reverse_lazy('supplier_master_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier Master added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return 204 No Content for HTMX to prevent full page reload
            # Trigger a custom event to refresh the list table
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # For HTMX, return the form with errors for re-rendering the modal
            return response
        return response


class SupplierMasterUpdateView(UpdateView):
    model = SupplierMaster
    form_class = SupplierMasterForm
    template_name = 'material_mgt/supplier_master/_supplier_master_form.html'
    success_url = reverse_lazy('supplier_master_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier Master updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return response
        return response


class SupplierMasterDeleteView(DeleteView):
    model = SupplierMaster
    template_name = 'material_mgt/supplier_master/_supplier_master_confirm_delete.html'
    success_url = reverse_lazy('supplier_master_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier Master deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierMasterList'
                }
            )
        return response

```

#### 4.4 Templates (`material_mgt/supplier_master/templates/material_mgt/supplier_master/`)

**`list.html`**: The main page for listing supplier masters. It uses HTMX to load the DataTables content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Supplier Masters</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'supplier_master_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Supplier Master
        </button>
    </div>
    
    <div id="supplierMasterTable-container"
         hx-trigger="load, refreshSupplierMasterList from:body"
         hx-get="{% url 'supplier_master_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Supplier Masters...</p>
        </div>
    </div>
    
    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full mx-4 sm:mx-0"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
        // For example, if you had a complex UI state within the page itself
    });
</script>
{% endblock %}
```

**`_supplier_master_table.html`**: A partial template to be loaded by HTMX for the DataTables.

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="supplierMasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registered Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in supplier_masters %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.SupplierName|default:"N/A" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.formatted_sys_date }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.company_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'supplier_master_detail' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        View
                    </button>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'supplier_master_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'supplier_master_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-6 text-center text-sm text-gray-500">No supplier masters found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables
    $(document).ready(function() {
        $('#supplierMasterTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
        });
    });
</script>
```

**`detail.html`**: Replaces the Crystal Report Viewer, displaying all resolved supplier details. This will be loaded into a modal via HTMX.

```html
<div class="p-6">
    <h3 class="text-2xl font-bold text-gray-900 mb-6">Supplier Details: {{ supplier.SupplierName|default:"N/A" }}</h3>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-3">General Information</h4>
            <div class="space-y-2">
                <p><strong class="text-gray-700">Supplier ID:</strong> {{ supplier.SupplierId }}</p>
                <p><strong class="text-gray-700">Registered Date:</strong> {{ supplier.formatted_sys_date }}</p>
                <p><strong class="text-gray-700">Company:</strong> {{ supplier.company_name }}</p>
                <p><strong class="text-gray-700">Company Address:</strong> {{ supplier.company_address }}</p>
                <p><strong class="text-gray-700">Supplier Address:</strong> {{ supplier.SupplierAddress|default:"N/A" }}</p>
            </div>
        </div>

        <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-3">Business Details</h4>
            <div class="space-y-2">
                <p><strong class="text-gray-700">Business Type:</strong> {{ supplier.get_business_types_str }}</p>
                <p><strong class="text-gray-700">Business Nature:</strong> {{ supplier.get_business_nature_str }}</p>
                <p><strong class="text-gray-700">Service Coverage:</strong> {{ supplier.get_service_coverage_type }}</p>
                <p><strong class="text-gray-700">MODVAT Applicable:</strong> {{ supplier.mod_vat_applicable_status }}</p>
                <p><strong class="text-gray-700">MODVAT Invoice:</strong> {{ supplier.mod_vat_invoice_status }}</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-3">Registered Address</h4>
            <div class="space-y-2">
                <p><strong class="text-gray-700">City:</strong> {{ supplier.RegdCity.CityName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">State:</strong> {{ supplier.RegdState.StateName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">Country:</strong> {{ supplier.RegdCountry.CountryName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">Full:</strong> {{ supplier.get_regd_address }}</p>
            </div>
        </div>

        <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-3">Work Address</h4>
            <div class="space-y-2">
                <p><strong class="text-gray-700">City:</strong> {{ supplier.WorkCity.CityName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">State:</strong> {{ supplier.WorkState.StateName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">Country:</strong> {{ supplier.WorkCountry.CountryName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">Full:</strong> {{ supplier.get_work_address }}</p>
            </div>
        </div>

        <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-3">Material Delivery Address</h4>
            <div class="space-y-2">
                <p><strong class="text-gray-700">City:</strong> {{ supplier.MaterialDelCity.CityName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">State:</strong> {{ supplier.MaterialDelState.StateName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">Country:</strong> {{ supplier.MaterialDelCountry.CountryName|default:"N/A" }}</p>
                <p><strong class="text-gray-700">Full:</strong> {{ supplier.get_del_address }}</p>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div>
            <h4 class="text-lg font-semibold text-gray-800 mb-3">Tax & Other Terms</h4>
            <div class="space-y-2">
                <p><strong class="text-gray-700">PF Terms:</strong> {{ supplier.get_pf_terms }}</p>
                <p><strong class="text-gray-700">Excise Service Terms:</strong> {{ supplier.get_exst_terms }}</p>
                <p><strong class="text-gray-700">VAT Terms:</strong> {{ supplier.get_vat_terms }}</p>
            </div>
        </div>
    </div>

    <div class="mt-6 flex items-center justify-end">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Close
        </button>
    </div>
</div>
```

**`_supplier_master_form.html`**: Partial template for create/update forms, designed for HTMX modals.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Supplier Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_supplier_master_confirm_delete.html`**: Partial template for delete confirmation, designed for HTMX modals.

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete supplier "{{ object.SupplierName|default:"N/A" }}" (ID: {{ object.pk }})?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`material_mgt/supplier_master/urls.py`)

This file defines the URL patterns for accessing the supplier master views.

```python
from django.urls import path
from .views import (
    SupplierMasterListView, 
    SupplierMasterCreateView, 
    SupplierMasterUpdateView, 
    SupplierMasterDeleteView,
    SupplierMasterDetailView,
    SupplierMasterTablePartialView
)

urlpatterns = [
    path('supplier-masters/', SupplierMasterListView.as_view(), name='supplier_master_list'),
    path('supplier-masters/add/', SupplierMasterCreateView.as_view(), name='supplier_master_add'),
    path('supplier-masters/<int:pk>/edit/', SupplierMasterUpdateView.as_view(), name='supplier_master_edit'),
    path('supplier-masters/<int:pk>/delete/', SupplierMasterDeleteView.as_view(), name='supplier_master_delete'),
    path('supplier-masters/<int:pk>/detail/', SupplierMasterDetailView.as_view(), name='supplier_master_detail'),
    path('supplier-masters/table/', SupplierMasterTablePartialView.as_view(), name='supplier_master_table'),
]
```

#### 4.6 Tests (`material_mgt/supplier_master/tests.py`)

Comprehensive tests for models and views. Note: For `managed=False` models, you often need to create mock data or ensure your test database setup includes the necessary tables and data. Here, `setUpTestData` creates minimal mock data for testing.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    SupplierMaster, Company, City, State, Country, 
    SupplierBusinessType, SupplierBusinessNature, SupplierServiceCoverage, 
    PackingMaster, ExciseMaster, VATMaster
)
from datetime import datetime

class SupplierMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related lookup tables
        cls.company = Company.objects.create(CompId=1, CompanyName="Test Company", CompanyAddress="123 Test St")
        cls.city1 = City.objects.create(CityId=1, CityName="Mumbai")
        cls.city2 = City.objects.create(CityId=2, CityName="Delhi")
        cls.state1 = State.objects.create(SId=1, StateName="Maharashtra")
        cls.country1 = Country.objects.create(CId=1, CountryName="India")
        cls.biz_type1 = SupplierBusinessType.objects.create(Id=1, Type="Manufacturer")
        cls.biz_type2 = SupplierBusinessType.objects.create(Id=2, Type="Supplier")
        cls.biz_nature1 = SupplierBusinessNature.objects.create(Id=1, Nature="Textile")
        cls.svc_cov1 = SupplierServiceCoverage.objects.create(Id=1, Type="National")
        cls.pf_term1 = PackingMaster.objects.create(Id=1, Terms="FOB")
        cls.exst_term1 = ExciseMaster.objects.create(Id=1, Terms="Ex-Factory")
        cls.vat_term1 = VATMaster.objects.create(Id=1, Terms="VAT @ 5%")

        # Create test supplier master
        SupplierMaster.objects.create(
            SupplierId=101,
            SupplierName="Alpha Corp",
            SupplierAddress="456 Main Rd",
            CompId=cls.company,
            SysDate=datetime(2023, 1, 15, 10, 30, 0),
            RegdCity=cls.city1, RegdState=cls.state1, RegdCountry=cls.country1,
            WorkCity=cls.city1, WorkState=cls.state1, WorkCountry=cls.country1,
            MaterialDelCity=cls.city2, MaterialDelState=cls.state1, MaterialDelCountry=cls.country1,
            ServiceCoverage=cls.svc_cov1,
            PF=cls.pf_term1, ExST=cls.exst_term1, VAT=cls.vat_term1,
            BusinessType="1,2", # Manufacturer, Supplier
            BusinessNature="1",  # Textile
            ModVatApplicable=1,
            ModVatInvoice=0,
        )
  
    def test_supplier_creation(self):
        supplier = SupplierMaster.objects.get(SupplierId=101)
        self.assertEqual(supplier.SupplierName, "Alpha Corp")
        self.assertEqual(supplier.RegdCity.CityName, "Mumbai")
        self.assertEqual(supplier.CompId.CompanyName, "Test Company")

    def test_formatted_sys_date_property(self):
        supplier = SupplierMaster.objects.get(SupplierId=101)
        self.assertEqual(supplier.formatted_sys_date, "15/01/2023")

    def test_get_regd_address_property(self):
        supplier = SupplierMaster.objects.get(SupplierId=101)
        self.assertEqual(supplier.get_regd_address, "Mumbai, Maharashtra, India")

    def test_get_business_types_str_property(self):
        supplier = SupplierMaster.objects.get(SupplierId=101)
        self.assertEqual(supplier.get_business_types_str, "Manufacturer, Supplier")

    def test_get_business_nature_str_property(
        self):
        supplier = SupplierMaster.objects.get(SupplierId=101)
        self.assertEqual(supplier.get_business_nature_str, "Textile")

    def test_mod_vat_applicable_status_property(self):
        supplier = SupplierMaster.objects.get(SupplierId=101)
        self.assertEqual(supplier.mod_vat_applicable_status, "Yes")
        # Test 'No' case
        supplier.ModVatApplicable = 0
        supplier.save()
        self.assertEqual(supplier.mod_vat_applicable_status, "No")

    def test_company_name_property(self):
        supplier = SupplierMaster.objects.get(SupplierId=101)
        self.assertEqual(supplier.company_name, "Test Company")


class SupplierMasterViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related lookup tables and a supplier
        cls.company = Company.objects.create(CompId=1, CompanyName="Test Company", CompanyAddress="123 Test St")
        cls.city1 = City.objects.create(CityId=1, CityName="Mumbai")
        cls.city2 = City.objects.create(CityId=2, CityName="Delhi")
        cls.state1 = State.objects.create(SId=1, StateName="Maharashtra")
        cls.country1 = Country.objects.create(CId=1, CountryName="India")
        cls.biz_type1 = SupplierBusinessType.objects.create(Id=1, Type="Manufacturer")
        cls.biz_type2 = SupplierBusinessType.objects.create(Id=2, Type="Supplier")
        cls.biz_nature1 = SupplierBusinessNature.objects.create(Id=1, Nature="Textile")
        cls.svc_cov1 = SupplierServiceCoverage.objects.create(Id=1, Type="National")
        cls.pf_term1 = PackingMaster.objects.create(Id=1, Terms="FOB")
        cls.exst_term1 = ExciseMaster.objects.create(Id=1, Terms="Ex-Factory")
        cls.vat_term1 = VATMaster.objects.create(Id=1, Terms="VAT @ 5%")
        
        cls.supplier = SupplierMaster.objects.create(
            SupplierId=101,
            SupplierName="Gamma Inc.",
            SupplierAddress="789 Prod St",
            CompId=cls.company,
            SysDate=datetime(2023, 2, 20, 9, 0, 0),
            RegdCity=cls.city1, RegdState=cls.state1, RegdCountry=cls.country1,
            WorkCity=cls.city1, WorkState=cls.state1, WorkCountry=cls.country1,
            MaterialDelCity=cls.city2, MaterialDelState=cls.state1, MaterialDelCountry=cls.country1,
            ServiceCoverage=cls.svc_cov1,
            PF=cls.pf_term1, ExST=cls.exst_term1, VAT=cls.vat_term1,
            BusinessType="1",
            BusinessNature="1",
            ModVatApplicable=1,
            ModVatInvoice=1,
        )
    
    def test_list_view(self):
        response = self.client.get(reverse('supplier_master_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_mgt/supplier_master/list.html')
        self.assertContains(response, 'Supplier Masters')

    def test_table_partial_view(self):
        response = self.client.get(reverse('supplier_master_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_mgt/supplier_master/_supplier_master_table.html')
        self.assertContains(response, self.supplier.SupplierName)
        self.assertTrue('supplier_masters' in response.context)

    def test_detail_view(self):
        response = self.client.get(reverse('supplier_master_detail', args=[self.supplier.SupplierId]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_mgt/supplier_master/detail.html')
        self.assertContains(response, self.supplier.SupplierName)
        self.assertContains(response, self.supplier.get_business_types_str) # Test a fat model property

    def test_create_view_get(self):
        response = self.client.get(reverse('supplier_master_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_mgt/supplier_master/_supplier_master_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'SupplierId': 102, # Must be unique primary key
            'SupplierName': 'New Supplier Ltd.',
            'SupplierAddress': '10 New Place',
            'SysDate': '2024-01-01',
            'CompId': self.company.CompId,
            'RegdCity': self.city1.CityId,
            'RegdState': self.state1.SId,
            'RegdCountry': self.country1.CId,
            'WorkCity': self.city1.CityId,
            'WorkState': self.state1.SId,
            'WorkCountry': self.country1.CId,
            'MaterialDelCity': self.city2.CityId,
            'MaterialDelState': self.state1.SId,
            'MaterialDelCountry': self.country1.CId,
            'ServiceCoverage': self.svc_cov1.Id,
            'PF': self.pf_term1.Id,
            'ExST': self.exst_term1.Id,
            'VAT': self.vat_term1.Id,
            'BusinessType': '1',
            'BusinessNature': '1',
            'ModVatApplicable': True,
            'ModVatInvoice': False,
        }
        response = self.client.post(reverse('supplier_master_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertTrue(SupplierMaster.objects.filter(SupplierName='New Supplier Ltd.').exists())
        
    def test_create_view_post_htmx_success(self):
        data = {
            'SupplierId': 103,
            'SupplierName': 'HTMX Supplier',
            'SupplierAddress': '20 HTMX Ave',
            'SysDate': '2024-01-02',
            'CompId': self.company.CompId,
            'RegdCity': self.city1.CityId, 'RegdState': self.state1.SId, 'RegdCountry': self.country1.CId,
            'WorkCity': self.city1.CityId, 'WorkState': self.state1.SId, 'WorkCountry': self.country1.CId,
            'MaterialDelCity': self.city2.CityId, 'MaterialDelState': self.state1.SId, 'MaterialDelCountry': self.country1.CId,
            'ServiceCoverage': self.svc_cov1.Id,
            'PF': self.pf_term1.Id, 'ExST': self.exst_term1.Id, 'VAT': self.vat_term1.Id,
            'BusinessType': '1', 'BusinessNature': '1',
            'ModVatApplicable': True, 'ModVatInvoice': False,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('supplier_master_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSupplierMasterList')
        self.assertTrue(SupplierMaster.objects.filter(SupplierName='HTMX Supplier').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('supplier_master_edit', args=[self.supplier.SupplierId]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_mgt/supplier_master/_supplier_master_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, self.supplier)

    def test_update_view_post_success(self):
        updated_name = "Gamma Inc. (Updated)"
        data = {
            'SupplierId': self.supplier.SupplierId, # Primary key must be present for update
            'SupplierName': updated_name,
            'SupplierAddress': '789 Prod St - Updated',
            'SysDate': '2023-02-20',
            'CompId': self.company.CompId,
            'RegdCity': self.city1.CityId, 'RegdState': self.state1.SId, 'RegdCountry': self.country1.CId,
            'WorkCity': self.city1.CityId, 'WorkState': self.state1.SId, 'WorkCountry': self.country1.CId,
            'MaterialDelCity': self.city2.CityId, 'MaterialDelState': self.state1.SId, 'MaterialDelCountry': self.country1.CId,
            'ServiceCoverage': self.svc_cov1.Id,
            'PF': self.pf_term1.Id, 'ExST': self.exst_term1.Id, 'VAT': self.vat_term1.Id,
            'BusinessType': '1', 'BusinessNature': '1',
            'ModVatApplicable': True, 'ModVatInvoice': True,
        }
        response = self.client.post(reverse('supplier_master_edit', args=[self.supplier.SupplierId]), data)
        self.assertEqual(response.status_code, 302)
        self.supplier.refresh_from_db()
        self.assertEqual(self.supplier.SupplierName, updated_name)

    def test_delete_view_get(self):
        response = self.client.get(reverse('supplier_master_delete', args=[self.supplier.SupplierId]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_mgt/supplier_master/_supplier_master_confirm_delete.html')
        self.assertTrue('object' in response.context)
        
    def test_delete_view_post_success(self):
        # Create a new supplier to delete so it doesn't affect other tests
        supplier_to_delete = SupplierMaster.objects.create(
            SupplierId=104, SupplierName="Temp Supplier",
            CompId=self.company, SysDate=datetime.now(),
            RegdCity=self.city1, RegdState=self.state1, RegdCountry=self.country1,
            WorkCity=self.city1, WorkState=self.state1, WorkCountry=self.country1,
            MaterialDelCity=self.city2, MaterialDelState=self.state1, MaterialDelCountry=self.country1,
            ServiceCoverage=self.svc_cov1,
            PF=self.pf_term1, ExST=self.exst_term1, VAT=self.vat_term1,
            ModVatApplicable=0, ModVatInvoice=0,
        )
        response = self.client.post(reverse('supplier_master_delete', args=[supplier_to_delete.SupplierId]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(SupplierMaster.objects.filter(SupplierId=supplier_to_delete.SupplierId).exists())

    def test_delete_view_post_htmx_success(self):
        supplier_to_delete = SupplierMaster.objects.create(
            SupplierId=105, SupplierName="HTMX Delete Supplier",
            CompId=self.company, SysDate=datetime.now(),
            RegdCity=self.city1, RegdState=self.state1, RegdCountry=self.country1,
            WorkCity=self.city1, WorkState=self.state1, WorkCountry=self.country1,
            MaterialDelCity=self.city2, MaterialDelState=self.state1, MaterialDelCountry=self.country1,
            ServiceCoverage=self.svc_cov1,
            PF=self.pf_term1, ExST=self.exst_term1, VAT=self.vat_term1,
            ModVatApplicable=0, ModVatInvoice=0,
        )
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('supplier_master_delete', args=[supplier_to_delete.SupplierId]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshSupplierMasterList')
        self.assertFalse(SupplierMaster.objects.filter(SupplierId=supplier_to_delete.SupplierId).exists())

```

### Step 5: HTMX and Alpine.js Integration

-   **HTMX for dynamic content**:
    -   The main `list.html` page uses `hx-get` to dynamically load the `_supplier_master_table.html` partial.
    -   `hx-trigger="load, refreshSupplierMasterList from:body"` ensures the table loads on page load and refreshes whenever a `refreshSupplierMasterList` custom event is triggered (after CUD operations).
    -   `hx-target="#modalContent"` and `hx-trigger="click"` are used on "Add", "Edit", "View", and "Delete" buttons to load corresponding forms/details into a modal.
    -   Form submissions (`hx-post`) on `_supplier_master_form.html` and `_supplier_master_confirm_delete.html` prevent full page reloads and send back a `204 No Content` response with an `HX-Trigger` header to refresh the main list.
-   **Alpine.js for UI state**:
    -   `_ = "on click add .is-active to #modal"` and `_ = "on click remove .is-active from me"` (or `_ = "on click if event.target.id == 'modal' remove .is-active from me"`) are used to manage the visibility of the modal directly from HTML attributes, providing simple, declarative UI control without custom JavaScript.
-   **DataTables for list views**:
    -   The `_supplier_master_table.html` partial includes the JavaScript to initialize DataTables on the `supplierMasterTable` element, enabling client-side search, sort, and pagination. It relies on the DataTables library being included in `core/base.html`.
-   **No Custom JavaScript**: All dynamic interactions are achieved through HTMX attributes and Alpine.js directives, minimizing the need for imperative JavaScript.

---

### Final Notes

-   This plan replaces the legacy Crystal Reports with a modern, web-native display of supplier details.
-   Placeholders like `[APP_NAME]`, `[MODEL_NAME]`, `[FIELD1]` have been replaced with concrete names (`material_mgt`, `SupplierMaster`, `SupplierName`, etc.) derived from your ASP.NET code analysis.
-   The "fat model" approach centralizes business logic, making views thin and reusable.
-   The solution emphasizes automation by providing a clear, file-by-file structure that can be generated and tested systematically.
-   The use of HTMX and Alpine.js ensures a highly interactive and responsive user experience without the complexity of a traditional JavaScript framework.
-   Ensure your Django project's `settings.py` includes `material_mgt` in `INSTALLED_APPS` and that your root `urls.py` includes the `material_mgt.supplier_master.urls`.
-   Database connection details for `managed=False` models must be configured correctly in Django's `settings.py` to point to your existing SQL Server database.