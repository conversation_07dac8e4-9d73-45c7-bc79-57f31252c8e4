This comprehensive modernization plan outlines the strategic transition of your legacy ASP.NET "Buyer Master" module to a modern, efficient Django application. By leveraging AI-assisted automation, we aim to minimize manual effort, ensure data integrity, and provide a scalable foundation for future development.

The core approach focuses on:
*   **Business Value:** Enhancing user experience with dynamic interfaces, improving data accuracy through robust validation, and streamlining operations with efficient data management.
*   **Technical Excellence:** Adopting Django's "Fat Model, Thin View" paradigm, implementing HTMX for seamless user interactions without complex JavaScript, and utilizing DataTables for superior data presentation.
*   **Automation Focus:** Designing components that can be systematically converted and tested, reducing the need for extensive manual recoding.

---

## ASP.NET to Django Conversion Script: Buyer Master

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET `SqlDataSource1` and code-behind reveal two primary tables involved: `tblMM_Buyer_Master` and `tblHR_OfficeStaff`.

*   **Primary Table:** `tblMM_Buyer_Master`
    *   `Id`: Integer, Primary Key (DataKeyNames="Id").
    *   `Category`: String (max length implied by single char dropdown, e.g., 'A', 'B').
    *   `Nos`: String (validated as numeric, up to 15 digits with optional 3 decimal places).
    *   `EmpId`: String, Foreign Key to `tblHR_OfficeStaff.EmpId`.

*   **Related Table (for Buyer lookup):** `tblHR_OfficeStaff`
    *   `EmpId`: String, Primary Key.
    *   `EmployeeName`: String.
    *   `CompId`: Integer (used for filtering in select command, usually from session).

**Business Benefit:** Accurately mapping existing database structures ensures seamless data migration and preserves historical data, preventing data loss and minimizing disruption to ongoing operations. This automated mapping reduces manual data definition errors.

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET `GridView` and its event handlers manage the core CRUD operations.

*   **Create (Insert):** Handled by `GridView1_RowCommand` (specifically `CommandName="Add"` and `CommandName="Add1"`).
    *   **Logic:** Retrieves `Category`, `Nos`, and `EmpId` from the footer/empty template controls.
    *   **Validation:** Checks if `Category` is "Select", `Nos` is empty, `EmpId` is empty. Performs a *duplicate check* to ensure no existing record has the same `Nos` and `Category` combination.
    *   **Employee ID Extraction:** Uses `fun.getCode()` to extract the `EmpId` from an `EmployeeName [EmpId]` formatted string provided by the `AutoCompleteExtender`.

*   **Read (Select):** Handled by `SqlDataSource1.SelectCommand`.
    *   **Logic:** Selects `Category`, `Nos`, `Id`, `EmployeeName` (concatenated with `EmpId`), and `EmpId` from `tblMM_Buyer_Master` joined with `tblHR_OfficeStaff` on `EmpId`. Filters records by `CompId` from the user's session.

*   **Update:** Handled by `GridView1_RowUpdating`.
    *   **Logic:** Only the `EmpId` field is allowed to be updated. Retrieves the new `EmpId` (again, extracted from `EmployeeName [EmpId]` format).

*   **Delete:** Handled by `GridView1_RowDeleted`.
    *   **Logic:** Deletes a record by `Id`.

*   **Validation:**
    *   **Required Fields:** `Category`, `Nos`, `Buyer` (EmployeeName).
    *   **Data Type/Format:** `Nos` must be numeric (regex: `^\d{1,15}(\.\d{0,3})?$`). `Category` must not be "Select".
    *   **Business Rule:** Unique combination of `Nos` and `Category`.

*   **Autocomplete:** `sql` WebMethod provides `EmployeeName [EmpId]` suggestions for the "Buyer" field by searching `tblHR_OfficeStaff` filtered by `CompId`.

**Business Benefit:** A clear understanding of existing business logic, validation rules, and data interactions is critical for accurate translation. Automating this extraction minimizes misinterpretations and ensures that critical business rules are not lost during the migration, leading to a more reliable and functionally equivalent new system.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET `GridView` is the central UI component.

*   **Data Display:** A table (`GridView`) with columns for "SN" (serial number), "Category", "Nos", "Buyer" (`EmployeeName [EmpId]`).
*   **Actions:** "Edit" and "Delete" link buttons per row.
*   **Form for Adding:** An "Insert" form is embedded in the `GridView`'s footer or `EmptyDataTemplate`.
    *   **Category:** `DropDownList` (A-Z, Select).
    *   **Nos:** `TextBox`.
    *   **Buyer:** `TextBox` with `AutoCompleteExtender`.
    *   **Insert Button:** `btnInsert`.
*   **User Feedback:** `lblMessage` for status updates (inserted, updated, deleted) and `ClientScript.RegisterStartupScript` for JavaScript alerts (e.g., "Record already exists").
*   **Client-side Confirmation:** JavaScript functions (`confirmationAdd`, `confirmationUpdate`, `confirmationDelete`) are triggered on button clicks.

**Business Benefit:** Identifying UI components helps design a user-friendly Django interface that mirrors or improves the original experience. By mapping to HTMX, Alpine.js, and DataTables, we ensure a highly responsive and interactive frontend without traditional complex JavaScript, improving user satisfaction and reducing frontend development costs. This systematic mapping process enables automated UI generation.

---

### Step 4: Generate Django Code

We will create a new Django application, for example, `buyer_master`.

#### 4.1 Models (`buyer_master/models.py`)

**Business Benefit:** Centralizing data definitions and business logic within Django models creates a "single source of truth." This "Fat Model" approach ensures data integrity, reduces redundancy across the codebase, and makes the system easier to maintain and extend, directly contributing to long-term cost savings and reliability.

```python
from django.db import models
from django.core.exceptions import ValidationError
import re

# Model for tblHR_OfficeStaff - assuming it exists for foreign key relationship
class OfficeStaff(models.Model):
    empid = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employeename = models.CharField(db_column='EmployeeName', max_length=255)
    compid = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False  # Important: Django won't create/manage this table
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.employeename} [{self.empid}]"


class BuyerMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    category = models.CharField(db_column='Category', max_length=1)
    # The 'Nos' field was a string in ASP.NET but validated as numeric.
    # We'll keep it as CharField for flexibility but enforce numeric validation in form/clean.
    nos = models.CharField(db_column='Nos', max_length=15)
    
    # EmpId is the foreign key. We link to OfficeStaff model using 'empid' as the to_field.
    employee = models.ForeignKey(
        OfficeStaff,
        models.DO_NOTHING,  # Equivalent to ON DELETE NO ACTION in SQL
        db_column='EmpId',
        to_field='empid',  # Specifies the field in OfficeStaff that EmpId points to
        related_name='buyer_masters'
    )

    class Meta:
        managed = False  # Important: Django won't create/manage this table
        db_table = 'tblMM_Buyer_Master'
        verbose_name = 'Buyer Master'
        verbose_name_plural = 'Buyer Masters'
        # Enforce unique constraint on (Category, Nos) as per ASP.NET's duplicate check
        unique_together = (('category', 'nos'),)

    def __str__(self):
        return f"Buyer: {self.employee.employeename} ({self.category}-{self.nos})"

    # Business logic method for employee display format, mimicking ASP.NET
    def get_employee_display(self):
        """Returns employee name and ID in 'EmployeeName [EmpId]' format."""
        return f"{self.employee.employeename} [{self.employee.empid}]"

    @staticmethod
    def extract_employee_id(display_string):
        """
        Extracts the Employee ID from a string formatted 'EmployeeName [EmpId]'.
        Mimics fun.getCode() from ASP.NET.
        """
        match = re.search(r'\[(.*?)\]$', display_string)
        if match:
            return match.group(1)
        return None

    # We add clean method for additional model-level validation beyond unique_together,
    # though unique_together handles the primary duplicate check for this case.
    # This example ensures 'Nos' adheres to the regex if not handled by form.
    def clean(self):
        # Validate 'Nos' format
        if self.nos and not re.match(r'^\d{1,15}(\.\d{0,3})?$', self.nos):
            raise ValidationError({'nos': "Nos must be a numeric value with up to 15 digits and 3 decimal places."})
        
        # Additional checks can go here, though unique_together covers (category, nos)
        # For example, if we wanted to enforce that category isn't 'Select' here (though better in form)
        if self.category == 'Select':
            raise ValidationError({'category': "Category cannot be 'Select'."})

    def save(self, *args, **kwargs):
        self.clean() # Ensure clean is called before save
        super().save(*args, **kwargs)

```

#### 4.2 Forms (`buyer_master/forms.py`)

**Business Benefit:** Django Forms provide robust, built-in validation and clean data handling, eliminating the need for manual input sanitization. This not only enhances security by preventing common web vulnerabilities but also significantly reduces development time and maintenance effort compared to ad-hoc validation methods.

```python
from django import forms
from .models import BuyerMaster, OfficeStaff
from django.core.exceptions import ValidationError
import re

class BuyerMasterForm(forms.ModelForm):
    # This field will be used for the autocomplete lookup
    # It's a CharField because the user input is a combined string "Name [ID]"
    employee_display = forms.CharField(
        label='Buyer',
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing buyer name...',
            # HTMX attributes for autocomplete
            'hx-get': '/buyer-master/autocomplete-employee/', # This URL needs to be defined
            'hx-trigger': 'keyup changed delay:300ms, search',
            'hx-target': '#employee-suggestions', # Target for the dropdown suggestions
            'hx-indicator': '.htmx-indicator',
            'hx-swap': 'outerHTML'
        })
    )

    class Meta:
        model = BuyerMaster
        # Exclude 'employee' from fields because we handle it via 'employee_display'
        # 'id' is auto-handled by ModelForm for existing instances
        fields = ['category', 'nos']
        widgets = {
            'category': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'nos': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

    # Populate category choices
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['category'].choices = [('Select', 'Select')] + [(chr(i), chr(i)) for i in range(ord('A'), ord('Z') + 1)]
        
        # If editing an existing instance, populate employee_display
        if self.instance and self.instance.pk:
            self.fields['employee_display'].initial = self.instance.get_employee_display()

    # Custom validation for category
    def clean_category(self):
        category = self.cleaned_data['category']
        if category == 'Select':
            raise ValidationError("Category cannot be 'Select'.")
        return category

    # Custom validation for Nos (numeric regex)
    def clean_nos(self):
        nos = self.cleaned_data['nos']
        if not re.match(r'^\d{1,15}(\.\d{0,3})?$', nos):
            raise ValidationError("Nos must be a numeric value with up to 15 digits and 3 decimal places.")
        return nos
    
    # Custom validation for employee_display and setting the actual 'employee' field
    def clean_employee_display(self):
        employee_display = self.cleaned_data['employee_display']
        empid = BuyerMaster.extract_employee_id(employee_display)
        
        if not empid:
            raise ValidationError("Invalid Buyer format. Please select from suggestions.")
        
        try:
            employee_obj = OfficeStaff.objects.get(empid=empid)
        except OfficeStaff.DoesNotExist:
            raise ValidationError("Selected Buyer does not exist.")
        
        # Set the actual employee instance for the model
        self.instance.employee = employee_obj
        return employee_display

    # Model-wide validation for duplicate (Category, Nos) combined
    # This is handled by unique_together in Meta, but we can add an extra message if needed
    # (The original ASP.NET did a manual select for this, so good to reflect here too)
    def clean(self):
        cleaned_data = super().clean()
        
        category = cleaned_data.get('category')
        nos = cleaned_data.get('nos')
        
        if category and nos:
            # Check for existing record with same category and nos, excluding current instance if updating
            qs = BuyerMaster.objects.filter(category=category, nos=nos)
            if self.instance.pk: # If updating, exclude the current instance
                qs = qs.exclude(pk=self.instance.pk)
            
            if qs.exists():
                raise ValidationError(
                    "A record with this Category and Nos already exists. Please choose a different combination."
                )
        return cleaned_data

```

#### 4.3 Views (`buyer_master/views.py`)

**Business Benefit:** Employing thin, class-based views (CBVs) separates business logic from presentation, making code easier to read, test, and maintain. By delegating complex operations to "Fat Models" and handling dynamic UI with HTMX, the system becomes more scalable, responsive, and less prone to errors during future enhancements.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q # For complex queries

from .models import BuyerMaster, OfficeStaff
from .forms import BuyerMasterForm

import json # For JSON response

# This view is for the main list page, which will load the table via HTMX
class BuyerMasterListView(ListView):
    model = BuyerMaster
    template_name = 'buyer_master/buyer_master_list.html'
    context_object_name = 'buyer_masters' # Not directly used for table data, but for completeness

    # Get the company ID from session, mimicking ASP.NET's SessionParameter
    def get_queryset(self):
        # Placeholder for CompId. In a real app, you'd get this from request.user profile etc.
        comp_id = self.request.session.get('CompId', 1) # Default to 1 if not set in session
        return BuyerMaster.objects.filter(employee__compid=comp_id).select_related('employee')


# This view is specifically for HTMX to load/reload the table content
class BuyerMasterTablePartialView(ListView):
    model = BuyerMaster
    template_name = 'buyer_master/_buyer_master_table.html'
    context_object_name = 'buyer_masters'

    # Get the company ID from session, mimicking ASP.NET's SessionParameter
    def get_queryset(self):
        comp_id = self.request.session.get('CompId', 1) # Default to 1 if not set in session
        return BuyerMaster.objects.filter(employee__compid=comp_id).select_related('employee')


class BuyerMasterCreateView(CreateView):
    model = BuyerMaster
    form_class = BuyerMasterForm
    template_name = 'buyer_master/_buyer_master_form.html'
    # success_url is handled by HTMX, so we don't need reverse_lazy here for redirect
    
    def form_valid(self, form):
        # Business logic is in the form's clean method and model's clean/save
        response = super().form_valid(form)
        messages.success(self.request, 'Buyer Master record added successfully.')
        
        # If HTMX request, return 204 No Content and trigger client-side event
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content
                headers={
                    'HX-Trigger': 'refreshBuyerMasterList' # Custom event to trigger table reload
                }
            )
        return response

    def form_invalid(self, form):
        # If HTMX request, return the form with errors
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class BuyerMasterUpdateView(UpdateView):
    model = BuyerMaster
    form_class = BuyerMasterForm
    template_name = 'buyer_master/_buyer_master_form.html'
    
    def form_valid(self, form):
        # Business logic is in the form's clean method and model's clean/save
        response = super().form_valid(form)
        messages.success(self.request, 'Buyer Master record updated successfully.')

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBuyerMasterList'
                }
            )
        return response

    def form_invalid(self, form):
        if self.request.headers.get('HX-Request'):
            return self.render_to_response(self.get_context_data(form=form))
        return super().form_invalid(form)


class BuyerMasterDeleteView(DeleteView):
    model = BuyerMaster
    template_name = 'buyer_master/_buyer_master_confirm_delete.html'
    
    def delete(self, request, *args, **kwargs):
        # Business logic for deletion (e.g., related checks) could be in model's delete method
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Buyer Master record deleted successfully.')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshBuyerMasterList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the object to the template for confirmation message
        context['object'] = self.get_object() 
        return context


# Mimics the ASP.NET WebMethod 'sql' for Employee autocomplete
class EmployeeAutocompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # Placeholder for CompId. In a real app, you'd get this from request.user profile etc.
        comp_id = request.session.get('CompId', 1) 

        if len(query) < 1: # Minimum prefix length from ASP.NET
            return HttpResponse('') # Return empty response for short queries

        # Filter by CompId and EmployeeName starting with prefixText (case-insensitive)
        employees = OfficeStaff.objects.filter(
            Q(compid=comp_id) & Q(employeename__istartswith=query)
        ).values('employeename', 'empid')[:20] # Limit suggestions, similar to ASP.NET's implied limit

        # Format results as "EmployeeName [EmpId]"
        suggestions = [f"{e['employeename']} [{e['empid']}]" for e in employees]
        suggestions.sort() # Sort alphabetically as in ASP.NET

        # HTMX will swap this into the target div
        html_suggestions = '<div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg w-full mt-1">'
        if suggestions:
            for s in suggestions:
                # hx-on:click is Alpine.js to update the input field and hide suggestions
                html_suggestions += f'<div class="p-2 cursor-pointer hover:bg-gray-200" hx-on:click="document.getElementById(\'id_employee_display\').value=\'{s}\'; document.getElementById(\'employee-suggestions\').innerHTML=\'\'; event.stopPropagation();">{s}</div>'
        else:
            html_suggestions += '<div class="p-2 text-gray-500">No results found.</div>'
        html_suggestions += '</div>'
        
        return HttpResponse(html_suggestions)

```

#### 4.4 Templates

**Business Benefit:** Standardized, modular templates (DRY principle) reduce code duplication and simplify UI consistency across the application. Using HTMX and Alpine.js ensures a highly dynamic and interactive user experience comparable to modern single-page applications, but with the simplicity of traditional server-rendered pages, making development faster and more maintainable.

##### `buyer_master/templates/buyer_master/buyer_master_list.html` (Main List View)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Buyer Masters</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'buyer_master_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus mr-2"></i>Add New Buyer Master
        </button>
    </div>
    
    <div id="buyerMasterTable-container"
         hx-trigger="load, refreshBuyerMasterList from:body"
         hx-get="{% url 'buyer_master_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-6 bg-white rounded-lg shadow-md">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Buyer Masters...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4"
             _="on htmx:afterOnLoad add .is-active to #modal">
            <!-- Content loaded via HTMX -->
            <div class="text-center p-6">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading form...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css">
{% endblock %}
```

##### `buyer_master/templates/buyer_master/_buyer_master_table.html` (Table Partial for HTMX)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow-md">
    <table id="buyerMasterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Nos</th>
                <th scope="col" class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Buyer</th>
                <th scope="col" class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in buyer_masters %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.category }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.nos }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.get_employee_display }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1.5 px-3 rounded-md shadow-sm transition duration-300 ease-in-out mr-2"
                        hx-get="{% url 'buyer_master_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1.5 px-3 rounded-md shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'buyer_master_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="5" class="py-4 px-4 text-center text-gray-500">No buyer masters found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once and on fresh content
    if ($.fn.DataTable.isDataTable('#buyerMasterTable')) {
        $('#buyerMasterTable').DataTable().destroy();
    }
    $('#buyerMasterTable').DataTable({
        "pageLength": 20, // PageSize from ASP.NET GridView
        "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
        "pagingType": "full_numbers"
    });
</script>
```

##### `buyer_master/templates/buyer_master/_buyer_master_form.html` (Form Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6 border-b pb-4">{{ form.instance.pk|yesno:'Edit,Add' }} Buyer Master</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-6">
        {% csrf_token %}
        
        {% for field in form %}
        <div class="mb-4">
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.name == 'employee_display' %}
            <div id="employee-suggestions"></div> {# This div will be populated by HTMX autocomplete #}
            {% endif %}
            {% if field.errors %}
            <p class="text-red-500 text-sm mt-1">{{ field.errors|join:", " }}</p>
            {% endif %}
            {% if field.help_text %}
            <p class="text-gray-500 text-xs mt-1">{{ field.help_text }}</p>
            {% endif %}
        </div>
        {% endfor %}

        {# Display non-field errors #}
        {% if form.non_field_errors %}
        <div class="p-3 bg-red-100 border border-red-400 text-red-700 rounded-md">
            <ul>
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-save mr-2"></i>Save
            </button>
        </div>
    </form>
</div>
```

##### `buyer_master/templates/buyer_master/_buyer_master_confirm_delete.html` (Delete Confirmation Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-800 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Buyer Master record for 
        <strong class="font-medium">{{ object.get_employee_display }} (Category: {{ object.category }}, Nos: {{ object.nos }})</strong>?
        This action cannot be undone.
    </p>
    
    <form hx-post="{% url 'buyer_master_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-300 ease-in-out">
                <i class="fas fa-trash-alt mr-2"></i>Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`buyer_master/urls.py`)

**Business Benefit:** Clearly defined URL patterns ensure that each application resource is accessible via a logical and predictable path. This improves API design, facilitates integration with other systems, and makes the application's structure easy to understand and navigate for both developers and automated tools.

```python
from django.urls import path
from .views import (
    BuyerMasterListView, 
    BuyerMasterCreateView, 
    BuyerMasterUpdateView, 
    BuyerMasterDeleteView,
    BuyerMasterTablePartialView,
    EmployeeAutocompleteView
)

urlpatterns = [
    path('buyer-master/', BuyerMasterListView.as_view(), name='buyer_master_list'),
    path('buyer-master/add/', BuyerMasterCreateView.as_view(), name='buyer_master_add'),
    path('buyer-master/edit/<int:pk>/', BuyerMasterUpdateView.as_view(), name='buyer_master_edit'),
    path('buyer-master/delete/<int:pk>/', BuyerMasterDeleteView.as_view(), name='buyer_master_delete'),
    
    # HTMX specific endpoints
    path('buyer-master/table/', BuyerMasterTablePartialView.as_view(), name='buyer_master_table_partial'),
    path('buyer-master/autocomplete-employee/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
]

```

#### 4.6 Tests (`buyer_master/tests.py`)

**Business Benefit:** Comprehensive unit and integration tests are critical for ensuring the reliability and correctness of the migrated application. By achieving high test coverage, we minimize post-migration bugs, accelerate future development cycles, and provide confidence in the system's functionality, ultimately reducing operational risks and maintenance costs.

```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import BuyerMaster, OfficeStaff
from .forms import BuyerMasterForm

class BuyerMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy OfficeStaff for FK
        cls.office_staff_1 = OfficeStaff.objects.create(empid='EMP001', employeename='Alice Smith', compid=1)
        cls.office_staff_2 = OfficeStaff.objects.create(empid='EMP002', employeename='Bob Johnson', compid=1)
        cls.office_staff_3 = OfficeStaff.objects.create(empid='EMP003', employeename='Charlie Brown', compid=2)

        # Create test data for BuyerMaster
        BuyerMaster.objects.create(id=1, category='A', nos='123', employee=cls.office_staff_1)
        BuyerMaster.objects.create(id=2, category='B', nos='456', employee=cls.office_staff_2)
  
    def test_buyer_master_creation(self):
        obj = BuyerMaster.objects.get(id=1)
        self.assertEqual(obj.category, 'A')
        self.assertEqual(obj.nos, '123')
        self.assertEqual(obj.employee, self.office_staff_1)
        self.assertEqual(str(obj), f"Buyer: {self.office_staff_1.employeename} ({obj.category}-{obj.nos})")
        
    def test_category_label(self):
        obj = BuyerMaster.objects.get(id=1)
        field_label = obj._meta.get_field('category').verbose_name
        self.assertEqual(field_label, 'category') # Default verbose name for CharField
        
    def test_get_employee_display_method(self):
        obj = BuyerMaster.objects.get(id=1)
        self.assertEqual(obj.get_employee_display(), f"Alice Smith [EMP001]")

    def test_extract_employee_id_static_method(self):
        self.assertEqual(BuyerMaster.extract_employee_id("Alice Smith [EMP001]"), "EMP001")
        self.assertIsNone(BuyerMaster.extract_employee_id("Invalid String"))

    def test_unique_together_constraint(self):
        # Should raise ValidationError for duplicate (category, nos)
        with self.assertRaises(Exception): # Will be IntegrityError at DB level, ValidationError at model clean
            BuyerMaster.objects.create(id=3, category='A', nos='123', employee=self.office_staff_2)
    
    def test_nos_validation_on_model_clean(self):
        # Test valid nos
        valid_obj = BuyerMaster(id=3, category='C', nos='123.45', employee=self.office_staff_1)
        valid_obj.clean() # Should not raise error

        # Test invalid nos
        invalid_obj = BuyerMaster(id=4, category='D', nos='abc', employee=self.office_staff_1)
        with self.assertRaises(ValidationError) as cm:
            invalid_obj.clean()
        self.assertIn('Nos must be a numeric value', str(cm.exception))


class BuyerMasterFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.office_staff_1 = OfficeStaff.objects.create(empid='EMP001', employeename='Alice Smith', compid=1)
        cls.office_staff_2 = OfficeStaff.objects.create(empid='EMP002', employeename='Bob Johnson', compid=1)
        cls.buyer = BuyerMaster.objects.create(id=1, category='A', nos='123', employee=cls.office_staff_1)

    def test_valid_form(self):
        form_data = {
            'category': 'C',
            'nos': '789',
            'employee_display': 'Bob Johnson [EMP002]'
        }
        form = BuyerMasterForm(data=form_data)
        self.assertTrue(form.is_valid(), form.errors)
        # Ensure the employee instance is set correctly
        self.assertEqual(form.instance.employee, self.office_staff_2)

    def test_form_category_validation(self):
        form_data = {
            'category': 'Select',
            'nos': '789',
            'employee_display': 'Bob Johnson [EMP002]'
        }
        form = BuyerMasterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('category', form.errors)
        self.assertIn('Category cannot be \'Select\'.', form.errors['category'])

    def test_form_nos_validation(self):
        form_data = {
            'category': 'C',
            'nos': 'abc', # Invalid Nos
            'employee_display': 'Bob Johnson [EMP002]'
        }
        form = BuyerMasterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('nos', form.errors)
        self.assertIn('Nos must be a numeric value', form.errors['nos'])

    def test_form_duplicate_validation(self):
        # Attempt to create a duplicate of existing buyer
        form_data = {
            'category': 'A',
            'nos': '123',
            'employee_display': 'Bob Johnson [EMP002]'
        }
        form = BuyerMasterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('A record with this Category and Nos already exists.', form.non_field_errors())

    def test_form_employee_display_validation(self):
        form_data = {
            'category': 'C',
            'nos': '789',
            'employee_display': 'Invalid Employee [XYZ001]' # Non-existent employee
        }
        form = BuyerMasterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('employee_display', form.errors)
        self.assertIn('Selected Buyer does not exist.', form.errors['employee_display'])

        form_data['employee_display'] = 'Just a name' # Invalid format
        form = BuyerMasterForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('employee_display', form.errors)
        self.assertIn('Invalid Buyer format.', form.errors['employee_display'])

    def test_form_initial_data_for_update(self):
        form = BuyerMasterForm(instance=self.buyer)
        self.assertEqual(form.initial['category'], 'A')
        self.assertEqual(form.initial['nos'], '123')
        self.assertEqual(form.initial['employee_display'], 'Alice Smith [EMP001]')


class BuyerMasterViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Set up session CompId for tests, mimicking ASP.NET
        cls.comp_id = 1
        cls.office_staff_1 = OfficeStaff.objects.create(empid='EMP001', employeename='Alice Staff', compid=cls.comp_id)
        cls.office_staff_2 = OfficeStaff.objects.create(empid='EMP002', employeename='Bob Staff', compid=cls.comp_id)
        cls.office_staff_other_comp = OfficeStaff.objects.create(empid='EMP003', employeename='Charlie Staff', compid=2)
        
        BuyerMaster.objects.create(id=1, category='A', nos='100', employee=cls.office_staff_1)
        BuyerMaster.objects.create(id=2, category='B', nos='200', employee=cls.office_staff_2)
        BuyerMaster.objects.create(id=3, category='C', nos='300', employee=cls.office_staff_other_comp) # Should not be in results for comp_id=1

    def setUp(self):
        self.client = Client()
        # Set session variable for CompId, as in ASP.NET
        session = self.client.session
        session['CompId'] = self.comp_id
        session.save()
    
    def test_list_view(self):
        response = self.client.get(reverse('buyer_master_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'buyer_master/buyer_master_list.html')
        # Check that the main list view doesn't directly contain the table content
        self.assertContains(response, '<div id="buyerMasterTable-container"')

    def test_table_partial_view(self):
        response = self.client.get(reverse('buyer_master_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'buyer_master/_buyer_master_table.html')
        # Only buyer masters for CompId 1 should be present
        self.assertContains(response, 'Alice Staff')
        self.assertContains(response, 'Bob Staff')
        self.assertNotContains(response, 'Charlie Staff') # From other company
        self.assertContains(response, '<table id="buyerMasterTable"')
        self.assertEqual(len(response.context['buyer_masters']), 2) # Only 2 records for CompId 1

    def test_create_view_get(self):
        response = self.client.get(reverse('buyer_master_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'buyer_master/_buyer_master_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Buyer Master')
        
    def test_create_view_post_success(self):
        data = {
            'category': 'D',
            'nos': '400',
            'employee_display': 'Alice Staff [EMP001]'
        }
        response = self.client.post(reverse('buyer_master_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBuyerMasterList')
        self.assertTrue(BuyerMaster.objects.filter(category='D', nos='400').exists())
        # Check messages framework for success message
        messages = list(response.client.session.get('_messages', []))
        self.assertIn('Buyer Master record added successfully.', [str(m) for m in messages])

    def test_create_view_post_invalid(self):
        data = {
            'category': 'Select', # Invalid category
            'nos': '400',
            'employee_display': 'Alice Staff [EMP001]'
        }
        response = self.client.post(reverse('buyer_master_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'buyer_master/_buyer_master_form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Category cannot be \'Select\'.', str(response.content))
        self.assertFalse(BuyerMaster.objects.filter(category='D', nos='400').exists())

    def test_update_view_get(self):
        buyer = BuyerMaster.objects.get(id=1)
        response = self.client.get(reverse('buyer_master_edit', args=[buyer.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'buyer_master/_buyer_master_form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Edit Buyer Master')
        self.assertContains(response, 'value="Alice Staff [EMP001]"') # Check initial value for employee_display

    def test_update_view_post_success(self):
        buyer = BuyerMaster.objects.get(id=1)
        data = {
            'category': 'A', # Category and Nos remain same
            'nos': '100',
            'employee_display': 'Bob Staff [EMP002]' # Only employee should change
        }
        response = self.client.post(reverse('buyer_master_edit', args=[buyer.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBuyerMasterList')
        buyer.refresh_from_db()
        self.assertEqual(buyer.employee, self.office_staff_2)
        # Check messages framework for success message
        messages = list(response.client.session.get('_messages', []))
        self.assertIn('Buyer Master record updated successfully.', [str(m) for m in messages])

    def test_update_view_post_invalid(self):
        buyer = BuyerMaster.objects.get(id=1)
        data = {
            'category': 'A',
            'nos': 'xyz', # Invalid Nos
            'employee_display': 'Alice Staff [EMP001]'
        }
        response = self.client.post(reverse('buyer_master_edit', args=[buyer.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'buyer_master/_buyer_master_form.html')
        self.assertFalse(response.context['form'].is_valid())
        self.assertIn('Nos must be a numeric value', str(response.content))

    def test_delete_view_get(self):
        buyer = BuyerMaster.objects.get(id=1)
        response = self.client.get(reverse('buyer_master_delete', args=[buyer.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'buyer_master/_buyer_master_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, 'Alice Staff (Category: A, Nos: 100)')
        
    def test_delete_view_post_success(self):
        buyer = BuyerMaster.objects.get(id=1)
        response = self.client.post(reverse('buyer_master_delete', args=[buyer.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshBuyerMasterList')
        self.assertFalse(BuyerMaster.objects.filter(id=1).exists())
        # Check messages framework for success message
        messages = list(response.client.session.get('_messages', []))
        self.assertIn('Buyer Master record deleted successfully.', [str(m) for m in messages])

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('employee_autocomplete'), {'q': 'ali'})
        self.assertEqual(response.status_code, 200)
        self.assertIn('Alice Staff [EMP001]', str(response.content))
        self.assertNotIn('Bob Staff', str(response.content)) # Should only match 'ali'
        self.assertNotIn('Charlie Staff', str(response.content)) # Different company

        response = self.client.get(reverse('employee_autocomplete'), {'q': 'bob'})
        self.assertEqual(response.status_code, 200)
        self.assertIn('Bob Staff [EMP002]', str(response.content))
        self.assertNotIn('Alice Staff', str(response.content))

        response = self.client.get(reverse('employee_autocomplete'), {'q': 'charlie'}) # From other compid
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('Charlie Staff', str(response.content)) # Should not find due to CompId filter

        response = self.client.get(reverse('employee_autocomplete'), {'q': ''}) # Empty query
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.content.decode('utf-8'), '') # Empty response

```

---

### Step 5: HTMX and Alpine.js Integration

**Business Benefit:** This combination delivers a highly interactive user experience without the complexity and overhead of traditional JavaScript frameworks. It streamlines development, reduces page load times, and creates a more responsive application, leading to increased user satisfaction and reduced network traffic.

*   **HTMX for all dynamic updates:**
    *   **Table Reload:** `hx-trigger="load, refreshBuyerMasterList from:body"` on `buyerMasterTable-container` ensures the table is loaded on initial page load and reloaded whenever a `refreshBuyerMasterList` custom event is triggered (after create/update/delete).
    *   **Modal Loading:** Buttons for "Add New", "Edit", and "Delete" use `hx-get` to fetch their respective forms (`_buyer_master_form.html`, `_buyer_master_confirm_delete.html`) and `hx-target="#modalContent"` to load them into the modal.
    *   **Form Submission:** Forms within the modal use `hx-post="{{ request.path }}" hx-swap="none"` for submission. On successful submission, the Django view returns a `204 No Content` status with `HX-Trigger: refreshBuyerMasterList` header, prompting the main list page to reload its table.
    *   **Autocomplete:** The `employee_display` input in the form uses `hx-get`, `hx-trigger`, `hx-target` to fetch suggestions from `EmployeeAutocompleteView` and display them in a dynamic dropdown.

*   **Alpine.js for UI state management:**
    *   **Modal Control:** The main `div` with `id="modal"` uses `_="on click if event.target.id == 'modal' remove .is-active from me"` to close the modal when clicking outside of `modalContent`. The "Cancel" buttons within the modal also use `_="on click remove .is-active from #modal"`.
    *   **Modal Visibility:** When a button triggers `hx-get` to load modal content, an `hx-on:htmx:afterOnLoad add .is-active to #modal"` listener ensures the modal is made visible *after* content is loaded. This prevents an empty modal from popping up.
    *   **Autocomplete Interaction:** HTMX loads `div`s with suggestions. Alpine.js `hx-on:click` on each suggestion `div` allows it to update the hidden input field and hide the suggestions.

*   **DataTables for List Views:**
    *   The `_buyer_master_table.html` partial includes the DataTables initialization script `$('#buyerMasterTable').DataTable({...});`.
    *   It's designed to re-initialize safely (`destroy()` and then `DataTable()`) each time the partial is loaded by HTMX, ensuring full client-side searching, sorting, and pagination capabilities.
    *   The `pageLength` is set to 20, matching the original ASP.NET `GridView`'s `PageSize`.

*   **No full page reloads:** All user interactions for CRUD operations and autocomplete are handled dynamically via HTMX, providing a snappy, modern user experience without full page refreshes.

---

## Final Notes

*   **Configuration:** Remember to add `buyer_master` to your `INSTALLED_APPS` in `settings.py` and include its URLs in your project's main `urls.py`. Configure your database connection in `settings.py` to point to your existing SQL Server database (or preferred database).
*   **Security:** Ensure proper authentication and authorization (e.g., using Django's built-in `LoginRequiredMixin` or custom permissions) are implemented for views to restrict access, especially for CRUD operations. The original `CompId` filter suggests a multi-tenant setup or user-specific data.
*   **Front-end Libraries:** Ensure jQuery, DataTables, HTMX, Alpine.js, and FontAwesome (for icons) CDN links are present in your `core/base.html` as demonstrated in the template snippets. Tailwind CSS setup is also assumed.
*   **Error Handling:** While `form_invalid` handles showing form errors, consider more robust global error handling and user feedback mechanisms (e.g., toast notifications for non-form errors).
*   **`CompId` Handling:** The current solution uses `request.session.get('CompId', 1)`. In a real application, you would integrate this with your Django authentication system, likely storing `CompId` on the `User` model or a related profile model.