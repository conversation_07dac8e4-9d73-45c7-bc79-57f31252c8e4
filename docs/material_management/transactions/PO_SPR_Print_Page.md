## ASP.NET to Django Conversion Script: Purchase Order Report Modernization

This document outlines a comprehensive plan to modernize the `PO_SPR_Print_Page.aspx` and its C# code-behind to a modern Django-based solution. The original page primarily serves to generate and display a detailed Purchase Order (PO) report, including historical amendment comparisons, using Crystal Reports. Our Django modernization will transform this into an interactive web page that displays the report data, suitable for viewing and printing, while adhering to Django best practices, fat models, thin views, and modern frontend techniques (HTMX, Alpine.js, DataTables where applicable).

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination (for list views, not for this specific detail/report view)
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with numerous tables to compile the PO report. The core tables are `tblMM_PO_Master` and `tblMM_PO_Details`, along with their amendment counterparts `tblMM_PO_Amd_Master` and `tblMM_PO_Amd_Details`. Extensive lookups are performed against related master data tables.

**Inferred Tables & Columns (Key Entities for this Report):**

*   **`tblMM_PO_Master` (Purchase Order Master):**
    *   `Id` (PK, int)
    *   `PONo` (string)
    *   `Insurance` (string)
    *   `AuthorizeDate` (datetime)
    *   `ApproveDate` (datetime)
    *   `SysDate` (datetime, likely creation date)
    *   `CheckedDate` (datetime)
    *   `ReferenceDate` (datetime)
    *   `CheckedBy` (int, EmpId)
    *   `ApprovedBy` (int, EmpId)
    *   `AuthorizedBy` (int, EmpId)
    *   `ModeOfDispatch` (string)
    *   `Inspection` (string)
    *   `Remarks` (string)
    *   `ShipTo` (string)
    *   `Reference` (int, FK to tblMM_PO_Reference)
    *   `ReferenceDesc` (string)
    *   `AmendmentNo` (int)
    *   `SupplierId` (string, FK to tblMM_Supplier_master)
    *   `Freight` (int, FK to tblFreight_Master)
    *   `Octroi` (int, FK to tblOctroi_Master)
    *   `Warrenty` (int, FK to tblWarrenty_Master)
    *   `PaymentTerms` (int, FK to tblPayment_Master)
    *   `TC` (string, Terms and Conditions)
    *   `CompId` (int, Company ID)

*   **`tblMM_PO_Details` (Purchase Order Details):**
    *   `Id` (PK, int)
    *   `MId` (int, FK to tblMM_PO_Master.Id)
    *   `Qty` (double)
    *   `Rate` (double)
    *   `Discount` (double)
    *   `DelDate` (datetime)
    *   `PF` (int, FK to tblPacking_Master)
    *   `ExST` (int, FK to tblExciseser_Master)
    *   `VAT` (int, FK to tblVAT_Master)
    *   `SPRId` (int, FK to tblMM_SPR_Details.Id)
    *   `BudgetCode` (int, FK to tblMIS_BudgetCode)
    *   `AddDesc` (string, Additional Description)

*   **`tblMM_PO_Amd_Master` (Purchase Order Amendment Master):**
    *   Similar structure to `tblMM_PO_Master` but for amendments.
    *   `POId` (int, FK to tblMM_PO_Master.Id, original PO ID)

*   **`tblMM_PO_Amd_Details` (Purchase Order Amendment Details):**
    *   Similar structure to `tblMM_PO_Details` but for amendments.
    *   `MId` (int, FK to tblMM_PO_Amd_Master.Id)
    *   `PODId` (int, FK to tblMM_PO_Details.Id, original PO Detail ID)

*   **Supporting/Lookup Tables:**
    *   `tblMM_PO_Reference` (`Id`, `RefDesc`)
    *   `tblMM_SPR_Master` (`Id`, `SessionId` (Indentor EmpId))
    *   `tblMM_SPR_Details` (`Id`, `MId` (FK to SPR_Master), `SPRNo`, `WONo`, `DeptId`, `AHId`, `ItemId`)
    *   `tblHR_OfficeStaff` (`EmpId`, `Title`, `EmployeeName`)
    *   `tblMIS_BudgetCode` (`Id`, `Symbol`)
    *   `BusinessGroup` (likely `tblBusinessGroup`, `Id`, `Symbol`)
    *   `tblDG_Item_Master` (`Id`, `ItemCode`, `ManfDesc`, `UOMBasic`)
    *   `Unit_Master` (`Id`, `Symbol`)
    *   `AccHead` (`Id`, `Symbol`)
    *   `tblMM_Supplier_master` (`SupplierId`, `SupplierName`, `ContactPerson`, `ContactNo`, `Email`, `RegdCountry`, `RegdAddress`, `RegdState`, `RegdCity`, `RegdPinNo`)
    *   `tblCountry` (`CId`, `CountryName`, `Symbol`)
    *   `tblState` (`SId`, `StateName`)
    *   `tblCity` (`CityId`, `CityName`)
    *   `tblPacking_Master` (`Id`, `Terms`, `Value`)
    *   `tblVAT_Master` (`Id`, `Terms`, `Value`)
    *   `tblExciseser_Master` (`Id`, `Terms`, `Value`)
    *   `tblOctroi_Master` (`Id`, `Terms`, `Value`)
    *   `tblPayment_Master` (`Id`, `Terms`)
    *   `tblWarrenty_Master` (`Id`, `Terms`)
    *   `tblFreight_Master` (`Id`, `Terms`)

## Step 2: Identify Backend Functionality

**Analysis:** The ASP.NET page is a read-only report generation page. It takes various query parameters (`mid`, `pono`, `Code`, `AmdNo`) and session data (`compid`) to retrieve and aggregate a large dataset for presentation. The core logic involves:

*   **Data Retrieval:** Fetching PO master and detail records, conditional fetching of amendment records.
*   **Complex Joins & Lookups:** Performing multiple SQL lookups to enrich the data (e.g., getting supplier name, item details, terms descriptions, employee names, budget codes, etc.).
*   **Amendment Comparison Logic:** This is the most complex part. It compares data fields between the current amendment and the *previous* amendment (or original PO if no previous amendment) and marks differing fields with an asterisk (`*`).
*   **Data Structuring:** Building a `DataTable` with a specific schema (66 columns) from disparate sources.
*   **Parameter Passing:** Setting parameters for the Crystal Report viewer.

**Django Equivalent Strategy:**
The "fat model" approach is perfect here. All the data retrieval, complex joins, lookups, and especially the amendment comparison logic will be encapsulated within a dedicated manager method or a class method on the `PurchaseOrderMaster` model. This method will return a structured Python dictionary or an object containing all the compiled report data, including the asterisk markings.

## Step 3: Infer UI Components

**Analysis:** The original ASP.NET page uses `CrystalReportViewer` to render a pre-defined report file (`.rpt`). This means the UI is essentially a static document viewer with print capabilities. There are no interactive data grids or CRUD forms on *this specific page*.

**Django Equivalent Strategy:**
*   **Report Display:** The report will be rendered as a detailed HTML page (`po_report_detail.html`). This page will be designed for display and print.
*   **HTMX/Alpine.js:** While DataTables are not applicable for a single detailed report, HTMX can be used for dynamic actions (e.g., fetching a print-friendly version, or reloading parts of the report). Alpine.js can manage minor UI states like a print modal or toggling sections.
*   **No Direct CRUD Forms:** Since this page is read-only, we won't create `ModelForm` instances for this page. However, for a complete application, associated CRUD forms for `PurchaseOrderMaster`, `PurchaseOrderDetail`, etc., would exist elsewhere.

---

## Step 4: Generate Django Code

**Assumed Django App Name:** `material_management` (derived from `Module_MaterialManagement_Transactions`)

### 4.1 Models (`material_management/models.py`)

We will define core models representing the tables involved. The complex report generation logic will reside within a custom manager for `PurchaseOrderMaster`.

```python
from django.db import models
from django.db.models import F
from django.utils import timezone
from datetime import datetime

# Helper function to convert date formats if needed, based on fun.FromDateDMY
# In Django, we typically handle dates as native datetime objects and format in templates.
def format_date_dmy(date_obj):
    if date_obj:
        return date_obj.strftime("%d/%m/%Y")
    return ""

class Company(models.Model):
    # This model is inferred from Session["compid"] and fun.CompAdd(cId)
    # Assumed table and fields for company information
    CompId = models.IntegerField(db_column='CompId', primary_key=True)
    CompanyName = models.CharField(db_column='CompanyName', max_length=255)
    Address = models.TextField(db_column='Address')

    class Meta:
        managed = False
        db_table = 'tblCompany' # Assuming a table named tblCompany
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.CompanyName

class Employee(models.Model):
    EmpId = models.IntegerField(db_column='EmpId', primary_key=True)
    Title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    EmployeeName = models.CharField(db_column='EmployeeName', max_length=255)
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.Title or ''} {self.EmployeeName}".strip()

class Supplier(models.Model):
    SupplierId = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    SupplierName = models.CharField(db_column='SupplierName', max_length=255)
    ContactPerson = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    ContactNo = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    Email = models.EmailField(db_column='Email', max_length=255, blank=True, null=True)
    RegdCountry = models.IntegerField(db_column='RegdCountry', blank=True, null=True)
    RegdAddress = models.TextField(db_column='RegdAddress', blank=True, null=True)
    RegdState = models.IntegerField(db_column='RegdState', blank=True, null=True)
    RegdCity = models.IntegerField(db_column='RegdCity', blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId')
    RegdPinNo = models.CharField(db_column='RegdPinNo', max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.SupplierName

class Country(models.Model):
    CId = models.IntegerField(db_column='CId', primary_key=True)
    CountryName = models.CharField(db_column='CountryName', max_length=100)
    Symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.CountryName

class State(models.Model):
    SId = models.IntegerField(db_column='SId', primary_key=True)
    StateName = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.StateName

class City(models.Model):
    CityId = models.IntegerField(db_column='CityId', primary_key=True)
    CityName = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.CityName

class ItemMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    ItemCode = models.CharField(db_column='ItemCode', max_length=50)
    ManfDesc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    UOMBasic = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # FK to UnitMaster
    CompId = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.ItemCode

    def get_item_code_part_no(self):
        # This logic comes from fun.GetItemCode_PartNo(cId, ItemId)
        # Assuming ItemCode is sufficient, or more complex logic here if needed
        return self.ItemCode

class UnitMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=20)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.Symbol

class AccHead(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.Symbol

class BusinessGroup(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup' # Assuming this is the table name
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.Symbol

class BudgetCode(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return self.Symbol

class SPRMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    SessionId = models.IntegerField(db_column='SessionId', blank=True, null=True) # Indentor EmpId

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return f"SPR Master {self.Id}"

class SPRDetail(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MId = models.IntegerField(db_column='MId') # FK to SPRMaster
    SPRNo = models.CharField(db_column='SPRNo', max_length=50)
    WONo = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    DeptId = models.IntegerField(db_column='DeptId', blank=True, null=True) # FK to BusinessGroup
    AHId = models.IntegerField(db_column='AHId', blank=True, null=True) # FK to AccHead
    ItemId = models.IntegerField(db_column='ItemId', blank=True, null=True) # FK to ItemMaster

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return self.SPRNo

class POReference(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    RefDesc = models.CharField(db_column='RefDesc', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Reference'
        verbose_name = 'PO Reference'
        verbose_name_plural = 'PO References'

    def __str__(self):
        return self.RefDesc

# Models for various terms (Packing, VAT, Excise, Octroi, Payment, Warranty, Freight)
class PackingTerm(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.TextField(db_column='Terms')
    Value = models.FloatField(db_column='Value')

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'

class VATTax(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.TextField(db_column='Terms')
    Value = models.FloatField(db_column='Value')

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'

class ExciseTax(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.TextField(db_column='Terms')
    Value = models.FloatField(db_column='Value')

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'

class OctroiTax(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.TextField(db_column='Terms')
    Value = models.FloatField(db_column='Value')

    class Meta:
        managed = False
        db_table = 'tblOctroi_Master'

class PaymentTerm(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblPayment_Master'

class WarrantyTerm(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblWarrenty_Master'

class FreightTerm(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    Terms = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tblFreight_Master'

# Custom Manager for Purchase Order Report Data
class PurchaseOrderManager(models.Manager):
    def get_detailed_report_data(self, po_master_id, company_id, amendment_no):
        report_data = []
        po_master = self.filter(pk=po_master_id, CompId=company_id).first()

        if not po_master:
            return None, "Purchase Order not found."

        # Determine if we're looking at current or amended PO
        current_amendment_details = self.get_queryset().filter(
            pk=po_master_id,
            CompId=company_id
        ).select_related(
            'supplier', 'poreference', 'freight_term', 'octroi_term', 'warranty_term', 'payment_term'
        ).prefetch_related(
            'details__spr_detail__spr_master__indentor_employee',
            'details__spr_detail__item_master__uom_basic',
            'details__spr_detail__acc_head',
            'details__spr_detail__business_group',
            'details__packing_term',
            'details__excise_tax',
            'details__vat_tax',
            'details__budget_code'
        ).first()

        if amendment_no < po_master.AmendmentNo: # Fetching a specific historical amendment
            # For amended POs, data comes from tblMM_PO_Amd_Master/Details
            current_po_query = PurchaseOrderAmendmentMaster.objects.filter(
                POId=po_master_id,
                AmendmentNo=amendment_no,
                CompId=company_id
            ).select_related(
                'supplier', 'poreference', 'freight_term', 'octroi_term', 'warranty_term', 'payment_term'
            ).prefetch_related(
                'details__spr_detail__spr_master__indentor_employee',
                'details__spr_detail__item_master__uom_basic',
                'details__spr_detail__acc_head',
                'details__spr_detail__business_group',
                'details__packing_term',
                'details__excise_tax',
                'details__vat_tax',
                'details__budget_code'
            )
            current_po_master_data = current_po_query.first()
            current_po_details_data = current_po_master_data.details.all() if current_po_master_data else []
        else: # Current PO or latest amendment
            current_po_master_data = po_master
            current_po_details_data = po_master.details.all()

        # Fetch previous amendment for comparison
        previous_po_master_data = None
        if amendment_no > 0: # Only compare if an amendment exists
            previous_po_master_data = PurchaseOrderAmendmentMaster.objects.filter(
                POId=po_master_id,
                AmendmentNo=amendment_no - 1,
                CompId=company_id
            ).select_related(
                'supplier', 'poreference', 'freight_term', 'octroi_term', 'warranty_term', 'payment_term'
            ).first()

        # Common lookups outside the detail loop to reduce queries
        company_obj = Company.objects.filter(CompId=company_id).first()
        company_address = company_obj.Address if company_obj else "N/A"

        # Prepare report data
        for current_detail in current_po_details_data:
            row = {}

            # --- Basic PO Detail Data ---
            row['Id'] = current_po_master_data.Id
            row['PONo'] = current_po_master_data.PONo
            row['POQty'] = current_detail.Qty
            row['Rate'] = current_detail.Rate
            row['Discount'] = current_detail.Discount
            row['DelDate'] = format_date_dmy(current_detail.DelDate)
            row['AmdNo'] = current_po_master_data.AmendmentNo
            row['RefDesc'] = current_po_master_data.p_o_reference.RefDesc if current_po_master_data.p_o_reference else ""
            row['ModeOfDispatch'] = current_po_master_data.ModeOfDispatch
            row['Inspection'] = current_po_master_data.Inspection
            row['ShipTo'] = current_po_master_data.ShipTo
            row['Remarks'] = current_po_master_data.Remarks

            # --- SPR and Item Details ---
            spr_detail = current_detail.spr_detail
            if spr_detail:
                row['SPRNo'] = spr_detail.SPRNo
                row['WONo'] = spr_detail.WONo or ""
                indentor_employee = spr_detail.spr_master.indentor_employee if spr_detail.spr_master else None
                row['Indentor'] = f"{indentor_employee.Title or ''} {indentor_employee.EmployeeName} [{spr_detail.spr_master.SessionId}]".strip() if indentor_employee else ""

                if spr_detail.WONo:
                    budget_code = spr_detail.budget_code
                    if budget_code:
                        row['BudgetCode'] = f"{budget_code.Symbol}{row['WONo']}"
                    else:
                        dept = spr_detail.business_group
                        row['BudgetCode'] = dept.Symbol if dept else ""
                else:
                    row['BudgetCode'] = ""

                item_master = spr_detail.item_master
                if item_master:
                    row['ItemCode'] = item_master.get_item_code_part_no()
                    row['manfDesc'] = item_master.ManfDesc
                    uom_basic = item_master.uom_basic
                    row['UOMBasic'] = uom_basic.Symbol if uom_basic else ""

                acc_head = spr_detail.acc_head
                row['AcHead'] = acc_head.Symbol if acc_head else ""

                dept = spr_detail.business_group
                row['Dept'] = dept.Symbol if dept else ""
            else:
                row['SPRNo'] = ""
                row['WONo'] = ""
                row['Indentor'] = ""
                row['BudgetCode'] = ""
                row['ItemCode'] = ""
                row['manfDesc'] = ""
                row['UOMBasic'] = ""
                row['AcHead'] = ""
                row['Dept'] = ""


            row['CompId'] = company_id

            # --- Supplier Details ---
            supplier = current_po_master_data.supplier
            if supplier:
                country = Country.objects.filter(CId=supplier.RegdCountry).first()
                row['Symbol'] = country.Symbol if country else "" # Country Symbol
                row['SuplierName'] = supplier.SupplierName
                row['ContactPerson'] = supplier.ContactPerson
                row['ContactNo'] = supplier.ContactNo
                row['Email'] = supplier.Email
                row['SupplierId'] = supplier.SupplierId
                row['Insurance'] = current_po_master_data.Insurance
            else:
                row['Symbol'] = ""
                row['SuplierName'] = ""
                row['ContactPerson'] = ""
                row['ContactNo'] = ""
                row['Email'] = ""
                row['SupplierId'] = ""
                row['Insurance'] = ""

            # --- Terms and Taxes ---
            row['PackagingTerm'] = current_detail.packing_term.Terms if current_detail.packing_term else ""
            row['PackagingValue'] = current_detail.packing_term.Value if current_detail.packing_term else 0.0

            row['VatTerm'] = current_detail.vat_tax.Terms if current_detail.vat_tax else ""
            row['VatValue'] = current_detail.vat_tax.Value if current_detail.vat_tax else 0.0

            row['ExciseTerm'] = current_detail.excise_tax.Terms if current_detail.excise_tax else ""
            row['ExciseValue'] = current_detail.excise_tax.Value if current_detail.excise_tax else 0.0

            row['OctriTerm'] = current_po_master_data.octroi_term.Terms if current_po_master_data.octroi_term else ""
            row['OctriValue'] = current_po_master_data.octroi_term.Value if current_po_master_data.octroi_term else 0.0

            row['PaymentTerm'] = current_po_master_data.payment_term.Terms if current_po_master_data.payment_term else ""
            row['Warranty'] = current_po_master_data.warranty_term.Terms if current_po_master_data.warranty_term else ""
            row['Fright'] = current_po_master_data.freight_term.Terms if current_po_master_data.freight_term else ""

            row['RefPODesc'] = current_po_master_data.ReferenceDesc
            row['AddDesc'] = current_detail.AddDesc or ""

            # --- Amendment Comparison Logic (Populate fields with '*' if changed) ---
            if previous_po_master_data:
                previous_detail = PurchaseOrderAmendmentDetail.objects.filter(
                    MId=previous_po_master_data.Id,
                    PODId=current_detail.PODId if hasattr(current_detail, 'PODId') else current_detail.Id # PODId for amendment details, Id for original
                ).first()

                # Master data comparisons
                row['SuplierName*'] = "*" if supplier and previous_po_master_data.supplier and supplier.SupplierId != previous_po_master_data.supplier.SupplierId else ""
                row['VenderCode*'] = "*" if supplier and previous_po_master_data.supplier and supplier.SupplierId != previous_po_master_data.supplier.SupplierId else "" # Same as SupplierId
                row['RefDate*'] = "*" if current_po_master_data.ReferenceDate != previous_po_master_data.ReferenceDate else ""
                row['RefDesc*'] = "*" if current_po_master_data.Reference != previous_po_master_data.Reference else ""
                row['RefPODesc*'] = "*" if current_po_master_data.ReferenceDesc != previous_po_master_data.ReferenceDesc else ""
                row['PaymentTerm*'] = "*" if current_po_master_data.PaymentTerms != previous_po_master_data.PaymentTerms else ""
                row['ModeOfDispatch*'] = "*" if current_po_master_data.ModeOfDispatch != previous_po_master_data.ModeOfDispatch else ""
                row['Inspection*'] = "*" if current_po_master_data.Inspection != previous_po_master_data.Inspection else ""
                row['OctriValue*'] = "*" if current_po_master_data.Octroi != previous_po_master_data.Octroi else ""
                row['Warranty*'] = "*" if current_po_master_data.Warrenty != previous_po_master_data.Warrenty else ""
                row['Fright*'] = "*" if current_po_master_data.Freight != previous_po_master_data.Freight else ""
                row['Insurance*'] = "*" if current_po_master_data.Insurance != previous_po_master_data.Insurance else ""
                row['ShipTo*'] = "*" if current_po_master_data.ShipTo != previous_po_master_data.ShipTo else ""
                row['Remarks*'] = "*" if current_po_master_data.Remarks != previous_po_master_data.Remarks else ""
                row['TC*'] = "*" if current_po_master_data.TC != previous_po_master_data.TC else ""

                # Detail data comparisons
                if previous_detail:
                    row['Rate*'] = "*" if current_detail.Rate != previous_detail.Rate else ""
                    row['Discount*'] = "*" if current_detail.Discount != previous_detail.Discount else ""
                    row['PackagingValue*'] = "*" if current_detail.PF != previous_detail.PF else ""
                    row['ExciseValue*'] = "*" if current_detail.ExST != previous_detail.ExST else ""
                    row['VatValue*'] = "*" if current_detail.VAT != previous_detail.VAT else ""
                    row['DelDate*'] = "*" if current_detail.DelDate != previous_detail.DelDate else ""
                    row['BudgetCode*'] = "*" if current_detail.BudgetCode != previous_detail.BudgetCode else ""
                    row['AddDesc*'] = "*" if current_detail.AddDesc != previous_detail.AddDesc else ""
                    row['POQty*'] = "*" if current_detail.Qty != previous_detail.Qty else ""
                else: # No matching previous detail found (e.g., new line item in amendment)
                    row['Rate*'] = "*"
                    row['Discount*'] = "*"
                    row['PackagingValue*'] = "*"
                    row['ExciseValue*'] = "*"
                    row['VatValue*'] = "*"
                    row['DelDate*'] = "*"
                    row['BudgetCode*'] = "*"
                    row['AddDesc*'] = "*"
                    row['POQty*'] = "*"
            else: # No previous amendment for comparison
                for key in ['SuplierName*', 'VenderCode*', 'RefDate*', 'RefDesc*', 'RefPODesc*', 'Rate*', 'Discount*',
                             'PackagingValue*', 'ExciseValue*', 'VatValue*', 'DelDate*', 'PaymentTerm*',
                             'ModeOfDispatch*', 'Inspection*', 'OctriValue*', 'Warranty*', 'Fright*', 'Insurance*',
                             'ShipTo*', 'Remarks*', 'BudgetCode*', 'AddDesc*', 'POQty*', 'TC*']:
                    row[key] = "" # No asterisk if no previous version to compare against

            report_data.append(row)

        # Gather main report header parameters
        main_report_params = {
            "SupplierAddress": None, # Will be set below
            "RegDate": format_date_dmy(current_po_master_data.SysDate),
            "RefDate": format_date_dmy(current_po_master_data.ReferenceDate),
            "CheckedBy": (current_po_master_data.checked_by_employee.__str__() if current_po_master_data.checked_by_employee else " "),
            "ApprovedBy": (current_po_master_data.approved_by_employee.__str__() if current_po_master_data.approved_by_employee else " "),
            "AuthorizedBy": (current_po_master_data.authorized_by_employee.__str__() if current_po_master_data.authorized_by_employee else " "),
            "CheckedDate": format_date_dmy(current_po_master_data.CheckedDate),
            "ApproveDate": format_date_dmy(current_po_master_data.ApproveDate),
            "AuthorizeDate": format_date_dmy(current_po_master_data.AuthorizeDate),
            "Address2": company_address,
            "TC": current_po_master_data.TC or ""
        }

        # Supplier Address Construction
        if supplier:
            country_obj = Country.objects.filter(CId=supplier.RegdCountry).first()
            state_obj = State.objects.filter(SId=supplier.RegdState).first()
            city_obj = City.objects.filter(CityId=supplier.RegdCity).first()

            supplier_address_parts = [
                supplier.RegdAddress or "",
                city_obj.CityName if city_obj else "",
                state_obj.StateName if state_obj else "",
                country_obj.CountryName if country_obj else "",
                supplier.RegdPinNo or ""
            ]
            main_report_params["SupplierAddress"] = ", ".join(filter(None, supplier_address_parts)) + "."
        else:
            main_report_params["SupplierAddress"] = "Supplier Address Not Available"

        # Determine report type based on country (for PO_SPR.rpt vs PO_SPR_Import.rpt)
        is_import_po = (supplier.RegdCountry != 1) if supplier else False # Assuming 1 is the domestic country

        return report_data, main_report_params, is_import_po

class PurchaseOrderMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    PONo = models.CharField(db_column='PONo', max_length=50)
    Insurance = models.CharField(db_column='Insurance', max_length=255, blank=True, null=True)
    AuthorizeDate = models.DateTimeField(db_column='AuthorizeDate', blank=True, null=True)
    ApproveDate = models.DateTimeField(db_column='ApproveDate', blank=True, null=True)
    SysDate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    CheckedDate = models.DateTimeField(db_column='CheckedDate', blank=True, null=True)
    ReferenceDate = models.DateTimeField(db_column='ReferenceDate', blank=True, null=True)
    CheckedBy = models.IntegerField(db_column='CheckedBy', blank=True, null=True) # FK to Employee.EmpId
    ApprovedBy = models.IntegerField(db_column='ApprovedBy', blank=True, null=True) # FK to Employee.EmpId
    AuthorizedBy = models.IntegerField(db_column='AuthorizedBy', blank=True, null=True) # FK to Employee.EmpId
    ModeOfDispatch = models.CharField(db_column='ModeOfDispatch', max_length=255, blank=True, null=True)
    Inspection = models.CharField(db_column='Inspection', max_length=255, blank=True, null=True)
    Remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    ShipTo = models.TextField(db_column='ShipTo', blank=True, null=True)
    Reference = models.IntegerField(db_column='Reference', blank=True, null=True) # FK to POReference
    ReferenceDesc = models.CharField(db_column='ReferenceDesc', max_length=255, blank=True, null=True)
    AmendmentNo = models.IntegerField(db_column='AmendmentNo')
    SupplierId = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True) # FK to Supplier.SupplierId
    Freight = models.IntegerField(db_column='Freight', blank=True, null=True) # FK to FreightTerm
    Octroi = models.IntegerField(db_column='Octroi', blank=True, null=True) # FK to OctroiTax
    Warrenty = models.IntegerField(db_column='Warrenty', blank=True, null=True) # FK to WarrantyTerm
    PaymentTerms = models.IntegerField(db_column='PaymentTerms', blank=True, null=True) # FK to PaymentTerm
    TC = models.TextField(db_column='TC', blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId')

    # Define direct relationships for easier access
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='SupplierId', related_name='po_masters', blank=True, null=True)
    p_o_reference = models.ForeignKey(POReference, on_delete=models.DO_NOTHING, db_column='Reference', related_name='po_masters', blank=True, null=True)
    freight_term = models.ForeignKey(FreightTerm, on_delete=models.DO_NOTHING, db_column='Freight', related_name='po_masters', blank=True, null=True)
    octroi_term = models.ForeignKey(OctroiTax, on_delete=models.DO_NOTHING, db_column='Octroi', related_name='po_masters', blank=True, null=True)
    warranty_term = models.ForeignKey(WarrantyTerm, on_delete=models.DO_NOTHING, db_column='Warrenty', related_name='po_masters', blank=True, null=True)
    payment_term = models.ForeignKey(PaymentTerm, on_delete=models.DO_NOTHING, db_column='PaymentTerms', related_name='po_masters', blank=True, null=True)
    checked_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='CheckedBy', related_name='checked_pos', blank=True, null=True)
    approved_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='ApprovedBy', related_name='approved_pos', blank=True, null=True)
    authorized_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='AuthorizedBy', related_name='authorized_pos', blank=True, null=True)

    objects = PurchaseOrderManager()

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'

    def __str__(self):
        return self.PONo

class PurchaseOrderDetail(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MId = models.IntegerField(db_column='MId') # FK to PurchaseOrderMaster
    Qty = models.FloatField(db_column='Qty')
    Rate = models.FloatField(db_column='Rate')
    Discount = models.FloatField(db_column='Discount')
    DelDate = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    PF = models.IntegerField(db_column='PF', blank=True, null=True) # FK to PackingTerm
    ExST = models.IntegerField(db_column='ExST', blank=True, null=True) # FK to ExciseTax
    VAT = models.IntegerField(db_column='VAT', blank=True, null=True) # FK to VATTax
    SPRId = models.IntegerField(db_column='SPRId', blank=True, null=True) # FK to SPRDetail
    BudgetCode = models.IntegerField(db_column='BudgetCode', blank=True, null=True) # FK to BudgetCode
    AddDesc = models.TextField(db_column='AddDesc', blank=True, null=True)

    po_master = models.ForeignKey(PurchaseOrderMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    packing_term = models.ForeignKey(PackingTerm, on_delete=models.DO_NOTHING, db_column='PF', related_name='po_details', blank=True, null=True)
    excise_tax = models.ForeignKey(ExciseTax, on_delete=models.DO_NOTHING, db_column='ExST', related_name='po_details', blank=True, null=True)
    vat_tax = models.ForeignKey(VATTax, on_delete=models.DO_NOTHING, db_column='VAT', related_name='po_details', blank=True, null=True)
    spr_detail = models.ForeignKey(SPRDetail, on_delete=models.DO_NOTHING, db_column='SPRId', related_name='po_details', blank=True, null=True)
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', related_name='po_details', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"PO Detail {self.Id} for PO {self.po_master.PONo}"


# Amendment Models
class PurchaseOrderAmendmentMaster(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True) # This is the Amendment Master ID
    POId = models.IntegerField(db_column='POId') # FK to original PurchaseOrderMaster
    PONo = models.CharField(db_column='PONo', max_length=50)
    Insurance = models.CharField(db_column='Insurance', max_length=255, blank=True, null=True)
    AuthorizeDate = models.DateTimeField(db_column='AuthorizeDate', blank=True, null=True)
    ApproveDate = models.DateTimeField(db_column='ApproveDate', blank=True, null=True)
    SysDate = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    CheckedDate = models.DateTimeField(db_column='CheckedDate', blank=True, null=True)
    ReferenceDate = models.DateTimeField(db_column='ReferenceDate', blank=True, null=True)
    CheckedBy = models.IntegerField(db_column='CheckedBy', blank=True, null=True)
    ApprovedBy = models.IntegerField(db_column='ApprovedBy', blank=True, null=True)
    AuthorizedBy = models.IntegerField(db_column='AuthorizedBy', blank=True, null=True)
    ModeOfDispatch = models.CharField(db_column='ModeOfDispatch', max_length=255, blank=True, null=True)
    Inspection = models.CharField(db_column='Inspection', max_length=255, blank=True, null=True)
    Remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    ShipTo = models.TextField(db_column='ShipTo', blank=True, null=True)
    Reference = models.IntegerField(db_column='Reference', blank=True, null=True)
    ReferenceDesc = models.CharField(db_column='ReferenceDesc', max_length=255, blank=True, null=True)
    AmendmentNo = models.IntegerField(db_column='AmendmentNo')
    SupplierId = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True)
    Freight = models.IntegerField(db_column='Freight', blank=True, null=True)
    Octroi = models.IntegerField(db_column='Octroi', blank=True, null=True)
    Warrenty = models.IntegerField(db_column='Warrenty', blank=True, null=True)
    PaymentTerms = models.IntegerField(db_column='PaymentTerms', blank=True, null=True)
    TC = models.TextField(db_column='TC', blank=True, null=True)
    CompId = models.IntegerField(db_column='CompId')

    # Define direct relationships for easier access
    original_po_master = models.ForeignKey(PurchaseOrderMaster, on_delete=models.CASCADE, db_column='POId', related_name='amendment_masters')
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='SupplierId', related_name='po_amendment_masters', blank=True, null=True)
    p_o_reference = models.ForeignKey(POReference, on_delete=models.DO_NOTHING, db_column='Reference', related_name='po_amendment_masters', blank=True, null=True)
    freight_term = models.ForeignKey(FreightTerm, on_delete=models.DO_NOTHING, db_column='Freight', related_name='po_amendment_masters', blank=True, null=True)
    octroi_term = models.ForeignKey(OctroiTax, on_delete=models.DO_NOTHING, db_column='Octroi', related_name='po_amendment_masters', blank=True, null=True)
    warranty_term = models.ForeignKey(WarrantyTerm, on_delete=models.DO_NOTHING, db_column='Warrenty', related_name='po_amendment_masters', blank=True, null=True)
    payment_term = models.ForeignKey(PaymentTerm, on_delete=models.DO_NOTHING, db_column='PaymentTerms', related_name='po_amendment_masters', blank=True, null=True)
    checked_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='CheckedBy', related_name='checked_po_amds', blank=True, null=True)
    approved_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='ApprovedBy', related_name='approved_po_amds', blank=True, null=True)
    authorized_by_employee = models.ForeignKey(Employee, on_delete=models.DO_NOTHING, db_column='AuthorizedBy', related_name='authorized_po_amds', blank=True, null=True)


    class Meta:
        managed = False
        db_table = 'tblMM_PO_Amd_Master'
        verbose_name = 'Purchase Order Amendment Master'
        verbose_name_plural = 'Purchase Order Amendment Masters'

    def __str__(self):
        return f"Amendment {self.AmendmentNo} for PO {self.original_po_master.PONo}"


class PurchaseOrderAmendmentDetail(models.Model):
    Id = models.IntegerField(db_column='Id', primary_key=True)
    MId = models.IntegerField(db_column='MId') # FK to PurchaseOrderAmendmentMaster
    PODId = models.IntegerField(db_column='PODId') # FK to original PurchaseOrderDetail.Id
    Qty = models.FloatField(db_column='Qty')
    Rate = models.FloatField(db_column='Rate')
    Discount = models.FloatField(db_column='Discount')
    DelDate = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    PF = models.IntegerField(db_column='PF', blank=True, null=True)
    ExST = models.IntegerField(db_column='ExST', blank=True, null=True)
    VAT = models.IntegerField(db_column='VAT', blank=True, null=True)
    SPRId = models.IntegerField(db_column='SPRId', blank=True, null=True)
    BudgetCode = models.IntegerField(db_column='BudgetCode', blank=True, null=True)
    AddDesc = models.TextField(db_column='AddDesc', blank=True, null=True)

    po_amendment_master = models.ForeignKey(PurchaseOrderAmendmentMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    original_po_detail = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='PODId', related_name='amendment_details', blank=True, null=True)
    packing_term = models.ForeignKey(PackingTerm, on_delete=models.DO_NOTHING, db_column='PF', related_name='po_amd_details', blank=True, null=True)
    excise_tax = models.ForeignKey(ExciseTax, on_delete=models.DO_NOTHING, db_column='ExST', related_name='po_amd_details', blank=True, null=True)
    vat_tax = models.ForeignKey(VATTax, on_delete=models.DO_NOTHING, db_column='VAT', related_name='po_amd_details', blank=True, null=True)
    spr_detail = models.ForeignKey(SPRDetail, on_delete=models.DO_NOTHING, db_column='SPRId', related_name='po_amd_details', blank=True, null=True)
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', related_name='po_amd_details', blank=True, null=True)


    class Meta:
        managed = False
        db_table = 'tblMM_PO_Amd_Details'
        verbose_name = 'Purchase Order Amendment Detail'
        verbose_name_plural = 'Purchase Order Amendment Details'

    def __str__(self):
        return f"PO Amendment Detail {self.Id}"

```

### 4.2 Forms (`material_management/forms.py`)

As this specific ASP.NET page (`PO_SPR_Print_Page.aspx`) is for displaying a report and has no input fields, no direct `ModelForm` is required for this view. If the application were to have a form for creating/editing Purchase Orders, it would be defined here following the general `ModelForm` template.

```python
# No forms.py needed for this specific report display page.
# A full application would have forms for creating/updating POs.
```

### 4.3 Views (`material_management/views.py`)

We will create a `DetailView` to display the report. To satisfy the "DataTables for all list views" requirement, we'll also include a placeholder `POListView` and a partial view for its table content.

```python
from django.views.generic import DetailView, ListView
from django.shortcuts import get_object_or_404, render
from django.http import HttpResponse, Http404
from django.contrib import messages
from django.urls import reverse_lazy

from .models import PurchaseOrderMaster, PurchaseOrderAmendmentMaster, format_date_dmy

# View for displaying the detailed PO report
class POReportDetailView(DetailView):
    model = PurchaseOrderMaster
    template_name = 'material_management/purchase_order/po_report_detail.html'
    context_object_name = 'po_master'

    def get_object(self, queryset=None):
        po_master_id = self.request.GET.get('mid')
        amendment_no = self.request.GET.get('AmdNo', 0) # Default to 0 for initial PO
        company_id = self.request.session.get('compid') # From ASP.NET Session["compid"]

        if not po_master_id or not company_id:
            raise Http404("Missing required parameters (mid or company_id).")

        try:
            po_master_id = int(po_master_id)
            amendment_no = int(amendment_no)
            company_id = int(company_id)
        except ValueError:
            raise Http404("Invalid parameter format.")

        # Use the custom manager method to get all report data
        report_data, main_report_params, is_import_po = PurchaseOrderMaster.objects.get_detailed_report_data(
            po_master_id, company_id, amendment_no
        )

        if report_data is None:
            raise Http404("Purchase Order not found or data error.")

        self.report_data = report_data
        self.main_report_params = main_report_params
        self.is_import_po = is_import_po

        # Return the actual PO Master object to satisfy DetailView's get_object
        return get_object_or_404(PurchaseOrderMaster, pk=po_master_id, CompId=company_id)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['report_items'] = self.report_data
        context['report_params'] = self.main_report_params
        context['is_import_po'] = self.is_import_po
        return context

# Placeholder for a PO list view, using DataTables as requested
class POListView(ListView):
    model = PurchaseOrderMaster
    template_name = 'material_management/purchase_order/list.html'
    context_object_name = 'purchase_orders'

    # This view would typically filter/paginate results
    def get_queryset(self):
        # Example: filter by company ID from session
        company_id = self.request.session.get('compid')
        if company_id:
            return PurchaseOrderMaster.objects.filter(CompId=company_id).order_by('-SysDate', '-Id')
        return PurchaseOrderMaster.objects.none()

# Partial view for HTMX loading of the PO list table (DataTables)
class POTablePartialView(POListView):
    template_name = 'material_management/purchase_order/_po_list_table.html'

    def render_to_response(self, context, **response_kwargs):
        # Ensure a 200 OK for HTMX partial
        return super().render_to_response(context, **response_kwargs)

```

### 4.4 Templates (`material_management/templates/material_management/purchase_order/`)

**`po_report_detail.html` (for the detailed PO report, main conversion target):**

This template will render the data prepared by the `POReportDetailView`. It's designed to mimic a report layout, not a DataTables grid.

```html
{% extends 'core/base.html' %}

{% block title %}Purchase Order Report - {{ po_master.PONo }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-4xl bg-white shadow-lg rounded-lg print:shadow-none print:rounded-none print:p-0">
    <div class="flex justify-between items-center mb-6 print:hidden">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Order Report: {{ po_master.PONo }} (Amd No: {{ po_master.AmendmentNo }})</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded print:hidden"
            onclick="window.print()">
            Print Report
        </button>
    </div>

    <!-- Report Header Section -->
    <div class="mb-8 p-4 border border-gray-200 rounded print:border-0">
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <p><strong>Company Address:</strong><br>{{ report_params.Address2|linebreaksbr }}</p>
            </div>
            <div>
                <p><strong>Supplier Name:</strong> {{ report_items.0.SuplierName }}{{ report_items.0.SuplierName* }} (ID: {{ report_items.0.SupplierId }}{{ report_items.0.VenderCode* }})</p>
                <p><strong>Supplier Address:</strong><br>{{ report_params.SupplierAddress|linebreaksbr }}</p>
                <p><strong>Contact Person:</strong> {{ report_items.0.ContactPerson }}</p>
                <p><strong>Contact No:</strong> {{ report_items.0.ContactNo }}</p>
                <p><strong>Email:</strong> {{ report_items.0.Email }}</p>
            </div>
        </div>
        <hr class="my-4 border-gray-200 print:hidden" />
        <div class="grid grid-cols-3 gap-4 text-sm">
            <div>
                <p><strong>PO Number:</strong> {{ report_items.0.PONo }}</p>
                <p><strong>Registration Date:</strong> {{ report_params.RegDate }}</p>
            </div>
            <div>
                <p><strong>Reference:</strong> {{ report_items.0.RefDesc }}{{ report_items.0.RefDesc* }}</p>
                <p><strong>Reference Date:</strong> {{ report_params.RefDate }}{{ report_items.0.RefDate* }}</p>
                <p><strong>Reference PO Desc:</strong> {{ report_items.0.RefPODesc }}{{ report_items.0.RefPODesc* }}</p>
            </div>
            <div>
                <p><strong>Amendment No:</strong> {{ report_items.0.AmdNo }}</p>
                <p><strong>Mode of Dispatch:</strong> {{ report_items.0.ModeOfDispatch }}{{ report_items.0.ModeOfDispatch* }}</p>
                <p><strong>Inspection:</strong> {{ report_items.0.Inspection }}{{ report_items.0.Inspection* }}</p>
            </div>
        </div>
    </div>

    <!-- PO Details Table -->
    <h3 class="text-xl font-semibold mb-4 text-gray-700">Order Details</h3>
    <div class="overflow-x-auto mb-8">
        <table class="min-w-full bg-white border border-gray-200 text-sm">
            <thead>
                <tr class="bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <th class="py-2 px-3 border-b border-gray-200">SN</th>
                    <th class="py-2 px-3 border-b border-gray-200">Item Code</th>
                    <th class="py-2 px-3 border-b border-gray-200">Manf Desc</th>
                    <th class="py-2 px-3 border-b border-gray-200">UOM</th>
                    <th class="py-2 px-3 border-b border-gray-200">Qty</th>
                    <th class="py-2 px-3 border-b border-gray-200">Rate</th>
                    <th class="py-2 px-3 border-b border-gray-200">Discount</th>
                    <th class="py-2 px-3 border-b border-gray-200">Delivery Date</th>
                    <th class="py-2 px-3 border-b border-gray-200">Budget Code</th>
                    <th class="py-2 px-3 border-b border-gray-200">Add. Desc</th>
                    <th class="py-2 px-3 border-b border-gray-200">SPR No</th>
                    <th class="py-2 px-3 border-b border-gray-200">WO No</th>
                    <th class="py-2 px-3 border-b border-gray-200">Ac Head</th>
                    <th class="py-2 px-3 border-b border-gray-200">Department</th>
                </tr>
            </thead>
            <tbody>
                {% for item in report_items %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-3 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.ItemCode }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.manfDesc }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.UOMBasic }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.POQty }}{{ item.POQty* }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.Rate }}{{ item.Rate* }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.Discount }}{{ item.Discount* }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.DelDate }}{{ item.DelDate* }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.BudgetCode }}{{ item.BudgetCode* }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.AddDesc }}{{ item.AddDesc* }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.SPRNo }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.WONo }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.AcHead }}</td>
                    <td class="py-2 px-3 border-b border-gray-200">{{ item.Dept }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Terms and Other Details -->
    <div class="grid grid-cols-2 gap-4 text-sm mb-8">
        <div>
            <h4 class="font-semibold text-gray-700 mb-2">Terms:</h4>
            <p><strong>Payment Terms:</strong> {{ report_items.0.PaymentTerm }}{{ report_items.0.PaymentTerm* }}</p>
            <p><strong>Packing & Forwarding:</strong> {{ report_items.0.PackagingTerm }} ({{ report_items.0.PackagingValue }}{{ report_items.0.PackagingValue* }})</p>
            <p><strong>VAT:</strong> {{ report_items.0.VatTerm }} ({{ report_items.0.VatValue }}{{ report_items.0.VatValue* }})</p>
            <p><strong>Excise/Service Tax:</strong> {{ report_items.0.ExciseTerm }} ({{ report_items.0.ExciseValue }}{{ report_items.0.ExciseValue* }})</p>
            <p><strong>Octroi:</strong> {{ report_items.0.OctriTerm }} ({{ report_items.0.OctriValue }}{{ report_items.0.OctriValue* }})</p>
            <p><strong>Warranty:</strong> {{ report_items.0.Warranty }}{{ report_items.0.Warranty* }}</p>
            <p><strong>Freight:</strong> {{ report_items.0.Fright }}{{ report_items.0.Fright* }}</p>
            <p><strong>Insurance:</strong> {{ report_items.0.Insurance }}{{ report_items.0.Insurance* }}</p>
        </div>
        <div>
            <h4 class="font-semibold text-gray-700 mb-2">Other Information:</h4>
            <p><strong>Remarks:</strong> {{ report_items.0.Remarks }}{{ report_items.0.Remarks* }}</p>
            <p><strong>Ship To:</strong> {{ report_items.0.ShipTo }}{{ report_items.0.ShipTo* }}</p>
            <p><strong>Indentor:</strong> {{ report_items.0.Indentor }}</p>
        </div>
    </div>

    <!-- Signatures and Approvals -->
    <div class="grid grid-cols-3 gap-4 text-sm mb-8">
        <div>
            <p><strong>Checked By:</strong> {{ report_params.CheckedBy }}</p>
            <p><strong>Date:</strong> {{ report_params.CheckedDate }}</p>
        </div>
        <div>
            <p><strong>Approved By:</strong> {{ report_params.ApprovedBy }}</p>
            <p><strong>Date:</strong> {{ report_params.ApproveDate }}</p>
        </div>
        <div>
            <p><strong>Authorized By:</strong> {{ report_params.AuthorizedBy }}</p>
            <p><strong>Date:</strong> {{ report_params.AuthorizeDate }}</p>
        </div>
    </div>

    <!-- Terms and Conditions -->
    <div class="border-t border-gray-200 pt-4 mt-4 text-sm">
        <h4 class="font-semibold text-gray-700 mb-2">Terms and Conditions{{ report_params.TC* }}:</h4>
        <p class="whitespace-pre-wrap">{{ report_params.TC }}</p>
    </div>

</div>
{% endblock %}
```

**`list.html` (Hypothetical PO list using DataTables):**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Purchase Orders</h2>
        {# Assuming there's a PO creation flow, which was not part of the original .aspx #}
        {# <button #}
        {#     class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" #}
        {#     hx-get="{% url 'purchase_order_add' %}" #}
        {#     hx-target="#modalContent" #}
        {#     hx-trigger="click" #}
        {#     _="on click add .is-active to #modal"> #}
        {#     Add New Purchase Order #}
        {# </button> #}
    </div>
    
    <div id="purchase_orderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'purchase_order_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Purchase Orders...</p>
        </div>
    </div>
    
    <!-- Modal for forms (if any CRUD operations were added) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
    });
</script>
{% endblock %}
```

**`_po_list_table.html` (Partial for DataTables, loaded via HTMX):**

```html
<table id="purchase_orderTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amendment No</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for po in purchase_orders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.PONo }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.supplier.SupplierName|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.AmendmentNo }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ po.SysDate|date:"d M Y" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a 
                    href="{% url 'po_report_detail' %}?mid={{ po.Id }}&AmdNo={{ po.AmendmentNo }}&Code={{ po.SupplierId }}"
                    target="_blank"
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2">
                    View Report
                </a>
                {# Add edit/delete buttons if actual CRUD was part of this list view #}
                {# <button #}
                {#     class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2" #}
                {#     hx-get="{% url 'purchase_order_edit' po.pk %}" #}
                {#     hx-target="#modalContent" #}
                {#     hx-trigger="click" #}
                {#     _="on click add .is-active to #modal"> #}
                {#     Edit #}
                {# </button> #}
                {# <button #}
                {#     class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded" #}
                {#     hx-get="{% url 'purchase_order_delete' po.pk %}" #}
                {#     hx-target="#modalContent" #}
                {#     hx-trigger="click" #}
                {#     _="on click add .is-active to #modal"> #}
                {#     Delete #}
                {# </button> #}
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="6" class="py-4 px-4 text-center text-gray-500">No purchase orders found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#purchase_orderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "order": [[4, "desc"]] // Order by Date descending
    });
});
</script>
```

### 4.5 URLs (`material_management/urls.py`)

```python
from django.urls import path
from .views import POReportDetailView, POListView, POTablePartialView

urlpatterns = [
    # URL for the main PO list page (using DataTables)
    path('purchase-orders/', POListView.as_view(), name='purchase_order_list'),
    # HTMX endpoint to load the DataTables content
    path('purchase-orders/table/', POTablePartialView.as_view(), name='purchase_order_table'),

    # URL for the specific PO Report detail page (replaces ASP.NET print page)
    # Parameters for mid, AmdNo, Code will be passed as query parameters like the original ASP.NET
    path('purchase-orders/report/', POReportDetailView.as_view(), name='po_report_detail'),
]
```

### 4.6 Tests (`material_management/tests.py`)

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock

# Import all models to ensure they are covered by tests
from .models import (
    Company, Employee, Supplier, Country, State, City, ItemMaster, UnitMaster,
    AccHead, BusinessGroup, BudgetCode, SPRMaster, SPRDetail, POReference,
    PackingTerm, VATTax, ExciseTax, OctroiTax, PaymentTerm, WarrantyTerm, FreightTerm,
    PurchaseOrderMaster, PurchaseOrderDetail, PurchaseOrderAmendmentMaster, PurchaseOrderAmendmentDetail,
    format_date_dmy
)

class ModelCreationTest(TestCase):
    def test_basic_model_creation(self):
        # Test creation of a few essential lookup models
        company = Company.objects.create(CompId=1, CompanyName='Test Co', Address='123 Main St')
        employee = Employee.objects.create(EmpId=101, Title='Mr.', EmployeeName='John Doe', CompId=1)
        supplier = Supplier.objects.create(SupplierId='SUP001', SupplierName='Test Supplier', CompId=1)
        country = Country.objects.create(CId=1, CountryName='India', Symbol='INR')
        item = ItemMaster.objects.create(Id=1, ItemCode='ITEM001', ManfDesc='Test Mfg', CompId=1)
        unit = UnitMaster.objects.create(Id=1, Symbol='KG')
        acchead = AccHead.objects.create(Id=1, Symbol='RAW_MAT')
        budget = BudgetCode.objects.create(Id=1, Symbol='PROJ-A')
        business_group = BusinessGroup.objects.create(Id=1, Symbol='PROD')
        
        self.assertIsNotNone(company)
        self.assertEqual(employee.EmployeeName, 'John Doe')
        self.assertEqual(supplier.SupplierName, 'Test Supplier')
        self.assertEqual(country.CountryName, 'India')
        self.assertEqual(item.ItemCode, 'ITEM001')
        self.assertEqual(unit.Symbol, 'KG')
        self.assertEqual(acchead.Symbol, 'RAW_MAT')
        self.assertEqual(budget.Symbol, 'PROJ-A')
        self.assertEqual(business_group.Symbol, 'PROD')

class PurchaseOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create base data for PO report generation tests
        cls.company = Company.objects.create(CompId=1, CompanyName='Test Co', Address='123 Main St')
        cls.domestic_country = Country.objects.create(CId=1, CountryName='India', Symbol='INR')
        cls.foreign_country = Country.objects.create(CId=2, CountryName='USA', Symbol='USD')
        cls.state = State.objects.create(SId=1, StateName='Delhi')
        cls.city = City.objects.create(CityId=1, CityName='Delhi')
        cls.supplier = Supplier.objects.create(
            SupplierId='SUP001', SupplierName='Domestic Supplier', CompId=1,
            RegdCountry=cls.domestic_country.CId, RegdAddress='456 Vendor Rd', RegdState=cls.state.SId, RegdCity=cls.city.CityId, RegdPinNo='110001'
        )
        cls.foreign_supplier = Supplier.objects.create(
            SupplierId='SUP002', SupplierName='Foreign Supplier', CompId=1,
            RegdCountry=cls.foreign_country.CId, RegdAddress='789 Global St', RegdState=None, RegdCity=None, RegdPinNo='12345'
        )
        
        cls.checked_by_emp = Employee.objects.create(EmpId=101, Title='Mr.', EmployeeName='John Doe', CompId=1)
        cls.approved_by_emp = Employee.objects.create(EmpId=102, Title='Ms.', EmployeeName='Jane Smith', CompId=1)
        cls.authorized_by_emp = Employee.objects.create(EmpId=103, Title='Dr.', EmployeeName='Alex Lee', CompId=1)
        cls.indentor_emp = Employee.objects.create(EmpId=104, Title='Eng.', EmployeeName='Priya Sharma', CompId=1)

        cls.po_ref = POReference.objects.create(Id=1, RefDesc='Standard Purchase')
        cls.packing_term = PackingTerm.objects.create(Id=1, Terms='10% Packing', Value=10.0)
        cls.vat_tax = VATTax.objects.create(Id=1, Terms='18% VAT', Value=18.0)
        cls.excise_tax = ExciseTax.objects.create(Id=1, Terms='5% Excise', Value=5.0)
        cls.octroi_tax = OctroiTax.objects.create(Id=1, Terms='2% Octroi', Value=2.0)
        cls.payment_term = PaymentTerm.objects.create(Id=1, Terms='30 days net')
        cls.warranty_term = WarrantyTerm.objects.create(Id=1, Terms='1 year warranty')
        cls.freight_term = FreightTerm.objects.create(Id=1, Terms='FOB Destination')

        cls.item_master = ItemMaster.objects.create(Id=1, ItemCode='ITM001', ManfDesc='Component A', UOMBasic=1, CompId=1)
        cls.unit_master = UnitMaster.objects.create(Id=1, Symbol='PCS')
        cls.acc_head = AccHead.objects.create(Id=1, Symbol='RAW_MAT')
        cls.budget_code = BudgetCode.objects.create(Id=1, Symbol='PRJ-123')
        cls.business_group = BusinessGroup.objects.create(Id=1, Symbol='ENG')

        cls.spr_master = SPRMaster.objects.create(Id=1, SessionId=cls.indentor_emp.EmpId)
        cls.spr_detail = SPRDetail.objects.create(
            Id=1, MId=cls.spr_master.Id, SPRNo='SPR001', WONo='WO001', DeptId=cls.business_group.Id,
            AHId=cls.acc_head.Id, ItemId=cls.item_master.Id
        )

        cls.po_master = PurchaseOrderMaster.objects.create(
            Id=1, PONo='PO001', CompId=1, AmendmentNo=0, SupplierId=cls.supplier.SupplierId,
            SysDate=timezone.now(), ReferenceDate=timezone.now() - timedelta(days=7),
            CheckedBy=cls.checked_by_emp.EmpId, ApprovedBy=cls.approved_by_emp.EmpId, AuthorizedBy=cls.authorized_by_emp.EmpId,
            CheckedDate=timezone.now(), ApproveDate=timezone.now(), AuthorizeDate=timezone.now(),
            Reference=cls.po_ref.Id, ReferenceDesc='Initial PO Ref Desc',
            ModeOfDispatch='Air', Inspection='QA Check', Remarks='Standard delivery', ShipTo='Warehouse A',
            Freight=cls.freight_term.Id, Octroi=cls.octroi_tax.Id, Warrenty=cls.warranty_term.Id,
            PaymentTerms=cls.payment_term.Id, Insurance='Covered', TC='Standard T&C apply.'
        )
        cls.po_detail = PurchaseOrderDetail.objects.create(
            Id=101, MId=cls.po_master.Id, Qty=100.0, Rate=10.0, Discount=0.5, DelDate=timezone.now() + timedelta(days=30),
            PF=cls.packing_term.Id, ExST=cls.excise_tax.Id, VAT=cls.vat_tax.Id, SPRId=cls.spr_detail.Id,
            BudgetCode=cls.budget_code.Id, AddDesc='Additional description for item 1'
        )

        # Create an amendment for comparison
        cls.po_amd_master = PurchaseOrderAmendmentMaster.objects.create(
            Id=2, POId=cls.po_master.Id, PONo='PO001', CompId=1, AmendmentNo=1, SupplierId=cls.supplier.SupplierId,
            SysDate=timezone.now() + timedelta(days=1), ReferenceDate=timezone.now() - timedelta(days=5),
            CheckedBy=cls.checked_by_emp.EmpId, ApprovedBy=cls.approved_by_emp.EmpId, AuthorizedBy=cls.authorized_by_emp.EmpId,
            CheckedDate=timezone.now() + timedelta(days=1), ApproveDate=timezone.now() + timedelta(days=1), AuthorizeDate=timezone.now() + timedelta(days=1),
            Reference=cls.po_ref.Id, ReferenceDesc='Amended PO Ref Desc', # Changed
            ModeOfDispatch='Sea', Inspection='QC Check', Remarks='Urgent delivery', ShipTo='Warehouse B',
            Freight=cls.freight_term.Id, Octroi=cls.octroi_tax.Id, Warrenty=cls.warranty_term.Id,
            PaymentTerms=cls.payment_term.Id, Insurance='Not Covered', TC='Amended T&C apply.'
        )
        cls.po_amd_detail = PurchaseOrderAmendmentDetail.objects.create(
            Id=201, MId=cls.po_amd_master.Id, PODId=cls.po_detail.Id, Qty=120.0, Rate=11.0, Discount=0.4, DelDate=timezone.now() + timedelta(days=40),
            PF=cls.packing_term.Id, ExST=cls.excise_tax.Id, VAT=cls.vat_tax.Id, SPRId=cls.spr_detail.Id,
            BudgetCode=cls.budget_code.Id, AddDesc='Revised description for item 1'
        )

    def test_get_detailed_report_data_initial_po(self):
        # Test for the initial PO (amendment_no=0)
        report_data, params, is_import_po = PurchaseOrderMaster.objects.get_detailed_report_data(
            self.po_master.Id, self.company.CompId, 0
        )

        self.assertIsNotNone(report_data)
        self.assertEqual(len(report_data), 1)
        item = report_data[0]

        self.assertEqual(item['PONo'], 'PO001')
        self.assertEqual(item['POQty'], 100.0)
        self.assertEqual(item['Rate'], 10.0)
        self.assertEqual(item['Discount'], 0.5)
        self.assertEqual(item['SPRNo'], 'SPR001')
        self.assertEqual(item['WONo'], 'WO001')
        self.assertEqual(item['ItemCode'], 'ITM001')
        self.assertEqual(item['UOMBasic'], 'PCS')
        self.assertEqual(item['SuplierName'], 'Domestic Supplier')
        self.assertEqual(item['Symbol'], 'INR') # Country Symbol
        self.assertEqual(item['Insurance'], 'Covered')
        self.assertEqual(item['Remarks'], 'Standard delivery')
        self.assertEqual(item['AddDesc'], 'Additional description for item 1')

        # Check that no comparison asterisks are present for initial PO
        self.assertEqual(item['Rate*'], '')
        self.assertEqual(item['RefDesc*'], '')
        self.assertEqual(item['AddDesc*'], '')

        # Check report parameters
        self.assertIsNotNone(params)
        self.assertIn('Test Co', params['Address2'])
        self.assertIn('Domestic Supplier', params['SupplierAddress'])
        self.assertEqual(params['CheckedBy'], 'Mr. John Doe')
        self.assertEqual(params['TC'], 'Standard T&C apply.')
        self.assertFalse(is_import_po)

    def test_get_detailed_report_data_amended_po(self):
        # Test for an amended PO (amendment_no=1) and check comparisons
        report_data, params, is_import_po = PurchaseOrderMaster.objects.get_detailed_report_data(
            self.po_master.Id, self.company.CompId, 1
        )

        self.assertIsNotNone(report_data)
        self.assertEqual(len(report_data), 1)
        item = report_data[0]

        # Verify data from amendment
        self.assertEqual(item['POQty'], 120.0)
        self.assertEqual(item['Rate'], 11.0)
        self.assertEqual(item['Discount'], 0.4)
        self.assertEqual(item['Remarks'], 'Urgent delivery') # From amendment
        self.assertEqual(item['AddDesc'], 'Revised description for item 1') # From amendment
        self.assertEqual(item['Insurance'], 'Not Covered') # From amendment

        # Check comparison asterisks
        self.assertEqual(item['Rate*'], '*') # Rate changed from 10.0 to 11.0
        self.assertEqual(item['Discount*'], '*') # Discount changed from 0.5 to 0.4
        self.assertEqual(item['POQty*'], '*') # Qty changed from 100.0 to 120.0
        self.assertEqual(item['AddDesc*'], '*') # AddDesc changed
        self.assertEqual(item['Remarks*'], '*') # Remarks changed
        self.assertEqual(item['Insurance*'], '*') # Insurance changed

        # Check report parameters from amendment
        self.assertEqual(params['TC'], 'Amended T&C apply.') # From amendment
        self.assertFalse(is_import_po) # Still domestic supplier

    def test_get_detailed_report_data_foreign_supplier(self):
        # Create a PO with a foreign supplier to test import report logic
        po_master_foreign = PurchaseOrderMaster.objects.create(
            Id=3, PONo='PO003', CompId=1, AmendmentNo=0, SupplierId=self.foreign_supplier.SupplierId,
            SysDate=timezone.now(), ReferenceDate=timezone.now(),
            Freight=self.freight_term.Id, Octroi=self.octroi_tax.Id, Warrenty=self.warranty_term.Id, PaymentTerms=self.payment_term.Id
        )
        PurchaseOrderDetail.objects.create(Id=301, MId=po_master_foreign.Id, Qty=50.0, Rate=20.0, Discount=0.0)

        report_data, params, is_import_po = PurchaseOrderMaster.objects.get_detailed_report_data(
            po_master_foreign.Id, self.company.CompId, 0
        )
        self.assertTrue(is_import_po)
        self.assertIn('Foreign Supplier', params['SupplierAddress'])


class POReportViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        # Re-use setUpTestData from the model test, but ensure it's called once.
        # For simplicity in this example, directly create minimal data here.
        self.company = Company.objects.create(CompId=1, CompanyName='Test Co', Address='123 Main St')
        self.domestic_country = Country.objects.create(CId=1, CountryName='India', Symbol='INR')
        self.supplier = Supplier.objects.create(
            SupplierId='SUP001', SupplierName='Test Supplier', CompId=1, RegdCountry=self.domestic_country.CId
        )
        self.po_master = PurchaseOrderMaster.objects.create(
            Id=1, PONo='PO001', CompId=1, AmendmentNo=0, SupplierId=self.supplier.SupplierId,
            SysDate=timezone.now(), ReferenceDate=timezone.now(), TC='Test T&C'
        )
        self.po_detail = PurchaseOrderDetail.objects.create(
            Id=101, MId=self.po_master.Id, Qty=10.0, Rate=100.0, Discount=0.0
        )
        self.client.session['compid'] = self.company.CompId # Mimic session data

    def test_report_detail_view_success(self):
        url = reverse('po_report_detail') + f'?mid={self.po_master.Id}&AmdNo=0&Code={self.supplier.SupplierId}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchase_order/po_report_detail.html')
        self.assertIn('po_master', response.context)
        self.assertIn('report_items', response.context)
        self.assertIn('report_params', response.context)
        self.assertContains(response, 'Purchase Order Report: PO001')
        self.assertContains(response, 'Standard T&C apply.')

    def test_report_detail_view_missing_params(self):
        url = reverse('po_report_detail') # Missing mid, AmdNo, Code
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_report_detail_view_invalid_params(self):
        url = reverse('po_report_detail') + f'?mid=abc&AmdNo=0&Code={self.supplier.SupplierId}'
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

    def test_report_detail_view_po_not_found(self):
        url = reverse('po_report_detail') + f'?mid=999&AmdNo=0&Code={self.supplier.SupplierId}' # Non-existent PO
        response = self.client.get(url)
        self.assertEqual(response.status_code, 404)

class POListViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.company = Company.objects.create(CompId=1, CompanyName='Test Co', Address='123 Main St')
        self.supplier = Supplier.objects.create(SupplierId='SUP001', SupplierName='Test Supplier', CompId=1)
        self.po_master_1 = PurchaseOrderMaster.objects.create(
            Id=1, PONo='PO001', CompId=1, AmendmentNo=0, SupplierId=self.supplier.SupplierId,
            SysDate=timezone.now() - timedelta(days=1), ReferenceDate=timezone.now() - timedelta(days=8)
        )
        self.po_master_2 = PurchaseOrderMaster.objects.create(
            Id=2, PONo='PO002', CompId=1, AmendmentNo=0, SupplierId=self.supplier.SupplierId,
            SysDate=timezone.now(), ReferenceDate=timezone.now() - timedelta(days=7)
        )
        self.client.session['compid'] = self.company.CompId

    def test_list_view_success(self):
        response = self.client.get(reverse('purchase_order_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchase_order/list.html')
        self.assertIn('purchase_orders', response.context)
        self.assertEqual(response.context['purchase_orders'].count(), 2)
        # Test order by SysDate descending
        self.assertEqual(list(response.context['purchase_orders']), [self.po_master_2, self.po_master_1])
        self.assertContains(response, 'Purchase Orders')

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchase_order_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchase_order/_po_list_table.html')
        self.assertIn('purchase_orders', response.context)
        self.assertEqual(response.context['purchase_orders'].count(), 2)
        self.assertContains(response, 'PO001')
        self.assertContains(response, 'PO002')
        self.assertContains(response, 'dataTables') # Check for DataTables JS initialization

```

---

## Step 5: HTMX and Alpine.js Integration

**HTMX for dynamic content:**
*   The `purchase_order_list.html` uses HTMX to load the `_po_list_table.html` partial. This means the DataTables content is fetched dynamically.
*   `hx-trigger="load, refreshPurchaseOrderList from:body"` ensures the table loads on page load and refreshes if a custom `refreshPurchaseOrderList` event is triggered (e.g., after a CRUD operation on a PO, though not implemented for this report page).

**Alpine.js for UI state management:**
*   The `list.html` includes a modal (hidden by default) for potential form interactions. Alpine.js (`_="on click add .is-active to #modal"`) handles showing/hiding this modal based on button clicks.
*   The `po_report_detail.html` is primarily a static display. HTMX is not used directly for displaying the report itself, but for a typical PO list view, it would drive CRUD forms within modals.

**DataTables for list views:**
*   The `_po_list_table.html` partial explicitly initializes `jQuery.DataTables()` on the `#purchase_orderTable`. This handles client-side searching, sorting, and pagination for the list of purchase orders. This fulfills the requirement for DataTables for *list views*.

**No full page reloads:**
*   When navigating the PO list, the table content can be refreshed via HTMX without a full page reload.
*   Viewing a report (clicking "View Report") opens a new tab/window, which is a common pattern for print-friendly reports and aligns with the original ASP.NET behavior (where the viewer opens a new report).

---

## Final Notes

*   **Placeholders:** All `[PLACEHOLDER]` values have been replaced with concrete names and logic derived from the ASP.NET code.
*   **DRY:** The `POListView` and `POTablePartialView` leverage inheritance to keep the queryset logic DRY. The `PurchaseOrderManager.get_detailed_report_data` method centralizes complex data fetching and transformation, ensuring the views remain thin.
*   **Business Logic in Models:** The extensive data lookup, aggregation, and particularly the amendment comparison logic (marking fields with `*`) are entirely encapsulated within the `PurchaseOrderManager`, adhering strictly to the "fat model" principle.
*   **Test Coverage:** Comprehensive unit tests are provided for the models, especially for the complex `get_detailed_report_data` method, and integration tests for the views to ensure functionality and template rendering are correct.
*   **Print Functionality:** While Crystal Reports are gone, the HTML template is structured to be print-friendly, and a simple `window.print()` button is provided. For true PDF generation, `WeasyPrint` or similar would be integrated.
*   **Session Data:** The `CompId` is accessed from `request.session`, mimicking the ASP.NET `Session["compid"]`. This assumes appropriate session middleware is configured in Django.
*   **Security:** The original ASP.NET code had commented-out `try-catch` blocks. In Django, proper error handling and input validation (e.g., using `get_object_or_404` and explicit `int()` conversions) are crucial to prevent `Http404` errors for invalid inputs and ensure robustness. Authorization (e.g., ensuring `compid` matches the user's allowed company) would typically be handled via Django's authentication and authorization system, perhaps with custom mixins, which is beyond the scope of this code migration but is a critical modernization step.