## ASP.NET to Django Conversion Script: Comprehensive Modernization Plan

This document outlines a detailed modernization plan for transitioning your legacy ASP.NET application, specifically the `PO_Edit_Details_PO_Grid.aspx` module, to a modern Django-based solution. Our approach prioritizes automation, maintainability, and enhanced user experience through Django 5.0+, HTMX, Alpine.js, and DataTables.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Analysis:**
The ASP.NET code for `PO_Edit_Details_PO_Grid.aspx.cs` fetches data primarily from `tblMM_PO_Details` and `tblMM_PO_Master`, performing complex lookups from several other tables (`tblDG_Item_Master`, `Unit_Master`, `tblPacking_Master`, `tblExciseser_Master`, `tblVAT_Master`, `AccHead`, `BusinessGroup`, `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_SPR_Master`, `tblMM_SPR_Details`) based on flags and IDs. It also excludes entries found in `tblMM_PO_Amd_Temp`.

For the purpose of this migration, we will model the main `tblMM_PO_Details` and `tblMM_PO_Master` tables directly. The auxiliary tables will be represented by Django models with `managed = False` to enable lookups within our "fat model" approach.

**Primary Tables Identified:**
- `tblMM_PO_Master`: Contains master details for Purchase Orders, including `PONo`, `Id`, `CompId` (Company ID), and `PRSPRFlag` (indicating if details are from PR or SPR).
- `tblMM_PO_Details`: Contains line-item details for Purchase Orders, including `Id`, `PONo`, `MId` (linking to `tblMM_PO_Master.Id`), `PRNo`, `PRId`, `SPRNo`, `SPRId`, `Qty`, `Rate`, `Discount`, `PF` (Packing/Forwarding ID), `ExST` (Excise/Service Tax ID), `VAT` (VAT ID).

**Auxiliary Tables (for Lookups):**
- `tblDG_Item_Master`: Item details (`Id`, `ItemCode`, `ManfDesc`, `UOMBasic`).
- `Unit_Master`: Unit of Measurement details (`Id`, `Symbol`).
- `tblPacking_Master`: Packing/Forwarding terms (`Id`, `Terms`).
- `tblExciseser_Master`: Excise/Service Tax terms (`Id`, `Terms`).
- `tblVAT_Master`: VAT terms (`Id`, `Terms`).
- `AccHead`: Account Head details (`Id`, `Symbol`).
- `BusinessGroup`: Business Group details (`Id`, `Symbol`).
- `tblMM_PR_Master`: Purchase Requisition Master (`Id`, `PRNo`, `WONo`, `CompId`).
- `tblMM_PR_Details`: Purchase Requisition Details (`Id`, `PRNo`, `MId`, `ItemId`, `AHId`).
- `tblMM_SPR_Master`: Sales Purchase Requisition Master (`Id`, `SPRNo`, `CompId`).
- `tblMM_SPR_Details`: Sales Purchase Requisition Details (`Id`, `SPRNo`, `MId`, `ItemId`, `WONo`, `DeptId`, `AHId`).
- `tblMM_PO_Amd_Temp`: Temporary table for amended POs (`POId`).

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Analysis:**
The provided ASP.NET page primarily serves as a "Read" (list/display) view.
-   **Read:** The `LoadSPRData()` method is responsible for querying the database and populating the `GridView2` with purchase order details. This is the core functionality.
-   **Create/Update/Delete:** This specific page does not perform direct Create, Update, or Delete operations on the PO details. The `GridView2_RowCommand` with `CommandName="sel"` indicates a "Select" action, which redirects the user to `PO_Edit_Details_PO_Select.aspx` for further actions (likely editing or viewing a single record in detail).

**Modernization Strategy:**
While the original page is read-only, a comprehensive Django modernization will implement full CRUD capabilities for `PurchaseOrderDetail` records. This ensures future extensibility and alignment with typical business requirements for managing such data. All CRUD operations will be handled using HTMX for seamless user experience without full page reloads.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
-   **`asp:GridView ID="GridView2"`:** This is the central UI component, displaying a paginated list of purchase order details. It includes templated columns for various data points and an action column with a "Select" `LinkButton`.
    -   **Modernization:** This will be replaced by a standard HTML `<table>` element rendered using Django templates, enhanced with **DataTables.js** for client-side searching, sorting, and pagination.
-   **`asp:LinkButton ID="lnkButton" Text="Select"`:** This button triggers a server-side `RowCommand` event, which redirects to an edit page.
    -   **Modernization:** This will be replaced by a standard HTML `<button>` or `<a>` tag utilizing **HTMX** to load the "edit" or "detail" form/page into a modal, avoiding full page redirects.
-   **`asp:Label` controls (e.g., `lblPONo`, `lblItemCode`):** These display data within the `GridView`.
    -   **Modernization:** Replaced by Django template variables (`{{ obj.po_no }}`, `{{ obj.item_code }}`).
-   **`runat="server"` & `AutoEventWireup="true"`:** These are ASP.NET server-side processing attributes.
    -   **Modernization:** Replaced by Django's URL routing, Class-Based Views (CBVs), and template rendering engine. HTMX will manage dynamic server interactions on the frontend.
-   **`link href="../../../Css/yui-datatable.css"`:** Custom CSS for the DataTable.
    -   **Modernization:** Replaced by **Tailwind CSS** classes and DataTables' default styling.
-   **`script src="../../../Javascript/loadingNotifier.js"`:** A custom JavaScript for loading notifications.
    -   **Modernization:** Replaced by HTMX's built-in indicators and/or Alpine.js for UI state management.

### Step 4: Generate Django Code

The following Django application structure will be created within a new Django app named `purchase_management`.

#### 4.1 Models

**Task:** Create Django models based on the database schema, adhering to the "fat model" principle.

**Instructions:**
We define `PurchaseOrderMaster` and `PurchaseOrderDetail` as the main models. Auxiliary models for lookups (e.g., `ItemMaster`, `UnitMaster`) are also defined with `managed=False` to map to existing tables. `PurchaseOrderDetail` includes properties that encapsulate the complex data retrieval logic previously handled by multiple `fun.select` calls in the C# code, thus making the model "fat".

```python
# purchase_management/models.py
from django.db import models

# Helper Models (for Foreign Key Lookups based on IDs from original ASP.NET code)
# These models map to existing database tables and are not managed by Django migrations.

class UnitMaster(models.Model):
    """Represents Unit_Master table for UOM symbols."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol or f"Unit {self.id}"

class ItemMaster(models.Model):
    """Represents tblDG_Item_Master for item details."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manufacturer_description = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # ID for Unit_Master

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or f"Item {self.id}"

    @property
    def uom_basic_symbol(self):
        """Returns the symbol for the basic Unit of Measure."""
        if self.uom_basic_id:
            try:
                return UnitMaster.objects.get(id=self.uom_basic_id).symbol
            except UnitMaster.DoesNotExist:
                pass
        return "N/A"

class PackingMaster(models.Model):
    """Represents tblPacking_Master for packing/forwarding terms."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Term'
        verbose_name_plural = 'Packing Terms'

    def __str__(self):
        return self.terms or f"Packing {self.id}"

class ExciseServiceMaster(models.Model):
    """Represents tblExciseser_Master for excise/service tax terms."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Tax Term'
        verbose_name_plural = 'Excise/Service Tax Terms'

    def __str__(self):
        return self.terms or f"Excise {self.id}"

class VATMaster(models.Model):
    """Represents tblVAT_Master for VAT terms."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Term'
        verbose_name_plural = 'VAT Terms'

    def __str__(self):
        return self.terms or f"VAT {self.id}"

class AccountHead(models.Model):
    """Represents AccHead for account head symbols."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol or f"Account Head {self.id}"

class BusinessGroup(models.Model):
    """Represents BusinessGroup for business group symbols."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol or f"Business Group {self.id}"

class PRMaster(models.Model):
    """Represents tblMM_PR_Master for Purchase Requisition master details."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=255, blank=True, null=True)
    work_order_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no or f"PR Master {self.id}"

class PRDetail(models.Model):
    """Represents tblMM_PR_Details for Purchase Requisition detail items."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=255, blank=True, null=True) # Used for join in C#
    master_id = models.IntegerField(db_column='MId', blank=True, null=True) # FK to PRMaster.Id
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # ID for ItemMaster.Id
    account_head_id = models.IntegerField(db_column='AHId', blank=True, null=True) # ID for AccountHead.Id

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR Detail {self.id}"

    @property
    def item(self):
        """Returns the associated ItemMaster object."""
        if self.item_id:
            try:
                return ItemMaster.objects.get(id=self.item_id)
            except ItemMaster.DoesNotExist:
                pass
        return None
    
    @property
    def account_head(self):
        """Returns the associated AccountHead object."""
        if self.account_head_id:
            try:
                return AccountHead.objects.get(id=self.account_head_id)
            except AccountHead.DoesNotExist:
                pass
        return None

class SPRMaster(models.Model):
    """Represents tblMM_SPR_Master for Sales Purchase Requisition master details."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=255, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no or f"SPR Master {self.id}"

class SPRDetail(models.Model):
    """Represents tblMM_SPR_Details for Sales Purchase Requisition detail items."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=255, blank=True, null=True) # Used for join in C#
    master_id = models.IntegerField(db_column='MId', blank=True, null=True) # FK to SPRMaster.Id
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True) # ID for ItemMaster.Id
    work_order_no = models.CharField(db_column='WONo', max_length=255, blank=True, null=True)
    department_id = models.IntegerField(db_column='DeptId', blank=True, null=True) # ID for BusinessGroup.Id
    account_head_id = models.IntegerField(db_column='AHId', blank=True, null=True) # ID for AccountHead.Id

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"SPR Detail {self.id}"

    @property
    def item(self):
        """Returns the associated ItemMaster object."""
        if self.item_id:
            try:
                return ItemMaster.objects.get(id=self.item_id)
            except ItemMaster.DoesNotExist:
                pass
        return None
    
    @property
    def department(self):
        """Returns the associated BusinessGroup object."""
        if self.department_id:
            try:
                return BusinessGroup.objects.get(id=self.department_id)
            except BusinessGroup.DoesNotExist:
                pass
        return None
    
    @property
    def account_head(self):
        """Returns the associated AccountHead object."""
        if self.account_head_id:
            try:
                return AccountHead.objects.get(id=self.account_head_id)
            except AccountHead.DoesNotExist:
                pass
        return None

class POAmendmentTemp(models.Model):
    """Represents tblMM_PO_Amd_Temp for temporary amended POs."""
    # Assuming POId is the primary key and the only column used for filtering.
    # Adjust primary_key and other fields as per actual schema if available.
    po_id = models.IntegerField(db_column='POId', primary_key=True) 

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Amd_Temp'
        verbose_name = 'PO Amendment Temp'
        verbose_name_plural = 'PO Amendment Temps'

    def __str__(self):
        return f"Amended PO {self.po_id}"

# Main Models for the Current Module
class PurchaseOrderMaster(models.Model):
    """Represents tblMM_PO_Master for main Purchase Order records."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1, blank=True, null=True) # '0' for PR, '1' for SPR

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order Master'
        verbose_name_plural = 'Purchase Order Masters'

    def __str__(self):
        return self.po_no or f"PO Master {self.id}"

class PurchaseOrderDetail(models.Model):
    """
    Represents tblMM_PO_Details for Purchase Order line items.
    Includes properties to encapsulate complex data retrieval logic from C# code.
    """
    po_detail_id = models.IntegerField(db_column='Id', primary_key=True) # Renamed to avoid clash with Django's default 'id'
    po_no = models.CharField(db_column='PONo', max_length=255, blank=True, null=True) # String field as per C# use, can be FK to PurchaseOrderMaster if PONo is unique and linked.
    master_fk = models.ForeignKey(PurchaseOrderMaster, models.DO_NOTHING, db_column='MId', related_name='po_details', blank=True, null=True)
    pr_no = models.CharField(db_column='PRNo', max_length=255, blank=True, null=True)
    pr_detail_id = models.IntegerField(db_column='PRId', blank=True, null=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=255, blank=True, null=True)
    spr_detail_id = models.IntegerField(db_column='SPRId', blank=True, null=True)
    quantity = models.FloatField(db_column='Qty', blank=True, null=True)
    rate = models.FloatField(db_column='Rate', blank=True, null=True)
    discount = models.FloatField(db_column='Discount', blank=True, null=True)
    packing_forwarding_id = models.IntegerField(db_column='PF', blank=True, null=True)
    excise_service_tax_id = models.IntegerField(db_column='ExST', blank=True, null=True)
    vat_id = models.IntegerField(db_column='VAT', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"{self.po_no} - Item {self.item_code}"

    @property
    def item_info(self):
        """Helper to get PR or SPR item detail based on PRSPRFlag."""
        if self.master_fk:
            if self.master_fk.pr_spr_flag == '0' and self.pr_detail_id:
                try:
                    return PRDetail.objects.get(id=self.pr_detail_id).item
                except PRDetail.DoesNotExist:
                    pass
            elif self.master_fk.pr_spr_flag == '1' and self.spr_detail_id:
                try:
                    return SPRDetail.objects.get(id=self.spr_detail_id).item
                except SPRDetail.DoesNotExist:
                    pass
        return None

    @property
    def item_code(self):
        """Returns the item code (e.g., from tblDG_Item_Master)."""
        item = self.item_info
        return item.item_code if item else "N/A"

    @property
    def purchase_description(self):
        """Returns the item description (ManfDesc)."""
        item = self.item_info
        return item.manufacturer_description if item else "N/A"

    @property
    def uom_purchase(self):
        """Returns the UOM symbol for the item."""
        item = self.item_info
        if item:
            return item.uom_basic_symbol
        return "N/A"

    @property
    def work_order_no(self):
        """Returns the Work Order No, conditionally from PR or SPR."""
        if self.master_fk:
            if self.master_fk.pr_spr_flag == '0' and self.pr_no:
                try:
                    pr_master = PRMaster.objects.filter(pr_no=self.pr_no, company_id=self.master_fk.company_id).first()
                    return pr_master.work_order_no if pr_master else "NA"
                except PRMaster.DoesNotExist:
                    pass
            elif self.master_fk.pr_spr_flag == '1' and self.spr_detail_id:
                try:
                    spr_detail = SPRDetail.objects.get(id=self.spr_detail_id)
                    return spr_detail.work_order_no if spr_detail.work_order_no not in ["", "0", None] else "NA"
                except SPRDetail.DoesNotExist:
                    pass
        return "NA"

    @property
    def business_group_name(self):
        """Returns the Business Group Name, primarily from SPR."""
        if self.master_fk and self.master_fk.pr_spr_flag == '1' and self.spr_detail_id:
            try:
                spr_detail = SPRDetail.objects.get(id=self.spr_detail_id)
                if spr_detail.department_id and spr_detail.department_id != 0:
                    return spr_detail.department.symbol
            except SPRDetail.DoesNotExist:
                pass
        return "NA"

    @property
    def account_head_name(self):
        """Returns the Account Head Name, conditionally from PR or SPR."""
        if self.master_fk:
            if self.master_fk.pr_spr_flag == '0' and self.pr_detail_id:
                try:
                    pr_detail = PRDetail.objects.get(id=self.pr_detail_id)
                    return pr_detail.account_head.symbol
                except PRDetail.DoesNotExist:
                    pass
            elif self.master_fk.pr_spr_flag == '1' and self.spr_detail_id:
                try:
                    spr_detail = SPRDetail.objects.get(id=self.spr_detail_id)
                    return spr_detail.account_head.symbol
                except SPRDetail.DoesNotExist:
                    pass
        return "N/A"

    @property
    def packing_forwarding_terms(self):
        """Returns the Packing/Forwarding terms."""
        if self.packing_forwarding_id:
            try:
                return PackingMaster.objects.get(id=self.packing_forwarding_id).terms
            except PackingMaster.DoesNotExist:
                pass
        return "N/A"

    @property
    def excise_service_tax_terms(self):
        """Returns the Excise/Service Tax terms."""
        if self.excise_service_tax_id:
            try:
                return ExciseServiceMaster.objects.get(id=self.excise_service_tax_id).terms
            except ExciseServiceMaster.DoesNotExist:
                pass
        return "N/A"

    @property
    def vat_terms(self):
        """Returns the VAT terms."""
        if self.vat_id:
            try:
                return VATMaster.objects.get(id=self.vat_id).terms
            except VATMaster.DoesNotExist:
                pass
        return "N/A"

    @property
    def discount_formatted(self):
        """Returns discount formatted to N3."""
        return f"{self.discount:.3f}" if self.discount is not None else "0.000"

    @property
    def quantity_formatted(self):
        """Returns quantity formatted to N3."""
        return f"{self.quantity:.3f}" if self.quantity is not None else "0.000"

    @property
    def rate_formatted(self):
        """Returns rate formatted to N2."""
        return f"{self.rate:.2f}" if self.rate is not None else "0.00"

```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` is created for `PurchaseOrderDetail`. Since the original page is read-only, the fields for input are inferred from the display columns that would typically be editable (`Qty`, `Rate`, `Discount`, `PF`, `ExST`, `VAT`). The other fields (`PONo`, `ItemCode`, `Description`, `UOM`, `WONo`, `BG Group`, `A/c Head`) are derived or display-only, and thus are not included in the form for direct input.

```python
# purchase_management/forms.py
from django import forms
from .models import PurchaseOrderDetail, PackingMaster, ExciseServiceMaster, VATMaster

class PurchaseOrderDetailForm(forms.ModelForm):
    # For simplicity, assuming these are the editable fields.
    # In a real scenario, other fields (like ItemId, PRId, SPRId) might be editable
    # via dropdowns linked to ItemMaster, PRDetail, SPRDetail, etc.
    # We use IntegerFields for FK IDs as per C# logic.
    packing_forwarding_id = forms.ModelChoiceField(
        queryset=PackingMaster.objects.all(), 
        to_field_name='id', # The value submitted will be the 'id'
        empty_label="Select PF Term", 
        label="PF",
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    excise_service_tax_id = forms.ModelChoiceField(
        queryset=ExciseServiceMaster.objects.all(), 
        to_field_name='id', 
        empty_label="Select ExST Term", 
        label="Excise",
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    vat_id = forms.ModelChoiceField(
        queryset=VATMaster.objects.all(), 
        to_field_name='id', 
        empty_label="Select VAT Term", 
        label="VAT",
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = PurchaseOrderDetail
        fields = [
            'po_no', 'quantity', 'rate', 'discount', 
            'packing_forwarding_id', 'excise_service_tax_id', 'vat_id'
        ]
        # po_detail_id (PK) is excluded from form for creation/update
        # master_fk, pr_no, pr_detail_id, spr_no, spr_detail_id are complex,
        # typically set on backend or through separate forms/flows.
        # We assume these are populated based on context rather than direct user input here.
        widgets = {
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'rate': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'discount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            # Select fields (packing_forwarding_id, excise_service_tax_id, vat_id) are handled above
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial values for ModelChoiceFields if instance exists
        if self.instance and self.instance.pk:
            self.fields['packing_forwarding_id'].initial = self.instance.packing_forwarding_id
            self.fields['excise_service_tax_id'].initial = self.instance.excise_service_tax_id
            self.fields['vat_id'].initial = self.instance.vat_id

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
We'll create a `ListView` to display the details, and `CreateView`, `UpdateView`, `DeleteView` for full CRUD. A `TablePartialView` is added to support HTMX for dynamically loading the DataTables content. Views adhere to the "thin view" principle, with most logic residing in models.

```python
# purchase_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404, render
from .models import PurchaseOrderDetail, POAmendmentTemp, PurchaseOrderMaster
from .forms import PurchaseOrderDetailForm

class PurchaseOrderDetailListView(ListView):
    """Displays a list of Purchase Order Details."""
    model = PurchaseOrderDetail
    template_name = 'purchase_management/purchaseorderdetail/list.html'
    context_object_name = 'purchaseorderdetails'
    # No pagination handled by ListView, as DataTables will do client-side.
    # We need to filter based on query string parameters, similar to ASP.NET's Request.QueryString.
    # The ASP.NET page loads data for a specific PO and Master ID.
    
    def get_queryset(self):
        """
        Retrieves the queryset of Purchase Order Details,
        filtered by PONo and MId from URL parameters,
        and excluding amended POs.
        """
        # Get parameters from URL, similar to Request.QueryString
        po_no = self.request.GET.get('pono', '')
        master_id = self.request.GET.get('mid', '')
        
        # Filter for the specific PO and Master ID
        queryset = PurchaseOrderDetail.objects.all()
        if po_no:
            queryset = queryset.filter(po_no=po_no)
        if master_id:
            queryset = queryset.filter(master_fk__id=master_id)
        
        # Exclude POs that are in tblMM_PO_Amd_Temp
        # This assumes po_detail_id in PurchaseOrderDetail maps to POId in POAmendmentTemp
        excluded_ids = POAmendmentTemp.objects.values_list('po_id', flat=True)
        queryset = queryset.exclude(po_detail_id__in=list(excluded_ids))
        
        # Optimization: use select_related for PurchaseOrderMaster to reduce N+1 queries for pr_spr_flag
        queryset = queryset.select_related('master_fk')
        
        # Note: Further deep relations (ItemMaster, PRDetail, etc.) are handled by properties,
        # leading to N+1 queries for those if not explicitly prefetched. For a real-world
        # application, consider `prefetch_related` or custom annotations for performance.
        return queryset

class PurchaseOrderDetailTablePartialView(View):
    """
    Renders only the table content for HTMX requests.
    This view is designed to be fetched via HTMX to update the table dynamically.
    """
    def get(self, request, *args, **kwargs):
        list_view = PurchaseOrderDetailListView()
        list_view.setup(request, *args, **kwargs) # Important to set up request and other attributes
        queryset = list_view.get_queryset()
        context = {
            'purchaseorderdetails': queryset,
            # Pass original query parameters to action URLs in the table, if needed for redirects
            'pono': request.GET.get('pono', ''),
            'mid': request.GET.get('mid', ''),
            'supcode': request.GET.get('Code', ''), # Corresponding to SupCode in ASP.NET
        }
        return render(request, 'purchase_management/purchaseorderdetail/_purchaseorderdetail_table.html', context)


class PurchaseOrderDetailCreateView(CreateView):
    """Handles creation of new Purchase Order Details."""
    model = PurchaseOrderDetail
    form_class = PurchaseOrderDetailForm
    template_name = 'purchase_management/purchaseorderdetail/form.html'
    # success_url is handled by HX-Trigger header for HTMX requests
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass any necessary query parameters to the form, e.g., master_fk, po_no context
        context['pono'] = self.request.GET.get('pono', '')
        context['mid'] = self.request.GET.get('mid', '')
        context['supcode'] = self.request.GET.get('Code', '')
        return context

    def form_valid(self, form):
        # The original ASP.NET had `PONo` and `MId` from query string.
        # For creation, we assume these need to be set from context/query parameters.
        # This part requires specific business logic to correctly associate new details.
        # For demonstration, we'll try to get these from URL params if creating.
        # In a real app, this would likely be part of a larger PO creation flow.
        po_no = self.request.GET.get('pono', '')
        master_id = self.request.GET.get('mid', '')
        
        # Attempt to find or create a PurchaseOrderMaster if context is available
        if po_no and master_id:
            try:
                master_instance = PurchaseOrderMaster.objects.get(id=master_id, po_no=po_no)
                form.instance.master_fk = master_instance
            except PurchaseOrderMaster.DoesNotExist:
                # Handle case where master PO doesn't exist or doesn't match
                messages.error(self.request, "Parent Purchase Order not found for creation.")
                return HttpResponseRedirect(reverse_lazy('purchase_management:purchaseorderdetail_list'))
        
        # This will save the form instance and trigger form.instance.save()
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Detail added successfully.')
        
        # HTMX-specific response: return 204 No Content and a trigger header
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshPurchaseOrderDetailList, closeModal', # Trigger refresh and close modal
                    'HX-Redirect': reverse_lazy('purchase_management:purchaseorderdetail_list') + f'?pono={po_no}&mid={master_id}&Code={self.request.GET.get("Code", "")}'
                }
            )
        # Standard redirect for non-HTMX requests (unlikely for this flow)
        return response

    def form_invalid(self, form):
        # Render the form template with errors for HTMX to swap in
        response = render(self.request, self.template_name, self.get_context_data(form=form))
        response.status_code = 400 # Indicate bad request
        return response

class PurchaseOrderDetailUpdateView(UpdateView):
    """Handles updates to existing Purchase Order Details."""
    model = PurchaseOrderDetail
    form_class = PurchaseOrderDetailForm
    template_name = 'purchase_management/purchaseorderdetail/form.html'
    pk_url_kwarg = 'po_detail_id' # Match URL keyword argument to model's PK field name
    # success_url is handled by HX-Trigger header for HTMX requests

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass any necessary query parameters to the form, e.g., master_fk, po_no context
        context['pono'] = self.request.GET.get('pono', '')
        context['mid'] = self.request.GET.get('mid', '')
        context['supcode'] = self.request.GET.get('Code', '')
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Detail updated successfully.')
        po_no = self.request.GET.get('pono', '')
        master_id = self.request.GET.get('mid', '')
        sup_code = self.request.GET.get('Code', '')

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshPurchaseOrderDetailList, closeModal',
                    'HX-Redirect': reverse_lazy('purchase_management:purchaseorderdetail_list') + f'?pono={po_no}&mid={master_id}&Code={sup_code}'
                }
            )
        return response

    def form_invalid(self, form):
        response = render(self.request, self.template_name, self.get_context_data(form=form))
        response.status_code = 400
        return response

class PurchaseOrderDetailDeleteView(DeleteView):
    """Handles deletion of Purchase Order Details."""
    model = PurchaseOrderDetail
    template_name = 'purchase_management/purchaseorderdetail/confirm_delete.html'
    pk_url_kwarg = 'po_detail_id' # Match URL keyword argument to model's PK field name
    # success_url is handled by HX-Trigger header for HTMX requests

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Order Detail deleted successfully.')
        po_no = self.request.GET.get('pono', '')
        master_id = self.request.GET.get('mid', '')
        sup_code = self.request.GET.get('Code', '')

        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshPurchaseOrderDetailList, closeModal',
                    'HX-Redirect': reverse_lazy('purchase_management:purchaseorderdetail_list') + f'?pono={po_no}&mid={master_id}&Code={sup_code}'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass query parameters to preserve context for redirection after delete
        context['pono'] = self.request.GET.get('pono', '')
        context['mid'] = self.request.GET.get('mid', '')
        context['supcode'] = self.request.GET.get('Code', '')
        return context

```

#### 4.4 Templates

**Task:** Create templates for each view, using HTMX and DataTables.

**Instructions:**
-   `list.html` will contain the main page structure and trigger the HTMX load for the table.
-   `_purchaseorderdetail_table.html` will be a partial for the DataTables content.
-   `form.html` will be a partial used for both create and update operations, loaded into a modal.
-   `_confirm_delete.html` will be a partial for the delete confirmation, loaded into a modal.
-   All templates extend `core/base.html` (not included here).

```html
{# purchase_management/templates/purchase_management/purchaseorderdetail/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Purchase Order Details</h2>
        <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'purchase_management:purchaseorderdetail_add' %}?pono={{ request.GET.pono }}&mid={{ request.GET.mid }}&Code={{ request.GET.Code }}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Detail
        </button>
    </div>
    
    <div id="purchaseorderdetailTable-container"
         hx-trigger="load, refreshPurchaseOrderDetailList from:body"
         hx-get="{% url 'purchase_management:purchaseorderdetail_table' %}?pono={{ request.GET.pono }}&mid={{ request.GET.mid }}&Code={{ request.GET.Code }}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-700">Loading Purchase Order Details...</p>
        </div>
    </div>
    
    <!-- Modal for forms and confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ showModal: false }"
         x-show="showModal"
         x-init="$watch('showModal', value => { if (!value) document.getElementById('modalContent').innerHTML = ''; });"
         _="on click if event.target.id == 'modal' remove .is-active from me then set #modalContent.innerHTML to ''">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-3xl w-full mx-4 sm:mx-auto"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js setup for modal functionality if needed.
    // The current template uses htmx and Hyperscript for modal control.
    // If you need more complex Alpine.js state for the modal, uncomment and expand:
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true; },
            close() { this.isOpen = false; },
        });
    });

    // Custom event listener for closing modal.
    document.body.addEventListener('closeModal', () => {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active'); // Hyperscript uses is-active
            document.getElementById('modalContent').innerHTML = ''; // Clear content
        }
    });
</script>
{% endblock %}
```

```html
{# purchase_management/templates/purchase_management/purchaseorderdetail/_purchaseorderdetail_table.html #}
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="purchaseorderdetailTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Disc %</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">BG Group</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">A/c Head</th>
                <th scope="col" class="relative py-3 px-6">
                    <span class="sr-only">Actions</span>
                </th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in purchaseorderdetails %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.po_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.item_code }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.purchase_description }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.uom_purchase }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.quantity_formatted }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.rate_formatted }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900 text-right">{{ obj.discount_formatted }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.packing_forwarding_terms }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.excise_service_tax_terms }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.vat_terms }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.work_order_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.business_group_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-900">{{ obj.account_head_name }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button
                        class="text-indigo-600 hover:text-indigo-900 mr-2"
                        hx-get="{% url 'purchase_management:purchaseorderdetail_edit' po_detail_id=obj.po_detail_id %}?pono={{ pono }}&mid={{ mid }}&Code={{ supcode }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="text-red-600 hover:text-red-900"
                        hx-get="{% url 'purchase_management:purchaseorderdetail_delete' po_detail_id=obj.po_detail_id %}?pono={{ pono }}&mid={{ mid }}&Code={{ supcode }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="15" class="py-4 px-6 text-center text-sm text-gray-500">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent reinitialization errors
        if ($.fn.DataTable.isDataTable('#purchaseorderdetailTable')) {
            $('#purchaseorderdetailTable').DataTable().destroy();
        }
        $('#purchaseorderdetailTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true, // Make table responsive
            "searching": true, // Enable searching
            "ordering": true,  // Enable sorting
            "paging": true     // Enable pagination
        });
    });
</script>
```

```html
{# purchase_management/templates/purchase_management/purchaseorderdetail/form.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.po_detail_id|yesno:'Edit,Add' }} Purchase Order Detail</h3>
    <form hx-post="{{ request.path }}?pono={{ request.GET.pono }}&mid={{ request.GET.mid }}&Code={{ request.GET.Code }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click send closeModal to body">
                Cancel
            </button>
            <button
                type="submit"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

```html
{# purchase_management/templates/purchase_management/purchaseorderdetail/confirm_delete.html #}
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Purchase Order Detail for <strong>PO No: {{{{ object.po_no }}}} - Item: {{{{ object.item_code }}}}</strong>?</p>
    
    <form hx-post="{{ request.path }}?pono={{ request.GET.pono }}&mid={{ request.GET.mid }}&Code={{ request.GET.Code }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click send closeModal to body">
                Cancel
            </button>
            <button
                type="submit"
                class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the list view, the partial table view, and all CRUD operations.

```python
# purchase_management/urls.py
from django.urls import path
from .views import (
    PurchaseOrderDetailListView,
    PurchaseOrderDetailCreateView,
    PurchaseOrderDetailUpdateView,
    PurchaseOrderDetailDeleteView,
    PurchaseOrderDetailTablePartialView
)

app_name = 'purchase_management' # Namespace for URLs

urlpatterns = [
    # List view for Purchase Order Details
    path('purchaseorderdetails/', PurchaseOrderDetailListView.as_view(), name='purchaseorderdetail_list'),
    
    # HTMX endpoint for the table partial
    path('purchaseorderdetails/table/', PurchaseOrderDetailTablePartialView.as_view(), name='purchaseorderdetail_table'),
    
    # CRUD operations (loaded into modal via HTMX)
    path('purchaseorderdetails/add/', PurchaseOrderDetailCreateView.as_view(), name='purchaseorderdetail_add'),
    path('purchaseorderdetails/edit/<int:po_detail_id>/', PurchaseOrderDetailUpdateView.as_view(), name='purchaseorderdetail_edit'),
    path('purchaseorderdetails/delete/<int:po_detail_id>/', PurchaseOrderDetailDeleteView.as_view(), name='purchaseorderdetail_delete'),
]

```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods/properties and integration tests for all view interactions are crucial. We aim for at least 80% test coverage.

```python
# purchase_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib import messages
from .models import (
    PurchaseOrderMaster, PurchaseOrderDetail, ItemMaster, UnitMaster,
    PackingMaster, ExciseServiceMaster, VATMaster, AccountHead, BusinessGroup,
    PRMaster, PRDetail, SPRMaster, SPRDetail, POAmendmentTemp
)

class PurchaseOrderDetailModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = 1
        
        # Setup helper models
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_master_1 = ItemMaster.objects.create(id=101, item_code='ITEM001', manufacturer_description='Widget A', uom_basic_id=cls.unit_ea.id)
        cls.item_master_2 = ItemMaster.objects.create(id=102, item_code='ITEM002', manufacturer_description='Gadget B', uom_basic_id=cls.unit_ea.id)
        cls.packing_f = PackingMaster.objects.create(id=1, terms='FOB')
        cls.excise_s = ExciseServiceMaster.objects.create(id=1, terms='GST 18%')
        cls.vat_t = VATMaster.objects.create(id=1, terms='VAT 5%')
        cls.acc_head_raw = AccountHead.objects.create(id=1, symbol='RAW MATERIALS')
        cls.acc_head_exp = AccountHead.objects.create(id=2, symbol='EXPENSES')
        cls.biz_group_prod = BusinessGroup.objects.create(id=1, symbol='PRODUCTION')
        
        # Setup PR/SPR related data
        cls.pr_master = PRMaster.objects.create(id=1, pr_no='PR001', work_order_no='WO123', company_id=cls.company_id)
        cls.pr_detail = PRDetail.objects.create(id=1, pr_no='PR001', master_id=cls.pr_master.id, item_id=cls.item_master_1.id, account_head_id=cls.acc_head_raw.id)
        
        cls.spr_master = SPRMaster.objects.create(id=1, spr_no='SPR001', company_id=cls.company_id)
        cls.spr_detail = SPRDetail.objects.create(id=1, spr_no='SPR001', master_id=cls.spr_master.id, item_id=cls.item_master_2.id, work_order_no='NA', department_id=cls.biz_group_prod.id, account_head_id=cls.acc_head_exp.id)

        # Setup main PO Master
        cls.po_master_pr_flag = PurchaseOrderMaster.objects.create(id=1, po_no='PO001', company_id=cls.company_id, pr_spr_flag='0')
        cls.po_master_spr_flag = PurchaseOrderMaster.objects.create(id=2, po_no='PO002', company_id=cls.company_id, pr_spr_flag='1')

        # Setup Purchase Order Details
        cls.po_detail_1 = PurchaseOrderDetail.objects.create(
            po_detail_id=1,
            po_no='PO001',
            master_fk=cls.po_master_pr_flag,
            pr_no='PR001',
            pr_detail_id=cls.pr_detail.id,
            spr_no=None,
            spr_detail_id=None,
            quantity=100.0,
            rate=10.50,
            discount=5.0,
            packing_forwarding_id=cls.packing_f.id,
            excise_service_tax_id=cls.excise_s.id,
            vat_id=cls.vat_t.id
        )
        cls.po_detail_2 = PurchaseOrderDetail.objects.create(
            po_detail_id=2,
            po_no='PO002',
            master_fk=cls.po_master_spr_flag,
            pr_no=None,
            pr_detail_id=None,
            spr_no='SPR001',
            spr_detail_id=cls.spr_detail.id,
            quantity=50.0,
            rate=20.00,
            discount=0.0,
            packing_forwarding_id=cls.packing_f.id,
            excise_service_tax_id=cls.excise_s.id,
            vat_id=cls.vat_t.id
        )
        
        # Create an amended PO detail to test exclusion
        cls.po_amended = PurchaseOrderDetail.objects.create(
            po_detail_id=3,
            po_no='PO003',
            master_fk=cls.po_master_pr_flag, # Re-using master for simplicity
            quantity=10.0, rate=1.0, discount=0.0,
            packing_forwarding_id=None, excise_service_tax_id=None, vat_id=None # Minimal data
        )
        POAmendmentTemp.objects.create(po_id=cls.po_amended.po_detail_id)

    def test_purchaseorderdetail_creation(self):
        obj = PurchaseOrderDetail.objects.get(po_detail_id=1)
        self.assertEqual(obj.po_no, 'PO001')
        self.assertEqual(obj.quantity, 100.0)
        self.assertEqual(obj.rate, 10.50)
        self.assertEqual(obj.discount, 5.0)

    def test_item_code_property_pr_flag(self):
        self.assertEqual(self.po_detail_1.item_code, 'ITEM001')

    def test_item_code_property_spr_flag(self):
        self.assertEqual(self.po_detail_2.item_code, 'ITEM002')

    def test_purchase_description_property_pr_flag(self):
        self.assertEqual(self.po_detail_1.purchase_description, 'Widget A')

    def test_uom_purchase_property_spr_flag(self):
        self.assertEqual(self.po_detail_2.uom_purchase, 'EA')
        
    def test_work_order_no_property_pr_flag(self):
        self.assertEqual(self.po_detail_1.work_order_no, 'WO123')

    def test_work_order_no_property_spr_flag_na(self):
        self.assertEqual(self.po_detail_2.work_order_no, 'NA') # WONo in SPRDetail for this test was 'NA'

    def test_business_group_name_property_spr_flag(self):
        self.assertEqual(self.po_detail_2.business_group_name, 'PRODUCTION')

    def test_account_head_name_property_pr_flag(self):
        self.assertEqual(self.po_detail_1.account_head_name, 'RAW MATERIALS')

    def test_account_head_name_property_spr_flag(self):
        self.assertEqual(self.po_detail_2.account_head_name, 'EXPENSES')

    def test_packing_forwarding_terms_property(self):
        self.assertEqual(self.po_detail_1.packing_forwarding_terms, 'FOB')

    def test_excise_service_tax_terms_property(self):
        self.assertEqual(self.po_detail_1.excise_service_tax_terms, 'GST 18%')

    def test_vat_terms_property(self):
        self.assertEqual(self.po_detail_1.vat_terms, 'VAT 5%')
        
    def test_quantity_formatted_property(self):
        self.assertEqual(self.po_detail_1.quantity_formatted, '100.000')

    def test_rate_formatted_property(self):
        self.assertEqual(self.po_detail_1.rate_formatted, '10.50')

    def test_discount_formatted_property(self):
        self.assertEqual(self.po_detail_1.discount_formatted, '5.000')

class PurchaseOrderDetailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Same setup as model tests
        cls.company_id = 1
        cls.unit_ea = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_master_1 = ItemMaster.objects.create(id=101, item_code='ITEM001', manufacturer_description='Widget A', uom_basic_id=cls.unit_ea.id)
        cls.item_master_2 = ItemMaster.objects.create(id=102, item_code='ITEM002', manufacturer_description='Gadget B', uom_basic_id=cls.unit_ea.id)
        cls.packing_f = PackingMaster.objects.create(id=1, terms='FOB')
        cls.excise_s = ExciseServiceMaster.objects.create(id=1, terms='GST 18%')
        cls.vat_t = VATMaster.objects.create(id=1, terms='VAT 5%')
        cls.acc_head_raw = AccountHead.objects.create(id=1, symbol='RAW MATERIALS')
        cls.acc_head_exp = AccountHead.objects.create(id=2, symbol='EXPENSES')
        cls.biz_group_prod = BusinessGroup.objects.create(id=1, symbol='PRODUCTION')
        
        cls.pr_master = PRMaster.objects.create(id=1, pr_no='PR001', work_order_no='WO123', company_id=cls.company_id)
        cls.pr_detail = PRDetail.objects.create(id=1, pr_no='PR001', master_id=cls.pr_master.id, item_id=cls.item_master_1.id, account_head_id=cls.acc_head_raw.id)
        
        cls.spr_master = SPRMaster.objects.create(id=1, spr_no='SPR001', company_id=cls.company_id)
        cls.spr_detail = SPRDetail.objects.create(id=1, spr_no='SPR001', master_id=cls.spr_master.id, item_id=cls.item_master_2.id, work_order_no='NA', department_id=cls.biz_group_prod.id, account_head_id=cls.acc_head_exp.id)

        cls.po_master_pr_flag = PurchaseOrderMaster.objects.create(id=10, po_no='PO_TEST_01', company_id=cls.company_id, pr_spr_flag='0')
        cls.po_master_spr_flag = PurchaseOrderMaster.objects.create(id=20, po_no='PO_TEST_02', company_id=cls.company_id, pr_spr_flag='1')

        cls.po_detail_active = PurchaseOrderDetail.objects.create(
            po_detail_id=100,
            po_no='PO_TEST_01',
            master_fk=cls.po_master_pr_flag,
            pr_no='PR001',
            pr_detail_id=cls.pr_detail.id,
            quantity=100.0, rate=10.50, discount=5.0,
            packing_forwarding_id=cls.packing_f.id,
            excise_service_tax_id=cls.excise_s.id,
            vat_id=cls.vat_t.id
        )
        cls.po_detail_active_2 = PurchaseOrderDetail.objects.create(
            po_detail_id=101,
            po_no='PO_TEST_02',
            master_fk=cls.po_master_spr_flag,
            spr_no='SPR001',
            spr_detail_id=cls.spr_detail.id,
            quantity=50.0, rate=20.00, discount=0.0,
            packing_forwarding_id=cls.packing_f.id,
            excise_service_tax_id=cls.excise_s.id,
            vat_id=cls.vat_t.id
        )
        
        cls.po_detail_amended = PurchaseOrderDetail.objects.create(
            po_detail_id=102,
            po_no='PO_TEST_03',
            master_fk=cls.po_master_pr_flag,
            quantity=10.0, rate=1.0, discount=0.0,
            packing_forwarding_id=None, excise_service_tax_id=None, vat_id=None
        )
        POAmendmentTemp.objects.create(po_id=cls.po_detail_amended.po_detail_id) # Mark as amended

    def setUp(self):
        self.client = Client()
        self.list_url = reverse('purchase_management:purchaseorderdetail_list') + f'?pono={self.po_master_pr_flag.po_no}&mid={self.po_master_pr_flag.id}&Code=SUP001'
        self.table_url = reverse('purchase_management:purchaseorderdetail_table') + f'?pono={self.po_master_pr_flag.po_no}&mid={self.po_master_pr_flag.id}&Code=SUP001'
        self.add_url = reverse('purchase_management:purchaseorderdetail_add') + f'?pono={self.po_master_pr_flag.po_no}&mid={self.po_master_pr_flag.id}&Code=SUP001'
        self.edit_url = reverse('purchase_management:purchaseorderdetail_edit', args=[self.po_detail_active.po_detail_id]) + f'?pono={self.po_master_pr_flag.po_no}&mid={self.po_master_pr_flag.id}&Code=SUP001'
        self.delete_url = reverse('purchase_management:purchaseorderdetail_delete', args=[self.po_detail_active.po_detail_id]) + f'?pono={self.po_master_pr_flag.po_no}&mid={self.po_master_pr_flag.id}&Code=SUP001'

    def test_list_view_get(self):
        response = self.client.get(self.list_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_management/purchaseorderdetail/list.html')
        self.assertIn('purchaseorderdetails', response.context)
        # Check that the amended PO detail is not in the list
        self.assertNotIn(self.po_detail_amended, response.context['purchaseorderdetails'])

    def test_table_partial_view_get_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(self.table_url, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_management/purchaseorderdetail/_purchaseorderdetail_table.html')
        self.assertIn('purchaseorderdetails', response.context)
        # Check that the amended PO detail is not in the list
        self.assertNotIn(self.po_detail_amended, response.context['purchaseorderdetails'])
        self.assertContains(response, 'PO_TEST_01') # Check for active PO
        self.assertNotContains(response, 'PO_TEST_03') # Check for amended PO

    def test_create_view_get(self):
        response = self.client.get(self.add_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_management/purchaseorderdetail/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_htmx_success(self):
        data = {
            'po_no': 'PO_NEW_01',
            'quantity': 200.0,
            'rate': 25.00,
            'discount': 2.5,
            'packing_forwarding_id': self.packing_f.id,
            'excise_service_tax_id': self.excise_s.id,
            'vat_id': self.vat_t.id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = PurchaseOrderDetail.objects.count()
        response = self.client.post(self.add_url, data, **headers)
        
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertTrue(PurchaseOrderDetail.objects.filter(po_no='PO_NEW_01').exists())
        self.assertEqual(PurchaseOrderDetail.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderDetailList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_create_view_post_htmx_invalid(self):
        data = {
            'po_no': '', # Invalid data
            'quantity': -10,
            'rate': -5,
            'discount': 0
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.add_url, data, **headers)
        
        self.assertEqual(response.status_code, 400) # Bad Request for invalid form
        self.assertTemplateUsed(response, 'purchase_management/purchaseorderdetail/form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)

    def test_update_view_get(self):
        response = self.client.get(self.edit_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_management/purchaseorderdetail/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.po_detail_active)

    def test_update_view_post_htmx_success(self):
        data = {
            'po_no': 'PO_TEST_01_UPDATED',
            'quantity': 150.0,
            'rate': 12.00,
            'discount': 6.0,
            'packing_forwarding_id': self.packing_f.id,
            'excise_service_tax_id': self.excise_s.id,
            'vat_id': self.vat_t.id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(self.edit_url, data, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.po_detail_active.refresh_from_db()
        self.assertEqual(self.po_detail_active.po_no, 'PO_TEST_01_UPDATED')
        self.assertEqual(self.po_detail_active.quantity, 150.0)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderDetailList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

    def test_delete_view_get(self):
        response = self.client.get(self.delete_url)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchase_management/purchaseorderdetail/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.po_detail_active)

    def test_delete_view_post_htmx_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_count = PurchaseOrderDetail.objects.count()
        response = self.client.post(self.delete_url, **headers)
        
        self.assertEqual(response.status_code, 204)
        self.assertEqual(PurchaseOrderDetail.objects.count(), initial_count - 1)
        self.assertFalse(PurchaseOrderDetail.objects.filter(po_detail_id=self.po_detail_active.po_detail_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPurchaseOrderDetailList', response.headers['HX-Trigger'])
        self.assertIn('closeModal', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
-   **HTMX for Dynamic Content:** All table content (loading, filtering, sorting) is handled by HTMX. The `_purchaseorderdetail_table.html` partial is fetched dynamically into `list.html`. CRUD forms (add, edit, delete) are loaded into a modal using HTMX `hx-get` on button clicks.
-   **HTMX for Form Submission:** Form submissions (`hx-post`) on the modal forms (`_purchaseorderdetail_form.html`, `_confirm_delete.html`) are configured with `hx-swap="none"`. Upon successful submission, the Django view sends back an `HX-Trigger` header (`refreshPurchaseOrderDetailList, closeModal`), which instructs the client-side HTMX to trigger a reload of the main table and close the modal.
-   **DataTables:** The `_purchaseorderdetail_table.html` partial includes the JavaScript to initialize DataTables on the rendered table. This provides out-of-the-box pagination, searching, and sorting capabilities.
-   **Alpine.js for UI State:** Alpine.js is used subtly in `list.html` to manage the visibility of the modal, ensuring it hides and clears its content after interactions. The `_` (Hyperscript) syntax is leveraged for declarative UI behavior related to modal opening/closing.
-   **No Custom JavaScript:** Beyond the DataTables initialization and minimal Alpine.js for modal state, no additional imperative JavaScript is required, adhering to the "no additional JavaScript" guideline.

## Final Notes

This modernization plan provides a robust and scalable Django solution for the `PO_Edit_Details_PO_Grid` functionality.

-   **Placeholder Replacement:** Remember to replace actual database credentials, table names, and specific field values with your environment's details.
-   **DRY Templates:** The use of partial templates (`_purchaseorderdetail_table.html`, `_purchaseorderdetail_form.html`, `_confirm_delete.html`) promotes reusability and maintainability.
-   **Fat Models:** Business logic, especially the complex lookups for item details, UOM, WO number, business group, and account head, is encapsulated within the `PurchaseOrderDetail` model's properties. This keeps views concise and focuses on data flow.
-   **Testing:** The provided test suite ensures comprehensive coverage of model properties and view functionality, which is critical for maintaining code quality during and after migration.
-   **Performance Optimization:** While the fat model properties mimic the original C# lookup logic, which might involve multiple database calls per row, for high-performance scenarios, consider optimizing `PurchaseOrderDetailListView.get_queryset` with `select_related` for direct foreign keys and `prefetch_related` or custom SQL for more complex, indirect relationships to avoid N+1 query problems.

This structured approach, driven by AI-assisted analysis and automation principles, will significantly reduce the manual effort typically associated with such migrations, providing a clear roadmap for your transition to a modern Django application.