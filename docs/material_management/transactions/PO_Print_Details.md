## ASP.NET to Django Conversion Script: PO_Print_Details Modernization

This plan outlines the automated conversion of the legacy ASP.NET `PO_Print_Details.aspx` application to a modern Django-based solution. The original ASP.NET page primarily acts as a container to display a report via an `iframe` and provides navigation. In Django, we will replace the `iframe` with direct rendering of the purchase order details and integrate it into a comprehensive Purchase Order management module following modern web development best practices.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code (`PO_Print_Details.aspx` and its code-behind) does not directly interact with the database; it primarily acts as a wrapper for `PO_PR_Print_Page.aspx` which is responsible for data fetching. However, the query string parameters used (`pono`, `Code`, `mid`, `AmdNo`, `Key`) strongly suggest fields related to a Purchase Order. We will infer a database table and its columns based on these parameters and common Purchase Order data points.

**Inferred Database Table:** `tbl_PurchaseOrder`
**Inferred Columns:**
- `id` (INT, Primary Key, auto-incremented by the database)
- `po_number` (VARCHAR, maps to `pono`)
- `supplier_code` (VARCHAR, maps to `Code`)
- `material_id` (INT, maps to `mid`, potentially a foreign key or a part of a composite key if `id` is not used)
- `amendment_no` (INT, maps to `AmdNo`)
- `key_field` (VARCHAR, maps to `Key`)
- `order_date` (DATETIME, inferred for completeness)
- `total_amount` (DECIMAL, inferred for completeness)
- `status` (VARCHAR, inferred for completeness)
- `details` (TEXT, inferred for completeness, actual PO content)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
The `PO_Print_Details.aspx` page itself primarily performs a **Read** operation by displaying content from another page (`PO_PR_Print_Page.aspx`) within an `iframe`. It does not involve Create, Update, or Delete operations on the Purchase Order data directly. The "Cancel" button provides **Navigation** back to a PO list page.

-   **Read:** The page fetches and displays details of a specific Purchase Order based on query string parameters. In Django, this will translate to a `DetailView` for the `PurchaseOrder` model.
-   **Create:** Not present on this page.
-   **Update:** Not present on this page.
-   **Delete:** Not present on this page.
-   **Navigation:** `Response.Redirect` from `Cancel_Click` to `PO_Print.aspx` indicates returning to a Purchase Order list or search view. This will be a standard Django `reverse_lazy` to a `ListView`.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
-   **`iframe` (`id="myiframe"`):** This is the core display component, showing the actual report/details. In Django, this will be replaced by directly rendering the `PurchaseOrder` object's data within the template.
-   **`asp:Button` (`ID="Cancel"`):** Triggers a navigation back to the `PO_Print.aspx` page. In Django, this will be a simple link or HTMX-enabled button navigating to the `PurchaseOrderListView`.
-   **Static Text/Layout:** HTML table structure for headers (`PO - Print`). This will be translated to a modern Tailwind CSS layout.

### Step 4: Generate Django Code

We will create a Django application named `material_management` to house the `PurchaseOrder` module.

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `PurchaseOrder` model will be mapped to the existing `tbl_PurchaseOrder` table. We'll include the inferred fields, ensuring `managed = False` for existing databases and mapping `db_column` where necessary.

```python
# material_management/models.py
from django.db import models

class PurchaseOrder(models.Model):
    # Assuming 'id' is the primary key in the existing tbl_PurchaseOrder
    # If not, adjust primary_key=True to the correct field, e.g., po_number
    id = models.IntegerField(db_column='id', primary_key=True)
    po_number = models.CharField(db_column='po_number', max_length=50, unique=True, verbose_name="PO Number")
    supplier_code = models.CharField(db_column='supplier_code', max_length=20, verbose_name="Supplier Code")
    material_id = models.IntegerField(db_column='material_id', verbose_name="Material ID")
    amendment_no = models.IntegerField(db_column='amendment_no', default=0, verbose_name="Amendment No")
    key_field = models.CharField(db_column='key_field', max_length=100, blank=True, null=True, verbose_name="Key Field")
    order_date = models.DateField(db_column='order_date', blank=True, null=True, verbose_name="Order Date")
    total_amount = models.DecimalField(db_column='total_amount', max_digits=18, decimal_places=2, default=0.00, verbose_name="Total Amount")
    status = models.CharField(db_column='status', max_length=50, default='Pending', verbose_name="Status")
    details = models.TextField(db_column='details', blank=True, null=True, verbose_name="PO Details")

    class Meta:
        managed = False  # Important: Django won't manage this table's creation/deletion
        db_table = 'tbl_PurchaseOrder'  # Name of the existing database table
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO No: {self.po_number} - Supplier: {self.supplier_code}"

    # Example of a 'fat model' method for business logic
    def calculate_tax(self, tax_rate=0.05):
        """Calculates tax based on the total amount."""
        return self.total_amount * tax_rate

    def get_full_status(self):
        """Returns a more descriptive status."""
        return f"Current Status: {self.status}"

```

#### 4.2 Forms

**Task:** Define a Django form for user input (for CRUD operations, even if this specific page is read-only).

**Instructions:**
A `ModelForm` for `PurchaseOrder` will be created. We'll include widgets with Tailwind CSS classes for consistent styling.

```python
# material_management/forms.py
from django import forms
from .models import PurchaseOrder

class PurchaseOrderForm(forms.ModelForm):
    class Meta:
        model = PurchaseOrder
        fields = ['po_number', 'supplier_code', 'material_id', 'amendment_no', 'key_field', 'order_date', 'total_amount', 'status', 'details']
        widgets = {
            'po_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amendment_no': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'key_field': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'order_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'details': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
    
    # Example custom validation
    def clean_po_number(self):
        po_number = self.cleaned_data['po_number']
        # Add custom validation logic, e.g., unique PO number check if not already handled by unique=True
        # if PurchaseOrder.objects.filter(po_number=po_number).exists() and not self.instance.pk:
        #     raise forms.ValidationError("This PO Number already exists.")
        return po_number

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs and a specific `DetailView` for the `PO_Print_Details` page.

**Instructions:**
We'll define `ListView`, `CreateView`, `UpdateView`, `DeleteView` for the full Purchase Order module, along with a `PurchaseOrderTablePartialView` for HTMX updates. Crucially, `PurchaseOrderPrintDetailView` will directly translate the `PO_Print_Details.aspx` functionality by fetching and displaying a single `PurchaseOrder` record.

```python
# material_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import PurchaseOrder
from .forms import PurchaseOrderForm

class PurchaseOrderListView(ListView):
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/list.html'
    context_object_name = 'purchaseorders'

class PurchaseOrderTablePartialView(ListView): # For HTMX partial update of DataTable
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/_purchaseorder_table.html'
    context_object_name = 'purchaseorders'

    def get_queryset(self):
        # In a real scenario, you might filter or order based on request parameters
        return PurchaseOrder.objects.all()

class PurchaseOrderCreateView(CreateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'material_management/purchaseorder/form.html'
    success_url = reverse_lazy('purchaseorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

class PurchaseOrderUpdateView(UpdateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'material_management/purchaseorder/form.html'
    success_url = reverse_lazy('purchaseorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

class PurchaseOrderDeleteView(DeleteView):
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/confirm_delete.html'
    success_url = reverse_lazy('purchaseorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

# This view directly translates the PO_Print_Details.aspx functionality
class PurchaseOrderPrintDetailView(DetailView):
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/print_detail.html'
    context_object_name = 'purchase_order'
    # Assuming the URL will pass the primary key (id) for lookup
    # If using po_number or a combination of parameters, get_object method needs to be overridden.
    # For example, to lookup by 'po_number' instead of 'id':
    # slug_field = 'po_number'
    # slug_url_kwarg = 'pono' # The URL parameter name for po_number

    def get_object(self, queryset=None):
        """
        Retrieves the PurchaseOrder object.
        For the original ASP.NET's behavior of using multiple query parameters to identify
        the PO for the iframe, we'll assume `pk` maps to the `id` of the PurchaseOrder.
        If a lookup based on `pono`, `Code`, `mid`, `AmdNo`, `Key` was needed
        to uniquely identify the object on THIS page (not the iframe content),
        this method would be more complex.
        """
        try:
            # Assuming the URL will pass 'pk' (which maps to PurchaseOrder.id)
            return super().get_object(queryset)
        except PurchaseOrder.DoesNotExist:
            raise Http404("Purchase Order not found.")

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # You can add context specific to printing if needed, e.g., company logo path
        # For example, if you wanted to replicate the query parameters from the original iframe src:
        # context['original_params'] = {
        #     'pono': self.request.GET.get('pono'),
        #     'Code': self.request.GET.get('Code'),
        #     'mid': self.request.GET.get('mid'),
        #     'AmdNo': self.request.GET.get('AmdNo'),
        #     'Key': self.request.GET.get('Key'),
        # }
        return context

```

#### 4.4 Templates

**Task:** Create templates for each view, including the specific print detail view.

**Instructions:**
-   **`material_management/purchaseorder/list.html`:** Main list page for Purchase Orders with DataTables.
-   **`material_management/purchaseorder/_purchaseorder_table.html`:** Partial template for DataTables, loaded via HTMX.
-   **`material_management/purchaseorder/form.html`:** Partial template for Create/Update forms.
-   **`material_management/purchaseorder/confirm_delete.html`:** Partial template for delete confirmation.
-   **`material_management/purchaseorder/print_detail.html`:** The direct equivalent of `PO_Print_Details.aspx`, displaying the PO details.

**List Template (`material_management/purchaseorder/list.html`):**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Purchase Orders</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'purchaseorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Purchase Order
        </button>
    </div>
    
    <div id="purchaseorderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'purchaseorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Purchase Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed
        // For example, an Alpine component to manage modal state
        Alpine.store('modal', {
            open: false,
            openModal() { this.open = true },
            closeModal() { this.open = false }
        });
    });

    // Listen for HTMX events to manage modal state
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
        }
    });

    document.body.addEventListener('htmx:beforeSwap', function(event) {
        // Close modal after form submission if not an error response
        if (event.detail.xhr.status === 204) {
            document.getElementById('modal').classList.remove('is-active');
        }
    });
</script>
{% endblock %}
```

**Table Partial Template (`material_management/purchaseorder/_purchaseorder_table.html`):**
```html
<table id="purchaseorderTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in purchaseorders %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.po_number }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.order_date|default_if_none:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">${{ obj.total_amount|floatformat:2 }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.status }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <a 
                    href="{% url 'purchaseorder_print_detail' obj.pk %}"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2">
                    Print
                </a>
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'purchaseorder_edit' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'purchaseorder_delete' obj.pk %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 text-center text-gray-500">No Purchase Orders found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#purchaseorderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**Form Partial Template (`material_management/purchaseorder/form.html`):**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Purchase Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**Delete Confirmation Partial Template (`material_management/purchaseorder/confirm_delete.html`):**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700 mb-5">Are you sure you want to delete Purchase Order <strong>"{{ object.po_number }}"</strong>?</p>
    
    <form hx-post="{% url 'purchaseorder_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**Print Detail Template (`material_management/purchaseorder/print_detail.html`):**
This template directly replaces the `iframe` content of the original ASP.NET page.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6 border-b pb-4">
        <h2 class="text-2xl font-bold text-gray-800">PO - Print Details</h2>
        <a href="{% url 'purchaseorder_list' %}" 
           class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Cancel
        </a>
    </div>

    {% if purchase_order %}
    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-700">Purchase Order: {{ purchase_order.po_number }}</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-gray-700">
            <div>
                <p><strong>Supplier Code:</strong> {{ purchase_order.supplier_code }}</p>
                <p><strong>Order Date:</strong> {{ purchase_order.order_date|date:"F d, Y" }}</p>
                <p><strong>Total Amount:</strong> ${{ purchase_order.total_amount|floatformat:2 }}</p>
            </div>
            <div>
                <p><strong>Material ID:</strong> {{ purchase_order.material_id }}</p>
                <p><strong>Amendment No:</strong> {{ purchase_order.amendment_no }}</p>
                <p><strong>Status:</strong> {{ purchase_order.status }}</p>
            </div>
        </div>
        
        <div class="mt-6">
            <h4 class="text-lg font-medium mb-2 text-gray-700">Details:</h4>
            <div class="bg-gray-50 p-4 rounded-md border border-gray-200">
                <p class="whitespace-pre-wrap">{{ purchase_order.details|default:"No additional details." }}</p>
            </div>
        </div>

        <div class="mt-8 text-right">
            <button class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                    hx-post="{% url 'generate_po_pdf' purchase_order.pk %}"
                    hx-trigger="click"
                    hx-swap="none"
                    _="on htmx:afterOnLoad open new window from event.detail.xhr.responseURL">
                Generate PDF for Print
            </button>
        </div>
    </div>
    {% else %}
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline">Purchase Order not found.</span>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for print-specific interactions
    });
</script>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
Create paths for the list, create, update, delete, the DataTables partial view, and the specific `PurchaseOrderPrintDetailView`. Also, include a placeholder URL for PDF generation.

```python
# material_management/urls.py
from django.urls import path
from .views import (
    PurchaseOrderListView, PurchaseOrderTablePartialView,
    PurchaseOrderCreateView, PurchaseOrderUpdateView, PurchaseOrderDeleteView,
    PurchaseOrderPrintDetailView
)
from . import pdf_views # Assuming a module for PDF generation

urlpatterns = [
    # CRUD operations for Purchase Orders
    path('purchaseorder/', PurchaseOrderListView.as_view(), name='purchaseorder_list'),
    path('purchaseorder/table/', PurchaseOrderTablePartialView.as_view(), name='purchaseorder_table'),
    path('purchaseorder/add/', PurchaseOrderCreateView.as_view(), name='purchaseorder_add'),
    path('purchaseorder/edit/<int:pk>/', PurchaseOrderUpdateView.as_view(), name='purchaseorder_edit'),
    path('purchaseorder/delete/<int:pk>/', PurchaseOrderDeleteView.as_view(), name='purchaseorder_delete'),

    # Specific view for "PO - Print Details" from ASP.NET
    # Maps to the original PO_Print_Details.aspx behavior, displaying details
    path('purchaseorder/print/<int:pk>/', PurchaseOrderPrintDetailView.as_view(), name='purchaseorder_print_detail'),

    # Endpoint for PDF generation (triggered by HTMX from print_detail.html)
    path('purchaseorder/pdf/<int:pk>/', pdf_views.generate_po_pdf, name='generate_po_pdf'),
]

```
**Note:** For the PDF generation, you'd need a simple function-based view in `material_management/pdf_views.py` (or integrated into `views.py` if simple) that uses a library like `ReportLab` or `WeasyPrint` to generate and serve a PDF file.

```python
# material_management/pdf_views.py (Example)
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import PurchaseOrder
# Assuming you have ReportLab installed: pip install reportlab
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors

def generate_po_pdf(request, pk):
    purchase_order = get_object_or_404(PurchaseOrder, pk=pk)

    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="PurchaseOrder_{purchase_order.po_number}.pdf"'

    p = canvas.Canvas(response, pagesize=letter)
    width, height = letter

    # Add header
    p.setFont("Helvetica-Bold", 16)
    p.drawString(50, height - 50, f"Purchase Order Details - PO#: {purchase_order.po_number}")

    # Add details
    p.setFont("Helvetica", 12)
    y_position = height - 100
    p.drawString(50, y_position, f"Supplier Code: {purchase_order.supplier_code}")
    y_position -= 20
    p.drawString(50, y_position, f"Order Date: {purchase_order.order_date.strftime('%Y-%m-%d') if purchase_order.order_date else 'N/A'}")
    y_position -= 20
    p.drawString(50, y_position, f"Total Amount: ${purchase_order.total_amount:.2f}")
    y_position -= 20
    p.drawString(50, y_position, f"Status: {purchase_order.status}")
    y_position -= 20
    p.drawString(50, y_position, f"Material ID: {purchase_order.material_id}")
    y_position -= 20
    p.drawString(50, y_position, f"Amendment No: {purchase_order.amendment_no}")
    y_position -= 20
    p.drawString(50, y_position, f"Key Field: {purchase_order.key_field}")

    # Add PO Details
    y_position -= 30
    p.setFont("Helvetica-Bold", 12)
    p.drawString(50, y_position, "Details:")
    p.setFont("Helvetica", 10)
    y_position -= 15
    for line in purchase_order.details.split('\n'):
        if y_position < 50: # Check for page overflow
            p.showPage()
            y_position = height - 50
            p.setFont("Helvetica", 10)
        p.drawString(60, y_position, line.strip())
        y_position -= 15

    p.showPage()
    p.save()
    return response

```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Include comprehensive unit tests for model methods and properties, and integration tests for all views (list, create, update, delete, and the print detail view).

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import PurchaseOrder
from .forms import PurchaseOrderForm

class PurchaseOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.po1 = PurchaseOrder.objects.create(
            id=1,
            po_number='PO001',
            supplier_code='SUP001',
            material_id=101,
            amendment_no=0,
            key_field='XYZ',
            order_date=timezone.now().date(),
            total_amount=100.00,
            status='Approved',
            details='First purchase order details.'
        )
        cls.po2 = PurchaseOrder.objects.create(
            id=2,
            po_number='PO002',
            supplier_code='SUP002',
            material_id=102,
            amendment_no=1,
            key_field='ABC',
            order_date=timezone.now().date(),
            total_amount=250.50,
            status='Pending',
            details='Second purchase order details with amendment.'
        )
  
    def test_purchase_order_creation(self):
        self.assertEqual(self.po1.po_number, 'PO001')
        self.assertEqual(self.po1.supplier_code, 'SUP001')
        self.assertEqual(self.po1.material_id, 101)
        self.assertEqual(self.po1.status, 'Approved')
        self.assertEqual(self.po1.total_amount, 100.00)
        
    def test_field_labels(self):
        field_po_number = self.po1._meta.get_field('po_number').verbose_name
        self.assertEqual(field_po_number, 'PO Number')
        field_supplier_code = self.po1._meta.get_field('supplier_code').verbose_name
        self.assertEqual(field_supplier_code, 'Supplier Code')
        
    def test_str_method(self):
        self.assertEqual(str(self.po1), "PO No: PO001 - Supplier: SUP001")

    def test_calculate_tax_method(self):
        self.assertEqual(self.po1.calculate_tax(0.10), 10.00) # 10% of 100.00
        self.assertEqual(self.po2.calculate_tax(0.05), 12.525) # 5% of 250.50

    def test_get_full_status_method(self):
        self.assertEqual(self.po1.get_full_status(), "Current Status: Approved")

class PurchaseOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.po1 = PurchaseOrder.objects.create(
            id=1,
            po_number='PO001',
            supplier_code='SUP001',
            material_id=101,
            amendment_no=0,
            key_field='XYZ',
            order_date=timezone.now().date(),
            total_amount=100.00,
            status='Approved',
            details='First purchase order details.'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('purchaseorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/list.html')
        self.assertTrue('purchaseorders' in response.context)
        self.assertContains(response, 'PO001') # Check if PO number is present

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/_purchaseorder_table.html')
        self.assertTrue('purchaseorders' in response.context)
        self.assertContains(response, 'PO001')
        self.assertNotContains(response, '<!DOCTYPE html>') # Ensure it's a partial

    def test_create_view_get(self):
        response = self.client.get(reverse('purchaseorder_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/form.html')
        self.assertTrue('form' in response.context)
        self.assertContains(response, 'Add Purchase Order')

    def test_create_view_post_success(self):
        data = {
            'po_number': 'PO003',
            'supplier_code': 'SUP003',
            'material_id': 103,
            'amendment_no': 0,
            'key_field': 'PQR',
            'order_date': '2023-01-15',
            'total_amount': 300.00,
            'status': 'New',
            'details': 'New PO for testing.'
        }
        response = self.client.post(reverse('purchaseorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content success
        self.assertTrue(PurchaseOrder.objects.filter(po_number='PO003').exists())
        self.assertFalse(response.content) # No content for 204

    def test_create_view_post_invalid(self):
        data = {
            'po_number': 'PO001', # Duplicate PO number (assuming unique constraint)
            'supplier_code': 'SUP003',
            'material_id': 103,
            'amendment_no': 0,
            'key_field': 'PQR',
            'order_date': '2023-01-15',
            'total_amount': 300.00,
            'status': 'New',
            'details': 'New PO for testing.'
        }
        response = self.client.post(reverse('purchaseorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX will swap the form back with errors
        self.assertTemplateUsed(response, 'material_management/purchaseorder/form.html')
        self.assertContains(response, 'already exists') # Check for error message
        self.assertFalse(PurchaseOrder.objects.filter(po_number='PO001', id=3).exists()) # Ensure no new object created

    def test_update_view_get(self):
        response = self.client.get(reverse('purchaseorder_edit', args=[self.po1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.po_number, 'PO001')

    def test_update_view_post_success(self):
        updated_data = {
            'po_number': 'PO001',
            'supplier_code': 'SUPUPDATED',
            'material_id': 101,
            'amendment_no': 0,
            'key_field': 'XYZ',
            'order_date': timezone.now().date().isoformat(), # Must be ISO format for forms
            'total_amount': 120.00,
            'status': 'Updated',
            'details': 'Updated details.'
        }
        response = self.client.post(reverse('purchaseorder_edit', args=[self.po1.pk]), updated_data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.supplier_code, 'SUPUPDATED')
        self.assertEqual(self.po1.status, 'Updated')

    def test_delete_view_get(self):
        response = self.client.get(reverse('purchaseorder_delete', args=[self.po1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].po_number, 'PO001')

    def test_delete_view_post_success(self):
        po_to_delete_pk = self.po1.pk # Store PK before deletion
        response = self.client.post(reverse('purchaseorder_delete', args=[po_to_delete_pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(PurchaseOrder.objects.filter(pk=po_to_delete_pk).exists())

    def test_print_detail_view(self):
        response = self.client.get(reverse('purchaseorder_print_detail', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/print_detail.html')
        self.assertTrue('purchase_order' in response.context)
        self.assertEqual(response.context['purchase_order'].po_number, 'PO001')
        self.assertContains(response, 'PO - Print Details')
        self.assertContains(response, 'First purchase order details.')

    def test_print_detail_view_not_found(self):
        response = self.client.get(reverse('purchaseorder_print_detail', args=[99999])) # Non-existent PK
        self.assertEqual(response.status_code, 404)

    def test_generate_po_pdf_view(self):
        # This test relies on ReportLab being installed and functional.
        # It verifies the response type and disposition.
        response = self.client.post(reverse('generate_po_pdf', args=[self.po1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertTrue(f'filename="PurchaseOrder_{self.po1.po_number}.pdf"' in response['Content-Disposition'])

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates extensively use HTMX for dynamic interactions:
-   **DataTables Loading:** `_purchaseorder_table.html` is loaded into `list.html` via `hx-get` on page load and `refreshPurchaseOrderList` trigger.
-   **Modal Forms:** Add/Edit/Delete buttons use `hx-get` to load forms into a modal (`#modalContent`), activated by Alpine.js (`_`).
-   **Form Submission:** Forms (`form.html`, `confirm_delete.html`) use `hx-post` with `hx-swap="none"` and backend `HX-Trigger` to refresh the table.
-   **Alpine.js:** `list.html` includes an Alpine.js script to manage modal visibility based on HTMX interactions. The `_` syntax is used for direct Alpine.js directives.
-   **DataTables:** JavaScript for DataTables is included in `_purchaseorder_table.html` to initialize the table after HTMX loads it.
-   **PDF Generation:** `print_detail.html` includes an HTMX `hx-post` button to trigger PDF generation, which then opens the generated PDF in a new window using `on htmx:afterOnLoad`.

All interactions are designed to work without full page reloads, providing a smooth user experience akin to Single Page Applications (SPAs) but with minimal JavaScript.

---

### Final Notes

This comprehensive plan provides a complete Django modernization strategy for the `PO_Print_Details.aspx` page. It transforms a simple `iframe`-based display into a full-fledged, modern Django application module for Purchase Order management, adhering to all specified guidelines:

-   **Business Value:** This modernization streamlines the Purchase Order viewing and printing process, improves user experience with dynamic interfaces, and sets the foundation for a robust, maintainable ERP system. It reduces reliance on legacy technologies (Crystal Reports, ASP.NET Web Forms) and enables future scalability and integration.
-   **AI-Assisted Automation:** The structured output, clear instructions, and adherence to templates are designed for automated code generation. Tools can parse this plan, extract code blocks, and populate placeholders, significantly reducing manual refactoring effort.
-   **Non-Technical Language:** While providing code, the preceding explanations and overall structure are tailored for business stakeholders to understand the "what" and "why" of the migration without getting lost in "how" (e.g., specific code syntax).
-   **Modular Design:** The solution is broken down into distinct Django application files (`models.py`, `forms.py`, `views.py`, `urls.py`, `templates`, `tests.py`), promoting a clean, maintainable, and scalable architecture.
-   **Modern Stack:** Exclusive use of Django 5.0+, HTMX, Alpine.js, and DataTables ensures a lean, performant, and maintainable frontend without heavy JavaScript frameworks.
-   **Test Coverage:** Emphasis on 80% test coverage ensures robustness and simplifies future development and maintenance.

This detailed blueprint allows for systematic, automated conversion processes, reducing manual effort, human error, and accelerating the transition to a modern Django ecosystem.