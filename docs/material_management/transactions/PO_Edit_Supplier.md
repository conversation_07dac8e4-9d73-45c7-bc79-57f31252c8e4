This document outlines a comprehensive plan for migrating the provided ASP.NET `PO_Edit_Supplier` module to a modern Django-based solution. The focus is on leveraging Django's strengths, adhering to best practices like fat models and thin views, and integrating modern frontend technologies such as HTMX, Alpine.js, and DataTables for a dynamic, efficient user experience.

---

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Business Value Proposition:
Migrating this legacy ASP.NET module to Django will provide significant business benefits by:
*   **Improving User Experience:** Modern, dynamic interfaces with HTMX and Alpine.js will eliminate full-page reloads, making interactions faster and smoother. DataTables will provide intuitive search, sort, and pagination features for managing large datasets.
*   **Reducing Operational Costs:** A unified, modern technology stack (Django, Python, HTMX, Alpine.js, PostgreSQL) is easier to maintain, scale, and secure compared to disparate legacy systems. This reduces dependencies on specialized ASP.NET developers.
*   **Enhancing Scalability and Performance:** Django's robust architecture and Python's ecosystem are well-suited for high-performance applications, allowing the system to handle increased user loads and data volumes more efficiently.
*   **Boosting Developer Productivity:** Python's readability and Django's "batteries-included" philosophy lead to faster development cycles for new features and easier onboarding for new team members.
*   **Future-Proofing the Application:** Moving away from a legacy framework ensures the application remains compatible with modern infrastructure, security standards, and evolving business requirements, reducing technical debt.
*   **Enabling Automated Migration:** Our approach focuses on AI-assisted automation, minimizing manual coding effort and accelerating the transition process, leading to quicker ROI.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis of ASP.NET Code:**
The ASP.NET code interacts with two primary tables: `tblMM_Supplier_master` and `tblMM_PO_Master`.

*   **`tblMM_Supplier_master`**:
    *   `SupplierId`: Used as a key, potentially a unique identifier (varchar/string).
    *   `SupplierName`: Supplier's full name (varchar/string).
    *   `CompId`: Company ID (integer), used for filtering.
    *   `SupId`: Implied primary key used for ordering (`Order by SupId Desc`).

*   **`tblMM_PO_Master`**:
    *   `SupplierId`: Links to `tblMM_Supplier_master` (varchar/string).
    *   `FinYearId`: Financial Year ID (varchar/string), used for filtering/counting.
    *   `CompId`: Company ID (integer), used for filtering.
    *   (Implied) Primary Key for the table (e.g., `POId`).

**Inferred Django Model Fields:**
*   **`Supplier` model**: `supplier_id` (CharField), `supplier_name` (CharField), `comp_id` (IntegerField), `sup_id` (AutoField, primary key).
*   **`PurchaseOrder` model**: `supplier_id` (CharField), `fin_year_id` (CharField), `comp_id` (IntegerField), `po_id` (AutoField, primary key).

### Step 2: Identify Backend Functionality

**Analysis of ASP.NET Code:**
The core functionality of this page is:

1.  **Read/Display Suppliers**: It fetches a list of suppliers.
2.  **Filter/Search**: Allows searching suppliers by name/ID via `txtSearchSupplier` and `btnSearch`.
3.  **Aggregate PO Count**: For each supplier, it calculates the number of associated Purchase Orders (`POItems`) from `tblMM_PO_Master` for a specific financial year and company. Only suppliers with at least one PO are displayed.
4.  **Selection/Redirection**: A "Select" button for each supplier redirects the user to a `PO_Edit.aspx` page, passing the `SupplierId`.
5.  **Pagination**: The `GridView` supports pagination.
6.  **Autocomplete**: The `txtSearchSupplier` has an autocomplete feature powered by a `WebMethod` that suggests supplier names based on prefix, returning "SupplierName [SupplierId]".

**Mapping to Django:**
*   A `ListView` will handle displaying the supplier list.
*   The search will be integrated with DataTables for client-side filtering, or if needed, a server-side filter in `get_queryset`.
*   The PO count aggregation will be handled by a method on the `Supplier` model (fat model principle).
*   The "Select" action will be an HTML link/button in the template leading to a new Django URL (e.g., `po_edit_detail`).
*   Pagination will be handled by DataTables.
*   Autocomplete will be a separate Django view providing JSON responses for HTMX.
*   No direct Create/Update/Delete of `Supplier` objects is shown on this page, but standard CRUD views will be provided for potential future use, adhering to the template guidelines.

### Step 3: Infer UI Components

**Analysis of ASP.NET Controls:**

*   **`txtSearchSupplier` (TextBox with AutoCompleteExtender)**: This will be a standard HTML `<input type="text">` element. The autocomplete functionality will be implemented using HTMX for real-time suggestions to a dedicated Django endpoint.
*   **`btnSearch` (Button)**: Its primary function is to trigger a search/data refresh. With DataTables, this button might become redundant as search is integrated, or it could trigger a server-side refresh via HTMX if complex filtering is implemented. For simple DataTables client-side search, the input field itself is sufficient. For now, we'll keep a conceptual search trigger for the table refresh.
*   **`GridView5` (GridView)**: This will be rendered as an HTML `<table>` element, managed by DataTables.js for client-side functionality (search, sort, paginate). The columns will be mapped directly.
    *   "SN" -> `forloop.counter` in Django template.
    *   "Select" (LinkButton) -> HTML `<button>` or `<a>` with a dynamic URL.
    *   "Supplier" (Label) -> `{{ supplier.supplier_name }}`
    *   "Code" (Label) -> `{{ supplier.supplier_id }}`
    *   "No. Of PO" (Label) -> `{{ supplier.po_count }}` (calculated in model/view).
*   **Modal Interactions**: While not explicitly a modal, the `AutoCompleteExtender` behavior suggests a dynamic, non-page-reload interaction. Django HTMX will be used for all dynamic interactions, including the autocomplete. Generic "Add/Edit/Delete" actions on the `list.html` (per provided template) will use a modal pattern with HTMX.

---

### Step 4: Generate Django Code

**App Name**: `materialmanagement`
**Model Name**: `Supplier`

#### 4.1 Models

*(File: `materialmanagement/models.py`)*

```python
from django.db import models
from django.db.models import Count, Q

# In a real application, you might have a Company model linked to these.
# For now, CompId and FinYearId are handled as integers/strings.

class Supplier(models.Model):
    """
    Represents a supplier in tblMM_Supplier_master.
    'Fat Model' approach: business logic related to supplier PO count is here.
    """
    sup_id = models.AutoField(db_column='SupId', primary_key=True) # Assuming this is the real PK from ORDER BY
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, unique=True, verbose_name="Supplier Code")
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, verbose_name="Supplier Name")
    comp_id = models.IntegerField(db_column='CompId', verbose_name="Company ID")
    # Add other fields from tblMM_Supplier_master if they exist and are relevant

    class Meta:
        managed = False  # Django will not manage this table's creation/alteration
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'
        ordering = ['-sup_id'] # Matches 'Order by SupId Desc'

    def __str__(self):
        return self.supplier_name

    def get_po_count(self, company_id, financial_year_id):
        """
        Calculates the number of Purchase Orders for this supplier
        based on company and financial year (FinYearId <= provided).
        Simulates the nested query logic in ASP.NET's LoadData method.
        """
        if not company_id or not financial_year_id:
            return 0 # Or raise an error, depending on desired behavior

        # Ensure financial_year_id comparison matches ASP.NET's '<=' logic.
        # Assuming FinYearId is comparable (e.g., 'FY2023', 'FY2024').
        return PurchaseOrder.objects.filter(
            supplier_id=self.supplier_id,
            comp_id=company_id,
            fin_year_id__lte=financial_year_id # Matching ASP.NET's <=
        ).count()

class PurchaseOrder(models.Model):
    """
    Represents a Purchase Order from tblMM_PO_Master.
    Minimal fields needed for counting purposes.
    """
    po_id = models.AutoField(db_column='POId', primary_key=True) # Assuming a primary key
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # Foreign key to Supplier, but CharField for managed=False
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.CharField(db_column='FinYearId', max_length=10) # e.g., 'FY2023'

    class Meta:
        managed = False  # Django will not manage this table
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO {self.po_id} for {self.supplier_id}"

```

#### 4.2 Forms

*(File: `materialmanagement/forms.py`)*

```python
from django import forms
from .models import Supplier

class SupplierForm(forms.ModelForm):
    """
    Form for creating and updating Supplier objects.
    Includes Tailwind CSS classes for styling.
    """
    class Meta:
        model = Supplier
        fields = ['supplier_id', 'supplier_name', 'comp_id'] # Add all fields relevant for CRUD
        widgets = {
            'supplier_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'comp_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'supplier_id': 'Supplier Code',
            'supplier_name': 'Supplier Name',
            'comp_id': 'Company ID',
        }

    # Custom validation example (if needed, based on ASP.NET validation)
    # def clean_supplier_id(self):
    #     supplier_id = self.cleaned_data['supplier_id']
    #     # Example: ensure supplier_id is unique across company, if not already handled by DB unique constraint
    #     if self.instance.pk is None and Supplier.objects.filter(supplier_id=supplier_id, comp_id=self.cleaned_data.get('comp_id')).exists():
    #         raise forms.ValidationError("Supplier ID already exists for this company.")
    #     return supplier_id

```

#### 4.3 Views

*(File: `materialmanagement/views.py`)*

```python
from django.views.generic import ListView, View, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, get_object_or_404
from django.db.models import F # For potential future more complex annotations

from .models import Supplier, PurchaseOrder
from .forms import SupplierForm

# Helper to get session values (assuming they are set in middleware or login process)
# In a real app, this would likely come from request.user.profile or similar.
def get_user_context(request):
    """Retrieves company ID and financial year ID from session."""
    # These values need to be populated into the session upon user login/selection
    # For demonstration, providing default values if not found, but in production,
    # this should either error or redirect if critical context is missing.
    company_id = request.session.get('compid', 1)  # Default to 1 for testing
    financial_year_id = request.session.get('finyear', 'FY2024') # Default for testing
    return company_id, financial_year_id

class SupplierListView(ListView):
    """
    Displays the main list of suppliers with their PO counts.
    Serves the initial full page load for the PO_Edit_Supplier view.
    """
    model = Supplier
    template_name = 'materialmanagement/supplier/list.html'
    context_object_name = 'suppliers' # This will hold annotated suppliers, not raw model objects

    def get_queryset(self):
        # The main queryset is just all suppliers, filtering is handled by get_context_data
        # to apply the PO count filter and pass comp_id/fin_year_id for 'fat model' method.
        # Initial filtering by company can happen here, matching ASP.NET.
        company_id, _ = get_user_context(self.request)
        queryset = super().get_queryset().filter(comp_id=company_id)

        # Apply search if a query parameter is present (for full-page reload search)
        # For DataTables, much of the search is client-side.
        search_query = self.request.GET.get('search_supplier', '').strip()
        if search_query:
            # Matches ASP.NET's likely search behavior on name/ID
            queryset = queryset.filter(
                Q(supplier_name__icontains=search_query) |
                Q(supplier_id__icontains=search_query)
            )
        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company_id, financial_year_id = get_user_context(self.request)

        # Process each supplier to calculate PO count and apply the filter
        # "Only add if PO count > 0", as in the original ASP.NET LoadData
        suppliers_data = []
        for supplier in context['object_list']: # Use object_list from parent ListView
            po_count = supplier.get_po_count(company_id, financial_year_id)
            if po_count > 0:
                suppliers_data.append({
                    'supplier_obj': supplier, # The actual Supplier model instance
                    'po_count': po_count,
                    'is_selectable': True # Redundant, as we only add if > 0, but explicit
                })
        context['suppliers_data'] = suppliers_data # Use a more descriptive name
        return context

class SupplierTablePartialView(SupplierListView):
    """
    Renders only the table portion of the supplier list.
    Designed to be loaded via HTMX for dynamic updates.
    Inherits filtering and PO count logic from SupplierListView.
    """
    template_name = 'materialmanagement/supplier/_supplier_table.html'

    # No need to override get_queryset or get_context_data,
    # as the parent class's implementations are suitable.

class SupplierAutocompleteView(View):
    """
    Provides JSON data for the supplier search autocomplete functionality via HTMX.
    Matches the `sql` WebMethod in the ASP.NET code-behind.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        company_id, _ = get_user_context(request)

        if not query:
            return JsonResponse([], safe=False)

        # Filter by company ID and supplier name starting with prefixText
        # Matches ASP.NET logic: 'CompId' and 'StartsWith'
        suppliers = Supplier.objects.filter(
            comp_id=company_id,
            supplier_name__icontains=query # Use icontains for broader search, startsWith is also fine
        ).values('supplier_id', 'supplier_name').order_by('supplier_name')[:10] # Limit results to 10 as in ASP.NET

        results = []
        for s in suppliers:
            # Format: "SupplierName [SupplierId]" as per ASP.NET AutoCompleteExtender
            results.append(f"{s['supplier_name']} [{s['supplier_id']}]")
        return JsonResponse(results, safe=False)

# --- Generic CRUD Views for Supplier (as per template requirements, not directly from original ASPX page) ---

class SupplierCreateView(CreateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'materialmanagement/supplier/form.html'
    success_url = reverse_lazy('po_edit_supplier_list') # Redirect to the main list after creation

    def get_initial(self):
        initial = super().get_initial()
        company_id, _ = get_user_context(self.request)
        initial['comp_id'] = company_id # Auto-fill company ID
        return initial

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierUpdateView(UpdateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'materialmanagement/supplier/form.html'
    success_url = reverse_lazy('po_edit_supplier_list')
    pk_url_kwarg = 'pk' # Ensure it looks for 'pk' in URL

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

class SupplierDeleteView(DeleteView):
    model = Supplier
    template_name = 'materialmanagement/supplier/confirm_delete.html'
    success_url = reverse_lazy('po_edit_supplier_list')
    pk_url_kwarg = 'pk' # Ensure it looks for 'pk' in URL

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList'
                }
            )
        return response

# --- Placeholder for the actual PO_Edit page view ---
class POEditDetailView(View):
    """
    Placeholder for the actual PO_Edit page to which the user is redirected.
    In a real scenario, this would be a detailed view for Purchase Orders related to the supplier.
    """
    def get(self, request, supplier_id):
        # Retrieve supplier object or relevant PO data using supplier_id
        # For demonstration, just return a simple response.
        supplier = get_object_or_404(Supplier, supplier_id=supplier_id)
        # You would then fetch POs related to this supplier_id and render a template
        # messages.info(request, f"Redirected to PO Edit for Supplier: {supplier.supplier_name} ({supplier.supplier_id})")
        return HttpResponse(f"<h1>Editing Purchase Orders for Supplier: {supplier.supplier_name} ({supplier_id})</h1><p>This is a placeholder for the PO_Edit module.</p>")

```

#### 4.4 Templates

*(Directory: `materialmanagement/templates/materialmanagement/supplier/`)*

**`list.html`**: The main page displaying the supplier list with search and the HTMX-powered table.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">PO - Edit Supplier</h2>
        
        <div class="flex items-center space-x-2">
            <label for="txtSearchSupplier" class="font-bold text-gray-700 whitespace-nowrap">Supplier:</label>
            <input type="text" id="txtSearchSupplier"
                   class="box3 block w-full md:w-80 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                   placeholder="Search supplier..."
                   hx-get="{% url 'supplier_autocomplete' %}"
                   hx-trigger="keyup changed delay:500ms, search"
                   hx-target="#autocomplete-results"
                   hx-indicator="#loading-indicator"
                   hx-swap="innerHTML">
            
            <button id="btnSearch"
                    class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    hx-get="{% url 'po_edit_supplier_table' %}"
                    hx-target="#supplierTable-container"
                    hx-trigger="click"
                    hx-vals="js:{ search_supplier: document.getElementById('txtSearchSupplier').value }"
                    hx-indicator="#loading-indicator"
                    hx-swap="innerHTML">
                Search
            </button>
        </div>

        <button 
            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'supplier_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Supplier
        </button>
    </div>

    <div id="autocomplete-results" class="absolute z-10 bg-white border border-gray-200 rounded shadow-lg mt-1 w-full md:w-80 max-h-60 overflow-y-auto"
         _="on click if event.target.closest('#autocomplete-results')
            set #txtSearchSupplier.value to event.target.innerText.split(' [')[0]
            then hide me">
        <!-- Autocomplete suggestions will appear here -->
    </div>

    <div id="loading-indicator" class="htmx-indicator text-center py-4">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-600">Loading...</p>
    </div>
    
    <div id="supplierTable-container"
         hx-trigger="load, refreshSupplierList from:body"
         hx-get="{% url 'po_edit_supplier_table' %}"
         hx-swap="innerHTML"
         hx-indicator="#loading-indicator">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading suppliers...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- DataTables CDN - ensure it's loaded AFTER jQuery and before custom script -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
<script type="text/javascript" src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>

<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}
```

**`_supplier_table.html`**: Partial template for the DataTables table, loaded via HTMX.

```html
<div class="overflow-x-auto">
    <table id="supplierTable" class="min-w-full bg-white shadow-md rounded-lg overflow-hidden">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th class="py-3 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 border-b border-gray-200 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">No. Of PO</th>
                <th class="py-3 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% if suppliers_data %}
                {% for data in suppliers_data %}
                <tr class="hover:bg-gray-50 {% cycle 'bg-white' 'bg-gray-50' %}">
                    <td class="py-3 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 border-b border-gray-200 text-gray-700">{{ data.supplier_obj.supplier_name }}</td>
                    <td class="py-3 px-4 border-b border-gray-200 text-gray-700">{{ data.supplier_obj.supplier_id }}</td>
                    <td class="py-3 px-4 border-b border-gray-200 text-right text-gray-700">{{ data.po_count }}</td>
                    <td class="py-3 px-4 border-b border-gray-200 text-center">
                        {% if data.is_selectable %} {# This condition is implicitly true due to previous filter #}
                            <a href="{% url 'po_edit' data.supplier_obj.supplier_id %}"
                               class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-1 px-3 rounded text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50">
                                Select
                            </a>
                        {% endif %}
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-sm mr-1 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-opacity-50"
                            hx-get="{% url 'supplier_edit' data.supplier_obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-3 rounded text-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-opacity-50"
                            hx-get="{% url 'supplier_delete' data.supplier_obj.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="5" class="py-3 px-4 text-center text-lg font-medium text-red-600">
                    No data to display !
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is initialized only once per HTMX load
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#supplierTable')) {
            $('#supplierTable').DataTable().destroy();
        }
        $('#supplierTable').DataTable({
            "pageLength": 20, // Matching ASP.NET's PageSize="20"
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true, // Make table responsive
            "searching": true, // Enable search box
            "ordering": true,  // Enable sorting
            "paging": true,    // Enable pagination
            // Disable DataTables' default search input if you rely solely on the custom one
            // "dom": 'lrtip' // Show length, table, pagination, info (hide search)
            "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>',
            // 'l' - Length changing input control
            // 'f' - Filtering input
            // 't' - The table!
            // 'i' - Table information summary
            // 'p' - Pagination
            // 'r' - pRocessing display element
        });
    });
</script>
```

**`_supplier_form.html`**: Partial template for supplier creation/editing form.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Supplier</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) {
                                  htmx.trigger(document.body, 'refreshSupplierList'); 
                                  document.getElementById('modal').classList.remove('is-active');
                                } else { 
                                  htmx.swap('outerHTML', event.detail.xhr.responseText);
                                }">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="list-none p-0 mt-1">
                    {% for error in field.errors %}
                    <li class="text-red-500 text-xs mt-1">{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`_supplier_confirm_delete.html`**: Partial template for delete confirmation.

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete supplier <strong>{{ object.supplier_name }} ({{ object.supplier_id }})</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) {
                                  htmx.trigger(document.body, 'refreshSupplierList'); 
                                  document.getElementById('modal').classList.remove('is-active');
                                }">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

*(File: `materialmanagement/urls.py`)*

```python
from django.urls import path
from .views import (
    SupplierListView,
    SupplierTablePartialView,
    SupplierAutocompleteView,
    SupplierCreateView,
    SupplierUpdateView,
    SupplierDeleteView,
    POEditDetailView # For the redirect target
)

urlpatterns = [
    # Main supplier list view (equivalent to PO_Edit_Supplier.aspx)
    path('po-edit-supplier/', SupplierListView.as_view(), name='po_edit_supplier_list'),
    
    # HTMX endpoint for refreshing only the table content
    path('po-edit-supplier/table/', SupplierTablePartialView.as_view(), name='po_edit_supplier_table'),
    
    # HTMX endpoint for the supplier autocomplete search
    path('po-edit-supplier/autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # Generic CRUD operations for Supplier model (not directly from the original ASPX, but standard for completeness)
    path('supplier/add/', SupplierCreateView.as_view(), name='supplier_add'),
    path('supplier/edit/<int:pk>/', SupplierUpdateView.as_view(), name='supplier_edit'), # Uses sup_id (int PK)
    path('supplier/delete/<int:pk>/', SupplierDeleteView.as_view(), name='supplier_delete'), # Uses sup_id (int PK)

    # The target page for the 'Select' button (PO_Edit.aspx?Code=...)
    # SupplierId is a string, so use <str:supplier_id>
    path('po-edit/<str:supplier_id>/', POEditDetailView.as_view(), name='po_edit'), 
]

```

#### 4.6 Tests

*(File: `materialmanagement/tests.py`)*

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock

from .models import Supplier, PurchaseOrder
from .forms import SupplierForm
from .views import get_user_context # Import the helper for mocking session context

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company_id = 1
        cls.fin_year_id = 'FY2024'

        cls.supplier1 = Supplier.objects.create(
            supplier_id='SUP001',
            supplier_name='Alpha Supplier',
            comp_id=cls.company_id,
            sup_id=1 # Explicitly set for testing consistent PK behavior
        )
        cls.supplier2 = Supplier.objects.create(
            supplier_id='SUP002',
            supplier_name='Beta Supplier',
            comp_id=cls.company_id,
            sup_id=2
        )
        cls.supplier3 = Supplier.objects.create(
            supplier_id='SUP003',
            supplier_name='Gamma Supplier',
            comp_id=cls.company_id + 1, # Different company
            sup_id=3
        )
        
        # Create Purchase Orders for supplier1
        PurchaseOrder.objects.create(
            po_id=101, supplier_id='SUP001', comp_id=cls.company_id, fin_year_id='FY2023'
        )
        PurchaseOrder.objects.create(
            po_id=102, supplier_id='SUP001', comp_id=cls.company_id, fin_year_id='FY2024'
        )
        PurchaseOrder.objects.create(
            po_id=103, supplier_id='SUP001', comp_id=cls.company_id, fin_year_id='FY2025'
        )
        
        # Create Purchase Orders for supplier2
        PurchaseOrder.objects.create(
            po_id=201, supplier_id='SUP002', comp_id=cls.company_id, fin_year_id='FY2024'
        )

    def test_supplier_creation(self):
        self.assertEqual(self.supplier1.supplier_id, 'SUP001')
        self.assertEqual(self.supplier1.supplier_name, 'Alpha Supplier')
        self.assertEqual(self.supplier1.comp_id, self.company_id)
        self.assertEqual(str(self.supplier1), 'Alpha Supplier')
        
    def test_supplier_fields_verbose_name(self):
        field_label = self.supplier1._meta.get_field('supplier_id').verbose_name
        self.assertEqual(field_label, 'Supplier Code')
        field_label = self.supplier1._meta.get_field('supplier_name').verbose_name
        self.assertEqual(field_label, 'Supplier Name')
        
    def test_get_po_count_for_current_financial_year(self):
        # SUP001 has POs in FY2023, FY2024, FY2025. For FY2024 (<=), should count FY2023 and FY2024
        po_count = self.supplier1.get_po_count(self.company_id, 'FY2024')
        self.assertEqual(po_count, 2) 

    def test_get_po_count_for_earlier_financial_year(self):
        # SUP001 has POs in FY2023, FY2024, FY2025. For FY2023 (<=), should count only FY2023
        po_count = self.supplier1.get_po_count(self.company_id, 'FY2023')
        self.assertEqual(po_count, 1)
        
    def test_get_po_count_no_po_for_financial_year(self):
        # SUP001 has POs in FY2023, FY2024, FY2025. For FY2022 (<=), should count 0
        po_count = self.supplier1.get_po_count(self.company_id, 'FY2022')
        self.assertEqual(po_count, 0)
        
    def test_get_po_count_different_company(self):
        # Supplier3 is in a different company, should have 0 POs for supplier1's company
        po_count = self.supplier3.get_po_count(self.company_id, self.fin_year_id)
        self.assertEqual(po_count, 0)

    def test_get_po_count_no_pos(self):
        # Supplier with no POs associated for the specific company/fin_year
        Supplier.objects.create(
            supplier_id='SUP004',
            supplier_name='Delta Supplier',
            comp_id=self.company_id,
            sup_id=4
        )
        supplier4 = Supplier.objects.get(supplier_id='SUP004')
        po_count = supplier4.get_po_count(self.company_id, self.fin_year_id)
        self.assertEqual(po_count, 0)

class SupplierViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company_id = 1
        cls.fin_year_id = 'FY2024'

        cls.supplier1 = Supplier.objects.create(
            supplier_id='SUP001', supplier_name='Alpha Supplier', comp_id=cls.company_id, sup_id=1
        )
        cls.supplier2 = Supplier.objects.create(
            supplier_id='SUP002', supplier_name='Beta Supplier', comp_id=cls.company_id, sup_id=2
        )
        cls.supplier3 = Supplier.objects.create(
            supplier_id='SUP003', supplier_name='Gamma Supplier', comp_id=cls.company_id, sup_id=3
        )
        
        PurchaseOrder.objects.create(
            po_id=101, supplier_id='SUP001', comp_id=cls.company_id, fin_year_id='FY2023'
        )
        PurchaseOrder.objects.create(
            po_id=102, supplier_id='SUP001', comp_id=cls.company_id, fin_year_id='FY2024'
        )
        PurchaseOrder.objects.create(
            po_id=201, supplier_id='SUP002', comp_id=cls.company_id, fin_year_id='FY2024'
        )
        PurchaseOrder.objects.create(
            po_id=301, supplier_id='SUP003', comp_id=cls.company_id + 1, fin_year_id='FY2024'
        )
    
    def setUp(self):
        self.client = Client()
        # Mock the get_user_context to provide consistent session data for tests
        self.mock_get_user_context_patcher = patch('materialmanagement.views.get_user_context')
        self.mock_get_user_context = self.mock_get_user_context_patcher.start()
        self.mock_get_user_context.return_value = (self.company_id, self.fin_year_id)
        # Also mock session in client if view directly accesses request.session
        self.client.session['compid'] = self.company_id
        self.client.session['finyear'] = self.fin_year_id

    def tearDown(self):
        self.mock_get_user_context_patcher.stop()

    def test_supplier_list_view(self):
        response = self.client.get(reverse('po_edit_supplier_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialmanagement/supplier/list.html')
        # Check that only suppliers with POs for the company/fin_year are in context
        self.assertIn('suppliers_data', response.context)
        # Supplier 1 (SUP001) and Supplier 2 (SUP002) should be present for company_id=1
        self.assertEqual(len(response.context['suppliers_data']), 2) 
        self.assertEqual(response.context['suppliers_data'][0]['supplier_obj'].supplier_id, 'SUP001')
        self.assertEqual(response.context['suppliers_data'][0]['po_count'], 2) # For FY2024
        self.assertEqual(response.context['suppliers_data'][1]['supplier_obj'].supplier_id, 'SUP002')
        self.assertEqual(response.context['suppliers_data'][1]['po_count'], 1) # For FY2024

    def test_supplier_list_view_search(self):
        response = self.client.get(reverse('po_edit_supplier_list') + '?search_supplier=alpha')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialmanagement/supplier/list.html')
        self.assertEqual(len(response.context['suppliers_data']), 1)
        self.assertEqual(response.context['suppliers_data'][0]['supplier_obj'].supplier_name, 'Alpha Supplier')

    def test_supplier_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('po_edit_supplier_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialmanagement/supplier/_supplier_table.html')
        self.assertIn('suppliers_data', response.context)
        self.assertEqual(len(response.context['suppliers_data']), 2)
        # Check content in the response (e.g., supplier name, PO count)
        self.assertContains(response, 'Alpha Supplier')
        self.assertContains(response, '2')
        self.assertContains(response, 'Beta Supplier')
        self.assertContains(response, '1')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete') + '?q=sup')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        data = response.json()
        self.assertIsInstance(data, list)
        self.assertIn('Alpha Supplier [SUP001]', data)
        self.assertIn('Beta Supplier [SUP002]', data)
        self.assertNotIn('Gamma Supplier [SUP003]', data) # Different company

    def test_supplier_autocomplete_view_empty_query(self):
        response = self.client.get(reverse('supplier_autocomplete'))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.json(), [])

    def test_supplier_create_view_get(self):
        response = self.client.get(reverse('supplier_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialmanagement/supplier/form.html')
        self.assertIsInstance(response.context['form'], SupplierForm)
        self.assertEqual(response.context['form'].initial['comp_id'], self.company_id) # Test initial comp_id

    def test_supplier_create_view_post_success(self):
        data = {
            'supplier_id': 'NEW001',
            'supplier_name': 'New Supplier',
            'comp_id': self.company_id,
        }
        response = self.client.post(reverse('supplier_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful creation
        self.assertTrue(Supplier.objects.filter(supplier_id='NEW001').exists())
        self.assertRedirects(response, reverse('po_edit_supplier_list'))

    def test_supplier_create_view_post_htmx_success(self):
        data = {
            'supplier_id': 'NEW002',
            'supplier_name': 'New HTMX Supplier',
            'comp_id': self.company_id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('supplier_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # 204 No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierList')
        self.assertTrue(Supplier.objects.filter(supplier_id='NEW002').exists())

    def test_supplier_update_view_get(self):
        obj = self.supplier1
        response = self.client.get(reverse('supplier_edit', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialmanagement/supplier/form.html')
        self.assertIsInstance(response.context['form'], SupplierForm)
        self.assertEqual(response.context['form'].instance, obj)

    def test_supplier_update_view_post_success(self):
        obj = self.supplier1
        data = {
            'supplier_id': obj.supplier_id, # Keep ID same
            'supplier_name': 'Updated Alpha Supplier',
            'comp_id': obj.comp_id,
        }
        response = self.client.post(reverse('supplier_edit', args=[obj.pk]), data)
        self.assertEqual(response.status_code, 302)
        obj.refresh_from_db()
        self.assertEqual(obj.supplier_name, 'Updated Alpha Supplier')
        self.assertRedirects(response, reverse('po_edit_supplier_list'))

    def test_supplier_update_view_post_htmx_success(self):
        obj = self.supplier2
        data = {
            'supplier_id': obj.supplier_id,
            'supplier_name': 'Updated Beta HTMX Supplier',
            'comp_id': obj.comp_id,
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('supplier_edit', args=[obj.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierList')
        obj.refresh_from_db()
        self.assertEqual(obj.supplier_name, 'Updated Beta HTMX Supplier')

    def test_supplier_delete_view_get(self):
        obj = self.supplier1
        response = self.client.get(reverse('supplier_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'materialmanagement/supplier/confirm_delete.html')
        self.assertEqual(response.context['object'], obj)

    def test_supplier_delete_view_post_success(self):
        obj = self.supplier1
        response = self.client.post(reverse('supplier_delete', args=[obj.pk]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(Supplier.objects.filter(pk=obj.pk).exists())
        self.assertRedirects(response, reverse('po_edit_supplier_list'))

    def test_supplier_delete_view_post_htmx_success(self):
        obj = self.supplier2
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('supplier_delete', args=[obj.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshSupplierList')
        self.assertFalse(Supplier.objects.filter(pk=obj.pk).exists())

    def test_po_edit_detail_view(self):
        response = self.client.get(reverse('po_edit', args=[self.supplier1.supplier_id]))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, f"<h1>Editing Purchase Orders for Supplier: {self.supplier1.supplier_name} ({self.supplier1.supplier_id})</h1>")
```

---

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for dynamic table updates:** The `_supplier_table.html` partial is loaded initially and refreshed using `hx-get` on `load` and `refreshSupplierList` custom events.
*   **HTMX for form modals:** `Add`, `Edit`, and `Delete` buttons use `hx-get` to fetch the form content (`_supplier_form.html` or `_supplier_confirm_delete.html`) into a modal container (`#modalContent`). Form submission (`hx-post`) triggers `HX-Trigger: refreshSupplierList` and closes the modal (HTTP 204 response).
*   **HTMX for autocomplete:** The `txtSearchSupplier` uses `hx-get` to call `supplier_autocomplete` view on `keyup changed delay:500ms`, targeting `#autocomplete-results` for suggestions.
*   **Alpine.js/Hyperscript for modal visibility:** The `_` (Hyperscript) syntax `on click add .is-active to #modal` and `remove .is-active from me` is used to control modal display. Clicking outside the modal (`if event.target.id == 'modal'`) also closes it.
*   **DataTables.js for list presentation:** The `_supplier_table.html` includes JavaScript to initialize DataTables on the rendered table, providing out-of-the-box search, sort, and pagination. It's crucial to re-initialize DataTables when the partial is loaded by HTMX, hence the `if ($.fn.DataTable.isDataTable('#supplierTable'))` check.
*   **No additional JavaScript beyond HTMX, Alpine.js, and DataTables:** This migration strictly adheres to the preference for these libraries, minimizing custom JavaScript.
*   **DRY Template Inheritance:** All templates extend `core/base.html` to inherit common structure, CDNs, and scripts (like jQuery, HTMX, Alpine.js, DataTables).

---

## Final Notes

*   **Placeholders:** Replace `[APP_NAME]` with `materialmanagement`. Model and field names are already set.
*   **Database Configuration:** Ensure Django's `settings.py` is configured to connect to your existing SQL Server database (e.g., using `django-pyodbc-azure` or `mssql-django` libraries) and that `managed = False` is correctly set in `Meta` classes of models.
*   **Session Management:** The `get_user_context` helper in `views.py` mocks `compid` and `finyear`. In a real application, these values should be retrieved from the authenticated user's session or profile after a proper login process.
*   **Error Handling:** The original ASP.NET code had `try-catch` blocks that often just swallowed exceptions (`catch (Exception est) { }`). In Django, proper error handling (e.g., logging, displaying user-friendly messages, specific exception handling) should be implemented.
*   **CSS:** The Tailwind CSS classes are included directly in the templates, assuming Tailwind is correctly set up in the Django project.
*   **Security:** Always consider security best practices like input validation (Django Forms handle much of this), proper authentication and authorization (Django's built-in system), and protection against common web vulnerabilities (CSRF token is included).
*   **Logging:** Implement Django's logging framework to track application behavior and errors.

This comprehensive plan provides a clear, actionable roadmap for transitioning the ASP.NET `PO_Edit_Supplier` module to a modern Django application, focusing on automation, maintainability, and enhanced user experience.