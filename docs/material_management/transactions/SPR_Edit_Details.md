## ASP.NET to Django Conversion Script: SPR - Edit Details

This modernization plan outlines the strategic transition of your existing ASP.NET SPR Edit Details module to a robust, scalable Django 5.0+ application. Our approach prioritizes AI-assisted automation, clean architecture (fat models, thin views), and a highly interactive frontend powered by HTMX and Alpine.js, ensuring a modern, maintainable, and high-performance solution.

### Business Value Proposition

Migrating this module to Django will deliver significant business benefits:

1.  **Enhanced Maintainability & Scalability:** Django's structured MVC (MTV) pattern, coupled with "fat model, thin view" principles, centralizes business logic, making the application easier to understand, debug, and extend. This reduces future development costs and accelerates feature delivery.
2.  **Improved User Experience:** By leveraging HTMX and Alpine.js, user interactions (like editing individual SPR detail items) will occur without full page reloads, providing a snappier, more responsive experience akin to a single-page application but with simpler development. DataTables integration ensures efficient handling of large datasets with instant search, sort, and pagination.
3.  **Future-Proof Technology Stack:** Moving away from legacy ASP.NET Web Forms to modern Django ensures your application runs on a cutting-edge, actively maintained framework, reducing security risks and improving developer productivity.
4.  **Reduced Development Time & Cost:** The automation-driven approach means significant portions of the migration can be handled by AI tools, drastically cutting down manual coding effort and human error, leading to faster deployment and lower overall project costs.
5.  **Centralized Business Logic:** Complex logic like preventing edits on PO-linked items (`disableEdit` function) is encapsulated within the Django models, ensuring consistency and reusability across the application, rather than being scattered across UI and data layers.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists (`core/base.html`).
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code-behind performs several SQL queries using a `clsFunctions` helper, joining data from multiple tables to build the `GridView`. The primary tables involved in this "SPR - Edit Details" view are:

*   `tblMM_SPR_Master`: Contains master information for an SPR, filtered by `SPRNo` and `MId` (which maps to `Id`).
*   `tblMM_SPR_Details`: The core detail records, filtered by `SPRNo` and `MId`, and providing most of the displayed columns.
*   `tblDG_Item_Master`: Provides `ItemCode` and `ManfDesc` (Description) based on `ItemId` from `tblMM_SPR_Details`.
*   `Unit_Master`: Provides `Symbol` (UOM) based on `UOMBasic` from `tblDG_Item_Master`.
*   `tblMM_Supplier_master`: Provides `SupplierName` based on `SupplierId` from `tblMM_SPR_Details`.
*   `AccHead`: Provides `Symbol` (AcHead) based on `AHId` from `tblMM_SPR_Details`.
*   `BusinessGroup`: Provides `Symbol` (Dept) based on `DeptId` from `tblMM_SPR_Details`.
*   `tblMM_PO_Details`: Used in `disableEdit()` to check if an SPR detail item is linked to a Purchase Order.
*   `tblMM_PO_Master`: Also used in `disableEdit()` for PO master details.
*   `Company`: Implied by `CompId` in many tables for multi-tenancy.

**Inferred Tables and Key Columns:**

*   **`tblMM_SPR_Master`** (Primary model for SPR header, referred to as `SPRMaster` in Django)
    *   `Id` (Primary Key, used as `MId` in query string)
    *   `SPRNo` (SPR Number)
    *   `CompId` (Company ID, Foreign Key to `tblCompany`)

*   **`tblMM_SPR_Details`** (Core model for SPR line items, referred to as `SPRDetail` in Django)
    *   `Id` (Primary Key, used as `recId` in query string)
    *   `MId` (Foreign Key to `tblMM_SPR_Master.Id`)
    *   `ItemId` (Foreign Key to `tblDG_Item_Master.Id`)
    *   `Qty` (Quantity)
    *   `Rate` (Rate)
    *   `SupplierId` (Foreign Key to `tblMM_Supplier_master.SupplierId`)
    *   `AHId` (Foreign Key to `AccHead.Id`)
    *   `WONo` (Work Order Number)
    *   `DeptId` (Foreign Key to `BusinessGroup.Id`)
    *   `Discount` (Discount)
    *   `Remarks` (Remarks)

*   **`tblDG_Item_Master`** (Lookup table for items, referred to as `ItemMaster` in Django)
    *   `Id` (Primary Key)
    *   `ItemCode` (Item Code)
    *   `ManfDesc` (Manufacturer Description, used as `Description`)
    *   `UOMBasic` (Foreign Key to `Unit_Master.Id`)

*   **`Unit_Master`** (Lookup table for Units of Measure, referred to as `Unit` in Django)
    *   `Id` (Primary Key)
    *   `Symbol` (Unit Symbol, used as `UOM`)

*   **`tblMM_Supplier_master`** (Lookup table for suppliers, referred to as `Supplier` in Django)
    *   `SupplierId` (Primary Key)
    *   `SupplierName` (Supplier Name)

*   **`AccHead`** (Lookup table for Account Heads, referred to as `AccountHead` in Django)
    *   `Id` (Primary Key)
    *   `Symbol` (Account Head Symbol, used as `AcHead`)

*   **`BusinessGroup`** (Lookup table for Business Groups/Departments, referred to as `BusinessGroup` in Django)
    *   `Id` (Primary Key)
    *   `Symbol` (Business Group Symbol, used as `Dept`)

*   **`tblMM_PO_Master`** (Lookup table for Purchase Order master, referred to as `POMaster` in Django)
    *   `Id` (Primary Key)
    *   `PONo` (Purchase Order Number)
    *   `CompId` (Foreign Key to `tblCompany`)

*   **`tblMM_PO_Details`** (Lookup table for Purchase Order details, referred to as `PODetail` in Django)
    *   `Id` (Primary Key)
    *   `SPRId` (Foreign Key to `tblMM_SPR_Details.Id`)
    *   `MId` (Foreign Key to `tblMM_PO_Master.Id`)

*   **`tblCompany`** (Implied lookup table for companies, referred to as `Company` in Django)
    *   `Id` (Primary Key, used as `CompId`)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Read (Display List):** The core functionality is to fetch and display a list of SPR detail items associated with a specific SPR Master. This involves complex joins and lookups across multiple master tables to enrich the display data. The `LoadData()` method handles this.
*   **Update (Edit Single Item):** The `GridView3_RowCommand` for `CommandName="edt"` redirects to `SPR_Edit_Details_Item.aspx` with the specific SPR detail item's ID (`recId`). This signifies an "Edit" operation for an individual line item.
*   **Business Rule (`disableEdit`):** A crucial business rule identified is that an SPR detail item cannot be edited if it has been linked to a Purchase Order. This check is performed by querying `tblMM_PO_Details` and `tblMM_PO_Master`. This must be implemented as a method within the `SPRDetail` model in Django.
*   **Navigation:** The `btncancel_Click` event simply redirects the user to `SPR_Edit.aspx`, which is likely the main list of SPRs.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **`GridView3` (`CssClass="yui-datatable-theme"`)**: This is the primary data display component. It features paging, custom column templates for rendering data (`<%# Eval() %>`), and an "Edit" `LinkButton`. This will be replaced by a DataTables-powered HTML table in Django, dynamically loaded and updated via HTMX.
*   **`Panel1` (`ScrollBars="Auto"`)**: A simple container for the `GridView`, indicating the need for scrollable content, which can be handled by Tailwind CSS classes like `overflow-auto`.
*   **`LinkButton` (`linkBtn`, `CommandName="edt"`)**: Used for the "Edit" action on each row. This will trigger an HTMX GET request to load an edit form into a modal.
*   **`Label` (`lblDel`, `Visible="false"`)**: Conditionally displayed as "PO" if the item cannot be edited. This visibility logic will be handled by a conditional statement in the Django template based on a model property.
*   **`Button` (`btncancel`, `CssClass="redbox"`)**: A simple "Cancel" button that redirects. This will be a standard HTML anchor or button linked to a Django URL.
*   **`Css/Javascript` includes**: The external CSS (`yui-datatable.css`, `StyleSheet.css`) and JavaScript (`PopUpMsg.js`, `loadingNotifier.js`) will be replaced by Tailwind CSS for styling and HTMX/Alpine.js for interactive elements, with DataTables handling the grid functionality.

### Step 4: Generate Django Code

We will create a new Django application, let's call it `material_management`, and within it, a `spr` sub-module to manage SPRs.

#### 4.1 Models (`material_management/spr/models.py`)

This section defines the Django models for the SPR and related entities, mapping them to your existing database tables. The `SPRDetail` model will contain crucial business logic for checking editability.

```python
from django.db import models
from django.db.models import F

# Supporting Lookup Models (managed=False to map to existing DB)
class Company(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    # Assuming 'Name' or similar column for company identification
    name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class SPRMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Master'
        verbose_name = 'SPR Master'
        verbose_name_plural = 'SPR Masters'

    def __str__(self):
        return self.spr_no

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    description = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.ForeignKey('Unit', models.DO_NOTHING, db_column='UOMBasic') # FK to Unit_Master.Id

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.description}"

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=10)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class Supplier(models.Model):
    supplier_id = models.IntegerField(db_column='SupplierId', primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol

class BusinessGroup(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'BusinessGroup'
        verbose_name = 'Business Group'
        verbose_name_plural = 'Business Groups'

    def __str__(self):
        return self.symbol

class POMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

    def __str__(self):
        return self.po_no

class PODetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_detail = models.ForeignKey('SPRDetail', models.DO_NOTHING, db_column='SPRId')
    po_master = models.ForeignKey(POMaster, models.DO_NOTHING, db_column='MId') # This MId refers to POMaster's Id

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"PO Detail {self.id} for SPR Detail {self.spr_detail.id}"


# Main Model for the page: SPRDetail
class SPRDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_master = models.ForeignKey(SPRMaster, models.DO_NOTHING, db_column='MId')
    item = models.ForeignKey(ItemMaster, models.DO_NOTHING, db_column='ItemId')
    qty = models.FloatField(db_column='Qty')
    rate = models.FloatField(db_column='Rate')
    supplier = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId')
    account_head = models.ForeignKey(AccountHead, models.DO_NOTHING, db_column='AHId')
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    business_group = models.ForeignKey(BusinessGroup, models.DO_NOTHING, db_column='DeptId')
    discount = models.CharField(db_column='Discount', max_length=50, blank=True, null=True) # Assuming string as per Eval("Discount")
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

    def __str__(self):
        return f"SPR Detail {self.id} for {self.spr_master.spr_no}"

    # Business logic methods (Fat Model)
    def has_purchase_order(self):
        """
        Checks if this SPR Detail item has an associated Purchase Order.
        Corresponds to the disableEdit() logic in ASP.NET.
        """
        return self.podetail_set.exists()

    def get_display_item_code(self):
        """
        Returns the Item Code from the related ItemMaster.
        """
        return self.item.item_code if self.item else 'N/A'

    def get_display_description(self):
        """
        Returns the Item Description from the related ItemMaster.
        """
        return self.item.description if self.item else 'N/A'

    def get_display_uom(self):
        """
        Returns the UOM symbol from the related Unit.
        """
        return self.item.uom_basic.symbol if self.item and self.item.uom_basic else 'N/A'

    def get_display_supplier_name(self):
        """
        Returns the Supplier Name from the related Supplier.
        """
        return self.supplier.supplier_name if self.supplier else 'N/A'

    def get_display_account_head(self):
        """
        Returns the Account Head symbol from the related AccountHead.
        """
        return self.account_head.symbol if self.account_head else 'N/A'

    def get_display_business_group(self):
        """
        Returns the Business Group symbol from the related BusinessGroup.
        """
        return self.business_group.symbol if self.business_group else 'N/A'

    def get_display_wo_no(self):
        """
        Returns WO No, 'NA' if empty as per ASP.NET logic.
        """
        return self.wo_no if self.wo_no else 'NA'
```

#### 4.2 Forms (`material_management/spr/forms.py`)

This form will be used for editing individual `SPRDetail` items.

```python
from django import forms
from .models import SPRDetail, ItemMaster, Supplier, AccountHead, BusinessGroup

class SPRDetailForm(forms.ModelForm):
    # Add choices for foreign key fields if they need to be selected from a list
    # For now, we'll assume basic text/number inputs matching the database types.
    # In a real scenario, these would likely be dropdowns/select fields.
    
    # Example for Item (assuming user selects an existing item)
    item = forms.ModelChoiceField(
        queryset=ItemMaster.objects.all().order_by('item_code'), 
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select Item"
    )
    supplier = forms.ModelChoiceField(
        queryset=Supplier.objects.all().order_by('supplier_name'),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select Supplier"
    )
    account_head = forms.ModelChoiceField(
        queryset=AccountHead.objects.all().order_by('symbol'),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select A/c Head"
    )
    business_group = forms.ModelChoiceField(
        queryset=BusinessGroup.objects.all().order_by('symbol'),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select BG Group"
    )

    class Meta:
        model = SPRDetail
        fields = [
            'item', 'qty', 'rate', 'supplier', 'account_head', 
            'wo_no', 'business_group', 'discount', 'remarks'
        ]
        # SPRMaster is set in the view, not editable directly in this form
        
        widgets = {
            'qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.001'}),
            'rate': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'wo_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'discount': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        
    def clean(self):
        cleaned_data = super().clean()
        # Add custom validation logic here if needed, e.g.,
        # if cleaned_data['qty'] <= 0:
        #    self.add_error('qty', 'Quantity must be positive.')
        return cleaned_data

```

#### 4.3 Views (`material_management/spr/views.py`)

These views implement the display and CRUD operations for SPR details. Note that `SPRDetailListView` and `SPRDetailTablePartialView` are designed to display details for a *specific* SPR Master, matching the ASP.NET page's behavior.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import get_object_or_404
from .models import SPRDetail, SPRMaster
from .forms import SPRDetailForm

# View for displaying SPR Details List for a specific SPR Master
class SPRDetailListView(ListView):
    model = SPRDetail
    template_name = 'material_management/spr/sprdetail/list.html'
    context_object_name = 'spr_details'

    def get_queryset(self):
        master_pk = self.kwargs.get('master_pk')
        self.spr_master = get_object_or_404(SPRMaster, pk=master_pk)
        # Prefetch related objects to reduce database queries (equivalent to ASP.NET's multiple selects)
        return SPRDetail.objects.filter(spr_master=self.spr_master).select_related(
            'item', 'item__uom_basic', 'supplier', 'account_head', 'business_group'
        )

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['spr_master'] = self.spr_master
        return context

# HTMX partial view for just the table content
class SPRDetailTablePartialView(SPRDetailListView):
    template_name = 'material_management/spr/sprdetail/_sprdetail_table.html'

# Create View for SPRDetail (provided for completeness as per prompt, not explicitly used in original ASPX)
class SPRDetailCreateView(CreateView):
    model = SPRDetail
    form_class = SPRDetailForm
    template_name = 'material_management/spr/sprdetail/_sprdetail_form.html'
    
    # We need spr_master_id to create a new detail
    def get_success_url(self):
        master_pk = self.kwargs.get('master_pk') # Assuming master_pk is passed in URL for create
        return reverse_lazy('spr_detail_list', kwargs={'master_pk': master_pk})

    def form_valid(self, form):
        master_pk = self.kwargs.get('master_pk')
        spr_master = get_object_or_404(SPRMaster, pk=master_pk)
        form.instance.spr_master = spr_master # Associate with SPRMaster
        
        response = super().form_valid(form)
        messages.success(self.request, 'SPR Detail added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, trigger client-side refresh
                headers={
                    'HX-Trigger': f'refreshSPRDetailList_{spr_master.pk}' # Unique trigger for this master
                }
            )
        return response

# Update View for SPRDetail (corresponds to "Edit" link)
class SPRDetailUpdateView(UpdateView):
    model = SPRDetail
    form_class = SPRDetailForm
    template_name = 'material_management/spr/sprdetail/_sprdetail_form.html'
    
    def get_success_url(self):
        # Redirect back to the details list of the specific SPR Master
        return reverse_lazy('spr_detail_list', kwargs={'master_pk': self.object.spr_master.pk})

    def dispatch(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.has_purchase_order():
            messages.error(self.request, 'This SPR Detail cannot be edited as it is linked to a Purchase Order.')
            # For HTMX, return a 204 with a specific header to close modal and maybe trigger alert
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': '{"showErrorModal": "This SPR Detail cannot be edited as it is linked to a Purchase Order."}'
                    }
                )
            # For non-HTMX, redirect back or show error page
            return HttpResponseRedirect(self.get_success_url())
        return super().dispatch(request, *args, **kwargs)

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'SPR Detail updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshSPRDetailList_{self.object.spr_master.pk}'
                }
            )
        return response

# Delete View for SPRDetail (provided for completeness as per prompt)
class SPRDetailDeleteView(DeleteView):
    model = SPRDetail
    template_name = 'material_management/spr/sprdetail/_sprdetail_confirm_delete.html'
    
    def get_success_url(self):
        # Redirect back to the details list of the specific SPR Master
        return reverse_lazy('spr_detail_list', kwargs={'master_pk': self.object.spr_master.pk})

    def dispatch(self, request, *args, **kwargs):
        self.object = self.get_object()
        if self.object.has_purchase_order():
            messages.error(self.request, 'This SPR Detail cannot be deleted as it is linked to a Purchase Order.')
            if request.headers.get('HX-Request'):
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Trigger': '{"showErrorModal": "This SPR Detail cannot be deleted as it is linked to a Purchase Order."}'
                    }
                )
            return HttpResponseRedirect(self.get_success_url())
        return super().dispatch(request, *args, **kwargs)

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'SPR Detail deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'refreshSPRDetailList_{self.object.spr_master.pk}'
                }
            )
        return response

```

#### 4.4 Templates

The templates are structured for HTMX partial loading, extending a base template (`core/base.html`) which would contain all common assets like Tailwind CSS, HTMX, Alpine.js, and DataTables CDNs.

**`material_management/spr/sprdetail/list.html`**

```html
{% extends 'core/base.html' %}

{% block title %}SPR - Edit Details - {{ spr_master.spr_no }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">SPR - Edit Details: <span class="text-blue-600">{{ spr_master.spr_no }}</span></h2>
        
        {# This "Add New" button is added for completeness as per prompt template structure #}
        {# The original ASP.NET page didn't show an "Add New" button for detail items, but focused on editing existing ones. #}
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-md transition duration-200 ease-in-out"
            hx-get="{% url 'spr_detail_add' master_pk=spr_master.pk %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New SPR Detail
        </button>
    </div>
    
    <div id="sprDetailTable-container"
         hx-trigger="load, refreshSPRDetailList_{{ spr_master.pk }} from:body"
         hx-get="{% url 'spr_detail_table' master_pk=spr_master.pk %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="flex items-center justify-center p-8">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="ml-3 text-lg text-gray-600">Loading SPR Details...</p>
        </div>
    </div>
    
    <div class="mt-8 text-right">
        <a href="{% url 'spr_list' %}" 
           class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition duration-150 ease-in-out">
            Cancel
        </a>
    </div>

    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 z-50 bg-gray-900 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ show: false }" 
         x-init="$watch('show', value => { if (value) document.body.classList.add('overflow-hidden'); else document.body.classList.remove('overflow-hidden') })"
         x-show="show"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90"
         _="on click if event.target.id == 'modal' remove .is-active from me then set #modal.x-data.show to false">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto"
             _="on htmx:afterSwap put #modal.x-data.show to true if #modalContent.innerHTML is not '' else put #modal.x-data.show to false
                on htmx:beforeRequest add .opacity-50 to #modalContent
                on htmx:afterRequest remove .opacity-50 from #modalContent">
            <!-- Content loaded via HTMX -->
        </div>
    </div>

    <!-- Error Modal (for business logic errors like cannot edit if PO exists) -->
    <div id="errorModal" class="fixed inset-0 z-50 bg-red-900 bg-opacity-75 flex items-center justify-center hidden"
         x-data="{ show: false, message: '' }"
         x-show="show"
         x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 scale-90" x-transition:enter-end="opacity-100 scale-100"
         x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 scale-100" x-transition:leave-end="opacity-0 scale-90"
         _="on hx-trigger from body showErrorMessage(event.detail.showErrorModal)
            on click if event.target.id == 'errorModal' set #errorModal.x-data.show to false">
        <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
            <h3 class="text-xl font-bold text-red-700 mb-4">Error</h3>
            <p class="text-gray-700" x-text="message"></p>
            <div class="mt-6 text-right">
                <button type="button" 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded"
                        _="on click set #errorModal.x-data.show to false">
                    Close
                </button>
            </div>
        </div>
    </div>

</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('errorModalHandler', () => ({
            show: false,
            message: '',
            showErrorMessage(msg) {
                this.message = msg;
                this.show = true;
            },
        }));
    });

    // Function to handle global error messages from HX-Trigger
    document.body.addEventListener('htmx:trigger', function(event) {
        if (event.detail && event.detail.showErrorModal) {
            document.querySelector('#errorModal')._x_dataStack[0].showErrorMessage(event.detail.showErrorModal);
        }
    });

    // Initialize DataTables after content is loaded via HTMX
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'sprDetailTable-container') {
            $('#sprDetailTable').DataTable({
                "pageLength": 17, // Matching original PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "autoWidth": false, // Prevent DataTables from calculating column widths
                "columnDefs": [
                    { "orderable": false, "targets": 0 }, // Disable sorting for SN
                    { "orderable": false, "targets": -1 } // Disable sorting for Actions
                ]
            });
        }
    });
</script>
{% endblock %}
```

**`material_management/spr/sprdetail/_sprdetail_table.html`** (Partial for HTMX)

```html
<div class="overflow-x-auto rounded-lg shadow-md border border-gray-200">
    <table id="sprDetailTable" class="min-w-full divide-y divide-gray-200 bg-white">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">SN</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10"></th> {# For Edit/PO Status #}
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">SPR No.</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">Item Code</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider w-2/10">Description</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">UOM</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider w-2/10">Supplier</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">A/c Head</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">WO No</th>
                <th class="py-3 px-4 text-center text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">BG Group</th>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">Qty</th>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">Rate</th>
                <th class="py-3 px-4 text-right text-xs font-semibold text-gray-500 uppercase tracking-wider w-1/10">Dis</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-500 uppercase tracking-wider w-2/10">Remarks</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for detail in spr_details %}
            <tr>
                <td class="py-2 px-4 text-right align-top text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-2 px-4 text-center align-top text-sm">
                    {% if detail.has_purchase_order %}
                        <span class="text-red-600 font-semibold">PO</span>
                    {% else %}
                        <button 
                            class="text-blue-600 hover:text-blue-900 font-medium py-1 px-2 rounded hover:underline"
                            hx-get="{% url 'spr_detail_edit' pk=detail.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                    {% endif %}
                </td>
                <td class="py-2 px-4 text-center align-top text-sm text-gray-700">{{ detail.spr_master.spr_no }}</td>
                <td class="py-2 px-4 text-left align-top text-sm text-gray-700">{{ detail.get_display_item_code }}</td>
                <td class="py-2 px-4 text-left align-top text-sm text-gray-700">{{ detail.get_display_description }}</td>
                <td class="py-2 px-4 text-center align-top text-sm text-gray-700">{{ detail.get_display_uom }}</td>
                <td class="py-2 px-4 text-left align-top text-sm text-gray-700">{{ detail.get_display_supplier_name }}</td>
                <td class="py-2 px-4 text-center align-top text-sm text-gray-700">{{ detail.get_display_account_head }}</td>
                <td class="py-2 px-4 text-center align-top text-sm text-gray-700">{{ detail.get_display_wo_no }}</td>
                <td class="py-2 px-4 text-center align-top text-sm text-gray-700">{{ detail.get_display_business_group }}</td>
                <td class="py-2 px-4 text-right align-top text-sm text-gray-700">{{ detail.qty|floatformat:"3g" }}</td>
                <td class="py-2 px-4 text-right align-top text-sm text-gray-700">{{ detail.rate|floatformat:"2g" }}</td>
                <td class="py-2 px-4 text-right align-top text-sm text-gray-700">{{ detail.discount }}</td>
                <td class="py-2 px-4 text-left align-top text-sm text-gray-700">{{ detail.remarks }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="14" class="py-4 px-6 text-center text-lg text-red-500 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

{# DataTables initialization happens globally in list.html after HTMX swap #}
```

**`material_management/spr/sprdetail/_sprdetail_form.html`** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} SPR Detail</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
            {% for field in form %}
            <div class="{% if field.field.widget.input_type == 'textarea' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %} <span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal then set #modal.x-data.show to false">
                Cancel
            </button>
            <button 
                type="submit" 
                class="inline-flex justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save
            </button>
        </div>
    </form>
</div>
```

**`material_management/spr/sprdetail/_sprdetail_confirm_delete.html`** (Partial for HTMX)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the SPR Detail for item "<span class="font-medium">{{ sprdetail.get_display_item_code }} - {{ sprdetail.get_display_description }}</span>"? This action cannot be undone.</p>
    
    <div class="flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            _="on click remove .is-active from #modal then set #modal.x-data.show to false">
            Cancel
        </button>
        <button 
            hx-post="{% url 'spr_detail_delete' pk=sprdetail.pk %}" 
            hx-swap="none"
            class="inline-flex justify-center px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
            Delete
        </button>
    </div>
</div>
```

#### 4.5 URLs (`material_management/spr/urls.py`)

These URL patterns define the routes for accessing the SPR Detail list and its associated CRUD operations.

```python
from django.urls import path
from .views import (
    SPRDetailListView, 
    SPRDetailTablePartialView, 
    SPRDetailCreateView, 
    SPRDetailUpdateView, 
    SPRDetailDeleteView
)

urlpatterns = [
    # Main list view for SPR details, filtered by SPR Master ID
    path('spr/<int:master_pk>/details/', SPRDetailListView.as_view(), name='spr_detail_list'),
    # HTMX endpoint for refreshing only the table content
    path('spr/<int:master_pk>/details/table/', SPRDetailTablePartialView.as_view(), name='spr_detail_table'),
    
    # CRUD operations for individual SPR Details (used by modals)
    # Note: Create is also contextualized by master_pk for linking new details
    path('spr/<int:master_pk>/details/add/', SPRDetailCreateView.as_view(), name='spr_detail_add'),
    path('spr/details/edit/<int:pk>/', SPRDetailUpdateView.as_view(), name='spr_detail_edit'),
    path('spr/details/delete/<int:pk>/', SPRDetailDeleteView.as_view(), name='spr_detail_delete'),
    
    # Placeholder for a top-level SPR list, which 'btncancel' might point to
    # This would reside in a higher-level urls.py (e.g., material_management/urls.py or project urls.py)
    # For now, let's assume it's part of the same app for demonstration.
    path('spr/', SPRDetailListView.as_view(), {'master_pk': 1}, name='spr_list'), # Example: points to master_pk=1 for a general SPR list
]
```
*(Note: The `spr_list` URL is a placeholder. In a real application, `SPR_Edit.aspx` would likely map to a `SPRMasterListView` that displays a list of `SPRMaster` records, and clicking on one would navigate to `spr/<int:master_pk>/details/`.)*

#### 4.6 Tests (`material_management/spr/tests.py`)

Comprehensive tests for models (unit tests) and views (integration tests) to ensure functionality and business logic are correctly implemented.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection # For checking database table existence
from .models import (
    Company, SPRMaster, ItemMaster, Unit, Supplier, AccountHead, BusinessGroup,
    POMaster, PODetail, SPRDetail
)

class ModelCreationTest(TestCase):
    def test_company_creation(self):
        company = Company.objects.create(id=1, name='Test Company')
        self.assertEqual(company.name, 'Test Company')
        self.assertEqual(Company.objects.count(), 1)

    def test_unit_creation(self):
        unit = Unit.objects.create(id=1, symbol='Kg')
        self.assertEqual(unit.symbol, 'Kg')

    def test_item_master_creation(self):
        unit = Unit.objects.create(id=1, symbol='Kg')
        item = ItemMaster.objects.create(id=1, item_code='ITEM001', description='Test Item', uom_basic=unit)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.uom_basic.symbol, 'Kg')

    def test_supplier_creation(self):
        supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        self.assertEqual(supplier.supplier_name, 'Test Supplier')

    def test_account_head_creation(self):
        acc_head = AccountHead.objects.create(id=1, symbol='RAW_MAT')
        self.assertEqual(acc_head.symbol, 'RAW_MAT')

    def test_business_group_creation(self):
        bg = BusinessGroup.objects.create(id=1, symbol='PROD')
        self.assertEqual(bg.symbol, 'PROD')

    def test_spr_master_creation(self):
        company = Company.objects.create(id=1, name='Test Company')
        spr_master = SPRMaster.objects.create(id=101, spr_no='SPR-001', company=company)
        self.assertEqual(spr_master.spr_no, 'SPR-001')
        self.assertEqual(spr_master.company.name, 'Test Company')

    def test_spr_detail_creation(self):
        company = Company.objects.create(id=1, name='Test Company')
        spr_master = SPRMaster.objects.create(id=101, spr_no='SPR-001', company=company)
        unit = Unit.objects.create(id=1, symbol='Kg')
        item = ItemMaster.objects.create(id=1, item_code='ITEM001', description='Test Item', uom_basic=unit)
        supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        acc_head = AccountHead.objects.create(id=1, symbol='RAW_MAT')
        bg = BusinessGroup.objects.create(id=1, symbol='PROD')

        spr_detail = SPRDetail.objects.create(
            id=1001,
            spr_master=spr_master,
            item=item,
            qty=10.5,
            rate=100.25,
            supplier=supplier,
            account_head=acc_head,
            wo_no='WO123',
            business_group=bg,
            discount='5%',
            remarks='Test remarks'
        )
        self.assertEqual(spr_detail.id, 1001)
        self.assertEqual(spr_detail.item.item_code, 'ITEM001')
        self.assertEqual(spr_detail.get_display_uom(), 'Kg')

    def test_po_master_creation(self):
        company = Company.objects.create(id=1, name='Test Company')
        po_master = POMaster.objects.create(id=201, po_no='PO-001', company=company)
        self.assertEqual(po_master.po_no, 'PO-001')

    def test_po_detail_creation(self):
        company = Company.objects.create(id=1, name='Test Company')
        spr_master = SPRMaster.objects.create(id=101, spr_no='SPR-001', company=company)
        unit = Unit.objects.create(id=1, symbol='Kg')
        item = ItemMaster.objects.create(id=1, item_code='ITEM001', description='Test Item', uom_basic=unit)
        supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        acc_head = AccountHead.objects.create(id=1, symbol='RAW_MAT')
        bg = BusinessGroup.objects.create(id=1, symbol='PROD')
        spr_detail = SPRDetail.objects.create(
            id=1001, spr_master=spr_master, item=item, qty=10.5, rate=100.25,
            supplier=supplier, account_head=acc_head, business_group=bg
        )
        po_master = POMaster.objects.create(id=201, po_no='PO-001', company=company)
        po_detail = PODetail.objects.create(id=3001, spr_detail=spr_detail, po_master=po_master)
        self.assertEqual(po_detail.spr_detail.id, 1001)
        self.assertEqual(po_detail.po_master.po_no, 'PO-001')

class SPRDetailModelLogicTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common data
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.spr_master = SPRMaster.objects.create(id=101, spr_no='SPR-001', company=cls.company)
        cls.unit = Unit.objects.create(id=1, symbol='Kg')
        cls.item = ItemMaster.objects.create(id=1, item_code='ITEM001', description='Test Item', uom_basic=cls.unit)
        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        cls.acc_head = AccountHead.objects.create(id=1, symbol='RAW_MAT')
        cls.bg = BusinessGroup.objects.create(id=1, symbol='PROD')

        # Create an editable SPR Detail
        cls.spr_detail_editable = SPRDetail.objects.create(
            id=1001,
            spr_master=cls.spr_master,
            item=cls.item,
            qty=10.5,
            rate=100.25,
            supplier=cls.supplier,
            account_head=cls.acc_head,
            wo_no='WO123',
            business_group=cls.bg,
            discount='5%',
            remarks='Test remarks'
        )

        # Create a non-editable SPR Detail (linked to a PO)
        cls.spr_detail_non_editable = SPRDetail.objects.create(
            id=1002,
            spr_master=cls.spr_master,
            item=cls.item,
            qty=5.0,
            rate=50.00,
            supplier=cls.supplier,
            account_head=cls.acc_head,
            business_group=cls.bg
        )
        cls.po_master = POMaster.objects.create(id=201, po_no='PO-001', company=cls.company)
        PODetail.objects.create(id=3001, spr_detail=cls.spr_detail_non_editable, po_master=cls.po_master)

    def test_has_purchase_order_true(self):
        self.assertTrue(self.spr_detail_non_editable.has_purchase_order())

    def test_has_purchase_order_false(self):
        self.assertFalse(self.spr_detail_editable.has_purchase_order())

    def test_get_display_methods(self):
        self.assertEqual(self.spr_detail_editable.get_display_item_code(), 'ITEM001')
        self.assertEqual(self.spr_detail_editable.get_display_description(), 'Test Item')
        self.assertEqual(self.spr_detail_editable.get_display_uom(), 'Kg')
        self.assertEqual(self.spr_detail_editable.get_display_supplier_name(), 'Test Supplier')
        self.assertEqual(self.spr_detail_editable.get_display_account_head(), 'RAW_MAT')
        self.assertEqual(self.spr_detail_editable.get_display_business_group(), 'PROD')
        self.assertEqual(self.spr_detail_editable.get_display_wo_no(), 'WO123')
        
        # Test WO_No for empty/None
        self.spr_detail_editable.wo_no = ''
        self.assertEqual(self.spr_detail_editable.get_display_wo_no(), 'NA')
        self.spr_detail_editable.wo_no = None
        self.assertEqual(self.spr_detail_editable.get_display_wo_no(), 'NA')


class SPRDetailViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup common data
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.spr_master = SPRMaster.objects.create(id=101, spr_no='SPR-001', company=cls.company)
        cls.unit = Unit.objects.create(id=1, symbol='Kg')
        cls.item = ItemMaster.objects.create(id=1, item_code='ITEM001', description='Test Item', uom_basic=cls.unit)
        cls.supplier = Supplier.objects.create(supplier_id=1, supplier_name='Test Supplier')
        cls.acc_head = AccountHead.objects.create(id=1, symbol='RAW_MAT')
        cls.bg = BusinessGroup.objects.create(id=1, symbol='PROD')

        cls.spr_detail_editable = SPRDetail.objects.create(
            id=1001, spr_master=cls.spr_master, item=cls.item, qty=10.5, rate=100.25,
            supplier=cls.supplier, account_head=cls.acc_head, business_group=cls.bg
        )
        cls.spr_detail_non_editable = SPRDetail.objects.create(
            id=1002, spr_master=cls.spr_master, item=cls.item, qty=5.0, rate=50.00,
            supplier=cls.supplier, account_head=cls.acc_head, business_group=cls.bg
        )
        cls.po_master = POMaster.objects.create(id=201, po_no='PO-001', company=cls.company)
        PODetail.objects.create(id=3001, spr_detail=cls.spr_detail_non_editable, po_master=cls.po_master)

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('spr_detail_list', args=[self.spr_master.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/sprdetail/list.html')
        self.assertIn('spr_details', response.context)
        self.assertEqual(response.context['spr_master'], self.spr_master)
        self.assertContains(response, self.spr_detail_editable.item.item_code)
        self.assertContains(response, 'Edit') # Should see edit button for editable item
        self.assertContains(response, 'PO') # Should see PO label for non-editable item

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('spr_detail_table', args=[self.spr_master.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/sprdetail/_sprdetail_table.html')
        self.assertIn('spr_details', response.context)
        self.assertContains(response, self.spr_detail_editable.item.item_code)

    def test_create_view_get(self):
        response = self.client.get(reverse('spr_detail_add', args=[self.spr_master.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/sprdetail/_sprdetail_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        initial_count = SPRDetail.objects.count()
        data = {
            'item': self.item.pk,
            'qty': 20.0,
            'rate': 200.0,
            'supplier': self.supplier.pk,
            'account_head': self.acc_head.pk,
            'wo_no': 'WO456',
            'business_group': self.bg.pk,
            'discount': '10%',
            'remarks': 'New item remarks'
        }
        response = self.client.post(reverse('spr_detail_add', args=[self.spr_master.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(SPRDetail.objects.count(), initial_count + 1)
        self.assertTrue(SPRDetail.objects.filter(qty=20.0, rate=200.0).exists())
        self.assertIn(f'refreshSPRDetailList_{self.spr_master.pk}', response.headers['HX-Trigger'])

    def test_update_view_get_editable(self):
        response = self.client.get(reverse('spr_detail_edit', args=[self.spr_detail_editable.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/sprdetail/_sprdetail_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.spr_detail_editable)

    def test_update_view_post_editable(self):
        new_qty = 15.0
        data = {
            'item': self.item.pk,
            'qty': new_qty,
            'rate': self.spr_detail_editable.rate,
            'supplier': self.supplier.pk,
            'account_head': self.acc_head.pk,
            'wo_no': self.spr_detail_editable.wo_no,
            'business_group': self.bg.pk,
            'discount': self.spr_detail_editable.discount,
            'remarks': 'Updated remarks'
        }
        response = self.client.post(reverse('spr_detail_edit', args=[self.spr_detail_editable.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.spr_detail_editable.refresh_from_db()
        self.assertEqual(self.spr_detail_editable.qty, new_qty)
        self.assertIn(f'refreshSPRDetailList_{self.spr_master.pk}', response.headers['HX-Trigger'])

    def test_update_view_get_non_editable(self):
        response = self.client.get(reverse('spr_detail_edit', args=[self.spr_detail_non_editable.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success with no content, but trigger error
        self.assertIn('showErrorModal', response.headers['HX-Trigger'])
        self.assertIn('This SPR Detail cannot be edited', response.headers['HX-Trigger'])

    def test_delete_view_get_editable(self):
        response = self.client.get(reverse('spr_detail_delete', args=[self.spr_detail_editable.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr/sprdetail/_sprdetail_confirm_delete.html')
        self.assertIn('sprdetail', response.context)

    def test_delete_view_post_editable(self):
        initial_count = SPRDetail.objects.count()
        response = self.client.post(reverse('spr_detail_delete', args=[self.spr_detail_editable.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertEqual(SPRDetail.objects.count(), initial_count - 1)
        self.assertFalse(SPRDetail.objects.filter(pk=self.spr_detail_editable.pk).exists())
        self.assertIn(f'refreshSPRDetailList_{self.spr_master.pk}', response.headers['HX-Trigger'])

    def test_delete_view_get_non_editable(self):
        response = self.client.get(reverse('spr_detail_delete', args=[self.spr_detail_non_editable.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('showErrorModal', response.headers['HX-Trigger'])
        self.assertIn('This SPR Detail cannot be deleted', response.headers['HX-Trigger'])

    def test_delete_view_post_non_editable(self):
        initial_count = SPRDetail.objects.count()
        response = self.client.post(reverse('spr_detail_delete', args=[self.spr_detail_non_editable.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(SPRDetail.objects.count(), initial_count) # Should not delete
        self.assertTrue(SPRDetail.objects.filter(pk=self.spr_detail_non_editable.pk).exists())
        self.assertIn('showErrorModal', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

The provided templates and views are designed to fully leverage HTMX and Alpine.js:

*   **HTMX for dynamic updates:**
    *   The `sprDetailTable-container` `div` uses `hx-trigger="load, refreshSPRDetailList_{{ spr_master.pk }} from:body"` and `hx-get="{% url 'spr_detail_table' master_pk=spr_master.pk %}" hx-swap="innerHTML"` to load the table content dynamically on page load and whenever a specific `HX-Trigger` event is fired (e.g., after a successful form submission).
    *   "Edit" and "Delete" buttons in the table use `hx-get` to fetch the form/confirmation partials into the modal (`#modalContent`).
    *   Form submissions (`hx-post` on the form) are configured with `hx-swap="none"` to prevent UI changes on submission, relying on the `HX-Trigger` header from the Django view to refresh the list and close the modal.
    *   The `HX-Trigger` headers (`refreshSPRDetailList_{{ pk }}`) ensure that only the relevant table is refreshed after a CRUD operation, improving efficiency.
    *   Error handling for non-editable items uses `HX-Trigger` to fire a custom event (`showErrorModal`) that Alpine.js can pick up and display an error message.

*   **Alpine.js for UI state management:**
    *   The main modal (`#modal`) uses `x-data="{ show: false }"` and `x-show="show"` to control its visibility. HTMX callbacks (`on htmx:afterSwap` etc.) are used to update this Alpine state (`set #modal.x-data.show to true/false`), ensuring the modal opens and closes correctly.
    *   A dedicated error modal also uses Alpine.js (`x-data="{ show: false, message: '' }"`) to display error messages triggered by `HX-Trigger`.

*   **DataTables for list views:**
    *   The `_sprdetail_table.html` partial contains a standard `<table>` tag with `id="sprDetailTable"`.
    *   A JavaScript block in `list.html` (within `{% block extra_js %}`) ensures that DataTables is initialized (`$('#sprDetailTable').DataTable(...)`) *after* the table content has been loaded into the DOM by HTMX (`htmx:afterSwap` event listener), providing client-side search, sorting, and pagination.

*   **No custom JavaScript requirements:** All dynamic behavior is handled by HTMX and Alpine.js attributes, adhering to the "no additional JavaScript" preference.

### Final Notes

*   This plan provides a complete framework for migrating the "SPR - Edit Details" functionality to Django.
*   The `managed = False` setting in Django models is crucial; it tells Django that the database tables already exist and it should not attempt to create or modify them during migrations. You would typically use `inspectdb` to initially generate these models from your existing database.
*   Authentication and authorization (e.g., `Session["username"]`, `Session["compid"]`) would be handled by Django's built-in authentication system and middleware, likely using `request.user` and custom permissions or filtering based on `request.user.company` (if your user model is extended).
*   The `clsFunctions fun = new clsFunctions();` and `fun.Connection()` aspects are replaced by Django's ORM and its `managed = False` setup directly connecting to the database.
*   The current conversion assumes a one-to-one mapping of original ASP.NET pages/logic to Django views. A full modernization might involve re-evaluating the overall user flow and combining some functionalities into single, more comprehensive Django views.
*   Remember to replace placeholders like `[APP_NAME]` (e.g., `material_management`) and `[MODEL_NAME_LOWER]` (e.g., `sprdetail`) in the `urls.py` and template paths.
*   Ensure that DataTables, HTMX, Alpine.js, and Tailwind CSS are correctly linked in your `core/base.html` for the frontend to function as intended.