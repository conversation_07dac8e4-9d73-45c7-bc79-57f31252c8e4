## ASP.NET to Django Conversion Script:

This modernization plan outlines the strategy to transition the `PO_Authorize_Dashboard` from ASP.NET to a modern Django 5.0+ application. Since the provided ASP.NET code is a placeholder, this plan will infer typical dashboard functionalities and database structures to create a complete, actionable Django solution.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
The provided ASP.NET code for `PO_Authorize_Dashboard.aspx` and its code-behind `PO_Authorize_Dashboard.aspx.cs` is a minimal template, lacking any explicit database interactions, UI elements, or business logic. Based on the page name "PO_Authorize_Dashboard" (Purchase Order Authorization Dashboard), we infer a common database structure for tracking and managing purchase order authorizations.

**Inferred Database Schema:**

*   **Table Name:** `PO_Authorization` (assuming an existing legacy table structure)
*   **Model Name:** `PurchaseOrderAuthorization`
*   **Columns:**
    *   `id` (Primary Key, Auto-incrementing)
    *   `po_number` (e.g., `VARCHAR(50)`) - Unique identifier for the Purchase Order.
    *   `vendor_name` (e.g., `VARCHAR(255)`) - Name of the vendor.
    *   `amount` (e.g., `DECIMAL(18, 2)`) - Total amount of the Purchase Order.
    *   `status` (e.g., `VARCHAR(20)`) - Current status (e.g., 'Pending', 'Authorized', 'Rejected').
    *   `requested_by` (e.g., `VARCHAR(100)`) - User who requested the PO.
    *   `request_date` (e.g., `DATETIME`) - Date the PO was requested.
    *   `authorized_by` (e.g., `VARCHAR(100)`, Nullable) - User who authorized/rejected the PO.
    *   `authorization_date` (e.g., `DATETIME`, Nullable) - Date of authorization/rejection.
    *   `comments` (e.g., `NVARCHAR(MAX)`, Nullable) - Any comments related to authorization.

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Given the dashboard nature, the primary functionality will be **Read (R)**, allowing users to view a list of purchase orders requiring authorization. The "Authorize" part implies **Update (U)** functionality, where the status of a PO changes. We will also include **Create (C)** for initiating new authorization requests and **Delete (D)** for canceling or removing requests, for a complete CRUD implementation typical of enterprise applications.

*   **Create:** Functionality to submit a new Purchase Order authorization request.
*   **Read:** Displaying a list of Purchase Order authorizations, with filtering, searching, and sorting. This will be the core dashboard view.
*   **Update:** Changing the status of a Purchase Order (e.g., from 'Pending' to 'Authorized' or 'Rejected'). This could involve a detailed view or an inline action.
*   **Delete:** Removing a Purchase Order authorization request from the system.
*   **Validation Logic:** Simple field validation will be implemented at the form level in Django.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Although no explicit UI controls are present in the provided ASP.NET markup, a "Dashboard" typically features:

*   **Data Grid/List:** Equivalent to a `GridView` in ASP.NET, displaying a tabular list of Purchase Order Authorizations. This will be replaced by a Django `ListView` rendering a DataTables-enabled HTML table.
*   **Action Buttons:** Buttons for "Add New Purchase Order Authorization", "Edit/View Details", "Authorize", "Reject", and "Delete" for each row. These will trigger HTMX requests.
*   **Forms:** Input forms for adding or editing Purchase Order details and status. These will be loaded dynamically into a modal using HTMX.
*   **Search/Filter:** Input fields for filtering the list based on status, PO number, vendor, etc., integrated with DataTables.
*   **Modals:** Used for displaying "Add", "Edit", and "Delete" forms to provide a seamless user experience without full page reloads, managed with Alpine.js and HTMX.

### Step 4: Generate Django Code

We will create a new Django app named `material_management` (inferred from the ASP.NET namespace `Module_MaterialManagement`).

#### 4.1 Models

**Task:** Create a Django model based on the database schema.

**Instructions:**
The `PurchaseOrderAuthorization` model maps directly to the `PO_Authorization` table.

```python
# material_management/models.py
from django.db import models
from django.utils import timezone

class PurchaseOrderAuthorization(models.Model):
    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Authorized', 'Authorized'),
        ('Rejected', 'Rejected'),
    ]

    po_number = models.CharField(db_column='PO_Number', max_length=50, unique=True, verbose_name="PO Number")
    vendor_name = models.CharField(db_column='Vendor_Name', max_length=255, verbose_name="Vendor Name")
    amount = models.DecimalField(db_column='Amount', max_digits=18, decimal_places=2, verbose_name="Amount")
    status = models.CharField(db_column='Status', max_length=20, choices=STATUS_CHOICES, default='Pending', verbose_name="Status")
    requested_by = models.CharField(db_column='Requested_By', max_length=100, verbose_name="Requested By")
    request_date = models.DateTimeField(db_column='Request_Date', default=timezone.now, verbose_name="Request Date")
    authorized_by = models.CharField(db_column='Authorized_By', max_length=100, null=True, blank=True, verbose_name="Authorized By")
    authorization_date = models.DateTimeField(db_column='Authorization_Date', null=True, blank=True, verbose_name="Authorization Date")
    comments = models.TextField(db_column='Comments', null=True, blank=True, verbose_name="Comments")

    class Meta:
        managed = False  # Set to True if Django manages this table, False if it's an existing legacy table
        db_table = 'PO_Authorization'
        verbose_name = 'Purchase Order Authorization'
        verbose_name_plural = 'Purchase Order Authorizations'
        ordering = ['-request_date'] # Order by latest requests first

    def __str__(self):
        return f"PO: {self.po_number} - Vendor: {self.vendor_name} ({self.status})"

    def authorize(self, user_name):
        """Authorizes the purchase order."""
        if self.status == 'Pending':
            self.status = 'Authorized'
            self.authorized_by = user_name
            self.authorization_date = timezone.now()
            self.save()
            return True
        return False

    def reject(self, user_name, comments=None):
        """Rejects the purchase order."""
        if self.status == 'Pending':
            self.status = 'Rejected'
            self.authorized_by = user_name
            self.authorization_date = timezone.now()
            self.comments = comments
            self.save()
            return True
        return False

    def is_pending(self):
        """Checks if the PO is pending authorization."""
        return self.status == 'Pending'
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` for `PurchaseOrderAuthorization` will handle creation and updates.

```python
# material_management/forms.py
from django import forms
from .models import PurchaseOrderAuthorization

class PurchaseOrderAuthorizationForm(forms.ModelForm):
    class Meta:
        model = PurchaseOrderAuthorization
        fields = ['po_number', 'vendor_name', 'amount', 'status', 'requested_by', 'request_date', 'comments']
        widgets = {
            'po_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'vendor_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'requested_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'request_date': forms.DateTimeInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'datetime-local'}),
            'comments': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
    
    def clean_amount(self):
        amount = self.cleaned_data['amount']
        if amount <= 0:
            raise forms.ValidationError("Amount must be positive.")
        return amount

    # Example of a custom authorization/rejection form that only allows status change for pending
    class AuthorizationForm(forms.ModelForm):
        class Meta:
            model = PurchaseOrderAuthorization
            fields = ['status', 'comments']
            widgets = {
                'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
                'comments': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            }

        def clean_status(self):
            status = self.cleaned_data['status']
            if self.instance.status != 'Pending' and status != self.instance.status:
                raise forms.ValidationError(f"Cannot change status from {self.instance.status}. Only Pending items can be authorized or rejected.")
            return status

        def save(self, commit=True, user=None):
            instance = super().save(commit=False)
            if user:
                if instance.status == 'Authorized':
                    instance.authorize(user.username)
                elif instance.status == 'Rejected':
                    instance.reject(user.username, instance.comments)
            if commit:
                instance.save()
            return instance
```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs.

**Instructions:**
Views will be kept thin, delegating business logic to the `PurchaseOrderAuthorization` model.

```python
# material_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from django.db import transaction

from .models import PurchaseOrderAuthorization
from .forms import PurchaseOrderAuthorizationForm, PurchaseOrderAuthorizationForm # Adjusted for custom authorization form

class PurchaseOrderAuthorizationListView(ListView):
    model = PurchaseOrderAuthorization
    template_name = 'material_management/purchaseorderauthorization/list.html'
    context_object_name = 'purchase_order_authorizations'

class PurchaseOrderAuthorizationTablePartialView(ListView):
    model = PurchaseOrderAuthorization
    template_name = 'material_management/purchaseorderauthorization/_purchaseorderauthorization_table.html'
    context_object_name = 'purchase_order_authorizations'

class PurchaseOrderAuthorizationCreateView(CreateView):
    model = PurchaseOrderAuthorization
    form_class = PurchaseOrderAuthorizationForm
    template_name = 'material_management/purchaseorderauthorization/_purchaseorderauthorization_form.html'
    success_url = reverse_lazy('purchaseorderauthorization_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Authorization request added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderAuthorizationList'
                }
            )
        return response

class PurchaseOrderAuthorizationUpdateView(UpdateView):
    model = PurchaseOrderAuthorization
    form_class = PurchaseOrderAuthorizationForm
    template_name = 'material_management/purchaseorderauthorization/_purchaseorderauthorization_form.html'
    success_url = reverse_lazy('purchaseorderauthorization_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Authorization updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderAuthorizationList'
                }
            )
        return response

class PurchaseOrderAuthorizationDeleteView(DeleteView):
    model = PurchaseOrderAuthorization
    template_name = 'material_management/purchaseorderauthorization/_purchaseorderauthorization_confirm_delete.html'
    success_url = reverse_lazy('purchaseorderauthorization_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Order Authorization deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderAuthorizationList'
                }
            )
        return response

class PurchaseOrderAuthorizationActionView(UpdateView):
    model = PurchaseOrderAuthorization
    form_class = PurchaseOrderAuthorizationForm.AuthorizationForm # Using the specific authorization form
    template_name = 'material_management/purchaseorderauthorization/_purchaseorderauthorization_action_form.html' # A specific form for auth/reject
    success_url = reverse_lazy('purchaseorderauthorization_list')

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        # Pass the current user to the form's save method for authorization/rejection
        kwargs['user'] = self.request.user if self.request.user.is_authenticated else None
        return kwargs

    def form_valid(self, form):
        action_type = form.cleaned_data['status'] # Get the selected status from the form
        if action_type == 'Authorized':
            self.object.authorize(self.request.user.username if self.request.user.is_authenticated else 'System')
            messages.success(self.request, f'Purchase Order {self.object.po_number} authorized successfully.')
        elif action_type == 'Rejected':
            self.object.reject(self.request.user.username if self.request.user.is_authenticated else 'System', form.cleaned_data.get('comments'))
            messages.warning(self.request, f'Purchase Order {self.object.po_number} rejected.')
        else:
            # If status is still 'Pending' or invalid, it means no action was taken
            messages.info(self.request, f'No change for Purchase Order {self.object.po_number}.')

        response = super().form_valid(form) # This will save the changes if form.save() was called without commit=False earlier

        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderAuthorizationList'
                }
            )
        return response

    def get_initial(self):
        initial = super().get_initial()
        # Pre-fill status to current object's status in the form
        initial['status'] = self.object.status
        initial['comments'] = self.object.comments
        return initial

```

#### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**
Templates will implement DataTables, HTMX for dynamic content, and Alpine.js for UI state.

**`material_management/purchaseorderauthorization/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Order Authorizations</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'purchaseorderauthorization_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Authorization
        </button>
    </div>

    <div id="purchaseorderauthorizationTable-container"
         hx-trigger="load, refreshPurchaseOrderAuthorizationList from:body"
         hx-get="{% url 'purchaseorderauthorization_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-md rounded-lg overflow-hidden">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Purchase Order Authorizations...</p>
        </div>
    </div>

    <!-- Modal for forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8 relative">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}
```

**`material_management/purchaseorderauthorization/_purchaseorderauthorization_table.html`**

```html
<div class="p-6">
    <table id="purchaseorderauthorizationTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Vendor Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requested By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in purchase_order_authorizations %}
            <tr>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.po_number }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.vendor_name }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">${{ obj.amount|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                    {% if obj.status == 'Pending' %}bg-yellow-100 text-yellow-800
                    {% elif obj.status == 'Authorized' %}bg-green-100 text-green-800
                    {% elif obj.status == 'Rejected' %}bg-red-100 text-red-800
                    {% endif %}">
                        {{ obj.status }}
                    </span>
                </td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.requested_by }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ obj.request_date|date:"Y-m-d H:i" }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out mr-1"
                        hx-get="{% url 'purchaseorderauthorization_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    {% if obj.is_pending %}
                    <button
                        class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out mr-1"
                        hx-get="{% url 'purchaseorderauthorization_action' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Action
                    </button>
                    {% endif %}
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'purchaseorderauthorization_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Ensure DataTables is loaded and initialized only once per HTMX swap
    if ($.fn.DataTable.isDataTable('#purchaseorderauthorizationTable')) {
        $('#purchaseorderauthorizationTable').DataTable().destroy();
    }
    $(document).ready(function() {
        $('#purchaseorderauthorizationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "lengthMenu": "Show _MENU_ entries",
                "search": "Search:",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "infoEmpty": "Showing 0 to 0 of 0 entries",
                "infoFiltered": "(filtered from _MAX_ total entries)",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    });
</script>
```

**`material_management/purchaseorderauthorization/_purchaseorderauthorization_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Purchase Order Authorization</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}

        {% for field in form %}
        <div>
            <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                {{ field.label }}
                {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
            </label>
            {{ field }}
            {% if field.help_text %}
            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
            {% endif %}
            {% if field.errors %}
            <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
            {% endif %}
        </div>
        {% endfor %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Save Authorization
            </button>
        </div>
    </form>
</div>
```

**`material_management/purchaseorderauthorization/_purchaseorderauthorization_action_form.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Action Purchase Order: {{ object.po_number }}</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" class="space-y-5">
        {% csrf_token %}

        <div class="text-gray-700 mb-4">
            <p><strong>PO Number:</strong> {{ object.po_number }}</p>
            <p><strong>Vendor:</strong> {{ object.vendor_name }}</p>
            <p><strong>Amount:</strong> ${{ object.amount|floatformat:2 }}</p>
            <p><strong>Current Status:</strong> <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
            {% if object.status == 'Pending' %}bg-yellow-100 text-yellow-800
            {% elif object.status == 'Authorized' %}bg-green-100 text-green-800
            {% elif object.status == 'Rejected' %}bg-red-100 text-red-800
            {% endif %}">
                {{ object.status }}
            </span></p>
        </div>

        {% for field in form %}
            {% if field.name == 'status' or field.name == 'comments' %}
            <div>
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-600 text-sm mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endif %}
        {% endfor %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                Submit Action
            </button>
        </div>
    </form>
</div>
```

**`material_management/purchaseorderauthorization/_purchaseorderauthorization_confirm_delete.html`**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the Purchase Order Authorization for <strong>{{ object.po_number }} - {{ object.vendor_name }}</strong>?</p>
    <form hx-post="{% url 'purchaseorderauthorization_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button
                type="button"
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are defined for the main list view, CRUD operations, and the HTMX table partial.

```python
# material_management/urls.py
from django.urls import path
from .views import (
    PurchaseOrderAuthorizationListView,
    PurchaseOrderAuthorizationCreateView,
    PurchaseOrderAuthorizationUpdateView,
    PurchaseOrderAuthorizationDeleteView,
    PurchaseOrderAuthorizationTablePartialView,
    PurchaseOrderAuthorizationActionView, # For authorize/reject
)

urlpatterns = [
    path('po-authorizations/', PurchaseOrderAuthorizationListView.as_view(), name='purchaseorderauthorization_list'),
    path('po-authorizations/table/', PurchaseOrderAuthorizationTablePartialView.as_view(), name='purchaseorderauthorization_table'),
    path('po-authorizations/add/', PurchaseOrderAuthorizationCreateView.as_view(), name='purchaseorderauthorization_add'),
    path('po-authorizations/edit/<int:pk>/', PurchaseOrderAuthorizationUpdateView.as_view(), name='purchaseorderauthorization_edit'),
    path('po-authorizations/delete/<int:pk>/', PurchaseOrderAuthorizationDeleteView.as_view(), name='purchaseorderauthorization_delete'),
    path('po-authorizations/action/<int:pk>/', PurchaseOrderAuthorizationActionView.as_view(), name='purchaseorderauthorization_action'), # For authorize/reject actions
]
```

#### 4.6 Tests

**Task:** Write tests for the model and views.

**Instructions:**
Comprehensive unit tests for model methods and integration tests for all views.

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import PurchaseOrderAuthorization

class PurchaseOrderAuthorizationModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.po_auth_pending = PurchaseOrderAuthorization.objects.create(
            po_number='PO-2023-001',
            vendor_name='Vendor A',
            amount=1000.00,
            status='Pending',
            requested_by='John Doe',
            request_date=timezone.now(),
        )
        cls.po_auth_authorized = PurchaseOrderAuthorization.objects.create(
            po_number='PO-2023-002',
            vendor_name='Vendor B',
            amount=2500.50,
            status='Authorized',
            requested_by='Jane Smith',
            request_date=timezone.now() - timezone.timedelta(days=1),
            authorized_by='Admin User',
            authorization_date=timezone.now() - timezone.timedelta(hours=12),
        )

    def test_purchase_order_authorization_creation(self):
        obj = PurchaseOrderAuthorization.objects.get(po_number='PO-2023-001')
        self.assertEqual(obj.vendor_name, 'Vendor A')
        self.assertEqual(obj.amount, 1000.00)
        self.assertEqual(obj.status, 'Pending')
        self.assertEqual(obj.requested_by, 'John Doe')
        self.assertIsNotNone(obj.request_date)

    def test_po_number_label(self):
        obj = PurchaseOrderAuthorization.objects.get(po_number='PO-2023-001')
        field_label = obj._meta.get_field('po_number').verbose_name
        self.assertEqual(field_label, 'PO Number')

    def test_authorize_method(self):
        obj = self.po_auth_pending
        self.assertTrue(obj.authorize('TestUser'))
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Authorized')
        self.assertEqual(obj.authorized_by, 'TestUser')
        self.assertIsNotNone(obj.authorization_date)

    def test_authorize_already_authorized(self):
        obj = self.po_auth_authorized
        self.assertFalse(obj.authorize('TestUser')) # Should not authorize if already authorized
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Authorized') # Status should remain Authorized

    def test_reject_method(self):
        obj = self.po_auth_pending
        self.assertTrue(obj.reject('TestUser', 'Insufficient budget.'))
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Rejected')
        self.assertEqual(obj.authorized_by, 'TestUser')
        self.assertEqual(obj.comments, 'Insufficient budget.')
        self.assertIsNotNone(obj.authorization_date)

    def test_reject_already_authorized(self):
        obj = self.po_auth_authorized
        self.assertFalse(obj.reject('TestUser', 'Test comments')) # Should not reject if already authorized
        obj.refresh_from_db()
        self.assertEqual(obj.status, 'Authorized') # Status should remain Authorized

    def test_is_pending(self):
        self.assertTrue(self.po_auth_pending.is_pending())
        self.assertFalse(self.po_auth_authorized.is_pending())


class PurchaseOrderAuthorizationViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.po_auth_pending = PurchaseOrderAuthorization.objects.create(
            po_number='PO-2023-001',
            vendor_name='Vendor A',
            amount=1000.00,
            status='Pending',
            requested_by='John Doe',
            request_date=timezone.now(),
        )
        self.po_auth_authorized = PurchaseOrderAuthorization.objects.create(
            po_number='PO-2023-002',
            vendor_name='Vendor B',
            amount=2500.50,
            status='Authorized',
            requested_by='Jane Smith',
            request_date=timezone.now() - timezone.timedelta(days=1),
            authorized_by='Admin User',
            authorization_date=timezone.now() - timezone.timedelta(hours=12),
        )

    def test_list_view(self):
        response = self.client.get(reverse('purchaseorderauthorization_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/list.html')
        self.assertIn('purchase_order_authorizations', response.context)
        self.assertGreaterEqual(len(response.context['purchase_order_authorizations']), 2)

    def test_table_partial_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorderauthorization_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/_purchaseorderauthorization_table.html')
        self.assertIn('purchase_order_authorizations', response.context)
        self.assertContains(response, 'PO-2023-001') # Check for content from the data

    def test_create_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorderauthorization_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/_purchaseorderauthorization_form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_success(self):
        initial_count = PurchaseOrderAuthorization.objects.count()
        data = {
            'po_number': 'PO-2023-003',
            'vendor_name': 'New Vendor',
            'amount': 500.00,
            'status': 'Pending',
            'requested_by': 'Test Creator',
            'request_date': timezone.now().isoformat(), # Use ISO format for DateTimeField
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorderauthorization_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX success returns 204 No Content
        self.assertEqual(PurchaseOrderAuthorization.objects.count(), initial_count + 1)
        self.assertTrue(PurchaseOrderAuthorization.objects.filter(po_number='PO-2023-003').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderAuthorizationList')

    def test_create_view_post_invalid(self):
        initial_count = PurchaseOrderAuthorization.objects.count()
        data = {
            'po_number': 'PO-2023-003', # Missing required fields, or invalid amount
            'vendor_name': 'New Vendor',
            'amount': -100.00, # Invalid amount
            'status': 'Pending',
            'requested_by': 'Test Creator',
            'request_date': timezone.now().isoformat(),
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorderauthorization_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form errors will re-render the form
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/_purchaseorderauthorization_form.html')
        self.assertIn('form', response.context)
        self.assertIn('amount', response.context['form'].errors) # Check for specific field error
        self.assertEqual(PurchaseOrderAuthorization.objects.count(), initial_count) # No new object created

    def test_update_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorderauthorization_edit', args=[self.po_auth_pending.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/_purchaseorderauthorization_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.po_auth_pending)

    def test_update_view_post_success(self):
        data = {
            'po_number': self.po_auth_pending.po_number,
            'vendor_name': 'Updated Vendor Name', # Change this field
            'amount': self.po_auth_pending.amount,
            'status': self.po_auth_pending.status,
            'requested_by': self.po_auth_pending.requested_by,
            'request_date': self.po_auth_pending.request_date.isoformat(),
            'comments': 'Updated comments.',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorderauthorization_edit', args=[self.po_auth_pending.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.po_auth_pending.refresh_from_db()
        self.assertEqual(self.po_auth_pending.vendor_name, 'Updated Vendor Name')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderAuthorizationList')

    def test_delete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorderauthorization_delete', args=[self.po_auth_pending.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/_purchaseorderauthorization_confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.po_auth_pending)

    def test_delete_view_post_success(self):
        initial_count = PurchaseOrderAuthorization.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorderauthorization_delete', args=[self.po_auth_pending.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(PurchaseOrderAuthorization.objects.count(), initial_count - 1)
        self.assertFalse(PurchaseOrderAuthorization.objects.filter(pk=self.po_auth_pending.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderAuthorizationList')

    def test_action_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorderauthorization_action', args=[self.po_auth_pending.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/_purchaseorderauthorization_action_form.html')
        self.assertIn('form', response.context)
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.po_auth_pending)

    def test_action_view_post_authorize_success(self):
        data = {
            'status': 'Authorized',
            'comments': 'Approved by manager.',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorderauthorization_action', args=[self.po_auth_pending.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.po_auth_pending.refresh_from_db()
        self.assertEqual(self.po_auth_pending.status, 'Authorized')
        self.assertEqual(self.po_auth_pending.comments, 'Approved by manager.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderAuthorizationList')

    def test_action_view_post_reject_success(self):
        data = {
            'status': 'Rejected',
            'comments': 'Budget constraints.',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorderauthorization_action', args=[self.po_auth_pending.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.po_auth_pending.refresh_from_db()
        self.assertEqual(self.po_auth_pending.status, 'Rejected')
        self.assertEqual(self.po_auth_pending.comments, 'Budget constraints.')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderAuthorizationList')

    def test_action_view_post_invalid_status_change(self):
        # Try to change status of an already authorized PO
        data = {
            'status': 'Rejected',
            'comments': 'Invalid action.',
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorderauthorization_action', args=[self.po_auth_authorized.pk]), data, **headers)
        self.assertEqual(response.status_code, 200) # Form will re-render with errors
        self.assertTemplateUsed(response, 'material_management/purchaseorderauthorization/_purchaseorderauthorization_action_form.html')
        self.assertIn('form', response.context)
        self.assertIn('status', response.context['form'].errors) # Check for specific field error
        self.po_auth_authorized.refresh_from_db()
        self.assertEqual(self.po_auth_authorized.status, 'Authorized') # Status should not have changed
```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated templates and views already incorporate HTMX and Alpine.js principles:

*   **HTMX for Dynamic Updates:**
    *   The main list view (`list.html`) uses `hx-get` to load the `_purchaseorderauthorization_table.html` partial on page load and whenever a `refreshPurchaseOrderAuthorizationList` event is triggered.
    *   CRUD operation buttons (`Add`, `Edit`, `Delete`, `Action`) use `hx-get` to load the corresponding form partials (`_form.html`, `_confirm_delete.html`, `_action_form.html`) into the `#modalContent` div.
    *   Form submissions within the modal use `hx-post` and `hx-swap="none"`. Upon successful submission, the Django view returns `HttpResponse` with `status=204` and `HX-Trigger` header (`refreshPurchaseOrderAuthorizationList`), which closes the modal (via `_` attribute on `modal` div) and refreshes the main table. This prevents full page reloads and provides a snappy user experience.
*   **Alpine.js for UI State Management:**
    *   Alpine.js is used for simple UI interactions like showing/hiding the modal. The `_` attribute (Alpine.js's `x-data` and `x-show` equivalents) is used directly in the HTML to control the modal's visibility. The modal disappears on click outside or on 'Cancel' button.
*   **DataTables for List Views:**
    *   The `_purchaseorderauthorization_table.html` partial contains the `<table>` element with the ID `purchaseorderauthorizationTable`.
    *   A JavaScript block at the end of this partial initializes DataTables on this table ID. It includes options for pagination, search, and showing entries.
    *   The DataTables initialization is robust to HTMX re-swaps by destroying any existing DataTable instance before re-initializing.
*   **No Additional JavaScript:** All interactions are handled purely via HTMX and Alpine.js (or direct DOM manipulation with `_` attribute), fulfilling the requirement to avoid complex custom JavaScript.

### Final Notes

This comprehensive plan provides a clear roadmap for migrating the `PO_Authorize_Dashboard` to Django. By following these steps, focusing on automation, and leveraging modern front-end technologies like HTMX and Alpine.js, the organization can achieve a highly efficient, maintainable, and user-friendly application. The "fat model, thin view" architecture ensures business logic is encapsulated and reusable, leading to cleaner code and easier debugging. The emphasis on automated testing ensures the reliability and correctness of the new Django application.