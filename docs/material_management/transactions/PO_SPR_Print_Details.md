## ASP.NET to Django Conversion Script: Modernizing PO Report Details

This modernization plan outlines the strategic transition of your legacy ASP.NET application, specifically the `PO_SPR_Print_Details.aspx` module, to a robust and scalable Django 5.0+ solution. By leveraging AI-assisted automation, we will systematically convert your existing functionality into a modern, maintainable, and highly performant Django application. Our focus is on business outcomes, ensuring a smoother user experience, improved data integrity, and reduced operational costs through a lean, HTMX-driven frontend.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the underlying database table and its columns that the ASP.NET code implicitly interacts with.

**Instructions:**
The `PO_SPR_Print_Details.aspx` page primarily handles query string parameters (`pono`, `Code`, `mid`, `AmdNo`, `Key`) to construct a URL for a report viewer. This suggests these parameters are identifiers for a Purchase Order (PO) and its related details. While this specific ASP.NET snippet doesn't directly show database queries, we infer a `PurchaseOrder` table is central to this module.

**Inferred Database Details:**
- **[TABLE_NAME]:** `tbl_purchase_orders` (hypothetical, as not explicitly stated in the provided code)
- **Key Columns (inferred from query strings and common PO structures):**
    - `po_no` (Purchase Order Number)
    - `supplier_code` (Supplier Identifier)
    - `material_id` (Material Identifier within the PO)
    - `amendment_no` (Amendment Number for the PO)
    - `report_key` (Specific key for report variant/type)
    - `description` (A general description of the PO)
    - `order_date` (Date the PO was placed)
    - `is_active` (Boolean flag for active records)

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and core logic present in the ASP.NET code.

**Instructions:**
The `PO_SPR_Print_Details.aspx` module is primarily a **read/display** function, serving as a wrapper to show a generated report. It receives specific identifiers via query parameters, constructs a URL for an underlying report generation service (`PO_SPR_Print_Page.aspx`), and provides a "Cancel" navigation button. There are no direct data creation, update, or deletion operations shown within this specific ASP.NET snippet. The "report generation" is a complex read operation handled by an external component (Crystal Reports in ASP.NET, which will be re-engineered in Django).

- **Create:** Not present in this specific ASP.NET module.
- **Read:** The core functionality involves reading URL query parameters (`pono`, `Code`, `mid`, `AmdNo`, `Key`) and using them to display a report. This will be mapped to a Django `TemplateView` that prepares data for rendering the report.
- **Update:** Not present in this specific ASP.NET module.
- **Delete:** Not present in this specific ASP.NET module.
- **Navigation:** The `Cancel_Click` event performs a `Response.Redirect` to `PO_Print.aspx`, which we infer is the main Purchase Order list or dashboard view. This will be a simple link/redirect in Django.
- **Business Logic:** The logic of constructing the report URL based on query parameters will be handled by the Django view, which will then interact with a model method to simulate report content generation.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles, noting client-side behaviors.

**Instructions:**
- **`<iframe id="myifram" ...>`:** This iframe is used to embed another ASP.NET page (`PO_SPR_Print_Page.aspx`) which generates the actual report. In Django, this will be replaced by directly rendering the report content (generated by a model method) within the template, or by providing a direct link to a generated PDF/HTML report.
- **`<asp:Button ID="Cancel" ... onclick="Cancel_Click" />`:** A standard button for navigation. In Django, this will be a simple HTML anchor tag or a button with a `hx-get` attribute for HTMX-driven navigation, or a standard form submission for non-HTMX. We will opt for a simple link as it's a pure navigation back to the list.
- **Styling:** The ASP.NET page uses an external stylesheet (`StyleSheet.css`) and inline styles. These will be replaced by Tailwind CSS classes.
- **`loadingNotifier.js`:** This suggests a loading indicator. This can be replicated with HTMX's `hx-indicator` and Alpine.js for more complex UI state management.

### Step 4: Generate Django Code

We will create a Django application named `material_management` to house these components.

#### 4.1 Models

**Task:** Create a Django model representing a Purchase Order, including methods for report generation.

**Instructions:**
The `PurchaseOrder` model will correspond to the inferred `tbl_purchase_orders` table. It will contain the identified fields and a method to simulate the generation of report content, demonstrating the "fat model" principle.

```python
# material_management/models.py
from django.db import models
from django.urls import reverse
from django.utils import timezone

class PurchaseOrder(models.Model):
    """
    Represents a Purchase Order, providing methods for report content generation.
    """
    po_no = models.CharField(max_length=50, db_column='PO_No', unique=True, verbose_name="PO Number")
    supplier_code = models.CharField(max_length=50, db_column='Supplier_Code', verbose_name="Supplier Code")
    material_id = models.CharField(max_length=50, db_column='Material_ID', verbose_name="Material ID")
    amendment_no = models.CharField(max_length=10, db_column='Amendment_No', blank=True, null=True, verbose_name="Amendment Number")
    report_key = models.CharField(max_length=50, db_column='Report_Key', blank=True, null=True, verbose_name="Report Key")
    description = models.TextField(db_column='Description', blank=True, null=True, verbose_name="Description")
    order_date = models.DateField(db_column='Order_Date', default=timezone.now, verbose_name="Order Date")
    is_active = models.BooleanField(db_column='Is_Active', default=True, verbose_name="Is Active")

    class Meta:
        managed = False  # Set to True if Django should manage this table
        db_table = 'tbl_purchase_orders'  # Assuming this table name
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO: {self.po_no} - Supplier: {self.supplier_code}"
        
    def get_absolute_url(self):
        """Returns the URL to access a particular instance of PurchaseOrder."""
        return reverse('purchaseorder_detail', args=[str(self.pk)])

    def generate_report_content(self, report_type='html'):
        """
        Business logic to simulate generating report content for this Purchase Order.
        In a real application, this would query related tables,
        format data, and prepare it for a PDF, Excel, or HTML report.
        """
        # Example: Basic HTML report content
        content = f"""
        <div class="p-6 bg-white rounded-lg shadow-md print-area">
            <h1 class="text-3xl font-bold mb-4 text-center">Purchase Order Report</h1>
            <div class="grid grid-cols-2 gap-4 text-gray-700">
                <div><strong class="font-semibold">PO Number:</strong> {self.po_no}</div>
                <div><strong class="font-semibold">Supplier Code:</strong> {self.supplier_code}</div>
                <div><strong class="font-semibold">Material ID:</strong> {self.material_id}</div>
                <div><strong class="font-semibold">Amendment No:</strong> {self.amendment_no if self.amendment_no else 'N/A'}</div>
                <div><strong class="font-semibold">Report Key:</strong> {self.report_key if self.report_key else 'N/A'}</div>
                <div><strong class="font-semibold">Order Date:</strong> {self.order_date.strftime('%Y-%m-%d')}</div>
            </div>
            <div class="mt-6">
                <h3 class="text-xl font-semibold mb-2">Description:</h3>
                <p class="text-gray-600">{self.description if self.description else 'No description provided.'}</p>
            </div>
            <hr class="my-6 border-gray-300" />
            <p class="text-sm text-gray-500 text-center">
                This is a simulated report content generated for Purchase Order: {self.po_no}.
                Actual report would contain detailed line items, pricing, terms, etc.
            </p>
        </div>
        """
        if report_type == 'html':
            return content
        elif report_type == 'pdf_link':
            # In a real scenario, this would return a URL to a PDF generation endpoint
            return f"/material_management/purchaseorder/{self.pk}/generate_pdf/"
        return "Report content could not be generated."

    @classmethod
    def get_po_by_params(cls, po_no, code, mid, amd_no, key):
        """
        Retrieves a PurchaseOrder instance based on the query string parameters.
        This method encapsulates the search logic from the original ASP.NET behavior.
        """
        try:
            # This is a simplified search. In a real system, you might need
            # to consider multiple matching POs or more complex filtering.
            return cls.objects.get(
                po_no=po_no,
                supplier_code=code,
                material_id=mid,
                amendment_no=amd_no,
                report_key=key
            )
        except cls.DoesNotExist:
            return None
        except Exception as e:
            # Log the error for debugging
            print(f"Error fetching PO by parameters: {e}")
            return None

```

#### 4.2 Forms

**Task:** Define a Django ModelForm for `PurchaseOrder` to enable CRUD operations (even though the ASP.NET page is only for display, we provide this for a complete module).

**Instructions:**
This form will be used for creating and updating `PurchaseOrder` records, aligning with the `fat model/thin view` philosophy and including Tailwind CSS for styling.

```python
# material_management/forms.py
from django import forms
from .models import PurchaseOrder

class PurchaseOrderForm(forms.ModelForm):
    """
    Form for creating and updating PurchaseOrder instances.
    """
    class Meta:
        model = PurchaseOrder
        fields = ['po_no', 'supplier_code', 'material_id', 'amendment_no', 'report_key', 'description', 'order_date', 'is_active']
        widgets = {
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amendment_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'report_key': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'order_date': forms.DateInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'type': 'date'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
    
    # Custom validation example (e.g., ensure PO number is unique before saving)
    def clean_po_no(self):
        po_no = self.cleaned_data['po_no']
        if self.instance.pk: # Editing existing object
            if PurchaseOrder.objects.filter(po_no=po_no).exclude(pk=self.instance.pk).exists():
                raise forms.ValidationError("This PO Number already exists.")
        else: # Creating new object
            if PurchaseOrder.objects.filter(po_no=po_no).exists():
                raise forms.ValidationError("This PO Number already exists.")
        return po_no

```

#### 4.3 Views

**Task:** Implement CRUD operations using CBVs for `PurchaseOrder`, and a dedicated view for the report details.

**Instructions:**
We will define standard `ListView`, `CreateView`, `UpdateView`, `DeleteView` for `PurchaseOrder` management. Crucially, a `PurchaseOrderReportDetailView` will directly replace the `PO_SPR_Print_Details.aspx` functionality, handling URL parameters and displaying report content. The views are kept thin (max 15 lines per method) by delegating business logic to the `PurchaseOrder` model.

```python
# material_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import PurchaseOrder
from .forms import PurchaseOrderForm

# Standard CRUD Views for PurchaseOrder

class PurchaseOrderListView(ListView):
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/list.html'
    context_object_name = 'purchaseorders'

    def get_queryset(self):
        # Example of applying query string filters for DataTables server-side processing
        # For client-side DataTables, this might just return all_active()
        return PurchaseOrder.objects.filter(is_active=True).order_by('-order_date')

class PurchaseOrderCreateView(CreateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'material_management/purchaseorder/form.html'
    success_url = reverse_lazy('purchaseorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

class PurchaseOrderUpdateView(UpdateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'material_management/purchaseorder/form.html'
    success_url = reverse_lazy('purchaseorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

class PurchaseOrderDeleteView(DeleteView):
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/confirm_delete.html'
    success_url = reverse_lazy('purchaseorder_list')

    def delete(self, request, *args, **kwargs):
        # Soft delete example: set is_active to False
        self.object = self.get_object()
        self.object.is_active = False
        self.object.save()
        messages.success(self.request, 'Purchase Order deactivated successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return super().delete(request, *args, **kwargs) # Or just return HttpResponse(status=204) if soft delete

# HTMX partial for DataTables
class PurchaseOrderTablePartialView(ListView):
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/_purchaseorder_table.html'
    context_object_name = 'purchaseorders'
    
    def get_queryset(self):
        return PurchaseOrder.objects.filter(is_active=True).order_by('-order_date')

# Dedicated View for PO_SPR_Print_Details.aspx migration
class PurchaseOrderReportDetailView(TemplateView):
    """
    Mimics the functionality of PO_SPR_Print_Details.aspx.
    Retrieves PO details based on query parameters and displays a generated report.
    """
    template_name = 'material_management/purchaseorder/report_detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract query parameters directly from the request
        po_no = self.request.GET.get('pono')
        supplier_code = self.request.GET.get('Code')
        material_id = self.request.GET.get('mid')
        amendment_no = self.request.GET.get('AmdNo')
        report_key = self.request.GET.get('Key')

        purchase_order = None
        report_content = "Please provide valid parameters to view the report."

        if po_no and supplier_code and material_id:
            # Use the fat model method to find the PO
            purchase_order = PurchaseOrder.get_po_by_params(po_no, supplier_code, material_id, amendment_no, report_key)
            if purchase_order:
                # Generate report content using the model's method
                report_content = purchase_order.generate_report_content(report_type='html')
            else:
                report_content = "No Purchase Order found with the provided details."
        
        context['purchase_order'] = purchase_order
        context['report_content'] = report_content
        context['query_params'] = self.request.GET.urlencode() # For showing parameters

        return context

```

#### 4.4 Templates

**Task:** Create comprehensive HTML templates for each view, ensuring DRY principles and HTMX/Alpine.js integration.

**Instructions:**
- **`list.html`:** The main entry point displaying a table of purchase orders.
- **`_purchaseorder_table.html`:** A partial template loaded by HTMX to refresh the DataTables content.
- **`form.html`:** A partial template for add/edit forms, loaded into a modal.
- **`confirm_delete.html`:** A partial template for delete confirmation, loaded into a modal.
- **`report_detail.html`:** The specific template for displaying the PO report, replacing the ASP.NET `iframe`.

```html
{# material_management/purchaseorder/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Purchase Orders</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
            hx-get="{% url 'purchaseorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Purchase Order
        </button>
    </div>
    
    {% include 'core/messages.html' %} {# For Django messages #}

    <div id="purchaseorderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'purchaseorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Purchase Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full mx-4 my-8"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI states
    });
</script>
{% endblock %}

```

```html
{# material_management/purchaseorder/_purchaseorder_table.html #}
<div class="overflow-x-auto rounded-lg shadow-md">
    <table id="purchaseorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Material ID</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in purchaseorders %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.po_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.supplier_code }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.material_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-700">{{ obj.order_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-green-500 hover:bg-green-600 text-white font-bold py-1 px-3 rounded text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'purchaseorder_report_detail' %}?pono={{ obj.po_no }}&Code={{ obj.supplier_code }}&mid={{ obj.material_id }}&AmdNo={{ obj.amendment_no|default_if_none:'' }}&Key={{ obj.report_key|default_if_none:'' }}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        View Report
                    </button>
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'purchaseorder_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'purchaseorder_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-6 text-center text-gray-500">No Purchase Orders found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#purchaseorderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 5] } // Disable sorting for SN and Actions columns
        ]
    });
});
</script>
```

```html
{# material_management/purchaseorder/_purchaseorder_form.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Purchase Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-spinner">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="text-red-500 text-xs mt-1">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save
            </button>
            <div id="form-spinner" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
            </div>
        </div>
    </form>
</div>
```

```html
{# material_management/purchaseorder/_purchaseorder_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to deactivate the Purchase Order 
        <span class="font-bold">"{{ object.po_no }}"</span>? 
        This action will make it inactive, but not permanently remove it from the system.
    </p>
    <form hx-post="{% url 'purchaseorder_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Confirm Deactivate
            </button>
        </div>
    </form>
</div>
```

```html
{# material_management/purchaseorder/report_detail.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">PO Print Details</h2>
        <a href="{% url 'purchaseorder_list' %}" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
            Back to PO List
        </a>
    </div>

    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Report Parameters Received:</h3>
        <ul class="list-disc list-inside text-gray-700">
            {% if purchase_order %}
            <li><strong>PO Number:</strong> {{ purchase_order.po_no }}</li>
            <li><strong>Supplier Code:</strong> {{ purchase_order.supplier_code }}</li>
            <li><strong>Material ID:</strong> {{ purchase_order.material_id }}</li>
            <li><strong>Amendment No:</strong> {{ purchase_order.amendment_no|default_if_none:'N/A' }}</li>
            <li><strong>Report Key:</strong> {{ purchase_order.report_key|default_if_none:'N/A' }}</li>
            {% else %}
            <li class="text-red-600">No matching Purchase Order found or insufficient parameters provided.</li>
            {% endif %}
            {% for key, value in request.GET.items %}
                {% if not purchase_order and not forloop.first %} {# If no PO, show all raw params #}
                    <li><strong>{{ key }}:</strong> {{ value }}</li>
                {% endif %}
            {% endfor %}
        </ul>
        <p class="mt-4 text-gray-600">
            This page dynamically renders report content based on the provided parameters. 
            In the original ASP.NET, this was an `iframe` loading `PO_SPR_Print_Page.aspx`. 
            Here, the report content is generated by the `PurchaseOrder` model's methods.
        </p>
    </div>

    <div class="report-display-area border border-gray-200 rounded-lg shadow-inner bg-gray-50 p-6">
        <h3 class="text-xl font-semibold text-gray-900 mb-4">Generated Report Content:</h3>
        {% if report_content %}
            <div class="prose max-w-none">
                {{ report_content|safe }} {# Mark as safe because content is generated by model #}
            </div>
        {% else %}
            <p class="text-gray-500">No report content available.</p>
        {% endif %}
    </div>

    <div class="mt-8 flex justify-end">
        <a href="{% url 'purchaseorder_list' %}" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
            Cancel
        </a>
    </div>
</div>
{% endblock %}

```

#### 4.5 URLs

**Task:** Define URL patterns for all views.

**Instructions:**
URLs are clean, descriptive, and follow Django's conventions. The `report_detail` view is designed to accept query parameters as per the original ASP.NET behavior.

```python
# material_management/urls.py
from django.urls import path
from .views import (
    PurchaseOrderListView, 
    PurchaseOrderCreateView, 
    PurchaseOrderUpdateView, 
    PurchaseOrderDeleteView,
    PurchaseOrderTablePartialView,
    PurchaseOrderReportDetailView,
)

urlpatterns = [
    # CRUD operations for Purchase Orders
    path('purchaseorders/', PurchaseOrderListView.as_view(), name='purchaseorder_list'),
    path('purchaseorders/add/', PurchaseOrderCreateView.as_view(), name='purchaseorder_add'),
    path('purchaseorders/edit/<int:pk>/', PurchaseOrderUpdateView.as_view(), name='purchaseorder_edit'),
    path('purchaseorders/delete/<int:pk>/', PurchaseOrderDeleteView.as_view(), name='purchaseorder_delete'),
    
    # HTMX endpoint for refreshing the table
    path('purchaseorders/table/', PurchaseOrderTablePartialView.as_view(), name='purchaseorder_table'),

    # Dedicated view for PO_SPR_Print_Details.aspx migration (Report Viewer)
    # This view expects query parameters like ?pono=...&Code=...
    path('purchaseorders/report_details/', PurchaseOrderReportDetailView.as_view(), name='purchaseorder_report_detail'),
]

# Example of how to include these URLs in your project's main urls.py:
# from django.contrib import admin
# from django.urls import path, include

# urlpatterns = [
#     path('admin/', admin.site.urls),
#     path('material_management/', include('material_management.urls')),
#     # ... other app URLs
# ]
```

#### 4.6 Tests

**Task:** Write comprehensive unit tests for the model and integration tests for the views.

**Instructions:**
Tests cover model creation, field validation, and the core business logic method (`generate_report_content`). View tests ensure correct template usage, HTTP responses, form handling, and HTMX interactions.

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import PurchaseOrder
from .forms import PurchaseOrderForm

class PurchaseOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.po1 = PurchaseOrder.objects.create(
            po_no='PO001',
            supplier_code='SUP001',
            material_id='MAT001',
            amendment_no='A01',
            report_key='RPTKEY01',
            description='Test Purchase Order 1',
            order_date='2023-01-01',
            is_active=True
        )
        cls.po2 = PurchaseOrder.objects.create(
            po_no='PO002',
            supplier_code='SUP002',
            material_id='MAT002',
            amendment_no='',
            report_key='',
            description='Test Purchase Order 2 (Inactive)',
            order_date='2023-01-02',
            is_active=False
        )

    def test_purchaseorder_creation(self):
        self.assertEqual(self.po1.po_no, 'PO001')
        self.assertEqual(self.po1.supplier_code, 'SUP001')
        self.assertEqual(self.po1.material_id, 'MAT001')
        self.assertTrue(self.po1.is_active)
        self.assertEqual(self.po1.order_date, timezone.localdate(timezone.datetime(2023, 1, 1)))

    def test_str_method(self):
        self.assertEqual(str(self.po1), "PO: PO001 - Supplier: SUP001")

    def test_get_absolute_url(self):
        self.assertEqual(self.po1.get_absolute_url(), reverse('purchaseorder_detail', args=[str(self.po1.pk)]))

    def test_generate_report_content(self):
        report_html = self.po1.generate_report_content()
        self.assertIn("Purchase Order Report", report_html)
        self.assertIn(f"PO Number: {self.po1.po_no}", report_html)
        self.assertIn(f"Supplier Code: {self.po1.supplier_code}", report_html)
        self.assertIn("This is a simulated report content", report_html)

    def test_get_po_by_params_found(self):
        found_po = PurchaseOrder.get_po_by_params(
            po_no='PO001',
            code='SUP001',
            mid='MAT001',
            amd_no='A01',
            key='RPTKEY01'
        )
        self.assertEqual(found_po, self.po1)

    def test_get_po_by_params_not_found(self):
        not_found_po = PurchaseOrder.get_po_by_params(
            po_no='PO999',
            code='SUP001',
            mid='MAT001',
            amd_no='A01',
            key='RPTKEY01'
        )
        self.assertIsNone(not_found_po)

    def test_get_po_by_params_incomplete(self):
        incomplete_po = PurchaseOrder.get_po_by_params(
            po_no='PO001',
            code='SUP001',
            mid='MAT001',
            amd_no='', # Missing amendment_no
            key='WRONG_KEY' # Wrong key
        )
        # Depending on exact matching logic in get_po_by_params, this might fail or return None
        # For our current strict match, it should be None
        self.assertIsNone(incomplete_po)
        
    def test_form_po_no_uniqueness_create(self):
        form = PurchaseOrderForm(data={
            'po_no': 'PO001', # This PO_NO already exists
            'supplier_code': 'NEW_SUP',
            'material_id': 'NEW_MAT',
            'order_date': '2023-01-03'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('This PO Number already exists.', form.errors['po_no'])

    def test_form_po_no_uniqueness_update(self):
        # Create a new PO for editing
        po_to_edit = PurchaseOrder.objects.create(
            po_no='PO003',
            supplier_code='SUP003',
            material_id='MAT003',
            order_date='2023-01-03'
        )
        form = PurchaseOrderForm(instance=po_to_edit, data={
            'po_no': 'PO001', # Try to change to an existing PO_NO
            'supplier_code': 'SUP003',
            'material_id': 'MAT003',
            'order_date': '2023-01-03'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('This PO Number already exists.', form.errors['po_no'])

    def test_form_po_no_uniqueness_update_self(self):
        # Update without changing po_no should be valid
        form = PurchaseOrderForm(instance=self.po1, data={
            'po_no': 'PO001', 
            'supplier_code': 'UPDATED_SUP',
            'material_id': 'MAT001',
            'order_date': '2023-01-01'
        })
        self.assertTrue(form.is_valid())


class PurchaseOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.po1 = PurchaseOrder.objects.create(
            po_no='PO_VIEW_001',
            supplier_code='SUP_VIEW_001',
            material_id='MAT_VIEW_001',
            amendment_no='A01',
            report_key='RPTKEY01',
            description='PO for testing views',
            order_date='2023-01-01',
            is_active=True
        )
        cls.po2 = PurchaseOrder.objects.create(
            po_no='PO_VIEW_002',
            supplier_code='SUP_VIEW_002',
            material_id='MAT_VIEW_002',
            description='Inactive PO',
            order_date='2023-01-02',
            is_active=False
        )

    def setUp(self):
        self.client = Client()

    def test_list_view(self):
        response = self.client.get(reverse('purchaseorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/list.html')
        self.assertIn('purchaseorders', response.context)
        # Check only active POs are displayed by default in the initial list container
        self.assertContains(response, 'PO_VIEW_001')
        self.assertNotContains(response, 'PO_VIEW_002') # Inactive

    def test_table_partial_view(self):
        response = self.client.get(reverse('purchaseorder_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/_purchaseorder_table.html')
        self.assertIn('purchaseorders', response.context)
        self.assertContains(response, 'PO_VIEW_001')
        self.assertNotContains(response, 'PO_VIEW_002') # Inactive

    def test_create_view_get(self):
        response = self.client.get(reverse('purchaseorder_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/form.html')
        self.assertIn('form', response.context)

    def test_create_view_post_valid(self):
        data = {
            'po_no': 'NEW_PO_001',
            'supplier_code': 'NEW_SUP',
            'material_id': 'NEW_MAT',
            'description': 'A newly created PO',
            'order_date': '2024-01-01',
            'is_active': True
        }
        response = self.client.post(reverse('purchaseorder_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect after successful creation
        self.assertTrue(PurchaseOrder.objects.filter(po_no='NEW_PO_001').exists())
        self.assertRedirects(response, reverse('purchaseorder_list'))

    def test_create_view_post_invalid(self):
        data = {
            'po_no': 'PO_VIEW_001', # Duplicate PO number
            'supplier_code': 'INVALID',
            'material_id': 'INVALID',
            'order_date': '2024-01-01'
        }
        response = self.client.post(reverse('purchaseorder_add'), data)
        self.assertEqual(response.status_code, 200) # Form re-render with errors
        self.assertTemplateUsed(response, 'material_management/purchaseorder/form.html')
        self.assertFormError(response.context['form'], 'po_no', 'This PO Number already exists.')
        self.assertFalse(PurchaseOrder.objects.filter(po_no='INVALID_PO').exists())

    def test_update_view_get(self):
        response = self.client.get(reverse('purchaseorder_edit', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.po1)

    def test_update_view_post_valid(self):
        new_desc = 'Updated description for PO'
        data = {
            'po_no': self.po1.po_no,
            'supplier_code': self.po1.supplier_code,
            'material_id': self.po1.material_id,
            'amendment_no': self.po1.amendment_no,
            'report_key': self.po1.report_key,
            'description': new_desc,
            'order_date': self.po1.order_date.strftime('%Y-%m-%d'),
            'is_active': self.po1.is_active
        }
        response = self.client.post(reverse('purchaseorder_edit', args=[self.po1.pk]), data)
        self.assertEqual(response.status_code, 302)
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.description, new_desc)
        self.assertRedirects(response, reverse('purchaseorder_list'))

    def test_update_view_post_invalid(self):
        # Attempt to change PO_VIEW_001's po_no to PO_VIEW_002 (which exists)
        data = {
            'po_no': self.po2.po_no, 
            'supplier_code': self.po1.supplier_code,
            'material_id': self.po1.material_id,
            'order_date': self.po1.order_date.strftime('%Y-%m-%d')
        }
        response = self.client.post(reverse('purchaseorder_edit', args=[self.po1.pk]), data)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/form.html')
        self.assertFormError(response.context['form'], 'po_no', 'This PO Number already exists.')
        
    def test_delete_view_get(self):
        response = self.client.get(reverse('purchaseorder_delete', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/confirm_delete.html')
        self.assertEqual(response.context['object'], self.po1)

    def test_delete_view_post(self):
        # Test soft delete (setting is_active=False)
        self.assertTrue(self.po1.is_active) # Ensure it's active initially
        response = self.client.post(reverse('purchaseorder_delete', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 302)
        self.po1.refresh_from_db()
        self.assertFalse(self.po1.is_active)
        self.assertRedirects(response, reverse('purchaseorder_list'))
        
    # HTMX Specific Tests
    def test_create_view_post_htmx(self):
        data = {
            'po_no': 'HX_PO_001',
            'supplier_code': 'HX_SUP',
            'material_id': 'HX_MAT',
            'order_date': '2024-02-01',
            'is_active': True
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderList')
        self.assertTrue(PurchaseOrder.objects.filter(po_no='HX_PO_001').exists())

    def test_update_view_post_htmx(self):
        updated_description = 'Updated via HTMX'
        data = {
            'po_no': self.po1.po_no,
            'supplier_code': self.po1.supplier_code,
            'material_id': self.po1.material_id,
            'description': updated_description,
            'order_date': self.po1.order_date.strftime('%Y-%m-%d'),
            'is_active': self.po1.is_active
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_edit', args=[self.po1.pk]), data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderList')
        self.po1.refresh_from_db()
        self.assertEqual(self.po1.description, updated_description)

    def test_delete_view_post_htmx(self):
        po_to_delete = PurchaseOrder.objects.create(
            po_no='HX_DEL_001', supplier_code='HX_DEL_SUP', material_id='HX_DEL_MAT', order_date='2024-03-01', is_active=True
        )
        self.assertTrue(po_to_delete.is_active)
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('purchaseorder_delete', args=[po_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPurchaseOrderList')
        po_to_delete.refresh_from_db()
        self.assertFalse(po_to_delete.is_active)

    # Report Detail View Specific Tests (PO_SPR_Print_Details.aspx migration)
    def test_report_detail_view_success(self):
        query_params = {
            'pono': self.po1.po_no,
            'Code': self.po1.supplier_code,
            'mid': self.po1.material_id,
            'AmdNo': self.po1.amendment_no,
            'Key': self.po1.report_key,
        }
        response = self.client.get(reverse('purchaseorder_report_detail'), query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/report_detail.html')
        self.assertIn('purchase_order', response.context)
        self.assertEqual(response.context['purchase_order'], self.po1)
        self.assertContains(response, f'PO Number: {self.po1.po_no}')
        self.assertContains(response, 'This is a simulated report content')

    def test_report_detail_view_no_matching_po(self):
        query_params = {
            'pono': 'NON_EXISTENT_PO',
            'Code': 'SUP_VIEW_001',
            'mid': 'MAT_VIEW_001',
            'AmdNo': 'A01',
            'Key': 'RPTKEY01',
        }
        response = self.client.get(reverse('purchaseorder_report_detail'), query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/report_detail.html')
        self.assertIsNone(response.context['purchase_order'])
        self.assertContains(response, 'No Purchase Order found with the provided details.')
        self.assertNotContains(response, 'This is a simulated report content')
        
    def test_report_detail_view_missing_params(self):
        query_params = {
            'pono': 'PO_VIEW_001',
            # 'Code' is missing
            'mid': 'MAT_VIEW_001',
        }
        response = self.client.get(reverse('purchaseorder_report_detail'), query_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/report_detail.html')
        self.assertIsNone(response.context['purchase_order'])
        self.assertContains(response, 'Please provide valid parameters to view the report.')
        self.assertNotContains(response, 'This is a simulated report content')

```

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
- **HTMX for CRUD:** All Create, Update, Delete, and List refresh operations are driven by HTMX. Forms are loaded into a modal and submit via HTMX, triggering a `refreshPurchaseOrderList` event on success. The list itself is periodically (or on event) reloaded via HTMX to ensure data consistency.
- **DataTables:** The `_purchaseorder_table.html` partial uses DataTables for client-side search, sort, and pagination. It's initialized via a `$(document).ready` script block within the partial, ensuring it re-initializes correctly after HTMX swaps.
- **Alpine.js for Modals:** The main `list.html` incorporates Alpine.js (via the `_=` attribute from `htmx.org/extensions/alpine-morph/`) to manage the modal's `hidden` class, providing a clean show/hide mechanism for forms and delete confirmations without extra JavaScript.
- **No Full Page Reloads:** All user interactions, including form submissions and list refreshes, occur without full page reloads, providing a snappy, modern user experience.
- **Report View:** The `PurchaseOrderReportDetailView` simply renders its content directly, replacing the old `iframe` approach, allowing for more control over styling and integration. The "Cancel" button navigates back to the PO list view. A "View Report" button in the list view now triggers a modal to show the report content, demonstrating how the old `iframe` behavior can be replaced with an HTMX-driven modal to display the report.

## Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your `PO_SPR_Print_Details.aspx` module to a modern Django application.

- **Placeholders:** All `[PLACEHOLDERS]` have been replaced with concrete values inferred from the ASP.NET code and best practices.
- **DRY Templates:** Template inheritance (`{% extends 'core/base.html' %}`) and partials (`_purchaseorder_table.html`, `_purchaseorder_form.html`, `_purchaseorder_confirm_delete.html`) are used extensively to maintain reusability and reduce redundancy.
- **Fat Models, Thin Views:** Business logic, such as `generate_report_content` and `get_po_by_params`, resides within the `PurchaseOrder` model, keeping views concise and focused on request handling.
- **Comprehensive Tests:** Robust unit and integration tests are included, ensuring the reliability and correctness of the migrated functionality and providing a safety net for future development.
- **Automation Focus:** The structure is designed to be easily digestible by conversational AI tools, enabling automated code generation and deployment steps based on these patterns.

This modernization approach not only addresses the functional migration but also significantly upgrades the underlying architecture and user experience, positioning your application for future growth and easier maintenance.