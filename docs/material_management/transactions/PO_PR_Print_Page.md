This comprehensive modernization plan will guide your transition from the legacy ASP.NET application to a robust, modern Django-based solution. Our focus is on leveraging AI-assisted automation by providing structured, clear, and repeatable steps. By moving to Django, you'll gain improved maintainability, scalability, and a more agile development environment, significantly reducing future operational costs and enabling faster feature delivery.

## ASP.NET to Django Conversion Script:

This plan outlines the conversion of your ASP.NET "PO/PR Print Page" into a dynamic, data-driven Django report view, complete with underlying data models and an example of core CRUD functionality for the `PurchaseOrder` entity.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination where appropriate (e.g., for purchase order line items within the report).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code to define Django models. The `loaddata` function in the C# code-behind performs extensive data aggregation from numerous tables. For this modernization, we will define models for the primary entities (`PurchaseOrder` and `PurchaseOrderDetail`) and illustrate how associated lookup data would be fetched and processed within a "fat model" approach.

## Instructions:

Based on the `loaddata` method's SQL queries, the primary tables and their inferred columns are:

**Primary Entities:**
-   `tblMM_PO_Master` -> `PurchaseOrder`
    -   `Id` (PK)
    -   `PONo`
    -   `SupplierId` (FK)
    -   `SysDate` (Registration Date)
    -   `ReferenceDate`
    -   `Reference`
    -   `ReferenceDesc`
    -   `AmendmentNo`
    -   `ModeOfDispatch`
    -   `Inspection`
    -   `Remarks`
    -   `ShipTo`
    -   `Insurance`
    -   `Freight` (FK to `tblFreight_Master`)
    -   `Octroi` (FK to `tblOctroi_Master`)
    -   `Warrenty` (FK to `tblWarrenty_Master`)
    -   `PaymentTerms` (FK to `tblPayment_Master`)
    -   `CheckedBy` (FK to `tblHR_OfficeStaff`)
    -   `ApprovedBy` (FK to `tblHR_OfficeStaff`)
    -   `AuthorizedBy` (FK to `tblHR_OfficeStaff`)
    -   `CheckedDate`
    -   `ApproveDate`
    -   `AuthorizeDate`
    -   `TC`
    -   `CompId` (Company ID, implicitly a FK)

-   `tblMM_PO_Details` -> `PurchaseOrderDetail`
    -   `Id` (PK)
    -   `MId` (FK to `tblMM_PO_Master`)
    -   `PODId` (Potentially FK to a previous PO detail or PR detail)
    -   `Qty`
    -   `Rate`
    -   `Discount`
    -   `DelDate` (Delivery Date)
    -   `PF` (FK to `tblPacking_Master`)
    -   `ExST` (FK to `tblExciseser_Master`)
    -   `VAT` (FK to `tblVAT_Master`)
    -   `PRId` (FK to `tblMM_PR_Details`)
    -   `BudgetCode` (FK to `tblMIS_BudgetCode`)
    -   `AddDesc`

-   `tblMM_PO_Amd_Master` and `tblMM_PO_Amd_Details` represent historical/amended versions of the above. The logic to pick between `_Master` and `_Amd_Master` will be embedded in the `PurchaseOrder` model's report generation method.

**Key Lookup Tables (Simplified for this plan):**
-   `tblMM_Supplier_master` -> `Supplier`
    -   `SupplierId` (PK)
    -   `SupplierName`
    -   `ContactPerson`
    -   `ContactNo`
    -   `Email`
    -   `RegdAddress`
    -   `RegdCountry` (FK)
    -   `RegdState` (FK)
    -   `RegdCity` (FK)
    -   `RegdPinNo`
    -   `CompId`
-   `tblHR_OfficeStaff` -> `Employee`
    -   `EmpId` (PK)
    -   `Title`
    -   `EmployeeName`
    -   `CompId`
-   `tblCountry` -> `Country`
    -   `CId` (PK)
    -   `CountryName`
    -   `Symbol`
-   `tblState` -> `State`
    -   `SId` (PK)
    -   `StateName`
-   `tblCity` -> `City`
    -   `CityId` (PK)
    -   `CityName`
-   `tblDG_Item_Master` -> `Item`
    -   `Id` (PK)
    -   `ItemCode`
    -   `ManfDesc`
    -   `UOMBasic` (FK to `Unit_Master`)
-   `Unit_Master` -> `Unit`
    -   `Id` (PK)
    -   `Symbol`
-   `tblMM_PR_Details` -> `PurchaseRequestDetail`
    -   `Id` (PK)
    -   `PRNo`
    -   `ItemId` (FK)
    -   `AHId` (FK to `AccHead`)
    -   `MId` (FK to `tblMM_PR_Master`)
-   `tblMM_PR_Master` -> `PurchaseRequest`
    -   `Id` (PK)
    -   `WONo`
    -   `SessionId` (Employee ID for Indentor)
-   `AccHead` -> `AccountHead`
    -   `Id` (PK)
    -   `Symbol`
-   `tblMIS_BudgetCode` -> `BudgetCode`
    -   `Id` (PK)
    -   `Symbol`
-   `tblMM_PO_Reference` -> `POReference`
    -   `Id` (PK)
    -   `RefDesc`
-   `tblPacking_Master` -> `PackingTerm`
-   `tblVAT_Master` -> `VATTax`
-   `tblExciseser_Master` -> `ExciseServiceTax`
-   `tblOctroi_Master` -> `OctroiTerm`
-   `tblPayment_Master` -> `PaymentTerm`
-   `tblWarrenty_Master` -> `WarrantyTerm`
-   `tblFreight_Master` -> `FreightTerm`
    *(These last few will have `Id`, `Terms`, `Value` columns)*

## Step 2: Identify Backend Functionality

Task: Determine how the ASP.NET code's data retrieval and processing logic will map to Django's "Fat Model" approach.

## Instructions:

The ASP.NET page is a complex "Read" operation (a report generation). It does not directly perform Create, Update, or Delete on the report data itself, but aggregates data from various underlying tables.

-   **Data Retrieval & Aggregation:** The core `loaddata()` method involves fetching data from many tables, performing lookups, and calculating derived values. This entire process will be encapsulated within methods of the `PurchaseOrder` model or a dedicated `PurchaseOrderReport` helper class within `models.py`. This ensures a "fat model" and "thin view" architecture.
-   **Conditional Logic (Amendments):** The branching logic for `tblMM_PO_Master`/`tblMM_PO_Details` versus `tblMM_PO_Amd_Master`/`tblMM_PO_Amd_Details` based on `AmendmentNo` will be handled within the model's data fetching method.
-   **Comparison Logic:** The intricate logic that compares current PO values with previous amendment values (`SupplierName*`, `Rate*`, etc.) will be implemented as properties or methods on the report data objects (e.g., a `PurchaseOrderItemReportData` object) returned by the model.
-   **No Direct CRUD for Report:** The output is a formatted report. Any CRUD operations would apply to the underlying `PurchaseOrder` and `PurchaseOrderDetail` entities, not the report snapshot. We will provide Django CRUD views for `PurchaseOrder` to demonstrate the pattern.

## Step 3: Infer UI Components

Task: Analyze the ASP.NET controls and their roles, and infer their Django/HTMX/Alpine.js equivalents.

## Instructions:

-   **Crystal Report Viewer:** This is replaced by a rich HTML template (`purchaseorder/report.html`) that displays the aggregated data directly, formatted using Tailwind CSS.
-   **Query String Parameters (`mid`, `pono`, `Code`, `AmdNo`, `Key`):** These will be accessed via `request.GET` in the Django view and passed to the model's report generation method.
-   **Data Presentation:**
    -   The main PO details will be rendered as structured HTML.
    -   The line items (from `tblMM_PO_Details`) will be presented in a `DataTables` enabled HTML table for interactive filtering and sorting, if the report includes a tabular section.
-   **Dynamic Interaction:** Although a "print page" might seem static, we'll implement it with HTMX for loading the report content, and potentially a `DataTables` driven section for line items, making it part of a modern interactive interface if integrated into a dashboard. Alpine.js can manage simple UI states like modal visibility for editing underlying PO data if integrated.

## Step 4: Generate Django Code

We will create a Django application named `purchaseorder`.

### 4.1 Models

Task: Create Django models based on the identified database schema. We'll include the main `PurchaseOrder` and `PurchaseOrderDetail` models, along with a few key lookup models to demonstrate the relationships. The complex `loaddata` logic will be encapsulated in a `get_report_data` method within the `PurchaseOrder` model.

**File: `purchaseorder/models.py`**
```python
from django.db import models
from django.db.models import F # For potential comparison logic in ORM

# --- Core PO/PR Models ---
class PurchaseOrder(models.Model):
    """
    Maps to tblMM_PO_Master.
    Represents the main Purchase Order details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    supplier_id = models.IntegerField(db_column='SupplierId', blank=True, null=True) # Will be ForeignKey to Supplier
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    reference_date = models.DateTimeField(db_column='ReferenceDate', blank=True, null=True)
    reference = models.CharField(db_column='Reference', max_length=255, blank=True, null=True)
    reference_desc = models.CharField(db_column='ReferenceDesc', max_length=255, blank=True, null=True)
    amendment_no = models.IntegerField(db_column='AmendmentNo', blank=True, null=True)
    mode_of_dispatch = models.CharField(db_column='ModeOfDispatch', max_length=255, blank=True, null=True)
    inspection = models.CharField(db_column='Inspection', max_length=255, blank=True, null=True)
    remarks = models.CharField(db_column='Remarks', max_length=255, blank=True, null=True)
    ship_to = models.CharField(db_column='ShipTo', max_length=255, blank=True, null=True)
    insurance = models.CharField(db_column='Insurance', max_length=255, blank=True, null=True)
    freight_id = models.IntegerField(db_column='Freight', blank=True, null=True) # FK to tblFreight_Master
    octroi_id = models.IntegerField(db_column='Octroi', blank=True, null=True)   # FK to tblOctroi_Master
    warranty_id = models.IntegerField(db_column='Warrenty', blank=True, null=True) # FK to tblWarrenty_Master
    payment_terms_id = models.IntegerField(db_column='PaymentTerms', blank=True, null=True) # FK to tblPayment_Master
    checked_by_emp_id = models.IntegerField(db_column='CheckedBy', blank=True, null=True) # FK to tblHR_OfficeStaff
    approved_by_emp_id = models.IntegerField(db_column='ApprovedBy', blank=True, null=True) # FK to tblHR_OfficeStaff
    authorized_by_emp_id = models.IntegerField(db_column='AuthorizedBy', blank=True, null=True) # FK to tblHR_OfficeStaff
    checked_date = models.DateTimeField(db_column='CheckedDate', blank=True, null=True)
    approve_date = models.DateTimeField(db_column='ApproveDate', blank=True, null=True)
    authorize_date = models.DateTimeField(db_column='AuthorizeDate', blank=True, null=True)
    tc = models.TextField(db_column='TC', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Company ID

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return f"PO No: {self.po_no or 'N/A'} (ID: {self.id})"

    @classmethod
    def get_po_by_amendment_id(cls, po_master_id, amendment_no, company_id):
        """
        Fetches the correct PO master record based on amendment number.
        This simulates the C# logic picking between tblMM_PO_Master and tblMM_PO_Amd_Master.
        In a real scenario, tblMM_PO_Amd_Master would be a separate model.
        For simplicity, we assume amendments are stored within the main PO_Master table
        or fetched by a more complex ORM query that considers the amendment structure.
        """
        # Simplified: In reality, you'd query tblMM_PO_Amd_Master if amendment_no < current_amendment_no
        # For now, we'll just fetch the primary PO.
        # The complex `loaddata` logic would involve joining with amendment tables or
        # using a separate model for historical amendments.
        try:
            current_po = cls.objects.get(
                id=po_master_id,
                comp_id=company_id,
                # For amendment logic, you'd likely fetch from a separate AMENDMENTS_TABLE
                # and then join/compare, but for this demo, we assume current PO is base.
            )
            return current_po
        except cls.DoesNotExist:
            return None

    def get_report_data(self, company_id, current_amendment_no=None):
        """
        This method encapsulates the complex data aggregation and lookup logic
        from the original C# loaddata() function, making the model 'fat'.
        It fetches all related details and lookup values for the PO report.
        """
        report_data = {
            'po_master': self,
            'details': [],
            'supplier_info': None,
            'company_address': self._get_company_address(company_id),
            'checked_by': self._get_employee_info(self.checked_by_emp_id, company_id),
            'approved_by': self._get_employee_info(self.approved_by_emp_id, company_id),
            'authorized_by': self._get_employee_info(self.authorized_by_emp_id, company_id),
            'supplier_address': '', # Placeholder
            'reg_date_formatted': self.sys_date.strftime('%d-%m-%Y') if self.sys_date else '',
            'ref_date_formatted': self.reference_date.strftime('%d-%m-%Y') if self.reference_date else '',
            'checked_date_formatted': self.checked_date.strftime('%d-%m-%Y') if self.checked_date else '',
            'approve_date_formatted': self.approve_date.strftime('%d-%m-%Y') if self.approve_date else '',
            'authorize_date_formatted': self.authorize_date.strftime('%d-%m-%Y') if self.authorize_date else '',
            'tc_formatted': self.tc.replace('\n', '<br>') if self.tc else '', # Replace newlines for HTML
            'amendment_comparison': {}, # To hold '*' flags
        }

        # Fetch supplier info
        try:
            supplier = Supplier.objects.get(supplier_id=self.supplier_id, comp_id=company_id)
            report_data['supplier_info'] = supplier
            # Construct supplier address - simplified
            country = Country.objects.filter(id=supplier.regd_country_id).first()
            state = State.objects.filter(id=supplier.regd_state_id).first()
            city = City.objects.filter(id=supplier.regd_city_id).first()
            report_data['supplier_address'] = (
                f"{supplier.regd_address or ''}, "
                f"{city.city_name if city else ''}, "
                f"{state.state_name if state else ''}, "
                f"{country.country_name if country else ''}. "
                f"{supplier.regd_pin_no or ''}"
            ).strip(', .')
            report_data['country_symbol'] = country.symbol if country else ''

        except Supplier.DoesNotExist:
            pass # Handle gracefully if supplier not found

        # Fetch all related PO details
        po_details = self.purchaseorderdetail_set.all() # Assuming reverse relation is set up
        
        previous_po_data = {} # In a real scenario, this would be fetched from tblMM_PO_Amd_Master/Details for (AmdNo - 1)

        for detail in po_details:
            detail_data = {
                'detail': detail,
                'item_code': '',
                'manf_desc': '',
                'uom_basic': '',
                'pr_no': '',
                'wo_no': '',
                'budget_code': '',
                'indentor': '',
                'account_head_symbol': '',
                'packing_term_info': self._get_lookup_term_value(PackingTerm, detail.pf_id),
                'vat_term_info': self._get_lookup_term_value(VATTax, detail.vat_id),
                'excise_st_term_info': self._get_lookup_term_info(ExciseServiceTax, detail.exst_id),
                'octroi_term_info': self._get_lookup_term_value(OctroiTerm, detail.octroi_id),
                'payment_term_info': self._get_lookup_term_value(PaymentTerm, self.payment_terms_id), # Note: this is master level
                'warranty_term_info': self._get_lookup_term_info(WarrantyTerm, self.warranty_id), # Note: master level
                'freight_term_info': self._get_lookup_term_info(FreightTerm, self.freight_id), # Note: master level
                # Add comparison flags as properties (e.g., rate_changed, qty_changed etc.)
            }

            # Fetch Item details
            try:
                # Need to lookup PR_Details first, then Item
                pr_detail = PurchaseRequestDetail.objects.filter(id=detail.pr_id).first()
                if pr_detail:
                    detail_data['pr_no'] = pr_detail.pr_no # Assuming PRNo is directly on PRDetail or fetched via master
                    
                    if pr_detail.item_id:
                        item = Item.objects.filter(id=pr_detail.item_id).first()
                        if item:
                            detail_data['item_code'] = item.item_code # Simplified. Original has GetItemCode_PartNo
                            detail_data['manf_desc'] = item.manf_desc
                            if item.uom_basic_id:
                                unit = Unit.objects.filter(id=item.uom_basic_id).first()
                                detail_data['uom_basic'] = unit.symbol if unit else ''

                    if pr_detail.ah_id:
                        acc_head = AccountHead.objects.filter(id=pr_detail.ah_id).first()
                        detail_data['account_head_symbol'] = acc_head.symbol if acc_head else ''

                    # Fetch WONo and Indentor from PR_Master
                    pr_master = PurchaseRequest.objects.filter(id=pr_detail.master_id).first() # Assuming master_id FK
                    if pr_master:
                        detail_data['wo_no'] = pr_master.wo_no
                        if pr_master.wo_no and detail.budget_code_id:
                            budget_code_obj = BudgetCode.objects.filter(id=detail.budget_code_id).first()
                            if budget_code_obj:
                                detail_data['budget_code'] = f"{budget_code_obj.symbol}{pr_master.wo_no}"
                        
                        indentor_emp = self._get_employee_info(pr_master.indentor_emp_id, company_id) # SessionId is EmpId
                        if indentor_emp:
                            detail_data['indentor'] = f"{indentor_emp['title']}. {indentor_emp['name']}"

            except PurchaseRequestDetail.DoesNotExist:
                pass

            # Implement comparison logic here for '*' fields if previous_po_data is available
            # e.g., detail_data['rate_changed_flag'] = '*' if detail.rate != previous_po_data.get('rate') else ''

            report_data['details'].append(detail_data)

        return report_data

    # --- Helper methods (kept private to keep primary method clean) ---
    def _get_employee_info(self, emp_id, company_id):
        if emp_id:
            try:
                employee = Employee.objects.get(emp_id=emp_id, comp_id=company_id)
                return {
                    'title': employee.title,
                    'name': employee.employee_name,
                }
            except Employee.DoesNotExist:
                pass
        return None
    
    def _get_lookup_term_value(self, model_class, term_id):
        """Helper to fetch Terms and Value for PF, VAT, Excise, Octroi"""
        if term_id:
            try:
                obj = model_class.objects.get(id=term_id)
                return {'terms': obj.terms, 'value': obj.value}
            except model_class.DoesNotExist:
                pass
        return {'terms': '', 'value': 0.0}

    def _get_lookup_term_info(self, model_class, term_id):
        """Helper to fetch only Terms for Payment, Warranty, Freight"""
        if term_id:
            try:
                obj = model_class.objects.get(id=term_id)
                return {'terms': obj.terms}
            except model_class.DoesNotExist:
                pass
        return {'terms': ''}

    def _get_company_address(self, company_id):
        # This function would be a separate helper, perhaps in a 'company' model.
        # For demonstration, we'll return a static string.
        # Original: fun.CompAdd(cId)
        return "123 Company Lane, Business City, State, Country - 123456"


class PurchaseOrderDetail(models.Model):
    """
    Maps to tblMM_PO_Details.
    Represents the line items for a Purchase Order.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    master_id = models.IntegerField(db_column='MId', blank=True, null=True) # Renamed from MId for clarity, FK to PurchaseOrder
    pod_id = models.IntegerField(db_column='PODId', blank=True, null=True) # Original PODId column
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=2, blank=True, null=True)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2, blank=True, null=True)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=2, blank=True, null=True)
    del_date = models.DateTimeField(db_column='DelDate', blank=True, null=True)
    pf_id = models.IntegerField(db_column='PF', blank=True, null=True) # FK to tblPacking_Master
    exst_id = models.IntegerField(db_column='ExST', blank=True, null=True) # FK to tblExciseser_Master
    vat_id = models.IntegerField(db_column='VAT', blank=True, null=True) # FK to tblVAT_Master
    pr_id = models.IntegerField(db_column='PRId', blank=True, null=True) # FK to tblMM_PR_Details
    budget_code_id = models.IntegerField(db_column='BudgetCode', blank=True, null=True) # FK to tblMIS_BudgetCode
    add_desc = models.TextField(db_column='AddDesc', blank=True, null=True)

    # Django recommends defining explicit ForeignKey relationships if models exist
    # For a demo, we'll keep it simple as IntegerField for non-direct relations
    # purchase_order = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, db_column='MId', related_name='details')

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'Purchase Order Detail'
        verbose_name_plural = 'Purchase Order Details'

    def __str__(self):
        return f"Detail ID: {self.id} (PO: {self.master_id})"

# --- Lookup Models (Simplified for illustration) ---
class Supplier(models.Model):
    id = models.IntegerField(db_column='SupplierId', primary_key=True) # Renamed to id for consistency
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    contact_person = models.CharField(db_column='ContactPerson', max_length=255, blank=True, null=True)
    contact_no = models.CharField(db_column='ContactNo', max_length=50, blank=True, null=True)
    email = models.CharField(db_column='Email', max_length=255, blank=True, null=True)
    regd_address = models.TextField(db_column='RegdAddress', blank=True, null=True)
    regd_country_id = models.IntegerField(db_column='RegdCountry', blank=True, null=True)
    regd_state_id = models.IntegerField(db_column='RegdState', blank=True, null=True)
    regd_city_id = models.IntegerField(db_column='RegdCity', blank=True, null=True)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name

class Employee(models.Model):
    id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title}. {self.employee_name}"

class Country(models.Model):
    id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=100)
    symbol = models.CharField(db_column='Symbol', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class State(models.Model):
    id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class City(models.Model):
    id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=100)

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'

    def __str__(self):
        return self.city_name

class Item(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=255, blank=True, null=True)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    uom_basic_id = models.IntegerField(db_column='UOMBasic', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.manf_desc

class Unit(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit'
        verbose_name_plural = 'Units'

    def __str__(self):
        return self.symbol

class PurchaseRequestDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, blank=True, null=True)
    item_id = models.IntegerField(db_column='ItemId', blank=True, null=True)
    ah_id = models.IntegerField(db_column='AHId', blank=True, null=True)
    master_id = models.IntegerField(db_column='MId', blank=True, null=True) # FK to tblMM_PR_Master

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'Purchase Request Detail'
        verbose_name_plural = 'Purchase Request Details'

    def __str__(self):
        return self.pr_no or str(self.id)

class PurchaseRequest(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    indentor_emp_id = models.IntegerField(db_column='SessionId', blank=True, null=True) # Indentor field

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'Purchase Request'
        verbose_name_plural = 'Purchase Requests'
    
    def __str__(self):
        return self.wo_no or str(self.id)

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return self.symbol

class BudgetCode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return self.symbol

class POReference(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ref_desc = models.CharField(db_column='RefDesc', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Reference'
        verbose_name = 'PO Reference'
        verbose_name_plural = 'PO References'

    def __str__(self):
        return self.ref_desc

# Simplified Lookup Tables for Terms and Values (e.g., PF, VAT, Excise)
class PackingTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    value = models.DecimalField(db_column='Value', max_digits=18, decimal_places=2)
    class Meta: managed = False; db_table = 'tblPacking_Master'
class VATTax(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    value = models.DecimalField(db_column='Value', max_digits=18, decimal_places=2)
    class Meta: managed = False; db_table = 'tblVAT_Master'
class ExciseServiceTax(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    value = models.DecimalField(db_column='Value', max_digits=18, decimal_places=2)
    class Meta: managed = False; db_table = 'tblExciseser_Master'
class OctroiTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    value = models.DecimalField(db_column='Value', max_digits=18, decimal_places=2)
    class Meta: managed = False; db_table = 'tblOctroi_Master'
class PaymentTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    class Meta: managed = False; db_table = 'tblPayment_Master'
class WarrantyTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    class Meta: managed = False; db_table = 'tblWarrenty_Master'
class FreightTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    class Meta: managed = False; db_table = 'tblFreight_Master'

```

### 4.2 Forms

Task: Define a Django `ModelForm` for the `PurchaseOrder` for standard CRUD operations, if this report page is part of a larger PO management system. The report display itself does not typically use a form for its primary function.

**File: `purchaseorder/forms.py`**
```python
from django import forms
from .models import PurchaseOrder

class PurchaseOrderForm(forms.ModelForm):
    class Meta:
        model = PurchaseOrder
        fields = [
            'po_no', 'supplier_id', 'sys_date', 'reference_date', 'amendment_no',
            'mode_of_dispatch', 'inspection', 'remarks', 'ship_to', 'insurance',
            'freight_id', 'octroi_id', 'warranty_id', 'payment_terms_id', 'tc'
        ]
        widgets = {
            'po_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'reference_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'amendment_no': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'mode_of_dispatch': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'inspection': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'remarks': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'ship_to': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'insurance': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'freight_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'octroi_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'warranty_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'payment_terms_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'tc': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            # Add other fields as necessary
        }

```

### 4.3 Views

Task: Implement a specialized `PurchaseOrderReportView` to handle the data aggregation and display, adhering to the "thin view" principle by delegating logic to the model. Also, include generic CRUD views for `PurchaseOrder` as per the general migration template.

**File: `purchaseorder/views.py`**
```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, DetailView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import PurchaseOrder, PurchaseOrderDetail # Import other models as needed
from .forms import PurchaseOrderForm

# --- CRUD Views for PurchaseOrder (General Management) ---
class PurchaseOrderListView(ListView):
    model = PurchaseOrder
    template_name = 'purchaseorder/purchaseorder/list.html'
    context_object_name = 'purchaseorders'

class PurchaseOrderCreateView(CreateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'purchaseorder/purchaseorder/form.html'
    success_url = reverse_lazy('purchaseorder_list')

    def form_valid(self, form):
        # Assign company ID from session/user if needed, similar to cId in ASP.NET
        # form.instance.comp_id = self.request.session.get('compid') # Example
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

class PurchaseOrderUpdateView(UpdateView):
    model = PurchaseOrder
    form_class = PurchaseOrderForm
    template_name = 'purchaseorder/purchaseorder/form.html'
    success_url = reverse_lazy('purchaseorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

class PurchaseOrderDeleteView(DeleteView):
    model = PurchaseOrder
    template_name = 'purchaseorder/purchaseorder/confirm_delete.html'
    success_url = reverse_lazy('purchaseorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderList'
                }
            )
        return response

# --- HTMX Partial View for the DataTables (list view) ---
class PurchaseOrderTablePartialView(ListView):
    model = PurchaseOrder
    template_name = 'purchaseorder/purchaseorder/_purchaseorder_table.html'
    context_object_name = 'purchaseorders' # This should match your template's loop variable name

    def get_queryset(self):
        # Apply any filtering/searching logic if needed for the table
        return super().get_queryset()

# --- Specific Report View (Replacing Crystal Report Viewer) ---
class PurchaseOrderReportView(TemplateView):
    template_name = 'purchaseorder/purchaseorder/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Extract parameters from query string, similar to Request.QueryString in ASP.NET
        po_master_id = self.request.GET.get('mid')
        amendment_no = self.request.GET.get('AmdNo')
        # comp_id would come from session or user profile in a real Django app
        company_id = self.request.session.get('compid', 1) # Default to 1 if not in session

        if not po_master_id:
            raise Http404("Purchase Order ID (mid) is required.")

        try:
            # Use the fat model to get the report data
            po = PurchaseOrder.get_po_by_amendment_id(po_master_id, amendment_no, company_id)
            if not po:
                raise Http404(f"Purchase Order with ID {po_master_id} not found.")

            report_data = po.get_report_data(company_id, amendment_no)
            context['report_data'] = report_data
            
        except Exception as e:
            # Log the exception, show a user-friendly message
            messages.error(self.request, f"Error generating report: {e}")
            raise Http404("Report generation failed.")

        return context

```

### 4.4 Templates

Task: Create HTML templates for each view, ensuring they extend `core/base.html` and utilize HTMX/Alpine.js for dynamic interactions and DataTables for list presentation.

**File: `purchaseorder/templates/purchaseorder/purchaseorder/list.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Purchase Orders</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'purchaseorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Purchase Order
        </button>
    </div>
    
    <div id="purchaseorderTable-container"
         hx-trigger="load, refreshPurchaseOrderList from:body"
         hx-get="{% url 'purchaseorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Purchase Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI
        // Example: Alpine.data('purchaseOrderApp', () => ({ showModal: false }));
    });
</script>
{% endblock %}

```

**File: `purchaseorder/templates/purchaseorder/purchaseorder/_purchaseorder_table.html`**
```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="purchaseorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO Number</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier ID</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amendment No</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for po in purchaseorders %}
            <tr class="hover:bg-gray-50">
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ po.po_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ po.supplier_id }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ po.amendment_no }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'purchaseorder_edit' po.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'purchaseorder_delete' po.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                    <a href="{% url 'purchaseorder_report' %}?mid={{ po.pk }}&AmdNo={{ po.amendment_no }}" target="_blank"
                       class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded ml-2">
                        View Report
                    </a>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#purchaseorderTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "columnDefs": [
            { "orderable": false, "targets": [0, 4] } // Disable sorting for SN and Actions
        ]
    });
});
</script>
```

**File: `purchaseorder/templates/purchaseorder/purchaseorder/form.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Purchase Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-ext="response-targets">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**File: `purchaseorder/templates/purchaseorder/purchaseorder/confirm_delete.html`**
```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete Purchase Order "{{ object.po_no }}"?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**File: `purchaseorder/templates/purchaseorder/purchaseorder/report.html`**
```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto p-6 bg-white shadow-lg rounded-lg my-8 print:shadow-none print:my-0">
    {% with po=report_data.po_master supplier=report_data.supplier_info %}
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
            <h1 class="text-3xl font-bold mb-2">Purchase Order: {{ po.po_no }}</h1>
            <p class="text-sm text-gray-600">Company Address: {{ report_data.company_address }}</p>
        </div>
        <div class="text-right">
            <p class="text-lg font-semibold">Date: {{ report_data.reg_date_formatted }}</p>
            <p class="text-lg font-semibold">Amendment No: {{ po.amendment_no }}</p>
        </div>
    </div>

    <hr class="my-4 border-gray-300">

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
            <h2 class="text-xl font-semibold mb-2">Supplier Details</h2>
            <p><strong>Name:</strong> {{ supplier.supplier_name }}</p>
            <p><strong>Address:</strong> {{ report_data.supplier_address }}</p>
            <p><strong>Contact Person:</strong> {{ supplier.contact_person }}</p>
            <p><strong>Contact No:</strong> {{ supplier.contact_no }}</p>
            <p><strong>Email:</strong> {{ supplier.email }}</p>
            <p><strong>Country Symbol:</strong> {{ report_data.country_symbol }}</p>
            <p><strong>Supplier ID:</strong> {{ po.supplier_id }}</p>
        </div>
        <div>
            <h2 class="text-xl font-semibold mb-2">Order Details</h2>
            <p><strong>Reference:</strong> {{ po.reference }}</p>
            <p><strong>Reference Date:</strong> {{ report_data.ref_date_formatted }}</p>
            <p><strong>Reference Description:</strong> {{ po.reference_desc }}</p>
            <p><strong>Mode of Dispatch:</strong> {{ po.mode_of_dispatch }}</p>
            <p><strong>Inspection:</strong> {{ po.inspection }}</p>
            <p><strong>Ship To:</strong> {{ po.ship_to }}</p>
            <p><strong>Insurance:</strong> {{ po.insurance }}</p>
            <p><strong>Remarks:</strong> {{ po.remarks }}</p>
            <p><strong>Payment Terms:</strong> {{ report_data.payment_term_info.terms }}</p>
            <p><strong>Warranty:</strong> {{ report_data.warranty_term_info.terms }}</p>
            <p><strong>Freight:</strong> {{ report_data.freight_term_info.terms }}</p>
        </div>
    </div>

    <hr class="my-4 border-gray-300">

    <h2 class="text-xl font-semibold mb-4">Purchase Order Items</h2>
    <div class="overflow-x-auto shadow-md sm:rounded-lg mb-6">
        <table id="poDetailsTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Del Date</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PR No</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Budget Code</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Indentor</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">A/c Head</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PF</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise/ST</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">VAT</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Octroi</th>
                    <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Add. Desc</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in report_data.details %}
                <tr class="hover:bg-gray-50">
                    <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.item_code }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.manf_desc }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.detail.qty }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.uom_basic }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.detail.rate }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.detail.discount }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.detail.del_date|date:"d-m-Y" }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.pr_no }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.wo_no }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.budget_code }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.indentor }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.account_head_symbol }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.packing_term_info.terms }} ({{ item.packing_term_info.value }})</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.excise_st_term_info.terms }} ({{ item.excise_st_term_info.value }})</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.vat_term_info.terms }} ({{ item.vat_term_info.value }})</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.octroi_term_info.terms }} ({{ item.octroi_term_info.value }})</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.detail.add_desc }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <h2 class="text-xl font-semibold mb-4">Terms and Conditions</h2>
    <div class="prose max-w-none text-gray-700 border p-4 rounded-md mb-6">
        <p>{{ report_data.tc_formatted|safe }}</p> {# Use safe filter as TC contains line breaks #}
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 text-center mt-8">
        <div>
            <p class="font-semibold">{{ report_data.checked_by.title }}. {{ report_data.checked_by.name }}</p>
            <p class="text-sm text-gray-600">Checked By</p>
            <p class="text-sm text-gray-600">Date: {{ report_data.checked_date_formatted }}</p>
        </div>
        <div>
            <p class="font-semibold">{{ report_data.approved_by.title }}. {{ report_data.approved_by.name }}</p>
            <p class="text-sm text-gray-600">Approved By</p>
            <p class="text-sm text-gray-600">Date: {{ report_data.approve_date_formatted }}</p>
        </div>
        <div>
            <p class="font-semibold">{{ report_data.authorized_by.title }}. {{ report_data.authorized_by.name }}</p>
            <p class="text-sm text-gray-600">Authorized By</p>
            <p class="text-sm text-gray-600">Date: {{ report_data.authorize_date_formatted }}</p>
        </div>
    </div>
    {% endwith %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#poDetailsTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "scrollX": true, // Enable horizontal scrolling for wide tables
        "columnDefs": [
            { "orderable": false, "targets": [0] } // Disable sorting for SN
        ]
    });
});
</script>
{% endblock %}
```

### 4.5 URLs

Task: Define URL patterns for the views within the `purchaseorder` app.

**File: `purchaseorder/urls.py`**
```python
from django.urls import path
from .views import (
    PurchaseOrderListView,
    PurchaseOrderCreateView,
    PurchaseOrderUpdateView,
    PurchaseOrderDeleteView,
    PurchaseOrderTablePartialView,
    PurchaseOrderReportView,
)

urlpatterns = [
    # CRUD URLs for PurchaseOrder
    path('purchaseorder/', PurchaseOrderListView.as_view(), name='purchaseorder_list'),
    path('purchaseorder/add/', PurchaseOrderCreateView.as_view(), name='purchaseorder_add'),
    path('purchaseorder/edit/<int:pk>/', PurchaseOrderUpdateView.as_view(), name='purchaseorder_edit'),
    path('purchaseorder/delete/<int:pk>/', PurchaseOrderDeleteView.as_view(), name='purchaseorder_delete'),
    
    # HTMX partial for the DataTables
    path('purchaseorder/table/', PurchaseOrderTablePartialView.as_view(), name='purchaseorder_table'),

    # Report View URL (replaces ASP.NET print page)
    path('purchaseorder/report/', PurchaseOrderReportView.as_view(), name='purchaseorder_report'),
]

```

### 4.6 Tests

Task: Write comprehensive tests for the models and views to ensure functionality and data integrity.

**File: `purchaseorder/tests.py`**
```python
from django.test import TestCase, Client
from django.urls import reverse
from .models import (
    PurchaseOrder, PurchaseOrderDetail, Supplier, Employee, Country, State, City,
    Item, Unit, PurchaseRequestDetail, PurchaseRequest, AccountHead, BudgetCode,
    POReference, PackingTerm, VATTax, ExciseServiceTax, OctroiTerm, PaymentTerm,
    WarrantyTerm, FreightTerm
)
from datetime import datetime
from unittest.mock import patch # For mocking external dependencies like sessions

class PurchaseOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal required data for tests
        cls.company_id = 1
        
        # Lookups
        Country.objects.create(id=1, country_name='India', symbol='INR')
        State.objects.create(id=1, state_name='Maharashtra')
        City.objects.create(id=1, city_name='Mumbai')
        Employee.objects.create(id=101, title='Mr', employee_name='John Doe', comp_id=cls.company_id)
        Employee.objects.create(id=102, title='Ms', employee_name='Jane Smith', comp_id=cls.company_id)
        Employee.objects.create(id=103, title='Dr', employee_name='Bob Johnson', comp_id=cls.company_id)
        Unit.objects.create(id=1, symbol='KGS')
        AccountHead.objects.create(id=1, symbol='RAW_MAT')
        BudgetCode.objects.create(id=1, symbol='BC')
        POReference.objects.create(id=1, ref_desc='Standard PO Ref')
        
        PackingTerm.objects.create(id=1, terms='FOB', value=5.0)
        VATTax.objects.create(id=1, terms='VAT@10%', value=10.0)
        ExciseServiceTax.objects.create(id=1, terms='Excise@5%', value=5.0)
        OctroiTerm.objects.create(id=1, terms='Local Octroi', value=2.0)
        PaymentTerm.objects.create(id=1, terms='Net 30')
        WarrantyTerm.objects.create(id=1, terms='1 Year')
        FreightTerm.objects.create(id=1, terms='Paid by Buyer')

        # Core Entities
        Supplier.objects.create(
            id=1, supplier_name='Test Supplier', contact_person='Supplier Contact',
            contact_no='**********', email='<EMAIL>', regd_address='123 Supplier St',
            regd_country_id=1, regd_state_id=1, regd_city_id=1, regd_pin_no='400001',
            comp_id=cls.company_id
        )
        Item.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Description', uom_basic_id=1)
        PurchaseRequest.objects.create(id=1, wo_no='WO001', indentor_emp_id=101)
        PurchaseRequestDetail.objects.create(id=1, pr_no='PR001', item_id=1, ah_id=1, master_id=1)

        PurchaseOrder.objects.create(
            id=1, po_no='PO-TEST-001', supplier_id=1, sys_date=datetime(2023, 1, 1),
            reference_date=datetime(2023, 1, 5), amendment_no=0,
            mode_of_dispatch='Road', inspection='Before Dispatch', remarks='Urgent',
            ship_to='Warehouse A', insurance='Covered', freight_id=1, octroi_id=1,
            warranty_id=1, payment_terms_id=1, checked_by_emp_id=101, approved_by_emp_id=102,
            authorized_by_emp_id=103, checked_date=datetime(2023, 1, 6),
            approve_date=datetime(2023, 1, 7), authorize_date=datetime(2023, 1, 8),
            tc='Standard T&C', comp_id=cls.company_id
        )
        PurchaseOrderDetail.objects.create(
            id=1, master_id=1, qty=10.0, rate=100.0, discount=5.0, del_date=datetime(2023, 2, 1),
            pf_id=1, exst_id=1, vat_id=1, pr_id=1, budget_code_id=1, add_desc='Additional Notes'
        )

    def test_purchase_order_creation(self):
        po = PurchaseOrder.objects.get(id=1)
        self.assertEqual(po.po_no, 'PO-TEST-001')
        self.assertEqual(po.supplier_id, 1)
        self.assertEqual(po.comp_id, self.company_id)

    def test_purchase_order_detail_creation(self):
        po_detail = PurchaseOrderDetail.objects.get(id=1)
        self.assertEqual(po_detail.master_id, 1)
        self.assertEqual(po_detail.qty, 10.0)
        self.assertEqual(po_detail.rate, 100.0)

    def test_get_report_data_method(self):
        po = PurchaseOrder.objects.get(id=1)
        report_data = po.get_report_data(self.company_id)

        self.assertIsNotNone(report_data)
        self.assertEqual(report_data['po_master'].po_no, 'PO-TEST-001')
        self.assertIsNotNone(report_data['supplier_info'])
        self.assertEqual(report_data['supplier_info'].supplier_name, 'Test Supplier')
        self.assertIn('123 Supplier St', report_data['supplier_address'])
        self.assertEqual(report_data['details'][0]['detail'].qty, 10.0)
        self.assertEqual(report_data['details'][0]['item_code'], 'ITEM001')
        self.assertEqual(report_data['details'][0]['uom_basic'], 'KGS')
        self.assertEqual(report_data['details'][0]['pr_no'], 'PR001')
        self.assertEqual(report_data['details'][0]['wo_no'], 'WO001')
        self.assertIn('BCWO001', report_data['details'][0]['budget_code'])
        self.assertIn('Mr. John Doe', report_data['details'][0]['indentor'])
        self.assertEqual(report_data['details'][0]['account_head_symbol'], 'RAW_MAT')
        self.assertEqual(report_data['checked_by']['name'], 'John Doe')
        self.assertIn('Standard T&C', report_data['tc_formatted'])


class PurchaseOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        # Set up minimal data for view tests, mirroring model tests where relevant
        Supplier.objects.create(id=1, supplier_name='Test Supplier', comp_id=cls.company_id)
        PurchaseOrder.objects.create(
            id=1, po_no='PO-VIEW-001', supplier_id=1, amendment_no=0, comp_id=cls.company_id
        )
        PurchaseOrder.objects.create(
            id=2, po_no='PO-VIEW-002', supplier_id=1, amendment_no=1, comp_id=cls.company_id
        )
        PurchaseOrderDetail.objects.create(id=1, master_id=1, qty=5.0, rate=50.0, pr_id=1, budget_code_id=1)
        PurchaseOrderDetail.objects.create(id=2, master_id=2, qty=10.0, rate=100.0, pr_id=1, budget_code_id=1)

        # Mock essential lookup data for report view tests
        # (This avoids setting up ALL lookup tables for every test if not directly tested)
        Country.objects.create(id=1, country_name='India', symbol='INR')
        State.objects.create(id=1, state_name='Maharashtra')
        City.objects.create(id=1, city_name='Mumbai')
        Employee.objects.create(id=101, title='Mr', employee_name='John Doe', comp_id=cls.company_id)
        Employee.objects.create(id=102, title='Ms', employee_name='Jane Smith', comp_id=cls.company_id)
        Employee.objects.create(id=103, title='Dr', employee_name='Bob Johnson', comp_id=cls.company_id)
        Unit.objects.create(id=1, symbol='KGS')
        AccountHead.objects.create(id=1, symbol='RAW_MAT')
        BudgetCode.objects.create(id=1, symbol='BC')
        POReference.objects.create(id=1, ref_desc='Standard PO Ref')
        
        PackingTerm.objects.create(id=1, terms='FOB', value=5.0)
        VATTax.objects.create(id=1, terms='VAT@10%', value=10.0)
        ExciseServiceTax.objects.create(id=1, terms='Excise@5%', value=5.0)
        OctroiTerm.objects.create(id=1, terms='Local Octroi', value=2.0)
        PaymentTerm.objects.create(id=1, terms='Net 30')
        WarrantyTerm.objects.create(id=1, terms='1 Year')
        FreightTerm.objects.create(id=1, terms='Paid by Buyer')
        PurchaseRequest.objects.create(id=1, wo_no='WO001', indentor_emp_id=101)
        PurchaseRequestDetail.objects.create(id=1, pr_no='PR001', item_id=1, ah_id=1, master_id=1)
        Item.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Description', uom_basic_id=1)


    def setUp(self):
        self.client = Client()
        # Mock session for company ID
        session = self.client.session
        session['compid'] = self.company_id
        session.save()

    def test_list_view(self):
        response = self.client.get(reverse('purchaseorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchaseorder/purchaseorder/list.html')
        self.assertTrue('purchaseorders' in response.context)
        self.assertEqual(len(response.context['purchaseorders']), 2)

    def test_table_partial_view_htmx(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseorder_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchaseorder/purchaseorder/_purchaseorder_table.html')
        self.assertTrue('purchaseorders' in response.context)
        self.assertContains(response, 'PO-VIEW-001') # Check content

    def test_create_view_get(self):
        response = self.client.get(reverse('purchaseorder_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchaseorder/purchaseorder/form.html')
        self.assertTrue('form' in response.context)

    def test_create_view_post_success(self):
        data = {
            'id': 3, # New ID for the new PO
            'po_no': 'PO-NEW-003',
            'supplier_id': 1,
            'sys_date': '2023-03-01',
            'reference_date': '2023-03-02',
            'amendment_no': 0,
            'mode_of_dispatch': 'Air', 'inspection': 'None', 'remarks': 'New PO',
            'ship_to': 'Client Site', 'insurance': 'No', 'freight_id': 1, 'octroi_id': 1,
            'warranty_id': 1, 'payment_terms_id': 1, 'tc': 'New TC'
        }
        response = self.client.post(reverse('purchaseorder_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect on success
        self.assertTrue(PurchaseOrder.objects.filter(po_no='PO-NEW-003').exists())
        # Test HTMX response for create
        headers = {'HTTP_HX_REQUEST': 'true'}
        response_htmx = self.client.post(reverse('purchaseorder_add'), data, **headers)
        self.assertEqual(response_htmx.status_code, 204) # HTMX should return 204 No Content
        self.assertIn('HX-Trigger', response_htmx.headers)
        self.assertEqual(response_htmx.headers['HX-Trigger'], 'refreshPurchaseOrderList')


    def test_update_view_get(self):
        po = PurchaseOrder.objects.get(id=1)
        response = self.client.get(reverse('purchaseorder_edit', args=[po.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchaseorder/purchaseorder/form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance.po_no, 'PO-VIEW-001')

    def test_update_view_post_success(self):
        po = PurchaseOrder.objects.get(id=1)
        data = {
            'id': po.id, # Must include PK for update
            'po_no': 'PO-VIEW-001-UPDATED',
            'supplier_id': po.supplier_id,
            'sys_date': po.sys_date.strftime('%Y-%m-%d') if po.sys_date else '', # Ensure date format
            'reference_date': po.reference_date.strftime('%Y-%m-%d') if po.reference_date else '',
            'amendment_no': po.amendment_no,
            'mode_of_dispatch': po.mode_of_dispatch, 'inspection': po.inspection, 'remarks': po.remarks,
            'ship_to': po.ship_to, 'insurance': po.insurance, 'freight_id': po.freight_id, 'octroi_id': po.octroi_id,
            'warranty_id': po.warranty_id, 'payment_terms_id': po.payment_terms_id, 'tc': po.tc
        }
        response = self.client.post(reverse('purchaseorder_edit', args=[po.id]), data)
        self.assertEqual(response.status_code, 302)
        po.refresh_from_db()
        self.assertEqual(po.po_no, 'PO-VIEW-001-UPDATED')
        # Test HTMX response for update
        headers = {'HTTP_HX_REQUEST': 'true'}
        response_htmx = self.client.post(reverse('purchaseorder_edit', args=[po.id]), data, **headers)
        self.assertEqual(response_htmx.status_code, 204)
        self.assertIn('HX-Trigger', response_htmx.headers)
        self.assertEqual(response_htmx.headers['HX-Trigger'], 'refreshPurchaseOrderList')

    def test_delete_view_get(self):
        po = PurchaseOrder.objects.get(id=2) # Use a different PO for delete test
        response = self.client.get(reverse('purchaseorder_delete', args=[po.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchaseorder/purchaseorder/confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'].po_no, 'PO-VIEW-002')

    def test_delete_view_post_success(self):
        po_to_delete_id = PurchaseOrder.objects.get(id=2).id
        response = self.client.post(reverse('purchaseorder_delete', args=[po_to_delete_id]))
        self.assertEqual(response.status_code, 302)
        self.assertFalse(PurchaseOrder.objects.filter(id=po_to_delete_id).exists())
        # Test HTMX response for delete
        headers = {'HTTP_HX_REQUEST': 'true'}
        response_htmx = self.client.post(reverse('purchaseorder_delete', args=[po_to_delete_id]), **headers)
        self.assertEqual(response_htmx.status_code, 204)
        self.assertIn('HX-Trigger', response_htmx.headers)
        self.assertEqual(response_htmx.headers['HX-Trigger'], 'refreshPurchaseOrderList')


    def test_report_view_get(self):
        po = PurchaseOrder.objects.get(id=1)
        # Simulate query string parameters
        report_url = reverse('purchaseorder_report') + f'?mid={po.id}&AmdNo={po.amendment_no}'
        response = self.client.get(report_url)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'purchaseorder/purchaseorder/report.html')
        self.assertTrue('report_data' in response.context)
        self.assertIn('PO-TEST-001', response.content.decode()) # Check if PO No is present
        self.assertIn('Test Supplier', response.content.decode()) # Check if Supplier name is present
        self.assertIn('Item Code', response.content.decode()) # Check for table headers
        self.assertIn('Item Code', response.content.decode())
        self.assertIn('Test Item Description', response.content.decode())
        self.assertIn('KGS', response.content.decode())
        self.assertIn('Mr. John Doe', response.content.decode())


    def test_report_view_no_mid(self):
        response = self.client.get(reverse('purchaseorder_report'))
        self.assertEqual(response.status_code, 404) # Should raise Http404 for missing 'mid'

    def test_report_view_po_not_found(self):
        report_url = reverse('purchaseorder_report') + f'?mid=99999&AmdNo=0' # Non-existent PO ID
        response = self.client.get(report_url)
        self.assertEqual(response.status_code, 404) # Should raise Http404 for PO not found

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for Dynamic Content:**
    -   The `purchaseorder/list.html` uses `hx-get` to load `_purchaseorder_table.html` (the DataTables enabled list) dynamically on page load and whenever a `refreshPurchaseOrderList` event is triggered (after Add/Edit/Delete).
    -   `hx-get` on "Add New", "Edit", and "Delete" buttons loads the respective forms into a modal (`#modalContent`).
    -   Form submissions (`hx-post`) on `form.html` and `confirm_delete.html` trigger a `HX-Trigger` to `refreshPurchaseOrderList`, ensuring the main list updates without a full page refresh.
    -   `hx-swap="none"` and `hx-ext="response-targets"` are used on forms for better control over success/error handling and to allow the server to dictate triggers.
-   **Alpine.js for UI State:**
    -   The `id="modal"` element in `list.html` uses `_="on click add .is-active to #modal"` and `_="on click if event.target.id == 'modal' remove .is-active from me"` to toggle the modal's visibility. This provides a simple, client-side way to manage the modal's open/close state without writing custom JavaScript.
-   **DataTables for List Views:**
    -   The `_purchaseorder_table.html` partial includes a `script` block to initialize `DataTables` on the `#purchaseorderTable`. This handles client-side sorting, searching, and pagination automatically.
    -   The `report.html` also includes a `DataTables` initialization for the `#poDetailsTable` for the Purchase Order line items, allowing for interactive exploration of the report's details.
-   **No Additional JavaScript:** All dynamic interactions are managed by HTMX and Alpine.js, fulfilling the requirement to avoid custom JavaScript.
-   **DRY Templates:** `list.html` extends `core/base.html`. The forms (`form.html`, `confirm_delete.html`) and the table (`_purchaseorder_table.html`) are partial templates designed to be loaded dynamically via HTMX. The report view (`report.html`) also extends `core/base.html`.

## Final Notes

This modernization plan provides a structured, automated approach to converting your ASP.NET "PO/PR Print Page" to a modern Django application.

-   **Business Value:** By migrating to Django, you gain a more agile, maintainable, and scalable platform. The fat model approach centralizes business logic, reducing code duplication and making future modifications easier. The use of HTMX, Alpine.js, and DataTables ensures a responsive and intuitive user experience without the overhead of complex JavaScript frameworks. This leads to faster feature development, lower long-term maintenance costs, and an improved foundation for future digital transformation initiatives.
-   **Replace Placeholders:** Remember to replace placeholder values like `[APP_NAME]` (used `purchaseorder`), and actual table/column names if they differ in your specific database schema. The `CompId` should be handled dynamically from the user's session or profile in a real application.
-   **Database Consistency:** Ensure your Django models accurately reflect your existing SQL Server database schema, paying close attention to data types and primary/foreign keys.
-   **Extend Functionality:** The `PurchaseOrder.get_report_data()` method is a starting point. It would need further refinement to fully replicate all the complex lookups and comparison logic from your original `loaddata()` function, especially for the amendment comparison (`*` fields).
-   **Error Handling:** Implement robust error handling and logging in your Django views and models for production readiness.
-   **Deployment:** Consider using modern deployment strategies (e.g., Docker, Kubernetes) for your Django application to further enhance scalability and reliability.