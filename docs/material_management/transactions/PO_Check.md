```markdown
## ASP.NET to Django Conversion Script: PO Check Module

This document outlines the modernization plan for the ASP.NET "PO Check" module, transitioning it to a modern Django-based solution. Our focus is on leveraging AI-assisted automation to streamline the migration, emphasizing a "fat model, thin view" architecture, HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience.

### Business Value of Django Modernization:

*   **Improved Performance:** Django's optimized ORM and efficient request handling, combined with HTMX for partial updates, will significantly reduce page load times and server strain compared to legacy ASP.NET PostBacks.
*   **Enhanced User Experience:** HTMX and Alpine.js provide a rich, responsive interface without full page reloads, making the application feel faster and more intuitive. DataTables offers advanced client-side search, sort, and pagination capabilities.
*   **Reduced Development Costs:** Adhering to Django's "Don't Repeat Yourself" (DRY) principle, standardized patterns, and clear separation of concerns (models for logic, views for coordination, templates for UI) simplifies future development, maintenance, and debugging.
*   **Scalability & Maintainability:** Django's robust framework and modular structure ensure the application can scale with business needs and is easier to maintain and extend with new features.
*   **Modern Technology Stack:** Moving to Django, Python, HTMX, and Alpine.js aligns the application with contemporary web development best practices, making it attractive for new talent and future integrations.
*   **Automation-Ready:** The structured, component-based approach makes the migration process highly amenable to AI-assisted code generation and automated testing, significantly accelerating the transition and reducing manual effort.

---

## Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, we identify the following key tables and their inferred columns. Note that `Id` often refers to the primary key, and other IDs like `SupplierId`, `EmpId`, `FinYearId` are likely foreign keys.

*   **Main Table:** `tblMM_PO_Master`
    *   `Id` (Primary Key, integer)
    *   `PONo` (Purchase Order Number, string)
    *   `AmendmentNo` (Amendment Number, string)
    *   `SysDate` (System Date, datetime)
    *   `SessionId` (Generated By User Session ID, integer - links to `tblHR_OfficeStaff.EmpId`)
    *   `Checked` (Boolean/Integer 0/1, indicates if checked)
    *   `CheckedDate` (Date checked, datetime)
    *   `CheckedBy` (User ID who checked, string/integer)
    *   `CheckedTime` (Time checked, string)
    *   `Approve` (Boolean/Integer 0/1)
    *   `ApproveDate` (Date approved, datetime)
    *   `Authorize` (Boolean/Integer 0/1)
    *   `AuthorizeDate` (Date authorized, datetime)
    *   `SupplierId` (Supplier ID, string/integer - links to `tblMM_Supplier_master.SupplierId`)
    *   `FinYearId` (Financial Year ID, string/integer - links to `tblFinancial_master.FinYearId`)
    *   `CompId` (Company ID, integer - assumed current company context)
    *   `PRSPRFlag` (PR/SPR Flag, string)

*   **Related Table 1:** `tblHR_OfficeStaff`
    *   `EmpId` (Employee ID, integer)
    *   `Title` (Employee Title, string)
    *   `EmployeeName` (Employee Name, string)

*   **Related Table 2:** `tblMM_Supplier_master`
    *   `SupplierId` (Supplier ID, string/integer)
    *   `SupplierName` (Supplier Name, string)

*   **Related Table 3:** `tblFinancial_master`
    *   `FinYearId` (Financial Year ID, string/integer)
    *   `FinYear` (Financial Year Name, string)

### Step 2: Identify Backend Functionality

The ASP.NET code primarily performs `Read` and `Update` operations for Purchase Orders, along with search and external navigation.

*   **Read (R):**
    *   Displays a paginated list of Purchase Orders (`GridView2`).
    *   Filters can be applied by "Supplier" or "PO No".
    *   Pulls associated data from `tblHR_OfficeStaff` (Generated By), `tblMM_Supplier_master` (Supplier Name, Code), and `tblFinancial_master` (Financial Year).
    *   Includes an autocomplete feature for supplier search.
*   **Update (U):**
    *   Allows marking selected Purchase Orders as "Checked". This updates the `Checked`, `CheckedBy`, `CheckedDate`, and `CheckedTime` fields in `tblMM_PO_Master`.
*   **Navigation:**
    *   "View" link on each row redirects to separate detailed view pages (`PO_PR_View_Print_Details.aspx` or `PO_SPR_View_Print_Details.aspx`). This functionality will be preserved as external links in Django.
*   **Validation:** Basic client-side confirmation for the "Checked" action.

### Step 3: Infer UI Components

The ASP.NET UI uses standard web controls. In Django, these will be replaced by HTML forms, DataTables, HTMX for dynamic interactions, and Alpine.js for UI state.

*   **Search Section:**
    *   `drpfield` (DropDownList): Replaced by an HTML `<select>` with Alpine.js to control visibility of search input fields.
    *   `txtSupplier` (TextBox with AutoCompleteExtender): Replaced by an HTML `<input type="text">` with HTMX for `hx-get` to a supplier autocomplete endpoint.
    *   `txtPONo` (TextBox): Replaced by an HTML `<input type="text">`.
    *   `Button1` (Search Button): Replaced by an HTML `<button>` with HTMX `hx-get` to refresh the table.
*   **Actions Section:**
    *   `Check` (Button): Replaced by an HTML `<button>` with HTMX `hx-post` to submit selected IDs for checking. A client-side confirmation (Alpine.js) will precede the HTMX request.
*   **Data Display:**
    *   `GridView2`: Replaced by a `<table>` enhanced with DataTables for client-side pagination, sorting, and searching. Each row will contain labels and a checkbox (`CK`).
    *   `LinkButton` for "View": Replaced by an HTML `<a>` tag.
*   **Container:** `Panel1` (scrollable) will be a simple `div` with CSS styling.

---

## Step 4: Generate Django Code

We will create a new Django application, let's call it `material_management`, to house this module.

### 4.1 Models (`material_management/models.py`)

This file defines the Django ORM models, mapping directly to your existing database tables. We'll use `managed = False` to ensure Django doesn't try to create/alter these tables, and `db_table` to specify the exact table names. Critical business logic, like getting related names or marking as checked, will be moved here.

```python
from django.db import models
from django.utils import timezone
from datetime import datetime

# Helper for parsing the date/time format used in ASP.NET
def parse_asp_date_time(date_str, time_str):
    if not date_str:
        return None
    try:
        # Assuming date_str is DMY format (e.g., "01-01-2023")
        # Assuming time_str is HH:MM:SS (e.g., "14:30:00")
        combined_str = f"{date_str} {time_str}"
        return datetime.strptime(combined_str, '%d-%m-%Y %H:%M:%S')
    except ValueError:
        return None

# Model for tblHR_OfficeStaff
class Employee(models.Model):
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    title = models.CharField(db_column='Title', max_length=100, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name or ''}".strip()

# Model for tblMM_Supplier_master
class Supplier(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50) # Assuming it's a code/string
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or ''

# Model for tblFinancial_master
class FinancialYear(models.Model):
    fin_year_id = models.CharField(db_column='FinYearId', primary_key=True, max_length=50) # Assuming it's a string ID
    fin_year = models.CharField(db_column='FinYear', max_length=100, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or ''

# Model for tblMM_PO_Master
class PurchaseOrder(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    amendment_no = models.CharField(db_column='AmendmentNo', max_length=50, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    session_id = models.ForeignKey(Employee, models.DO_NOTHING, db_column='SessionId', blank=True, null=True) # Renamed from SessionId to session_id to reflect FK
    checked = models.BooleanField(db_column='Checked', default=False)
    checked_date = models.DateTimeField(db_column='CheckedDate', blank=True, null=True)
    checked_by = models.CharField(db_column='CheckedBy', max_length=50, blank=True, null=True) # Store username
    checked_time = models.CharField(db_column='CheckedTime', max_length=20, blank=True, null=True) # Time stored as string in ASP.NET
    approve = models.BooleanField(db_column='Approve', default=False)
    approve_date = models.DateTimeField(db_column='ApproveDate', blank=True, null=True)
    authorize = models.BooleanField(db_column='Authorize', default=False)
    authorize_date = models.DateTimeField(db_column='AuthorizeDate', blank=True, null=True)
    supplier_id = models.ForeignKey(Supplier, models.DO_NOTHING, db_column='SupplierId', blank=True, null=True) # Renamed to supplier_id to reflect FK
    fin_year_id = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYearId', blank=True, null=True) # Renamed to fin_year_id to reflect FK
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True) # Company ID
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'
        ordering = ['-id'] # Matches 'Order by Id Desc'

    def __str__(self):
        return self.po_no or f"PO {self.id}"

    # Business logic methods (Fat Model approach)
    @property
    def formatted_sys_date(self):
        return self.sys_date.strftime('%d-%m-%Y') if self.sys_date else ''

    @property
    def formatted_checked_date(self):
        return self.checked_date.strftime('%d-%m-%Y') if self.checked_date else ''

    @property
    def formatted_approved_date(self):
        return self.approve_date.strftime('%d-%m-%Y') if self.approve_date else ''

    @property
    def formatted_authorized_date(self):
        return self.authorize_date.strftime('%d-%m-%Y') if self.authorize_date else ''

    @classmethod
    def mark_pos_as_checked(cls, po_ids, username, comp_id):
        """
        Marks multiple Purchase Orders as 'checked'.
        This method replaces the logic in Check_Click and GridView2_RowCommand.
        """
        now = timezone.now()
        current_date = now.strftime('%d-%m-%Y') # Matches fun.getCurrDate() format
        current_time = now.strftime('%H:%M:%S') # Matches fun.getCurrTime() format

        updated_count = cls.objects.filter(
            id__in=po_ids,
            comp_id=comp_id,
            checked=False # Only update if not already checked
        ).update(
            checked=True,
            checked_by=username,
            checked_date=now,
            checked_time=current_time
        )
        return updated_count

    @classmethod
    def get_po_list_for_display(cls, fin_year_id, comp_id, search_type=None, search_query=None):
        """
        Retrieves a filtered list of Purchase Orders for display.
        This method encapsulates the makegrid logic.
        """
        queryset = cls.objects.select_related(
            'session_id', 'supplier_id', 'fin_year_id'
        ).filter(
            fin_year_id__fin_year_id__lte=fin_year_id, # ASP.NET: FinYearId<='FyId'
            comp_id=comp_id,
            checked=False # Only show unchecked POs
        )

        if search_type == '1' and search_query: # PO No search
            queryset = queryset.filter(po_no__iexact=search_query)
        elif search_type == '0' and search_query: # Supplier search
            # SupplierId is the code, so we need to get the code from the name
            # Assuming search_query is the supplier name, we need to find its ID
            supplier_code = None
            try:
                # The autocomplete returns "SupplierName [SupplierId]"
                # So we need to extract the ID
                if '[' in search_query and ']' in search_query:
                    parts = search_query.split('[')
                    supplier_code = parts[-1].rstrip(']')
                else:
                    # Fallback if no code, try to find by name directly
                    supplier = Supplier.objects.filter(supplier_name__iexact=search_query).first()
                    if supplier:
                        supplier_code = supplier.supplier_id

                if supplier_code:
                    queryset = queryset.filter(supplier_id__supplier_id=supplier_code)
                else:
                    # If no supplier code found, return empty queryset
                    return cls.objects.none()
            except Exception:
                return cls.objects.none() # Handle parsing errors

        return queryset
```

### 4.2 Forms (`material_management/forms.py`)

We'll define a simple form for the search functionality.

```python
from django import forms

class POSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Supplier'),
        ('1', 'PO No'),
    ]
    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        initial='0',
        widget=forms.Select(attrs={'class': 'box3 block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'hx-trigger': 'change', 'hx-post': '/material_management/po_check/toggle_search_fields/'}), # HTMX to toggle fields
        label="Search By"
    )
    supplier_search_query = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter Supplier Name',
            'id': 'txtSupplier', # Match old ID for Alpine.js visibility
            'hx-get': '/material_management/po_check/supplier_autocomplete/',
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-autocomplete-results',
            'hx-indicator': '#autocomplete-indicator',
            'autocomplete': 'off',
        }),
        label="Supplier"
    )
    po_no_search_query = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter PO Number',
            'id': 'txtPONo', # Match old ID for Alpine.js visibility
        }),
        label="PO No"
    )

```

### 4.3 Views (`material_management/views.py`)

Views will be thin, primarily delegating logic to models and handling HTTP responses, especially for HTMX interactions.

```python
from django.views.generic import ListView, View, RedirectView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect
from django.db.models import F # For F expressions if needed for complex updates

from .models import PurchaseOrder, Supplier, Employee # Import all relevant models
from .forms import POSearchForm

# Assume 'core' app handles session/company ID
# In a real app, this would be handled by middleware or a base view mixin
# For this example, we'll hardcode or mock session/comp_id
MOCK_FIN_YEAR_ID = '2023-2024' # Example financial year ID
MOCK_COMP_ID = 1 # Example company ID
MOCK_USERNAME = 'admin' # Example username for CheckedBy

class PurchaseOrderListView(ListView):
    model = PurchaseOrder
    template_name = 'material_management/po_check/list.html'
    context_object_name = 'purchase_orders' # This will hold the initial (empty) list

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['search_form'] = POSearchForm(self.request.GET)
        return context

    # This view doesn't directly return the table data, it sets up the page.
    # The table data is loaded via HTMX into _po_master_table.html partial.

class PurchaseOrderTablePartialView(View):
    """
    Returns the HTML partial for the Purchase Order DataTables content.
    This view is loaded via HTMX.
    """
    def get(self, request, *args, **kwargs):
        fin_year_id = MOCK_FIN_YEAR_ID # Get from session/user context in real app
        comp_id = MOCK_COMP_ID # Get from session/user context in real app

        search_type = request.GET.get('search_type')
        supplier_search_query = request.GET.get('supplier_search_query')
        po_no_search_query = request.GET.get('po_no_search_query')

        # Use the model method to get the filtered queryset
        purchase_orders = PurchaseOrder.get_po_list_for_display(
            fin_year_id,
            comp_id,
            search_type=search_type,
            search_query=supplier_search_query if search_type == '0' else po_no_search_query
        )

        return self.render_to_response(
            {'purchase_orders': purchase_orders},
            template_name='material_management/po_check/_po_master_table.html'
        )

    def render_to_response(self, context, **kwargs):
        # Helper to render template response, common in Django views
        from django.template.loader import render_to_string
        html = render_to_string(kwargs['template_name'], context, request=self.request)
        return HttpResponse(html)


class PurchaseOrderCheckView(View):
    """
    Handles marking selected Purchase Orders as 'checked'.
    Receives POST request from HTMX.
    """
    def post(self, request, *args, **kwargs):
        po_ids_to_check = request.POST.getlist('po_ids[]') # Get list of IDs from checkboxes
        current_username = MOCK_USERNAME # Get from session/request.user in real app
        current_comp_id = MOCK_COMP_ID # Get from session/request.user in real app

        if not po_ids_to_check:
            messages.warning(request, "No records found to mark as checked.")
            return HttpResponse(status=200) # Return 200 with message if no IDs

        updated_count = PurchaseOrder.mark_pos_as_checked(
            po_ids_to_check, current_username, current_comp_id
        )

        if updated_count > 0:
            messages.success(request, f"{updated_count} Purchase Order(s) marked as checked successfully.")
            # Trigger HTMX to reload the table
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshPoList'})
        else:
            messages.info(request, "No Purchase Orders were updated (they might already be checked).")
            return HttpResponse(status=200)

class SupplierAutocompleteView(View):
    """
    Provides JSON data for supplier autocomplete feature.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        # In ASP.NET, it filters by startsWith, case-insensitive.
        # It also returns "SupplierName [SupplierId]"
        suppliers = Supplier.objects.filter(
            supplier_name__istartswith=query
        ).values('supplier_id', 'supplier_name')[:10] # Limit results

        results = []
        for s in suppliers:
            results.append(f"{s['supplier_name']} [{s['supplier_id']}]")

        return JsonResponse(results, safe=False)

class ToggleSearchFieldsView(View):
    """
    A simple HTMX view to return the appropriate input field partial
    based on the search type selected.
    """
    def post(self, request, *args, **kwargs):
        search_type = request.POST.get('search_type')
        if search_type == '1': # PO No selected
            template_name = 'material_management/po_check/_po_no_search_field.html'
        else: # Supplier selected (default)
            template_name = 'material_management/po_check/_supplier_search_field.html'

        return self.render_to_response({}, template_name=template_name)

    def render_to_response(self, context, **kwargs):
        from django.template.loader import render_to_string
        html = render_to_string(kwargs['template_name'], context, request=self.request)
        return HttpResponse(html)


class PurchaseOrderViewDetailsRedirectView(RedirectView):
    """
    Redirects to the external PO/PR/SPR view details page, replicating the ASP.NET logic.
    """
    permanent = False
    query_string = True # Preserve query string from incoming request

    def get_redirect_url(self, *args, **kwargs):
        po_id = kwargs.get('pk')
        po_no = kwargs.get('po_no')
        supplier_code = kwargs.get('supplier_code')
        amendment_no = kwargs.get('amendment_no')

        # Fetch PRSPRFlag from the database
        try:
            po = PurchaseOrder.objects.get(
                id=po_id,
                po_no=po_no,
                supplier_id__supplier_id=supplier_code, # Query by FK's field
                amendment_no=amendment_no,
                comp_id=MOCK_COMP_ID # Assuming comp_id is part of the lookup
            )
            pr_spr_flag = po.pr_spr_flag
        except PurchaseOrder.DoesNotExist:
            messages.error(self.request, "Purchase Order not found for details view.")
            return reverse_lazy('po_check_list') # Redirect back to list or error page

        # Hardcode some values as per ASP.NET original
        random_key = "some_random_key" # fun.GetRandomAlphaNumeric()
        mod_id = 6
        sub_mod_id = 35
        parent_page = "PO_Check.aspx" # Original parent page name

        base_url = "PO_PR_View_Print_Details.aspx" if pr_spr_flag == "0" else "PO_SPR_View_Print_Details.aspx"
        
        # Build query parameters
        params = {
            'mid': po_id,
            'pono': po_no,
            'Code': supplier_code,
            'AmdNo': amendment_no,
            'Key': random_key,
            'ModId': mod_id,
            'SubModId': sub_mod_id,
            'parentpage': parent_page
        }

        if pr_spr_flag == "0":
            params['Trans'] = 'some_transaction_value' # The ASP.NET code shows Trans is an empty string here, if(DSFlag.Tables[0].Rows[0][0].ToString() == "0") { string Trans = string.Empty; ... Response.Redirect("PO_PR_View_Print_Details.aspx?mid=" + Id + "&pono=" + pocode + "&Code=" + supcode + "&AmdNo=" + AmdNo + "&Key=" + getRandomKey + "&Trans=" + Trans + "&ModId=6&SubModId=35&parentpage=" + parentPage); }

        # Construct the full external URL
        from urllib.parse import urlencode
        external_url = f"/{base_url}?{urlencode(params)}" # Assuming ASP.NET app is at root
        return external_url

```

### 4.4 Templates

Templates will be structured into a main list view and several partials loaded via HTMX, ensuring a dynamic and efficient user interface.

**`material_management/templates/material_management/po_check/list.html`**

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">PO Check</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <form hx-get="{% url 'po_check_table' %}" hx-target="#po-table-container" hx-swap="innerHTML" hx-indicator="#loading-indicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4" x-data="{ searchType: '{{ search_form.search_type.value }}' }">
                <div>
                    <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.search_type.label }}
                    </label>
                    <select id="{{ search_form.search_type.id_for_label }}" name="{{ search_form.search_type.html_name }}"
                            class="{{ search_form.search_type.widget.attrs.class }}"
                            x-model="searchType"
                            hx-post="{% url 'toggle_search_fields' %}"
                            hx-target="#search-input-container"
                            hx-swap="innerHTML"
                            hx-indicator="#loading-indicator">
                        {% for value, label in search_form.search_type.field.choices %}
                            <option value="{{ value }}" {% if value == search_form.search_type.value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <div id="search-input-container">
                    <!-- HTMX will swap content here based on search_type -->
                    {% if search_form.search_type.value == '1' %}
                        {% include 'material_management/po_check/_po_no_search_field.html' with field=search_form.po_no_search_query %}
                    {% else %}
                        {% include 'material_management/po_check/_supplier_search_field.html' with field=search_form.supplier_search_query %}
                    {% endif %}
                </div>
                
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox" hx-indicator="#loading-indicator">
                        Search
                    </button>
                    <img id="loading-indicator" class="htmx-indicator h-8 w-8 ml-3" src="{% static 'img/loading.svg' %}" alt="Loading...">
                </div>
            </div>
        </form>
    </div>

    <!-- Alert for messages -->
    {% if messages %}
        <div id="messages" class="mb-4">
            {% for message in messages %}
            <div class="p-3 mb-2 {% if message.tags == 'success' %}bg-green-100 text-green-700{% elif message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-700{% else %}bg-blue-100 text-blue-700{% endif %} rounded-md"
                 x-data="{ show: true }" x-init="setTimeout(() => show = false, 5000)" x-show="show" x-transition>
                {{ message }}
            </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <div class="mb-4 flex justify-end">
            <button
                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded redbox"
                hx-post="{% url 'po_check_check' %}"
                hx-confirm="Are you sure you want to mark selected POs as checked?"
                hx-include="#po-table-container input[type='checkbox']:checked"
                hx-swap="none"
                hx-indicator="#loading-indicator"
                _="on htmx:afterRequest if event.detail.xhr.status == 204 trigger refreshPoList">
                Mark Selected as Checked
            </button>
        </div>

        <div id="po-table-container"
             hx-trigger="load, refreshPoList from:body"
             hx-get="{% url 'po_check_table' %}"
             hx-swap="innerHTML">
            <!-- Initial content or loading indicator -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-lg text-gray-600">Loading Purchase Orders...</p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<!-- Add any specific Alpine.js components or HTMX logic -->
<script>
    // Alpine.js for showing/hiding search fields.
    // The hx-post on the select field already handles the content swap,
    // but Alpine can be used for local UI state or more complex interactions.
    // Example: If search type is 'PO No', hide supplier input and show PO No input
    document.addEventListener('alpine:init', () => {
        Alpine.data('poCheckPage', () => ({
            searchType: '{{ search_form.search_type.value }}',
            init() {
                // Initialize the correct field visibility on page load
                this.updateFieldVisibility();
            },
            updateFieldVisibility() {
                const supplierField = document.getElementById('txtSupplier');
                const poNoField = document.getElementById('txtPONo');

                if (supplierField) supplierField.style.display = (this.searchType === '0' ? 'block' : 'none');
                if (poNoField) poNoField.style.display = (this.searchType === '1' ? 'block' : 'none');
            }
        }));
    });

    // Custom HTMX listener for DataTables reinitialization after table partial swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'po-table-container') {
            // Re-initialize DataTable if the new content contains the table
            if ($.fn.DataTable.isDataTable('#poTable')) {
                $('#poTable').DataTable().destroy(); // Destroy existing instance
            }
            $('#poTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "pagingType": "full_numbers"
            });
        }
    });

    // Client-side confirmation for "Mark Selected as Checked" (old ASP.NET had OnClientClick)
    // HTMX hx-confirm can replace this, but if complex logic is needed, Alpine.js could manage a modal.
    // For now, hx-confirm is sufficient.
</script>
{% endblock %}
```

**`material_management/templates/material_management/po_check/_po_master_table.html`** (Partial for DataTables)

```html
{% load static %}
<div class="overflow-x-auto">
    <table id="poTable" class="min-w-full bg-white yui-datatable-theme">
        <thead>
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">View</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amd No</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen By</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For Checking</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized</th>
            </tr>
        </thead>
        <tbody>
            {% for po in purchase_orders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.fin_year_id.fin_year }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <a href="{% url 'po_check_view_details' pk=po.id po_no=po.po_no supplier_code=po.supplier_id.supplier_id amendment_no=po.amendment_no %}" class="text-blue-600 hover:text-blue-800">View</a>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.amendment_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.formatted_sys_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.session_id.employee_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left">{{ po.supplier_id.supplier_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.supplier_id.supplier_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    {% if po.checked %}
                        {{ po.formatted_checked_date }}
                    {% else %}
                        <input type="checkbox" name="po_ids[]" value="{{ po.id }}" class="form-checkbox h-4 w-4 text-blue-600">
                    {% endif %}
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.formatted_approved_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.formatted_authorized_date }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization (this script is re-run after each HTMX swap)
    $(document).ready(function() {
        if ($.fn.DataTable.isDataTable('#poTable')) {
            $('#poTable').DataTable().destroy(); // Destroy existing instance if any
        }
        $('#poTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "pagingType": "full_numbers"
        });
    });
</script>
```

**`material_management/templates/material_management/po_check/_supplier_search_field.html`**

```html
<div>
    <label for="txtSupplier" class="block text-sm font-medium text-gray-700">Supplier</label>
    <input type="text" name="supplier_search_query" id="txtSupplier" 
           value="{{ search_form.supplier_search_query.value|default:'' }}"
           class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
           placeholder="Enter Supplier Name"
           hx-get="{% url 'supplier_autocomplete' %}"
           hx-trigger="keyup changed delay:300ms, search"
           hx-target="#supplier-autocomplete-results"
           hx-swap="innerHTML"
           autocomplete="off">
    <div id="supplier-autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto w-full"></div>
    {% if search_form.supplier_search_query.errors %}
        <p class="text-red-500 text-xs mt-1">{{ search_form.supplier_search_query.errors }}</p>
    {% endif %}
</div>

<script>
    // Alpine.js for autocomplete selection if needed (not strictly HTMX, but good for UX)
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'supplier-autocomplete-results') {
            document.querySelectorAll('#supplier-autocomplete-results div').forEach(item => {
                item.onclick = function() {
                    document.getElementById('txtSupplier').value = this.textContent.trim();
                    document.getElementById('supplier-autocomplete-results').innerHTML = '';
                };
            });
        }
    });
</script>
```

**`material_management/templates/material_management/po_check/_po_no_search_field.html`**

```html
<div>
    <label for="txtPONo" class="block text-sm font-medium text-gray-700">PO No</label>
    <input type="text" name="po_no_search_query" id="txtPONo"
           value="{{ search_form.po_no_search_query.value|default:'' }}"
           class="box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
           placeholder="Enter PO Number">
    {% if search_form.po_no_search_query.errors %}
        <p class="text-red-500 text-xs mt-1">{{ search_form.po_no_search_query.errors }}</p>
    {% endif %}
</div>
```

### 4.5 URLs (`material_management/urls.py`)

This file defines the URL routing for the `material_management` app.

```python
from django.urls import path
from .views import (
    PurchaseOrderListView,
    PurchaseOrderTablePartialView,
    PurchaseOrderCheckView,
    SupplierAutocompleteView,
    ToggleSearchFieldsView,
    PurchaseOrderViewDetailsRedirectView,
)

urlpatterns = [
    # Main page for PO Check
    path('po_check/', PurchaseOrderListView.as_view(), name='po_check_list'),
    
    # HTMX endpoint for the DataTable content
    path('po_check/table/', PurchaseOrderTablePartialView.as_view(), name='po_check_table'),
    
    # HTMX endpoint for marking POs as checked
    path('po_check/check/', PurchaseOrderCheckView.as_view(), name='po_check_check'),
    
    # HTMX endpoint for supplier autocomplete
    path('po_check/supplier_autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # HTMX endpoint for toggling search input fields
    path('po_check/toggle_search_fields/', ToggleSearchFieldsView.as_view(), name='toggle_search_fields'),

    # Redirect view for "View" link (external ASP.NET application)
    # The parameters in the path should match the ones retrieved in the view's get_redirect_url
    path('po_check/<int:pk>/<str:po_no>/<str:supplier_code>/<str:amendment_no>/view/', 
         PurchaseOrderViewDetailsRedirectView.as_view(), name='po_check_view_details'),
]

```

### 4.6 Tests (`material_management/tests.py`)

Comprehensive unit tests for models and integration tests for views ensure the correctness and robustness of the migrated functionality.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import PurchaseOrder, Employee, Supplier, FinancialYear
from .views import MOCK_COMP_ID, MOCK_FIN_YEAR_ID, MOCK_USERNAME

class ModelTestSetupMixin(TestCase):
    """
    Helper mixin to set up common test data for models and views.
    """
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.employee = Employee.objects.create(
            emp_id=1, title='Mr.', employee_name='John Doe'
        )
        cls.supplier_abc = Supplier.objects.create(
            supplier_id='SUP001', supplier_name='ABC Corp'
        )
        cls.supplier_xyz = Supplier.objects.create(
            supplier_id='SUP002', supplier_name='XYZ Ltd'
        )
        cls.fin_year = FinancialYear.objects.create(
            fin_year_id=MOCK_FIN_YEAR_ID, fin_year='2023-2024'
        )

        # Create test PurchaseOrder instances
        cls.po1 = PurchaseOrder.objects.create(
            id=101,
            po_no='PO-001',
            amendment_no='0',
            sys_date=timezone.now(),
            session_id=cls.employee,
            checked=False,
            supplier_id=cls.supplier_abc,
            fin_year_id=cls.fin_year,
            comp_id=MOCK_COMP_ID,
            pr_spr_flag='0' # PR type
        )
        cls.po2 = PurchaseOrder.objects.create(
            id=102,
            po_no='PO-002',
            amendment_no='0',
            sys_date=timezone.now(),
            session_id=cls.employee,
            checked=False,
            supplier_id=cls.supplier_xyz,
            fin_year_id=cls.fin_year,
            comp_id=MOCK_COMP_ID,
            pr_spr_flag='1' # SPR type
        )
        cls.po_checked = PurchaseOrder.objects.create(
            id=103,
            po_no='PO-003',
            amendment_no='0',
            sys_date=timezone.now(),
            session_id=cls.employee,
            checked=True, # Already checked
            checked_date=timezone.now(),
            checked_by='previous_user',
            checked_time='10:00:00',
            supplier_id=cls.supplier_abc,
            fin_year_id=cls.fin_year,
            comp_id=MOCK_COMP_ID,
            pr_spr_flag='0'
        )

class PurchaseOrderModelTest(ModelTestSetupMixin, TestCase):

    def test_purchase_order_creation(self):
        po = PurchaseOrder.objects.get(id=self.po1.id)
        self.assertEqual(po.po_no, 'PO-001')
        self.assertFalse(po.checked)
        self.assertEqual(po.session_id.employee_name, 'John Doe')
        self.assertEqual(po.supplier_id.supplier_name, 'ABC Corp')
        self.assertEqual(po.fin_year_id.fin_year, '2023-2024')

    def test_formatted_date_properties(self):
        po = PurchaseOrder.objects.get(id=self.po1.id)
        self.assertEqual(po.formatted_sys_date, po.sys_date.strftime('%d-%m-%Y'))
        self.assertEqual(po.formatted_checked_date, '') # Not checked yet

        po_checked = PurchaseOrder.objects.get(id=self.po_checked.id)
        self.assertEqual(po_checked.formatted_checked_date, po_checked.checked_date.strftime('%d-%m-%Y'))

    def test_mark_pos_as_checked(self):
        # Mark po1 as checked
        updated_count = PurchaseOrder.mark_pos_as_checked(
            [self.po1.id], MOCK_USERNAME, MOCK_COMP_ID
        )
        self.assertEqual(updated_count, 1)

        po1_after_check = PurchaseOrder.objects.get(id=self.po1.id)
        self.assertTrue(po1_after_check.checked)
        self.assertEqual(po1_after_check.checked_by, MOCK_USERNAME)
        self.assertIsNotNone(po1_after_check.checked_date)
        self.assertIsNotNone(po1_after_check.checked_time)

        # Try to mark an already checked PO
        updated_count_already_checked = PurchaseOrder.mark_pos_as_checked(
            [self.po_checked.id], MOCK_USERNAME, MOCK_COMP_ID
        )
        self.assertEqual(updated_count_already_checked, 0) # Should not update

        # Mark multiple POs
        po3 = PurchaseOrder.objects.create(
            id=104, po_no='PO-004', sys_date=timezone.now(),
            session_id=self.employee, checked=False, supplier_id=self.supplier_abc,
            fin_year_id=self.fin_year, comp_id=MOCK_COMP_ID, pr_spr_flag='0'
        )
        updated_count_multi = PurchaseOrder.mark_pos_as_checked(
            [self.po2.id, po3.id], MOCK_USERNAME, MOCK_COMP_ID
        )
        self.assertEqual(updated_count_multi, 2)
        self.assertTrue(PurchaseOrder.objects.get(id=self.po2.id).checked)
        self.assertTrue(PurchaseOrder.objects.get(id=po3.id).checked)

    def test_get_po_list_for_display(self):
        # No search query (should only return unchecked POs)
        po_list = PurchaseOrder.get_po_list_for_display(MOCK_FIN_YEAR_ID, MOCK_COMP_ID)
        self.assertEqual(po_list.count(), 2) # po1, po2 (po_checked is filtered out)
        self.assertIn(self.po1, po_list)
        self.assertIn(self.po2, po_list)
        self.assertNotIn(self.po_checked, po_list)

        # Search by PO No
        po_list_po_no = PurchaseOrder.get_po_list_for_display(MOCK_FIN_YEAR_ID, MOCK_COMP_ID, search_type='1', search_query='PO-001')
        self.assertEqual(po_list_po_no.count(), 1)
        self.assertEqual(po_list_po_no.first().po_no, 'PO-001')

        # Search by Supplier Name
        po_list_supplier_name = PurchaseOrder.get_po_list_for_display(MOCK_FIN_YEAR_ID, MOCK_COMP_ID, search_type='0', search_query='ABC Corp [SUP001]')
        self.assertEqual(po_list_supplier_name.count(), 1)
        self.assertEqual(po_list_supplier_name.first().supplier_id.supplier_name, 'ABC Corp')

        # Search by Supplier Name (case-insensitive without code)
        po_list_supplier_name_no_code = PurchaseOrder.get_po_list_for_display(MOCK_FIN_YEAR_ID, MOCK_COMP_ID, search_type='0', search_query='xyz ltd')
        self.assertEqual(po_list_supplier_name_no_code.count(), 1)
        self.assertEqual(po_list_supplier_name_no_code.first().supplier_id.supplier_name, 'XYZ Ltd')

        # No results
        po_list_no_results = PurchaseOrder.get_po_list_for_display(MOCK_FIN_YEAR_ID, MOCK_COMP_ID, search_type='1', search_query='NONEXISTENT')
        self.assertEqual(po_list_no_results.count(), 0)

class PurchaseOrderViewsTest(ModelTestSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('po_check_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_check/list.html')
        self.assertIsInstance(response.context['search_form'], self.client.get(reverse('po_check_list')).context['search_form'].__class__)
        # Initial list view should not contain purchase_orders directly (loaded by HTMX)
        self.assertNotIn('purchase_orders', response.context)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('po_check_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_check/_po_master_table.html')
        self.assertIn('purchase_orders', response.context)
        self.assertEqual(response.context['purchase_orders'].count(), 2) # po1, po2 should be displayed

    def test_table_partial_view_search_po_no(self):
        response = self.client.get(reverse('po_check_table'), {'search_type': '1', 'po_no_search_query': 'PO-001'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'PO-001', response.content)
        self.assertNotIn(b'PO-002', response.content)

    def test_table_partial_view_search_supplier(self):
        response = self.client.get(reverse('po_check_table'), {'search_type': '0', 'supplier_search_query': 'XYZ Ltd [SUP002]'})
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'PO-002', response.content)
        self.assertNotIn(b'PO-001', response.content)

    def test_check_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('po_check_check'), {'po_ids[]': [self.po1.id, self.po2.id]}, **headers)
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX swap="none" + trigger
        self.assertEqual(response.headers['HX-Trigger'], 'refreshPoList')
        self.assertTrue(PurchaseOrder.objects.get(id=self.po1.id).checked)
        self.assertTrue(PurchaseOrder.objects.get(id=self.po2.id).checked)

    def test_check_view_post_no_ids(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('po_check_check'), {}, **headers)
        self.assertEqual(response.status_code, 200) # Should be 200 with a message
        self.assertContains(response, "No records found to mark as checked.")

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'abc'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/json')
        self.assertIn('ABC Corp [SUP001]', response.json())
        self.assertNotIn('XYZ Ltd [SUP002]', response.json())

    def test_toggle_search_fields_view(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Toggle to PO No
        response_po = self.client.post(reverse('toggle_search_fields'), {'search_type': '1'}, **headers)
        self.assertEqual(response_po.status_code, 200)
        self.assertTemplateUsed(response_po, 'material_management/po_check/_po_no_search_field.html')
        self.assertContains(response_po, 'id="txtPONo"')

        # Toggle to Supplier
        response_supplier = self.client.post(reverse('toggle_search_fields'), {'search_type': '0'}, **headers)
        self.assertEqual(response_supplier.status_code, 200)
        self.assertTemplateUsed(response_supplier, 'material_management/po_check/_supplier_search_field.html')
        self.assertContains(response_supplier, 'id="txtSupplier"')


    def test_view_details_redirect_pr(self):
        # Test redirect for PR (PRSPRFlag == '0')
        po = PurchaseOrder.objects.get(id=self.po1.id)
        url = reverse('po_check_view_details', kwargs={
            'pk': po.id,
            'po_no': po.po_no,
            'supplier_code': po.supplier_id.supplier_id,
            'amendment_no': po.amendment_no
        })
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
        expected_url_part = f"/PO_PR_View_Print_Details.aspx?mid={po.id}&pono={po.po_no}&Code={po.supplier_id.supplier_id}&AmdNo={po.amendment_no}&Key=some_random_key&Trans=&ModId=6&SubModId=35&parentpage=PO_Check.aspx"
        self.assertTrue(response.url.startswith(expected_url_part.split('&Trans=')[0])) # Partial match due to Trans empty string
        self.assertTrue('Trans=' in response.url)


    def test_view_details_redirect_spr(self):
        # Test redirect for SPR (PRSPRFlag == '1')
        po = PurchaseOrder.objects.get(id=self.po2.id)
        url = reverse('po_check_view_details', kwargs={
            'pk': po.id,
            'po_no': po.po_no,
            'supplier_code': po.supplier_id.supplier_id,
            'amendment_no': po.amendment_no
        })
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
        expected_url_part = f"/PO_SPR_View_Print_Details.aspx?mid={po.id}&pono={po.po_no}&Code={po.supplier_id.supplier_id}&AmdNo={po.amendment_no}&Key=some_random_key&ModId=6&SubModId=35&parentpage=PO_Check.aspx"
        self.assertEqual(response.url, expected_url_part)

    def test_view_details_redirect_not_found(self):
        # Test redirect for non-existent PO
        url = reverse('po_check_view_details', kwargs={
            'pk': 999, 'po_no': 'NONEXISTENT', 'supplier_code': 'NONE', 'amendment_no': '0'
        })
        response = self.client.get(url)
        self.assertEqual(response.status_code, 302)
        self.assertEqual(response.url, reverse('po_check_list')) # Redirect back to list
        # Check for error message (if messages framework is configured to work in tests)
        messages = list(response.context.get('messages')) if 'messages' in response.context else []
        self.assertIn('Purchase Order not found for details view.', [str(m) for m in messages])

```

---

## Step 5: HTMX and Alpine.js Integration

*   **Search Form Interaction:**
    *   The `search_type` dropdown (`drpfield`) uses `hx-post` to `{% url 'toggle_search_fields' %}`. This partial endpoint returns either `_supplier_search_field.html` or `_po_no_search_field.html`, which are then swapped into `#search-input-container`. This replicates the `drpfield_SelectedIndexChanged` logic using HTMX.
    *   The "Search" button uses `hx-get` to `{% url 'po_check_table' %}` which renders `_po_master_table.html` containing the DataTables. The search parameters from the form are included in the GET request.
*   **Supplier Autocomplete:**
    *   `txtSupplier` has `hx-get` to `{% url 'supplier_autocomplete' %}` triggered on `keyup changed delay:300ms, search`. The response is swapped into `#supplier-autocomplete-results`. A small Alpine.js snippet in `_supplier_search_field.html` handles clicking on autocomplete suggestions to populate the input field.
*   **DataTables:**
    *   The main table container (`#po-table-container`) uses `hx-trigger="load, refreshPoList from:body"` and `hx-get="{% url 'po_check_table' %}"` to initially load the table and refresh it after certain actions (like marking as checked).
    *   The `_po_master_table.html` partial contains the `<table>` element and the DataTables initialization script `$(document).ready(function() { $('#poTable').DataTable(...) });`. This script is crucial as it re-initializes DataTables every time the partial is swapped in by HTMX.
*   **Mark Selected as Checked:**
    *   The "Mark Selected as Checked" button uses `hx-post` to `{% url 'po_check_check' %}`.
    *   `hx-include="#po-table-container input[type='checkbox']:checked"` ensures that only the `value` (PO ID) of the *checked* checkboxes within the table are sent in the POST request.
    *   `hx-confirm="Are you sure you want to mark selected POs as checked?"` provides the client-side confirmation.
    *   `hx-swap="none"` prevents UI changes from the POST request itself, as the `HX-Trigger: refreshPoList` header will cause the table to reload asynchronously.
*   **Alpine.js for UI State:**
    *   Used in `list.html` with `x-data="{ searchType: '{{ search_form.search_type.value }}' }"` to manage the current search type and potentially control visibility of search fields client-side. The `htmx:afterSwap` event listener also uses `Alpine.data` for re-initialization.

---

## Final Notes

*   **Placeholders:** Replace `MOCK_FIN_YEAR_ID`, `MOCK_COMP_ID`, `MOCK_USERNAME` in `views.py` with actual values from your Django authentication system (e.g., `request.session.get('finyear')`, `request.session.get('compid')`, `request.user.username`). These mock values are for demonstration and testing purposes.
*   **Error Handling:** The `makegrid` function in C# had a broad `try-catch (Exception ess) { }`. In Django, errors should be handled gracefully (e.g., proper form validation, specific exception handling, logging). Messages framework is used for user feedback.
*   **External Links:** The "View" link points to `PO_PR_View_Print_Details.aspx` or `PO_SPR_View_Print_Details.aspx`. Ensure these legacy ASP.NET pages are still accessible and can handle the passed query parameters. If these are also to be migrated, this plan would extend to them.
*   **CSS and JavaScript Assets:** Assume `core/base.html` includes necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and your Tailwind CSS setup. The provided `yui-datatable.css` and `StyleSheet.css` would be integrated into the Tailwind CSS configuration or included via `{% static %}` tags.
*   **Security:** In a real-world scenario, ensure proper authentication, authorization, and input sanitization are implemented. Django's built-in features (like `csrf_token`) are included.
*   **Migration Strategy:** This plan details the target state for a single module. The full migration strategy would involve identifying all modules, prioritizing them, and applying this automated conversion process iteratively. Tools like custom scripts or AI agents would parse the ASP.NET code, extract schema, infer logic, and generate these Django components.
```