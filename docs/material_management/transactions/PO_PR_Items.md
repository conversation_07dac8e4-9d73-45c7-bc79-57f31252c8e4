## ASP.NET to Django Conversion Script: Purchase Order from PR Items

This document outlines a strategic plan for modernizing your existing ASP.NET `PO_PR_Items.aspx` application to a robust Django-based solution. Our approach emphasizes AI-assisted automation, clean architecture, and a superior user experience with minimal manual coding.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include base.html code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the `PO_PR_Items.aspx` functionality.

**Instructions:**
The ASP.NET code interacts with several tables. The primary table for the "Selected Items" grid is `tblMM_PR_PO_Temp`. The "Generate PO" logic then interacts with `tblMM_PO_Master` and `tblMM_PO_Details`, and reads from numerous lookup tables.

**Identified Tables and Inferred Columns:**

*   **`tblMM_PR_PO_Temp` (Temporary PO Items):**
    *   `Id` (int, PK)
    *   `SessionId` (string, `sId` from `Session["username"]`)
    *   `CompId` (int, `CompId` from `Session["compid"]`)
    *   `PRNo` (string)
    *   `PRId` (int, Foreign Key to `tblMM_PR_Details.Id`)
    *   `Qty` (double)
    *   `Rate` (double)
    *   `Discount` (double)
    *   `AddDesc` (string)
    *   `PF` (int, Foreign Key to `tblPacking_Master.Id`)
    *   `ExST` (int, Foreign Key to `tblExciseser_Master.Id`)
    *   `VAT` (int, Foreign Key to `tblVAT_Master.Id`)
    *   `DelDate` (datetime)
    *   `BudgetCode` (int, Foreign Key to `tblMIS_BudgetCode.Id`)

*   **`tblMM_PO_Master` (Purchase Order Header):**
    *   `Id` (int, PK)
    *   `SysDate` (datetime)
    *   `SysTime` (string)
    *   `SessionId` (string)
    *   `CompId` (int)
    *   `FinYearId` (int)
    *   `PRSPRFlag` (string)
    *   `PONo` (string, auto-generated)
    *   `SupplierId` (string, Foreign Key to `tblMM_Supplier_master.SupplierId`)
    *   `Reference` (int, Foreign Key to `tblMM_PO_Reference.Id`)
    *   `ReferenceDate` (datetime)
    *   `ReferenceDesc` (string)
    *   `PaymentTerms` (int, Foreign Key to `tblPayment_Master.Id`)
    *   `Warrenty` (int, Foreign Key to `tblWarrenty_Master.Id`)
    *   `Freight` (int, Foreign Key to `tblFreight_Master.Id`)
    *   `Octroi` (int, Foreign Key to `tblOctroi_Master.Id`)
    *   `ModeOfDispatch` (string)
    *   `Inspection` (string)
    *   `ShipTo` (string)
    *   `Remarks` (string)
    *   `FileName` (string)
    *   `FileSize` (long)
    *   `ContentType` (string)
    *   `FileData` (byte array/blob)
    *   `Insurance` (string)
    *   `TC` (string, full terms and conditions text)

*   **`tblMM_PO_Details` (Purchase Order Items):**
    *   `Id` (int, PK)
    *   `MId` (int, Foreign Key to `tblMM_PO_Master.Id`)
    *   `PONo` (string, matches `tblMM_PO_Master.PONo`)
    *   `PRNo` (string, matches `tblMM_PR_PO_Temp.PRNo`)
    *   `PRId` (int, matches `tblMM_PR_PO_Temp.PRId`)
    *   `Qty` (double)
    *   `Rate` (double)
    *   `Discount` (double)
    *   `AddDesc` (string)
    *   `PF` (int)
    *   `ExST` (int)
    *   `VAT` (int)
    *   `DelDate` (datetime)
    *   `BudgetCode` (int)

*   **Lookup/Master Tables:**
    *   `tblMM_Supplier_master` (SupplierId, SupplierName, RegdAddress, RegdCountry, RegdState, RegdCity, RegdPinNo)
    *   `tblMM_PO_Reference` (Id, RefDesc)
    *   `tblPayment_Master` (Id, Terms)
    *   `tblFreight_Master` (Id, Terms)
    *   `tblOctroi_Master` (Id, Terms)
    *   `tblWarrenty_Master` (Id, Terms)
    *   `tblVAT_Master` (Id, Terms, Value)
    *   `tblExciseser_Master` (Id, Terms, Value)
    *   `tblPacking_Master` (Id, Terms, Value)
    *   `tblDG_Item_Master` (Id, ItemCode, ManfDesc, UOMBasic)
    *   `Unit_Master` (Id, Symbol)
    *   `AccHead` (Id, Symbol, Description)
    *   `tblMIS_BudgetCode` (Id, Symbol, Description)
    *   `tblMM_PR_Master` (Id, PRNo, WONo)
    *   `tblMM_PR_Details` (Id, PRNo, MId, ItemId, AHId)
    *   `tbl_PO_terms` (Terms)
    *   `tblMM_RateLockUnLock_Master` (ItemId, Type, LockUnlock, LockedbyTranaction, LockDate, LockTime, CompId)

#### Step 2: Identify Backend Functionality

**Task:** Determine the core business logic and data operations from the C# code-behind.

**Instructions:**
The ASP.NET code defines a multi-step process for generating a Purchase Order from selected Purchase Requisition items.

*   **Initialization (`Page_Load`):**
    *   Retrieves session data (`username`, `compid`, `finyear`).
    *   Sets `txtRefDate` to read-only.
    *   Initializes `TabContainer` state.
    *   Populates `txtShipTo` with company address.
    *   Fetches supplier details (Name, Address) based on `SupCode` from query string and displays them.
    *   Loads `PO_PR_ItemGrid.aspx` into an iframe (this is the source of `tblMM_PR_PO_Temp` data).
    *   Loads default terms and conditions into `TextBox1` from `tbl_PO_terms`.
    *   Calls `LoadData()` to populate `GridView3` from `tblMM_PR_PO_Temp`.

*   **Item Listing (`LoadData()`):**
    *   Creates a `DataTable` schema matching `GridView3` columns.
    *   Queries `tblMM_PR_PO_Temp` filtered by `CompId` and `SessionId`.
    *   For each temporary item, it fetches related details from `tblMM_PR_Details`, `tblDG_Item_Master`, `Unit_Master`, `AccHead`, `tblVAT_Master`, `tblExciseser_Master`, `tblPacking_Master`.
    *   Performs calculations: `BasicAmt`, `DiscAmt`, `TaxAmt`, `TotalAmt` using `fun.CalBasicAmt`, `fun.CalDiscAmt`, `fun.CalTaxAmt`, `fun.CalTotAmt`.
    *   Binds the resulting `DataTable` to `GridView3`.

*   **Item Deletion (`GridView3_RowCommand`):**
    *   Deletes a specific record from `tblMM_PR_PO_Temp` based on its `Id`, `CompId`, and `SessionId`.
    *   Reloads the `GridView3`.

*   **PO Generation (`btnProceed_Click`):** This is the core transaction.
    *   Generates a new `PONo` by incrementing the last one for the current `CompId` and `FinYearId`.
    *   Extracts `SupplierCode` from the supplier name textbox.
    *   Performs date validation on `txtRefDate`.
    *   **Crucial Business Logic: Budget Check:** It iterates through `tblMM_PR_PO_Temp` (joining with `tblMM_PR_Details`, `tblMM_PR_Master`, `tblVAT_Master`, `tblExciseser_Master`, `tblPacking_Master`) to calculate total amounts grouped by `WONo` and `BudgetCode`. It then calls `calbalbud.TotBalBudget_WONO` to check if the budget is sufficient. If not, it stores insufficient budget details for error reporting.
    *   If budget checks pass:
        *   Handles file upload (`FileUpload1`) to store annexure data (filename, size, type, binary data).
        *   Inserts a new record into `tblMM_PO_Master` with all header details.
        *   Retrieves the newly generated `MId` (PK of `tblMM_PO_Master`).
        *   Iterates through `tblMM_PR_PO_Temp` again. For each item:
            *   Inserts a record into `tblMM_PO_Details`, linking it to the new PO Master.
            *   Updates `tblMM_RateLockUnLock_Master` to 'lock' the item by setting `LockUnlock='0'` and linking it to the new `PONo`.
        *   Deletes all records from `tblMM_PR_PO_Temp` for the current `CompId` and `SessionId`.
        *   Redirects to a success page (`PO_new.aspx`) or an error page (`PO_Error.aspx`).

*   **Supplier Autocomplete (`sql` WebMethod):**
    *   Provides suggestions for `SupplierName` based on `tblMM_Supplier_master`, filtering by `CompId` and prefix text.

#### Step 3: Infer UI Components

**Task:** Analyze the ASP.NET controls and their roles in the user interface.

**Instructions:**
The page is built using standard ASP.NET Web Forms controls, many with AjaxControlToolkit extensions for enhanced interactivity.

*   **Top Header:** `fontcsswhite` for "PO - For PR Items".
*   **Supplier & Reference Details (Form Area):**
    *   **Supplier Name:** `asp:TextBox` (`txtNewCustomerName`) with `cc1:AutoCompleteExtender`.
    *   **Reference:** `asp:DropDownList` (`DDLReference`) bound to `SqlDataSource4`.
    *   **Address:** `asp:Label` (`LblAddress`).
    *   **Ref. Date:** `asp:TextBox` (`txtRefDate`) with `cc1:CalendarExtender`, `RequiredFieldValidator`, `RegularExpressionValidator`.
    *   **Reference Desc:** `asp:TextBox` (`txtReferenceDesc`).
*   **Tabbed Interface (`cc1:TabContainer`):**
    *   **Tab 1: PR Items:**
        *   `iframe` (`Iframe1`) loading `PO_PR_ItemGrid.aspx`. This will be replaced by a modern HTMX-driven modal or sidebar component for adding items.
    *   **Tab 2: Selected Items:**
        *   `asp:Panel` (`Panel1`) for scrollability.
        *   `asp:GridView` (`GridView3`) displaying PR items. Supports paging, auto-generated columns, `OnPageIndexChanging`, `OnRowCommand` (for delete).
            *   Columns: LinkButton (Delete), Labels for PRNo, WONo, ItemCode, PurchDesc, UOMPurch, Qty, Rate, BasicAmt, Discount, DiscAmt, PF, Ex/Ser Tax, VAT, Taxble Amt, Total Amt, Add Desc., A/c Head, Deli Date, Id (hidden).
    *   **Tab 3: Terms & Conditions:**
        *   `asp:DropDownList` for `DDLPaymentTerms`, `DDLFreight`, `DDLOctroi`, `drpwarrenty` (each bound to its own `SqlDataSource`).
        *   `asp:TextBox` for `txtInsurance`, `txtRemarks`.
        *   `asp:TextBox` (`TextBox1`) for full T&C text (populated from DB).
        *   `asp:TextBox` for `txtShipTo`, `txtModeOfDispatch`, `txtInspection`.
        *   `asp:FileUpload` (`FileUpload1`) for annexure.
*   **Action Buttons:**
    *   `asp:Button` (`btnProceed`) for "Generate PO" with client-side confirmation and `ValidationGroup="B"`.
    *   `asp:Button` (`btnCancel`) for "Cancel".
*   **Progress Indicator:** `asp:UpdateProgress` and `cc1:ModalPopupExtender` (used for AJAX postbacks). This will be handled by HTMX global indicators.

#### Step 4: Generate Django Code

We will create a new Django app named `material_management`.

#### 4.1 Models (`material_management/models.py`)

This section defines the Django models, mapping them to your existing database tables. Business logic and calculation methods are added to the models adhering to the "Fat Model" principle.

```python
from django.db import models
from django.db.models import Sum, F
from decimal import Decimal
import datetime

# --- Lookup/Master Models (Assuming these exist or will be migrated first) ---

class Supplier(models.Model):
    id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    name = models.CharField(db_column='SupplierName', max_length=255)
    regd_address = models.TextField(db_column='RegdAddress', blank=True, null=True)
    regd_country_id = models.IntegerField(db_column='RegdCountry', blank=True, null=True)
    regd_state_id = models.IntegerField(db_column='RegdState', blank=True, null=True)
    regd_city_id = models.IntegerField(db_column='RegdCity', blank=True, null=True)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=10, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.name} [{self.id}]"

    @property
    def full_address(self):
        # In a real scenario, you'd fetch country, state, city names from their respective master tables
        # For this example, we'll use placeholder or assume direct text
        city_name = "City" # Placeholder
        state_name = "State" # Placeholder
        country_name = "Country" # Placeholder
        return f"{self.regd_address or ''},<br>{city_name},{state_name}, {country_name}. {self.regd_pin_no or ''}."

class PoReference(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    ref_desc = models.CharField(db_column='RefDesc', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Reference'
        verbose_name = 'PO Reference'
        verbose_name_plural = 'PO References'

    def __str__(self):
        return self.ref_desc

class PaymentTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblPayment_Master'
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'

    def __str__(self):
        return self.terms

class FreightTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblFreight_Master'
        verbose_name = 'Freight Term'
        verbose_name_plural = 'Freight Terms'

    def __str__(self):
        return self.terms

class OctroiTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblOctroi_Master'
        verbose_name = 'Octroi Term'
        verbose_name_plural = 'Octroi Terms'

    def __str__(self):
        return self.terms

class WarrantyTerm(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblWarrenty_Master'
        verbose_name = 'Warranty Term'
        verbose_name_plural = 'Warranty Terms'

    def __str__(self):
        return self.terms

class VATMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    value = models.DecimalField(db_column='Value', max_digits=10, decimal_places=2) # Assuming Value is percentage

    class Meta:
        managed = False
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT Master'
        verbose_name_plural = 'VAT Masters'

    def __str__(self):
        return self.terms

class ExciseServiceTaxMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    value = models.DecimalField(db_column='Value', max_digits=10, decimal_places=2) # Assuming Value is percentage

    class Meta:
        managed = False
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise/Service Tax Master'
        verbose_name_plural = 'Excise/Service Tax Masters'

    def __str__(self):
        return self.terms

class PackingMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    terms = models.CharField(db_column='Terms', max_length=255)
    value = models.DecimalField(db_column='Value', max_digits=10, decimal_places=2) # Assuming Value is percentage

    class Meta:
        managed = False
        db_table = 'tblPacking_Master'
        verbose_name = 'Packing Master'
        verbose_plural = 'Packing Masters'

    def __str__(self):
        return self.terms

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50)
    manf_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.IntegerField(db_column='UOMBasic', blank=True, null=True) # Foreign Key to Unit_Master

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return self.item_code

class UnitMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)

    class Meta:
        managed = False
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class AccountHead(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'AccHead'
        verbose_name = 'Account Head'
        verbose_name_plural = 'Account Heads'

    def __str__(self):
        return f"[{self.symbol}] {self.description}"

class BudgetCode(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50)
    description = models.CharField(db_column='Description', max_length=255)

    class Meta:
        managed = False
        db_table = 'tblMIS_BudgetCode'
        verbose_name = 'Budget Code'
        verbose_name_plural = 'Budget Codes'

    def __str__(self):
        return f"{self.symbol} - {self.description}"

class PrMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Master'
        verbose_name = 'PR Master'
        verbose_name_plural = 'PR Masters'

    def __str__(self):
        return self.pr_no

class PrDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.ForeignKey(PrMaster, on_delete=models.DO_NOTHING, db_column='MId', related_name='details')
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    item_id = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    ah_id = models.ForeignKey(AccountHead, on_delete=models.DO_NOTHING, db_column='AHId')

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

    def __str__(self):
        return f"PR: {self.pr_no} - Item: {self.item_id.item_code}"

class PoMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True) # PK is usually auto-increment, but specified here
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.CharField(db_column='SysTime', max_length=10)
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=10)
    po_no = models.CharField(db_column='PONo', max_length=50, unique=True)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId')
    reference = models.ForeignKey(PoReference, on_delete=models.DO_NOTHING, db_column='Reference')
    reference_date = models.DateField(db_column='ReferenceDate')
    reference_desc = models.CharField(db_column='ReferenceDesc', max_length=255, blank=True, null=True)
    payment_terms = models.ForeignKey(PaymentTerm, on_delete=models.DO_NOTHING, db_column='PaymentTerms', blank=True, null=True)
    warrenty = models.ForeignKey(WarrantyTerm, on_delete=models.DO_NOTHING, db_column='Warrenty', blank=True, null=True)
    freight = models.ForeignKey(FreightTerm, on_delete=models.DO_NOTHING, db_column='Freight', blank=True, null=True)
    octroi = models.ForeignKey(OctroiTerm, on_delete=models.DO_NOTHING, db_column='Octroi', blank=True, null=True)
    mode_of_dispatch = models.CharField(db_column='ModeOfDispatch', max_length=255, blank=True, null=True)
    inspection = models.CharField(db_column='Inspection', max_length=255, blank=True, null=True)
    ship_to = models.TextField(db_column='ShipTo', blank=True, null=True)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    file_size = models.BigIntegerField(db_column='FileSize', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    insurance = models.CharField(db_column='Insurance', max_length=255, blank=True, null=True)
    tc = models.TextField(db_column='TC', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'

    def __str__(self):
        return self.po_no

    @classmethod
    def generate_po_number(cls, comp_id, fin_year_id):
        last_po = cls.objects.filter(comp_id=comp_id, fin_year_id=fin_year_id).order_by('-po_no').first()
        if last_po and last_po.po_no.isdigit():
            next_po_int = int(last_po.po_no) + 1
        else:
            next_po_int = 1
        return f"{next_po_int:04d}" # Format as 0001, 0002 etc.

class PoDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(PoMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    po_no = models.CharField(db_column='PONo', max_length=50) # Redundant, but kept for db_column mapping
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    pr_id = models.ForeignKey(PrDetail, on_delete=models.DO_NOTHING, db_column='PRId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount = models.DecimalField(db_column='Discount', max_digits=10, decimal_places=2)
    add_desc = models.TextField(db_column='AddDesc', blank=True, null=True)
    pf = models.ForeignKey(PackingMaster, on_delete=models.DO_NOTHING, db_column='PF', blank=True, null=True)
    ex_st = models.ForeignKey(ExciseServiceTaxMaster, on_delete=models.DO_NOTHING, db_column='ExST', blank=True, null=True)
    vat = models.ForeignKey(VATMaster, on_delete=models.DO_NOTHING, db_column='VAT', blank=True, null=True)
    del_date = models.DateField(db_column='DelDate', blank=True, null=True)
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

    def __str__(self):
        return f"{self.po_no} - {self.pr_no}"


# --- Core Model for the current page's temporary items ---
class PoPrTempItemManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().select_related(
            'pr_id__item_id', 'pr_id__item_id__uom_basic', 'pr_id__ah_id',
            'vat', 'ex_st', 'pf', 'pr_id__m_id'
        )

    def calculate_item_amounts(self, item):
        """Calculates BasicAmt, DiscAmt, TaxAmt, TotalAmt for a single PoPrTempItem."""
        qty = Decimal(item.qty)
        rate = Decimal(item.rate)
        discount_percent = Decimal(item.discount)

        basic_amt = qty * rate
        disc_amt = basic_amt * (discount_percent / 100)
        
        amount_after_discount = basic_amt - disc_amt

        pf_value = Decimal(item.pf.value) if item.pf else Decimal('0.00')
        exst_value = Decimal(item.ex_st.value) if item.ex_st else Decimal('0.00')
        vat_value = Decimal(item.vat.value) if item.vat else Decimal('0.00')

        # Taxable amount calculations as per original C# logic:
        # Sum of basic - discount + (basic - discount) * PF% + (basic - discount) * ExST% + (basic - discount) * VAT%
        # This implies PF, ExST, VAT are applied on the discounted basic amount.
        # Original: (SUM(tblMM_PR_PO_Temp.Qty * (tblMM_PR_PO_Temp.Rate - tblMM_PR_PO_Temp.Rate * tblMM_PR_PO_Temp.Discount / 100)) ) +
        #            (SUM(...) )* tblExciseser_Master.Value/100 + (SUM(...) )* tblVAT_Master.Value/100 + (SUM(...) )* tblPacking_Master.Value/100
        
        # Simplified:
        pf_amt = amount_after_discount * (pf_value / 100)
        exst_amt = amount_after_discount * (exst_value / 100)
        vat_amt = amount_after_discount * (vat_value / 100)

        tax_amt = pf_amt + exst_amt + vat_amt
        total_amt = amount_after_discount + tax_amt

        return {
            'basic_amt': basic_amt.quantize(Decimal('0.00')),
            'disc_amt': disc_amt.quantize(Decimal('0.00')),
            'tax_amt': tax_amt.quantize(Decimal('0.00')),
            'total_amt': total_amt.quantize(Decimal('0.00')),
        }

    def validate_budget_for_po(self, temp_items_queryset, comp_id, fin_year_id):
        """
        Performs budget validation for all items in the temporary PO list.
        Returns a tuple: (is_budget_sufficient, list_of_insufficient_budgets)
        """
        # Group items by WONo and BudgetCode and calculate their total amounts
        # This mirrors the complex SQL query in btnProceed_Click
        # Ensure that pr_id__m_id__wo_no and budget_code are correct foreign key lookups
        budget_summary = temp_items_queryset.annotate(
            calculated_basic_amt=F('qty') * F('rate') * (Decimal('1') - F('discount') / Decimal('100'))
        ).annotate(
            calculated_total_amt=F('calculated_basic_amt') +
                                 F('calculated_basic_amt') * (F('pf__value') / Decimal('100')) +
                                 F('calculated_basic_amt') * (F('ex_st__value') / Decimal('100')) +
                                 F('calculated_basic_amt') * (F('vat__value') / Decimal('100'))
        ).values('pr_id__m_id__wo_no', 'budget_code').annotate(
            amount=Sum('calculated_total_amt')
        )

        insufficient_budgets = []
        is_sufficient = True

        for entry in budget_summary:
            wo_no = entry.get('pr_id__m_id__wo_no')
            budget_code_id = entry.get('budget_code')
            total_amount_needed = entry.get('amount', Decimal('0.00'))

            if budget_code_id is None: # Handle cases where budget code might be missing
                insufficient_budgets.append({
                    'WONO': wo_no or 'N/A',
                    'BudgetCode': 'Missing',
                    'Description': 'Budget Code Missing',
                    'BalAmt': Decimal('0.00')
                })
                is_sufficient = False
                continue

            # This part simulates `calbalbud.TotBalBudget_WONO`
            # In a real system, `TotBalBudget_WONO` would query a budget ledger
            # For this example, we mock a budget check or implement a simplified one
            try:
                # Placeholder for actual budget fetching logic
                # Example: Assume a BudgetAllocation model exists
                # budget_allocation = BudgetAllocation.objects.get(budget_code_id=budget_code_id, wo_no=wo_no, comp_id=comp_id, fin_year_id=fin_year_id)
                # total_budget = budget_allocation.allocated_amount
                # For demonstration, let's assume a static budget or look up in BudgetCode
                budget_obj = BudgetCode.objects.get(id=budget_code_id)
                total_budget = Decimal('100000.00') # Placeholder: Replace with actual budget check
                
                if wo_no: # Assuming WO specific budget logic
                    # Check if WO is valid (fun.CheckValidWONo) - often means it exists and is open
                    is_wo_valid = True # Placeholder: Replace with actual WO validation
                    if not is_wo_valid:
                        insufficient_budgets.append({
                            'WONO': wo_no,
                            'BudgetCode': f"{budget_obj.symbol}",
                            'Description': f"{budget_obj.description} (Invalid WO)",
                            'BalAmt': total_budget.quantize(Decimal('0.001'))
                        })
                        is_sufficient = False
                        continue
                        
                remaining_budget = total_budget - total_amount_needed

                if remaining_budget < Decimal('0.00'):
                    is_sufficient = False
                    insufficient_budgets.append({
                        'WONO': wo_no or 'N/A',
                        'BudgetCode': f"{budget_obj.symbol}",
                        'Description': f"{budget_obj.description}",
                        'BalAmt': total_budget.quantize(Decimal('0.001'))
                    })

            except BudgetCode.DoesNotExist:
                is_sufficient = False
                insufficient_budgets.append({
                    'WONO': wo_no or 'N/A',
                    'BudgetCode': 'Invalid',
                    'Description': 'Budget Code Not Found',
                    'BalAmt': Decimal('0.00')
                })
            except Exception as e:
                # Log error and consider as insufficient for safety
                is_sufficient = False
                insufficient_budgets.append({
                    'WONO': wo_no or 'N/A',
                    'BudgetCode': 'Error',
                    'Description': f"Error during budget check: {e}",
                    'BalAmt': Decimal('0.00')
                })

        return is_sufficient, insufficient_budgets


class PoPrTempItem(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    session_id = models.CharField(db_column='SessionId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    pr_id = models.ForeignKey(PrDetail, on_delete=models.DO_NOTHING, db_column='PRId')
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=2)
    discount = models.DecimalField(db_column='Discount', max_digits=10, decimal_places=2)
    add_desc = models.TextField(db_column='AddDesc', blank=True, null=True)
    pf = models.ForeignKey(PackingMaster, on_delete=models.DO_NOTHING, db_column='PF', blank=True, null=True)
    ex_st = models.ForeignKey(ExciseServiceTaxMaster, on_delete=models.DO_NOTHING, db_column='ExST', blank=True, null=True)
    vat = models.ForeignKey(VATMaster, on_delete=models.DO_NOTHING, db_column='VAT', blank=True, null=True)
    del_date = models.DateField(db_column='DelDate', blank=True, null=True)
    budget_code = models.ForeignKey(BudgetCode, on_delete=models.DO_NOTHING, db_column='BudgetCode', blank=True, null=True)

    objects = PoPrTempItemManager()

    class Meta:
        managed = False
        db_table = 'tblMM_PR_PO_Temp'
        verbose_name = 'Temporary PO Item'
        verbose_name_plural = 'Temporary PO Items'

    def __str__(self):
        return f"{self.pr_no} - {self.pr_id.item_id.item_code}"

    # Properties for calculated values (mimicking GridView columns)
    @property
    def item_code_display(self):
        return self.pr_id.item_id.item_code if self.pr_id and self.pr_id.item_id else ''

    @property
    def purch_desc_display(self):
        return self.pr_id.item_id.manf_desc if self.pr_id and self.pr_id.item_id else ''

    @property
    def uom_purch_display(self):
        return self.pr_id.item_id.uom_basic.symbol if self.pr_id and self.pr_id.item_id and self.pr_id.item_id.uom_basic else ''

    @property
    def ac_head_display(self):
        return self.pr_id.ah_id.__str__() if self.pr_id and self.pr_id.ah_id else ''

    @property
    def wo_no_display(self):
        return self.pr_id.m_id.wo_no if self.pr_id and self.pr_id.m_id else ''
    
    @property
    def pf_display(self):
        return self.pf.terms if self.pf else ''
    
    @property
    def ex_st_display(self):
        return self.ex_st.terms if self.ex_st else ''
    
    @property
    def vat_display(self):
        return self.vat.terms if self.vat else ''

    @property
    def basic_amt(self):
        return self.objects.calculate_item_amounts(self)['basic_amt']

    @property
    def disc_amt(self):
        return self.objects.calculate_item_amounts(self)['disc_amt']

    @property
    def tax_amt(self):
        return self.objects.calculate_item_amounts(self)['tax_amt']

    @property
    def total_amt(self):
        return self.objects.calculate_item_amounts(self)['total_amt']
    
    @property
    def schedule_date_display(self):
        return self.del_date.strftime('%d-%m-%Y') if self.del_date else ''


class PoService:
    """
    Service class to encapsulate complex PO generation business logic,
    keeping views thin and models focused on data.
    """

    @staticmethod
    def generate_po(form_data, files, temp_items_queryset, user, comp_id, fin_year_id):
        """
        Handles the entire PO generation process.
        """
        # 1. Budget Validation
        is_sufficient_budget, insufficient_budgets = PoPrTempItem.objects.validate_budget_for_po(
            temp_items_queryset, comp_id, fin_year_id
        )

        if not is_sufficient_budget:
            return False, "Insufficient budget detected.", insufficient_budgets

        if not temp_items_queryset.exists():
            return False, "No PR items selected for PO.", []

        # 2. Generate PO Number
        po_no = PoMaster.generate_po_number(comp_id, fin_year_id)

        # 3. File Handling
        annexure_file = files.get('annexure_file')
        file_data = None
        file_name = None
        file_size = None
        content_type = None

        if annexure_file:
            file_data = annexure_file.read()
            file_name = annexure_file.name
            file_size = annexure_file.size
            content_type = annexure_file.content_type

        # 4. Create PO Master record
        po_master = PoMaster.objects.create(
            sys_date=datetime.date.today(),
            sys_time=datetime.datetime.now().strftime('%H:%M:%S'),
            session_id=user.username, # Assuming Django user model maps to username
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            pr_spr_flag='0', # '0' for PR-based PO
            po_no=po_no,
            supplier=form_data['supplier_id'],
            reference=form_data['reference_id'],
            reference_date=form_data['ref_date'],
            reference_desc=form_data['reference_desc'],
            payment_terms=form_data['payment_terms_id'],
            warrenty=form_data['warranty_id'],
            freight=form_data['freight_id'],
            octroi=form_data['octroi_id'],
            mode_of_dispatch=form_data['mode_of_dispatch'],
            inspection=form_data['inspection'],
            ship_to=form_data['ship_to'],
            remarks=form_data['remarks'],
            file_name=file_name,
            file_size=file_size,
            content_type=content_type,
            file_data=file_data,
            insurance=form_data['insurance'],
            tc=form_data['tc'],
        )

        # 5. Create PO Details records and update RateLockUnLock_Master
        for temp_item in temp_items_queryset:
            PoDetail.objects.create(
                master=po_master,
                po_no=po_no, # Redundant, but matching original structure
                pr_no=temp_item.pr_no,
                pr_id=temp_item.pr_id,
                qty=temp_item.qty,
                rate=temp_item.rate,
                discount=temp_item.discount,
                add_desc=temp_item.add_desc,
                pf=temp_item.pf,
                ex_st=temp_item.ex_st,
                vat=temp_item.vat,
                del_date=temp_item.del_date,
                budget_code=temp_item.budget_code,
            )
            # Update RateLockUnLock_Master (assuming ItemId in tblMM_PR_Details.ItemId)
            # This is a critical side effect, ensure proper locking logic based on actual business needs
            # For simplicity, I'm just showing the update call
            RateLockUnlockMaster.objects.filter(
                item_id=temp_item.pr_id.item_id.id, type='2', comp_id=comp_id
            ).update(
                lock_unlock='0', locked_by_transaction=po_no,
                lock_date=datetime.date.today(), lock_time=datetime.datetime.now().strftime('%H:%M:%S')
            )

        # 6. Clear temporary items
        temp_items_queryset.delete()

        return True, "Purchase Order generated successfully.", po_no

class TermsAndConditions(models.Model):
    # This model represents tbl_PO_terms which seems to store pre-defined terms.
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an Id column
    terms_text = models.TextField(db_column='Terms')

    class Meta:
        managed = False
        db_table = 'tbl_PO_terms'
        verbose_name = 'PO Term'
        verbose_name_plural = 'PO Terms'

    def __str__(self):
        return self.terms_text[:50] + "..." if len(self.terms_text) > 50 else self.terms_text

class RateLockUnlockMaster(models.Model):
    # Model for tblMM_RateLockUnLock_Master
    id = models.IntegerField(db_column='Id', primary_key=True) # Assuming an Id column
    item_id = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    type = models.CharField(db_column='Type', max_length=10) # '2' for PO according to code
    lock_unlock = models.CharField(db_column='LockUnlock', max_length=10) # '0' for locked
    locked_by_transaction = models.CharField(db_column='LockedbyTranaction', max_length=50, blank=True, null=True)
    lock_date = models.DateField(db_column='LockDate', blank=True, null=True)
    lock_time = models.CharField(db_column='LockTime', max_length=10, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock/Unlock'
        verbose_name_plural = 'Rate Locks/Unlocks'

    def __str__(self):
        return f"{self.item_id.item_code} - Locked: {self.lock_unlock}"

```

#### 4.2 Forms (`material_management/forms.py`)

We'll define two forms: one for the main PO header details and one for the terms and conditions, and a custom form for the supplier autocomplete.

```python
from django import forms
from .models import (
    Supplier, PoReference, PaymentTerm, FreightTerm, OctroiTerm, WarrantyTerm,
    PoMaster, TermsAndConditions
)
import datetime

class SupplierAutoCompleteForm(forms.Form):
    supplier_name = forms.CharField(
        label="Supplier Name",
        max_length=255,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/material-management/po-pr-items/supplier-autocomplete/',
            'hx-trigger': 'input changed delay:500ms, blur', # Trigger search on input change after delay or on blur
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off',
            'x-on:focus': 'showSuggestions = true',
            'x-on:click.away': 'showSuggestions = false',
            'x-on:keydown.escape.window': 'showSuggestions = false',
        })
    )
    supplier_id_hidden = forms.CharField(
        widget=forms.HiddenInput(attrs={'id': 'supplier_id_hidden'}),
        required=False # This will store the actual supplier ID once selected
    )

    def clean_supplier_name(self):
        supplier_name_with_code = self.cleaned_data['supplier_name']
        if '[' in supplier_name_with_code and ']' in supplier_name_with_code:
            code_start = supplier_name_with_code.rfind('[')
            code_end = supplier_name_with_code.rfind(']')
            supplier_id_str = supplier_name_with_code[code_start+1:code_end]
            try:
                supplier = Supplier.objects.get(id=supplier_id_str)
                self.cleaned_data['supplier_id'] = supplier # Attach the actual supplier object
                return supplier_name_with_code # Return original string
            except Supplier.DoesNotExist:
                raise forms.ValidationError("Invalid supplier selected.")
        raise forms.ValidationError("Please select a supplier from the suggestions.")


class PoHeaderForm(forms.Form):
    # Supplier Name and ID will be handled by a separate autocomplete field in the template
    # and passed to the view separately, or via the SupplierAutoCompleteForm.
    # For now, let's include the supplier ID as a regular field, assuming it gets populated from JS.
    supplier_id = forms.ModelChoiceField(
        queryset=Supplier.objects.all(),
        to_field_name='id', # Map to SupplierId in DB
        widget=forms.HiddenInput(), # Hidden, populated by JS from autocomplete selection
        required=True,
        error_messages={'required': 'Please select a supplier.'}
    )
    supplier_name_display = forms.CharField( # For display purposes
        label="Supplier Name",
        max_length=255,
        required=False, # This field is only for display
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'readonly': True,
            'id': 'txtNewCustomerName_display' # Matches original ASPX ID for easier mapping
        })
    )
    supplier_address_display = forms.CharField( # For display purposes
        label="Address",
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'readonly': True,
            'rows': 3,
            'id': 'LblAddress' # Matches original ASPX ID
        })
    )

    reference_id = forms.ModelChoiceField(
        queryset=PoReference.objects.all(),
        label="Reference",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )
    ref_date = forms.DateField(
        label="Ref. Date",
        widget=forms.DateInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'type': 'date', # HTML5 date input
            'id': 'txtRefDate'
        }),
        input_formats=['%Y-%m-%d', '%d-%m-%Y'], # Allow multiple formats if needed
        required=True
    )
    reference_desc = forms.CharField(
        label="", # Label provided by HTML template
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'id': 'txtReferenceDesc'})
    )

    def clean_ref_date(self):
        ref_date = self.cleaned_data['ref_date']
        # Original C# used fun.DateValidation - Django's DateField does this automatically
        # Additional custom validation can be added here if needed (e.g., date in past/future constraints)
        if ref_date > datetime.date.today():
             # Example: Check if reference date is in the future
             # raise forms.ValidationError("Reference date cannot be in the future.")
             pass
        return ref_date

class PoTermsForm(forms.Form):
    payment_terms_id = forms.ModelChoiceField(
        queryset=PaymentTerm.objects.all(),
        label="Payment Terms",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False
    )
    freight_id = forms.ModelChoiceField(
        queryset=FreightTerm.objects.all(),
        label="Freight",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False
    )
    octroi_id = forms.ModelChoiceField(
        queryset=OctroiTerm.objects.all(),
        label="Octroi",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False
    )
    warranty_id = forms.ModelChoiceField(
        queryset=WarrantyTerm.objects.all(),
        label="Warranty",
        widget=forms.Select(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=False
    )
    insurance = forms.CharField(
        label="Insurance",
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'id': 'txtInsurance'})
    )
    remarks = forms.CharField(
        label="Remarks",
        required=False,
        widget=forms.Textarea(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3, 'id': 'txtRemarks'})
    )
    tc = forms.CharField(
        label="", # Label provided by HTML
        required=False,
        widget=forms.Textarea(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 5, 'id': 'TextBox1'})
    )
    ship_to = forms.CharField(
        label="Ship To",
        required=False,
        widget=forms.Textarea(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 4, 'id': 'txtShipTo'})
    )
    mode_of_dispatch = forms.CharField(
        label="Mode of Dispatch",
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'id': 'txtModeOfDispatch'})
    )
    inspection = forms.CharField(
        label="Inspection",
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'id': 'txtInspection'})
    )
    annexure_file = forms.FileField(
        label="Annexure",
        required=False,
        widget=forms.FileInput(attrs={'class': 'box3 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Pre-populate TC field from tbl_PO_terms similar to ASP.NET
        try:
            # Assuming there's a single default set of terms or a specific ID
            default_terms = TermsAndConditions.objects.first() # Or .get(id=some_id)
            if default_terms:
                self.fields['tc'].initial = default_terms.terms_text
        except TermsAndConditions.DoesNotExist:
            self.fields['tc'].initial = "Default Terms and Conditions not found."
        except Exception:
            self.fields['tc'].initial = "Error loading default Terms and Conditions."

```

#### 4.3 Views (`material_management/views.py`)

The main page functionality will be handled by a `TemplateView` or `FormView` that manages multiple forms and HTMX interactions. The "Selected Items" table and delete operations will be separate HTMX endpoints.

```python
from django.views.generic import TemplateView, ListView, View
from django.views.generic.edit import DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, Http404
from django.shortcuts import redirect, render
from django.db.models import Q
from django.core.exceptions import ValidationError
from django.contrib.auth.mixins import LoginRequiredMixin # For authentication

from .models import PoPrTempItem, Supplier, PoMaster, TermsAndConditions, PoService
from .forms import PoHeaderForm, PoTermsForm, SupplierAutoCompleteForm

# Assuming global context for session/comp/finyear data for simplicity in this example
# In a real app, use request.user.profile or custom middleware/context processors
def get_user_session_data(request):
    """Helper to get session data, mimicking ASP.NET Session."""
    # This is a placeholder. In a real Django app, user/company/finyear
    # would likely come from the authenticated user's profile or a selected context.
    return {
        'username': request.user.username if request.user.is_authenticated else 'anonymous',
        'comp_id': getattr(request.user, 'company_id', 1), # Default company ID
        'fin_year_id': getattr(request.user, 'finance_year_id', 1), # Default finance year ID
        'supplier_code_from_qs': request.GET.get('Code', ''), # From query string, e.g., ?Code=SUP001
    }

class PurchaseOrderCreateUpdateView(LoginRequiredMixin, TemplateView):
    """
    Main view for the PO generation page. Handles displaying forms,
    selected items, and processing the 'Generate PO' action.
    """
    template_name = 'material_management/po_pr_items/main_po_form.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        session_data = get_user_session_data(self.request)
        supplier_code = session_data['supplier_code_from_qs']

        # Initialize forms
        if 'po_header_form' not in context:
            context['po_header_form'] = PoHeaderForm(prefix='header')
        if 'po_terms_form' not in context:
            context['po_terms_form'] = PoTermsForm(prefix='terms')
        if 'supplier_autocomplete_form' not in context:
            context['supplier_autocomplete_form'] = SupplierAutoCompleteForm(prefix='supplier_ac')

        # Populate initial data if supplier code is present in query string
        if supplier_code:
            try:
                supplier = Supplier.objects.get(id=supplier_code)
                context['po_header_form'].fields['supplier_id'].initial = supplier.id
                context['po_header_form'].fields['supplier_name_display'].initial = f"{supplier.name} [{supplier.id}]"
                context['po_header_form'].fields['supplier_address_display'].initial = supplier.full_address
                context['supplier_autocomplete_form'].fields['supplier_name'].initial = f"{supplier.name} [{supplier.id}]"
                context['supplier_autocomplete_form'].fields['supplier_id_hidden'].initial = supplier.id

            except Supplier.DoesNotExist:
                messages.error(self.request, "Invalid supplier code provided in URL.")
        
        # Populate Ship To field (Company Address)
        # In a real system, `fun.CompAdd` would map to fetching from a `Company` model
        # For now, a placeholder or a default value
        company_address = "Your Company Address, City, State, Country. Pincode." # Placeholder for fun.CompAdd
        context['po_terms_form'].fields['ship_to'].initial = company_address

        return context

    def post(self, request, *args, **kwargs):
        session_data = get_user_session_data(request)
        comp_id = session_data['comp_id']
        fin_year_id = session_data['fin_year_id']
        current_username = session_data['username']

        po_header_form = PoHeaderForm(request.POST, prefix='header')
        po_terms_form = PoTermsForm(request.POST, request.FILES, prefix='terms')
        supplier_autocomplete_form = SupplierAutoCompleteForm(request.POST, prefix='supplier_ac')

        if po_header_form.is_valid() and po_terms_form.is_valid() and supplier_autocomplete_form.is_valid():
            # Get actual supplier object from the cleaned supplier_id
            supplier_id = po_header_form.cleaned_data['supplier_id'] # This is the actual Supplier object now
            
            # Combine cleaned data for PoService
            combined_form_data = po_header_form.cleaned_data
            combined_form_data.update(po_terms_form.cleaned_data)
            combined_form_data['supplier_id'] = supplier_id # Ensure this is the model instance

            # Retrieve temporary items for the current session
            temp_items = PoPrTempItem.objects.filter(comp_id=comp_id, session_id=current_username)
            
            # Call the PO generation service
            success, message, extra_data = PoService.generate_po(
                combined_form_data, request.FILES, temp_items, request.user, comp_id, fin_year_id
            )

            if success:
                messages.success(request, message)
                # Redirect to a success page or the new PO's detail page
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Redirect': reverse_lazy('po_list_page') # Assume a PO list page exists
                    }
                )
            else:
                messages.error(request, message)
                if extra_data: # If budget errors, store them in session to display on error page
                    request.session['budget_errors'] = extra_data
                # Redirect to an error page or re-render form with errors
                return HttpResponse(
                    status=204,
                    headers={
                        'HX-Redirect': reverse_lazy('po_error_page', kwargs={'supplier_code': supplier_code})
                    }
                )
        else:
            # Forms are not valid, re-render the page with errors
            context = self.get_context_data(
                po_header_form=po_header_form,
                po_terms_form=po_terms_form,
                supplier_autocomplete_form=supplier_autocomplete_form
            )
            # HTMX specific error handling: swap the entire form if not valid
            return render(request, self.template_name, context)


class PoPrTempItemListView(LoginRequiredMixin, ListView):
    """
    HTMX endpoint to render the list of selected PR items from the temporary table.
    """
    model = PoPrTempItem
    template_name = 'material_management/po_pr_items/_selected_items_table.html'
    context_object_name = 'temp_items'

    def get_queryset(self):
        session_data = get_user_session_data(self.request)
        comp_id = session_data['comp_id']
        current_username = session_data['username']
        # Use the custom manager for prefetching related data
        return PoPrTempItem.objects.filter(
            comp_id=comp_id, session_id=current_username
        ).order_by('id') # Ensure consistent order

class PoPrTempItemDeleteView(LoginRequiredMixin, DeleteView):
    """
    HTMX endpoint for confirming and deleting a temporary PR item.
    """
    model = PoPrTempItem
    template_name = 'material_management/po_pr_items/_confirm_delete_temp_item.html'
    # No success_url needed for HTMX, we return 204 or trigger a refresh

    def get_queryset(self):
        # Ensure only current user's and company's items can be deleted
        session_data = get_user_session_data(self.request)
        comp_id = session_data['comp_id']
        current_username = session_data['username']
        return PoPrTempItem.objects.filter(comp_id=comp_id, session_id=current_username)

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        self.object.delete()
        messages.success(request, "Item deleted successfully.")
        
        # HTMX response to close modal and refresh list
        return HttpResponse(
            status=204,
            headers={
                'HX-Trigger': 'refreshPoPrTempItemList, closeModals' # Custom event for Alpine.js to close modals
            }
        )

class SupplierAutoCompleteView(LoginRequiredMixin, View):
    """
    HTMX endpoint for supplier name autocomplete.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('supplier_ac-supplier_name', '') # Use form field name
        comp_id = get_user_session_data(request)['comp_id']
        
        if not prefix_text:
            return HttpResponse("") # No suggestions if no input

        # Filter suppliers starting with prefix_text (case-insensitive)
        suppliers = Supplier.objects.filter(
            Q(name__istartswith=prefix_text) | Q(id__istartswith=prefix_text),
            # Add company filter if needed, though original C# implies global suppliers
            # comp_id=comp_id
        ).values('id', 'name')[:10] # Limit to 10 suggestions

        suggestions = []
        for s in suppliers:
            suggestions.append({
                'id': s['id'],
                'name_display': f"{s['name']} [{s['id']}]",
                'full_address': Supplier.objects.get(id=s['id']).full_address # Fetch full address for display
            })
        
        # Render a partial HTML template for suggestions
        return render(request, 'material_management/po_pr_items/_supplier_suggestions.html', {'suggestions': suggestions})

# Placeholder views for redirection targets
class PoListPageView(LoginRequiredMixin, TemplateView):
    template_name = 'material_management/po_pr_items/po_list_page.html' # Actual PO list page

class PoErrorPageView(LoginRequiredMixin, TemplateView):
    template_name = 'material_management/po_pr_items/po_error_page.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['supplier_code'] = self.kwargs.get('supplier_code', 'N/A')
        context['budget_errors'] = self.request.session.pop('budget_errors', None)
        return context

```

#### 4.4 Templates (`material_management/templates/material_management/po_pr_items/`)

These templates demonstrate the structure, HTMX, Alpine.js, and DataTables integration.

**`main_po_form.html` (Main Page Template)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-blue-600 text-white p-3 rounded-t-lg">
        <h1 class="text-xl font-bold">PO - For PR Items</h1>
    </div>
    
    <!-- Messages (Django's messages framework) -->
    {% if messages %}
        <div class="mt-4">
            {% for message in messages %}
                <div class="p-3 mb-3 text-sm {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% else %}bg-blue-100 text-blue-800{% endif %} rounded-md" role="alert">
                    {{ message }}
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <div class="bg-white p-6 shadow-md rounded-b-lg">
        <form hx-post="." hx-trigger="submit" hx-swap="outerHTML" hx-target="#main-po-form-container"
              hx-on::after-request="if(event.detail.xhr.status === 204) { /* Handle redirect via HX-Redirect header */ } else { /* Re-render form with errors */ }"
              id="main-po-form-container"
              enctype="multipart/form-data"> {# Important for file uploads #}
            {% csrf_token %}

            <table class="w-full mb-6">
                <tr>
                    <td class="w-1/6 py-2">Supplier Name</td>
                    <td class="w-2/6 py-2 relative" x-data="{ showSuggestions: false }">
                        {{ supplier_autocomplete_form.supplier_name }}
                        {{ supplier_autocomplete_form.supplier_id_hidden }}
                        <div id="supplier-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full" x-show="showSuggestions && $el.innerHTML.trim() !== ''">
                            <!-- Suggestions loaded here via HTMX -->
                        </div>
                        {% if supplier_autocomplete_form.supplier_name.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ supplier_autocomplete_form.supplier_name.errors }}</p>
                        {% endif %}
                    </td>
                    <td class="w-1/6 py-2">Reference</td>
                    <td class="w-2/6 py-2">
                        {{ po_header_form.reference_id }}
                        {% if po_header_form.reference_id.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ po_header_form.reference_id.errors }}</p>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td class="w-1/6 py-2 align-top">Address</td>
                    <td class="w-2/6 py-2">{{ po_header_form.supplier_address_display }}</td>
                    <td class="w-1/6 py-2 align-top">Ref. Date</td>
                    <td class="w-2/6 py-2">
                        <div class="flex items-center space-x-2">
                            {{ po_header_form.ref_date }}
                            {{ po_header_form.reference_desc }}
                        </div>
                        {% if po_header_form.ref_date.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ po_header_form.ref_date.errors }}</p>
                        {% endif %}
                    </td>
                </tr>
            </table>

            {# Hidden supplier_id for PoHeaderForm. It will be updated by JS from autocomplete #}
            {{ po_header_form.supplier_id }}
            {{ po_header_form.supplier_name_display }} {# Display-only field for consistency #}

            <div x-data="{ activeTab: localStorage.getItem('activePoPrTab') || 'pr-items' }" class="mt-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                        <button type="button" 
                            class="{% raw %}{{{ activeTab === 'pr-items' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}}{% endraw %} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            x-on:click="activeTab = 'pr-items'; localStorage.setItem('activePoPrTab', 'pr-items')"
                            hx-get="{% url 'po_pr_items_pr_grid' %}" hx-target="#tab-content" hx-swap="innerHTML" hx-indicator="#tab-loader">
                            PR Items
                        </button>
                        <button type="button" 
                            class="{% raw %}{{{ activeTab === 'selected-items' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}}{% endraw %} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            x-on:click="activeTab = 'selected-items'; localStorage.setItem('activePoPrTab', 'selected-items')"
                            hx-get="{% url 'po_pr_items_selected_list' %}" hx-target="#tab-content" hx-swap="innerHTML" hx-indicator="#tab-loader">
                            Selected Items
                        </button>
                        <button type="button" 
                            class="{% raw %}{{{ activeTab === 'terms-conditions' ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300' }}}{% endraw %} whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                            x-on:click="activeTab = 'terms-conditions'; localStorage.setItem('activePoPrTab', 'terms-conditions')"
                            hx-get="{% url 'po_pr_items_terms_form' %}" hx-target="#tab-content" hx-swap="innerHTML" hx-indicator="#tab-loader">
                            Terms & Conditions
                        </button>
                    </nav>
                </div>
                
                <div id="tab-loader" class="htmx-indicator flex items-center justify-center h-24">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                </div>

                <div id="tab-content" class="mt-4 min-h-[340px]" 
                    hx-trigger="load" 
                    hx-get="{% raw %}{{{ activeTab === 'pr-items' ? '/material-management/po-pr-items/pr-grid/' : (activeTab === 'selected-items' ? '/material-management/po-pr-items/selected-list/' : '/material-management/po-pr-items/terms-conditions/') }}}{% endraw %}" 
                    hx-swap="innerHTML" 
                    hx-indicator="#tab-loader">
                    <!-- Initial content loaded here -->
                    <div class="text-center">Loading tab content...</div>
                </div>
            </div>

            <div class="mt-6 flex justify-end items-center space-x-4">
                <span class="text-red-500 text-sm">* for data entry error.</span>
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded redbox">
                    Generate PO
                </button>
                <a href="{% url 'po_list_page' %}" class="bg-gray-400 hover:bg-gray-500 text-gray-800 font-bold py-2 px-4 rounded redbox">
                    Cancel
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Modal for form (Add/Edit/Delete confirmation) -->
<div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
     x-data="{ open: false }" x-show="open"
     x-init="$watch('$store.modal.open', value => open = value); $el.addEventListener('click', (e) => { if(e.target.id === 'modal') $store.modal.close() })"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0 scale-90"
     x-transition:enter-end="opacity-100 scale-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100 scale-100"
     x-transition:leave-end="opacity-0 scale-90">
    <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4"
         x-on:close-modal.window="$store.modal.close()"
         x-on:htmx:after-request="if(event.detail.xhr.status === 204) $store.modal.close()">
        <!-- HTMX loaded content goes here -->
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js"></script>
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/2.0.7/css/dataTables.dataTables.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            open: false,
            show() { this.open = true; },
            close() { this.open = false; },
        });

        // Event listener for HTMX triggering modal show
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.target.id === 'modalContent' && event.detail.xhr.status !== 204) {
                Alpine.store('modal').show();
            }
        });
        document.body.addEventListener('closeModals', function(event) {
            Alpine.store('modal').close();
        });

        // Initialize DataTable when content is loaded by HTMX
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.target.id === 'selected-items-table-container') {
                $('#poPrTempItemsTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Allow re-initialization
                });
            }
        });

        // Handle supplier selection from autocomplete
        document.body.addEventListener('click', function(event) {
            if (event.target.closest('.supplier-suggestion-item')) {
                const suggestion = event.target.closest('.supplier-suggestion-item');
                const supplierName = suggestion.getAttribute('data-supplier-name');
                const supplierId = suggestion.getAttribute('data-supplier-id');
                const supplierAddress = suggestion.getAttribute('data-supplier-address');

                document.getElementById('supplier_ac-supplier_name').value = supplierName;
                document.getElementById('supplier_id_hidden').value = supplierId; // For hidden ID in autocomplete form
                
                // Update the PoHeaderForm fields for display and actual ID
                document.getElementById('id_header-supplier_id').value = supplierId; // Actual FK value
                document.getElementById('txtNewCustomerName_display').value = supplierName; // Display only
                document.getElementById('LblAddress').value = supplierAddress; // Display only

                Alpine.store('modal').close(); // Or close suggestions manually if using x-data for suggestions
                document.getElementById('supplier-suggestions').innerHTML = ''; // Clear suggestions
            }
        });
    });
</script>
{% endblock %}
```

**`_supplier_suggestions.html` (Partial for Supplier Autocomplete)**

```html
<ul x-show="$el.innerHTML.trim() !== ''">
    {% for suggestion in suggestions %}
    <li class="p-2 hover:bg-gray-100 cursor-pointer text-sm supplier-suggestion-item"
        data-supplier-id="{{ suggestion.id }}"
        data-supplier-name="{{ suggestion.name_display }}"
        data-supplier-address="{{ suggestion.full_address|safe }}">
        {{ suggestion.name_display }}
    </li>
    {% empty %}
    <li class="p-2 text-gray-500 text-sm">No suggestions</li>
    {% endfor %}
</ul>
```

**`_pr_items_grid.html` (Partial for PR Items Tab - Placeholder for Iframe content)**

```html
<div class="h-80 flex items-center justify-center border border-gray-200 bg-gray-50 text-gray-500">
    <p>This section historically contained an iframe loading another page (`PO_PR_ItemGrid.aspx`).</p>
    <p class="mt-2">For modernization, this would be replaced by a dedicated HTMX-driven component or modal for selecting and adding PR items to your temporary list ({{% url 'po_pr_items_selected_list' %}}).</p>
</div>
```

**`_selected_items_table.html` (Partial for Selected Items Tab - DataTables)**

```html
<div id="selected-items-table-container">
    {% if temp_items %}
    <table id="poPrTempItemsTable" class="min-w-full bg-white border border-gray-200">
        <thead>
            <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left">SN</th>
                <th class="py-3 px-6 text-left">PR No</th>
                <th class="py-3 px-6 text-left">WO No</th>
                <th class="py-3 px-6 text-left">Item Code</th>
                <th class="py-3 px-6 text-left">Description</th>
                <th class="py-3 px-6 text-center">UOM</th>
                <th class="py-3 px-6 text-right">Qty</th>
                <th class="py-3 px-6 text-right">Rate</th>
                <th class="py-3 px-6 text-right">Basic Amt</th>
                <th class="py-3 px-6 text-right">Dis %</th>
                <th class="py-3 px-6 text-right">Dis Amt</th>
                <th class="py-3 px-6 text-left">PF</th>
                <th class="py-3 px-6 text-left">Ex/Ser Tax</th>
                <th class="py-3 px-6 text-left">VAT</th>
                <th class="py-3 px-6 text-right">Taxable Amt</th>
                <th class="py-3 px-6 text-right">Total Amt</th>
                <th class="py-3 px-6 text-left">Add Desc.</th>
                <th class="py-3 px-6 text-left">A/c Head</th>
                <th class="py-3 px-6 text-center">Deli Date</th>
                <th class="py-3 px-6 text-center">Actions</th>
            </tr>
        </thead>
        <tbody class="text-gray-700 text-sm">
            {% for item in temp_items %}
            <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-6 text-left">{{ item.pr_no }}</td>
                <td class="py-3 px-6 text-left">{{ item.wo_no_display }}</td>
                <td class="py-3 px-6 text-left">{{ item.item_code_display }}</td>
                <td class="py-3 px-6 text-left">{{ item.purch_desc_display }}</td>
                <td class="py-3 px-6 text-center">{{ item.uom_purch_display }}</td>
                <td class="py-3 px-6 text-right">{{ item.qty|floatformat:3 }}</td>
                <td class="py-3 px-6 text-right">{{ item.rate|floatformat:2 }}</td>
                <td class="py-3 px-6 text-right">{{ item.basic_amt|floatformat:3 }}</td>
                <td class="py-3 px-6 text-right">{{ item.discount|floatformat:2 }}</td>
                <td class="py-3 px-6 text-right">{{ item.disc_amt|floatformat:3 }}</td>
                <td class="py-3 px-6 text-left">{{ item.pf_display }}</td>
                <td class="py-3 px-6 text-left">{{ item.ex_st_display }}</td>
                <td class="py-3 px-6 text-left">{{ item.vat_display }}</td>
                <td class="py-3 px-6 text-right">{{ item.tax_amt|floatformat:3 }}</td>
                <td class="py-3 px-6 text-right">{{ item.total_amt|floatformat:3 }}</td>
                <td class="py-3 px-6 text-left">{{ item.add_desc }}</td>
                <td class="py-3 px-6 text-left">{{ item.ac_head_display }}</td>
                <td class="py-3 px-6 text-center">{{ item.schedule_date_display }}</td>
                <td class="py-3 px-6 text-center">
                    <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded text-xs"
                            hx-get="{% url 'po_pr_items_delete' item.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            x-on:click="$store.modal.show()">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
        <div class="text-center py-10 font-bold text-gray-500">
            <p>No data to display !</p>
        </div>
    {% endif %}
</div>

<script>
    // This script runs when HTMX loads this partial.
    // Ensure DataTables is initialized only once or properly destroyed/reinitialized.
    // The main_po_form.html contains the afterSwap listener for DataTables init.
    // We can trigger it manually here if needed for initial load, but the event listener is better.
    // This section is empty as the main page handles DataTables initialization via HTMX event.
</script>
```

**`_terms_conditions_form.html` (Partial for Terms & Conditions Tab)**

```html
<div class="p-4 bg-white border border-gray-200 rounded-md">
    <table class="w-full">
        <tr>
            <td class="py-2 pr-4 w-1/4">Payment Terms</td>
            <td class="py-2 w-3/4">{{ po_terms_form.payment_terms_id }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4">Freight</td>
            <td class="py-2">{{ po_terms_form.freight_id }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4">Octroi</td>
            <td class="py-2">{{ po_terms_form.octroi_id }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4">Warranty</td>
            <td class="py-2">{{ po_terms_form.warranty_id }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4">Insurance</td>
            <td class="py-2">{{ po_terms_form.insurance }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4 align-top">Remarks</td>
            <td class="py-2">{{ po_terms_form.remarks }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4 colspan-2">
                <label for="{{ po_terms_form.tc.id_for_label }}" class="block text-sm font-medium text-gray-700">Terms & Conditions:</label>
                {{ po_terms_form.tc }}
            </td>
        </tr>
    </table>

    <table class="w-full mt-6">
        <tr>
            <td class="py-2 pr-4 w-1/4 align-top font-semibold">Ship To</td>
            <td class="py-2 w-3/4">{{ po_terms_form.ship_to }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4">Mode of Dispatch</td>
            <td class="py-2">{{ po_terms_form.mode_of_dispatch }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4">Inspection</td>
            <td class="py-2">{{ po_terms_form.inspection }}</td>
        </tr>
        <tr>
            <td class="py-2 pr-4">Annexure</td>
            <td class="py-2">{{ po_terms_form.annexure_file }}</td>
        </tr>
    </table>
</div>
```

**`_confirm_delete_temp_item.html` (Partial for Delete Confirmation Modal)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700">Are you sure you want to delete this temporary item?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            x-on:click="$store.modal.close()">
            Cancel
        </button>
        <button 
            type="button" 
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
            hx-delete="{% url 'po_pr_items_delete' object.pk %}"
            hx-confirm="This action cannot be undone."
            hx-target="body" {# Target body to receive HX-Trigger for refresh #}
            hx-swap="none"
            x-on:click="$store.modal.close()"> {# Close modal immediately on click #}
            Delete
        </button>
    </div>
</div>
```

**`po_list_page.html` (Placeholder for PO List Redirection)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Purchase Order List</h2>
    <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4" role="alert">
        <p class="font-bold">Success!</p>
        <p>Your Purchase Order was generated successfully. You can find it in the list below.</p>
    </div>
    <div class="mt-6">
        <p>This page would display a list of all your Purchase Orders.</p>
        <p>Future automation could involve automatically navigating to the newly created PO's detail page.</p>
    </div>
</div>
{% endblock %}
```

**`po_error_page.html` (Placeholder for PO Error Redirection)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Purchase Order Generation Error</h2>
    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4" role="alert">
        <p class="font-bold">Error!</p>
        <p>There was an issue generating your Purchase Order.</p>
        {% if messages %}
            <ul class="list-disc ml-5 mt-2">
            {% for message in messages %}
                <li>{{ message }}</li>
            {% endfor %}
            </ul>
        {% endif %}
        {% if budget_errors %}
            <p class="font-bold mt-4">Insufficient Budget Details:</p>
            <table class="min-w-full bg-white mt-2 text-sm border">
                <thead>
                    <tr class="bg-red-50 text-red-700">
                        <th class="py-2 px-4 text-left">WO No</th>
                        <th class="py-2 px-4 text-left">Budget Code</th>
                        <th class="py-2 px-4 text-left">Description</th>
                        <th class="py-2 px-4 text-right">Available Budget</th>
                    </tr>
                </thead>
                <tbody>
                    {% for error in budget_errors %}
                    <tr class="border-t border-red-200">
                        <td class="py-2 px-4">{{ error.WONO }}</td>
                        <td class="py-2 px-4">{{ error.BudgetCode }}</td>
                        <td class="py-2 px-4">{{ error.Description }}</td>
                        <td class="py-2 px-4 text-right">{{ error.BalAmt|floatformat:2 }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% endif %}
    </div>
    <div class="mt-6">
        <p>Please review the details and try again.</p>
        <a href="{% url 'po_pr_items_main' %}{% if supplier_code %}?Code={{ supplier_code }}{% endif %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mt-4 inline-block">
            Go Back to PO Creation
        </a>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs (`material_management/urls.py`)

This file defines the URL routing for your new Django application.

```python
from django.urls import path
from .views import (
    PurchaseOrderCreateUpdateView,
    PoPrTempItemListView,
    PoPrTempItemDeleteView,
    SupplierAutoCompleteView,
    PoListPageView,
    PoErrorPageView,
)

urlpatterns = [
    # Main PO form page
    path('po-pr-items/', PurchaseOrderCreateUpdateView.as_view(), name='po_pr_items_main'),

    # HTMX endpoints for tabs
    path('po-pr-items/pr-grid/', PoPrTempItemListView.as_view(template_name='material_management/po_pr_items/_pr_items_grid.html'), name='po_pr_items_pr_grid'), # Placeholder for iframe content
    path('po-pr-items/selected-list/', PoPrTempItemListView.as_view(), name='po_pr_items_selected_list'),
    path('po-pr-items/terms-conditions/', PurchaseOrderCreateUpdateView.as_view(template_name='material_management/po_pr_items/_terms_conditions_form.html'), name='po_pr_items_terms_form'),
    
    # HTMX endpoint for deleting temporary items
    path('po-pr-items/delete/<int:pk>/', PoPrTempItemDeleteView.as_view(), name='po_pr_items_delete'),

    # HTMX endpoint for supplier autocomplete
    path('po-pr-items/supplier-autocomplete/', SupplierAutoCompleteView.as_view(), name='supplier_autocomplete'),

    # Redirection target placeholders
    path('po-list/', PoListPageView.as_view(), name='po_list_page'),
    path('po-error/<str:supplier_code>/', PoErrorPageView.as_view(), name='po_error_page'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive unit tests for models and integration tests for views are crucial for verifying the correctness and robustness of the migrated application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from unittest.mock import patch, MagicMock
from decimal import Decimal
import datetime

from .models import (
    Supplier, PoReference, PaymentTerm, FreightTerm, OctroiTerm, WarrantyTerm,
    VATMaster, ExciseServiceTaxMaster, PackingMaster, ItemMaster, UnitMaster,
    AccountHead, BudgetCode, PrMaster, PrDetail, PoPrTempItem, PoMaster,
    PoService, RateLockUnlockMaster
)

class ModelSetupMixin:
    """Mixin to set up common test data for models and views."""
    @classmethod
    def setUpTestData(cls):
        # Create necessary lookup data
        cls.supplier = Supplier.objects.create(id='SUP001', name='Test Supplier', regd_address='123 Test St')
        cls.po_ref = PoReference.objects.create(id=1, ref_desc='Standard PO Ref')
        cls.payment_term = PaymentTerm.objects.create(id=1, terms='Net 30')
        cls.freight_term = FreightTerm.objects.create(id=1, terms='FOB Shipping Point')
        cls.octroi_term = OctroiTerm.objects.create(id=1, terms='Paid by Buyer')
        cls.warranty_term = WarrantyTerm.objects.create(id=1, terms='1 Year Standard')
        cls.vat_master = VATMaster.objects.create(id=1, terms='VAT 10%', value=Decimal('10.00'))
        cls.ex_st_master = ExciseServiceTaxMaster.objects.create(id=1, terms='Excise 5%', value=Decimal('5.00'))
        cls.packing_master = PackingMaster.objects.create(id=1, terms='Standard Packing 2%', value=Decimal('2.00'))
        cls.item_unit = UnitMaster.objects.create(id=1, symbol='EA')
        cls.item_master = ItemMaster.objects.create(id=1, item_code='ITEM001', manf_desc='Test Item Desc', uom_basic=cls.item_unit.id)
        cls.acc_head = AccountHead.objects.create(id=1, symbol='RAW', description='Raw Materials')
        cls.budget_code = BudgetCode.objects.create(id=1, symbol='BUD001', description='General Budget')
        cls.pr_master = PrMaster.objects.create(id=1, pr_no='PR001', wo_no='WO001')
        cls.pr_detail = PrDetail.objects.create(id=1, m_id=cls.pr_master, pr_no='PR001', item_id=cls.item_master, ah_id=cls.acc_head)
        cls.po_terms = TermsAndConditions.objects.create(id=1, terms_text='Default T&C text.')
        
        # User for testing authentication (assuming a custom user model or Django's default)
        from django.contrib.auth import get_user_model
        User = get_user_model()
        cls.user = User.objects.create_user(username='testuser', password='password123', company_id=1, finance_year_id=1)

class PoPrTempItemModelTest(ModelSetupMixin, TestCase):
    def setUp(self):
        super().setUp() # Call ModelSetupMixin's setUpTestData
        # Create a temporary item for testing calculations
        self.temp_item = PoPrTempItem.objects.create(
            id=1, session_id='testuser', comp_id=1, pr_no='PR001', pr_id=self.pr_detail,
            qty=Decimal('10.00'), rate=Decimal('100.00'), discount=Decimal('5.00'),
            add_desc='Test Add Desc', pf=self.packing_master, ex_st=self.ex_st_master,
            vat=self.vat_master, del_date=datetime.date.today(), budget_code=self.budget_code
        )

    def test_temp_item_creation(self):
        self.assertEqual(self.temp_item.pr_no, 'PR001')
        self.assertEqual(self.temp_item.qty, Decimal('10.00'))

    def test_calculated_properties(self):
        # Test basic_amt (Qty * Rate)
        self.assertEqual(self.temp_item.basic_amt, Decimal('1000.00'))
        
        # Test disc_amt (BasicAmt * Discount%)
        # 1000 * (5/100) = 50.00
        self.assertEqual(self.temp_item.disc_amt, Decimal('50.00'))

        # Test tax_amt (PF + ExST + VAT on discounted amount)
        # Discounted amount = 1000 - 50 = 950
        # PF = 950 * 2% = 19.00
        # ExST = 950 * 5% = 47.50
        # VAT = 950 * 10% = 95.00
        # Tax Amt = 19.00 + 47.50 + 95.00 = 161.50
        self.assertEqual(self.temp_item.tax_amt, Decimal('161.50'))

        # Test total_amt (Discounted amount + Tax Amt)
        # 950 + 161.50 = 1111.50
        self.assertEqual(self.temp_item.total_amt, Decimal('1111.50'))

    def test_display_properties(self):
        self.assertEqual(self.temp_item.item_code_display, 'ITEM001')
        self.assertEqual(self.temp_item.purch_desc_display, 'Test Item Desc')
        self.assertEqual(self.temp_item.uom_purch_display, 'EA')
        self.assertEqual(self.temp_item.ac_head_display, '[RAW] Raw Materials')
        self.assertEqual(self.temp_item.wo_no_display, 'WO001')
        self.assertEqual(self.temp_item.pf_display, 'Standard Packing 2%')
        self.assertEqual(self.temp_item.ex_st_display, 'Excise 5%')
        self.assertEqual(self.temp_item.vat_display, 'VAT 10%')
        self.assertEqual(self.temp_item.schedule_date_display, self.temp_item.del_date.strftime('%d-%m-%Y'))


class PoServiceTest(ModelSetupMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        
        self.temp_item1 = PoPrTempItem.objects.create(
            id=1, session_id='testuser', comp_id=1, pr_no='PR001', pr_id=self.pr_detail,
            qty=Decimal('10.00'), rate=Decimal('100.00'), discount=Decimal('5.00'),
            pf=self.packing_master, ex_st=self.ex_st_master, vat=self.vat_master,
            del_date=datetime.date.today(), budget_code=self.budget_code
        )
        # Create a second item with a different WO/Budget if needed for budget validation
        self.pr_master2 = PrMaster.objects.create(id=2, pr_no='PR002', wo_no='WO002')
        self.pr_detail2 = PrDetail.objects.create(id=2, m_id=self.pr_master2, pr_no='PR002', item_id=self.item_master, ah_id=self.acc_head)
        self.budget_code2 = BudgetCode.objects.create(id=2, symbol='BUD002', description='Project Budget')
        self.temp_item2 = PoPrTempItem.objects.create(
            id=2, session_id='testuser', comp_id=1, pr_no='PR002', pr_id=self.pr_detail2,
            qty=Decimal('5.00'), rate=Decimal('200.00'), discount=Decimal('10.00'),
            pf=self.packing_master, ex_st=self.ex_st_master, vat=self.vat_master,
            del_date=datetime.date.today(), budget_code=self.budget_code2
        )

    @patch('material_management.models.PoPrTempItemManager.validate_budget_for_po')
    def test_generate_po_success(self, mock_validate_budget):
        mock_validate_budget.return_value = (True, []) # Simulate successful budget validation

        form_data = {
            'supplier_id': self.supplier, # Pass actual model instance
            'reference_id': self.po_ref,
            'ref_date': datetime.date.today(),
            'reference_desc': 'Test PO Ref Desc',
            'payment_terms_id': self.payment_term,
            'warranty_id': self.warranty_term,
            'freight_id': self.freight_term,
            'octroi_id': self.octroi_term,
            'mode_of_dispatch': 'Courier',
            'inspection': 'Site Inspection',
            'ship_to': 'Test Ship To Address',
            'remarks': 'Test Remarks',
            'insurance': 'Full Cover',
            'tc': 'Custom T&C text.'
        }
        files = {'annexure_file': MagicMock(spec=bytes, name='test_file.pdf', size=1024, content_type='application/pdf', read=lambda: b'file_data')}

        temp_items_queryset = PoPrTempItem.objects.filter(session_id='testuser', comp_id=1)
        
        success, message, po_no = PoService.generate_po(
            form_data, files, temp_items_queryset, self.user, 1, 1
        )

        self.assertTrue(success)
        self.assertEqual(message, "Purchase Order generated successfully.")
        self.assertIsNotNone(po_no) # PO number should be generated

        self.assertTrue(PoMaster.objects.filter(po_no=po_no).exists())
        self.assertEqual(PoDetail.objects.filter(master__po_no=po_no).count(), 2) # Both temp items moved
        self.assertFalse(PoPrTempItem.objects.filter(session_id='testuser', comp_id=1).exists()) # Temp items cleared

        # Check RateLockUnlockMaster update (assuming default item_id from setup)
        # You'd need to assert specific item_ids and types here
        # For this test, we verify that at least one lock entry was updated
        self.assertTrue(RateLockUnlockMaster.objects.filter(locked_by_transaction=po_no).exists())


    @patch('material_management.models.PoPrTempItemManager.validate_budget_for_po')
    def test_generate_po_insufficient_budget(self, mock_validate_budget):
        mock_validate_budget.return_value = (False, [{'WONO': 'WO001', 'BudgetCode': 'BUD001', 'Description': 'Budget Error', 'BalAmt': Decimal('50.00')}])

        form_data = {} # Minimal form data, as validation happens before full processing
        files = {}
        temp_items_queryset = PoPrTempItem.objects.filter(session_id='testuser', comp_id=1)

        success, message, extra_data = PoService.generate_po(
            form_data, files, temp_items_queryset, self.user, 1, 1
        )

        self.assertFalse(success)
        self.assertEqual(message, "Insufficient budget detected.")
        self.assertIn('WONO', extra_data[0]) # Check for budget error details

        self.assertFalse(PoMaster.objects.exists()) # No PO should be created
        self.assertTrue(PoPrTempItem.objects.filter(session_id='testuser', comp_id=1).exists()) # Temp items should not be cleared


class PoPrTempItemViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.client.login(username='testuser', password='password123')
        self.temp_item = PoPrTempItem.objects.create(
            id=1, session_id='testuser', comp_id=1, pr_no='PR001', pr_id=self.pr_detail,
            qty=Decimal('10.00'), rate=Decimal('100.00'), discount=Decimal('5.00'),
            pf=self.packing_master, ex_st=self.ex_st_master, vat=self.vat_master,
            del_date=datetime.date.today(), budget_code=self.budget_code
        )

    def test_main_po_form_get(self):
        response = self.client.get(reverse('po_pr_items_main'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_pr_items/main_po_form.html')
        self.assertContains(response, 'PO - For PR Items')
        self.assertContains(response, 'name="header-supplier_id"') # Check for form fields

    def test_po_pr_items_selected_list_htmx_get(self):
        response = self.client.get(reverse('po_pr_items_selected_list'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_pr_items/_selected_items_table.html')
        self.assertContains(response, self.temp_item.pr_no) # Check if item is displayed

    def test_po_pr_items_delete_get_modal(self):
        response = self.client.get(reverse('po_pr_items_delete', args=[self.temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_pr_items/_confirm_delete_temp_item.html')
        self.assertContains(response, 'Confirm Deletion')

    def test_po_pr_items_delete_post(self):
        response = self.client.delete(reverse('po_pr_items_delete', args=[self.temp_item.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX expects 204 No Content
        self.assertFalse(PoPrTempItem.objects.filter(pk=self.temp_item.pk).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Item deleted successfully.")
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshPoPrTempItemList', response.headers['HX-Trigger'])

    @patch('material_management.models.PoService.generate_po')
    def test_main_po_form_post_success(self, mock_generate_po):
        mock_generate_po.return_value = (True, "PO generated successfully.", "PO0001")

        data = {
            'header-supplier_id': self.supplier.id,
            'header-supplier_name_display': f"{self.supplier.name} [{self.supplier.id}]",
            'header-supplier_address_display': self.supplier.full_address,
            'header-reference_id': self.po_ref.id,
            'header-ref_date': '2024-05-15',
            'header-reference_desc': 'Annual Purchase',
            'terms-payment_terms_id': self.payment_term.id,
            'terms-freight_id': self.freight_term.id,
            'terms-octroi_id': self.octroi_term.id,
            'terms-warranty_id': self.warranty_term.id,
            'terms-insurance': 'None',
            'terms-remarks': 'Urgent delivery',
            'terms-tc': 'Standard T&C 1',
            'terms-ship_to': 'Main Warehouse',
            'terms-mode_of_dispatch': 'Road',
            'terms-inspection': 'Pre-dispatch',
            'supplier_ac-supplier_name': f"{self.supplier.name} [{self.supplier.id}]", # Required by autocomplete form
            'supplier_ac-supplier_id_hidden': self.supplier.id, # Required by autocomplete form
        }
        response = self.client.post(reverse('po_pr_items_main'), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "PO generated successfully.")
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse_lazy('po_list_page'))

    @patch('material_management.models.PoService.generate_po')
    def test_main_po_form_post_fail_budget(self, mock_generate_po):
        mock_generate_po.return_value = (False, "Insufficient budget detected.", [{'WONO': 'WO001', 'BudgetCode': 'BUD001', 'Description': 'Budget Error', 'BalAmt': Decimal('50.00')}])

        data = {
            'header-supplier_id': self.supplier.id,
            'header-supplier_name_display': f"{self.supplier.name} [{self.supplier.id}]",
            'header-supplier_address_display': self.supplier.full_address,
            'header-reference_id': self.po_ref.id,
            'header-ref_date': '2024-05-15',
            'header-reference_desc': 'Annual Purchase',
            'terms-payment_terms_id': self.payment_term.id,
            'terms-freight_id': self.freight_term.id,
            'terms-octroi_id': self.octroi_term.id,
            'terms-warranty_id': self.warranty_term.id,
            'terms-insurance': 'None',
            'terms-remarks': 'Urgent delivery',
            'terms-tc': 'Standard T&C 1',
            'terms-ship_to': 'Main Warehouse',
            'terms-mode_of_dispatch': 'Road',
            'terms-inspection': 'Pre-dispatch',
            'supplier_ac-supplier_name': f"{self.supplier.name} [{self.supplier.id}]",
            'supplier_ac-supplier_id_hidden': self.supplier.id,
        }
        response = self.client.post(reverse('po_pr_items_main'), data, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 204) # Still 204 for HTMX redirect
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), "Insufficient budget detected.")
        self.assertIn('HX-Redirect', response.headers)
        self.assertIn('po_error_page', response.headers['HX-Redirect'])
        self.assertIn('budget_errors', response.wsgi_request.session)

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_ac-supplier_name': 'Test'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_pr_items/_supplier_suggestions.html')
        self.assertContains(response, 'Test Supplier [SUP001]')

    def test_supplier_autocomplete_view_no_match(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'supplier_ac-supplier_name': 'NonExistent'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No suggestions')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The Django templates provided extensively use HTMX and Alpine.js for dynamic interactions, eliminating the need for traditional full-page reloads and complex JavaScript.

*   **Supplier Autocomplete:**
    *   The `supplier_autocomplete_form.supplier_name` input has `hx-get` to `{% url 'supplier_autocomplete' %}`, `hx-trigger="input changed delay:500ms, blur"`, and `hx-target="#supplier-suggestions"`. This makes suggestions appear dynamically as the user types.
    *   Alpine.js (`x-data="{ showSuggestions: false }"`) controls the visibility of the suggestion list based on input focus and clicks.
    *   `x-on:click` on suggestion items fills the hidden `supplier_id_hidden` field and the display fields (`txtNewCustomerName_display`, `LblAddress`) for the `PoHeaderForm`.

*   **Tabbed Interface (`TabContainer` Replacement):**
    *   Each tab button uses `hx-get` to fetch its content from a dedicated Django URL (`po_pr_items_pr_grid`, `po_pr_items_selected_list`, `po_pr_items_terms_form`).
    *   `hx-target="#tab-content"` ensures the new content replaces the previous one in the `tab-content` div.
    *   `hx-trigger="load"` on `#tab-content` loads the default tab content on page load.
    *   `hx-indicator="#tab-loader"` shows a loading spinner during tab content fetches.
    *   Alpine.js (`x-data="{ activeTab: localStorage.getItem('activePoPrTab') || 'pr-items' }"`) manages the active tab state, making the tab persistent across page reloads (if `localStorage` is used).

*   **Selected Items Table (`GridView3` Replacement):**
    *   The `_selected_items_table.html` partial is loaded via HTMX into the "Selected Items" tab.
    *   It uses DataTables (`id="poPrTempItemsTable"`) for client-side searching, sorting, and pagination, mirroring the `GridView` functionality.
    *   The surrounding `div` (`id="selected-items-table-container"`) has `hx-trigger="load, refreshPoPrTempItemList from:body"` and `hx-get="{% url 'po_pr_items_selected_list' %}"`. This ensures the table is loaded on page load and refreshes automatically when a `refreshPoPrTempItemList` custom event is triggered (e.g., after a successful delete).

*   **Delete Item:**
    *   The "Delete" button in the `_selected_items_table.html` partial uses `hx-get="{% url 'po_pr_items_delete' item.pk %}"` to fetch a confirmation modal (`_confirm_delete_temp_item.html`).
    *   `hx-target="#modalContent"` loads the modal content.
    *   Alpine.js (`$store.modal.show()`) manages the modal's visibility.
    *   The confirmation modal's "Delete" button uses `hx-delete="{% url 'po_pr_items_delete' object.pk %}"` to send the actual DELETE request.
    *   `hx-swap="none"` and `hx-trigger="refreshPoPrTempItemList, closeModals"` ensure no content is swapped, but the list is refreshed and the modal is closed upon successful deletion.

*   **"Generate PO" Button (`btnProceed` Replacement):**
    *   The main form uses `hx-post="."` (posts to the current URL), `hx-trigger="submit"`, and `hx-target="#main-po-form-container"` (to re-swap the form if validation fails).
    *   `hx-on::after-request` JavaScript handles checking for a 204 status (success) and `HX-Redirect` header for a full page navigation, or re-rendering the form with errors.
    *   File uploads are handled by Django's form processing and the `enctype="multipart/form-data"` on the form.

*   **Global Progress Indicator:**
    *   HTMX provides built-in `htmx-indicator` classes. A global spinner can be configured in your `base.html` or `tailwind.config.js` to show on any active HTMX request, replacing the `UpdateProgress` extender.

*   **Validation Display:**
    *   Django forms automatically handle validation errors, which are displayed next to their respective fields in the HTML, replacing ASP.NET's `RequiredFieldValidator` and `RegularExpressionValidator`.

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating your ASP.NET `PO_PR_Items.aspx` application to a modern Django solution. By leveraging AI-assisted automation, adhering to best practices, and focusing on a modular, test-driven approach, you can significantly reduce manual effort, improve maintainability, and deliver a superior user experience. Remember to replace placeholder comments (e.g., `CompAdd` logic, `TotBalBudget_WONO` implementation) with your specific business logic and data access patterns during the actual migration. The emphasis remains on moving complex business logic into Python models or service classes, keeping your Django views concise and focused on request/response handling.