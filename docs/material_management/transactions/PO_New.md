This comprehensive Django modernization plan outlines the strategic approach to transitioning the provided ASP.NET application functionality to a modern Django-based solution. Our focus is on leveraging AI-assisted automation, adhering to a "fat model, thin view" architecture, and utilizing HTMX, Alpine.js, and DataTables for a highly interactive and efficient user experience.

The original ASP.NET page, `PO_New.aspx`, primarily serves as a search and selection interface for existing Purchase Requisitions (PR) and Supplier Purchase Requisitions (SPR) to initiate a new Purchase Order (PO). It features tabbed navigation, supplier search with autocomplete, and paginated data grids.

## ASP.NET to Django Conversion Script:

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns involved in the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with several tables implicitly through stored procedures and direct SQL queries. We infer the primary entities and their relevant columns for this page's functionality:

-   **`tblMM_Supplier_master`**: This table is used for supplier lookup and autocomplete.
    -   `SupplierId` (likely `CHAR`/`VARCHAR`, primary key)
    -   `SupplierName` (`NVARCHAR`/`VARCHAR`)
    -   `CompId` (`INT`, from session)

-   **Conceptual Summary Data (from Stored Procedures `GetSupplier_PO_PR`, `GetSupplier_PO_SPR`):**
    The `GridViews` display aggregated information that is likely sourced from database views or the output of stored procedures. We'll model these outputs for Django.
    -   `Supplier` (corresponds to `SupplierName`)
    -   `Code` (could be `SupplierId`, `PR_ID`, or `SPR_ID`)
    -   `Items` (calculated count of items)

**Assumptions for `managed=False` Models:**
For `PurchaseRequisitionSummary` and `SupplierPurchaseRequisitionSummary` models, we assume they map to existing database views (e.g., `View_GetSupplier_POPR` and `View_GetSupplier_POSPR`) that provide the summarized data directly, matching the columns displayed in the GridViews. This is a common pattern in legacy systems that use stored procedures for data aggregation.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data flow in the ASP.NET code.

**Key Functionalities Identified:**

-   **Data Listing (Read):**
    -   `LoadPR(string supcode)`: Populates the "PR" tab's grid (`GridView2`) with Purchase Requisition summaries, optionally filtered by supplier. This uses `GetSupplier_PO_PR` stored procedure.
    -   `LoadData(string supcode)`: Populates the "SPR" tab's grid (`GridView5`) with Supplier Purchase Requisition summaries, optionally filtered by supplier. This uses `GetSupplier_PO_SPR` stored procedure.

-   **Searching/Filtering:**
    -   Two textboxes (`txtSupplierPR`, `txtSearchSupplier`) are used for supplier name input, paired with "Search" buttons (`Button1`, `btnSearch`).
    -   An `AutoCompleteExtender` provides real-time suggestions for supplier names, sourcing data from a `sql` WebMethod.

-   **Navigation/Selection:**
    -   `GridView2_RowCommand` (`selme`): Clicking "Select" for a PR entry redirects to `PO_PR_Items.aspx`, passing a `Code` parameter (likely `PR_ID` or `SupplierId`).
    -   `GridView5_RowCommand` (`sel`): Clicking "Select" for an SPR entry redirects to `PO_SPR_Items.aspx`, passing a `Code` parameter (likely `SPR_ID` or `SupplierId`).

-   **Session Management & Cleanup:**
    -   The `Page_Load` event initializes `CompId` and `FinYearId` from session data and performs cleanup (`DELETE`) operations on temporary tables (`tblMM_PR_Po_Temp`, `tblMM_SPR_Po_Temp`). This temporary table usage implies a "shopping cart" or "staging" mechanism for PO creation. For this migration, we'll indicate this cleanup, but the focus remains on the listing and selection.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles in the user interface.

**UI Component Mapping:**

-   **`cc1:TabContainer` (ID `TabContainer1`)**: This will be implemented using HTMX for tab content loading and Alpine.js for managing the active tab state, providing a seamless single-page application feel.
-   **`asp:TextBox` (IDs `txtSupplierPR`, `txtSearchSupplier`)**: These input fields will be standard HTML `<input type="text">` elements enhanced with HTMX for triggering search requests and Alpine.js for binding and interaction.
-   **`cc1:AutoCompleteExtender`**: This rich ASP.NET control will be replaced by a combination of HTMX (`hx-get` on `keyup`) for fetching suggestions and Alpine.js for displaying and selecting from a dynamic list.
-   **`asp:Button` (IDs `Button1`, `btnSearch`)**: These search buttons will trigger HTMX requests (`hx-get`) to refresh the respective list views with filtered data.
-   **`asp:GridView` (IDs `GridView2`, `GridView5`)**: These data display grids will be replaced by standard HTML `<table>` elements initialized with DataTables for client-side features (pagination, sorting, search). HTMX will be used to load these tables as partials.
-   **`asp:LinkButton` (for "Select" action)**: These will be converted to standard HTML `<button>` elements with `hx-post` attributes to trigger the selection logic and redirect.

---

### Step 4: Generate Django Code

**Application Name:** We will create a Django application named `material_management`.

#### 4.1 Models

**Task:** Create Django models based on the identified database schema.

**Instructions:**
-   We'll create a `Supplier` model for the `tblMM_Supplier_master` table.
-   For the `GridView` data, we'll create `PurchaseRequisitionSummary` and `SupplierPurchaseRequisitionSummary` models. These are marked `managed = False` and map to hypothetical database views (`View_GetSupplier_POPR`, `View_GetSupplier_POSPR`) that would represent the aggregated data provided by the original stored procedures. This approach maintains the "fat model" principle by encapsulating data structure and potential business logic (like autocomplete search) within models, even when dealing with legacy database views.

```python
# material_management/models.py
from django.db import models
from django.db.models import Q # For complex queries in the future if needed

# Helper for session data, in a real app this would be integrated with user auth
def get_user_session_data(request=None):
    """
    Placeholder to mimic ASP.NET Session variables.
    In a real application, this would come from the authenticated user's profile
    or a dedicated settings model.
    For testing/development, default values are provided.
    """
    if request:
        comp_id = request.session.get('compid', 1)
        fin_year_id = request.session.get('finyear', '2023-2024')
        session_key = request.session.session_key # Using Django session key as SessionId
    else: # For model methods that don't have request context, or for testing setup
        comp_id = 1
        fin_year_id = '2023-2024'
        session_key = 'test_session_id' # Placeholder
    return comp_id, fin_year_id, session_key

class Supplier(models.Model):
    """
    Maps to tblMM_Supplier_master. Represents a supplier.
    """
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, primary_key=True)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    # Assuming CompId exists in the Supplier master table based on original code logic
    comp_id = models.IntegerField(db_column='CompId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"

    @classmethod
    def autocomplete_search(cls, prefix_text, comp_id):
        """
        Provides autocomplete suggestions for supplier names, mimicking the 'sql' WebMethod.
        Filters by company ID and orders alphabetically.
        """
        # Using __istartswith for case-insensitive starts-with search
        suppliers = cls.objects.filter(
            supplier_name__istartswith=prefix_text,
            comp_id=comp_id
        ).order_by('supplier_name')[:10] # Limit results for performance

        return [str(s) for s in suppliers]

class PurchaseRequisitionSummary(models.Model):
    """
    Represents a summarized Purchase Requisition record for listing.
    This model maps to a conceptual database view (e.g., View_GetSupplier_POPR)
    that would be populated by the logic within the GetSupplier_PO_PR stored procedure.
    """
    supplier_name = models.CharField(db_column='Supplier', max_length=255)
    code = models.CharField(db_column='Code', max_length=50, primary_key=True) # Unique code for the PR/Supplier
    num_items = models.IntegerField(db_column='Items')

    class Meta:
        managed = False
        # IMPORTANT: Replace 'View_GetSupplier_POPR' with the actual database view name if available.
        # If no view exists, this model primarily defines the *structure* of data returned by a query/SP.
        db_table = 'View_GetSupplier_POPR'
        verbose_name = 'Purchase Requisition Summary'
        verbose_name_plural = 'Purchase Requisition Summaries'

    def __str__(self):
        return f"PR Summary - {self.supplier_name} ({self.code})"
    
    # Business logic methods related to PR summary data can go here
    # E.g., calculate_remaining_qty() if 'Items' needs re-calculation

class SupplierPurchaseRequisitionSummary(models.Model):
    """
    Represents a summarized Supplier Purchase Requisition record for listing.
    This model maps to a conceptual database view (e.g., View_GetSupplier_POSPR)
    that would be populated by the logic within the GetSupplier_PO_SPR stored procedure.
    """
    supplier_name = models.CharField(db_column='Supplier', max_length=255)
    code = models.CharField(db_column='Code', max_length=50, primary_key=True) # Unique code for the SPR/Supplier
    num_items = models.IntegerField(db_column='Items')

    class Meta:
        managed = False
        # IMPORTANT: Replace 'View_GetSupplier_POSPR' with the actual database view name if available.
        db_table = 'View_GetSupplier_POSPR'
        verbose_name = 'Supplier Purchase Requisition Summary'
        verbose_name_plural = 'Supplier Purchase Requisition Summaries'

    def __str__(self):
        return f"SPR Summary - {self.supplier_name} ({self.code})"

    # Business logic methods related to SPR summary data can go here
```

#### 4.2 Forms

**Task:** Define Django forms for user input.

**Instructions:**
For this specific ASP.NET page (`PO_New.aspx`), there are no complex data entry forms. The textboxes are primarily for search/filter, and the "Select" action is a simple row command. Therefore, we don't need a separate `forms.py` file for this functionality. Search parameters will be handled directly in the views via `request.GET`.

#### 4.3 Views

**Task:** Implement the page's functionality using Django Class-Based Views (CBVs).

**Instructions:**
-   A `TemplateView` will serve as the main dashboard page, providing the structure for the tabs.
-   Separate `ListView`s will handle the data display for PR and SPR summaries. These views will be rendered as partials via HTMX.
-   A dedicated `View` will handle the supplier autocomplete requests, returning an HTML fragment.
-   Views will adhere to the "thin view" principle (5-15 lines per method), pushing complex data retrieval logic to models or assuming it's handled by database views.
-   Redirection logic for "Select" actions will use `redirect` and `reverse_lazy`.

```python
# material_management/views.py
from django.views.generic import TemplateView, ListView, View
from django.http import JsonResponse, HttpResponse
from django.shortcuts import redirect
from django.urls import reverse_lazy
from django.template.loader import render_to_string
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

from .models import Supplier, PurchaseRequisitionSummary, SupplierPurchaseRequisitionSummary, get_user_session_data

class PONewDashboardView(TemplateView):
    """
    Main dashboard view for PO New. Handles the overall page structure and tab navigation.
    This replaces the PO_New.aspx master layout and initial setup.
    """
    template_name = 'material_management/po_new/dashboard.html'

    def dispatch(self, request, *args, **kwargs):
        # Mimic ASP.NET Page_Load initial cleanup of temporary data.
        # In Django, this might involve session-based temporary data or a custom model.
        # For simplicity, we'll just acknowledge the cleanup concept here.
        comp_id, fin_year_id, session_key = get_user_session_data(request)

        # Example: If you had models for these temp tables:
        # from .models import PRPoTemp, SPRPoTemp
        # PRPoTemp.objects.filter(comp_id=comp_id, session_id=session_key).delete()
        # SPRPoTemp.objects.filter(comp_id=comp_id, session_id=session_key).delete()
        # Alternatively, if selections are stored in session:
        # request.session['current_pr_selections'] = []
        # request.session['current_spr_selections'] = []

        return super().dispatch(request, *args, **kwargs)

class PurchaseRequisitionListView(ListView):
    """
    View for listing Purchase Requisition summaries, rendered as an HTMX partial.
    Corresponds to GridView2 and LoadPR method.
    """
    model = PurchaseRequisitionSummary
    template_name = 'material_management/po_new/_pr_table.html'
    context_object_name = 'prs'
    paginate_by = 17 # Matches ASP.NET PageSize

    def get_queryset(self):
        comp_id, fin_year_id, _ = get_user_session_data(self.request)
        supplier_code = self.request.GET.get('supplier_code', '')

        # Query the PurchaseRequisitionSummary model.
        # Assumes `View_GetSupplier_POPR` (or equivalent) in the database handles
        # the complex aggregation logic (like item counts, remaining qty).
        # The original ASP.NET code filtered by `SupplierId`. We assume 'Code' in our summary
        # corresponds to that `SupplierId` or PR unique ID.
        queryset = super().get_queryset()
        if supplier_code:
            # IMPORTANT: This filtering assumes 'code' in the summary view is the SupplierId
            # which aligns with `fun.getCode` used in ASP.NET to filter by supplier.
            queryset = queryset.filter(code=supplier_code)

        # If the underlying DB view/SP also filters by CompId or FinYearId,
        # ensure those parameters are passed, potentially via raw SQL queries or
        # a custom manager that wraps stored procedure calls.
        # Example using raw SQL for complex SP integration (if needed):
        # raw_sql = f"EXEC GetSupplier_PO_PR @SupId='{supplier_code}', @CompId={comp_id}, @FinId='{fin_year_id}'"
        # return self.model.objects.raw(raw_sql)
        
        return queryset

    def post(self, request, *args, **kwargs):
        """
        Handles the "Select" action for a PR item, redirecting to the detail page.
        """
        code = request.POST.get('code') # Get the code from the selected row
        if code:
            # Redirect to the PO_PR_Items equivalent page
            return redirect(reverse_lazy('material_management:po_pr_items', kwargs={'code': code}))
        return HttpResponse(status=400) # Bad request if code is missing

class SupplierPurchaseRequisitionListView(ListView):
    """
    View for listing Supplier Purchase Requisition summaries, rendered as an HTMX partial.
    Corresponds to GridView5 and LoadData method.
    """
    model = SupplierPurchaseRequisitionSummary
    template_name = 'material_management/po_new/_spr_table.html'
    context_object_name = 'sprs'
    paginate_by = 17 # Matches ASP.NET PageSize

    def get_queryset(self):
        comp_id, fin_year_id, _ = get_user_session_data(self.request)
        supplier_code = self.request.GET.get('supplier_code', '')

        # Query the SupplierPurchaseRequisitionSummary model.
        queryset = super().get_queryset()
        if supplier_code:
            # IMPORTANT: This filtering assumes 'code' in the summary view is the SupplierId
            queryset = queryset.filter(code=supplier_code)
        
        return queryset

    def post(self, request, *args, **kwargs):
        """
        Handles the "Select" action for an SPR item, redirecting to the detail page.
        """
        code = request.POST.get('code')
        if code:
            # Redirect to the PO_SPR_Items equivalent page
            return redirect(reverse_lazy('material_management:po_spr_items', kwargs={'code': code}))
        return HttpResponse(status=400)

class SupplierAutocompleteView(View):
    """
    Provides autocomplete suggestions for suppliers, replacing the ASP.NET WebMethod 'sql'.
    Returns an HTML fragment via HTMX.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        comp_id, _, _ = get_user_session_data(request)

        # Get suggestions using the model method
        suggestions = Supplier.autocomplete_search(prefix_text, comp_id)

        # Render suggestions as an HTML fragment to be swapped by HTMX
        html = render_to_string(
            'material_management/po_new/_supplier_autocomplete_suggestions.html',
            {'suggestions': suggestions},
            request=request
        )
        return HttpResponse(html)

# Placeholder views for the redirected pages (PO_PR_Items, PO_SPR_Items)
# These would be full Django views handling the detailed item selection/PO creation logic.
class PO_PR_ItemsView(TemplateView):
    template_name = 'material_management/po_new/po_pr_items.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['selected_code'] = self.kwargs['code']
        return context

class PO_SPR_ItemsView(TemplateView):
    template_name = 'material_management/po_new/po_spr_items.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['selected_code'] = self.kwargs['code']
        return context
```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring HTMX, Alpine.js, and DataTables integration.

**Instructions:**
-   `dashboard.html`: The main page, containing the tab structure. It uses HTMX `hx-get` to load the content of the tabs (`_pr_table.html` or `_spr_table.html`) dynamically. Alpine.js manages the active tab state.
-   `_pr_table.html`, `_spr_table.html`: Partial templates containing the DataTables structure, search input, and "Select" buttons. These are loaded into the main `dashboard.html` via HTMX.
-   `_supplier_autocomplete_suggestions.html`: A small partial to render the autocomplete suggestions.
-   `po_pr_items.html`, `po_spr_items.html`: Placeholder templates for the navigation targets.

```html
{# material_management/po_new/dashboard.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6 text-gray-800">PO - New</h2>
    
    <div x-data="{ activeTab: 'pr' }" class="bg-white shadow-lg rounded-lg p-6">
        <!-- Tab Headers -->
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button 
                    @click="activeTab = 'pr'"
                    :class="activeTab === 'pr' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200"
                    hx-get="{% url 'material_management:pr_list_partial' %}"
                    hx-target="#tab-content"
                    hx-trigger="click, load once delay:100ms" {# Load PR tab content on initial page load #}
                    hx-indicator="#tab-content-loader"
                    hx-swap="innerHTML">
                    &nbsp;&nbsp;PR&nbsp;&nbsp;
                </button>
                <button 
                    @click="activeTab = 'spr'"
                    :class="activeTab === 'spr' ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
                    class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200"
                    hx-get="{% url 'material_management:spr_list_partial' %}"
                    hx-target="#tab-content"
                    hx-indicator="#tab-content-loader"
                    hx-swap="innerHTML">
                    &nbsp;&nbsp;SPR&nbsp;&nbsp;
                </button>
            </nav>
        </div>
        
        <!-- Tab Content -->
        <div id="tab-content" class="mt-6">
            <!-- Content will be loaded here via HTMX -->
            <div id="tab-content-loader" class="text-center p-8 htmx-indicator">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // DataTables initialization handled within partial templates
</script>
{% endblock %}
```

```html
{# material_management/po_new/_pr_table.html #}
<div class="p-4" id="pr_table_wrapper">
    <div class="mb-4 flex flex-wrap items-center space-x-4">
        <label for="txtSupplierPR" class="font-bold text-gray-700 whitespace-nowrap">Supplier</label>
        <div class="relative flex-grow">
            <input 
                type="text" 
                id="txtSupplierPR" 
                name="supplier_pr_search"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3"
                placeholder="Search Supplier"
                hx-get="{% url 'material_management:supplier_autocomplete' %}"
                hx-trigger="keyup changed delay:300ms, search" {# Triggers on keyup after 300ms, or on explicit search event #}
                hx-target="#pr-supplier-suggestions"
                hx-swap="innerHTML"
                autocomplete="off"
                x-data="{ value: '' }"
                x-model="value"
                @select-supplier.window="value = $event.detail.value; $el.value = value; $dispatch('trigger-pr-search');" {# Listen for custom event to update input and trigger search #}>
            <div id="pr-supplier-suggestions" class="absolute z-10 bg-white border border-gray-200 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto"></div>
        </div>
        <button 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded redbox"
            hx-get="{% url 'material_management:pr_list_partial' %}"
            hx-target="#pr_table_wrapper"
            hx-swap="outerHTML"
            hx-vals="js:{supplier_code: document.getElementById('txtSupplierPR').value.split('[').pop().slice(0, -1) || ''}" {# Extracts the code from 'Name [Code]' or empty string #}
            hx-trigger="click, trigger-pr-search from:body"> {# Trigger on click or the custom event from Alpine.js #}
            Search
        </button>
    </div>

    <div id="pr_table_container" class="overflow-x-auto">
        <table id="prTable" class="min-w-full bg-white yui-datatable-theme border border-gray-200">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">No. of Items</th>
                </tr>
            </thead>
            <tbody>
                {% if prs %}
                    {% for pr_summary in prs %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">
                            <button 
                                class="text-blue-600 hover:text-blue-800 font-medium whitespace-nowrap"
                                hx-post="{% url 'material_management:pr_list_partial' %}" {# POST to the current partial view #}
                                hx-vals='{"code": "{{ pr_summary.code }}"}'
                                hx-target="body" hx-swap="none" {# Swap none, as this triggers a redirect #}
                                hx-push-url="true"> {# Updates browser history on redirect #}
                                Select
                            </button>
                        </td>
                        <td class="py-2 px-4 border-b border-gray-200 text-left">{{ pr_summary.supplier_name }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">{{ pr_summary.code }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ pr_summary.num_items }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" class="py-4 px-4 text-center text-lg font-medium text-red-700 fontcss">No data to display !</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<script>
    // Initialize DataTables after content is loaded via HTMX
    $(document).ready(function() {
        $('#prTable').DataTable({
            "pageLength": 17,
            "lengthMenu": [[17, 25, 50, -1], [17, 25, 50, "All"]],
            "responsive": true // Make table responsive
        });
    });
</script>
```

```html
{# material_management/po_new/_spr_table.html #}
<div class="p-4" id="spr_table_wrapper">
    <div class="mb-4 flex flex-wrap items-center space-x-4">
        <label for="txtSearchSupplier" class="font-bold text-gray-700 whitespace-nowrap">Supplier</label>
        <div class="relative flex-grow">
            <input 
                type="text" 
                id="txtSearchSupplier" 
                name="supplier_spr_search"
                class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm box3"
                placeholder="Search Supplier"
                hx-get="{% url 'material_management:supplier_autocomplete' %}"
                hx-trigger="keyup changed delay:300ms, search"
                hx-target="#spr-supplier-suggestions"
                hx-swap="innerHTML"
                autocomplete="off"
                x-data="{ value: '' }"
                x-model="value"
                @select-supplier.window="value = $event.detail.value; $el.value = value; $dispatch('trigger-spr-search');">
            <div id="spr-supplier-suggestions" class="absolute z-10 bg-white border border-gray-200 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto"></div>
        </div>
        <button 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded redbox"
            hx-get="{% url 'material_management:spr_list_partial' %}"
            hx-target="#spr_table_wrapper"
            hx-swap="outerHTML"
            hx-vals="js:{supplier_code: document.getElementById('txtSearchSupplier').value.split('[').pop().slice(0, -1) || ''}"
            hx-trigger="click, trigger-spr-search from:body">
            Search
        </button>
    </div>

    <div id="spr_table_container" class="overflow-x-auto">
        <table id="sprTable" class="min-w-full bg-white yui-datatable-theme border border-gray-200">
            <thead>
                <tr>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                    <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">No. Of Items</th>
                </tr>
            </thead>
            <tbody>
                {% if sprs %}
                    {% for spr_summary in sprs %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">
                            <button 
                                class="text-blue-600 hover:text-blue-800 font-medium whitespace-nowrap"
                                hx-post="{% url 'material_management:spr_list_partial' %}"
                                hx-vals='{"code": "{{ spr_summary.code }}"}'
                                hx-target="body" hx-swap="none"
                                hx-push-url="true">
                                Select
                            </button>
                        </td>
                        <td class="py-2 px-4 border-b border-gray-200 text-left">{{ spr_summary.supplier_name }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-center">{{ spr_summary.code }}</td>
                        <td class="py-2 px-4 border-b border-gray-200 text-right">{{ spr_summary.num_items }}</td>
                    </tr>
                    {% endfor %}
                {% else %}
                    <tr>
                        <td colspan="5" class="py-4 px-4 text-center text-lg font-medium text-red-700 fontcss">No data to display !</td>
                    </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#sprTable').DataTable({
            "pageLength": 17,
            "lengthMenu": [[17, 25, 50, -1], [17, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

```html
{# material_management/po_new/_supplier_autocomplete_suggestions.html #}
{% for suggestion in suggestions %}
    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer bg" 
         @click="$dispatch('select-supplier', { value: '{{ suggestion }}' });">
        {{ suggestion }}
    </div>
{% empty %}
    {# No suggestions #}
{% endfor %}
```

```html
{# material_management/po_new/po_pr_items.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Purchase Order - PR Items</h2>
    <p class="text-lg">You selected PR with code: <span class="font-semibold">{{ selected_code }}</span></p>
    <p class="mt-4 text-gray-600">This page would display the details of the selected Purchase Requisition and allow for PO creation.</p>
    <a href="{% url 'material_management:po_new_dashboard' %}" class="mt-6 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Back to PO New Dashboard
    </a>
</div>
{% endblock %}
```

```html
{# material_management/po_new/po_spr_items.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold mb-6">Purchase Order - SPR Items</h2>
    <p class="text-lg">You selected SPR with code: <span class="font-semibold">{{ selected_code }}</span></p>
    <p class="mt-4 text-gray-600">This page would display the details of the selected Supplier Purchase Requisition and allow for PO creation.</p>
    <a href="{% url 'material_management:po_new_dashboard' %}" class="mt-6 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        Back to PO New Dashboard
    </a>
</div>
{% endblock %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views.

**Instructions:**
URLs are structured to be clear and RESTful where appropriate. HTMX partials have their own dedicated URLs for dynamic loading.

```python
# material_management/urls.py
from django.urls import path
from .views import (
    PONewDashboardView,
    PurchaseRequisitionListView,
    SupplierPurchaseRequisitionListView,
    SupplierAutocompleteView,
    PO_PR_ItemsView,
    PO_SPR_ItemsView,
)

app_name = 'material_management'

urlpatterns = [
    # Main dashboard view for PO New
    path('po_new/', PONewDashboardView.as_view(), name='po_new_dashboard'),

    # HTMX endpoints for tab content and search results
    path('po_new/pr_list_partial/', PurchaseRequisitionListView.as_view(), name='pr_list_partial'),
    path('po_new/spr_list_partial/', SupplierPurchaseRequisitionListView.as_view(), name='spr_list_partial'),
    path('po_new/supplier_autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),

    # Destination pages after selecting a PR or SPR
    path('po_pr_items/<str:code>/', PO_PR_ItemsView.as_view(), name='po_pr_items'),
    path('po_spr_items/<str:code>/', PO_SPR_ItemsView.as_view(), name='po_spr_items'),
]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the models and views.

**Instructions:**
Tests cover model functionality (e.g., `autocomplete_search`) and view integration, including HTMX requests and redirects. Mocking is used for `managed=False` models to simulate data from database views/stored procedures without needing a complex test database setup for those specific scenarios. This ensures that the Django view logic is correctly implemented, assuming the underlying database provides the data as expected.

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.sessions.middleware import SessionMiddleware
from django.test.client import RequestFactory
import json

from .models import Supplier, PurchaseRequisitionSummary, SupplierPurchaseRequisitionSummary

# Helper function to add session data to a request for testing
def add_session_to_request(request):
    middleware = SessionMiddleware(lambda: None)
    middleware.process_request(request)
    request.session.save()

class SupplierModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for the Supplier model
        cls.comp_id_1 = 1
        cls.comp_id_2 = 2
        Supplier.objects.create(supplier_id='SUP001', supplier_name='Acme Corp', comp_id=cls.comp_id_1)
        Supplier.objects.create(supplier_id='SUP002', supplier_name='Beta Industries', comp_id=cls.comp_id_1)
        Supplier.objects.create(supplier_id='SUP003', supplier_name='Alpha Solutions', comp_id=cls.comp_id_1)
        Supplier.objects.create(supplier_id='SUP999', supplier_name='Another Company', comp_id=cls.comp_id_2) # Different company

    def test_supplier_creation(self):
        obj = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(obj.supplier_name, 'Acme Corp')
        self.assertEqual(obj.comp_id, self.comp_id_1)

    def test_supplier_str_representation(self):
        obj = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(str(obj), 'Acme Corp [SUP001]')

    def test_autocomplete_search_filter_by_prefix_and_company(self):
        # Test with prefix 'A' and company ID 1
        suggestions = Supplier.autocomplete_search('A', self.comp_id_1)
        self.assertIn('Acme Corp [SUP001]', suggestions)
        self.assertIn('Alpha Solutions [SUP003]', suggestions)
        self.assertNotIn('Beta Industries [SUP002]', suggestions) # Does not start with 'A'
        self.assertNotIn('Another Company [SUP999]', suggestions) # Wrong company ID
        self.assertEqual(len(suggestions), 2) # Only Acme and Alpha for comp_id 1 starting with 'A'
        
        # Test with different prefix
        suggestions = Supplier.autocomplete_search('beta', self.comp_id_1)
        self.assertIn('Beta Industries [SUP002]', suggestions)
        self.assertEqual(len(suggestions), 1)

        # Test with no matching prefix
        suggestions = Supplier.autocomplete_search('NonExistent', self.comp_id_1)
        self.assertEqual(len(suggestions), 0)

        # Test with another company ID
        suggestions = Supplier.autocomplete_search('Another', self.comp_id_2)
        self.assertIn('Another Company [SUP999]', suggestions)
        self.assertEqual(len(suggestions), 1)

# Mocking `managed=False` models for testing purposes
# In a real project, you would set up your test database to include these views or
# use a more sophisticated mocking framework. This approach is for demonstrating
# how the views would be tested assuming the data source is available.
class MockSummaryManager(models.Manager):
    """A manager that provides mock data for `managed=False` models."""
    def __init__(self, test_data):
        super().__init__()
        self._test_data = test_data

    def get_queryset(self):
        return self # This allows chaining

    def filter(self, **kwargs):
        # Simple filter logic for mock data
        filtered_data = [
            item for item in self._test_data
            if all(getattr(item, k) == v for k, v in kwargs.items())
        ]
        # Return a new instance of this manager with filtered data
        return MockSummaryManager(filtered_data)
    
    def order_by(self, *args, **kwargs):
        return self # No actual ordering for this simple mock

    def __iter__(self):
        return iter(self._test_data)

    def count(self):
        return len(self._test_data)
    
    def all(self):
        return self # Return self to allow .all() chaining

class PONewViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.factory = RequestFactory()
        self.comp_id = 1
        self.fin_year_id = '2023-2024'
        
        # Set session data for requests
        session_middleware = SessionMiddleware(lambda: None)
        request = self.factory.get('/')
        session_middleware.process_request(request)
        request.session['compid'] = self.comp_id
        request.session['finyear'] = self.fin_year_id
        request.session.save()
        self.client.cookies = request.cookies # Propagate session to client

        # Prepare mock data for summary models
        self.pr_mock_data = [
            PurchaseRequisitionSummary(supplier_name='Acme Corp', code='PR001', num_items=5),
            PurchaseRequisitionSummary(supplier_name='Beta Industries', code='PR002', num_items=10),
        ]
        self.spr_mock_data = [
            SupplierPurchaseRequisitionSummary(supplier_name='Acme Corp', code='SPR001', num_items=7),
            SupplierPurchaseRequisitionSummary(supplier_name='Gamma Corp', code='SPR002', num_items=12),
        ]

        # Patch models for views to use mock data during tests
        self.original_pr_objects = PurchaseRequisitionSummary.objects
        self.original_spr_objects = SupplierPurchaseRequisitionSummary.objects
        PurchaseRequisitionSummary.objects = MockSummaryManager(self.pr_mock_data)
        SupplierPurchaseRequisitionSummary.objects = MockSummaryManager(self.spr_mock_data)

        # Create test data for real Supplier model (used by autocomplete)
        Supplier.objects.create(supplier_id='SUP001', supplier_name='Acme Corp', comp_id=self.comp_id)
        Supplier.objects.create(supplier_id='SUP002', supplier_name='Beta Industries', comp_id=self.comp_id)

    def tearDown(self):
        # Restore original managers after tests
        PurchaseRequisitionSummary.objects = self.original_pr_objects
        SupplierPurchaseRequisitionSummary.objects = self.original_spr_objects

    def test_dashboard_view_get(self):
        response = self.client.get(reverse('material_management:po_new_dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_new/dashboard.html')
        # Check for initial loading indicator or general structure
        self.assertContains(response, 'PO - New')
        self.assertContains(response, 'id="tab-content"')

    def test_pr_list_partial_view_get(self):
        # Test HTMX request for PR list partial
        response = self.client.get(reverse('material_management:pr_list_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_new/_pr_table.html')
        self.assertContains(response, 'Acme Corp')
        self.assertContains(response, 'PR001')
        self.assertContains(response, 'Beta Industries')
        self.assertContains(response, 'PR002')
        self.assertTrue('prs' in response.context)
        self.assertEqual(len(response.context['prs']), len(self.pr_mock_data))

    def test_pr_list_partial_view_with_search_get(self):
        # Test filtering by supplier_code (assuming 'code' in summary is the filter key)
        response = self.client.get(reverse('material_management:pr_list_partial') + '?supplier_code=PR001', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_new/_pr_table.html')
        self.assertContains(response, 'Acme Corp')
        self.assertContains(response, 'PR001')
        self.assertNotContains(response, 'Beta Industries')
        self.assertEqual(len(response.context['prs']), 1)

    def test_pr_list_select_action_post(self):
        # Test the POST request for "Select" action, which triggers a redirect
        response = self.client.post(reverse('material_management:pr_list_partial'), {'code': 'PR001'})
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, reverse('material_management:po_pr_items', kwargs={'code': 'PR001'}))

    def test_spr_list_partial_view_get(self):
        # Test HTMX request for SPR list partial
        response = self.client.get(reverse('material_management:spr_list_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_new/_spr_table.html')
        self.assertContains(response, 'Acme Corp')
        self.assertContains(response, 'SPR001')
        self.assertContains(response, 'Gamma Corp')
        self.assertContains(response, 'SPR002')
        self.assertTrue('sprs' in response.context)
        self.assertEqual(len(response.context['sprs']), len(self.spr_mock_data))

    def test_spr_list_partial_view_with_search_get(self):
        # Test filtering by supplier_code for SPRs
        response = self.client.get(reverse('material_management:spr_list_partial') + '?supplier_code=SPR001', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_new/_spr_table.html')
        self.assertContains(response, 'Acme Corp')
        self.assertContains(response, 'SPR001')
        self.assertNotContains(response, 'Gamma Corp')
        self.assertEqual(len(response.context['sprs']), 1)

    def test_spr_list_select_action_post(self):
        # Test the POST request for "Select" action for SPRs
        response = self.client.post(reverse('material_management:spr_list_partial'), {'code': 'SPR001'})
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('material_management:po_spr_items', kwargs={'code': 'SPR001'}))

    def test_supplier_autocomplete_view(self):
        # Test HTMX request for autocomplete suggestions
        response = self.client.get(reverse('material_management:supplier_autocomplete') + '?q=A', HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Acme Corp [SUP001]')
        self.assertContains(response, 'Alpha Solutions [SUP003]')
        self.assertNotContains(response, 'Beta Industries [SUP002]') # Should not match 'A'

    def test_po_pr_items_view(self):
        # Test the placeholder destination view for PR items
        response = self.client.get(reverse('material_management:po_pr_items', kwargs={'code': 'PR001'}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_new/po_pr_items.html')
        self.assertContains(response, 'You selected PR with code: <span class="font-semibold">PR001</span>')

    def test_po_spr_items_view(self):
        # Test the placeholder destination view for SPR items
        response = self.client.get(reverse('material_management:po_spr_items', kwargs={'code': 'SPR001'}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_new/po_spr_items.html')
        self.assertContains(response, 'You selected SPR with code: <span class="font-semibold">SPR001</span>')

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates inherently integrate HTMX and Alpine.js:

-   **HTMX for dynamic updates:**
    -   The `PONewDashboardView`'s `dashboard.html` uses `hx-get` on tab buttons to dynamically load `_pr_table.html` or `_spr_table.html` into the `#tab-content` div.
    -   Search buttons within `_pr_table.html` and `_spr_table.html` use `hx-get` to refresh their respective table wrappers (`#pr_table_wrapper`, `#spr_table_wrapper`), sending search parameters via `hx-vals`.
    -   The supplier search input uses `hx-get` with `hx-trigger="keyup changed delay:300ms, search"` to fetch autocomplete suggestions from `SupplierAutocompleteView` into a dedicated suggestion div.
    -   "Select" buttons use `hx-post` to send the selected `code` to the list partial views, which then trigger a `redirect` to the `po_pr_items` or `po_spr_items` pages. `hx-push-url="true"` is used to update the browser's URL history on redirect.

-   **Alpine.js for UI state management:**
    -   The `dashboard.html` uses `x-data="{ activeTab: 'pr' }"` and `@click` directives to manage the active tab's visual state and dispatch HTMX requests.
    -   Autocomplete input fields use `x-data` and `x-model` for two-way data binding. A custom `@select-supplier.window` event is dispatched by the suggestion list, which the input listens to, setting its value and then triggering the search via `$dispatch('trigger-pr-search')` or `$dispatch('trigger-spr-search')`. This keeps all UI logic within the HTML and Alpine.js, avoiding manual JavaScript.

-   **DataTables for list views:**
    -   Both `_pr_table.html` and `_spr_table.html` contain a `<table>` element with a unique ID. A small `script` block within each partial template initializes DataTables on that specific table ID, providing client-side pagination, sorting, and search capabilities. Since HTMX swaps the *entire* partial, the `$(document).ready()` function ensures DataTables is re-initialized every time the partial is loaded.

-   **No full page reloads:** All user interactions (tab switching, search, autocomplete, selecting a row leading to redirect) are handled via HTMX, minimizing full page refreshes until a navigation to a new detail page is explicitly chosen.

---

## Final Notes

This modernization plan provides a solid foundation for migrating the `PO_New.aspx` functionality to Django.

-   **Replace Placeholders:** Ensure that `db_table` names in `managed=False` models (e.g., `View_GetSupplier_POPR`, `View_GetSupplier_POSPR`) are updated with the actual database view names or adjusted if the data comes from direct complex queries or stored procedures.
-   **DRY Templates:** The approach leverages partial templates (`_pr_table.html`, `_spr_table.html`, `_supplier_autocomplete_suggestions.html`) to keep templates concise and reusable.
-   **Fat Model, Thin View:** Business logic, such as the `autocomplete_search` method, resides within the `Supplier` model, keeping views focused on orchestrating data flow and rendering.
-   **Comprehensive Tests:** The included tests cover critical paths, ensuring the robustness and correctness of the migrated functionality. The mock data for `managed=False` models demonstrates how to test views dependent on external database structures without needing full replication in the test environment.
-   **Scalability & Maintainability:** Django's structured approach, combined with HTMX and Alpine.js, results in a more modular, maintainable, and scalable application compared to the legacy ASP.NET Web Forms model.

This automated conversion process significantly reduces manual coding, minimizes human error, and provides clear, actionable steps for a seamless transition to a modern Django architecture.