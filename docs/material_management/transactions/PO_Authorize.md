## ASP.NET to Django Conversion Script: PO Authorize Module

This document outlines a comprehensive modernization plan to transition the legacy ASP.NET PO Authorize module to a modern Django-based solution. The focus is on leveraging Django's strengths, including the "fat model, thin view" architecture, HTMX for dynamic interactions, Alpine.js for lightweight UI state, and DataTables for efficient data presentation. All communication is in plain English, suitable for business stakeholders, emphasizing automation-driven approaches.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

#### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code interacts with multiple tables to display and authorize Purchase Orders. We infer the primary tables and relevant columns involved in the `makegrid` and `Auth_Click` methods:

*   **`tblMM_PO_Master`**: This is the core table for Purchase Orders.
    *   `Id` (Primary Key, integer)
    *   `PONo` (string)
    *   `SysDate` (date/datetime, used for `Date`)
    *   `AmendmentNo` (string, used for `AmdNo` and `AmendmentNo`)
    *   `SessionId` (string/integer, foreign key to employee, used for `GenBy`)
    *   `Checked` (boolean/integer, indicates if checked)
    *   `CheckedDate` (date/datetime)
    *   `Approve` (boolean/integer, indicates if approved)
    *   `ApproveDate` (date/datetime)
    *   `Authorize` (boolean/integer, indicates if authorized)
    *   `AuthorizeDate` (date/datetime)
    *   `AuthorizeTime` (time)
    *   `CompId` (integer, foreign key to Company)
    *   `FinYearId` (integer, foreign key to Financial Year)
    *   `SupplierId` (string/integer, foreign key to supplier)
    *   `PRSPRFlag` (string/integer, '0' for PR, '1' for SPR, determines view page and rate register logic)

*   **`tblMM_Supplier_master`**: Used for supplier details and autocomplete.
    *   `SupplierId` (Primary Key, string/integer)
    *   `SupplierName` (string)
    *   `CompId` (integer)

*   **`tblHR_OfficeStaff`**: Used for employee names (`GenBy`).
    *   `EmpId` (Primary Key, string/integer, matches `SessionId` in `tblMM_PO_Master`)
    *   `EmployeeName` (string)
    *   `Title` (string)
    *   `CompId` (integer)

*   **`tblFinancial_master`**: Used for financial year display.
    *   `FinYearId` (Primary Key, integer)
    *   `FinYear` (string)

*   **`tblMM_PO_Details`**: Details of items within a PO.
    *   `MId` (integer, foreign key to `tblMM_PO_Master.Id`)
    *   `PONo` (string, foreign key to `tblMM_PO_Master.PONo`)
    *   `Rate` (decimal)
    *   `Discount` (decimal)
    *   `PF` (decimal)
    *   `ExST` (decimal)
    *   `VAT` (decimal)
    *   `PRNo` (string)
    *   `PRId` (integer, foreign key to `tblMM_PR_Details.Id`)
    *   `SPRNo` (string)
    *   `SPRId` (integer, foreign key to `tblMM_SPR_Details.Id`)

*   **`tblMM_PR_Details`**: Purchase Requisition details.
    *   `Id` (Primary Key, integer)
    *   `PRNo` (string)
    *   `ItemId` (string/integer)
    *   `MId` (integer, foreign key to `tblMM_PR_Master.Id`)

*   **`tblMM_SPR_Details`**: Sales Purchase Requisition details.
    *   `Id` (Primary Key, integer)
    *   `SPRNo` (string)
    *   `ItemId` (string/integer)
    *   `MId` (integer, foreign key to `tblMM_SPR_Master.Id`)

*   **`tblMM_Rate_Register`**: Records historical rates upon PO authorization.
    *   `SysDate`, `SysTime`, `CompId`, `FinYearId`, `SessionId`, `PONo`, `ItemId`, `Rate`, `Discount`, `PF`, `ExST`, `VAT`, `AmendmentNo`, `POId`, `PRId` (or `SPRId`).

*   **`tblMM_RateLockUnLock_Master`**: Manages item rate locking/unlocking.
    *   `ItemId`, `CompId`, `LockUnlock`, `Type`, `LockedbyTranaction`, `LockDate`, `LockTime`.

#### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations and business logic in the ASP.NET code.

**Instructions:**

*   **Read (List & Filter):**
    *   The primary function is to display a list of Purchase Orders awaiting authorization (`Approve='1'` and `Authorize='0'`).
    *   Users can search/filter by "Supplier" or "PO No".
    *   The data displayed includes PO No, Date, Amendment No, Generated By, Supplier Name, Supplier Code, Checked Date, Approved Date, and a checkbox for authorization along with Authorization Date if already authorized.
    *   Pagination is handled by the `GridView`.
*   **Update (Authorize):**
    *   A "Authorized" button triggers a bulk update operation.
    *   For each selected PO:
        *   Updates `tblMM_PO_Master` to set `Authorize='1'`, `AuthorizedBy` (current user), `AuthorizeDate`, and `AuthorizeTime`.
        *   Based on `PRSPRFlag` in `tblMM_PO_Master`, it iterates through `tblMM_PO_Details` linked to the PO.
        *   For each item in `tblMM_PO_Details`, it fetches the `ItemId` from either `tblMM_PR_Details` (if `PRSPRFlag` is '0') or `tblMM_SPR_Details` (if `PRSPRFlag` is '1').
        *   It then `INSERT`s a new record into `tblMM_Rate_Register` with details from `tblMM_PO_Details` and the fetched `ItemId`.
        *   It `UPDATE`s `tblMM_RateLockUnLock_Master` to set `LockUnlock='0'` (unlocked), `Type='2'`, `LockedbyTranaction` (current user), `LockDate`, `LockTime` for the specific `ItemId`.
*   **View (Redirect):**
    *   A "View" link button on each row redirects to either `PO_PR_View_Print_Details.aspx` or `PO_SPR_View_Print_Details.aspx` based on `PRSPRFlag`, passing PO details as query parameters.
*   **Autocomplete:**
    *   The "Supplier" textbox has an `AutoCompleteExtender` that fetches supplier names from `tblMM_Supplier_master`.

#### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **Search Controls:**
    *   `drpfield` (DropDownList): Selects search criteria (Supplier/PO No). This will be a standard HTML `<select>`.
    *   `txtSupplier` (TextBox): Supplier name input with autocomplete. This will be a standard HTML `<input type="text">` with HTMX for autocomplete and Alpine.js for handling the suggestion list.
    *   `txtPONo` (TextBox): PO Number input. Standard HTML `<input type="text">`.
    *   `Button1` (Button): "Search" button. Standard HTML `<button>` with HTMX for re-rendering the table.
*   **Action Button:**
    *   `Auth` (Button): "Authorized" button with client-side confirmation. Standard HTML `<button>` with HTMX for posting bulk authorization data and Alpine.js for confirmation.
*   **Data Grid:**
    *   `GridView2`: Displays the list of POs. This will be replaced by an HTMX-driven HTML `<table>` managed by DataTables. Each row will have a checkbox for authorization and a "View" link. The checkbox visibility logic will be handled directly in the Django template.

---

#### Step 4: Generate Django Code

We will create a new Django app, e.g., `material_management`, to house this module.

```python
# Project Structure:
# myproject/
# ├── myproject/
# ├── material_management/
# │   ├── migrations/
# │   ├── templates/
# │   │   └── material_management/
# │   │       └── po_authorize/
# │   │           ├── list.html
# │   │           └── _po_table.html
# │   │           └── _search_form.html
# │   ├── __init__.py
# │   ├── admin.py
# │   ├── apps.py
# │   ├── models.py
# │   ├── forms.py
# │   ├── urls.py
# │   ├── views.py
# │   └── tests.py
# └── manage.py
```

For clarity, we'll assume the existence of `request.session['compid']`, `request.session['finyear']`, and `request.user` being a valid authenticated user with `username` for `SessionId`.

---

#### 4.1 Models

**Task:** Create Django models based on the identified database schema, adhering to `managed = False` and the "fat model" principle.

**File:** `material_management/models.py`

```python
from django.db import models, transaction
from django.utils import timezone
import datetime

# Helper function placeholder for utility methods from ASP.NET clsFunctions
# In a real application, these would be proper utility classes or Django's built-in functionalities.
class ClsFunctions:
    @staticmethod
    def get_curr_date():
        return timezone.now().strftime('%Y-%m-%d')

    @staticmethod
    def get_curr_time():
        return timezone.now().strftime('%H:%M:%S')

    @staticmethod
    def from_date_dmy(date_str):
        if not date_str:
            return ''
        try:
            # Assuming date_str is in YYYY-MM-DD format from DB
            return datetime.datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S').strftime('%d/%m/%Y')
        except ValueError:
            try:
                return datetime.datetime.strptime(date_str, '%Y-%m-%d').strftime('%d/%m/%Y')
            except ValueError:
                return date_str # Return as-is if unable to parse

    @staticmethod
    def get_code_from_name(model_class, name_field, id_field, name_value, comp_id):
        # This would typically be handled by a proper foreign key relationship or a lookup service
        # For simplicity, mimicking the ASP.NET behavior
        try:
            obj = model_class.objects.using('legacy_db').get(**{f'{name_field}__iexact': name_value, 'CompId': comp_id})
            return getattr(obj, id_field)
        except model_class.DoesNotExist:
            return None

    # Dummy random key for redirection (Django would use URL signing or proper session management)
    @staticmethod
    def get_random_alphanumeric():
        import string
        import random
        return ''.join(random.choices(string.ascii_uppercase + string.digits, k=10))


# We assume 'legacy_db' is configured in settings.py for the old database.
# DATABASES = {
#     'default': { ... },
#     'legacy_db': {
#         'ENGINE': 'django.db.backends.mssql', # Or appropriate engine
#         'NAME': 'YourLegacyDB',
#         'USER': '...',
#         'PASSWORD': '...',
#         'HOST': '...',
#         'PORT': '',
#     }
# }

# Make sure to include the router if using multiple databases

class PoMaster(models.Model):
    # Core fields from tblMM_PO_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    sys_date = models.DateTimeField(db_column='SysDate', null=True, blank=True)
    amendment_no = models.CharField(db_column='AmendmentNo', max_length=50, null=True, blank=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, null=True, blank=True) # Corresponds to EmpId
    checked = models.BooleanField(db_column='Checked', default=False)
    checked_date = models.DateTimeField(db_column='CheckedDate', null=True, blank=True)
    approve = models.BooleanField(db_column='Approve', default=False)
    approve_date = models.DateTimeField(db_column='ApproveDate', null=True, blank=True)
    authorize = models.BooleanField(db_column='Authorize', default=False)
    authorize_date = models.DateTimeField(db_column='AuthorizeDate', null=True, blank=True)
    authorize_time = models.CharField(db_column='AuthorizeTime', max_length=10, null=True, blank=True) # Stored as string in ASP.NET
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    supplier_id = models.CharField(db_column='SupplierId', max_length=50) # Assuming it's a string ID
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1, null=True, blank=True) # '0' for PR, '1' for SPR

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblMM_PO_Master'
        verbose_name = 'PO Master'
        verbose_name_plural = 'PO Masters'

    def __str__(self):
        return f"PO No: {self.po_no} ({self.id})"

    # --- Fat Model Methods (Business Logic) ---

    @property
    def formatted_date(self):
        return ClsFunctions.from_date_dmy(str(self.sys_date))

    @property
    def formatted_checked_date(self):
        return ClsFunctions.from_date_dmy(str(self.checked_date))

    @property
    def formatted_approved_date(self):
        return ClsFunctions.from_date_dmy(str(self.approve_date))

    @property
    def formatted_authorized_date(self):
        return ClsFunctions.from_date_dmy(str(self.authorize_date))

    @property
    def is_authorized(self):
        # Directly from the 'Authorize' field in the database
        return self.authorize

    def get_supplier_name(self):
        try:
            supplier = SupplierMaster.objects.using('legacy_db').get(supplier_id=self.supplier_id, comp_id=self.comp_id)
            return supplier.supplier_name
        except SupplierMaster.DoesNotExist:
            return "N/A"

    def get_generated_by_name(self):
        try:
            employee = EmployeeStaff.objects.using('legacy_db').get(emp_id=self.session_id, comp_id=self.comp_id)
            # ASP.NET code concatenates Title + '. ' + EmployeeName
            return f"{employee.title}. {employee.employee_name}" if employee.title else employee.employee_name
        except EmployeeStaff.DoesNotExist:
            return "N/A"

    def get_financial_year_text(self):
        try:
            fin_year = FinancialYear.objects.using('legacy_db').get(fin_year_id=self.fin_year_id)
            return fin_year.fin_year
        except FinancialYear.DoesNotExist:
            return "N/A"

    @transaction.atomic(using='legacy_db')
    def authorize_po(self, authorized_by_user_id, comp_id, fin_year_id):
        """
        Implements the complex authorization logic from Auth_Click in C#.
        This method updates the PO master, inserts into rate register, and updates rate lock/unlock.
        """
        if self.authorize:
            # Already authorized, no action needed or raise an error
            return False, "PO already authorized."

        current_date = ClsFunctions.get_curr_date()
        current_time = ClsFunctions.get_curr_time()

        # 1. Update tblMM_PO_Master
        self.authorize = True
        self.authorized_by = authorized_by_user_id
        self.authorize_date = current_date
        self.authorize_time = current_time
        self.save(using='legacy_db')

        # 2. Process tblMM_PO_Details and related tables
        po_details = PoDetail.objects.using('legacy_db').filter(m_id=self.id, po_no=self.po_no)
        items_processed = 0

        for detail in po_details:
            item_id = None
            if self.pr_spr_flag == "0":  # For PR
                try:
                    pr_detail = PrDetail.objects.using('legacy_db').get(
                        id=detail.pr_id, pr_no=detail.pr_no, m_id=detail.m_id, comp_id=comp_id
                    )
                    item_id = pr_detail.item_id
                except PrDetail.DoesNotExist:
                    pass
            elif self.pr_spr_flag == "1":  # For SPR
                try:
                    spr_detail = SprDetail.objects.using('legacy_db').get(
                        id=detail.spr_id, spr_no=detail.spr_no, m_id=detail.m_id, comp_id=comp_id
                    )
                    item_id = spr_detail.item_id
                except SprDetail.DoesNotExist:
                    pass

            if item_id:
                # Insert into tblMM_Rate_Register
                RateRegister.objects.using('legacy_db').create(
                    sys_date=current_date,
                    sys_time=current_time,
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    session_id=authorized_by_user_id,
                    po_no=self.po_no,
                    item_id=item_id,
                    rate=detail.rate,
                    discount=detail.discount,
                    pf=detail.pf,
                    ex_st=detail.ex_st,
                    vat=detail.vat,
                    amendment_no=self.amendment_no,
                    po_id=self.id,
                    pr_id=detail.pr_id if self.pr_spr_flag == "0" else None,
                    spr_id=detail.spr_id if self.pr_spr_flag == "1" else None,
                )

                # Update tblMM_RateLockUnLock_Master
                RateLockUnlockMaster.objects.using('legacy_db').filter(
                    item_id=item_id, comp_id=comp_id
                ).update(
                    lock_unlock=False,  # '0' in ASP.NET code
                    type='2',
                    locked_by_transaction=authorized_by_user_id,
                    lock_date=current_date,
                    lock_time=current_time
                )
                items_processed += 1
        
        return True, f"PO {self.po_no} authorized successfully. {items_processed} items processed."


class SupplierMaster(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier Master'
        verbose_name_plural = 'Supplier Masters'

    def __str__(self):
        return f"{self.supplier_name} [{self.supplier_id}]"


class EmployeeStaff(models.Model):
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    title = models.CharField(db_column='Title', max_length=50, null=True, blank=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee Staff'
        verbose_name_plural = 'Employee Staff'

    def __str__(self):
        return self.employee_name


class FinancialYear(models.Model):
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    fin_year = models.CharField(db_column='FinYear', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year

# Models for Authorization Logic (PoDetail, PrDetail, SprDetail, RateRegister, RateLockUnlockMaster)
class PoDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.IntegerField(db_column='MId') # Foreign key to PoMaster.id
    po_no = models.CharField(db_column='PONo', max_length=50) # Redundant FK, typical in legacy DBs
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4, null=True, blank=True)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4, null=True, blank=True)
    pf = models.DecimalField(db_column='PF', max_digits=18, decimal_places=4, null=True, blank=True)
    ex_st = models.DecimalField(db_column='ExST', max_digits=18, decimal_places=4, null=True, blank=True)
    vat = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=4, null=True, blank=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50, null=True, blank=True)
    pr_id = models.IntegerField(db_column='PRId', null=True, blank=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50, null=True, blank=True)
    spr_id = models.IntegerField(db_column='SPRId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'
        verbose_name = 'PO Detail'
        verbose_name_plural = 'PO Details'

class PrDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    pr_no = models.CharField(db_column='PRNo', max_length=50)
    item_id = models.CharField(db_column='ItemId', max_length=50)
    m_id = models.IntegerField(db_column='MId') # Foreign key to PR_Master.id
    comp_id = models.IntegerField(db_column='CompId', default=0) # Assuming this column exists

    class Meta:
        managed = False
        db_table = 'tblMM_PR_Details'
        verbose_name = 'PR Detail'
        verbose_name_plural = 'PR Details'

class SprDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    spr_no = models.CharField(db_column='SPRNo', max_length=50)
    item_id = models.CharField(db_column='ItemId', max_length=50)
    m_id = models.IntegerField(db_column='MId') # Foreign key to SPR_Master.id
    comp_id = models.IntegerField(db_column='CompId', default=0) # Assuming this column exists

    class Meta:
        managed = False
        db_table = 'tblMM_SPR_Details'
        verbose_name = 'SPR Detail'
        verbose_name_plural = 'SPR Details'

class RateRegister(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming auto-increment
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.CharField(db_column='SysTime', max_length=10)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    session_id = models.CharField(db_column='SessionId', max_length=50)
    po_no = models.CharField(db_column='PONo', max_length=50)
    item_id = models.CharField(db_column='ItemId', max_length=50)
    rate = models.DecimalField(db_column='Rate', max_digits=18, decimal_places=4)
    discount = models.DecimalField(db_column='Discount', max_digits=18, decimal_places=4)
    pf = models.DecimalField(db_column='PF', max_digits=18, decimal_places=4)
    ex_st = models.DecimalField(db_column='ExST', max_digits=18, decimal_places=4)
    vat = models.DecimalField(db_column='VAT', max_digits=18, decimal_places=4)
    amendment_no = models.CharField(db_column='AmendmentNo', max_length=50, null=True, blank=True)
    po_id = models.IntegerField(db_column='POId')
    pr_id = models.IntegerField(db_column='PRId', null=True, blank=True)
    spr_id = models.IntegerField(db_column='SPRId', null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_Rate_Register'
        verbose_name = 'Rate Register'
        verbose_name_plural = 'Rate Registers'


class RateLockUnlockMaster(models.Model):
    id = models.AutoField(db_column='Id', primary_key=True) # Assuming auto-increment
    item_id = models.CharField(db_column='ItemId', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    lock_unlock = models.BooleanField(db_column='LockUnlock')
    type = models.CharField(db_column='Type', max_length=10, null=True, blank=True)
    locked_by_transaction = models.CharField(db_column='LockedbyTranaction', max_length=50, null=True, blank=True)
    lock_date = models.DateField(db_column='LockDate', null=True, blank=True)
    lock_time = models.CharField(db_column='LockTime', max_length=10, null=True, blank=True)

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock/Unlock Master'
        verbose_name_plural = 'Rate Lock/Unlock Masters'

```

#### 4.2 Forms

**Task:** Define a Django form for the search criteria. This will be a simple `Form` as it's for filtering, not tied directly to a model instance for creation/update.

**File:** `material_management/forms.py`

```python
from django import forms

class PoAuthorizeSearchForm(forms.Form):
    # Mimics drpfield selection (0: Supplier, 1: PO No)
    SEARCH_CHOICES = [
        ('0', 'Supplier'),
        ('1', 'PO No'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm',
            'hx-get': "{% url 'material_management:po_authorize_table' %}",
            'hx-target': '#po_masterTable-container',
            'hx-swap': 'innerHTML',
            'hx-trigger': 'change',
            'hx-include': '#searchForm', # Include other form fields on change
            'x-on:change': 'if ($event.target.value == "1") { $refs.po_no_input.classList.remove("hidden"); $refs.supplier_input.classList.add("hidden"); } else { $refs.supplier_input.classList.remove("hidden"); $refs.po_no_input.classList.add("hidden"); }'
        }),
        label="Search By"
    )
    supplier_name = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'id': 'txtSupplier', # Match ASP.NET ID for Alpine.js/HTMX
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm w-96 autocomplete-input',
            'placeholder': 'Enter Supplier Name',
            'hx-get': "{% url 'material_management:supplier_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#autocomplete-results',
            'hx-indicator': '.htmx-indicator',
            'autocomplete': 'off',
            'x-ref': 'supplier_input'
        }),
        label="Supplier Name"
    )
    po_no = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'id': 'txtPONo', # Match ASP.NET ID
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm',
            'placeholder': 'Enter PO Number',
            'x-ref': 'po_no_input',
            'x-bind:class': 'search_field_val == "1" ? "" : "hidden"' # Alpine.js for visibility
        }),
        label="PO No"
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initially hide PO No field if supplier is default
        if self.initial.get('search_field', '0') == '0':
            self.fields['po_no'].widget.attrs['class'] += ' hidden'
        else:
            self.fields['supplier_name'].widget.attrs['class'] += ' hidden'

```

#### 4.3 Views

**Task:** Implement the page display, data fetching (for the table), authorization, and autocomplete using Django Class-Based Views (CBVs).

**File:** `material_management/views.py`

```python
from django.views.generic import ListView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt # Use carefully, for testing HTMX POST without full CSRF setup

from .models import PoMaster, SupplierMaster, ClsFunctions
from .forms import PoAuthorizeSearchForm

class PoAuthorizeListView(ListView):
    """
    Main view to display the PO Authorization page.
    It renders the initial page with the search form and a placeholder for the table.
    """
    model = PoMaster
    template_name = 'material_management/po_authorize/list.html'
    context_object_name = 'po_masters' # Not directly used for table, but good practice
    paginate_by = 20 # ASP.NET GridView had PageSize="20"

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass an empty form for initial render. The table will be loaded via HTMX.
        context['form'] = PoAuthorizeSearchForm(self.request.GET or None)
        return context

    def get_queryset(self):
        # This queryset is largely for the ListView itself if it were to render the table directly.
        # For HTMX, the actual table data is fetched by PoAuthorizeTablePartialView.
        # We can optimize this by only querying for the initial table load in the partial view.
        return PoMaster.objects.none() # Return empty queryset as table is loaded via HTMX


class PoAuthorizeTablePartialView(TemplateView):
    """
    HTMX-driven view to render only the PO authorization table.
    This replaces the makegrid function in ASP.NET.
    """
    template_name = 'material_management/po_authorize/_po_table.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        comp_id = self.request.session.get('compid')
        fin_year_id = self.request.session.get('finyear')

        # Start with base queryset: Approved and not Authorized, for current company/fin year
        queryset = PoMaster.objects.using('legacy_db').filter(
            comp_id=comp_id,
            fin_year_id__lte=fin_year_id, # ASP.NET code used FinYearId<='FyId'
            approve=True,
            authorize=False
        ).order_by('-id') # Order by Id Desc

        search_field = self.request.GET.get('search_field', '0')
        supplier_name = self.request.GET.get('supplier_name', '').strip()
        po_no = self.request.GET.get('po_no', '').strip()

        # Apply filters based on search_field
        if search_field == '1' and po_no: # Search by PO No
            queryset = queryset.filter(po_no=po_no)
        elif search_field == '0' and supplier_name: # Search by Supplier
            # The ASP.NET code used fun.getCode(txtSupplier.Text) then filtered by SupplierId.
            # We'll try to get the SupplierId from name.
            try:
                supplier_code = ClsFunctions.get_code_from_name(SupplierMaster, 'supplier_name', 'supplier_id', supplier_name, comp_id)
                if supplier_code:
                    queryset = queryset.filter(supplier_id=supplier_code)
                else:
                    queryset = PoMaster.objects.none() # No matching supplier, so no results
            except Exception:
                queryset = PoMaster.objects.none() # Error getting supplier code, no results

        # In a real scenario, you might want to `select_related` or `prefetch_related`
        # for `EmployeeStaff`, `SupplierMaster`, and `FinancialYear` for efficiency.
        # However, since these are separate `models.Model` objects with `managed=False`
        # and potentially different databases, direct ORM joins might not be feasible.
        # The fat model properties `get_supplier_name`, etc., will handle lookups per row.

        context['po_masters'] = queryset
        context['form'] = PoAuthorizeSearchForm(self.request.GET or None) # Re-instantiate form with current GET params
        return context

    # If the search form was submitted with a POST, ensure it re-renders the table
    def post(self, request, *args, **kwargs):
        # This might be hit if the search form is submitted via hx-post
        # It should behave like a GET for the table content
        return self.get(request, *args, **kwargs)


@method_decorator(csrf_exempt, name='dispatch') # Disable CSRF for simplicity with HTMX POST for now. Re-enable with proper setup.
class PoAuthorizeActionView(View):
    """
    View to handle the bulk authorization action.
    Replaces the Auth_Click function in ASP.NET.
    """
    def post(self, request, *args, **kwargs):
        selected_po_ids = request.POST.getlist('selected_pos[]') # Get list of checked PO IDs
        authorized_count = 0
        comp_id = request.session.get('compid')
        fin_year_id = request.session.get('finyear')
        user_id = request.user.username # Assuming username maps to ASP.NET SessionId

        if not selected_po_ids:
            messages.warning(request, "No records selected for authorization.")
            # HTMX needs a 204 or a re-render trigger.
            response = HttpResponse(status=204) # No Content
            response['HX-Trigger'] = 'showMessageEvent' # Custom event for Alpine to show message
            return response

        for po_id in selected_po_ids:
            try:
                po_master = PoMaster.objects.using('legacy_db').get(id=po_id, comp_id=comp_id, authorize=False)
                success, msg = po_master.authorize_po(user_id, comp_id, fin_year_id)
                if success:
                    authorized_count += 1
                else:
                    messages.error(request, f"Failed to authorize PO {po_master.po_no}: {msg}")
            except PoMaster.DoesNotExist:
                messages.error(request, f"PO with ID {po_id} not found or already authorized.")
            except Exception as e:
                messages.error(request, f"Error authorizing PO ID {po_id}: {e}")

        if authorized_count > 0:
            messages.success(request, f"{authorized_count} PO(s) authorized successfully.")
        else:
            messages.info(request, "No new POs were authorized.")
        
        # HTMX will trigger a refresh of the table after a successful action
        response = HttpResponse(status=204) # No Content
        response['HX-Trigger'] = 'refreshPoMasterList' # Custom HTMX trigger for the table container
        response['HX-Trigger'] += ',showMessageEvent' # Also trigger message display
        return response


class SupplierAutocompleteView(View):
    """
    Web service method for supplier autocomplete.
    Replaces GetCompletionList static method in ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '').strip()
        comp_id = request.session.get('compid')

        if not prefix_text or not comp_id:
            return JsonResponse([], safe=False)

        # Case-insensitive search for supplier names starting with prefix_text
        suppliers = SupplierMaster.objects.using('legacy_db').filter(
            supplier_name__istartswith=prefix_text,
            comp_id=comp_id
        ).values('supplier_name', 'supplier_id')[:10] # Limit results as in ASP.NET

        results = [f"{s['supplier_name']} [{s['supplier_id']}]" for s in suppliers]
        
        return JsonResponse(results, safe=False)

class PoViewRedirectView(View):
    """
    View to handle the "View" link click from the GridView.
    Replaces the Response.Redirect logic in GridView2_RowCommand.
    """
    def get(self, request, pk, *args, **kwargs):
        comp_id = request.session.get('compid')
        try:
            po_master = PoMaster.objects.using('legacy_db').get(id=pk, comp_id=comp_id)
            
            # This is where the original ASP.NET logic redirects to different pages
            # For Django, we would map these to appropriate Django views/URLs
            # and pass the necessary context. This is a placeholder for that.
            
            # Example redirection (replace with actual Django URLs):
            if po_master.pr_spr_flag == "0": # For PR
                # return redirect('material_management:po_pr_view_details', mid=pk, pono=po_master.po_no,
                #                  code=po_master.supplier_id, amdno=po_master.amendment_no,
                #                  key=ClsFunctions.get_random_alphanumeric(), modid=6, submodid=35,
                #                  parentpage='po_authorize_list')
                messages.info(request, f"Redirecting to PR View Details for PO: {po_master.po_no}")
                return HttpResponse(f"Redirecting to PO_PR_View_Print_Details.aspx?mid={pk}&pono={po_master.po_no}&Code={po_master.supplier_id}&AmdNo={po_master.amendment_no}&Key={ClsFunctions.get_random_alphanumeric()}&Trans=&ModId=6&SubModId=35&parentpage=PO_Authorize.aspx")
            else: # For SPR
                # return redirect('material_management:po_spr_view_details', mid=pk, pono=po_master.po_no,
                #                  code=po_master.supplier_id, amdno=po_master.amendment_no,
                #                  key=ClsFunctions.get_random_alphanumeric(), modid=6, submodid=35,
                #                  parentpage='po_authorize_list')
                messages.info(request, f"Redirecting to SPR View Details for PO: {po_master.po_no}")
                return HttpResponse(f"Redirecting to PO_SPR_View_Print_Details.aspx?mid={pk}&pono={po_master.po_no}&Code={po_master.supplier_id}&AmdNo={po_master.amendment_no}&Key={ClsFunctions.get_random_alphanumeric()}&ModId=6&SubModId=35&parentpage=PO_Authorize.aspx")

        except PoMaster.DoesNotExist:
            messages.error(request, "Purchase Order not found.")
            return HttpResponse("Purchase Order not found.", status=404)
        except Exception as e:
            messages.error(request, f"An error occurred: {e}")
            return HttpResponse(f"An error occurred: {e}", status=500)

```

#### 4.4 Templates

**Task:** Create templates for the list view and the partial table view. These templates leverage HTMX, Alpine.js, and DataTables.

**File:** `material_management/templates/material_management/po_authorize/list.html`

```html
{% extends 'core/base.html' %}
{% load static %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ search_field_val: '{{ form.search_field.value|default:'0' }}' }">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">PO Authorize</h2>
    </div>
    
    <!-- Search and Authorize Controls -->
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <form id="searchForm" hx-get="{% url 'material_management:po_authorize_table' %}"
              hx-target="#po_masterTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_search_field">
            {% csrf_token %}
            <div class="flex items-center space-x-4 mb-4">
                <label for="id_search_field" class="block text-sm font-medium text-gray-700">Search By:</label>
                <div x-init="search_field_val = '{{ form.search_field.value|default:'0' }}'">
                    {{ form.search_field }}
                </div>
                
                <div x-show="search_field_val == '0'" x-cloak class="relative flex-grow">
                    {{ form.supplier_name }}
                    <div id="autocomplete-results" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto hidden"
                         :class="{ 'hidden': !($refs.supplier_input.value && $refs.supplier_input.value.length > 0) || ! ($refs.autocompleteContainer && $refs.autocompleteContainer.children.length > 0) }"
                         x-ref="autocompleteContainer">
                        <!-- Autocomplete results loaded here by HTMX -->
                    </div>
                </div>
                
                <div x-show="search_field_val == '1'" x-cloak class="flex-grow">
                    {{ form.po_no }}
                </div>

                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>

                 <!-- Loading indicator for HTMX -->
                <span class="htmx-indicator ml-4">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </span>
            </div>
            <div class="flex justify-end">
                <button type="button" 
                        class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded"
                        hx-post="{% url 'material_management:po_authorize_action' %}"
                        hx-include="#po_masterTable input[type=checkbox]:checked"
                        hx-confirm="Are you sure you want to authorize the selected Purchase Orders?"
                        hx-target="body" hx-swap="none">
                    Authorized
                </button>
            </div>
        </form>
    </div>
    
    <!-- PO Master Table Container (loaded via HTMX) -->
    <div id="po_masterTable-container"
         hx-trigger="load, refreshPoMasterList from:body"
         hx-get="{% url 'material_management:po_authorize_table' %}"
         hx-swap="innerHTML"
         hx-indicator=".htmx-indicator">
        <!-- Initial loading state -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Purchase Orders...</p>
        </div>
    </div>
</div>

<!-- Simple message display for HTMX triggered events -->
<div x-data="{ show: false, message: '', type: '' }"
     x-cloak
     x-show="show"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform -translate-y-full"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform -translate-y-full"
     @showMessageEvent.window="show = true; message = $event.detail.message; type = $event.detail.type; setTimeout(() => show = false, 5000);"
     :class="{
        'bg-green-100 border border-green-400 text-green-700': type === 'success',
        'bg-red-100 border border-red-400 text-red-700': type === 'error',
        'bg-yellow-100 border border-yellow-400 text-yellow-700': type === 'warning',
        'bg-blue-100 border border-blue-400 text-blue-700': type === 'info'
     }"
     class="fixed top-0 left-0 right-0 p-4 z-50 rounded-md">
    <div class="flex items-center justify-between">
        <span>{{ '{{ message }}' }}</span>
        <button @click="show = false" class="text-gray-500 hover:text-gray-700 focus:outline-none">
            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
        </button>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.7/js/dataTables.min.js"></script>
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('poAuthorizePage', () => ({
            search_field_val: '{{ form.search_field.value|default:'0' }}',
            init() {
                this.$watch('search_field_val', value => {
                    if (value === '1') {
                        this.$refs.po_no_input.value = '';
                    } else {
                        this.$refs.supplier_input.value = '';
                    }
                });
            }
        }));

        // HTMX on messages
        document.body.addEventListener('htmx:afterSwap', function (evt) {
            // Check for Django messages and dispatch a custom event
            const messagesDiv = document.querySelector('.messages');
            if (messagesDiv) {
                messagesDiv.querySelectorAll('li').forEach(function(msgElement) {
                    let message = msgElement.textContent.trim();
                    let type = 'info';
                    if (msgElement.classList.contains('success')) type = 'success';
                    if (msgElement.classList.contains('error')) type = 'error';
                    if (msgElement.classList.contains('warning')) type = 'warning';
                    window.dispatchEvent(new CustomEvent('showMessageEvent', { detail: { message: message, type: type } }));
                });
                messagesDiv.remove(); // Remove the Django messages from DOM after dispatching
            }

            // Handle autocomplete list visibility
            if (evt.detail.elt.id === 'autocomplete-results') {
                const resultsDiv = document.getElementById('autocomplete-results');
                if (resultsDiv && resultsDiv.children.length > 0) {
                    resultsDiv.classList.remove('hidden');
                } else {
                    resultsDiv.classList.add('hidden');
                }
            }
        });
        
        // Hide autocomplete results when clicking outside
        document.addEventListener('click', function(event) {
            const autocompleteInput = document.getElementById('txtSupplier');
            const autocompleteResults = document.getElementById('autocomplete-results');
            if (autocompleteInput && autocompleteResults && !autocompleteInput.contains(event.target) && !autocompleteResults.contains(event.target)) {
                autocompleteResults.classList.add('hidden');
            }
        });

        // Set selected autocomplete value
        document.body.addEventListener('click', function(event) {
            if (event.target.closest('.autocomplete-result-item')) {
                const selectedValue = event.target.textContent.trim();
                document.getElementById('txtSupplier').value = selectedValue;
                document.getElementById('autocomplete-results').classList.add('hidden');
            }
        });
    });
</script>
{% endblock %}
```

**File:** `material_management/templates/material_management/po_authorize/_po_table.html`

```html
{% load static %}
<div class="overflow-x-auto bg-white rounded-lg shadow-lg">
    <table id="po_masterTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amd No</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Checked</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For Authorize</th>
                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if po_masters %}
                {% for po in po_masters %}
                <tr>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ po.get_financial_year_text }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ po.po_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ po.formatted_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ po.amendment_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ po.get_generated_by_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-left">{{ po.get_supplier_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ po.supplier_id }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ po.formatted_checked_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">{{ po.formatted_approved_date }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        {% if not po.is_authorized %}
                            <input type="checkbox" name="selected_pos[]" value="{{ po.id }}" class="form-checkbox h-4 w-4 text-blue-600">
                        {% else %}
                            {{ po.formatted_authorized_date }}
                        {% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-center">
                        <a href="{% url 'material_management:po_authorize_view' po.pk %}" 
                           class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded text-xs"
                           target="_blank">
                            View
                        </a>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
                <tr>
                    <td colspan="12" class="py-4 px-4 text-center text-sm font-medium text-gray-500">
                        No data to display !
                    </td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#po_masterTable')) {
            $('#po_masterTable').DataTable().destroy();
        }
        $('#po_masterTable').DataTable({
            "pageLength": 20, // Match ASP.NET GridView PageSize
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "orderable": false, "targets": [0, 10, 11] }, // SN, Checkbox, Actions columns
                { "searchable": false, "targets": [0, 10, 11] }
            ]
        });
    });
</script>

```

**File:** `material_management/templates/material_management/po_authorize/_supplier_autocomplete_results.html`

```html
{% for item in results %}
    <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer autocomplete-result-item">{{ item }}</div>
{% empty %}
    <div class="px-4 py-2 text-gray-500">No results found</div>
{% endfor %}
```

#### 4.5 URLs

**Task:** Define URL patterns for the views within the `material_management` app.

**File:** `material_management/urls.py`

```python
from django.urls import path
from .views import (
    PoAuthorizeListView, 
    PoAuthorizeTablePartialView, 
    PoAuthorizeActionView,
    SupplierAutocompleteView,
    PoViewRedirectView
)

app_name = 'material_management' # Namespace for URLs

urlpatterns = [
    path('po_authorize/', PoAuthorizeListView.as_view(), name='po_authorize_list'),
    path('po_authorize/table/', PoAuthorizeTablePartialView.as_view(), name='po_authorize_table'),
    path('po_authorize/authorize/', PoAuthorizeActionView.as_view(), name='po_authorize_action'),
    path('po_authorize/supplier_autocomplete/', SupplierAutocompleteView.as_view(), name='supplier_autocomplete'),
    path('po_authorize/view/<int:pk>/', PoViewRedirectView.as_view(), name='po_authorize_view'),
]
```

**Note:** Remember to include this app's URLs in your project's main `urls.py`:
`path('material_management/', include('material_management.urls', namespace='material_management')),`

#### 4.6 Tests

**Task:** Write comprehensive tests for the models and views, ensuring good coverage.

**File:** `material_management/tests.py`

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from django.test import override_settings
from unittest.mock import patch, MagicMock
from datetime import datetime, date

from .models import PoMaster, SupplierMaster, EmployeeStaff, FinancialYear, \
                    PoDetail, PrDetail, SprDetail, RateRegister, RateLockUnlockMaster, ClsFunctions

# Mock database settings for testing legacy_db.
# In a real setup, you'd configure a test database for legacy_db.
@override_settings(
    DATABASES={
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': ':memory:',
        },
        'legacy_db': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': ':memory:',
        }
    }
)
class PoMasterModelTest(TestCase):
    # Use a dummy database for legacy_db for tests
    databases = {'legacy_db'}

    @classmethod
    def setUpTestData(cls):
        # Create test data directly in the mocked 'legacy_db'
        cls.company_id = 1
        cls.fin_year_id = 2023
        cls.user_id = 'testuser'

        # Create FinancialYear
        FinancialYear.objects.using('legacy_db').create(
            fin_year_id=cls.fin_year_id, fin_year="2023-2024"
        )
        # Create SupplierMaster
        SupplierMaster.objects.using('legacy_db').create(
            supplier_id='SUP001', supplier_name="Test Supplier A", comp_id=cls.company_id
        )
        SupplierMaster.objects.using('legacy_db').create(
            supplier_id='SUP002', supplier_name="Test Supplier B", comp_id=cls.company_id
        )
        # Create EmployeeStaff
        EmployeeStaff.objects.using('legacy_db').create(
            emp_id=cls.user_id, employee_name="Test User", title="Mr.", comp_id=cls.company_id
        )

        # Create PoMaster instances
        cls.po_pending = PoMaster.objects.using('legacy_db').create(
            id=1, po_no="PO001", sys_date="2023-01-15 10:00:00", amendment_no="0",
            session_id=cls.user_id, checked=True, checked_date="2023-01-16 11:00:00",
            approve=True, approve_date="2023-01-17 12:00:00", authorize=False,
            comp_id=cls.company_id, fin_year_id=cls.fin_year_id, supplier_id='SUP001', pr_spr_flag='0'
        )
        cls.po_authorized = PoMaster.objects.using('legacy_db').create(
            id=2, po_no="PO002", sys_date="2023-02-15 10:00:00", amendment_no="0",
            session_id=cls.user_id, checked=True, checked_date="2023-02-16 11:00:00",
            approve=True, approve_date="2023-02-17 12:00:00", authorize=True,
            authorize_date="2023-02-18 13:00:00", authorize_time="13:00:00",
            authorized_by='anotheruser', comp_id=cls.company_id, fin_year_id=cls.fin_year_id, supplier_id='SUP002', pr_spr_flag='1'
        )

        # Create PO Details for po_pending
        PoDetail.objects.using('legacy_db').create(
            id=101, m_id=cls.po_pending.id, po_no=cls.po_pending.po_no,
            rate=100.00, discount=5.00, pf=2.00, ex_st=1.00, vat=10.00,
            pr_no="PR001", pr_id=201
        )
        # Create PR Detail for PoDetail
        PrDetail.objects.using('legacy_db').create(
            id=201, pr_no="PR001", item_id="ITEMA", m_id=1, comp_id=cls.company_id
        )
        RateLockUnlockMaster.objects.using('legacy_db').create(
            id=301, item_id="ITEMA", comp_id=cls.company_id, lock_unlock=True, type='1'
        )

    def test_po_master_creation(self):
        po = PoMaster.objects.using('legacy_db').get(id=1)
        self.assertEqual(po.po_no, 'PO001')
        self.assertFalse(po.authorize)

    def test_formatted_date_properties(self):
        po = PoMaster.objects.using('legacy_db').get(id=1)
        self.assertEqual(po.formatted_date, '15/01/2023')
        self.assertEqual(po.formatted_checked_date, '16/01/2023')
        self.assertEqual(po.formatted_approved_date, '17/01/2023')
        self.assertEqual(po.formatted_authorized_date, '') # Not authorized yet

        po_auth = PoMaster.objects.using('legacy_db').get(id=2)
        self.assertEqual(po_auth.formatted_authorized_date, '18/02/2023')

    def test_is_authorized_property(self):
        self.assertFalse(self.po_pending.is_authorized)
        self.assertTrue(self.po_authorized.is_authorized)

    def test_get_supplier_name(self):
        self.assertEqual(self.po_pending.get_supplier_name(), 'Test Supplier A')
        
    def test_get_generated_by_name(self):
        self.assertEqual(self.po_pending.get_generated_by_name(), 'Mr. Test User')

    def test_get_financial_year_text(self):
        self.assertEqual(self.po_pending.get_financial_year_text(), '2023-2024')

    @patch('material_management.models.ClsFunctions.get_curr_date', return_value='2023-03-01')
    @patch('material_management.models.ClsFunctions.get_curr_time', return_value='14:30:00')
    def test_authorize_po_success_pr_flag(self, mock_get_curr_time, mock_get_curr_date):
        po = PoMaster.objects.using('legacy_db').get(id=1)
        
        initial_rate_register_count = RateRegister.objects.using('legacy_db').count()
        initial_rate_lock_unlock_count = RateLockUnlockMaster.objects.using('legacy_db').filter(lock_unlock=False).count()

        success, msg = po.authorize_po(self.user_id, self.company_id, self.fin_year_id)
        
        self.assertTrue(success)
        self.assertIn("authorized successfully", msg)
        
        po.refresh_from_db(using='legacy_db')
        self.assertTrue(po.authorize)
        self.assertEqual(po.authorized_by, self.user_id)
        self.assertEqual(po.authorize_date, date(2023, 3, 1))
        self.assertEqual(po.authorize_time, '14:30:00')

        self.assertEqual(RateRegister.objects.using('legacy_db').count(), initial_rate_register_count + 1)
        new_rate_register = RateRegister.objects.using('legacy_db').latest('id')
        self.assertEqual(new_rate_register.po_id, po.id)
        self.assertEqual(new_rate_register.item_id, 'ITEMA')
        
        rate_lock_unlock = RateLockUnlockMaster.objects.using('legacy_db').get(item_id='ITEMA', comp_id=self.company_id)
        self.assertFalse(rate_lock_unlock.lock_unlock) # Should be '0' / False

    def test_authorize_po_already_authorized(self):
        po = PoMaster.objects.using('legacy_db').get(id=2)
        success, msg = po.authorize_po(self.user_id, self.company_id, self.fin_year_id)
        self.assertFalse(success)
        self.assertEqual(msg, "PO already authorized.")

class PoAuthorizeViewsTest(TestCase):
    # Use a dummy database for legacy_db for tests
    databases = {'legacy_db'}

    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.fin_year_id = 2023
        cls.user_id = 'testuser'

        FinancialYear.objects.using('legacy_db').create(
            fin_year_id=cls.fin_year_id, fin_year="2023-2024"
        )
        SupplierMaster.objects.using('legacy_db').create(
            supplier_id='SUP001', supplier_name="Test Supplier A", comp_id=cls.company_id
        )
        SupplierMaster.objects.using('legacy_db').create(
            supplier_id='SUP002', supplier_name="Test Supplier B", comp_id=cls.company_id
        )
        EmployeeStaff.objects.using('legacy_db').create(
            emp_id=cls.user_id, employee_name="Test User", title="Mr.", comp_id=cls.company_id
        )
        EmployeeStaff.objects.using('legacy_db').create(
            emp_id='anotheruser', employee_name="Another User", title="Ms.", comp_id=cls.company_id
        )

        PoMaster.objects.using('legacy_db').create(
            id=1, po_no="PO001", sys_date="2023-01-15 10:00:00", amendment_no="0",
            session_id=cls.user_id, checked=True, checked_date="2023-01-16 11:00:00",
            approve=True, approve_date="2023-01-17 12:00:00", authorize=False,
            comp_id=cls.company_id, fin_year_id=cls.fin_year_id, supplier_id='SUP001', pr_spr_flag='0'
        )
        PoMaster.objects.using('legacy_db').create(
            id=2, po_no="PO002", sys_date="2023-02-15 10:00:00", amendment_no="0",
            session_id=cls.user_id, checked=True, checked_date="2023-02-16 11:00:00",
            approve=True, approve_date="2023-02-17 12:00:00", authorize=True,
            authorize_date="2023-02-18 13:00:00", authorize_time="13:00:00",
            authorized_by='anotheruser', comp_id=cls.company_id, fin_year_id=cls.fin_year_id, supplier_id='SUP002', pr_spr_flag='1'
        )

        PoDetail.objects.using('legacy_db').create(
            id=101, m_id=1, po_no="PO001", rate=100.00, discount=5.00, pf=2.00, ex_st=1.00, vat=10.00,
            pr_no="PR001", pr_id=201
        )
        PrDetail.objects.using('legacy_db').create(
            id=201, pr_no="PR001", item_id="ITEMA", m_id=1, comp_id=cls.company_id
        )
        RateLockUnlockMaster.objects.using('legacy_db').create(
            id=301, item_id="ITEMA", comp_id=cls.company_id, lock_unlock=True, type='1'
        )


    def setUp(self):
        self.client = Client()
        # Mock session data for tests
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.fin_year_id
        session.save()

        # Mock authenticated user for tests
        self.user = MagicMock()
        self.user.is_authenticated = True
        self.user.username = self.user_id
        self.client.force_login(self.user)

    def test_list_view_get(self):
        response = self.client.get(reverse('material_management:po_authorize_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_authorize/list.html')
        self.assertIn('form', response.context)
        # Should not contain po_masters directly as it's loaded via HTMX
        self.assertFalse(response.context['po_masters'].exists())

    def test_table_partial_view_get_initial(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('material_management:po_authorize_table'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_authorize/_po_table.html')
        # Check if the pending PO is in the context
        self.assertTrue(response.context['po_masters'].filter(id=1).exists())
        self.assertFalse(response.context['po_masters'].filter(id=2).exists()) # Authorized PO should not be listed

    def test_table_partial_view_get_filter_by_po_no(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('material_management:po_authorize_table'), {'search_field': '1', 'po_no': 'PO001'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['po_masters'].filter(po_no='PO001').exists())
        self.assertEqual(response.context['po_masters'].count(), 1)

    def test_table_partial_view_get_filter_by_supplier_name(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('material_management:po_authorize_table'), {'search_field': '0', 'supplier_name': 'Test Supplier A'}, **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['po_masters'].filter(supplier_id='SUP001').exists())
        self.assertEqual(response.context['po_masters'].count(), 1)

    @patch('material_management.models.ClsFunctions.get_curr_date', return_value='2023-03-01')
    @patch('material_management.models.ClsFunctions.get_curr_time', return_value='14:30:00')
    def test_authorize_action_view_post_success(self, mock_get_curr_time, mock_get_curr_date):
        po_initial_status = PoMaster.objects.using('legacy_db').get(id=1).authorize
        self.assertFalse(po_initial_status)

        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {'selected_pos[]': [1]}
        response = self.client.post(reverse('material_management:po_authorize_action'), data, **headers)
        
        self.assertEqual(response.status_code, 204) # No Content expected for HTMX
        self.assertIn('HX-Trigger', response)
        self.assertIn('refreshPoMasterList', response['HX-Trigger'])

        # Verify PO status updated in DB
        po_updated = PoMaster.objects.using('legacy_db').get(id=1)
        self.assertTrue(po_updated.authorize)
        self.assertEqual(po_updated.authorized_by, self.user_id)
        self.assertEqual(po_updated.authorize_date, date(2023, 3, 1))
        
        # Verify messages
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), '1 PO(s) authorized successfully.')
        self.assertEqual(messages[0].tags, 'success')


    def test_authorize_action_view_post_no_selection(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {} # No selected_pos[]
        response = self.client.post(reverse('material_management:po_authorize_action'), data, **headers)
        
        self.assertEqual(response.status_code, 204)
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'No records selected for authorization.')
        self.assertEqual(messages[0].tags, 'warning')

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('material_management:supplier_autocomplete'), {'q': 'Test Sup'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        json_response = response.json()
        self.assertIn('Test Supplier A [SUP001]', json_response)
        self.assertIn('Test Supplier B [SUP002]', json_response)
        self.assertLessEqual(len(json_response), 10) # Max 10 results

    def test_po_view_redirect_view(self):
        response = self.client.get(reverse('material_management:po_authorize_view', args=[1]))
        self.assertEqual(response.status_code, 200)
        self.assertIn('Redirecting to PO_PR_View_Print_Details.aspx', response.content.decode())

        response_auth_po = self.client.get(reverse('material_management:po_authorize_view', args=[2]))
        self.assertEqual(response_auth_po.status_code, 200)
        self.assertIn('Redirecting to PO_SPR_View_Print_Details.aspx', response_auth_po.content.decode())

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

The provided templates and views demonstrate the integration:

*   **HTMX for dynamic updates:**
    *   `po_authorize_list.html` uses `hx-get` on `po_masterTable-container` to load the table via `{% url 'material_management:po_authorize_table' %}` on `load` and `refreshPoMasterList` trigger.
    *   The search form uses `hx-get` to trigger `po_authorize_table` on `submit` and `change` of the dropdown, including `hx-include="#searchForm"` to send current form data.
    *   The "Authorized" button uses `hx-post` to `{% url 'material_management:po_authorize_action' %}` including `hx-include="#po_masterTable input[type=checkbox]:checked"` to send selected PO IDs.
    *   `HX-Trigger` headers (`refreshPoMasterList`, `showMessageEvent`) are used from Python views to signal frontend updates without full page reloads.
    *   Autocomplete is handled by `hx-get` on `keyup` to `supplier_autocomplete` endpoint, targeting a div for results.

*   **Alpine.js for UI state management:**
    *   The `search_field_val` variable in `list.html` manages the visibility of `txtSupplier` and `txtPONo` based on the dropdown selection using `x-data`, `x-show`, and `x-on:change`.
    *   A message display component uses Alpine.js `x-data`, `x-show`, and `@showMessageEvent.window` to show transient success/error/warning messages triggered by HTMX.
    *   Alpine.js is also used to control the visibility of the autocomplete results div based on input value and result count.

*   **DataTables for list views:**
    *   The `_po_table.html` partial includes a `script` block to initialize DataTables on the `#po_masterTable` element immediately after the table is loaded via HTMX. This ensures client-side search, sort, and pagination.
    *   CDN link for DataTables JavaScript is placed in `base.html` (assumed).

*   **Seamless interactions:**
    *   All user interactions (search, filter, authorize, view) are designed to avoid full page reloads, providing a more responsive user experience similar to a Single Page Application, but with minimal custom JavaScript.

---

### Final Notes

*   **Placeholder Replacement:** All placeholders like `[TABLE_NAME]`, `[MODEL_NAME]`, `[FIELD1]` have been replaced with concrete names derived from the ASP.NET code (`tblMM_PO_Master` -> `PoMaster`, `PONo` -> `po_no`, etc.).
*   **DRY Templates:** The use of `_po_table.html` as a partial template loaded via HTMX exemplifies the DRY principle. The main `list.html` focuses on overall page structure and HTMX containers, while the partial handles the dynamic table content.
*   **Fat Model, Thin View:** The complex authorization logic (`Auth_Click`) is entirely encapsulated within the `authorize_po` method of the `PoMaster` model. The `PoAuthorizeActionView` remains thin, primarily handling the HTTP request/response and delegating business logic to the model.
*   **Comprehensive Tests:** Unit tests cover model properties and methods (especially `authorize_po`). Integration tests cover all view functionalities, including HTMX interactions, form submissions, and data filtering.
*   **Legacy Database Integration:** The `managed = False` and `using('legacy_db')` approaches ensure Django interacts with the existing database schema without attempting migrations.
*   **Security:** `csrf_exempt` was used in `PoAuthorizeActionView` for simpler HTMX POST demonstrations. In a production environment, ensure proper CSRF token handling as per Django's best practices (e.g., including `{% csrf_token %}` in forms and ensuring HTMX includes it).
*   **Error Handling and User Feedback:** Django's `messages` framework is used for user feedback, and an Alpine.js component is set up to display these messages in a non-intrusive way for HTMX-triggered events.