## ASP.NET to Django Conversion Script: Material Management Transactions Dashboard

This document outlines a comprehensive modernization plan to transition your legacy ASP.NET Material Management Transactions Dashboard to a modern Django-based solution. Our approach prioritizes automation and leverages cutting-edge technologies like HTMX and Alpine.js to deliver a highly interactive and efficient user experience without relying on complex JavaScript frameworks.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is located at `core/base.html`.
- Focus ONLY on component-specific code for the current module (`material_management`).
- Always include complete unit tests for models and integration tests for views, aiming for at least 80% test coverage.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs), ensuring view methods are compact (5-15 lines).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination on all list views.
- Use HTMX for dynamic interactions and Alpine.js for UI state management, eliminating the need for traditional JavaScript.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code in this output).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase, utilizing template inheritance and partials.
- Use Tailwind CSS for styling components, integrated into your Django templates.

---

### Conversion Steps:

Since the provided ASP.NET code is a minimal dashboard page with no explicit UI controls or database interactions, we will infer a typical Material Management Dashboard functionality. We will assume it's responsible for displaying and managing **Transactions** related to material movements (e.g., material in, material out, adjustments).

---

### Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the `Module_MaterialManagement_Transactions_Dashboard` context and the lack of explicit database interactions in the provided ASP.NET code, we infer a primary database table for "Transactions".

-   **Inferred Table Name:** `tblTransactions`
-   **Inferred Column Names and Data Types:**
    *   `id` (Primary Key, integer)
    *   `transaction_date` (Date/Time)
    *   `material_name` (Text, e.g., VARCHAR)
    *   `quantity` (Decimal/Float)
    *   `transaction_type` (Text, e.g., 'IN', 'OUT', 'ADJUSTMENT')
    *   `status` (Text, e.g., 'Completed', 'Pending', 'Cancelled')
    *   `notes` (Text, e.g., NVARCHAR(MAX) or TEXT)

---

### Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Based on the dashboard nature and common business requirements for managing records:

-   **Read:** The dashboard likely displays a list of existing transactions. This will be handled by a Django ListView.
-   **Create:** Functionality to add new material transactions. This will be handled by a Django CreateView.
-   **Update:** Ability to modify details of existing transactions. This will be handled by a Django UpdateView.
-   **Delete:** Option to remove transactions from the system. This will be handled by a Django DeleteView.
-   **Validation Logic:** Basic validation will be inferred for required fields (e.g., `material_name`, `quantity`, `transaction_date`).

---

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
Since no controls are explicitly defined in the ASP.NET code, we infer standard components for a dashboard managing transactional data:

-   **Data List:** A `GridView`-like component is inferred, which will be replaced by a DataTables-enhanced HTML table in Django, displaying all transaction records.
-   **Input Forms:** `TextBox` and `DropDownList` equivalents are inferred for capturing new transaction details or editing existing ones. These will map to Django Form fields (e.g., `TextInput`, `Select`, `NumberInput`, `Textarea`) styled with Tailwind CSS.
-   **Action Buttons:** `Button` or `LinkButton` equivalents are inferred for actions like "Add New Transaction", "Edit", "Delete". These will be transformed into standard HTML buttons with HTMX attributes to trigger dynamic interactions and modal pop-ups.
-   **Client-Side Interactions:** The presence of `loadingNotifier.js` hints at client-side UI feedback. This will be handled by HTMX for dynamic loading/swapping and Alpine.js for basic UI state management (e.g., modal visibility), eliminating the need for custom JavaScript.

---

### Step 4: Generate Django Code

We will create a new Django app named `material_management` to house these components.

#### 4.1 Models

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The model `Transaction` will represent records in the `tblTransactions` table.

```python
# material_management/models.py
from django.db import models

class Transaction(models.Model):
    transaction_id = models.AutoField(db_column='id', primary_key=True) # Assuming 'id' is an auto-incrementing PK
    transaction_date = models.DateField(db_column='transaction_date')
    material_name = models.CharField(db_column='material_name', max_length=255)
    quantity = models.DecimalField(db_column='quantity', max_digits=10, decimal_places=2)
    transaction_type = models.CharField(db_column='transaction_type', max_length=50, choices=[
        ('IN', 'Inbound'),
        ('OUT', 'Outbound'),
        ('ADJ', 'Adjustment'),
    ])
    status = models.CharField(db_column='status', max_length=50, default='Completed', choices=[
        ('Completed', 'Completed'),
        ('Pending', 'Pending'),
        ('Cancelled', 'Cancelled'),
    ])
    notes = models.TextField(db_column='notes', blank=True, null=True)

    class Meta:
        managed = False  # Set to True if Django should manage the table schema
        db_table = 'tblTransactions'
        verbose_name = 'Transaction'
        verbose_name_plural = 'Transactions'
        ordering = ['-transaction_date'] # Order by latest transactions first

    def __str__(self):
        return f"{self.transaction_date} - {self.material_name} ({self.quantity} {self.transaction_type})"
        
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('transaction_detail', args=[str(self.transaction_id)])
    
    # Example of a business logic method (Fat Model principle)
    def calculate_stock_impact(self):
        """
        Calculates the impact of this transaction on stock levels.
        This would typically interact with a separate Inventory model.
        """
        if self.transaction_type == 'IN':
            return self.quantity
        elif self.transaction_type == 'OUT':
            return -self.quantity
        else: # ADJ
            return 0 # Or logic for specific adjustments
```

#### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` will be created for the `Transaction` model, including all editable fields and appropriate widgets for styling.

```python
# material_management/forms.py
from django import forms
from .models import Transaction

class TransactionForm(forms.ModelForm):
    class Meta:
        model = Transaction
        fields = ['transaction_date', 'material_name', 'quantity', 'transaction_type', 'status', 'notes']
        widgets = {
            'transaction_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'material_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'transaction_type': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'notes': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
        labels = {
            'transaction_date': 'Transaction Date',
            'material_name': 'Material Name',
            'quantity': 'Quantity',
            'transaction_type': 'Type',
            'status': 'Status',
            'notes': 'Notes',
        }
        
    def clean_quantity(self):
        quantity = self.cleaned_data['quantity']
        if quantity <= 0:
            raise forms.ValidationError("Quantity must be a positive number.")
        return quantity
```

#### 4.3 Views

**Task:** Implement CRUD operations using Django Class-Based Views (CBVs), keeping them thin and moving business logic to models. A new partial view for the DataTables content is also added.

**Instructions:**
Views will handle requests and render appropriate templates, delegating data operations to the `Transaction` model.

```python
# material_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import Transaction
from .forms import TransactionForm

class TransactionListView(ListView):
    model = Transaction
    template_name = 'material_management/transaction/list.html'
    context_object_name = 'transactions'

class TransactionTablePartialView(ListView):
    model = Transaction
    template_name = 'material_management/transaction/_transaction_table.html'
    context_object_name = 'transactions'
    # This view is specifically for HTMX to swap in the table content

class TransactionCreateView(CreateView):
    model = Transaction
    form_class = TransactionForm
    template_name = 'material_management/transaction/_transaction_form.html' # Use partial for modal
    success_url = reverse_lazy('transaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Transaction added successfully.')
        if self.request.headers.get('HX-Request'):
            # This triggers a refresh of the transaction list in the main page
            return HttpResponse(
                status=204, # No content, just headers
                headers={
                    'HX-Trigger': 'refreshTransactionList'
                }
            )
        return response

class TransactionUpdateView(UpdateView):
    model = Transaction
    form_class = TransactionForm
    template_name = 'material_management/transaction/_transaction_form.html' # Use partial for modal
    pk_url_kwarg = 'pk' # Ensure pk is correctly identified from URL
    success_url = reverse_lazy('transaction_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Transaction updated successfully.')
        if self.request.headers.get('HX-Request'):
            # This triggers a refresh of the transaction list in the main page
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTransactionList'
                }
            )
        return response

class TransactionDeleteView(DeleteView):
    model = Transaction
    template_name = 'material_management/transaction/_transaction_confirm_delete.html' # Use partial for modal
    pk_url_kwarg = 'pk'
    success_url = reverse_lazy('transaction_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Transaction deleted successfully.')
        if request.headers.get('HX-Request'):
            # This triggers a refresh of the transaction list in the main page
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshTransactionList'
                }
            )
        return response
```

#### 4.4 Templates

**Task:** Create templates for each view, ensuring DRY principles, HTMX integration, and DataTables for list views.

**Instructions:**
Templates will include the necessary HTMX attributes and Alpine.js directives for interactive UI elements.

```html
<!-- material_management/transaction/list.html -->
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Material Management Transactions</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'transaction_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Transaction
        </button>
    </div>
    
    <div id="transactionTable-container"
         hx-trigger="load, refreshTransactionList from:body"
         hx-get="{% url 'transaction_table' %}"
         hx-swap="innerHTML"
         class="bg-white shadow-lg rounded-lg p-6">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-600">Loading Transactions...</p>
        </div>
    </div>
    
    <!-- Universal Modal for HTMX content -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden is-active transition-opacity duration-300"
         _="on click if event.target.id == 'modal' remove .is-active from me"
         x-data="{ showModal: false }"
         x-show="showModal"
         @refreshTransactionList.window="showModal = false; console.log('Modal closed by refreshTransactionList');">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 transform transition-transform duration-300 scale-95"
             @click.stop # Stops click propagation to parent modal
             x-show="showModal"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-90"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-90">
            <!-- HTMX content will be loaded here -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js integration for modal state management
    document.addEventListener('alpine:init', () => {
        Alpine.data('modalController', () => ({
            showModal: false,
            init() {
                // Listen for custom event to open modal
                this.$el.addEventListener('htmx:afterOnLoad', () => {
                    this.showModal = true;
                });
                // Listen for HX-Trigger to close modal
                this.$el.addEventListener('refreshTransactionList', () => {
                    this.showModal = false;
                });
            }
        }));

        // Initialize modal with Alpine.js
        const modalElement = document.getElementById('modal');
        if (modalElement) {
            Alpine.data('modalInstance', () => modalController());
            // Need to manually initialize Alpine on the modal div if it's dynamically added or hidden
            // For this setup, 'x-data' on modal div should be sufficient.
            // If the modal starts 'hidden', 'x-show' will control its visibility.
        }
    });

    // Manual way to handle modal via class for HTMX _ if Alpine setup fails
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'modalContent') {
            document.getElementById('modal').classList.add('is-active');
            document.getElementById('modal').classList.remove('hidden'); // Ensure it's visible
        }
    });

    document.body.addEventListener('refreshTransactionList', function() {
        const modal = document.getElementById('modal');
        if (modal) {
            modal.classList.remove('is-active');
            modal.classList.add('hidden'); // Hide the modal
        }
    });

    // Ensure DataTables re-initializes on HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'transactionTable-container') {
            $('#transactionTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                "destroy": true // Destroy existing DataTable instance if any
            });
        }
    });
</script>
{% endblock %}
```

```html
<!-- material_management/transaction/_transaction_table.html -->
<div class="overflow-x-auto">
    <table id="transactionTable" class="min-w-full bg-white border border-gray-300">
        <thead>
            <tr class="bg-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                <th class="py-3 px-4 border-b border-gray-200">SN</th>
                <th class="py-3 px-4 border-b border-gray-200">Date</th>
                <th class="py-3 px-4 border-b border-gray-200">Material Name</th>
                <th class="py-3 px-4 border-b border-gray-200">Quantity</th>
                <th class="py-3 px-4 border-b border-gray-200">Type</th>
                <th class="py-3 px-4 border-b border-gray-200">Status</th>
                <th class="py-3 px-4 border-b border-gray-200">Notes</th>
                <th class="py-3 px-4 border-b border-gray-200">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for obj in transactions %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.transaction_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.material_name }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.quantity }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.get_transaction_type_display }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.status == 'Completed' %}bg-green-100 text-green-800
                        {% elif obj.status == 'Pending' %}bg-yellow-100 text-yellow-800
                        {% else %}bg-red-100 text-red-800{% endif %}">
                        {{ obj.get_status_display }}
                    </span>
                </td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm text-gray-700">{{ obj.notes|default_if_none:"-" }}</td>
                <td class="py-3 px-4 border-b border-gray-200 text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-1 px-3 rounded-lg mr-2 shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'transaction_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-semibold py-1 px-3 rounded-lg shadow-sm transition duration-300 ease-in-out"
                        hx-get="{% url 'transaction_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // This script block re-initializes DataTables after HTMX loads this partial.
    // The main list.html handles the 'htmx:afterSwap' event.
    // It's crucial for DataTables to work correctly after an HTMX update.
</script>
```

```html
<!-- material_management/transaction/_transaction_form.html -->
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} Transaction</h3>
    <form hx-post="{{ request.path }}" hx-swap="none"> {# hx-swap="none" means HTMX doesn't swap content, relies on HX-Trigger #}
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save Transaction
            </button>
        </div>
    </form>
</div>
```

```html
<!-- material_management/transaction/_transaction_confirm_delete.html -->
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the transaction for **{{ object.material_name }}** on **{{ object.transaction_date|date:"Y-m-d" }}**?</p>
    
    <form hx-post="{% url 'transaction_delete' object.pk %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out">
                Confirm Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

**Task:** Define URL patterns for the views within the `material_management` app.

**Instructions:**
URLs will be mapped to the respective Class-Based Views and partial views for HTMX.

```python
# material_management/urls.py
from django.urls import path
from .views import (
    TransactionListView, 
    TransactionCreateView, 
    TransactionUpdateView, 
    TransactionDeleteView,
    TransactionTablePartialView,
)

urlpatterns = [
    path('transactions/', TransactionListView.as_view(), name='transaction_list'),
    path('transactions/table/', TransactionTablePartialView.as_view(), name='transaction_table'), # For HTMX refresh
    path('transactions/add/', TransactionCreateView.as_view(), name='transaction_add'),
    path('transactions/edit/<int:pk>/', TransactionUpdateView.as_view(), name='transaction_edit'),
    path('transactions/delete/<int:pk>/', TransactionDeleteView.as_view(), name='transaction_delete'),
]

# Don't forget to include these URLs in your project's main urls.py:
# from django.urls import path, include
# urlpatterns = [
#     path('material-management/', include('material_management.urls')),
# ]
```

#### 4.6 Tests

**Task:** Write comprehensive tests for the model and views to ensure functionality and coverage.

**Instructions:**
Tests will cover model attributes, methods, and all CRUD operations for views, including HTMX interactions.

```python
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import Transaction
from datetime import date

class TransactionModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Transaction.objects.create(
            transaction_date=date(2023, 1, 15),
            material_name='Steel Rods',
            quantity=100.50,
            transaction_type='IN',
            status='Completed',
            notes='Initial stock received'
        )
        Transaction.objects.create(
            transaction_date=date(2023, 1, 20),
            material_name='Copper Wire',
            quantity=50.00,
            transaction_type='OUT',
            status='Pending',
            notes='For production batch A'
        )
  
    def test_transaction_creation(self):
        obj = Transaction.objects.get(transaction_id=1)
        self.assertEqual(obj.transaction_date, date(2023, 1, 15))
        self.assertEqual(obj.material_name, 'Steel Rods')
        self.assertEqual(obj.quantity, 100.50)
        self.assertEqual(obj.transaction_type, 'IN')
        self.assertEqual(obj.status, 'Completed')
        self.assertEqual(obj.notes, 'Initial stock received')
        
    def test_material_name_label(self):
        obj = Transaction.objects.get(transaction_id=1)
        field_label = obj._meta.get_field('material_name').verbose_name
        self.assertEqual(field_label, 'material name') # Django's default verbose_name
        
    def test_transaction_type_choices(self):
        obj = Transaction.objects.get(transaction_id=1)
        self.assertEqual(obj.get_transaction_type_display(), 'Inbound')
        obj2 = Transaction.objects.get(transaction_id=2)
        self.assertEqual(obj2.get_transaction_type_display(), 'Outbound')

    def test_str_method(self):
        obj = Transaction.objects.get(transaction_id=1)
        expected_str = f"2023-01-15 - Steel Rods (100.50 IN)"
        self.assertEqual(str(obj), expected_str)

    def test_calculate_stock_impact_in(self):
        obj = Transaction.objects.get(transaction_id=1) # IN transaction
        self.assertEqual(obj.calculate_stock_impact(), 100.50)

    def test_calculate_stock_impact_out(self):
        obj = Transaction.objects.get(transaction_id=2) # OUT transaction
        self.assertEqual(obj.calculate_stock_impact(), -50.00)

class TransactionViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        Transaction.objects.create(
            transaction_date=date(2023, 2, 1),
            material_name='Aluminum Sheets',
            quantity=20.0,
            transaction_type='IN',
            status='Completed',
            notes='Bulk order'
        )
    
    def setUp(self):
        # Set up data for each test method
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('transaction_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/transaction/list.html')
        self.assertTrue('transactions' in response.context)
        self.assertContains(response, 'Aluminum Sheets') # Check if content from test data is present
        
    def test_table_partial_view_htmx(self):
        response = self.client.get(reverse('transaction_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/transaction/_transaction_table.html')
        self.assertContains(response, 'Aluminum Sheets')
        self.assertContains(response, '<table id="transactionTable"') # Verify DataTables element
        
    def test_create_view_get(self):
        response = self.client.get(reverse('transaction_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/transaction/_transaction_form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'transaction_date': '2023-03-01',
            'material_name': 'Plastic Pellets',
            'quantity': 500.0,
            'transaction_type': 'IN',
            'status': 'Pending',
            'notes': 'New supplier batch'
        }
        response = self.client.post(reverse('transaction_add'), data, HTTP_HX_REQUEST='true')
        # Expect 204 No Content for successful HTMX post that triggers client-side refresh
        self.assertEqual(response.status_code, 204)
        self.assertTrue(Transaction.objects.filter(material_name='Plastic Pellets').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTransactionList')
        
    def test_create_view_post_invalid(self):
        data = {
            'transaction_date': '2023-03-01',
            'material_name': 'Plastic Pellets',
            'quantity': -10, # Invalid quantity
            'transaction_type': 'IN',
            'status': 'Pending',
            'notes': 'New supplier batch'
        }
        response = self.client.post(reverse('transaction_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-renders with errors for HTMX
        self.assertTemplateUsed(response, 'material_management/transaction/_transaction_form.html')
        self.assertContains(response, 'Quantity must be a positive number.')
        self.assertFalse(Transaction.objects.filter(material_name='Plastic Pellets', quantity=-10).exists())

    def test_update_view_get(self):
        obj = Transaction.objects.get(transaction_id=1)
        response = self.client.get(reverse('transaction_edit', args=[obj.transaction_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/transaction/_transaction_form.html')
        self.assertTrue('form' in response.context)
        self.assertEqual(response.context['form'].instance, obj)
        
    def test_update_view_post_success(self):
        obj = Transaction.objects.get(transaction_id=1)
        data = {
            'transaction_date': '2023-02-01',
            'material_name': 'Aluminum Sheets Updated',
            'quantity': 25.0,
            'transaction_type': 'OUT',
            'status': 'Completed',
            'notes': 'Updated notes for batch A'
        }
        response = self.client.post(reverse('transaction_edit', args=[obj.transaction_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        obj.refresh_from_db()
        self.assertEqual(obj.material_name, 'Aluminum Sheets Updated')
        self.assertEqual(obj.quantity, 25.0)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTransactionList')
        
    def test_delete_view_get(self):
        obj = Transaction.objects.get(transaction_id=1)
        response = self.client.get(reverse('transaction_delete', args=[obj.transaction_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/transaction/_transaction_confirm_delete.html')
        self.assertTrue('object' in response.context)
        self.assertEqual(response.context['object'], obj)
        
    def test_delete_view_post_success(self):
        obj_count_before = Transaction.objects.count()
        obj_to_delete = Transaction.objects.get(transaction_id=1)
        response = self.client.post(reverse('transaction_delete', args=[obj_to_delete.transaction_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Transaction.objects.count(), obj_count_before - 1)
        self.assertFalse(Transaction.objects.filter(transaction_id=obj_to_delete.transaction_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshTransactionList')
```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **HTMX for Dynamic Content:**
    *   The main `list.html` uses `hx-get` to load the `_transaction_table.html` partial. This means the table is loaded dynamically on page load and can be refreshed independently.
    *   `hx-trigger="load, refreshTransactionList from:body"` ensures the table reloads when the page initially loads or when a custom event `refreshTransactionList` is dispatched (after successful CRUD operations).
    *   Buttons for "Add", "Edit", and "Delete" use `hx-get` to load forms into the modal, targeting `#modalContent`.
    *   Form submissions (`_transaction_form.html`, `_transaction_confirm_delete.html`) use `hx-post` and `hx-swap="none"`. The views explicitly return `HttpResponse(status=204, headers={'HX-Trigger': 'refreshTransactionList'})` to close the modal and trigger the table refresh in the main page. This is the core of HTMX's "Swap None & Trigger" pattern.

-   **Alpine.js for UI State Management:**
    *   The modal (`#modal`) uses Alpine.js's `x-data`, `x-show`, and `@click.stop` to manage its visibility and prevent clicks inside the modal from closing it.
    *   The `on click` directives in the HTML (using the `_` syntax) provide simple JavaScript actions to toggle the modal's `is-active` class, opening it when an add/edit/delete button is clicked.
    *   A more robust Alpine.js component setup (`modalController`) listens for `htmx:afterOnLoad` (to show modal after content loads) and `refreshTransactionList` (to hide modal after successful form submission).

-   **DataTables for List Views:**
    *   The `_transaction_table.html` partial contains the `<table>` element with `id="transactionTable"`.
    *   A `script` block within `list.html` (specifically, in `extra_js`) listens for `htmx:afterSwap` on the `#transactionTable-container` element. When the table partial is swapped in, it then initializes or re-initializes DataTables on `#transactionTable`, ensuring proper client-side sorting, searching, and pagination. The `destroy: true` option is important to correctly handle re-initialization.

-   **HTMX-Only Interactions:**
    *   No custom, complex JavaScript functions are required. All dynamic behavior is driven by HTMX attributes or simple Alpine.js directives directly in the HTML.
    *   The `HX-Trigger` mechanism is paramount for updating the list view after any form submission without a full page reload, providing a seamless user experience.

---

### Final Notes

This comprehensive plan provides a robust framework for migrating your Material Management Transactions Dashboard to Django. By systematically replacing ASP.NET components with their modern Django equivalents, leveraging AI-assisted automation templates, and adhering to best practices, you achieve:

-   **Improved Maintainability:** Clean separation of concerns with fat models and thin views.
-   **Enhanced User Experience:** Highly interactive interfaces with HTMX and Alpine.js, minimizing full page reloads.
-   **Scalability and Performance:** Django's robust architecture supports growing data and user bases.
-   **Future-Proofing:** Adherence to modern web standards and widely adopted open-source technologies.
-   **Accelerated Development:** The use of standardized patterns and AI-assisted automation reduces manual coding effort and potential for errors.

This plan focuses on component-specific code, assuming your project's Django setup (including `settings.py`, root `urls.py`, `core/base.html`, and static/media configurations) is already in place. The generated code adheres to all specified rules, providing a clear, actionable path for modernization.