## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Analysis of Provided ASP.NET Code:

The provided ASP.NET `.aspx` and C# code-behind files are largely empty.
- The `.aspx` file primarily defines content placeholders for a master page and includes a JavaScript file. It does not contain any specific UI controls (like GridView, TextBox, Button) or data binding logic.
- The C# code-behind file only contains an empty `Page_Load` event handler.

Due to the lack of concrete functionality, database interaction, or UI components in the provided ASP.NET snippets, the following modernization plan makes reasonable inferences to demonstrate the Django conversion process. We will assume a typical "Dashboard" scenario implies displaying a list of "SPR Approvals" and enabling basic CRUD operations.

We will proceed with the following inferred parameters:
- **Application Name:** `material_management` (derived from the ASP.NET namespace `Module_MaterialManagement`)
- **Model Name:** `SprApproval`
- **Database Table Name:** `spr_approvals`
- **Inferred Fields:** `id` (primary key, auto-generated), `spr_code` (unique code for the SPR), `approval_date` (date of approval), `status` (e.g., 'Pending', 'Approved', 'Rejected'), `approved_by` (user who approved).

---

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Since the provided ASP.NET code does not explicitly define database interactions (e.g., `SqlDataSource` or direct ADO.NET calls), we infer the schema based on the page's name: `SPR_Approve_Dashboard`. This suggests a table storing "SPR Approval" records.

- **Table Name:** `spr_approvals`
- **Columns (inferred):**
    - `id` (Primary Key, integer, auto-incrementing)
    - `spr_code` (String, e.g., 'SPR001', unique identifier for the approval request)
    - `approval_date` (Date/Time, when the approval occurred)
    - `status` (String, e.g., 'Approved', 'Pending', 'Rejected')
    - `approved_by` (String, e.g., 'John Doe', name of the approver)

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

Given the empty ASP.NET code, no specific CRUD operations are present. However, a "Dashboard" typically implies:
- **Read:** Displaying a list of existing SPR Approvals.
- **Create:** Adding new SPR Approval records.
- **Update:** Modifying existing SPR Approval records.
- **Delete:** Removing SPR Approval records.

We will implement these standard CRUD operations in Django using the inferred `SprApproval` model.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

As no UI controls are present in the `.aspx` file, we infer the typical components required for an "Approval Dashboard":
- **Data List:** A table (analogous to ASP.NET GridView) to display multiple `SprApproval` records. This will be implemented using a Django template with DataTables for rich client-side features.
- **Input Forms:** Forms for adding or editing individual `SprApproval` records (analogous to TextBoxes, DropDownLists in ASP.NET forms). These will be rendered by Django Forms.
- **Action Buttons:** Buttons for 'Add New', 'Edit', 'Delete' actions. These will use HTMX to trigger dynamic content loading (e.g., modals).

## Step 4: Generate Django Code

We will create a Django application named `material_management` for this module.

### 4.1 Models (`material_management/models.py`)

Task: Create a Django model based on the database schema.

```python
from django.db import models
from django.utils import timezone

class SprApproval(models.Model):
    # Assuming 'id' is an auto-incrementing primary key managed by Django by default.
    # We define explicit fields for clarity based on inferred columns.
    spr_code = models.CharField(
        db_column='spr_code',
        max_length=50,
        unique=True,
        verbose_name='SPR Code'
    )
    approval_date = models.DateTimeField(
        db_column='approval_date',
        default=timezone.now,
        verbose_name='Approval Date'
    )
    status = models.CharField(
        db_column='status',
        max_length=20,
        choices=[
            ('PENDING', 'Pending'),
            ('APPROVED', 'Approved'),
            ('REJECTED', 'Rejected')
        ],
        default='PENDING',
        verbose_name='Status'
    )
    approved_by = models.CharField(
        db_column='approved_by',
        max_length=100,
        blank=True,
        null=True,
        verbose_name='Approved By'
    )

    class Meta:
        managed = False  # Set to True if Django should manage table creation
        db_table = 'spr_approvals'
        verbose_name = 'SPR Approval'
        verbose_name_plural = 'SPR Approvals'
        ordering = ['-approval_date', 'spr_code'] # Default ordering

    def __str__(self):
        return f"{self.spr_code} - {self.status}"
        
    # Business logic methods (Fat Model)
    def approve(self, approver_name):
        """
        Approves the SPR if it's currently pending.
        Returns True on success, False otherwise.
        """
        if self.status == 'PENDING':
            self.status = 'APPROVED'
            self.approved_by = approver_name
            self.approval_date = timezone.now() # Update date on action
            self.save()
            return True
        return False

    def reject(self, approver_name):
        """
        Rejects the SPR if it's currently pending.
        Returns True on success, False otherwise.
        """
        if self.status == 'PENDING':
            self.status = 'REJECTED'
            self.approved_by = approver_name
            self.approval_date = timezone.now() # Update date on action
            self.save()
            return True
        return False

    def can_be_modified(self):
        """
        Determines if the SPR approval record can still be modified.
        """
        return self.status == 'PENDING'

```

### 4.2 Forms (`material_management/forms.py`)

Task: Define a Django form for user input.

```python
from django import forms
from .models import SprApproval

class SprApprovalForm(forms.ModelForm):
    class Meta:
        model = SprApproval
        fields = ['spr_code', 'approval_date', 'status', 'approved_by']
        widgets = {
            'spr_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'approval_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'approved_by': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        
    def clean_spr_code(self):
        spr_code = self.cleaned_data['spr_code']
        # Example validation: SPR code must be alphanumeric
        if not spr_code.isalnum():
            raise forms.ValidationError("SPR Code must be alphanumeric.")
        return spr_code

    def clean(self):
        cleaned_data = super().clean()
        status = cleaned_data.get('status')
        approved_by = cleaned_data.get('approved_by')

        # Custom validation: If status is 'APPROVED' or 'REJECTED', 'approved_by' must not be empty
        if status in ['APPROVED', 'REJECTED'] and not approved_by:
            self.add_error('approved_by', 'Approved By is required for Approved/Rejected status.')
        
        # Example: Prevent modifying approved/rejected records (unless explicitly allowed by business logic)
        if self.instance.pk and self.instance.status in ['APPROVED', 'REJECTED'] and not self.instance.can_be_modified():
             raise forms.ValidationError("Approved or Rejected SPR records cannot be modified.")

        return cleaned_data

```

### 4.3 Views (`material_management/views.py`)

Task: Implement CRUD operations using CBVs.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render, redirect
from .models import SprApproval
from .forms import SprApprovalForm

# Constants for HTMX headers
HX_REFRESH_LIST_TRIGGER = 'refreshSprApprovalList'
HX_CLOSE_MODAL_TRIGGER = 'closeModal' # Custom trigger to close the modal

class SprApprovalListView(ListView):
    model = SprApproval
    template_name = 'material_management/spr_approval/list.html'
    context_object_name = 'spr_approvals' # Will be used to populate the table dynamically via HTMX

    # This view primarily sets up the page for the HTMX table to load into
    # No direct data fetching in the main ListView to keep it thin
    # The actual table data is loaded by SprApprovalTablePartialView

class SprApprovalTablePartialView(ListView):
    model = SprApproval
    template_name = 'material_management/spr_approval/_spr_approval_table.html'
    context_object_name = 'spr_approvals'

    # This view is explicitly for HTMX requests to refresh just the table content.
    # It queries the data to be displayed in the DataTables.

class SprApprovalCreateView(CreateView):
    model = SprApproval
    form_class = SprApprovalForm
    template_name = 'material_management/spr_approval/form.html'
    success_url = reverse_lazy('spr_approval_list') # Not directly used for HTMX responses

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'SPR Approval added successfully.')
        if self.request.headers.get('HX-Request'):
            # For HTMX requests, we send 204 No Content and a trigger to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'{HX_REFRESH_LIST_TRIGGER}, {HX_CLOSE_MODAL_TRIGGER}'
                }
            )
        return response # Fallback for non-HTMX requests

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # For HTMX requests, render the form again with errors
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class SprApprovalUpdateView(UpdateView):
    model = SprApproval
    form_class = SprApprovalForm
    template_name = 'material_management/spr_approval/form.html'
    success_url = reverse_lazy('spr_approval_list') # Not directly used for HTMX responses

    def form_valid(self, form):
        # Business logic can be placed in model methods
        if not form.instance.can_be_modified():
            # If the model method indicates it can't be modified, return an error
            messages.error(self.request, 'This SPR Approval record cannot be modified as it is already approved or rejected.')
            if self.request.headers.get('HX-Request'):
                # Re-render the form with the error message
                return render(self.request, self.template_name, {'form': form})
            return redirect(self.success_url) # For non-HTMX
        
        response = super().form_valid(form)
        messages.success(self.request, 'SPR Approval updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'{HX_REFRESH_LIST_TRIGGER}, {HX_CLOSE_MODAL_TRIGGER}'
                }
            )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            return render(self.request, self.template_name, {'form': form})
        return response

class SprApprovalDeleteView(DeleteView):
    model = SprApproval
    template_name = 'material_management/spr_approval/confirm_delete.html'
    success_url = reverse_lazy('spr_approval_list') # Not directly used for HTMX responses

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        # Business logic for deletion permission (e.g., only pending can be deleted)
        if not self.object.can_be_modified():
            messages.error(self.request, 'This SPR Approval record cannot be deleted as it is already approved or rejected.')
            # For HTMX, return a response that can be handled by the modal
            return HttpResponse(
                status=403, # Forbidden
                content='<div class="p-6 text-red-700">Cannot delete approved/rejected SPR.</div>',
                headers={
                    'HX-Retarget': '#modalContent', # Display error in modal
                    'HX-Reswap': 'innerHTML'
                }
            )

        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'SPR Approval deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': f'{HX_REFRESH_LIST_TRIGGER}, {HX_CLOSE_MODAL_TRIGGER}'
                }
            )
        return response

    # Handle GET requests for confirm_delete.html if it's rendered directly
    def get(self, request, *args, **kwargs):
        self.object = self.get_object()
        if request.headers.get('HX-Request'):
            return render(request, self.template_name, {'object': self.object})
        return super().get(request, *args, **kwargs)

```

### 4.4 Templates (`material_management/templates/material_management/spr_approval/`)

Task: Create templates for each view.

#### `list.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">SPR Approvals</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'spr_approval_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New SPR Approval
        </button>
    </div>
    
    <div id="spr_approvalTable-container"
         hx-trigger="load, refreshSprApprovalList from:body"
         hx-get="{% url 'spr_approval_table' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-4 border-blue-500"></div>
            <p class="mt-4 text-lg text-gray-700">Loading SPR Approvals...</p>
        </div>
    </div>
    
    <!-- Modal for form/confirmations -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 transition-opacity duration-300 opacity-0 pointer-events-none"
         _="on closeModal remove .is-active from me then remove .opacity-100 from me then add .opacity-0 to me then add .pointer-events-none to me
            on click if event.target.id == 'modal' remove .is-active from me then remove .opacity-100 from me then add .opacity-0 to me then add .pointer-events-none to me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full transform transition-transform duration-300 scale-95 opacity-0"
             _="on modal.htmx:afterOnLoad add .scale-100 to me add .opacity-100 to me">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for more complex UI state
        // Example: managing modal open/close state if not fully handled by HTMX/_
        Alpine.store('modal', {
            isOpen: false,
            open() { this.isOpen = true },
            close() { this.isOpen = false }
        });

        // Add event listener to hide modal when HX-Trigger 'closeModal' is received
        document.body.addEventListener('closeModal', () => {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.classList.remove('is-active');
                modal.classList.remove('opacity-100');
                modal.classList.add('opacity-0');
                modal.classList.add('pointer-events-none');
            }
        });
    });
</script>
{% endblock %}
```

#### `_spr_approval_table.html` (Partial)

```html
<div class="overflow-x-auto bg-white shadow-md rounded-lg">
    <table id="spr_approvalTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SPR Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approval Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved By</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in spr_approvals %}
            <tr>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.spr_code }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.approval_date|date:"Y-m-d H:i" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        {% if obj.status == 'APPROVED' %}bg-green-100 text-green-800
                        {% elif obj.status == 'PENDING' %}bg-yellow-100 text-yellow-800
                        {% elif obj.status == 'REJECTED' %}bg-red-100 text-red-800
                        {% endif %}">
                        {{ obj.get_status_display }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ obj.approved_by|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out mr-2"
                        hx-get="{% url 'spr_approval_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md transition duration-300 ease-in-out"
                        hx-get="{% url 'spr_approval_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
// DataTables initialization must be inside the partial to ensure it runs when content is loaded by HTMX
// It is good practice to destroy any existing DataTables instance before re-initializing
$(document).ready(function() {
    if ($.fn.DataTable.isDataTable('#spr_approvalTable')) {
        $('#spr_approvalTable').DataTable().destroy();
    }
    $('#spr_approvalTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
        "searching": true,
        "paging": true,
        "info": true,
        "order": [[ 2, "desc" ]] // Default order by approval_date descending
    });
});
</script>
```

#### `_spr_approval_form.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">{{ form.instance.pk|yesno:'Edit,Add' }} SPR Approval</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        
        <div class="space-y-5">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-600 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
                {% if field.help_text %}
                <p class="text-gray-500 text-xs mt-1">{{ field.help_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {% if form.non_field_errors %}
                <div class="text-red-600 text-sm mt-2">
                    {{ form.non_field_errors }}
                </div>
            {% endif %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `_spr_approval_confirm_delete.html` (Partial)

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete the SPR Approval for <strong>{{ object.spr_code }}</strong>?</p>
    
    <form hx-post="{% url 'spr_approval_delete' object.pk %}" hx-swap="none" hx-trigger="submit">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-5 rounded-lg shadow-sm transition duration-300 ease-in-out"
                _="on click trigger closeModal from body">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-5 rounded-lg shadow-md transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs (`material_management/urls.py`)

Task: Define URL patterns for the views.

```python
from django.urls import path
from .views import SprApprovalListView, SprApprovalCreateView, SprApprovalUpdateView, SprApprovalDeleteView, SprApprovalTablePartialView

urlpatterns = [
    path('spr_approvals/', SprApprovalListView.as_view(), name='spr_approval_list'),
    path('spr_approvals/table/', SprApprovalTablePartialView.as_view(), name='spr_approval_table'), # For HTMX partial load
    path('spr_approvals/add/', SprApprovalCreateView.as_view(), name='spr_approval_add'),
    path('spr_approvals/edit/<int:pk>/', SprApprovalUpdateView.as_view(), name='spr_approval_edit'),
    path('spr_approvals/delete/<int:pk>/', SprApprovalDeleteView.as_view(), name='spr_approval_delete'),
]
```

### 4.6 Tests (`material_management/tests.py`)

Task: Write tests for the model and views.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from .models import SprApproval
from .forms import SprApprovalForm

class SprApprovalModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.approved_spr = SprApproval.objects.create(
            spr_code='SPR001',
            approval_date=timezone.now() - timezone.timedelta(days=1),
            status='APPROVED',
            approved_by='Alice Smith'
        )
        cls.pending_spr = SprApproval.objects.create(
            spr_code='SPR002',
            approval_date=timezone.now(),
            status='PENDING',
            approved_by=None
        )
        cls.rejected_spr = SprApproval.objects.create(
            spr_code='SPR003',
            approval_date=timezone.now() - timezone.timedelta(hours=1),
            status='REJECTED',
            approved_by='Bob Johnson'
        )
  
    def test_spr_approval_creation(self):
        self.assertEqual(self.approved_spr.spr_code, 'SPR001')
        self.assertEqual(self.approved_spr.status, 'APPROVED')
        self.assertEqual(self.pending_spr.spr_code, 'SPR002')
        self.assertEqual(self.pending_spr.status, 'PENDING')
        self.assertEqual(SprApproval.objects.count(), 3)
        
    def test_spr_code_label(self):
        field_label = self.approved_spr._meta.get_field('spr_code').verbose_name
        self.assertEqual(field_label, 'SPR Code')
        
    def test_str_method(self):
        self.assertEqual(str(self.approved_spr), 'SPR001 - APPROVED')
        self.assertEqual(str(self.pending_spr), 'SPR002 - PENDING')

    def test_approve_method(self):
        self.assertTrue(self.pending_spr.approve('New Approver'))
        self.assertEqual(self.pending_spr.status, 'APPROVED')
        self.assertEqual(self.pending_spr.approved_by, 'New Approver')
        # Test approving an already approved/rejected SPR
        self.assertFalse(self.approved_spr.approve('Another Approver'))
        self.assertFalse(self.rejected_spr.approve('Another Approver'))

    def test_reject_method(self):
        new_pending_spr = SprApproval.objects.create(
            spr_code='SPR004',
            approval_date=timezone.now(),
            status='PENDING',
            approved_by=None
        )
        self.assertTrue(new_pending_spr.reject('Rejecter Man'))
        self.assertEqual(new_pending_spr.status, 'REJECTED')
        self.assertEqual(new_pending_spr.approved_by, 'Rejecter Man')
        self.assertFalse(self.approved_spr.reject('Another Rejecter'))

    def test_can_be_modified_method(self):
        self.assertFalse(self.approved_spr.can_be_modified())
        self.assertTrue(self.pending_spr.can_be_modified())
        self.assertFalse(self.rejected_spr.can_be_modified())

    def test_form_validation(self):
        # Test valid form
        valid_data = {
            'spr_code': 'SPR005',
            'approval_date': timezone.now().strftime('%Y-%m-%dT%H:%M'),
            'status': 'PENDING',
            'approved_by': ''
        }
        form = SprApprovalForm(data=valid_data)
        self.assertTrue(form.is_valid())

        # Test invalid SPR code
        invalid_spr_code_data = {**valid_data, 'spr_code': 'SPR 006'}
        form = SprApprovalForm(data=invalid_spr_code_data)
        self.assertFalse(form.is_valid())
        self.assertIn('spr_code', form.errors)

        # Test required 'approved_by' for APPROVED status
        missing_approver_data = {
            'spr_code': 'SPR007',
            'approval_date': timezone.now().strftime('%Y-%m-%dT%H:%M'),
            'status': 'APPROVED',
            'approved_by': ''
        }
        form = SprApprovalForm(data=missing_approver_data)
        self.assertFalse(form.is_valid())
        self.assertIn('approved_by', form.errors)
        
        # Test updating an approved record (should fail by model logic if can_be_modified is false)
        form = SprApprovalForm(instance=self.approved_spr, data={
            'spr_code': self.approved_spr.spr_code,
            'approval_date': self.approved_spr.approval_date.strftime('%Y-%m-%dT%H:%M'),
            'status': 'PENDING', # Attempting to change status of approved
            'approved_by': self.approved_spr.approved_by
        })
        self.assertFalse(form.is_valid())
        self.assertIn("Approved or Rejected SPR records cannot be modified.", form.non_field_errors())


class SprApprovalViewsTest(TestCase):
    client = Client()

    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.spr1 = SprApproval.objects.create(spr_code='SPR_A', approval_date=timezone.now(), status='PENDING')
        cls.spr2 = SprApproval.objects.create(spr_code='SPR_B', approval_date=timezone.now(), status='APPROVED', approved_by='Test User')
    
    def test_list_view_get(self):
        response = self.client.get(reverse('spr_approval_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approval/list.html')
        # The actual data is loaded by HTMX, so context_object_name won't be directly on this response

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('spr_approval_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approval/_spr_approval_table.html')
        self.assertTrue('spr_approvals' in response.context)
        self.assertEqual(len(response.context['spr_approvals']), 2)
        self.assertContains(response, 'SPR_A')
        self.assertContains(response, 'SPR_B')

    def test_create_view_get(self):
        response = self.client.get(reverse('spr_approval_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approval/form.html')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_valid(self):
        initial_count = SprApproval.objects.count()
        data = {
            'spr_code': 'SPR_C',
            'approval_date': timezone.now().strftime('%Y-%m-%dT%H:%M'),
            'status': 'PENDING',
            'approved_by': ''
        }
        response = self.client.post(reverse('spr_approval_add'), data)
        self.assertEqual(SprApproval.objects.count(), initial_count + 1)
        self.assertTrue(SprApproval.objects.filter(spr_code='SPR_C').exists())
        self.assertRedirects(response, reverse('spr_approval_list'), status_code=302, target_status_code=200) # Should redirect for non-HTMX
        
    def test_create_view_post_valid_htmx(self):
        initial_count = SprApproval.objects.count()
        data = {
            'spr_code': 'SPR_HTMX_C',
            'approval_date': timezone.now().strftime('%Y-%m-%dT%H:%M'),
            'status': 'PENDING',
            'approved_by': ''
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('spr_approval_add'), data, **headers)
        self.assertEqual(SprApproval.objects.count(), initial_count + 1)
        self.assertEqual(response.status_code, 204) # No Content for HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprApprovalList', response.headers['HX-Trigger'])

    def test_create_view_post_invalid(self):
        initial_count = SprApproval.objects.count()
        data = {
            'spr_code': 'INV SPR', # Invalid character
            'approval_date': timezone.now().strftime('%Y-%m-%dT%H:%M'),
            'status': 'PENDING',
            'approved_by': ''
        }
        response = self.client.post(reverse('spr_approval_add'), data)
        self.assertEqual(SprApproval.objects.count(), initial_count) # No new object created
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'material_management/spr_approval/form.html')
        self.assertFormError(response.context['form'], 'spr_code', 'SPR Code must be alphanumeric.')

    def test_update_view_get(self):
        response = self.client.get(reverse('spr_approval_edit', args=[self.spr1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approval/form.html')
        self.assertEqual(response.context['form'].instance, self.spr1)
        
    def test_update_view_post_valid(self):
        updated_code = 'SPR_A_MODIFIED'
        data = {
            'spr_code': updated_code,
            'approval_date': self.spr1.approval_date.strftime('%Y-%m-%dT%H:%M'),
            'status': 'APPROVED',
            'approved_by': 'Test User'
        }
        response = self.client.post(reverse('spr_approval_edit', args=[self.spr1.pk]), data)
        self.spr1.refresh_from_db()
        self.assertEqual(self.spr1.spr_code, updated_code)
        self.assertEqual(self.spr1.status, 'APPROVED')
        self.assertRedirects(response, reverse('spr_approval_list'))
        
    def test_update_view_post_valid_htmx(self):
        updated_code = 'SPR_HTMX_A_MODIFIED'
        data = {
            'spr_code': updated_code,
            'approval_date': self.spr1.approval_date.strftime('%Y-%m-%dT%H:%M'),
            'status': 'APPROVED',
            'approved_by': 'HTMX User'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('spr_approval_edit', args=[self.spr1.pk]), data, **headers)
        self.spr1.refresh_from_db()
        self.assertEqual(self.spr1.spr_code, updated_code)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprApprovalList', response.headers['HX-Trigger'])

    def test_update_view_post_on_approved_record(self):
        # Attempt to modify an already approved record
        original_code = self.spr2.spr_code
        data = {
            'spr_code': 'SPR_B_CHANGED', # Attempting to change
            'approval_date': self.spr2.approval_date.strftime('%Y-%m-%dT%H:%M'),
            'status': self.spr2.status,
            'approved_by': self.spr2.approved_by
        }
        response = self.client.post(reverse('spr_approval_edit', args=[self.spr2.pk]), data)
        self.spr2.refresh_from_db()
        self.assertEqual(self.spr2.spr_code, original_code) # Should not have changed
        self.assertRedirects(response, reverse('spr_approval_list')) # Redirect even if update failed for non-HTMX
        messages = list(response.context['messages']) # For non-HTMX response, messages are shown on success_url
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'This SPR Approval record cannot be modified as it is already approved or rejected.')
    
    def test_update_view_post_on_approved_record_htmx(self):
        original_code = self.spr2.spr_code
        data = {
            'spr_code': 'SPR_B_CHANGED', 
            'approval_date': self.spr2.approval_date.strftime('%Y-%m-%dT%H:%M'),
            'status': self.spr2.status,
            'approved_by': self.spr2.approved_by
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('spr_approval_edit', args=[self.spr2.pk]), data, **headers)
        self.spr2.refresh_from_db()
        self.assertEqual(self.spr2.spr_code, original_code)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors for HTMX
        self.assertTemplateUsed(response, 'material_management/spr_approval/form.html')
        self.assertContains(response, 'This SPR Approval record cannot be modified as it is already approved or rejected.')


    def test_delete_view_get(self):
        response = self.client.get(reverse('spr_approval_delete', args=[self.spr1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/spr_approval/confirm_delete.html')
        self.assertEqual(response.context['object'], self.spr1)
        
    def test_delete_view_post_valid(self):
        initial_count = SprApproval.objects.count()
        response = self.client.post(reverse('spr_approval_delete', args=[self.spr1.pk]))
        self.assertEqual(SprApproval.objects.count(), initial_count - 1)
        self.assertFalse(SprApproval.objects.filter(pk=self.spr1.pk).exists())
        self.assertRedirects(response, reverse('spr_approval_list'))
        
    def test_delete_view_post_valid_htmx(self):
        initial_count = SprApproval.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('spr_approval_delete', args=[self.spr1.pk]), **headers)
        self.assertEqual(SprApproval.objects.count(), initial_count - 1)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSprApprovalList', response.headers['HX-Trigger'])

    def test_delete_view_post_on_approved_record_htmx(self):
        initial_count = SprApproval.objects.count()
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('spr_approval_delete', args=[self.spr2.pk]), **headers)
        self.assertEqual(SprApproval.objects.count(), initial_count) # Object should not be deleted
        self.assertEqual(response.status_code, 403) # Forbidden
        self.assertContains(response, 'Cannot delete approved/rejected SPR.')

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

-   **HTMX for dynamic updates:**
    -   The `list.html` page uses `hx-get` to load the table content from `spr_approval_table/` on page load and whenever a `refreshSprApprovalList` event is triggered.
    -   'Add New', 'Edit', and 'Delete' buttons use `hx-get` to fetch their respective forms/confirmations into a modal (`#modalContent`).
    -   Form submissions (POST requests for Create/Update/Delete) use `hx-post` with `hx-swap="none"` or `hx-swap="innerHTML"`. Upon successful submission, the views respond with `status=204` (No Content) and an `HX-Trigger` header (`refreshSprApprovalList, closeModal`) to instruct the client-side to refresh the table and close the modal.
    -   Form validation errors cause the form to be re-rendered into the modal via HTMX, displaying the errors inline.
-   **Alpine.js for UI state management:**
    -   The `_` (hyperscript) syntax is used directly in HTML attributes for simple DOM manipulations like adding/removing CSS classes to show/hide the modal (`on click add .is-active to #modal`).
    -   A custom `closeModal` event is triggered from the modal's Cancel button or after a successful form submission via `HX-Trigger` to ensure the modal closes correctly.
    -   Alpine.js's `Alpine.store` is demonstrated for potential future complex state management, although simple modal control is handled by Hyperscript.
-   **DataTables for list views:**
    -   The `_spr_approval_table.html` partial template contains the `<table>` element and the JavaScript to initialize DataTables on that table. This ensures DataTables is re-initialized whenever the table content is refreshed via HTMX.
    -   DataTables provides client-side searching, sorting, and pagination out of the box.
-   **No full page reloads:** All CRUD operations and list refreshes occur dynamically using HTMX without requiring full page navigations.

## Final Notes

-   This plan successfully transforms the conceptual "SPR Approve Dashboard" into a modern Django application using a "Fat Model, Thin View" architecture, HTMX for dynamic interactions, Alpine.js for lightweight UI control, and DataTables for advanced table features.
-   Placeholders like `[APP_NAME]`, `[MODEL_NAME]`, `[FIELDx]` have been replaced with concrete inferred values (`material_management`, `SprApproval`, `spr_code`, `approval_date`, `status`, `approved_by`).
-   Business logic, such as `approve()`, `reject()`, and `can_be_modified()` methods, is strictly encapsulated within the `SprApproval` model.
-   Views remain concise, primarily coordinating between the model, form, and template.
-   Templates are designed to be modular and reusable, extending a base template and using partials for HTMX-loaded content.
-   Comprehensive tests are provided for both model business logic and view functionality, including HTMX interactions.
-   The overall approach prioritizes automation and maintainability, paving the way for AI-assisted migration of similar patterns.