## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

This plan outlines the modernization of the "Insufficient Budget" error display page from ASP.NET to a modern Django application. The goal is to move from a legacy, session-dependent data display to a robust, database-backed solution with a highly interactive user interface using HTMX and Alpine.js, all while ensuring maintainability and scalability.

### Business Value Proposition:
Transitioning this module to Django provides significant business advantages:
*   **Enhanced User Experience:** Modern, responsive interfaces with instant feedback from HTMX and Alpine.js eliminate full page reloads, making the application faster and more enjoyable for users.
*   **Improved Maintainability:** Django's clear structure, Python's readability, and explicit separation of concerns (models for business logic, thin views for control flow) drastically reduce the complexity of the codebase, making it easier to understand, debug, and extend.
*   **Increased Performance & Scalability:** Django is designed for high-traffic applications. By moving away from session-dependent data handling and leveraging efficient database interactions, the application will perform better under load and scale more effectively as your business grows.
*   **Future-Proofing:** Adopting a popular, actively maintained framework like Django, combined with modern frontend techniques (HTMX, Alpine.js), ensures your application remains relevant and adaptable to future technology trends.
*   **Reduced Operational Costs:** Simplified development and maintenance processes lead to lower long-term operational costs and a quicker time-to-market for new features.

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Analysis:** The ASP.NET code primarily uses `Session["X"]` as its data source for the `GridView`. This indicates that the data is not directly fetched from a database within this specific ASP.NET page's code-behind. However, in a robust, modern application, data should persist in a database. Therefore, we **infer** a plausible database table structure that would hold such "Insufficient Budget" error information. This allows the Django application to be database-driven rather than relying on session state, which is an anti-pattern.

**Inferred Details:**
*   **[TABLE_NAME]:** `tbl_purchase_order_errors`
*   **[MODEL_NAME]:** `PurchaseOrderError`
*   **Columns & Data Types (inferred from "Insufficient Budget" context and typical error logs/data):**
    *   `id`: Primary key (auto-generated by Django).
    *   `po_number`: `VARCHAR(50)` - The purchase order number related to the error.
    *   `item_description`: `TEXT` - Description of the item causing the budget issue.
    *   `required_budget`: `DECIMAL(18, 2)` - The budget amount required.
    *   `available_budget`: `DECIMAL(18, 2)` - The budget amount currently available.
    *   `error_message`: `TEXT` - A detailed message about the budget error.
    *   `error_date`: `DATETIME` - When the error occurred.
    *   `is_resolved`: `BOOLEAN` - A flag to indicate if the error has been addressed/resolved (for future management).

## Step 2: Identify Backend Functionality

**Task:** Determine the core operations from the ASP.NET code.

**Analysis:**
*   **Read (Display):** The `GridView2` is populated by `Session["X"]` via the `FillGrid()` method during `Page_Load`. This is a core "read" operation, displaying a list of "Insufficient Budget" items.
*   **Pagination:** The `GridView2_PageIndexChanging` event handles pagination for the displayed data. This will be replaced by DataTables' client-side pagination in Django.
*   **Navigation/Redirect:** The `btnCancel_Click` event conditionally redirects the user to `PO_PR_Items.aspx` or `PO_SPR_Items.aspx` based on the `PRSPR` query string parameter. This involves reading `SupCode` and `PRSPR` from the query string.
*   **CRUD (Create, Update, Delete):** No explicit CUD operations are present in *this specific* ASP.NET page (`PO_Error.aspx`). It functions purely as an error display. However, to provide a comprehensive and extensible Django solution as per requirements, we will implement standard Django `CreateView`, `UpdateView`, and `DeleteView` as placeholders. These views would enable future management of these "errors" if needed (e.g., marking as resolved, adding notes).

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Analysis:**
*   **`<asp:GridView ID="GridView2">`:** This control is used to display tabular data with pagination. In Django, this will be replaced by a standard HTML `<table>` element rendered by a `ListView`, enhanced with the DataTables JavaScript library for client-side features like pagination, sorting, and searching.
*   **`<asp:Button ID="btnCancel">`:** A standard button that triggers a server-side click event (`btnCancel_Click`). In Django, this will be a simple HTML `<button>` or `<a>` tag that navigates to a relevant URL, potentially using Django's URL routing to mimic the conditional redirection.
*   **Master Page (`MasterPage.master`):** The ASP.NET page uses a master page for layout. In Django, this translates directly to template inheritance, where `list.html` (and other templates) will `extend 'core/base.html'`.
*   **CSS/JS Links:** `yui-datatable.css`, `StyleSheet.css`, `PopUpMsg.js`, `loadingNotifier.js`. These will be managed by Django's static files system, included in `base.html`, or replaced by modern alternatives (e.g., Tailwind CSS, HTMX, Alpine.js, DataTables CDN).

## Step 4: Generate Django Code

### 4.1 Models (`material_management/models.py`)

This model defines the structure for `PurchaseOrderError` records, mapping to the inferred database table. The `managed = False` ensures Django doesn't create/manage the table, allowing it to work with an existing schema.

```python
from django.db import models

class PurchaseOrderError(models.Model):
    """
    Represents an insufficient budget error for purchase orders.
    Maps to the inferred tbl_purchase_order_errors table.
    """
    # Assuming primary key `id` is auto-created by Django by default for unmanaged models
    po_number = models.CharField(db_column='po_number', max_length=50, verbose_name='PO Number')
    item_description = models.TextField(db_column='item_description', verbose_name='Item Description')
    required_budget = models.DecimalField(db_column='required_budget', max_digits=18, decimal_places=2, verbose_name='Required Budget')
    available_budget = models.DecimalField(db_column='available_budget', max_digits=18, decimal_places=2, verbose_name='Available Budget')
    error_message = models.TextField(db_column='error_message', verbose_name='Error Message')
    error_date = models.DateTimeField(db_column='error_date', verbose_name='Error Date', auto_now_add=True)
    is_resolved = models.BooleanField(db_column='is_resolved', default=False, verbose_name='Is Resolved')

    class Meta:
        managed = False  # Important: Django won't create/manage this table.
        db_table = 'tbl_purchase_order_errors'  # Matches the inferred table name
        verbose_name = 'Purchase Order Error'
        verbose_name_plural = 'Purchase Order Errors'
        ordering = ['-error_date'] # Order by latest errors first

    def __str__(self):
        return f"PO Error {self.po_number} - {self.error_message[:50]}"

    # Example of a 'fat model' business logic method (not present in original, but good practice)
    def can_be_resolved(self):
        """
        Determines if the error can logically be marked as resolved.
        (e.g., if required_budget is no longer greater than available_budget)
        """
        return self.required_budget <= self.available_budget and not self.is_resolved

    def mark_as_resolved(self):
        """Marks the error as resolved if possible."""
        if self.can_be_resolved():
            self.is_resolved = True
            self.save()
            return True
        return False
```

### 4.2 Forms (`material_management/forms.py`)

A simple ModelForm for the `PurchaseOrderError` model, primarily used for the placeholder Create/Update views. Widgets are styled with Tailwind CSS classes.

```python
from django import forms
from .models import PurchaseOrderError

class PurchaseOrderErrorForm(forms.ModelForm):
    """
    Form for creating and updating PurchaseOrderError instances.
    """
    class Meta:
        model = PurchaseOrderError
        fields = [
            'po_number', 
            'item_description', 
            'required_budget', 
            'available_budget', 
            'error_message', 
            'is_resolved'
        ]
        widgets = {
            'po_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item_description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-24'}),
            'required_budget': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'available_budget': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'error_message': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm h-32'}),
            'is_resolved': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
        }
    
    # Custom validation example (not explicitly in original, but good practice)
    def clean(self):
        cleaned_data = super().clean()
        required_budget = cleaned_data.get('required_budget')
        available_budget = cleaned_data.get('available_budget')

        if required_budget is not None and available_budget is not None:
            if required_budget < 0 or available_budget < 0:
                self.add_error(None, "Budget values cannot be negative.")
            if required_budget < available_budget and not cleaned_data.get('is_resolved'):
                self.add_error(None, "Error might be resolved if required budget is less than available.")
        return cleaned_data
```

### 4.3 Views (`material_management/views.py`)

This section contains the core Django views. `PurchaseOrderErrorListView` handles the main page, while `PurchaseOrderErrorTablePartialView` specifically renders the DataTables content via HTMX. The other views are standard CRUD placeholders, designed to work with HTMX-powered modals. A `CancelRedirectView` mimics the original ASP.NET button logic.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from .models import PurchaseOrderError
from .forms import PurchaseOrderErrorForm

class PurchaseOrderErrorListView(ListView):
    """
    Displays a list of Purchase Order Errors. This is the main entry point for the page.
    The actual table content is loaded via HTMX by PurchaseOrderErrorTablePartialView.
    """
    model = PurchaseOrderError
    template_name = 'material_management/purchaseordererror/list.html'
    context_object_name = 'purchase_order_errors'

class PurchaseOrderErrorTablePartialView(ListView):
    """
    Renders only the table content for Purchase Order Errors.
    Designed to be fetched via HTMX to dynamically update the list view.
    """
    model = PurchaseOrderError
    template_name = 'material_management/purchaseordererror/_purchaseordererror_table.html'
    context_object_name = 'purchase_order_errors'

    def get_queryset(self):
        """
        The original ASP.NET used Session["X"]. In Django, this should be data from the DB.
        For demonstration, it fetches all errors. If this were a real 'session-based' error,
        it would be more complex, e.g., fetching a specific user's temporary errors.
        """
        # In a real system, you might filter this based on user or context
        # For now, it fetches all errors from the assumed database table.
        return super().get_queryset()

class PurchaseOrderErrorCreateView(CreateView):
    """
    Handles the creation of new Purchase Order Error records.
    Designed to be loaded and submitted via HTMX in a modal.
    """
    model = PurchaseOrderError
    form_class = PurchaseOrderErrorForm
    template_name = 'material_management/purchaseordererror/_purchaseordererror_form.html'
    success_url = reverse_lazy('purchaseordererror_list') # Fallback, HTMX usually handles redirect

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Error added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return no content for HTMX, trigger refresh on client
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderErrorList'
                }
            )
        return response # For non-HTMX requests

class PurchaseOrderErrorUpdateView(UpdateView):
    """
    Handles the updating of existing Purchase Order Error records.
    Designed to be loaded and submitted via HTMX in a modal.
    """
    model = PurchaseOrderError
    form_class = PurchaseOrderErrorForm
    template_name = 'material_management/purchaseordererror/_purchaseordererror_form.html'
    context_object_name = 'purchase_order_error'
    success_url = reverse_lazy('purchaseordererror_list') # Fallback

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Purchase Order Error updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderErrorList'
                }
            )
        return response

class PurchaseOrderErrorDeleteView(DeleteView):
    """
    Handles the deletion of Purchase Order Error records.
    Designed to be loaded and submitted via HTMX in a modal.
    """
    model = PurchaseOrderError
    template_name = 'material_management/purchaseordererror/_purchaseordererror_confirm_delete.html'
    context_object_name = 'purchase_order_error'
    success_url = reverse_lazy('purchaseordererror_list') # Fallback

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Purchase Order Error deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshPurchaseOrderErrorList'
                }
            )
        return response

class CancelRedirectView(View):
    """
    Mimics the btnCancel_Click functionality from the ASP.NET code.
    Redirects based on the 'PRSPR' query parameter.
    """
    def get(self, request, *args, **kwargs):
        sup_code = request.GET.get('Code', '')
        prspr = request.GET.get('PRSPR', '0') # Default to '0' as per ASP.NET logic

        if prspr == '0':
            # This URL needs to map to the Django equivalent of PO_PR_Items.aspx
            # Assuming a 'po_pr_items_list' URL name for the target page
            return HttpResponseRedirect(reverse_lazy('po_pr_items_list') + f'?Code={sup_code}')
        else:
            # This URL needs to map to the Django equivalent of PO_SPR_Items.aspx
            # Assuming a 'po_spr_items_list' URL name for the target page
            return HttpResponseRedirect(reverse_lazy('po_spr_items_list') + f'?Code={sup_code}')

# Placeholder views for the redirect targets for testing purposes
class PO_PR_ItemsListView(ListView):
    template_name = 'material_management/po_pr_items.html'
    queryset = [] # No actual data needed for this placeholder
    context_object_name = 'items'

class PO_SPR_ItemsListView(ListView):
    template_name = 'material_management/po_spr_items.html'
    queryset = [] # No actual data needed for this placeholder
    context_object_name = 'items'
```

### 4.4 Templates (`material_management/templates/material_management/purchaseordererror/`)

These templates handle the rendering of the list and modal forms, incorporating HTMX for dynamic interactions and DataTables for list management.

#### `list.html`

This is the main page template. It extends `core/base.html` and uses HTMX to load the actual table content.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Insufficient Budget Errors</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'purchaseordererror_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Error
        </button>
    </div>
    
    <div class="bg-white shadow-lg rounded-lg overflow-hidden p-6">
        <div id="purchaseordererrorTable-container"
             hx-trigger="load, refreshPurchaseOrderErrorList from:body"
             hx-get="{% url 'purchaseordererror_table' %}"
             hx-swap="innerHTML">
            <!-- Loading indicator while HTMX fetches the table -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading Purchase Order Errors...</p>
            </div>
        </div>
    </div>

    <!-- Modal for form (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 hidden is-active"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
             _="on htmx:afterOnLoad add .is-active to #modal">
            <!-- Content will be loaded here via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for broader UI state.
        // For simple modal open/close, HTMX + _hyperscript is often sufficient.
    });
</script>
{% endblock %}
```

#### `_purchaseordererror_table.html`

This partial template contains only the table structure, designed to be loaded dynamically by HTMX and integrated with DataTables.

```html
<table id="purchaseordererrorTable" class="min-w-full bg-white border-collapse">
    <thead>
        <tr class="bg-gray-100 text-gray-600 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-left border-b-2 border-gray-200">SN</th>
            <th class="py-3 px-6 text-left border-b-2 border-gray-200">PO Number</th>
            <th class="py-3 px-6 text-left border-b-2 border-gray-200">Item Description</th>
            <th class="py-3 px-6 text-right border-b-2 border-gray-200">Required Budget</th>
            <th class="py-3 px-6 text-right border-b-2 border-gray-200">Available Budget</th>
            <th class="py-3 px-6 text-left border-b-2 border-gray-200">Error Message</th>
            <th class="py-3 px-6 text-left border-b-2 border-gray-200">Error Date</th>
            <th class="py-3 px-6 text-left border-b-2 border-gray-200">Resolved</th>
            <th class="py-3 px-6 text-center border-b-2 border-gray-200">Actions</th>
        </tr>
    </thead>
    <tbody class="text-gray-700 text-sm font-light">
        {% for obj in purchase_order_errors %}
        <tr class="border-b border-gray-200 hover:bg-gray-50">
            <td class="py-3 px-6 text-left whitespace-nowrap">{{ forloop.counter }}</td>
            <td class="py-3 px-6 text-left">{{ obj.po_number }}</td>
            <td class="py-3 px-6 text-left truncate max-w-xs">{{ obj.item_description }}</td>
            <td class="py-3 px-6 text-right">{{ obj.required_budget|floatformat:2 }}</td>
            <td class="py-3 px-6 text-right">{{ obj.available_budget|floatformat:2 }}</td>
            <td class="py-3 px-6 text-left truncate max-w-xs">{{ obj.error_message }}</td>
            <td class="py-3 px-6 text-left">{{ obj.error_date|date:"Y-m-d H:i" }}</td>
            <td class="py-3 px-6 text-center">
                {% if obj.is_resolved %}
                    <span class="bg-green-200 text-green-800 py-1 px-3 rounded-full text-xs">Yes</span>
                {% else %}
                    <span class="bg-red-200 text-red-800 py-1 px-3 rounded-full text-xs">No</span>
                {% endif %}
            </td>
            <td class="py-3 px-6 text-center">
                <div class="flex item-center justify-center">
                    <button
                        class="w-8 h-8 rounded-full bg-yellow-100 hover:bg-yellow-200 text-yellow-700 transition duration-300 ease-in-out flex items-center justify-center mr-2"
                        hx-get="{% url 'purchaseordererror_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        title="Edit">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zm-6.044 1.772a1 1 0 00-.283 1.314l-.578 1.446-2.5-2.5 1.446-.578a1 1 0 001.314-.283l1.89-1.89zM5 10a1 1 0 011-1h.01a1 1 0 011 1v.01a1 1 0 01-1 1H6a1 1 0 01-1-1v-.01zM10 15a1 1 0 011-1h.01a1 1 0 011 1v.01a1 1 0 01-1 1H11a1 1 0 01-1-1v-.01zM15 10a1 1 0 011-1h.01a1 1 0 011 1v.01a1 1 0 01-1 1H16a1 1 0 01-1-1v-.01zM10 5a1 1 0 011-1h.01a1 1 0 011 1v.01a1 1 0 01-1 1H11a1 1 0 01-1-1v-.01zM15 15a1 1 0 011-1h.01a1 1 0 011 1v.01a1 1 0 01-1 1H16a1 1 0 01-1-1v-.01zM10 10a1 1 0 011-1h.01a1 1 0 011 1v.01a1 1 0 01-1 1H11a1 1 0 01-1-1v-.01zM5 15a1 1 0 011-1h.01a1 1 0 011 1v.01a1 1 0 01-1 1H6a1 1 0 01-1-1v-.01z"/></svg>
                    </button>
                    <button
                        class="w-8 h-8 rounded-full bg-red-100 hover:bg-red-200 text-red-700 transition duration-300 ease-in-out flex items-center justify-center"
                        hx-get="{% url 'purchaseordererror_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal"
                        title="Delete">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm6 0a1 1 0 11-2 0v6a1 1 0 112 0V8z" clip-rule="evenodd"/></svg>
                    </button>
                </div>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="9" class="py-8 px-6 text-center text-gray-500">No insufficient budget errors found.</td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
    // Ensure DataTables is initialized ONLY when the content is loaded via HTMX
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $(document).ready(function() {
            $('#purchaseordererrorTable').DataTable({
                "pageLength": 15, // Matches original ASP.NET PageSize
                "lengthMenu": [[10, 15, 25, 50, -1], [10, 15, 25, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf>rt<"flex justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });
        });
    } else {
        console.warn("jQuery or DataTables not loaded. Ensure CDN links are in base.html.");
    }
</script>
```

#### `_purchaseordererror_form.html`

This partial template is used for both creating and updating forms within the modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">
        {{ form.instance.pk|yesno:'Edit,Add' }} Purchase Order Error
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.querySelector('#modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            {% for field in form %}
            <div class="col-span-1 {% if field.name == 'item_description' or field.name == 'error_message' %}md:col-span-2{% endif %}">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}<span class="text-red-500">*</span>{% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-2 text-sm text-red-600">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        {% if form.non_field_errors %}
        <div class="mt-4 p-4 border border-red-300 bg-red-50 rounded-md text-red-700">
            {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
            {% endfor %}
        </div>
        {% endif %}

        <div class="mt-8 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-100 shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 transition duration-300 ease-in-out">
                Save Changes
            </button>
        </div>
    </form>
</div>
```

#### `_purchaseordererror_confirm_delete.html`

This partial template is used for the delete confirmation within the modal.

```html
<div class="p-6">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-4">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">
        Are you sure you want to delete the Purchase Order Error for <strong>{{ purchase_order_error.po_number }}</strong>?
        This action cannot be undone.
    </p>
    
    <form hx-post="{% url 'purchaseordererror_delete' purchase_order_error.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.querySelector('#modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button
                type="button"
                class="px-5 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-100 shadow-sm transition duration-300 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="px-5 py-2 border border-transparent rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 transition duration-300 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### Placeholder Templates for Redirect Targets:
These minimal templates are necessary for the `CancelRedirectView` to have valid targets for testing.

**`material_management/templates/material_management/po_pr_items.html`**
```html
{% extends 'core/base.html' %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold">PO PR Items List (Placeholder)</h2>
    <p class="mt-4 text-gray-700">This page would display details for Purchase Request Items.</p>
    <p class="mt-2 text-gray-600">Code received: {{ request.GET.Code|default:"N/A" }}</p>
    <a href="{% url 'purchaseordererror_list' %}" class="mt-6 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Back to PO Error List</a>
</div>
{% endblock %}
```

**`material_management/templates/material_management/po_spr_items.html`**
```html
{% extends 'core/base.html' %}
{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-2xl font-bold">PO SPR Items List (Placeholder)</h2>
    <p class="mt-4 text-gray-700">This page would display details for Supplier Purchase Request Items.</p>
    <p class="mt-2 text-gray-600">Code received: {{ request.GET.Code|default:"N/A" }}</p>
    <a href="{% url 'purchaseordererror_list' %}" class="mt-6 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Back to PO Error List</a>
</div>
{% endblock %}
```

### 4.5 URLs (`material_management/urls.py`)

Defines the URL patterns for accessing the various views, including the main list page, HTMX partials, and CRUD operations.

```python
from django.urls import path
from .views import (
    PurchaseOrderErrorListView,
    PurchaseOrderErrorTablePartialView,
    PurchaseOrderErrorCreateView,
    PurchaseOrderErrorUpdateView,
    PurchaseOrderErrorDeleteView,
    CancelRedirectView,
    PO_PR_ItemsListView, # Placeholder for redirect target
    PO_SPR_ItemsListView, # Placeholder for redirect target
)

urlpatterns = [
    # Main list view for Purchase Order Errors
    path('purchaseordererror/', PurchaseOrderErrorListView.as_view(), name='purchaseordererror_list'),
    
    # HTMX endpoint to fetch only the table content
    path('purchaseordererror/table/', PurchaseOrderErrorTablePartialView.as_view(), name='purchaseordererror_table'),
    
    # CRUD operations for Purchase Order Errors (intended for HTMX modals)
    path('purchaseordererror/add/', PurchaseOrderErrorCreateView.as_view(), name='purchaseordererror_add'),
    path('purchaseordererror/edit/<int:pk>/', PurchaseOrderErrorUpdateView.as_view(), name='purchaseordererror_edit'),
    path('purchaseordererror/delete/<int:pk>/', PurchaseOrderErrorDeleteView.as_view(), name='purchaseordererror_delete'),

    # URL to handle the "Cancel" button logic
    path('purchaseordererror/cancel/', CancelRedirectView.as_view(), name='purchaseordererror_cancel'),

    # Placeholder URLs for the redirect targets from the original ASP.NET page
    path('po_pr_items/', PO_PR_ItemsListView.as_view(), name='po_pr_items_list'),
    path('po_spr_items/', PO_SPR_ItemsListView.as_view(), name='po_spr_items_list'),
]
```

### 4.6 Tests (`material_management/tests.py`)

Comprehensive unit tests for the `PurchaseOrderError` model and integration tests for all implemented views, ensuring functionality and HTMX interactions are correctly handled.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import HttpResponseRedirect
from datetime import datetime
from .models import PurchaseOrderError

class PurchaseOrderErrorModelTest(TestCase):
    """
    Unit tests for the PurchaseOrderError model.
    """
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.error1 = PurchaseOrderError.objects.create(
            po_number='PO-001',
            item_description='Component X procurement',
            required_budget=1500.00,
            available_budget=1000.00,
            error_message='Insufficient funds for Component X.',
            error_date=datetime(2023, 1, 15, 10, 30, 0),
            is_resolved=False
        )
        cls.error2 = PurchaseOrderError.objects.create(
            po_number='PO-002',
            item_description='Software License Renewal',
            required_budget=500.00,
            available_budget=700.00,
            error_message='Budget surplus, but flagged for review.',
            error_date=datetime(2023, 2, 1, 14, 0, 0),
            is_resolved=False # Initially not resolved
        )

    def test_purchaseordererror_creation(self):
        """Test that an error object is created correctly."""
        self.assertEqual(self.error1.po_number, 'PO-001')
        self.assertEqual(self.error1.item_description, 'Component X procurement')
        self.assertEqual(self.error1.required_budget, 1500.00)
        self.assertEqual(self.error1.available_budget, 1000.00)
        self.assertFalse(self.error1.is_resolved)
        self.assertEqual(str(self.error1), 'PO Error PO-001 - Insufficient funds for Component X.')

    def test_model_field_verbose_names(self):
        """Test verbose names for model fields."""
        field_label = self.error1._meta.get_field('po_number').verbose_name
        self.assertEqual(field_label, 'PO Number')
        field_label = self.error1._meta.get_field('required_budget').verbose_name
        self.assertEqual(field_label, 'Required Budget')
        field_label = self.error1._meta.get_field('is_resolved').verbose_name
        self.assertEqual(field_label, 'Is Resolved')

    def test_can_be_resolved_method(self):
        """Test the can_be_resolved business logic."""
        # Error1: Required > Available, not resolved
        self.assertFalse(self.error1.can_be_resolved())

        # Error2: Required < Available, not resolved initially
        self.assertTrue(self.error2.can_be_resolved())

        # Mark error2 as resolved, then it should not be "can_be_resolved" anymore
        self.error2.is_resolved = True
        self.error2.save()
        self.assertFalse(self.error2.can_be_resolved())

    def test_mark_as_resolved_method(self):
        """Test the mark_as_resolved method."""
        # Error1 cannot be resolved yet
        self.assertFalse(self.error1.mark_as_resolved())
        self.assertFalse(self.error1.is_resolved) # Still not resolved

        # Simulate budget increase for error1
        self.error1.available_budget = 2000.00
        self.error1.save()
        
        # Now error1 should be resolvable
        self.assertTrue(self.error1.mark_as_resolved())
        self.assertTrue(self.error1.is_resolved)

class PurchaseOrderErrorViewsTest(TestCase):
    """
    Integration tests for PurchaseOrderError views, including HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create a sample error for testing CRUD operations
        cls.test_error = PurchaseOrderError.objects.create(
            po_number='PO-TEST-001',
            item_description='Test item for views',
            required_budget=100.00,
            available_budget=50.00,
            error_message='Test error message.',
            error_date=datetime(2023, 3, 1, 12, 0, 0),
            is_resolved=False
        )
        # Create a resolved error for testing list view filter/display
        PurchaseOrderError.objects.create(
            po_number='PO-TEST-002',
            item_description='Another test item',
            required_budget=200.00,
            available_budget=200.00,
            error_message='Another test error message.',
            error_date=datetime(2023, 3, 2, 12, 0, 0),
            is_resolved=True
        )

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        """Test that the main list view loads correctly."""
        response = self.client.get(reverse('purchaseordererror_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseordererror/list.html')
        self.assertContains(response, 'Insufficient Budget Errors')
        # Check that the HTMX container for the table is present
        self.assertContains(response, 'id="purchaseordererrorTable-container"')

    def test_table_partial_view_get(self):
        """Test that the HTMX table partial loads correctly."""
        response = self.client.get(reverse('purchaseordererror_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseordererror/_purchaseordererror_table.html')
        self.assertContains(response, 'PO-TEST-001') # Check for data
        self.assertContains(response, 'PO-TEST-002')
        self.assertContains(response, 'id="purchaseordererrorTable"') # Check for table element

    def test_create_view_get(self):
        """Test that the create form loads correctly via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseordererror_add'), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseordererror/_purchaseordererror_form.html')
        self.assertContains(response, 'Add Purchase Order Error')
        self.assertContains(response, '<form hx-post') # Ensure HTMX form attributes are present

    def test_create_view_post_success(self):
        """Test successful creation of a new error via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'po_number': 'NEW-PO-003',
            'item_description': 'New item for test',
            'required_budget': 300.00,
            'available_budget': 250.00,
            'error_message': 'New test error',
            'is_resolved': 'off' # Checkbox value for 'false'
        }
        response = self.client.post(reverse('purchaseordererror_add'), data, **headers)
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshPurchaseOrderErrorList')
        self.assertTrue(PurchaseOrderError.objects.filter(po_number='NEW-PO-003').exists())

    def test_create_view_post_invalid(self):
        """Test invalid form submission for creation via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        data = {
            'po_number': '', # Invalid: missing required field
            'item_description': 'Invalid item',
            'required_budget': -100.00, # Invalid: negative budget
            'available_budget': 50.00,
            'error_message': 'Should fail',
            'is_resolved': 'off'
        }
        response = self.client.post(reverse('purchaseordererror_add'), data, **headers)
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'material_management/purchaseordererror/_purchaseordererror_form.html')
        self.assertContains(response, 'This field is required.')
        self.assertContains(response, 'Budget values cannot be negative.')
        self.assertFalse(PurchaseOrderError.objects.filter(item_description='Invalid item').exists())

    def test_update_view_get(self):
        """Test that the update form loads correctly via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseordererror_edit', args=[self.test_error.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseordererror/_purchaseordererror_form.html')
        self.assertContains(response, 'Edit Purchase Order Error')
        self.assertContains(response, self.test_error.po_number) # Check that existing data is pre-filled

    def test_update_view_post_success(self):
        """Test successful update of an existing error via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        updated_data = {
            'po_number': self.test_error.po_number,
            'item_description': 'Updated description',
            'required_budget': 120.00,
            'available_budget': 120.00, # Making it resolvable
            'error_message': 'Updated message, now resolved',
            'is_resolved': 'on' # Mark as resolved
        }
        response = self.client.post(reverse('purchaseordererror_edit', args=[self.test_error.pk]), updated_data, **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshPurchaseOrderErrorList')
        self.test_error.refresh_from_db()
        self.assertEqual(self.test_error.item_description, 'Updated description')
        self.assertTrue(self.test_error.is_resolved)

    def test_delete_view_get(self):
        """Test that the delete confirmation loads correctly via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('purchaseordererror_delete', args=[self.test_error.pk]), **headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseordererror/_purchaseordererror_confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')
        self.assertContains(response, self.test_error.po_number)

    def test_delete_view_post_success(self):
        """Test successful deletion of an error via HTMX."""
        headers = {'HTTP_HX_REQUEST': 'true'}
        # Create a new error specifically for deletion test so self.test_error remains for other tests if needed
        error_to_delete = PurchaseOrderError.objects.create(
            po_number='TO_DELETE',
            item_description='This will be deleted',
            required_budget=1.0,
            available_budget=0.0,
            error_message='Delete me.',
            is_resolved=False
        )
        response = self.client.post(reverse('purchaseordererror_delete', args=[error_to_delete.pk]), **headers)
        self.assertEqual(response.status_code, 204)
        self.assertEqual(response.headers.get('HX-Trigger'), 'refreshPurchaseOrderErrorList')
        self.assertFalse(PurchaseOrderError.objects.filter(pk=error_to_delete.pk).exists())

    def test_cancel_redirect_view_pr(self):
        """Test cancel redirect for PRSPR='0'."""
        response = self.client.get(reverse('purchaseordererror_cancel') + '?Code=SUP001&PRSPR=0')
        self.assertIsInstance(response, HttpResponseRedirect)
        self.assertEqual(response.url, reverse('po_pr_items_list') + '?Code=SUP001')

    def test_cancel_redirect_view_spr(self):
        """Test cancel redirect for PRSPR != '0'."""
        response = self.client.get(reverse('purchaseordererror_cancel') + '?Code=SUP002&PRSPR=1')
        self.assertIsInstance(response, HttpResponseRedirect)
        self.assertEqual(response.url, reverse('po_spr_items_list') + '?Code=SUP002')

    def test_cancel_redirect_view_default_prspr(self):
        """Test cancel redirect when PRSPR is not provided (defaults to '0')."""
        response = self.client.get(reverse('purchaseordererror_cancel') + '?Code=SUP003')
        self.assertIsInstance(response, HttpResponseRedirect)
        self.assertEqual(response.url, reverse('po_pr_items_list') + '?Code=SUP003')

    # Test the placeholder redirect target pages load correctly
    def test_po_pr_items_list_view(self):
        response = self.client.get(reverse('po_pr_items_list') + '?Code=TESTCODE')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_pr_items.html')
        self.assertContains(response, 'PO PR Items List (Placeholder)')
        self.assertContains(response, 'Code received: TESTCODE')

    def test_po_spr_items_list_view(self):
        response = self.client.get(reverse('po_spr_items_list') + '?Code=ANOTHERCODE')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/po_spr_items.html')
        self.assertContains(response, 'PO SPR Items List (Placeholder)')
        self.assertContains(response, 'Code received: ANOTHERCODE')

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

*   **HTMX for Dynamic Content:** The main `purchaseordererror/list.html` page uses `hx-get` to load the table content from `{% url 'purchaseordererror_table' %}` into a `div` with `id="purchaseordererrorTable-container"`. This `hx-get` is triggered `on load` and also `on refreshPurchaseOrderErrorList from:body`, ensuring the table automatically updates after any CRUD operation.
*   **HTMX for Modals:** All CRUD operations (add, edit, delete) are initiated by buttons with `hx-get` attributes that target `id="modalContent"` within a hidden modal (`id="modal"`). When the HTMX request completes and the form/confirmation partial is loaded into `modalContent`, a `_hyperscript` listener `on htmx:afterOnLoad add .is-active to #modal` ensures the modal becomes visible.
*   **HTMX Form Submission:** Forms inside the modal (`_purchaseordererror_form.html`, `_purchaseordererror_confirm_delete.html`) use `hx-post="{{ request.path }}" hx-swap="none"`. Upon successful submission (HTTP 204 No Content), the `HX-Trigger: refreshPurchaseOrderErrorList` header sent by the Django view triggers the list to refresh automatically, and a `_hyperscript` listener on the form closes the modal: `hx-on::after-request="if(event.detail.xhr.status === 204) { document.querySelector('#modal').classList.remove('is-active'); }"`.
*   **Alpine.js for UI State (Optional/Refinement):** While much of the modal logic is handled by HTMX and `_hyperscript`, Alpine.js could be used for more complex client-side state management if required (e.g., dynamic form fields, client-side validation feedback before server submission). For this specific conversion, the basic modal visibility is handled by `_hyperscript`, keeping the Alpine.js integration minimal but present for future expansion.
*   **DataTables for List Views:** The `_purchaseordererror_table.html` partial includes the JavaScript to initialize DataTables on `id="purchaseordererrorTable"`. This provides client-side searching, sorting, and pagination, replacing the server-side `GridView` pagination logic. The `$(document).ready()` ensures DataTables initializes after the content is loaded by HTMX.
*   **DRY Template Inheritance:** All main templates (`list.html`, and the placeholder `po_pr_items.html`, `po_spr_items.html`) extend `core/base.html`, ensuring all necessary CDN links for jQuery, DataTables, HTMX, Alpine.js, and Tailwind CSS are loaded once at the base level.

## Final Notes

*   **Placeholders:** The database table `tbl_purchase_order_errors` and its columns were inferred. In a real migration, these would be precisely extracted from the database schema or data access layer of the ASP.NET application. Similarly, the "Add/Edit/Delete" functionality for errors is provided as a standard Django pattern, assuming future requirements, as the original page was read-only.
*   **Data Source `Session["X"]`:** The most significant difference is the transition from `Session["X"]` to a persistent database model. This is a fundamental modernization step that greatly improves data integrity, scalability, and maintainability.
*   **Business Logic:** The `can_be_resolved` and `mark_as_resolved` methods in the `PurchaseOrderError` model exemplify the "fat model" approach, placing business logic directly where the data resides, keeping views clean and focused on request/response handling.
*   **Modularity:** The solution is broken down into distinct Django application files (`models.py`, `forms.py`, `views.py`, `urls.py`, and separate template files), promoting a clean and maintainable architecture.