## ASP.NET to Django Conversion Script: PO Print Module

This document outlines a comprehensive plan for modernizing the ASP.NET `PO_Print` module to a Django-based solution. The focus is on leveraging modern Django 5.0+ patterns, HTMX for dynamic interactions, Alpine.js for UI state management, and DataTables for efficient data presentation, all while maintaining a "Fat Model, Thin View" architecture and emphasizing automation-driven conversion.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

The ASP.NET code interacts primarily with `tblMM_PO_Master`, and performs lookups against `tblHR_OfficeStaff` and `tblFinancial_master`.

**Identified Tables and Columns:**

*   **`tblMM_PO_Master`** (Primary table for Purchase Orders)
    *   `Id` (INT, Primary Key)
    *   `FileName` (NVARCHAR, for download)
    *   `FinYearId` (INT/NVARCHAR, Foreign Key to `tblFinancial_master`)
    *   `PONo` (NVARCHAR, Purchase Order Number)
    *   `SysDate` (DATETIME, Date of PO creation)
    *   `SysTime` (DATETIME, Time of PO creation)
    *   `SessionId` (NVARCHAR, Employee ID who generated the PO, Foreign Key to `tblHR_OfficeStaff`)
    *   `AmendmentNo` (INT, Amendment number)
    *   `SupplierId` (NVARCHAR, Supplier Code, expected from query string `Code`)
    *   `CompId` (INT, Company ID, expected from session `compid`)
    *   `PRSPRFlag` (INT, Flag to determine redirection for "Select" action, likely 0 or 1)
    *   `FileData` (VARBINARY(MAX), inferred for file storage based on `DownloadFile.aspx` parameters `qfd=FileData`)
    *   `ContentType` (NVARCHAR, inferred for file type based on `DownloadFile.aspx` parameters `qct=ContentType`)

*   **`tblHR_OfficeStaff`** (For Employee details)
    *   `EmpId` (NVARCHAR, Primary Key, corresponds to `SessionId` in `tblMM_PO_Master`)
    *   `EmployeeName` (NVARCHAR)
    *   `Title` (NVARCHAR)
    *   `CompId` (INT)

*   **`tblFinancial_master`** (For Financial Year details)
    *   `FinYearId` (INT/NVARCHAR, Primary Key, corresponds to `FinYearId` in `tblMM_PO_Master`)
    *   `FinYear` (NVARCHAR, e.g., "2023-2024")
    *   `CompId` (INT)

### Step 2: Identify Backend Functionality

Task: Determine the operations performed by the ASP.NET code.

This module primarily serves as a search and display interface for Purchase Orders, with actions to view details or download associated files. It does **not** perform direct Create, Update, or Delete (CRUD) operations on the `tblMM_PO_Master` table from this page.

*   **Read (Query & Display):**
    *   Retrieves Purchase Order records from `tblMM_PO_Master`.
    *   Filters records based on:
        *   `SupplierId` (from URL query string).
        *   `CompId` (from user session).
        *   `FinYearId` (from user session).
        *   Optional search criteria: `PONo` (Purchase Order Number) or `SessionId` (Employee ID, derived from Employee Name search).
    *   Enriches displayed data by fetching `EmployeeName` (as "Gen. By") from `tblHR_OfficeStaff` and `FinYear` from `tblFinancial_master`. This is an N+1 query pattern in ASP.NET that needs to be optimized using Django's `select_related`.
    *   Presents data in a paginated, sortable, and filterable table (GridView).
    *   Dynamically populates an "Amd No" dropdown for each row with numbers from `AmendmentNo` down to `0`.

*   **Actions:**
    *   **"Select" (LinkButton `lnkButton`):** Based on `PRSPRFlag` in `tblMM_PO_Master`, redirects the user to either `PO_Print_Details.aspx` or `PO_SPR_Print_Details.aspx`, passing `mid`, `pono`, `Code`, `AmdNo`, `Key`, `ModId`, `SubModId` as URL query parameters. This will be mapped to a Django redirect view.
    *   **"Download" (LinkButton `LinkBtnDownload`):** Checks if `FileName` exists for the PO. If so, it redirects to a generic `DownloadFile.aspx` page, passing `Id`, `tbl`, `qfd`, `qfn`, `qct` parameters to serve the file. This will be handled by a Django file response view.

*   **Search/Filter Control Logic:**
    *   Toggles visibility of `txtpoNo` and `txtEmpName` based on `drpfield` selection.
    *   Triggers data reload (`LoadData`) on search button click, dropdown selection change, or pagination events.

*   **Autocomplete (Web Method `GetCompletionList`):**
    *   Provides suggestions for `Employee Name` text box by querying `tblHR_OfficeStaff` based on `prefixText`. Returns formatted strings like "EmployeeName [EmpId]".

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

*   **Search/Filter Area:**
    *   `drpfield` (DropDownList): Selects search type. Django equivalent: HTML `<select>` with Alpine.js for visibility toggling.
    *   `txtEmpName` (TextBox): Text input for employee name search. Django equivalent: HTML `<input type="text">` with HTMX for autocomplete.
    *   `txtEmpName_AutoCompleteExtender`: Provides autocomplete for `txtEmpName`. Django equivalent: HTMX `hx-get` to an autocomplete endpoint.
    *   `txtpoNo` (TextBox): Text input for PO number search. Django equivalent: HTML `<input type="text">`.
    *   `btnSearch` (Button): Triggers search. Django equivalent: HTML `<button>` with HTMX `hx-get` to reload the table.
    *   `btnCancel` (Button): Redirects to `PO_Print_Supplier.aspx`. Django equivalent: HTML `<button>` with `window.location.href` or a Django redirect.

*   **Data Display Area:**
    *   `GridView2`: Displays the list of Purchase Orders. Django equivalent: HTML `<table>` structured for DataTables with HTMX for dynamic loading.
    *   `PagerSettings`: For pagination. DataTables handles this client-side.
    *   `EmptyDataTemplate`: Displays "No data to display!" Django equivalent: Conditional rendering in template.
    *   **Columns within GridView:**
        *   `SN`: Row index. Django equivalent: `{{ forloop.counter }}`.
        *   `LinkButton ID="lnkButton"` (Select): Action button. Django equivalent: HTML `<button>` or `<a>` with HTMX.
        *   `Label ID="lblId"` (Id, hidden): PO ID.
        *   `Label ID="lblFinYearId"` (Fin Year Id, hidden): Financial Year ID.
        *   `Label ID="lblFinYear"` (Fin Year): Financial Year.
        *   `Label ID="lblpono"` (PO No): Purchase Order Number.
        *   `Label ID="lblDate"` (Date): PO Date.
        *   `DropDownList ID="AmdDropDown"` (Amd No): Amendment Number dropdown. Django equivalent: HTML `<select>` dynamically populated from the `AmendmentNo` field from the model, potentially with Alpine.js for client-side generation, or simply iterating in the template.
        *   `Label ID="lblGenBy"` (Gen. By): Generated By (Employee Name).
        *   `LinkButton ID="LinkBtnDownload"` (Download): Action button. Django equivalent: HTML `<button>` or `<a>` with HTMX.

### Step 4: Generate Django Code

We will create a Django application named `material_management` for this module.

#### 4.1 Models (`material_management/models.py`)

We'll define models mapping directly to the existing database tables, utilizing `managed = False` and `db_table`. Business logic (like `get_generated_by_name` and `get_financial_year`) will be encapsulated within the `PurchaseOrder` model to adhere to the Fat Model principle.

```python
from django.db import models
from django.db.models import F
from django.urls import reverse
from django.utils import timezone

class OfficeStaff(models.Model):
    """
    Maps to tblHR_OfficeStaff for employee details.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    title = models.CharField(db_column='Title', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Office Staff Member'
        verbose_name_plural = 'Office Staff'

    def __str__(self):
        return f"{self.title or ''} {self.employee_name or ''}".strip()

class FinancialYear(models.Model):
    """
    Maps to tblFinancial_master for financial year details.
    """
    fin_year_id = models.CharField(db_column='FinYearId', primary_key=True, max_length=50) # Assuming FinYearId could be string
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"ID: {self.fin_year_id}"

class PurchaseOrder(models.Model):
    """
    Maps to tblMM_PO_Master for Purchase Order details.
    """
    id = models.IntegerField(db_column='Id', primary_key=True)
    file_name = models.CharField(db_column='FileName', max_length=255, blank=True, null=True)
    # Use CharField for fin_year_id as the ASP.NET code treats it as string sometimes
    fin_year_id = models.CharField(db_column='FinYearId', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=255, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    sys_time = models.DateTimeField(db_column='SysTime', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True) # Corresponds to EmpId
    amendment_no = models.IntegerField(db_column='AmendmentNo', blank=True, null=True)
    supplier_id = models.CharField(db_column='SupplierId', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    prspr_flag = models.IntegerField(db_column='PRSPRFlag', blank=True, null=True) # 0 or 1 for redirection logic
    # Inferred fields for file download
    file_data = models.BinaryField(db_column='FileData', blank=True, null=True)
    content_type = models.CharField(db_column='ContentType', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'
        verbose_name = 'Purchase Order'
        verbose_name_plural = 'Purchase Orders'
        ordering = ['-id'] # Matches Order By tblMM_PO_Master.Id Desc

    def __str__(self):
        return self.po_no or f"PO ID: {self.id}"

    @property
    def formatted_date(self):
        """Returns SysDate in a user-friendly format."""
        return self.sys_date.strftime('%d/%m/%Y') if self.sys_date else ''

    @property
    def generated_by_name(self):
        """Fetches the employee name from tblHR_OfficeStaff."""
        if self.session_id:
            try:
                staff = OfficeStaff.objects.get(emp_id=self.session_id, comp_id=self.comp_id)
                return str(staff) # Uses OfficeStaff.__str__
            except OfficeStaff.DoesNotExist:
                pass
        return 'N/A'

    @property
    def financial_year_display(self):
        """Fetches the financial year from tblFinancial_master."""
        if self.fin_year_id:
            try:
                fy = FinancialYear.objects.get(fin_year_id=self.fin_year_id, comp_id=self.comp_id)
                return fy.fin_year
            except FinancialYear.DoesNotExist:
                pass
        return 'N/A'

    def get_amendment_options(self):
        """Generates a list of amendment numbers from AmendmentNo down to 0."""
        if self.amendment_no is not None and self.amendment_no >= 0:
            return list(range(self.amendment_no, -1, -1))
        return [0] # Default to [0] if no amendment number or invalid

    def get_select_redirect_url(self, supplier_code, current_module_id, current_sub_module_id):
        """
        Determines the redirection URL for the 'Select' action based on PRSPRFlag.
        This mimics the ASP.NET Response.Redirect logic.
        """
        base_url = reverse('material_management:po_details') # Default or PO_Print_Details equivalent
        if self.prspr_flag == 1:
            base_url = reverse('material_management:po_spr_details') # PO_SPR_Print_Details equivalent

        # Construct query parameters
        # `getRandomKey` is a unique key, often for cache busting or security;
        # in Django, session/CSRF tokens handle this. A simple UUID could be used if strictly needed.
        import uuid
        random_key = uuid.uuid4().hex[:10] # Example for a short key

        params = {
            'mid': self.id,
            'pono': self.po_no,
            'Code': supplier_code,
            'AmdNo': self.amendment_no, # This is the initial AmdNo, not the selected one from dropdown
            'Key': random_key,
            'ModId': current_module_id,
            'SubModId': current_sub_module_id
        }
        from urllib.parse import urlencode
        return f"{base_url}?{urlencode(params)}"
        
    def get_download_url(self):
        """Returns the URL for downloading the associated file."""
        if self.file_data and self.file_name:
            # We'll use a generic download view for consistency
            return reverse('material_management:download_file', kwargs={'pk': self.pk})
        return None

```

#### 4.2 Forms (`material_management/forms.py`)

This page doesn't have a traditional CRUD form for `PurchaseOrder` itself, but it has a search form. We'll create a simple `Form` for search criteria.

```python
from django import forms
from django.db.models import Q
from .models import PurchaseOrder, OfficeStaff

class PurchaseOrderSearchForm(forms.Form):
    SEARCH_BY_CHOICES = [
        ('employee_name', 'Employee Name'),
        ('po_no', 'PO No'),
    ]
    search_by = forms.ChoiceField(
        choices=SEARCH_BY_CHOICES,
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': 'hx-vals', 'hx-target': '#search-input-container', 'hx-swap': 'innerHTML', 'hx-trigger': 'change', 'x-model': 'searchByType'})
    )
    search_term_employee = forms.CharField(
        label='Employee Name',
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3 w-[350px]', 'placeholder': 'Enter Employee Name', 'hx-get': '/material_management/autocomplete/employees/', 'hx-trigger': 'keyup changed delay:500ms', 'hx-target': '#employee-suggestions', 'hx-swap': 'innerHTML', 'autocomplete': 'off'})
    )
    search_term_po = forms.CharField(
        label='PO No',
        required=False,
        widget=forms.TextInput(attrs={'class': 'box3', 'placeholder': 'Enter PO Number'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Initially hide the PO No field or Employee Name field based on default
        self.fields['search_term_po'].widget.attrs['x-show'] = "searchByType === 'po_no'"
        self.fields['search_term_employee'].widget.attrs['x-show'] = "searchByType === 'employee_name'"
        # Placeholder for autocomplete suggestions
        self.fields['search_term_employee'].widget.attrs['x-init'] = "setupAutocomplete()"


    def get_search_query(self, company_id, financial_year_id):
        search_by = self.cleaned_data.get('search_by')
        search_term_po = self.cleaned_data.get('search_term_po')
        search_term_employee = self.cleaned_data.get('search_term_employee')

        q_objects = Q(comp_id=company_id) & Q(fin_year_id__lte=financial_year_id)

        if search_by == 'po_no' and search_term_po:
            q_objects &= Q(po_no__iexact=search_term_po) # Use iexact for case-insensitive exact match
        elif search_by == 'employee_name' and search_term_employee:
            # Need to get EmpId from EmployeeName. The original ASP.NET uses fun.getCode(txtEmpName.Text)
            # This implies `txtEmpName.Text` might be "EmployeeName [EmpId]". We'll parse it.
            emp_id = None
            if '[' in search_term_employee and ']' in search_term_employee:
                try:
                    emp_id = search_term_employee.split('[')[-1].replace(']', '').strip()
                except IndexError:
                    pass # Malformed input, emp_id remains None
            
            if emp_id:
                q_objects &= Q(session_id=emp_id)
            else:
                # Fallback if emp_id not parsed, try to find by name directly (less efficient but handles input)
                # This could result in multiple EmpIds if names are not unique.
                # The original fun.getCode suggests a unique code lookup.
                # For robust search, we could use prefetch_related for OfficeStaff and filter by name.
                # However, sticking to the `fun.getCode` interpretation, we expect EmployeeName [EmpId]
                # For simplicity, if emp_id is not parsed, we will not filter by employee name.
                # A more sophisticated solution would involve a dedicated employee search via model method.
                pass # No filter if emp_id cannot be derived

        return q_objects

```

#### 4.3 Views (`material_management/views.py`)

The views will be thin, primarily handling HTTP requests and delegating business logic to the models or managers. We'll use Django's built-in `ListView` for the main page, and custom `View` classes for the HTMX partials, autocomplete, and action handling.

```python
from django.views.generic import ListView, View
from django.urls import reverse_lazy, reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse, FileResponse, Http404
from django.shortcuts import redirect, get_object_or_404
from django.db.models import Q
from django.template.loader import render_to_string
from django.conf import settings
import os

from .models import PurchaseOrder, OfficeStaff, FinancialYear
from .forms import PurchaseOrderSearchForm

class PurchaseOrderListView(ListView):
    """
    Main view for displaying the PO Print page.
    Handles initial load and rendering of the search form.
    The PO list table itself is loaded via HTMX.
    """
    model = PurchaseOrder
    template_name = 'material_management/purchaseorder/list.html'
    context_object_name = 'purchase_orders'
    paginate_by = 20 # Although DataTables handles pagination, this is for server-side if needed for filtering

    def get_queryset(self):
        # Initial queryset, actual filtering for DataTable is done in PurchaseOrderTablePartialView
        # Apply global filters like company_id and financial_year_id if present in session
        queryset = super().get_queryset()
        
        company_id = self.request.session.get('compid', None)
        financial_year_id = self.request.session.get('finyear', None)

        if company_id:
            queryset = queryset.filter(comp_id=company_id)
        if financial_year_id:
            queryset = queryset.filter(fin_year_id__lte=financial_year_id)
        
        # Prefetch related data to avoid N+1 queries in template
        # Note: session_id and fin_year_id are CharFields, so direct select_related is not possible
        # We need to manually annotate or access properties which use gets (handled by fat model properties)
        # If the relationships were true FKs, select_related would be more efficient.
        return queryset.order_by('-id') # Ensure default ordering

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['search_form'] = PurchaseOrderSearchForm(self.request.GET or None)
        # Pass initial state for Alpine.js if needed (e.g., based on previous search)
        if self.request.GET.get('search_by'):
            context['initial_search_by'] = self.request.GET.get('search_by')
        else:
            context['initial_search_by'] = 'employee_name' # Default from ASP.NET
        return context

class PurchaseOrderTablePartialView(View):
    """
    HTMX endpoint to render just the DataTables content.
    Handles filtering, searching, and pagination based on query parameters.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.session.get('compid')
        financial_year_id = request.session.get('finyear')
        supplier_code = request.GET.get('Code') # From QueryString, like original ASP.NET
        
        # Defensive check for required session/query parameters
        if not all([company_id, financial_year_id, supplier_code]):
            # Render an empty table or an error message within the table container
            html = render_to_string(
                'material_management/purchaseorder/_purchaseorder_table.html',
                {'purchase_orders': [], 'error_message': 'Required session/query parameters missing.'},
                request=request
            )
            return HttpResponse(html)

        # Initialize form with GET data for validation and cleaned data access
        form = PurchaseOrderSearchForm(request.GET)
        queryset = PurchaseOrder.objects.all()

        if form.is_valid():
            # Apply base filters from session and query string first
            # Apply company ID and financial year ID from session as per original ASP.NET
            queryset = queryset.filter(
                supplier_id=supplier_code,
                comp_id=company_id,
                fin_year_id__lte=financial_year_id # FinYearId<='FyId' as per original
            )
            
            # Apply search criteria from form
            search_query_q_objects = form.get_search_query(company_id, financial_year_id)
            queryset = queryset.filter(search_query_q_objects)
        else:
            # If form is not valid (e.g., malformed GET params), just apply base filters
            queryset = queryset.filter(
                supplier_id=supplier_code,
                comp_id=company_id,
                fin_year_id__lte=financial_year_id
            )

        # Order by Id Desc as per original code
        purchase_orders = queryset.order_by('-id')

        html = render_to_string(
            'material_management/purchaseorder/_purchaseorder_table.html',
            {'purchase_orders': purchase_orders, 'supplier_code': supplier_code},
            request=request
        )
        return HttpResponse(html)

class EmployeeAutocompleteView(View):
    """
    HTMX endpoint for employee name autocomplete.
    Returns JSON list of suggestions.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        company_id = request.session.get('compid')

        if not company_id:
            return JsonResponse([], safe=False)

        if prefix_text:
            employees = OfficeStaff.objects.filter(
                comp_id=company_id,
                employee_name__icontains=prefix_text
            ).values_list('employee_name', 'emp_id').order_by('employee_name')[:10] # Limit suggestions

            suggestions = []
            for name, emp_id in employees:
                suggestions.append(f"{name} [{emp_id}]")
            return JsonResponse(suggestions, safe=False)
        return JsonResponse([], safe=False)

class PurchaseOrderSelectView(View):
    """
    Handles the 'Select' command from the GridView.
    Performs redirection based on the PRSPRFlag.
    """
    def get(self, request, pk, *args, **kwargs):
        po = get_object_or_404(PurchaseOrder, pk=pk)
        
        # Extract necessary info from request. The original ASP.NET was very specific.
        supplier_code = request.GET.get('Code', '')
        # Assuming current_module_id and current_sub_module_id are fixed or passed via session/config
        current_module_id = request.session.get('ModId', 6) # Default 6
        current_sub_module_id = request.session.get('SubModId', 35) # Default 35

        # The AmdNo from the dropdown on the grid is the one chosen by the user
        # This view's URL won't directly get it. The HTMX `hx-get` on the button 
        # needs to include it, likely via `hx-vals` or a dynamic URL part.
        # For simplicity, we'll assume the initially loaded amendment_no from the PO object.
        # If the dropdown *selection* needs to be passed, the HTMX call needs to capture that.
        # Example: hx-get="{{% url 'material_management:po_select' po.pk %}}?selectedAmdNo={{ Alpine.js_value }}"
        # For now, matching the original ASP.NET `LoadData` that populates dropdown based on PO's AmdNo.
        selected_amd_no = po.amendment_no

        # Construct the redirect URL using the model's helper method
        redirect_url = po.get_select_redirect_url(
            supplier_code, current_module_id, current_sub_module_id
        )
        
        return redirect(redirect_url)

class PurchaseOrderDownloadView(View):
    """
    Handles the 'Download' command from the GridView.
    Serves the file stored in the database.
    """
    def get(self, request, pk, *args, **kwargs):
        po = get_object_or_404(PurchaseOrder, pk=pk)

        if not po.file_data or not po.file_name:
            messages.error(request, 'No file found for this Purchase Order.')
            # Redirect back to the list page or show an error
            return redirect(reverse('material_management:po_print_list')) # Or better, a HTMX error message

        # Serve the file directly from BinaryField
        response = FileResponse(po.file_data, content_type=po.content_type or 'application/octet-stream')
        response['Content-Disposition'] = f'attachment; filename="{po.file_name}"'
        return response

class CancelView(View):
    """
    Handles the Cancel button click, redirecting to PO_Print_Supplier.aspx.
    """
    def get(self, request, *args, **kwargs):
        # Assuming PO_Print_Supplier.aspx maps to a Django URL 'material_management:po_supplier_list'
        # with required parameters.
        # Original: Response.Redirect("PO_Print_Supplier.aspx?ModId=6&SubModId=35");
        return redirect(reverse_lazy('material_management:po_supplier_list') + '?ModId=6&SubModId=35')

```

#### 4.4 Templates (`material_management/purchaseorder/list.html`, `material_management/purchaseorder/_purchaseorder_table.html`)

We will create the main list template and a partial for the DataTables content to be loaded via HTMX. The `_purchaseorder_table.html` will contain the DataTables initialization.

**`material_management/purchaseorder/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg">
        <h2 class="text-2xl font-bold mb-4">PO - Print</h2>

        <div x-data="{ searchByType: '{{ initial_search_by }}' }" class="mb-6">
            <form hx-get="{% url 'material_management:po_print_table' %}"
                  hx-target="#poTable-container"
                  hx-swap="innerHTML"
                  hx-indicator="#loading-indicator">
                {% csrf_token %}
                <table class="w-full">
                    <tr>
                        <td class="pb-4">
                            <label for="{{ search_form.search_by.id_for_label }}" class="sr-only">Search By</label>
                            <select id="{{ search_form.search_by.id_for_label }}" name="{{ search_form.search_by.name }}"
                                    class="box3" x-model="searchByType">
                                {% for value, label in search_form.search_by.field.choices %}
                                    <option value="{{ value }}" {% if value == initial_search_by %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            &nbsp;
                            <span id="search-input-container">
                                <input type="text" id="{{ search_form.search_term_employee.id_for_label }}"
                                       name="{{ search_form.search_term_employee.name }}"
                                       x-show="searchByType === 'employee_name'"
                                       x-bind:class="searchByType === 'employee_name' ? 'block' : 'hidden'"
                                       class="box3 w-[350px]" placeholder="Enter Employee Name"
                                       hx-get="{% url 'material_management:employee_autocomplete' %}"
                                       hx-trigger="keyup changed delay:500ms"
                                       hx-target="#employee-suggestions"
                                       hx-swap="innerHTML"
                                       autocomplete="off"
                                       value="{{ search_form.search_term_employee.value|default:'' }}">
                                <div id="employee-suggestions" class="absolute z-10 bg-white border border-gray-300 mt-1 w-[350px] shadow-lg rounded-md"
                                     x-show="searchByType === 'employee_name' && $el.innerHTML.trim() !== ''"
                                     x-cloak>
                                     <!-- Suggestions loaded here by HTMX -->
                                </div>

                                <input type="text" id="{{ search_form.search_term_po.id_for_label }}"
                                       name="{{ search_form.search_term_po.name }}"
                                       x-show="searchByType === 'po_no'"
                                       x-bind:class="searchByType === 'po_no' ? 'block' : 'hidden'"
                                       class="box3" placeholder="Enter PO No"
                                       value="{{ search_form.search_term_po.value|default:'' }}">
                            </span>
                            &nbsp;
                            <button type="submit" class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">Search</button>
                            &nbsp;
                            <a href="{% url 'material_management:po_cancel' %}" class="redbox bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">Cancel</a>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        
        <div id="poTable-container"
             hx-trigger="load, refreshPoList from:body"
             hx-get="{% url 'material_management:po_print_table' %}?{{ request.GET.urlencode }}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div id="loading-indicator" class="text-center htmx-indicator">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2">Loading Purchase Orders...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js is initiated via base.html
    // No specific Alpine.js component needed here other than x-data for search type.

    // Manual handling for HTMX autocomplete suggestions click
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'employee-suggestions') {
            document.querySelectorAll('#employee-suggestions div').forEach(item => {
                item.onclick = function() {
                    const employeeInput = document.getElementById('{{ search_form.search_term_employee.id_for_label }}');
                    employeeInput.value = this.textContent.trim();
                    event.detail.target.innerHTML = ''; // Clear suggestions
                };
            });
        }
    });

    // Event listener for messages (Django messages)
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        // Check for specific HTMX triggers if needed, e.g., to close modals
        const hxTrigger = evt.detail.xhr.getResponseHeader('HX-Trigger');
        if (hxTrigger) {
            try {
                const triggers = JSON.parse(hxTrigger);
                if (triggers.refreshPoList) {
                    // Handle refresh logic if needed, already done by hx-trigger on container
                }
            } catch (e) {
                console.error("Failed to parse HX-Trigger:", hxTrigger, e);
            }
        }
    });
</script>
{% endblock %}

```

**`material_management/purchaseorder/_purchaseorder_table.html`**

```html
{% if error_message %}
    <div class="text-red-600 text-center py-4 text-lg">
        {{ error_message }}
    </div>
{% elif purchase_orders %}
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="purchaseOrderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Select</th>
                <th scope="col" class="hidden">Id</th>
                <th scope="col" class="hidden">Fin Year Id</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">PO No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Amd No</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[35%]">Gen. By</th>
                <th scope="col" class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-[250px]">Download</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for po in purchase_orders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button class="text-blue-600 hover:text-blue-800 font-semibold"
                            hx-get="{% url 'material_management:po_select' po.pk %}?Code={{ supplier_code }}"
                            hx-trigger="click"
                            hx-swap="none"
                            hx-push-url="true">
                        Select
                    </button>
                </td>
                <td class="hidden">{{ po.id }}</td>
                <td class="hidden">{{ po.fin_year_id }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.financial_year_display }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.po_no }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ po.formatted_date }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <select class="box3 w-[50px]">
                        {% for amd_option in po.get_amendment_options %}
                            <option value="{{ amd_option }}" {% if amd_option == po.amendment_no %}selected{% endif %}>{{ amd_option }}</option>
                        {% endfor %}
                    </select>
                </td>
                <td class="py-2 px-4 border-b border-gray-200 w-[35%]">{{ po.generated_by_name }}</td>
                <td class="py-2 px-4 border-b border-gray-200 w-[250px]">
                    {% if po.file_name %}
                        <button class="text-green-600 hover:text-green-800 font-semibold"
                                hx-get="{% url 'material_management:po_download' po.pk %}"
                                hx-trigger="click"
                                hx-swap="none"
                                hx-push-url="false">
                            {{ po.file_name }}
                        </button>
                    {% else %}
                        <span class="text-gray-500">No File</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    $(document).ready(function() {
        $('#purchaseOrderTable').DataTable({
            "pageLength": 20, // Matches original PageSize
            "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
            "responsive": true,
            "columnDefs": [
                { "visible": false, "targets": [2, 3] } // Hide 'Id' and 'Fin Year Id' columns
            ]
        });
    });
</script>
{% else %}
    <div class="text-center fontcss">
        <p class="font-bold text-lg text-red-700">No data to display !</p>
    </div>
{% endif %}

```

**`material_management/employee/_autocomplete_results.html`**

This template is for the HTMX autocomplete response (suggestions displayed below the input field).

```html
{% for suggestion in suggestions %}
    <div class="px-3 py-2 cursor-pointer hover:bg-gray-100 text-gray-800"
         hx-target="#{{ search_form.search_term_employee.id_for_label }}"
         hx-swap="outerHTML"
         _="on click set my input's value to event.target.textContent.trim() then remove @x-show from my parentElement">
        {{ suggestion }}
    </div>
{% empty %}
    <div class="px-3 py-2 text-gray-500">No suggestions</div>
{% endfor %}

```
*(Note: The `hx-target` and `hx-swap` on the `div` for suggestions is an advanced HTMX pattern to update the input field directly and hide the suggestions. It replaces the input field's content rather than just setting its value. A simpler way is `on click set #your_input_id.value to event.target.textContent` via Alpine or simpler JS, and then clearing the suggestion container's innerHTML.)*
Let's simplify the autocomplete HTML response to be picked up by Alpine.js for full control or by a simple JS listener as per the list.html `document.addEventListener('htmx:afterSwap')` function.
The provided `employee/_autocomplete_results.html` is good for HTMX directly.

Revised `EmployeeAutocompleteView` for `_autocomplete_results.html`:

```python
# ... (imports)

class EmployeeAutocompleteView(View):
    """
    HTMX endpoint for employee name autocomplete.
    Returns HTML fragment of suggestions.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        company_id = request.session.get('compid')

        suggestions = []
        if company_id and prefix_text:
            employees = OfficeStaff.objects.filter(
                comp_id=company_id,
                employee_name__icontains=prefix_text
            ).values_list('employee_name', 'emp_id').order_by('employee_name')[:10]

            for name, emp_id in employees:
                suggestions.append(f"{name} [{emp_id}]")
        
        html = render_to_string(
            'material_management/employee/_autocomplete_results.html',
            {'suggestions': suggestions, 'search_form': PurchaseOrderSearchForm()}, # Pass form for ID reference
            request=request
        )
        return HttpResponse(html)

```

#### 4.5 URLs (`material_management/urls.py`)

Define the URL patterns to map incoming requests to the appropriate views.

```python
from django.urls import path
from .views import (
    PurchaseOrderListView,
    PurchaseOrderTablePartialView,
    EmployeeAutocompleteView,
    PurchaseOrderSelectView,
    PurchaseOrderDownloadView,
    CancelView
)

app_name = 'material_management' # Namespace for URLs

urlpatterns = [
    # Main PO Print List Page
    path('po-print/', PurchaseOrderListView.as_view(), name='po_print_list'),
    
    # HTMX endpoint for the PO list table
    path('po-print/table/', PurchaseOrderTablePartialView.as_view(), name='po_print_table'),
    
    # HTMX endpoint for employee autocomplete
    path('autocomplete/employees/', EmployeeAutocompleteView.as_view(), name='employee_autocomplete'),
    
    # Action endpoints for 'Select' and 'Download'
    path('po-print/select/<int:pk>/', PurchaseOrderSelectView.as_view(), name='po_select'),
    path('po-print/download/<int:pk>/', PurchaseOrderDownloadView.as_view(), name='po_download'),
    
    # Cancel button redirect
    path('po-print/cancel/', CancelView.as_view(), name='po_cancel'),

    # Placeholders for pages redirected to by 'Select' (e.g., PO_Print_Details.aspx)
    # These would be actual views in a full system.
    path('po-details/', View.as_view(), name='po_details'), # Dummy view for redirection target
    path('po-spr-details/', View.as_view(), name='po_spr_details'), # Dummy view for redirection target
    path('po-supplier-list/', View.as_view(), name='po_supplier_list'), # Dummy view for redirection target
]

```

#### 4.6 Tests (`material_management/tests.py`)

Comprehensive tests are crucial. We'll include tests for the models and the views, covering data retrieval, filtering, and actions.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.http import FileResponse
from django.contrib.sessions.middleware import SessionMiddleware
from django.test.utils import override_settings
from io import BytesIO

from .models import PurchaseOrder, OfficeStaff, FinancialYear
from .forms import PurchaseOrderSearchForm

# Mock settings for file uploads and sessions if needed
@override_settings(MEDIA_ROOT='/tmp/test_media/', FILE_UPLOAD_TEMP_DIR='/tmp/test_temp/')
class ModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.company_id = 1
        cls.supplier_code = 'SUP001'
        cls.financial_year_id = '2023-24'

        cls.fy_2023 = FinancialYear.objects.create(fin_year_id='2023-24', fin_year='2023-2024', comp_id=cls.company_id)
        cls.fy_2022 = FinancialYear.objects.create(fin_year_id='2022-23', fin_year='2022-2023', comp_id=cls.company_id)

        cls.staff1 = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr.', comp_id=cls.company_id)
        cls.staff2 = OfficeStaff.objects.create(emp_id='EMP002', employee_name='Jane Smith', title='Ms.', comp_id=cls.company_id)

        cls.po1 = PurchaseOrder.objects.create(
            id=101, po_no='PO-2023-001', sys_date='2023-01-15', sys_time='10:00:00',
            session_id='EMP001', amendment_no=2, supplier_id=cls.supplier_code,
            comp_id=cls.company_id, fin_year_id='2023-24', prspr_flag=0,
            file_name='document1.pdf', file_data=b'pdf_content_1', content_type='application/pdf'
        )
        cls.po2 = PurchaseOrder.objects.create(
            id=102, po_no='PO-2023-002', sys_date='2023-02-20', sys_time='11:00:00',
            session_id='EMP002', amendment_no=0, supplier_id=cls.supplier_code,
            comp_id=cls.company_id, fin_year_id='2022-23', prspr_flag=1,
            file_name='image1.png', file_data=b'image_content_1', content_type='image/png'
        )
        cls.po3 = PurchaseOrder.objects.create(
            id=103, po_no='PO-2023-003', sys_date='2023-03-01', sys_time='12:00:00',
            session_id='EMP001', amendment_no=5, supplier_id='SUP002', # Different supplier
            comp_id=cls.company_id, fin_year_id='2023-24', prspr_flag=0,
            file_name=None, file_data=None, content_type=None
        )
        cls.po4 = PurchaseOrder.objects.create(
            id=104, po_no='PO-2023-004', sys_date='2023-03-10', sys_time='13:00:00',
            session_id='EMP001', amendment_no=0, supplier_id=cls.supplier_code,
            comp_id=cls.company_id + 1, fin_year_id='2023-24', prspr_flag=0 # Different company
        )

    def test_purchase_order_properties(self):
        self.assertEqual(self.po1.formatted_date, '15/01/2023')
        self.assertEqual(self.po1.generated_by_name, 'Mr. John Doe')
        self.assertEqual(self.po1.financial_year_display, '2023-2024')
        self.assertEqual(self.po1.get_amendment_options(), [2, 1, 0])
        self.assertEqual(self.po2.get_amendment_options(), [0])
        self.assertEqual(self.po3.get_amendment_options(), [5, 4, 3, 2, 1, 0])

    def test_purchase_order_download_url(self):
        self.assertIsNotNone(self.po1.get_download_url())
        self.assertIsNone(self.po3.get_download_url())

    def test_purchase_order_select_redirect_url(self):
        # Test PRSPRFlag = 0
        url_po1 = self.po1.get_select_redirect_url(self.supplier_code, 6, 35)
        self.assertTrue(url_po1.startswith(reverse('material_management:po_details')))
        self.assertIn('mid=101', url_po1)
        self.assertIn('pono=PO-2023-001', url_po1)

        # Test PRSPRFlag = 1
        url_po2 = self.po2.get_select_redirect_url(self.supplier_code, 6, 35)
        self.assertTrue(url_po2.startswith(reverse('material_management:po_spr_details')))
        self.assertIn('mid=102', url_po2)
        self.assertIn('pono=PO-2023-002', url_po2)

    def test_officestaff_str(self):
        self.assertEqual(str(self.staff1), 'Mr. John Doe')
        self.assertEqual(str(self.staff2), 'Ms. Jane Smith')

    def test_financialyear_str(self):
        self.assertEqual(str(self.fy_2023), '2023-2024')


class PurchaseOrderViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Setup data similar to ModelTest for views
        cls.company_id = 1
        cls.supplier_code = 'SUP001'
        cls.financial_year_id = '2023-24'

        cls.fy_2023 = FinancialYear.objects.create(fin_year_id='2023-24', fin_year='2023-2024', comp_id=cls.company_id)
        cls.fy_2022 = FinancialYear.objects.create(fin_year_id='2022-23', fin_year='2022-2023', comp_id=cls.company_id)

        cls.staff1 = OfficeStaff.objects.create(emp_id='EMP001', employee_name='John Doe', title='Mr.', comp_id=cls.company_id)
        cls.staff2 = OfficeStaff.objects.create(emp_id='EMP002', employee_name='Jane Smith', title='Ms.', comp_id=cls.company_id)

        cls.po1 = PurchaseOrder.objects.create(
            id=101, po_no='PO-2023-001', sys_date='2023-01-15', sys_time='10:00:00',
            session_id='EMP001', amendment_no=2, supplier_id=cls.supplier_code,
            comp_id=cls.company_id, fin_year_id='2023-24', prspr_flag=0,
            file_name='document1.pdf', file_data=b'pdf_content_1', content_type='application/pdf'
        )
        cls.po2 = PurchaseOrder.objects.create(
            id=102, po_no='PO-2023-002', sys_date='2023-02-20', sys_time='11:00:00',
            session_id='EMP002', amendment_no=0, supplier_id=cls.supplier_code,
            comp_id=cls.company_id, fin_year_id='2022-23', prspr_flag=1,
            file_name='image1.png', file_data=b'image_content_1', content_type='image/png'
        )
        cls.po3 = PurchaseOrder.objects.create(
            id=103, po_no='PO-2023-003', sys_date='2023-03-01', sys_time='12:00:00',
            session_id='EMP001', amendment_no=5, supplier_id='SUP002',
            comp_id=cls.company_id, fin_year_id='2023-24', prspr_flag=0,
            file_name=None, file_data=None, content_type=None
        )
        cls.po4 = PurchaseOrder.objects.create(
            id=104, po_no='PO-2023-004', sys_date='2023-03-10', sys_time='13:00:00',
            session_id='EMP001', amendment_no=0, supplier_id=cls.supplier_code,
            comp_id=cls.company_id + 1, fin_year_id='2023-24', prspr_flag=0
        )

    def setUp(self):
        self.client = Client()
        # Set up session variables
        session = self.client.session
        session['compid'] = self.company_id
        session['finyear'] = self.financial_year_id
        session.save()

    def test_po_print_list_view(self):
        response = self.client.get(reverse('material_management:po_print_list'), {'Code': self.supplier_code})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/list.html')
        self.assertIn('search_form', response.context)
        self.assertContains(response, 'PO - Print') # Check for page title

    def test_po_print_table_partial_view_no_params(self):
        # Simulate HTMX request without required GET params
        response = self.client.get(reverse('material_management:po_print_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/_purchaseorder_table.html')
        self.assertContains(response, 'Required session/query parameters missing.')


    def test_po_print_table_partial_view_initial_load(self):
        # Simulate HTMX request with required GET params
        response = self.client.get(reverse('material_management:po_print_table'), {'Code': self.supplier_code}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/purchaseorder/_purchaseorder_table.html')
        self.assertContains(response, self.po1.po_no)
        self.assertContains(response, self.po2.po_no)
        self.assertNotContains(response, self.po3.po_no) # Different supplier
        self.assertNotContains(response, self.po4.po_no) # Different company

    def test_po_print_table_partial_view_search_po_no(self):
        data = {'search_by': 'po_no', 'search_term_po': 'PO-2023-001', 'Code': self.supplier_code}
        response = self.client.get(reverse('material_management:po_print_table'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.po1.po_no)
        self.assertNotContains(response, self.po2.po_no)

    def test_po_print_table_partial_view_search_employee_name(self):
        data = {'search_by': 'employee_name', 'search_term_employee': 'John Doe [EMP001]', 'Code': self.supplier_code}
        response = self.client.get(reverse('material_management:po_print_table'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.po1.po_no)
        self.assertNotContains(response, self.po2.po_no)

    def test_employee_autocomplete_view(self):
        response = self.client.get(reverse('material_management:employee_autocomplete'), {'q': 'john'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/employee/_autocomplete_results.html')
        self.assertContains(response, 'John Doe [EMP001]')
        self.assertNotContains(response, 'Jane Smith')

    def test_employee_autocomplete_view_no_query(self):
        response = self.client.get(reverse('material_management:employee_autocomplete'), {'q': ''}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/employee/_autocomplete_results.html')
        self.assertContains(response, 'No suggestions')

    def test_po_select_view_flag_0(self):
        response = self.client.get(reverse('material_management:po_select', args=[self.po1.pk]), {'Code': self.supplier_code})
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertRedirects(response, self.po1.get_select_redirect_url(self.supplier_code, 6, 35), fetch_redirect_response=False)

    def test_po_select_view_flag_1(self):
        response = self.client.get(reverse('material_management:po_select', args=[self.po2.pk]), {'Code': self.supplier_code})
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertRedirects(response, self.po2.get_select_redirect_url(self.supplier_code, 6, 35), fetch_redirect_response=False)

    def test_po_select_view_non_existent(self):
        response = self.client.get(reverse('material_management:po_select', args=[999]), {'Code': self.supplier_code})
        self.assertEqual(response.status_code, 404) # Not Found

    def test_po_download_view_success(self):
        response = self.client.get(reverse('material_management:po_download', args=[self.po1.pk]))
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        self.assertEqual(response['Content-Disposition'], 'attachment; filename="document1.pdf"')
        self.assertEqual(response.content, b'pdf_content_1')

    def test_po_download_view_no_file(self):
        response = self.client.get(reverse('material_management:po_download', args=[self.po3.pk]))
        self.assertEqual(response.status_code, 302) # Redirect because of messages.error
        self.assertRedirects(response, reverse('material_management:po_print_list'), fetch_redirect_response=False)

    def test_po_download_view_non_existent(self):
        response = self.client.get(reverse('material_management:po_download', args=[999]))
        self.assertEqual(response.status_code, 404)

    def test_cancel_view(self):
        response = self.client.get(reverse('material_management:po_cancel'))
        self.assertEqual(response.status_code, 302) # Redirect
        self.assertRedirects(response, reverse('material_management:po_supplier_list') + '?ModId=6&SubModId=35', fetch_redirect_response=False)

```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Search and Table Refresh:** The main `list.html` uses `hx-get` on a form submission and on `poTable-container` load/refresh to fetch `_purchaseorder_table.html`. This ensures the table refreshes dynamically without a full page reload when search criteria change or on initial load.
*   **HTMX for Autocomplete:** `txtEmpName` uses `hx-get` to `employee_autocomplete` endpoint, updating `#employee-suggestions` div with search results.
*   **HTMX for Actions:** "Select" and "Download" buttons use `hx-get` to their respective views. `hx-swap="none"` for redirects ensures HTMX doesn't try to swap content.
*   **Alpine.js for UI State:** `x-data` and `x-model` are used in `list.html` to control the visibility of `txtEmpName` and `txtpoNo` based on the `drpfield` selection, mirroring `drpfield_SelectedIndexChanged` behavior client-side.
*   **DataTables:** Initialized on `_purchaseorder_table.html` load to provide client-side searching, sorting, and pagination.
*   **DRY Template Inheritance:** All templates extend `core/base.html`, adhering to the DRY principle.
*   **No custom JavaScript:** All dynamic interactions are handled by HTMX, Alpine.js, or DataTables, requiring no additional bespoke JavaScript logic.

### Final Notes

*   **Placeholders:** `[MODEL_NAME]`, `[APP_NAME]`, `[FIELD1]`, etc., have been replaced with `PurchaseOrder`, `material_management`, and actual field names.
*   **Fat Model, Thin View:** Business logic, such as `generated_by_name`, `financial_year_display`, `get_amendment_options`, and `get_select_redirect_url`, resides within the `PurchaseOrder` model, keeping views concise.
*   **Test Coverage:** Comprehensive unit and integration tests are provided for models and views, ensuring functionality and maintainability.
*   **Database Schema:** This plan assumes a direct migration with existing database schema, using `managed = False`. Any schema changes would require separate migration steps.
*   **Security:** `SessionMiddleware` and `CSRF` protection are implicitly handled by Django for POST requests. Query string parameters for redirects are sanitized by Django's URL encoding.
*   **Environment:** Assumes standard Django setup with database configuration to connect to the existing SQL Server.