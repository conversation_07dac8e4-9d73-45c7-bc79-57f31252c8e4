## ASP.NET to Django Conversion Script: Rate Register Modernization

This plan outlines the strategic transition of your ASP.NET `RateRegister` module to a modern Django-based solution. Our approach emphasizes AI-assisted automation, "fat model, thin view" architecture, and a dynamic frontend powered by HTMX and Alpine.js, ensuring a robust, scalable, and maintainable application.

### Business Benefits and Outcomes:

*   **Enhanced Performance**: Django's efficient ORM and Python's speed, combined with HTMX for partial page updates, will deliver a significantly faster user experience compared to traditional postbacks.
*   **Improved User Experience**: A highly interactive interface with instant search results and seamless form submissions, without full page reloads, will make the "Rate Register" more user-friendly.
*   **Reduced Development Costs**: By moving business logic to models and using well-defined patterns, future development and maintenance will become more predictable and less prone to errors, significantly reducing costs.
*   **Simplified Maintenance**: Adherence to Django best practices, clear separation of concerns, and comprehensive test coverage will make the codebase easier to understand, debug, and extend.
*   **Future-Proof Technology**: Migrating to a modern, actively maintained framework like Django positions your application for long-term sustainability and easier integration with new technologies.
*   **Scalability**: Django's architecture is designed to scale, allowing the application to grow with your business needs without significant re-engineering.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:

*   NEVER include `base.html` template code in your output - assume it already exists.
*   Focus ONLY on component-specific code for the current module.
*   Always include complete unit tests for models and integration tests for views.
*   Use modern Django 5.0+ patterns and follow best practices.
*   Keep your code clean, efficient, and avoid redundancy.
*   Always generate complete, runnable Django code.

## AutoERP Guidelines:

*   Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
*   Map models to existing database using `managed = False` and `db_table`.
*   Implement DataTables for client-side searching, sorting, and pagination.
*   Use HTMX for dynamic interactions and Alpine.js for UI state management.
*   All templates should extend `core/base.html` (but DO NOT include `base.html` code).
*   Achieve at least 80% test coverage with unit and integration tests.
*   Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
*   Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Task:** Identify the database tables and their columns from the ASP.NET code.

**Instructions:**
The ASP.NET code references `tblDG_Item_Master`, `tblDG_Category_Master`, and `tblDG_Location_Master`. It also uses `CompId` and `FinYearId` from session, implying related `Company` and `FinancialYear` tables.

*   **`tblDG_Item_Master`**: Main table for items (will be `Item` model).
    *   Columns inferred from `GridView2` and `Fillgrid` logic: `Id` (PK), `CId` (FK to Category), `PartNo`, `ItemCode`, `ManfDesc`, `UOMBasic`, `MinOrderQty`, `MinStockQty`, `StockQty`, `Location` (FK to Location), `Absolute`, `Excise`, `ImportLocal`, `OpeningBalDate`, `OpeningBalQty`, `UOMconv`, `CompId` (FK to Company), `FinYearId` (FK to FinancialYear).
*   **`tblDG_Category_Master`**: Stores item categories (will be `Category` model).
    *   Columns inferred: `CId` (PK), `Symbol`, `CName`.
*   **`tblDG_Location_Master`**: Stores item locations (will be `Location` model).
    *   Columns inferred: `Id` (PK), `LocationLabel`, `LocationNo`.
*   **`tblCompanyMaster`**: Stores company information (will be `Company` model). (Inferred placeholder name)
    *   Columns: `CompId` (PK), `CompName`.
*   **`tblFinancialYearMaster`**: Stores financial year information (will be `FinancialYear` model). (Inferred placeholder name)
    *   Columns: `FinYearId` (PK), `FinYearName`.

### Step 2: Identify Backend Functionality

**Task:** Determine the core operations and data flow.

**Instructions:**
The `RateRegister.aspx` primarily functions as a comprehensive **Read/List** page with advanced filtering capabilities.

*   **Read/List**: Displays a paginated list of items (`GridView2`).
*   **Filtering**:
    *   **Search Option (`RadioButtonList1`)**: Toggles between "Item Wise" (activates search panel) and "All" (redirects to a detail page).
    *   **Item Type (`DrpType`)**: Filters by "Category" or "WOItems". This dynamically changes the visibility and relevance of other search fields.
    *   **Category (`DrpCategory1`)**: Filters items by selected category, visible when `DrpType` is "Category".
    *   **Search By (`DrpSearchCode`)**: Allows searching by "Item Code", "Description", or "Location". Dynamically toggles visibility of `txtSearchItemCode` or `DropDownList3`.
    *   **Search Text (`txtSearchItemCode`)**: Text input for "Item Code" or "Description" search.
    *   **Location (`DropDownList3`)**: Dropdown for "Location" search, visible when `DrpType` is "Category" and `DrpSearchCode` is "Location".
    *   **Search Button (`btnSearch`)**: Triggers the data reload based on current filters.
*   **Pagination**: The `GridView2` supports pagination.
*   **Item Selection**: A "Select" link button (`lnkButton`) redirects to `RateRegister_Details.aspx` with the selected `ItemId`.
*   **No direct Create/Update/Delete**: This specific page does not directly support CRUD operations on `Item` records; it's a reporting/listing view that links to a detail page. However, for a complete `Item` module, standard CRUD operations will be provided in separate views.

### Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

*   **Layout**: Master page (`MasterPage.master`) with multiple `asp:Content` placeholders.
*   **Display**: `asp:Label` for messages (`lblmsg`), `asp:Panel` to conditionally show/hide the search form (`Panel1`).
*   **User Input**:
    *   `asp:RadioButtonList` (`RadioButtonList1`) for mode selection.
    *   Multiple `asp:DropDownList` controls (`DrpType`, `DrpCategory1`, `DrpSearchCode`, `DropDownList3`) for filtering.
    *   `asp:TextBox` (`txtSearchItemCode`) for free-text search.
*   **Actions**: `asp:Button` (`btnSearch`) for initiating search, `asp:LinkButton` within `GridView` for row selection.
*   **Data Display**: `asp:GridView` (`GridView2`) for tabular data presentation, including column definition and pagination settings.

---

### Step 4: Generate Django Code

We will structure this into a Django application, typically named `material_management` (derived from `Module_MaterialManagement`).

#### 4.1 Models (`material_management/models.py`)

**Task:** Create Django models mapping to the identified database tables, incorporating a "fat model" approach by adding business logic to managers or model methods.

**Instructions:**
Define models for `Item`, `Category`, `Location`, `Company`, and `FinancialYear`. Ensure `managed = False` and `db_table` are set for existing database tables. The complex filtering logic from `Fillgrid` in ASP.NET will be moved to a custom `ItemManager`.

```python
from django.db import models
from django.db.models import Q # Used for complex ORM queries

class Company(models.Model):
    """
    Represents the company entity, often retrieved from session data in ASP.NET.
    Assumed mapping to a tblCompanyMaster or similar.
    """
    id = models.IntegerField(primary_key=True, db_column='CompId')
    name = models.CharField(max_length=255, db_column='CompName', blank=True, null=True) 

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it exists)
        db_table = 'tblCompanyMaster' # Placeholder table name, adjust if known
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.name or f"Company {self.id}"

class FinancialYear(models.Model):
    """
    Represents the financial year, often retrieved from session data in ASP.NET.
    Assumed mapping to a tblFinancialYearMaster or similar.
    """
    id = models.IntegerField(primary_key=True, db_column='FinYearId')
    year_name = models.CharField(max_length=50, db_column='FinYearName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancialYearMaster' # Placeholder table name, adjust if known
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name or f"Financial Year {self.id}"

class Category(models.Model):
    """
    Represents the 'tblDG_Category_Master' table.
    """
    id = models.IntegerField(primary_key=True, db_column='CId')
    symbol = models.CharField(max_length=50, db_column='Symbol', blank=True, null=True)
    name = models.CharField(max_length=255, db_column='CName', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Category_Master'
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return f"{self.symbol or ''}-{self.name or ''}".strip('-')

class Location(models.Model):
    """
    Represents the 'tblDG_Location_Master' table.
    """
    id = models.IntegerField(primary_key=True, db_column='Id') # Assuming PK is Id
    label = models.CharField(max_length=255, db_column='LocationLabel', blank=True, null=True)
    number = models.CharField(max_length=50, db_column='LocationNo', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblDG_Location_Master'
        verbose_name = 'Location'
        verbose_name_plural = 'Locations'

    def __str__(self):
        return f"{self.label or ''}-{self.number or ''}".strip('-')

class ItemManager(models.Manager):
    """
    Custom manager for the Item model to encapsulate complex query logic
    for the Rate Register filtering.
    """
    def get_queryset(self):
        # Optimize queries by selecting related objects in advance
        return super().get_queryset().select_related('category', 'location', 'company', 'financial_year')

    def filter_for_rate_register(self, item_type, category_id, search_code_field, search_text, company_id, financial_year_id, location_id=None):
        """
        Applies filtering logic similar to the ASP.NET Fillgrid method.
        All business logic for fetching items resides here.
        """
        queryset = self.get_queryset()

        # Apply mandatory filters from ASP.NET session (CompId, FinYearId)
        # Note: financial_year_id <= FinYearId in ASP.NET code
        if company_id:
            queryset = queryset.filter(company_id=company_id)
        if financial_year_id:
            queryset = queryset.filter(financial_year_id__lte=financial_year_id)

        # Apply filtering based on DrpType selection
        if item_type == 'Category':
            if category_id and category_id != 'Select': # Ensure category_id is valid
                queryset = queryset.filter(category_id=category_id)

            if search_code_field and search_code_field != 'Select':
                # Apply search text/location based on selected search code
                if search_code_field == 'tblDG_Item_Master.ItemCode' and search_text:
                    queryset = queryset.filter(item_code__istartswith=search_text)
                elif search_code_field == 'tblDG_Item_Master.ManfDesc' and search_text:
                    queryset = queryset.filter(manf_desc__icontains=search_text)
                elif search_code_field == 'tblDG_Item_Master.Location' and location_id and location_id != 'Select':
                    queryset = queryset.filter(location_id=location_id)
            elif not search_code_field and search_text: # Fallback for `sd == "Select" && B == "Select" && s != string.Empty`
                queryset = queryset.filter(manf_desc__icontains=search_text)

        elif item_type == 'WOItems':
            if search_code_field and search_code_field != 'Select':
                if search_code_field == 'tblDG_Item_Master.ItemCode' and search_text:
                    queryset = queryset.filter(item_code__icontains=search_text)
                elif search_code_field == 'tblDG_Item_Master.ManfDesc' and search_text:
                    queryset = queryset.filter(manf_desc__icontains=search_text)
            elif not search_code_field and search_text: # Similar fallback
                 queryset = queryset.filter(manf_desc__icontains=search_text)
        
        # If item_type is 'Select' or no specific item-type filters are applied,
        # the initial queryset filtered by company/financial year is returned.
        # The ASP.NET redirect for "Select" is handled in the view.

        return queryset

class Item(models.Model):
    """
    Represents the 'tblDG_Item_Master' table.
    """
    id = models.IntegerField(primary_key=True, db_column='Id')
    category = models.ForeignKey(Category, on_delete=models.DO_NOTHING, db_column='CId', blank=True, null=True)
    part_no = models.CharField(max_length=255, db_column='PartNo', blank=True, null=True)
    item_code = models.CharField(max_length=255, db_column='ItemCode', blank=True, null=True)
    manf_desc = models.TextField(db_column='ManfDesc', blank=True, null=True)
    uom_basic = models.CharField(max_length=50, db_column='UOMBasic', blank=True, null=True)
    min_order_qty = models.DecimalField(max_digits=18, decimal_places=4, db_column='MinOrderQty', blank=True, null=True)
    min_stock_qty = models.DecimalField(max_digits=18, decimal_places=4, db_column='MinStockQty', blank=True, null=True)
    stock_qty = models.DecimalField(max_digits=18, decimal_places=4, db_column='StockQty', blank=True, null=True)
    location = models.ForeignKey(Location, on_delete=models.DO_NOTHING, db_column='Location', blank=True, null=True) # Assuming 'Location' column stores FK
    absolute = models.BooleanField(db_column='Absolute', blank=True, null=True)
    excise = models.CharField(max_length=50, db_column='Excise', blank=True, null=True)
    import_local = models.CharField(max_length=50, db_column='ImportLocal', blank=True, null=True)
    opening_bal_date = models.DateTimeField(db_column='OpeningBalDate', blank=True, null=True)
    opening_bal_qty = models.DecimalField(max_digits=18, decimal_places=4, db_column='OpeningBalQty', blank=True, null=True)
    uom_conv = models.CharField(max_length=50, db_column='UOMconv', blank=True, null=True)
    
    # Foreign keys for Company and Financial Year (from ASP.NET session context)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', blank=True, null=True)
    financial_year = models.ForeignKey(FinancialYear, on_delete=models.DO_NOTHING, db_column='FinYearId', blank=True, null=True)

    # Use the custom manager for enhanced querying
    objects = ItemManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_code or self.manf_desc or f"Item {self.id}"
    
    # Example business logic method (Fat Model)
    def is_in_stock(self):
        """Checks if the item has a positive stock quantity."""
        return self.stock_qty and self.stock_qty > 0

    def get_display_name(self):
        """Returns a user-friendly display name for the item."""
        return f"{self.item_code} - {self.manf_desc[:50]}..." if self.manf_desc else self.item_code

```

#### 4.2 Forms (`material_management/forms.py`)

**Task:** Define a Django form for the search parameters and a `ModelForm` for general Item CRUD operations.

**Instructions:**
Create `ItemSearchForm` to handle the various search inputs and an `ItemForm` for adding/editing item details. Use Tailwind CSS classes for styling through widgets. HTMX will be used for dynamic form re-rendering.

```python
from django import forms
from .models import Item, Category, Location

class ItemSearchForm(forms.Form):
    """
    Form to handle the search and filter options for the Rate Register.
    HTMX will be used to dynamically re-render parts of this form and the table.
    """
    ITEM_TYPE_CHOICES = [
        ('', 'Select'), 
        ('Category', 'Category'),
        ('WOItems', 'WO Items'),
    ]
    SEARCH_CODE_CHOICES = [
        ('', 'Select'), 
        ('tblDG_Item_Master.ItemCode', 'Item Code'),
        ('tblDG_Item_Master.ManfDesc', 'Description'),
        ('tblDG_Item_Master.Location', 'Location'),
    ]

    item_type = forms.ChoiceField(
        choices=ITEM_TYPE_CHOICES,
        required=False,
        label="Type",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '{% url "material_management:item_search_form_partial" %}', # HX request to re-render form
            'hx-target': '#searchFormContainer',
            'hx-swap': 'outerHTML',
            'hx-indicator': '#loading-indicator', # Show loading spinner
            'hx-trigger': 'change, load once' # Trigger on change and once on load
        })
    )
    category = forms.ModelChoiceField(
        queryset=Category.objects.all().order_by('name'),
        required=False,
        empty_label="Select",
        label="Category",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-trigger': 'change', # Only updates table
            'hx-get': '{% url "material_management:item_table_partial" %}', 
            'hx-target': '#itemTable-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#loading-indicator',
            'hx-params': 'form', # Send all form fields
        })
    )
    search_code = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        label="Search By",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '{% url "material_management:item_search_form_partial" %}', # Re-render form
            'hx-target': '#searchFormContainer',
            'hx-swap': 'outerHTML',
            'hx-indicator': '#loading-indicator',
            'hx-trigger': 'change'
        })
    )
    search_text = forms.CharField(
        max_length=255,
        required=False,
        label="Search Text",
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Enter search text...'
        })
    )
    location = forms.ModelChoiceField(
        queryset=Location.objects.all().order_by('label'),
        required=False,
        empty_label="Select",
        label="Location",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-trigger': 'change',
            'hx-get': '{% url "material_management:item_table_partial" %}',
            'hx-target': '#itemTable-container',
            'hx-swap': 'innerHTML',
            'hx-indicator': '#loading-indicator',
            'hx-params': 'form', # Send all form fields
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Apply conditional visibility logic for fields based on initial/post data
        # This will be used in the template to show/hide fields, or the partial view
        # will only render the necessary fields.
        item_type = self.initial.get('item_type') or (self.data.get('item_type') if self.is_bound else None)
        search_code = self.initial.get('search_code') or (self.data.get('search_code') if self.is_bound else None)

        self.fields['category'].required = False
        self.fields['search_code'].required = False
        self.fields['search_text'].required = False
        self.fields['location'].required = False
        
        # Initial visibility settings for the template rendering logic
        self.show_category = False
        self.show_search_code = False
        self.show_search_text = False
        self.show_location = False

        if item_type == 'Category':
            self.show_category = True
            self.show_search_code = True
            if search_code == 'tblDG_Item_Master.Location':
                self.show_location = True
            else:
                self.show_search_text = True
        elif item_type == 'WOItems':
            self.show_search_code = True
            self.show_search_text = True # txtSearchItemCode was always visible for WOItems
            
        # Ensure search_text is visible even if search_code isn't selected, if item_type is WOItems
        if item_type == 'WOItems' and not search_code:
             self.show_search_text = True


class ItemForm(forms.ModelForm):
    """
    Standard ModelForm for creating and updating Item instances.
    Used for modal forms (Add/Edit).
    """
    class Meta:
        model = Item
        fields = ['category', 'part_no', 'item_code', 'manf_desc', 'uom_basic', 
                  'min_order_qty', 'min_stock_qty', 'stock_qty', 'location', 
                  'absolute', 'excise', 'import_local', 'opening_bal_date', 
                  'opening_bal_qty', 'uom_conv']
        widgets = {
            'category': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'part_no': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'item_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'manf_desc': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
            'uom_basic': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'min_order_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'min_stock_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'stock_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'location': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'absolute': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded'}),
            'excise': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'import_local': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'opening_bal_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'opening_bal_qty': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'uom_conv': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }

```

#### 4.3 Views (`material_management/views.py`)

**Task:** Implement the list, search, and CRUD operations using Django Class-Based Views. Ensure views remain thin by delegating complex logic to models.

**Instructions:**
Create `RateRegisterListView` for the main page, `ItemTablePartialView` for HTMX-driven table updates, and `ItemSearchFormPartialView` for dynamic form rendering. Also, include standard `CreateView`, `UpdateView`, `DeleteView` for `Item` management.

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, HttpResponseRedirect
from django.shortcuts import render
from .models import Item, Category, Location, Company, FinancialYear # Import necessary models
from .forms import ItemForm, ItemSearchForm
import secrets # For generating random keys, similar to fun.GetRandomAlphaNumeric()

class RateRegisterListView(ListView):
    """
    Main view for the Rate Register, displaying the search form and item list.
    Corresponds to RateRegister.aspx's main functionality (Item Wise).
    """
    model = Item
    template_name = 'material_management/item/rate_register_list.html'
    context_object_name = 'items'
    paginate_by = 20 # Same as ASP.NET GridView PageSize

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Pass the search form to the template
        # Initialize form with data from GET parameters for persistent state
        search_form = ItemSearchForm(self.request.GET)
        context['search_form'] = search_form
        context['categories'] = Category.objects.all().order_by('name')
        context['locations'] = Location.objects.all().order_by('label')
        context['message'] = self.request.session.pop('message', None) # For lblmsg
        return context

    def get_queryset(self):
        # Retrieve CompId and FinYearId from session (simulating ASP.NET behavior)
        # In a real Django app, consider authentication/user profiles for these
        company_id = self.request.session.get('compid', 1)  # Default to 1 if not found
        financial_year_id = self.request.session.get('finyear', 2024) # Default to 2024

        # Initialize filter parameters from request GET
        item_type = self.request.GET.get('item_type')
        category_id = self.request.GET.get('category')
        search_code_field = self.request.GET.get('search_code')
        search_text = self.request.GET.get('search_text')
        location_id = self.request.GET.get('location')

        # Use ItemManager's method to apply all filtering logic
        queryset = Item.objects.filter_for_rate_register(
            item_type, category_id, search_code_field, search_text, 
            company_id, financial_year_id, location_id
        )
        return queryset.order_by('item_code') # Default sorting

class ItemTablePartialView(ListView):
    """
    HTMX view to render only the DataTables portion of the item list.
    Triggered by search form changes or page refresh events.
    """
    model = Item
    template_name = 'material_management/item/_item_table.html'
    context_object_name = 'items'
    paginate_by = 20 # Same as ASP.NET GridView PageSize

    def get_queryset(self):
        # Retrieve CompId and FinYearId from session
        company_id = self.request.session.get('compid', 1)
        financial_year_id = self.request.session.get('finyear', 2024)

        # Retrieve filter parameters from request GET
        item_type = self.request.GET.get('item_type')
        category_id = self.request.GET.get('category')
        search_code_field = self.request.GET.get('search_code')
        search_text = self.request.GET.get('search_text')
        location_id = self.request.GET.get('location')

        # Use ItemManager's method for filtering
        queryset = Item.objects.filter_for_rate_register(
            item_type, category_id, search_code_field, search_text, 
            company_id, financial_year_id, location_id
        )
        return queryset.order_by('item_code')

    def render_to_response(self, context, **response_kwargs):
        # Ensure DataTables reinitializes by including the script block
        response = super().render_to_response(context, **response_kwargs)
        return response

class ItemSearchFormPartialView(View):
    """
    HTMX view to render only the dynamic search form panel.
    Triggered by changes in 'item_type' or 'search_code' dropdowns.
    """
    def get(self, request, *args, **kwargs):
        form = ItemSearchForm(request.GET)
        context = {
            'search_form': form,
            'categories': Category.objects.all().order_by('name'),
            'locations': Location.objects.all().order_by('label'),
        }
        return render(request, 'material_management/item/_item_search_form.html', context)


# --- Standard Item CRUD Views (for general Item management, not directly from RateRegister.aspx) ---

class ItemCreateView(CreateView):
    model = Item
    form_class = ItemForm
    template_name = 'material_management/item/_item_form.html'
    success_url = reverse_lazy('material_management:item_list') # Redirect to general item list

    def form_valid(self, form):
        # Set session-based fields if not already set by form
        # In a real app, these might come from authenticated user profile
        form.instance.company_id = self.request.session.get('compid', 1) 
        form.instance.financial_year_id = self.request.session.get('finyear', 2024)
        
        response = super().form_valid(form)
        messages.success(self.request, 'Item added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, tells HTMX to do nothing but process headers
                headers={
                    'HX-Trigger': 'refreshItemList' # Custom event to refresh the table
                }
            )
        return response
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Add New Item'
        return context


class ItemUpdateView(UpdateView):
    model = Item
    form_class = ItemForm
    template_name = 'material_management/item/_item_form.html'
    success_url = reverse_lazy('material_management:item_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemList'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'Edit Item'
        return context


class ItemDeleteView(DeleteView):
    model = Item
    template_name = 'material_management/item/_item_confirm_delete.html'
    success_url = reverse_lazy('material_management:item_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemList'
                }
            )
        return response

# Example for RateRegister_Details.aspx redirection
class RateRegisterDetailView(View):
    """
    Simulates the redirection logic for RateRegister_Details.aspx.
    This would typically be a separate module's view.
    """
    def get(self, request, *args, **kwargs):
        item_id = kwargs.get('pk') # From path('<int:pk>/details')
        # Here, you would redirect to a detailed view of the item,
        # or load an entirely different module/page.
        # This is a placeholder for `Response.Redirect("~/Module/MaterialManagement/Reports/RateRegister_Details.aspx?ItemId=" + Id + "&Key=" + Key + "")`
        
        # Example redirect to a hypothetical item detail page
        random_key = secrets.token_urlsafe(16) # Simulate GetRandomAlphaNumeric
        return HttpResponseRedirect(reverse_lazy('material_management:item_detail', args=[item_id]) + f'?key={random_key}')

```

#### 4.4 Templates (`material_management/item/`)

**Task:** Create templates for the Rate Register list view, dynamic search form, DataTables partial, and modal CRUD forms.

**Instructions:**
Ensure templates extend `core/base.html`, use DataTables for item lists, HTMX for dynamic updates, and Alpine.js for UI state.

**`rate_register_list.html`** (Main Rate Register page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Rate Register</h2>
        <div class="flex items-center space-x-4">
            <span class="font-bold">Search Option :</span>
            <div x-data="{ selectedOption: '0' }" class="flex space-x-4">
                <label class="inline-flex items-center">
                    <input type="radio" x-model="selectedOption" value="0" name="search_mode" class="form-radio text-indigo-600 h-4 w-4">
                    <span class="ml-2 font-bold">Item Wise</span>
                </label>
                <label class="inline-flex items-center">
                    <input type="radio" x-model="selectedOption" value="1" name="search_mode" class="form-radio text-indigo-600 h-4 w-4">
                    <span class="ml-2 font-bold">All</span>
                </label>
            </div>
            
            <a x-show="selectedOption === '1'" 
               href="{% url 'material_management:item_detail_list' %}" 
               class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                View All Items
            </a>
        </div>
    </div>

    {% if message %}
    <p class="font-bold text-red-500 mb-4">{{ message }}</p>
    {% endif %}

    <div id="itemWisePanel" x-data="{ showPanel: true }" x-show="showPanel">
        <div id="searchFormContainer"
             hx-trigger="load, reloadSearchForm from:body"
             hx-get="{% url 'material_management:item_search_form_partial' %}?{{ request.GET.urlencode }}"
             hx-swap="outerHTML">
             <!-- Search form will be loaded here via HTMX -->
             <div class="text-center py-8">
                 <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                 <p class="mt-2">Loading search form...</p>
             </div>
        </div>

        <div class="mt-6" id="itemTable-container"
             hx-trigger="load, refreshItemList from:body"
             hx-get="{% url 'material_management:item_table_partial' %}?{{ request.GET.urlencode }}"
             hx-swap="innerHTML">
            <!-- DataTable will be loaded here via HTMX -->
            <div class="text-center py-8">
                <div id="loading-indicator" class="htmx-indicator inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 htmx-indicator">Loading items...</p>
            </div>
        </div>
    </div>

    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state management
    });

    // Handle initial state of radio buttons and panel visibility if needed
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const searchMode = urlParams.get('search_mode');
        if (searchMode === '1') {
             // If "All" was selected on a previous load, ensure panel is hidden
             document.getElementById('itemWisePanel').setAttribute('x-show', 'false');
        } else {
             document.getElementById('itemWisePanel').setAttribute('x-show', 'true');
        }

        // Add a global event listener for message display
        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const message = evt.detail.xhr.getResponseHeader('HX-Trigger-After-Settle');
            if (message && message.includes('showToast')) {
                // Parse message and display using a toast component
                // For simplicity, directly update a message div for now
                const msgDiv = document.getElementById('lblmsg'); // Assuming a div for messages
                if (msgDiv) {
                    const msgData = JSON.parse(message).showToast;
                    msgDiv.innerText = msgData.message;
                    msgDiv.style.color = 'red'; // Example
                    // Clear after some time if needed
                    setTimeout(() => msgDiv.innerText = '', 5000);
                }
            }
        });
    });
</script>
{% endblock %}
```

**`_item_search_form.html`** (Partial for dynamic search panel)

```html
<div id="searchFormContainer">
    <div class="mb-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
            <div>
                <label for="{{ search_form.item_type.id_for_label }}" class="block text-sm font-medium text-gray-700">Type:</label>
                {{ search_form.item_type }}
            </div>

            {% if search_form.show_category %}
            <div>
                <label for="{{ search_form.category.id_for_label }}" class="block text-sm font-medium text-gray-700">Category:</label>
                {{ search_form.category }}
            </div>
            {% else %}
            <div class="md:col-span-1"></div> {# Placeholder to maintain grid layout #}
            {% endif %}

            {% if search_form.show_search_code %}
            <div>
                <label for="{{ search_form.search_code.id_for_label }}" class="block text-sm font-medium text-gray-700">Search By:</label>
                {{ search_form.search_code }}
            </div>
            {% else %}
            <div class="md:col-span-1"></div>
            {% endif %}

            {% if search_form.show_search_text %}
            <div>
                <label for="{{ search_form.search_text.id_for_label }}" class="block text-sm font-medium text-gray-700">Search Text:</label>
                {{ search_form.search_text }}
            </div>
            {% elif search_form.show_location %}
            <div>
                <label for="{{ search_form.location.id_for_label }}" class="block text-sm font-medium text-gray-700">Location:</label>
                {{ search_form.location }}
            </div>
            {% else %}
            <div class="md:col-span-1"></div>
            {% endif %}
        </div>
        <div class="mt-4 flex justify-end">
             <button
                 type="button"
                 class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                 hx-get="{% url 'material_management:item_table_partial' %}"
                 hx-target="#itemTable-container"
                 hx-swap="innerHTML"
                 hx-params="form" {# Send all form fields when button is clicked #}
                 hx-indicator="#loading-indicator">
                 Search
             </button>
        </div>
    </div>
</div>
```

**`_item_table.html`** (Partial for DataTable)

```html
<div class="overflow-x-auto shadow-md sm:rounded-lg">
    <table id="itemTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manf Desc</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Excise</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Import/Local</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Open Bal Date</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Opening Bal Qty</th>
                <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in items %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.category.name|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.item_code|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200">{{ obj.manf_desc|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.uom_basic|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.stock_qty|default:"0" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.location|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.excise|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.import_local|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">{{ obj.opening_bal_date|date:"Y-m-d"|default:"N/A" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-right">{{ obj.opening_bal_qty|default:"0" }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <button 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'material_management:item_detail' obj.pk %}"
                        hx-target="body" {# Redirect as per ASP.NET LinkButton behavior #}
                        hx-push-url="true"
                        hx-indicator="#loading-indicator">
                        Select
                    </button>
                    {# Example CRUD buttons, not directly from RateRegister.aspx, but good practice #}
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'material_management:item_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'material_management:item_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="12" class="py-4 text-center text-gray-500 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization script. Re-initialize on HTMX swap.
    $(document).ready(function() {
        $('#itemTable').DataTable({
            "pageLength": 10, // Default page size for DataTables
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true // Make table responsive
        });
    });
</script>
```

**`_item_form.html`** (Partial for Add/Edit Item Modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Item</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#modal-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
                <span id="modal-loading-indicator" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </div>
    </form>
</div>
```

**`_item_confirm_delete.html`** (Partial for Delete Item Modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Delete</h3>
    <p class="text-gray-700">Are you sure you want to delete "<strong>{{ object.get_display_name }}</strong>"?</p>
    <p class="text-red-600 font-semibold mt-2">This action cannot be undone.</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <form hx-post="{% url 'material_management:item_delete' object.pk %}" hx-swap="none" hx-indicator="#modal-loading-indicator-delete">
            {% csrf_token %}
            <button 
                type="submit" 
                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
                <span id="modal-loading-indicator-delete" class="htmx-indicator ml-2">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                </span>
            </button>
        </form>
    </div>
</div>
```

#### 4.5 URLs (`material_management/urls.py`)

**Task:** Define URL patterns for all views, including HTMX-specific endpoints.

**Instructions:**
Use a consistent naming convention and define paths for the main list, partials, and CRUD operations.

```python
from django.urls import path
from .views import (
    RateRegisterListView, ItemTablePartialView, ItemSearchFormPartialView,
    ItemCreateView, ItemUpdateView, ItemDeleteView, RateRegisterDetailView
)

app_name = 'material_management' # Namespace for the app

urlpatterns = [
    # Main Rate Register List View
    path('rate-register/', RateRegisterListView.as_view(), name='rate_register_list'),
    
    # HTMX partial views for dynamic updates
    path('rate-register/table/', ItemTablePartialView.as_view(), name='item_table_partial'),
    path('rate-register/search-form/', ItemSearchFormPartialView.as_view(), name='item_search_form_partial'),

    # Standard Item CRUD operations (for general item management)
    # These are typically not directly on the RateRegister.aspx page itself,
    # but are provided for complete Item management.
    path('items/add/', ItemCreateView.as_view(), name='item_add'),
    path('items/edit/<int:pk>/', ItemUpdateView.as_view(), name='item_edit'),
    path('items/delete/<int:pk>/', ItemDeleteView.as_view(), name='item_delete'),

    # Placeholder for RateRegister_Details.aspx redirection
    # This URL would lead to a comprehensive item detail page/module
    path('items/<int:pk>/details/', RateRegisterDetailView.as_view(), name='item_detail'),
    path('items/all-details/', RateRegisterDetailView.as_view(), name='item_detail_list'), # For "All" option
]
```

#### 4.6 Tests (`material_management/tests.py`)

**Task:** Write comprehensive unit tests for models and integration tests for views to ensure functionality and maintainability.

**Instructions:**
Include tests for model methods, manager logic, and all view interactions (GET, POST, HTMX requests), aiming for high test coverage.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import Item, Category, Location, Company, FinancialYear
from .forms import ItemForm, ItemSearchForm

class ModelTestBase(TestCase):
    """Base class for setting up common test data."""
    @classmethod
    def setUpTestData(cls):
        # Create necessary related objects first
        cls.company = Company.objects.create(id=1, name='Test Company')
        cls.financial_year = FinancialYear.objects.create(id=2024, year_name='2024-2025')
        cls.category_a = Category.objects.create(id=101, symbol='CAT-A', name='Category Alpha')
        cls.category_b = Category.objects.create(id=102, symbol='CAT-B', name='Category Beta')
        cls.location_main = Location.objects.create(id=201, label='Main', number='001')
        cls.location_aux = Location.objects.create(id=202, label='Aux', number='002')

        # Create test Item data
        Item.objects.create(
            id=1,
            company=cls.company,
            financial_year=cls.financial_year,
            category=cls.category_a,
            item_code='ITEM001',
            manf_desc='Widget Alpha',
            uom_basic='PCS',
            stock_qty=100,
            location=cls.location_main,
            excise='EXC1',
            import_local='Local',
            opening_bal_date='2023-01-01',
            opening_bal_qty=50
        )
        Item.objects.create(
            id=2,
            company=cls.company,
            financial_year=cls.financial_year,
            category=cls.category_b,
            item_code='ITEM002',
            manf_desc='Gadget Beta',
            uom_basic='KGS',
            stock_qty=200,
            location=cls.location_aux,
            excise='EXC2',
            import_local='Import',
            opening_bal_date='2023-02-01',
            opening_bal_qty=100
        )
        Item.objects.create(
            id=3,
            company=cls.company,
            financial_year=FinancialYear.objects.create(id=2023, year_name='2023-2024'), # Older financial year
            category=cls.category_a,
            item_code='ITEM003',
            manf_desc='Tool Gamma',
            uom_basic='UNIT',
            stock_qty=50,
            location=cls.location_main,
            excise='EXC1',
            import_local='Local',
            opening_bal_date='2022-12-01',
            opening_bal_qty=20
        )

class ItemModelTest(ModelTestBase):
    def test_item_creation(self):
        item = Item.objects.get(id=1)
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.category.name, 'Category Alpha')
        self.assertEqual(item.location.label, 'Main')
        self.assertEqual(item.company.name, 'Test Company')
        self.assertEqual(item.financial_year.year_name, '2024-2025')
        
    def test_item_str_method(self):
        item = Item.objects.get(id=1)
        self.assertEqual(str(item), 'ITEM001 - Widget Alpha...')
        
    def test_item_is_in_stock(self):
        item = Item.objects.get(id=1)
        self.assertTrue(item.is_in_stock())
        item.stock_qty = 0
        self.assertFalse(item.is_in_stock())
        item.stock_qty = -5
        self.assertFalse(item.is_in_stock())
        item.stock_qty = None
        self.assertFalse(item.is_in_stock())

    def test_item_manager_filter_basic(self):
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.financial_year.id
        
        # Test basic filtering by company and financial year
        items = Item.objects.filter_for_rate_register(
            item_type=None, category_id=None, search_code_field=None, search_text=None,
            company_id=self.company.id, financial_year_id=self.financial_year.id
        )
        self.assertEqual(items.count(), 2) # ITEM001, ITEM002 (ITEM003 is older fin_year)
        self.assertIn(Item.objects.get(id=1), items)
        self.assertIn(Item.objects.get(id=2), items)
        self.assertNotIn(Item.objects.get(id=3), items)

    def test_item_manager_filter_category_type(self):
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.financial_year.id

        items = Item.objects.filter_for_rate_register(
            item_type='Category', category_id=self.category_a.id, 
            search_code_field=None, search_text=None,
            company_id=self.company.id, financial_year_id=self.financial_year.id
        )
        self.assertEqual(items.count(), 1) # Only ITEM001 is in category_a and current fin_year
        self.assertEqual(items.first().item_code, 'ITEM001')

    def test_item_manager_filter_item_code_search(self):
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.financial_year.id

        items = Item.objects.filter_for_rate_register(
            item_type='Category', category_id=None, 
            search_code_field='tblDG_Item_Master.ItemCode', search_text='ITEM00',
            company_id=self.company.id, financial_year_id=self.financial_year.id
        )
        self.assertEqual(items.count(), 2) # ITEM001, ITEM002
        self.assertIn(Item.objects.get(id=1), items)
        self.assertIn(Item.objects.get(id=2), items)

    def test_item_manager_filter_manf_desc_search(self):
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.financial_year.id

        items = Item.objects.filter_for_rate_register(
            item_type='WOItems', category_id=None, 
            search_code_field='tblDG_Item_Master.ManfDesc', search_text='Widget',
            company_id=self.company.id, financial_year_id=self.financial_year.id
        )
        self.assertEqual(items.count(), 1)
        self.assertEqual(items.first().item_code, 'ITEM001')

    def test_item_manager_filter_location_search(self):
        self.client.session['compid'] = self.company.id
        self.client.session['finyear'] = self.financial_year.id

        items = Item.objects.filter_for_rate_register(
            item_type='Category', category_id=None, 
            search_code_field='tblDG_Item_Master.Location', search_text=None,
            location_id=self.location_main.id,
            company_id=self.company.id, financial_year_id=self.financial_year.id
        )
        self.assertEqual(items.count(), 1) # ITEM001
        self.assertEqual(items.first().item_code, 'ITEM001')


class ItemViewsTest(ModelTestBase):
    def setUp(self):
        super().setUp()
        self.client = Client()
        # Set session data for each test, as middleware won't run in test client setup
        session = self.client.session
        session['compid'] = self.company.id
        session['finyear'] = self.financial_year.id
        session.save()
    
    def test_rate_register_list_view_get(self):
        response = self.client.get(reverse('material_management:rate_register_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/item/rate_register_list.html')
        self.assertIn('items', response.context)
        self.assertIn('search_form', response.context)
        self.assertEqual(response.context['items'].count(), 2) # Initial filter by compid/finyear

    def test_item_table_partial_view_get(self):
        response = self.client.get(reverse('material_management:item_table_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/item/_item_table.html')
        self.assertIn('items', response.context)
        self.assertEqual(response.context['items'].count(), 2)

    def test_item_table_partial_view_filter_category(self):
        response = self.client.get(reverse('material_management:item_table_partial'), {
            'item_type': 'Category',
            'category': self.category_a.id
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.context['items'].count(), 1)
        self.assertEqual(response.context['items'].first().item_code, 'ITEM001')

    def test_item_search_form_partial_view_get(self):
        response = self.client.get(reverse('material_management:item_search_form_partial'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/item/_item_search_form.html')
        self.assertIn('search_form', response.context)

    def test_item_search_form_partial_view_dynamic_fields(self):
        # Test when item_type is 'Category'
        response = self.client.get(reverse('material_management:item_search_form_partial'), {
            'item_type': 'Category',
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['search_form'].show_category)
        self.assertTrue(response.context['search_form'].show_search_code)
        self.assertTrue(response.context['search_form'].show_search_text) # Default for search_code 'Select'

        # Test when item_type is 'Category' and search_code is 'Location'
        response = self.client.get(reverse('material_management:item_search_form_partial'), {
            'item_type': 'Category',
            'search_code': 'tblDG_Item_Master.Location'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.context['search_form'].show_location)
        self.assertFalse(response.context['search_form'].show_search_text)

    # --- Item CRUD Views Tests ---

    def test_item_create_view_get(self):
        response = self.client.get(reverse('material_management:item_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/item/_item_form.html')
        self.assertIn('form', response.context)
        
    def test_item_create_view_post_success(self):
        data = {
            'category': self.category_a.id,
            'item_code': 'NEWITEM',
            'manf_desc': 'New Product Description',
            'uom_basic': 'PCS',
            'stock_qty': 50,
            'location': self.location_main.id,
            'excise': 'EXC0',
            'import_local': 'Local',
            'opening_bal_date': '2024-01-01',
            'opening_bal_qty': 10
        }
        response = self.client.post(reverse('material_management:item_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success
        self.assertIn('HX-Trigger', response.headers)
        self.assertContains(response, 'refreshItemList', status_code=204) # check for HX-Trigger: refreshItemList
        self.assertTrue(Item.objects.filter(item_code='NEWITEM').exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Item added successfully.')

    def test_item_update_view_get(self):
        item = Item.objects.get(id=1)
        response = self.client.get(reverse('material_management:item_edit', args=[item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/item/_item_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance.item_code, 'ITEM001')

    def test_item_update_view_post_success(self):
        item = Item.objects.get(id=1)
        data = {
            'category': self.category_a.id,
            'item_code': 'UPDATEDITEM',
            'manf_desc': 'Updated Description',
            'uom_basic': 'PCS',
            'stock_qty': 110,
            'location': self.location_main.id,
            'excise': 'EXC1',
            'import_local': 'Local',
            'opening_bal_date': '2023-01-01',
            'opening_bal_qty': 50
        }
        response = self.client.post(reverse('material_management:item_edit', args=[item.id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        item.refresh_from_db()
        self.assertEqual(item.item_code, 'UPDATEDITEM')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Item updated successfully.')

    def test_item_delete_view_get(self):
        item = Item.objects.get(id=1)
        response = self.client.get(reverse('material_management:item_delete', args=[item.id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/item/_item_confirm_delete.html')
        self.assertIn('object', response.context)

    def test_item_delete_view_post_success(self):
        item_to_delete = Item.objects.get(id=1)
        response = self.client.post(reverse('material_management:item_delete', args=[item_to_delete.id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(Item.objects.filter(id=item_to_delete.id).exists())
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(str(messages[0]), 'Item deleted successfully.')

    def test_rate_register_detail_view_redirect(self):
        item = Item.objects.get(id=1)
        response = self.client.get(reverse('material_management:item_detail', args=[item.id]))
        self.assertEqual(response.status_code, 302) # Expect redirect
        self.assertTrue(response.url.startswith(reverse('material_management:item_detail', args=[item.id])))
        self.assertIn('key=', response.url) # Check for the random key

```

---

### Step 5: HTMX and Alpine.js Integration

**Instructions:**
The generated Django templates and views are already designed for HTMX and Alpine.js.

*   **HTMX for Dynamic Updates**:
    *   The `item_type` and `search_code` dropdowns in `_item_search_form.html` use `hx-get` to re-render the `_item_search_form.html` itself (`item_search_form_partial` URL) when changed, allowing server-side logic to determine which fields are visible.
    *   The `category`, `search_code`, `location` dropdowns, and the `Search` button in `_item_search_form.html` use `hx-get` to refresh the `_item_table.html` partial (`item_table_partial` URL) when changed.
    *   `hx-trigger="load, refreshItemList from:body"` on the table container ensures the table loads initially and refreshes after any item CRUD operations (triggered by the `HX-Trigger` header from views).
    *   Modal forms for CRUD (`_item_form.html`, `_item_confirm_delete.html`) use `hx-post` for submission, and `hx-swap="none"` with `HX-Trigger` headers for event-driven updates.
    *   Loading indicators (`htmx-indicator`) are used to provide visual feedback during HTMX requests.

*   **Alpine.js for UI State**:
    *   `rate_register_list.html` uses `x-data` and `x-show` to control the visibility of the "Item Wise" panel based on the `RadioButtonList1` equivalent (simulated by Alpine.js `selectedOption`).
    *   The modal (`#modal`) uses Alpine.js for showing/hiding (`x-show`) and closing on outside clicks.

*   **DataTables for List Views**:
    *   The `_item_table.html` partial contains the `<table id="itemTable">` element.
    *   A `<script>` block within `_item_table.html` initializes DataTables on `document.ready()`. Since this partial is reloaded by HTMX, the `$(document).ready()` inside the partial ensures DataTables re-initializes correctly after each HTMX swap.
    *   DataTables provides client-side searching, sorting, and pagination without requiring server-side reloads, improving performance significantly.

*   **HTMX-only Interactions**: All dynamic behaviors are managed through HTMX requests and responses, minimizing the need for custom JavaScript outside of Alpine.js for UI state and DataTables initialization.

---

### Final Notes

*   **Placeholders**: Remember to replace placeholder table names (`tblCompanyMaster`, `tblFinancialYearMaster`) with your actual database table names if they differ.
*   **Authentication/Session Management**: The ASP.NET application heavily relies on `Session["compid"]` and `Session["finyear"]`. In Django, these would typically be derived from the authenticated user's profile or selected context. The provided code assumes these are populated into the Django `request.session` for testing purposes. A full migration would involve mapping your ASP.NET authentication system to Django's or an external identity provider.
*   **Error Handling and Messaging**: `lblmsg` in ASP.NET is handled by Django's `messages` framework, which is then displayed in `rate_register_list.html`. HTMX responses use `HX-Trigger` to inform the client of messages or successful actions.
*   **Data Migration**: This plan focuses on code modernization. A separate step would be needed to migrate your actual data from SQL Server (or other database) to a compatible format for Django (e.g., PostgreSQL, MySQL) or ensure Django connects directly to the existing SQL Server if preferred.
*   **CSS and Styling**: The provided templates use Tailwind CSS classes, assuming you have Tailwind CSS configured in your Django project.
*   **Master Page Conversion**: The `MasterPage.master` conversion is implicitly handled by extending `core/base.html`, which is assumed to contain common layout, header, footer, and global CSS/JS imports.

This comprehensive plan provides a clear roadmap for migrating your ASP.NET `RateRegister` module to a modern, efficient, and maintainable Django application using AI-assisted automation principles.