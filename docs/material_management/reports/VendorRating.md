The following modernization plan outlines the strategic transition of your ASP.NET application to a modern Django-based solution. This approach leverages AI-assisted automation, focuses on business benefits, and provides a clear roadmap for implementation through conversational AI guidance.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

**Business Value:** Understanding the existing data structure is the foundation for building a robust and scalable new system. This step ensures that all critical business data is accurately represented in the new Django application, maintaining data integrity.

**Analysis:**
The ASP.NET code interacts with at least three primary tables: `tblMM_Supplier_master`, `tblFinancial_master`, and `tblHR_OfficeStaff`.

*   **`tblMM_Supplier_master` (Main Entity: Supplier)**
    *   `SupplierId`: Primary Key (inferred from `DataKeyNames="SupplierId"` in GridView and usage in SQL query).
    *   `SupplierName`: Text field.
    *   `SysDate`: Date/Time field (stored as `varchar` but parsed to `datetime` in ASP.NET, ideally `DateField` in Django).
    *   `FinYearId`: Foreign Key to `tblFinancial_master`.
    *   `SessionId`: Foreign Key to `tblHR_OfficeStaff` (representing `EmpId`).
    *   `CompId`: Company ID (inferred from `Session["compid"]`, common across tables).

*   **`tblFinancial_master` (Lookup Entity: Financial Year)**
    *   `FinYearId`: Primary Key.
    *   `FinYear`: Text field (e.g., "2023-24").
    *   `FinYearFrom`: Date field.
    *   `FinYearTo`: Date field.
    *   `CompId`: Company ID.

*   **`tblHR_OfficeStaff` (Lookup Entity: Employee/Staff)**
    *   `EmpId`: Primary Key (inferred from `SessionId` mapping).
    *   `EmployeeName`: Text field.
    *   `CompId`: Company ID.

### Step 2: Identify Backend Functionality

**Business Value:** This step clarifies the actions and reports critical to the business. By replicating these precisely, we ensure that users can perform the same operations and access the same valuable insights as before, but with improved performance and reliability.

**Analysis:**

*   **Read (List & Filter):**
    *   The `SearchGridView1` displays a list of suppliers with their financial year, generation date, and generator.
    *   **Filtering:**
        *   **Date Range:** `TxtFromDate` and `TxtToDate` (for Supplier Wise) and `TxtFromDate1`, `TxtToDate1` (for Overall Rating).
        *   **Item Category:** `DrpType` dropdown ("BoughtOut", "WO Items").
        *   **Supplier Name:** `TxtSearchValue` with an autocomplete feature.
    *   **Pagination:** The `GridView` supports pagination via `SearchGridView1_PageIndexChanging`.

*   **Navigation/Reporting:**
    *   **Overall Rating Report:** `BtnSearch` on the "Overall rating" tab redirects to `OverallRating.aspx` with selected dates.
    *   **Supplier Wise Report:** The "Select" `LinkButton` within `SearchGridView1` redirects to `VendorRating_Print.aspx` with supplier code, dates, and item category.

*   **Autocomplete Service:**
    *   A `WebMethod` `sql` provides supplier names and IDs for the autocomplete functionality, filtering by `prefixText`.

*   **No Direct Create/Update/Delete:**
    *   The provided ASP.NET code (`VendorRating.aspx`) does *not* directly perform CRUD operations on `tblMM_Supplier_master` for *this specific page's* functionality. It's primarily a reporting/search interface.
    *   **However, as per the prompt's instruction, we will generate dummy CRUD operations for the `Supplier` model to demonstrate the general migration pattern.**

### Step 3: Infer UI Components

**Business Value:** Translating existing UI components ensures a familiar user experience while upgrading the underlying technology. Modernizing with HTMX and Alpine.js provides a snappier, more responsive interface without complex JavaScript development.

**Analysis:**

*   **Tabbed Interface:** `cc1:TabContainer` with `Overallrating` and `SupplierWise` panels. This will be replaced by Django templates, possibly controlled by a URL parameter or simple Alpine.js state for local tab switching.
*   **Date Inputs:** `asp:TextBox` with `cc1:CalendarExtender`. These will become standard HTML `input type="date"` fields, potentially enhanced by Alpine.js for a more sophisticated date picker UI.
*   **Text Input:** `asp:TextBox` for supplier name (`TxtSearchValue`). This will be an `input type="text"`, enhanced with HTMX for autocomplete.
*   **Dropdown:** `asp:DropDownList` for `DrpType`. This maps directly to an HTML `select` element.
*   **Buttons:** `asp:Button` for "Submit" and "Search". These will trigger HTMX requests for form submissions or redirects.
*   **Data Display:** `asp:GridView` (`SearchGridView1`). This will be replaced by a `<table>` element rendered with Django's template engine and enhanced on the client-side using DataTables.js for pagination, sorting, and client-side filtering.
*   **Hidden Fields:** `asp:HiddenField` (`hfSearchText`, `hfSort`). These typically manage client-side state or server-side data passing. In Django + HTMX, much of this is handled by HTMX request parameters or Alpine.js state.

### Step 4: Generate Django Code

For this modernization, we will create a Django application named `material_management`.

#### 4.1 Models (`material_management/models.py`)

**Business Value:** Models are the core of your data. By precisely mapping existing database tables, we ensure that your Django application interacts correctly with your current data, preserving historical information and business logic. The "fat model" approach centralizes business rules, making the system easier to maintain and extend.

```python
from django.db import models

class FinancialYear(models.Model):
    """
    Represents the tblFinancial_master table.
    """
    fin_year_id = models.CharField(db_column='FinYearId', primary_key=True, max_length=50) # Assuming FinYearId is string based on its usage in SQL
    fin_year = models.CharField(db_column='FinYear', max_length=50, blank=True, null=True)
    fin_year_from = models.DateField(db_column='FinYearFrom', blank=True, null=True)
    fin_year_to = models.DateField(db_column='FinYearTo', blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.fin_year or f"ID: {self.fin_year_id}"

class Employee(models.Model):
    """
    Represents the tblHR_OfficeStaff table.
    """
    emp_id = models.CharField(db_column='EmpId', primary_key=True, max_length=50) # Assuming EmpId is string
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name or f"ID: {self.emp_id}"

class SupplierManager(models.Manager):
    """
    Custom manager for Supplier model to encapsulate complex data retrieval logic.
    Corresponds to the BindData method in ASP.NET.
    """
    def get_filtered_suppliers(self, supplier_id=None, financial_year_id=None, company_id=None,
                               from_date=None, to_date=None, item_category=None):
        """
        Fetches supplier data, applying filters and joining related tables.
        This method mirrors the logic in the ASP.NET BindData method and
        the commented-out BindData method's data enrichment.
        """
        # Start with the base query for suppliers
        queryset = self.get_queryset()

        # Apply Company ID and Financial Year ID from session, as per ASP.NET
        if company_id:
            queryset = queryset.filter(comp_id=company_id)
        if financial_year_id:
            # ASP.NET uses FinYearId <= FyId, so we will replicate this for now.
            # In a modern system, exact matching is usually preferred.
            queryset = queryset.filter(fin_year_id__lte=financial_year_id)

        # Apply specific supplier filter if provided (from search textbox)
        if supplier_id:
            queryset = queryset.filter(supplier_id=supplier_id)

        # Apply date filters to SysDate, if provided.
        # Note: ASP.NET SysDate conversion from string to datetime is complex.
        # Assuming SysDate in DB is an actual date/datetime type for efficient filtering.
        # If SysDate is stored as VARCHAR 'DD-MM-YYYY', additional database functions
        # or raw SQL would be needed for date range filtering in the DB.
        # For 'managed=False', the field in Django model should match actual DB type.
        # Let's assume SysDate is an actual date column in the database now.
        if from_date:
            queryset = queryset.filter(sys_date__gte=from_date)
        if to_date:
            queryset = queryset.filter(sys_date__lte=to_date)

        # Item Category (DrpType) filtering. The ASP.NET code *had* DrpType,
        # but the BindData SQL didn't use it. If it was used in other reports,
        # we'd implement filtering here. For this context, it's captured but not applied.
        # e.g., if item_category and item_category != 'Select':
        #     queryset = queryset.filter(item_category_field=item_category)

        # Select related data to avoid N+1 queries for joined fields
        # This implicitly performs the joins from ASP.NET SQL.
        queryset = queryset.select_related('financial_year', 'created_by_employee')

        # Order by SupplierName for consistent display
        queryset = queryset.order_by('supplier_name')
        
        # In the ASP.NET, there's complex date formatting and fetching employee name
        # and financial year name. These are handled via related objects in Django.
        # For example, obj.financial_year.fin_year and obj.created_by_employee.employee_name

        return queryset

class Supplier(models.Model):
    """
    Represents the tblMM_Supplier_master table.
    """
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255, blank=True, null=True)
    # ASP.NET SysDate conversion implies it's stored as varchar, but for Django's
    # date field and proper DB filtering, it should ideally be a date type in DB.
    # If the column is truly varchar, you would need custom Field or DB functions.
    # For now, assuming it's a date type in the DB despite the ASP.NET string parsing.
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True) 
    
    # Foreign keys
    fin_year_id = models.CharField(db_column='FinYearId', max_length=50, blank=True, null=True)
    financial_year = models.ForeignKey(
        FinancialYear,
        on_delete=models.DO_NOTHING, # Or SET_NULL, CASCADE depending on business logic
        db_column='FinYearId', # Explicitly map to the existing column
        related_name='suppliers',
        blank=True, null=True # Allow null if not always linked
    )
    
    session_id = models.CharField(db_column='SessionId', max_length=50, blank=True, null=True)
    created_by_employee = models.ForeignKey(
        Employee,
        on_delete=models.DO_NOTHING, # Or SET_NULL, CASCADE depending on business logic
        db_column='SessionId', # Explicitly map to the existing column (EmpId in Employee)
        related_name='created_suppliers',
        blank=True, null=True # Allow null if not always linked
    )
    
    comp_id = models.IntegerField(db_column='CompId', blank=True, null=True)

    # Attach the custom manager
    objects = SupplierManager()

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def __str__(self):
        return self.supplier_name or f"Supplier ID: {self.supplier_id}"

    # Business logic methods can go here, e.g., for calculating rating if present.
    # def calculate_rating(self, start_date, end_date):
    #     """
    #     Dummy method to illustrate business logic on the model.
    #     """
    #     # Placeholder for actual rating calculation based on orders, quality, etc.
    #     return f"Rating for {self.supplier_name} between {start_date} and {end_date}: A+"

```

#### 4.2 Forms (`material_management/forms.py`)

**Business Value:** Forms streamline user input and validate data before it enters your system. By defining clear forms, you ensure data accuracy and improve the user experience, making your application more reliable and efficient.

```python
from django import forms
from .models import Supplier, FinancialYear, Employee

# Helper function to get current financial year and company ID
# In a real app, this would come from user session/context.
# For demo, using dummy values.
def get_current_financial_year_and_company(request):
    # In a real ERP system, this would be retrieved from the user's session
    # or an active company/financial year setting.
    # For now, hardcode or retrieve from a mock session object.
    # Example: CId = Convert.ToInt32(Session["compid"]); FyId = Session["finyear"].ToString();
    # Let's assume some default values for testing/demonstration.
    current_company_id = 1 # Example Company ID
    # Get the latest FinancialYearId from the DB for demonstration.
    # In production, this would be dynamic.
    latest_fin_year = FinancialYear.objects.order_by('-fin_year_id').first()
    current_financial_year_id = latest_fin_year.fin_year_id if latest_fin_year else '2023-24'
    return current_company_id, current_financial_year_id

class OverallRatingSearchForm(forms.Form):
    """
    Form for the 'Overall rating' tab's date range.
    """
    from_date = forms.DateField(
        label="From Date",
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input for native picker
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        required=True
    )
    to_date = forms.DateField(
        label="To Date",
        widget=forms.DateInput(attrs={
            'type': 'date', # HTML5 date input
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        required=True
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', "To Date cannot be before From Date.")
        return cleaned_data


class SupplierWiseSearchForm(forms.Form):
    """
    Form for the 'Supplier Wise' tab's search criteria.
    """
    from_date = forms.DateField(
        label="From Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        required=True
    )
    to_date = forms.DateField(
        label="To Date",
        widget=forms.DateInput(attrs={
            'type': 'date',
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        required=True
    )
    item_category = forms.ChoiceField(
        label="Item Category",
        choices=[('Select', 'Select'), ('BoughtOut', 'BoughtOut'), ('WOItems', 'WO Items')],
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        })
    )
    supplier_name_autocomplete = forms.CharField(
        label="Supplier Name",
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Start typing supplier name...',
            'hx-get': '/material_management/supplier/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#supplier-suggestions',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Prevent browser autocomplete
            '@input': 'clearSupplierId()', # Alpine.js: clear hidden ID on input change
        })
    )
    # Hidden field to store the actual supplier ID from autocomplete selection
    supplier_id_selected = forms.CharField(
        widget=forms.HiddenInput(attrs={'id': 'id_supplier_id_selected'}),
        required=False
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('to_date', "To Date cannot be before From Date.")
        
        # If supplier_name_autocomplete is provided but supplier_id_selected is not,
        # it might mean an invalid selection or incomplete typing.
        # Depending on desired behavior, could add validation here.
        return cleaned_data

class SupplierForm(forms.ModelForm):
    """
    Dummy form for Supplier Create/Update operations,
    as per prompt's requirement for CRUD example.
    """
    # Assuming default values for financial_year and created_by_employee for simplicity
    # In a real app, these would be selected or derived from context.
    
    # Overriding to use CharField for DB column mapping, but allow choosing from existing FKs
    financial_year_choice = forms.ModelChoiceField(
        queryset=FinancialYear.objects.all(),
        label="Financial Year",
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    employee_choice = forms.ModelChoiceField(
        queryset=Employee.objects.all(),
        label="Generated By",
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = Supplier
        fields = ['supplier_id', 'supplier_name', 'sys_date'] # Exclude FKs for direct handling
        widgets = {
            'supplier_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'supplier_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'sys_date': 'Generated Date',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate initial values for FK choice fields
        if self.instance.pk:
            if self.instance.financial_year:
                self.fields['financial_year_choice'].initial = self.instance.financial_year
            if self.instance.created_by_employee:
                self.fields['employee_choice'].initial = self.instance.created_by_employee
        
        # Make supplier_id read-only for update operations
        if self.instance.pk:
            self.fields['supplier_id'].widget.attrs['readonly'] = True
            self.fields['supplier_id'].widget.attrs['class'] += ' bg-gray-100'

    def clean_supplier_id(self):
        supplier_id = self.cleaned_data['supplier_id']
        if not self.instance.pk and Supplier.objects.filter(supplier_id=supplier_id).exists():
            raise forms.ValidationError("This Supplier ID already exists.")
        return supplier_id

    def save(self, commit=True):
        instance = super().save(commit=False)
        # Manually set FKs from the choice fields
        instance.financial_year = self.cleaned_data.get('financial_year_choice')
        instance.fin_year_id = instance.financial_year.fin_year_id if instance.financial_year else None
        
        instance.created_by_employee = self.cleaned_data.get('employee_choice')
        instance.session_id = instance.created_by_employee.emp_id if instance.created_by_employee else None

        # Dummy CompId for demonstration
        instance.comp_id = 1 
        
        if commit:
            instance.save()
        return instance

```

#### 4.3 Views (`material_management/views.py`)

**Business Value:** Views are the brain of your application, orchestrating how data is retrieved, processed, and displayed. By using Django's Class-Based Views (CBVs) and keeping them "thin," we ensure maintainability, reduce code duplication, and enforce a clear separation of concerns, leading to a more robust and scalable system.

```python
from django.views.generic import FormView, ListView, View, CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import redirect, get_object_or_404
from datetime import date
from django.db.models import Q # For OR queries if needed in future
from django.utils import timezone

from .models import Supplier, FinancialYear, Employee
from .forms import OverallRatingSearchForm, SupplierWiseSearchForm, SupplierForm, get_current_financial_year_and_company

# --- Main Page View (handles tab selection) ---
class VendorRatingHomeView(FormView):
    """
    Handles the main Vendor Rating page with two tabs: Overall Rating and Supplier Wise.
    It renders the initial page and handles initial form setup.
    """
    template_name = 'material_management/vendor_rating/home.html'
    
    # We will instantiate forms in get_context_data based on tab
    overall_form_class = OverallRatingSearchForm
    supplier_form_class = SupplierWiseSearchForm

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Determine the active tab from query params or default
        active_tab = self.request.GET.get('tab', 'overall_rating')
        context['active_tab'] = active_tab

        # Get session-like values for company and financial year
        company_id, financial_year_id = get_current_financial_year_and_company(self.request)
        context['company_id'] = company_id
        context['financial_year_id'] = financial_year_id

        # Populate forms with default dates based on ASP.NET logic (financial year start/end or current date)
        # Default dates are usually handled in form __init__ or view logic.
        # For demo, let's use financial year start and current date.
        default_from_date = None
        default_to_date = None
        current_fin_year_obj = FinancialYear.objects.filter(fin_year_id=financial_year_id, comp_id=company_id).first()
        if current_fin_year_obj:
            default_from_date = current_fin_year_obj.fin_year_from
            default_to_date = timezone.localdate() # getCurrDate() equivalent

        if active_tab == 'overall_rating':
            context['overall_form'] = self.overall_form_class(initial={
                'from_date': default_from_date,
                'to_date': default_to_date,
            })
            context['supplier_form'] = self.supplier_form_class() # Initialize but don't bind for this tab
        else: # active_tab == 'supplier_wise'
            context['overall_form'] = self.overall_form_class() # Initialize but don't bind for this tab
            context['supplier_form'] = self.supplier_form_class(initial={
                'from_date': default_from_date,
                'to_date': default_to_date,
            })
        return context

    # No POST handler for the main page, as forms are submitted via HTMX to other endpoints.
    # This view just renders the shell.


# --- HTMX Partial View for Supplier Table ---
class SupplierTablePartialView(ListView):
    """
    Returns the HTML partial for the supplier table, used by HTMX.
    This encapsulates the `BindData` logic from ASP.NET.
    """
    model = Supplier
    template_name = 'material_management/vendor_rating/_supplier_table.html'
    context_object_name = 'suppliers'

    def get_queryset(self):
        # Get query parameters for filtering
        company_id, financial_year_id = get_current_financial_year_and_company(self.request)
        supplier_id = self.request.GET.get('supplier_id_selected')
        from_date_str = self.request.GET.get('from_date')
        to_date_str = self.request.GET.get('to_date')
        item_category = self.request.GET.get('item_category') # Not used in ASP.NET BindData, but captured.

        from_date = date.fromisoformat(from_date_str) if from_date_str else None
        to_date = date.fromisoformat(to_date_str) if to_date_str else None

        # Call the custom manager method to get filtered data
        queryset = Supplier.objects.get_filtered_suppliers(
            supplier_id=supplier_id,
            financial_year_id=financial_year_id,
            company_id=company_id,
            from_date=from_date,
            to_date=to_date,
            item_category=item_category # Pass, even if not directly used in manager
        )
        return queryset

    # We don't need pagination handled by ListView if DataTables handles it client-side.
    # If server-side pagination is needed, then mixins like PaginatorMixin can be used.


# --- Autocomplete View ---
class SupplierAutocomplete(View):
    """
    Provides JSON response for supplier name autocomplete functionality.
    Corresponds to the `sql` WebMethod in ASP.NET.
    """
    def get(self, request, *args, **kwargs):
        prefix_text = request.GET.get('q', '')
        company_id, _ = get_current_financial_year_and_company(request)

        # Filter suppliers by name prefix and company ID
        suggestions = Supplier.objects.filter(
            supplier_name__icontains=prefix_text,
            comp_id=company_id
        ).values_list('supplier_name', 'supplier_id')[:10] # Limit to 10 results like ASP.NET example

        results = []
        for name, sid in suggestions:
            results.append({'value': f"{name} [{sid}]", 'id': sid})
        
        # HTMX expects plain text or HTML for hx-target, but if used for datalist or JS, JSON is fine.
        # For this setup, we'll return HTML fragments for HTMX.
        # This will render a list of suggestions that Alpine.js can use.
        html_suggestions = ""
        if results:
            html_suggestions = "<div class='absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto'>"
            for res in results:
                html_suggestions += f"<div class='p-2 cursor-pointer hover:bg-gray-200' hx-on:click=\"this.closest('[x-data]').querySelector('[name=supplier_name_autocomplete]').value='{res['value']}';this.closest('[x-data]').querySelector('[name=supplier_id_selected]').value='{res['id']}';this.innerHTML='';\">{res['value']}</div>"
            html_suggestions += "</div>"
        
        return HttpResponse(html_suggestions)
        # If using JSON for client-side processing (e.g. Typeahead.js or custom Alpine.js logic):
        # return JsonResponse([{'id': s[1], 'text': s[0]} for s in suggestions], safe=False)


# --- Report Redirection Views ---
class OverallRatingReportRedirectView(View):
    """
    Handles the redirection for 'Overall rating' report.
    Corresponds to `BtnSearch_Click` for the Overall Rating tab.
    """
    def post(self, request, *args, **kwargs):
        form = OverallRatingSearchForm(request.POST)
        if form.is_valid():
            fd = form.cleaned_data['from_date'].isoformat()
            td = form.cleaned_data['to_date'].isoformat()
            # In a real app, generate a unique key for the report session if needed.
            # For now, just pass params.
            return redirect(f"{reverse_lazy('overall_rating_print')}?FD={fd}&TD={td}&Key=DummyKey")
        else:
            # If form is invalid, return the form with errors via HTMX.
            # This requires the form to be loaded via HTMX initially.
            # For simplicity, we assume the initial form load covers errors.
            # Or re-render the partial containing the form.
            return HttpResponse(status=400) # Indicate an error if not handled client-side
            # For HTMX, you might return the form partial again with errors
            # from django.template.loader import render_to_string
            # html_content = render_to_string('material_management/vendor_rating/_overall_rating_search_form.html', {'overall_form': form}, request=request)
            # return HttpResponse(html_content, status=400)


class VendorRatingPrintRedirectView(View):
    """
    Handles the redirection for 'Supplier Wise' report (after "Select" action).
    Corresponds to `SearchGridView1_RowCommand` for CommandName="Sel".
    """
    def get(self, request, supplier_id): # Get supplier_id from URL kwargs
        # Extract from_date and to_date from query parameters, not POST
        # In ASP.NET, these came from TextBoxes on the same page.
        # We need to pass them via hidden inputs or from the form submission on the main page.
        fd = request.GET.get('FD', '')
        td = request.GET.get('TD', '')
        val = request.GET.get('Val', '') # Item Category
        
        # Generate a unique key for report tracking if needed
        return redirect(f"{reverse_lazy('vendor_rating_print')}?SupCode={supplier_id}&FD={fd}&TD={td}&Val={val}&Key=DummyKey")

# --- Dummy Report Pages (Landing pages for redirects) ---
class OverallRatingPrintView(View):
    """
    Placeholder for the actual 'Overall Rating' report page.
    """
    def get(self, request, *args, **kwargs):
        from_date = request.GET.get('FD')
        to_date = request.GET.get('TD')
        key = request.GET.get('Key')
        # Here, you would fetch data based on from_date, to_date and render the report.
        return HttpResponse(f"<h1>Overall Rating Report</h1><p>From: {from_date}, To: {to_date}, Key: {key}</p><p>This would be the full report content.</p>")

class VendorRatingPrintView(View):
    """
    Placeholder for the actual 'Vendor Rating Print' report page.
    """
    def get(self, request, *args, **kwargs):
        supplier_code = request.GET.get('SupCode')
        from_date = request.GET.get('FD')
        to_date = request.GET.get('TD')
        item_category = request.GET.get('Val')
        key = request.GET.get('Key')
        # Here, you would fetch data for the specific supplier and dates, then render the report.
        return HttpResponse(f"<h1>Supplier Wise Rating Report for {supplier_code}</h1><p>From: {from_date}, To: {to_date}, Category: {item_category}, Key: {key}</p><p>This would be the detailed report content.</p>")


# --- Dummy CRUD Views for Supplier Model (as per prompt request) ---
class SupplierCreateView(CreateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'material_management/supplier/_form.html'
    success_url = reverse_lazy('vendor_rating_home') # Redirect back to the home page or supplier list

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No content, HTMX will handle triggering
                headers={
                    'HX-Trigger': 'refreshSupplierList, closeModals' # Custom trigger to refresh list and close modal
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['is_new'] = True
        return context

class SupplierUpdateView(UpdateView):
    model = Supplier
    form_class = SupplierForm
    template_name = 'material_management/supplier/_form.html'
    success_url = reverse_lazy('vendor_rating_home')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Supplier updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList, closeModals'
                }
            )
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['is_new'] = False
        return context

class SupplierDeleteView(DeleteView):
    model = Supplier
    template_name = 'material_management/supplier/_confirm_delete.html'
    success_url = reverse_lazy('vendor_rating_home')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Supplier deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshSupplierList, closeModals'
                }
            )
        return response
```

#### 4.4 Templates (`material_management/templates/material_management/vendor_rating/`)

**Business Value:** Templates provide the visual structure and user interface for your application. By using modern, clean HTML with HTMX and Alpine.js, we deliver a highly interactive and responsive experience without complex JavaScript, ensuring a faster, more engaging application for users.

**`material_management/templates/material_management/vendor_rating/home.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: '{{ active_tab }}', showModal: false }"
     @close-modals.window="showModal = false">
    
    <div class="mb-6">
        <h2 class="text-2xl font-bold mb-4">Vendor Rating</h2>
        
        <!-- Tab Navigation -->
        <div class="flex border-b border-gray-300">
            <button @click="activeTab = 'overall_rating'" 
                    :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'overall_rating', 'text-gray-600 hover:text-blue-600': activeTab !== 'overall_rating'}"
                    class="py-2 px-4 focus:outline-none font-medium">
                Overall Rating
            </button>
            <button @click="activeTab = 'supplier_wise'" 
                    :class="{'border-b-2 border-blue-500 text-blue-600': activeTab === 'supplier_wise', 'text-gray-600 hover:text-blue-600': activeTab !== 'supplier_wise'}"
                    class="py-2 px-4 focus:outline-none font-medium">
                Supplier Wise
            </button>
        </div>
    </div>

    <!-- Tab Content -->
    <div x-show="activeTab === 'overall_rating'" class="p-4 bg-white rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4">Overall Rating Search</h3>
        <form hx-post="{% url 'overall_rating_redirect' %}" hx-swap="none" hx-indicator="#overall-loading-indicator">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ overall_form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
                    {{ overall_form.from_date }}
                    {% if overall_form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ overall_form.from_date.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ overall_form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
                    {{ overall_form.to_date }}
                    {% if overall_form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ overall_form.to_date.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6">
                <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    Submit
                </button>
                <span id="overall-loading-indicator" class="htmx-indicator ml-3">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </span>
            </div>
        </form>
    </div>

    <div x-show="activeTab === 'supplier_wise'" class="p-4 bg-white rounded-lg shadow mt-6">
        <h3 class="text-lg font-semibold mb-4">Supplier Wise Search</h3>
        <form id="supplier-wise-search-form" hx-get="{% url 'supplier_table_partial' %}" 
              hx-target="#supplierTable-container" 
              hx-swap="innerHTML" 
              hx-indicator="#supplier-loading-indicator">
            {% csrf_token %}
            <div x-data="{ supplierId: '{{ supplier_form.supplier_id_selected.value|default:"" }}', clearSupplierId: () => { supplierId = ''; document.getElementById('id_supplier_id_selected').value = ''; } }" class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <label for="{{ supplier_form.from_date.id_for_label }}" class="block text-sm font-medium text-gray-700">From Date</label>
                        {{ supplier_form.from_date }}
                        {% if supplier_form.from_date.errors %}<p class="text-red-500 text-xs mt-1">{{ supplier_form.from_date.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ supplier_form.to_date.id_for_label }}" class="block text-sm font-medium text-gray-700">To Date</label>
                        {{ supplier_form.to_date }}
                        {% if supplier_form.to_date.errors %}<p class="text-red-500 text-xs mt-1">{{ supplier_form.to_date.errors }}</p>{% endif %}
                    </div>
                    <div>
                        <label for="{{ supplier_form.item_category.id_for_label }}" class="block text-sm font-medium text-gray-700">Item Category</label>
                        {{ supplier_form.item_category }}
                        {% if supplier_form.item_category.errors %}<p class="text-red-500 text-xs mt-1">{{ supplier_form.item_category.errors }}</p>{% endif %}
                    </div>
                </div>

                <div class="relative">
                    <label for="{{ supplier_form.supplier_name_autocomplete.id_for_label }}" class="block text-sm font-medium text-gray-700">Supplier Name</label>
                    {{ supplier_form.supplier_name_autocomplete }}
                    {{ supplier_form.supplier_id_selected }} {# Hidden field for selected ID #}
                    {% if supplier_form.supplier_name_autocomplete.errors %}<p class="text-red-500 text-xs mt-1">{{ supplier_form.supplier_name_autocomplete.errors }}</p>{% endif %}
                    <div id="supplier-suggestions"></div> {# HTMX target for autocomplete results #}
                </div>
            </div>
            
            <div class="mt-6 flex justify-between items-center">
                <button type="submit" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                    Search
                </button>
                <span id="supplier-loading-indicator" class="htmx-indicator ml-3">
                    <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-indigo-500"></div>
                </span>
                <button
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                    hx-get="{% url 'supplier_add' %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click set showModal to true">
                    Add New Supplier
                </button>
            </div>
        </form>

        <div id="supplierTable-container"
             hx-trigger="load, refreshSupplierList from:body"
             hx-get="{% url 'supplier_table_partial' %}?from_date={{ supplier_form.from_date.value|date:"Y-m-d" }}&to_date={{ supplier_form.to_date.value|date:"Y-m-d" }}&item_category={{ supplier_form.item_category.value }}&supplier_id_selected={{ supplier_form.supplier_id_selected.value }}"
             hx-swap="innerHTML"
             class="mt-8">
            <!-- Initial loading state for DataTables -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Supplier Data...</p>
            </div>
        </div>
    </div>

    <!-- Modal for forms/confirmation -->
    <div x-show="showModal" 
         class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center p-4 z-50"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         @click.self="showModal = false">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
             x-transition:enter="ease-out duration-300"
             x-transition:enter-start="opacity-0 scale-95"
             x-transition:enter-end="opacity-100 scale-100"
             x-transition:leave="ease-in duration-200"
             x-transition:leave-start="opacity-100 scale-100"
             x-transition:leave-end="opacity-0 scale-95">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
<script src="https://cdn.datatables.net/2.0.8/js/dataTables.tailwindcss.min.js"></script>
<link href="https://cdn.datatables.net/2.0.8/css/dataTables.tailwindcss.min.css" rel="stylesheet">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('modal', {
            show: false,
            open() { this.show = true },
            close() { this.show = false },
        });

        // Event listener for HTMX closeModals trigger
        document.body.addEventListener('closeModals', () => {
            Alpine.store('modal').close();
            // Also reset supplier autocomplete if it's currently showing suggestions
            const supplierSuggestions = document.getElementById('supplier-suggestions');
            if (supplierSuggestions) {
                supplierSuggestions.innerHTML = '';
            }
        });
    });

    // Delegate DataTables initialization using HTMX's htmx:afterSwap event
    document.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'supplierTable-container') {
            const table = document.getElementById('supplierTable');
            if (table && !$.fn.DataTable.isDataTable(table)) {
                $(table).DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "destroy": true, // Allow reinitialization if HTMX swaps again
                    "searching": true, // Enable built-in search
                    "ordering": true,  // Enable built-in sorting
                    "paging": true,    // Enable built-in pagination
                });
            }
        }
    });

    // Initial load for supplier wise table (if default tab)
    document.addEventListener('DOMContentLoaded', () => {
        const supplierWiseForm = document.getElementById('supplier-wise-search-form');
        if (supplierWiseForm) {
            // Check if supplier_wise tab is active on initial load
            const activeTabInput = document.querySelector('[x-data]').__alpine.$data.activeTab;
            if (activeTabInput === 'supplier_wise') {
                htmx.trigger(supplierWiseForm, 'submit'); // Trigger initial search on load
            }
        }
    });

</script>
{% endblock %}
```

**`material_management/templates/material_management/vendor_rating/_supplier_table.html` (Partial for DataTables)**

```html
<table id="supplierTable" class="min-w-full bg-white border border-gray-200">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in suppliers %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.financial_year.fin_year|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.supplier_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.supplier_id }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.sys_date|date:"d-m-Y"|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200 text-left">{{ obj.created_by_employee.employee_name|default:"N/A" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button
                    class="bg-green-500 hover:bg-green-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'vendor_rating_print_redirect' supplier_id=obj.supplier_id %}?FD={{ request.GET.from_date|default:"" }}&TD={{ request.GET.to_date|default:"" }}&Val={{ request.GET.item_category|default:"" }}"
                    hx-target="body"
                    hx-swap="outerHTML"> {# This will redirect the whole page #}
                    Select
                </button>
                <button
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'supplier_edit' pk=obj.supplier_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click set showModal to true">
                    Edit
                </button>
                <button
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'supplier_delete' pk=obj.supplier_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click set showModal to true">
                    Delete
                </button>
            </td>
        </tr>
        {% empty %}
        <tr>
            <td colspan="7" class="py-4 px-4 border-b border-gray-200 text-center text-maroon font-semibold text-lg">
                No data to display !
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

```

**`material_management/templates/material_management/supplier/_form.html` (Partial for Create/Update Modals)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">{{ is_new|yesno:'Add New,Edit' }} Supplier</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click call Alpine.store('modal').close()">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
             <span id="form-loading-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

**`material_management/templates/material_management/supplier/_confirm_delete.html` (Partial for Delete Modal)**

```html
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-6">Confirm Delete</h3>
    <p class="text-gray-700 mb-8">Are you sure you want to delete supplier <strong>"{{ object.supplier_name }}" ({{ object.supplier_id }})</strong>?</p>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#delete-loading-indicator">
        {% csrf_token %}
        <div class="flex justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click call Alpine.store('modal').close()">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
            <span id="delete-loading-indicator" class="htmx-indicator ml-3">
                <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
            </span>
        </div>
    </form>
</div>
```

#### 4.5 URLs (`material_management/urls.py`)

**Business Value:** Clean and predictable URLs are crucial for user experience and search engine optimization. This step organizes your application's endpoints logically, making it easy for users and other systems to interact with your data.

```python
from django.urls import path
from .views import (
    VendorRatingHomeView,
    SupplierTablePartialView,
    SupplierAutocomplete,
    OverallRatingReportRedirectView,
    VendorRatingPrintRedirectView,
    OverallRatingPrintView,
    VendorRatingPrintView,
    SupplierCreateView,
    SupplierUpdateView,
    SupplierDeleteView,
)

urlpatterns = [
    # Main Vendor Rating Page
    path('vendor-rating/', VendorRatingHomeView.as_view(), name='vendor_rating_home'),
    
    # HTMX endpoints for partial updates
    path('vendor-rating/supplier-table/', SupplierTablePartialView.as_view(), name='supplier_table_partial'),
    path('vendor-rating/supplier-autocomplete/', SupplierAutocomplete.as_view(), name='supplier_autocomplete'),

    # Report Redirection Endpoints (Post requests for forms that redirect)
    path('vendor-rating/overall-rating-redirect/', OverallRatingReportRedirectView.as_view(), name='overall_rating_redirect'),
    path('vendor-rating/supplier-print-redirect/<str:supplier_id>/', VendorRatingPrintRedirectView.as_view(), name='vendor_rating_print_redirect'),
    
    # Actual Report Pages (these are the targets of the redirects)
    path('reports/overall-rating/', OverallRatingPrintView.as_view(), name='overall_rating_print'),
    path('reports/vendor-rating-print/', VendorRatingPrintView.as_view(), name='vendor_rating_print'),

    # Dummy CRUD for Supplier (as per prompt instructions)
    path('supplier/add/', SupplierCreateView.as_view(), name='supplier_add'),
    path('supplier/edit/<str:pk>/', SupplierUpdateView.as_view(), name='supplier_edit'),
    path('supplier/delete/<str:pk>/', SupplierDeleteView.as_view(), name='supplier_delete'),
]
```

#### 4.6 Tests (`material_management/tests.py`)

**Business Value:** Comprehensive testing guarantees the reliability and accuracy of your migrated system. By automating tests, we drastically reduce the risk of errors and ensure that every piece of business logic functions as expected, building trust in your new application.

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import date
from .models import Supplier, FinancialYear, Employee
from .forms import OverallRatingSearchForm, SupplierWiseSearchForm

# Mock the session data for tests
class MockSession:
    def __init__(self):
        self.data = {}
    
    def __setitem__(self, key, value):
        self.data[key] = value

    def __getitem__(self, key):
        return self.data[key]

    def get(self, key, default=None):
        return self.data.get(key, default)

class ModelTestSetup(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create dummy FinancialYear data
        cls.fin_year_2023 = FinancialYear.objects.create(
            fin_year_id='2023-24', fin_year='2023-24', fin_year_from=date(2023, 4, 1), fin_year_to=date(2024, 3, 31), comp_id=1
        )
        cls.fin_year_2022 = FinancialYear.objects.create(
            fin_year_id='2022-23', fin_year='2022-23', fin_year_from=date(2022, 4, 1), fin_year_to=date(2023, 3, 31), comp_id=1
        )

        # Create dummy Employee data
        cls.employee1 = Employee.objects.create(emp_id='EMP001', employee_name='John Doe', comp_id=1)
        cls.employee2 = Employee.objects.create(emp_id='EMP002', employee_name='Jane Smith', comp_id=1)

        # Create test Supplier data
        cls.supplier1 = Supplier.objects.create(
            supplier_id='SUP001', supplier_name='ABC Corp', sys_date=date(2023, 10, 15),
            financial_year=cls.fin_year_2023, fin_year_id=cls.fin_year_2023.fin_year_id,
            created_by_employee=cls.employee1, session_id=cls.employee1.emp_id, comp_id=1
        )
        cls.supplier2 = Supplier.objects.create(
            supplier_id='SUP002', supplier_name='XYZ Ltd', sys_date=date(2023, 11, 20),
            financial_year=cls.fin_year_2023, fin_year_id=cls.fin_year_2023.fin_year_id,
            created_by_employee=cls.employee2, session_id=cls.employee2.emp_id, comp_id=1
        )
        cls.supplier3_old_fin_year = Supplier.objects.create(
            supplier_id='SUP003', supplier_name='Old Inc', sys_date=date(2022, 5, 10),
            financial_year=cls.fin_year_2022, fin_year_id=cls.fin_year_2022.fin_year_id,
            created_by_employee=cls.employee1, session_id=cls.employee1.emp_id, comp_id=1
        )

class FinancialYearModelTest(ModelTestSetup):
    def test_financial_year_creation(self):
        fin_year = FinancialYear.objects.get(fin_year_id='2023-24')
        self.assertEqual(fin_year.fin_year, '2023-24')
        self.assertEqual(fin_year.fin_year_from, date(2023, 4, 1))
        self.assertEqual(fin_year.comp_id, 1)

    def test_str_method(self):
        fin_year = FinancialYear.objects.get(fin_year_id='2023-24')
        self.assertEqual(str(fin_year), '2023-24')

class EmployeeModelTest(ModelTestSetup):
    def test_employee_creation(self):
        employee = Employee.objects.get(emp_id='EMP001')
        self.assertEqual(employee.employee_name, 'John Doe')
        self.assertEqual(employee.comp_id, 1)

    def test_str_method(self):
        employee = Employee.objects.get(emp_id='EMP001')
        self.assertEqual(str(employee), 'John Doe')

class SupplierModelTest(ModelTestSetup):
    def test_supplier_creation(self):
        supplier = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(supplier.supplier_name, 'ABC Corp')
        self.assertEqual(supplier.sys_date, date(2023, 10, 15))
        self.assertEqual(supplier.financial_year, self.fin_year_2023)
        self.assertEqual(supplier.created_by_employee, self.employee1)
        self.assertEqual(supplier.comp_id, 1)

    def test_str_method(self):
        supplier = Supplier.objects.get(supplier_id='SUP001')
        self.assertEqual(str(supplier), 'ABC Corp')

    def test_get_filtered_suppliers(self):
        # Test basic filtering by current financial year (lte)
        # Assuming get_current_financial_year_and_company returns latest fin_year_id (2023-24)
        suppliers = Supplier.objects.get_filtered_suppliers(
            financial_year_id='2023-24', company_id=1
        )
        # Should include suppliers from 2023-24 and 2022-23 (due to <= logic)
        self.assertEqual(suppliers.count(), 3)
        self.assertIn(self.supplier1, suppliers)
        self.assertIn(self.supplier2, suppliers)
        self.assertIn(self.supplier3_old_fin_year, suppliers)

        # Test filtering by specific supplier_id
        suppliers = Supplier.objects.get_filtered_suppliers(
            supplier_id='SUP001', financial_year_id='2023-24', company_id=1
        )
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 'SUP001')

        # Test filtering by date range
        suppliers = Supplier.objects.get_filtered_suppliers(
            from_date=date(2023, 10, 1), to_date=date(2023, 10, 31),
            financial_year_id='2023-24', company_id=1
        )
        self.assertEqual(suppliers.count(), 1)
        self.assertEqual(suppliers.first().supplier_id, 'SUP001')

        # Test no results
        suppliers = Supplier.objects.get_filtered_suppliers(
            supplier_id='NONEXISTENT', financial_year_id='2023-24', company_id=1
        )
        self.assertEqual(suppliers.count(), 0)


class VendorRatingViewsTest(ModelTestSetup):
    def setUp(self):
        self.client = Client()
        # Mock session for the duration of tests
        # This is a simplified mock; for full session fidelity, consider django-mock-session
        # For simplicity, we just set the necessary attributes on request.
        # This will be handled in a `with self.client.session()` block if real session needed.
        # For FormView, the request context is typically created by Django.

    def test_vendor_rating_home_view_get(self):
        response = self.client.get(reverse('vendor_rating_home'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/vendor_rating/home.html')
        self.assertIn('overall_form', response.context)
        self.assertIn('supplier_form', response.context)
        self.assertEqual(response.context['active_tab'], 'overall_rating')

    def test_vendor_rating_home_view_get_supplier_tab(self):
        response = self.client.get(reverse('vendor_rating_home') + '?tab=supplier_wise')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/vendor_rating/home.html')
        self.assertEqual(response.context['active_tab'], 'supplier_wise')

    def test_supplier_table_partial_view(self):
        # HTMX GET request to load the table content
        # Pass dummy date range as in ASP.NET's default behavior
        response = self.client.get(reverse('supplier_table_partial'), {
            'from_date': '2023-04-01',
            'to_date': '2024-03-31',
            'supplier_id_selected': '',
            'item_category': 'Select'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/vendor_rating/_supplier_table.html')
        self.assertIn('suppliers', response.context)
        self.assertGreater(len(response.context['suppliers']), 0)
        self.assertContains(response, 'ABC Corp')
        self.assertContains(response, 'XYZ Ltd')

    def test_supplier_table_partial_view_with_filter(self):
        response = self.client.get(reverse('supplier_table_partial'), {
            'from_date': '2023-10-01',
            'to_date': '2023-10-31',
            'supplier_id_selected': 'SUP001',
            'item_category': 'Select'
        }, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'ABC Corp')
        self.assertNotContains(response, 'XYZ Ltd') # Only SUP001 should be visible

    def test_supplier_autocomplete_view(self):
        response = self.client.get(reverse('supplier_autocomplete'), {'q': 'abc'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('ABC Corp [SUP001]', response.content.decode('utf-8'))
        self.assertNotIn('XYZ Ltd', response.content.decode('utf-8'))
        self.assertTrue('hx-on:click' in response.content.decode('utf-8'))

    def test_overall_rating_report_redirect(self):
        data = {
            'from_date': '2023-01-01',
            'to_date': '2023-12-31'
        }
        response = self.client.post(reverse('overall_rating_redirect'), data)
        self.assertEqual(response.status_code, 302) # Should redirect
        self.assertRedirects(response, f"{reverse('overall_rating_print')}?FD=2023-01-01&TD=2023-12-31&Key=DummyKey")

    def test_vendor_rating_print_redirect(self):
        # Simulate selecting a row in the DataTables and clicking 'Select'
        response = self.client.get(reverse('vendor_rating_print_redirect', args=['SUP001']) + '?FD=2023-10-01&TD=2023-10-31&Val=BoughtOut')
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, f"{reverse('vendor_rating_print')}?SupCode=SUP001&FD=2023-10-01&TD=2023-10-31&Val=BoughtOut&Key=DummyKey")

    def test_overall_rating_print_view(self):
        response = self.client.get(reverse('overall_rating_print') + '?FD=2023-01-01&TD=2023-12-31&Key=TestKey')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Overall Rating Report')
        self.assertContains(response, 'From: 2023-01-01, To: 2023-12-31, Key: TestKey')

    def test_vendor_rating_print_view(self):
        response = self.client.get(reverse('vendor_rating_print') + '?SupCode=SUP001&FD=2023-10-01&TD=2023-10-31&Val=BoughtOut&Key=TestKey')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Supplier Wise Rating Report for SUP001')
        self.assertContains(response, 'From: 2023-10-01, To: 2023-10-31, Category: BoughtOut, Key: TestKey')

    # --- Dummy CRUD Tests for Supplier ---
    def test_supplier_create_view_get(self):
        response = self.client.get(reverse('supplier_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_form.html')
        self.assertContains(response, 'Add New Supplier')
        self.assertIn('form', response.context)

    def test_supplier_create_view_post_success(self):
        initial_count = Supplier.objects.count()
        data = {
            'supplier_id': 'SUP004',
            'supplier_name': 'New Test Supplier',
            'sys_date': '2024-01-01',
            'financial_year_choice': self.fin_year_2023.pk, # Use PK of existing FinYear
            'employee_choice': self.employee1.pk, # Use PK of existing Employee
        }
        response = self.client.post(reverse('supplier_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success status
        self.assertEqual(Supplier.objects.count(), initial_count + 1)
        self.assertTrue(Supplier.objects.filter(supplier_id='SUP004').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])

    def test_supplier_create_view_post_invalid(self):
        initial_count = Supplier.objects.count()
        data = {
            'supplier_id': 'SUP001', # Duplicate ID
            'supplier_name': 'Invalid Supplier',
            'sys_date': '2024-01-01',
            'financial_year_choice': self.fin_year_2023.pk,
            'employee_choice': self.employee1.pk,
        }
        response = self.client.post(reverse('supplier_add'), data, HTTP_HX_REQUEST='true')
        # HTTP 200 OK because HTMX receives the form with errors, not a redirect
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "This Supplier ID already exists.")
        self.assertEqual(Supplier.objects.count(), initial_count)

    def test_supplier_update_view_get(self):
        response = self.client.get(reverse('supplier_edit', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_form.html')
        self.assertContains(response, 'Edit Supplier')
        self.assertContains(response, self.supplier1.supplier_name)
        self.assertIn('form', response.context)
        # Ensure supplier_id field is readonly
        self.assertContains(response, 'name="supplier_id" value="SUP001" readonly')

    def test_supplier_update_view_post_success(self):
        new_name = 'Updated ABC Corp'
        data = {
            'supplier_id': self.supplier1.supplier_id,
            'supplier_name': new_name,
            'sys_date': self.supplier1.sys_date.isoformat(),
            'financial_year_choice': self.fin_year_2023.pk,
            'employee_choice': self.employee1.pk,
        }
        response = self.client.post(reverse('supplier_edit', args=[self.supplier1.supplier_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.supplier1.refresh_from_db()
        self.assertEqual(self.supplier1.supplier_name, new_name)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])

    def test_supplier_delete_view_get(self):
        response = self.client.get(reverse('supplier_delete', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/supplier/_confirm_delete.html')
        self.assertContains(response, 'Confirm Delete')
        self.assertContains(response, self.supplier1.supplier_name)

    def test_supplier_delete_view_post_success(self):
        initial_count = Supplier.objects.count()
        response = self.client.post(reverse('supplier_delete', args=[self.supplier1.supplier_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(Supplier.objects.count(), initial_count - 1)
        self.assertFalse(Supplier.objects.filter(supplier_id=self.supplier1.supplier_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshSupplierList', response.headers['HX-Trigger'])

```

### Step 5: HTMX and Alpine.js Integration

**Business Value:** This integration transforms a static page into a dynamic, responsive user experience. Users will benefit from instant feedback, faster interactions, and a smoother workflow without full page reloads, enhancing productivity and satisfaction.

**Instructions:**

1.  **HTMX for Dynamic Content Loading:**
    *   The main `home.html` page uses `hx-get` to load the initial content of the `supplierTable-container` div from `{% url 'supplier_table_partial' %}` on `load` and `refreshSupplierList` custom events.
    *   Form submissions (`OverallRatingSearchForm`, `SupplierWiseSearchForm`) use `hx-post` or `hx-get` to either redirect or update parts of the page without full reloads.
    *   The "Add New Supplier", "Edit", and "Delete" buttons trigger `hx-get` requests to load their respective forms/confirmation dialogs into the `#modalContent` div.
    *   Form submissions within the modal (`_form.html`, `_confirm_delete.html`) use `hx-post` and set `hx-swap="none"` combined with `HX-Trigger` headers (`refreshSupplierList, closeModals`) from the Django views. This allows the server to instruct the client to refresh the table and close the modal.
    *   The `supplier_name_autocomplete` field uses `hx-get` to `{% url 'supplier_autocomplete' %}` with `hx-trigger="keyup changed delay:500ms, search"` and `hx-target="#supplier-suggestions"` to dynamically populate suggestions below the input.

2.  **Alpine.js for UI State Management:**
    *   The `home.html` uses `x-data="{ activeTab: '...', showModal: false }"` to manage the visibility of tabs and the modal.
    *   Buttons for opening modals (`Add New Supplier`, `Edit`, `Delete`) set `showModal` to `true`.
    *   The modal's background (`@click.self="showModal = false"`) and 'Cancel' buttons (`_="on click call Alpine.store('modal').close()"`) hide the modal.
    *   A global Alpine.js store `Alpine.store('modal')` and event listener `@close-modals.window="showModal = false"` on `home.html` enable Django views to trigger modal closure via HTMX `HX-Trigger` header (`closeModals`).
    *   The autocomplete suggestion list uses Alpine.js `x-data` and `hx-on:click` to update the text input and hidden ID field when a suggestion is clicked, and then clear the suggestion list.

3.  **DataTables for List Views:**
    *   The `_supplier_table.html` partial contains the `<table>` element with `id="supplierTable"`.
    *   The JavaScript block in `base.html` (or `extra_js` block in `home.html`) includes DataTables CDN links (`js` and `css`).
    *   A `htmx:afterSwap` event listener is used to initialize DataTables *after* the `_supplier_table.html` content has been swapped into the DOM by HTMX. This ensures DataTables correctly applies to the dynamically loaded table.
    *   `"pageLength": 10`, `"lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]` are configured for standard pagination options. `"destroy": true` ensures that if the table is swapped again, DataTables can be reinitialized without error.

4.  **HTMX-Only Interactions:**
    *   All user-initiated dynamic interactions (tab switching logic, form submissions, modal open/close) are managed via HTMX attributes or Alpine.js. No custom jQuery or vanilla JavaScript listeners are needed beyond the initial DataTables setup and Alpine.js initialization.

## Final Notes

*   **Replace Placeholders:** Ensure all placeholders like `[APP_NAME]`, `[MODEL_NAME]`, `[TABLE_NAME]`, etc., are replaced with their determined values (`material_management`, `Supplier`, `tblMM_Supplier_master`).
*   **DRY Templates:** The use of `_supplier_table.html`, `_form.html`, and `_confirm_delete.html` promotes reusability and keeps the main template cleaner.
*   **Fat Models, Thin Views:** Business logic (like `get_filtered_suppliers` in `SupplierManager`) is concentrated in the models, keeping views concise and focused on handling HTTP requests and responses.
*   **Comprehensive Tests:** The provided `tests.py` includes unit tests for models and integration tests for views, covering key functionalities and HTMX interactions. Aim for high test coverage (80%+) to ensure the reliability of the migrated application.
*   **Database Considerations for `managed=False`:** For `SysDate`, the ASP.NET code shows it's stored as `varchar` but manipulated as a `datetime`. In Django, mapping it to `models.DateField` assumes the underlying database column is indeed a date/datetime type. If it's truly `varchar` in the DB, you might need a custom Django field or explicit `CAST`/`CONVERT` in raw SQL queries if filters on date ranges are complex and performant on the DB. For simplicity and typical `managed=False` scenarios where the underlying DB column *should* be a date, `models.DateField` is used.
*   **Session Data:** The ASP.NET application heavily relies on `Session["compid"]` and `Session["finyear"]`. In Django, `request.session` would be used. The `get_current_financial_year_and_company` helper function is a placeholder; in a production application, this would securely retrieve actual session data or user context.
*   **Security:** Ensure proper authentication and authorization (`@login_required`, `PermissionRequiredMixin`) are added to views once user management is in place. All forms include `{% csrf_token %}` for Cross-Site Request Forgery protection.
*   **Deployment:** Consider using Gunicorn/uWSGI with Nginx for production deployment.