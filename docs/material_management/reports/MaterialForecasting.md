The organization's legacy ASP.NET application for Material Forecasting requires a comprehensive modernization to a Django-based solution. This plan outlines the automated conversion process, focusing on the adoption of modern Django 5.0+ patterns, a fat model/thin view architecture, and a highly dynamic user interface built with HTMX and Alpine.js. The goal is to deliver a robust, maintainable, and scalable system with improved performance and user experience, all while minimizing manual coding effort through AI-assisted automation.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code, the system interacts with the following database tables and their relevant columns, which will be mapped to Django models:

-   **`tblSD_WO_Category`**: This table stores Work Order categories used in the dropdown filter.
    -   `CId` (Primary Key, Integer)
    -   `CName` (String, representing the category name)
    -   `Symbol` (String, often a prefix or code for the category)
    -   `CompId` (Integer, likely a Company ID for multi-tenancy filtering)

-   **`SD_Cust_WorkOrder_Master`**: This is the central table containing Work Order details, which is queried and displayed in the main grid. The data displayed (WO No, Project Title, Customer Name, Code) are columns from this table or derived through joins, primarily via the `Sp_ForeCast` stored procedure.
    -   `WOId` (Primary Key, Integer)
    -   `WONo` (String, Work Order Number)
    -   `ProjectTitle` (String)
    -   `CustomerName` (String, appears to be denormalized or from a join)
    -   `Code` (String, possibly an item or material code, appears to be denormalized or from a join)
    -   `CustomerId` (Integer, Foreign Key to `SD_Cust_master`)
    -   `TaskProjectTitle` (String, used for search, might be identical to `ProjectTitle`)
    -   `CId` (Integer, Foreign Key to `tblSD_WO_Category`)
    -   `CompId` (Integer, Company ID)
    -   `FinYearId` (Integer, Financial Year ID)

-   **`SD_Cust_master`**: This table stores customer information, used for the customer search autocomplete feature.
    -   `CustomerId` (Primary Key, Integer)
    -   `CustomerName` (String)
    -   `CompId` (Integer, Company ID)

The `Sp_ForeCast` stored procedure aggregates and filters data from these tables. In Django, its logic will be recreated as a method within a custom model manager for the `WorkOrder` model.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations and business logic in the ASP.NET code.

## Instructions:

The ASP.NET page `MaterialForecasting.aspx` primarily acts as a sophisticated data **Read** interface with extensive filtering capabilities, culminating in a "Proceed" action that redirects to another report.

-   **Data Retrieval & Filtering (Read):**
    -   The core of the application is displaying a list of Work Orders in a `GridView`.
    -   Data is sourced by executing the `Sp_ForeCast` stored procedure, which receives parameters for `CompanyId`, `FinancialYearId`, and dynamic search criteria.
    -   **Filtering Options:** Users can filter the Work Order list using:
        -   **Work Order Category:** A dropdown (`DDLTaskWOType`) populated from `tblSD_WO_Category`.
        -   **Transaction Type:** A dropdown (`DropDownList_Trans`) with static options ("Open", "Close", "Both"). This selection is passed to the next page.
        -   **Item Type:** A dropdown (`DropDownList2`) with static options ("Bought Out Items", "Manufacturing Items"). This selection is also passed to the next page.
        -   **Dynamic Search Field:** A dropdown (`drpfield`) allows choosing whether to search by "Customer", "WO No", or "Project Title".
            -   **Customer Search:** When "Customer" is selected, a textbox (`txtSupplier`) appears with an autocomplete feature pulling from `SD_Cust_master`. The selected customer's `CustomerId` is used for filtering.
            -   **WO No / Project Title Search:** When "WO No" or "Project Title" is selected, a different textbox (`txtPONo`) appears. For "WO No", an exact match is sought on `WONo`; for "Project Title", a partial match (`LIKE '%...%'`) on `TaskProjectTitle` is performed.
    -   **"Search" Button (`Button1`):** Triggers the data reload and re-applies all selected filters.
    -   **Session Context:** The `CompId` (Company ID) and `FinYearId` (Financial Year ID) are retrieved from the user's session to filter data specific to their context.

-   **Work Order Selection and Proceed Action:**
    -   Each row in the `GridView` includes a checkbox for individual Work Order selection.
    -   A "Select All Work Order" checkbox (`SelectAll`) provides a bulk selection/deselection option.
    -   The "Proceed" button (`btnPrint`) gathers the `WONo` of all *selected* Work Orders. These `WONo`s are stored in a session variable (`Session["WorkOrderId"]`) and then the user is redirected to a different page (`ProjectSummary_Shortage.aspx`) with `Item Type` and `Transaction Type` passed as URL query parameters.
    -   **Client-Side Validation:** If the "Proceed" button is clicked without any Work Orders selected, a JavaScript alert message is displayed.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The ASP.NET user interface will be translated into Django templates using a combination of standard HTML elements, Tailwind CSS for styling, and HTMX/Alpine.js for dynamic interactivity.

-   **Main Layout:** The ASP.NET `MasterPageFile` will be replaced by Django's `core/base.html` for consistent layout and shared resources.
-   **Filter Controls:**
    -   `DDLTaskWOType`, `DropDownList_Trans`, `DropDownList2`, `drpfield`: These `asp:DropDownList` controls will become standard HTML `<select>` elements. Their `AutoPostBack="True"` behavior will be managed by HTMX (`hx-trigger="change" hx-get="..."`) to refresh the work order table dynamically without full page reloads.
    -   `txtSupplier` and `txtPONo`: These `asp:TextBox` controls will become HTML `<input type="text">` elements. Their visibility, which is toggled by `drpfield` selection, will be managed by Alpine.js (`x-show` directive).
    -   `AutoCompleteExtender` for `txtSupplier`: This will be replaced by HTMX, using `hx-get` on `keyup changed delay:500ms` to fetch customer suggestions from a dedicated Django view and render them into an overlaying `<div>`.
-   **Action Controls:**
    -   `Button1` ("Search"): This `asp:Button` will become an HTML `<button>` with HTMX attributes to trigger a refresh of the work order table (`hx-get="..." hx-target="..." hx-swap="..."`).
    -   `SelectAll` (Checkbox): This `asp:CheckBox` will be an HTML `<input type="checkbox">` and its functionality to toggle all checkboxes in the grid will be handled by Alpine.js (`x-model`, `x-on:click`).
    -   `btnPrint` ("Proceed"): This `asp:Button` will be an HTML `<button>` inside an HTML `<form>`. The form submission will use HTMX (`hx-post="..." hx-swap="none"`) to process selected items and then trigger an `HX-Redirect` header to navigate to the next page.
-   **Data Display:**
    -   `GridView1`: This `asp:GridView` is the central data display. It will be replaced by a standard HTML `<table>` element.
        -   **DataTables:** The table will be initialized with the DataTables JavaScript library for client-side sorting, searching, and pagination, replicating and enhancing the `GridView`'s capabilities.
        -   **HTMX for Content:** The entire `<table>` or just its `<tbody>` will be a target for HTMX (`hx-target`, `hx-swap`), allowing it to be dynamically updated when filters change or the "Search" button is clicked, without reloading the whole page.
        -   **Checkboxes in Rows:** Each row will include an HTML `<input type="checkbox">` for individual selection, managed by Alpine.js in conjunction with the `SelectAll` checkbox.
-   **Styling:** Custom CSS files like `yui-datatable.css` and `StyleSheet.css` will be replaced by Tailwind CSS utility classes, ensuring a modern and responsive design.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create Django models based on the database schema.

## Instructions:

We will define three models: `WOCategory`, `Customer`, and `WorkOrder`. The `WorkOrder` model will include a custom manager to encapsulate the complex data retrieval logic from the `Sp_ForeCast` stored procedure. `managed = False` is crucial as we are mapping to existing database tables.

```python
# forecasting/models.py
from django.db import models
from django.db.models import Q

class WOCategory(models.Model):
    """
    Maps to tblSD_WO_Category for Work Order Categories.
    """
    cid = models.IntegerField(db_column='CId', primary_key=True)
    symbol = models.CharField(db_column='Symbol', max_length=50, blank=True, null=True)
    cname = models.CharField(db_column='CName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblSD_WO_Category'
        verbose_name = 'WO Category'
        verbose_name_plural = 'WO Categories'

    def __str__(self):
        return f"{self.symbol} - {self.cname}" if self.symbol else self.cname


class Customer(models.Model):
    """
    Maps to SD_Cust_master for Customer details, used in autocomplete.
    """
    customerid = models.IntegerField(db_column='CustomerId', primary_key=True)
    customername = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customername


class WorkOrderManager(models.Manager):
    """
    Custom manager to encapsulate the Sp_ForeCast stored procedure's filtering logic.
    This method dynamically builds the ORM query based on input parameters.
    """
    def get_forecast_data(self, company_id, financial_year_id, wo_category_id=None,
                          search_field=None, search_term=None):
        """
        Filters WorkOrder data based on company, financial year, and search criteria.
        """
        queryset = self.get_queryset().filter(compid=company_id, finyearid=financial_year_id)

        if wo_category_id and wo_category_id != '': # Assuming empty string if "WO Category" selected
            queryset = queryset.filter(cid=wo_category_id)

        if search_term:
            if search_field == '0':  # Customer
                # Find customer ID from name, then filter by it
                try:
                    customer = Customer.objects.using(self.db).get(customername=search_term, compid=company_id)
                    queryset = queryset.filter(customerid=customer.customerid)
                except Customer.DoesNotExist:
                    queryset = queryset.none() # Return empty if customer not found
            elif search_field == '1':  # WO No
                queryset = queryset.filter(wono=search_term)
            elif search_field == '2':  # Project Title
                queryset = queryset.filter(Q(projecttitle__icontains=search_term) | Q(taskprojecttitle__icontains=search_term))
                # Assuming TaskProjectTitle is a field in WorkOrder model

        return queryset.order_by('wono') # Default ordering


class WorkOrder(models.Model):
    """
    Maps to SD_Cust_WorkOrder_Master for Work Order details.
    Includes fields inferred from the GridView and SP parameters.
    """
    woid = models.IntegerField(db_column='WOId', primary_key=True)
    wono = models.CharField(db_column='WONo', max_length=255)
    projecttitle = models.CharField(db_column='ProjectTitle', max_length=255, blank=True, null=True)
    customername = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    code = models.CharField(db_column='Code', max_length=50, blank=True, null=True)
    customerid = models.IntegerField(db_column='CustomerId', blank=True, null=True)
    cid = models.IntegerField(db_column='CId', blank=True, null=True)
    compid = models.IntegerField(db_column='CompId', blank=True, null=True)
    finyearid = models.IntegerField(db_column='FinYearId', blank=True, null=True)
    taskprojecttitle = models.CharField(db_column='TaskProjectTitle', max_length=255, blank=True, null=True) # Added for search

    objects = WorkOrderManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'

    def __str__(self):
        return self.wono

```

### 4.2 Forms

Task: Define a Django form for user input.

## Instructions:

A single `ForecastingFilterForm` will manage all the search and filter inputs from the ASP.NET page. This is not a `ModelForm` as it's for data filtering, not direct model manipulation. HTMX attributes will be included directly in the form's widgets for dynamic interactions.

```python
# forecasting/forms.py
from django import forms
from .models import WOCategory, Customer # Ensure these models are accessible

class ForecastingFilterForm(forms.Form):
    """
    Form for filtering Material Forecasting data.
    """
    # Choices are populated dynamically in __init__ or in the view
    WO_CATEGORY_PLACEHOLDER = 'WO Category'
    TRANSACTION_CHOICES = [
        ('1', 'Open Transaction'),
        ('2', 'Close Transaction'),
        ('3', 'Both Transaction'),
    ]
    ITEM_TYPE_CHOICES = [
        ('1', 'Bought Out Items'),
        ('2', 'Manufacturing Items'),
    ]
    SEARCH_FIELD_CHOICES = [
        ('0', 'Customer'),
        ('1', 'WO No'),
        ('2', 'Project Title'),
    ]

    wo_category = forms.ChoiceField(
        choices=[('', WO_CATEGORY_PLACEHOLDER)], # Initial dummy choice
        required=False,
        label="",
        widget=forms.Select(attrs={'class': 'box3', 'hx-get': '/forecasting/work-orders/table/', 'hx-trigger': 'change', 'hx-target': '#work-order-table-container', 'hx-swap': 'innerHTML', 'hx-include': '#forecasting-filter-form :input'})
    )
    transaction_type = forms.ChoiceField(
        choices=TRANSACTION_CHOICES,
        required=False,
        label="",
        initial='1', # Default to Open Transaction as per ASP.NET
        widget=forms.Select(attrs={'class': 'box3'})
    )
    item_type = forms.ChoiceField(
        choices=ITEM_TYPE_CHOICES,
        required=False,
        label="",
        initial='1', # Default to Bought Out Items as per ASP.NET
        widget=forms.Select(attrs={'class': 'box3'})
    )
    search_field = forms.ChoiceField(
        choices=SEARCH_FIELD_CHOICES,
        required=False,
        label="",
        initial='0', # Default to Customer as per ASP.NET
        widget=forms.Select(attrs={'class': 'box3', 'x-model': 'searchField', 'hx-get': '/forecasting/work-orders/table/', 'hx-trigger': 'change', 'hx-target': '#work-order-table-container', 'hx-swap': 'innerHTML', 'hx-include': '#forecasting-filter-form :input'})
    )
    # Note: search_term_customer and search_term_po are conditionally visible via Alpine.js
    search_term_customer = forms.CharField(
        max_length=250,
        required=False,
        label="",
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Search Customer',
            'x-show': "searchField === '0'", # Alpine.js conditional display
            'hx-get': '/forecasting/work-orders/table/', # Trigger table refresh
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#work-order-table-container',
            'hx-swap': 'innerHTML',
            'hx-include': '#forecasting-filter-form :input',
            'autocomplete': 'off',
            'data-customer-id': '' # To store selected CustomerId from autocomplete
        })
    )
    search_term_po = forms.CharField(
        max_length=250,
        required=False,
        label="",
        widget=forms.TextInput(attrs={
            'class': 'box3',
            'placeholder': 'Search WO No/Project Title',
            'x-show': "searchField === '1' || searchField === '2'", # Alpine.js conditional display
            'hx-get': '/forecasting/work-orders/table/', # Trigger table refresh
            'hx-trigger': 'keyup changed delay:500ms',
            'hx-target': '#work-order-table-container',
            'hx-swap': 'innerHTML',
            'hx-include': '#forecasting-filter-form :input'
        })
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate WO Category choices dynamically
        try:
            wo_category_choices = [(c.cid, f"{c.symbol} - {c.cname}") for c in WOCategory.objects.all().order_by('cname')]
        except Exception:
            # Handle cases where WOCategory might not be synced yet or DB connection issues
            wo_category_choices = []
        self.fields['wo_category'].choices = [('', self.WO_CATEGORY_PLACEHOLDER)] + wo_category_choices

    def clean(self):
        cleaned_data = super().clean()
        search_field = cleaned_data.get('search_field')
        search_term_customer = cleaned_data.get('search_term_customer')
        search_term_po = cleaned_data.get('search_term_po')

        # Consolidate search term based on selected search field
        if search_field == '0':
            cleaned_data['search_term'] = search_term_customer
        elif search_field in ['1', '2']:
            cleaned_data['search_term'] = search_term_po
        else:
            cleaned_data['search_term'] = None # No search term if field is not selected

        return cleaned_data

```

### 4.3 Views

Task: Implement the main view for Material Forecasting and supporting views for HTMX interactions.

## Instructions:

A `ListView` will serve the main page. A separate `ListView` will specifically render the work order table partial for HTMX requests. A `View` will handle the autocomplete for customers, and another `View` will process the "Proceed" action.

```python
# forecasting/views.py
from django.views.generic import ListView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import render, redirect

from .models import WorkOrder, WOCategory, Customer
from .forms import ForecastingFilterForm

class MaterialForecastingListView(ListView):
    """
    Displays the main Material Forecasting page with search filters.
    The actual table content will be loaded via HTMX into a container.
    """
    model = WorkOrder
    template_name = 'forecasting/material_forecasting_list.html'
    context_object_name = 'work_orders' # This is primarily for the form on the initial load

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # The form is passed to the main page to render filter controls
        context['form'] = ForecastingFilterForm(self.request.GET or None) # Initialize with GET params if present
        # Company and financial year IDs would typically come from user session/profile
        context['company_id'] = self.request.session.get('compid', 1) # Example: Default to 1
        context['financial_year_id'] = self.request.session.get('finyear', 1) # Example: Default to 1
        return context

    # Views should be thin, so no complex logic here beyond passing form to template


class WorkOrderTablePartialView(ListView):
    """
    HTMX-specific view to render only the work order table content.
    Triggered by filter changes, search button clicks, or initial load.
    """
    model = WorkOrder
    template_name = 'forecasting/_work_order_table.html' # Partial template
    context_object_name = 'work_orders' # The queryset for the table

    def get_queryset(self):
        # Retrieve company_id and financial_year_id from session/user profile
        company_id = self.request.session.get('compid', 1)
        financial_year_id = self.request.session.get('finyear', 1)

        # Get filter parameters from the request's GET data (sent by HTMX)
        form = ForecastingFilterForm(self.request.GET)
        
        # Validate form to get cleaned data. If invalid, return empty queryset or handle errors.
        if form.is_valid():
            wo_category_id = form.cleaned_data.get('wo_category')
            search_field = form.cleaned_data.get('search_field')
            search_term = form.cleaned_data.get('search_term')
        else:
            # If form is invalid (e.g., missing data), provide default or empty results
            wo_category_id = None
            search_field = None
            search_term = None
            # Log form.errors if debugging needed

        # Use the custom manager method to fetch filtered data
        queryset = WorkOrder.objects.get_forecast_data(
            company_id=company_id,
            financial_year_id=financial_year_id,
            wo_category_id=wo_category_id,
            search_field=search_field,
            search_term=search_term
        )
        return queryset

    def render_to_response(self, context, **response_kwargs):
        # This view is for HTMX, it will always render a partial.
        # No redirect or message handling required at this stage.
        return super().render_to_response(context, **response_kwargs)


class CustomerAutocompleteView(View):
    """
    HTMX view for customer name autocomplete suggestions.
    Returns a partial HTML list of suggestions, or JSON if preferred.
    """
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        company_id = request.session.get('compid', 1) # Dummy company_id

        if query:
            customers = Customer.objects.using('default').filter(
                customername__icontains=query,
                compid=company_id
            ).order_by('customername')[:10] # Limit suggestions to 10
        else:
            customers = Customer.objects.none()

        context = {'customers': customers}
        # Render a partial template for suggestions
        return render(request, 'forecasting/_customer_autocomplete_suggestions.html', context)


class ProcessSelectedWorkOrdersView(View):
    """
    Handles the "Proceed" button logic. Collects selected work order IDs,
    stores relevant data in session, and redirects to the next report page.
    """
    def post(self, request, *args, **kwargs):
        selected_wo_ids = request.POST.getlist('selected_work_orders')
        item_type = request.POST.get('item_type')
        transaction_type = request.POST.get('transaction_type')

        if not selected_wo_ids:
            # Use Django messages framework for user feedback
            messages.error(request, 'Please select at least one Work Order to proceed.')
            # For HTMX, trigger a client-side message or just re-render
            # HX-Trigger allows custom events for Alpine.js to listen to
            if request.headers.get('HX-Request'):
                return HttpResponse(status=204, headers={'HX-Trigger': '{"showAlert": {"message": "Please select at least one Work Order.", "type": "error"}}'})
            return redirect(reverse_lazy('forecasting_list')) # Fallback for non-HTMX requests
        
        # Get WorkOrder Numbers for selected IDs as per original ASP.NET logic
        selected_wo_nos = []
        for woid_str in selected_wo_ids:
            try:
                woid = int(woid_str)
                wo = WorkOrder.objects.get(woid=woid)
                selected_wo_nos.append(wo.wono)
            except (ValueError, WorkOrder.DoesNotExist):
                # Handle cases where ID is invalid or WO no longer exists
                continue

        request.session['WorkOrderId'] = ','.join(selected_wo_nos)

        # Build the redirect URL replicating ASP.NET's query parameters
        redirect_url = reverse_lazy('project_summary_shortage') # This URL needs to be defined in your project's urls.py
        redirect_url = f"{redirect_url}?ModId=6&SwitchTo={item_type}&Trans={transaction_type}"

        # For HTMX, use HX-Redirect header for client-side navigation
        if request.headers.get('HX-Request'):
            return HttpResponse(status=204, headers={'HX-Redirect': redirect_url})
        
        return redirect(redirect_url)

```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates will utilize DRY principles with `core/base.html` inheritance, Tailwind CSS for styling, and DataTables for client-side table enhancements. HTMX will manage dynamic content updates, and Alpine.js will handle UI state and interactions.

```html
<!-- forecasting/templates/forecasting/material_forecasting_list.html -->
{% extends 'core/base.html' %}

{% block title %}Material Forecasting{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ searchField: '{{ form.search_field.value|default:'0' }}' }">
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4 border-b pb-2">Material Forecasting</h2>
        
        <form id="forecasting-filter-form" class="space-y-4">
            <div class="flex items-center space-x-4 flex-wrap gap-y-2">
                <div>
                    {{ form.wo_category }}
                </div>
                <div>
                    {{ form.transaction_type }}
                </div>
                <div>
                    {{ form.item_type }}
                </div>
                <div>
                    {{ form.search_field }}
                </div>
                
                <!-- Search term inputs with Alpine.js x-show for conditional visibility -->
                <div x-show="searchField === '0'" class="relative" x-cloak>
                    {{ form.search_term_customer }}
                    <div id="customer-suggestions" class="absolute z-10 bg-white border border-gray-300 rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto">
                        <!-- Autocomplete suggestions will be loaded here via HTMX -->
                    </div>
                </div>
                <div x-show="searchField === '1' || searchField === '2'" x-cloak>
                    {{ form.search_term_po }}
                </div>

                <button type="button" 
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md"
                        hx-get="{% url 'forecasting_work_order_table' %}"
                        hx-target="#work-order-table-container"
                        hx-swap="innerHTML"
                        hx-include="#forecasting-filter-form :input">
                    Search
                </button>
            </div>
        </form>
    </div>

    <!-- Container for the HTMX-loaded DataTables table -->
    <div id="work-order-table-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'forecasting_work_order_table' %}"
         hx-swap="innerHTML"
         hx-include="#forecasting-filter-form :input"
         class="bg-white shadow-md rounded-lg p-6 relative min-h-[200px]">
        <!-- Initial loading indicator -->
        <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75" hx-indicator="on show: #work-order-table-container .loading-indicator; on hide: #work-order-table-container .loading-indicator">
            <div class="loading-indicator hidden text-center">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading Work Orders...</p>
            </div>
        </div>
        <!-- Table will be loaded here via HTMX -->
    </div>

    <div class="flex justify-center mt-6">
        <form hx-post="{% url 'forecasting_process_selected' %}" hx-swap="none" x-data="{ selectedCount: 0, selectAll: false }" x-init="$watch('selectAll', value => {
            document.querySelectorAll('#work-order-table-container input[type=\"checkbox\"][name=\"selected_work_orders\"]').forEach(cb => cb.checked = value);
            selectedCount = document.querySelectorAll('#work-order-table-container input[type=\"checkbox\"][name=\"selected_work_orders\"]:checked').length;
        }); $watch('selectedCount', count => {
            if (count === 0 && selectAll) selectAll = false;
        });"
        @showAlert.window="alert($event.detail.message)">
            {% csrf_token %}
            <input type="hidden" name="transaction_type" :value="document.getElementById('id_transaction_type').value">
            <input type="hidden" name="item_type" :value="document.getElementById('id_item_type').value">
            
            <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-6 rounded-md shadow-lg">
                Proceed ({{ selectedCount }})
            </button>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('forecastingPage', () => ({
            searchField: '{{ form.search_field.value|default:'0' }}',
            init() {
                // Initialize selectedCount on page load based on existing checks
                this.$nextTick(() => {
                    const checkboxes = document.querySelectorAll('#work-order-table-container input[type="checkbox"][name="selected_work_orders"]');
                    this.selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
                    this.selectAll = checkboxes.length > 0 && this.selectedCount === checkboxes.length;
                });
            },
            updateSelectedCount() {
                this.selectedCount = document.querySelectorAll('#work-order-table-container input[type="checkbox"][name="selected_work_orders"]:checked').length;
                const totalCheckboxes = document.querySelectorAll('#work-order-table-container input[type="checkbox"][name="selected_work_orders"]').length;
                this.selectAll = totalCheckboxes > 0 && this.selectedCount === totalCheckboxes;
            }
        }));

        // Attach event listener for the HTMX table update event
        document.body.addEventListener('htmx:afterSwap', function(event) {
            if (event.detail.target.id === 'work-order-table-container') {
                // Destroy existing DataTable instance if it exists
                if ($.fn.DataTable.isDataTable('#work-order-table')) {
                    $('#work-order-table').DataTable().destroy();
                }
                // Initialize new DataTable instance
                $('#work-order-table').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [1, 2] } // Disable sorting for SN, Checkbox, Actions
                    ]
                });

                // Update Alpine.js state after table reloads
                const forecastingPage = document.querySelector('[x-data]').__alpine.$data;
                if (forecastingPage && typeof forecastingPage.updateSelectedCount === 'function') {
                    forecastingPage.updateSelectedCount();
                }

                // Attach event listener for individual checkboxes
                document.querySelectorAll('#work-order-table-container input[type="checkbox"][name="selected_work_orders"]').forEach(cb => {
                    cb.addEventListener('change', () => {
                        if (forecastingPage && typeof forecastingPage.updateSelectedCount === 'function') {
                            forecastingPage.updateSelectedCount();
                        }
                    });
                });
            }
        });
    });
</script>
{% endblock %}

```

```html
<!-- forecasting/templates/forecasting/_work_order_table.html -->
<!-- This partial template is loaded via HTMX into #work-order-table-container -->
<table id="work-order-table" class="min-w-full divide-y divide-gray-200">
    <thead class="bg-gray-50">
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                <input type="checkbox" id="selectAllCheckbox" x-model="selectAll" 
                       class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded">
            </th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Project Title</th>
            <th class="py-2 px-4 border-b border-gray-200 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
            <th class="py-2 px-4 border-b border-gray-200 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
        </tr>
    </thead>
    <tbody class="bg-white divide-y divide-gray-200">
        {% if work_orders %}
            {% for wo in work_orders %}
            <tr>
                <td class="py-2 px-4 border-b border-gray-200 text-right text-sm text-gray-700">{{ forloop.counter }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center">
                    <input type="checkbox" name="selected_work_orders" value="{{ wo.woid }}" 
                           class="form-checkbox h-4 w-4 text-blue-600 border-gray-300 rounded"
                           @change="updateSelectedCount">
                </td>
                <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-700">{{ wo.wono }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-700">{{ wo.projecttitle }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-left text-sm text-gray-700">{{ wo.customername }}</td>
                <td class="py-2 px-4 border-b border-gray-200 text-center text-sm text-gray-700">{{ wo.code }}</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="6" class="py-4 text-center text-gray-500 text-sm">No data found to display</td>
            </tr>
        {% endif %}
    </tbody>
</table>

<!-- DataTables initialization script -->
<script>
    // This script block will be re-executed by HTMX after the table content is swapped.
    // The main list.html will destroy and re-initialize it.
    // For standalone testing of partial, you might put $(document).ready here.
    // But as per instructions, list.html block extra_js will handle it.
</script>

```

```html
<!-- forecasting/templates/forecasting/_customer_autocomplete_suggestions.html -->
<!-- This partial is loaded into #customer-suggestions div for autocomplete -->
{% if customers %}
    {% for customer in customers %}
        <div class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm"
             @click="document.getElementById('id_search_term_customer').value = '{{ customer.customername }}'; 
                     document.getElementById('id_search_term_customer').dispatchEvent(new Event('change')); 
                     document.getElementById('customer-suggestions').innerHTML = '';">
            {{ customer.customername }} <span class="text-gray-500 text-xs">[{{ customer.customerid }}]</span>
        </div>
    {% endfor %}
{% else %}
    <div class="px-4 py-2 text-gray-500 text-sm">No suggestions found.</div>
{% endif %}
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

URL patterns will be defined in `forecasting/urls.py` for the main list view, the HTMX table partial, the customer autocomplete endpoint, and the "Proceed" action handler.

```python
# forecasting/urls.py
from django.urls import path
from .views import MaterialForecastingListView, WorkOrderTablePartialView, CustomerAutocompleteView, ProcessSelectedWorkOrdersView

urlpatterns = [
    # Main page for Material Forecasting with filters
    path('material-forecasting/', MaterialForecastingListView.as_view(), name='forecasting_list'),

    # HTMX endpoint for dynamic loading/refreshing of the work order table
    path('work-orders/table/', WorkOrderTablePartialView.as_view(), name='forecasting_work_order_table'),

    # HTMX endpoint for customer name autocomplete suggestions
    path('customers/autocomplete/', CustomerAutocompleteView.as_view(), name='forecasting_customer_autocomplete'),

    # Endpoint to process selected work orders and redirect
    path('process-selected-work-orders/', ProcessSelectedWorkOrdersView.as_view(), name='forecasting_process_selected'),
]

# Don't forget to include these URLs in your project's main urls.py (e.g., config/urls.py)
# from django.urls import include, path
# urlpatterns = [
#     path('forecasting/', include('forecasting.urls')),
#     # Define project_summary_shortage URL here or in another app's urls.py
#     path('project-summary-shortage/', YourProjectSummaryView.as_view(), name='project_summary_shortage'),
# ]
```

### 4.6 Tests

Task: Write tests for the model and views.

## Instructions:

Comprehensive unit tests will cover the model's custom manager logic to ensure filtering works correctly. Integration tests will verify that views return appropriate HTTP responses, use correct templates, and handle HTMX interactions as expected.

```python
# forecasting/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.db import connection
from .models import WOCategory, Customer, WorkOrder
from .forms import ForecastingFilterForm

class WOCategoryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        WOCategory.objects.create(cid=1, symbol='MFR', cname='Manufacturing', compid=cls.company_id)
        WOCategory.objects.create(cid=2, symbol='PUR', cname='Purchase', compid=cls.company_id)

    def test_wocategory_creation(self):
        category = WOCategory.objects.get(cid=1)
        self.assertEqual(category.cname, 'Manufacturing')
        self.assertEqual(str(category), 'MFR - Manufacturing')
        self.assertEqual(category._meta.db_table, 'tblSD_WO_Category')
        self.assertFalse(category._meta.managed)

class CustomerModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        Customer.objects.create(customerid=101, customername='Alpha Corp', compid=cls.company_id)
        Customer.objects.create(customerid=102, customername='Beta Inc', compid=cls.company_id)

    def test_customer_creation(self):
        customer = Customer.objects.get(customerid=101)
        self.assertEqual(customer.customername, 'Alpha Corp')
        self.assertEqual(str(customer), 'Alpha Corp')
        self.assertEqual(customer._meta.db_table, 'SD_Cust_master')
        self.assertFalse(customer._meta.managed)

class WorkOrderModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.financial_year_id = 2023
        WOCategory.objects.create(cid=1, symbol='MFR', cname='Manufacturing', compid=cls.company_id)
        Customer.objects.create(customerid=101, customername='Alpha Corp', compid=cls.company_id)
        Customer.objects.create(customerid=102, customername='Beta Inc', compid=cls.company_id)

        WorkOrder.objects.create(
            woid=1, wono='WO001', projecttitle='Project X', customername='Alpha Corp',
            code='C1', customerid=101, cid=1, compid=cls.company_id, finyearid=cls.financial_year_id,
            taskprojecttitle='Project X Phase 1'
        )
        WorkOrder.objects.create(
            woid=2, wono='WO002', projecttitle='Project Y', customername='Beta Inc',
            code='C2', customerid=102, cid=1, compid=cls.company_id, finyearid=cls.financial_year_id,
            taskprojecttitle='Project Y Planning'
        )
        WorkOrder.objects.create(
            woid=3, wono='WO003', projecttitle='Project Z', customername='Alpha Corp',
            code='C3', customerid=101, cid=2, compid=cls.company_id, finyearid=cls.financial_year_id
        )
        # WO from different company
        WorkOrder.objects.create(
            woid=4, wono='WO004', projecttitle='Foreign Project', customername='Gamma Ltd',
            code='C4', customerid=103, cid=1, compid=2, finyearid=cls.financial_year_id
        )

    def test_workorder_creation(self):
        wo = WorkOrder.objects.get(woid=1)
        self.assertEqual(wo.wono, 'WO001')
        self.assertEqual(wo.projecttitle, 'Project X')
        self.assertEqual(wo._meta.db_table, 'SD_Cust_WorkOrder_Master')
        self.assertFalse(wo._meta.managed)

    def test_get_forecast_data_basic(self):
        queryset = WorkOrder.objects.get_forecast_data(self.company_id, self.financial_year_id)
        self.assertEqual(queryset.count(), 3)
        self.assertIn(WorkOrder.objects.get(woid=1), queryset)
        self.assertIn(WorkOrder.objects.get(woid=2), queryset)
        self.assertIn(WorkOrder.objects.get(woid=3), queryset)
        self.assertNotIn(WorkOrder.objects.get(woid=4), queryset) # Different company_id

    def test_get_forecast_data_wo_category_filter(self):
        queryset = WorkOrder.objects.get_forecast_data(self.company_id, self.financial_year_id, wo_category_id='1')
        self.assertEqual(queryset.count(), 2)
        self.assertIn(WorkOrder.objects.get(woid=1), queryset)
        self.assertIn(WorkOrder.objects.get(woid=2), queryset)
        self.assertNotIn(WorkOrder.objects.get(woid=3), queryset)

    def test_get_forecast_data_customer_search(self):
        queryset = WorkOrder.objects.get_forecast_data(self.company_id, self.financial_year_id,
                                                       search_field='0', search_term='Alpha Corp')
        self.assertEqual(queryset.count(), 2)
        self.assertIn(WorkOrder.objects.get(woid=1), queryset)
        self.assertIn(WorkOrder.objects.get(woid=3), queryset)
        self.assertNotIn(WorkOrder.objects.get(woid=2), queryset)

    def test_get_forecast_data_wo_no_search(self):
        queryset = WorkOrder.objects.get_forecast_data(self.company_id, self.financial_year_id,
                                                       search_field='1', search_term='WO002')
        self.assertEqual(queryset.count(), 1)
        self.assertIn(WorkOrder.objects.get(woid=2), queryset)

    def test_get_forecast_data_project_title_search(self):
        queryset = WorkOrder.objects.get_forecast_data(self.company_id, self.financial_year_id,
                                                       search_field='2', search_term='Project X')
        self.assertEqual(queryset.count(), 1) # Matches Project X Phase 1
        self.assertIn(WorkOrder.objects.get(woid=1), queryset)
        
        queryset = WorkOrder.objects.get_forecast_data(self.company_id, self.financial_year_id,
                                                       search_field='2', search_term='Planning')
        self.assertEqual(queryset.count(), 1) # Matches Project Y Planning
        self.assertIn(WorkOrder.objects.get(woid=2), queryset)

    def test_get_forecast_data_no_search_term(self):
        queryset = WorkOrder.objects.get_forecast_data(self.company_id, self.financial_year_id,
                                                       search_field='0', search_term='')
        self.assertEqual(queryset.count(), 3) # Should return all for current company/finyear

class ForecastingFormsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        WOCategory.objects.create(cid=1, symbol='MFR', cname='Manufacturing', compid=cls.company_id)
        WOCategory.objects.create(cid=2, symbol='PUR', cname='Purchase', compid=cls.company_id)
        Customer.objects.create(customerid=101, customername='Alpha Corp', compid=cls.company_id)

    def test_form_initialization(self):
        form = ForecastingFilterForm()
        self.assertIn(('', 'WO Category'), form.fields['wo_category'].choices)
        self.assertIn((1, 'MFR - Manufacturing'), form.fields['wo_category'].choices)
        self.assertEqual(form.fields['search_field'].initial, '0')

    def test_form_valid_data_customer_search(self):
        data = {
            'wo_category': '', 'transaction_type': '1', 'item_type': '1', 'search_field': '0',
            'search_term_customer': 'Alpha Corp', 'search_term_po': ''
        }
        form = ForecastingFilterForm(data=data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['search_term'], 'Alpha Corp')

    def test_form_valid_data_wo_no_search(self):
        data = {
            'wo_category': '', 'transaction_type': '1', 'item_type': '1', 'search_field': '1',
            'search_term_customer': '', 'search_term_po': 'WO001'
        }
        form = ForecastingFilterForm(data=data)
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['search_term'], 'WO001')

    def test_form_invalid_data(self):
        data = {'search_field': '0', 'search_term_customer': 'A' * 251} # Too long
        form = ForecastingFilterForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('search_term_customer', form.errors)

class MaterialForecastingViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_id = 1
        cls.financial_year_id = 2023
        WOCategory.objects.create(cid=1, symbol='MFR', cname='Manufacturing', compid=cls.company_id)
        Customer.objects.create(customerid=101, customername='Alpha Corp', compid=cls.company_id)
        WorkOrder.objects.create(
            woid=1, wono='WO001', projecttitle='Project X', customername='Alpha Corp',
            code='C1', customerid=101, cid=1, compid=cls.company_id, finyearid=cls.financial_year_id,
            taskprojecttitle='Project X Phase 1'
        )
        WorkOrder.objects.create(
            woid=2, wono='WO002', projecttitle='Project Y', customername='Alpha Corp',
            code='C2', customerid=101, cid=1, compid=cls.company_id, finyearid=cls.financial_year_id,
            taskprojecttitle='Project Y Planning'
        )

    def setUp(self):
        self.client = Client()
        self.client.session['compid'] = self.company_id
        self.client.session['finyear'] = self.financial_year_id

    def test_material_forecasting_list_view(self):
        response = self.client.get(reverse('forecasting_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'forecasting/material_forecasting_list.html')
        self.assertIsInstance(response.context['form'], ForecastingFilterForm)
        self.assertEqual(response.context['company_id'], self.company_id)
        self.assertEqual(response.context['financial_year_id'], self.financial_year_id)

    def test_work_order_table_partial_view_no_filters(self):
        response = self.client.get(reverse('forecasting_work_order_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'forecasting/_work_order_table.html')
        self.assertIn('work_orders', response.context)
        self.assertEqual(len(response.context['work_orders']), 2)

    def test_work_order_table_partial_view_with_category_filter(self):
        response = self.client.get(reverse('forecasting_work_order_table'), {'wo_category': '1'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['work_orders']), 2)

    def test_work_order_table_partial_view_with_customer_search(self):
        response = self.client.get(reverse('forecasting_work_order_table'), {'search_field': '0', 'search_term_customer': 'Alpha Corp'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['work_orders']), 2)

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('forecasting_customer_autocomplete'), {'q': 'alpha'})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'forecasting/_customer_autocomplete_suggestions.html')
        self.assertIn('customers', response.context)
        self.assertEqual(len(response.context['customers']), 1)
        self.assertEqual(response.context['customers'][0].customername, 'Alpha Corp')

    def test_process_selected_work_orders_view_no_selection(self):
        response = self.client.post(reverse('forecasting_process_selected'), {'item_type': '1', 'transaction_type': '1'}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('showAlert', response.headers['HX-Trigger'])
        self.assertIsNone(self.client.session.get('WorkOrderId'))

    def test_process_selected_work_orders_view_with_selection(self):
        data = {
            'selected_work_orders': ['1', '2'],
            'item_type': '1',
            'transaction_type': '2'
        }
        response = self.client.post(reverse('forecasting_process_selected'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(self.client.session['WorkOrderId'], 'WO001,WO002')
        expected_redirect_url = reverse_lazy('project_summary_shortage') + '?ModId=6&SwitchTo=1&Trans=2'
        self.assertEqual(response.headers['HX-Redirect'], expected_redirect_url)

    def test_process_selected_work_orders_view_with_selection_non_htmx(self):
        data = {
            'selected_work_orders': ['1'],
            'item_type': '1',
            'transaction_type': '2'
        }
        response = self.client.post(reverse('forecasting_process_selected'), data)
        self.assertEqual(response.status_code, 302) # Redirect for non-HTMX
        self.assertEqual(self.client.session['WorkOrderId'], 'WO001')
        expected_redirect_url = reverse_lazy('project_summary_shortage') + '?ModId=6&SwitchTo=1&Trans=2'
        self.assertRedirects(response, expected_redirect_url)

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The integration of HTMX and Alpine.js is critical for delivering a dynamic, single-page application-like experience without complex JavaScript frameworks.

-   **HTMX for Dynamic Content and Forms:**
    -   All filter dropdowns (`wo_category`, `search_field`) and search textboxes (`search_term_customer`, `search_term_po`) are configured with `hx-get` attributes pointing to `{% url 'forecasting_work_order_table' %}`. They use `hx-trigger="change"` or `hx-trigger="keyup changed delay:500ms"` to automatically refresh the work order table.
    -   The main "Search" button explicitly triggers the table refresh using `hx-get`.
    -   `hx-target="#work-order-table-container"` and `hx-swap="innerHTML"` ensure only the table section is updated.
    -   `hx-include="#forecasting-filter-form :input"` sends all current form values with the HTMX request, allowing the Django view to apply all filters.
    -   The "Proceed" form (`<form hx-post="..." hx-swap="none">`) submits selected work order IDs and other parameters via HTMX. Upon success, the Django view returns an `HX-Redirect` header, causing the client to navigate to the next page.
    -   Client-side alerts (e.g., "Please select Workorder") are handled by the Django view returning an `HX-Trigger` header, which Alpine.js listens for to show a custom alert (`@showAlert.window`).
    -   Customer autocomplete (`_customer_autocomplete_suggestions.html`) is loaded into a dynamic `div` using `hx-get` on `id_search_term_customer`'s `keyup` event.

-   **Alpine.js for UI State and Interactivity:**
    -   `x-data="{ searchField: '...' }"`: Manages the state of the selected search field, allowing conditional display of `search_term_customer` or `search_term_po` using `x-show`.
    -   `x-data="{ selectedCount: 0, selectAll: false }"` on the "Proceed" form: Manages the count of selected checkboxes and the state of the "Select All" checkbox.
    -   `x-model="selectAll"`: Binds the master checkbox to the `selectAll` Alpine.js variable.
    -   `x-on:click` or `@change` on individual checkboxes: Updates `selectedCount` and `selectAll` status.
    -   `x-init` and `x-watch`: Used to synchronize the "Select All" checkbox with individual checkboxes and update the `selectedCount` dynamically.
    -   `@showAlert.window`: A custom event listener for messages sent by HTMX's `HX-Trigger`, enabling client-side display of notifications.
    -   Customer autocomplete suggestion clicks: Alpine.js `x-on:click` is used to populate the search textbox and clear the suggestions.

-   **DataTables for List Views:**
    -   The `_work_order_table.html` partial contains the `<table>` element with `id="work-order-table"`.
    -   In `material_forecasting_list.html`'s `extra_js` block, a JavaScript function is set up to `destroy()` any existing DataTable instance and then `initialize()` a new one on the `#work-order-table` *after* HTMX swaps the content (`htmx:afterSwap` event).
    -   This ensures that DataTables is correctly applied to the newly loaded table content, providing client-side search, sort, and pagination.

-   **No Full Page Reloads:** All filtering, searching, and form submissions are handled via HTMX, ensuring only necessary parts of the page are updated, providing a fast and seamless user experience.

## Final Notes

-   **Placeholders:** Replace `{{ company_id }}`, `{{ financial_year_id }}` with actual session management or user-profile based retrieval in a production environment.
-   **Error Handling:** The provided code includes basic `try-catch` in C# and some `if not selected_wo_ids` in Django. Robust error handling (e.g., form errors, database exceptions, user permissions) should be fully implemented in a production system.
-   **Security:** Ensure proper authentication and authorization (e.g., using Django's `LoginRequiredMixin` for views) are in place. Session management is assumed.
-   **Database Connection:** The Django `settings.py` must be configured with the correct database connection for the existing SQL Server database (e.g., using `django-pyodbc-azure` or `django-mssql-backend`). Remember to set `NAME`, `HOST`, `PORT`, `USER`, `PASSWORD`, `OPTIONS` for `ODBC_DRIVER`.
-   **Project URL Integration:** Remember to include `path('forecasting/', include('forecasting.urls'))` in your main Django project's `urls.py`. Also define `project_summary_shortage` URL.
-   **CSS Integration:** Ensure Tailwind CSS is correctly set up and compiled within your Django project.
-   **JavaScript Dependencies:** Ensure jQuery (for DataTables), DataTables, HTMX, and Alpine.js are correctly included in your `core/base.html` template via CDN or local assets.