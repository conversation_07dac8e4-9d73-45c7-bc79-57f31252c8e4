This modernization plan outlines the strategic transition of your ASP.NET Vendor Rating application to a robust, modern Django 5.0+ solution. We will leverage AI-assisted automation to systematically convert your existing business logic and presentation layer, ensuring a smooth and efficient migration. Our focus is on creating a maintainable, high-performance, and scalable system with minimal manual coding, emphasizing HTMX and Alpine.js for dynamic interfaces and DataTables for superior data presentation.

## ASP.NET to Django Conversion Script: Vendor Rating Module

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module (`vendormanagement`).
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table` where applicable for *source* data. For *derived* report data, calculations will be performed in custom managers.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code is not directly querying a single table for the `VendorRating_Print` page. Instead, it constructs a complex `DataTable` (`dt`) by joining multiple tables (`tblMM_PO_Master`, `tblMM_PO_Details`, `tblDG_Item_Master`, `Unit_Master`, `tblInv_Inward_Master`, `tblInv_Inward_Details`, `tblinv_MaterialReceived_Master`, `tblinv_MaterialReceived_Details`, `tblQc_MaterialQuality_Master`, `tblQc_MaterialQuality_Details`, `tblMM_PR_Master`, `tblMM_PR_Details`, `tblMM_SPR_Master`, `tblMM_SPR_Details`, `tblMM_Supplier_master`, `tblcountry`, `tblState`, `tblCity`) and performing extensive calculations. This `dt` is then passed to Crystal Reports.

**Instructions & Inference:**
Since the core data displayed is a *derived report*, we will define a Django model that represents the *structure of this derived data* (`dt`), and a custom manager that encapsulates the complex C# logic to populate instances of this model. For the underlying *source* tables, they would be mapped as `managed=False` models in a broader migration context.

**Derived Report Table Columns (from C# `dt`):**
- `ItemCode` (string) -> `item_code`
- `ManfDesc` (string) -> `manufacturer_desc`
- `UOMBasic` (string) -> `uom_basic` (Unit of Measure)
- `RecedQty` (double) -> `received_qty`
- `NormalAccQty` (double) -> `accepted_qty`
- `DeviatedQty` (double) -> `deviated_qty`
- `SegregatedQty` (double) -> `segregated_qty`
- `RejectedQty` (double) -> `rejected_qty`
- `SupId` (string) -> `supplier_id`
- `CompId` (int) -> `company_id`
- `Delrate` (double) -> `delivery_rate`
- `OverallRating` (double) -> `overall_rating`
- `Rating` (double) -> `quality_rating` (from `rating = (((AccQty * 1) + (DevQty * 0.70) + (SegQty * 0.50)) * 100) / (RecQty)`)

**Underlying Source Tables (for conceptual mapping, would be separate `managed=False` models):**
- `tblMM_PO_Master` (Purchase Order Master)
- `tblMM_PO_Details` (Purchase Order Details)
- `tblMM_PR_Master` (Purchase Requisition Master)
- `tblMM_PR_Details` (Purchase Requisition Details)
- `tblMM_SPR_Master` (Supplier Performance Report Master)
- `tblMM_SPR_Details` (Supplier Performance Report Details)
- `tblDG_Item_Master` (Item Master)
- `Unit_Master` (Unit of Measure Master)
- `tblInv_Inward_Master` (Inventory Inward Master)
- `tblInv_Inward_Details` (Inventory Inward Details)
- `tblinv_MaterialReceived_Master` (Material Received Master)
- `tblinv_MaterialReceived_Details` (Material Received Details)
- `tblQc_MaterialQuality_Master` (Quality Control Material Quality Master)
- `tblQc_MaterialQuality_Details` (Quality Control Material Quality Details)
- `tblMM_Supplier_master` (Supplier Master)
- `tblcountry`, `tblState`, `tblCity` (Location data)

### Step 2: Identify Backend Functionality

**Functionality Analysis:**
The ASP.NET page is purely for *displaying* reports based on filtering parameters. There are no direct Create, Update, or Delete operations on the underlying data from this specific page.

-   **Read:** This is the primary function. The application reads various related records from the database, performs complex calculations and aggregations (`rating`, `DelRate`, `orating`), and then displays them. The report is presented in different "tabs" (Overall, Quality, Delivery) which likely represent different aggregations or views of the same calculated data.
-   **Create/Update/Delete:** Not applicable for this report page.
-   **Validation Logic:** Input parameters (`SupCode`, `FD`, `TD`, `Val`) are retrieved from query strings. Date parsing (`fun.FromDate()`) and basic conditional logic (`switch (Request.QueryString["Val"])`) are present. This will be handled by Django Forms and view logic.

### Step 3: Infer UI Components

**UI Components Analysis:**
-   **`AjaxControlToolkit:TabContainer` (`TabContainer1`)**: This component provides the tabbed interface (Overall Rating, Quality Rating, Delivery Rating). In Django, this will be replicated using simple HTML `div`s with `hx-get` attributes on tab headers to dynamically load content via HTMX, and Alpine.js to manage the active tab state.
-   **`CR:CrystalReportViewer`**: These are placeholders for Crystal Reports. In Django, the calculated data will be rendered in standard HTML `<table>` elements, enhanced with DataTables for interactivity (sorting, filtering, pagination). Each tab will have its own DataTables instance, possibly sharing the underlying data but presenting different aggregations.
-   **`asp:Panel` with `ScrollBars="Auto"`**: Indicates content that might exceed fixed dimensions, suitable for scrollable DataTables.
-   **`asp:Button` (`Button1`)**: A simple "Cancel" button, which redirects to `VendorRating.aspx`. In Django, this will be a simple `<button>` or `<a>` tag with a link.
-   **Input Parameters:** The C# code fetches `SupCode`, `FD`, `TD`, `Val` from query string. These would typically be provided via a filter form.

### Step 4: Generate Django Code

We will create a new Django app, `vendormanagement`.

#### 4.1 Models (vendormanagement/models.py)

**Explanation:**
For this complex reporting scenario, we will define a `VendorRatingItem` model that conceptually represents a single *calculated row* in the final report, analogous to the `dt` `DataTable` in the ASP.NET code. This model will *not* be managed by Django's ORM in the traditional sense (`managed = False` is not appropriate here as it's not a direct database table, but a *derived data structure*). Instead, its instances will be populated by a custom manager which executes the complex calculation logic.

We will also define placeholder models for the *actual* underlying database tables (`tblMM_PO_Master`, `tblDG_Item_Master`, etc.) that would be mapped using `managed = False` if they existed in the database and we needed direct ORM interaction. These are crucial for the `VendorRatingManager` to query.

```python
from django.db import models
from datetime import date, datetime, timedelta
from django.db.models import F, Sum, Avg
from django.db import connection

# --- Placeholder Models for underlying ASP.NET database tables ---
# These models would be generated by inspectdb or manually mapped if
# we needed direct ORM access to these existing tables.
# They are included to illustrate the data sources for calculations.

class Company(models.Model):
    # Assuming 'CompId' maps to 'id' or a specific field
    id = models.IntegerField(db_column='CompId', primary_key=True)
    name = models.CharField(db_column='CompanyName', max_length=255)
    address = models.TextField(db_column='Address') # Placeholder for company address
    # Add other fields as necessary
    class Meta:
        managed = False
        db_table = 'tblCompanyMaster' # Assuming a table for Company details
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    @classmethod
    def get_company_address(cls, comp_id):
        try:
            company = cls.objects.get(id=comp_id)
            return company.address # Or construct a more detailed address string
        except cls.DoesNotExist:
            return "Company Address Not Found"

class Supplier(models.Model):
    supplier_id = models.CharField(db_column='SupplierId', primary_key=True, max_length=50)
    supplier_name = models.CharField(db_column='SupplierName', max_length=255)
    regd_address = models.TextField(db_column='RegdAddress')
    regd_country = models.CharField(db_column='RegdCountry', max_length=50) # Assuming IDs for country/state/city
    regd_state = models.CharField(db_column='RegdState', max_length=50)
    regd_city = models.CharField(db_column='RegdCity', max_length=50)
    regd_pin_no = models.CharField(db_column='RegdPinNo', max_length=20)

    class Meta:
        managed = False
        db_table = 'tblMM_Supplier_master'
        verbose_name = 'Supplier'
        verbose_name_plural = 'Suppliers'

    def get_full_address(self):
        # This would ideally query Country, State, City lookup tables
        # For demonstration, we'll use placeholder direct names or query them if mapped
        country_name = self._get_lookup_name('tblcountry', 'CId', 'CountryName', self.regd_country)
        state_name = self._get_lookup_name('tblState', 'SId', 'StateName', self.regd_state)
        city_name = self._get_lookup_name('tblCity', 'CityId', 'CityName', self.regd_city)
        
        address_parts = [self.regd_address]
        if city_name: address_parts.append(city_name)
        if state_name: address_parts.append(state_name)
        if country_name: address_parts.append(country_name)
        if self.regd_pin_no: address_parts.append(self.regd_pin_no)
        
        return ", ".join(filter(None, address_parts))

    def _get_lookup_name(self, table_name, id_col, name_col, pk_value):
        if not pk_value:
            return None
        with connection.cursor() as cursor:
            cursor.execute(f"SELECT {name_col} FROM {table_name} WHERE {id_col} = %s", [pk_value])
            result = cursor.fetchone()
            return result[0] if result else None

class ItemMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100)
    manufacturer_desc = models.CharField(db_column='ManfDesc', max_length=255)
    uom_basic = models.CharField(db_column='UOMBasic', max_length=50) # Assuming UOM ID
    c_id = models.IntegerField(db_column='CId', null=True, blank=True) # For WOItems/BoughtOut logic

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def get_uom_symbol(self):
        # Assuming Unit_Master mapping
        with connection.cursor() as cursor:
            cursor.execute("SELECT Symbol FROM Unit_Master WHERE Id = %s", [self.uom_basic])
            result = cursor.fetchone()
            return result[0] if result else self.uom_basic # Return ID if not found

class PurchaseOrderMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    po_no = models.CharField(db_column='PONo', max_length=50)
    supplier = models.ForeignKey(Supplier, on_delete=models.DO_NOTHING, db_column='SupplierId', to_field='supplier_id')
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', to_field='id')
    pr_spr_flag = models.CharField(db_column='PRSPRFlag', max_length=1) # '0' for PR, '1' for SPR
    authorize = models.BooleanField(db_column='Authorize')
    # Add other fields as necessary

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Master'

class PurchaseOrderDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(PurchaseOrderMaster, on_delete=models.DO_NOTHING, db_column='MId')
    item = models.ForeignKey(ItemMaster, on_delete=models.DO_NOTHING, db_column='ItemId')
    po_no = models.CharField(db_column='PONo', max_length=50)
    pr_id = models.IntegerField(db_column='PRId', null=True, blank=True) # Link to PR details
    spr_id = models.IntegerField(db_column='SPRId', null=True, blank=True) # Link to SPR details
    delivery_date = models.DateField(db_column='DelDate')
    # Add other fields

    class Meta:
        managed = False
        db_table = 'tblMM_PO_Details'

class InwardMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_no = models.CharField(db_column='GINNo', max_length=50)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', to_field='id')
    po_no = models.CharField(db_column='PONo', max_length=50) # Redundant, but exists in old schema

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Master'

class InwardDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    gin_master = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId')
    po_detail = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='POId')

    class Meta:
        managed = False
        db_table = 'tblInv_Inward_Details'

class MaterialReceivedMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    grr_no = models.CharField(db_column='GRRNo', max_length=50)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', to_field='id')
    gin_master = models.ForeignKey(InwardMaster, on_delete=models.DO_NOTHING, db_column='GINId')

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Master'

class MaterialReceivedDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialReceivedMaster, on_delete=models.DO_NOTHING, db_column='MId')
    po_detail = models.ForeignKey(PurchaseOrderDetail, on_delete=models.DO_NOTHING, db_column='POId')
    received_qty = models.FloatField(db_column='ReceivedQty')

    class Meta:
        managed = False
        db_table = 'tblinv_MaterialReceived_Details'

class MaterialQualityMaster(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    company = models.ForeignKey(Company, on_delete=models.DO_NOTHING, db_column='CompId', to_field='id')
    system_date = models.DateField(db_column='SysDate') # Date of quality check

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Master'

class MaterialQualityDetail(models.Model):
    id = models.IntegerField(db_column='Id', primary_key=True)
    master = models.ForeignKey(MaterialQualityMaster, on_delete=models.DO_NOTHING, db_column='MId')
    grr_detail = models.ForeignKey(MaterialReceivedDetail, on_delete=models.DO_NOTHING, db_column='GRRId')
    normal_accepted_qty = models.FloatField(db_column='NormalAccQty')
    deviated_qty = models.FloatField(db_column='DeviatedQty')
    segregated_qty = models.FloatField(db_column='SegregatedQty')
    rejected_qty = models.FloatField(db_column='RejectedQty')

    class Meta:
        managed = False
        db_table = 'tblQc_MaterialQuality_Details'

# --- Derived Report Model and Custom Manager ---

class VendorRatingItemManager(models.Manager):
    """
    A custom manager to encapsulate the complex logic of calculating vendor ratings,
    mirroring the C# Page_Init method. This manager will query the various
    source models and compute the final report data.
    """
    def get_vendor_ratings(self, comp_id, fin_year_id, supplier_id, from_date, to_date, item_filter_type):
        ratings_data = []
        
        # Parse dates (assuming YYYY-MM-DD or similar string format for input)
        from_date = datetime.strptime(from_date, '%Y-%m-%d').date()
        to_date = datetime.strptime(to_date, '%Y-%m-%d').date()

        # Determine item filter condition (x in C# code)
        item_condition = {}
        if item_filter_type == "WOItems": # Without Items
            item_condition['c_id__isnull'] = True
        elif item_filter_type == "BoughtOut": # BoughtOut Items
            item_condition['c_id__isnull'] = False
        # 'Select' means no additional filter on CId

        # Query PO Master/Details filtered by supplier, company, authorization and delivery date range
        po_details_qs = PurchaseOrderDetail.objects.filter(
            master__supplier__supplier_id=supplier_id,
            master__company__id=comp_id,
            master__authorize=True,
            delivery_date__range=(from_date, to_date)
        ).select_related('master', 'item', 'item__uom_basic') # Prefetch related data

        # SPR Details are handled in a similar loop as in C# (PRSPRFlag == '1')
        # This implementation simplifies by treating PR and SPR paths similarly after initial data retrieval.
        # A more robust system might use common sub-queries or a materialized view.

        # Aggregate SPR count if flag is '1' (from C#)
        # In Django, this implies a different approach. We'll compute it dynamically if needed.
        spr_sup_count = 1.0 # Default if no SPR calculation happens or if it's not applicable
        # The C# code has: StrSup = fun.select(" count(tblMM_PO_Master.SupplierId)", "tblMM_PO_Master,tblMM_PO_Details", "tblMM_PO_Master.PONo=tblMM_PO_Details.PONo AND tblMM_PO_Master.CompId='" + CompId + "' AND tblMM_PO_Master.Id=tblMM_PO_Details.MId And tblMM_PO_Master.SupplierId='" + SupplId + "'  Group by tblMM_PO_Master.SupplierId ");
        # This seems to count POs for a supplier. We'll assume the simpler overall_rating calc for now.
        # If PRSPRFlag logic significantly changes the final `orating`, that needs careful re-evaluation.
        # For this example, I'll use the '0.6 * quality + 0.4 * delivery' formula and avoid the division by SPRSup for overall.
        # If 'SPRSup' was meant to be the number of POs contributing to the rating, it should be an aggregation factor.

        for po_detail in po_details_qs:
            item_master = po_detail.item
            if item_filter_type == "WOItems" and item_master.c_id is not None:
                continue
            if item_filter_type == "BoughtOut" and item_master.c_id is None:
                continue

            # Get received quantity from MaterialReceivedDetail
            received_details = MaterialReceivedDetail.objects.filter(
                po_detail=po_detail,
                master__company__id=comp_id
            ).select_related('master__gin_master')

            for rec_detail in received_details:
                received_qty = rec_detail.received_qty

                # Get quality data from MaterialQualityDetail
                quality_details = MaterialQualityDetail.objects.filter(
                    grr_detail=rec_detail,
                    master__company__id=comp_id
                ).select_related('master')

                for q_detail in quality_details:
                    accepted_qty = q_detail.normal_accepted_qty
                    deviated_qty = q_detail.deviated_qty
                    segregated_qty = q_detail.segregated_qty
                    rejected_qty = q_detail.rejected_qty
                    
                    gqn_delivery_date = q_detail.master.system_date

                    # Calculate Quality Rating
                    if received_qty > 0:
                        quality_rating = round(
                            (((accepted_qty * 1) + (deviated_qty * 0.70) + (segregated_qty * 0.50)) * 100) / received_qty, 3
                        )
                    else:
                        quality_rating = 0.0

                    # Calculate Delivery Rate
                    pod_delivery_date = po_detail.delivery_date
                    time_difference = pod_delivery_date - gqn_delivery_date
                    # Note: C# `Math.Round((TimeDel.TotalDays * 1.1), 0)` is unusual.
                    # It adds 10% to total days then rounds. This is likely to create
                    # a small buffer. Replicating this directly.
                    calculated_date_diff = round(time_difference.total_days * 1.1, 0)
                    extended_delivery_date = gqn_delivery_date + timedelta(days=calculated_date_diff)

                    if extended_delivery_date >= pod_delivery_date:
                        delivery_rate = 100.0
                    else:
                        delivery_rate = 0.0

                    # Calculate Overall Rating
                    overall_rating = round((quality_rating * 0.6) + (delivery_rate * 0.4))

                    # Append to results
                    ratings_data.append(
                        VendorRatingItem(
                            item_code=item_master.item_code,
                            manufacturer_desc=item_master.manufacturer_desc,
                            uom_basic=item_master.get_uom_symbol(),
                            received_qty=received_qty,
                            accepted_qty=accepted_qty,
                            deviated_qty=deviated_qty,
                            segregated_qty=segregated_qty,
                            rejected_qty=rejected_qty,
                            supplier_id=supplier_id,
                            company_id=comp_id,
                            delivery_rate=delivery_rate,
                            overall_rating=overall_rating,
                            quality_rating=quality_rating
                        )
                    )
        return ratings_data

class VendorRatingItem(models.Model):
    """
    Represents a single calculated vendor rating record.
    This model does not directly map to a database table but is populated
    dynamically by the VendorRatingItemManager.
    """
    # Fields mirroring the C# DataTable structure
    item_code = models.CharField(max_length=100)
    manufacturer_desc = models.CharField(max_length=255)
    uom_basic = models.CharField(max_length=50) # Unit of Measure symbol
    received_qty = models.FloatField()
    accepted_qty = models.FloatField()
    deviated_qty = models.FloatField()
    segregated_qty = models.FloatField()
    rejected_qty = models.FloatField()
    supplier_id = models.CharField(max_length=50)
    company_id = models.IntegerField()
    delivery_rate = models.FloatField()
    overall_rating = models.FloatField()
    quality_rating = models.FloatField()

    # Custom manager for calculation logic
    objects = VendorRatingItemManager()

    class Meta:
        # This model is not managed by Django's ORM for database creation/migration
        # as its instances are generated in memory based on complex calculations.
        # We define it as a Model to leverage Django's field definitions and validation,
        # but it won't have a backing table in the traditional sense.
        # If this data were to be materialized into a view or temp table,
        # 'managed = False' and 'db_table = 'would be appropriate for that specific mapping.
        # For an in-memory report, these are omitted as it's not bound to a specific table.
        verbose_name = 'Vendor Rating Item'
        verbose_name_plural = 'Vendor Rating Items'

    def __str__(self):
        return f"Rating for {self.item_code} by {self.supplier_id} (Overall: {self.overall_rating}%)"

    # Add other business logic methods here if further calculations/transformations are needed
    # for individual items, keeping the fat model principle.
```

#### 4.2 Forms (vendormanagement/forms.py)

**Explanation:**
A simple form is created to capture the input parameters for the report (Supplier Code, From Date, To Date, and Item Category filter).

```python
from django import forms
from .models import Supplier

class VendorRatingFilterForm(forms.Form):
    supplier_id = forms.CharField(
        label='Supplier Code',
        required=True,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    from_date = forms.DateField(
        label='From Date',
        required=True,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    to_date = forms.DateField(
        label='To Date',
        required=True,
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    ITEM_FILTER_CHOICES = [
        ('Select', 'Select'),
        ('WOItems', 'Without Items (CId is NULL)'),
        ('BoughtOut', 'Bought Out Items (CId is not NULL)'),
    ]
    item_filter_type = forms.ChoiceField(
        label='Item Category',
        choices=ITEM_FILTER_CHOICES,
        required=True,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    def clean(self):
        cleaned_data = super().clean()
        from_date = cleaned_data.get('from_date')
        to_date = cleaned_data.get('to_date')

        if from_date and to_date and from_date > to_date:
            self.add_error('from_date', "From Date cannot be after To Date.")
        
        # Optional: Validate supplier_id exists if needed
        # supplier_id = cleaned_data.get('supplier_id')
        # if supplier_id and not Supplier.objects.filter(supplier_id=supplier_id).exists():
        #     self.add_error('supplier_id', "Supplier does not exist.")

        return cleaned_data

```

#### 4.3 Views (vendormanagement/views.py)

**Explanation:**
We use `TemplateView` for the main page to render the filter form and tab structure. Dedicated `ListView`s (or `TemplateView`s that render lists) are used for the HTMX-loaded table content of each tab, ensuring clean, concise views that delegate data retrieval to the custom manager.```python
from django.views.generic import TemplateView, ListView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from .models import VendorRatingItem, Company, Supplier
from .forms import VendorRatingFilterForm
from django.db.models import Avg # For aggregation in views


class VendorRatingReportView(TemplateView):
    """
    Main view for the Vendor Rating Report.
    Handles the initial display of the filter form and the tab structure.
    The report data tables are loaded dynamically via HTMX into the tabs.
    """
    template_name = 'vendormanagement/vendor_rating/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the form with any query parameters for sticky filters
        context['form'] = VendorRatingFilterForm(self.request.GET or None)
        context['company_address'] = Company.get_company_address(self.request.session.get('compid')) # Assuming compid is in session
        context['supplier_address'] = "" # Will be populated by HTMX if a supplier is selected
        return context

class BaseVendorRatingTablePartialView(ListView):
    """
    Base class for rendering vendor rating data tables.
    Handles fetching and validating parameters, and delegating data retrieval
    to the VendorRatingItemManager.
    """
    model = VendorRatingItem
    context_object_name = 'vendor_rating_items' # Default context name
    template_name = 'vendormanagement/vendor_rating/_report_table.html' # Default partial template

    def get_queryset(self):
        # Retrieve parameters from GET request (expected from HTMX calls)
        form = VendorRatingFilterForm(self.request.GET)
        if not form.is_valid():
            # If form is not valid, it means required parameters are missing or malformed.
            # For HTMX, we might return an empty table or an error message within the partial.
            # For simplicity, returning an empty queryset and logging error.
            messages.error(self.request, "Invalid report parameters provided.")
            print(f"Form errors: {form.errors}") # Log errors for debugging
            return [] # Return empty list if parameters are invalid

        comp_id = self.request.session.get('compid') # From session, as in ASP.NET
        fin_year_id = self.request.session.get('finyear') # From session, as in ASP.NET
        supplier_id = form.cleaned_data['supplier_id']
        from_date = form.cleaned_data['from_date'].strftime('%Y-%m-%d') # Format for manager
        to_date = form.cleaned_data['to_date'].strftime('%Y-%m-%d')     # Format for manager
        item_filter_type = form.cleaned_data['item_filter_type']

        if not comp_id or not fin_year_id:
            messages.error(self.request, "Company or Financial Year ID missing from session.")
            return []

        # Use the custom manager to get the calculated rating items
        all_items = VendorRatingItem.objects.get_vendor_ratings(
            comp_id=comp_id,
            fin_year_id=fin_year_id,
            supplier_id=supplier_id,
            from_date=from_date,
            to_date=to_date,
            item_filter_type=item_filter_type
        )
        return all_items

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Add supplier address to context if available (for header)
        supplier_id = self.request.GET.get('supplier_id')
        if supplier_id:
            try:
                supplier = Supplier.objects.get(supplier_id=supplier_id)
                context['supplier_address'] = supplier.get_full_address()
            except Supplier.DoesNotExist:
                context['supplier_address'] = "Supplier address not found."
        else:
            context['supplier_address'] = "" # No supplier selected

        return context


class OverallRatingTablePartialView(BaseVendorRatingTablePartialView):
    """
    Renders the Overall Rating table.
    Aggregates data by supplier to show overall rating.
    """
    template_name = 'vendormanagement/vendor_rating/_overall_rating_table.html'

    def get_queryset(self):
        items = super().get_queryset()
        if not items:
            return []
        
        # Aggregate the calculated items for the overall view
        # The C# code computed an 'orating' per item and then averaged.
        # This requires grouping. Since our VendorRatingItem is not a real DB model,
        # we'll do this aggregation in Python.
        
        # For simplicity, if `items` contains records for only one supplier_id
        # (which is enforced by the filter form), we can average `overall_rating` directly.
        # If there were multiple suppliers in the results, we'd need to group by supplier_id.
        
        if items:
            avg_overall_rating = sum(item.overall_rating for item in items) / len(items)
            # Create a simplified structure for the overall report
            # The original Crystal Report probably just showed one aggregate line.
            # We'll return a list with a single dict for clarity.
            return [{
                'supplier_id': items[0].supplier_id if items else 'N/A', # Assuming one supplier per request
                'company_id': items[0].company_id if items else 'N/A',
                'avg_overall_rating': round(avg_overall_rating, 2)
            }]
        return []

class QualityRatingTablePartialView(BaseVendorRatingTablePartialView):
    """
    Renders the Quality Rating table.
    Displays detailed quality ratings per item.
    """
    template_name = 'vendormanagement/vendor_rating/_quality_rating_table.html'
    # No further aggregation needed, BaseVendorRatingTablePartialView's get_queryset is sufficient.
    # The context_object_name is already 'vendor_rating_items' which suits.

class DeliveryRatingTablePartialView(BaseVendorRatingTablePartialView):
    """
    Renders the Delivery Rating table.
    Displays detailed delivery ratings per item.
    """
    template_name = 'vendormanagement/vendor_rating/_delivery_rating_table.html'
    # No further aggregation needed, BaseVendorRatingTablePartialView's get_queryset is sufficient.
    # The context_object_name is already 'vendor_rating_items' which suits.
```

#### 4.4 Templates

**Explanation:**
Templates are structured with `list.html` as the main page and partials (`_report_table.html`, `_overall_rating_table.html`, etc.) for content loaded via HTMX. This ensures dynamic updates without full page reloads. Alpine.js will manage UI state for tabs. DataTables is initialized on the dynamically loaded tables.

**File: `vendormanagement/vendor_rating/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-4">Supplier Rating Report</h2>
        
        <form hx-post="{% url 'vendor_rating_report' %}" hx-target="#report-tabs-container" hx-swap="outerHTML" hx-trigger="submit">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                {% for field in form %}
                <div>
                    <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ field.label }}</label>
                    {{ field }}
                    {% if field.errors %}
                    <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
            <div class="flex justify-end space-x-3">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Generate Report
                </button>
                <a href="{% url 'vendor_rating_cancel' %}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <div id="report-tabs-container" x-data="{ activeTab: 'overall' }" 
         hx-trigger="load, refreshReportData from:body" 
         hx-get="{% url 'vendor_rating_overall_table' %}{% if form.is_valid %}?{{ request.GET.urlencode }}{% endif %}" 
         hx-target="#tab-content" 
         hx-indicator="#tab-content-loader" 
         hx-swap="innerHTML">
        <!-- Report Header -->
        <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
            <h3 class="text-xl font-semibold text-gray-700">Report Details</h3>
            <div class="mt-2 text-sm text-gray-600">
                <p><strong>Company Address:</strong> {{ company_address }}</p>
                <p><strong>Supplier Address:</strong> <span id="supplier-address-display">{{ supplier_address }}</span></p>
                <p><strong>Period:</strong> From {{ form.from_date.value|date:"M d, Y" }} to {{ form.to_date.value|date:"M d, Y" }}</p>
                <p><strong>Item Category:</strong> {{ form.item_filter_type.value }}</p>
            </div>
        </div>

        <!-- Tabs -->
        <div class="bg-white p-6 rounded-lg shadow-lg">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                    <button 
                        @click="activeTab = 'overall'" 
                        :class="{'border-blue-500 text-blue-600': activeTab === 'overall', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'overall'}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                        hx-get="{% url 'vendor_rating_overall_table' %}{% if form.is_valid %}?{{ request.GET.urlencode }}{% endif %}" 
                        hx-target="#tab-content" 
                        hx-indicator="#tab-content-loader" 
                        hx-swap="innerHTML">
                        Overall Rating
                    </button>
                    <button 
                        @click="activeTab = 'quality'" 
                        :class="{'border-blue-500 text-blue-600': activeTab === 'quality', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'quality'}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                        hx-get="{% url 'vendor_rating_quality_table' %}{% if form.is_valid %}?{{ request.GET.urlencode }}{% endif %}" 
                        hx-target="#tab-content" 
                        hx-indicator="#tab-content-loader" 
                        hx-swap="innerHTML">
                        Quality Rating
                    </button>
                    <button 
                        @click="activeTab = 'delivery'" 
                        :class="{'border-blue-500 text-blue-600': activeTab === 'delivery', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'delivery'}" 
                        class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm"
                        hx-get="{% url 'vendor_rating_delivery_table' %}{% if form.is_valid %}?{{ request.GET.urlencode }}{% endif %}" 
                        hx-target="#tab-content" 
                        hx-indicator="#tab-content-loader" 
                        hx-swap="innerHTML">
                        Delivery Rating
                    </button>
                </nav>
            </div>
            
            <div id="tab-content" class="mt-4">
                <!-- Initial content will be loaded here via hx-get="load" on #report-tabs-container -->
                <div id="tab-content-loader" class="text-center htmx-indicator">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <p class="mt-2">Loading report data...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('reportTabs', () => ({
            activeTab: 'overall',
            // Add any other reactive data/functions here
        }));
    });

    // Event listener for HTMX after swap to re-initialize DataTables
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.target.id === 'tab-content' || event.target.id === 'report-tabs-container') {
            // Destroy existing DataTables instance if it exists
            if ($.fn.DataTable.isDataTable('#vendorRatingTable')) {
                $('#vendorRatingTable').DataTable().destroy();
            }
            // Initialize new DataTables instance
            $('#vendorRatingTable').DataTable({
                "pageLength": 10,
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
            // Update supplier address display if present in new content
            const newSupplierAddress = event.detail.xhr.getResponseHeader('HX-Trigger-After-Swap-supplier_address');
            if (newSupplierAddress) {
                document.getElementById('supplier-address-display').innerText = newSupplierAddress;
            }
        }
    });

    // Intercept form submission to add supplier address to header for report header update
    document.body.addEventListener('htmx:configRequest', function(evt) {
        if (evt.detail.elt.tagName === 'FORM' && evt.detail.elt.closest('#report-tabs-container')) {
            const supplierId = evt.detail.elt.querySelector('input[name="supplier_id"]').value;
            if (supplierId) {
                // Get supplier address for display in the main report header after swap
                fetch(`/vendormanagement/api/supplier-address/?supplier_id=${supplierId}`)
                    .then(response => response.json())
                    .then(data => {
                        evt.detail.triggeringEvent.detail.xhr.setRequestHeader('HX-Trigger-After-Swap-supplier_address', data.address);
                    });
            }
        }
    });
</script>
{% endblock %}
```

**File: `vendormanagement/vendor_rating/_report_table.html` (Generic partial for DataTables)**

This partial will be used by `QualityRatingTablePartialView` and `DeliveryRatingTablePartialView`.

```html
<table id="vendorRatingTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 shadow-sm rounded-lg">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Manufacturer Desc.</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Received Qty</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Accepted Qty</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Deviated Qty</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Segregated Qty</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rejected Qty</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quality Rating (%)</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Delivery Rate (%)</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Overall Rating (%)</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% if vendor_rating_items %}
            {% for item in vendor_rating_items %}
            <tr class="hover:bg-gray-100">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ forloop.counter }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.item_code }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.manufacturer_desc }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.uom_basic }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.received_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.accepted_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.deviated_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.segregated_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.rejected_qty|floatformat:2 }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.quality_rating|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.delivery_rate|floatformat:2 }}%</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ item.overall_rating|floatformat:2 }}%</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="12" class="py-4 px-4 text-center text-sm text-gray-500">No data available for the selected criteria.</td>
            </tr>
        {% endif %}
    </tbody>
</table>
```

**File: `vendormanagement/vendor_rating/_overall_rating_table.html` (Specific partial for Overall Rating)**

```html
<table id="vendorRatingTable" class="min-w-full bg-white border border-gray-200 divide-y divide-gray-200 shadow-sm rounded-lg">
    <thead>
        <tr class="bg-gray-50">
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Supplier ID</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company ID</th>
            <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Average Overall Rating (%)</th>
        </tr>
    </thead>
    <tbody class="divide-y divide-gray-200">
        {% if vendor_rating_items %}
            {# vendor_rating_items will contain a single dictionary for overall view #}
            {% for item in vendor_rating_items %}
            <tr class="hover:bg-gray-100">
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.supplier_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900">{{ item.company_id }}</td>
                <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-900 text-right font-semibold text-lg">{{ item.avg_overall_rating|floatformat:2 }}%</td>
            </tr>
            {% endfor %}
        {% else %}
            <tr>
                <td colspan="3" class="py-4 px-4 text-center text-sm text-gray-500">No overall rating data available for the selected criteria.</td>
            </tr>
        {% endif %}
    </tbody>
</table>
```

**File: `vendormanagement/vendor_rating/_quality_rating_table.html` (Specific partial for Quality Rating)**
This will use the generic `_report_table.html` for its structure.

```html
{% include 'vendormanagement/vendor_rating/_report_table.html' with vendor_rating_items=vendor_rating_items %}
```

**File: `vendormanagement/vendor_rating/_delivery_rating_table.html` (Specific partial for Delivery Rating)**
This will also use the generic `_report_table.html` for its structure.

```html
{% include 'vendormanagement/vendor_rating/_report_table.html' with vendor_rating_items=vendor_rating_items %}
```

#### 4.5 URLs (vendormanagement/urls.py)

**Explanation:**
Defines URL patterns for the main report page, and separate endpoints for each tab's content that HTMX will request. Also includes an API endpoint to get supplier address.

```python
from django.urls import path
from django.views.generic import RedirectView
from .views import (
    VendorRatingReportView,
    OverallRatingTablePartialView,
    QualityRatingTablePartialView,
    DeliveryRatingTablePartialView,
)
from django.http import JsonResponse
from .models import Supplier # Import Supplier model for API view

# Helper view for the cancel button
def vendor_rating_cancel_view(request):
    """Placeholder for redirecting to the main VendorRating page (VendorRating.aspx equivalent)."""
    # In a real application, this would redirect to the search/filter page or a dashboard.
    return RedirectView.as_view(url=reverse_lazy('dashboard_home'), permanent=False)(request) # Assuming a dashboard home URL

# API endpoint for supplier address lookup
def get_supplier_address_api(request):
    supplier_id = request.GET.get('supplier_id')
    if supplier_id:
        try:
            supplier = Supplier.objects.get(supplier_id=supplier_id)
            return JsonResponse({'address': supplier.get_full_address()})
        except Supplier.DoesNotExist:
            return JsonResponse({'address': 'Supplier not found'}, status=404)
    return JsonResponse({'address': 'No supplier ID provided'}, status=400)


urlpatterns = [
    path('vendor-rating/', VendorRatingReportView.as_view(), name='vendor_rating_report'),
    
    # HTMX partials for tab content
    path('vendor-rating/overall-table/', OverallRatingTablePartialView.as_view(), name='vendor_rating_overall_table'),
    path('vendor-rating/quality-table/', QualityRatingTablePartialView.as_view(), name='vendor_rating_quality_table'),
    path('vendor-rating/delivery-table/', DeliveryRatingTablePartialView.as_view(), name='vendor_rating_delivery_table'),

    # Cancel button redirect
    path('vendor-rating/cancel/', vendor_rating_cancel_view, name='vendor_rating_cancel'),

    # API endpoint for client-side data
    path('api/supplier-address/', get_supplier_address_api, name='api_supplier_address'),
]

```

#### 4.6 Tests (vendormanagement/tests.py)

**Explanation:**
Comprehensive tests are provided for the `VendorRatingItemManager` (to ensure calculations are correct) and the views (to ensure correct rendering and HTMX interaction). We mock the `Company` and `Supplier` data to ensure tests are isolated and efficient.

```python
from django.test import TestCase, Client
from django.urls import reverse
from unittest.mock import patch, MagicMock
from datetime import date, timedelta

# Import models to be tested
from .models import (
    VendorRatingItem,
    Company,
    Supplier,
    PurchaseOrderMaster,
    PurchaseOrderDetail,
    InwardMaster,
    InwardDetail,
    MaterialReceivedMaster,
    MaterialReceivedDetail,
    MaterialQualityMaster,
    MaterialQualityDetail,
    ItemMaster
)

class VendorRatingManagerTest(TestCase):
    """
    Tests for the VendorRatingItemManager's get_vendor_ratings method,
    which encapsulates the core calculation logic from the C# code.
    """
    @classmethod
    def setUpTestData(cls):
        # Create mock data for underlying tables for testing calculations
        # CompId = 1, FinYearId = 2023, SupplierId = 'SUP001'
        cls.comp_id = 1
        cls.fin_year_id = 2023
        cls.supplier_id = 'SUP001'
        cls.from_date = date(2023, 1, 1)
        cls.to_date = date(2023, 12, 31)

        # Mock Company and Supplier as they are used in calculations
        cls.mock_company = MagicMock(spec=Company)
        cls.mock_company.id = cls.comp_id
        cls.mock_company.get_company_address.return_value = "Mock Company Address"

        cls.mock_supplier = MagicMock(spec=Supplier)
        cls.mock_supplier.supplier_id = cls.supplier_id
        cls.mock_supplier.get_full_address.return_value = "Mock Supplier Address"

        # Create an item
        cls.item1 = ItemMaster.objects.create(
            id=101, item_code='ITEM001', manufacturer_desc='Test Widget', uom_basic='UOM_KG', c_id=None
        )
        cls.item2 = ItemMaster.objects.create(
            id=102, item_code='ITEM002', manufacturer_desc='BoughtOut Part', uom_basic='UOM_PC', c_id=2
        )
        # Mock UOM symbol lookup
        with patch('vendormanagement.models.connection.cursor') as mock_cursor:
            mock_cursor.return_value.__enter__.return_value.fetchone.return_value = ('KG',)
            cls.item1.get_uom_symbol() # Call once to set up the mock

            mock_cursor.return_value.__enter__.return_value.fetchone.return_value = ('PC',)
            cls.item2.get_uom_symbol() # Call once to set up the mock

        # Create PO Master
        cls.po_master = PurchaseOrderMaster.objects.create(
            id=1, po_no='PO001', supplier=cls.mock_supplier, company=cls.mock_company, pr_spr_flag='0', authorize=True
        )

        # Create PO Detail for item1
        cls.po_detail_item1 = PurchaseOrderDetail.objects.create(
            id=1001, master=cls.po_master, item=cls.item1, po_no='PO001', pr_id=1, spr_id=None, delivery_date=date(2023, 3, 10)
        )

        # Create Inward Records
        cls.inward_master = InwardMaster.objects.create(id=10, gin_no='GIN001', company=cls.mock_company, po_no='PO001')
        cls.inward_detail = InwardDetail.objects.create(id=100, gin_master=cls.inward_master, po_detail=cls.po_detail_item1)

        # Create Material Received Records
        cls.mr_master = MaterialReceivedMaster.objects.create(id=1000, grr_no='GRR001', company=cls.mock_company, gin_master=cls.inward_master)
        cls.mr_detail = MaterialReceivedDetail.objects.create(id=10000, master=cls.mr_master, po_detail=cls.po_detail_item1, received_qty=100.0)

        # Create Material Quality Records
        cls.mq_master = MaterialQualityMaster.objects.create(id=100000, company=cls.mock_company, system_date=date(2023, 3, 12))
        cls.mq_detail_good = MaterialQualityDetail.objects.create(
            id=1, master=cls.mq_master, grr_detail=cls.mr_detail,
            normal_accepted_qty=90.0, deviated_qty=5.0, segregated_qty=3.0, rejected_qty=2.0
        )
        # Create a second quality record for same received item, different delivery date scenario
        cls.mq_master_late = MaterialQualityMaster.objects.create(id=100001, company=cls.mock_company, system_date=date(2023, 3, 20))
        cls.mq_detail_late = MaterialQualityDetail.objects.create(
            id=2, master=cls.mq_master_late, grr_detail=cls.mr_detail,
            normal_accepted_qty=80.0, deviated_qty=10.0, segregated_qty=5.0, rejected_qty=5.0
        )


    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    @patch('vendormanagement.models.Supplier.get_full_address', return_value="Mock Supplier Address")
    @patch('vendormanagement.models.PurchaseOrderMaster.objects.filter')
    @patch('vendormanagement.models.MaterialReceivedDetail.objects.filter')
    @patch('vendormanagement.models.MaterialQualityDetail.objects.filter')
    @patch('vendormanagement.models.ItemMaster.get_uom_symbol', side_effect=['KG', 'PC'])
    def test_get_vendor_ratings_calculation(self, mock_get_uom_symbol, mock_mqd_filter, mock_mrd_filter, mock_pom_filter, mock_supplier_address, mock_company_address):
        # Configure mocks to return our setUpTestData objects
        mock_pom_filter.return_value.select_related.return_value = [self.po_detail_item1]
        mock_mrd_filter.return_value.select_related.return_value = [self.mr_detail]
        mock_mqd_filter.return_value.select_related.return_value = [self.mq_detail_good]

        # Call the manager method
        ratings = VendorRatingItem.objects.get_vendor_ratings(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            supplier_id=self.supplier_id,
            from_date=self.from_date.strftime('%Y-%m-%d'),
            to_date=self.to_date.strftime('%Y-%m-%d'),
            item_filter_type='Select'
        )

        self.assertEqual(len(ratings), 1)
        item = ratings[0]

        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.manufacturer_desc, 'Test Widget')
        self.assertEqual(item.uom_basic, 'KG') # From mock_get_uom_symbol
        self.assertEqual(item.received_qty, 100.0)
        self.assertEqual(item.accepted_qty, 90.0)
        self.assertEqual(item.deviated_qty, 5.0)
        self.assertEqual(item.segregated_qty, 3.0)
        self.assertEqual(item.rejected_qty, 2.0)

        # Test Quality Rating: (90*1 + 5*0.7 + 3*0.5) * 100 / 100 = (90 + 3.5 + 1.5) = 95
        self.assertAlmostEqual(item.quality_rating, 95.0)

        # Test Delivery Rate: PODelDate = 2023-03-10, GQNDelDate = 2023-03-12
        # TimeDel = (2023-03-10 - 2023-03-12) = -2 days
        # CalDateDiff = round(-2 * 1.1, 0) = -2
        # ExtDate = 2023-03-12 + (-2 days) = 2023-03-10
        # ExtDate (2023-03-10) >= PODelDate (2023-03-10) -> DelRate = 100
        self.assertAlmostEqual(item.delivery_rate, 100.0)

        # Test Overall Rating: (95 * 0.6) + (100 * 0.4) = 57 + 40 = 97
        self.assertAlmostEqual(item.overall_rating, 97.0)

    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    @patch('vendormanagement.models.Supplier.get_full_address', return_value="Mock Supplier Address")
    @patch('vendormanagement.models.PurchaseOrderMaster.objects.filter')
    @patch('vendormanagement.models.MaterialReceivedDetail.objects.filter')
    @patch('vendormanagement.models.MaterialQualityDetail.objects.filter')
    @patch('vendormanagement.models.ItemMaster.get_uom_symbol', return_value='KG')
    def test_get_vendor_ratings_delivery_late(self, mock_get_uom_symbol, mock_mqd_filter, mock_mrd_filter, mock_pom_filter, mock_supplier_address, mock_company_address):
        # Configure mocks to return our setUpTestData objects
        mock_pom_filter.return_value.select_related.return_value = [self.po_detail_item1]
        mock_mrd_filter.return_value.select_related.return_value = [self.mr_detail]
        mock_mqd_filter.return_value.select_related.return_value = [self.mq_detail_late] # Use the late delivery quality record

        ratings = VendorRatingItem.objects.get_vendor_ratings(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            supplier_id=self.supplier_id,
            from_date=self.from_date.strftime('%Y-%m-%d'),
            to_date=self.to_date.strftime('%Y-%m-%d'),
            item_filter_type='Select'
        )

        self.assertEqual(len(ratings), 1)
        item = ratings[0]

        # Test Quality Rating for mq_detail_late: (80*1 + 10*0.7 + 5*0.5) * 100 / 100 = (80 + 7 + 2.5) = 89.5
        self.assertAlmostEqual(item.quality_rating, 89.5)

        # Test Delivery Rate: PODelDate = 2023-03-10, GQNDelDate = 2023-03-20
        # TimeDel = (2023-03-10 - 2023-03-20) = -10 days
        # CalDateDiff = round(-10 * 1.1, 0) = -11
        # ExtDate = 2023-03-20 + (-11 days) = 2023-03-09
        # ExtDate (2023-03-09) < PODelDate (2023-03-10) -> DelRate = 0
        self.assertAlmostEqual(item.delivery_rate, 0.0)

        # Test Overall Rating: (89.5 * 0.6) + (0 * 0.4) = 53.7 + 0 = 53.7
        self.assertAlmostEqual(item.overall_rating, 53.7)

    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    @patch('vendormanagement.models.Supplier.get_full_address', return_value="Mock Supplier Address")
    @patch('vendormanagement.models.PurchaseOrderMaster.objects.filter')
    @patch('vendormanagement.models.MaterialReceivedDetail.objects.filter')
    @patch('vendormanagement.models.MaterialQualityDetail.objects.filter')
    @patch('vendormanagement.models.ItemMaster.get_uom_symbol', return_value='KG')
    def test_get_vendor_ratings_item_filter(self, mock_get_uom_symbol, mock_mqd_filter, mock_mrd_filter, mock_pom_filter, mock_supplier_address, mock_company_address):
        # Setup for WOItems (c_id is NULL)
        self.item1.c_id = None # Ensure item1 is WOItems
        self.item1.save()
        mock_pom_filter.return_value.select_related.return_value = [self.po_detail_item1]
        mock_mrd_filter.return_value.select_related.return_value = [self.mr_detail]
        mock_mqd_filter.return_value.select_related.return_value = [self.mq_detail_good]

        # Test with WOItems filter (should include item1)
        ratings_wo_items = VendorRatingItem.objects.get_vendor_ratings(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            supplier_id=self.supplier_id,
            from_date=self.from_date.strftime('%Y-%m-%d'),
            to_date=self.to_date.strftime('%Y-%m-%d'),
            item_filter_type='WOItems'
        )
        self.assertEqual(len(ratings_wo_items), 1)
        self.assertEqual(ratings_wo_items[0].item_code, 'ITEM001')

        # Change item1 to be bought out
        self.item1.c_id = 10 # Simulate bought out
        self.item1.save()

        # Test with WOItems filter (should exclude item1)
        ratings_wo_items_excluded = VendorRatingItem.objects.get_vendor_ratings(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            supplier_id=self.supplier_id,
            from_date=self.from_date.strftime('%Y-%m-%d'),
            to_date=self.to_date.strftime('%Y-%m-%d'),
            item_filter_type='WOItems'
        )
        self.assertEqual(len(ratings_wo_items_excluded), 0)

        # Test with BoughtOut filter (should include item1 now)
        ratings_bought_out = VendorRatingItem.objects.get_vendor_ratings(
            comp_id=self.comp_id,
            fin_year_id=self.fin_year_id,
            supplier_id=self.supplier_id,
            from_date=self.from_date.strftime('%Y-%m-%d'),
            to_date=self.to_date.strftime('%Y-%m-%d'),
            item_filter_type='BoughtOut'
        )
        self.assertEqual(len(ratings_bought_out), 1)
        self.assertEqual(ratings_bought_out[0].item_code, 'ITEM001')


class VendorRatingViewsTest(TestCase):
    """
    Tests for the Django views and HTMX interactions.
    We'll mock the VendorRatingItem.objects.get_vendor_ratings manager
    to control the data returned without complex DB setup.
    """
    def setUp(self):
        self.client = Client()
        # Mock session data
        self.client.session['compid'] = 1
        self.client.session['finyear'] = 2023

        # Create mock Supplier and Company instances for address lookups
        self.mock_supplier_obj = Supplier.objects.create(
            supplier_id='SUP001', supplier_name='Test Supplier', regd_address='123 Main St',
            regd_country='1', regd_state='1', regd_city='1', regd_pin_no='12345'
        )
        # Mock the _get_lookup_name method as it hits the DB cursor directly
        with patch('vendormanagement.models.connection.cursor') as mock_cursor:
            mock_cursor.return_value.__enter__.return_value.fetchone.side_effect = [
                ('USA',), ('CA',), ('Los Angeles',), # For first get_full_address call
                ('USA',), ('CA',), ('Los Angeles',), # For second call etc.
            ]
            self.mock_supplier_obj.get_full_address() # Trigger once to setup side_effect

        self.mock_company_obj = Company.objects.create(
            id=1, name='Test Company', address='456 Corp Ave'
        )


    @patch('vendormanagement.models.VendorRatingItem.objects.get_vendor_ratings', return_value=[])
    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    def test_report_view_get(self, mock_get_company_address, mock_get_vendor_ratings):
        response = self.client.get(reverse('vendor_rating_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'vendormanagement/vendor_rating/list.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], VendorRatingFilterForm)

    @patch('vendormanagement.models.VendorRatingItem.objects.get_vendor_ratings')
    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    @patch('vendormanagement.models.Supplier.get_full_address', return_value="Mock Supplier Address")
    def test_report_view_post_htmx(self, mock_get_supplier_address, mock_get_company_address, mock_get_vendor_ratings):
        # Mock the manager to return some dummy data
        mock_get_vendor_ratings.return_value = [
            VendorRatingItem(item_code='A', manufacturer_desc='Desc A', uom_basic='EA', received_qty=100, accepted_qty=90, deviated_qty=5, segregated_qty=5, rejected_qty=0, supplier_id='SUP001', company_id=1, quality_rating=92.5, delivery_rate=100, overall_rating=96),
        ]

        data = {
            'supplier_id': 'SUP001',
            'from_date': '2023-01-01',
            'to_date': '2023-12-31',
            'item_filter_type': 'Select'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('vendor_rating_report'), data, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'vendormanagement/vendor_rating/list.html') # The form post re-renders the whole list page
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].is_valid())
        mock_get_vendor_ratings.assert_called_once() # Should have been called during the initial load of the tab content

    @patch('vendormanagement.models.VendorRatingItem.objects.get_vendor_ratings')
    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    @patch('vendormanagement.models.Supplier.get_full_address', return_value="Mock Supplier Address")
    def test_overall_rating_table_htmx(self, mock_get_supplier_address, mock_get_company_address, mock_get_vendor_ratings):
        # Mock the manager to return data for overall calculation
        mock_get_vendor_ratings.return_value = [
            VendorRatingItem(item_code='A', quality_rating=90.0, delivery_rate=100.0, overall_rating=96.0, supplier_id='SUP001', company_id=1),
            VendorRatingItem(item_code='B', quality_rating=80.0, delivery_rate=100.0, overall_rating=92.0, supplier_id='SUP001', company_id=1),
        ]

        data = {
            'supplier_id': 'SUP001',
            'from_date': '2023-01-01',
            'to_date': '2023-12-31',
            'item_filter_type': 'Select'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendor_rating_overall_table'), data, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'vendormanagement/vendor_rating/_overall_rating_table.html')
        self.assertIn('vendor_rating_items', response.context)
        self.assertEqual(len(response.context['vendor_rating_items']), 1)
        self.assertAlmostEqual(response.context['vendor_rating_items'][0]['avg_overall_rating'], 94.0) # (96+92)/2
        mock_get_vendor_ratings.assert_called_once()

    @patch('vendormanagement.models.VendorRatingItem.objects.get_vendor_ratings')
    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    @patch('vendormanagement.models.Supplier.get_full_address', return_value="Mock Supplier Address")
    def test_quality_rating_table_htmx(self, mock_get_supplier_address, mock_get_company_address, mock_get_vendor_ratings):
        mock_get_vendor_ratings.return_value = [
            VendorRatingItem(item_code='A', quality_rating=90.0, delivery_rate=100.0, overall_rating=96.0, supplier_id='SUP001', company_id=1),
        ]
        data = {
            'supplier_id': 'SUP001',
            'from_date': '2023-01-01',
            'to_date': '2023-12-31',
            'item_filter_type': 'Select'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendor_rating_quality_table'), data, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'vendormanagement/vendor_rating/_quality_rating_table.html')
        self.assertIn('vendor_rating_items', response.context)
        self.assertEqual(len(response.context['vendor_rating_items']), 1)
        self.assertEqual(response.context['vendor_rating_items'][0].quality_rating, 90.0)
        mock_get_vendor_ratings.assert_called_once()

    @patch('vendormanagement.models.VendorRatingItem.objects.get_vendor_ratings')
    @patch('vendormanagement.models.Company.get_company_address', return_value="Mock Company Address")
    @patch('vendormanagement.models.Supplier.get_full_address', return_value="Mock Supplier Address")
    def test_delivery_rating_table_htmx(self, mock_get_supplier_address, mock_get_company_address, mock_get_vendor_ratings):
        mock_get_vendor_ratings.return_value = [
            VendorRatingItem(item_code='A', quality_rating=90.0, delivery_rate=100.0, overall_rating=96.0, supplier_id='SUP001', company_id=1),
        ]
        data = {
            'supplier_id': 'SUP001',
            'from_date': '2023-01-01',
            'to_date': '2023-12-31',
            'item_filter_type': 'Select'
        }
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('vendor_rating_delivery_table'), data, **headers)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'vendormanagement/vendor_rating/_delivery_rating_table.html')
        self.assertIn('vendor_rating_items', response.context)
        self.assertEqual(len(response.context['vendor_rating_items']), 1)
        self.assertEqual(response.context['vendor_rating_items'][0].delivery_rate, 100.0)
        mock_get_vendor_ratings.assert_called_once()

    @patch('vendormanagement.models.Supplier.objects.get')
    def test_get_supplier_address_api(self, mock_supplier_get):
        mock_supplier = MagicMock()
        mock_supplier.get_full_address.return_value = "Mocked Supplier Address"
        mock_supplier_get.return_value = mock_supplier

        response = self.client.get(reverse('api_supplier_address') + '?supplier_id=SUP001')
        self.assertEqual(response.status_code, 200)
        self.assertJSONEqual(response.content, {'address': 'Mocked Supplier Address'})
        mock_supplier_get.assert_called_once_with(supplier_id='SUP001')

    def test_get_supplier_address_api_no_id(self):
        response = self.client.get(reverse('api_supplier_address'))
        self.assertEqual(response.status_code, 400)
        self.assertJSONEqual(response.content, {'address': 'No supplier ID provided'})

    def test_vendor_rating_cancel_redirect(self):
        # Assuming 'dashboard_home' is a defined URL in your project's root urls.py
        # For testing, we might need a mock URL or define a simple one
        with self.settings(ROOT_URLCONF='your_project_name.urls'): # Replace with your project's root urls.py
            # Temporarily define a dummy dashboard_home URL for testing
            from django.urls import path as django_path
            from django.views.generic import TemplateView as DjangoTemplateView
            urlpatterns_mock = [
                django_path('dashboard/', DjangoTemplateView.as_view(template_name='dummy.html'), name='dashboard_home'),
                django_path('vendormanagement/', include('vendormanagement.urls')) # Include your app's urls
            ]
            with patch('your_project_name.urls.urlpatterns', urlpatterns_mock): # Patch your project's urls
                response = self.client.get(reverse('vendor_rating_cancel'))
                self.assertEqual(response.status_code, 302) # Should redirect
                self.assertRedirects(response, reverse('dashboard_home'))

```

### Step 5: HTMX and Alpine.js Integration

**Instructions for AI-Assisted Implementation:**

1.  **Filter Form Submission (HTMX):**
    *   The `VendorRatingFilterForm` in `list.html` is configured to submit via `hx-post` to the same URL (`{% url 'vendor_rating_report' %}`).
    *   `hx-target="#report-tabs-container"` and `hx-swap="outerHTML"`: This means the entire section containing the tabs and report header will be replaced after form submission. This updates the report header with new filter criteria and triggers a fresh load of the first tab.
    *   `hx-trigger="submit"`: The form submission triggers the HTMX request.
    *   `hx-trigger="load, refreshReportData from:body"` on `#report-tabs-container`: Ensures the initial report content is loaded when the page loads, and also refreshes if a custom event `refreshReportData` is triggered (e.g., after an update elsewhere, though not directly applicable here).

2.  **Tab Switching (HTMX + Alpine.js):**
    *   **Alpine.js (`x-data="{ activeTab: 'overall' }"`):** Manages the visual active state of the tabs. `x-bind:class` (shorthand `:class`) is used to apply Tailwind CSS classes (`border-blue-500`, `text-blue-600`) to the active tab button.
    *   **HTMX (`hx-get`, `hx-target`, `hx-indicator`, `hx-swap`):**
        *   Each tab button has an `hx-get` attribute pointing to its respective partial view URL (e.g., `{% url 'vendor_rating_overall_table' %}`). Crucially, it passes the *current form parameters* in the URL to ensure the data is filtered correctly: `{% if form.is_valid %}?{{ request.GET.urlencode }}{% endif %}`.
        *   `hx-target="#tab-content"`: The content of the selected tab is loaded into the `tab-content` div.
        *   `hx-indicator="#tab-content-loader"`: A loading spinner is shown while content is being fetched.
        *   `hx-swap="innerHTML"`: Replaces the entire content of the `tab-content` div.
        *   `@click="activeTab = 'overall'"`: Alpine.js updates the `activeTab` variable on click, changing the styling.

3.  **DataTables Initialization:**
    *   The DataTables library (`vendorRatingTable`) is initialized via JavaScript.
    *   A critical `htmx:afterSwap` event listener ensures that DataTables is *re-initialized* every time new content is swapped into the `#tab-content` or `#report-tabs-container` divs. This handles cases where old DataTables instances might be present and need to be destroyed before a new one is created, preventing conflicts.

4.  **Supplier Address Dynamic Update:**
    *   The `htmx:configRequest` event listener on the form helps capture the `supplier_id` before the HTMX request.
    *   It makes a *separate* `fetch` call to a Django API endpoint (`/api/supplier-address/`) to get the full supplier address string.
    *   This address is then injected into an `HX-Trigger-After-Swap-supplier_address` response header.
    *   The `htmx:afterSwap` listener detects this custom header and updates the `#supplier-address-display` span with the retrieved address, ensuring the header is accurate without needing to re-render the entire header from the server.

5.  **Error Handling (Implicit):**
    *   Invalid form submissions will cause `form.is_valid()` to be false in the `BaseVendorRatingTablePartialView`, leading to an empty queryset and a `messages.error` flash message (which `base.html` would display). This avoids showing broken tables.

### Final Notes

This comprehensive plan addresses the migration from ASP.NET to Django, focusing on automation, best practices, and a modern frontend stack. The key is to:

-   **Deconstruct Complex Logic:** Break down the monolithic C# `Page_Init` into modular, testable components within Django models (custom managers for calculations) and thin views.
-   **Separate Concerns:** Strictly adhere to the fat model, thin view principle, ensuring business logic resides in models and presentation logic in templates.
-   **Embrace HTMX:** Use HTMX for all dynamic interactions, minimizing custom JavaScript and enabling a highly interactive user experience with server-rendered HTML fragments.
-   **Leverage DataTables:** Standardize on DataTables for all list and report views to provide rich, client-side data manipulation capabilities.
-   **Test Thoroughly:** Implement robust unit and integration tests to ensure the correctness of calculations and the reliability of view interactions.
-   **Automate Where Possible:** This detailed blueprint serves as a guide for AI-assisted tools to generate much of the boilerplate code, focusing human effort on verifying business logic translation and integration.

By following these steps, your organization can achieve a significant modernization outcome, moving from a legacy ASP.NET application to a performant, maintainable, and scalable Django solution.