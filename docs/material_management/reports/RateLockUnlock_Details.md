The following modernization plan outlines the strategic transition of your ASP.NET application, specifically the Rate Lock/Unlock Details report viewer, to a robust and modern Django 5.0+ solution. This plan emphasizes automation-driven migration, clear communication, and the adoption of a cutting-edge technology stack focused on maintainability and performance.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

The ASP.NET code interacts with the database primarily through a stored procedure `GetRateLockUnlockPrint` and references `tblDG_Item_Master` and `tblMM_RateLockUnLock_Master`. Based on the query parameters and filtering logic, we infer the following structures:

**Primary Report Data Table:** `tblMM_RateLockUnLock_Master`
-   **Purpose:** Stores the core rate lock/unlock transaction entries.
-   **Inferred Columns:**
    -   `RLU_ID` (Integer, Primary Key)
    -   `Item_Master_ID` (Integer, Foreign Key to `tblDG_Item_Master`)
    -   `SessionId` (Varchar/String, for `LockedBy`)
    -   `SysDate` (DateTime, for `FDate`, `TDate`)
    -   `LockStatus` (Boolean/Bit, assumed for report context)
    -   `UnlockStatus` (Boolean/Bit, assumed for report context)
    -   `RateValue` (Decimal, assumed for report context)
    -   `Quantity` (Decimal, assumed for report context)
    -   `CompId` (Integer, Company ID)
    -   `FinYearId` (Integer, Financial Year ID)

**Related Lookup Table:** `tblDG_Item_Master`
-   **Purpose:** Provides details for items referenced in rate lock/unlock entries.
-   **Inferred Columns:**
    -   `Item_Id` (Integer, Primary Key)
    -   `CId` (Varchar/String, for `category`)
    -   `ItemCode` (Varchar/String, for `SearchItemCode` when `SearchCode` is '1')
    -   `ManfDesc` (Varchar/String, for `SearchItemCode` when `SearchCode` is '2')
    -   `CompId` (Integer, Company ID)
    -   `FinYearId` (Integer, Financial Year ID)

### Step 2: Identify Backend Functionality

Task: Determine the core operations of the ASP.NET page.

The ASP.NET page `RateLockUnlock_Details.aspx` serves primarily as a **Read (Reporting)** interface. It displays a Crystal Report based on a complex set of filters provided via query string parameters. There are no explicit user interface elements for Create, Update, or Delete operations on the page itself. The C# code fetches data, applies filtering logic, and binds it to the report viewer.

While this specific page is a report, for a complete modernization, we will provide the standard CRUD operations for the core `RateLockUnlockEntry` model, which would typically reside in a separate management section of the application. The report view will then focus on filtering and displaying this data.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

-   **`CR:CrystalReportViewer`**: This is the main component for displaying the report. In Django, this will be replaced by a dynamic HTML table rendered with DataTables, providing interactive sorting, searching, and pagination.
-   **Query String Parameters (`category`, `Type`, `SearchCode`, `SearchItemCode`, `loc`, `FDate`, `TDate`, `LockedBy`, `Key`)**: These parameters imply a filtering mechanism. In Django, a user-friendly filter form will be implemented, submitting parameters via HTMX to update the DataTables.
-   **`loadingNotifier.js`**: Suggests a loading indicator. This will be handled gracefully by HTMX's built-in `hx-indicator` functionality.

### Step 4: Generate Django Code

We will create a new Django application, e.g., `material_management`, to house these components.

#### 4.1 Models

Task: Create Django models based on the identified database schema. We'll define two models: `ItemMaster` and `RateLockUnlockEntry`, the latter being the primary model for the report and potential CRUD.

**`material_management/models.py`**

```python
from django.db import models
from datetime import datetime

# Manager for ItemMaster, could include specific lookup methods if needed
class ItemMasterManager(models.Manager):
    pass

class ItemMaster(models.Model):
    # Mapping to tblDG_Item_Master
    item_id = models.IntegerField(db_column='Item_Id', primary_key=True)
    category_id = models.CharField(db_column='CId', max_length=50, blank=True, null=True)
    item_code = models.CharField(db_column='ItemCode', max_length=100, blank=True, null=True)
    manf_description = models.CharField(db_column='ManfDesc', max_length=255, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = ItemMasterManager()

    class Meta:
        managed = False
        db_table = 'tblDG_Item_Master'
        verbose_name = 'Item Master'
        verbose_name_plural = 'Item Masters'

    def __str__(self):
        return f"{self.item_code} - {self.manf_description}"

# Manager for RateLockUnlockEntry, containing the report generation logic
class RateLockUnlockEntryManager(models.Manager):
    def get_report_data(self, filters):
        # This method encapsulates the filtering logic from the original ASP.NET code-behind
        # and the 'GetRateLockUnlockPrint' stored procedure.

        company_id = filters.get('compid')
        fin_year_id = filters.get('finyear')
        category_id = filters.get('category')
        cw_type = filters.get('Type')
        search_code = filters.get('SearchCode')
        search_item_code = filters.get('SearchItemCode')
        lock_by_session_id = filters.get('LockedBy')
        from_date_str = filters.get('FDate')
        to_date_str = filters.get('TDate')

        queryset = self.get_queryset().select_related('item') # Join with ItemMaster

        if company_id:
            queryset = queryset.filter(company_id=company_id)
        if fin_year_id:
            queryset = queryset.filter(financial_year_id__lte=fin_year_id) # FinYearId <= fin_year_id
        
        if lock_by_session_id and lock_by_session_id != "0" and lock_by_session_id != "Select":
            queryset = queryset.filter(session_id=lock_by_session_id)
        
        # Date range filtering (SysDate between FDate and TDate)
        try:
            if from_date_str and from_date_str != "0":
                from_date_obj = datetime.strptime(from_date_str, '%m/%d/%Y').date()
                queryset = queryset.filter(sys_date__date__gte=from_date_obj)
            if to_date_str and to_date_str != "0":
                to_date_obj = datetime.strptime(to_date_str, '%m/%d/%Y').date()
                queryset = queryset.filter(sys_date__date__lte=to_date_obj)
        except ValueError:
            # Handle cases where date format is invalid, e.g., by returning an empty queryset or logging
            return self.none()

        # Dynamic ItemMaster related filters based on CWType and SearchCode
        if cw_type and cw_type != "Select":
            if cw_type == "Category":
                if category_id and category_id != "Select":
                    queryset = queryset.filter(item__category_id=category_id)
                    
                    if search_code and search_code != "0" and search_item_code:
                        if search_code == "1": # ItemCode Like '...%'
                            queryset = queryset.filter(item__item_code__startswith=search_item_code)
                        elif search_code == "2": # ManfDesc Like '%...%'
                            queryset = queryset.filter(item__manf_description__icontains=search_item_code)
            elif cw_type == "WOItems": # "Without Items" implies no category filter but item code search might apply
                if search_code and search_code != "0" and search_item_code:
                    if search_code == "1": # ItemCode Like '%...%'
                        queryset = queryset.filter(item__item_code__icontains=search_item_code)
                    elif search_code == "2": # ManfDesc Like '%...%'
                        queryset = queryset.filter(item__manf_description__icontains=search_item_code)
        
        # Note: The original code's "loc" parameter is present but not used in the SQL generation,
        # so it's omitted here unless its purpose is clarified.
        
        # Example of adding calculated fields if the report output required them
        # from django.db.models import F
        # queryset = queryset.annotate(total_value=F('rate_value') * F('quantity'))

        return queryset


class RateLockUnlockEntry(models.Model):
    # Mapping to tblMM_RateLockUnLock_Master
    rlu_id = models.IntegerField(db_column='RLU_ID', primary_key=True)
    item = models.ForeignKey(ItemMaster, on_delete=models.PROTECT, db_column='Item_Master_ID', related_name='rate_lock_unlock_entries', blank=True, null=True)
    session_id = models.CharField(db_column='SessionId', max_length=100, blank=True, null=True)
    sys_date = models.DateTimeField(db_column='SysDate', blank=True, null=True)
    lock_status = models.BooleanField(db_column='LockStatus', default=False)
    unlock_status = models.BooleanField(db_column='UnlockStatus', default=False)
    rate_value = models.DecimalField(db_column='RateValue', max_digits=18, decimal_places=4, blank=True, null=True)
    quantity = models.DecimalField(db_column='Quantity', max_digits=18, decimal_places=4, blank=True, null=True)
    company_id = models.IntegerField(db_column='CompId', blank=True, null=True)
    financial_year_id = models.IntegerField(db_column='FinYearId', blank=True, null=True)

    objects = RateLockUnlockEntryManager()

    class Meta:
        managed = False
        db_table = 'tblMM_RateLockUnLock_Master'
        verbose_name = 'Rate Lock Unlock Entry'
        verbose_name_plural = 'Rate Lock Unlock Entries'

    def __str__(self):
        return f"RLU {self.rlu_id} - Item: {self.item.item_code if self.item else 'N/A'}"
    
    # Business logic methods, adhering to the "fat model" principle
    def update_status(self, new_lock_status, new_unlock_status):
        """
        Updates the lock and unlock status of the entry.
        """
        self.lock_status = new_lock_status
        self.unlock_status = new_unlock_status
        self.save()
        return True # Indicate success

    def get_display_item_category(self):
        return self.item.category_id if self.item else 'N/A'
    
    def get_display_item_code(self):
        return self.item.item_code if self.item else 'N/A'
    
    def get_display_item_description(self):
        return self.item.manf_description if self.item else 'N/A'
```

#### 4.2 Forms

Task: Define Django forms for user input (filter form for the report, and a standard CRUD form for managing entries).

**`material_management/forms.py`**

```python
from django import forms
from .models import RateLockUnlockEntry, ItemMaster
from django.db.models import Q # For dynamic choices

class RateLockUnlockEntryForm(forms.ModelForm):
    # This form is for standard CRUD operations on RateLockUnlockEntry, not directly for the report filters.
    # It assumes the item_master_id is selectable and other fields are directly editable.
    item = forms.ModelChoiceField(
        queryset=ItemMaster.objects.all(),
        empty_label="Select Item",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )

    class Meta:
        model = RateLockUnlockEntry
        fields = [
            'item', 'session_id', 'sys_date', 'lock_status', 
            'unlock_status', 'rate_value', 'quantity', 'company_id', 'financial_year_id'
        ]
        widgets = {
            'session_id': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'sys_date': forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'lock_status': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'unlock_status': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
            'rate_value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'quantity': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.01'}),
            'company_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'financial_year_id': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
    
    def clean(self):
        cleaned_data = super().clean()
        # Add any custom validation logic here if needed
        return cleaned_data


class RateLockUnlockReportFilterForm(forms.Form):
    # Choices for dropdowns, dynamically populated if needed from DB
    CATEGORY_CHOICES = [('', 'Select')] + [(item.category_id, item.category_id) for item in ItemMaster.objects.distinct('category_id').exclude(category_id__isnull=True).exclude(category_id=' ')]
    TYPE_CHOICES = [
        ('', 'Select'),
        ('Category', 'Category Wise'),
        ('WOItems', 'Without Items') # Inferred as 'WOItems' from code
    ]
    SEARCH_CODE_CHOICES = [
        ('0', 'Select'),
        ('1', 'By Item Code'),
        ('2', 'By Manufacturer Description')
    ]
    LOCKED_BY_CHOICES = [
        ('0', 'Select'), # Assuming '0' means 'All'
        # Add dynamic choices for SessionId if needed from RateLockUnlockEntry.objects.distinct('session_id')
    ]

    category = forms.ChoiceField(
        choices=CATEGORY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    Type = forms.ChoiceField(
        choices=TYPE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    SearchCode = forms.ChoiceField(
        choices=SEARCH_CODE_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    SearchItemCode = forms.CharField(
        max_length=255, 
        required=False,
        widget=forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'Enter item code or description'})
    )
    # The 'loc' parameter is present in ASP.NET but not used in C# filtering logic, so omitting for now
    # loc = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control'}))
    FDate = forms.CharField(
        max_length=10, # MM/DD/YYYY
        required=False,
        widget=forms.TextInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    TDate = forms.CharField(
        max_length=10, # MM/DD/YYYY
        required=False,
        widget=forms.TextInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    LockedBy = forms.ChoiceField(
        choices=LOCKED_BY_CHOICES,
        required=False,
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'})
    )
    
    # Hidden fields for company_id and financial_year_id, typically from session/user context
    compid = forms.IntegerField(widget=forms.HiddenInput(), required=False)
    finyear = forms.IntegerField(widget=forms.HiddenInput(), required=False)

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Dynamically populate 'LockedBy' choices from existing session_ids
        self.fields['LockedBy'].choices = [('0', 'Select')] + \
                                          [(s, s) for s in RateLockUnlockEntry.objects.distinct('session_id').exclude(session_id__isnull=True).exclude(session_id=' ')]
        # Dates should be converted to MM/DD/YYYY string if coming from date objects for consistency with old system
        if 'FDate' in self.initial and isinstance(self.initial['FDate'], datetime.date):
            self.initial['FDate'] = self.initial['FDate'].strftime('%m/%d/%Y')
        if 'TDate' in self.initial and isinstance(self.initial['TDate'], datetime.date):
            self.initial['TDate'] = self.initial['TDate'].strftime('%m/%d/%Y')
```

#### 4.3 Views

Task: Implement the report display and standard CRUD operations using Django Class-Based Views. The report view will utilize the model manager's filtering logic.

**`material_management/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.db.models import F # For potentially complex annotations/updates within model methods

from .models import RateLockUnlockEntry, ItemMaster
from .forms import RateLockUnlockEntryForm, RateLockUnlockReportFilterForm

# Helper to pass session-like data (compid, finyear) to filters
def get_session_context(request):
    # In a real application, these would come from the user's session or profile
    # For demonstration, using dummy values.
    # The original ASP.NET code pulls these from Session["compid"] and Session["finyear"].
    return {
        'compid': request.session.get('compid', 1), # Example company ID
        'finyear': request.session.get('finyear', 2023) # Example financial year
    }

# --- Report Views ---
class RateLockUnlockReportView(TemplateView):
    template_name = 'material_management/ratelockunlockentry/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the filter form.
        # Pass session context (compid, finyear) to the form's initial data.
        session_context = get_session_context(self.request)
        context['filter_form'] = RateLockUnlockReportFilterForm(initial=session_context)
        return context

class RateLockUnlockTablePartialView(ListView):
    model = RateLockUnlockEntry
    template_name = 'material_management/ratelockunlockentry/_report_table.html'
    context_object_name = 'ratelockunlock_entries'

    def get_queryset(self):
        # Extract filters from GET request.
        # Ensure company_id and financial_year_id are always passed for filtering.
        session_context = get_session_context(self.request)
        filters = {k: v for k, v in self.request.GET.items() if v} # Get all non-empty GET params
        filters.update(session_context) # Add mandatory session filters

        # Use the manager's method to get the filtered report data
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        return queryset

# --- Standard CRUD Views for RateLockUnlockEntry (as per template requirements) ---

class RateLockUnlockEntryListView(ListView):
    model = RateLockUnlockEntry
    template_name = 'material_management/ratelockunlockentry/list.html'
    context_object_name = 'ratelockunlockentries' # Plural name

class RateLockUnlockEntryCreateView(CreateView):
    model = RateLockUnlockEntry
    form_class = RateLockUnlockEntryForm
    template_name = 'material_management/ratelockunlockentry/form.html'
    success_url = reverse_lazy('ratelockunlockentry_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Rate Lock Unlock Entry added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshRateLockUnlockEntryList' # Trigger custom event
                }
            )
        return response

class RateLockUnlockEntryUpdateView(UpdateView):
    model = RateLockUnlockEntry
    form_class = RateLockUnlockEntryForm
    template_name = 'material_management/ratelockunlockentry/form.html'
    success_url = reverse_lazy('ratelockunlockentry_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Rate Lock Unlock Entry updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshRateLockUnlockEntryList'
                }
            )
        return response

class RateLockUnlockEntryDeleteView(DeleteView):
    model = RateLockUnlockEntry
    template_name = 'material_management/ratelockunlockentry/confirm_delete.html'
    success_url = reverse_lazy('ratelockunlockentry_list') # Redirect to list after delete

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Rate Lock Unlock Entry deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshRateLockUnlockEntryList'
                }
            )
        return response
```

#### 4.4 Templates

Task: Create templates for each view, leveraging HTMX, Alpine.js, and DataTables.

**`material_management/templates/material_management/ratelockunlockentry/report.html` (Main Report Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h2 class="text-3xl font-bold text-gray-800 mb-6">Rate Lock Unlock Report</h2>

    <div class="bg-white shadow-md rounded-lg p-6 mb-8">
        <h3 class="text-xl font-semibold text-gray-700 mb-4">Filter Report Data</h3>
        <form hx-get="{% url 'ratelockunlockentry_report_table' %}" hx-target="#report-table-container" hx-swap="innerHTML" hx-indicator="#report-loading-indicator" hx-sync="submit:this">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for field in filter_form %}
                {% if field.is_hidden %}
                    {{ field }}
                {% else %}
                    <div>
                        <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ field.label }}</label>
                        {{ field }}
                        {% if field.help_text %}
                            <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                        {% endif %}
                        {% if field.errors %}
                            <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                        {% endif %}
                    </div>
                {% endif %}
                {% endfor %}
            </div>
            
            <div class="mt-8 flex justify-end">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Apply Filters
                </button>
            </div>
        </form>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-xl font-semibold text-gray-700">Report Results</h3>
            <div id="report-loading-indicator" class="htmx-indicator inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 hidden"></div>
        </div>
        <div id="report-table-container"
             hx-trigger="load delay:100ms"
             hx-get="{% url 'ratelockunlockentry_report_table' %}"
             hx-swap="innerHTML">
            <!-- Initial loading message -->
            <div class="text-center py-12">
                <div class="inline-block animate-spin rounded-full h-10 w-10 border-b-2 border-blue-500"></div>
                <p class="mt-4 text-gray-600">Loading report data...</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Alpine.js or other custom JS for date pickers if needed
    document.addEventListener('alpine:init', () => {
        // Example for date pickers (using flatpickr or similar)
        // Ensure your base.html includes the necessary CDN for flatpickr or a similar library
        // flatpickr("input[type='date']", {});
    });
</script>
{% endblock %}
```

**`material_management/templates/material_management/ratelockunlockentry/_report_table.html` (Report Table Partial)**

```html
{% load static %}
<div class="overflow-x-auto">
    <table id="rateLockUnlockReportTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Description</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Session ID (Locked By)</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate Value</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lock Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unlock Status</th>
                <!-- Add more headers as per your report requirements -->
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for entry in ratelockunlock_entries %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.get_display_item_code }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.get_display_item_description }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.get_display_item_category }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.sys_date|date:"m/d/Y H:i" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.session_id }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.rate_value }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.quantity }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.lock_status|yesno:"Locked,Unlocked" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ entry.unlock_status|yesno:"Unlocked,Locked" }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="10" class="py-4 px-6 text-center text-sm text-gray-500">No data found matching the selected filters.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after the content is loaded via HTMX
    $(document).ready(function() {
        $('#rateLockUnlockReportTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "paging": true,      // Enable pagination
            "ordering": true,    // Enable sorting
            "searching": true,   // Enable search box
            "info": true         // Show info like "Showing 1 to 10 of X entries"
        });
    });
</script>
```

**`material_management/templates/material_management/ratelockunlockentry/list.html` (CRUD List View)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Rate Lock Unlock Entries Management</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm"
            hx-get="{% url 'ratelockunlockentry_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Entry
        </button>
    </div>
    
    <div id="ratelockunlockentryTable-container"
         hx-trigger="load, refreshRateLockUnlockEntryList from:body"
         hx-get="{% url 'ratelockunlockentry_table_partial' %}" {# Separate endpoint for the list table #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center py-12">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading entries...</p>
        </div>
    </div>
    
    <!-- Modal for CRUD forms -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components if needed for UI state management not covered by HTMX
    });
</script>
{% endblock %}
```

**`material_management/templates/material_management/ratelockunlockentry/_list_table.html` (CRUD List Table Partial)**

```html
{% load static %}
<div class="overflow-x-auto">
    <table id="ratelockunlockentryTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sys Date</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Locked By</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate Value</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Lock Status</th>
                <th class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for obj in ratelockunlockentries %}
            <tr>
                <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.item.item_code if obj.item else 'N/A' }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.sys_date|date:"m/d/Y H:i" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.session_id }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.rate_value }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ obj.lock_status|yesno:"Locked,Unlocked" }}</td>
                <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                        hx-get="{% url 'ratelockunlockentry_edit' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                        hx-get="{% url 'ratelockunlockentry_delete' obj.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-sm text-gray-500">No Rate Lock Unlock entries found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
$(document).ready(function() {
    $('#ratelockunlockentryTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**`material_management/templates/material_management/ratelockunlockentry/form.html` (Form Partial)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Rate Lock Unlock Entry</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#form-loading-indicator">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Save
            </button>
            <div id="form-loading-indicator" class="htmx-indicator inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500 hidden"></div>
        </div>
    </form>
</div>
```

**`material_management/templates/material_management/ratelockunlockentry/confirm_delete.html` (Delete Confirmation Partial)**

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the Rate Lock Unlock Entry for item "{{ ratelockunlockentry.item.item_code if ratelockunlockentry.item else 'N/A' }}" (ID: {{ ratelockunlockentry.rlu_id }})?</p>
    
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md shadow-sm"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views within the `material_management` app.

**`material_management/urls.py`**

```python
from django.urls import path
from .views import (
    RateLockUnlockReportView,
    RateLockUnlockTablePartialView,
    RateLockUnlockEntryListView,
    RateLockUnlockEntryCreateView,
    RateLockUnlockEntryUpdateView,
    RateLockUnlockEntryDeleteView
)

urlpatterns = [
    # Report-specific URLs
    path('ratelockunlock/report/', RateLockUnlockReportView.as_view(), name='ratelockunlockentry_report'),
    path('ratelockunlock/report/table/', RateLockUnlockTablePartialView.as_view(), name='ratelockunlockentry_report_table'),

    # Standard CRUD URLs for RateLockUnlockEntry
    path('ratelockunlock/entries/', RateLockUnlockEntryListView.as_view(), name='ratelockunlockentry_list'),
    path('ratelockunlock/entries/table/', RateLockUnlockEntryListView.as_view(), name='ratelockunlockentry_table_partial'), # Same view for table partial
    path('ratelockunlock/entries/add/', RateLockUnlockEntryCreateView.as_view(), name='ratelockunlockentry_add'),
    path('ratelockunlock/entries/edit/<int:pk>/', RateLockUnlockEntryUpdateView.as_view(), name='ratelockunlockentry_edit'),
    path('ratelockunlock/entries/delete/<int:pk>/', RateLockUnlockEntryDeleteView.as_view(), name='ratelockunlockentry_delete'),
]
```

#### 4.6 Tests

Task: Write tests for the models and views, ensuring comprehensive coverage.

**`material_management/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from datetime import datetime
from unittest.mock import patch

from .models import ItemMaster, RateLockUnlockEntry

# Mock session context for tests
def mock_get_session_context(request):
    return {'compid': 1, 'finyear': 2023}

class ItemMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for ItemMaster
        ItemMaster.objects.create(item_id=1, category_id='FABRIC', item_code='ITEM001', manf_description='Super Fabric', company_id=1, financial_year_id=2023)
        ItemMaster.objects.create(item_id=2, category_id='YARN', item_code='YARN001', manf_description='Cotton Yarn', company_id=1, financial_year_id=2023)
        ItemMaster.objects.create(item_id=3, category_id='FABRIC', item_code='ITEM002', manf_description='Nylon Fabric', company_id=2, financial_year_id=2022)

    def test_item_master_creation(self):
        item = ItemMaster.objects.get(item_id=1)
        self.assertEqual(item.category_id, 'FABRIC')
        self.assertEqual(item.item_code, 'ITEM001')
        self.assertEqual(item.manf_description, 'Super Fabric')

    def test_str_method(self):
        item = ItemMaster.objects.get(item_id=1)
        self.assertEqual(str(item), 'ITEM001 - Super Fabric')

class RateLockUnlockEntryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create related ItemMaster data
        cls.item1 = ItemMaster.objects.create(item_id=101, category_id='CAT_A', item_code='A001', manf_description='Desc A', company_id=1, financial_year_id=2023)
        cls.item2 = ItemMaster.objects.create(item_id=102, category_id='CAT_B', item_code='B001', manf_description='Desc B', company_id=1, financial_year_id=2023)
        
        # Create test data for RateLockUnlockEntry
        cls.entry1 = RateLockUnlockEntry.objects.create(
            rlu_id=1, item=cls.item1, session_id='UserA', sys_date=datetime(2023, 1, 15, 10, 0),
            lock_status=True, unlock_status=False, rate_value=100.50, quantity=10.0,
            company_id=1, financial_year_id=2023
        )
        cls.entry2 = RateLockUnlockEntry.objects.create(
            rlu_id=2, item=cls.item2, session_id='UserB', sys_date=datetime(2023, 2, 20, 11, 30),
            lock_status=False, unlock_status=True, rate_value=200.00, quantity=5.0,
            company_id=1, financial_year_id=2023
        )
        cls.entry3 = RateLockUnlockEntry.objects.create(
            rlu_id=3, item=cls.item1, session_id='UserA', sys_date=datetime(2022, 12, 1, 9, 0),
            lock_status=True, unlock_status=False, rate_value=150.00, quantity=7.0,
            company_id=1, financial_year_id=2022 # Different financial year
        )
        cls.entry4 = RateLockUnlockEntry.objects.create(
            rlu_id=4, item=cls.item1, session_id='UserC', sys_date=datetime(2023, 1, 20, 14, 0),
            lock_status=False, unlock_status=False, rate_value=120.00, quantity=3.0,
            company_id=1, financial_year_id=2023
        )

    def test_entry_creation(self):
        self.assertEqual(self.entry1.session_id, 'UserA')
        self.assertEqual(self.entry1.item.item_code, 'A001')
        self.assertTrue(self.entry1.lock_status)
        self.assertFalse(self.entry1.unlock_status)
        self.assertEqual(self.entry1.rate_value, 100.50)

    def test_str_method(self):
        self.assertEqual(str(self.entry1), 'RLU 1 - Item: A001')

    def test_update_status_method(self):
        self.entry1.update_status(False, True)
        self.entry1.refresh_from_db()
        self.assertFalse(self.entry1.lock_status)
        self.assertTrue(self.entry1.unlock_status)

    def test_get_display_methods(self):
        self.assertEqual(self.entry1.get_display_item_category(), 'CAT_A')
        self.assertEqual(self.entry1.get_display_item_code(), 'A001')
        self.assertEqual(self.entry1.get_display_item_description(), 'Desc A')

    def test_report_data_filtering_no_filters(self):
        # Test with minimal filters (only mandatory session context)
        filters = {'compid': 1, 'finyear': 2023}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 3) # entry1, entry2, entry4 (entry3 is finyear 2022)
        self.assertIn(self.entry1, queryset)
        self.assertIn(self.entry2, queryset)
        self.assertIn(self.entry4, queryset)
        self.assertNotIn(self.entry3, queryset)

    def test_report_data_filtering_category(self):
        filters = {'compid': 1, 'finyear': 2023, 'Type': 'Category', 'category': 'CAT_A'}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 2) # entry1, entry4 (entry3 is CAT_A but finyear 2022)
        self.assertIn(self.entry1, queryset)
        self.assertIn(self.entry4, queryset)

    def test_report_data_filtering_item_code_startswith_category(self):
        filters = {'compid': 1, 'finyear': 2023, 'Type': 'Category', 'category': 'CAT_A', 'SearchCode': '1', 'SearchItemCode': 'A00'}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 2) # entry1, entry4
        self.assertIn(self.entry1, queryset)
        self.assertIn(self.entry4, queryset)

    def test_report_data_filtering_manf_desc_contains_category(self):
        filters = {'compid': 1, 'finyear': 2023, 'Type': 'Category', 'category': 'CAT_A', 'SearchCode': '2', 'SearchItemCode': 'Desc'}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 2) # entry1, entry4
        self.assertIn(self.entry1, queryset)
        self.assertIn(self.entry4, queryset)
    
    def test_report_data_filtering_wo_items_item_code_contains(self):
        filters = {'compid': 1, 'finyear': 2023, 'Type': 'WOItems', 'SearchCode': '1', 'SearchItemCode': 'B00'}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 1) # entry2
        self.assertIn(self.entry2, queryset)

    def test_report_data_filtering_locked_by(self):
        filters = {'compid': 1, 'finyear': 2023, 'LockedBy': 'UserA'}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 1) # entry1 (entry3 is finyear 2022)
        self.assertIn(self.entry1, queryset)
        self.assertNotIn(self.entry3, queryset)

    def test_report_data_filtering_date_range(self):
        filters = {'compid': 1, 'finyear': 2023, 'FDate': '01/15/2023', 'TDate': '01/20/2023'}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 2) # entry1, entry4
        self.assertIn(self.entry1, queryset)
        self.assertIn(self.entry4, queryset)
        self.assertNotIn(self.entry2, queryset)

    def test_report_data_filtering_invalid_date(self):
        filters = {'compid': 1, 'finyear': 2023, 'FDate': 'invalid-date', 'TDate': '01/20/2023'}
        queryset = RateLockUnlockEntry.objects.get_report_data(filters)
        self.assertEqual(queryset.count(), 0)

@patch('material_management.views.get_session_context', side_effect=mock_get_session_context)
class RateLockUnlockViewsTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.item1 = ItemMaster.objects.create(item_id=101, category_id='CAT_A', item_code='A001', manf_description='Desc A', company_id=1, financial_year_id=2023)
        self.item2 = ItemMaster.objects.create(item_id=102, category_id='CAT_B', item_code='B001', manf_description='Desc B', company_id=1, financial_year_id=2023)
        self.entry = RateLockUnlockEntry.objects.create(
            rlu_id=1, item=self.item1, session_id='UserX', sys_date=datetime(2023, 1, 1, 12, 0),
            lock_status=True, unlock_status=False, rate_value=50.00, quantity=5.0,
            company_id=1, financial_year_id=2023
        )

    def test_report_view_get(self, mock_get_session_context_arg):
        response = self.client.get(reverse('ratelockunlockentry_report'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/ratelockunlockentry/report.html')
        self.assertIsInstance(response.context['filter_form'], RateLockUnlockReportFilterForm)

    def test_report_table_partial_view_get(self, mock_get_session_context_arg):
        response = self.client.get(reverse('ratelockunlockentry_report_table'), {'compid': 1, 'finyear': 2023})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/ratelockunlockentry/_report_table.html')
        self.assertTrue('ratelockunlock_entries' in response.context)
        self.assertEqual(len(response.context['ratelockunlock_entries']), 1) # Only one entry matches initial dummy context

    def test_report_table_partial_view_get_with_filters(self, mock_get_session_context_arg):
        filters = {
            'compid': 1, 'finyear': 2023, 'Type': 'Category', 'category': 'CAT_A',
            'FDate': '01/01/2023', 'TDate': '01/31/2023'
        }
        response = self.client.get(reverse('ratelockunlockentry_report_table'), filters)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['ratelockunlock_entries']), 1)
        self.assertEqual(response.context['ratelockunlock_entries'][0].rlu_id, self.entry.rlu_id)

    def test_list_view(self, mock_get_session_context_arg):
        response = self.client.get(reverse('ratelockunlockentry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/ratelockunlockentry/list.html')
        self.assertTrue('ratelockunlockentries' in response.context)
        self.assertEqual(len(response.context['ratelockunlockentries']), 1)

    def test_create_view_get(self, mock_get_session_context_arg):
        response = self.client.get(reverse('ratelockunlockentry_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/ratelockunlockentry/form.html')
        self.assertIsInstance(response.context['form'], RateLockUnlockEntryForm)

    def test_create_view_post_success(self, mock_get_session_context_arg):
        data = {
            'item': self.item2.pk,
            'session_id': 'NewUser',
            'sys_date': '2023-03-10T14:00',
            'lock_status': 'on',
            'unlock_status': '',
            'rate_value': '75.00',
            'quantity': '12.0',
            'company_id': 1,
            'financial_year_id': 2023
        }
        response = self.client.post(reverse('ratelockunlockentry_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertTrue(RateLockUnlockEntry.objects.filter(session_id='NewUser').exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshRateLockUnlockEntryList')

    def test_update_view_get(self, mock_get_session_context_arg):
        response = self.client.get(reverse('ratelockunlockentry_edit', args=[self.entry.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/ratelockunlockentry/form.html')
        self.assertEqual(response.context['form'].instance, self.entry)

    def test_update_view_post_success(self, mock_get_session_context_arg):
        data = {
            'item': self.item1.pk,
            'session_id': 'UserX_Updated',
            'sys_date': '2023-01-01T12:00',
            'lock_status': '',
            'unlock_status': 'on',
            'rate_value': '55.00',
            'quantity': '6.0',
            'company_id': 1,
            'financial_year_id': 2023
        }
        response = self.client.post(reverse('ratelockunlockentry_edit', args=[self.entry.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.entry.refresh_from_db()
        self.assertEqual(self.entry.session_id, 'UserX_Updated')
        self.assertIn('HX-Trigger', response.headers)

    def test_delete_view_get(self, mock_get_session_context_arg):
        response = self.client.get(reverse('ratelockunlockentry_delete', args=[self.entry.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/ratelockunlockentry/confirm_delete.html')
        self.assertEqual(response.context['ratelockunlockentry'], self.entry)

    def test_delete_view_post_success(self, mock_get_session_context_arg):
        response = self.client.post(reverse('ratelockunlockentry_delete', args=[self.entry.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(RateLockUnlockEntry.objects.filter(pk=self.entry.pk).exists())
        self.assertIn('HX-Trigger', response.headers)
```

### Step 5: HTMX and Alpine.js Integration

This plan fully integrates HTMX for dynamic content loading, form submissions, and table refreshes. Alpine.js is suggested for modal toggling, complementing HTMX's server-side rendering with client-side UI state management. DataTables is implemented for interactive lists, providing out-of-the-box sorting, searching, and pagination.

-   **Report Page (`report.html`)**: Filters are submitted via `hx-get` to `ratelockunlockentry_report_table`, updating only the report table area.
-   **CRUD List Page (`list.html`)**: The table is loaded via `hx-get` to `ratelockunlockentry_table_partial`. CRUD actions (`add`, `edit`, `delete`) open forms in an HTMX-powered modal (`#modalContent`).
-   **Form Submissions**: `hx-post` is used for form submissions. Upon success, views return `HttpResponse(status=204)` with `HX-Trigger` headers to inform HTMX to `refreshRateLockUnlockEntryList`, automatically reloading the main table without a full page refresh.
-   **Modals**: Alpine.js `x-data` and `x-show` would manage modal visibility (e.g., `hidden` class toggle) triggered by HTMX `on click` events (using `_` syntax).
-   **DataTables**: Initialized in `_report_table.html` and `_list_table.html` `<script>` tags, ensuring they re-initialize correctly after HTMX swaps.

## Final Notes

This comprehensive plan provides a clear roadmap for migrating your ASP.NET Rate Lock Unlock Details report to a modern Django application.

-   **Business Value:** This transition moves your application from a legacy, proprietary reporting system to a flexible, open-source, and web-native platform. It significantly enhances user experience with dynamic, interactive reports, improves maintainability, and positions the application for future scalability and feature development with a standard, well-supported technology stack. The focus on automation means faster, more reliable transitions.
-   **Efficiency:** The "Fat Model, Thin View" approach centralizes business logic, making it easier to test and maintain. HTMX and Alpine.js drastically reduce the complexity of frontend development by minimizing custom JavaScript, allowing for rapid iteration.
-   **Scalability:** Django's robust ORM and architecture are well-suited for handling complex data operations and can scale to meet growing business demands.
-   **Automated Conversion:** The detailed breakdown into specific Django application files and the consistent use of patterns enable automated tools to generate much of this code, drastically reducing manual development effort and potential for human error. The instructions are designed to be actionable by a conversational AI.

To execute this plan, ensure your Django environment is set up with necessary dependencies (like `django-environ` for settings, `psycopg2` for PostgreSQL if applicable, etc.) and integrate with your existing database using the `managed = False` setup.