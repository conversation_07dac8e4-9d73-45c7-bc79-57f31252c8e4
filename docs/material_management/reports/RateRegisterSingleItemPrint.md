This modernization plan details the conversion of a legacy ASP.NET Crystal Reports viewer for a "Rate Register" into a modern Django 5.0+ application. It focuses on replacing the Crystal Reports dependency with interactive web components using Django's "Fat Model, Thin View" architecture, HTMX, Alpine.js, and DataTables.

The original ASP.NET page primarily served as a report display, fetching data based on query string parameters (`CompId`, `ItemId`) and binding it to a Crystal Report. Our Django solution will transform this static report into a dynamic, searchable, and sortable web table, while also providing full CRUD capabilities for the underlying "Item Rate" data.

## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code and inferred functionality.

## Instructions:

Based on the `RateRegister` function (likely fetching item rates) and `CompAdd` (fetching company address), we infer the following database tables:

**`tbl_ItemRates`** (for storing individual rate entries):
*   `ItemRateId` (int, Primary Key)
*   `ItemId` (int, Foreign Key to `tbl_Item`)
*   `CompanyId` (int, Foreign Key to `tbl_Company`)
*   `RateValue` (decimal)
*   `EffectiveFromDate` (date)
*   `CurrencyCode` (nvarchar(10))
*   `UnitMeasure` (nvarchar(50))

**`tbl_Item`** (inferred, for item details):
*   `ItemId` (int, Primary Key)
*   `ItemCode` (nvarchar(50))
*   `ItemName` (nvarchar(255))
*   `ItemDescription` (nvarchar(MAX))

**`tbl_Company`** (inferred, for company details):
*   `CompanyId` (int, Primary Key)
*   `CompanyName` (nvarchar(255))
*   `AddressLine1` (nvarchar(255))
*   `AddressLine2` (nvarchar(255))
*   `City` (nvarchar(100))
*   `State` (nvarchar(100))
*   `ZipCode` (nvarchar(20))

## Step 2: Identify Backend Functionality

Task: Determine the core operations implied by the ASP.NET code.

## Instructions:

The original ASP.NET code primarily performs a **Read** operation to generate a report:
*   **Read (Report Data):** The `fun.RateRegister(ItemId, CompId)` retrieves historical rate data for a specific item and company.
*   **Read (Company Address):** The `fun.CompAdd(CompId)` retrieves the address for a given company.
*   **Report Generation:** Crystal Reports combines this data into a formatted report.

While the original ASP.NET only showcased reporting, for a complete modernization, we will implement full CRUD (Create, Read, Update, Delete) functionality for the core `ItemRate` data, in addition to recreating the report viewing capability as an interactive table.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

*   **`CrystalReportViewer`**: This was used to display the formatted report. In Django, this will be replaced by DataTables for interactive display of tabular data, potentially with export options (e.g., PDF) as an enhancement.
*   **`Panel`**: Used for scrolling the report viewer. This functionality will be handled naturally by responsive web design and DataTables' pagination.
*   The page implicitly accepted parameters (`CompId`, `ItemId`) via query strings. In Django, these will be passed directly in the URL path for cleaner, RESTful URLs.

## Step 4: Generate Django Code

We will create a Django application named `material_management` to house these components, aligning with the original module structure.

### 4.1 Models

Task: Create Django models based on the identified database schemas.

## Instructions:

We define `ItemRate`, `Item`, and `Company` models. The `ItemRate` model will include methods to replicate the `fun.RateRegister` functionality, demonstrating the "Fat Model" principle.

```python
# material_management/models.py
from django.db import models

class Company(models.Model):
    """
    Represents a Company from the legacy tbl_Company table.
    """
    company_id = models.IntegerField(db_column='CompanyId', primary_key=True)
    company_name = models.CharField(db_column='CompanyName', max_length=255, blank=True, null=True)
    address_line1 = models.CharField(db_column='AddressLine1', max_length=255, blank=True, null=True)
    address_line2 = models.CharField(db_column='AddressLine2', max_length=255, blank=True, null=True)
    city = models.CharField(db_column='City', max_length=100, blank=True, null=True)
    state = models.CharField(db_column='State', max_length=100, blank=True, null=True)
    zip_code = models.CharField(db_column='ZipCode', max_length=20, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tbl_Company'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name or f"Company ID: {self.company_id}"

    @classmethod
    def get_company_address(cls, company_id):
        """
        Simulates fun.CompAdd(CompId) by retrieving the full address string.
        """
        try:
            company = cls.objects.get(company_id=company_id)
            address_parts = [
                company.address_line1,
                company.address_line2,
                f"{company.city}, {company.state} {company.zip_code}"
            ]
            return ", ".join(filter(None, address_parts))
        except cls.DoesNotExist:
            return "Address not found"

class Item(models.Model):
    """
    Represents an Item from the legacy tbl_Item table.
    """
    item_id = models.IntegerField(db_column='ItemId', primary_key=True)
    item_code = models.CharField(db_column='ItemCode', max_length=50, blank=True, null=True)
    item_name = models.CharField(db_column='ItemName', max_length=255, blank=True, null=True)
    item_description = models.TextField(db_column='ItemDescription', blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tbl_Item'
        verbose_name = 'Item'
        verbose_name_plural = 'Items'

    def __str__(self):
        return self.item_name or f"Item ID: {self.item_id}"


class ItemRateManager(models.Manager):
    """
    Custom manager for ItemRate to encapsulate report-specific data fetching.
    """
    def get_rate_register_data(self, item_id, company_id):
        """
        Simulates fun.RateRegister(ItemId, CompId) by fetching historical rates
        for a given item and company, including related details.
        """
        return self.get_queryset().filter(
            item__item_id=item_id,
            company__company_id=company_id
        ).select_related('item', 'company').order_by('-effective_from_date')


class ItemRate(models.Model):
    """
    Represents a single rate entry from the legacy tbl_ItemRates table.
    """
    item_rate_id = models.IntegerField(db_column='ItemRateId', primary_key=True)
    item = models.ForeignKey(Item, models.DO_NOTHING, db_column='ItemId', verbose_name='Item')
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompanyId', verbose_name='Company')
    rate_value = models.DecimalField(db_column='RateValue', max_digits=18, decimal_places=4)
    effective_from_date = models.DateField(db_column='EffectiveFromDate')
    currency_code = models.CharField(db_column='CurrencyCode', max_length=10, default='USD')
    unit_measure = models.CharField(db_column='UnitMeasure', max_length=50, default='Each')

    objects = ItemRateManager() # Attach the custom manager

    class Meta:
        managed = False
        db_table = 'tbl_ItemRates'
        verbose_name = 'Item Rate'
        verbose_name_plural = 'Item Rates'

    def __str__(self):
        return f"Rate for {self.item.item_name} at {self.company.company_name}: {self.rate_value}"
    
    # Business logic methods can be added here if needed, e.g., validation or calculation
    def is_current_rate(self):
        """
        Example business logic: Checks if this is the most current rate for its item/company.
        """
        latest_rate = ItemRate.objects.filter(
            item=self.item,
            company=self.company,
            effective_from_date__lte=models.functions.Now().get_db_prep_value(None, None)
        ).order_by('-effective_from_date').first()
        return self == latest_rate

```

### 4.2 Forms

Task: Define a Django form for `ItemRate` for user input.

## Instructions:

A `ModelForm` is created for `ItemRate`, including appropriate widgets for styling and basic validation.

```python
# material_management/forms.py
from django import forms
from .models import ItemRate, Item, Company

class ItemRateForm(forms.ModelForm):
    item = forms.ModelChoiceField(
        queryset=Item.objects.all(),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select an Item"
    )
    company = forms.ModelChoiceField(
        queryset=Company.objects.all(),
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        empty_label="Select a Company"
    )
    effective_from_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        help_text='Format: YYYY-MM-DD'
    )

    class Meta:
        model = ItemRate
        fields = ['item', 'company', 'rate_value', 'effective_from_date', 'currency_code', 'unit_measure']
        widgets = {
            'rate_value': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'step': '0.0001'}),
            'currency_code': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'unit_measure': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'item': 'Item',
            'company': 'Company',
            'rate_value': 'Rate Value',
            'effective_from_date': 'Effective From Date',
            'currency_code': 'Currency Code',
            'unit_measure': 'Unit of Measure',
        }
        
    def clean(self):
        cleaned_data = super().clean()
        # Add custom validation logic here if needed, e.g., unique constraint for item/company/date
        return cleaned_data
```

### 4.3 Views

Task: Implement CRUD operations using CBVs for `ItemRate` and a dedicated view for the "Rate Register Report".

## Instructions:

Views are kept thin, delegating business logic to models. HTMX headers are handled for dynamic updates.

```python
# material_management/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import get_object_or_404
from .models import ItemRate, Item, Company
from .forms import ItemRateForm

# --- CRUD Views for ItemRate ---

class ItemRateListView(ListView):
    model = ItemRate
    template_name = 'material_management/itemrate/list.html'
    context_object_name = 'itemrates'
    paginate_by = 10 # Example pagination

    def get_queryset(self):
        # Prefetch related objects to avoid N+1 queries
        return super().get_queryset().select_related('item', 'company')

class ItemRateTablePartialView(View):
    """
    Renders the item rates table content for HTMX requests.
    """
    def get(self, request, *args, **kwargs):
        itemrates = ItemRate.objects.all().select_related('item', 'company')
        return render(request, 'material_management/itemrate/_itemrate_table.html', {'itemrates': itemrates})

class ItemRateCreateView(CreateView):
    model = ItemRate
    form_class = ItemRateForm
    template_name = 'material_management/itemrate/form.html'
    success_url = reverse_lazy('itemrate_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item Rate added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemRateList' # Trigger HTMX event to refresh list
                }
            )
        return response

class ItemRateUpdateView(UpdateView):
    model = ItemRate
    form_class = ItemRateForm
    template_name = 'material_management/itemrate/form.html'
    success_url = reverse_lazy('itemrate_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item Rate updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemRateList'
                }
            )
        return response

class ItemRateDeleteView(DeleteView):
    model = ItemRate
    template_name = 'material_management/itemrate/confirm_delete.html'
    success_url = reverse_lazy('itemrate_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item Rate deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshItemRateList'
                }
            )
        return response

# --- Report View for Rate Register ---

class RateRegisterReportView(TemplateView):
    """
    Displays the Rate Register report for a specific item and company,
    similar to the original ASP.NET page.
    """
    template_name = 'material_management/rateregister/report.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        item_id = self.kwargs['item_id']
        company_id = self.kwargs['company_id']

        # Fetch report data using the fat model
        context['rate_entries'] = ItemRate.objects.get_rate_register_data(item_id, company_id)
        context['company_address'] = Company.get_company_address(company_id)
        
        # Optionally, get Item and Company details for display
        context['item'] = get_object_or_404(Item, item_id=item_id)
        context['company'] = get_object_or_404(Company, company_id=company_id)

        return context

from django.shortcuts import render # Ensure render is imported for partial views
```

### 4.4 Templates

Task: Create templates for each view.

## Instructions:

Templates extend `core/base.html`, use DataTables for lists, and implement HTMX for dynamic interactions, particularly for modal forms and refreshing lists.

**`material_management/itemrate/list.html`** (Main list page for Item Rates):

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Item Rates</h2>
        <button 
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            hx-get="{% url 'itemrate_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Item Rate
        </button>
    </div>
    
    <div id="itemrateTable-container"
         hx-trigger="load, refreshItemRateList from:body"
         hx-get="{% url 'itemrate_table_partial' %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2">Loading Item Rates...</p>
        </div>
    </div>
    
    <!-- Modal for form -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component initialization if needed for UI state
        // For example, managing modal visibility directly with Alpine
        // x-data="{ showModal: false }" on the modal div
    });
</script>
{% endblock %}
```

**`material_management/itemrate/_itemrate_table.html`** (Partial for DataTables list content):

```html
<table id="itemrateTable" class="min-w-full bg-white">
    <thead>
        <tr>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company Name</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate Value</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective From</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
            <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for obj in itemrates %}
        <tr>
            <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.item.item_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.company.company_name }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.rate_value }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.effective_from_date|date:"Y-m-d" }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.currency_code }}</td>
            <td class="py-2 px-4 border-b border-gray-200">{{ obj.unit_measure }}</td>
            <td class="py-2 px-4 border-b border-gray-200">
                <button 
                    class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded mr-2"
                    hx-get="{% url 'itemrate_edit' obj.item_rate_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Edit
                </button>
                <button 
                    class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded"
                    hx-get="{% url 'itemrate_delete' obj.item_rate_id %}"
                    hx-target="#modalContent"
                    hx-trigger="click"
                    _="on click add .is-active to #modal">
                    Delete
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>

<script>
$(document).ready(function() {
    $('#itemrateTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
```

**`material_management/itemrate/form.html`** (Partial for Add/Edit form, loaded in modal):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Item Rate</h3>
    <form hx-post="{{ request.path }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors }}</p>
                {% endif %}
                {% if field.help_text %}
                <p class="mt-2 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

**`material_management/itemrate/confirm_delete.html`** (Partial for delete confirmation):

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700">Are you sure you want to delete the item rate for <strong>{{ itemrate.item.item_name }}</strong> at <strong>{{ itemrate.company.company_name }}</strong> (Rate: {{ itemrate.rate_value }})?</p>
    
    <form hx-post="{% url 'itemrate_delete' itemrate.item_rate_id %}" hx-swap="none">
        {% csrf_token %}
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                Delete
            </button>
        </div>
    </form>
</div>
```

**`material_management/rateregister/report.html`** (Dedicated template for the Rate Register Report view):

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold">Rate Register Report</h2>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
        <h3 class="text-xl font-semibold mb-4">Report Details</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
                <p class="text-sm font-medium text-gray-600">Item:</p>
                <p class="text-lg font-bold text-gray-900">{{ item.item_name }} ({{ item.item_code }})</p>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Company:</p>
                <p class="text-lg font-bold text-gray-900">{{ company.company_name }}</p>
            </div>
            <div class="md:col-span-2">
                <p class="text-sm font-medium text-gray-600">Company Address:</p>
                <p class="text-base text-gray-900">{{ company_address }}</p>
            </div>
        </div>
    </div>

    <div class="bg-white shadow-md rounded-lg p-6">
        <h3 class="text-xl font-semibold mb-4">Historical Rates</h3>
        {% if rate_entries %}
            <table id="rateRegisterTable" class="min-w-full bg-white">
                <thead>
                    <tr>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate Value</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Effective From</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
                        <th class="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                    </tr>
                </thead>
                <tbody>
                    {% for entry in rate_entries %}
                    <tr>
                        <td class="py-2 px-4 border-b border-gray-200">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ entry.rate_value }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ entry.effective_from_date|date:"Y-m-d" }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ entry.currency_code }}</td>
                        <td class="py-2 px-4 border-b border-gray-200">{{ entry.unit_measure }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <p class="text-gray-600">No rate entries found for this item and company.</p>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    $('#rateRegisterTable').DataTable({
        "pageLength": 10,
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
    });
});
</script>
{% endblock %}
```

### 4.5 URLs

Task: Define URL patterns for the views.

## Instructions:

Create paths for list, create, update, delete for `ItemRate`, and a dedicated URL for the `RateRegisterReportView`.

```python
# material_management/urls.py
from django.urls import path
from .views import (
    ItemRateListView, ItemRateCreateView, ItemRateUpdateView, ItemRateDeleteView,
    ItemRateTablePartialView, RateRegisterReportView
)

urlpatterns = [
    # CRUD URLs for ItemRate
    path('itemrates/', ItemRateListView.as_view(), name='itemrate_list'),
    path('itemrates/add/', ItemRateCreateView.as_view(), name='itemrate_add'),
    path('itemrates/edit/<int:pk>/', ItemRateUpdateView.as_view(), name='itemrate_edit'),
    path('itemrates/delete/<int:pk>/', ItemRateDeleteView.as_view(), name='itemrate_delete'),
    
    # HTMX partial view for the DataTables content
    path('itemrates/table-partial/', ItemRateTablePartialView.as_view(), name='itemrate_table_partial'),

    # URL for the Rate Register Report (migrated Crystal Report functionality)
    # Parameters ItemId and CompId from original QueryString are now URL parameters
    path('rate-register/report/<int:item_id>/<int:company_id>/', RateRegisterReportView.as_view(), name='rate_register_report'),
]
```

### 4.6 Tests

Task: Write tests for the models and views.

## Instructions:

Include comprehensive unit tests for model methods and integration tests for all views to ensure at least 80% test coverage.

```python       
# material_management/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from .models import ItemRate, Item, Company
from datetime import date

class CompanyModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Company.objects.create(
            company_id=1,
            company_name='Test Company A',
            address_line1='123 Main St',
            address_line2='Suite 100',
            city='Anytown',
            state='Anystate',
            zip_code='12345'
        )
        Company.objects.create(
            company_id=2,
            company_name='Test Company B',
            address_line1='456 Oak Ave',
            city='Otherville',
            state='Anystate',
            zip_code='67890'
        )
  
    def test_company_creation(self):
        company = Company.objects.get(company_id=1)
        self.assertEqual(company.company_name, 'Test Company A')
        self.assertEqual(company.zip_code, '12345')
        
    def test_company_address_method(self):
        company_a_address = Company.get_company_address(1)
        self.assertEqual(company_a_address, '123 Main St, Suite 100, Anytown, Anystate 12345')

        company_b_address = Company.get_company_address(2)
        self.assertEqual(company_b_address, '456 Oak Ave, Otherville, Anystate 67890')

        non_existent_address = Company.get_company_address(999)
        self.assertEqual(non_existent_address, 'Address not found')


class ItemModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        Item.objects.create(
            item_id=101,
            item_code='ITM001',
            item_name='Product X',
            item_description='Description for Product X'
        )
        Item.objects.create(
            item_id=102,
            item_code='ITM002',
            item_name='Product Y',
            item_description='Description for Product Y'
        )
    
    def test_item_creation(self):
        item = Item.objects.get(item_id=101)
        self.assertEqual(item.item_name, 'Product X')
        self.assertEqual(item.item_code, 'ITM001')


class ItemRateModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company1 = Company.objects.create(company_id=1, company_name='Comp A')
        cls.company2 = Company.objects.create(company_id=2, company_name='Comp B')
        cls.item1 = Item.objects.create(item_id=101, item_name='Item X')
        cls.item2 = Item.objects.create(item_id=102, item_name='Item Y')

        ItemRate.objects.create(
            item_rate_id=1,
            item=cls.item1,
            company=cls.company1,
            rate_value=10.50,
            effective_from_date=date(2023, 1, 1),
            currency_code='USD',
            unit_measure='KG'
        )
        ItemRate.objects.create(
            item_rate_id=2,
            item=cls.item1,
            company=cls.company1,
            rate_value=12.75,
            effective_from_date=date(2023, 3, 1),
            currency_code='USD',
            unit_measure='KG'
        )
        ItemRate.objects.create(
            item_rate_id=3,
            item=cls.item2,
            company=cls.company2,
            rate_value=5.00,
            effective_from_date=date(2023, 2, 1),
            currency_code='EUR',
            unit_measure='Each'
        )
  
    def test_itemrate_creation(self):
        item_rate = ItemRate.objects.get(item_rate_id=1)
        self.assertEqual(item_rate.rate_value, 10.50)
        self.assertEqual(item_rate.item.item_name, 'Item X')
        self.assertEqual(item_rate.company.company_name, 'Comp A')
        
    def test_get_rate_register_data(self):
        # Test for item1, company1
        rates = ItemRate.objects.get_rate_register_data(self.item1.item_id, self.company1.company_id)
        self.assertEqual(len(rates), 2)
        # Check ordering (most recent first)
        self.assertEqual(rates[0].rate_value, 12.75) 
        self.assertEqual(rates[1].rate_value, 10.50)

        # Test for item2, company2
        rates_single = ItemRate.objects.get_rate_register_data(self.item2.item_id, self.company2.company_id)
        self.assertEqual(len(rates_single), 1)
        self.assertEqual(rates_single[0].rate_value, 5.00)

        # Test for non-existent combination
        rates_none = ItemRate.objects.get_rate_register_data(999, 999)
        self.assertEqual(len(rates_none), 0)


class ItemRateViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company = Company.objects.create(company_id=1, company_name='TestComp', address_line1='Test St')
        cls.item = Item.objects.create(item_id=101, item_name='TestItem')
        ItemRate.objects.create(
            item_rate_id=1,
            item=cls.item,
            company=cls.company,
            rate_value=10.00,
            effective_from_date=date(2023, 1, 1),
            currency_code='USD',
            unit_measure='Unit'
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_list_view(self):
        response = self.client.get(reverse('itemrate_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/itemrate/list.html')
        self.assertContains(response, 'Add New Item Rate') # Check for specific text from template
        self.assertContains(response, '<div id="itemrateTable-container"') # Check for HTMX container

    def test_list_table_partial_view(self):
        response = self.client.get(reverse('itemrate_table_partial'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/itemrate/_itemrate_table.html')
        self.assertContains(response, 'itemrateTable') # Check for table ID
        self.assertContains(response, 'TestItem') # Check for data from setUpTestData

    def test_create_view_get(self):
        response = self.client.get(reverse('itemrate_add'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/itemrate/form.html')
        self.assertContains(response, 'Add Item Rate')
        self.assertTrue('form' in response.context)
        
    def test_create_view_post_success(self):
        data = {
            'item': self.item.item_id,
            'company': self.company.company_id,
            'rate_value': 15.00,
            'effective_from_date': '2024-01-01',
            'currency_code': 'USD',
            'unit_measure': 'Each'
        }
        response = self.client.post(reverse('itemrate_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(ItemRate.objects.count(), 2) # One created in setUp, one now
        self.assertTrue(ItemRate.objects.filter(rate_value=15.00).exists())
        
    def test_update_view_get(self):
        obj = ItemRate.objects.get(item_rate_id=1)
        response = self.client.get(reverse('itemrate_edit', args=[obj.item_rate_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/itemrate/form.html')
        self.assertContains(response, 'Edit Item Rate')
        self.assertEqual(response.context['form'].instance.rate_value, 10.00)

    def test_update_view_post_success(self):
        obj = ItemRate.objects.get(item_rate_id=1)
        data = {
            'item': obj.item.item_id,
            'company': obj.company.company_id,
            'rate_value': 12.50,
            'effective_from_date': '2023-01-01',
            'currency_code': 'USD',
            'unit_measure': 'Unit'
        }
        response = self.client.post(reverse('itemrate_edit', args=[obj.item_rate_id]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        obj.refresh_from_db()
        self.assertEqual(obj.rate_value, 12.50)

    def test_delete_view_get(self):
        obj = ItemRate.objects.get(item_rate_id=1)
        response = self.client.get(reverse('itemrate_delete', args=[obj.item_rate_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/itemrate/confirm_delete.html')
        self.assertContains(response, 'Confirm Deletion')

    def test_delete_view_post_success(self):
        obj = ItemRate.objects.get(item_rate_id=1)
        response = self.client.post(reverse('itemrate_delete', args=[obj.item_rate_id]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertIn('HX-Trigger', response.headers)
        self.assertFalse(ItemRate.objects.filter(item_rate_id=1).exists())


class RateRegisterReportViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.company_a = Company.objects.create(
            company_id=1, company_name='Company A', address_line1='Add1 A', city='City A', state='SA', zip_code='111'
        )
        cls.company_b = Company.objects.create(
            company_id=2, company_name='Company B', address_line1='Add1 B', city='City B', state='SB', zip_code='222'
        )
        cls.item_x = Item.objects.create(item_id=101, item_name='Item X', item_code='IX')
        cls.item_y = Item.objects.create(item_id=102, item_name='Item Y', item_code='IY')

        ItemRate.objects.create(item_rate_id=1, item=cls.item_x, company=cls.company_a, rate_value=10.00, effective_from_date=date(2023, 1, 1))
        ItemRate.objects.create(item_rate_id=2, item=cls.item_x, company=cls.company_a, rate_value=12.00, effective_from_date=date(2023, 2, 1))
        ItemRate.objects.create(item_rate_id=3, item=cls.item_y, company=cls.company_b, rate_value=5.00, effective_from_date=date(2023, 3, 1))
    
    def setUp(self):
        self.client = Client()

    def test_rate_register_report_view_success(self):
        response = self.client.get(reverse('rate_register_report', args=[self.item_x.item_id, self.company_a.company_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/rateregister/report.html')
        self.assertContains(response, 'Rate Register Report')
        self.assertContains(response, self.item_x.item_name)
        self.assertContains(response, self.company_a.company_name)
        self.assertContains(response, Company.get_company_address(self.company_a.company_id))
        self.assertContains(response, '10.00') # Check for rates
        self.assertContains(response, '12.00')
        self.assertEqual(response.context['item'].item_id, self.item_x.item_id)
        self.assertEqual(response.context['company'].company_id, self.company_a.company_id)
        self.assertEqual(len(response.context['rate_entries']), 2)

    def test_rate_register_report_view_no_data(self):
        # Use an item/company combo with no rates
        response = self.client.get(reverse('rate_register_report', args=[self.item_x.item_id, self.company_b.company_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'material_management/rateregister/report.html')
        self.assertContains(response, 'No rate entries found for this item and company.')
        self.assertEqual(len(response.context['rate_entries']), 0)

    def test_rate_register_report_view_non_existent_item_or_company(self):
        # Non-existent item
        response = self.client.get(reverse('rate_register_report', args=[999, self.company_a.company_id]))
        self.assertEqual(response.status_code, 404) # get_object_or_404 will raise Http404

        # Non-existent company
        response = self.client.get(reverse('rate_register_report', args=[self.item_x.item_id, 999]))
        self.assertEqual(response.status_code, 404) # get_object_or_404 will raise Http404

```

## Step 5: HTMX and Alpine.js Integration

## Instructions:

The provided templates and views already integrate HTMX for dynamic content loading, form submissions (add/edit/delete), and list refreshing (via `HX-Trigger`). Alpine.js is typically used for client-side UI state management, such as toggling modals, which is handled in the example `list.html` using the `_` (Hyperscript) attribute for simple DOM manipulation directly in HTML.

*   **HTMX for Dynamic Updates**: All CRUD operations on `ItemRate` trigger HTMX requests. Submitting forms (`hx-post`) results in a 204 No Content status with an `HX-Trigger` header (`refreshItemRateList`), which tells the `itemrateTable-container` to re-fetch its content, thus refreshing the DataTables without a full page reload.
*   **Modals with HTMX and Alpine.js/Hyperscript**: The list view uses HTMX `hx-get` on buttons to load form or delete confirmation partials into a modal container (`#modalContent`). The modal visibility is controlled using Alpine.js (or in this case, Hyperscript's `_` syntax for simplicity) to add/remove a `hidden` class on the modal div.
*   **DataTables for List Views**: Both the `ItemRateListView` and `RateRegisterReportView` leverage DataTables for client-side searching, sorting, and pagination, providing a rich user experience for tabular data.
*   **DRY Templates**: Partial templates (`_itemrate_table.html`, `_itemrate_form.html`, `_itemrate_confirm_delete.html`) are used for reusable components that can be loaded dynamically via HTMX.

## Final Notes

This modernization plan provides a complete blueprint for migrating the specified ASP.NET reporting functionality to a modern Django application.

*   **Business Value:** This transition replaces an expensive, Windows-dependent reporting tool (Crystal Reports) with a flexible, web-native solution. It offers a more interactive user experience, reduces licensing costs, improves accessibility across different devices, and enables faster development of new features. The added CRUD functionality on the `ItemRate` data provides a complete management interface where only a report viewer existed before.
*   **Automation Focus:** This plan is designed to be actionable for an AI-assisted automation process.
    *   **Schema Extraction:** Automated tools can parse `.aspx` and `.cs` files to identify database interactions (`SqlDataSource`, `SqlCommand`, `DataSet` usage) and infer table/column names.
    *   **Model Generation:** Once schema is extracted, Django models with `managed=False` and `db_table` can be automatically generated.
    *   **Form Generation:** Django `ModelForms` can be automatically created from models.
    *   **View Stub Generation:** Standard CBVs (ListView, CreateView, etc.) can be generated with appropriate `model`, `form_class`, `template_name`, and `success_url` based on model names. Custom logic for report views (`RateRegisterReportView`) needs pattern matching on `QueryString` parsing and `DataSet` binding.
    *   **Template Generation:** Templates with DataTables, HTMX attributes, and Alpine.js directives can be generated programmatically, substituting model/field names into the provided patterns.
    *   **URL Generation:** URL patterns follow a predictable structure and can be generated based on view names.
    *   **Test Generation:** Basic test stubs for models and views can be generated, with placeholders for specific assertions that may require manual refinement or advanced static analysis.
*   **Extensibility:** The new Django application is highly extensible. Further enhancements could include:
    *   PDF/Excel export options for the `Rate Register Report`.
    *   More sophisticated filtering/searching on the `ItemRateListView`.
    *   Integration with a proper authentication and authorization system (e.g., Django's built-in `User` model and permissions).
    *   Advanced error logging and monitoring.