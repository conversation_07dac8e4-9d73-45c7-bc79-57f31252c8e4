## ASP.NET to Django Conversion Script: 

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

## Instructions:

Based on the ASP.NET code analysis, the following database tables and their columns are identified:

-   **`tblCountry`**
    -   `CId` (Integer, Primary Key) - Maps to `country_id` in Django
    -   `CountryName` (String) - Maps to `country_name` in Django
-   **`tblState`**
    -   `SId` (Integer, Primary Key) - Maps to `state_id` in Django
    -   `StateName` (String) - Maps to `state_name` in Django
    -   `CId` (Integer, Foreign Key to `tblCountry`) - Maps to `country` in Django
-   **`tblCity`**
    -   `CityId` (Integer, Primary Key) - Maps to `city_id` in Django
    -   `CityName` (String) - Maps to `city_name` in Django
    -   `SId` (Integer, Foreign Key to `tblState`) - Maps to `state` in Django

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

## Instructions:

The ASP.NET application provides comprehensive functionality for managing cities, including filtering by country and state.

-   **Create (Add City):**
    -   Triggered by "Insert" button in the `GridView` footer or `EmptyDataTemplate`.
    -   Captures `CityName` and associates it with the currently selected `State`.
    -   Validation: `CityName` is a required field.
-   **Read (View Cities):**
    -   The `GridView1` displays a paginated list of cities.
    -   Initial load shows all cities.
    -   Filtering: Cities can be filtered by selecting a `Country` and then a `State`.
    -   Dropdowns for `Country` and `State` dynamically update based on selections.
-   **Update (Edit City):**
    -   Triggered by the "Edit" link within a `GridView` row.
    -   Allows modification of the `CityName`.
    -   The city is associated with the *currently selected* State on the page, not necessarily its original state.
    -   Validation: `CityName` is a required field.
-   **Delete (Remove City):**
    -   Triggered by the "Delete" link within a `GridView` row.
    -   Requires client-side confirmation (`confirmationDelete()`).
-   **Page Reloads:** Most CRUD operations in ASP.NET code result in a full page redirect/reload, which will be replaced by HTMX-driven partial updates in Django.

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

## Instructions:

The UI components in the ASP.NET application, along with their Django/HTMX equivalents, are:

-   **Country DropDownList (`DrpCountry`):**
    -   Django: `forms.ModelChoiceField` within a filter form.
    -   HTMX: `hx-get` to trigger updating the State dropdown when the Country selection changes.
-   **State DropDownList (`Drpstate`):**
    -   Django: `forms.ModelChoiceField` within a filter form.
    -   HTMX: `hx-get` to trigger updating the City table when the State selection changes. Its options are dynamically loaded based on the selected Country.
-   **GridView (`GridView1`):**
    -   Django: A combination of `ListView` and a DataTables-powered HTML table.
    -   HTMX: The entire table will be loaded dynamically via `hx-get` and `hx-trigger` for filtering and post-CRUD refreshes.
    -   **Edit/Delete Links:**
        -   Django: Buttons/links within the table rows.
        -   HTMX: `hx-get` to load edit/delete forms into a modal.
    -   **Insert Controls (Footer/EmptyDataTemplate):**
        -   Django: Separate `CreateView` rendered in a modal.
        -   HTMX: `hx-get` to load the add form into a modal.
-   **Message Label (`lblMessage`):**
    -   Django: `django.contrib.messages` framework.
    -   HTMX: Messages can be shown via `hx-swap` on a message container or `HX-Trigger` to show a toast.
-   **Client-side confirmations (`PopUpMsg.js`, `confirmationDelete()`):**
    -   Django: Alpine.js for modal logic and HTMX for form submission within modals.

## Step 4: Generate Django Code

### 4.1 Models

Task: Create a Django model based on the database schema.

**File: `sysadmin/models.py`**

```python
from django.db import models

class Country(models.Model):
    country_id = models.IntegerField(db_column='CId', primary_key=True)
    country_name = models.CharField(db_column='CountryName', max_length=255)

    class Meta:
        managed = False  # Ensures Django doesn't manage table creation/alteration
        db_table = 'tblCountry'
        verbose_name = 'Country'
        verbose_name_plural = 'Countries'

    def __str__(self):
        return self.country_name

class State(models.Model):
    state_id = models.IntegerField(db_column='SId', primary_key=True)
    state_name = models.CharField(db_column='StateName', max_length=255)
    country = models.ForeignKey(Country, models.DO_NOTHING, db_column='CId') # DO_NOTHING for existing database

    class Meta:
        managed = False
        db_table = 'tblState'
        verbose_name = 'State'
        verbose_name_plural = 'States'

    def __str__(self):
        return self.state_name

class City(models.Model):
    city_id = models.IntegerField(db_column='CityId', primary_key=True)
    city_name = models.CharField(db_column='CityName', max_length=255)
    state = models.ForeignKey(State, models.DO_NOTHING, db_column='SId') # DO_NOTHING for existing database

    class Meta:
        managed = False
        db_table = 'tblCity'
        verbose_name = 'City'
        verbose_name_plural = 'Cities'
        ordering = ['-city_id'] # Order by CityId Desc as seen in ASP.NET loadata

    def __str__(self):
        return self.city_name
    
    # --- Business Logic Methods (Fat Model) ---
    @classmethod
    def get_filtered_cities(cls, state_id=None):
        """
        Retrieves cities, optionally filtered by state.
        Corresponds to ASP.NET's loadata function.
        """
        if state_id and state_id != '0': # '0' represents 'Select' or all in ASP.NET logic
            try:
                return cls.objects.filter(state__state_id=int(state_id))
            except (ValueError, TypeError):
                # Handle invalid state_id gracefully, e.g., return all cities or an empty queryset
                return cls.objects.all()
        return cls.objects.all()

    @classmethod
    def create_new_city(cls, city_name, state_id):
        """
        Creates a new city record.
        Corresponds to ASP.NET's GridView1_RowCommand (Add/Add1).
        """
        state_instance = State.objects.get(state_id=state_id)
        city = cls.objects.create(city_name=city_name, state=state_instance)
        return city
        
    def update_existing_city(self, city_name, state_id):
        """
        Updates an existing city record.
        Corresponds to ASP.NET's GridView1_RowUpdating.
        """
        state_instance = State.objects.get(state_id=state_id)
        self.city_name = city_name
        self.state = state_instance
        self.save()
        return self
        
    def delete_existing_city(self):
        """
        Deletes a city record.
        Corresponds to ASP.NET's GridView1_RowCommand (Del).
        """
        self.delete()

```

### 4.2 Forms

Task: Define a Django form for user input.

**File: `sysadmin/forms.py`**

```python
from django import forms
from .models import City, State, Country

class CityForm(forms.ModelForm):
    # This form is used for Create and Update operations of a City.
    # The 'state' field must be a ModelChoiceField to allow selection,
    # and its queryset might be dynamically set in the view if filtered by country.
    state = forms.ModelChoiceField(
        queryset=State.objects.all().order_by('state_name'), # Default: all states
        empty_label="Select State",
        widget=forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        required=True
    )

    class Meta:
        model = City
        fields = ['city_name', 'state']
        widgets = {
            'city_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        }
        labels = {
            'city_name': 'City Name',
            'state': 'State',
        }
        
    def clean_city_name(self):
        city_name = self.cleaned_data['city_name']
        if not city_name: # Corresponds to RequiredFieldValidator
            raise forms.ValidationError("City Name is required.")
        return city_name

class CityFilterForm(forms.Form):
    # This form handles the country and state filtering dropdowns on the main page.
    country = forms.ModelChoiceField(
        queryset=Country.objects.all().order_by('country_name'),
        empty_label="Select Country",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        required=False
    )
    state = forms.ModelChoiceField(
        queryset=State.objects.none(), # Initially empty, populated via HTMX
        empty_label="Select State",
        widget=forms.Select(attrs={
            'class': 'box3 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
        }),
        required=False
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Populate the state queryset based on selected country if available
        if 'country' in self.data and self.data['country']:
            try:
                country_id = int(self.data['country'])
                self.fields['state'].queryset = State.objects.filter(country__country_id=country_id).order_by('state_name')
            except (ValueError, TypeError):
                pass
        elif self.initial.get('country'):
             self.fields['state'].queryset = State.objects.filter(country__country_id=self.initial['country'].country_id).order_by('state_name')

```

### 4.3 Views

Task: Implement CRUD operations using CBVs.

**File: `sysadmin/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from django.shortcuts import render, get_object_or_404
from .models import City, State, Country
from .forms import CityForm, CityFilterForm

class CityListView(ListView):
    model = City
    template_name = 'sysadmin/city/list.html'
    context_object_name = 'cities' # This will be the context for the main list, but HTMX loads the table content

    def get_queryset(self):
        # The actual city data for the table is fetched by CityTablePartialView.
        # This queryset is largely for initial setup or if not using HTMX for initial load.
        return City.objects.none() # Return empty initially, HTMX will populate

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize filter form with current GET parameters for stickiness
        filter_form = CityFilterForm(self.request.GET)
        context['filter_form'] = filter_form
        return context

class CityTablePartialView(ListView):
    model = City
    template_name = 'sysadmin/city/_city_table.html'
    context_object_name = 'cities'

    def get_queryset(self):
        state_id = self.request.GET.get('state', '0') # Default to '0' to load all as in ASP.NET
        return City.get_filtered_cities(state_id)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Re-initialize the filter form to pass selected values to the partial
        # for consistent UI state if needed, though not directly used by table.
        state_id = self.request.GET.get('state', '0')
        country_id = self.request.GET.get('country', '0')
        initial_data = {}
        try:
            if country_id != '0':
                initial_data['country'] = Country.objects.get(country_id=int(country_id))
            if state_id != '0':
                initial_data['state'] = State.objects.get(state_id=int(state_id))
        except (ValueError, Country.DoesNotExist, State.DoesNotExist):
            pass # Invalid IDs
        context['filter_form'] = CityFilterForm(initial=initial_data)
        return context

class StateDropdownPartialView(View):
    def get(self, request, country_id):
        states = State.objects.none()
        if country_id != 0: # 0 indicates 'Select Country'
            states = State.objects.filter(country__country_id=country_id).order_by('state_name')
        
        # Create a form instance to render just the state dropdown part
        form = CityFilterForm(initial={'country': country_id})
        form.fields['state'].queryset = states
        
        selected_state_id = request.GET.get('state', '') # Keep previously selected state if available
        
        return render(request, 'sysadmin/city/_state_dropdown.html', {
            'form': form, 
            'selected_state_id': selected_state_id # Pass selected value for HTML template
        })

class CityCreateView(CreateView):
    model = City
    form_class = CityForm
    template_name = 'sysadmin/city/_city_form.html'
    success_url = reverse_lazy('sysadmin:city_list') # Fallback for non-HTMX requests

    def get_initial(self):
        initial = super().get_initial()
        # Pre-populate the state field in the form if a state is selected in the main filter
        state_id_from_filter = self.request.GET.get('state_id')
        if state_id_from_filter and state_id_from_filter != '0':
            try:
                initial['state'] = State.objects.get(state_id=int(state_id_from_filter))
            except (ValueError, State.DoesNotExist):
                pass
        return initial

    def form_valid(self, form):
        # Delegate business logic to model method
        city_name = form.cleaned_data['city_name']
        state_instance = form.cleaned_data['state']
        City.create_new_city(city_name=city_name, state_id=state_instance.state_id)

        messages.success(self.request, 'City added successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content response for HTMX success
                headers={
                    'HX-Trigger': 'refreshCityList' # Custom event to refresh the list table
                }
            )
        return super().form_valid(form) # For non-HTMX (full page reload)

    def form_invalid(self, form):
        # For HTMX, render the form again with errors
        return render(self.request, self.template_name, {'form': form})

class CityUpdateView(UpdateView):
    model = City
    form_class = CityForm
    template_name = 'sysadmin/city/_city_form.html'
    context_object_name = 'city' # For displaying current object in template
    success_url = reverse_lazy('sysadmin:city_list')

    def form_valid(self, form):
        # Delegate business logic to model method
        city_instance = self.get_object()
        city_name = form.cleaned_data['city_name']
        state_instance = form.cleaned_data['state']
        city_instance.update_existing_city(city_name=city_name, state_id=state_instance.state_id)

        messages.success(self.request, 'City updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshCityList'
                }
            )
        return super().form_valid(form)

    def form_invalid(self, form):
        # For HTMX, render the form again with errors
        return render(self.request, self.template_name, {'form': form})

class CityDeleteView(DeleteView):
    model = City
    template_name = 'sysadmin/city/_city_confirm_delete.html'
    context_object_name = 'city'
    success_url = reverse_lazy('sysadmin:city_list')

    def delete(self, request, *args, **kwargs):
        # Delegate business logic to model method
        city_instance = self.get_object()
        city_instance.delete_existing_city()

        messages.success(self.request, 'City deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204, # No Content for HTMX success
                headers={
                    'HX-Trigger': 'refreshCityList'
                }
            )
        return super().delete(request, *args, **kwargs)

```

### 4.4 Templates

Task: Create templates for each view.

**File: `sysadmin/city/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">City Management</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-200"
            hx-get="{% url 'sysadmin:city_add' %}{% if request.GET.state %}{% if request.GET.state != '0' %}?state_id={{ request.GET.state }}{% endif %}{% endif %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New City
        </button>
    </div>
    
    {# Country and State filter dropdowns #}
    <div class="bg-white p-6 rounded-lg shadow-lg mb-6">
        <form class="flex flex-col md:flex-row gap-4 items-center">
            <div class="flex-grow w-full md:w-auto">
                <label for="{{ filter_form.country.id_for_label }}" class="block text-sm font-medium text-gray-700">Country:</label>
                {{ filter_form.country | as_crispy_field }} {# Using crispy forms to render the field #}
                <select name="country" id="{{ filter_form.country.id_for_label }}"
                        class="{{ filter_form.country.widget.attrs.class }}"
                        hx-get="{% url 'sysadmin:states_by_country' 0 %}" {# Initial placeholder URL #}
                        hx-target="#state-dropdown-container"
                        hx-trigger="change, load from:body"
                        hx-swap="outerHTML">
                    <option value="0">Select Country</option>
                    {% for country in filter_form.country.field.queryset %}
                    <option value="{{ country.pk }}" {% if filter_form.country.value == country.pk|stringformat:"s" %}selected{% endif %}>{{ country.country_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div id="state-dropdown-container" class="flex-grow w-full md:w-auto">
                {# State dropdown will be loaded here via HTMX #}
                {% include 'sysadmin/city/_state_dropdown.html' with form=filter_form selected_state_id=request.GET.state %}
            </div>
            {# No explicit submit button for filter, changes trigger HTMX directly #}
        </form>
    </div>

    <div id="cityTable-container"
         hx-trigger="load, refreshCityList from:body"
         hx-get="{% url 'sysadmin:city_table' %}{% if request.GET.country %}?country={{ request.GET.country }}{% endif %}{% if request.GET.state %}{% if request.GET.state != '0' %}&state={{ request.GET.state }}{% endif %}{% endif %}"
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8 bg-white rounded-lg shadow-lg">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading cities...</p>
        </div>
    </div>
    
    <!-- Modal for forms (Add/Edit/Delete) -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden z-50"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4 sm:mx-0">
            <!-- Content will be loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'cityTable-container') {
            // Re-initialize DataTables after HTMX swaps in new table content
            const table = $('#cityTable');
            if (table.length && !$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 17, // From ASP.NET GridView PageSize
                    "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": [0, -1] } // Disable sorting for SN and Actions
                    ]
                });
            }
        }
        // Handle messages after any HTMX request
        if (evt.detail.xhr.getResponseHeader('HX-Trigger')) {
            const hxTrigger = JSON.parse(evt.detail.xhr.getResponseHeader('HX-Trigger'));
            if (hxTrigger.refreshCityList) {
                // Remove modal active class after successful form submission
                document.getElementById('modal').classList.remove('is-active');
            }
        }
    });

    // Initial DataTables load (redundant if using hx-trigger="load" on container, but good for robustness)
    $(document).ready(function() {
        const table = $('#cityTable');
        if (table.length && !$.fn.DataTable.isDataTable(table)) {
            table.DataTable({
                "pageLength": 17,
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "columnDefs": [
                    { "orderable": false, "targets": [0, -1] }
                ]
            });
        }
    });
</script>
{% endblock %}
```

**File: `sysadmin/city/_state_dropdown.html`**

```html
{# This template renders only the state dropdown, to be swapped by HTMX #}
<label for="{{ form.state.id_for_label }}" class="block text-sm font-medium text-gray-700">State:</label>
<select name="state" id="{{ form.state.id_for_label }}"
        class="{{ form.state.widget.attrs.class }}"
        hx-get="{% url 'sysadmin:city_table' %}"
        hx-target="#cityTable-container"
        hx-trigger="change"
        hx-swap="innerHTML">
    <option value="0">Select State</option>
    {% for state_obj in form.state.field.queryset %}
    <option value="{{ state_obj.pk }}" {% if selected_state_id and selected_state_id|stringformat:"s" == state_obj.pk|stringformat:"s" %}selected{% endif %}>{{ state_obj.state_name }}</option>
    {% endfor %}
</select>
```

**File: `sysadmin/city/_city_table.html`**

```html
{# This partial template renders the DataTables content for cities #}
<div class="overflow-x-auto bg-white rounded-lg shadow-lg">
    <table id="cityTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">City Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
                <th class="py-3 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if cities %}
                {% for city in cities %}
                <tr>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900 text-right">{{ forloop.counter }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ city.city_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-900">{{ city.state.state_name }}</td>
                    <td class="py-3 px-4 whitespace-nowrap text-center text-sm font-medium">
                        <button 
                            class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-200 mr-2"
                            hx-get="{% url 'sysadmin:city_edit' city.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Edit
                        </button>
                        <button 
                            class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-200"
                            hx-get="{% url 'sysadmin:city_delete' city.pk %}"
                            hx-target="#modalContent"
                            hx-trigger="click"
                            _="on click add .is-active to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="4" class="py-6 px-4 text-center text-sm text-gray-500">
                    No cities found. 
                    <button 
                        class="text-blue-600 hover:text-blue-800 font-bold"
                        hx-get="{% url 'sysadmin:city_add' %}{% if filter_form.state.value %}{% if filter_form.state.value != '0' %}?state_id={{ filter_form.state.value }}{% endif %}{% endif %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Add New City
                    </button>
                </td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization for HTMX-loaded content
    $(document).ready(function() {
        const table = $('#cityTable');
        if (table.length && !$.fn.DataTable.isDataTable(table)) {
            table.DataTable({
                "pageLength": 17, // Match ASP.NET PageSize
                "lengthMenu": [[10, 17, 25, 50, -1], [10, 17, 25, 50, "All"]],
                "destroy": true, // Allow re-initialization
                "columnDefs": [
                    { "orderable": false, "targets": [0, -1] } // Disable sorting for SN and Actions
                ]
            });
        }
    });
</script>
```

**File: `sysadmin/city/_city_form.html`**

```html
{# This partial template renders the form for adding or editing a city #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} City</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-indicator="#loading-spinner">
        {% csrf_token %}
        
        <div class="space-y-4">
            {# Iterate through form fields to render them #}
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                    {{ field.label }}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <p class="text-red-500 text-xs mt-1">{{ field.errors|join:", " }}</p>
                {% endif %}
            </div>
            {% endfor %}
            {# Render non-field errors #}
            {% if form.non_field_errors %}
            <div class="text-red-500 text-sm mb-4">
                {{ form.non_field_errors }}
            </div>
            {% endif %}
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-200"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-200">
                Save City
            </button>
            <span id="loading-spinner" class="htmx-indicator ml-2">
                <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
            </span>
        </div>
    </form>
</div>
```

**File: `sysadmin/city/_city_confirm_delete.html`**

```html
{# This partial template renders the confirmation dialog for deleting a city #}
<div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-4">Confirm Delete</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the city "{{ city.city_name }}"?</p>
    
    <div class="mt-6 flex items-center justify-end space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition duration-200"
            _="on click remove .is-active from #modal">
            Cancel
        </button>
        <button 
            hx-post="{% url 'sysadmin:city_delete' city.pk %}" 
            hx-swap="none"
            hx-indicator="#loading-spinner-delete"
            class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded shadow-md transition duration-200">
            Delete City
        </button>
        <span id="loading-spinner-delete" class="htmx-indicator ml-2">
            <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-red-500"></div>
        </span>
    </div>
</div>
```

### 4.5 URLs

Task: Define URL patterns for the views.

**File: `sysadmin/urls.py`** (to be included in your project's main `urls.py`)

```python
from django.urls import path
from .views import (
    CityListView, 
    CityTablePartialView, 
    StateDropdownPartialView,
    CityCreateView, 
    CityUpdateView, 
    CityDeleteView
)

app_name = 'sysadmin' # Namespace for the app

urlpatterns = [
    # Main city list page
    path('city/', CityListView.as_view(), name='city_list'),
    
    # HTMX endpoints for table and dropdowns
    path('city/table/', CityTablePartialView.as_view(), name='city_table'),
    path('country/<int:country_id>/states/', StateDropdownPartialView.as_view(), name='states_by_country'),
    
    # HTMX endpoints for CRUD operations (forms loaded into modals)
    path('city/add/', CityCreateView.as_view(), name='city_add'),
    path('city/edit/<int:pk>/', CityUpdateView.as_view(), name='city_edit'),
    path('city/delete/<int:pk>/', CityDeleteView.as_view(), name='city_delete'),
]

```

### 4.6 Tests

Task: Write tests for the model and views.

**File: `sysadmin/tests.py`**

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from django.contrib.messages import get_messages
from unittest.mock import patch # For mocking database interactions if needed, though for managed=False, it's direct

from .models import Country, State, City

class SysAdminModelsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data directly for managed=False models
        # Ensure PKs are unique and match the expected integer type
        cls.country1 = Country.objects.create(country_id=1, country_name='Testland')
        cls.country2 = Country.objects.create(country_id=2, country_name='Otherland')
        
        cls.state1 = State.objects.create(state_id=101, state_name='State Alpha', country=cls.country1)
        cls.state2 = State.objects.create(state_id=102, state_name='State Beta', country=cls.country1)
        cls.state3 = State.objects.create(state_id=103, state_name='State Gamma', country=cls.country2)

        cls.city1 = City.objects.create(city_id=1001, city_name='Cityville', state=cls.state1)
        cls.city2 = City.objects.create(city_id=1002, city_name='Townsville', state=cls.state1)
        cls.city3 = City.objects.create(city_id=1003, city_name='Villagetown', state=cls.state2)
  
    def test_country_creation(self):
        self.assertEqual(self.country1.country_name, 'Testland')
        self.assertEqual(Country.objects.count(), 2)

    def test_state_creation(self):
        self.assertEqual(self.state1.state_name, 'State Alpha')
        self.assertEqual(self.state1.country, self.country1)
        self.assertEqual(State.objects.count(), 3)

    def test_city_creation(self):
        self.assertEqual(self.city1.city_name, 'Cityville')
        self.assertEqual(self.city1.state, self.state1)
        self.assertEqual(City.objects.count(), 3)
        
    def test_city_get_filtered_cities(self):
        # Test getting all cities
        cities_all = City.get_filtered_cities(state_id='0')
        self.assertEqual(cities_all.count(), 3)
        cities_all = City.get_filtered_cities(state_id=None)
        self.assertEqual(cities_all.count(), 3)

        # Test getting cities by specific state
        cities_state1 = City.get_filtered_cities(state_id=self.state1.state_id)
        self.assertEqual(cities_state1.count(), 2)
        self.assertIn(self.city1, cities_state1)
        self.assertIn(self.city2, cities_state1)
        self.assertNotIn(self.city3, cities_state1)

        # Test with invalid state_id
        cities_invalid_state = City.get_filtered_cities(state_id='invalid')
        self.assertEqual(cities_invalid_state.count(), 3) # Should fall back to all

    def test_city_create_new_city(self):
        new_city = City.create_new_city(city_name='Newtown', state_id=self.state2.state_id)
        self.assertIsInstance(new_city, City)
        self.assertEqual(new_city.city_name, 'Newtown')
        self.assertEqual(new_city.state, self.state2)
        self.assertEqual(City.objects.count(), 4)

    def test_city_update_existing_city(self):
        updated_city = self.city1.update_existing_city(city_name='Updated Cityville', state_id=self.state2.state_id)
        self.assertEqual(updated_city.city_name, 'Updated Cityville')
        self.assertEqual(updated_city.state, self.state2)
        self.city1.refresh_from_db() # Reload to ensure DB state matches
        self.assertEqual(self.city1.city_name, 'Updated Cityville')
        self.assertEqual(self.city1.state, self.state2)

    def test_city_delete_existing_city(self):
        city_count = City.objects.count()
        self.city1.delete_existing_city()
        self.assertEqual(City.objects.count(), city_count - 1)
        self.assertFalse(City.objects.filter(city_id=self.city1.city_id).exists())


class SysAdminViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests
        cls.country1 = Country.objects.create(country_id=1, country_name='Testland')
        cls.state1 = State.objects.create(state_id=101, state_name='State Alpha', country=cls.country1)
        cls.state2 = State.objects.create(state_id=102, state_name='State Beta', country=cls.country1)
        cls.city1 = City.objects.create(city_id=1001, city_name='Cityville', state=cls.state1)
        cls.city2 = City.objects.create(city_id=1002, city_name='Townsville', state=cls.state1)
    
    def setUp(self):
        # Set up client for each test method
        self.client = Client()
    
    def test_city_list_view(self):
        response = self.client.get(reverse('sysadmin:city_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/list.html')
        self.assertIn('filter_form', response.context)
        # Verify initial state of the table container (should be empty, loaded via HTMX)
        self.assertContains(response, 'id="cityTable-container"')
        self.assertContains(response, 'Loading cities...') # HTMX loading message

    def test_city_table_partial_view_no_filter(self):
        response = self.client.get(reverse('sysadmin:city_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/_city_table.html')
        self.assertIn('cities', response.context)
        self.assertEqual(response.context['cities'].count(), City.objects.count())
        self.assertContains(response, 'Cityville')
        self.assertContains(response, 'Townsville')

    def test_city_table_partial_view_with_state_filter(self):
        response = self.client.get(reverse('sysadmin:city_table'), {'state': self.state1.state_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/_city_table.html')
        self.assertEqual(response.context['cities'].count(), 2)
        self.assertContains(response, 'Cityville')
        self.assertContains(response, 'Townsville')
        
        # Test with a state that has no cities
        state_no_cities = State.objects.create(state_id=104, state_name='Empty State', country=self.country1)
        response = self.client.get(reverse('sysadmin:city_table'), {'state': state_no_cities.state_id})
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'No cities found.')
        self.assertEqual(response.context['cities'].count(), 0)

    def test_state_dropdown_partial_view(self):
        response = self.client.get(reverse('sysadmin:states_by_country', args=[self.country1.country_id]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/_state_dropdown.html')
        self.assertContains(response, f'<option value="{self.state1.state_id}">State Alpha</option>')
        self.assertContains(response, f'<option value="{self.state2.state_id}">State Beta</option>')
        self.assertNotContains(response, 'State Gamma') # Assuming State Gamma is in another country

        response_no_country = self.client.get(reverse('sysadmin:states_by_country', args=[0]))
        self.assertEqual(response_no_country.status_code, 200)
        self.assertNotContains(response_no_country, 'State Alpha') # Should only contain "Select State"

    def test_city_create_view_get(self):
        response = self.client.get(reverse('sysadmin:city_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/_city_form.html')
        self.assertIn('form', response.context)
        self.assertContains(response, 'Add City') # Check for form title

    def test_city_create_view_post_success(self):
        data = {
            'city_name': 'New Atlantis',
            'state': self.state1.state_id, # Link to an existing state
        }
        city_count_before = City.objects.count()
        response = self.client.post(reverse('sysadmin:city_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX success code
        self.assertEqual(City.objects.count(), city_count_before + 1)
        self.assertTrue(City.objects.filter(city_name='New Atlantis', state=self.state1).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCityList', response.headers['HX-Trigger'])

    def test_city_create_view_post_invalid(self):
        data = {
            'city_name': '', # Required field missing
            'state': self.state1.state_id,
        }
        city_count_before = City.objects.count()
        response = self.client.post(reverse('sysadmin:city_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # HTMX returns form with errors
        self.assertTemplateUsed(response, 'sysadmin/city/_city_form.html')
        self.assertFormError(response.context['form'], 'city_name', 'City Name is required.')
        self.assertEqual(City.objects.count(), city_count_before) # No new city created

    def test_city_update_view_get(self):
        response = self.client.get(reverse('sysadmin:city_edit', args=[self.city1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/_city_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.city1)
        self.assertContains(response, 'Edit City') # Check for form title

    def test_city_update_view_post_success(self):
        data = {
            'city_name': 'Renamed Cityville',
            'state': self.state2.state_id, # Change state
        }
        response = self.client.post(reverse('sysadmin:city_edit', args=[self.city1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.city1.refresh_from_db()
        self.assertEqual(self.city1.city_name, 'Renamed Cityville')
        self.assertEqual(self.city1.state, self.state2)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCityList', response.headers['HX-Trigger'])

    def test_city_update_view_post_invalid(self):
        data = {
            'city_name': '', # Required field missing
            'state': self.state1.state_id,
        }
        response = self.client.post(reverse('sysadmin:city_edit', args=[self.city1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/_city_form.html')
        self.assertFormError(response.context['form'], 'city_name', 'City Name is required.')
        self.city1.refresh_from_db()
        self.assertNotEqual(self.city1.city_name, '') # Ensure it wasn't updated

    def test_city_delete_view_get(self):
        response = self.client.get(reverse('sysadmin:city_delete', args=[self.city1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/city/_city_confirm_delete.html')
        self.assertIn('city', response.context)
        self.assertEqual(response.context['city'], self.city1)
        self.assertContains(response, f'Are you sure you want to delete the city "{self.city1.city_name}"?')

    def test_city_delete_view_post_success(self):
        city_count_before = City.objects.count()
        response = self.client.post(reverse('sysadmin:city_delete', args=[self.city1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertEqual(City.objects.count(), city_count_before - 1)
        self.assertFalse(City.objects.filter(city_id=self.city1.city_id).exists())
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshCityList', response.headers['HX-Trigger'])

```