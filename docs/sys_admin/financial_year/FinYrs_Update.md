## ASP.NET to Django Conversion Script:

This modernization plan outlines the strategic transition from your legacy ASP.NET application to a modern Django-based solution. Our focus is on leveraging AI-assisted automation, minimizing manual coding, and ensuring a robust, scalable, and maintainable system. The approach emphasizes Django's "Fat Model, Thin View" paradigm, combined with the power of HTMX and Alpine.js for dynamic, reactive user interfaces without complex JavaScript.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists and is extended.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination (where applicable for list views).
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:**
The ASP.NET code interacts primarily with `tblFinancial_master` and implicitly with a company table (e.g., `tblCompany_master`). The `FinYrs_Update.aspx` page's purpose is to edit existing financial year records.

**Identified Tables and Columns:**

-   **Table Name:** `tblFinancial_master`
    -   `FinYearId`: Integer (Primary Key)
    -   `CompId`: Integer (Foreign Key to `tblCompany_master`)
    -   `FinYearFrom`: Date
    -   `FinYearTo`: Date
    -   `SysDate`: Date (System update date)
    -   `SysTime`: Time (System update time)
    -   `SessionId`: String (Stores username of the modifying user)
    -   `FinYear`: String (Concatenated financial year, e.g., "2023-2024")

-   **Table Name:** `tblCompany_master` (Inferred from `DropDownUpFYCName` and its population logic)
    -   `CompId`: Integer (Primary Key)
    -   `CName`: String (Company Name)
    *(Note: A minimal `Company` model will be created for demonstration, assuming it maps to `tblCompany_master`.)*

### Step 2: Identify Backend Functionality

**Analysis:**
The ASP.NET code-behind handles dynamic data loading and updates.

-   **Read Operations:**
    -   Populating the Company dropdown on page load.
    -   Loading Financial Years into a ListBox based on selected Company.
    -   Fetching `FinYearFrom` and `FinYearTo` dates based on selected Financial Year.
-   **Update Operation:**
    -   Modifying `FinYearFrom`, `FinYearTo`, `SysDate`, `SysTime`, `SessionId`, and `FinYear` for a selected financial year record.
-   **Validation:**
    -   Required fields: Company, Financial Year, Date From, Date To.
    -   Date format validation (`dd-MM-yyyy`).
    -   Implicit date range validation (Date From must be less than or equal to Date To, though not explicitly coded in ASP.NET, it's good practice).
-   **System Fields:** `SysDate`, `SysTime`, `SessionId`, `FinYear` are automatically generated/updated upon saving.

### Step 3: Infer UI Components

**Analysis:**
The ASP.NET page uses standard Web Forms controls for data entry and selection.

-   **`DropDownUpFYCName` (Company Selection):** Will be converted to a Django `ModelChoiceField` for `Company`, rendered as a `<select>` element. HTMX will be used for dynamic filtering.
-   **`ListBoxUpFinYear` (Financial Year Selection):** Will be converted to a Django `ModelChoiceField` for `FinancialYear`, rendered as a `<select>` element. HTMX will update its options and trigger date field population.
-   **`txtFDate`, `txtTDate` (Date Input):** Will be converted to Django `DateField`s rendered as text inputs. Alpine.js with Flatpickr will provide the calendar functionality.
-   **`Update` Button:** A standard HTML `<button type="submit">` will be used with HTMX for form submission.
-   **`Label1` (Message Display):** Django's built-in messages framework will handle success/error messages.
-   **`confirmationUpdate()` (Client-side confirmation):** This can be handled directly with HTMX's `hx-confirm` attribute or a simple Alpine.js modal.

---

### Step 4: Generate Django Code

We will create a Django application named `financial_year`.

#### 4.1 Models (`financial_year/models.py`)

This file defines the Django models that map directly to your existing database tables. `managed = False` tells Django not to create or manage these tables, allowing them to remain as-is in your database.

```python
from django.db import models
from django.utils import timezone
from django.conf import settings # For AUTH_USER_MODEL if needed, otherwise using username string directly

# Assuming a Company model exists, mapping to tblCompany_master.
# This is required because FinancialYear has a foreign key to it.
class Company(models.Model):
    # 'CompId' is the primary key in tblCompany_master
    company_id = models.IntegerField(db_column='CompId', primary_key=True)
    # Assuming 'CName' is the column for company name
    company_name = models.CharField(db_column='CName', max_length=255)

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.company_name

class FinancialYear(models.Model):
    # 'FinYearId' is the primary key in tblFinancial_master
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    # Foreign key to Company, mapping to 'CompId'
    company = models.ForeignKey(Company, on_delete=models.PROTECT, db_column='CompId', related_name='financial_years')
    date_from = models.DateField(db_column='FinYearFrom')
    date_to = models.DateField(db_column='FinYearTo')

    # auto_now=True will update SysDate and SysTime automatically on each save, matching ASP.NET's DateTime.Now
    system_date = models.DateField(db_column='SysDate', auto_now=True)
    system_time = models.TimeField(db_column='SysTime', auto_now=True)
    # 'SessionId' stores the username of the user who last updated the record
    session_user = models.CharField(db_column='SessionId', max_length=255)
    # 'FinYear' stores the derived financial year string (e.g., "2023-2024")
    financial_year_display = models.CharField(db_column='FinYear', max_length=100)

    class Meta:
        managed = False  # Django will not manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        # This string representation is used in Django admin and ModelChoiceField displays
        return f"{self.company.company_name} - {self.financial_year_display}"

    # Business logic method for the Fat Model
    def update_derived_fields(self, user_username):
        """
        Updates `session_user` and calculates `financial_year_display`.
        `system_date` and `system_time` are handled by `auto_now=True` in the model fields.
        This method should be called from the form's save method or directly from the view.
        """
        self.session_user = user_username

        if self.date_from and self.date_to:
            from_year = self.date_from.year
            to_year = self.date_to.year
            self.financial_year_display = f"{from_year}-{to_year}"
        else:
            # Handle cases where dates might be invalid or not set
            self.financial_year_display = "Invalid Date Range"

```

#### 4.2 Forms (`financial_year/forms.py`)

This form handles the user input for the financial year dates and includes validation logic. We are defining the fields that are directly editable by the user (`date_from`, `date_to`). The selection fields (`company`, `financial_year`) will be handled separately by the views and HTMX.

```python
from django import forms
from .models import FinancialYear, Company
from django.core.exceptions import ValidationError
import datetime # For date validation

class FinancialYearUpdateForm(forms.ModelForm):
    class Meta:
        model = FinancialYear
        fields = ['date_from', 'date_to'] # Only these fields are editable by the user for update

        widgets = {
            'date_from': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'DD-MM-YYYY',
                # Alpine.js for Flatpickr integration
                'x-data': '',
                'x-init': 'flatpickr($el, {dateFormat: "d-m-Y", allowInput: true, altFormat: "d-m-Y", altInput: true})',
                'autocomplete': 'off', # Prevent browser autocomplete
                'readonly': 'readonly' # Match ASP.NET readonly attribute
            }),
            'date_to': forms.TextInput(attrs={
                'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
                'placeholder': 'DD-MM-YYYY',
                # Alpine.js for Flatpickr integration
                'x-data': '',
                'x-init': 'flatpickr($el, {dateFormat: "d-m-Y", allowInput: true, altFormat: "d-m-Y", altInput: true})',
                'autocomplete': 'off', # Prevent browser autocomplete
                'readonly': 'readonly' # Match ASP.NET readonly attribute
            }),
        }

    def clean_date_from(self):
        date_str = self.cleaned_data.get('date_from')
        # ASP.NET had a RegexValidator for date format. Django DateField handles this automatically
        # but if we used TextInput, we'd need custom parsing like this:
        # try:
        #     # Attempt to parse date in DD-MM-YYYY format
        #     return datetime.datetime.strptime(date_str, '%d-%m-%Y').date()
        # except (ValueError, TypeError):
        #     raise forms.ValidationError("Please enter a valid date in DD-MM-YYYY format.")
        return date_str # DateField already converts to date object

    def clean_date_to(self):
        date_str = self.cleaned_data.get('date_to')
        # Similar logic as clean_date_from if TextInput is used
        return date_str

    def clean(self):
        cleaned_data = super().clean()
        date_from = cleaned_data.get('date_from')
        date_to = cleaned_data.get('date_to')

        # Custom validation: Date To must be after or equal to Date From
        if date_from and date_to:
            if date_from > date_to:
                raise ValidationError(
                    "Date To must be on or after Date From.",
                    code='invalid_date_range'
                )
        return cleaned_data

    def save(self, commit=True, user=None):
        """
        Custom save method to set the session_user and update derived fields
        before saving the model instance.
        """
        instance = super().save(commit=False)
        if user:
            instance.update_derived_fields(user.username) # Call the fat model method
        if commit:
            instance.save()
        return instance

```

#### 4.3 Views (`financial_year/views.py`)

This file contains the logic for handling requests, rendering templates, and interacting with models and forms. We'll use a mix of `TemplateView` for the main page and `View` or `ListView` derivatives for HTMX partials.

```python
from django.views.generic import TemplateView, View
from django.views.generic.edit import UpdateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.shortcuts import get_object_or_404, render
from django.contrib.auth.mixins import LoginRequiredMixin # Ensure user is logged in
from .models import Company, FinancialYear
from .forms import FinancialYearUpdateForm

class FinancialYearUpdatePage(LoginRequiredMixin, TemplateView):
    """
    Renders the main Financial Year Update page. This view provides the initial
    structure with company dropdown, financial year listbox, and date fields.
    It relies on HTMX to dynamically populate and update content.
    """
    template_name = 'financial_year/financialyear_update_page.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Populate the company dropdown
        context['companies'] = Company.objects.all().order_by('company_name')
        # Initialize an empty form for the date fields
        context['form'] = FinancialYearUpdateForm()
        # Initial selected_company_id and selected_fin_year_id to render
        # the initial state correctly. They will be None until a selection is made.
        context['selected_company_id'] = None
        context['selected_fin_year_id'] = None
        return context

class GetFinancialYearsByCompany(LoginRequiredMixin, View):
    """
    HTMX endpoint to retrieve and render a list of financial years
    based on the selected company ID.
    Returns an HTML snippet of <option> tags for the financial year select box.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.GET.get('company_id')
        if not company_id:
            return HttpResponse('<option value="">Select Company First</option>')

        financial_years = FinancialYear.objects.filter(company_id=company_id).order_by('date_from')
        return render(request, 'financial_year/_financialyear_select_options.html', {
            'financial_years': financial_years
        })

class GetFinancialYearDetails(LoginRequiredMixin, View):
    """
    HTMX endpoint to retrieve and render the date_from and date_to fields
    for a selected financial year.
    Returns an HTML snippet of the date input fields, pre-filled with data.
    """
    def get(self, request, *args, **kwargs):
        fin_year_id = request.GET.get('fin_year_id')
        if not fin_year_id:
            # If no financial year selected, return empty form fields
            form = FinancialYearUpdateForm()
            return render(request, 'financial_year/_date_fields_form_part.html', {'form': form})

        financial_year = get_object_or_404(FinancialYear, fin_year_id=fin_year_id)
        form = FinancialYearUpdateForm(instance=financial_year)
        return render(request, 'financial_year/_date_fields_form_part.html', {'form': form})

class FinancialYearUpdateView(LoginRequiredMixin, UpdateView):
    """
    Handles the actual update operation for a FinancialYear instance.
    This view will be targeted by the HTMX form submission.
    It performs validation and updates the record.
    """
    model = FinancialYear
    form_class = FinancialYearUpdateForm
    template_name = 'financial_year/_date_fields_form_part.html' # Render the form partial on success/error

    # The success_url is handled by HTMX triggers, so we return a 204 No Content response
    # with HX-Trigger headers to refresh the necessary parts of the page.
    success_url = reverse_lazy('financial_year_update_page') # Fallback, not typically used with HTMX

    def get_object(self, queryset=None):
        """
        Retrieves the FinancialYear instance to be updated based on the fin_year_id from POST data.
        """
        fin_year_id = self.request.POST.get('fin_year_id')
        if not fin_year_id:
            # If fin_year_id is not present, it's an invalid request for UpdateView
            # In a real scenario, this might need more robust error handling
            # or a different view for initial form display.
            # For HTMX, we might simply return an error response.
            return None # Allow form processing to fail
        return get_object_or_404(FinancialYear, fin_year_id=fin_year_id)

    def form_valid(self, form):
        """
        Handles successful form submission.
        Updates the model instance and sends HTMX triggers.
        """
        # Call custom save method to set derived fields (session_user, financial_year_display)
        self.object = form.save(user=self.request.user)
        messages.success(self.request, 'Financial year updated successfully.')

        # HTMX response: 204 No Content to prevent full page reload,
        # with HX-Trigger to refresh relevant parts of the page.
        # This will also re-render the _date_fields_form_part.html with the updated values.
        return HttpResponse(
            status=200, # Use 200 OK to re-render the form with updated values, or 204 No Content if no re-render needed.
            headers={
                'HX-Trigger': 'refreshFinancialYearDetails, updateFinancialYearList'
            },
            content=render(self.request, self.template_name, {'form': form, 'object': self.object}).content
        )

    def form_invalid(self, form):
        """
        Handles invalid form submission.
        Re-renders the form with validation errors.
        """
        # HTMX response: Re-render the form partial with errors.
        messages.error(self.request, 'Please correct the errors below.')
        return render(self.request, self.template_name, {'form': form}, status=400) # Use 400 Bad Request for errors

```

#### 4.4 Templates (`financial_year/templates/financial_year/`)

We'll define three partial templates and one main page template. Remember, `core/base.html` is assumed to exist and is not included here.

**`financial_year/financialyear_update_page.html` (Main Page)**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Financial Year - Edit</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-lg max-w-xl mx-auto">
        <form id="financialYearUpdateForm" hx-post="{% url 'financial_year_update' %}" hx-swap="outerHTML" hx-target="#dateFieldsContainer"
              hx-trigger="submit from #financialYearUpdateForm" _="on hx-success then call Alpine.store('notifications').add('success', 'Financial year updated successfully.')
                                                                 on hx-error then call Alpine.store('notifications').add('error', 'Update failed. Please check errors.')">
            {% csrf_token %}
            
            <!-- Hidden field to carry the selected financial year ID for update -->
            <input type="hidden" name="fin_year_id" id="selectedFinYearId" value="{{ selected_fin_year_id }}">

            <div class="space-y-4">
                <!-- Company Dropdown -->
                <div>
                    <label for="company_select" class="block text-sm font-medium text-gray-700">Company</label>
                    <select id="company_select" name="company"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            hx-get="{% url 'get_financial_years_by_company' %}"
                            hx-target="#financial_year_select"
                            hx-swap="innerHTML"
                            hx-indicator="#loadingIndicator"
                            hx-vals='{"company_id": this.value}'
                            hx-trigger="change">
                        <option value="">Select</option>
                        {% for company in companies %}
                            <option value="{{ company.company_id }}" {% if selected_company_id == company.company_id %}selected{% endif %}>{{ company.company_name }}</option>
                        {% endfor %}
                    </select>
                    <div id="loadingIndicator" class="htmx-indicator mt-2">
                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                        <span class="ml-2 text-sm text-gray-500">Loading financial years...</span>
                    </div>
                </div>

                <!-- Financial Year Listbox (populated via HTMX) -->
                <div>
                    <label for="financial_year_select" class="block text-sm font-medium text-gray-700">Financial Year</label>
                    <select id="financial_year_select" name="fin_year" size="5"
                            class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            hx-get="{% url 'get_financial_year_details' %}"
                            hx-target="#dateFieldsContainer"
                            hx-swap="outerHTML"
                            hx-indicator="#loadingDateFields"
                            hx-vals='js:{fin_year_id: this.value}'
                            hx-on--htmx-after-request="document.getElementById('selectedFinYearId').value = event.detail.elt.value;"
                            hx-trigger="change, updateFinancialYearList from:body"> {# Trigger on change and custom event #}
                        <option value="">Select Financial Year</option>
                        <!-- Options will be loaded here by HTMX -->
                    </select>
                    <div id="loadingDateFields" class="htmx-indicator mt-2">
                        <div class="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                        <span class="ml-2 text-sm text-gray-500">Loading date details...</span>
                    </div>
                </div>
                
                <!-- Date Fields Container (populated via HTMX) -->
                <div id="dateFieldsContainer">
                    <!-- Initial form fields, will be replaced by HTMX -->
                    {% include 'financial_year/_date_fields_form_part.html' with form=form %}
                </div>
            </div>
            
            <div class="mt-6 flex items-center justify-center space-x-4">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                        hx-confirm="Are you sure you want to update this financial year?">
                    Update
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.store('notifications', {
            items: [],
            add(type, message, duration = 3000) {
                const id = Date.now();
                this.items.push({ id, type, message });
                setTimeout(() => this.remove(id), duration);
            },
            remove(id) {
                this.items = this.items.filter(item => item.id !== id);
            }
        });
    });

    // Manually trigger initial load for financial years if a company is pre-selected
    // or if you want to load the first company's financial years
    document.addEventListener('DOMContentLoaded', function() {
        const companySelect = document.getElementById('company_select');
        if (companySelect && companySelect.value !== '') {
            htmx.trigger(companySelect, 'change');
        }
    });
</script>
{% endblock %}

```

**`financial_year/_financialyear_select_options.html` (Partial for Financial Year Listbox Options)**

```html
<option value="">Select Financial Year</option>
{% for fy in financial_years %}
    <option value="{{ fy.fin_year_id }}">{{ fy.financial_year_display }}</option>
{% endfor %}
```

**`financial_year/_date_fields_form_part.html` (Partial for Date Fields Form)**

```html
{% comment %}
This template fragment contains the date input fields and their validation messages.
It will be dynamically swapped by HTMX.
It's designed to be used both for initial rendering and for HTMX responses.
{% endcomment %}

{% if messages %}
    <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)"
         class="mb-4 p-3 rounded-md text-sm {% if 'success' in message.tags %}bg-green-100 text-green-700{% elif 'error' in message.tags %}bg-red-100 text-red-700{% endif %}"
         role="alert">
        {% for message in messages %}
            <p{% if message.tags %} class="{{ message.tags }}"{% endif %}>{{ message }}</p>
        {% endfor %}
    </div>
{% endif %}

<div class="mb-4">
    <label for="{{ form.date_from.id_for_label }}" class="block text-sm font-medium text-gray-700">Date From</label>
    {{ form.date_from }}
    {% if form.date_from.errors %}
        <p class="text-red-500 text-xs mt-1">{{ form.date_from.errors|join:", " }}</p>
    {% endif %}
</div>

<div class="mb-4">
    <label for="{{ form.date_to.id_for_label }}" class="block text-sm font-medium text-gray-700">Date To</label>
    {{ form.date_to }}
    {% if form.date_to.errors %}
        <p class="text-red-500 text-xs mt-1">{{ form.date_to.errors|join:", " }}</p>
    {% endif %}
</div>

{% if form.non_field_errors %}
    <div class="text-red-500 text-xs mt-1">
        {% for error in form.non_field_errors %}
            <p>{{ error }}</p>
        {% endfor %}
    </div>
{% endif %}

```

#### 4.5 URLs (`financial_year/urls.py`)

This file defines the URL patterns that map to your Django views.

```python
from django.urls import path
from .views import (
    FinancialYearUpdatePage,
    GetFinancialYearsByCompany,
    GetFinancialYearDetails,
    FinancialYearUpdateView,
)

urlpatterns = [
    # Main page for updating financial years
    path('financial-year/update/', FinancialYearUpdatePage.as_view(), name='financial_year_update_page'),

    # HTMX endpoint to get financial years based on selected company
    path('financial-year/get-by-company/', GetFinancialYearsByCompany.as_view(), name='get_financial_years_by_company'),

    # HTMX endpoint to get financial year date details based on selected financial year ID
    path('financial-year/get-details/', GetFinancialYearDetails.as_view(), name='get_financial_year_details'),

    # HTMX endpoint for submitting the update form
    path('financial-year/perform-update/', FinancialYearUpdateView.as_view(), name='financial_year_update'),
]
```
*(Remember to include these URLs in your project's main `urls.py` file, e.g., `path('sysadmin/', include('financial_year.urls'))`)*

#### 4.6 Tests (`financial_year/tests.py`)

Thorough tests are crucial for verifying functionality and preventing regressions. We'll include unit tests for the models and integration tests for the views, ensuring high test coverage.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import date, time
from .models import Company, FinancialYear
from .forms import FinancialYearUpdateForm

User = get_user_model()

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a test user
        cls.user = User.objects.create_user(username='testuser', password='password123')

        # Create a test company (managed=False, so manual PK assignment)
        cls.company1 = Company.objects.create(company_id=1, company_name='Test Company A')
        cls.company2 = Company.objects.create(company_id=2, company_name='Test Company B')

        # Create test financial year data
        cls.fy1 = FinancialYear.objects.create(
            fin_year_id=101,
            company=cls.company1,
            date_from=date(2023, 1, 1),
            date_to=date(2023, 12, 31),
            session_user='initial_user',
            financial_year_display='2023-2023'
        )
        cls.fy2 = FinancialYear.objects.create(
            fin_year_id=102,
            company=cls.company1,
            date_from=date(2024, 1, 1),
            date_to=date(2024, 12, 31),
            session_user='initial_user',
            financial_year_display='2024-2024'
        )
        cls.fy3 = FinancialYear.objects.create(
            fin_year_id=103,
            company=cls.company2,
            date_from=date(2023, 7, 1),
            date_to=date(2024, 6, 30),
            session_user='initial_user',
            financial_year_display='2023-2024'
        )

    def test_financial_year_creation(self):
        # Verify basic object creation and field values
        fy = FinancialYear.objects.get(fin_year_id=101)
        self.assertEqual(fy.company, self.company1)
        self.assertEqual(fy.date_from, date(2023, 1, 1))
        self.assertEqual(fy.date_to, date(2023, 12, 31))
        self.assertEqual(fy.financial_year_display, '2023-2023')
        self.assertIsNotNone(fy.system_date)
        self.assertIsNotNone(fy.system_time)

    def test_str_method(self):
        fy = FinancialYear.objects.get(fin_year_id=101)
        self.assertEqual(str(fy), f"{self.company1.company_name} - 2023-2023")

    def test_update_derived_fields(self):
        fy = FinancialYear.objects.get(fin_year_id=101)
        # Simulate a change in dates
        fy.date_from = date(2022, 7, 1)
        fy.date_to = date(2023, 6, 30)
        
        # Call the fat model method
        fy.update_derived_fields(self.user.username)
        
        # Check if derived fields are updated correctly
        self.assertEqual(fy.session_user, self.user.username)
        self.assertEqual(fy.financial_year_display, '2022-2023')
        
        # Save to trigger auto_now fields and persist changes
        fy.save()
        updated_fy = FinancialYear.objects.get(fin_year_id=101)
        self.assertEqual(updated_fy.session_user, self.user.username)
        self.assertEqual(updated_fy.financial_year_display, '2022-2023')
        self.assertEqual(updated_fy.system_date, timezone.now().date())


class FinancialYearFormTest(TestCase):
    def test_valid_form_data(self):
        form = FinancialYearUpdateForm(data={
            'date_from': '01-01-2023',
            'date_to': '31-12-2023'
        })
        self.assertTrue(form.is_valid())
        self.assertEqual(form.cleaned_data['date_from'], date(2023, 1, 1))
        self.assertEqual(form.cleaned_data['date_to'], date(2023, 12, 31))

    def test_invalid_date_range(self):
        form = FinancialYearUpdateForm(data={
            'date_from': '31-12-2023',
            'date_to': '01-01-2023'
        })
        self.assertFalse(form.is_valid())
        self.assertIn('date_to', form.errors) # Error on date_to field
        self.assertIn('Date To must be on or after Date From.', form.errors['date_to'][0])
        self.assertIn('invalid_date_range', form.errors['__all__'][0].code) # Or non_field_errors if error is there

    def test_missing_required_fields(self):
        form = FinancialYearUpdateForm(data={})
        self.assertFalse(form.is_valid())
        self.assertIn('date_from', form.errors)
        self.assertIn('date_to', form.errors)


class FinancialYearViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.user = User.objects.create_user(username='testuser', password='password123')
        cls.company1 = Company.objects.create(company_id=1, company_name='Test Company A')
        cls.company2 = Company.objects.create(company_id=2, company_name='Test Company B')
        cls.fy_to_update = FinancialYear.objects.create(
            fin_year_id=200,
            company=cls.company1,
            date_from=date(2023, 1, 1),
            date_to=date(2023, 12, 31),
            session_user='creator',
            financial_year_display='2023-2023'
        )
        FinancialYear.objects.create(
            fin_year_id=201,
            company=cls.company1,
            date_from=date(2024, 1, 1),
            date_to=date(2024, 12, 31),
            session_user='creator',
            financial_year_display='2024-2024'
        )
        FinancialYear.objects.create(
            fin_year_id=202,
            company=cls.company2,
            date_from=date(2023, 7, 1),
            date_to=date(2024, 6, 30),
            session_user='creator',
            financial_year_display='2023-2024'
        )

    def setUp(self):
        self.client = Client()
        self.client.login(username='testuser', password='password123')

    def test_financial_year_update_page_get(self):
        response = self.client.get(reverse('financial_year_update_page'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financialyear_update_page.html')
        self.assertIn('companies', response.context)
        self.assertIsInstance(response.context['form'], FinancialYearUpdateForm)

    def test_get_financial_years_by_company_htmx(self):
        # Test valid company_id
        response = self.client.get(reverse('get_financial_years_by_company'), {'company_id': self.company1.company_id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/_financialyear_select_options.html')
        self.assertContains(response, f'<option value="{self.fy_to_update.fin_year_id}">')
        self.assertContains(response, '2023-2023')
        self.assertContains(response, '2024-2024')
        self.assertNotContains(response, '2023-2024') # For company2's FY

        # Test invalid company_id
        response = self.client.get(reverse('get_financial_years_by_company'), {'company_id': 999}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/_financialyear_select_options.html')
        self.assertContains(response, '<option value="">Select Financial Year</option>') # Only default option

        # Test no company_id provided
        response = self.client.get(reverse('get_financial_years_by_company'), {}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '<option value="">Select Company First</option>')


    def test_get_financial_year_details_htmx(self):
        # Test valid fin_year_id
        response = self.client.get(reverse('get_financial_year_details'), {'fin_year_id': self.fy_to_update.fin_year_id}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/_date_fields_form_part.html')
        self.assertContains(response, f'value="{self.fy_to_update.date_from.strftime("%d-%m-%Y")}"')
        self.assertContains(response, f'value="{self.fy_to_update.date_to.strftime("%d-%m-%Y")}"')

        # Test no fin_year_id
        response = self.client.get(reverse('get_financial_year_details'), {}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/_date_fields_form_part.html')
        self.assertContains(response, 'name="date_from"')
        self.assertContains(response, 'name="date_to"')
        self.assertContains(response, 'value=""', count=2) # Should be empty values

        # Test invalid fin_year_id
        response = self.client.get(reverse('get_financial_year_details'), {'fin_year_id': 9999}, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404)


    def test_financial_year_update_post_valid(self):
        initial_date_from = self.fy_to_update.date_from
        initial_date_to = self.fy_to_update.date_to

        new_date_from = date(2023, 7, 1)
        new_date_to = date(2024, 6, 30)
        
        data = {
            'fin_year_id': self.fy_to_update.fin_year_id,
            'date_from': new_date_from.strftime('%d-%m-%Y'),
            'date_to': new_date_to.strftime('%d-%m-%Y'),
        }
        
        response = self.client.post(reverse('financial_year_update'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshFinancialYearDetails', response.headers['HX-Trigger'])
        self.assertIn('updateFinancialYearList', response.headers['HX-Trigger'])

        # Verify the database was updated
        updated_fy = FinancialYear.objects.get(fin_year_id=self.fy_to_update.fin_year_id)
        self.assertEqual(updated_fy.date_from, new_date_from)
        self.assertEqual(updated_fy.date_to, new_date_to)
        self.assertEqual(updated_fy.session_user, self.user.username)
        self.assertEqual(updated_fy.financial_year_display, '2023-2024')
        self.assertEqual(updated_fy.system_date, timezone.now().date())


    def test_financial_year_update_post_invalid_data(self):
        data = {
            'fin_year_id': self.fy_to_update.fin_year_id,
            'date_from': '31-12-2023',
            'date_to': '01-01-2023', # Invalid range
        }
        response = self.client.post(reverse('financial_year_update'), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 400) # Bad Request due to validation error
        self.assertTemplateUsed(response, 'financial_year/_date_fields_form_part.html')
        self.assertContains(response, 'Date To must be on or after Date From.')
        
        # Verify database was NOT updated
        updated_fy = FinancialYear.objects.get(fin_year_id=self.fy_to_update.fin_year_id)
        self.assertEqual(updated_fy.date_from, date(2023, 1, 1)) # Should be original date
        self.assertEqual(updated_fy.date_to, date(2023, 12, 31)) # Should be original date

    def test_financial_year_update_post_missing_id(self):
        data = {
            'date_from': '01-01-2023',
            'date_to': '31-12-2023',
        }
        response = self.client.post(reverse('financial_year_update'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 404) # Not Found because get_object() couldn't find an instance
        
    def test_unauthenticated_access(self):
        self.client.logout()
        response = self.client.get(reverse('financial_year_update_page'))
        self.assertRedirects(response, f'{reverse(settings.LOGIN_URL)}?next={reverse("financial_year_update_page")}')

```

---

### Step 5: HTMX and Alpine.js Integration

**Implementation Details:**

-   **Dynamic Dropdowns/Listboxes:**
    -   The `company_select` dropdown uses `hx-get` to `{% url 'get_financial_years_by_company' %}` with `hx-target="#financial_year_select"` and `hx-swap="innerHTML"`. This dynamically loads the `<option>` tags for the financial year listbox.
    -   The `financial_year_select` listbox then uses `hx-get` to `{% url 'get_financial_year_details' %}` with `hx-target="#dateFieldsContainer"` and `hx-swap="outerHTML"`. This loads the pre-filled date input fields. A hidden input (`selectedFinYearId`) is updated via `hx-on--htmx-after-request` to pass the selected PK to the update form.
-   **Form Submission:**
    -   The `<form>` element uses `hx-post` to `{% url 'financial_year_update' %}`.
    -   `hx-swap="outerHTML"` and `hx-target="#dateFieldsContainer"` will replace the entire date fields container with the response from the `FinancialYearUpdateView`, ensuring validation errors are displayed in place or the updated values are shown.
    -   `hx-trigger="submit from #financialYearUpdateForm"` ensures the form only submits when its own submit button is clicked.
    -   `hx-confirm="Are you sure..."` provides the client-side confirmation directly via HTMX.
-   **Live Updates and Refresh:**
    -   The `FinancialYearUpdateView` on successful update sends `HX-Trigger: refreshFinancialYearDetails, updateFinancialYearList`.
    -   The `financial_year_select` has `hx-trigger="..., updateFinancialYearList from:body"` which allows it to reload its options if, for example, the `financial_year_display` changes after an update.
    -   The `dateFieldsContainer` also reloads on `refreshFinancialYearDetails` (implicitly via its target and swap).
-   **Date Picker:**
    -   Flatpickr library is integrated using Alpine.js `x-init` on the `date_from` and `date_to` text inputs. This provides a modern, interactive date selection interface without traditional JavaScript.
-   **Messages/Notifications:**
    -   Alpine.js store `notifications` is used to manage dynamic success/error messages, replacing the ASP.NET `Label1`. These messages are triggered by `hx-success` and `hx-error` on the form.
-   **Loading Indicators:**
    -   `htmx-indicator` classes are used with specific IDs to show visual feedback during HTMX requests, indicating that data is being loaded or processed.

---

### Final Notes

-   **DRY Principle:** Templates extend `base.html`. Partial templates (`_financialyear_select_options.html`, `_date_fields_form_part.html`) are used for reusable UI components.
-   **Fat Model, Thin View:** Business logic for deriving fields (`financial_year_display`, `session_user`) is encapsulated within the `FinancialYear` model's `update_derived_fields` method, called from the form's `save` method. Views remain concise, focusing on handling requests and rendering responses.
-   **Automated Conversion Focus:** This plan provides complete, runnable code examples that can be used as a template for AI-assisted conversion. The structure simplifies the mapping from ASP.NET Web Forms components to their Django/HTMX equivalents, making the process systematic and reducing manual intervention.
-   **Business Value:** This modernized system will offer a faster, more responsive user experience due to HTMX's partial page updates, improved maintainability with clear separation of concerns, and enhanced scalability provided by the Django framework. The use of standard, modern technologies ensures a future-proof solution.