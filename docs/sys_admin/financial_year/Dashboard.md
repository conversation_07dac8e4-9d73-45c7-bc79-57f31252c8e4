## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

The provided ASP.NET code for `Dashboard.aspx` and its code-behind is very minimal, showing only an empty page with content placeholders and an empty `Page_Load` method. This means there are no explicit database interactions, UI components, or backend functionalities (like CRUD operations) defined within the provided code.

To provide a concrete modernization plan, we will infer the purpose of this "FinancialYear_Dashboard" page. It's likely intended to display and manage financial year data. Therefore, we will create a comprehensive Django solution for managing `FinancialYear` entities, demonstrating a standard CRUD pattern with a list view acting as the "dashboard".

We will use the following inferred details:
*   **Application Name:** `financial_year` (derived from `Module_SysAdmin_FinancialYear_Dashboard`)
*   **Model Name:** `FinancialYear`
*   **Database Table:** `tblFinancialYear` (common ASP.NET naming convention)
*   **Inferred Fields:** `year_name` (e.g., "FY 2023-2024"), `start_date`, `end_date`, `is_active` (boolean).

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**
Given the ASP.NET page is named `FinancialYear_Dashboard` and no explicit database schema is provided, we infer that the primary entity managed by this dashboard is a `FinancialYear`.

*   **Inferred Table Name:** `tblFinancialYear`
*   **Inferred Column Names and Data Types:**
    *   `id`: Primary Key (auto-incrementing integer)
    *   `YearName`: `NVARCHAR(100)` (e.g., "FY 2023-2024")
    *   `StartDate`: `DATE`
    *   `EndDate`: `DATE`
    *   `IsActive`: `BIT` (Boolean)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**
Since the provided `Page_Load` method is empty and no ASP.NET controls are defined, no explicit CRUD (Create, Read, Update, Delete) operations are visible. However, a "Dashboard" for an entity like "Financial Year" typically implies the ability to:

*   **Read:** List all existing financial years.
*   **Create:** Add a new financial year.
*   **Update:** Modify details of an existing financial year.
*   **Delete:** Remove a financial year.

We will implement these standard CRUD functionalities in the Django application.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**
The provided ASP.NET code contains no specific UI controls. For a "Dashboard" focused on `FinancialYear` management, the standard Django/HTMX/Alpine.js pattern would involve:

*   **Data List:** A table (rendered using DataTables) to display all financial years.
*   **Action Buttons:** Buttons for "Add New Financial Year", "Edit", and "Delete" for each row.
*   **Modals:** Forms for Create/Update/Delete operations displayed within dynamic modals loaded via HTMX.
*   **Input Controls:** Text boxes for `YearName`, date pickers for `StartDate` and `EndDate`, and a checkbox for `IsActive`.

## Step 4: Generate Django Code

### 4.1 Models

**Task:** Create a Django model based on the inferred database schema.

**Instructions:**
The `FinancialYear` model will map to the `tblFinancialYear` database table. We'll include basic business logic for validation (e.g., ensuring `start_date` is before `end_date`).

**File: `financial_year/models.py`**

```python
from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone

class FinancialYear(models.Model):
    id = models.AutoField(db_column='ID', primary_key=True)
    year_name = models.CharField(db_column='YearName', max_length=100, unique=True,
                                 help_text="e.g., 'FY 2023-2024'")
    start_date = models.DateField(db_column='StartDate')
    end_date = models.DateField(db_column='EndDate')
    is_active = models.BooleanField(db_column='IsActive', default=False)

    class Meta:
        managed = False  # Set to True if Django should manage table creation/alteration
        db_table = 'tblFinancialYear'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'
        ordering = ['-start_date'] # Order by most recent year first

    def __str__(self):
        """Returns the year name as the string representation."""
        return self.year_name

    def clean(self):
        """
        Custom validation for the FinancialYear model.
        Ensures end_date is not before start_date and year_name format.
        """
        if self.start_date and self.end_date:
            if self.end_date < self.start_date:
                raise ValidationError({
                    'end_date': 'End date cannot be before start date.'
                })
        
        # Basic validation for year_name format (e.g., "FY YYYY-YYYY")
        if not self.year_name.startswith('FY ') or len(self.year_name) != 13 or self.year_name[7] != '-':
            raise ValidationError({
                'year_name': "Year name should be in 'FY YYYY-YYYY' format (e.g., 'FY 2023-2024')."
            })

    def is_current_year(self):
        """
        Checks if this financial year is the current one based on today's date.
        """
        today = timezone.localdate()
        return self.start_date <= today <= self.end_date
    
    def activate_year(self):
        """
        Marks this financial year as active and deactivates others.
        This demonstrates a fat model approach for business logic.
        """
        # Deactivate all other financial years
        FinancialYear.objects.exclude(id=self.id).update(is_active=False)
        self.is_active = True
        self.save()
        
    def get_summary(self):
        """
        Returns a dictionary summary of the financial year.
        """
        return {
            'id': self.id,
            'year_name': self.year_name,
            'period': f"{self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}",
            'status': 'Active' if self.is_active else 'Inactive',
            'is_current': self.is_current_year()
        }

```

### 4.2 Forms

**Task:** Define a Django form for user input.

**Instructions:**
A `ModelForm` for `FinancialYear` will be created, including widgets with Tailwind CSS classes for consistent styling.

**File: `financial_year/forms.py`**

```python
from django import forms
from .models import FinancialYear

class FinancialYearForm(forms.ModelForm):
    class Meta:
        model = FinancialYear
        fields = ['year_name', 'start_date', 'end_date', 'is_active']
        widgets = {
            'year_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'placeholder': 'e.g., FY 2023-2024'}),
            'start_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'end_date': forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500'}),
        }
        labels = {
            'year_name': 'Financial Year Name',
            'start_date': 'Start Date',
            'end_date': 'End Date',
            'is_active': 'Is Active (Current Year)'
        }
        
    # Custom validation logic from model's clean method is automatically called.
    # Any form-specific validation can go here.
    def clean(self):
        cleaned_data = super().clean()
        # Example of form-specific validation:
        # If is_active is checked, ensure no other year is active.
        # This is better handled in the model's activate_year method for persistence.
        return cleaned_data

```

### 4.3 Views

**Task:** Implement CRUD operations using CBVs, ensuring views are thin and business logic is in models. An additional view for the HTMX-loaded table partial is included.

**File: `financial_year/views.py`**

```python
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import FinancialYear
from .forms import FinancialYearForm

class FinancialYearListView(ListView):
    """
    Displays a list of all financial years. This serves as the main dashboard view.
    """
    model = FinancialYear
    template_name = 'financial_year/financial_year/list.html'
    context_object_name = 'financial_years' # This variable will be used in the template

class FinancialYearTablePartialView(ListView):
    """
    Returns only the table portion of the financial year list,
    designed to be loaded via HTMX for dynamic updates.
    """
    model = FinancialYear
    template_name = 'financial_year/financial_year/_financial_year_table.html'
    context_object_name = 'financial_years'

class FinancialYearCreateView(CreateView):
    """
    Handles creation of new financial year records.
    """
    model = FinancialYear
    form_class = FinancialYearForm
    template_name = 'financial_year/financial_year/_financial_year_form.html' # Partial for HTMX modal
    success_url = reverse_lazy('financial_year_list') # Fallback if not HTMX

    def form_valid(self, form):
        # Business logic can be placed here if it cannot be in the model
        # For 'is_active', we'll use the model method
        response = super().form_valid(form) # Saves the form instance
        
        if form.instance.is_active:
            form.instance.activate_year() # Call model method to handle activation logic
        
        messages.success(self.request, 'Financial Year added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response with a trigger for HTMX to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinancialYearList' # HTMX trigger for list refresh
                }
            )
        return response

class FinancialYearUpdateView(UpdateView):
    """
    Handles updating existing financial year records.
    """
    model = FinancialYear
    form_class = FinancialYearForm
    template_name = 'financial_year/financial_year/_financial_year_form.html' # Partial for HTMX modal
    success_url = reverse_lazy('financial_year_list') # Fallback if not HTMX

    def form_valid(self, form):
        response = super().form_valid(form) # Saves the form instance
        
        if form.instance.is_active:
            form.instance.activate_year() # Call model method to handle activation logic
        
        messages.success(self.request, 'Financial Year updated successfully.')
        if self.request.headers.get('HX-Request'):
            # Return a 204 No Content response with a trigger for HTMX to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinancialYearList' # HTMX trigger for list refresh
                }
            )
        return response

class FinancialYearDeleteView(DeleteView):
    """
    Handles deletion of financial year records.
    """
    model = FinancialYear
    template_name = 'financial_year/financial_year/confirm_delete.html' # Partial for HTMX modal
    success_url = reverse_lazy('financial_year_list') # Fallback if not HTMX

    def delete(self, request, *args, **kwargs):
        # Optional: Add business logic before deletion in the model
        # e.g., self.object.can_be_deleted()
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Financial Year deleted successfully.')
        if request.headers.get('HX-Request'):
            # Return a 204 No Content response with a trigger for HTMX to refresh the list
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshFinancialYearList' # HTMX trigger for list refresh
                }
            )
        return response

```

### 4.4 Templates

**Task:** Create templates for each view, ensuring HTMX and Alpine.js integration, DataTables for list views, and proper template inheritance.

**Instructions:**
Remember: `core/base.html` is assumed to exist and provides the overall page structure, including CDN links for HTMX, Alpine.js, jQuery, and DataTables.

**File: `financial_year/templates/financial_year/financial_year/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Financial Years Dashboard</h2>
        <button
            class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-md shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'financial_year_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            <i class="fas fa-plus-circle mr-2"></i> Add New Financial Year
        </button>
    </div>
    
    <div id="financial_yearTable-container"
         hx-trigger="load, refreshFinancialYearList from:body"
         hx-get="{% url 'financial_year_table' %}"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-xl overflow-hidden">
        <!-- Initial loading state -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Financial Year data...</p>
        </div>
    </div>
    
    <!-- Global Modal Structure for HTMX -->
    <div id="modal" 
         class="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center z-50 hidden"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" 
             class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 sm:mx-auto relative transform transition-all duration-300 scale-95 opacity-0"
             _="on load add .scale-100 .opacity-100 to me then wait 0.1s then remove .scale-95 .opacity-0 from me
                on htmx:afterSwap remove .is-active from #modal">
            <!-- Content loaded via HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js components can be defined here if needed for broader page scope.
        // For specific component state, Alpine.js is often embedded directly in HTMX partials.
    });
    
    // Listen for HTMX success messages and display them
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.detail.xhr.status >= 200 && evt.detail.xhr.status < 300) {
            const successMessage = evt.detail.xhr.getResponseHeader('X-Success-Message');
            if (successMessage) {
                // You would typically have a global toast/notification component here
                console.log('Success:', successMessage);
                // Example: display a simple alert (replace with proper UI notification)
                // alert(successMessage);
            }
        }
    });

</script>
{% endblock %}
```

**File: `financial_year/templates/financial_year/financial_year/_financial_year_table.html`**

```html
<div class="overflow-x-auto">
    <table id="financialYearTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Financial Year Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Start Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">End Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% for financial_year in financial_years %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ financial_year.year_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ financial_year.start_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">{{ financial_year.end_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap">
                    {% if financial_year.is_active %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes</span>
                    {% else %}
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">No</span>
                    {% endif %}
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <button
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded-md text-xs mr-2 transition duration-150 ease-in-out"
                        hx-get="{% url 'financial_year_edit' financial_year.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded-md text-xs transition duration-150 ease-in-out"
                        hx-get="{% url 'financial_year_delete' financial_year.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="6" class="py-4 px-4 text-center text-gray-500">No financial years found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables only after the content is loaded via HTMX
    $(document).ready(function() {
        $('#financialYearTable').DataTable({
            "pagingType": "full_numbers", // For full pagination controls
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "language": {
                "search": "Search records:",
                "lengthMenu": "Show _MENU_ entries",
                "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                "infoEmpty": "Showing 0 to 0 of 0 entries",
                "infoFiltered": "(filtered from _MAX_ total entries)",
                "paginate": {
                    "first": "First",
                    "last": "Last",
                    "next": "Next",
                    "previous": "Previous"
                }
            }
        });
    });
</script>
```

**File: `financial_year/templates/financial_year/financial_year/_financial_year_form.html`**

```html
<div class="p-6" x-data="{}" hx-target="this" hx-swap="outerHTML">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">
        {% if form.instance.pk %}Edit Financial Year{% else %}Add New Financial Year{% endif %}
    </h3>
    <form hx-post="{{ request.path }}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-y-6 gap-x-4">
            <div>
                <label for="{{ form.year_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.year_name.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.year_name }}
                {% if form.year_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.year_name.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.start_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.start_date.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.start_date }}
                {% if form.start_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.start_date.errors }}</p>
                {% endif %}
            </div>
            
            <div>
                <label for="{{ form.end_date.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ form.end_date.label }}<span class="text-red-500">*</span>
                </label>
                {{ form.end_date }}
                {% if form.end_date.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.end_date.errors }}</p>
                {% endif %}
            </div>
            
            <div class="flex items-center mt-6">
                {{ form.is_active }}
                <label for="{{ form.is_active.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-900">
                    {{ form.is_active.label }}
                </label>
                {% if form.is_active.errors %}
                <p class="text-red-500 text-xs mt-1">{{ form.is_active.errors }}</p>
                {% endif %}
            </div>
        </div>
        
        {% if form.non_field_errors %}
            <div class="mt-4 text-red-500 text-sm">
                {% for error in form.non_field_errors %}
                    <p>{{ error }}</p>
                {% endfor %}
            </div>
        {% endif %}

        <div class="mt-8 flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Save Financial Year
            </button>
        </div>
    </form>
</div>
```

**File: `financial_year/templates/financial_year/financial_year/confirm_delete.html`**

```html
<div class="p-6" x-data="{}" hx-target="this" hx-swap="outerHTML">
    <h3 class="text-2xl font-semibold text-gray-900 mb-6 border-b pb-3">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the financial year: <strong>{{ object.year_name }}</strong>?</p>
    
    <form hx-post="{% url 'financial_year_delete' object.pk %}" hx-swap="none" hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="mt-6 flex justify-end space-x-4">
            <button
                type="button"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button
                type="submit"
                class="bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

### 4.5 URLs

**Task:** Define URL patterns for the views, including HTMX partials.

**File: `financial_year/urls.py`**

```python
from django.urls import path
from .views import (
    FinancialYearListView,
    FinancialYearTablePartialView,
    FinancialYearCreateView,
    FinancialYearUpdateView,
    FinancialYearDeleteView
)

urlpatterns = [
    # Main list view (dashboard)
    path('financial_years/', FinancialYearListView.as_view(), name='financial_year_list'),
    
    # HTMX partial for the table (for refreshing the list)
    path('financial_years/table/', FinancialYearTablePartialView.as_view(), name='financial_year_table'),

    # CRUD operations loaded into modal via HTMX
    path('financial_years/add/', FinancialYearCreateView.as_view(), name='financial_year_add'),
    path('financial_years/edit/<int:pk>/', FinancialYearUpdateView.as_view(), name='financial_year_edit'),
    path('financial_years/delete/<int:pk>/', FinancialYearDeleteView.as_view(), name='financial_year_delete'),
]
```

### 4.6 Tests

**Task:** Write comprehensive unit tests for the model and integration tests for all views.

**File: `financial_year/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from django.core.exceptions import ValidationError
from .models import FinancialYear
from .forms import FinancialYearForm

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create a non-active financial year for testing
        cls.fy1 = FinancialYear.objects.create(
            year_name='FY 2022-2023',
            start_date='2022-07-01',
            end_date='2023-06-30',
            is_active=False
        )
        # Create an active financial year for testing
        cls.fy2 = FinancialYear.objects.create(
            year_name='FY 2023-2024',
            start_date='2023-07-01',
            end_date='2024-06-30',
            is_active=True # This one is initially active
        )

    def test_financial_year_creation(self):
        """Test basic creation and field values."""
        self.assertEqual(self.fy1.year_name, 'FY 2022-2023')
        self.assertEqual(self.fy1.start_date, timezone.localdate(2022, 7, 1))
        self.assertEqual(self.fy1.end_date, timezone.localdate(2023, 6, 30))
        self.assertFalse(self.fy1.is_active)
        
        self.assertEqual(self.fy2.year_name, 'FY 2023-2024')
        self.assertTrue(self.fy2.is_active)

    def test_year_name_label(self):
        """Test verbose name of a field."""
        field_label = self.fy1._meta.get_field('year_name').verbose_name
        self.assertEqual(field_label, 'Financial Year Name')

    def test_str_method(self):
        """Test the __str__ method returns the year name."""
        self.assertEqual(str(self.fy1), 'FY 2022-2023')

    def test_end_date_before_start_date_validation(self):
        """Test model validation for end_date being before start_date."""
        fy = FinancialYear(
            year_name='FY 2024-2025',
            start_date='2025-01-01',
            end_date='2024-12-31'
        )
        with self.assertRaisesMessage(ValidationError, 'End date cannot be before start date.'):
            fy.full_clean() # Calls clean() and validates all fields

    def test_year_name_format_validation(self):
        """Test model validation for year_name format."""
        # Invalid format
        fy_invalid = FinancialYear(
            year_name='2024-2025', # Missing 'FY '
            start_date='2024-07-01',
            end_date='2025-06-30'
        )
        with self.assertRaisesMessage(ValidationError, "Year name should be in 'FY YYYY-YYYY' format"):
            fy_invalid.full_clean()

        fy_invalid_len = FinancialYear(
            year_name='FY 24-25', # Incorrect length
            start_date='2024-07-01',
            end_date='2025-06-30'
        )
        with self.assertRaisesMessage(ValidationError, "Year name should be in 'FY YYYY-YYYY' format"):
            fy_invalid_len.full_clean()

    def test_is_current_year_method(self):
        """Test the is_current_year method."""
        # Create a financial year that encompasses today
        today = timezone.localdate()
        current_fy = FinancialYear.objects.create(
            year_name='FY Current',
            start_date=today - timezone.timedelta(days=30),
            end_date=today + timezone.timedelta(days=30),
            is_active=False
        )
        self.assertTrue(current_fy.is_current_year())

        # Test past year
        self.assertFalse(self.fy1.is_current_year())
        # Test future year
        future_fy = FinancialYear(
            year_name='FY 2050-2051',
            start_date='2050-07-01',
            end_date='2051-06-30',
            is_active=False
        )
        self.assertFalse(future_fy.is_current_year())
        
    def test_activate_year_method(self):
        """Test activate_year sets is_active and deactivates others."""
        # Ensure fy1 is inactive and fy2 is active before test
        self.assertFalse(FinancialYear.objects.get(pk=self.fy1.pk).is_active)
        self.assertTrue(FinancialYear.objects.get(pk=self.fy2.pk).is_active)

        # Activate fy1
        self.fy1.activate_year()
        
        # Reload objects from DB to verify changes
        self.fy1.refresh_from_db()
        self.fy2.refresh_from_db()

        self.assertTrue(self.fy1.is_active)
        self.assertFalse(self.fy2.is_active) # fy2 should now be inactive

    def test_get_summary_method(self):
        """Test the get_summary method output."""
        summary = self.fy1.get_summary()
        self.assertIn('id', summary)
        self.assertIn('year_name', summary)
        self.assertIn('period', summary)
        self.assertIn('status', summary)
        self.assertIn('is_current', summary)
        self.assertEqual(summary['year_name'], 'FY 2022-2023')
        self.assertEqual(summary['status'], 'Inactive')


class FinancialYearViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial data for views tests
        cls.fy_active = FinancialYear.objects.create(
            year_name='FY 2023-2024',
            start_date='2023-07-01',
            end_date='2024-06-30',
            is_active=True
        )
        cls.fy_inactive = FinancialYear.objects.create(
            year_name='FY 2022-2023',
            start_date='2022-07-01',
            end_date='2023-06-30',
            is_active=False
        )
    
    def setUp(self):
        self.client = Client()

    # --- List View Tests ---
    def test_list_view_get(self):
        response = self.client.get(reverse('financial_year_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financial_year/list.html')
        self.assertIn('financial_years', response.context)
        self.assertIsInstance(response.context['financial_years'].first(), FinancialYear)

    def test_table_partial_view_get_htmx(self):
        response = self.client.get(reverse('financial_year_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financial_year/_financial_year_table.html')
        self.assertIn('financial_years', response.context)
        # Check if it contains expected data from the table
        self.assertContains(response, self.fy_active.year_name)
        self.assertContains(response, self.fy_inactive.year_name)

    # --- Create View Tests ---
    def test_create_view_get_htmx(self):
        response = self.client.get(reverse('financial_year_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financial_year/_financial_year_form.html')
        self.assertIn('form', response.context)
        self.assertIsInstance(response.context['form'], FinancialYearForm)

    def test_create_view_post_success_htmx(self):
        data = {
            'year_name': 'FY 2024-2025',
            'start_date': '2024-07-01',
            'end_date': '2025-06-30',
            'is_active': 'on'
        }
        # Simulate HTMX request
        response = self.client.post(reverse('financial_year_add'), data, HTTP_HX_REQUEST='true')
        
        # HTMX successful post should return 204 No Content
        self.assertEqual(response.status_code, 204)
        # Verify HTMX trigger is present
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinancialYearList')
        
        # Verify object was created and only the new one is active
        self.assertTrue(FinancialYear.objects.filter(year_name='FY 2024-2025', is_active=True).exists())
        self.assertFalse(FinancialYear.objects.get(pk=self.fy_active.pk).is_active) # Previous active should be deactivated

    def test_create_view_post_invalid(self):
        data = {
            'year_name': 'Invalid Year', # Invalid format
            'start_date': '2025-01-01',
            'end_date': '2024-12-31', # End date before start date
            'is_active': False
        }
        response = self.client.post(reverse('financial_year_add'), data, HTTP_HX_REQUEST='true')
        
        # Invalid form submission should return 200 OK and render the form again with errors
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financial_year/_financial_year_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertContains(response, 'End date cannot be before start date.')
        self.assertContains(response, 'Year name should be in &#x27;FY YYYY-YYYY&#x27; format')


    # --- Update View Tests ---
    def test_update_view_get_htmx(self):
        response = self.client.get(reverse('financial_year_edit', args=[self.fy_inactive.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financial_year/_financial_year_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.fy_inactive)

    def test_update_view_post_success_htmx(self):
        # Update inactive year to active
        data = {
            'year_name': self.fy_inactive.year_name,
            'start_date': self.fy_inactive.start_date.isoformat(),
            'end_date': self.fy_inactive.end_date.isoformat(),
            'is_active': 'on'
        }
        response = self.client.post(reverse('financial_year_edit', args=[self.fy_inactive.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinancialYearList')
        
        # Verify fy_inactive is now active and fy_active is now inactive
        self.fy_inactive.refresh_from_db()
        self.fy_active.refresh_from_db()
        self.assertTrue(self.fy_inactive.is_active)
        self.assertFalse(self.fy_active.is_active)

    def test_update_view_post_invalid(self):
        data = {
            'year_name': self.fy_inactive.year_name,
            'start_date': '2025-01-01',
            'end_date': '2024-12-31', # Invalid end date
            'is_active': False
        }
        response = self.client.post(reverse('financial_year_edit', args=[self.fy_inactive.pk]), data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financial_year/_financial_year_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertContains(response, 'End date cannot be before start date.')


    # --- Delete View Tests ---
    def test_delete_view_get_htmx(self):
        response = self.client.get(reverse('financial_year_delete', args=[self.fy_inactive.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'financial_year/financial_year/confirm_delete.html')
        self.assertIn('object', response.context)
        self.assertEqual(response.context['object'], self.fy_inactive)

    def test_delete_view_post_success_htmx(self):
        # Create a new object to delete to avoid affecting other tests if possible
        fy_to_delete = FinancialYear.objects.create(
            year_name='FY 2021-2022',
            start_date='2021-07-01',
            end_date='2022-06-30',
            is_active=False
        )
        initial_count = FinancialYear.objects.count()
        
        response = self.client.post(reverse('financial_year_delete', args=[fy_to_delete.pk]), HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshFinancialYearList')
        
        # Verify object was deleted
        self.assertFalse(FinancialYear.objects.filter(pk=fy_to_delete.pk).exists())
        self.assertEqual(FinancialYear.objects.count(), initial_count - 1)

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

The generated code heavily leverages HTMX and Alpine.js for a modern, dynamic user experience:

*   **HTMX for dynamic updates:**
    *   The `financial_year_list.html` uses `hx-get="{% url 'financial_year_table' %}"` with `hx-trigger="load, refreshFinancialYearList from:body"` to dynamically load and refresh the DataTables table. This ensures the table updates without a full page reload after any CRUD operation.
    *   Create, Update, and Delete actions are triggered by `hx-get` requests to specific URL endpoints, loading the form/confirmation partials into a modal.
    *   Form submissions (`hx-post`) return `204 No Content` along with an `HX-Trigger: refreshFinancialYearList` header, instructing the `financial_yearTable-container` to reload its content, thereby updating the DataTables.
*   **Alpine.js for UI state management:**
    *   A global `x-data="{}"` is implied on the `body` or a parent container, allowing Alpine.js to manage local UI state.
    *   The modal (`#modal`) uses Alpine.js (via `_` attribute, which is Alpine's syntax through HTMX) for basic show/hide functionality, e.g., `on click add .is-active to #modal` and `on click if event.target.id == 'modal' remove .is-active from me` for closing on overlay click.
    *   Form partials use `hx-on::after-request` to explicitly close the modal after a successful HTMX form submission (status 204).
*   **DataTables for list views:**
    *   The `_financial_year_table.html` partial includes the JavaScript initialization for DataTables. This ensures that every time the table partial is loaded/reloaded via HTMX, DataTables re-initializes on the new HTML, providing client-side searching, sorting, and pagination.
*   **No Full Page Reloads:** All CRUD interactions are handled dynamically via HTMX, providing a smooth single-page application feel without the complexity of a full JavaScript framework.
*   **DRY Template Inheritance:** All main templates (`list.html`) extend `core/base.html` for consistent styling and inclusion of global assets like CDN links for Tailwind CSS, HTMX, Alpine.js, jQuery, and DataTables. Form and delete templates are partials meant to be loaded into the modal.

## Final Notes

*   **Placeholders:** All placeholders (`[MODEL_NAME]`, `[FIELD1]`, etc.) have been replaced with concrete values derived from the inferred `FinancialYear` entity.
*   **DRY Principles:** Templates are designed with reusability in mind (e.g., `_financial_year_form.html` for both create and update). Model methods (`activate_year`, `is_current_year`) encapsulate business logic, adhering to the "Fat Model, Thin View" principle.
*   **Component-specific code:** Only code relevant to the `FinancialYear` module is generated. `base.html` content is explicitly excluded as per instructions.
*   **Comprehensive Tests:** Robust unit tests for the `FinancialYear` model and integration tests for all CRUD views (including HTMX interactions) are provided, aiming for high test coverage.
*   **Business Value:** This modernization plan transitions a legacy ASP.NET dashboard, which was essentially a placeholder, into a fully functional, modern web application. It offers:
    *   **Improved User Experience:** Fast, dynamic interactions with no full page reloads, making the application feel more responsive.
    *   **Simplified Frontend Development:** Reliance on HTMX and Alpine.js minimizes custom JavaScript, leading to faster development and easier maintenance.
    *   **Robust Data Management:** Clear separation of concerns with business logic in models and structured forms ensures data integrity and consistency.
    *   **Scalability & Maintainability:** Django's robust framework, combined with best practices like CBVs and testing, provides a highly maintainable and scalable solution for future growth.
    *   **Cost Efficiency:** Automation-focused approach, leveraging Django's built-in features and popular libraries, significantly reduces manual coding effort and potential for human error.