## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Comprehensive Django Modernization Plan for Financial Year Deletion

This plan outlines the modernization of your ASP.NET financial year deletion module to a robust, maintainable, and highly efficient Django application. The focus is on leveraging modern web technologies like HTMX and Alpine.js for dynamic, interactive user experiences without the complexity of traditional JavaScript frameworks.

**Business Benefits:**

1.  **Reduced Technical Debt:** Moving away from legacy ASP.NET reduces reliance on outdated technologies, making the application easier to maintain and extend in the long run.
2.  **Improved Performance & Responsiveness:** Django's efficient backend combined with HTMX's partial page updates and Alpine.js's lightweight reactivity will deliver a snappier user experience, similar to a single-page application without the overhead.
3.  **Enhanced Maintainability:** Django's "Don't Repeat Yourself" (DRY) principle, clear separation of concerns (models for business logic, thin views for data presentation, templates for UI), and comprehensive testing framework make the codebase easier to understand, debug, and scale.
4.  **Cost-Effective Scalability:** Django is designed for scalability, allowing your application to grow with your business needs without significant infrastructure overhauls. Open-source technologies reduce licensing costs.
5.  **Modern User Experience:** The use of Tailwind CSS ensures a clean, mobile-responsive interface out-of-the-box, providing a consistent and intuitive experience for users.
6.  **AI-Assisted Migration:** The systematic breakdown and template-driven code generation facilitate the use of AI tools to automate significant portions of the conversion, drastically reducing manual coding effort and accelerating the migration timeline.

**High-Level Steps for Modernization:**

1.  **Database Mapping:** We will define Django models that precisely map to your existing `tblCompany_master` and `tblFinancial_master` database tables without altering the database schema, ensuring a non-disruptive transition.
2.  **Business Logic Centralization:** The logic for formatting financial year strings and handling the deletion process will be moved into the Django models, adhering to the "Fat Model, Thin View" philosophy. This ensures that business rules are consistent and easily testable.
3.  **Dynamic Form Interactions with HTMX:** The existing Company dropdown and Financial Year listbox will be replaced with HTMX-powered components. Selecting a company will dynamically load the corresponding financial years into the listbox without a full page reload, mimicking the ASP.NET `AutoPostBack` functionality more efficiently.
4.  **User Confirmation with Alpine.js:** A simple, lightweight confirmation modal will be integrated using Alpine.js to provide a "Are you sure?" prompt before deletion, enhancing user experience and preventing accidental data loss.
5.  **Robust Error Handling & Messaging:** Django's built-in messaging framework will provide clear success or error feedback to the user after a delete operation, similar to how the ASP.NET query string messages functioned.
6.  **Automated Testing:** Comprehensive unit and integration tests will be developed alongside the code to ensure that the migrated functionality works exactly as expected, guaranteeing data integrity and application reliability.

**Automation Focus:**

This plan is designed to be highly amenable to AI-assisted automation. Each step, from database schema extraction to code generation and testing, can be guided by conversational AI tools. For example, AI can:
-   Automatically generate Django models based on inferred database schema.
-   Draft Django forms with appropriate widgets and validation.
-   Structure Django views and URL patterns.
-   Generate initial HTMX attributes and Alpine.js components.
-   Provide boilerplate for unit and integration tests, requiring only specific test data.

This approach significantly reduces the manual effort required, minimizes human error, and accelerates the overall migration process, allowing your team to focus on validation and refinement rather than repetitive coding.

## Conversion Steps:

## Step 1: Extract Database Schema

**Task:** Identify the database table and its columns from the ASP.NET code.

**Instructions:**

From the ASP.NET code, we identify the following database interactions:
-   `fun.dropdownCompany(DropDownDelFYCName)` implies a `Company` table is used for the dropdown.
-   `Select * from tblFinancial_master where CompId=...` directly references `tblFinancial_master`.
-   `delete from tblFinancial_master where FinYearId='...' AND CompId=...` confirms the table and key columns.
-   `DS.Tables[0].Rows[i]["FinYearFrom"].ToString()`, `DS.Tables[0].Rows[i]["FinYearTo"].ToString()`, `DS.Tables[0].Rows[i][0].ToString()` (which is `FinYearId`) show the columns used.

**Identified Database Schema:**

*   **Table Name for Financial Years:** `tblFinancial_master`
    *   **Columns:**
        *   `FinYearId` (Primary Key - inferred from `DS.Tables[0].Rows[i][0]`)
        *   `CompId` (Foreign Key - inferred from `CompId=` in SQL query)
        *   `FinYearFrom` (Date)
        *   `FinYearTo` (Date)
*   **Table Name for Companies:** `tblCompany_master` (Inferred from `dropdownCompany` function)
    *   **Columns:**
        *   `CompId` (Primary Key - inferred from `DropDownDelFYCName.SelectedValue`)
        *   `CompName` (String - inferred from display in dropdown)

## Step 2: Identify Backend Functionality

**Task:** Determine the CRUD operations in the ASP.NET code.

**Instructions:**

The provided ASP.NET code primarily handles **Read** and **Delete** operations.

*   **Create:** No creation functionality is present on this page.
*   **Read:**
    *   **Company List:** The `fun.dropdownCompany(DropDownDelFYCName)` function populates the initial list of companies.
    *   **Financial Year List (Conditional):** The `DropDownDelFYCName_SelectedIndexChanged` event dynamically fetches and populates the `ListBoxDelFinYear` with financial years (`FinYearFrom`, `FinYearTo`, `FinYearId`) associated with the selected company.
*   **Update:** No update functionality is present on this page.
*   **Delete:**
    *   The `Delete_Click` event handler performs the core deletion. It executes a `DELETE` SQL command on `tblFinancial_master` based on the selected `FinYearId` and `CompId`.
*   **Validation Logic:**
    *   `RequiredFieldValidator` ensures both `DropDownDelFYCName` and `ListBoxDelFinYear` have a selection.
    *   Client-side JavaScript `confirmationDelete()` prompts for user confirmation before deletion.
*   **Messaging:** A `Label1` is used to display success or error messages, often populated from a query string after a redirect.

## Step 3: Infer UI Components

**Task:** Analyze ASP.NET controls and their roles.

**Instructions:**

The following ASP.NET controls are identified and their corresponding Django/HTMX/Alpine.js equivalents are determined:

*   **`DropDownDelFYCName` (asp:DropDownList):**
    *   **Role:** Allows selection of a Company. `AutoPostBack="True"` for dynamic updates.
    *   **Django Equivalent:** `forms.ModelChoiceField` rendered as a `<select>` tag.
    *   **HTMX/Alpine.js:** HTMX `hx-get` to trigger a request to fetch financial years based on the selected company, `hx-target` to update the financial year dropdown, and `hx-swap` to inject the new options.
*   **`ListBoxDelFinYear` (asp:ListBox):**
    *   **Role:** Displays a list of financial years for the selected company, allowing single selection for deletion.
    *   **Django Equivalent:** `forms.ModelChoiceField` rendered as a `<select size="X">` tag to mimic a listbox.
    *   **HTMX/Alpine.js:** Populated dynamically by the HTMX call triggered by the Company dropdown.
*   **`Delete` (asp:Button):**
    *   **Role:** Initiates the deletion process. `OnClientClick="return confirmationDelete()"` for client-side confirmation.
    *   **Django Equivalent:** A standard HTML `<button type="submit">` within a Django form.
    *   **HTMX/Alpine.js:** An Alpine.js `x-on:click` to show a confirmation modal. The modal's confirm button will trigger the HTMX `hx-post` for form submission.
*   **`Label1` (asp:Label):**
    *   **Role:** Displays status messages (success/error).
    *   **Django Equivalent:** Django's messages framework.
    *   **HTMX/Alpine.js:** Messages will be displayed after a full page reload (mimicking original behavior), or injected into a specific `div` using HTMX `hx-swap` if a more dynamic message display is desired (though a full page refresh simplifies message handling).
*   **`RequiredFieldValidator`:**
    *   **Role:** Enforces selection for Company and Financial Year.
    *   **Django Equivalent:** Form field `required=True` and custom `clean()` methods in the Django form.

## Step 4: Generate Django Code

We will create a new Django application, for example, named `sysadmin`.

### 4.1 Models (`sysadmin/models.py`)

**Task:** Create Django models based on the database schema.

**Instructions:**

-   `Company` model represents `tblCompany_master`.
-   `FinancialYear` model represents `tblFinancial_master`, with a `ForeignKey` to `Company`.
-   `managed = False` and `db_table` are used to map to existing database tables.
-   A method `formatted_financial_year` is added to `FinancialYear` to encapsulate the business logic of formatting the year string (`YYYY-YYYY`), adhering to the "Fat Model" principle.
-   A `delete_financial_year` class method is added to `FinancialYear` for handling the specific deletion logic, keeping views thin.

```python
from django.db import models

class Company(models.Model):
    """
    Maps to the existing tblCompany_master table.
    Assumes CompId is the primary key and CompName is the display field.
    """
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    comp_name = models.CharField(db_column='CompName', max_length=255) # Assuming max_length sufficient

    class Meta:
        managed = False  # Tells Django not to manage this table's schema (it already exists)
        db_table = 'tblCompany_master'
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return self.comp_name

class FinancialYear(models.Model):
    """
    Maps to the existing tblFinancial_master table.
    Assumes FinYearId is the primary key and CompId is a foreign key to Company.
    """
    fin_year_id = models.IntegerField(db_column='FinYearId', primary_key=True)
    # Use DO_NOTHING for on_delete as schema is managed externally
    company = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')
    fin_year_from = models.DateField(db_column='FinYearFrom')
    fin_year_to = models.DateField(db_column='FinYearTo')

    class Meta:
        managed = False  # Tells Django not to manage this table's schema
        db_table = 'tblFinancial_master'
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.formatted_financial_year()

    def formatted_financial_year(self):
        """
        Generates the 'YYYY-YYYY' financial year string, e.g., '2023-2024'.
        This logic is encapsulated within the model (Fat Model).
        """
        from_year = self.fin_year_from.strftime('%Y')
        to_year = self.fin_year_to.strftime('%Y')
        return f"{from_year}-{to_year}"

    @classmethod
    def delete_by_company_and_id(cls, company_pk, fin_year_pk):
        """
        Class method to delete a specific financial year associated with a company.
        This centralizes the delete logic in the model.
        Returns (success_boolean, message_string).
        """
        try:
            # Get the object using both PKs to ensure correctness and prevent accidental deletion
            obj = cls.objects.get(company__pk=company_pk, fin_year_id=fin_year_pk)
            obj.delete()
            return True, "Financial year deleted successfully."
        except cls.DoesNotExist:
            return False, "Selected financial year not found for the chosen company. It may have been deleted already."
        except Exception as e:
            # Log the full exception `e` in a real application
            return False, f"An unexpected error occurred during deletion: {e}"

```

### 4.2 Forms (`sysadmin/forms.py`)

**Task:** Define a Django form for user input.

**Instructions:**

-   A custom `FinancialYearDeleteForm` is created as a `forms.Form` (not `ModelForm`) because it handles data from two related models for a specific action (deletion based on two selections).
-   `ModelChoiceField` is used for `company` and `financial_year`.
-   The `financial_year` queryset is initially empty and dynamically updated based on the selected company using form initialization logic (for non-HTMX submissions) or via the HTMX endpoint.
-   Widgets are styled with Tailwind CSS classes.
-   A `clean` method provides validation for both fields, similar to `RequiredFieldValidator`.

```python
from django import forms
from .models import Company, FinancialYear

class FinancialYearDeleteForm(forms.Form):
    """
    Form for selecting a company and a financial year for deletion.
    Handles dynamic population of financial year choices via HTMX.
    """
    company = forms.ModelChoiceField(
        queryset=Company.objects.all().order_by('comp_name'),
        empty_label="Select Company",
        label="Company",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'hx-get': '/sysadmin/financial-years-by-company/', # HTMX endpoint to fetch financial years
            'hx-target': '#id_financial_year_container', # Target div to update
            'hx-swap': 'innerHTML', # Replace content of target div
        }),
        required=True # Replicates RequiredFieldValidator
    )
    financial_year = forms.ModelChoiceField(
        queryset=FinancialYear.objects.none(), # Initially empty, populated dynamically
        empty_label="Select Financial Year",
        label="Financial Year",
        widget=forms.Select(attrs={
            'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'size': '8', # Mimics the ASP.NET ListBox height
        }),
        required=True # Replicates RequiredFieldValidator
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # If the form is submitted or initialized with data, try to populate financial_year
        if 'company' in self.data:
            try:
                company_id = int(self.data.get('company'))
                self.fields['financial_year'].queryset = FinancialYear.objects.filter(company_id=company_id).order_by('fin_year_from')
            except (ValueError, TypeError):
                # Handle cases where company_id might be invalid
                pass

    def clean(self):
        """
        Custom validation to ensure both company and financial year are selected.
        """
        cleaned_data = super().clean()
        company = cleaned_data.get('company')
        financial_year = cleaned_data.get('financial_year')

        if not company:
            self.add_error('company', 'Company selection is required.')
        if not financial_year:
            self.add_error('financial_year', 'Financial Year selection is required.')
        
        # You could add more complex validation here if needed, e.g.,
        # ensuring the selected financial_year actually belongs to the selected company.
        # However, ModelChoiceField combined with proper queryset filtering usually handles this.

        return cleaned_data

```

### 4.3 Views (`sysadmin/views.py`)

**Task:** Implement the delete operation using CBVs and an HTMX endpoint.

**Instructions:**

-   `FinancialYearDeleteView` (a `FormView`) handles the display of the deletion form and processing its submission. It calls the model's `delete_by_company_and_id` method.
-   `FinancialYearsByCompanyHTMXView` (a `View`) provides the HTMX endpoint to dynamically load financial year options based on company selection, returning an HTML partial.
-   Views are kept thin, delegating business logic to models.
-   `messages.success` and `messages.error` are used for user feedback.
-   HTMX `HX-Trigger` headers are used to signal client-side refreshes or message display.

```python
from django.views.generic import FormView, View
from django.http import HttpResponse, HttpResponseRedirect
from django.urls import reverse_lazy
from django.contrib import messages
from django.shortcuts import render # Needed for rendering HTMX partials
from .models import FinancialYear
from .forms import FinancialYearDeleteForm

class FinancialYearDeleteView(FormView):
    """
    Handles the display and submission of the financial year deletion form.
    Utilizes a FormView to manage form rendering and processing.
    """
    template_name = 'sysadmin/financialyear/delete.html'
    form_class = FinancialYearDeleteForm
    # Redirects back to the same page to show message and reset form state
    success_url = reverse_lazy('financial_year_delete') 

    def get_context_data(self, **kwargs):
        """
        Adds any success/error messages from the Django messages framework
        or (if present) from the query string (mimicking ASP.NET behavior).
        """
        context = super().get_context_data(**kwargs)
        # For initial page load, check if there's a 'msg' in query string
        # (similar to original ASP.NET redirect behavior)
        if 'msg' in self.request.GET and not self.request.headers.get('HX-Request'):
            msg_text = self.request.GET['msg']
            messages.success(self.request, msg_text) # Adds as a success message
        return context

    def form_valid(self, form):
        """
        Handles valid form submissions. Calls the model's delete method.
        Kept thin by delegating deletion logic to the model.
        """
        company = form.cleaned_data['company']
        financial_year = form.cleaned_data['financial_year']

        success, msg = FinancialYear.delete_by_company_and_id(
            company_pk=company.pk,
            fin_year_pk=financial_year.pk
        )

        if success:
            messages.success(self.request, msg)
        else:
            messages.error(self.request, msg)

        # For HTMX requests, respond with 204 No Content and trigger client-side event.
        # This will allow Alpine.js to handle message display and clear form.
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    # Triggers a custom event on the client side to show messages
                    # and potentially reset the form for the next operation.
                    # Using a full page reload for now to simplify message display
                    # as per original ASP.NET redirect behavior.
                    'HX-Redirect': self.get_success_url() # HTMX equivalent of client-side redirect
                }
            )
        
        # For non-HTMX requests (e.g., direct browser navigation), perform a standard redirect.
        return HttpResponseRedirect(self.get_success_url())

    def form_invalid(self, form):
        """
        Handles invalid form submissions. Re-renders the form with errors.
        For HTMX requests, returns the form partial with errors.
        """
        response = super().form_invalid(form)
        if self.request.headers.get('HX-Request'):
            # If it's an HTMX request, we re-render the form content
            # including the validation errors within the modal/partial view.
            return render(self.request, self.template_name, self.get_context_data(form=form))
        return response

class FinancialYearsByCompanyHTMXView(View):
    """
    HTMX endpoint to dynamically load financial year options for the dropdown.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.GET.get('company') # Get company ID from query parameter
        financial_years = FinancialYear.objects.none() # Initialize empty queryset

        if company_id:
            try:
                # Filter financial years by the selected company and order them
                financial_years = FinancialYear.objects.filter(company_id=int(company_id)).order_by('fin_year_from')
            except ValueError:
                # Handle cases where company_id might not be a valid integer
                pass
        
        # Render only the <option> tags (or the entire <select> if preferred)
        # The partial template will handle rendering the options.
        return render(request, 'sysadmin/financialyear/_financial_year_options.html', {
            'financial_years': financial_years
        })

```

### 4.4 Templates

**Task:** Create templates for each view.

**Instructions:**

-   `delete.html` is the main page, extending `core/base.html`. It contains the form structure and Alpine.js for the confirmation modal.
-   `_financial_year_options.html` is a partial template used by HTMX to update the financial year dropdown.
-   Tailwind CSS classes are applied for styling.
-   HTMX attributes (`hx-get`, `hx-target`, `hx-swap`) enable dynamic updates.
-   Alpine.js (`x-data`, `x-show`, `x-on:click`) handles the modal's visibility.

#### `sysadmin/financialyear/delete.html`

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Financial Year - Delete</h2>
    </div>
    
    <div x-data="{ showModal: false }" class="bg-white p-6 rounded-lg shadow-xl">
        <form hx-post="{% url 'financial_year_delete' %}" hx-swap="none"
              _="on htmx:afterRequest if event.detail.xhr.status == 204 then
                   htmx.redirect(event.detail.xhr.getResponseHeader('HX-Redirect'))
                else if event.detail.xhr.status >= 400 then
                   this.outerHTML = event.detail.xhr.responseText">
            {% csrf_token %}
            
            <table class="w-full max-w-md mx-auto table-auto border-collapse">
                <thead>
                    <tr>
                        <th colspan="3" class="bg-blue-600 text-white font-semibold text-left p-3 rounded-t-lg">
                            &nbsp;<b>Financial Year - Delete</b>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b border-gray-200">
                        <td class="w-2 px-2 py-3 text-gray-700">&nbsp;</td>
                        <td class="w-1/4 px-2 py-3 text-gray-700 font-medium">Company</td>
                        <td class="w-3/4 px-2 py-3">
                            <span class="mr-2">:</span> 
                            {{ form.company }}
                            {% if form.company.errors %}
                                <p class="text-red-500 text-xs mt-1">{{ form.company.errors|join:", " }}</p>
                            {% endif %}
                        </td>
                    </tr>
                    <tr class="border-b border-gray-200">
                        <td class="w-2 px-2 py-3 text-gray-700">&nbsp;</td>
                        <td class="w-1/4 px-2 py-3 text-gray-700 font-medium align-top">Financial Year</td>
                        <td class="w-3/4 px-2 py-3">
                            <span class="mr-2">&nbsp;</span> 
                            <div id="id_financial_year_container">
                                {{ form.financial_year }}
                                {% if form.financial_year.errors %}
                                    <p class="text-red-500 text-xs mt-1">{{ form.financial_year.errors|join:", " }}</p>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" class="p-3 text-center">
                            {# Display Django messages #}
                            {% if messages %}
                                <div class="message-container mt-2">
                                    {% for message in messages %}
                                        <div class="p-2 rounded text-sm 
                                            {% if message.tags == 'success' %}bg-green-100 text-green-700 border border-green-200
                                            {% elif message.tags == 'error' %}bg-red-100 text-red-700 border border-red-200
                                            {% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-700 border border-yellow-200
                                            {% else %}bg-blue-100 text-blue-700 border border-blue-200{% endif %}">
                                            {{ message }}
                                        </div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <td colspan="3" class="p-3 text-center">
                            <button type="button" 
                                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-6 rounded-md shadow-md transition duration-200"
                                x-on:click="showModal = true">
                                Delete
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Confirmation Modal (Alpine.js) -->
            <div x-show="showModal" 
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 scale-90"
                 x-transition:enter-end="opacity-100 scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 scale-100"
                 x-transition:leave-end="opacity-0 scale-90"
                 class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50"
                 style="display: none;"> {# Added display:none for initial hide #}
                <div class="relative p-6 border w-96 max-w-md shadow-lg rounded-lg bg-white transform transition-all">
                    <h3 class="text-xl font-semibold leading-6 text-gray-900 mb-4">Confirm Deletion</h3>
                    <div class="mt-2 text-gray-700">
                        <p class="text-sm">Are you sure you want to delete the selected financial year?</p>
                    </div>
                    <div class="mt-6 flex justify-end space-x-3">
                        <button type="button" 
                                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-md transition duration-200"
                                x-on:click="showModal = false">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="bg-red-600 hover:bg-red-800 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-200"
                                x-on:click="showModal = false">
                            Confirm Delete
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js is initialized automatically by the script tag in base.html
        // No specific init needed here for this page's x-data.
    });

    // Optionally, if you wanted to clear form on HTMX success without full reload:
    // document.body.addEventListener('htmx:afterRequest', function(evt) {
    //     if (evt.detail.xhr.status === 204 && evt.detail.request.url.includes('financial-year/delete/')) {
    //         // Manually reset the form fields if not doing a full redirect
    //         const form = document.querySelector('form[hx-post]');
    //         if (form) {
    //             form.reset(); // Resets all form fields
    //             // Additionally, you might need to manually trigger hx-get on company dropdown
    //             // if its default state depends on the selected company.
    //             // For this specific case, a full redirect (HX-Redirect) is simpler.
    //         }
    //     }
    // });
</script>
{% endblock %}

```

#### `sysadmin/financialyear/_financial_year_options.html`

```html
{# This partial template is rendered by FinancialYearsByCompanyHTMXView #}
{# and swapped into the #id_financial_year_container div by HTMX. #}

<select name="financial_year" id="id_financial_year"
        class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        size="8" required>
    <option value="">Select Financial Year</option>
    {% for fy in financial_years %}
        <option value="{{ fy.pk }}">{{ fy.formatted_financial_year }}</option>
    {% empty %}
        <option value="" disabled>No financial years available for this company.</option>
    {% endfor %}
</select>
```

### 4.5 URLs (`sysadmin/urls.py`)

**Task:** Define URL patterns for the views.

**Instructions:**

-   A path for the main `FinancialYearDeleteView`.
-   A path for the HTMX endpoint `FinancialYearsByCompanyHTMXView` to fetch dynamic options.

```python
from django.urls import path
from .views import FinancialYearDeleteView, FinancialYearsByCompanyHTMXView

urlpatterns = [
    # Main URL for the financial year delete page
    path('financial-year/delete/', FinancialYearDeleteView.as_view(), name='financial_year_delete'),
    
    # HTMX endpoint to get financial years based on selected company
    path('financial-years-by-company/', FinancialYearsByCompanyHTMXView.as_view(), name='financial_years_by_company'),
]

```

### 4.6 Tests (`sysadmin/tests.py`)

**Task:** Write tests for the model and views.

**Instructions:**

-   `Company` and `FinancialYear` test data are set up.
-   Model tests verify field attributes and the `formatted_financial_year` method.
-   Integration tests cover the `FinancialYearDeleteView` (GET and POST) and the HTMX `FinancialYearsByCompanyHTMXView` endpoint, ensuring correct responses and data manipulation.
-   HTTP headers are used to simulate HTMX requests.

```python       
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import Company, FinancialYear
from datetime import date

class FinancialYearModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company1 = Company.objects.create(comp_id=1, comp_name='Company A')
        cls.company2 = Company.objects.create(comp_id=2, comp_name='Company B')
        cls.fy1_comp1 = FinancialYear.objects.create(
            fin_year_id=101, company=cls.company1,
            fin_year_from=date(2023, 1, 1), fin_year_to=date(2023, 12, 31)
        )
        cls.fy2_comp1 = FinancialYear.objects.create(
            fin_year_id=102, company=cls.company1,
            fin_year_from=date(2024, 1, 1), fin_year_to=date(2024, 12, 31)
        )
        cls.fy1_comp2 = FinancialYear.objects.create(
            fin_year_id=201, company=cls.company2,
            fin_year_from=date(2023, 7, 1), fin_year_to=date(2024, 6, 30)
        )
  
    def test_company_creation(self):
        self.assertEqual(self.company1.comp_name, 'Company A')
        self.assertEqual(self.company1.comp_id, 1)

    def test_financial_year_creation(self):
        self.assertEqual(self.fy1_comp1.company, self.company1)
        self.assertEqual(self.fy1_comp1.fin_year_id, 101)
        self.assertEqual(self.fy1_comp1.fin_year_from, date(2023, 1, 1))
        self.assertEqual(self.fy1_comp1.fin_year_to, date(2023, 12, 31))
        
    def test_financial_year_formatted_string(self):
        self.assertEqual(self.fy1_comp1.formatted_financial_year(), '2023-2023')
        self.assertEqual(self.fy1_comp2.formatted_financial_year(), '2023-2024')

    def test_financial_year_str_method(self):
        self.assertEqual(str(self.fy1_comp1), '2023-2023')

    def test_delete_financial_year_success(self):
        # Verify initial count
        self.assertEqual(FinancialYear.objects.count(), 3)
        # Attempt deletion
        success, msg = FinancialYear.delete_by_company_and_id(
            company_pk=self.company1.comp_id, fin_year_pk=self.fy1_comp1.fin_year_id
        )
        self.assertTrue(success)
        self.assertEqual(msg, "Financial year deleted successfully.")
        # Verify object is deleted
        self.assertEqual(FinancialYear.objects.count(), 2)
        self.assertFalse(FinancialYear.objects.filter(fin_year_id=self.fy1_comp1.fin_year_id).exists())

    def test_delete_financial_year_not_found(self):
        # Attempt to delete a non-existent financial year
        success, msg = FinancialYear.delete_by_company_and_id(
            company_pk=self.company1.comp_id, fin_year_pk=999
        )
        self.assertFalse(success)
        self.assertIn("not found", msg)
        self.assertEqual(FinancialYear.objects.count(), 3) # No change

    def test_delete_financial_year_wrong_company(self):
        # Attempt to delete a financial year with wrong company ID
        success, msg = FinancialYear.delete_by_company_and_id(
            company_pk=self.company2.comp_id, fin_year_pk=self.fy1_comp1.fin_year_id
        )
        self.assertFalse(success)
        self.assertIn("not found", msg)
        self.assertEqual(FinancialYear.objects.count(), 3) # No change


class FinancialYearViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for all tests in this class
        cls.company1 = Company.objects.create(comp_id=1, comp_name='Company Alpha')
        cls.company2 = Company.objects.create(comp_id=2, comp_name='Company Beta')
        cls.fy1_comp1 = FinancialYear.objects.create(
            fin_year_id=1, company=cls.company1,
            fin_year_from=date(2022, 1, 1), fin_year_to=date(2022, 12, 31)
        )
        cls.fy2_comp1 = FinancialYear.objects.create(
            fin_year_id=2, company=cls.company1,
            fin_year_from=date(2023, 1, 1), fin_year_to=date(2023, 12, 31)
        )
        cls.fy1_comp2 = FinancialYear.objects.create(
            fin_year_id=3, company=cls.company2,
            fin_year_from=date(2022, 7, 1), fin_year_to=date(2023, 6, 30)
        )
    
    def setUp(self):
        self.client = Client()
    
    def test_delete_view_get(self):
        """Test that the delete form page loads correctly."""
        response = self.client.get(reverse('financial_year_delete'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/financialyear/delete.html')
        self.assertContains(response, 'Financial Year - Delete')
        self.assertContains(response, '<select name="company"') # Check for company dropdown
        self.assertContains(response, '<select name="financial_year"') # Check for financial year dropdown

    def test_delete_view_post_success(self):
        """Test successful deletion via POST request."""
        initial_count = FinancialYear.objects.count()
        response = self.client.post(reverse('financial_year_delete'), {
            'company': self.company1.comp_id,
            'financial_year': self.fy1_comp1.fin_year_id
        }, follow=True) # follow=True to follow redirect

        self.assertEqual(response.status_code, 200) # After redirect
        self.assertTemplateUsed(response, 'sysadmin/financialyear/delete.html')
        self.assertEqual(FinancialYear.objects.count(), initial_count - 1)
        self.assertFalse(FinancialYear.objects.filter(fin_year_id=self.fy1_comp1.fin_year_id).exists())
        
        # Check success message
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), "Financial year deleted successfully.")
        self.assertEqual(messages[0].tags, "success")


    def test_delete_view_post_invalid_form(self):
        """Test form submission with missing required fields."""
        initial_count = FinancialYear.objects.count()
        response = self.client.post(reverse('financial_year_delete'), {
            'company': '', # Missing company
            'financial_year': self.fy1_comp1.fin_year_id
        })
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sysadmin/financialyear/delete.html')
        self.assertContains(response, 'Company selection is required.')
        self.assertEqual(FinancialYear.objects.count(), initial_count) # No deletion

    def test_delete_view_post_financial_year_not_found(self):
        """Test form submission where financial year does not exist for company."""
        initial_count = FinancialYear.objects.count()
        response = self.client.post(reverse('financial_year_delete'), {
            'company': self.company1.comp_id,
            'financial_year': 9999 # Non-existent financial year ID
        }, follow=True)

        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/financialyear/delete.html')
        self.assertEqual(FinancialYear.objects.count(), initial_count) # No deletion

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn("not found", str(messages[0]))
        self.assertEqual(messages[0].tags, "error")

    def test_financial_years_by_company_htmx_view(self):
        """Test the HTMX endpoint for fetching financial years."""
        response = self.client.get(reverse('financial_years_by_company'), {'company': self.company1.comp_id})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/financialyear/_financial_year_options.html')
        
        # Check if the correct financial years are in the response
        self.assertContains(response, f'<option value="{self.fy1_comp1.fin_year_id}">{self.fy1_comp1.formatted_financial_year()}</option>')
        self.assertContains(response, f'<option value="{self.fy2_comp1.fin_year_id}">{self.fy2_comp1.formatted_financial_year()}</option>')
        self.assertNotContains(response, f'<option value="{self.fy1_comp2.fin_year_id}">') # Should not contain company B's FY

    def test_financial_years_by_company_htmx_view_no_company_selected(self):
        """Test HTMX endpoint when no company is selected."""
        response = self.client.get(reverse('financial_years_by_company'), {'company': ''})
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sysadmin/financialyear/_financial_year_options.html')
        self.assertContains(response, '<option value="">Select Financial Year</option>')
        self.assertNotContains(response, '<option value="') # No actual financial years

    def test_delete_view_htmx_post_success(self):
        """Test successful HTMX deletion, checks for 204 response and HX-Redirect header."""
        initial_count = FinancialYear.objects.count()
        response = self.client.post(reverse('financial_year_delete'), {
            'company': self.company1.comp_id,
            'financial_year': self.fy1_comp1.fin_year_id
        }, HTTP_HX_REQUEST='true') # Simulate HTMX request

        self.assertEqual(response.status_code, 204) # No Content expected for HTMX success
        self.assertEqual(response.headers['HX-Redirect'], reverse('financial_year_delete'))
        self.assertEqual(FinancialYear.objects.count(), initial_count - 1)
        self.assertFalse(FinancialYear.objects.filter(fin_year_id=self.fy1_comp1.fin_year_id).exists())
        # Messages would be retrieved on the subsequent HX-Redirect in a real browser

    def test_delete_view_htmx_post_invalid_form(self):
        """Test HTMX submission with invalid form data, expects 200 and re-rendered form with errors."""
        initial_count = FinancialYear.objects.count()
        response = self.client.post(reverse('financial_year_delete'), {
            'company': '', 
            'financial_year': self.fy1_comp1.fin_year_id
        }, HTTP_HX_REQUEST='true')

        self.assertEqual(response.status_code, 200) # Should return the form content with errors
        self.assertTemplateUsed(response, 'sysadmin/financialyear/delete.html')
        self.assertContains(response, 'Company selection is required.')
        self.assertEqual(FinancialYear.objects.count(), initial_count) # No deletion

```

## Step 5: HTMX and Alpine.js Integration

**Instructions:**

-   **Company Dropdown:** The `<select name="company">` element in `delete.html` has `hx-get="/sysadmin/financial-years-by-company/"`, `hx-target="#id_financial_year_container"`, and `hx-swap="innerHTML"`. This means when a company is selected, an HTMX GET request is sent to the `/sysadmin/financial-years-by-company/` endpoint, and the response (the HTML options from `_financial_year_options.html`) replaces the content within the `id_financial_year_container` div, which holds the financial year `<select>` element.
-   **Delete Form Submission:** The `<form>` tag uses `hx-post="{% url 'financial_year_delete' %}"` and `hx-swap="none"`. The `hx-swap="none"` indicates that HTMX itself won't swap anything after a successful 204 No Content response. Instead, a custom `HX-Redirect` header is sent, which HTMX on the client-side will interpret as a full page navigation, mimicking the original ASP.NET redirect. This ensures Django's `messages` framework can display feedback on the reloaded page.
-   **Confirmation Modal:** An Alpine.js `x-data="{ showModal: false }"` block is used on the main container. The "Delete" button has `x-on:click="showModal = true"` to reveal the modal. Inside the modal, "Cancel" has `x-on:click="showModal = false"`, and "Confirm Delete" also sets `showModal = false` before allowing the form to submit. This provides the client-side confirmation without requiring additional JavaScript beyond Alpine.js.
-   **DataTables:** While DataTables is a core recommendation, this specific ASP.NET page is a "delete form" with dropdowns, not a list display. Therefore, DataTables is not directly applied here, as it's not a list view. If this were a general financial year management page with a list, DataTables would be utilized on that list.
-   **No Custom JavaScript:** All dynamic interactions are handled by HTMX attributes and Alpine.js directives directly in the HTML, eliminating the need for separate, complex JavaScript files.

## Final Notes

-   Placeholders like `[MODEL_NAME]`, `[FIELD1]` etc., have been replaced with concrete names (`FinancialYear`, `company`, `financial_year`, `fin_year_from`, etc.) derived from the ASP.NET code.
-   Templates are kept DRY by using partial templates (`_financial_year_options.html`) for reusable components.
-   Business logic for formatting financial years and the specific deletion process is encapsulated within the `FinancialYear` model, ensuring thin views.
-   Comprehensive unit and integration tests are provided to cover all functionality and interactions.
-   All HTMX and Alpine.js interactions are configured to work seamlessly, providing a modern, interactive user experience without full page reloads for dynamic content.
-   Tailwind CSS classes are applied for a clean, modern UI.