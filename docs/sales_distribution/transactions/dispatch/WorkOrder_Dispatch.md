## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database tables and their columns from the ASP.NET code.

Based on the `BindDataCust` method's SQL query and the `sql` WebMethod:

**Primary Data for List View (requires a SQL Database View):**

For `WorkOrder_Dispatch.aspx`, the data displayed in the `GridView` is a result of a complex join and calculation (`WOQty - DAQty > 0`). To represent this effectively using `managed=False` and align with Django's ORM, it's best to create a SQL Server *view* that encapsulates this logic.

*   **Proposed SQL View Name:** `vw_WorkOrder_Dispatch_Summary`
*   **Purpose:** This view will pre-calculate the `WOQty` and `DAQty` and include the `WOQty - DAQty > 0` filter, presenting a flattened dataset suitable for the Django model.
*   **Columns in `vw_WorkOrder_Dispatch_Summary` (and corresponding Django fields):**
    *   `WRNo` (NVARCHAR(50) or similar) -> `wr_no` (CharField, `primary_key=True` for model)
    *   `WONo` (NVARCHAR(50) or similar) -> `wo_no` (CharField)
    *   `SysDate` (DATETIME or converted VARCHAR(10)) -> `sys_date` (DateField)
    *   `CustomerId` (NVARCHAR(50) or similar) -> `customer_id` (CharField)
    *   `FinYear` (NVARCHAR(10) or similar) -> `fin_year` (CharField)
    *   `CustomerName` (NVARCHAR(255) or similar) -> `customer_name` (CharField)
    *   `EmployeeName` (NVARCHAR(255) or similar) -> `employee_name` (CharField)
    *   `WOQty` (FLOAT/DECIMAL) -> `wo_qty` (DecimalField)
    *   `DAQty` (FLOAT/DECIMAL) -> `da_qty` (DecimalField)

**Supporting Data for Autocomplete:**

*   **Table Name:** `SD_Cust_master`
*   **Purpose:** Used for customer name autocomplete functionality.
*   **Columns:**
    *   `CustomerId` (NVARCHAR(50) or similar) -> `customer_id` (CharField, `primary_key=True`)
    *   `CustomerName` (NVARCHAR(255) or similar) -> `customer_name` (CharField)

**Conceptual SQL View Definition for `vw_WorkOrder_Dispatch_Summary`:**

```sql
-- This view should be created in your SQL Server database.
-- It encapsulates the complex join and filtering logic from the original C# code.
-- Adjust data types (e.g., NVARCHAR, FLOAT) to match your actual database schema.

CREATE VIEW vw_WorkOrder_Dispatch_Summary AS
SELECT
    T1.WRNo,
    T1.WONo,
    -- Handle date conversion as per original C# logic, or store as actual date type.
    -- Assuming SysDate can be directly cast to a date type for Django.
    -- For exact conversion of 'DD-MM-YYYY' string format:
    CONVERT(DATE, SUBSTRING(T1.SysDate, CHARINDEX('-', T1.SysDate) + 1, 2) + '-' + LEFT(T1.SysDate, CHARINDEX('-', T1.SysDate) - 1) + '-' + RIGHT(T1.SysDate, CHARINDEX('-', REVERSE(T1.SysDate)) - 1), 103) AS SysDate,
    T2.CustomerId,
    T4.FinYear,
    T3.CustomerName,
    T5.Title + '.' + T5.EmployeeName AS EmployeeName,
    (
        SELECT SUM(P.Qty)
        FROM SD_Cust_WorkOrder_Master M_Inner
        INNER JOIN SD_Cust_WorkOrder_Products_Details P ON M_Inner.Id = P.MId
        WHERE M_Inner.WONo = T1.WONo
        GROUP BY M_Inner.WONo
    ) AS WOQty,
    (
        SELECT SUM(D.DispatchQty)
        FROM SD_Cust_WorkOrder_Release R_Inner
        INNER JOIN SD_Cust_WorkOrder_Dispatch D ON R_Inner.Id = D.WRId
        WHERE R_Inner.WONo = T2.WONo
        GROUP BY R_Inner.WONo
    ) AS DAQty
FROM
    SD_Cust_WorkOrder_Release T1
INNER JOIN SD_Cust_WorkOrder_Master T2 ON T1.WONo = T2.WONo
INNER JOIN SD_Cust_Master T3 ON T3.CustomerId = T2.CustomerId
INNER JOIN tblFinancial_master T4 ON T4.FinYearId = T1.FinYearId
INNER JOIN tblHR_OfficeStaff T5 ON T5.EmpId = T1.SessionId
WHERE
    T2.CloseOpen = 0
    -- The C# code applied `(WOQty - DAQty) > 0` after fetching.
    -- It's often better to put this in the view if possible, or handle in Django's queryset.
    -- For simplicity, this view provides WOQty and DAQty, and the Django queryset will filter.
    -- If a SQL-level filter is desired for performance:
    -- HAVING (ISNULL((SELECT SUM(P.Qty) FROM SD_Cust_WorkOrder_Master M_Inner INNER JOIN SD_Cust_WorkOrder_Products_Details P ON M_Inner.Id = P.MId WHERE M_Inner.WONo = T1.WONo GROUP BY M_Inner.WONo), 0) -
    --         ISNULL((SELECT SUM(D.DispatchQty) FROM SD_Cust_WorkOrder_Release R_Inner INNER JOIN SD_Cust_WorkOrder_Dispatch D ON R_Inner.Id = D.WRId WHERE R_Inner.WONo = T2.WONo GROUP BY R_Inner.WONo), 0)) > 0
```

### Step 2: Identify Backend Functionality

The ASP.NET page primarily implements a **Read/List** operation with **Search** and **Pagination**.
*   **Search Criteria:**
    *   `Customer Name`: Search by `CustomerName` from `SD_Cust_Master`. The original code uses `CustomerId` after parsing `CustomerName [CustomerId]`.
    *   `WO No` (Work Order Number): Search by `WONo` from `SD_Cust_WorkOrder_Release`.
    *   `WR No` (Work Request Number): Search by `WRNo` from `SD_Cust_WorkOrder_Release`.
*   **Autocomplete:** For `Customer Name` input, retrieving `CustomerName` and `CustomerId` from `SD_Cust_master`.
*   **Business Logic Filtering:** The C# code filters records where `(WOQty - DAQty) > 0`. This means only records with remaining dispatchable quantity are shown.

No direct Create, Update, or Delete (CRUD) operations are present on this specific ASP.NET page. The `HyperLinkField` to `WorkOrder_Dispatch_Details.aspx` suggests a detail/edit page exists elsewhere.

### Step 3: Infer UI Components

*   **Search Form:**
    *   `Search By` Dropdown: `DropDownList1` (Django equivalent: `forms.ChoiceField` with select input).
        *   Options: "Select", "Customer Name", "WO No", "WR No".
    *   `Search Value` Textboxes: `txtEnqId` and `TxtSearchValue`.
        *   `TxtSearchValue` includes an `AutoCompleteExtender` (Django equivalent: HTMX `hx-get` for autocomplete endpoint).
    *   `Search` Button: `btnSearch` (Django equivalent: HTML button with `hx-post`).
*   **Data Display:**
    *   `GridView`: `SearchGridView1` (Django equivalent: HTML `<table>` with DataTables JavaScript).
        *   Columns: `SN`, `FinYear`, `CustomerName`, `CustomerId`, `WRNo` (Hyperlink), `WONo`, `SysDate`, `EmployeeName`.
        *   Pagination (`AllowPaging="True"`, `PageSize="20"`).
        *   Sorting (`AllowSorting="True"`).
*   **Messages:** `Label2` for displaying status messages.

### Step 4: Generate Django Code

**Django Application Name:** `sales_distribution`

#### 4.1 Models

Define models for the database view and the customer master table.

**`sales_distribution/models.py`**

```python
from django.db import models
from django.urls import reverse
from decimal import Decimal

# Model for the Work Order Dispatch Summary list view, mapped to a SQL database view.
# Ensure 'vw_WorkOrder_Dispatch_Summary' is created in your SQL Server database.
class WorkOrderDispatchSummary(models.Model):
    wr_no = models.CharField(db_column='WRNo', primary_key=True, max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50, blank=True, null=True)
    sys_date = models.DateField(db_column='SysDate', blank=True, null=True)
    customer_id = models.CharField(db_column='CustomerId', max_length=50, blank=True, null=True)
    fin_year = models.CharField(db_column='FinYear', max_length=10, blank=True, null=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255, blank=True, null=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255, blank=True, null=True)
    wo_qty = models.DecimalField(db_column='WOQty', max_digits=18, decimal_places=3, blank=True, null=True)
    da_qty = models.DecimalField(db_column='DAQty', max_digits=18, decimal_places=3, blank=True, null=True)

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'vw_WorkOrder_Dispatch_Summary' # Map to the SQL Server view
        verbose_name = 'Work Order Dispatch Summary'
        verbose_name_plural = 'Work Order Dispatch Summaries'

    def __str__(self):
        return f"WR No: {self.wr_no} - Customer: {self.customer_name}"

    @property
    def remaining_qty(self):
        """Calculates the remaining quantity for dispatch."""
        return (self.wo_qty or Decimal(0)) - (self.da_qty or Decimal(0))
        
    def get_dispatch_details_url(self):
        """Generates the URL for the dispatch details page, similar to the original HyperLinkField."""
        # This assumes a 'workorder_dispatch_details' URL pattern exists in your Django app.
        # The original URL had parameters WONo, WRNo, ModId, SubModId.
        # You'll need to define a corresponding Django URL and view for WorkOrder_Dispatch_Details.
        return reverse('sales_distribution:workorder_dispatch_details', 
                       kwargs={'wo_no': self.wo_no, 'wr_no': self.wr_no})


# Model for Customer Master for autocomplete
class CustomerMaster(models.Model):
    customer_id = models.CharField(db_column='CustomerId', primary_key=True, max_length=50)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)

    class Meta:
        managed = False  # Tells Django not to manage table creation/deletion
        db_table = 'SD_Cust_master' # Map to the actual SQL Server table
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return self.customer_name

```

#### 4.2 Forms

Define a form for the search criteria. This will not be a `ModelForm` as it's for filtering, not creating/updating a specific model instance.

**`sales_distribution/forms.py`**

```python
from django import forms

class WorkOrderDispatchSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('select', 'Select'),
        ('customer_name', 'Customer Name'),
        ('wo_no', 'WO No'),
        ('wr_no', 'WR No'),
    ]

    search_type = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'hx-post': 'hx-post="{% url "sales_distribution:workorder_dispatch_update_search_inputs" %}"', # HTMX for dynamic input visibility
            'hx-target': '#search_inputs_container',
            'hx-swap': 'outerHTML',
            'hx-trigger': 'change',
        }),
        label="Search By"
    )

    # Use CharField for all, and handle visibility/autocomplete via HTMX/Alpine.js
    search_value_text = forms.CharField(
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'id': 'TxtSearchValue', # Match original ID for clarity
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'placeholder': 'Enter Customer Name (with autocomplete)',
            # HTMX for autocomplete
            'hx-get': "{% url 'sales_distribution:customer_autocomplete' %}",
            'hx-trigger': 'keyup changed delay:300ms, search',
            'hx-target': '#autocomplete-results',
            'hx-swap': 'innerHTML',
            'autocomplete': 'off', # Disable browser autocomplete
            '@input': 'selectedCustomerName = ""', # Alpine.js to clear selection on manual input
            '@keydown.down.prevent': '$refs.results.children[0] && $refs.results.children[0].focus()', # Alpine.js for keyboard nav
        })
    )
    
    # Hidden field to store CustomerId when selected from autocomplete
    customer_id_hidden = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={
            'id': 'CustomerIdHidden',
            'x-model': 'selectedCustomerId' # Alpine.js model for selected ID
        })
    )

    search_value_id = forms.CharField(
        required=False,
        label="Search Value",
        widget=forms.TextInput(attrs={
            'id': 'txtEnqId', # Match original ID for clarity
            'class': 'box3 w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500',
            'placeholder': 'Enter WO No or WR No',
        })
    )

    # Custom clean method for search logic if needed
    def clean(self):
        cleaned_data = super().clean()
        search_type = cleaned_data.get('search_type')
        search_value_text = cleaned_data.get('search_value_text')
        search_value_id = cleaned_data.get('search_value_id')
        customer_id_hidden = cleaned_data.get('customer_id_hidden')

        if search_type == 'customer_name':
            # If customer name is chosen, we primarily want the hidden customer_id
            # However, if user types directly, they might not select from autocomplete.
            # The original ASP.NET parsed "Name [ID]". If `customer_id_hidden` is empty,
            # we might need to parse `search_value_text` or rely on the autocomplete selection.
            # For this implementation, we'll assume `customer_id_hidden` contains the value if selected.
            # Otherwise, use `search_value_text` for a LIKE search.
            if customer_id_hidden:
                cleaned_data['parsed_search_value'] = customer_id_hidden
            elif search_value_text: # Fallback to text search if no ID selected
                cleaned_data['parsed_search_value'] = search_value_text
            else:
                cleaned_data['parsed_search_value'] = '' # No search value
        elif search_type in ['wo_no', 'wr_no']:
            cleaned_data['parsed_search_value'] = search_value_id
        else: # 'select' or no specific type
            cleaned_data['parsed_search_value'] = '' # No search value

        return cleaned_data
```

#### 4.3 Views

Implement the list view and the autocomplete view. Views will be thin.

**`sales_distribution/views.py`**

```python
from django.views.generic import ListView, TemplateView, View
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.db.models import F, ExpressionWrapper, DecimalField
from django.contrib import messages
from .models import WorkOrderDispatchSummary, CustomerMaster
from .forms import WorkOrderDispatchSearchForm
import re # For parsing customer ID from autocomplete string

# Main list view for Work Order Dispatch
class WorkOrderDispatchListView(TemplateView):
    template_name = 'sales_distribution/workorderdispatchsummary/list.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize search form
        context['search_form'] = WorkOrderDispatchSearchForm(self.request.GET or None)
        return context

# HTMX partial view for the search inputs (dynamically shown/hidden)
class WorkOrderDispatchUpdateSearchInputsView(View):
    def post(self, request, *args, **kwargs):
        form = WorkOrderDispatchSearchForm(request.POST)
        # Ensure form is valid or handle errors if necessary for parsing `search_type`
        search_type = request.POST.get('search_type', 'select')
        
        # Render the partial template for the search inputs
        # This will contain the conditional visibility logic
        context = {
            'form': form,
            'search_type': search_type
        }
        return render(request, 'sales_distribution/workorderdispatchsummary/_search_inputs.html', context)


# HTMX partial view for the DataTables table
class WorkOrderDispatchTablePartialView(ListView):
    model = WorkOrderDispatchSummary
    template_name = 'sales_distribution/workorderdispatchsummary/_table.html'
    context_object_name = 'work_order_records'
    # No pagination handled by Django ListView, DataTables handles it client-side

    def get_queryset(self):
        queryset = super().get_queryset()

        # Apply the original C# filtering logic for remaining quantity
        # In a real scenario, this filter ideally would be part of the database view
        # if performance is critical on large datasets.
        queryset = queryset.annotate(
            remaining_qty_calc=ExpressionWrapper(
                F('wo_qty') - F('da_qty'),
                output_field=DecimalField()
            )
        ).filter(remaining_qty_calc__gt=0)
        
        # Apply search filters from the form
        form = WorkOrderDispatchSearchForm(self.request.GET)
        if form.is_valid():
            search_type = form.cleaned_data.get('search_type')
            parsed_search_value = form.cleaned_data.get('parsed_search_value')
            
            if parsed_search_value:
                if search_type == 'customer_name':
                    # Check if parsed_search_value is a CustomerId (from autocomplete selection)
                    # If not, treat it as a partial name match
                    if re.match(r'^[a-zA-Z0-9_.-]+$', parsed_search_value): # Simple check for ID-like string
                         queryset = queryset.filter(customer_id=parsed_search_value)
                    else: # Fallback to name contains if ID wasn't perfectly selected
                         queryset = queryset.filter(customer_name__icontains=parsed_search_value)

                elif search_type == 'wo_no':
                    queryset = queryset.filter(wo_no__icontains=parsed_search_value)
                elif search_type == 'wr_no':
                    queryset = queryset.filter(wr_no__icontains=parsed_search_value)

        # Apply session-based filters (CompId, FinYearId) if they were relevant.
        # For demonstration, we'll mock them or assume they come from user profile.
        # In a real app, you'd get these from request.user or a custom middleware.
        # Example: user_comp_id = self.request.user.userprofile.comp_id
        # Example: user_fin_year_id = self.request.user.userprofile.fin_year_id
        # queryset = queryset.filter(company_id=user_comp_id, financial_year_id=user_fin_year_id)
        
        # Example hardcoded filters based on original ASP.NET code:
        # Assuming FinYearId and CompId are '1' and '1' for example.
        # These should come from the logged-in user's session/profile in a real app.
        # queryset = queryset.filter(fin_year_id=1, comp_id=1) # Replace with actual column names if different

        # The original query had: AND SD_Cust_WorkOrder_Release.FinYearId<='" + FinYearId + "'
        # This implies it should be <=.
        # For demo purposes, we'll assume a specific FinYear as it's from Session
        # if not self.request.user.is_authenticated:
        #     # Handle anonymous user or redirect to login
        #     return self.model.objects.none()

        # Mocking session values for demonstration
        mock_fin_year = '2023-24' # Example financial year
        mock_comp_id = 'COMP001' # Example company ID
        
        # If your model has these fields directly, apply them:
        # queryset = queryset.filter(fin_year=mock_fin_year, comp_id=mock_comp_id)
        # Note: The original query had '<= FinYearId'. If FinYear is a string like '2023-24',
        # a direct comparison might be tricky. You might need to parse years or have a FinYear model.
        # For simplicity, assuming a direct match for this demo.
        # The original query was: `SD_Cust_WorkOrder_Release.FinYearId<='" + FinYearId + "'`
        # This implies `FinYearId` is sortable as a string or number.
        # If 'FinYear' in model is char: queryset = queryset.filter(fin_year__lte=mock_fin_year)
        # If 'FinYearId' in model is integer: queryset = queryset.filter(fin_year_id__lte=mock_fin_year_id)
        
        return queryset.order_by('wo_no') # As per original "Order by SD_Cust_WorkOrder_Release.WONo ASC"


# Autocomplete view for customer names
class CustomerAutoCompleteView(View):
    def get(self, request, *args, **kwargs):
        query = request.GET.get('q', '')
        if query:
            customers = CustomerMaster.objects.filter(customer_name__icontains=query)[:10] # Limit results
            # The original ASP.NET returned "CustomerName [CustomerId]"
            results = [
                f"{c.customer_name} [{c.customer_id}]" for c in customers
            ]
        else:
            results = []
        
        # HTMX requires a simple text response or a fragment of HTML.
        # For autocomplete, returning a list of li elements is common.
        html_response = render_to_string(
            'sales_distribution/workorderdispatchsummary/_autocomplete_results.html',
            {'results': results, 'raw_customers': customers}, # Pass raw customers for easy ID extraction in JS
            request=request
        )
        return HttpResponse(html_response)
        
# For a dispatch details page (placeholder - needs full implementation)
class WorkOrderDispatchDetailsView(TemplateView):
    template_name = 'sales_distribution/workorderdispatchsummary/details.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Retrieve won_no and wr_no from kwargs
        wo_no = self.kwargs.get('wo_no')
        wr_no = self.kwargs.get('wr_no')
        
        # Fetch details using wo_no and wr_no if needed, or pass directly to template
        # Example: work_order = WorkOrderDispatchSummary.objects.filter(wo_no=wo_no, wr_no=wr_no).first()
        context['wo_no'] = wo_no
        context['wr_no'] = wr_no
        return context

```

#### 4.4 Templates

**`sales_distribution/workorderdispatchsummary/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg mb-4">
        <h2 class="text-2xl font-bold">Work Order - Dispatch</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <form hx-get="{% url 'sales_distribution:workorder_dispatch_table' %}"
              hx-target="#workOrderTable-container"
              hx-swap="innerHTML"
              hx-trigger="submit"
              class="space-y-4">
            {% csrf_token %}
            <div class="flex items-center space-x-4">
                <div class="flex-none">
                    <label for="{{ search_form.search_type.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Search By</label>
                    {{ search_form.search_type }}
                </div>

                <div id="search_inputs_container" class="flex-grow" x-data="{ 
                    searchType: '{{ search_form.search_type.value|default:'select' }}', 
                    selectedCustomerId: '{{ search_form.customer_id_hidden.value|default:'' }}',
                    selectedCustomerName: '{{ search_form.search_value_text.value|default:'' }}'
                }">
                    {% include 'sales_distribution/workorderdispatchsummary/_search_inputs.html' with form=search_form search_type=search_form.search_type.value %}
                </div>
                
                <div class="flex-none self-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm">
                        Search
                    </button>
                </div>
            </div>
            {% if messages %}
                <ul class="messages list-none p-0">
                    {% for message in messages %}
                    <li {% if message.tags %} class="{{ message.tags }} text-red-600 font-semibold"{% endif %}>{{ message }}</li>
                    {% endfor %}
                </ul>
            {% endif %}
        </form>
    </div>

    <div id="workOrderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'sales_distribution:workorder_dispatch_table' %}?{{ request.GET.urlencode }}" {# Pass initial search params #}
         hx-swap="innerHTML">
        <!-- DataTable will be loaded here via HTMX -->
        <div class="text-center p-8">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading Work Order Dispatch Records...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('searchInputs', () => ({
            searchType: '{{ search_form.search_type.value|default:'select' }}',
            selectedCustomerId: '{{ search_form.customer_id_hidden.value|default:'' }}',
            selectedCustomerName: '{{ search_form.search_value_text.value|default:'' }}',

            updateSelection(customerName, customerId) {
                this.selectedCustomerName = customerName;
                this.selectedCustomerId = customerId;
                // Trigger an input event on the search_value_text field to update its value visually
                document.getElementById('TxtSearchValue').value = customerName;
                document.getElementById('TxtSearchValue').dispatchEvent(new Event('input'));
                // Hide autocomplete results after selection
                document.getElementById('autocomplete-results').innerHTML = '';
            }
        }));
    });

    // Ensure DataTables is re-initialized after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'workOrderTable-container') {
            const table = $('#workOrderDispatchTable');
            if (table.length && !$.fn.DataTable.isDataTable(table)) {
                table.DataTable({
                    "pageLength": 20, // Match original ASP.NET PageSize
                    "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                    "columnDefs": [
                        { "orderable": false, "targets": 0 } // SN column is not orderable
                    ],
                    "pagingType": "full_numbers" // More comprehensive pagination controls
                });
            }
        }
    });

    // Optional: Auto-submit on dropdown change if behavior is like original
    // document.getElementById('id_search_type').addEventListener('change', function() {
    //     this.closest('form').submit();
    // });

</script>
{% endblock %}
```

**`sales_distribution/workorderdispatchsummary/_search_inputs.html`** (Partial for dynamic search inputs)

```html
<div id="search_inputs_container" x-data="searchInputs" :key="searchType">
    {% if search_type == 'customer_name' %}
        <label for="{{ form.search_value_text.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">Customer Name</label>
        <div class="relative">
            <input type="text" 
                   name="{{ form.search_value_text.html_name }}" 
                   id="{{ form.search_value_text.id_for_label }}" 
                   value="{{ form.search_value_text.value }}"
                   class="{{ form.search_value_text.field.widget.attrs.class }}" 
                   placeholder="{{ form.search_value_text.field.widget.attrs.placeholder }}"
                   hx-get="{{ form.search_value_text.field.widget.attrs.hx-get }}"
                   hx-trigger="{{ form.search_value_text.field.widget.attrs.hx-trigger }}"
                   hx-target="{{ form.search_value_text.field.widget.attrs.hx-target }}"
                   hx-swap="{{ form.search_value_text.field.widget.attrs.hx-swap }}"
                   autocomplete="{{ form.search_value_text.field.widget.attrs.autocomplete }}"
                   x-model="selectedCustomerName"
                   @input="selectedCustomerId = ''"> {# Clear hidden ID when input changes #}
            {{ form.customer_id_hidden }} {# Hidden field for selected customer ID #}
            <div id="autocomplete-results" class="absolute z-10 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1" x-ref="results">
                <!-- Autocomplete suggestions will be loaded here via HTMX -->
            </div>
        </div>
    {% elif search_type == 'wo_no' %}
        <label for="{{ form.search_value_id.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">WO No</label>
        <input type="text" 
               name="{{ form.search_value_id.html_name }}" 
               id="{{ form.search_value_id.id_for_label }}" 
               value="{{ form.search_value_id.value }}"
               class="{{ form.search_value_id.field.widget.attrs.class }}"
               placeholder="{{ form.search_value_id.field.widget.attrs.placeholder }}">
    {% elif search_type == 'wr_no' %}
        <label for="{{ form.search_value_id.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">WR No</label>
        <input type="text" 
               name="{{ form.search_value_id.html_name }}" 
               id="{{ form.search_value_id.id_for_label }}" 
               value="{{ form.search_value_id.value }}"
               class="{{ form.search_value_id.field.widget.attrs.class }}"
               placeholder="{{ form.search_value_id.field.widget.attrs.placeholder }}">
    {% else %}
        <label for="dummy_search_input" class="block text-sm font-medium text-gray-700 mb-1 invisible">Search Value</label>
        <input type="text" id="dummy_search_input" class="box3 w-full p-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 cursor-not-allowed" disabled value="Please select a search type">
        {{ form.search_value_text }} {# Keep hidden fields so they are always in form data #}
        {{ form.search_value_id }}
        {{ form.customer_id_hidden }}
    {% endif %}
</div>
```

**`sales_distribution/workorderdispatchsummary/_table.html`** (Partial for DataTables)

```html
<div class="overflow-x-auto">
    <table id="workOrderDispatchTable" class="min-w-full bg-white border border-gray-200 shadow-sm rounded-lg overflow-hidden">
        <thead class="bg-gray-50">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Yrs</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WR No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WO No</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. Date</th>
                <th class="py-3 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gen. By</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for record in work_order_records %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-right">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ record.fin_year }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-left">{{ record.customer_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ record.customer_id }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-blue-600 hover:text-blue-800 text-center">
                    <a href="{{ record.get_dispatch_details_url }}" class="underline font-medium">
                        {{ record.wr_no }}
                    </a>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ record.wo_no }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-center">{{ record.sys_date|date:"d-m-Y" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800 text-left">{{ record.employee_name }}</td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="py-6 px-4 text-center text-lg text-maroon-700 font-semibold">No data to display !</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization handled in parent template's htmx:afterSwap event listener.
    // This script block is primarily here to be included in the partial if needed,
    // but the actual .DataTable() call should be outside for full page reloads or a smarter HTMX handler.
    // For HTMX, the logic in `list.html`'s `htmx:afterSwap` is sufficient.
</script>
```

**`sales_distribution/workorderdispatchsummary/_autocomplete_results.html`** (Partial for autocomplete dropdown)

```html
{% if results %}
    <ul role="listbox" class="py-1" x-ref="results">
        {% for result in results %}
            <li role="option" 
                tabindex="0" 
                class="cursor-pointer select-none relative py-2 pl-3 pr-9 hover:bg-blue-600 hover:text-white focus:outline-none focus:bg-blue-600 focus:text-white"
                @click="updateSelection('{{ result.split(" [")[0] }}', '{{ result.split(" [")[1]|replace:"]","" }}')"
                @keydown.enter.prevent="updateSelection('{{ result.split(" [")[0] }}', '{{ result.split(" [")[1]|replace:"]","" }}')">
                {{ result }}
            </li>
        {% endfor %}
    </ul>
{% endif %}
```

**`sales_distribution/workorderdispatchsummary/details.html`** (Placeholder for the details page)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-gradient-to-r from-green-600 to-green-800 text-white p-4 rounded-t-lg mb-4">
        <h2 class="text-2xl font-bold">Work Order Dispatch Details</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6">
        <p class="text-gray-700">This is the detail page for Work Order: <span class="font-semibold">{{ wo_no }}</span> and Work Request: <span class="font-semibold">{{ wr_no }}</span>.</p>
        <p class="mt-4 text-gray-600">Implement your specific Work Order Dispatch details, CRUD operations, and related forms here.</p>
        <div class="mt-6 flex items-center justify-end">
            <a href="{% url 'sales_distribution:workorder_dispatch_list' %}" 
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                Back to List
            </a>
        </div>
    </div>
</div>
{% endblock %}
```

#### 4.5 URLs

Define URL patterns for all views.

**`sales_distribution/urls.py`**

```python
from django.urls import path
from .views import (
    WorkOrderDispatchListView,
    WorkOrderDispatchTablePartialView,
    WorkOrderDispatchUpdateSearchInputsView,
    CustomerAutoCompleteView,
    WorkOrderDispatchDetailsView # Placeholder for the details page
)

app_name = 'sales_distribution' # Namespace for URLs

urlpatterns = [
    path('workorder-dispatch/', WorkOrderDispatchListView.as_view(), name='workorder_dispatch_list'),
    path('workorder-dispatch/table/', WorkOrderDispatchTablePartialView.as_view(), name='workorder_dispatch_table'),
    path('workorder-dispatch/search-inputs/', WorkOrderDispatchUpdateSearchInputsView.as_view(), name='workorder_dispatch_update_search_inputs'),
    path('autocomplete/customer/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),
    
    # Placeholder URL for WorkOrder_Dispatch_Details.aspx
    # Adapt parameters (ModId, SubModId) if they are truly used beyond just query strings.
    path('workorder-dispatch/details/<str:wo_no>/<str:wr_no>/', WorkOrderDispatchDetailsView.as_view(), name='workorder_dispatch_details'),
    # If ModId and SubModId are crucial for lookup:
    # path('workorder-dispatch/details/<str:wo_no>/<str:wr_no>/<int:mod_id>/<int:sub_mod_id>/', WorkOrderDispatchDetailsView.as_view(), name='workorder_dispatch_details'),
]

```

#### 4.6 Tests

Write comprehensive tests for models and views.

**`sales_distribution/tests.py`**

```python
from django.test import TestCase, Client
from django.urls import reverse
from decimal import Decimal
from .models import WorkOrderDispatchSummary, CustomerMaster

# Unit tests for Models
class WorkOrderDispatchSummaryModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create test data for the WorkOrderDispatchSummary model (representing the DB View)
        # Note: Since this maps to a VIEW, you typically don't create instances directly via ORM.
        # For testing, you'd mock the database interaction or use a test database that has the view.
        # For an in-memory SQLite test database, you might manually create the view if your Django setup allows.
        # Or, simulate the data that the view would return.
        cls.record1 = WorkOrderDispatchSummary.objects.create(
            wr_no='WR001', wo_no='WO001', sys_date='2023-01-15',
            customer_id='CUST001', fin_year='2023-24', customer_name='Alpha Corp',
            employee_name='Mr. Smith', wo_qty=Decimal('100.000'), da_qty=Decimal('50.000')
        )
        cls.record2 = WorkOrderDispatchSummary.objects.create(
            wr_no='WR002', wo_no='WO002', sys_date='2023-02-20',
            customer_id='CUST002', fin_year='2023-24', customer_name='Beta Inc',
            employee_name='Ms. Jones', wo_qty=Decimal('75.000'), da_qty=Decimal('75.000') # This one should be filtered out
        )
        cls.record3 = WorkOrderDispatchSummary.objects.create(
            wr_no='WR003', wo_no='WO003', sys_date='2024-03-10',
            customer_id='CUST003', fin_year='2023-24', customer_name='Gamma Ltd',
            employee_name='Dr. Lee', wo_qty=Decimal('200.000'), da_qty=Decimal('0.000')
        )

    def test_record_creation(self):
        self.assertEqual(self.record1.wr_no, 'WR001')
        self.assertEqual(self.record1.customer_name, 'Alpha Corp')
        self.assertEqual(self.record1.wo_qty, Decimal('100.000'))
        self.assertEqual(self.record1.da_qty, Decimal('50.000'))

    def test_remaining_qty_property(self):
        self.assertEqual(self.record1.remaining_qty, Decimal('50.000'))
        self.assertEqual(self.record2.remaining_qty, Decimal('0.000'))
        self.assertEqual(self.record3.remaining_qty, Decimal('200.000'))

    def test_get_dispatch_details_url(self):
        expected_url = reverse('sales_distribution:workorder_dispatch_details', 
                               kwargs={'wo_no': 'WO001', 'wr_no': 'WR001'})
        self.assertEqual(self.record1.get_dispatch_details_url(), expected_url)

class CustomerMasterModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.customer1 = CustomerMaster.objects.create(customer_id='CUST001', customer_name='Alpha Corp')
        cls.customer2 = CustomerMaster.objects.create(customer_id='CUST002', customer_name='Beta Inc')

    def test_customer_creation(self):
        self.assertEqual(self.customer1.customer_id, 'CUST001')
        self.assertEqual(self.customer1.customer_name, 'Alpha Corp')

    def test_str_representation(self):
        self.assertEqual(str(self.customer1), 'Alpha Corp')

# Integration tests for Views
class WorkOrderDispatchViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create initial data for views to operate on
        WorkOrderDispatchSummary.objects.create(
            wr_no='WR001', wo_no='WO001', sys_date='2023-01-15',
            customer_id='CUST001', fin_year='2023-24', customer_name='Alpha Corp',
            employee_name='Mr. Smith', wo_qty=Decimal('100.000'), da_qty=Decimal('50.000')
        )
        WorkOrderDispatchSummary.objects.create(
            wr_no='WR002', wo_no='WO002', sys_date='2023-02-20',
            customer_id='CUST002', fin_year='2023-24', customer_name='Beta Inc',
            employee_name='Ms. Jones', wo_qty=Decimal('75.000'), da_qty=Decimal('75.000')
        ) # This should be filtered out by remaining_qty_calc > 0
        WorkOrderDispatchSummary.objects.create(
            wr_no='WR003', wo_no='WO003', sys_date='2024-03-10',
            customer_id='CUST003', fin_year='2023-24', customer_name='Gamma Ltd',
            employee_name='Dr. Lee', wo_qty=Decimal('200.000'), da_qty=Decimal('0.000')
        )
        
        CustomerMaster.objects.create(customer_id='CUST001', customer_name='Alpha Corp')
        CustomerMaster.objects.create(customer_id='CUST002', customer_name='Beta Inc')
        CustomerMaster.objects.create(customer_id='CUST003', customer_name='Gamma Ltd')

    def setUp(self):
        self.client = Client()

    def test_list_view_get(self):
        response = self.client.get(reverse('sales_distribution:workorder_dispatch_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderdispatchsummary/list.html')
        self.assertIn('search_form', response.context)

    def test_table_partial_view_get(self):
        response = self.client.get(reverse('sales_distribution:workorder_dispatch_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderdispatchsummary/_table.html')
        self.assertIn('work_order_records', response.context)
        # Only records with remaining_qty > 0 should be present
        self.assertEqual(len(response.context['work_order_records']), 2) # WR001, WR003 should be there

    def test_table_partial_view_search_by_customer_name(self):
        response = self.client.get(reverse('sales_distribution:workorder_dispatch_table'), {
            'search_type': 'customer_name',
            'search_value_text': 'alpha', # Case-insensitive contains
            'customer_id_hidden': 'CUST001' # Simulate autocomplete selection
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['work_order_records']), 1)
        self.assertEqual(response.context['work_order_records'][0].customer_name, 'Alpha Corp')

    def test_table_partial_view_search_by_wo_no(self):
        response = self.client.get(reverse('sales_distribution:workorder_dispatch_table'), {
            'search_type': 'wo_no',
            'search_value_id': 'WO001'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['work_order_records']), 1)
        self.assertEqual(response.context['work_order_records'][0].wo_no, 'WO001')

    def test_table_partial_view_search_by_wr_no(self):
        response = self.client.get(reverse('sales_distribution:workorder_dispatch_table'), {
            'search_type': 'wr_no',
            'search_value_id': 'WR003'
        })
        self.assertEqual(response.status_code, 200)
        self.assertEqual(len(response.context['work_order_records']), 1)
        self.assertEqual(response.context['work_order_records'][0].wr_no, 'WR003')

    def test_customer_autocomplete_view(self):
        response = self.client.get(reverse('sales_distribution:customer_autocomplete'), {'q': 'al'})
        self.assertEqual(response.status_code, 200)
        # Check if the response contains the expected customer name and ID in the specified format
        self.assertIn('Alpha Corp [CUST001]', response.content.decode())
        self.assertNotIn('Beta Inc', response.content.decode())
        
    def test_customer_autocomplete_view_no_query(self):
        response = self.client.get(reverse('sales_distribution:customer_autocomplete'), {'q': ''})
        self.assertEqual(response.status_code, 200)
        self.assertNotIn('<li>', response.content.decode()) # No results for empty query

    def test_update_search_inputs_view(self):
        response = self.client.post(reverse('sales_distribution:workorder_dispatch_update_search_inputs'), {
            'search_type': 'wo_no'
        })
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderdispatchsummary/_search_inputs.html')
        self.assertIn('name="search_value_id"', response.content.decode()) # Check for WO No input
        self.assertNotIn('name="search_value_text"', response.content.decode()) # Check for customer text input

    def test_dispatch_details_view(self):
        response = self.client.get(reverse('sales_distribution:workorder_dispatch_details', 
                                            kwargs={'wo_no': 'WO001', 'wr_no': 'WR001'}))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorderdispatchsummary/details.html')
        self.assertContains(response, 'WO001')
        self.assertContains(response, 'WR001')
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX for Dynamic Content Loading:**
    *   The main list page (`list.html`) uses `hx-get` on `#workOrderTable-container` to load the table content (`_table.html`) on page `load` and when a `refreshWorkOrderList` event is triggered.
    *   The search form uses `hx-get` on `submit` to reload the table with new filters, targeting the same `#workOrderTable-container`.
    *   The `search_type` dropdown uses `hx-post` to `workorder_dispatch_update_search_inputs` endpoint, dynamically swapping the search input fields (`_search_inputs.html`).
    *   Customer search input uses `hx-get` to `customer_autocomplete` endpoint for real-time suggestions, targeting `#autocomplete-results`.
*   **Alpine.js for UI State Management:**
    *   Used in `_search_inputs.html` to control the visibility of the text fields based on `searchType`.
    *   Manages `selectedCustomerId` and `selectedCustomerName` to ensure the correct values are passed to the form, and the input field is updated on autocomplete selection.
    *   Handles keyboard navigation and selection within autocomplete results.
*   **DataTables for List Views:**
    *   The `_table.html` partial expects to be initialized with DataTables.
    *   The `list.html` includes JavaScript that listens for `htmx:afterSwap` on the table container. Once the table content is loaded by HTMX, DataTables is initialized, providing client-side searching, sorting, and pagination.
*   **No Full Page Reloads:** All interactions (search, dropdown change, autocomplete) are handled via HTMX, preventing full page refreshes.
*   **HX-Trigger for List Refresh:** Although not explicitly shown in this search-only example, if CRUD operations were on a separate page (like `WorkOrder_Dispatch_Details.aspx`), they would send back an `HX-Trigger` header (e.g., `HX-Trigger: refreshWorkOrderList`) to tell the list page to re-fetch its data.
*   **DRY Templates:** Use of `_search_inputs.html` and `_table.html` partials promotes DRY.

This comprehensive plan provides a clear, actionable path for migrating the ASP.NET Work Order Dispatch module to a modern Django application, adhering to the specified architectural and technological guidelines.