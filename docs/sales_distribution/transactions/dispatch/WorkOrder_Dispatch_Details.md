## ASP.NET to Django Conversion Script: Work Order Dispatch Details

### Business Value Proposition for Django Modernization

This modernization initiative aims to transform your legacy ASP.NET 'Work Order Dispatch' module into a robust, scalable, and user-friendly Django application. By leveraging modern Django principles, HTMX for dynamic interactions, and Alpine.js for lightweight frontend logic, we will deliver a solution that offers significant business benefits:

1.  **Enhanced User Experience:** Replacing outdated ASP.NET WebForms and AJAX controls with HTMX and Alpine.js results in a highly responsive and interactive interface. Users will experience faster page loads and seamless data updates without full page refreshes, leading to improved productivity and reduced frustration.
2.  **Streamlined Data Management:** By adopting Django's "Fat Model, Thin View" architecture, complex business logic, such as calculating dispatchable quantities and managing dispatch records, will be centralized within the models. This ensures data integrity, consistency, and simplifies future maintenance and feature additions.
3.  **Improved Maintainability and Scalability:** Django's structured, modular design promotes cleaner code, making it easier for developers to understand, maintain, and extend the application. This reduces development costs and allows for easier integration of new features as your business evolves.
4.  **Automated Processes and Reduced Manual Effort:** The migration will automate the generation of dispatch acknowledgements and email notifications (including PDF attachments) using a background task queue. This eliminates manual steps, reduces human error, and ensures timely communication, freeing up your team for more critical tasks.
5.  **Robust Validation and Error Handling:** Django's powerful form validation capabilities will ensure that only accurate and complete dispatch data is processed, preventing costly errors and improving the reliability of your operations.
6.  **Future-Proof Technology Stack:** Moving to Django, HTMX, and Alpine.js positions your application on a modern, open-source stack with a vibrant community. This provides access to a wealth of tools, libraries, and ongoing support, protecting your investment and facilitating future innovations.
7.  **Cost Efficiency:** Leveraging open-source technologies and an automation-driven migration approach significantly reduces licensing costs associated with proprietary frameworks and minimizes the manual effort required for the transition.

In essence, this modernization will transform a critical operational module into a more efficient, reliable, and adaptable system, directly contributing to operational excellence and business growth.

---

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines: 
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

### Step 1: Extract Database Schema

**Analysis:** The ASP.NET code interacts with multiple database tables, primarily `SD_Cust_WorkOrder_Release`, `SD_Cust_WorkOrder_Products_Details`, `SD_Cust_WorkOrder_Dispatch`, and `tblHR_OfficeStaff`. It also reads from `SD_Cust_WorkOrder_Master`, `SD_Cust_master`, `SD_Cust_PO_Master`, and `tblCompany_master` for report generation and email.

**Inferred Tables and Key Columns:**

*   **`SD_Cust_WorkOrder_Release`**:
    *   `Id` (Primary Key, referenced as `WRId` in dispatch)
    *   `ItemId` (Foreign Key to `SD_Cust_WorkOrder_Products_Details.Id`)
    *   `IssuedQty` (Decimal)
    *   `WRNo` (String, Work Order Release Number)
    *   `WONo` (String, Work Order Number)
    *   `CompId` (Integer, Company ID)
    *   `SysDate` (Date)
*   **`SD_Cust_WorkOrder_Products_Details`**:
    *   `Id` (Primary Key, referenced as `ItemId` in dispatch and release)
    *   `ItemCode` (String)
    *   `Description` (String)
    *   `Qty` (Decimal, Original Work Order Quantity)
    *   `MId` (Foreign Key to `SD_Cust_WorkOrder_Master.Id`)
    *   `CompId` (Integer, Company ID)
*   **`SD_Cust_WorkOrder_Dispatch`**:
    *   `Id` (Primary Key)
    *   `SysDate` (Date)
    *   `SysTime` (Time)
    *   `SessionId` (String, User Session/ID)
    *   `CompId` (Integer, Company ID)
    *   `FinYearId` (Integer, Financial Year ID)
    *   `DANo` (String, Dispatch Acknowledgement Number)
    *   `WRNo` (String)
    *   `WRId` (Integer, Foreign Key to `SD_Cust_WorkOrder_Release.Id`)
    *   `ItemId` (Integer, Foreign Key to `SD_Cust_WorkOrder_Products_Details.Id`)
    *   `IssuedQty` (Decimal, Quantity released for dispatch)
    *   `DispatchQty` (Decimal, Quantity actually dispatched)
    *   `FreightCharges` (String, "Customer" or "Self")
    *   `Vehicleby` (String, "Customer" or "Self")
    *   `OctroiCharges` (String, "Customer" or "Self")
*   **`tblHR_OfficeStaff`**:
    *   `EmpId` (Primary Key, Employee ID)
    *   `EmployeeName` (String)
    *   `EmailId1` (String)
    *   `DA` (Integer, Dispatch Authority indicator)
    *   `ResignationDate` (String, check for empty string)
    *   `CompId` (Integer, Company ID)
    *   `UserID` (String, User ID, check for not '1')
*   **`SD_Cust_WorkOrder_Master`**: `Id`, `WONo`, `CustomerId`, `TaskWorkOrderDate`, `TaskCustInspection_FDate`, `TaskCustInspection_TDate`, `PONo`, `CompId`
*   **`SD_Cust_master`**: `CustomerId`, `RegdCity`, `RegdState`, `RegdCountry`, `WorkCity`, `WorkState`, `WorkCountry`, `MaterialDelCity`, `MaterialDelState`, `MaterialDelCountry`, `CompId`
*   **`SD_Cust_PO_Master`**: `PONo`, `PODate`, `CompId`
*   **`tblCompany_master`**: `CompId`, `MailServerIp`, `ErpSysmail`

### Step 2: Identify Backend Functionality

**Analysis:** The core functionality involves displaying dispatchable items, allowing users to specify quantities, selecting employees for notification, recording dispatch, and generating/emailing a report.

*   **Read:**
    *   Retrieving Work Order Release details (items, original quantities, released quantities).
    *   Calculating already dispatched quantities (`DATotalQty`) and remaining dispatchable quantities (`DARemainQty`) per item.
    *   Listing eligible employees for dispatch email notifications.
*   **Create:**
    *   Recording new dispatch entries in `SD_Cust_WorkOrder_Dispatch` for selected items.
*   **Validation:**
    *   Ensuring entered `DispatchQty` is numeric, positive, and does not exceed `DARemainQty`.
    *   Requiring at least one item selected for dispatch.
    *   Requiring at least one employee selected for email notification.
*   **Reporting & Communication:**
    *   Generating a detailed dispatch PDF report.
    *   Sending email notifications with the generated PDF report to selected employees.
*   **State Management:**
    *   `ViewState` is used to persist the `GridView1` data across postbacks; in Django, this state will be managed by re-querying or by form data submission.

### Step 3: Infer UI Components

**Analysis:** The ASP.NET UI consists of two main grids, several radio button groups, text inputs, checkboxes, and action buttons.

*   **Data Presentation:**
    *   `GridView1`: Displays dispatchable items with `SN`, `CK` (checkbox), `To DA Qty` (text input), `Item Code`, `Description`, `Qty`, `Released Qty`, `DA Total Qty`, `DA Remain Qty`. This will be a DataTables-enhanced HTML table.
    *   `GridView2`: Displays employees with `SN`, `CK` (checkbox), `Name of Employee`, `Emp Id`, `Email Id`. This will also be a DataTables-enhanced HTML table.
*   **User Input:**
    *   `TextBox1` (To DA Qty): Numeric input for dispatch quantity.
    *   `CheckBox1`, `CheckBox2`: Item and employee selection.
    *   Radio Buttons (`FCustomer`/`FSelf`, `VCustomer`/`VSelf`, `OCustomer`/`OSelf`): Choice for charges and vehicle.
*   **Actions:**
    *   `Submit` Button: Triggers dispatch process.
    *   `Cancel` Button: Navigates back.
*   **Dynamic UI:**
    *   `UpdateProgress` and `ModalPopupExtender`: Indicate ongoing AJAX operations with a spinner. This will be replaced by HTMX `hx-indicator` and Alpine.js for modals.
    *   `RequiredFieldValidator`, `RegularExpressionValidator`: Client-side and server-side validation. Handled by Django forms and HTMX error feedback.

### Step 4: Generate Django Code
For the purposes of this migration, we will create a new Django application called `sales_distribution`.

#### 4.1 Models (`sales_distribution/models.py`)

We'll define Django models that map to the identified database tables, using `managed = False` as per the requirement for existing databases. We'll also add a custom manager to `WorkOrderRelease` to encapsulate the complex logic of retrieving dispatchable items with calculated quantities (`DATotalQty`, `DARemainQty`).

```python
from django.db import models, transaction
from django.db.models import Sum, F, Q
from django.conf import settings
from django.utils import timezone
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
import logging

logger = logging.getLogger(__name__)

# --- Core Models (Mapping to ASP.NET DB Tables) ---

class CompanyMaster(models.Model):
    """Maps to tblCompany_master for company-specific settings like mail server."""
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    mail_server_ip = models.CharField(db_column='MailServerIp', max_length=255, blank=True, null=True)
    erp_sys_mail = models.CharField(db_column='ErpSysmail', max_length=255, blank=True, null=True)

    class Meta:
        managed = False
        db_table = 'tblCompany_master'
        verbose_name = 'Company Master'
        verbose_name_plural = 'Company Masters'

    def __str__(self):
        return f"Company {self.comp_id}"

class Employee(models.Model):
    """Maps to tblHR_OfficeStaff for employee details."""
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmployeeName', max_length=255)
    email_id1 = models.CharField(db_column='EmailId1', max_length=255, blank=True, null=True)
    da = models.IntegerField(db_column='DA', default=0) # Dispatch Authority
    resignation_date = models.CharField(db_column='ResignationDate', max_length=50, blank=True) # Stored as string in ASP.NET
    comp_id = models.IntegerField(db_column='CompId')
    user_id = models.CharField(db_column='UserID', max_length=50)

    class Meta:
        managed = False
        db_table = 'tblHR_OfficeStaff'
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class WorkOrderMaster(models.Model):
    """Maps to SD_Cust_WorkOrder_Master for Work Order details."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    wo_no = models.CharField(db_column='WONo', max_length=50)
    customer_id = models.IntegerField(db_column='CustomerId') # Assuming FK, but keeping as int for managed=False
    task_work_order_date = models.CharField(db_column='TaskWorkOrderDate', max_length=50, blank=True, null=True)
    task_cust_inspection_fdate = models.CharField(db_column='TaskCustInspection_FDate', max_length=50, blank=True, null=True)
    task_cust_inspection_tdate = models.CharField(db_column='TaskCustInspection_TDate', max_length=50, blank=True, null=True)
    po_no = models.CharField(db_column='PONo', max_length=50, blank=True, null=True)
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Master'
        verbose_name = 'Work Order Master'
        verbose_name_plural = 'Work Order Masters'

    def __str__(self):
        return self.wo_no

class WorkOrderProductDetail(models.Model):
    """Maps to SD_Cust_WorkOrder_Products_Details for detailed item info within a WO."""
    id = models.IntegerField(db_column='Id', primary_key=True)
    m_id = models.IntegerField(db_column='MId') # Foreign Key to WorkOrderMaster.id
    item_code = models.CharField(db_column='ItemCode', max_length=255)
    description = models.CharField(db_column='Description', max_length=500)
    qty = models.DecimalField(db_column='Qty', max_digits=18, decimal_places=3) # Original WO Quantity
    comp_id = models.IntegerField(db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Products_Details'
        verbose_name = 'Work Order Product Detail'
        verbose_name_plural = 'Work Order Product Details'

    def __str__(self):
        return f"{self.item_code} - {self.description}"

class WorkOrderReleaseManager(models.Manager):
    """Custom manager for WorkOrderRelease to handle complex data retrieval."""
    def get_dispatchable_items(self, wr_no, comp_id):
        """
        Retrieves items from WorkOrderRelease with calculated dispatch quantities.
        Simulates the LoadData() method from ASP.NET code-behind.
        """
        # Join WorkOrderRelease with WorkOrderProductDetail
        # and prefetch/annotate dispatch sums.
        items = self.filter(wr_no=wr_no, comp_id=comp_id).annotate(
            # Get original WO item details
            item_code=F('product_detail__item_code'),
            description=F('product_detail__description'),
            original_wo_qty=F('product_detail__qty'),
            # Calculate total dispatched quantity for this item within this release
            total_dispatched_qty=Sum(
                'dispatch_records__dispatch_qty',
                filter=Q(dispatch_records__item_id=F('item_id'), dispatch_records__wr_id=F('id')),
                default=0
            ),
        ).select_related('product_detail') # product_detail is the related_name from WorkOrderProductDetail

        # Post-process to calculate remaining quantities, mimicking ASP.NET logic
        dispatchable_data = []
        for item in items:
            released_qty = item.issued_qty # 'IssuedQty' from SD_Cust_WorkOrder_Release is 'released_qty'
            da_total_qty = item.total_dispatched_qty
            da_remain_qty = released_qty - da_total_qty

            if da_remain_qty > 0: # Only include if there's quantity remaining
                dispatchable_data.append({
                    'id': item.id, # This is WRId from SD_Cust_WorkOrder_Release
                    'item_id': item.item_id, # This is ItemId from SD_Cust_WorkOrder_Products_Details
                    'item_code': item.item_code,
                    'description': item.description,
                    'original_wo_qty': item.original_wo_qty,
                    'released_qty': released_qty,
                    'da_total_qty': da_total_qty,
                    'da_remain_qty': da_remain_qty,
                })
        return dispatchable_data

class WorkOrderRelease(models.Model):
    """Maps to SD_Cust_WorkOrder_Release for released items for a WO."""
    id = models.IntegerField(db_column='Id', primary_key=True) # This is WRId
    item_id = models.IntegerField(db_column='ItemId') # Foreign Key to WorkOrderProductDetail.id
    issued_qty = models.DecimalField(db_column='IssuedQty', max_digits=18, decimal_places=3)
    wr_no = models.CharField(db_column='WRNo', max_length=50)
    wo_no = models.CharField(db_column='WONo', max_length=50)
    comp_id = models.IntegerField(db_column='CompId')
    sys_date = models.CharField(db_column='SysDate', max_length=50) # Stored as string in ASP.NET

    # Custom manager
    objects = WorkOrderReleaseManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Release'
        verbose_name = 'Work Order Release'
        verbose_name_plural = 'Work Order Releases'

    def __str__(self):
        return f"WR {self.wr_no} - Item {self.item_id}"

    # For reverse relation from WorkOrderProductDetail
    # This relation is conceptual for joining purposes in the manager
    # product_detail = models.ForeignKey(WorkOrderProductDetail, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='releases', related_query_name='release_set')


class WorkOrderDispatchManager(models.Manager):
    def generate_dano(self, comp_id):
        """Generates a new DANo."""
        # This implementation assumes sequential numbering for DANo within a company.
        # In a real system, this should use a proper sequence generator or database-level auto-increment.
        today_str = timezone.now().strftime("%Y%m%d")
        last_dispatch = self.filter(comp_id=comp_id, dano__startswith=f'DANo{today_str}') \
                            .order_by('-id').first()
        if last_dispatch:
            try:
                last_suffix = int(last_dispatch.dano.replace(f'DANo{today_str}', ''))
                new_suffix = last_suffix + 1
            except ValueError:
                new_suffix = 1 # Fallback if DANo format is unexpected
        else:
            new_suffix = 1
        return f"DANo{today_str}{new_suffix:03d}"

class WorkOrderDispatch(models.Model):
    """Maps to SD_Cust_WorkOrder_Dispatch for dispatched items."""
    id = models.AutoField(db_column='Id', primary_key=True) # Using AutoField as it's typically an identity
    sys_date = models.CharField(db_column='SysDate', max_length=50) # Stored as string in ASP.NET
    sys_time = models.CharField(db_column='SysTime', max_length=50) # Stored as string in ASP.NET
    session_id = models.CharField(db_column='SessionId', max_length=255)
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    dano = models.CharField(db_column='DANo', max_length=50, unique=True)
    wr_no = models.CharField(db_column='WRNo', max_length=50)
    wr_id = models.IntegerField(db_column='WRId') # Foreign Key to WorkOrderRelease.id
    item_id = models.IntegerField(db_column='ItemId') # Foreign Key to WorkOrderProductDetail.id
    issued_qty = models.DecimalField(db_column='IssuedQty', max_digits=18, decimal_places=3)
    dispatch_qty = models.DecimalField(db_column='DispatchQty', max_digits=18, decimal_places=3)
    freight_charges = models.CharField(db_column='FreightCharges', max_length=50)
    vehicle_by = models.CharField(db_column='Vehicleby', max_length=50)
    octroi_charges = models.CharField(db_column='OctroiCharges', max_length=50)

    objects = WorkOrderDispatchManager() # Use the custom manager

    class Meta:
        managed = False
        db_table = 'SD_Cust_WorkOrder_Dispatch'
        verbose_name = 'Work Order Dispatch'
        verbose_name_plural = 'Work Order Dispatches'

    def __str__(self):
        return f"DANo: {self.dano} - Item: {self.item_id}"

    # Helper methods for business logic (Fat Model)
    @classmethod
    def process_dispatch(cls, comp_id, fin_year_id, session_id,
                         wr_no, wo_no, dispatch_items_data, selected_employee_ids,
                         freight_charges, vehicle_by, octroi_charges):
        """
        Handles the complete dispatch process: validation, database insert, and triggers reports/emails.
        This method encapsulates the core logic from Submit_Click.
        """
        if not dispatch_items_data:
            raise ValueError("No items selected for dispatch.")
        if not selected_employee_ids:
            raise ValueError("No employees selected for email notification.")

        # Validate dispatch quantities and consistency
        for item_data in dispatch_items_data:
            dispatch_qty = item_data['dispatch_qty']
            da_remain_qty = item_data['da_remain_qty']

            if not isinstance(dispatch_qty, (int, float)) or dispatch_qty <= 0:
                raise ValueError(f"Invalid dispatch quantity for item {item_data['item_id']}.")
            if dispatch_qty > da_remain_qty:
                raise ValueError(f"Dispatch quantity for item {item_data['item_code']} exceeds remaining quantity.")

        current_date = timezone.now().strftime("%Y-%m-%d")
        current_time = timezone.now().strftime("%H:%M:%S")
        dano = cls.objects.generate_dano(comp_id)

        dispatch_records_created = []

        with transaction.atomic():
            for item_data in dispatch_items_data:
                dispatch_record = cls.objects.create(
                    sys_date=current_date,
                    sys_time=current_time,
                    session_id=session_id,
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    dano=dano,
                    wr_no=wr_no,
                    wr_id=item_data['id'],  # This is WRId from WorkOrderRelease
                    item_id=item_data['item_id'],
                    issued_qty=item_data['released_qty'],
                    dispatch_qty=item_data['dispatch_qty'],
                    freight_charges=freight_charges,
                    vehicle_by=vehicle_by,
                    octroi_charges=octroi_charges,
                )
                dispatch_records_created.append(dispatch_record)

        if not dispatch_records_created:
            raise Exception("No dispatch records were successfully created.")

        # Trigger background tasks for report generation and email sending
        # In a real application, this would use Celery or a similar task queue
        from .tasks import generate_dispatch_report_and_email
        generate_dispatch_report_and_email.delay(
            dano=dano,
            wo_no=wo_no,
            wr_no=wr_no,
            comp_id=comp_id,
            selected_employee_ids=selected_employee_ids
        )

        return dano # Return the generated DANo for success message

# --- Conceptual Models for Reporting/Email (if not directly mapped or needed for FKs) ---
# For brevity, these are conceptual and not fully defined managed=False models as they are only
# used in the background task for report parameters, not direct view interactions.
# In a full migration, they would also be managed=False models.

class CustomerMaster(models.Model):
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    regd_city_id = models.IntegerField(db_column='RegdCity', null=True)
    regd_state_id = models.IntegerField(db_column='RegdState', null=True)
    regd_country_id = models.IntegerField(db_column='RegdCountry', null=True)
    # ... other fields

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'

class PurchaseOrderMaster(models.Model):
    po_no = models.CharField(db_column='PONo', primary_key=True, max_length=50)
    po_date = models.CharField(db_column='PODate', max_length=50, null=True)

    class Meta:
        managed = False
        db_table = 'SD_Cust_PO_Master'

# --- Add related_name for reverse lookups (Important for Manager queries) ---
# This is typically done in the model definitions, but for existing databases
# and `managed=False`, you might need to ensure your queries align.
# For example, to relate WorkOrderRelease to WorkOrderProductDetail:
# In WorkOrderRelease model:
# product_detail = models.ForeignKey(WorkOrderProductDetail, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='release_records')
# In WorkOrderDispatch model:
# wr_record = models.ForeignKey(WorkOrderRelease, on_delete=models.DO_NOTHING, db_column='WRId', related_name='dispatch_records')
# item_product_detail = models.ForeignKey(WorkOrderProductDetail, on_delete=models.DO_NOTHING, db_column='ItemId', related_name='dispatch_item_records')

# For the purpose of this example, we'll assume a database setup where these
# relations work or that the manager methods explicitly join them.
# For a robust solution with managed=False, careful FK definition is key.

```

#### 4.2 Forms (`sales_distribution/forms.py`)

A single form will manage the dispatch submission, including dynamically selected items and employees, and the radio button choices.

```python
from django import forms
from .models import WorkOrderDispatch, WorkOrderRelease, Employee
from django.core.exceptions import ValidationError
import decimal

class DispatchForm(forms.Form):
    """
    Form to handle the dispatch submission, including dynamic item quantities
    and selected employees.
    """
    # Hidden fields to carry context
    wo_no = forms.CharField(widget=forms.HiddenInput())
    wr_no = forms.CharField(widget=forms.HiddenInput())

    # Radio button choices
    FREIGHT_CHOICES = [('Customer', 'Customer'), ('Self', 'Self')]
    VEHICLE_CHOICES = [('Customer', 'Customer'), ('Self', 'Self')]
    OCTROI_CHOICES = [('Customer', 'Customer'), ('Self', 'Self')]

    freight_charges = forms.ChoiceField(
        choices=FREIGHT_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out'}),
        initial='Customer',
        label="Freight Charges by"
    )
    vehicle_by = forms.ChoiceField(
        choices=VEHICLE_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out'}),
        initial='Customer',
        label="Vehicle by"
    )
    octroi_charges = forms.ChoiceField(
        choices=OCTROI_CHOICES,
        widget=forms.RadioSelect(attrs={'class': 'form-radio h-4 w-4 text-blue-600 transition duration-150 ease-in-out'}),
        initial='Customer',
        label="Octroi Charges by"
    )

    # Dynamic fields for dispatch items and employees will be handled in clean()
    # or by iterating through POST data in the view after basic form validation.

    def __init__(self, *args, **kwargs):
        self.dispatchable_items = kwargs.pop('dispatchable_items', [])
        self.eligible_employees = kwargs.pop('eligible_employees', [])
        super().__init__(*args, **kwargs)

        # Dynamically add fields for employees checkboxes for display purposes
        # Actual validation of selected employees happens in clean()
        self.employee_checkboxes = []
        for emp in self.eligible_employees:
            field_name = f'employee_checked_{emp.emp_id}'
            self.fields[field_name] = forms.BooleanField(
                required=False,
                label=emp.employee_name,
                widget=forms.CheckboxInput(attrs={'class': 'form-checkbox h-4 w-4 text-blue-600'})
            )
            self.employee_checkboxes.append((field_name, emp))

    def clean(self):
        cleaned_data = super().clean()

        # Validate dispatch items and quantities
        selected_dispatch_items = []
        item_found = False
        for item in self.dispatchable_items:
            item_id = item['id'] # This is WRId
            form_checkbox_name = f'item_checked_{item_id}'
            form_qty_name = f'item_dispatch_qty_{item_id}'

            is_checked = self.data.get(form_checkbox_name) == 'on'
            dispatch_qty_str = self.data.get(form_qty_name, '').strip()

            if is_checked:
                item_found = True
                if not dispatch_qty_str:
                    self.add_error(form_qty_name, "Quantity is required for checked items.")
                    continue

                try:
                    dispatch_qty = decimal.Decimal(dispatch_qty_str)
                    if dispatch_qty <= 0:
                        self.add_error(form_qty_name, "Quantity must be positive.")
                        continue
                    if dispatch_qty.as_tuple().exponent < -3: # Check for more than 3 decimal places
                         self.add_error(form_qty_name, "Quantity cannot have more than 3 decimal places.")
                         continue
                except decimal.InvalidOperation:
                    self.add_error(form_qty_name, "Invalid numeric quantity.")
                    continue

                if dispatch_qty > item['da_remain_qty']:
                    self.add_error(form_qty_name, f"Quantity exceeds remaining ({item['da_remain_qty']}).")
                    continue

                selected_dispatch_items.append({
                    'id': item['id'], # WRId
                    'item_id': item['item_id'], # Actual ItemId
                    'item_code': item['item_code'],
                    'description': item['description'],
                    'released_qty': item['released_qty'],
                    'da_total_qty': item['da_total_qty'],
                    'da_remain_qty': item['da_remain_qty'],
                    'dispatch_qty': dispatch_qty,
                })

        if not item_found:
            self.add_error(None, "At least one item must be selected for dispatch.")

        cleaned_data['selected_dispatch_items'] = selected_dispatch_items

        # Validate selected employees
        selected_employee_ids = []
        employee_found = False
        for emp in self.eligible_employees:
            field_name = f'employee_checked_{emp.emp_id}'
            if self.data.get(field_name) == 'on':
                selected_employee_ids.append(emp.emp_id)
                employee_found = True

        if not employee_found:
            self.add_error(None, "At least one employee must be selected for email notification.")

        cleaned_data['selected_employee_ids'] = selected_employee_ids

        return cleaned_data

```

#### 4.3 Views (`sales_distribution/views.py`)

We'll use a `TemplateView` for the main page display and a custom `ProcessDispatchView` (inheriting from `View`) to handle the HTMX POST request for dispatch processing. A `TablePartialView` will render the dynamic tables.

```python
from django.views.generic import TemplateView, View
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse, Http404
from django.shortcuts import render, redirect
from django.utils.decorators import method_decorator
from django.views.decorators.http import require_POST
from .models import WorkOrderRelease, Employee, WorkOrderDispatch, WorkOrderProductDetail # Import necessary models
from .forms import DispatchForm
import logging

logger = logging.getLogger(__name__)

# Assume these are obtained from the session/user profile context
# In a real app, these would come from request.user.company.id, request.user.id, etc.
GLOBAL_COMP_ID = 1 # Example: Replace with actual company ID from session
GLOBAL_FIN_YEAR_ID = 2024 # Example: Replace with actual financial year from session
GLOBAL_SESSION_ID = 'system_user' # Example: Replace with actual user session ID

class WorkOrderDispatchDetailView(TemplateView):
    """
    Main view for Work Order Dispatch details. Displays dispatchable items and employees.
    This replaces the initial Page_Load and LoadData logic.
    """
    template_name = 'sales_distribution/workorder_dispatch/detail.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        wo_no = self.kwargs['wo_no']
        wr_no = self.kwargs['wr_no']
        comp_id = GLOBAL_COMP_ID # Get from session/user profile in real app

        # Prepare form with initial data
        # No initial POST data, so empty form for initial GET request
        form = DispatchForm(
            wo_no=wo_no,
            wr_no=wr_no,
            dispatchable_items=[], # Will be loaded by HTMX
            eligible_employees=[] # Will be loaded by HTMX
        )
        context['form'] = form
        context['wo_no'] = wo_no
        context['wr_no'] = wr_no
        return context

class WorkOrderDispatchItemsTablePartialView(View):
    """
    Renders the partial HTML table for dispatchable items (mimics GridView1).
    Loaded via HTMX.
    """
    def get(self, request, wo_no, wr_no):
        comp_id = GLOBAL_COMP_ID # Get from session/user profile in real app
        dispatchable_items = WorkOrderRelease.objects.get_dispatchable_items(wr_no, comp_id)

        # Filter items with remaining quantity > 0, matching ASP.NET logic
        # (This filtering is already inside get_dispatchable_items now)
        # filtered_items = [item for item in dispatchable_items if item['da_remain_qty'] > 0]

        return render(request, 'sales_distribution/workorder_dispatch/_items_table.html', {
            'dispatchable_items': dispatchable_items,
            'wo_no': wo_no,
            'wr_no': wr_no,
        })

class WorkOrderDispatchEmployeesTablePartialView(View):
    """
    Renders the partial HTML table for eligible employees (mimics GridView2).
    Loaded via HTMX.
    """
    def get(self, request):
        comp_id = GLOBAL_COMP_ID # Get from session/user profile in real app
        eligible_employees = Employee.objects.filter(
            da=1,
            resignation_date__exact='', # Assuming empty string means not resigned
            comp_id=comp_id
            # Assuming UserID != '1' is handled by role-based access or is not a universal filter
        ).exclude(user_id='1') # Exclude user ID '1' as per ASP.NET code

        return render(request, 'sales_distribution/workorder_dispatch/_employees_table.html', {
            'eligible_employees': eligible_employees
        })

@method_decorator(require_POST, name='dispatch')
class WorkOrderProcessDispatchView(View):
    """
    Handles the POST request for dispatch submission. This replaces Submit_Click.
    Uses HTMX for form submission and redirects/messages.
    """
    def post(self, request, wo_no, wr_no):
        comp_id = GLOBAL_COMP_ID
        fin_year_id = GLOBAL_FIN_YEAR_ID
        session_id = GLOBAL_SESSION_ID # Get from request.user in real app

        # Re-fetch necessary data for form validation
        dispatchable_items = WorkOrderRelease.objects.get_dispatchable_items(wr_no, comp_id)
        eligible_employees = Employee.objects.filter(
            da=1,
            resignation_date__exact='',
            comp_id=comp_id
        ).exclude(user_id='1')

        form = DispatchForm(
            request.POST,
            wo_no=wo_no,
            wr_no=wr_no,
            dispatchable_items=dispatchable_items,
            eligible_employees=eligible_employees
        )

        if form.is_valid():
            try:
                dano = WorkOrderDispatch.process_dispatch(
                    comp_id=comp_id,
                    fin_year_id=fin_year_id,
                    session_id=session_id,
                    wr_no=wr_no,
                    wo_no=wo_no,
                    dispatch_items_data=form.cleaned_data['selected_dispatch_items'],
                    selected_employee_ids=form.cleaned_data['selected_employee_ids'],
                    freight_charges=form.cleaned_data['freight_charges'],
                    vehicle_by=form.cleaned_data['vehicle_by'],
                    octroi_charges=form.cleaned_data['octroi_charges']
                )
                messages.success(request, f"Dispatch of Work Order No. {wo_no} (DA No: {dano}) is completed successfully.")

                # HTMX redirect/trigger for success
                # This will redirect the main page to the WorkOrderDispatch list view
                response = HttpResponse(status=204) # No content, indicates success to HTMX
                response['HX-Redirect'] = reverse_lazy('workorder_dispatch_list') # Assuming a list view exists
                return response

            except ValueError as e:
                # Specific business validation errors
                messages.error(request, str(e))
            except Exception as e:
                # General errors during dispatch processing
                logger.exception("Error processing dispatch:")
                messages.error(request, f"An unexpected error occurred: {e}. Please try again.")

        # If form is not valid or an error occurred, re-render the form fragment with errors
        # This assumes the form is submitted via HTMX to swap content in the modal or main area
        return render(request, 'sales_distribution/workorder_dispatch/_dispatch_form_content.html', {
            'form': form,
            'dispatchable_items': dispatchable_items, # Pass items back for re-rendering table fields
            'eligible_employees': eligible_employees, # Pass employees back for re-rendering table fields
            'wo_no': wo_no,
            'wr_no': wr_no,
        })

# Assuming a main list view to redirect to after dispatch
class WorkOrderDispatchListView(TemplateView):
    template_name = 'sales_distribution/workorder_dispatch/list.html'
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # This view would typically list all past dispatches.
        # For simplicity, just a placeholder.
        return context

```

#### 4.4 Templates (`sales_distribution/templates/sales_distribution/workorder_dispatch/`)

**`detail.html` (Main Page - Replaces `WorkOrder_Dispatch_Details.aspx` structure)**

```html
{% extends 'core/base.html' %}

{% block title %}Work Order - Dispatch{% endblock %}

{% block extra_head %}
    <!-- DataTables CSS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/2.0.8/css/dataTables.dataTables.min.css">
    <!-- Optional: Tailwind CSS for DataTables integration, if custom styling needed -->
    <!-- <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/v/dt/dt-2.0.8/datatables.min.css"/> -->
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8 max-w-6xl">
    <div class="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-4 rounded-t-lg shadow-md mb-6">
        <h1 class="text-xl font-bold">Work Order - Dispatch (WO: {{ wo_no }}, WR: {{ wr_no }})</h1>
    </div>

    <!-- Main content area to be swapped by HTMX -->
    <div id="dispatch-form-container"
         hx-get="{% url 'sales_distribution:workorderdispatch_form_content' wo_no=wo_no wr_no=wr_no %}"
         hx-trigger="load"
         hx-swap="innerHTML"
         class="bg-white p-6 rounded-b-lg shadow-lg relative">
        <!-- Initial loading indicator -->
        <div class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-75 z-10" id="loading-indicator">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-700">Loading dispatch details...</p>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/2.0.8/js/dataTables.min.js"></script>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script>
        // Alpine.js for general UI state, e.g., modal handling if any.
        // For this page, DataTables initialization is main JS task.
        // DataTables needs to be re-initialized after HTMX swaps.
        document.body.addEventListener('htmx:afterSwap', function (evt) {
            if (evt.detail.target.id === 'dispatch-form-container') {
                // Initialize DataTables on the new content if the tables exist
                $('#itemsDataTable').DataTable();
                $('#employeesDataTable').DataTable();

                // Handle the confirmation dialog (replaces confDyna)
                const submitButton = document.getElementById('submitButton');
                if (submitButton) {
                    submitButton.addEventListener('click', function(e) {
                        if (!confirm('Do you really want to Dispatch this Work Order?')) {
                            e.preventDefault(); // Prevent HTMX request if user cancels
                        }
                    });
                }
            }
        });
        // Hide initial loading indicator once HTMX loads content
        document.addEventListener('htmx:afterOnLoad', function(evt) {
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
        });
    </script>
{% endblock %}
```

**`_dispatch_form_content.html` (Partial for main form content, swapped via HTMX)**
This partial will contain the entire form, including the tables and radio buttons, and is re-rendered on form submission (with errors if any).

```html
<form hx-post="{% url 'sales_distribution:workorderdispatch_process' wo_no=wo_no wr_no=wr_no %}"
      hx-swap="outerHTML"
      hx-target="#dispatch-form-container"
      hx-indicator="#loading-indicator"> {# Use a global indicator for the main form #}
    {% csrf_token %}

    <div class="flex items-center justify-center absolute inset-0 bg-white bg-opacity-75 z-10 hidden" id="loading-indicator">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <p class="mt-2 text-gray-700">Processing dispatch...</p>
    </div>

    <div class="space-y-6">
        <!-- Display general form errors -->
        {% if form.non_field_errors %}
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <strong class="font-bold">Error!</strong>
                <span class="block sm:inline">{{ form.non_field_errors }}</span>
            </div>
        {% endif %}

        <!-- Work Order Items Table (GridView1) -->
        <h3 class="text-lg font-semibold text-gray-800 mb-3">Work Order Items for Dispatch</h3>
        <div class="overflow-x-auto border border-gray-200 rounded-lg shadow-sm">
            <table id="itemsDataTable" class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                        <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
                        <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">To DA Qty</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Code</th>
                        <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty (WO)</th>
                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Released Qty</th>
                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">DA Total Qty</th>
                        <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">DA Remain Qty</th>
                        <!-- Hidden columns for Id and ItemId will be handled by input names -->
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for item in dispatchable_items %}
                    <tr>
                        <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ forloop.counter }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-center text-sm">
                            <input type="checkbox" name="item_checked_{{ item.id }}" id="item_checked_{{ item.id }}"
                                   class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out"
                                   {% if item.da_remain_qty <= 0 %}disabled{% endif %}>
                            <!-- Hidden inputs for item context required by form validation -->
                            <input type="hidden" name="item_id_{{ item.id }}" value="{{ item.item_id }}">
                            <input type="hidden" name="wr_id_{{ item.id }}" value="{{ item.id }}">
                            <input type="hidden" name="released_qty_{{ item.id }}" value="{{ item.released_qty }}">
                            <input type="hidden" name="da_total_qty_{{ item.id }}" value="{{ item.da_total_qty }}">
                            <input type="hidden" name="da_remain_qty_{{ item.id }}" value="{{ item.da_remain_qty }}">
                        </td>
                        <td class="py-2 px-4 whitespace-nowrap text-center text-sm">
                            <input type="number" step="0.001" min="0" name="item_dispatch_qty_{{ item.id }}"
                                   class="block w-24 px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm text-right"
                                   value="{% if form.is_bound %}{{ request.POST.getlist(form.prefix|add:'item_dispatch_qty_')|index:forloop.counter0 }}{% endif %}"
                                   {% if item.da_remain_qty <= 0 %}disabled{% endif %}>
                            {% if form.errors and form.errors|get_item:form.prefix|add:'item_dispatch_qty_'|add:forloop.counter0 %}
                                <p class="text-red-500 text-xs mt-1">{{ form.errors|get_item:form.prefix|add:'item_dispatch_qty_'|add:forloop.counter0 }}</p>
                            {% endif %}
                            {% for error in form.errors.as_data.item_dispatch_qty %} {# Accessing specific dynamic field errors #}
                                {% if error.params == item.id %}
                                    <p class="text-red-500 text-xs mt-1">{{ error.message }}</p>
                                {% endif %}
                            {% endfor %}
                        </td>
                        <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-800">{{ item.item_code }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-800">{{ item.description }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ item.original_wo_qty|floatformat:3 }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ item.released_qty|floatformat:3 }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ item.da_total_qty|floatformat:3 }}</td>
                        <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ item.da_remain_qty|floatformat:3 }}</td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="9" class="py-4 px-4 text-center text-lg text-red-600">No data to display !</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <br>

        <!-- Employees Table (GridView2) and Radio Buttons -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Employees Table -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Employees for Email Notification</h3>
                <div class="overflow-x-auto border border-gray-200 rounded-lg shadow-sm h-64">
                    <table id="employeesDataTable" class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="py-2 px-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                                <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">CK</th>
                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name of Employee</th>
                                <th class="py-2 px-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Emp Id</th>
                                <th class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email Id</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {% for employee in eligible_employees %}
                            <tr>
                                <td class="py-2 px-4 whitespace-nowrap text-right text-sm text-gray-500">{{ forloop.counter }}</td>
                                <td class="py-2 px-4 whitespace-nowrap text-center text-sm">
                                    <input type="checkbox" name="employee_checked_{{ employee.emp_id }}"
                                           class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                                </td>
                                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-800">{{ employee.employee_name }}</td>
                                <td class="py-2 px-4 whitespace-nowrap text-center text-sm text-gray-500">{{ employee.emp_id }}</td>
                                <td class="py-2 px-4 whitespace-nowrap text-left text-sm text-gray-500">{{ employee.email_id1|default:"N/A" }}</td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="py-4 px-4 text-center text-lg text-red-600">No data to display !</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Radio Buttons -->
            <div>
                <h3 class="text-lg font-semibold text-gray-800 mb-3">Dispatch Charges & Vehicle</h3>
                <div class="bg-gray-50 p-4 rounded-lg shadow-sm space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ form.freight_charges.label }}</label>
                        <div class="mt-2 space-x-4">
                            {% for radio in form.freight_charges %}
                                <label class="inline-flex items-center">
                                    {{ radio.tag }}
                                    <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                </label>
                            {% endfor %}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ form.vehicle_by.label }}</label>
                        <div class="mt-2 space-x-4">
                            {% for radio in form.vehicle_by %}
                                <label class="inline-flex items-center">
                                    {{ radio.tag }}
                                    <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                </label>
                            {% endfor %}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">{{ form.octroi_charges.label }}</label>
                        <div class="mt-2 space-x-4">
                            {% for radio in form.octroi_charges %}
                                <label class="inline-flex items-center">
                                    {{ radio.tag }}
                                    <span class="ml-2 text-sm text-gray-700">{{ radio.choice_label }}</span>
                                </label>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 flex justify-center space-x-4">
            <button type="submit" id="submitButton"
                    class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                Submit
            </button>
            <a href="{% url 'sales_distribution:workorder_dispatch_list' %}"
               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-6 rounded-md shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                Cancel
            </a>
        </div>
    </div>
</form>
```

**Note on template logic:**
*   The `{{ form.errors|get_item:form.prefix|add:'item_dispatch_qty_'|add:forloop.counter0 }}` syntax is a placeholder for accessing errors on dynamically named fields. You might need a custom template filter or more robust error handling in the form/view. For this example, direct access to `form.errors.as_data` and iterating through it with `error.params` is used as a more robust way to show errors for dynamic fields.
*   The `item.da_remain_qty <= 0` check for disabling inputs directly mirrors the ASP.NET logic.

#### 4.5 URLs (`sales_distribution/urls.py`)

```python
from django.urls import path
from .views import (
    WorkOrderDispatchDetailView,
    WorkOrderDispatchItemsTablePartialView,
    WorkOrderDispatchEmployeesTablePartialView,
    WorkOrderProcessDispatchView,
    WorkOrderDispatchListView, # Assuming this exists for redirection
)

app_name = 'sales_distribution'

urlpatterns = [
    # Main page for dispatch details
    path('workorder-dispatch/<str:wo_no>/<str:wr_no>/',
         WorkOrderDispatchDetailView.as_view(),
         name='workorder_dispatch_detail'),

    # HTMX endpoint to load the main dispatch form content
    path('workorder-dispatch/<str:wo_no>/<str:wr_no>/form-content/',
         WorkOrderProcessDispatchView.as_view(), # Uses the POST view for GET to render form with data
         name='workorderdispatch_form_content'),

    # HTMX endpoint to process dispatch submission
    path('workorder-dispatch/<str:wo_no>/<str:wr_no>/process/',
         WorkOrderProcessDispatchView.as_view(),
         name='workorderdispatch_process'),

    # Placeholder for the list view to redirect after successful dispatch
    path('workorder-dispatch/', WorkOrderDispatchListView.as_view(), name='workorder_dispatch_list'),
]

# Note: The original ASP.NET code used query parameters for WONo and WRNo.
# In Django, it's cleaner to use URL path parameters as done here.
# The `form-content` endpoint is a pragmatic choice to re-render the entire form
# with items, employees, and potentially errors on POST, or for initial load.
```

#### 4.6 Tests (`sales_distribution/tests.py`)

This will cover model business logic and view interactions, including HTMX.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch, MagicMock
from decimal import Decimal
import datetime

from .models import (
    CompanyMaster, Employee, WorkOrderMaster, WorkOrderProductDetail,
    WorkOrderRelease, WorkOrderDispatch
)
from .forms import DispatchForm
# Import Celery task, though it will be mocked for unit tests
from .tasks import generate_dispatch_report_and_email


class WorkOrderDispatchModelTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        # Create minimal test data for related models to ensure FK integrity during queries
        cls.comp_id = 101
        cls.fin_year_id = 2024
        cls.session_id = 'test_user_session'
        cls.wo_no = 'WO001'
        cls.wr_no = 'WR001'
        cls.emp_id_valid = 1
        cls.emp_id_invalid = 2
        cls.item_id_1 = 10
        cls.item_id_2 = 11

        CompanyMaster.objects.create(comp_id=cls.comp_id, mail_server_ip='127.0.0.1', erp_sys_mail='<EMAIL>')

        Employee.objects.create(emp_id=cls.emp_id_valid, employee_name='John Doe', email_id1='<EMAIL>', da=1, resignation_date='', comp_id=cls.comp_id, user_id='JDoe')
        Employee.objects.create(emp_id=cls.emp_id_invalid, employee_name='Jane Smith', email_id1='<EMAIL>', da=0, resignation_date='', comp_id=cls.comp_id, user_id='JSmith')
        Employee.objects.create(emp_id=3, employee_name='System User', email_id1='<EMAIL>', da=1, resignation_date='', comp_id=cls.comp_id, user_id='1') # Excluded

        WorkOrderMaster.objects.create(id=1, wo_no=cls.wo_no, customer_id=1, comp_id=cls.comp_id)

        WorkOrderProductDetail.objects.create(id=cls.item_id_1, m_id=1, item_code='ITEM001', description='Product A', qty=Decimal('100.000'), comp_id=cls.comp_id)
        WorkOrderProductDetail.objects.create(id=cls.item_id_2, m_id=1, item_code='ITEM002', description='Product B', qty=Decimal('50.000'), comp_id=cls.comp_id)

        # Work Order Release items
        cls.wr_item_1 = WorkOrderRelease.objects.create(id=1, item_id=cls.item_id_1, issued_qty=Decimal('80.000'), wr_no=cls.wr_no, wo_no=cls.wo_no, comp_id=cls.comp_id, sys_date='2024-01-01')
        cls.wr_item_2 = WorkOrderRelease.objects.create(id=2, item_id=cls.item_id_2, issued_qty=Decimal('40.000'), wr_no=cls.wr_no, wo_no=cls.wo_no, comp_id=cls.comp_id, sys_date='2024-01-01')

    def setUp(self):
        self.client = Client()

    def test_get_dispatchable_items(self):
        # Simulate some existing dispatch for wr_item_1
        WorkOrderDispatch.objects.create(
            sys_date=timezone.now().strftime("%Y-%m-%d"), sys_time=timezone.now().strftime("%H:%M:%S"),
            session_id='prev_session', comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            dano='DANoTEST001', wr_no=self.wr_no, wr_id=self.wr_item_1.id, item_id=self.item_id_1,
            issued_qty=Decimal('80.000'), dispatch_qty=Decimal('30.000'),
            freight_charges='Customer', vehicle_by='Customer', octroi_charges='Customer'
        )

        dispatchable_data = WorkOrderRelease.objects.get_dispatchable_items(self.wr_no, self.comp_id)

        self.assertEqual(len(dispatchable_data), 2)

        item1_data = next((item for item in dispatchable_data if item['item_id'] == self.item_id_1), None)
        self.assertIsNotNone(item1_data)
        self.assertEqual(item1_data['released_qty'], Decimal('80.000'))
        self.assertEqual(item1_data['da_total_qty'], Decimal('30.000'))
        self.assertEqual(item1_data['da_remain_qty'], Decimal('50.000'))

        item2_data = next((item for item in dispatchable_data if item['item_id'] == self.item_id_2), None)
        self.assertIsNotNone(item2_data)
        self.assertEqual(item2_data['released_qty'], Decimal('40.000'))
        self.assertEqual(item2_data['da_total_qty'], Decimal('0.000'))
        self.assertEqual(item2_data['da_remain_qty'], Decimal('40.000'))

    def test_generate_dano(self):
        # Test DANo generation logic
        dano1 = WorkOrderDispatch.objects.generate_dano(self.comp_id)
        self.assertTrue(dano1.startswith(f"DANo{timezone.now().strftime('%Y%m%d')}"))
        self.assertTrue(dano1.endswith('001'))

        # Create one dispatch record
        WorkOrderDispatch.objects.create(
            sys_date=timezone.now().strftime("%Y-%m-%d"), sys_time=timezone.now().strftime("%H:%M:%S"),
            session_id='test_session', comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            dano=dano1, wr_no=self.wr_no, wr_id=self.wr_item_1.id, item_id=self.item_id_1,
            issued_qty=Decimal('10'), dispatch_qty=Decimal('10'),
            freight_charges='Self', vehicle_by='Self', octroi_charges='Self'
        )

        dano2 = WorkOrderDispatch.objects.generate_dano(self.comp_id)
        self.assertTrue(dano2.endswith('002'))

    @patch('sales_distribution.tasks.generate_dispatch_report_and_email.delay')
    def test_process_dispatch_success(self, mock_delay):
        initial_dispatch_count = WorkOrderDispatch.objects.count()

        dispatch_items_data = [
            {
                'id': self.wr_item_1.id, # WRId
                'item_id': self.item_id_1,
                'item_code': 'ITEM001',
                'description': 'Product A',
                'released_qty': Decimal('80.000'),
                'da_total_qty': Decimal('0.000'),
                'da_remain_qty': Decimal('80.000'),
                'dispatch_qty': Decimal('50.000'),
            },
            {
                'id': self.wr_item_2.id, # WRId
                'item_id': self.item_id_2,
                'item_code': 'ITEM002',
                'description': 'Product B',
                'released_qty': Decimal('40.000'),
                'da_total_qty': Decimal('0.000'),
                'da_remain_qty': Decimal('40.000'),
                'dispatch_qty': Decimal('20.000'),
            }
        ]
        selected_employee_ids = [self.emp_id_valid]

        dano = WorkOrderDispatch.process_dispatch(
            comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
            wr_no=self.wr_no, wo_no=self.wo_no, dispatch_items_data=dispatch_items_data,
            selected_employee_ids=selected_employee_ids,
            freight_charges='Customer', vehicle_by='Customer', octroi_charges='Customer'
        )

        self.assertEqual(WorkOrderDispatch.objects.count(), initial_dispatch_count + 2)
        self.assertTrue(WorkOrderDispatch.objects.filter(dano=dano).exists())
        mock_delay.assert_called_once_with(
            dano=dano, wo_no=self.wo_no, wr_no=self.wr_no, comp_id=self.comp_id,
            selected_employee_ids=selected_employee_ids
        )

    def test_process_dispatch_no_items_selected(self):
        with self.assertRaisesMessage(ValueError, "No items selected for dispatch."):
            WorkOrderDispatch.process_dispatch(
                comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
                wr_no=self.wr_no, wo_no=self.wo_no, dispatch_items_data=[],
                selected_employee_ids=[self.emp_id_valid],
                freight_charges='Customer', vehicle_by='Customer', octroi_charges='Customer'
            )

    def test_process_dispatch_no_employees_selected(self):
        dispatch_items_data = [
            {
                'id': self.wr_item_1.id, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
                'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'), 'da_remain_qty': Decimal('80.000'),
                'dispatch_qty': Decimal('10.000'),
            }
        ]
        with self.assertRaisesMessage(ValueError, "No employees selected for email notification."):
            WorkOrderDispatch.process_dispatch(
                comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
                wr_no=self.wr_no, wo_no=self.wo_no, dispatch_items_data=dispatch_items_data,
                selected_employee_ids=[],
                freight_charges='Customer', vehicle_by='Customer', octroi_charges='Customer'
            )

    def test_process_dispatch_qty_exceeds_remain(self):
        dispatch_items_data = [
            {
                'id': self.wr_item_1.id, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
                'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'), 'da_remain_qty': Decimal('80.000'),
                'dispatch_qty': Decimal('90.000'), # Exceeds remaining
            }
        ]
        selected_employee_ids = [self.emp_id_valid]
        with self.assertRaisesMessage(ValueError, "Dispatch quantity for item ITEM001 exceeds remaining quantity."):
            WorkOrderDispatch.process_dispatch(
                comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
                wr_no=self.wr_no, wo_no=self.wo_no, dispatch_items_data=dispatch_items_data,
                selected_employee_ids=selected_employee_ids,
                freight_charges='Customer', vehicle_by='Customer', octroi_charges='Customer'
            )

    def test_process_dispatch_qty_invalid(self):
        dispatch_items_data = [
            {
                'id': self.wr_item_1.id, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
                'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'), 'da_remain_qty': Decimal('80.000'),
                'dispatch_qty': Decimal('0.000'), # Invalid quantity
            }
        ]
        selected_employee_ids = [self.emp_id_valid]
        with self.assertRaisesMessage(ValueError, "Invalid dispatch quantity for item 10."):
            WorkOrderDispatch.process_dispatch(
                comp_id=self.comp_id, fin_year_id=self.fin_year_id, session_id=self.session_id,
                wr_no=self.wr_no, wo_no=self.wo_no, dispatch_items_data=dispatch_items_data,
                selected_employee_ids=selected_employee_ids,
                freight_charges='Customer', vehicle_by='Customer', octroi_charges='Customer'
            )

class DispatchFormTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 101
        cls.wo_no = 'WO001'
        cls.wr_no = 'WR001'
        cls.emp_id_valid = 1
        cls.item_id_1 = 10
        cls.wr_id_1 = 1

        Employee.objects.create(emp_id=cls.emp_id_valid, employee_name='John Doe', email_id1='<EMAIL>', da=1, resignation_date='', comp_id=cls.comp_id, user_id='JDoe')
        WorkOrderProductDetail.objects.create(id=cls.item_id_1, m_id=1, item_code='ITEM001', description='Product A', qty=Decimal('100.000'), comp_id=cls.comp_id)
        WorkOrderRelease.objects.create(id=cls.wr_id_1, item_id=cls.item_id_1, issued_qty=Decimal('80.000'), wr_no=cls.wr_no, wo_no=cls.wo_no, comp_id=cls.comp_id, sys_date='2024-01-01')

    def test_form_valid(self):
        dispatchable_items = [{
            'id': self.wr_id_1, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
            'original_wo_qty': Decimal('100.000'), 'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'),
            'da_remain_qty': Decimal('80.000'),
        }]
        eligible_employees = Employee.objects.filter(emp_id=self.emp_id_valid)

        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'item_checked_{self.wr_id_1}': 'on',
            f'item_dispatch_qty_{self.wr_id_1}': '10.500',
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        form = DispatchForm(data=data, dispatchable_items=dispatchable_items, eligible_employees=eligible_employees)
        self.assertTrue(form.is_valid(), form.errors)
        self.assertEqual(len(form.cleaned_data['selected_dispatch_items']), 1)
        self.assertEqual(form.cleaned_data['selected_dispatch_items'][0]['dispatch_qty'], Decimal('10.500'))
        self.assertEqual(form.cleaned_data['selected_employee_ids'], [self.emp_id_valid])

    def test_form_invalid_no_items_checked(self):
        dispatchable_items = [{
            'id': self.wr_id_1, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
            'original_wo_qty': Decimal('100.000'), 'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'),
            'da_remain_qty': Decimal('80.000'),
        }]
        eligible_employees = Employee.objects.filter(emp_id=self.emp_id_valid)

        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        form = DispatchForm(data=data, dispatchable_items=dispatchable_items, eligible_employees=eligible_employees)
        self.assertFalse(form.is_valid())
        self.assertIn("At least one item must be selected for dispatch.", form.errors['__all__'])

    def test_form_invalid_qty_missing(self):
        dispatchable_items = [{
            'id': self.wr_id_1, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
            'original_wo_qty': Decimal('100.000'), 'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'),
            'da_remain_qty': Decimal('80.000'),
        }]
        eligible_employees = Employee.objects.filter(emp_id=self.emp_id_valid)

        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'item_checked_{self.wr_id_1}': 'on',
            f'item_dispatch_qty_{self.wr_id_1}': '', # Missing quantity
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        form = DispatchForm(data=data, dispatchable_items=dispatchable_items, eligible_employees=eligible_employees)
        self.assertFalse(form.is_valid())
        self.assertIn("Quantity is required for checked items.", form.errors[f'item_dispatch_qty_{self.wr_id_1}'][0])

    def test_form_invalid_qty_exceeds_remain(self):
        dispatchable_items = [{
            'id': self.wr_id_1, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
            'original_wo_qty': Decimal('100.000'), 'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'),
            'da_remain_qty': Decimal('80.000'),
        }]
        eligible_employees = Employee.objects.filter(emp_id=self.emp_id_valid)

        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'item_checked_{self.wr_id_1}': 'on',
            f'item_dispatch_qty_{self.wr_id_1}': '90.000', # Exceeds remaining
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        form = DispatchForm(data=data, dispatchable_items=dispatchable_items, eligible_employees=eligible_employees)
        self.assertFalse(form.is_valid())
        self.assertIn("Quantity exceeds remaining (80.000).", form.errors[f'item_dispatch_qty_{self.wr_id_1}'][0])

    def test_form_invalid_qty_too_many_decimals(self):
        dispatchable_items = [{
            'id': self.wr_id_1, 'item_id': self.item_id_1, 'item_code': 'ITEM001', 'description': 'Product A',
            'original_wo_qty': Decimal('100.000'), 'released_qty': Decimal('80.000'), 'da_total_qty': Decimal('0.000'),
            'da_remain_qty': Decimal('80.000'),
        }]
        eligible_employees = Employee.objects.filter(emp_id=self.emp_id_valid)

        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'item_checked_{self.wr_id_1}': 'on',
            f'item_dispatch_qty_{self.wr_id_1}': '10.1234', # Too many decimals
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        form = DispatchForm(data=data, dispatchable_items=dispatchable_items, eligible_employees=eligible_employees)
        self.assertFalse(form.is_valid())
        self.assertIn("Quantity cannot have more than 3 decimal places.", form.errors[f'item_dispatch_qty_{self.wr_id_1}'][0])


class WorkOrderDispatchViewsTest(TestCase):
    @classmethod
    def setUpTestData(cls):
        cls.comp_id = 101
        cls.fin_year_id = 2024
        cls.wo_no = 'WO001'
        cls.wr_no = 'WR001'
        cls.emp_id_valid = 1
        cls.item_id_1 = 10
        cls.wr_id_1 = 1

        CompanyMaster.objects.create(comp_id=cls.comp_id, mail_server_ip='127.0.0.1', erp_sys_mail='<EMAIL>')
        Employee.objects.create(emp_id=cls.emp_id_valid, employee_name='John Doe', email_id1='<EMAIL>', da=1, resignation_date='', comp_id=cls.comp_id, user_id='JDoe')
        Employee.objects.create(emp_id=2, employee_name='Jane Smith', email_id1='<EMAIL>', da=0, resignation_date='', comp_id=cls.comp_id, user_id='JSmith')
        Employee.objects.create(emp_id=3, employee_name='System User', email_id1='<EMAIL>', da=1, resignation_date='', comp_id=cls.comp_id, user_id='1') # Excluded

        WorkOrderMaster.objects.create(id=1, wo_no=cls.wo_no, customer_id=1, comp_id=cls.comp_id)
        WorkOrderProductDetail.objects.create(id=cls.item_id_1, m_id=1, item_code='ITEM001', description='Product A', qty=Decimal('100.000'), comp_id=cls.comp_id)
        cls.wr_item_1_obj = WorkOrderRelease.objects.create(id=cls.wr_id_1, item_id=cls.item_id_1, issued_qty=Decimal('80.000'), wr_no=cls.wr_no, wo_no=cls.wo_no, comp_id=cls.comp_id, sys_date='2024-01-01')

    def test_detail_view_get(self):
        response = self.client.get(reverse('sales_distribution:workorder_dispatch_detail', args=[self.wo_no, self.wr_no]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorder_dispatch/detail.html')
        self.assertContains(response, 'Work Order - Dispatch')
        self.assertContains(response, self.wo_no)
        self.assertContains(response, self.wr_no)

    def test_form_content_partial_view_get(self):
        response = self.client.get(reverse('sales_distribution:workorderdispatch_form_content', args=[self.wo_no, self.wr_no]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorder_dispatch/_dispatch_form_content.html')
        self.assertContains(response, 'id="itemsDataTable"')
        self.assertContains(response, 'id="employeesDataTable"')
        self.assertContains(response, 'John Doe') # Ensure valid employee is listed
        self.assertNotContains(response, 'Jane Smith') # Ensure invalid employee is not listed
        self.assertNotContains(response, 'System User') # Ensure user_id=1 is not listed
        self.assertContains(response, 'ITEM001') # Ensure item is listed

    @patch('sales_distribution.tasks.generate_dispatch_report_and_email.delay')
    def test_process_dispatch_view_post_success(self, mock_delay):
        initial_dispatch_count = WorkOrderDispatch.objects.count()
        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'item_checked_{self.wr_id_1}': 'on',
            f'item_dispatch_qty_{self.wr_id_1}': '10.500',
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        response = self.client.post(reverse('sales_distribution:workorderdispatch_process', args=[self.wo_no, self.wr_no]), data, HTTP_HX_Request='true')

        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertEqual(response['HX-Redirect'], reverse('sales_distribution:workorder_dispatch_list'))
        self.assertEqual(WorkOrderDispatch.objects.count(), initial_dispatch_count + 1)
        mock_delay.assert_called_once()
        self.assertIn(b'HX-Redirect', response.headers)

    def test_process_dispatch_view_post_invalid_form(self):
        initial_dispatch_count = WorkOrderDispatch.objects.count()
        # Invalid data: no items checked
        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        response = self.client.post(reverse('sales_distribution:workorderdispatch_process', args=[self.wo_no, self.wr_no]), data, HTTP_HX_Request='true')

        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_distribution/workorder_dispatch/_dispatch_form_content.html')
        self.assertContains(response, "At least one item must be selected for dispatch.")
        self.assertEqual(WorkOrderDispatch.objects.count(), initial_dispatch_count) # No dispatch created

    def test_process_dispatch_view_post_no_htmx_request(self):
        # Test direct POST, should behave like a normal redirect on success (though in this case it's 204)
        data = {
            'wo_no': self.wo_no,
            'wr_no': self.wr_no,
            'freight_charges': 'Customer',
            'vehicle_by': 'Self',
            'octroi_charges': 'Customer',
            f'item_checked_{self.wr_id_1}': 'on',
            f'item_dispatch_qty_{self.wr_id_1}': '10.500',
            f'employee_checked_{self.emp_id_valid}': 'on',
        }
        response = self.client.post(reverse('sales_distribution:workorderdispatch_process', args=[self.wo_no, self.wr_no]), data)
        self.assertEqual(response.status_code, 204) # Still 204 as HX-Redirect is handled by header, not typical Django redirect
        self.assertIn(b'HX-Redirect', response.headers)

    def test_employees_table_partial_view_get(self):
        response = self.client.get(reverse('sales_distribution:workorderdispatch_employees_table'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorder_dispatch/_employees_table.html')
        self.assertContains(response, 'John Doe')
        self.assertNotContains(response, 'Jane Smith') # Not DA=1
        self.assertNotContains(response, 'System User') # UserID=1

    def test_items_table_partial_view_get(self):
        response = self.client.get(reverse('sales_distribution:workorderdispatch_items_table', args=[self.wo_no, self.wr_no]))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_distribution/workorder_dispatch/_items_table.html')
        self.assertContains(response, 'ITEM001')
        self.assertContains(response, '80.000') # Released Qty
        self.assertContains(response, '0.000')  # DA Total Qty (initially)
        self.assertContains(response, '80.000') # DA Remain Qty (initially)

```

#### 4.7 Background Tasks (`sales_distribution/tasks.py`)
For Crystal Reports and email, use Celery to offload heavy processing from the web request cycle.

```python
from celery import shared_task
import logging
from django.conf import settings
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.db.models import Sum, F, Q
from .models import WorkOrderDispatch, WorkOrderMaster, WorkOrderProductDetail, Employee, CompanyMaster, CustomerMaster, PurchaseOrderMaster
from django.utils import timezone
from decimal import Decimal

logger = logging.getLogger(__name__)

# Dummy helper functions for reporting, mimicking clsFunctions
class ReportHelperFunctions:
    @staticmethod
    def get_company(comp_id):
        try:
            return CompanyMaster.objects.get(comp_id=comp_id).__str__() # Assuming __str__ returns name
        except CompanyMaster.DoesNotExist:
            return "Unknown Company"

    @staticmethod
    def get_city(city_id, type_id=None):
        # In a real app, this would query a City/Location model
        return f"City {city_id}" if city_id else "N/A"

    @staticmethod
    def get_state(state_id, type_id=None):
        # In a real app, this would query a State/Location model
        return f"State {state_id}" if state_id else "N/A"

    @staticmethod
    def get_country(country_id, type_id=None):
        # In a real app, this would query a Country/Location model
        return f"Country {country_id}" if country_id else "N/A"

    @staticmethod
    def comp_add(comp_id):
        # In a real app, this would query CompanyMaster for address fields
        return f"Company Address for {comp_id}"

    @staticmethod
    def from_date(date_str):
        if not date_str:
            return "N/A"
        try:
            # Assuming date_str is in 'YYYY-MM-DD' or similar parseable format
            return timezone.datetime.strptime(date_str, "%Y-%m-%d").strftime("%d/%m/%Y")
        except ValueError:
            return date_str # Return as is if format fails

report_fun = ReportHelperFunctions()


@shared_task
def generate_dispatch_report_and_email(dano, wo_no, wr_no, comp_id, selected_employee_ids):
    """
    Background task to generate the dispatch report (PDF) and send emails.
    This replaces the Crystal Reports and email sending logic.
    """
    logger.info(f"Starting report generation and email for DANo: {dano}")

    try:
        # 1. Gather data for the report (mimicking ASP.NET's data preparation for Crystal Report)
        dispatch_records = WorkOrderDispatch.objects.filter(dano=dano, comp_id=comp_id)
        if not dispatch_records.exists():
            logger.warning(f"No dispatch records found for DANo: {dano}, CompId: {comp_id}")
            return

        report_items = []
        for record in dispatch_records:
            try:
                product_detail = WorkOrderProductDetail.objects.get(id=record.item_id, comp_id=comp_id)
                wo_master = WorkOrderMaster.objects.get(wo_no=wo_no, comp_id=comp_id) # Assuming WO No links directly to WO Master

                report_items.append({
                    'Id': record.id,
                    'ItemCode': product_detail.item_code,
                    'Description': product_detail.description,
                    'IssuedQty': record.issued_qty,
                    'DispatchQty': record.dispatch_qty,
                    'WONo': wo_master.wo_no, # Or record.wo_no if from dispatch table
                    'DANo': record.dano,
                    'CompId': record.comp_id,
                })
            except (WorkOrderProductDetail.DoesNotExist, WorkOrderMaster.DoesNotExist) as e:
                logger.error(f"Missing related data for dispatch record ID {record.id}: {e}")
                continue

        # Gather parameter data for report (mimicking ASP.NET's parameter setting)
        report_params = {
            "Company": report_fun.get_company(comp_id),
            "Address": report_fun.comp_add(comp_id),
            "WRDate": "N/A", # Will be set below
            "WODate": "N/A", # Will be set below
            "CustInspection_FD": "N/A",
            "CustInspection_TD": "N/A",
            "PODate": "N/A",
            # Add other parameters required by the report template
        }

        # Customer details
        try:
            wo_master = WorkOrderMaster.objects.get(wo_no=wo_no, comp_id=comp_id)
            customer_master = CustomerMaster.objects.get(customer_id=wo_master.customer_id, comp_id=comp_id)
            report_params["RegCity"] = report_fun.get_city(customer_master.regd_city_id)
            report_params["RegState"] = report_fun.get_state(customer_master.regd_state_id)
            report_params["RegCountry"] = report_fun.get_country(customer_master.regd_country_id)
            # Add other customer related cities/states/countries if applicable
            report_params["WrkCity"] = report_fun.get_city(customer_master.work_city_id) if hasattr(customer_master, 'work_city_id') else "N/A"
            report_params["WrkState"] = report_fun.get_state(customer_master.work_state_id) if hasattr(customer_master, 'work_state_id') else "N/A"
            report_params["WrkCountry"] = report_fun.get_country(customer_master.work_country_id) if hasattr(customer_master, 'work_country_id') else "N/A"
            report_params["DelCity"] = report_fun.get_city(customer_master.material_del_city_id) if hasattr(customer_master, 'material_del_city_id') else "N/A"
            report_params["DelState"] = report_fun.get_state(customer_master.material_del_state_id) if hasattr(customer_master, 'material_del_state_id') else "N/A"
            report_params["DelCountry"] = report_fun.get_country(customer_master.material_del_country_id) if hasattr(customer_master, 'material_del_country_id') else "N/A"

            report_params["WODate"] = report_fun.from_date(wo_master.task_work_order_date)
            report_params["CustInspection_FD"] = report_fun.from_date(wo_master.task_cust_inspection_fdate)
            report_params["CustInspection_TD"] = report_fun.from_date(wo_master.task_cust_inspection_tdate)

            # PO Date
            if wo_master.po_no:
                po_master = PurchaseOrderMaster.objects.filter(po_no=wo_master.po_no, comp_id=comp_id).first()
                if po_master:
                    report_params["PODate"] = report_fun.from_date(po_master.po_date)

        except (WorkOrderMaster.DoesNotExist, CustomerMaster.DoesNotExist) as e:
            logger.warning(f"Could not retrieve customer/WO master details for report: {e}")

        # Work Order Release Date
        wr_record = WorkOrderRelease.objects.filter(wr_no=wr_no, wo_no=wo_no, comp_id=comp_id).first()
        if wr_record:
            report_params["WRDate"] = report_fun.from_date(wr_record.sys_date)


        # 2. Generate PDF Report (using Django template to HTML, then convert to PDF)
        # This part assumes you have a PDF generation library like WeasyPrint or ReportLab configured.
        # For simplicity, we'll just simulate the process.
        report_context = {
            'dano': dano,
            'report_items': report_items,
            'params': report_params,
        }
        html_content = render_to_string('sales_distribution/reports/dispatch_report.html', report_context)

        # In a real app:
        # from weasyprint import HTML
        # pdf_file_path = f"{settings.MEDIA_ROOT}/temppdf/WorkOrderDispatch_{wo_no}_{wr_no}_{dano}.pdf"
        # HTML(string=html_content).write_pdf(pdf_file_path)

        # For this example, we simulate a PDF path
        pdf_file_path = f"/tmp/WorkOrderDispatch_{wo_no}_{wr_no}_{dano}.pdf"
        # Simulate PDF creation by writing a dummy file
        with open(pdf_file_path, 'w') as f:
            f.write("Simulated PDF content for Work Order Dispatch")

        logger.info(f"Simulated PDF report generated at: {pdf_file_path}")

        # 3. Send Emails
        company_settings = CompanyMaster.objects.filter(comp_id=comp_id).first()
        erp_mail = company_settings.erp_sys_mail if company_settings else settings.DEFAULT_FROM_EMAIL
        mail_server = company_settings.mail_server_ip if company_settings and company_settings.mail_server_ip else settings.EMAIL_HOST

        # Ensure Django's mail settings are configured (EMAIL_HOST, EMAIL_PORT, etc.)
        # For this example, we mock SMTP server.
        # settings.EMAIL_HOST = mail_server # This would be set dynamically

        email_recipients = []
        for emp_id in selected_employee_ids:
            try:
                employee = Employee.objects.get(emp_id=emp_id)
                if employee.email_id1:
                    email_recipients.append(employee.email_id1)
            except Employee.DoesNotExist:
                logger.warning(f"Employee ID {emp_id} not found for email notification.")

        if not email_recipients:
            logger.warning("No valid email recipients found for dispatch notification. Sending to ERP system mail.")
            email_recipients = [erp_mail]
        else:
            # If ERP mail is not already in recipients, add it as BCC or To depending on policy
            if erp_mail not in email_recipients:
                email_recipients.append(erp_mail) # Add ERP mail as recipient for tracking/backup

        subject = f"Work Order Dispatch WONo: {wo_no} WRNo: {wr_no} DANo: {dano}"
        body = "Dear Sir,<br><br>This is an auto-generated mail by the ERP system, please do not reply.<br><br>Thank you."

        email = EmailMessage(
            subject,
            body,
            from_email=erp_mail,
            to=email_recipients,
        )
        email.content_subtype = "html" # Main content is HTML

        # Attach PDF
        with open(pdf_file_path, 'rb') as f:
            email.attach(f'WorkOrderDispatch_{wo_no}_{wr_no}_{dano}.pdf', f.read(), 'application/pdf')

        email.send(fail_silently=False)
        logger.info(f"Dispatch email sent successfully for DANo: {dano} to {email_recipients}")

    except Exception as e:
        logger.exception(f"Failed to generate report or send email for DANo: {dano}: {e}")
    finally:
        # Clean up generated PDF file
        import os
        if os.path.exists(pdf_file_path):
            os.remove(pdf_file_path)
            logger.info(f"Cleaned up temporary PDF file: {pdf_file_path}")

```

#### 4.8 Report Template (`sales_distribution/templates/sales_distribution/reports/dispatch_report.html`)
This is a basic HTML template for the report, which would be rendered to PDF.

```html
<!DOCTYPE html>
<html>
<head>
    <title>Work Order Dispatch Report - DANo: {{ dano }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20mm; font-size: 10pt; }
        h1, h2, h3 { color: #333; }
        table { width: 100%; border-collapse: collapse; margin-bottom: 10mm; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .header-info { margin-bottom: 15mm; }
        .footer-address { margin-top: 20mm; text-align: center; font-size: 8pt; color: #555; }
        .right-align { text-align: right; }
        .center-align { text-align: center; }
    </style>
</head>
<body>
    <div class="header-info">
        <h1 style="text-align: center;">Work Order Dispatch Acknowledgement</h1>
        <p><strong>Company:</strong> {{ params.Company }}</p>
        <p><strong>Dispatch No (DANo):</strong> {{ dano }}</p>
        <p><strong>Work Order No (WONo):</strong> {{ params.WONo }}</p>
        <p><strong>Work Order Release No (WRNo):</strong> {{ params.WRNo }}</p>
        <p><strong>Dispatch Date:</strong> {{ dano|slice:"4:12"|default:current_date }} (from DANo)</p>
        <p><strong>WO Date:</strong> {{ params.WODate }}</p>
        <p><strong>WR Date:</strong> {{ params.WRDate }}</p>
        <p><strong>PO Date:</strong> {{ params.PODate }}</p>
        <p><strong>Customer:</strong> {{ params.CustomerName }} (Assuming this would be passed)</p>
        <p><strong>Registered Address:</strong> {{ params.RegCity }}, {{ params.RegState }}, {{ params.RegCountry }}</p>
        <p><strong>Work Location:</strong> {{ params.WrkCity }}, {{ params.WrkState }}, {{ params.WrkCountry }}</p>
        <p><strong>Delivery Address:</strong> {{ params.DelCity }}, {{ params.DelState }}, {{ params.DelCountry }}</p>
        <p><strong>Customer Inspection Dates:</strong> {{ params.CustInspection_FD }} to {{ params.CustInspection_TD }}</p>
        <p><strong>Freight Charges by:</strong> {{ dispatch_items.0.FreightCharges|default:'N/A' }}</p>
        <p><strong>Vehicle by:</strong> {{ dispatch_items.0.Vehicleby|default:'N/A' }}</p>
        <p><strong>Octroi Charges by:</strong> {{ dispatch_items.0.OctroiCharges|default:'N/A' }}</p>
    </div>

    <h2>Dispatch Items:</h2>
    <table>
        <thead>
            <tr>
                <th>SN</th>
                <th>Item Code</th>
                <th>Description</th>
                <th class="right-align">Issued Qty (Release)</th>
                <th class="right-align">Dispatch Qty</th>
            </tr>
        </thead>
        <tbody>
            {% for item in report_items %}
            <tr>
                <td>{{ forloop.counter }}</td>
                <td>{{ item.ItemCode }}</td>
                <td>{{ item.Description }}</td>
                <td class="right-align">{{ item.IssuedQty|floatformat:3 }}</td>
                <td class="right-align">{{ item.DispatchQty|floatformat:3 }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <div class="footer-address">
        <p>{{ params.Address }}</p>
        <p>This is an auto-generated report. No signature required.</p>
    </div>
</body>
</html>
```

### Step 5: HTMX and Alpine.js Integration

**Instructions & Implementation:**

*   **Initial Page Load:** The `WorkOrderDispatchDetailView` renders `detail.html`. This `detail.html` uses `hx-get` on a container (`#dispatch-form-container`) to load the actual form content (`_dispatch_form_content.html`) from `workorderdispatch_form_content` URL. This provides a single-page feel even on the initial load.
*   **Loading Indicator:** A `div` with `id="loading-indicator"` is used with `hx-indicator` on the main form to show a spinner during HTMX requests (e.g., initial load, form submission). It is initially displayed and hidden after `htmx:afterOnLoad`.
*   **Form Submission:** The main dispatch form has `hx-post` pointing to `workorderdispatch_process`.
    *   `hx-swap="outerHTML" hx-target="#dispatch-form-container"`: This instructs HTMX to replace the entire form container with the response from the server. If validation errors occur, the view re-renders the form with errors; otherwise, on success, it sends a 204 response with `HX-Redirect` header.
*   **Success Redirect:** On successful dispatch, the `WorkOrderProcessDispatchView` returns a `HttpResponse(status=204)` with an `HX-Redirect` header. HTMX intercepts this and performs a full page redirect to the `workorder_dispatch_list` URL, providing a clean navigation.
*   **DataTables Integration:**
    *   DataTables are initialized on the `itemsDataTable` and `employeesDataTable` elements. Since these tables are loaded dynamically via HTMX, the DataTables initialization script is placed in the `extra_js` block of `base.html` and triggered by `htmx:afterSwap` event listener. This ensures DataTables runs only after the dynamic content is loaded into the DOM.
    *   The `min-w-full` class ensures tables take full width and are responsive.
*   **Alpine.js for Client-Side Interactivity (Minimal):**
    *   Alpine.js is included in `base.html` (`defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"`).
    *   For this specific scenario, a direct confirmation dialog (`confDyna`) is implemented using a standard JavaScript `confirm()` on the submit button's click event listener, wrapped to ensure HTMX can still take over if confirmed. For more complex modal interactions, Alpine.js's `x-data` and `x-show` would be used on a modal element.
*   **DRY Templates:** The `_dispatch_form_content.html` serves as a reusable partial that contains the form, tables, and radio buttons. It's swapped in and out by HTMX, ensuring consistency and preventing code duplication.
*   **No Custom JavaScript (beyond initialization/HTMX glue):** All dynamic interactions are driven by HTMX attributes or standard browser behavior (like `confirm()`), minimizing the need for complex, module-specific JavaScript files.

This comprehensive plan outlines a clear, automated path for migrating the ASP.NET Work Order Dispatch module to a modern Django application, focusing on efficiency, maintainability, and enhanced user experience.