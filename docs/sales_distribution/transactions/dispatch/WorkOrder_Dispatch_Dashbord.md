## ASP.NET to Django Conversion Script:

This plan outlines a modern Django-based solution for the "Work Order Dispatch Dashboard" application, leveraging automated migration strategies and AI-assisted tools for efficiency. The original ASP.NET code provides a minimal structure, primarily defining content placeholders. This suggests the actual logic and UI elements were either in user controls, external includes, or dynamically generated, none of which are present in the provided snippet.

Therefore, for a meaningful modernization plan, we will infer the typical functionalities of a Work Order Dispatch Dashboard. This involves managing (Create, Read, Update, Delete) work orders, presenting them in a sortable, searchable list, and enabling dynamic interactions without full page reloads.

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

## Conversion Steps:

### Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

Instructions:
Given the minimal ASP.NET code, no explicit database schema or SQL commands are present. We will infer a common database table for "Work Orders" based on the page's name: `WorkOrder_Dispatch_Dashbord`.

**Inferred Database Details:**

*   **Table Name:** `tblWorkOrder` (A common ASP.NET naming convention for tables is `tbl` prefix).
*   **Columns:**
    *   `WorkOrderID` (Primary Key, Integer)
    *   `OrderNumber` (String, e.g., WO-2023-001)
    *   `CustomerName` (String)
    *   `DispatchDate` (Date/DateTime)
    *   `Status` (String, e.g., 'Pending', 'Dispatched', 'Completed')
    *   `AssignedTo` (String, e.g., 'Dispatcher A')
    *   `TotalAmount` (Decimal)
    *   `Description` (Text)

### Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

Instructions:
The provided C# code-behind only contains an empty `Page_Load` method, indicating no explicit backend functionality or CRUD operations defined in this specific file. However, for a "Dashboard" page, it's a fundamental business requirement to view, add, modify, and potentially delete work orders.

**Inferred Functionality:**

*   **Read (R):** Display a list of all work orders. This is the core dashboard functionality.
*   **Create (C):** Ability to add new work orders.
*   **Update (U):** Ability to edit existing work order details.
*   **Delete (D):** Ability to remove work orders.
*   **Status Management:** Implicitly, the dashboard should allow viewing and possibly changing the dispatch status of work orders.

### Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

Instructions:
The `.aspx` file is empty, providing no UI control definitions. Based on the "Dashboard" name and typical enterprise application patterns, we infer the following UI components for the Django replacement:

**Inferred UI Components:**

*   **Data Table (replacing GridView):** To display a list of `WorkOrder` records with columns for `OrderNumber`, `CustomerName`, `DispatchDate`, `Status`, `AssignedTo`, and `TotalAmount`. This will be implemented using DataTables for client-side features.
*   **Action Buttons:**
    *   "Add New Work Order" button: To open a form for creating new records.
    *   "Edit" button (per row): To open a form for modifying existing records.
    *   "Delete" button (per row): To open a confirmation dialog for deleting records.
*   **Input Forms (replacing TextBoxes, DropDownLists):**
    *   Text inputs for `OrderNumber`, `CustomerName`, `AssignedTo`, `TotalAmount`, `Description`.
    *   Date picker for `DispatchDate`.
    *   Dropdown/Select for `Status` (with options like 'Pending', 'Dispatched', 'Completed').
*   **Modals:** For creating, editing, and deleting operations, utilizing HTMX for dynamic content loading and Alpine.js for modal state management.

### Step 4: Generate Django Code

We will create a new Django app named `workorders` to house this functionality.

#### 4.1 Models

Task: Create a Django model based on the inferred database schema.

Instructions:
The `WorkOrder` model will map to the `tblWorkOrder` table.

```python
# workorders/models.py
from django.db import models

class WorkOrder(models.Model):
    # Define choices for the 'Status' field
    STATUS_CHOICES = [
        ('Pending', 'Pending'),
        ('Dispatched', 'Dispatched'),
        ('Completed', 'Completed'),
        ('Cancelled', 'Cancelled'), # Added an extra status for completeness
    ]

    # Primary key, assuming it's an auto-incrementing integer in the legacy DB
    # Django will automatically create an 'id' field if not specified.
    # We map common database column names to Django field names.
    order_number = models.CharField(max_length=50, unique=True, db_column='OrderNumber', verbose_name='Order Number')
    customer_name = models.CharField(max_length=100, db_column='CustomerName', verbose_name='Customer Name')
    dispatch_date = models.DateField(db_column='DispatchDate', verbose_name='Dispatch Date')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='Pending', db_column='Status')
    assigned_to = models.CharField(max_length=100, blank=True, null=True, db_column='AssignedTo', verbose_name='Assigned To')
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, db_column='TotalAmount', verbose_name='Total Amount')
    description = models.TextField(blank=True, null=True, db_column='Description')

    class Meta:
        managed = False  # Tells Django not to manage this table's creation/alteration
        db_table = 'tblWorkOrder'  # Maps to the existing database table
        verbose_name = 'Work Order'
        verbose_name_plural = 'Work Orders'
        ordering = ['-dispatch_date', 'order_number'] # Default ordering for list views

    def __str__(self):
        """Returns a string representation of the WorkOrder object."""
        return f'{self.order_number} - {self.customer_name}'

    def is_dispatch_pending(self):
        """
        Business logic: Checks if the work order is pending dispatch.
        This method keeps view logic minimal.
        """
        return self.status == 'Pending'

    def update_status(self, new_status):
        """
        Business logic: Updates the work order status.
        Ensures status transitions are valid if needed (not implemented here for simplicity).
        """
        if new_status in [choice[0] for choice in self.STATUS_CHOICES]:
            self.status = new_status
            self.save()
            return True
        return False
```

#### 4.2 Forms

Task: Define a Django form for user input.

Instructions:
A `ModelForm` will be created for the `WorkOrder` model, including all necessary fields and styling with Tailwind CSS.

```python
# workorders/forms.py
from django import forms
from .models import WorkOrder

class WorkOrderForm(forms.ModelForm):
    dispatch_date = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date', 'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
        label='Dispatch Date'
    )
    
    class Meta:
        model = WorkOrder
        fields = ['order_number', 'customer_name', 'dispatch_date', 'status', 'assigned_to', 'total_amount', 'description']
        widgets = {
            'order_number': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'customer_name': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'status': forms.Select(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'assigned_to': forms.TextInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'total_amount': forms.NumberInput(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm'}),
            'description': forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 3}),
        }
    
    def clean_total_amount(self):
        """
        Custom validation for total_amount, ensuring it's not negative.
        """
        total_amount = self.cleaned_data.get('total_amount')
        if total_amount is not None and total_amount < 0:
            raise forms.ValidationError("Total amount cannot be negative.")
        return total_amount

```

#### 4.3 Views

Task: Implement CRUD operations using CBVs and a partial view for the DataTables table.

Instructions:
Views will be kept thin, delegating business logic to the `WorkOrder` model. HTMX headers will be used for seamless updates.

```python
# workorders/views.py
from django.views.generic import ListView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib import messages
from django.http import HttpResponse
from .models import WorkOrder
from .forms import WorkOrderForm

class WorkOrderListView(ListView):
    model = WorkOrder
    template_name = 'workorders/workorder/list.html'
    context_object_name = 'workorders' # Renamed for clarity in templates

class WorkOrderTablePartialView(ListView):
    model = WorkOrder
    template_name = 'workorders/workorder/_workorder_table.html'
    context_object_name = 'workorders'

    def get_queryset(self):
        # This can be extended to include filtering, sorting based on request.GET
        # For DataTables, the sorting/filtering often happens on the client-side
        # but server-side processing could be integrated here if needed.
        return super().get_queryset()

class WorkOrderCreateView(CreateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'workorders/workorder/_workorder_form.html' # Use partial template
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order added successfully.')
        if self.request.headers.get('HX-Request'):
            # Return no content for HTMX to trigger a client-side refresh
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

class WorkOrderUpdateView(UpdateView):
    model = WorkOrder
    form_class = WorkOrderForm
    template_name = 'workorders/workorder/_workorder_form.html' # Use partial template
    context_object_name = 'workorder' # Ensures the object is available to the form
    success_url = reverse_lazy('workorder_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Work Order updated successfully.')
        if self.request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

class WorkOrderDeleteView(DeleteView):
    model = WorkOrder
    template_name = 'workorders/workorder/_workorder_confirm_delete.html' # Use partial template
    context_object_name = 'workorder'
    success_url = reverse_lazy('workorder_list')

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Work Order deleted successfully.')
        if request.headers.get('HX-Request'):
            return HttpResponse(
                status=204,
                headers={
                    'HX-Trigger': 'refreshWorkOrderList'
                }
            )
        return response

```

#### 4.4 Templates

Task: Create templates for each view.

Instructions:
Templates will use `core/base.html` for structure and HTMX for dynamic content loading, minimizing full page reloads. DataTables will manage the list display.

```html
{# workorders/templates/workorders/workorder/list.html #}
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Work Order Dashboard</h2>
        <button 
            class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-300 ease-in-out"
            hx-get="{% url 'workorder_add' %}"
            hx-target="#modalContent"
            hx-trigger="click"
            _="on click add .is-active to #modal">
            Add New Work Order
        </button>
    </div>
    
    <div id="workorderTable-container"
         hx-trigger="load, refreshWorkOrderList from:body"
         hx-get="{% url 'workorder_table' %}"
         hx-swap="innerHTML">
        <!-- DataTables will be loaded here via HTMX -->
        <div class="text-center py-10">
            <div class="inline-block animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-blue-500"></div>
            <p class="mt-4 text-gray-600">Loading Work Orders...</p>
        </div>
    </div>
    
    <!-- Modal for form/delete confirmation -->
    <div id="modal" class="fixed inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center hidden is-active:flex animated fadeIn faster"
         _="on click if event.target.id == 'modal' remove .is-active from me">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-2xl max-w-2xl w-full mx-4 animated zoomIn"
             _="on closeModal add .hidden to #modal">
            <!-- Content loaded here by HTMX -->
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Example Alpine.js for global UI state if needed, though most modal logic is in htmx/hyperscript
    document.addEventListener('alpine:init', () => {
        Alpine.data('workOrderDashboard', () => ({
            // No specific state needed here as HTMX/Hyperscript handles modal
        }));
    });
</script>
{% endblock %}
```

```html
{# workorders/templates/workorders/workorder/_workorder_table.html #}
<div class="overflow-x-auto bg-white shadow-lg rounded-lg p-4">
    <table id="workorderTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-100">
            <tr>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">SN</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Order Number</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Customer Name</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Dispatch Date</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Status</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Assigned To</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Total Amount</th>
                <th class="py-3 px-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="divide-y divide-gray-200">
            {% for workorder in workorders %}
            <tr class="hover:bg-gray-50">
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ forloop.counter }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ workorder.order_number }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ workorder.customer_name }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ workorder.dispatch_date|date:"Y-m-d" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm font-medium">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    {% if workorder.status == 'Pending' %}bg-yellow-100 text-yellow-800
                    {% elif workorder.status == 'Dispatched' %}bg-blue-100 text-blue-800
                    {% elif workorder.status == 'Completed' %}bg-green-100 text-green-800
                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                        {{ workorder.status }}
                    </span>
                </td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ workorder.assigned_to|default:"N/A" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm text-gray-800">{{ workorder.total_amount|default:"0.00" }}</td>
                <td class="py-3 px-4 whitespace-nowrap text-sm">
                    <button 
                        class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-1 px-3 rounded text-xs mr-2 transition duration-200 ease-in-out"
                        hx-get="{% url 'workorder_edit' workorder.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Edit
                    </button>
                    <button 
                        class="bg-red-500 hover:bg-red-600 text-white font-bold py-1 px-3 rounded text-xs transition duration-200 ease-in-out"
                        hx-get="{% url 'workorder_delete' workorder.pk %}"
                        hx-target="#modalContent"
                        hx-trigger="click"
                        _="on click add .is-active to #modal">
                        Delete
                    </button>
                </td>
            </tr>
            {% empty %}
            <tr>
                <td colspan="8" class="text-center py-8 text-gray-500">No work orders found.</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<script>
    // Initialize DataTables after HTMX loads the content
    $(document).ready(function() {
        $('#workorderTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "responsive": true
        });
    });
</script>
```

```html
{# workorders/templates/workorders/workorder/_workorder_form.html #}
<div class="p-6">
    <h3 class="text-2xl font-medium text-gray-900 mb-6">{{ form.instance.pk|yesno:'Edit,Add' }} Work Order</h3>
    <form hx-post="{{ request.path }}" hx-swap="none" 
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {% for field in form %}
            <div class="mb-4">
                <label for="{{ field.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    {{ field.label }}
                    {% if field.field.required %}
                        <span class="text-red-500">*</span>
                    {% endif %}
                </label>
                {{ field }}
                {% if field.help_text %}
                <p class="mt-1 text-sm text-gray-500">{{ field.help_text }}</p>
                {% endif %}
                {% if field.errors %}
                <ul class="mt-1 text-red-500 text-xs">
                    {% for error in field.errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                </ul>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        
        <div class="mt-8 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-200 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200 ease-in-out">
                Save Work Order
            </button>
        </div>
    </form>
</div>
```

```html
{# workorders/templates/workorders/workorder/_workorder_confirm_delete.html #}
<div class="p-6">
    <h3 class="text-xl font-medium text-gray-900 mb-5">Confirm Deletion</h3>
    <p class="text-gray-700 mb-6">Are you sure you want to delete the work order: 
        <span class="font-semibold text-red-600">{{ workorder.order_number }} - {{ workorder.customer_name }}</span>?
        This action cannot be undone.
    </p>
    <form hx-post="{% url 'workorder_delete' workorder.pk %}" hx-swap="none"
          hx-on::after-request="if(event.detail.xhr.status === 204) { document.getElementById('modal').classList.remove('is-active'); }">
        {% csrf_token %}
        <div class="flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded-lg shadow-sm transition duration-200 ease-in-out"
                _="on click remove .is-active from #modal">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded-lg shadow-md transition duration-200 ease-in-out">
                Delete
            </button>
        </div>
    </form>
</div>
```

#### 4.5 URLs

Task: Define URL patterns for the views.

Instructions:
URL patterns will be set up in `workorders/urls.py`.

```python
# workorders/urls.py
from django.urls import path
from .views import WorkOrderListView, WorkOrderCreateView, WorkOrderUpdateView, WorkOrderDeleteView, WorkOrderTablePartialView

urlpatterns = [
    # Main dashboard view displaying the list
    path('workorders/', WorkOrderListView.as_view(), name='workorder_list'),
    
    # HTMX endpoint for refreshing the table content
    path('workorders/table/', WorkOrderTablePartialView.as_view(), name='workorder_table'),

    # HTMX endpoint for adding a new work order (GET for form, POST for submission)
    path('workorders/add/', WorkOrderCreateView.as_view(), name='workorder_add'),
    
    # HTMX endpoint for editing an existing work order
    path('workorders/edit/<int:pk>/', WorkOrderUpdateView.as_view(), name='workorder_edit'),
    
    # HTMX endpoint for deleting a work order
    path('workorders/delete/<int:pk>/', WorkOrderDeleteView.as_view(), name='workorder_delete'),
]
```

#### 4.6 Tests

Task: Write tests for the model and views.

Instructions:
Comprehensive unit tests for model methods and integration tests for all view interactions will be included to ensure high test coverage.

```python
# workorders/tests.py
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.messages import get_messages
from .models import WorkOrder
from datetime import date

class WorkOrderModelTest(TestCase):
    """
    Unit tests for the WorkOrder model, including field attributes and custom methods.
    """
    @classmethod
    def setUpTestData(cls):
        # Set up non-modified objects used by all test methods
        WorkOrder.objects.create(
            order_number='WO-2023-001',
            customer_name='ABC Corp',
            dispatch_date=date(2023, 1, 15),
            status='Pending',
            assigned_to='John Doe',
            total_amount='1500.75',
            description='Installation of new network infrastructure.'
        )
        WorkOrder.objects.create(
            order_number='WO-2023-002',
            customer_name='XYZ Ltd',
            dispatch_date=date(2023, 1, 20),
            status='Dispatched',
            assigned_to='Jane Smith',
            total_amount='500.00',
            description='Emergency repair service.'
        )

    def test_workorder_creation(self):
        """Test that a WorkOrder instance can be created and its attributes are correct."""
        wo = WorkOrder.objects.get(order_number='WO-2023-001')
        self.assertEqual(wo.customer_name, 'ABC Corp')
        self.assertEqual(wo.status, 'Pending')
        self.assertEqual(wo.dispatch_date, date(2023, 1, 15))
        self.assertEqual(str(wo.total_amount), '1500.75') # Decimal fields return Decimal objects
        self.assertEqual(wo.description, 'Installation of new network infrastructure.')

    def test_order_number_label(self):
        """Verify the verbose name for the order_number field."""
        wo = WorkOrder.objects.get(order_number='WO-2023-001')
        field_label = wo._meta.get_field('order_number').verbose_name
        self.assertEqual(field_label, 'Order Number')

    def test_customer_name_label(self):
        """Verify the verbose name for the customer_name field."""
        wo = WorkOrder.objects.get(order_number='WO-2023-001')
        field_label = wo._meta.get_field('customer_name').verbose_name
        self.assertEqual(field_label, 'Customer Name')

    def test_db_table_and_managed(self):
        """Ensure Meta options for db_table and managed are correctly set."""
        self.assertEqual(WorkOrder._meta.db_table, 'tblWorkOrder')
        self.assertFalse(WorkOrder._meta.managed)

    def test_str_method(self):
        """Test the __str__ method returns the expected string."""
        wo = WorkOrder.objects.get(order_number='WO-2023-001')
        self.assertEqual(str(wo), 'WO-2023-001 - ABC Corp')

    def test_is_dispatch_pending_method(self):
        """Test the custom business logic method is_dispatch_pending."""
        wo_pending = WorkOrder.objects.get(order_number='WO-2023-001')
        wo_dispatched = WorkOrder.objects.get(order_number='WO-2023-002')
        self.assertTrue(wo_pending.is_dispatch_pending())
        self.assertFalse(wo_dispatched.is_dispatch_pending())

    def test_update_status_method(self):
        """Test the custom business logic method update_status."""
        wo = WorkOrder.objects.get(order_number='WO-2023-001')
        self.assertTrue(wo.update_status('Completed'))
        self.assertEqual(wo.status, 'Completed')
        self.assertFalse(wo.update_status('InvalidStatus')) # Test invalid status
        self.assertEqual(wo.status, 'Completed') # Status should not change for invalid input

class WorkOrderViewsTest(TestCase):
    """
    Integration tests for WorkOrder views, covering CRUD operations and HTMX interactions.
    """
    @classmethod
    def setUpTestData(cls):
        # Create initial test data for views
        cls.workorder1 = WorkOrder.objects.create(
            order_number='WO-TEST-001',
            customer_name='Test Customer 1',
            dispatch_date=date(2023, 2, 1),
            status='Pending',
            assigned_to='Tester 1',
            total_amount='100.00'
        )
        cls.workorder2 = WorkOrder.objects.create(
            order_number='WO-TEST-002',
            customer_name='Test Customer 2',
            dispatch_date=date(2023, 2, 2),
            status='Dispatched',
            assigned_to='Tester 2',
            total_amount='200.00'
        )
    
    def setUp(self):
        self.client = Client()

    def test_workorder_list_view_get(self):
        """Test the WorkOrder list view renders correctly and includes work orders."""
        response = self.client.get(reverse('workorder_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder/list.html')
        self.assertIn('workorders', response.context)
        self.assertQuerysetEqual(response.context['workorders'].order_by('order_number'), 
                                 [repr(self.workorder1), repr(self.workorder2)], ordered=False)

    def test_workorder_table_partial_view_get(self):
        """Test the HTMX partial view for the table content."""
        response = self.client.get(reverse('workorder_table'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder/_workorder_table.html')
        self.assertIn('workorders', response.context)
        self.assertContains(response, 'WO-TEST-001')
        self.assertContains(response, 'WO-TEST-002')

    def test_workorder_create_view_get(self):
        """Test GET request for the create form loads correctly."""
        response = self.client.get(reverse('workorder_add'), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder/_workorder_form.html')
        self.assertIn('form', response.context)

    def test_workorder_create_view_post_success(self):
        """Test successful POST request to create a new work order."""
        initial_count = WorkOrder.objects.count()
        data = {
            'order_number': 'WO-NEW-003',
            'customer_name': 'New Customer',
            'dispatch_date': '2023-03-01',
            'status': 'Pending',
            'assigned_to': 'New Guy',
            'total_amount': '750.50',
            'description': 'New work order for testing.'
        }
        response = self.client.post(reverse('workorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # HTMX No Content response
        self.assertTrue(WorkOrder.objects.filter(order_number='WO-NEW-003').exists())
        self.assertEqual(WorkOrder.objects.count(), initial_count + 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order added successfully.')

    def test_workorder_create_view_post_invalid(self):
        """Test invalid POST request to create a new work order (e.g., missing required field)."""
        initial_count = WorkOrder.objects.count()
        data = {
            'order_number': 'WO-INVALID',
            'customer_name': '', # Missing required field
            'dispatch_date': '2023-03-01',
            'status': 'Pending',
        }
        response = self.client.post(reverse('workorder_add'), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'workorders/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        self.assertTrue(response.context['form'].errors)
        self.assertFalse(WorkOrder.objects.filter(order_number='WO-INVALID').exists())
        self.assertEqual(WorkOrder.objects.count(), initial_count)

    def test_workorder_update_view_get(self):
        """Test GET request for the update form loads correctly."""
        response = self.client.get(reverse('workorder_edit', args=[self.workorder1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder/_workorder_form.html')
        self.assertIn('form', response.context)
        self.assertEqual(response.context['form'].instance, self.workorder1)

    def test_workorder_update_view_post_success(self):
        """Test successful POST request to update an existing work order."""
        updated_customer_name = 'Updated Customer Name'
        data = {
            'order_number': self.workorder1.order_number, # Must include all fields
            'customer_name': updated_customer_name,
            'dispatch_date': self.workorder1.dispatch_date,
            'status': 'Completed',
            'assigned_to': self.workorder1.assigned_to,
            'total_amount': self.workorder1.total_amount,
            'description': self.workorder1.description
        }
        response = self.client.post(reverse('workorder_edit', args=[self.workorder1.pk]), data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.workorder1.refresh_from_db()
        self.assertEqual(self.workorder1.customer_name, updated_customer_name)
        self.assertEqual(self.workorder1.status, 'Completed')
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order updated successfully.')

    def test_workorder_delete_view_get(self):
        """Test GET request for the delete confirmation loads correctly."""
        response = self.client.get(reverse('workorder_delete', args=[self.workorder1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'workorders/workorder/_workorder_confirm_delete.html')
        self.assertIn('workorder', response.context)
        self.assertEqual(response.context['workorder'], self.workorder1)

    def test_workorder_delete_view_post_success(self):
        """Test successful POST request to delete a work order."""
        initial_count = WorkOrder.objects.count()
        response = self.client.post(reverse('workorder_delete', args=[self.workorder1.pk]), HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertFalse(WorkOrder.objects.filter(pk=self.workorder1.pk).exists())
        self.assertEqual(WorkOrder.objects.count(), initial_count - 1)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshWorkOrderList')
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Work Order deleted successfully.')

    def test_non_htmx_request_behavior(self):
        """
        Verify that non-HTMX requests (e.g., direct browser access) behave as expected
        (e.g., redirect after POST).
        """
        data = {
            'order_number': 'WO-STANDARD-004',
            'customer_name': 'Standard User',
            'dispatch_date': '2023-04-01',
            'status': 'Pending',
            'assigned_to': 'Standard Guy',
            'total_amount': '99.99',
            'description': 'Standard request test.'
        }
        response = self.client.post(reverse('workorder_add'), data)
        self.assertEqual(response.status_code, 302) # Redirect to list view
        self.assertRedirects(response, reverse('workorder_list'))
        self.assertTrue(WorkOrder.objects.filter(order_number='WO-STANDARD-004').exists())

```

### Step 5: HTMX and Alpine.js Integration

Instructions:
The Django templates and views are designed to fully embrace HTMX and Alpine.js for a dynamic, single-page application feel without complex JavaScript frameworks.

*   **HTMX for dynamic updates:**
    *   The `list.html` template uses `hx-get` on `workorderTable-container` to load the table content dynamically from `{% url 'workorder_table' %}` on page load and on `refreshWorkOrderList` event.
    *   Add/Edit/Delete buttons use `hx-get` to load forms/confirmation dialogs into a modal (`#modalContent`).
    *   Form submissions (`hx-post`) in `_workorder_form.html` and `_workorder_confirm_delete.html` trigger a `HX-Trigger` header (`refreshWorkOrderList`) from the server upon successful operation. This event is listened for by the `workorderTable-container` to refresh the table.
    *   `hx-swap="none"` is used on form submissions to prevent HTMX from swapping content, as the HTTP 204 response handles the modal dismissal and trigger.

*   **Alpine.js for UI state management (minimal here, mostly Hyperscript):**
    *   The modal (`#modal`) uses Hyperscript (`_=` attributes) for showing/hiding classes:
        *   `on click add .is-active to #modal`: When "Add/Edit/Delete" buttons are clicked, the modal `is-active` class is added to show it.
        *   `on click if event.target.id == 'modal' remove .is-active from me`: Clicking outside the modal content dismisses it.
        *   `on click remove .is-active from #modal`: "Cancel" button in forms explicitly closes the modal.
    *   Alpine.js can be extended for more complex client-side state, form validation feedback, or dynamic element toggling, but for this basic CRUD, HTMX and Hyperscript are sufficient.

*   **DataTables for List Views:**
    *   The `_workorder_table.html` partial template includes a `<table id="workorderTable">` and a JavaScript snippet to initialize DataTables on this table upon its loading. This provides out-of-the-box searching, sorting, and pagination.
    *   CDN links for DataTables (and jQuery if not already included) would reside in `core/base.html`.

This comprehensive plan ensures a robust, modern, and user-friendly Work Order Dispatch Dashboard, designed for efficient development and maintainability within the Django ecosystem.

## Final Notes

*   This plan assumes the `workorders` app is added to `INSTALLED_APPS` in `settings.py` and its URLs are included in the project's main `urls.py`.
*   Ensure that `django.contrib.messages` and `django.contrib.staticfiles` are configured in `settings.py`.
*   Tailwind CSS setup (via PostCSS/Webpack or Django Tailwind) is assumed to be part of the `core/base.html` and project configuration.
*   The `core/base.html` file should include HTMX, Alpine.js, jQuery, and DataTables CDN links for proper functioning.