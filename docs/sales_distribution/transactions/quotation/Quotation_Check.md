## ASP.NET to Django Conversion Script: Quotation Check Module

This document outlines a comprehensive plan for modernizing the ASP.NET "Quotation Check" module to a robust Django-based solution. The focus is on leveraging AI-assisted automation, adhering to modern Django 5.0+ practices, and integrating HTMX + Alpine.js for a dynamic, efficient user experience.

### IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include `base.html` template code in your output - assume it already exists.
- Focus ONLY on component-specific code for the current module.
- Always include complete unit tests for models and integration tests for views.
- Use modern Django 5.0+ patterns and follow best practices.
- Keep your code clean, efficient, and avoid redundancy.
- Always generate complete, runnable Django code.

### AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs).
- Map models to existing database using `managed = False` and `db_table`.
- Implement DataTables for client-side searching, sorting, and pagination.
- Use HTMX for dynamic interactions and Alpine.js for UI state management.
- All templates should extend `core/base.html` (but DO NOT include `base.html` code).
- Achieve at least 80% test coverage with unit and integration tests.
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase.
- Use Tailwind CSS for styling components.

---

### Conversion Steps:

### Step 1: Extract Database Schema

Based on the ASP.NET code, the module interacts primarily with `SD_Cust_Quotation_Master` and `SD_Cust_master`. The `Sp_Quatation_Grid` stored procedure likely performs joins to pull related data like `EmpLoyeeName` and `CustomerName`. For our `managed=False` Django models, we will reflect the *actual table columns* and handle joins via Django ORM.

**Inferred Database Tables and Columns:**

1.  **`SD_Cust_Quotation_Master`** (`[TABLE_NAME]` = `SD_Cust_Quotation_Master`)
    *   `Id` (Primary Key, Integer)
    *   `FinYear` (Varchar/Char)
    *   `QuotationNo` (Varchar)
    *   `SysDate` (DateTime)
    *   `EmpId` (Integer, likely Foreign Key to an Employee table)
    *   `CustomerId` (Integer, Foreign Key to `SD_Cust_master`)
    *   `Checked` (Boolean/Bit, stores 0 or 1)
    *   `CheckedBy` (Varchar/Integer, stores username or ID)
    *   `CheckedDate` (DateTime)
    *   `CheckedTime` (Time)
    *   `ApprovedDate` (DateTime)
    *   `AuthorizedDate` (DateTime)
    *   `EnqId` (Integer/Varchar)
    *   `CompId` (Integer, Company ID for filtering)

2.  **`SD_Cust_master`** (`[TABLE_NAME]` = `SD_Cust_master`)
    *   `CustomerId` (Primary Key, Integer)
    *   `CustomerName` (Varchar)
    *   `CompId` (Integer, Company ID for filtering)

*(Note: We'll also implicitly assume an `Employee` table with `EmpId` and `EmpLoyeeName` for completeness, as `EmpLoyeeName` is displayed, though not explicitly used in C# filtering logic for the *Quotation Check*.)*

### Step 2: Identify Backend Functionality

The ASP.NET code primarily performs **Read** and **Update** operations:

*   **Read (List & Search):** The `makegrid` method, powered by `Sp_Quatation_Grid`, fetches and displays a list of quotations based on `CompId`, `FinId`, and optional filters (`QuotationNo` or `CustomerId`). Pagination is also handled.
*   **Update (Bulk Check):** The `GridView2_RowCommand` with `CommandName="check"` updates the `Checked`, `CheckedBy`, `CheckedDate`, and `CheckedTime` fields for multiple selected quotations.
*   **Read (View Details Redirection):** The `GridView2_RowCommand` with `CommandName="view"` redirects to `Quotation_Print_Details.aspx` with specific query parameters, implying a separate detail view.
*   **Read (Customer Autocomplete):** The `GetCompletionList` WebMethod provides customer names for autocomplete functionality.

### Step 3: Infer UI Components

The `Quotation_Check.aspx` page contains the following key UI components:

*   **Master Page Integration:** Content is placed within defined content placeholders, indicating a templated layout. (Handled by `core/base.html` in Django).
*   **Search/Filter Section:**
    *   `drpfield`: A dropdown to select the search criteria ("Quotation No" or "Customer").
    *   `txtPONo`: A textbox for "Quotation No" input.
    *   `txtSupplier`: A textbox for "Customer" input, linked with an `AutoCompleteExtender`.
    *   `Button1`: A "Search" button to trigger filtering.
*   **Data Display Grid:**
    *   `GridView2`: Displays quotation data in a tabular format.
    *   Columns include: "SN", "Fin Year", "Quotation No", "Date", "Gen By", "Customer", "Code", "For Checking" (with a checkbox and a date label), "Approved", "Authorized".
    *   `LinkButton` with "View" command in each row.
    *   `CheckBox` (`CK`) within "For Checking" column, which becomes invisible if already checked (`lblcheck` is not empty).
    *   `Button` with "Checked" command in the footer for bulk updates.
*   **Dynamic UI Logic:**
    *   Conditional visibility of `txtPONo` and `txtSupplier` based on `drpfield` selection.
    *   Client-side confirmation (`confirmation()`) for the "Checked" button.
    *   Autocomplete for customer search.

---

### Step 4: Generate Django Code

**App Name:** `sales` (for the `Module_SalesDistribution_Transactions` context)
**Model Name:** `QuotationMaster` (from `SD_Cust_Quotation_Master`)

### 4.1 Models (`sales/models.py`)

We'll define models for `QuotationMaster`, `Customer`, and a placeholder `Employee` to reflect the implied relationships for a more robust ORM structure, always keeping `managed=False`. We will also add a custom manager to `QuotationMaster` to encapsulate the data retrieval and filtering logic.

```python
from django.db import models
from django.db.models import F, Value
from django.db.models.functions import Concat
from django.utils import timezone

class Company(models.Model):
    # This model would typically hold company details.
    # Assuming it has a 'CompId' matching the session 'compid'.
    # We only define 'comp_id' here for foreign key purposes.
    comp_id = models.IntegerField(db_column='CompId', primary_key=True)
    # Add other company fields if needed, e.g., company_name

    class Meta:
        managed = False
        db_table = 'CompanyMaster' # Assuming a table name
        verbose_name = 'Company'
        verbose_name_plural = 'Companies'

    def __str__(self):
        return f"Company {self.comp_id}" # Or company_name if exists

class FinancialYear(models.Model):
    # This model would hold financial year details.
    # Assuming it has a 'FinId' matching the session 'finyear'.
    fin_id = models.IntegerField(db_column='FinId', primary_key=True)
    year_name = models.CharField(db_column='FinYearName', max_length=50) # e.g., 2023-24

    class Meta:
        managed = False
        db_table = 'FinancialYearMaster' # Assuming a table name
        verbose_name = 'Financial Year'
        verbose_name_plural = 'Financial Years'

    def __str__(self):
        return self.year_name

class Customer(models.Model):
    # Corresponds to SD_Cust_master
    customer_id = models.IntegerField(db_column='CustomerId', primary_key=True)
    customer_name = models.CharField(db_column='CustomerName', max_length=255)
    comp = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'

    def __str__(self):
        return self.customer_name

class Employee(models.Model):
    # Placeholder for Employee data, as EmpLoyeeName is displayed
    emp_id = models.IntegerField(db_column='EmpId', primary_key=True)
    employee_name = models.CharField(db_column='EmpLoyeeName', max_length=255)
    # Add other employee fields if needed

    class Meta:
        managed = False
        db_table = 'EmployeeMaster' # Assuming a table name
        verbose_name = 'Employee'
        verbose_name_plural = 'Employees'

    def __str__(self):
        return self.employee_name

class QuotationMasterManager(models.Manager):
    """
    Custom manager for QuotationMaster to handle filtering and search logic
    similar to the ASP.NET 'makegrid' method and 'Sp_Quatation_Grid' stored procedure.
    """
    def get_quotations_for_grid(self, company_id, fin_year_id, search_type=None, search_value=None):
        """
        Retrieves quotations with related customer and employee data,
        applying filters based on company, financial year, and search criteria.
        """
        qs = self.get_queryset().filter(
            comp_id=company_id,
            fin_year__fin_id=fin_year_id # Assuming FinId matches fin_year__fin_id
        ).select_related('customer', 'employee', 'fin_year') # Pre-fetch related data

        if search_type == '0' and search_value: # Quotation No
            qs = qs.filter(quotation_no__icontains=search_value)
        elif search_type == '1' and search_value: # Customer ID
            # In ASP.NET, fun.getCode() extracts ID from "Name [ID]".
            # Here, we assume search_value is the ID itself, or a simple customer name search.
            # If search_value is "Name [ID]", we need to parse it. Let's assume it's the ID.
            try:
                customer_id_from_search = int(search_value)
                qs = qs.filter(customer_id=customer_id_from_search)
            except ValueError:
                # Fallback to name search if ID parsing fails
                qs = qs.filter(customer__customer_name__icontains=search_value)
        
        # Annotate with fields for display that might not be direct model fields
        # if the SP returns flattened data. For managed=False, we map what's in the table.
        # But if the grid expects EmpLoyeeName/CustomerName directly, and they come via joins,
        # we can annotate them. Assuming relationships are enough.
        
        return qs.order_by('-sys_date', '-quotation_no') # Order as expected

class QuotationMaster(models.Model):
    # Corresponds to SD_Cust_Quotation_Master
    id = models.IntegerField(db_column='Id', primary_key=True)
    fin_year = models.ForeignKey(FinancialYear, models.DO_NOTHING, db_column='FinYear') # Assuming 'FinYear' column stores FinId
    quotation_no = models.CharField(db_column='QuotationNo', max_length=50)
    sys_date = models.DateTimeField(db_column='SysDate')
    employee = models.ForeignKey(Employee, models.DO_NOTHING, db_column='EmpId') # Assuming EmpId column
    customer = models.ForeignKey(Customer, models.DO_NOTHING, db_column='CustomerId')
    checked_status = models.BooleanField(db_column='Checked', default=False)
    checked_by = models.CharField(db_column='CheckedBy', max_length=50, blank=True, null=True)
    checked_date = models.DateTimeField(db_column='CheckedDate', blank=True, null=True)
    checked_time = models.TimeField(db_column='CheckedTime', blank=True, null=True) # SQL Time field
    approved_date = models.DateTimeField(db_column='ApprovedDate', blank=True, null=True)
    authorized_date = models.DateTimeField(db_column='AuthorizedDate', blank=True, null=True)
    enq_id = models.CharField(db_column='EnqId', max_length=50, blank=True, null=True) # Can be integer or varchar
    comp = models.ForeignKey(Company, models.DO_NOTHING, db_column='CompId')

    objects = QuotationMasterManager()

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Quotation'
        verbose_name_plural = 'Quotations'

    def __str__(self):
        return self.quotation_no

    def mark_as_checked(self, checked_by_user_id):
        """
        Business logic to mark a quotation as checked.
        Corresponds to the 'check' command in ASP.NET.
        """
        if not self.checked_status: # Only update if not already checked
            now = timezone.now()
            self.checked_status = True
            self.checked_by = checked_by_user_id
            self.checked_date = now.date() # Only date part
            self.checked_time = now.time() # Only time part
            self.save(update_fields=['checked_status', 'checked_by', 'checked_date', 'checked_time'])
            return True
        return False

    @property
    def is_checked(self):
        """Returns True if the quotation has been checked."""
        return self.checked_status
```

### 4.2 Forms (`sales/forms.py`)

A form for the search criteria and a basic form for customer autocomplete. No full `QuotationMasterForm` is needed as the main page is a list/check, not CRUD for the quotation details themselves.

```python
from django import forms
from .models import Customer, QuotationMaster

class QuotationSearchForm(forms.Form):
    SEARCH_CHOICES = [
        ('0', 'Quotation No'),
        ('1', 'Customer'),
    ]
    search_field = forms.ChoiceField(
        choices=SEARCH_CHOICES,
        widget=forms.Select(attrs={
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm',
            'x-model': 'searchType', # Alpine.js model
            'x-on:change': 'if (searchType === "0") { searchCustomer = ""; } else { searchQuotationNo = ""; }'
        }),
        label="Search By"
    )
    quotation_no = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm',
            'x-model': 'searchQuotationNo', # Alpine.js model
            'x-show': 'searchType === "0"', # Alpine.js show/hide
            'placeholder': 'Enter Quotation No'
        }),
        label="Quotation No"
    )
    customer = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'box3 p-2 border border-gray-300 rounded-md shadow-sm',
            'x-model': 'searchCustomer', # Alpine.js model
            'x-show': 'searchType === "1"', # Alpine.js show/hide
            'placeholder': 'Enter Customer Name or ID',
            'hx-get': '/sales/customers/autocomplete/', # HTMX endpoint for autocomplete
            'hx-trigger': 'keyup changed delay:500ms, search',
            'hx-target': '#customer-autocomplete-results',
            'hx-swap': 'innerHTML',
            'name': 'customer_name_or_id', # Name used in request
        }),
        label="Customer"
    )

    # Helper method to extract customer ID from "Name [ID]" string, if needed
    def clean_customer(self):
        customer_input = self.cleaned_data.get('customer')
        if self.cleaned_data.get('search_field') == '1' and customer_input:
            # Attempt to extract ID if format is "Name [ID]"
            if '[' in customer_input and ']' in customer_input:
                try:
                    return customer_input.split('[')[-1].strip(']')
                except IndexError:
                    pass # Fallback to original input if parsing fails
            # If not in "Name [ID]" format, assume it's just the name
        return customer_input

class CustomerAutoCompleteForm(forms.Form):
    # Form for the autocomplete endpoint, mainly for validation
    q = forms.CharField(label='Search customer', max_length=255)
```

### 4.3 Views (`sales/views.py`)

These views will handle the display, search, and bulk update logic using HTMX for dynamic content updates. The views are kept thin, delegating complex logic to the `QuotationMasterManager`.

```python
from django.views.generic import ListView, View
from django.shortcuts import render, redirect, get_object_or_404
from django.urls import reverse
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from .models import QuotationMaster, Customer
from .forms import QuotationSearchForm, CustomerAutoCompleteForm
from django.contrib.auth.mixins import LoginRequiredMixin # Assuming user authentication

class QuotationCheckListView(LoginRequiredMixin, ListView):
    """
    Main view for the Quotation Check page.
    Renders the initial search form and the container for the HTMX-loaded table.
    """
    model = QuotationMaster
    template_name = 'sales/quotation_check/list.html'
    context_object_name = 'quotations' # Will be empty initially, table loaded via HTMX

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Initialize the search form
        context['search_form'] = QuotationSearchForm(self.request.GET or None)
        return context

class QuotationCheckTablePartialView(LoginRequiredMixin, View):
    """
    HTMX endpoint to load the quotation table.
    Handles search filtering and renders the table partial.
    """
    def get(self, request, *args, **kwargs):
        company_id = request.session.get('compid', 1) # Default to 1 if not in session
        fin_year_id = request.session.get('finyear', 1) # Default to 1 if not in session
        
        search_type = request.GET.get('search_field')
        search_value = request.GET.get('quotation_no') or request.GET.get('customer_name_or_id')

        # Get the form to clean customer input, if applicable
        search_form = QuotationSearchForm(request.GET)
        if search_form.is_valid():
            cleaned_search_value = search_form.cleaned_data.get('customer') if search_type == '1' else search_value
        else:
            cleaned_search_value = search_value # Fallback if form not valid (e.g., initial load without params)

        quotations = QuotationMaster.objects.get_quotations_for_grid(
            company_id,
            fin_year_id,
            search_type=search_type,
            search_value=cleaned_search_value
        )
        
        context = {'quotations': quotations}
        return render(request, 'sales/quotation_check/_quotation_table.html', context)

class QuotationCheckActionView(LoginRequiredMixin, View):
    """
    HTMX endpoint to handle the 'Check' bulk action for quotations.
    Corresponds to GridView2_RowCommand (check).
    """
    def post(self, request, *args, **kwargs):
        checked_ids_str = request.POST.getlist('checked_quotations')
        checked_ids = []
        for q_id in checked_ids_str:
            try:
                checked_ids.append(int(q_id))
            except ValueError:
                continue # Skip invalid IDs

        user_id = request.session.get('username', 'system') # Get logged-in user

        if not checked_ids:
            messages.warning(request, "No quotations selected for checking.")
            return HttpResponse(status=204, headers={'HX-Trigger': 'refreshQuotationList'})

        try:
            with transaction.atomic():
                updated_count = 0
                for q_id in checked_ids:
                    quotation = get_object_or_404(QuotationMaster, id=q_id)
                    if quotation.mark_as_checked(user_id):
                        updated_count += 1
            messages.success(request, f"{updated_count} quotation(s) marked as checked successfully.")
        except Exception as e:
            messages.error(request, f"Error marking quotations as checked: {e}")

        # HTMX will trigger a refresh of the list
        return HttpResponse(status=204, headers={'HX-Trigger': 'refreshQuotationList'})

class CustomerAutoCompleteView(LoginRequiredMixin, View):
    """
    HTMX endpoint for customer name autocomplete functionality.
    Corresponds to GetCompletionList WebMethod.
    """
    def get(self, request, *args, **kwargs):
        form = CustomerAutoCompleteForm(request.GET)
        if form.is_valid():
            query = form.cleaned_data['q']
            company_id = request.session.get('compid', 1)
            
            # Filter customers based on query and company ID
            customers = Customer.objects.filter(
                customer_name__icontains=query,
                comp_id=company_id
            ).annotate(
                display_name=Concat(F('customer_name'), Value(' ['), F('customer_id'), Value(']'))
            )[:10] # Limit results for performance

            results = [customer.display_name for customer in customers]
            return render(request, 'sales/quotation_check/_customer_autocomplete_results.html', {'results': results})
        return HttpResponse("") # Return empty if form invalid
```

### 4.4 Templates

Templates will follow the DRY principle, with `list.html` extending `core/base.html` and partials (`_quotation_table.html`, `_search_form.html`, `_customer_autocomplete_results.html`) loaded via HTMX.

**`sales/quotation_check/list.html`**

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 space-y-4 md:space-y-0">
        <h2 class="text-2xl font-bold text-gray-800">Quotation Check</h2>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md mb-6" x-data="{ searchType: '0', searchQuotationNo: '', searchCustomer: '' }">
        <form id="quotation-search-form"
              hx-get="{% url 'sales:quotation_table_partial' %}"
              hx-target="#quotation-table-container"
              hx-swap="innerHTML"
              hx-trigger="submit, change from:#id_search_field, searchQuotationNoChange from:#id_quotation_no, searchCustomerChange from:#id_customer"
              class="space-y-4">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 items-end">
                <div>
                    <label for="{{ search_form.search_field.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.search_field.label }}
                    </label>
                    {{ search_form.search_field }}
                </div>
                
                <div x-show="searchType === '0'">
                    <label for="{{ search_form.quotation_no.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.quotation_no.label }}
                    </label>
                    {{ search_form.quotation_no }}
                </div>

                <div x-show="searchType === '1'">
                    <label for="{{ search_form.customer.id_for_label }}" class="block text-sm font-medium text-gray-700">
                        {{ search_form.customer.label }}
                    </label>
                    {{ search_form.customer }}
                    <div id="customer-autocomplete-results" class="relative z-10 bg-white border border-gray-200 rounded-md shadow-lg mt-1 max-h-48 overflow-y-auto"></div>
                </div>

                <div>
                    <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                        Search
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div id="quotation-table-container"
         hx-trigger="load, refreshQuotationList from:body"
         hx-get="{% url 'sales:quotation_table_partial' %}"
         hx-target="#quotation-table-container"
         hx-swap="innerHTML"
         class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Initial loading state -->
        <div class="p-6 text-center">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p class="mt-2 text-gray-600">Loading quotations...</p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('alpine:init', () => {
        // Alpine.js component for search form
        Alpine.data('quotationSearch', () => ({
            searchType: '0', // Default to Quotation No
            searchQuotationNo: '',
            searchCustomer: '',
            init() {
                // Initialize with values from URL query parameters if present
                const params = new URLSearchParams(window.location.search);
                this.searchType = params.get('search_field') || '0';
                this.searchQuotationNo = params.get('quotation_no') || '';
                this.searchCustomer = params.get('customer_name_or_id') || '';
            }
        }));

        // HTMX event listener for autocomplete selection
        htmx.on('#customer-autocomplete-results', 'click', function(evt) {
            const selectedText = evt.target.closest('li').dataset.value;
            document.querySelector('#id_customer').value = selectedText;
            document.querySelector('#customer-autocomplete-results').innerHTML = ''; // Clear results
            htmx.trigger(document.querySelector('#id_customer'), 'searchCustomerChange'); // Trigger form re-submit
        });
    });

    // Re-initialize DataTables after HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(event) {
        if (event.detail.target.id === 'quotation-table-container') {
            $('#quotationTable').DataTable({
                "pageLength": 20,
                "lengthMenu": [[10, 20, 50, -1], [10, 20, 50, "All"]],
                "dom": '<"flex justify-between items-center mb-4"lf><"shadow overflow-hidden border-b border-gray-200 sm:rounded-lg"rt><"flex justify-between items-center mt-4"ip>',
                "language": {
                    "search": "Filter:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "Showing 0 to 0 of 0 entries",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    },
                    "zeroRecords": "No matching records found"
                },
                "initComplete": function () {
                    // Apply Tailwind classes to DataTable components
                    $('.dataTables_filter input').addClass('w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm');
                    $('.dataTables_length select').addClass('px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm');
                    $('.dataTables_paginate .pagination').addClass('flex justify-end items-center space-x-2');
                    $('.dataTables_paginate .paginate_button').addClass('px-3 py-1 border border-gray-300 rounded-md hover:bg-gray-100');
                    $('.dataTables_paginate .paginate_button.current').addClass('bg-blue-600 text-white hover:bg-blue-700 border-blue-600');
                    $('.dataTables_paginate .paginate_button.disabled').addClass('opacity-50 cursor-not-allowed');
                }
            });
        }
    });

    // Custom HTMX event to trigger form submit on input change after delay
    document.querySelector('#id_quotation_no').addEventListener('input', function() {
        htmx.trigger(this, 'searchQuotationNoChange');
    });
    // This is already handled by hx-trigger='keyup changed delay:500ms' on customer input field.
    // document.querySelector('#id_customer').addEventListener('input', function() {
    //     htmx.trigger(this, 'searchCustomerChange');
    // });
</script>
{% endblock %}
```

**`sales/quotation_check/_quotation_table.html`** (Partial for HTMX)

```html
<form id="quotation-check-form"
      hx-post="{% url 'sales:quotation_check_action' %}"
      hx-target="#quotation-table-container"
      hx-swap="innerHTML"
      hx-confirm="Are you sure you want to mark selected quotations as checked?"
      class="p-6">
    {% csrf_token %}
    <div class="overflow-x-auto">
        <table id="quotationTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fin Year</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Generated By</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer Code</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">For Checking</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Approved Date</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Authorized Date</th>
                    <th scope="col" class="py-2 px-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for quotation in quotations %}
                <tr class="hover:bg-gray-50">
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.fin_year.year_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.quotation_no }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.sys_date|date:"d M Y" }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.employee.employee_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.customer.customer_name }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500">{{ quotation.customer.customer_id }}</td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">
                        {% if not quotation.is_checked %}
                            <input type="checkbox" name="checked_quotations" value="{{ quotation.id }}" class="form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out">
                        {% endif %}
                        {% if quotation.checked_date %}{{ quotation.checked_date|date:"d M Y" }}{% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">
                        {% if quotation.approved_date %}{{ quotation.approved_date|date:"d M Y" }}{% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm text-gray-500 text-center">
                        {% if quotation.authorized_date %}{{ quotation.authorized_date|date:"d M Y" }}{% endif %}
                    </td>
                    <td class="py-2 px-4 whitespace-nowrap text-sm font-medium">
                        <a href="{% url 'sales:quotation_print_details' quotation.id quotation.quotation_no quotation.customer.customer_id quotation.enq_id %}"
                           class="text-blue-600 hover:text-blue-900 ml-2"
                           target="_blank">
                           View
                        </a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="11" class="py-4 text-center text-gray-500">No data to display!</td>
                </tr>
                {% endfor %}
            </tbody>
            {% if quotations %}
            <tfoot class="bg-gray-50">
                <tr>
                    <td colspan="11" class="py-3 px-4 text-right">
                        <button type="submit"
                                class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-md shadow-sm transition duration-150 ease-in-out">
                            Mark Selected as Checked
                        </button>
                    </td>
                </tr>
            </tfoot>
            {% endif %}
        </table>
    </div>
</form>

<script>
    // DataTable initialization is handled in the main list.html's htmx:afterSwap event.
    // This script block is primarily for dataTable to attach to the new table element.
</script>
```

**`sales/quotation_check/_customer_autocomplete_results.html`** (Partial for HTMX)

```html
{% if results %}
    <ul class="list-none p-0 m-0">
        {% for result in results %}
            <li class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-gray-700 text-sm" data-value="{{ result }}">{{ result }}</li>
        {% endfor %}
    </ul>
{% endif %}
```

### 4.5 URLs (`sales/urls.py`)

Define the URL patterns within the `sales` application.

```python
from django.urls import path
from .views import QuotationCheckListView, QuotationCheckTablePartialView, QuotationCheckActionView, CustomerAutoCompleteView

app_name = 'sales'

urlpatterns = [
    path('quotation-check/', QuotationCheckListView.as_view(), name='quotation_check_list'),
    path('quotation-check/table/', QuotationCheckTablePartialView.as_view(), name='quotation_table_partial'),
    path('quotation-check/action/', QuotationCheckActionView.as_view(), name='quotation_check_action'),
    path('customers/autocomplete/', CustomerAutoCompleteView.as_view(), name='customer_autocomplete'),
    # Placeholder for the "Quotation Print Details" redirection, as per ASP.NET code
    path('quotation-print-details/<int:quotation_id>/<str:quotation_no>/<int:customer_id>/<str:enq_id>/', 
         lambda request, quotation_id, quotation_no, customer_id, enq_id: redirect(f'/some-other-app/quotation-print-details/?Id={quotation_id}&QuotationNo={quotation_no}&EnqId={enq_id}&CustomerId={customer_id}&parentpage=1'),
         name='quotation_print_details'),
]
```

*(Note: The `quotation_print_details` URL is a placeholder. In a real scenario, this would point to a proper Django view that renders the print details page, likely in another app.)*

### 4.6 Tests (`sales/tests.py`)

Comprehensive tests for models and views, ensuring functionality and robustness.

```python
from django.test import TestCase, Client
from django.urls import reverse
from django.utils import timezone
from unittest.mock import patch
from .models import Company, FinancialYear, Customer, Employee, QuotationMaster

class ModelSetupMixin:
    @classmethod
    def setUpTestData(cls):
        # Create common test data for all model and view tests
        cls.company = Company.objects.create(comp_id=1)
        cls.fin_year = FinancialYear.objects.create(fin_id=1, year_name='2023-24')
        cls.customer1 = Customer.objects.create(customer_id=101, customer_name='Customer A', comp=cls.company)
        cls.customer2 = Customer.objects.create(customer_id=102, customer_name='Customer B', comp=cls.company)
        cls.employee1 = Employee.objects.create(emp_id=1, employee_name='John Doe')
        cls.employee2 = Employee.objects.create(emp_id=2, employee_name='Jane Smith')

        cls.quotation1 = QuotationMaster.objects.create(
            id=1,
            fin_year=cls.fin_year,
            quotation_no='Q001',
            sys_date=timezone.datetime(2024, 1, 1, 10, 0, 0, tzinfo=timezone.utc),
            employee=cls.employee1,
            customer=cls.customer1,
            checked_status=False,
            approved_date=None,
            authorized_date=None,
            enq_id='E001',
            comp=cls.company
        )
        cls.quotation2 = QuotationMaster.objects.create(
            id=2,
            fin_year=cls.fin_year,
            quotation_no='Q002',
            sys_date=timezone.datetime(2024, 1, 2, 11, 0, 0, tzinfo=timezone.utc),
            employee=cls.employee2,
            customer=cls.customer2,
            checked_status=True, # Already checked
            checked_by='admin',
            checked_date=timezone.datetime(2024, 1, 2, tzinfo=timezone.utc).date(),
            checked_time=timezone.datetime(2024, 1, 2, 11, 0, 0, tzinfo=timezone.utc).time(),
            approved_date=timezone.datetime(2024, 1, 3, tzinfo=timezone.utc),
            authorized_date=timezone.datetime(2024, 1, 4, tzinfo=timezone.utc),
            enq_id='E002',
            comp=cls.company
        )

class QuotationMasterModelTest(ModelSetupMixin, TestCase):
    def test_quotation_creation(self):
        self.assertEqual(QuotationMaster.objects.count(), 2)
        q1 = QuotationMaster.objects.get(id=1)
        self.assertEqual(q1.quotation_no, 'Q001')
        self.assertFalse(q1.checked_status)
        self.assertEqual(q1.customer.customer_name, 'Customer A')
        self.assertEqual(q1.employee.employee_name, 'John Doe')

    def test_mark_as_checked_method(self):
        q1 = QuotationMaster.objects.get(id=1)
        self.assertFalse(q1.is_checked)
        
        # Test marking as checked
        with patch('django.utils.timezone.now') as mock_now:
            mock_now.return_value = timezone.datetime(2024, 1, 5, 12, 30, 0, tzinfo=timezone.utc)
            result = q1.mark_as_checked('testuser')
            self.assertTrue(result)
            q1.refresh_from_db()
            self.assertTrue(q1.is_checked)
            self.assertEqual(q1.checked_by, 'testuser')
            self.assertEqual(q1.checked_date, timezone.datetime(2024, 1, 5, tzinfo=timezone.utc).date())
            self.assertEqual(q1.checked_time, timezone.datetime(2024, 1, 5, 12, 30, 0, tzinfo=timezone.utc).time())

        # Test marking an already checked quotation
        q2 = QuotationMaster.objects.get(id=2)
        self.assertTrue(q2.is_checked)
        result = q2.mark_as_checked('anotheruser')
        self.assertFalse(result) # Should return False as it was already checked
        q2.refresh_from_db()
        self.assertEqual(q2.checked_by, 'admin') # Should not change

    def test_quotation_manager_get_quotations(self):
        # Test without search
        quotations = QuotationMaster.objects.get_quotations_for_grid(self.company.comp_id, self.fin_year.fin_id)
        self.assertEqual(len(quotations), 2)
        self.assertEqual(quotations[0].quotation_no, 'Q002') # Ordered by sys_date desc

        # Test search by Quotation No
        quotations_q001 = QuotationMaster.objects.get_quotations_for_grid(self.company.comp_id, self.fin_year.fin_id, '0', 'Q001')
        self.assertEqual(len(quotations_q001), 1)
        self.assertEqual(quotations_q001[0].quotation_no, 'Q001')

        # Test search by Customer ID (assuming search value is ID)
        quotations_cust_a = QuotationMaster.objects.get_quotations_for_grid(self.company.comp_id, self.fin_year.fin_id, '1', str(self.customer1.customer_id))
        self.assertEqual(len(quotations_cust_a), 1)
        self.assertEqual(quotations_cust_a[0].customer.customer_name, 'Customer A')

        # Test search by Customer Name (fallback in manager)
        quotations_cust_b_name = QuotationMaster.objects.get_quotations_for_grid(self.company.comp_id, self.fin_year.fin_id, '1', 'Customer B')
        self.assertEqual(len(quotations_cust_b_name), 1)
        self.assertEqual(quotations_cust_b_name[0].customer.customer_name, 'Customer B')

        # Test no results
        quotations_no_match = QuotationMaster.objects.get_quotations_for_grid(self.company.comp_id, self.fin_year.fin_id, '0', 'NONEXISTENT')
        self.assertEqual(len(quotations_no_match), 0)

class QuotationCheckViewsTest(ModelSetupMixin, TestCase):
    def setUp(self):
        self.client = Client()
        # Mock session data for compid and finyear
        session = self.client.session
        session['compid'] = self.company.comp_id
        session['finyear'] = self.fin_year.fin_id
        session['username'] = 'testuser_session'
        session.save()

    def test_quotation_check_list_view_get(self):
        response = self.client.get(reverse('sales:quotation_check_list'))
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation_check/list.html')
        self.assertIn('search_form', response.context)

    def test_quotation_table_partial_view_get(self):
        # HTMX request to load the table
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:quotation_table_partial'), headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation_check/_quotation_table.html')
        self.assertIn('quotations', response.context)
        self.assertEqual(len(response.context['quotations']), 2)
        self.assertContains(response, 'Q001')
        self.assertContains(response, 'Q002')

    def test_quotation_table_partial_view_search_quotation_no(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:quotation_table_partial'), {'search_field': '0', 'quotation_no': 'Q001'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Q001')
        self.assertNotContains(response, 'Q002') # Should only contain Q001

    def test_quotation_table_partial_view_search_customer_id(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:quotation_table_partial'), {'search_field': '1', 'customer_name_or_id': str(self.customer1.customer_id)}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Customer A')
        self.assertNotContains(response, 'Customer B')

    def test_quotation_table_partial_view_search_customer_name(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:quotation_table_partial'), {'search_field': '1', 'customer_name_or_id': 'Customer B'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Customer B')
        self.assertNotContains(response, 'Customer A')

    def test_quotation_check_action_view_post_success(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        initial_q1 = QuotationMaster.objects.get(id=1)
        self.assertFalse(initial_q1.is_checked)
        
        response = self.client.post(reverse('sales:quotation_check_action'), {'checked_quotations': [initial_q1.id]}, headers=headers)
        self.assertEqual(response.status_code, 204) # HTMX success, no content
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')

        q1_after = QuotationMaster.objects.get(id=1)
        self.assertTrue(q1_after.is_checked)
        self.assertEqual(q1_after.checked_by, 'testuser_session')

    def test_quotation_check_action_view_post_no_selection(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.post(reverse('sales:quotation_check_action'), {}, headers=headers)
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertEqual(response.headers['HX-Trigger'], 'refreshQuotationList')
        # Check for message (might need custom assertion or check response body if messages were rendered)

    def test_quotation_check_action_view_post_already_checked(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        q2_initial = QuotationMaster.objects.get(id=2)
        self.assertTrue(q2_initial.is_checked)

        response = self.client.post(reverse('sales:quotation_check_action'), {'checked_quotations': [q2_initial.id]}, headers=headers)
        self.assertEqual(response.status_code, 204)
        q2_after = QuotationMaster.objects.get(id=2)
        self.assertEqual(q2_after.checked_by, 'admin') # Should not change
        self.assertTrue(q2_after.is_checked)

    def test_customer_autocomplete_view_get(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:customer_autocomplete'), {'q': 'Customer A'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation_check/_customer_autocomplete_results.html')
        self.assertContains(response, 'Customer A [101]')
        self.assertNotContains(response, 'Customer B [102]')

    def test_customer_autocomplete_view_get_no_match(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:customer_autocomplete'), {'q': 'XYZ'}, headers=headers)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales/quotation_check/_customer_autocomplete_results.html')
        self.assertNotContains(response, 'Customer') # Should contain no customers

    def test_customer_autocomplete_view_get_empty_query(self):
        headers = {'HTTP_HX_REQUEST': 'true'}
        response = self.client.get(reverse('sales:customer_autocomplete'), {'q': ''}, headers=headers)
        self.assertEqual(response.status_code, 200)
        # Should return empty div or no results for empty query based on implementation
        self.assertTemplateUsed(response, 'sales/quotation_check/_customer_autocomplete_results.html')

    def test_quotation_print_details_redirection(self):
        quotation_id = self.quotation1.id
        quotation_no = self.quotation1.quotation_no
        customer_id = self.quotation1.customer.customer_id
        enq_id = self.quotation1.enq_id

        # Test the redirect URL. The lambda in urls.py does an actual redirect.
        response = self.client.get(reverse('sales:quotation_print_details', args=[quotation_id, quotation_no, customer_id, enq_id]))
        self.assertEqual(response.status_code, 302)
        expected_url = f'/some-other-app/quotation-print-details/?Id={quotation_id}&QuotationNo={quotation_no}&EnqId={enq_id}&CustomerId={customer_id}&parentpage=1'
        self.assertRedirects(response, expected_url, fetch_redirect_response=False)
```

### Step 5: HTMX and Alpine.js Integration

*   **HTMX:**
    *   The `list.html` uses `hx-get` to initially load and periodically refresh the `_quotation_table.html` partial (`hx-trigger="load, refreshQuotationList from:body"`).
    *   The search form uses `hx-get` to trigger the table reload on submit or when search inputs change (`hx-trigger="submit, change from:#id_search_field, searchQuotationNoChange from:#id_quotation_no, searchCustomerChange from:#id_customer"`).
    *   The "Mark Selected as Checked" button in `_quotation_table.html` uses `hx-post` to submit the form data to `QuotationCheckActionView`.
    *   `QuotationCheckActionView` responds with `status=204` and `HX-Trigger: refreshQuotationList` to tell the client to re-render the table.
    *   Customer autocomplete field uses `hx-get` to fetch suggestions from `CustomerAutoCompleteView` on `keyup changed delay:500ms`.
    *   Autocomplete results are placed in `#customer-autocomplete-results` via `hx-swap`.
    *   Clicking an autocomplete suggestion triggers `searchCustomerChange` event on the input field, which in turn triggers the search form's HTMX request.
*   **Alpine.js:**
    *   Used in `list.html` (`x-data`) to manage the visibility of `quotation_no` and `customer` input fields based on `search_field` selection (`x-show` and `x-model`).
    *   This replaces the ASP.NET `drpfield_SelectedIndexChanged` AutoPostBack logic.
*   **DataTables:**
    *   Initialized on the `_quotation_table.html` partial *after* it's loaded via HTMX (`htmx:afterSwap` event listener in `list.html`).
    *   This provides client-side sorting, searching, and pagination, replacing the `GridView`'s built-in server-side pagination.
*   **No Full Page Reloads:** All search, filter, and action interactions are handled via HTMX, ensuring a smooth, single-page application feel without complex JavaScript.

---

### Final Notes

This comprehensive plan provides a clear, actionable roadmap for migrating the ASP.NET "Quotation Check" module to Django. By leveraging AI-assisted automation, the focus is on a systematic transformation that reduces manual coding effort, maintains data integrity through `managed=False` models, and enhances user experience with modern frontend technologies like HTMX and Alpine.js. The emphasis on 'fat models' and 'thin views' ensures maintainable, scalable code, while thorough testing guarantees reliability.