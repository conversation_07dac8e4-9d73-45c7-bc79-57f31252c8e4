## ASP.NET to Django Conversion Script:

## IMPORTANT RULES - FOLLOW THESE STRICTLY:
- NEVER include base.html template code in your output - assume it already exists
- Focus ONLY on component-specific code for the current module
- Always include complete unit tests for models and integration tests for views
- Use modern Django 5.0+ patterns and follow best practices
- Keep your code clean, efficient, and avoid redundancy
- Always generate complete, runnable Django code

## AutoERP Guidelines:
- Use 'Fat Model, Thin View' with Django Class-Based Views (CBVs)
- Map models to existing database using managed = False and db_table
- Implement DataTables for client-side searching, sorting, and pagination
- Use HTMX for dynamic interactions and Alpine.js for UI state management
- All templates should extend core/base.html (but DO NOT include base.html code)
- Achieve at least 80% test coverage with unit and integration tests
- Follow DRY (Don't Repeat Yourself) principle throughout the codebase
- Use Tailwind CSS for styling components

---

## Conversion Steps:

## Step 1: Extract Database Schema

Task: Identify the database table and its columns from the ASP.NET code.

Based on the ASP.NET code, we identify the following tables and their relevant columns for this module:

*   **`SD_Cust_Quotation_Master`** (Main quotation details)
    *   Columns: `Id` (PK), `SysDate`, `SysTime`, `SessionId`, `CompId`, `FinYearId`, `CustomerId` (FK to `SD_Cust_master`), `EnqId` (FK to `SD_Cust_Enquiry_Master`), `QuotationNo`, `PaymentTerms`, `PF`, `VATCST` (FK to `tblVAT_Master`), `Excise` (FK to `tblExciseser_Master`), `Octroi`, `Warrenty`, `Insurance`, `Transport`, `NoteNo`, `RegistrationNo`, `Freight`, `Remarks`, `Validity`, `OtherCharges`, `DeliveryTerms`, `PFType`, `OctroiType`, `OtherChargesType`, `FreightType`, `DueDate`.
*   **`SD_Cust_Quotation_Details`** (Final line items/goods details for a quotation)
    *   Columns: `Id` (PK), `SessionId`, `CompId`, `FinYearId`, `MId` (FK to `SD_Cust_Quotation_Master.Id`), `ItemDesc`, `TotalQty`, `Unit` (FK to `Unit_Master`), `Rate`, `Discount`.
*   **`SD_Cust_Quotation_Details_Temp`** (Temporary line items/goods details, per session)
    *   Columns: `Id` (PK), `SessionId`, `CompId`, `FinYearId`, `ItemDesc`, `TotalQty`, `Unit` (FK to `Unit_Master`), `Rate`, `Discount`.
*   **`Unit_Master`** (Units for items)
    *   Columns: `Id` (PK), `Symbol`.
*   **`tblVAT_Master`** (VAT/CST terms)
    *   Columns: `Id` (PK), `Terms`.
*   **`tblExciseser_Master`** (Excise/Service Tax terms)
    *   Columns: `Id` (PK), `Terms`.
*   **`SD_Cust_master`** (Customer details - referenced, not fully managed here)
    *   Columns: `CustomerId` (PK), `CustomerName`, `RegdAddress`, `RegdCountry`, `RegdState`, `RegdCity`, `RegdPinNo`.
*   **`SD_Cust_Enquiry_Master`** (Enquiry details - referenced, not fully managed here)
    *   Columns: `EnqId` (PK), `POStatus`.
*   **`tblcountry`**, **`tblState`**, **`tblCity`** (Location masters - referenced, assumed to exist elsewhere)
    *   Columns: `CId`/`SId`/`CityId` (PK), `CountryName`/`StateName`/`CityName`.

## Step 2: Identify Backend Functionality

Task: Determine the CRUD operations in the ASP.NET code.

The application functions as a multi-step form or wizard to create a new customer quotation.

*   **Read Operations:**
    *   Retrieving customer and enquiry details (`SD_Cust_master`, `SD_Cust_Enquiry_Master`) on `Page_Load` using `Request.QueryString`.
    *   Fetching list of units (`Unit_Master`) for dropdown.
    *   Fetching list of VAT/CST and Excise terms (`tblVAT_Master`, `tblExciseser_Master`) for dropdowns.
    *   Displaying temporary quotation details from `SD_Cust_Quotation_Details_Temp` in `GridView1`.
*   **Create Operations:**
    *   Adding new temporary quotation items to `SD_Cust_Quotation_Details_Temp` (via `Button5_Click`).
    *   Creating the final quotation in `SD_Cust_Quotation_Master` and populating `SD_Cust_Quotation_Details` from `SD_Cust_Quotation_Details_Temp` (via `Button6_Click`).
*   **Update Operations:**
    *   Updating existing temporary quotation items in `SD_Cust_Quotation_Details_Temp` (via `GridView1_RowUpdating`).
    *   Updating `POStatus` in `SD_Cust_Enquiry_Master` after final quotation generation.
*   **Delete Operations:**
    *   Deleting temporary quotation items from `SD_Cust_Quotation_Details_Temp` (via `GridView1_RowDeleting`).
    *   Clearing `SD_Cust_Quotation_Details_Temp` after final quotation submission.
*   **Validation Logic:**
    *   Extensive server-side and client-side validation using `RequiredFieldValidator` and `RegularExpressionValidator` for various fields (e.g., quantities, rates, dates).

## Step 3: Infer UI Components

Task: Analyze ASP.NET controls and their roles.

The ASP.NET page uses an `AjaxControlToolkit:TabContainer` to organize content into three main sections: "Customer Details", "Goods Details", and "Terms & Conditions".

*   **Customer Details Tab:**
    *   Static Labels (`asp:Label`) for customer name, address, and enquiry number. These are display-only fields populated from query strings and database lookups.
    *   Hidden Fields (`asp:Label` with `Visible="False"`) for `CustomerId` and `EnqId`.
    *   Navigation Buttons (`asp:Button`): "Next" to move to Goods Details, "Cancel" to redirect.
*   **Goods Details Tab:**
    *   Input Fields:
        *   `TxtItemDesc` (Multi-line `asp:TextBox`) for item description.
        *   `TxtQty` (`asp:TextBox`) for total quantity.
        *   `TxtRate` (`asp:TextBox`) for rate per unit.
        *   `TxtDiscount` (`asp:TextBox`) for discount percentage.
        *   `DrpUnit` (`asp:DropDownList`) for unit selection, populated from `Unit_Master`.
    *   Action Buttons: "Submit" (to add item to the temporary list), "Next" (to move to Terms & Conditions), "Cancel".
    *   Data Presentation: `GridView1` to display temporary quotation items. It supports:
        *   Client-side searching, sorting, paging (via `AllowPaging="True"`, `CssClass="yui-datatable-theme"`).
        *   Inline editing with textboxes and dropdowns (`EditItemTemplate`).
        *   Delete functionality.
        *   `EmptyDataTemplate` for no data message.
*   **Terms & Conditions Tab:**
    *   Input Fields:
        *   `TxtPayments` (`asp:TextBox`) for payment terms.
        *   `TxtPF` (`asp:TextBox`), `DrpPFType` (`asp:DropDownList`) for P&F amount/percentage.
        *   `DrpExcise` (`asp:DropDownList`), `DrpVat` (`asp:DropDownList`) for tax terms, populated from master tables.
        *   `TxtOctroi` (`asp:TextBox`), `DrpOctroiType` (`asp:DropDownList`) for Octroi amount/percentage.
        *   `TxtWarrenty`, `TxtInsurance`, `TxtTransPort`, `TxtNoteNo`, `TxtRegdNo`, `TxtFreight`, `DrpFreightType`, `Txtvalidity`, `Txtocharges`, `DrpOChargeType`, `TxtDelTerms` (`asp:TextBox` and `asp:DropDownList`) for various terms and charges.
        *   `TxtDueDate` (`asp:TextBox`) with `CalendarExtender` for date selection.
        *   `TxtRemarks` (Multi-line `asp:TextBox`).
    *   Action Buttons: "Submit" (to finalize quotation), "Cancel".
*   **Validation:** `RequiredFieldValidator` and `RegularExpressionValidator` are used extensively for client-side validation before server-side submission.
*   **JavaScript:** `loadingNotifier.js`, `PopUpMsg.js` for UI feedback. `OnChanged` for tab container, `confirmationAdd()`, `confirmationUpdate()`, `confirmationDelete()` for user confirmations.

## Step 4: Generate Django Code

We will create a Django application named `sales_quotation`.

### 4.1 Models (`sales_quotation/models.py`)

The models will be mapped to the existing database tables using `managed = False` and `db_table`. Business logic will be incorporated as model methods.

```python
import uuid
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError

# Helper for common fields (optional, but good for DRY)
class BaseMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id')
    
    class Meta:
        abstract = True
        managed = False

# Master Data Models (assuming they exist or are simple)
class UnitMaster(BaseMaster):
    symbol = models.CharField(max_length=50, db_column='Symbol')

    class Meta:
        db_table = 'Unit_Master'
        verbose_name = 'Unit Master'
        verbose_name_plural = 'Unit Masters'

    def __str__(self):
        return self.symbol

class VatMaster(BaseMaster):
    terms = models.CharField(max_length=255, db_column='Terms')

    class Meta:
        db_table = 'tblVAT_Master'
        verbose_name = 'VAT/CST Master'
        verbose_name_plural = 'VAT/CST Masters'

    def __str__(self):
        return self.terms

class ExciseMaster(BaseMaster):
    terms = models.CharField(max_length=255, db_column='Terms')

    class Meta:
        db_table = 'tblExciseser_Master'
        verbose_name = 'Excise Master'
        verbose_name_plural = 'Excise Masters'

    def __str__(self):
        return self.terms

# Core Entity Models
class CustomerMaster(models.Model):
    customer_id = models.CharField(primary_key=True, max_length=50, db_column='CustomerId')
    customer_name = models.CharField(max_length=255, db_column='CustomerName')
    regd_address = models.TextField(db_column='RegdAddress')
    regd_country_id = models.IntegerField(db_column='RegdCountry')
    regd_state_id = models.IntegerField(db_column='RegdState')
    regd_city_id = models.IntegerField(db_column='RegdCity')
    regd_pin_no = models.CharField(max_length=10, db_column='RegdPinNo')
    # Assuming Country, State, City models exist elsewhere.
    # We will use simple lookups for now, or define minimal models here.
    # For demonstration, we'll assume foreign keys if the other tables are in scope.

    class Meta:
        managed = False
        db_table = 'SD_Cust_master'
        verbose_name = 'Customer Master'
        verbose_name_plural = 'Customer Masters'

    def __str__(self):
        return self.customer_name

    @property
    def full_address(self):
        # This would ideally join with Country, State, City models
        # For simplicity, we'll return the base address for now.
        # In a real system, you'd fetch related objects.
        return f"{self.regd_address}, {self.regd_pin_no}"

class EnquiryMaster(models.Model):
    enq_id = models.IntegerField(primary_key=True, db_column='EnqId')
    po_status = models.BooleanField(default=False, db_column='POStatus')
    # Add other relevant fields if any from SD_Cust_Enquiry_Master

    class Meta:
        managed = False
        db_table = 'SD_Cust_Enquiry_Master'
        verbose_name = 'Enquiry Master'
        verbose_name_plural = 'Enquiry Masters'

    def __str__(self):
        return f"Enquiry {self.enq_id}"

    def mark_as_quoted(self):
        """Marks this enquiry as having a purchase order status."""
        self.po_status = True
        self.save(update_fields=['po_status'])
        return True


class QuotationDetailsTemp(models.Model):
    # This table holds temporary items for the current session/user.
    # ASP.NET used SessionId, CompId, FinYearId for uniqueness.
    # Django will use user/session for temporary data, perhaps a UUID for the session.
    id = models.IntegerField(primary_key=True, db_column='Id') # PK from original DB
    session_key = models.UUIDField(default=uuid.uuid4, editable=False, db_column='SessionId') # Using UUID as a substitute for ASP.NET SessionId for temporary uniqueness
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    item_desc = models.TextField(db_column='ItemDesc')
    total_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='TotalQty')
    unit = models.ForeignKey(UnitMaster, on_delete=models.PROTECT, db_column='Unit')
    rate = models.DecimalField(max_digits=18, decimal_places=3, db_column='Rate')
    discount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Discount')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Details_Temp'
        verbose_name = 'Quotation Temporary Detail'
        verbose_name_plural = 'Quotation Temporary Details'

    def __str__(self):
        return f"{self.item_desc} ({self.total_qty} {self.unit.symbol})"

    def calculate_line_total(self):
        """Calculates the total for this line item, considering discount."""
        gross_total = self.total_qty * self.rate
        discount_amount = gross_total * (self.discount / 100)
        return gross_total - discount_amount

    @classmethod
    def get_user_temp_details(cls, user_session_key, comp_id, fin_year_id):
        """Retrieves all temporary details for a given session."""
        return cls.objects.filter(session_key=user_session_key, comp_id=comp_id, fin_year_id=fin_year_id)

    @classmethod
    def clear_user_temp_details(cls, user_session_key, comp_id, fin_year_id):
        """Clears all temporary details for a given session."""
        return cls.objects.filter(session_key=user_session_key, comp_id=comp_id, fin_year_id=fin_year_id).delete()


class QuotationMaster(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # PK from original DB
    sys_date = models.DateField(db_column='SysDate')
    sys_time = models.TimeField(db_column='SysTime')
    session_id = models.CharField(max_length=50, db_column='SessionId') # Original ASP.NET session ID
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    customer = models.ForeignKey(CustomerMaster, on_delete=models.PROTECT, db_column='CustomerId')
    enquiry = models.ForeignKey(EnquiryMaster, on_delete=models.PROTECT, db_column='EnqId')
    quotation_no = models.CharField(max_length=10, db_column='QuotationNo')
    payment_terms = models.CharField(max_length=255, db_column='PaymentTerms')
    pf = models.DecimalField(max_digits=18, decimal_places=3, db_column='PF')
    pf_type = models.IntegerField(db_column='PFType') # 0: Amt(Rs), 1: Per(%)
    vat_cst = models.ForeignKey(VatMaster, on_delete=models.PROTECT, db_column='VATCST')
    excise = models.ForeignKey(ExciseMaster, on_delete=models.PROTECT, db_column='Excise')
    octroi = models.DecimalField(max_digits=18, decimal_places=3, db_column='Octroi')
    octroi_type = models.IntegerField(db_column='OctroiType') # 0: Amt(Rs), 1: Per(%)
    warrenty = models.CharField(max_length=255, db_column='Warrenty')
    insurance = models.DecimalField(max_digits=18, decimal_places=3, db_column='Insurance')
    transport = models.CharField(max_length=255, db_column='Transport')
    note_no = models.CharField(max_length=255, db_column='NoteNo')
    registration_no = models.CharField(max_length=255, db_column='RegistrationNo')
    freight = models.DecimalField(max_digits=18, decimal_places=3, db_column='Freight')
    freight_type = models.IntegerField(db_column='FreightType') # 0: Amt(Rs), 1: Per(%)
    remarks = models.TextField(db_column='Remarks', blank=True, null=True)
    validity = models.CharField(max_length=255, db_column='Validity')
    other_charges = models.DecimalField(max_digits=18, decimal_places=3, db_column='OtherCharges')
    other_charges_type = models.IntegerField(db_column='OtherChargesType') # 0: Amt(Rs), 1: Per(%)
    delivery_terms = models.CharField(max_length=255, db_column='DeliveryTerms')
    due_date = models.DateField(db_column='DueDate')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Master'
        verbose_name = 'Quotation Master'
        verbose_name_plural = 'Quotation Masters'

    def __str__(self):
        return self.quotation_no

    @classmethod
    def generate_next_quotation_no(cls, comp_id, fin_year_id):
        """Generates the next sequential quotation number."""
        last_quotation = cls.objects.filter(comp_id=comp_id, fin_year_id=fin_year_id).order_by('-quotation_no').first()
        if last_quotation and last_quotation.quotation_no.isdigit():
            next_num = int(last_quotation.quotation_no) + 1
        else:
            next_num = 1
        return f"{next_num:04d}" # D4 format means pad with leading zeros to 4 digits

    def create_from_temp_details(self, temp_details_queryset):
        """
        Creates final quotation details from a queryset of temporary details.
        This method assumes the QuotationMaster instance is already saved.
        """
        for temp_detail in temp_details_queryset:
            QuotationDetail.objects.create(
                session_id=temp_detail.session_key, # Or map to current user session id
                comp_id=temp_detail.comp_id,
                fin_year_id=temp_detail.fin_year_id,
                master=self, # Link to the newly created QuotationMaster
                item_desc=temp_detail.item_desc,
                total_qty=temp_detail.total_qty,
                unit=temp_detail.unit,
                rate=temp_detail.rate,
                discount=temp_detail.discount
            )
        return True


class QuotationDetail(models.Model):
    id = models.IntegerField(primary_key=True, db_column='Id') # PK from original DB
    session_id = models.CharField(max_length=50, db_column='SessionId') # Original ASP.NET session ID
    comp_id = models.IntegerField(db_column='CompId')
    fin_year_id = models.IntegerField(db_column='FinYearId')
    master = models.ForeignKey(QuotationMaster, on_delete=models.CASCADE, db_column='MId', related_name='details')
    item_desc = models.TextField(db_column='ItemDesc')
    total_qty = models.DecimalField(max_digits=18, decimal_places=3, db_column='TotalQty')
    unit = models.ForeignKey(UnitMaster, on_delete=models.PROTECT, db_column='Unit')
    rate = models.DecimalField(max_digits=18, decimal_places=3, db_column='Rate')
    discount = models.DecimalField(max_digits=18, decimal_places=3, db_column='Discount')

    class Meta:
        managed = False
        db_table = 'SD_Cust_Quotation_Details'
        verbose_name = 'Quotation Detail'
        verbose_name_plural = 'Quotation Details'

    def __str__(self):
        return f"Detail for {self.master.quotation_no}: {self.item_desc}"

    def calculate_line_total(self):
        """Calculates the total for this line item, considering discount."""
        gross_total = self.total_qty * self.rate
        discount_amount = gross_total * (self.discount / 100)
        return gross_total - discount_amount

```

### 4.2 Forms (`sales_quotation/forms.py`)

We will create forms for the temporary quotation details and the final terms and conditions.

```python
from django import forms
from .models import QuotationDetailsTemp, QuotationMaster, UnitMaster, VatMaster, ExciseMaster
from django.core.validators import RegexValidator

# Custom validators based on ASP.NET RegularExpressionValidator
decimal_regex_validator = RegexValidator(
    r"^\d{1,15}(\.\d{0,3})?$",
    "Enter a valid decimal number (up to 15 digits before, 3 after decimal)."
)
date_regex_validator = RegexValidator(
    r"^([1-9]|0[1-9]|[12][0-9]|3[01])[- /.]([1-9]|0[1-9]|1[012])[- /.][0-9]{4}$",
    "Enter a valid date in DD-MM-YYYY format."
)

class QuotationDetailTempForm(forms.ModelForm):
    # Overriding fields to apply custom validators and widgets
    item_desc = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm', 'rows': 4}),
        label="Description & Specification of Goods"
    )
    total_qty = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3'}),
        label="Total Qty of Goods"
    )
    rate = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3'}),
        label="Rate per unit"
    )
    discount = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3'}),
        label="Discount (in %)"
    )
    unit = forms.ModelChoiceField(
        queryset=UnitMaster.objects.all(),
        empty_label="Select",
        widget=forms.Select(attrs={'class': 'box3'}),
        label="Unit"
    )

    class Meta:
        model = QuotationDetailsTemp
        fields = ['item_desc', 'total_qty', 'rate', 'discount', 'unit']
        # Note: session_key, comp_id, fin_year_id will be set in the view
        # Widgets for fields already defined above.

    def clean_unit(self):
        unit = self.cleaned_data['unit']
        if unit.symbol == 'Select': # Check for initial value if present
            raise forms.ValidationError("Please select a valid Unit.")
        return unit


class QuotationTermsForm(forms.ModelForm):
    # Using ChoiceField for types (Amt/Per) as integers are stored
    PF_TYPE_CHOICES = [(0, 'Amt(Rs)'), (1, 'Per(%)')]

    payment_terms = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Payment Terms"
    )
    pf = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="P & F"
    )
    pf_type = forms.ChoiceField(
        choices=PF_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'box3 inline-block w-full sm:w-32 ml-2'}),
        label="" # Label handled by UI
    )
    vat_cst = forms.ModelChoiceField(
        queryset=VatMaster.objects.all(),
        widget=forms.Select(attrs={'class': 'box3 w-full sm:w-80'}),
        label="VAT/ CST"
    )
    excise = forms.ModelChoiceField(
        queryset=ExciseMaster.objects.all(),
        widget=forms.Select(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Excise / Service Tax"
    )
    octroi = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Octroi"
    )
    octroi_type = forms.ChoiceField(
        choices=PF_TYPE_CHOICES, # Same choices as PF type
        widget=forms.Select(attrs={'class': 'box3 inline-block w-full sm:w-32 ml-2'}),
        label=""
    )
    warrenty = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Warrenty"
    )
    insurance = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Insurance"
    )
    transport = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Mode of Transport"
    )
    note_no = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="R.R./G.C. Note No."
    )
    registration_no = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="If by motor vehicle, it's registr. no:"
    )
    freight = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Freight"
    )
    freight_type = forms.ChoiceField(
        choices=PF_TYPE_CHOICES, # Same choices as PF type
        widget=forms.Select(attrs={'class': 'box3 inline-block w-full sm:w-32 ml-2'}),
        label=""
    )
    due_date = forms.DateField(
        widget=forms.DateInput(attrs={'class': 'box3 w-full sm:w-80', 'type': 'date'}), # HTML5 date input
        label="Due Date",
        validators=[date_regex_validator]
    )
    validity = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Validity"
    )
    other_charges = forms.DecimalField(
        validators=[decimal_regex_validator],
        widget=forms.NumberInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Other Charges"
    )
    other_charges_type = forms.ChoiceField(
        choices=PF_TYPE_CHOICES, # Same choices as PF type
        widget=forms.Select(attrs={'class': 'box3 inline-block w-full sm:w-32 ml-2'}),
        label=""
    )
    delivery_terms = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'box3 w-full sm:w-80'}),
        label="Delivery Terms"
    )
    remarks = forms.CharField(
        widget=forms.Textarea(attrs={'class': 'box3 w-full sm:w-full', 'rows': 3}),
        label="Remarks",
        required=False
    )

    class Meta:
        model = QuotationMaster
        fields = [
            'payment_terms', 'pf', 'pf_type', 'vat_cst', 'excise', 'octroi', 'octroi_type',
            'warrenty', 'insurance', 'transport', 'note_no', 'registration_no',
            'freight', 'freight_type', 'due_date', 'validity', 'other_charges',
            'other_charges_type', 'delivery_terms', 'remarks'
        ]
        # customer, enquiry, comp_id, fin_year_id, session_id, quotation_no will be set in the view
```

### 4.3 Views (`sales_quotation/views.py`)

A single view to manage the wizard logic via HTMX and Alpine.js, with additional partial views for dynamic content.

```python
import uuid
from datetime import datetime
from django.views.generic import TemplateView, View
from django.views.generic.edit import CreateView, UpdateView, DeleteView
from django.urls import reverse_lazy
from django.shortcuts import get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
from django.db import transaction
from django.contrib import messages

from .models import (
    CustomerMaster, EnquiryMaster,
    QuotationDetailsTemp, QuotationMaster, QuotationDetail,
    UnitMaster
)
from .forms import QuotationDetailTempForm, QuotationTermsForm

# Helper to get user/session specific data for temporary records
def get_session_data(request):
    # In a real application, you'd map these to Django's session/user system
    # For now, we simulate based on ASP.NET's session variables
    session_key = request.session.get('user_quotation_session_key')
    if not session_key:
        session_key = uuid.uuid4()
        request.session['user_quotation_session_key'] = str(session_key)
    
    # Assuming 'compid' and 'finyear' are also stored in session or user profile
    # For now, hardcoding or getting from query params as in ASP.NET original
    comp_id = int(request.GET.get('CompId', 1)) # Default or get from user profile
    fin_year_id = int(request.GET.get('FinYearId', 1)) # Default or get from user profile
    username_session = request.session.get('username', 'AnonymousUser') # Simulating ASP.NET's SId

    return {
        'session_key': session_key,
        'comp_id': comp_id,
        'fin_year_id': fin_year_id,
        'username_session': username_session
    }


class QuotationCreateWizardView(TemplateView):
    """
    Main view for the Quotation creation wizard.
    Manages the overall page structure and initial data display.
    Tabs are handled client-side with Alpine.js and content loaded via HTMX.
    """
    template_name = 'sales_quotation/quotation/create_wizard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Simulating data from QueryString as in ASP.NET Page_Load
        customer_id = self.request.GET.get('CustomerId')
        enquiry_id = self.request.GET.get('EnqId')

        customer = None
        enquiry = None
        customer_address_display = "N/A"

        if customer_id:
            customer = get_object_or_404(CustomerMaster, customer_id=customer_id)
            # Simulating address assembly as in ASP.NET code
            # This would ideally involve more database lookups for Country, State, City names
            customer_address_display = customer.full_address # Using model property for fat model

        if enquiry_id:
            enquiry = get_object_or_404(EnquiryMaster, enq_id=enquiry_id)

        context['customer'] = customer
        context['enquiry'] = enquiry
        context['customer_address_display'] = customer_address_display
        context['enquiry_no'] = enquiry_id # LblEnqNo.Text in ASP.NET

        # Pass initial form for Goods Details tab, to be rendered as a partial
        context['goods_detail_form'] = QuotationDetailTempForm()
        context['terms_form'] = QuotationTermsForm()
        
        return context


class CustomerDetailsPartialView(TemplateView):
    """
    HTMX partial view for the Customer Details tab.
    """
    template_name = 'sales_quotation/quotation/_customer_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Same logic as in QuotationCreateWizardView for customer details
        customer_id = self.request.GET.get('CustomerId')
        enquiry_id = self.request.GET.get('EnqId')

        customer = None
        enquiry = None
        customer_address_display = "N/A"

        if customer_id:
            customer = get_object_or_404(CustomerMaster, customer_id=customer_id)
            customer_address_display = customer.full_address

        if enquiry_id:
            enquiry = get_object_or_404(EnquiryMaster, enq_id=enquiry_id)

        context['customer'] = customer
        context['enquiry'] = enquiry
        context['customer_address_display'] = customer_address_display
        context['enquiry_no'] = enquiry_id
        return context


class GoodsDetailsPartialView(TemplateView):
    """
    HTMX partial view for the Goods Details tab.
    Contains the form for adding new items and a container for the items table.
    """
    template_name = 'sales_quotation/quotation/_goods_details.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = QuotationDetailTempForm()
        return context


class QuotationDetailTempTableView(View):
    """
    HTMX partial view to render only the DataTables portion of temporary quotation details.
    This is fetched dynamically and refreshed after CRUD operations.
    """
    def get(self, request, *args, **kwargs):
        session_data = get_session_data(request)
        temp_details = QuotationDetailsTemp.get_user_temp_details(
            str(session_data['session_key']), session_data['comp_id'], session_data['fin_year_id']
        ).order_by('-id') # Order by Id Desc as in ASP.NET

        context = {'quotation_details_temp': temp_details}
        return HttpResponse(
            self.request.htmx.render(
                'sales_quotation/quotation/_goods_details_table.html', context
            )
        )


class QuotationDetailTempCreateView(CreateView):
    """
    Handles adding a new temporary quotation detail.
    Designed for HTMX submission.
    """
    model = QuotationDetailsTemp
    form_class = QuotationDetailTempForm
    template_name = 'sales_quotation/quotation/_goods_detail_form.html' # Use a partial for modal

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        if 'pk' in self.kwargs: # This view is also used for edit via the same form
            kwargs['instance'] = get_object_or_404(QuotationDetailsTemp, pk=self.kwargs['pk'])
        return kwargs

    def form_valid(self, form):
        session_data = get_session_data(self.request)
        form.instance.session_key = str(session_data['session_key'])
        form.instance.comp_id = session_data['comp_id']
        form.instance.fin_year_id = session_data['fin_year_id']
        
        # Handle original ASP.NET's `Id` behavior. If the ID is an auto-incrementing
        # integer handled by the database, we might need to retrieve the max ID and increment.
        # For `managed=False` models, Django doesn't auto-handle PKs unless specified.
        # Assuming `Id` is effectively auto-incrementing in the original DB,
        # we might need to manually set it for new records if the DB doesn't handle it
        # or rely on DB default if it does.
        # For simplicity, if ID is not provided, let the DB assign. If the original
        # ASP.NET code retrieves a max ID to set, this is a more complex sync.
        # Given `Id` is DataKeyNames="Id", it is likely the PK.
        # For this example, let's assume `Id` column is auto-incrementing in the target DB.

        response = super().form_valid(form)
        messages.success(self.request, 'Item added successfully.')
        
        # HTMX response for success: close modal, trigger list refresh
        if self.request.htmx:
            return HttpResponse(
                status=204, # No Content
                headers={'HX-Trigger': 'refreshQuotationDetailList'}
            )
        return response

    def form_invalid(self, form):
        # HTMX response for validation errors: re-render the form with errors
        if self.request.htmx:
            return HttpResponse(
                self.request.htmx.render(self.template_name, {'form': form})
            )
        return super().form_invalid(form)


class QuotationDetailTempUpdateView(UpdateView):
    """
    Handles updating an existing temporary quotation detail.
    Designed for HTMX submission.
    """
    model = QuotationDetailsTemp
    form_class = QuotationDetailTempForm
    template_name = 'sales_quotation/quotation/_goods_detail_form.html' # Use same partial as create

    def get_object(self, queryset=None):
        # Ensure only current session's records can be updated
        session_data = get_session_data(self.request)
        return get_object_or_404(
            QuotationDetailsTemp, 
            pk=self.kwargs['pk'], 
            session_key=str(session_data['session_key']),
            comp_id=session_data['comp_id'],
            fin_year_id=session_data['fin_year_id']
        )

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Item updated successfully.')
        if self.request.htmx:
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshQuotationDetailList'}
            )
        return response

    def form_invalid(self, form):
        if self.request.htmx:
            return HttpResponse(
                self.request.htmx.render(self.template_name, {'form': form})
            )
        return super().form_invalid(form)


class QuotationDetailTempDeleteView(DeleteView):
    """
    Handles deleting a temporary quotation detail.
    Designed for HTMX submission.
    """
    model = QuotationDetailsTemp
    template_name = 'sales_quotation/quotation/_confirm_delete.html' # Generic delete confirmation modal

    def get_object(self, queryset=None):
        # Ensure only current session's records can be deleted
        session_data = get_session_data(self.request)
        return get_object_or_404(
            QuotationDetailsTemp, 
            pk=self.kwargs['pk'], 
            session_key=str(session_data['session_key']),
            comp_id=session_data['comp_id'],
            fin_year_id=session_data['fin_year_id']
        )

    def delete(self, request, *args, **kwargs):
        response = super().delete(request, *args, **kwargs)
        messages.success(self.request, 'Item deleted successfully.')
        if request.htmx:
            return HttpResponse(
                status=204,
                headers={'HX-Trigger': 'refreshQuotationDetailList'}
            )
        return response


class TermsConditionsPartialView(TemplateView):
    """
    HTMX partial view for the Terms & Conditions tab.
    Contains the form for final quotation terms.
    """
    template_name = 'sales_quotation/quotation/_terms_conditions.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['form'] = QuotationTermsForm()
        return context


class QuotationFinalizeView(View):
    """
    Handles the final submission of the quotation (Button6_Click equivalent).
    This logic moves temporary details to permanent tables and updates enquiry status.
    """
    def post(self, request, *args, **kwargs):
        session_data = get_session_data(request)
        
        # Simulating QueryString values
        customer_id_param = request.GET.get('CustomerId')
        enquiry_id_param = request.GET.get('EnqId')

        customer = get_object_or_404(CustomerMaster, customer_id=customer_id_param)
        enquiry = get_object_or_404(EnquiryMaster, enq_id=enquiry_id_param)

        form = QuotationTermsForm(request.POST)

        if form.is_valid():
            try:
                with transaction.atomic():
                    # 1. Generate new Quotation Number
                    quotation_no = QuotationMaster.generate_next_quotation_no(
                        session_data['comp_id'], session_data['fin_year_id']
                    )

                    # 2. Create QuotationMaster entry
                    quotation_master = form.save(commit=False)
                    quotation_master.sys_date = timezone.now().date()
                    quotation_master.sys_time = timezone.now().time()
                    quotation_master.session_id = session_data['username_session'] # Using ASP.NET's SId
                    quotation_master.comp_id = session_data['comp_id']
                    quotation_master.fin_year_id = session_data['fin_year_id']
                    quotation_master.customer = customer
                    quotation_master.enquiry = enquiry
                    quotation_master.quotation_no = quotation_no
                    quotation_master.save()

                    # 3. Transfer temporary details to permanent QuotationDetail
                    temp_details = QuotationDetailsTemp.get_user_temp_details(
                        str(session_data['session_key']), session_data['comp_id'], session_data['fin_year_id']
                    )
                    
                    if not temp_details.exists():
                        messages.error(request, 'No goods details added to the quotation.')
                        # Re-render the form with error, using HTMX partial
                        return HttpResponse(request.htmx.render(
                            'sales_quotation/quotation/_terms_conditions.html', {'form': form}
                        ))

                    quotation_master.create_from_temp_details(temp_details)

                    # 4. Clear temporary details
                    QuotationDetailsTemp.clear_user_temp_details(
                        str(session_data['session_key']), session_data['comp_id'], session_data['fin_year_id']
                    )

                    # 5. Update Enquiry Status
                    enquiry.mark_as_quoted()

                messages.success(request, f'Quotation {quotation_no} is generated successfully.')
                # Redirect to a success page or list view via HTMX
                if request.htmx:
                    # HX-Redirect is better for full page redirects after HTMX operations
                    return HttpResponse(status=204, headers={'HX-Redirect': reverse_lazy('quotation_list')})
                else:
                    return redirect(reverse_lazy('quotation_list')) # A dummy list view or success page
            
            except Exception as e:
                messages.error(request, f'Error generating quotation: {e}')
                # Re-render the form with error, using HTMX partial
                return HttpResponse(request.htmx.render(
                    'sales_quotation/quotation/_terms_conditions.html', {'form': form}
                ))
        else:
            # Form is invalid, re-render the form with errors
            messages.error(request, 'Please correct the errors in the terms and conditions.')
            return HttpResponse(request.htmx.render(
                'sales_quotation/quotation/_terms_conditions.html', {'form': form}
            ))

# Dummy view for success redirect (Quotation_New.aspx equivalent)
class QuotationListView(TemplateView):
    template_name = 'sales_quotation/quotation/list_all_quotations.html' # Or any appropriate list view
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['quotations'] = QuotationMaster.objects.all().order_by('-sys_date', '-sys_time')
        return context

```

### 4.4 Templates

All templates will extend `core/base.html` and use Tailwind CSS for styling. HTMX and Alpine.js are integrated for dynamic interactions.

#### `sales_quotation/quotation/create_wizard.html` (Main page with tab structure)

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8" x-data="{ activeTab: localStorage.getItem('activeTab') || 'customer' }">
    <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-xl p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6 border-b pb-4">Customer Quotation - New</h2>

        <div class="mb-6 border-b border-gray-200">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="myTab" role="tablist">
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg"
                            :class="{ 'border-blue-600 text-blue-600': activeTab === 'customer', 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab !== 'customer' }"
                            x-on:click="activeTab = 'customer'; localStorage.setItem('activeTab', 'customer')"
                            hx-get="{% url 'sales_quotation:customer_details_partial' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
                            hx-target="#tabContent"
                            hx-swap="innerHTML"
                            hx-trigger="click, load[activeTab === 'customer'] once">
                        Customer Details
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg"
                            :class="{ 'border-blue-600 text-blue-600': activeTab === 'goods', 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab !== 'goods' }"
                            x-on:click="activeTab = 'goods'; localStorage.setItem('activeTab', 'goods')"
                            hx-get="{% url 'sales_quotation:goods_details_partial' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
                            hx-target="#tabContent"
                            hx-swap="innerHTML"
                            hx-trigger="click, load[activeTab === 'goods'] once">
                        Goods Details
                    </button>
                </li>
                <li class="mr-2" role="presentation">
                    <button class="inline-block p-4 border-b-2 rounded-t-lg"
                            :class="{ 'border-blue-600 text-blue-600': activeTab === 'terms', 'border-transparent text-gray-500 hover:text-gray-600 hover:border-gray-300': activeTab !== 'terms' }"
                            x-on:click="activeTab = 'terms'; localStorage.setItem('activeTab', 'terms')"
                            hx-get="{% url 'sales_quotation:terms_conditions_partial' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
                            hx-target="#tabContent"
                            hx-swap="innerHTML"
                            hx-trigger="click, load[activeTab === 'terms'] once">
                        Terms & Conditions
                    </button>
                </li>
            </ul>
        </div>

        <div id="tabContent" class="p-4">
            <!-- Tab content will be loaded here via HTMX -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading tab content...</p>
            </div>
        </div>
    </div>

    <!-- Global Modal for Forms/Deletes -->
    <div id="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center hidden"
         x-cloak x-init="document.getElementById('modal').classList.add('hidden')"
         _="on click if event.target.id === 'modal' remove .flex then add .hidden to #modal else if event.target.closest('.modal-close') remove .flex then add .hidden to #modal">
        <div id="modalContent" class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full"></div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('htmx:afterSwap', function(event) {
        // Re-initialize Alpine.js components that might be in the newly swapped content
        if (event.detail.target.id === 'tabContent') {
            Alpine.initTree(event.detail.target);
        }
    });

    // Handle HTMX triggers to close modal (e.g., after form submission)
    document.body.addEventListener('htmx:beforeSwap', function(evt) {
        if (evt.detail.xhr.status === 204) {
            // Check for HX-Trigger header for modal closing
            const hxTrigger = evt.detail.xhr.getResponseHeader('HX-Trigger');
            if (hxTrigger && hxTrigger.includes('refreshQuotationDetailList')) {
                const modal = document.getElementById('modal');
                if (modal) {
                    modal.classList.remove('flex');
                    modal.classList.add('hidden');
                }
            }
        }
    });
</script>
{% endblock %}
```

#### `sales_quotation/quotation/_customer_details.html` (Partial for Customer Details tab)

```html
<div class="fontcss p-4">
    <table width="100%" class="fontcss">
        <tr>
            <td width="15%">Name of Customer&nbsp;</td>
            <td>
                :
                <span class="font-bold">{{ customer.customer_name|default:"N/A" }}</span>
                <input type="hidden" id="hfCustId" value="{{ customer.customer_id|default:"" }}">
                <input type="hidden" id="hfEnqId" value="{{ enquiry.enq_id|default:"" }}">
            </td>
            <td class="w-20">Enquiry No</td>
            <td>
                :
                <span class="font-bold text-blue-700">{{ enquiry.enq_id|default:"N/A" }}</span>
            </td>
            <td></td>
        </tr>
        <tr>
            <td class="align-top">Regd. Office Address</td>
            <td rowspan="2" class="align-top">
                :
                <span class="text-gray-700">{{ customer_address_display|linebreaksbr }}</span>
            </td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
        </tr>
        <tr>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>
                <button type="button" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        x-on:click="activeTab = 'goods'; localStorage.setItem('activeTab', 'goods')"
                        hx-get="{% url 'sales_quotation:goods_details_partial' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
                        hx-target="#tabContent" hx-swap="innerHTML">
                    Next
                </button>
                <a href="{% url 'quotation_list' %}" class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded ml-2">
                    Cancel
                </a>
            </td>
        </tr>
    </table>
</div>

```

#### `sales_quotation/quotation/_goods_details.html` (Partial for Goods Details tab)

```html
<div class="fontcss p-4">
    <div class="mb-6 p-4 border rounded-lg bg-gray-50">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">Add Item Details</h4>
        <form hx-post="{% url 'sales_quotation:quotation_detail_temp_add' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}" 
              hx-target="#goodsDetailFormContainer" hx-swap="outerHTML">
            {% csrf_token %}
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <label for="{{ form.item_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.item_desc.label }}</label>
                    {{ form.item_desc }}
                    {% if form.item_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.item_desc.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.total_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.total_qty.label }}</label>
                    {{ form.total_qty }}
                    {% if form.total_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.total_qty.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.rate.label }}</label>
                    {{ form.rate }}
                    {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.discount.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.discount.label }}</label>
                    {{ form.discount }}
                    {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.unit.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.unit.label }}</label>
                    {{ form.unit }}
                    {% if form.unit.errors %}<p class="text-red-500 text-xs mt-1">{{ form.unit.errors }}</p>{% endif %}
                </div>
            </div>
            <div class="mt-6 flex justify-end space-x-4">
                <button type="submit" class="redbox bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Submit Item
                </button>
                <button type="button" class="redbox bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
                        x-on:click="activeTab = 'terms'; localStorage.setItem('activeTab', 'terms')"
                        hx-get="{% url 'sales_quotation:terms_conditions_partial' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
                        hx-target="#tabContent" hx-swap="innerHTML">
                    Next
                </button>
                <a href="{% url 'quotation_list' %}" class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Cancel
                </a>
            </div>
        </form>
    </div>

    <div class="mt-8">
        <h4 class="text-lg font-semibold text-gray-800 mb-4">Current Quotation Items</h4>
        <div id="quotationDetailTempTableContainer"
             hx-trigger="load, refreshQuotationDetailList from:body"
             hx-get="{% url 'sales_quotation:quotation_detail_temp_table' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
             hx-swap="innerHTML">
            <!-- DataTables will be loaded here -->
            <div class="text-center py-10">
                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                <p class="mt-2 text-gray-600">Loading items...</p>
            </div>
        </div>
    </div>
</div>
```

#### `sales_quotation/quotation/_goods_details_table.html` (Partial for DataTables)

```html
<div class="overflow-x-auto bg-white rounded-lg shadow">
    <table id="quotationDetailTempTable" class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
            <tr>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Discount (%)</th>
                <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
            {% if quotation_details_temp %}
                {% for item in quotation_details_temp %}
                <tr>
                    <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.item_desc }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ item.unit.symbol }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500 text-right">{{ item.total_qty|floatformat:"3" }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500 text-right">{{ item.rate|floatformat:"2" }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500 text-right">{{ item.discount|floatformat:"2" }}</td>
                    <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                        <button class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-1 px-2 rounded-md mr-2"
                                hx-get="{% url 'sales_quotation:quotation_detail_temp_edit' item.pk %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
                                hx-target="#modalContent"
                                hx-trigger="click"
                                _="on click add .flex then remove .hidden to #modal">
                            Edit
                        </button>
                        <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-1 px-2 rounded-md"
                                hx-get="{% url 'sales_quotation:quotation_detail_temp_delete' item.pk %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}"
                                hx-target="#modalContent"
                                hx-trigger="click"
                                _="on click add .flex then remove .hidden to #modal">
                            Delete
                        </button>
                    </td>
                </tr>
                {% endfor %}
            {% else %}
            <tr>
                <td colspan="7" class="py-4 px-6 text-center text-gray-500">No data to display !</td>
            </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<script>
    // DataTables initialization
    $(document).ready(function() {
        // Destroy existing DataTable instance if it exists to prevent re-initialization errors
        if ($.fn.DataTable.isDataTable('#quotationDetailTempTable')) {
            $('#quotationDetailTempTable').DataTable().destroy();
        }
        $('#quotationDetailTempTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true
        });
    });
</script>
```

#### `sales_quotation/quotation/_goods_detail_form.html` (Partial for Add/Edit Item modal)

```html
<div class="p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-5">{{ form.instance.pk|yesno:'Edit,Add' }} Quotation Item</h3>
    <form hx-post="{{ request.path }}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}" hx-swap="none">
        {% csrf_token %}
        
        <div class="space-y-4">
            <div>
                <label for="{{ form.item_desc.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.item_desc.label }}</label>
                {{ form.item_desc }}
                {% if form.item_desc.errors %}<p class="text-red-500 text-xs mt-1">{{ form.item_desc.errors }}</p>{% endif %}
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="{{ form.total_qty.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.total_qty.label }}</label>
                    {{ form.total_qty }}
                    {% if form.total_qty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.total_qty.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.rate.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.rate.label }}</label>
                    {{ form.rate }}
                    {% if form.rate.errors %}<p class="text-red-500 text-xs mt-1">{{ form.rate.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.discount.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.discount.label }}</label>
                    {{ form.discount }}
                    {% if form.discount.errors %}<p class="text-red-500 text-xs mt-1">{{ form.discount.errors }}</p>{% endif %}
                </div>
                <div>
                    <label for="{{ form.unit.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.unit.label }}</label>
                    {{ form.unit }}
                    {% if form.unit.errors %}<p class="text-red-500 text-xs mt-1">{{ form.unit.errors }}</p>{% endif %}
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="button" 
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded modal-close">
                Cancel
            </button>
            <button 
                type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Save
            </button>
        </div>
    </form>
</div>
```

#### `sales_quotation/quotation/_terms_conditions.html` (Partial for Terms & Conditions tab)

```html
<div class="fontcss p-4">
    <h3 class="text-lg font-medium text-gray-900 mb-5">Quotation Terms & Conditions</h3>
    <form hx-post="{% url 'sales_quotation:quotation_finalize' %}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}" 
          hx-target="#tabContent" hx-swap="outerHTML">
        {% csrf_token %}
        
        {% if messages %}
        <div class="mb-4">
            {% for message in messages %}
            <div class="p-3 mb-2 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="col-span-full">
                <label for="{{ form.payment_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.payment_terms.label }}</label>
                {{ form.payment_terms }}
                {% if form.payment_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.payment_terms.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.pf.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.pf.label }}</label>
                <div class="flex items-center">
                    {{ form.pf }} {{ form.pf_type }}
                </div>
                {% if form.pf.errors %}<p class="text-red-500 text-xs mt-1">{{ form.pf.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.excise.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.excise.label }}</label>
                {{ form.excise }}
                {% if form.excise.errors %}<p class="text-red-500 text-xs mt-1">{{ form.excise.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.vat_cst.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.vat_cst.label }}</label>
                {{ form.vat_cst }}
                {% if form.vat_cst.errors %}<p class="text-red-500 text-xs mt-1">{{ form.vat_cst.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.octroi.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.octroi.label }}</label>
                <div class="flex items-center">
                    {{ form.octroi }} {{ form.octroi_type }}
                </div>
                {% if form.octroi.errors %}<p class="text-red-500 text-xs mt-1">{{ form.octroi.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.warrenty.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.warrenty.label }}</label>
                {{ form.warrenty }}
                {% if form.warrenty.errors %}<p class="text-red-500 text-xs mt-1">{{ form.warrenty.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.insurance.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.insurance.label }}</label>
                {{ form.insurance }}
                {% if form.insurance.errors %}<p class="text-red-500 text-xs mt-1">{{ form.insurance.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.transport.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.transport.label }}</label>
                {{ form.transport }}
                {% if form.transport.errors %}<p class="text-red-500 text-xs mt-1">{{ form.transport.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.note_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.note_no.label }}</label>
                {{ form.note_no }}
                {% if form.note_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.note_no.errors }}</p>{% endif %}
            </div>
            <div class="col-span-full">
                <label for="{{ form.registration_no.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.registration_no.label }}</label>
                {{ form.registration_no }}
                {% if form.registration_no.errors %}<p class="text-red-500 text-xs mt-1">{{ form.registration_no.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.freight.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.freight.label }}</label>
                <div class="flex items-center">
                    {{ form.freight }} {{ form.freight_type }}
                </div>
                {% if form.freight.errors %}<p class="text-red-500 text-xs mt-1">{{ form.freight.errors }}</p{% endif %}
            </div>
            <div>
                <label for="{{ form.due_date.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.due_date.label }}</label>
                {{ form.due_date }}
                {% if form.due_date.errors %}<p class="text-red-500 text-xs mt-1">{{ form.due_date.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.validity.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.validity.label }}</label>
                {{ form.validity }}
                {% if form.validity.errors %}<p class="text-red-500 text-xs mt-1">{{ form.validity.errors }}</p>{% endif %}
            </div>
            <div>
                <label for="{{ form.other_charges.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.other_charges.label }}</label>
                <div class="flex items-center">
                    {{ form.other_charges }} {{ form.other_charges_type }}
                </div>
                {% if form.other_charges.errors %}<p class="text-red-500 text-xs mt-1">{{ form.other_charges.errors }}</p>{% endif %}
            </div>
            <div class="col-span-full">
                <label for="{{ form.delivery_terms.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.delivery_terms.label }}</label>
                {{ form.delivery_terms }}
                {% if form.delivery_terms.errors %}<p class="text-red-500 text-xs mt-1">{{ form.delivery_terms.errors }}</p>{% endif %}
            </div>
            <div class="col-span-full">
                <label for="{{ form.remarks.id_for_label }}" class="block text-sm font-medium text-gray-700">{{ form.remarks.label }}</label>
                {{ form.remarks }}
                {% if form.remarks.errors %}<p class="text-red-500 text-xs mt-1">{{ form.remarks.errors }}</p>{% endif %}
            </div>
        </div>

        <div class="mt-6 flex items-center justify-end space-x-4">
            <button 
                type="submit" 
                class="redbox bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                Submit Quotation
            </button>
            <a href="{% url 'quotation_list' %}" class="redbox bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Cancel
            </a>
        </div>
    </form>
</div>
```

#### `sales_quotation/quotation/_confirm_delete.html` (Partial for Delete confirmation modal)

```html
<div class="p-6 text-center">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm Deletion</h3>
    <p class="text-gray-600 mb-6">Are you sure you want to delete this item?</p>
    <div class="flex justify-center space-x-4">
        <button 
            type="button" 
            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded modal-close">
            Cancel
        </button>
        <button 
            hx-post="{{ request.path }}?CustomerId={{ customer.customer_id }}&EnqId={{ enquiry.enq_id }}" 
            hx-swap="none"
            type="button" 
            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            Delete
        </button>
    </div>
</div>
```

#### `sales_quotation/quotation/list_all_quotations.html` (Example Quotation List View for redirection)

This acts as a placeholder for the `Quotation_New.aspx` redirect.

```html
{% extends 'core/base.html' %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">All Customer Quotations</h2>
        <a href="{% url 'sales_quotation:quotation_create' %}" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Create New Quotation
        </a>
    </div>

    {% if messages %}
    <div class="mb-4">
        {% for message in messages %}
        <div class="p-3 mb-2 rounded-md {% if message.tags == 'error' %}bg-red-100 text-red-700{% elif message.tags == 'success' %}bg-green-100 text-green-700{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="overflow-x-auto bg-white rounded-lg shadow">
        <table id="allQuotationTable" class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">SN</th>
                    <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quotation No.</th>
                    <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                    <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Enquiry No.</th>
                    <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                    <th scope="col" class="py-3 px-6 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th scope="col" class="py-3 px-6 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% if quotations %}
                    {% for quotation in quotations %}
                    <tr>
                        <td class="py-4 px-6 whitespace-nowrap text-sm font-medium text-gray-900">{{ forloop.counter }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ quotation.quotation_no }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ quotation.customer.customer_name }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ quotation.enquiry.enq_id }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">{{ quotation.due_date|date:"d-M-Y" }}</td>
                        <td class="py-4 px-6 whitespace-nowrap text-sm text-gray-500">Generated</td>
                        <td class="py-4 px-6 whitespace-nowrap text-right text-sm font-medium">
                            <a href="#" class="text-blue-600 hover:text-blue-900">View Details</a>
                        </td>
                    </tr>
                    {% endfor %}
                {% else %}
                <tr>
                    <td colspan="7" class="py-4 px-6 text-center text-gray-500">No quotations found.</td>
                </tr>
                {% endif %}
            </tbody>
        </table>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        $('#allQuotationTable').DataTable({
            "pageLength": 10,
            "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
            "searching": true,
            "ordering": true,
            "paging": true
        });
    });
</script>
{% endblock %}
```

### 4.5 URLs (`sales_quotation/urls.py`)

```python
from django.urls import path
from .views import (
    QuotationCreateWizardView,
    CustomerDetailsPartialView, GoodsDetailsPartialView,
    QuotationDetailTempTableView, QuotationDetailTempCreateView,
    QuotationDetailTempUpdateView, QuotationDetailTempDeleteView,
    TermsConditionsPartialView, QuotationFinalizeView,
    QuotationListView # For redirection after completion
)

app_name = 'sales_quotation' # Important for namespacing URLs

urlpatterns = [
    # Main wizard view
    path('quotation/new/', QuotationCreateWizardView.as_view(), name='quotation_create'),
    
    # HTMX partials for tabs
    path('quotation/new/customer-details/', CustomerDetailsPartialView.as_view(), name='customer_details_partial'),
    path('quotation/new/goods-details/', GoodsDetailsPartialView.as_view(), name='goods_details_partial'),
    path('quotation/new/terms-conditions/', TermsConditionsPartialView.as_view(), name='terms_conditions_partial'),

    # HTMX endpoints for temporary quotation details (Goods Details tab)
    path('quotation/new/goods-details/table/', QuotationDetailTempTableView.as_view(), name='quotation_detail_temp_table'),
    path('quotation/new/goods-details/add/', QuotationDetailTempCreateView.as_view(), name='quotation_detail_temp_add'),
    path('quotation/new/goods-details/edit/<int:pk>/', QuotationDetailTempUpdateView.as_view(), name='quotation_detail_temp_edit'),
    path('quotation/new/goods-details/delete/<int:pk>/', QuotationDetailTempDeleteView.as_view(), name='quotation_detail_temp_delete'),

    # HTMX endpoint for final quotation submission
    path('quotation/new/finalize/', QuotationFinalizeView.as_view(), name='quotation_finalize'),

    # Dummy list view for redirection after successful creation (ASP.NET's Quotation_New.aspx)
    path('quotations/', QuotationListView.as_view(), name='quotation_list'),
]

```

### 4.6 Tests (`sales_quotation/tests.py`)

Comprehensive tests for models, forms, and views including HTMX interactions.

```python
import uuid
from datetime import date, time
from django.test import TestCase, Client
from django.urls import reverse
from django.db.utils import IntegrityError
from django.contrib.messages import get_messages

from .models import (
    UnitMaster, VatMaster, ExciseMaster, CustomerMaster, EnquiryMaster,
    QuotationDetailsTemp, QuotationMaster, QuotationDetail
)
from .forms import QuotationDetailTempForm, QuotationTermsForm

# --- Model Tests ---
class MasterDataModelTest(TestCase):
    # Dummy data for related models to satisfy FK constraints
    @classmethod
    def setUpTestData(cls):
        UnitMaster.objects.create(id=1, symbol='Pcs')
        VatMaster.objects.create(id=1, terms='VAT 10%')
        ExciseMaster.objects.create(id=1, terms='EXC 5%')
        CustomerMaster.objects.create(
            customer_id='CUST001', customer_name='Test Customer', regd_address='123 Test St',
            regd_country_id=1, regd_state_id=1, regd_city_id=1, regd_pin_no='12345'
        )
        EnquiryMaster.objects.create(enq_id=1, po_status=False)

class UnitMasterTest(MasterDataModelTest):
    def test_unit_master_creation(self):
        unit = UnitMaster.objects.get(id=1)
        self.assertEqual(unit.symbol, 'Pcs')
        self.assertEqual(str(unit), 'Pcs')
        self.assertFalse(unit._meta.managed)
        self.assertEqual(unit._meta.db_table, 'Unit_Master')

class VatMasterTest(MasterDataModelTest):
    def test_vat_master_creation(self):
        vat = VatMaster.objects.get(id=1)
        self.assertEqual(vat.terms, 'VAT 10%')
        self.assertEqual(str(vat), 'VAT 10%')

class ExciseMasterTest(MasterDataModelTest):
    def test_excise_master_creation(self):
        excise = ExciseMaster.objects.get(id=1)
        self.assertEqual(excise.terms, 'EXC 5%')
        self.assertEqual(str(excise), 'EXC 5%')

class CustomerMasterTest(MasterDataModelTest):
    def test_customer_master_creation(self):
        customer = CustomerMaster.objects.get(customer_id='CUST001')
        self.assertEqual(customer.customer_name, 'Test Customer')
        self.assertEqual(customer.full_address, '123 Test St, 12345')

class EnquiryMasterTest(MasterDataModelTest):
    def test_enquiry_master_creation(self):
        enquiry = EnquiryMaster.objects.get(enq_id=1)
        self.assertFalse(enquiry.po_status)
        self.assertEqual(str(enquiry), 'Enquiry 1')

    def test_mark_as_quoted(self):
        enquiry = EnquiryMaster.objects.get(enq_id=1)
        enquiry.mark_as_quoted()
        enquiry.refresh_from_db()
        self.assertTrue(enquiry.po_status)


class QuotationDetailsTempTest(MasterDataModelTest):
    def setUp(self):
        super().setUp()
        self.session_key = uuid.uuid4()
        self.comp_id = 1
        self.fin_year_id = 1
        self.unit = UnitMaster.objects.get(id=1)
        QuotationDetailsTemp.objects.create(
            id=1, session_key=self.session_key, comp_id=self.comp_id, fin_year_id=self.fin_year_id,
            item_desc='Test Item', total_qty=10.000, unit=self.unit, rate=50.00, discount=5.00
        )

    def test_quotation_details_temp_creation(self):
        temp_item = QuotationDetailsTemp.objects.get(id=1)
        self.assertEqual(temp_item.item_desc, 'Test Item')
        self.assertEqual(temp_item.total_qty, 10.000)
        self.assertEqual(temp_item.unit, self.unit)
        self.assertEqual(temp_item.rate, 50.00)
        self.assertEqual(temp_item.discount, 5.00)
        self.assertEqual(str(temp_item), 'Test Item (10 Pcs)')

    def test_calculate_line_total(self):
        temp_item = QuotationDetailsTemp.objects.get(id=1)
        # (10 * 50) - (10 * 50 * 0.05) = 500 - 25 = 475
        self.assertEqual(temp_item.calculate_line_total(), 475.00)

    def test_get_user_temp_details(self):
        details = QuotationDetailsTemp.get_user_temp_details(self.session_key, self.comp_id, self.fin_year_id)
        self.assertEqual(details.count(), 1)
        self.assertEqual(details.first().item_desc, 'Test Item')

    def test_clear_user_temp_details(self):
        QuotationDetailsTemp.clear_user_temp_details(self.session_key, self.comp_id, self.fin_year_id)
        self.assertEqual(QuotationDetailsTemp.objects.count(), 0)


class QuotationMasterTest(MasterDataModelTest):
    def setUp(self):
        super().setUp()
        self.customer = CustomerMaster.objects.get(customer_id='CUST001')
        self.enquiry = EnquiryMaster.objects.get(enq_id=1)
        self.vat = VatMaster.objects.get(id=1)
        self.excise = ExciseMaster.objects.get(id=1)
        
        QuotationMaster.objects.create(
            id=1, sys_date=date.today(), sys_time=time(10, 0, 0), session_id='TEST_SID',
            comp_id=1, fin_year_id=1, customer=self.customer, enquiry=self.enquiry,
            quotation_no='0001', payment_terms='30 days', pf=100.00, pf_type=0,
            vat_cst=self.vat, excise=self.excise, octroi=50.00, octroi_type=0,
            warrenty='1 year', insurance=20.00, transport='Road', note_no='NOTE123',
            registration_no='REG123', freight=30.00, freight_type=0, validity='3 months',
            other_charges=10.00, other_charges_type=0, delivery_terms='FOB', remarks='None',
            due_date=date.today()
        )
        
        self.session_key_temp = uuid.uuid4()
        QuotationDetailsTemp.objects.create(
            id=2, session_key=self.session_key_temp, comp_id=1, fin_year_id=1,
            item_desc='Temp Item 1', total_qty=5.000, unit=UnitMaster.objects.get(id=1), rate=10.00, discount=0.00
        )
        QuotationDetailsTemp.objects.create(
            id=3, session_key=self.session_key_temp, comp_id=1, fin_year_id=1,
            item_desc='Temp Item 2', total_qty=2.000, unit=UnitMaster.objects.get(id=1), rate=25.00, discount=10.00
        )

    def test_quotation_master_creation(self):
        qm = QuotationMaster.objects.get(id=1)
        self.assertEqual(qm.quotation_no, '0001')
        self.assertEqual(qm.customer, self.customer)
        self.assertEqual(qm.enquiry, self.enquiry)
        self.assertEqual(str(qm), '0001')

    def test_generate_next_quotation_no(self):
        next_no = QuotationMaster.generate_next_quotation_no(1, 1)
        self.assertEqual(next_no, '0002') # Based on existing '0001'

        # Test with no existing quotations
        QuotationMaster.objects.all().delete()
        next_no = QuotationMaster.generate_next_quotation_no(1, 1)
        self.assertEqual(next_no, '0001')

    def test_create_from_temp_details(self):
        qm = QuotationMaster.objects.get(id=1)
        temp_details_qs = QuotationDetailsTemp.objects.filter(session_key=self.session_key_temp)
        qm.create_from_temp_details(temp_details_qs)
        self.assertEqual(QuotationDetail.objects.filter(master=qm).count(), 2)
        
        detail1 = QuotationDetail.objects.get(master=qm, item_desc='Temp Item 1')
        self.assertEqual(detail1.total_qty, 5.000)


class QuotationDetailTest(MasterDataModelTest):
    def setUp(self):
        super().setUp()
        self.customer = CustomerMaster.objects.get(customer_id='CUST001')
        self.enquiry = EnquiryMaster.objects.get(enq_id=1)
        self.vat = VatMaster.objects.get(id=1)
        self.excise = ExciseMaster.objects.get(id=1)
        self.quotation_master = QuotationMaster.objects.create(
            id=10, sys_date=date.today(), sys_time=time(10, 0, 0), session_id='TEST_SID',
            comp_id=1, fin_year_id=1, customer=self.customer, enquiry=self.enquiry,
            quotation_no='0010', payment_terms='30 days', pf=100.00, pf_type=0,
            vat_cst=self.vat, excise=self.excise, octroi=50.00, octroi_type=0,
            warrenty='1 year', insurance=20.00, transport='Road', note_no='NOTE123',
            registration_no='REG123', freight=30.00, freight_type=0, validity='3 months',
            other_charges=10.00, other_charges_type=0, delivery_terms='FOB', remarks='None',
            due_date=date.today()
        )
        self.unit = UnitMaster.objects.get(id=1)
        QuotationDetail.objects.create(
            id=1, session_id='S1', comp_id=1, fin_year_id=1, master=self.quotation_master,
            item_desc='Final Item', total_qty=20.000, unit=self.unit, rate=100.00, discount=10.00
        )

    def test_quotation_detail_creation(self):
        detail = QuotationDetail.objects.get(id=1)
        self.assertEqual(detail.item_desc, 'Final Item')
        self.assertEqual(detail.master, self.quotation_master)
        self.assertEqual(str(detail), f"Detail for {self.quotation_master.quotation_no}: Final Item")

    def test_calculate_line_total(self):
        detail = QuotationDetail.objects.get(id=1)
        # (20 * 100) - (20 * 100 * 0.10) = 2000 - 200 = 1800
        self.assertEqual(detail.calculate_line_total(), 1800.00)


# --- Form Tests ---
class QuotationDetailTempFormTest(MasterDataModelTest):
    def test_valid_form(self):
        unit = UnitMaster.objects.get(id=1)
        data = {
            'item_desc': 'Test Description',
            'total_qty': '10.500',
            'rate': '150.75',
            'discount': '10.00',
            'unit': unit.id
        }
        form = QuotationDetailTempForm(data=data)
        self.assertTrue(form.is_valid())

    def test_invalid_form_missing_data(self):
        data = {}
        form = QuotationDetailTempForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('item_desc', form.errors)
        self.assertIn('total_qty', form.errors)
        self.assertIn('unit', form.errors)

    def test_invalid_form_qty_regex(self):
        unit = UnitMaster.objects.get(id=1)
        data = {
            'item_desc': 'Test Description',
            'total_qty': 'invalid_qty',
            'rate': '150.75',
            'discount': '10.00',
            'unit': unit.id
        }
        form = QuotationDetailTempForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('total_qty', form.errors)

class QuotationTermsFormTest(MasterDataModelTest):
    def setUp(self):
        super().setUp()
        self.vat = VatMaster.objects.get(id=1)
        self.excise = ExciseMaster.objects.get(id=1)

    def test_valid_form(self):
        data = {
            'payment_terms': 'Net 30',
            'pf': '50.00', 'pf_type': '0',
            'vat_cst': self.vat.id,
            'excise': self.excise.id,
            'octroi': '20.00', 'octroi_type': '1',
            'warrenty': '6 months',
            'insurance': '15.00',
            'transport': 'Air Cargo',
            'note_no': 'RR123',
            'registration_no': 'VEH456',
            'freight': '100.00', 'freight_type': '0',
            'due_date': '20-12-2024',
            'validity': '30 days',
            'other_charges': '5.00', 'other_charges_type': '1',
            'delivery_terms': 'DDP',
            'remarks': 'Test remarks'
        }
        form = QuotationTermsForm(data=data)
        self.assertTrue(form.is_valid(), form.errors)

    def test_invalid_form_missing_required(self):
        data = {} # Missing all required fields
        form = QuotationTermsForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('payment_terms', form.errors)
        self.assertIn('due_date', form.errors)

    def test_invalid_form_date_format(self):
        data = {
            'payment_terms': 'Net 30',
            'pf': '50.00', 'pf_type': '0',
            'vat_cst': self.vat.id,
            'excise': self.excise.id,
            'octroi': '20.00', 'octroi_type': '1',
            'warrenty': '6 months',
            'insurance': '15.00',
            'transport': 'Air Cargo',
            'note_no': 'RR123',
            'registration_no': 'VEH456',
            'freight': '100.00', 'freight_type': '0',
            'due_date': '2024-12-20', # Incorrect format
            'validity': '30 days',
            'other_charges': '5.00', 'other_charges_type': '1',
            'delivery_terms': 'DDP',
            'remarks': 'Test remarks'
        }
        form = QuotationTermsForm(data=data)
        self.assertFalse(form.is_valid())
        self.assertIn('due_date', form.errors)


# --- View Tests ---
class QuotationViewsTest(MasterDataModelTest):
    def setUp(self):
        super().setUp()
        self.client = Client()
        self.customer = CustomerMaster.objects.get(customer_id='CUST001')
        self.enquiry = EnquiryMaster.objects.get(enq_id=1)
        self.unit = UnitMaster.objects.get(id=1)
        self.vat = VatMaster.objects.get(id=1)
        self.excise = ExciseMaster.objects.get(id=1)

        self.base_url_params = f'?CustomerId={self.customer.customer_id}&EnqId={self.enquiry.enq_id}'

    def test_quotation_create_wizard_view_get(self):
        response = self.client.get(reverse('sales_quotation:quotation_create') + self.base_url_params)
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_quotation/quotation/create_wizard.html')
        self.assertContains(response, 'Customer Quotation - New')
        self.assertIn('customer', response.context)
        self.assertIn('enquiry', response.context)

    def test_customer_details_partial_view_get(self):
        response = self.client.get(reverse('sales_quotation:customer_details_partial') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_quotation/quotation/_customer_details.html')
        self.assertContains(response, 'Name of Customer')
        self.assertContains(response, self.customer.customer_name)

    def test_goods_details_partial_view_get(self):
        response = self.client.get(reverse('sales_quotation:goods_details_partial') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_quotation/quotation/_goods_details.html')
        self.assertContains(response, 'Add Item Details')
        self.assertIn('form', response.context)

    def test_quotation_detail_temp_create_view_post_valid_htmx(self):
        session = self.client.session
        session.create()
        session['user_quotation_session_key'] = str(uuid.uuid4())
        session.save()

        data = {
            'item_desc': 'New HTMX Item',
            'total_qty': '10.000',
            'rate': '100.00',
            'discount': '5.00',
            'unit': self.unit.id
        }
        response = self.client.post(reverse('sales_quotation:quotation_detail_temp_add') + self.base_url_params, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204) # No Content, successful HTMX response
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshQuotationDetailList', response.headers['HX-Trigger'])
        
        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertEqual(str(messages[0]), 'Item added successfully.')

        self.assertTrue(QuotationDetailsTemp.objects.filter(item_desc='New HTMX Item').exists())

    def test_quotation_detail_temp_create_view_post_invalid_htmx(self):
        session = self.client.session
        session.create()
        session['user_quotation_session_key'] = str(uuid.uuid4())
        session.save()

        data = {
            'item_desc': '', # Invalid
            'total_qty': '10.000',
            'rate': '100.00',
            'discount': '5.00',
            'unit': self.unit.id
        }
        response = self.client.post(reverse('sales_quotation:quotation_detail_temp_add') + self.base_url_params, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200) # Form re-rendered with errors
        self.assertTemplateUsed(response, 'sales_quotation/quotation/_goods_detail_form.html')
        self.assertContains(response, 'This field is required.')


    def test_quotation_detail_temp_table_view_get_htmx(self):
        session = self.client.session
        session.create()
        session['user_quotation_session_key'] = str(uuid.uuid4())
        session['compid'] = 1 # Match get_session_data
        session['finyear'] = 1 # Match get_session_data
        session.save()

        # Add a temp item
        QuotationDetailsTemp.objects.create(
            id=1, session_key=session['user_quotation_session_key'], comp_id=1, fin_year_id=1,
            item_desc='Table Item', total_qty=1.000, unit=self.unit, rate=1.00, discount=0.00
        )
        
        response = self.client.get(reverse('sales_quotation:quotation_detail_temp_table') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_quotation/quotation/_goods_details_table.html')
        self.assertContains(response, 'Table Item')
        self.assertContains(response, self.unit.symbol)


    def test_quotation_detail_temp_update_view_post_valid_htmx(self):
        session = self.client.session
        session.create()
        session['user_quotation_session_key'] = str(uuid.uuid4())
        session['compid'] = 1 # Match get_session_data
        session['finyear'] = 1 # Match get_session_data
        session.save()

        temp_item = QuotationDetailsTemp.objects.create(
            id=10, session_key=session['user_quotation_session_key'], comp_id=1, fin_year_id=1,
            item_desc='Original Item', total_qty=1.000, unit=self.unit, rate=1.00, discount=0.00
        )

        data = {
            'item_desc': 'Updated Item',
            'total_qty': '2.000',
            'rate': '2.00',
            'discount': '0.00',
            'unit': self.unit.id
        }
        response = self.client.post(reverse('sales_quotation:quotation_detail_temp_edit', args=[temp_item.id]) + self.base_url_params, data, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshQuotationDetailList', response.headers['HX-Trigger'])
        
        temp_item.refresh_from_db()
        self.assertEqual(temp_item.item_desc, 'Updated Item')
        self.assertEqual(temp_item.total_qty, 2.000)

    def test_quotation_detail_temp_delete_view_post_htmx(self):
        session = self.client.session
        session.create()
        session['user_quotation_session_key'] = str(uuid.uuid4())
        session['compid'] = 1 # Match get_session_data
        session['finyear'] = 1 # Match get_session_data
        session.save()

        temp_item = QuotationDetailsTemp.objects.create(
            id=20, session_key=session['user_quotation_session_key'], comp_id=1, fin_year_id=1,
            item_desc='Item to Delete', total_qty=1.000, unit=self.unit, rate=1.00, discount=0.00
        )
        
        response = self.client.post(reverse('sales_quotation:quotation_detail_temp_delete', args=[temp_item.id]) + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Trigger', response.headers)
        self.assertIn('refreshQuotationDetailList', response.headers['HX-Trigger'])
        self.assertFalse(QuotationDetailsTemp.objects.filter(id=temp_item.id).exists())


    def test_terms_conditions_partial_view_get(self):
        response = self.client.get(reverse('sales_quotation:terms_conditions_partial') + self.base_url_params, HTTP_HX_REQUEST='true')
        self.assertEqual(response.status_code, 200)
        self.assertTemplateUsed(response, 'sales_quotation/quotation/_terms_conditions.html')
        self.assertContains(response, 'Quotation Terms & Conditions')
        self.assertIn('form', response.context)

    def test_quotation_finalize_view_post_valid_htmx(self):
        session = self.client.session
        session.create()
        session_key_uuid = uuid.uuid4()
        session['user_quotation_session_key'] = str(session_key_uuid)
        session['compid'] = 1
        session['finyear'] = 1
        session['username'] = 'testuser'
        session.save()

        # Create temporary items
        QuotationDetailsTemp.objects.create(
            id=30, session_key=session_key_uuid, comp_id=1, fin_year_id=1,
            item_desc='Temp Item A', total_qty=1.000, unit=self.unit, rate=10.00, discount=0.00
        )
        QuotationDetailsTemp.objects.create(
            id=31, session_key=session_key_uuid, comp_id=1, fin_year_id=1,
            item_desc='Temp Item B', total_qty=2.000, unit=self.unit, rate=20.00, discount=5.00
        )

        data = {
            'payment_terms': 'Net 30',
            'pf': '50.00', 'pf_type': '0',
            'vat_cst': self.vat.id,
            'excise': self.excise.id,
            'octroi': '20.00', 'octroi_type': '1',
            'warrenty': '6 months',
            'insurance': '15.00',
            'transport': 'Air Cargo',
            'note_no': 'RR123',
            'registration_no': 'VEH456',
            'freight': '100.00', 'freight_type': '0',
            'due_date': '20-12-2024',
            'validity': '30 days',
            'other_charges': '5.00', 'other_charges_type': '1',
            'delivery_terms': 'DDP',
            'remarks': 'Test remarks'
        }
        response = self.client.post(reverse('sales_quotation:quotation_finalize') + self.base_url_params, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 204)
        self.assertIn('HX-Redirect', response.headers)
        self.assertEqual(response.headers['HX-Redirect'], reverse_lazy('quotation_list'))

        messages = list(get_messages(response.wsgi_request))
        self.assertEqual(len(messages), 1)
        self.assertIn('Quotation is generated successfully.', str(messages[0]))

        # Verify QuotationMaster created
        quotation = QuotationMaster.objects.order_by('-id').first()
        self.assertIsNotNone(quotation)
        self.assertEqual(quotation.customer, self.customer)
        self.assertEqual(quotation.enquiry, self.enquiry)
        
        # Verify QuotationDetails created from temp
        self.assertEqual(quotation.details.count(), 2)
        self.assertFalse(QuotationDetailsTemp.objects.filter(session_key=session_key_uuid).exists()) # Temp items cleared
        self.assertTrue(EnquiryMaster.objects.get(enq_id=self.enquiry.enq_id).po_status) # Enquiry status updated

    def test_quotation_finalize_view_post_invalid_form_htmx(self):
        session = self.client.session
        session.create()
        session['user_quotation_session_key'] = str(uuid.uuid4())
        session['compid'] = 1
        session['finyear'] = 1
        session.save()

        data = {
            'payment_terms': '', # Invalid
            # ... other required fields missing
        }
        response = self.client.post(reverse('sales_quotation:quotation_finalize') + self.base_url_params, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Re-renders partial
        self.assertTemplateUsed(response, 'sales_quotation/quotation/_terms_conditions.html')
        self.assertContains(response, 'Please correct the errors in the terms and conditions.')
        self.assertContains(response, 'This field is required.')

    def test_quotation_finalize_view_post_no_temp_details_htmx(self):
        session = self.client.session
        session.create()
        session['user_quotation_session_key'] = str(uuid.uuid4()) # No temp items associated
        session['compid'] = 1
        session['finyear'] = 1
        session.save()

        data = {
            'payment_terms': 'Net 30',
            'pf': '50.00', 'pf_type': '0',
            'vat_cst': self.vat.id,
            'excise': self.excise.id,
            'octroi': '20.00', 'octroi_type': '1',
            'warrenty': '6 months',
            'insurance': '15.00',
            'transport': 'Air Cargo',
            'note_no': 'RR123',
            'registration_no': 'VEH456',
            'freight': '100.00', 'freight_type': '0',
            'due_date': '20-12-2024',
            'validity': '30 days',
            'other_charges': '5.00', 'other_charges_type': '1',
            'delivery_terms': 'DDP',
            'remarks': 'Test remarks'
        }
        response = self.client.post(reverse('sales_quotation:quotation_finalize') + self.base_url_params, data, HTTP_HX_REQUEST='true')
        
        self.assertEqual(response.status_code, 200) # Re-renders partial with error
        self.assertTemplateUsed(response, 'sales_quotation/quotation/_terms_conditions.html')
        self.assertContains(response, 'No goods details added to the quotation.')

```

## Step 5: HTMX and Alpine.js Integration

*   **Tabs Navigation:** `create_wizard.html` uses Alpine.js (`x-data`, `x-on:click`) to manage `activeTab` state and `localStorage` for persistence. Each tab `button` uses `hx-get` to fetch its content (`_customer_details.html`, `_goods_details.html`, `_terms_conditions.html`) into the `#tabContent` div via HTMX `hx-target` and `hx-swap="innerHTML"`. `hx-trigger="click, load[activeTab === 'customer'] once"` ensures content loads on tab click and also once on initial page load if that tab is active.
*   **Modal Forms:** The main `create_wizard.html` defines a global `#modal` element and `#modalContent` div. Buttons like "Edit" and "Delete" in `_goods_details_table.html` use `hx-get` to fetch form (`_goods_detail_form.html`) or confirmation (`_confirm_delete.html`) partials into `#modalContent`. Alpine.js `_ = "on click add .flex then remove .hidden to #modal"` is used to show the modal.
*   **Form Submission:** Forms (`_goods_detail_form.html`, `_terms_conditions.html`) use `hx-post`.
    *   For add/edit item forms, `hx-swap="none"` is used on the form itself, and the server responds with `status=204` and `HX-Trigger: 'refreshQuotationDetailList'` to close the modal and update the table.
    *   For final submission, `hx-swap="outerHTML"` on the form's container or a redirect is used. The `QuotationFinalizeView` sends `HX-Redirect` header on success.
*   **DataTables:** The `_goods_details_table.html` partial initializes DataTables on `document.ready`. This partial is loaded into `#quotationDetailTempTableContainer` via `hx-get` (`hx-trigger="load, refreshQuotationDetailList from:body"`). This ensures the table is rendered and DataTables is initialized each time the content is refreshed by HTMX. The `refreshQuotationDetailList` custom event is triggered by the server after any CRUD operation on temporary details.
*   **Messages:** Django's `messages` framework is used for user feedback. These messages are included in the HTMX-swapped partials, so they appear dynamically.
*   **Client-Side Validation:** While Django forms handle server-side validation, the `RequiredFieldValidator` and `RegularExpressionValidator` logic from ASP.NET is handled by Django's form validation and `RegexValidator`. HTMX will re-render the form partial with validation errors, providing immediate feedback. Alpine.js could be used for more advanced real-time client-side validation as the user types, but Django's form re-rendering is a solid starting point for HTMX.

---

## Final Notes

This modernization plan transitions the multi-step ASP.NET form flow into a modern Django application using a "Fat Model, Thin View" approach. All dynamic interactions are managed by HTMX and Alpine.js, minimizing custom JavaScript. DataTables provides robust data presentation and interaction. The codebase prioritizes maintainability, testability, and adherence to DRY principles. This structured approach, combined with AI-assisted automation for code generation and schema extraction, significantly reduces manual migration effort and improves quality.